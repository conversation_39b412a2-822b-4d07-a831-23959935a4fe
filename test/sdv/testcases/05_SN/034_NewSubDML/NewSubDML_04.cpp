/* ****************************************************************************
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: NewSubDML_04.cpp
 * Description: 状态合并DML -结构化混合复杂表 对V3转换
 * Author: ywx1037054
 * Create: 2023-05-12
 **************************************************************************** */
#include <stdlib.h>
#include <stdio.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "NewSubDML.h"
#include "special_complex_table_tools.h"

class NewSubDML_04 : public testing::Test
{
protected:
    static void SetUpTestCase()
    {
        ;
    }
    static void TearDownTestCase()
    {
        ;
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void NewSubDML_04::SetUp()
{
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"compatibleV3=1\""); 
    system("sh $TEST_HOME/tools/start.sh ");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);
    AW_CHECK_LOG_BEGIN();
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    char errorMsg2[128] = {};
    (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_CONNECTION_RESET_BY_PEER);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg2);
    char errorMsg3[128] = {};
    (void)snprintf(errorMsg3, sizeof(errorMsg3), "GMERR-%d", GMERR_COMMON_STREAM_OVERLOAD);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg3);
    char errorMsg4[128] = {};
    (void)snprintf(errorMsg4, sizeof(errorMsg4), "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg4);
    char errorMsg5[128] = {};
    (void)snprintf(errorMsg5, sizeof(errorMsg5), "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg5);
    
}
void NewSubDML_04::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = 0;
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"compatibleV3=1\""); 
    system("sh $TEST_HOME/tools/start.sh ");
}

class NewSubDML_04CONF : public testing::Test
{
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/modifyCfg.sh \"compatibleV3=1\" ");
        if (g_envType == 0 || g_envType == 1 || g_envType == 3)
        {
            system("sh $TEST_HOME/tools/modifyCfg.sh \"subsChannelGlobalDynamicMemSizeMax=32\"");
            system("sh $TEST_HOME/tools/modifyCfg.sh \"subsChannelGlobalShareMemSizeMax=32\"");
        }
        else
        {
            system("sh $TEST_HOME/tools/modifyCfg.sh \"subsChannelGlobalShareMemSizeMax=15\"");
        }
        system("sh ${TEST_HOME}/tools/start.sh -f");
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        int ret = 0;
        ret = testEnvInit();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = 0;
        ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(0, ret);
        system("gmddl -c drop -t T20_all_type");
        GmcDetachAllShmSeg();
        testEnvClean();
    }
public:
    virtual void SetUp();
    virtual void TearDown();
};

void NewSubDML_04CONF::SetUp()
{
    int ret = 0;
    AW_CHECK_LOG_BEGIN();
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    char errorMsg3[128] = {};
    (void)snprintf(errorMsg3, sizeof(errorMsg3), "GMERR-%d", GMERR_COMMON_STREAM_OVERLOAD);
    printf("errorMsg3%s\n",errorMsg3);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg3);
}
void NewSubDML_04CONF::TearDown()
{
    AW_CHECK_LOG_END();
}

// 001. 全量订阅，特殊复杂表结构化写入
TEST_F(NewSubDML_04, SN_034_004_001)
{
    int ret = 0;
    char *mySchema = NULL;
    GmcConnT *conn_sub = NULL;
    GmcStmtT *stmt_sub = NULL;
    const uint16_t t2Count = 10, t3Count = 10;
    char g_specialTableName[] = "TEST_SC_T1";
    char g_pkName[] = "TEST_PK";
    SnUserDataT *userData = NULL;
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSnMallocUserData(&userData, 400, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *newSubInfoAddMulti = R"(
    {
            "name": "newSubTreeMulti001",
            "label_name": "TEST_SC_T1",
            "comment":"status_merge",
            "type":"before_commit",
            "events":
                [
                    {"type":"initial_load", "msgTypes":["new object"]}
                ],
            "is_path":false,
            "retry":true,
            "priority": 1,
            "subs_type":"status_merge"
    })";
    GmcDropVertexLabel(g_stmt, g_specialTableName);
    readJanssonFile("schema_file/special_complex_table.gmjson", &mySchema);
    AW_MACRO_EXPECT_NE_INT((void *)NULL, mySchema);
    const char *labelConfig = "{\"max_recordNum\":10000, \"isFastReadUncommitted\":0, \"status_merge_sub\":true}";
    ret = GmcCreateVertexLabel(g_stmt, mySchema, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(mySchema);
    ret = testSubConnect(&conn_sub, &stmt_sub, 1, g_epoll_reg_info, "newsubconn", &g_chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    db_test_struct_write_special_complex_table_batch(
        g_stmt, 100, g_specialTableName, t2Count, t3Count, 1);
    GmcSubConfigT tsub_info;
    tsub_info.subsName = "newSubTreeMulti001";
    tsub_info.configJson = newSubInfoAddMulti;
    ret = GmcSubscribe(g_stmt, &tsub_info, conn_sub, NewSnCallBackTree, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INITIAL_LOAD, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, "newSubTreeMulti001");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(conn_sub, stmt_sub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testSnFreeUserData(userData);
    ret = GmcDropVertexLabel(g_stmt, g_specialTableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "END\n");
}
// 002. 增量订阅，特殊复杂表结构化写入
TEST_F(NewSubDML_04, SN_034_004_002)
{
    int ret = 0;
    char *mySchema = NULL;
    GmcConnT *conn_sub = NULL;
    GmcStmtT *stmt_sub = NULL;
    const uint16_t t2Count = 10, t3Count = 10;
    char g_specialTableName[] = "TEST_SC_T1";
    char g_pkName[] = "TEST_PK";
    SnUserDataT *userData = NULL;
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSnMallocUserData(&userData, 400, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *newSubInfoAddMulti = R"(
    {
            "name": "newSubTreeMulti002",
            "label_name": "TEST_SC_T1",
            "comment":"status_merge",
            "type":"before_commit",
            "events":
                [
                    {"type":"modify", "msgTypes":["new object", "old object"]},
                    {"type":"delete", "msgTypes":["new object", "old object"]}
                ],
            "is_path":false,
            "retry":true,
            "priority": 1,
            "subs_type":"status_merge"
    })";
    GmcDropVertexLabel(g_stmt, g_specialTableName);
    readJanssonFile("schema_file/special_complex_table.gmjson", &mySchema);
    AW_MACRO_EXPECT_NE_INT((void *)NULL, mySchema);
    const char *labelConfig = "{\"max_recordNum\":10000, \"isFastReadUncommitted\":0, \"status_merge_sub\":true}";
    ret = GmcCreateVertexLabel(g_stmt, mySchema, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(mySchema);
    ret = testSubConnect(&conn_sub, &stmt_sub, 1, g_epoll_reg_info, "newsubconn", &g_chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcSubConfigT tsub_info;
    tsub_info.subsName = "newSubTreeMulti002";
    tsub_info.configJson = newSubInfoAddMulti;
    ret = GmcSubscribe(g_stmt, &tsub_info, conn_sub, NewSnCallBackTree, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    db_test_struct_write_special_complex_table_batch(
        g_stmt, 100, g_specialTableName, t2Count, t3Count, 1);

    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_MODIFY, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, "newSubTreeMulti002");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(conn_sub, stmt_sub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testSnFreeUserData(userData);
    ret = GmcDropVertexLabel(g_stmt, g_specialTableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "END\n");
}
// 003. 全量+增量订阅，特殊复杂表结构化写入
TEST_F(NewSubDML_04, SN_034_004_003)
{
    int ret = 0;
    char *mySchema = NULL;
    GmcConnT *conn_sub = NULL;
    GmcStmtT *stmt_sub = NULL;
    const uint16_t t2Count = 10, t3Count = 10;
    char g_specialTableName[] = "TEST_SC_T1";
    char g_pkName[] = "TEST_PK";
    SnUserDataT *userData = NULL;
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSnMallocUserData(&userData, 400, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *newSubInfoAddMulti = R"(
    {
            "name": "newSubTreeMulti002",
            "label_name": "TEST_SC_T1",
            "comment":"status_merge",
            "type":"before_commit",
            "events":
                [
                    {"type":"initial_load", "msgTypes":["new object"]},
                    {"type":"initial_load_eof"},
                    {"type":"modify", "msgTypes":["new object", "old object"]},
                    {"type":"delete", "msgTypes":["new object", "old object"]}
                ],
            "is_path":false,
            "retry":true,
            "priority": 1,
            "subs_type":"status_merge"
    })";
    GmcDropVertexLabel(g_stmt, g_specialTableName);
    readJanssonFile("schema_file/special_complex_table.gmjson", &mySchema);
    AW_MACRO_EXPECT_NE_INT((void *)NULL, mySchema);
    const char *labelConfig = "{\"max_recordNum\":10000, \"isFastReadUncommitted\":0, \"status_merge_sub\":true}";
    ret = GmcCreateVertexLabel(g_stmt, mySchema, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(mySchema);
    ret = testSubConnect(&conn_sub, &stmt_sub, 1, g_epoll_reg_info, "newsubconn", &g_chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcSubConfigT tsub_info;
    tsub_info.subsName = "newSubTreeMulti002";
    tsub_info.configJson = newSubInfoAddMulti;
    ret = GmcSubscribe(g_stmt, &tsub_info, conn_sub, NewSnCallBackTree, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    db_test_struct_write_special_complex_table_batch(
        g_stmt, 100, g_specialTableName, t2Count, t3Count, 1);

    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INITIAL_LOAD_EOF, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitStMgSnRecv(userData, GMC_SUB_EVENT_INITIAL_LOAD, 0, 100);// 0524全量+增量订阅，增量操作有可能被读成全量订阅事件，需要修改用例预期
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitStMgSnRecv(userData, GMC_SUB_EVENT_MODIFY, 0, 100);// 0524全量+增量订阅，增量操作有可能被读成全量订阅事件，需要修改用例预期
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, "newSubTreeMulti002");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(conn_sub, stmt_sub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testSnFreeUserData(userData);
    ret = GmcDropVertexLabel(g_stmt, g_specialTableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "END\n");
}
// 004. 全量订阅，特殊复杂表结构化写入后删除
TEST_F(NewSubDML_04, SN_034_004_004)
{
    int ret = 0;
    char *mySchema = NULL;
    GmcConnT *conn_sub = NULL;
    GmcStmtT *stmt_sub = NULL;
    const uint16_t t2Count = 10, t3Count = 10;
    char g_specialTableName[] = "TEST_SC_T1";
    char g_pkName[] = "TEST_PK";
    SnUserDataT *userData = NULL;
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSnMallocUserData(&userData, 400, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *newSubInfoAddMulti = R"(
    {
            "name": "newSubTreeMulti001",
            "label_name": "TEST_SC_T1",
            "comment":"status_merge",
            "type":"before_commit",
            "events":
                [
                    {"type":"initial_load", "msgTypes":["new object"]}
                ],
            "is_path":false,
            "retry":true,
            "priority": 1,
            "subs_type":"status_merge"
    })";
    GmcDropVertexLabel(g_stmt, g_specialTableName);
    readJanssonFile("schema_file/special_complex_table.gmjson", &mySchema);
    AW_MACRO_EXPECT_NE_INT((void *)NULL, mySchema);
    const char *labelConfig = "{\"max_recordNum\":10000, \"isFastReadUncommitted\":0, \"status_merge_sub\":true}";
    ret = GmcCreateVertexLabel(g_stmt, mySchema, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(mySchema);
    ret = testSubConnect(&conn_sub, &stmt_sub, 1, g_epoll_reg_info, "newsubconn", &g_chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    db_test_struct_write_special_complex_table_batch(
        g_stmt, 100, g_specialTableName, t2Count, t3Count, 1);
    // 结构化批量删除
    db_test_struct_delete_special_complex_table(g_stmt, 100, g_specialTableName, 1);

    GmcSubConfigT tsub_info;
    tsub_info.subsName = "newSubTreeMulti001";
    tsub_info.configJson = newSubInfoAddMulti;
    ret = GmcSubscribe(g_stmt, &tsub_info, conn_sub, NewSnCallBackTree, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INITIAL_LOAD, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, "newSubTreeMulti001");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(conn_sub, stmt_sub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testSnFreeUserData(userData);
    ret = GmcDropVertexLabel(g_stmt, g_specialTableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "END\n");
}
// 005. 增量订阅，特殊复杂表结构化写入后删除
TEST_F(NewSubDML_04, SN_034_004_005)
{
    int ret = 0;
    char *mySchema = NULL;
    GmcConnT *conn_sub = NULL;
    GmcStmtT *stmt_sub = NULL;
    const uint16_t t2Count = 10, t3Count = 10;
    char g_specialTableName[] = "TEST_SC_T1";
    char g_pkName[] = "TEST_PK";
    SnUserDataT *userData = NULL;
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSnMallocUserData(&userData, 400, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *newSubInfoAddMulti = R"(
    {
            "name": "newSubTreeMulti002",
            "label_name": "TEST_SC_T1",
            "comment":"status_merge",
            "type":"before_commit",
            "events":
                [
                    {"type":"modify", "msgTypes":["new object", "old object"]},
                    {"type":"delete", "msgTypes":["new object", "old object"]}
                ],
            "is_path":false,
            "retry":true,
            "priority": 1,
            "subs_type":"status_merge"
    })";
    GmcDropVertexLabel(g_stmt, g_specialTableName);
    readJanssonFile("schema_file/special_complex_table.gmjson", &mySchema);
    AW_MACRO_EXPECT_NE_INT((void *)NULL, mySchema);
    const char *labelConfig = "{\"max_recordNum\":10000, \"isFastReadUncommitted\":0, \"status_merge_sub\":true}";
    ret = GmcCreateVertexLabel(g_stmt, mySchema, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(mySchema);
    ret = testSubConnect(&conn_sub, &stmt_sub, 1, g_epoll_reg_info, "newsubconn", &g_chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcSubConfigT tsub_info;
    tsub_info.subsName = "newSubTreeMulti002";
    tsub_info.configJson = newSubInfoAddMulti;
    ret = GmcSubscribe(g_stmt, &tsub_info, conn_sub, NewSnCallBackTree, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    db_test_struct_write_special_complex_table_batch(
        g_stmt, 100, g_specialTableName, t2Count, t3Count, 1);

    // 结构化批量删除
    db_test_struct_delete_special_complex_table(g_stmt, 100, g_specialTableName, 1);
    int ex_nub = 100;
    ret = testWaitStMgSnRecv(userData, GMC_SUB_EVENT_DELETE, ex_nub, ex_nub * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, "newSubTreeMulti002");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(conn_sub, stmt_sub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testSnFreeUserData(userData);
    ret = GmcDropVertexLabel(g_stmt, g_specialTableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "END\n");
}
// 006. 全量+增量订阅，特殊复杂表结构化写入后删除
TEST_F(NewSubDML_04, SN_034_004_006)
{
    int ret = 0;
    char *mySchema = NULL;
    GmcConnT *conn_sub = NULL;
    GmcStmtT *stmt_sub = NULL;
    const uint16_t t2Count = 10, t3Count = 10;
    char g_specialTableName[] = "TEST_SC_T1";
    char g_pkName[] = "TEST_PK";
    SnUserDataT *userData = NULL;
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSnMallocUserData(&userData, 400, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *newSubInfoAddMulti = R"(
    {
            "name": "newSubTreeMulti002",
            "label_name": "TEST_SC_T1",
            "comment":"status_merge",
            "type":"before_commit",
            "events":
                [
                    {"type":"initial_load", "msgTypes":["new object"]},
                    {"type":"initial_load_eof"},
                    {"type":"modify", "msgTypes":["new object", "old object"]},
                    {"type":"delete", "msgTypes":["new object", "old object"]}
                ],
            "is_path":false,
            "retry":true,
            "priority": 1,
            "subs_type":"status_merge"
    })";
    GmcDropVertexLabel(g_stmt, g_specialTableName);
    readJanssonFile("schema_file/special_complex_table.gmjson", &mySchema);
    AW_MACRO_EXPECT_NE_INT((void *)NULL, mySchema);
    const char *labelConfig = "{\"max_recordNum\":10000, \"isFastReadUncommitted\":0, \"status_merge_sub\":true}";
    ret = GmcCreateVertexLabel(g_stmt, mySchema, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(mySchema);
    ret = testSubConnect(&conn_sub, &stmt_sub, 1, g_epoll_reg_info, "newsubconn", &g_chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcSubConfigT tsub_info;
    tsub_info.subsName = "newSubTreeMulti002";
    tsub_info.configJson = newSubInfoAddMulti;
    ret = GmcSubscribe(g_stmt, &tsub_info, conn_sub, NewSnCallBackTree, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    db_test_struct_write_special_complex_table_batch(
        g_stmt, 100, g_specialTableName, t2Count, t3Count, 1);
    // 结构化批量删除
    db_test_struct_delete_special_complex_table(g_stmt, 100, g_specialTableName, 1);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INITIAL_LOAD_EOF, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INITIAL_LOAD, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitStMgSnRecv(userData, GMC_SUB_EVENT_DELETE,100, 200);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, "newSubTreeMulti002");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(conn_sub, stmt_sub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testSnFreeUserData(userData);
    ret = GmcDropVertexLabel(g_stmt, g_specialTableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "END\n");
}
SnUserDataT *userData1 = NULL;
SnUserDataT *userData2 = NULL;
SnUserDataT *userData3 = NULL;
void *simple_table_writeupdatedelete_fun(void *args)
{
    GmcConnT *conn = NULL, *conn_sub = NULL;
    GmcStmtT *stmt = NULL, *stmt_sub = NULL;
    int ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = NewSub_Vertex_DML(conn, stmt, (char *)"V_1", SINGLE_REPLACE, g_ezdata, 1000, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = NewSub_Vertex_DML(conn, stmt, (char *)"V_1", SINGLE_UPDATE, g_ezdata, 1000, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = NewSub_Vertex_DML(conn, stmt, (char *)"V_1", SINGLE_DELETE, g_ezdata, 1000, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}
void *simple_table_write_fun(void *args)
{
    GmcConnT *conn = NULL, *conn_sub = NULL;
    GmcStmtT *stmt = NULL, *stmt_sub = NULL;
    int ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = NewSub_Vertex_DML(conn, stmt, (char *)"V_1", SINGLE_INSERT, g_ezdata, 10, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}
void *simple_table_update_fun(void *args)
{
    GmcConnT *conn = NULL, *conn_sub = NULL;
    GmcStmtT *stmt = NULL, *stmt_sub = NULL;
    int ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = NewSub_Vertex_DML(conn, stmt, (char *)"V_1", SINGLE_INSERT, g_ezdata, 5, 20);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = NewSub_Vertex_DML(conn, stmt, (char *)"V_1", SINGLE_UPDATE, g_ezdata, 5, 20);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}
void *simple_table_delete_fun(void *args)
{
    GmcConnT *conn = NULL, *conn_sub = NULL;
    GmcStmtT *stmt = NULL, *stmt_sub = NULL;
    int ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = NewSub_Vertex_DML(conn, stmt, (char *)"V_1", SINGLE_INSERT, g_ezdata, 5, 30);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = NewSub_Vertex_DML(conn, stmt, (char *)"V_1", SINGLE_DELETE, g_ezdata, 5, 30);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

void *complex_table_write_fun(void *args)
{
    GmcConnT *conn = NULL, *conn_sub = NULL;
    GmcStmtT *stmt = NULL, *stmt_sub = NULL;
    int ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int start_num = 0;
    int end_num = 1000;
    int array_num = 3;
    int vector_num = 3;
    int ex_nub = 1000;
    int out_time = 24000;
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, "OP_T0");
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}
void *complex_table_update_fun(void *args)
{
    GmcConnT *conn = NULL, *conn_sub = NULL;
    GmcStmtT *stmt = NULL, *stmt_sub = NULL;
    int ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int start_num = 2000;
    int end_num = 3000;
    int array_num = 3;
    int vector_num = 3;
    int ex_nub = 1000;
    int out_time = 24000;
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, "OP_T0");
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;
    char f14_value[8] = "stoooo";

    // 更新node
    for (int i = start_num; i < end_num; i++)
    {
        ret = testGmcPrepareStmtByLabelName(stmt, "OP_T0", GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // update
        int newValue = i + 100;
        updateindex = 2;
        ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        // append
        updateindex = 0;
        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        ret = GmcSetIndexKeyName(stmt, "localhash_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}
void *complex_table_delete_fun(void *args)
{
    GmcConnT *conn = NULL, *conn_sub = NULL;
    GmcStmtT *stmt = NULL, *stmt_sub = NULL;
    int ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int start_num = 3000;
    int end_num = 4000;
    int array_num = 3;
    int vector_num = 3;
    int ex_nub = 1000;
    int out_time = 24000;
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, "OP_T0");
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;
    char f14_value[8] = "stoooo";
    for (int i = start_num; i < end_num; i++)
    {
        ret = testGmcPrepareStmtByLabelName(stmt, "OP_T0", GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "localhash_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}
void *sp_complex_table_write_fun(void *args)
{
    GmcConnT *g_conn = NULL, *conn_sub = NULL;
    GmcStmtT *g_stmt = NULL, *stmt_sub = NULL;
    char g_specialTableName[] = "TEST_SC_T1";
    int ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int start_num = 0;
    int end_num = 10;
    int array_num = 3;
    int vector_num = 3;
    const uint16_t t2Count = 10, t3Count = 10;
    db_test_struct_write_special_complex_table_batch2(
        g_stmt, g_conn, 0, 100, g_specialTableName, t2Count, t3Count, 1);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}
void *sp_complex_table_update_fun(void *args)
{
    GmcConnT *g_conn = NULL, *conn_sub = NULL;
    GmcStmtT *g_stmt = NULL, *stmt_sub = NULL;
    char g_specialTableName[] = "TEST_SC_T1";
    int ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int start_num = 0;
    int end_num = 10;
    int array_num = 3;
    int vector_num = 3;
    const uint16_t t2Count = 10, t3Count = 10;
    db_test_struct_write_special_complex_table_batch2(
        g_stmt, g_conn, 200, 300, g_specialTableName, t2Count, t3Count, 1);
    db_test_struct_replace_special_complex_table_batch2(
        g_stmt, g_conn, 200, 300, g_specialTableName, t2Count, t3Count, 1);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}
void *sp_complex_table_delete_fun(void *args)
{
    GmcConnT *g_conn = NULL, *conn_sub = NULL;
    GmcStmtT *g_stmt = NULL, *stmt_sub = NULL;
    int ret = testGmcConnect(&g_conn, &g_stmt);
    char g_specialTableName[] = "TEST_SC_T1";
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const uint16_t t2Count = 10, t3Count = 10;
    db_test_struct_write_special_complex_table_batch2(
        g_stmt, g_conn, 100, 200, g_specialTableName, t2Count, t3Count, 1);
    db_test_struct_delete_special_complex_table2(g_stmt, 100, 200, g_specialTableName, 1);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

// 007. 全量+增量订阅，3个生产线程分别操作3张表（每个线程对应简单、一般复杂、结构化表，分别进行写、更新、删除）一共9个线程，
// 1个订阅消费接收全部数据（考虑不同索引场景，数据批量操作）
// 【V3单号DTS2023040803866】
TEST_F(NewSubDML_04, SN_034_004_007)
{
    int ret = 0;
    char *mySchema = NULL;
    GmcConnT *conn_sub = NULL;
    GmcStmtT *stmt_sub = NULL;
    const uint16_t t2Count = 10, t3Count = 10;
    char g_specialTableName[] = "TEST_SC_T1";
    char g_pkName[] = "TEST_PK";
    const char *newSubInfoAddMulti1 = R"(
    {
            "name": "newSubTreeMulti001",
            "label_name": "V_1",
            "comment":"status_merge",
            "type":"before_commit",
            "events":
                [
                    {"type":"initial_load", "msgTypes":["new object"]},
                    {"type":"modify", "msgTypes":["new object", "old object"]},
                    {"type":"delete", "msgTypes":["new object", "old object"]},
                    {"type":"initial_load_eof"}
                ],
            "is_path":false,
            "retry":true,
            "priority": 1,
            "subs_type":"status_merge"
    })";
    const char *newSubInfoAddMulti2 = R"(
    {
            "name": "newSubTreeMulti002",
            "label_name": "OP_T0",
            "comment":"status_merge",
            "type":"before_commit",
            "events":
                [
                    {"type":"initial_load", "msgTypes":["new object"]},
                    {"type":"modify", "msgTypes":["new object", "old object"]},
                    {"type":"delete", "msgTypes":["new object", "old object"]},
                    {"type":"initial_load_eof"}
                ],
            "is_path":false,
            "retry":true,
            "priority": 1,
            "subs_type":"status_merge"
    })";
    const char *newSubInfoAddMulti3 = R"(
    {
            "name": "newSubTreeMulti003",
            "label_name": "TEST_SC_T1",
            "comment":"status_merge",
            "type":"before_commit",
            "events":
                [
                    {"type":"initial_load", "msgTypes":["new object"]},
                    {"type":"modify", "msgTypes":["new object", "old object"]},
                    {"type":"delete", "msgTypes":["new object", "old object"]},
                    {"type":"initial_load_eof"}
                ],
            "is_path":false,
            "retry":true,
            "priority": 1,
            "subs_type":"status_merge"
    })";
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *g_schema1 = NULL;
    char *g_schema3 = NULL;
    GmcDropVertexLabel(g_stmt, "V_1");
    readJanssonFile("./schema_file/NewSubV_1.gmjson", &g_schema1);
    AW_MACRO_ASSERT_NE_INT((void *)NULL, g_schema1);
    ret = GmcCreateVertexLabel(g_stmt, g_schema1, g_tableConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_schema1);

    GmcDropVertexLabel(g_stmt, "OP_T0");
    ret = GmcCreateVertexLabel(g_stmt, g_treeschema, g_tableConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcDropVertexLabel(g_stmt, "TEST_SC_T1");
    readJanssonFile("./schema_file/special_complex_table.gmjson", &g_schema3);
    AW_MACRO_ASSERT_NE_INT((void *)NULL, g_schema3);
    ret = GmcCreateVertexLabel(g_stmt, g_schema3, g_tableConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_schema3);
    ret = testSnMallocUserData(&userData1, 400, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSnMallocUserData(&userData2, 400, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSnMallocUserData(&userData3, 400, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubConnect(&conn_sub, &stmt_sub, 1, g_epoll_reg_info, "newSubTreeMulti001", &g_chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubConnect(&conn_sub, &stmt_sub, 1, g_epoll_reg_info, "newSubTreeMulti002", &g_chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubConnect(&conn_sub, &stmt_sub, 1, g_epoll_reg_info, "newSubTreeMulti003", &g_chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubConfigT tsub_info1;
    tsub_info1.subsName = "V_1";
    tsub_info1.configJson = newSubInfoAddMulti1;
    ret = GmcSubscribe(g_stmt, &tsub_info1, conn_sub, NewSnCallBack10, userData1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcSubConfigT tsub_info2;
    tsub_info2.subsName = "OP_T0";
    tsub_info2.configJson = newSubInfoAddMulti2;
    ret = GmcSubscribe(g_stmt, &tsub_info2, conn_sub, NewSnCallBack10, userData2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcSubConfigT tsub_info3;
    tsub_info3.subsName = "TEST_SC_T1";
    tsub_info3.configJson = newSubInfoAddMulti3;
    ret = GmcSubscribe(g_stmt, &tsub_info3, conn_sub, NewSnCallBack10, userData3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(userData1, GMC_SUB_EVENT_INITIAL_LOAD_EOF, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData2, GMC_SUB_EVENT_INITIAL_LOAD_EOF, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData3, GMC_SUB_EVENT_INITIAL_LOAD_EOF, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int thr_num = 9;
    pthread_t thr_arr[thr_num];
    void *thr_ret[thr_num];
    typedef void *(*ThreadFunc)(void *);
    ThreadFunc thr_func[9] = {simple_table_write_fun, simple_table_update_fun, simple_table_delete_fun,
                              complex_table_write_fun, complex_table_update_fun, complex_table_delete_fun,
                              sp_complex_table_write_fun, sp_complex_table_update_fun, sp_complex_table_delete_fun};
    for (int i = 0; i < 9; i++)
    {
        ret = pthread_create(&thr_arr[i], NULL, thr_func[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    for (int y = 0; y < 9; y++)
    {
        ret = pthread_join(thr_arr[y], &thr_ret[y]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = testWaitStMgSnRecv(userData1, GMC_SUB_EVENT_MODIFY, 15, 30);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitStMgSnRecv(userData1, GMC_SUB_EVENT_DELETE, 5, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitStMgSnRecv(userData2, GMC_SUB_EVENT_MODIFY, 2000, 4000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitStMgSnRecv(userData2, GMC_SUB_EVENT_DELETE, 1000, 2000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitStMgSnRecv(userData3, GMC_SUB_EVENT_MODIFY, 200, 400);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitStMgSnRecv(userData3, GMC_SUB_EVENT_DELETE, 100, 200);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt, "V_1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt, "OP_T0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt, "TEST_SC_T1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, "V_1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "OP_T0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "TEST_SC_T1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testSnFreeUserData(userData1);
    testSnFreeUserData(userData2);
    testSnFreeUserData(userData3);

    ret = testSubDisConnect(conn_sub, stmt_sub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "END\n");
}

// 008. 复杂表，写，订阅初始化加载事件，取消订阅，再订阅更新事件，更新数据，取消订阅，再订阅删除事件，删除数据，取消订阅，循环300次
//   V\$SERVER_MEMORY_OVERHEAD HEAP_SHM正常波动
// 视图gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=SeStMgPubSubMemCtx 正常
// 视图gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=DbTopSharedMemoryContext
// 视图V$\STORAGE_MEMDATA_STAT 正常
// 【V3单号DTS2023041117831】
// 【V5单号DTS2023051712156】
TEST_F(NewSubDML_04, SN_034_004_008)
{
    int ret = 0;
    GmcConnT *conn = NULL, *conn_sub = NULL;
    GmcStmtT *stmt = NULL, *stmt_sub = NULL;
    int start_num = 0;
    int end_num = 10000;
#if defined(ENV_RTOSV2X)
    end_num = 100;
#else
    end_num = 10000;
#endif
    int array_num = 3;
    int vector_num = 3;

    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SnUserDataT *userData = NULL;
    ret = testSnMallocUserData(&userData, 100, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *newSubInfoAddMulti = R"(
    {
            "name": "newSubTreeMulti001",
            "label_name": "OP_T0",
            "comment":"status_merge",
            "type":"before_commit",
            "events":
                [
                    {"type":"initial_load", "msgTypes":["new object"]},
                    {"type":"initial_load_eof"}
                ],
            "is_path":false,
            "retry":true,
            "priority": 1,
            "subs_type":"status_merge"
    })";
    const char *newSubInfoAddMulti2 = R"(
    {
            "name": "newSubTreeMulti002",
            "label_name": "OP_T0",
            "comment":"status_merge",
            "type":"before_commit",
            "events":
                [
                    {"type":"modify", "msgTypes":["new object", "old object"]}
                ],
            "is_path":false,
            "retry":true,
            "priority": 1,
            "subs_type":"status_merge"
    })";
    const char *newSubInfoAddMulti3 = R"(
    {
            "name": "newSubTreeMulti003",
            "label_name": "OP_T0",
            "comment":"status_merge",
            "type":"before_commit",
            "events":
                [
                    {"type":"delete", "msgTypes":["new object", "old object"]}
                ],
            "is_path":false,
            "retry":true,
            "priority": 1,
            "subs_type":"status_merge"
    })";
    GmcDropVertexLabel(stmt, "OP_T0");
    ret = GmcCreateVertexLabel(stmt, g_treeschema, g_tableConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&conn_sub, &stmt_sub, 1, g_epoll_reg_info, "newsubconn", &g_chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 15; i++)
    {
        TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, "OP_T0");
        uint32_t updateindex = 0;
        GmcSubConfigT tsub_info;
        tsub_info.subsName = "newSubTreeMulti001";
        tsub_info.configJson = newSubInfoAddMulti;
        ret = GmcSubscribe(stmt, &tsub_info, conn_sub, NewSnCallBack10, userData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INITIAL_LOAD, end_num);
        ret = GmcUnSubscribe(stmt, "newSubTreeMulti001");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        tsub_info.subsName = "newSubTreeMulti002";
        tsub_info.configJson = newSubInfoAddMulti2;
        ret = GmcSubscribe(stmt, &tsub_info, conn_sub, NewSnCallBack10, userData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *root = NULL, *T3 = NULL;
        char f14_value[8] = "stoooo";
        // 更新node
        for (int i = start_num; i < end_num; i++)
        {
            ret = testGmcPrepareStmtByLabelName(stmt, "OP_T0", GMC_OPERATION_UPDATE);
            EXPECT_EQ(GMERR_OK, ret);

            int64_t f0_value = i;
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
            EXPECT_EQ(GMERR_OK, ret);

            ret = GmcGetRootNode(stmt, &root);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T3", &T3);
            EXPECT_EQ(GMERR_OK, ret);

            // 增量更新vector节点
            // update
            int newValue = i + 100;
            updateindex = 2;
            ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

            // append
            updateindex = 0;
            ret = GmcNodeAppendElement(T3, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

            ret = GmcSetIndexKeyName(stmt, "localhash_key");
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = testWaitSnRecv(userData, GMC_SUB_EVENT_MODIFY, end_num);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcUnSubscribe(stmt, "newSubTreeMulti002");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        tsub_info.subsName = "newSubTreeMulti003";
        tsub_info.configJson = newSubInfoAddMulti3;
        ret = GmcSubscribe(stmt, &tsub_info, conn_sub, NewSnCallBack10, userData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (int i = start_num; i < end_num; i++)
        {
            ret = testGmcPrepareStmtByLabelName(stmt, "OP_T0", GMC_OPERATION_DELETE);
            EXPECT_EQ(GMERR_OK, ret);

            int64_t f0_value = i;
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyName(stmt, "localhash_key");
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = testWaitSnRecv(userData, GMC_SUB_EVENT_DELETE, end_num);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcUnSubscribe(stmt, "newSubTreeMulti003");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(stmt, "OP_T0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"compatibleV3=1\""); 
    system("sh $TEST_HOME/tools/start.sh ");

    AW_FUN_Log(LOG_STEP, "END\n");
}

// 009. 用例循环创建订阅连接执行写更新删除操作取消订阅连接，第二次循环开始删除推送异常
// 链表中数据不是立即释放的，接收到的数据会有差异
// ?{"type":"initial_load", "msgTypes":["new object"]},去掉会卡主
// 【V3单号DTS2023041309076】
TEST_F(NewSubDML_04, SN_034_004_009)
{
    int ret = 0;
    GmcConnT *conn = NULL, *conn_sub = NULL;
    GmcStmtT *stmt = NULL, *stmt_sub = NULL;
    int start_num = 0;
    int end_num = 100;
    int array_num = 3;
    int vector_num = 3;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SnUserDataT *userData = NULL;
    const char *newSubInfoAddMulti = R"(
    {
            "name": "newSubTreeMulti001",
            "label_name": "OP_T0",
            "comment":"status_merge",
            "type":"before_commit",
            "events":
                [
                    {"type":"initial_load", "msgTypes":["new object"]},
                    {"type":"modify", "msgTypes":["new object", "old object"]},
                    {"type":"delete", "msgTypes":["new object", "old object"]}
                ],
            "is_path":false,
            "retry":true,
            "priority": 1,
            "subs_type":"status_merge"
    })";
    GmcDropVertexLabel(stmt, "OP_T0");
    ret = GmcCreateVertexLabel(stmt, g_treeschema, g_tableConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 30; i++)
    {
        ret = testSnMallocUserData(&userData, 100, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testSubConnect(&conn_sub, &stmt_sub, 1, g_epoll_reg_info, "newsubconn", &g_chanRingLen);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t updateindex = 0;
        GmcSubConfigT tsub_info;
        tsub_info.subsName = "newSubTreeMulti001";
        tsub_info.configJson = newSubInfoAddMulti;
        ret = GmcSubscribe(stmt, &tsub_info, conn_sub, NewSnCallBack10, userData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, "OP_T0");
        ret = testWaitStMgSnRecv(userData, GMC_SUB_EVENT_MODIFY, 0, 200);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *root = NULL, *T3 = NULL;
        char f14_value[8] = "stoooo";
        // 更新node
        for (int i = start_num; i < end_num; i++)
        {
            ret = testGmcPrepareStmtByLabelName(stmt, "OP_T0", GMC_OPERATION_UPDATE);
            EXPECT_EQ(GMERR_OK, ret);

            int64_t f0_value = i;
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
            EXPECT_EQ(GMERR_OK, ret);

            ret = GmcGetRootNode(stmt, &root);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T3", &T3);
            EXPECT_EQ(GMERR_OK, ret);

            // 增量更新vector节点
            // update
            int newValue = i + 100;
            updateindex = 2;
            ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

            // append
            updateindex = 0;
            ret = GmcNodeAppendElement(T3, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

            ret = GmcSetIndexKeyName(stmt, "localhash_key");
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = testWaitStMgSnRecv(userData, GMC_SUB_EVENT_MODIFY, 0, 200);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        usleep(25000);
        for (int i = start_num; i < end_num; i++)
        {
            ret = testGmcPrepareStmtByLabelName(stmt, "OP_T0", GMC_OPERATION_DELETE);
            EXPECT_EQ(GMERR_OK, ret);

            int64_t f0_value = i;
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyName(stmt, "localhash_key");
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = testWaitStMgSnRecv(userData, GMC_SUB_EVENT_DELETE, 0, 200);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcUnSubscribe(stmt, "newSubTreeMulti001");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testSubDisConnect(conn_sub, stmt_sub);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testSnFreeUserData(userData);
    }
    ret = GmcDropVertexLabel(stmt, "OP_T0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "END\n");
}

// 010. 写数据，订阅，对账期间异步写数据，服务端core
// 【V3单号DTS2023041108981】
TEST_F(NewSubDML_04, SN_034_004_010)
{
    int ret = 0;
    GmcConnT *conn = NULL, *conn_sub = NULL;
    GmcStmtT *stmt = NULL, *stmt_sub = NULL;
    GmcStmtT *g_stmt_async = NULL;
    GmcConnT *g_conn_async = NULL;
    int start_num = 0;
    int end_num = 10;
    int array_num = 3;
    int vector_num = 3;
    AsyncUserDataT data2 = {0};
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SnUserDataT *userData = NULL;
    ret = testSnMallocUserData(&userData, 100, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *newSubInfoAddMulti = R"(
    {
            "name": "newSubTreeMulti001",
            "label_name": "OP_T0",
            "comment":"status_merge",
            "type":"before_commit",
            "events":
                [
                    {"type":"modify", "msgTypes":["new object", "old object"]},
                    {"type":"delete", "msgTypes":["new object", "old object"]}
                ],
            "is_path":false,
            "retry":true,
            "priority": 1,
            "subs_type":"status_merge"
    })";
    GmcDropVertexLabel(stmt, "OP_T0");
    ret = GmcCreateVertexLabel(stmt, g_treeschema, g_tableConfig2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&conn_sub, &stmt_sub, 1, g_epoll_reg_info, "newsubconn", &g_chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcSubConfigT tsub_info;
    tsub_info.subsName = "newSubTreeMulti001";
    tsub_info.configJson = newSubInfoAddMulti;
    ret = GmcSubscribe(stmt, &tsub_info, conn_sub, NewSnCallBack10, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.readOnly = false;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.trxType = GMC_PESSIMISITIC_TRX;

    ret = GmcTransStart(conn, &config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBeginCheck(stmt, "OP_T0", 0xff);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestGmcInsertVertexdifferentAsync(g_stmt_async, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, "OP_T0");
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;
    char f14_value[8] = "stoooo";
    // 更新node
    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isAbnormals = false;
    ret = GmcEndCheck(stmt, "OP_T0", 0xff, isAbnormals);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, "OP_T0", GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt_async, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // update
        int newValue = i + 100;
        updateindex = 2;
        ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        // append
        updateindex = 0;
        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        ret = GmcSetIndexKeyName(g_stmt_async, "localhash_key");
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT upRequestCtx;
        upRequestCtx.insertCb = update_vertex_callback;
        upRequestCtx.userData = &data2;
        ret = GmcExecuteAsync(g_stmt_async, &upRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data2);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data2.status);
        EXPECT_EQ(1, data2.affectRows);
    }
    for (int i = start_num; i < end_num; i++)
    {
        ret = testGmcPrepareStmtByLabelName(stmt, "OP_T0", GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // update
        updateindex = 0;
        ret = GmcNodeRemoveElementByIndex(T3, updateindex);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(stmt, "localhash_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    for (int i = start_num; i < end_num; i++)
    {
        ret = testGmcPrepareStmtByLabelName(stmt, "OP_T0", GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "localhash_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testWaitStMgSnRecv(userData, GMC_SUB_EVENT_MODIFY, 0, 30);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitStMgSnRecv(userData, GMC_SUB_EVENT_DELETE, 0, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, "newSubTreeMulti001");
    ret = GmcDropVertexLabel(stmt, "OP_T0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);
    AW_FUN_Log(LOG_STEP, "END\n");
}

// 011. 单个状态合并订阅关系，全表订阅modify。先写再创建订阅关系, 覆盖写，更新，对账中更新，删除；服务端core
// 【V3单号DTS2023041016063】
TEST_F(NewSubDML_04, SN_034_004_011)
{
    int ret = 0;
    GmcConnT *conn = NULL, *conn_sub = NULL;
    GmcStmtT *stmt = NULL, *stmt_sub = NULL;
    GmcStmtT *g_stmt_async = NULL;
    GmcConnT *g_conn_async = NULL;
    int start_num = 0;
    int end_num = 10;
    int array_num = 3;
    int vector_num = 3;
    AsyncUserDataT data2 = {0};
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SnUserDataT *userData = NULL;
    ret = testSnMallocUserData(&userData, 100, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *newSubInfoAddMulti = R"(
    {
            "name": "newSubTreeMulti001",
            "label_name": "OP_T0",
            "comment":"status_merge",
            "type":"before_commit",
            "events":
                [
                    {"type":"modify", "msgTypes":["new object", "old object"]},
                    {"type":"delete", "msgTypes":["new object", "old object"]}
                ],
            "is_path":false,
            "retry":true,
            "priority": 1,
            "subs_type":"status_merge"
    })";
    GmcDropVertexLabel(stmt, "OP_T0");
    ret = GmcCreateVertexLabel(stmt, g_treeschema, g_tableConfig2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubConnect(&conn_sub, &stmt_sub, 1, g_epoll_reg_info, "newsubconn", &g_chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubConfigT tsub_info;
    tsub_info.subsName = "newSubTreeMulti001";
    tsub_info.configJson = newSubInfoAddMulti;
    ret = GmcSubscribe(stmt, &tsub_info, conn_sub, NewSnCallBack10, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.readOnly = false;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.trxType = GMC_PESSIMISITIC_TRX;

    ret = GmcTransStart(conn, &config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBeginCheck(stmt, "OP_T0", 0xff);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, "OP_T0");
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;
    char f14_value[8] = "stoooo";
    // 更新node
    for (int i = start_num; i < end_num; i++)
    {
        ret = testGmcPrepareStmtByLabelName(stmt, "OP_T0", GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // update
        int newValue = i + 100;
        updateindex = 2;
        ret = GmcNodeGetElementByIndex(T3, updateindex, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        // append
        updateindex = 0;
        ret = GmcNodeAppendElement(T3, &T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, newValue, 0, f14_value);

        ret = GmcSetIndexKeyName(stmt, "localhash_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    for (int i = start_num; i < end_num; i++)
    {
        ret = testGmcPrepareStmtByLabelName(stmt, "OP_T0", GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // update
        updateindex = 0;
        ret = GmcNodeRemoveElementByIndex(T3, updateindex);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(stmt, "localhash_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    for (int i = start_num; i < end_num; i++)
    {
        ret = testGmcPrepareStmtByLabelName(stmt, "OP_T0", GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "localhash_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isAbnormals = false;
    ret = GmcEndCheck(stmt, "OP_T0", 0xff, isAbnormals);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitStMgSnRecv(userData, GMC_SUB_EVENT_MODIFY, 0, 50);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitStMgSnRecv(userData, GMC_SUB_EVENT_DELETE, 0, 30);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, "newSubTreeMulti001");
    ret = GmcDropVertexLabel(stmt, "OP_T0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(conn_sub, stmt_sub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);
    AW_FUN_Log(LOG_STEP, "END\n");
}

// 012. 使用fd的数量超过8192也能创建订阅通道成功
// 【V3单号DTS2023040414734】
// 【V5单号DTS2023052217743】
TEST_F(NewSubDML_04, SN_034_004_012)
{
    AW_FUN_Log(LOG_STEP, "START\n");
    char errorMsg1[1024] = {};
    char errorMsg2[1024] = {};
    char errorMsg3[1024] = {};
    char errorMsg4[1024] = {};
    char errorMsg5[1024] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_CONNECTION_FAILURE);
    (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_CONNECTION_RESET_BY_PEER);
    (void)snprintf(errorMsg3, sizeof(errorMsg3), "GMERR-%d", GMERR_NO_DATA);
    (void)snprintf(errorMsg4, sizeof(errorMsg4), "GMERR-%d", GMERR_CONNECTION_EXCEPTION);
    (void)snprintf(errorMsg5, sizeof(errorMsg5), "GMERR-%d", GMERR_INTERNAL_ERROR);
    AW_ADD_ERR_WHITE_LIST(5, errorMsg1, errorMsg2, errorMsg3, errorMsg4, errorMsg5);
    system("sysctl -w fs.file-max=20480");
    system("touch file");
    int ret = 0;
    GmcConnT *conn = NULL, *conn_sub = NULL;
    GmcStmtT *stmt = NULL, *stmt_sub = NULL;
    AsyncUserDataT data2 = {0};
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SnUserDataT *userData = NULL;
    ret = testSnMallocUserData(&userData, 100, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int index = 0;
    int file_open_num = 20480;
    FILE *g_fp[file_open_num];
    for (index = 0; index < file_open_num; index++)
    {
        if ((g_fp[index] = fopen("file", "r")) == NULL)
        {
            AW_FUN_Log(LOG_ERROR, "error to open [%d]\n", index);
            break;
        }
    }
    system("sh check_fd.sh fd");
    char *fd_count = NULL;
    readJanssonFile("temp.log", &fd_count);
    if (fd_count)
    {
        AW_FUN_Log(LOG_ERROR, "fd usering end ! fd_count: %s \n", fd_count);
        free(fd_count);
    }
    system("touch temp.log");
    // 创建订阅通道
    ret = testSubConnect(&conn_sub, &stmt_sub, 1, g_epoll_reg_info, "newsubconn", &g_chanRingLen);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);// fd耗尽可能会出现各种错误码，与海马对齐不ok就
    char *fd_count2 = NULL;
    system("sh check_fd.sh fd");
    readJanssonFile("temp.log", &fd_count2);
    if (fd_count2)
    {
        AW_FUN_Log(LOG_ERROR, "fd usering end ! fd_count: %s \n", fd_count2);
        free(fd_count2);
    }
    testSnFreeUserData(userData);
    for (index = 0; index < file_open_num; index++)
    {
        if (g_fp[index] == NULL) {
            break;
        } else {
            fclose(g_fp[index]);
        }
    }
    AddWhiteList(GMERR_FILE_OPERATE_FAILED);
    AW_FUN_Log(LOG_STEP, "END\n");
}

// 013. 创建一张合并订阅的表，下发订阅关系，使用gmimport导入数据，服务端core
// 【V3单号DTS2023040803818】
TEST_F(NewSubDML_04, SN_034_004_013)
{
    AW_FUN_Log(LOG_STEP, "START\n");
    int ret = 0;
    GmcConnT *conn = NULL, *conn_sub = NULL;
    GmcStmtT *stmt = NULL, *stmt_sub = NULL;
    SnUserDataT *userData = NULL;
    ret = testSnMallocUserData(&userData, 100, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName = "newSub001";

    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcUnSubscribe(stmt, subName);
    GmcDropVertexLabel(stmt, "V_1");
    readJanssonFile("./schema_file/NewSubV_1.gmjson", &g_schema);
    AW_MACRO_ASSERT_NE_INT((void *)NULL, g_schema);
    ret = GmcCreateVertexLabel(stmt, g_schema, g_tableConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&conn_sub, &stmt_sub, 1, g_epoll_reg_info, subName, &g_chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcSubConfigT tsub_info;
    tsub_info.subsName = subName;
    tsub_info.configJson = g_newSubInfoAdd;
    ret = GmcSubscribe(stmt, &tsub_info, conn_sub, NewSnCallBack10, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char cmd[1024] = {0};
    (void)snprintf(cmd, 1024, "%s/gmimport -c vdata -f V_1.gmdata -ns %s", g_toolPath, g_testNameSpace);
    system(cmd);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_MODIFY, 10, 24000, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(conn_sub, stmt_sub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, "V_1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_schema);
    testSnFreeUserData(userData);
    AW_FUN_Log(LOG_STEP, "END\n");
}
// 014. hashcluster删除时，由于没有初始化hashcluster_cnt以及索引index，导致在进入存储时参数非法访问非法内存core down
// 不写数据直接删除
// 【V3单号DTS2019122513304】
TEST_F(NewSubDML_04, SN_034_004_014)
{
    AW_FUN_Log(LOG_STEP, "START\n");
    int ret = 0;
    GmcConnT *conn = NULL, *conn_sub = NULL;
    GmcStmtT *stmt = NULL, *stmt_sub = NULL;
    SnUserDataT *userData = NULL;
    ret = testSnMallocUserData(&userData, 100, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName = "newSub001";

    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcUnSubscribe(stmt, subName);
    GmcDropVertexLabel(stmt, "OP_T0");
    readJanssonFile("./schema_file/time_tree.gmjson", &g_schema);
    AW_MACRO_ASSERT_NE_INT((void *)NULL, g_schema);
    ret = GmcCreateVertexLabel(stmt, g_schema, g_tableConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, "OP_T0", GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t timesValue = 0;
    for (uint32_t loop = 0; loop <= 1000; loop++)
    {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &timesValue, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "hashcluster_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(stmt, "OP_T0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_schema);
    testSnFreeUserData(userData);
    AW_FUN_Log(LOG_STEP, "END\n");
}
// 015. hashcluster删除时，由于没有初始化hashcluster_cnt以及索引index，导致在进入存储时参数非法访问非法内存core down
// 写数据删除
// 【V3单号DTS2019122513304】
TEST_F(NewSubDML_04, SN_034_004_015)
{
    AW_FUN_Log(LOG_STEP, "START\n");
    int ret = 0;
    GmcConnT *conn = NULL, *conn_sub = NULL;
    GmcStmtT *stmt = NULL, *stmt_sub = NULL;
    SnUserDataT *userData = NULL;
    ret = testSnMallocUserData(&userData, 100, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName = "newSub001";

    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcUnSubscribe(stmt, subName);
    GmcDropVertexLabel(stmt, "OP_T0");
    readJanssonFile("./schema_file/time_tree.gmjson", &g_schema);
    AW_MACRO_ASSERT_NE_INT((void *)NULL, g_schema);
    ret = GmcCreateVertexLabel(stmt, g_schema, g_tableConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, "OP_T0", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    int64_t valuess = 0;
    uint64_t timesValue = 10110;
    uint32_t timesValue2 = 10111;
    for (int64_t i = 400; i < 403; i++)
    {
        valuess = i;
        timesValue = i;
        timesValue2 = i;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT64, &valuess, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT64, &timesValue, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT32, &timesValue2, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(stmt, "OP_T0", GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t loop = 0; loop <= 1000; loop++)
    {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &timesValue, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "hashcluster_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(stmt, "OP_T0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_schema);
    testSnFreeUserData(userData);
    AW_FUN_Log(LOG_STEP, "END\n");
}

// 016. 【叠加场景】【GX】【启动】【定位时长：1 h】【用例发现：Y】【自动化发现：N】设备静置状态，异常重启
// 原用例的创建无表锁模式对应V5的轻量化事务，写写串行，读写并发，即默认操作
// 【V3单号DTS2019122407669】
TEST_F(NewSubDML_04, SN_034_004_016)
{
#ifdef DIRECT_WRITE
#else
    char errorMsg2[1024] = {};
    (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_CONNECTION_RESET_BY_PEER);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg2);
    AW_FUN_Log(LOG_STEP, "START\n");
    int ret = 0;
    const char *DTSSchema = R"(
    {
    "version": "2.0",
    "type": "record",
    "name": "T0",
    "fields": [
        { "name": "F1", "type": "uint32" ,"nullable":false},
        { "name": "F2", "type": "uint32" ,"nullable":true},
        { "name": "F3", "type": "float","nullable":true},
        { "name": "F4", "type": "double" ,"nullable":true},
        { "name": "F5", "type": "boolean","nullable":true },
        { "name": "F6", "type": "uint32" ,"nullable":true},
        { "name": "F7", "type": "uint32" ,"nullable":true},
        { "name": "F8", "type": "int32" ,"nullable":true},
        { "name": "F9", "type": "uint32" ,"nullable":true},
        { "name": "F10", "type": "int64" ,"nullable":true},
        { "name": "F11", "type": "uint64" ,"nullable":true},
        { "name": "F12", "type": "int" ,"nullable":true},
        { "name": "F13", "type": "int64" ,"nullable":true},
        { "name": "F14", "type": "fixed", "size": 5 ,"nullable":true},
        { "name": "F15", "type": "bytes", "size": 15 ,"nullable":true},
        { "name": "F16", "type": "string", "size": 1500 ,"nullable":true},
        { "name": "F17", "type": "time" ,"nullable":true},
        { "name": "F18", "type": "int64" ,"nullable":true}
    ],
    "keys": [
        {
        "name": "primary_key",
        "index": { "type": "primary" },
        "node": "T0",
        "fields": [ "F1"],
        "constraints": { "unique": true }
        },
        {
        "name":"LOCALHASH_KEY",
        "index":{"type":"localhash"},
        "node":"T0",
        "fields":["F2"],
        "constraints": { "unique": false }
        },
        {
        "name": "local_key",
        "index": {"type": "local"},
        "node": "T0",
        "fields": ["F6"],
        "constraints":{ "unique": false }
        }]
    })";
    GmcConnT *conn = NULL, *conn_sub = NULL;
    GmcStmtT *stmt = NULL, *stmt_sub = NULL;
    SnUserDataT *userData = NULL;
    ret = testSnMallocUserData(&userData, 100, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName = "newSub001";

    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcUnSubscribe(stmt, subName);
    GmcDropVertexLabel(stmt, "T0");
    ret = GmcCreateVertexLabel(stmt, DTSSchema, g_tableConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t f2Value = 10110;
    uint32_t f6Value = 10111;
    uint32_t count = 0;
    for (int j = 0; j < 2; j++)
    {
        for (uint32_t i = 0; i < 20000; i++)
        {
            ret = testGmcPrepareStmtByLabelName(stmt, "T0", GMC_OPERATION_INSERT);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2Value, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_UINT32, &f6Value, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            if (ret != 0)
            {
                EXPECT_EQ(GMERR_CONNECTION_RESET_BY_PEER, ret);
            }
            else
            {
                EXPECT_EQ(GMERR_OK, ret);
            }
            count++;
            if (count == 10000)
            {
                system("kill -9 `pidof gmserver`");
                sleep(3);
            }
        }
        for (uint32_t loop = 0; loop < 20000; loop++)
        {
            ret = testGmcPrepareStmtByLabelName(stmt, "T0", GMC_OPERATION_DELETE);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f2Value, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyName(stmt, "LOCALHASH_KEY");
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            EXPECT_EQ(GMERR_CONNECTION_RESET_BY_PEER, ret);
        }
    }
    ret = GmcDropVertexLabel(stmt, "T0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_RESET_BY_PEER, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testSnFreeUserData(userData);
    FILE *fp2;
    char line[1024] = {0};
    bool isExistCore = true;
    char word[128] = "No such file or directory";
    fp2 = popen("ls core*", "r");
    if (fp2 == NULL)
    {
        AW_FUN_Log(LOG_ERROR, "\n popen error\n");
    }
    while (fgets(line, sizeof(line), fp2))
    {
        if (strstr(line, word) != NULL)
        {
            isExistCore = false;
            AW_FUN_Log(LOG_INFO, "\n FIND\n");
            break;
        }
    }
    pclose(fp2);
#endif
    AW_FUN_Log(LOG_STEP, "END\n");
}

// 【DFX-可靠性】【叠加场景】【GX】【定位时长：0.5H】GX升级B325后批备状态不对，未建立IPC链接所有业务不通
// 【测试】：该场景为多线程全表订阅，某个线程回调卡死导致其余客户端无法订阅，属于测试场景遗漏，
// 测试中已覆盖多线程订阅，但未考虑其中某个线程故障情况，已针对该问题补充普通表订阅、kv表订阅、
// 老化数据订阅、lpm订阅等多线程订阅场景
// 4个用例，主用例创造线程，创造线程执行1个阻塞1个正常写数据，对应2个回调，回调正常执行操作
// 一般复杂表+lpm4表+对账
int flag = 0;
int flag2 = 0;
void lpmv4CallBack(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    printf("************%s : %s**************\n", "lpmv4CallBack", "lpmv4_sub");
    uint32_t i;
    while (flag2 == 0)
    {
        sleep(10);
        flag = 1;
    }
    int subNum = 0;
    int subPushModify = 0;
    int subInital = 0;
    int subPushDel = 0;
    int index = 0;
    char labelName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;
    int ret = 0;
    bool eof = false;
    uint32_t getValueSizeOut;
    char readValueOut[128];
    for (int i = 0; i < info->labelCount; i++)
    {
        memset(labelName, 0, sizeof(labelName));
        labelNameLen = MAX_NAME_LENGTH;
        if (info->eventType != GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
                ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
                EXPECT_EQ(GMERR_OK, ret);
                EXPECT_EQ(strlen(labelName), labelNameLen);
        }
        switch (info->eventType)
        {
        case GMC_SUB_EVENT_MODIFY:
        {
            ret = GmcFetch(subStmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (ret != GMERR_OK || eof == true)
            {
                break;
            }
            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);
            index = ((int *)user_data->new_value)[user_data->subIndex];
            break;
        }
        case GMC_SUB_EVENT_DELETE:
        {
            // check old value
            ret = GmcFetch(subStmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (ret != GMERR_OK || eof == true)
            {
                break;
            }
            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
            EXPECT_EQ(GMERR_OK, ret);
            index = ((int *)user_data->old_value)[user_data->subIndex];
            break;
        }
        case GMC_SUB_EVENT_INITIAL_LOAD:
        {
            ret = GmcFetch(subStmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (ret != GMERR_OK || eof == true)
            {
                break;
            }
            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);
            index = ((int *)user_data->new_value)[user_data->subIndex];
            break;
        }
        case GMC_SUB_EVENT_INITIAL_LOAD_EOF:
        {
            ;
        }
        default:
        {
            printf("default: invalid eventType\r\n");
            break;
        }
        }
    }
    switch (info->eventType)
    {
    case GMC_SUB_EVENT_MODIFY:
    {
        user_data->insertNum++;
        break;
    }
    case GMC_SUB_EVENT_DELETE:
    {
        user_data->deleteNum++;
        break;
    }
    case GMC_SUB_EVENT_INITIAL_LOAD:
    {
        user_data->scanNum++;
        break;
    }
    case GMC_SUB_EVENT_INITIAL_LOAD_EOF:
    {
        user_data->scanEofNum++;
        break;
    }
    default:
    {
        printf("default: eventType\r\n");
        break;
    }
    }
}
void lpmv4CallBack2(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    printf("************%s : %s**************\n", "lpmv4CallBack2", "lpmv4_sub");
    int subNum = 0;
    int subPushModify = 0;
    int subInital = 0;
    int subPushDel = 0;
    int index = 0;
    char labelName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;
    int ret = 0;
    bool eof = false;
    uint32_t getValueSizeOut;
    char readValueOut[128];
    for (int i = 0; i < info->labelCount; i++)
    {
        memset(labelName, 0, sizeof(labelName));
        labelNameLen = MAX_NAME_LENGTH;
        if (info->eventType != GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
                ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
                EXPECT_EQ(GMERR_OK, ret);
                EXPECT_EQ(strlen(labelName), labelNameLen);
        }
        switch (info->eventType)
        {
        case GMC_SUB_EVENT_MODIFY:
        {
            ret = GmcFetch(subStmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (ret != GMERR_OK || eof == true)
            {
                break;
            }
            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);
            index = ((int *)user_data->new_value)[user_data->subIndex];
            break;
        }
        case GMC_SUB_EVENT_DELETE:
        {
            // check old value
            ret = GmcFetch(subStmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (ret != GMERR_OK || eof == true)
            {
                break;
            }
            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
            EXPECT_EQ(GMERR_OK, ret);
            index = ((int *)user_data->old_value)[user_data->subIndex];
            break;
        }
        case GMC_SUB_EVENT_INITIAL_LOAD:
        {
            ret = GmcFetch(subStmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (ret != GMERR_OK || eof == true)
            {
                break;
            }
            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);
            index = ((int *)user_data->new_value)[user_data->subIndex];
            break;
        }
        case GMC_SUB_EVENT_INITIAL_LOAD_EOF:
        {
            ;
        }
        default:
        {
            printf("default: invalid eventType\r\n");
            break;
        }
        }
    }
    switch (info->eventType)
    {
    case GMC_SUB_EVENT_MODIFY:
    {
        user_data->insertNum++;
        break;
    }
    case GMC_SUB_EVENT_DELETE:
    {
        user_data->deleteNum++;
        break;
    }
    case GMC_SUB_EVENT_INITIAL_LOAD:
    {
        user_data->scanNum++;
        break;
    }
    case GMC_SUB_EVENT_INITIAL_LOAD_EOF:
    {
        user_data->scanEofNum++;
        break;
    }
    default:
    {
        printf("default: eventType\r\n");
        break;
    }
    }
}
void *lpmv4_thread(void *args)
{
    GmcConnT *conn = NULL, *conn2 = NULL, *conn_sub = NULL, *conn_sub2 = NULL;
    GmcStmtT *stmt = NULL, *stmt2 = NULL, *stmt_sub = NULL, *stmt_sub2 = NULL;
    int i = *((int *)args);
    int ret = 0;
    if (i == 0)
    {
        YangConnOptionT connOptions = {0};
        connOptions.epollFd = &g_epAsync[0].userEpollFd;
        ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char *fileSchema = NULL;
        GmcDropVertexLabel(stmt, "ip4forward");
        readJanssonFile("./schema_file/ip4forward_lpm4.gmjson", &fileSchema);
        AW_MACRO_EXPECT_NE_INT((void *)NULL, fileSchema);
        ret = GmcCreateVertexLabel(stmt, fileSchema, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        free(fileSchema);
        const char *newSubInfoAddMulti = R"(
        {
                "name": "newSubMulti001",
                "label_name": "ip4forward",
                "comment":"status_merge",
                "type":"before_commit",
                "events":
                    [
                        {"type":"initial_load", "msgTypes":["new object"]},
                        {"type":"modify", "msgTypes":["new object", "old object"]},
                        {"type":"delete", "msgTypes":["new object", "old object"]}
                    ],
                "is_path":false,
                "retry":true,
                "priority": 1,
                "subs_type":"status_merge"
        })";
        GmcSubConfigT tsub_info;
        tsub_info.subsName = "newSubMulti001";
        tsub_info.configJson = newSubInfoAddMulti;
        SnUserDataT *userData = NULL;
        connOptions.connName = "newSubMulti001";
        ret = TestYangGmcConnect(&conn_sub, &stmt_sub, GMC_CONN_TYPE_SUB, &connOptions);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testSnMallocUserData(&userData, 100, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSubscribe(stmt, &tsub_info, conn_sub, lpmv4CallBack, userData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitSnRecv(userData, GMC_SUB_EVENT_MODIFY, 10);
        AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
        ret = GmcUnSubscribe(stmt, "newSubMulti001");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testSubDisConnect(conn_sub, stmt_sub);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testSnFreeUserData(userData);
    }
    if (i == 1)
    {
        while (flag == 0)
        {
            sleep(1);
            printf("flag : %d please wait......\n", flag);
        }
        printf("flag : %d start sec sub......\n", flag);
        YangConnOptionT connOptions = {0};
        connOptions.epollFd = &g_epAsync[1].userEpollFd;
        ret = TestYangGmcConnect(&conn2, &stmt2, GMC_CONN_TYPE_SYNC, &connOptions);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char *fileSchema = NULL;
        const char *newSubInfoAddMulti = R"(
        {
                "name": "newSubMulti002",
                "label_name": "ip4forward",
                "comment":"status_merge",
                "type":"before_commit",
                "events":
                    [
                        {"type":"initial_load", "msgTypes":["new object"]},
                        {"type":"modify", "msgTypes":["new object", "old object"]},
                        {"type":"delete", "msgTypes":["new object", "old object"]}
                    ],
                "is_path":false,
                "retry":true,
                "priority": 1,
                "subs_type":"status_merge"
        })";
        GmcSubConfigT tsub_info;
        tsub_info.subsName = "newSubMulti002";
        tsub_info.configJson = newSubInfoAddMulti;
        SnUserDataT *userData2 = NULL;
        connOptions.connName = "newSubMulti002";
        ret = TestYangGmcConnect(&conn_sub2, &stmt_sub2, GMC_CONN_TYPE_SUB, &connOptions);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testSnMallocUserData(&userData2, 100, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSubscribe(stmt2, &tsub_info, conn_sub2, lpmv4CallBack2, userData2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(stmt2, "ip4forward", GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t vr_idValue = 0;
        uint32_t vrf_indexValue = 1;
        uint32_t dest_ip_addrValue = 1;
        uint8_t mask_lenValue = 1;
        for (uint32_t i = 0; i < 100; i++)
        {
            vr_idValue = i % 15;
            ret = GmcSetVertexProperty(stmt2, "vr_id", GMC_DATATYPE_UINT32, &vr_idValue, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetVertexProperty(stmt2, "vrf_index", GMC_DATATYPE_UINT32, &vrf_indexValue, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            dest_ip_addrValue = i;
            ret = GmcSetVertexProperty(stmt2, "dest_ip_addr", GMC_DATATYPE_UINT32, &dest_ip_addrValue, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            mask_lenValue = i % 32;
            ret = GmcSetVertexProperty(stmt2, "mask_len", GMC_DATATYPE_UINT8, &mask_lenValue, sizeof(uint8_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt2);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = testWaitSnRecv(userData2, GMC_SUB_EVENT_MODIFY, 100, 4800);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcUnSubscribe(stmt2, "newSubMulti002");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testSubDisConnect(conn_sub2, stmt_sub2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testSnFreeUserData(userData2);
    }
    return NULL;
}
// 017. lpm4表【V3单号DTS2019122303535】用例模拟故障无法自动化构建
TEST_F(NewSubDML_04, SN_034_004_017)
{
    AW_FUN_Log(LOG_STEP, "START\n");
    int ret = 0;
    pthread_t write_thr[3];
    int conn_id = 0;
    int index[100] = {0};
    for (int i = 0; i < 2; i++)
    {
        ret = create_epoll_thread(&g_epAsync[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    for (conn_id = 0; conn_id < 2; conn_id++)
    {
        index[conn_id] = conn_id;
        ret = pthread_create(&write_thr[conn_id], NULL, &lpmv4_thread, (void *)&index[conn_id]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    for (conn_id = 0; conn_id < 2; conn_id++)
    {
        pthread_join(write_thr[conn_id], 0);
    }
    for (int i = 0; i < 2; i++)
    {
        ret = close_epoll_thread(&g_epAsync[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "END\n");
}

void normalCallBack(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    uint32_t i;
    while (flag2 == 0)
    {
        sleep(10);
        flag = 1;
    }
    int subNum = 0;
    int subPushModify = 0;
    int subInital = 0;
    int subPushDel = 0;
    int index = 0;
    char labelName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;
    int ret = 0;
    bool eof = false;
    uint32_t getValueSizeOut;
    char readValueOut[128];
    for (int i = 0; i < info->labelCount; i++)
    {
        memset(labelName, 0, sizeof(labelName));
        labelNameLen = MAX_NAME_LENGTH;
        if (info->eventType != GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
                ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
                EXPECT_EQ(GMERR_OK, ret);
                EXPECT_EQ(strlen(labelName), labelNameLen);
        }
        switch (info->eventType)
        {
        case GMC_SUB_EVENT_MODIFY:
        {
            ret = GmcFetch(subStmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (ret != GMERR_OK || eof == true)
            {
                break;
            }
            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);
            index = ((int *)user_data->new_value)[user_data->subIndex];
            break;
        }
        case GMC_SUB_EVENT_DELETE:
        {
            // check old value
            ret = GmcFetch(subStmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (ret != GMERR_OK || eof == true)
            {
                break;
            }
            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
            EXPECT_EQ(GMERR_OK, ret);
            index = ((int *)user_data->old_value)[user_data->subIndex];
            break;
        }
        case GMC_SUB_EVENT_INITIAL_LOAD:
        {
            ret = GmcFetch(subStmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (ret != GMERR_OK || eof == true)
            {
                break;
            }
            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);
            index = ((int *)user_data->new_value)[user_data->subIndex];
            break;
        }
        case GMC_SUB_EVENT_INITIAL_LOAD_EOF:
        {
           ;
        }
        default:
        {
            printf("default: invalid eventType\r\n");
            break;
        }
        }
    }
    switch (info->eventType)
    {
    case GMC_SUB_EVENT_MODIFY:
    {
        user_data->insertNum++;
        break;
    }
    case GMC_SUB_EVENT_DELETE:
    {
        user_data->deleteNum++;

        break;
    }
    case GMC_SUB_EVENT_INITIAL_LOAD:
    {
        user_data->scanNum++;

        break;
    }
    case GMC_SUB_EVENT_INITIAL_LOAD_EOF:
    {
        user_data->scanEofNum++;
        break;
    }
    default:
    {
        printf("default: eventType\r\n");
        break;
    }
    }
}
void normalCallBack2(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int subNum = 0;
    int subPushModify = 0;
    int subInital = 0;
    int subPushDel = 0;
    int index = 0;
    char labelName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;
    int ret = 0;
    bool eof = false;
    uint32_t getValueSizeOut;
    char readValueOut[128];
    for (int i = 0; i < 1; i++)
    {
        memset(labelName, 0, sizeof(labelName));
        labelNameLen = MAX_NAME_LENGTH;
        if (info->eventType != GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
                ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
                EXPECT_EQ(GMERR_OK, ret);
                EXPECT_EQ(strlen(labelName), labelNameLen);
        }
        switch (info->eventType)
        {
        case GMC_SUB_EVENT_MODIFY:
        {
            ret = GmcFetch(subStmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (ret != GMERR_OK || eof == true)
            {
                break;
            }
            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);
            index = ((int *)user_data->new_value)[user_data->subIndex];
            break;
        }
        case GMC_SUB_EVENT_DELETE:
        {
            // check old value
            ret = GmcFetch(subStmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (ret != GMERR_OK || eof == true)
            {
                break;
            }
            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
            EXPECT_EQ(GMERR_OK, ret);
            index = ((int *)user_data->old_value)[user_data->subIndex];
            break;
        }
        case GMC_SUB_EVENT_INITIAL_LOAD:
        {
            ret = GmcFetch(subStmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (ret != GMERR_OK || eof == true)
            {
                break;
            }
            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);
            index = ((int *)user_data->new_value)[user_data->subIndex];
            break;
        }
        case GMC_SUB_EVENT_INITIAL_LOAD_EOF:
        {
            ;
        }
        default:
        {
            printf("default: invalid eventType\r\n");
            break;
        }
        }
    }
    switch (info->eventType)
    {
    case GMC_SUB_EVENT_MODIFY:
    {
        user_data->insertNum++;
        break;
    }
    case GMC_SUB_EVENT_DELETE:
    {
        user_data->deleteNum++;
        break;
    }
    case GMC_SUB_EVENT_INITIAL_LOAD:
    {
        user_data->scanNum++;
        break;
    }
    case GMC_SUB_EVENT_INITIAL_LOAD_EOF:
    {
        user_data->scanEofNum++;
        break;
    }
    default:
    {
        printf("default: eventType\r\n");
        break;
    }
    }
}
void *normal_thread(void *args)
{
    GmcConnT *conn = NULL, *conn2 = NULL, *conn_sub = NULL, *conn_sub2 = NULL;
    GmcStmtT *stmt = NULL, *stmt2 = NULL, *stmt_sub = NULL, *stmt_sub2 = NULL;
    int i = *((int *)args);
    int ret = 0;
    if (i == 0)
    {
        YangConnOptionT connOptions = {0};
        connOptions.epollFd = &g_epAsync[0].userEpollFd;
        ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char *fileSchema = NULL;
        GmcDropVertexLabel(stmt, "V_1");
        readJanssonFile("./schema_file/NewSubV_1.gmjson", &fileSchema);
        AW_MACRO_EXPECT_NE_INT((void *)NULL, fileSchema);
        ret = GmcCreateVertexLabel(stmt, fileSchema, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        free(fileSchema);
        const char *newSubInfoAddMulti = R"(
        {
                "name": "newSubMulti001",
                "label_name": "V_1",
                "comment":"status_merge",
                "type":"before_commit",
                "events":
                    [
                        {"type":"initial_load", "msgTypes":["new object"]},
                        {"type":"modify", "msgTypes":["new object", "old object"]},
                        {"type":"delete", "msgTypes":["new object", "old object"]}
                    ],
                "is_path":false,
                "retry":true,
                "priority": 1,
                "subs_type":"status_merge"
        })";
        GmcSubConfigT tsub_info;
        tsub_info.subsName = "newSubMulti001";
        tsub_info.configJson = newSubInfoAddMulti;
        SnUserDataT *userData = NULL;
        connOptions.connName = "newSubMulti001";
        ret = TestYangGmcConnect(&conn_sub, &stmt_sub, GMC_CONN_TYPE_SUB, &connOptions);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testSnMallocUserData(&userData, 100000, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSubscribe(stmt, &tsub_info, conn_sub, normalCallBack, userData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitSnRecv(userData, GMC_SUB_EVENT_MODIFY, 100000, 48000);
        AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    }
    if (i == 1)
    {
        while (flag == 0)
        {
            sleep(1);
            printf("flag : %d please wait......\n", flag);
        }
        printf("flag : %d start sec sub......\n", flag);
        YangConnOptionT connOptions = {0};
        connOptions.epollFd = &g_epAsync[1].userEpollFd;
        ret = TestYangGmcConnect(&conn2, &stmt2, GMC_CONN_TYPE_SYNC, &connOptions);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char *fileSchema = NULL;
        const char *newSubInfoAddMulti = R"(
        {
                "name": "newSubMulti002",
                "label_name": "V_1",
                "comment":"status_merge",
                "type":"before_commit",
                "events":
                    [
                        {"type":"initial_load", "msgTypes":["new object"]},
                        {"type":"modify", "msgTypes":["new object", "old object"]},
                        {"type":"delete", "msgTypes":["new object", "old object"]}
                    ],
                "is_path":false,
                "retry":true,
                "priority": 1,
                "subs_type":"status_merge"
        })";
        GmcSubConfigT tsub_info;
        tsub_info.subsName = "newSubMulti002";
        tsub_info.configJson = newSubInfoAddMulti;
        SnUserDataT *userData2 = NULL;
        connOptions.connName = "newSubMulti002";
        ret = TestYangGmcConnect(&conn_sub2, &stmt_sub2, GMC_CONN_TYPE_SUB, &connOptions);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testSnMallocUserData(&userData2, 100000, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSubscribe(stmt2, &tsub_info, conn_sub2, normalCallBack2, userData2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        sleep(3);
        ret = NewSub_Vertex_DML(conn2, stmt2, (char *)"V_1", SINGLE_INSERT, g_ezdata, 100000, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        system("gmsysview count");
        ret = testWaitSnRecv(userData2, GMC_SUB_EVENT_MODIFY, 100000, 48000);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testSnFreeUserData(userData2);
    }
    return NULL;
}
// 018. 普通表【V3单号DTS2019122303535】用例模拟故障无法自动化构建
TEST_F(NewSubDML_04, SN_034_004_018)
{
    AW_FUN_Log(LOG_STEP, "START\n");
    int ret = 0;
    pthread_t write_thr[3];
    int conn_id = 0;
    int index[100] = {0};
    for (int i = 0; i < 2; i++)
    {
        ret = create_epoll_thread(&g_epAsync[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    for (conn_id = 0; conn_id < 2; conn_id++)
    {
        index[conn_id] = conn_id;
        ret = pthread_create(&write_thr[conn_id], NULL, &normal_thread, (void *)&index[conn_id]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    for (conn_id = 0; conn_id < 2; conn_id++)
    {
        pthread_join(write_thr[conn_id], 0);
    }
    for (int i = 0; i < 2; i++)
    {
        ret = close_epoll_thread(&g_epAsync[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "END\n");
}
void checkCallBack(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    uint32_t i;
    while (flag2 == 0)
    {
        sleep(10);
        flag = 1;
        usleep(300);
        flag2 = 15;
    }
    int subNum = 0;
    int subPushModify = 0;
    int subInital = 0;
    int subPushDel = 0;
    int index = 0;
    char labelName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;
    int ret = 0;
    bool eof = false;
    uint32_t getValueSizeOut;
    char readValueOut[128];
    for (int i = 0; i < info->labelCount; i++)
    {
        memset(labelName, 0, sizeof(labelName));
        labelNameLen = MAX_NAME_LENGTH;
        if (info->eventType != GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
                ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
                EXPECT_EQ(GMERR_OK, ret);
                EXPECT_EQ(strlen(labelName), labelNameLen);
        }
        switch (info->eventType)
        {
        case GMC_SUB_EVENT_MODIFY:
        {
            ret = GmcFetch(subStmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (ret != GMERR_OK || eof == true)
            {
                break;
            }
            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);
            index = ((int *)user_data->new_value)[user_data->subIndex];
            break;
        }
        case GMC_SUB_EVENT_DELETE:
        {
            // check old value
            ret = GmcFetch(subStmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (ret != GMERR_OK || eof == true)
            {
                break;
            }
            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
            EXPECT_EQ(GMERR_OK, ret);
            index = ((int *)user_data->old_value)[user_data->subIndex];
            break;
        }
        case GMC_SUB_EVENT_INITIAL_LOAD:
        {
            ret = GmcFetch(subStmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (ret != GMERR_OK || eof == true)
            {
                break;
            }
            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);
            index = ((int *)user_data->new_value)[user_data->subIndex];
            break;
        }
        case GMC_SUB_EVENT_INITIAL_LOAD_EOF:
        {
            ;
        }
        default:
        {
            printf("default: invalid eventType\r\n");
            break;
        }
        }
    }
    switch (info->eventType)
    {
    case GMC_SUB_EVENT_MODIFY:
    {
        user_data->insertNum++;
        break;
    }
    case GMC_SUB_EVENT_DELETE:
    {
        user_data->deleteNum++;
        break;
    }
    case GMC_SUB_EVENT_INITIAL_LOAD:
    {
        user_data->scanNum++;
        break;
    }
    case GMC_SUB_EVENT_INITIAL_LOAD_EOF:
    {
        user_data->scanEofNum++;
        break;
    }
    default:
    {
        printf("default: eventType\r\n");
        break;
    }
    }
}
void checkCallBack2(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int subNum = 0;
    int subPushModify = 0;
    int subInital = 0;
    int subPushDel = 0;
    int index = 0;
    char labelName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;
    int ret = 0;
    bool eof = false;
    uint32_t getValueSizeOut;
    char readValueOut[128];

    /* 多表情景下不好保证推送得到的表顺序是恒定的
        */
    for (int i = 0; i < info->labelCount; i++)
    {
        memset(labelName, 0, sizeof(labelName));
        labelNameLen = MAX_NAME_LENGTH;
        if (info->eventType != GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
                ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
                EXPECT_EQ(GMERR_OK, ret);
                EXPECT_EQ(strlen(labelName), labelNameLen);
        }
        switch (info->eventType)
        {
        case GMC_SUB_EVENT_MODIFY:
        {
            ret = GmcFetch(subStmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (ret != GMERR_OK || eof == true)
            {
                break;
            }
            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);
            index = ((int *)user_data->new_value)[user_data->subIndex];
            break;
        }
        case GMC_SUB_EVENT_DELETE:
        {
            // check old value
            ret = GmcFetch(subStmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (ret != GMERR_OK || eof == true)
            {
                break;
            }
            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
            EXPECT_EQ(GMERR_OK, ret);
            index = ((int *)user_data->old_value)[user_data->subIndex];
            break;
        }
        case GMC_SUB_EVENT_INITIAL_LOAD:
        {
            ret = GmcFetch(subStmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (ret != GMERR_OK || eof == true)
            {
                break;
            }
            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);
            index = ((int *)user_data->new_value)[user_data->subIndex];
            break;
        }
        case GMC_SUB_EVENT_INITIAL_LOAD_EOF:
        {
           ;
        }
        default:
        {
            printf("default: invalid eventType\r\n");
            break;
        }
        }
    }
    switch (info->eventType)
    {
    case GMC_SUB_EVENT_MODIFY:
    {
        user_data->insertNum++;
        break;
    }
    case GMC_SUB_EVENT_DELETE:
    {
        user_data->deleteNum++;
        break;
    }
    case GMC_SUB_EVENT_INITIAL_LOAD:
    {
        user_data->scanNum++;
        break;
    }
    case GMC_SUB_EVENT_INITIAL_LOAD_EOF:
    {
        user_data->scanEofNum++;
        break;
    }
    default:
    {
        printf("default: eventType\r\n");
        break;
    }
    }
}
void *check_thread(void *args)
{
    GmcConnT *conn = NULL, *conn2 = NULL, *conn_sub = NULL, *conn_sub2 = NULL;
    GmcStmtT *stmt = NULL, *stmt2 = NULL, *stmt_sub = NULL, *stmt_sub2 = NULL;
    int i = *((int *)args);
    int ret = 0;
    if (i == 0)
    {
        ret = testGmcConnect(&conn, &stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char *fileSchema = NULL;
        GmcDropVertexLabel(stmt, "V_1");
        readJanssonFile("./schema_file/NewSubV_1.gmjson", &fileSchema);
        AW_MACRO_EXPECT_NE_INT((void *)NULL, fileSchema);
        ret = GmcCreateVertexLabel(stmt, fileSchema, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        free(fileSchema);
        const char *newSubInfoAddMulti = R"(
        {
                "name": "newSubMulti001",
                "label_name": "V_1",
                "comment":"status_merge",
                "type":"before_commit",
                "events":
                    [
                        {"type":"initial_load", "msgTypes":["new object"]},
                        {"type":"modify", "msgTypes":["new object", "old object"]},
                        {"type":"delete", "msgTypes":["new object", "old object"]}
                    ],
                "is_path":false,
                "retry":true,
                "priority": 1,
                "subs_type":"status_merge"
        })";
        GmcSubConfigT tsub_info;
        tsub_info.subsName = "newSubMulti001";
        tsub_info.configJson = newSubInfoAddMulti;
        SnUserDataT *userData = NULL;
        bool isAbnormal = false;
        GmcCheckInfoT *checkInfo;
        GmcCheckStatusE checkStatus;
        ret = testSubConnect(&conn_sub, &stmt_sub, 1, g_epoll_reg_info, "newSubMulti001", &g_chanRingLen);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testSnMallocUserData(&userData, 10000, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSubscribe(stmt, &tsub_info, conn_sub, checkCallBack, userData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitSnRecv(userData, GMC_SUB_EVENT_MODIFY, 10000);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        sleep(11);
        ret = GmcUnSubscribe(stmt, "newSubMulti001");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testSubDisConnect(conn_sub, stmt_sub);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testSnFreeUserData(userData);
    }
    if (i == 1)
    {
        while (flag == 0)
        {
            sleep(1);
            printf("flag : %d please wait......\n", flag);
        }
        printf("flag : %d start sec sub......\n", flag);
        ret = testGmcConnect(&conn2, &stmt2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char *fileSchema = NULL;
        GmcCheckInfoT *checkInfo;
        GmcCheckStatusE checkStatus;
        const char *newSubInfoAddMulti = R"(
        {
                "name": "newSubMulti002",
                "label_name": "V_1",
                "comment":"status_merge",
                "type":"before_commit",
                "events":
                    [
                        {"type":"initial_load", "msgTypes":["new object"]},
                        {"type":"modify", "msgTypes":["new object", "old object"]},
                        {"type":"delete", "msgTypes":["new object", "old object"]}
                    ],
                "is_path":false,
                "retry":true,
                "priority": 1,
                "subs_type":"status_merge"
        })";
        GmcSubConfigT tsub_info;
        tsub_info.subsName = "newSubMulti002";
        tsub_info.configJson = newSubInfoAddMulti;
        SnUserDataT *userData2 = NULL;
        ret = testSubConnect(&conn_sub2, &stmt_sub2, 1, g_epoll_reg_info, "newSubMulti002", &g_chanRingLen);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testSnMallocUserData(&userData2, 10000, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSubscribe(stmt2, &tsub_info, conn_sub2, checkCallBack2, userData2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBeginCheck(stmt2, "V_1", 0xff);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = NewSub_Vertex_DML(conn2, stmt2, (char *)"V_1", SINGLE_INSERT, g_ezdata, 10000, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcEndCheck(stmt2, "V_1", 0xff, isAbnormal);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetCheckInfo(stmt2, "V_1", 0xff, &checkInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetCheckStatus(checkInfo, &checkStatus);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(checkStatus, GMC_CHECK_STATUS_NORMAL);
        sleep(3);
        ret = testWaitSnRecv(userData2, GMC_SUB_EVENT_MODIFY, 10000); // 【DTS】为什么此处接收不到数据？
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcUnSubscribe(stmt2, "newSubMulti002");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testSubDisConnect(conn_sub2, stmt_sub2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testSnFreeUserData(userData2);
    }
    return NULL;
}
// 019. 表对账【V3单号DTS2019122303535】用例模拟故障无法自动化构建
TEST_F(NewSubDML_04, SN_034_004_019)
{
    AW_FUN_Log(LOG_STEP, "START\n");
    int ret = 0;
    pthread_t write_thr[3];
    int conn_id = 0;
    int index[100] = {0};
    for (int i = 0; i < 2; i++)
    {
        ret = create_epoll_thread(&g_epAsync[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    for (conn_id = 0; conn_id < 2; conn_id++)
    {
        index[conn_id] = conn_id;
        ret = pthread_create(&write_thr[conn_id], NULL, &check_thread, (void *)&index[conn_id]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    for (conn_id = 0; conn_id < 2; conn_id++)
    {
        pthread_join(write_thr[conn_id], 0);
    }
    for (int i = 0; i < 2; i++)
    {
        ret = close_epoll_thread(&g_epAsync[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "END\n");
}
typedef struct TagSnUserDataT2
{
    void *old_value;
    void *new_value;
    bool *isReplace_insert; // 0|replace已存在的数据， 1|replace不存在的数据
    int subIndex;           // 用于存储预期数据(old_value、new_value、isReplace_insert)的数组下标
    int threadId;           // 用户自定义的线程ID，用于区分线程
    int insertNum;
    int updateNum;
    int deleteNum;
    int replaceNum;
    int replaceInsertNum;
    int replaceUpdateNum;
    int mergeNum;
    int mergeInsertNum;
    int mergeUpdateNum;
    int kvSetNum;
    int scanNum;
    int scanEofNum;
    int agedNum;
    int triggerScanBeginNum;
    int triggerScanNum;
    int triggerScanEndNum;
    uint32_t callbackTimes;
    uint8_t *dataCheckIndexes;
    bool blocking;
} SnUserDataT2;
int testSnMallocUserData2(SnUserDataT2 **userData, uint32_t sizeMalloc, uint32_t checkNum = 0)
{
    SnUserDataT2 *user_data = NULL;
    user_data = (SnUserDataT2 *)malloc(sizeof(SnUserDataT2));
    if (!user_data)
    {
        printf("[testSnMallocUserData] user_data malloc failed\n");
        return -1;
    }
    memset(user_data, 0, sizeof(SnUserDataT2));

    user_data->new_value = (int *)malloc(sizeof(int) * sizeMalloc);
    if (!user_data->new_value)
    {
        printf("[testSnMallocUserData] user_data->new_value malloc failed\n");
        return -1;
    }
    memset(user_data->new_value, 0, sizeof(int) * sizeMalloc);

    user_data->old_value = (int *)malloc(sizeof(int) * sizeMalloc);
    if (!user_data->old_value)
    {
        printf("[testSnMallocUserData] user_data->old_value malloc failed\n");
        return -1;
    }
    memset(user_data->old_value, 0, sizeof(int) * sizeMalloc);

    user_data->isReplace_insert = (bool *)malloc(sizeof(bool) * sizeMalloc);
    if (!user_data->isReplace_insert)
    {
        printf("[testSnMallocUserData] user_data->isReplace_insert malloc failed\n");
        return -1;
    }
    memset(user_data->isReplace_insert, 0, sizeof(bool) * sizeMalloc);

    if (checkNum > 0)
    {
        user_data->dataCheckIndexes = (uint8_t *)malloc(sizeof(uint8_t) * checkNum);
        if (!user_data->dataCheckIndexes)
        {
            printf("[testSnMallocUserData] user_data->dataCheckIndexes malloc failed\n");
            return -1;
        }
        memset(user_data->dataCheckIndexes, 0, sizeof(uint8_t) * checkNum);
    }

    *userData = user_data;
    return 0;
}

// 释放 订阅UserData malloc申请的内存
void testSnFreeUserData2(SnUserDataT2 *userData)
{
    free(userData->new_value);
    free(userData->old_value);
    free(userData->isReplace_insert);
    if (userData->dataCheckIndexes != NULL)
    {
        free(userData->dataCheckIndexes);
    }
    free(userData);
}

void sn_callback_num(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    SnUserDataT2 *user_data = (SnUserDataT2 *)userData;
    while (blocking == true)
    {
        usleep(100);
    }
    int ret;
    int index, i;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;
    void *label = 0;
    const void *keyValue = 0;
    uint32_t size;
    GmcConnT *conn_sync = 0;
    GmcStmtT *stmt_sync = 0;
    char keyName[MAX_NAME_LENGTH] = {0};

    bool eof = false;
    while (!eof)
    {
        ret = GmcFetch(subStmt, &eof);
        if (ret != GMERR_OK)
        {
            if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF)
            {
                user_data->scanEofNum++;
                EXPECT_EQ(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, ret);
                printf("[INFO] <---GMC_SUB_EVENT_INITIAL_LOAD_EOF IS OVER--->\r\n");
                break;
            }
            else if (info->eventType == GMC_SUB_EVENT_TRIGGER_SCAN_BEGIN)
            {
                user_data->triggerScanBeginNum++;
                printf("[INFO] <---GMC_SUB_EVENT_TRIGGER_SCAN_BEGIN --->\r\n");
                break;
            }
            else if (info->eventType == GMC_SUB_EVENT_TRIGGER_SCAN_END)
            {
                user_data->triggerScanEndNum++;
                printf("[INFO] <---GMC_SUB_EVENT_TRIGGER_SCAN_END --->\r\n");
                break;
            }
            else
            {
                printf("[INFO] <---Subs Abnormal--!!!-->%d\r\n", ret);
                break;
            }
        }
        else if (eof == true)
        {
            break;
        }
        switch (info->eventType)
        {
        case GMC_SUB_EVENT_INSERT:
        {
            user_data->insertNum++;
            break;
        }
        case GMC_SUB_EVENT_DELETE:
        {
            user_data->deleteNum++;
            break;
        }
        case GMC_SUB_EVENT_UPDATE:
        {
            user_data->updateNum++;
            break;
        }
        case GMC_SUB_EVENT_REPLACE_INSERT:
        {
            user_data->replaceInsertNum++;
            break;
        }
        case GMC_SUB_EVENT_REPLACE_UPDATE:
        {
            user_data->replaceUpdateNum++;
            break;
        }
        case GMC_SUB_EVENT_MERGE_INSERT:
        {
            user_data->mergeInsertNum++;
            break;
        }
        case GMC_SUB_EVENT_MERGE_UPDATE:
        {
            user_data->mergeUpdateNum++;
            break;
        }
        case GMC_SUB_EVENT_KV_SET:
        {
            user_data->kvSetNum++;
            break;
        }
        case GMC_SUB_EVENT_INITIAL_LOAD:
        {
            user_data->scanNum++;
            break;
        }
        case GMC_SUB_EVENT_INITIAL_LOAD_EOF:
        {
            user_data->scanEofNum++;
            break;
        }
        case GMC_SUB_EVENT_AGED:
        {
            user_data->agedNum++;
            break;
        }
        }
    }
}
// 在订阅推送队列满的场景下，批写流程遇到非STATUS_OK错误码时直接中断批写操作，
// 后面的操作都会被回滚。但是当前返回13044的那个写操作的数据能写入DB
// 批量写入大量数据到订阅通道满，虽然接受不到全部数据，但是数据正常写入，可以查询到异常回调中的计数
// 该用例HPE上无法构造，配置项subsChannelGlobalDynamicMemSizeMax不可在线更改且为512M
static void SubNotifyCallback(GmcStmtT *stmt, void *arg)
{
    unsigned int *callCnt = (unsigned int *)arg;
    (*callCnt)++;
}
// 020. 在订阅推送队列满的场景下, 不可靠订阅批写逻辑验证
// 【V3单号DTS2019121703233】
// 【V5单号DTS2023052616103】
TEST_F(NewSubDML_04CONF, SN_034_004_020)
{
    AW_FUN_Log(LOG_STEP, "START\n");
    char errorMsg1[1024] = {};
    char errorMsg2[1024] = {};
    char errorMsg3[1024] = {};
    char errorMsg4[1024] = {};
    char errorMsg5[1024] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD);
    (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_MEMORY_OPERATE_FAILED);
    (void)snprintf(errorMsg3, sizeof(errorMsg3), "GMERR-%d", GMERR_INTERNAL_ERROR);
    (void)snprintf(errorMsg4, sizeof(errorMsg4), "GMERR-%d", GMERR_REQUEST_TIME_OUT);
    AW_ADD_ERR_WHITE_LIST(4, errorMsg1, errorMsg2, errorMsg3, errorMsg4);
    const char *schema8k = R"(
    [{
        "type":"record",
        "name":"lable1_8k",
        "fields":[
            {"name":"F0", "type":"uint32", "nullable":false},
            {"name":"FF", "type":"uint32", "nullable":false},
            {"name":"F1", "type":"string", "nullable":true, "size":1024},
            {"name":"F2", "type":"string", "nullable":true, "size":1024},
            {"name":"F3", "type":"string", "nullable":true, "size":1024},
            {"name":"F4", "type":"string", "nullable":true, "size":1024},
            {"name":"F5", "type":"string", "nullable":true, "size":1024},
            {"name":"F6", "type":"string", "nullable":true, "size":1024},
            {"name":"F7", "type":"string", "nullable":true, "size":1024},
            {"name":"F8", "type":"string", "nullable":true, "size":1024}
        ],
        "keys":[
        {
                "node":"lable1_8k",
                "name":"PK",
                "fields":["F0"],
                "index":{"type":"primary"},
                "constraints":{"unique":true}
            }
        ]
    }
    ])";
    GmcConnT *conn = NULL, *conn_async = NULL, *conn_sub = NULL, *conn_sub2 = NULL;
    GmcStmtT *stmt = NULL, *stmt_async = NULL, *stmt_sub = NULL, *stmt_sub2 = NULL;
    int ret = 0;
    ret = testGmcConnect1(&conn_async, &stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDropVertexLabel(stmt, "lable1_8k");
    ret = GmcCreateVertexLabel(stmt, schema8k, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *newSubInfoAddMulti = R"(
    {
                "name": "newSubMulti001",
                "label_name": "lable1_8k",
                "comment":"insert",
                "type":"before_commit",
                "events":
                    [
                        {"type":"update", "msgTypes":["new object"]},
                        {"type":"insert", "msgTypes":["new object"]}
                    ],
            "is_path":false,
            "retry":true,
            "is_reliable": false
    })";
    // 可靠订阅回滚了不会继续写入，本用例使用不可靠
    ConnOptionT *connOption;
    unsigned int connNotifyCnt = 0;
    int chanRingLen = 256;
    ret = testMallocConnOptions(&connOption, NULL, NULL, NULL, 0, 0, 0, SubNotifyCallback, (void *)&connNotifyCnt);
    EXPECT_EQ(GMERR_OK, ret);
    const char *subChan = "subChan1";
    ret = testSubConnect(&conn_sub, &stmt_sub, 1, g_epoll_reg_info, subChan, &chanRingLen, connOption);
    EXPECT_EQ(GMERR_OK, ret);
    testFreeConnOptions(connOption);
    GmcSubConfigT subInfo;
    subInfo.subsName = "newSubMulti001";
    subInfo.configJson = newSubInfoAddMulti;
    SnUserDataT2 *userData = NULL;
    ret = testSnMallocUserData2(&userData, 100, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcUnSubscribe(stmt, subInfo.subsName);
    userData->blocking == true;
    ret = GmcSubscribe(stmt, &subInfo, conn_sub, sn_callback_num, userData);
    EXPECT_EQ(GMERR_OK, ret);
    blocking = true; // 阻塞回调
    int messageNum = 0;
    int tmp = 1;
    char str[1024];
    memset(str, 'a', 1023);
    str[1023] = '\0';
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;

    ret = testGmcPrepareStmtByLabelName(stmt_async, "lable1_8k", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 32768U); // 32768或者其他32M内值均有此问题
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 2100; i++)
    {
        ret = GmcSetVertexProperty(stmt_async, "F0", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "FF", GMC_DATATYPE_UINT32, &tmp, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "F1", GMC_DATATYPE_STRING, str, strlen(str));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "F2", GMC_DATATYPE_STRING, str, strlen(str));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "F3", GMC_DATATYPE_STRING, str, strlen(str));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "F4", GMC_DATATYPE_STRING, str, strlen(str));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "F5", GMC_DATATYPE_STRING, str, strlen(str));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "F6", GMC_DATATYPE_STRING, str, strlen(str));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "F7", GMC_DATATYPE_STRING, str, strlen(str));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "F8", GMC_DATATYPE_STRING, str, strlen(str));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt_async);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    if (ret != 0)
    {
        AW_FUN_Log(LOG_INFO, "\n%d\n", ret);
        EXPECT_EQ(GMERR_SUB_PUSH_QUEUE_FULL, data.status); // 使用不可靠订阅队列满也不会报错，只有异常回调推送的数据
        ret = testGmcGetLastError();
        EXPECT_EQ(GMERR_OK, ret);
    }
    blocking = false;
    int num1 = 0;
    bool checkSigns = false;
    ret = getSysViewValue((char *)"gmsysview -q V$\\STORAGE_VERTEX_COUNT -f table=lable1_8k", (char *)"record count:", &num1, &checkSigns);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_EQ(num1, 2100);
    EXPECT_EQ(checkSigns, 1);
    uint32_t timeOut = 0;
    while((userData->insertNum) < 2100 && (timeOut < 10)) {
        sleep(1);
        timeOut ++;
        AW_FUN_Log(LOG_INFO, "sub push times %d, wait times %d.", userData->insertNum, timeOut);
    }
    //  不校验具体推送的数据量，只推送的数据比写入数据少，不可靠订阅通道满情况下，丢部分推送数据
    EXPECT_LT(userData->insertNum, 2100);
    EXPECT_GT(userData->insertNum, 2000);

    for (int i = 0; i < 50; i++)
    {
        sleep(1);
        if (connNotifyCnt > 0)
        {
            break;
        }
    }
    AW_FUN_Log(LOG_INFO, "--connNotifyCnt-->%d\n", connNotifyCnt);
    EXPECT_GT(connNotifyCnt, 0);
    ret = GmcUnSubscribe(stmt, "newSubMulti001");
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(conn_sub, stmt_sub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(conn_sub2, stmt_sub2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcDropVertexLabelAsync(stmt_async, "lable1_8k", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = testGmcDisconnect(conn_async, stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "END\n");
}
// "业务在订阅推送的回调函数中对SUB_OBJ错用了set field的接口，导致在下增函数中访问了一个空指针造成core dump。
// DB则需增加对空指针的校验。"
// v5一直没有在回调中做值的设置，本用例验证设置各数据类型的一般复杂表，以及错误使用
// 预计新增2个用例
// 回调中设置各字段值
void TestGmcInsertVertexInCallBack(GmcStmtT *stmt, int index, bool bool_value, char *f14Value, int startNum,
                                   int endNum, int arrayNum, int vectorNum, const char *labelName)
{
    int32_t ret = 0;

    char *stringA = (char *)"aaaaaa";
    char *stringB = (char *)"bbbbbb";
    char *stringC = (char *)"cccccc";

    // 插入顶点   数组成员分别传 1 ，0,2
    for (int i = startNum; i < endNum; i++)
    {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_PK(root, i * index);
        TestGmcNodeSetPropertyByName_R(root, i * index, bool_value, f14Value);
        TestGmcNodeSetPropertyByName_P(t1, i * index, bool_value, f14Value);
        // 插入array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < arrayNum; j++)
        {
            if (j < arrayNum - 1)
            {
                if (j == 0)
                {
                    TestGmcNodeSetPropertyByName_A(t2, 1 * index, bool_value, stringB);
                    GmcNodeGetNextElement(t2, &t2);
                }
                else if (j == 1)
                {
                    TestGmcNodeSetPropertyByName_A(t2, 0 * index, bool_value, stringA);
                    GmcNodeGetNextElement(t2, &t2);
                }
                else
                {
                    TestGmcNodeSetPropertyByName_A(t2, 2 * index, bool_value, stringC);
                    GmcNodeGetNextElement(t2, &t2);
                }
            }
        }
        // 插入vector节点
        ret = GmcNodeAppendElement(t3, &t3);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(t3, 1 * index, bool_value, stringB);

        ret = GmcNodeAppendElement(t3, &t3);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(t3, 0 * index, bool_value, stringA);

        ret = GmcNodeAppendElement(t3, &t3);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(t3, 2 * index, bool_value, stringC);
        ret = GmcExecute(stmt);
        if (ret != 0)
        {
            EXPECT_EQ(GMERR_PRIMARY_KEY_VIOLATION, ret);
        }
        else
        {
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
}
void NewSnCallBackSetField(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int subNum = 0;
    int subPushModify = 0;
    int subInital = 0;
    int subPushDel = 0;
    int index = 0;
    GmcConnT *conn2 = NULL, *conn_sub = NULL;
    GmcStmtT *stmt2 = NULL, *stmt_sub = NULL;
    int ret = testGmcConnect(&conn2, &stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestGmcInsertVertexInCallBack(stmt2, 1, 0, (char *)"string", 10000, 10001, 1, 1, "OP_T0");
    char labelName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;
    ret = 0;
    bool eof = false;
    uint32_t getValueSizeOut;
    char readValueOut[128];
    for (int i = 0; i < info->labelCount; i++)
    {
        memset(labelName, 0, sizeof(labelName));
        labelNameLen = MAX_NAME_LENGTH;
        ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(strlen(labelName), labelNameLen);
        switch (info->eventType)
        {
        case GMC_SUB_EVENT_MODIFY:
        {
            ret = GmcFetch(subStmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (ret != GMERR_OK || eof == true)
            {
                break;
            }
            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);
            index = ((int *)user_data->new_value)[user_data->subIndex];

            if (index == 99)
            {
                checkLastValue(subStmt, 99, 'e', 'e', 99.00, (char *)"end", (char *)"end", (char *)"end");
            }
            break;
        }
        case GMC_SUB_EVENT_DELETE:
        {
            // check old value
            ret = GmcFetch(subStmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (ret != GMERR_OK || eof == true)
            {
                break;
            }
            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
            EXPECT_EQ(GMERR_OK, ret);
            index = ((int *)user_data->old_value)[user_data->subIndex];
            if (index == 99)
            {
                checkLastValue(subStmt, 99, 'e', 'e', 99.00, (char *)"end", (char *)"end", (char *)"end");
            }
            break;
        }
        case GMC_SUB_EVENT_INITIAL_LOAD:
        {
            ret = GmcFetch(subStmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (ret != GMERR_OK || eof == true)
            {
                break;
            }
            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);
            index = ((int *)user_data->new_value)[user_data->subIndex];
            if (index == 99)
            {
                checkLastValue(subStmt, 99, 'e', 'e', 99.00, (char *)"end", (char *)"end", (char *)"end");
            }
            break;
        }
        case GMC_SUB_EVENT_INITIAL_LOAD_EOF:
        {
            // TOF
            ret = GmcFetch(subStmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (ret != GMERR_OK || eof == true)
            {
                printf("\n[EOF] GMC_SUB_EVENT_INITIAL_LOAD_EOF\n");
                break;
            }
        }
        default:
        {
            printf("default: invalid eventType\r\n");
            break;
        }
        }
    }
    switch (info->eventType)
    {
    case GMC_SUB_EVENT_MODIFY:
    {
        user_data->insertNum++;
        break;
    }
    case GMC_SUB_EVENT_DELETE:
    {
        user_data->deleteNum++;
        break;
    }
    case GMC_SUB_EVENT_INITIAL_LOAD:
    {
        user_data->scanNum++;
        break;
    }
    case GMC_SUB_EVENT_INITIAL_LOAD_EOF:
    {
        user_data->scanEofNum++;
        break;
    }
    default:
    {
        printf("default: eventType\r\n");
        break;
    }
    }
}
// 021. 订阅回调中设置字段值正常设置
// 【V3单号DTS2019121406329】
TEST_F(NewSubDML_04, SN_034_004_021)
{
    int ret = 0;
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_PRIMARY_KEY_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    GmcConnT *conn = NULL, *conn_sub = NULL;
    GmcStmtT *stmt = NULL, *stmt_sub = NULL;
    int start_num = 0;
    int end_num = 1;
    int array_num = 3;
    int vector_num = 3;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SnUserDataT *userData = NULL;
    ret = testSnMallocUserData(&userData, 100, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *newSubInfoAddMulti = R"(
    {
            "name": "newSubTreeMulti001",
            "label_name": "OP_T0",
            "comment":"status_merge",
            "type":"before_commit",
            "events":
                [
                    {"type":"modify", "msgTypes":["new object", "old object"]},
                    {"type":"delete", "msgTypes":["new object", "old object"]}
                ],
            "is_path":false,
            "retry":true,
            "priority": 1,
            "subs_type":"status_merge"
    })";
    GmcDropVertexLabel(stmt, "OP_T0");
    ret = GmcCreateVertexLabel(stmt, g_treeschema, g_tableConfig2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&conn_sub, &stmt_sub, 1, g_epoll_reg_info, "newsubconn", &g_chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcSubConfigT tsub_info;
    tsub_info.subsName = "newSubTreeMulti001";
    tsub_info.configJson = newSubInfoAddMulti;
    ret = GmcSubscribe(stmt, &tsub_info, conn_sub, NewSnCallBackSetField, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, "OP_T0");
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;
    char f14_value[8] = "stoooo";
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_MODIFY, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, "newSubTreeMulti001");
    ret = GmcDropVertexLabel(stmt, "OP_T0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(conn_sub, stmt_sub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);
    AW_FUN_Log(LOG_STEP, "END\n");
}

void TestGmcNodeSetPropertyByName_PKError(GmcNodeT *node, int i)
{
    int ret = 0;
    int64_t f0_value = i;
    ret = GmcNodeSetPropertyByName(NULL, (char *)"F0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
}
void TestGmcInsertVertexInCallBackError(GmcStmtT *stmt, int index, bool bool_value, char *f14Value, int startNum,
                                        int endNum, int arrayNum, int vectorNum, const char *labelName)
{
    int32_t ret = 0;

    char *stringA = (char *)"aaaaaa";
    char *stringB = (char *)"bbbbbb";
    char *stringC = (char *)"cccccc";

    // 插入顶点   数组成员分别传 1 ，0,2
    for (int i = startNum; i < endNum; i++)
    {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_PKError(root, i * index);
        TestGmcNodeSetPropertyByName_R(root, i * index, bool_value, f14Value);
        TestGmcNodeSetPropertyByName_P(t1, i * index, bool_value, f14Value);
        // 插入array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < arrayNum; j++)
        {
            if (j < arrayNum - 1)
            {
                if (j == 0)
                {
                    TestGmcNodeSetPropertyByName_A(t2, 1 * index, bool_value, stringB);
                    GmcNodeGetNextElement(t2, &t2);
                }
                else if (j == 1)
                {
                    TestGmcNodeSetPropertyByName_A(t2, 0 * index, bool_value, stringA);
                    GmcNodeGetNextElement(t2, &t2);
                }
                else
                {
                    TestGmcNodeSetPropertyByName_A(t2, 2 * index, bool_value, stringC);
                    GmcNodeGetNextElement(t2, &t2);
                }
            }
        }

        // 插入vector节点
        ret = GmcNodeAppendElement(t3, &t3);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(t3, 1 * index, bool_value, stringB);

        ret = GmcNodeAppendElement(t3, &t3);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(t3, 0 * index, bool_value, stringA);

        ret = GmcNodeAppendElement(t3, &t3);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(t3, 2 * index, bool_value, stringC);
        ret = GmcExecute(stmt);
        if (ret != 0)
        {
            EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);
        }
        else
        {
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
}
// 回调中设置值，调用错误接口
void NewSnCallBackSetFieldError(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int subNum = 0;
    int subPushModify = 0;
    int subInital = 0;
    int subPushDel = 0;
    int index = 0;
    GmcConnT *conn2 = NULL, *conn_sub = NULL;
    GmcStmtT *stmt2 = NULL, *stmt_sub = NULL;
    int ret = testGmcConnect(&conn2, &stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestGmcInsertVertexInCallBackError(stmt2, 1, 0, (char *)"string", 10000, 10001, 1, 1, "OP_T0");
    char labelName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;
    ret = 0;
    bool eof = false;
    uint32_t getValueSizeOut;
    char readValueOut[128];
    for (int i = 0; i < info->labelCount; i++)
    {
        memset(labelName, 0, sizeof(labelName));
        labelNameLen = MAX_NAME_LENGTH;
        ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(strlen(labelName), labelNameLen);
        switch (info->eventType)
        {
        case GMC_SUB_EVENT_MODIFY:
        {
            ret = GmcFetch(subStmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (ret != GMERR_OK || eof == true)
            {
                break;
            }
            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);
            index = ((int *)user_data->new_value)[user_data->subIndex];

            if (index == 99)
            {
                checkLastValue(subStmt, 99, 'e', 'e', 99.00, (char *)"end", (char *)"end", (char *)"end");
            }
            break;
        }
        case GMC_SUB_EVENT_DELETE:
        {
            // check old value
            ret = GmcFetch(subStmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (ret != GMERR_OK || eof == true)
            {
                break;
            }
            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
            EXPECT_EQ(GMERR_OK, ret);
            index = ((int *)user_data->old_value)[user_data->subIndex];
            if (index == 99)
            {
                checkLastValue(subStmt, 99, 'e', 'e', 99.00, (char *)"end", (char *)"end", (char *)"end");
            }
            break;
        }
        case GMC_SUB_EVENT_INITIAL_LOAD:
        {
            ret = GmcFetch(subStmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (ret != GMERR_OK || eof == true)
            {
                break;
            }
            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);
            index = ((int *)user_data->new_value)[user_data->subIndex];
            if (index == 99)
            {
                checkLastValue(subStmt, 99, 'e', 'e', 99.00, (char *)"end", (char *)"end", (char *)"end");
            }
            break;
        }
        case GMC_SUB_EVENT_INITIAL_LOAD_EOF:
        {
            // TOF
            ret = GmcFetch(subStmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (ret != GMERR_OK || eof == true)
            {
                printf("\n[EOF] GMC_SUB_EVENT_INITIAL_LOAD_EOF\n");
                break;
            }
        }
        default:
        {
            printf("default: invalid eventType\r\n");
            break;
        }
        }
    }
    switch (info->eventType)
    {
    case GMC_SUB_EVENT_MODIFY:
    {
        user_data->insertNum++;
        break;
    }
    case GMC_SUB_EVENT_DELETE:
    {
        user_data->deleteNum++;
        break;
    }
    case GMC_SUB_EVENT_INITIAL_LOAD:
    {
        user_data->scanNum++;
        break;
    }
    case GMC_SUB_EVENT_INITIAL_LOAD_EOF:
    {
        user_data->scanEofNum++;
        break;
    }
    default:
    {
        printf("default: eventType\r\n");
        break;
    }
    }
}
// 022. 订阅回调中设置字段值调用错误接口
// 【V3单号DTS2019121406329】
TEST_F(NewSubDML_04, SN_034_004_022)
{
    int ret = 0;
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    GmcConnT *conn = NULL, *conn_sub = NULL;
    GmcStmtT *stmt = NULL, *stmt_sub = NULL;
    int start_num = 0;
    int end_num = 1;
    int array_num = 3;
    int vector_num = 3;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    SnUserDataT *userData = NULL;
    ret = testSnMallocUserData(&userData, 100, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *newSubInfoAddMulti = R"(
    {
            "name": "newSubTreeMulti001",
            "label_name": "OP_T0",
            "comment":"status_merge",
            "type":"before_commit",
            "events":
                [
                    {"type":"modify", "msgTypes":["new object", "old object"]},
                    {"type":"delete", "msgTypes":["new object", "old object"]}
                ],
            "is_path":false,
            "retry":true,
            "priority": 1,
            "subs_type":"status_merge"
    })";
    GmcDropVertexLabel(stmt, "OP_T0");
    ret = GmcCreateVertexLabel(stmt, g_treeschema, g_tableConfig2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&conn_sub, &stmt_sub, 1, g_epoll_reg_info, "newsubconn", &g_chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcSubConfigT tsub_info;
    tsub_info.subsName = "newSubTreeMulti001";
    tsub_info.configJson = newSubInfoAddMulti;
    ret = GmcSubscribe(stmt, &tsub_info, conn_sub, NewSnCallBackSetFieldError, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, "OP_T0");
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;
    char f14_value[8] = "stoooo";
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_MODIFY, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, "newSubTreeMulti001");
    ret = GmcDropVertexLabel(stmt, "OP_T0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(conn_sub, stmt_sub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);
    
    AW_FUN_Log(LOG_STEP, "END\n");
}

// DB测试在订阅的回调中没有构造找不到子节点的异常场景，属于异常发散场景。
// 整体梳理下接口异常场景，发散可靠性场景，补充覆盖。"
// GmcSubSetFetchMode中stmt设置为NULL
void NewSnCallBackNull(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int subNum = 0;
    int subPushModify = 0;
    int subInital = 0;
    int subPushDel = 0;
    int index = 0;
    char labelName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;
    int ret = 0;
    bool eof = false;
    uint32_t getValueSizeOut;
    char readValueOut[128];
    for (int i = 0; i < info->labelCount; i++)
    {
        memset(labelName, 0, sizeof(labelName));
        labelNameLen = MAX_NAME_LENGTH;
        ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(strlen(labelName), labelNameLen);
        switch (info->eventType)
        {
        case GMC_SUB_EVENT_MODIFY:
        {
            ret = GmcFetch(subStmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (ret != GMERR_OK || eof == true)
            {
                break;
            }
            ret = GmcSubSetFetchMode(NULL, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            index = ((int *)user_data->new_value)[user_data->subIndex];

            if (index == 99)
            {
                checkLastValue(subStmt, 99, 'e', 'e', 99.00, (char *)"end", (char *)"end", (char *)"end");
            }
            break;
        }
        case GMC_SUB_EVENT_DELETE:
        {
            // check old value
            ret = GmcFetch(subStmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (ret != GMERR_OK || eof == true)
            {
                break;
            }
            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
            EXPECT_EQ(GMERR_OK, ret);
            index = ((int *)user_data->old_value)[user_data->subIndex];
            if (index == 99)
            {
                checkLastValue(subStmt, 99, 'e', 'e', 99.00, (char *)"end", (char *)"end", (char *)"end");
            }
            break;
        }
        case GMC_SUB_EVENT_INITIAL_LOAD:
        {
            ret = GmcFetch(subStmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (ret != GMERR_OK || eof == true)
            {
                break;
            }
            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);
            index = ((int *)user_data->new_value)[user_data->subIndex];
            if (index == 99)
            {
                checkLastValue(subStmt, 99, 'e', 'e', 99.00, (char *)"end", (char *)"end", (char *)"end");
            }
            break;
        }
        case GMC_SUB_EVENT_INITIAL_LOAD_EOF:
        {
            // TOF
            ret = GmcFetch(subStmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (ret != GMERR_OK || eof == true)
            {
                printf("\n[EOF] GMC_SUB_EVENT_INITIAL_LOAD_EOF\n");
                break;
            }
        }
        default:
        {
            printf("default: invalid eventType\r\n");
            break;
        }
        }
    }
    switch (info->eventType)
    {
    case GMC_SUB_EVENT_MODIFY:
    {
        user_data->insertNum++;
        break;
    }
    case GMC_SUB_EVENT_DELETE:
    {
        user_data->deleteNum++;
        break;
    }
    case GMC_SUB_EVENT_INITIAL_LOAD:
    {
        user_data->scanNum++;
        break;
    }
    case GMC_SUB_EVENT_INITIAL_LOAD_EOF:
    {
        user_data->scanEofNum++;
        break;
    }
    default:
    {
        printf("default: eventType\r\n");
        break;
    }
    }
}
// 023. 订阅回调中fetch接口找不到节点，stmt设置为NULL
// 【V3单号DTS2019121405156】
TEST_F(NewSubDML_04, SN_034_004_023)
{
    int ret = 0;
    char errorMsg1[128] = {};
    char errorMsg2[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_DATA_EXCEPTION);
    (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);
    GmcConnT *conn = NULL, *conn_sub = NULL;
    GmcStmtT *stmt = NULL, *stmt_sub = NULL;
    int start_num = 0;
    int end_num = 1;
    int array_num = 3;
    int vector_num = 3;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    SnUserDataT *userData = NULL;
    ret = testSnMallocUserData(&userData, 100, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *newSubInfoAddMulti = R"(
    {
            "name": "newSubTreeMulti001",
            "label_name": "OP_T0",
            "comment":"status_merge",
            "type":"before_commit",
            "events":
                [
                    {"type":"modify", "msgTypes":["new object", "old object"]},
                    {"type":"delete", "msgTypes":["new object", "old object"]}
                ],
            "is_path":false,
            "retry":true,
            "priority": 1,
            "subs_type":"status_merge"
    })";
    GmcDropVertexLabel(stmt, "OP_T0");
    ret = GmcCreateVertexLabel(stmt, g_treeschema, g_tableConfig2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&conn_sub, &stmt_sub, 1, g_epoll_reg_info, "newsubconn", &g_chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcSubConfigT tsub_info;
    tsub_info.subsName = "newSubTreeMulti001";
    tsub_info.configJson = newSubInfoAddMulti;
    ret = GmcSubscribe(stmt, &tsub_info, conn_sub, NewSnCallBackNull, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, "OP_T0");
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;
    char f14_value[8] = "stoooo";
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_MODIFY, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, "newSubTreeMulti001");
    ret = GmcDropVertexLabel(stmt, "OP_T0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(conn_sub, stmt_sub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);
    
    AW_FUN_Log(LOG_STEP, "END\n");
}

void NewSnCallBackErrorStmt(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int subNum = 0;
    int subPushModify = 0;
    int subInital = 0;
    int subPushDel = 0;
    int index = 0;
    char labelName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;
    int ret = 0;
    GmcConnT *conn2 = NULL, *conn_sub = NULL;
    GmcStmtT *stmt2 = NULL, *stmt_sub = NULL;
    ret = testGmcConnect(&conn2, &stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool eof = false;
    uint32_t getValueSizeOut;
    char readValueOut[128];
    for (int i = 0; i < info->labelCount; i++)
    {
        memset(labelName, 0, sizeof(labelName));
        labelNameLen = MAX_NAME_LENGTH;
        ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(strlen(labelName), labelNameLen);
        switch (info->eventType)
        {
        case GMC_SUB_EVENT_MODIFY:
        {
            ret = GmcFetch(subStmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (ret != GMERR_OK || eof == true)
            {
                break;
            }
            ret = GmcSubSetFetchMode(stmt2, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
            index = ((int *)user_data->new_value)[user_data->subIndex];
            if (index == 99)
            {
                checkLastValue(subStmt, 99, 'e', 'e', 99.00, (char *)"end", (char *)"end", (char *)"end");
            }
            break;
        }
        case GMC_SUB_EVENT_DELETE:
        {
            // check old value
            ret = GmcFetch(subStmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (ret != GMERR_OK || eof == true)
            {
                break;
            }
            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
            EXPECT_EQ(GMERR_OK, ret);
            index = ((int *)user_data->old_value)[user_data->subIndex];
            if (index == 99)
            {
                checkLastValue(subStmt, 99, 'e', 'e', 99.00, (char *)"end", (char *)"end", (char *)"end");
            }
            break;
        }
        case GMC_SUB_EVENT_INITIAL_LOAD:
        {
            ret = GmcFetch(subStmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (ret != GMERR_OK || eof == true)
            {
                break;
            }
            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);
            index = ((int *)user_data->new_value)[user_data->subIndex];
            if (index == 99)
            {
                checkLastValue(subStmt, 99, 'e', 'e', 99.00, (char *)"end", (char *)"end", (char *)"end");
            }
            break;
        }
        case GMC_SUB_EVENT_INITIAL_LOAD_EOF:
        {
            // TOF
            ret = GmcFetch(subStmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (ret != GMERR_OK || eof == true)
            {
                printf("\n[EOF] GMC_SUB_EVENT_INITIAL_LOAD_EOF\n");
                break;
            }
        }
        default:
        {
            printf("default: invalid eventType\r\n");
            break;
        }
        }
    }
    switch (info->eventType)
    {
    case GMC_SUB_EVENT_MODIFY:
    {
        user_data->insertNum++;
        break;
    }
    case GMC_SUB_EVENT_DELETE:
    {
        user_data->deleteNum++;
        break;
    }
    case GMC_SUB_EVENT_INITIAL_LOAD:
    {
        user_data->scanNum++;
        break;
    }
    case GMC_SUB_EVENT_INITIAL_LOAD_EOF:
    {
        user_data->scanEofNum++;
        break;
    }
    default:
    {
        printf("default: eventType\r\n");
        break;
    }
    }
}
// 024. 回调中找不到子节点, stmt是类型不对的
// 【V3单号DTS2019121405156】
TEST_F(NewSubDML_04, SN_034_004_024)
{
    int ret = 0;
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    GmcConnT *conn = NULL, *conn_sub = NULL;
    GmcStmtT *stmt = NULL, *stmt_sub = NULL;
    int start_num = 0;
    int end_num = 1;
    int array_num = 3;
    int vector_num = 3;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    SnUserDataT *userData = NULL;
    ret = testSnMallocUserData(&userData, 100, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *newSubInfoAddMulti = R"(
    {
            "name": "newSubTreeMulti001",
            "label_name": "OP_T0",
            "comment":"status_merge",
            "type":"before_commit",
            "events":
                [
                    {"type":"modify", "msgTypes":["new object", "old object"]},
                    {"type":"delete", "msgTypes":["new object", "old object"]}
                ],
            "is_path":false,
            "retry":true,
            "priority": 1,
            "subs_type":"status_merge"
    })";
    GmcDropVertexLabel(stmt, "OP_T0");
    ret = GmcCreateVertexLabel(stmt, g_treeschema, g_tableConfig2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&conn_sub, &stmt_sub, 1, g_epoll_reg_info, "newsubconn", &g_chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcSubConfigT tsub_info;
    tsub_info.subsName = "newSubTreeMulti001";
    tsub_info.configJson = newSubInfoAddMulti;
    ret = GmcSubscribe(stmt, &tsub_info, conn_sub, NewSnCallBackErrorStmt, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, "OP_T0");
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;
    char f14_value[8] = "stoooo";
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_MODIFY, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, "newSubTreeMulti001");
    ret = GmcDropVertexLabel(stmt, "OP_T0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(conn_sub, stmt_sub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);
    // sdv 0722
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1009008");
    AW_FUN_Log(LOG_STEP, "END\n");
}

// 025. 批量返回正常情况下，有对total num进行比较验证，批量返回异常的话，用例退出不会再校验total num，属于异常场景漏测
// 补充异常场景用例，批量操作异常下，校验异步回调中的所有返回值
// 不可靠订阅批量写入，为什么没有notify
// 该用例HPE上无法构造，配置项subsChannelGlobalDynamicMemSizeMax不可在线更改且为512M
// 【V3单号DTS2020011610939】
TEST_F(NewSubDML_04CONF, SN_034_004_025)
{
    AW_FUN_Log(LOG_STEP, "START\n");
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_PRIMARY_KEY_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    const char *schema8k = R"(
    [{
        "type":"record",
        "name":"lable1_8k",
        "fields":[
            {"name":"F0", "type":"uint32", "nullable":false},
            {"name":"FF", "type":"uint32", "nullable":false},
            {"name":"F1", "type":"string", "nullable":true, "size":1024},
            {"name":"F2", "type":"string", "nullable":true, "size":1024},
            {"name":"F3", "type":"string", "nullable":true, "size":1024},
            {"name":"F4", "type":"string", "nullable":true, "size":1024},
            {"name":"F5", "type":"string", "nullable":true, "size":1024},
            {"name":"F6", "type":"string", "nullable":true, "size":1024},
            {"name":"F7", "type":"string", "nullable":true, "size":1024},
            {"name":"F8", "type":"string", "nullable":true, "size":1024}
        ],
        "keys":[
        {
                "node":"lable1_8k",
                "name":"PK",
                "fields":["F0"],
                "index":{"type":"primary"},
                "constraints":{"unique":true}
            }
        ]
    }
    ])";
    GmcConnT *conn = NULL, *conn_async = NULL, *conn_sub = NULL, *conn_sub2 = NULL;
    GmcStmtT *stmt = NULL, *stmt_async = NULL, *stmt_sub = NULL, *stmt_sub2 = NULL;
    int ret = 0;
    ret = testGmcConnect(&conn_async, &stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDropVertexLabel(stmt, "lable1_8k");
    ret = GmcCreateVertexLabel(stmt, schema8k, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *newSubInfoAddMulti = R"(
    {
                "name": "newSubMulti001",
                "label_name": "lable1_8k",
                "comment":"insert",
                "type":"before_commit",
                "events":
                    [
                        {"type":"update", "msgTypes":["new object"]},
                        {"type":"insert", "msgTypes":["new object"]}
                    ],
            "is_path":false,
            "retry":true,
            "is_reliable": false
    })";
    // 可靠订阅回滚了不会继续写入，本用例使用不可靠
    ConnOptionT *connOption;
    unsigned int connNotifyCnt = 0;
    int chanRingLen = 256;
    ret = testMallocConnOptions(&connOption, NULL, NULL, NULL, 0, 0, 0, SubNotifyCallback, (void *)&connNotifyCnt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subChan = "subChan1";
    ret = testSubConnect(&conn_sub, &stmt_sub, 1, g_epoll_reg_info, subChan, &chanRingLen, connOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubConfigT subInfo;
    subInfo.subsName = "newSubMulti001";
    subInfo.configJson = newSubInfoAddMulti;
    SnUserDataT2 *userData = NULL;
    ret = testSnMallocUserData2(&userData, 100, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcUnSubscribe(stmt, subInfo.subsName);
    userData->blocking == true;
    ret = GmcSubscribe(stmt, &subInfo, conn_sub, OldSnCallBack, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    blocking = true; // 阻塞回调
    int messageNum = 0;
    int tmp = 1;
    char str[1024];
    memset(str, 'a', 1023);
    str[1023] = '\0';
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;

    ret = testGmcPrepareStmtByLabelName(stmt_async, "lable1_8k", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 100; i++)
    {
        if (i == 50)
        {
            ret = GmcSetVertexProperty(stmt_async, "F0", GMC_DATATYPE_STRING, &i, sizeof(uint32_t));
            EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
        }
        else
        {
            ret = GmcSetVertexProperty(stmt_async, "F0", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcSetVertexProperty(stmt_async, "FF", GMC_DATATYPE_UINT32, &tmp, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "F1", GMC_DATATYPE_STRING, str, strlen(str));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    if (data.status != 0)
    {
        EXPECT_EQ(GMERR_PRIMARY_KEY_VIOLATION, data.status);
        ret = testGmcGetLastError();
        EXPECT_EQ(GMERR_OK, ret);
    }
    else
    {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    }
    AW_MACRO_EXPECT_EQ_INT(50, data.succNum);
    AW_MACRO_EXPECT_EQ_INT(100, data.totalNum);
    blocking = false;
    int num1 = 0;
    bool checkSigns = false;
    ret = getSysViewValue((char *)"gmsysview -q V$\\STORAGE_VERTEX_COUNT -f table=lable1_8k", (char *)"record count:", &num1, &checkSigns);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(num1, 50);
    AW_MACRO_EXPECT_EQ_INT(checkSigns, 1);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INSERT, 50, 12600);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, "newSubMulti001");
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(conn_sub, stmt_sub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(conn_sub2, stmt_sub2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcDropVertexLabelAsync(stmt_async, "lable1_8k", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = testGmcDisconnect(conn_async, stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "END\n");
}
// 026. 批量返回正常情况下，有对total num进行比较验证，批量返回异常的话，用例退出不会再校验total num，属于异常场景漏测
// 补充异常场景用例，批量操作异常下，校验异步回调中的所有返回值
// 可靠订阅批量写入
// 该用例HPE上无法构造，配置项subsChannelGlobalDynamicMemSizeMax不可在线更改且为512M
// 【V3单号DTS2020011610939】
TEST_F(NewSubDML_04CONF, SN_034_004_026)
{
    AW_FUN_Log(LOG_STEP, "START\n");
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_PRIMARY_KEY_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    const char *schema8k = R"(
    [{
        "type":"record",
        "name":"lable1_8k",
        "fields":[
            {"name":"F0", "type":"uint32", "nullable":false},
            {"name":"FF", "type":"uint32", "nullable":false},
            {"name":"F1", "type":"string", "nullable":true, "size":1024},
            {"name":"F2", "type":"string", "nullable":true, "size":1024},
            {"name":"F3", "type":"string", "nullable":true, "size":1024},
            {"name":"F4", "type":"string", "nullable":true, "size":1024},
            {"name":"F5", "type":"string", "nullable":true, "size":1024},
            {"name":"F6", "type":"string", "nullable":true, "size":1024},
            {"name":"F7", "type":"string", "nullable":true, "size":1024},
            {"name":"F8", "type":"string", "nullable":true, "size":1024}
        ],
        "keys":[
        {
                "node":"lable1_8k",
                "name":"PK",
                "fields":["F0"],
                "index":{"type":"primary"},
                "constraints":{"unique":true}
            }
        ]
    }
    ])";
    GmcConnT *conn = NULL, *conn_async = NULL, *conn_sub = NULL, *conn_sub2 = NULL;
    GmcStmtT *stmt = NULL, *stmt_async = NULL, *stmt_sub = NULL, *stmt_sub2 = NULL;
    int ret = 0;
    ret = testGmcConnect(&conn_async, &stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDropVertexLabel(stmt, "lable1_8k");
    ret = GmcCreateVertexLabel(stmt, schema8k, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *newSubInfoAddMulti = R"(
    {
                "name": "newSubMulti001",
                "label_name": "lable1_8k",
                "comment":"insert",
                "type":"before_commit",
                "events":
                    [
                        {"type":"update", "msgTypes":["new object"]},
                        {"type":"insert", "msgTypes":["new object"]}
                    ],
            "is_path":false,
            "retry":true,
            "is_reliable": true
    })";
    ConnOptionT *connOption;
    unsigned int connNotifyCnt = 0;
    int chanRingLen = 256;
    ret = testMallocConnOptions(&connOption, NULL, NULL, NULL, 0, 0, 0, SubNotifyCallback, (void *)&connNotifyCnt);
    EXPECT_EQ(GMERR_OK, ret);
    const char *subChan = "subChan1";
    ret = testSubConnect(&conn_sub, &stmt_sub, 1, g_epoll_reg_info, subChan, &chanRingLen, connOption);
    EXPECT_EQ(GMERR_OK, ret);
    GmcSubConfigT subInfo;
    subInfo.subsName = "newSubMulti001";
    subInfo.configJson = newSubInfoAddMulti;
    SnUserDataT2 *userData = NULL;
    ret = testSnMallocUserData2(&userData, 100, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcUnSubscribe(stmt, subInfo.subsName);
    userData->blocking == true;
    ret = GmcSubscribe(stmt, &subInfo, conn_sub, OldSnCallBack, userData);
    EXPECT_EQ(GMERR_OK, ret);
    blocking = true; // 阻塞回调
    int messageNum = 0;
    int tmp = 1;
    char str[1024];
    memset(str, 'a', 1023);
    str[1023] = '\0';
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;

    ret = testGmcPrepareStmtByLabelName(stmt_async, "lable1_8k", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 100; i++)
    {
        if (i == 50)
        {
            ret = GmcSetVertexProperty(stmt_async, "F0", GMC_DATATYPE_STRING, &i, sizeof(uint32_t));
            EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
        }
        else
        {
            ret = GmcSetVertexProperty(stmt_async, "F0", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcSetVertexProperty(stmt_async, "FF", GMC_DATATYPE_UINT32, &tmp, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "F1", GMC_DATATYPE_STRING, str, strlen(str));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt_async);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    if (data.status != 0)
    {
        EXPECT_EQ(GMERR_PRIMARY_KEY_VIOLATION, data.status);
        ret = testGmcGetLastError();
        EXPECT_EQ(GMERR_OK, ret);
    }
    else
    {
        EXPECT_EQ(GMERR_OK, data.status);
    }
    EXPECT_EQ(50, data.succNum);
    EXPECT_EQ(100, data.totalNum);
    blocking = false;
    int num1 = 0;
    bool checkSigns = false;
    ret = getSysViewValue((char *)"gmsysview -q V$\\STORAGE_VERTEX_COUNT -f table=lable1_8k", (char *)"record count:", &num1, &checkSigns);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_EQ(num1, 50);
    EXPECT_EQ(checkSigns, 1);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INSERT, 50, 12600);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, "newSubMulti001");
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(conn_sub, stmt_sub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(conn_sub2, stmt_sub2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcDropVertexLabelAsync(stmt_async, "lable1_8k", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = testGmcDisconnect(conn_async, stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "END\n");
}

// 027. 多个订阅关系订阅同一张表，且有的指定推key，有的指定推obj，然后不停的update表
// 【V3单号DTS2020011511689】
TEST_F(NewSubDML_04, SN_034_004_027)
{
    AW_FUN_Log(LOG_STEP, "START\n");
    int ret;
    int i;
    int userDataIdx = 0;
    char *g_sub_info = NULL;
    char *g_sub_info2 = NULL;
    char *g_sub_info3 = NULL;
    SnUserDataT *user_data1;
    GmcConnT *g_conn_sync = NULL;
    GmcStmtT *g_stmt_sync = NULL, *g_stmt_sync_2 = NULL, *g_stmt_sync_3 = NULL;
    GmcConnT *g_conn_sub = NULL;
    GmcStmtT *g_stmt_sub = NULL;
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *g_subConnName = "subConnName";
    uint32_t g_data_num = 100;
    char g_label_name[] = "T20_all_type";
    char g_label_config[] = "{\"max_record_count\":30000, \"isFastReadUncommitted\":1}";
    readJanssonFile("schema_file/all_type_schema_001.gmjson", &g_schema);
    EXPECT_NE((void *)NULL, g_schema);
    GmcDropVertexLabel(g_stmt_sync, g_label_name);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_schema);

    int chanRingLen = 256;
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testSnMallocUserData(&user_data1, g_data_num * 10);
    EXPECT_EQ(GMERR_OK, ret);

    SnUserDataT *user_data2;
    ret = testSnMallocUserData(&user_data2, g_data_num * 10);
    EXPECT_EQ(GMERR_OK, ret);

    SnUserDataT *user_data3;
    ret = testSnMallocUserData(&user_data3, g_data_num * 10);
    EXPECT_EQ(GMERR_OK, ret);

    const char *g_subName = "subVertexLabel";
    const char *g_subName2 = "subVertexLabel2";
    const char *g_subName3 = "subVertexLabel3";
    readJanssonFile("schema_file/all_type_schema_subinfo_006_003.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);
    readJanssonFile("schema_file/all_type_schema_subinfo_006_006.gmjson", &g_sub_info2);
    EXPECT_NE((void *)NULL, g_sub_info);
    readJanssonFile("schema_file/all_type_schema_subinfo_006_007.gmjson", &g_sub_info3);
    EXPECT_NE((void *)NULL, g_sub_info);
    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback_key_object, user_data1);
    EXPECT_EQ(GMERR_OK, ret);
    GmcSubConfigT tmp_g_sub_info2;
    tmp_g_sub_info2.subsName = g_subName2;
    tmp_g_sub_info2.configJson = g_sub_info2;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info2, g_conn_sub, sn_callback_key_object, user_data2);
    EXPECT_EQ(GMERR_OK, ret);
    GmcSubConfigT tmp_g_sub_info3;
    tmp_g_sub_info3.subsName = g_subName3;
    tmp_g_sub_info3.configJson = g_sub_info3;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info3, g_conn_sub, sn_callback_key_object, user_data3);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_sub_info);
    free(g_sub_info2);
    free(g_sub_info3);
    // //写数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    int affectRows;
    SnUserDataT *user_datas[3] = {user_data1, user_data2, user_data3};
    for (i = 0; i < g_data_num; i++)
    {
        ((int *)(user_datas[0]->new_value))[userDataIdx] = i;
        ((int *)(user_datas[1]->new_value))[userDataIdx] = i;
        ((int *)(user_datas[2]->new_value))[userDataIdx] = i;
        userDataIdx++;
        test_setVertexPK(g_stmt_sync, i);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //等待insert事件推送完成
    ret = testWaitSnRecv(user_data1, GMC_SUB_EVENT_INSERT, g_data_num, RECV_TIMEOUT * 2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_INSERT, g_data_num, RECV_TIMEOUT * 2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_INSERT, g_data_num, RECV_TIMEOUT * 2);
    EXPECT_EQ(GMERR_OK, ret);
    //更新数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);

    char g_label_PK[] = "T20_PK";
    for (i = 0; i < g_data_num; i++)
    {
        ((int *)(user_datas[0]->old_value))[userDataIdx] = i;
        ((int *)(user_datas[0]->new_value))[userDataIdx] = i + g_data_num;
        ((int *)(user_datas[1]->old_value))[userDataIdx] = i;
        ((int *)(user_datas[1]->new_value))[userDataIdx] = i + g_data_num;
        ((int *)(user_datas[2]->old_value))[userDataIdx] = i;
        ((int *)(user_datas[2]->new_value))[userDataIdx] = i + g_data_num;
        userDataIdx++;

        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        test_setVertexProperty(g_stmt_sync, i + g_data_num);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_label_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
        usleep(500);
    }
    //等待update事件推送完成
    ret = testWaitSnRecv(user_data1, GMC_SUB_EVENT_UPDATE, g_data_num, RECV_TIMEOUT * 2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_UPDATE, g_data_num, RECV_TIMEOUT * 2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_UPDATE, g_data_num, RECV_TIMEOUT * 2);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt_sync, g_subName2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt_sync, g_subName3);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);
    system("gmddl -c drop -t T20_all_type");
    testSnFreeUserData(user_data1);
    testSnFreeUserData(user_data2);
    testSnFreeUserData(user_data3);
    AW_FUN_Log(LOG_STEP, "END\n");
}

// 028. 服务端处理一条长消息超时，长消息发送30s 后，客户端发送正常消息给服务端，预期服务端针对长消息返回错误码，后续消息正常处理
// 和伟站确认黑盒无法构造
// 该用例HPE上无法构造，配置项subsChannelGlobalDynamicMemSizeMax不可在线更改且为512M
// 【V3单号DTS2020062602286】
TEST_F(NewSubDML_04CONF, SN_034_004_028)
{
    AW_FUN_Log(LOG_STEP, "START\n");
    char errorMsg1[1024] = {};
    char errorMsg2[1024] = {};
    char errorMsg3[1024] = {};
    char errorMsg4[1024] = {};
    char errorMsg5[1024] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD);
    (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_MEMORY_OPERATE_FAILED);
    (void)snprintf(errorMsg3, sizeof(errorMsg3), "GMERR-%d", GMERR_INTERNAL_ERROR);
    (void)snprintf(errorMsg4, sizeof(errorMsg4), "GMERR-%d", GMERR_REQUEST_TIME_OUT);
    (void)snprintf(errorMsg5, sizeof(errorMsg5), "GMERR-%d", GMERR_SUB_PUSH_QUEUE_FULL);
    AW_ADD_ERR_WHITE_LIST(5, errorMsg1, errorMsg2, errorMsg3, errorMsg4, errorMsg5);
    // runtime的消息有控制消息和业务消息，只有控制消息失败的分支会有16002，所以偶现概率比较低 0822欧拉构建
    AW_ADD_ERR_WHITE_LIST(2, "GMERR-1016001", "GMERR-1016002");
    const char *schema8k = R"(
    [{
        "type":"record",
        "name":"lable1_8k",
        "fields":[
            {"name":"F0", "type":"uint32", "nullable":false},
            {"name":"FF", "type":"uint32", "nullable":false},
            {"name":"F1", "type":"string", "nullable":true, "size":1024},
            {"name":"F2", "type":"string", "nullable":true, "size":1024},
            {"name":"F3", "type":"string", "nullable":true, "size":1024},
            {"name":"F4", "type":"string", "nullable":true, "size":1024},
            {"name":"F5", "type":"string", "nullable":true, "size":1024},
            {"name":"F6", "type":"string", "nullable":true, "size":1024},
            {"name":"F7", "type":"string", "nullable":true, "size":1024},
            {"name":"F8", "type":"string", "nullable":true, "size":1024}
        ],
        "keys":[
        {
                "node":"lable1_8k",
                "name":"PK",
                "fields":["F0"],
                "index":{"type":"primary"},
                "constraints":{"unique":true}
            }
        ]
    }
    ])";
    GmcConnT *conn = NULL, *conn_async = NULL, *conn_sub = NULL, *conn_sub2 = NULL;
    GmcStmtT *stmt = NULL, *stmt_async = NULL, *stmt_sub = NULL, *stmt_sub2 = NULL;
    int ret = 0;
    ret = testGmcConnect1(&conn_async, &stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDropVertexLabel(stmt, "lable1_8k");
    ret = GmcCreateVertexLabel(stmt, schema8k, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *newSubInfoAddMulti = R"(
    {
                "name": "newSubMulti001",
                "label_name": "lable1_8k",
                "comment":"insert",
                "type":"before_commit",
                "events":
                    [
                        {"type":"update", "msgTypes":["new object", "old object"]},
                        {"type":"insert", "msgTypes":["new object"]}
                    ],
            "is_path":false,
            "retry":true,
            "is_reliable": true
    })";
    // 可靠订阅回滚了不会继续写入，本用例使用不可靠
    ConnOptionT *connOption;
    unsigned int connNotifyCnt = 0;
    int chanRingLen = 256;
    ret = testMallocConnOptions(&connOption, NULL, NULL, NULL, 0, 0, 0, SubNotifyCallback, (void *)&connNotifyCnt);
    EXPECT_EQ(GMERR_OK, ret);
    const char *subChan = "subChan1";
    ret = testSubConnect(&conn_sub, &stmt_sub, 1, g_epoll_reg_info, subChan, &chanRingLen, connOption);
    EXPECT_EQ(GMERR_OK, ret);
    GmcSubConfigT subInfo;
    subInfo.subsName = "newSubMulti001";
    subInfo.configJson = newSubInfoAddMulti;
    SnUserDataT2 *userData = NULL;
    ret = testSnMallocUserData2(&userData, 100, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcUnSubscribe(stmt, subInfo.subsName);
    userData->blocking == true;
    ret = GmcSubscribe(stmt, &subInfo, conn_sub, sn_callback_num, userData);
    EXPECT_EQ(GMERR_OK, ret);
    blocking = true; // 阻塞回调
    int messageNum = 0;
    int tmp = 1;
    char str[1024];
    memset(str, 'a', 1023);
    str[1023] = '\0';
    char str2[1024];
    memset(str2, 'b', 1023);
    str2[1023] = '\0';
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;

    ret = testGmcPrepareStmtByLabelName(stmt_async, "lable1_8k", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 32768U); // 32768或者其他32M内值均有此问题
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 2100; i++)
    {
        ret = GmcSetVertexProperty(stmt_async, "F0", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "FF", GMC_DATATYPE_UINT32, &tmp, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "F1", GMC_DATATYPE_STRING, str, strlen(str));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "F2", GMC_DATATYPE_STRING, str, strlen(str));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "F3", GMC_DATATYPE_STRING, str, strlen(str));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "F4", GMC_DATATYPE_STRING, str, strlen(str));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "F5", GMC_DATATYPE_STRING, str, strlen(str));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "F6", GMC_DATATYPE_STRING, str, strlen(str));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "F7", GMC_DATATYPE_STRING, str, strlen(str));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "F8", GMC_DATATYPE_STRING, str, strlen(str));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt_async);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_REQUEST_TIME_OUT, data.status);

   int num1 = 0;
    bool checkSigns = false;
    ret = getSysViewValue((char *)"gmsysview -q V$\\STORAGE_VERTEX_COUNT -f table=lable1_8k", (char *)"record count:", &num1, &checkSigns);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_LT(num1, 2100);
    EXPECT_GT(num1, 2000);// 调整经验下限值
    EXPECT_EQ(checkSigns, 1);

    sleep(30);
    // 没得到响应超时，休眠30s后继续向服务端DML
    // 回调卡死，使用同一把锁，断连就卡死了。DTS2023030401844卡死在4906行，
    // 假如连接断开成功，后续内容应该成功
    blocking = false;
    ret = GmcUnSubscribe(stmt, "newSubMulti001");
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(conn_sub, stmt_sub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData2(userData);

    const char *subChan2 = "subChan2";
    ret = testSubConnect(&conn_sub2, &stmt_sub2, 1, g_epoll_reg_info, subChan2, &chanRingLen, connOption);
    EXPECT_EQ(GMERR_OK, ret);
    GmcSubConfigT subInfo2;
    subInfo2.subsName = "newSubMulti001";
    subInfo2.configJson = newSubInfoAddMulti;
    SnUserDataT *userData2 = NULL;
    data = {0};
    ret = testSnMallocUserData(&userData2, 100, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcUnSubscribe(stmt, subInfo2.subsName);
    ret = GmcSubscribe(stmt, &subInfo2, conn_sub2, OldSnCallBack, userData2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt_async, "lable1_8k", GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U); // 32768或者其他32M内值均有此问题
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 10; i++)
    {
        ret = GmcSetIndexKeyName(stmt_async, "PK");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t tmpValue = i;
        ret = GmcSetIndexKeyValue(stmt_async, 0, GMC_DATATYPE_UINT32, &tmpValue, sizeof(tmpValue));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "FF", GMC_DATATYPE_UINT32, &tmp, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "F1", GMC_DATATYPE_STRING, str, strlen(str));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "F2", GMC_DATATYPE_STRING, str, strlen(str));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "F3", GMC_DATATYPE_STRING, str, strlen(str));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "F4", GMC_DATATYPE_STRING, str, strlen(str));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "F5", GMC_DATATYPE_STRING, str, strlen(str));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "F6", GMC_DATATYPE_STRING, str, strlen(str));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "F7", GMC_DATATYPE_STRING, str, strlen(str));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "F8", GMC_DATATYPE_STRING, str, strlen(str));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt_async);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, data.status);
    ret = testWaitSnRecv(userData2, GMC_SUB_EVENT_UPDATE, 10, 12600);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, "newSubMulti001");
    EXPECT_EQ(GMERR_OK, ret);
    
    ret = testSubDisConnect(conn_sub2, stmt_sub2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcDropVertexLabelAsync(stmt_async, "lable1_8k", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = testGmcDisconnect(conn_async, stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "END\n");
}

// 029. 内存将近满时，定长表的大对象更新，预期更新成功
// 【V3单号DTS2020051911149】
TEST_F(NewSubDML_04, SN_034_004_029)
{
    AW_FUN_Log(LOG_STEP, "start\n");
    
    GmcConnT *conn = NULL, *conn_async = NULL, *conn_sub = NULL, *conn_sub2 = NULL;
    GmcStmtT *stmt = NULL, *stmt_async = NULL, *stmt_sub = NULL, *stmt_sub2 = NULL;
    int ret = 0;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *g_schema = NULL;
    readJanssonFile("schema_file/BigObject.gmjson", &g_schema);
    EXPECT_NE((void *)NULL, g_schema);
    GmcDropVertexLabel(stmt, "TypicalVertexLabel");
    ret = GmcCreateVertexLabel(stmt, g_schema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_schema);
    ret = testGmcPrepareStmtByLabelName(stmt, "TypicalVertexLabel", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t a0 = 0;
    int32_t coefficient = 0; //8098
    for (int i = 0; i < 8000; i++)
    {
        ret = GmcSetVertexProperty(stmt, "A0", GMC_DATATYPE_INT32, &i, sizeof(i));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int64_t eleNum = i;
        uint8_t eleBit = i % 255;
        uint8_t eleStr = i % 26 + 'a';
        uint8_t eleFixed = i % 26 + 'A';
        uint8_t eleBytes = i % 26 + 'A';

        int64_t a1 = eleNum;
        int ret = GmcSetVertexProperty(stmt, "A1", GMC_DATATYPE_INT64, &a1, sizeof(a1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t a2 = eleNum;
        ret = GmcSetVertexProperty(stmt, "A2", GMC_DATATYPE_UINT32, &a2, sizeof(a2));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint64_t a3 = eleNum;
        ret = GmcSetVertexProperty(stmt, "A3", GMC_DATATYPE_UINT64, &a3, sizeof(a3));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        float a4 = eleNum;
        ret = GmcSetVertexProperty(stmt, "A4", GMC_DATATYPE_FLOAT, &a4, sizeof(a4));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        double a5 = eleNum;
        ret = GmcSetVertexProperty(stmt, "A5", GMC_DATATYPE_DOUBLE, &a5, sizeof(a5));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint8_t bits[16 / 8] = {0};
        memset(bits, eleBit, sizeof(bits));
        GmcBitMapT a6 = {0};
        a6.beginPos = 0;
        a6.endPos = 16 - 1;
        a6.bits = bits;
        ret = GmcSetVertexProperty(stmt, "A6", GMC_DATATYPE_BITMAP, &a6, sizeof(a6));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint8_t a7[16];
        memset(a7, eleFixed, sizeof(a7));
        ret = GmcSetVertexProperty(stmt, "A7", GMC_DATATYPE_FIXED, &a7, sizeof(a7));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint8_t a8[65500];
        memset(a8, eleBytes, sizeof(a8));
        ret = GmcSetVertexProperty(stmt, "A8", GMC_DATATYPE_BYTES, a8, sizeof(a8));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint8_t a9[65500];
        memset(a9, eleStr, sizeof(a9) - 1);
        // ubsan
        a9[65499] = '\0';
        ret = GmcSetVertexProperty(stmt, "A9", GMC_DATATYPE_STRING, a9, strlen((char *)a9));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    system("gmsysview count TypicalVertexLabel");
    system("gmsysview -q V$\\CATA_TABLESPACE_INFO");
    ret = testGmcPrepareStmtByLabelName(stmt, "TypicalVertexLabel", GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 20; i++)
    {
        ret = GmcSetIndexKeyName(stmt, "PrimaryKey");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int32_t tmpValue = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &tmpValue, sizeof(tmpValue));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int64_t eleNum = i + 10000;
        uint8_t eleBit = i % 255;
        uint8_t eleStr = i % 26 + 'a';
        uint8_t eleFixed = i % 26 + 'A';
        uint8_t eleBytes = i % 26 + 'A';

        int64_t a1 = eleNum;
        int ret = GmcSetVertexProperty(stmt, "A1", GMC_DATATYPE_INT64, &a1, sizeof(a1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t a2 = eleNum;
        ret = GmcSetVertexProperty(stmt, "A2", GMC_DATATYPE_UINT32, &a2, sizeof(a2));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint64_t a3 = eleNum;
        ret = GmcSetVertexProperty(stmt, "A3", GMC_DATATYPE_UINT64, &a3, sizeof(a3));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        float a4 = eleNum;
        ret = GmcSetVertexProperty(stmt, "A4", GMC_DATATYPE_FLOAT, &a4, sizeof(a4));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        double a5 = eleNum;
        ret = GmcSetVertexProperty(stmt, "A5", GMC_DATATYPE_DOUBLE, &a5, sizeof(a5));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint8_t bits[16 / 8] = {0};
        memset(bits, eleBit, sizeof(bits));
        GmcBitMapT a6 = {0};
        a6.beginPos = 0;
        a6.endPos = 16 - 1;
        a6.bits = bits;
        ret = GmcSetVertexProperty(stmt, "A6", GMC_DATATYPE_BITMAP, &a6, sizeof(a6));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint8_t a7[16];
        memset(a7, eleFixed, sizeof(a7));
        ret = GmcSetVertexProperty(stmt, "A7", GMC_DATATYPE_FIXED, &a7, sizeof(a7));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint8_t a8[65500];
        memset(a8, eleBytes, sizeof(a8));
        ret = GmcSetVertexProperty(stmt, "A8", GMC_DATATYPE_BYTES, a8, sizeof(a8));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint8_t a9[65500];
        memset(a9, eleStr, sizeof(a9) - 1);
        // ubsan
        a9[65499] = '\0';
        ret = GmcSetVertexProperty(stmt, "A9", GMC_DATATYPE_STRING, a9, strlen((char *)a9));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(stmt, "TypicalVertexLabel");
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    AW_FUN_Log(LOG_STEP, "END\n");
}

// 030. sub_info -a失败次数查找，视图对比，V5对应视图为CATA_LABEL_SUBS_INFO和QRY_STMG_SUBS_CURSOR_INFO
// 观察CATA_LABEL_SUBS_INFO中的失败次数需求是旧订阅，【RETRY当前已经失去效用】
// DRT_CONN_SUBS_STAT视图检测成功失败次数最好使用可靠订阅，因为会回滚
// 该用例HPE上无法构造，配置项subsChannelGlobalDynamicMemSizeMax不可在线更改且为512M
// 【V3单号DTS2020051408210】
TEST_F(NewSubDML_04CONF, SN_034_004_030)
{
    char errorMsg1[1024] = {};
    char errorMsg2[1024] = {};
    char errorMsg3[1024] = {};
    char errorMsg4[1024] = {};
    char errorMsg5[1024] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD);
    (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_MEMORY_OPERATE_FAILED);
    (void)snprintf(errorMsg3, sizeof(errorMsg3), "GMERR-%d", GMERR_INTERNAL_ERROR);
    (void)snprintf(errorMsg4, sizeof(errorMsg4), "GMERR-%d", GMERR_REQUEST_TIME_OUT);
    (void)snprintf(errorMsg5, sizeof(errorMsg5), "GMERR-%d", GMERR_SUB_PUSH_QUEUE_FULL);
    AW_ADD_ERR_WHITE_LIST(5, errorMsg1, errorMsg2, errorMsg3, errorMsg4, errorMsg5);
    AW_FUN_Log(LOG_STEP, "START\n");
    const char *schema8k = R"(
    [{
        "type":"record",
        "name":"lable1_8k",
        "fields":[
            {"name":"F0", "type":"uint32", "nullable":false},
            {"name":"FF", "type":"uint32", "nullable":false},
            {"name":"F1", "type":"string", "nullable":true, "size":1024},
            {"name":"F2", "type":"string", "nullable":true, "size":1024},
            {"name":"F3", "type":"string", "nullable":true, "size":1024},
            {"name":"F4", "type":"string", "nullable":true, "size":1024},
            {"name":"F5", "type":"string", "nullable":true, "size":1024},
            {"name":"F6", "type":"string", "nullable":true, "size":1024},
            {"name":"F7", "type":"string", "nullable":true, "size":1024},
            {"name":"F8", "type":"string", "nullable":true, "size":1024}
        ],
        "keys":[
        {
                "node":"lable1_8k",
                "name":"PK",
                "fields":["F0"],
                "index":{"type":"primary"},
                "constraints":{"unique":true}
            }
        ]
    }
    ])";
    GmcConnT *conn = NULL, *conn_async = NULL, *conn_sub = NULL, *conn_sub2 = NULL;
    GmcStmtT *stmt = NULL, *stmt_async = NULL, *stmt_sub = NULL, *stmt_sub2 = NULL;
    int ret = 0;
    ret = testGmcConnect1(&conn_async, &stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDropVertexLabel(stmt, "lable1_8k");
    ret = GmcCreateVertexLabel(stmt, schema8k, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *newSubInfoAddMulti = R"(
    {
                "name": "newSubMulti001",
                "label_name": "lable1_8k",
                "comment":"insert",
                "type":"before_commit",
                "events":
                    [
                        {"type":"update", "msgTypes":["new object"]},
                        {"type":"insert", "msgTypes":["new object"]}
                    ],
            "is_path":false,
            "retry":true,
            "is_reliable": true
    })";
    // 可靠订阅回滚了不会继续写入，本用例使用不可靠
    ConnOptionT *connOption;
    unsigned int connNotifyCnt = 0;
    int chanRingLen = 256;
    ret = testMallocConnOptions(&connOption, NULL, NULL, NULL, 0, 0, 0, SubNotifyCallback, (void *)&connNotifyCnt);
    EXPECT_EQ(GMERR_OK, ret);
    const char *subChan = "subChan1";
    ret = testSubConnect(&conn_sub, &stmt_sub, 1, g_epoll_reg_info, subChan, &chanRingLen, connOption);
    EXPECT_EQ(GMERR_OK, ret);
    testFreeConnOptions(connOption);
    GmcSubConfigT subInfo;
    subInfo.subsName = "newSubMulti001";
    subInfo.configJson = newSubInfoAddMulti;
    SnUserDataT2 *userData = NULL;
    ret = testSnMallocUserData2(&userData, 100, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcUnSubscribe(stmt, subInfo.subsName);
    userData->blocking == true;
    ret = GmcSubscribe(stmt, &subInfo, conn_sub, sn_callback_num, userData);
    EXPECT_EQ(GMERR_OK, ret);
    blocking = true; // 阻塞回调
    int messageNum = 0;
    int tmp = 1;
    char str[1024];
    memset(str, 'a', 1023);
    str[1023] = '\0';
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;

    ret = testGmcPrepareStmtByLabelName(stmt_async, "lable1_8k", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 32768U); // 32768或者其他32M内值均有此问题
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 2100; i++)
    {
        ret = GmcSetVertexProperty(stmt_async, "F0", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "FF", GMC_DATATYPE_UINT32, &tmp, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "F1", GMC_DATATYPE_STRING, str, strlen(str));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "F2", GMC_DATATYPE_STRING, str, strlen(str));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "F3", GMC_DATATYPE_STRING, str, strlen(str));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "F4", GMC_DATATYPE_STRING, str, strlen(str));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "F5", GMC_DATATYPE_STRING, str, strlen(str));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "F6", GMC_DATATYPE_STRING, str, strlen(str));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "F7", GMC_DATATYPE_STRING, str, strlen(str));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "F8", GMC_DATATYPE_STRING, str, strlen(str));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt_async);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_REQUEST_TIME_OUT, data.status);
    if (ret != 0)
    {
        AW_FUN_Log(LOG_INFO, "\n%d\n", ret);
        EXPECT_EQ(GMERR_SUB_PUSH_QUEUE_FULL, data.status); // 使用不可靠订阅队列满也不会报错，只有异常回调推送的数据
        ret = testGmcGetLastError();
        EXPECT_EQ(GMERR_OK, ret);
    }
    blocking = false;
    system("gmsysview -q V\\$DRT_CONN_SUBS_STAT -f NODE_NAME=subChan1 | grep SUB_TRY_CNT | awk 'NR==1'");
    int subTryCnt =
        GetViewValueByField("DRT_CONN_SUBS_STAT -f NODE_NAME=subChan1 | grep SUB_TRY_CNT | awk 'NR==1'", "SUB_TRY_CNT");
    EXPECT_LT(subTryCnt, 2100);
    EXPECT_GT(subTryCnt, 2000);// 调整经验下限值
    system("gmsysview -q V\\$DRT_CONN_SUBS_STAT -f NODE_NAME=subChan1 | grep SUB_SEND_FAIL_CNT | awk 'NR==1'");
    int subSendFailCnt = GetViewValueByField(
        "DRT_CONN_SUBS_STAT -f NODE_NAME=subChan1 | grep SUB_SEND_FAIL_CNT | awk 'NR==1'", "SUB_SEND_FAIL_CNT");
    EXPECT_EQ(subSendFailCnt, 1);
    system("gmsysview -q V\\$DRT_CONN_SUBS_STAT -f NODE_NAME=subChan1 | grep SUB_SEND_SUC_CNT | awk 'NR==1'");
    int subSendSucCnt = GetViewValueByField(
        "DRT_CONN_SUBS_STAT -f NODE_NAME=subChan1 | grep SUB_SEND_SUC_CNT | awk 'NR==1'", "SUB_SEND_SUC_CNT");
    EXPECT_EQ(subSendSucCnt, subTryCnt - subSendFailCnt);

    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INSERT, subSendSucCnt, 12600);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, "newSubMulti001");
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(conn_sub, stmt_sub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(conn_sub2, stmt_sub2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcDropVertexLabelAsync(stmt_async, "lable1_8k", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = testGmcDisconnect(conn_async, stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "END\n");
}

void *UpdateBigObjectThreadFunc(void *args)
{
    GmcConnT *conn = NULL, *conn_async = NULL, *conn_sub = NULL, *conn_sub2 = NULL;
    GmcStmtT *stmt = NULL, *stmt_async = NULL, *stmt_sub = NULL, *stmt_sub2 = NULL;
    int ret = 0;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, "TypicalVertexLabel", GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 2000; i++)
    {
        ret = GmcSetIndexKeyName(stmt, "PrimaryKey");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int32_t tmpValue = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &tmpValue, sizeof(tmpValue));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int64_t eleNum = i + 10000;
        uint8_t eleBit = i % 255;
        uint8_t eleStr = i % 26 + 'a';
        uint8_t eleFixed = i % 26 + 'A';
        uint8_t eleBytes = i % 26 + 'A';

        int64_t a1 = eleNum;
        int ret = GmcSetVertexProperty(stmt, "A1", GMC_DATATYPE_INT64, &a1, sizeof(a1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t a2 = eleNum;
        ret = GmcSetVertexProperty(stmt, "A2", GMC_DATATYPE_UINT32, &a2, sizeof(a2));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint64_t a3 = eleNum;
        ret = GmcSetVertexProperty(stmt, "A3", GMC_DATATYPE_UINT64, &a3, sizeof(a3));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        float a4 = eleNum;
        ret = GmcSetVertexProperty(stmt, "A4", GMC_DATATYPE_FLOAT, &a4, sizeof(a4));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        double a5 = eleNum;
        ret = GmcSetVertexProperty(stmt, "A5", GMC_DATATYPE_DOUBLE, &a5, sizeof(a5));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint8_t bits[16 / 8] = {0};
        memset(bits, eleBit, sizeof(bits));
        GmcBitMapT a6 = {0};
        a6.beginPos = 0;
        a6.endPos = 16 - 1;
        a6.bits = bits;
        ret = GmcSetVertexProperty(stmt, "A6", GMC_DATATYPE_BITMAP, &a6, sizeof(a6));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint8_t a7[16];
        memset(a7, eleFixed, sizeof(a7));
        ret = GmcSetVertexProperty(stmt, "A7", GMC_DATATYPE_FIXED, &a7, sizeof(a7));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint8_t a8[65500];
        memset(a8, eleBytes, sizeof(a8));
        ret = GmcSetVertexProperty(stmt, "A8", GMC_DATATYPE_BYTES, a8, sizeof(a8));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint8_t a9[65500];
        memset(a9, eleStr, sizeof(a9) - 1);
        a9[65549] = '\0';
        ret = GmcSetVertexProperty(stmt, "A9", GMC_DATATYPE_STRING, a9, strlen((char *)a9));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}
void *ScanBigObjectThreadFunc(void *args)
{
    GmcConnT *conn = NULL, *conn_async = NULL, *conn_sub = NULL, *conn_sub2 = NULL;
    GmcStmtT *stmt = NULL, *stmt_async = NULL, *stmt_sub = NULL, *stmt_sub2 = NULL;
    int ret = 0;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, "TypicalVertexLabel", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char VarField[65500];
    uint32_t prop_size = 0;
    bool isNull;
    bool isFinish;
    int cnt;
    for (int32_t i = 0; i < 2000; i++)
    {
        int32_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "PrimaryKey");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        isFinish = false;
        cnt = 0;
        while (!isFinish)
        {
            ret = GmcFetch(stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (isFinish)
            {
                break;
            }
            ret = GmcGetVertexPropertySizeByName(stmt, "A9", &prop_size);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(stmt, "A9", VarField, prop_size, &isNull);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            cnt++;
        }
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}
// 031. 大对象更新和扫描并发，预期更新成功
// 【V3单号DTS2020051211969】
TEST_F(NewSubDML_04, SN_034_004_031)
{
    AW_FUN_Log(LOG_STEP, "START\n");
    
    GmcConnT *conn = NULL, *conn_async = NULL, *conn_sub = NULL, *conn_sub2 = NULL;
    GmcStmtT *stmt = NULL, *stmt_async = NULL, *stmt_sub = NULL, *stmt_sub2 = NULL;
    int ret = 0;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *g_schema = NULL;
    readJanssonFile("schema_file/BigObject.gmjson", &g_schema);
    EXPECT_NE((void *)NULL, g_schema);
    GmcDropVertexLabel(stmt, "TypicalVertexLabel");
    ret = GmcCreateVertexLabel(stmt, g_schema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_schema);
    ret = testGmcPrepareStmtByLabelName(stmt, "TypicalVertexLabel", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t a0 = 0;
    int32_t coefficient = 0; //8098
    for (int i = 0; i < 2000; i++)
    {
        ret = GmcSetVertexProperty(stmt, "A0", GMC_DATATYPE_INT32, &i, sizeof(i));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int64_t eleNum = i;
        uint8_t eleBit = i % 255;
        uint8_t eleStr = i % 26 + 'a';
        uint8_t eleFixed = i % 26 + 'A';
        uint8_t eleBytes = i % 26 + 'A';

        int64_t a1 = eleNum;
        int ret = GmcSetVertexProperty(stmt, "A1", GMC_DATATYPE_INT64, &a1, sizeof(a1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t a2 = eleNum;
        ret = GmcSetVertexProperty(stmt, "A2", GMC_DATATYPE_UINT32, &a2, sizeof(a2));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint64_t a3 = eleNum;
        ret = GmcSetVertexProperty(stmt, "A3", GMC_DATATYPE_UINT64, &a3, sizeof(a3));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        float a4 = eleNum;
        ret = GmcSetVertexProperty(stmt, "A4", GMC_DATATYPE_FLOAT, &a4, sizeof(a4));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        double a5 = eleNum;
        ret = GmcSetVertexProperty(stmt, "A5", GMC_DATATYPE_DOUBLE, &a5, sizeof(a5));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint8_t bits[16 / 8] = {0};
        memset(bits, eleBit, sizeof(bits));
        GmcBitMapT a6 = {0};
        a6.beginPos = 0;
        a6.endPos = 16 - 1;
        a6.bits = bits;
        ret = GmcSetVertexProperty(stmt, "A6", GMC_DATATYPE_BITMAP, &a6, sizeof(a6));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint8_t a7[16];
        memset(a7, eleFixed, sizeof(a7));
        ret = GmcSetVertexProperty(stmt, "A7", GMC_DATATYPE_FIXED, &a7, sizeof(a7));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint8_t a8[65500];
        memset(a8, eleBytes, sizeof(a8));
        ret = GmcSetVertexProperty(stmt, "A8", GMC_DATATYPE_BYTES, a8, sizeof(a8));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint8_t a9[65500];
        memset(a9, eleStr, sizeof(a9) - 1);
        // ubsan
        a9[65499] = '\0';
        ret = GmcSetVertexProperty(stmt, "A9", GMC_DATATYPE_STRING, a9, strlen((char *)a9));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    system("gmsysview count TypicalVertexLabel");
    system("gmsysview -q V$\\CATA_TABLESPACE_INFO");

    pthread_t dropLable[2];
    pthread_t openLable[2];
    int i = 0;
    int index[2];
    memset(index, 0x00, 2 * sizeof(int));
    for (i = 1; i < 2; i++)
    {
        index[i] = i;
        ret = pthread_create(&openLable[i], NULL, UpdateBigObjectThreadFunc, (void *)&index[i]);
        EXPECT_EQ(ret, GMERR_OK);
        ret = pthread_create(&dropLable[i], NULL, ScanBigObjectThreadFunc, (void *)&index[i]);
        EXPECT_EQ(ret, GMERR_OK);
    }
    for (int i = 1; i < 2; i++)
    {
        ret = pthread_join(openLable[i], NULL);
        EXPECT_EQ(ret, GMERR_OK);
        ret = pthread_join(dropLable[i], NULL);
        EXPECT_EQ(ret, GMERR_OK);
    }
    ret = GmcDropVertexLabel(stmt, "TypicalVertexLabel");
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    AW_FUN_Log(LOG_STEP, "END\n");
}

// 订阅回调支持dml操作
// if_sub_callback_localhash_scan_test.cpp
// 补充订阅回调中执行localhash扫描操作，预期扫描功能正常
void NewSnCallBackLocalFetch(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int subNum = 0;
    int subPushModify = 0;
    int subInital = 0;
    int subPushDel = 0;
    int index = 0;
    GmcConnT *conn2 = NULL, *conn_sub = NULL;
    GmcStmtT *stmt2 = NULL, *stmt_sub = NULL;
    int ret = testGmcConnect(&conn2, &stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char labelName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;
    bool eof = false;
    uint32_t getValueSizeOut;
    char readValueOut[128];
    ret = testGmcPrepareStmtByLabelName(stmt2, "OP_T0", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t VarField = 0;
    uint32_t prop_size = 0;
    bool isNull;
    bool isFinish;
    int cnt;
    for (int64_t i = 0; i < 1000; i++)
    {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt2, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt2, "localhash_key");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        isFinish = false;
        cnt = 0;
        while (!isFinish)
        {
            ret = GmcFetch(stmt2, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (isFinish)
            {
                break;
            }
            ret = GmcGetVertexPropertySizeByName(stmt2, "F1", &prop_size);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(stmt2, "F1", &VarField, prop_size, &isNull);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            cnt++;
        }
    }
    for (int i = 0; i < info->labelCount; i++)
    {
        memset(labelName, 0, sizeof(labelName));
        labelNameLen = MAX_NAME_LENGTH;
        ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(strlen(labelName), labelNameLen);
        switch (info->eventType)
        {
        case GMC_SUB_EVENT_MODIFY:
        {
            ret = GmcFetch(subStmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (ret != GMERR_OK || eof == true)
            {
                break;
            }
            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);
            index = ((int *)user_data->new_value)[user_data->subIndex];

            if (index == 99)
            {
                checkLastValue(subStmt, 99, 'e', 'e', 99.00, (char *)"end", (char *)"end", (char *)"end");
            }
            break;
        }
        case GMC_SUB_EVENT_DELETE:
        {
            // check old value
            ret = GmcFetch(subStmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (ret != GMERR_OK || eof == true)
            {
                break;
            }
            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
            EXPECT_EQ(GMERR_OK, ret);
            index = ((int *)user_data->old_value)[user_data->subIndex];
            if (index == 99)
            {
                checkLastValue(subStmt, 99, 'e', 'e', 99.00, (char *)"end", (char *)"end", (char *)"end");
            }
            break;
        }
        case GMC_SUB_EVENT_INITIAL_LOAD:
        {
            ret = GmcFetch(subStmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (ret != GMERR_OK || eof == true)
            {
                break;
            }
            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);
            index = ((int *)user_data->new_value)[user_data->subIndex];
            if (index == 99)
            {
                checkLastValue(subStmt, 99, 'e', 'e', 99.00, (char *)"end", (char *)"end", (char *)"end");
            }
            break;
        }
        case GMC_SUB_EVENT_INITIAL_LOAD_EOF:
        {
            // TOF
            ret = GmcFetch(subStmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (ret != GMERR_OK || eof == true)
            {
                printf("\n[EOF] GMC_SUB_EVENT_INITIAL_LOAD_EOF\n");
                break;
            }
        }
        default:
        {
            printf("default: invalid eventType\r\n");
            break;
        }
        }
    }
    switch (info->eventType)
    {
    case GMC_SUB_EVENT_MODIFY:
    {
        user_data->insertNum++;
        break;
    }
    case GMC_SUB_EVENT_DELETE:
    {
        user_data->deleteNum++;
        break;
    }
    case GMC_SUB_EVENT_INITIAL_LOAD:
    {
        user_data->scanNum++;
        break;
    }
    case GMC_SUB_EVENT_INITIAL_LOAD_EOF:
    {
        user_data->scanEofNum++;
        break;
    }
    default:
    {
        printf("default: eventType\r\n");
        break;
    }
    }
}
// 032. 订阅回调支持dml操作, 要注意回调机制是不断触发，回调中做建连和DML操作容易出错
// 【V3单号DTS2020050717014】
TEST_F(NewSubDML_04, SN_034_004_032)
{
    int ret = 0;
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_PRIMARY_KEY_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    GmcConnT *conn = NULL, *conn_sub = NULL;
    GmcStmtT *stmt = NULL, *stmt_sub = NULL;
    int start_num = 0;
    int end_num = 2;
    int array_num = 3;
    int vector_num = 3;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    SnUserDataT *userData = NULL;
    ret = testSnMallocUserData(&userData, 100, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *newSubInfoAddMulti = R"(
    {
            "name": "newSubTreeMulti001",
            "label_name": "OP_T0",
            "comment":"status_merge",
            "type":"before_commit",
            "events":
                [
                    {"type":"modify", "msgTypes":["new object", "old object"]},
                    {"type":"delete", "msgTypes":["new object", "old object"]}
                ],
            "is_path":false,
            "retry":true,
            "priority": 1,
            "subs_type":"status_merge"
    })";
    GmcDropVertexLabel(stmt, "OP_T0");
    ret = GmcCreateVertexLabel(stmt, g_treeschema, g_tableConfig2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&conn_sub, &stmt_sub, 1, g_epoll_reg_info, "newsubconn", &g_chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcSubConfigT tsub_info;
    tsub_info.subsName = "newSubTreeMulti001";
    tsub_info.configJson = newSubInfoAddMulti;
    ret = GmcSubscribe(stmt, &tsub_info, conn_sub, NewSnCallBackLocalFetch, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, "OP_T0");
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;
    char f14_value[8] = "stoooo";
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_MODIFY, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, "newSubTreeMulti001");
    ret = GmcDropVertexLabel(stmt, "OP_T0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(conn_sub, stmt_sub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);
    
    AW_FUN_Log(LOG_STEP, "END\n");
}

// 033. 1MB，更新场景，每次更新正常大小的对象，但逐渐累加导致记录总大小超过1M
// 大对象指一条记录，当前V5根本没办法做到所谓的累加，只不过是多次操作，其中有几次记录数超了限制
// V5没有对应对象视图，查询DB上涨内存采用动态或共享内存观察
// 【V3单号DTS2021051702XEBJP0H00】
TEST_F(NewSubDML_04, SN_034_004_033)
{
    AW_FUN_Log(LOG_STEP, "START\n");
    
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    GmcConnT *conn = NULL, *conn_async = NULL, *conn_sub = NULL, *conn_sub2 = NULL;
    GmcStmtT *stmt = NULL, *stmt_async = NULL, *stmt_sub = NULL, *stmt_sub2 = NULL;
    int ret = 0;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *g_schema = NULL;
    readJanssonFile("schema_file/BigObject2.gmjson", &g_schema);
    EXPECT_NE((void *)NULL, g_schema);
    GmcDropVertexLabel(stmt, "TypicalVertexLabel");
    ret = GmcCreateVertexLabel(stmt, g_schema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_schema);
    ret = testGmcPrepareStmtByLabelName(stmt, "TypicalVertexLabel", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t a0 = 0;
    int32_t coefficient = 0; //8098
    for (int i = 1; i < 2; i++)
    {
        ret = GmcSetVertexProperty(stmt, "A0", GMC_DATATYPE_INT32, &i, sizeof(i));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int64_t eleNum = i;
        uint8_t eleBit = i % 255;
        uint8_t eleStr = i % 26 + 'a';
        uint8_t eleFixed = i % 26 + 'A';
        uint8_t eleBytes = i % 26 + 'A';

        int64_t a1 = eleNum;
        int ret = GmcSetVertexProperty(stmt, "A1", GMC_DATATYPE_INT64, &a1, sizeof(a1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t a2 = eleNum;
        ret = GmcSetVertexProperty(stmt, "A2", GMC_DATATYPE_UINT32, &a2, sizeof(a2));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint64_t a3 = eleNum;
        ret = GmcSetVertexProperty(stmt, "A3", GMC_DATATYPE_UINT64, &a3, sizeof(a3));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        float a4 = eleNum;
        ret = GmcSetVertexProperty(stmt, "A4", GMC_DATATYPE_FLOAT, &a4, sizeof(a4));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        double a5 = eleNum;
        ret = GmcSetVertexProperty(stmt, "A5", GMC_DATATYPE_DOUBLE, &a5, sizeof(a5));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint8_t bits[16 / 8];
        memset(bits, eleBit, sizeof(bits));
        GmcBitMapT a6 = {0};
        a6.beginPos = 0;
        a6.endPos = 16 - 1;
        a6.bits = bits;
        ret = GmcSetVertexProperty(stmt, "A6", GMC_DATATYPE_BITMAP, &a6, sizeof(a6));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint8_t a7[16];
        memset(a7, eleFixed, sizeof(a7));
        ret = GmcSetVertexProperty(stmt, "A7", GMC_DATATYPE_FIXED, &a7, sizeof(a7));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    system("gmsysview count TypicalVertexLabel");
    system("gmsysview -q V$\\CATA_TABLESPACE_INFO");
    ret = testGmcPrepareStmtByLabelName(stmt, "TypicalVertexLabel", GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t coefficients = 4096;
    for (int i = 1; i < 4; i++)
    {
        uint32_t varlength = coefficients * i;
        ret = GmcSetIndexKeyName(stmt, "PrimaryKey");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int32_t tmpValue = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &tmpValue, sizeof(tmpValue));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int64_t eleNum = i + 10000;
        uint8_t eleBit = i % 255;
        uint8_t eleStr = i % 26 + 'a';
        uint8_t eleFixed = i % 26 + 'A';
        uint8_t eleBytes = i % 26 + 'A';

        int64_t a1 = eleNum;
        int ret = GmcSetVertexProperty(stmt, "A1", GMC_DATATYPE_INT64, &a1, sizeof(a1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t a2 = eleNum;
        ret = GmcSetVertexProperty(stmt, "A2", GMC_DATATYPE_UINT32, &a2, sizeof(a2));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint64_t a3 = eleNum;
        ret = GmcSetVertexProperty(stmt, "A3", GMC_DATATYPE_UINT64, &a3, sizeof(a3));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        float a4 = eleNum;
        ret = GmcSetVertexProperty(stmt, "A4", GMC_DATATYPE_FLOAT, &a4, sizeof(a4));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        double a5 = eleNum;
        ret = GmcSetVertexProperty(stmt, "A5", GMC_DATATYPE_DOUBLE, &a5, sizeof(a5));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint8_t bits[16 / 8];
        memset(bits, eleBit, sizeof(bits));
        GmcBitMapT a6 = {0};
        a6.beginPos = 0;
        a6.endPos = 16 - 1;
        a6.bits = bits;
        ret = GmcSetVertexProperty(stmt, "A6", GMC_DATATYPE_BITMAP, &a6, sizeof(a6));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint8_t a7[16];
        memset(a7, eleFixed, sizeof(a7));
        ret = GmcSetVertexProperty(stmt, "A7", GMC_DATATYPE_FIXED, &a7, sizeof(a7));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t a8[varlength];
        memset(a8, eleBytes, sizeof(a8));
        ret = GmcSetVertexProperty(stmt, "A8", GMC_DATATYPE_BYTES, a8, sizeof(a8));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t a9[varlength];
        memset(a9, eleStr, sizeof(a9));
        ret = GmcSetVertexProperty(stmt, "A9", GMC_DATATYPE_STRING, a9, strlen((char *)a9));
        if (ret != GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
        }

        uint32_t a10[varlength];
        memset(a10, eleBytes, sizeof(a10));
        ret = GmcSetVertexProperty(stmt, "A10", GMC_DATATYPE_BYTES, a10, sizeof(a10));
        if (ret != GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
        }

        uint32_t a11[varlength];
        memset(a11, eleStr, sizeof(a11));
        ret = GmcSetVertexProperty(stmt, "A11", GMC_DATATYPE_STRING, a11, strlen((char *)a11));
        if (ret != GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
        }

        uint32_t a12[varlength];
        memset(a12, eleBytes, sizeof(a12));
        ret = GmcSetVertexProperty(stmt, "A12", GMC_DATATYPE_BYTES, a12, sizeof(a12));
        if (ret != GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
        }

        uint32_t a13[varlength];
        memset(a13, eleStr, sizeof(a13));
        ret = GmcSetVertexProperty(stmt, "A13", GMC_DATATYPE_STRING, a13, strlen((char *)a13));
        if (ret != GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
        }

        uint32_t a14[varlength];
        memset(a14, eleBytes, sizeof(a14));
        ret = GmcSetVertexProperty(stmt, "A14", GMC_DATATYPE_BYTES, a14, sizeof(a14));
        if (ret != GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
        }

        uint32_t a15[varlength];
        memset(a15, eleStr, sizeof(a15));
        ret = GmcSetVertexProperty(stmt, "A15", GMC_DATATYPE_STRING, a15, strlen((char *)a15));
        if (ret != GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
        }

        uint32_t a16[varlength];
        memset(a16, eleBytes, sizeof(a16));
        ret = GmcSetVertexProperty(stmt, "A16", GMC_DATATYPE_BYTES, a16, sizeof(a16));
        if (ret != GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
        }

        uint32_t a17[varlength];
        memset(a17, eleStr, sizeof(a17));
        ret = GmcSetVertexProperty(stmt, "A17", GMC_DATATYPE_STRING, a17, strlen((char *)a17));
        if (ret != GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
        }

        uint32_t a18[varlength];
        memset(a18, eleBytes, sizeof(a18));
        ret = GmcSetVertexProperty(stmt, "A18", GMC_DATATYPE_BYTES, a18, sizeof(a18));
        if (ret != GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
        }

        uint32_t a19[varlength];
        memset(a19, eleStr, sizeof(a19));
        ret = GmcSetVertexProperty(stmt, "A19", GMC_DATATYPE_STRING, a19, strlen((char *)a19));
        if (ret != GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
        }

        uint32_t a20[varlength];
        memset(a20, eleBytes, sizeof(a20));
        ret = GmcSetVertexProperty(stmt, "A20", GMC_DATATYPE_BYTES, a20, sizeof(a20));
        if (ret != GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
        }

        uint32_t a21[varlength];
        memset(a21, eleStr, sizeof(a21));
        ret = GmcSetVertexProperty(stmt, "A21", GMC_DATATYPE_STRING, a21, strlen((char *)a21));
        if (ret != GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
        }

        uint32_t a22[varlength];
        memset(a22, eleBytes, sizeof(a22));
        ret = GmcSetVertexProperty(stmt, "A22", GMC_DATATYPE_BYTES, a22, sizeof(a22));
        if (ret != GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
        }

        uint32_t a23[varlength];
        memset(a23, eleStr, sizeof(a23));
        ret = GmcSetVertexProperty(stmt, "A23", GMC_DATATYPE_STRING, a23, strlen((char *)a23));
        if (ret != GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
        }
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        system("gmsysview -q V$\\CATA_TABLESPACE_INFO");
        system("gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME='Top Dynamic Memory Context'");
    }
    ret = GmcDropVertexLabel(stmt, "TypicalVertexLabel");
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    AW_FUN_Log(LOG_STEP, "END\n");
}

// 034. yang 10M大对象场景SN_034_005_034
//【V3单号DTS2021051702XEBJP0H00】

// 035. KV数据序列化后Key部分长度为512字节
// KV无法达到大报文设置32M
// 【V3单号DTS2021051702XEBJP0H00】
TEST_F(NewSubDML_04, SN_034_004_035)
{
    AW_FUN_Log(LOG_STEP, "start\n");
    GmcConnT *g_conn = NULL, *conn_async = NULL, *conn_sub = NULL, *conn_sub2 = NULL;
    GmcStmtT *g_stmt = NULL, *stmt_async = NULL, *stmt_sub = NULL, *stmt_sub2 = NULL;
    const char *cfgJson = R"({"max_record_count":1000000})";
    int ret = 0;
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    void *tableLabel = NULL;
    char kvTableName2[] = "student2";
    ret = GmcKvCreateTable(g_stmt, kvTableName2, cfgJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(g_stmt, kvTableName2);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchRetT batchRet;
    GmcBatchT *batch = NULL;
    ret = GmcBatchPrepare(g_conn, NULL, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    // batch set
    GmcKvTupleT kvInfo1 = {0};
    char key1[] = "zhangsan1";
    int32_t value1 = 10;
    kvInfo1.key = key1;
    kvInfo1.keyLen = strlen(key1);
    kvInfo1.value = &value1;
    kvInfo1.valueLen = sizeof(int32_t);

    ret = GmcKvInputToStmt(g_stmt, key1, strlen(key1), &value1, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddKvDML(batch, g_stmt, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    GmcKvTupleT kvInfo2 = {0};
    char key2[512];
    memset(key2, 'a', 510);
    key2[511] = '\0';
    char value2[1034];
    memset(value2, 'b', 1032);
    value2[1033] = '\0';

    kvInfo2.key = key2;
    kvInfo2.keyLen = strlen(key2);
    kvInfo2.value = &value2;
    kvInfo2.valueLen = strlen(value2);

    ret = GmcKvInputToStmt(g_stmt, key2, strlen(key2), &value2, strlen(value2));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddKvDML(batch, g_stmt, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    bool isExist = true;
    ret = GmcKvIsExist(g_stmt, key2, strlen(key2), &isExist);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, isExist);

    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(totalNum, (uint32_t)2);
    EXPECT_EQ(successNum, (uint32_t)2);

    system("gmsysview -q V$\\STORAGE_KV_COUNT");
    ret = GmcKvDropTable(g_stmt, kvTableName2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);
    AddWhiteList(GMERR_INVALID_NAME);
    system("gmddl -c drop -t student2");
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "END\n");
}


// 036. 超过64个member索引建表失败
TEST_F(NewSubDML_04, SN_034_004_036)
{
    AW_FUN_Log(LOG_STEP, "start\n");
    int32_t ret = 0;
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    GmcConnT *conn = NULL, *conn_sub = NULL;
    GmcStmtT *stmt = NULL, *stmt_sub = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *test_schema =
        "[{\"name\":\"T80\", \"version\":\"2.0\", \"type\":\"record\","
        "\"fields\":["
        "{ \"name\":\"F0\", \"type\":\"int8\"},"
        "{ \"name\":\"F1\", \"type\":\"int32\",\"nullable\":true},"
        "{ \"name\":\"F2\", \"type\":\"int16\" },"
        "{ \"name\":\"F3\", \"type\":\"uint32\"},"
        "{ \"name\":\"F4\", \"type\":\"int16\" ,\"nullable\":true},"
        "{ \"name\":\"F5\", \"type\":\"int16\" ,\"nullable\":true},"
        "{ \"name\":\"F6\", \"type\":\"int16\" ,\"nullable\":true},"
        "{ \"type\":\"record\", \"name\":\"T82\" ,\"nullable\": true,\"array\":true, \"size\":900, \"fields\":["
        "{ \"name\":\"A0\", \"type\":\"uint32\",\"nullable\":true},"
        "{ \"name\":\"A1\", \"type\":\"uint32\",\"nullable\":true},"
        "{ \"name\":\"A2\", \"type\":\"uint32\" ,\"nullable\":true}"
        "]"
        "}"
        "],"
        "\"keys\":["
        "{ \"name\":\"PK\", \"node\":\"T80\", \"fields\":[\"F0\"],\"index\": { \"type\":\"primary\" }},"
        "{ \"name\": \"T82_A0\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_A1\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_A2\", \"node\": \"T82\", \"fields\": [\"A2\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_A3\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_A4\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_A5\", \"node\": \"T82\", \"fields\": [\"A2\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_A6\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_A7\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_A8\", \"node\": \"T82\", \"fields\": [\"A2\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_A9\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_A10\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_B0\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_B1\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_B2\", \"node\": \"T82\", \"fields\": [\"A2\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_B3\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_B4\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_B5\", \"node\": \"T82\", \"fields\": [\"A2\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_B6\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_B7\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_B8\", \"node\": \"T82\", \"fields\": [\"A2\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_B9\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_B10\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_C0\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_C1\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_C2\", \"node\": \"T82\", \"fields\": [\"A2\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_C3\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_C4\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_C5\", \"node\": \"T82\", \"fields\": [\"A2\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_C6\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_C7\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_C8\", \"node\": \"T82\", \"fields\": [\"A2\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_C9\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_C10\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_D0\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_D1\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_D2\", \"node\": \"T82\", \"fields\": [\"A2\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_D3\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_D4\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_D5\", \"node\": \"T82\", \"fields\": [\"A2\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_D6\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_D7\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_D8\", \"node\": \"T82\", \"fields\": [\"A2\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_D9\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"

        "{ \"name\": \"T82_D10\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"

        "{ \"name\": \"T82_E0\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"

        "{ \"name\": \"T82_E1\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_E2\", \"node\": \"T82\", \"fields\": [\"A2\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_E3\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_E4\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_E5\", \"node\": \"T82\", \"fields\": [\"A2\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_E6\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_E7\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_E8\", \"node\": \"T82\", \"fields\": [\"A2\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_E9\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_E10\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_F0\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_F1\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_F2\", \"node\": \"T82\", \"fields\": [\"A2\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_F3\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_F4\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_F5\", \"node\": \"T82\", \"fields\": [\"A2\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_F6\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_F7\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_F8\", \"node\": \"T82\", \"fields\": [\"A2\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_F9\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_F10\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}}"
        "]"
        "}]";

    ret = GmcCreateVertexLabel(stmt, test_schema, NULL);
    ASSERT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
    system("gmsysview -q V$\\STORAGE_HEAP_VERTEX_LABEL_STAT -f VERTEXLABEL_NAME=T80");
    ret = GmcDropVertexLabel(stmt, "T80");
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "END\n");
}
// 037. 64个member索引没有显示
TEST_F(NewSubDML_04, SN_034_004_037)
{
    AW_FUN_Log(LOG_STEP, "start\n");
    int32_t ret = 0;
    GmcConnT *conn = NULL, *conn_sub = NULL;
    GmcStmtT *stmt = NULL, *stmt_sub = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *test_schema =
        "[{\"name\":\"T80\", \"version\":\"2.0\", \"type\":\"record\","
        "\"fields\":["
        "{ \"name\":\"F0\", \"type\":\"int8\"},"
        "{ \"name\":\"F1\", \"type\":\"int32\",\"nullable\":true},"
        "{ \"name\":\"F2\", \"type\":\"int16\" },"
        "{ \"name\":\"F3\", \"type\":\"uint32\"},"
        "{ \"name\":\"F4\", \"type\":\"int16\" ,\"nullable\":true},"
        "{ \"name\":\"F5\", \"type\":\"int16\" ,\"nullable\":true},"
        "{ \"name\":\"F6\", \"type\":\"int16\" ,\"nullable\":true},"
        "{ \"type\":\"record\", \"name\":\"T82\" ,\"nullable\": true,\"array\":true, \"size\":900, \"fields\":["
        "{ \"name\":\"A0\", \"type\":\"uint32\",\"nullable\":true},"
        "{ \"name\":\"A1\", \"type\":\"uint32\",\"nullable\":true},"
        "{ \"name\":\"A2\", \"type\":\"uint32\" ,\"nullable\":true}"
        "]"
        "}"
        "],"
        "\"keys\":["
        "{ \"name\":\"PK\", \"node\":\"T80\", \"fields\":[\"F0\"],\"index\": { \"type\":\"primary\" }},"
        "{ \"name\": \"T82_A0\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_A1\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_A2\", \"node\": \"T82\", \"fields\": [\"A2\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_A3\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_A4\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_A5\", \"node\": \"T82\", \"fields\": [\"A2\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_A6\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_A7\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_A8\", \"node\": \"T82\", \"fields\": [\"A2\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_A9\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_A10\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_B0\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_B1\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_B2\", \"node\": \"T82\", \"fields\": [\"A2\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_B3\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_B4\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_B5\", \"node\": \"T82\", \"fields\": [\"A2\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_B6\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_B7\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_B8\", \"node\": \"T82\", \"fields\": [\"A2\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_B9\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_B10\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_C0\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_C1\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_C2\", \"node\": \"T82\", \"fields\": [\"A2\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_C3\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_C4\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_C5\", \"node\": \"T82\", \"fields\": [\"A2\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_C6\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_C7\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_C8\", \"node\": \"T82\", \"fields\": [\"A2\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_C9\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_C10\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_D0\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_D1\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_D2\", \"node\": \"T82\", \"fields\": [\"A2\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_D3\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_D4\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_D5\", \"node\": \"T82\", \"fields\": [\"A2\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_D6\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_D7\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_D8\", \"node\": \"T82\", \"fields\": [\"A2\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_D9\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"

        "{ \"name\": \"T82_D10\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"

        "{ \"name\": \"T82_E0\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"

        "{ \"name\": \"T82_E1\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_E2\", \"node\": \"T82\", \"fields\": [\"A2\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_E3\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_E4\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_E5\", \"node\": \"T82\", \"fields\": [\"A2\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_E6\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_E7\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_E8\", \"node\": \"T82\", \"fields\": [\"A2\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_E9\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_E10\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_F0\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_F1\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_F2\", \"node\": \"T82\", \"fields\": [\"A2\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_F3\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_F4\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_F5\", \"node\": \"T82\", \"fields\": [\"A2\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_F6\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_F7\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}}"
        "]"
        "}]";

    ret = GmcCreateVertexLabel(stmt, test_schema, NULL);
    ASSERT_EQ(0, ret);
    system("gmsysview -q V$\\STORAGE_HEAP_VERTEX_LABEL_STAT -f VERTEXLABEL_NAME=T80");
    ret = GmcDropVertexLabel(stmt, "T80");
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "END\n");
}
// 038. 64个总索引+其他索引没有显示
TEST_F(NewSubDML_04, SN_034_004_038)
{
    AW_FUN_Log(LOG_STEP, "start\n");
    int32_t ret = 0;
    GmcConnT *conn = NULL, *conn_sub = NULL;
    GmcStmtT *stmt = NULL, *stmt_sub = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *test_schema =
        "[{\"name\":\"T80\", \"version\":\"2.0\", \"type\":\"record\","
        "\"fields\":["
        "{ \"name\":\"F0\", \"type\":\"int8\"},"
        "{ \"name\":\"F1\", \"type\":\"int32\",\"nullable\":true},"
        "{ \"name\":\"F2\", \"type\":\"int16\" },"
        "{ \"name\":\"F3\", \"type\":\"uint32\"},"
        "{ \"name\":\"F4\", \"type\":\"int16\" ,\"nullable\":true},"
        "{ \"name\":\"F5\", \"type\":\"int16\" ,\"nullable\":true},"
        "{ \"name\":\"F6\", \"type\":\"int16\" ,\"nullable\":true},"

        "{ \"name\": \"vr_id\", \"type\": \"uint32\", \"comment\": \"Vs索引\" },"
        "{ \"name\": \"vrf_index\", \"type\": \"uint32\", \"comment\": \"VpnInstace索引\" },"
        "{ \"name\": \"dest_ip_addr\", \"type\": \"uint32\", \"comment\": \"目的地址\" },"
        "{ \"name\": \"mask_len\", \"type\": \"uint8\", \"comment\": \"掩码长度\" },"

        "{ \"type\":\"record\", \"name\":\"T82\" ,\"nullable\": true,\"array\":true, \"size\":900, \"fields\":["
        "{ \"name\":\"A0\", \"type\":\"uint32\",\"nullable\":true},"
        "{ \"name\":\"A1\", \"type\":\"uint32\",\"nullable\":true},"
        "{ \"name\":\"A2\", \"type\":\"uint32\" ,\"nullable\":true}"
        "]"
        "}"
        "],"
        "\"keys\":["
        "{ \"name\":\"PK\", \"node\":\"T80\", \"fields\":[\"F0\"],\"index\": { \"type\":\"primary\" }},"
        "{ \"name\": \"T82_A0\", \"node\": \"T80\", \"fields\": [\"F1\"],\"index\": { \"type\": \"localhash\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_A1\", \"node\": \"T80\", \"fields\": [\"F1\"],\"index\": { \"type\": \"localhash\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_A2\", \"node\": \"T80\", \"fields\": [\"F1\"],\"index\": { \"type\": \"localhash\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_A3\", \"node\": \"T80\", \"fields\": [\"F1\"],\"index\": { \"type\": \"localhash\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_A4\", \"node\": \"T80\", \"fields\": [\"F1\"],\"index\": { \"type\": \"localhash\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_A5\", \"node\": \"T80\", \"fields\": [\"F1\"],\"index\": { \"type\": \"localhash\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_A6\", \"node\": \"T80\", \"fields\": [\"F1\"],\"index\": { \"type\": \"localhash\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_A7\", \"node\": \"T80\", \"fields\": [\"F1\"],\"index\": { \"type\": \"localhash\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_A8\", \"node\": \"T80\", \"fields\": [\"F2\"],\"index\": { \"type\": \"local\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_A9\", \"node\": \"T80\", \"fields\": [\"F2\"],\"index\": { \"type\": \"local\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_A10\", \"node\": \"T80\", \"fields\": [\"F2\"],\"index\": { \"type\": \"local\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_B0\", \"node\": \"T80\", \"fields\": [\"F2\"],\"index\": { \"type\": \"local\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_B1\", \"node\": \"T80\", \"fields\": [\"F3\"],\"index\": { \"type\": \"hashcluster\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_B2\", \"node\": \"T80\", \"fields\": [\"F3\"],\"index\": { \"type\": \"hashcluster\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_B3\", \"node\": \"T80\", \"fields\": [\"F3\"],\"index\": { \"type\": \"hashcluster\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_B4\", \"node\": \"T80\", \"fields\": [\"F3\"],\"index\": { \"type\": \"hashcluster\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_B5\", \"node\": \"T82\", \"fields\": [\"A2\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_B6\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_B7\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_B8\", \"node\": \"T82\", \"fields\": [\"A2\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_B9\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_B10\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_C0\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_C1\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_C2\", \"node\": \"T82\", \"fields\": [\"A2\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_C3\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_C4\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_C5\", \"node\": \"T82\", \"fields\": [\"A2\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_C6\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_C7\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_C8\", \"node\": \"T82\", \"fields\": [\"A2\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_C9\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_C10\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_D0\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_D1\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_D2\", \"node\": \"T82\", \"fields\": [\"A2\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_D3\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_D4\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_D5\", \"node\": \"T82\", \"fields\": [\"A2\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_D6\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_D7\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_D8\", \"node\": \"T82\", \"fields\": [\"A2\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_D9\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"

        "{ \"name\": \"T82_D10\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"

        "{ \"name\": \"T82_E0\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"

        "{ \"name\": \"T82_E1\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_E2\", \"node\": \"T82\", \"fields\": [\"A2\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_E3\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_E4\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_E5\", \"node\": \"T82\", \"fields\": [\"A2\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_E6\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_E7\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_E8\", \"node\": \"T82\", \"fields\": [\"A2\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_E9\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_E10\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_F0\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_F1\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_F2\", \"node\": \"T82\", \"fields\": [\"A2\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_F3\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_F4\", \"node\": \"T82\", \"fields\": [\"A1\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_F5\", \"node\": \"T82\", \"fields\": [\"A2\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"T82_F6\", \"node\": \"T82\", \"fields\": [\"A0\"],\"index\": { \"type\": \"none\" "
        "},\"constraints\": {\"unique\": true}},"
        "{ \"name\": \"ip4forward_lpm\", \"index\": { \"type\": \"lpm4_tree_bitmap\" },"
        "\"node\": \"T80\","
        "\"fields\": [ \"vr_id\", \"vrf_index\", \"dest_ip_addr\", \"mask_len\" ],"
        "\"constraints\": { \"unique\": true }}"
        "]"
        "}]";

    ret = GmcCreateVertexLabel(stmt, test_schema, NULL);
    ASSERT_EQ(0, ret);
    system("gmsysview -q V$\\STORAGE_HEAP_VERTEX_LABEL_STAT -f VERTEXLABEL_NAME=T80");
    ret = GmcDropVertexLabel(stmt, "T80");
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "END\n");
}

// 039. 新订阅，50个线程并发写更新删除, 每个线程都写，更新，删除
// 【V3单号DTS2023040803987】
TEST_F(NewSubDML_04, SN_034_004_039)
{
    int ret = 0;
    char *mySchema = NULL;
    GmcConnT *conn_sub = NULL;
    GmcStmtT *stmt_sub = NULL;
    int32_t thrNum = 50;
    int32_t insertNum = 10000;
#if defined(ENV_RTOSV2X)
    thrNum = 5;
    insertNum = 100;
#else
    thrNum = 50;
    insertNum = 10000;
#endif
    const uint16_t t2Count = 10, t3Count = 10;
    char g_specialTableName[] = "TEST_SC_T1";
    char g_pkName[] = "TEST_PK";
    const char *newSubInfoAddMulti1 = R"(
    {
            "name": "newSubTreeMulti001",
            "label_name": "V_1",
            "comment":"status_merge",
            "type":"before_commit",
            "events":
                [
                    {"type":"initial_load", "msgTypes":["new object"]},
                    {"type":"modify", "msgTypes":["new object", "old object"]},
                    {"type":"delete", "msgTypes":["new object", "old object"]}
                ],
            "is_path":false,
            "retry":true,
            "priority": 1,
            "subs_type":"status_merge"
    })";
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *g_schema1 = NULL;
    char *g_schema3 = NULL;
    GmcDropVertexLabel(g_stmt, "V_1");
    readJanssonFile("./schema_file/NewSubV_1.gmjson", &g_schema1);
    AW_MACRO_ASSERT_NE_INT((void *)NULL, g_schema1);
    ret = GmcCreateVertexLabel(g_stmt, g_schema1, g_tableConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_schema1);

    ret = testSnMallocUserData(&userData1, 400, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubConnect(&conn_sub, &stmt_sub, 1, g_epoll_reg_info, "newSubTreeMulti001", &g_chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubConfigT tsub_info1;
    tsub_info1.subsName = "V_1";
    tsub_info1.configJson = newSubInfoAddMulti1;
    ret = GmcSubscribe(g_stmt, &tsub_info1, conn_sub, NewSnCallBack10, userData1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_t thr_arr[thrNum];
    void *thr_ret[thrNum];
    for (int i = 0; i < thrNum; i++)
    {
        ret = pthread_create(&thr_arr[i], NULL, simple_table_writeupdatedelete_fun, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testWaitStMgSnRecv(userData1, GMC_SUB_EVENT_MODIFY, 0, 1000 * 2 * thrNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitStMgSnRecv(userData1, GMC_SUB_EVENT_DELETE, 1000, 1000 * thrNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int y = 0; y < thrNum; y++)
    {
        ret = pthread_join(thr_arr[y], &thr_ret[y]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcUnSubscribe(g_stmt, "V_1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, "V_1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData1);

    ret = testSubDisConnect(conn_sub, stmt_sub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "END\n");
}

// 040. 创建订阅通道后下发订阅关系，直到上限，用例结束后，服务端是否死循环
// DTS，为什么显示 1011000 cannot send sub notify
// 【V3单号DTS2023040304347】
// 【V5单号DTS2023060608136】
TEST_F(NewSubDML_04, SN_034_004_040)
{
    int ret = 0;
    char *mySchema = NULL;
    GmcConnT *conn_sub = NULL;
    GmcStmtT *stmt_sub = NULL;
    int32_t thrNum = 10;
    int32_t insertNum = 10000;
#if defined(ENV_RTOSV2X)
    thrNum = 2;
    insertNum = 200;
#else
    thrNum = 10;
    insertNum = 10000;
#endif
    const uint16_t t2Count = 10, t3Count = 10;
    char g_specialTableName[] = "TEST_SC_T1";
    char g_pkName[] = "TEST_PK";
    const char *newSubInfoFuAddMulti = R"(
    {
            "name": "newSubMulti00%d",
            "label_name": "V_1",
            "comment":"status_merge",
            "type":"before_commit",
            "events":
                [
                    {"type":"modify", "msgTypes":["new object", "old object"]},
                    {"type":"delete", "msgTypes":["new object", "old object"]},
                    {"type":"initial_load", "msgTypes":["new object"]}
                ],
            "is_path":false,
            "retry":true,
            "priority": 1,
            "subs_type":"status_merge"
    })";
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *g_schema1 = NULL;
    char *g_schema3 = NULL;
    GmcDropVertexLabel(g_stmt, "V_1");
    readJanssonFile("./schema_file/NewSubV_1.gmjson", &g_schema1);
    AW_MACRO_ASSERT_NE_INT((void *)NULL, g_schema1);
    ret = GmcCreateVertexLabel(g_stmt, g_schema1, g_tableConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_schema1);

    ret = testSnMallocUserData(&userData1, 400, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubConnect(&conn_sub, &stmt_sub, 1, g_epoll_reg_info, "newSubTreeMulti001", &g_chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char subifoaarr[4096] = {0};
    char labelnamearr[128] = {0};
    char subnamearr[128] = {0};
    SnUserDataT *arrss[1024];
    char udarr[1024] = {0};
    for (int i = 0; i < 1024; i++)
    {
        snprintf(udarr, 128, "userData%d", i);
        SnUserDataT *udarr = NULL;
        int ret = testSnMallocUserData(&udarr, 100, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        arrss[i] = udarr;
    }
    uint32_t subNums = 0;
    ret = testGetSubsNum(&subNums);
    AW_FUN_Log(LOG_INFO, "\nsub exist num is :%d\n", subNums);
    for (int i = 0; i < 1024 - subNums; i++)
    {
        snprintf(subifoaarr, 4096, newSubInfoFuAddMulti, i);
        snprintf(subnamearr, 128, "newSubMulti00%d", i);
        snprintf(subifoaarr, 4096, newSubInfoFuAddMulti, i, i);

        GmcSubConfigT tsub_info;
        tsub_info.subsName = subnamearr;
        tsub_info.configJson = subifoaarr;
        ret = GmcSubscribe(g_stmt, &tsub_info, conn_sub, NewSnCallBack10, arrss[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    pthread_t thr_arr[thrNum];
    void *thr_ret[thrNum];
    for (int i = 0; i < thrNum; i++)
    {
        ret = pthread_create(&thr_arr[i], NULL, simple_table_writeupdatedelete_fun, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testWaitStMgSnRecv(arrss[0], GMC_SUB_EVENT_MODIFY, 0, insertNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitStMgSnRecv(arrss[0], GMC_SUB_EVENT_DELETE, 0, insertNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int y = 0; y < thrNum; y++)
    {
        ret = pthread_join(thr_arr[y], &thr_ret[y]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (int i = 0; i < 1024 - subNums; i++)
    {
        snprintf(labelnamearr, 128, "V_%d", i);
        snprintf(subnamearr, 128, "newSubMulti00%d", i);
        ret = GmcUnSubscribe(g_stmt, subnamearr);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (int i = 0; i < 1024; i++)
    {
        testSnFreeUserData(arrss[i]);
    }
    ret = GmcDropVertexLabel(g_stmt, "V_1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData1);

    ret = testSubDisConnect(conn_sub, stmt_sub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "END\n");
}

// 041. tree表，订阅deleted，写数据，使用hashcluster删除数据时先set字段的值，推送异常
// 【V3单号DTS2023041016367】
TEST_F(NewSubDML_04, SN_034_004_041)
{
    int ret = 0;
    GmcConnT *conn = NULL, *conn_sub = NULL;
    GmcStmtT *stmt = NULL, *stmt_sub = NULL;
    int start_num = 0;
    int end_num = 10000;
    int array_num = 3;
    int vector_num = 3;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    SnUserDataT *userData = NULL;
    ret = testSnMallocUserData(&userData, 100, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *newSubInfoAddMulti = R"(
    {
            "name": "newSubTreeMulti001",
            "label_name": "OP_T0",
            "comment":"status_merge",
            "type":"before_commit",
            "events":
                [
                    {"type":"modify", "msgTypes":["new object", "old object"]},
                    {"type":"delete", "msgTypes":["new object", "old object"]}
                ],
            "is_path":false,
            "retry":true,
            "priority": 1,
            "subs_type":"status_merge"
    })";
    GmcDropVertexLabel(stmt, "OP_T0");
    ret = GmcCreateVertexLabel(stmt, g_treeschema, g_tableConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&conn_sub, &stmt_sub, 1, g_epoll_reg_info, "newsubconn", &g_chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcSubConfigT tsub_info;
    tsub_info.subsName = "newSubTreeMulti001";
    tsub_info.configJson = newSubInfoAddMulti;
    ret = GmcSubscribe(stmt, &tsub_info, conn_sub, NewSnCallBack10, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, "OP_T0");
    uint32_t updateindex = 0;
    GmcNodeT *root = NULL, *T3 = NULL;
    char f14_value[8] = "stoooo";
    for (int i = start_num; i < end_num; i++)
    {
        ret = testGmcPrepareStmtByLabelName(stmt, "OP_T0", GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        uint64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "hashcluster_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_DELETE, 10000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, "newSubTreeMulti001");
    ret = GmcDropVertexLabel(stmt, "OP_T0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);
    
    AW_FUN_Log(LOG_STEP, "END\n");
}

// 042. lpmv4表，状态合并订阅modified和deleted，反复写数据然后truncate，接收推送
// 【V3单号DTS2023041015066】
TEST_F(NewSubDML_04, SN_034_004_042)
{
    AW_FUN_Log(LOG_STEP, "start\n");
    GmcConnT *conn = NULL, *conn_sub = NULL;
    GmcStmtT *stmt = NULL, *stmt_sub = NULL;
    int ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *fileSchema = NULL;
    GmcDropVertexLabel(stmt, "ip4forward");
    readJanssonFile("./schema_file/ip4forward_lpm4.gmjson", &fileSchema);
    AW_MACRO_EXPECT_NE_INT((void *)NULL, fileSchema);
    const char *labelConfig = "{\"max_recordNum\":10000, \"isFastReadUncommitted\":0, \"status_merge_sub\":true}";
    ret = GmcCreateVertexLabel(stmt, fileSchema, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(fileSchema);
    const char *newSubInfoAddMulti = R"(
        {
                "name": "newSubMulti001",
                "label_name": "ip4forward",
                "comment":"status_merge",
                "type":"before_commit",
                "events":
                    [
                        {"type":"modify", "msgTypes":["new object", "old object"]},
                        {"type":"delete", "msgTypes":["new object", "old object"]}
                    ],
                "is_path":false,
                "retry":true,
                "priority": 1,
                "subs_type":"status_merge"
        })";
    GmcSubConfigT tsub_info;
    tsub_info.subsName = "newSubMulti001";
    tsub_info.configJson = newSubInfoAddMulti;
    SnUserDataT *userData = NULL;
    ret = testSubConnect(&conn_sub, &stmt_sub, 1, g_epoll_reg_info, "newSubMulti001", &g_chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSnMallocUserData(&userData, 100, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSubscribe(stmt, &tsub_info, conn_sub, NewSnCallBack10, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int j = 0; j < 5; j++)
    {
        ret = testGmcPrepareStmtByLabelName(stmt, "ip4forward", GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t vr_idValue = 0;
        uint32_t vrf_indexValue = 1;
        uint32_t dest_ip_addrValue = 1;
        uint8_t mask_lenValue = 1;
        for (uint32_t i = 0; i < 100; i++)
        {
            vr_idValue = i % 15;
            ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &vr_idValue, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &vrf_indexValue, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            dest_ip_addrValue = i;
            ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_UINT32, &dest_ip_addrValue, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            mask_lenValue = i % 32;
            ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &mask_lenValue, sizeof(uint8_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcDeleteAllFast(stmt, "ip4forward");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 当前是每一次清零接收
        ret = testWaitStMgSnRecv(userData, GMC_SUB_EVENT_MODIFY, 0, 200);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int ex_nub = 100;
        ret = testWaitStMgSnRecv(userData, GMC_SUB_EVENT_DELETE, 0, ex_nub *2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcUnSubscribe(stmt, "newSubMulti001");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDropVertexLabel(stmt, "ip4forward");
    ret = testSubDisConnect(conn_sub, stmt_sub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);
    AW_FUN_Log(LOG_STEP, "END\n");
}

// 043. 同一张表，先后台truncate，再进行降级；truncate推送异常，日志打印15005
// 【V3单号DTS2023041312516】
TEST_F(NewSubDML_04, SN_034_004_043)
{
    GmcConnT *conn = NULL, *conn_sub = NULL;
    GmcStmtT *stmt = NULL, *stmt_sub = NULL;
    int ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *fileSchema = NULL;
    GmcDropVertexLabel(stmt, "ip4forward");
    readJanssonFile("./schema_file/ip4forward_lpm4.gmjson", &fileSchema);
    AW_MACRO_EXPECT_NE_INT((void *)NULL, fileSchema);
    const char *labelConfig = "{\"max_recordNum\":10000, \"isFastReadUncommitted\":0, \"status_merge_sub\":true}";
    ret = GmcCreateVertexLabel(stmt, fileSchema, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(fileSchema);

    char cmd[512] = {0};
    char *uWay = (char *)"online";
    (void)snprintf(cmd, 512, "%s/gmddl -c alter -t %s -f %s  -u %s -ns %s", g_toolPath, "ip4forward",
                   "./schema_file/ip4forward_lpm4v2.gmjson", uWay, g_testNameSpace);
                   

    AW_FUN_Log(LOG_INFO, "cmd: %s\n", cmd);
    ret = executeCommand(cmd, (char *)"upgrade successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *newSubInfoAddMulti = R"(
        {
                "name": "newSubMulti001",
                "label_name": "ip4forward",
                "comment":"status_merge",
                "type":"before_commit",
                "events":
                    [
                        {"type":"initial_load", "msgTypes":["new object"]},
                        {"type":"modify", "msgTypes":["new object", "old object"]},
                        {"type":"delete", "msgTypes":["new object", "old object"]}
                    ],
                "is_path":false,
                "retry":true,
                "priority": 1,
                "subs_type":"status_merge"
        })";
    GmcSubConfigT tsub_info;
    tsub_info.subsName = "newSubMulti001";
    tsub_info.configJson = newSubInfoAddMulti;
    SnUserDataT *userData = NULL;
    ret = testSubConnect(&conn_sub, &stmt_sub, 1, g_epoll_reg_info, "newSubMulti001", &g_chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSnMallocUserData(&userData, 100, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, "ip4forward", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t vr_idValue = 0;
    uint32_t vrf_indexValue = 1;
    uint32_t dest_ip_addrValue = 1;
    uint8_t mask_lenValue = 1;
    for (uint32_t i = 0; i < 100; i++)
    {
        vr_idValue = i % 15;
        ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &vr_idValue, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &vrf_indexValue, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        dest_ip_addrValue = i;
        ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_UINT32, &dest_ip_addrValue, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        mask_lenValue = i % 32;
        ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &mask_lenValue, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcSubscribe(stmt, &tsub_info, conn_sub, NewSnCallBack10, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char cmd2[512] = {0};
    char *dWay = (char *)"sync"; // 同步降级
    (void)snprintf(cmd2, 512, "%s/gmddl -c alter -t %s  -d %s -ns %s", g_toolPath, "ip4forward",
        dWay, g_testNameSpace);

    AW_FUN_Log(LOG_INFO, "cmd2: %s\n", cmd2);
    ret = executeCommand(cmd2, (char *) "degrade successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDeleteAllFast(stmt, "ip4forward");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_DELETE, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, "newSubMulti001");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDropVertexLabel(stmt, "ip4forward");
    ret = testSubDisConnect(conn_sub, stmt_sub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);
}
// 044. 简单表，表配置状态合并订阅，低版本写入数据，升级后，v1版本主键更新时db core
// 【V3单号DTS2023041215312】
TEST_F(NewSubDML_04, SN_034_004_044)
{
    GmcConnT *conn = NULL, *conn_sub = NULL, *conn_sub1 = NULL;
    GmcStmtT *stmt = NULL, *stmt_sub = NULL, *stmt_sub1 = NULL;
    int ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char *fileSchema = NULL;
    GmcDropVertexLabel(stmt, "ip4forward");
    readJanssonFile("./schema_file/ip4forward_lpm4.gmjson", &fileSchema);
    AW_MACRO_EXPECT_NE_INT((void *)NULL, fileSchema);
    const char *labelConfig = "{\"max_recordNum\":10000, \"isFastReadUncommitted\":0, \"status_merge_sub\":true}";
    ret = GmcCreateVertexLabel(stmt, fileSchema, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(fileSchema);

    const char *newSubInfoAddMulti = R"(
        {
                "name": "newSubMulti001",
                "label_name": "ip4forward",
                "comment":"status_merge",
                "schema_version":0,
                "type":"before_commit",
                "events":
                    [
                        {"type":"initial_load", "msgTypes":["new object"]},
                        {"type":"modify", "msgTypes":["new object", "old object"]},
                        {"type":"delete", "msgTypes":["new object", "old object"]}
                    ],
                "is_path":false,
                "retry":true,
                "priority": 1,
                "subs_type":"status_merge"
        })";
    const char *newSubInfoAddMulti2 = R"(
        {
                "name": "newSubMulti002",
                "label_name": "ip4forward",
                "comment":"status_merge",
                "schema_version":1,
                "type":"before_commit",
                "events":
                    [
                        {"type":"initial_load", "msgTypes":["new object"]},
                        {"type":"modify", "msgTypes":["new object", "old object"]},
                        {"type":"delete", "msgTypes":["new object", "old object"]}
                    ],
                "is_path":false,
                "retry":true,
                "priority": 1,
                "subs_type":"status_merge"
        })";
    GmcSubConfigT tsub_info;
    tsub_info.subsName = "newSubMulti001";
    tsub_info.configJson = newSubInfoAddMulti;

    GmcSubConfigT tsub_info2;
    tsub_info2.subsName = "newSubMulti002";
    tsub_info2.configJson = newSubInfoAddMulti2;

    SnUserDataT *userData = NULL;
    SnUserDataT *userData2 = NULL;
    ret = testSubConnect(&conn_sub, &stmt_sub, 1, g_epoll_reg_info, "newSubMulti001", &g_chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubConnect(&conn_sub1, &stmt_sub1, 1, g_epoll_reg_info, "newSubMulti002", &g_chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSnMallocUserData(&userData, 100, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSnMallocUserData(&userData2, 100, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, "ip4forward", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t vr_idValue = 0;
    uint32_t vrf_indexValue = 1;
    uint32_t dest_ip_addrValue = 1;
    uint8_t mask_lenValue = 1;
    for (uint32_t i = 0; i < 100; i++)
    {
        vr_idValue = i % 15;
        ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &vr_idValue, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &vrf_indexValue, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        dest_ip_addrValue = i;
        ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_UINT32, &dest_ip_addrValue, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        mask_lenValue = i % 32;
        ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &mask_lenValue, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    char cmd[512] = {0};
    char *uWay = (char *)"online";
    (void)snprintf(cmd, 512, "%s/gmddl -c alter -t %s -f %s  -u %s -ns %s", g_toolPath, "ip4forward",
                   "./schema_file/ip4forward_lpm4v2.gmjson", uWay, g_testNameSpace);

    AW_FUN_Log(LOG_INFO, "cmd: %s\n", cmd);
    ret = executeCommand(cmd, (char *)"upgrade successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 此处为什么无法升级第二次？
    char cmd1[512] = {0};
    char *uWay2 = (char *)"online";
    (void)snprintf(cmd1, 512, "%s/gmddl -c alter -t %s -f %s  -u %s -ns %s", g_toolPath, "ip4forward",
                   "./schema_file/ip4forward_lpm4v1.gmjson", uWay2, g_testNameSpace);

    AW_FUN_Log(LOG_INFO, "cmd1: %s\n", cmd1);
    ret = executeCommand(cmd1, (char *)"upgrade successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char cmd2[512] = {0};
    char *dWay = (char *)"sync"; // 同步降级
    (void)snprintf(cmd2, 512, "%s/gmddl -c alter -t %s -v 1 -d %s -ns %s", g_toolPath, "ip4forward",
        dWay, g_testNameSpace);

    AW_FUN_Log(LOG_INFO, "cmd2: %s\n", cmd2);
    ret = executeCommand(cmd2, (char *) "degrade successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSubscribe(stmt, &tsub_info, conn_sub, NewSnCallBack10, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSubscribe(stmt, &tsub_info2, conn_sub1, NewSnCallBack10, userData2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, "ip4forward", GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < 100; i++)
    {
        vr_idValue = i % 15;
        ret = GmcSetIndexKeyName(stmt, "primary_key");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vr_idValue, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrf_indexValue, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        dest_ip_addrValue = i;
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &dest_ip_addrValue, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        mask_lenValue = i % 32;
        ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &mask_lenValue, sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT32, &vr_idValue, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, "ip4forward", 1, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t upValue = 1;
    for (uint32_t i = 0; i < 100; i++)
    {
        vr_idValue = i % 15;
        upValue = i;
        ret = GmcSetIndexKeyName(stmt, "primary_key");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vr_idValue, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrf_indexValue, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        dest_ip_addrValue = i;
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &dest_ip_addrValue, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        mask_lenValue = i % 32;
        ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &mask_lenValue, sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "update_filed", GMC_DATATYPE_UINT32, &upValue, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testWaitStMgSnRecv(userData, GMC_SUB_EVENT_MODIFY,0, 200);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitStMgSnRecv(userData2, GMC_SUB_EVENT_MODIFY,0, 200);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, "newSubMulti001");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, "newSubMulti002");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, "ip4forward");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(conn_sub, stmt_sub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);
    testSnFreeUserData(userData2);
}
