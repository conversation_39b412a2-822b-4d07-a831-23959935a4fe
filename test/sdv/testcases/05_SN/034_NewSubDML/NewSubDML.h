/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2012-2018. All rights reserved.
 Description  : 新订阅-状态合并订阅公共头文件
 Author       : ywx1037054
 Modification :
 Date         : 2023/03/11
**************************************************************************** */
#ifndef NEW_SUB_DML_H
#define NEW_SUB_DML_H
#include <semaphore.h>
#include "t_datacom_lite.h"
// 全局变量区域，集中存放建表涉及资源，尽量减少全局链接资源投放
// 创建vertex表，全量订阅、增量订阅、全量+增量订阅、全量+增量下的条件订阅
// 初步：vertex表结构包含基础的uint32_t,float,string,bytes,fixed类型对上述字段写入并订阅a
#define MAX_NAME_LENGTH 128
#define DMDB_MAX_NAME_LENGTH 128
#define NONE "\033[0m"
#define BLACK "\033[0;30m"
#define L_BLACK "\033[1;30m"
#define RED_N "\033[0;31m"
#define L_RED "\033[1;31m"

#define L_GREEN "\033[1;32m"
#define BROWN "\033[0;33m"
#define YELLOW_N "\033[1;33m"

#define L_BLUE "\033[1;34m"
#define PURPLE "\033[0;35m"
#define L_PURPLE "\033[1;35m"
#define CYAN "\033[0;36m"
#define L_CYAN "\033[1;36m"
#define GRAY "\033[0;37m"
#define WHITE "\033[1;37m"

bool TestIsHpeEnv(void)
{
#ifdef HPE
    AW_FUN_Log(LOG_INFO, "current env is hpe.") return true;
#else
    return false;
#endif
}

typedef void (*TestCrashHandlerFunc)(int signo, siginfo_t *info, void *context);

int32_t g_expectPolicy = 0;
void TestCrashHandler(int signo, siginfo_t *info, void *context)
{
    GmcSignalInfoT dbSignalInfo = {
        .signalNum = signo,
        .reserved = 0,
        .sigInfo = info,
        .signalCon = context,
    };
    GmcSignalOutputDataT output = {0};
    int32_t ret = GmcCrashHandlerHook(&dbSignalInfo, &output);
    if (ret != GMERR_OK) {
        return;
    }
    AW_FUN_Log(LOG_INFO, "single output: policy: %d, rebootCode: %d, userInfo: %s.", output.policy, output.rebootCode,
        output.userInfo);
    EXPECT_EQ(g_expectPolicy, output.policy);
    /* switch (output.policy) {
        case 0:
            (void)raise(signo);
            break;
        case 2:
            AW_FUN_Log(LOG_ERROR, "Reboot due to policy two.");
            (void)raise(signo);
            break;
        case 1:
            AW_FUN_Log(LOG_INFO, "policy one.");
        default:
            break;
    } */
    return;
}

int32_t TestRegisterLinuxSignalProtect(TestCrashHandlerFunc crashHandler)
{
    struct sigaction act;
    act.sa_sigaction = crashHandler;
    act.sa_flags = (int)(SA_SIGINFO | SA_RESETHAND);
    // pssp接管信号范围
    int32_t signalList[] = {SIGHUP, SIGINT, SIGQUIT, SIGILL, SIGABRT, SIGBUS, SIGFPE, SIGSEGV, SIGTERM, SIGXCPU,
        SIGXFSZ, SIGSYS, SIGUSR1, SIGUSR2};
    uint32_t listSize = (uint32_t)(sizeof(signalList) / sizeof(int32_t));
    Status ret = GMERR_OK;
    for (uint32_t i = 0; i < listSize; i++) {
        int32_t signo = signalList[i];
        ret = sigaction(signo, &act, NULL);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "Register signal unsucc, signo: %" PRId32 ".", signo);
            return ret;
        }
    }
    return GMERR_OK;
}

typedef int32_t (*TestDbSignalHookFunc)(GmcSignalInfoT *signalInfo, GmcSignalOutputDataT *output);
typedef int32_t (*TestRegSigHandlerHook)(TestDbSignalHookFunc dbSignalHook);

uint32_t g_registerCount = 0;
int TestTryRegisterSignal(TestCrashHandlerFunc crashHandler)
{
    if (TestIsHpeEnv()) {
        return GMERR_OK;
    }
    int ret = GMERR_OK;
    TestRegSigHandlerHook sigHookFunc = (TestRegSigHandlerHook)dlsym(RTLD_DEFAULT, "DBG_RegSigHandlerHook");
    if (sigHookFunc == NULL) {
        sigHookFunc = (TestRegSigHandlerHook)dlsym(RTLD_DEFAULT, "PSSP_RegSigHandlerHook");
    }
    ret = (sigHookFunc != NULL) ? sigHookFunc(GmcCrashHandlerHook) : TestRegisterLinuxSignalProtect(crashHandler);
    if (ret == GMERR_OK) {
        GmcSignalRegisterNotify();
        g_registerCount++;
        AW_FUN_Log(LOG_INFO, "register times is: %d.", g_registerCount);
    }
    return ret;
}

sem_t g_sem;
const char *g_newSubInfoAdd = R"(
{
        "name": "newSub001",
        "label_name": "V_1",
        "comment":"status_merge",
        "type":"before_commit",
        "events":
            [
                {"type":"modify", "msgTypes":["new object", "old object"]},
                {"type":"delete", "msgTypes":["new object", "old object"]}
            ],
        "is_path":false,
        "retry":true,
        "priority": 1,
        "subs_type":"status_merge"
})";
const char *g_newSubInfoAddWithInitLoad = R"(
{
        "name": "newSub001",
        "label_name": "V_1",
        "comment":"status_merge",
        "type":"before_commit",
        "events":
            [
                {"type":"modify", "msgTypes":["new object", "old object"]},
                {"type":"delete", "msgTypes":["new object", "old object"]},
                {"type":"initial_load", "msgTypes":["new object"]},
                {"type":"initial_load_eof"}
            ],
        "is_path":false,
        "retry":true,
        "priority": 1,
        "subs_type":"status_merge"
})";
const char *g_oldSubInfoAdd = R"(
{
        "name": "oldSub001",
        "label_name": "V_1",
        "comment":"status_merge",
        "type":"before_commit",
        "events":
            [
                {"type":"modify", "msgTypes":["new object", "old object"]},
                {"type":"delete", "msgTypes":["new object", "old object"]}
            ],
        "is_path":false,
        "retry":true,
        "priority": 1
})";
const char *g_newSubInfoFu = R"(
{
        "name": "newSub002",
        "label_name": "V_1",
        "comment":"status_merge",
        "type":"before_commit",
        "events":
            [
                {"type":"initial_load", "msgTypes":["new object"]},
                {"type":"initial_load_eof"}
            ],
        "is_path":false,
        "retry":true,
        "priority": 1,
        "subs_type":"status_merge"
})";
const char *g_newSubInfoFu77 = R"(
{
        "name": "newSub077",
        "label_name": "V_1",
        "comment":"status_merge",
        "type":"before_commit",
        "events":
            [
                {"type":"initial_load", "msgTypes":["new object"]},
                {"type":"initial_load_eof"}
            ],
        "is_path":false,
        "retry":true,
        "priority": 1,
        "subs_type":"status_merge"
})";
const char *g_newSubInfoFuAdd = R"(
{
        "name": "newSub003",
        "label_name": "V_1",
        "comment":"status_merge",
        "type":"before_commit",
        "events":
            [
                {"type":"initial_load", "msgTypes":["new object"]},
                {"type":"initial_load_eof"},
                {"type":"modify", "msgTypes":["new object", "old object"]},
                {"type":"delete", "msgTypes":["new object", "old object"]}
            ],
        "is_path":false,
        "retry":true,
        "priority": 1,
        "subs_type":"status_merge"
})";
const char *g_newSubInfoFuAddConAnd = R"(
{
        "name": "newSub004",
        "label_name": "V_1",
        "comment":"status_merge",
        "type":"before_commit",
        "events":
            [
                {"type":"initial_load", "msgTypes":["new object"]},
                {"type":"initial_load_eof"},
                {"type":"modify", "msgTypes":["new object", "old object"]},
                {"type":"delete", "msgTypes":["new object", "old object"]}
            ],
        "is_path":false,
        "retry":true,
        "priority": 1,
        "subs_type":"status_merge",
        "constraint":
            {
                "operator_type":"and",
                "conditions":
                    [
                        {
                            "property":"F0",
                            "value":5
                        },
                        {
                            "property":"F5",
                            "value":"string_5"
                        }
                    ]
            }
})";
const char *g_newSubInfoFuAddConOr = R"(
{
        "name": "newSub005",
        "label_name": "V_1",
        "comment":"status_merge",
        "type":"before_commit",
        "events":
            [
                {"type":"initial_load", "msgTypes":["new object"]},
                {"type":"initial_load_eof"},
                {"type":"modify", "msgTypes":["new object", "old object"]},
                {"type":"delete", "msgTypes":["new object", "old object"]}
            ],
        "is_path":false,
        "retry":true,
        "priority": 1,
        "subs_type":"status_merge",
        "constraint":
            {
                "operator_type":"or",
                "conditions":
                    [
                        {
                            "property":"F4",
                            "value":5.00
                        }
                    ]
            }
})";

char *g_schema = NULL, *g_subInfo = NULL;
const char *g_tableConfig2 = "{\"max_record_count\" : 10000000, \"isFastReadUncommitted\":false,"
                             "\"status_merge_sub\":true}";
const char *g_tableConfig = "{\"max_record_count\" : 10000000, \"isFastReadUncommitted\":true,"
                            "\"status_merge_sub\":true}";
int g_chanRingLen = 256;
bool isNull;
uint32_t labelNameLen = 0;
bool blocking = false;
/*******************************************************************************
  回调函数:需要新老订阅各1个
  创建资源接口：批量准备接口
  DML接口：函数驱动表包含单插、批插、单更、批更、单删、批删、读
  DDL接口：drop\truncate表不使用整合接口
  保护线程接口: 发送信号、信号处理函数接口a
*******************************************************************************/
int32_t queryFieldValueAndCompare(
    GmcStmtT *stmt, const char *fieldName, void *readValueOut, unsigned int *getValueSizeOut)
{
    int ret = 0;
    ret = GmcGetVertexPropertySizeByName(stmt, fieldName, getValueSizeOut);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetVertexPropertyByName(stmt, fieldName, readValueOut, *getValueSizeOut, &isNull);
    EXPECT_EQ(GMERR_OK, ret);

    return ret;
}
int32_t QueryVertexPropertyValue(GmcDataTypeE ttype, void *insertValue, void *queryValue, unsigned int size = 0)
{
    int32_t statusRet = 0;
    if (insertValue == NULL || queryValue == NULL) {
        return 1;
    }
    switch (ttype) {
        case GMC_DATATYPE_CHAR:
            printf("insertValue:%c queryValue:%c\n", *(char *)insertValue, *(char *)queryValue);
            break;
        case GMC_DATATYPE_UCHAR:
            printf("insertValue:%c queryValue:%c\n", *(unsigned char *)insertValue, *(unsigned char *)queryValue);
            break;
        case GMC_DATATYPE_INT8:

            printf("insertValue:%d queryValue:%d\n", *(int8_t *)insertValue, *(int8_t *)queryValue);

            break;
        case GMC_DATATYPE_UINT8:

            printf("insertValue:%u queryValue:%u\n", *(uint8_t *)insertValue, *(uint8_t *)queryValue);

            break;
        case GMC_DATATYPE_INT16:

            printf("insertValue:%d queryValue:%d\n", *(int16_t *)insertValue, *(int16_t *)queryValue);

            break;
        case GMC_DATATYPE_UINT16:

            printf("insertValue:%u queryValue:%u\n", *(uint16_t *)insertValue, *(uint16_t *)queryValue);

            break;
        case GMC_DATATYPE_INT32:

            printf("insertValue:%d queryValue:%d\n", *(int32_t *)insertValue, *(int32_t *)queryValue);

            break;
        case GMC_DATATYPE_UINT32:

            printf("insertValue:%u queryValue:%u\n", *(uint32_t *)insertValue, *(uint32_t *)queryValue);

            break;
        case GMC_DATATYPE_BOOL:
            if ((*(bool *)insertValue) == (*(bool *)queryValue)) {
                statusRet = 0;
            } else {
                statusRet = 10;
            }
            break;
        case GMC_DATATYPE_INT64:
            printf("insertValue:%ld queryValue:%ld\n", *(int64_t *)insertValue, *(int64_t *)queryValue);

            break;
        case GMC_DATATYPE_UINT64:

            printf("insertValue:%lu queryValue:%lu\n", *(uint64_t *)insertValue, *(uint64_t *)queryValue);

            break;
        case GMC_DATATYPE_FLOAT:

            printf("insertValue:%lf queryValue:%lf\n", *(float *)insertValue, *(float *)queryValue);

            break;
        case GMC_DATATYPE_DOUBLE:

            printf("insertValue:%lf queryValue:%lf\n", *(double *)insertValue, *(double *)queryValue);
            break;
        case GMC_DATATYPE_TIME:

            printf("insertValue:%lu queryValue:%lu\n", *(uint64_t *)insertValue, *(uint64_t *)queryValue);

            break;
        case GMC_DATATYPE_BITFIELD8:

            printf("insertValue:%u queryValue:%u\n", *(uint8_t *)insertValue, *(uint8_t *)queryValue);

            break;
        case GMC_DATATYPE_BITFIELD16:

            printf("insertValue:%u queryValue:%u\n", *(uint16_t *)insertValue, *(uint16_t *)queryValue);

            break;
        case GMC_DATATYPE_BITFIELD32:

            printf("insertValue:%u queryValue:%u\n", *(uint32_t *)insertValue, *(uint32_t *)queryValue);

            break;
        case GMC_DATATYPE_BITFIELD64:

            printf("insertValue:%u queryValue:%u\n", *(uint32_t *)insertValue, *(uint32_t *)queryValue);

        case GMC_DATATYPE_PARTITION:

            printf("insertValue:%u queryValue:%u\n", *(uint8_t *)insertValue, *(uint8_t *)queryValue);

        case GMC_DATATYPE_STRING:

            printf("insertValue: %s, queryValue: %s\n", (char *)insertValue, (char *)queryValue);

            break;
        case GMC_DATATYPE_BYTES:

            printf("insertValue:%s queryValue:%s size: %d\n", (char *)insertValue, (char *)queryValue, size);

            break;
        case GMC_DATATYPE_FIXED:

            printf("insertValue:%s queryValue:%s\n", (char *)insertValue, (char *)queryValue);

            break;
        case GMC_DATATYPE_BITMAP:

            printf("insertValue:%s queryValue:%s\n", (char *)insertValue, (char *)queryValue);

            break;
        default:
            printf("the type %d is not existence!\n", ttype);
            statusRet = 100;
            break;
    }
    return statusRet;
}
int queryProperty(GmcStmtT *stmt, const char *propertyName, GmcDataTypeE ttype, void *insertValue)
{
    int statusRet = 0;
    unsigned int sizeValue = 0;
    bool isNull = 0;
    statusRet = GmcGetVertexPropertySizeByName(stmt, propertyName, &sizeValue);
    if (statusRet != GMERR_OK) {
        printf("get property size fail: statusRet: %d\n", statusRet);
        return statusRet;
    }
    if (sizeValue == 0 && insertValue == NULL) {
        return 0;
    }
    char *pValue = (char *)malloc(sizeValue);
    if (pValue == NULL) {
        printf("error:get property size=0!!!\n");
        return 1;
    }
    EXPECT_NE((void *)NULL, pValue);
    statusRet = GmcGetVertexPropertyByName(stmt, propertyName, pValue, sizeValue, &isNull);
    if (statusRet != GMERR_OK) {
        printf("get property fail: statusRet: %d\n", statusRet);
        free(pValue);
        return statusRet;
    }
    if (!isNull) {
        statusRet = QueryVertexPropertyValue(ttype, insertValue, pValue, sizeValue);
    } else {
        if (insertValue == NULL) {
            statusRet = 0;
        } else {
            printf("query value==NULL!!!\n");
            statusRet = 1;
        }
    }
    free(pValue);
    return statusRet;
}

int queryAllValue(GmcStmtT *subStmt, uint32_t f0, char f2, unsigned char f3, float f4, char f5[], char f6[], char f7[])
{
    int ret = queryProperty(subStmt, "F0", GMC_DATATYPE_UINT32, &f0);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryProperty(subStmt, "F2", GMC_DATATYPE_CHAR, &f2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryProperty(subStmt, "F3", GMC_DATATYPE_UCHAR, &f3);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryProperty(subStmt, "F4", GMC_DATATYPE_FLOAT, &f4);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryProperty(subStmt, "F5", GMC_DATATYPE_STRING, f5);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryProperty(subStmt, "F6", GMC_DATATYPE_BYTES, f6);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryProperty(subStmt, "F7", GMC_DATATYPE_FIXED, f7);
    EXPECT_EQ(GMERR_OK, ret);
    return ret;
}
int checkLastValue(GmcStmtT *subStmt, uint32_t f0, char f2, unsigned char f3, float f4, char f5[], char f6[], char f7[])
{
    int ret = queryPropertyAndCompare(subStmt, "F0", GMC_DATATYPE_UINT32, &f0);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(subStmt, "F2", GMC_DATATYPE_CHAR, &f2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(subStmt, "F3", GMC_DATATYPE_UCHAR, &f3);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(subStmt, "F4", GMC_DATATYPE_FLOAT, &f4);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(subStmt, "F5", GMC_DATATYPE_STRING, f5);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(subStmt, "F6", GMC_DATATYPE_BYTES, f6);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(subStmt, "F7", GMC_DATATYPE_FIXED, f7);
    EXPECT_EQ(GMERR_OK, ret);
    return ret;
}

void OldSnCallBack(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret;
    int index, i;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;
    void *keyValue = 0;
    char keyName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;

    bool eof = false;
    while (!eof) {
        if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
            return;
        }
        ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }

        for (i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(strlen(labelName), labelNameLen);

            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    if (index == 99) {
                        checkLastValue(subStmt, 99, 'e', 'e', 99.00, (char *)"end", (char *)"end", (char *)"end");
                    }

                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_NO_DATA, ret);
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_NO_DATA, ret);

                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    if (index == 99) {
                        checkLastValue(subStmt, 99, 'e', 'e', 99.00, (char *)"end", (char *)"end", (char *)"end");
                    }
                    break;
                }
                case GMC_SUB_EVENT_UPDATE: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    if (index == 99) {
                        checkLastValue(subStmt, 99, 'e', 'e', 99.00, (char *)"end", (char *)"end", (char *)"end");
                    }
                    break;
                }
                case GMC_SUB_EVENT_REPLACE: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    if (index == 99) {
                        checkLastValue(subStmt, 99, 'e', 'e', 99.00, (char *)"end", (char *)"end", (char *)"end");
                    }
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    if (index == 99) {
                        checkLastValue(subStmt, 99, 'e', 'e', 99.00, (char *)"end", (char *)"end", (char *)"end");
                    }
                    break;
                }
                case GMC_SUB_EVENT_INITIAL_LOAD: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    if (index == 99) {
                        checkLastValue(subStmt, 99, 'e', 'e', 99.00, (char *)"end", (char *)"end", (char *)"end");
                    }
                    break;
                }
                case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                    break;
                }
                default: {
                    break;
                }
            }
            break;
        }
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
            default: {
                printf("default: eventType\r\n");
                break;
            }
        }
    }
}
void OldSnCallBack2(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret;
    int index, i;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;
    void *keyValue = 0;
    char keyName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;
    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }
        sleep(3);
        for (i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            printf("[INFO] vrtxLabelIdx : %d, labelName : %s\r\n", i, labelName);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(strlen(labelName), labelNameLen);
            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    printf("[NEW OBJECT] GMC_SUB_EVENT_INSERT \n");
                    if (index == 99) {
                        checkLastValue(subStmt, 99, 'e', 'e', 99.00, (char *)"end", (char *)"end", (char *)"end");
                    }
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_NO_DATA, ret);
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_NO_DATA, ret);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    printf("[OLD OBJECT] GMC_SUB_EVENT_DELETE \n");
                    if (index == 99) {
                        checkLastValue(subStmt, 99, 'e', 'e', 99.00, (char *)"end", (char *)"end", (char *)"end");
                    }
                    break;
                }
                case GMC_SUB_EVENT_UPDATE: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    printf("[OLD OBJECT] GMC_SUB_EVENT_UPDATE \n");
                    if (index == 99) {
                        checkLastValue(subStmt, 99, 'e', 'e', 99.00, (char *)"end", (char *)"end", (char *)"end");
                    }
                    break;
                }
                case GMC_SUB_EVENT_REPLACE: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    if (index == 99) {
                        checkLastValue(subStmt, 99, 'e', 'e', 99.00, (char *)"end", (char *)"end", (char *)"end");
                    }
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    if (index == 99) {
                        checkLastValue(subStmt, 99, 'e', 'e', 99.00, (char *)"end", (char *)"end", (char *)"end");
                    }
                    break;
                }
                case GMC_SUB_EVENT_INITIAL_LOAD: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    printf("[OBJECT] GMC_SUB_EVENT_INITIAL_LOAD \n");
                    if (index == 99) {
                        checkLastValue(subStmt, 99, 'e', 'e', 99.00, (char *)"end", (char *)"end", (char *)"end");
                    }
                    break;
                }
                case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                    printf("\n[EOF] GMC_SUB_EVENT_INITIAL_LOAD_EOF\n");
                    break;
                }
                default: {
                    printf("default: invalid eventType\r\n");
                    break;
                }
            }
            break;
        }
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
            default: {
                printf("default: eventType\r\n");
                break;
            }
        }
    }
}

void NewSnCallBackTree(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int subNum = 0;
    int subPushModify = 0;
    int subInital = 0;
    int subPushDel = 0;
    int index = 0;
    char labelName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;
    int ret = 0;
    bool eof = false;
    uint32_t getValueSizeOut;
    char readValueOut[128];

    /* 多表情景下不好保证推送得到的表顺序是恒定的
     */
    for (int i = 0; i < info->labelCount; i++) {
        memset(labelName, 0, sizeof(labelName));
        labelNameLen = MAX_NAME_LENGTH;
        if (info->eventType != GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(strlen(labelName), labelNameLen);
        }
        switch (info->eventType) {
            case GMC_SUB_EVENT_MODIFY: {
                ret = GmcFetch(subStmt, &eof);
                EXPECT_EQ(GMERR_OK, ret);
                if (ret != GMERR_OK || eof == true) {
                    break;
                }
                ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                EXPECT_EQ(GMERR_OK, ret);
                index = ((int *)user_data->new_value)[user_data->subIndex];

                if (index == 10) {
                    checkLastValue(subStmt, 99, 'e', 'e', 99.00, (char *)"end", (char *)"end", (char *)"end");
                }
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                // check old value
                ret = GmcFetch(subStmt, &eof);
                EXPECT_EQ(GMERR_OK, ret);
                if (ret != GMERR_OK || eof == true) {
                    break;
                }
                ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                EXPECT_EQ(GMERR_OK, ret);
                index = ((int *)user_data->old_value)[user_data->subIndex];
                if (index == 99) {
                    checkLastValue(subStmt, 99, 'e', 'e', 99.00, (char *)"end", (char *)"end", (char *)"end");
                }
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                ret = GmcFetch(subStmt, &eof);
                EXPECT_EQ(GMERR_OK, ret);
                if (ret != GMERR_OK || eof == true) {
                    break;
                }
                ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                EXPECT_EQ(GMERR_OK, ret);
                index = ((int *)user_data->new_value)[user_data->subIndex];
                if (index == 99) {
                    checkLastValue(subStmt, 99, 'e', 'e', 99.00, (char *)"end", (char *)"end", (char *)"end");
                }
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                printf("default: invalid EOF\r\n");
                ;
            }
            default: {
                printf("default: invalid eventType\r\n");
                break;
            }
        }
    }
    switch (info->eventType) {
        case GMC_SUB_EVENT_MODIFY: {
            user_data->insertNum++;
            break;
        }
        case GMC_SUB_EVENT_DELETE: {
            user_data->deleteNum++;
            break;
        }
        case GMC_SUB_EVENT_INITIAL_LOAD: {
            user_data->scanNum++;
            break;
        }
        case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
            user_data->scanEofNum++;
            break;
        }
        default: {
            printf("default: eventType\r\n");
            break;
        }
    }
}

void NewSnCallBackDelay(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int subNum = 0;
    int subPushModify = 0;
    int subInital = 0;
    int subPushDel = 0;
    int index = 0;
    char labelName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;
    int ret = 0;
    bool eof = false;
    uint32_t getValueSizeOut;
    char readValueOut[128];

    sleep(1);
    /* 多表情景下不好保证推送得到的表顺序是恒定的
     */
    for (int i = 0; i < info->labelCount; i++) {
        memset(labelName, 0, sizeof(labelName));
        labelNameLen = MAX_NAME_LENGTH;
        if (info->eventType != GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(strlen(labelName), labelNameLen);
        }
        switch (info->eventType) {
            case GMC_SUB_EVENT_MODIFY: {
                ret = GmcFetch(subStmt, &eof);
                EXPECT_EQ(GMERR_OK, ret);
                if (ret != GMERR_OK || eof == true) {
                    break;
                }
                ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                EXPECT_EQ(GMERR_OK, ret);

                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                // check old value
                ret = GmcFetch(subStmt, &eof);
                EXPECT_EQ(GMERR_OK, ret);
                if (ret != GMERR_OK || eof == true) {
                    break;
                }
                ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                EXPECT_EQ(GMERR_OK, ret);

                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                ret = GmcFetch(subStmt, &eof);
                EXPECT_EQ(GMERR_OK, ret);
                if (ret != GMERR_OK || eof == true) {
                    break;
                }
                ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                EXPECT_EQ(GMERR_OK, ret);

                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                ;
            }
            default: {
                printf("default: invalid eventType\r\n");
                break;
            }
        }
    }
    switch (info->eventType) {
        case GMC_SUB_EVENT_MODIFY: {
            user_data->insertNum++;
            break;
        }
        case GMC_SUB_EVENT_DELETE: {
            user_data->deleteNum++;
            break;
        }
        case GMC_SUB_EVENT_INITIAL_LOAD: {
            user_data->scanNum++;
            break;
        }
        case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
            user_data->scanEofNum++;
            break;
        }
        default: {
            printf("default: eventType\r\n");
            break;
        }
    }
}
void NewSnCallBackDelay2(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int subNum = 0;
    int subPushModify = 0;
    int subInital = 0;
    int subPushDel = 0;
    int index = 0;
    char labelName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;
    int ret = 0;
    bool eof = false;
    uint32_t getValueSizeOut;
    char readValueOut[128];

    sleep(10);
    /* 多表情景下不好保证推送得到的表顺序是恒定的
     */
    for (int i = 0; i < info->labelCount; i++) {
        memset(labelName, 0, sizeof(labelName));
        labelNameLen = MAX_NAME_LENGTH;
        if (info->eventType != GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(strlen(labelName), labelNameLen);
        }
        switch (info->eventType) {
            case GMC_SUB_EVENT_MODIFY: {
                // check new value
                ret = GmcFetch(subStmt, &eof);
                EXPECT_EQ(GMERR_OK, ret);
                if (ret != GMERR_OK || eof == true) {
                    break;
                }
                ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                EXPECT_EQ(GMERR_OK, ret);
                index = ((int *)user_data->new_value)[user_data->subIndex];
                if (index == 99) {
                    checkLastValue(subStmt, 99, 'e', 'e', 99.00, (char *)"end", (char *)"end", (char *)"end");
                }
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                // check old value
                ret = GmcFetch(subStmt, &eof);
                EXPECT_EQ(GMERR_OK, ret);
                if (ret != GMERR_OK || eof == true) {
                    break;
                }
                ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                EXPECT_EQ(GMERR_OK, ret);
                index = ((int *)user_data->old_value)[user_data->subIndex];
                if (index == 99) {
                    checkLastValue(subStmt, 99, 'e', 'e', 99.00, (char *)"end", (char *)"end", (char *)"end");
                }
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                // check new value
                ret = GmcFetch(subStmt, &eof);
                EXPECT_EQ(GMERR_OK, ret);
                if (ret != GMERR_OK || eof == true) {
                    break;
                }
                ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                EXPECT_EQ(GMERR_OK, ret);
                index = ((int *)user_data->new_value)[user_data->subIndex];
                if (index == 99) {
                    checkLastValue(subStmt, 99, 'e', 'e', 99.00, (char *)"end", (char *)"end", (char *)"end");
                }
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                ;
            }
            default: {
                printf("default: invalid eventType\r\n");
                break;
            }
        }
    }
    switch (info->eventType) {
        case GMC_SUB_EVENT_MODIFY: {
            user_data->insertNum++;
            break;
        }
        case GMC_SUB_EVENT_DELETE: {
            user_data->deleteNum++;
            break;
        }
        case GMC_SUB_EVENT_INITIAL_LOAD: {
            user_data->scanNum++;
            break;
        }
        case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
            user_data->scanEofNum++;
            break;
        }
        default: {
            printf("default: eventType\r\n");
            break;
        }
    }
}

int batchPrepare(GmcConnT *conn, GmcBatchT **batch)
{
    GmcBatchOptionT batchOption;
    int ret = GmcBatchOptionInit(&batchOption);
    RETURN_IFERR(ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    RETURN_IFERR(ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    RETURN_IFERR(ret);
    ret = GmcBatchPrepare(conn, &batchOption, batch);
    RETURN_IFERR(ret);
    return ret;
}
struct DataTypeE {
    uint32_t f0;
    char f2;
    unsigned char f3;
    float f4;
    char *f5;
    char *f6;
    char *f7;
} g_ezdata;
typedef int (*OperFuncHandlePtr)(
    GmcConnT *conn, GmcStmtT *stmt, DataTypeE value, int dataNum, int initail, SnUserDataT *userData);

typedef struct {
    const char *desc;
    OperFuncHandlePtr phandle;
} OperType;
// 操作类型
typedef enum {
    SINGLE_INSERT,
    SINGLE_STRUCT_INSERT,
    BATCH_INSERT,
    BATCH_STRUCT_INSERT,
    SINGLE_ERROR_WRITE,
    BATCH_ERROR_WRITE,
    SINGLE_UPDATE,
    BATCH_UPDATE,
    SINGLE_DELETE,
    BATCH_DELETE,
    READ,
    STRUCT_READ,
    SINGLE_REPLACE,
    BATCH_REPLACE,
    SINGLE_MERGE,
    BATCH_MERGE
} OperTypeE;

/*--------------------------------------set value------------------------------------*/
void setPkValue(GmcStmtT *stmt, uint32_t f0)
{
    int ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void setOtherValue(GmcStmtT *stmt, DataTypeE ezdata)
{
    int ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_CHAR, &ezdata.f2, sizeof(char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UCHAR, &ezdata.f3, sizeof(unsigned char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_FLOAT, &ezdata.f4, sizeof(float));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_STRING, ezdata.f5, strlen(ezdata.f5));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_BYTES, ezdata.f6, strlen(ezdata.f6));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_FIXED, ezdata.f7, strlen(ezdata.f7));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*--------------------------------------write table------------------------------------*/
// 单写
int singleWriteTable(
    GmcConnT *conn, GmcStmtT *stmt, DataTypeE ezdata, int dataNum, int initail, SnUserDataT *userData = NULL)
{
    int ret = 0;
    char f5[29] = "string";
    char f6[29] = "bytes";
    char f7[29] = "fix";

    for (int i = 0; i < dataNum; i++) {
        // set value arm32环境会出现翻转
        (void)snprintf(f5, 28, "string_%d", i + initail);
        (void)snprintf(f6, 28, "bytes_%d", i + initail);
        (void)snprintf(f7, 28, "fixed_%d", (i + initail) % 9);
        ezdata.f0 = i + initail;
        ezdata.f2 = i + initail;
        ezdata.f3 = i + initail;
        ezdata.f4 = i + initail;
        ezdata.f5 = (char *)f5;
        ezdata.f6 = (char *)f6;
        ezdata.f7 = (char *)f7;

        if (userData != NULL) {
            StmgSubCtxT *stmgCtx = userData->stmgSubCtx;
            // 用一个连续且唯一的int类型字段值，来作为来判断是否推送成功的依据,此处f0字段连续且唯一
            stmgCtx[i + initail].index = ezdata.f0;
            stmgCtx[i + initail].diffListIndex = initail;
            stmgCtx[i + initail].updateAddVal = 0;
            stmgCtx[i + initail].isEndStatus = true;
            stmgCtx[i + initail].opType = TEST_OP_INSERT;
        }

        setPkValue(stmt, ezdata.f0);
        setOtherValue(stmt, ezdata);
        ret = GmcExecute(stmt);
        // 不能直接改成OK，sdv0927构建结果SN_034_001_035回滚后不继续执行
        if (ret == GMERR_COMMON_STREAM_OVERLOAD || ret == GMERR_TRANSACTION_ROLLBACK) {
            return ret;
        }
        if (ret != 0) {
            printf("\n NO.%d: \n", i);
            break;
        }
    }
    return ret;
}

// 结构化单写
int structWriteTable(
    GmcConnT *conn, GmcStmtT *stmt, DataTypeE ezdata, int dataNum, int initail, SnUserDataT *userData = NULL)
{
    int ret = 0;
    AW_FUN_Log(LOG_INFO, "[structWriteTable]TOF");
    return ret;
}

// 批写
int batchWriteTable(
    GmcConnT *conn, GmcStmtT *stmt, DataTypeE ezdata, int dataNum, int initail, SnUserDataT *userData = NULL)
{
    int ret = 0;
    // 批写
    char f5[29] = "string";
    char f6[29] = "bytes";
    char f7[29] = "fix";
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < dataNum; i++) {
        // arm32环境会出现翻转
        (void)snprintf(f5, 28, "string_%d", i + initail);
        (void)snprintf(f6, 28, "bytes_%d", i + initail);
        (void)snprintf(f7, 28, "fixed_%d", (i + initail) % 9);
        ezdata.f0 = i + initail;
        ezdata.f2 = i + initail;
        ezdata.f3 = i + initail;
        ezdata.f4 = i + initail;
        ezdata.f5 = (char *)f5;
        ezdata.f6 = (char *)f6;
        ezdata.f7 = (char *)f7;

        if (userData != NULL) {
            StmgSubCtxT *stmgCtx = userData->stmgSubCtx;
            // 用一个连续且唯一的int类型字段值，来作为来判断是否推送成功的依据,此处f0字段连续且唯一
            stmgCtx[i + initail].index = ezdata.f0;
            stmgCtx[i + initail].diffListIndex = initail;
            stmgCtx[i + initail].updateAddVal = 0;
            stmgCtx[i + initail].isEndStatus = true;
            stmgCtx[i + initail].opType = TEST_OP_INSERT;
        }

        setPkValue(stmt, ezdata.f0);
        setOtherValue(stmt, ezdata);
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    return ret;
}

// 结构化批写
int structBatchWriteTable(
    GmcConnT *conn, GmcStmtT *stmt, DataTypeE ezdata, int dataNum, int initail, SnUserDataT *userData = NULL)
{
    int ret = 0;
    AW_FUN_Log(LOG_INFO, "[structBatchWriteTable]TOF");
    return ret;
}

/*-------------------------------------update table------------------------------------*/
// 更新
int g_addVal = 0;
int singleUpdateTable(
    GmcConnT *conn, GmcStmtT *stmt, DataTypeE ezdata, int dataNum, int initail, SnUserDataT *userData = NULL)
{
    int ret = 0;
    char f5[29] = "string";
    char f6[29] = "bytes";
    char f7[29] = "fix";
    uint32_t f0_u = 0;
    for (int i = 0; i < dataNum; i++) {
        // set value
        (void)snprintf(f5, 28, "string_%d", i + initail + g_addVal);
        (void)snprintf(f6, 28, "bytes_%d", i + initail + g_addVal);
        (void)snprintf(f7, 28, "fixed_%d", (i + initail + g_addVal) % 9);
        f0_u = i + initail;

        if (userData != NULL) {
            StmgSubCtxT *stmgCtx = userData->stmgSubCtx;
            // 用一个连续且唯一的int类型字段值，来作为来判断是否推送成功的依据,此处f0字段连续且唯一
            stmgCtx[i + initail].index = f0_u;
            stmgCtx[i + initail].diffListIndex = initail;
            stmgCtx[i + initail].updateAddVal = g_addVal;
            stmgCtx[i + initail].isEndStatus = true;
            stmgCtx[i + initail].opType = TEST_OP_UPDATE;
        }

        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f0_u, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "pk");
        EXPECT_EQ(GMERR_OK, ret);
        ezdata.f2 = i + initail + g_addVal;
        ezdata.f3 = i + initail + g_addVal;
        ezdata.f4 = i + initail + g_addVal;
        ezdata.f5 = (char *)f5;
        ezdata.f6 = (char *)f6;
        ezdata.f7 = (char *)f7;
        setOtherValue(stmt, ezdata);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}
// 批量更新
int batchUpdateTable(
    GmcConnT *conn, GmcStmtT *stmt, DataTypeE ezdata, int dataNum, int initail, SnUserDataT *userData = NULL)
{
    int ret = 0;
    // 批写
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char f5[29] = "string";
    char f6[29] = "bytes";
    char f7[29] = "fix";
    uint32_t f0_u = 0;
    for (int i = 0; i < dataNum; i++) {
        // set value
        f0_u = i + initail;

        if (userData != NULL) {
            StmgSubCtxT *stmgCtx = userData->stmgSubCtx;
            // 用一个连续且唯一的int类型字段值，来作为来判断是否推送成功的依据,此处f0字段连续且唯一
            stmgCtx[i + initail].index = f0_u;
            stmgCtx[i + initail].diffListIndex = initail;
            stmgCtx[i + initail].updateAddVal = g_addVal;
            stmgCtx[i + initail].isEndStatus = true;
            stmgCtx[i + initail].opType = TEST_OP_UPDATE;
        }

        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f0_u, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "pk");
        (void)snprintf(f5, 28, "string_%d", i + initail + g_addVal);
        (void)snprintf(f6, 28, "bytes_%d", i + initail + g_addVal);
        (void)snprintf(f7, 28, "fixed_%d", (i + initail + g_addVal) % 9);
        ezdata.f2 = i + initail + g_addVal;
        ezdata.f3 = i + initail + g_addVal;
        ezdata.f4 = i + initail + g_addVal;
        ezdata.f5 = (char *)f5;
        ezdata.f6 = (char *)f6;
        ezdata.f7 = (char *)f7;
        setOtherValue(stmt, ezdata);

        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    return ret;
}

// replace
int singleReplaceTable(
    GmcConnT *conn, GmcStmtT *stmt, DataTypeE ezdata, int dataNum, int initail, SnUserDataT *userData = NULL)
{
    int ret = 0;
    char f5[29] = "string";
    char f6[29] = "bytes";
    char f7[29] = "fix";
    uint32_t f0_u = 0;
    for (int i = 0; i < dataNum; i++) {
        // set value
        f0_u = i + initail;
        if (userData != NULL) {
            StmgSubCtxT *stmgCtx = userData->stmgSubCtx;
            // 用一个连续且唯一的int类型字段值，来作为来判断是否推送成功的依据,此处f0字段连续且唯一
            stmgCtx[i + initail].index = f0_u;
            stmgCtx[i + initail].diffListIndex = initail;
            stmgCtx[i + initail].updateAddVal = g_addVal;
            stmgCtx[i + initail].isEndStatus = true;
            stmgCtx[i + initail].opType = TEST_OP_INSERT;
        }

        (void)snprintf(f5, 28, "string_%d", i + initail + g_addVal);
        (void)snprintf(f6, 28, "bytes_%d", i + initail + g_addVal);
        (void)snprintf(f7, 28, "fixed_%d", (i + initail + g_addVal) % 9);
        ezdata.f0 = i + initail;
        ezdata.f2 = i + initail + g_addVal;
        ezdata.f3 = i + initail + g_addVal;
        ezdata.f4 = i + initail + g_addVal;
        ezdata.f5 = (char *)f5;
        ezdata.f6 = (char *)f6;
        ezdata.f7 = (char *)f7;
        setPkValue(stmt, ezdata.f0);
        setOtherValue(stmt, ezdata);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}
// 批量replace
int batchReplaceTable(
    GmcConnT *conn, GmcStmtT *stmt, DataTypeE ezdata, int dataNum, int initail, SnUserDataT *userData = NULL)
{
    int ret = 0;
    // 批写
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char f5[29] = "string";
    char f6[29] = "bytes";
    char f7[29] = "fix";
    uint32_t f0_u = 0;
    for (int i = 0; i < dataNum; i++) {
        // set value
        f0_u = i + initail;
        if (userData != NULL) {
            StmgSubCtxT *stmgCtx = userData->stmgSubCtx;
            // 用一个连续且唯一的int类型字段值，来作为来判断是否推送成功的依据,此处f0字段连续且唯一
            stmgCtx[i + initail].index = f0_u;
            stmgCtx[i + initail].diffListIndex = initail;
            stmgCtx[i + initail].updateAddVal = g_addVal;
            stmgCtx[i + initail].isEndStatus = true;
            stmgCtx[i + initail].opType = TEST_OP_INSERT;
        }

        (void)snprintf(f5, 28, "string_%d", i + initail + g_addVal);
        (void)snprintf(f6, 28, "bytes_%d", i + initail + g_addVal);
        (void)snprintf(f7, 28, "fixed_%d", (i + initail + g_addVal) % 9);
        ezdata.f0 = i + initail;
        ezdata.f2 = i + initail + g_addVal;
        ezdata.f3 = i + initail + g_addVal;
        ezdata.f4 = i + initail + g_addVal;
        ezdata.f5 = (char *)f5;
        ezdata.f6 = (char *)f6;
        ezdata.f7 = (char *)f7;

        setPkValue(stmt, ezdata.f0);
        setOtherValue(stmt, ezdata);
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    return ret;
}

// merge
int singleMergeTable(
    GmcConnT *conn, GmcStmtT *stmt, DataTypeE ezdata, int dataNum, int initail, SnUserDataT *userData = NULL)
{
    int ret = 0;
    char f5[29] = "string";
    char f6[29] = "bytes";
    char f7[29] = "fix";
    uint32_t f0_u = 0;
    for (int i = 0; i < dataNum; i++) {
        // set value
        f0_u = i + initail;
        if (userData != NULL) {
            StmgSubCtxT *stmgCtx = userData->stmgSubCtx;
            // 用一个连续且唯一的int类型字段值，来作为来判断是否推送成功的依据,此处f0字段连续且唯一
            stmgCtx[i + initail].index = f0_u;
            stmgCtx[i + initail].diffListIndex = initail;
            stmgCtx[i + initail].updateAddVal = g_addVal;
            stmgCtx[i + initail].isEndStatus = true;
            stmgCtx[i + initail].opType = TEST_OP_UPDATE;
        }

        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f0_u, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "pk");
        EXPECT_EQ(GMERR_OK, ret);

        (void)snprintf(f5, 28, "string_%d", i + initail + g_addVal);
        (void)snprintf(f6, 28, "bytes_%d", i + initail + g_addVal);
        (void)snprintf(f7, 28, "fixed_%d", (i + initail + g_addVal) % 9);
        ezdata.f2 = i + initail + g_addVal;
        ezdata.f3 = i + initail + g_addVal;
        ezdata.f4 = i + initail + g_addVal;
        ezdata.f5 = (char *)f5;
        ezdata.f6 = (char *)f6;
        ezdata.f7 = (char *)f7;
        setOtherValue(stmt, ezdata);

        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}

// 批量merge
int batchMergeTable(
    GmcConnT *conn, GmcStmtT *stmt, DataTypeE ezdata, int dataNum, int initail, SnUserDataT *userData = NULL)
{
    int ret = 0;
    // 批写
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char f5[29] = "string";
    char f6[29] = "bytes";
    char f7[29] = "fix";
    uint32_t f0_u = 0;
    for (int i = 0; i < dataNum; i++) {
        // set value
        f0_u = i + initail;
        if (userData != NULL) {
            StmgSubCtxT *stmgCtx = userData->stmgSubCtx;
            // 用一个连续且唯一的int类型字段值，来作为来判断是否推送成功的依据,此处f0字段连续且唯一
            stmgCtx[i + initail].index = f0_u;
            stmgCtx[i + initail].diffListIndex = initail;
            stmgCtx[i + initail].updateAddVal = g_addVal;
            stmgCtx[i + initail].isEndStatus = true;
            stmgCtx[i + initail].opType = TEST_OP_UPDATE;
        }

        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f0_u, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "pk");

        (void)snprintf(f5, 28, "string_%d", i + initail + g_addVal);
        (void)snprintf(f6, 28, "bytes_%d", i + initail + g_addVal);
        (void)snprintf(f7, 28, "fixed_%d", (i + initail + g_addVal) % 9);
        ezdata.f2 = i + initail + g_addVal;
        ezdata.f3 = i + initail + g_addVal;
        ezdata.f4 = i + initail + g_addVal;
        ezdata.f5 = (char *)f5;
        ezdata.f6 = (char *)f6;
        ezdata.f7 = (char *)f7;
        setOtherValue(stmt, ezdata);
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    return ret;
}

// 单删
int singleDeleteTable(
    GmcConnT *conn, GmcStmtT *stmt, DataTypeE ezdata, int dataNum, int initail, SnUserDataT *userData = NULL)
{
    int ret = 0;
    for (int i = 0; i < dataNum; i++) {
        // set value
        ezdata.f0 = i + initail;

        if (userData != NULL) {
            StmgSubCtxT *stmgCtx = userData->stmgSubCtx;
            // 用一个连续且唯一的int类型字段值，来作为来判断是否推送成功的依据,此处f0字段连续且唯一
            stmgCtx[i + initail].index = ezdata.f0;
            stmgCtx[i + initail].diffListIndex = initail;
            stmgCtx[i + initail].updateAddVal = g_addVal;
            stmgCtx[i + initail].isEndStatus = true;
            stmgCtx[i + initail].opType = TEST_OP_DELETE;
        }

        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &ezdata.f0, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // 删除
        ret = GmcSetIndexKeyName(stmt, "pk");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return ret;
}

// 批删
int batchDeleteTable(
    GmcConnT *conn, GmcStmtT *stmt, DataTypeE ezdata, int dataNum, int initail, SnUserDataT *userData = NULL)
{
    int ret = 0;
    // 批写
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < dataNum; i++) {
        // set value
        ezdata.f0 = i + initail;
        if (userData != NULL) {
            StmgSubCtxT *stmgCtx = userData->stmgSubCtx;
            // 用一个连续且唯一的int类型字段值，来作为来判断是否推送成功的依据,此处f0字段连续且唯一
            stmgCtx[i + initail].index = ezdata.f0;
            stmgCtx[i + initail].diffListIndex = initail;
            stmgCtx[i + initail].updateAddVal = g_addVal;
            stmgCtx[i + initail].isEndStatus = true;
            stmgCtx[i + initail].opType = TEST_OP_DELETE;
        }

        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &ezdata.f0, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // 删除
        ret = GmcSetIndexKeyName(stmt, "pk");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    return ret;
}

// /*-------------------------------------write table but not all property was set------------------------------------*/
int singleErrorWriteTable(
    GmcConnT *conn, GmcStmtT *stmt, DataTypeE ezdata, int dataNum, int initail, SnUserDataT *userData = NULL)
{
    int ret = 0;
    char f5[29] = "string";
    char f6[29] = "bytes";
    char f7[29] = "fix";

    for (int i = 0; i < dataNum; i++) {
        // set value
        (void)snprintf(f5, 28, "string_%d", i + initail);
        (void)snprintf(f6, 28, "bytes_%d", i + initail);
        (void)snprintf(f7, 28, "fixed_%d", (i + initail) % 9);
        ezdata.f0 = i + initail;
        ezdata.f2 = i + initail;
        ezdata.f3 = i + initail;
        ezdata.f4 = i + initail;
        ezdata.f5 = (char *)f5;
        ezdata.f6 = (char *)f6;
        ezdata.f7 = (char *)f7;

        if (userData != NULL) {
            StmgSubCtxT *stmgCtx = userData->stmgSubCtx;
            // 用一个连续且唯一的int类型字段值，来作为来判断是否推送成功的依据,此处f0字段连续且唯一
            stmgCtx[i + initail].index = ezdata.f0;
            stmgCtx[i + initail].diffListIndex = initail;
            stmgCtx[i + initail].updateAddVal = 0;
            stmgCtx[i + initail].isEndStatus = true;
            stmgCtx[i + initail].opType = TEST_OP_INSERT;
        }

        setPkValue(stmt, ezdata.f0);
        setOtherValue(stmt, ezdata);
        ret = GmcExecute(stmt);
        if (ret == GMERR_PRIMARY_KEY_VIOLATION) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);
            ret = GMERR_OK;
        } else if (ret == GMERR_TRANSACTION_ROLLBACK) {
            return -2;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    return ret;
}

int batchErrorWriteTable(
    GmcConnT *conn, GmcStmtT *stmt, DataTypeE ezdata, int dataNum, int initail, SnUserDataT *userData = NULL)
{
    int ret = 0;
    char f5[29] = "string";
    char f6[29] = "bytes";
    char f7[29] = "fix";

    for (int i = 0; i < dataNum; i++) {
        // set value
        (void)snprintf(f5, 28, "string_%d", i + initail);
        (void)snprintf(f6, 28, "bytes_%d", i + initail);
        (void)snprintf(f7, 28, "fixed_%d", (i + initail) % 9);
        if (i == dataNum - 1) {
            ezdata.f0 = i + initail;
            ezdata.f2 = 'e';
            ezdata.f3 = 'e';
            ezdata.f4 = 99.00;
            ezdata.f5 = (char *)"end";
            ezdata.f6 = (char *)"end";
            ezdata.f7 = (char *)"fffffff";
        } else {
            ezdata.f0 = i + initail;
            ezdata.f2 = i + initail;
            ezdata.f3 = i + initail;
            ezdata.f4 = i + initail;
            ezdata.f5 = (char *)f5;
            ezdata.f6 = (char *)f6;
            ezdata.f7 = (char *)f7;
        }
        setPkValue(stmt, ezdata.f0);
        setOtherValue(stmt, ezdata);
        ret = GmcExecute(stmt);
        if (ret != 0) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);
        }
    }
    return ret;
}

int readTable(
    GmcConnT *conn, GmcStmtT *stmt, DataTypeE ezdata, int dataNum, int initail, SnUserDataT *userData = NULL)
{
    int ret = 0;
    AW_FUN_Log(LOG_INFO, "[readTable]TOF");
    return ret;
}

int structReadTable(
    GmcConnT *conn, GmcStmtT *stmt, DataTypeE ezdata, int dataNum, int initail, SnUserDataT *userData = NULL)
{
    int ret = 0;
    AW_FUN_Log(LOG_INFO, "[structReadTable]TOF");
    return ret;
}

// DML函数驱动表
static OperType g_eventMap[] = {{"single insert", singleWriteTable}, {"single struct insert", structWriteTable},
    {"batch insert", batchWriteTable}, {"batch struct insert", structBatchWriteTable},
    {"some property not set single", singleErrorWriteTable}, {"some property not set batch", batchErrorWriteTable},
    {"single update", singleUpdateTable}, {"batch update", batchUpdateTable}, {"single delete", singleDeleteTable},
    {"batch delete", batchDeleteTable}, {"read table", readTable}, {"struct read table", structReadTable},
    {"single replace", singleReplaceTable}, {"batch replace", batchReplaceTable}, {"single merge", singleMergeTable},
    {"batch merge", batchMergeTable}};

int NewSub_Vertex_DML(GmcConnT *conn, GmcStmtT *stmt, char *labelName, OperTypeE type, DataTypeE ezdata, int dataNum,
    int64_t current, SnUserDataT *userData = NULL)
{
    int ret = 0;
    if (type < SINGLE_INSERT || type > BATCH_MERGE) {
        return -1;
    }
    bool walkDataService;
    if (type >= SINGLE_INSERT && type <= BATCH_ERROR_WRITE) {
        // 插入和更新操作
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else if (type >= SINGLE_UPDATE && type <= BATCH_UPDATE) {
        // 更新操作
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else if (type >= SINGLE_DELETE && type <= BATCH_DELETE) {
        // 删除操作
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else if (type >= READ && type <= STRUCT_READ) {
        // 读操作
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else if (type >= SINGLE_REPLACE && type <= BATCH_REPLACE) {
        // REPLACE操作
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else if (type >= SINGLE_MERGE && type <= BATCH_MERGE) {
        // MEREGE操作
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 执行操作
    OperType handle = g_eventMap[type];
    ret = handle.phandle(conn, stmt, ezdata, dataNum, current, userData);
    return ret;
}

// 初始化预期推送数据的标记位,将SnUserDataT中的old_value成员作为标记位
void TestInitPushDataFlag(SnUserDataT *userData, int dataNum, int diffListIndex, int sleepWait = 0)
{
    // 根据情况sleep等待，让前面触发的推送，消费完
    sleep(sleepWait);
    StmgSubCtxT *stmgCtx = userData->stmgSubCtx;
    int count = dataNum;

    for (int i = diffListIndex; i < count + diffListIndex; i++) {
        stmgCtx[i].isPushed = false;
    }

    return;
}

// set已推送数据的标记位
void TestSetPushDataFlag(SnUserDataT *userData, int uniqueVal)
{
    StmgSubCtxT *stmgCtx = userData->stmgSubCtx;
    stmgCtx[uniqueVal].isPushed = true;
    return;
}

// check数据是不是已经被全部推送过
int TestCheckPushDataFlag(SnUserDataT *userData, int dataNum, int diffListIndex)
{
    StmgSubCtxT *stmgCtx = userData->stmgSubCtx;

    for (int i = diffListIndex; i < dataNum + diffListIndex; i++) {
        if (stmgCtx[i].isPushed != 1) {
            AW_FUN_Log(LOG_ERROR, "f0Val %d is not push, F0 push flag is %d.", i, stmgCtx[i].isPushed);
            return -1;
        } else {
            stmgCtx[i].isPushed = 0;
            stmgCtx[i].index = 0;
            stmgCtx[i].diffListIndex = 0;
            stmgCtx[i].updateAddVal = 0;
            stmgCtx[i].isEndStatus = false;
            stmgCtx[i].opType = (StmtSubOpTypeE)0;
        }
    }
    return 0;
}

void CheckPushData(GmcStmtT *subStmt, int index, int diffListIndex, int addVal)
{
    DataTypeE data = {0};
    int ret = 0;
    char f5[29] = {0};
    char f6[29] = {0};
    char f7[29] = {0};
    (void)snprintf(f5, 28, "string_%d", index + diffListIndex + addVal);
    (void)snprintf(f6, 28, "bytes_%d", index + diffListIndex + addVal);
    (void)snprintf(f7, 28, "fixed_%d", (index + diffListIndex + addVal) % 9);
    data.f2 = index + diffListIndex + addVal;
    data.f3 = index + diffListIndex + addVal;
    data.f4 = index + diffListIndex + addVal;
    data.f5 = (char *)f5;
    data.f6 = (char *)f6;
    data.f7 = (char *)f7;
    ret = queryPropertyAndCompare(subStmt, "F2", GMC_DATATYPE_CHAR, &data.f2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_INFO, "expect f2 %d.", data.f2);
        int f0Val = 0;
        bool isNull = true;
        ret = GmcGetVertexPropertyByName(subStmt, "F0", &f0Val, sizeof(int), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_BOOL(isNull, false);
        AW_FUN_Log(LOG_INFO, "get f0 %d.", f0Val);

        char f2Val;
        isNull = true;
        ret = GmcGetVertexPropertyByName(subStmt, "F2", &f2Val, sizeof(char), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_BOOL(isNull, false);
        AW_FUN_Log(LOG_INFO, "get f2 %d.", f2Val);

        char cmd[128];
        snprintf(cmd, 128, "gmsysview record V_1 -c F0 = %d", f0Val);
        system(cmd);
        return;
    }
    ret = queryPropertyAndCompare(subStmt, "F3", GMC_DATATYPE_UCHAR, &data.f3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(subStmt, "F4", GMC_DATATYPE_FLOAT, &data.f4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(subStmt, "F5", GMC_DATATYPE_STRING, data.f5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(subStmt, "F6", GMC_DATATYPE_BYTES, data.f6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(subStmt, "F7", GMC_DATATYPE_FIXED, data.f7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

int TestCheckSubStmt(GmcStmtT *subStmt, SnUserDataT *userData, const char *type)
{
    StmgSubCtxT *stmgCtx = userData->stmgSubCtx;
    int f0Val = 0;
    bool isNull = true;
    int ret = GmcGetVertexPropertyByName(subStmt, "F0", &f0Val, sizeof(int), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_BOOL(isNull, false);
    int diffListIndex = stmgCtx[f0Val].diffListIndex;
    int addVal = stmgCtx[f0Val].updateAddVal;
    bool isEndStatus = stmgCtx[f0Val].isEndStatus;
    AW_FUN_Log(LOG_INFO, "push %s f0Val %d isEndStatus: %d, addValm %d, diffListIndex %d, op %d.", type, f0Val,
        stmgCtx[f0Val].isEndStatus, stmgCtx[f0Val].updateAddVal, stmgCtx[f0Val].diffListIndex, stmgCtx[f0Val].opType);

    if (isEndStatus && (stmgCtx[f0Val].isPushed == false)) {
        if (stmgCtx[f0Val].index == f0Val) {
            TestSetPushDataFlag(userData, f0Val);
        } else {
            AW_FUN_Log(LOG_ERROR, "%s f0Val %d is not expect, expect %d.", type, f0Val, stmgCtx[f0Val].index);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
        };

        CheckPushData(subStmt, f0Val - diffListIndex, diffListIndex, addVal);
    }
    return 0;
}

void NewSnCallBack2(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    char labelName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;
    int ret = 0;
    bool eof = false;

    while (!eof) {
        if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
            user_data->scanEofNum++;
            break;
        }

        ret = GmcFetch(subStmt, &eof);  // fetch 1 次
        if (ret != GMERR_OK || eof == true) {
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);

        for (int i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            if (info->eventType != GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
                ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
                EXPECT_EQ(GMERR_OK, ret);
                EXPECT_EQ(strlen(labelName), labelNameLen);
            }
            switch (info->eventType) {
                case GMC_SUB_EVENT_MODIFY: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = TestCheckSubStmt(subStmt, user_data, "MODIFY");
                    EXPECT_EQ(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = TestCheckSubStmt(subStmt, user_data, "DELETE");
                    EXPECT_EQ(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_INITIAL_LOAD: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = TestCheckSubStmt(subStmt, user_data, "INITIAL_LOAD");
                    EXPECT_EQ(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                    break;
                }
                default: {
                    printf("default: invalid eventType\r\n");
                    break;
                }
            }
        }
        switch (info->eventType) {
            case GMC_SUB_EVENT_MODIFY: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            default: {
                printf("default: eventType\r\n");
                break;
            }
        }
    }
}

void NewSnCallBack10(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    char labelName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;
    int ret = 0;
    bool eof = false;

    while (!eof) {
        if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
            user_data->scanEofNum++;
            break;
        }

        ret = GmcFetch(subStmt, &eof);  // fetch 1 次
        if (ret != GMERR_OK || eof == true) {
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);

        for (int i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            if (info->eventType != GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
                ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
                EXPECT_EQ(GMERR_OK, ret);
                EXPECT_EQ(strlen(labelName), labelNameLen);
            }
            switch (info->eventType) {
                case GMC_SUB_EVENT_MODIFY: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_INITIAL_LOAD: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                    break;
                }
                default: {
                    printf("default: invalid eventType\r\n");
                    break;
                }
            }
        }
        switch (info->eventType) {
            case GMC_SUB_EVENT_MODIFY: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            default: {
                printf("default: eventType\r\n");
                break;
            }
        }
    }
}

void NewSnCallBackError1(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    char labelName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;
    int ret = 0;
    bool eof = false;

    while (!eof) {
        if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
            user_data->scanEofNum++;
            break;
        }

        ret = GmcFetch(subStmt, &eof);  // fetch 1 次
        if (ret != GMERR_OK || eof == true) {
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);

        for (int i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            if (info->eventType != GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
                ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
                EXPECT_EQ(GMERR_OK, ret);
                EXPECT_EQ(strlen(labelName), labelNameLen);
            }
            const void *keyy;
            switch (info->eventType) {
                case GMC_SUB_EVENT_MODIFY: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = TestCheckSubStmt(subStmt, user_data, "MODIFY");
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_NO_DATA, ret);
                    ret = GmcSubGetKey(subStmt, &keyy, NULL);
                    EXPECT_EQ(GMERR_NO_DATA, ret);
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = TestCheckSubStmt(subStmt, user_data, "DELETE");
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_NO_DATA, ret);
                    ret = GmcSubGetKey(subStmt, &keyy, NULL);
                    EXPECT_EQ(GMERR_NO_DATA, ret);
                    break;
                }
                case GMC_SUB_EVENT_INITIAL_LOAD: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = TestCheckSubStmt(subStmt, user_data, "INITIAL_LOAD");
                    EXPECT_EQ(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_AGED: {
                    AW_FUN_Log(LOG_INFO, "agedNum %d.", user_data->agedNum);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                    break;
                }
                default: {
                    printf("default: invalid eventType\r\n");
                    break;
                }
            }
        }

        ret = GmcUnSubscribe(subStmt, "newSub001error");
        AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);

        switch (info->eventType) {
            case GMC_SUB_EVENT_MODIFY: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
            default: {
                printf("default: eventType\r\n");
                break;
            }
        }
    }
}

unsigned int TransIp(const char *ipStr)
{
    char *ipstr = NULL;
    char strIpIndex[4] = {'\0'};
    unsigned int ipInt, ipAdd = 0, ipIntIndex[4], ipTempNumbr = 24;
    int j = 0, a = 3;
    for (unsigned int i = 0; i <= strlen(ipStr); i++) {
        if (ipStr[i] == '\0' || ipStr[i] == '.') {
            ipInt = atoi(strIpIndex);
            if (ipInt < 0 || ipInt > 255) {
                printf("IP地址有误\n");
                system("pause");
                return 0;
            }
            ipAdd += (ipInt * ((unsigned int)pow(256.0, a)));
            a--;
            memset(strIpIndex, 0, sizeof(strIpIndex));
            j = 0;
            continue;
        }
        strIpIndex[j] = ipStr[i];
        j++;
    }
    return ipAdd;
}
char g_ipAddr[16];
int test_insert_vertex_ip4forward_lpm4(GmcStmtT *stmt, const char *vtxLabelName, uint32_t vr_id, uint32_t vrf_index,
    uint32_t dest_ip_addr, uint8_t mask_len)
{
    int ret = 0;
    for (int loop = 0; loop < dest_ip_addr; loop++) {
        ret = testGmcPrepareStmtByLabelName(stmt, vtxLabelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &loop, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &vr_id, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &vrf_index, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        char ip_addr_tmp[16];
        (void)sprintf(ip_addr_tmp, "192.168.%d.0", loop);
        uint32_t trans_val =
            TransIp(ip_addr_tmp);  // ************  3232239108  192.地址，切割 --> 二进制（去掉.）--> 10进制

        ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_UINT32, &trans_val, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &mask_len, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return ret;
}

int test_insert_vertex_lpm6(GmcStmtT *stmt, const char *labelName, int operBegin, int operEnd)
{
    int ret = 0;
    unsigned int wrUnit32 = 0;
    uint8_t iss = 0;
    uint8_t wr_fixed[16] = {
        0xcd, 0xcd, 0x91, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00, 0x20, 0x20};
    for (uint32_t loop = operBegin; loop <= operEnd; loop++) {
        iss = loop;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        // lpm6 index
        ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &wrUnit32, sizeof(wrUnit32));
        EXPECT_EQ(GMERR_OK, ret);
        // lpm6 index
        ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &wrUnit32, sizeof(wrUnit32));
        EXPECT_EQ(GMERR_OK, ret);
        // lpm6 index
        ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_FIXED, wr_fixed, 16);
        EXPECT_EQ(GMERR_OK, ret);
        // lpm6 index
        ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &iss, sizeof(iss));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    return ret;
}

const char *g_treeschema = R"([{
    "type":"record",
    "name":"OP_T0",
    "fields":[
        {"name":"F0", "type":"int64", "nullable":false},
        {"name":"F1", "type":"uint64", "nullable":true},
        {"name":"F2", "type":"int32", "nullable":true},
        {"name":"F3", "type":"uint32", "nullable":true},
        {"name":"F4", "type":"int16", "nullable":true},
        {"name":"F5", "type":"uint16", "nullable":true},
        {"name":"F6", "type":"int8", "nullable":true},
        {"name":"F7", "type":"uint8", "nullable":true},
        {"name":"F8", "type":"boolean", "nullable":true},
        {"name":"F9", "type":"float", "nullable":true},
        {"name":"F10", "type":"double", "nullable":true},
        {"name":"F11", "type":"time", "nullable":true},
        {"name":"F12", "type":"char", "nullable":true},
	    {"name":"F13", "type":"uchar", "nullable":true},
        {"name":"F14", "type":"string", "size":100, "nullable":true},
        {"name":"F15", "type":"bytes", "size":7, "nullable":true},
        {"name":"F16", "type":"fixed", "size":7, "nullable":true},
        {"name":"T1", "type":"record",
	    "fields": [
	          {"name":"P0", "type":"int64"},
              {"name":"P1", "type":"uint64"},
              {"name":"P2", "type":"int32"},
              {"name":"P3", "type":"uint32"},
              {"name":"P4", "type":"int16"},
              {"name":"P5", "type":"uint16"},
              {"name":"P6", "type":"int8"},
              {"name":"P7", "type":"uint8"},
              {"name":"P8", "type":"boolean"},
              {"name":"P9", "type":"float"},
              {"name":"P10", "type":"double"},
              {"name":"P11", "type":"time"},
              {"name":"P12", "type":"char"},
	          {"name":"P13", "type":"uchar"},
              {"name":"P14", "type":"string", "size":100},
              {"name":"P15", "type":"bytes", "size":7},
              {"name":"P16", "type":"fixed", "size":7},
	  		  {"name":"T2", "type": "record", "fixed_array": true, "size": 2,
	  		  "fields": [
	  		  	     {"name":"A0", "type":"int64", "nullable":true},
                     {"name":"A1", "type":"uint64", "nullable":true},
                     {"name":"A2", "type":"int32", "nullable":true},
                     {"name":"A3", "type":"uint32", "nullable":true},
                     {"name":"A4", "type":"int16", "nullable":true},
                     {"name":"A5", "type":"uint16", "nullable":true},
                     {"name":"A6", "type":"int8", "nullable":true},
                     {"name":"A7", "type":"uint8", "nullable":true},
                     {"name":"A8", "type":"boolean", "nullable":true},
                     {"name":"A9", "type":"float", "nullable":true},
                     {"name":"A10", "type":"double", "nullable":true},
                     {"name":"A11", "type":"time", "nullable":true},
                     {"name":"A12", "type":"char", "nullable":true},
	                 {"name":"A13", "type":"uchar", "nullable":true},
                     {"name":"A14", "type":"string", "size":100, "nullable":true},
                     {"name":"A15", "type":"bytes", "size":7, "nullable":true},
                     {"name":"A16", "type":"fixed", "size":7, "nullable":true}
                ],
             "super_fields":
                [
                {"name":"superfiled1",  "comment":"test",
                  "fields":["A0", "A1", "A2", "A3", "A4"]
                    }
                ]
            }],
            "super_fields":
                [
                {"name":"superfiled0",  "comment":"test",
                  "fields":["P0", "P1", "P2", "P3", "P4"]
                    }
                ]
        },
	    {"name":"T3", "type": "record", "vector": true, "size": 20,
	    "fields": [
	    	  {"name":"V0", "type":"int64"},
              {"name":"V1", "type":"uint64"},
              {"name":"V2", "type":"int32"},
              {"name":"V3", "type":"uint32"},
              {"name":"V4", "type":"int16"},
              {"name":"V5", "type":"uint16"},
              {"name":"V6", "type":"int8"},
              {"name":"V7", "type":"uint8"},
              {"name":"V8", "type":"boolean"},
              {"name":"V9", "type":"float"},
              {"name":"V10", "type":"double"},
              {"name":"V11", "type":"time"},
              {"name":"V12", "type":"char"},
	          {"name":"V13", "type":"uchar"},
              {"name":"V14", "type":"string", "size":100},
              {"name":"V15", "type":"bytes", "size":7},
              {"name":"V16", "type":"fixed", "size":7}
            ],
 "super_fields":
                [
                 {"name":"superfiled2",  "comment":"test",
                 "fields":["V0", "V1", "V2", "V3", "V4"]
                }
                ]
                }
    ],
    "keys":[
       {
            "node":"OP_T0",
            "name":"OP_PK",
            "fields":["F0"],
            "index":{"type":"primary"},
            "constraints":{"unique":true}
        },
       {
           "node": "OP_T0",
           "name": "localhash_key",
           "index": { "type": "localhash"},
           "fields": [ "F0" ],
	       "constraints": { "unique": true }
        },
       {
           "node": "OP_T0",
           "name": "hashcluster_key",
           "index": { "type": "hashcluster"},
           "fields": [ "F1" ],
	       "constraints": { "unique": true }
        }
    ],
    "subs_type":"status_merge"
}])";
int g_startNum = 0;
int g_endNum = 10;
int g_arrayNum = 3;
int g_vectorNum = 3;
void TestGmcNodeSetPropertyByName_PK(GmcNodeT *node, int i)
{
    int ret = 0;
    int64_t f0Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F0", GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
}

void TestGmcNodeSetPropertyByName_R(GmcNodeT *node, int i, bool bool_value, char *f14Value)
{
    int ret = 0;
    int8_t value8 = i % 128;
    uint8_t value_u8 = i % 256;
    int16_t value16 = i % 32768;
    uint16_t value_u16 = i % 65566;

    uint64_t f1value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F1", GMC_DATATYPE_UINT64, &f1value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    int32_t f2Value = 2 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t f3Value = 3 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F3", GMC_DATATYPE_UINT32, &f3Value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    int16_t f4Value = value16;
    ret = GmcNodeSetPropertyByName(node, (char *)"F4", GMC_DATATYPE_INT16, &f4Value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint16_t f5Value = value_u16;
    ret = GmcNodeSetPropertyByName(node, (char *)"F5", GMC_DATATYPE_UINT16, &f5Value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    int8_t f6Value = value8;
    ret = GmcNodeSetPropertyByName(node, (char *)"F6", GMC_DATATYPE_INT8, &f6Value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t f7Value = value_u8;
    ret = GmcNodeSetPropertyByName(node, (char *)"F7", GMC_DATATYPE_UINT8, &f7Value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    bool f8Value = bool_value;
    ret = GmcNodeSetPropertyByName(node, (char *)"F8", GMC_DATATYPE_BOOL, &f8Value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);

    float f9Value = 9 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F9", GMC_DATATYPE_FLOAT, &f9Value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);

    double f10Value = 10 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F10", GMC_DATATYPE_DOUBLE, &f10Value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    char f12Value = value8;
    ret = GmcNodeSetPropertyByName(node, (char *)"F12", GMC_DATATYPE_CHAR, &f12Value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);

    unsigned char f13Value = value_u8;
    ret = GmcNodeSetPropertyByName(node, (char *)"F13", GMC_DATATYPE_UCHAR, &f13Value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F14", GMC_DATATYPE_STRING, f14Value, (strlen(f14Value)));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F15", GMC_DATATYPE_BYTES, f14Value, (strlen(f14Value)));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F16", GMC_DATATYPE_FIXED, f14Value, 7);
    ASSERT_EQ(GMERR_OK, ret);
}

void TestGmcNodeSetPropertyByName_P(GmcNodeT *node, int i, bool bool_value, char *f14Value)
{
    int ret = 0;
    int8_t value8 = i % 128;
    uint8_t value_u8 = i % 256;
    int16_t value16 = i % 32768;
    uint16_t value_u16 = i % 65566;

    int64_t f0Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P0", GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f1value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P1", GMC_DATATYPE_UINT64, &f1value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    int32_t f2Value = 2 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t f3Value = 3 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P3", GMC_DATATYPE_UINT32, &f3Value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    int16_t f4Value = value16;
    ret = GmcNodeSetPropertyByName(node, (char *)"P4", GMC_DATATYPE_INT16, &f4Value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint16_t f5Value = value_u16;
    ret = GmcNodeSetPropertyByName(node, (char *)"P5", GMC_DATATYPE_UINT16, &f5Value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    int8_t f6Value = value8;
    ret = GmcNodeSetPropertyByName(node, (char *)"P6", GMC_DATATYPE_INT8, &f6Value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t f7Value = value_u8;
    ret = GmcNodeSetPropertyByName(node, (char *)"P7", GMC_DATATYPE_UINT8, &f7Value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    bool f8Value = bool_value;
    ret = GmcNodeSetPropertyByName(node, (char *)"P8", GMC_DATATYPE_BOOL, &f8Value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);

    float f9Value = 9 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P9", GMC_DATATYPE_FLOAT, &f9Value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);

    double f10Value = 10 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P10", GMC_DATATYPE_DOUBLE, &f10Value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    char f12Value = value8;
    ret = GmcNodeSetPropertyByName(node, (char *)"P12", GMC_DATATYPE_CHAR, &f12Value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);

    unsigned char f13Value = value_u8;
    ret = GmcNodeSetPropertyByName(node, (char *)"P13", GMC_DATATYPE_UCHAR, &f13Value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"P14", GMC_DATATYPE_STRING, f14Value, (strlen(f14Value)));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"P15", GMC_DATATYPE_BYTES, f14Value, (strlen(f14Value)));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"P16", GMC_DATATYPE_FIXED, f14Value, 7);
    ASSERT_EQ(GMERR_OK, ret);
}

void TestGmcNodeSetPropertyByName_A(GmcNodeT *node, int i, bool bool_value, char *f14Value)
{
    int ret = 0;
    int8_t value8 = i % 128;
    uint8_t value_u8 = i % 256;
    int16_t value16 = i % 32768;
    uint16_t value_u16 = i % 65566;

    int64_t f0Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A0", GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f1value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A1", GMC_DATATYPE_UINT64, &f1value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    int32_t f2Value = 2 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t f3Value = 3 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A3", GMC_DATATYPE_UINT32, &f3Value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    int16_t f4Value = value16;
    ret = GmcNodeSetPropertyByName(node, (char *)"A4", GMC_DATATYPE_INT16, &f4Value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint16_t f5Value = value_u16;
    ret = GmcNodeSetPropertyByName(node, (char *)"A5", GMC_DATATYPE_UINT16, &f5Value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    int8_t f6Value = value8;
    ret = GmcNodeSetPropertyByName(node, (char *)"A6", GMC_DATATYPE_INT8, &f6Value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t f7Value = value_u8;
    ret = GmcNodeSetPropertyByName(node, (char *)"A7", GMC_DATATYPE_UINT8, &f7Value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    bool f8Value = bool_value;
    ret = GmcNodeSetPropertyByName(node, (char *)"A8", GMC_DATATYPE_BOOL, &f8Value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);

    float f9Value = 9 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A9", GMC_DATATYPE_FLOAT, &f9Value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);

    double f10Value = 10 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A10", GMC_DATATYPE_DOUBLE, &f10Value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    char f12Value = value8;
    ret = GmcNodeSetPropertyByName(node, (char *)"A12", GMC_DATATYPE_CHAR, &f12Value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);

    unsigned char f13Value = value_u8;
    ret = GmcNodeSetPropertyByName(node, (char *)"A13", GMC_DATATYPE_UCHAR, &f13Value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"A14", GMC_DATATYPE_STRING, f14Value, (strlen(f14Value)));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"A15", GMC_DATATYPE_BYTES, f14Value, (strlen(f14Value)));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"A16", GMC_DATATYPE_FIXED, f14Value, 7);
    ASSERT_EQ(GMERR_OK, ret);
}

void TestGmcNodeSetPropertyByName_V(GmcNodeT *node, int i, bool bool_value, char *f14Value)
{
    int ret = 0;
    int8_t value8 = i % 128;
    uint8_t value_u8 = i % 256;
    int16_t value16 = i % 32768;
    uint16_t value_u16 = i % 65566;

    int64_t f0Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V0", GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f1value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V1", GMC_DATATYPE_UINT64, &f1value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    int32_t f2Value = 2 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t f3Value = 3 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V3", GMC_DATATYPE_UINT32, &f3Value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    int16_t f4Value = value16;
    ret = GmcNodeSetPropertyByName(node, (char *)"V4", GMC_DATATYPE_INT16, &f4Value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint16_t f5Value = value_u16;
    ret = GmcNodeSetPropertyByName(node, (char *)"V5", GMC_DATATYPE_UINT16, &f5Value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    int8_t f6Value = value8;
    ret = GmcNodeSetPropertyByName(node, (char *)"V6", GMC_DATATYPE_INT8, &f6Value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t f7Value = value_u8;
    ret = GmcNodeSetPropertyByName(node, (char *)"V7", GMC_DATATYPE_UINT8, &f7Value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    bool f8Value = bool_value;
    ret = GmcNodeSetPropertyByName(node, (char *)"V8", GMC_DATATYPE_BOOL, &f8Value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);

    float f9Value = 9 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V9", GMC_DATATYPE_FLOAT, &f9Value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);

    double f10Value = 10 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V10", GMC_DATATYPE_DOUBLE, &f10Value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    char f12Value = value8;
    ret = GmcNodeSetPropertyByName(node, (char *)"V12", GMC_DATATYPE_CHAR, &f12Value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);

    unsigned char f13Value = value_u8;
    ret = GmcNodeSetPropertyByName(node, (char *)"V13", GMC_DATATYPE_UCHAR, &f13Value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"V14", GMC_DATATYPE_STRING, f14Value, (strlen(f14Value)));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"V15", GMC_DATATYPE_BYTES, f14Value, (strlen(f14Value)));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"V16", GMC_DATATYPE_FIXED, f14Value, 7);
    ASSERT_EQ(GMERR_OK, ret);
}

void TestGmcNodeGetPropertyByName_R(GmcNodeT *node, int i, bool bool_value, char *f14Value)
{
    int ret = 0;
    int8_t value8 = i % 128;
    uint8_t value_u8 = i % 256;
    int16_t value16 = i % 32768;
    uint16_t value_u16 = i % 65566;

    bool isNull;
    uint64_t f1value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F1", &f1value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f1value);

    int32_t f2Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F2", &f2Value, sizeof(int32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(2 * i, f2Value);

    uint32_t f3Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F3", &f3Value, sizeof(uint32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(3 * i, f3Value);

    int16_t f4Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F4", &f4Value, sizeof(int16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(value16, f4Value);

    uint16_t f5Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F5", &f5Value, sizeof(uint16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(value_u16, f5Value);

    int8_t f6Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F6", &f6Value, sizeof(int8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(value8, f6Value);

    uint8_t f7Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F7", &f7Value, sizeof(uint8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(value_u8, f7Value);

    bool f8Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F8", &f8Value, sizeof(bool), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(bool_value, f8Value);

    float f9Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F9", &f9Value, sizeof(float), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(9 * i, f9Value);

    double f10Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F10", &f10Value, sizeof(double), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(10 * i, f10Value);

    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F11", &f11_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(11 * i, f11_value);

    char f12Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F12", &f12Value, sizeof(char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(value8, f12Value);

    unsigned char f13Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F13", &f13Value, sizeof(unsigned char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    unsigned char k = (unsigned char)(value_u8);
    ASSERT_EQ(k, f13Value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"F14", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(propSize, strlen(f14Value) + 1);

    char stringValue[strlen(f14Value)];
    ret = GmcNodeGetPropertyByName(node, (char *)"F14", &stringValue, propSize, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(strcmp(stringValue, f14Value), 0);

    ret = GmcNodeGetPropertySizeByName(node, (char *)"F15", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(propSize, strlen(f14Value));

    ret = GmcNodeGetPropertyByName(node, (char *)"F15", &stringValue, 10, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(strcmp(stringValue, f14Value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"F16", &stringValue, 7, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(strcmp(stringValue, f14Value), 0);
}

void TestGmcNodeGetPropertyByName_p(GmcNodeT *node, int i, bool bool_value, char *f14Value)
{
    int ret = 0;
    int8_t value8 = i % 128;
    uint8_t value_u8 = i % 256;
    int16_t value16 = i % 32768;
    uint16_t value_u16 = i % 65566;

    bool isNull;
    int64_t f0Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P0", &f0Value, sizeof(int64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f0Value);

    uint64_t f1value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P1", &f1value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f1value);

    int32_t f2Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P2", &f2Value, sizeof(int32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(2 * i, f2Value);

    uint32_t f3Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P3", &f3Value, sizeof(uint32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(3 * i, f3Value);

    int16_t f4Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P4", &f4Value, sizeof(int16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(value16, f4Value);

    uint16_t f5Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P5", &f5Value, sizeof(uint16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(value_u16, f5Value);

    int8_t f6Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P6", &f6Value, sizeof(int8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(value8, f6Value);

    uint8_t f7Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P7", &f7Value, sizeof(uint8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(value_u8, f7Value);

    bool f8Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P8", &f8Value, sizeof(bool), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(bool_value, f8Value);

    float f9Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P9", &f9Value, sizeof(float), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(9 * i, f9Value);

    double f10Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P10", &f10Value, sizeof(double), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(10 * i, f10Value);

    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P11", &f11_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(11 * i, f11_value);

    char f12Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P12", &f12Value, sizeof(char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(value8, f12Value);

    unsigned char f13Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P13", &f13Value, sizeof(unsigned char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    unsigned char k = (unsigned char)(value_u8);
    ASSERT_EQ(k, f13Value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"P14", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(propSize, strlen(f14Value) + 1);

    char stringValue[strlen(f14Value)];
    ret = GmcNodeGetPropertyByName(node, (char *)"P14", &stringValue, propSize, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(strcmp(stringValue, f14Value), 0);

    ret = GmcNodeGetPropertySizeByName(node, (char *)"P15", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(propSize, strlen(f14Value));

    ret = GmcNodeGetPropertyByName(node, (char *)"P15", &stringValue, propSize, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(strcmp(stringValue, f14Value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"P16", &stringValue, 7, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(strcmp(stringValue, f14Value), 0);
}

void TestGmcNodeGetPropertyByName_A(GmcNodeT *node, int i, bool bool_value, char *f14Value)
{
    int ret = 0;
    int8_t value8 = i % 128;
    uint8_t value_u8 = i % 256;
    int16_t value16 = i % 32768;
    uint16_t value_u16 = i % 65566;

    bool isNull;
    int64_t f0Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A0", &f0Value, sizeof(int64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f0Value);

    uint64_t f1value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A1", &f1value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f1value);

    int32_t f2Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A2", &f2Value, sizeof(int32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(2 * i, f2Value);

    uint32_t f3Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A3", &f3Value, sizeof(uint32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(3 * i, f3Value);

    int16_t f4Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A4", &f4Value, sizeof(int16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(value16, f4Value);

    uint16_t f5Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A5", &f5Value, sizeof(uint16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(value_u16, f5Value);

    int8_t f6Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A6", &f6Value, sizeof(int8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(value8, f6Value);

    uint8_t f7Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A7", &f7Value, sizeof(uint8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(value_u8, f7Value);

    bool f8Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A8", &f8Value, sizeof(bool), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(bool_value, f8Value);

    float f9Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A9", &f9Value, sizeof(float), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(9 * i, f9Value);

    double f10Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A10", &f10Value, sizeof(double), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(10 * i, f10Value);

    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A11", &f11_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(11 * i, f11_value);

    char f12Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A12", &f12Value, sizeof(char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(value8, f12Value);

    unsigned char f13Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A13", &f13Value, sizeof(unsigned char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    unsigned char k = (unsigned char)(value_u8);
    ASSERT_EQ(k, f13Value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"A14", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(propSize, strlen(f14Value) + 1);

    char stringValue[strlen(f14Value)];
    ret = GmcNodeGetPropertyByName(node, (char *)"A14", &stringValue, propSize, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(strcmp(stringValue, f14Value), 0);

    ret = GmcNodeGetPropertySizeByName(node, (char *)"A15", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(propSize, strlen(f14Value));

    ret = GmcNodeGetPropertyByName(node, (char *)"A15", &stringValue, propSize, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(strcmp(stringValue, f14Value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"A16", &stringValue, 7, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(strcmp(stringValue, f14Value), 0);
}

void TestGmcNodeGetPropertyByName_V(GmcNodeT *node, int i, bool bool_value, char *f14Value)
{
    int ret = 0;
    int8_t value8 = i % 128;
    uint8_t value_u8 = i % 256;
    int16_t value16 = i % 32768;
    uint16_t value_u16 = i % 65566;

    bool isNull;
    int64_t f0Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V0", &f0Value, sizeof(int64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f0Value);

    uint64_t f1value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V1", &f1value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f1value);

    int32_t f2Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V2", &f2Value, sizeof(int32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(2 * i, f2Value);

    uint32_t f3Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V3", &f3Value, sizeof(uint32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(3 * i, f3Value);

    int16_t f4Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V4", &f4Value, sizeof(int16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(value16, f4Value);

    uint16_t f5Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V5", &f5Value, sizeof(uint16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(value_u16, f5Value);

    int8_t f6Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V6", &f6Value, sizeof(int8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(value8, f6Value);

    uint8_t f7Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V7", &f7Value, sizeof(uint8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(value_u8, f7Value);

    bool f8Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V8", &f8Value, sizeof(bool), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(bool_value, f8Value);

    float f9Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V9", &f9Value, sizeof(float), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(9 * i, f9Value);

    double f10Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V10", &f10Value, sizeof(double), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(10 * i, f10Value);

    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V11", &f11_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(11 * i, f11_value);

    char f12Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V12", &f12Value, sizeof(char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(value8, f12Value);

    unsigned char f13Value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V13", &f13Value, sizeof(unsigned char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    unsigned char k = (unsigned char)(value_u8);
    ASSERT_EQ(k, f13Value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"V14", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(propSize, strlen(f14Value) + 1);

    char stringValue[strlen(f14Value)];
    ret = GmcNodeGetPropertyByName(node, (char *)"V14", &stringValue, propSize, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(strcmp(stringValue, f14Value), 0);

    ret = GmcNodeGetPropertySizeByName(node, (char *)"V15", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(propSize, strlen(f14Value));

    ret = GmcNodeGetPropertyByName(node, (char *)"V15", &stringValue, propSize, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(strcmp(stringValue, f14Value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"V16", &stringValue, 7, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(strcmp(stringValue, f14Value), 0);
}

void TestGmcInsertVertex(GmcStmtT *stmt, int index, bool bool_value, char *f14Value, int startNum, int endNum,
    int arrayNum, int vectorNum, const char *labelName)
{
    int32_t ret = 0;

    // 插入顶点
    for (int i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_PK(root, i * index);
        TestGmcNodeSetPropertyByName_R(root, i * index, bool_value, f14Value);
        TestGmcNodeSetPropertyByName_P(t1, i * index, bool_value, f14Value);
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        // 插入array节点
        for (uint32_t j = 0; j < arrayNum; j++) {
            TestGmcNodeSetPropertyByName_A(t2, i * index, bool_value, f14Value);
            if (j < arrayNum - 1) {
                GmcNodeGetNextElement(t2, &t2);
            }
        }
        // 插入vector节点
        for (uint32_t j = 0; j < vectorNum; j++) {
            ret = GmcNodeAppendElement(t3, &t3);
            ASSERT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_V(t3, i * index, bool_value, f14Value);
        }
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
}

void TestGmcInsertVertexdifferent(GmcStmtT *stmt, int index, bool bool_value, char *f14Value, int startNum, int endNum,
    int arrayNum, int vectorNum, const char *labelName)
{
    int32_t ret = 0;

    char *stringA = (char *)"aaaaaa";
    char *stringB = (char *)"bbbbbb";
    char *stringC = (char *)"cccccc";

    // 插入顶点   数组成员分别传 1 ，0,2
    for (int i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_PK(root, i * index);
        TestGmcNodeSetPropertyByName_R(root, i * index, bool_value, f14Value);
        TestGmcNodeSetPropertyByName_P(t1, i * index, bool_value, f14Value);
        // 插入array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < arrayNum; j++) {
            if (j < arrayNum - 1) {
                if (j == 0) {
                    TestGmcNodeSetPropertyByName_A(t2, 1 * index, bool_value, stringB);
                    GmcNodeGetNextElement(t2, &t2);
                } else if (j == 1) {
                    TestGmcNodeSetPropertyByName_A(t2, 0 * index, bool_value, stringA);
                    GmcNodeGetNextElement(t2, &t2);
                } else {
                    TestGmcNodeSetPropertyByName_A(t2, 2 * index, bool_value, stringC);
                    GmcNodeGetNextElement(t2, &t2);
                }
            }
        }

        // 插入vector节点
        ret = GmcNodeAppendElement(t3, &t3);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(t3, 1 * index, bool_value, stringB);

        ret = GmcNodeAppendElement(t3, &t3);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(t3, 0 * index, bool_value, stringA);

        ret = GmcNodeAppendElement(t3, &t3);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(t3, 2 * index, bool_value, stringC);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
}

void TestGmcInsertVertexdifferentAsync(GmcStmtT *stmt, int index, bool bool_value, char *f14Value, int startNum,
    int endNum, int arrayNum, int vectorNum, const char *labelName)
{
    int32_t ret = 0;

    char *stringA = (char *)"aaaaaa";
    char *stringB = (char *)"bbbbbb";
    char *stringC = (char *)"cccccc";

    // 插入顶点   数组成员分别传 1 ，0,2
    for (int i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_PK(root, i * index);
        TestGmcNodeSetPropertyByName_R(root, i * index, bool_value, f14Value);
        TestGmcNodeSetPropertyByName_P(t1, i * index, bool_value, f14Value);
        // 插入array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < arrayNum; j++) {
            if (j < arrayNum - 1) {
                if (j == 0) {
                    TestGmcNodeSetPropertyByName_A(t2, 1 * index, bool_value, stringB);
                    GmcNodeGetNextElement(t2, &t2);
                } else if (j == 1) {
                    TestGmcNodeSetPropertyByName_A(t2, 0 * index, bool_value, stringA);
                    GmcNodeGetNextElement(t2, &t2);
                } else {
                    TestGmcNodeSetPropertyByName_A(t2, 2 * index, bool_value, stringC);
                    GmcNodeGetNextElement(t2, &t2);
                }
            }
        }

        // 插入vector节点
        ret = GmcNodeAppendElement(t3, &t3);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(t3, 1 * index, bool_value, stringB);

        ret = GmcNodeAppendElement(t3, &t3);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(t3, 0 * index, bool_value, stringA);

        ret = GmcNodeAppendElement(t3, &t3);
        ASSERT_EQ(GMERR_OK, ret);
        AsyncUserDataT data = {0};
        TestGmcNodeSetPropertyByName_V(t3, 2 * index, bool_value, stringC);
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &data;
        ret = GmcExecuteAsync(stmt, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(1, data.affectRows);
    }
}

#define CMD_LEN 1024
int GetMemFromString(const char *result)
{
    char tmp[5] = {0};  // 从结果得到MB单位的值,上限2048M
    for (int i = 0; i < strlen(result); i++) {
        if (result[i] == '[') {
            int j = 0;
            i++;
            while (result[i] != ']') {  // [2048]获取2048
                tmp[j++] = result[i++];
                tmp[j] = '\0';
            }
            break;
        }
    }
    return (atoi(tmp));
}

int testGmcConnect1(GmcConnT **connOut, GmcStmtT **stmt = NULL, int syncMode = 0, bool needEpoll = 1,
    EpollRegFunctionT epollReg = g_epoll_reg_info, const char *connName = NULL, const void *chanRingLen = NULL,
    ConnOptionT *connOptions = NULL, const int32_t *packShrinkThresholdSize = NULL, int runMode = -1, int csMode = 0,
    int *epollFd = &g_epollData.userEpollFd)
{
    int ret = testEnvInit(runMode);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_INFO, "[testGmcConnect1] testEnvInit failed, ret = %d.\n", ret);
        return -1;
    }
    *connOut = NULL;
    GmcConnT *conn;

    GmcConnOptionsT *connOptionsInner;
    ret = GmcConnOptionsCreate(&connOptionsInner);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_INFO, "[testGmcConnect1] GmcConnOptionsCreate failed, ret = %d.\n", ret);
        return ret;
    }

    // 开启大报文
    ret = GmcConnOptionsSetBigMessage(connOptionsInner);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "开启大报文\n");
    const char *p1 = g_connServer;
    if (connOptions && connOptions->serverLocator) {
        p1 = connOptions->serverLocator;
    }
    const char *p3 = g_passwd;
    if (connOptions && connOptions->passwd) {
        p3 = connOptions->passwd;
    }

    ret = GmcConnOptionsSetServerLocator(connOptionsInner, p1);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_INFO, "[testGmcConnect1] GmcConnOptionsSetServerLocator failed, serverLocator = %s, ret = %d.\n",
            p1, ret);
        GmcConnOptionsDestroy(connOptionsInner);
        return ret;
    }

    if (connName) {
        ret = GmcConnOptionsSetConnName(connOptionsInner, connName);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "[testGmcConnect1] GmcConnOptionsSetConnName failed, connName = %s, ret = %d.\n",
                connName, ret);
            GmcConnOptionsDestroy(connOptionsInner);
            return ret;
        }
    }

    if (csMode) {
        ret = GmcConnOptionsSetCSRead(connOptionsInner);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "[testGmcConnect1] GmcConnOptionsSetConnName failed, connName = %s, ret = %d.\n",
                connName, ret);
            GmcConnOptionsDestroy(connOptionsInner);
            return ret;
        }
    }

    if (needEpoll) {
        ret = GmcConnOptionsSetEpollRegFuncWithUserData(connOptionsInner, epollReg, epollFd);
        if (ret != GMERR_OK) {
            printf("[testGmcConnect] GmcConnOptionsSetEpollRegFuncWithUserData failed, ret = %d.\n", ret);
            return ret;
        }
    }

    if (packShrinkThresholdSize) {
        ret = GmcConnOptionsSetPackShrinkThreshold(connOptionsInner, *packShrinkThresholdSize);
        if (ret != GMERR_OK) {
            GmcConnOptionsDestroy(connOptionsInner);
            return ret;
        }
    }

    if (connOptions && connOptions->requestTimeout) {
        ret = GmcConnOptionsSetRequestTimeout(connOptionsInner, connOptions->requestTimeout);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO,
                "[testGmcConnect1] GmcConnOptionsSetRequestTimeout failed, requestTimeout = %d, ret = %d.\n",
                connOptions->requestTimeout, ret);
            GmcConnOptionsDestroy(connOptionsInner);
            return ret;
        }
    }

    if (connOptions && connOptions->msgReadTimeout) {
        ret = GmcConnOptionsSetMsgReadTimeout(connOptionsInner, connOptions->msgReadTimeout);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO,
                "[testGmcConnect1] GmcConnOptionsSetMsgReadTimeout failed,"
                "msgReadTimeout = %d, ret = %d.\n",
                connOptions->msgReadTimeout, ret);
            GmcConnOptionsDestroy(connOptionsInner);
            return ret;
        }
    }

    if (connOptions && connOptions->msgWriteTimeout) {
        ret = GmcConnOptionsSetMsgWriteTimeout(connOptionsInner, connOptions->msgWriteTimeout);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO,
                "[testGmcConnect1] GmcConnOptionsSetMsgWriteTimeout failed,"
                "msgWriteTimeout = %d, ret = %d.\n",
                connOptions->msgWriteTimeout, ret);
            GmcConnOptionsDestroy(connOptionsInner);
            return ret;
        }
    }

    if (connOptions && connOptions->subFailedCb) {
        ret =
            GmcConnOptionsSetSubFailedCallback(connOptionsInner, connOptions->subFailedCb, connOptions->subFailedData);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "[testGmcConnect1] GmcConnOptionsSetSubFailedCallback failed, ret = %d.\n", ret);
            GmcConnOptionsDestroy(connOptionsInner);
            return ret;
        }
    }

    if (connOptions && connOptions->subCallBackTimeout) {
        ret = GmcConnOptionsSetCallbackTimeout(connOptionsInner, connOptions->subCallBackTimeout, true);
        if (ret != GMERR_OK) {
            printf("[testGmcConnect] GmcConnOptionsSetCallbackTimeout failed, ret = %d.\n", ret);
            GmcConnOptionsDestroy(connOptionsInner);
            return ret;
        }
    }

    if (connOptions && connOptions->subCallBackAvgTimeout) {
        ret = GmcConnOptionsSetCallbackTimeout(connOptionsInner, connOptions->subCallBackAvgTimeout, false);
        if (ret != GMERR_OK) {
            printf("[testGmcConnect] GmcConnOptionsSetCallbackTimeout failed, ret = %d.\n", ret);
            GmcConnOptionsDestroy(connOptionsInner);
            return ret;
        }
    }

    ret = GmcConnOptionsSetFlowCtrlCallback(connOptionsInner, ConnCtionFlowCtrlNotice, NULL);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_INFO, "[testGmcConnect1] GmcConnOptionsSetFlowCtrlCallback failed, ret = %d.\n", ret);
        GmcConnOptionsDestroy(connOptionsInner);
        return ret;
    }

    ret = testSetAsyncQueueSize(connOptionsInner, (uint32_t *)chanRingLen);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_INFO, "[testGmcConnect1] testSetAsyncQueueSize failed, ret = %d.\n", ret);
        GmcConnOptionsDestroy(connOptionsInner);
        return ret;
    }

    if (connOptions && connOptions->useReservedConn) {
        ret = GmcConnOptionsSetReservedFlag(connOptionsInner, connOptions->useReservedConn);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "[testGmcConnect1] GmcConnOptionsSetReservedFlag failed, ret = %d.\n", ret);
            GmcConnOptionsDestroy(connOptionsInner);
            return ret;
        }
    }
    if (connOptions && connOptions->requestConnWeight) {
        ret = GmcConnOptionsSetRequestWeight(connOptionsInner, connOptions->requestConnWeight);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "[testGmcConnect1] GmcConnOptionsSetRequestWeight failed, ret = %d.\n", ret);
            GmcConnOptionsDestroy(connOptionsInner);
            return ret;
        }
    }
    int n = 500;
    if (g_envType == 0) {
        pthread_mutex_lock(&g_connConcurrent);
        ++g_connRequest;
        while (g_connRequest > g_connOnline && g_connRequest - g_connOnline > 128 && n > 0) {
            usleep(10000);
            --n;
        }
        pthread_mutex_unlock(&g_connConcurrent);
    }

    GmcStmtT *st;
    do {
        ret = GmcConnect((GmcConnTypeE)syncMode, connOptionsInner, &conn);
        if (ret != GMERR_OK) {
            break;
        }
        *connOut = conn;
        if (stmt == NULL) {
            break;
        }
        ret = GmcAllocStmt(conn, &st);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "[testGmcConnect1] GmcAllocStmt failed, ret = %d.\n", ret);
            break;
        }
        *stmt = st;
    } while (0);
    GmcConnOptionsDestroy(connOptionsInner);
    if (ret == 0) {
        pthread_mutex_lock(&g_connLock);
        ++g_connOnline;
        pthread_mutex_unlock(&g_connLock);
        ret = testSetNameSpace(conn, syncMode);
    } else {
        pthread_mutex_lock(&g_connConcurrent);
        --g_connRequest;
        pthread_mutex_unlock(&g_connConcurrent);
    }
    return ret;
}
void test_setVertexPK(GmcStmtT *stmt, int index)
{
    int ret;
    uint32_t value7 = index;  // F7是PK
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT32, &value7, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
}
void test_setVertexProperty(GmcStmtT *stmt, int index)
{
    int ret;
    char teststr0 = 'a';
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_CHAR, &teststr0, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);
    unsigned char teststr1 = 'b';
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UCHAR, &teststr1, sizeof(unsigned char));
    EXPECT_EQ(GMERR_OK, ret);
    int8_t value2 = index;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &value2, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint16_t value5 = index;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &value5, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    int32_t value6 = index;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, &value6, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t value10 = index;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_UINT64, &value10, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    float value11 = (float)1.2 + (float)index;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_FLOAT, &value11, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);
    double value12 = 10.86 + index;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_DOUBLE, &value12, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t value13 = index;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_TIME, &value13, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    char teststr14[] = "string";
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, teststr14, (strlen(teststr14)));
    EXPECT_EQ(GMERR_OK, ret);
    char teststr15[10] = "bytes";
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_BYTES, teststr15, 5);
    EXPECT_EQ(GMERR_OK, ret);
    bool value8 = false;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &value8, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);
    int64_t value9 = index;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &value9, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t value3 = index;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &value3, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    int16_t value4 = index;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &value4, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);
    char teststr16[6] = "fixed";
    EXPECT_EQ(6, strlen(teststr16) + 1);
    ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_FIXED, teststr16, strlen(teststr16));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value17 = index;
    ret = GmcSetVertexProperty(stmt, "F17", GMC_DATATYPE_UINT32, &value17, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
}

void test_setVertexProperty_upd(GmcStmtT *stmt, int index)
{
    int ret;
    char teststr0 = 'a';
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_CHAR, &teststr0, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);
    unsigned char teststr1 = 'b';
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UCHAR, &teststr1, sizeof(unsigned char));
    EXPECT_EQ(GMERR_OK, ret);
    int8_t value2 = index;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &value2, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint16_t value5 = index;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &value5, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    int32_t value6 = index;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, &value6, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t value10 = index;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_UINT64, &value10, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    float value11 = (float)1.2 + (float)index;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_FLOAT, &value11, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);
    double value12 = 10.86 + index;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_DOUBLE, &value12, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t value13 = index;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_TIME, &value13, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    char teststr14[] = "string";
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, teststr14, (strlen(teststr14)));
    EXPECT_EQ(GMERR_OK, ret);
    char teststr15[10] = "bytes";
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_BYTES, teststr15, 5);
    EXPECT_EQ(GMERR_OK, ret);
    bool value8 = false;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &value8, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);
    int64_t value9 = index;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &value9, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t value3 = index;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &value3, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    int16_t value4 = index;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &value4, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);
    char teststr16[6] = "fixed";
    EXPECT_EQ(6, strlen(teststr16) + 1);
    ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_FIXED, teststr16, strlen(teststr16));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value17 = index;
    ret = GmcSetVertexProperty(stmt, "F17", GMC_DATATYPE_UINT32, &value17, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
}
void sn_callback_key_object(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret;
    int index, i;
    SnUserDataT *user_data = (SnUserDataT *)userData;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;
    void *label = 0;
    const void *keyValue = 0;
    uint32_t size;
    GmcConnT *conn_sync = 0;
    GmcStmtT *stmt_sync = 0;
    char keyName[MAX_NAME_LENGTH] = {0};

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }

        for (i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(strlen(labelName), labelNameLen);

            ret = testGmcConnect(&conn_sync, &stmt_sync);  // 创建同步连接进行读vertex
            EXPECT_EQ(GMERR_OK, ret);

            ret = testGmcPrepareStmtByLabelName(stmt_sync, labelName, GMC_OPERATION_SCAN);  // 同步stmt
            EXPECT_EQ(GMERR_OK, ret);

            ret = GmcGetPrimaryKeyName(stmt_sync, keyName, MAX_NAME_LENGTH);  // 同步stmt
            EXPECT_EQ(GMERR_OK, ret);

            switch (info->msgType) {
                case 4: {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_INSERT: {
                            // 读new object
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            EXPECT_EQ(GMERR_NO_DATA, ret);

                            // 读old object
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            EXPECT_EQ(GMERR_NO_DATA, ret);

                            // 读new
                            ret = GmcSubGetKey(subStmt, &keyValue, &size);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcSetIndexKeyValueBuffer(stmt_sync, keyValue, size);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcExecute(stmt_sync);
                            EXPECT_EQ(GMERR_OK, ret);

                            bool isFinish = true;
                            ret = GmcFetch(stmt_sync, &isFinish);
                            EXPECT_EQ(GMERR_OK, ret);

                            index = ((int *)user_data->new_value)[user_data->subIndex];
                            printf("[KEY] GMC_SUB_EVENT_INSERT new_value is %d\r\n", index);
                            (stmt_sync, index);
                            break;
                        }
                        case GMC_SUB_EVENT_DELETE: {
                            // 读new object
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            EXPECT_EQ(GMERR_NO_DATA, ret);

                            // 读old object
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            EXPECT_EQ(GMERR_NO_DATA, ret);

                            // 读old
                            ret = GmcSubGetKey(subStmt, &keyValue, &size);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcSetIndexKeyValueBuffer(stmt_sync, keyValue, size);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcExecute(stmt_sync);
                            EXPECT_EQ(GMERR_OK, ret);

                            bool isFinish = true;
                            ret = GmcFetch(stmt_sync, &isFinish);
                            EXPECT_EQ(GMERR_OK, ret);
                            EXPECT_EQ(isFinish, true);

                            index = ((int *)user_data->old_value)[user_data->subIndex];
                            printf("[KEY] GMC_SUB_EVENT_DELETE old_value is %d\r\n", index);
                            break;
                        }
                        default: {
                            printf("default: invalid eventType\r\n");
                            break;
                        }
                    }
                    break;
                }
                case 5: {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_UPDATE: {
                            // 读new object
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            EXPECT_EQ(GMERR_NO_DATA, ret);

                            // 读old object
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            EXPECT_EQ(GMERR_OK, ret);
                            index = ((int *)user_data->old_value)[user_data->subIndex];
                            printf("[OBJECT] GMC_SUB_EVENT_UPDATE old_value is %d\r\n", index);
                            (subStmt, index);

                            // 读new
                            ret = GmcSubGetKey(subStmt, &keyValue, &size);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcSetIndexKeyValueBuffer(stmt_sync, keyValue, size);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcExecute(stmt_sync);
                            EXPECT_EQ(GMERR_OK, ret);

                            bool isFinish = true;
                            ret = GmcFetch(stmt_sync, &isFinish);
                            EXPECT_EQ(GMERR_OK, ret);

                            index = ((int *)user_data->new_value)[user_data->subIndex];
                            printf("[KEY] GMC_SUB_EVENT_UPDATE new_value is %d\r\n", index);
                            (stmt_sync, index);
                            break;
                        }
                        case GMC_SUB_EVENT_DELETE: {
                            // 读new object
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            EXPECT_EQ(GMERR_NO_DATA, ret);

                            // 读old object
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            EXPECT_EQ(GMERR_OK, ret);
                            index = ((int *)user_data->old_value)[user_data->subIndex];
                            printf("[OBJECT] GMC_SUB_EVENT_DELETE old_value is %d\r\n", index);
                            (subStmt, index);

                            // 读old
                            ret = GmcSubGetKey(subStmt, &keyValue, &size);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcSetIndexKeyValueBuffer(stmt_sync, keyValue, size);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcExecute(stmt_sync);
                            EXPECT_EQ(GMERR_OK, ret);
                            bool isFinish = true;
                            ret = GmcFetch(stmt_sync, &isFinish);
                            EXPECT_EQ(GMERR_OK, ret);
                            EXPECT_EQ(isFinish, true);

                            index = ((int *)user_data->old_value)[user_data->subIndex];
                            printf("[KEY] GMC_SUB_EVENT_DELETE old_value is %d\r\n", index);
                            break;
                        }
                        case GMC_SUB_EVENT_REPLACE_INSERT:
                        case GMC_SUB_EVENT_MERGE_INSERT: {
                            // 读new object
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            EXPECT_EQ(GMERR_NO_DATA, ret);

                            // 读new
                            ret = GmcSubGetKey(subStmt, &keyValue, &size);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcSetIndexKeyValueBuffer(stmt_sync, keyValue, size);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcExecute(stmt_sync);
                            EXPECT_EQ(GMERR_OK, ret);
                            bool isFinish = true;
                            ret = GmcFetch(stmt_sync, &isFinish);
                            EXPECT_EQ(GMERR_OK, ret);
                            index = ((int *)user_data->new_value)[user_data->subIndex];
                            printf("[KEY] GMC_SUB_EVENT_REPLACE_INSERT new_value is %d\r\n", index);
                            (stmt_sync, index);
                            break;
                        }
                        case GMC_SUB_EVENT_REPLACE_UPDATE:
                        case GMC_SUB_EVENT_MERGE_UPDATE: {
                            // 读new object
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            EXPECT_EQ(GMERR_NO_DATA, ret);

                            // 读new
                            ret = GmcSubGetKey(subStmt, &keyValue, &size);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcSetIndexKeyValueBuffer(stmt_sync, keyValue, size);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcExecute(stmt_sync);
                            EXPECT_EQ(GMERR_OK, ret);
                            bool isFinish = true;
                            ret = GmcFetch(stmt_sync, &isFinish);
                            EXPECT_EQ(GMERR_OK, ret);
                            index = ((int *)user_data->new_value)[user_data->subIndex];
                            printf("[KEY] GMC_SUB_EVENT_REPLACE_UPDATE new_value is %d\r\n", index);
                            (stmt_sync, index);
                            break;
                        }
                        default: {
                            printf("default: invalid eventType\r\n");
                            break;
                        }
                    }
                    break;
                }
                case 6: {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_INSERT: {
                            // 读new object
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            EXPECT_EQ(GMERR_OK, ret);
                            index = ((int *)user_data->new_value)[user_data->subIndex];
                            printf("[OBJECT] GMC_SUB_EVENT_INSERT new_value is %d\r\n", index);
                            (subStmt, index);

                            // 读old object
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            EXPECT_EQ(GMERR_NO_DATA, ret);

                            // 读new
                            ret = GmcSubGetKey(subStmt, &keyValue, &size);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcSetIndexKeyValueBuffer(stmt_sync, keyValue, size);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcExecute(stmt_sync);
                            EXPECT_EQ(GMERR_OK, ret);
                            bool isFinish = true;
                            ret = GmcFetch(stmt_sync, &isFinish);
                            EXPECT_EQ(GMERR_OK, ret);
                            index = ((int *)user_data->new_value)[user_data->subIndex];
                            printf("[KEY] GMC_SUB_EVENT_INSERT new_value is %d\r\n", index);
                            (stmt_sync, index);

                            break;
                        }
                        case GMC_SUB_EVENT_UPDATE: {
                            // 读new object
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            EXPECT_EQ(GMERR_OK, ret);
                            index = ((int *)user_data->new_value)[user_data->subIndex];
                            printf("[OBJECT] GMC_SUB_EVENT_UPDATE new_value is %d\r\n", index);
                            (subStmt, index);

                            // 读old object
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            EXPECT_EQ(GMERR_NO_DATA, ret);

                            // 读new key
                            ret = GmcSubGetKey(subStmt, &keyValue, &size);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcSetIndexKeyValueBuffer(stmt_sync, keyValue, size);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcExecute(stmt_sync);
                            EXPECT_EQ(GMERR_OK, ret);
                            bool isFinish = true;
                            ret = GmcFetch(stmt_sync, &isFinish);
                            EXPECT_EQ(GMERR_OK, ret);
                            index = ((int *)user_data->new_value)[user_data->subIndex];
                            printf("[KEY] GMC_SUB_EVENT_UPDATE new_value is %d\r\n", index);
                            (stmt_sync, index);
                            break;
                        }
                        case GMC_SUB_EVENT_REPLACE_INSERT:
                        case GMC_SUB_EVENT_MERGE_INSERT: {
                            // 读new object
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            EXPECT_EQ(GMERR_OK, ret);
                            index = ((int *)user_data->new_value)[user_data->subIndex];
                            printf("[OBJECT] GMC_SUB_EVENT_REPLACE_INSERT new_value is %d\r\n", index);
                            (subStmt, index);

                            // 读new
                            ret = GmcSubGetKey(subStmt, &keyValue, &size);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcSetIndexKeyValueBuffer(stmt_sync, keyValue, size);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcExecute(stmt_sync);
                            EXPECT_EQ(GMERR_OK, ret);
                            bool isFinish = true;
                            ret = GmcFetch(stmt_sync, &isFinish);
                            EXPECT_EQ(GMERR_OK, ret);
                            index = ((int *)user_data->new_value)[user_data->subIndex];
                            printf("[KEY] GMC_SUB_EVENT_REPLACE_INSERT new_value is %d\r\n", index);
                            (stmt_sync, index);
                            break;
                        }
                        case GMC_SUB_EVENT_REPLACE_UPDATE:
                        case GMC_SUB_EVENT_MERGE_UPDATE: {
                            // 读new object
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            EXPECT_EQ(GMERR_OK, ret);
                            index = ((int *)user_data->new_value)[user_data->subIndex];
                            printf("[OBJECT] GMC_SUB_EVENT_REPLACE_UPDATE new_value is %d\r\n", index);
                            (subStmt, index);
                            // 读new
                            ret = GmcSubGetKey(subStmt, &keyValue, &size);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcSetIndexKeyValueBuffer(stmt_sync, keyValue, size);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcExecute(stmt_sync);
                            EXPECT_EQ(GMERR_OK, ret);
                            bool isFinish = true;
                            ret = GmcFetch(stmt_sync, &isFinish);
                            EXPECT_EQ(GMERR_OK, ret);
                            index = ((int *)user_data->new_value)[user_data->subIndex];
                            printf("[KEY] GMC_SUB_EVENT_REPLACE_UPDATE new_value is %d\r\n", index);
                            (stmt_sync, index);
                            break;
                        }
                        default: {
                            printf("default: invalid eventType\r\n");
                            break;
                        }
                    }
                    break;
                }
                case 7: {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_UPDATE: {
                            // 读new object
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            EXPECT_EQ(GMERR_OK, ret);
                            index = ((int *)user_data->new_value)[user_data->subIndex];
                            printf("[OBJECT] GMC_SUB_EVENT_UPDATE new_value is %d\r\n", index);
                            (subStmt, index);

                            // 读old object
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            EXPECT_EQ(GMERR_OK, ret);
                            index = ((int *)user_data->old_value)[user_data->subIndex];
                            printf("[OBJECT] GMC_SUB_EVENT_UPDATE old_value is %d\r\n", index);
                            (subStmt, index);

                            // 读new
                            ret = GmcSubGetKey(subStmt, &keyValue, &size);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcSetIndexKeyValueBuffer(stmt_sync, keyValue, size);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcExecute(stmt_sync);
                            EXPECT_EQ(GMERR_OK, ret);
                            bool isFinish = true;
                            ret = GmcFetch(stmt_sync, &isFinish);
                            EXPECT_EQ(GMERR_OK, ret);
                            index = ((int *)user_data->new_value)[user_data->subIndex];
                            printf("[KEY] GMC_SUB_EVENT_UPDATE new_value is %d\r\n", index);
                            (stmt_sync, index);
                            break;
                        }
                        case GMC_SUB_EVENT_REPLACE_INSERT:
                        case GMC_SUB_EVENT_MERGE_INSERT: {
                            // 读new object
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            EXPECT_EQ(GMERR_OK, ret);
                            index = ((int *)user_data->new_value)[user_data->subIndex];
                            printf("[OBJECT] GMC_SUB_EVENT_REPLACE_INSERT new_value is %d\r\n", index);
                            (subStmt, index);

                            // 读new
                            ret = GmcSubGetKey(subStmt, &keyValue, &size);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcSetIndexKeyValueBuffer(stmt_sync, keyValue, size);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcExecute(stmt_sync);
                            EXPECT_EQ(GMERR_OK, ret);
                            bool isFinish = true;
                            ret = GmcFetch(stmt_sync, &isFinish);
                            EXPECT_EQ(GMERR_OK, ret);
                            index = ((int *)user_data->new_value)[user_data->subIndex];
                            printf("[KEY] GMC_SUB_EVENT_REPLACE_INSERT new_value is %d\r\n", index);
                            (stmt_sync, index);
                            break;
                        }
                        case GMC_SUB_EVENT_REPLACE_UPDATE:
                        case GMC_SUB_EVENT_MERGE_UPDATE: {
                            // 读new object
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            EXPECT_EQ(GMERR_OK, ret);
                            index = ((int *)user_data->new_value)[user_data->subIndex];
                            printf("[OBJECT] GMC_SUB_EVENT_REPLACE_UPDATE new_value is %d\r\n", index);
                            (subStmt, index);

                            // 读new
                            ret = GmcSubGetKey(subStmt, &keyValue, &size);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcSetIndexKeyValueBuffer(stmt_sync, keyValue, size);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcExecute(stmt_sync);
                            EXPECT_EQ(GMERR_OK, ret);
                            bool isFinish = true;
                            ret = GmcFetch(stmt_sync, &isFinish);
                            EXPECT_EQ(GMERR_OK, ret);
                            index = ((int *)user_data->new_value)[user_data->subIndex];
                            printf("[KEY] GMC_SUB_EVENT_REPLACE_UPDATE new_value is %d\r\n", index);
                            (stmt_sync, index);
                            break;
                        }
                        default: {
                            printf("default: invalid eventType\r\n");
                            break;
                        }
                    }
                    break;
                }
                default: {
                    printf("default: invalid msgType\r\n");
                    break;
                }
            }
            ret = testGmcDisconnect(conn_sync, stmt_sync);
            EXPECT_EQ(GMERR_OK, ret);
        }
        user_data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE_INSERT: {
                user_data->replaceInsertNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE_UPDATE: {
                user_data->replaceUpdateNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE_INSERT: {
                user_data->mergeInsertNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE_UPDATE: {
                user_data->mergeUpdateNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
            default: {
                printf("default: eventType\r\n");
                break;
            }
        }
    }
}

int getSysViewValue(char cmd[1024], char word[128], int *num, bool *checkSigns)
{
    char *result = NULL;
    FILE *fd = popen(cmd, "r");
    if (fd == NULL) {
        printf("\n open fd error \n");
        return -1;
    }

    int size = 1024 * 100;
    char *tmpResult = (char *)malloc(sizeof(char) * size);
    if (tmpResult == NULL) {
        printf("\n malloc mem error \n");
        return -1;
    }
    memset(tmpResult, 0, size);
    uint32_t cnts = 0;
    bool signs = false;
    char buf[1024] = {0};
    char line[1024] = {0};
    char *tmp = NULL;
    char *token;
    while (fgets(line, sizeof(line), fd) != NULL) {
        if (strstr(line, word) != NULL) {
            signs = true;
            token = strchr(line, ':');
            if (token != NULL) {
                token++;
                cnts = atoi(token);
            }
        }
    }
    *num = cnts;
    *checkSigns = signs;
    int ret = pclose(fd);
    if (ret == -1) {
        printf("\n pclose error \n");
        free(tmpResult);
        return -1;
    }
    free(tmpResult);
    return 0;
}

#endif
