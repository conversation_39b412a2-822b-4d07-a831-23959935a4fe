/* ****************************************************************************
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: NewSubDML_03.cpp
 * Description: 状态合并DML3
 * Author: ywx1037054
 * Create: 2023-03-11
 **************************************************************************** */
#include <stdlib.h>
#include <stdio.h>
#include <unistd.h>
#include <sys/types.h>
#include "gtest/gtest.h"
#include "NewSubDML.h"

class NewSubDML_Null : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/modifyCfg.sh \"compatibleV3=1\" ");
        system("sh ${TEST_HOME}/tools/start.sh -f");
        int ret = testEnvInit();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        create_epoll_thread();
    }
    static void TearDownTestCase()
    {
        GmcDetachAllShmSeg();
        close_epoll_thread();
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void NewSubDML_Null::SetUp()
{
    g_registerCount = 0;
    int ret = TestTryRegisterSignal(TestCrashHandler);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_registerCount, 1);
}

void NewSubDML_Null::TearDown()
{}

void *p3(void *args)
{
    raise(SIGFPE);
    return NULL;
}

int check_process(char *processName) {
    int ret_t;
    char g_cmd[1024]={0};
    (void)sprintf(g_cmd, "ps -ef |grep %s|grep -v grep | wc -l", processName);
    ret_t = executeCommand(g_cmd, "1");
    AW_FUN_Log(LOG_STEP, "check_process %d", ret_t);
    return ret_t;
}


// 006.[AKA]使用保护测试套建连
TEST_F(NewSubDML_Null, SN_034_003_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcConnT *conn = NULL, *conn_sub = NULL;
    GmcStmtT *stmt = NULL, *stmt_sub = NULL;
    for (int i = 0; i < 10; i++) {
        ret = testGmcConnect(&conn, &stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcDisconnect(conn, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 007.[AKA] 客户端保护线程接口做subDML操作
TEST_F(NewSubDML_Null, SN_034_003_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *subName = "newSub001";
    GmcConnT *conn = NULL, *conn_sub = NULL;
    GmcStmtT *stmt = NULL, *stmt_sub = NULL;
    int ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SnUserDataT *userData = NULL;
    ret = testSnMallocUserData(&userData, 100, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcUnSubscribe(stmt, subName);
    GmcDropVertexLabel(stmt, "V_1");
    readJanssonFile("./schema_file/NewSubV_1.gmjson", &g_schema);
    AW_MACRO_ASSERT_NE_INT((void *)NULL, g_schema);
    ret = GmcCreateVertexLabel(stmt, g_schema, g_tableConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubConnect(&conn_sub, &stmt_sub, 1, g_epoll_reg_info, subName, &g_chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubConfigT tsub_info;
    tsub_info.subsName = subName;
    tsub_info.configJson = g_newSubInfoAdd;
    ret = GmcSubscribe(stmt, &tsub_info, conn_sub, NewSnCallBack2, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = NewSub_Vertex_DML(conn, stmt, (char *)"V_1", SINGLE_INSERT, g_ezdata, 10, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    usleep(1000);
    ret = NewSub_Vertex_DML(conn, stmt, (char *)"V_1", SINGLE_DELETE, g_ezdata, 5, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    usleep(1000);
    ret = NewSub_Vertex_DML(conn, stmt, (char *)"V_1", SINGLE_UPDATE, g_ezdata, 5, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    usleep(1000);
    int ex_times = 10;
    ret = testWaitStMgSnRecv(userData, GMC_SUB_EVENT_MODIFY, ex_times/2, ex_times);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(conn_sub, stmt_sub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, "V_1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_schema);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 008.[AKA] 使用namespace建表
TEST_F(NewSubDML_Null, SN_034_003_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcConnT *conn = NULL, *conn_sub = NULL;
    GmcStmtT *stmt = NULL, *stmt_sub = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        ret = GmcCreateNamespace(stmt, "subs", "abc");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcUseNamespace(stmt, "subs");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcDropNamespace(stmt, "subs");
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 009.[AKA] 创建KV表并写入数据删除数据删除表
TEST_F(NewSubDML_Null, SN_034_003_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcConnT *conn = NULL, *conn_sub = NULL;
    GmcStmtT *stmt = NULL, *stmt_sub = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char key[128] = "zhangsan";
    char configJson[128] = "{\"max_record_count\" : 10000}";
    char tableName[128] = "KVsub";
    for (int i = 0; i < 10; i++) {
        ret = GmcCreateNamespace(stmt, "subs", "abc");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcUseNamespace(stmt, "subs");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcKvCreateTable(stmt, tableName, configJson);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcKvPrepareStmtByLabelName(stmt, tableName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (i = 0; i < 10; i++) {
            sprintf(key, "zhangsan_%d", i);
            int value = i;
            GmcKvTupleT kvInfo = {0};
            kvInfo.key = key;
            kvInfo.keyLen = strlen(key);
            kvInfo.value = &value;
            kvInfo.valueLen = sizeof(int32_t);
            ret = GmcKvSet(stmt, key, strlen(key), &value, sizeof(int32_t));
            EXPECT_EQ(GMERR_OK, ret);
        }
        for (i = 0; i < 10; i++) {
            sprintf(key, "zhangsan_%d", i);
            ret = GmcKvRemove(stmt, key, strlen(key));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcKvDropTable(stmt, tableName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcDropNamespace(stmt, "subs");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010.[AKA] 创建tree表并写入数据删除数据删除表
TEST_F(NewSubDML_Null, SN_034_003_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcConnT *conn = NULL, *conn_sub = NULL;
    GmcStmtT *stmt = NULL, *stmt_sub = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, g_treeschema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestGmcInsertVertexdifferent(stmt, 1, 0, (char *)"string", g_startNum, g_endNum, g_arrayNum, g_vectorNum, "OP_T0");

    ret = GmcDropVertexLabel(stmt, "OP_T0");
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 011.[AKA] 使用gmimport导入工具
TEST_F(NewSubDML_Null, SN_034_003_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcConnT *conn = NULL, *conn_sub = NULL;
    GmcStmtT *stmt = NULL, *stmt_sub = NULL;
    char cmd[1024];
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    snprintf(cmd, 1024, "gmimport -c cache  -f ./schema_file/");
    ret = executeCommand(cmd, "successfully");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, "TEST_SC_T1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, "OP_T0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, "ip4forward");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, "TypicalVertexLabel");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, "V_1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, "T20_all_type");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 012.[AKA] 使用gmsysview工具
TEST_F(NewSubDML_Null, SN_034_003_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcConnT *conn = NULL, *conn_sub = NULL;
    GmcStmtT *stmt = NULL, *stmt_sub = NULL;
    char cmd[1024];
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 10; i ++) {
        ret = executeCommand((char *)"gmsysview -q V$\\DB_SERVER", (char *)"BUILD_MODE");
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 013.[AKA] 订阅操作下发送USR1信号[子进程没有接收到回调推送数据]
TEST_F(NewSubDML_Null, SN_034_003_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t g_testCaseNo = 1;
    pid_t mainPid = getpid();
    AW_FUN_Log(LOG_INFO, "mainPid is %d \n", mainPid);
    int ret = GtExecSystemCmd("./NewSubDML_03_process_01 &");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    usleep(100);
    for (int i = 0; i < 10; i++) {
        ret = check_process((char *)"NewSubDML_03_process_01");
        if (ret == GMERR_OK ) {
            ret = GtExecSystemCmd("kill -s USR1 `pidof NewSubDML_03_process_01`");
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            break;
        } else {
            sleep(2);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 014.[AKA] 订阅操作下发送USR2信号[子进程没有接收到回调推送数据]
// 后续补子进程也纳入保护测试套
TEST_F(NewSubDML_Null, SN_034_003_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t g_testCaseNo = 1;
    pid_t mainPid = getpid();
    AW_FUN_Log(LOG_INFO, "mainPid is %d \n", mainPid);
    // 等待epoll资源成功创建
    sleep(1);
    int ret = GtExecSystemCmd("./NewSubDML_03_process_01 &");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    usleep(100);
    for (int i =0; i < 10; i++) {
        ret = check_process((char *)"NewSubDML_03_process_01");
        if (ret == GMERR_OK ) {
            ret = GtExecSystemCmd("kill -s USR2 `pidof NewSubDML_03_process_01`");
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            break;
        } else {
            sleep(2);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 015.[AKA] 订阅操作下发送TERM信号[子进程没有接收到回调推送数据]
TEST_F(NewSubDML_Null, SN_034_003_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t g_testCaseNo = 1;
    pid_t mainPid = getpid();
    AW_FUN_Log(LOG_INFO, "mainPid is %d \n", mainPid);
    int ret = GtExecSystemCmd("./NewSubDML_03_process_01 &");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    usleep(100);
    for (int i =0; i < 10; i++) {
        ret = check_process((char *)"NewSubDML_03_process_01");
        if (ret == GMERR_OK ) {
            ret = GtExecSystemCmd("kill -s TERM `pidof NewSubDML_03_process_01`");
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            break;
        } else {
            sleep(2);
        }
    }
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 016.[AKA] 订阅操作下发送KILL信号
TEST_F(NewSubDML_Null, SN_034_003_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t g_testCaseNo = 1;
    pid_t mainPid = getpid();
    AW_FUN_Log(LOG_INFO, "mainPid is %d \n", mainPid);
    int ret = GtExecSystemCmd("./NewSubDML_03_process_01 &");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    usleep(100);
    for (int i = 0; i < 10; i++) {
        ret = check_process((char *)"NewSubDML_03_process_01");
        if (ret == GMERR_OK ) {
            ret = GtExecSystemCmd("kill -s KILL `pidof NewSubDML_03_process_01`");
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            break;
        } else {
            sleep(2);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 017.[AKA] 订阅操作下发送INT信号
TEST_F(NewSubDML_Null, SN_034_003_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t g_testCaseNo = 1;
    pid_t mainPid = getpid();
    AW_FUN_Log(LOG_INFO, "mainPid is %d \n", mainPid);
    int ret = GtExecSystemCmd("./NewSubDML_03_process_01 &");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    usleep(100);
    for (int i = 0; i < 10; i++) {
        ret = check_process((char *)"NewSubDML_03_process_01");
        if (ret == GMERR_OK ) {
            ret = GtExecSystemCmd("kill -s 9 `pidof NewSubDML_03_process_01`");
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            break;
        } else {
            sleep(2);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 018.[AKA] 订阅操作下发送BUS信号
TEST_F(NewSubDML_Null, SN_034_003_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t g_testCaseNo = 1;
    pid_t mainPid = getpid();
    AW_FUN_Log(LOG_INFO, "mainPid is %d \n", mainPid);
    int ret = GtExecSystemCmd("./NewSubDML_03_process_01 &");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    usleep(100);
    for (int i = 0; i < 10; i++) {
        ret = check_process((char *)"NewSubDML_03_process_01");
        if (ret == GMERR_OK ) {
            ret = GtExecSystemCmd("kill -s BUS `pidof NewSubDML_03_process_01`");
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            break;
        } else {
            sleep(2);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 019.[AKA] 无内部操作下发送TERM信号
// 外部进程发送信号，此时无共享内存区域操作，客户端异常处理函数返回错误类型为false
TEST_F(NewSubDML_Null, SN_034_003_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t g_testCaseNo = 1;
    pid_t mainPid = getpid();
    AW_FUN_Log(LOG_INFO, "mainPid is %d \n", mainPid);
    int ret = GtExecSystemCmd("./NewSubDML_03_process_02 &");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        ret = check_process((char *)"NewSubDML_03_process_02");
        if (ret == GMERR_OK ) {
            ret = GtExecSystemCmd("kill -s TERM `pidof NewSubDML_03_process_02`");
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            break;
        } else {
            sleep(2);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 020.[AKA]内部进程产生信号，此时无共享内存区域操作
TEST_F(NewSubDML_Null, SN_034_003_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t g_testCaseNo = 1;
    pid_t mainPid = getpid();
    AW_FUN_Log(LOG_INFO, "mainPid is %d \n", mainPid);
    int ret = 0;
    pthread_t pid = 0;
    ret = pthread_create(&pid, NULL, p3, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_join(pid, NULL);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 021.[AKA] 2个客户端都对同一个表做简单订阅，两个预期接收数据均为最后一条且不影响
// 不使用fork
// 后续计划-》完全重新初始化客户端
TEST_F(NewSubDML_Null, SN_034_003_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t g_testCaseNo = 1;
    pid_t mainPid = getpid();
    AW_FUN_Log(LOG_INFO, "mainPid is %d \n", mainPid);
    int ret = 0;
    const char *subName = "newSub001";
    GmcConnT *conn = NULL, *conn_sub = NULL;
    GmcStmtT *stmt = NULL, *stmt_sub = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcUnSubscribe(stmt, subName);
    GmcDropVertexLabel(stmt, "V_1");
    readJanssonFile("./schema_file/NewSubV_1.gmjson", &g_schema);
    AW_MACRO_ASSERT_NE_INT((void *)NULL, g_schema);
    ret = GmcCreateVertexLabel(stmt, g_schema, g_tableConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_schema);
    ret = NewSub_Vertex_DML(conn, stmt, (char *)"V_1", SINGLE_INSERT, g_ezdata, 1000, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = NewSub_Vertex_DML(conn, stmt, (char *)"V_1", SINGLE_UPDATE, g_ezdata, 1000, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSystemCmd("./NewSubDML_03_process_03 &");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSystemCmd("./NewSubDML_03_process_04 &");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    usleep(100);
    for (int i =0; i < 10; i++) {
        ret = check_process((char *)"NewSubDML_03_process_03");
        if (ret == GMERR_OK ) {
            ret = GtExecSystemCmd("kill -s TERM `pidof NewSubDML_03_process_03`");
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            break;
        } else {
            sleep(2);
        }
    }
    for (int i =0; i < 10; i++) {
        ret = check_process((char *)"NewSubDML_03_process_04");
        if (ret == GMERR_OK ) {
            ret = GtExecSystemCmd("kill -s TERM `pidof NewSubDML_03_process_04`");
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            break;
        } else {
            sleep(2);
        }
    }
    GmcDropVertexLabel(stmt, "V_1");
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 022.[AKA] 2个客户端都对同一个表做简单订阅，1生产1消费[不稳定]
TEST_F(NewSubDML_Null, SN_034_003_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t g_testCaseNo = 1;
    pid_t mainPid = getpid();
    AW_FUN_Log(LOG_INFO, "mainPid is %d \n", mainPid);
    int ret = 0;
    const char *subName = "newSub001";
    GmcConnT *conn = NULL, *conn_sub = NULL;
    GmcStmtT *stmt = NULL, *stmt_sub = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SnUserDataT *userData = NULL;
    GmcUnSubscribe(stmt, subName);
    GmcDropVertexLabel(stmt, "V_1");
    readJanssonFile("./schema_file/NewSubV_1.gmjson", &g_schema);
    AW_MACRO_ASSERT_NE_INT((void *)NULL, g_schema);
    ret = GmcCreateVertexLabel(stmt, g_schema, g_tableConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSystemCmd("./NewSubDML_03_process_03 > NewSubDML_03_process_03.log &");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSystemCmd("./NewSubDML_03_process_05 > NewSubDML_03_process_05.log &");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(10);
    GmcDropVertexLabel(stmt, "V_1");
    free(g_schema);
    // 清理
    GtExecSystemCmd("kill -9 `pidof NewSubDML_03_process_03`");
    GtExecSystemCmd("kill -9 `pidof NewSubDML_03_process_05`");
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 023.[AKA] 3个客户端都对同一个表做简单订阅，1生产1消费,1个杀掉生产进程
TEST_F(NewSubDML_Null, SN_034_003_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t g_testCaseNo = 1;
    pid_t mainPid = getpid();
    AW_FUN_Log(LOG_INFO, "mainPid is %d \n", mainPid);
    int ret = 0;
    const char *subName = "newSub001";
    GmcConnT *conn = NULL, *conn_sub = NULL;
    GmcStmtT *stmt = NULL, *stmt_sub = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcUnSubscribe(stmt, subName);
    GmcDropVertexLabel(stmt, "V_1");
    readJanssonFile("./schema_file/NewSubV_1.gmjson", &g_schema);
    AW_MACRO_ASSERT_NE_INT((void *)NULL, g_schema);
    ret = GmcCreateVertexLabel(stmt, g_schema, g_tableConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_schema);
    ret = GtExecSystemCmd("./NewSubDML_03_process_03 > NewSubDML_03_process_03.log &");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSystemCmd("./NewSubDML_03_process_05 > NewSubDML_03_process_05.log & ");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    usleep(100);
    for (int i =0; i < 10; i++) {
        ret = check_process((char *)"NewSubDML_03_process_03");
        if (ret == GMERR_OK ) {
            ret = GtExecSystemCmd("kill -9 `pidof NewSubDML_03_process_03`");
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            break;
        } else {
            sleep(2);
        }
    }
    GmcDropVertexLabel(stmt, "V_1");
    AW_FUN_Log(LOG_STEP, "test end.");
}
