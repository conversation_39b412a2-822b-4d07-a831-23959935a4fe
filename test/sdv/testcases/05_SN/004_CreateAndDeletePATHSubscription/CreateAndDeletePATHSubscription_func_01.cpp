/*****************************************************************************
 Description  : PATH订阅关系的创建和删除
 Notes        :
SN_004_015 订阅的label与cypher语句中的label有关联（订阅的label与cypher中没有相同的label），cypher中关联关系不正确
SN_004_016 订阅的label与cypher语句中的label有关联（订阅的label与cypher中没有相同的label），cypher中关联关系正确
SN_004_017 订阅的label与cypher语句中的label无关联（订阅的label与cypher中没有相同的label），cypher中关联关系不正确
SN_004_018 订阅的label与cypher语句中的label无关联（订阅的label与cypher中没有相同的label），cypher中关联关系正确
 History      :
 Author       : 吴雪琦 00495442
 Modification :
 Date         : 2020/10/22
*****************************************************************************/

extern "C" {
}

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "../002_CreateAndDeleteFullLabelSubscription/sub_tools.h"

char g_label_name[] = "T10", g_label_name_2[] = "T20_all_type", g_label_name_3[] = "T40_all_type_nullable";
char g_lable_PK[] = "T10_PK", g_lable_PK_2[] = "T20_PK", g_lable_PK_3[] = "T40_PK";
const char *g_subName = "subVertexLabel";
const char *g_subConnName = "subConnName";

using namespace std;

class CreateAndDeletePATHSubscription_func : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh");
        int ret;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret;
        ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void CreateAndDeletePATHSubscription_func::SetUp()
{
    printf("CreateAndDeletePATHSubscription_func Start.\n");
    g_schema = NULL;
    g_label = NULL;
    g_schema_2 = NULL;
    g_label_2 = NULL;
    g_schema_3 = NULL;
    g_label_3 = NULL;
    g_conn_sync = NULL;
    g_stmt_sync = NULL;
    g_sub_info = NULL;
    g_subChan = NULL;
    int ret;

    //创建同步连接
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);

    //创建label1
    readJanssonFile("schema_file/simple_schema.gmjson", &g_schema);
    EXPECT_NE((void *)NULL, g_schema);
    // printf("g_schema: %s\r\n", g_schema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    //创建label2
    readJanssonFile("schema_file/all_type_schema.gmjson", &g_schema_2);
    EXPECT_NE((void *)NULL, g_schema_2);
    // printf("g_schema: %s\r\n", g_schema_2);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schema_2, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    //创建label3
    readJanssonFile("schema_file/all_type_nullable_schema.gmjson", &g_schema_3);
    EXPECT_NE((void *)NULL, g_schema_3);
    // printf("g_schema: %s\r\n", g_schema_3);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schema_3, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testSubConnect(&g_subChan, NULL, 1, g_epoll_reg_info, g_subConnName);
    EXPECT_EQ(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN();
}
void CreateAndDeletePATHSubscription_func::TearDown()
{
    AW_CHECK_LOG_END();
    int ret;
    // 释放订阅连接通道
    ret = testGmcDisconnect(g_subChan);
    EXPECT_EQ(GMERR_OK, ret);
    test_close_and_drop_label(g_stmt_sync, g_label, g_label_name);
    test_close_and_drop_label(g_stmt_sync, g_label_2, g_label_name_2);
    test_close_and_drop_label(g_stmt_sync, g_label_3, g_label_name_3);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_schema);
    free(g_schema_2);
    free(g_schema_3);
    printf("CreateAndDeletePATHSubscription_func End.\n");
}

//订阅的label与cypher语句中的label有关联（订阅的label与cypher中没有相同的label），cypher中关联关系不正确
TEST_F(CreateAndDeletePATHSubscription_func, SN_004_015)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    int ret;
    char *sub_info = NULL;

    char EdgeLabelName[] = "from_T10_to_T20";
    char EdgeLabelName2[] = "from_T20_to_T40";
    char EdgeLabel_config[] = "{\"max_record_count\":1000}";
    void *edgelabel = NULL, *edgelabel2 = NULL;
    char *edge_label_schema_and = NULL, *edge_label_schema_and2 = NULL;
    const char *subName = "subVertexLabel3";
    uint8_t pk1 = 0;
    uint32_t pk2 = 0, pk3 = 0;

    readJanssonFile("schema_file/edge_insert_schema.gmjson", &edge_label_schema_and);
    EXPECT_NE((void *)NULL, edge_label_schema_and);
    printf("edge_label_schema_and: %s\r\n", edge_label_schema_and);
    readJanssonFile("schema_file/edge_insert_schema2.gmjson", &edge_label_schema_and2);
    EXPECT_NE((void *)NULL, edge_label_schema_and2);
    printf("edge_label_schema_and: %s\r\n", edge_label_schema_and2);

    // insert edgeT10-T20
    ret = GmcCreateEdgeLabel(g_stmt_sync, edge_label_schema_and, EdgeLabel_config);
    EXPECT_EQ(GMERR_OK, ret);

    // insert edgeT20-T40
    ret = GmcCreateEdgeLabel(g_stmt_sync, edge_label_schema_and2, EdgeLabel_config);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schema_file/simple_schema_subinfo2.gmjson", &sub_info);
    EXPECT_NE((void *)NULL, sub_info);
    printf("sub_info: %s\r\n", sub_info);

    GmcSubConfigT tmp_sub_info;
    tmp_sub_info.subsName = g_subName;
    tmp_sub_info.configJson = sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_sub_info, g_subChan, sn_callback_NULL, NULL);
    EXPECT_EQ(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    free(sub_info);

    //删除edge
    ret = GmcDropEdgeLabel(g_stmt_sync, EdgeLabelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropEdgeLabel(g_stmt_sync, EdgeLabelName2);
    EXPECT_EQ(GMERR_OK, ret);
    free(edge_label_schema_and);
    free(edge_label_schema_and2);
}

//订阅的label与cypher语句中的label有关联（订阅的label与cypher中没有相同的label），cypher中关联关系正确
TEST_F(CreateAndDeletePATHSubscription_func, SN_004_016)
{
    int ret;
    char *sub_info = NULL;

    char EdgeLabelName[] = "from_T10_to_T20";
    char EdgeLabelName2[] = "from_T20_to_T40";
    char EdgeLabel_config[] = "{\"max_record_count\":1000}";
    void *edgelabel = NULL, *edgelabel2 = NULL;
    char *edge_label_schema_and = NULL, *edge_label_schema_and2 = NULL;
    uint8_t pk1 = 0;
    uint32_t pk2 = 0, pk3 = 0;

    readJanssonFile("schema_file/edge_insert_schema.gmjson", &edge_label_schema_and);
    EXPECT_NE((void *)NULL, edge_label_schema_and);
    printf("edge_label_schema_and: %s\r\n", edge_label_schema_and);
    readJanssonFile("schema_file/edge_insert_schema2.gmjson", &edge_label_schema_and2);
    EXPECT_NE((void *)NULL, edge_label_schema_and2);
    printf("edge_label_schema_and: %s\r\n", edge_label_schema_and2);

    // insert edgeT10-T20
    ret = GmcCreateEdgeLabel(g_stmt_sync, edge_label_schema_and, EdgeLabel_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcOpenEdgeLabelByName(g_stmt_sync, EdgeLabelName, &edgelabel);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexName(g_stmt_sync, g_lable_PK);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT8, &pk1, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexName(g_stmt_sync, g_lable_PK_2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &pk2, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcInsertEdge(g_stmt_sync, edgelabel);
    EXPECT_EQ(GMERR_OK, ret);

    // insert edgeT20-T40
    ret = GmcCreateEdgeLabel(g_stmt_sync, edge_label_schema_and2, EdgeLabel_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcOpenEdgeLabelByName(g_stmt_sync, EdgeLabelName2, &edgelabel2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexName(g_stmt_sync, g_lable_PK_2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &pk2, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexName(g_stmt_sync, g_lable_PK_3);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &pk3, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcInsertEdge(g_stmt_sync, edgelabel2);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schema_file/simple_schema_subinfo3.gmjson", &sub_info);
    EXPECT_NE((void *)NULL, sub_info);
    printf("sub_info: %s\r\n", sub_info);

    GmcSubConfigT tmp_sub_info;
    tmp_sub_info.subsName = g_subName;
    tmp_sub_info.configJson = sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_sub_info, g_subChan, sn_callback_NULL, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(sub_info);
    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);

    //删除edge
    ret = GmcCloseEdgeLabel(g_stmt_sync, edgelabel);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropEdgeLabel(g_stmt_sync, EdgeLabelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCloseEdgeLabel(g_stmt_sync, edgelabel2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropEdgeLabel(g_stmt_sync, EdgeLabelName2);
    EXPECT_EQ(GMERR_OK, ret);
    free(edge_label_schema_and);
    free(edge_label_schema_and2);
}

//订阅的label与cypher语句中的label无关联（订阅的label与cypher中没有相同的label），cypher中关联关系不正确
TEST_F(CreateAndDeletePATHSubscription_func, SN_004_017)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_UNDEFINED_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    int ret;
    char *sub_info = NULL;

    char EdgeLabelName[] = "from_T10_to_T20";
    char EdgeLabelName2[] = "from_T20_to_T40";
    char EdgeLabel_config[] = "{\"max_record_count\":1000}";
    void *edgelabel = NULL, *edgelabel2 = NULL;
    char *edge_label_schema_and = NULL, *edge_label_schema_and2 = NULL;
    const char *subName = "subVertexLabel3";
    uint8_t pk1 = 0;
    uint32_t pk2 = 0, pk3 = 0;

    readJanssonFile("schema_file/edge_insert_schema.gmjson", &edge_label_schema_and);
    EXPECT_NE((void *)NULL, edge_label_schema_and);
    printf("edge_label_schema_and: %s\r\n", edge_label_schema_and);
    readJanssonFile("schema_file/edge_insert_schema2.gmjson", &edge_label_schema_and2);
    EXPECT_NE((void *)NULL, edge_label_schema_and2);
    printf("edge_label_schema_and: %s\r\n", edge_label_schema_and2);

    // insert edgeT20-T40
    ret = GmcCreateEdgeLabel(g_stmt_sync, edge_label_schema_and2, EdgeLabel_config);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schema_file/simple_schema_subinfo6.gmjson", &sub_info);
    EXPECT_NE((void *)NULL, sub_info);
    printf("sub_info: %s\r\n", sub_info);

    GmcSubConfigT tmp_sub_info;
    tmp_sub_info.subsName = g_subName;
    tmp_sub_info.configJson = sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_sub_info, g_subChan, sn_callback_NULL, NULL);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(sub_info);

    //删除edge
    ret = GmcDropEdgeLabel(g_stmt_sync, EdgeLabelName2);
    EXPECT_EQ(GMERR_OK, ret);
    free(edge_label_schema_and);
    free(edge_label_schema_and2);
}

//订阅的label与cypher语句中的label无关联（订阅的label与cypher中没有相同的label），cypher中关联关系正确
TEST_F(CreateAndDeletePATHSubscription_func, SN_004_018)
{
    int ret;
    char *sub_info = NULL;

    char EdgeLabelName[] = "from_T10_to_T20";
    char EdgeLabelName2[] = "from_T20_to_T40";
    char EdgeLabel_config[] = "{\"max_record_count\":1000}";
    void *edgelabel = NULL, *edgelabel2 = NULL;
    char *edge_label_schema_and = NULL, *edge_label_schema_and2 = NULL;
    uint8_t pk1 = 0;
    uint32_t pk2 = 0, pk3 = 0;

    readJanssonFile("schema_file/edge_insert_schema.gmjson", &edge_label_schema_and);
    EXPECT_NE((void *)NULL, edge_label_schema_and);
    printf("edge_label_schema_and: %s\r\n", edge_label_schema_and);
    readJanssonFile("schema_file/edge_insert_schema2.gmjson", &edge_label_schema_and2);
    EXPECT_NE((void *)NULL, edge_label_schema_and2);
    printf("edge_label_schema_and: %s\r\n", edge_label_schema_and2);

    // insert edgeT20-T40
    ret = GmcCreateEdgeLabel(g_stmt_sync, edge_label_schema_and2, EdgeLabel_config);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schema_file/simple_schema_subinfo3.gmjson", &sub_info);
    EXPECT_NE((void *)NULL, sub_info);
    printf("sub_info: %s\r\n", sub_info);

    GmcSubConfigT tmp_sub_info;
    tmp_sub_info.subsName = g_subName;
    tmp_sub_info.configJson = sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_sub_info, g_subChan, sn_callback_NULL, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(sub_info);
    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);

    //删除edge
    ret = GmcDropEdgeLabel(g_stmt_sync, EdgeLabelName2);
    EXPECT_EQ(GMERR_OK, ret);
    free(edge_label_schema_and);
    free(edge_label_schema_and2);
}
