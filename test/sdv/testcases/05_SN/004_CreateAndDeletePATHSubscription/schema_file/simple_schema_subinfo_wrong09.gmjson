{"name": "subVertexLabel", "label_name": "T10", "comment": "VertexLabel subscription", "type": "before_commit", "events": [{"type": "insert", "msgTypes": ["new object", "old object"]}, {"type": "delete", "msgTypes": ["new object", "old object"]}], "is_path": true, "retry": true, "cypher": "MATCH path = (node:T10{F0;$T10_PK})-[:from_T10_to_T20]->(node:T20_all_type) return path", "constraint": {"operator_type": "and", "conditions": [{"property": "F0", "value": 1}]}}