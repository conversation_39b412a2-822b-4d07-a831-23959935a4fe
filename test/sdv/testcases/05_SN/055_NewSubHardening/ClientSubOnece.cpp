#include "NewSubHardeningCommon.h"

int main(int argc, char *argv[])
{

    if (argc != 2) {
        AW_FUN_Log(LOG_ERROR, "no parameter.");
        return -1;
    }
    int32_t times = atoi(argv[1]);

    AW_FUN_Log(LOG_INFO, "ClientSubOnece times :%d, begin %d", times, getpid());
    testEnvInit();
    create_epoll_thread();
    int ret;

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    GmcConnT *connNewSub = NULL;
    GmcStmtT *stmtNewSub = NULL;
    // 创建同步连接
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int chanRingLen = 256;
    char subConnName[MAX_NAME_LENGTH] = {0};
    (void)snprintf_s(subConnName, MAX_NAME_LENGTH, MAX_NAME_LENGTH, "SN055SubConn%d", times);
    ret = testSubConnect(&connNewSub, &stmtNewSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SnUserDataT *userData[4];
    for (int loop = 0; loop < 4; loop++) {
        userData[loop] = (SnUserDataT *)malloc(sizeof(SnUserDataT));
        memset(userData[loop], 0, sizeof(SnUserDataT));
    }

    const char *subJosnList[4] = {g_newSubAddNotPersist, g_newSubBig, g_newSubGen, g_newSubSpe};
    char subName[MAX_NAME_LENGTH] = {0};

    GmcSubConfigT newSubInfo;
    for (int loop = 0; loop < 4; loop++) {
        (void)snprintf_s(subName, MAX_NAME_LENGTH, MAX_NAME_LENGTH, "SN055Sub%d", loop);
        newSubInfo.subsName = subName;
        newSubInfo.configJson = subJosnList[loop];
        GmcUnSubscribe(stmt, subName);
        ret = GmcSubscribe(stmt, &newSubInfo, connNewSub, NewSnCallBack, userData[loop]);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "create sub name %s, pid is %d.", subName, getpid());
            // 两个进程并发创建，可能会存在重复创建，不影响后续业务
            if (ret == GMERR_DUPLICATE_OBJECT) {
                ret = GMERR_OK;
            };
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (int loop = 0; loop < 4; loop++) {
        (void)snprintf_s(subName, MAX_NAME_LENGTH, MAX_NAME_LENGTH, "SN055Sub%d", loop);
        ret = GmcUnSubscribe(stmt, subName);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "unsub name %s, pid is %d.", subName, getpid());
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = testSubDisConnect(connNewSub, stmtNewSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int loop = 0; loop < 4; loop++) {
        free(userData[loop]);
    }

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    close_epoll_thread();
    testEnvClean();
    AW_FUN_Log(LOG_INFO, "ClientSubOnece times :%d, end %d", times, getpid());
}
