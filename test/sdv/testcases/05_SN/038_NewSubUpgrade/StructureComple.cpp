/*****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2022. All rights reserved.
 Description  : 状态合并表支持结构化读写升级 特殊复杂表
 Notes        : 001.结构化批量insert数据，普通读、结构化读，结构化批量删除，普通读、结构化读
                002.结构化批量replace数据，普通读、结构化读，结构化批量删除，普通读、结构化读
                003.结构化写，主键索引等值查询
                004.结构化写，二级索引等值查询
                005.下发新订阅，回调中结构化读

 History      :
 Author       : liaoxiang lwx1036939
 Modification : 2023/4/18
*****************************************************************************/

#include "special_complex_table_tools.h"

class StructureComple : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testEnvClean();
    }
};

void StructureComple::SetUp()
{
    int ret = 0;

    // 创建同步连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    char errorMsg6[128] = {};
    (void)snprintf(errorMsg6, sizeof(errorMsg6), "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg6);

    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    AW_CHECK_LOG_BEGIN();

    // 创建特性复杂表
    const char *labelConfig = "{\"max_recordNum\":10000, \"isFastReadUncommitted\":0, \"status_merge_sub\":true}";
    readJanssonFile("schemafile/special_complex_table.gmjson", &g_schema);
    AW_MACRO_EXPECT_NE_INT((void *)NULL, g_schema);
    GmcDropVertexLabel(g_stmt, g_specialTableName);
    ret = GmcCreateVertexLabel(g_stmt, g_schema, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_schema);
}

void StructureComple::TearDown()
{
    AW_CHECK_LOG_END();

    int ret = 0;

    // drop vertex table
    ret = GmcDropVertexLabel(g_stmt, g_specialTableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放同步连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 001.结构化批量insert数据，普通读、结构化读，结构化批量删除，普通读、结构化读
TEST_F(StructureComple, SN_038_StructureComple_001)
{
    AW_FUN_Log(LOG_STEP, "START\n");

    const uint16_t t2Count = 10, t3Count = 10;
    int ret = 0;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构化批量insert数据
    db_test_struct_write_special_complex_table_batch(
        g_stmt, g_dataNum, g_specialTableName, t2Count, t3Count, 1);

    // 普通读
    db_test_read_special_complex_table(
        g_stmt, g_dataNum, g_specialTableName, g_pkName, t2Count, t3Count, true);

    // 结构化读
    db_test_struct_read_special_complex_table(
        g_stmt, g_dataNum, g_specialTableName, t2Count, t3Count, true);

    // 结构化批量删除
    db_test_struct_delete_special_complex_table(g_stmt, g_dataNum, g_specialTableName, 1);

    // 普通读
    db_test_read_special_complex_table(
        g_stmt, g_dataNum, g_specialTableName, g_pkName, t2Count, t3Count, false);

    // 结构化读
    db_test_struct_read_special_complex_table(
        g_stmt, g_dataNum, g_specialTableName, t2Count, t3Count, false);

    AW_FUN_Log(LOG_STEP, "END\n");
}

// 002.结构化批量replace数据，普通读、结构化读，结构化批量删除，普通读、结构化读
TEST_F(StructureComple, SN_038_StructureComple_002)
{
    AW_FUN_Log(LOG_STEP, "START\n");

    const uint16_t t2Count = 10, t3Count = 10;
    int ret = 0;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构化批量replace数据
    db_test_struct_replace_special_complex_table_batch(
        g_stmt, g_dataNum, g_specialTableName, t2Count, t3Count, 1);

    // 普通读
    db_test_read_special_complex_table(
        g_stmt, g_dataNum, g_specialTableName, g_pkName, t2Count, t3Count, true);

    // 结构化读
    db_test_struct_read_special_complex_table(
        g_stmt, g_dataNum, g_specialTableName, t2Count, t3Count, true);

    // 结构化批量删除
    db_test_struct_delete_special_complex_table(g_stmt, g_dataNum, g_specialTableName, 1);

    // 普通读
    db_test_read_special_complex_table(
        g_stmt, g_dataNum, g_specialTableName, g_pkName, t2Count, t3Count, false);

    // 结构化读
    db_test_struct_read_special_complex_table(
        g_stmt, g_dataNum, g_specialTableName, t2Count, t3Count, false);

    AW_FUN_Log(LOG_STEP, "END\n");
}

// 003.结构化写，主键索引等值查询
TEST_F(StructureComple, SN_038_StructureComple_003)
{
    AW_FUN_Log(LOG_STEP, "START\n");

    const uint16_t t2Count = 10, t3Count = 10;
    int ret = 0;
    uint32_t keyId = 0;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构化merge插入写
    db_test_struct_merge_index_special_complex_table(
        g_stmt, 0, g_dataNum, 0, true, true, g_specialTableName, keyId, t2Count, t3Count, 1, false);
    
    // 主键读
    db_test_struct_index_read_special_complex_table(
        g_stmt, 0, g_dataNum, 0, true, true, g_specialTableName, keyId, t2Count, t3Count, true);

    AW_FUN_Log(LOG_STEP, "END\n");
}

// 004.结构化写，二级索引等值查询
TEST_F(StructureComple, SN_038_StructureComple_004)
{
    AW_FUN_Log(LOG_STEP, "START\n");

    const uint16_t t2Count = 10, t3Count = 10;
    int ret = 0;
    uint32_t keyId = 1;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构化merge插入写
    db_test_struct_merge_index_special_complex_table(
        g_stmt, 0, g_dataNum, 0, true, true, g_specialTableName, 0, t2Count, t3Count, 1, false);
    
    // 二级索引读
    db_test_struct_index_read_special_complex_table(
        g_stmt, 0, g_dataNum, 0, true, true, g_specialTableName, keyId, t2Count, t3Count, true);

    AW_FUN_Log(LOG_STEP, "END\n");
}

// 005.下发新订阅，回调中结构化读
TEST_F(StructureComple, SN_038_StructureComple_005)
{
    AW_FUN_Log(LOG_STEP, "START\n");

    int ret, i;
    int userDataIdx = 0;
    const char *g_subConnName = "subConnName";
    const char *g_subName = "subVertexLabel";
    SnUserDataT *user_data;
    const uint16_t t2Count = 10, t3Count = 10;
    uint32_t keyId = 1;

    ret = testSnMallocUserData(&user_data, g_dataNum * 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建订阅连接
    GmcConnT *g_subChan = NULL;
    int chanRingLen = 256;
    ret = testSubConnect(&g_subChan, &g_stmtSub, 1, g_epoll_reg_info, g_subConnName, &chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *subInfo = NULL;
    readJanssonFile("subinfofile/special_complex_table_subinfo.gmjson", &subInfo);
    AW_MACRO_EXPECT_NE_INT((void *)NULL, subInfo);

    GmcSubConfigT tmpSubInfo;
    tmpSubInfo.subsName = g_subName;
    tmpSubInfo.configJson = subInfo;
    ret = GmcSubscribe(g_stmt, &tmpSubInfo, g_subChan, sn_callback_special_complex_table, user_data);
    ASSERT_EQ(GMERR_OK, ret);
    free(subInfo);

    // 预制推送校验数据
    for (i = 0; i < g_dataNum; i++) {
        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;
    }

    // 结构化merge插入写
    db_test_struct_merge_index_special_complex_table(
        g_stmt, 0, g_dataNum, 0, true, true, g_specialTableName, 0, t2Count, t3Count, 1, false);

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_MERGE_INSERT, g_dataNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt, g_subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubDisConnect(g_subChan, g_stmtSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testSnFreeUserData(user_data);

    AW_FUN_Log(LOG_STEP, "END\n");
}

