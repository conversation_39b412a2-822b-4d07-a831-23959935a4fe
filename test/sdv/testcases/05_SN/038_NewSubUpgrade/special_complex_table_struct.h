/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2012-2018. All rights reserved.
 Description  :状态合并订阅支持表升级
 Author       : lwx1036939
 Modification :
 Date         : 2023/04/18
**************************************************************************** */
#ifndef SPECIAL_COMPLEX_TABLE_STRUCT_H
#define SPECIAL_COMPLEX_TABLE_STRUCT_H

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"

int g_stringLen = 10, g_bytesLen = 20;

#pragma pack(1)
struct record_T1 {
    int64_t p0;
    uint64_t p1;
    int32_t p2;
    uint32_t p3;
    int16_t p4;
    uint16_t p5;
    int8_t p6;
    uint8_t p7;
    bool p8;
    float p9;
    double p10;
    uint64_t p11;
    char p12;
    unsigned char p13;
    uint8_t p14[16];
    uint16_t p15Len;
    uint8_t *p15;
    uint16_t p16Len;
    uint8_t *p16;
};
#pragma pack()

#pragma pack(1)
struct fixed_array_T2 {
    int64_t a0;
    uint64_t a1;
    int32_t a2;
    uint32_t a3;
    int16_t a4;
    uint16_t a5;
    int8_t a6;
    uint8_t a7;
    bool a8;
    float a9;
    double a10;
    uint64_t a11;
    char a12;
    unsigned char a13;
    uint8_t a14[16];
    uint16_t a15Len;
    uint8_t *a15;
    uint16_t a16Len;
    uint8_t *a16;
};
#pragma pack()

#pragma pack(1)
struct vector_T3 {
    int64_t v0;
    uint64_t v1;
    int32_t v2;
    uint32_t v3;
    int16_t v4;
    uint16_t v5;
    int8_t v6;
    uint8_t v7;
    bool v8;
    float v9;
    double v10;
    uint64_t v11;
    char v12;
    unsigned char v13;
    uint8_t v14[16];
    uint16_t v15Len;
    uint8_t *v15;
    uint16_t v16Len;
    uint8_t *v16;
};
#pragma pack()

#pragma pack(1)
struct TEST_SC_T1_struct_t {
    int64_t f0;
    uint64_t f1;
    int32_t f2;
    uint32_t f3;
    int16_t f4;
    uint16_t f5;
    int8_t f6;
    uint8_t f7;
    bool f8;
    float f9;
    double f10;
    uint64_t f11;
    char f12;
    unsigned char f13;
    uint8_t f14[16];
    uint8_t f15;
    uint16_t f16Len;
    uint8_t *f16;
    uint16_t f17Len;
    uint8_t *f17;
    uint16_t t1Flag;  // 1
    record_T1 *t1;
    uint16_t t2Count;
    fixed_array_T2 *t2;
    uint16_t t3Count;
    vector_T3 *t3;
};
#pragma pack()

#pragma pack(1)
struct TEST_T4_struct_t {
    int64_t f0;
    uint64_t f1;
    int32_t f2;
    uint32_t f3;
    int16_t f4;
    uint16_t f5;
    int8_t f6;
    uint8_t f7;
    bool f8;
    float f9;
    double f10;
    uint64_t f11;
    char f12;
    unsigned char f13;
    uint8_t f14[16];
    uint8_t f15;
    uint16_t f16Len;
    uint8_t *f16;
    uint16_t f17Len;
    uint8_t *f17;
    uint16_t t1Flag;  // 1
    record_T1 *t1;
    uint16_t t2Count;
    fixed_array_T2 *t2;
    uint16_t t3Count;
    vector_T3 *t3;
};
#pragma pack()

void TestSetTestT4NonuniqKey(TEST_T4_struct_t *d, int v)
{
    d->f1 = (uint64_t)v + 0xFFFFFFFF;  // uint64
    d->f2 = (int32_t)v;                // int32
    d->f3 = (uint32_t)v;               // uint32
}

void test_set_TEST_T4_primary_key(TEST_T4_struct_t *d, int v, bool isUniq)
{
    d->f0 = (int64_t)v;  // int64
    if (isUniq) {
        d->f1 = (uint64_t)v + 0xFFFFFFFF;  // uint64
        d->f2 = (int32_t)v;                // int32
        d->f3 = (uint32_t)v;               // uint32
    } else {                               // 构造非唯一二级索引冲突链
        d->f1 = (uint64_t)0 + 0xFFFFFFFF;  // uint64
        d->f2 = (int32_t)0;                // int32
        d->f3 = (uint32_t)0;               // uint32
    }
    d->f4 = (int16_t)v & 0x7FFF;   // int16
    d->f5 = (uint16_t)v & 0xFFFF;  // uint16
    d->f6 = (int8_t)v & 0x7F;      // int8
    d->f7 = (uint8_t)v & 0xFF;     // uint8
}

void test_free_TEST_T4(TEST_T4_struct_t *d, uint16_t t2Count, uint16_t t3Count)
{
    if (d->f16) {
        free(d->f16);
    }
    if (d->f17) {
        free(d->f17);
    }
    if (d->t1->p15) {
        free(d->t1->p15);
    }
    if (d->t1->p16) {
        free(d->t1->p16);
    }
    for (int i = 0; i < t2Count; ++i) {
        if (d->t2[i].a15) {
            free(d->t2[i].a15);
        }
        if (d->t2[i].a16) {
            free(d->t2[i].a16);
        }
    }
    for (int i = 0; i < t3Count; ++i) {
        if (d->t3[i].v15) {
            free(d->t3[i].v15);
        }
        if (d->t3[i].v16) {
            free(d->t3[i].v16);
        }
    }
}

void test_malloc_TEST_T4(
    TEST_T4_struct_t *d, record_T1 *t1, fixed_array_T2 *t2, vector_T3 *t3, uint16_t t2Count, uint16_t t3Count)
{
    int i, stringLen = 10, bytesLen = 20;

    // record 子节点 的变长字段 分配内存
    t1->p15 = (uint8_t *)malloc(stringLen + 1);
    EXPECT_NE((void *)NULL, t1->p15);
    t1->p16 = (uint8_t *)malloc(bytesLen + 1);
    EXPECT_NE((void *)NULL, t1->p16);

    // array 的变长字段 分配内存
    for (i = 0; i < t2Count; ++i) {
        t2[i].a15Len = stringLen;
        t2[i].a15 = (uint8_t *)malloc(stringLen + 1);
        EXPECT_NE((void *)NULL, t2[i].a15);
        t2[i].a16Len = bytesLen;
        t2[i].a16 = (uint8_t *)malloc(bytesLen + 1);
        EXPECT_NE((void *)NULL, t2[i].a16);
    }

    // vector 的变长字段 分配内存
    for (i = 0; i < t3Count; ++i) {
        t3[i].v15Len = stringLen;
        t3[i].v15 = (uint8_t *)malloc(stringLen + 1);
        EXPECT_NE((void *)NULL, t3[i].v15);
        t3[i].v16Len = bytesLen;
        t3[i].v16 = (uint8_t *)malloc(bytesLen + 1);
        EXPECT_NE((void *)NULL, t3[i].v16);
    }

    d->t1 = t1;
    d->t2 = t2;
    d->t3 = t3;
}

void TestSetTestT4UniqKey(TEST_T4_struct_t *d, int v)
{
    d->f1 = (uint64_t)v + 0xFFFFFFFF;   // uint64
    d->f2 = (int32_t)v;                 // int32
    d->f3 = (uint32_t)v;                // uint32
    d->f4 = (int16_t)v & 0x7FFF;        // int16
    d->f5 = (uint16_t)v & 0xFFFF;       // uint16
    d->f6 = (int8_t)v & 0x7F;           // int8
    d->f7 = (uint8_t)v & 0xFF;          // uint8
    d->f11 = (uint64_t)v + 0xFFFFFFFF;  // time
}

void test_free_TEST_SC_T1(TEST_SC_T1_struct_t *d, uint16_t t2Count, uint16_t t3Count)
{
    if (d->f16) {
        free(d->f16);
    }
    if (d->f17) {
        free(d->f17);
    }
    if (d->t1->p15) {
        free(d->t1->p15);
    }
    if (d->t1->p16) {
        free(d->t1->p16);
    }
    for (int i = 0; i < t2Count; ++i) {
        if (d->t2[i].a15) {
            free(d->t2[i].a15);
        }
        if (d->t2[i].a16) {
            free(d->t2[i].a16);
        }
    }
    for (int i = 0; i < t3Count; ++i) {
        if (d->t3[i].v15) {
            free(d->t3[i].v15);
        }
        if (d->t3[i].v16) {
            free(d->t3[i].v16);
        }
    }
}

void test_malloc_TEST_SC_T1(
    TEST_SC_T1_struct_t *d, record_T1 *t1, fixed_array_T2 *t2, vector_T3 *t3, uint16_t t2Count, uint16_t t3Count)
{
    int i, stringLen = g_stringLen, bytesLen = g_bytesLen;

    // record 子节点 的变长字段 分配内存
    t1->p15 = (uint8_t *)malloc(stringLen + 1);
    EXPECT_NE((void *)NULL, t1->p15);
    t1->p16 = (uint8_t *)malloc(bytesLen + 1);
    EXPECT_NE((void *)NULL, t1->p16);

    // array 的变长字段 分配内存
    for (i = 0; i < t2Count; ++i) {
        t2[i].a15Len = stringLen;
        t2[i].a15 = (uint8_t *)malloc(stringLen + 1);
        EXPECT_NE((void *)NULL, t2[i].a15);
        t2[i].a16Len = bytesLen;
        t2[i].a16 = (uint8_t *)malloc(bytesLen + 1);
        EXPECT_NE((void *)NULL, t2[i].a16);
    }

    // vector 的变长字段 分配内存
    for (i = 0; i < t3Count; ++i) {
        t3[i].v15Len = stringLen;
        t3[i].v15 = (uint8_t *)malloc(stringLen + 1);
        EXPECT_NE((void *)NULL, t3[i].v15);
        t3[i].v16Len = bytesLen;
        t3[i].v16 = (uint8_t *)malloc(bytesLen + 1);
        EXPECT_NE((void *)NULL, t3[i].v16);
    }

    d->t1 = t1;
    d->t2 = t2;
    d->t3 = t3;
}

void test_set_TEST_SC_T1_root_value(TEST_SC_T1_struct_t *d, int v, bool boolValue)
{
    int stringLen = g_stringLen, bytesLen = g_bytesLen;

    // root
    d->f0 = (int64_t)v;                                          // int64   8
    d->f1 = (uint64_t)v + 0xFFFFFFFF;                            // uint64  8
    d->f2 = v;                                                   // int32   4
    d->f3 = v;                                                   // uint32  4
    d->f4 = v & 0x7FFF;                                          // int16   2
    d->f5 = v & 0xFFFF;                                          // uint16  2
    d->f6 = v & 0x7F;                                            // int8    1
    d->f7 = v & 0xFF;                                            // uint8   1
    d->f8 = boolValue;                                          // boolean 1
    d->f9 = v;                                                   // float   4
    d->f10 = v;                                                  // double  8
    d->f11 = v + 0xFFFFFFFF;                                     // time    8
    d->f12 = 'a' + (v & 0x1A);                                   // char     1
    d->f13 = 'A' + (v & 0x1A);                                   // uchar   1
    (void)snprintf((char *)d->f14, sizeof(d->f14), "aaaaaaa%08d", v);  // fixed  16
    d->f15 = v & 0xF;                                            // partition  1
    d->f16Len = stringLen;                                     // string
    if (d->f16 == NULL) {
        d->f16 = (uint8_t *)malloc(d->f16Len + 1);
        if (d->f16 != NULL) {
            (void)snprintf((char *)d->f16, d->f16Len + 1, "b%08d", v);
        }
    } else {
        (void)snprintf((char *)d->f16, d->f16Len + 1, "b%08d", v);
    }
    d->f17Len = bytesLen;  // bytes
    if (d->f17 == NULL) {
        d->f17 = (uint8_t *)malloc(d->f17Len + 1);
        if (d->f17 != NULL) {
            (void)snprintf((char *)d->f17, d->f17Len + 1, "ABCDEFGHIJKL%08d", v);
        }
    } else {
        (void)snprintf((char *)d->f17, d->f17Len + 1, "ABCDEFGHIJKL%08d", v);
    }
}

void test_set_TEST_SC_T1_T1_value(TEST_SC_T1_struct_t *d, int v, bool boolValue)
{
    int stringLen = g_stringLen, bytesLen = g_bytesLen;

    // record子节点
    d->t1Flag = 1;
    d->t1->p0 = (int64_t)v;                                              // int64   8
    d->t1->p1 = (uint64_t)v + 0xFFFFFFFF;                                // uint64  8
    d->t1->p2 = v;                                                       // int32   4
    d->t1->p3 = v;                                                       // uint32  4
    d->t1->p4 = v & 0x7FFF;                                              // int16   2
    d->t1->p5 = v & 0xFFFF;                                              // uint16  2
    d->t1->p6 = v & 0x7F;                                                // int8    1
    d->t1->p7 = v & 0xFF;                                                // uint8   1
    d->t1->p8 = boolValue;                                              // boolean 1
    d->t1->p9 = v;                                                       // float   4
    d->t1->p10 = v;                                                      // double  8
    d->t1->p11 = v + 0xFFFFFFFF;                                         // time    8
    d->t1->p12 = 'a' + (v & 0x1A);                                       // char     1
    d->t1->p13 = 'A' + (v & 0x1A);                                       // uchar   1
    (void)snprintf((char *)d->t1->p14, sizeof(d->t1->p14), "aaaaaaa%08d", v);  // fixed  16
    d->t1->p15Len = stringLen;                                         // string
    (void)snprintf((char *)d->t1->p15, d->t1->p15Len + 1, "b%08d", v);
    d->t1->p16Len = bytesLen;  // bytes
    (void)snprintf((char *)d->t1->p16, d->t1->p16Len + 1, "ABCDEFGHIJKL%08d", v);
}

void test_set_TEST_SC_T1_T2_value(TEST_SC_T1_struct_t *d, int v, bool boolValue, uint16_t t2Count)
{
    int i, stringLen = g_stringLen, bytesLen = g_bytesLen;

    // array子节点
    d->t2Count = t2Count;
    for (i = 0; i < d->t2Count; ++i) {
        d->t2[i].a0 = (int64_t)i;                                                // int64   8
        d->t2[i].a1 = (uint64_t)i + 0xFFFFFFFF;                                  // uint64  8
        d->t2[i].a2 = i;                                                         // int32   4
        d->t2[i].a3 = i;                                                         // uint32  4
        d->t2[i].a4 = i & 0x7FFF;                                                // int16   2
        d->t2[i].a5 = i & 0xFFFF;                                                // uint16  2
        d->t2[i].a6 = i & 0x7F;                                                  // int8    1
        d->t2[i].a7 = i & 0xFF;                                                  // uint8   1
        d->t2[i].a8 = boolValue;                                                // boolean 1
        d->t2[i].a9 = i;                                                         // float   4
        d->t2[i].a10 = i;                                                        // double  8
        d->t2[i].a11 = i + 0xFFFFFFFF;                                           // time    8
        d->t2[i].a12 = 'a' + (i & 0x1A);                                         // char     1
        d->t2[i].a13 = 'A' + (i & 0x1A);                                         // uchar   1
        (void)snprintf((char *)d->t2[i].a14, sizeof(d->t2[i].a14), "aaaaaaa%08d", i);  // fixed  16
        d->t2[i].a15Len = stringLen;                                           // string
        (void)snprintf((char *)d->t2[i].a15, d->t2[i].a15Len + 1, "b%08d", i);
        d->t2[i].a16Len = bytesLen;  // bytes
        (void)snprintf((char *)d->t2[i].a16, d->t2[i].a16Len + 1, "ABCDEFGHIJKL%08d", i);
    }
}

void test_set_TEST_SC_T1_T3_value(TEST_SC_T1_struct_t *d, int v, bool boolValue, uint16_t t3Count)
{
    int i, stringLen = g_stringLen, bytesLen = g_bytesLen;

    // vector子节点
    d->t3Count = t3Count;
    for (i = 0; i < d->t3Count; ++i) {
        d->t3[i].v0 = (int64_t)i;                                                // int64   8
        d->t3[i].v1 = (uint64_t)i + 0xFFFFFFFF;                                  // uint64  8
        d->t3[i].v2 = i;                                                         // int32   4
        d->t3[i].v3 = i;                                                         // uint32  4
        d->t3[i].v4 = i & 0x7FFF;                                                // int16   2
        d->t3[i].v5 = i & 0xFFFF;                                                // uint16  2
        d->t3[i].v6 = i & 0x7F;                                                  // int8    1
        d->t3[i].v7 = i & 0xFF;                                                  // uint8   1
        d->t3[i].v8 = boolValue;                                                // boolean 1
        d->t3[i].v9 = i;                                                         // float   4
        d->t3[i].v10 = i;                                                        // double  8
        d->t3[i].v11 = i + 0xFFFFFFFF;                                           // time    8
        d->t3[i].v12 = 'a' + (i & 0x1A);                                         // char     1
        d->t3[i].v13 = 'A' + (i & 0x1A);                                         // uchar   1
        (void)snprintf((char *)d->t3[i].v14, sizeof(d->t3[i].v14), "aaaaaaa%08d", i);  // fixed  16
        d->t3[i].v15Len = stringLen;                                           // string
        (void)snprintf((char *)d->t3[i].v15, d->t3[i].v15Len + 1, "b%08d", i);
        d->t3[i].v16Len = bytesLen;  // bytes
        (void)snprintf((char *)d->t3[i].v16, d->t3[i].v16Len + 1, "ABCDEFGHIJKL%08d", i);
    }
}

void test_set_TEST_SC_T1_value(TEST_SC_T1_struct_t *d, int v, bool boolValue, uint16_t t2Count, uint16_t t3Count)
{
    // root
    test_set_TEST_SC_T1_root_value(d, v, boolValue);

    // record子节点
    test_set_TEST_SC_T1_T1_value(d, v, boolValue);

    // array子节点
    test_set_TEST_SC_T1_T2_value(d, v, boolValue, t2Count);

    // vector子节点
    test_set_TEST_SC_T1_T3_value(d, v, boolValue, t3Count);
}

void TestSetTestScT1PrimaryKey(TEST_SC_T1_struct_t *d, int v)
{
    d->f0 = (int64_t)v;  // int64
}

void test_set_TEST_SC_T1_value_root2(
    TEST_SC_T1_struct_t *d, int v, bool boolValue, uint16_t t2Count, uint16_t t3Count)
{
    int i, stringLen = g_stringLen, bytesLen = g_bytesLen;

    // root
    d->f0 = (int64_t)v;                // int64   8
    d->f1 = (uint64_t)v + 0xFFFFFFFF;  // uint64  8
    d->f3 = v;                                                   // uint32  4
    d->f4 = v & 0x7FFF;                                          // int16   2
    d->f5 = v & 0xFFFF;                                          // uint16  2
    d->f6 = v & 0x7F;                                            // int8    1
    d->f7 = v & 0xFF;                                            // uint8   1
    d->f8 = boolValue;                                          // boolean 1
    d->f9 = v;                                                   // float   4
    d->f10 = v;                                                  // double  8
    d->f11 = v + 0xFFFFFFFF;                                     // time    8
    d->f12 = 'a' + (v & 0x1A);                                   // char     1
    d->f13 = 'A' + (v & 0x1A);                                   // uchar   1
    (void)snprintf((char *)d->f14, sizeof(d->f14), "aaaaaaa%08d", v);  // fixed  16
    d->f15 = v & 0xF;                                            // partition  1
    d->f16Len = stringLen;                                     // string
    if (d->f16 == NULL) {
        d->f16 = (uint8_t *)malloc(d->f16Len + 1);
        if (d->f16 != NULL) {
            (void)snprintf((char *)d->f16, d->f16Len + 1, "b%08d", v);
        }
    } else {
        (void)snprintf((char *)d->f16, d->f16Len + 1, "b%08d", v);
    }
    d->f17Len = bytesLen;  // bytes
    if (d->f17 == NULL) {
        d->f17 = (uint8_t *)malloc(d->f17Len + 1);
        if (d->f17 != NULL) {
            (void)snprintf((char *)d->f17, d->f17Len + 1, "ABCDEFGHIJKL%08d", v);
        }
    } else {
        (void)snprintf((char *)d->f17, d->f17Len + 1, "ABCDEFGHIJKL%08d", v);
    }

    // record子节点
    d->t1Flag = 1;
    d->t1->p0 = (int64_t)v;                                              // int64   8
    d->t1->p1 = (uint64_t)v + 0xFFFFFFFF;                                // uint64  8
    d->t1->p2 = v;                                                       // int32   4
    d->t1->p3 = v;                                                       // uint32  4
    d->t1->p4 = v & 0x7FFF;                                              // int16   2
    d->t1->p5 = v & 0xFFFF;                                              // uint16  2
    d->t1->p6 = v & 0x7F;                                                // int8    1
    d->t1->p7 = v & 0xFF;                                                // uint8   1
    d->t1->p8 = boolValue;                                              // boolean 1
    d->t1->p9 = v;                                                       // float   4
    d->t1->p10 = v;                                                      // double  8
    d->t1->p11 = v + 0xFFFFFFFF;                                         // time    8
    d->t1->p12 = 'a' + (v & 0x1A);                                       // char     1
    d->t1->p13 = 'A' + (v & 0x1A);                                       // uchar   1
    (void)snprintf((char *)d->t1->p14, sizeof(d->t1->p14), "aaaaaaa%08d", v);  // fixed  16
    d->t1->p15Len = stringLen;                                         // string
    (void)snprintf((char *)d->t1->p15, d->t1->p15Len + 1, "b%08d", v);
    d->t1->p16Len = bytesLen;  // bytes
    (void)snprintf((char *)d->t1->p16, d->t1->p16Len + 1, "ABCDEFGHIJKL%08d", v);

    // array子节点
    d->t2Count = t2Count;
    for (i = 0; i < d->t2Count; ++i) {
        d->t2[i].a0 = (int64_t)i;                                                // int64   8
        d->t2[i].a1 = (uint64_t)i + 0xFFFFFFFF;                                  // uint64  8
        d->t2[i].a2 = i;                                                         // int32   4
        d->t2[i].a3 = i;                                                         // uint32  4
        d->t2[i].a4 = i & 0x7FFF;                                                // int16   2
        d->t2[i].a5 = i & 0xFFFF;                                                // uint16  2
        d->t2[i].a6 = i & 0x7F;                                                  // int8    1
        d->t2[i].a7 = i & 0xFF;                                                  // uint8   1
        d->t2[i].a8 = boolValue;                                                // boolean 1
        d->t2[i].a9 = i;                                                         // float   4
        d->t2[i].a10 = i;                                                        // double  8
        d->t2[i].a11 = i + 0xFFFFFFFF;                                           // time    8
        d->t2[i].a12 = 'a' + (i & 0x1A);                                         // char     1
        d->t2[i].a13 = 'A' + (i & 0x1A);                                         // uchar   1
        (void)snprintf((char *)d->t2[i].a14, sizeof(d->t2[i].a14), "aaaaaaa%08d", i);  // fixed  16
        d->t2[i].a15Len = stringLen;                                           // string
        (void)snprintf((char *)d->t2[i].a15, d->t2[i].a15Len + 1, "b%08d", i);
        d->t2[i].a16Len = bytesLen;  // bytes
        (void)snprintf((char *)d->t2[i].a16, d->t2[i].a16Len + 1, "ABCDEFGHIJKL%08d", i);
    }

    // vector子节点
    d->t3Count = t3Count;
    for (i = 0; i < d->t3Count; ++i) {
        d->t3[i].v0 = (int64_t)i;                                                // int64   8
        d->t3[i].v1 = (uint64_t)i + 0xFFFFFFFF;                                  // uint64  8
        d->t3[i].v2 = i;                                                         // int32   4
        d->t3[i].v3 = i;                                                         // uint32  4
        d->t3[i].v4 = i & 0x7FFF;                                                // int16   2
        d->t3[i].v5 = i & 0xFFFF;                                                // uint16  2
        d->t3[i].v6 = i & 0x7F;                                                  // int8    1
        d->t3[i].v7 = i & 0xFF;                                                  // uint8   1
        d->t3[i].v8 = boolValue;                                                // boolean 1
        d->t3[i].v9 = i;                                                         // float   4
        d->t3[i].v10 = i;                                                        // double  8
        d->t3[i].v11 = i + 0xFFFFFFFF;                                           // time    8
        d->t3[i].v12 = 'a' + (i & 0x1A);                                         // char     1
        d->t3[i].v13 = 'A' + (i & 0x1A);                                         // uchar   1
        (void)snprintf((char *)d->t3[i].v14, sizeof(d->t3[i].v14), "aaaaaaa%08d", i);  // fixed  16
        d->t3[i].v15Len = stringLen;                                           // string
        (void)snprintf((char *)d->t3[i].v15, d->t3[i].v15Len + 1, "b%08d", i);
        d->t3[i].v16Len = bytesLen;  // bytes
        (void)snprintf((char *)d->t3[i].v16, d->t3[i].v16Len + 1, "ABCDEFGHIJKL%08d", i);
    }
}

#endif
