/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: AgeEventTypeCommon.h
 * Author: gwx620465
 * Create: 2024-01-15
 */
#ifndef AGE_EVENT_TYPE_COMMON_H
#define AGE_EVENT_TYPE_COMMON_H

#include "t_datacom_lite.h"
#include "BigTableLockCommon.h"

const char *g_newSubInfoAge = R"(
{
        "name": "subAgeSN047V1",
        "label_name": "SN047V1",
        "comment":"status_merge",
        "events":
            [
                {"type":"age", "msgTypes":["new object", "old object", "key"]}
            ],
        "is_path":false,
        "retry":true,
        "priority": 1,
        "subs_type":"status_merge"
})";

const char *g_newSubInfoAgeV5 = R"(
{
        "name": "subAgeSN047V5",
        "label_name": "SN047V5",
        "comment":"status_merge",
        "events":
            [
                {"type":"age", "msgTypes":["new object", "old object", "key"]}
            ],
        "is_path":false,
        "retry":true,
        "priority": 1,
        "subs_type":"status_merge"
})";

const char *g_newSubAgeAndDelete = R"(
{
        "name": "subAgeDeleteSN047V1",
        "label_name": "SN047V1",
        "comment":"status_merge",
        "events":
            [
                {"type":"age", "msgTypes":["new object", "old object", "key"]},
                {"type":"delete", "msgTypes":["new object", "old object", "key"]}
            ],
        "is_path":false,
        "retry":true,
        "priority": 1,
        "subs_type":"status_merge"
})";

const char *g_newSubAgeAndModify = R"(
{
        "name": "subAgeModifySN047V1",
        "label_name": "SN047V1",
        "comment":"status_merge",
        "events":
            [
                {"type":"age", "msgTypes":["new object", "old object", "key"]},
                {"type":"modify", "msgTypes":["new object", "old object"]}
            ],
        "is_path":false,
        "retry":true,
        "priority": 1,
        "subs_type":"status_merge"
})";

const char *g_newSubAgePartition = R"(
{
        "name": "subAgeSN047V4",
        "label_name": "SN047V4Partition",
        "comment":"status_merge",
        "events":
            [
                {"type":"age", "msgTypes":["new object", "old object", "key"]}
            ],
        "is_path":false,
        "retry":true,
        "priority": 1,
        "subs_type":"status_merge"
})";

const char *g_newSubAgeAndModifyPartition = R"(
{
        "name": "subAgeModifySN047V4",
        "label_name": "SN047V4Partition",
        "comment":"status_merge",
        "events":
            [
                {"type":"age", "msgTypes":["new object", "old object", "key"]},
                {"type":"modify", "msgTypes":["new object", "old object"]}
            ],
        "is_path":false,
        "retry":true,
        "priority": 1,
        "subs_type":"status_merge"
})";

const char *g_newSubAgeAndDeletePartition = R"(
{
        "name": "subAgeDeleteSN047V4",
        "label_name": "SN047V4Partition",
        "comment":"status_merge",
        "events":
            [
                {"type":"age", "msgTypes":["new object", "old object", "key"]},
                {"type":"delete", "msgTypes":["new object", "old object", "key"]}
            ],
        "is_path":false,
        "retry":true,
        "priority": 1,
        "subs_type":"status_merge"
})";

void NewSnCallBackAge(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret = 0;
    int index = 0;
    int addVal = 0;
    char labelName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *data = (SnUserDataT *)userData;
    bool eof = false;

    while (!eof) {
        ret = GmcFetch(subStmt, &eof);  // fetch 1 次
        if (ret != GMERR_OK || eof == true) {
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);

        /* 多表情景下不好保证推送得到的表顺序是恒定的 */
        for (int i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            uint32_t labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(strlen(labelName), labelNameLen);

            switch (info->eventType) {
                case GMC_SUB_EVENT_MODIFY: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_AGED: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_NO_DATA, ret);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_NO_DATA, ret);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_INITIAL_LOAD: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_INFO, "default: invalid eventType %s .", info->eventType);
                    break;
                }
            }
        }
        data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_MODIFY: {
                data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                data->agedNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                data->scanEofNum++;
                break;
            }
            default: {
                AW_FUN_Log(LOG_INFO, "default: invalid eventType.");
                break;
            }
        }
    }
}

void NewSnCallBackAgePartition(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret = 0;
    int index = 0;
    int addVal = 0;
    char labelName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *data = (SnUserDataT *)userData;
    bool eof = false;

    while (!eof) {
        ret = GmcFetch(subStmt, &eof);  // fetch 1 次
        if (ret != GMERR_OK || eof == true) {
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);

        /* 多表情景下不好保证推送得到的表顺序是恒定的 */
        for (int i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            uint32_t labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(strlen(labelName), labelNameLen);

            switch (info->eventType) {
                case GMC_SUB_EVENT_MODIFY: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_AGED: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_NO_DATA, ret);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_INFO, "default: invalid eventType %s .", info->eventType);
                    break;
                }
            }
        }
        data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_AGED: {
                data->agedNum++;
                break;
            }
            case GMC_SUB_EVENT_MODIFY: {
                data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                data->scanEofNum++;
                break;
            }
            default: {
                AW_FUN_Log(LOG_INFO, "default: invalid eventType.");
                break;
            }
        }
    }
}

void NewSnCallBackAgeDelete(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret = 0;
    int index = 0;
    int addVal = 0;
    char labelName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *data = (SnUserDataT *)userData;
    bool eof = false;

    while (!eof) {
        ret = GmcFetch(subStmt, &eof);  // fetch 1 次
        if (ret != GMERR_OK || eof == true) {
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);

        /* 多表情景下不好保证推送得到的表顺序是恒定的 */
        for (int i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            uint32_t labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(strlen(labelName), labelNameLen);

            switch (info->eventType) {
                case GMC_SUB_EVENT_AGED: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_NO_DATA, ret);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_NO_DATA, ret);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_INFO, "default: invalid eventType %s .", info->eventType);
                    break;
                }
            }
        }
        data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_DELETE: {
                data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                data->agedNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                data->scanEofNum++;
                break;
            }
            default: {
                AW_FUN_Log(LOG_INFO, "default: invalid eventType.");
                break;
            }
        }
    }
}

// localhash范围删除
int TestLocalhashDeleteTable(
    GmcConnT *conn, GmcStmtT *stmt, uint32_t begin, uint32_t end, const char *labelName, const char *keyName)
{
    int ret = 0;
    char f5Val[FILED_FIX_SIZE + 1] = {0};

    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = begin; i < end; i++) {
        ret = snprintf_s(f5Val, FILED_FIX_SIZE + 1, FILED_FIX_SIZE, "d%06d", i % 10000);
        if (ret != -1) {
            ret = GMERR_OK;
        }
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_STRING, &f5Val, strlen(f5Val));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    return ret;
}

// HashClusterf非唯一索引删除
int TestHashClusterDeleteTable(
    GmcConnT *conn, GmcStmtT *stmt, uint32_t begin, uint32_t end, const char *labelName, const char *keyName)
{
    int ret = 0;

    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char f2Val = '0';
    for (int i = begin; i < end; i++) {
        f2Val = (char)(i % 9);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_CHAR, &f2Val, sizeof(char));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}

#endif
