{"name": "subPath_7_insert", "label_name": "ip4forward", "comment": "PATH subscription", "type": "before_commit", "events": [{"type": "insert", "msgTypes": ["new object", "old object"]}], "is_path": true, "retry": true, "cypher": "MATCH path = (node1:ip4forward)-[:from_7_to_8]->(node2:nhp_group{vr_id:$id and nhp_group_id:$id})-[:from_8_to_9]->(node3:nhp_group_node)-[:from_9_to_10]->(node4:nhp)-[:from_10_to_42]->(node5:nhp_std) return path"}