
extern "C" {
}

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <pthread.h>
#include <sys/time.h>
#include "gtest/gtest.h"
#include "common_tool.h"

#define TEST_NUMBER_64 64
#define TEST_NUMBER_16 16
#define TEST_NUMBER_32 32
#define TEST_NUMBER_48 48

class SupportPathBasicPushCapability_8 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh");
        int ret;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        //创建epoll监听线程
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret;
        ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
    }

public:
    SnUserDataT *user_data;

    virtual void SetUp();
    virtual void TearDown();
};

void SupportPathBasicPushCapability_8::SetUp()
{
    printf("......SupportPathBasicPushCapability_8 Start......\n");

    g_conn_sync = NULL;
    g_stmt_sync = NULL;
    g_sub_info_insert = NULL;

    int ret;

    user_data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(user_data, 0, sizeof(SnUserDataT));
    user_data->new_value = (int *)malloc(sizeof(int) * 10000 * 10);
    memset(user_data->new_value, 0, sizeof(int) * 10000 * 10);
    user_data->old_value = (int *)malloc(sizeof(int) * 10000 * 10);
    memset(user_data->old_value, 0, sizeof(int) * 10000 * 10);

    //创建同步连接
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    ASSERT_EQ(GMERR_OK, ret);

    g_subIndex = 0;
    //创建订阅连接
    int chanRingLen = 256;
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);

    //创建fib表7#...42#表
    test_add_all_fib_vertex_label(g_stmt_sync);

    //导入edge的gmjson，建边edgelabel，插入数据满足建边条件后会自动化建边
    test_import_and_create_edge_gmjson(g_stmt_sync);

    AW_CHECK_LOG_BEGIN();
}

void SupportPathBasicPushCapability_8::TearDown()
{
    AW_CHECK_LOG_END();
    int ret;

    //删除全部EdgeLabel
    test_drop_all_edgelabel();

    //断开和删除fib下的顶点label
    test_close_and_drop_fib_label();

    // 释放订阅连接
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    //断开同步连接
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);

    //清除相应的内存空间
    test_free_sub();
    // test_free_lable_json();
    test_free_edge_json();
    free(user_data->new_value);
    free(user_data->old_value);
    free(user_data);

    printf(".....SupportPathBasicPushCapability_8_ End.....\n");
}

void test_insert_32data_2fib_01_001(int count)
{
    int i;
    for (i = 0; i < DATA_NUMBER_32; i++) {
        test_insert_object_ip4forward(g_stmt_sync, DATA_NUMBER_32 * count + i);
        test_insert_object_nhp_group(g_stmt_sync, DATA_NUMBER_32 * count + i);
        test_insert_object_nhp_group_node(g_stmt_sync, DATA_NUMBER_32 * count + i);
        test_insert_object_nhp(g_stmt_sync, DATA_NUMBER_32 * count + i);
        test_insert_object_nhp_std(g_stmt_sync, DATA_NUMBER_32 * count + i);
    }
}

//以下SN_01_001~~006为场景1的细分场景
// 1)采用顺序(a->(b->c)->(d->e))*1k,下发数据测试
TEST_F(SupportPathBasicPushCapability_8, SN_008_022)
{
    printf("...SN_01_001 Start...\n");
    int ret, i;
    int userDataIdx = 0;
    //记录开始下发数据和最后订阅回调消息的时间
    struct timeval start, end;
    double timeUsed;

    //订阅#7的insert
    readJanssonFile("schema_file/fib_path_7_subinfo_insert.gmjson", &g_sub_info_insert);
    ASSERT_NE((void *)NULL, g_sub_info_insert);
    GmcSubConfigT tmp_g_sub_info_insert;
    tmp_g_sub_info_insert.subsName = (char *)"subPath_7_insert";
    tmp_g_sub_info_insert.configJson = g_sub_info_insert;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info_insert, g_conn_sub, sn_callback_not_cmp, user_data);
    ASSERT_EQ(GMERR_OK, ret);
    printf("Subscribe for Path7 Success!\n");

    //记录开始时间
    gettimeofday(&start, NULL);

    //(a->(b->c)->(d->e))*4k顺序下发数据,每张表都下发256k
    // 256K的数据在8k个事务中执行,每个下发32数据量
    for (i = 0; i < TEST_NUMBER_16 * 2; i++) {
        //开启事务
        GmcTxConfigT config;
        config.transMode = GMC_TRANS_USED_IN_CS;
        config.type = GMC_TX_ISOLATION_COMMITTED;
        config.readOnly = false;
        ret = GmcTransStart(g_conn_sync, &config);
        ASSERT_EQ(GMERR_OK, ret);

        test_insert_32data_2fib_01_001(i);
        //事务commit
        ret = GmcTransCommit(g_conn_sync);
        ASSERT_EQ(GMERR_OK, ret);

        ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 32);
        EXPECT_EQ(GMERR_OK, ret);
    }

    printf("[Info]Insert all datas OK...\n");

    //记录最后推送消息结束时间
    gettimeofday(&end, NULL);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName_insert);
    ASSERT_EQ(GMERR_OK, ret);

    timeUsed = (end.tv_sec - start.tv_sec) + (end.tv_usec - start.tv_usec) / 1000000.0;
    printf("---FIB Scenario (a->(b->c)->(d->e))*1k, CostTime: %f seconds.---\r\n", timeUsed);
    printf("...SN_01_001 End...\n");
}

void test_insert_32data_2fib_01_002(int count)
{
    int i;
    for (i = 0; i < DATA_NUMBER_32; i++) {
        test_insert_object_nhp(g_stmt_sync, DATA_NUMBER_32 * count + i);
        test_insert_object_nhp_std(g_stmt_sync, DATA_NUMBER_32 * count + i);
        test_insert_object_nhp_group(g_stmt_sync, DATA_NUMBER_32 * count + i);
        test_insert_object_nhp_group_node(g_stmt_sync, DATA_NUMBER_32 * count + i);
        test_insert_object_ip4forward(g_stmt_sync, DATA_NUMBER_32 * count + i);
    }
}

// 1)采用顺序((d->e)->(b->c)->a)*1k,下发数据测试
TEST_F(SupportPathBasicPushCapability_8, SN_008_023)
{
    printf("...SN_01_002 Start...\n");
    int ret, i;
    int userDataIdx = 0;
    //记录开始下发数据和最后订阅回调消息的时间
    struct timeval start, end;
    double timeUsed;
    // pthread_t threads[THREAD_NUM_8];
    pthread_t threads[1];

    //订阅#7的insert
    readJanssonFile("schema_file/fib_path_7_subinfo_insert.gmjson", &g_sub_info_insert);
    ASSERT_NE((void *)NULL, g_sub_info_insert);
    GmcSubConfigT tmp_g_sub_info_insert;
    tmp_g_sub_info_insert.subsName = (char *)"subPath_7_insert";
    tmp_g_sub_info_insert.configJson = g_sub_info_insert;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info_insert, g_conn_sub, sn_callback_not_cmp, user_data);
    ASSERT_EQ(GMERR_OK, ret);

    //记录开始时间
    gettimeofday(&start, NULL);

    // 4K的数据在128个事务中执行
    for (i = 0; i < TEST_NUMBER_16 * 2; i++) {
        //开启事务
        GmcTxConfigT config;
        config.transMode = GMC_TRANS_USED_IN_CS;
        config.type = GMC_TX_ISOLATION_COMMITTED;
        config.readOnly = false;
        ret = GmcTransStart(g_conn_sync, &config);
        ASSERT_EQ(GMERR_OK, ret);

        test_insert_32data_2fib_01_002(i);
        //事务commit
        ret = GmcTransCommit(g_conn_sync);
        ASSERT_EQ(GMERR_OK, ret);

        ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 32);
        EXPECT_EQ(GMERR_OK, ret);
    }

    printf("[Info]Insert all datas Success...\n");

    //记录最后推送消息结束时间
    gettimeofday(&end, NULL);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName_insert);
    ASSERT_EQ(GMERR_OK, ret);

    timeUsed = (end.tv_sec - start.tv_sec) + (end.tv_usec - start.tv_usec) / 1000000.0;
    printf("---FIB Scenario ((d->e)->(b->c)->a)*1k, CostTime: %f seconds.---\r\n", timeUsed);
    printf("...SN_01_002 End...\n");
}

void test_insert_32data_2fib_01_003(int count)
{
    int i;
    for (i = 0; i < DATA_NUMBER_32; i++) {
        test_insert_object_nhp_group(g_stmt_sync, DATA_NUMBER_32 * count + i);
        test_insert_object_nhp_group_node(g_stmt_sync, DATA_NUMBER_32 * count + i);
        test_insert_object_ip4forward(g_stmt_sync, DATA_NUMBER_32 * count + i);
        test_insert_object_nhp(g_stmt_sync, DATA_NUMBER_32 * count + i);
        test_insert_object_nhp_std(g_stmt_sync, DATA_NUMBER_32 * count + i);
    }
}

// 1)采用顺序((b->c)->a->(d->e))*1k,下发数据测试
TEST_F(SupportPathBasicPushCapability_8, SN_008_024)
{
    printf("...SN_01_003 Start...\n");
    int ret, i;
    int userDataIdx = 0;
    //记录开始下发数据和最后订阅回调消息的时间
    struct timeval start, end;
    double timeUsed;

    //订阅#7的insert
    readJanssonFile("schema_file/fib_path_7_subinfo_insert.gmjson", &g_sub_info_insert);
    ASSERT_NE((void *)NULL, g_sub_info_insert);
    GmcSubConfigT tmp_g_sub_info_insert;
    tmp_g_sub_info_insert.subsName = (char *)"subPath_7_insert";
    tmp_g_sub_info_insert.configJson = g_sub_info_insert;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info_insert, g_conn_sub, sn_callback_not_cmp, user_data);
    ASSERT_EQ(GMERR_OK, ret);

    //记录开始时间
    gettimeofday(&start, NULL);
    // 256K的数据在2k个事务中执行
    for (i = 0; i < TEST_NUMBER_16 * 2; i++) {
        //开启事务
        GmcTxConfigT config;
        config.transMode = GMC_TRANS_USED_IN_CS;
        config.type = GMC_TX_ISOLATION_COMMITTED;
        config.readOnly = false;
        ret = GmcTransStart(g_conn_sync, &config);
        ASSERT_EQ(GMERR_OK, ret);

        test_insert_32data_2fib_01_002(i);
        //事务commit
        ret = GmcTransCommit(g_conn_sync);
        ASSERT_EQ(GMERR_OK, ret);

        ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 32);
        EXPECT_EQ(GMERR_OK, ret);
    }

    printf("[Info]Insert all datas OK...\n");

    //记录最后推送消息结束时间
    gettimeofday(&end, NULL);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName_insert);
    ASSERT_EQ(GMERR_OK, ret);

    timeUsed = (end.tv_sec - start.tv_sec) + (end.tv_usec - start.tv_usec) / 1000000.0;
    printf("---FIB Scenario ((b->c)->a->(d->e))*1k, CostTime: %f seconds.---\r\n", timeUsed);
    printf("...SN_01_003 End...\n");
}

void test_insert_128data_7(int count)
{
    int i;
    for (i = 0; i < 128; i++) {
        test_insert_object_ip4forward(g_stmt_sync, 128 * count + i);
    }
}

void test_insert_128data_8and9(int count)
{
    int i;
    for (i = 0; i < 128; i++) {
        test_insert_object_nhp_group(g_stmt_sync, 128 * count + i);
        test_insert_object_nhp_group_node(g_stmt_sync, 128 * count + i);
    }
}

void test_insert_128data_10and42(int count)
{
    int i;
    for (i = 0; i < 128; i++) {
        test_insert_object_nhp(g_stmt_sync, 128 * count + i);
        test_insert_object_nhp_std(g_stmt_sync, 128 * count + i);
    }
}

// 1)采用顺序(d->e)*1k+(b->c)*1k+a*1k,下发数据测试
TEST_F(SupportPathBasicPushCapability_8, SN_008_025)
{
    printf("...SN_01_005 Start...\n");
    int ret, i;
    int userDataIdx = 0;
    //记录开始下发数据和最后订阅回调消息的时间
    struct timeval start, end;
    double timeUsed;

    //订阅#7的insert
    readJanssonFile("schema_file/fib_path_7_subinfo_insert.gmjson", &g_sub_info_insert);
    ASSERT_NE((void *)NULL, g_sub_info_insert);
    GmcSubConfigT tmp_g_sub_info_insert;
    tmp_g_sub_info_insert.subsName = (char *)"subPath_7_insert";
    tmp_g_sub_info_insert.configJson = g_sub_info_insert;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info_insert, g_conn_sub, sn_callback_not_cmp, user_data);
    ASSERT_EQ(GMERR_OK, ret);

    //记录开始时间
    gettimeofday(&start, NULL);
    // 16k的d、e表数据
    for (i = 0; i < TEST_NUMBER_64 / 8; i++) {
        //开启事务
        GmcTxConfigT config;
        config.transMode = GMC_TRANS_USED_IN_CS;
        config.type = GMC_TX_ISOLATION_COMMITTED;
        config.readOnly = false;
        ret = GmcTransStart(g_conn_sync, &config);
        ASSERT_EQ(GMERR_OK, ret);

        test_insert_128data_10and42(i);
        //事务commit
        ret = GmcTransCommit(g_conn_sync);
        ASSERT_EQ(GMERR_OK, ret);
    }
    // 16k的b、c表数据
    for (i = 0; i < TEST_NUMBER_64 / 8; i++) {
        //开启事务
        GmcTxConfigT config;
        config.transMode = GMC_TRANS_USED_IN_CS;
        config.type = GMC_TX_ISOLATION_COMMITTED;
        config.readOnly = false;
        ret = GmcTransStart(g_conn_sync, &config);
        ASSERT_EQ(GMERR_OK, ret);

        test_insert_128data_8and9(i);
        //事务commit
        ret = GmcTransCommit(g_conn_sync);
        ASSERT_EQ(GMERR_OK, ret);
    }
    // 256k的7号表数据
    for (i = 0; i < TEST_NUMBER_32 / 4; i++) {
        //开启事务
        GmcTxConfigT config;
        config.transMode = GMC_TRANS_USED_IN_CS;
        config.type = GMC_TX_ISOLATION_COMMITTED;
        config.readOnly = false;
        ret = GmcTransStart(g_conn_sync, &config);
        ASSERT_EQ(GMERR_OK, ret);

        test_insert_128data_7(i);
        //事务commit
        ret = GmcTransCommit(g_conn_sync);
        ASSERT_EQ(GMERR_OK, ret);

        // ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 128);
        EXPECT_EQ(GMERR_OK, ret);
    }

    printf("[Info]Insert ip4forward datas OK...\n");

    //记录最后推送消息结束时间
    gettimeofday(&end, NULL);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName_insert);
    ASSERT_EQ(GMERR_OK, ret);

    timeUsed = (end.tv_sec - start.tv_sec) + (end.tv_usec - start.tv_usec) / 1000000.0;
    printf("---FIB Scenario (d->e)*256k+(b->c)*256k+a*256k, CostTime: %f seconds.---\r\n", timeUsed);
    printf("...SN_01_005 End...\n");
}

// 1)采用顺序(b->c)*1k+(d->e)*1k+a*1k,下发数据测试
TEST_F(SupportPathBasicPushCapability_8, SN_008_026)
{
    printf("...SN_01_006 Start...\n");
    int ret, i;
    int userDataIdx = 0;
    //记录开始下发数据和最后订阅回调消息的时间
    struct timeval start, end;
    double timeUsed;

    //订阅#7的insert
    readJanssonFile("schema_file/fib_path_7_subinfo_insert.gmjson", &g_sub_info_insert);
    ASSERT_NE((void *)NULL, g_sub_info_insert);
    GmcSubConfigT tmp_g_sub_info_insert;
    tmp_g_sub_info_insert.subsName = (char *)"subPath_7_insert";
    tmp_g_sub_info_insert.configJson = g_sub_info_insert;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info_insert, g_conn_sub, sn_callback_not_cmp, user_data);
    ASSERT_EQ(GMERR_OK, ret);

    //记录开始时间
    gettimeofday(&start, NULL);

    // 16k的b、c表数据
    for (i = 0; i < TEST_NUMBER_64 / 8; i++) {
        //开启事务
        GmcTxConfigT config;
        config.transMode = GMC_TRANS_USED_IN_CS;
        config.type = GMC_TX_ISOLATION_COMMITTED;
        config.readOnly = false;
        ret = GmcTransStart(g_conn_sync, &config);
        ASSERT_EQ(GMERR_OK, ret);

        test_insert_128data_8and9(i);
        //事务commit
        ret = GmcTransCommit(g_conn_sync);
        ASSERT_EQ(GMERR_OK, ret);
    }
    // 16K的d、e表数据
    for (i = 0; i < TEST_NUMBER_64 / 8; i++) {
        //开启事务
        GmcTxConfigT config;
        config.transMode = GMC_TRANS_USED_IN_CS;
        config.type = GMC_TX_ISOLATION_COMMITTED;
        config.readOnly = false;
        ret = GmcTransStart(g_conn_sync, &config);
        ASSERT_EQ(GMERR_OK, ret);

        test_insert_128data_10and42(i);
        //事务commit
        ret = GmcTransCommit(g_conn_sync);
        ASSERT_EQ(GMERR_OK, ret);
    }
    // 1k的7号表数据
    for (i = 0; i < TEST_NUMBER_32 / 4; i++) {
        //开启事务
        GmcTxConfigT config;
        config.transMode = GMC_TRANS_USED_IN_CS;
        config.type = GMC_TX_ISOLATION_COMMITTED;
        config.readOnly = false;
        ret = GmcTransStart(g_conn_sync, &config);
        ASSERT_EQ(GMERR_OK, ret);

        test_insert_128data_7(i);
        //事务commit
        ret = GmcTransCommit(g_conn_sync);
        ASSERT_EQ(GMERR_OK, ret);

        // ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 128);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("[Info]Insert ip4forward datas OK...\n");

    //记录最后推送消息结束时间
    gettimeofday(&end, NULL);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName_insert);
    ASSERT_EQ(GMERR_OK, ret);

    timeUsed = (end.tv_sec - start.tv_sec) + (end.tv_usec - start.tv_usec) / 1000000.0;
    // printf("---FIB Scenario (b->c)*256k+(d->e)*256k+a*256k, CostTime: %f seconds.---\r\n", timeUsed);
    printf("...SN_01_006 End...\n");
}

void test_insert_object_ip4forward_with_same(GmcStmtT *stmt, int value)
{
    int ret;
    uint8_t value_u8 = value;
    uint16_t value_u16 = value;
    uint32_t value_u32 = value;
    uint32_t value_0 = 0;
    uint64_t value_u64 = value;
    int32_t fixed[16] = {0};
    void *vertexLabel = NULL;
    for (int i = 0; i < 16; i++) {
        fixed[i] = value + i;
    }

    ret = testGmcPrepareStmtByLabelName(stmt, vertexLabelNameT7, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    //主键字段
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    //主键字段
    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    //主键字段
    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    //主键字段
    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &value_u8, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "nhp_group_flag", GMC_DATATYPE_UINT8, &value_u8, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &value_u16, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    // nhp_group_id设置值都为0，
    ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT32, &value_0, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "path_flags", GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "flags", GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "status_high_prio", GMC_DATATYPE_UINT8, &value_u8, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "status_normal_prio", GMC_DATATYPE_UINT8, &value_u8, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "errcode_high_prio", GMC_DATATYPE_UINT8, &value_u8, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "errcode_normal_prio", GMC_DATATYPE_UINT8, &value_u8, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "svc_ctx_high_prio", GMC_DATATYPE_FIXED, fixed, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "svc_ctx_normal_prio", GMC_DATATYPE_FIXED, fixed, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "table_smooth_id", GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &value_u64, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "trace", GMC_DATATYPE_UINT64, &value_u64, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "route_flags", GMC_DATATYPE_UINT16, &value_u16, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "reserved", GMC_DATATYPE_UINT16, &value_u16, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "time_stamp_create", GMC_DATATYPE_TIME, &value_u64, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "time_stamp_smooth", GMC_DATATYPE_TIME, &value_u64, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    wait_insert_OK(stmt, ret);
    ASSERT_EQ(GMERR_OK, ret);

    ASSERT_EQ(GMERR_OK, ret);

    if (value > 0 && value % 1024 == 0) {
        printf("Inserted %d %s data.\n", value, "ip4forward");
    }
}

void test_insert_128data_7_with_same(int count)
{
    int i;
    for (i = 0; i < DATA_NUMBER_128; i++) {
        test_insert_object_ip4forward_with_same(g_stmt_sync, DATA_NUMBER_128 * count + i);
    }
}

//场景2(fib下发性能场景)--比例：1k:1:1:1:1测试性能吞吐量
TEST_F(SupportPathBasicPushCapability_8, SN_008_027)
{
    printf("...SN_02_001 Start...\n");
    int ret, i, m, value = 0;
    int userDataIdx = 0;
    //记录开始下发数据和最后订阅回调消息的时间
    struct timeval start, end;
    double testOps;

    //订阅#7的insert
    readJanssonFile("schema_file/fib_path_7_subinfo_insert.gmjson", &g_sub_info_insert);
    ASSERT_NE((void *)NULL, g_sub_info_insert);
    GmcSubConfigT tmp_g_sub_info_insert;
    tmp_g_sub_info_insert.subsName = (char *)"subPath_7_insert";
    tmp_g_sub_info_insert.configJson = g_sub_info_insert;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info_insert, g_conn_sub, sn_callback_not_cmp, user_data);
    ASSERT_EQ(GMERR_OK, ret);

    //首先按照1:1:1:1的比例预置b/c/d/e表数据
    test_insert_object_nhp_group(g_stmt_sync, value);
    test_insert_object_nhp_group_node(g_stmt_sync, value);
    test_insert_object_nhp(g_stmt_sync, value);
    test_insert_object_nhp_std(g_stmt_sync, value);
    printf("Prepare BCDE with one data, OK!\n");

    //写6k数据至ip4forward，测试吞吐量
    gettimeofday(&start, NULL);
    // 4M的数据在4K个事务中执行
    for (i = 0; i < TEST_NUMBER_48 / 6; i++) {
        //开启事务
        GmcTxConfigT config;
        config.transMode = GMC_TRANS_USED_IN_CS;
        config.type = GMC_TX_ISOLATION_COMMITTED;
        config.readOnly = false;
        ret = GmcTransStart(g_conn_sync, &config);
        ASSERT_EQ(GMERR_OK, ret);

        test_insert_128data_7_with_same(i);
        //事务commit
        ret = GmcTransCommit(g_conn_sync);
        ASSERT_EQ(GMERR_OK, ret);

        // ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 128);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("[Info]Insert ip4forward datas OK...\n");

    ret = GmcUnSubscribe(g_stmt_sync, g_subName_insert);
    ASSERT_EQ(GMERR_OK, ret);

    gettimeofday(&end, NULL);
    testOps = DATA_NUMBER_6K / ((end.tv_sec - start.tv_sec) + (end.tv_usec - start.tv_usec) / 1000000.0);
    // printf("---FIB Scenario 比例：4M:1:1:1:1测试性能吞吐量, TestOPS: %f .---\r\n", testOps);
    printf("...SN_02_001 End...\n");
}

//场景2(fib下发性能场景)--比例：1k:1:128:128:128*128测试性能吞吐量
TEST_F(SupportPathBasicPushCapability_8, SN_008_028)
{
    printf("...SN_02_002 Start...\n");
    int ret, i, m, value = 0;
    int userDataIdx = 0;
    //记录开始下发数据和最后订阅回调消息的时间
    struct timeval start, end;
    double testOps;

    //订阅#7的insert
    readJanssonFile("schema_file/fib_path_7_subinfo_insert.gmjson", &g_sub_info_insert);
    ASSERT_NE((void *)NULL, g_sub_info_insert);
    GmcSubConfigT tmp_g_sub_info_insert;
    tmp_g_sub_info_insert.subsName = (char *)"subPath_7_insert";
    tmp_g_sub_info_insert.configJson = g_sub_info_insert;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info_insert, g_conn_sub, sn_callback_not_cmp, user_data);
    ASSERT_EQ(GMERR_OK, ret);

    //首先按照1:128:128:128的比例预置b/c/d/e表数据
    test_insert_object_nhp_group(g_stmt_sync, value);
    for (i = 0; i < DATA_NUMBER_128; i++) {
        test_insert_object_nhp_group_node(g_stmt_sync, i);
        test_insert_object_nhp(g_stmt_sync, i);
    }
    //添加128*128条E数据
    for (i = 0; i < DATA_NUMBER_128; i++) {
        //开启事务
        GmcTxConfigT config;
        config.transMode = GMC_TRANS_USED_IN_CS;
        config.type = GMC_TX_ISOLATION_COMMITTED;
        config.readOnly = false;
        ret = GmcTransStart(g_conn_sync, &config);
        ASSERT_EQ(GMERR_OK, ret);

        for (m = 0; m < DATA_NUMBER_128; m++) {
            test_insert_object_nhp_std(g_stmt_sync, i * DATA_NUMBER_128 + m);
            ;
        }
        //事务commit
        ret = GmcTransCommit(g_conn_sync);
        ASSERT_EQ(GMERR_OK, ret);
    }
    printf("Prepare BCDE with 1:128:128:128*128 data, OK!\n");

    gettimeofday(&start, NULL);

    for (i = 0; i < TEST_NUMBER_48 / 6; i++) {
        //开启事务
        GmcTxConfigT config;
        config.transMode = GMC_TRANS_USED_IN_CS;
        config.type = GMC_TX_ISOLATION_COMMITTED;
        config.readOnly = false;
        ret = GmcTransStart(g_conn_sync, &config);
        ASSERT_EQ(GMERR_OK, ret);

        test_insert_128data_7_with_same(i);
        //事务commit
        ret = GmcTransCommit(g_conn_sync);
        ASSERT_EQ(GMERR_OK, ret);

        // ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 128);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("[Info]Insert ip4forward datas OK...\n");

    ret = GmcUnSubscribe(g_stmt_sync, g_subName_insert);
    ASSERT_EQ(GMERR_OK, ret);

    gettimeofday(&end, NULL);
    testOps = DATA_NUMBER_6K / 6 / ((end.tv_sec - start.tv_sec) + (end.tv_usec - start.tv_usec) / 1000000.0);
    // printf("---FIB Scenario 比例：4M:1:128:128:128*128测试性能吞吐量, TestOPS: %f .---\r\n", testOps);
    printf("...SN_02_002 End...\n");
}
