#include "StatusMergePubSub.h"

int main(int argc, char **argv)
{
    AW_FUN_Log(LOG_INFO, "SubReliPrc1 begin %d.", getpid());

    if (argc != 2) {
        AW_FUN_Log(LOG_ERROR, "no parameter.");
        return -1;
    }

    uint32_t index = atoi(argv[1]);
    const char *subFilePath = (index == 1) ? g_statusMergePunSubPath1 : g_statusMergePunSubPath8;

    GmcConnT *conn1 = NULL, *connSub1 = NULL;
    GmcStmtT *stmt1 = NULL, *stmtSub1 = NULL;

    free(g_epollData.events);
    memset(&g_epollData, 0, sizeof(g_epollData));
    free(g_timeoutEpollData.events);
    memset(&g_timeoutEpollData, 0, sizeof(g_timeoutEpollData));
    GmcStopHeartbeat(NULL);

    int ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建连
    ret = testGmcConnect(&conn1, &stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建状态合并订阅连接1
    ret = testSubConnect(&connSub1, &stmtSub1, 1, g_epoll_reg_info, g_subConnName, &g_chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建状态合并表1
    testGmcCreateVertex(stmt1, g_statusMergeVertexPath1, g_schema);

    // 通道1绑定订阅关系1
    testGmcSubscribe(stmt1, connSub1, subFilePath, g_subName, g_subInfo);

    // 断连
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 客户端异常退出
    char killPid[20] = {0};
    sprintf(killPid, "kill -9 %d", getpid());
    AW_FUN_Log(LOG_INFO, "killPid : %s.", killPid);
    system(killPid);
    exit(0);

    AW_FUN_Log(LOG_INFO, "SubReliPrc1 end %d.", getpid());
}
