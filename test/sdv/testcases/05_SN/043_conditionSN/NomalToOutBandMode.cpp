#include "OutBanModeCommon.h"

class NomalToOutBandMode : public testing::Test {
public:
    SnUserDataT *userData;
    SnUserDataT *userData1;
    SnUserDataT *userData2;
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void NomalToOutBandMode::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/start.sh");
    testEnvInit();
    create_epoll_thread();
}

void NomalToOutBandMode::TearDownTestCase()
{
    close_epoll_thread();
    testEnvClean();
}

void NomalToOutBandMode::SetUp()
{
    int ret;

    ret = testSnMallocUserData(&userData, g_dataNum * 2, g_dataNum * 2);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testSnMallocUserData(&userData1, g_dataNum * 2, g_dataNum * 2);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testSnMallocUserData(&userData2, g_dataNum * 2, g_dataNum * 2);
    ASSERT_EQ(GMERR_OK, ret);

    // 创建同步连接
    g_connSync = NULL;
    g_stmtSync = NULL;
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    ASSERT_EQ(GMERR_OK, ret);

    g_schema = NULL;
    readJanssonFile("schema_file/all_type_schema.gmjson", &g_schema);
    EXPECT_NE((void *)NULL, g_schema);
    GmcDropVertexLabel(g_stmtSync, "T20_all_type");
    ret = GmcCreateVertexLabel(g_stmtSync, g_schema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);

    // 创建订阅连接
    int chanRingLen = 256;
    g_connSub = NULL;
    g_stmtSub = NULL;
    ret = testSubConnect(&g_connSub, &g_stmtSub, 1, g_epoll_reg_info, g_subConnName, &chanRingLen);
    ASSERT_EQ(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN(0);
}

void NomalToOutBandMode::TearDown()
{
    int ret;

    // 释放订阅连接
    ret = testSubDisConnect(g_connSub, g_stmtSub);
    ASSERT_EQ(GMERR_OK, ret);

    // 删表断连
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    ASSERT_EQ(GMERR_OK, ret);

    free(g_subInfoJson);
    free(g_schema);
    testSnFreeUserData(userData);
    testSnFreeUserData(userData1);
    testSnFreeUserData(userData2);

    AW_CHECK_LOG_END();
}

// 创建单个非条件订阅关系，单写插入数据，推送订阅后，增加多个非条件订阅
TEST_F(NomalToOutBandMode, SN_043_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret;
    int32_t i;
    int userDataIdx = 0;
    int userDataIdx1 = 0;
    int userDataIdx2 = 0;
    int affectRows = 0;

    ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    readJanssonFile("subinfo_file/all_type_schema_subinfo_insert.gmjson", &g_subInfoJson);
    EXPECT_NE((void *)NULL, g_subInfoJson);

    GmcSubConfigT subInfo;
    subInfo.subsName = g_subName;
    subInfo.configJson = g_subInfoJson;
    ret = GmcSubscribe(g_stmtSync, &subInfo, g_connSub, sn_callback, userData);
    ASSERT_EQ(GMERR_OK, ret);

    // 写数据
    for (i = 0; i < g_dataNum; i++) {
        ((int *)(userData->new_value))[userDataIdx] = i;
        userDataIdx++;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        testSetVertexProperty(g_stmtSync, i);
        ret = GmcExecute(g_stmtSync);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmtSync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1, affectRows);
        // 检查所写
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        ASSERT_EQ(GMERR_OK, ret);
        testCheckVertexProperty(g_stmtSync, g_lablePk, i);
    }

    // 弱校验，仅校验推送次数
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INSERT, g_dataNum);
    ASSERT_EQ(0, ret);

    GmcSubConfigT subInfo1;
    GmcSubConfigT subInfo2;
    subInfo1.subsName = g_subName1;
    subInfo1.configJson = g_subInfoJson;
    subInfo2.subsName = g_subName2;
    subInfo2.configJson = g_subInfoJson;
    ret = GmcSubscribe(g_stmtSync, &subInfo1, g_connSub, sn_callback1, userData1);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSubscribe(g_stmtSync, &subInfo2, g_connSub, sn_callback2, userData2);
    ASSERT_EQ(GMERR_OK, ret);

    // 写数据
    for (i = g_dataNum; i < g_dataNum + g_dataNum; i++) {
        ((int *)(userData->new_value))[userDataIdx] = i;
        userDataIdx++;
        ((int *)(userData1->new_value))[userDataIdx1] = i;
        userDataIdx1++;
        ((int *)(userData2->new_value))[userDataIdx2] = i;
        userDataIdx2++;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        testSetVertexProperty(g_stmtSync, i);
        ret = GmcExecute(g_stmtSync);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmtSync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1, affectRows);
        // 检查所写
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        ASSERT_EQ(GMERR_OK, ret);
        testCheckVertexProperty(g_stmtSync, g_lablePk, i);
    }

    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INSERT, g_dataNum);
    ASSERT_EQ(0, ret);
    ret = testWaitSnRecv(userData1, GMC_SUB_EVENT_INSERT, g_dataNum);
    ASSERT_EQ(0, ret);
    ret = testWaitSnRecv(userData2, GMC_SUB_EVENT_INSERT, g_dataNum);
    ASSERT_EQ(0, ret);

    ret = GmcUnSubscribe(g_stmtSync, g_subName);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, g_subName1);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, g_subName2);
    ASSERT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建单个非条件订阅关系，单写插入数据，推送订阅后，切换为批量写
TEST_F(NomalToOutBandMode, SN_043_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret;
    int32_t i;
    int userDataIdx = 0;
    int userDataIdx1 = 0;
    int userDataIdx2 = 0;
    int affectRows = 0;

    ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    readJanssonFile("subinfo_file/all_type_schema_subinfo_insert.gmjson", &g_subInfoJson);
    EXPECT_NE((void *)NULL, g_subInfoJson);

    GmcSubConfigT subInfo;
    subInfo.subsName = g_subName;
    subInfo.configJson = g_subInfoJson;
    ret = GmcSubscribe(g_stmtSync, &subInfo, g_connSub, sn_callback, userData);
    ASSERT_EQ(GMERR_OK, ret);

    // 写数据
    for (i = 0; i < g_dataNum; i++) {
        ((int *)(userData->new_value))[userDataIdx] = i;
        userDataIdx++;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        testSetVertexProperty(g_stmtSync, i);
        ret = GmcExecute(g_stmtSync);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmtSync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1, affectRows);
        // 检查所写
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        ASSERT_EQ(GMERR_OK, ret);
        testCheckVertexProperty(g_stmtSync, g_lablePk, i);
    }

    // 弱校验，仅校验推送次数
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INSERT, g_dataNum);
    ASSERT_EQ(0, ret);

    // 批量写数据
    ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_connSync, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (i = g_dataNum; i < g_dataNum + g_dataNum; i++) {
        ((int *)(userData->new_value))[userDataIdx] = i;
        userDataIdx++;
        testSetVertexProperty(g_stmtSync, i);
        ret = GmcBatchAddDML(batch, g_stmtSync);
        ASSERT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(g_dataNum, totalNum);
    ASSERT_EQ(g_dataNum, successNum);
    GmcBatchDestroy(batch);

    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INSERT, g_dataNum);
    ASSERT_EQ(0, ret);

    ret = GmcUnSubscribe(g_stmtSync, g_subName);
    ASSERT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建单个非条件订阅关系，单写插入数据，推送订阅后，增加多个非条件订阅，切换批量写
TEST_F(NomalToOutBandMode, SN_043_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret;
    int32_t i;
    int userDataIdx = 0;
    int userDataIdx1 = 0;
    int userDataIdx2 = 0;
    int affectRows = 0;

    ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    readJanssonFile("subinfo_file/all_type_schema_subinfo_insert.gmjson", &g_subInfoJson);
    EXPECT_NE((void *)NULL, g_subInfoJson);

    GmcSubConfigT subInfo;
    subInfo.subsName = g_subName;
    subInfo.configJson = g_subInfoJson;
    ret = GmcSubscribe(g_stmtSync, &subInfo, g_connSub, sn_callback, userData);
    ASSERT_EQ(GMERR_OK, ret);

    // 写数据
    for (i = 0; i < g_dataNum; i++) {
        ((int *)(userData->new_value))[userDataIdx] = i;
        userDataIdx++;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        testSetVertexProperty(g_stmtSync, i);
        ret = GmcExecute(g_stmtSync);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmtSync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1, affectRows);
        // 检查所写
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        ASSERT_EQ(GMERR_OK, ret);
        testCheckVertexProperty(g_stmtSync, g_lablePk, i);
    }

    // 弱校验，仅校验推送次数
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INSERT, g_dataNum);
    ASSERT_EQ(0, ret);

    GmcSubConfigT subInfo1;
    GmcSubConfigT subInfo2;
    subInfo1.subsName = g_subName1;
    subInfo1.configJson = g_subInfoJson;
    subInfo2.subsName = g_subName2;
    subInfo2.configJson = g_subInfoJson;
    ret = GmcSubscribe(g_stmtSync, &subInfo1, g_connSub, sn_callback1, userData1);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSubscribe(g_stmtSync, &subInfo2, g_connSub, sn_callback2, userData2);
    ASSERT_EQ(GMERR_OK, ret);

    // 批量写数据
    ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_connSync, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (i = g_dataNum; i < g_dataNum + g_dataNum; i++) {
        ((int *)(userData->new_value))[userDataIdx] = i;
        userDataIdx++;
        ((int *)(userData1->new_value))[userDataIdx1] = i;
        userDataIdx1++;
        ((int *)(userData2->new_value))[userDataIdx2] = i;
        userDataIdx2++;
        testSetVertexProperty(g_stmtSync, i);
        ret = GmcBatchAddDML(batch, g_stmtSync);
        ASSERT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(g_dataNum, totalNum);
    ASSERT_EQ(g_dataNum, successNum);
    GmcBatchDestroy(batch);

    for (i = g_dataNum; i < g_dataNum + g_dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        ASSERT_EQ(GMERR_OK, ret);
        testCheckVertexProperty(g_stmtSync, g_lablePk, i);
    }

    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INSERT, g_dataNum);
    ASSERT_EQ(0, ret);
    ret = testWaitSnRecv(userData1, GMC_SUB_EVENT_INSERT, g_dataNum);
    ASSERT_EQ(0, ret);
    ret = testWaitSnRecv(userData2, GMC_SUB_EVENT_INSERT, g_dataNum);
    ASSERT_EQ(0, ret);

    ret = GmcUnSubscribe(g_stmtSync, g_subName);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, g_subName1);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, g_subName2);
    ASSERT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建单个非条件订阅关系，单写插入数据，推送订阅后，取消订阅再创建单个条件订阅
TEST_F(NomalToOutBandMode, SN_043_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret;
    int32_t i;
    int userDataIdx = 0;
    int userDataIdx1 = 0;
    int userDataIdx2 = 0;
    int affectRows = 0;

    ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    readJanssonFile("subinfo_file/all_type_schema_subinfo_insert.gmjson", &g_subInfoJson);
    EXPECT_NE((void *)NULL, g_subInfoJson);

    GmcSubConfigT subInfo;
    subInfo.subsName = g_subName;
    subInfo.configJson = g_subInfoJson;
    ret = GmcSubscribe(g_stmtSync, &subInfo, g_connSub, sn_callback, userData);
    ASSERT_EQ(GMERR_OK, ret);

    // 写数据
    for (i = 0; i < g_dataNum; i++) {
        ((int *)(userData->new_value))[userDataIdx] = i;
        userDataIdx++;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        testSetVertexProperty(g_stmtSync, i);
        ret = GmcExecute(g_stmtSync);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmtSync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1, affectRows);
        // 检查所写
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        ASSERT_EQ(GMERR_OK, ret);
        testCheckVertexProperty(g_stmtSync, g_lablePk, i);
    }

    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INSERT, g_dataNum);
    ASSERT_EQ(0, ret);

    ret = GmcUnSubscribe(g_stmtSync, g_subName);
    ASSERT_EQ(GMERR_OK, ret);

    free(g_subInfoJson);
    g_subInfoJson = NULL;
    readJanssonFile("subinfo_file/all_type_schema_subinfo_condition_insert.gmjson", &g_subInfoJson);
    EXPECT_NE((void *)NULL, g_subInfoJson);

    ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    GmcSubConfigT subInfo1;
    subInfo1.subsName = g_subName1;
    subInfo1.configJson = g_subInfoJson;
    ret = GmcSubscribe(g_stmtSync, &subInfo1, g_connSub, sn_callback_condition, userData);
    ASSERT_EQ(GMERR_OK, ret);

    // 写数据
    affectRows = 0;
    for (i = g_dataNum; i < g_dataNum + g_dataNum; i++) {
        ((int *)(userData->new_value))[userDataIdx] = i;
        userDataIdx++;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        testSetVertexProperty(g_stmtSync, i);
        ret = GmcExecute(g_stmtSync);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmtSync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1, affectRows);
        // 检查所写
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        ASSERT_EQ(GMERR_OK, ret);
        testCheckVertexProperty(g_stmtSync, g_lablePk, i);
    }

    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INSERT, g_dataNum);
    ASSERT_EQ(0, ret);

    ret = GmcUnSubscribe(g_stmtSync, g_subName1);
    ASSERT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
