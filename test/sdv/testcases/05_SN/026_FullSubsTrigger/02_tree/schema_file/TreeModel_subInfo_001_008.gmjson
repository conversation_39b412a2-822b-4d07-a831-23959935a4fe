{"name": "subVertexLabel", "label_name": "TEST_T0", "comment": "VertexLabel subscription", "events": [{"type": "initial_load", "msgTypes": ["new object", "key"]}, {"type": "insert", "msgTypes": ["new object", "old object"]}, {"type": "update", "msgTypes": ["new object", "old object"]}, {"type": "replace insert", "msgTypes": ["new object", "old object"]}, {"type": "replace update", "msgTypes": ["new object", "old object"]}, {"type": "merge insert", "msgTypes": ["new object", "old object"]}, {"type": "merge update", "msgTypes": ["new object", "old object"]}], "is_path": false, "retry": true, "constraint": {"operator_type": "and", "conditions": [{"property": "T1/P0", "value": 0}, {"property": "F7", "value": 0}, {"property": "T1/P19", "value": 0}]}}