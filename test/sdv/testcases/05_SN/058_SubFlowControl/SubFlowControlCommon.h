/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: SubFlowControlCommon.h
 * Author: gwx620465
 * Create: 2025-04-03
 */
#ifndef SUB_FLOW_CONTROL_COMMON_H
#define SUB_FLOW_CONTROL_COMMON_H

#include "t_datacom_lite.h"

#define MAX_CMD_SIZE 1024
#define MAX_NAME_LENGTH 128
#define FILED_FIX_SIZE 7
#define OPERRA_COUNT 10
#define MAX_SN_RING_SIZE ((16 * 1024) + 20)
#define FILE_PATH 512

GmcConnT *g_connSync = NULL;
GmcStmtT *g_stmtSync = NULL;
GmcConnT *g_connSub = NULL;
GmcStmtT *g_stmtSub = NULL;
const char *g_schemaPath = "schemaFile/SN058Label.gmjson";
const char *g_labelName = "SN058Label";
char g_labelConfig[] = "{\"max_record_count\":1000000}";
char g_lablePk[] = "pk";
const char *g_schemaPath2 = "schemaFile/SN058Label2.gmjson";
const char *g_label2Name = "SN058Label2";
char g_label2Config[] = "{\"max_record_count\":1000000, \"sub_flow_control\":[800000,900000,1000000]}";
char g_lable2Pk[] = "pk";
pthread_barrier_t gBarrier;
char g_inp1[MAX_NAME_LENGTH] = "inp1";    // 在datalog中定义的普通外部表的输入表
char g_dtlEtn[MAX_NAME_LENGTH] = "out1";  // 在datalog中定义的普通外部表
char g_fileName[FILE_PATH] = "pubsubRsc";

static const char *g_subSn058LabelJson =
    R"({
        "name":"subSn058Label",
        "label_name":"SN058Label",
        "events":
            [
                {"type":"insert", "msgTypes": ["new object", "key"]},
                {"type":"update", "msgTypes": ["new object", "old object"]},
                {"type":"delete", "msgTypes": ["old object", "key"]},
                {"type":"replace", "msgTypes":["new object", "old object"]}
            ],
        "constraint":
                {
                    "operator_type":"or",
                    "conditions":
                        [
                            {
                                "property": "F0"
                            }
                        ]
                }
    })";

static const char *g_subSn058Label2Json =
    R"({
        "name":"subSn058Label",
        "label_name":"SN058Label2",
        "events":
            [
                {"type":"insert", "msgTypes": ["new object", "key"]},
                {"type":"update", "msgTypes": ["new object", "old object"]},
                {"type":"delete", "msgTypes": ["old object", "key"]},
                {"type":"replace", "msgTypes":["new object", "old object"]}
            ]
    })";

static const char *g_subSn058Label3Json =
    R"({
        "name":"subSn058Label",
        "label_name":"SN058Label3",
        "events":
            [
                {"type":"insert", "msgTypes": ["new object", "key"]},
                {"type":"update", "msgTypes": ["new object", "old object"]},
                {"type":"delete", "msgTypes": ["old object", "key"]},
                {"type":"replace", "msgTypes":["new object", "old object"]}
            ],
        "constraint":
                {
                    "operator_type":"or",
                    "conditions":
                        [
                            {
                                "property": "F0"
                            }
                        ]
                }
    })";

static const char *g_subSn058Initial =
    R"({
        "name":"subSn058Label",
        "label_name":"SN058Label",
        "events":
            [
                {"type":"initial_load"}
            ],
        "constraint":
                {
                    "operator_type":"or",
                    "conditions":
                        [
                            {
                                "property": "F0"
                            }
                        ]
                }
    })";

static const char *g_subSn058Add =
    R"({
        "name":"subSn058Label",
        "label_name":"SN058Label",
        "events":
            [
                {"type":"insert", "msgTypes": ["new object", "key"]},
                {"type":"delete", "msgTypes": ["old object", "key"]},
                {"type":"replace", "msgTypes":["new object", "old object"]}
            ],
        "is_reliable":true
        
    })";

static const char *g_subSn058Label2 =
    R"({
        "name":"subSn058Label2",
        "label_name":"SN058Label2",
        "events":
            [
                {"type":"insert", "msgTypes": ["new object", "key"]},
                {"type":"delete", "msgTypes": ["old object", "key"]},
                {"type":"replace", "msgTypes":["new object", "old object"]}
            ],
        "is_reliable":true
    })";

static const char *g_subSn058Label2Condition =
    R"({
        "name":"subSn058Label2",
        "label_name":"SN058Label2",
        "events":
            [
                {"type":"insert", "msgTypes": ["new object", "key"]},
                {"type":"delete", "msgTypes": ["old object", "key"]},
                {"type":"replace", "msgTypes":["new object", "old object"]}
            ],
        "constraint":
                {
                    "operator_type":"or",
                    "conditions":
                        [
                            {
                                "property": "F0"
                            }
                        ]
                },
        "is_reliable":true
    })";

const char *g_stmgSubInfo = R"(
{
        "name": "sub2SN047V1",
        "label_name": "SN058Label",
        "comment":"status_merge",
        "events":
            [
                {"type":"modify", "msgTypes":["new object", "old object"]},
                {"type":"delete", "msgTypes":["new object", "old object"]}
            ],
        "subs_type":"status_merge"
})";

const char *g_subKvAdd =
    R"({
        "name":"subKvAdd",
        "label_name":"KvTable058",
        "events":
            [
                {"type":"set", "msgTypes":["new object", "old object", "key"]},
                {"type":"delete", "msgTypes":["new object", "old object", "key"]}
            ]
    })";

const char *g_subKvInitial =
    R"({
        "name":"subKvInitial",
        "label_name":"KvTable058",
        "events":
            [
                {"type":"initial_load"}
            ]
    })";

static const char *g_subSn058Out1 =
    R"({
        "name":"subSn058Out1",
        "label_name":"out1",
        "events":
            [
                {"type":"merge insert", "msgTypes": ["new object", "old object"]}
            ]
    })";

bool g_isBlock = true;
uint32_t g_controlSleepLevel = 0;

void TestControlSleepTime()
{
    switch (g_controlSleepLevel) {
        case 1: {
            usleep(100);
            break;
        }
        case 2: {
            usleep(200);
            break;
        }
        case 3: {
            usleep(1000);
            break;
        }
        case 4: {
            uint32_t sleepCount = 0;
            while (g_isBlock) {
                sleep(1);
                sleepCount++;
            }
            break;
        }
        default: {
            break;
        }
    }
}

void OldSnCallBack(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret = 0;
    char labelName[128] = {0};
    unsigned int labelNameLen = 0;
    SnUserDataT *user_data = (SnUserDataT *)userData;
    bool eof = false;
    TestControlSleepTime();

    while (!eof) {
        if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
            user_data->scanEofNum++;
            break;
        }

        if (info->eventType == GMC_SUB_EVENT_TRIGGER_SCAN_BEGIN) {
            user_data->triggerScanBeginNum++;
            break;
        }

        if (info->eventType == GMC_SUB_EVENT_TRIGGER_SCAN_END) {
            user_data->triggerScanEndNum++;
            break;
        }

        ret = GmcFetch(subStmt, &eof);  // fetch 1 次
        if (ret != GMERR_OK || eof == true) {
            break;
        }

        for (int i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            ret = GmcSubGetLabelName(subStmt, i, labelName, &(labelNameLen = sizeof(labelName)));
            TEST_EXPECT_INT32(GMERR_OK, ret);
            TEST_EXPECT_INT32(strlen(labelName), labelNameLen);
            // 默认推送new object和old object
            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    TEST_EXPECT_INT32(GMERR_OK, ret);

                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    TEST_EXPECT_INT32(GMERR_NO_DATA, ret);
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    TEST_EXPECT_INT32(GMERR_NO_DATA, ret);

                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_UPDATE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    TEST_EXPECT_INT32(GMERR_OK, ret);

                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_REPLACE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    TEST_EXPECT_INT32(GMERR_OK, ret);

                    break;
                }
                case GMC_SUB_EVENT_AGED: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    TEST_EXPECT_INT32(GMERR_NO_DATA, ret);
                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_INITIAL_LOAD: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    TEST_EXPECT_INT32(GMERR_NO_DATA, ret);
                    break;
                }
                case GMC_SUB_EVENT_MERGE_INSERT: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    TEST_EXPECT_INT32(GMERR_NO_DATA, ret);
                    break;
                }
                case GMC_SUB_EVENT_TRIGGER_SCAN: {
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_INFO, "default: [%s] [%d] invalid eventType %d.\n", __FUNCTION__, __LINE__,
                        info->eventType);
                    break;
                }
            }
            break;
        }
        user_data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE_INSERT: {
                user_data->mergeInsertNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_TRIGGER_SCAN: {
                user_data->triggerScanNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
            default: {
                AW_FUN_Log(
                    LOG_INFO, "default: [%s] [%d] invalid eventType %d.\n", __FUNCTION__, __LINE__, info->eventType);
                break;
            }
        }
    }
}

bool g_isCb2Block = false;
void OldSnCallBack2(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    while (g_isCb2Block) {
        sleep(1);
    };
    int ret = 0;
    char labelName[128] = {0};
    unsigned int labelNameLen = 0;
    SnUserDataT *user_data = (SnUserDataT *)userData;
    bool eof = false;

    while (!eof) {
        if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
            user_data->scanEofNum++;
            break;
        }

        ret = GmcFetch(subStmt, &eof);  // fetch 1 次
        if (ret != GMERR_OK || eof == true) {
            break;
        }

        for (int i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            ret = GmcSubGetLabelName(subStmt, i, labelName, &(labelNameLen = sizeof(labelName)));
            TEST_EXPECT_INT32(GMERR_OK, ret);
            TEST_EXPECT_INT32(strlen(labelName), labelNameLen);

            // 默认推送new object和old object
            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    TEST_EXPECT_INT32(GMERR_OK, ret);

                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    TEST_EXPECT_INT32(GMERR_NO_DATA, ret);
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    TEST_EXPECT_INT32(GMERR_NO_DATA, ret);

                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_UPDATE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    TEST_EXPECT_INT32(GMERR_OK, ret);

                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_REPLACE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    TEST_EXPECT_INT32(GMERR_OK, ret);

                    break;
                }
                case GMC_SUB_EVENT_AGED: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    TEST_EXPECT_INT32(GMERR_NO_DATA, ret);
                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_INITIAL_LOAD: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    TEST_EXPECT_INT32(GMERR_NO_DATA, ret);
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_INFO, "default: [%s] [%d] invalid eventType %d.\n", __FUNCTION__, __LINE__,
                        info->eventType);
                    break;
                }
            }
            break;
        }
        user_data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            default: {
                AW_FUN_Log(
                    LOG_INFO, "default: [%s] [%d] invalid eventType %d.\n", __FUNCTION__, __LINE__, info->eventType);
                break;
            }
        }
    }
}

bool g_isCb3Block = false;
void OldSnCallBack3(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    while (g_isCb3Block) {
        sleep(1);
    };
    int ret = 0;
    char labelName[128] = {0};
    unsigned int labelNameLen = 0;
    SnUserDataT *user_data = (SnUserDataT *)userData;
    bool eof = false;

    while (!eof) {
        if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
            user_data->scanEofNum++;
            break;
        }

        ret = GmcFetch(subStmt, &eof);  // fetch 1 次
        if (ret != GMERR_OK || eof == true) {
            break;
        }

        for (int i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            ret = GmcSubGetLabelName(subStmt, i, labelName, &(labelNameLen = sizeof(labelName)));
            TEST_EXPECT_INT32(GMERR_OK, ret);
            TEST_EXPECT_INT32(strlen(labelName), labelNameLen);

            // 默认推送new object和old object
            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    TEST_EXPECT_INT32(GMERR_OK, ret);

                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    TEST_EXPECT_INT32(GMERR_NO_DATA, ret);
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    TEST_EXPECT_INT32(GMERR_NO_DATA, ret);

                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_UPDATE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    TEST_EXPECT_INT32(GMERR_OK, ret);

                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_REPLACE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    TEST_EXPECT_INT32(GMERR_OK, ret);

                    break;
                }
                case GMC_SUB_EVENT_AGED: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    TEST_EXPECT_INT32(GMERR_NO_DATA, ret);
                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_INITIAL_LOAD: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    TEST_EXPECT_INT32(GMERR_NO_DATA, ret);
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_INFO, "default: [%s] [%d] invalid eventType %d.\n", __FUNCTION__, __LINE__,
                        info->eventType);
                    break;
                }
            }
            break;
        }
        user_data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            default: {
                AW_FUN_Log(
                    LOG_INFO, "default: [%s] [%d] invalid eventType %d.\n", __FUNCTION__, __LINE__, info->eventType);
                break;
            }
        }
    }
}

void NewSnCallBack(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret = 0;
    int index = 0;
    int addVal = 0;
    char labelName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *data = (SnUserDataT *)userData;
    bool eof = false;

    while (!eof) {
        if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
            data->scanEofNum++;
            break;
        }

        ret = GmcFetch(subStmt, &eof);  // fetch 1 次
        if (ret != GMERR_OK || eof == true) {
            break;
        }

        /* 多表情景下不好保证推送得到的表顺序是恒定的 */
        for (int i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            uint32_t labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(strlen(labelName), labelNameLen);

            switch (info->eventType) {
                case GMC_SUB_EVENT_MODIFY: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_INITIAL_LOAD: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_INFO, "default: invalid eventType %s .", info->eventType);
                    break;
                }
            }
        }
        data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_MODIFY: {
                data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                data->scanNum++;
                break;
            }
            default: {
                AW_FUN_Log(LOG_INFO, "default: invalid eventType.");
                break;
            }
        }
    }
}

void TestCheckOtherValue(GmcStmtT *subStmt, uint32_t indexVal, uint32_t addVal)
{
    int ret = 0;
    char f2Val = (char)(indexVal + addVal) % 10;
    unsigned char f3Val = (char)(indexVal + addVal) % 10;
    float f4Val = indexVal + addVal + 0.01;
    char f5Val[FILED_FIX_SIZE + 1] = {0};
    ret = snprintf_s(f5Val, FILED_FIX_SIZE + 1, FILED_FIX_SIZE, "d%06d", (indexVal + addVal) % 10000);
    if (ret != -1) {
        ret = GMERR_OK;
    }

    ret = queryPropertyAndCompare(subStmt, "F2", GMC_DATATYPE_CHAR, &f2Val);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(subStmt, "F3", GMC_DATATYPE_UCHAR, &f3Val);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(subStmt, "F4", GMC_DATATYPE_FLOAT, &f4Val);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(subStmt, "F5", GMC_DATATYPE_STRING, f5Val);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(subStmt, "F6", GMC_DATATYPE_BYTES, f5Val);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(subStmt, "F7", GMC_DATATYPE_FIXED, f5Val);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestCheckPkValue(GmcStmtT *subStmt, uint32_t index)
{
    int ret = 0;

    ret = queryPropertyAndCompare(subStmt, "F0", GMC_DATATYPE_UINT32, &index);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char f7Val[FILED_FIX_SIZE + 1] = {0};
    bool isNull = true;
    ret = GmcGetVertexPropertyByName(subStmt, "F7", f7Val, FILED_FIX_SIZE, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestSetPkValue(GmcStmtT *stmt, uint32_t index)
{
    int ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &index, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestSetOtherValue(GmcStmtT *stmt, uint32_t indexVal, uint32_t addVal)
{
    int ret = 0;
    char f2Val = (char)(indexVal + addVal) % 10;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_CHAR, &f2Val, sizeof(char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    unsigned char f3Val = (char)(indexVal + addVal) % 10;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UCHAR, &f3Val, sizeof(unsigned char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    float f4Val = indexVal + addVal + 0.01;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_FLOAT, &f4Val, sizeof(float));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char f5Val[FILED_FIX_SIZE + 1] = {0};
    ret = snprintf_s(f5Val, FILED_FIX_SIZE + 1, FILED_FIX_SIZE, "d%06d", (indexVal + addVal) % 10000);
    if (ret != -1) {
        ret = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_STRING, f5Val, strlen(f5Val));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_BYTES, f5Val, strlen(f5Val));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_FIXED, f5Val, FILED_FIX_SIZE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

int TestCheckAffectRows(GmcStmtT *stmt, int32_t expect)
{
    int32_t affect;
    int ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affect, sizeof(affect));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

// 单写
int TestInsertTable(GmcStmtT *stmt, const char *labelName, uint32_t indexVal, uint32_t addVal)
{
    int ret = 0;
    uint32_t index = indexVal;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSetPkValue(stmt, index);
    TestSetOtherValue(stmt, index, addVal);
    ret = GmcExecute(stmt);
    if (ret != GMERR_OK) {
        return ret;
    } else {
        ret = TestCheckAffectRows(stmt, 1);
        return ret;
    }
}

// 批写
void TestBatchInsertThreeTable(GmcConnT *conn, GmcStmtT *stmt, uint32_t index, uint32_t *costTime,
    const char *labelName, const char *labelName2, const char *labelName3)
{
    uint32_t ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batch_ret;
    GmcBatchOptionT batch_option;
    uint32_t suc_num = 0, total_num = 0;
    ret = GmcBatchOptionInit(&batch_option);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batch_option, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batch_option, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batch_option, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSetPkValue(stmt, index);
    TestSetOtherValue(stmt, index, 0);
    ret = GmcBatchAddDML(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, labelName2, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSetPkValue(stmt, index);
    TestSetOtherValue(stmt, index, 0);
    ret = GmcBatchAddDML(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, labelName3, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSetPkValue(stmt, index);
    TestSetOtherValue(stmt, index, 0);
    ret = GmcBatchAddDML(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t startTime = DbGetNsec();
    ret = GmcBatchExecute(batch, &batch_ret);
    uint64_t finishTime = DbGetNsec();
    *costTime = (uint32_t)((finishTime - startTime) / NSECONDS_IN_USECOND);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchDeparseRet(&batch_ret, &total_num, &suc_num);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(3, total_num);
    AW_MACRO_EXPECT_EQ_INT(3, suc_num);
    GmcBatchDestroy(batch);
}

// 更新
int TestUpdateTable(GmcStmtT *stmt, const char *labelName, uint32_t indexVal, uint32_t addVal)
{
    int ret = 0;
    uint32_t f0_u = indexVal;

    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f0_u, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, g_lablePk);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSetOtherValue(stmt, indexVal, addVal);
    ret = GmcExecute(stmt);
    return ret;
}

// 更新一条需要的时间
int TestUpdateTableWithCostTime(
    GmcStmtT *stmt, const char *labelName, uint32_t indexVal, uint32_t addVal, uint32_t *costTime)
{
    int ret = 0;
    uint32_t f0_u = indexVal;

    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f0_u, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, g_lablePk);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSetOtherValue(stmt, indexVal, addVal);
    uint64_t startTime = DbGetNsec();
    ret = GmcExecute(stmt);
    uint64_t finishTime = DbGetNsec();
    *costTime = (uint32_t)((finishTime - startTime) / NSECONDS_IN_USECOND);
    return ret;
}

// replace
int TestReplaceTable(GmcStmtT *stmt, const char *labelName, uint32_t indexVal, uint32_t addVal)
{
    int ret = 0;
    uint32_t f0_u = indexVal;

    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f0_u, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, g_lablePk);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSetPkValue(stmt, indexVal);
    TestSetOtherValue(stmt, indexVal, addVal);
    ret = GmcExecute(stmt);

    return ret;
}

// 单删
int TestDeleteTable(GmcStmtT *stmt, const char *labelName, uint32_t indexVal, uint32_t addVal)
{
    int ret = 0;
    uint32_t f0_u = indexVal;

    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f0_u, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删除
    ret = GmcSetIndexKeyName(stmt, g_lablePk);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);

    return ret;
}

// 通过主键删除连续的顶点
int TestDeleteVertexByPk(GmcStmtT *stmt, const char *labelName, uint32_t startValue, uint32_t vertexCount)
{
    int ret = 0;
    for (uint32_t i = 0; i < vertexCount; i++) {
        uint32_t pkVal = startValue + i;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkVal, sizeof(pkVal));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "record index: %d.", i);
            break;
        }
    }
    return ret;
}

// 通过hashCluster删除连续的顶点
int TestDeleteVertexByPk(
    GmcStmtT *stmt, const char *labelName, uint32_t startValue, uint32_t vertexCount, uint32_t addVal)
{
    int ret = 0;
    for (uint32_t i = 0; i < vertexCount; i++) {
        char f2Val = (char)(i + addVal) % 10;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_CHAR, &f2Val, sizeof(char));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "hckey");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "record index: %c.", f2Val);
            break;
        }
    }
    return ret;
}

void TestSetOtherValueTabel2(GmcStmtT *stmt, uint32_t indexVal, uint32_t addVal, bool isSetAllfield = true)
{
    int ret = 0;
    char stringVal[65536] = {0};
    ret = snprintf_s(stringVal, 65536, 65535, "d%065534d", indexVal + addVal);
    if (ret != -1) {
        ret = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_STRING, stringVal, 65535);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (isSetAllfield) {
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_STRING, stringVal, 65535);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_STRING, stringVal, 65535);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_STRING, stringVal, 65535);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_STRING, stringVal, 65535);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_STRING, stringVal, 65535);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_STRING, stringVal, 65535);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_STRING, stringVal, 65535);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_STRING, stringVal, 65535);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_STRING, stringVal, 65535);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_STRING, stringVal, 65535);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_STRING, stringVal, 65535);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_STRING, stringVal, 65535);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, stringVal, 65535);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_STRING, stringVal, 65535);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char f16Val[32769] = {0};
        ret = snprintf_s(f16Val, 32769, 32768, "d%032767d", indexVal + addVal);
        if (ret != -1) {
            ret = GMERR_OK;
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_STRING, f16Val, 32768);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

// 单写表2
int TestInsertTable2(
    GmcStmtT *stmt, const char *labelName, uint32_t indexVal, uint32_t addVal, bool isSetAllfield = true)
{
    int ret = 0;
    uint32_t index = indexVal;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSetPkValue(stmt, index);
    TestSetOtherValueTabel2(stmt, index, addVal, isSetAllfield);
    ret = GmcExecute(stmt);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_INFO, "record index: %d.", index);
        return ret;
    } else {
        ret = TestCheckAffectRows(stmt, 1);
        return ret;
    }
}

// 更新表2
int TestUpdateTable2(
    GmcStmtT *stmt, const char *labelName, uint32_t indexVal, uint32_t addVal, bool isSetAllfield = true)
{
    int ret = 0;
    uint32_t f0_u = indexVal;

    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f0_u, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, g_lable2Pk);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSetOtherValueTabel2(stmt, indexVal, addVal, isSetAllfield);
    ret = GmcExecute(stmt);
    return ret;
}

// 更新表2需要的时间
int TestUpdateTable2WithCostTime(GmcStmtT *stmt, const char *labelName, uint32_t indexVal, uint32_t addVal,
    uint32_t *costTime, bool isSetAllfield = true)
{
    int ret = 0;
    uint32_t f0_u = indexVal;

    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f0_u, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, g_lable2Pk);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSetOtherValueTabel2(stmt, indexVal, addVal, isSetAllfield);
    uint64_t startTime = DbGetNsec();
    ret = GmcExecute(stmt);
    uint64_t finishTime = DbGetNsec();
    *costTime = (uint32_t)((finishTime - startTime) / NSECONDS_IN_USECOND);
    return ret;
}

// 通过主键删除连续的顶点
int TestDeleteTable2ByPk(GmcStmtT *stmt, const char *labelName, uint32_t startValue, uint32_t vertexCount)
{
    int ret = 0;
    for (uint32_t i = 0; i < vertexCount; i++) {
        uint32_t pkVal = startValue + i;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkVal, sizeof(pkVal));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, g_lable2Pk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "record index: %d.", i);
            break;
        }
    }
    return ret;
}

int TestSetKv(GmcStmtT *stmt, const char *kvName, uint32_t index, int32_t addVal = 0)
{
    // 写数
    int ret = GmcKvPrepareStmtByLabelName(stmt, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t value = 0;
    char key[MAX_NAME_LENGTH];
    char getValue[128] = {0};
    uint32_t valueLen = 4;
    uint32_t usrKeyIndex = 0;
    uint32_t i = index;
    (void)sprintf_s(key, sizeof(key), "SN057KvKey%d", i);
    value = i + addVal;
    ret = GmcKvSet(stmt, key, strlen(key), &value, sizeof(int32_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "set kv  ret is %d, kv key is %d.", ret, i);
    }
    return ret;
}

// 更新一条需要的时间
int TestSetKvWithCostTime(GmcStmtT *stmt, const char *kvName, uint32_t indexVal, uint32_t addVal, uint32_t *costTime)
{
    // 写数
    int ret = GmcKvPrepareStmtByLabelName(stmt, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t value = 0;
    char key[MAX_NAME_LENGTH];
    char getValue[128] = {0};
    uint32_t valueLen = 4;
    uint32_t usrKeyIndex = 0;
    uint32_t i = indexVal;
    (void)sprintf_s(key, sizeof(key), "SN057KvKey%d", i);
    value = i + addVal;
    uint64_t startTime = DbGetNsec();
    ret = GmcKvSet(stmt, key, strlen(key), &value, sizeof(int32_t));
    uint64_t finishTime = DbGetNsec();
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "set kv  ret is %d, kv key is %d.", ret, i);
    }
    *costTime = (uint32_t)((finishTime - startTime) / NSECONDS_IN_USECOND);
    return ret;
}

bool g_isKvCbBlock = false;
void KvOldSnCallBack(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    while (g_isKvCbBlock) {
        sleep(1);
    };
    int ret, i, index;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;
    SnUserDataT *user_data = (SnUserDataT *)userData;
    void *tableLabel = 0;

    bool eof = false;
    while (!eof) {
        if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
            user_data->scanEofNum++;
            break;
        }

        ret = GmcFetch(subStmt, &eof);  // fetch 1 次
        if (ret != GMERR_OK || eof == true) {
            break;
        }

        for (i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(strlen(labelName), labelNameLen);
            switch (info->eventType) {
                case GMC_SUB_EVENT_KV_SET: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    TEST_EXPECT_INT32(GMERR_NO_DATA, ret);
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    TEST_EXPECT_INT32(GMERR_NO_DATA, ret);
                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_INITIAL_LOAD: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    break;
                }
                default: {
                    printf("default: invalid eventType\r\n");
                    break;
                }
            }
        }

        user_data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            default: {
                AW_FUN_Log(
                    LOG_INFO, "default: [%s] [%d] invalid eventType %d.\n", __FUNCTION__, __LINE__, info->eventType);
                break;
            }
        }
    }
}

#if defined FEATURE_DATALOG
// 加载so文件
int CompileAndLoad(char *fileName)
{
    // 加载.so
    TestUninstallDatalog(fileName, NULL, false);
    char command[MAX_CMD_SIZE] = {0};
    char outputDir[FILE_PATH] = "datalogFile";
    (void)snprintf(command, FILE_PATH, "./%s/%s.so", outputDir, fileName);
    return TestLoadDatalog(command);
}

// 单写
int TestInsertOut1(GmcStmtT *stmt, const char *labelName, uint32_t indexVal, uint32_t addVal)
{
    int ret = 0;
    int32_t index = indexVal;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // insert a
    int32_t a = (int32_t)index;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &a, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // insert b
    int32_t b = (int32_t)(index + addVal);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &b, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // insert dtlReservedCount
    int32_t dtlReservedCount = 1;
    ret =
        GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &dtlReservedCount, sizeof(dtlReservedCount));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    if (ret != GMERR_OK) {
        return ret;
    } else {
        ret = TestCheckAffectRows(stmt, 1);
        return ret;
    }
}

// 更新一条需要的时间
int TestUpdateInp1WithCostTime(
    GmcStmtT *stmt, const char *labelName, uint32_t indexVal, uint32_t addVal, uint32_t *costTime)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t a = (int32_t)indexVal;
    int32_t x = 0;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &x, sizeof(int32_t));
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &a, sizeof(int32_t));
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &a, sizeof(int32_t));
    ret = GmcSetIndexKeyName(stmt, "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // update b
    int32_t b = (int32_t)(indexVal + addVal);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &b, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t startTime = DbGetNsec();
    ret = GmcExecute(stmt);
    uint64_t finishTime = DbGetNsec();
    *costTime = (uint32_t)((finishTime - startTime) / NSECONDS_IN_USECOND);
    return ret;
}

int TestUpdateOut1WithCostTime(
    GmcStmtT *stmt, const char *labelName, uint32_t indexVal, uint32_t addVal, uint32_t *costTime)
{
    int ret = 0;
    int32_t a = (int32_t)indexVal;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &a, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "out1_PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t b = (int32_t)indexVal + addVal;
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &b, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t startTime = DbGetNsec();
    ret = GmcExecute(stmt);
    uint64_t finishTime = DbGetNsec();
    *costTime = (uint32_t)((finishTime - startTime) / NSECONDS_IN_USECOND);
    return ret;
}
#endif

#endif
