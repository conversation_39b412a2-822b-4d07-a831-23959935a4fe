#include "SubFlowControlCommon.h"
/*默认流控配置指的是如下配置：1）server config中clientServerFlowControl为0;1;0;1 ;
2)  flowControlSleepTime设置为（um），100,200,1000；
3）overloadThreshold起控阀门为；“cpu:70,80,80,85,85,90;dynamicMemory:70,80,80,85,85,90;shareMemory:70,80,80,85,85,90;subscribeQueue:70,80,80,85,85,90”*/

class SubFlowControl : public testing::Test {
public:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/stop.sh -f");
        system(
            "sh $TEST_HOME/tools/modifyCfg.sh \"clientServerFlowControl=0;0;0;1\" "
            "\"overloadThreshold=cpu:70,80,80,85,85,90;dynamicMemory:70,80,80,85,85,90;shareMemory:70,80,80,85,85,90;"
            "subscribeQueue:70,80,80,85,85,90\" ");
        system("${TEST_HOME}/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_controlSleepLevel = 0;
        g_flowCnt = 0;
        g_isCb2Block = false;
        g_isCb3Block = false;
        g_isKvCbBlock = false;
        ret = create_epoll_thread();
        ASSERT_EQ(GMERR_OK, ret);
    }

    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testEnvClean();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_controlSleepLevel = 0;
        g_flowCnt = 0;
        g_isCb2Block = false;
        g_isCb3Block = false;
        g_isKvCbBlock = false;
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        system("sh $TEST_HOME/tools/stop.sh -f");
    }

    virtual void SetUp()
    {
        int ret = testGmcConnect(&g_connSync, &g_stmtSync);
        ASSERT_EQ(GMERR_OK, ret);
        AW_CHECK_LOG_BEGIN();
    }

    virtual void TearDown()
    {
        AW_CHECK_LOG_END();
        int ret = testGmcDisconnect(g_connSync, g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
};

// 001 gmjson中，sub_flow_control配置为【800000，900000,  1000000】  ---  忽略掉，不生效
TEST_F(SubFlowControl, SN_058_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *schemaPath = "schemaFile/SubFlowControl.gmjson";
    int ret = testGmcCreateVertexLabel(g_stmtSync, schemaPath, g_labelName, 0, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *viewLabelInfo = "V\\$CATA_VERTEX_LABEL_INFO ";
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=SN058Label");
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(
        cmd, MAX_CMD_SIZE, "gmsysview -s %s -q %s -f VERTEX_LABEL_NAME=SN058Label", g_connServer, viewLabelInfo);
    ret = executeCommand(cmd, "sub_flow_control", "0", "0", "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建老订阅
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subConnSn058Label";
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SnUserDataT *userData;
    ret = testSnMallocUserData(&userData, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName = "subSn058Label";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subSn058LabelJson;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, OldSnCallBack, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_controlSleepLevel = 4, g_isBlock = true;  // 阻塞回调

    // 写入数据，写到队列满的时候，触发流控
    uint32_t indexCount = 0;
    GmcDbFlowCtrlLevelE flowCtrlLevel = GMC_DB_FLOW_CTRL_LEVEL_0;
    while (indexCount < MAX_SN_RING_SIZE) {
        ret = TestInsertTable(g_stmtSync, g_labelName, indexCount, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexCount++;

        ret = GmcGetConnFlowCtrlLevel(g_connSync, &flowCtrlLevel);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (flowCtrlLevel == GMC_DB_FLOW_CTRL_LEVEL_1) {
            AW_FUN_Log(LOG_INFO, "insert record num is: %d, client flowCtrlLevel %d.", indexCount, flowCtrlLevel);
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel, GMC_DB_FLOW_CTRL_LEVEL_1);

    uint32_t timeCount = 0;
    ret = TestUpdateTableWithCostTime(g_stmtSync, g_labelName, 0, 100, &timeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount is: %d.", timeCount);

    g_isBlock = false;  // 放开阻塞
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INSERT, indexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除数据
    g_controlSleepLevel = 0;
    ret = TestDeleteVertexByPk(g_stmtSync, g_labelName, 0, indexCount);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_DELETE, indexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);

    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002 gmjson/config中，sub_flow_control配置为【800000，900000,  1000000】
TEST_F(SubFlowControl, SN_058_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *schemaPath = "schemaFile/ConfigSubFlowControl.gmjson";
    int ret = testGmcCreateVertexLabel(g_stmtSync, schemaPath, g_labelName, 0, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *viewLabelInfo = "V\\$CATA_VERTEX_LABEL_INFO ";
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=SN058Label");
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(
        cmd, MAX_CMD_SIZE, "gmsysview -s %s -q %s -f VERTEX_LABEL_NAME=SN058Label", g_connServer, viewLabelInfo);
    AW_FUN_Log(LOG_DEBUG, "cmd is: %s.", cmd);
    ret = executeCommand(cmd, "sub_flow_control", "800000", "900000", "1000000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建老订阅
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subConnSn058Label";
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SnUserDataT *userData;
    ret = testSnMallocUserData(&userData, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName = "subSn058Label";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subSn058LabelJson;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, OldSnCallBack, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_controlSleepLevel = 4, g_isBlock = true;  // 阻塞回调

    // 写入数据，写到队列满的时候，触发流控
    uint32_t indexCount = 0;
    GmcDbFlowCtrlLevelE flowCtrlLevel = GMC_DB_FLOW_CTRL_LEVEL_0;
    while (indexCount < MAX_SN_RING_SIZE) {
        ret = TestInsertTable(g_stmtSync, g_labelName, indexCount, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexCount++;

        ret = GmcGetConnFlowCtrlLevel(g_connSync, &flowCtrlLevel);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (flowCtrlLevel == GMC_DB_FLOW_CTRL_LEVEL_1) {
            AW_FUN_Log(LOG_INFO, "insert record num is: %d, client flowCtrlLevel %d.", indexCount, flowCtrlLevel);
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel, GMC_DB_FLOW_CTRL_LEVEL_1);

    uint32_t timeCount = 0;
    ret = TestUpdateTableWithCostTime(g_stmtSync, g_labelName, 0, 100, &timeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount is: %d.", timeCount);
    EXPECT_GT(timeCount, 800000);

    // 新建一个连接，验证流控是否起效
    GmcConnT *connSync = NULL;
    GmcStmtT *stmtSync = NULL;
    ret = testGmcConnect(&connSync, &stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDbFlowCtrlLevelE flowCtrlLevel2 = GMC_DB_FLOW_CTRL_LEVEL_0;
    ret = GmcGetConnFlowCtrlLevel(connSync, &flowCtrlLevel2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel2, GMC_DB_FLOW_CTRL_LEVEL_0);

    // 更新一条，获取流控等级
    uint32_t timeCount2 = 0;
    ret = TestUpdateTableWithCostTime(stmtSync, g_labelName, 1, 100, &timeCount2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount2 is: %d.", timeCount2);
    sleep(1);
    ret = TestUpdateTableWithCostTime(stmtSync, g_labelName, 2, 100, &timeCount2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount2 is: %d.", timeCount2);
    GmcDbFlowCtrlLevelE flowCtrlLevel3 = GMC_DB_FLOW_CTRL_LEVEL_0;
    ret = GmcGetConnFlowCtrlLevel(connSync, &flowCtrlLevel3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel3, GMC_DB_FLOW_CTRL_LEVEL_1);

    // 获取流控等级后，再次更新
    uint32_t timeCount3 = 0;
    ret = TestUpdateTableWithCostTime(stmtSync, g_labelName, 3, 100, &timeCount3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount3 is: %d.", timeCount3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_GT(timeCount3, 800000);
    ret = testGmcDisconnect(connSync, stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_isBlock = false;  // 放开阻塞
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INSERT, indexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除数据
    g_controlSleepLevel = 0;
    ret = TestDeleteVertexByPk(g_stmtSync, g_labelName, 0, indexCount);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_DELETE, indexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);

    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003 gmconfig中，sub_flow_control配置为【800000，900000,  1000000】
TEST_F(SubFlowControl, SN_058_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char labelConfig[] = "{\"max_record_count\":1000000, \"sub_flow_control\":[800000,900000,1000000]}";
    int ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *viewLabelInfo = "V\\$CATA_VERTEX_LABEL_INFO ";
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=SN058Label");
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(
        cmd, MAX_CMD_SIZE, "gmsysview -s %s -q %s -f VERTEX_LABEL_NAME=SN058Label", g_connServer, viewLabelInfo);
    ret = executeCommand(cmd, "sub_flow_control", "800000", "900000", "1000000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建老订阅
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subConnSn058Label";
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SnUserDataT *userData;
    ret = testSnMallocUserData(&userData, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName = "subSn058Label";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subSn058LabelJson;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, OldSnCallBack, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_controlSleepLevel = 4, g_isBlock = true;  // 阻塞回调

    // 写入数据，写到队列满的时候，触发流控
    uint32_t indexCount = 0;
    GmcDbFlowCtrlLevelE flowCtrlLevel = GMC_DB_FLOW_CTRL_LEVEL_0;
    while (indexCount < MAX_SN_RING_SIZE) {
        ret = TestInsertTable(g_stmtSync, g_labelName, indexCount, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexCount++;

        ret = GmcGetConnFlowCtrlLevel(g_connSync, &flowCtrlLevel);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (flowCtrlLevel == GMC_DB_FLOW_CTRL_LEVEL_1) {
            AW_FUN_Log(LOG_INFO, "insert record num is: %d, client flowCtrlLevel %d.", indexCount, flowCtrlLevel);
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel, GMC_DB_FLOW_CTRL_LEVEL_1);

    uint32_t timeCount = 0;
    ret = TestUpdateTableWithCostTime(g_stmtSync, g_labelName, 0, 100, &timeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount is: %d.", timeCount);
    EXPECT_GT(timeCount, 800000);

    // 新建一个连接，验证流控是否起效
    GmcConnT *connSync = NULL;
    GmcStmtT *stmtSync = NULL;
    ret = testGmcConnect(&connSync, &stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDbFlowCtrlLevelE flowCtrlLevel2 = GMC_DB_FLOW_CTRL_LEVEL_0;
    ret = GmcGetConnFlowCtrlLevel(connSync, &flowCtrlLevel2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel2, GMC_DB_FLOW_CTRL_LEVEL_0);

    // 更新一条，获取流控等级
    uint32_t timeCount2 = 0;
    ret = TestUpdateTableWithCostTime(stmtSync, g_labelName, 1, 100, &timeCount2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount2 is: %d.", timeCount2);
    sleep(1);
    ret = TestUpdateTableWithCostTime(stmtSync, g_labelName, 2, 100, &timeCount2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount2 is: %d.", timeCount2);
    GmcDbFlowCtrlLevelE flowCtrlLevel3 = GMC_DB_FLOW_CTRL_LEVEL_0;
    ret = GmcGetConnFlowCtrlLevel(connSync, &flowCtrlLevel3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel3, GMC_DB_FLOW_CTRL_LEVEL_1);

    // 获取流控等级后，再次更新
    uint32_t timeCount3 = 0;
    ret = TestUpdateTableWithCostTime(stmtSync, g_labelName, 3, 100, &timeCount3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount3 is: %d.", timeCount3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_GT(timeCount3, 800000);
    ret = testGmcDisconnect(connSync, stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_isBlock = false;  // 放开阻塞
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INSERT, indexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除数据
    g_controlSleepLevel = 0;
    ret = TestDeleteVertexByPk(g_stmtSync, g_labelName, 0, indexCount);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_DELETE, indexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);

    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004 sub_flow_control配置优先级，gmjson/config中为【0,0,0】，gmconfig中为【800000，900000, 1000000】
TEST_F(SubFlowControl, SN_058_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *schemaPath = "schemaFile/ConfigSubFlowControlFalse.gmjson";
    char labelConfig[] = "{\"max_record_count\":1000000, \"sub_flow_control\":[800000,900000,1000000]}";
    int ret = testGmcCreateVertexLabel(g_stmtSync, schemaPath, g_labelName, 0, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *viewLabelInfo = "V\\$CATA_VERTEX_LABEL_INFO ";
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=SN058Label");
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(
        cmd, MAX_CMD_SIZE, "gmsysview -s %s -q %s -f VERTEX_LABEL_NAME=SN058Label", g_connServer, viewLabelInfo);
    ret = executeCommand(cmd, "sub_flow_control", "800000", "900000", "1000000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建老订阅
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subConnSn058Label";
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SnUserDataT *userData;
    ret = testSnMallocUserData(&userData, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName = "subSn058Label";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subSn058LabelJson;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, OldSnCallBack, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_controlSleepLevel = 4, g_isBlock = true;  // 阻塞回调

    // 写入数据，写到队列满的时候，触发流控
    uint32_t indexCount = 0;
    GmcDbFlowCtrlLevelE flowCtrlLevel = GMC_DB_FLOW_CTRL_LEVEL_0;
    while (indexCount < MAX_SN_RING_SIZE) {
        ret = TestInsertTable(g_stmtSync, g_labelName, indexCount, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexCount++;

        ret = GmcGetConnFlowCtrlLevel(g_connSync, &flowCtrlLevel);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (flowCtrlLevel == GMC_DB_FLOW_CTRL_LEVEL_1) {
            AW_FUN_Log(LOG_INFO, "insert record num is: %d, client flowCtrlLevel %d.", indexCount, flowCtrlLevel);
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel, GMC_DB_FLOW_CTRL_LEVEL_1);

    uint32_t timeCount = 0;
    ret = TestUpdateTableWithCostTime(g_stmtSync, g_labelName, 0, 100, &timeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount is: %d.", timeCount);
    EXPECT_GT(timeCount, 800000);

    // 新建一个连接，验证流控是否起效
    GmcConnT *connSync = NULL;
    GmcStmtT *stmtSync = NULL;
    ret = testGmcConnect(&connSync, &stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDbFlowCtrlLevelE flowCtrlLevel2 = GMC_DB_FLOW_CTRL_LEVEL_0;
    ret = GmcGetConnFlowCtrlLevel(connSync, &flowCtrlLevel2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel2, GMC_DB_FLOW_CTRL_LEVEL_0);

    // 更新一条，获取流控等级
    uint32_t timeCount2 = 0;
    ret = TestUpdateTableWithCostTime(stmtSync, g_labelName, 1, 100, &timeCount2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount2 is: %d.", timeCount2);
    sleep(1);
    ret = TestUpdateTableWithCostTime(stmtSync, g_labelName, 2, 100, &timeCount2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount2 is: %d.", timeCount2);
    GmcDbFlowCtrlLevelE flowCtrlLevel3 = GMC_DB_FLOW_CTRL_LEVEL_0;
    ret = GmcGetConnFlowCtrlLevel(connSync, &flowCtrlLevel3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel3, GMC_DB_FLOW_CTRL_LEVEL_1);

    // 获取流控等级后，再次更新
    uint32_t timeCount3 = 0;
    ret = TestUpdateTableWithCostTime(stmtSync, g_labelName, 3, 100, &timeCount3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount3 is: %d.", timeCount3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_GT(timeCount3, 800000);
    ret = testGmcDisconnect(connSync, stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_isBlock = false;  // 放开阻塞
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INSERT, indexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除数据
    g_controlSleepLevel = 0;
    ret = TestDeleteVertexByPk(g_stmtSync, g_labelName, 0, indexCount);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_DELETE, indexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);

    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005 sub_flow_control配置优先级，gmjson/config中为【800000，900000,  1000000】，gmconfig中为【0,0,0】
TEST_F(SubFlowControl, SN_058_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *schemaPath = "schemaFile/ConfigSubFlowControl.gmjson";
    char labelConfig[] = "{\"max_record_count\":1000000, \"sub_flow_control\":[0,0,0]}";
    int ret = testGmcCreateVertexLabel(g_stmtSync, schemaPath, g_labelName, 0, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *viewLabelInfo = "V\\$CATA_VERTEX_LABEL_INFO ";
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=SN058Label");
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(
        cmd, MAX_CMD_SIZE, "gmsysview -s %s -q %s -f VERTEX_LABEL_NAME=SN058Label", g_connServer, viewLabelInfo);
    ret = executeCommand(cmd, "sub_flow_control", "0", "0", "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建老订阅
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subConnSn058Label";
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SnUserDataT *userData;
    ret = testSnMallocUserData(&userData, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName = "subSn058Label";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subSn058LabelJson;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, OldSnCallBack, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_controlSleepLevel = 4, g_isBlock = true;  // 阻塞回调

    // 写入数据，写到队列满的时候，触发流控
    uint32_t indexCount = 0;
    GmcDbFlowCtrlLevelE flowCtrlLevel = GMC_DB_FLOW_CTRL_LEVEL_0;
    while (indexCount < MAX_SN_RING_SIZE) {
        ret = TestInsertTable(g_stmtSync, g_labelName, indexCount, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexCount++;

        ret = GmcGetConnFlowCtrlLevel(g_connSync, &flowCtrlLevel);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (flowCtrlLevel == GMC_DB_FLOW_CTRL_LEVEL_1) {
            AW_FUN_Log(LOG_INFO, "insert record num is: %d, client flowCtrlLevel %d.", indexCount, flowCtrlLevel);
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel, GMC_DB_FLOW_CTRL_LEVEL_1);

    uint32_t timeCount = 0;
    ret = TestUpdateTableWithCostTime(g_stmtSync, g_labelName, 0, 100, &timeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount is: %d.", timeCount);
    EXPECT_LT(timeCount, 800000);
    EXPECT_GT(timeCount, 200);

    // 新建一个连接，验证流控是否起效
    GmcConnT *connSync = NULL;
    GmcStmtT *stmtSync = NULL;
    ret = testGmcConnect(&connSync, &stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDbFlowCtrlLevelE flowCtrlLevel2 = GMC_DB_FLOW_CTRL_LEVEL_0;
    ret = GmcGetConnFlowCtrlLevel(connSync, &flowCtrlLevel2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel2, GMC_DB_FLOW_CTRL_LEVEL_0);

    // 更新一条，获取流控等级
    uint32_t timeCount2 = 0;
    ret = TestUpdateTableWithCostTime(stmtSync, g_labelName, 1, 100, &timeCount2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount2 is: %d.", timeCount2);
    sleep(1);
    ret = TestUpdateTableWithCostTime(stmtSync, g_labelName, 2, 100, &timeCount2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount2 is: %d.", timeCount2);
    GmcDbFlowCtrlLevelE flowCtrlLevel3 = GMC_DB_FLOW_CTRL_LEVEL_0;
    ret = GmcGetConnFlowCtrlLevel(connSync, &flowCtrlLevel3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel3, GMC_DB_FLOW_CTRL_LEVEL_1);

    // 获取流控等级后，再次更新
    uint32_t timeCount3 = 0;
    ret = TestUpdateTableWithCostTime(stmtSync, g_labelName, 3, 100, &timeCount3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount3 is: %d.", timeCount3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_LT(timeCount3, 800000);
    ret = testGmcDisconnect(connSync, stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_isBlock = false;  // 放开阻塞
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INSERT, indexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除数据
    g_controlSleepLevel = 0;
    ret = TestDeleteVertexByPk(g_stmtSync, g_labelName, 0, indexCount);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_DELETE, indexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);

    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006 sub_flow_control默认值
TEST_F(SubFlowControl, SN_058_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *viewLabelInfo = "V\\$CATA_VERTEX_LABEL_INFO ";
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=SN058Label");
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(
        cmd, MAX_CMD_SIZE, "gmsysview -s %s -q %s -f VERTEX_LABEL_NAME=SN058Label", g_connServer, viewLabelInfo);
    ret = executeCommand(cmd, "sub_flow_control", "0", "0", "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建老订阅
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subConnSn058Label";
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SnUserDataT *userData;
    ret = testSnMallocUserData(&userData, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName = "subSn058Label";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subSn058LabelJson;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, OldSnCallBack, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_controlSleepLevel = 4, g_isBlock = true;  // 阻塞回调

    // 写入数据，写到队列满的时候，触发流控
    uint32_t indexCount = 0;
    GmcDbFlowCtrlLevelE flowCtrlLevel = GMC_DB_FLOW_CTRL_LEVEL_0;
    while (indexCount < MAX_SN_RING_SIZE) {
        ret = TestInsertTable(g_stmtSync, g_labelName, indexCount, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexCount++;

        ret = GmcGetConnFlowCtrlLevel(g_connSync, &flowCtrlLevel);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (flowCtrlLevel == GMC_DB_FLOW_CTRL_LEVEL_1) {
            AW_FUN_Log(LOG_INFO, "insert record num is: %d, client flowCtrlLevel %d.", indexCount, flowCtrlLevel);
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel, GMC_DB_FLOW_CTRL_LEVEL_1);

    uint32_t timeCount = 0;
    ret = TestUpdateTableWithCostTime(g_stmtSync, g_labelName, 0, 100, &timeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount is: %d.", timeCount);
    EXPECT_LT(timeCount, 800000);
    EXPECT_GT(timeCount, 200);

    // 新建一个连接，验证流控是否起效
    GmcConnT *connSync = NULL;
    GmcStmtT *stmtSync = NULL;
    ret = testGmcConnect(&connSync, &stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDbFlowCtrlLevelE flowCtrlLevel2 = GMC_DB_FLOW_CTRL_LEVEL_0;
    ret = GmcGetConnFlowCtrlLevel(connSync, &flowCtrlLevel2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel2, GMC_DB_FLOW_CTRL_LEVEL_0);

    // 更新一条，获取流控等级
    uint32_t timeCount2 = 0;
    ret = TestUpdateTableWithCostTime(stmtSync, g_labelName, 1, 100, &timeCount2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount2 is: %d.", timeCount2);
    sleep(1);
    ret = TestUpdateTableWithCostTime(stmtSync, g_labelName, 2, 100, &timeCount2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount2 is: %d.", timeCount2);
    GmcDbFlowCtrlLevelE flowCtrlLevel3 = GMC_DB_FLOW_CTRL_LEVEL_0;
    ret = GmcGetConnFlowCtrlLevel(connSync, &flowCtrlLevel3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel3, GMC_DB_FLOW_CTRL_LEVEL_1);

    // 获取流控等级后，再次更新
    uint32_t timeCount3 = 0;
    ret = TestUpdateTableWithCostTime(stmtSync, g_labelName, 3, 100, &timeCount3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount3 is: %d.", timeCount3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_LT(timeCount3, 800000);
    EXPECT_GT(timeCount3, 200);
    ret = testGmcDisconnect(connSync, stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_isBlock = false;  // 放开阻塞
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INSERT, indexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除数据
    g_controlSleepLevel = 0;
    ret = TestDeleteVertexByPk(g_stmtSync, g_labelName, 0, indexCount);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_DELETE, indexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);

    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007 sub_flow_control配置为异常值，取值范围之外的值，不配置值，其他值等
TEST_F(SubFlowControl, SN_058_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_DATATYPE_MISMATCH);
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    AddWhiteList(GMERR_INVALID_JSON_CONTENT);
    char labelConfig[] = "{\"max_record_count\":1000000, \"sub_flow_control\":\"1\"}";
    int ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATATYPE_MISMATCH, ret);

    char labelConfig1[] = "{\"max_record_count\":1000000, \"sub_flow_control\":[0, 8000000, 1000001]}";
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, labelConfig1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    char labelConfig4[] = "{\"max_record_count\":1000000, \"sub_flow_control\":[-1, 8000000, 1000000]}";
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, labelConfig1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    char labelConfig2[] = "{\"max_record_count\":1000000, \"sub_flow_control\":}";
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, labelConfig2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_JSON_CONTENT, ret);

    char labelConfig3[] = "{\"max_record_count\":1000000, \"sub_flow_control\":@@@@}";
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, labelConfig3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_JSON_CONTENT, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008 导入表时，指定sub_flow_control为【800000，900000,  1000000】
TEST_F(SubFlowControl, SN_058_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导表
    char cmd[MAX_CMD_SIZE] = {0};
    snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c vschema -f %s -s %s -ns %s", g_toolPath, g_schemaPath, g_connServer,
        g_testNameSpace);
    int ret = executeCommand(cmd, "import_vschema", "Import file from", "SN058Label.gmjson", "successfully");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *viewLabelInfo = "V\\$CATA_VERTEX_LABEL_INFO ";
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=SN058Label");
    (void)snprintf(
        cmd, MAX_CMD_SIZE, "gmsysview -s %s -q %s -f VERTEX_LABEL_NAME=SN058Label", g_connServer, viewLabelInfo);
    ret = executeCommand(cmd, "sub_flow_control", "0", "0", "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建老订阅
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subConnSn058Label";
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SnUserDataT *userData;
    ret = testSnMallocUserData(&userData, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName = "subSn058Label";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subSn058LabelJson;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, OldSnCallBack, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_controlSleepLevel = 4, g_isBlock = true;  // 阻塞回调

    // 写入数据，写到队列满的时候，触发流控
    uint32_t indexCount = 0;
    GmcDbFlowCtrlLevelE flowCtrlLevel = GMC_DB_FLOW_CTRL_LEVEL_0;
    while (indexCount < MAX_SN_RING_SIZE) {
        ret = TestInsertTable(g_stmtSync, g_labelName, indexCount, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexCount++;

        ret = GmcGetConnFlowCtrlLevel(g_connSync, &flowCtrlLevel);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (flowCtrlLevel == GMC_DB_FLOW_CTRL_LEVEL_1) {
            AW_FUN_Log(LOG_INFO, "insert record num is: %d, client flowCtrlLevel %d.", indexCount, flowCtrlLevel);
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel, GMC_DB_FLOW_CTRL_LEVEL_1);

    uint32_t timeCount = 0;
    ret = TestUpdateTableWithCostTime(g_stmtSync, g_labelName, 0, 100, &timeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount is: %d.", timeCount);
    EXPECT_GT(timeCount, 800000);

    // 新建一个连接，验证流控是否起效
    GmcConnT *connSync = NULL;
    GmcStmtT *stmtSync = NULL;
    ret = testGmcConnect(&connSync, &stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDbFlowCtrlLevelE flowCtrlLevel2 = GMC_DB_FLOW_CTRL_LEVEL_0;
    ret = GmcGetConnFlowCtrlLevel(connSync, &flowCtrlLevel2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel2, GMC_DB_FLOW_CTRL_LEVEL_0);

    // 更新一条，获取流控等级
    uint32_t timeCount2 = 0;
    ret = TestUpdateTableWithCostTime(stmtSync, g_labelName, 1, 100, &timeCount2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount2 is: %d.", timeCount2);
    sleep(1);
    ret = TestUpdateTableWithCostTime(stmtSync, g_labelName, 2, 100, &timeCount2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount2 is: %d.", timeCount2);
    GmcDbFlowCtrlLevelE flowCtrlLevel3 = GMC_DB_FLOW_CTRL_LEVEL_0;
    ret = GmcGetConnFlowCtrlLevel(connSync, &flowCtrlLevel3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel3, GMC_DB_FLOW_CTRL_LEVEL_1);

    // 获取流控等级后，再次更新
    uint32_t timeCount3 = 0;
    ret = TestUpdateTableWithCostTime(stmtSync, g_labelName, 3, 100, &timeCount3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount3 is: %d.", timeCount3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_GT(timeCount3, 800000);
    ret = testGmcDisconnect(connSync, stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_isBlock = false;  // 放开阻塞
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INSERT, indexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除数据
    g_controlSleepLevel = 0;
    ret = TestDeleteVertexByPk(g_stmtSync, g_labelName, 0, indexCount);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_DELETE, indexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);

    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    snprintf(cmd, MAX_CMD_SIZE, "%s/gmddl -c drop -t %s -s %s -ns %s", g_toolPath, g_labelName, g_connServer,
        g_testNameSpace);
    ret = executeCommand(cmd, "drop label SN058Label successfully", "successfully dropped table num is 1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 此处订阅通道流控阈值设置为4.是因为initial_load时，推送的数据占满4k的buff才占1个通道中的node槽位，故16k数据占的node槽位比例降低
class SubFlowControl7 : public testing::Test {
public:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/stop.sh -f");
        system(
            "sh $TEST_HOME/tools/modifyCfg.sh \"clientServerFlowControl=0;0;0;1\" "
            "\"overloadThreshold=cpu:70,80,80,85,85,90;dynamicMemory:70,80,80,85,85,90;shareMemory:70,80,80,85,85,90;"
            "subscribeQueue:2,4,80,85,85,90\" ");
        system("${TEST_HOME}/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_controlSleepLevel = 0;
        g_flowCnt = 0;
        g_isCb2Block = false;
        g_isCb3Block = false;
        g_isKvCbBlock = false;
        ret = create_epoll_thread();
        ASSERT_EQ(GMERR_OK, ret);
    }

    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testEnvClean();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_controlSleepLevel = 0;
        g_flowCnt = 0;
        g_isCb2Block = false;
        g_isCb3Block = false;
        g_isKvCbBlock = false;
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        system("sh $TEST_HOME/tools/stop.sh -f");
    }

    virtual void SetUp()
    {
        int ret = testGmcConnect(&g_connSync, &g_stmtSync);
        ASSERT_EQ(GMERR_OK, ret);
        AW_CHECK_LOG_BEGIN();
    }

    virtual void TearDown()
    {
        AW_CHECK_LOG_END();
        int ret = testGmcDisconnect(g_connSync, g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
};

// 009 vetex表指定sub_flow_control为【800000，900000,  1000000】，订阅initial_load
TEST_F(SubFlowControl7, SN_058_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char labelConfig[] = "{\"max_record_count\":1000000, \"sub_flow_control\":[800000,900000,1000000]}";
    int ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=SN058Label");

    // 写入数据
    uint32_t indexCount = 0;
    GmcDbFlowCtrlLevelE flowCtrlLevel = GMC_DB_FLOW_CTRL_LEVEL_0;
    while (indexCount < MAX_SN_RING_SIZE) {
        ret = TestInsertTable(g_stmtSync, g_labelName, indexCount, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexCount++;
    }

    g_controlSleepLevel = 4, g_isBlock = true;  // 阻塞回调
    // 创建老订阅
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subConnSn058Label";
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SnUserDataT *userData;
    ret = testSnMallocUserData(&userData, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName = "subSn058Label";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subSn058Initial;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, OldSnCallBack, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t timeCount = 0;
    ret = TestUpdateTableWithCostTime(g_stmtSync, g_labelName, 0, 100, &timeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount is: %d.", timeCount);

    ret = TestUpdateTableWithCostTime(g_stmtSync, g_labelName, 1, 100, &timeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount is: %d.", timeCount);

    g_isBlock = false;  // 放开阻塞
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INITIAL_LOAD, indexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除数据
    g_controlSleepLevel = 0;
    ret = TestDeleteVertexByPk(g_stmtSync, g_labelName, 0, indexCount);
    ASSERT_EQ(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);

    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010 vetex表指定sub_flow_control为【800000，900000,  1000000】，订阅增量
TEST_F(SubFlowControl, SN_058_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_SUB_PUSH_QUEUE_FULL);
    AddWhiteList(GMERR_INSUFFICIENT_RESOURCES);

    char labelConfig[] = "{\"max_record_count\":1000000, \"sub_flow_control\":[800000,900000,1000000]}";
    int ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建老订阅
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subConnSn058Label";
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SnUserDataT *userData;
    ret = testSnMallocUserData(&userData, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName = "subSn058Label";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subSn058Add;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, OldSnCallBack, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_controlSleepLevel = 4, g_isBlock = true;  // 阻塞回调

    // 写入数据，写到队列size + 1000条
    uint32_t indexCount = 0;
    GmcDbFlowCtrlLevelE flowCtrlLevel = GMC_DB_FLOW_CTRL_LEVEL_0;
    while (indexCount < MAX_SN_RING_SIZE + 1000) {
        ret = TestInsertTable(g_stmtSync, g_labelName, indexCount, 0);
        if (ret == GMERR_SUB_PUSH_QUEUE_FULL) {
            AW_FUN_Log(LOG_INFO, "insert record num is: %d, ret  is  %d.", indexCount, ret);
            break;
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexCount++;

        ret = GmcGetConnFlowCtrlLevel(g_connSync, &flowCtrlLevel);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (flowCtrlLevel == GMC_DB_FLOW_CTRL_LEVEL_1) {
            AW_FUN_Log(LOG_INFO, "insert record num is: %d, client flowCtrlLevel %d.", indexCount, flowCtrlLevel);
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel, GMC_DB_FLOW_CTRL_LEVEL_1);
    system("gmsysview -q V\\$QRY_DML_OPER_STATIS -f LABEL_NAME=SN058Label");

    uint32_t timeCount = 0;
    ret = TestUpdateTableWithCostTime(g_stmtSync, g_labelName, 0, 100, &timeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount is: %d.", timeCount);
    EXPECT_GT(timeCount, 800000);

    g_isBlock = false;  // 放开阻塞
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INSERT, indexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除数据
    g_controlSleepLevel = 0;
    ret = TestDeleteVertexByPk(g_stmtSync, g_labelName, 0, indexCount);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_DELETE, indexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);

    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 011 vetex表指定sub_flow_control为true，触发全量推送事件（GMC_SUB_EVENT_TRIGGER_SCAN）
TEST_F(SubFlowControl7, SN_058_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char labelConfig[] = "{\"max_record_count\":1000000, \"sub_flow_control\":[800000,900000,1000000]}";
    int ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建老订阅
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subConnSn058Label";
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SnUserDataT *userData;
    ret = testSnMallocUserData(&userData, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName = "subSn058Label";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subSn058LabelJson;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, OldSnCallBack, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据，写到队列size + 1000条
    uint32_t indexCount = 0;
    GmcDbFlowCtrlLevelE flowCtrlLevel = GMC_DB_FLOW_CTRL_LEVEL_0;
    while (indexCount < MAX_SN_RING_SIZE + 1000) {
        ret = TestInsertTable(g_stmtSync, g_labelName, indexCount, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexCount++;
    }
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INSERT, MAX_SN_RING_SIZE + 1000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_controlSleepLevel = 4, g_isBlock = true;  // 阻塞回调
    ret = GmcSubTriggerScan(g_stmtSync, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t timeCount = 0;
    ret = TestUpdateTableWithCostTime(g_stmtSync, g_labelName, 0, 100, &timeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount is: %d.", timeCount);
    ret = TestUpdateTableWithCostTime(g_stmtSync, g_labelName, 1, 100, &timeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount is: %d.", timeCount);
    ret = GmcGetConnFlowCtrlLevel(g_connSync, &flowCtrlLevel);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_isBlock = false;  // 放开阻塞
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_TRIGGER_SCAN, MAX_SN_RING_SIZE + 1000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_TRIGGER_SCAN_BEGIN, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_TRIGGER_SCAN_END, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除数据
    g_controlSleepLevel = 0;
    ret = TestDeleteVertexByPk(g_stmtSync, g_labelName, 0, indexCount);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_DELETE, indexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);

    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 012 vetex表指定sub_flow_control为true，订阅共享内存超过阈值触发流控
TEST_F(SubFlowControl, SN_058_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath2, g_label2Name, 0, g_label2Config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建老订阅
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subConnSn058Label2";
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    SnUserDataT *userData;
    ret = testSnMallocUserData(&userData, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName = "subSn058Label2";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subSn058Label2Condition;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, OldSnCallBack, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    g_controlSleepLevel = 4, g_isBlock = true;  // 阻塞回调
    uint32_t indexCount = 0;
    GmcDbFlowCtrlLevelE flowCtrlLevel = GMC_DB_FLOW_CTRL_LEVEL_0;
    while (indexCount < 500) {
        ret = TestInsertTable2(g_stmtSync, g_label2Name, indexCount, 0);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexCount++;
        ret = GmcGetConnFlowCtrlLevel(g_connSync, &flowCtrlLevel);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (flowCtrlLevel == GMC_DB_FLOW_CTRL_LEVEL_1) {
            AW_FUN_Log(LOG_INFO, "insert record num is: %d, client flowCtrlLevel %d.", indexCount, flowCtrlLevel);
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel, GMC_DB_FLOW_CTRL_LEVEL_1);

    uint32_t timeCount = 0;
    ret = TestUpdateTable2WithCostTime(g_stmtSync, g_label2Name, 0, 100, &timeCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount is: %d.", timeCount);
    EXPECT_GT(timeCount, 800000);

    // 动态内存：
    system("gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopDynmemCtx");
    // 共享内存：
    system("gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=RtMsgTopShareMemCtx");

    g_isBlock = false;  // 放开阻塞
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INSERT, indexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除数据
    g_controlSleepLevel = 0;
    ret = TestDeleteTable2ByPk(g_stmtSync, g_label2Name, 0, indexCount);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_DELETE, indexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);

    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_label2Name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 013 vetex表指定sub_flow_control为true，动态内存超过阈值触发流控
TEST_F(SubFlowControl, SN_058_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath2, g_label2Name, 0, g_label2Config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建老订阅
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subConnSn058Label2";
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    SnUserDataT *userData;
    ret = testSnMallocUserData(&userData, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName = "subSn058Label2";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subSn058Label2;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, OldSnCallBack, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    g_controlSleepLevel = 4, g_isBlock = true;  // 阻塞回调
    uint32_t indexCount = 0;
    GmcDbFlowCtrlLevelE flowCtrlLevel = GMC_DB_FLOW_CTRL_LEVEL_0;
    while (indexCount < 500) {
        ret = TestInsertTable2(g_stmtSync, g_label2Name, indexCount, 0);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexCount++;
        ret = GmcGetConnFlowCtrlLevel(g_connSync, &flowCtrlLevel);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (flowCtrlLevel == GMC_DB_FLOW_CTRL_LEVEL_1) {
            AW_FUN_Log(LOG_INFO, "insert record num is: %d, client flowCtrlLevel %d.", indexCount, flowCtrlLevel);
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel, GMC_DB_FLOW_CTRL_LEVEL_1);

    uint32_t timeCount = 0;
    ret = TestUpdateTable2WithCostTime(g_stmtSync, g_label2Name, 0, 100, &timeCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount is: %d.", timeCount);
    EXPECT_GT(timeCount, 800000);

    // 动态内存：
    system("gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopDynmemCtx");
    // 共享内存：
    system("gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=RtMsgTopShareMemCtx");

    g_isBlock = false;  // 放开阻塞
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INSERT, indexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除数据
    g_controlSleepLevel = 0;
    ret = TestDeleteTable2ByPk(g_stmtSync, g_label2Name, 0, indexCount);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_DELETE, indexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);

    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_label2Name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 014 一个连接，一张表，一个订阅通道，3个订阅关系，vetex表指定sub_flow_control为true，订阅1,2,3都阻塞
TEST_F(SubFlowControl, SN_058_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char labelConfig[] = "{\"max_record_count\":1000000, \"sub_flow_control\":[800000,900000,1000000]}";
    int ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建老订阅
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subConnSn058Label";
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SnUserDataT *userData;
    ret = testSnMallocUserData(&userData, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName = "subSn058Label";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subSn058LabelJson;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, OldSnCallBack, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SnUserDataT *userData2;
    ret = testSnMallocUserData(&userData2, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName2 = "sub2Sn058Label";
    subInfo.subsName = subName2;
    subInfo.configJson = g_subSn058LabelJson;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, OldSnCallBack2, userData2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SnUserDataT *userData3;
    ret = testSnMallocUserData(&userData3, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName3 = "sub2Sn058Labe3";
    subInfo.subsName = subName3;
    subInfo.configJson = g_subSn058LabelJson;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, OldSnCallBack3, userData3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 订阅1,2,3都阻塞
    g_controlSleepLevel = 4, g_isBlock = true;
    g_isCb2Block = true;
    g_isCb3Block = true;

    // 写入数据，写到队列满的时候，触发流控
    uint32_t indexCount = 0;
    GmcDbFlowCtrlLevelE flowCtrlLevel = GMC_DB_FLOW_CTRL_LEVEL_0;
    while (indexCount < MAX_SN_RING_SIZE) {
        ret = TestInsertTable(g_stmtSync, g_labelName, indexCount, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexCount++;

        ret = GmcGetConnFlowCtrlLevel(g_connSync, &flowCtrlLevel);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (flowCtrlLevel == GMC_DB_FLOW_CTRL_LEVEL_1) {
            AW_FUN_Log(LOG_INFO, "insert record num is: %d, client flowCtrlLevel %d.", indexCount, flowCtrlLevel);
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel, GMC_DB_FLOW_CTRL_LEVEL_1);

    uint32_t timeCount = 0;
    ret = TestUpdateTableWithCostTime(g_stmtSync, g_labelName, 0, 100, &timeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount is: %d.", timeCount);
    EXPECT_GT(timeCount, 800000);

    // 订阅1,2,3阻塞放开
    g_isBlock = false;
    g_isCb2Block = false;
    g_isCb3Block = false;
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INSERT, indexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData2, GMC_SUB_EVENT_INSERT, indexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData3, GMC_SUB_EVENT_INSERT, indexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, subName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, subName3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);
    testSnFreeUserData(userData2);
    testSnFreeUserData(userData3);

    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 015 一个连接，一张表，3个订阅通道，3个订阅关系，vetex表指定sub_flow_control为true，订阅1阻塞
TEST_F(SubFlowControl, SN_058_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    for (int i = 0; i < 3; i++) {
        ret = create_epoll_thread(&g_epAsync[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }

    char labelConfig[] = "{\"max_record_count\":1000000, \"sub_flow_control\":[800000,900000,1000000]}";
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建老订阅
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subConnSn058Label";
    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[0].userEpollFd;
    connOptions.connName = subConnName;
    ret = TestYangGmcConnect(&connSub, &stmtSub, GMC_CONN_TYPE_SUB, &connOptions);
    SnUserDataT *userData;
    ret = testSnMallocUserData(&userData, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName = "subSn058Label";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subSn058LabelJson;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, OldSnCallBack, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *connSub2 = NULL;
    GmcStmtT *stmtSub2 = NULL;
    const char *subConn2Name = "subConn2Sn058Label";
    YangConnOptionT connOptions2 = {0};
    connOptions2.epollFd = &g_epAsync[1].userEpollFd;
    connOptions2.connName = subConn2Name;
    ret = TestYangGmcConnect(&connSub2, &stmtSub2, GMC_CONN_TYPE_SUB, &connOptions2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SnUserDataT *userData2;
    ret = testSnMallocUserData(&userData2, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName2 = "sub2Sn058Label";
    subInfo.subsName = subName2;
    subInfo.configJson = g_subSn058LabelJson;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub2, OldSnCallBack2, userData2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *connSub3 = NULL;
    GmcStmtT *stmtSub3 = NULL;
    const char *subConn3Name = "subConn3Sn058Label";
    YangConnOptionT connOptions3 = {0};
    connOptions3.epollFd = &g_epAsync[2].userEpollFd;
    connOptions3.connName = subConn3Name;
    ret = TestYangGmcConnect(&connSub3, &stmtSub3, GMC_CONN_TYPE_SUB, &connOptions3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SnUserDataT *userData3;
    ret = testSnMallocUserData(&userData3, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName3 = "sub3Sn058Labe";
    subInfo.subsName = subName3;
    subInfo.configJson = g_subSn058LabelJson;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub3, OldSnCallBack3, userData3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 订阅1都阻塞
    g_controlSleepLevel = 4, g_isBlock = true;

    // 写入数据，写到队列满的时候，触发流控
    uint32_t indexCount = 0;
    GmcDbFlowCtrlLevelE flowCtrlLevel = GMC_DB_FLOW_CTRL_LEVEL_0;
    while (indexCount < MAX_SN_RING_SIZE) {
        ret = TestInsertTable(g_stmtSync, g_labelName, indexCount, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexCount++;

        ret = GmcGetConnFlowCtrlLevel(g_connSync, &flowCtrlLevel);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (flowCtrlLevel == GMC_DB_FLOW_CTRL_LEVEL_1) {
            AW_FUN_Log(LOG_INFO, "insert record num is: %d, client flowCtrlLevel %d.", indexCount, flowCtrlLevel);
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel, GMC_DB_FLOW_CTRL_LEVEL_1);
    ret = testWaitSnRecv(userData2, GMC_SUB_EVENT_INSERT, indexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData3, GMC_SUB_EVENT_INSERT, indexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t timeCount = 0;
    ret = TestUpdateTableWithCostTime(g_stmtSync, g_labelName, 0, 100, &timeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount is: %d.", timeCount);
    EXPECT_GT(timeCount, 800000);

    // 订阅1阻塞放开
    g_isBlock = false;
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INSERT, indexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, subName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, subName3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);
    testSnFreeUserData(userData2);
    testSnFreeUserData(userData3);

    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(connSub2, stmtSub2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(connSub3, stmtSub3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 3; i++) {
        ret = close_epoll_thread(&g_epAsync[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 016 一个连接，3张表，3个订阅通道，3个订阅关系，表1，表2指定sub_flow_control为true，订阅1,2,3均阻塞
TEST_F(SubFlowControl, SN_058_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    for (int i = 0; i < 3; i++) {
        ret = create_epoll_thread(&g_epAsync[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }

    char labelConfig[] = "{\"max_record_count\":1000000, \"sub_flow_control\":[800000,900000,1000000]}";
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *labelName2 = "SN058Label2";
    char labelConfig2[] = "{\"max_record_count\":1000000, \"sub_flow_control\":[800000,900000,1000000]}";
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, labelName2, 0, labelConfig2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *labelName3 = "SN058Label3";
    char labelConfig3[] = "{\"max_record_count\":1000000, \"sub_flow_control\":[0,0,0]}";
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, labelName3, 0, labelConfig3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建老订阅
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subConnSn058Label";
    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[0].userEpollFd;
    connOptions.connName = subConnName;
    ret = TestYangGmcConnect(&connSub, &stmtSub, GMC_CONN_TYPE_SUB, &connOptions);
    SnUserDataT *userData;
    ret = testSnMallocUserData(&userData, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName = "subSn058Label";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subSn058LabelJson;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, OldSnCallBack, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *connSub2 = NULL;
    GmcStmtT *stmtSub2 = NULL;
    const char *subConn2Name = "subConn2Sn058Label";
    YangConnOptionT connOptions2 = {0};
    connOptions2.epollFd = &g_epAsync[1].userEpollFd;
    connOptions2.connName = subConn2Name;
    ret = TestYangGmcConnect(&connSub2, &stmtSub2, GMC_CONN_TYPE_SUB, &connOptions2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SnUserDataT *userData2;
    ret = testSnMallocUserData(&userData2, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName2 = "sub2Sn058Label";
    subInfo.subsName = subName2;
    subInfo.configJson = g_subSn058Label2Json;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub2, OldSnCallBack2, userData2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *connSub3 = NULL;
    GmcStmtT *stmtSub3 = NULL;
    const char *subConn3Name = "subConn3Sn058Label";
    YangConnOptionT connOptions3 = {0};
    connOptions3.epollFd = &g_epAsync[2].userEpollFd;
    connOptions3.connName = subConn3Name;
    ret = TestYangGmcConnect(&connSub3, &stmtSub3, GMC_CONN_TYPE_SUB, &connOptions3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SnUserDataT *userData3;
    ret = testSnMallocUserData(&userData3, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName3 = "sub3Sn058Labe";
    subInfo.subsName = subName3;
    subInfo.configJson = g_subSn058Label3Json;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub3, OldSnCallBack3, userData3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 订阅1,2,3都阻塞
    g_controlSleepLevel = 4, g_isBlock = true;
    g_isCb2Block = true;
    g_isCb3Block = true;

    // 写入数据，写到队列满的时候，触发流控
    uint32_t indexCount = 0;
    GmcDbFlowCtrlLevelE flowCtrlLevel = GMC_DB_FLOW_CTRL_LEVEL_0;
    while (indexCount < MAX_SN_RING_SIZE) {
        ret = TestInsertTable(g_stmtSync, g_labelName, indexCount, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestInsertTable(g_stmtSync, labelName2, indexCount, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestInsertTable(g_stmtSync, labelName3, indexCount, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexCount++;

        ret = GmcGetConnFlowCtrlLevel(g_connSync, &flowCtrlLevel);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (flowCtrlLevel == GMC_DB_FLOW_CTRL_LEVEL_1) {
            AW_FUN_Log(LOG_INFO, "insert record num is: %d, client flowCtrlLevel %d.", indexCount, flowCtrlLevel);
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel, GMC_DB_FLOW_CTRL_LEVEL_1);

    uint32_t timeCount = 0;
    ret = TestUpdateTableWithCostTime(g_stmtSync, g_labelName, 0, 100, &timeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount is: %d.", timeCount);
    EXPECT_GT(timeCount, 800000);
    uint32_t timeCount2 = 0;
    ret = TestUpdateTableWithCostTime(g_stmtSync, labelName2, 0, 100, &timeCount2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount2 is: %d.", timeCount2);
    EXPECT_GT(timeCount2, 800000);
    uint32_t timeCount3 = 0;
    ret = TestUpdateTableWithCostTime(g_stmtSync, labelName3, 0, 100, &timeCount3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount3 is: %d.", timeCount3);
    EXPECT_GT(timeCount3, 200);

    // 订阅1,2,3阻塞放开
    g_isBlock = false;
    g_isCb2Block = false;
    g_isCb3Block = false;
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INSERT, indexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData2, GMC_SUB_EVENT_INSERT, indexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData3, GMC_SUB_EVENT_INSERT, indexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, subName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, subName3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);
    testSnFreeUserData(userData2);
    testSnFreeUserData(userData3);

    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(connSub2, stmtSub3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(connSub3, stmtSub3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, labelName3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, labelName3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 3; i++) {
        ret = close_epoll_thread(&g_epAsync[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 017 一个连接，3张表，3个订阅通道，3个订阅关系，表1，表2指定sub_flow_control为true，订阅1阻塞
TEST_F(SubFlowControl, SN_058_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    for (int i = 0; i < 3; i++) {
        ret = create_epoll_thread(&g_epAsync[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }

    char labelConfig[] = "{\"max_record_count\":1000000, \"sub_flow_control\":[800000,900000,1000000]}";
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *labelName2 = "SN058Label2";
    char labelConfig2[] = "{\"max_record_count\":1000000, \"sub_flow_control\":[800000,900000,1000000]}";
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, labelName2, 0, labelConfig2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *labelName3 = "SN058Label3";
    char labelConfig3[] = "{\"max_record_count\":1000000, \"sub_flow_control\":[0,0,0]}";
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, labelName3, 0, labelConfig3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建老订阅
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subConnSn058Label";
    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[0].userEpollFd;
    connOptions.connName = subConnName;
    ret = TestYangGmcConnect(&connSub, &stmtSub, GMC_CONN_TYPE_SUB, &connOptions);
    SnUserDataT *userData;
    ret = testSnMallocUserData(&userData, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName = "subSn058Label";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subSn058LabelJson;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, OldSnCallBack, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *connSub2 = NULL;
    GmcStmtT *stmtSub2 = NULL;
    const char *subConn2Name = "subConn2Sn058Label";
    YangConnOptionT connOptions2 = {0};
    connOptions2.epollFd = &g_epAsync[1].userEpollFd;
    connOptions2.connName = subConn2Name;
    ret = TestYangGmcConnect(&connSub2, &stmtSub2, GMC_CONN_TYPE_SUB, &connOptions2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SnUserDataT *userData2;
    ret = testSnMallocUserData(&userData2, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName2 = "sub2Sn058Label";
    subInfo.subsName = subName2;
    subInfo.configJson = g_subSn058Label2Json;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub2, OldSnCallBack2, userData2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *connSub3 = NULL;
    GmcStmtT *stmtSub3 = NULL;
    const char *subConn3Name = "subConn3Sn058Label";
    YangConnOptionT connOptions3 = {0};
    connOptions3.epollFd = &g_epAsync[2].userEpollFd;
    connOptions3.connName = subConn3Name;
    ret = TestYangGmcConnect(&connSub3, &stmtSub3, GMC_CONN_TYPE_SUB, &connOptions3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SnUserDataT *userData3;
    ret = testSnMallocUserData(&userData3, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName3 = "sub3Sn058Labe";
    subInfo.subsName = subName3;
    subInfo.configJson = g_subSn058Label3Json;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub3, OldSnCallBack3, userData3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 订阅1阻塞
    g_controlSleepLevel = 4, g_isBlock = true;

    // 写入数据，写到队列满的时候，触发流控
    uint32_t indexCount = 0;
    GmcDbFlowCtrlLevelE flowCtrlLevel = GMC_DB_FLOW_CTRL_LEVEL_0;
    while (indexCount < MAX_SN_RING_SIZE) {
        ret = TestInsertTable(g_stmtSync, g_labelName, indexCount, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexCount++;

        ret = GmcGetConnFlowCtrlLevel(g_connSync, &flowCtrlLevel);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (flowCtrlLevel == GMC_DB_FLOW_CTRL_LEVEL_1) {
            AW_FUN_Log(LOG_INFO, "insert record num is: %d, client flowCtrlLevel %d.", indexCount, flowCtrlLevel);
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel, GMC_DB_FLOW_CTRL_LEVEL_1);

    uint32_t indexCount2 = 0;
    GmcDbFlowCtrlLevelE flowCtrlLevel2 = GMC_DB_FLOW_CTRL_LEVEL_0;
    ret = GmcGetConnFlowCtrlLevel(g_connSync, &flowCtrlLevel2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "before insert table2 g_stmtSync flowCtrlLevel2 is: %d .", flowCtrlLevel2);
    ret = TestInsertTable(g_stmtSync, labelName2, indexCount2, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    indexCount2++;
    sleep(1);
    while (indexCount2 < MAX_SN_RING_SIZE) {
        ret = TestInsertTable(g_stmtSync, labelName2, indexCount2, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexCount2++;

        ret = GmcGetConnFlowCtrlLevel(g_connSync, &flowCtrlLevel2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (flowCtrlLevel2 == GMC_DB_FLOW_CTRL_LEVEL_1) {
            AW_FUN_Log(
                LOG_INFO, "table2 insert record num is: %d, client flowCtrlLevel %d.", indexCount2, flowCtrlLevel2);
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel2, GMC_DB_FLOW_CTRL_LEVEL_0);
    ret = testWaitSnRecv(userData2, GMC_SUB_EVENT_INSERT, indexCount2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t indexCount3 = 0;
    GmcDbFlowCtrlLevelE flowCtrlLevel3 = GMC_DB_FLOW_CTRL_LEVEL_0;
    ret = GmcGetConnFlowCtrlLevel(g_connSync, &flowCtrlLevel3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "before insert table3 g_stmtSync flowCtrlLevel3 is: %d .", flowCtrlLevel3);
    ret = TestInsertTable(g_stmtSync, labelName3, indexCount3, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    indexCount3++;
    sleep(1);
    while (indexCount3 < MAX_SN_RING_SIZE) {
        ret = TestInsertTable(g_stmtSync, labelName3, indexCount3, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexCount3++;

        ret = GmcGetConnFlowCtrlLevel(g_connSync, &flowCtrlLevel3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (flowCtrlLevel3 == GMC_DB_FLOW_CTRL_LEVEL_1) {
            AW_FUN_Log(LOG_INFO, "insert record num is: %d, client flowCtrlLevel3 %d.", indexCount3, flowCtrlLevel3);
            break;
        }
    }
    AW_FUN_Log(LOG_INFO, "insert table3 flowCtrlLevel3 is: %d .", flowCtrlLevel3);
    ret = testWaitSnRecv(userData3, GMC_SUB_EVENT_INSERT, indexCount3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#ifndef DIRECT_WRITE
    // 流控等级是根据上一次dml操作返回的,此是g_connSync上流控等级为0，所以即使更新表1，此次dml操作也是不会流控的
    uint32_t timeCount = 0;
    ret = TestUpdateTableWithCostTime(g_stmtSync, g_labelName, 0, 100, &timeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount is: %d.", timeCount);
    EXPECT_LT(timeCount, 800000);
    // 更新了一条表1数据，此时g_connSync上的流控等级为1，此时再更新表2，即使表2没有阻塞，此次dml操作也是会流控的
    ret = GmcGetConnFlowCtrlLevel(g_connSync, &flowCtrlLevel2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel2, GMC_DB_FLOW_CTRL_LEVEL_1);
    uint32_t timeCount2 = 0;
    ret = TestUpdateTableWithCostTime(g_stmtSync, labelName2, 0, 100, &timeCount2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount2 is: %d.", timeCount2);
    EXPECT_GT(timeCount2, 800000);
    ret = GmcGetConnFlowCtrlLevel(g_connSync, &flowCtrlLevel2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel2, GMC_DB_FLOW_CTRL_LEVEL_0);
    // 更新表2后，返回的流控等级是0，故更新表3，此次dml操作不会流控
    uint32_t timeCount3 = 0;
    ret = TestUpdateTableWithCostTime(g_stmtSync, labelName3, 0, 100, &timeCount3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 此处timeCount3 理应少于全局1级流控sleep时间200us + 操作耗时，为保证稳定性，此处不校验时间了
    AW_FUN_Log(LOG_DEBUG, "timeCount3 is: %d.", timeCount3);
    ret = GmcGetConnFlowCtrlLevel(g_connSync, &flowCtrlLevel3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel3, GMC_DB_FLOW_CTRL_LEVEL_0);
#endif
    // 订阅1阻塞放开
    g_isBlock = false;
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INSERT, indexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, subName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, subName3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);
    testSnFreeUserData(userData2);
    testSnFreeUserData(userData3);

    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(connSub2, stmtSub3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(connSub3, stmtSub3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, labelName3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, labelName3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 3; i++) {
        ret = close_epoll_thread(&g_epAsync[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 018 一个连接，3张表，1个订阅通道，3个订阅关系，表1，表2指定sub_flow_control为true，订阅1，2,3均被阻塞
TEST_F(SubFlowControl, SN_058_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    for (int i = 0; i < 3; i++) {
        ret = create_epoll_thread(&g_epAsync[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }

    char labelConfig[] = "{\"max_record_count\":1000000, \"sub_flow_control\":[800000,900000,1000000]}";
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *labelName2 = "SN058Label2";
    char labelConfig2[] = "{\"max_record_count\":1000000, \"sub_flow_control\":[800000,900000,1000000]}";
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, labelName2, 0, labelConfig2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *labelName3 = "SN058Label3";
    char labelConfig3[] = "{\"max_record_count\":1000000,\"sub_flow_control\":[0,0,0]}";
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, labelName3, 0, labelConfig3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建老订阅
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subConnSn058Label";
    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[0].userEpollFd;
    connOptions.connName = subConnName;
    ret = TestYangGmcConnect(&connSub, &stmtSub, GMC_CONN_TYPE_SUB, &connOptions);
    SnUserDataT *userData;
    ret = testSnMallocUserData(&userData, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName = "subSn058Label";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subSn058LabelJson;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, OldSnCallBack, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *connSub2 = NULL;
    GmcStmtT *stmtSub2 = NULL;
    SnUserDataT *userData2;
    ret = testSnMallocUserData(&userData2, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName2 = "sub2Sn058Label";
    subInfo.subsName = subName2;
    subInfo.configJson = g_subSn058Label2Json;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, OldSnCallBack2, userData2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *connSub3 = NULL;
    GmcStmtT *stmtSub3 = NULL;
    SnUserDataT *userData3;
    ret = testSnMallocUserData(&userData3, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName3 = "sub3Sn058Labe";
    subInfo.subsName = subName3;
    subInfo.configJson = g_subSn058Label3Json;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, OldSnCallBack3, userData3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 订阅1,2,3都阻塞
    g_controlSleepLevel = 4, g_isBlock = true;
    g_isCb2Block = true;
    g_isCb3Block = true;

    // 写入数据，写到队列满的时候，触发流控
    uint32_t indexCount = 0;
    GmcDbFlowCtrlLevelE flowCtrlLevel = GMC_DB_FLOW_CTRL_LEVEL_0;
    while (indexCount < MAX_SN_RING_SIZE) {
        ret = TestInsertTable(g_stmtSync, g_labelName, indexCount, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestInsertTable(g_stmtSync, labelName2, indexCount, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestInsertTable(g_stmtSync, labelName3, indexCount, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexCount++;

        ret = GmcGetConnFlowCtrlLevel(g_connSync, &flowCtrlLevel);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (flowCtrlLevel == GMC_DB_FLOW_CTRL_LEVEL_1) {
            AW_FUN_Log(LOG_INFO, "insert record num is: %d, client flowCtrlLevel %d.", indexCount, flowCtrlLevel);
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel, GMC_DB_FLOW_CTRL_LEVEL_1);

    uint32_t timeCount = 0;
    ret = TestUpdateTableWithCostTime(g_stmtSync, g_labelName, 0, 100, &timeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount is: %d.", timeCount);
    EXPECT_GT(timeCount, 800000);

    uint32_t timeCount2 = 0;
    ret = TestUpdateTableWithCostTime(g_stmtSync, labelName2, 0, 100, &timeCount2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount2 is: %d.", timeCount2);
    ret = TestUpdateTableWithCostTime(g_stmtSync, labelName2, 1, 100, &timeCount2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount2 is: %d.", timeCount2);
    EXPECT_GT(timeCount2, 800000);
    GmcDbFlowCtrlLevelE flowCtrlLevel2 = GMC_DB_FLOW_CTRL_LEVEL_0;
    ret = GmcGetConnFlowCtrlLevel(g_connSync, &flowCtrlLevel2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel2, GMC_DB_FLOW_CTRL_LEVEL_1);

    uint32_t timeCount3 = 0;
    // 表3 全局流控sleep 时间
    ret = TestUpdateTableWithCostTime(g_stmtSync, labelName3, 0, 100, &timeCount3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount3 is: %d.", timeCount3);
    ret = TestUpdateTableWithCostTime(g_stmtSync, labelName3, 1, 100, &timeCount3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount3 is: %d.", timeCount3);
    EXPECT_GT(timeCount3, 200);
    GmcDbFlowCtrlLevelE flowCtrlLevel3 = GMC_DB_FLOW_CTRL_LEVEL_0;
    ret = GmcGetConnFlowCtrlLevel(g_connSync, &flowCtrlLevel3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel3, GMC_DB_FLOW_CTRL_LEVEL_1);

    // 订阅1,2,3阻塞放开
    g_isBlock = false;
    g_isCb2Block = false;
    g_isCb3Block = false;
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INSERT, indexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData2, GMC_SUB_EVENT_INSERT, indexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData3, GMC_SUB_EVENT_INSERT, indexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, subName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, subName3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);
    testSnFreeUserData(userData2);
    testSnFreeUserData(userData3);

    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, labelName3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, labelName3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 3; i++) {
        ret = close_epoll_thread(&g_epAsync[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 019 一个连接，3张表，1个订阅通道，3个订阅关系，表1，表2指定sub_flow_control为true，订阅1阻塞
TEST_F(SubFlowControl, SN_058_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    for (int i = 0; i < 3; i++) {
        ret = create_epoll_thread(&g_epAsync[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }

    char labelConfig[] = "{\"max_record_count\":1000000, \"sub_flow_control\":[800000,900000,1000000]}";
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *labelName2 = "SN058Label2";
    char labelConfig2[] = "{\"max_record_count\":1000000, \"sub_flow_control\":[800000,900000,1000000]}";
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, labelName2, 0, labelConfig2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *labelName3 = "SN058Label3";
    char labelConfig3[] = "{\"max_record_count\":1000000,\"sub_flow_control\":[0,0,0]}";
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, labelName3, 0, labelConfig3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建老订阅
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subConnSn058Label";
    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[0].userEpollFd;
    connOptions.connName = subConnName;
    ret = TestYangGmcConnect(&connSub, &stmtSub, GMC_CONN_TYPE_SUB, &connOptions);
    SnUserDataT *userData;
    ret = testSnMallocUserData(&userData, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName = "subSn058Label";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subSn058LabelJson;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, OldSnCallBack, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *connSub2 = NULL;
    GmcStmtT *stmtSub2 = NULL;
    SnUserDataT *userData2;
    ret = testSnMallocUserData(&userData2, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName2 = "sub2Sn058Label";
    subInfo.subsName = subName2;
    subInfo.configJson = g_subSn058Label2Json;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, OldSnCallBack2, userData2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *connSub3 = NULL;
    GmcStmtT *stmtSub3 = NULL;
    SnUserDataT *userData3;
    ret = testSnMallocUserData(&userData3, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName3 = "sub3Sn058Labe";
    subInfo.subsName = subName3;
    subInfo.configJson = g_subSn058Label3Json;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, OldSnCallBack3, userData3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 订阅1阻塞
    g_controlSleepLevel = 4, g_isBlock = true;

    // 写入数据，写到队列满的时候，触发流控
    uint32_t indexCount = 0;
    GmcDbFlowCtrlLevelE flowCtrlLevel = GMC_DB_FLOW_CTRL_LEVEL_0;
    while (indexCount < MAX_SN_RING_SIZE) {
        ret = TestInsertTable(g_stmtSync, g_labelName, indexCount, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestInsertTable(g_stmtSync, labelName2, indexCount, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestInsertTable(g_stmtSync, labelName3, indexCount, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexCount++;

        ret = GmcGetConnFlowCtrlLevel(g_connSync, &flowCtrlLevel);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (flowCtrlLevel == GMC_DB_FLOW_CTRL_LEVEL_1) {
            AW_FUN_Log(LOG_INFO, "insert record num is: %d, client flowCtrlLevel %d.", indexCount, flowCtrlLevel);
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel, GMC_DB_FLOW_CTRL_LEVEL_1);

    uint32_t timeCount = 0;
    ret = TestUpdateTableWithCostTime(g_stmtSync, g_labelName, 0, 100, &timeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount is: %d.", timeCount);
    EXPECT_GT(timeCount, 800000);

    uint32_t timeCount2 = 0;
    ret = TestUpdateTableWithCostTime(g_stmtSync, labelName2, 0, 100, &timeCount2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount2 is: %d.", timeCount2);
    ret = TestUpdateTableWithCostTime(g_stmtSync, labelName2, 1, 100, &timeCount2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount2 is: %d.", timeCount2);
    EXPECT_GT(timeCount2, 800000);
    GmcDbFlowCtrlLevelE flowCtrlLevel2 = GMC_DB_FLOW_CTRL_LEVEL_0;
    ret = GmcGetConnFlowCtrlLevel(g_connSync, &flowCtrlLevel2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel2, GMC_DB_FLOW_CTRL_LEVEL_1);

    uint32_t timeCount3 = 0;
    // 表3 全局流控sleep 时间
    ret = TestUpdateTableWithCostTime(g_stmtSync, labelName3, 0, 100, &timeCount3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount3 is: %d.", timeCount3);
    ret = TestUpdateTableWithCostTime(g_stmtSync, labelName3, 1, 100, &timeCount3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount3 is: %d.", timeCount3);
    EXPECT_GT(timeCount3, 200);
    GmcDbFlowCtrlLevelE flowCtrlLevel3 = GMC_DB_FLOW_CTRL_LEVEL_0;
    ret = GmcGetConnFlowCtrlLevel(g_connSync, &flowCtrlLevel3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel3, GMC_DB_FLOW_CTRL_LEVEL_1);

    // 订阅1阻塞放开
    g_isBlock = false;
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INSERT, indexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData2, GMC_SUB_EVENT_INSERT, indexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData3, GMC_SUB_EVENT_INSERT, indexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, subName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, subName3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);
    testSnFreeUserData(userData2);
    testSnFreeUserData(userData3);

    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, labelName3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, labelName3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 3; i++) {
        ret = close_epoll_thread(&g_epAsync[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 020 3个连接，3张表，3个订阅通道，3个订阅关系，表1，表2指定sub_flow_control为true，订阅1，2,3均触发流控
TEST_F(SubFlowControl, SN_058_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcConnT *connSync2 = NULL;
    GmcStmtT *stmtSync2 = NULL;
    ret = testGmcConnect(&connSync2, &stmtSync2);
    ASSERT_EQ(GMERR_OK, ret);
    GmcConnT *connSync3 = NULL;
    GmcStmtT *stmtSync3 = NULL;
    ret = testGmcConnect(&connSync3, &stmtSync3);
    ASSERT_EQ(GMERR_OK, ret);

    for (int i = 0; i < 3; i++) {
        ret = create_epoll_thread(&g_epAsync[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }

    char labelConfig[] = "{\"max_record_count\":1000000, \"sub_flow_control\":[800000,900000,1000000]}";
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *labelName2 = "SN058Label2";
    char labelConfig2[] = "{\"max_record_count\":1000000, \"sub_flow_control\":[800000,900000,1000000]}";
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, labelName2, 0, labelConfig2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *labelName3 = "SN058Label3";
    char labelConfig3[] = "{\"max_record_count\":1000000,\"sub_flow_control\":[0,0,0]}";
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, labelName3, 0, labelConfig3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建老订阅
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subConnSn058Label";
    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[0].userEpollFd;
    connOptions.connName = subConnName;
    ret = TestYangGmcConnect(&connSub, &stmtSub, GMC_CONN_TYPE_SUB, &connOptions);
    SnUserDataT *userData;
    ret = testSnMallocUserData(&userData, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName = "subSn058Label";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subSn058LabelJson;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, OldSnCallBack, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *connSub2 = NULL;
    GmcStmtT *stmtSub2 = NULL;
    const char *subConn2Name = "subConn2Sn058Label";
    YangConnOptionT connOptions2 = {0};
    connOptions2.epollFd = &g_epAsync[1].userEpollFd;
    connOptions2.connName = subConn2Name;
    ret = TestYangGmcConnect(&connSub2, &stmtSub2, GMC_CONN_TYPE_SUB, &connOptions2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SnUserDataT *userData2;
    ret = testSnMallocUserData(&userData2, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName2 = "sub2Sn058Label";
    subInfo.subsName = subName2;
    subInfo.configJson = g_subSn058Label2Json;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub2, OldSnCallBack2, userData2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *connSub3 = NULL;
    GmcStmtT *stmtSub3 = NULL;
    const char *subConn3Name = "subConn3Sn058Label";
    YangConnOptionT connOptions3 = {0};
    connOptions3.epollFd = &g_epAsync[2].userEpollFd;
    connOptions3.connName = subConn3Name;
    ret = TestYangGmcConnect(&connSub3, &stmtSub3, GMC_CONN_TYPE_SUB, &connOptions3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SnUserDataT *userData3;
    ret = testSnMallocUserData(&userData3, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName3 = "sub3Sn058Labe";
    subInfo.subsName = subName3;
    subInfo.configJson = g_subSn058Label3Json;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub3, OldSnCallBack3, userData3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 订阅1,2,3都阻塞
    g_controlSleepLevel = 4, g_isBlock = true;
    g_isCb2Block = true;
    g_isCb3Block = true;

    // 写入数据，写到队列满的时候，触发流控
    uint32_t indexCount = 0;
    GmcDbFlowCtrlLevelE flowCtrlLevel = GMC_DB_FLOW_CTRL_LEVEL_0;
    while (indexCount < MAX_SN_RING_SIZE) {
        ret = TestInsertTable(g_stmtSync, g_labelName, indexCount, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexCount++;

        ret = GmcGetConnFlowCtrlLevel(g_connSync, &flowCtrlLevel);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (flowCtrlLevel == GMC_DB_FLOW_CTRL_LEVEL_1) {
            AW_FUN_Log(LOG_INFO, "insert %s record num is: %d, client flowCtrlLevel %d.", g_labelName, indexCount,
                flowCtrlLevel);
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel, GMC_DB_FLOW_CTRL_LEVEL_1);
    AW_FUN_Log(LOG_STEP, "insert %s end.", g_labelName);

    uint32_t indexCount2 = 0;
    GmcDbFlowCtrlLevelE flowCtrlLevel2 = GMC_DB_FLOW_CTRL_LEVEL_0;
    while (indexCount2 < MAX_SN_RING_SIZE) {
        ret = TestInsertTable(stmtSync2, labelName2, indexCount2, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexCount2++;

        ret = GmcGetConnFlowCtrlLevel(connSync2, &flowCtrlLevel2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (flowCtrlLevel2 == GMC_DB_FLOW_CTRL_LEVEL_1) {
            AW_FUN_Log(LOG_INFO, "insert %s record num is: %d, client flowCtrlLevel %d.", labelName2, indexCount2,
                flowCtrlLevel2);
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel2, GMC_DB_FLOW_CTRL_LEVEL_1);
    AW_FUN_Log(LOG_STEP, "insert %s end.", labelName2);

    uint32_t indexCount3 = 0;
    GmcDbFlowCtrlLevelE flowCtrlLevel3 = GMC_DB_FLOW_CTRL_LEVEL_0;
    while (indexCount3 < MAX_SN_RING_SIZE) {
        ret = TestInsertTable(stmtSync3, labelName3, indexCount3, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexCount3++;

        ret = GmcGetConnFlowCtrlLevel(connSync3, &flowCtrlLevel3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (flowCtrlLevel3 == GMC_DB_FLOW_CTRL_LEVEL_1) {
            AW_FUN_Log(LOG_INFO, "insert %s record num is: %d, client flowCtrlLevel %d.", labelName3, indexCount3,
                flowCtrlLevel3);
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel3, GMC_DB_FLOW_CTRL_LEVEL_1);
    AW_FUN_Log(LOG_STEP, "insert %s end.", labelName3);

    uint32_t timeCount = 0;
    ret = TestUpdateTableWithCostTime(g_stmtSync, g_labelName, 0, 100, &timeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount is: %d.", timeCount);
    ret = TestUpdateTableWithCostTime(g_stmtSync, g_labelName, 1, 100, &timeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount is: %d.", timeCount);
    EXPECT_GT(timeCount, 800000);

    uint32_t timeCount2 = 0;
    ret = TestUpdateTableWithCostTime(stmtSync2, labelName2, 0, 100, &timeCount2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount is: %d.", timeCount2);
    ret = TestUpdateTableWithCostTime(stmtSync2, labelName2, 1, 100, &timeCount2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount is: %d.", timeCount2);
    EXPECT_GT(timeCount2, 800000);

    uint32_t timeCount3 = 0;
    ret = TestUpdateTableWithCostTime(stmtSync3, labelName3, 0, 100, &timeCount3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount is: %d.", timeCount3);
    ret = TestUpdateTableWithCostTime(stmtSync3, labelName3, 1, 100, &timeCount3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount is: %d.", timeCount3);
    EXPECT_GT(timeCount3, 200);

    // 订阅1,2,3阻塞放开
    g_isBlock = false;
    g_isCb2Block = false;
    g_isCb3Block = false;
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INSERT, indexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData2, GMC_SUB_EVENT_INSERT, indexCount2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData3, GMC_SUB_EVENT_INSERT, indexCount3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, subName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, subName3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);
    testSnFreeUserData(userData2);
    testSnFreeUserData(userData3);

    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(connSub2, stmtSub3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(connSub3, stmtSub3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, labelName3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, labelName3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 3; i++) {
        ret = close_epoll_thread(&g_epAsync[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(connSync2, stmtSync2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(connSync3, stmtSync3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 021 3个连接，3张表，3个订阅通道，3个订阅关系，表1，表2指定sub_flow_control为true，订阅1触发流控
TEST_F(SubFlowControl, SN_058_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcConnT *connSync2 = NULL;
    GmcStmtT *stmtSync2 = NULL;
    ret = testGmcConnect(&connSync2, &stmtSync2);
    ASSERT_EQ(GMERR_OK, ret);
    GmcConnT *connSync3 = NULL;
    GmcStmtT *stmtSync3 = NULL;
    ret = testGmcConnect(&connSync3, &stmtSync3);
    ASSERT_EQ(GMERR_OK, ret);

    for (int i = 0; i < 3; i++) {
        ret = create_epoll_thread(&g_epAsync[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }

    char labelConfig[] = "{\"max_record_count\":1000000, \"sub_flow_control\":[800000,900000,1000000]}";
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *labelName2 = "SN058Label2";
    char labelConfig2[] = "{\"max_record_count\":1000000, \"sub_flow_control\":[800000,900000,1000000]}";
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, labelName2, 0, labelConfig2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *labelName3 = "SN058Label3";
    char labelConfig3[] = "{\"max_record_count\":1000000,\"sub_flow_control\":[0,0,0]}";
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, labelName3, 0, labelConfig3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建老订阅
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subConnSn058Label";
    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[0].userEpollFd;
    connOptions.connName = subConnName;
    ret = TestYangGmcConnect(&connSub, &stmtSub, GMC_CONN_TYPE_SUB, &connOptions);
    SnUserDataT *userData;
    ret = testSnMallocUserData(&userData, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName = "subSn058Label";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subSn058LabelJson;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, OldSnCallBack, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *connSub2 = NULL;
    GmcStmtT *stmtSub2 = NULL;
    const char *subConn2Name = "subConn2Sn058Label";
    YangConnOptionT connOptions2 = {0};
    connOptions2.epollFd = &g_epAsync[1].userEpollFd;
    connOptions2.connName = subConn2Name;
    ret = TestYangGmcConnect(&connSub2, &stmtSub2, GMC_CONN_TYPE_SUB, &connOptions2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SnUserDataT *userData2;
    ret = testSnMallocUserData(&userData2, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName2 = "sub2Sn058Label";
    subInfo.subsName = subName2;
    subInfo.configJson = g_subSn058Label2Json;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub2, OldSnCallBack2, userData2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *connSub3 = NULL;
    GmcStmtT *stmtSub3 = NULL;
    const char *subConn3Name = "subConn3Sn058Label";
    YangConnOptionT connOptions3 = {0};
    connOptions3.epollFd = &g_epAsync[2].userEpollFd;
    connOptions3.connName = subConn3Name;
    ret = TestYangGmcConnect(&connSub3, &stmtSub3, GMC_CONN_TYPE_SUB, &connOptions3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SnUserDataT *userData3;
    ret = testSnMallocUserData(&userData3, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName3 = "sub3Sn058Labe";
    subInfo.subsName = subName3;
    subInfo.configJson = g_subSn058Label3Json;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub3, OldSnCallBack3, userData3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 订阅1阻塞
    g_controlSleepLevel = 4, g_isBlock = true;

    // 写入数据，写到队列满的时候，触发流控
    uint32_t indexCount = 0;
    GmcDbFlowCtrlLevelE flowCtrlLevel = GMC_DB_FLOW_CTRL_LEVEL_0;
    while (indexCount < MAX_SN_RING_SIZE) {
        ret = TestInsertTable(g_stmtSync, g_labelName, indexCount, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexCount++;

        ret = GmcGetConnFlowCtrlLevel(g_connSync, &flowCtrlLevel);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (flowCtrlLevel == GMC_DB_FLOW_CTRL_LEVEL_1) {
            AW_FUN_Log(LOG_INFO, "insert %s record num is: %d, client flowCtrlLevel %d.", g_labelName, indexCount,
                flowCtrlLevel);
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel, GMC_DB_FLOW_CTRL_LEVEL_1);
    AW_FUN_Log(LOG_STEP, "insert %s end.", g_labelName);

    uint32_t indexCount2 = 0;
    GmcDbFlowCtrlLevelE flowCtrlLevel2 = GMC_DB_FLOW_CTRL_LEVEL_0;
    while (indexCount2 < MAX_SN_RING_SIZE) {
        ret = TestInsertTable(stmtSync2, labelName2, indexCount2, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexCount2++;

        ret = GmcGetConnFlowCtrlLevel(connSync2, &flowCtrlLevel2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (flowCtrlLevel2 == GMC_DB_FLOW_CTRL_LEVEL_1) {
            AW_FUN_Log(LOG_INFO, "insert %s record num is: %d, client flowCtrlLevel %d.", labelName2, indexCount2,
                flowCtrlLevel2);
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel2, GMC_DB_FLOW_CTRL_LEVEL_0);
    AW_FUN_Log(LOG_STEP, "insert %s end.", labelName2);

    uint32_t indexCount3 = 0;
    GmcDbFlowCtrlLevelE flowCtrlLevel3 = GMC_DB_FLOW_CTRL_LEVEL_0;
    while (indexCount3 < MAX_SN_RING_SIZE) {
        ret = TestInsertTable(stmtSync3, labelName3, indexCount3, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexCount3++;

        ret = GmcGetConnFlowCtrlLevel(connSync3, &flowCtrlLevel3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (flowCtrlLevel3 == GMC_DB_FLOW_CTRL_LEVEL_1) {
            AW_FUN_Log(LOG_INFO, "insert %s record num is: %d, client flowCtrlLevel %d.", labelName3, indexCount3,
                flowCtrlLevel3);
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel3, GMC_DB_FLOW_CTRL_LEVEL_0);
    AW_FUN_Log(LOG_STEP, "insert %s end.", labelName3);

#ifndef DIRECT_WRITE
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=SN058Label");
    ret = GmcGetConnFlowCtrlLevel(g_connSync, &flowCtrlLevel);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "flowCtr1 %d.", flowCtrlLevel);
    uint32_t timeCount = 0;
    ret = TestUpdateTableWithCostTime(g_stmtSync, g_labelName, 0, 100, &timeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUpdateTableWithCostTime(g_stmtSync, g_labelName, 1, 100, &timeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount is: %d.", timeCount);
    EXPECT_GT(timeCount, 800000);

    uint32_t timeCount2 = 0;
    ret = TestUpdateTableWithCostTime(stmtSync2, labelName2, 0, 100, &timeCount2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount is: %d.", timeCount2);
    EXPECT_LT(timeCount2, 800000);

    uint32_t timeCount3 = 0;
    ret = TestUpdateTableWithCostTime(stmtSync3, labelName3, 0, 100, &timeCount3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount is: %d.", timeCount3);
    // 小于全局1以及流控sleeptime 200,容易受其他dml本身耗时影响，故此处不进行校验
#endif

    ret = testWaitSnRecv(userData2, GMC_SUB_EVENT_INSERT, indexCount2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData3, GMC_SUB_EVENT_INSERT, indexCount3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 订阅1阻塞放开
    g_isBlock = false;
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INSERT, indexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, subName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, subName3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);
    testSnFreeUserData(userData2);
    testSnFreeUserData(userData3);

    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(connSub2, stmtSub3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(connSub3, stmtSub3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, labelName3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, labelName3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 3; i++) {
        ret = close_epoll_thread(&g_epAsync[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(connSync2, stmtSync2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(connSync3, stmtSync3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 022 3个连接混合操作3张表，3个订阅关系，表1，表2指定sub_flow_control为true，订阅1，2,3均触发流控
TEST_F(SubFlowControl, SN_058_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcConnT *connSync2 = NULL;
    GmcStmtT *stmtSync2 = NULL;
    ret = testGmcConnect(&connSync2, &stmtSync2);
    ASSERT_EQ(GMERR_OK, ret);
    GmcConnT *connSync3 = NULL;
    GmcStmtT *stmtSync3 = NULL;
    ret = testGmcConnect(&connSync3, &stmtSync3);
    ASSERT_EQ(GMERR_OK, ret);

    for (int i = 0; i < 3; i++) {
        ret = create_epoll_thread(&g_epAsync[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }

    char labelConfig[] = "{\"max_record_count\":1000000, \"sub_flow_control\":[800000,900000,1000000]}";
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *labelName2 = "SN058Label2";
    char labelConfig2[] = "{\"max_record_count\":1000000, \"sub_flow_control\":[800000,900000,1000000]}";
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, labelName2, 0, labelConfig2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *labelName3 = "SN058Label3";
    char labelConfig3[] = "{\"max_record_count\":1000000,\"sub_flow_control\":[0,0,0]}";
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, labelName3, 0, labelConfig3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建老订阅
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subConnSn058Label";
    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[0].userEpollFd;
    connOptions.connName = subConnName;
    ret = TestYangGmcConnect(&connSub, &stmtSub, GMC_CONN_TYPE_SUB, &connOptions);
    SnUserDataT *userData;
    ret = testSnMallocUserData(&userData, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName = "subSn058Label";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subSn058LabelJson;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, OldSnCallBack, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *connSub2 = NULL;
    GmcStmtT *stmtSub2 = NULL;
    const char *subConn2Name = "subConn2Sn058Label";
    YangConnOptionT connOptions2 = {0};
    connOptions2.epollFd = &g_epAsync[1].userEpollFd;
    connOptions2.connName = subConn2Name;
    ret = TestYangGmcConnect(&connSub2, &stmtSub2, GMC_CONN_TYPE_SUB, &connOptions2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SnUserDataT *userData2;
    ret = testSnMallocUserData(&userData2, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName2 = "sub2Sn058Label";
    subInfo.subsName = subName2;
    subInfo.configJson = g_subSn058Label2Json;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub2, OldSnCallBack2, userData2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *connSub3 = NULL;
    GmcStmtT *stmtSub3 = NULL;
    const char *subConn3Name = "subConn3Sn058Label";
    YangConnOptionT connOptions3 = {0};
    connOptions3.epollFd = &g_epAsync[2].userEpollFd;
    connOptions3.connName = subConn3Name;
    ret = TestYangGmcConnect(&connSub3, &stmtSub3, GMC_CONN_TYPE_SUB, &connOptions3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SnUserDataT *userData3;
    ret = testSnMallocUserData(&userData3, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName3 = "sub3Sn058Labe";
    subInfo.subsName = subName3;
    subInfo.configJson = g_subSn058Label3Json;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub3, OldSnCallBack3, userData3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 订阅1,2,3都阻塞
    g_controlSleepLevel = 4, g_isBlock = true;
    g_isCb2Block = true;
    g_isCb3Block = true;

    // 写入数据，写到队列满的时候，触发流控
    uint32_t indexCount = 0;
    GmcDbFlowCtrlLevelE flowCtrlLevel = GMC_DB_FLOW_CTRL_LEVEL_0;
    GmcDbFlowCtrlLevelE flowCtrlLevel2 = GMC_DB_FLOW_CTRL_LEVEL_0;
    GmcDbFlowCtrlLevelE flowCtrlLevel3 = GMC_DB_FLOW_CTRL_LEVEL_0;
    while (indexCount < MAX_SN_RING_SIZE) {
        ret = TestInsertTable(g_stmtSync, g_labelName, indexCount, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexCount++;
        ret = GmcGetConnFlowCtrlLevel(g_connSync, &flowCtrlLevel);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = TestInsertTable(stmtSync2, g_labelName, indexCount, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexCount++;
        ret = GmcGetConnFlowCtrlLevel(connSync2, &flowCtrlLevel2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = TestInsertTable(stmtSync3, g_labelName, indexCount, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexCount++;
        ret = GmcGetConnFlowCtrlLevel(connSync3, &flowCtrlLevel3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        if ((flowCtrlLevel == GMC_DB_FLOW_CTRL_LEVEL_1) && (flowCtrlLevel2 == GMC_DB_FLOW_CTRL_LEVEL_1) &&
            (flowCtrlLevel3 == GMC_DB_FLOW_CTRL_LEVEL_1)) {
            AW_FUN_Log(LOG_INFO,
                "3 conn insert %s record num is: %d, conn1 flowCtrlLevel %d, conn2 flowCtrlLevel %d, conn3 "
                "flowCtrlLevel %d.",
                g_labelName, indexCount, flowCtrlLevel, flowCtrlLevel2, flowCtrlLevel3);
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel, GMC_DB_FLOW_CTRL_LEVEL_1);
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel2, GMC_DB_FLOW_CTRL_LEVEL_1);
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel3, GMC_DB_FLOW_CTRL_LEVEL_1);
    AW_FUN_Log(LOG_STEP, "insert %s end.", g_labelName);

    uint32_t indexCount2 = 0;
    flowCtrlLevel = GMC_DB_FLOW_CTRL_LEVEL_0;
    flowCtrlLevel2 = GMC_DB_FLOW_CTRL_LEVEL_0;
    flowCtrlLevel3 = GMC_DB_FLOW_CTRL_LEVEL_0;
    while (indexCount2 < MAX_SN_RING_SIZE) {
        ret = TestInsertTable(g_stmtSync, labelName2, indexCount2, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexCount2++;
        ret = GmcGetConnFlowCtrlLevel(g_connSync, &flowCtrlLevel);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = TestInsertTable(stmtSync2, labelName2, indexCount2, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexCount2++;
        ret = GmcGetConnFlowCtrlLevel(connSync2, &flowCtrlLevel2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = TestInsertTable(stmtSync3, labelName2, indexCount2, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexCount2++;
        ret = GmcGetConnFlowCtrlLevel(connSync3, &flowCtrlLevel3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        if ((flowCtrlLevel == GMC_DB_FLOW_CTRL_LEVEL_1) && (flowCtrlLevel2 == GMC_DB_FLOW_CTRL_LEVEL_1) &&
            (flowCtrlLevel3 == GMC_DB_FLOW_CTRL_LEVEL_1)) {
            AW_FUN_Log(LOG_INFO,
                "3 conn insert %s record num is: %d, conn1 flowCtrlLevel %d, conn2 flowCtrlLevel %d, conn3 "
                "flowCtrlLevel %d.",
                labelName2, indexCount2, flowCtrlLevel, flowCtrlLevel2, flowCtrlLevel3);
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel, GMC_DB_FLOW_CTRL_LEVEL_1);
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel2, GMC_DB_FLOW_CTRL_LEVEL_1);
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel3, GMC_DB_FLOW_CTRL_LEVEL_1);
    AW_FUN_Log(LOG_STEP, "insert %s end.", labelName2);

    uint32_t indexCount3 = 0;
    flowCtrlLevel = GMC_DB_FLOW_CTRL_LEVEL_0;
    flowCtrlLevel2 = GMC_DB_FLOW_CTRL_LEVEL_0;
    flowCtrlLevel3 = GMC_DB_FLOW_CTRL_LEVEL_0;
    while (indexCount3 < MAX_SN_RING_SIZE) {
        ret = TestInsertTable(g_stmtSync, labelName3, indexCount3, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexCount3++;
        ret = GmcGetConnFlowCtrlLevel(g_connSync, &flowCtrlLevel);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = TestInsertTable(stmtSync2, labelName3, indexCount3, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexCount3++;
        ret = GmcGetConnFlowCtrlLevel(connSync2, &flowCtrlLevel2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = TestInsertTable(stmtSync3, labelName3, indexCount3, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexCount3++;
        ret = GmcGetConnFlowCtrlLevel(connSync3, &flowCtrlLevel3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        if ((flowCtrlLevel == GMC_DB_FLOW_CTRL_LEVEL_1) && (flowCtrlLevel2 == GMC_DB_FLOW_CTRL_LEVEL_1) &&
            (flowCtrlLevel3 == GMC_DB_FLOW_CTRL_LEVEL_1)) {
            AW_FUN_Log(LOG_INFO,
                "3 conn insert %s record num is: %d, conn1 flowCtrlLevel %d, conn2 flowCtrlLevel %d, conn3 "
                "flowCtrlLevel %d.",
                labelName3, indexCount3, flowCtrlLevel, flowCtrlLevel2, flowCtrlLevel3);
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel, GMC_DB_FLOW_CTRL_LEVEL_1);
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel2, GMC_DB_FLOW_CTRL_LEVEL_1);
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel3, GMC_DB_FLOW_CTRL_LEVEL_1);
    AW_FUN_Log(LOG_STEP, "insert %s end.", labelName3);

    uint32_t timeCount = 0;
    ret = TestUpdateTableWithCostTime(g_stmtSync, g_labelName, 0, 100, &timeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount is: %d.", timeCount);
    EXPECT_GT(timeCount, 800000);

    uint32_t timeCount2 = 0;
    ret = TestUpdateTableWithCostTime(stmtSync2, labelName2, 0, 100, &timeCount2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount is: %d.", timeCount2);
    EXPECT_GT(timeCount2, 800000);

    uint32_t timeCount3 = 0;
    ret = TestUpdateTableWithCostTime(stmtSync3, labelName3, 0, 100, &timeCount3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount is: %d.", timeCount3);
    EXPECT_GT(timeCount3, 200);

    // 订阅1,2,3阻塞放开
    g_isBlock = false;
    g_isCb2Block = false;
    g_isCb3Block = false;
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INSERT, indexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData2, GMC_SUB_EVENT_INSERT, indexCount2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData3, GMC_SUB_EVENT_INSERT, indexCount3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, subName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, subName3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);
    testSnFreeUserData(userData2);
    testSnFreeUserData(userData3);

    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(connSub2, stmtSub3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(connSub3, stmtSub3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, labelName3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, labelName3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 3; i++) {
        ret = close_epoll_thread(&g_epAsync[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(connSync2, stmtSync2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(connSync3, stmtSync3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 023 3个连接混合操作3张表，3个订阅关系，表1，表2指定sub_flow_control为true，订阅1触发流控
TEST_F(SubFlowControl, SN_058_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcConnT *connSync2 = NULL;
    GmcStmtT *stmtSync2 = NULL;
    ret = testGmcConnect(&connSync2, &stmtSync2);
    ASSERT_EQ(GMERR_OK, ret);
    GmcConnT *connSync3 = NULL;
    GmcStmtT *stmtSync3 = NULL;
    ret = testGmcConnect(&connSync3, &stmtSync3);
    ASSERT_EQ(GMERR_OK, ret);

    for (int i = 0; i < 3; i++) {
        ret = create_epoll_thread(&g_epAsync[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }

    char labelConfig[] = "{\"max_record_count\":1000000, \"sub_flow_control\":[800000,900000,1000000]}";
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *labelName2 = "SN058Label2";
    char labelConfig2[] = "{\"max_record_count\":1000000, \"sub_flow_control\":[800000,900000,1000000]}";
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, labelName2, 0, labelConfig2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *labelName3 = "SN058Label3";
    char labelConfig3[] = "{\"max_record_count\":1000000,\"sub_flow_control\":[0,0,0]}";
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, labelName3, 0, labelConfig3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建老订阅
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subConnSn058Label";
    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[0].userEpollFd;
    connOptions.connName = subConnName;
    ret = TestYangGmcConnect(&connSub, &stmtSub, GMC_CONN_TYPE_SUB, &connOptions);
    SnUserDataT *userData;
    ret = testSnMallocUserData(&userData, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName = "subSn058Label";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subSn058LabelJson;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, OldSnCallBack, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *connSub2 = NULL;
    GmcStmtT *stmtSub2 = NULL;
    const char *subConn2Name = "subConn2Sn058Label";
    YangConnOptionT connOptions2 = {0};
    connOptions2.epollFd = &g_epAsync[1].userEpollFd;
    connOptions2.connName = subConn2Name;
    ret = TestYangGmcConnect(&connSub2, &stmtSub2, GMC_CONN_TYPE_SUB, &connOptions2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SnUserDataT *userData2;
    ret = testSnMallocUserData(&userData2, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName2 = "sub2Sn058Label";
    subInfo.subsName = subName2;
    subInfo.configJson = g_subSn058Label2Json;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub2, OldSnCallBack2, userData2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *connSub3 = NULL;
    GmcStmtT *stmtSub3 = NULL;
    const char *subConn3Name = "subConn3Sn058Label";
    YangConnOptionT connOptions3 = {0};
    connOptions3.epollFd = &g_epAsync[2].userEpollFd;
    connOptions3.connName = subConn3Name;
    ret = TestYangGmcConnect(&connSub3, &stmtSub3, GMC_CONN_TYPE_SUB, &connOptions3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SnUserDataT *userData3;
    ret = testSnMallocUserData(&userData3, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName3 = "sub3Sn058Labe";
    subInfo.subsName = subName3;
    subInfo.configJson = g_subSn058Label3Json;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub3, OldSnCallBack3, userData3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 订阅1阻塞
    g_controlSleepLevel = 4, g_isBlock = true;

    // 写入数据，写到队列满的时候，触发流控
    uint32_t indexCount = 0;
    GmcDbFlowCtrlLevelE flowCtrlLevel = GMC_DB_FLOW_CTRL_LEVEL_0;
    GmcDbFlowCtrlLevelE flowCtrlLevel2 = GMC_DB_FLOW_CTRL_LEVEL_0;
    GmcDbFlowCtrlLevelE flowCtrlLevel3 = GMC_DB_FLOW_CTRL_LEVEL_0;
    while (indexCount < MAX_SN_RING_SIZE) {
        ret = TestInsertTable(g_stmtSync, g_labelName, indexCount, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexCount++;
        ret = GmcGetConnFlowCtrlLevel(g_connSync, &flowCtrlLevel);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = TestInsertTable(stmtSync2, g_labelName, indexCount, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexCount++;
        ret = GmcGetConnFlowCtrlLevel(connSync2, &flowCtrlLevel2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = TestInsertTable(stmtSync3, g_labelName, indexCount, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexCount++;
        ret = GmcGetConnFlowCtrlLevel(connSync3, &flowCtrlLevel3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        if ((flowCtrlLevel == GMC_DB_FLOW_CTRL_LEVEL_1) && (flowCtrlLevel2 == GMC_DB_FLOW_CTRL_LEVEL_1) &&
            (flowCtrlLevel3 == GMC_DB_FLOW_CTRL_LEVEL_1)) {
            AW_FUN_Log(LOG_INFO,
                "3 conn insert %s record num is: %d, conn1 flowCtrlLevel %d, conn2 flowCtrlLevel %d, conn3 "
                "flowCtrlLevel %d.",
                g_labelName, indexCount, flowCtrlLevel, flowCtrlLevel2, flowCtrlLevel3);
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel, GMC_DB_FLOW_CTRL_LEVEL_1);
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel2, GMC_DB_FLOW_CTRL_LEVEL_1);
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel3, GMC_DB_FLOW_CTRL_LEVEL_1);
    AW_FUN_Log(LOG_STEP, "insert %s end.", g_labelName);

    uint32_t indexCount2 = 0;
    flowCtrlLevel = GMC_DB_FLOW_CTRL_LEVEL_0;
    flowCtrlLevel2 = GMC_DB_FLOW_CTRL_LEVEL_0;
    flowCtrlLevel3 = GMC_DB_FLOW_CTRL_LEVEL_0;
    while (indexCount2 < MAX_SN_RING_SIZE) {
        ret = TestInsertTable(g_stmtSync, labelName2, indexCount2, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexCount2++;
        ret = GmcGetConnFlowCtrlLevel(g_connSync, &flowCtrlLevel);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = TestInsertTable(stmtSync2, labelName2, indexCount2, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexCount2++;
        ret = GmcGetConnFlowCtrlLevel(connSync2, &flowCtrlLevel2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = TestInsertTable(stmtSync3, labelName2, indexCount2, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexCount2++;
        ret = GmcGetConnFlowCtrlLevel(connSync3, &flowCtrlLevel3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        if ((flowCtrlLevel == GMC_DB_FLOW_CTRL_LEVEL_0) && (flowCtrlLevel2 == GMC_DB_FLOW_CTRL_LEVEL_0) &&
            (flowCtrlLevel3 == GMC_DB_FLOW_CTRL_LEVEL_0)) {
            AW_FUN_Log(LOG_INFO,
                "3 conn insert %s record num is: %d, conn1 flowCtrlLevel %d, conn2 flowCtrlLevel %d, conn3 "
                "flowCtrlLevel %d.",
                labelName2, indexCount2, flowCtrlLevel, flowCtrlLevel2, flowCtrlLevel3);
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel, GMC_DB_FLOW_CTRL_LEVEL_0);
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel2, GMC_DB_FLOW_CTRL_LEVEL_0);
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel3, GMC_DB_FLOW_CTRL_LEVEL_0);
    AW_FUN_Log(LOG_STEP, "insert %s end.", labelName2);

    uint32_t indexCount3 = 0;
    flowCtrlLevel = GMC_DB_FLOW_CTRL_LEVEL_0;
    flowCtrlLevel2 = GMC_DB_FLOW_CTRL_LEVEL_0;
    flowCtrlLevel3 = GMC_DB_FLOW_CTRL_LEVEL_0;
    while (indexCount3 < MAX_SN_RING_SIZE) {
        ret = TestInsertTable(g_stmtSync, labelName3, indexCount3, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexCount3++;
        ret = GmcGetConnFlowCtrlLevel(g_connSync, &flowCtrlLevel);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = TestInsertTable(stmtSync2, labelName3, indexCount3, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexCount3++;
        ret = GmcGetConnFlowCtrlLevel(connSync2, &flowCtrlLevel2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = TestInsertTable(stmtSync3, labelName3, indexCount3, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexCount3++;
        ret = GmcGetConnFlowCtrlLevel(connSync3, &flowCtrlLevel3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        if ((flowCtrlLevel == GMC_DB_FLOW_CTRL_LEVEL_1) && (flowCtrlLevel2 == GMC_DB_FLOW_CTRL_LEVEL_1) &&
            (flowCtrlLevel3 == GMC_DB_FLOW_CTRL_LEVEL_1)) {
            AW_FUN_Log(LOG_INFO,
                "3 conn insert %s record num is: %d, conn1 flowCtrlLevel %d, conn2 flowCtrlLevel %d, conn3 "
                "flowCtrlLevel %d.",
                labelName3, indexCount3, flowCtrlLevel, flowCtrlLevel2, flowCtrlLevel3);
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel, GMC_DB_FLOW_CTRL_LEVEL_0);
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel2, GMC_DB_FLOW_CTRL_LEVEL_0);
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel3, GMC_DB_FLOW_CTRL_LEVEL_0);
    AW_FUN_Log(LOG_STEP, "insert %s end.", labelName3);

    uint32_t timeCount = 0;
    ret = TestUpdateTableWithCostTime(g_stmtSync, g_labelName, 0, 100, &timeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount is: %d.", timeCount);
    EXPECT_LT(timeCount, 800000);

    uint32_t timeCount2 = 0;
    ret = TestUpdateTableWithCostTime(stmtSync2, labelName2, 0, 100, &timeCount2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount2 is: %d.", timeCount2);
    EXPECT_LT(timeCount2, 800000);

    uint32_t timeCount3 = 0;
    ret = TestUpdateTableWithCostTime(stmtSync3, labelName3, 0, 100, &timeCount3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 小于全局1级流控sleep时间，容易波动故此处不校验
    AW_FUN_Log(LOG_DEBUG, "timeCount3 is: %d.", timeCount3);

    // 订阅1,2,3阻塞放开
    g_isBlock = false;
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INSERT, indexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData2, GMC_SUB_EVENT_INSERT, indexCount2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData3, GMC_SUB_EVENT_INSERT, indexCount3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, subName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, subName3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);
    testSnFreeUserData(userData2);
    testSnFreeUserData(userData3);

    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(connSub2, stmtSub3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(connSub3, stmtSub3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, labelName3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, labelName3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 3; i++) {
        ret = close_epoll_thread(&g_epAsync[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(connSync2, stmtSync2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(connSync3, stmtSync3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 024  同一个连接同时订阅新订阅和老订阅，表指定sub_flow_control为true
TEST_F(SubFlowControl, SN_058_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char labelConfig[] =
        "{\"max_record_count\":1000000, \"status_merge_sub\":true, \"sub_flow_control\":[800000,900000,1000000]}";
    int ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建老订阅
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subConnSn058Label";
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SnUserDataT *userData;
    ret = testSnMallocUserData(&userData, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName = "subSn058Label";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subSn058LabelJson;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, OldSnCallBack, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *stmgSubName = "stmgSubSn058Label";
    GmcSubConfigT stmgSubInfo;
    stmgSubInfo.subsName = stmgSubName;
    stmgSubInfo.configJson = g_stmgSubInfo;
    SnUserDataT *stmgUserData;
    ret = testSnMallocUserData(&stmgUserData, 1, 0);
    ret = GmcSubscribe(g_stmtSync, &stmgSubInfo, connSub, NewSnCallBack, stmgUserData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_controlSleepLevel = 4, g_isBlock = true;  // 阻塞回调

    // 写入数据，写到队列满的时候，触发流控
    uint32_t indexCount = 0;
    GmcDbFlowCtrlLevelE flowCtrlLevel = GMC_DB_FLOW_CTRL_LEVEL_0;
    while (indexCount < MAX_SN_RING_SIZE) {
        ret = TestInsertTable(g_stmtSync, g_labelName, indexCount, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexCount++;

        ret = GmcGetConnFlowCtrlLevel(g_connSync, &flowCtrlLevel);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (flowCtrlLevel == GMC_DB_FLOW_CTRL_LEVEL_1) {
            AW_FUN_Log(LOG_INFO, "insert record num is: %d, client flowCtrlLevel %d.", indexCount, flowCtrlLevel);
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel, GMC_DB_FLOW_CTRL_LEVEL_1);

    uint32_t timeCount = 0;
    ret = TestUpdateTableWithCostTime(g_stmtSync, g_labelName, 0, 100, &timeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount is: %d.", timeCount);
    EXPECT_GT(timeCount, 800000);

    g_isBlock = false;  // 放开阻塞
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INSERT, indexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitStMgSnRecv(stmgUserData, GMC_SUB_EVENT_MODIFY, indexCount, indexCount + 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, stmgSubName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);
    testSnFreeUserData(stmgUserData);

    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 025  vertex表，同时指定sub_flow_control为true，status_merge_sub为2
TEST_F(SubFlowControl, SN_058_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char labelConfig[] =
        "{\"max_record_count\":1000000, \"status_merge_sub\":2, \"sub_flow_control\":[800000,900000,1000000]}";
    int ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建老订阅
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subConnSn058Label";
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SnUserDataT *userData;
    ret = testSnMallocUserData(&userData, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName = "subSn058Label";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subSn058LabelJson;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, OldSnCallBack, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    const char *stmgSubName = "stmgSubSn058Label";
    GmcSubConfigT stmgSubInfo;
    stmgSubInfo.subsName = stmgSubName;
    stmgSubInfo.configJson = g_stmgSubInfo;
    SnUserDataT *stmgUserData;
    ret = testSnMallocUserData(&stmgUserData, 1, 0);
    ret = GmcSubscribe(g_stmtSync, &stmgSubInfo, connSub, NewSnCallBack, stmgUserData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_controlSleepLevel = 4, g_isBlock = true;  // 阻塞回调

    // 写入数据，写到队列满的时候，触发流控
    uint32_t indexCount = 0;
    GmcDbFlowCtrlLevelE flowCtrlLevel = GMC_DB_FLOW_CTRL_LEVEL_0;
    while (indexCount < MAX_SN_RING_SIZE) {
        ret = TestInsertTable(g_stmtSync, g_labelName, indexCount, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexCount++;

        ret = GmcGetConnFlowCtrlLevel(g_connSync, &flowCtrlLevel);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (flowCtrlLevel == GMC_DB_FLOW_CTRL_LEVEL_1) {
            AW_FUN_Log(LOG_INFO, "insert record num is: %d, client flowCtrlLevel %d.", indexCount, flowCtrlLevel);
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel, GMC_DB_FLOW_CTRL_LEVEL_0);

    uint32_t timeCount = 0;
    ret = TestUpdateTableWithCostTime(g_stmtSync, g_labelName, 0, 100, &timeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount is: %d.", timeCount);
    EXPECT_LT(timeCount, 800000);

    ret = testWaitStMgSnRecv(stmgUserData, GMC_SUB_EVENT_MODIFY, indexCount, indexCount + 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, stmgSubName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);
    testSnFreeUserData(stmgUserData);

    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 026 vertex表，同时指定sub_flow_control为true，disable_sub_back_pressure为true，不可靠订阅
TEST_F(SubFlowControl, SN_058_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char labelConfig[] =
        "{\"max_record_count\":1000000, \"sub_flow_control\":[800000,900000,1000000], \"disable_sub_back_pressure\":1}";
    int ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建老订阅
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subConnSn058Label";
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SnUserDataT *userData;
    ret = testSnMallocUserData(&userData, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName = "subSn058Label";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subSn058LabelJson;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, OldSnCallBack, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_controlSleepLevel = 4, g_isBlock = true;  // 阻塞回调

    // 写入数据，写到队列满的时候，触发流控
    uint32_t indexCount = 0;
    GmcDbFlowCtrlLevelE flowCtrlLevel = GMC_DB_FLOW_CTRL_LEVEL_0;
    while (indexCount < MAX_SN_RING_SIZE) {
        ret = TestInsertTable(g_stmtSync, g_labelName, indexCount, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexCount++;

        ret = GmcGetConnFlowCtrlLevel(g_connSync, &flowCtrlLevel);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (flowCtrlLevel == GMC_DB_FLOW_CTRL_LEVEL_1) {
            AW_FUN_Log(LOG_INFO, "insert record num is: %d, client flowCtrlLevel %d.", indexCount, flowCtrlLevel);
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel, GMC_DB_FLOW_CTRL_LEVEL_1);

    uint32_t timeCount = 0;
    ret = TestUpdateTableWithCostTime(g_stmtSync, g_labelName, 0, 100, &timeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount is: %d.", timeCount);
    EXPECT_GT(timeCount, 800000);

    g_isBlock = false;  // 放开阻塞
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INSERT, indexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);

    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 027 kv表指定sub_flow_control为true，订阅initial_load
TEST_F(SubFlowControl7, SN_058_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *kvName = "KvTable058";
    const char *kvConfig = "{\"max_record_count\":2000000, \"sub_flow_control\":[800000,900000,1000000]}";
    int ret = GmcKvCreateTable(g_stmtSync, kvName, kvConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    uint32_t indexCount = 0;
    GmcDbFlowCtrlLevelE flowCtrlLevel = GMC_DB_FLOW_CTRL_LEVEL_0;
    while (indexCount < MAX_SN_RING_SIZE - 2) {
        ret = TestSetKv(g_stmtSync, kvName, indexCount, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexCount++;
    }

    // 创建老订阅
    SnUserDataT *userData;
    ret = testSnMallocUserData(&userData, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subKvConn";
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *subName = "subKvInitial";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subKvInitial;
    // 阻塞回调
    g_isKvCbBlock = true;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, KvOldSnCallBack, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t timeCount = 0;
    ret = TestSetKvWithCostTime(g_stmtSync, kvName, indexCount, 100, &timeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount is: %d.", timeCount);
    ret = TestSetKvWithCostTime(g_stmtSync, kvName, indexCount + 1, 100, &timeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount is: %d.", timeCount);
    ret = GmcGetConnFlowCtrlLevel(g_connSync, &flowCtrlLevel);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_isKvCbBlock = false;  // 放开阻塞
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INITIAL_LOAD, indexCount + 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);
    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcKvTruncateTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 028 kv表指定sub_flow_control为true，订阅增量
TEST_F(SubFlowControl, SN_058_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *kvName = "KvTable058";
    const char *kvConfig = "{\"max_record_count\":2000000, \"sub_flow_control\":[800000,900000,1000000]}";
    int ret = GmcKvCreateTable(g_stmtSync, kvName, kvConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建老订阅
    SnUserDataT *userData;
    ret = testSnMallocUserData(&userData, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subKvConn";
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *subName = "subKvAdd";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subKvAdd;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, KvOldSnCallBack, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 阻塞回调
    g_isKvCbBlock = true;

    // 写入数据
    uint32_t indexCount = 0;
    GmcDbFlowCtrlLevelE flowCtrlLevel = GMC_DB_FLOW_CTRL_LEVEL_0;
    while (indexCount < MAX_SN_RING_SIZE) {
        ret = TestSetKv(g_stmtSync, kvName, indexCount, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexCount++;

        ret = GmcGetConnFlowCtrlLevel(g_connSync, &flowCtrlLevel);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (flowCtrlLevel == GMC_DB_FLOW_CTRL_LEVEL_1) {
            AW_FUN_Log(LOG_INFO, "insert record num is: %d, client flowCtrlLevel %d.", indexCount, flowCtrlLevel);
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel, GMC_DB_FLOW_CTRL_LEVEL_1);

    uint32_t timeCount = 0;
    ret = TestSetKvWithCostTime(g_stmtSync, kvName, indexCount, 100, &timeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount is: %d.", timeCount);
    EXPECT_GT(timeCount, 800000);

    g_isKvCbBlock = false;  // 放开阻塞
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_KV_SET, indexCount + 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);
    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcKvTruncateTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 029  同一连接同时订阅kv和vertex，均指定sub_flow_control为true
TEST_F(SubFlowControl, SN_058_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *kvName = "KvTable058";
    const char *kvConfig = "{\"max_record_count\":2000000, \"sub_flow_control\":[800000,900000,1000000]}";
    int ret = GmcKvCreateTable(g_stmtSync, kvName, kvConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char labelConfig[] = "{\"max_record_count\":1000000, \"sub_flow_control\":[800000,900000,1000000]}";
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建老订阅
    SnUserDataT *userData;
    ret = testSnMallocUserData(&userData, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subKvConn";
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *subName = "subKvAdd";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subKvAdd;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, KvOldSnCallBack, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SnUserDataT *userData2;
    ret = testSnMallocUserData(&userData2, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName2 = "subSn058Label";
    subInfo.subsName = subName2;
    subInfo.configJson = g_subSn058LabelJson;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, OldSnCallBack, userData2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // kv表阻塞回调
    g_isKvCbBlock = true;
    uint32_t indexCount = 0;
    GmcDbFlowCtrlLevelE flowCtrlLevel = GMC_DB_FLOW_CTRL_LEVEL_0;
    while (indexCount < MAX_SN_RING_SIZE) {
        ret = TestSetKv(g_stmtSync, kvName, indexCount, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexCount++;

        ret = GmcGetConnFlowCtrlLevel(g_connSync, &flowCtrlLevel);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (flowCtrlLevel == GMC_DB_FLOW_CTRL_LEVEL_1) {
            AW_FUN_Log(LOG_INFO, "insert record num is: %d, client flowCtrlLevel %d.", indexCount, flowCtrlLevel);
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel, GMC_DB_FLOW_CTRL_LEVEL_1);
    uint32_t timeCount = 0;
    ret = TestSetKvWithCostTime(g_stmtSync, kvName, indexCount, 100, &timeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount is: %d.", timeCount);
    EXPECT_GT(timeCount, 800000);

    g_isKvCbBlock = false;  // 放开阻塞
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_KV_SET, indexCount + 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // vertex表阻塞回调
    g_controlSleepLevel = 4, g_isBlock = true;
    uint32_t indexCount2 = 0;
    GmcDbFlowCtrlLevelE flowCtrlLevel2 = GMC_DB_FLOW_CTRL_LEVEL_1;
    while (indexCount2 < MAX_SN_RING_SIZE) {
        ret = TestInsertTable(g_stmtSync, g_labelName, indexCount2, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexCount2++;

        ret = GmcGetConnFlowCtrlLevel(g_connSync, &flowCtrlLevel2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (flowCtrlLevel2 == GMC_DB_FLOW_CTRL_LEVEL_1) {
            AW_FUN_Log(LOG_INFO, "insert record num is: %d, client flowCtrlLevel %d.", indexCount2, flowCtrlLevel2);
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel2, GMC_DB_FLOW_CTRL_LEVEL_1);

    uint32_t timeCount2 = 0;
    ret = TestUpdateTableWithCostTime(g_stmtSync, g_labelName, 0, 100, &timeCount2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount is: %d.", timeCount2);
    EXPECT_GT(timeCount2, 800000);

    g_isBlock = false;  // 放开阻塞
    ret = testWaitSnRecv(userData2, GMC_SUB_EVENT_INSERT, indexCount2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, subName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData2);
    ret = GmcUnSubscribe(g_stmtSync, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);
    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcKvTruncateTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 030  kv表，同时指定sub_flow_control为true，status_merge_sub为2----忽略status_merge_sub配置
TEST_F(SubFlowControl, SN_058_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *kvName = "KvTable058";
    const char *kvConfig =
        "{\"max_record_count\":2000000, \"status_merge_sub\":2, \"sub_flow_control\":[800000,900000,1000000]}";
    int ret = GmcKvCreateTable(g_stmtSync, kvName, kvConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *viewLabelInfo = "V\\$CATA_KV_TABLE_INFO -f BASE_LABEL/LABEL_NAME=KvTable058";
    const char *grepStr = "sub_flow_control";
    system("gmsysview -q V\\$CATA_KV_TABLE_INFO -f BASE_LABEL/LABEL_NAME=KvTable058");
    char cmd[MAX_CMD_SIZE] = {0};
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -s %s -q %s | grep \"%s\"", g_connServer, viewLabelInfo, grepStr);
    ret = executeCommand(cmd, "\"sub_flow_control\":[800000,900000,1000000]");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建老订阅
    SnUserDataT *userData;
    ret = testSnMallocUserData(&userData, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subKvConn";
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *subName = "subKvAdd";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subKvAdd;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, KvOldSnCallBack, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 阻塞回调
    g_isKvCbBlock = true;

    // 写入数据
    uint32_t indexCount = 0;
    GmcDbFlowCtrlLevelE flowCtrlLevel = GMC_DB_FLOW_CTRL_LEVEL_0;
    while (indexCount < MAX_SN_RING_SIZE) {
        ret = TestSetKv(g_stmtSync, kvName, indexCount, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexCount++;

        ret = GmcGetConnFlowCtrlLevel(g_connSync, &flowCtrlLevel);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (flowCtrlLevel == GMC_DB_FLOW_CTRL_LEVEL_1) {
            AW_FUN_Log(LOG_INFO, "insert record num is: %d, client flowCtrlLevel %d.", indexCount, flowCtrlLevel);
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel, GMC_DB_FLOW_CTRL_LEVEL_1);

    uint32_t timeCount = 0;
    ret = TestSetKvWithCostTime(g_stmtSync, kvName, indexCount, 100, &timeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount is: %d.", timeCount);
    EXPECT_GT(timeCount, 800000);

    g_isKvCbBlock = false;  // 放开阻塞
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_KV_SET, indexCount + 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);

    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcKvTruncateTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 031  kv表，同时指定sub_flow_control为true，disable_sub_back_pressure为true，不可靠订阅
TEST_F(SubFlowControl, SN_058_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *kvName = "KvTable058";
    const char *kvConfig = "{\"max_record_count\":2000000, \"disable_sub_back_pressure\":true, "
                           "\"sub_flow_control\":[800000,900000,1000000]}";
    int ret = GmcKvCreateTable(g_stmtSync, kvName, kvConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建老订阅
    SnUserDataT *userData;
    ret = testSnMallocUserData(&userData, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subKvConn";
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *subName = "subKvAdd";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subKvAdd;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, KvOldSnCallBack, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 阻塞回调
    g_isKvCbBlock = true;

    // 写入数据
    uint32_t indexCount = 0;
    GmcDbFlowCtrlLevelE flowCtrlLevel = GMC_DB_FLOW_CTRL_LEVEL_0;
    while (indexCount < MAX_SN_RING_SIZE) {
        ret = TestSetKv(g_stmtSync, kvName, indexCount, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexCount++;

        ret = GmcGetConnFlowCtrlLevel(g_connSync, &flowCtrlLevel);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (flowCtrlLevel == GMC_DB_FLOW_CTRL_LEVEL_1) {
            AW_FUN_Log(LOG_INFO, "insert record num is: %d, client flowCtrlLevel %d.", indexCount, flowCtrlLevel);
            break;
        }
    }
    AW_FUN_Log(LOG_INFO, "g_flowCnt: %d, client flowCtrlLevel %d.", g_flowCnt, flowCtrlLevel);
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel, GMC_DB_FLOW_CTRL_LEVEL_1);

    uint32_t timeCount = 0;
    ret = TestSetKvWithCostTime(g_stmtSync, kvName, indexCount, 100, &timeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount is: %d.", timeCount);
    EXPECT_GT(timeCount, 800000);

    g_isKvCbBlock = false;  // 放开阻塞
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_KV_SET, indexCount + 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);
    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcKvTruncateTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

class SubFlowControl5 : public testing::Test {
public:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/stop.sh -f");
        system("sh $TEST_HOME/tools/modifyCfg.sh \"clientServerFlowControl=1;1;1;1\" "
               "\"overloadThreshold=cpu:70,80,80,85,85,90;dynamicMemory:70,80,80,85,85,90;shareMemory:2,3,80,85,85,90;"
               "subscribeQueue:70,80,80,85,85,90\" ");
        system("${TEST_HOME}/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_controlSleepLevel = 0;
        g_flowCnt = 0;
        g_isCb2Block = false;
        g_isCb3Block = false;
        g_isKvCbBlock = false;
        ret = create_epoll_thread();
        ASSERT_EQ(GMERR_OK, ret);
    }

    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testEnvClean();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_controlSleepLevel = 0;
        g_flowCnt = 0;
        g_isCb2Block = false;
        g_isCb3Block = false;
        g_isKvCbBlock = false;
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        system("sh $TEST_HOME/tools/stop.sh -f");
    }

    virtual void SetUp()
    {
        int ret = testGmcConnect(&g_connSync, &g_stmtSync);
        ASSERT_EQ(GMERR_OK, ret);
        AW_CHECK_LOG_BEGIN();
    }

    virtual void TearDown()
    {
        AW_CHECK_LOG_END();
        int ret = testGmcDisconnect(g_connSync, g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
};

// 035 全局内存，触发流控后，操作配置了sub_flow_control的表，操作未配置的sub_flow_control表
TEST_F(SubFlowControl5, SN_058_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    char labelConfig[] = "{\"max_record_count\":10000000000, \"sub_flow_control\":[800000,900000,1000000]}";
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath2, g_labelName, 0, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *labelName2 = "SN058Label2";
    char labelConfig2[] = "{\"max_record_count\":1000000, \"sub_flow_control\":[400000,500000,600000]}";
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath2, labelName2, 0, labelConfig2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *labelName3 = "SN058Label3";
    char labelConfig3[] = "{\"max_record_count\":1000000, \"sub_flow_control\":[0,0,0]}";
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath2, labelName3, 0, labelConfig3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    uint32_t indexCount = 0;
    GmcDbFlowCtrlLevelE flowCtrlLevel = GMC_DB_FLOW_CTRL_LEVEL_0;
    ret = TestInsertTable2(g_stmtSync, labelName2, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestInsertTable2(g_stmtSync, labelName3, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    while (true) {
        ret = TestInsertTable2(g_stmtSync, g_labelName, indexCount, 0);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "insert record num is: %d, ret %d.", indexCount, ret);
            break;
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexCount++;
        ret = GmcGetConnFlowCtrlLevel(g_connSync, &flowCtrlLevel);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (flowCtrlLevel == GMC_DB_FLOW_CTRL_LEVEL_1) {
            AW_FUN_Log(LOG_INFO, "insert record num is: %d, client flowCtrlLevel %d.", indexCount, flowCtrlLevel);
            break;
        }
    }

    AW_FUN_Log(LOG_INFO, "g_flowCnt: %d, client flowCtrlLevel %d.", g_flowCnt, flowCtrlLevel);
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel, GMC_DB_FLOW_CTRL_LEVEL_1);

    uint32_t timeCount = 0;
    ret = TestUpdateTable2WithCostTime(g_stmtSync, g_labelName, 0, 100, &timeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount is: %d.", timeCount);
    EXPECT_GT(timeCount, 800000);

    uint32_t timeCount2 = 0;
    ret = TestUpdateTable2WithCostTime(g_stmtSync, labelName2, 0, 100, &timeCount2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount is: %d.", timeCount2);
    EXPECT_GT(timeCount2, 400000);

    uint32_t timeCount3 = 0;
    ret = TestUpdateTable2WithCostTime(g_stmtSync, labelName3, 0, 100, &timeCount3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount is: %d.", timeCount3);
    EXPECT_GT(timeCount3, 200);

    // 动态内存：
    system("gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=\"Top Dynamic Memory Context\"");
    // 共享内存：
    system("gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=DbTopSharedMemoryContext");
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, labelName3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 036 表级流控配置sub_flow_control，中配置的二级流控睡眠时间，小于1级流控睡眠时间
TEST_F(SubFlowControl, SN_058_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    char labelConfig[] = "{\"max_record_count\":1000000, \"sub_flow_control\":[900000,800000,1000000]}";
    int ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

class SubFlowControl2 : public testing::Test {
public:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/stop.sh -f");
        system(
            "sh $TEST_HOME/tools/modifyCfg.sh \"clientServerFlowControl=0;0;0;1\" "
            "\"overloadThreshold=cpu:70,80,80,85,85,90;dynamicMemory:70,80,80,85,85,90;shareMemory:70,80,80,85,85,90;"
            "subscribeQueue:70,80,80,85,85,90\" "
            "\"flowControlSleepTime=0,0,0\" ");
        system("${TEST_HOME}/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_controlSleepLevel = 0;
        g_flowCnt = 0;
        g_isCb2Block = false;
        g_isCb3Block = false;
        g_isKvCbBlock = false;
        ret = create_epoll_thread();
        ASSERT_EQ(GMERR_OK, ret);
    }

    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testEnvClean();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_controlSleepLevel = 0;
        g_flowCnt = 0;
        g_isCb2Block = false;
        g_isCb3Block = false;
        g_isKvCbBlock = false;
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        system("sh $TEST_HOME/tools/stop.sh -f");
    }

    virtual void SetUp()
    {
        int ret = testGmcConnect(&g_connSync, &g_stmtSync);
        ASSERT_EQ(GMERR_OK, ret);
        AW_CHECK_LOG_BEGIN();
    }

    virtual void TearDown()
    {
        AW_CHECK_LOG_END();
        int ret = testGmcDisconnect(g_connSync, g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
};

// 037 全局流控配置sleepTime设置为0.0.0，表级流控配置sub_flow_control
TEST_F(SubFlowControl2, SN_058_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    char labelConfig[] = "{\"max_record_count\":1000000, \"sub_flow_control\":[800000,900000,1000000]}";
    int ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

class SubFlowControl3 : public testing::Test {
public:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/stop.sh -f");
        system(
            "sh $TEST_HOME/tools/modifyCfg.sh \"clientServerFlowControl=0;0;0;0\" "
            "\"overloadThreshold=cpu:70,80,80,85,85,90;dynamicMemory:70,80,80,85,85,90;shareMemory:70,80,80,85,85,90;"
            "subscribeQueue:70,80,80,85,85,90\" ");
        system("${TEST_HOME}/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_controlSleepLevel = 0;
        g_flowCnt = 0;
        g_isCb2Block = false;
        g_isCb3Block = false;
        g_isKvCbBlock = false;
        ret = create_epoll_thread();
        ASSERT_EQ(GMERR_OK, ret);
    }

    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testEnvClean();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_controlSleepLevel = 0;
        g_flowCnt = 0;
        g_isCb2Block = false;
        g_isCb3Block = false;
        g_isKvCbBlock = false;
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        system("sh $TEST_HOME/tools/stop.sh -f");
    }

    virtual void SetUp()
    {
        int ret = testGmcConnect(&g_connSync, &g_stmtSync);
        ASSERT_EQ(GMERR_OK, ret);
        AW_CHECK_LOG_BEGIN();
    }

    virtual void TearDown()
    {
        AW_CHECK_LOG_END();
        int ret = testGmcDisconnect(g_connSync, g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
};

// 038 全局流控开关flowCtr关闭，配置表级流控配置sub_flow_control
TEST_F(SubFlowControl3, SN_058_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    char labelConfig[] = "{\"max_record_count\":1000000, \"sub_flow_control\":[800000,900000,1000000]}";
    int ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 039 一个batch操作，批量插入3张表，表1，表2，表3配置的sub_flow_control睡眠时间不同，批操作之后在单写，
TEST_F(SubFlowControl, SN_058_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    for (int i = 0; i < 3; i++) {
        ret = create_epoll_thread(&g_epAsync[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }

    char labelConfig[] = "{\"max_record_count\":1000000, \"sub_flow_control\":[600000,700000,800000]}";
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *labelName2 = "SN058Label2";
    char labelConfig2[] = "{\"max_record_count\":1000000, \"sub_flow_control\":[800000,900000,1000000]}";
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, labelName2, 0, labelConfig2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *labelName3 = "SN058Label3";
    char labelConfig3[] = "{\"max_record_count\":1000000, \"sub_flow_control\":[0,0,0]}";
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, labelName3, 0, labelConfig3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建老订阅
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subConnSn058Label";
    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[0].userEpollFd;
    connOptions.connName = subConnName;
    ret = TestYangGmcConnect(&connSub, &stmtSub, GMC_CONN_TYPE_SUB, &connOptions);
    SnUserDataT *userData;
    ret = testSnMallocUserData(&userData, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName = "subSn058Label";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subSn058LabelJson;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, OldSnCallBack, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *connSub2 = NULL;
    GmcStmtT *stmtSub2 = NULL;
    const char *subConn2Name = "subConn2Sn058Label";
    YangConnOptionT connOptions2 = {0};
    connOptions2.epollFd = &g_epAsync[1].userEpollFd;
    connOptions2.connName = subConn2Name;
    ret = TestYangGmcConnect(&connSub2, &stmtSub2, GMC_CONN_TYPE_SUB, &connOptions2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SnUserDataT *userData2;
    ret = testSnMallocUserData(&userData2, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName2 = "sub2Sn058Label";
    subInfo.subsName = subName2;
    subInfo.configJson = g_subSn058Label2Json;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub2, OldSnCallBack2, userData2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *connSub3 = NULL;
    GmcStmtT *stmtSub3 = NULL;
    const char *subConn3Name = "subConn3Sn058Label";
    YangConnOptionT connOptions3 = {0};
    connOptions3.epollFd = &g_epAsync[2].userEpollFd;
    connOptions3.connName = subConn3Name;
    ret = TestYangGmcConnect(&connSub3, &stmtSub3, GMC_CONN_TYPE_SUB, &connOptions3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SnUserDataT *userData3;
    ret = testSnMallocUserData(&userData3, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName3 = "sub3Sn058Labe";
    subInfo.subsName = subName3;
    subInfo.configJson = g_subSn058Label3Json;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub3, OldSnCallBack3, userData3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 订阅1,2,3都阻塞
    g_controlSleepLevel = 4, g_isBlock = true;
    g_isCb2Block = true;
    g_isCb3Block = true;

    // 写入数据，写到队列满的时候，触发流控
    uint32_t indexCount = 0;
    GmcDbFlowCtrlLevelE flowCtrlLevel = GMC_DB_FLOW_CTRL_LEVEL_0;
    uint32_t timeCount = 0;
    while (indexCount < MAX_SN_RING_SIZE) {
        TestBatchInsertThreeTable(g_connSync, g_stmtSync, indexCount, &timeCount, g_labelName, labelName2, labelName3);
        indexCount++;

        ret = GmcGetConnFlowCtrlLevel(g_connSync, &flowCtrlLevel);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (flowCtrlLevel == GMC_DB_FLOW_CTRL_LEVEL_1) {
            AW_FUN_Log(LOG_INFO, "insert record num is: %d, client flowCtrlLevel %d.", indexCount, flowCtrlLevel);
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel, GMC_DB_FLOW_CTRL_LEVEL_1);
    AW_FUN_Log(LOG_DEBUG, "timeCount is: %d.", timeCount);
    EXPECT_GT(timeCount, 200);
    EXPECT_LT(timeCount, 600000);

    ret = TestUpdateTableWithCostTime(g_stmtSync, g_labelName, 0, 100, &timeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount is: %d.", timeCount);
    EXPECT_GT(timeCount, 600000);
    uint32_t timeCount2 = 0;
    ret = TestUpdateTableWithCostTime(g_stmtSync, labelName2, 0, 100, &timeCount2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount2 is: %d.", timeCount2);
    EXPECT_GT(timeCount2, 800000);
    uint32_t timeCount3 = 0;
    ret = TestUpdateTableWithCostTime(g_stmtSync, labelName3, 0, 100, &timeCount3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount3 is: %d.", timeCount3);
    EXPECT_GT(timeCount3, 200);

    // 订阅1,2,3阻塞放开
    g_isBlock = false;
    g_isCb2Block = false;
    g_isCb3Block = false;
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INSERT, indexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData2, GMC_SUB_EVENT_INSERT, indexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData3, GMC_SUB_EVENT_INSERT, indexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, subName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, subName3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);
    testSnFreeUserData(userData2);
    testSnFreeUserData(userData3);

    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(connSub2, stmtSub3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(connSub3, stmtSub3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, labelName3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, labelName3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 3; i++) {
        ret = close_epoll_thread(&g_epAsync[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

uint32_t g_indexCount = 0;
pthread_mutex_t g_subLock;
void *ThreadFunc1(void *args)
{
    AW_FUN_Log(LOG_STEP, "thread1 dml, begin.");
    int ret = 0;
    GmcConnT *connSync = NULL;
    GmcStmtT *stmtSync = NULL;
    ret = testGmcConnect(&connSync, &stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据，写到队列满的时候，触发流控
    GmcDbFlowCtrlLevelE flowCtrlLevel = GMC_DB_FLOW_CTRL_LEVEL_0;
    uint32_t timeCount = 0;
    const char *labelName2 = "SN058Label2";
    const char *labelName3 = "SN058Label3";
    while (g_indexCount < MAX_SN_RING_SIZE) {

        pthread_mutex_lock(&g_subLock);
        TestBatchInsertThreeTable(connSync, stmtSync, g_indexCount, &timeCount, g_labelName, labelName2, labelName3);
        g_indexCount++;

        ret = GmcGetConnFlowCtrlLevel(connSync, &flowCtrlLevel);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (flowCtrlLevel == GMC_DB_FLOW_CTRL_LEVEL_1) {
            AW_FUN_Log(LOG_INFO, "thread1 insert record num is: %d, client flowCtrlLevel %d， timeCount is %d.",
                g_indexCount, flowCtrlLevel, timeCount);
            pthread_mutex_unlock(&g_subLock);
            break;
        }
        pthread_mutex_unlock(&g_subLock);
    }
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel, GMC_DB_FLOW_CTRL_LEVEL_1);
    TestBatchInsertThreeTable(connSync, stmtSync, 1000001, &timeCount, g_labelName, labelName2, labelName3);
    AW_FUN_Log(LOG_DEBUG, "thread1 timeCount is: %d.", timeCount);
    EXPECT_GT(timeCount, 200);
    EXPECT_LT(timeCount, 600000);

    ret = testGmcDisconnect(connSync, stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "thread1 dml, end.");
    return NULL;
}

void *ThreadFunc2(void *args)
{
    AW_FUN_Log(LOG_STEP, "thread2 dml, begin.");
    int ret = 0;
    GmcConnT *connSync = NULL;
    GmcStmtT *stmtSync = NULL;
    ret = testGmcConnect(&connSync, &stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据，写到队列满的时候，触发流控
    uint32_t indexCount = 0;
    GmcDbFlowCtrlLevelE flowCtrlLevel = GMC_DB_FLOW_CTRL_LEVEL_0;
    uint32_t timeCount = 0;
    const char *labelName2 = "SN058Label2";
    const char *labelName3 = "SN058Label3";
    while (g_indexCount < MAX_SN_RING_SIZE) {

        pthread_mutex_lock(&g_subLock);
        TestBatchInsertThreeTable(connSync, stmtSync, g_indexCount, &timeCount, labelName3, labelName2, g_labelName);
        g_indexCount++;

        ret = GmcGetConnFlowCtrlLevel(connSync, &flowCtrlLevel);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (flowCtrlLevel == GMC_DB_FLOW_CTRL_LEVEL_1) {
            AW_FUN_Log(LOG_INFO, "thread2 insert record num is: %d, client flowCtrlLevel %d， timeCount is %d.",
                g_indexCount, flowCtrlLevel, timeCount);
            pthread_mutex_unlock(&g_subLock);
            break;
        }
        pthread_mutex_unlock(&g_subLock);
    }

    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel, GMC_DB_FLOW_CTRL_LEVEL_1);
    TestBatchInsertThreeTable(connSync, stmtSync, 1000000, &timeCount, labelName3, labelName2, g_labelName);
    AW_FUN_Log(LOG_DEBUG, "thread2 timeCount is: %d.", timeCount);
    EXPECT_GT(timeCount, 600000);

    ret = testGmcDisconnect(connSync, stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "thread2 dml, end.");
    return NULL;
}

// 040 两个线程并发，线程1 batch操作顺序，表1，表2，表3，线程2 batch 操作顺序，表3，表2，表1，
TEST_F(SubFlowControl, SN_058_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    for (int i = 0; i < 3; i++) {
        ret = create_epoll_thread(&g_epAsync[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }

    char labelConfig[] = "{\"max_record_count\":1000000, \"sub_flow_control\":[600000,700000,800000]}";
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *labelName2 = "SN058Label2";
    char labelConfig2[] = "{\"max_record_count\":1000000, \"sub_flow_control\":[800000,900000,1000000]}";
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, labelName2, 0, labelConfig2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *labelName3 = "SN058Label3";
    char labelConfig3[] = "{\"max_record_count\":1000000, \"sub_flow_control\":[0,0,0]}";
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, labelName3, 0, labelConfig3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建老订阅
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subConnSn058Label";
    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[0].userEpollFd;
    connOptions.connName = subConnName;
    ret = TestYangGmcConnect(&connSub, &stmtSub, GMC_CONN_TYPE_SUB, &connOptions);
    SnUserDataT *userData;
    ret = testSnMallocUserData(&userData, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName = "subSn058Label";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subSn058LabelJson;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, OldSnCallBack, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *connSub2 = NULL;
    GmcStmtT *stmtSub2 = NULL;
    const char *subConn2Name = "subConn2Sn058Label";
    YangConnOptionT connOptions2 = {0};
    connOptions2.epollFd = &g_epAsync[1].userEpollFd;
    connOptions2.connName = subConn2Name;
    ret = TestYangGmcConnect(&connSub2, &stmtSub2, GMC_CONN_TYPE_SUB, &connOptions2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SnUserDataT *userData2;
    ret = testSnMallocUserData(&userData2, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName2 = "sub2Sn058Label";
    subInfo.subsName = subName2;
    subInfo.configJson = g_subSn058Label2Json;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub2, OldSnCallBack2, userData2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *connSub3 = NULL;
    GmcStmtT *stmtSub3 = NULL;
    const char *subConn3Name = "subConn3Sn058Label";
    YangConnOptionT connOptions3 = {0};
    connOptions3.epollFd = &g_epAsync[2].userEpollFd;
    connOptions3.connName = subConn3Name;
    ret = TestYangGmcConnect(&connSub3, &stmtSub3, GMC_CONN_TYPE_SUB, &connOptions3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SnUserDataT *userData3;
    ret = testSnMallocUserData(&userData3, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName3 = "sub3Sn058Labe";
    subInfo.subsName = subName3;
    subInfo.configJson = g_subSn058Label3Json;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub3, OldSnCallBack3, userData3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 订阅1,2,3都阻塞
    g_controlSleepLevel = 4, g_isBlock = true;
    g_isCb2Block = true;
    g_isCb3Block = true;

    pthread_t thrDml1;
    ret = pthread_create(&thrDml1, NULL, ThreadFunc1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_t thrDml2;
    ret = pthread_create(&thrDml2, NULL, ThreadFunc2, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = pthread_join(thrDml1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_join(thrDml2, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t timeCount = 0;
    ret = TestUpdateTableWithCostTime(g_stmtSync, g_labelName, 0, 100, &timeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUpdateTableWithCostTime(g_stmtSync, g_labelName, 1, 100, &timeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount is: %d.", timeCount);
    EXPECT_GT(timeCount, 600000);
    uint32_t timeCount2 = 0;
    ret = TestUpdateTableWithCostTime(g_stmtSync, labelName2, 0, 100, &timeCount2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount2 is: %d.", timeCount2);
    EXPECT_GT(timeCount2, 800000);
    uint32_t timeCount3 = 0;
    ret = TestUpdateTableWithCostTime(g_stmtSync, labelName3, 0, 100, &timeCount3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount3 is: %d.", timeCount3);
    EXPECT_GT(timeCount3, 200);

    // 订阅1,2,3阻塞放开
    g_isBlock = false;
    g_isCb2Block = false;
    g_isCb3Block = false;
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INSERT, g_indexCount + 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData2, GMC_SUB_EVENT_INSERT, g_indexCount + 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData3, GMC_SUB_EVENT_INSERT, g_indexCount + 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, subName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, subName3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);
    testSnFreeUserData(userData2);
    testSnFreeUserData(userData3);

    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(connSub2, stmtSub3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(connSub3, stmtSub3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, labelName3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, labelName3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 3; i++) {
        ret = close_epoll_thread(&g_epAsync[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* 034 轻量化事务，存在订阅关系场景，vertex表设置最大记录数，获取GMC_ALARM_SUB_CONN_RING类型告警，failTimes + 1
预期failTimes 不+ 1 (DTS2025040919147)*/
TEST_F(SubFlowControl, SN_058_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_RECORD_COUNT_LIMIT_EXCEEDED);
    char labelConfig[] = "{\"max_record_count\":100}";
    int ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建老订阅
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subConnSn058Label";
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SnUserDataT *userData;
    ret = testSnMallocUserData(&userData, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName = "subSn058Label";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subSn058LabelJson;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, OldSnCallBack, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t before = 0;
    uint64_t after = 0;
    GmcAlarmDataT alarmData;
    memset(&alarmData, 0, sizeof(alarmData));
    ret = GmcGetAlarmData(g_stmtSync, GMC_ALARM_DYM_USED_INFO, &alarmData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    before = alarmData.failTimes;

    // 写入数据，到最大记录数
    uint32_t indexCount = 0;
    GmcDbFlowCtrlLevelE flowCtrlLevel = GMC_DB_FLOW_CTRL_LEVEL_0;
    while (indexCount < 101) {
        ret = TestInsertTable(g_stmtSync, g_labelName, indexCount, 0);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "insert record num is: %d, ret %d.", indexCount, ret);
            break;
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexCount++;
    }
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INSERT, indexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview -q V\\$QRY_DML_OPER_STATIS -f LABEL_NAME=SN058Label");

    memset(&alarmData, 0, sizeof(alarmData));
    ret = GmcGetAlarmData(g_stmtSync, GMC_ALARM_SUB_CONN_RING, &alarmData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    after = alarmData.failTimes;
    AW_FUN_Log(LOG_INFO, "GMC_ALARM_SUB_CONN_RING failTimes before is: %d, after is: %d.", before, after);
    AW_MACRO_EXPECT_EQ_INT(0, after - before);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);

    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

#if defined FEATURE_DATALOG
class SubFlowControlDataLog : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system(
            "sh $TEST_HOME/tools/modifyCfg.sh \"clientServerFlowControl=0;0;0;1\" "
            "\"overloadThreshold=cpu:70,80,80,85,85,90;dynamicMemory:70,80,80,85,85,90;shareMemory:70,80,80,85,85,90;"
            "subscribeQueue:70,80,80,85,85,90\" "
            "\"flowControlSleepTime=1000,5000,1000000\" ");
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    };
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        testEnvClean();
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        system("sh $TEST_HOME/tools/stop.sh -f");
    };

public:
    virtual void SetUp();
    virtual void TearDown();
};

void SubFlowControlDataLog::SetUp()
{
    // 用例执行前卸载残留的so
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());
    // 建连
    int ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(g_stmtSync, g_testNameSpace);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void SubFlowControlDataLog::TearDown()
{
    AW_CHECK_LOG_END();

    // 释放连接
    int ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 032 datalog 外部表配置，配置sub_flow_control为true
TEST_F(SubFlowControlDataLog, SN_058_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = GmcUseNamespace(g_stmtSync, g_testNameSpace);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 先建立有 datalog 表定义的普通外部表订阅
    // 加载json文件
    char labelConfig[] =
        "{\"max_record_count\":1000000, \"isFastReadUncommitted\":false, \"sub_flow_control\":[800000,900000,1000000]}";
    char *schemaJson = NULL;
    readJanssonFile("./schemaFile/out1_dtlEtn.gmjson", &schemaJson);
    EXPECT_NE((void *)NULL, schemaJson);
    GmcDropVertexLabel(g_stmtSync, g_dtlEtn);
    ret = GmcCreateVertexLabel(g_stmtSync, schemaJson, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schemaJson);

    // 加载so文件
    ret = CompileAndLoad(g_fileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 创建老订阅
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subConnSn058";
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SnUserDataT *userData;
    ret = testSnMallocUserData(&userData, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName = "subSn058Out1";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subSn058Out1;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, OldSnCallBack, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_controlSleepLevel = 4, g_isBlock = true;  // 阻塞回调

    // 写入数据，写到队列满的时候，触发流控
    int32_t indexCount = 0;
    GmcDbFlowCtrlLevelE flowCtrlLevel = GMC_DB_FLOW_CTRL_LEVEL_0;
    while (indexCount < MAX_SN_RING_SIZE) {
        ret = TestInsertOut1(g_stmtSync, g_inp1, indexCount, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexCount++;

        ret = GmcGetConnFlowCtrlLevel(g_connSync, &flowCtrlLevel);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (flowCtrlLevel == GMC_DB_FLOW_CTRL_LEVEL_1) {
            AW_FUN_Log(LOG_INFO, "insert record num is: %d, client flowCtrlLevel %d.", indexCount, flowCtrlLevel);
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel, GMC_DB_FLOW_CTRL_LEVEL_1);

    uint32_t timeCount = 0;
    ret = TestUpdateOut1WithCostTime(g_stmtSync, g_dtlEtn, 0, 100, &timeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount is: %d.", timeCount);
    EXPECT_GT(timeCount, 800000);

    // 接收推送
    g_isBlock = false;
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_MERGE_INSERT, indexCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 取消有 datalog 表定义的普通外部表订阅关系并删表
    ret = GmcUnSubscribe(g_stmtSync, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);
    ret = GmcDropVertexLabel(g_stmtSync, g_dtlEtn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(g_fileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 033 DTS2025041510120 datalog 外部表配置，配置sub_flow_control为0
TEST_F(SubFlowControlDataLog, SN_058_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = GmcUseNamespace(g_stmtSync, g_testNameSpace);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 先建立有 datalog 表定义的普通外部表订阅
    // 加载json文件
    char labelConfig[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":false,\"sub_flow_control\":[0,0,0]}";
    char *schemaJson = NULL;
    readJanssonFile("./schemaFile/out1_dtlEtn.gmjson", &schemaJson);
    EXPECT_NE((void *)NULL, schemaJson);
    GmcDropVertexLabel(g_stmtSync, g_dtlEtn);
    ret = GmcCreateVertexLabel(g_stmtSync, schemaJson, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schemaJson);

    // 加载so文件
    ret = CompileAndLoad(g_fileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 创建老订阅
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subConnSn058";
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SnUserDataT *userData;
    ret = testSnMallocUserData(&userData, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName = "subSn058Out1";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subSn058Out1;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, OldSnCallBack, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_controlSleepLevel = 4, g_isBlock = true;  // 阻塞回调

    // 写入数据，写到队列满的时候，触发流控
    int32_t indexCount = 0;
    GmcDbFlowCtrlLevelE flowCtrlLevel = GMC_DB_FLOW_CTRL_LEVEL_0;
    while (indexCount < MAX_SN_RING_SIZE) {
        ret = TestInsertOut1(g_stmtSync, g_inp1, indexCount, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexCount++;

        ret = GmcGetConnFlowCtrlLevel(g_connSync, &flowCtrlLevel);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (flowCtrlLevel == GMC_DB_FLOW_CTRL_LEVEL_1) {
            AW_FUN_Log(LOG_INFO, "insert record num is: %d, client flowCtrlLevel %d.", indexCount, flowCtrlLevel);
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(flowCtrlLevel, GMC_DB_FLOW_CTRL_LEVEL_1);

    uint32_t timeCount = 0;
    ret = TestUpdateOut1WithCostTime(g_stmtSync, g_dtlEtn, 0, 100, &timeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "timeCount is: %d.", timeCount);
    EXPECT_GT(timeCount, 200);

    // 接收推送
    g_isBlock = false;
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_MERGE_INSERT, indexCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 取消有 datalog 表定义的普通外部表订阅关系并删表
    ret = GmcUnSubscribe(g_stmtSync, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);
    ret = GmcDropVertexLabel(g_stmtSync, g_dtlEtn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(g_fileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
#endif

class SubConnFull : public testing::Test {
public:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/stop.sh -f");
        system("sh $TEST_HOME/tools/modifyCfg.sh \"clientServerFlowControl=0;0;0;0\" ");
        system("${TEST_HOME}/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_controlSleepLevel = 0;
        g_flowCnt = 0;
        g_isCb2Block = false;
        g_isCb3Block = false;
        g_isKvCbBlock = false;
        ret = create_epoll_thread();
        ASSERT_EQ(GMERR_OK, ret);
    }

    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testEnvClean();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_controlSleepLevel = 0;
        g_flowCnt = 0;
        g_isCb2Block = false;
        g_isCb3Block = false;
        g_isKvCbBlock = false;
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        system("sh $TEST_HOME/tools/stop.sh -f");
    }

    virtual void SetUp()
    {
        int ret = testGmcConnect(&g_connSync, &g_stmtSync);
        ASSERT_EQ(GMERR_OK, ret);
        AW_CHECK_LOG_BEGIN();
    }

    virtual void TearDown()
    {
        AW_CHECK_LOG_END();
        int ret = testGmcDisconnect(g_connSync, g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
};

/* 041 二级索引删除触发大量订阅消息到订阅通道满场景，打印dfx日志 (DTS2025040919147)*/
TEST_F(SubConnFull, SN_058_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_INSUFFICIENT_RESOURCES);
    int ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入20000
    uint32_t inSertCount = 0;
    while (inSertCount < 20000) {
        ret = TestInsertTable(g_stmtSync, g_labelName, inSertCount, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        inSertCount++;
    }

    // 创建老订阅
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subConnSn058Label";
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SnUserDataT *userData;
    ret = testSnMallocUserData(&userData, 1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subName = "subSn058Label";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subSn058LabelJson;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, OldSnCallBack3, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除数据
    g_isCb3Block = true;  // 阻塞回调
    ret = TestDeleteVertexByPk(g_stmtSync, g_labelName, 0, 20000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_isCb3Block = false;  // 放开阻塞
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_DELETE, MAX_SN_RING_SIZE + 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);

    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验错误日志
    bool retOfLog = AW_CHECK_LOG_EXIST(SERVER, 2, "op: 1", "shouldGenCnt: 1");
    EXPECT_TRUE(retOfLog);
    AW_FUN_Log(LOG_STEP, "test end.");
}
