{"name": "subVertexLabel", "label_name": "OP_T0", "comment": "Old VertexLabel subscription", "schema_version": 0, "type": "before_commit", "events": [{"type": "insert", "msgTypes": ["new object", "old object"]}, {"type": "delete", "msgTypes": ["new object", "old object"]}, {"type": "update", "msgTypes": ["new object", "old object"]}, {"type": "replace insert", "msgTypes": ["new object", "old object"]}, {"type": "replace update", "msgTypes": ["new object", "old object"]}, {"type": "merge insert", "msgTypes": ["new object", "old object"]}, {"type": "merge update", "msgTypes": ["new object", "old object"]}], "is_path": false, "retry": true, "constraint": {"operator_type": "or", "conditions": [{"property": "F4"}]}}