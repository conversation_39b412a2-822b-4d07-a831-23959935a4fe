[{"name": "nhp_std", "fields": [{"name": "nhp_index", "type": "uint32"}, {"name": "next_hop", "type": "uint32"}, {"name": "out_if_index", "type": "uint32"}, {"name": "vr_id", "type": "uint32"}, {"name": "vrf_index", "type": "uint32"}, {"name": "flags", "type": "uint32"}, {"name": "if_type", "type": "uint32"}, {"name": "iid_flags", "type": "uint32"}, {"name": "work_if_index", "type": "uint32"}, {"name": "app_source_id", "type": "uint32"}, {"name": "group_smooth_id", "type": "uint32"}, {"name": "app_obj_id", "type": "uint64"}, {"name": "app_version", "type": "uint32"}, {"name": "attr_flag", "type": "uint32"}, {"name": "fwd_if_type", "type": "uint16"}, {"name": "reserved", "type": "uint16"}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "nhp_std", "fields": ["nhp_index", "next_hop", "out_if_index"], "constraints": {"unique": true}}]}]