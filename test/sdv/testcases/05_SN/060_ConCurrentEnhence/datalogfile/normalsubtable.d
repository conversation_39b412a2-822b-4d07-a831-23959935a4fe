%table inp1(a41:int8, a42:int8, b41:uint8, c31:uint2_16,  c71:uint8_32, c81:uint8_64, d71:byte128, e11:str, f11:byte){index(0(a41, a42)), index(1(b41)), update, status_merge_sub(true)}
%table mid2(a41:int8, a42:int8, b41:uint8, c31:uint2_16, c71:uint8_32, c81:uint8_64, d71:byte128, e11:str, f11:byte){index(0(a41, a42)), status_merge_sub(true)}
%table out3(a41:int8, a42:int8, b41:uint8, c31:uint2_16,  c71:uint8_32, c81:uint8_64, d71:byte128, e11:str, f11:byte){index(0(a41, a42)), external}

mid2(a41, a42, b41, c31, c71, c81, d71, e11, f11) :- inp1(a41, a42, b41, c31, c71, c81, d71, e11, f11).

out3(a41, a42, b41, c31, c71, c81, d71, e11, f11) :- mid2(a41, a42, b41, c31, c71, c81, d71, e11, f11).
