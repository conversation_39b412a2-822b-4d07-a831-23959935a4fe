[{"type": "record", "name": "inp1", "fields": [{"name": "dtlReservedCount", "type": "int32"}, {"name": "upgradeVersion", "type": "int32"}, {"name": "a41", "type": "int64", "nullable": false}, {"name": "a42", "type": "int64", "nullable": false}, {"name": "b41", "type": "uint64", "nullable": false}, {"name": "c31", "type": "uint16:16", "nullable": false}, {"name": "c71", "type": "uint64:32", "nullable": false}, {"name": "c81", "type": "uint64:64", "nullable": false}, {"name": "d71", "type": "fixed", "nullable": false, "size": 128}, {"name": "e11", "type": "string", "nullable": false, "size": 100}, {"name": "f11", "type": "bytes", "nullable": false, "size": 100}], "keys": [{"node": "inp1", "name": "PK", "fields": ["upgradeVersion", "a41", "a42"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]