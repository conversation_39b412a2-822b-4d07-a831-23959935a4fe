/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: tbm_udf
 * Author: qibingsen 00880292
 * Create: 2024-12-23
 */

#include "gm_udf.h"
#include "stdio.h"
#include "unistd.h"

#pragma pack(1)

typedef struct Func {
    int32_t dtlReservedCount;
    int64_t a;
    int64_t b;
    int64_t c;
    int64_t d;
} Func;

#pragma pack(0)

const char *g_logName = "/root/_datalog_/TbmRunLog.txt";

int32_t dtl_ext_func_funcmid1(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b - f->c;
    return GMERR_OK;
}
