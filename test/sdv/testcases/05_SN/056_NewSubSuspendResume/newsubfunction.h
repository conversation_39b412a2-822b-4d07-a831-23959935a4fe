/*  版权所有 (c) 华为技术有限公司 2025-2025 */

#ifndef __NEWSUBFUNCTION_H__
#define __NEWSUBFUNCTION_H__
extern "C" {
}

#include "t_datacom_lite.h"
#include "gmc_dlr.h"
#include "cmplxTblTools.h"
#include "special_complex_table_tools.h"

#define MAX_NAME_LENGTH 512
#define MAX_FILE_PATH 512
#define MULTI_THREAD_NUM 128
#define LOG_MAX_SIZE_OF_ERROR_MSG 128
#define MAX_SUB_NUM 1024
#define FILENOTEXIST (-1)
#define VIEWNOTFIND (-1)
#define CMDOUTNULL (-1)
#define MAX_LOOP_NUM 100

/**-------------------存储 DLR buf-----------------------**/
#define DLR_BUF_SIZE 500
#define DLR_BUF_LEN 500

GmcConnT *g_connSub = NULL, *g_conn_replay_nsp1 = NULL, *g_conn_replay_nsp2 = NULL;
GmcStmtT *g_stmtSub = NULL, *g_stmt_replay_nsp1 = NULL, *g_stmt_replay_nsp2 = NULL;

char g_namespace1[MAX_NAMESPACE_LENGTH] = "nsp1";
char g_namespace2[MAX_NAMESPACE_LENGTH] = "nsp2";
char g_namespace[MAX_NAMESPACE_LENGTH] = "public";
char g_user[MAX_NAMESPACE_LENGTH] = "user";
char g_inp[MAX_NAME_LENGTH] = "inp";
char g_inp1[MAX_NAME_LENGTH] = "dnsp1.inp1";
char g_mid1[MAX_NAME_LENGTH] = "dnsp1.mid1";
char g_out1[MAX_NAME_LENGTH] = "dnsp1.out1";
char g_inp2[MAX_NAME_LENGTH] = "dnsp2.inp2";
char g_mid2[MAX_NAME_LENGTH] = "dnsp2.mid2";
char g_out2[MAX_NAME_LENGTH] = "out2";
char g_inp3[MAX_NAME_LENGTH] = "dnsp3.inp3";
char g_inp4[MAX_NAME_LENGTH] = "dnsp4.inp4";
char g_out3[MAX_NAME_LENGTH] = "out3";
char g_subConnName[MAX_NAME_LENGTH] = "subConnName";
int32_t g_countcallback = 0;
uint32_t g_fetchNum = 1;
bool g_notjumpFlag = true;

typedef int (*FuncWrite)(GmcStmtT *stmt, void *t);
typedef int (*FuncDelete)(GmcStmtT *stmt, void *t);
typedef int (*FuncUpdate)(GmcStmtT *stmt, void *t);
typedef int (*FuncRead)(GmcStmtT *stmt, void *t);
typedef int (*FuncRead1)(GmcStmtT *stmt, uint8_t *dataCheckIndexes);

typedef struct DlrReplayData {
    char *replaytablename;
    char *userBuf;
    uint32_t bufSize;
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    GmcConnT *subConn;
    int32_t countcallback;
    int32_t sendEof = 1;
} DlrReplayDataT;

typedef struct TaglabelCfg {
    GmcOperationTypeE opType;
    char *labelName;
    int32_t writeCount;  // 主键或其他非成员索引的数量
    bool isBatch;
    bool isStruct;
    char *namespaceName = g_namespace1;
} GtlabelCfgT;

typedef struct TableStruct {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int64_t a;
    int64_t b;
    int64_t c;
} TableStructT;

typedef struct RscTableStruct {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int64_t a;
    int64_t b;
    int64_t c;
    int32_t d;
} RscTableStructT;

typedef struct WriteTableCfg {
    char *localnamespace = NULL;
    FuncWrite funcWrite = NULL;
    TableStructT *objIn;
    GtlabelCfgT vertexCfg = {};
} WriteTableCfgT;

typedef struct TagSnUserDataWithFuncT {
    SnUserDataT *data;
    FuncRead1 func;
    bool isPubsubRsc;
} SnUserDataWithFuncT;

int InterfaceLoadDatalog(GmcStmtT *stmt, const char *soFile, const char *nspName = NULL, bool isDistribute = false)
{
    GmcImportDatalogOptionsT *options = NULL;
    int ret = GmcImportDatalogOptionsCreate(&options);
    RETURN_IFERR(ret);
    ret = GmcImportDatalogOptionsSetFilePath(options, soFile);
    RETURN_IFERR(ret);
    if (nspName != NULL) {
        ret = GmcImportDatalogOptionsSetNamespaceName(options, nspName);
        RETURN_IFERR(ret);
    }
    if (isDistribute) {
        ret = GmcImportDatalogOptionsSetIsDistribute(options);
        RETURN_IFERR(ret);
    }
    ret = GmcImportDatalogWithOption(stmt, options);
    RETURN_IFERR(ret);
    GmcImportDatalogOptionsDestroy(options);
    return ret;
}

int InterfaceUnLoadDatalog(GmcStmtT *stmt, const char *soName, const char *nspName = NULL)
{
    GmcUnimportDatalogOptionsT *options = NULL;
    int ret = GmcUnimportDatalogOptionsCreate(&options);
    RETURN_IFERR(ret);
    ret = GmcUnimportDatalogOptionsSetSoName(options, soName);
    RETURN_IFERR(ret);
    if (nspName != NULL) {
        ret = GmcUnimportDatalogOptionsSetNamespaceName(options, nspName);
        RETURN_IFERR(ret);
    }
    ret = GmcUnimportDatalogWithOption(stmt, options);
    RETURN_IFERR(ret);
    GmcUnimportDatalogOptionsDestroy(options);
    return ret;
}

int createDlrSubscription(GmcStmtT *stmtSync, GmcConnT *connSub, char *subJsonPath, DlrReplayData *subData,
                          uint32_t mallocCount, const char *subsName, GmcSubCallbackT userCb)
{
    char *subInfo = NULL;
    readJanssonFile(subJsonPath, &subInfo);
    AW_MACRO_EXPECT_NOTNULL(subInfo);
    GmcSubConfigT tmpSubInfo;
    tmpSubInfo.subsName = subsName;
    tmpSubInfo.configJson = subInfo;
    int ret = GmcSubscribe(stmtSync, &tmpSubInfo, connSub, userCb, subData);
    RETURN_IFERR(ret);
    free(subInfo);
    return GMERR_OK;
}

int createDlrSubscription_new1(GmcStmtT *stmtSync, GmcConnT *connSub, char *subJson, DlrReplayData *subData,
                               uint32_t mallocCount, const char *subsName, GmcSubCallbackT userCb)
{
    AW_MACRO_EXPECT_NOTNULL(subJson);
    GmcSubConfigT tmpSubInfo;
    tmpSubInfo.subsName = subsName;
    tmpSubInfo.configJson = subJson;
    int ret = GmcSubscribe(stmtSync, &tmpSubInfo, connSub, userCb, NULL);
    RETURN_IFERR(ret);
    return GMERR_OK;
}

int cancelDlrSubscription(GmcStmtT *stmtSync, const char *subsName)
{
    int ret = GmcUnSubscribe(stmtSync, subsName);
    RETURN_IFERR(ret);
    return GMERR_OK;
}

int cancelDlrSubscription_new1(GmcStmtT *stmtSync, const char *subsName)
{
    int ret = GmcUnSubscribe(stmtSync, subsName);
    RETURN_IFERR(ret);
    return GMERR_OK;
}

int LoadExternalVertex(GmcStmtT *stmt)
{
    char *schema = NULL;
    GmcDropVertexLabel(stmt, "out2");
    readJanssonFile("datalogfile/SN_056_001_003.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    const char *configJson = "{\"max_record_count\":10000, \"isFastReadUncommitted\":0, \"status_merge_sub\":true}";
    int ret = GmcCreateVertexLabel(stmt, schema, configJson);
    free(schema);
    return ret;
}

int DropExternalVertex(GmcStmtT *stmt)
{
    int ret = GmcDropVertexLabel(stmt, "out2");
    return ret;
}

int LoadVertexLabel(GmcStmtT *stmt, const char *labelName, const char *labelconfigJsonpath, bool is_merge_sub = false)
{
    char *schema = NULL;
    GmcDropVertexLabel(stmt, labelName);
    readJanssonFile(labelconfigJsonpath, &schema);
    EXPECT_NE((void *)NULL, schema);
    char configJson[MAX_NAME_LENGTH] = { 0 };
    if (is_merge_sub) {
        (void)snprintf(configJson, MAX_NAME_LENGTH,
                       "{\"max_record_count\":10000, \"isFastReadUncommitted\":0, \"data_sync_label\":true, "
                       "\"status_merge_sub\":true}");
    } else {
        (void)snprintf(configJson, MAX_NAME_LENGTH,
                       "{\"max_record_count\":10000, \"isFastReadUncommitted\":0, \"data_sync_label\":true}");
    }
    int ret = GmcCreateVertexLabel(stmt, schema, configJson);
    free(schema);
    return ret;
}

int DropVertexLabel(GmcStmtT *stmt, const char *labelName)
{
    int ret = GmcDropVertexLabel(stmt, labelName);
    return ret;
}

int createSubscription(GmcStmtT *stmtSync, GmcConnT *connSub, char *subJsonPath, SnUserDataWithFuncT *userData,
                       uint32_t mallocCount, const char *subsName, GmcSubCallbackT userCb, FuncRead1 func)
{
    char *subInfo = NULL;
    readJanssonFile(subJsonPath, &subInfo);
    AW_MACRO_EXPECT_NOTNULL(subInfo);

    int ret = testSnMallocUserData(&userData->data, mallocCount * 2, mallocCount * 2);
    RETURN_IFERR(ret);
    userData->func = func;
    userData->isPubsubRsc = true;

    GmcSubConfigT tmpSubInfo;
    tmpSubInfo.subsName = subsName;
    tmpSubInfo.configJson = subInfo;
    ret = GmcSubscribe(stmtSync, &tmpSubInfo, connSub, userCb, userData);
    free(subInfo);
    RETURN_IFERR(ret);
    return ret;
}
int cancelSubscription(GmcStmtT *stmtSync, const char *subsName, SnUserDataWithFuncT *userData)
{
    int ret = GmcUnSubscribe(stmtSync, subsName);
    RETURN_IFERR(ret);

    testSnFreeUserData(userData->data);
    return GMERR_OK;
}

void snCallback(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    SnUserDataWithFuncT *userDefinedData = (SnUserDataWithFuncT *)userData;
    SnUserDataT *data = userDefinedData->data;
    FuncRead1 func = userDefinedData->func;
    AW_MACRO_EXPECT_EQ_INT(1, info->labelCount);

    int ret;
    int pk, i;
    SnUserDataT *user_data = (SnUserDataT *)data;
    char labelName[MAX_NAME_LENGTH] = { 0 };
    unsigned int labelNameLen = MAX_NAME_LENGTH;

    user_data->callbackTimes++;
    AW_MACRO_EXPECT_EQ_INT(1, info->labelCount);
    bool eof = false;
    while (!eof) {
        printf("in snCallback, eventType is %d\n", info->eventType);
        if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
            user_data->scanEofNum++;
            break;
        } else {
            ret = GmcFetch(subStmt, &eof);
            if (ret != GMERR_OK) {
                AW_FUN_Log(LOG_INFO, "NewVersion GMC_SUB_EVENT_INITIAL_LOAD_EOF Abnormal\n");
                break;
            } else if (eof == true) {
                break;
            }
        }
        for (i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(strlen(labelName), labelNameLen);
            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = func(subStmt, user_data->dataCheckIndexes);
                    EXPECT_EQ(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = func(subStmt, user_data->dataCheckIndexes);
                    EXPECT_EQ(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_UPDATE: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = func(subStmt, user_data->dataCheckIndexes);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = func(subStmt, user_data->dataCheckIndexes);
                    EXPECT_EQ(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_MERGE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = func(subStmt, user_data->dataCheckIndexes);
                    EXPECT_EQ(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_MODIFY: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_INITIAL_LOAD: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_AGED: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    break;
                }
                default: {
                    printf("default: invalid eventType\r\n");
                    break;
                }
            }
        }
        user_data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE: {
                user_data->mergeNum++;
                break;
            }
            case GMC_SUB_EVENT_MODIFY: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
            default: {
                printf("default: invalid eventType\r\n");
                break;
            }
        }
    }
}

int ExternaltableGet(GmcStmtT *stmt, uint8_t *dataCheckIndexes)
{
    int64_t a = 0;
    bool isNull = true;
    int ret = GmcGetVertexPropertyByName(stmt, "a", &a, sizeof(int64_t), &isNull);
    RETURN_IFERR(ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);

    int64_t b = a;
    int64_t c = a;

    ret = queryPropertyAndCompare(stmt, "b", GMC_DATATYPE_INT64, &b);
    RETURN_IFERR(ret);
    ret = queryPropertyAndCompare(stmt, "c", GMC_DATATYPE_INT64, &c);
    RETURN_IFERR(ret);

    dataCheckIndexes[a]++;
    return ret;
}

int CreateReplayData(DlrReplayDataT *replayData, char *replaytablename, GmcConnT *conn, GmcStmtT *stmt,
                     GmcConnT *Subconn)
{
    uint32_t bufSize = 1024;
    char *userBuf = (char *)malloc(sizeof(char) * bufSize);
    if (userBuf == NULL) {  // 符合: 对返回值进行合法性检查
        AW_FUN_Log(LOG_INFO, "malloc failed");
    }
    replayData->userBuf = userBuf;
    replayData->replaytablename = replaytablename;
    replayData->syncConn = conn;
    replayData->syncStmt = stmt;
    replayData->subConn = Subconn;
    replayData->bufSize = bufSize;
}

void sn_callback_new(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *subDataTest)
{
    int ret = 0;
    DlrReplayData *replayData = (DlrReplayData *)subDataTest;

    if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
        return;
    }
    bool getEof = false;
    bool isEof = false;
    GmcStmtT *syncStmt = replayData->syncStmt;
    while (!getEof) {
        GmcDlrDataBufT dlrBuf;
        (void)memset_s(replayData->userBuf, replayData->bufSize, 0u, replayData->bufSize);
        ret = GmcDlrDataBufInit(&dlrBuf, replayData->userBuf, replayData->bufSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetDlrDataBufAttr(&dlrBuf, GMC_DLR_MAX_FETCH_NUM, &g_fetchNum, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcGetSubDlrDataBuf(subStmt, info, &dlrBuf, &getEof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        GmcBatchOptionT batchOption;
        ret = GmcBatchOptionInit(&batchOption);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_NORMAL);  // GMC_BATCH_NORMAL
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcBatchT *batch = NULL;
        GmcBatchRetT batchRet;
        // prepare
        ret = GmcBatchPrepare(replayData->syncConn, &batchOption, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(syncStmt, replayData->replaytablename, GMC_OPERATION_REPLAY);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool setEof = false;
        ret = GmcSetDlrDataBuf(syncStmt, batch, &dlrBuf, &setEof);
        EXPECT_TRUE(setEof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchExecute(batch, &batchRet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (ret != 0) {
            testGmcGetLastError(NULL);
        }
        GmcBatchDestroy(batch);
        replayData->countcallback++;
    }
    if (replayData->sendEof == 0) {
        replayData->sendEof = 0;
    } else {
        replayData->sendEof--;
    }
}

void sn_callback_suspend(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *subDataTest)
{
    int ret = 0;
    DlrReplayData *replayData = (DlrReplayData *)subDataTest;

    if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
        printf("replayData->countcallback is %d\n", replayData->countcallback);
        printf("sn_callback_suspend GMC_SUB_EVENT_INITIAL_LOAD_EOF\n");
        return;
    }
    bool getEof = false;
    bool isEof = false;
    GmcStmtT *syncStmt = replayData->syncStmt;
    g_fetchNum = 1;  // 单次fetch数据最大100条
    while (!getEof) {
        GmcDlrDataBufT dlrBuf;
        (void)memset_s(replayData->userBuf, replayData->bufSize, 0u, replayData->bufSize);
        ret = GmcDlrDataBufInit(&dlrBuf, replayData->userBuf, replayData->bufSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetDlrDataBufAttr(&dlrBuf, GMC_DLR_MAX_FETCH_NUM, &g_fetchNum, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcGetSubDlrDataBuf(subStmt, info, &dlrBuf, &getEof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcBatchOptionT batchOption;
        ret = GmcBatchOptionInit(&batchOption);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_NORMAL);  // GMC_BATCH_NORMAL
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcBatchT *batch = NULL;
        GmcBatchRetT batchRet;
        // prepare
        ret = GmcBatchPrepare(replayData->syncConn, &batchOption, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(syncStmt, replayData->replaytablename, GMC_OPERATION_REPLAY);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool setEof = false;
        ret = GmcSetDlrDataBuf(syncStmt, batch, &dlrBuf, &setEof);
        EXPECT_TRUE(setEof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchExecute(batch, &batchRet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (ret != 0) {
            testGmcGetLastError(NULL);
        }
        GmcBatchDestroy(batch);
        replayData->countcallback++;
        if (replayData->countcallback % 90 == 0 && g_notjumpFlag) {
            ret = GmcStmgConnSuspend(replayData->subConn);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            printf("this the %d times callback, suspend conn\n", replayData->countcallback);
            replayData->sendEof++;
            break;
        }
    }
    if (replayData->sendEof == 0) {
        replayData->sendEof = 0;
    } else {
        replayData->sendEof--;
    }
}

int32_t g_suspendcount = 0;
void sn_callback_suspend_count(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *subDataTest)
{
    int ret = 0;
    DlrReplayData *replayData = (DlrReplayData *)subDataTest;

    if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
        printf("replayData->countcallback is %d\n", replayData->countcallback);
        printf("sn_callback_suspend GMC_SUB_EVENT_INITIAL_LOAD_EOF\n");
        return;
    }
    bool getEof = false;
    bool isEof = false;
    GmcStmtT *syncStmt = replayData->syncStmt;
    g_fetchNum = 1;  // 单次fetch数据最大100条
    while (!getEof) {
        GmcDlrDataBufT dlrBuf;
        (void)memset_s(replayData->userBuf, replayData->bufSize, 0u, replayData->bufSize);
        ret = GmcDlrDataBufInit(&dlrBuf, replayData->userBuf, replayData->bufSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetDlrDataBufAttr(&dlrBuf, GMC_DLR_MAX_FETCH_NUM, &g_fetchNum, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcGetSubDlrDataBuf(subStmt, info, &dlrBuf, &getEof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcBatchOptionT batchOption;
        ret = GmcBatchOptionInit(&batchOption);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_NORMAL);  // GMC_BATCH_NORMAL
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcBatchT *batch = NULL;
        GmcBatchRetT batchRet;
        // prepare
        ret = GmcBatchPrepare(replayData->syncConn, &batchOption, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(syncStmt, replayData->replaytablename, GMC_OPERATION_REPLAY);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool setEof = false;
        ret = GmcSetDlrDataBuf(syncStmt, batch, &dlrBuf, &setEof);
        EXPECT_TRUE(setEof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchExecute(batch, &batchRet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (ret != 0) {
            testGmcGetLastError(NULL);
        }
        GmcBatchDestroy(batch);
        replayData->countcallback++;
        if (replayData->countcallback % 90 == 0 && g_notjumpFlag) {
            ret = GmcStmgConnSuspend(replayData->subConn);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            printf("this the %d times callback, suspend conn\n", replayData->countcallback);
            replayData->sendEof++;
            g_suspendcount++;
            printf("g_suspendcount is %d\n", g_suspendcount);
            break;
        }
    }
    if (replayData->sendEof == 0) {
        replayData->sendEof = 0;
    } else {
        replayData->sendEof--;
    }
}

int32_t g_waittimes = 20;
bool g_suspend1_1 = false;
bool g_suspend1 = true;
void sn_callback_suspend1(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *subDataTest)
{
    int ret = 0;
    DlrReplayData *replayData = (DlrReplayData *)subDataTest;

    if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
        printf("replayData->countcallback is %d\n", replayData->countcallback);
        printf("sn_callback_suspend GMC_SUB_EVENT_INITIAL_LOAD_EOF\n");
        return;
    }
    bool getEof = false;
    bool isEof = false;
    GmcStmtT *syncStmt = replayData->syncStmt;
    g_fetchNum = 1;  // 单次fetch数据最大100条
    while (!getEof) {
        GmcDlrDataBufT dlrBuf;
        (void)memset_s(replayData->userBuf, replayData->bufSize, 0u, replayData->bufSize);
        ret = GmcDlrDataBufInit(&dlrBuf, replayData->userBuf, replayData->bufSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetDlrDataBufAttr(&dlrBuf, GMC_DLR_MAX_FETCH_NUM, &g_fetchNum, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcGetSubDlrDataBuf(subStmt, info, &dlrBuf, &getEof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcBatchOptionT batchOption;
        ret = GmcBatchOptionInit(&batchOption);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_NORMAL);  // GMC_BATCH_NORMAL
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcBatchT *batch = NULL;
        GmcBatchRetT batchRet;
        // prepare
        ret = GmcBatchPrepare(replayData->syncConn, &batchOption, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(syncStmt, replayData->replaytablename, GMC_OPERATION_REPLAY);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool setEof = false;
        ret = GmcSetDlrDataBuf(syncStmt, batch, &dlrBuf, &setEof);
        EXPECT_TRUE(setEof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchExecute(batch, &batchRet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (ret != 0) {
            testGmcGetLastError(NULL);
        }
        GmcBatchDestroy(batch);
        replayData->countcallback++;
        while (g_suspend1_1 && g_waittimes <= replayData->countcallback) {
        }
        if (replayData->countcallback % g_waittimes == 0 && g_suspend1) {
            ret = GmcStmgConnSuspend(replayData->subConn);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            printf("this the %d times callback, suspend conn\n", replayData->countcallback);
            replayData->sendEof++;
            break;
        }
    }
    if (replayData->sendEof == 0) {
        replayData->sendEof = 0;
    } else {
        replayData->sendEof--;
    }
}

bool g_suspend2_1 = true;
bool g_suspend2 = false;
void sn_callback_suspend2(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *subDataTest)
{
    int ret = 0;
    DlrReplayData *replayData = (DlrReplayData *)subDataTest;

    if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
        printf("replayData->countcallback is %d\n", replayData->countcallback);
        printf("sn_callback_suspend GMC_SUB_EVENT_INITIAL_LOAD_EOF\n");
        return;
    }
    bool getEof = false;
    bool isEof = false;
    GmcStmtT *syncStmt = replayData->syncStmt;
    g_fetchNum = 1;  // 单次fetch数据最大100条
    while (!getEof) {
        GmcDlrDataBufT dlrBuf;
        (void)memset_s(replayData->userBuf, replayData->bufSize, 0u, replayData->bufSize);
        ret = GmcDlrDataBufInit(&dlrBuf, replayData->userBuf, replayData->bufSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetDlrDataBufAttr(&dlrBuf, GMC_DLR_MAX_FETCH_NUM, &g_fetchNum, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcGetSubDlrDataBuf(subStmt, info, &dlrBuf, &getEof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcBatchOptionT batchOption;
        ret = GmcBatchOptionInit(&batchOption);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_NORMAL);  // GMC_BATCH_NORMAL
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcBatchT *batch = NULL;
        GmcBatchRetT batchRet;
        // prepare
        ret = GmcBatchPrepare(replayData->syncConn, &batchOption, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(syncStmt, replayData->replaytablename, GMC_OPERATION_REPLAY);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool setEof = false;
        ret = GmcSetDlrDataBuf(syncStmt, batch, &dlrBuf, &setEof);
        EXPECT_TRUE(setEof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchExecute(batch, &batchRet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (ret != 0) {
            testGmcGetLastError(NULL);
        }
        GmcBatchDestroy(batch);
        replayData->countcallback++;
        while (g_suspend2_1 && g_waittimes <= replayData->countcallback) {
        }
        if (replayData->countcallback % g_waittimes == 0 && g_suspend2) {
            ret = GmcStmgConnSuspend(replayData->subConn);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            printf("this the %d times callback, suspend conn\n", replayData->countcallback);
            replayData->sendEof++;
            break;
        }
    }
    if (replayData->sendEof == 0) {
        replayData->sendEof = 0;
    } else {
        replayData->sendEof--;
    }
}

bool g_suspend3_1 = true;
bool g_suspend3 = false;
void sn_callback_suspend3(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *subDataTest)
{
    int ret = 0;
    DlrReplayData *replayData = (DlrReplayData *)subDataTest;

    if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
        printf("replayData->countcallback is %d\n", replayData->countcallback);
        printf("sn_callback_suspend GMC_SUB_EVENT_INITIAL_LOAD_EOF\n");
        return;
    }
    bool getEof = false;
    bool isEof = false;
    GmcStmtT *syncStmt = replayData->syncStmt;
    g_fetchNum = 1;  // 单次fetch数据最大100条
    while (!getEof) {
        GmcDlrDataBufT dlrBuf;
        (void)memset_s(replayData->userBuf, replayData->bufSize, 0u, replayData->bufSize);
        ret = GmcDlrDataBufInit(&dlrBuf, replayData->userBuf, replayData->bufSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetDlrDataBufAttr(&dlrBuf, GMC_DLR_MAX_FETCH_NUM, &g_fetchNum, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcGetSubDlrDataBuf(subStmt, info, &dlrBuf, &getEof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcBatchOptionT batchOption;
        ret = GmcBatchOptionInit(&batchOption);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_NORMAL);  // GMC_BATCH_NORMAL
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcBatchT *batch = NULL;
        GmcBatchRetT batchRet;
        // prepare
        ret = GmcBatchPrepare(replayData->syncConn, &batchOption, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(syncStmt, replayData->replaytablename, GMC_OPERATION_REPLAY);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool setEof = false;
        ret = GmcSetDlrDataBuf(syncStmt, batch, &dlrBuf, &setEof);
        EXPECT_TRUE(setEof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchExecute(batch, &batchRet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (ret != 0) {
            testGmcGetLastError(NULL);
        }
        GmcBatchDestroy(batch);
        replayData->countcallback++;
        while (g_suspend3_1 && g_waittimes <= replayData->countcallback) {
        }
        if (replayData->countcallback % g_waittimes == 0 && g_suspend3) {
            ret = GmcStmgConnSuspend(replayData->subConn);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            printf("this the %d times callback, suspend conn\n", replayData->countcallback);
            replayData->sendEof++;
            break;
        }
    }
    if (replayData->sendEof == 0) {
        replayData->sendEof = 0;
    } else {
        replayData->sendEof--;
    }
}

void sn_callback_suspend_more(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *subDataTest)
{
    int ret = 0;
    DlrReplayData *replayData = (DlrReplayData *)subDataTest;

    if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
        printf("replayData->countcallback is %d\n", replayData->countcallback);
        printf("sn_callback_suspend GMC_SUB_EVENT_INITIAL_LOAD_EOF\n");
        return;
    }
    bool getEof = false;
    bool isEof = false;
    GmcStmtT *syncStmt = replayData->syncStmt;
    while (!getEof) {
        GmcDlrDataBufT dlrBuf;
        (void)memset_s(replayData->userBuf, replayData->bufSize, 0u, replayData->bufSize);
        ret = GmcDlrDataBufInit(&dlrBuf, replayData->userBuf, replayData->bufSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetDlrDataBufAttr(&dlrBuf, GMC_DLR_MAX_FETCH_NUM, &g_fetchNum, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcGetSubDlrDataBuf(subStmt, info, &dlrBuf, &getEof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcBatchOptionT batchOption;
        ret = GmcBatchOptionInit(&batchOption);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_NORMAL);  // GMC_BATCH_NORMAL
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcBatchT *batch = NULL;
        GmcBatchRetT batchRet;
        // prepare
        ret = GmcBatchPrepare(replayData->syncConn, &batchOption, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(syncStmt, replayData->replaytablename, GMC_OPERATION_REPLAY);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool setEof = false;
        ret = GmcSetDlrDataBuf(syncStmt, batch, &dlrBuf, &setEof);
        EXPECT_TRUE(setEof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchExecute(batch, &batchRet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (ret != 0) {
            testGmcGetLastError(NULL);
        }
        GmcBatchDestroy(batch);
        replayData->countcallback++;
        if (replayData->countcallback % (10 / g_fetchNum * 3) == 0 && g_notjumpFlag) {
            ret = GmcStmgConnSuspend(replayData->subConn);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            printf("this the %d times callback, suspend conn\n", replayData->countcallback);
            replayData->sendEof++;
            break;
        }
    }
    if (replayData->sendEof == 0) {
        replayData->sendEof = 0;
    } else {
        replayData->sendEof--;
    }
}

void sn_callback_suspend_more1024(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *subDataTest)
{
    int ret = 0;
    DlrReplayData *replayData = (DlrReplayData *)subDataTest;

    if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
        printf("replayData->countcallback is %d\n", replayData->countcallback);
        printf("sn_callback_suspend GMC_SUB_EVENT_INITIAL_LOAD_EOF\n");
        return;
    }
    bool getEof = false;
    bool isEof = false;
    GmcStmtT *syncStmt = replayData->syncStmt;
    while (!getEof) {
        GmcDlrDataBufT dlrBuf;
        (void)memset_s(replayData->userBuf, replayData->bufSize, 0u, replayData->bufSize);
        ret = GmcDlrDataBufInit(&dlrBuf, replayData->userBuf, replayData->bufSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetDlrDataBufAttr(&dlrBuf, GMC_DLR_MAX_FETCH_NUM, &g_fetchNum, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcGetSubDlrDataBuf(subStmt, info, &dlrBuf, &getEof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcBatchOptionT batchOption;
        ret = GmcBatchOptionInit(&batchOption);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_NORMAL);  // GMC_BATCH_NORMAL
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcBatchT *batch = NULL;
        GmcBatchRetT batchRet;
        // prepare
        ret = GmcBatchPrepare(replayData->syncConn, &batchOption, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(syncStmt, replayData->replaytablename, GMC_OPERATION_REPLAY);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool setEof = false;
        ret = GmcSetDlrDataBuf(syncStmt, batch, &dlrBuf, &setEof);
        EXPECT_TRUE(setEof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchExecute(batch, &batchRet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (ret != 0) {
            testGmcGetLastError(NULL);
        }
        GmcBatchDestroy(batch);
        replayData->countcallback++;
        if (replayData->countcallback % 2 == 0 && g_notjumpFlag) {
            ret = GmcStmgConnSuspend(replayData->subConn);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            printf("this the %d times callback, suspend conn\n", replayData->countcallback);
            replayData->sendEof++;
            break;
        }
    }
    if (replayData->sendEof == 0) {
        replayData->sendEof = 0;
    } else {
        replayData->sendEof--;
    }
}

void sn_callback_suspend_failed(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *subDataTest)
{
    int ret = 0;
    DlrReplayData *replayData = (DlrReplayData *)subDataTest;

    if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
        return;
    }
    bool getEof = false;
    bool isEof = false;
    GmcStmtT *syncStmt = replayData->syncStmt;
    g_fetchNum = 1;  // 单次fetch数据最大100条
    while (!getEof) {
        GmcDlrDataBufT dlrBuf;
        (void)memset_s(replayData->userBuf, replayData->bufSize, 0u, replayData->bufSize);
        ret = GmcDlrDataBufInit(&dlrBuf, replayData->userBuf, replayData->bufSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetDlrDataBufAttr(&dlrBuf, GMC_DLR_MAX_FETCH_NUM, &g_fetchNum, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcGetSubDlrDataBuf(subStmt, info, &dlrBuf, &getEof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcBatchOptionT batchOption;
        ret = GmcBatchOptionInit(&batchOption);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_NORMAL);  // GMC_BATCH_NORMAL
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcBatchT *batch = NULL;
        GmcBatchRetT batchRet;
        // prepare
        ret = GmcBatchPrepare(replayData->syncConn, &batchOption, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(syncStmt, replayData->replaytablename, GMC_OPERATION_REPLAY);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool setEof = false;
        ret = GmcSetDlrDataBuf(syncStmt, batch, &dlrBuf, &setEof);
        EXPECT_TRUE(setEof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchExecute(batch, &batchRet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (ret != 0) {
            testGmcGetLastError(NULL);
        }
        GmcBatchDestroy(batch);
        replayData->countcallback++;
        if (replayData->countcallback % 90 == 0) {
            ret = GmcStmgConnSuspend(g_conn);
            AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);  // 预期失败
            ret = GmcStmgConnSuspend(g_conn_replay_nsp2);
            AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);  // 预期失败
            ret = GmcStmgConnSuspend(NULL);
            AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);  // 预期失败
            printf("this the %d times callback, suspend conn\n", replayData->countcallback);
        }
    }
    if (replayData->sendEof == 0) {
        replayData->sendEof = 0;
    } else {
        replayData->sendEof--;
    }
}
void sn_callback_resume(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *subDataTest)
{
    int ret = 0;
    DlrReplayData *replayData = (DlrReplayData *)subDataTest;

    if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
        printf("replayData->countcallback is %d\n", replayData->countcallback);
        printf("sn_callback_suspend GMC_SUB_EVENT_INITIAL_LOAD_EOF\n");
        return;
    }
    bool getEof = false;
    bool isEof = false;
    GmcStmtT *syncStmt = replayData->syncStmt;
    g_fetchNum = 1;  // 单次fetch数据最大100条
    while (!getEof) {
        GmcDlrDataBufT dlrBuf;
        (void)memset_s(replayData->userBuf, replayData->bufSize, 0u, replayData->bufSize);
        ret = GmcDlrDataBufInit(&dlrBuf, replayData->userBuf, replayData->bufSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetDlrDataBufAttr(&dlrBuf, GMC_DLR_MAX_FETCH_NUM, &g_fetchNum, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcGetSubDlrDataBuf(subStmt, info, &dlrBuf, &getEof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcBatchOptionT batchOption;
        ret = GmcBatchOptionInit(&batchOption);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_NORMAL);  // GMC_BATCH_NORMAL
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcBatchT *batch = NULL;
        GmcBatchRetT batchRet;
        // prepare
        ret = GmcBatchPrepare(replayData->syncConn, &batchOption, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(syncStmt, replayData->replaytablename, GMC_OPERATION_REPLAY);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool setEof = false;
        ret = GmcSetDlrDataBuf(syncStmt, batch, &dlrBuf, &setEof);
        EXPECT_TRUE(setEof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchExecute(batch, &batchRet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (ret != 0) {
            testGmcGetLastError(NULL);
        }
        replayData->countcallback++;
        GmcBatchDestroy(batch);
        if (replayData->countcallback % 90 == 0) {
            ret = GmcStmgConnResume(replayData->subConn);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            printf("this the %d times callback, resmue conn\n", replayData->countcallback);
            break;
        }
    }
    if (replayData->sendEof == 0) {
        replayData->sendEof = 0;
    } else {
        replayData->sendEof--;
    }
}

int BatchPrepare(GmcConnT *conn, GmcBatchT **batch)
{
    GmcBatchOptionT batchOption;
    int ret = GmcBatchOptionInit(&batchOption);
    RETURN_IFERR(ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    RETURN_IFERR(ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    RETURN_IFERR(ret);
    ret = GmcBatchPrepare(conn, &batchOption, batch);
    RETURN_IFERR(ret);
    return ret;
}

int SetinpValue(GmcStmtT *stmt, void *t)
{
    TableStructT *writetable = (TableStructT *)t;
    int64_t a = writetable->a;
    int64_t b = writetable->b;
    int64_t c = writetable->c;
    int32_t dtlReservedCount = writetable->dtlReservedCount;

    int ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT64, &a, sizeof(a));
    RETURN_IFERR(ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &b, sizeof(b));
    RETURN_IFERR(ret);
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT64, &c, sizeof(c));
    RETURN_IFERR(ret);
    ret =
        GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &dtlReservedCount, sizeof(dtlReservedCount));
    RETURN_IFERR(ret);
    return ret;
}

int SetVertexLabelValue(GmcStmtT *stmt, void *t)
{
    TableStructT *writetable = (TableStructT *)t;
    int64_t a = writetable->a;
    int64_t b = writetable->b;
    int64_t c = writetable->c;

    int ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT64, &a, sizeof(a));
    RETURN_IFERR(ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &b, sizeof(b));
    RETURN_IFERR(ret);
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT64, &c, sizeof(c));
    RETURN_IFERR(ret);
    return ret;
}

int SetinpallValue(GmcStmtT *stmt, int32_t v)
{
    int8_t a1 = v;
    int8_t a2 = v;
    int8_t a3 = v;
    int8_t a4 = v;
    int8_t b1 = v;
    int8_t b2 = v;
    int8_t b3 = v;
    int8_t b4 = v;
    int8_t c1 = v;
    int8_t c2 = v;
    int8_t c3 = v;
    int8_t c4 = v;
    int8_t c5 = v;
    int8_t c6 = v;
    int8_t c7 = v;
    int8_t c8 = v;
    int8_t c9 = v;
    int8_t c10 = v;
    int8_t a11 = v;
    int8_t a21 = v;
    int8_t a31 = v;
    int8_t a41 = v;
    int8_t b11 = v;
    int8_t b21 = v;
    int8_t b31 = v;
    int8_t b41 = v;
    int8_t c11 = v;
    int8_t c21 = v;
    int8_t c31 = v;
    int8_t c41 = v;
    int8_t c51 = v;
    int8_t c61 = v;
    int8_t c71 = v;
    int8_t c81 = v;
    int8_t c91 = v;
    int8_t c101 = v;
    int8_t a12 = v;
    int16_t a22 = v;
    int32_t a32 = v;
    int64_t a42 = v;
    uint8_t b12 = v;
    uint16_t b22 = v;
    uint32_t b32 = v;
    uint64_t b42 = v;
    uint8_t c12[1] = { 0 };
    int ret = memset_s(c12, 1, 0xff, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint8_t c22[2] = { 0 };
    ret = memset_s(c22, 2, 0xff, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint8_t c32[4] = { 0 };
    ret = memset_s(c32, 4, 0xff, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint8_t c42[8] = { 0 };
    ret = memset_s(c42, 8, 0xff, 8);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint8_t c52[16] = { 0 };
    ret = memset_s(c52, 16, 0xff, 16);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint8_t c62[32] = { 0 };
    ret = memset_s(c62, 32, 0xff, 32);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint8_t c72[64] = { 0 };
    ret = memset_s(c72, 64, 0xff, 64);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint8_t c82[128] = { 0 };
    ret = memset_s(c82, 128, 0xff, 128);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint8_t c92[256] = { 0 };
    ret = memset_s(c92, 256, 0xff, 256);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint8_t c102[512] = { 0 };
    ret = memset_s(c102, 512, 0xff, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint8_t d1 = 1;
    uint16_t d3 = 1;
    uint16_t d4 = 1;
    uint32_t d5 = 1;
    uint32_t d6 = 1;
    uint64_t d7 = 1;
    uint64_t d8 = 1;

    int stringLen = 10;  // string
    uint8_t *buf = (uint8_t *)malloc(stringLen + 1);
    if (buf == NULL) {
        return -1;
    }
    memset(buf, stringLen + 1, 0);
    (void)snprintf((char *)buf, stringLen + 1, "b%08d", v);
    char teststr15[10] = "bytes";
    ret = GmcSetVertexProperty(stmt, "a1", GMC_DATATYPE_INT8, &a1, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a2", GMC_DATATYPE_INT8, &a2, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a3", GMC_DATATYPE_INT8, &a3, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a4", GMC_DATATYPE_INT8, &a4, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b1", GMC_DATATYPE_INT8, &b1, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b2", GMC_DATATYPE_INT8, &b2, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b3", GMC_DATATYPE_INT8, &b3, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b4", GMC_DATATYPE_INT8, &b4, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c1", GMC_DATATYPE_INT8, &c1, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c2", GMC_DATATYPE_INT8, &c2, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c3", GMC_DATATYPE_INT8, &c3, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c4", GMC_DATATYPE_INT8, &c4, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c5", GMC_DATATYPE_INT8, &c5, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c6", GMC_DATATYPE_INT8, &c6, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c7", GMC_DATATYPE_INT8, &c7, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c8", GMC_DATATYPE_INT8, &c8, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c9", GMC_DATATYPE_INT8, &c9, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c10", GMC_DATATYPE_INT8, &c10, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a11", GMC_DATATYPE_INT8, &a11, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a21", GMC_DATATYPE_INT8, &a21, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a31", GMC_DATATYPE_INT8, &a31, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a41", GMC_DATATYPE_INT8, &a41, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b11", GMC_DATATYPE_INT8, &b11, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b21", GMC_DATATYPE_INT8, &b21, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b31", GMC_DATATYPE_INT8, &b31, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b41", GMC_DATATYPE_INT8, &b41, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c11", GMC_DATATYPE_INT8, &c11, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c21", GMC_DATATYPE_INT8, &c21, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c31", GMC_DATATYPE_INT8, &c31, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c41", GMC_DATATYPE_INT8, &c41, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c51", GMC_DATATYPE_INT8, &c51, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c61", GMC_DATATYPE_INT8, &c61, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c71", GMC_DATATYPE_INT8, &c71, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c81", GMC_DATATYPE_INT8, &c81, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c91", GMC_DATATYPE_INT8, &c91, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c101", GMC_DATATYPE_INT8, &c101, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a12", GMC_DATATYPE_INT8, &a12, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a22", GMC_DATATYPE_INT16, &a22, sizeof(int16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a32", GMC_DATATYPE_INT32, &a32, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a42", GMC_DATATYPE_INT64, &a42, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b12", GMC_DATATYPE_UINT8, &b12, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b22", GMC_DATATYPE_UINT16, &b22, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b32", GMC_DATATYPE_UINT32, &b32, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b42", GMC_DATATYPE_UINT64, &b42, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c12", GMC_DATATYPE_FIXED, &c12, sizeof(c12));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c22", GMC_DATATYPE_FIXED, &c22, sizeof(c22));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c32", GMC_DATATYPE_FIXED, &c32, sizeof(c32));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c42", GMC_DATATYPE_FIXED, &c42, sizeof(c42));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c52", GMC_DATATYPE_FIXED, &c52, sizeof(c52));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c62", GMC_DATATYPE_FIXED, &c62, sizeof(c62));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c72", GMC_DATATYPE_FIXED, &c72, sizeof(c72));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c82", GMC_DATATYPE_FIXED, &c82, sizeof(c82));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c92", GMC_DATATYPE_FIXED, &c92, sizeof(c92));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c102", GMC_DATATYPE_FIXED, &c102, sizeof(c102));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "d1", GMC_DATATYPE_BITFIELD8, &d1, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "d3", GMC_DATATYPE_BITFIELD16, &d3, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "d4", GMC_DATATYPE_BITFIELD16, &d4, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "d5", GMC_DATATYPE_BITFIELD32, &d5, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "d6", GMC_DATATYPE_BITFIELD32, &d6, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "d7", GMC_DATATYPE_BITFIELD64, &d7, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "d8", GMC_DATATYPE_BITFIELD64, &d8, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "d9", GMC_DATATYPE_STRING, buf, (strlen((char *)buf)));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "d10", GMC_DATATYPE_BYTES, teststr15, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t dtlReservedCount = 1;
    ret =
        GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &dtlReservedCount, sizeof(dtlReservedCount));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(buf);
    ret = GmcExecute(stmt);
    return ret;
}

int ReadKnownAlltypeValue(GmcConnT *conn, GmcStmtT *stmt, int32_t vertexCount, char *labelName)
{
    int ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    RETURN_IFERR(ret);

    for (int32_t v = 0; v < vertexCount; v++) {
        int8_t a1 = v;
        int8_t a2 = v;
        int8_t a3 = v;
        int8_t a4 = v;
        int8_t b1 = v;
        int8_t b2 = v;
        int8_t b3 = v;
        int8_t b4 = v;
        int8_t c1 = v;
        int8_t c2 = v;
        int8_t c3 = v;
        int8_t c4 = v;
        int8_t c5 = v;
        int8_t c6 = v;
        int8_t c7 = v;
        int8_t c8 = v;
        int8_t c9 = v;
        int8_t c10 = v;
        int8_t a11 = v;
        int8_t a21 = v;
        int8_t a31 = v;
        int8_t a41 = v;
        int8_t b11 = v;
        int8_t b21 = v;
        int8_t b31 = v;
        int8_t b41 = v;
        int8_t c11 = v;
        int8_t c21 = v;
        int8_t c31 = v;
        int8_t c41 = v;
        int8_t c51 = v;
        ret = GmcSetIndexKeyId(stmt, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int32_t upVerVal = -1;
        ret = GmcGetUpgradeVersion(stmt, &upVerVal);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upVerVal, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT8, &a1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT8, &a2, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_INT8, &a3, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 4, GMC_DATATYPE_INT8, &a4, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 5, GMC_DATATYPE_INT8, &b1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 6, GMC_DATATYPE_INT8, &b2, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 7, GMC_DATATYPE_INT8, &b3, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 8, GMC_DATATYPE_INT8, &b4, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 9, GMC_DATATYPE_INT8, &c1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 10, GMC_DATATYPE_INT8, &c2, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 11, GMC_DATATYPE_INT8, &c3, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 12, GMC_DATATYPE_INT8, &c4, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 13, GMC_DATATYPE_INT8, &c5, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 14, GMC_DATATYPE_INT8, &c6, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 15, GMC_DATATYPE_INT8, &c7, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 16, GMC_DATATYPE_INT8, &c8, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 17, GMC_DATATYPE_INT8, &c9, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 18, GMC_DATATYPE_INT8, &c10, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 19, GMC_DATATYPE_INT8, &a11, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 20, GMC_DATATYPE_INT8, &a21, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 21, GMC_DATATYPE_INT8, &a31, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 22, GMC_DATATYPE_INT8, &a41, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 23, GMC_DATATYPE_INT8, &b11, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 24, GMC_DATATYPE_INT8, &b21, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 25, GMC_DATATYPE_INT8, &b31, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 26, GMC_DATATYPE_INT8, &b41, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 27, GMC_DATATYPE_INT8, &c11, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 28, GMC_DATATYPE_INT8, &c21, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 29, GMC_DATATYPE_INT8, &c31, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 30, GMC_DATATYPE_INT8, &c41, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 31, GMC_DATATYPE_INT8, &c51, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        bool isFinish = false;
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int8_t c61 = v;
        int8_t c71 = v;
        int8_t c81 = v;
        int8_t c91 = v;
        int8_t c101 = v;
        int8_t a12 = v;
        int16_t a22 = v;
        int32_t a32 = v;
        int64_t a42 = v;
        uint8_t b12 = v;
        uint16_t b22 = v;
        uint32_t b32 = v;
        uint64_t b42 = v;
        uint8_t c12[1] = { 0 };
        ret = memset_s(c12, 1, 0xff, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint8_t c22[2] = { 0 };
        ret = memset_s(c22, 2, 0xff, 2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint8_t c32[4] = { 0 };
        ret = memset_s(c32, 4, 0xff, 4);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint8_t c42[8] = { 0 };
        ret = memset_s(c42, 8, 0xff, 8);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint8_t c52[16] = { 0 };
        ret = memset_s(c52, 16, 0xff, 16);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint8_t c62[32] = { 0 };
        ret = memset_s(c62, 32, 0xff, 32);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint8_t c72[64] = { 0 };
        ret = memset_s(c72, 64, 0xff, 64);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint8_t c82[128] = { 0 };
        ret = memset_s(c82, 128, 0xff, 128);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint8_t c92[256] = { 0 };
        ret = memset_s(c92, 256, 0xff, 256);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint8_t c102[512] = { 0 };
        ret = memset_s(c102, 512, 0xff, 512);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint8_t d1 = 1;
        uint16_t d3 = 1;
        uint16_t d4 = 1;
        uint32_t d5 = 1;
        uint32_t d6 = 1;
        uint64_t d7 = 1;
        uint64_t d8 = 1;

        int stringLen = 10;  // string
        uint8_t *buf = (uint8_t *)malloc(stringLen + 1);
        if (buf == NULL) {
            return -1;
        }
        memset(buf, stringLen + 1, 0);
        (void)snprintf((char *)buf, stringLen + 1, "b%08d", v);
        char teststr15[10] = "bytes";
        ret = queryPropertyAndCompare(stmt, "c61", GMC_DATATYPE_INT8, &c61);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "c71", GMC_DATATYPE_INT8, &c71);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "c81", GMC_DATATYPE_INT8, &c81);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "c91", GMC_DATATYPE_INT8, &c91);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "c101", GMC_DATATYPE_INT8, &c101);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "a12", GMC_DATATYPE_INT8, &a12);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "a22", GMC_DATATYPE_INT16, &a22);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "a32", GMC_DATATYPE_INT32, &a32);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "a42", GMC_DATATYPE_INT64, &a42);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "b12", GMC_DATATYPE_UINT8, &b12);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "b22", GMC_DATATYPE_UINT16, &b22);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "b32", GMC_DATATYPE_UINT32, &b32);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "b42", GMC_DATATYPE_UINT64, &b42);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "c12", GMC_DATATYPE_FIXED, &c12);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "c22", GMC_DATATYPE_FIXED, &c22);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "c32", GMC_DATATYPE_FIXED, &c32);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "c42", GMC_DATATYPE_FIXED, &c42);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "c52", GMC_DATATYPE_FIXED, &c52);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "c62", GMC_DATATYPE_FIXED, &c62);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "c62", GMC_DATATYPE_FIXED, &c62);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "c72", GMC_DATATYPE_FIXED, &c72);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "c82", GMC_DATATYPE_FIXED, &c82);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "c92", GMC_DATATYPE_FIXED, &c92);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "c102", GMC_DATATYPE_FIXED, &c102);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "d1", GMC_DATATYPE_UINT8, &d1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "d3", GMC_DATATYPE_UINT16, &d3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "d4", GMC_DATATYPE_UINT16, &d4);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "d5", GMC_DATATYPE_UINT32, &d5);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "d6", GMC_DATATYPE_UINT32, &d6);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "d7", GMC_DATATYPE_UINT64, &d7);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "d8", GMC_DATATYPE_UINT64, &d8);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "d9", GMC_DATATYPE_STRING, buf);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "d10", GMC_DATATYPE_BYTES, teststr15);
        EXPECT_EQ(GMERR_OK, ret);
        free(buf);
    }
    return ret;
}

int WriteGiantTable(GmcConnT *conn, GmcStmtT *stmt, int32_t startPkVal, int32_t vertexCount, bool isBatch)
{
    int ret = testGmcPrepareStmtByLabelName(stmt, g_inp1, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = SetinpallValue(stmt, i);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}

template <typename StructObjT>
int ReadKnownData(GmcConnT *conn, GmcStmtT *stmt, GtlabelCfgT vertexCfg, StructObjT *obj)
{
    int ret = 0;

    char *labelName = vertexCfg.labelName;
    GmcOperationTypeE opType = vertexCfg.opType;
    int32_t vertexCount = vertexCfg.writeCount;
    bool isBatch = vertexCfg.isBatch;
    for (int i = 0; i < vertexCount; i++) {
        int32_t upVerVal = -1;
        int32_t dtlReservedCount = 1;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetUpgradeVersion(stmt, &upVerVal);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 获取upgradeVersion值并设置
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upVerVal, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT64, &obj[i].a, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT64, &obj[i].b, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);

        bool isFinish = false;
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        int64_t value = 0;
        bool isNull = true;
        ret = GmcGetVertexPropertyByName(stmt, "c", &value, sizeof(int64_t), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(false, isNull);
        AW_MACRO_EXPECT_EQ_INT(obj[i].c, value);
    }
    return ret;
}

template <typename StructObjT>
int ReadKnownRscData(GmcConnT *conn, GmcStmtT *stmt, GtlabelCfgT vertexCfg, StructObjT *obj)
{
    int ret = 0;

    char *labelName = vertexCfg.labelName;
    GmcOperationTypeE opType = vertexCfg.opType;
    int32_t vertexCount = vertexCfg.writeCount;
    bool isBatch = vertexCfg.isBatch;
    for (int i = 0; i < vertexCount; i++) {
        int32_t upVerVal = -1;
        int32_t dtlReservedCount = 1;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetUpgradeVersion(stmt, &upVerVal);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 获取upgradeVersion值并设置
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upVerVal, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT64, &obj[i].a, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT64, &obj[i].b, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_INT64, &obj[i].c, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);

        bool isFinish = false;
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        int32_t value = 0;
        bool isNull = true;
        ret = GmcGetVertexPropertyByName(stmt, "d", &value, sizeof(int32_t), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(false, isNull);
        AW_MACRO_EXPECT_EQ_INT(obj[i].d, value);
    }
    return ret;
}

template <typename StructObjT>
int ReadKnownVertexLabelData(GmcConnT *conn, GmcStmtT *stmt, GtlabelCfgT vertexCfg, StructObjT *obj)
{
    int ret = 0;

    char *labelName = vertexCfg.labelName;
    GmcOperationTypeE opType = vertexCfg.opType;
    int32_t vertexCount = vertexCfg.writeCount;
    bool isBatch = vertexCfg.isBatch;
    for (int i = 0; i < vertexCount; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 获取upgradeVersion值并设置
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &obj[i].a, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT64, &obj[i].b, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);

        bool isFinish = false;
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        int64_t value = 0;
        bool isNull = true;
        ret = GmcGetVertexPropertyByName(stmt, "c", &value, sizeof(int64_t), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(false, isNull);
        AW_MACRO_EXPECT_EQ_INT(obj[i].c, value);
    }
    return ret;
}

template <typename StructObjT>
int WriteKnownData(GmcConnT *conn, GmcStmtT *stmt, GtlabelCfgT vertexCfg, FuncWrite func, StructObjT *obj,
                   char *strcutLabelName = NULL);

template <typename StructObjT>
int WriteKnownData(GmcConnT *conn, GmcStmtT *stmt, GtlabelCfgT vertexCfg, FuncWrite func, StructObjT *obj,
                   char *strcutLabelName)
{
    int ret = 0;

    char *labelName = vertexCfg.labelName;
    GmcOperationTypeE opType = vertexCfg.opType;
    int32_t vertexCount = vertexCfg.writeCount;
    bool isBatch = vertexCfg.isBatch;
    bool isStruct = vertexCfg.isStruct;

    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    RETURN_IFERR(ret);

    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    if (isBatch) {
        ret = BatchPrepare(conn, &batch);
        RETURN_IFERR(ret);
        if (isStruct) {
            ret = GmcBatchBindStmt(batch, stmt);
            RETURN_IFERR(ret);
        }
    }
    for (int32_t i = 0; i < vertexCount; i++) {
        if (isStruct) {
            TestLabelInfoT labelInfo = { (char *)strcutLabelName, 0, vertexCfg.namespaceName };
            ret = testStructSetVertexWithBuf(stmt, (obj + i), &labelInfo);
        } else {
            ret = func(stmt, (void *)(obj + i));
        }
        RETURN_IFERR(ret);
        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            RETURN_IFERR(ret);
        } else {
            ret = GmcExecute(stmt);
            RETURN_IFERR(ret);
        }
    }
    if (isBatch) {
        ret = GmcBatchExecute(batch, &batchRet);
        RETURN_IFERR(ret);
        uint32_t totalNum, successNum;
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        RETURN_IFERR(ret);
        RETURN_IFERR((int32_t)totalNum != vertexCount);
        RETURN_IFERR((int32_t)successNum != vertexCount);
        if (isStruct) {
            GmcBatchUnbindStmt(batch, stmt);
        }
        GmcBatchDestroy(batch);
    }
    return ret;
}

int WriteTwoTableData(GmcConnT *conn, GmcStmtT *stmt, int32_t writeCount)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, g_inp1, GMC_OPERATION_INSERT);
    RETURN_IFERR(ret);

    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    ret = BatchPrepare(conn, &batch);
    RETURN_IFERR(ret);
    for (int32_t i = 0; i < writeCount; i++) {
        int64_t a = i;
        int64_t b = i;
        int64_t c = i;
        int32_t dtlReservedCount = 1;

        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT64, &a, sizeof(a));
        RETURN_IFERR(ret);
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &b, sizeof(b));
        RETURN_IFERR(ret);
        ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT64, &c, sizeof(c));
        RETURN_IFERR(ret);
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &dtlReservedCount,
                                   sizeof(dtlReservedCount));
        RETURN_IFERR(ret);
        ret = GmcBatchAddDML(batch, stmt);
        RETURN_IFERR(ret);
    }
    ret = testGmcPrepareStmtByLabelName(stmt, g_inp2, GMC_OPERATION_INSERT);
    RETURN_IFERR(ret);
    for (int32_t i = 0; i < writeCount; i++) {
        int64_t a = i;
        int64_t b = i;
        int64_t c = i;
        int32_t dtlReservedCount = 1;

        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT64, &a, sizeof(a));
        RETURN_IFERR(ret);
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &b, sizeof(b));
        RETURN_IFERR(ret);
        ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT64, &c, sizeof(c));
        RETURN_IFERR(ret);
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &dtlReservedCount,
                                   sizeof(dtlReservedCount));
        RETURN_IFERR(ret);
        ret = GmcBatchAddDML(batch, stmt);
        RETURN_IFERR(ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    RETURN_IFERR(ret);
    uint32_t totalNum, successNum;
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    RETURN_IFERR(ret);
    RETURN_IFERR((int32_t)totalNum != writeCount * 2);
    RETURN_IFERR((int32_t)successNum != writeCount * 2);
    GmcBatchDestroy(batch);
    return ret;
}

int WriteOneTableData(GmcConnT *conn, GmcStmtT *stmt, int32_t writeCount)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, g_inp1, GMC_OPERATION_INSERT);
    RETURN_IFERR(ret);

    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    ret = BatchPrepare(conn, &batch);
    RETURN_IFERR(ret);
    for (int32_t i = 0; i < writeCount; i++) {
        int64_t a = i;
        int64_t b = i;
        int64_t c = i;
        int32_t dtlReservedCount = 1;

        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT64, &a, sizeof(a));
        RETURN_IFERR(ret);
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &b, sizeof(b));
        RETURN_IFERR(ret);
        ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT64, &c, sizeof(c));
        RETURN_IFERR(ret);
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &dtlReservedCount,
                                   sizeof(dtlReservedCount));
        RETURN_IFERR(ret);
        ret = GmcBatchAddDML(batch, stmt);
        RETURN_IFERR(ret);
    }
    ret = testGmcPrepareStmtByLabelName(stmt, g_inp1, GMC_OPERATION_INSERT);
    RETURN_IFERR(ret);
    for (int32_t i = 0; i < writeCount; i++) {
        int64_t a = i;
        int64_t b = i;
        int64_t c = i + 1;
        int32_t dtlReservedCount = 1;

        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT64, &a, sizeof(a));
        RETURN_IFERR(ret);
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &b, sizeof(b));
        RETURN_IFERR(ret);
        ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT64, &c, sizeof(c));
        RETURN_IFERR(ret);
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &dtlReservedCount,
                                   sizeof(dtlReservedCount));
        RETURN_IFERR(ret);
        ret = GmcBatchAddDML(batch, stmt);
        RETURN_IFERR(ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    RETURN_IFERR(ret);
    uint32_t totalNum, successNum;
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    RETURN_IFERR(ret);
    RETURN_IFERR((int32_t)totalNum != writeCount * 2);
    RETURN_IFERR((int32_t)successNum != writeCount * 2);
    GmcBatchDestroy(batch);
    return ret;
}

int WriteDeleteTwoTableData(GmcConnT *conn, GmcStmtT *stmt, int32_t writeCount)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, g_inp1, GMC_OPERATION_INSERT);
    RETURN_IFERR(ret);

    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    ret = BatchPrepare(conn, &batch);
    RETURN_IFERR(ret);
    for (int32_t i = writeCount; i < writeCount * 2; i++) {
        int64_t a = i;
        int64_t b = i;
        int64_t c = i;
        int32_t dtlReservedCount = 1;

        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT64, &a, sizeof(a));
        RETURN_IFERR(ret);
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &b, sizeof(b));
        RETURN_IFERR(ret);
        ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT64, &c, sizeof(c));
        RETURN_IFERR(ret);
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &dtlReservedCount,
                                   sizeof(dtlReservedCount));
        RETURN_IFERR(ret);
        ret = GmcBatchAddDML(batch, stmt);
        RETURN_IFERR(ret);
    }
    ret = testGmcPrepareStmtByLabelName(stmt, g_inp2, GMC_OPERATION_DELETE);
    RETURN_IFERR(ret);
    for (int32_t i = 0; i < writeCount; i++) {
        int32_t upVerVal = -1;
        int32_t dtlReservedCount = 1;
        int64_t v = i;
        ret = GmcGetUpgradeVersion(stmt, &upVerVal);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(stmt, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 获取upgradeVersion值并设置
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upVerVal, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT64, &v, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT64, &v, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        RETURN_IFERR(ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    RETURN_IFERR(ret);
    GmcBatchDestroy(batch);
    return ret;
}

template <typename StructObjT>
int WriteKnownDataAsync(GmcConnT *conn, GmcStmtT *stmt, GtlabelCfgT vertexCfg, FuncWrite func, StructObjT *obj,
                        char *strcutLabelName = NULL);

template <typename StructObjT>
int WriteKnownDataAsync(GmcConnT *conn, GmcStmtT *stmt, GtlabelCfgT vertexCfg, FuncWrite func, StructObjT *obj,
                        char *strcutLabelName)
{
    int ret = 0;
    AsyncUserDataT dataRev = { 0 };
    char *labelName = vertexCfg.labelName;
    GmcOperationTypeE opType = vertexCfg.opType;
    int32_t vertexCount = vertexCfg.writeCount;
    bool isBatch = vertexCfg.isBatch;
    bool isStruct = vertexCfg.isStruct;

    ret = testGmcPrepareStmtByLabelName(stmt, labelName, opType);
    RETURN_IFERR(ret);

    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    if (isBatch) {
        ret = BatchPrepare(conn, &batch);
        RETURN_IFERR(ret);
        if (isStruct) {
            ret = GmcBatchBindStmt(batch, stmt);
            RETURN_IFERR(ret);
        }
    }
    for (int32_t i = 0; i < vertexCount; i++) {
        if (isStruct) {
            TestLabelInfoT labelInfo = { (char *)strcutLabelName, 0, vertexCfg.namespaceName };
            ret = testStructSetVertexWithBuf(stmt, (obj + i), &labelInfo);
        } else {
            ret = func(stmt, (void *)(obj + i));
        }
        RETURN_IFERR(ret);
        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            RETURN_IFERR(ret);
        } else {
            GmcAsyncRequestDoneContextT insertRequestCtx;
            insertRequestCtx.insertCb = insert_vertex_callback;
            insertRequestCtx.userData = &dataRev;
            ret = GmcExecuteAsync(stmt, &insertRequestCtx);
            RETURN_IFERR(ret);
            ret = testWaitAsyncRecv(&dataRev);
            RETURN_IFERR(ret);
            ret = dataRev.status;
        }
    }
    if (isBatch) {
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &dataRev);
        ret = testWaitAsyncRecv(&dataRev);
        RETURN_IFERR(ret);
        ret = dataRev.status;
        if (isStruct) {
            GmcBatchUnbindStmt(batch, stmt);
        }
        GmcBatchDestroy(batch);
    }
    return ret;
}

bool g_isAlarm = false;
#define WAIT_TIME 10
void *ThrDoSleep(void *args)
{
    sleep(WAIT_TIME);
    g_isAlarm = true;
}

int StartClock(pthread_t *thrAlarm)
{
    int ret = 0;
    g_isAlarm = false;
    ret = pthread_create(thrAlarm, NULL, ThrDoSleep, NULL);
    return ret;
}

bool IsAlarm()
{
    bool expectIsAlarm = false;
    AW_MACRO_EXPECT_EQ_INT(expectIsAlarm, g_isAlarm);
    return g_isAlarm;
}

int32_t CompareRecordNum(const char *labelName, const char *namespaceName, int32_t recordNum)
{
    int32_t count = 0;
    char countcmd[MAX_CMD_SIZE] = { 0 };
    (void)snprintf(countcmd, MAX_CMD_SIZE, "gmsysview record %s -ns %s | grep -o 'index' | wc -l", labelName,
                   namespaceName);

    int ret = TestGetResultCommand(countcmd, &count);
    RETURN_IFERR(ret);
    int32_t wrongtimes = 0;
    if (count != recordNum) {
        AW_FUN_Log(LOG_ERROR, "ComparRecordNum failed, labelName: %s, namespaceName: %s, recordNum: %d, count: %d.",
                   labelName, namespaceName, recordNum, count);
        wrongtimes++;
    }
    if (strcmp(namespaceName, "public") == 0) {
        (void)snprintf(countcmd, MAX_CMD_SIZE, "gmsysview count | sed -n '/%s/ s/.*\\s\\+\\([0-9]\\+\\).*/\\1/p'",
                       labelName);
    } else {
        (void)snprintf(countcmd, MAX_CMD_SIZE, "gmsysview count | sed -n '/%s.%s/ s/.*\\s\\+\\([0-9]\\+\\).*/\\1/p'",
                       namespaceName, labelName);
    }
    system(countcmd);
    ret = TestGetResultCommand(countcmd, &count);
    RETURN_IFERR(ret);
    if (count != recordNum) {
        AW_FUN_Log(LOG_ERROR, "ComparCountNum failed, labelName: %s, namespaceName: %s, recordNum: %d, count: %d.",
                   labelName, namespaceName, recordNum, count);
        wrongtimes++;
    }
    if (wrongtimes > 0) {
        return -1;
    }
    return 0;
}

void *MultiThreadWriteTable(void *args)
{
    int ret = 0;
    WriteTableCfgT *cfg = (WriteTableCfgT *)args;
    GmcConnT *localconn = NULL;
    GmcStmtT *localstmt = NULL;
    GtlabelCfgT vertexCfg = cfg->vertexCfg;
    FuncWrite func = cfg->funcWrite;
    TableStructT *objIn = cfg->objIn;
    char *localnamespace = cfg->localnamespace;
    ret = testGmcConnect(&localconn, &localstmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(localstmt, localnamespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = WriteKnownData(localconn, localstmt, vertexCfg, func, objIn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(localconn, localstmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

int WaitUtillFetchFinish(DlrReplayDataT *replayData, const char *labelName, const char *namespaceName,
                         int32_t waitcount)
{
    pthread_t thrAlarm;
    sleep(2);
    int ret = StartClock(&thrAlarm);
    RETURN_IFERR(ret);
    int32_t count = 0;
    while (!IsAlarm()) {
        char countcmd[MAX_CMD_SIZE] = { 0 };
        (void)snprintf(countcmd, MAX_CMD_SIZE, "gmsysview record %s -ns %s | grep -o 'index' | wc -l", labelName,
                       namespaceName);
        ret = TestGetResultCommand(countcmd, &count);
        RETURN_IFERR(ret);
        if (replayData->sendEof == 0) {
            if (count == waitcount) {
                break;
            }
        } else {
            if ((count == replayData->countcallback || replayData->countcallback == waitcount)) {
                break;
            }
        }
    }
    printf("count is %d\n", count);
    printf("replayData.countcallback is %d\n", replayData->countcallback);
    pthread_cancel(thrAlarm);
    return ret;
}

int WaitUtilSingleFetchFinish(DlrReplayDataT *replayData, int32_t waitcount)
{
    pthread_t thrAlarm;
    int ret = StartClock(&thrAlarm);
    RETURN_IFERR(ret);
    int32_t count = 0;
    while (!IsAlarm()) {
        if ((replayData->countcallback == waitcount)) {
            break;
        }
    }
    printf("replayData.countcallback is %d\n", replayData->countcallback);
    pthread_cancel(thrAlarm);
    return ret;
}

int CheckSuspendTimeOut()
{
    int ret;
#if defined RUN_INDEPENDENT
    char envCheck[MAX_NAME_LENGTH] = "$TEST_HOME/";
    char logPpath[MAX_NAME_LENGTH] = "log/run/rgmserver/rgmserver.log";
#else
    char envCheck[MAX_NAME_LENGTH] = { '/', '\0' };
    char logPpath[MAX_NAME_LENGTH] = "opt/vrpv8/home/<USER>/diag.log";
#endif
    char catcmd[MAX_CMD_SIZE] = { 0 };
    (void)snprintf(catcmd, MAX_CMD_SIZE, "grep -rn \"Conn suspend time exceed threshold:\" %s%s", &envCheck, &logPpath);
    int trycnt = 0;
    while (trycnt < MAX_LOOP_NUM) {
        ret = GtExecSystemCmd(catcmd);
        if (ret == GMERR_OK) {
            break;
        }
        trycnt++;
        sleep(1);
    }
    return ret;
}
template <typename StructObjT>
int GetSpecialValue(GmcStmtT *stmt, char *labelName, StructObjT *obj, int32_t index, int64_t *value)
{
    int32_t upVerVal = -1;
    int32_t dtlReservedCount = 1;
    int ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 获取upgradeVersion值并设置
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upVerVal, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT64, &obj[index].a, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT64, &obj[index].b, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);

    bool isFinish = false;
    ret = GmcFetch(stmt, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isNull = true;
    ret = GmcGetVertexPropertyByName(stmt, "c", value, sizeof(int64_t), &isNull);
    return ret;
}

#endif
