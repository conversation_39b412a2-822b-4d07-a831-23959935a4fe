{"name": "subVertexLabel1", "label_name": "TEST_T0", "comment": "VertexLabel subscription", "type": "before_commit", "events": [{"type": "insert", "msgTypes": ["new object", "old object"]}, {"type": "update", "msgTypes": ["new object", "old object"]}, {"type": "delete", "msgTypes": ["new object", "old object"]}, {"type": "replace", "msgTypes": ["new object", "old object"]}], "is_path": false, "retry": true, "constraint": {"operator_type": "or", "conditions": [{"property": "T1/P8", "value": false}, {"property": "T1/P9", "value": 1.0}, {"property": "T1/P10", "value": 1.0}, {"property": "T1/P11", "value": 1}, {"property": "T1/P12", "value": "b"}, {"property": "T1/P13", "value": "b"}, {"property": "T1/P14", "value": "string1"}, {"property": "T1/P15", "value": "string1"}, {"property": "T1/P16", "value": "string1"}, {"property": "T1/P16", "value": "1234567"}]}}