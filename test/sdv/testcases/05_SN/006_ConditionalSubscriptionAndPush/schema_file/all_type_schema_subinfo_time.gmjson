{"name": "subVertexLabel", "label_name": "T20_all_type", "comment": "VertexLabel subscription", "type": "before_commit", "events": [{"type": "insert", "msgTypes": ["new object", "old object"]}, {"type": "update", "msgTypes": ["new object", "old object"]}, {"type": "delete", "msgTypes": ["new object", "old object"]}], "is_path": false, "retry": true, "constraint": {"operator_type": "or", "conditions": [{"property": "F13", "value": 1001}, {"property": "F13", "value": 1051}]}}