[{"type": "record", "name": "OP_T0", "fields": [{"name": "F0", "type": "int64"}, {"name": "F1", "type": "uint64"}, {"name": "F2", "type": "int32"}, {"name": "F3", "type": "uint32"}, {"name": "F4", "type": "int16"}, {"name": "F5", "type": "uint16"}, {"name": "F6", "type": "int8"}, {"name": "F7", "type": "uint8"}, {"name": "F8", "type": "boolean"}, {"name": "F9", "type": "float"}, {"name": "F10", "type": "double"}, {"name": "F11", "type": "time"}, {"name": "F12", "type": "char"}, {"name": "F13", "type": "uchar"}, {"name": "F14", "type": "string", "size": 100}], "keys": [{"node": "OP_T0", "name": "OP_PK", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]