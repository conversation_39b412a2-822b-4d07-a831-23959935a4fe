#include "messagePoolMaxMem.h"

int main(void)
{
    AW_FUN_Log(LOG_STEP, "process start.");
    int dmlTime = 0;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testEnvInit());
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcConnT *syncConn = NULL;
    GmcStmtT *syncStmt = NULL;
    GmcConnT *subConn = NULL;
    ret = testGmcConnect(&syncConn, &syncStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *subChan = "subChan1";
    ret = testSubConnect(&subConn, NULL, 1, g_epoll_reg_info, subChan);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *subJson = NULL;
    GmcSubConfigT subInfo;
    const char *subSchema = "schemaFile/subinfo_schema1_reliable_insert_update.gmjson";
    const char *subName = "sub1";
    SnUserDataT *user_data = NULL;
    user_data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(user_data, 0, sizeof(SnUserDataT));
    readJanssonFile(subSchema, &subJson);
    subInfo.subsName = subName;
    subInfo.configJson = subJson;
    GmcUnSubscribe(syncStmt, subName);
    ret = GmcSubscribe(syncStmt, &subInfo, subConn, sn_callback_num, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(subJson);
    blocking = true;  // 阻塞回调
    int messageNum = 0;
    for (int i = 0; i < 4 * 1024; i++) {
        ret = testGmcPrepareStmtByLabelName(syncStmt, vertexLabelName[0], GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(syncStmt, "PK");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        setschema1(syncStmt, i + 1);
        ret = GmcExecute(syncStmt);
        if (ret != 0) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_SUB_PUSH_QUEUE_FULL, ret);
            ret = testGmcGetLastError();
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (g_envType == 0 || g_envType == 1) {
                checkMem(31);
            } else {
                checkMem(14);
            }
            messageNum = i;
            printf("messageNum : %d\n", i);
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    free(user_data);
#ifdef DIRECT_WRITE
    AW_MACRO_EXPECT_EQ_INT(0, messageNum);  // 直连写只支持不可靠订阅
#else
    EXPECT_NE(0, messageNum);
#endif
    dmlTime = messageNum;
    blocking = false;
    ret = GmcUnSubscribe(syncStmt, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "kill child process");
    char cmd[1024];
    (void)sprintf(cmd, "kill -9 %d", getpid());
    system(cmd);
    AW_FUN_Log(LOG_STEP, "process stop.");
}
