/*****************************************************************************
 Description  : 使用消息池内存上限替换单个订阅通道内存上限能力
 Notes        : 头文件
 History      :
 Author       : chenbo cwx5332626
 Modification :
 Date         : 2022/1/20
*****************************************************************************/
extern "C" {
}

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#define CMD_LEN 1024
#define MAX_NAME_LENGTH 128
int ret = 0;
bool blocking = false;  // 是否阻塞回调
GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
const char *labelConfig = "{\"max_record_num\" : 1000000}";
const char *vertexLabelName[5] = {"lable1_8k", "lable2_8k", "lable3_600k", "lable4_600k", "lable5_8k"};
const char *vertexSchemaPath[5] = {"./schemaFile/schema1_8k.gmjson", "./schemaFile/schema2_8k.gmjson",
    "./schemaFile/schema3_600k.gmjson", "./schemaFile/schema4_600k.gmjson", "./schemaFile/schema5_8k.gmjson"};
const char *schema2k = R"(
    [{
    "type":"record",
    "name":"vertex_2k",
    "fields":[
        {"name":"F0", "type":"uint32", "nullable":false},
        {"name":"FF", "type":"uint32", "nullable":false},
        {"name":"F1", "type":"string", "nullable":true, "size":1024},
        {"name":"F2", "type":"string", "nullable":true, "size":1024}
    ],
    "keys":[
       {
            "node":"vertex_2k",
            "name":"PK",
            "fields":["F0"],
            "index":{"type":"primary"},
            "constraints":{"unique":true}
        }
    ]
}
]
)";
const char *subSchema2k = R"(
    {
    "label_name":"vertex_2k",
    "comment":"update",
    "type":"before_commit",
    "events":
        [
            {"type":"update", "msgTypes":["new object"]},
            {"type":"insert", "msgTypes":["new object"]}
        ],
    "is_path":false,
    "retry":true,
    "is_reliable": true
}
)";
// popen获取系统命令执行结果
void execCmd(const char *cmd, char result[])
{
    result[0] = '\0';
    extern int errno;

    FILE *fp = popen(cmd, "r");
    if (fp == NULL) {
        printf("[ERROR] popen create fp failed file=%s, line=%d, func=%s\n", __FILE__, __LINE__, __FUNCTION__);
        return;
    }

    char *p = fgets(result, CMD_LEN, fp);
    int len = strlen(result);
    result[(len > 0) ? (len - 1) : 0] = 0;
    pclose(fp);

    if (NULL == p)
        return;

    len = strlen(result);
    if (len > 0 && '\r' == result[len - 1]) {
        result[len - 1] = 0;
    }
    return;
}
/*
校验视图：COM_DYN_CTX && COM_SHMEM_CTX
关键字： CTX_NAME=subsChannelTopDynmemCtx / subsChannelTopMemCtx --2022/4/11--> RtMsgTopDynMemCtx/RtMsgTopShareMemCtx
校验字段： TOTAL_ALLOC_SIZE_ON_THIS_TREE
该特性分别限制动态内存和共享内存，当前欧拉环境，对于单表多订阅情况主要使用共享内存。即一条消息发多次会使用共享内存
打印并获取DFX中的内存值，单位MB
*/
int getMemFromString(const char *result)
{
    char tmp[5] = {0};  // 从结果得到MB单位的值,上限2048M
    for (int i = 0; i < strlen(result); i++) {
        if (result[i] == '[') {
            int j = 0;
            i++;
            while (result[i] != ']') {  // [2048]获取2048
                tmp[j++] = result[i++];
                tmp[j] = '\0';
            }
            break;
        }
    }
    return(atoi(tmp));
}

void getSubMemMaxSize(int &maxMem)
{  
    // 欧拉下也会使用共享内存
#if 0
    system("gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME='RtMsgTopDynmemCtx'");
    system("gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME='RtMsgTopShareMemCtx'");
#endif
    char cmd[CMD_LEN];
    char shm[CMD_LEN];
    char dynm[CMD_LEN];
    ret = sprintf(
        cmd, "gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME='RtMsgTopDynmemCtx' | grep TOTAL_ALLOC_SIZE_ON_THIS_TREE");
    EXPECT_NE(0, ret);
    execCmd(cmd, dynm);
    ret = sprintf(
        cmd, "gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME='RtMsgTopShareMemCtx' | grep TOTAL_ALLOC_SIZE_ON_THIS_TREE");
    EXPECT_NE(0, ret);
    execCmd(cmd, shm);
    printf("sub share mem:\n%s,\ndynamic mem:\n%s\n", shm, dynm);
    int shareMem = getMemFromString(shm);
    int dynMem = getMemFromString(dynm);
    if (g_runMode == 0) {  // 欧拉环境，同时校验共享内存、动态内存，取较大值
        maxMem = (dynMem > shareMem ? dynMem : shareMem);
        printf("info: dynamic mem :%dM, share mem :%dM\n", dynMem, shareMem);
    } else {  // hpe只用共享内存
        maxMem = shareMem;
    }
}
// 校验DFX值,参数传入 预期值、误差范围，单位MB
void checkMem(int expectMaxMem, int range = 0)
{
    int maxMem = 0;
    getSubMemMaxSize(maxMem);
    if (range == 0) {
        EXPECT_EQ(expectMaxMem, maxMem);
    } else {
        EXPECT_LE(expectMaxMem - range, maxMem);
        EXPECT_GE(expectMaxMem + range, maxMem);
    }
}
// 建表
void createVertexLabel(GmcStmtT *stmt, const char *path, const char *config = NULL)
{
    char *schema = NULL;
    readJanssonFile(path, &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(stmt, schema, config);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);
}
// schema1、schema2共用
void setschema1PK(GmcStmtT *stmt, int base)
{
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &base, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
}
void setschema1(GmcStmtT *stmt, int base)
{
    int tmp = 1;
    ret = GmcSetVertexProperty(stmt, "FF", GMC_DATATYPE_UINT32, &tmp, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    char str[1024];
    memset(str, 'a', 1023);
    str[1023] = '\0';
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_STRING, str, strlen(str));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_STRING, str, strlen(str));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_STRING, str, strlen(str));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_STRING, str, strlen(str));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_STRING, str, strlen(str));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_STRING, str, strlen(str));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_STRING, str, strlen(str));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_STRING, str, strlen(str));
    EXPECT_EQ(GMERR_OK, ret);
}
// 600K schema
void setschema3(GmcStmtT *stmt, int base)
{
    int tmp = 1;
    ret = GmcSetVertexProperty(stmt, "FF", GMC_DATATYPE_UINT32, &tmp, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    char *str = (char *)malloc(sizeof(char) * 10240);  // 10K字符串，60字段
    memset(str, 'a', 10240);
    str[10239] = '\0';
    char field[5];
    for (int i = 1; i < 61; i++) {
        sprintf(field, "F%d", i);
        ret = GmcSetVertexProperty(stmt, field, GMC_DATATYPE_STRING, str, strlen(str));
        EXPECT_EQ(GMERR_OK, ret);
    }
    free(str);
}
int g_call_num = 0;
// sn call_back_num 计算消息个数
void sn_callback_num(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    while (blocking) {
        sleep(1);
    }
    int ret;
    int index, i;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;
    void *label = 0;
    const void *keyValue = 0;
    uint32_t size;
    GmcConnT *conn_sync = 0;
    GmcStmtT *stmt_sync = 0;
    char keyName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        if (ret != GMERR_OK) {
            if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
                user_data->scanEofNum++;
                EXPECT_EQ(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, ret);
                printf("[INFO] <---GMC_SUB_EVENT_INITIAL_LOAD_EOF IS OVER--->\r\n");
                break;
            } else if (info->eventType == GMC_SUB_EVENT_TRIGGER_SCAN_BEGIN) {
                user_data->triggerScanBeginNum++;
                printf("[INFO] <---GMC_SUB_EVENT_TRIGGER_SCAN_BEGIN --->\r\n");
                break;
            } else if (info->eventType == GMC_SUB_EVENT_TRIGGER_SCAN_END) {
                user_data->triggerScanEndNum++;
                printf("[INFO] <---GMC_SUB_EVENT_TRIGGER_SCAN_END --->\r\n");
                break;
            } else {
                printf("[INFO] <---Subs Abnormal--!!!-->%d\r\n", ret);
                break;
            }
        } else if (eof == true) {
            break;
        }
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                // g_call_num++;
                // printf("call_back num :%d\n", g_call_num);
                break;
            }
            case GMC_SUB_EVENT_REPLACE_INSERT: {
                user_data->replaceInsertNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE_UPDATE: {
                user_data->replaceUpdateNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE_INSERT: {
                user_data->mergeInsertNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE_UPDATE: {
                user_data->mergeUpdateNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
        }
    }
}

// sn call_back_num 计算消息个数
void sn_callback_key(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    while (blocking) {
        sleep(1);
    }
    int ret;
    int index, i;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;
    void *label = 0;
    const void *keyValue = 0;
    uint32_t size;
    GmcConnT *conn_sync = 0;
    GmcStmtT *stmt_sync = 0;
    char keyName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }
        memset(labelName, 0, sizeof(labelName));
        labelNameLen = MAX_NAME_LENGTH;
        ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(strlen(labelName), labelNameLen);
        // printf("[sn_callback] msgType : %d\n", msgType);
        EXPECT_EQ(4, info->msgType);
        EXPECT_EQ(GMC_SUB_EVENT_UPDATE, info->eventType);
        // ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
        // EXPECT_EQ(GMERR_OK, ret);
        // index = ((int *)user_data->new_value)[user_data->subIndex];
        // test_checkVertexProperty_sub(subStmt, index);
        ret = testGmcConnect(&conn_sync, &stmt_sync);  // 创建同步连接进行读vertex
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(stmt_sync, labelName, GMC_OPERATION_SCAN);  // 同步stmt
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetPrimaryKeyName(stmt_sync, keyName, MAX_NAME_LENGTH);  // 同步stmt
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSubGetKey(subStmt, &keyValue, &size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValueBuffer(stmt_sync, keyValue, size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt_sync, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t FFSize = 0;
        uint32_t FFValue = 0;
        ret = GmcGetVertexPropertySizeByName(stmt_sync, "FF", &FFSize);
        EXPECT_EQ(GMERR_OK, ret);
        bool isNULL = true;
        ret = GmcGetVertexPropertyByName(stmt_sync, "FF", &FFValue, FFSize, &isNULL);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNULL);
        EXPECT_EQ(1, FFValue);
        ret = testGmcDisconnect(conn_sync, stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        user_data->updateNum++;
    }
}
