/*****************************************************************************
 Description  : 订阅推送变更前、后的key数据
 Notes        : SN_013_001 insert事件，msgType设定为new object
                SN_013_002 insert事件，msgType设定为old object
                SN_013_003 insert事件，msgType设定为old object和new object
                SN_013_004 update事件，msgType设定为new object
                SN_013_005 update事件，msgType设定为old object
                SN_013_006 update事件，msgType设定为old object和new object
                SN_013_007 delete事件，msgType设定为new object
                SN_013_008 delete事件，msgType设定为old object
                SN_013_009 delete事件，msgType设定为old object和new object
                SN_013_010 replace事件，msgType设定为new object，写入不存在的数据，写入已存在的数据
                SN_013_011 replace事件，msgType设定为old object，写入不存在的数据，写入已存在的数据
                SN_013_012 replace事件，msgType设定为new object和old object，写入不存在的数据，写入已存在的数据
                SN_013_013 merge事件，msgType设定为new object，写入不存在的数据，写入已存在的数据
                SN_013_014 merge事件，msgType设定为old object，写入不存在的数据，写入已存在的数据
                SN_013_015 merge事件，msgType设定为new object和old object，写入不存在的数据，写入已存在的数据
                SN_013_016 msgType设定为key,进行insert、update、delete、merge、replace
                SN_013_017 条件订阅key数据,进行insert、update、delete、merge、replace
                SN_013_018 key为多字段
                SN_013_019
订阅key数据，insert收到推送的key后读vertex，update收到key后，删除新数据，主线程再进行delete失败

 History      :
 Author       : 吴雪琦 00495442
 Modification :
 Date         : 2021/4/1
*****************************************************************************/

extern "C" {
}

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "sub_tools.h"

char g_label_name[] = "T20_all_type";
char g_label_name_2[] = "T20_all_type_2";
char g_label_PK[] = "T20_PK";
char g_label_hash[] = "T20_hash";
const char *g_subName = "subVertexLabel";
const char *g_subConnName = "subConnName";

using namespace std;

class KeySubscription_func : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh");
        int ret;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret;
        ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
    }

public:
    SnUserDataT *user_data;

    virtual void SetUp();
    virtual void TearDown();
};

void KeySubscription_func::SetUp()
{
    g_schema = NULL;
    g_label = NULL;
    g_conn_sync = NULL;
    g_stmt_sync = NULL;
    g_sub_info = NULL;
    int ret;

    ret = testSnMallocUserData(&user_data, g_data_num * 10);
    EXPECT_EQ(GMERR_OK, ret);

    //创建同步连接
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_sync, &g_stmt_sync_2);
    EXPECT_EQ(GMERR_OK, ret);

    GmcUnSubscribe(g_stmt_sync, g_subName);
    GmcDropVertexLabel(g_stmt_sync, g_label_name);
    GmcDropVertexLabel(g_stmt_sync, g_label_name_2);

    readJanssonFile("schema_file/all_type_schema_001.gmjson", &g_schema);
    EXPECT_NE((void *)NULL, g_schema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schema_file/all_type_schema_002.gmjson", &g_schema_2);
    EXPECT_NE((void *)NULL, g_schema_2);
    ret = GmcCreateVertexLabel(g_stmt_sync_2, g_schema_2, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_2, g_label_name_2, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    //创建订阅连接
    int chanRingLen = 256;

    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN();
}
void KeySubscription_func::TearDown()
{
    AW_CHECK_LOG_END();
    int ret;
    // 释放订阅连接
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    //删表断连
    test_close_and_drop_label(g_stmt_sync, g_label, g_label_name);
    test_close_and_drop_label(g_stmt_sync_2, g_label_2, g_label_name_2);
    GmcFreeStmt(g_stmt_sync_2);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_sub_info);
    free(g_schema);
    free(g_schema_2);
    testSnFreeUserData(user_data);
}

// insert事件，msgType设定为new object
TEST_F(KeySubscription_func, SN_013_001)
{
    int ret;
    int i;
    int userDataIdx = 0;

    readJanssonFile("schema_file/all_type_schema_subinfo_001_001.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    //写数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        // printf("[INSERT] i = %d\r\n", i);
        // newValue[userDataIdx] = i;
        // user_data[userDataIdx].new_value = &newValue[userDataIdx];
        // testSnSetValue(testSubConn, GMC_SUB_EVENT_INSERT, &user_data[userDataIdx], sn_callback);
        // userDataIdx++;

        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;

        test_setVertexPK(g_stmt_sync, i);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //等待insert事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
}

// insert事件，msgType设定为old object
TEST_F(KeySubscription_func, SN_013_002)
{
    const int errCodeLen = 1024;
    char errorMsg[errCodeLen] = {0};
    (void)snprintf(errorMsg, errCodeLen, "GMERR-%d", GMERR_DATATYPE_MISMATCH);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    int ret;
    int i;
    int userDataIdx = 0;

    readJanssonFile("schema_file/all_type_schema_subinfo_001_002.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// insert事件，msgType设定为old object和new object
TEST_F(KeySubscription_func, SN_013_003)
{
    int ret;
    int i;
    int userDataIdx = 0;

    readJanssonFile("schema_file/all_type_schema_subinfo_001_003.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback_simple, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    //写数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        // printf("[INSERT] i = %d\r\n", i);
        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;

        test_setVertexPK(g_stmt_sync, i);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //等待insert事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
}

// update事件，msgType设定为new object
TEST_F(KeySubscription_func, SN_013_004)
{
    int ret;
    int i;
    int userDataIdx = 0;

    readJanssonFile("schema_file/all_type_schema_subinfo_002_001.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    //写数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        // printf("[INSERT] i = %d\r\n", i);
        test_setVertexPK(g_stmt_sync, i);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    //更新数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        // printf("[UPDATE] i = %d\r\n", i);
        ((int *)(user_data->old_value))[userDataIdx] = i;
        ((int *)(user_data->new_value))[userDataIdx] = i + g_data_num;
        userDataIdx++;

        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // test_setVertexPK(g_stmt_sync, i + g_data_num);    // 2021.11.23: 主键不可更新
        test_setVertexProperty(g_stmt_sync, i + g_data_num);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_label_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //等待update事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_UPDATE, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
}

// update事件，msgType设定为old object
TEST_F(KeySubscription_func, SN_013_005)
{
    int ret;
    int i;
    int userDataIdx = 0;

    readJanssonFile("schema_file/all_type_schema_subinfo_002_002.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    //写数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        // printf("[INSERT] i = %d\r\n", i);
        test_setVertexPK(g_stmt_sync, i);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    //更新数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        // printf("[UPDATE] i = %d\r\n", i);
        ((int *)(user_data->old_value))[userDataIdx] = i;
        ((int *)(user_data->new_value))[userDataIdx] = i + g_data_num;
        userDataIdx++;

        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // test_setVertexPK(g_stmt_sync, i + g_data_num);    // 2021.11.23: 主键不可更新
        test_setVertexProperty(g_stmt_sync, i + g_data_num);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_label_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //等待update事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_UPDATE, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
}

// update事件，msgType设定为old object和new object
TEST_F(KeySubscription_func, SN_013_006)
{
    int ret;
    int i;
    int userDataIdx = 0;

    readJanssonFile("schema_file/all_type_schema_subinfo_002_003.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    //写数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        // printf("[INSERT] i = %d\r\n", i);
        test_setVertexPK(g_stmt_sync, i);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback_simple, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    //更新数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        // printf("[UPDATE] i = %d\r\n", i);
        ((int *)(user_data->old_value))[userDataIdx] = i;
        ((int *)(user_data->new_value))[userDataIdx] = i + g_data_num;
        userDataIdx++;

        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // test_setVertexPK(g_stmt_sync, i + g_data_num);    // 2021.11.23: 主键不可更新
        test_setVertexProperty(g_stmt_sync, i + g_data_num);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_label_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //等待update事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_UPDATE, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
}

// delete事件，msgType设定为new object
TEST_F(KeySubscription_func, SN_013_007)
{
    const int errCodeLen = 1024;
    char errorMsg[errCodeLen] = {0};
    (void)snprintf(errorMsg, errCodeLen, "GMERR-%d", GMERR_DATATYPE_MISMATCH);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    int ret;
    int i;
    int userDataIdx = 0;

    readJanssonFile("schema_file/all_type_schema_subinfo_003_001.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    //写数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        // printf("[INSERT] i = %d\r\n", i);
        test_setVertexPK(g_stmt_sync, i);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// delete事件，msgType设定为old object
TEST_F(KeySubscription_func, SN_013_008)
{
    int ret;
    int i;
    int userDataIdx = 0;

    readJanssonFile("schema_file/all_type_schema_subinfo_003_002.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    //写数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        // printf("[INSERT] i = %d\r\n", i);
        test_setVertexPK(g_stmt_sync, i);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    int tmp;
    //删除数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        // printf("[DELETE] i = %d\r\n", i);
        tmp = i;
        ((int *)(user_data->old_value))[userDataIdx] = tmp;
        userDataIdx++;

        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &tmp, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_label_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //等待delete事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
}

// delete事件，msgType设定为old object和new object
TEST_F(KeySubscription_func, SN_013_009)
{
    int ret;
    int i;
    int userDataIdx = 0;

    readJanssonFile("schema_file/all_type_schema_subinfo_003_003.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    //写数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        // printf("[INSERT] i = %d\r\n", i);
        test_setVertexPK(g_stmt_sync, i);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback_simple, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    int tmp;
    //删除数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        // printf("[DELETE] i = %d\r\n", i);
        tmp = i;
        ((int *)(user_data->old_value))[userDataIdx] = tmp;
        userDataIdx++;

        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &tmp, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_label_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //等待delete事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
}

// replace事件，msgType设定为new object，写入不存在的数据，写入已存在的数据
TEST_F(KeySubscription_func, SN_013_010)
{
    int ret;
    int i;
    int userDataIdx = 0;

    readJanssonFile("schema_file/all_type_schema_subinfo_004_001.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    // replace数据,写入不存在的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        // printf("[REPLACE] i = %d\r\n", i);
        ((int *)(user_data->new_value))[userDataIdx] = i;
        ((bool *)(user_data->isReplace_insert))[userDataIdx] = true;
        userDataIdx++;

        test_setVertexPK(g_stmt_sync, i);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //等待replace事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_REPLACE_INSERT, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    // replace数据,写入已存在的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        // printf("[REPLACE] i = %d\r\n", i);
        ((int *)(user_data->old_value))[userDataIdx] = i;
        ((int *)(user_data->new_value))[userDataIdx] = i + g_data_num;
        ((bool *)(user_data->isReplace_insert))[userDataIdx] = false;
        userDataIdx++;

        test_setVertexPK(g_stmt_sync, i);
        test_setVertexProperty(g_stmt_sync, i + g_data_num);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(2, affectRows);
    }
    //等待replace事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_REPLACE_UPDATE, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
}

// replace事件，msgType设定为old object，写入不存在的数据，写入已存在的数据
//写入不存在的数据会推送:
// replace insert 推的old是个len=0的报文
// replace update 推的old是个len>0的报文
TEST_F(KeySubscription_func, SN_013_011)
{
    int ret;
    int i;
    int userDataIdx = 0;

    readJanssonFile("schema_file/all_type_schema_subinfo_004_002.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    // replace数据,写入不存在的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        // printf("[REPLACE] i = %d\r\n", i);
        ((bool *)(user_data->isReplace_insert))[userDataIdx] = true;
        userDataIdx++;
        test_setVertexPK(g_stmt_sync, i);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //等待replace事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_REPLACE_INSERT, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    // replace数据,写入已存在的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        // printf("[REPLACE] i = %d\r\n", i);
        ((int *)(user_data->old_value))[userDataIdx] = i;
        ((int *)(user_data->new_value))[userDataIdx] = i + g_data_num;
        ((bool *)(user_data->isReplace_insert))[userDataIdx] = false;
        userDataIdx++;

        test_setVertexPK(g_stmt_sync, i);
        test_setVertexProperty(g_stmt_sync, i + g_data_num);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(2, affectRows);
    }
    //等待replace事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_REPLACE_UPDATE, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
}

// replace事件，msgType设定为new object和old object，写入不存在的数据，写入已存在的数据
TEST_F(KeySubscription_func, SN_013_012)
{
    int ret;
    int i;
    int userDataIdx = 0;

    readJanssonFile("schema_file/all_type_schema_subinfo_004_003.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback_simple, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    // replace数据,写入不存在的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        // printf("[REPLACE] i = %d\r\n", i);
        ((int *)(user_data->new_value))[userDataIdx] = i;
        ((bool *)(user_data->isReplace_insert))[userDataIdx] = true;
        userDataIdx++;

        test_setVertexPK(g_stmt_sync, i);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //等待replace事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_REPLACE, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    // replace数据,写入已存在的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        // printf("[REPLACE] i = %d\r\n", i);
        ((int *)(user_data->old_value))[userDataIdx] = i;
        ((int *)(user_data->new_value))[userDataIdx] = i + g_data_num;
        ((bool *)(user_data->isReplace_insert))[userDataIdx] = false;
        userDataIdx++;

        test_setVertexPK(g_stmt_sync, i);
        test_setVertexProperty(g_stmt_sync, i + g_data_num);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(2, affectRows);
    }
    //等待replace事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_REPLACE, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
}

// merge事件，msgType设定为new object，写入不存在的数据，写入已存在的数据
TEST_F(KeySubscription_func, SN_013_013)
{
    int ret;
    int i;
    int userDataIdx = 0;

    readJanssonFile("schema_file/all_type_schema_subinfo_004_001.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    // merge数据,写入不存在的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        // printf("[REPLACE] i = %d\r\n", i);
        ((int *)(user_data->new_value))[userDataIdx] = i;
        ((bool *)(user_data->isReplace_insert))[userDataIdx] = true;
        userDataIdx++;

        uint32_t value7 = i;  // F7是PK
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value7, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, "T20_PK");
        EXPECT_EQ(GMERR_OK, ret);
        // test_setVertexPK(g_stmt_sync, i);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //等待merge事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_MERGE_INSERT, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    // merge数据,写入已存在的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        // printf("[REPLACE] i = %d\r\n", i);
        ((int *)(user_data->old_value))[userDataIdx] = i;
        ((int *)(user_data->new_value))[userDataIdx] = i + g_data_num;
        ((bool *)(user_data->isReplace_insert))[userDataIdx] = false;
        userDataIdx++;
        uint32_t value7 = i;  // F7是PK
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value7, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, "T20_PK");
        EXPECT_EQ(GMERR_OK, ret);
        // test_setVertexPK(g_stmt_sync, i);
        test_setVertexProperty(g_stmt_sync, i + g_data_num);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(2, affectRows);
    }
    //等待merge事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_MERGE_UPDATE, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
}

// merge事件，msgType设定为old object，写入不存在的数据，写入已存在的数据
TEST_F(KeySubscription_func, SN_013_014)
{
    int ret;
    int i;
    int userDataIdx = 0;

    readJanssonFile("schema_file/all_type_schema_subinfo_004_002.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    // merge数据,写入不存在的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        // printf("[REPLACE] i = %d\r\n", i);
        ((bool *)(user_data->isReplace_insert))[userDataIdx] = true;
        userDataIdx++;
        uint32_t value7 = i;  // F7是PK
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value7, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, "T20_PK");
        EXPECT_EQ(GMERR_OK, ret);
        // test_setVertexPK(g_stmt_sync, i);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //等待merge事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_MERGE_INSERT, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    // merge数据,写入已存在的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        // printf("[REPLACE] i = %d\r\n", i);
        ((int *)(user_data->old_value))[userDataIdx] = i;
        ((int *)(user_data->new_value))[userDataIdx] = i + g_data_num;
        ((bool *)(user_data->isReplace_insert))[userDataIdx] = false;
        userDataIdx++;
        uint32_t value7 = i;  // F7是PK
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value7, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, "T20_PK");
        EXPECT_EQ(GMERR_OK, ret);
        // test_setVertexPK(g_stmt_sync, i);
        test_setVertexProperty(g_stmt_sync, i + g_data_num);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(2, affectRows);
    }
    //等待merge事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_MERGE_UPDATE, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
}

// merge事件，msgType设定为new object和old object，写入不存在的数据，写入已存在的数据
TEST_F(KeySubscription_func, SN_013_015)
{
    int ret;
    int i;
    int userDataIdx = 0;

    readJanssonFile("schema_file/all_type_schema_subinfo_004_003.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback_simple, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    // merge数据,写入不存在的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        // printf("[REPLACE] i = %d\r\n", i);
        ((int *)(user_data->new_value))[userDataIdx] = i;
        ((bool *)(user_data->isReplace_insert))[userDataIdx] = true;
        userDataIdx++;
        uint32_t value7 = i;  // F7是PK
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value7, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, "T20_PK");
        EXPECT_EQ(GMERR_OK, ret);
        // test_setVertexPK(g_stmt_sync, i);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //等待merge事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_MERGE, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    // merge数据,写入已存在的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        // printf("[REPLACE] i = %d\r\n", i);
        ((int *)(user_data->old_value))[userDataIdx] = i;
        ((int *)(user_data->new_value))[userDataIdx] = i + g_data_num;
        ((bool *)(user_data->isReplace_insert))[userDataIdx] = false;
        userDataIdx++;
        uint32_t value7 = i;  // F7是PK
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value7, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, "T20_PK");
        EXPECT_EQ(GMERR_OK, ret);
        // test_setVertexPK(g_stmt_sync, i);
        test_setVertexProperty(g_stmt_sync, i + g_data_num);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(2, affectRows);
    }
    //等待merge事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_MERGE, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
}

// msgType设定为key,进行insert、update、delete、merge、replace
TEST_F(KeySubscription_func, SN_013_016)
{
    int ret;
    int i;
    int userDataIdx = 0;

    readJanssonFile("schema_file/all_type_schema_subinfo.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    //写数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        // printf("[INSERT] i = %d\r\n", i);
        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;

        test_setVertexPK(g_stmt_sync, i);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //等待insert事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, g_data_num, RECV_TIMEOUT * 2);
    EXPECT_EQ(GMERR_OK, ret);

    //更新数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        // printf("[UPDATE] i = %d\r\n", i);
        ((int *)(user_data->old_value))[userDataIdx] = i;
        ((int *)(user_data->new_value))[userDataIdx] = i + g_data_num;
        userDataIdx++;

        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // test_setVertexPK(g_stmt_sync, i + g_data_num);    // 2021.11.23: 主键不可更新
        test_setVertexProperty(g_stmt_sync, i + g_data_num);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_label_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //等待update事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_UPDATE, g_data_num, RECV_TIMEOUT * 2);
    EXPECT_EQ(GMERR_OK, ret);

    int tmp;
    //删除数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        // printf("[DELETE] i = %d\r\n", i);
        tmp = i + g_data_num;
        ((int *)(user_data->old_value))[userDataIdx] = tmp;
        userDataIdx++;

        // 2021.11.23: 主键不可更新
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_label_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //等待delete事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, g_data_num, RECV_TIMEOUT * 2);
    EXPECT_EQ(GMERR_OK, ret);

    // merge数据,写入不存在的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num / 2; i++) {
        // printf("[MERGE] i = %d\r\n", i);
        ((int *)(user_data->new_value))[userDataIdx] = i;
        ((bool *)(user_data->isReplace_insert))[userDataIdx] = true;
        userDataIdx++;
        uint32_t value7 = i;  // F7是PK
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value7, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, "T20_PK");
        EXPECT_EQ(GMERR_OK, ret);
        // test_setVertexPK(g_stmt_sync, i);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //等待merge事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_MERGE_INSERT, g_data_num / 2, RECV_TIMEOUT * 2);
    EXPECT_EQ(GMERR_OK, ret);

    // merge数据,写入已存在的数据
    for (i = 0; i < g_data_num / 2; i++) {
        // printf("[MERGE] i = %d\r\n", i);
        ((int *)(user_data->old_value))[userDataIdx] = i;
        ((int *)(user_data->new_value))[userDataIdx] = i + g_data_num;
        ((bool *)(user_data->isReplace_insert))[userDataIdx] = false;
        userDataIdx++;
        uint32_t value7 = i;  // F7是PK
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value7, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, "T20_PK");
        EXPECT_EQ(GMERR_OK, ret);
        // test_setVertexPK(g_stmt_sync, i);
        test_setVertexProperty(g_stmt_sync, i + g_data_num);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(2, affectRows);
    }
    //等待merge事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_MERGE_UPDATE, g_data_num / 2, RECV_TIMEOUT * 2);
    EXPECT_EQ(GMERR_OK, ret);

    // replace数据,写入不存在的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = g_data_num / 2; i < g_data_num; i++) {
        // printf("[REPLACE] i = %d\r\n", i);
        ((int *)(user_data->new_value))[userDataIdx] = i;
        ((bool *)(user_data->isReplace_insert))[userDataIdx] = true;
        userDataIdx++;

        test_setVertexPK(g_stmt_sync, i);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //等待replace事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_REPLACE_INSERT, g_data_num / 2, RECV_TIMEOUT * 2);
    EXPECT_EQ(GMERR_OK, ret);

    // replace数据,写入已存在的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = g_data_num / 2; i < g_data_num; i++) {
        // printf("[REPLACE] i = %d\r\n", i);
        ((int *)(user_data->old_value))[userDataIdx] = i;
        ((int *)(user_data->new_value))[userDataIdx] = i + g_data_num;
        ((bool *)(user_data->isReplace_insert))[userDataIdx] = false;
        userDataIdx++;

        test_setVertexPK(g_stmt_sync, i);
        test_setVertexProperty(g_stmt_sync, i + g_data_num);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(2, affectRows);
    }
    //等待replace事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_REPLACE_UPDATE, g_data_num / 2, RECV_TIMEOUT * 2);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
}

bool isMatchPushCond(int value)
{
    return (value == 0);
}

//条件订阅key数据,进行insert、update、delete、merge、replace
TEST_F(KeySubscription_func, SN_013_017)
{
    int ret;
    int i;
    int userDataIdx = 0;

    readJanssonFile("schema_file/all_type_schema_subinfo_cond.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    //写数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexPK(g_stmt_sync, i);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    //等待insert事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 0);
    EXPECT_EQ(GMERR_OK, ret);

    //更新数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        if (isMatchPushCond(i)) {
            ((int *)(user_data->old_value))[userDataIdx] = i;
            ((int *)(user_data->new_value))[userDataIdx] = i + g_data_num;
            userDataIdx++;
        }

        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        test_setVertexProperty(g_stmt_sync, i + g_data_num);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_label_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    //等待update事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_UPDATE, 1);
    EXPECT_EQ(GMERR_OK, ret);

    int tmp;
    //删除数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        // printf("[DELETE] i = %d\r\n", i);
        tmp = i + g_data_num;
        ((int *)(user_data->old_value))[userDataIdx] = tmp;
        userDataIdx++;

        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_label_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    //等待delete事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    // merge数据,写入不存在的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num / 2; i++) {
        uint32_t value7 = i;  // F7是PK
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value7, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, "T20_PK");
        EXPECT_EQ(GMERR_OK, ret);
        // test_setVertexPK(g_stmt_sync, i);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    //等待merge事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_MERGE_INSERT, 0);
    EXPECT_EQ(GMERR_OK, ret);

    // merge数据,写入已存在的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num / 2; i++) {
        if (isMatchPushCond(i)) {
            ((int *)(user_data->old_value))[userDataIdx] = i;
            ((int *)(user_data->new_value))[userDataIdx] = i + g_data_num;
            userDataIdx++;
        }

        uint32_t value7 = i;  // F7是PK
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value7, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, "T20_PK");
        EXPECT_EQ(GMERR_OK, ret);
        // test_setVertexPK(g_stmt_sync, i);
        test_setVertexProperty(g_stmt_sync, i + g_data_num);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(2, affectRows);
    }

    //等待merge事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_MERGE_UPDATE, 1);
    EXPECT_EQ(GMERR_OK, ret);

    // replace数据,写入不存在的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = g_data_num / 2; i < g_data_num; i++) {
        test_setVertexPK(g_stmt_sync, i);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    //等待replace事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_REPLACE_INSERT, 0);
    EXPECT_EQ(GMERR_OK, ret);

    // replace数据,写入已存在的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = g_data_num / 2; i < g_data_num; i++) {
        test_setVertexPK(g_stmt_sync, i);
        test_setVertexProperty(g_stmt_sync, i + g_data_num);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(2, affectRows);
    }

    //等待replace事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_REPLACE_UPDATE, 0);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
}

// key为多字段，包含所有属性类型：待补充新增类型
TEST_F(KeySubscription_func, SN_013_018)
{
    int ret;
    int i;
    int userDataIdx = 0;

    readJanssonFile("schema_file/all_type_schema_subinfo_multifield.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync_2, &tmp_g_sub_info, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    //写数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_2, g_label_name_2, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        // printf("[INSERT] i = %d\r\n", i);
        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;

        test_setVertexPK(g_stmt_sync_2, i);
        test_setVertexProperty(g_stmt_sync_2, i);
        ret = GmcExecute(g_stmt_sync_2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync_2, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //等待insert事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    //更新数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_2, g_label_name_2, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        // printf("[UPDATE] i = %d\r\n", i);
        ((int *)(user_data->old_value))[userDataIdx] = i;
        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;

        char teststr0 = 'a';
        unsigned char teststr1 = 'b';
        int8_t value2 = i;
        uint8_t value3 = i;
        int16_t value4 = i;
        uint16_t value5 = i;
        int32_t value6 = i;
        uint32_t value7 = i;
        bool value8 = false;
        int64_t value9 = i;
        uint64_t value10 = i;
        float value11 = (float)1.2 + (float)i;
        double value12 = 10.86 + i;
        uint64_t value13 = i;
        char teststr14[] = "string";
        char teststr15[10] = "bytes";
        char teststr16[6] = "fixed";
        EXPECT_EQ(6, strlen(teststr16) + 1);
        uint32_t value17 = i;

        ret = GmcSetIndexKeyValue(g_stmt_sync_2, 0, GMC_DATATYPE_UINT32, &value7, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync_2, 1, GMC_DATATYPE_INT64, &value9, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync_2, 2, GMC_DATATYPE_UINT64, &value10, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync_2, 3, GMC_DATATYPE_TIME, &value13, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync_2, 4, GMC_DATATYPE_STRING, teststr14, strlen(teststr14));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt_sync_2, g_label_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync_2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync_2, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //等待update事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_UPDATE, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    int tmp;
    //删除数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_2, g_label_name_2, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        // printf("[DELETE] i = %d\r\n", i);
        tmp = i;
        ((int *)(user_data->old_value))[userDataIdx] = tmp;
        userDataIdx++;

        char teststr0 = 'a';
        unsigned char teststr1 = 'b';
        int8_t value2 = tmp;
        uint8_t value3 = tmp;
        int16_t value4 = tmp;
        uint16_t value5 = tmp;
        int32_t value6 = tmp;
        uint32_t value7 = tmp;
        bool value8 = false;
        int64_t value9 = tmp;
        uint64_t value10 = tmp;
        float value11 = (float)1.2 + (float)tmp;
        double value12 = 10.86 + tmp;
        uint64_t value13 = tmp;
        char teststr14[] = "string";
        char teststr15[10] = "bytes";
        char teststr16[6] = "fixed";
        EXPECT_EQ(6, strlen(teststr16) + 1);
        uint32_t value17 = tmp;

        ret = GmcSetIndexKeyValue(g_stmt_sync_2, 0, GMC_DATATYPE_UINT32, &value7, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync_2, 1, GMC_DATATYPE_INT64, &value9, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync_2, 2, GMC_DATATYPE_UINT64, &value10, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync_2, 3, GMC_DATATYPE_TIME, &value13, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync_2, 4, GMC_DATATYPE_STRING, teststr14, strlen(teststr14));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt_sync_2, g_label_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync_2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync_2, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //等待delete事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt_sync_2, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
}

void sn_callback_019(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret;
    int index, i;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;
    void *label = 0;
    const void *keyValue = 0;
    uint32_t size;
    GmcConnT *conn_sync = 0;
    GmcStmtT *stmt_sync = 0;
    char keyName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }

        for (i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(strlen(labelName), labelNameLen);
            // printf("[sn_callback] msgType : %d\n", msgType);

            switch (info->msgType) {
                case 4:  //推送key
                {
                    ret = testGmcConnect(&conn_sync, &stmt_sync);  //创建同步连接进行读vertex
                    EXPECT_EQ(GMERR_OK, ret);

                    ret = testGmcPrepareStmtByLabelName(stmt_sync, labelName, GMC_OPERATION_SCAN);  //同步stmt
                    EXPECT_EQ(GMERR_OK, ret);

                    ret = GmcGetPrimaryKeyName(stmt_sync, keyName, MAX_NAME_LENGTH);  //同步stmt
                    EXPECT_EQ(GMERR_OK, ret);

                    switch (info->eventType) {
                        case GMC_SUB_EVENT_INSERT: {
                            //读new
                            ret = GmcSubGetKey(subStmt, &keyValue, &size);  //订阅stmt
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcSetIndexKeyValueBuffer(stmt_sync, keyValue, size);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcExecute(stmt_sync);
                            EXPECT_EQ(GMERR_OK, ret);
                            bool isFinish = true;
                            ret = GmcFetch(stmt_sync, &isFinish);
                            EXPECT_EQ(GMERR_OK, ret);
                            index = ((int *)user_data->new_value)[user_data->subIndex];
                            // 刷屏日志注释printf("[KEY] GMC_SUB_EVENT_INSERT new_value is %d\r\n", index);
                            test_checkVertexProperty_sub(stmt_sync, index);

                            break;
                        }
                        case GMC_SUB_EVENT_DELETE: {
                            //读old
                            ret = GmcSubGetKey(subStmt, &keyValue, &size);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcSetIndexKeyValueBuffer(stmt_sync, keyValue, size);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcExecute(stmt_sync);
                            EXPECT_EQ(GMERR_OK, ret);
                            bool isFinish = true;
                            ret = GmcFetch(stmt_sync, &isFinish);
                            EXPECT_EQ(GMERR_OK, ret);
                            EXPECT_EQ(isFinish, true);

                            index = ((int *)user_data->old_value)[user_data->subIndex];
                            // 刷屏日志注释printf("[KEY] GMC_SUB_EVENT_DELETE old_value is %d\r\n", index);
                            break;
                        }
                        case GMC_SUB_EVENT_UPDATE: {
                            //读new
                            ret = GmcSubGetKey(subStmt, &keyValue, &size);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcSetIndexKeyValueBuffer(stmt_sync, keyValue, size);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcExecute(stmt_sync);
                            EXPECT_EQ(GMERR_OK, ret);
                            bool isFinish = true;
                            ret = GmcFetch(stmt_sync, &isFinish);
                            EXPECT_EQ(GMERR_OK, ret);
                            index = ((int *)user_data->new_value)[user_data->subIndex];
                            // 刷屏日志注释printf("[KEY] GMC_SUB_EVENT_UPDATE new_value is %d\r\n", index);
                            test_checkVertexProperty_sub(stmt_sync, index);

                            ret = testGmcPrepareStmtByLabelName(stmt_sync, labelName, GMC_OPERATION_DELETE);  //同步stmt
                            EXPECT_EQ(GMERR_OK, ret);
                            char teststr0 = 'a';
                            unsigned char teststr1 = 'b';
                            int8_t value2 = index;
                            uint8_t value3 = index;
                            int16_t value4 = index;
                            uint16_t value5 = index;
                            int32_t value6 = index;
                            uint32_t value7 = index;
                            bool value8 = false;
                            int64_t value9 = index;
                            uint64_t value10 = index;
                            float value11 = (float)1.2 + (float)index;
                            double value12 = 10.86 + index;
                            uint64_t value13 = index;
                            char teststr14[] = "string";
                            char teststr15[10] = "bytes";
                            char teststr16[6] = "fixed";
                            EXPECT_EQ(6, strlen(teststr16) + 1);
                            uint32_t value17 = index;
                            ret = GmcSetIndexKeyValue(stmt_sync, 0, GMC_DATATYPE_UINT32, &value7, sizeof(uint32_t));
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcSetIndexKeyValue(stmt_sync, 1, GMC_DATATYPE_INT64, &value9, sizeof(int64_t));
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcSetIndexKeyValue(stmt_sync, 2, GMC_DATATYPE_UINT64, &value10, sizeof(uint64_t));
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcSetIndexKeyValue(stmt_sync, 3, GMC_DATATYPE_TIME, &value13, sizeof(uint64_t));
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcSetIndexKeyValue(stmt_sync, 4, GMC_DATATYPE_STRING, teststr14, strlen(teststr14));
                            EXPECT_EQ(GMERR_OK, ret);

                            ret = GmcSetIndexKeyName(stmt_sync, keyName);
                            EXPECT_EQ(GMERR_OK, ret);
                            ret = GmcExecute(stmt_sync);
                            EXPECT_EQ(GMERR_OK, ret);
                            int affectRows;
                            unsigned int len;
                            ret =
                                GmcGetStmtAttr(stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
                            EXPECT_EQ(GMERR_OK, ret);
                            EXPECT_EQ(1, affectRows);
                            break;
                        }
                        default: {
                            printf("default: invalid eventType\r\n");
                            break;
                        }
                    }
                    ret = testGmcDisconnect(conn_sync, stmt_sync);
                    EXPECT_EQ(GMERR_OK, ret);
                    break;
                }
                default: {
                    printf("default: invalid msgType\r\n");
                    break;
                }
            }
        }
    }
    user_data->subIndex++;
    switch (info->eventType) {
        case GMC_SUB_EVENT_INSERT: {
            user_data->insertNum++;
            break;
        }
        case GMC_SUB_EVENT_DELETE: {
            user_data->deleteNum++;
            break;
        }
        case GMC_SUB_EVENT_UPDATE: {
            user_data->updateNum++;
            break;
        }
        case GMC_SUB_EVENT_REPLACE: {
            user_data->replaceNum++;
            break;
        }
        case GMC_SUB_EVENT_KV_SET: {
            user_data->kvSetNum++;
            break;
        }
        case GMC_SUB_EVENT_INITIAL_LOAD: {
            user_data->scanNum++;
            break;
        }
        case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
            user_data->scanEofNum++;
            break;
        }
        case GMC_SUB_EVENT_AGED: {
            user_data->agedNum++;
            break;
        }
    }
}

//订阅key数据，insert收到推送的key后读vertex，update收到key后，删除新数据，主线程再进行delete失败
TEST_F(KeySubscription_func, SN_013_019)
{
    int ret;
    int i;
    int userDataIdx = 0;

    readJanssonFile("schema_file/all_type_schema_subinfo_multifield.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync_2, &tmp_g_sub_info, g_conn_sub, sn_callback_019, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    //写数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_2, g_label_name_2, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        // printf("[INSERT] i = %d\r\n", i);
        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;

        test_setVertexPK(g_stmt_sync_2, i);
        test_setVertexProperty(g_stmt_sync_2, i);
        ret = GmcExecute(g_stmt_sync_2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync_2, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //等待insert事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    //更新数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_2, g_label_name_2, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        // printf("[UPDATE] i = %d\r\n", i);
        ((int *)(user_data->old_value))[userDataIdx] = i;
        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;

        ((int *)(user_data->old_value))[userDataIdx] = i;
        userDataIdx++;

        char teststr0 = 'a';
        unsigned char teststr1 = 'b';
        int8_t value2 = i;
        uint8_t value3 = i;
        int16_t value4 = i;
        uint16_t value5 = i;
        int32_t value6 = i;
        uint32_t value7 = i;
        bool value8 = false;
        int64_t value9 = i;
        uint64_t value10 = i;
        float value11 = (float)1.2 + (float)i;
        double value12 = 10.86 + i;
        uint64_t value13 = i;
        char teststr14[] = "string";
        char teststr15[10] = "bytes";
        char teststr16[6] = "fixed";
        EXPECT_EQ(6, strlen(teststr16) + 1);
        uint32_t value17 = i;

        ret = GmcSetIndexKeyValue(g_stmt_sync_2, 0, GMC_DATATYPE_UINT32, &value7, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync_2, 1, GMC_DATATYPE_INT64, &value9, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync_2, 2, GMC_DATATYPE_UINT64, &value10, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync_2, 3, GMC_DATATYPE_TIME, &value13, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync_2, 4, GMC_DATATYPE_STRING, teststr14, strlen(teststr14));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt_sync_2, g_label_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync_2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync_2, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);

        //等待update事件推送完成
        ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_UPDATE, 1);
        EXPECT_EQ(GMERR_OK, ret);
    }

    int tmp;
    //删除数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_2, g_label_name_2, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        // printf("[DELETE] i = %d\r\n", i);
        tmp = i;
        // oldValue[userDataIdx] = tmp;
        // user_data[userDataIdx].old_value = &oldValue[userDataIdx];
        // testSnSetValue(testSubConn, GMC_SUB_EVENT_DELETE, &user_data[userDataIdx], sn_callback_019);
        // userDataIdx++;

        char teststr0 = 'a';
        unsigned char teststr1 = 'b';
        int8_t value2 = tmp;
        uint8_t value3 = tmp;
        int16_t value4 = tmp;
        uint16_t value5 = tmp;
        int32_t value6 = tmp;
        uint32_t value7 = tmp;
        bool value8 = false;
        int64_t value9 = tmp;
        uint64_t value10 = tmp;
        float value11 = (float)1.2 + (float)tmp;
        double value12 = 10.86 + tmp;
        uint64_t value13 = tmp;
        char teststr14[] = "string";
        char teststr15[10] = "bytes";
        char teststr16[6] = "fixed";
        EXPECT_EQ(6, strlen(teststr16) + 1);
        uint32_t value17 = tmp;

        ret = GmcSetIndexKeyValue(g_stmt_sync_2, 0, GMC_DATATYPE_UINT32, &value7, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync_2, 1, GMC_DATATYPE_INT64, &value9, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync_2, 2, GMC_DATATYPE_UINT64, &value10, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync_2, 3, GMC_DATATYPE_TIME, &value13, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync_2, 4, GMC_DATATYPE_STRING, teststr14, strlen(teststr14));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt_sync_2, g_label_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync_2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync_2, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, affectRows);
    }
    //等待delete事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, g_data_num);  // delete会推送
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt_sync_2, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
}
