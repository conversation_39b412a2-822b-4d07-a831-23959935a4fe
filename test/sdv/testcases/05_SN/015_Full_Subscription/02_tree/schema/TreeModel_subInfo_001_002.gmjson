{"name": "subVertexLabel", "label_name": "TEST_T0", "comment": "VertexLabel subscription", "type": "before_commit", "events": [{"type": "insert", "msgTypes": ["new object", "old object"]}, {"type": "update", "msgTypes": ["new object", "old object"]}, {"type": "delete", "msgTypes": ["new object", "old object"]}, {"type": "replace insert", "msgTypes": ["new object", "old object"]}, {"type": "replace update", "msgTypes": ["new object", "old object"]}, {"type": "merge insert", "msgTypes": ["new object", "old object"]}, {"type": "merge update", "msgTypes": ["new object", "old object"]}], "is_path": false, "retry": true}