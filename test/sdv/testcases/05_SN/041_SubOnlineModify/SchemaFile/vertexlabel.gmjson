[{"type": "record", "name": "OP_T0", "fields": [{"name": "F7", "type": "uint32", "nullable": false}, {"name": "F0", "type": "char", "nullable": true}, {"name": "F1", "type": "uchar", "nullable": true}, {"name": "F2", "type": "int8", "nullable": true}, {"name": "F3", "type": "uint8", "nullable": true}, {"name": "F4", "type": "int16", "nullable": true}, {"name": "F5", "type": "uint16", "nullable": true}, {"name": "F6", "type": "int32", "nullable": true}, {"name": "F8", "type": "boolean", "nullable": true}], "keys": [{"name": "pk", "node": "OP_T0", "fields": ["F7"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]