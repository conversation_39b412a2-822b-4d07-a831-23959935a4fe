{"name": "subVertexLabel_33", "label_name": "ArrayVectorConditionalSubscription_Tree", "comment": "VertexLabel subscription", "type": "before_commit", "events": [{"type": "insert", "msgTypes": ["new object", "old object"]}, {"type": "update", "msgTypes": ["new object", "old object"]}, {"type": "delete", "msgTypes": ["new object", "old object"]}, {"type": "replace", "msgTypes": ["new object", "old object"]}, {"type": "merge", "msgTypes": ["new object", "old object"]}], "is_path": false, "retry": true, "constraint": {"operator_type": "or", "conditions": [{"property": "T1/T2/A0"}]}}