{"name": "subVertexLabel", "label_name": "ArrayVectorConditionalSubscription_Tree", "comment": "VertexLabel subscription", "type": "before_commit", "events": [{"type": "update", "msgTypes": ["new object", "old object"]}], "is_path": false, "retry": true, "constraint": {"operator_type": "or", "conditions": [{"property": "T3/V0", "value": 301}, {"property": "T3/V1", "value": 304}, {"property": "T3/V3", "value": 307}, {"property": "T3/V4", "value": 310}]}}