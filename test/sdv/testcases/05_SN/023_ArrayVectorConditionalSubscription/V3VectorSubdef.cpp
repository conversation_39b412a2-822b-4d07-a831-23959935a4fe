extern "C" {
}
#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"

#include "V3TransConditionalSubBatch_Vector_tool.h"

// global value
int ret = 0;  // expect_value

class V3TransConditional_Vector_default : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    SnUserDataT *user_data;
    int *newValue;
    int *oldValue;

    virtual void SetUp();
    virtual void TearDown();
};

void V3TransConditional_Vector_default::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/start.sh ");
    ret = testEnvInit();
    ASSERT_EQ(GMERR_OK, ret);

    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
}

void V3TransConditional_Vector_default::TearDownTestCase()
{

    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    testEnvClean();;
}

void V3TransConditional_Vector_default::SetUp()
{
    user_data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(user_data, 0, sizeof(SnUserDataT));

    user_data->new_value = (int *)malloc(sizeof(int) * g_end_num * 10);
    memset(user_data->new_value, 0, sizeof(int) * g_end_num * 10);

    user_data->old_value = (int *)malloc(sizeof(int) * g_end_num * 10);
    memset(user_data->old_value, 0, sizeof(int) * g_end_num * 10);

    user_data->isReplace_insert = (bool *)malloc(sizeof(bool) * g_end_num * 10);
    memset(user_data->isReplace_insert, 0, sizeof(bool) * g_end_num * 10);

    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN();
}

void V3TransConditional_Vector_default::TearDown()
{
    AW_CHECK_LOG_END();
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);
    free(user_data->new_value);
    free(user_data->old_value);
    free(user_data->isReplace_insert);
    free(user_data);
}

/******************************************************************************************
 * Description  : 001 订阅vector节点  V1字段的default值，事件类型为insert 将订阅的默认值写入表中
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :对应V3中的订阅vector节点default值，写入默认值
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************************/
TEST_F(V3TransConditional_Vector_default, SN_023_V3TransConditional_Vector_default_001)
{
    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    char g_errorCode02[1024] = {0};
    (void)snprintf(g_errorCode02, 1024, "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode01, g_errorCode02);
    int32_t ret = 0, i, userDataIdx = 0;

    char string1[] = "string1";
    char string2[] = "1234567";

    readJanssonFile("V3Trans_schema_file/ArrayVectorConditionalSubscription_Tree_Vector_default.gmjson", &g_schema);
    EXPECT_NE((void *)NULL, g_schema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_schema);

    //创建订阅连接
    int chanRingLen = 256;
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("V3Trans_schema_file/V3TransConditional_Vector_default_subinfo_insert.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_sub_info);

    // 批量写入数据

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName1, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = g_start_num; i < g_end_num; i++) {
        if (i == 99) {
            ((int *)(user_data->new_value))[userDataIdx] = i;
            userDataIdx++;
        }
        V3_Trans_TestGmcInsertVertexBatch(g_stmt_sync, i, 0, 0, string1, g_array_num, g_vector_num);
        ret = GmcBatchAddDML(batch, g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        GmcResetVertex(g_stmt_sync, true);
    }

    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(g_end_num, totalNum);
    ASSERT_EQ(g_end_num, successNum);
    GmcBatchDestroy(batch);

    //等待推送数据
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 1);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt_sync, g_labelName1);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
}

/******************************************************************************************
 * Description  : 002 订阅vector节点  V1字段的default值，事件类型为delete 将订阅的默认值写入表中
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :对应V3中的订阅vector节点default值，写入默认值，删除数据
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************************/
TEST_F(V3TransConditional_Vector_default, SN_023_V3TransConditional_Vector_default_002)
{
    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    char g_errorCode02[1024] = {0};
    (void)snprintf(g_errorCode02, 1024, "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode01, g_errorCode02);
    int32_t ret = 0, i, userDataIdx = 0;

    char string1[] = "string1";
    char string2[] = "1234567";

    readJanssonFile("V3Trans_schema_file/ArrayVectorConditionalSubscription_Tree_Vector_default.gmjson", &g_schema);
    EXPECT_NE((void *)NULL, g_schema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_schema);

    //创建订阅连接
    int chanRingLen = 256;
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("V3Trans_schema_file/V3TransConditional_Vector_default_subinfo_delete.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, V3_Trans_defaultsn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_sub_info);

    // 批量写入数据

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName1, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = g_start_num; i < g_end_num; i++) {
        V3_Trans_TestGmcInsertVertexBatch(g_stmt_sync, i, 0, 0, string1, g_array_num, g_vector_num);
        ret = GmcBatchAddDML(batch, g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        GmcResetVertex(g_stmt_sync, true);
    }

    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(g_end_num, totalNum);
    ASSERT_EQ(g_end_num, successNum);
    GmcBatchDestroy(batch);
    // 批量删除数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName1, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    int start_num = 99;
    for (i = start_num; i < g_end_num; i++) {
        if (i == 99) {
            ((int *)(user_data->old_value))[userDataIdx] = i;
            userDataIdx++;
        }
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lableName_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        GmcResetVertex(g_stmt_sync, true);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(1, totalNum);
    ASSERT_EQ(1, successNum);
    GmcBatchDestroy(batch);
    //等待推送数据
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, 1);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt_sync, g_labelName1);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
}
/******************************************************************************************
 * Description  : 003 订阅vector节点V1字段的default值，事件类型为update,在更新数据的时候，进行append
                 操作,append的数据和默认值相同
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :对应V3中的订阅vector节点default值，写入默认值，更新append节点，更新为默认值
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************************/
TEST_F(V3TransConditional_Vector_default, SN_023_V3TransConditional_Vector_default_003)
{
    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    char g_errorCode02[1024] = {0};
    (void)snprintf(g_errorCode02, 1024, "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode01, g_errorCode02);
    int32_t ret = 0, i, userDataIdx = 0;

    char string1[] = "string1";
    char string2[] = "1234567";

    readJanssonFile(
        "V3Trans_schema_file/ArrayVectorConditionalSubscription_Tree_Vector_default_append.gmjson", &g_schema);
    EXPECT_NE((void *)NULL, g_schema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_schema);

    //创建订阅连接
    int chanRingLen = 256;
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("V3Trans_schema_file/V3TransConditional_Vector_default_subinfo_update.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callbackAppend, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_sub_info);

    //同步插入数据
    for (i = g_start_num; i < g_end_num; i++) {
        TestGmcInsertAppendNodeVertex(g_stmt_sync, i, 0, 0, string1, g_vector_num, g_labelName1);
    }

    //同步更新数据  更新数据做append操作  append的数据 和default 相同 301
    for (i = g_start_num; i < 1; i++) {
        if (i == 0) {
            ((int *)(user_data->new_value))[userDataIdx] = i + g_end_num;
            userDataIdx++;
        }
        TestGmcAppendUpdateVertexByIndexKey(
            g_stmt_sync, i, 1, 1, string2, g_array_num, g_vector_num, g_lableName_PK1, g_labelName1);
    }

    //等待推送数据
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_UPDATE, 1);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt_sync, g_labelName1);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
}

/******************************************************************************************
 * Description  : 004 订阅vector节点中的，事件类型为delete 将订阅的默认值写入表中，进行truncate操作
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :对应V3中的订阅vector节点数据，写入数据，truncate_table表数据，触发推送
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************************/

TEST_F(V3TransConditional_Vector_default, SN_023_V3TransConditional_Vector_default_004)
{
    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    char g_errorCode02[1024] = {0};
    (void)snprintf(g_errorCode02, 1024, "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode01, g_errorCode02);
    int32_t ret = 0, i, userDataIdx = 0;

    char string1[] = "string1";
    char string2[] = "1234567";

    readJanssonFile("V3Trans_schema_file/ArrayVectorConditionalSubscription_Tree_Vector_default.gmjson", &g_schema);
    EXPECT_NE((void *)NULL, g_schema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_schema);

    //创建订阅连接
    int chanRingLen = 256;
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("V3Trans_schema_file/V3TransConditional_Vector_default_subinfo_delete.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, V3_Trans_defaultsn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_sub_info);

    // 批量写入数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName1, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    int start_num = 99;
    for (int i = start_num; i < g_end_num; i++) {
        if (i == 99) {
            ((int *)(user_data->old_value))[userDataIdx] =
                i;  // 这里设置user_data->old_value 是为了truncate 结束后 进行推送强校验
            userDataIdx++;
        }
        V3_Trans_TestGmcInsertVertexBatch(g_stmt_sync, i, 0, 0, string1, g_array_num, g_vector_num);
        ret = GmcBatchAddDML(batch, g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        GmcResetVertex(g_stmt_sync, true);
    }

    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(1, totalNum);
    ASSERT_EQ(1, successNum);
    GmcBatchDestroy(batch);

    // truncate
    ret = GmcDeleteAllFast(g_stmt_sync, g_labelName1);
    EXPECT_EQ(GMERR_OK, ret);
    printf("\n[INFO] [ truncate success ] \n\n");

    //等待推送数据
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, 1);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt_sync, g_labelName1);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
}
