/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * @Author: ywx1157510
 * @Date: 2025-07-12
 */

#ifndef TOOL_H
#define TOOL_H

#define MAX_CMD_SIZE 1024
bool g_isDebug = true;
char *g_currentDir = getenv("PWD");

void SystemSnprintf(const char *format, ...)
{
    char command[MAX_CMD_SIZE] = {0};
    (void)memset(command, 0, sizeof(command));
    va_list p;
    va_start(p, format);
    (void)vsnprintf(command, MAX_CMD_SIZE, format, p);
    va_end(p);
    int32_t ret = system(command);
    if (ret) {
        AW_FUN_Log(LOG_INFO, "system cmd is excuted no ok !!!");
        AW_FUN_Log(LOG_INFO, "cmd is %s", command);
    }
}

// 获取关键字value
int GetViewFieldResultValue(const char *viewStatement, const char *key)
{
    char cmdOutput[MAX_CMD_SIZE] = {0};
    int32_t result[MAX_CMD_SIZE] = {0};
    int i = 0;
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));

    char command[MAX_CMD_SIZE] = {0};
    (void)snprintf(command, MAX_CMD_SIZE, "%s |grep %s |awk -F ':' '{print $2}'", viewStatement, key);

    FILE *pf = popen(command, "r");
    if (g_isDebug) {
        AW_FUN_Log(LOG_INFO, "cmd is %s", command);
    }
    if (pf == NULL) {
        printf("popen(%s) error./n", command);
    }
    EXPECT_NE((void *)NULL, pf);
    while (NULL != fgets(cmdOutput, MAX_CMD_SIZE, pf) && i < MAX_CMD_SIZE) {
        result[i] = atoi(cmdOutput);
        i++;
    };
    pclose(pf);
    return result[0];
}

// STORAGE_DISK_USAGE视图信息结构
typedef struct {
    char *tableName;        // 表名
    uint32_t diskLimit;     // 最大占用内存上限
    uint32_t diskUsage;     // 磁盘占用总情况
    uint32_t rowDiskUsage;  // 行存磁盘占用总情况
    uint32_t colDiskUsage;  // 列存磁盘占用总情况
    uint32_t phyTblNum;     // 物理表个数
    uint32_t rowCnt;        // 时序表行数
} DiskUsageInfoT;
// 用于查询日志表磁盘占用情况

int32_t GetDiskUsageValue(DiskUsageInfoT *labelInfo)
{
    char cmd[1024] = {};
    // STORAGE_DISK_USAGE
    (void)sprintf(cmd,
        "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s | grep -A 8 \'%s\'",
        g_connServerTsdb,
        labelInfo->tableName);
    labelInfo->diskLimit = GetViewValueByField(cmd, "DISK_LIMIT");
    labelInfo->diskUsage = GetViewValueByField(cmd, "DISK_USAGE");
    labelInfo->rowDiskUsage = GetViewValueByField(cmd, "ROW_DISK_USAGE");
    labelInfo->colDiskUsage = GetViewValueByField(cmd, "COL_DISK_USAGE");
    labelInfo->phyTblNum = GetViewValueByField(cmd, "PHY_TBL_NUM");
    labelInfo->rowCnt = GetViewValueByField(cmd, "ROW_CNT");
    SystemSnprintf(cmd);
    return 0;
}

// PHY_TBL_DISK_USAGE视图信息结构
typedef struct {
    char *logIdogicLabelName;  // 表名
    uint32_t interval;         // 分区间隔
    uint32_t cuCnt;            // 物理表cu个数
    uint32_t rowCnt;           // 物理表行数
} PhyDiskUsageInfoT;

// 用于查询物理表统计信息
int32_t GetPhyDiskUsageValue(PhyDiskUsageInfoT *labelInfo)
{
    char cmd[1024] = {};
    // PHY_TBL_DISK_USAGE
    (void)sprintf(cmd,
        "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s | grep -A 8 \'%s\'",
        g_connServerTsdb,
        labelInfo->logIdogicLabelName);
    labelInfo->interval = GetViewValueByField(cmd, "INTERVAL");
    labelInfo->cuCnt = GetViewValueByField(cmd, "CU_CNT");
    labelInfo->rowCnt = GetViewValueByField(cmd, "ROW_CNT");
    SystemSnprintf(cmd);
    return 0;
}

// TS_TBL_OPER_STATIS视图信息结构
typedef struct {
    char *tableName;             // 表名
    uint32_t dmlSuccessCnt;      // Dml操作成功次数。
    uint32_t dmlTotalFailedCnt;  // Dml操作失败次数。
    uint32_t dmlDiskFailedCnt;   // Dml操作磁盘故障次数。
    uint32_t dqlSuccessCnt;      // Dql操作成功次数。
    uint32_t dqlTotalFailedCnt;  // Dql操作失败次数。
    uint32_t dqlDiskFailedCnt;   // Dql操作磁盘故障次数。
} TsTblOperStatisInfoT;

// 用于统计时序逻辑表的DML/DQL操作失败成功次数，并显示统计访问磁盘失败的次数。
int32_t GetTsTblOperStatisValue(TsTblOperStatisInfoT *labelInfo)
{
    char cmd[1024] = {};
    // TS_TBL_OPER_STATIS
    (void)sprintf(cmd,
        "gmsysview -sql \"select * from 'V\\$TS_TBL_OPER_STATIS'\" -s %s | grep -A 8 \'%s\'",
        g_connServerTsdb,
        labelInfo->tableName);
    labelInfo->dmlSuccessCnt = GetViewValueByField(cmd, "DML_SUCCESS_CNT");
    labelInfo->dmlTotalFailedCnt = GetViewValueByField(cmd, "DML_TOTAL_FAILED_CNT");
    labelInfo->dmlDiskFailedCnt = GetViewValueByField(cmd, "DML_DISK_FAILED_CNT");
    labelInfo->dqlSuccessCnt = GetViewValueByField(cmd, "DQL_SUCCESS_CNT");
    labelInfo->dqlTotalFailedCnt = GetViewValueByField(cmd, "DQL_TOTAL_FAILED_CNT");
    labelInfo->dqlDiskFailedCnt = GetViewValueByField(cmd, "DQL_DISK_FAILED_CNT");
    SystemSnprintf(cmd);
    return 0;
}

// TS_TBL_PROPS视图信息结构
typedef struct {
    char *tableName;            // 表名
    uint32_t tableType;         // 表类型。
    uint32_t tablePath;         // 表的table_path属性，建表时指定。内存表不存在该属性，恒为空。
    uint32_t defaultTablePath;  // •建表时不指定table_path属性时，表文件的路径
    uint32_t interval;          // 表的interval属性的值。
    uint32_t intervalUnit;      // 表的interval属性的单位。
    uint32_t ttl;               // 表的ttl属性的值。
    uint32_t ttlUnit;           // 表的ttl属性的单位。没有ttl时，为空字符串。
    uint32_t diskLimit;         // •表的disk_limit属性，单位：byte。
    uint32_t maxSize;           // 表的max_size属性，该值对逻辑表为0。
} TsTblPropsInfoT;

// 用于查询日志表磁盘占用情况
int32_t GetTsTblPropsInfoValue(TsTblPropsInfoT *labelInfo)
{
    char cmd[1024] = {};
    // TS_TBL_PROPS
    (void)sprintf(cmd,
        "gmsysview -sql \"select * from 'V\\$TS_TBL_PROPS'\" -s %s | grep -A 8 \'%s\'",
        g_connServerTsdb,
        labelInfo->tableName);
    labelInfo->tableType = GetViewValueByField(cmd, "TABLE_TYPE");
    labelInfo->tablePath = GetViewValueByField(cmd, "TABLE_PATH");
    labelInfo->defaultTablePath = GetViewValueByField(cmd, "DEFAULT_TABLE_PATH");
    labelInfo->interval = GetViewValueByField(cmd, "INTERVAL_UNIT");
    labelInfo->intervalUnit = GetViewValueByField(cmd, "INTERVAL_UNIT");
    labelInfo->ttl = GetViewValueByField(cmd, "TTL");
    labelInfo->ttlUnit = GetViewValueByField(cmd, "TTL_UNIT");
    labelInfo->diskLimit = GetViewValueByField(cmd, "DISK_LIMIT");
    labelInfo->maxSize = GetViewValueByField(cmd, "MAX_SIZE");
    SystemSnprintf(cmd);
    return 0;
}

// 校验视图信息
int32_t CheckViewValue(DiskUsageInfoT *diskUsageInfo, PhyDiskUsageInfoT *phyDiskUsageInfo,
    TsTblOperStatisInfoT *tsTblOperStatisInfo, TsTblPropsInfoT *tsTblPropsInfo)
{
    int32_t ret = 1;
    if (diskUsageInfo) {
        ret = GetDiskUsageValue(diskUsageInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    if (phyDiskUsageInfo) {
        ret = GetPhyDiskUsageValue(phyDiskUsageInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    if (tsTblOperStatisInfo) {
        ret = GetTsTblOperStatisValue(tsTblOperStatisInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    if (tsTblPropsInfo) {
        ret = GetTsTblPropsInfoValue(tsTblPropsInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}

// 查询cu块数
int32_t GetCuNumValue(int32_t expectCuCount, const char *taleName = "testdba", const char *expectValue = "CU_CNT")
{
    char viewCmd[256] = {0};
    int cuCount = 1000;
    // 查视图
    (void)sprintf(viewCmd,
        "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE'\" -s %s | grep -A 8 \'%s\'",
        g_connServerTsdb,
        taleName);
    cuCount = GetViewFieldResultValue(viewCmd, expectValue);
    if (g_isDebug) {
        system(viewCmd);
    }
    if (expectCuCount != cuCount) {
        if (!g_isDebug) {
            system(viewCmd);
        }
        return -1;
    }
    return cuCount;
}

// 查询cu块数
int32_t GetAllCuNumValue(
    int32_t expectCuCount, const char *expectValue = "CU_CNT: 29", const char *taleName = "testdba")
{
    char viewCmd[256] = {0};
    int cuCount = 1000;
    // 查视图
    (void)sprintf(viewCmd,
        "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE'\" -s %s | grep -A 8 \'%s\' |grep \'%s\'|wc -l",
        g_connServerTsdb,
        taleName,
        expectValue);
    if (g_isDebug) {
        system(viewCmd);
    }
    char cmdOutput[MAX_CMD_SIZE] = {0};
    int32_t result[MAX_CMD_SIZE] = {0};
    int i = 0;
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));

    FILE *pf = popen(viewCmd, "r");
    if (g_isDebug) {
        AW_FUN_Log(LOG_INFO, "cmd is %s", viewCmd);
    }

    if (pf == NULL) {
        printf("popen(%s) error./n", viewCmd);
    }
    EXPECT_NE((void *)NULL, pf);
    while (NULL != fgets(cmdOutput, MAX_CMD_SIZE, pf) && i < MAX_CMD_SIZE) {
        cuCount = atoi(cmdOutput);
    };
    pclose(pf);
    if (expectCuCount != cuCount) {
        if (!g_isDebug) {
            system(viewCmd);
        }
        return -1;
    }
    return cuCount;
}
char g_nameSource[10][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
char g_ipSource[10][33] = {"10101040",
    "10101040",
    "98765432",
    "78000000",
    "ffaa0021",
    "00000000000000000000000000000000",
    "ffffffffffffffffffffffffffffffff",
    "0000000000000000ffffffffffffffff",
    "11110000000000000000000000002345",
    "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"};
char g_messageSource[10][160] = {
    "0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537", "0071 3521", "0010 0001", "", "3102 0022"};
char *g_nsSource[10] = {(char *)"david",
    (char *)"nut",
    (char *)"bob",
    (char *)"olivia",
    (char *)"tim",
    (char *)"lucy",
    (char *)"apple",
    (char *)"bob",
    (char *)"bob",
    (char *)"omgd"};

int DropTable(GmcStmtT *stmt, char *tableName)
{
    int ret = 0;
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "drop table %s;", tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    if (ret != 0) {
        AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNDEFINED_TABLE);
    }
    return ret;
}
// 含ttl的时序逻辑表
int CreateTsTable(GmcStmtT *stmt, char *tableNameA, char *tableNameB, bool isCompress = false, bool isTablePath = false,
    char *newDataDir = NULL)
{
    int ret = 0;
    char sqlCmd[1024] = {0};
    uint32_t cmdLen = 0;
    (void)DropTable(stmt, tableNameA);
    (void)DropTable(stmt, tableNameB);
    // 建表
    if (isCompress) {
        // AW_FUN_Log(LOG_STEP, "当前压缩模式为:no");
        (void)sprintf(sqlCmd,
            "create table %s(id integer, time integer, name char(64), ip inet, message blob(160), ns text) with "
            "(time_col = 'time', interval = '1 hour', compression = 'no', ttl = '1 hours',sensitive_col = 'name');",
            tableNameA);
        if (isTablePath) {
            (void)sprintf(sqlCmd,
                "create table %s(id integer, time integer, name char(64), ip inet, message blob(160), ns text) with "
                "(time_col = 'time', interval = '1 hour', compression = 'no', ttl = '1 hours',sensitive_col = 'name', "
                "table_path = '%s');",
                tableNameA,
                newDataDir);
        }
    } else {
        // AW_FUN_Log(LOG_STEP, "当前压缩模式为:默认");
        (void)sprintf(sqlCmd,
            "create table %s(id integer, time integer, name char(64), ip inet, message blob(160), ns text) with "
            "(time_col = 'time', interval = '1 hour', ttl = '1 hours',sensitive_col = 'name');",
            tableNameA);
        if (isTablePath) {
            (void)sprintf(sqlCmd,
                "create table %s(id integer, time integer, name char(64), ip inet, message blob(160), ns text) with "
                "(time_col = 'time', interval = '1 hour', compression = 'no', ttl = '1 hours',sensitive_col = 'name', "
                "table_path = '%s');",
                tableNameA,
                newDataDir);
        }
    }
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    if (ret) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    memset(sqlCmd, 256, 0);
    if (isCompress) {
        (void)sprintf(sqlCmd,
            "create table %s(id integer, time integer, message blob(160), ns text) with "
            "(time_col = 'time', interval = '1 hour', compression = 'no', ttl = '1 hours');",
            tableNameB);
        if (isTablePath) {
            (void)sprintf(sqlCmd,
                "create table %s(id integer, time integer, message blob(160), ns text) with "
                "(time_col = 'time', interval = '1 hour', compression = 'no', ttl = '1 hours', "
                "table_path = '%s');",
                tableNameB,
                newDataDir);
        }
    } else {
        (void)sprintf(sqlCmd,
            "create table %s(id integer, time integer, message blob(160), ns text) with "
            "(time_col = 'time', interval = '1 hour', ttl = '1 hours');",
            tableNameB);
        if (isTablePath) {
            (void)sprintf(sqlCmd,
                "create table %s(id integer, time integer, message blob(160), ns text) with "
                "(time_col = 'time', interval = '1 hour', compression = 'no', ttl = '1 hours', "
                "table_path = '%s');",
                tableNameB,
                newDataDir);
        }
    }

    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

int DropTsTable(GmcStmtT *stmt, char *tableNameA, char *tableNameB)
{
    int ret = 0;
    char sqlCmd[256] = {0};
    uint32_t cmdLen = 0;
    ret = DropTable(stmt, tableNameA);
    if (ret) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    if (tableNameB) {
        ret = DropTable(stmt, tableNameB);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 删表
    return ret;
}

void FreeMemForTextData(char **ns, int dataCount)
{
    if (ns == NULL) {
        return;
    }
    for (int i = 0; i < dataCount; i++) {
        if (ns[i] != NULL) {
            free(ns[i]);
            ns[i] = NULL;
        }
    }
    free(ns);
    ns = NULL;
}
// 为text类型数据申请内存
int32_t AllocMemForTextData(char ***ns, int dataCount)
{
    if (ns == NULL || dataCount <= 0) {
        return -1;
    }
    *ns = (char **)malloc(dataCount * sizeof(char *));
    if (*ns == NULL) {
        AW_FUN_Log(LOG_ERROR, "malloc ns failed\n");
        return FAILED;
    }
    for (int i = 0; i < dataCount; i++) {
        (*ns)[i] = (char *)malloc(65535 * sizeof(char));
        if ((*ns)[i] == NULL) {
            AW_FUN_Log(LOG_ERROR, "malloc ns[i] failed\n");
            FreeMemForTextData(*ns, i);  // 释放已分配部分
            return FAILED;
        }
        (void)memset((*ns)[i], 0, 65535);
    }
    return 0;
}

// 生成随机字符串
int32_t GenerateRandomString(char **textData, int dataCount)
{
    if (textData == NULL || dataCount <= 0) {
        return -1;
    }
    static bool srand_inited = false;
    if (!srand_inited) {
        unsigned int now = (unsigned int)time(NULL);
        if (now < 0) {
            return -1;
        }
        (void)srand(now);  // 初始化一次
        srand_inited = true;
    }
    char charset[] = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    int charsetLen = sizeof(charset) - 1;
    for (int i = 0; i < dataCount; i++) {
        int textLength = rand() % 65535 + 1;
        char str[65535] = {0};
        for (int n = 0; n < textLength - 1; n++) {
            int key = rand() % charsetLen;
            str[n] = charset[key];
        }
        str[textLength - 1] = '\0';
        (void)sprintf(textData[i], "%s", str);  // 修正参数顺序
    }
    return 0;
}

// 获取当前时间戳
int64_t GetCurrentTime()
{
    time_t now;
    now = time(NULL);  // 获取当前时间戳（秒）
    return ((now / 3600) + 1) * 3600;
}

// 生成长度递增字符串
void GenerateIncreaseString(char **textData, int dataCount)
{
    time_t time_T;
    time_T = time(NULL);
    (void)srand((uint32_t)time_T);
    char charset[] = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    int textLength = 0;
    char str[65535] = {0};
    for (int i = 0; i < dataCount; i++) {
        textLength = i + 1;
        for (int n = 0; n < textLength - 1; n++) {
            int key = n % (int)(sizeof(charset) - 1);
            str[n] = charset[key];
        }
        str[textLength - 1] = '\0';
        (void)sprintf(*(textData + i), str, sizeof(str));
    }
    // 定义用于生成随机字符串的字符集
}

// 查询
int32_t CheckDataCount(GmcStmtT *stmt, char *tableName)
{
    char sqlCmd[256] = {0};
    int dataCount = 1;
    uint32_t cmdLen = 0;
    (void)sprintf(sqlCmd, "SELECT * FROM %s order by time;", tableName);
    cmdLen = strlen(sqlCmd);
    int32_t ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return dataCount;
}

// 写第一张表A
// 连接句柄,表名，生成的数据量,time字段初始值，写入数据的初始位置
int32_t g_jumpTime = 0;
int insertDataToTableA(
    GmcStmtT *stmt, char *tableName, int count, int64_t startTime, int64_t startCount, char **ns, int32_t alterMode = 0)
{
    int ret = 0;
    int64_t id[count];
    (void)memset_s(id, count * sizeof(int64_t), 0, count * sizeof(int64_t));
    int64_t time[count];
    (void)memset_s(time, count * sizeof(int64_t), 0, count * sizeof(int64_t));
    char *name = (char *)malloc(count * 64);
    if (name == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    char *ip = (char *)malloc(count * 33);
    if (ip == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    char **message = (char **)malloc(count * sizeof(char *));
    if (message == NULL) {
        AW_FUN_Log(LOG_ERROR, "malloc failed\n");
        return FAILED;
    }
    for (int i = 0; i < count; i++) {
        message[i] = (char *)malloc(160 * sizeof(char));
        if (message[i] == NULL) {
            AW_FUN_Log(LOG_ERROR, "malloc failed\n");
            return FAILED;
        }
        (void)memset(message[i], 0, 160);
    }
    int32_t beginValue = startCount;
    for (int i = 0; i < count; i++) {
        id[i] = beginValue;
        time[i] = startTime + beginValue;
        if (g_jumpTime == 1) {
            time[0] = startTime - 1;
        }
        if (g_jumpTime == 2) {
            time[count - startCount - 1] = startTime + 3601;
        }
        memcpy((name + i * 64), (char *)g_nameSource[(beginValue) % 10], 64);
        memcpy(ip + i * 33, (char *)g_ipSource[(beginValue) % 10], 33);
        memcpy(*(message + i), (char *)g_messageSource[(beginValue) % 10], 160);
        beginValue++;
    }
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    RETURN_IFERR(ret);
    int32_t realCount = count - startCount;
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &realCount, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, time, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_FIXED, name, 64, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_FIXED, ip, 33, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_BYTES, message, sizeof(message[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (alterMode != 6) {
        ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_STRING, ns, sizeof(ns[0]), NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    switch (alterMode) {
        case 5:
            // 新增text类型字段
            ret = GmcBindCol(stmt, 10, (GmcDataTypeE)DB_DATATYPE_STRING, ns, sizeof(ns[0]), NULL);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        case 4:
            // 新增blob(160)类型字段
            ret = GmcBindCol(stmt, 9, (GmcDataTypeE)DB_DATATYPE_BYTES, message, sizeof(message[0]), NULL);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        case 3:
            // 新增inet类型字段
            ret = GmcBindCol(stmt, 8, (GmcDataTypeE)DB_DATATYPE_FIXED, ip, 33, NULL);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        case 2:
            // 新增char(64)类型字段
            ret = GmcBindCol(stmt, 7, (GmcDataTypeE)DB_DATATYPE_FIXED, name, 64, NULL);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        case 1:
            // 新增integer类型字段
            ret = GmcBindCol(stmt, 6, (GmcDataTypeE)DB_DATATYPE_INT64, id, 0, 0);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        default:
            break;
    }

    ret = GmcExecute(stmt);  // cu 落盘
    free(name);
    free(ip);
    for (int i = 0; i < count; i++) {
        free(message[i]);
        message[i] = NULL;
    }
    free(message);
    return ret;
}

// 读表A
int ReadTableA(
    GmcStmtT *stmt, char *tableName, int count, int64_t startTime, int64_t startCount, char **ns, int32_t alterMode = 0)
{
    int ret = 0;
    uint32_t size1 = sizeof(int64_t);
    int64_t scanId = 0;
    int64_t time1 = 0;
    char name1[64] = {0};
    char ip1[33] = {0};
    char message1[160] = {0};
    char ns1[65535] = {0};
    uint32_t size2 = sizeof(name1);
    uint32_t size3 = sizeof(ip1);
    uint32_t size4 = sizeof(message1);
    uint32_t size5 = sizeof(ns1);
    int64_t id[count];
    (void)memset_s(id, count * sizeof(int64_t), 0, count * sizeof(int64_t));
    int64_t time[count];
    (void)memset_s(time, count * sizeof(int64_t), 0, count * sizeof(int64_t));
    char *name = (char *)malloc(count * 64);
    if (name == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    char *ip = (char *)malloc(count * 33);
    if (ip == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    char **message = (char **)malloc(count * sizeof(char *));
    if (message == NULL) {
        AW_FUN_Log(LOG_ERROR, "malloc failed\n");
        return FAILED;
    }
    for (int i = 0; i < count; i++) {
        message[i] = (char *)malloc(160 * sizeof(char));
        if (message[i] == NULL) {
            AW_FUN_Log(LOG_ERROR, "malloc failed\n");
            return FAILED;
        }
        (void)memset(message[i], 0, 160);
    }
    int32_t beginValue = startCount;
    for (int i = 0; i < count; i++) {
        id[i] = beginValue;
        time[i] = startTime + beginValue;
        memcpy((name + i * 64), (char *)g_nameSource[(beginValue) % 10], 64);
        memcpy(ip + i * 33, (char *)g_ipSource[(beginValue) % 10], 33);
        memcpy(*(message + i), (char *)g_messageSource[(beginValue) % 10], 160);
        beginValue++;
    }
    bool isFinish;
    int cnt = 0;
    bool isNull;
    char sqlCmd[256] = {0};
    int dataCount = 1;
    uint32_t cmdLen = 0;
    (void)sprintf(sqlCmd, "SELECT * FROM %s order by time;", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    while (true) {
        ret = GmcFetch(stmt, &isFinish);  // 获取扫描的顶点
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (ret != GMERR_OK || isFinish) {
            break;
        }
        ret = GmcGetPropertyById(stmt, 0, &scanId, &size1, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(id[cnt], scanId);

        if (id[cnt] != scanId) {
            break;
        }
        ret = GmcGetPropertyById(stmt, 1, &time1, &size1, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(time[cnt], time1);
        printf("%lld\n", time1);

        ret = GmcGetPropertyById(stmt, 2, name1, &size2, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = strncmp(name + (cnt % 10) * size2, name1, size2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcGetPropertyById(stmt, 3, ip1, &size3, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = strncmp(ip + (cnt % 10) * size3, ip1, size3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcGetPropertyById(stmt, 4, message1, &size4, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_STR(message[cnt % 10], message1);

        ret = GmcGetVertexPropertySizeById(stmt, 5, &size5);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetPropertyById(stmt, 5, ns1, &size5, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_STR(ns[scanId], ns1);
        cnt++;
    }
    free(name);
    free(ip);
    for (int i = 0; i < count; i++) {
        free(message[i]);
        message[i] = NULL;
    }
    printf("cnt %d\n", cnt);
    free(message);
    return ret;
}

int ReadTableB(
    GmcStmtT *stmt, char *tableName, int count, int64_t startTime, int64_t startCount, char **ns, int32_t alterMode = 0)
{
    int ret = 0;
    uint32_t size1 = sizeof(int64_t);
    int64_t scanId = 0;
    int64_t time1 = 0;
    char name1[64] = {0};
    char ip1[33] = {0};
    char message1[160] = {0};
    char ns1[65535] = {0};
    uint32_t size2 = sizeof(name1);
    uint32_t size3 = sizeof(ip1);
    uint32_t size4 = sizeof(message1);
    uint32_t size5 = sizeof(ns1);
    int64_t id[count];
    (void)memset_s(id, count * sizeof(int64_t), 0, count * sizeof(int64_t));
    int64_t time[count];
    (void)memset_s(time, count * sizeof(int64_t), 0, count * sizeof(int64_t));
    char *name = (char *)malloc(count * 64);
    if (name == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    char *ip = (char *)malloc(count * 33);
    if (ip == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    char **message = (char **)malloc(count * sizeof(char *));
    if (message == NULL) {
        AW_FUN_Log(LOG_ERROR, "malloc failed\n");
        return FAILED;
    }
    for (int i = 0; i < count; i++) {
        message[i] = (char *)malloc(160 * sizeof(char));
        if (message[i] == NULL) {
            AW_FUN_Log(LOG_ERROR, "malloc failed\n");
            return FAILED;
        }
        (void)memset(message[i], 0, 160);
    }
    int32_t beginValue = startCount;
    for (int i = 0; i < count; i++) {
        id[i] = beginValue;
        time[i] = startTime + beginValue;
        memcpy((name + i * 64), (char *)g_nameSource[(beginValue) % 10], 64);
        memcpy(ip + i * 33, (char *)g_ipSource[(beginValue) % 10], 33);
        memcpy(*(message + i), (char *)g_messageSource[(beginValue) % 10], 160);
        beginValue++;
    }
    bool isFinish;
    int cnt = 0;
    bool isNull;
    char sqlCmd[256] = {0};
    int dataCount = 1;
    uint32_t cmdLen = 0;
    (void)sprintf(sqlCmd, "SELECT * FROM %s order by time;", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    while (true) {
        ret = GmcFetch(stmt, &isFinish);  // 获取扫描的顶点
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (ret != GMERR_OK || isFinish) {
            break;
        }
        ret = GmcGetPropertyById(stmt, 0, &scanId, &size1, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(id[cnt], scanId);

        if (id[cnt] != scanId) {
            break;
        }
        ret = GmcGetPropertyById(stmt, 2, message1, &size4, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_STR(message[cnt % 10], message1);

        ret = GmcGetVertexPropertySizeById(stmt, 3, &size5);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetPropertyById(stmt, 3, ns1, &size5, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_STR(ns[scanId], ns1);
        cnt++;
    }
    free(name);
    free(ip);
    for (int i = 0; i < count; i++) {
        free(message[i]);
        message[i] = NULL;
    }
    printf("cnt %d\n", cnt);
    free(message);
    return ret;
}

// 写第一张表B
// 连接句柄,表名，生成的数据量,time字段初始值，写入数据的初始位置
int insertDataToTableB(
    GmcStmtT *stmt, char *tableName, int count, int64_t startTime, int64_t startCount, char **ns, int32_t alterMode = 0)
{
    int ret = 0;
    int64_t id[count];
    (void)memset_s(id, count * sizeof(int64_t), 0, count * sizeof(int64_t));
    int64_t time[count];
    (void)memset_s(time, count * sizeof(int64_t), 0, count * sizeof(int64_t));
    char *name = (char *)malloc(count * 64);
    if (name == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    char *ip = (char *)malloc(count * 33);
    if (ip == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    char **message = (char **)malloc(count * sizeof(char *));
    if (message == NULL) {
        AW_FUN_Log(LOG_ERROR, "malloc failed\n");
        return FAILED;
    }
    for (int i = 0; i < count; i++) {
        message[i] = (char *)malloc(160 * sizeof(char));
        if (message[i] == NULL) {
            AW_FUN_Log(LOG_ERROR, "malloc failed\n");
            return FAILED;
        }
        (void)memset(message[i], 0, 160);
    }
    int32_t beginValue = startCount;
    for (int i = 0; i < count; i++) {
        id[i] = beginValue;
        time[i] = startTime + beginValue;
        memcpy((name + i * 64), (char *)g_nameSource[(beginValue) % 10], 64);
        memcpy(ip + i * 33, (char *)g_ipSource[(beginValue) % 10], 33);
        memcpy(*(message + i), (char *)g_messageSource[(beginValue) % 10], 160);
        beginValue++;
    }
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    RETURN_IFERR(ret);
    int32_t realCount = count - startCount;
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &realCount, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, time, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_BYTES, message, sizeof(message[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (alterMode != 6) {
        ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_STRING, ns, sizeof(ns[0]), NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    switch (alterMode) {
        case 5:
            // 新增text类型字段
            ret = GmcBindCol(stmt, 8, (GmcDataTypeE)DB_DATATYPE_STRING, ns, sizeof(ns[0]), NULL);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        case 4:
            // 新增blob(160)类型字段
            ret = GmcBindCol(stmt, 7, (GmcDataTypeE)DB_DATATYPE_BYTES, message, sizeof(message[0]), NULL);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        case 3:
            // 新增inet类型字段
            ret = GmcBindCol(stmt, 6, (GmcDataTypeE)DB_DATATYPE_FIXED, ip, 33, NULL);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        case 2:
            // 新增char(64)类型字段
            ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_FIXED, name, 64, NULL);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        case 1:
            // 新增integer类型字段
            ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, id, 0, 0);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        default:
            break;
    }
    ret = GmcExecute(stmt);  // cu 落盘
    free(name);
    free(ip);
    for (int i = 0; i < count; i++) {
        free(message[i]);
        message[i] = NULL;
    }
    free(message);
    return ret;
}

void *InsertTableB(void *arg)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    char tableNameB[20] = "testdbB";
    int ret = 0;
    // 构造多时区数据
    int32_t nowTimeValue = GetCurrentTime();
    const int32_t interval = 100;  // 时间分区数量
    int newStartTime[interval] = {0};
    // 数组长度和大小不要计算错误
    for (int32_t i = 0; i < sizeof(newStartTime) / sizeof(newStartTime[0]); i++) {
        newStartTime[i] = nowTimeValue + 3600 * (1 + i);
    }

    // 准备数据
    char **ns = NULL;
    int32_t dataCount = 61;
    ret = AllocMemForTextData(&ns, dataCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GenerateRandomString(ns, dataCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 小批量数据
    int32_t batchCount = dataCount;  // 每批写入数据量
    int32_t totalCount = dataCount;  // 当前已写入的数据
    int32_t startCount = 0;

    // 写入数据
    for (int32_t k = 0; k < interval; k++) {
        startCount = 0;
        batchCount = 1;
        totalCount = 1;
        int32_t startTime = newStartTime[k];
        for (int32_t i = 0; i < dataCount; i++) {
            ret = insertDataToTableB(stmt, tableNameB, totalCount, startTime, startCount, ns + startCount);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (ret) {
                AW_FUN_Log(LOG_STEP, "insertDataToTableB i %d is no ok ret is %d", i, ret);
                break;
            }
            startCount += batchCount;
            totalCount += batchCount;
        }
        if (ret) {
            AW_FUN_Log(LOG_STEP, "insertDataToTableB k %d is no ok ret is %d", k, ret);
            break;
        }
    }

    // 校验数据条数
    AW_MACRO_EXPECT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 3", tableNameB));
    AW_MACRO_EXPECT_EQ_INT(dataCount * 100, CheckDataCount(stmt, tableNameB));
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    FreeMemForTextData(ns, dataCount);  // 清理前序用例申请的内存
    return nullptr;
}

void *InsertTableA(void *arg)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    char tableNameA[20] = "testdba";
    int ret = 0;
    // 构造多时区数据
    int32_t nowTimeValue = GetCurrentTime();
    const int32_t interval = 100;  // 时间分区数量
    int newStartTime[interval] = {0};
    // 数组长度和大小不要计算错误
    for (int32_t i = 0; i < sizeof(newStartTime) / sizeof(newStartTime[0]); i++) {
        newStartTime[i] = nowTimeValue + 3600 * (1 + i);
    }

    // 准备数据
    char **ns = NULL;
    int32_t dataCount = 61;
    ret = AllocMemForTextData(&ns, dataCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GenerateRandomString(ns, dataCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 小批量数据
    int32_t batchCount = dataCount;  // 每批写入数据量
    int32_t totalCount = dataCount;  // 当前已写入的数据
    int32_t startCount = 0;

    // 写入数据
    for (int32_t k = 0; k < interval; k++) {
        startCount = 0;
        batchCount = 1;
        totalCount = 1;
        int32_t startTime = newStartTime[k];
        for (int32_t i = 0; i < dataCount; i++) {
            ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (ret) {
                AW_FUN_Log(LOG_STEP, "insertDataToTableB i %d is no ok ret is %d", i, ret);
                break;
            }
            startCount += batchCount;
            totalCount += batchCount;
        }
        if (ret) {
            AW_FUN_Log(LOG_STEP, "insertDataToTableB k %d is no ok ret is %d", k, ret);
            break;
        }
    }

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    FreeMemForTextData(ns, dataCount);  // 清理前序用例申请的内存
    return nullptr;
}

// 重复建表删表
void *CreateDropTables(void *arg)
{
    AW_FUN_Log(LOG_STEP, "CreateDropTables begin");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int32_t ret = 1;
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t tableNum = 500;

    for (int32_t k = 0; k < tableNum; k++) {
        char tableName1[48] = "tableName1";
        (void)sprintf(tableName1, "tableName1_%d", k);
        char tableName2[48] = "tableName2";
        (void)sprintf(tableName2, "tableName2_%d", k);
        ret = CreateTsTable(stmt, tableName1, tableName2, k % 2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (ret) {
            AW_FUN_Log(LOG_STEP, "【CreateDropTables】CreateTsTable no ok k is %d", k);
            break;
        }
    }
    for (int32_t k = 0; k < tableNum; k++) {
        char tableName1[48] = "tableName1";
        (void)sprintf(tableName1, "tableName1_%d", k);
        char tableName2[48] = "tableName2";
        (void)sprintf(tableName2, "tableName2_%d", k);
        ret = DropTsTable(stmt, tableName1, tableName2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (ret) {
            AW_FUN_Log(LOG_STEP, "【CreateDropTables】DropTsTable no ok k is %d", k);
        }
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "CreateDropTables end");
    return nullptr;
}

// 重复读表
void *QueryTable(void *arg)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int32_t tableNum = 10;
    int32_t ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 0; i < 10; i++) {
        for (int32_t k = 0; k < tableNum; k++) {
            char tableName1[48] = "tableName1";
            (void)sprintf(tableName1, "tableName1_%d", k);
            char tableName2[48] = "tableName2";
            (void)sprintf(tableName2, "tableName2_%d", k);
            AW_MACRO_EXPECT_EQ_INT(310, CheckDataCount(stmt, tableName1));
            AW_MACRO_EXPECT_EQ_INT(310, CheckDataCount(stmt, tableName2));
        }

        usleep(1000);
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return nullptr;
}

// 并发truncate表
void *TruncateTable(void *arg)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int32_t tableNum = 10;
    int32_t ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char sqlCmdTruncate[128] = "truncate table testdbA;";
    ret = GmcExecDirect(stmt, sqlCmdTruncate, 128);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return nullptr;
}

// 并发alter ttl
void *AlterTtlTable(void *arg)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int32_t tableNum = 10;
    int32_t ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[128] = "truncate table testdbA;";
    (void)sprintf(sqlCmd, "alter table testdbA SET (ttl = '4 hours');");
    ret = GmcExecDirect(stmt, sqlCmd, 128);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return nullptr;
}

// 并发alter disk_Limit
void *AlterDiskLimitTable(void *arg)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int32_t tableNum = 10;
    int32_t ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[128] = "truncate table testdbA;";
    (void)sprintf(sqlCmd, "alter table testdbA SET (disk_limit = '100 MB');");
    ret = GmcExecDirect(stmt, sqlCmd, 128);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return nullptr;
}

// 并发alter 新增列
void *AlterNewColumnTable(void *arg)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int32_t tableNum = 10;
    int32_t ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[128] = "truncate table testdbA;";
    (void)sprintf(sqlCmd, "alter table if exists testdbA ADD COLUMN ADDCOLUMN1 integer;");
    ret = GmcExecDirect(stmt, sqlCmd, 128);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return nullptr;
}

// 并发老化数据
void *AgeOutTable(void *arg)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int32_t tableNum = 10;
    int32_t ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[128] = "truncate table testdbA;";
    (void)sprintf(sqlCmd, "SELECT tsdb_aging('testdbA');");
    ret = GmcExecDirect(stmt, sqlCmd, 128);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return nullptr;
}

// 在环境中预置多张表以及写入数据
int32_t PrepareEnvTableAndData(GmcStmtT *stmt, int32_t tableNum)
{
    int32_t ret = 1;
    for (int32_t k = 0; k < tableNum; k++) {
        char tableName1[48] = "tableName1";
        (void)sprintf(tableName1, "tableName1_%d", k);
        char tableName2[48] = "tableName2";
        (void)sprintf(tableName2, "tableName2_%d", k);
        ret = CreateTsTable(stmt, tableName1, tableName2, k % 2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 准备数据
    char **ns = NULL;
    int32_t dataCount = 1000;
    ret = AllocMemForTextData(&ns, dataCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GenerateRandomString(ns, dataCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t batchCount = 1;    // 每批写入1条数据
    int32_t totalCount = 100;  // 数据量
    int startCount = 0;
    int32_t nowTimeValue = GetCurrentTime();
    int newStartTime[100] = {0};
    // 数组长度和大小不要计算错误
    for (int32_t i = 0; i < sizeof(newStartTime) / sizeof(newStartTime[0]); i++) {
        newStartTime[i] = nowTimeValue + 3600 * (1 + i);
    }

    int startTime = 0;
    for (int32_t k = 0; k < tableNum; k++) {
        batchCount = 1;
        totalCount = 31;
        startCount = 0;
        char tableName1[48] = "tableName1";
        (void)sprintf(tableName1, "tableName1_%d", k);
        char tableName2[48] = "tableName2";
        (void)sprintf(tableName2, "tableName2_%d", k);
        startTime = newStartTime[k];
        for (int32_t i = 0; i < 10; i++) {
            ret = insertDataToTableA(stmt, tableName1, totalCount, startTime, startCount, ns + startCount);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = insertDataToTableB(stmt, tableName2, totalCount, startTime, startCount, ns + startCount);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            startCount += batchCount;
            totalCount += batchCount;
        }
    }
    FreeMemForTextData(ns, dataCount);  // 清理申请的内存
    return ret;
}

// 清理预置的表
int32_t CleanPrepareEnvTable(GmcStmtT *stmt, int32_t tableNum)
{
    int32_t ret;
    for (int32_t k = 0; k < tableNum; k++) {
        char tableName1[48] = "tableName1";
        (void)sprintf(tableName1, "tableName1_%d", k);
        char tableName2[48] = "tableName2";
        (void)sprintf(tableName2, "tableName2_%d", k);
        ret = DropTsTable(stmt, tableName1, tableName2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}
#endif /* _TOOL_H_ */
