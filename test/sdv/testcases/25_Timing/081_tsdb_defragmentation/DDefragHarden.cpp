/*****************************************************************************
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
Description  : 【时序加固】TSDB磁盘整理加固
Notes        :
History      :
Author       :  ywx1157510
Modification :
Date         : 2025/07/12
*****************************************************************************/
#include <time.h>
#include <errno.h>
#include "gtest/gtest.h"
#include "t_rd_ts.h"
#include "tool.h"
static GmcConnT *conn = NULL;
static GmcStmtT *stmt = NULL;
Status ret = 0;
char tableNameA[20] = "testdba";
char tableNameB[20] = "testdbb";

// 重启服务
void RestartTsdb()
{
    int32_t ret = 1;
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    conn = NULL;
    stmt = NULL;
    testEnvClean();

    // 落盘重启
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");

    testEnvInit();
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

class DDefragHarden : public testing::Test {
protected:
    static void SetUpTestCase()
    {
// euler和仿真环境清共享内存
#if defined RUN_INDEPENDENT
        system("sh $TEST_HOME/tools/stop.sh -f");
#endif
        system("sh $TEST_HOME/tools/stop.sh -ts");
        TsDefulatDbFileClean();
        system("sh $TEST_HOME/tools/modifyCfg.sh -ts recover");
        system("sh $TEST_HOME/tools/modifyCfg.sh -ts cuCompactEnable=1");   // 开启列存磁盘整理
        system("sh $TEST_HOME/tools/modifyCfg.sh -ts tsLcmCheckPeriod=3");  // 设置数据过期时间检查周期为3s
        system("sh $TEST_HOME/tools/start.sh -ts");
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        // 创建epoll监听线程
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        ret = TestTsGmcConnect(&conn, &stmt, 0);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        testGmcDisconnect(conn, stmt);
        close_epoll_thread();
        testEnvClean();
        TsDefulatDbFileClean();
        RecoverTsCiCfg();
        system("rm -rf ./newData");
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void DDefragHarden::SetUp()
{
    AW_CHECK_LOG_BEGIN();
}

void DDefragHarden::TearDown()
{
    AW_CHECK_LOG_END();
}

// 001.ttl和磁盘整理并发
// 分批写入数据，部分数据过期,连续写入30批次数据时，前面的部分批次数据已经过期，查询cu数量预期不发生磁盘整理(压缩模式：no)
TEST_F(DDefragHarden, Timing_081_DDefragHarden_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    uint32_t cmdLen = 0;
    bool isCompress = true;  // 压缩模式 no,默认
    // 建表
    while (true) {
        // 创建需要使用到的表
        int ret = CreateTsTable(stmt, tableNameA, tableNameB, isCompress);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 准备数据
        char **ns = NULL;
        int32_t dataCount = 1000;
        ret = AllocMemForTextData(&ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GenerateRandomString(ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 小批量数据
        int32_t batchCount = 1;                       // 每批写入29条数据
        int32_t totalCount = 1;                       // 当前已写入的数据量
        int oldStartTime = GetCurrentTime() - 10800;  // 当前时间减2H已过期数据
        int newStartTime = GetCurrentTime() + 7200;   // 当前时间加2H未过期数据
        int startCount = 0;

        // 小批量写入前15批过期数据&后15批未过期数据
        int startTime = oldStartTime;
        for (int32_t i = 0; i < 30; i++) {
            startTime = (i < 15) ? oldStartTime : newStartTime;
            ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            startCount += batchCount;
            totalCount += batchCount;
        }

        sleep(2);  // 等待后台数据开始过期

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(15, GetCuNumValue(15));

        for (int32_t i = 0; i < 15; i++) {
            startTime = newStartTime;
            ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            startCount += batchCount;
            totalCount += batchCount;
        }

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(1, GetCuNumValue(1));

        sleep(8);  // 等待后台数据过期

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(30 * batchCount, CheckDataCount(stmt, tableNameA));

        ret = ReadTableA(stmt, tableNameA, totalCount - batchCount, startTime, 15 * batchCount, ns);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 删表
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameA, tableNameB));
        FreeMemForTextData(ns, dataCount);  // 清理前序用例申请的内存

        // 查询环境是否清理干净
        // DiskUsageInfoT PhyDiskUsageInfoT TsTblOperStatisInfoT TsTblPropsInfoT
        DiskUsageInfoT diskUsageInfo{tableNameA};
        PhyDiskUsageInfoT phyDiskUsageInfo{tableNameA};
        TsTblOperStatisInfoT tsTblOperStatisInfo{tableNameA};
        TsTblPropsInfoT tsTblPropsInfo{tableNameA};
        ret = CheckViewValue(&diskUsageInfo, &phyDiskUsageInfo, &tsTblOperStatisInfo, &tsTblPropsInfo);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (isCompress == false) {
            break;
        }
        isCompress = false;
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002.写入小批量数据小于30次，alter新增列（全部数据类型），触发磁盘整理，再次写入数据再次到达30次再次触发磁盘整理
// (新增多种类型后再次触发磁盘整理）(压缩模式：no) 新增整型
TEST_F(DDefragHarden, Timing_081_DDefragHarden_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    uint32_t cmdLen = 0;
    bool isCompress = true;                       // 压缩模式 no,默认
    int32_t batchCount = 3;                       // 每批写入29条数据
    int32_t totalCount = 3;                       // 当前已写入的数据量
    int oldStartTime = GetCurrentTime() - 10800;  // 当前时间减2H已过期数据
    int newStartTime = GetCurrentTime() + 7200;   // 当前时间加2H未过期数据
    int startCount = 0;
    // 建表
    while (true) {
        // 创建需要使用到的表
        int ret = CreateTsTable(stmt, tableNameA, tableNameB, isCompress);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 准备数据
        char **ns = NULL;
        int32_t dataCount = 1000;
        ret = AllocMemForTextData(&ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GenerateRandomString(ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 小批量数据
        batchCount = 3;                           // 每批写入3条数据
        totalCount = 3;                           // 当前已写入的数据量
        oldStartTime = GetCurrentTime() - 10800;  // 当前时间减2H已过期数据
        newStartTime = GetCurrentTime() + 7200;   // 当前时间加2H未过期数据
        startCount = 0;

        // 小批量写入前15批过期数据&后15批未过期数据
        int startTime = newStartTime;
        for (int32_t i = 0; i < 29; i++) {
            ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            startCount += batchCount;
            totalCount += batchCount;
        }

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(29, GetCuNumValue(29));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(29 * batchCount, CheckDataCount(stmt, tableNameA));

        /********************使用alter新增字段integer ********************/
        AW_FUN_Log(LOG_STEP, "使用alter新增字段integer.");
        (void)memset(sqlCmd, 0, sizeof(sqlCmd));
        (void)(void)sprintf(sqlCmd, "alter table if exists %s ADD COLUMN ADDCOLUMN1 integer;", tableNameA);
        ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 再次写入数据
        for (int32_t i = 0; i < 1; i++) {
            ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount, 1);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            startCount += batchCount;
            totalCount += batchCount;
        }
        // cu块 1(29) + 1(1)
        AW_MACRO_ASSERT_EQ_INT(2, GetCuNumValue(2));
        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(30 * batchCount, CheckDataCount(stmt, tableNameA));

        // 再次写入数据触发磁盘整理
        for (int32_t i = 0; i < 29; i++) {
            ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount, 1);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            startCount += batchCount;
            totalCount += batchCount;
        }

        // cu块 1(29) + 1(30)
        AW_MACRO_ASSERT_EQ_INT(2, GetCuNumValue(2));
        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT((29 + 30) * batchCount, CheckDataCount(stmt, tableNameA));

        // 再次写入数据5批数据用于下一次触发 磁盘整理
        for (int32_t i = 0; i < 5; i++) {
            ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount, 1);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            startCount += batchCount;
            totalCount += batchCount;
        }

        // 查cu块统计视图 cu块 1(29) + 1(30) + 5(1)
        AW_MACRO_ASSERT_EQ_INT(7, GetCuNumValue(7));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT((29 + 30 + 5) * batchCount, CheckDataCount(stmt, tableNameA));

        /********************使用alter新增字段char(64) ********************/
        AW_FUN_Log(LOG_STEP, "使用alter新增字段char(64).");
        (void)memset(sqlCmd, 0, sizeof(sqlCmd));
        (void)(void)sprintf(sqlCmd, "alter table if exists %s ADD COLUMN ADDCOLUMN2 char(64);", tableNameA);
        ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 再次写入数据
        for (int32_t i = 0; i < 1; i++) {
            ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount, 2);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            startCount += batchCount;
            totalCount += batchCount;
        }
        // cu块 1(29) + 1(30) + 1(5) + 1(1)
        AW_MACRO_ASSERT_EQ_INT(4, GetCuNumValue(4));
        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT((29 + 30 + 5 + 1) * batchCount, CheckDataCount(stmt, tableNameA));

        // 再次写入数据触发磁盘整理
        for (int32_t i = 0; i < 29; i++) {
            ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount, 2);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            startCount += batchCount;
            totalCount += batchCount;
        }

        // cu块 1(29) + 1(30) + 1(5) + 1(30)
        AW_MACRO_ASSERT_EQ_INT(4, GetCuNumValue(4));
        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT((29 + 30 + 5 + 30) * batchCount, CheckDataCount(stmt, tableNameA));

        // 再次写入30批未绑定新增的列的数据预期cu块不合并
        for (int32_t i = 0; i < 30; i++) {
            ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount, 1);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            startCount += batchCount;
            totalCount += batchCount;
        }

        // cu块 1(29) + 1(30) + 1(5) + 1(30) + 30(1)
        AW_MACRO_ASSERT_EQ_INT(34, GetCuNumValue(34));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT((29 + 30 + 5 + 30 + 30) * batchCount, CheckDataCount(stmt, tableNameA));

        // 再次写入数据5批数据用于下一次触发 磁盘整理
        for (int32_t i = 0; i < 5; i++) {
            ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount, 2);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            startCount += batchCount;
            totalCount += batchCount;
        }

        // 查cu块统计视图 cu块 1(29) + 1(30) + 1(5) + 1(30) + 30(1) + 5(1)
        AW_MACRO_ASSERT_EQ_INT(39, GetCuNumValue(39));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT((29 + 30 + 5 + 30 + 30 + 5) * batchCount, CheckDataCount(stmt, tableNameA));
        /********************使用alter新增字段inet ********************/
        AW_FUN_Log(LOG_STEP, "使用alter新增字段inet.");

        (void)memset(sqlCmd, 0, sizeof(sqlCmd));
        (void)(void)sprintf(sqlCmd, "alter table if exists %s ADD COLUMN ADDCOLUMN3 inet;", tableNameA);
        ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 再次写入数据
        for (int32_t i = 0; i < 1; i++) {
            ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount, 3);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            startCount += batchCount;
            totalCount += batchCount;
        }
        // cu块 1(29) + 1(30) + 1(5) + 1(30) + 30(1) + 1(5) + 1(1)
        AW_MACRO_ASSERT_EQ_INT(36, GetCuNumValue(36));
        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT((29 + 30 + 5 + 30 + 30 + 5 + 1) * batchCount, CheckDataCount(stmt, tableNameA));

        // 再次写入数据触发磁盘整理
        for (int32_t i = 0; i < 29; i++) {
            ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount, 3);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            startCount += batchCount;
            totalCount += batchCount;
        }

        // cu块 1(29) + 1(30) + 1(5) + 1(30) + 30(1) + 1(5) + 1(30)
        AW_MACRO_ASSERT_EQ_INT(36, GetCuNumValue(36));
        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT((29 + 30 + 5 + 30 + 30 + 5 + 30) * batchCount, CheckDataCount(stmt, tableNameA));

        // 再次写入15批未绑定新增的列的数据预期cu块不合并
        for (int32_t i = 0; i < 15; i++) {
            ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount, 2);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            startCount += batchCount;
            totalCount += batchCount;
        }

        // cu块 1(29) + 1(30) + 1(5) + 1(30) + 30(1) + 1(5) + 1(30) +15(1)
        AW_MACRO_ASSERT_EQ_INT(51, GetCuNumValue(51));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT((29 + 30 + 5 + 30 + 30 + 5 + 30 + 15) * batchCount, CheckDataCount(stmt, tableNameA));

        // 再次写入数据5批数据用于下一次触发 磁盘整理
        for (int32_t i = 0; i < 5; i++) {
            ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount, 3);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            startCount += batchCount;
            totalCount += batchCount;
        }

        // 查cu块统计视图 cu块 1(29) + 1(30) + 1(5) + 1(30) + 30(1) + 1(5) + 1(30) +15(1) + 5(1)
        AW_MACRO_ASSERT_EQ_INT(56, GetCuNumValue(56));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(
            (29 + 30 + 5 + 30 + 30 + 5 + 30 + 15 + 5) * batchCount, CheckDataCount(stmt, tableNameA));

        /********************4.使用alter新增字段blob(160) ********************/
        AW_FUN_Log(LOG_STEP, "4.使用alter新增字段blob(160).");

        (void)memset(sqlCmd, 0, sizeof(sqlCmd));
        (void)(void)sprintf(sqlCmd, "alter table if exists %s ADD COLUMN ADDCOLUMN4 blob(160);", tableNameA);
        // (void)(void)sprintf(sqlCmd, "alter table if exists %s ADD COLUMN ADDCOLUMN5 text;", tableNameA);
        ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 再次写入数据
        for (int32_t i = 0; i < 1; i++) {
            ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount, 4);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            startCount += batchCount;
            totalCount += batchCount;
        }
        // cu块 1(29) + 1(30) + 1(5) + 1(30) + 30(1) + 1(5) + 1(30) +15(1) + 1(5) + 1(1)
        AW_MACRO_ASSERT_EQ_INT(53, GetCuNumValue(53));
        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(
            (29 + 30 + 5 + 30 + 30 + 5 + 30 + 15 + 5 + 1) * batchCount, CheckDataCount(stmt, tableNameA));

        // 再次写入数据触发磁盘整理
        for (int32_t i = 0; i < 29; i++) {
            ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount, 4);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            startCount += batchCount;
            totalCount += batchCount;
        }

        // cu块 1(29) + 1(30) + 1(5) + 1(30) + 30(1) + 1(5) + 1(30) +15(1) + 1(5) + 1(30)
        AW_MACRO_ASSERT_EQ_INT(53, GetCuNumValue(53));
        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(
            (29 + 30 + 5 + 30 + 30 + 5 + 30 + 15 + 5 + 30) * batchCount, CheckDataCount(stmt, tableNameA));

        // 再次写入7批未绑定新增的列的数据预期cu块不合并
        for (int32_t i = 0; i < 7; i++) {
            ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount, 3);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            startCount += batchCount;
            totalCount += batchCount;
        }

        // cu块 1(29) + 1(30) + 1(5) + 1(30) + 30(1) + 1(5) + 1(30) +15(1) + 1(5) + 1(30) + 7(1)
        AW_MACRO_ASSERT_EQ_INT(60, GetCuNumValue(60));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(
            (29 + 30 + 5 + 30 + 30 + 5 + 30 + 15 + 5 + 30 + 7) * batchCount, CheckDataCount(stmt, tableNameA));

        // 再次写入数据5批数据用于下一次触发 磁盘整理
        for (int32_t i = 0; i < 5; i++) {
            ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount, 4);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            startCount += batchCount;
            totalCount += batchCount;
        }

        // 查cu块统计视图 cu块 1(29) + 1(30) + 1(5) + 1(30) + 30(1) + 1(5) + 1(30) +15(1) + 1(5) + 1(30) + 7(1) + 5(1)
        AW_MACRO_ASSERT_EQ_INT(65, GetCuNumValue(65));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(
            (29 + 30 + 5 + 30 + 30 + 5 + 30 + 15 + 5 + 30 + 7 + 5) * batchCount, CheckDataCount(stmt, tableNameA));

        /********************5.使用alter新增字段text ********************/
        AW_FUN_Log(LOG_STEP, "5.使用alter新增字段text.");

        (void)memset(sqlCmd, 0, sizeof(sqlCmd));
        (void)(void)sprintf(sqlCmd, "alter table if exists %s ADD COLUMN ADDCOLUMN5 text;", tableNameA);
        ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 再次写入数据
        for (int32_t i = 0; i < 1; i++) {
            ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount, 5);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            startCount += batchCount;
            totalCount += batchCount;
        }
        // cu块 1(29) + 1(30) + 1(5) + 1(30) + 30(1) + 1(5) + 1(30) +15(1) + 1(5) + 1(30) + 7(1) + 1(5) + 1(1)
        AW_MACRO_ASSERT_EQ_INT(62, GetCuNumValue(62));
        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(
            (29 + 30 + 5 + 30 + 30 + 5 + 30 + 15 + 5 + 30 + 7 + 5 + 1) * batchCount, CheckDataCount(stmt, tableNameA));

        // 再次写入数据触发磁盘整理
        for (int32_t i = 0; i < 29; i++) {
            ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount, 5);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            startCount += batchCount;
            totalCount += batchCount;
        }

        // cu块 1(29) + 1(30) + 1(5) + 1(30) + 30(1) + 1(5) + 1(30) +15(1) + 1(5) + 1(30) + 7(1) + 1(5) + 1(30)
        AW_MACRO_ASSERT_EQ_INT(62, GetCuNumValue(62));
        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(
            (29 + 30 + 5 + 30 + 30 + 5 + 30 + 15 + 5 + 30 + 7 + 5 + 30) * batchCount, CheckDataCount(stmt, tableNameA));

        // 再次写入9批未绑定新增的列的数据预期cu块不合并
        for (int32_t i = 0; i < 9; i++) {
            ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount, 4);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            startCount += batchCount;
            totalCount += batchCount;
        }

        // cu块 1(29) + 1(30) + 1(5) + 1(30) + 30(1) + 1(5) + 1(30) +15(1) + 1(5) + 1(30) + 7(1) + 1(5) + 1(30)+ 9(1)
        AW_MACRO_ASSERT_EQ_INT(71, GetCuNumValue(71));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT((29 + 30 + 5 + 30 + 30 + 5 + 30 + 15 + 5 + 30 + 7 + 5 + 30 + 9) * batchCount,
            CheckDataCount(stmt, tableNameA));

        // 再次写入数据5批数据
        for (int32_t i = 0; i < 5; i++) {
            ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount, 5);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            startCount += batchCount;
            totalCount += batchCount;
        }

        // 查cu块统计视图 cu块 1(29) + 1(30) + 1(5) + 1(30) + 30(1) + 1(5) + 1(30) +15(1) + 1(5) + 1(30) + 7(1) + 1(5) +
        // 1(30)+ 9(1)+ 5(1)
        AW_MACRO_ASSERT_EQ_INT(76, GetCuNumValue(76));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT((29 + 30 + 5 + 30 + 30 + 5 + 30 + 15 + 5 + 30 + 7 + 5 + 30 + 9 + 5) * batchCount,
            CheckDataCount(stmt, tableNameA));
        ret = ReadTableA(stmt,
            tableNameA,
            (29 + 30 + 5 + 30 + 30 + 5 + 30 + 15 + 5 + 30 + 7 + 5 + 30 + 9 + 5) * batchCount,
            startTime,
            0,
            ns);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 删表
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameA, tableNameB));
        FreeMemForTextData(ns, dataCount);  // 清理前序用例申请的内存

        // 查询环境是否清理干净
        // DiskUsageInfoT PhyDiskUsageInfoT TsTblOperStatisInfoT TsTblPropsInfoT
        DiskUsageInfoT diskUsageInfo{tableNameA};
        PhyDiskUsageInfoT phyDiskUsageInfo{tableNameA};
        TsTblOperStatisInfoT tsTblOperStatisInfo{tableNameA};
        TsTblPropsInfoT tsTblPropsInfo{tableNameA};
        ret = CheckViewValue(&diskUsageInfo, &phyDiskUsageInfo, &tsTblOperStatisInfo, &tsTblPropsInfo);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (isCompress == false) {
            break;
        }
        isCompress = false;
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003.写入小批量数据小于30次，alter修改表的disk_limit大小，触发disk_limit（删除该分区数据）主动老化，再次写入相同数据再次到达30次能够触发磁盘整理(压缩模式：no)
TEST_F(DDefragHarden, Timing_081_DDefragHarden_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    uint32_t cmdLen = 0;
    bool isCompress = true;                       // 压缩模式 no,默认
    int32_t batchCount = 15;                      // 每批写入29条数据
    int32_t totalCount = 15;                      // 当前已写入的数据量
    int oldStartTime = GetCurrentTime() - 10800;  // 当前时间减2H已过期数据
    int newStartTime = GetCurrentTime() + 7200;   // 当前时间加2H未过期数据
    int startCount = 0;
    // 建表
    while (true) {
        // 创建需要使用到的表
        int ret = CreateTsTable(stmt, tableNameA, tableNameB, isCompress);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 准备数据
        char **ns = NULL;
        int32_t dataCount = 1000;
        ret = AllocMemForTextData(&ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GenerateRandomString(ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 小批量数据
        batchCount = 15;  // 每批写入29条数据
        totalCount = 15;  // 当前已写入的数据量
        startCount = 0;

        // 小批量写入25批数据
        int startTime = newStartTime;
        for (int32_t i = 0; i < 29; i++) {
            ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            startCount += batchCount;
            totalCount += batchCount;
        }

        // 查cu块统计视图 29(1)
        AW_MACRO_ASSERT_EQ_INT(29, GetCuNumValue(29));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(29 * batchCount, CheckDataCount(stmt, tableNameA));

        // alter 修改disk_limit
        (void)memset(sqlCmd, 0, sizeof(sqlCmd));
        (void)(void)sprintf(sqlCmd, "alter table %s SET (disk_limit = '40 MB');", tableNameA);
        ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 查cu块统计视图 29(1)
        AW_MACRO_ASSERT_EQ_INT(29, GetCuNumValue(29));

        // 老化数据后再次写入30批数据触发磁盘整理
        (void)memset(sqlCmd, 0, sizeof(sqlCmd));
        (void)(void)sprintf(sqlCmd, "SELECT tsdb_aging('%s');", tableNameA);
        ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        // 查cu块统计视图 0
        AW_MACRO_ASSERT_EQ_INT(0, GetCuNumValue(0));
        // 再次写入数据
        batchCount = 15;  // 每批写入29条数据
        totalCount = 15;  // 当前已写入的数据量
        startCount = 0;
        for (int32_t i = 0; i < 30; i++) {
            ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            startCount += batchCount;
            totalCount += batchCount;
        }
        // 查cu块统计视图 1(30)
        AW_MACRO_ASSERT_EQ_INT(1, GetCuNumValue(1));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(30 * batchCount, CheckDataCount(stmt, tableNameA));

        // 删表
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameA, tableNameB));
        FreeMemForTextData(ns, dataCount);  // 清理前序用例申请的内存

        // 查询环境是否清理干净
        // DiskUsageInfoT PhyDiskUsageInfoT TsTblOperStatisInfoT TsTblPropsInfoT
        DiskUsageInfoT diskUsageInfo{tableNameA};
        PhyDiskUsageInfoT phyDiskUsageInfo{tableNameA};
        TsTblOperStatisInfoT tsTblOperStatisInfo{tableNameA};
        TsTblPropsInfoT tsTblPropsInfo{tableNameA};
        ret = CheckViewValue(&diskUsageInfo, &phyDiskUsageInfo, &tsTblOperStatisInfo, &tsTblPropsInfo);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (isCompress == false) {
            break;
        }
        isCompress = false;
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004.写入小批量数据小于30次，alter修改表的ttl，再次写入数据再次到达30次再次触发磁盘整理(压缩模式：no)
TEST_F(DDefragHarden, Timing_081_DDefragHarden_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    uint32_t cmdLen = 0;
    bool isCompress = true;                       // 压缩模式 no,默认
    int32_t batchCount = 15;                      // 每批写入29条数据
    int32_t totalCount = 15;                      // 当前已写入的数据量
    int oldStartTime = GetCurrentTime() - 10800;  // 当前时间减2H已过期数据
    int newStartTime = GetCurrentTime() + 7200;   // 当前时间加2H未过期数据
    int startCount = 0;
    // 建表
    while (true) {
        // 创建需要使用到的表
        int ret = CreateTsTable(stmt, tableNameA, tableNameB, isCompress);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 准备数据
        char **ns = NULL;
        int32_t dataCount = 1000;
        ret = AllocMemForTextData(&ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GenerateRandomString(ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 小批量数据
        batchCount = 15;  // 每批写入29条数据
        totalCount = 15;  // 当前已写入的数据量
        startCount = 0;

        // 小批量写入29批数据
        int startTime = newStartTime;
        for (int32_t i = 0; i < 29; i++) {
            ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            startCount += batchCount;
            totalCount += batchCount;
        }

        // 查cu块统计视图 29(1)
        AW_MACRO_ASSERT_EQ_INT(29, GetCuNumValue(29));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(29 * batchCount, CheckDataCount(stmt, tableNameA));

        // alter 修改ttl
        (void)memset(sqlCmd, 0, sizeof(sqlCmd));
        (void)(void)sprintf(sqlCmd, "alter table %s SET (ttl = '4 hours');", tableNameA);
        ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 查cu块统计视图 29(1)
        AW_MACRO_ASSERT_EQ_INT(29, GetCuNumValue(29));

        // 再次写入数据
        for (int32_t i = 0; i < 1; i++) {
            ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            startCount += batchCount;
            totalCount += batchCount;
        }
        // 查cu块统计视图 1(30)
        AW_MACRO_ASSERT_EQ_INT(1, GetCuNumValue(1));
        // 再次写入数据
        for (int32_t i = 0; i < 30; i++) {
            ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            startCount += batchCount;
            totalCount += batchCount;
        }
        // 查cu块统计视图 1(30)+ 1(30)
        AW_MACRO_ASSERT_EQ_INT(2, GetCuNumValue(2));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT((30 + 30) * batchCount, CheckDataCount(stmt, tableNameA));

        // 删表
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameA, tableNameB));
        FreeMemForTextData(ns, dataCount);  // 清理前序用例申请的内存

        // 查询环境是否清理干净
        // DiskUsageInfoT PhyDiskUsageInfoT TsTblOperStatisInfoT TsTblPropsInfoT
        DiskUsageInfoT diskUsageInfo{tableNameA};
        PhyDiskUsageInfoT phyDiskUsageInfo{tableNameA};
        TsTblOperStatisInfoT tsTblOperStatisInfo{tableNameA};
        TsTblPropsInfoT tsTblPropsInfo{tableNameA};
        ret = CheckViewValue(&diskUsageInfo, &phyDiskUsageInfo, &tsTblOperStatisInfo, &tsTblPropsInfo);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (isCompress == false) {
            break;
        }
        isCompress = false;
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005.a.写29次小批量数据，落盘后重启，再次写入一次小批量数据，查询cu数量(30)，再次写数据直到达到30次查詢cu数量(31);
// b.写29次小批量数据，落盘后重启，再次写入大批量数据，查询cu数量(30)，再次写数据直到达到30次
// c.写29次小批量数据，落盘后重启，再次写含空列数据，查询cu数量，再次写数据直到达到30次，查询cu数量（30）后，再次落盘重启，查询cu数量（30）(压缩模式：no)
TEST_F(DDefragHarden, Timing_081_DDefragHarden_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    uint32_t cmdLen = 0;
    bool isCompress = true;                       // 压缩模式 no,默认
    int32_t batchCount = 15;                      // 每批写入29条数据
    int32_t totalCount = 15;                      // 当前已写入的数据量
    int oldStartTime = GetCurrentTime() - 10800;  // 当前时间减2H已过期数据
    int newStartTime = GetCurrentTime() + 7200;   // 当前时间加2H未过期数据
    int startCount = 0;
    bool isTablePath = true;
    char newDataDir[256] = {0};
    (void)(void)sprintf(newDataDir, "%s/newData/", g_currentDir);
    (void)(void)sprintf(sqlCmd, "rm -rf %s", newDataDir);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, system(sqlCmd));
    // 建表
    while (true) {
        // 创建需要使用到的表
        int ret = CreateTsTable(stmt, tableNameA, tableNameB, isCompress, isTablePath, newDataDir);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 准备数据
        char **ns = NULL;
        int32_t dataCount = 1000;
        ret = AllocMemForTextData(&ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GenerateRandomString(ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 小批量数据
        batchCount = 5;  // 每批写入5条数据
        totalCount = 5;  // 当前已写入的数据量
        startCount = 0;

        // 小批量写入29批数据
        int startTime = newStartTime;
        for (int32_t i = 0; i < 29; i++) {
            ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            startCount += batchCount;
            totalCount += batchCount;
        }

        // 查cu块统计视图 29(1)
        AW_MACRO_ASSERT_EQ_INT(29, GetCuNumValue(29));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(29 * batchCount, CheckDataCount(stmt, tableNameA));

        // 重启服务,重启前数据不会再磁盘整理
        RestartTsdb();

        // 查cu块统计视图 29(1)
        AW_MACRO_ASSERT_EQ_INT(29, GetCuNumValue(29));
        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(29 * batchCount, CheckDataCount(stmt, tableNameA));

        // 再次写入数据
        for (int32_t i = 0; i < 1; i++) {
            ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            startCount += batchCount;
            totalCount += batchCount;
        }
        // 查cu块统计视图 29(1) + 1(1)
        AW_MACRO_ASSERT_EQ_INT(30, GetCuNumValue(30));

        // 再次写入数据
        for (int32_t i = 0; i < 29; i++) {
            ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            startCount += batchCount;
            totalCount += batchCount;
        }
        // 查cu块统计视图 29(1)+ 1(30)
        AW_MACRO_ASSERT_EQ_INT(30, GetCuNumValue(30));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT((29 + 30) * batchCount, CheckDataCount(stmt, tableNameA));

        // b.写29次小批量数据，落盘后重启，再次写入大批量数据，查询cu数量(30)，再次写数据直到达到30次

        // 再次写29次小批量数据后重启
        for (int32_t i = 0; i < 29; i++) {
            ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            startCount += batchCount;
            totalCount += batchCount;
        }

        // 重启服务,重启前数据不会再磁盘整理
        RestartTsdb();

        // 查cu块统计视图 29(1)+ 1(30) + 29(1)
        AW_MACRO_ASSERT_EQ_INT(59, GetCuNumValue(59));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT((29 + 30 + 29) * batchCount, CheckDataCount(stmt, tableNameA));

        // 写入大批量数据
        batchCount = 31;               // 每批写入31条数据
        totalCount += batchCount - 5;  // 下一次写到的数据量

        for (int32_t i = 0; i < 1; i++) {
            ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
        startCount += batchCount;  // 当前已写入的数据量
        batchCount = 5;            // 每批写入5条数据
        totalCount += batchCount;  // 当前已写入的数据量

        // 查cu块统计视图 29(1)+ 1(30) + 29(1) + 1(1)
        AW_MACRO_ASSERT_EQ_INT(60, GetCuNumValue(60));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT((29 + 30 + 29 + 1) * batchCount + 26, CheckDataCount(stmt, tableNameA));

        // 再次写30次小批量数据触发磁盘整理
        for (int32_t i = 0; i < 29; i++) {
            ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            startCount += batchCount;
            totalCount += batchCount;
        }
        // 查cu块统计视图 29(1)+ 1(30) + 29(1) + 1(1) + 29(1)
        AW_MACRO_ASSERT_EQ_INT(89, GetCuNumValue(89));
        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT((29 + 30 + 29 + 1 + 29) * batchCount + 26, CheckDataCount(stmt, tableNameA));

        for (int32_t i = 0; i < 1; i++) {
            ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            startCount += batchCount;
            totalCount += batchCount;
        }

        // 查cu块统计视图 29(1)+ 1(30) + 29(1) + 1(1) + 1(30)
        AW_MACRO_ASSERT_EQ_INT(61, GetCuNumValue(61));
        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT((29 + 30 + 29 + 1 + 30) * batchCount + 26, CheckDataCount(stmt, tableNameA));

        // c.写29次小批量数据，落盘后重启，再次写含空列数据，查询cu数量，再次写数据直到达到30次，查询cu数量（30）后，再次落盘重启，查询cu数量（30）(压缩模式：no)
        //  写29次小批量数据触发磁盘整理
        for (int32_t i = 0; i < 29; i++) {
            ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            startCount += batchCount;
            totalCount += batchCount;
        }

        // 重启服务,重启前数据不会再磁盘整理
        RestartTsdb();

        // 查cu块统计视图 29(1)+ 1(30) + 29(1) + 1(1) + 1(30) + 29(1)
        AW_MACRO_ASSERT_EQ_INT(90, GetCuNumValue(90));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT((29 + 30 + 29 + 1 + 30 + 29) * batchCount + 26, CheckDataCount(stmt, tableNameA));

        // 写入存在空列的数据
        for (int32_t i = 0; i < 1; i++) {
            ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount, 6);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            startCount += batchCount;
            totalCount += batchCount;
        }

        // 查cu块统计视图 29(1)+ 1(30) + 29(1) + 1(1) + 1(30) + 29(1) + 1(1)
        AW_MACRO_ASSERT_EQ_INT(91, GetCuNumValue(91));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT((29 + 30 + 29 + 1 + 30 + 29 + 1) * batchCount + 26, CheckDataCount(stmt, tableNameA));

        // 再次写入30次数据触发磁盘整理
        for (int32_t i = 0; i < 30; i++) {
            ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            startCount += batchCount;
            totalCount += batchCount;
        }

        // 查cu块统计视图 29(1)+ 1(30) + 29(1) + 1(1) + 1(30) + 29(1) + 1(1)+ 1(30)
        AW_MACRO_ASSERT_EQ_INT(92, GetCuNumValue(92));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(
            (29 + 30 + 29 + 1 + 30 + 29 + 1 + 30) * batchCount + 26, CheckDataCount(stmt, tableNameA));
        // 重启服务,重启前数据不会再磁盘整理
        RestartTsdb();

        // 查cu块统计视图 29(1)+ 1(30) + 29(1) + 1(1) + 1(30) + 29(1) + 1(1)+ 1(30)
        AW_MACRO_ASSERT_EQ_INT(92, GetCuNumValue(92));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(
            (29 + 30 + 29 + 1 + 30 + 29 + 1 + 30) * batchCount + 26, CheckDataCount(stmt, tableNameA));

        // 删表
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameA, tableNameB));
        FreeMemForTextData(ns, dataCount);  // 清理前序用例申请的内存

        // 查询环境是否清理干净
        // DiskUsageInfoT PhyDiskUsageInfoT TsTblOperStatisInfoT TsTblPropsInfoT
        DiskUsageInfoT diskUsageInfo{tableNameA};
        PhyDiskUsageInfoT phyDiskUsageInfo{tableNameA};
        TsTblOperStatisInfoT tsTblOperStatisInfo{tableNameA};
        TsTblPropsInfoT tsTblPropsInfo{tableNameA};
        ret = CheckViewValue(&diskUsageInfo, &phyDiskUsageInfo, &tsTblOperStatisInfo, &tsTblPropsInfo);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (isCompress == false) {
            break;
        }
        isCompress = false;
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

int ShutdownByApi(int timeout = 20)
{
    int ret;
    ret = GmcShutdownServer(conn);
    RETURN_IFERR(ret);
    bool procExist = true;
    for (int i = 0; i < timeout; i++) {
        int ret = GtExecSystemCmd("pidof gmserver_ts");
        if (ret != GMERR_OK) {
            procExist = false;
            break;
        }
        sleep(1);
    }
    if (procExist) {
        AW_FUN_Log(LOG_STEP, "The process does not exit after %d seconds.", timeout);
        return FAILED;
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    conn = NULL;
    stmt = NULL;
    testEnvClean();
    system("sh $TEST_HOME/tools/start.sh -ts");
    testEnvInit();
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

int ShutdownByGmadmin(int timeout = 20)
{
    (void)GtExecSystemCmd("gmadmin -shutdown -s %s", g_connServerTsdb);
    for (int i = 0; i < timeout; i++) {
        int ret = GtExecSystemCmd("pidof gmserver_ts");
        if (ret != GMERR_OK) {
            ret = testGmcDisconnect(conn, stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            conn = NULL;
            stmt = NULL;
            testEnvClean();
            system("sh $TEST_HOME/tools/start.sh -ts");
            testEnvInit();
            ret = TestTsGmcConnect(&conn, &stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            return GMERR_OK;
        }
        sleep(1);
    }
    AW_FUN_Log(LOG_STEP, "The process does not exit after %d seconds.", timeout);

    return FAILED;
}

// 006.写入小批量数据直到29次优雅关闭服务再次重启后再写一次小批量数据，查询cu数量，写入小批量数据直到29次优雅关闭服务再次重启后再写一次大批量数据，查询cu数量，写入小批量数据直到29次优雅关闭服务再次重启后再写一次含空列数据，查询cu数量，再次重启查询cu数量
// (压缩模式：no)
TEST_F(DDefragHarden, Timing_081_DDefragHarden_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_CONNECTION_RESET_BY_PEER);
    AddWhiteList(GMERR_NULL_VALUE_NOT_ALLOWED); // 服务停止后断连共享内存失效
    char sqlCmd[256] = {0};
    uint32_t cmdLen = 0;
    bool isCompress = true;                       // 压缩模式 no,默认
    int32_t batchCount = 15;                      // 每批写入29条数据
    int32_t totalCount = 15;                      // 当前已写入的数据量
    int oldStartTime = GetCurrentTime() - 10800;  // 当前时间减2H已过期数据
    int newStartTime = GetCurrentTime() + 7200;   // 当前时间加2H未过期数据
    int startCount = 0;
    bool isTablePath = true;
    char newDataDir[256] = {0};
    (void)(void)sprintf(newDataDir, "%s/newData/", g_currentDir);
    (void)(void)sprintf(sqlCmd, "rm -rf %s", newDataDir);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, system(sqlCmd));
    // 建表
    while (true) {
        // 创建需要使用到的表
        int ret = CreateTsTable(stmt, tableNameA, tableNameB, isCompress, isTablePath, newDataDir);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 准备数据
        char **ns = NULL;
        int32_t dataCount = 1000;
        ret = AllocMemForTextData(&ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GenerateRandomString(ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 小批量数据
        batchCount = 5;  // 每批写入5条数据
        totalCount = 5;  // 当前已写入的数据量
        startCount = 0;

        // 小批量写入29批数据
        int startTime = newStartTime;
        for (int32_t i = 0; i < 29; i++) {
            ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            startCount += batchCount;
            totalCount += batchCount;
        }

        // 查cu块统计视图 29(1)
        AW_MACRO_ASSERT_EQ_INT(29, GetCuNumValue(29));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(29 * batchCount, CheckDataCount(stmt, tableNameA));

        // 重启服务,重启前数据不会再磁盘整理
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ShutdownByApi());

        // 查cu块统计视图 29(1)
        AW_MACRO_ASSERT_EQ_INT(29, GetCuNumValue(29));
        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(29 * batchCount, CheckDataCount(stmt, tableNameA));

        // 再次写入数据
        for (int32_t i = 0; i < 1; i++) {
            ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            startCount += batchCount;
            totalCount += batchCount;
        }
        // 查cu块统计视图 29(1) + 1(1)
        AW_MACRO_ASSERT_EQ_INT(30, GetCuNumValue(30));

        // 再次写入数据
        for (int32_t i = 0; i < 29; i++) {
            ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            startCount += batchCount;
            totalCount += batchCount;
        }
        // 查cu块统计视图 29(1)+ 1(30)
        AW_MACRO_ASSERT_EQ_INT(30, GetCuNumValue(30));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT((29 + 30) * batchCount, CheckDataCount(stmt, tableNameA));

        // b.写29次小批量数据，落盘后重启，再次写入大批量数据，查询cu数量(30)，再次写数据直到达到30次

        // 再次写29次小批量数据后重启
        for (int32_t i = 0; i < 29; i++) {
            ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            startCount += batchCount;
            totalCount += batchCount;
        }

        // 重启服务,重启前数据不会再磁盘整理
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ShutdownByGmadmin());

        // 查cu块统计视图 29(1)+ 1(30) + 29(1)
        AW_MACRO_ASSERT_EQ_INT(59, GetCuNumValue(59));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT((29 + 30 + 29) * batchCount, CheckDataCount(stmt, tableNameA));

        // 写入大批量数据
        batchCount = 31;               // 每批写入31条数据
        totalCount += batchCount - 5;  // 下一次写到的数据量

        for (int32_t i = 0; i < 1; i++) {
            ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
        startCount += batchCount;  // 当前已写入的数据量
        batchCount = 5;            // 每批写入5条数据
        totalCount += batchCount;  // 当前已写入的数据量

        // 查cu块统计视图 29(1)+ 1(30) + 29(1) + 1(1)
        AW_MACRO_ASSERT_EQ_INT(60, GetCuNumValue(60));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT((29 + 30 + 29 + 1) * batchCount + 26, CheckDataCount(stmt, tableNameA));

        // 再次写30次小批量数据触发磁盘整理
        for (int32_t i = 0; i < 29; i++) {
            ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            startCount += batchCount;
            totalCount += batchCount;
        }
        // 查cu块统计视图 29(1)+ 1(30) + 29(1) + 1(1) + 29(1)
        AW_MACRO_ASSERT_EQ_INT(89, GetCuNumValue(89));
        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT((29 + 30 + 29 + 1 + 29) * batchCount + 26, CheckDataCount(stmt, tableNameA));

        for (int32_t i = 0; i < 1; i++) {
            ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            startCount += batchCount;
            totalCount += batchCount;
        }

        // 查cu块统计视图 29(1)+ 1(30) + 29(1) + 1(1) + 1(30)
        AW_MACRO_ASSERT_EQ_INT(61, GetCuNumValue(61));
        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT((29 + 30 + 29 + 1 + 30) * batchCount + 26, CheckDataCount(stmt, tableNameA));

        // c.写29次小批量数据，落盘后重启，再次写含空列数据，查询cu数量，再次写数据直到达到30次，查询cu数量（30）后，再次落盘重启，查询cu数量（30）(压缩模式：no)
        //  写29次小批量数据触发磁盘整理
        for (int32_t i = 0; i < 29; i++) {
            ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            startCount += batchCount;
            totalCount += batchCount;
        }

        // 重启服务,重启前数据不会再磁盘整理
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ShutdownByGmadmin());

        // 查cu块统计视图 29(1)+ 1(30) + 29(1) + 1(1) + 1(30) + 29(1)
        AW_MACRO_ASSERT_EQ_INT(90, GetCuNumValue(90));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT((29 + 30 + 29 + 1 + 30 + 29) * batchCount + 26, CheckDataCount(stmt, tableNameA));

        // 写入存在空列的数据
        for (int32_t i = 0; i < 1; i++) {
            ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount, 6);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            startCount += batchCount;
            totalCount += batchCount;
        }

        // 查cu块统计视图 29(1)+ 1(30) + 29(1) + 1(1) + 1(30) + 29(1) + 1(1)
        AW_MACRO_ASSERT_EQ_INT(91, GetCuNumValue(91));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT((29 + 30 + 29 + 1 + 30 + 29 + 1) * batchCount + 26, CheckDataCount(stmt, tableNameA));

        // 再次写入30次数据触发磁盘整理
        for (int32_t i = 0; i < 30; i++) {
            ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            startCount += batchCount;
            totalCount += batchCount;
        }

        // 查cu块统计视图 29(1)+ 1(30) + 29(1) + 1(1) + 1(30) + 29(1) + 1(1)+ 1(30)
        AW_MACRO_ASSERT_EQ_INT(92, GetCuNumValue(92));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(
            (29 + 30 + 29 + 1 + 30 + 29 + 1 + 30) * batchCount + 26, CheckDataCount(stmt, tableNameA));
        // 重启服务,重启前数据不会再磁盘整理
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ShutdownByApi());

        // 查cu块统计视图 29(1)+ 1(30) + 29(1) + 1(1) + 1(30) + 29(1) + 1(1)+ 1(30)
        AW_MACRO_ASSERT_EQ_INT(92, GetCuNumValue(92));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(
            (29 + 30 + 29 + 1 + 30 + 29 + 1 + 30) * batchCount + 26, CheckDataCount(stmt, tableNameA));

        // 删表
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameA, tableNameB));
        FreeMemForTextData(ns, dataCount);  // 清理前序用例申请的内存

        // 查询环境是否清理干净
        // DiskUsageInfoT PhyDiskUsageInfoT TsTblOperStatisInfoT TsTblPropsInfoT
        DiskUsageInfoT diskUsageInfo{tableNameA};
        PhyDiskUsageInfoT phyDiskUsageInfo{tableNameA};
        TsTblOperStatisInfoT tsTblOperStatisInfo{tableNameA};
        TsTblPropsInfoT tsTblPropsInfo{tableNameA};
        ret = CheckViewValue(&diskUsageInfo, &phyDiskUsageInfo, &tsTblOperStatisInfo, &tsTblPropsInfo);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (isCompress == false) {
            break;
        }
        isCompress = false;
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007.写入多次小批量数据还未到30次时已达磁盘上限，到达30次时查询cu数量不会触发磁盘整理(压缩模式：no)
TEST_F(DDefragHarden, Timing_081_DDefragHarden_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    uint32_t cmdLen = 0;
    bool isCompress = true;                       // 压缩模式 no,默认
    int32_t batchCount = 15;                      // 每批写入29条数据
    int32_t totalCount = 15;                      // 当前已写入的数据量
    int oldStartTime = GetCurrentTime() - 10800;  // 当前时间减2H已过期数据
    int newStartTime = GetCurrentTime() + 7200;   // 当前时间加2H未过期数据
    int startCount = 0;
    // 建表
    while (true) {
        // 创建需要使用到的表
        int ret = CreateTsTable(stmt, tableNameA, tableNameB, isCompress);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 准备数据
        char **ns = NULL;
        int32_t dataCount = 1000;
        ret = AllocMemForTextData(&ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GenerateRandomString(ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 小批量数据
        batchCount = 29;  // 每批写入29条数据
        totalCount = 29;  // 当前已写入的数据量
        startCount = 0;

        // alter 修改disk_limit
        (void)memset(sqlCmd, 0, sizeof(sqlCmd));
        (void)(void)sprintf(sqlCmd, "alter table %s SET (disk_limit = '20 MB');", tableNameA);
        ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 小批量写入30批数据
        int startTime = newStartTime;
        for (int32_t i = 0; i < 30; i++) {
            ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            startCount += batchCount;
            totalCount += batchCount;
        }

        // 查cu块统计视图 29(1)
        AW_MACRO_ASSERT_NE_INT(1, GetCuNumValue(1));

        // 校验数据条数
        AW_MACRO_ASSERT_NE_INT(0, CheckDataCount(stmt, tableNameA));

        // 删表
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameA, tableNameB));
        FreeMemForTextData(ns, dataCount);  // 清理前序用例申请的内存

        // 查询环境是否清理干净
        // DiskUsageInfoT PhyDiskUsageInfoT TsTblOperStatisInfoT TsTblPropsInfoT
        DiskUsageInfoT diskUsageInfo{tableNameA};
        PhyDiskUsageInfoT phyDiskUsageInfo{tableNameA};
        TsTblOperStatisInfoT tsTblOperStatisInfo{tableNameA};
        TsTblPropsInfoT tsTblPropsInfo{tableNameA};
        ret = CheckViewValue(&diskUsageInfo, &phyDiskUsageInfo, &tsTblOperStatisInfo, &tsTblPropsInfo);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (isCompress == false) {
            break;
        }
        isCompress = false;
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008.时序不支持显示事务
TEST_F(DDefragHarden, Timing_081_DDefragHarden_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    uint32_t cmdLen = 0;
    bool isCompress = true;                       // 压缩模式 no,默认
    int32_t batchCount = 15;                      // 每批写入29条数据
    int32_t totalCount = 15;                      // 当前已写入的数据量
    int oldStartTime = GetCurrentTime() - 10800;  // 当前时间减2H已过期数据
    int newStartTime = GetCurrentTime() + 7200;   // 当前时间加2H未过期数据
    int startCount = 0;
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    GmcTxConfigT msTrxCfgCommitted;  // 悲观+读已提交
    msTrxCfgCommitted.transMode = GMC_TRANS_USED_IN_CS;
    msTrxCfgCommitted.type = GMC_TX_ISOLATION_COMMITTED;
    msTrxCfgCommitted.readOnly = false;
    msTrxCfgCommitted.trxType = GMC_PESSIMISITIC_TRX;

    // 建表
    while (true) {
        // 创建需要使用到的表
        int ret = CreateTsTable(stmt, tableNameA, tableNameB, isCompress);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 准备数据
        char **ns = NULL;
        int32_t dataCount = 1000;
        ret = AllocMemForTextData(&ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GenerateRandomString(ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 小批量数据
        batchCount = 29;  // 每批写入29条数据
        totalCount = 29;  // 当前已写入的数据量
        startCount = 0;

        // 写一批29条数据
        int startTime = newStartTime;
        ret = GmcTransStart(conn, &msTrxCfgCommitted);
        AW_MACRO_ASSERT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
        ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcTransRollBack(conn);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 查cu块统计视图 1(29)
        AW_MACRO_ASSERT_EQ_INT(1, GetCuNumValue(1));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(29, CheckDataCount(stmt, tableNameA));

        // 删表
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameA, tableNameB));
        FreeMemForTextData(ns, dataCount);  // 清理前序用例申请的内存

        // 查询环境是否清理干净
        // DiskUsageInfoT PhyDiskUsageInfoT TsTblOperStatisInfoT TsTblPropsInfoT
        DiskUsageInfoT diskUsageInfo{tableNameA};
        PhyDiskUsageInfoT phyDiskUsageInfo{tableNameA};
        TsTblOperStatisInfoT tsTblOperStatisInfo{tableNameA};
        TsTblPropsInfoT tsTblPropsInfo{tableNameA};
        ret = CheckViewValue(&diskUsageInfo, &phyDiskUsageInfo, &tsTblOperStatisInfo, &tsTblPropsInfo);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (isCompress == false) {
            break;
        }
        isCompress = false;
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 009.100分区单批次写入多分区数据，单分区少于30个时，不触发磁盘整理，(压缩模式：no)
// 写数据到内存到内存满读数据失败
TEST_F(DDefragHarden, Timing_081_DDefragHarden_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    uint32_t cmdLen = 0;
    bool isCompress = true;  // 压缩模式 no,默认
    AddWhiteList(GMERR_DISK_NO_SPACE_ERROR);
    AddWhiteList(GMERR_MEMORY_OPERATE_FAILED);
    AddWhiteList(GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD);
    AddWhiteList(GMERR_OUT_OF_MEMORY);
    // 构造多时区数据
    int32_t nowTimeValue = GetCurrentTime();
    const int32_t interval = 100;  // 时间分区数量
    int newStartTime[interval] = {0};
    int32_t tableNum = 100;
    // 数组长度和大小不要计算错误
    for (int32_t i = 0; i < sizeof(newStartTime) / sizeof(newStartTime[0]); i++) {
        newStartTime[i] = nowTimeValue + 3600 * (1 + i);
    }
    // 建表
    while (true) {
        // 创建需要使用到的表
        int ret = CreateTsTable(stmt, tableNameA, tableNameB, isCompress);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        for (int32_t k = 0; k < tableNum; k++) {
            char tableName1[48] = "tableName1";
            (void)(void)sprintf(tableName1, "%s_%d", tableNameA, k);
            char tableName2[48] = "tableName2";
            (void)(void)sprintf(tableName2, "%s_%d", tableNameB, k);
            ret = CreateTsTable(stmt, tableName1, tableName2, isCompress);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
        // 准备数据
        char **ns = NULL;
        int32_t dataCount = 1000;
        ret = AllocMemForTextData(&ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GenerateRandomString(ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 小批量数据
        int32_t batchCount = 2;  // 每批写入29条数据
        int32_t totalCount = 1;  // 当前已写入的数据量
        int startCount = 0;

        // 小批量写入29批数据
        int startTime = 0;
        for (int32_t k = 0; k < tableNum; k++) {
            char tableName1[48] = "tableName1";
            (void)(void)sprintf(tableName1, "%s_%d", tableNameA, k);
            char tableName2[48] = "tableName2";
            (void)(void)sprintf(tableName2, "%s_%d", tableNameB, k);
            for (int32_t i = 0; i < interval; i++) {
                batchCount = 29;  // 每批写入29条数据
                totalCount = 29;  // 当前已写入的数据量
                startCount = 0;
                startTime = newStartTime[i];
                for (int32_t i = 0; i < 29; i++) {
                    ret = insertDataToTableA(stmt, tableName1, totalCount, startTime, startCount, ns + startCount);
                    if (ret) {
                        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                        AW_FUN_Log(LOG_STEP, "write table %s no ok, k is %d, i is %d", tableName1, k, i);
                        break;
                    }
                    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                    ret = insertDataToTableB(stmt, tableName2, totalCount, startTime, startCount, ns + startCount);
                    if (ret) {
                        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                        AW_FUN_Log(LOG_STEP, "write table %s no ok, k is %d, i is %d", tableName2, k, i);
                        break;
                    }
                    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                    startCount += batchCount;
                    totalCount += batchCount;
                }
                if (ret) {
                    break;
                }
            }
            if (ret) {
                break;
            }
        }

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(29, GetCuNumValue(29, "testdba_0"));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 29", "testdba_0"));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(2900 * batchCount, CheckDataCount(stmt, tableNameA));
        // 每个分区写入一条数据触发磁盘整理
        for (int32_t i = 0; i < interval; i++) {
            batchCount = 1;   // 每批写入1条数据
            totalCount = 30;  // 当前已写入的数据量
            startCount = 29;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 1; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                ret = insertDataToTableB(stmt, tableNameB, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                startCount += batchCount;
                totalCount += batchCount;
            }
        }

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(1, GetCuNumValue(1));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 1", "testdba_0"));
        // 每个分区写入30批数据触发磁盘整理
        for (int32_t i = 0; i < 100; i++) {
            batchCount = 29;  // 每批写入1条数据
            totalCount = 59;  // 当前已写入的数据量
            startCount = 30;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 30; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                ret = insertDataToTableB(stmt, tableNameB, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                startCount += batchCount;
                totalCount += batchCount;
            }
        }

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(2, GetCuNumValue(2, "testdba_0"));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 2", "testdba_0"));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(6000 * batchCount, CheckDataCount(stmt, "testdba_0"));

        // 删表
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameA, tableNameB));
        for (int32_t k = 0; k < tableNum; k++) {
            char tableName1[48] = "tableName1";
            (void)(void)sprintf(tableName1, "%s_%d", tableNameA, k);
            char tableName2[48] = "tableName2";
            (void)(void)sprintf(tableName2, "%s_%d", tableNameB, k);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameA, tableNameB));
        }
        FreeMemForTextData(ns, dataCount);  // 清理前序用例申请的内存

        // 查询环境是否清理干净
        // DiskUsageInfoT PhyDiskUsageInfoT TsTblOperStatisInfoT TsTblPropsInfoT
        DiskUsageInfoT diskUsageInfo{tableNameA};
        PhyDiskUsageInfoT phyDiskUsageInfo{tableNameA};
        TsTblOperStatisInfoT tsTblOperStatisInfo{tableNameA};
        TsTblPropsInfoT tsTblPropsInfo{tableNameA};
        ret = CheckViewValue(&diskUsageInfo, &phyDiskUsageInfo, &tsTblOperStatisInfo, &tsTblPropsInfo);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (isCompress == false) {
            break;
        }
        isCompress = false;
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010.100分区单批次写入多分区数据，单分区少于30个时，不触发磁盘整理，(压缩模式：no，默认)并发DML
TEST_F(DDefragHarden, Timing_081_DDefragHarden_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    uint32_t cmdLen = 0;
    bool isCompress = true;  // 压缩模式 no,默认
    // 构造多时区数据
    int32_t nowTimeValue = GetCurrentTime();
    const int32_t interval = 100;  // 时间分区数量
    int newStartTime[interval] = {0};
    // 数组长度和大小不要计算错误
    for (int32_t i = 0; i < sizeof(newStartTime) / sizeof(newStartTime[0]); i++) {
        newStartTime[i] = nowTimeValue + 3600 * (1 + i);
    }
    // 建表
    while (true) {
        // 创建需要使用到的表
        int ret = CreateTsTable(stmt, tableNameA, tableNameB, isCompress);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 准备数据
        char **ns = NULL;
        int32_t dataCount = 1000;
        ret = AllocMemForTextData(&ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GenerateRandomString(ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 小批量数据
        int32_t batchCount = 2;  // 每批写入29条数据
        int32_t totalCount = 1;  // 当前已写入的数据量
        int startCount = 0;

        // 小批量写入29批数据
        int startTime = 0;
        for (int32_t i = 0; i < interval; i++) {
            batchCount = 20;  // 每批写入20条数据
            totalCount = 20;  // 当前已写入的数据量
            startCount = 0;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 29; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                startCount += batchCount;
                totalCount += batchCount;
            }
        }

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(29, GetCuNumValue(29));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 29"));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(29 * interval * batchCount, CheckDataCount(stmt, tableNameA));

        // 并发写B表
        int threadCount = 1;
        pthread_t tid[threadCount];
        pthread_create(&tid[0], NULL, InsertTableB, NULL);

        // 每个分区写入一批数据触发磁盘整理
        for (int32_t i = 0; i < interval; i++) {
            batchCount = 20;       // 写入1批数据
            totalCount = 20 * 30;  // 当前已写入的数据量
            startCount = 20 * 29;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 1; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                if (ret) {
                    break;
                }
                startCount += batchCount;
                totalCount += batchCount;
            }
        }

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(1, GetCuNumValue(1));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 1"));
        // 每个分区写入30批数据触发磁盘整理
        for (int32_t i = 0; i < 100; i++) {
            batchCount = 1;            // 每批写入1条数据
            totalCount = 29 * 30 + 1;  // 当前已写入的数据量
            startCount = 29 * 30;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 30; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                if (ret) {
                    break;
                }
                startCount += batchCount;
                totalCount += batchCount;
            }
        }
        pthread_join(tid[0], NULL);

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(2, GetCuNumValue(2));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 2"));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(30 * interval * batchCount + 30, CheckDataCount(stmt, tableNameA));

        // 删表
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameA, tableNameB));
        FreeMemForTextData(ns, dataCount);  // 清理前序用例申请的内存

        // 查询环境是否清理干净
        // DiskUsageInfoT PhyDiskUsageInfoT TsTblOperStatisInfoT TsTblPropsInfoT
        DiskUsageInfoT diskUsageInfo{tableNameA};
        PhyDiskUsageInfoT phyDiskUsageInfo{tableNameA};
        TsTblOperStatisInfoT tsTblOperStatisInfo{tableNameA};
        TsTblPropsInfoT tsTblPropsInfo{tableNameA};
        ret = CheckViewValue(&diskUsageInfo, &phyDiskUsageInfo, &tsTblOperStatisInfo, &tsTblPropsInfo);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (isCompress == false) {
            break;
        }
        isCompress = false;
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 011.100分区单批次写入多分区数据，单分区少于30个时，不触发磁盘整理，(压缩模式：no，默认)并发DDL
// 第二次循环时查数据报错1010001
TEST_F(DDefragHarden, Timing_081_DDefragHarden_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    uint32_t cmdLen = 0;
    bool isCompress = true;  // 压缩模式 no,默认
    // 构造多时区数据
    int32_t nowTimeValue = GetCurrentTime();
    const int32_t interval = 100;  // 时间分区数量
    int newStartTime[interval] = {0};
    // 数组长度和大小不要计算错误
    for (int32_t i = 0; i < sizeof(newStartTime) / sizeof(newStartTime[0]); i++) {
        newStartTime[i] = nowTimeValue + 3600 * (1 + i);
    }
    // 建表
    while (true) {
        // 创建需要使用到的表
        int ret = CreateTsTable(stmt, tableNameA, tableNameB, isCompress);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 准备数据
        char **ns = NULL;
        int32_t dataCount = 1000;
        ret = AllocMemForTextData(&ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GenerateRandomString(ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 小批量数据
        int32_t batchCount = 2;  // 每批写入29条数据
        int32_t totalCount = 1;  // 当前已写入的数据量
        int startCount = 0;

        // 小批量写入29批数据
        int startTime = 0;
        for (int32_t i = 0; i < interval; i++) {
            batchCount = 5;  // 每批写入20条数据
            totalCount = 5;  // 当前已写入的数据量
            startCount = 0;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 29; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                startCount += batchCount;
                totalCount += batchCount;
            }
        }

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(29, GetCuNumValue(29));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 29"));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(29 * interval * batchCount, CheckDataCount(stmt, tableNameA));

        // 并发DDL
        int threadCount = 1;
        pthread_t tid[threadCount];
        pthread_create(&tid[0], NULL, CreateDropTables, NULL);

        // 每个分区写入一批数据触发磁盘整理
        for (int32_t i = 0; i < interval; i++) {
            batchCount = 5;       // 写入1批数据
            totalCount = 5 * 30;  // 当前已写入的数据量
            startCount = 5 * 29;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 1; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                if (ret) {
                    break;
                }
                startCount += batchCount;
                totalCount += batchCount;
            }
        }

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(1, GetCuNumValue(1));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 1"));
        // 每个分区写入30批数据触发磁盘整理
        for (int32_t i = 0; i < 100; i++) {
            batchCount = 1;           // 每批写入1条数据
            totalCount = 5 * 30 + 1;  // 当前已写入的数据量
            startCount = 5 * 30;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 30; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                if (ret) {
                    break;
                }
                startCount += batchCount;
                totalCount += batchCount;
            }
        }
        pthread_join(tid[0], NULL);

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(2, GetCuNumValue(2));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 2"));

        // 校验数据条数
        batchCount = 5;
        AW_MACRO_ASSERT_EQ_INT(30 * interval * batchCount + 30 * interval, CheckDataCount(stmt, tableNameA));

        // 删表
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameA, tableNameB));
        FreeMemForTextData(ns, dataCount);  // 清理前序用例申请的内存

        // 查询环境是否清理干净
        // DiskUsageInfoT PhyDiskUsageInfoT TsTblOperStatisInfoT TsTblPropsInfoT
        DiskUsageInfoT diskUsageInfo{tableNameA};
        PhyDiskUsageInfoT phyDiskUsageInfo{tableNameA};
        TsTblOperStatisInfoT tsTblOperStatisInfo{tableNameA};
        TsTblPropsInfoT tsTblPropsInfo{tableNameA};
        ret = CheckViewValue(&diskUsageInfo, &phyDiskUsageInfo, &tsTblOperStatisInfo, &tsTblPropsInfo);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (isCompress == false) {
            break;
        }
        isCompress = false;
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 012.100分区单批次写入多分区数据，单分区少于30个时，不触发磁盘整理，(压缩模式：no，默认)并发DQL
// 第二次循环时查数据报错1010001
TEST_F(DDefragHarden, Timing_081_DDefragHarden_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    uint32_t cmdLen = 0;
    bool isCompress = true;  // 压缩模式 no,默认
    // 构造多时区数据
    int32_t nowTimeValue = GetCurrentTime();
    const int32_t interval = 100;  // 时间分区数量
    int newStartTime[interval] = {0};
    // 数组长度和大小不要计算错误
    for (int32_t i = 0; i < sizeof(newStartTime) / sizeof(newStartTime[0]); i++) {
        newStartTime[i] = nowTimeValue + 3600 * (1 + i);
    }
    // 建表
    while (true) {
        // 创建需要使用到的表
        int ret = CreateTsTable(stmt, tableNameA, tableNameB, isCompress);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = PrepareEnvTableAndData(stmt, 10);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 准备数据
        char **ns = NULL;
        int32_t dataCount = 1000;
        ret = AllocMemForTextData(&ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GenerateRandomString(ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 小批量数据
        int32_t batchCount = 2;  // 每批写入29条数据
        int32_t totalCount = 1;  // 当前已写入的数据量
        int startCount = 0;

        // 小批量写入29批数据
        int startTime = 0;
        for (int32_t i = 0; i < interval; i++) {
            batchCount = 5;  // 每批写入20条数据
            totalCount = 5;  // 当前已写入的数据量
            startCount = 0;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 29; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                startCount += batchCount;
                totalCount += batchCount;
            }
        }

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(29, GetCuNumValue(29));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 29"));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(29 * interval * batchCount, CheckDataCount(stmt, tableNameA));

        // 并发DDL
        int threadCount = 1;
        pthread_t tid[threadCount];
        pthread_create(&tid[0], NULL, QueryTable, NULL);

        // 每个分区写入一批数据触发磁盘整理
        for (int32_t i = 0; i < interval; i++) {
            batchCount = 5;       // 写入1批数据
            totalCount = 5 * 30;  // 当前已写入的数据量
            startCount = 5 * 29;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 1; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                if (ret) {
                    break;
                }
                startCount += batchCount;
                totalCount += batchCount;
            }
        }

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(1, GetCuNumValue(1));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 1"));
        // 每个分区写入30批数据触发磁盘整理
        for (int32_t i = 0; i < 100; i++) {
            batchCount = 1;           // 每批写入1条数据
            totalCount = 5 * 30 + 1;  // 当前已写入的数据量
            startCount = 5 * 30;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 30; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                if (ret) {
                    break;
                }
                startCount += batchCount;
                totalCount += batchCount;
            }
        }
        pthread_join(tid[0], NULL);

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(2, GetCuNumValue(2));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 2"));

        // 校验数据条数
        batchCount = 5;
        AW_MACRO_ASSERT_EQ_INT(30 * interval * batchCount + 30 * interval, CheckDataCount(stmt, tableNameA));

        // 删表
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameA, tableNameB));
        FreeMemForTextData(ns, dataCount);  // 清理前序用例申请的内存
        ret = CleanPrepareEnvTable(stmt, 10);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 查询环境是否清理干净
        // DiskUsageInfoT PhyDiskUsageInfoT TsTblOperStatisInfoT TsTblPropsInfoT
        DiskUsageInfoT diskUsageInfo{tableNameA};
        PhyDiskUsageInfoT phyDiskUsageInfo{tableNameA};
        TsTblOperStatisInfoT tsTblOperStatisInfo{tableNameA};
        TsTblPropsInfoT tsTblPropsInfo{tableNameA};
        ret = CheckViewValue(&diskUsageInfo, &phyDiskUsageInfo, &tsTblOperStatisInfo, &tsTblPropsInfo);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (isCompress == false) {
            break;
        }
        isCompress = false;
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 013.100分区单批次写入多分区数据，单分区少于30个时，不触发磁盘整理，(压缩模式：no，默认)并发ttl
TEST_F(DDefragHarden, Timing_081_DDefragHarden_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    uint32_t cmdLen = 0;
    bool isCompress = true;  // 压缩模式 no,默认
    // 构造多时区数据
    int32_t nowTimeValue = GetCurrentTime();
    const int32_t interval = 100;  // 时间分区数量
    int newStartTime[interval] = {0};
    // 数组长度和大小不要计算错误
    for (int32_t i = 0; i < sizeof(newStartTime) / sizeof(newStartTime[0]); i++) {
        newStartTime[i] = nowTimeValue + 3600 * (1 + i);
    }
    // 建表
    while (true) {
        // 创建需要使用到的表
        int ret = CreateTsTable(stmt, tableNameA, tableNameB, isCompress);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = PrepareEnvTableAndData(stmt, 10);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 准备数据
        char **ns = NULL;
        int32_t dataCount = 1000;
        ret = AllocMemForTextData(&ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GenerateRandomString(ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 小批量数据
        int32_t batchCount = 2;  // 每批写入29条数据
        int32_t totalCount = 1;  // 当前已写入的数据量
        int startCount = 0;

        // 小批量写入29批数据
        int startTime = 0;
        for (int32_t i = 0; i < interval; i++) {
            batchCount = 5;  // 每批写入20条数据
            totalCount = 5;  // 当前已写入的数据量
            startCount = 0;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 29; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                startCount += batchCount;
                totalCount += batchCount;
            }
        }

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(29, GetCuNumValue(29));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 29"));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(29 * interval * batchCount, CheckDataCount(stmt, tableNameA));

        // 并发ttl
        sleep(2);

        // 每个分区写入一批数据触发磁盘整理
        for (int32_t i = 0; i < interval; i++) {
            batchCount = 5;       // 写入1批数据
            totalCount = 5 * 30;  // 当前已写入的数据量
            startCount = 5 * 29;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 1; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                if (ret) {
                    break;
                }
                startCount += batchCount;
                totalCount += batchCount;
            }
        }

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(1, GetCuNumValue(1));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 1"));
        // 每个分区写入30批数据触发磁盘整理
        for (int32_t i = 0; i < 100; i++) {
            batchCount = 1;           // 每批写入1条数据
            totalCount = 5 * 30 + 1;  // 当前已写入的数据量
            startCount = 5 * 30;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 30; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                if (ret) {
                    break;
                }
                startCount += batchCount;
                totalCount += batchCount;
            }
        }

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(2, GetCuNumValue(2));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 2"));

        // 校验数据条数
        batchCount = 5;
        AW_MACRO_ASSERT_EQ_INT(30 * interval * batchCount + 30 * interval, CheckDataCount(stmt, tableNameA));

        // 删表
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameA, tableNameB));
        FreeMemForTextData(ns, dataCount);  // 清理前序用例申请的内存
        ret = CleanPrepareEnvTable(stmt, 10);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 查询环境是否清理干净
        // DiskUsageInfoT PhyDiskUsageInfoT TsTblOperStatisInfoT TsTblPropsInfoT
        DiskUsageInfoT diskUsageInfo{tableNameA};
        PhyDiskUsageInfoT phyDiskUsageInfo{tableNameA};
        TsTblOperStatisInfoT tsTblOperStatisInfo{tableNameA};
        TsTblPropsInfoT tsTblPropsInfo{tableNameA};
        ret = CheckViewValue(&diskUsageInfo, &phyDiskUsageInfo, &tsTblOperStatisInfo, &tsTblPropsInfo);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (isCompress == false) {
            break;
        }
        isCompress = false;
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 014.100分区单批次写入多分区数据，单分区少于30个时，不触发磁盘整理，(压缩模式：no，默认)并发Truncate
TEST_F(DDefragHarden, Timing_081_DDefragHarden_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    uint32_t cmdLen = 0;
    bool isCompress = true;  // 压缩模式 no,默认
    // 构造多时区数据
    int32_t nowTimeValue = GetCurrentTime();
    const int32_t interval = 100;  // 时间分区数量
    int newStartTime[interval] = {0};
    // 数组长度和大小不要计算错误
    for (int32_t i = 0; i < sizeof(newStartTime) / sizeof(newStartTime[0]); i++) {
        newStartTime[i] = nowTimeValue + 3600 * (1 + i);
    }
    // 建表
    while (true) {
        // 创建需要使用到的表
        int ret = CreateTsTable(stmt, tableNameA, tableNameB, isCompress);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = PrepareEnvTableAndData(stmt, 10);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 准备数据
        char **ns = NULL;
        int32_t dataCount = 1000;
        ret = AllocMemForTextData(&ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GenerateRandomString(ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 小批量数据
        int32_t batchCount = 2;  // 每批写入29条数据
        int32_t totalCount = 1;  // 当前已写入的数据量
        int startCount = 0;

        // 小批量写入29批数据
        int startTime = 0;
        for (int32_t i = 0; i < interval; i++) {
            batchCount = 5;  // 每批写入20条数据
            totalCount = 5;  // 当前已写入的数据量
            startCount = 0;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 29; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                startCount += batchCount;
                totalCount += batchCount;
            }
        }

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(29, GetCuNumValue(29));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 29"));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(29 * interval * batchCount, CheckDataCount(stmt, tableNameA));

        // 并发Truncate
        int threadCount = 1;
        pthread_t tid[threadCount];
        pthread_create(&tid[0], NULL, TruncateTable, NULL);

        // 每个分区写入一批数据触发磁盘整理
        for (int32_t i = 0; i < interval; i++) {
            batchCount = 5;       // 写入1批数据
            totalCount = 5 * 30;  // 当前已写入的数据量
            startCount = 5 * 29;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 1; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                if (ret) {
                    break;
                }
                startCount += batchCount;
                totalCount += batchCount;
            }
        }
        pthread_join(tid[0], NULL);
        // 查cu块统计视图 总写入次数/30

        if (GetCuNumValue(1) != 1) {
            AW_MACRO_ASSERT_EQ_INT(0, GetCuNumValue(1));
            AW_MACRO_EXPECT_NE_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 1"));
        } else {
            AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 1"));
        }

        // 每个分区写入30批数据触发磁盘整理
        for (int32_t i = 0; i < 100; i++) {
            batchCount = 1;           // 每批写入1条数据
            totalCount = 5 * 30 + 1;  // 当前已写入的数据量
            startCount = 5 * 30;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 30; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                if (ret) {
                    break;
                }
                startCount += batchCount;
                totalCount += batchCount;
            }
        }

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(2, GetCuNumValue(2));
        if (GetCuNumValue(2) != 2) {
            AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 1"));
            // 校验数据条数
            batchCount = 5;
            AW_MACRO_ASSERT_EQ_INT(30 * interval, CheckDataCount(stmt, tableNameA));
        } else {
            AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 2"));
            // 校验数据条数
            batchCount = 5;
            AW_MACRO_ASSERT_EQ_INT(35 * interval, CheckDataCount(stmt, tableNameA));
        }

        // 删表
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameA, tableNameB));
        FreeMemForTextData(ns, dataCount);  // 清理前序用例申请的内存
        ret = CleanPrepareEnvTable(stmt, 10);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 查询环境是否清理干净
        // DiskUsageInfoT PhyDiskUsageInfoT TsTblOperStatisInfoT TsTblPropsInfoT
        DiskUsageInfoT diskUsageInfo{tableNameA};
        PhyDiskUsageInfoT phyDiskUsageInfo{tableNameA};
        TsTblOperStatisInfoT tsTblOperStatisInfo{tableNameA};
        TsTblPropsInfoT tsTblPropsInfo{tableNameA};
        ret = CheckViewValue(&diskUsageInfo, &phyDiskUsageInfo, &tsTblOperStatisInfo, &tsTblPropsInfo);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (isCompress == false) {
            break;
        }
        isCompress = false;
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 015.100分区单批次写入多分区数据，单分区少于30个时，不触发磁盘整理，(压缩模式：no，默认)并发Alter ttl
TEST_F(DDefragHarden, Timing_081_DDefragHarden_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    uint32_t cmdLen = 0;
    bool isCompress = true;  // 压缩模式 no,默认
    // 构造多时区数据
    int32_t nowTimeValue = GetCurrentTime();
    const int32_t interval = 100;  // 时间分区数量
    int newStartTime[interval] = {0};
    // 数组长度和大小不要计算错误
    for (int32_t i = 0; i < sizeof(newStartTime) / sizeof(newStartTime[0]); i++) {
        newStartTime[i] = nowTimeValue + 3600 * (1 + i);
    }
    // 建表
    while (true) {
        // 创建需要使用到的表
        int ret = CreateTsTable(stmt, tableNameA, tableNameB, isCompress);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 准备数据
        char **ns = NULL;
        int32_t dataCount = 1000;
        ret = AllocMemForTextData(&ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GenerateRandomString(ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 小批量数据
        int32_t batchCount = 2;  // 每批写入29条数据
        int32_t totalCount = 1;  // 当前已写入的数据量
        int startCount = 0;

        // 小批量写入29批数据
        int startTime = 0;
        for (int32_t i = 0; i < interval; i++) {
            batchCount = 10;  // 每批写入20条数据
            totalCount = 10;  // 当前已写入的数据量
            startCount = 0;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 29; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                startCount += batchCount;
                totalCount += batchCount;
            }
        }

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(29, GetCuNumValue(29));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 29"));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(29 * interval * batchCount, CheckDataCount(stmt, tableNameA));

        // 并发写B表
        int threadCount = 1;
        pthread_t tid[threadCount];
        pthread_create(&tid[0], NULL, AlterTtlTable, NULL);

        // 每个分区写入一批数据触发磁盘整理
        for (int32_t i = 0; i < interval; i++) {
            batchCount = 10;       // 写入1批数据
            totalCount = 10 * 30;  // 当前已写入的数据量
            startCount = 10 * 29;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 1; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                if (ret) {
                    break;
                }
                startCount += batchCount;
                totalCount += batchCount;
            }
        }
        pthread_join(tid[0], NULL);
        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(1, GetCuNumValue(1));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 1"));
        // 每个分区写入30批数据触发磁盘整理
        for (int32_t i = 0; i < interval; i++) {
            batchCount = 1;            // 每批写入1条数据
            totalCount = 29 * 30 + 1;  // 当前已写入的数据量
            startCount = 29 * 30;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 30; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                if (ret) {
                    break;
                }
                startCount += batchCount;
                totalCount += batchCount;
            }
        }

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(2, GetCuNumValue(2));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 2"));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(30 * interval * 10 + 30 * interval, CheckDataCount(stmt, tableNameA));

        // 删表
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameA, tableNameB));
        FreeMemForTextData(ns, dataCount);  // 清理前序用例申请的内存

        // 查询环境是否清理干净
        // DiskUsageInfoT PhyDiskUsageInfoT TsTblOperStatisInfoT TsTblPropsInfoT
        DiskUsageInfoT diskUsageInfo{tableNameA};
        PhyDiskUsageInfoT phyDiskUsageInfo{tableNameA};
        TsTblOperStatisInfoT tsTblOperStatisInfo{tableNameA};
        TsTblPropsInfoT tsTblPropsInfo{tableNameA};
        ret = CheckViewValue(&diskUsageInfo, &phyDiskUsageInfo, &tsTblOperStatisInfo, &tsTblPropsInfo);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (isCompress == false) {
            break;
        }
        isCompress = false;
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 016.100分区单批次写入多分区数据，单分区少于30个时，不触发磁盘整理，(压缩模式：no，默认)并发alter disklimit
TEST_F(DDefragHarden, Timing_081_DDefragHarden_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    uint32_t cmdLen = 0;
    bool isCompress = true;  // 压缩模式 no,默认
    // 构造多时区数据
    int32_t nowTimeValue = GetCurrentTime();
    const int32_t interval = 100;  // 时间分区数量
    int newStartTime[interval] = {0};
    // 数组长度和大小不要计算错误
    for (int32_t i = 0; i < sizeof(newStartTime) / sizeof(newStartTime[0]); i++) {
        newStartTime[i] = nowTimeValue + 3600 * (1 + i);
    }
    // 建表
    while (true) {
        // 创建需要使用到的表
        int ret = CreateTsTable(stmt, tableNameA, tableNameB, isCompress);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 准备数据
        char **ns = NULL;
        int32_t dataCount = 1000;
        ret = AllocMemForTextData(&ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GenerateRandomString(ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 小批量数据
        int32_t batchCount = 2;  // 每批写入29条数据
        int32_t totalCount = 1;  // 当前已写入的数据量
        int startCount = 0;

        // 小批量写入29批数据
        int startTime = 0;
        for (int32_t i = 0; i < interval; i++) {
            batchCount = 10;  // 每批写入20条数据
            totalCount = 10;  // 当前已写入的数据量
            startCount = 0;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 29; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                startCount += batchCount;
                totalCount += batchCount;
            }
        }

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(29, GetCuNumValue(29));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 29"));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(29 * interval * batchCount, CheckDataCount(stmt, tableNameA));

        // 并发写B表
        int threadCount = 1;
        pthread_t tid[threadCount];
        pthread_create(&tid[0], NULL, AlterDiskLimitTable, NULL);

        // 每个分区写入一批数据触发磁盘整理
        for (int32_t i = 0; i < interval; i++) {
            batchCount = 10;       // 写入1批数据
            totalCount = 10 * 30;  // 当前已写入的数据量
            startCount = 10 * 29;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 1; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                if (ret) {
                    break;
                }
                startCount += batchCount;
                totalCount += batchCount;
            }
        }
        pthread_join(tid[0], NULL);
        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(1, GetCuNumValue(1));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 1"));
        // 每个分区写入30批数据触发磁盘整理
        for (int32_t i = 0; i < interval; i++) {
            batchCount = 1;            // 每批写入1条数据
            totalCount = 29 * 30 + 1;  // 当前已写入的数据量
            startCount = 29 * 30;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 30; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                if (ret) {
                    break;
                }
                startCount += batchCount;
                totalCount += batchCount;
            }
        }

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(2, GetCuNumValue(2));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 2"));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(30 * interval * 10 + 30 * interval, CheckDataCount(stmt, tableNameA));

        // 删表
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameA, tableNameB));
        FreeMemForTextData(ns, dataCount);  // 清理前序用例申请的内存

        // 查询环境是否清理干净
        // DiskUsageInfoT PhyDiskUsageInfoT TsTblOperStatisInfoT TsTblPropsInfoT
        DiskUsageInfoT diskUsageInfo{tableNameA};
        PhyDiskUsageInfoT phyDiskUsageInfo{tableNameA};
        TsTblOperStatisInfoT tsTblOperStatisInfo{tableNameA};
        TsTblPropsInfoT tsTblPropsInfo{tableNameA};
        ret = CheckViewValue(&diskUsageInfo, &phyDiskUsageInfo, &tsTblOperStatisInfo, &tsTblPropsInfo);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (isCompress == false) {
            break;
        }
        isCompress = false;
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 017.100分区单批次写入多分区数据，单分区少于30个时，不触发磁盘整理，(压缩模式：no，默认)并发alter 新列
TEST_F(DDefragHarden, Timing_081_DDefragHarden_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    uint32_t cmdLen = 0;
    bool isCompress = true;  // 压缩模式 no,默认
    // 构造多时区数据
    int32_t nowTimeValue = GetCurrentTime();
    const int32_t interval = 100;  // 时间分区数量
    int newStartTime[interval] = {0};
    // 数组长度和大小不要计算错误
    for (int32_t i = 0; i < sizeof(newStartTime) / sizeof(newStartTime[0]); i++) {
        newStartTime[i] = nowTimeValue + 3600 * (1 + i);
    }
    // 建表
    while (true) {
        // 创建需要使用到的表
        int ret = CreateTsTable(stmt, tableNameA, tableNameB, isCompress);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 准备数据
        char **ns = NULL;
        int32_t dataCount = 1000;
        ret = AllocMemForTextData(&ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GenerateRandomString(ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 小批量数据
        int32_t batchCount = 2;  // 每批写入29条数据
        int32_t totalCount = 1;  // 当前已写入的数据量
        int startCount = 0;

        // 小批量写入29批数据
        int startTime = 0;
        for (int32_t i = 0; i < interval; i++) {
            batchCount = 10;  // 每批写入20条数据
            totalCount = 10;  // 当前已写入的数据量
            startCount = 0;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 29; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                startCount += batchCount;
                totalCount += batchCount;
            }
        }

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(29, GetCuNumValue(29));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 29"));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(29 * interval * batchCount, CheckDataCount(stmt, tableNameA));

        // 并發新增表B
        int threadCount = 1;
        pthread_t tid[threadCount];
        pthread_create(&tid[0], NULL, AlterNewColumnTable, NULL);

        // 每个分区写入一批数据触发磁盘整理
        for (int32_t i = 0; i < interval; i++) {
            batchCount = 10;       // 写入1批数据
            totalCount = 10 * 30;  // 当前已写入的数据量
            startCount = 10 * 29;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 1; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                if (ret) {
                    break;
                }
                startCount += batchCount;
                totalCount += batchCount;
            }
        }
        pthread_join(tid[0], NULL);
        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(1, GetCuNumValue(1));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 1"));
        // 每个分区写入30批数据触发磁盘整理
        for (int32_t i = 0; i < interval; i++) {
            batchCount = 1;            // 每批写入1条数据
            totalCount = 29 * 30 + 1;  // 当前已写入的数据量
            startCount = 29 * 30;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 30; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                if (ret) {
                    break;
                }
                startCount += batchCount;
                totalCount += batchCount;
            }
        }

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(2, GetCuNumValue(2));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 2"));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(30 * interval * 10 + 30 * interval, CheckDataCount(stmt, tableNameA));

        // 删表
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameA, tableNameB));
        FreeMemForTextData(ns, dataCount);  // 清理前序用例申请的内存

        // 查询环境是否清理干净
        // DiskUsageInfoT PhyDiskUsageInfoT TsTblOperStatisInfoT TsTblPropsInfoT
        DiskUsageInfoT diskUsageInfo{tableNameA};
        PhyDiskUsageInfoT phyDiskUsageInfo{tableNameA};
        TsTblOperStatisInfoT tsTblOperStatisInfo{tableNameA};
        TsTblPropsInfoT tsTblPropsInfo{tableNameA};
        ret = CheckViewValue(&diskUsageInfo, &phyDiskUsageInfo, &tsTblOperStatisInfo, &tsTblPropsInfo);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (isCompress == false) {
            break;
        }
        isCompress = false;
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 018.写入30次小批量数据的数据时间列在tolerance（数据乱序）范围，最后一批数据存在往前跨分区不超过1min数据(压缩模式：no，默认)
// 不会触发磁盘整理，后续能够正常触发磁盘整理(未卡到)
TEST_F(DDefragHarden, Timing_081_DDefragHarden_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    uint32_t cmdLen = 0;
    bool isCompress = true;  // 压缩模式 no,默认
    // 构造多时区数据
    int32_t nowTimeValue = GetCurrentTime();
    const int32_t interval = 1;  // 时间分区数量
    int64_t newStartTime[interval] = {0};
    // 数组长度和大小不要计算错误
    for (int32_t i = 0; i < sizeof(newStartTime) / sizeof(newStartTime[0]); i++) {
        newStartTime[i] = nowTimeValue + i * 3600;
    }
    // 建表
    while (true) {
        // 创建需要使用到的表
        int ret = CreateTsTable(stmt, tableNameA, tableNameB, isCompress);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 准备数据
        char **ns = NULL;
        int32_t dataCount = 1000;
        ret = AllocMemForTextData(&ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GenerateRandomString(ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 小批量数据
        int32_t batchCount = 2;  // 每批写入29条数据
        int32_t totalCount = 1;  // 当前已写入的数据量
        int startCount = 0;

        // 小批量写入29批数据
        int64_t startTime = 0;
        for (int32_t i = 0; i < interval; i++) {
            batchCount = 1;  // 每批写入10条数据
            totalCount = 1;  // 当前已写入的数据量
            startCount = 0;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 29; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                startCount += batchCount;
                totalCount += batchCount;
            }
        }

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(29, GetCuNumValue(29));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 29"));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(29 * interval * batchCount, CheckDataCount(stmt, tableNameA));

        // 每个分区写入一批数据触发磁盘整理
        for (int32_t i = 0; i < interval; i++) {
            batchCount = 10;           // 写入1批数据
            totalCount = 1 * 30 + 10;  // 当前已写入的数据量
            startCount = 1 * 29;
            startTime = newStartTime[i];
            g_jumpTime = 2;  // 最后一条数据时间跳变
            for (int32_t i = 0; i < 1; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                if (ret) {
                    break;
                }
                startCount += batchCount;
                totalCount += batchCount;
            }
        }
        ret = ReadTableA(stmt, tableNameA, totalCount, startTime, 0, ns);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(30, GetCuNumValue(30));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 30"));
        // 每个分区写入30批数据触发磁盘整理
        g_jumpTime = 0;
        for (int32_t i = 0; i < interval; i++) {
            batchCount = 10;           // 每批写入1条数据
            totalCount = 29 * 30 + 1;  // 当前已写入的数据量
            startCount = 29 * 30;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 30; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                if (ret) {
                    break;
                }
                startCount += batchCount;
                totalCount += batchCount;
            }
        }

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(2, GetCuNumValue(2));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 2"));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(30 * interval * batchCount + 30 * interval, CheckDataCount(stmt, tableNameA));

        // 删表
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameA, tableNameB));
        FreeMemForTextData(ns, dataCount);  // 清理前序用例申请的内存

        // 查询环境是否清理干净
        // DiskUsageInfoT PhyDiskUsageInfoT TsTblOperStatisInfoT TsTblPropsInfoT
        DiskUsageInfoT diskUsageInfo{tableNameA};
        PhyDiskUsageInfoT phyDiskUsageInfo{tableNameA};
        TsTblOperStatisInfoT tsTblOperStatisInfo{tableNameA};
        TsTblPropsInfoT tsTblPropsInfo{tableNameA};
        ret = CheckViewValue(&diskUsageInfo, &phyDiskUsageInfo, &tsTblOperStatisInfo, &tsTblPropsInfo);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (isCompress == false) {
            break;
        }
        isCompress = false;
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 019.写入30次小批量数据的数据时间列在tolerance范围，最后一批数据存在往后跨分区不超过1min数据(压缩模式：no，默认)
// 不会触发磁盘整理，后续能够正常触发磁盘整理(未卡到)
TEST_F(DDefragHarden, Timing_081_DDefragHarden_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    uint32_t cmdLen = 0;
    bool isCompress = true;  // 压缩模式 no,默认
    // 构造多时区数据
    int32_t nowTimeValue = GetCurrentTime();
    const int32_t interval = 1;  // 时间分区数量
    int64_t newStartTime[interval] = {0};
    // 数组长度和大小不要计算错误
    for (int32_t i = 0; i < sizeof(newStartTime) / sizeof(newStartTime[0]); i++) {
        newStartTime[i] = nowTimeValue + i * 3600;
    }
    // 建表
    while (true) {
        // 创建需要使用到的表
        int ret = CreateTsTable(stmt, tableNameA, tableNameB, isCompress);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 准备数据
        char **ns = NULL;
        int32_t dataCount = 1000;
        ret = AllocMemForTextData(&ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GenerateRandomString(ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 小批量数据
        int32_t batchCount = 2;  // 每批写入29条数据
        int32_t totalCount = 1;  // 当前已写入的数据量
        int startCount = 0;

        // 小批量写入29批数据
        int64_t startTime = 0;
        for (int32_t i = 0; i < interval; i++) {
            batchCount = 10;  // 每批写入10条数据
            totalCount = 10;  // 当前已写入的数据量
            startCount = 0;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 29; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                startCount += batchCount;
                totalCount += batchCount;
            }
        }

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(29, GetCuNumValue(29));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 29"));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(29 * interval * batchCount, CheckDataCount(stmt, tableNameA));

        // 每个分区写入一批数据触发磁盘整理
        for (int32_t i = 0; i < interval; i++) {
            batchCount = 10;       // 写入1批数据
            totalCount = 10 * 30;  // 当前已写入的数据量
            startCount = 10 * 29;
            startTime = newStartTime[i];
            g_jumpTime = 1;  // 最后一条数据时间跳变
            for (int32_t i = 0; i < 1; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                if (ret) {
                    break;
                }
                startCount += batchCount;
                totalCount += batchCount;
            }
        }

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(30, GetCuNumValue(30));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 30"));
        // 每个分区写入30批数据触发磁盘整理
        g_jumpTime = 0;
        for (int32_t i = 0; i < interval; i++) {
            batchCount = 1;            // 每批写入1条数据
            totalCount = 29 * 30 + 1;  // 当前已写入的数据量
            startCount = 29 * 30;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 30; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                if (ret) {
                    break;
                }
                startCount += batchCount;
                totalCount += batchCount;
            }
        }

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(2, GetCuNumValue(2));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 2"));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(30 * interval * batchCount + 30 * interval, CheckDataCount(stmt, tableNameA));

        // 删表
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameA, tableNameB));
        FreeMemForTextData(ns, dataCount);  // 清理前序用例申请的内存

        // 查询环境是否清理干净
        // DiskUsageInfoT PhyDiskUsageInfoT TsTblOperStatisInfoT TsTblPropsInfoT
        DiskUsageInfoT diskUsageInfo{tableNameA};
        PhyDiskUsageInfoT phyDiskUsageInfo{tableNameA};
        TsTblOperStatisInfoT tsTblOperStatisInfo{tableNameA};
        TsTblPropsInfoT tsTblPropsInfo{tableNameA};
        ret = CheckViewValue(&diskUsageInfo, &phyDiskUsageInfo, &tsTblOperStatisInfo, &tsTblPropsInfo);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (isCompress == false) {
            break;
        }
        isCompress = false;
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 020.构造数据，前29次数据小批量写入正常，第30次写入小批量数据时到达磁盘上限，预期不会触达磁盘整理(压缩模式：no，默认)
TEST_F(DDefragHarden, Timing_081_DDefragHarden_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    uint32_t cmdLen = 0;
    bool isCompress = true;                       // 压缩模式 no,默认
    int32_t batchCount = 15;                      // 每批写入29条数据
    int32_t totalCount = 15;                      // 当前已写入的数据量
    int oldStartTime = GetCurrentTime() - 10800;  // 当前时间减2H已过期数据
    int newStartTime = GetCurrentTime() + 7200;   // 当前时间加2H未过期数据
    int startCount = 0;
    // 建表
    while (true) {
        // 创建需要使用到的表
        int ret = CreateTsTable(stmt, tableNameA, tableNameB, isCompress);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 准备数据
        char **ns = NULL;
        int32_t dataCount = 1000;
        ret = AllocMemForTextData(&ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GenerateRandomString(ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 小批量数据
        batchCount = 29;  // 每批写入29条数据
        totalCount = 29;  // 当前已写入的数据量
        startCount = 0;

        // alter 修改disk_limit
        (void)memset(sqlCmd, 0, sizeof(sqlCmd));
        (void)(void)sprintf(sqlCmd, "alter table %s SET (disk_limit = '20 MB');", tableNameA);
        ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 小批量写入30批数据
        int startTime = newStartTime;
        for (int32_t i = 0; i < 30; i++) {
            ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            startCount += batchCount;
            totalCount += batchCount;
        }

        // 查cu块统计视图 29(1)
        AW_MACRO_ASSERT_NE_INT(1, GetCuNumValue(1));

        // 校验数据条数
        AW_MACRO_ASSERT_NE_INT(0, CheckDataCount(stmt, tableNameA));

        // 删表
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameA, tableNameB));
        FreeMemForTextData(ns, dataCount);  // 清理前序用例申请的内存

        // 查询环境是否清理干净
        // DiskUsageInfoT PhyDiskUsageInfoT TsTblOperStatisInfoT TsTblPropsInfoT
        DiskUsageInfoT diskUsageInfo{tableNameA};
        PhyDiskUsageInfoT phyDiskUsageInfo{tableNameA};
        TsTblOperStatisInfoT tsTblOperStatisInfo{tableNameA};
        TsTblPropsInfoT tsTblPropsInfo{tableNameA};
        ret = CheckViewValue(&diskUsageInfo, &phyDiskUsageInfo, &tsTblOperStatisInfo, &tsTblPropsInfo);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (isCompress == false) {
            break;
        }
        isCompress = false;
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 021.起服务，关闭tsAllowDiskClean，写多个小批量数据，第30次写数据时超过磁盘上限，报错，不合并，开启tsAllowDiskClean，再次写入数据合并(压缩模式：no，默认)
TEST_F(DDefragHarden, Timing_081_DDefragHarden_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    uint32_t cmdLen = 0;
    bool isCompress = true;  // 压缩模式 no,默认
    AddWhiteList(GMERR_TABLE_EXCEED_DISK_LIMIT);
    // 构造多时区数据
    int32_t nowTimeValue = GetCurrentTime();
    const int32_t interval = 1;  // 时间分区数量
    int newStartTime[interval] = {0};
    // 数组长度和大小不要计算错误
    for (int32_t i = 0; i < sizeof(newStartTime) / sizeof(newStartTime[0]); i++) {
        newStartTime[i] = nowTimeValue + 3600 * (1 + i);
    }
    // 建表
    while (true) {
        // 创建需要使用到的表
        int ret = CreateTsTable(stmt, tableNameA, tableNameB, isCompress);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        (void)memset(sqlCmd, 0, sizeof(sqlCmd));
        (void)(void)sprintf(sqlCmd, "alter table %s SET (disk_limit = '40 MB');", tableNameA);
        ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 准备数据
        char **ns = NULL;
        int32_t dataCount = 1000;
        ret = AllocMemForTextData(&ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GenerateRandomString(ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 修改配置项tsAllowDiskClean为0
        uint32_t setConfigValue = 0;
        ret = GmcSetCfg(stmt, "tsAllowDiskClean", GMC_DATATYPE_INT32, &setConfigValue, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 小批量数据
        int32_t batchCount = 2;  // 每批写入29条数据
        int32_t totalCount = 1;  // 当前已写入的数据量
        int startCount = 0;

        // 小批量写入29批数据
        int startTime = 0;
        for (int32_t j = 0; j < interval; j++) {
            batchCount = 29;  // 每批写入20条数据
            totalCount = 29;  // 当前已写入的数据量
            startCount = 0;
            startTime = newStartTime[j];
            for (int32_t i = 0; i < 29; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                if (ret) {
                    AW_FUN_Log(LOG_STEP, "interval is %d,ret is %d, i is %d.", j, ret, i);
                }
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                startCount += batchCount;
                totalCount += batchCount;
            }
        }
        AW_MACRO_ASSERT_EQ_INT(29, GetCuNumValue(29));
        int32_t cuCnt = 0;
        for (int32_t j = 0; j < interval; j++) {
            batchCount = 20;  // 每批写入20条数据
            totalCount = 20;  // 当前已写入的数据量
            startCount = 0;

            startTime = newStartTime[j] + 3600;
            for (int32_t i = 0; i < 30; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                if (ret) {
                    AW_FUN_Log(LOG_STEP, "interval is %d,ret is %d, i is %d.", j, ret, i);
                    cuCnt = i;
                    break;
                }
                startCount += batchCount;
                if (i == 28) {
                    totalCount += 400;
                    AW_FUN_Log(LOG_STEP, "interval is %d,ret is %d, i is %d.", j, ret, i);
                } else {
                    totalCount += batchCount;
                }
                AW_FUN_Log(LOG_STEP, "interval is %d,ret is %d, i is %d.", j, ret, i);
            }
        }
        AW_MACRO_ASSERT_NE_INT(GMERR_OK, ret);
        // 查cu块统计视图 cu块视图显示顺序随机
        char expectCnt[24] = "CU_CNT: 29";
        (void)(void)sprintf(expectCnt, "CU_CNT: %d", cuCnt);
        AW_MACRO_ASSERT_EQ_INT(1, GetAllCuNumValue(1, (char *)expectCnt));

        // // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(cuCnt * interval * 20 + 29 * interval * 29, CheckDataCount(stmt, tableNameA));

        setConfigValue = 1;
        ret = GmcSetCfg(stmt, "tsAllowDiskClean", GMC_DATATYPE_INT32, &setConfigValue, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        // 每个分区写入一批数据触发磁盘整理
        for (int32_t i = 0; i < interval; i++) {
            startTime = newStartTime[i] + 3600;
            batchCount = 1;
            for (int32_t i = 0; i < 30; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                if (ret) {
                    break;
                }
                startCount += batchCount;
                totalCount += batchCount;
            }
            if (i == cuCnt) {
                AW_MACRO_ASSERT_EQ_INT(30, GetCuNumValue(30));
            }
            if (ret) {
                break;
            }
        }
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(cuCnt + 1, GetCuNumValue(cuCnt + 1));

        // 删表
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameA, tableNameB));
        FreeMemForTextData(ns, dataCount);  // 清理前序用例申请的内存

        // 查询环境是否清理干净
        // DiskUsageInfoT PhyDiskUsageInfoT TsTblOperStatisInfoT TsTblPropsInfoT
        DiskUsageInfoT diskUsageInfo{tableNameA};
        PhyDiskUsageInfoT phyDiskUsageInfo{tableNameA};
        TsTblOperStatisInfoT tsTblOperStatisInfo{tableNameA};
        TsTblPropsInfoT tsTblPropsInfo{tableNameA};
        ret = CheckViewValue(&diskUsageInfo, &phyDiskUsageInfo, &tsTblOperStatisInfo, &tsTblPropsInfo);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (isCompress == false) {
            break;
        }
        isCompress = false;
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 022.写入多次小批量数据数据跨分区还未到30次，中途主动老化，最老分区数据被删除，再次写最老分区数据累计到达30次时查询cu数量
// 。。循环老化多次(压缩模式：no，默认)
TEST_F(DDefragHarden, Timing_081_DDefragHarden_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    uint32_t cmdLen = 0;
    bool isCompress = true;  // 压缩模式 no,默认
    // 构造多时区数据
    int32_t nowTimeValue = GetCurrentTime();
    const int32_t interval = 2;  // 时间分区数量
    int newStartTime[interval] = {0};
    // 数组长度和大小不要计算错误
    for (int32_t i = 0; i < sizeof(newStartTime) / sizeof(newStartTime[0]); i++) {
        newStartTime[i] = nowTimeValue + 3600 * (1 + i);
    }
    // 建表
    while (true) {
        // 创建需要使用到的表
        int ret = CreateTsTable(stmt, tableNameA, tableNameB, isCompress);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 准备数据
        char **ns = NULL;
        int32_t dataCount = 1000;
        ret = AllocMemForTextData(&ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GenerateRandomString(ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 小批量数据
        int32_t batchCount = 2;  // 每批写入29条数据
        int32_t totalCount = 1;  // 当前已写入的数据量
        int startCount = 0;

        // 小批量写入29批数据
        int startTime = 0;
        for (int32_t i = 0; i < interval; i++) {
            batchCount = 20;  // 每批写入20条数据
            totalCount = 20;  // 当前已写入的数据量
            startCount = 0;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 29; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                startCount += batchCount;
                totalCount += batchCount;
            }
        }

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(29, GetCuNumValue(29));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 29"));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(29 * interval * batchCount, CheckDataCount(stmt, tableNameA));

        // 主动老化最老分区
        (void)memset(sqlCmd, 0, sizeof(sqlCmd));
        (void)(void)sprintf(sqlCmd, "SELECT tsdb_aging('%s');", tableNameA);
        ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        // 每个分区写入一批数据触发磁盘整理
        for (int32_t i = 0; i < interval; i++) {
            batchCount = 20;       // 写入1批数据
            totalCount = 20 * 30;  // 当前已写入的数据量
            startCount = 20 * 29;
            startTime = newStartTime[i];
            int32_t cycle = 1;
            if (i == 0) {
                cycle = 30;
                batchCount = 20;  // 每批写入20条数据
                totalCount = 20;  // 当前已写入的数据量
                startCount = 0;
            }
            for (int32_t i = 0; i < cycle; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                if (ret) {
                    break;
                }
                startCount += batchCount;
                totalCount += batchCount;
            }
        }

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(1, GetCuNumValue(1));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 1"));
        // 每个分区写入30批数据触发磁盘整理
        for (int32_t i = 0; i < interval; i++) {
            batchCount = 1;            // 每批写入1条数据
            totalCount = 29 * 30 + 1;  // 当前已写入的数据量
            startCount = 29 * 30;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 30; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                if (ret) {
                    break;
                }
                startCount += batchCount;
                totalCount += batchCount;
            }
        }

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(2, GetCuNumValue(2));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 2"));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(1260, CheckDataCount(stmt, tableNameA));

        // 删表
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameA, tableNameB));
        FreeMemForTextData(ns, dataCount);  // 清理前序用例申请的内存

        // 查询环境是否清理干净
        // DiskUsageInfoT PhyDiskUsageInfoT TsTblOperStatisInfoT TsTblPropsInfoT
        DiskUsageInfoT diskUsageInfo{tableNameA};
        PhyDiskUsageInfoT phyDiskUsageInfo{tableNameA};
        TsTblOperStatisInfoT tsTblOperStatisInfo{tableNameA};
        TsTblPropsInfoT tsTblPropsInfo{tableNameA};
        ret = CheckViewValue(&diskUsageInfo, &phyDiskUsageInfo, &tsTblOperStatisInfo, &tsTblPropsInfo);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (isCompress == false) {
            break;
        }
        isCompress = false;
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 023.构造数据，前29次数据小批量写入正常，第30次写入小批量数据时并发进行数据老化操作，预期数据被删除(压缩模式：no，默认)
TEST_F(DDefragHarden, Timing_081_DDefragHarden_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    uint32_t cmdLen = 0;
    bool isCompress = true;  // 压缩模式 no,默认
    // 构造多时区数据
    int32_t nowTimeValue = GetCurrentTime();
    const int32_t interval = 1;  // 时间分区数量
    int newStartTime[interval] = {0};
    // 数组长度和大小不要计算错误
    for (int32_t i = 0; i < sizeof(newStartTime) / sizeof(newStartTime[0]); i++) {
        newStartTime[i] = nowTimeValue + 3600 * (1 + i);
    }
    // 建表
    while (true) {
        // 创建需要使用到的表
        int ret = CreateTsTable(stmt, tableNameA, tableNameB, isCompress);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 准备数据
        char **ns = NULL;
        int32_t dataCount = 1000;
        ret = AllocMemForTextData(&ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GenerateRandomString(ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 小批量数据
        int32_t batchCount = 2;  // 每批写入29条数据
        int32_t totalCount = 1;  // 当前已写入的数据量
        int startCount = 0;

        // 小批量写入29批数据
        int startTime = 0;
        for (int32_t i = 0; i < interval; i++) {
            batchCount = 20;  // 每批写入20条数据
            totalCount = 20;  // 当前已写入的数据量
            startCount = 0;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 29; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                startCount += batchCount;
                totalCount += batchCount;
            }
        }

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(29, GetCuNumValue(29));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 29"));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(29 * interval * batchCount, CheckDataCount(stmt, tableNameA));

        // 并发老化最老分区
        int threadCount = 1;
        pthread_t tid[threadCount];
        pthread_create(&tid[0], NULL, AgeOutTable, NULL);

        // 每个分区写入一批数据触发磁盘整理
        for (int32_t i = 0; i < interval; i++) {
            batchCount = 20;       // 写入1批数据
            totalCount = 20 * 30;  // 当前已写入的数据量
            startCount = 20 * 29;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 1; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                if (ret) {
                    break;
                }
                startCount += batchCount;
                totalCount += batchCount;
            }
        }
        int32_t cnt = CheckDataCount(stmt, tableNameA);
        // 查cu块统计视图 总写入次数/30
        if (cnt != 0) {
            AW_MACRO_ASSERT_EQ_INT(1, GetCuNumValue(1));
            AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 1"));
        }

        // 每个分区写入30批数据触发磁盘整理
        for (int32_t i = 0; i < interval; i++) {
            batchCount = 1;            // 每批写入1条数据
            totalCount = 29 * 30 + 1;  // 当前已写入的数据量
            startCount = 29 * 30;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 30; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                if (ret) {
                    break;
                }
                startCount += batchCount;
                totalCount += batchCount;
            }
        }
        pthread_join(tid[0], NULL);

        // 查cu块统计视图 总写入次数/30
        if (cnt != 0) {
            AW_MACRO_ASSERT_EQ_INT(2, GetCuNumValue(2));
            AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 2"));
        } else {
            AW_MACRO_ASSERT_EQ_INT(1, GetCuNumValue(1));
            AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 1"));
        }

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(cnt + 30, CheckDataCount(stmt, tableNameA));

        // 删表
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameA, tableNameB));
        FreeMemForTextData(ns, dataCount);  // 清理前序用例申请的内存

        // 查询环境是否清理干净
        // DiskUsageInfoT PhyDiskUsageInfoT TsTblOperStatisInfoT TsTblPropsInfoT
        DiskUsageInfoT diskUsageInfo{tableNameA};
        PhyDiskUsageInfoT phyDiskUsageInfo{tableNameA};
        TsTblOperStatisInfoT tsTblOperStatisInfo{tableNameA};
        TsTblPropsInfoT tsTblPropsInfo{tableNameA};
        ret = CheckViewValue(&diskUsageInfo, &phyDiskUsageInfo, &tsTblOperStatisInfo, &tsTblPropsInfo);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (isCompress == false) {
            break;
        }
        isCompress = false;
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 024.开启独立重启，设备重启的过程中，持续写入30次小批量数据，查询cu
// 数量，再次写入多次小批量数据后再写入的数据条数大于等于30条时触发合并，，再次写入多次小批量数据后再写入的数据含空列是触发合并(压缩模式：no，默认)
// 获取快照，切换表操作后，再设置快照触发磁盘整理
TEST_F(DDefragHarden, Timing_081_DDefragHarden_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    uint32_t cmdLen = 0;
    bool isCompress = true;                       // 压缩模式 no,默认
    int32_t batchCount = 15;                      // 每批写入29条数据
    int32_t totalCount = 15;                      // 当前已写入的数据量
    int oldStartTime = GetCurrentTime() - 10800;  // 当前时间减2H已过期数据
    int newStartTime = GetCurrentTime() + 7200;   // 当前时间加2H未过期数据
    int startCount = 0;
    bool isTablePath = true;
    char newDataDir[256] = {0};
    (void)(void)sprintf(newDataDir, "%s/newData/", g_currentDir);
    (void)(void)sprintf(sqlCmd, "rm -rf %s", newDataDir);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, system(sqlCmd));
    // 建表
    while (true) {
        // 创建需要使用到的表
        int ret = CreateTsTable(stmt, tableNameA, tableNameB, isCompress, isTablePath, newDataDir);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 准备数据
        char **ns = NULL;
        int32_t dataCount = 1000;
        ret = AllocMemForTextData(&ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GenerateRandomString(ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 小批量数据
        batchCount = 5;  // 每批写入5条数据
        totalCount = 5;  // 当前已写入的数据量
        startCount = 0;

        // 小批量写入29批数据
        int startTime = newStartTime;
        for (int32_t i = 0; i < 29; i++) {
            ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            startCount += batchCount;
            totalCount += batchCount;
        }

        // 查cu块统计视图 29(1)
        AW_MACRO_ASSERT_EQ_INT(29, GetCuNumValue(29));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(29 * batchCount, CheckDataCount(stmt, tableNameA));

        // 重启服务,重启前数据不会再磁盘整理
        RestartTsdb();

        // 查cu块统计视图 29(1)
        AW_MACRO_ASSERT_EQ_INT(29, GetCuNumValue(29));
        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(29 * batchCount, CheckDataCount(stmt, tableNameA));

        // 再次写入数据
        for (int32_t i = 0; i < 1; i++) {
            ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            startCount += batchCount;
            totalCount += batchCount;
        }
        // 查cu块统计视图 29(1) + 1(1)
        AW_MACRO_ASSERT_EQ_INT(30, GetCuNumValue(30));

        // 获取快照，操作表B
        GmcStmtViewT *stmtView = NULL;
        ret = GmcGetStmtView(stmt, &stmtView);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        for (int32_t i = 0; i < 1; i++) {
            ret = insertDataToTableB(stmt, tableNameB, totalCount, startTime, startCount, ns + startCount);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }

        // 设置快照，触发A表磁盘整理
        ret = GmcSetStmtView(stmt, stmtView);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 再次写入数据
        for (int32_t i = 0; i < 29; i++) {
            ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            startCount += batchCount;
            totalCount += batchCount;
        }
        // 查cu块统计视图 29(1)+ 1(30)
        AW_MACRO_ASSERT_EQ_INT(30, GetCuNumValue(30));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT((29 + 30) * batchCount, CheckDataCount(stmt, tableNameA));

        // 删表
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameA, tableNameB));
        FreeMemForTextData(ns, dataCount);  // 清理前序用例申请的内存

        // 查询环境是否清理干净
        // DiskUsageInfoT PhyDiskUsageInfoT TsTblOperStatisInfoT TsTblPropsInfoT
        DiskUsageInfoT diskUsageInfo{tableNameA};
        PhyDiskUsageInfoT phyDiskUsageInfo{tableNameA};
        TsTblOperStatisInfoT tsTblOperStatisInfo{tableNameA};
        TsTblPropsInfoT tsTblPropsInfo{tableNameA};
        ret = CheckViewValue(&diskUsageInfo, &phyDiskUsageInfo, &tsTblOperStatisInfo, &tsTblPropsInfo);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (isCompress == false) {
            break;
        }
        isCompress = false;
        GmcFreeStmtView(stmtView);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 025.连续使用insert into，写入30次小批量数据，查询cu数量(压缩模式：no，默认)
TEST_F(DDefragHarden, Timing_081_DDefragHarden_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    uint32_t cmdLen = 0;
    bool isCompress = true;  // 压缩模式 no,默认
    // 构造多时区数据
    int32_t nowTimeValue = GetCurrentTime();
    const int32_t interval = 10;  // 时间分区数量
    int newStartTime[interval] = {0};
    // 数组长度和大小不要计算错误
    for (int32_t i = 0; i < sizeof(newStartTime) / sizeof(newStartTime[0]); i++) {
        newStartTime[i] = nowTimeValue + 3600 * (1 + i);
    }
    // 建表
    while (true) {
        // 创建需要使用到的表
        int ret = CreateTsTable(stmt, tableNameA, tableNameB, isCompress);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 准备数据
        char **ns = NULL;
        int32_t dataCount = 1000;
        ret = AllocMemForTextData(&ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GenerateRandomString(ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 小批量数据
        int32_t batchCount = 2;  // 每批写入29条数据
        int32_t totalCount = 1;  // 当前已写入的数据量
        int startCount = 0;

        // 小批量写入29批数据
        int startTime = 0;
        for (int32_t k = 0; k < 30; k++) {
            for (int32_t i = 0; i < interval; i++) {
                batchCount = 1;          // 每批写入20条数据
                totalCount = 1 + k * 2;  // 当前已写入的数据量
                startCount = 0 + k * 2;
                startTime = newStartTime[i];
                for (int32_t i = 0; i < 2; i++) {
                    ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                    startCount += batchCount;
                    totalCount += batchCount;
                }
                // 执行insert into语句
                (void)(void)sprintf(
                    sqlCmd, "insert into %s select id, time, message, ns from %s;", tableNameB, tableNameA);
                uint32_t cmdLen = strlen(sqlCmd);
                ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

                char sqlCmdTruncate[128] = "truncate table testdbA;";
                ret = GmcExecDirect(stmt, sqlCmdTruncate, 128);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                usleep(1000);
            }
        }
        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(1, GetCuNumValue(1, tableNameB));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 1", tableNameB));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(30 * interval * 2, CheckDataCount(stmt, tableNameB));
        // 删表
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameA, tableNameB));
        FreeMemForTextData(ns, dataCount);  // 清理前序用例申请的内存

        // 查询环境是否清理干净
        // DiskUsageInfoT PhyDiskUsageInfoT TsTblOperStatisInfoT TsTblPropsInfoT
        DiskUsageInfoT diskUsageInfo{tableNameA};
        PhyDiskUsageInfoT phyDiskUsageInfo{tableNameA};
        TsTblOperStatisInfoT tsTblOperStatisInfo{tableNameA};
        TsTblPropsInfoT tsTblPropsInfo{tableNameA};
        ret = CheckViewValue(&diskUsageInfo, &phyDiskUsageInfo, &tsTblOperStatisInfo, &tsTblPropsInfo);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (isCompress == false) {
            break;
        }
        isCompress = false;
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 026.写入小批量数据累计29次时Truncate删除数据（整理计数归0），再次写入一次小批量数据，再次从truncate操作执行之后开始累计写入30次小批量数据(压缩模式：no，默认)
TEST_F(DDefragHarden, Timing_081_DDefragHarden_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    uint32_t cmdLen = 0;
    bool isCompress = true;  // 压缩模式 no,默认
    // 构造多时区数据
    int32_t nowTimeValue = GetCurrentTime();
    const int32_t interval = 100;  // 时间分区数量
    int newStartTime[interval] = {0};
    // 数组长度和大小不要计算错误
    for (int32_t i = 0; i < sizeof(newStartTime) / sizeof(newStartTime[0]); i++) {
        newStartTime[i] = nowTimeValue + 3600 * (1 + i);
    }
    // 建表
    while (true) {
        // 创建需要使用到的表
        int ret = CreateTsTable(stmt, tableNameA, tableNameB, isCompress);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        // 准备数据
        char **ns = NULL;
        int32_t dataCount = 1000;
        ret = AllocMemForTextData(&ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GenerateRandomString(ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 小批量数据
        int32_t batchCount = 2;  // 每批写入29条数据
        int32_t totalCount = 1;  // 当前已写入的数据量
        int startCount = 0;

        // 预置数据
        int startTime = 0;
        for (int32_t i = 0; i < interval; i++) {
            batchCount = 20;  // 每批写入20条数据
            totalCount = 29;  // 当前已写入的数据量
            startCount = 0;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 29; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                startCount += batchCount;
                totalCount += batchCount;
            }
        }

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(29, GetCuNumValue(29));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 29"));

        char sqlCmdTruncate[128] = "truncate table testdbA;";
        ret = GmcExecDirect(stmt, sqlCmdTruncate, 128);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        sleep(1);

        // 每个分区写入一批数据触发磁盘整理
        for (int32_t i = 0; i < interval; i++) {
            batchCount = 1;    // 写入1批数据
            totalCount = 291;  // 当前已写入的数据量
            startCount = 290;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 29; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                if (ret) {
                    break;
                }
                startCount += batchCount;
                totalCount += batchCount;
            }
        }

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(29, GetCuNumValue(29));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 29"));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(interval * 29, CheckDataCount(stmt, tableNameA));

        for (int32_t i = 0; i < interval; i++) {
            batchCount = 29;             // 写入1批数据
            totalCount = 290 + 29 + 29;  // 当前已写入的数据量
            startCount = 290 + 29;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 1; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                if (ret) {
                    break;
                }
                startCount += batchCount;
                totalCount += batchCount;
            }
        }
        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(1, GetCuNumValue(1));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 1"));
        AW_MACRO_ASSERT_EQ_INT(interval * 29 * 2, CheckDataCount(stmt, tableNameA));

        // 删表
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameA, tableNameB));
        FreeMemForTextData(ns, dataCount);  // 清理前序用例申请的内存

        // 查询环境是否清理干净
        // DiskUsageInfoT PhyDiskUsageInfoT TsTblOperStatisInfoT TsTblPropsInfoT
        DiskUsageInfoT diskUsageInfo{tableNameA};
        PhyDiskUsageInfoT phyDiskUsageInfo{tableNameA};
        TsTblOperStatisInfoT tsTblOperStatisInfo{tableNameA};
        TsTblPropsInfoT tsTblPropsInfo{tableNameA};
        ret = CheckViewValue(&diskUsageInfo, &phyDiskUsageInfo, &tsTblOperStatisInfo, &tsTblPropsInfo);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (isCompress == false) {
            break;
        }
        isCompress = false;
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 027.大批量预置数据后，写入29次小批量数据第29次时并发truncate，第30次写入数据查询cu数量(压缩模式：no，默认)
TEST_F(DDefragHarden, Timing_081_DDefragHarden_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    uint32_t cmdLen = 0;
    bool isCompress = true;  // 压缩模式 no,默认
    // 构造多时区数据
    int32_t nowTimeValue = GetCurrentTime();
    const int32_t interval = 100;  // 时间分区数量
    int newStartTime[interval] = {0};
    // 数组长度和大小不要计算错误
    for (int32_t i = 0; i < sizeof(newStartTime) / sizeof(newStartTime[0]); i++) {
        newStartTime[i] = nowTimeValue + 3600 * (1 + i);
    }
    // 建表
    while (true) {
        // 创建需要使用到的表
        int ret = CreateTsTable(stmt, tableNameA, tableNameB, isCompress);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        // 准备数据
        char **ns = NULL;
        int32_t dataCount = 1000;
        ret = AllocMemForTextData(&ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GenerateRandomString(ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 小批量数据
        int32_t batchCount = 2;  // 每批写入29条数据
        int32_t totalCount = 1;  // 当前已写入的数据量
        int startCount = 0;

        // 预置数据
        int startTime = 0;
        for (int32_t i = 0; i < interval; i++) {
            batchCount = 290;  // 每批写入20条数据
            totalCount = 290;  // 当前已写入的数据量
            startCount = 0;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 1; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                startCount += batchCount;
                totalCount += batchCount;
            }
        }

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(1, GetCuNumValue(1));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 1"));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(interval * batchCount, CheckDataCount(stmt, tableNameA));

        // 每个分区写入一批数据触发磁盘整理
        for (int32_t i = 0; i < interval; i++) {
            batchCount = 1;    // 写入1批数据
            totalCount = 291;  // 当前已写入的数据量
            startCount = 290;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 29; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                if (ret) {
                    break;
                }
                startCount += batchCount;
                totalCount += batchCount;
            }
        }

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(30, GetCuNumValue(30));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 30"));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(interval * (290 + 29), CheckDataCount(stmt, tableNameA));
        // 并发Truncate表
        int threadCount = 1;
        pthread_t tid[threadCount];
        pthread_create(&tid[0], NULL, TruncateTable, NULL);
        for (int32_t i = 0; i < interval; i++) {
            batchCount = 29;             // 写入1批数据
            totalCount = 290 + 29 + 29;  // 当前已写入的数据量
            startCount = 290 + 29;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 1; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                if (ret) {
                    break;
                }
                startCount += batchCount;
                totalCount += batchCount;
            }
        }
        pthread_join(tid[0], NULL);

        // 校验数据条数
        int32_t cycyle = 29;
        if (CheckDataCount(stmt, tableNameA) == 0) {
            cycyle = 30;
        }
        // 每个分区写入一批数据触发磁盘整理
        for (int32_t i = 0; i < interval; i++) {
            batchCount = 1;    // 写入1批数据
            totalCount = 291;  // 当前已写入的数据量
            startCount = 290;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < cycyle; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                if (ret) {
                    break;
                }
                startCount += batchCount;
                totalCount += batchCount;
            }
        }

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(1, GetCuNumValue(1));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 1"));
        AW_MACRO_ASSERT_EQ_INT(interval * cycyle - (cycyle - 30) * 29 * interval, CheckDataCount(stmt, tableNameA));

        // 删表
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameA, tableNameB));
        FreeMemForTextData(ns, dataCount);  // 清理前序用例申请的内存

        // 查询环境是否清理干净
        // DiskUsageInfoT PhyDiskUsageInfoT TsTblOperStatisInfoT TsTblPropsInfoT
        DiskUsageInfoT diskUsageInfo{tableNameA};
        PhyDiskUsageInfoT phyDiskUsageInfo{tableNameA};
        TsTblOperStatisInfoT tsTblOperStatisInfo{tableNameA};
        TsTblPropsInfoT tsTblPropsInfo{tableNameA};
        ret = CheckViewValue(&diskUsageInfo, &phyDiskUsageInfo, &tsTblOperStatisInfo, &tsTblPropsInfo);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (isCompress == false) {
            break;
        }
        isCompress = false;
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 028.预置数据后，写小批量数据直到30次磁盘超1G，查询cu数量
TEST_F(DDefragHarden, Timing_081_DDefragHarden_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    uint32_t cmdLen = 0;
    bool isCompress = true;  // 压缩模式 no,默认
    // 构造多时区数据
    int32_t nowTimeValue = GetCurrentTime();
    const int32_t interval = 100;  // 时间分区数量
    int newStartTime[interval] = {0};
    // 数组长度和大小不要计算错误
    for (int32_t i = 0; i < sizeof(newStartTime) / sizeof(newStartTime[0]); i++) {
        newStartTime[i] = nowTimeValue + 3600 * (1 + i);
    }
    // 建表
    while (true) {
        // 创建需要使用到的表
        int ret = CreateTsTable(stmt, tableNameA, tableNameB, isCompress);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        (void)memset(sqlCmd, 0, sizeof(sqlCmd));
        (void)(void)sprintf(sqlCmd, "alter table %s SET (disk_limit = '1 GB');", tableNameA);
        ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 准备数据
        char **ns = NULL;
        int32_t dataCount = 1000;
        ret = AllocMemForTextData(&ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GenerateRandomString(ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 小批量数据
        int32_t batchCount = 2;  // 每批写入29条数据
        int32_t totalCount = 1;  // 当前已写入的数据量
        int startCount = 0;

        // 预置数据
        int startTime = 0;
        for (int32_t i = 0; i < interval; i++) {
            batchCount = 290;  // 每批写入20条数据
            totalCount = 290;  // 当前已写入的数据量
            startCount = 0;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 1; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                startCount += batchCount;
                totalCount += batchCount;
            }
        }

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(1, GetCuNumValue(1));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 1"));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(interval * batchCount, CheckDataCount(stmt, tableNameA));

        // 每个分区写入一批数据触发磁盘整理
        for (int32_t i = 0; i < interval; i++) {
            batchCount = 1;    // 写入1批数据
            totalCount = 291;  // 当前已写入的数据量
            startCount = 290;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 29; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                if (ret) {
                    break;
                }
                startCount += batchCount;
                totalCount += batchCount;
            }
        }

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(30, GetCuNumValue(30));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 30"));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(interval * (290 + 29), CheckDataCount(stmt, tableNameA));

        for (int32_t i = 0; i < interval; i++) {
            batchCount = 29;             // 写入1批数据
            totalCount = 290 + 29 + 29;  // 当前已写入的数据量
            startCount = 290 + 29;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 1; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                if (ret) {
                    break;
                }
                startCount += batchCount;
                totalCount += batchCount;
            }
        }

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(2, GetCuNumValue(2));
        AW_MACRO_ASSERT_EQ_INT(-1, GetAllCuNumValue(0, (char *)"CU_CNT: 2"));

        // 校验数据条数
        AW_MACRO_EXPECT_NE_INT(0, CheckDataCount(stmt, tableNameA));

        // 删表
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameA, tableNameB));
        FreeMemForTextData(ns, dataCount);  // 清理前序用例申请的内存

        // 查询环境是否清理干净
        // DiskUsageInfoT PhyDiskUsageInfoT TsTblOperStatisInfoT TsTblPropsInfoT
        DiskUsageInfoT diskUsageInfo{tableNameA};
        PhyDiskUsageInfoT phyDiskUsageInfo{tableNameA};
        TsTblOperStatisInfoT tsTblOperStatisInfo{tableNameA};
        TsTblPropsInfoT tsTblPropsInfo{tableNameA};
        ret = CheckViewValue(&diskUsageInfo, &phyDiskUsageInfo, &tsTblOperStatisInfo, &tsTblPropsInfo);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (isCompress == false) {
            break;
        }
        isCompress = false;
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 029.创建两张表A，B，写A表29次小批量数据后查询cu数量，对B表写入数据，读数据，再次往A表写入小批次数据，查询两表cu数量
TEST_F(DDefragHarden, Timing_081_DDefragHarden_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    uint32_t cmdLen = 0;
    bool isCompress = true;  // 压缩模式 no,默认
    // 构造多时区数据
    int32_t nowTimeValue = GetCurrentTime();
    const int32_t interval = 1;  // 时间分区数量
    int newStartTime[interval] = {0};
    // 数组长度和大小不要计算错误
    for (int32_t i = 0; i < sizeof(newStartTime) / sizeof(newStartTime[0]); i++) {
        newStartTime[i] = nowTimeValue + 3600 * (1 + i);
    }
    // 建表
    while (true) {
        // 创建需要使用到的表
        int ret = CreateTsTable(stmt, tableNameA, tableNameB, isCompress);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 准备数据
        char **ns = NULL;
        int32_t dataCount = 1000;
        ret = AllocMemForTextData(&ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GenerateRandomString(ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 小批量数据
        int32_t batchCount = 2;  // 每批写入29条数据
        int32_t totalCount = 1;  // 当前已写入的数据量
        int startCount = 0;

        // 小批量写入29批数据
        int startTime = 0;
        for (int32_t i = 0; i < interval; i++) {
            batchCount = 10;  // 每批写入20条数据
            totalCount = 10;  // 当前已写入的数据量
            startCount = 0;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 29; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                startCount += batchCount;
                totalCount += batchCount;
            }
        }

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(29, GetCuNumValue(29));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 29"));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(29 * interval * batchCount, CheckDataCount(stmt, tableNameA));

        // 写B表
        for (int32_t i = 0; i < interval; i++) {
            batchCount = 10;  // 每批写入20条数据
            totalCount = 10;  // 当前已写入的数据量
            startCount = 0;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 29; i++) {
                ret = insertDataToTableB(stmt, tableNameB, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                startCount += batchCount;
                totalCount += batchCount;
            }
        }

        ret = ReadTableB(stmt, tableNameB, totalCount - batchCount, startTime, 0, ns);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 每个分区写入一批数据触发磁盘整理
        for (int32_t i = 0; i < interval; i++) {
            batchCount = 10;       // 写入1批数据
            totalCount = 10 * 30;  // 当前已写入的数据量
            startCount = 10 * 29;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 1; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                if (ret) {
                    break;
                }
                startCount += batchCount;
                totalCount += batchCount;
            }
        }

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(1, GetCuNumValue(1));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 1"));

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(29, GetCuNumValue(29, tableNameB));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 29", tableNameB));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(30 * interval * batchCount, CheckDataCount(stmt, tableNameA));
        AW_MACRO_ASSERT_EQ_INT(29 * interval * batchCount, CheckDataCount(stmt, tableNameB));

        // 删表
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameA, tableNameB));
        FreeMemForTextData(ns, dataCount);  // 清理前序用例申请的内存

        // 查询环境是否清理干净
        // DiskUsageInfoT PhyDiskUsageInfoT TsTblOperStatisInfoT TsTblPropsInfoT
        DiskUsageInfoT diskUsageInfo{tableNameA};
        PhyDiskUsageInfoT phyDiskUsageInfo{tableNameA};
        TsTblOperStatisInfoT tsTblOperStatisInfo{tableNameA};
        TsTblPropsInfoT tsTblPropsInfo{tableNameA};
        ret = CheckViewValue(&diskUsageInfo, &phyDiskUsageInfo, &tsTblOperStatisInfo, &tsTblPropsInfo);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (isCompress == false) {
            break;
        }
        isCompress = false;
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 030.创建两张表A，B，写A表29次小批量数据后，对B表写入数据，读数据，往B表写入小批次数据，触发磁盘整理，查询cu数量，再次往A表写入小批次数据，查询两表cu数量
TEST_F(DDefragHarden, Timing_081_DDefragHarden_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    uint32_t cmdLen = 0;
    bool isCompress = true;  // 压缩模式 no,默认
    // 构造多时区数据
    int32_t nowTimeValue = GetCurrentTime();
    const int32_t interval = 1;  // 时间分区数量
    int newStartTime[interval] = {0};
    // 数组长度和大小不要计算错误
    for (int32_t i = 0; i < sizeof(newStartTime) / sizeof(newStartTime[0]); i++) {
        newStartTime[i] = nowTimeValue + 3600 * (1 + i);
    }
    // 建表
    while (true) {
        // 创建需要使用到的表
        int ret = CreateTsTable(stmt, tableNameA, tableNameB, isCompress);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 准备数据
        char **ns = NULL;
        int32_t dataCount = 1000;
        ret = AllocMemForTextData(&ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GenerateRandomString(ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 小批量数据
        int32_t batchCount = 2;  // 每批写入29条数据
        int32_t totalCount = 1;  // 当前已写入的数据量
        int startCount = 0;

        // 小批量写入29批数据
        int startTime = 0;
        for (int32_t i = 0; i < interval; i++) {
            batchCount = 10;  // 每批写入20条数据
            totalCount = 10;  // 当前已写入的数据量
            startCount = 0;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 29; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                startCount += batchCount;
                totalCount += batchCount;
            }
        }

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(29, GetCuNumValue(29));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 29"));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(29 * interval * batchCount, CheckDataCount(stmt, tableNameA));

        // 写B表
        for (int32_t i = 0; i < interval; i++) {
            batchCount = 10;  // 每批写入10条数据
            totalCount = 10;  // 当前已写入的数据量
            startCount = 0;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 30; i++) {
                ret = insertDataToTableB(stmt, tableNameB, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                startCount += batchCount;
                totalCount += batchCount;
            }
        }

        ret = ReadTableB(stmt, tableNameB, totalCount - batchCount, startTime, 0, ns);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 每个分区写入一批数据触发磁盘整理
        for (int32_t i = 0; i < interval; i++) {
            batchCount = 10;       // 写入1批数据
            totalCount = 10 * 30;  // 当前已写入的数据量
            startCount = 10 * 29;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 1; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                if (ret) {
                    break;
                }
                startCount += batchCount;
                totalCount += batchCount;
            }
        }

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(1, GetCuNumValue(1));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 1"));

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(1, GetCuNumValue(1, tableNameB));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 1", tableNameB));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(30 * interval * batchCount, CheckDataCount(stmt, tableNameA));
        AW_MACRO_ASSERT_EQ_INT(30 * interval * batchCount, CheckDataCount(stmt, tableNameB));

        // 删表
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameA, tableNameB));
        FreeMemForTextData(ns, dataCount);  // 清理前序用例申请的内存

        // 查询环境是否清理干净
        // DiskUsageInfoT PhyDiskUsageInfoT TsTblOperStatisInfoT TsTblPropsInfoT
        DiskUsageInfoT diskUsageInfo{tableNameA};
        PhyDiskUsageInfoT phyDiskUsageInfo{tableNameA};
        TsTblOperStatisInfoT tsTblOperStatisInfo{tableNameA};
        TsTblPropsInfoT tsTblPropsInfo{tableNameA};
        ret = CheckViewValue(&diskUsageInfo, &phyDiskUsageInfo, &tsTblOperStatisInfo, &tsTblPropsInfo);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (isCompress == false) {
            break;
        }
        isCompress = false;
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 031.创建两张表A，B，写A表29次小批量数据后，对A表Alter新字段后，查询cu数量，对B表写入数据，读数据，往B表写入小批次数据，触发磁盘整理，查询cu数量，再次往A表写入小批次数据，直到触发磁盘整理
// 查询两表cu数量
TEST_F(DDefragHarden, Timing_081_DDefragHarden_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    uint32_t cmdLen = 0;
    bool isCompress = true;  // 压缩模式 no,默认
    // 构造多时区数据
    int32_t nowTimeValue = GetCurrentTime();
    const int32_t interval = 1;  // 时间分区数量
    int newStartTime[interval] = {0};
    // 数组长度和大小不要计算错误
    for (int32_t i = 0; i < sizeof(newStartTime) / sizeof(newStartTime[0]); i++) {
        newStartTime[i] = nowTimeValue + 3600 * (1 + i);
    }
    // 建表
    while (true) {
        // 创建需要使用到的表
        int ret = CreateTsTable(stmt, tableNameA, tableNameB, isCompress);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 准备数据
        char **ns = NULL;
        int32_t dataCount = 1000;
        ret = AllocMemForTextData(&ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GenerateRandomString(ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 小批量数据
        int32_t batchCount = 2;  // 每批写入29条数据
        int32_t totalCount = 1;  // 当前已写入的数据量
        int startCount = 0;

        // 小批量写入29批数据
        int startTime = 0;
        for (int32_t i = 0; i < interval; i++) {
            batchCount = 10;  // 每批写入20条数据
            totalCount = 10;  // 当前已写入的数据量
            startCount = 0;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 29; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                startCount += batchCount;
                totalCount += batchCount;
            }
        }
        // tableA表新增字段
        (void)memset(sqlCmd, 0, sizeof(sqlCmd));
        (void)(void)sprintf(sqlCmd, "alter table if exists %s ADD COLUMN ADDCOLUMN1 integer;", tableNameA);
        ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(29, GetCuNumValue(29));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 29"));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(29 * interval * batchCount, CheckDataCount(stmt, tableNameA));

        // 写B表
        for (int32_t i = 0; i < interval; i++) {
            batchCount = 10;  // 每批写入10条数据
            totalCount = 10;  // 当前已写入的数据量
            startCount = 0;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 30; i++) {
                ret = insertDataToTableB(stmt, tableNameB, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                startCount += batchCount;
                totalCount += batchCount;
            }
        }

        ret = ReadTableB(stmt, tableNameB, totalCount - batchCount, startTime, 0, ns);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 每个分区写入一批数据触发磁盘整理
        for (int32_t i = 0; i < interval; i++) {
            batchCount = 10;       // 写入1批数据
            totalCount = 10 * 30;  // 当前已写入的数据量
            startCount = 10 * 29;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 30; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount, 1);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                if (ret) {
                    break;
                }
                startCount += batchCount;
                totalCount += batchCount;
            }
        }

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(2, GetCuNumValue(2));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 2"));

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(1, GetCuNumValue(1, tableNameB));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 1", tableNameB));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(59 * interval * batchCount, CheckDataCount(stmt, tableNameA));
        AW_MACRO_ASSERT_EQ_INT(30 * interval * batchCount, CheckDataCount(stmt, tableNameB));

        // 删表
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameA, tableNameB));
        FreeMemForTextData(ns, dataCount);  // 清理前序用例申请的内存

        // 查询环境是否清理干净
        // DiskUsageInfoT PhyDiskUsageInfoT TsTblOperStatisInfoT TsTblPropsInfoT
        DiskUsageInfoT diskUsageInfo{tableNameA};
        PhyDiskUsageInfoT phyDiskUsageInfo{tableNameA};
        TsTblOperStatisInfoT tsTblOperStatisInfo{tableNameA};
        TsTblPropsInfoT tsTblPropsInfo{tableNameA};
        ret = CheckViewValue(&diskUsageInfo, &phyDiskUsageInfo, &tsTblOperStatisInfo, &tsTblPropsInfo);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (isCompress == false) {
            break;
        }
        isCompress = false;
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 032.创建两张表A，B，写A表29次小批量数据后，对A表Alter修改磁盘容量往大修改后，查询cu数量，对B表写入数据，读数据，往B表写入小批次数据，触发磁盘整理查询cu数量，再次往A表写入小批次数据，直到触发磁盘整理
// 查询两表cu数量
TEST_F(DDefragHarden, Timing_081_DDefragHarden_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    uint32_t cmdLen = 0;
    bool isCompress = true;  // 压缩模式 no,默认
    // 构造多时区数据
    int32_t nowTimeValue = GetCurrentTime();
    const int32_t interval = 1;  // 时间分区数量
    int newStartTime[interval] = {0};
    // 数组长度和大小不要计算错误
    for (int32_t i = 0; i < sizeof(newStartTime) / sizeof(newStartTime[0]); i++) {
        newStartTime[i] = nowTimeValue + 3600 * (1 + i);
    }
    // 建表
    while (true) {
        // 创建需要使用到的表
        int ret = CreateTsTable(stmt, tableNameA, tableNameB, isCompress);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 准备数据
        char **ns = NULL;
        int32_t dataCount = 1000;
        ret = AllocMemForTextData(&ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GenerateRandomString(ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        (void)memset(sqlCmd, 0, sizeof(sqlCmd));
        (void)(void)sprintf(sqlCmd, "alter table %s SET (disk_limit = '20 MB');", tableNameA);
        ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 小批量数据
        int32_t batchCount = 2;  // 每批写入29条数据
        int32_t totalCount = 1;  // 当前已写入的数据量
        int startCount = 0;

        // 小批量写入29批数据
        int startTime = 0;
        for (int32_t i = 0; i < interval; i++) {
            batchCount = 10;  // 每批写入20条数据
            totalCount = 10;  // 当前已写入的数据量
            startCount = 0;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 29; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                startCount += batchCount;
                totalCount += batchCount;
            }
        }
        (void)memset(sqlCmd, 0, sizeof(sqlCmd));
        (void)(void)sprintf(sqlCmd, "alter table %s SET (disk_limit = '40 MB');", tableNameA);
        ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(29, GetCuNumValue(29));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 29"));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(29 * interval * batchCount, CheckDataCount(stmt, tableNameA));

        // 写B表
        for (int32_t i = 0; i < interval; i++) {
            batchCount = 10;  // 每批写入10条数据
            totalCount = 10;  // 当前已写入的数据量
            startCount = 0;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 30; i++) {
                ret = insertDataToTableB(stmt, tableNameB, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                startCount += batchCount;
                totalCount += batchCount;
            }
        }

        ret = ReadTableB(stmt, tableNameB, totalCount - batchCount, startTime, 0, ns);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 每个分区写入一批数据触发磁盘整理
        for (int32_t i = 0; i < interval; i++) {
            batchCount = 10;       // 写入1批数据
            totalCount = 10 * 30;  // 当前已写入的数据量
            startCount = 10 * 29;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 31; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                if (ret) {
                    break;
                }
                startCount += batchCount;
                totalCount += batchCount;
            }
        }

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(2, GetCuNumValue(2));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 2"));

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(1, GetCuNumValue(1, tableNameB));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 1", tableNameB));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(60 * interval * batchCount, CheckDataCount(stmt, tableNameA));
        AW_MACRO_ASSERT_EQ_INT(30 * interval * batchCount, CheckDataCount(stmt, tableNameB));

        // 删表
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameA, tableNameB));
        FreeMemForTextData(ns, dataCount);  // 清理前序用例申请的内存

        // 查询环境是否清理干净
        // DiskUsageInfoT PhyDiskUsageInfoT TsTblOperStatisInfoT TsTblPropsInfoT
        DiskUsageInfoT diskUsageInfo{tableNameA};
        PhyDiskUsageInfoT phyDiskUsageInfo{tableNameA};
        TsTblOperStatisInfoT tsTblOperStatisInfo{tableNameA};
        TsTblPropsInfoT tsTblPropsInfo{tableNameA};
        ret = CheckViewValue(&diskUsageInfo, &phyDiskUsageInfo, &tsTblOperStatisInfo, &tsTblPropsInfo);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (isCompress == false) {
            break;
        }
        isCompress = false;
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 033.创建两张表A，B，写A表29次小批量数据后，对A表Alter修改ttl后，查询cu数量，对B表写入数据，读数据，往B表写入小批次数据，触发磁盘整理，查询cu数量，再次往A表写入小批次数据，直到触发磁盘整理
// 查询两表cu数量
TEST_F(DDefragHarden, Timing_081_DDefragHarden_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    uint32_t cmdLen = 0;
    bool isCompress = true;  // 压缩模式 no,默认
    // 构造多时区数据
    int32_t nowTimeValue = GetCurrentTime();
    const int32_t interval = 1;  // 时间分区数量
    int newStartTime[interval] = {0};
    // 数组长度和大小不要计算错误
    for (int32_t i = 0; i < sizeof(newStartTime) / sizeof(newStartTime[0]); i++) {
        newStartTime[i] = nowTimeValue + 3600 * 8;
    }
    // 建表
    while (true) {
        // 创建需要使用到的表
        int ret = CreateTsTable(stmt, tableNameA, tableNameB, isCompress);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 准备数据
        char **ns = NULL;
        int32_t dataCount = 1000;
        ret = AllocMemForTextData(&ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GenerateRandomString(ns, dataCount);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 小批量数据
        int32_t batchCount = 2;  // 每批写入29条数据
        int32_t totalCount = 1;  // 当前已写入的数据量
        int startCount = 0;

        // 小批量写入29批数据
        int startTime = 0;
        for (int32_t i = 0; i < interval; i++) {
            batchCount = 10;  // 每批写入20条数据
            totalCount = 10;  // 当前已写入的数据量
            startCount = 0;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 29; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                startCount += batchCount;
                totalCount += batchCount;
            }
        }
        // alter 修改ttl
        (void)memset(sqlCmd, 0, sizeof(sqlCmd));
        (void)(void)sprintf(sqlCmd, "alter table %s SET (ttl = '4 hours');", tableNameA);
        ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(29, GetCuNumValue(29));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 29"));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(29 * interval * batchCount, CheckDataCount(stmt, tableNameA));

        // 写B表
        for (int32_t i = 0; i < interval; i++) {
            batchCount = 10;  // 每批写入10条数据
            totalCount = 10;  // 当前已写入的数据量
            startCount = 0;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 30; i++) {
                ret = insertDataToTableB(stmt, tableNameB, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                startCount += batchCount;
                totalCount += batchCount;
            }
        }

        ret = ReadTableB(stmt, tableNameB, totalCount - batchCount, startTime, 0, ns);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 每个分区写入一批数据触发磁盘整理
        for (int32_t i = 0; i < interval; i++) {
            batchCount = 10;       // 写入1批数据
            totalCount = 10 * 30;  // 当前已写入的数据量
            startCount = 10 * 29;
            startTime = newStartTime[i];
            for (int32_t i = 0; i < 31; i++) {
                ret = insertDataToTableA(stmt, tableNameA, totalCount, startTime, startCount, ns + startCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                if (ret) {
                    break;
                }
                startCount += batchCount;
                totalCount += batchCount;
            }
        }

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(2, GetCuNumValue(2));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 2"));

        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(1, GetCuNumValue(1, tableNameB));
        AW_MACRO_ASSERT_EQ_INT(interval, GetAllCuNumValue(interval, (char *)"CU_CNT: 1", tableNameB));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(60 * interval * batchCount, CheckDataCount(stmt, tableNameA));
        AW_MACRO_ASSERT_EQ_INT(30 * interval * batchCount, CheckDataCount(stmt, tableNameB));

        // 删表
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameA, tableNameB));
        FreeMemForTextData(ns, dataCount);  // 清理前序用例申请的内存

        // 查询环境是否清理干净
        // DiskUsageInfoT PhyDiskUsageInfoT TsTblOperStatisInfoT TsTblPropsInfoT
        DiskUsageInfoT diskUsageInfo{tableNameA};
        PhyDiskUsageInfoT phyDiskUsageInfo{tableNameA};
        TsTblOperStatisInfoT tsTblOperStatisInfo{tableNameA};
        TsTblPropsInfoT tsTblPropsInfo{tableNameA};
        ret = CheckViewValue(&diskUsageInfo, &phyDiskUsageInfo, &tsTblOperStatisInfo, &tsTblPropsInfo);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (isCompress == false) {
            break;
        }
        isCompress = false;
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 034.批写单条数据31次并查询STORAGE_DISK_USAGE视图，预期第29次，30次，31次查询视图时COL_DISK_USAGE大小相等
TEST_F(DDefragHarden, Timing_081_DDefragHarden_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t cmdLen = 0;
    bool isCompress = true;  // 压缩模式 no,默认
    // 构造多时区数据
    int32_t nowTimeValue = GetCurrentTime();

    int newStartTime = nowTimeValue + 3600;
    char sqlCmd[1024] = {0};
    (void)DropTable(stmt, tableNameA);
    // 建表
    while (true) {
        // 准备数据
        int32_t dataCount = 1000;
        if (isCompress) {
            (void)(void)sprintf(sqlCmd,
                "create table %s(id integer, time integer) with "
                "(time_col = 'time', interval = '1 hour', compression = 'no', ttl = '1 hours');",
                tableNameA);
        } else {
            (void)(void)sprintf(sqlCmd,
                "create table %s(id integer, time integer) with "
                "(time_col = 'time', interval = '1 hour', ttl = '1 hours');",
                tableNameA);
        }

        cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        DiskUsageInfoT diskUsageInfo = {tableNameA};

        // 小批量写入31条数据数据
        int startTime = 0;
        startTime = newStartTime;
        int64_t id[dataCount];
        (void)memset_s(id, dataCount * sizeof(int64_t), 0, dataCount * sizeof(int64_t));
        for (int32_t i = 0; i < dataCount; i++) {
            id[i] = i + 1;
        }
        int64_t time[dataCount];
        (void)memset_s(time, dataCount * sizeof(int64_t), 0, dataCount * sizeof(int64_t));
        for (int32_t i = 0; i < dataCount; i++) {
            time[i] = newStartTime + i;
        }
        int32_t value1 = 0;
        int32_t value2 = 1;
        int32_t value3 = 2;
        for (int32_t i = 0; i < 31; i++) {
            id[0] = i;
            time[0] = newStartTime + i;
            ret = GmcPrepareStmtByLabelName(stmt, tableNameA, GMC_OPERATION_SQL_INSERT);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            int32_t realCount = 1;
            ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &realCount, sizeof(int64_t));
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, 0, 0);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, time, 0, 0);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            if (i == 28 || i == 29 || i == 30) {
                ret = GetDiskUsageValue(&diskUsageInfo);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                if (i == 28) {
                    value1 = diskUsageInfo.colDiskUsage;
                }
                if (i == 29) {
                    value2 = diskUsageInfo.colDiskUsage;
                }
                if (i == 30) {
                    value3 = diskUsageInfo.colDiskUsage;
                }
            }
        }
        AW_MACRO_ASSERT_EQ_INT(value3, value2);
        AW_MACRO_ASSERT_EQ_INT(value1, value2);
        // 查cu块统计视图 总写入次数/30
        AW_MACRO_ASSERT_EQ_INT(2, GetCuNumValue(2));
        AW_MACRO_ASSERT_EQ_INT(1, GetAllCuNumValue(1, (char *)"CU_CNT: 2"));

        // 校验数据条数
        AW_MACRO_ASSERT_EQ_INT(31, CheckDataCount(stmt, tableNameA));

        // 删表
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameA, NULL));

        // 查询环境是否清理干净
        // DiskUsageInfoT PhyDiskUsageInfoT TsTblOperStatisInfoT TsTblPropsInfoT

        PhyDiskUsageInfoT phyDiskUsageInfo = {tableNameA};
        TsTblOperStatisInfoT tsTblOperStatisInfo = {tableNameA};
        TsTblPropsInfoT tsTblPropsInfo = {tableNameA};
        ret = CheckViewValue(&diskUsageInfo, &phyDiskUsageInfo, &tsTblOperStatisInfo, &tsTblPropsInfo);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (isCompress == false) {
            break;
        }
        isCompress = false;
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
