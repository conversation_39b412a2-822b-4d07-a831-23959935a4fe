/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: 【交付增强】TSDB磁盘满支持覆盖写或丢弃数据
 * Author: jiangjjincheng
 * Create: 2025-02-07
 */
#include <time.h>
#include <errno.h>
#include "gtest/gtest.h"
#include "t_rd_ts.h"
#include "tsdb_defragmentation.h"

static GmcConnOptionsT *connOptions = NULL;
static GmcConnT *conn = NULL;
static GmcStmtT *stmt = NULL;
Status ret = 0;
char g_cStoreDir[64] = {0};
char *dir = getenv("GMDB_HOME");
char tableName[20] = "testdb";
bool eof = false;
bool isNull = false;

class tsdbDefragmentation : public testing::Test {
protected:
    static void SetUpTestCase()
    {
// euler和环境环境清共享内存
#if defined RUN_INDEPENDENT
        system("sh $TEST_HOME/tools/stop.sh -f");
#endif
        system("sh $TEST_HOME/tools/stop.sh -ts");
        TsDefulatDbFileClean();
        system("sh $TEST_HOME/tools/modifyCfg.sh -ts recover");
        system("sh $TEST_HOME/tools/modifyCfg.sh -ts cuCompactEnable=0");
        system("sh $TEST_HOME/tools/start.sh -ts");
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        // 创建epoll监听线程
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        ret = TestTsGmcConnect(&conn, &stmt, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        testGmcDisconnect(conn, stmt);
        close_epoll_thread();
        testEnvClean();
        RecoverTsCiCfg();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void tsdbDefragmentation::SetUp()
{
    AW_CHECK_LOG_BEGIN();
}

void tsdbDefragmentation::TearDown()
{
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_CHECK_LOG_END();
}

class tsdbDefragmentationValue1 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
// euler和环境环境清共享内存
#if defined RUN_INDEPENDENT
        system("sh $TEST_HOME/tools/stop.sh -f");
#endif
        system("sh $TEST_HOME/tools/stop.sh -ts");
        TsDefulatDbFileClean();
        system("sh $TEST_HOME/tools/modifyCfg.sh -ts recover");
        system("sh $TEST_HOME/tools/modifyCfg.sh -ts cuCompactEnable=1");
        system("sh $TEST_HOME/tools/start.sh -ts");
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        // 创建epoll监听线程
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        ret = TestTsGmcConnect(&conn, &stmt, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        testGmcDisconnect(conn, stmt);
        close_epoll_thread();
        testEnvClean();
        RecoverTsCiCfg();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void tsdbDefragmentationValue1::SetUp()
{
    AW_CHECK_LOG_BEGIN();
}

void tsdbDefragmentationValue1::TearDown()
{
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_CHECK_LOG_END();
}

int QueryResult(GmcStmtT *stmt, const char *tableName, int startTime)
{
    int ret = 0;
    // 验证查询结果
    int64_t cPersonId = 0;
    int64_t cPersonTime = 0;
    char cPersonName[64] = {0};
    char cPersonIp[33] = {0};
    char cPersonMessage[160] = {0};
    char cPersonNs[33] = {0};
    uint32_t size = sizeof(int64_t);
    int fetchTimes = 0;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof) {
            break;
        }
        size = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &cPersonId, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(fetchTimes, cPersonId);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &cPersonTime, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(fetchTimes + startTime, cPersonTime);
        size = sizeof(cPersonName);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &cPersonName, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_STR(g_nameSource[fetchTimes % 10], cPersonName);
        size = sizeof(cPersonIp);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &cPersonIp, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_STR(g_ipSource[fetchTimes % 10], cPersonIp);
        size = sizeof(cPersonMessage);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &cPersonMessage, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_STR(g_messageSource[fetchTimes % 10], cPersonMessage);
        size = sizeof(cPersonNs);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 5, &cPersonNs, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_STR(g_nsSource[fetchTimes % 10], cPersonNs);
        fetchTimes++;
    }
    return ret;
}

// 001.将配置项cuCompactEnable设为0，启动服务
// 预期：重启服务成功，建连成功
TEST_F(tsdbDefragmentation, Timing_81_tsdbDefragmentation_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/stop.sh -ts");
    // 更改配置项
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts cuCompactEnable=0");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(1);
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts recover");
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_CONNECTION_RESET_BY_PEER);
}

// 002.将配置项cuCompactEnable设为1，启动服务
// 预期：重启服务成功，建连成功
TEST_F(tsdbDefragmentation, Timing_81_tsdbDefragmentation_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/stop.sh -ts");
    // 更改配置项
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts cuCompactEnable=1");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(1);
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts recover");
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_CONNECTION_RESET_BY_PEER);
}

// 003.打开磁盘整理配置项，连续写入1条递增数据，总共30条
// 预期：全部数据在一个cu块中
TEST_F(tsdbDefragmentationValue1, Timing_81_tsdbDefragmentation_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char fdCmd[256] = {0};
    (void)sprintf(fdCmd, "ls -l /proc/`pidof gmserver_ts`/fd |wc -l");
    int32_t fdBefore = 0;
    int32_t fdAfter = 0;
    fdBefore = GetResultValue(fdCmd);
    char sqlCmd[256] = {0};
    char viewStatement[256] = {0};
    uint32_t cmdLen = 0;
    // 建表
    int ret = createTsTable(stmt, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 1, fdAfter);
    // 写数据
    int insertTimes = 30;
    int count = 1;
    // 起始时间2030-1-1 0:0:0
    int startTime = 1893427200;
    int startCount = 0;
    for (int i = 0; i < insertTimes; i++) {
        startCount = startCount + count * (i > 0 ? 1 : 0);
        ret = insertDataToTable(stmt, tableName, count, startTime + startCount, startCount);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    fdAfter = GetResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 1, fdAfter);
    int *cuCount = {0};
    // 查视图
    (void)sprintf(viewStatement, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE'\" -s %s", g_connServerTsdb);
    cuCount = GetViewFieldResultValue(viewStatement, "CU_CNT");
    // 物理分区中有1个cu块
    AW_MACRO_EXPECT_EQ_INT(1, cuCount[0]);
    fdAfter = GetResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 1, fdAfter);
    int dataCount = 0;
    (void)sprintf(sqlCmd, "SELECT * FROM %s order by time;", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(insertTimes * count, dataCount);

    // 验证查询结果
    int64_t cPersonId = 0;
    int64_t cPersonTime = 0;
    char cPersonName[64] = {0};
    char cPersonIp[33] = {0};
    char cPersonMessage[160] = {0};
    char cPersonNs[33] = {0};
    uint32_t size = sizeof(int64_t);
    int fetchTimes = 0;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof) {
            break;
        }
        size = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &cPersonId, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(fetchTimes, cPersonId);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &cPersonTime, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(fetchTimes + startTime, cPersonTime);
        size = sizeof(cPersonName);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &cPersonName, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_STR(g_nameSource[fetchTimes % 10], cPersonName);
        size = sizeof(cPersonIp);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &cPersonIp, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_STR(g_ipSource[fetchTimes % 10], cPersonIp);
        size = sizeof(cPersonMessage);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &cPersonMessage, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_STR(g_messageSource[fetchTimes % 10], cPersonMessage);
        size = sizeof(cPersonNs);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 5, &cPersonNs, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_STR(g_nsSource[fetchTimes % 10], cPersonNs);
        fetchTimes++;
    }

    // 删表
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableName));
    // 多次查看fd，判断是否有fd泄漏
    ret = GetAfterFdValue(fdBefore, fdAfter, fdCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 004.打开磁盘整理配置项，连续写入1条递增数据，总共61条
// 预期：3个cu块
TEST_F(tsdbDefragmentationValue1, Timing_81_tsdbDefragmentation_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char fdCmd[256] = {0};
    (void)sprintf(fdCmd, "ls -l /proc/`pidof gmserver_ts`/fd |wc -l");
    int32_t fdBefore = 0;
    int32_t fdAfter = 0;
    fdBefore = GetResultValue(fdCmd);
    char sqlCmd[256] = {0};
    char viewStatement[256] = {0};
    uint32_t cmdLen = 0;
    // 建表
    int ret = createTsTable(stmt, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 1, fdAfter);
    // 写数据
    int insertTimes = 61;
    int count = 1;
    // 起始时间2030-1-1 0:0:0
    int startTime = 1893427200;
    int startCount = 0;
    for (int i = 0; i < insertTimes; i++) {
        startCount = startCount + count * (i > 0 ? 1 : 0);
        ret = insertDataToTable(stmt, tableName, count, startTime + startCount, startCount);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    fdAfter = GetResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 1, fdAfter);
    int *cuCount = {0};
    // 查视图
    (void)sprintf(viewStatement, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE'\" -s %s", g_connServerTsdb);
    cuCount = GetViewFieldResultValue(viewStatement, "CU_CNT");
    // 物理分区中有3个cu块
    AW_MACRO_EXPECT_EQ_INT(3, cuCount[0]);
    fdAfter = GetResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 1, fdAfter);
    int dataCount = 0;
    (void)sprintf(sqlCmd, "SELECT * FROM %s order by time;", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(insertTimes * count, dataCount);
    // 验证查询结果
    ret = QueryResult(stmt, tableName, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删表
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableName));
    // 多次查看fd，判断是否有fd泄漏
    ret = GetAfterFdValue(fdBefore, fdAfter, fdCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 005.关闭磁盘整理配置项，连续写入1条递增数据，总共30条
// 预期：30个cu块
TEST_F(tsdbDefragmentation, Timing_81_tsdbDefragmentation_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    char viewStatement[256] = {0};
    uint32_t cmdLen = 0;
    // 建表
    int ret = createTsTable(stmt, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int insertTimes = 30;
    int count = 1;
    // 起始时间2030-1-1 0:0:0
    int startTime = 1893427200;
    int startCount = 0;
    for (int i = 0; i < insertTimes; i++) {
        startCount = startCount + count * (i > 0 ? 1 : 0);
        ret = insertDataToTable(stmt, tableName, count, startTime + startCount, startCount);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    int *cuCount = {0};
    // 查视图
    (void)sprintf(viewStatement, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE'\" -s %s", g_connServerTsdb);
    cuCount = GetViewFieldResultValue(viewStatement, "CU_CNT");
    // 物理分区中有30个cu块
    AW_MACRO_EXPECT_EQ_INT(30, cuCount[0]);

    int dataCount = 0;
    (void)sprintf(sqlCmd, "SELECT * FROM %s order by time;", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(insertTimes * count, dataCount);
    // 验证查询结果
    ret = QueryResult(stmt, tableName, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删表
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableName));
}

// 006.关闭磁盘整理配置项，连续写入1条递增数据，总共61条
// 预期：61个cu块
TEST_F(tsdbDefragmentation, Timing_81_tsdbDefragmentation_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    char viewStatement[256] = {0};
    uint32_t cmdLen = 0;
    // 建表
    int ret = createTsTable(stmt, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int insertTimes = 61;
    int count = 1;
    // 起始时间2030-1-1 0:0:0
    int startTime = 1893427200;
    int startCount = 0;
    for (int i = 0; i < insertTimes; i++) {
        startCount = startCount + count * (i > 0 ? 1 : 0);
        ret = insertDataToTable(stmt, tableName, count, startTime + startCount, startCount);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    int *cuCount = {0};
    // 查视图
    (void)sprintf(viewStatement, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE'\" -s %s", g_connServerTsdb);
    cuCount = GetViewFieldResultValue(viewStatement, "CU_CNT");
    // 物理分区中有61个cu块
    AW_MACRO_EXPECT_EQ_INT(61, cuCount[0]);

    int dataCount = 0;
    (void)sprintf(sqlCmd, "SELECT * FROM %s order by time;", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(insertTimes * count, dataCount);
    // 验证查询结果
    ret = QueryResult(stmt, tableName, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删表
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableName));
}

// 007.打开磁盘整理配置项，连续写入31条递增数据，总共310条
// 预期：10个cu块
TEST_F(tsdbDefragmentationValue1, Timing_81_tsdbDefragmentation_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    char viewStatement[256] = {0};
    uint32_t cmdLen = 0;
    // 建表
    int ret = createTsTable(stmt, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int insertTimes = 10;
    int count = 31;
    // 起始时间2030-1-1 0:0:0
    int startTime = 1893427200;
    int startCount = 0;
    for (int i = 0; i < insertTimes; i++) {
        startCount = startCount + count * (i > 0 ? 1 : 0);
        ret = insertDataToTable(stmt, tableName, count, startTime + startCount, startCount);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    int *cuCount = {0};
    // 查视图
    (void)sprintf(viewStatement, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE'\" -s %s", g_connServerTsdb);
    cuCount = GetViewFieldResultValue(viewStatement, "CU_CNT");
    // 物理分区中有10个cu块
    AW_MACRO_EXPECT_EQ_INT(10, cuCount[0]);

    int dataCount = 0;
    (void)sprintf(sqlCmd, "SELECT * FROM %s order by time;", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(insertTimes * count, dataCount);

    // 验证查询结果
    ret = QueryResult(stmt, tableName, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableName));
}

// 008.打开磁盘整理配置项，连续写入30条递增数据，总共300条
// 预期：10个cu块
TEST_F(tsdbDefragmentationValue1, Timing_81_tsdbDefragmentation_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    char viewStatement[256] = {0};
    uint32_t cmdLen = 0;
    // 建表
    int ret = createTsTable(stmt, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int insertTimes = 10;
    int count = 30;
    // 起始时间2030-1-1 0:0:0
    int startTime = 1893427200;
    int startCount = 0;
    for (int i = 0; i < insertTimes; i++) {
        startCount = startCount + count * (i > 0 ? 1 : 0);
        ret = insertDataToTable(stmt, tableName, count, startTime + startCount, startCount);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    int *cuCount = {0};
    // 查视图
    (void)sprintf(viewStatement, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE'\" -s %s", g_connServerTsdb);
    cuCount = GetViewFieldResultValue(viewStatement, "CU_CNT");
    // 物理分区中有10个cu块
    AW_MACRO_EXPECT_EQ_INT(10, cuCount[0]);

    int dataCount = 0;
    (void)sprintf(sqlCmd, "SELECT * FROM %s order by time;", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(insertTimes * count, dataCount);
    // 验证查询结果
    ret = QueryResult(stmt, tableName, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删表
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableName));
}

// 009.打开磁盘整理配置项，交叉写入29条，30条数据重复5次
// 预期：5个cu块
TEST_F(tsdbDefragmentationValue1, Timing_81_tsdbDefragmentation_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    char viewStatement[256] = {0};
    uint32_t cmdLen = 0;
    // 建表
    int ret = createTsTable(stmt, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int insertTimes = 10;
    int count2 = 30;
    int count = 29;
    // 起始时间2030-1-1 0:0:0
    int startTime = 1893427200;
    int startCount = 0;
    // 交叉写入29条，30条数据重复5次
    for (int i = 0; i < insertTimes; i++) {
        if (i % 2 == 0) {
            startCount = startCount + count2 * (i > 0 ? 1 : 0);
            ret = insertDataToTable(stmt, tableName, count, startTime + startCount, startCount);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            startCount = startCount + count;
            ret = insertDataToTable(stmt, tableName, count2, startTime + startCount, startCount);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }

    int *cuCount = {0};
    // 查视图
    (void)sprintf(viewStatement, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE'\" -s %s", g_connServerTsdb);
    cuCount = GetViewFieldResultValue(viewStatement, "CU_CNT");
    // 物理分区中有5个cu块
    AW_MACRO_EXPECT_EQ_INT(5, cuCount[0]);

    int dataCount = 0;
    (void)sprintf(sqlCmd, "SELECT * FROM %s order by time;", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((count + count2) * insertTimes / 2, dataCount);

    // 验证查询结果
    ret = QueryResult(stmt, tableName, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删表
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableName));
}

// 010.打开磁盘整理配置项，连续写入1条数据，总共15次，再写入一次30条数据
// 预期：1个cu块
TEST_F(tsdbDefragmentationValue1, Timing_81_tsdbDefragmentation_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    char viewStatement[256] = {0};
    uint32_t cmdLen = 0;
    // 建表
    int ret = createTsTable(stmt, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int insertTimes = 15;
    int count = 1;
    // 起始时间2030-1-1 0:0:0
    int startTime = 1893427200;
    int startCount = 0;
    // 连续写入1条数据，总共15次
    for (int i = 0; i < insertTimes; i++) {
        startCount = startCount + count * (i > 0 ? 1 : 0);
        ret = insertDataToTable(stmt, tableName, count, startTime + startCount, startCount);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 再写入一次30条数据
    startCount = startCount + count;
    count = 30;
    ret = insertDataToTable(stmt, tableName, count, startTime + startCount, startCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int *cuCount = {0};
    // 查视图
    (void)sprintf(viewStatement, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE'\" -s %s", g_connServerTsdb);
    cuCount = GetViewFieldResultValue(viewStatement, "CU_CNT");
    // 物理分区中有1个cu块
    AW_MACRO_EXPECT_EQ_INT(1, cuCount[0]);

    int dataCount = 0;
    (void)sprintf(sqlCmd, "SELECT * FROM %s order by time;", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(45, dataCount);
    // 验证查询结果
    ret = QueryResult(stmt, tableName, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删表
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableName));
}

// 011.打开磁盘整理配置项，连续写入1条数据，总共15次，再写入一次31条数据
// 预期：2个cu块
TEST_F(tsdbDefragmentationValue1, Timing_81_tsdbDefragmentation_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    char viewStatement[256] = {0};
    uint32_t cmdLen = 0;
    // 建表
    int ret = createTsTable(stmt, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int insertTimes = 15;
    int count = 1;
    // 起始时间2030-1-1 0:0:0
    int startTime = 1893427200;
    int startCount = 0;
    // 连续写入1条数据，总共15次
    for (int i = 0; i < insertTimes; i++) {
        startCount = startCount + count * (i > 0 ? 1 : 0);
        ret = insertDataToTable(stmt, tableName, count, startTime + startCount, startCount);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 再写入一次31条数据
    startCount = startCount + count;
    count = 31;
    ret = insertDataToTable(stmt, tableName, count, startTime + startCount, startCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int *cuCount = {0};
    // 查视图
    (void)sprintf(viewStatement, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE'\" -s %s", g_connServerTsdb);
    cuCount = GetViewFieldResultValue(viewStatement, "CU_CNT");
    // 物理分区中有2个cu块
    AW_MACRO_EXPECT_EQ_INT(2, cuCount[0]);

    int dataCount = 0;
    (void)sprintf(sqlCmd, "SELECT * FROM %s order by time;", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(46, dataCount);
    // 验证查询结果
    ret = QueryResult(stmt, tableName, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删表
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableName));
}

// 012.打开磁盘整理配置项，连续写入1条递增数据，共62条，单次和双次分别落在不同分区
// 预期：两个物理表中分别有2个cu块
TEST_F(tsdbDefragmentationValue1, Timing_81_tsdbDefragmentation_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    char viewStatement[256] = {0};
    uint32_t cmdLen = 0;
    // 建表
    int ret = createTsTable(stmt, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int insertTimes = 62;
    int count = 1;
    // 起始时间2030-1-1 0:0:0
    int startTime = 1893427200;
    int startCount = 0;
    // 连续写入1条递增数据，共62条，单次和双次分别落在不同分区
    for (int i = 0; i < insertTimes; i++) {
        if (i % 2 == 0) {
            startCount = startCount + count * (i > 0 ? 1 : 0);
            ret = insertDataToTable(stmt, tableName, count, startTime + startCount, startCount);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = insertDataToTable(stmt, tableName, count, startTime + startCount + 7200, startCount);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }

    int *cuCount = {0};
    // 查视图
    (void)sprintf(viewStatement, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE'\" -s %s", g_connServerTsdb);
    cuCount = GetViewFieldResultValue(viewStatement, "CU_CNT");
    // 两个物理分区中有2个cu块
    AW_MACRO_EXPECT_EQ_INT(2, cuCount[0]);
    AW_MACRO_EXPECT_EQ_INT(2, cuCount[1]);

    int dataCount = 0;
    (void)sprintf(sqlCmd, "SELECT * FROM %s order by time;", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(count * insertTimes, dataCount);

    // 验证查询结果
    int64_t cPersonId = 0;
    int64_t cPersonTime = 0;
    char cPersonName[64] = {0};
    uint32_t size = sizeof(int64_t);
    int fetchTimes = 0;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof) {
            break;
        }
        size = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &cPersonId, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &cPersonTime, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        size = sizeof(cPersonName);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &cPersonName, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (fetchTimes < insertTimes / 2) {
            AW_MACRO_EXPECT_EQ_INT(fetchTimes, cPersonId);
            AW_MACRO_EXPECT_EQ_INT(fetchTimes + startTime, cPersonTime);
            AW_MACRO_EXPECT_EQ_STR(g_nameSource[fetchTimes % 10], cPersonName);
        } else {
            AW_MACRO_EXPECT_EQ_INT(fetchTimes - insertTimes / 2, cPersonId);
            AW_MACRO_EXPECT_EQ_INT(fetchTimes - insertTimes / 2 + startTime + 7200, cPersonTime);
            AW_MACRO_EXPECT_EQ_STR(g_nameSource[(fetchTimes - insertTimes / 2) % 10], cPersonName);
        }
        fetchTimes++;
    }
    // 删表
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableName));
}

// 013.打开磁盘整理配置项，单次写62条数据，单数和双数分别落在不同分区
// 预期：两个物理表中分别有2个cu块
TEST_F(tsdbDefragmentationValue1, Timing_81_tsdbDefragmentation_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    char viewStatement[256] = {0};
    uint32_t cmdLen = 0;
    // 建表
    int ret = createTsTable1(stmt, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int insertTimes = 1;
    int count = 62;
    // 起始时间2030-1-1 0:0:0
    int startTime = 1893427200;
    int startCount = 0;
    // 连续写入1条递增数据，共62条，单次和双次分别落在不同分区
    int64_t id[count];
    (void)memset_s(id, count * sizeof(int64_t), 0, count * sizeof(int64_t));
    int64_t time[count];
    (void)memset_s(time, count * sizeof(int64_t), 0, count * sizeof(int64_t));
    char *name = (char *)malloc(count * 64);
    if (name == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }

    for (int i = 0; i < count; i++) {
        if (i % 2 == 0) {
            id[i] = i / 2 + startCount;
            time[i] = startTime + i / 2;
            memcpy((name + i * 64), (char *)g_nameSource[(i / 2 + startCount) % 10], 64);
        } else {
            id[i] = i / 2 + startCount;
            time[i] = startTime + i / 2 + 7200;
            memcpy((name + i * 64), (char *)g_nameSource[(i / 2 + startCount) % 10], 64);
        }
    }
    ret = BlukInsert_char(stmt, tableName, count, 3, id, time, name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(name);

    int *cuCount = {0};
    // 查视图
    (void)sprintf(viewStatement, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE'\" -s %s", g_connServerTsdb);
    cuCount = GetViewFieldResultValue(viewStatement, "CU_CNT");
    // 两个物理分区中有2个cu块
    AW_MACRO_EXPECT_EQ_INT(2, cuCount[0]);
    AW_MACRO_EXPECT_EQ_INT(2, cuCount[1]);

    int dataCount = 0;
    (void)sprintf(sqlCmd, "SELECT * FROM %s order by time;", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(count * insertTimes, dataCount);

    // 验证查询结果
    int64_t cPersonId = 0;
    int64_t cPersonTime = 0;
    char cPersonName[64] = {0};
    uint32_t size = sizeof(int64_t);
    int fetchTimes = 0;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof) {
            break;
        }
        size = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &cPersonId, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &cPersonTime, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        size = sizeof(cPersonName);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &cPersonName, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (fetchTimes < count / 2) {
            AW_MACRO_EXPECT_EQ_INT(fetchTimes, cPersonId);
            AW_MACRO_EXPECT_EQ_INT(fetchTimes + startTime, cPersonTime);
            AW_MACRO_EXPECT_EQ_STR(g_nameSource[fetchTimes % 10], cPersonName);
        } else {
            AW_MACRO_EXPECT_EQ_INT(fetchTimes - count / 2, cPersonId);
            AW_MACRO_EXPECT_EQ_INT(fetchTimes - count / 2 + startTime + 7200, cPersonTime);
            AW_MACRO_EXPECT_EQ_STR(g_nameSource[(fetchTimes - count / 2) % 10], cPersonName);
        }
        fetchTimes++;
    }
    // 删表
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableName));
}

// 014.打开磁盘整理配置项，连续写入1条递增数据，总共15条，再新增一列后，再次连续写入30条数据
// 预期：2个cu块，修改表结构前后cu不合并
TEST_F(tsdbDefragmentationValue1, Timing_81_tsdbDefragmentation_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    char viewStatement[256] = {0};
    uint32_t cmdLen = 0;
    // 建表
    int ret = createTsTable(stmt, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int insertTimes = 15;
    int count = 1;
    // 起始时间2030-1-1 0:0:0
    int startTime = 1893427200;
    int startCount = 0;
    // 连续写入1条数据，总共15次
    for (int i = 0; i < insertTimes; i++) {
        startCount = startCount + count * (i > 0 ? 1 : 0);
        ret = insertDataToTable(stmt, tableName, count, startTime + startCount, startCount);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 新增一列
    (void)sprintf(sqlCmd, "ALTER TABLE %s ADD value INTEGER;", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 再写入一次30条数据
    startCount = startCount + count;
    count = 30;
    ret = insertDataToTable(stmt, tableName, count, startTime + startCount, startCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int *cuCount = {0};
    // 查视图
    (void)sprintf(viewStatement, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE'\" -s %s", g_connServerTsdb);
    cuCount = GetViewFieldResultValue(viewStatement, "CU_CNT");
    // 物理分区中有2个cu块，alter table新增列会触发磁盘整理
    AW_MACRO_EXPECT_EQ_INT(2, cuCount[0]);

    int dataCount = 0;
    (void)sprintf(sqlCmd, "SELECT * FROM %s order by time;", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(45, dataCount);
    // 验证查询结果
    ret = QueryResult(stmt, tableName, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删表
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableName));
}

// 015.打开磁盘整理配置项，建表是开启cache为5，连续写入1条递增数据，总,共186条数据
// 预期：2个cu块
TEST_F(tsdbDefragmentationValue1, Timing_81_tsdbDefragmentation_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    char viewStatement[256] = {0};
    uint32_t cmdLen = 0;
    // 建表，cache_size设为5
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64)) with (time_col = 'time', interval = '1 hour', "
        "cache_size = 5);",
        tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int insertTimes = 186;
    int count = 1;
    // 起始时间2030-1-1 0:0:0
    int startTime = 1893427200;
    int startCount = 0;
    for (int i = 0; i < insertTimes; i++) {
        startCount = startCount + count * (i > 0 ? 1 : 0);
        ret = insertDataToTable(stmt, tableName, count, startTime + startCount, startCount);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    int *cuCount = {0};
    // 查视图
    (void)sprintf(viewStatement, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE'\" -s %s", g_connServerTsdb);
    cuCount = GetViewFieldResultValue(viewStatement, "CU_CNT");
    // 物理分区中有2个cu块
    AW_MACRO_EXPECT_EQ_INT(2, cuCount[0]);

    int dataCount = 0;
    (void)sprintf(sqlCmd, "SELECT * FROM %s order by time;", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(insertTimes * count, dataCount);

    // 验证查询结果
    int64_t cPersonId = 0;
    int64_t cPersonTime = 0;
    char cPersonName[64] = {0};
    uint32_t size = sizeof(int64_t);
    int fetchTimes = 0;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof) {
            break;
        }
        size = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &cPersonId, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(fetchTimes, cPersonId);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &cPersonTime, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(fetchTimes + startTime, cPersonTime);
        size = sizeof(cPersonName);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &cPersonName, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_STR(g_nameSource[fetchTimes % 10], cPersonName);
        fetchTimes++;
    }

    // 删表
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableName));
}

// 016.打开磁盘整理配置项，建表是开启cache为40，连续写入1条递增数据，总,共124条数据
// 预期：3个cu块
TEST_F(tsdbDefragmentationValue1, Timing_81_tsdbDefragmentation_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    char viewStatement[256] = {0};
    uint32_t cmdLen = 0;
    // 建表，cache_size设为40
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64)) with (time_col = 'time', interval = '1 hour', "
        "cache_size = 40);",
        tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int insertTimes = 124;
    int count = 1;
    // 起始时间2030-1-1 0:0:0
    int startTime = 1893427200;
    int startCount = 0;
    for (int i = 0; i < insertTimes; i++) {
        startCount = startCount + count * (i > 0 ? 1 : 0);
        ret = insertDataToTable(stmt, tableName, count, startTime + startCount, startCount);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    int *cuCount = {0};
    // 查视图
    (void)sprintf(viewStatement, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE'\" -s %s", g_connServerTsdb);
    cuCount = GetViewFieldResultValue(viewStatement, "CU_CNT");
    // 物理分区中有3个cu块
    AW_MACRO_EXPECT_EQ_INT(3, cuCount[0]);

    int dataCount = 0;
    (void)sprintf(sqlCmd, "SELECT * FROM %s order by time;", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(insertTimes * count, dataCount);

    // 验证查询结果
    int64_t cPersonId = 0;
    int64_t cPersonTime = 0;
    char cPersonName[64] = {0};
    uint32_t size = sizeof(int64_t);
    int fetchTimes = 0;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof) {
            break;
        }
        size = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &cPersonId, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(fetchTimes, cPersonId);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &cPersonTime, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(fetchTimes + startTime, cPersonTime);
        size = sizeof(cPersonName);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &cPersonName, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_STR(g_nameSource[fetchTimes % 10], cPersonName);
        fetchTimes++;
    }

    // 删表
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableName));
}

// 017.有空列时不触发磁盘整理
// 预期：30个cu块
TEST_F(tsdbDefragmentationValue1, Timing_81_tsdbDefragmentation_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    char viewStatement[256] = {0};
    uint32_t cmdLen = 0;
    // 建表
    int ret = createTsTable1(stmt, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int insertTimes = 30;
    int count = 1;
    // 起始时间2030-1-1 0:0:0
    int startTime = 1893427200;
    int startCount = 0;
    for (int i = 0; i < insertTimes; i++) {
        startCount = startCount + count * (i > 0 ? 1 : 0);
        ret = insertDataToTable1(stmt, tableName, count, startTime + startCount, startCount);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    int *cuCount = {0};
    // 查视图
    (void)sprintf(viewStatement, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE'\" -s %s", g_connServerTsdb);
    cuCount = GetViewFieldResultValue(viewStatement, "CU_CNT");
    // 物理分区中有30个cu块
    AW_MACRO_EXPECT_EQ_INT(30, cuCount[0]);

    int dataCount = 0;
    (void)sprintf(sqlCmd, "SELECT * FROM %s order by time;", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(insertTimes * count, dataCount);

    // 验证查询结果
    int64_t cPersonId = 0;
    int64_t cPersonTime = 0;
    char cPersonName[64] = {0};
    uint32_t size = sizeof(int64_t);
    int fetchTimes = 0;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof) {
            break;
        }
        size = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &cPersonId, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(0, cPersonId);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &cPersonTime, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(fetchTimes + startTime, cPersonTime);
        size = sizeof(cPersonName);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &cPersonName, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_STR(g_nameSource[fetchTimes % 10], cPersonName);
        fetchTimes++;
    }

    // 删表
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableName));
}

// 018.空数据列触发磁盘整理
// 预期：3个cu块
TEST_F(tsdbDefragmentationValue1, Timing_81_tsdbDefragmentation_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    char viewStatement[256] = {0};
    uint32_t cmdLen = 0;
    // 建表
    int ret = createTsTable1(stmt, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int insertTimes = 10;
    int count = 1;
    // 起始时间2030-1-1 0:0:0
    int startTime = 1893427200;
    int startCount = 0;
    // 先连续写入1条，共10条完整数据
    for (int i = 0; i < insertTimes; i++) {
        startCount = startCount + count * (i > 0 ? 1 : 0);
        ret = insertDataToTable2(stmt, tableName, count, startTime + startCount, startCount);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    startCount += count;
    // 再写入1条缺列数据
    ret = insertDataToTable1(stmt, tableName, count, startTime + startCount, startCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    insertTimes = 30;
    // 先连续写入1条，共30条完整数据
    for (int i = 0; i < insertTimes; i++) {
        startCount = startCount + count;
        ret = insertDataToTable2(stmt, tableName, count, startTime + startCount, startCount);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    int *cuCount = {0};
    // 查视图
    (void)sprintf(viewStatement, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE'\" -s %s", g_connServerTsdb);
    cuCount = GetViewFieldResultValue(viewStatement, "CU_CNT");
    // 物理分区中有3个cu块
    AW_MACRO_EXPECT_EQ_INT(3, cuCount[0]);

    int dataCount = 0;
    (void)sprintf(sqlCmd, "SELECT * FROM %s order by time;", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(41, dataCount);
    // 删表
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableName));
}

// 019.改进措施DTS2025043017206，where查询物理表剪枝  预期：查询成功，数据正确
TEST_F(tsdbDefragmentation, Timing_081_tsdbPruning_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    char viewStatement[256] = {0};
    uint32_t cmdLen = 0;
    // 建表
    int ret = createTsTable(stmt, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 构造数据
    int insertTimes = 10;
    int count = 1;
    // 起始时间2030-1-1 0:5:0，防止tolerance影响
    int startTimeDefulat = 1893427500;
    int startTime = 0;
    int startCount = 0;
    // 构造特殊数据，方便构造查询场景
    for (int i = 0; i < insertTimes; i++) {
        startTime = startTimeDefulat + 3600 * i;
        ret = insertDataToTable(stmt, tableName, count, startTime, startCount);
        startCount++;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    (void)sprintf(sqlCmd,
        "select time, name, ip from %s where id=1 and time < 1893445500 or name='nut' and id != 3 or id >= "
        "2 and time <= 1893445500;",
        tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 验证查询结果
    int64_t cPersonTime = 0;
    char cPersonName[64] = {0};
    char cPersonIp[33] = {0};

    uint32_t size = sizeof(int64_t);
    int fetchTimes = 0;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof) {
            break;
        }
        size = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &cPersonTime, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT((fetchTimes + 1) * 3600 + startTimeDefulat, cPersonTime);
        size = sizeof(cPersonName);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &cPersonName, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_STR(g_nameSource[(fetchTimes + 1) % 10], cPersonName);
        size = sizeof(cPersonIp);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &cPersonIp, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_STR(g_ipSource[(fetchTimes + 1) % 10], cPersonIp);
        fetchTimes++;
    }

    // 删表
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableName));
}

// 020.改进措施DTS2025043017206，TopN查询物理表剪枝  预期：查询成功，数据正确
TEST_F(tsdbDefragmentation, Timing_081_tsdbPruning_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    char viewStatement[256] = {0};
    uint32_t cmdLen = 0;
    // 建表
    int ret = createTsTable(stmt, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 构造数据
    int insertTimes = 10;
    int count = 10;
    // 起始时间2030-1-1 0:5:0，防止tolerance影响
    int startTimeDefulat = 1893427500;
    int startTime = 0;
    int startCount = 0;
    // 构造特殊数据，方便构造查询场景
    for (int i = 0; i < insertTimes; i++) {
        startTime = startTimeDefulat + 3600 * i + count * i;
        ret = insertDataToTable(stmt, tableName, count, startTime, startCount);
        startCount += count;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    (void)sprintf(sqlCmd, "select time, name, ip from %s order by time asc limit 20 offset 5;", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 验证查询结果
    int64_t cPersonTime = 0;
    char cPersonName[64] = {0};
    char cPersonIp[33] = {0};
    int64_t expectTime[20] = {1893427505, 1893427506, 1893427507, 1893427508, 1893427509, 1893431110, 1893431111,
        1893431112, 1893431113, 1893431114, 1893431115, 1893431116, 1893431117, 1893431118, 1893431119, 1893434720,
        1893434721, 1893434722, 1893434723, 1893434724};

    uint32_t size = sizeof(int64_t);
    int fetchTimes = 0;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof) {
            break;
        }
        size = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &cPersonTime, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expectTime[fetchTimes], cPersonTime);
        size = sizeof(cPersonName);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &cPersonName, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_STR(g_nameSource[(fetchTimes + 5) % 10], cPersonName);
        size = sizeof(cPersonIp);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &cPersonIp, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_STR(g_ipSource[(fetchTimes + 5) % 10], cPersonIp);
        fetchTimes++;
    }

    // 删表
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableName));
}

// 021.改进措施DTS2025043017206，TopNcu块剪枝，时间列排序  预期：查询成功，数据正确
TEST_F(tsdbDefragmentation, Timing_081_tsdbPruning_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    char viewStatement[256] = {0};
    uint32_t cmdLen = 0;
    // 建表
    int ret = createTsTable(stmt, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 构造数据
    int insertTimes = 50;
    int count = 10;
    // 起始时间2030-1-1 0:5:0，防止tolerance影响
    int startTimeDefulat = 1893427500;
    int startTime = 0;
    int startCount = 0;
    // 构造特殊数据，方便构造查询场景
    for (int i = 0; i < insertTimes; i++) {
        startTime = startTimeDefulat + count * i;
        ret = insertDataToTable(stmt, tableName, count, startTime, startCount);
        startCount += count;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    (void)sprintf(sqlCmd,
        "select time, message, ns from %s where length(name) = 5 order by time asc limit 20 offset 10;", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 验证查询结果
    int64_t cPersonTime = 0;
    char cPersonMessage[160] = {0};
    char cPersonNs[33] = {0};
    int64_t expectTime[20] = {1893427550, 1893427558, 1893427560, 1893427568, 1893427570, 1893427578, 1893427580,
        1893427588, 1893427590, 1893427598, 1893427600, 1893427608, 1893427610, 1893427618, 1893427620, 1893427628,
        1893427630, 1893427638, 1893427640, 1893427648};

    uint32_t size = sizeof(int64_t);
    int fetchTimes = 0;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof) {
            break;
        }
        size = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &cPersonTime, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expectTime[fetchTimes], cPersonTime);
        size = sizeof(cPersonMessage);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &cPersonMessage, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        size = sizeof(cPersonNs);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &cPersonNs, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (fetchTimes % 2 == 0) {
            AW_MACRO_EXPECT_EQ_STR(g_messageSource[0], cPersonMessage);
            AW_MACRO_EXPECT_EQ_STR(g_nsSource[0], cPersonNs);
        } else {
            AW_MACRO_EXPECT_EQ_STR(g_messageSource[8], cPersonMessage);
            AW_MACRO_EXPECT_EQ_STR(g_nsSource[8], cPersonNs);
        }
        fetchTimes++;
    }

    // 删表
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableName));
}

// 022.改进措施DTS2025043017206，TopNcu块剪枝，非时间列排序，子句有其他操作  预期：查询成功，数据正确
TEST_F(tsdbDefragmentation, Timing_081_tsdbPruning_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    char viewStatement[256] = {0};
    uint32_t cmdLen = 0;
    // 建表
    int ret = createTsTable(stmt, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 构造数据
    int insertTimes = 50;
    int count = 10;
    // 起始时间2030-1-1 0:5:0，防止tolerance影响
    int startTimeDefulat = 1893427500;
    int startTime = 0;
    int startCount = 0;
    // 构造特殊数据，方便构造查询场景
    for (int i = 0; i < insertTimes; i++) {
        startTime = startTimeDefulat + count * i;
        ret = insertDataToTable(stmt, tableName, count, startTime, startCount);
        startCount += count;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    (void)sprintf(
        sqlCmd, "select id, name, ip from %s where time >= 1893427600 order by id asc limit 20 offset 7;", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 验证查询结果
    int64_t cPersonId = 0;
    char cPersonName[64] = {0};
    char cPersonIp[33] = {0};
    int64_t expectTime[20] = {1893427550, 1893427558, 1893427560, 1893427568, 1893427570, 1893427578, 1893427580,
        1893427588, 1893427590, 1893427598, 1893427600, 1893427608, 1893427610, 1893427618, 1893427620, 1893427628,
        1893427630, 1893427638, 1893427640, 1893427648};

    uint32_t size = sizeof(int64_t);
    int fetchTimes = 0;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof) {
            break;
        }
        size = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &cPersonId, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 过滤时limit为20，offset为7
        AW_MACRO_EXPECT_EQ_INT(fetchTimes + 107, cPersonId);
        size = sizeof(cPersonName);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &cPersonName, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_STR(g_nameSource[(fetchTimes + 7) % 10], cPersonName);
        size = sizeof(cPersonIp);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &cPersonIp, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_STR(g_ipSource[(fetchTimes + 7) % 10], cPersonIp);
        fetchTimes++;
    }

    // 删表
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableName));
}

// 023.改进措施DTS2025043017206，where查询，向量化比较剪枝  预期：查询成功，数据正确
TEST_F(tsdbDefragmentation, Timing_081_tsdbPruning_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    char viewStatement[256] = {0};
    uint32_t cmdLen = 0;
    // 建表
    int ret = createTsTable(stmt, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 构造数据
    constexpr int insertTimes = 6;
    int count = 10;
    // 起始时间2030-1-1 0:5:0，防止tolerance影响
    int startTimeDefulat = 1893427500;
    int startTime[insertTimes] = {1893427510, 1893427520, 1893427500, 1893427540, 1893427550, 1893427530};
    int startCount = 0;
    // 构造特殊数据，方便构造查询场景
    for (int i = 0; i < insertTimes; i++) {
        ret = insertDataToTable(stmt, tableName, count, startTime[i], startCount);
        startCount += count;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 构造查询语句批次1，4 半满足 批次2,6 不满足 批次3,5 全满足
    (void)sprintf(sqlCmd,
        "select time, name, ip from %s where time <= 1893427512 or id = 3 or time = 1893427514 or id >= 35 and time < "
        "1893427560 and id <= 49 order by time asc;",
        tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 验证查询结果
    int64_t cPersonTime = 0;
    char cPersonName[64] = {0};
    char cPersonIp[33] = {0};
    int64_t expectTime[20] = {1893427550, 1893427558, 1893427560, 1893427568, 1893427570, 1893427578, 1893427580,
        1893427588, 1893427590, 1893427598, 1893427600, 1893427608, 1893427610, 1893427618, 1893427620, 1893427628,
        1893427630, 1893427638, 1893427640, 1893427648};

    uint32_t size = sizeof(int64_t);
    int fetchTimes = 0;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof) {
            break;
        }
        size = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &cPersonTime, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        size = sizeof(cPersonName);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &cPersonName, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        size = sizeof(cPersonIp);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &cPersonIp, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (fetchTimes <= 14) {
            AW_MACRO_EXPECT_EQ_INT(fetchTimes + startTimeDefulat, cPersonTime);
            AW_MACRO_EXPECT_EQ_STR(g_nameSource[fetchTimes % 10], cPersonName);
            AW_MACRO_EXPECT_EQ_STR(g_ipSource[fetchTimes % 10], cPersonIp);
        } else {
            AW_MACRO_EXPECT_EQ_INT(fetchTimes + startTimeDefulat + 30, cPersonTime);
            AW_MACRO_EXPECT_EQ_STR(g_nameSource[fetchTimes % 10], cPersonName);
            AW_MACRO_EXPECT_EQ_STR(g_ipSource[fetchTimes % 10], cPersonIp);
        }
        fetchTimes++;
    }
    // 删表
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableName));
}

// 024.改进措施DTS2025051908236，integer边界值验证包含时间列  预期：范围内可以写入
TEST_F(tsdbDefragmentation, Timing_081_tsdbPruning_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    char viewStatement[256] = {0};
    uint32_t cmdLen = 0;
    // 建表
    int ret = createTsTable(stmt, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 构造数据
    constexpr int insertTimes = 6;
    int count = 1;
    // 起始时间2030-1-1 0:5:0，防止tolerance影响
    int startTimeDefulat = 1893427500;
    int64_t startTime = 0;
    int64_t startId = 0;
    // 构造时间列约束最大值,64位和32位最大值不统一，32位最大值为2145887999，2037-12-31 23:59:59
#if defined(CPU_BIT_32)
    startTime = 2145887999;
#else
    startTime = 253402271999;
#endif
    ret = InsertIntegerBoundaryValue(stmt, tableName, count, startTime, startId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 构造时间列最小值
#if defined(CPU_BIT_32)
    startTime = -2145887999;
#else
    startTime = -253402271999;
#endif
    ret = InsertIntegerBoundaryValue(stmt, tableName, count, startTime, startId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    // 构造时间列0
    startTime = 0;
    startId = 1;
    ret = InsertIntegerBoundaryValue(stmt, tableName, count, startTime, startId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 构造非时间列int64最大值
    startTime = 1;
    startId = 9223372036854775807;
    ret = InsertIntegerBoundaryValue(stmt, tableName, count, startTime, startId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 构造非时间列int64最小值
    startTime = 2;
    startId = -9223372036854775807;
    ret = InsertIntegerBoundaryValue(stmt, tableName, count, startTime, startId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询验证
    (void)sprintf(sqlCmd, "select id, time from %s order by time asc;", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 验证查询结果
    int64_t cPersonTime = 0;
    int64_t cPersonId = 0;
    // 64位和32位时间列最大值不统一
#if defined(CPU_BIT_32)
    int64_t expectTime[20] = {0, 1, 2, 2145887999};
#else
    int64_t expectTime[20] = {0, 1, 2, 253402271999};
#endif
    int64_t expectId[20] = {1, 9223372036854775807, -9223372036854775807, 0};

    uint32_t size = sizeof(int64_t);
    int fetchTimes = 0;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof) {
            break;
        }
        size = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &cPersonId, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expectId[fetchTimes], cPersonId);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &cPersonTime, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expectTime[fetchTimes], cPersonTime);
        fetchTimes++;
    }
    // 删表
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableName));
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_PROGRAM_LIMIT_EXCEEDED, GMERR_INVALID_VALUE);
}

// 025.改进措施DTS2025051908236，char类型长度超规格验证  预期：超规格插入失败
TEST_F(tsdbDefragmentation, Timing_081_tsdbPruning_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    char viewStatement[256] = {0};
    uint32_t cmdLen = 0;
    // 建表
    (void)sprintf(sqlCmd,
        "create table %s (id integer, time integer, name char(65535)) with (time_col "
        "= 'time', interval = '1 hour');",
        tableName);
    cmdLen = strlen(sqlCmd);
    int ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 起始时间2030-1-1 0:5:0，防止tolerance影响
    int startTimeDefulat = 1893427500;
    // 构造数据长度最大值和超限场景
    int64_t id = 0;
    int64_t time = 0;
    int count = 1;
    char nameFalse[65536] = {0};
    char nameTrue[65535] = {0};
    for (int i = 0; i < 65536; i++) {
        nameFalse[i] = 'a';
    }
    (void)memcpy(nameTrue, nameFalse, sizeof(nameTrue));

    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, &id, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, &time, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_FIXED, nameFalse, sizeof(nameFalse), 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, ret);

    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, &id, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, &time, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_FIXED, nameTrue, sizeof(nameTrue), 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 构造长度最小值场景
    char nameNull[65535] = {0};
    id = 1;
    time = 1;
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, &id, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, &time, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_FIXED, nameNull, 65535, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询验证
    (void)sprintf(sqlCmd, "select * from %s order by time asc;", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 验证查询结果
    int64_t cPersonTime = 0;
    int64_t cPersonId = 0;
    char cPersonName[65535] = {0};
    int64_t expectTime[20] = {0, 1};
    int64_t expectId[20] = {0, 1};
    char expextName[2][65535] = {{0}, {0}};
    (void)memcpy(expextName[0], nameTrue, sizeof(expextName[0]));

    uint32_t size = sizeof(int64_t);
    int fetchTimes = 0;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof) {
            break;
        }
        size = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &cPersonId, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expectId[fetchTimes], cPersonId);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &cPersonTime, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expectTime[fetchTimes], cPersonTime);
        size = sizeof(cPersonName);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &cPersonName, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_STR(expextName[fetchTimes], cPersonName);
        fetchTimes++;
    }
    // 删表
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableName));
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);
}

// 026.改进措施DTS2025051908236，blob类型长度超规格验证  预期：超规格插入失败
TEST_F(tsdbDefragmentation, Timing_081_tsdbPruning_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    char viewStatement[256] = {0};
    uint32_t cmdLen = 0;
    // 建表
    (void)sprintf(sqlCmd,
        "create table %s (id integer, time integer, name blob(65535)) with (time_col "
        "= 'time', interval = '1 hour');",
        tableName);
    cmdLen = strlen(sqlCmd);
    int ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 起始时间2030-1-1 0:5:0，防止tolerance影响
    int startTimeDefulat = 1893427500;
    // 构造数据长度最最小值和超限场景
    int64_t id = 0;
    int64_t time = 0;
    constexpr int count = 1;
    char **nameFalse = (char **)malloc(count * sizeof(char *));
    if (nameFalse == NULL) {
        AW_FUN_Log(LOG_ERROR, "malloc failed\n");
    }
    for (int i = 0; i < count; i++) {
        nameFalse[i] = (char *)malloc(65536 * sizeof(char));
        if (nameFalse[i] == NULL) {
            AW_FUN_Log(LOG_ERROR, "malloc failed\n");
        }
        (void)memset(nameFalse[i], 0, 65536);
    }

    char **nameTrue = (char **)malloc(count * sizeof(char *));
    if (nameTrue == NULL) {
        AW_FUN_Log(LOG_ERROR, "malloc failed\n");
    }
    for (int i = 0; i < count; i++) {
        nameTrue[i] = (char *)malloc(65535 * sizeof(char));
        if (nameTrue[i] == NULL) {
            AW_FUN_Log(LOG_ERROR, "malloc failed\n");
        }
        (void)memset(nameTrue[i], 0, 65535);
    }
    uint32_t nameLen[count] = {65536};
    // 长度最小值场景
    for (int i = 0; i < 65536; i++) {
        nameFalse[0][i] = 'a';
    }

    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, &id, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, &time, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_BYTES, nameFalse, sizeof(nameFalse[0]), nameLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FIELD_OVERFLOW, ret);

    nameLen[0] = 65535;
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, &id, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, &time, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_BYTES, nameTrue, sizeof(nameTrue[0]), 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 构造长度最大值场景
    (void)memcpy(nameTrue[0], nameFalse[0], 65535);
    id = 1;
    time = 1;
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, &id, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, &time, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_BYTES, nameTrue, sizeof(nameTrue[0]), 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询验证
    (void)sprintf(sqlCmd, "select * from %s order by time asc;", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 验证查询结果
    int64_t cPersonTime = 0;
    int64_t cPersonId = 0;
    char cPersonName[65536] = {0};
    int64_t expectTime[20] = {0, 1};
    int64_t expectId[20] = {0, 1};
    char expextName[2][65536] = {{0}, {0}};
    (void)memcpy(expextName[1], nameTrue[0], sizeof(expextName[0]));

    uint32_t size = sizeof(int64_t);
    int fetchTimes = 0;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof) {
            break;
        }
        size = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &cPersonId, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expectId[fetchTimes], cPersonId);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &cPersonTime, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expectTime[fetchTimes], cPersonTime);
        size = sizeof(cPersonName);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &cPersonName, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_STR(expextName[fetchTimes], cPersonName);
        fetchTimes++;
    }

    for (int i = 0; i < count; i++) {
        free(nameFalse[i]);
        free(nameTrue[i]);
        nameFalse[i] = NULL;
        nameTrue[i] = NULL;
    }
    free(nameFalse);
    free(nameTrue);
    // 删表
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableName));
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_FIELD_OVERFLOW);
}

// 027.改进措施DTS2025051908236，text类型长度超规格验证  预期：超规格插入失败
TEST_F(tsdbDefragmentation, Timing_081_tsdbPruning_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    char viewStatement[256] = {0};
    uint32_t cmdLen = 0;
    // 建表
    (void)sprintf(sqlCmd,
        "create table %s (id integer, time integer, name text) with (time_col "
        "= 'time', interval = '1 hour');",
        tableName);
    cmdLen = strlen(sqlCmd);
    int ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 起始时间2030-1-1 0:5:0，防止tolerance影响
    int startTimeDefulat = 1893427500;
    // 构造数据长度最最小值和超限场景
    int64_t id = 0;
    int64_t time = 0;
    constexpr int count = 1;
    char **nameFalse = (char **)malloc(count * sizeof(char *));
    if (nameFalse == NULL) {
        AW_FUN_Log(LOG_ERROR, "malloc failed\n");
    }
    for (int i = 0; i < count; i++) {
        nameFalse[i] = (char *)malloc(65536 * sizeof(char));
        if (nameFalse[i] == NULL) {
            AW_FUN_Log(LOG_ERROR, "malloc failed\n");
        }
        (void)memset(nameFalse[i], 0, 65536);
    }

    char **nameTrue = (char **)malloc(count * sizeof(char *));
    if (nameTrue == NULL) {
        AW_FUN_Log(LOG_ERROR, "malloc failed\n");
    }
    for (int i = 0; i < count; i++) {
        nameTrue[i] = (char *)malloc(65535 * sizeof(char));
        if (nameTrue[i] == NULL) {
            AW_FUN_Log(LOG_ERROR, "malloc failed\n");
        }
        (void)memset(nameTrue[i], 0, 65535);
    }
    // 长度最小值场景
    for (int i = 0; i < 65536; i++) {
        nameFalse[0][i] = 'a';
    }

    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, &id, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, &time, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_STRING, nameFalse, sizeof(nameFalse[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 构造长度最大值场景
    (void)memcpy(nameTrue[0], nameFalse[0], 65535);
    id = 1;
    time = 1;
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, &id, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, &time, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_STRING, nameTrue, sizeof(nameTrue[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询验证
    (void)sprintf(sqlCmd, "select * from %s order by time asc;", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 验证查询结果
    int64_t cPersonTime = 0;
    int64_t cPersonId = 0;
    char cPersonName[65535] = {0};
    int64_t expectTime[20] = {0, 1};
    int64_t expectId[20] = {0, 1};
    char expextName[2][65535] = {{0}, {0}};
    // 开发逻辑是截取65535字节，且第65535字节会用/0替换，所以这里需要把第65535字节置为0
    (void)memcpy(expextName[0], nameTrue[0], sizeof(expextName[0]) - 1);
    (void)memcpy(expextName[1], nameTrue[0], sizeof(expextName[0]) - 1);

    uint32_t size = sizeof(int64_t);
    int fetchTimes = 0;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof) {
            break;
        }
        size = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &cPersonId, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expectId[fetchTimes], cPersonId);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &cPersonTime, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expectTime[fetchTimes], cPersonTime);
        size = sizeof(cPersonName);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &cPersonName, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_STR(expextName[fetchTimes], cPersonName);
        fetchTimes++;
    }

    for (int i = 0; i < count; i++) {
        free(nameFalse[i]);
        free(nameTrue[i]);
        nameFalse[i] = NULL;
        nameTrue[i] = NULL;
    }
    free(nameFalse);
    free(nameTrue);
    // 删表
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableName));
}

// 028.建逻辑表按月分区，ttl单位设置为天  预期：按照设置的老化时间进行老化
// ttl设置为day，alter使ttl逐渐变小并验证ttl后台线程是否生效，在设置为month后再验证
TEST_F(tsdbDefragmentation, Timing_081_tsdbPruning_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"tsLcmCheckPeriod=10\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    ret = TestTsGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[256] = {0};
    char tableName2[20] = "testdb2";
    uint32_t cmdLen = 0;
    int ret = 0;
    // 建表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(160), ns text) with (time_col "
        "= 'time', interval = '1 month', ttl = '8 day');",
        tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(160), ns text) with (time_col "
        "= 'time', interval = '1 month', ttl = '10 month');",
        tableName2);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T;
    int count = 1000;
    int idStart = 10000;
    ret = insertDataToTable(stmt, tableName, count, startTime, idStart);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    startTime = time_T - 86400 * 39;
    ret = insertDataToTable(stmt, tableName, count, startTime, idStart * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    startTime = time_T - 86400 * 40;
    ret = insertDataToTable(stmt, tableName, count, startTime, idStart * 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 第二张表注入数据
    startTime = time_T - 86400 * 61;
    ret = insertDataToTable(stmt, tableName2, count, startTime, idStart * 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 等待ttl触发
    sleep(12);
    int dataCount = 0;
    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "SELECT * FROM %s where id >= 10000 order by time;", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1000, dataCount);
    // 变更ttl为5day
    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "alter table %s set (ttl = '5 day')", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    startTime = time_T - 86400 * 37;
    ret = insertDataToTable(stmt, tableName, count, startTime, idStart * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    startTime = time_T - 86400;
    ret = insertDataToTable(stmt, tableName, count, startTime, idStart * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 等待ttl触发
    sleep(12);
    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "SELECT * FROM %s where id >= 10000 order by time;", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(2000, dataCount);
    // 验证第二张表数据
    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "SELECT * FROM %s order by time;", tableName2);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1000, dataCount);
    // 变更ttl为month
    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "alter table %s set (ttl = '1 month')", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    startTime = time_T - 86400 * 62;
    ret = insertDataToTable(stmt, tableName, count, startTime, idStart * 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    startTime = time_T - 86400 * 9;
    ret = insertDataToTable(stmt, tableName, count, startTime, idStart * 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 等待ttl触发
    sleep(12);
    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "SELECT * FROM %s where id >= 10000 order by time;", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(3000, dataCount);
    ret = DropTsTable(stmt, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTsTable(stmt, tableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 029.建逻辑表按月分区，ttl单位设置为月，alter后变更为day  预期：按照设置的老化时间进行老化
// ttl设置为month，alter使ttl逐渐变小并验证ttl后台线程是否生效，在设置为month后再验证
TEST_F(tsdbDefragmentation, Timing_081_tsdbPruning_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"tsLcmCheckPeriod=10\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    ret = TestTsGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[256] = {0};
    char tableName2[20] = "testdb2";
    uint32_t cmdLen = 0;
    int ret = 0;
    // 建表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(160), ns text) with (time_col "
        "= 'time', interval = '1 month', ttl = '2 month');",
        tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(160), ns text) with (time_col "
        "= 'time', interval = '1 month', ttl = '10 month');",
        tableName2);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T;
    int count = 1000;
    int idStart = 10000;
    ret = insertDataToTable(stmt, tableName, count, startTime, idStart);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    startTime = time_T - 86400 * 39;
    ret = insertDataToTable(stmt, tableName, count, startTime, idStart * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    startTime = time_T - 86400 * 40;
    ret = insertDataToTable(stmt, tableName, count, startTime, idStart * 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 第二张表注入数据
    startTime = time_T - 86400 * 40;
    ret = insertDataToTable(stmt, tableName2, count, startTime, idStart * 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 等待ttl触发
    sleep(12);
    int dataCount = 0;
    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "SELECT * FROM %s order by time;", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(3000, dataCount);
    (void)memset(sqlCmd, 0, 256);
    // 变更ttl
    (void)sprintf(sqlCmd, "alter table %s set (ttl = '5 day')", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 等待ttl触发
    sleep(12);
    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "SELECT * FROM %s order by time;", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1000, dataCount);
    // 验证第二张表数据
    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "SELECT * FROM %s order by time;", tableName2);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1000, dataCount);
    // 变更ttl
    (void)sprintf(sqlCmd, "alter table %s set (ttl = '1234 day')", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    startTime = time_T - 86400 * 40;
    ret = insertDataToTable(stmt, tableName, count, startTime, idStart * 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 等待ttl触发
    sleep(12);
    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "SELECT * FROM %s order by time;", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(2000, dataCount);
    ret = DropTsTable(stmt, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTsTable(stmt, tableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 030.建内存表按月分区，ttl单位设置为天  预期：按照设置的老化时间进行老化
// ttl设置为day，alter使ttl逐渐变小并验证ttl后台线程是否生效，在设置为month后再验证
TEST_F(tsdbDefragmentation, Timing_081_tsdbPruning_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"tsLcmCheckPeriod=10\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    ret = TestTsGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[256] = {0};
    char tableName2[20] = "testdb2";
    uint32_t cmdLen = 0;
    int ret = 0;
    // 建表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(160), ns text) with (time_col "
        "= 'time', interval = '1 month', ttl = '8 day', enGine = 'mEmOry', max_size = 50000);",
        tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(160), ns text) with (time_col "
        "= 'time', interval = '1 month', ttl = '10 month');",
        tableName2);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T - 86400 * 7;
    int count = 1000;
    int idStart = 10000;
    ret = insertDataToTable(stmt, tableName, count, startTime, idStart);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    startTime = time_T - 86400 * 35;
    ret = insertDataToTable(stmt, tableName, count, startTime, idStart * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    startTime = time_T - 86400 * 40;
    ret = insertDataToTable(stmt, tableName, count, startTime, idStart * 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 第二张表注入数据
    startTime = time_T - 86400 * 40;
    ret = insertDataToTable(stmt, tableName2, count, startTime, idStart * 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 等待ttl触发
    sleep(12);
    int dataCount = 0;
    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "SELECT * FROM %s where id >= 10000 order by time;", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1000, dataCount);
    // 变更ttl为5day
    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "alter table %s set (ttl = '5 day')", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    startTime = time_T - 86400 * 32;
    ret = insertDataToTable(stmt, tableName, count, startTime, idStart * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    startTime = time_T - 86400;
    ret = insertDataToTable(stmt, tableName, count, startTime, idStart * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 等待ttl触发
    sleep(12);
    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "SELECT * FROM %s where id >= 10000 order by time;", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1000, dataCount);
    // 验证第二张表数据
    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "SELECT * FROM %s order by time;", tableName2);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1000, dataCount);
    // 变更ttl为month
    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "alter table %s set (ttl = '1 month')", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    startTime = time_T - 86400 * 10;
    ret = insertDataToTable(stmt, tableName, count, startTime, idStart * 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    startTime = time_T - 86400 * 40;
    ret = insertDataToTable(stmt, tableName, count, startTime, idStart * 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 等待ttl触发
    sleep(12);
    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "SELECT * FROM %s where id >= 10000 order by time;", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(2000, dataCount);
    ret = DropTsTable(stmt, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTsTable(stmt, tableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 031.问题单DTS2025062539837补充用例，物理表id回收后再次利用时
TEST_F(tsdbDefragmentationValue1, Timing_081_tsdbPruning_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    char tableName2[20] = "t_oper_org";
    uint32_t cmdLen = 0;
    int ret = 0;
    // 建表
    (void)sprintf(sqlCmd,
        "CREATE TABLE IF NOT EXISTS %s(log_id INTEGER, log_time INTEGER, login_user TEXT, login_ip CHAR(64), "
        // "vsys_id INTEGER, content_en TEXT, content_ch TEXT) WITH (interval = '1 hour', disk_limit = '3321284030 B', "
        "vsys_id INTEGER, content_en TEXT, content_ch TEXT, add_new_col INTEGER) WITH (interval = '1 hour', disk_limit "
        "= '3321284030 B', "
        "time_col = 'log_time');",
        tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)sprintf(sqlCmd,
        "CREATE TABLE IF NOT EXISTS %s(log_id INTEGER, log_time INTEGER, login_user INET, login_ip CHAR(64), "
        "vsys_id INTEGER, content_en TEXT, content_ch TEXT) WITH (interval = '1 hour', disk_limit = '3321284030 B', "
        "time_col = 'log_time');",
        tableName2);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    int64_t startTime = 1750787840;
    int count = 24;
    int idStart = 200159825;
    int times = 0;
    for (int i = 0; i < 20; i++) {
        // 注入数据到检查时间全部在1h以外
        startTime += 1;
        count = 1;
        idStart += 1;
        ret = insertNewDataToTable(stmt, tableName, count, startTime, idStart);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE'\" -s %s", g_connServerTsdb);
    system(sqlCmd);

    (void)sprintf(sqlCmd, "Truncate table %s;", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < 20; i++) {
        // 注入数据到检查时间全部在1h以外
        startTime += 1;
        count = 1;
        idStart += 1;
        ret = insertNewDataToTable(stmt, tableName, count, startTime, idStart);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE'\" -s %s", g_connServerTsdb);
    system(sqlCmd);

    (void)sprintf(sqlCmd, "Truncate table %s;", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < 31; i++) {
        // 注入数据到检查时间全部在1h以外
        startTime += 1;
        count = 1;
        idStart += 1;
        ret = insertIPDataToTable(stmt, tableName2, count, startTime, idStart);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE'\" -s %s", g_connServerTsdb);
    system(sqlCmd);

    // 删表
    DropTsTable(stmt, tableName);
    DropTsTable(stmt, tableName2);
}

// 0、打开磁盘整理
// 1、创建表A
// -- 查一下表A的ID
// 2、插入表A 1条数据
// 3、alter表A
// -- 查一下表A的ID
// 4、创建表B
// 5、插入表A 31条数据
// 032.问题单DTS2025062539837补充用例，物理表id回收后再次利用时，踩存
TEST_F(tsdbDefragmentationValue1, Timing_081_tsdbPruning_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    char tableName2[20] = {0};
    uint32_t cmdLen = 0;
    int ret = 0;
    // 建表
    (void)sprintf(sqlCmd,
        "CREATE TABLE IF NOT EXISTS %s(log_id INTEGER, log_time INTEGER, login_user INTEGER, login_ip INTEGER, "
        "vsys_id INTEGER, content_en INTEGER, content_ch INTEGER) WITH (interval = '1 hour', disk_limit = '3321284030 "
        "B', "
        "time_col = 'log_time');",
        tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < 80; i++) {
        (void)sprintf(tableName2, "testdb%d", i);
        (void)sprintf(sqlCmd,
            "CREATE TABLE IF NOT EXISTS %s(log_id INTEGER, log_time INTEGER, login_user TEXT, login_ip TEXT, "
            "vsys_id TEXT, content_en TEXT, content_ch TEXT) WITH (interval = '1 hour', disk_limit = '3321284030 "
            "B', "
            "time_col = 'log_time');",
            tableName2);
        cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    int64_t startTime = 1750787840;
    int count = 24;
    int idStart = 200159825;
    int times = 0;
    for (int i = 0; i < 80; i++) {

        // 注入数据到检查时间全部在1h以外
        count = 1;
        ret = insertAllIntDataTable(stmt, tableName, count, startTime, idStart);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        (void)sprintf(sqlCmd, "alter table %s SET (ttl = '20 hour');", tableName);
        cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        (void)sprintf(tableName2, "testdb%d", i);
        // 注入数据到检查时间全部在1h以外
        count = 20;
        ret = insertAllTextDataTable(stmt, tableName2, count, startTime, idStart);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        (void)sprintf(sqlCmd, "alter table %s SET (ttl = '20 hour');", tableName2);
        cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (int i = 0; i < 31; i++) {
        // 注入数据到检查时间全部在1h以外
        startTime += 1;
        count = 1;
        idStart += 1;
        ret = insertAllIntDataTable(stmt, tableName, count, startTime, idStart);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 删表
    DropTsTable(stmt, tableName);
    for (int i = 0; i < 80; i++) {
        (void)sprintf(tableName2, "testdb%d", i);
        DropTsTable(stmt, tableName2);
    }
}
