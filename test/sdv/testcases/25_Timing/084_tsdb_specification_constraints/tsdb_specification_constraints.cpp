/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2026. All rights reserved.
 * Description: TSDB 配置项 测试
 * Author: qinjianhua
 * Create: 2025-06-12
 */

#include "gtest/gtest.h"
#include "t_rd_common.h"
#include "../../common/include/component/t_rd_ts.h"
#define MAX_CMD_SIZE 1024
char tabelName[] = "testdb";
static GmcConnT *g_conn_sync = NULL;
static GmcStmtT *g_stmt_sync = NULL;
char g_command[MAX_CMD_SIZE];
Status ret = 0;

class tsdb_specification_constraints : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void tsdb_specification_constraints::SetUpTestCase()
{
    AW_FUN_Log(LOG_STEP, "test start.");
}

void tsdb_specification_constraints::TearDownTestCase()
{
    AW_FUN_Log(LOG_STEP, "test start.");
}

void tsdb_specification_constraints::SetUp()
{
    AW_FUN_Log(LOG_STEP, "test start.");
    InitTsCiCfg();
    // 创建epoll监听线程
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("rm -rf /data/gmdb*");
}

void tsdb_specification_constraints::TearDown()
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RecoverTsCiCfg();
    system("sh $TEST_HOME/tools/start.sh -ts ");
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 配置文件参数 全部正常，建连、断连成功
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    system("sh $TEST_HOME/tools/start.sh -ts ");
    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 deviceSize=0 ，预期服务启动失败
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"deviceSize=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 deviceSize=1 ，预期服务启动成功
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"deviceSize=1\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"extendSize=1024\"");

    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 deviceSize=4 ，预期服务启动成功
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    // 持久化场景：4*deviceSize≤maxSeMem
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"deviceSize=4\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 deviceSize=1025 ，预期服务启动失败
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"deviceSize=1025\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 pageSize= ，预期服务启动失败
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"pageSize=\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 pageSize=4 ，预期服务启动成功
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"pageSize=4\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 pageSize=7 ，预期服务启动失败
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"pageSize=7\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 pageSize=8 ，预期服务启动成功
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"pageSize=8\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 pageSize=9 ，预期服务启动失败
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"pageSize=9 \"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 pageSize=15 ，预期服务启动失败
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"pageSize=15\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 pageSize=16 ，预期服务启动成功
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"pageSize=16\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 pageSize=17 ，预期服务启动失败
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"pageSize=17 \"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 pageSize=31 ，预期服务启动失败
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"pageSize=31\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 pageSize=32 ，预期服务启动成功
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"pageSize=32\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 pageSize=33 ，预期服务启动失败
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"pageSize=33 \"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 pageSize=63 ，预期服务启动失败
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"pageSize=63\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 pageSize=64 ，预期服务启动成功
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"pageSize=64\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 pageSize=65 ，预期服务启动失败
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"pageSize=65 \"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 maxSeMem=7 ，预期服务启动失败
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    if (g_envType == 2) {
        system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxSeMem=7\"");
    } else {
        system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxSeMem=7\"");
    }
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数maxSeMem  =8 ，预期服务启动成功
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"deviceSize=2\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"extendSize=1024\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxSeMem=8\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 maxSeMem=1024 ，预期服务启动成功
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxSeMem=1024\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 maxSeMem=1048552 ，预期服务启动成功
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    // 修改1 DTS2020110904OJ00P0G00 机器本身最大内存有限，目前能设置4096能起服务成功，5120会失败
    // 修改2 开发修改了配置项deviceSize大小，默认是4M。如果maxSemMem配置项小于deviceSize，服务器会拉不起来，用例需适配
    // maxTotalShmSize=（maxSeMem+maxSysShmSize[256]+maxHprShmSize[512]+APP_SHM_SIZE[12]）;arm32中maxTotalShmSize最大3072M
#if (defined ENV_RTOSV2 && defined CPU_BIT_32) || (RUN_SIMULATE)
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxSeMem=3048\"");  // 3072 - 512 - 256 - 12 = 2992
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxSysShmSize=12\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxTotalShmSize=3072\"");
#elif defined FEATURE_PERSISTENCE
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxSeMem=1024\"");
#else
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"pageSize=64\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"deviceSize=1024\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxSeMem=1048552\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxSysShmSize=12\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxTotalShmSize=1048576\"");
#endif
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 maxSeMem=边界值+1 ，预期服务启动失败
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

#if (defined ENV_RTOSV2 && defined CPU_BIT_32) || (RUN_SIMULATE)
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxSeMem=3049\"");  // 3072 - 512 - 256 - 12 = 2992
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxSysShmSize=12\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxTotalShmSize=3072\"");
#elif defined FEATURE_PERSISTENCE
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"pageSize=64\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"deviceSize=1024\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxSeMem=729445\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxSysShmSize=12\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxTotalShmSize=1048576\"");
#else
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"deviceSize=1024\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxSeMem=1048553\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxSysShmSize=12\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxTotalShmSize=1048576\"");
#endif
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 maxSeMem=4 ，预期服务启动失败；验证maxSeMem等于deviceSize的最小值且小于8时，服务启动失败
 Author       : **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    // 开发修改了配置项deviceSize大小，默认是4M。如果maxSemMem配置项小于deviceSize，服务器会拉不起来，用例需适配
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxSeMem=4\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    ASSERT_EQ(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 maxSeMem=16384 ，预期服务启动成功
 Author       : **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    // 2022-05-07 deviceSize已将默认的值从32改为4，为验证maxSeMem的上边界，需将deviceSize改成16
    // 2022-10-27 maxTotalShmSize = maxSeMem + maxSysShmSize + maxHprShmSize + APP_SHM_SIZE[12]
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"deviceSize=16\"");
    // maxSysShmSize、maxHprShmSize和应用区的值各分配16M的空间，所以maxSeMem的值为 16384 - 48 = 16336；arm32：3072 - 48
    // - 12 = 3012
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxSysShmSize=16\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxHprShmSize=16\"");
#if (defined ENV_RTOSV2 && defined CPU_BIT_32) || (RUN_SIMULATE)
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxSeMem=3012\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxTotalShmSize=3072\"");
#else
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxSeMem=16324\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxTotalShmSize=16384\"");
#endif
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 maxSeMem=16385 ，预期服务启动失败
 Author       : **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"deviceSize=16\"");
    // 验证maxSeMem的值超出上边界
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxSeMem=16385\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 instanceId=0 ，预期服务启动失败
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"instanceId=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 instanceId=2 ，预期服务启动成功
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"instanceId=2\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 instanceId=3 ，预期服务启动失败
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"instanceId=3\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    // 502版本多实例要求，参数范围变更
    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 localLocatorListened 为默认值 ，预期服务启动成功
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 localLocatorListened 为空 ，预期服务启动失败
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"localLocatorListened= \"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 logLengthMax=127 ，预期服务启动失败
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"logLengthMax=127\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 logLengthMax=128 ，预期服务启动成功
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"logLengthMax=128\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 logLengthMax=1024 预期服务启动成功
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"logLengthMax=1024\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 logLengthMax=1025 ，预期服务启动失败
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"logLengthMax=1024\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (g_envType == 0) {
        system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"logLengthMax=1025\"");
        system("sh $TEST_HOME/tools/start.sh -ts ");

        ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
    } else {
        AW_FUN_Log(LOG_STEP, "IoT/9700 does not need to start.sh\n");
    }
}

/* ****************************************************************************
 Description  : 配置文件参数 logFileNumMax=0 ，预期服务启动失败
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"logFileNumMax=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 logFileNumMax=1 ，预期服务启动成功
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"logFileNumMax=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 logFileNumMax=16 ，预期服务启动成功
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"logFileNumMax=16\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 logFileNumMax=1024 ，预期服务启动成功
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"logFileNumMax=1024\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 logFileNumMax=1025 ，预期服务启动失败
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"logFileNumMax=1025\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 logFileSizeMax超出边界值[128,67108864] ，预期服务启动成功
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"logFileSizeMax=127\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"logFileSizeMax=67108865\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"logFileSizeMax=128\"");  // 2025.01.13 配置项范围调整[128,67108864]
    system("sh $TEST_HOME/tools/start.sh -ts ");
    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system(
        "sh $TEST_HOME/tools/modifyCfg.sh -ts \"logFileSizeMax=67108864\"");  // 2025.01.13 配置项范围调整[128,67108864]
    system("sh $TEST_HOME/tools/start.sh -ts ");
    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 isUseHugePage=-1 ，预期服务启动失败
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"isUseHugePage=-1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 isUseHugePage=0 ，预期服务启动成功
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"isUseHugePage=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 2023-02-15：欧拉SDV构建发生变更，不能占用太多内存，下架43用例

/* ****************************************************************************
 Description  : 配置文件参数 isUseHugePage=2 ，预期服务启动失败
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"isUseHugePage=2\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 maxTotalShmSize=31 ，预期服务启动失败
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxTotalShmSize=31\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 maxTotalShmSize=3 ，预期服务启动成功
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    // maxTotalShmSize >= maxSeMem[8] + maxSysShmSize[12]  + APP[12]
    // maxTotalShmSize的最小值调整为40M,maxSysShmSize最小12M，应用区最小12M。
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"deviceSize=2\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"extendSize=1024\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxTotalShmSize=40\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxSeMem=8\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxSysShmSize=12\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 maxTotalShmSize=2048 ，预期服务启动成功
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxTotalShmSize=2048\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 maxTotalShmSize=1048576 ，预期服务启动成功
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

#if defined ENV_RTOSV2 && defined CPU_BIT_32
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxTotalShmSize=3072\"");
#else
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxTotalShmSize=1048576\"");
#endif
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 maxTotalShmSize=1048577 ，预期服务启动失败
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system(
        "sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxTotalShmSize=1048577\"");  // 引入特性：支持大规模向量数据管理与ANN查询/SR.IR20240827001382.001
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 maxTotalDynSize=23 ，预期服务启动失败
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxTotalDynSize=23\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 maxTotalDynSize=24 ，预期服务启动成功
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    // maxTotalDynSize = maxSysDynSize + 应用区最小值为12M
    // 逃生通道占用内存为61M60Kb，持久化占用内存约60M，应用区最少12M，maxSysDynSize最少12M
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"pageSize=16\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"bufferPoolSize=512\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"redoPubBufSize=1024\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxSysDynSize=12\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxTotalDynSize=24\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 maxTotalDynSize=2048 ，预期服务启动成功
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxTotalDynSize=2048\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 maxTotalDynSize=1572864 ，预期服务启动成功
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_054)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

#if defined ENV_RTOSV2 && defined CPU_BIT_32
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxTotalDynSize=3072\"");
#else
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxTotalDynSize=1572864\"");
#endif
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 maxTotalDynSize=1572865 ，预期服务启动失败
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_055)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system(
        "sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxTotalDynSize=1572865\"");  // 2025.01.13 配置项范围调整[24,1572864] M
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 planCacheSize=7 ，预期服务启动失败
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_056)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"planCacheSize=7\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 planCacheSize=8 ，预期服务启动成功
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_057)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"planCacheSize=8\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 planCacheSize=32 ，预期服务启动成功
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_058)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"planCacheSize=32\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 planCacheSize=1048576 ，预期服务启动成功
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_059)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"planCacheSize=1048576\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 planCacheSize=1048577 ，预期服务启动失败
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_060)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"planCacheSize=1048577\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 overloadThreshold cpu 超过边界值[0,100] ，预期服务启动失败
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_061)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    // CPU配置为负数

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts "
           "\"overloadThreshold=cpu:70,80,80,85,85,-1;dynamicMemory:70,80,80,85,85,90;shareMemory:70,80,80,85,85,90;"
           "subscribeQueue:70,80,80,85,85,90\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
    // CPU配置超过100

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts "
           "\"overloadThreshold=cpu:70,80,80,85,85,101;dynamicMemory:70,80,80,85,85,90;shareMemory:70,80,80,85,85,90;"
           "subscribeQueue:70,80,80,85,85,90\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 overloadThreshold dynamicMemory 超过边界值[0,100] ，预期服务启动失败
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_062)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    // dynamicMemory配置为负数

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts "
           "\"overloadThreshold=cpu:70,80,80,85,85,90;dynamicMemory:70,80,80,85,85,-1;shareMemory:70,80,80,85,85,90;"
           "subscribeQueue:70,80,80,85,85,90\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
    // dynamicMemory配置超过100

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts "
           "\"overloadThreshold=cpu:70,80,80,85,85,90;dynamicMemory:70,80,80,85,85,101;shareMemory:70,80,80,85,85,90;"
           "subscribeQueue:70,80,80,85,85,90\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 overloadThreshold shareMemory 超过边界值[0,100] ，预期服务启动失败
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_063)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    // shareMemory配置为负数

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts "
           "\"overloadThreshold=cpu:70,80,80,85,85,90;dynamicMemory:70,80,80,85,85,90;shareMemory:70,80,80,85,85,-1;"
           "subscribeQueue:70,80,80,85,85,90\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);

    // shareMemory配置超过100

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts "
           "\"overloadThreshold=cpu:70,80,80,85,85,90;dynamicMemory:70,80,80,85,85,90;shareMemory:70,80,80,85,85,101;"
           "subscribeQueue:70,80,80,85,85,90\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 overloadThreshold subscribeQueue 超过边界值[0,100] ，预期服务启动失败
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_064)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    // subscribeQueue配置为负数

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts "
           "\"overloadThreshold=cpu:70,80,80,85,85,90;dynamicMemory:70,80,80,85,85,90;shareMemory:70,80,80,85,85,90;"
           "subscribeQueue:70,80,80,85,85,-1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
    // subscribeQueue配置超过100

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts "
           "\"overloadThreshold=cpu:70,80,80,85,85,90;dynamicMemory:70,80,80,85,85,90;shareMemory:70,80,80,85,85,90;"
           "subscribeQueue:70,80,80,85,85,101\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 overloadThreshold cpu 为默认值 ，预期服务启动
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_065)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts "
           "\"overloadThreshold=cpu:70,80,80,85,85,90;dynamicMemory:70,80,80,85,85,90;shareMemory:70,80,80,85,85,90;"
           "subscribeQueue:70,80,80,85,85,90\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 memCompactEnable=-1 ，预期服务启动失败
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_066)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"memCompactEnable=-1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 memCompactEnable=0 ，预期服务启动成功
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_067)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"memCompactEnable=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 memCompactEnable=1 ，预期服务启动成功
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_068)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"memCompactEnable=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 memCompactEnable=2 ，预期服务启动失败
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_069)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"memCompactEnable=2\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 enableTableLock=-1 ，预期服务启动失败
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_070)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enableTableLock=-1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 enableTableLock=0 ，预期服务启动成功
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_071)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enableTableLock=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 enableTableLock=1 ，预期服务启动成功
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_072)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enableTableLock=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 enableTableLock=2 ，预期服务启动失败
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_073)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enableTableLock=2\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 minFragmentationRateThreshold=-1 ，预期服务启动失败
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_074)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"minFragmentationRateThreshold=-1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 minFragmentationRateThreshold=0 ，预期服务启动成功
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_075)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"minFragmentationRateThreshold=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 minFragmentationRateThreshold=50 ，预期服务启动成功
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_076)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"minFragmentationRateThreshold=50\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 minFragmentationRateThreshold=100 ，预期服务启动成功
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_077)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"minFragmentationRateThreshold=100\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 minFragmentationRateThreshold=101 ，预期服务启动失败
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_078)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"minFragmentationRateThreshold=101\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 minFragmentationMemThreshold=-1 ，预期服务启动失败
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_079)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"minFragmentationMemThreshold=-1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 minFragmentationMemThreshold=0 ，预期服务启动成功
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_080)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"minFragmentationMemThreshold=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 minFragmentationMemThreshold=64 ，预期服务启动成功
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_081)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"minFragmentationMemThreshold=64\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 minFragmentationMemThreshold=16384 ，预期服务启动成功
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_082)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

#if defined ENV_RTOSV2 && defined CPU_BIT_32
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"minFragmentationMemThreshold=3072\"");
#else
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"minFragmentationMemThreshold=16384\"");
#endif
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 minFragmentationMemThreshold=16385 ，预期服务启动失败
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_083)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"minFragmentationMemThreshold=16385\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 longProcTimeThreshold=-2 ，预期服务启动失败
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_084)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"longProcTimeThreshold=-2\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 longProcTimeThreshold=-1 ，预期服务启动成功
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_085)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"longProcTimeThreshold=-1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 longProcTimeThreshold=100 ，预期服务启动成功
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_086)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"longProcTimeThreshold=100\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 longProcTimeThreshold=1000 ，预期服务启动成功
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_087)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"longProcTimeThreshold=1000\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 longProcTimeThreshold=1001 ，预期服务启动失败
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_088)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"workerHungThreshold=6\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"longProcTimeThreshold=1001\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : maxUndoSpaceSize=0 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_089)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxUndoSpaceSize=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}
/* ****************************************************************************
 Description  : maxUndoSpaceSize=1025 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_090)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxUndoSpaceSize=1025\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}
/* ****************************************************************************
 Description  : maxUndoSpaceSize=1 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_091)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 1;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxUndoSpaceSize=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  : maxUndoSpaceSize=1024 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_092)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxUndoSpaceSize=1024\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  : latchDeadlockDebugTimeout=0 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_093)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"latchDeadlockDebugTimeout=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}
/* ****************************************************************************
 Description  : latchDeadlockDebugTimeout=60000001 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_094)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"latchDeadlockDebugTimeout=60000001\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}
/* ****************************************************************************
 Description  : latchDeadlockDebugTimeout=1 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_095)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"latchDeadlockDebugTimeout=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  : latchDeadlockDebugTimeout=60000000 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_096)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"latchDeadlockDebugTimeout=60000000\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  : maxSortBufferSize=0 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_097)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxSortBufferSize=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : maxSortBufferSize=257 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_098)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxSortBufferSize=257\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : maxSortBufferSize=1 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_099)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxSortBufferSize=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : maxSortBufferSize=256 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_100)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxSortBufferSize=256\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : sortDataLenThreshold=-1 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_101)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  : sortDataLenThreshold=1000 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_102)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  : sortDataLenThreshold=1025 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_103)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  : sortDataLenThreshold=0 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_104)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  : isCopyBufferWhenSort=-1 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_105)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  : isCopyBufferWhenSort=3 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_106)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  : isCopyBufferWhenSort=2 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_107)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  : monitorWorkerSchedulePeriod=5001 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_108)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"workerHungThreshold=15,16,17\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"monitorWorkerSchedulePeriod=5001\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : monitorWorkerSchedulePeriod=0 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_109)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"workerHungThreshold=15,16,17\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"monitorWorkerSchedulePeriod=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : monitorWorkerSchedulePeriod=5000 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_110)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"workerHungThreshold=15,16,17\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"monitorWorkerSchedulePeriod=5000\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  : monitorWorkerSchedulePeriod=-1 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_111)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"monitorWorkerSchedulePeriod=-1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}
/* ****************************************************************************
 Description  : workerHungThreshold=3,4,5 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_112)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"workerHungThreshold=3,4,5\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  : workerHungThreshold=998,999,1000 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_113)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"workerHungThreshold=998,999,1000\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  : workerHungThreshold=999,1000,1001 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_114)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"workerHungThreshold=999,1000,1001\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}
/* ****************************************************************************
 Description  : workerHungThreshold=0,1,2 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_115)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"workerHungThreshold=0,1,2\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : agePushSubsBatch=1 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_116)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"agePushSubsBatch=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  : agePushSubsBatch=200 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_117)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"agePushSubsBatch=200\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  : agePushSubsBatch=201 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_118)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"agePushSubsBatch=201\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}
/* ****************************************************************************
 Description  : agePushSubsBatch=0 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_119)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"agePushSubsBatch=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}
/* ****************************************************************************
 Description  : userPolicyMode=-1 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_120)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"userPolicyMode=-1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : userPolicyMode=0 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_121)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"userPolicyMode=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : userPolicyMode=1 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_122)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"userPolicyMode=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  : userPolicyMode=3 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_123)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"userPolicyMode=3\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}
/* ****************************************************************************
 Description  : enablePrintAgedInfo=-1 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_124)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enablePrintAgedInfo=-1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : enablePrintAgedInfo=0 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_125)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enablePrintAgedInfo=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : enablePrintAgedInfo=1 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_126)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enablePrintAgedInfo=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  : enablePrintAgedInfo=2 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_127)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enablePrintAgedInfo=2\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : enableLogFold=0 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_128)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enableLogFold=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  : enableLogFold=1 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_129)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enableLogFold=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  : enableLogFold=-1 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_130)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enableLogFold=-1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}
/* ****************************************************************************
 Description  : enableLogFold=2 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_131)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enableLogFold=2\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : enableDmlPerfStat=2 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_132)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enableDmlPerfStat=2\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}
/* ****************************************************************************
 Description  : enableDmlPerfStat=-1 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_133)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enableDmlPerfStat=-1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : enableDmlPerfStat=0 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_134)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enableDmlPerfStat=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  : enableDmlPerfStat=1 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_135)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enableDmlPerfStat=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : enableDmlOperStat=2 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_136)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enableDmlOperStat=2\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}
/* ****************************************************************************
 Description  : enableDmlOperStat=-1 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_137)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enableDmlOperStat=-1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 enableDmlOperStat=0 ，预期服务启动成功
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_138)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enableDmlOperStat=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 enableDmlOperStat=1 ，预期服务启动成功
**************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_139)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enableDmlOperStat=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 DBA超过255 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_140)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    char name[1024] = "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                      "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                      "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx";
    // localLocatorListened配置参数与连接g_connServer不一致
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts "
           "\"DBA="
           "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
           "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
           "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : 配置文件参数 DBA•user和process有空字符串 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_141)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"DBA=:gmrule\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"DBA=litedb_admin:\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : 一级挂死阈值 < 二级挂死阈值 > 三级挂死阈值1
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_142)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"workerHungThreshold=100,101,99\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
    AW_FUN_Log(LOG_STEP, "test stop.");
}

/* ****************************************************************************
 Description  : 一级挂死阈值 > 二级挂死阈值 < 三级挂死阈值2
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_143)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"workerHungThreshold=100,99,101\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
    AW_FUN_Log(LOG_STEP, "test stop.");
}

/* ****************************************************************************
 Description  : 一级挂死阈值 > 二级挂死阈值 > 三级挂死阈值3
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_144)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"workerHungThreshold=100,99,98\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
    AW_FUN_Log(LOG_STEP, "test stop.");
}

/* ****************************************************************************
 Description  : 一级挂死阈值 > 二级挂死阈值 > 三级挂死阈值4
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_145)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"workerHungThreshold=100,100,100\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
    AW_FUN_Log(LOG_STEP, "test stop.");
}

/* ****************************************************************************
 Description  : 一级挂死阈值小于3*monitorWorkerSchedulePeriod
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_146)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_DATA_EXCEPTION);
    AddWhiteList(GMERR_CONFIG_ERROR);
    int32_t ret = 0;
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"workerHungThreshold=3,4,5\"");
    system(
        "sh $TEST_HOME/tools/modifyCfg.sh -ts \"monitorWorkerSchedulePeriod=2000\"");  // monitorWorkerSchedulePeriod=2000,workerHungThreshold至少需要6s
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
    AW_FUN_Log(LOG_STEP, "test stop.");
}

#define MAX_CMD_SIZE 1024
/* ****************************************************************************
 Description  : 三级挂死阈值默认为20s,299s,300s
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_147)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"workerHungThreshold=20,299,300\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test stop.");
}

/* ****************************************************************************
 Description  : trxLockWakeupPeriod==0 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_148)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"trxLockWakeupPeriod=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}
/* ****************************************************************************
 Description  : trxLockWakeupPeriod=1000001 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_149)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"trxLockWakeupPeriod=1000001\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}
/* ****************************************************************************
 Description  : trxLockWakeupPeriod=1 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_150)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 1;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"trxLockWakeupPeriod=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  : trxLockWakeupPeriod=1000000 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_151)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"trxLockWakeupPeriod=1000000\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : trxDeadlockCheckPeriod=0 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_152)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"trxDeadlockCheckPeriod=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}
/* ****************************************************************************
 Description  : trxDeadlockCheckPeriod=1000001 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_153)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"trxDeadlockCheckPeriod=1000001\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}
/* ****************************************************************************
 Description  : trxDeadlockCheckPeriod=1 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_154)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 1;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"trxDeadlockCheckPeriod=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : trxDeadlockCheckPeriod=1000000 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_155)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"trxDeadlockCheckPeriod=1000000\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : trxLockJumpQueuePeriod=0 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_156)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"trxLockJumpQueuePeriod=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}
/* ****************************************************************************
 Description  : trxLockJumpQueuePeriod=1000001 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_157)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"trxLockJumpQueuePeriod=1000001\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}
/* ****************************************************************************
 Description  : trxLockJumpQueuePeriod=1 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_158)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"trxLockJumpQueuePeriod=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  : trxLockJumpQueuePeriod=1000000 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_159)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"trxLockJumpQueuePeriod=1000000\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : trxLockTimeOut=0 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_160)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"trxLockTimeOut=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}
/* ****************************************************************************
 Description  : trxLockTimeOut=1000001 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_161)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"trxLockTimeOut=1000001\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}
/* ****************************************************************************
 Description  : trxLockTimeOut=1 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_162)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"trxLockTimeOut=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  : trxLockTimeOut=1000000 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_163)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"trxLockTimeOut=1000000\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : trxMonitorEnable=-1 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_164)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"trxMonitorEnable=-1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : trxMonitorEnable=2 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_165)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"trxMonitorEnable=2\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}
/* ****************************************************************************
 Description  : trxMonitorEnable=0 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_166)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"trxMonitorEnable=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  : trxMonitorEnable=1 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_167)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"trxMonitorEnable=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : trxMonitorThreshold=0,1 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_168)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"trxMonitorThreshold=0,1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}
/* ****************************************************************************
 Description  : trxMonitorThreshold=1000,1001 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_169)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"trxMonitorThreshold=1000,1001\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}
/* ****************************************************************************
 Description  : trxMonitorThreshold=1,2 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_170)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"trxMonitorThreshold=1,2\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  : trxMonitorThreshold=999,1000 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_171)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"trxMonitorThreshold=999,1000\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : defaultTablespaceMaxSize=0 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_172)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"defaultTablespaceMaxSize=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}
/* ****************************************************************************
 Description  : defaultTablespaceMaxSize=65535 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_173)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"defaultTablespaceMaxSize=65535\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}
/* ****************************************************************************
 Description  : defaultTablespaceMaxSize=1 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_174)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"defaultTablespaceMaxSize=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  : defaultTablespaceMaxSize=3071 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_175)
{
    AW_FUN_Log(LOG_STEP, "test start.");

#if defined(CPU_BIT_32)
    // arm32环境 defaultTablespaceMaxSize Range ：[deviceSize, maxSeMem-deviceSize] maxSeMem Range ：[8,3048]
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"defaultTablespaceMaxSize=3047\"");
#else
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"defaultTablespaceMaxSize=3071\"");
#endif

    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : logFoldRule=0:0;86400:1000 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_176)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"logFoldRule=0:0;86400:1000\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}
/* ****************************************************************************
 Description  : logFoldRule=0:0;86401:1001  ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_177)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"logFoldRule=1:1;86401:1001\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}
/* ****************************************************************************
 Description  : logFoldRule=1:1,3600:50 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_178)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"logFoldRule=1:1,3600:50\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  : logFoldRule=2:2,86400:1000 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_179)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"logFoldRule=2:2,86400:1000\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : maxSysShmSize=11 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_180)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxSysShmSize=11\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}
/* ****************************************************************************
 Description  : maxSysShmSize=16365 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_181)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxSysShmSize=16365\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}
/* ****************************************************************************
 Description  : maxSysShmSize==12 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_182)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxSysShmSize=12\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  : maxSysShmSize=16364 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_183)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // maxTotalShmSize >=（maxSeMem+maxSysShmSize+APP_SHM_SIZE），其中APP_SHM_SIZE为应用区共享内存大小且至少要有12MB以上

#if defined(CPU_BIT_32)
    // arm32环境 maxSysShmSize Range ：[12,3052] maxTotalShmSize Range ：[24,3072]
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxTotalShmSize=3072\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxSysShmSize=256\"");
#else
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxTotalShmSize=1048576\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxSysShmSize=16364\"");
#endif

    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : fixPersistEnable=-1 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_184)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"fixPersistEnable=-1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : fixPersistEnable=0 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_185)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"fixPersistEnable=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : fixPersistEnable=1 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_186)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // •启多区持久化才有实时备份的能力。
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"dataFileDirPath=/data/gmdb,/data/gmdb2\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"multizonePersistNum=2\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"recoveryZoneId=2\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"crcCheckEnable=1\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"fixPersistEnable=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : fixPersistEnable=2 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_187)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"fixPersistEnable=2\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : maxSysDynSize=11 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_188)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxSysDynSize=11\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}
/* ****************************************************************************
 Description  : maxSysDynSize=1572832 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_189)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxSysDynSize=1572832\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}
/* ****************************************************************************
 Description  : maxSysDynSize=12 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_190)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // DTS2025062707703
    //  •如果想要取得maxTotalDynSize、maxSysDynSize的最小值，则pageSize设置值需小于32kB（即只能为8kB或者16kB）。该场景下只能保证数据库最基本的功能。
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"pageSize=16\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"bufferPoolSize=512\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"redoPubBufSize=1024\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxSysDynSize=12\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxTotalDynSize=24\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  : maxSysDynSize=1572831 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_191)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // •maxTotalDynSize>=maxSysDynSize+APP_DYN_SIZE，其中APP_DYN_SIZE为应用区动态内存大小且至少要有12MB。
#if defined(CPU_BIT_32)
    // arm32环境 maxSysDynSize Range ：[12,3039] maxTotalDynSize Range ：[24,3072]
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxTotalDynSize=3072\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxSysDynSize=256\"");
#else
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxTotalDynSize=1572864\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxSysDynSize=1572831\"");
#endif

    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : liteDynMemMod=-1 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_192)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"liteDynMemMod=-1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}
/* ****************************************************************************
 Description  : liteDynMemMod=2 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_193)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"liteDynMemMod=2\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}
/* ****************************************************************************
 Description  : liteDynMemMod=0 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_194)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"liteDynMemMod=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  : liteDynMemMod=1 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_195)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"liteDynMemMod=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : clientServerFlowControl=2;0;0;0 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_196)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"clientServerFlowControl=2;0;0;0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}
/* ****************************************************************************
 Description  : clientServerFlowControl=0;0;0;2 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_197)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"clientServerFlowControl=0;0;0;2\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}
/* ****************************************************************************
 Description  : clientServerFlowControl=0;0;0;0 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_198)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"clientServerFlowControl=0;0;0;0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  : clientServerFlowControl=1;1;1;1 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_199)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"clientServerFlowControl=1;1;1;1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : lpm4VrIdMax=15 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_200)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"lpm4VrIdMax=15\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}
/* ****************************************************************************
 Description  : lpm4VrIdMax=4097 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_201)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"lpm4VrIdMax=4097\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}
/* ****************************************************************************
 Description  : lpm4VrIdMax=16 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_202)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"lpm4VrIdMax=16\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  : lpm4VrIdMax=4096 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_203)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"lpm4VrIdMax=4096\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : lpm4VrfIdMax=1023 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_204)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"lpm4VrfIdMax=1023\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}
/* ****************************************************************************
 Description  : lpm4VrfIdMax=16385 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_205)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"lpm4VrfIdMax=16385\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}
/* ****************************************************************************
 Description  : lpm4VrfIdMax=1024 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_206)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"lpm4VrfIdMax=1024\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  : lpm4VrfIdMax=16384 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_207)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"lpm4VrfIdMax=16384\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : lpm6VrIdMax=15 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_208)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"lpm6VrIdMax=15\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : lpm6VrIdMax=16 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_209)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"lpm6VrIdMax=16   `\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : lpm6VrIdMax=4096 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_210)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"lpm6VrIdMax=4096\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : lpm6VrIdMax=4097 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_211)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"lpm6VrIdMax=4097\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : lpm6VrfIdMax=1023 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_212)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"lpm6VrfIdMax=1023\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : lpm6VrfIdMax=1024 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_213)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"lpm6VrfIdMax=1024\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : lpm6VrfIdMax=16384 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_214)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"lpm6VrfIdMax=16384\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : lpm6VrfIdMax=16385 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_215)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"lpm6VrfIdMax=16385\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : maxConnNum=15 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_216)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxConnNum=15\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : maxConnNum=16 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_217)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxConnNum=16\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : maxConnNum=1536 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_218)
{
    AW_FUN_Log(LOG_STEP, "test start.");

#if (defined(ENV_RTOSV2) || defined(ENV_RTOSV2X)) && !defined(CPU_BIT_32)
    // 仿真环境maxConnNum Range : [16, 1024]
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxConnNum=1024\"");
#else
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxConnNum=1536\"");
#endif

    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : maxConnNum=1537 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_219)
{
    AW_FUN_Log(LOG_STEP, "test start.");

#if (defined(ENV_RTOSV2) || defined(ENV_RTOSV2X)) && !defined(CPU_BIT_32)
    // 仿真环境maxConnNum Range : [16, 1024]
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxConnNum=1025\"");
#else
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxConnNum=1537\"");
#endif
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : maxStmtCnt=0 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_220)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxStmtCnt=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : maxStmtCnt=1 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_221)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxStmtCnt=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : maxStmtCnt=65535 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_222)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxStmtCnt=65535\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : maxStmtCnt=65536 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_223)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxStmtCnt=65536\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : scheduleMode=-1 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_224)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"scheduleMode=-1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : scheduleMode=0 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_225)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"scheduleMode=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : scheduleMode=2 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_226)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"scheduleMode=2\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : scheduleMode=3 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_227)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"scheduleMode=3\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : permanentWorkerNum=0 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_228)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"permanentWorkerNum=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : permanentWorkerNum=1 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_229)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"permanentWorkerNum=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : permanentWorkerNum=1536 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_230)
{
    AW_FUN_Log(LOG_STEP, "test start.");

#if defined(ENV_RTOSV2) || defined(ENV_RTOSV2X) || defined(CPU_BIT_32)
    // permanentWorkerNum Range : [1, 1024]
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"permanentWorkerNum=1024\"");
#else
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"permanentWorkerNum=1536\"");
#endif
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : permanentWorkerNum=1537 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_231)
{
    AW_FUN_Log(LOG_STEP, "test start.");

#if defined(ENV_RTOSV2) || defined(ENV_RTOSV2X)
    // permanentWorkerNum Range : [1, 1024]
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"permanentWorkerNum=1025\"");
#else
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"permanentWorkerNum=1537\"");
#endif
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : subsChannelGlobalShareMemSizeMax=0 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_232)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"subsChannelGlobalShareMemSizeMax=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : subsChannelGlobalShareMemSizeMax=1 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_233)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"subsChannelGlobalShareMemSizeMax=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : subsChannelGlobalShareMemSizeMax=2048 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_234)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"subsChannelGlobalShareMemSizeMax=2048\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : subsChannelGlobalShareMemSizeMax=2049 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_235)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"subsChannelGlobalShareMemSizeMax=2049\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : subsChannelGlobalDynamicMemSizeMax=0 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_236)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"subsChannelGlobalDynamicMemSizeMax=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : subsChannelGlobalDynamicMemSizeMax=1 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_237)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"subsChannelGlobalDynamicMemSizeMax=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : subsChannelGlobalDynamicMemSizeMax=2048 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_238)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"subsChannelGlobalDynamicMemSizeMax=2048\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : subsChannelGlobalDynamicMemSizeMax=2049 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_239)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"subsChannelGlobalDynamicMemSizeMax=2049\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : compatibleV3=-1 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_240)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"compatibleV3=-1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : compatibleV3=0 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_241)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"compatibleV3=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : compatibleV3=1 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_242)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"compatibleV3=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : compatibleV3=2 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_243)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"compatibleV3=2\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : isFastReadUncommitted=-1 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_244)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"isFastReadUncommitted=-1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : isFastReadUncommitted=0 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_245)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"isFastReadUncommitted=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : isFastReadUncommitted=1 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_246)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"isFastReadUncommitted=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : isFastReadUncommitted=2 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_247)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"isFastReadUncommitted=2\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : auditLogEnableDCL=-1 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_248)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"auditLogEnableDCL=-1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : auditLogEnableDCL=0 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_249)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"auditLogEnableDCL=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : auditLogEnableDCL=1 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_250)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"auditLogEnableDCL=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : auditLogEnableDCL=2 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_251)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"auditLogEnableDCL=2\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : auditLogEnableDDL=-1 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_252)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"auditLogEnableDDL=-1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : auditLogEnableDDL=0 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_253)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"auditLogEnableDDL=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : auditLogEnableDDL=1 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_254)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"auditLogEnableDDL=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : auditLogEnableDDL=2 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_255)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"auditLogEnableDDL=2\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : auditLogEnableDML=-1 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_256)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"auditLogEnableDML=-1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : auditLogEnableDML=0 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_257)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"auditLogEnableDML=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : auditLogEnableDML=1 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_258)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"auditLogEnableDML=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : auditLogEnableDML=2 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_259)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"auditLogEnableDML=2\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : auditLogEnableDQL=-1 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_260)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"auditLogEnableDQL=-1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : auditLogEnableDQL=0 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_261)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"auditLogEnableDQL=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : auditLogEnableDQL=1 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_262)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"auditLogEnableDQL=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : auditLogEnableDQL=2 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_263)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"auditLogEnableDQL=2\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : isCltStatisEnable=-1 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_264)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"isCltStatisEnable=-1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : isCltStatisEnable=0 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_265)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"isCltStatisEnable=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : isCltStatisEnable=1 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_266)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"isCltStatisEnable=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : isCltStatisEnable=2 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_267)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"isCltStatisEnable=2\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : enableConSubsStatis=-1 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_268)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enableConSubsStatis=-1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : enableConSubsStatis=0 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_269)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enableConSubsStatis=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : enableConSubsStatis=1 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_270)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enableConSubsStatis=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : enableConSubsStatis=2 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_271)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enableConSubsStatis=2\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : enableSchedulePerfStat=-1 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_272)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enableSchedulePerfStat=-1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : enableSchedulePerfStat=0 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_273)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enableSchedulePerfStat=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : enableSchedulePerfStat=1 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_274)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enableSchedulePerfStat=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : enableSchedulePerfStat=2 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_275)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enableSchedulePerfStat=2\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : messageSecurityCheck=-1 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_276)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"messageSecurityCheck=-1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : messageSecurityCheck=0 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_277)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"messageSecurityCheck=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : messageSecurityCheck=1 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_278)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"messageSecurityCheck=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : messageSecurityCheck=2 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_279)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"messageSecurityCheck=2\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : shmemPermission=-1 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_280)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"shmemPermission=-1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : shmemPermission=0 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_281)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"shmemPermission=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : shmemPermission=0600 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_282)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"shmemPermission=0600\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : shmemPermission=2 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_283)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"shmemPermission=2\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : shmemPermission=0x66 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_284)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"shmemPermission=0x66\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : shmemPermission=0600 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_285)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"shmemPermission=0600\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : devShmemPermission=0x66 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_286)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"devShmemPermission=0x66\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : devShmemPermission=0600 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_287)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"devShmemPermission=0600\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : defaultTransactionType=-1 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_288)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"defaultTransactionType=-1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : defaultTransactionType=0 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_289)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"defaultTransactionType=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : defaultTransactionType=1 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_290)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // YANG场景（乐观+可重复读）
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"defaultTransactionType=1\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"defaultIsolationLevel=2\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : defaultTransactionType=2 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_291)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"defaultTransactionType=2\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : maxConnMsgShmMem=7 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_292)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxConnMsgShmMem=7\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : maxConnMsgShmMem=8 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_293)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxConnMsgShmMem=8\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : maxConnMsgShmMem=16384 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_294)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxConnMsgShmMem=16384\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : maxConnMsgShmMem=16385 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_295)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxConnMsgShmMem=16385\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : connectTimeout=-1 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_296)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"connectTimeout=-1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : connectTimeout=0 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_297)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"connectTimeout=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : connectTimeout=1000000 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_298)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"connectTimeout=1000000\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : connectTimeout=1000001 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_299)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"connectTimeout=1000001\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : enableClusterHash=-1 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_300)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enableClusterHash=-1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : enableClusterHash=0 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_301)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enableClusterHash=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : enableClusterHash=1 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_302)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enableClusterHash=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : enableClusterHash=2 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_303)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enableClusterHash=2\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : maxNormalTableNum=999 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_304)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxNormalTableNum=999\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : maxNormalTableNum=1000 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_305)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxNormalTableNum=1000\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : maxNormalTableNum=10000 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_306)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxNormalTableNum=10000\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : maxNormalTableNum=10001 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_307)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxNormalTableNum=10001\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : persistentMode=-1 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_308)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"persistentMode=-1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : persistentMode=0 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_309)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // buffer pool场景只支持增量持久化
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"persistentMode=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : persistentMode=1 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_310)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"persistentMode=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : persistentMode=2 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_311)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"persistentMode=2\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : redoPubBufSize=255 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_312)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"redoPubBufSize=255\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : redoPubBufSize=256 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_313)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"redoPubBufSize=256\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : redoPubBufSize=16384 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_314)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // •redoFileSize取值需>= 2 * redoPubBufSize + 512B。
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"redoFileSize=1024\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"redoPubBufSize=16384\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : redoPubBufSize=16385 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_315)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"redoPubBufSize=16385\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : redoBufParts=0 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_316)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"redoBufParts=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : redoBufParts=1 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_317)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"redoBufParts=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : redoBufParts=16 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_318)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"redoBufParts=16\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : redoBufParts=17 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_319)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"redoBufParts=17\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : redoFlushByTrx=-1 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_320)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"redoFlushByTrx=-1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : redoFlushByTrx=0 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_321)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"redoFlushByTrx=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : redoFlushByTrx=1 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_322)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"redoFlushByTrx=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : redoFlushByTrx=2 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_323)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"redoFlushByTrx=2\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : redoFlushBySize=-1 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_324)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"redoFlushBySize=-1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : redoFlushBySize=0 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_325)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"redoFlushBySize=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : redoFlushBySize=16384 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_326)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"redoFlushBySize=16384\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : redoFlushBySize=16385 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_327)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"redoFlushBySize=16385\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : redoFlushByTime=-1 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_328)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"redoFlushByTime=-1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : redoFlushByTime=11 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_329)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // redoFlushByTime > redoFlushCheckPeriod
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"redoFlushByTime=11\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"redoFlushCheckPeriod=10\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : redoFlushByTime=600000 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_330)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"redoFlushByTime=600000\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : redoFlushByTime=600001 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_331)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"redoFlushByTime=600001\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : redoFlushCheckPeriod=9 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_332)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"redoFlushCheckPeriod=9\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : redoFlushCheckPeriod=10 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_333)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"redoFlushCheckPeriod=10\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : redoFlushCheckPeriod=300000 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_334)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // redoFlushByTime > redoFlushCheckPeriod
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"redoFlushByTime=600000\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"redoFlushCheckPeriod=300000\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : redoFlushCheckPeriod=300001 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_335)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"redoFlushCheckPeriod=300000\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : redoFileSize=0 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_336)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"redoFileSize=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : redoFileSize=1 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_337)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // •redoFileSize取值需>= 2 * redoPubBufSize + 512B。
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"redoPubBufSize=256\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"redoFileSize=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : redoFileSize=1024 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_338)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"redoFileSize=1024\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : redoFileSize=1025 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_339)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"redoFileSize=1025\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : redoFileCount=0 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_340)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"redoFileCount=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : redoFileCount=1 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_341)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"redoFileCount=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : redoFileCount=64 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_342)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"redoFileCount=64\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : redoFileCount=65 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_343)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"redoFileCount=65\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : dbFilesMaxCnt=2 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_344)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"dbFilesMaxCnt=2\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : dbFilesMaxCnt=3 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_345)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"spaceMaxNum=3\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"dbFilesMaxCnt=3\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : dbFilesMaxCnt=1024 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_346)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"dbFilesMaxCnt=1024\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : dbFilesMaxCnt=1025 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_347)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"dbFilesMaxCnt=1025\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : spaceMaxNum=2 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_348)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"spaceMaxNum=2\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : spaceMaxNum=3 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_349)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"spaceMaxNum=3\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : spaceMaxNum=1024 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_350)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"spaceMaxNum=1024\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : spaceMaxNum=1025 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_351)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"spaceMaxNum=1025\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : dataFileDirPath= ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_352)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"dataFileDirPath= \"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : dataFileDirPath=256字节（包含结束符） ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_353)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts "
           "\"dataFileDirPath=/data/"
           "gmdb11111111111111111111111111111gmdb11111111111111111111111111111gmdb11111111111111111111111111111gmdb1111"
           "1111111111111111111111111gmdb11111111111111111111111111111gmdb11111111111111111111111111111gmdb111111111111"
           "11111111111111111gmdb11111111111111\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : dataFileDirPath=257字节（包含结束符），预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_354)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts "
           "\"dataFileDirPath=/data/"
           "gmdb11111111111111111111111111111gmdb11111111111111111111111111111gmdb11111111111111111111111111111gmdb1111"
           "1111111111111111111111111gmdb11111111111111111111111111111gmdb11111111111111111111111111111gmdb111111111111"
           "11111111111111111gmdb111111111111112\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : dbFileSize=4095 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_355)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"dbFileSize=4095\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : dbFileSize=4096 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_356)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"dbFileSize=4096\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : dbFileSize=33554432 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_357)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"dbFileSize=33554432\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : dbFileSize=33554433 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_358)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"dbFileSize=33554433\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : diskLessBoot=-1 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_359)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=-1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : diskLessBoot=0 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_360)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : diskLessBoot=1 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_361)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : diskLessBoot=2 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_362)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=2\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : ckptPeriod=0 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_363)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"ckptPeriod=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : ckptPeriod=1 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_364)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"ckptPeriod=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : ckptPeriod=65535 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_365)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"ckptPeriod=65535\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : ckptPeriod=65536 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_366)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"ckptPeriod=65536\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : ckptThreshold=1 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_367)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"ckptThreshold=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : ckptThreshold=2 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_368)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"ckptThreshold=2\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : ckptThreshold=268435456 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_369)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"ckptThreshold=268435456\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : ckptThreshold=268435457 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_370)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"ckptThreshold=268435457\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : bufferPoolSize=127 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_371)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"bufferPoolSize=127\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : bufferPoolSize=4096 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_372)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"bufferPoolSize=4096\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : bufferPoolSize=4096 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_373)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"bufferPoolSize=4096\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : bufferPoolSize=4194273 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_374)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"bufferPoolSize=4194273\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : bufferPoolPolicy=-1 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_375)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"bufferPoolPolicy=-1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : bufferPoolPolicy=0 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_376)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"bufferPoolPolicy=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : bufferPoolPolicy=3 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_377)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"bufferPoolPolicy=3\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : bufferPoolPolicy=4 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_378)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"bufferPoolPolicy=4\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : dwrEnable=-1 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_379)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"dwrEnable=-1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : dwrEnable=0 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_380)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"dwrEnable=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : dwrEnable=1 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_381)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"dwrEnable=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : dwrEnable=2 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_382)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"dwrEnable=2\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : crcCheckEnable=-1 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_383)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"crcCheckEnable=-1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : crcCheckEnable=0 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_384)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"crcCheckEnable=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : crcCheckEnable=1 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_385)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"crcCheckEnable=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : crcCheckEnable=2 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_386)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"crcCheckEnable=2\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : shaCheckEnable=-1 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_387)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"shaCheckEnable=-1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : shaCheckEnable=0 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_388)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"shaCheckEnable=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : shaCheckEnable=1 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_389)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"shaCheckEnable=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : shaCheckEnable=2 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_390)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"shaCheckEnable=2\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : multizonePersistNum=0 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_391)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"multizonePersistNum=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : multizonePersistNum=1 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_392)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"multizonePersistNum=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : multizonePersistNum=2 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_393)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"multizonePersistNum=2\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);

    // 多区路径通过dataFileDirPath配置项配置，使用“,”逗号分隔多个路径。路径数必须和multizonePersistNum相等，否则报错。
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"dataFileDirPath=/data/gmdb,/data/gmdb2\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"multizonePersistNum=2\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : multizonePersistNum=3 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_394)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"multizonePersistNum=3\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : recoveryZoneId=0 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_395)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"recoveryZoneId=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : recoveryZoneId=1 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_396)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"recoveryZoneId=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : recoveryZoneId=2 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_397)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"recoveryZoneId=2\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);

    // multizonePersistNum >= recoveryZoneId
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"dataFileDirPath=/data/gmdb,/data/gmdb2\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"multizonePersistNum=2\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"recoveryZoneId=2\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : recoveryZoneId=3 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_398)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"recoveryZoneId=3\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : enableShareMsgPool=-1 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_399)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enableShareMsgPool=-1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : enableShareMsgPool=0 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_400)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enableShareMsgPool=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : enableShareMsgPool=1 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_401)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enableShareMsgPool=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : enableShareMsgPool=2 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_402)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enableShareMsgPool=2\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : directWrite=-1 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_403)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"directWrite=-1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : directWrite=0 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_404)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"directWrite=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : directWrite=2 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_405)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"directWrite=2\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : directWrite=3 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_406)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"directWrite=4\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

// 时序专有配置项

/* ****************************************************************************
 Description  : tempFileDir=256字节（包含结束符） ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_407)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts "
           "\"tempFileDir=/data/"
           "gmdb11111111111111111111111111111gmdb11111111111111111111111111111gmdb11111111111111111111111111111gmdb1111"
           "1111111111111111111111111gmdb11111111111111111111111111111gmdb11111111111111111111111111111gmdb111111111111"
           "11111111111111111gmdb11111111111111\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : tempFileDir=257字节（包含结束符），预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_408)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts "
           "\"tempFileDir=/data/"
           "gmdb11111111111111111111111111111gmdb11111111111111111111111111111gmdb11111111111111111111111111111gmdb1111"
           "1111111111111111111111111gmdb11111111111111111111111111111gmdb11111111111111111111111111111gmdb111111111111"
           "11111111111111111gmdb111111111111112\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : forceUseTempFileForQueryResult=-1 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_409)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"forceUseTempFileForQueryResult=-1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : forceUseTempFileForQueryResult=0 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_410)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"forceUseTempFileForQueryResult=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : forceUseTempFileForQueryResult=1 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_411)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"forceUseTempFileForQueryResult=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : forceUseTempFileForQueryResult=2 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_412)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"forceUseTempFileForQueryResult=2\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : operatorMemory=0 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_413)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"operatorMemory=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : operatorMemory=1 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_414)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"operatorMemory=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : operatorMemory=1024 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_415)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"operatorMemory=1024\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : operatorMemory=1025 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_416)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"operatorMemory=1025\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : cStoreDir=256字节（包含结束符） ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_417)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts "
           "\"cStoreDir=/data/"
           "gmdb11111111111111111111111111111gmdb11111111111111111111111111111gmdb11111111111111111111111111111gmdb1111"
           "1111111111111111111111111gmdb11111111111111111111111111111gmdb11111111111111111111111111111gmdb111111111111"
           "11111111111111111gmdb11111111111111\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : cStoreDir=257字节（包含结束符），预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_418)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts "
           "\"cStoreDir=/data/"
           "gmdb11111111111111111111111111111gmdb11111111111111111111111111111gmdb11111111111111111111111111111gmdb1111"
           "1111111111111111111111111gmdb11111111111111111111111111111gmdb11111111111111111111111111111gmdb111111111111"
           "11111111111111111gmdb111111111111112\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : enableChannelReuse=-1 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_419)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enableChannelReuse=-1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : enableChannelReuse=0 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_420)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enableChannelReuse=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : enableChannelReuse=1 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_421)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enableChannelReuse=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : enableChannelReuse=2 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_422)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enableChannelReuse=2\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : tsLcmCheckPeriod=2 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_423)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"tsLcmCheckPeriod=2\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : tsLcmCheckPeriod=3 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_424)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"tsLcmCheckPeriod=3\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : tsLcmCheckPeriod=3600 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_425)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"tsLcmCheckPeriod=3600\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : tsLcmCheckPeriod=3601 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_426)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"tsLcmCheckPeriod=3601\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : isFileTapeCheckSum=-1 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_427)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"isFileTapeCheckSum=-1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : isFileTapeCheckSum=0 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_428)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"isFileTapeCheckSum=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : isFileTapeCheckSum=1 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_429)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"isFileTapeCheckSum=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : isFileTapeCheckSum=2 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_430)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"isFileTapeCheckSum=2\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : resultTempFileMaxSize=31MB ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_431)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"resultTempFileMaxSize=31MB\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : resultTempFileMaxSize=32MB ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_432)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"resultTempFileMaxSize=32MB\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : resultTempFileMaxSize=1TB ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_433)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"resultTempFileMaxSize=1TB\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : resultTempFileMaxSize=1.1TB ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_434)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"resultTempFileMaxSize=1.1TB\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : tsAllowDiskClean=-1 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_435)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"tsAllowDiskClean=-1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : tsAllowDiskClean=0 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_436)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"tsAllowDiskClean=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : tsAllowDiskClean=1 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_437)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"tsAllowDiskClean=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : tsAllowDiskClean=2 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_438)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"tsAllowDiskClean=2\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : cuCompactEnable=-1 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_439)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"cuCompactEnable=-1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : cuCompactEnable=0 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_440)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"cuCompactEnable=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : cuCompactEnable=1 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_441)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"cuCompactEnable=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : cuCompactEnable=2 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_442)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"cuCompactEnable=2\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : enableVectorizedPushDown=-1 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_443)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enableVectorizedPushDown=-1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : enableVectorizedPushDown=0 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_444)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enableVectorizedPushDown=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : enableVectorizedPushDown=1 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_445)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enableVectorizedPushDown=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : enableVectorizedPushDown=2 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_446)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enableVectorizedPushDown=2\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : isHighConcurrent=-1 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_447)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"isHighConcurrent=-1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}

/* ****************************************************************************
 Description  : isHighConcurrent=0 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_448)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 不能同时打开isHighConcurrent和cuCompactEnable
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"isHighConcurrent=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : isHighConcurrent=1 ，预期服务启动成功
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_449)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"cuCompactEnable=0\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"isHighConcurrent=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : isHighConcurrent=2 ，预期服务启动失败
 **************************************************************************** */
TEST_F(tsdb_specification_constraints, Timing_084_450)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"isHighConcurrent=2\"");
    system("sh $TEST_HOME/tools/start.sh -ts ");

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
}
