/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2024. All rights reserved.
 * Description: 【技术转交付】TSDB 基础查询 测试
 * Author: jiangjincheng
 * Create: 2024-03-19
 */

#include "gtest/gtest.h"
#include "t_rd_ts.h"
#include "../006_tsdb_query/tsdb_query.h"

char tabelName_T_TRF_ORG[] = "t_trf_org";
char tabelName_T_THRT_ORG[] = "t_thrt_org";
char tabelName_T_REPORT[] = "t_report";
char *dir = getenv("GMDB_HOME");

char tabelName[] = "testdb";
char tabelName_1[] = "testdb_1";
char compressModeFast[] = "fast";
char compressModeRapidlz[] = "fast(rapidlz)";
char compressModeZstar[] = "fast(zstar)";
char compressModeNo[] = "no";

static GmcConnOptionsT *connOptions = NULL;
static GmcConnT *conn = NULL;
static GmcStmtT *stmt = NULL;
Status ret = 0;
bool eof = false;
bool isNull = false;
uint32_t size = sizeof(int64_t);

class TsdbBatchReadFirewall : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        InitTsCiCfg();
    }
    static void TearDownTestCase()
    {
        GmcConnOptionsDestroy(connOptions);
        GmcFreeStmt(stmt);
        GmcDisconnect(conn);
        testEnvClean();
        RecoverTsCiCfg();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void TsdbBatchReadFirewall::SetUp()
{
    AW_CHECK_LOG_BEGIN();
}

void TsdbBatchReadFirewall::TearDown()
{
    CsvFileDelete();
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_CHECK_LOG_END();
}

int DropCmTable(char *tableName)
{
    int ret = 0;
    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd, "drop table %s;", tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    return ret;
}

void FirewallDataBulkInsert(int tableTypeFlag)
{
    // tableTypeFlag=0表示建表为T_REPORT
    if (tableTypeFlag == 0) {
        system("cd $GMDB_HOME;source scripts/env_aarch64.sh;cd $GMDB_HOME/test/sdv/tools/;./deploy.sh euler;source "
               "../autotest_env.sh;");
        system("cp -f $GMDB_HOME/test/dt/st/experimental_tsdb/ts/gmserver_ts.ini /usr/local/file/gmserver.ini");
        system(
            "sh start.sh;taskset -c 0 start.sh;cd $GMDB_HOME/test/dt/st/experimental_tsdb/ts_perf_nlog/;sh build.sh;");
        system("cd $GMDB_HOME/test/dt/st/experimental_tsdb/ts_perf_nlog/build/;"
               "./benchmark-gmdb-ddl -file=./schema/T_REPORT_gmdb.sql >> "
               "$GMDB_HOME/test/sdv/testcases/25_Timing/006_tsdb_query/mylog.txt;"
               "./nlog_batch_insert_T_REPORT_gmdb 1000000 50000 1704038400 >> "
               "$GMDB_HOME/test/sdv/testcases/25_Timing/006_tsdb_query/mylog.txt");
    }
    // tableTypeFlag=1表示建表为T_TRF_ORG
    else if (tableTypeFlag == 1) {
        system("cd $GMDB_HOME;source scripts/env_aarch64.sh;cd $GMDB_HOME/test/sdv/tools/;./deploy.sh euler;source "
               "../autotest_env.sh;");
        system("cp -f $GMDB_HOME/test/dt/st/experimental_tsdb/ts/gmserver_ts.ini /usr/local/file/gmserver.ini");
        system(
            "sh start.sh;taskset -c 0 start.sh;cd $GMDB_HOME/test/dt/st/experimental_tsdb/ts_perf_nlog/;sh build.sh;");
        system("cd $GMDB_HOME/test/dt/st/experimental_tsdb/ts_perf_nlog/build/;"
               "./benchmark-gmdb-ddl -file=./schema/T_TRF_ORG_gmdb.sql >> "
               "$GMDB_HOME/test/sdv/testcases/25_Timing/006_tsdb_query/mylog.txt;"
               "./nlog_batch_insert_T_TRF_ORG_gmdb 1000000 50000 1704038400 >> "
               "$GMDB_HOME/test/sdv/testcases/25_Timing/006_tsdb_query/mylog.txt");
    }
    // tableTypeFlag=2表示建表为T_THRT_ORG
    else {
        system("cd $GMDB_HOME;source scripts/env_aarch64.sh;cd $GMDB_HOME/test/sdv/tools/;./deploy.sh euler;source "
               "../autotest_env.sh;");
        system("cp -f $GMDB_HOME/test/dt/st/experimental_tsdb/ts/gmserver_ts.ini /usr/local/file/gmserver.ini");
        system(
            "sh start.sh;taskset -c 0 start.sh;cd $GMDB_HOME/test/dt/st/experimental_tsdb/ts_perf_nlog/;sh build.sh;");
        system("cd $GMDB_HOME/test/dt/st/experimental_tsdb/ts_perf_nlog/build/;"
               "./benchmark-gmdb-ddl -file=./schema/T_THRT_ORG_gmdb.sql >> "
               "$GMDB_HOME/test/sdv/testcases/25_Timing/006_tsdb_query/mylog.txt;"
               "./nlog_batch_insert_T_THRT_ORG_gmdb 1000000 50000 1704038400 >> "
               "$GMDB_HOME/test/sdv/testcases/25_Timing/006_tsdb_query/mylog.txt");
    }
}

int getFileDataCount()
{
    FILE *file = fopen("data.csv", "r");
    int rowCount = 0;
    char rows[1000];
    while (true) {
        if (fgets(rows, 1000, file) != NULL) {
            rowCount++;
            continue;
        }
        break;
    }
    fclose(file);
    return rowCount;
}

// 原始流量表T_REPORT报表查询1 预期：功能正常
TEST_F(TsdbBatchReadFirewall, TsdbBatchReadFirewall_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int tableTypeFlag = 0;
    FirewallDataBulkInsert(tableTypeFlag);

    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&conn, &stmt, 0);

    char sqlCmd[4096] = {0};
    sprintf(sqlCmd,
        "COPY (SELECT SUM(send_byte), src_ip, vsys_id, count(session_num), SUM(session_num) FROM T_REPORT WHERE "
        "log_time "
        "> 1704038405 and log_time < 1704038505 GROUP BY src_ip, vsys_id order by SUM(send_byte) DESC LIMIT 10000) TO"
        "'%s/test/sdv/testcases/25_Timing/002_tsdb_batch_read/data.csv';",
        dir);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int rowCount = getFileDataCount();
    AW_MACRO_ASSERT_EQ_INT(10000, rowCount);
    ret = DropCmTable(tabelName_T_REPORT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 原始流量表T_REPORT报表查询2 预期：功能正常
TEST_F(TsdbBatchReadFirewall, TsdbBatchReadFirewall_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int tableTypeFlag = 0;
    FirewallDataBulkInsert(tableTypeFlag);

    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&conn, &stmt, 0);

    char sqlCmd[4096] = {0};
    sprintf(sqlCmd,
        "COPY (select SUM(send_byte), dst_ip, vsys_id, count(session_num), SUM(receive_byte) "
        "from T_REPORT WHERE log_time > 1704038440 AND log_time < 1704038640 AND vsys_id = 1 "
        "GROUP BY dst_ip, vsys_id order by SUM(send_byte) DESC LIMIT 10000) TO"
        "'%s/test/sdv/testcases/25_Timing/002_tsdb_batch_read/data.csv';",
        dir);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int rowCount = getFileDataCount();
    AW_MACRO_ASSERT_EQ_INT(10000, rowCount);

    ret = DropCmTable(tabelName_T_REPORT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 原始流量表T_REPORT报表查询3 预期：功能正常
TEST_F(TsdbBatchReadFirewall, TsdbBatchReadFirewall_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int tableTypeFlag = 0;
    FirewallDataBulkInsert(tableTypeFlag);

    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&conn, &stmt, 0);

    char sqlCmd[4096] = {0};
    sprintf(sqlCmd,
        "COPY (Select log_time, vsys_id, ip_version, src_ip, src_loc_id, src_country_id, session_num, "
        "send_byte, receive_byte from T_REPORT where (log_time > 1704038423 AND log_time < 1704038625 AND vsys_id = 1) "
        "AND "
        "(src_ip = 3595561408 OR src_ip = 2673160948 OR src_ip = 1403293744 "
        "OR src_ip = 190510588 OR src_ip = 401525596 OR src_ip = 2858415436 OR src_ip = 1468026928 OR "
        "src_ip = 2907154972 OR src_ip = 3267612604 OR src_ip = 2907154972)) TO"
        "'%s/test/sdv/testcases/25_Timing/002_tsdb_batch_read/data.csv';",
        dir);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int rowCount = getFileDataCount();
    AW_MACRO_ASSERT_EQ_INT(10838, rowCount);
    ret = DropCmTable(tabelName_T_REPORT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 原始流量表T_TRF_ORG日志查询1 预期：功能正常
TEST_F(TsdbBatchReadFirewall, TsdbBatchReadFirewall_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int tableTypeFlag = 1;
    FirewallDataBulkInsert(tableTypeFlag);

    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&conn, &stmt, 0);

    char sqlCmd[4096] = {0};
    sprintf(sqlCmd,
        "COPY (SELECT end_time, * FROM T_TRF_ORG WHERE vsys_id = 1 AND end_time > 1704038400 AND end_time < "
        "1704038525 order by end_time DESC LIMIT 10000) TO"
        "'%s/test/sdv/testcases/25_Timing/002_tsdb_batch_read/data.csv';",
        dir);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int rowCount = getFileDataCount();
    AW_MACRO_ASSERT_EQ_INT(10000, rowCount);
    ret = DropCmTable(tabelName_T_TRF_ORG);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 原始流量表T_TRF_ORG日志查询2 预期：功能正常
TEST_F(TsdbBatchReadFirewall, TsdbBatchReadFirewall_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int tableTypeFlag = 1;
    FirewallDataBulkInsert(tableTypeFlag);

    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&conn, &stmt, 0);

    char sqlCmd[4096] = {0};
    sprintf(sqlCmd,
        "COPY (SELECT end_time, * FROM T_TRF_ORG WHERE vsys_id = 1 AND src_ip = 2907154972 AND end_time >= 0 AND "
        "end_time <= 1704038650"
        " order by end_time ASC limit 10000) TO"
        "'%s/test/sdv/testcases/25_Timing/002_tsdb_batch_read/data.csv';",
        dir);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int rowCount = getFileDataCount();
    AW_MACRO_ASSERT_EQ_INT(1897, rowCount);
    ret = DropCmTable(tabelName_T_TRF_ORG);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 原始流量表T_TRF_ORG日志查询3 预期：功能正常
TEST_F(TsdbBatchReadFirewall, TsdbBatchReadFirewall_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int tableTypeFlag = 1;
    FirewallDataBulkInsert(tableTypeFlag);

    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&conn, &stmt, 0);

    char sqlCmd[4096] = {0};
    sprintf(sqlCmd,
        "COPY (SELECT end_time, * FROM T_TRF_ORG WHERE vsys_id = 1 AND src_port = 54361 AND end_time > 0 AND "
        "end_time < 1704038650 order by end_time DESC limit 10000) TO"
        "'%s/test/sdv/testcases/25_Timing/002_tsdb_batch_read/data.csv';",
        dir);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int rowCount = getFileDataCount();
    AW_MACRO_ASSERT_EQ_INT(8, rowCount);
    ret = DropCmTable(tabelName_T_TRF_ORG);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 原始威胁表T_THRT_ORG日志查询 预期：功能正常
TEST_F(TsdbBatchReadFirewall, TsdbBatchReadFirewall_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int tableTypeFlag = 2;
    FirewallDataBulkInsert(tableTypeFlag);

    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&conn, &stmt, 0);

    char sqlCmd[4096] = {0};
    sprintf(sqlCmd,
        "COPY (SELECT log_time, * FROM T_THRT_ORG WHERE vsys_id = 1 AND log_time > 1704038402 AND log_time < "
        "1704038528 ORDER BY log_time DESC LIMIT 10000) TO"
        "'%s/test/sdv/testcases/25_Timing/002_tsdb_batch_read/data.csv';",
        dir);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int rowCount = getFileDataCount();
    AW_MACRO_ASSERT_EQ_INT(10000, rowCount);
    ret = DropCmTable(tabelName_T_THRT_ORG);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 原始威胁表T_THRT_ORG日志汇聚 预期：功能正常
TEST_F(TsdbBatchReadFirewall, TsdbBatchReadFirewall_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int tableTypeFlag = 2;
    FirewallDataBulkInsert(tableTypeFlag);

    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&conn, &stmt, 0);

    char sqlCmd[4096] = {0};
    sprintf(sqlCmd,
        "COPY (SELECT SUM(event_num), vsys_id, app_id, user_id, attacker_type, vctmer_type, "
        "threat_type, thrt_id, thrtname_id, sec_policy_id, attacker, vctmer, SUM(packet_num), MAX(max_speed) "
        "FROM T_THRT_ORG WHERE log_time >= 1704038405 AND log_time <= 1704038550 AND vsys_id = 1 GROUP BY "
        "vsys_id, app_id, user_id, attacker_type, vctmer_type, threat_type, thrt_id, thrtname_id, sec_policy_id, "
        "attacker, vctmer order by SUM(event_num) DESC limit 500) TO"
        "'%s/test/sdv/testcases/25_Timing/002_tsdb_batch_read/data.csv';",
        dir);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int rowCount = getFileDataCount();
    AW_MACRO_ASSERT_EQ_INT(500, rowCount);
    ret = DropCmTable(tabelName_T_THRT_ORG);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// COPY TO执行原始威胁表T_THRT_ORG定长字符串order by日志子句查询 预期：功能正常
TEST_F(TsdbBatchReadFirewall, TsdbBatchReadFirewall_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int tableTypeFlag = 2;
    FirewallDataBulkInsert(tableTypeFlag);

    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&conn, &stmt, 0);

    char sqlCmd[4096] = {0};
    sprintf(sqlCmd,
        "COPY (SELECT log_time, * FROM T_THRT_ORG WHERE vsys_id = 1 AND log_time > 1704038402 AND log_time < "
        "1704038528 ORDER BY log_time DESC LIMIT 10000) TO"
        "'%s/test/sdv/testcases/25_Timing/002_tsdb_batch_read/data.csv';",
        dir);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int rowCount = getFileDataCount();
    AW_MACRO_ASSERT_EQ_INT(10000, rowCount);
    ret = DropCmTable(tabelName_T_THRT_ORG);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// COPY TO执行原始威胁表T_THRT_ORG定长字符串group by日志子句查询 预期：功能正常
TEST_F(TsdbBatchReadFirewall, TsdbBatchReadFirewall_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int tableTypeFlag = 2;
    FirewallDataBulkInsert(tableTypeFlag);

    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&conn, &stmt, 0);

    char sqlCmd[4096] = {0};
    sprintf(sqlCmd,
        "COPY (SELECT packet_id, SUM(log_time) FROM T_THRT_ORG GROUP BY packet_id LIMIT 10000) TO"
        "'%s/test/sdv/testcases/25_Timing/002_tsdb_batch_read/data.csv';",
        dir);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int rowCount = getFileDataCount();
    AW_MACRO_ASSERT_EQ_INT(1, rowCount);
    ret = DropCmTable(tabelName_T_THRT_ORG);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// COPY TO执行原始威胁表T_THRT_ORG定长字符串作为聚合函数中参数列日志子句查询 预期：功能正常
TEST_F(TsdbBatchReadFirewall, TsdbBatchReadFirewall_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int tableTypeFlag = 2;
    FirewallDataBulkInsert(tableTypeFlag);

    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&conn, &stmt, 0);

    char sqlCmd[4096] = {0};
    sprintf(sqlCmd,
        "COPY (SELECT COUNT(domain_name), log_time FROM T_THRT_ORG GROUP BY log_time order by"
        " COUNT(domain_name) DESC LIMIT 10000) TO"
        "'%s/test/sdv/testcases/25_Timing/002_tsdb_batch_read/data.csv';",
        dir);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int rowCount = getFileDataCount();
    AW_MACRO_ASSERT_EQ_INT(242, rowCount);
    ret = DropCmTable(tabelName_T_THRT_ORG);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// COPY TO执行原始威胁表T_THRT_ORG多列定长字符串列作为分组多参数日志子句查询 预期：功能正常
TEST_F(TsdbBatchReadFirewall, TsdbBatchReadFirewall_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int tableTypeFlag = 2;
    FirewallDataBulkInsert(tableTypeFlag);

    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&conn, &stmt, 0);

    char sqlCmd[4096] = {0};
    sprintf(sqlCmd,
        "COPY (SELECT COUNT(log_time), domain_name, packet_id FROM T_THRT_ORG GROUP BY "
        "domain_name, packet_id order by COUNT(log_time) DESC LIMIT 10000) TO"
        "'%s/test/sdv/testcases/25_Timing/002_tsdb_batch_read/data.csv';",
        dir);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int rowCount = getFileDataCount();
    AW_MACRO_ASSERT_EQ_INT(1, rowCount);
    ret = DropCmTable(tabelName_T_THRT_ORG);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// COPY TO执行原始威胁表T_THRT_ORG定长字符串和整型作为分组多参数日志子句查询 预期：功能正常
TEST_F(TsdbBatchReadFirewall, TsdbBatchReadFirewall_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int tableTypeFlag = 2;
    FirewallDataBulkInsert(tableTypeFlag);

    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&conn, &stmt, 0);

    char sqlCmd[4096] = {0};
    sprintf(sqlCmd,
        "COPY (SELECT MAX(log_time), vsys_id, app_id, user_id, attacker_type, domain_name FROM T_THRT_ORG GROUP BY"
    " vsys_id, app_id, user_id, attacker_type, domain_name order by MAX(log_time) DESC LIMIT 500) TO"
        "'%s/test/sdv/testcases/25_Timing/002_tsdb_batch_read/data.csv';",
        dir);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int rowCount = getFileDataCount();
    AW_MACRO_ASSERT_EQ_INT(500, rowCount);
    ret = DropCmTable(tabelName_T_THRT_ORG);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
