/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: 【交付增强】列存可靠性增强
 * Author: chenbangjun
 * Create: 2024-08-31
 */
#include "gtest/gtest.h"
#include "t_rd_ts.h"
#include "tsdb_colstorage_reliability.h"

static GmcConnOptionsT *connOptions = NULL;
static GmcConnT *conn = NULL;
static GmcStmtT *stmt = NULL;
int64_t dataSizeBefore = 0;
int64_t dataSizeAfter = 0;
char g_cStoreDir[64] = {0};

class TsdbColstorageReliabilityBasic : public testing::Test {
protected:
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}

public:
    virtual void SetUp();
    virtual void TearDown();
};

void TsdbColstorageReliabilityBasic::SetUp()
{
    // 清理内容放到setup里面避免构建上连跑时受影响
    InitTsCiCfg();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建epoll监听线程
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
    ret =
        TestGetResultCommand("cat ../common/logicTablePath.txt | tr -d '$\r'", NULL, g_cStoreDir, sizeof(g_cStoreDir));
    EXPECT_EQ(0, ret);
}

void TsdbColstorageReliabilityBasic::TearDown()
{
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_CHECK_LOG_END();
    GmcConnOptionsDestroy(connOptions);
    GmcFreeStmt(stmt);
    GmcDisconnect(conn);
    testEnvClean();
    RecoverTsCiCfg();
}

int64_t GetDirSize(const char *dirPath)
{
    struct stat st;
    int64_t size = 0;
    DIR *dir;
    struct dirent *entry;

    if ((dir = opendir(dirPath)) == NULL) {
        perror("opendir");
        return T_FAILED;
    }

    while ((entry = readdir(dir)) != NULL) {
        char fullPath[1024];
        (void)snprintf(fullPath, sizeof(fullPath), "%s/%s", dirPath, entry->d_name);
        if (stat(fullPath, &st) == -1) {
            perror("stat");
            return T_FAILED;
        }

        if (S_ISDIR(st.st_mode)) {
            if (strcmp(entry->d_name, ".") == 0 || strcmp(entry->d_name, "..") == 0) {
                continue;
            }
            size += GetDirSize(fullPath);
        } else {
            size += st.st_size;
        }
    }

    int64_t ret = closedir(dir);
    if (ret != T_OK) {
        return ret;
    }
    return size;
}

// 时区标志
int TimeZoneFlag()
{
    int ret = 0;
    bool isTrue = false;
    char sqlCmd[512] = {0};
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(sqlCmd, "date");
    char resStr[30000] = {0};
    FILE *pResultStr = NULL;
    pResultStr = popen(sqlCmd, "r");
    fread(resStr, 1, sizeof(resStr), pResultStr);
    isTrue = strstr(resStr, "CST") != NULL;
    pclose(pResultStr);
    if (isTrue == true) {
        ret = 1;
    } else {
        ret = 0;
    }
    return ret;
}

// 001.建表时设置IsCStoreHighReliable为0 预期：建表失败
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    (void)DropTable(stmt, g_tableName);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNDEFINED_TABLE);
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(160))"
        " with (time_col = 'time', interval = '1 hour', compression = 'fast(rapidlz)', IsCStoreHighReliable = 0 );",
        g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_JSON_CONTENT, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_INVALID_JSON_CONTENT);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002.建表时设置IsCStoreHighReliable为1 预期：建表失败
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    (void)DropTable(stmt, g_tableName);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNDEFINED_TABLE);
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(160))"
        " with (time_col = 'time', interval = '1 hour', compression = 'fast(rapidlz)', IsCStoreHighReliable = 1 );",
        g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_JSON_CONTENT, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_INVALID_JSON_CONTENT);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003.并发注入顺序数据 预期：建表失败
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int threadCount = 2;
    pthread_t tid[threadCount];
    GmcConnT *conn_1 = NULL;
    GmcStmtT *stmt_1 = NULL;
    ret = TestTsGmcConnect(&conn_1, &stmt_1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    static GmcConnT *conn_2 = NULL;
    static GmcStmtT *stmt_2 = NULL;
    ret = TestTsGmcConnect(&conn_2, &stmt_2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t count = 100000;
    int64_t partitioningTime = 1704068200;
    int64_t partitioningNum = 1;  // 构造同分区数据
    int64_t addTime = 0;          // 构造顺序数据
    ConstructDataType constructDataType_1 = {stmt_1, 1, partitioningTime, partitioningNum, addTime, count, 10};
    ConstructDataType constructDataType_2 = {stmt_2, 1, partitioningTime, partitioningNum, addTime, count, 10};
    pthread_create(&tid[0], NULL, InsertDataToTable, &constructDataType_1);
    pthread_create(&tid[1], NULL, InsertDataToTable, &constructDataType_2);

    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);

    ret = testGmcDisconnect(conn_1, stmt_1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn_2, stmt_2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004.并发注入乱序数据 预期：建表失败
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int threadCount = 2;
    pthread_t tid[threadCount];
    GmcConnT *conn_1 = NULL;
    GmcStmtT *stmt_1 = NULL;
    ret = TestTsGmcConnect(&conn_1, &stmt_1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    static GmcConnT *conn_2 = NULL;
    static GmcStmtT *stmt_2 = NULL;
    ret = TestTsGmcConnect(&conn_2, &stmt_2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t count = 100000;
    int64_t partitioningTime = 1704068200;
    int64_t partitioningNum = 1;  // 构造同分区数据
    int64_t addTime = 900;        // 构造乱序数据
    ConstructDataType constructDataType_1 = {stmt_1, 1, partitioningTime, partitioningNum, addTime, count, 10};
    ConstructDataType constructDataType_2 = {stmt_2, 1, partitioningTime, partitioningNum, addTime, count, 10};
    pthread_create(&tid[0], NULL, InsertDataToTable, &constructDataType_1);
    pthread_create(&tid[1], NULL, InsertDataToTable, &constructDataType_2);

    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);

    ret = testGmcDisconnect(conn_1, stmt_1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn_2, stmt_2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005.对空表注入同一分区有序数据时，服务端异常重启 预期：重启前后磁盘占用相同
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int threadCount = 1;
    pthread_t tid[threadCount];
    int64_t count = 100000;
    int64_t partitioningTime = 1704068200;
    int64_t partitioningNum = 1;  // 构造同分区数据
    int64_t addTime = 0;          // 构造顺序数据
    GmcConnT *conn_1 = NULL;
    GmcStmtT *stmt_1 = NULL;
    ret = TestTsGmcConnect(&conn_1, &stmt_1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    insertTimes = 0;
    ConstructDataType constructDataType_1 = {stmt_1, 1, partitioningTime, partitioningNum, addTime, count, 100};
    ret = pthread_create(&tid[0], NULL, InsertDataToTable, &constructDataType_1);
    sleep(2);
    system("pkill gmserver");
    pthread_join(tid[0], NULL);
    ret = GMKill(&conn, &stmt);
    dataSizeBefore = GetDirSize(g_cStoreDir);
    // 删表后重新建表
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ConstructDataType constructDataType_2 = {stmt, 1, partitioningTime, partitioningNum, addTime, count, insertTimes};
    ret = pthread_create(&tid[0], NULL, InsertDataToTable, &constructDataType_2);
    pthread_join(tid[0], NULL);
    dataSizeAfter = GetDirSize(g_cStoreDir);
    // 可能存在最后一批数据的事务提交了但是在返回应答时异常重启,导致前后插入总数据不一致，需要再次插入一批数据
    if (dataSizeBefore > dataSizeAfter) {
        ConstructDataType constructDataType_3 = {stmt, 1, partitioningTime, partitioningNum, addTime, count, 1};
        ret = pthread_create(&tid[0], NULL, InsertDataToTable, &constructDataType_3);
        pthread_join(tid[0], NULL);
        dataSizeAfter = GetDirSize(g_cStoreDir);
    }
    AW_MACRO_ASSERT_EQ_INT(dataSizeBefore, dataSizeAfter);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_CONNECTION_RESET_BY_PEER);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006.对空表注入同一分区乱序数据时，服务端异常重启 预期：重启前后磁盘占用相同
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int threadCount = 1;
    pthread_t tid[threadCount];
    int64_t count = 100000;
    int64_t partitioningTime = 1704068200;
    int64_t partitioningNum = 1;  // 构造同分区数据
    int64_t addTime = 900;        // 构造乱序数据
    GmcConnT *conn_1 = NULL;
    GmcStmtT *stmt_1 = NULL;
    ret = TestTsGmcConnect(&conn_1, &stmt_1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    insertTimes = 0;
    ConstructDataType constructDataType_1 = {stmt_1, 1, partitioningTime, partitioningNum, addTime, count, 100};
    ret = pthread_create(&tid[0], NULL, InsertDataToTable, &constructDataType_1);
    sleep(2);
    system("pkill gmserver");
    pthread_join(tid[0], NULL);
    ret = GMKill(&conn, &stmt);
    dataSizeBefore = GetDirSize(g_cStoreDir);
    // 删表后重新建表
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ConstructDataType constructDataType_2 = {stmt, 1, partitioningTime, partitioningNum, addTime, count, insertTimes};
    ret = pthread_create(&tid[0], NULL, InsertDataToTable, &constructDataType_2);
    pthread_join(tid[0], NULL);
    dataSizeAfter = GetDirSize(g_cStoreDir);
    // 可能存在最后一批数据的事务提交了但是在返回应答时异常重启,导致前后插入总数据不一致，需要再次插入一批数据
    if (dataSizeBefore > dataSizeAfter) {
        ConstructDataType constructDataType_3 = {stmt, 1, partitioningTime, partitioningNum, addTime, count, 1};
        ret = pthread_create(&tid[0], NULL, InsertDataToTable, &constructDataType_3);
        pthread_join(tid[0], NULL);
        dataSizeAfter = GetDirSize(g_cStoreDir);
    }
    AW_MACRO_ASSERT_EQ_INT(dataSizeBefore, dataSizeAfter);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_CONNECTION_RESET_BY_PEER);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007.对非空表注入同一分区有序数据时，服务端异常重启 预期：重启前后磁盘占用相同
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t time = 170406;
    ret = rowInsertData(stmt, g_tableName, time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int threadCount = 1;
    pthread_t tid[threadCount];
    int64_t count = 100000;
    int64_t partitioningTime = 1704068200;
    int64_t partitioningNum = 1;  // 构造同分区数据
    int64_t addTime = 0;          // 构造顺序数据
    GmcConnT *conn_1 = NULL;
    GmcStmtT *stmt_1 = NULL;
    ret = TestTsGmcConnect(&conn_1, &stmt_1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    insertTimes = 0;
    ConstructDataType constructDataType_1 = {stmt_1, 1, partitioningTime, partitioningNum, addTime, count, 10};
    ret = pthread_create(&tid[0], NULL, InsertDataToTable, &constructDataType_1);
    system("pkill gmserver");
    pthread_join(tid[0], NULL);
    ret = GMKill(&conn, &stmt);
    dataSizeBefore = GetDirSize(g_cStoreDir);
    // 删表后重新建表
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 插入相同数据
    ret = rowInsertData(stmt, g_tableName, time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ConstructDataType constructDataType_2 = {stmt, 1, partitioningTime, partitioningNum, addTime, count, insertTimes};
    ret = pthread_create(&tid[0], NULL, InsertDataToTable, &constructDataType_2);
    pthread_join(tid[0], NULL);
    dataSizeAfter = GetDirSize(g_cStoreDir);
    // 可能存在最后一批数据的事务提交了但是在返回应答时异常重启,导致前后插入总数据不一致，需要再次插入一批数据
    if (dataSizeBefore > dataSizeAfter) {
        ConstructDataType constructDataType_3 = {stmt, 1, partitioningTime, partitioningNum, addTime, count, 1};
        ret = pthread_create(&tid[0], NULL, InsertDataToTable, &constructDataType_3);
        pthread_join(tid[0], NULL);
        dataSizeAfter = GetDirSize(g_cStoreDir);
    }
    // 重启前后磁盘占用相同
    AW_MACRO_ASSERT_EQ_INT(dataSizeBefore, dataSizeAfter);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_CONNECTION_RESET_BY_PEER);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008.对非空表注入同一分区乱序数据时，服务端异常重启 预期：重启前后磁盘占用相同
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t time = 170406;
    ret = rowInsertData(stmt, g_tableName, time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int threadCount = 1;
    pthread_t tid[threadCount];
    int64_t count = 100000;
    int64_t partitioningTime = 1704068200;
    int64_t partitioningNum = 1;  // 构造同分区数据
    int64_t addTime = 900;        // 构造乱序数据
    GmcConnT *conn_1 = NULL;
    GmcStmtT *stmt_1 = NULL;
    ret = TestTsGmcConnect(&conn_1, &stmt_1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    insertTimes = 0;
    ConstructDataType constructDataType_1 = {stmt_1, 1, partitioningTime, partitioningNum, addTime, count, 10};
    ret = pthread_create(&tid[0], NULL, InsertDataToTable, &constructDataType_1);
    system("pkill gmserver");
    pthread_join(tid[0], NULL);
    ret = GMKill(&conn, &stmt);
    dataSizeBefore = GetDirSize(g_cStoreDir);
    // 删表后重新建表
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 插入相同数据
    ret = rowInsertData(stmt, g_tableName, time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ConstructDataType constructDataType_2 = {stmt, 1, partitioningTime, partitioningNum, addTime, count, insertTimes};
    ret = pthread_create(&tid[0], NULL, InsertDataToTable, &constructDataType_2);
    pthread_join(tid[0], NULL);
    dataSizeAfter = GetDirSize(g_cStoreDir);
    // 可能存在最后一批数据的事务提交了但是在返回应答时异常重启,导致前后插入总数据不一致，需要再次插入一批数据
    if (dataSizeBefore > dataSizeAfter) {
        ConstructDataType constructDataType_3 = {stmt, 1, partitioningTime, partitioningNum, addTime, count, 1};
        ret = pthread_create(&tid[0], NULL, InsertDataToTable, &constructDataType_3);
        pthread_join(tid[0], NULL);
        dataSizeAfter = GetDirSize(g_cStoreDir);
    }
    // 重启前后磁盘占用相同
    AW_MACRO_ASSERT_EQ_INT(dataSizeBefore, dataSizeAfter);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_CONNECTION_RESET_BY_PEER);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 009.对非空表注入跨分区有序数据时，服务端异常重启 预期：重启前磁盘占用小于等于重启后磁盘占用
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t time = 170406;
    ret = rowInsertData(stmt, g_tableName, time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int threadCount = 1;
    pthread_t tid[threadCount];
    int64_t count = 100000;
    int64_t partitioningTime = 1704068200;
    int64_t partitioningNum = 100;  // 构造跨分区数据
    int64_t addTime = 0;            // 构造顺序数据
    GmcConnT *conn_1 = NULL;
    GmcStmtT *stmt_1 = NULL;
    ret = TestTsGmcConnect(&conn_1, &stmt_1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    insertTimes = 0;
    ConstructDataType constructDataType_1 = {stmt_1, 1, partitioningTime, partitioningNum, addTime, count, 20};
    ret = pthread_create(&tid[0], NULL, InsertDataToTable, &constructDataType_1);
    system("pkill gmserver");
    pthread_join(tid[0], NULL);
    ret = GMKill(&conn, &stmt);
    dataSizeBefore = GetDirSize(g_cStoreDir);
    // 删表后重新建表
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 插入相同数据
    ret = rowInsertData(stmt, g_tableName, time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ConstructDataType constructDataType_2 = {
        stmt, 1, partitioningTime, partitioningNum, addTime, count, insertTimes + 1};
    ret = pthread_create(&tid[0], NULL, InsertDataToTable, &constructDataType_2);
    pthread_join(tid[0], NULL);
    dataSizeAfter = GetDirSize(g_cStoreDir);
    // 重启前磁盘占用大于等于重启后磁盘占用
    EXPECT_GE(dataSizeAfter, dataSizeBefore);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_CONNECTION_RESET_BY_PEER);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010.对非空表注入跨分区乱序数据时，服务端异常重启 预期：重启前磁盘占用小于等于重启后磁盘占用
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t time = 170406;
    ret = rowInsertData(stmt, g_tableName, time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int threadCount = 1;
    pthread_t tid[threadCount];
    int64_t count = 100000;
    int64_t partitioningTime = 1704068200;
    int64_t partitioningNum = 100;  // 构造跨分区数据
    int64_t addTime = 900;          // 构造乱序数据
    GmcConnT *conn_1 = NULL;
    GmcStmtT *stmt_1 = NULL;
    ret = TestTsGmcConnect(&conn_1, &stmt_1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    insertTimes = 0;
    ConstructDataType constructDataType_1 = {stmt_1, 1, partitioningTime, partitioningNum, addTime, count, 20};
    ret = pthread_create(&tid[0], NULL, InsertDataToTable, &constructDataType_1);
    system("pkill gmserver");
    pthread_join(tid[0], NULL);
    ret = GMKill(&conn, &stmt);
    dataSizeBefore = GetDirSize(g_cStoreDir);
    // 删表后重新建表
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 插入相同数据
    ret = rowInsertData(stmt, g_tableName, time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ConstructDataType constructDataType_2 = {
        stmt, 1, partitioningTime, partitioningNum, addTime, count, insertTimes + 1};
    ret = pthread_create(&tid[0], NULL, InsertDataToTable, &constructDataType_2);
    pthread_join(tid[0], NULL);
    dataSizeAfter = GetDirSize(g_cStoreDir);
    // 重启前磁盘占用大于等于重启后磁盘占用
    EXPECT_GE(dataSizeAfter, dataSizeBefore);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_CONNECTION_RESET_BY_PEER);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 011.对同一分区进行两次相同的数据注入，第一次构造服务端异常重启 预期：两次注入的磁盘占用量相等
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t time = 170406;
    ret = rowInsertData(stmt, g_tableName, time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int threadCount = 1;
    pthread_t tid[threadCount];
    int64_t count = 100000;
    int64_t partitioningTime = 1704068200;
    int64_t partitioningNum = 1;  // 构造同分区数据
    int64_t addTime = 900;        // 构造乱序数据
    GmcConnT *conn_1 = NULL;
    GmcStmtT *stmt_1 = NULL;
    ret = TestTsGmcConnect(&conn_1, &stmt_1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    insertTimes = 0;
    ConstructDataType constructDataType_1 = {stmt_1, 1, partitioningTime, partitioningNum, addTime, count, 100};
    ret = pthread_create(&tid[0], NULL, InsertDataToTable, &constructDataType_1);
    system("pkill gmserver");
    pthread_join(tid[0], NULL);
    ret = GMKill(&conn, &stmt);
    dataSizeBefore = GetDirSize(g_cStoreDir);
    // 删表后重新建表
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 插入相同数据
    ret = rowInsertData(stmt, g_tableName, time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ConstructDataType constructDataType_2 = {stmt, 1, partitioningTime, partitioningNum, addTime, count, insertTimes};
    ret = pthread_create(&tid[0], NULL, InsertDataToTable, &constructDataType_2);
    pthread_join(tid[0], NULL);
    dataSizeAfter = GetDirSize(g_cStoreDir);
    // 可能存在最后一批数据的事务提交了但是在返回应答时异常重启,导致前后插入总数据不一致，需要再次插入一批数据
    if (dataSizeBefore > dataSizeAfter) {
        ConstructDataType constructDataType_3 = {stmt, 1, partitioningTime, partitioningNum, addTime, count, 1};
        ret = pthread_create(&tid[0], NULL, InsertDataToTable, &constructDataType_3);
        pthread_join(tid[0], NULL);
        dataSizeAfter = GetDirSize(g_cStoreDir);
    }
    // 重启前后磁盘占用相同
    AW_MACRO_ASSERT_EQ_INT(dataSizeBefore, dataSizeAfter);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_CONNECTION_RESET_BY_PEER);
    AW_FUN_Log(LOG_STEP, "test end.");
}

void changeTsLcmCheckPoind()
{
    // 对于NEVER_TTL和TTL_POST_BULK_INSERT这两种情况，需要在TTL之前完成数据注入
    // 对于TTL_DURING_BULK_INSERT，需要在注入过程中执行TTL
    // 因此tsLcmCheckPeriod不能设置过大也不能过小。这里折中取5秒
    // 获取当前的小时数和分钟数，例如当前09:30分在，则修改tsLcmCheckPeriod修改为30分5秒，即1805
    struct tm nowTm = {0};
    time_t curTime0 = (time_t)time(NULL);
    if (curTime0 == 0) {
        printf("get current time failed.\n");
        return;
    }
    (void)DB_LOCAL_TIME_R(&curTime0, &nowTm);
    int period = nowTm.tm_min * 60 + nowTm.tm_sec + 15;
    if (period > 3600) {
        period -= 3600;
        if (period < 3) {
            period += 3;
        }
    }
    char cmd[64] = {0};
    (void)sprintf(cmd, "sh $TEST_HOME/tools/modifyCfg.sh -ts \"tsLcmCheckPeriod=%d\"", period);
    system(cmd);
}

class TsdbColstorageReliabilityTtl : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/stop.sh -ts");
        TsDefulatDbFileClean();
        system("sh $TEST_HOME/tools/modifyCfg.sh -ts recover");
        changeTsLcmCheckPoind();
        system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enableLogFold=0\"");
        system("sh $TEST_HOME/tools/start.sh -ts");
        ret = testEnvInit();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestTsGmcConnect(&conn, &stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        GmcConnOptionsDestroy(connOptions);
        GmcFreeStmt(stmt);
        GmcDisconnect(conn);
        close_epoll_thread();
        testEnvClean();
        RecoverTsCiCfg();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void TsdbColstorageReliabilityTtl::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    ret =
        TestGetResultCommand("cat ../common/logicTablePath.txt | tr -d '$\r'", NULL, g_cStoreDir, sizeof(g_cStoreDir));
    EXPECT_EQ(0, ret);
}

void TsdbColstorageReliabilityTtl::TearDown()
{
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_CHECK_LOG_END();
}

// 012.单个非空表触发TTL时，服务端异常重启 预期：重启后数据没被删除
TEST_F(TsdbColstorageReliabilityTtl, Timing_047_Basic_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTableTtl(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int threadCount = 1;
    pthread_t tid[threadCount];
    int64_t count = 10000;
    int64_t partitioningTime = 0;
    int64_t partitioningNum = 1;  // 构造同分区数据
    int64_t addTime = 900;        // 构造乱序数据
    GmcConnT *conn_1 = NULL;
    GmcStmtT *stmt_1 = NULL;
    ret = TestTsGmcConnect(&conn_1, &stmt_1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    insertTimes = 0;
    ConstructDataType constructDataType_1 = {stmt_1, 1, partitioningTime, partitioningNum, addTime, count, 10};
    ret = pthread_create(&tid[0], NULL, InsertDataToTable, &constructDataType_1);
    pthread_join(tid[0], NULL);
    dataSizeBefore = GetDirSize(g_cStoreDir);
    // 等待后台TTL线程启动
    sleep(6);
    ret = GMKill(&conn, &stmt);
    dataSizeAfter = GetDirSize(g_cStoreDir);
    // TTL触发时机不好把握，该用例改为看护没有core产生
    EXPECT_NE(dataSizeBefore + 1, dataSizeAfter);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_CONNECTION_RESET_BY_PEER);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 013.单个非空表触发disk_limit时，服务端异常重启 预期：重启后数据没被删除
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "create table testdb0(id integer, time integer, name char(64), ip inet, "
                          "message blob(160)) with (time_col = 'time', interval = '1 hour', disk_limit = '10 MB', "
                          "compression = 'fast(rapidlz)');");
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t time = 0;
    ret = rowInsertData(stmt, g_tableName, time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int threadCount = 1;
    pthread_t tid[threadCount];
    int64_t count = 100000;
    int64_t partitioningTime = 0;
    int64_t partitioningNum = 1;  // 构造同分区数据
    int64_t addTime = 0;          // 构造顺序数据
    ConstructDataType constructDataType_1 = {stmt, 1, partitioningTime, partitioningNum, addTime, count, 10};
    ret = pthread_create(&tid[0], NULL, InsertDataToTable, &constructDataType_1);
    sleep(1);
    pthread_join(tid[0], NULL);
    ret = GMKill(&conn, &stmt);
    // 等待disk_limit清理完成
    sleep(7);
    (void)sprintf(sqlCmd, "select * from %s;", g_tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 进行时区判断，不同时区同时间戳对应时间不一致,导致清除数据不一致
    ret = TimeZoneFlag();
    if (ret == 1) {
        // blob类型结构变化占用内存有变化，此处需要调整
        AW_MACRO_ASSERT_EQ_INT(100390, dataCount);
    } else {
        AW_MACRO_ASSERT_EQ_INT(300390, dataCount);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 014.单个非空表执行主动老化函数，服务端异常重启 预期：重启后数据没被删除
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int threadCount = 2;
    pthread_t tid[threadCount];
    int64_t count = 100000;
    int64_t partitioningTime = 0;
    int64_t partitioningNum = 1;  // 构造分区
    int64_t addTime = 0;          // 构造乱序数据
    GmcConnT *conn_1 = NULL;
    GmcStmtT *stmt_1 = NULL;
    ret = TestTsGmcConnect(&conn_1, &stmt_1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcConnT *conn_2 = NULL;
    GmcStmtT *stmt_2 = NULL;
    ret = TestTsGmcConnect(&conn_2, &stmt_2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    insertTimes = 0;
    // 插入数据
    ConstructDataType constructDataType_1 = {stmt_1, 1, partitioningTime, partitioningNum, addTime, count, 100};
    ret = pthread_create(&tid[0], NULL, InsertDataToTable, &constructDataType_1);
    pthread_join(tid[0], NULL);
    // 执行主动老化函数
    TtlDataStruct ttl = {stmt_2, 1, 1};
    ret = pthread_create(&tid[1], NULL, TtlAging, &ttl);
    usleep(200 * 1000);
    system("pkill gmserver");
    pthread_join(tid[0], NULL);
    ret = GMKill(&conn, &stmt);
    // 构造查询语句，通过时间来筛选执行主动老化函数时异常退出的分区
    char sqlCmd[512] = {0};
    int time = 7200 * agingNum;
    (void)sprintf(sqlCmd, "select * from %s where time = %ld ", g_tableName, time);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_GE(10001, dataCount);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_CONNECTION_RESET_BY_PEER);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 015.删除单个非空表，服务端异常重启 预期：重启后数据没被删除
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int threadCount = 2;
    pthread_t tid[threadCount];
    int64_t count = 100000;
    int64_t partitioningTime = 0;
    int64_t partitioningNum = 1;  // 构造分区
    int64_t addTime = 0;          // 构造乱序数据
    GmcConnT *conn_1 = NULL;
    GmcStmtT *stmt_1 = NULL;
    ret = TestTsGmcConnect(&conn_1, &stmt_1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcConnT *conn_2 = NULL;
    GmcStmtT *stmt_2 = NULL;
    ret = TestTsGmcConnect(&conn_2, &stmt_2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    insertTimes = 0;
    // 插入数据
    ConstructDataType constructDataType_1 = {stmt_1, 1, partitioningTime, partitioningNum, addTime, count, 50};
    ret = pthread_create(&tid[0], NULL, InsertDataToTable, &constructDataType_1);
    pthread_join(tid[0], NULL);
    isInsertDataSuccessed = true;
    // 删表
    TtlDataStruct ttl = {stmt_2, 1, 1};
    ret = pthread_create(&tid[1], NULL, CycleDeleteTable, &ttl);
    system("pkill gmserver");
    pthread_join(tid[1], NULL);
    ret = GMKill(&conn, &stmt);
    usleep(200 * 1000);
    // 构造查询语句，通过时间来筛选执行主动老化函数时异常退出的分区
    char sqlCmd[512] = {0};
    // 增加表被删除的判断，避免用例偶现失败
    if (tableNums == 0 && isInsertDataSuccessed == false) {
        (void)sprintf(sqlCmd, "select * from %s;", g_tableName);
        uint32_t cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
        // 增加判断，避免删表完成后，此时重启通信报错导致用例报错
        if (ret != GMERR_UNDEFINED_TABLE) {
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            int64_t dataCount = 0;
            ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            // arm32环境下单个文件不能超过2GB，需要调整数据量
            AW_MACRO_ASSERT_EQ_INT(5000000, dataCount);  // 单表10000条数据
            ret = DropTable(stmt, g_tableName);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_CONNECTION_RESET_BY_PEER);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 016.多个非空表触发TTL时，服务端异常重启 预期：重启后数据没被删除
TEST_F(TsdbColstorageReliabilityTtl, Timing_047_Basic_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int tableCount = 10;
    ret = CreateTableTtl(stmt, tableCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int threadCount = 1;
    pthread_t tid[threadCount];
    int64_t count = 100000;
    int64_t partitioningTime = 0;
    int64_t partitioningNum = 1;  // 构造同分区数据
    int64_t addTime = 0;          // 构造顺序数据
    ConstructDataType constructDataType_1 = {stmt, tableCount, partitioningTime, partitioningNum, addTime, count, 10};
    ret = pthread_create(&tid[0], NULL, InsertDataToTable, &constructDataType_1);
    pthread_join(tid[0], NULL);
    ret = GMKill(&conn, &stmt);
    char sqlCmd[512] = {0};
    // 查询触发到第几张表
    (void)sprintf(sqlCmd, "select * from testdb%ld", tableNums);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_GE(10000001, dataCount);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 017.多个非空表触发disk_limit时，服务端异常重启 预期：重启后数据没被删除
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    int tableCount = 10;
    uint32_t cmdLen = 0;
    for (int i = 0; i < tableCount; i++) {
        (void)sprintf(sqlCmd,
            "create table testdb%ld(id integer, time integer, name char(64), ip inet, "
            "message blob(160)) with (time_col = 'time', interval = '1 hour', disk_limit = '10 MB', "
            "compression = 'fast(rapidlz)');",
            i);
        cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    int threadCount = 1;
    pthread_t tid[threadCount];
    int64_t count = 100000;
    int64_t partitioningTime = 0;
    int64_t partitioningNum = 1;  // 构造同分区数据
    int64_t addTime = 0;          // 构造顺序数据
    ConstructDataType constructDataType_1 = {stmt, tableCount, partitioningTime, partitioningNum, addTime, count, 10};
    ret = pthread_create(&tid[0], NULL, InsertDataToTable, &constructDataType_1);
    pthread_join(tid[0], NULL);
    ret = GMKill(&conn, &stmt);
    for (int i = 0; i < tableCount; i++) {
        sleep(3);
        (void)sprintf(sqlCmd, "select * from testdb%ld;", i);
        cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        int64_t dataCount = 0;
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        // 进行时区判断，不同时区同时间戳对应时间不一致,导致清除数据不一致
        ret = TimeZoneFlag();
        if (ret == 1) {
            AW_MACRO_ASSERT_EQ_INT(100000, dataCount);
        } else {
            AW_MACRO_ASSERT_EQ_INT(300000, dataCount);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 018.多个非空表执行主动老化函数，服务端异常重启 预期：重启后数据没被删除
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int tableCount = 10;
    ret = CreateTable(stmt, tableCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int threadCount = 2;
    pthread_t tid[threadCount];
    int64_t count = 100000;
    int64_t partitioningTime = 0;
    int64_t partitioningNum = 100;  // 构造分区
    int64_t addTime = 0;            // 构造乱序数据
    GmcConnT *conn_1 = NULL;
    GmcStmtT *stmt_1 = NULL;
    ret = TestTsGmcConnect(&conn_1, &stmt_1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcConnT *conn_2 = NULL;
    GmcStmtT *stmt_2 = NULL;
    ret = TestTsGmcConnect(&conn_2, &stmt_2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    insertTimes = 0;
    // 插入数据
    ConstructDataType constructDataType_1 = {stmt_1, tableCount, partitioningTime, partitioningNum, addTime, count, 10};
    ret = pthread_create(&tid[0], NULL, InsertDataToTable, &constructDataType_1);
    pthread_join(tid[0], NULL);
    // 执行主动老化函数
    TtlDataStruct ttl = {stmt_2, 1, 100};
    ret = pthread_create(&tid[1], NULL, TtlAging, &ttl);
    usleep(200 * 1000);
    system("pkill gmserver");
    pthread_join(tid[1], NULL);
    ret = GMKill(&conn, &stmt);
    // 构造查询语句，通过时间来筛选执行主动老化函数时异常退出的分区，预期数据没有被删除
    char sqlCmd[512] = {0};
    int time = 7200 * agingNum;
    (void)sprintf(sqlCmd, "select * from testdb%ld where time = %ld ", tableNums, time);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_GE(10001, dataCount);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_CONNECTION_RESET_BY_PEER);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 019.删除多个非空表，服务端异常重启 预期：重启后数据没被删除
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int tableCount = 10;
    ret = CreateTable(stmt, tableCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int threadCount = 2;
    pthread_t tid[threadCount];
    int64_t count = 100000;
    int64_t partitioningTime = 0;
    int64_t partitioningNum = 1;  // 构造分区
    int64_t addTime = 0;          // 构造乱序数据
    GmcConnT *conn_1 = NULL;
    GmcStmtT *stmt_1 = NULL;
    ret = TestTsGmcConnect(&conn_1, &stmt_1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcConnT *conn_2 = NULL;
    GmcStmtT *stmt_2 = NULL;
    ret = TestTsGmcConnect(&conn_2, &stmt_2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    insertTimes = 0;
    // 插入数据
    ConstructDataType constructDataType_1 = {stmt_1, tableCount, partitioningTime, partitioningNum, addTime, count, 10};
    ret = pthread_create(&tid[0], NULL, InsertDataToTable, &constructDataType_1);
    pthread_join(tid[0], NULL);
    // 删表
    TtlDataStruct ttl = {stmt_2, tableCount, 100};
    ret = pthread_create(&tid[1], NULL, CycleDeleteTable, &ttl);
    system("pkill gmserver");
    pthread_join(tid[1], NULL);
    ret = GMKill(&conn, &stmt);
    // 标志位从函数里前置到用例，避免并发执行后导致标志位变化
    isInsertDataSuccessed = true;
    // 增加校验，避免所有表全部删除完成后查询报错
    if (isInsertDataSuccessed == false) {
        // 构造查询语句，查询触发到第几张表
        char sqlCmd[512] = {0};
        (void)sprintf(sqlCmd, "select * from testdb%ld;", tableNums);
        uint32_t cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        int64_t dataCount = 0;
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(1000000, dataCount);  // 每个分区10000条数据
    }
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_CONNECTION_RESET_BY_PEER);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 020.将非空表的cuMetaFile人为修改损坏，再对该表进行两次查询 预期：第一次查询报错，第二次查询成功
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 构造非空表
    int64_t time = 1704067200;
    ret = rowInsertData(stmt, g_tableName, time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 损坏cuMetaFile
    DamageDataFile(true, 1, false);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "select * from %s", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DATA_CORRUPTION, ret);
    // 第二次查询
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(390, dataCount);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 损坏文件会报CRC校验出错，需要加入白名单
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_DATA_CORRUPTION, GMERR_CRC_CHECK_FAILED);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 021.将非空表的cuMetaFile人为修改损坏，再对该表进行行式注入 预期：数据注入失败后第一次查询成功
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 构造非空表
    int64_t time = 1704067200;
    ret = rowInsertData(stmt, g_tableName, time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 损坏cuMetaFile
    DamageDataFile(true, 1, false);
    // 再次插入数据
    time = 1704068200;
    ret = rowInsertData(stmt, g_tableName, time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DATA_CORRUPTION, ret);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "select * from %s", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(390, dataCount);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_DATA_CORRUPTION, GMERR_CRC_CHECK_FAILED);
    AW_FUN_Log(LOG_STEP, "test end.");
}

typedef struct {
    int id;
    int64_t time;
    char name[64];
    char ip[33];
    uint8_t *blob;
} TableBulk;

// 列式存储方式
int columnInsertData(GmcStmtT *stmt, char *tableName)
{
    int ret = 0;
    int64_t count = 20;
    TableBulk row[count];
    uint8_t message[20][160] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537", "0071 3521",
        "0010 0001", "", "3102 0022", "0000 0001", "9021 6538", "0071 3522", "0010 0002", "0010 0000", "3102 0023",
        "0000 0002", "9021 6539", "0071 3523", "0010 0003"};
    char ips[20][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111", "33333333",
        "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char names[20][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc", "lucy",
        "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    for (int i = 0; i < count; i++) {
        row[i].id = i;
        row[i].time = 1704067200 + i * 2;
        sprintf(row[i].name, "%s", names[i]);
        sprintf(row[i].ip, "%s", ips[i]);
        row[i].blob = message[i];
    }
    uint32_t rowNum = count;
    uint32_t offset = sizeof(TableBulk);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    RETURN_IFERR(ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    RETURN_IFERR(ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_BIND_BY_ROW, &offset, sizeof(uint32_t));
    RETURN_IFERR(ret);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, &row[0].id, sizeof(uint32_t), 0);
    RETURN_IFERR(ret);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, &row[0].time, sizeof(uint32_t), 0);
    RETURN_IFERR(ret);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_FIXED, &row[0].name, sizeof(row[0].name), NULL);
    RETURN_IFERR(ret);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_FIXED, &row[0].ip, sizeof(row[0].ip), NULL);
    RETURN_IFERR(ret);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_BYTES, &row[0].blob, sizeof(uint8_t *), NULL);
    RETURN_IFERR(ret);
    ret = GmcExecute(stmt);
    RETURN_IFERR(ret);
    return ret;
}

// 022.将非空表的cuMetaFile人为修改损坏，再对该表进行列式注入 预期：数据注入失败后第一次查询成功
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 构造非空表
    int64_t time = 1704067200;
    ret = rowInsertData(stmt, g_tableName, time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 损坏cuMetaFile
    DamageDataFile(true, 1, false);
    // 进行列式注入
    ret = columnInsertData(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DATA_CORRUPTION, ret);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "select * from %s", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(390, dataCount);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_DATA_CORRUPTION, GMERR_CRC_CHECK_FAILED);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 023.注入顺序数据,将cuFile内容人为修改损坏（仅一处单字节），再对该表进行两次查询 预期：第一次查询失败，第二次查询成功
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t time = 0;
    ret = rowInsertData(stmt, g_tableName, time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 损坏cuFile
    DamageDataFile(false, 1, false);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "select * from %s", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DATA_CORRUPTION, ret);
    // 再次查询
    int64_t ids[1000] = {0};
    int64_t times[1000] = {0};
    for (int i = 0; i < 500; i++) {
        ids[i] = i + 610;
        times[i] = (i + 610) * 6;
    }
    QueryData data1 = {.ids = ids, .times = times, .names = g_name, .ips = g_ip, .blobs = g_message, .dataCount = 390};
    checkQueryData(stmt, sqlCmd, data1);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_DATA_CORRUPTION, GMERR_CRC_CHECK_FAILED);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 024.注入跳变数据,将cuFile内容人为修改损坏（仅一处单字节），再对该表进行两次查询 预期：第一次查询失败，第二次查询成功
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t time = 0;
    ret = rowInsertOutOfOrderData(stmt, g_tableName, time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 损坏cuFile
    DamageDataFile(false, 1, false);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "select * from %s order by id", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DATA_CORRUPTION, ret);
    // 再次查询
    int64_t ids[1000] = {0};
    int64_t times[1000] = {0};
    // 适配磁盘整理配置项打开，cu逻辑变化
    for (int i = 0; i < 1000; i++) {
        if (i <= 29) {
            ids[i] = 1 + i * 2;
        } else {
            ids[i] = i + 30;
        }
        int j = (ids[i]) % 2;
        times[i] = ids[i] + 7200 * j;
    }
    QueryData data1 = {.ids = ids, .times = times, .names = g_name, .ips = g_ip, .blobs = g_message, .dataCount = 970};
    checkQueryData(stmt, sqlCmd, data1);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_DATA_CORRUPTION, GMERR_CRC_CHECK_FAILED);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 025.注入顺序数据,将cuFile内容人为修改损坏致使空洞率超过阈值 预期：第一次查询失败，第二次查询成功
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t time = 0;
    ret = rowInsertData(stmt, g_tableName, time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 损坏cuFile
    DamageDataFile(false, 1, true);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "select * from %s", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DATA_CORRUPTION, ret);
    // 再次查询
    int64_t ids[1000] = {0};
    int64_t times[1000] = {0};
    for (int i = 0; i < 500; i++) {
        ids[i] = i + 610;
        times[i] = (i + 610) * 6;
    }
    QueryData data1 = {.ids = ids, .times = times, .names = g_name, .ips = g_ip, .blobs = g_message, .dataCount = 390};
    checkQueryData(stmt, sqlCmd, data1);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_DATA_CORRUPTION, GMERR_CRC_CHECK_FAILED);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 026.注入跳变数据,将cuFile内容人为修改损坏致使空洞率超过阈值 预期：第一次查询失败，第二次查询成功
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t time = 0;
    ret = rowInsertOutOfOrderData(stmt, g_tableName, time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 损坏cuFile
    DamageDataFile(false, 1, true);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "select * from %s order by id", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DATA_CORRUPTION, ret);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 再次查询
    int64_t ids[1000] = {0};
    int64_t times[1000] = {0};
    for (int i = 0; i < 500; i++) {
        ids[i] = i * 2 + 1;
        times[i] = (i * 2 + 1) + 7200;
    }
    QueryData data1 = {.ids = ids, .times = times, .names = g_name, .ips = g_ip, .blobs = g_message, .dataCount = 500};
    checkQueryData(stmt, sqlCmd, data1);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_DATA_CORRUPTION, GMERR_CRC_CHECK_FAILED);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 027.注入顺序数据,将cuFile内容人为修改损坏致使空洞率超过阈值，构造服务端异常重启，再对该表进行两次查询
// 预期：第一次查询失败，第二次查询成功
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t time = 0;
    ret = rowInsertData(stmt, g_tableName, time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 损坏cuFile
    DamageDataFile(false, 1, true);
    ret = GMKill(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "select * from %s", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DATA_CORRUPTION, ret);
    // 再次查询
    int64_t ids[1000] = {0};
    int64_t times[1000] = {0};
    for (int i = 0; i < 500; i++) {
        ids[i] = i + 610;
        times[i] = (i + 610) * 6;
    }
    QueryData data1 = {.ids = ids, .times = times, .names = g_name, .ips = g_ip, .blobs = g_message, .dataCount = 390};
    checkQueryData(stmt, sqlCmd, data1);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_DATA_CORRUPTION, GMERR_CRC_CHECK_FAILED);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 028.注入跳变数据,将cuFile内容人为修改损坏致使空洞率超过阈值，构造服务端异常重启，再对该表进行两次查询
// 预期：第一次查询失败，第二次查询成功
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t time = 0;
    ret = rowInsertOutOfOrderData(stmt, g_tableName, time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 损坏cuFile
    DamageDataFile(false, 1, true);
    sleep(1);
    ret = GMKill(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 等待服务启动
    sleep(1);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "select * from %s order by id", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DATA_CORRUPTION, ret);
    // 再次查询
    int64_t ids[1000] = {0};
    int64_t times[1000] = {0};
    for (int i = 0; i < 500; i++) {
        ids[i] = i * 2 + 1;
        times[i] = (i * 2 + 1) + 7200;
    }
    QueryData data1 = {.ids = ids, .times = times, .names = g_name, .ips = g_ip, .blobs = g_message, .dataCount = 500};
    checkQueryData(stmt, sqlCmd, data1);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_DATA_CORRUPTION, GMERR_CRC_CHECK_FAILED);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 029.注入顺序数据,将cuFile内容人为修改损坏（同一分区多处分散）但空洞率不超过阈值 预期：第一次查询失败，第二次查询成功
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t time = 0;
    ret = rowInsertData(stmt, g_tableName, time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 损坏cuFile,同一分区多处分散，但不超过阈值
    DamageDataCuFile(0, 1, false, false);

    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "select * from %s", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DATA_CORRUPTION, ret);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 再次查询
    int64_t ids[1000] = {0};
    int64_t times[1000] = {0};
    for (int i = 0; i < 500; i++) {
        ids[i] = i + 610;
        times[i] = (i + 610) * 6;
    }
    QueryData data1 = {.ids = ids, .times = times, .names = g_name, .ips = g_ip, .blobs = g_message, .dataCount = 390};
    checkQueryData(stmt, sqlCmd, data1);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_DATA_CORRUPTION, GMERR_CRC_CHECK_FAILED);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 030.注入跳变数据,将cuFile内容人为修改损坏（同一分区多处分散）但空洞率不超过阈值 预期：第一次查询失败，第二次查询成功
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t time = 0;
    ret = rowInsertOutOfOrderData(stmt, g_tableName, time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 损坏cuFile,同一分区多处分散，但不超过阈值
    DamageDataCuFile(0, 1, false, false);

    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "select * from %s order by id", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DATA_CORRUPTION, ret);
    // 再次查询
    int64_t ids[1000] = {0};
    int64_t times[1000] = {0};
    // 适配磁盘整理配置项打开，cu逻辑变化
    for (int i = 0; i < 1000; i++) {
        if (i <= 29) {
            ids[i] = 1 + i * 2;
        } else {
            ids[i] = i + 30;
        }
        int j = (ids[i]) % 2;
        times[i] = ids[i] + 7200 * j;
    }
    QueryData data1 = {.ids = ids, .times = times, .names = g_name, .ips = g_ip, .blobs = g_message, .dataCount = 970};
    checkQueryData(stmt, sqlCmd, data1);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_DATA_CORRUPTION, GMERR_CRC_CHECK_FAILED);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 031.注入顺序数据，将cuFile内容人为修改损坏（同一分区多处分散）但空洞率超过阈值 预期：第一次查询失败，第二次查询成功
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t time = 0;
    ret = rowInsertData(stmt, g_tableName, time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 损坏cuFile,同一分区多处分散，超过阈值
    DamageDataCuFile(0, 1, false, true);

    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "select * from %s", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DATA_CORRUPTION, ret);
    // 再次查询
    int64_t ids[1000] = {0};
    int64_t times[1000] = {0};
    for (int i = 0; i < 500; i++) {
        ids[i] = i + 610;
        times[i] = (i + 610) * 6;
    }
    QueryData data1 = {.ids = ids, .times = times, .names = g_name, .ips = g_ip, .blobs = g_message, .dataCount = 390};
    checkQueryData(stmt, sqlCmd, data1);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_DATA_CORRUPTION, GMERR_CRC_CHECK_FAILED);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 032.注入乱序数据，将cuFile内容人为修改损坏（同一分区多处分散）但空洞率超过阈值 预期：第一次查询失败，第二次查询成功
// ==报错1012002
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t time = 0;
    ret = rowInsertOutOfOrderData(stmt, g_tableName, time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 损坏cuFile,同一分区多处分散，超过阈值
    DamageDataCuFile(0, 1, false, true);

    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "select * from %s", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DATA_CORRUPTION, ret);
    // 再次查询
    int64_t ids[1000] = {0};
    int64_t times[1000] = {0};
    for (int i = 0; i < 500; i++) {
        ids[i] = i * 2 + 1;
        times[i] = (i * 2 + 1) + 7200;
    }
    QueryData data1 = {.ids = ids, .times = times, .names = g_name, .ips = g_ip, .blobs = g_message, .dataCount = 500};
    checkQueryData(stmt, sqlCmd, data1);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_DATA_CORRUPTION, GMERR_CRC_CHECK_FAILED);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 033.注入顺序数据，将cuFile内容人为修改损坏（同一分区多处分散）且空洞率超过阈值，构造服务端异常重启，恢复后对该表进行两次查询
// 预期：第一次查询失败，第二次查询成功
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t time = 0;
    ret = rowInsertData(stmt, g_tableName, time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 损坏cuFile,同一分区多处分散，超过阈值
    DamageDataCuFile(0, 1, false, true);
    // 服务端重启
    ret = GMKill(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "select * from %s", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DATA_CORRUPTION, ret);
    // 再次查询
    int64_t ids[1000] = {0};
    int64_t times[1000] = {0};
    for (int i = 0; i < 500; i++) {
        ids[i] = i + 610;
        times[i] = (i + 610) * 6;
    }
    QueryData data1 = {.ids = ids, .times = times, .names = g_name, .ips = g_ip, .blobs = g_message, .dataCount = 390};
    checkQueryData(stmt, sqlCmd, data1);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_DATA_CORRUPTION, GMERR_CRC_CHECK_FAILED);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 034.注入跳变数据，将cuFile内容人为修改损坏（同一分区多处分散）且空洞率超过阈值，构造服务端异常重启，恢复后对该表进行两次查询
// 预期：第一次查询失败，第二次查询成功
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t time = 0;
    ret = rowInsertOutOfOrderData(stmt, g_tableName, time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 损坏cuFile,同一分区多处分散，超过阈值
    DamageDataCuFile(0, 1, false, true);
    // 服务端重启
    ret = GMKill(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "select * from %s", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DATA_CORRUPTION, ret);
    // 再次查询
    int64_t ids[1000] = {0};
    int64_t times[1000] = {0};
    for (int i = 0; i < 500; i++) {
        ids[i] = i * 2 + 1;
        times[i] = (i * 2 + 1) + 7200;
    }
    QueryData data1 = {.ids = ids, .times = times, .names = g_name, .ips = g_ip, .blobs = g_message, .dataCount = 500};
    checkQueryData(stmt, sqlCmd, data1);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_DATA_CORRUPTION, GMERR_CRC_CHECK_FAILED);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 035.注入顺序数据，将cuFile内容人为修改损坏，多个分区空洞率超过阈值，再对该表进行两次查询
// 预期：第一次查询失败，第二次查询成功
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t time = 0;
    ret = rowInsertData(stmt, g_tableName, time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 损坏cuFile,多分区多处分散，超过阈值
    DamageDataCuFile(0, 1, false, true);
    DamageDataCuFile(1, 2, false, true);

    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "select * from %s", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DATA_CORRUPTION, ret);
    // 再次查询
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(0, dataCount);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_DATA_CORRUPTION, GMERR_CRC_CHECK_FAILED);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 036.注入跳变数据，将cuFile内容人为修改损坏，多个分区空洞率超过阈值，再对该表进行两次查询
// 预期：第一次查询失败，第二次查询成功
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t time = 0;
    ret = rowInsertOutOfOrderData(stmt, g_tableName, time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 损坏cuFile,多分区多处分散，超过阈值
    DamageDataCuFile(0, 1, false, true);
    DamageDataCuFile(1, 2, false, true);

    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "select * from %s", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DATA_CORRUPTION, ret);
    // 再次查询
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(0, dataCount);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_DATA_CORRUPTION, GMERR_CRC_CHECK_FAILED);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 037.注入顺序数据，将多个分区的cuFile内容人为修改损坏，某个分区空洞率超过阈值，另一个分区不超过阈值，进行两次查询
// 预期：第一次查询失败，第二次查询成功
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t time = 0;
    ret = rowInsertData(stmt, g_tableName, time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 损坏cuFile,多分区多处分散
    DamageDataCuFile(0, 1, false, true);
    DamageDataCuFile(1, 2, false, false);

    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "select * from %s", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DATA_CORRUPTION, ret);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(0, dataCount);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_DATA_CORRUPTION, GMERR_CRC_CHECK_FAILED);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 038.注入跳变数据，将多个分区的cuFile内容人为修改损坏，某个分区空洞率超过阈值，另一个分区不超过阈值，进行两次查询
// 预期：第一次查询失败，第二次查询成功
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t time = 0;
    ret = rowInsertOutOfOrderData(stmt, g_tableName, time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 损坏cuFile,多分区多处分散
    DamageDataCuFile(0, 1, false, true);
    DamageDataCuFile(1, 2, false, false);

    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "select * from %s", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DATA_CORRUPTION, ret);
    // 查询校验数据
    int64_t ids[1000] = {0};
    int64_t times[1000] = {0};
    for (int i = 30; i < 500; i++) {
        ids[i - 30] = i * 2 + 1;
        times[i - 30] = (i * 2 + 1) + 7200;
    }
    QueryData data1 = {.ids = ids, .times = times, .names = g_name, .ips = g_ip, .blobs = g_message, .dataCount = 470};
    checkQueryData(stmt, sqlCmd, data1);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_DATA_CORRUPTION, GMERR_CRC_CHECK_FAILED);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 039.注入顺序数据，将cuFile内容人为修改损坏使空洞率超过阈值，对该表进行注入，使空洞率不超过阈值，在进行两次查询
// 预期：第一次查询失败，第二次查询成功
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t time = 0;
    ret = rowInsertData(stmt, g_tableName, time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 损坏cuFile
    DamageDataFile(false, 1, true);
    for (int i = 0; i < 50; i++) {
        ret = rowInsertData(stmt, g_tableName, time);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "select * from %s", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DATA_CORRUPTION, ret);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(50390, dataCount);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_DATA_CORRUPTION, GMERR_CRC_CHECK_FAILED);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 040.注入跳变数据，将cuFile内容人为修改损坏使空洞率超过阈值，对该表进行注入，使空洞率不超过阈值，在进行两次查询
// 预期：第一次查询失败，第二次查询成功
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t time = 0;
    ret = rowInsertOutOfOrderData(stmt, g_tableName, time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 损坏cuFile
    DamageDataFile(false, 1, true);
    for (int i = 0; i < 20; i++) {
        ret = rowInsertOutOfOrderData(stmt, g_tableName, time);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "select * from %s", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DATA_CORRUPTION, ret);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 数量需要大于20500，另一分区数据不会全部清除
    EXPECT_GE(dataCount, 20500);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_DATA_CORRUPTION, GMERR_CRC_CHECK_FAILED);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 041.注入顺序数据，将cuFile内容人为修改损坏，某个分区空洞率超过阈值，另一个分区不超过阈值，对超过阈值的分区注入数据，使空洞率不超过阈值，再进行两次查询
// 预期：第一次查询失败，第二次查询成功
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t time = 0;
    ret = rowInsertData(stmt, g_tableName, time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 损坏cuFile,多分区多处分散
    DamageDataCuFile(0, 1, false, true);
    DamageDataCuFile(1, 2, false, false);

    for (int i = 0; i < 50; i++) {
        time = 0;
        ret = rowInsertData(stmt, g_tableName, time);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "select * from %s", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DATA_CORRUPTION, ret);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(50000, dataCount);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_DATA_CORRUPTION, GMERR_CRC_CHECK_FAILED);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 042.注入跳变数据，将cuFile内容人为修改损坏，某个分区空洞率超过阈值，另一个分区不超过阈值，对超过阈值的分区注入数据，使空洞率不超过阈值，再进行两次查询
// 预期：第一次查询失败，第二次查询成功
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 构建上连跑偶现报错，此处删除 /data/gmdb下持久化文件避免连跑时影响
    // 增加循环，验证删表后再建表功能是否正常
    TsDefulatDbFileClean();
    ret = GMKill(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int j = 0; j < 2; j++) {
        ret = CreateTable(stmt, 1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        int64_t time = 0;
        ret = rowInsertOutOfOrderData(stmt, g_tableName, time);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        // 损坏cuFile,多分区多处分散
        DamageDataCuFile(0, 1, false, true);
        DamageDataCuFile(1, 2, true, false);

        for (int i = 0; i < 20; i++) {
            time = 0;
            ret = rowInsertOutOfOrderData(stmt, g_tableName, time);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
        char sqlCmd[512] = {0};
        (void)sprintf(sqlCmd, "select * from %s", g_tableName);
        uint32_t cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
        AW_MACRO_ASSERT_EQ_INT(GMERR_DATA_CORRUPTION, ret);
        ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        int64_t dataCount = 0;
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        // 另一分区数据不会全部清除, 数量需要大于20499但是开启磁盘整理后会丢弃部分数据
        EXPECT_GE(dataCount, 20469);
        ret = DropTable(stmt, g_tableName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_DATA_CORRUPTION, GMERR_CRC_CHECK_FAILED);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 043.注入顺序数据，将cuFile内容人为修改损坏，某个分区空洞率超过阈值，另一个分区不超过阈值，对超过阈值的分区注入数据，此时服务端异常重启，恢复后对该表进行两次查询
// 预期：第一次查询失败，第二次查询成功
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t time = 0;
    ret = rowInsertData(stmt, g_tableName, time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 损坏cuFile,多分区多处分散
    DamageDataCuFile(0, 1, false, true);
    DamageDataCuFile(1, 2, false, false);

    pthread_t tid;
    GmcConnT *conn_1 = NULL;
    GmcStmtT *stmt_1 = NULL;
    ret = TestTsGmcConnect(&conn_1, &stmt_1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t count = 100000;
    int64_t partitioningTime = 1704068200;
    int64_t partitioningNum = 1;  // 构造同分区数据
    int64_t addTime = 0;          // 构造顺序数据
    insertTimes = 0;
    ConstructDataType constructDataType_1 = {stmt_1, 1, partitioningTime, partitioningNum, addTime, count, 10};
    pthread_create(&tid, NULL, InsertDataToTable, &constructDataType_1);
    usleep(750 * 1000);
    system("pkill gmserver");
    pthread_join(tid, NULL);
    ret = GMKill(&conn, &stmt);

    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "select * from %s", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DATA_CORRUPTION, ret);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_GE(dataCount, 0);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(3, GMERR_DATA_CORRUPTION, GMERR_CRC_CHECK_FAILED, GMERR_CONNECTION_RESET_BY_PEER);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 044.注入跳变数据，将cuFile内容人为修改损坏，某个分区空洞率超过阈值，另一个分区不超过阈值，对超过阈值的分区注入数据，此时服务端异常重启，恢复后对该表进行两次查询
// 预期：第一次查询失败，第二次查询成功  =============
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t time = 0;
    ret = rowInsertOutOfOrderData(stmt, g_tableName, time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 损坏cuFile,多分区多处分散
    DamageDataCuFile(0, 1, false, true);
    DamageDataCuFile(1, 2, false, false);

    pthread_t tid;
    GmcConnT *conn_1 = NULL;
    GmcStmtT *stmt_1 = NULL;
    ret = TestTsGmcConnect(&conn_1, &stmt_1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t count = 100000;
    int64_t partitioningTime = 1704068200;
    int64_t partitioningNum = 2;  // 构造跨分区数据
    int64_t addTime = 0;          // 构造顺序数据
    insertTimes = 0;
    ConstructDataType constructDataType_1 = {stmt_1, 1, partitioningTime, partitioningNum, addTime, count, 10};
    pthread_create(&tid, NULL, InsertDataToTable, &constructDataType_1);
    usleep(750 * 1000);
    system("pkill gmserver");
    pthread_join(tid, NULL);
    ret = GMKill(&conn, &stmt);

    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "select * from %s", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DATA_CORRUPTION, ret);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_GE(dataCount, 0);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(3, GMERR_DATA_CORRUPTION, GMERR_CRC_CHECK_FAILED, GMERR_CONNECTION_RESET_BY_PEER);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 045.注入顺序数据，将cuFile内容人为修改损坏，某个分区空洞率超过阈值，另一个分区不超过阈值，对超过阈值的分区注入数据完成后，此时服务端异常重启，恢复后对该表进行两次查询
// 预期：第一次查询失败，第二次查询成功
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t time = 0;
    ret = rowInsertData(stmt, g_tableName, time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 损坏cuFile,多分区多处分散
    DamageDataCuFile(0, 1, false, true);
    DamageDataCuFile(1, 2, false, false);
    // 对超过阈值的分区注入数据，使得注入完成后不超过阈值
    for (int i = 0; i < 20; i++) {
        ret = rowInsertData(stmt, g_tableName, time);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // 服务端异常重启
    ret = GMKill(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "select * from %s", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DATA_CORRUPTION, ret);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(20000, dataCount);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_DATA_CORRUPTION, GMERR_CRC_CHECK_FAILED);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 046.注入跳变数据，将cuFile内容人为修改损坏，某个分区空洞率超过阈值，另一个分区不超过阈值，对超过阈值的分区注入数据完成后，此时服务端异常重启，恢复后对该表进行两次查询
// 预期：第一次查询失败，第二次查询成功
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 增加每步操作的打印辅助判断偶现超时步骤
    AW_FUN_Log(LOG_STEP, "建表开始");
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t time = 0;
    AW_FUN_Log(LOG_STEP, "建表结束");
    AW_FUN_Log(LOG_STEP, "插入数据开始");
    ret = rowInsertOutOfOrderData(stmt, g_tableName, time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "插入数据结束");
    // 损坏cuFile,多分区多处分散
    AW_FUN_Log(LOG_STEP, "cuFile损坏开始");
    DamageDataCuFile(0, 1, false, true);
    DamageDataCuFile(1, 2, true, false);
    AW_FUN_Log(LOG_STEP, "cuFile损坏结束");
    // 对超过阈值的分区注入数据，使得注入完成后不超过阈值
    for (int i = 0; i < 20; i++) {
        AW_FUN_Log(LOG_STEP, "注入数据开始，批次为：%ld", i);
        ret = rowInsertOutOfOrderData(stmt, g_tableName, time);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "注入数据结束，批次为：%ld", i);
    }
    // 服务端异常重启
    AW_FUN_Log(LOG_STEP, "服务端重启开始");
    ret = GMKill(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "服务端重启结束");
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "select * from %s", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DATA_CORRUPTION, ret);
    AW_FUN_Log(LOG_STEP, "第一次查询结束");
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "第二次查询结束");
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 数量需要大于20499，另一分区数据不会全部清除,开启磁盘整理后会丢弃部分数据
    EXPECT_GE(dataCount, 20469);
    AW_FUN_Log(LOG_STEP, "获取数据总数结束");
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "删表结束");
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_DATA_CORRUPTION, GMERR_CRC_CHECK_FAILED);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 047.注入顺序数据，将cuFile内容人为修改损坏（仅一处单字节），对表进行一次查询后，对该分区循环注入和查询
// 预期：第一次查询失败，第二次查询成功
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t time = 0;
    ret = rowInsertData(stmt, g_tableName, time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 损坏cuFile
    DamageDataFile(false, 1, false);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "select * from %s", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DATA_CORRUPTION, ret);
    int64_t dataCount = 0;
    int64_t count = 1390;
    for (int i = 0; i < 20; i++) {
        ret = rowInsertData(stmt, g_tableName, time);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(count + i * 1000, dataCount);
    }
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_DATA_CORRUPTION, GMERR_CRC_CHECK_FAILED);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 048.注入跳变数据，将cuFile内容人为修改损坏（仅一处单字节），对表进行一次查询后，对该分区循环注入和查询
// 预期：第一次查询失败，第二次查询成功
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t time = 0;
    ret = rowInsertOutOfOrderData(stmt, g_tableName, time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 损坏cuFile
    DamageDataFile(false, 1, false);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "select * from %s", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DATA_CORRUPTION, ret);
    int64_t dataCount = 0;
    int64_t count = 1970;
    for (int i = 0; i < 20; i++) {
        ret = rowInsertOutOfOrderData(stmt, g_tableName, time);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(count + i * 1000, dataCount);
    }
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_DATA_CORRUPTION, GMERR_CRC_CHECK_FAILED);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 049.注入顺序数据，将cuFile内容人为修改损坏（同一分区多处分散）且空洞率超过阈值，对表进行一次查询后，对该分区循环注入和循环查询
// 预期：第一次查询失败，第二次查询成功
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t time = 0;
    ret = rowInsertData(stmt, g_tableName, time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 损坏cuFile
    DamageDataCuFile(0, 1, false, true);
    DamageDataCuFile(1, 2, false, true);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "select * from %s", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DATA_CORRUPTION, ret);
    int64_t dataCount = 0;
    int64_t count = 1000;
    for (int i = 0; i < 20; i++) {
        ret = rowInsertData(stmt, g_tableName, time);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(count + i * 1000, dataCount);
    }
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_DATA_CORRUPTION, GMERR_CRC_CHECK_FAILED);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 050.注入跳变数据，将cuFile内容人为修改损坏（同一分区多处分散）且空洞率超过阈值，对表进行一次查询后，对该分区循环注入和循环查询
// 预期：第一次查询失败，第二次查询成功
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t time = 0;
    ret = rowInsertOutOfOrderData(stmt, g_tableName, time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 损坏cuFile
    DamageDataCuFile(0, 1, false, true);
    DamageDataCuFile(1, 2, false, true);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "select * from %s", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DATA_CORRUPTION, ret);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    int64_t count = 1000;
    for (int i = 0; i < 20; i++) {
        ret = rowInsertOutOfOrderData(stmt, g_tableName, time);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(count + i * 1000, dataCount);
    }
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_DATA_CORRUPTION, GMERR_CRC_CHECK_FAILED);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 051.注入顺序数据。将多个分区的cuFile内容人为修改损坏，某个分区空洞率超过阈值，另一个分区不超过阈值，对表进行一次查询后，对另一个分区循环注入和循环查询
// 预期：第一次查询失败，第二次查询成功
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t time = 0;
    ret = rowInsertData(stmt, g_tableName, time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 损坏cuFile,多分区多处分散
    DamageDataCuFile(0, 1, false, true);
    DamageDataCuFile(1, 2, false, false);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "select * from %s", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DATA_CORRUPTION, ret);
    int64_t dataCount = 0;
    int64_t count = 1000;
    time = 3700;
    for (int i = 0; i < 20; i++) {
        ret = rowInsertData(stmt, g_tableName, time);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(count + i * 1000, dataCount);
    }
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_DATA_CORRUPTION, GMERR_CRC_CHECK_FAILED);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 052.注入跳变数据。将多个分区的cuFile内容人为修改损坏，某个分区空洞率超过阈值，另一个分区不超过阈值，对表进行一次查询后，对另一个分区循环注入和循环查询
// 预期：第一次查询失败，第二次查询成功
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t time = 0;
    ret = rowInsertOutOfOrderData(stmt, g_tableName, time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 损坏cuFile,多分区多处分散
    DamageDataCuFile(0, 1, false, true);
    DamageDataCuFile(1, 2, false, false);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "select * from %s", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DATA_CORRUPTION, ret);
    int64_t dataCount = 0;
    int64_t count = 1470;
    time = 7200;
    for (int i = 0; i < 20; i++) {
        ret = rowInsertData(stmt, g_tableName, time);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(count + i * 1000, dataCount);
    }
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_DATA_CORRUPTION, GMERR_CRC_CHECK_FAILED);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 053.注入顺序数据，将多个分区的cuFile内容人为修改损坏，某个分区空洞率超过阈值，执行主动老化函数，删除超过阈值的分区，再进行一次查询
// 预期：第一次查询失败，第二次查询成功
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t time = 0;
    ret = rowInsertData(stmt, g_tableName, time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 损坏cuFile,多分区多处分散
    DamageDataCuFile(0, 1, false, true);
    DamageDataCuFile(1, 2, false, false);
    // 执行主动老化
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "SELECT tsdb_aging('%s');", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)sprintf(sqlCmd, "select * from %s", g_tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DATA_CORRUPTION, ret);
    int64_t dataCount = 0;
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(0, dataCount);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_DATA_CORRUPTION, GMERR_CRC_CHECK_FAILED);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 054.注入跳变数据，将多个分区的cuFile内容人为修改损坏，某个分区空洞率超过阈值，执行主动老化函数，删除超过阈值的分区，再进行一次查询
// 预期：第一次查询失败，第二次查询成功
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_054)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t time = 0;
    ret = rowInsertOutOfOrderData(stmt, g_tableName, time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 损坏cuFile,多分区多处分散
    DamageDataCuFile(0, 1, false, true);
    DamageDataCuFile(1, 2, false, false);
    // 执行主动老化
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "SELECT tsdb_aging('%s');", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)sprintf(sqlCmd, "select * from %s", g_tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DATA_CORRUPTION, ret);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(0, dataCount);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_DATA_CORRUPTION, GMERR_CRC_CHECK_FAILED);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 055.注入顺序数据，将多个分区的cuFile内容人为修改损坏，某个分区空洞率超过阈值，另一个分区不超过阈值，执行主动老化函数，删除超过阈值的分区，进行一次查询，再对超过阈值的分区循环注入和循环查询
// 预期：第一次查询失败，第二次查询成功
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_055)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t time = 0;
    ret = rowInsertData(stmt, g_tableName, time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 损坏cuFile,多分区多处分散
    DamageDataCuFile(0, 1, false, true);
    DamageDataCuFile(1, 2, false, false);
    // 执行主动老化
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "SELECT tsdb_aging('%s');", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)sprintf(sqlCmd, "select * from %s", g_tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DATA_CORRUPTION, ret);
    int64_t dataCount = 0;
    int64_t count = 1000;
    for (int i = 0; i < 20; i++) {
        ret = rowInsertData(stmt, g_tableName, time);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(count + i * 1000, dataCount);
    }
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_DATA_CORRUPTION, GMERR_CRC_CHECK_FAILED);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 056.注入跳变数据，将多个分区的cuFile内容人为修改损坏，某个分区空洞率超过阈值，另一个分区不超过阈值，执行主动老化函数，删除超过阈值的分区，进行一次查询，再对超过阈值的分区循环注入和循环查询
// 预期：第一次查询失败，第二次查询成功
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_056)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t time = 0;
    ret = rowInsertOutOfOrderData(stmt, g_tableName, time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 损坏cuFile,多分区多处分散
    DamageDataCuFile(0, 1, false, true);
    DamageDataCuFile(1, 2, false, false);
    // 执行主动老化
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "SELECT tsdb_aging('%s');", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)sprintf(sqlCmd, "select * from %s", g_tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DATA_CORRUPTION, ret);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    int64_t count = 1000;
    for (int i = 0; i < 20; i++) {
        ret = rowInsertData(stmt, g_tableName, time);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(count + i * 1000, dataCount);
    }
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_DATA_CORRUPTION, GMERR_CRC_CHECK_FAILED);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 057.注入顺序数据，将多个分区的cuFile内容人为修改损坏，某个分区空洞率超过阈值，另一个分区不超过阈值，删表后再建立相同的表，进行一次查询
// 预期：第一次查询失败，第二次查询成功
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_057)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t time = 0;
    ret = rowInsertData(stmt, g_tableName, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 损坏cuFile,多分区多处分散
    DamageDataCuFile(0, 1, false, true);
    DamageDataCuFile(1, 2, false, false);
    // 删表后再建表，建表语句中已有删表语句
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName, time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "select * from %s order by id", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    int64_t count = 1000;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(1000, dataCount);
    int64_t ids[1000] = {0};
    int64_t times[1000] = {0};
    for (int i = 0; i < 1000; i++) {
        ids[i] = i;
        times[i] = i * 6;
    }
    QueryData data1 = {.ids = ids, .times = times, .names = g_name, .ips = g_ip, .blobs = g_message, .dataCount = 1000};
    checkQueryData(stmt, sqlCmd, data1);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_DATA_CORRUPTION, GMERR_CRC_CHECK_FAILED);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 058.注入跳变数据，将多个分区的cuFile内容人为修改损坏，某个分区空洞率超过阈值，另一个分区不超过阈值，删表后再建立相同的表，进行一次查询
// 预期：第一次查询失败，第二次查询成功
TEST_F(TsdbColstorageReliabilityBasic, Timing_047_Basic_058)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t time = 0;
    ret = rowInsertOutOfOrderData(stmt, g_tableName, time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 损坏cuFile,多分区多处分散
    DamageDataCuFile(0, 1, false, true);
    DamageDataCuFile(1, 2, false, false);
    // 删表后再建表，建表语句中已有删表语句
    ret = CreateTable(stmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertOutOfOrderData(stmt, g_tableName, time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "select * from %s order by id", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    int64_t count = 1000;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(1000, dataCount);
    int64_t ids[1000] = {0};
    int64_t times[1000] = {0};
    for (int i = 0; i < 1000; i++) {
        ids[i] = i;
        int k = i % 2;
        times[i] = i + k * 7200;
    }
    QueryData data1 = {.ids = ids, .times = times, .names = g_name, .ips = g_ip, .blobs = g_message, .dataCount = 1000};
    checkQueryData(stmt, sqlCmd, data1);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_DATA_CORRUPTION, GMERR_CRC_CHECK_FAILED);
    AW_FUN_Log(LOG_STEP, "test end.");
}
