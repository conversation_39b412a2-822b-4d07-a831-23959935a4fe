/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2025. All rights reserved.
 * Description: 【技术转交付】TSDB SUM 测试
 * Author: qinjianhua
 * Create: 2024-06-28
 */

#include "gtest/gtest.h"
#include "t_rd_common.h"
#include "../../common/include/component/t_rd_ts.h"

char tabelName[] = "testdb";
char tabelName_1[] = "testdb_1";
char compressModeFast[] = "fast";
char compressModeRapidlz[] = "fast(rapidlz)";
char compressModeZstar[] = "fast(zstar)";
char compressModeNo[] = "no";

static GmcConnOptionsT *connOptions = NULL;
static GmcConnT *conn = NULL;
static GmcStmtT *stmt = NULL;
Status ret = 0;
bool eof = false;
bool isNull = false;
char *dir = getenv("GMDB_HOME");

bool isDataCsvExist()
{
    if (FILE *file = fopen("data.csv", "r")) {
        fclose(file);
        return true;
    }
    return false;
}

void GetCsvFileContent(char returnValue[])
{
    FILE *file = fopen("data.csv", "r");
    char row[128];
    while (fgets(row, 128, file) != NULL) {
        strcat(returnValue, row);
    }
    fclose(file);
}

typedef struct {
    char (*name)[10];
    int64_t *age;
    int64_t *id;
    int64_t *worktime;
    int64_t *salary;
    uint32_t rowNum;
} Data;

typedef struct {
    char (*name)[10];
    int64_t *age;
    int64_t *id;
    int64_t *worktime;
    int64_t *salary;
    uint32_t rowNum;
} Data2;

typedef struct {
    char (*name)[10];
    int64_t *age;
    int64_t *id;
    int64_t *worktime;
    int64_t *salary;
    uint32_t rowNum;
} Data3;

void ExecQueryCmd(GmcStmtT *stmt, const char *queryCommand)
{
    assert(queryCommand != NULL);
    Status ret = GmcExecDirect(stmt, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
}
int DropCmTable(char *tableName)
{
    int ret = 0 ;
    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd, "drop table %s;", tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    RETURN_IFERR(ret);
    return ret;
}

class tsdb_blob_basic : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        InitTsCiCfg();
        ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建epoll监听线程
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        close_epoll_thread();
        testEnvClean();
        RecoverTsCiCfg();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void tsdb_blob_basic::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char names[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t ages[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t ids[] = {1, 2, 4, 9, 8, 14};
    int64_t worktimes[] = {24, 24, 11, 11, 12, 11};
    int64_t salaries[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message[][65535] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    uint32_t rowNum = 6;
    uint8_t *newmessage[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage[j] = message[j];
    }

    Data data = {.name = names, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message blob) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        "tablequery4");

    Status ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, "tablequery4", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, data.name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &data.rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, data.age, sizeof(data.age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, data.id, sizeof(data.id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, data.worktime, sizeof(data.worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, data.salary, sizeof(data.salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage, sizeof(newmessage[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
}

void tsdb_blob_basic::TearDown()
{
    ret = DropCmTable("tablequery4");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcConnOptionsDestroy(connOptions);
    GmcFreeStmt(stmt);
    GmcDisconnect(conn);
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_CHECK_LOG_END();
}

class tsdb_blob_basic_ttl : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/stop.sh -ts");
        TsDefulatDbFileClean();
        UpdateTsLcmCheckPoind();
        system("sh $TEST_HOME/tools/start.sh -ts");
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        // 创建epoll监听线程
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        ret = TestTsGmcConnect(&conn, &stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        close_epoll_thread();
        testEnvClean();
        RecoverTsCiCfg();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void tsdb_blob_basic_ttl::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    char names[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t ages[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t ids[] = {1, 2, 4, 9, 8, 14};
    int64_t worktimes[] = {24, 24, 11, 11, 12, 11};
    int64_t salaries[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message[][65535] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    uint32_t rowNum = 6;
    uint8_t *newmessage[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage[j] = message[j];
    }

    Data data = {.name = names, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message blob) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        "tablequery4");

    Status ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, "tablequery4", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, data.name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &data.rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, data.age, sizeof(data.age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, data.id, sizeof(data.id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, data.worktime, sizeof(data.worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, data.salary, sizeof(data.salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage, sizeof(newmessage[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
}

void tsdb_blob_basic_ttl::TearDown()
{
    ret = DropCmTable("tablequery4");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcConnOptionsDestroy(connOptions);
    GmcFreeStmt(stmt);
    GmcDisconnect(conn);
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_CHECK_LOG_END();
}

// 001、建表支持blob类型，blob列默认长度
TEST_F(tsdb_blob_basic, Timing_031_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[65535] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11};
    uint8_t message[][65535] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select message, worktime from tablequery4;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = 65535;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &messageRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message[i], 65535), 0);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);

        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)6);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002、建表支持blob类型，设置blob列长度为范围内
TEST_F(tsdb_blob_basic, Timing_031_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    DropCmTable("tablequery4");
    char names[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t ages[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t ids[] = {1, 2, 4, 9, 8, 14};
    int64_t worktimes[] = {24, 24, 11, 11, 12, 11};
    int64_t salaries[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message[][1600] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    uint32_t rowNum = 6;
    uint8_t *newmessage[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage[j] = message[j];
    }

    Data2 data2 = {.name = names, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message "
        "blob(1600)) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        "tablequery4");

    Status ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, "tablequery4", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, data2.name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &data2.rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, data2.age, sizeof(data2.age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, data2.id, sizeof(data2.id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, data2.worktime, sizeof(data2.worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, data2.salary, sizeof(data2.salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage, sizeof(newmessage[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[1600] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select message, worktime from tablequery4;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = 1600;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &messageRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message[i], 1600), 0);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);

        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)6);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003、建表支持blob类型，设置blob列的长度为-1
TEST_F(tsdb_blob_basic, Timing_031_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    DropCmTable("tablequery4");

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message blob(1)) "
        "with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        "tablequery4");

    Status ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009000);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004、建表支持blob类型，设置blob列的长度为65536
TEST_F(tsdb_blob_basic, Timing_031_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    DropCmTable("tablequery4");

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message blob(1)) "
        "with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        "tablequery4");

    Status ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1003000);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005、建表支持blob类型多列操作
TEST_F(tsdb_blob_basic, Timing_031_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    DropCmTable("tablequery4");
    char names[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t ages[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t ids[] = {1, 2, 4, 9, 8, 14};
    int64_t worktimes[] = {24, 24, 11, 11, 12, 11};
    int64_t salaries[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message[][65535] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    uint8_t message2[][1600] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    constexpr uint32_t rowNum = 6;
    uint8_t *newmessage[rowNum];
    uint8_t *newmessage2[rowNum];
    uint32_t newmessage2len[rowNum] = {
        9, 9, 0, 9, 9, 9};  // blob申请的最大长度1600与建表时不一样，则GmcBindCol需绑定实际传入的数据长度
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage[j] = message[j];
        newmessage2[j] = message2[j];
    }

    Data3 data3 = {.name = names, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message blob ,  "
        "message2 blob)with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        "tablequery4");

    Status ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, "tablequery4", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, data3.name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &data3.rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, data3.age, sizeof(data3.age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, data3.id, sizeof(data3.id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, data3.worktime, sizeof(data3.worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, data3.salary, sizeof(data3.salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage, sizeof(newmessage[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 6, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage2, sizeof(newmessage2[0]),
        newmessage2len);  // GmcBindCol需绑定实际传入的数据长度
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[65535] = {0};
    uint8_t messageRes2[1600] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select message, message2,worktime from tablequery4;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = 65535;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &messageRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = 1600;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &messageRes2, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message[i], 65535), 0);
        AW_MACRO_EXPECT_EQ_INT(
            memcmp(messageRes2, message2[i], newmessage2len[i]), 0);  // 查询时需绑定实际传入的数据长度
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);

        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)6);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006、插入blob类型数据为blob类型
TEST_F(tsdb_blob_basic, Timing_031_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    DropCmTable("tablequery4");
    char name[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t age[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t id[] = {1, 2, 4, 9, 8, 14};
    int64_t worktime[] = {24, 24, 11, 11, 12, 11};
    int64_t salary[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message[][1600] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    uint32_t rowNum = 6;
    uint8_t *newmessage[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage[j] = message[j];
    }

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message "
        "blob(1600)) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        "tablequery4");

    Status ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, "tablequery4", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, salary, sizeof(salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage, sizeof(newmessage[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[1600] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select message, worktime from tablequery4;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = 1600;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &messageRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message[i], 1600), 0);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);

        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)6);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007、插入blob类型数据为fixed
TEST_F(tsdb_blob_basic, Timing_031_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    DropCmTable("tablequery4");
    char name[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t age[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t id[] = {1, 2, 4, 9, 8, 14};
    int64_t worktime[] = {24, 24, 11, 11, 12, 11};
    int64_t salary[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message[][1600] = {"?!2@$123", "?!2@$456", "", "", "", ""};
    uint32_t rowNum = 6;
    uint8_t *newmessage[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage[j] = message[j];
    }

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message "
        "blob(1600)) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        "tablequery4");

    Status ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, "tablequery4", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, salary, sizeof(salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_FIXED, message, 1600, NULL);  // 不支持DB_DATATYPE_FIXED类型
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_INVALID_PARAMETER_VALUE);
}

// 008、插入blob类型数据为int类型（int64/int32/int16/int8）
TEST_F(tsdb_blob_basic, Timing_031_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    DropCmTable("tablequery4");
    char name[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t age[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t id[] = {1, 2, 4, 9, 8, 14};
    int64_t worktime[] = {24, 24, 11, 11, 12, 11};
    int64_t salary[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message[][1600] = {"20000", "10000", "4000", "30000", "31000", "10000"};
    uint32_t rowNum = 6;
    uint8_t *newmessage[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage[j] = message[j];
    }

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message "
        "blob(1600)) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        "tablequery4");

    Status ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, "tablequery4", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, salary, sizeof(salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(
        stmt, 5, (GmcDataTypeE)DB_DATATYPE_INT64, newmessage, sizeof(newmessage[0]), NULL);  // DB_DATATYPE_INT64类型
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_INVALID_PARAMETER_VALUE);
}

// 009、插入blob类型数据,非blob、fixed和int中一种
TEST_F(tsdb_blob_basic, Timing_031_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    DropCmTable("tablequery4");
    char name[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t age[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t id[] = {1, 2, 4, 9, 8, 14};
    int64_t worktime[] = {24, 24, 11, 11, 12, 11};
    int64_t salary[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message[][1600] = {1, 1, 1, 1, 1, 1};
    uint32_t rowNum = 6;
    uint8_t *newmessage[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage[j] = message[j];
    }

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message "
        "blob(1600)) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        "tablequery4");

    Status ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, "tablequery4", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, salary, sizeof(salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BOOL, &message, sizeof(bool), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, 1009007);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, 1012000);
    AW_ADD_ERRNUM_WHITE_LIST(2, 1009007, 1012000);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010、插入blob类型数据,插入不定长字符串
TEST_F(tsdb_blob_basic, Timing_031_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    DropCmTable("tablequery4");
    char name[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t age[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t id[] = {1, 2, 4, 9, 8, 14};
    int64_t worktime[] = {24, 24, 11, 11, 12, 11};
    int64_t salary[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message[][1600] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    uint32_t rowNum = 6;
    uint8_t *newmessage[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage[j] = message[j];
    }

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message "
        "blob(1600)) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        "tablequery4");

    Status ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, "tablequery4", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, salary, sizeof(salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage, sizeof(newmessage[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[1600] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select message, worktime from tablequery4;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = 1600;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &messageRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message[i], 1600), 0);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);

        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)6);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 011、建表指定blob类型列长度1600，插入blob类型数据长度不大于1600(包含结尾符)
TEST_F(tsdb_blob_basic, Timing_031_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    DropCmTable("tablequery4");
    char name[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t age[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t id[] = {1, 2, 4, 9, 8, 14};
    int64_t worktime[] = {24, 24, 11, 11, 12, 11};
    int64_t salary[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message[][1000] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    constexpr uint32_t rowNum = 6;
    uint8_t *newmessage[rowNum];
    uint32_t messagelen[rowNum] = {
        9, 9, 0, 9, 9, 9};  // blob申请的最大长度1000与建表时不一样，则GmcBindCol需绑定实际传入的数据长度
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage[j] = message[j];
    }

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message "
        "blob(1600)) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        "tablequery4");

    Status ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, "tablequery4", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, salary, sizeof(salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage, sizeof(newmessage[0]), messagelen);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[1000] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select message, worktime from tablequery4;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = 1000;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &messageRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message[i], messagelen[i]), 0);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);

        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)6);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 012、建表指定blob类型列长度1600，插入blob类型数据长度大于1600
TEST_F(tsdb_blob_basic, Timing_031_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    DropCmTable("tablequery4");
    char name[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t age[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t id[] = {1, 2, 4, 9, 8, 14};
    int64_t worktime[] = {24, 24, 11, 11, 12, 11};
    int64_t salary[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message[][1601] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    constexpr uint32_t rowNum = 6;
    uint8_t *newmessage[rowNum];
    uint32_t messagelen[rowNum] = {1601, 1601, 1601, 1601, 1601, 1601};
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage[j] = message[j];
    }

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message "
        "blob(1600)) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        "tablequery4");

    Status ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, "tablequery4", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, salary, sizeof(salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage, sizeof(newmessage[0]),
        messagelen);  // 目前实现机制是：1.用户设置长度为小于建表1600，则为用户设置的长度，2.设置长度小于65535大于建表1600时按建表1601长度截断，
                      // 3.设置长度大于65535是可能会出现越界问题，资料有说明，风险用户控制
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[1601] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select message, worktime from tablequery4;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = 1601;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &messageRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message[i], messagelen[i]), 0);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);

        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)6);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 013、建表blob类型默认列长度，插入blob类型数据长度不大于65535(包含结尾符)
TEST_F(tsdb_blob_basic, Timing_031_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    DropCmTable("tablequery4");
    char name[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t age[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t id[] = {1, 2, 4, 9, 8, 14};
    int64_t worktime[] = {24, 24, 11, 11, 12, 11};
    int64_t salary[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message[][1000] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    constexpr uint32_t rowNum = 6;
    uint8_t *newmessage[rowNum];
    uint32_t messagelen[rowNum] = {9, 9, 0, 9, 9, 9};
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage[j] = message[j];
    }

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message "
        "blob(65535)) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        "tablequery4");

    Status ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, "tablequery4", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, salary, sizeof(salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage, sizeof(newmessage[0]), messagelen);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[1000] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select message, worktime from tablequery4;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = 1000;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &messageRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message[i], messagelen[i]), 0);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);

        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)6);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 014、建表blob类型默认列长度，插入blob类型数据长度大于65535
TEST_F(tsdb_blob_basic, Timing_031_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    DropCmTable("tablequery4");
    char name[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t age[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t id[] = {1, 2, 4, 9, 8, 14};
    int64_t worktime[] = {24, 24, 11, 11, 12, 11};
    int64_t salary[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message[][65536] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    constexpr uint32_t rowNum = 6;
    uint8_t *newmessage[rowNum];
    uint32_t messagelen[rowNum] = {65536, 65536, 65536, 65536, 65536, 65536};
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage[j] = message[j];
    }

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message "
        "blob(65535)) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        "tablequery4");

    Status ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, "tablequery4", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, salary, sizeof(salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage, 65536, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, 1004004);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, 1012000);

    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[65536] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select message, worktime from tablequery4;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = 1601;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &messageRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message[i], 65535), 0);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);

        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)0);
    AW_ADD_ERRNUM_WHITE_LIST(2, 1004004, 1012000);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 015、插入大量blob类型数据（超过5w）
TEST_F(tsdb_blob_basic, Timing_031_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    DropCmTable("tablequery4");
    constexpr int count = 50000;
    uint8_t message[count][10];

    char name[count][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t age[count] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t id[count] = {1, 2, 4, 9, 8, 14};
    int64_t worktime[count] = {24, 24, 11, 11, 12, 11};
    int64_t salary[count] = {20000, 10000, 4000, 30000, 31000, 10000};

    uint32_t rowNum = count;
    uint8_t *newmessage[rowNum];
    for (uint32_t i = 0; i < rowNum; i++) {
        for (int j = 0; j < 10; j++) {
            message[i][j] = (uint8_t)(i * 10 + j);  // 示例：使用i和j计算一个值
        }
    }
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage[j] = message[j];
    }

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message "
        "blob(10)) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        "tablequery4");

    Status ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, "tablequery4", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, salary, sizeof(salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage, sizeof(newmessage[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[10] = {0};
    int64_t resultWorktime[count] = {24, 24, 11, 11, 12, 11};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select message, worktime from tablequery4;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = 10;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &messageRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message[i], 10), 0);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);

        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)count);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 016、多次重复插入blob类型数据
TEST_F(tsdb_blob_basic, Timing_031_016)
{

    AW_FUN_Log(LOG_STEP, "test start.");

    DropCmTable("tablequery4");
    char names[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t ages[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t ids[] = {1, 2, 4, 9, 8, 14};
    int64_t worktimes[] = {24, 24, 11, 11, 12, 11};
    int64_t salaries[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message[][65535] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    uint32_t rowNum = 6;
    uint8_t *newmessage[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage[j] = message[j];
    }

    Data data = {.name = names, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message blob) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        "tablequery4");

    Status ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    int count = 2;
    for (int i = 0; i < count; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, "tablequery4", GMC_OPERATION_SQL_INSERT);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

        ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, data.name, 10, NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &data.rowNum, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, data.age, sizeof(data.age[0]), NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, data.id, sizeof(data.id[0]), NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, data.worktime, sizeof(data.worktime[0]), NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, data.salary, sizeof(data.salary[0]), NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage, sizeof(newmessage[0]), NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    }

    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[65535] = {0};
    uint8_t resultWorktime[] = {24, 24, 11, 11, 12, 11, 24, 24, 11, 11, 12, 11};

    uint8_t message2[][65535] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537", "0071 3520",
        "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select message, worktime from tablequery4;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = 65535;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &messageRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message2[i], 65535), 0);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);

        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)12);
    AW_FUN_Log(LOG_STEP, "test end.");
}

void *Timing_031_017_01(void *args)
{
    char name[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t age[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t id[] = {1, 2, 4, 9, 8, 14};
    int64_t worktime[] = {24, 24, 11, 11, 12, 11};
    int64_t salary[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message[][1600] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    uint32_t rowNum = 6;
    uint8_t *newmessage[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage[j] = message[j];
    }
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, "tablequery4", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, salary, sizeof(salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage, sizeof(newmessage[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    GmcFreeStmt(stmt);
    GmcDisconnect(conn);
    return NULL;
}

void *Timing_031_017_02(void *args)
{
    char name[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t age[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t id[] = {1, 2, 4, 9, 8, 14};
    int64_t worktime[] = {24, 24, 11, 11, 12, 11};
    int64_t salary[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message[][1600] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    uint32_t rowNum = 6;
    uint8_t *newmessage[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage[j] = message[j];
    }
    static GmcConnT *conn = NULL;
    static GmcStmtT *stmt = NULL;
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, "tablequery4", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, salary, sizeof(salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage, sizeof(newmessage[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    GmcConnOptionsDestroy(connOptions);
    GmcFreeStmt(stmt);
    GmcDisconnect(conn);
    return NULL;
}

// 017、并发插入blob类型数据
TEST_F(tsdb_blob_basic, Timing_031_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    DropCmTable("tablequery4");
    char name[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t age[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t id[] = {1, 2, 4, 9, 8, 14};
    int64_t worktime[] = {24, 24, 11, 11, 12, 11};
    int64_t salary[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message[][1600] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    uint32_t rowNum = 6;
    uint8_t *newmessage[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage[j] = message[j];
    }

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message "
        "blob(1600)) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        "tablequery4");

    Status ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    pthread_t client_thr_01, client_thr_02;
    void *thr_ret[30] = {0};
    int end_num = 1;
    for (int i = 0; i < end_num; i++) {
        ret = pthread_create(&client_thr_01, NULL, Timing_031_017_01, NULL);
        EXPECT_EQ(GMERR_OK, ret);

        ret = pthread_create(&client_thr_02, NULL, Timing_031_017_02, NULL);
        EXPECT_EQ(GMERR_OK, ret);

        pthread_join(client_thr_01, &thr_ret[1]);
        pthread_join(client_thr_02, &thr_ret[2]);
    }

    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[1600] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11, 24, 24, 11, 11, 12, 11};
    uint8_t message2[][1600] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537", "0071 3520",
        "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select message, worktime from tablequery4;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = 1600;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &messageRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message2[i], 1600), 0);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);

        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)12);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 018、全表查询blob类型数据
TEST_F(tsdb_blob_basic, Timing_031_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[65535] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11};
    uint8_t message[][65535] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select * from tablequery4;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = 65535;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 5, &messageRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message[i], 65535), 0);

        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)6);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 019、单列查询blob类型数据
TEST_F(tsdb_blob_basic, Timing_031_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[65535] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11};
    uint8_t message[][65535] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select message, worktime from tablequery4;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = 65535;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &messageRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message[i], 65535), 0);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);

        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)6);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 020、查询blob类型数据，表中无数据
TEST_F(tsdb_blob_basic, Timing_031_020)
{

    AW_FUN_Log(LOG_STEP, "test start.");

    DropCmTable("tablequery4");
    char names[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t ages[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t ids[] = {1, 2, 4, 9, 8, 14};
    int64_t worktimes[] = {24, 24, 11, 11, 12, 11};
    int64_t salaries[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message[][65535] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    uint32_t rowNum = 6;
    uint8_t *newmessage[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage[j] = message[j];
    }

    Data data = {.name = names, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message blob) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        "tablequery4");

    Status ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[65535] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select message, worktime from tablequery4;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = 65535;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &messageRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message[i], 65535), 0);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);

        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)0);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 021、删表blob类型表
TEST_F(tsdb_blob_basic, Timing_031_021)
{

    AW_FUN_Log(LOG_STEP, "test start.");
    DropCmTable("tablequery4");
    char names[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t ages[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t ids[] = {1, 2, 4, 9, 8, 14};
    int64_t worktimes[] = {24, 24, 11, 11, 12, 11};
    int64_t salaries[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message[][65535] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    uint32_t rowNum = 6;
    uint8_t *newmessage[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage[j] = message[j];
    }

    Data data = {.name = names, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message blob) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        "tablequery4");

    Status ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, "tablequery4", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, data.name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &data.rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, data.age, sizeof(data.age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, data.id, sizeof(data.id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, data.worktime, sizeof(data.worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, data.salary, sizeof(data.salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage, sizeof(newmessage[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[65535] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select message, worktime from tablequery4;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = 65535;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &messageRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message[i], 65535), 0);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);

        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)6);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 022、删表blob类型表后，重新建表插入、查询数据
TEST_F(tsdb_blob_basic, Timing_031_022)
{

    AW_FUN_Log(LOG_STEP, "test start.");

    DropCmTable("tablequery4");
    char names[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t ages[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t ids[] = {1, 2, 4, 9, 8, 14};
    int64_t worktimes[] = {24, 24, 11, 11, 12, 11};
    int64_t salaries[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message[][65535] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    uint32_t rowNum = 6;
    uint8_t *newmessage[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage[j] = message[j];
    }

    Data data = {.name = names, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message blob) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        "tablequery4");

    Status ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, "tablequery4", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, data.name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &data.rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, data.age, sizeof(data.age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, data.id, sizeof(data.id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, data.worktime, sizeof(data.worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, data.salary, sizeof(data.salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage, sizeof(newmessage[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[65535] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select message, worktime from tablequery4;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = 65535;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &messageRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message[i], 65535), 0);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);

        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)6);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 023、两个表都带有blob类型，表的格式一致，将一个表中数据全扫描后插入另一张表中
TEST_F(tsdb_blob_basic, Timing_031_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char name[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t age[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t id[] = {1, 2, 4, 9, 8, 14};
    int64_t worktime[] = {24, 24, 11, 11, 12, 11};
    int64_t salary[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message2[][65535] = {"9021 6537", "0071 3520", "0010 0000", "", "3102 0021", "0000 0000"};
    uint32_t rowNum = 6;
    uint8_t *newmessage2[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage2[j] = message2[j];
    }

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message "
        "blob(65535)) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        "tablequery5");

    Status ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, "tablequery5", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, salary, sizeof(salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage2, sizeof(newmessage2[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[65535] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11};

    // 查询insert into前表tablequery5数据
    isNull = false;
    uint32_t i = 0;
    const char *queryCommand = "select message, worktime from tablequery5;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }

        propSize = 65535;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &messageRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message2[i], 65535), 0);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);

        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)6);

    // insert into 相当于不清除表tablequery5的数据，然后把tablequery4的数据往tablequery5后按数据插入
    char commandInsert[512] = "insert into tablequery5 select * from tablequery4;";
    ret = GmcExecDirect(stmt, commandInsert, 512);
    EXPECT_EQ(ret, GMERR_OK);

    uint8_t message3[][65535] = {"9021 6537", "0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "0071 3520",
        "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    int64_t resultWorktime3[] = {24, 24, 11, 11, 12, 11, 24, 24, 11, 11, 12, 11};
    // 查询insert into后表tablequery5数据
    isNull = false;
    i = 0;
    queryCommand = "select message, worktime from tablequery5;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = 65535;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &messageRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message3[i], 65535), 0);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime3[i]);

        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)12);

    DropCmTable("tablequery5");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 024、两个表都带有blob类型，将一个表中扫描出几列后，插入到另一张表对应数据类型的列
TEST_F(tsdb_blob_basic, Timing_031_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char name[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t age[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t id[] = {1, 2, 4, 9, 8, 14};
    int64_t worktime[] = {24, 24, 11, 11, 12, 11};
    int64_t salary[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message2[][65535] = {"9021 6537", "0071 3520", "0010 0000", "", "3102 0021", "0000 0000"};
    uint32_t rowNum = 6;

    uint8_t *newmessage2[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage2[j] = message2[j];
    }

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message "
        "blob(65535)) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        "tablequery5");

    Status ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, "tablequery5", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, salary, sizeof(salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage2, sizeof(newmessage2[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[65535] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11};

    // 查询insert into前表tablequery5数据
    isNull = false;
    uint32_t i = 0;
    const char *queryCommand = "select message, worktime from tablequery5;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }

        propSize = 65535;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &messageRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message2[i], 65535), 0);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);

        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)6);

    // insert into 相当于不清除表tablequery5的数据，然后把tablequery4的数据往tablequery5后按数据插入
    char commandInsert[512] = "insert into tablequery5(message, worktime) select message, worktime from tablequery4;";
    ret = GmcExecDirect(stmt, commandInsert, 512);
    EXPECT_EQ(ret, GMERR_OK);

    uint8_t message3[][65535] = {"9021 6537", "0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "0071 3520",
        "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    int64_t resultWorktime3[] = {24, 24, 11, 11, 12, 11, 24, 24, 11, 11, 12, 11};
    // 查询insert into后表tablequery5数据
    isNull = false;
    i = 0;
    queryCommand = "select message, worktime from tablequery5;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = 65535;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &messageRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message3[i], 65535), 0);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime3[i]);

        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)12);

    DropCmTable("tablequery5");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 025、两个表都带有blob类型，将一个表中扫描出几列后，插入到另一张表不同类型的列
TEST_F(tsdb_blob_basic, Timing_031_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char name[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t age[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t id[] = {1, 2, 4, 9, 8, 14};
    int64_t worktime[] = {24, 24, 11, 11, 12, 11};
    int64_t salary[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message2[][65535] = {"9021 6537", "0071 3520", "0010 0000", "", "3102 0021", "0000 0000"};
    uint32_t rowNum = 6;
    uint8_t *newmessage2[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage2[j] = message2[j];
    }

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message "
        "blob(65535)) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        "tablequery5");

    Status ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, "tablequery5", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, salary, sizeof(salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage2, sizeof(newmessage2[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[65535] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11};

    // 查询insert into前表tablequery5数据
    isNull = false;
    uint32_t i = 0;
    const char *queryCommand = "select message, worktime from tablequery5;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }

        propSize = 65535;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &messageRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message2[i], 65535), 0);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);

        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)6);

    // insert into 相当于不清除表tablequery5的数据，然后把tablequery4的数据往tablequery5后按数据插入
    char commandInsert[512] = "insert into tablequery5(message, name) select message, worktime from tablequery4;";
    ret = GmcExecDirect(stmt, commandInsert, 512);
    EXPECT_EQ(ret, 1009007);

    uint8_t message3[][65535] = {"9021 6537", "0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "0071 3520",
        "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    int64_t resultWorktime3[] = {24, 24, 11, 11, 12, 11, 24, 24, 11, 11, 12, 11};
    // 查询insert into后表tablequery5数据,插入失败仍是之前的数据
    isNull = false;
    i = 0;
    queryCommand = "select message, worktime from tablequery5;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }

        propSize = 65535;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &messageRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message2[i], 65535), 0);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);

        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)6);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009007);
    DropCmTable("tablequery5");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 026、两个表都带有blob类型，但blob列大小不一致，将一个表中扫描出几列后，插入到另一张表不同类型的列（tableinsertblobErr1>tableselectblobErr1
// blob列长度）
TEST_F(tsdb_blob_basic, Timing_031_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    DropCmTable("tablequery4");
    char names[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t ages[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t ids[] = {1, 2, 4, 9, 8, 14};
    int64_t worktimes[] = {24, 24, 11, 11, 12, 11};
    int64_t salaries[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message[][1600] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    uint32_t rowNum = 6;
    uint8_t *newmessage[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage[j] = message[j];
    }

    Data2 data2 = {.name = names, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message "
        "blob(1600)) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        "tablequery4");

    Status ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, "tablequery4", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, data2.name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &data2.rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, data2.age, sizeof(data2.age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, data2.id, sizeof(data2.id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, data2.worktime, sizeof(data2.worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, data2.salary, sizeof(data2.salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage, sizeof(newmessage[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char name[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t age[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t id[] = {1, 2, 4, 9, 8, 14};
    int64_t worktime[] = {24, 24, 11, 11, 12, 11};
    int64_t salary[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message2[][65535] = {"9021 6537", "0071 3520", "0010 0000", "", "3102 0021", "0000 0000"};
    rowNum = 6;
    uint8_t *newmessage2[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage2[j] = message2[j];
    }

    ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message "
        "blob(65535)) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        "tablequery5");

    ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, "tablequery5", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, salary, sizeof(salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage2, sizeof(newmessage2[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[65535] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11};

    // 查询insert into前表tablequery5数据
    isNull = false;
    uint32_t i = 0;
    const char *queryCommand = "select message, worktime from tablequery5;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }

        propSize = 65535;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &messageRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message2[i], 65535), 0);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);

        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)6);

    // insert into 相当于不清除表tablequery5的数据，然后把tablequery4的数据往tablequery5后按数据插入
    char commandInsert[512] = "insert into tablequery5 select * from tablequery4;";
    ret = GmcExecDirect(stmt, commandInsert, 512);
    EXPECT_EQ(ret, GMERR_OK);

    uint8_t message3[][65535] = {"9021 6537", "0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "0071 3520",
        "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    int64_t resultWorktime3[] = {24, 24, 11, 11, 12, 11, 24, 24, 11, 11, 12, 11};
    // 查询insert into后表tablequery5数据
    isNull = false;
    i = 0;
    queryCommand = "select message, worktime from tablequery5;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = 65535;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &messageRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message3[i], 65535), 0);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime3[i]);

        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)12);

    DropCmTable("tablequery5");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 027、两个表都带有blob类型，但blob列大小不一致，将一个表中扫描出几列后，插入到另一张表不同类型的列（tableinsertblobErr1<tableselectblobErr1
// blob列长度）
TEST_F(tsdb_blob_basic, Timing_031_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char name[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t age[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t id[] = {1, 2, 4, 9, 8, 14};
    int64_t worktime[] = {24, 24, 11, 11, 12, 11};
    int64_t salary[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message2[][1600] = {"9021 6537", "0071 3520", "0010 0000", "", "3102 0021", "0000 0000"};
    uint32_t rowNum = 6;
    uint8_t *newmessage2[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage2[j] = message2[j];
    }

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message "
        "blob(1600)) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        "tablequery5");

    Status ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, "tablequery5", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, salary, sizeof(salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage2, sizeof(newmessage2[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[1600] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11};

    // 查询insert into前表tablequery5数据
    isNull = false;
    uint32_t i = 0;
    const char *queryCommand = "select message, worktime from tablequery5;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }

        propSize = 1600;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &messageRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message2[i], 1600), 0);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);

        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)6);

    // insert into 相当于不清除表tablequery5的数据，然后把tablequery4的数据往tablequery5后按数据插入
    char commandInsert[512] = "insert into tablequery5 select * from tablequery4;";
    ret = GmcExecDirect(stmt, commandInsert, 512);
    EXPECT_EQ(ret, 1009007);

    uint8_t message3[][1600] = {"9021 6537", "0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "0071 3520",
        "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    int64_t resultWorktime3[] = {24, 24, 11, 11, 12, 11, 24, 24, 11, 11, 12, 11};
    // 查询insert into后表tablequery5数据
    isNull = false;
    i = 0;
    queryCommand = "select message, worktime from tablequery5;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }

        propSize = 1600;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &messageRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message3[i], 1600), 0);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);

        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)6);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009007);
    DropCmTable("tablequery5");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 028、按blob类型过滤where
TEST_F(tsdb_blob_basic, Timing_031_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[65535] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11};
    uint8_t message[][65535] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select message, worktime from tablequery4 where message >1 ;";
    Status ret = GmcExecDirect(stmt, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(
        ret, GMERR_SEMANTIC_ERROR);  // 2014.11.13 DTS2024111113519提前了校验时机，在verifier报错不支持blob type
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_SEMANTIC_ERROR);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 029、按blob类型过滤like
TEST_F(tsdb_blob_basic, Timing_031_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[65535] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11};
    uint8_t message[][65535] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select message, worktime from tablequery4 where message like '00%' ;";
    Status ret = GmcExecDirect(stmt, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(
        ret, GMERR_SEMANTIC_ERROR);  // 2014.11.13 DTS2024111113519提前了校验时机，在verifier报错不支持blob type
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_SEMANTIC_ERROR);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 030、按blob类型过滤 escape
TEST_F(tsdb_blob_basic, Timing_031_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[65535] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11};
    uint8_t message[][65535] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select message, worktime from tablequery4 escape '#';";
    Status ret = GmcExecDirect(stmt, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, 1009000);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009000);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 031、按blob类型过滤等值/不等值查询
TEST_F(tsdb_blob_basic, Timing_031_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[65535] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11};
    uint8_t message[][65535] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select message, worktime from tablequery4 where message =1 ;";
    Status ret = GmcExecDirect(stmt, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_SEMANTIC_ERROR);

    queryCommand = "select message, worktime from tablequery4 where message !=1 ;";
    ret = GmcExecDirect(stmt, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(
        ret, GMERR_SEMANTIC_ERROR);  // 2014.11.13 DTS2024111113519提前了校验时机，在verifier报错不支持blob type
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_SEMANTIC_ERROR);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 032、按blob类型排序
TEST_F(tsdb_blob_basic, Timing_031_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[65535] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11};
    uint8_t message[][65535] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select message, worktime from tablequery4 order by message ;";
    Status ret = GmcExecDirect(stmt, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(
        ret, GMERR_SEMANTIC_ERROR);  // 2014.11.13 DTS2024111113519提前了校验时机，在verifier报错不支持blob type
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_SEMANTIC_ERROR);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 033、按blob类型分组
TEST_F(tsdb_blob_basic, Timing_031_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[65535] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11};
    uint8_t message[][65535] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select message, worktime from tablequery4 group by message ;";
    Status ret = GmcExecDirect(stmt, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(
        ret, GMERR_SEMANTIC_ERROR);  // 2014.11.13 DTS2024111113519提前了校验时机，在verifier报错不支持blob type
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_SEMANTIC_ERROR);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 034、按blob类型聚合sum
TEST_F(tsdb_blob_basic, Timing_031_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[65535] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11};
    uint8_t message[][65535] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select sum(message+worktime), worktime from tablequery4 ;";
    Status ret = GmcExecDirect(stmt, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_SEMANTIC_ERROR);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_SEMANTIC_ERROR);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 035、按blob类型聚合max
TEST_F(tsdb_blob_basic, Timing_031_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[65535] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11};
    uint8_t message[][65535] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select max(message), worktime from tablequery4 group by worktime;";
    Status ret = GmcExecDirect(stmt, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, 1003000);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1003000);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 036、按blob类型聚合min
TEST_F(tsdb_blob_basic, Timing_031_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[65535] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11};
    uint8_t message[][65535] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select min(message), worktime from tablequery4 group by worktime;  ;";
    Status ret = GmcExecDirect(stmt, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, 1009000);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009000);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 037、按blob类型聚合count
TEST_F(tsdb_blob_basic, Timing_031_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[65535] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11};
    uint8_t message[][65535] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select count(message), worktime from tablequery4  group by worktime;;";
    Status ret = GmcExecDirect(stmt, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, 1009000);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009000);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 038、按blob类型聚合first
TEST_F(tsdb_blob_basic, Timing_031_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[65535] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11};
    uint8_t message[][65535] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select first(message), worktime from tablequery4 ;";
    Status ret = GmcExecDirect(stmt, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, 1003000);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1003000);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 039、按blob类型隐式first
TEST_F(tsdb_blob_basic, Timing_031_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[65535] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11};
    uint8_t message[][65535] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select message, worktime from tablequery4 group by worktime order by worktime ;";
    Status ret = GmcExecDirect(stmt, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, 1003000);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1003000);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 040、按blob类型聚合last
TEST_F(tsdb_blob_basic, Timing_031_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[65535] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11};
    uint8_t message[][65535] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select last(message), worktime from tablequery4 ;";
    Status ret = GmcExecDirect(stmt, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, 1003000);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1003000);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 041、按blob类型聚合colset
TEST_F(tsdb_blob_basic, Timing_031_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[65535] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11};
    uint8_t message[][65535] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select colset(message), worktime from tablequery4 ;";
    Status ret = GmcExecDirect(stmt, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, 1003000);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1003000);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 042、按blob类型聚合length
TEST_F(tsdb_blob_basic, Timing_031_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[65535] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11};
    uint8_t message[][65535] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select length(message), worktime from tablequery4 ;";
    Status ret = GmcExecDirect(stmt, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, 1003000);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1003000);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 043、对blob类型数据进行压缩（四种压缩模式）
TEST_F(tsdb_blob_basic_ttl, Timing_031_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    DropCmTable("tablequery4");
    char name[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t age[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t id[] = {1, 2, 4, 9, 8, 14};
    int64_t worktime[] = {24, 24, 11, 11, 12, 11};
    int64_t salary[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message[][1600] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    uint32_t rowNum = 6;
    uint8_t *newmessage[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage[j] = message[j];
    }

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message "
        "blob(1600)) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'no', ttl = '1 hours');",
        "tablequery4");

    Status ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, "tablequery4", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, salary, sizeof(salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage, sizeof(newmessage[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // sleep(5)之前查询删除未数据
    const char *queryCommand = "select message, worktime from tablequery4;";
    ExecQueryCmd(stmt, queryCommand);

    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[1600] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11};

    bool isNull = false;
    uint32_t i = 0;
    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = 1600;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &messageRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message[i], 1600), 0);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);

        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)6);

    sleep(6);

    // sleep(5)之后查询后删除已数据
    queryCommand = "select message, worktime from tablequery4;";
    ExecQueryCmd(stmt, queryCommand);

    i = 0;
    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = 1600;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &messageRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message[i], 1600), 0);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);

        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)0);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 044、对blob类型数据进行老化
TEST_F(tsdb_blob_basic, Timing_031_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    DropCmTable("tablequery4");
    char name[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t age[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t id[] = {1, 2, 4, 9, 8, 14};
    int64_t worktime[] = {24, 24, 11, 11, 12, 11};
    int64_t salary[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message[][1600] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    uint32_t rowNum = 6;
    uint8_t *newmessage[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage[j] = message[j];
    }

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message "
        "blob(1600)) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        "tablequery4");

    Status ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, "tablequery4", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, salary, sizeof(salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage, sizeof(newmessage[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand), "SELECT tsdb_aging('tablequery4');");
    ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(1);

    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[1600] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select message, worktime from tablequery4;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = 1600;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &messageRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message[i], 65535), 0);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);

        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)0);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 045、对blob类型数据批量读
TEST_F(tsdb_blob_basic, Timing_031_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    DropCmTable("tablequery4");
    char name[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t age[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t id[] = {1, 2, 4, 9, 8, 14};
    int64_t worktime[] = {24, 24, 11, 11, 12, 11};
    int64_t salary[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message[][1600] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    uint32_t rowNum = 6;
    uint8_t *newmessage[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage[j] = message[j];
    }

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message "
        "blob(1600)) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        "tablequery4");

    Status ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, "tablequery4", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, salary, sizeof(salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage, sizeof(newmessage[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    sprintf(ddlCommand,
        "COPY (SELECT id, message, worktime FROM tablequery4) TO"
        "'%s/test/sdv/testcases/25_Timing/031_tsdb_blob/data.csv';",
        dir);

    ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *expectContent = R"(1,0071 3520,24
2,0010 0000,24
4,,11
9,3102 0021,11
8,0000 0000,12
14,9021 6537,11
)";

    // 检查csv文件是否生成
    AW_MACRO_ASSERT_EQ_INT(true, isDataCsvExist());
    char actualContent[1024] = {0};
    (void)GetCsvFileContent(actualContent);
    // 检查获取到内容是否正确
    AW_MACRO_ASSERT_EQ_STR(expectContent, actualContent);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 046、新增列指定为blob类型数据，插入、读取数据
TEST_F(tsdb_blob_basic, Timing_031_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    DropCmTable("tablequery4");
    char name[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t age[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t id[] = {1, 2, 4, 9, 8, 14};
    int64_t worktime[] = {24, 24, 11, 11, 12, 11};
    int64_t salary[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message[][1600] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    uint32_t rowNum = 6;
    uint8_t *newmessage[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage[j] = message[j];
    }

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message "
        "blob(1600)) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        "tablequery4");

    Status ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, "tablequery4", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, salary, sizeof(salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage, sizeof(newmessage[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 新增列
    sprintf(ddlCommand, "alter table tablequery4 add message3 blob(1600);");
    ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t message3[][1600] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    uint8_t *newmessage3[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage3[j] = message3[j];
    }
    // 写入数据
    ret = GmcPrepareStmtByLabelName(stmt, "tablequery4", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, salary, sizeof(salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage, sizeof(newmessage[0]), NULL);
    ;
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 6, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage3, sizeof(newmessage3[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[1600] = {0};
    uint8_t messageRes3[1600] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11, 24, 24, 11, 11, 12, 11};
    uint8_t message2[][1600] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537", "0071 3520",
        "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    uint8_t message4[][1600] = {
        "", "", "", "", "", "", "0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select message,message3, worktime from tablequery4;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = 1600;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &messageRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = 1600;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &messageRes3, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message2[i], 1600), 0);
        AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes3, message4[i], 1600), 0);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);

        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)12);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 048、一个表没有blob类型，另一个表有blob类型，将一个表中扫描出几列后，插入到另一张表（不涉及blob列）
TEST_F(tsdb_blob_basic, Timing_031_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    DropCmTable("tablequery4");

    char names[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t ages[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t ids[] = {1, 2, 4, 9, 8, 14};
    int64_t worktimes[] = {24, 24, 11, 11, 12, 11};
    int64_t salaries[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message[][65535] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    constexpr uint32_t rowNum = 6;
    uint8_t *newmessage[rowNum];
    uint32_t newmessagelen[rowNum] = {9, 9, 0, 9, 9, 9};

    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage[j] = message[j];
    }

    Data data = {.name = names, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message blob) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        "tablequery4");

    Status ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, "tablequery4", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, data.name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &data.rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, data.age, sizeof(data.age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, data.id, sizeof(data.id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, data.worktime, sizeof(data.worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, data.salary, sizeof(data.salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage, sizeof(newmessage[0]), newmessagelen);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer) with"
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        "tablequery5");

    ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, "tablequery5", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, data.name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &data.rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, data.age, sizeof(data.age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, data.id, sizeof(data.id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, data.worktime, sizeof(data.worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, data.salary, sizeof(data.salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[65535] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11};

    // 查询insert into前表tablequery4数据
    isNull = false;
    uint32_t i = 0;
    const char *queryCommand = "select message, worktime from tablequery4;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }

        propSize = 65535;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &messageRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message[i], newmessagelen[i]), 0);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);

        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)6);

    // insert into 相当于不清除表tablequery5的数据，然后把tablequery4的数据往tablequery5后按数据插入
    char commandInsert[512] = "insert into tablequery4(worktime) select  worktime from tablequery5;";
    ret = GmcExecDirect(stmt, commandInsert, 512);
    EXPECT_EQ(ret, GMERR_OK);

    int64_t resultWorktime3[] = {24, 24, 11, 11, 12, 11, 24, 24, 11, 11, 12, 11};
    uint8_t message3[][65535] = {
        "0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537", "", "", "", "", "", ""};
    // 查询insert into后表tablequery5数据
    isNull = false;
    i = 0;
    queryCommand = "select message, worktime from tablequery4;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = 65535;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &messageRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (i < 6) {
            AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message3[i], newmessagelen[i]), 0);
        } else {
            AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message3[i], 0), 0);  // insert into是原表要设置数据长度
        }
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime3[i]);

        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)12);

    DropCmTable("tablequery5");
    AW_FUN_Log(LOG_STEP, "test end.");
}
void FreeMemForTextData(char **ns, int dataCount)
{
    if (ns == NULL) {
        return;
    }
    for (int i = 0; i < dataCount; i++) {
        if (ns[i] != NULL) {
            free(ns[i]);
            ns[i] = NULL;
        }
    }
    free(ns);
    ns = NULL;
}
// 为text类型数据申请内存
int32_t AllocMemForTextData(char ***ns, int dataCount)
{
    if (ns == NULL || dataCount <= 0) {
        return -1;
    }
    *ns = (char **)malloc(dataCount * sizeof(char *));
    if (*ns == NULL) {
        AW_FUN_Log(LOG_ERROR, "malloc ns failed\n");
        return FAILED;
    }
    for (int i = 0; i < dataCount; i++) {
        (*ns)[i] = (char *)malloc(65535 * sizeof(char));
        if ((*ns)[i] == NULL) {
            AW_FUN_Log(LOG_ERROR, "malloc ns[i] failed\n");
            FreeMemForTextData(*ns, i);  // 释放已分配部分
            return FAILED;
        }
        (void)memset((*ns)[i], 0, 65535);
    }
    return 0;
}
// 生成随机字符串
int32_t GenerateRandomString(char **textData, int dataCount)
{
    if (textData == NULL || dataCount <= 0) {
        return -1;
    }
    static bool srand_inited = false;
    if (!srand_inited) {
        srand((unsigned int)time(NULL));  // 初始化一次
        srand_inited = true;
    }
    char charset[] = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    int charset_len = sizeof(charset) - 1;
    for (int i = 0; i < dataCount; i++) {
        int textLength = 65535;
        char str[65535] = {0};
        for (int n = 0; n < textLength - 1; n++) {
            int key = rand() % charset_len;
            str[n] = charset[key];
        }
        str[textLength - 1] = '\0';
        (void)sprintf(textData[i], "%s", str);  // 修正参数顺序
    }
    return 0;
}
// 049、blob类型写入数据长度为65535
TEST_F(tsdb_blob_basic, Timing_031_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    DropCmTable("tablequery4");
    char name[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t age[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t id[] = {1, 2, 4, 9, 8, 14};
    int64_t worktime[] = {24, 24, 11, 11, 12, 11};
    int64_t salary[] = {20000, 10000, 4000, 30000, 31000, 10000};
    constexpr uint32_t rowNum = 6;
    char **message = NULL;
    int32_t dataCount = 1000;
    ret = AllocMemForTextData(&message, rowNum);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GenerateRandomString(message, rowNum);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char *newmessage[rowNum];
    uint32_t messagelen[rowNum] = {65535, 65535, 65535, 65535, 65535, 65535};
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage[j] = message[j];
    }

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message "
        "blob(65535)) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        "tablequery4");

    Status ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, "tablequery4", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, salary, sizeof(salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage, 65535, messagelen);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    char messageRes[65535] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select message, worktime from tablequery4;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = 65535;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &messageRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message[i], messagelen[i]), 0);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);

        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)6);
    FreeMemForTextData(message, rowNum);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 050、blob类型写入数据长度为0
TEST_F(tsdb_blob_basic, Timing_031_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    DropCmTable("tablequery4");
    char name[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t age[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t id[] = {1, 2, 4, 9, 8, 14};
    int64_t worktime[] = {24, 24, 11, 11, 12, 11};
    int64_t salary[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message[][1000] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    constexpr uint32_t rowNum = 6;
    uint8_t *newmessage[rowNum];
    uint32_t messagelen[rowNum] = {9, 9, 0, 9, 9, 9};
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage[j] = message[j];
    }

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message "
        "blob(65535)) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        "tablequery4");

    Status ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, "tablequery4", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, salary, sizeof(salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage, 0, messagelen);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_INVALID_PARAMETER_VALUE);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);

    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[1000] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select message, worktime from tablequery4;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = 1000;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &messageRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message[i], messagelen[i]), 0);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);

        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)0);
    AddWhiteList(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 051.char 类型写入数据长度为1
TEST_F(tsdb_blob_basic, Timing_031_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    DropCmTable("tablequery4");
    char name[6] = {'a', 'b', 'c', 'd', 'e', 'F'};
    int64_t age[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t id[] = {1, 2, 4, 9, 8, 14};
    int64_t worktime[] = {24, 24, 11, 11, 12, 11};
    int64_t salary[] = {20000, 10000, 4000, 30000, 31000, 10000};
    constexpr uint32_t rowNum = 6;
    char **message = NULL;
    int32_t dataCount = 1000;
    ret = AllocMemForTextData(&message, rowNum);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GenerateRandomString(message, rowNum);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char *newmessage[rowNum];
    uint32_t messagelen[rowNum] = {65535, 65535, 65535, 65535, 65535, 65535};
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage[j] = message[j];
    }

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(1), age integer, id integer, worktime integer, salary integer,  message "
        "blob(65535)) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        "tablequery4");

    Status ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, "tablequery4", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, name, 1, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, salary, sizeof(salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage, 65535, messagelen);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    char messageRes[65535] = {0};
    char nameRes[1] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select  name, message, worktime from tablequery4;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = 1;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &nameRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = 65535;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &messageRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(nameRes[0], name[i]);
        AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message[i], messagelen[i]), 0);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);

        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)6);
    FreeMemForTextData(message, rowNum);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 052.char 类型写入数据长度为65535
TEST_F(tsdb_blob_basic, Timing_031_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    DropCmTable("tablequery4");
    int64_t age[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t id[] = {1, 2, 4, 9, 8, 14};
    int64_t worktime[] = {24, 24, 11, 11, 12, 11};
    int64_t salary[] = {20000, 10000, 4000, 30000, 31000, 10000};
    constexpr uint32_t rowNum = 6;
    char **message = NULL;
    ret = AllocMemForTextData(&message, rowNum);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GenerateRandomString(message, rowNum);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int nameLen = 65535;
    char name[rowNum][nameLen] = {0};
    char charset[] = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    int charsetLen = sizeof(charset) - 1;
    for (int i = 0; i < rowNum; i++) {
        for (int n = 0; n < nameLen - 1; n++) {
            int key = rand() % charsetLen;
            name[i][n] = charset[key];
        }
        name[i][nameLen - 1] = '\0';
    }
    char *newmessage[rowNum];
    uint32_t messagelen[rowNum] = {65535, 65535, 65535, 65535, 65535, 65535};
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage[j] = message[j];
    }

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(65535), age integer, id integer, worktime integer, salary integer,  message "
        "blob(65535)) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        "tablequery4");

    Status ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, "tablequery4", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, name, nameLen, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, salary, sizeof(salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage, 65535, messagelen);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    char messageRes[65535] = {0};
    char nameRes[nameLen] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select name, message, worktime from tablequery4;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = nameLen;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &nameRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = 65535;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &messageRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(memcmp(nameRes, name[i], nameLen), 0);
        AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message[i], messagelen[i]), 0);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)6);
    FreeMemForTextData(message, rowNum);
    AW_FUN_Log(LOG_STEP, "test end.");
}
