/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

extern "C" {}
#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include <sys/time.h>
#include <time.h>
#include <sys/ipc.h>
#include <sys/shm.h>
#include <malloc.h>

#include "gtest/gtest.h"
#include "tsdb_view_enhancement.h"

// 内存申请大小限制
#if defined ENV_RTOSV2X
#define MEGABYTE (10 * 1024)
#else
#define MEGABYTE (1024 * 1024 * 1024)
#endif

int ret = 0;
int thr_count = 0;

class tsdb_view_enhancement : public testing::Test {
public:
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}
    virtual void SetUp()
    {
        system("ipcrm -a");  // 避免被其他用例影响
        InitTsCiCfg();
        ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建epoll监听线程
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        char sqlCmd[256] = {0};
        (void)sprintf(sqlCmd, "drop table %s;", g_tableName);
        uint32_t cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);

        char ddlCommand[512];
        snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
            "create table %s(name char(64),  id integer, worktime integer,vsysId TEXT) with "
            "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)');",
            g_tableName);

        ret = GmcExecDirect(g_stmt_sync, ddlCommand, 512);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    }
    virtual void TearDown()
    {
        char sqlCmd[256] = {0};
        (void)sprintf(sqlCmd, "drop table %s;", g_tableName);
        uint32_t cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

        ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        system("sh $TEST_HOME/tools/modifyCfg.sh -ts recover");
        close_epoll_thread();
        testEnvClean();
    }
};

// 表不存在，查询视图STORAGE_DISK_USAGE数据
TEST_F(tsdb_view_enhancement, Timing_063_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd, "drop table %s;", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char g_command[1024];
    snprintf(g_command, 1024,
        "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where TABLE_NAME='testdb' \" -s %s", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(64),  id integer, worktime integer,vsysId TEXT) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        g_tableName);

    ret = GmcExecDirect(g_stmt_sync, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char sqlCmd2[512] = {0};
    (void)sprintf(sqlCmd2, "SELECT * FROM %s;", g_tableName);
    uint32_t cmdLen2 = strlen(sqlCmd2);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd2, cmdLen2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, dataCount);

    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "PHY_TBL_NUM: 0", "ROW_CNT: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // ls -l获取本地文件大小
    int folderSize1 = GetFolderSize(g_folderPath1);
    AW_FUN_Log(LOG_STEP, "folderSize1: %d bytes \n", folderSize1);

    // 视图获取文件大小
    char const *viewName = "V\\$STORAGE_DISK_USAGE";
    uint64_t getValueBefore1 = 0;
    getDBOperStatis("ROW_DISK_USAGE", &getValueBefore1, viewName);
    AW_FUN_Log(LOG_STEP, "getValueBefore1: %d bytes \n", getValueBefore1);

    // 结果对比
    AW_MACRO_EXPECT_EQ_INT(folderSize1, getValueBefore1);

    // 视图获取文件大小
    uint64_t getValueBefore2 = 0;
    getDBOperStatis("COL_DISK_USAGE", &getValueBefore2, viewName);
    AW_FUN_Log(LOG_STEP, "getValueBefore2: %d bytes \n", getValueBefore2);

    int folderSize = 0;
    AW_FUN_Log(LOG_STEP, "folderSize: %d bytes \n", folderSize);
    // 结果对比
    AW_MACRO_EXPECT_EQ_INT(folderSize, getValueBefore2);

    // 视图获取文件大小
    uint64_t getValueBefore3 = 0;
    getDBOperStatis("DISK_USAGE", &getValueBefore3, viewName);
    AW_FUN_Log(LOG_STEP, "getValueBefore3: %d bytes \n", getValueBefore3);

    // 结果对比
    AW_MACRO_EXPECT_EQ_INT(getValueBefore1 + getValueBefore2, getValueBefore3);
}

// 单表写入单分区数据，查询视图STORAGE_DISK_USAGE数据
TEST_F(tsdb_view_enhancement, Timing_063_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = WriteDataSinglePartition(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    FolderPathWrite();
    char g_command[1024];
    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "PHY_TBL_NUM: 1", "ROW_CNT: 10000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // ls -l获取本地文件大小
    int folderSize1 = GetFolderSize(g_folderPath1);
    AW_FUN_Log(LOG_STEP, "folderSize1: %d bytes \n", folderSize1);

    // 视图获取文件大小
    char const *viewName = "V\\$STORAGE_DISK_USAGE";
    uint64_t getValueBefore1 = 0;
    getDBOperStatis("ROW_DISK_USAGE", &getValueBefore1, viewName);
    AW_FUN_Log(LOG_STEP, "getValueBefore1: %d bytes \n", getValueBefore1);

    // 结果对比
    AW_MACRO_EXPECT_EQ_INT(folderSize1, getValueBefore1);

    // ls -l获取本地文件大小
    int folderSize2 = GetFolderSize(g_folderPath2);
    AW_FUN_Log(LOG_STEP, "folderSize2: %d bytes \n", folderSize2);

    int folderSize3 = GetFolderSize(g_folderPath3);
    AW_FUN_Log(LOG_STEP, "folderSize3: %d bytes \n", folderSize3);

    int folderSize = folderSize2 + folderSize3;
    AW_FUN_Log(LOG_STEP, "folderSize: %d bytes \n", folderSize);

    // 视图获取文件大小
    uint64_t getValueBefore2 = 0;
    getDBOperStatis("COL_DISK_USAGE", &getValueBefore2, viewName);
    AW_FUN_Log(LOG_STEP, "getValueBefore2: %d bytes \n", getValueBefore2);

    // 结果对比
    AW_MACRO_EXPECT_EQ_INT(folderSize, getValueBefore2);

    // 视图获取文件大小
    uint64_t getValueBefore3 = 0;
    getDBOperStatis("DISK_USAGE", &getValueBefore3, viewName);
    AW_FUN_Log(LOG_STEP, "getValueBefore3: %d bytes \n", getValueBefore3);

    // 结果对比
    AW_MACRO_EXPECT_EQ_INT(getValueBefore1 + getValueBefore2, getValueBefore3);
}

// 单表写入多分区数据，查询视图STORAGE_DISK_USAGE数据
TEST_F(tsdb_view_enhancement, Timing_063_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = WriteDataDualPartition(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char g_command[1024];
    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "PHY_TBL_NUM: 3", "ROW_CNT: 10000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // ls -l获取本地文件大小
    int folderSize1 = GetFolderSize(g_folderPath1);
    AW_FUN_Log(LOG_STEP, "folderSize1: %d bytes \n", folderSize1);

    // 视图获取文件大小
    char const *viewName = "V\\$STORAGE_DISK_USAGE";
    uint64_t getValueBefore1 = 0;
    getDBOperStatis("ROW_DISK_USAGE", &getValueBefore1, viewName);
    AW_FUN_Log(LOG_STEP, "getValueBefore1: %d bytes \n", getValueBefore1);

    // 结果对比
    AW_MACRO_EXPECT_EQ_INT(folderSize1, getValueBefore1);

    // 视图获取文件大小
    uint64_t getValueBefore2 = 0;
    getDBOperStatis("COL_DISK_USAGE", &getValueBefore2, viewName);
    AW_FUN_Log(LOG_STEP, "getValueBefore2: %d bytes \n", getValueBefore2);

    // 视图获取文件大小
    uint64_t getValueBefore3 = 0;
    getDBOperStatis("DISK_USAGE", &getValueBefore3, viewName);
    AW_FUN_Log(LOG_STEP, "getValueBefore3: %d bytes \n", getValueBefore3);

    // 结果对比
    AW_MACRO_EXPECT_EQ_INT(getValueBefore1 + getValueBefore2, getValueBefore3);
}

// 多表写入多分区数据，查询视图STORAGE_DISK_USAGE数据
TEST_F(tsdb_view_enhancement, Timing_063_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd, "drop table %s;", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    int tableNum = 3;
    for (int j = 0; j < tableNum; j++) {
        (void)sprintf(g_tableName, "testdb%d", j);
        char sqlCmd[256] = {0};
        (void)sprintf(sqlCmd, "drop table %s;", g_tableName);
        uint32_t cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);

        char ddlCommand[512];
        snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
            "create table %s(name char(64),  id integer, worktime integer,vsysId TEXT) with "
            "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
            g_tableName);

        ret = GmcExecDirect(g_stmt_sync, ddlCommand, 512);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

        constexpr uint32_t count = 10000;
        int64_t ids[count] = {0};
        int64_t worktimes[count] = {0};
        char *names = (char *)malloc(count * 64);
        char nameSource[10][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
        char **vsysId = (char **)malloc(count * sizeof(char *));

        for (int i = 0; i < count; i++) {
            ids[i] = i + 1;
            worktimes[i] = 1695042000 + i;
            if (i % 10 == 0) {
                memcpy((names + i * 64), (char *)nameSource, 640);
            }
            vsysId[i] = (char *)malloc(TEXT_LEN * sizeof(char));
            (void)memset(vsysId[i], 0, TEXT_LEN);
        }

        GenerateRandomString(vsysId, count);

        uint32_t rowNum = count;
        ret = GmcPrepareStmtByLabelName(g_stmt_sync, g_tableName, GMC_OPERATION_SQL_INSERT);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(g_stmt_sync, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, names, 64, NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(g_stmt_sync, 1, (GmcDataTypeE)DB_DATATYPE_INT64, ids, sizeof(ids[0]), NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(g_stmt_sync, 2, (GmcDataTypeE)DB_DATATYPE_INT64, worktimes, sizeof(worktimes[0]), NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(g_stmt_sync, 3, (GmcDataTypeE)DB_DATATYPE_STRING, vsysId, sizeof(char *), 0);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcSetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcExecute(g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        free(names);
        for (int i = 0; i < count; ++i) {
            free(vsysId[i]);  // 释放每个 char* 指向的内存
        }
        free(vsysId);  // 释放指针数组本身的内存

        char sqlCmd2[512] = {0};
        (void)sprintf(sqlCmd2, "SELECT * FROM %s;", g_tableName);
        uint32_t cmdLen2 = strlen(sqlCmd2);
        ret = GmcExecDirect(g_stmt_sync, sqlCmd2, cmdLen2);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

        int64_t dataCount = 0;
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(count, dataCount);
    }
    char g_command[1024];
    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "PHY_TBL_NUM: 3", "ROW_CNT: 10000", "index = 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // ls -l获取本地文件大小
    int folderSize1 = GetFolderSize(g_folderPath1);
    AW_FUN_Log(LOG_STEP, "folderSize1: %d bytes \n", folderSize1);

    // 视图获取文件大小
    char const *viewName = "V\\$STORAGE_DISK_USAGE";
    uint64_t getValueBefore1 = 0;
    getDBOperStatis("ROW_DISK_USAGE", &getValueBefore1, viewName);
    AW_FUN_Log(LOG_STEP, "getValueBefore1: %d bytes \n", getValueBefore1);

    // 结果对比
    AW_MACRO_EXPECT_EQ_INT(folderSize1, getValueBefore1 * tableNum);

    // 视图获取文件大小
    uint64_t getValueBefore2 = 0;
    getDBOperStatis("COL_DISK_USAGE", &getValueBefore2, viewName);
    AW_FUN_Log(LOG_STEP, "getValueBefore2: %d bytes \n", getValueBefore2);

    // 视图获取文件大小
    uint64_t getValueBefore3 = 0;
    getDBOperStatis("DISK_USAGE", &getValueBefore3, viewName);
    AW_FUN_Log(LOG_STEP, "getValueBefore3: %d bytes \n", getValueBefore3);

    // 结果对比
    AW_MACRO_EXPECT_EQ_INT(getValueBefore1 + getValueBefore2, getValueBefore3);

    for (int j = 0; j < tableNum; j++) {
        (void)sprintf(g_tableName, "testdb%d", j);
        char sqlCmd[256] = {0};
        (void)sprintf(sqlCmd, "drop table %s;", g_tableName);
        uint32_t cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    }

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(64),  id integer, worktime integer,vsysId TEXT) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        g_tableName);

    ret = GmcExecDirect(g_stmt_sync, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
}

// 写入多分区数据，使用aging_func删除部分分区数据，查询视图STORAGE_DISK_USAGE数据，使用aging_func删除全部分区数据，继续查询视图STORAGE_DISK_USAGE数据，
TEST_F(tsdb_view_enhancement, Timing_063_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = WriteDataDualPartition(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char g_command[1024];
    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "PHY_TBL_NUM: 3", "ROW_CNT: 10000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // ls -l获取本地文件大小
    int folderSize1 = GetFolderSize(g_folderPath1);
    AW_FUN_Log(LOG_STEP, "folderSize1: %d bytes \n", folderSize1);

    // 视图获取文件大小
    char const *viewName = "V\\$STORAGE_DISK_USAGE";
    uint64_t getValueBefore1 = 0;
    getDBOperStatis("ROW_DISK_USAGE", &getValueBefore1, viewName);
    AW_FUN_Log(LOG_STEP, "getValueBefore1: %d bytes \n", getValueBefore1);

    // 结果对比
    AW_MACRO_EXPECT_EQ_INT(folderSize1, getValueBefore1);

    // 视图获取文件大小
    uint64_t getValueBefore2 = 0;
    getDBOperStatis("COL_DISK_USAGE", &getValueBefore2, viewName);
    AW_FUN_Log(LOG_STEP, "getValueBefore2: %d bytes \n", getValueBefore2);

    // 视图获取文件大小
    uint64_t getValueBefore3 = 0;
    getDBOperStatis("DISK_USAGE", &getValueBefore3, viewName);
    AW_FUN_Log(LOG_STEP, "getValueBefore3: %d bytes \n", getValueBefore3);

    // 结果对比
    AW_MACRO_EXPECT_EQ_INT(getValueBefore1 + getValueBefore2, getValueBefore3);

    for (int j = 1; j <= 3; j++) {
        char sqlCmd3[256] = {0};
        uint32_t cmdLen3 = 0;
        (void)sprintf(sqlCmd3, "SELECT tsdb_aging('%s');", g_tableName);
        cmdLen3 = strlen(sqlCmd3);
        ret = GmcExecDirect(g_stmt_sync, sqlCmd3, cmdLen3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        char g_command[1024];
        snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
        system(g_command);
        ret = executeCommand(g_command);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // ls -l获取本地文件大小
        int folderSize1 = GetFolderSize(g_folderPath1);
        AW_FUN_Log(LOG_STEP, "folderSize1: %d bytes \n", folderSize1);

        // 视图获取文件大小
        char const *viewName = "V\\$STORAGE_DISK_USAGE";
        uint64_t getValueBefore1 = 0;
        getDBOperStatis("ROW_DISK_USAGE", &getValueBefore1, viewName);
        AW_FUN_Log(LOG_STEP, "getValueBefore1: %d bytes \n", getValueBefore1);

        // 结果对比
        AW_MACRO_EXPECT_EQ_INT(folderSize1, getValueBefore1);

        // 视图获取文件大小
        uint64_t getValueBefore2 = 0;
        getDBOperStatis("COL_DISK_USAGE", &getValueBefore2, viewName);
        AW_FUN_Log(LOG_STEP, "getValueBefore2: %d bytes \n", getValueBefore2);

        // 视图获取文件大小
        uint64_t getValueBefore3 = 0;
        getDBOperStatis("DISK_USAGE", &getValueBefore3, viewName);
        AW_FUN_Log(LOG_STEP, "getValueBefore3: %d bytes \n", getValueBefore3);

        // 结果对比
        AW_MACRO_EXPECT_EQ_INT(getValueBefore1 + getValueBefore2, getValueBefore3);

        // 视图获取文件大小
        uint64_t getValueBefore4 = 0;
        getDBOperStatis("PHY_TBL_NUM", &getValueBefore4, viewName);
        AW_FUN_Log(LOG_STEP, "getValueBefore4: %d bytes \n", getValueBefore4);

        // 结果对比
        AW_MACRO_EXPECT_EQ_INT(3 - j, getValueBefore4);
    }

    // 视图获取文件大小
    char const *viewName5 = "V\\$STORAGE_DISK_USAGE";
    uint64_t getValueBefore5 = 0;
    getDBOperStatis("ROW_CNT", &getValueBefore5, viewName5);
    AW_FUN_Log(LOG_STEP, "getValueBefore5: %d bytes \n", getValueBefore5);

    // 结果对比
    AW_MACRO_EXPECT_EQ_INT(0, getValueBefore5);
}

// 写入多分区数据，ttl删除部分分区数据，查询视图STORAGE_DISK_USAGE数据，使用ttl删除全部分区数据，继续查询视图STORAGE_DISK_USAGE数据，
TEST_F(tsdb_view_enhancement, Timing_063_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"tsLcmCheckPeriod=3\" ");
    system("sh $TEST_HOME/tools/stop.sh -ts");
    TsDefulatDbFileClean();
    system("sh $TEST_HOME/tools/start.sh -ts");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd, "drop table %s;", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(64),  id integer, worktime integer,vsysId TEXT) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '1 hours');",
        g_tableName);

    ret = GmcExecDirect(g_stmt_sync, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    constexpr uint32_t count = 10000;
    int64_t ids[count] = {0};
    int64_t worktimes[count] = {0};
    char *names = (char *)malloc(count * 64);
    char nameSource[10][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
    char **vsysId = (char **)malloc(count * sizeof(char *));

    for (int i = 0; i < count; i++) {
        ids[i] = i + 1;
        worktimes[i] = 0;
        if (i % 10 == 0) {
            memcpy((names + i * 64), (char *)nameSource, 640);
        }
        vsysId[i] = (char *)malloc(TEXT_LEN * sizeof(char));
        (void)memset(vsysId[i], 0, TEXT_LEN);
    }

    GenerateRandomString(vsysId, count);

    uint32_t rowNum = count;
    sleep(5);
    uint32_t j = 0;

    ret = GmcPrepareStmtByLabelName(g_stmt_sync, g_tableName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(g_stmt_sync, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, names, 64, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(g_stmt_sync, 1, (GmcDataTypeE)DB_DATATYPE_INT64, ids, sizeof(ids[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(g_stmt_sync, 2, (GmcDataTypeE)DB_DATATYPE_INT64, worktimes, sizeof(worktimes[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(g_stmt_sync, 3, (GmcDataTypeE)DB_DATATYPE_STRING, vsysId, sizeof(char *), 0);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(g_stmt_sync);

    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    free(names);
    sleep(5);
    char sqlCmd2[512] = {0};
    (void)sprintf(sqlCmd2, "SELECT * FROM %s;", g_tableName);
    uint32_t cmdLen2 = strlen(sqlCmd2);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd2, cmdLen2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, dataCount);

    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "PHY_TBL_NUM: 0", "ROW_CNT: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // ls -l获取本地文件大小
    int folderSize1 = GetFolderSize(g_folderPath1);
    AW_FUN_Log(LOG_STEP, "folderSize1: %d bytes \n", folderSize1);

    // 视图获取文件大小
    char const *viewName = "V\\$STORAGE_DISK_USAGE";
    uint64_t getValueBefore1 = 0;
    getDBOperStatis("ROW_DISK_USAGE", &getValueBefore1, viewName);
    AW_FUN_Log(LOG_STEP, "getValueBefore1: %d bytes \n", getValueBefore1);

    // 结果对比
    AW_MACRO_EXPECT_EQ_INT(folderSize1, getValueBefore1);

    // 视图获取文件大小
    uint64_t getValueBefore2 = 0;
    getDBOperStatis("COL_DISK_USAGE", &getValueBefore2, viewName);
    AW_FUN_Log(LOG_STEP, "getValueBefore2: %d bytes \n", getValueBefore2);

    int folderSize = 0;
    AW_FUN_Log(LOG_STEP, "folderSize: %d bytes \n", folderSize);
    // 结果对比
    AW_MACRO_EXPECT_EQ_INT(folderSize, getValueBefore2);

    // 视图获取文件大小
    uint64_t getValueBefore3 = 0;
    getDBOperStatis("DISK_USAGE", &getValueBefore3, viewName);
    AW_FUN_Log(LOG_STEP, "getValueBefore3: %d bytes \n", getValueBefore3);

    // 结果对比
    AW_MACRO_EXPECT_EQ_INT(getValueBefore1 + getValueBefore2, getValueBefore3);
}

// 表不存在，查询视图PHY_TBL_DISK_USAGE数据
TEST_F(tsdb_view_enhancement, Timing_063_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd, "drop table %s;", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char g_command[1024];
    snprintf(g_command, 1024,
        "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE' where TABLE_NAME='testdb' \" -s %s", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(64),  id integer, worktime integer,vsysId TEXT) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        g_tableName);

    ret = GmcExecDirect(g_stmt_sync, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char sqlCmd2[512] = {0};
    (void)sprintf(sqlCmd2, "SELECT * FROM %s;", g_tableName);
    uint32_t cmdLen2 = strlen(sqlCmd2);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd2, cmdLen2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, dataCount);

    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE'\" -s %s", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 写入单分区数据，查询视图PHY_TBL_DISK_USAGE、STORAGE_DISK_USAGE数据、接口查询
TEST_F(tsdb_view_enhancement, Timing_063_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = WriteDataSinglePartition(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    FolderPathWrite();
    char g_command[1024];
    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE'\" -s %s", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "TABLE_ID: 10486", "TABLE_NAME: testdb",
        // 暂时取消校验该值" LOGIC_LABEL_ID: 1048600",
        "UPPER_BOUND:", "UPPER_BOUND_TIME_STR");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "INTERVAL: 3600 seconds", "CU_CNT: 1", "ROW_CNT: 10000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "ROW_CNT: 10000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // ls -l获取本地文件大小
    int folderSize1 = GetFolderSize(g_folderPath1);
    AW_FUN_Log(LOG_STEP, "folderSize1: %d bytes \n", folderSize1);

    // 视图获取文件大小
    char const *viewName = "V\\$PHY_TBL_DISK_USAGE";
    uint64_t getValueBefore1 = 0;
    getDBOperStatis("ROW_DISK_USAGE", &getValueBefore1, viewName);
    AW_FUN_Log(LOG_STEP, "getValueBefore1: %d bytes \n", getValueBefore1);

    // 结果对比
    AW_MACRO_EXPECT_EQ_INT(folderSize1, getValueBefore1);

    // ls -l获取本地文件大小
    int folderSize2 = GetFolderSize(g_folderPath2);
    AW_FUN_Log(LOG_STEP, "folderSize2: %d bytes \n", folderSize2);

    int folderSize3 = GetFolderSize(g_folderPath3);
    AW_FUN_Log(LOG_STEP, "folderSize3: %d bytes \n", folderSize3);

    int folderSize = folderSize2 + folderSize3;
    AW_FUN_Log(LOG_STEP, "folderSize: %d bytes \n", folderSize);

    // 视图获取文件大小
    uint64_t getValueBefore2 = 0;
    getDBOperStatis("COL_DISK_USAGE", &getValueBefore2, viewName);
    AW_FUN_Log(LOG_STEP, "getValueBefore2: %d bytes \n", getValueBefore2);

    // 结果对比
    AW_MACRO_EXPECT_EQ_INT(folderSize, getValueBefore2);
}

// 写入多分区数据，查询视图PHY_TBL_DISK_USAGE、STORAGE_DISK_USAGE数据、接口查询
TEST_F(tsdb_view_enhancement, Timing_063_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = WriteDataDualPartition(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    FolderPathWrite();
    char g_command[1024];
    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE'\" -s %s", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "TABLE_ID: 10486", "TABLE_NAME: testdb",
        // 暂时取消校验该值" LOGIC_LABEL_ID: 1048600",
        "UPPER_BOUND:", "UPPER_BOUND_TIME_STR");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "INTERVAL: 3600 seconds", "CU_CNT: 1", "index = 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_command[1024];
    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "PHY_TBL_NUM: 3", "ROW_CNT: 10000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 写入多分区数据，查询视图PHY_TBL_DISK_USAGE数据，使用aging_func删除部分分区数据，继续查询视图PHY_TBL_DISK_USAGE数据
TEST_F(tsdb_view_enhancement, Timing_063_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = WriteDataDualPartition(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    FolderPathWrite();
    char g_command[1024];
    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE'\" -s %s", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "TABLE_ID: 10486", "TABLE_NAME: testdb",
        // 暂时取消校验该值" LOGIC_LABEL_ID: 1048600",
        "UPPER_BOUND:", "UPPER_BOUND_TIME_STR");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "INTERVAL: 3600 seconds", "CU_CNT: 1", "index = 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int j = 1; j <= 3; j++) {
        char sqlCmd3[256] = {0};
        uint32_t cmdLen3 = 0;
        (void)sprintf(sqlCmd3, "SELECT tsdb_aging('%s');", g_tableName);
        cmdLen3 = strlen(sqlCmd3);
        ret = GmcExecDirect(g_stmt_sync, sqlCmd3, cmdLen3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (j == 1) {
            char g_command[1024];
            snprintf(
                g_command, 1024, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE'\" -s %s", g_connServerTsdb);
            system(g_command);
            ret = executeCommand(g_command, "index = 1");
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = executeCommand(g_command, "index = 2");
            AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
        }
    }

    // 视图获取文件大小
    char const *viewName5 = "V\\$STORAGE_DISK_USAGE";
    uint64_t getValueBefore5 = 0;
    getDBOperStatis("ROW_CNT", &getValueBefore5, viewName5);
    AW_FUN_Log(LOG_STEP, "getValueBefore5: %d bytes \n", getValueBefore5);

    // 结果对比
    AW_MACRO_EXPECT_EQ_INT(0, getValueBefore5);
}

// 写入多分区数据，查询视图PHY_TBL_DISK_USAGE数据，Disk_Limit删除部分分区数据，继续查询视图PHY_TBL_DISK_USAGE数据
TEST_F(tsdb_view_enhancement, Timing_063_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = WriteDataDualPartition(g_stmt_sync, 100000, 100000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    FolderPathWrite();
    char g_command[1024];
    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE'\" -s %s", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "TABLE_ID: 10486", "TABLE_NAME: testdb",
        // 暂时取消校验该值" LOGIC_LABEL_ID: 1048600",
        "UPPER_BOUND:", "UPPER_BOUND_TIME_STR");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "INTERVAL: 3600 seconds", "CU_CNT: 1", "index = 27");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 在线修改disk_limit
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "alter table %s set (disk_limit = '10 MB')", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE'\" -s %s", g_connServerTsdb);
    system(g_command);

#if defined(CPU_BIT_32) && defined(CPU_ARC_ARM)
    ret = executeCommand(g_command, "TABLE_ID: 10486", "TABLE_NAME: testdb",
        // 暂时取消校验该值" LOGIC_LABEL_ID: 1048600",
        "index = 22");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#else
    ret = executeCommand(g_command, "TABLE_ID: 10486", "TABLE_NAME: testdb",
        // 暂时取消校验该值" LOGIC_LABEL_ID: 1048600",
        "index = 24");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
}

// 写入多分区数据，查询视图PHY_TBL_DISK_USAGE数据，TTL删除所有分区数据，继续查询视图PHY_TBL_DISK_USAGE数据
TEST_F(tsdb_view_enhancement, Timing_063_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"tsLcmCheckPeriod=3\" ");
    system("sh $TEST_HOME/tools/stop.sh -ts");
    TsDefulatDbFileClean();
    system("sh $TEST_HOME/tools/start.sh -ts");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd, "drop table %s;", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(64),  id integer, worktime integer,vsysId TEXT) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '1 hours');",
        g_tableName);

    ret = GmcExecDirect(g_stmt_sync, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    constexpr uint32_t count = 10000;
    int64_t ids[count] = {0};
    int64_t worktimes[count] = {0};
    char *names = (char *)malloc(count * 64);
    char nameSource[10][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
    char **vsysId = (char **)malloc(count * sizeof(char *));

    for (int i = 0; i < count; i++) {
        ids[i] = i + 1;
        worktimes[i] = 0;
        if (i % 10 == 0) {
            memcpy((names + i * 64), (char *)nameSource, 640);
        }
        vsysId[i] = (char *)malloc(TEXT_LEN * sizeof(char));
        (void)memset(vsysId[i], 0, TEXT_LEN);
    }

    GenerateRandomString(vsysId, count);

    uint32_t rowNum = count;
    sleep(5);
    uint32_t j = 0;

    ret = GmcPrepareStmtByLabelName(g_stmt_sync, g_tableName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(g_stmt_sync, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, names, 64, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(g_stmt_sync, 1, (GmcDataTypeE)DB_DATATYPE_INT64, ids, sizeof(ids[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(g_stmt_sync, 2, (GmcDataTypeE)DB_DATATYPE_INT64, worktimes, sizeof(worktimes[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(g_stmt_sync, 3, (GmcDataTypeE)DB_DATATYPE_STRING, vsysId, sizeof(char *), 0);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(g_stmt_sync);

    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    free(names);
    sleep(5);
    char sqlCmd2[512] = {0};
    (void)sprintf(sqlCmd2, "SELECT * FROM %s;", g_tableName);
    uint32_t cmdLen2 = strlen(sqlCmd2);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd2, cmdLen2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, dataCount);

    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE'\" -s %s", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 表不存在，查询视图TS_TBL_OPER_STATIS数据
TEST_F(tsdb_view_enhancement, Timing_063_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd, "drop table %s;", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char g_command[1024];
    snprintf(g_command, 1024,
        "gmsysview -sql \"select * from 'V\\$TS_TBL_OPER_STATIS' where TABLE_NAME='testdb' \" -s %s", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(64),  id integer, worktime integer,vsysId TEXT) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        g_tableName);

    ret = GmcExecDirect(g_stmt_sync, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char sqlCmd2[512] = {0};
    (void)sprintf(sqlCmd2, "SELECT * FROM %s;", g_tableName);
    uint32_t cmdLen2 = strlen(sqlCmd2);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd2, cmdLen2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, dataCount);

    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$TS_TBL_OPER_STATIS'\" -s %s", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "TABLE_NAME: testdb", "DML_SUCCESS_CNT: 0", "DML_TOTAL_FAILED_CNT: 0",
        "DML_DISK_FAILED_CNT: 0", "DQL_SUCCESS_CNT: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, " DQL_TOTAL_FAILED_CNT: 0", "DQL_DISK_FAILED_CNT: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 批量写入1w数据，查询视图TS_TBL_OPER_STATIS数据DML_SUCCESS_CNT
TEST_F(tsdb_view_enhancement, Timing_063_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = WriteDataDualPartition(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$TS_TBL_OPER_STATIS'\" -s %s", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "TABLE_NAME: testdb", "DML_SUCCESS_CNT: 1", "DML_TOTAL_FAILED_CNT: 0",
        "DML_DISK_FAILED_CNT: 0", "DQL_SUCCESS_CNT: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, " DQL_TOTAL_FAILED_CNT: 0", "DQL_DISK_FAILED_CNT: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 单条写1w次总共1w数据，查询视图TS_TBL_OPER_STATIS数据DML_SUCCESS_CNT
TEST_F(tsdb_view_enhancement, Timing_063_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    constexpr uint32_t count = 10000;
    int64_t ids[count] = {0};
    int64_t worktimes[count] = {0};
    char *names = (char *)malloc(count * 64);
    char nameSource[10][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
    char **vsysId = (char **)malloc(count * sizeof(char *));

    for (int i = 0; i < count; i++) {
        ids[i] = i + 1;
        worktimes[i] = 1695042000 + i % 3600;
        if (i % 10 == 0) {
            memcpy((names + i * 64), (char *)nameSource, 640);
        }
        vsysId[i] = (char *)malloc(TEXT_LEN * sizeof(char));
        (void)memset(vsysId[i], 0, TEXT_LEN);
    }

    GenerateRandomString(vsysId, count);

    for (int j = 0; j < count; j++) {
        uint32_t rowNum = count / count;
        ret = GmcPrepareStmtByLabelName(g_stmt_sync, g_tableName, GMC_OPERATION_SQL_INSERT);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(g_stmt_sync, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, names, 64, NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(g_stmt_sync, 1, (GmcDataTypeE)DB_DATATYPE_INT64, ids, sizeof(ids[0]), NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(g_stmt_sync, 2, (GmcDataTypeE)DB_DATATYPE_INT64, worktimes, sizeof(worktimes[0]), NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(g_stmt_sync, 3, (GmcDataTypeE)DB_DATATYPE_STRING, vsysId, sizeof(char *), 0);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcSetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcExecute(g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    }
    free(names);

    char sqlCmd2[512] = {0};
    (void)sprintf(sqlCmd2, "SELECT * FROM %s;", g_tableName);
    uint32_t cmdLen2 = strlen(sqlCmd2);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd2, cmdLen2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(1);  // 数据量大，环境不同查询性能不稳定
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(count, dataCount);

    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$TS_TBL_OPER_STATIS'\" -s %s", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "TABLE_NAME: testdb", "DML_SUCCESS_CNT: 10000", "DML_TOTAL_FAILED_CNT: 0",
        "DML_DISK_FAILED_CNT: 0", "DQL_SUCCESS_CNT: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, " DQL_TOTAL_FAILED_CNT: 0", "DQL_DISK_FAILED_CNT: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// insert into写入1w数据，查询视图TS_TBL_OPER_STATIS数据DML_SUCCESS_CNT
TEST_F(tsdb_view_enhancement, Timing_063_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = WriteDataDualPartition(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd, "drop table %s;", "testdb0");
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(64),  id integer, worktime integer,vsysId TEXT) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        "testdb0");

    ret = GmcExecDirect(g_stmt_sync, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    sqlCmd[512] = {0};
    // 执行insert into语句
    (void)sprintf(sqlCmd, "insert into %s select * from %s;", "testdb0", g_tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 原表
    snprintf(g_command, 1024,
        "gmsysview -sql \"select * from 'V\\$TS_TBL_OPER_STATIS'  where TABLE_NAME='testdb' \" -s %s",
        g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "TABLE_NAME: testdb", "DML_SUCCESS_CNT: 1", "DML_TOTAL_FAILED_CNT: 0",
        "DML_DISK_FAILED_CNT: 0", "DQL_SUCCESS_CNT: 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, " DQL_TOTAL_FAILED_CNT: 0", "DQL_DISK_FAILED_CNT: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 目的表
    snprintf(g_command, 1024,
        "gmsysview -sql \"select * from 'V\\$TS_TBL_OPER_STATIS'  where TABLE_NAME='testdb0' \" -s %s",
        g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "TABLE_NAME: testdb0", "DML_SUCCESS_CNT: 1", "DML_TOTAL_FAILED_CNT: 0",
        "DML_DISK_FAILED_CNT: 0", "DQL_SUCCESS_CNT: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, " DQL_TOTAL_FAILED_CNT: 0", "DQL_DISK_FAILED_CNT: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sqlCmd[256] = {0};
    (void)sprintf(sqlCmd, "drop table %s;", "testdb0");
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
}

// 写入1w数据，改连接超时时间，写入超时，查询视图TS_TBL_OPER_STATIS数据DML_TOTAL_FAILED_CNT
TEST_F(tsdb_view_enhancement, Timing_063_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    YangConnOptionT tsConnOptions = {0};
    tsConnOptions.connName = NULL;
    tsConnOptions.isCsMode = true;
    tsConnOptions.requestTimeout = 3000;
    tsConnOptions.msgReadTimeout = 3000;
    tsConnOptions.serverLocator = g_connServerTsdb;
    ret = TestYangGmcConnect(&g_conn_sync, &g_stmt_sync, 0, &tsConnOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    constexpr int count = 500;
    int64_t ids[count] = {0};
    int64_t worktimes[count] = {0};
    char *names = (char *)malloc(count * 64);
    char nameSource[10][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
    char **vsysId = (char **)malloc(count * sizeof(char *));

    for (int i = 0; i < count; i++) {
        ids[i] = i + 1;
        worktimes[i] = 1695042000 + i * 3600;
        if (i % 10 == 0) {
            memcpy((names + i * 64), (char *)nameSource, 640);
        }
        vsysId[i] = (char *)malloc(TEXT_LEN * sizeof(char));
        (void)memset(vsysId[i], 0, TEXT_LEN);
    }

    GenerateRandomString(vsysId, count);

    uint32_t rowNum = count;
    int ret = GmcPrepareStmtByLabelName(g_stmt_sync, g_tableName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(g_stmt_sync, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, names, 64, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(g_stmt_sync, 1, (GmcDataTypeE)DB_DATATYPE_INT64, ids, sizeof(ids[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(g_stmt_sync, 2, (GmcDataTypeE)DB_DATATYPE_INT64, worktimes, sizeof(worktimes[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(g_stmt_sync, 3, (GmcDataTypeE)DB_DATATYPE_STRING, vsysId, sizeof(char *), 0);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(ret, 1016004);
    free(names);
    for (int i = 0; i < count; i++) {
        free(vsysId[i]);
    }
    free(vsysId);

    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$TS_TBL_OPER_STATIS'\" -s %s", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "TABLE_NAME: testdb", "DML_SUCCESS_CNT: 0", "DML_TOTAL_FAILED_CNT: 1",
        "DML_DISK_FAILED_CNT: 0", "DQL_SUCCESS_CNT: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, " DQL_TOTAL_FAILED_CNT: 0", "DQL_DISK_FAILED_CNT: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 写入数据，select *查询视图TS_TBL_OPER_STATIS数据DQL_SUCCESS_CNT
TEST_F(tsdb_view_enhancement, Timing_063_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = WriteDataDualPartition(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$TS_TBL_OPER_STATIS'\" -s %s", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "TABLE_NAME: testdb", "DML_SUCCESS_CNT: 1", "DML_TOTAL_FAILED_CNT: 0",
        "DML_DISK_FAILED_CNT: 0", "DQL_SUCCESS_CNT: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, " DQL_TOTAL_FAILED_CNT: 0", "DQL_DISK_FAILED_CNT: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 写入数据，select count、sum等聚合函数查询视图TS_TBL_OPER_STATIS数据DQL_SUCCESS_CNT
TEST_F(tsdb_view_enhancement, Timing_063_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = WriteDataDualPartition(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$TS_TBL_OPER_STATIS'\" -s %s", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "TABLE_NAME: testdb", "DML_SUCCESS_CNT: 1", "DML_TOTAL_FAILED_CNT: 0",
        "DML_DISK_FAILED_CNT: 0", "DQL_SUCCESS_CNT: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, " DQL_TOTAL_FAILED_CNT: 0", "DQL_DISK_FAILED_CNT: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char sqlCmd2[512] = {0};
    (void)sprintf(sqlCmd2, "SELECT count(id),sum(id+worktime) FROM %s group by id;", g_tableName);
    uint32_t cmdLen2 = strlen(sqlCmd2);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd2, cmdLen2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$TS_TBL_OPER_STATIS'\" -s %s", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "TABLE_NAME: testdb", "DML_SUCCESS_CNT: 1", "DML_TOTAL_FAILED_CNT: 0",
        "DML_DISK_FAILED_CNT: 0", "DQL_SUCCESS_CNT: 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, " DQL_TOTAL_FAILED_CNT: 0", "DQL_DISK_FAILED_CNT: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 写入数据，select 高级聚合查询，查询视图TS_TBL_OPER_STATIS数据DQL_SUCCESS_CNT
TEST_F(tsdb_view_enhancement, Timing_063_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = WriteDataDualPartition(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$TS_TBL_OPER_STATIS'\" -s %s", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "TABLE_NAME: testdb", "DML_SUCCESS_CNT: 1", "DML_TOTAL_FAILED_CNT: 0",
        "DML_DISK_FAILED_CNT: 0", "DQL_SUCCESS_CNT: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, " DQL_TOTAL_FAILED_CNT: 0", "DQL_DISK_FAILED_CNT: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char sqlCmd2[512] = {0};
    (void)sprintf(sqlCmd2, "SELECT * FROM %s group by id;", g_tableName);
    uint32_t cmdLen2 = strlen(sqlCmd2);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd2, cmdLen2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$TS_TBL_OPER_STATIS'\" -s %s", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "TABLE_NAME: testdb", "DML_SUCCESS_CNT: 1", "DML_TOTAL_FAILED_CNT: 0",
        "DML_DISK_FAILED_CNT: 0", "DQL_SUCCESS_CNT: 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, " DQL_TOTAL_FAILED_CNT: 0", "DQL_DISK_FAILED_CNT: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 写入数据，copy to查询成功，查询视图TS_TBL_OPER_STATIS数据DQL_SUCCESS_CNT
TEST_F(tsdb_view_enhancement, Timing_063_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = WriteDataDualPartition(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$TS_TBL_OPER_STATIS'\" -s %s", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "TABLE_NAME: testdb", "DML_SUCCESS_CNT: 1", "DML_TOTAL_FAILED_CNT: 0",
        "DML_DISK_FAILED_CNT: 0", "DQL_SUCCESS_CNT: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, " DQL_TOTAL_FAILED_CNT: 0", "DQL_DISK_FAILED_CNT: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char sqlCmd[256] = {0};
    sprintf(sqlCmd,
        "COPY (SELECT * FROM testdb WHERE id >= 2 ) TO"
        "'%s/test/sdv/testcases/25_Timing/063_tsdb_view_enhancement/data.csv';",
        g_dir);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, 256);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$TS_TBL_OPER_STATIS'\" -s %s", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "TABLE_NAME: testdb", "DML_SUCCESS_CNT: 1", "DML_TOTAL_FAILED_CNT: 0",
        "DML_DISK_FAILED_CNT: 0", "DQL_SUCCESS_CNT: 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, " DQL_TOTAL_FAILED_CNT: 0", "DQL_DISK_FAILED_CNT: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 写入1w数据，aging_func删除数据，查询视图TS_TBL_OPER_STATIS数据DQL_SUCCESS_CNT
TEST_F(tsdb_view_enhancement, Timing_063_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = WriteDataSinglePartition(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    system(g_command);

    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$TS_TBL_OPER_STATIS'\" -s %s", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "TABLE_NAME: testdb", "DML_SUCCESS_CNT: 1", "DML_TOTAL_FAILED_CNT: 0",
        "DML_DISK_FAILED_CNT: 0", "DQL_SUCCESS_CNT: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, " DQL_TOTAL_FAILED_CNT: 0", "DQL_DISK_FAILED_CNT: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char sqlCmd3[256] = {0};
    uint32_t cmdLen3 = 0;
    (void)sprintf(sqlCmd3, "SELECT tsdb_aging('%s');", g_tableName);
    cmdLen3 = strlen(sqlCmd3);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd3, cmdLen3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    system(g_command);

    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$TS_TBL_OPER_STATIS'\" -s %s", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "TABLE_NAME: testdb", "DML_SUCCESS_CNT: 1", "DML_TOTAL_FAILED_CNT: 0",
        "DML_DISK_FAILED_CNT: 0", "DQL_SUCCESS_CNT: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, " DQL_TOTAL_FAILED_CNT: 0", "DQL_DISK_FAILED_CNT: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 写入1w数据，Disk_Limit删除数据，查询视图TS_TBL_OPER_STATIS数据DQL_SUCCESS_CNT
TEST_F(tsdb_view_enhancement, Timing_063_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = WriteDataSinglePartition(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    system(g_command);

    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$TS_TBL_OPER_STATIS'\" -s %s", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "TABLE_NAME: testdb", "DML_SUCCESS_CNT: 1", "DML_TOTAL_FAILED_CNT: 0",
        "DML_DISK_FAILED_CNT: 0", "DQL_SUCCESS_CNT: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, " DQL_TOTAL_FAILED_CNT: 0", "DQL_DISK_FAILED_CNT: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 在线修改disk_limit
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "alter table %s set (disk_limit = '1 MB')", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    system(g_command);

    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$TS_TBL_OPER_STATIS'\" -s %s", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "TABLE_NAME: testdb", "DML_SUCCESS_CNT: 1", "DML_TOTAL_FAILED_CNT: 0",
        "DML_DISK_FAILED_CNT: 0", "DQL_SUCCESS_CNT: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, " DQL_TOTAL_FAILED_CNT: 0", "DQL_DISK_FAILED_CNT: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 写入1w数据，TTL删除数据，查询视图TS_TBL_OPER_STATIS数据DQL_SUCCESS_CNT
TEST_F(tsdb_view_enhancement, Timing_063_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"tsLcmCheckPeriod=3\" ");
    system("sh $TEST_HOME/tools/stop.sh -ts");
    TsDefulatDbFileClean();
    system("sh $TEST_HOME/tools/start.sh -ts");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd, "drop table %s;", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(64),  id integer, worktime integer,vsysId TEXT) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '1 hours');",
        g_tableName);

    ret = GmcExecDirect(g_stmt_sync, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    constexpr uint32_t count = 10000;
    int64_t ids[count] = {0};
    int64_t worktimes[count] = {0};
    char *names = (char *)malloc(count * 64);
    char nameSource[10][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
    char **vsysId = (char **)malloc(count * sizeof(char *));

    for (int i = 0; i < count; i++) {
        ids[i] = i + 1;
        worktimes[i] = 0;
        if (i % 10 == 0) {
            memcpy((names + i * 64), (char *)nameSource, 640);
        }
        vsysId[i] = (char *)malloc(TEXT_LEN * sizeof(char));
        (void)memset(vsysId[i], 0, TEXT_LEN);
    }

    GenerateRandomString(vsysId, count);

    uint32_t rowNum = count;
    sleep(5);
    uint32_t j = 0;

    ret = GmcPrepareStmtByLabelName(g_stmt_sync, g_tableName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(g_stmt_sync, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, names, 64, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(g_stmt_sync, 1, (GmcDataTypeE)DB_DATATYPE_INT64, ids, sizeof(ids[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(g_stmt_sync, 2, (GmcDataTypeE)DB_DATATYPE_INT64, worktimes, sizeof(worktimes[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(g_stmt_sync, 3, (GmcDataTypeE)DB_DATATYPE_STRING, vsysId, sizeof(char *), 0);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(g_stmt_sync);

    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    free(names);
    sleep(5);

    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    system(g_command);

    // 数据写成功了，只不过后面ttl删除了
    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$TS_TBL_OPER_STATIS'\" -s %s", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "TABLE_NAME: testdb", "DML_SUCCESS_CNT: 1", "DML_TOTAL_FAILED_CNT: 0",
        "DML_DISK_FAILED_CNT: 0", "DQL_SUCCESS_CNT: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, " DQL_TOTAL_FAILED_CNT: 0", "DQL_DISK_FAILED_CNT: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 写入数据，改连接超时时间，select *查询超时失败，查询视图TS_TBL_OPER_STATIS数据DQL_TOTAL_FAILED_CNT
TEST_F(tsdb_view_enhancement, Timing_063_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    constexpr int count = 10000;
    int64_t ids[count] = {0};
    int64_t worktimes[count] = {0};
    char *names = (char *)malloc(count * 64);
    char nameSource[10][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
    char **vsysId = (char **)malloc(count * sizeof(char *));

    for (int i = 0; i < count; i++) {
        ids[i] = i + 1;
        worktimes[i] = 1695042000 + i;
        if (i % 10 == 0) {
            memcpy((names + i * 64), (char *)nameSource, 640);
        }
        vsysId[i] = (char *)malloc(TEXT_LEN * sizeof(char));
        (void)memset(vsysId[i], 0, TEXT_LEN);
    }

    GenerateRandomString(vsysId, count);

    uint32_t rowNum = count;
    int ret = GmcPrepareStmtByLabelName(g_stmt_sync, g_tableName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(g_stmt_sync, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, names, 64, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(g_stmt_sync, 1, (GmcDataTypeE)DB_DATATYPE_INT64, ids, sizeof(ids[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(g_stmt_sync, 2, (GmcDataTypeE)DB_DATATYPE_INT64, worktimes, sizeof(worktimes[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(g_stmt_sync, 3, (GmcDataTypeE)DB_DATATYPE_STRING, vsysId, sizeof(char *), 0);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    YangConnOptionT tsConnOptions = {0};
    tsConnOptions.connName = NULL;
    tsConnOptions.isCsMode = true;
    tsConnOptions.requestTimeout = 5;
    tsConnOptions.msgReadTimeout = 5;
    tsConnOptions.serverLocator = g_connServerTsdb;
    ret = TestYangGmcConnect(&g_conn_sync, &g_stmt_sync, 0, &tsConnOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char sqlCmd2[512] = {0};
    (void)sprintf(sqlCmd2, "SELECT * FROM %s;", g_tableName);
    uint32_t cmdLen2 = strlen(sqlCmd2);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd2, cmdLen2);
    AW_MACRO_EXPECT_EQ_INT(ret, 1016004);

    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$TS_TBL_OPER_STATIS'\" -s %s", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "TABLE_NAME: testdb", "DML_SUCCESS_CNT: 1", "DML_TOTAL_FAILED_CNT: 0",
        "DML_DISK_FAILED_CNT: 0", "DQL_SUCCESS_CNT: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, " DQL_TOTAL_FAILED_CNT: 1", "DQL_DISK_FAILED_CNT: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 磁盘满，写入数据失败，查询视图TS_TBL_OPER_STATIS数据DML_DISK_FAILED_CNT
TEST_F(tsdb_view_enhancement, Timing_063_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    snprintf(g_command, 1024, "%s", g_cfePath);
    ret = system(g_command);
    if (ret == 0) {
        snprintf(g_command, 1024, "%s 'inject rfile_full(diskname) values(/data/gmdb)'", g_cfePath);
        ret = system(g_command);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        snprintf(g_command, 1024, "%s 'query rfile_full'", g_cfePath);
        ret = system(g_command);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        sleep(3);

        constexpr int count = 10000;
        int64_t ids[count] = {0};
        int64_t worktimes[count] = {0};
        char *names = (char *)malloc(count * 64);
        char nameSource[10][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
        char **vsysId = (char **)malloc(count * sizeof(char *));

        for (int i = 0; i < count; i++) {
            ids[i] = i + 1;
            worktimes[i] = 1695042000 + i;
            if (i % 10 == 0) {
                memcpy((names + i * 64), (char *)nameSource, 640);
            }
            vsysId[i] = (char *)malloc(TEXT_LEN * sizeof(char));
            (void)memset(vsysId[i], 0, TEXT_LEN);
        }

        GenerateRandomString(vsysId, count);

        uint32_t rowNum = count;
        int ret = GmcPrepareStmtByLabelName(g_stmt_sync, g_tableName, GMC_OPERATION_SQL_INSERT);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(g_stmt_sync, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, names, 64, NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(g_stmt_sync, 1, (GmcDataTypeE)DB_DATATYPE_INT64, ids, sizeof(ids[0]), NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(g_stmt_sync, 2, (GmcDataTypeE)DB_DATATYPE_INT64, worktimes, sizeof(worktimes[0]), NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(g_stmt_sync, 3, (GmcDataTypeE)DB_DATATYPE_STRING, vsysId, sizeof(char *), 0);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcSetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcExecute(g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(ret, 1019000);
        free(names);
        for (int i = 0; i < count; ++i) {
            free(vsysId[i]);  // 释放每个 char* 指向的内存
        }
        free(vsysId);  // 释放指针数组本身的内存

        char sqlCmd2[512] = {0};
        (void)sprintf(sqlCmd2, "SELECT * FROM %s;", g_tableName);
        uint32_t cmdLen2 = strlen(sqlCmd2);
        ret = GmcExecDirect(g_stmt_sync, sqlCmd2, cmdLen2);
        AW_MACRO_EXPECT_EQ_INT(ret, 1019000);

        int64_t dataCount = 0;
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(0, dataCount);

        snprintf(g_command, 1024, "%s 'clean rfile_full where diskname = /data/gmdb'", g_cfePath);
        ret = system(g_command);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        sleep(3);

        snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
        system(g_command);

        snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$TS_TBL_OPER_STATIS'\" -s %s", g_connServerTsdb);
        system(g_command);
        ret = executeCommand(g_command, "TABLE_NAME: testdb", "DML_SUCCESS_CNT: 0", "DML_TOTAL_FAILED_CNT: 1",
            "DML_DISK_FAILED_CNT: 1", "DQL_SUCCESS_CNT: 0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = executeCommand(g_command, " DQL_TOTAL_FAILED_CNT: 1", "DQL_DISK_FAILED_CNT: 1");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    } else {
        AW_FUN_Log(LOG_STEP, "no cfe.");
    }
}

// 磁盘文件没权限，写入数据失败，查询视图TS_TBL_OPER_STATIS数据DML_DISK_FAILED_CNT
TEST_F(tsdb_view_enhancement, Timing_063_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    snprintf(g_command, 1024, "%s", g_cfePath);
    ret = system(g_command);
    if (ret == 0) {
        snprintf(g_command, 1024, "%s 'inject rfile_unread (filename) values(/data/gmdb)'", g_cfePath);
        ret = system(g_command);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        snprintf(g_command, 1024, "%s 'query rfile_unread'", g_cfePath);
        ret = system(g_command);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        sleep(3);

        constexpr int count = 10000;
        int64_t ids[count] = {0};
        int64_t worktimes[count] = {0};
        char *names = (char *)malloc(count * 64);
        char nameSource[10][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
        char **vsysId = (char **)malloc(count * sizeof(char *));

        for (int i = 0; i < count; i++) {
            ids[i] = i + 1;
            worktimes[i] = 1695042000 + i;
            if (i % 10 == 0) {
                memcpy((names + i * 64), (char *)nameSource, 640);
            }
            vsysId[i] = (char *)malloc(TEXT_LEN * sizeof(char));
            (void)memset(vsysId[i], 0, TEXT_LEN);
        }

        GenerateRandomString(vsysId, count);

        uint32_t rowNum = count;
        int ret = GmcPrepareStmtByLabelName(g_stmt_sync, g_tableName, GMC_OPERATION_SQL_INSERT);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(g_stmt_sync, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, names, 64, NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(g_stmt_sync, 1, (GmcDataTypeE)DB_DATATYPE_INT64, ids, sizeof(ids[0]), NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(g_stmt_sync, 2, (GmcDataTypeE)DB_DATATYPE_INT64, worktimes, sizeof(worktimes[0]), NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(g_stmt_sync, 3, (GmcDataTypeE)DB_DATATYPE_STRING, vsysId, sizeof(char *), 0);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcSetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcExecute(g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(ret, 1013000);
        free(names);
        for (int i = 0; i < count; ++i) {
            free(vsysId[i]);  // 释放每个 char* 指向的内存
        }
        free(vsysId);  // 释放指针数组本身的内存

        char sqlCmd2[512] = {0};
        (void)sprintf(sqlCmd2, "SELECT * FROM %s;", g_tableName);
        uint32_t cmdLen2 = strlen(sqlCmd2);
        ret = GmcExecDirect(g_stmt_sync, sqlCmd2, cmdLen2);
        AW_MACRO_EXPECT_EQ_INT(ret, 1013000);

        int64_t dataCount = 0;
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(0, dataCount);

        snprintf(g_command, 1024, "%s 'clean rfile_unwrite where filename =/data/gmdb'", g_cfePath);
        ret = system(g_command);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        sleep(3);

        snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
        system(g_command);

        snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$TS_TBL_OPER_STATIS'\" -s %s", g_connServerTsdb);
        system(g_command);
        ret = executeCommand(g_command, "TABLE_NAME: testdb", "DML_SUCCESS_CNT: 0", "DML_TOTAL_FAILED_CNT: 1",
            "DML_DISK_FAILED_CNT: 1", "DQL_SUCCESS_CNT: 0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = executeCommand(g_command, " DQL_TOTAL_FAILED_CNT: 1", "DQL_DISK_FAILED_CNT: 1");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    } else {
        AW_FUN_Log(LOG_STEP, "no cfe.");
    }
}

// 磁盘满，select *数据失败，查询视图TS_TBL_OPER_STATIS数据DML_DISK_FAILED_CNT
TEST_F(tsdb_view_enhancement, Timing_063_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    snprintf(g_command, 1024, "%s", g_cfePath);
    ret = system(g_command);
    if (ret == 0) {
        snprintf(g_command, 1024, "%s 'inject rfile_full(diskname) values(/data/gmdb)'", g_cfePath);
        ret = system(g_command);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        snprintf(g_command, 1024, "%s 'query rfile_full'", g_cfePath);
        ret = system(g_command);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        sleep(3);

        constexpr int count = 10000;
        int64_t ids[count] = {0};
        int64_t worktimes[count] = {0};
        char *names = (char *)malloc(count * 64);
        char nameSource[10][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
        char **vsysId = (char **)malloc(count * sizeof(char *));

        for (int i = 0; i < count; i++) {
            ids[i] = i + 1;
            worktimes[i] = 1695042000 + i;
            if (i % 10 == 0) {
                memcpy((names + i * 64), (char *)nameSource, 640);
            }
            vsysId[i] = (char *)malloc(TEXT_LEN * sizeof(char));
            (void)memset(vsysId[i], 0, TEXT_LEN);
        }

        GenerateRandomString(vsysId, count);

        uint32_t rowNum = count;
        int ret = GmcPrepareStmtByLabelName(g_stmt_sync, g_tableName, GMC_OPERATION_SQL_INSERT);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(g_stmt_sync, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, names, 64, NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(g_stmt_sync, 1, (GmcDataTypeE)DB_DATATYPE_INT64, ids, sizeof(ids[0]), NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(g_stmt_sync, 2, (GmcDataTypeE)DB_DATATYPE_INT64, worktimes, sizeof(worktimes[0]), NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(g_stmt_sync, 3, (GmcDataTypeE)DB_DATATYPE_STRING, vsysId, sizeof(char *), 0);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcSetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcExecute(g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(ret, 1019000);
        free(names);
        for (int i = 0; i < count; ++i) {
            free(vsysId[i]);  // 释放每个 char* 指向的内存
        }
        free(vsysId);  // 释放指针数组本身的内存

        char sqlCmd2[512] = {0};
        (void)sprintf(sqlCmd2, "SELECT * FROM %s;", g_tableName);
        uint32_t cmdLen2 = strlen(sqlCmd2);
        ret = GmcExecDirect(g_stmt_sync, sqlCmd2, cmdLen2);
        AW_MACRO_EXPECT_EQ_INT(ret, 1019000);

        int64_t dataCount = 0;
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(0, dataCount);

        snprintf(g_command, 1024, "%s 'clean rfile_full where diskname = /data/gmdb'", g_cfePath);
        ret = system(g_command);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        sleep(3);

        snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
        system(g_command);

        snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$TS_TBL_OPER_STATIS'\" -s %s", g_connServerTsdb);
        system(g_command);
        ret = executeCommand(g_command, "TABLE_NAME: testdb", "DML_SUCCESS_CNT: 0", "DML_TOTAL_FAILED_CNT: 1",
            "DML_DISK_FAILED_CNT: 1", "DQL_SUCCESS_CNT: 0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = executeCommand(g_command, " DQL_TOTAL_FAILED_CNT: 1", "DQL_DISK_FAILED_CNT: 1");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    } else {
        AW_FUN_Log(LOG_STEP, "no cfe.");
    }
}

// 磁盘文件没权限，select *数据失败，查询视图TS_TBL_OPER_STATIS数据DML_DISK_FAILED_CNT
TEST_F(tsdb_view_enhancement, Timing_063_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    snprintf(g_command, 1024, "%s", g_cfePath);
    ret = system(g_command);
    if (ret == 0) {
        snprintf(g_command, 1024, "%s 'inject rfile_unread (filename) values(/data/gmdb)'", g_cfePath);
        ret = system(g_command);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        snprintf(g_command, 1024, "%s 'query rfile_unread'", g_cfePath);
        ret = system(g_command);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        sleep(3);

        constexpr int count = 10000;
        int64_t ids[count] = {0};
        int64_t worktimes[count] = {0};
        char *names = (char *)malloc(count * 64);
        char nameSource[10][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
        char **vsysId = (char **)malloc(count * sizeof(char *));

        for (int i = 0; i < count; i++) {
            ids[i] = i + 1;
            worktimes[i] = 1695042000 + i;
            if (i % 10 == 0) {
                memcpy((names + i * 64), (char *)nameSource, 640);
            }
            vsysId[i] = (char *)malloc(TEXT_LEN * sizeof(char));
            (void)memset(vsysId[i], 0, TEXT_LEN);
        }

        GenerateRandomString(vsysId, count);

        uint32_t rowNum = count;
        int ret = GmcPrepareStmtByLabelName(g_stmt_sync, g_tableName, GMC_OPERATION_SQL_INSERT);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(g_stmt_sync, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, names, 64, NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(g_stmt_sync, 1, (GmcDataTypeE)DB_DATATYPE_INT64, ids, sizeof(ids[0]), NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(g_stmt_sync, 2, (GmcDataTypeE)DB_DATATYPE_INT64, worktimes, sizeof(worktimes[0]), NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(g_stmt_sync, 3, (GmcDataTypeE)DB_DATATYPE_STRING, vsysId, sizeof(char *), 0);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcSetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcExecute(g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(ret, 1013000);
        free(names);
        for (int i = 0; i < count; ++i) {
            free(vsysId[i]);  // 释放每个 char* 指向的内存
        }
        free(vsysId);  // 释放指针数组本身的内存

        char sqlCmd2[512] = {0};
        (void)sprintf(sqlCmd2, "SELECT * FROM %s;", g_tableName);
        uint32_t cmdLen2 = strlen(sqlCmd2);
        ret = GmcExecDirect(g_stmt_sync, sqlCmd2, cmdLen2);
        AW_MACRO_EXPECT_EQ_INT(ret, 1013000);

        int64_t dataCount = 0;
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(0, dataCount);

        snprintf(g_command, 1024, "%s 'clean rfile_unwrite where filename =/data/gmdb'", g_cfePath);
        ret = system(g_command);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        sleep(3);

        snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
        system(g_command);

        snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$TS_TBL_OPER_STATIS'\" -s %s", g_connServerTsdb);
        system(g_command);
        ret = executeCommand(g_command, "TABLE_NAME: testdb", "DML_SUCCESS_CNT: 0", "DML_TOTAL_FAILED_CNT: 1",
            "DML_DISK_FAILED_CNT: 1", "DQL_SUCCESS_CNT: 0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = executeCommand(g_command, " DQL_TOTAL_FAILED_CNT: 1", "DQL_DISK_FAILED_CNT: 1");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    } else {
        AW_FUN_Log(LOG_STEP, "no cfe.");
    }
}

// 建表，写入数据，select查询数据，再次写入数据，查询视图TS_TBL_OPER_STATIS数据DML_SUCCESS_CNT、DQL_SUCCESS_CNT
TEST_F(tsdb_view_enhancement, Timing_063_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = WriteDataDualPartition(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$TS_TBL_OPER_STATIS'\" -s %s", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "TABLE_NAME: testdb", "DML_SUCCESS_CNT: 1", "DML_TOTAL_FAILED_CNT: 0",
        "DML_DISK_FAILED_CNT: 0", "DQL_SUCCESS_CNT: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, " DQL_TOTAL_FAILED_CNT: 0", "DQL_DISK_FAILED_CNT: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = WriteDataDualPartition(g_stmt_sync, 10000, 20000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$TS_TBL_OPER_STATIS'\" -s %s", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "TABLE_NAME: testdb", "DML_SUCCESS_CNT: 2", "DML_TOTAL_FAILED_CNT: 0",
        "DML_DISK_FAILED_CNT: 0", "DQL_SUCCESS_CNT: 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, " DQL_TOTAL_FAILED_CNT: 0", "DQL_DISK_FAILED_CNT: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 建表，写入数据，查询视图TS_TBL_OPER_STATIS数据DML_SUCCESS_CNT，删除表，重新建相同表名的表，写入数据，查询视图TS_TBL_OPER_STATIS数据DML_SUCCESS_CNT，
TEST_F(tsdb_view_enhancement, Timing_063_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = WriteDataDualPartition(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$TS_TBL_OPER_STATIS'\" -s %s", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "TABLE_NAME: testdb", "DML_SUCCESS_CNT: 1", "DML_TOTAL_FAILED_CNT: 0",
        "DML_DISK_FAILED_CNT: 0", "DQL_SUCCESS_CNT: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, " DQL_TOTAL_FAILED_CNT: 0", "DQL_DISK_FAILED_CNT: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd, "drop table %s;", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(64),  id integer, worktime integer,vsysId TEXT) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        g_tableName);

    ret = GmcExecDirect(g_stmt_sync, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = WriteDataDualPartition(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$TS_TBL_OPER_STATIS'\" -s %s", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "TABLE_NAME: testdb", "DML_SUCCESS_CNT: 1", "DML_TOTAL_FAILED_CNT: 0",
        "DML_DISK_FAILED_CNT: 0", "DQL_SUCCESS_CNT: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, " DQL_TOTAL_FAILED_CNT: 0", "DQL_DISK_FAILED_CNT: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 查询视图TS_TBL_OPER_STATIS where过滤表名
TEST_F(tsdb_view_enhancement, Timing_063_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = WriteDataDualPartition(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(g_command, 1024,
        "gmsysview -sql \"select * from 'V\\$TS_TBL_OPER_STATIS' where TABLE_NAME='testdb' \" -s %s", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "TABLE_NAME: testdb", "DML_SUCCESS_CNT: 1", "DML_TOTAL_FAILED_CNT: 0",
        "DML_DISK_FAILED_CNT: 0", "DQL_SUCCESS_CNT: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, " DQL_TOTAL_FAILED_CNT: 0", "DQL_DISK_FAILED_CNT: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// -fmt hex选项在select前面
TEST_F(tsdb_view_enhancement, Timing_063_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd, "drop table %s;", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(64),  id integer, worktime integer,vsysId TEXT, ip inet, message blob(160)) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        g_tableName);

    ret = GmcExecDirect(g_stmt_sync, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = WriteDataIp(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //-sql只能加select，-fmt hex可以加在gmsisview后面
    snprintf(g_command, 1024, "gmsysview -fmt hex -sql \"select * from testdb \" -s %s", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "name: 0x6461766964", "id: 1", "worktime: 1695042000", "vsysId: 0x");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "ip: 0x3130313031303430",
        "message: "
        "0x303031302030303030000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000"
        "00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000"
        "000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// -fmt hex选项在select后面
TEST_F(tsdb_view_enhancement, Timing_063_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd, "drop table %s;", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(64),  id integer, worktime integer,vsysId TEXT, ip inet, message blob(160)) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        g_tableName);

    ret = GmcExecDirect(g_stmt_sync, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = WriteDataIp(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(g_command, 1024, "gmsysview -sql \"select * from testdb \" -fmt hex -s %s", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "name: 0x6461766964", "id: 1", "worktime: 1695042000", "vsysId: 0x");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "ip: 0x3130313031303430",
        "message: "
        "0x303031302030303030000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000"
        "00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000"
        "000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// -fmt hex与聚合函数使用
TEST_F(tsdb_view_enhancement, Timing_063_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd, "drop table %s;", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(64),  id integer, worktime integer,vsysId TEXT, ip inet, message blob(160)) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        g_tableName);

    ret = GmcExecDirect(g_stmt_sync, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = WriteDataIp(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(g_command, 1024,
        "gmsysview -sql \"select name,id ,worktime,vsysID,count(id),ip,message from testdb  where id >0\" -fmt hex -s "
        "%s",
        g_connServerTsdb);
    system(g_command);
    ret =
        executeCommand(g_command, "name: 0x6461766964", "id: 1", "worktime: 1695042000", "vsysId: 0x", "count(id): 20");
    if (ret == 0) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = executeCommand(
            g_command, "name: 0x6461766964", "id: 1", "worktime: 1695042000", "vsysId: ", "count(id): 20");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = executeCommand(g_command, "ip: 0x3130313031303430",
        "message: "
        "0x303037312033353230000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000"
        "00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000"
        "000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// -fmt选项重复使用
TEST_F(tsdb_view_enhancement, Timing_063_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd, "drop table %s;", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(64),  id integer, worktime integer,vsysId TEXT, ip inet, message blob(160)) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        g_tableName);

    ret = GmcExecDirect(g_stmt_sync, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = WriteDataIp(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(g_command, 1024, "gmsysview -sql \"select * from testdb  \" -s %s -fmt hex -fmt hex ", g_connServerTsdb);
    system(g_command);
#if defined(FEATURE_MULTI_TS)
    ret = executeCommand(g_command, "ts view format option can not repeat, ret = 1009006");
#else
    ret = executeCommand(g_command, "ts view has duplicated option: -fmt, ret = 1009000");
#endif
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// -fmt选项加非hex参数
TEST_F(tsdb_view_enhancement, Timing_063_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd, "drop table %s;", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(64),  id integer, worktime integer,vsysId TEXT, ip inet, message blob(160)) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        g_tableName);

    ret = GmcExecDirect(g_stmt_sync, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = WriteDataIp(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(g_command, 1024, "gmsysview -sql \"select * from testdb  \"  -s %s -fmt ip", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "ts view format option only supports hex, ret = 1009006");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// -fmt选项不加参数
TEST_F(tsdb_view_enhancement, Timing_063_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd, "drop table %s;", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(64),  id integer, worktime integer,vsysId TEXT, ip inet, message blob(160)) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        g_tableName);

    ret = GmcExecDirect(g_stmt_sync, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = WriteDataIp(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(g_command, 1024, "gmsysview -sql \"select * from testdb  \"  -s %s -fmt", g_connServerTsdb);
    system(g_command);
#if defined(FEATURE_MULTI_TS)
    ret = executeCommand(g_command, "option param unsucc, ret = 1004004");
#else
    ret = executeCommand(g_command, "ts view format option: -fmt needs type, ret = 1009006");
#endif
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// -fmt选项查询视图
TEST_F(tsdb_view_enhancement, Timing_063_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd, "drop table %s;", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(64),  id integer, worktime integer,vsysId TEXT, ip inet, message blob(160)) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        g_tableName);

    ret = GmcExecDirect(g_stmt_sync, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = WriteDataIp(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(
        g_command, 1024, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -fmt hex -s %s", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "TABLE_NAME: 0x746573746462", "PHY_TBL_NUM: 1", "ROW_CNT: 20");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 建表，写入数据，select数据，查询视图TS_TBL_OPER_STATIS数据DML_SUCCESS_CNT、DQL_SUCCESS_CNT，重启服务后继续写入数据，select
TEST_F(tsdb_view_enhancement, Timing_063_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = WriteDataDualPartition(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$TS_TBL_OPER_STATIS'\" -s %s", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "TABLE_NAME: testdb", "DML_SUCCESS_CNT: 1", "DML_TOTAL_FAILED_CNT: 0",
        "DML_DISK_FAILED_CNT: 0", "DQL_SUCCESS_CNT: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, " DQL_TOTAL_FAILED_CNT: 0", "DQL_DISK_FAILED_CNT: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    YangConnOptionT tsConnOptions = {0};
    tsConnOptions.isCsMode = true;

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启之后对该表dml或dql数据后（重启之后数据丢失，该步骤可加载数据）才能查到该表的统计数操作视图
    snprintf(g_command, 1024, "gmsysview -sql \"select * from testdb\" -s %s", g_connServerTsdb);
    system(g_command);

    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$TS_TBL_OPER_STATIS'\" -s %s", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "TABLE_NAME: testdb", "DML_SUCCESS_CNT: 0", "DML_TOTAL_FAILED_CNT: 0",
        "DML_DISK_FAILED_CNT: 0", "DQL_SUCCESS_CNT: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, " DQL_TOTAL_FAILED_CNT: 0", "DQL_DISK_FAILED_CNT: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = WriteDataDualPartition(g_stmt_sync, 10000, 20000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$TS_TBL_OPER_STATIS'\" -s %s", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "TABLE_NAME: testdb", "DML_SUCCESS_CNT: 1", "DML_TOTAL_FAILED_CNT: 0",
        "DML_DISK_FAILED_CNT: 0", "DQL_SUCCESS_CNT: 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, " DQL_TOTAL_FAILED_CNT: 0", "DQL_DISK_FAILED_CNT: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 100个表写入100个分区数据，查询视图PHY_TBL_DISK_USAGE数据
TEST_F(tsdb_view_enhancement, Timing_063_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char tableName[100][64] = {0};

    int tableNum = 100;
    for (int j = 0; j < tableNum; j++) {
        sprintf(tableName[j], "testdb%d", j);
        char sqlCmd[256] = {0};
        (void)sprintf(sqlCmd, "drop table %s;", tableName[j]);
        uint32_t cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
        // 此处不能增加强校验 AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        char ddlCommand[512];
        snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
            "create table %s(name char(64),  id integer, worktime integer,vsysId TEXT) with "
            "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)');",
            tableName[j]);

        ret = GmcExecDirect(g_stmt_sync, ddlCommand, 512);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

        constexpr uint32_t count = 10000;
        int64_t ids[count] = {0};
        int64_t worktimes[count] = {0};
        char *names = (char *)malloc(count * 64);
        char nameSource[10][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
        char **vsysId = (char **)malloc(count * sizeof(char *));

        for (int i = 0; i < count; i++) {
            ids[i] = i + 1;
            worktimes[i] = 1695042000 + i;
            if (i % 10 == 0) {
                memcpy((names + i * 64), (char *)nameSource, 640);
            }
            vsysId[i] = (char *)malloc(TEXT_LEN * sizeof(char));
            (void)memset(vsysId[i], 0, TEXT_LEN);
        }

        GenerateRandomString(vsysId, count);

        uint32_t rowNum = count;

        ret = GmcPrepareStmtByLabelName(g_stmt_sync, tableName[j], GMC_OPERATION_SQL_INSERT);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(g_stmt_sync, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, names, 64, NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(g_stmt_sync, 1, (GmcDataTypeE)DB_DATATYPE_INT64, ids, sizeof(ids[0]), NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(g_stmt_sync, 2, (GmcDataTypeE)DB_DATATYPE_INT64, worktimes, sizeof(worktimes[0]), NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(g_stmt_sync, 3, (GmcDataTypeE)DB_DATATYPE_STRING, vsysId, sizeof(char *), 0);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcSetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcExecute(g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        free(names);
        for (int i = 0; i < count; ++i) {
            free(vsysId[i]);  // 释放每个 char* 指向的内存
        }
        free(vsysId);  // 释放指针数组本身的内存
    }
    char g_command[1024];
    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE'\" -s %s", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "index = 299");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "index = 300");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    for (int j = 0; j < tableNum; j++) {
        char sqlCmd[256] = {0};
        (void)sprintf(sqlCmd, "drop table %s;", tableName[j]);
        uint32_t cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    }

    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd, "drop table %s;", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(64),  id integer, worktime integer,vsysId TEXT) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        g_tableName);

    ret = GmcExecDirect(g_stmt_sync, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
}

void *thread_tsdb_view_enhancement_042(void *args)
{
    int ret1 = 0;
    int m = *(int *)args;
    GmcConnT *g_conn_sync1 = NULL;  //  conn 句柄
    GmcStmtT *g_stmt_sync = NULL;   //  stmt 句柄
    YangConnOptionT tsConnOptions = {0};
    tsConnOptions.isCsMode = true;

    ret = TestTsGmcConnect(&g_conn_sync1, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);

    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    system(g_command);
    ret1 = executeCommand(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);

    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE'\" -s %s", g_connServerTsdb);
    system(g_command);
    ret1 = executeCommand(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);

    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$TS_TBL_OPER_STATIS'\" -s %s", g_connServerTsdb);
    system(g_command);
    ret1 = executeCommand(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);

    ret1 = testGmcDisconnect(g_conn_sync1, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);
    thr_count++;
    return 0;
}

//  写入数据，多线程并发查询视图STORAGE_DISK_USAGE、PHY_TBL_DISK_USAGE、TS_TBL_OPER_STATIS和-fmt
TEST_F(tsdb_view_enhancement, Timing_063_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int thr_num = 100;

    ret = WriteDataDualPartition(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_t thr1;
    int ret1 = 0;
    int index[thr_num];
    for (int k = 0; k < thr_num; k++) {
        index[k] = k;
        ret1 = pthread_create(&thr1, NULL, thread_tsdb_view_enhancement_042, (void *)&index[k]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);
    }
    for (int k = 0; k < thr_num; k++) {
        ret1 = pthread_join(thr1, NULL);
    }

    while (thr_count < thr_num) {
        sleep(3);
    }
}

void *thread_tsdb_view_enhancement_043(void *args)
{
    int ret1 = 0;
    int m = *(int *)args;
    GmcConnT *g_conn_sync1 = NULL;  //  conn 句柄
    GmcStmtT *g_stmt_sync = NULL;   //  stmt 句柄
    ret = TestTsGmcConnect(&g_conn_sync1, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);

    constexpr uint32_t count = 10000;
    int64_t ids[count] = {0};
    int64_t worktimes[count] = {0};
    char *names = (char *)malloc(count * 64);
    char nameSource[10][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
    char **vsysId = (char **)malloc(count * sizeof(char *));

    for (int i = 0; i < count; i++) {
        ids[i] = i + 1;
        worktimes[i] = 1695042000 + i;
        if (i % 10 == 0) {
            memcpy((names + i * 64), (char *)nameSource, 640);
        }
        vsysId[i] = (char *)malloc(TEXT_LEN * sizeof(char));
        (void)memset(vsysId[i], 0, TEXT_LEN);
    }

    GenerateRandomString(vsysId, count);

    uint32_t rowNum = count;

    ret1 = GmcPrepareStmtByLabelName(g_stmt_sync, g_tableName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret1, GMERR_OK);
    ret1 = GmcBindCol(g_stmt_sync, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, names, 64, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret1 = GmcBindCol(g_stmt_sync, 1, (GmcDataTypeE)DB_DATATYPE_INT64, ids, sizeof(ids[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret1, GMERR_OK);
    ret1 = GmcBindCol(g_stmt_sync, 2, (GmcDataTypeE)DB_DATATYPE_INT64, worktimes, sizeof(worktimes[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret1, GMERR_OK);
    ret1 = GmcBindCol(g_stmt_sync, 3, (GmcDataTypeE)DB_DATATYPE_STRING, vsysId, sizeof(char *), 0);
    AW_MACRO_EXPECT_EQ_INT(ret1, GMERR_OK);
    ret1 = GmcSetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret1, GMERR_OK);
    ret1 = GmcExecute(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(ret1, GMERR_OK);
    free(names);

    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$TS_TBL_OPER_STATIS'\" -s %s", g_connServerTsdb);
    system(g_command);
    ret1 = executeCommand(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);

    char sqlCmd2[512] = {0};
    (void)sprintf(sqlCmd2, "SELECT * FROM %s;", g_tableName);
    uint32_t cmdLen2 = strlen(sqlCmd2);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd2, cmdLen2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret1 = testGmcDisconnect(g_conn_sync1, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);
    thr_count++;
    return 0;
}

//  写入数据，多线程并发查询视图STORAGE_DISK_USAGE、PHY_TBL_DISK_USAGE、TS_TBL_OPER_STATIS和-fmt
TEST_F(tsdb_view_enhancement, Timing_063_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int thr_num = 10;

    pthread_t thr1;
    int ret1 = 0;
    int index[thr_num];
    for (int k = 0; k < thr_num; k++) {
        index[k] = k;
        ret1 = pthread_create(&thr1, NULL, thread_tsdb_view_enhancement_043, (void *)&index[k]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);
    }
    for (int k = 0; k < thr_num; k++) {
        ret1 = pthread_join(thr1, NULL);
    }

    while (thr_count < thr_num) {
        sleep(3);
    }
}

void *thread_tsdb_view_enhancement_044(void *args)
{
    int ret1 = 0;
    int m = *(int *)args;
    GmcConnT *g_conn_sync1 = NULL;  //  conn 句柄
    GmcStmtT *g_stmt_sync = NULL;   //  stmt 句柄
    YangConnOptionT tsConnOptions = {0};
    tsConnOptions.isCsMode = true;

    ret = TestTsGmcConnect(&g_conn_sync1, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);

    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd, "drop table %s;", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret1 = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);

    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE'\" -s %s", g_connServerTsdb);
    system(g_command);
    ret1 = executeCommand(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);

    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$TS_TBL_OPER_STATIS'\" -s %s", g_connServerTsdb);
    system(g_command);
    ret1 = executeCommand(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);

    snprintf(g_command, 1024, "gmsysview -sql \"select * from testdb\" -fmt hex");
    system(g_command);
    ret1 = executeCommand(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);

    ret1 = testGmcDisconnect(g_conn_sync1, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);
    thr_count++;
    return 0;
}

//  写入数据，多线程并发查询视图STORAGE_DISK_USAGE、PHY_TBL_DISK_USAGE、TS_TBL_OPER_STATIS和-fmt
TEST_F(tsdb_view_enhancement, Timing_063_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int thr_num = 100;

    pthread_t thr1;
    int ret1 = 0;
    int index[thr_num];
    for (int k = 0; k < thr_num; k++) {
        index[k] = k;
        ret1 = pthread_create(&thr1, NULL, thread_tsdb_view_enhancement_044, (void *)&index[k]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);
    }
    for (int k = 0; k < thr_num; k++) {
        ret1 = pthread_join(thr1, NULL);
    }

    while (thr_count < thr_num) {
        sleep(3);
    }

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(64),  id integer, worktime integer,vsysId TEXT) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        g_tableName);

    ret = GmcExecDirect(g_stmt_sync, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
}

// 补充视图STORAGE_REDO_INFO验证（仅debug模式可查，故不加入构建）
TEST_F(tsdb_view_enhancement, Timing_063_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    char g_command[1024];
    snprintf(g_command, 1024,
        "gmsysview -sql \"select * from 'V\\$STORAGE_REDO_INFO'\" -s %s", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
