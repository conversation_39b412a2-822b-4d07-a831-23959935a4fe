/*****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2024. All rights reserved.
 Description  : GMDB 506.1.0 迭代1 alter加固测试--功能测试
 Notes        :
 History      :
 Author       : luyang/l00618033
 Create       : [2025.7.13]
*****************************************************************************/
#include "gtest/gtest.h"
#include "t_rd_ts.h"
#include "tsdbSupGmimport.h"

class tsdbAlterEnh_001_test : public testing::Test {
protected:
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}

public:
    virtual void SetUp();
    virtual void TearDown();
};

void tsdbAlterEnh_001_test::SetUp()
{
    InitTsCiCfg();
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建epoll监听线程
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN();
    ret = TestTsGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void tsdbAlterEnh_001_test::TearDown()
{
    int ret = 0;
    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 断开连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_END();

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    RecoverTsCiCfg();
    system("sh $TEST_HOME/tools/stop.sh -ts");
}

// DTS2025071408295
/* ****************************************************************************
 Description  :
01.创建时序逻辑表，插入数据，进行语句查询，新增列为inet类型，使用原先语句查询，使用新增量列进行过滤
**************************************************************************** */
TEST_F(tsdbAlterEnh_001_test, Timing_054_005_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    system("rm -rf /home/<USER>/");
    char tableName[1024] = {0};

    constexpr int64_t count = 20;
    C3Int8C4StrT *obj = NULL;
    ret = TestAllocTagC3Int8C4Str(&obj, count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char ip[count][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[count][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc", "lucy",
        "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    GenerateRandomString(obj->ns, count);
    for (int i = 0; i < count; i++) {
        obj->id[i] = i;
        obj->time[i] = 1704067200 + i;
        if (i % 20 == 0) {
            memcpy_s(obj->ip + i * 33, 33 * 20, (char *)ip, 33 * 20);
            memcpy_s(obj->name + i * 64, 64 * 20, (char *)name, 64 * 20);
        }
        obj->messageLen[i] = 160;
        (void)snprintf(obj->message[i], obj->messageLen[i], "this is %d;", i);
    }

    // 创建逻辑表
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(160), ns text, ip1 inet)"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '5000 MB',"
        " compression = 'fast(rapidlz)');",
        g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入数据
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C4StrSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询并校验
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "select * from %s order by id;", g_tableName);
    ret = readRecordAndCompareTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol inet;", g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "select * from %s order by id;", g_tableName);
    ret = readRecordAndCompareTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 使用新增列进行过滤
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "select * from %s where newcol >= '00000000' order by id;", g_tableName);
    ret = readRecordAndCompareTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "truncate table %s", g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C4StrSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecordAndCompareTs2(g_stmt, g_tableName, obj, count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放内存
    TestFreeTagC3Int8C4StrAlloc(obj, count);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
02.创建时序逻辑表，插入数据，进行语句查询，新增列为inet类型，使用原先语句查询，使用新增量列进行过滤，含like过滤，
like不含中文字符，预期报错，inet不支持模糊查询
**************************************************************************** */
TEST_F(tsdbAlterEnh_001_test, Timing_054_005_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_SEMANTIC_ERROR);
    int ret = 0;
    system("rm -rf /home/<USER>/");
    char tableName[1024] = {0};

    constexpr int64_t count = 20;
    C3Int8C4StrT *obj = NULL;
    ret = TestAllocTagC3Int8C4Str(&obj, count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char ip[count][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[count][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc", "lucy",
        "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    GenerateRandomString(obj->ns, count);
    for (int i = 0; i < count; i++) {
        obj->id[i] = i;
        obj->time[i] = 1704067200 + i;
        if (i % 20 == 0) {
            memcpy_s(obj->ip + i * 33, 33 * 20, (char *)ip, 33 * 20);
            memcpy_s(obj->name + i * 64, 64 * 20, (char *)name, 64 * 20);
        }
        obj->messageLen[i] = 160;
        (void)snprintf(obj->message[i], obj->messageLen[i], "this is %d;", i);
    }

    // 创建逻辑表
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer CODEC(no), time integer CODEC(no), name char(64) CODEC(no), ip inet CODEC(no),"
        " message blob(160) CODEC(no), ns text CODEC(no), ip1 inet CODEC(no))"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '5000 MB',"
        " compression = 'fast(rapidlz)');",
        g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入数据
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C4StrSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询并校验
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "select * from %s order by id;", g_tableName);
    ret = readRecordAndCompareTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol inet COdEc(No);", g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "select * from %s order by id;", g_tableName);
    ret = readRecordAndCompareTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 使用新增列进行过滤
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "select * from %s where newcol like '%%' order by id;", g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_SEMANTIC_ERROR, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmsysview -sql \"select newCol, id, ip from %s where newcol like '%%' order by id;\" -s %s", g_tableName,
        g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_SEMANTIC_ERROR);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql,", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 释放内存
    TestFreeTagC3Int8C4StrAlloc(obj, count);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
03.创建时序逻辑表，插入数据，进行语句查询，新增列为inet类型，使用原先语句查询，使用新增量列进行过滤，含like过滤，like含中文字符,
过滤不到数据，预期报错，inet不支持模糊查询
**************************************************************************** */
TEST_F(tsdbAlterEnh_001_test, Timing_054_005_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_SEMANTIC_ERROR);
    int ret = 0;
    system("rm -rf /home/<USER>/");
    char tableName[1024] = {0};

    constexpr int64_t count = 20;
    C3Int8C4StrT *obj = NULL;
    ret = TestAllocTagC3Int8C4Str(&obj, count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char ip[count][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[count][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc", "lucy",
        "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    GenerateRandomString(obj->ns, count);
    for (int i = 0; i < count; i++) {
        obj->id[i] = i;
        obj->time[i] = 1704067200 + i;
        if (i % 20 == 0) {
            memcpy_s(obj->ip + i * 33, 33 * 20, (char *)ip, 33 * 20);
            memcpy_s(obj->name + i * 64, 64 * 20, (char *)name, 64 * 20);
        }
        obj->messageLen[i] = 160;
        (void)snprintf(obj->message[i], obj->messageLen[i], "this is %d;", i);
    }

    // 创建逻辑表
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet,"
        " message blob(160), ns text, ip1 inet)"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '5000 MB',"
        " compression = 'fast(rapidlz)');",
        g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入数据
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C4StrSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询并校验
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "select * from %s order by id;", g_tableName);
    ret = readRecordAndCompareTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol inet;", g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "select * from %s order by id;", g_tableName);
    ret = readRecordAndCompareTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 使用新增列进行过滤
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "select * from %s where newcol like '%%中%%' order by id;", g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_SEMANTIC_ERROR, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmsysview -sql \"select newCol, id, ip from %s where newcol like '%%中%%' order by id;\" -s %s", g_tableName,
        g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_SEMANTIC_ERROR);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql,", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 释放内存
    TestFreeTagC3Int8C4StrAlloc(obj, count);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
04.创建时序逻辑表，插入数据，进行语句查询，新增列为inet类型，使用原先语句查询，使用新增量列进行group by和order by
**************************************************************************** */
TEST_F(tsdbAlterEnh_001_test, Timing_054_005_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    system("rm -rf /home/<USER>/");
    char tableName[1024] = {0};
    constexpr int64_t count = 20;
    C3Int8C4StrT *obj = NULL;
    ret = TestAllocTagC3Int8C4Str(&obj, count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char ip[count][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[count][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc", "lucy",
        "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    GenerateRandomString(obj->ns, count);
    for (int i = 0; i < count; i++) {
        obj->id[i] = i;
        obj->time[i] = 1704067200 + i;
        if (i % 20 == 0) {
            memcpy_s(obj->ip + i * 33, 33 * 20, (char *)ip, 33 * 20);
            memcpy_s(obj->name + i * 64, 64 * 20, (char *)name, 64 * 20);
        }
        obj->messageLen[i] = 160;
        (void)snprintf(obj->message[i], obj->messageLen[i], "this is %d;", i);
    }

    // 创建逻辑表
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet,"
        " message blob(160), ns text, ip1 inet)"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '5000 MB',"
        " compression = 'fast(rapidlz)');",
        g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入数据
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C4StrSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询并校验
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "select * from %s order by id;", g_tableName);
    ret = readRecordAndCompareTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol inet;", g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "select * from %s order by id;", g_tableName);
    ret = readRecordAndCompareTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 使用新增列进行group by，order by
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "select id, time, name, ip, ns, ip1, newcol from %s group by newcol, id "
        "order by id, newcol limit 10000;",
        g_tableName);
    ret = readRecordAndCompareNoBlobTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放内存
    TestFreeTagC3Int8C4StrAlloc(obj, count);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
05.创建时序逻辑表，插入数据，进行语句查询，新增列为inet类型，插入数据含新增列，对新增列进行过滤
**************************************************************************** */
TEST_F(tsdbAlterEnh_001_test, Timing_054_005_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    system("rm -rf /home/<USER>/");
    char tableName[1024] = {0};
    constexpr int64_t count = 20;
    C3Int8C4StrT *obj = NULL;
    ret = TestAllocTagC3Int8C4Str(&obj, count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char ip[count][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[count][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc", "lucy",
        "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    GenerateRandomString(obj->ns, count);
    for (int i = 0; i < count; i++) {
        obj->id[i] = i;
        obj->time[i] = 1704067200 + i;
        if (i % 20 == 0) {
            memcpy_s(obj->ip + i * 33, 33 * 20, (char *)ip, 33 * 20);
            memcpy_s(obj->name + i * 64, 64 * 20, (char *)name, 64 * 20);
        }
        obj->messageLen[i] = 160;
        (void)snprintf(obj->message[i], obj->messageLen[i], "this is %d;", i);
    }

    // 创建逻辑表
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet,"
        " message blob(160), ns text, ip1 inet)"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '5000 MB',"
        " compression = 'fast(rapidlz)');",
        g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入数据
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C4StrSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询并校验
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "select * from %s order by id;", g_tableName);
    ret = readRecordAndCompareTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol inet;", g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "select * from %s order by id;", g_tableName);
    ret = readRecordAndCompareTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 再次插入数据，新增ip列插入数据和原先ip列数据一样
    for (int i = 0; i < count; i++) {
        obj->id[i] = i + count;
    }
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C5StrSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "select * from %s where newcol > '00000000' or "
        "newCol >= '00000000000000000000000000000000' and id >= 20 order by id, newcol limit 10000;",
        g_tableName);
    ret = readRecordAndNewColCompareTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放内存
    TestFreeTagC3Int8C4StrAlloc(obj, count);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
06.创建时序逻辑表，插入数据，进行语句查询，新增列为inet类型，插入数据含新增列，对新增列进行group by和order by
**************************************************************************** */
TEST_F(tsdbAlterEnh_001_test, Timing_054_005_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    system("rm -rf /home/<USER>/");
    char tableName[1024] = {0};
    constexpr int64_t count = 20;
    C3Int8C4StrT *obj = NULL;
    ret = TestAllocTagC3Int8C4Str(&obj, count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char ip[count][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[count][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc", "lucy",
        "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    GenerateRandomString(obj->ns, count);
    for (int i = 0; i < count; i++) {
        obj->id[i] = i;
        obj->time[i] = 1704067200 + i;
        if (i % 20 == 0) {
            memcpy_s(obj->ip + i * 33, 33 * 20, (char *)ip, 33 * 20);
            memcpy_s(obj->name + i * 64, 64 * 20, (char *)name, 64 * 20);
        }
        obj->messageLen[i] = 160;
        (void)snprintf(obj->message[i], obj->messageLen[i], "this is %d;", i);
    }

    // 创建逻辑表
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet,"
        " message blob(160), ns text, ip1 inet)"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '5000 MB',"
        " compression = 'fast(rapidlz)');",
        g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入数据
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C4StrSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询并校验
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "select * from %s order by id;", g_tableName);
    ret = readRecordAndCompareTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol inet;", g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "select * from %s order by id;", g_tableName);
    ret = readRecordAndCompareTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 再次插入数据，新增ip列插入数据和原先ip列数据一样
    for (int i = 0; i < count; i++) {
        obj->id[i] = i + count;
    }
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C5StrSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "select id, time, name, ip, ns, ip1, newcol from %s where "
        "newcol > '00000000' or newCol >= '00000000000000000000000000000000' and id >= 20 group by newcol, id "
        "order by id, newcol limit 10000;",
        g_tableName);
    ret = readRecordAndNewColCompareNoBlobTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放内存
    TestFreeTagC3Int8C4StrAlloc(obj, count);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
07.创建时序逻辑表，插入数据，进行语句查询，新增列为text类型，使用原先语句查询，使用新增量列进行过滤
**************************************************************************** */
TEST_F(tsdbAlterEnh_001_test, Timing_054_005_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    system("rm -rf /home/<USER>/");
    char tableName[1024] = {0};
    constexpr int64_t count = 20;
    C3Int8C4StrT *obj = NULL;
    ret = TestAllocTagC3Int8C4Str(&obj, count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char ip[count][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[count][64] = {"david", "nut中", "bob中", "olivia，", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc",
        "lucy", "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    GenerateRandomAndChinString(obj->ns, count);
    for (int i = 0; i < count; i++) {
        obj->id[i] = i;
        obj->time[i] = 1704067200 + i;
        if (i % 20 == 0) {
            memcpy_s(obj->ip + i * 33, 33 * 20, (char *)ip, 33 * 20);
            memcpy_s(obj->name + i * 64, 64 * 20, (char *)name, 64 * 20);
        }
        obj->messageLen[i] = 160;
        (void)snprintf(obj->message[i], obj->messageLen[i], "this is %d;", i);
    }

    // 创建逻辑表
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet,"
        " message blob(160), ns text, ip1 inet)"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '5000 MB',"
        " compression = 'fast(rapidlz)');",
        g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入数据
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C4StrSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询并校验
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "select * from %s order by id;", g_tableName);
    ret = readRecordAndCompareTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol text;", g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "select * from %s order by id;", g_tableName);
    ret = readRecordAndCompareTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(
        g_sqlCmd, MAX_CMD_SIZE, "select * from %s  where ns like '%%中%%' and newcol = '' order by id;", g_tableName);
    ret = readRecordAndCompareTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放内存
    TestFreeTagC3Int8C4StrAlloc(obj, count);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
08.创建时序逻辑表，插入数据，进行语句查询，新增列为text类型，使用原先语句查询，使用新增量列进行过滤，含like过滤，能过滤到数据
**************************************************************************** */
TEST_F(tsdbAlterEnh_001_test, Timing_054_005_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    system("rm -rf /home/<USER>/");
    char tableName[1024] = {0};
    constexpr int64_t count = 20;
    C3Int8C4StrT *obj = NULL;
    ret = TestAllocTagC3Int8C4Str(&obj, count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char ip[count][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[count][64] = {"david", "nut中", "bob中", "olivia，", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc",
        "lucy", "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    GenerateRandomAndChinString(obj->ns, count);
    for (int i = 0; i < count; i++) {
        obj->id[i] = i;
        obj->time[i] = 1704067200 + i;
        if (i % 20 == 0) {
            memcpy_s(obj->ip + i * 33, 33 * 20, (char *)ip, 33 * 20);
            memcpy_s(obj->name + i * 64, 64 * 20, (char *)name, 64 * 20);
        }
        obj->messageLen[i] = 160;
        (void)snprintf(obj->message[i], obj->messageLen[i], "this is %d;", i);
    }

    // 创建逻辑表
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet,"
        " message blob(160), ns text, ip1 inet)"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '5000 MB',"
        " compression = 'fast(rapidlz)');",
        g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入数据
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C4StrSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询并校验
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "select * from %s order by id;", g_tableName);
    ret = readRecordAndCompareTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol text;", g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "select * from %s order by id;", g_tableName);
    ret = readRecordAndCompareTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "select * from %s  where ns like '%%中%%' and newcol like '%%' order by id;",
        g_tableName);
    ret = readRecordAndCompareTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放内存
    TestFreeTagC3Int8C4StrAlloc(obj, count);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
09.创建时序逻辑表，插入数据，进行语句查询，新增列为text类型，使用原先语句查询，使用新增量列进行group by和order by
**************************************************************************** */
TEST_F(tsdbAlterEnh_001_test, Timing_054_005_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    system("rm -rf /home/<USER>/");
    char tableName[1024] = {0};
    constexpr int64_t count = 20;
    C3Int8C4StrT *obj = NULL;
    ret = TestAllocTagC3Int8C4Str(&obj, count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char ip[count][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[count][64] = {"david", "nut中", "bob中", "olivia，", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc",
        "lucy", "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    GenerateRandomAndChinString(obj->ns, count);
    for (int i = 0; i < count; i++) {
        obj->id[i] = i;
        obj->time[i] = 1704067200 + i;
        if (i % 20 == 0) {
            memcpy_s(obj->ip + i * 33, 33 * 20, (char *)ip, 33 * 20);
            memcpy_s(obj->name + i * 64, 64 * 20, (char *)name, 64 * 20);
        }
        obj->messageLen[i] = 160;
        (void)snprintf(obj->message[i], obj->messageLen[i], "this is %d;", i);
    }

    // 创建逻辑表
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet,"
        " message blob(160), ns text, ip1 inet)"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '5000 MB',"
        " compression = 'fast(rapidlz)');",
        g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入数据
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C4StrSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询并校验
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "select * from %s order by id;", g_tableName);
    ret = readRecordAndCompareTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol text;", g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "select * from %s order by id;", g_tableName);
    ret = readRecordAndCompareTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "select id, time, name, ip, ns, ip1, newcol from %s group by newcol, id "
        "order by id, newcol limit 10000;",
        g_tableName);
    ret = readRecordAndNewTextDefaultCompareNoBlobTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放内存
    TestFreeTagC3Int8C4StrAlloc(obj, count);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
10.创建时序逻辑表，插入数据，进行语句查询，新增列为text类型，插入数据含新增列，对新增列进行过滤，含like过滤，like含中文字符，能过滤到数据
**************************************************************************** */
TEST_F(tsdbAlterEnh_001_test, Timing_054_005_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    system("rm -rf /home/<USER>/");
    char tableName[1024] = {0};
    constexpr int64_t count = 20;
    C3Int8C4StrT *obj = NULL;
    ret = TestAllocTagC3Int8C4Str(&obj, count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char ip[count][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[count][64] = {"david", "nut中", "bob中", "olivia，", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc",
        "lucy", "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    GenerateRandomAndChinString(obj->ns, count);
    for (int i = 0; i < count; i++) {
        obj->id[i] = i;
        obj->time[i] = 1704067200 + i;
        if (i % 20 == 0) {
            memcpy_s(obj->ip + i * 33, 33 * 20, (char *)ip, 33 * 20);
            memcpy_s(obj->name + i * 64, 64 * 20, (char *)name, 64 * 20);
        }
        obj->messageLen[i] = 160;
        (void)snprintf(obj->message[i], obj->messageLen[i], "this is %d;", i);
    }

    // 创建逻辑表
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet,"
        " message blob(160), ns text, ip1 inet)"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '5000 MB',"
        " compression = 'fast(rapidlz)');",
        g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入数据
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C4StrSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询并校验
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "select * from %s order by id;", g_tableName);
    ret = readRecordAndCompareTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol text;", g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "select * from %s order by id;", g_tableName);
    ret = readRecordAndCompareTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 再次插入数据，新增ip列插入数据和原先ip列数据一样
    for (int i = 0; i < count; i++) {
        obj->id[i] = i + count;
    }
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C5StrTextSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "select * from %s where newcol like '%%中%%' and id >= 20 order by "
        "id, newcol limit 10000;",
        g_tableName);
    ret = readRecordAndNewColTextCompareTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放内存
    TestFreeTagC3Int8C4StrAlloc(obj, count);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
11.创建时序逻辑表，插入数据，进行语句查询，新增列为text类型，使用原先语句查询，使用新增量列进行group by和order by
**************************************************************************** */
TEST_F(tsdbAlterEnh_001_test, Timing_054_005_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    system("rm -rf /home/<USER>/");
    char tableName[1024] = {0};
    constexpr int64_t count = 20;
    C3Int8C4StrT *obj = NULL;
    ret = TestAllocTagC3Int8C4Str(&obj, count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char ip[count][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[count][64] = {"david", "nut中", "bob中", "olivia，", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc",
        "lucy", "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    GenerateRandomAndChinString(obj->ns, count);
    for (int i = 0; i < count; i++) {
        obj->id[i] = i;
        obj->time[i] = 1704067200 + i;
        if (i % 20 == 0) {
            memcpy_s(obj->ip + i * 33, 33 * 20, (char *)ip, 33 * 20);
            memcpy_s(obj->name + i * 64, 64 * 20, (char *)name, 64 * 20);
        }
        obj->messageLen[i] = 160;
        (void)snprintf(obj->message[i], obj->messageLen[i], "this is %d;", i);
    }

    // 创建逻辑表
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet,"
        " message blob(160), ns text, ip1 inet)"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '5000 MB',"
        " compression = 'fast(rapidlz)');",
        g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入数据
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C4StrSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询并校验
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "select * from %s order by id;", g_tableName);
    ret = readRecordAndCompareTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol text;", g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "select * from %s order by id;", g_tableName);
    ret = readRecordAndCompareTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 再次插入数据，新增ip列插入数据和原先ip列数据一样
    for (int i = 0; i < count; i++) {
        obj->id[i] = i + count;
    }
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C5StrTextSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "select id, time, name, ip, ns, ip1, newcol from %s where "
        "newcol like '%%中%%' and id >= 20 group by newcol, id "
        "order by id, newcol limit 10000;",
        g_tableName);
    ret = readRecordAndNewColTextCompareNoBlobTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放内存
    TestFreeTagC3Int8C4StrAlloc(obj, count);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
12.创建时序逻辑表，插入数据，进行语句查询（不含group by），新增列为blob类型，使用原先语句查询，使用where过滤，预期报错
**************************************************************************** */
TEST_F(tsdbAlterEnh_001_test, Timing_054_005_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_SEMANTIC_ERROR);
    int ret = 0;
    system("rm -rf /home/<USER>/");
    char tableName[1024] = {0};
    constexpr int64_t count = 20;
    C3Int8C4StrT *obj = NULL;
    ret = TestAllocTagC3Int8C4Str(&obj, count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char ip[count][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[count][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc", "lucy",
        "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    GenerateRandomAndChinString(obj->ns, count);
    for (int i = 0; i < count; i++) {
        obj->id[i] = i;
        obj->time[i] = 1704067200 + i;
        if (i % 20 == 0) {
            memcpy_s(obj->ip + i * 33, 33 * 20, (char *)ip, 33 * 20);
            memcpy_s(obj->name + i * 64, 64 * 20, (char *)name, 64 * 20);
        }
        obj->messageLen[i] = 160;
        (void)snprintf(obj->message[i], obj->messageLen[i], "this is 中%d，zhong,中;", i);
    }

    // 创建逻辑表
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet,"
        " message blob(160), ns text, ip1 inet)"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '5000 MB',"
        " compression = 'fast(rapidlz)');",
        g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入数据
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C4StrSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询并校验
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "select * from %s order by id;", g_tableName);
    ret = readRecordAndCompareTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol blob(160) CODEC(no);", g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "select * from %s order by id;", g_tableName);
    ret = readRecordAndCompareTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 使用新增blob列过滤预期报错
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "select * from %s where newcol like '%%' order by id;", g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_SEMANTIC_ERROR, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmsysview -sql \"select newCol, id, ip from %s where newcol like '%%' order by id;\" -s %s", g_tableName,
        g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_SEMANTIC_ERROR);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql,", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 释放内存
    TestFreeTagC3Int8C4StrAlloc(obj, count);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
13.创建时序逻辑表，插入数据，进行语句查询（不含group by），新增列为blob类型，使用原先语句查询，查询blob默认值
**************************************************************************** */
TEST_F(tsdbAlterEnh_001_test, Timing_054_005_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    system("rm -rf /home/<USER>/");
    char tableName[1024] = {0};
    constexpr int64_t count = 20;
    C3Int8C4StrT *obj = NULL;
    ret = TestAllocTagC3Int8C4Str(&obj, count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char ip[count][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[count][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc", "lucy",
        "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    GenerateRandomAndChinString(obj->ns, count);
    for (int i = 0; i < count; i++) {
        obj->id[i] = i;
        obj->time[i] = 1704067200 + i;
        if (i % 20 == 0) {
            memcpy_s(obj->ip + i * 33, 33 * 20, (char *)ip, 33 * 20);
            memcpy_s(obj->name + i * 64, 64 * 20, (char *)name, 64 * 20);
        }
        obj->messageLen[i] = 160;
        (void)snprintf(obj->message[i], obj->messageLen[i], "this is 中%d，zhong,中;", i);
    }

    // 创建逻辑表
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet,"
        " message blob(160), ns text, ip1 inet)"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '5000 MB',"
        " compression = 'fast(rapidlz)');",
        g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入数据
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C4StrSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询并校验
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "select * from %s order by id;", g_tableName);
    ret = readRecordAndCompareTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol blob(160) CODEC(no);", g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "select * from %s order by id;", g_tableName);
    ret = readRecordAndCompareTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询新增blob类型的默认值为空字符
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "select * from %s order by id;", g_tableName);
    ret = readRecordAndNewColBlobDefaultCompareTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放内存
    TestFreeTagC3Int8C4StrAlloc(obj, count);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
14.创建时序逻辑表，插入数据，进行语句查询（不含group by），新增列为blob类型，使用原先语句查询，插入数据含新增列,
使用新的查询（含blob）语句进行查询
**************************************************************************** */
TEST_F(tsdbAlterEnh_001_test, Timing_054_005_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    system("rm -rf /home/<USER>/");
    char tableName[1024] = {0};
    constexpr int64_t count = 20;
    C3Int8C4StrT *obj = NULL;
    ret = TestAllocTagC3Int8C4Str(&obj, count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char ip[count][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[count][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc", "lucy",
        "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    GenerateRandomAndChinString(obj->ns, count);
    for (int i = 0; i < count; i++) {
        obj->id[i] = i;
        obj->time[i] = 1704067200 + i;
        if (i % 20 == 0) {
            memcpy_s(obj->ip + i * 33, 33 * 20, (char *)ip, 33 * 20);
            memcpy_s(obj->name + i * 64, 64 * 20, (char *)name, 64 * 20);
        }
        obj->messageLen[i] = 160;
        (void)snprintf(obj->message[i], obj->messageLen[i], "this is 中%d，zhong,中;", i);
    }

    // 创建逻辑表
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet,"
        " message blob(160), ns text, ip1 inet)"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '5000 MB',"
        " compression = 'fast(rapidlz)');",
        g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入数据
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C4StrSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询并校验
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "select * from %s order by id;", g_tableName);
    ret = readRecordAndCompareTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol blob(160) CODEC(no);", g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "select * from %s order by id;", g_tableName);
    ret = readRecordAndCompareTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据，新增blob列和原先blob列数据一样
    for (int i = 0; i < count; i++) {
        obj->id[i] = i + count;
    }
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C5StrBlobSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询新增blob类型的默认值为空字符
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "select * from %s where id >= 20 order by id limit 10000;", g_tableName);
    ret = readRecordAndNewColBlobCompareTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放内存
    TestFreeTagC3Int8C4StrAlloc(obj, count);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
15.创建时序逻辑表，插入数据，进行语句查询，新增列为text类型，插入数据，新增inet类型，插入数据，使用原先语句进行查询，
使用新增两列进行过滤，包含like过滤
**************************************************************************** */
TEST_F(tsdbAlterEnh_001_test, Timing_054_005_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    system("rm -rf /home/<USER>/");
    char tableName[1024] = {0};
    constexpr int64_t count = 20;
    C3Int8C4StrT *obj = NULL;
    ret = TestAllocTagC3Int8C4Str(&obj, count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char ip[count][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[count][64] = {"david", "nut中", "bob中", "olivia，", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc",
        "lucy", "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    GenerateRandomAndChinString(obj->ns, count);
    for (int i = 0; i < count; i++) {
        obj->id[i] = i;
        obj->time[i] = 1704067200 + i;
        if (i % 20 == 0) {
            memcpy_s(obj->ip + i * 33, 33 * 20, (char *)ip, 33 * 20);
            memcpy_s(obj->name + i * 64, 64 * 20, (char *)name, 64 * 20);
        }
        obj->messageLen[i] = 160;
        (void)snprintf(obj->message[i], obj->messageLen[i], "this is 中%d，zhong,中;", i);
    }

    // 创建逻辑表
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet,"
        " message blob(160), ns text, ip1 inet)"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '5000 MB',"
        " compression = 'fast(rapidlz)');",
        g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入数据
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C4StrSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询并校验
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "select * from %s order by id;", g_tableName);
    ret = readRecordAndCompareTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol text;", g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "select * from %s order by id;", g_tableName);
    ret = readRecordAndCompareTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 再次插入数据，新增text列插入数据和原先text列数据一样
    for (int i = 0; i < count; i++) {
        obj->id[i] = i + count;
    }
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C5StrTextSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "select * from %s where newcol like '%%中%%' and id >= 20 order by "
        "id, newcol limit 10000;",
        g_tableName);
    ret = readRecordAndNewColTextCompareTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 新增inet类型
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol2 inet;", g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 再次插入数据，新增ip列插入数据和原先ip列数据一样
    for (int i = 0; i < count; i++) {
        obj->id[i] = i + count * 2;
    }
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C6StrTextIpSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select id, name, newcol2 from %s order by id;\" -s %s",
        g_tableName, g_connServerTsdb);
    ret = executeCommand(g_command, "id: 59", "name: John", "newcol2: f0d0c0a0124332543668542246548622");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "select * from %s where newcol like '%%中%%' and newcol2 != '00000000' "
        "and id >= 40 order by id, newcol limit 10000;",
        g_tableName);
    ret = readRecordAndNewColTextIPCompareTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放内存
    TestFreeTagC3Int8C4StrAlloc(obj, count);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
16.创建时序逻辑表，插入数据，进行语句查询，新增列为text类型，插入数据，新增inet类型，插入数据，使用原先语句进行查询，
对新增两列进行group by和order by
**************************************************************************** */
TEST_F(tsdbAlterEnh_001_test, Timing_054_005_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    system("rm -rf /home/<USER>/");
    char tableName[1024] = {0};
    constexpr int64_t count = 20;
    C3Int8C4StrT *obj = NULL;
    ret = TestAllocTagC3Int8C4Str(&obj, count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char ip[count][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[count][64] = {"david", "nut中", "bob中", "olivia，", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc",
        "lucy", "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    GenerateRandomAndChinString(obj->ns, count);
    for (int i = 0; i < count; i++) {
        obj->id[i] = i;
        obj->time[i] = 1704067200 + i;
        if (i % 20 == 0) {
            memcpy_s(obj->ip + i * 33, 33 * 20, (char *)ip, 33 * 20);
            memcpy_s(obj->name + i * 64, 64 * 20, (char *)name, 64 * 20);
        }
        obj->messageLen[i] = 160;
        (void)snprintf(obj->message[i], obj->messageLen[i], "this is 中%d，zhong,中;", i);
    }

    // 创建逻辑表
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet,"
        " message blob(160), ns text, ip1 inet)"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '5000 MB',"
        " compression = 'fast(rapidlz)');",
        g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入数据
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C4StrSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询并校验
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "select * from %s order by id;", g_tableName);
    ret = readRecordAndCompareTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol text;", g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "select * from %s order by id;", g_tableName);
    ret = readRecordAndCompareTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 再次插入数据，新增text列插入数据和原先text列数据一样
    for (int i = 0; i < count; i++) {
        obj->id[i] = i + count;
    }
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C5StrTextSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "select * from %s where newcol like '%%中%%' and id >= 20 order by "
        "id, newcol limit 10000;",
        g_tableName);
    ret = readRecordAndNewColTextCompareTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 新增inet类型
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol2 inet;", g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 再次插入数据，新增ip列插入数据和原先ip列数据一样
    for (int i = 0; i < count; i++) {
        obj->id[i] = i + count * 2;
    }
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C6StrTextIpSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select id, name, newcol2 from %s order by id;\" -s %s",
        g_tableName, g_connServerTsdb);
    ret = executeCommand(g_command, "id: 59", "name: John", "newcol2: f0d0c0a0124332543668542246548622");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "select id, time, name, ip, ns, ip1, newcol, newcol2 from %s where newcol like '%%中%%' and newcol2 != "
        "'00000000' and id >= 40 group by newcol, newcol2, id order by id, newcol, newcol2 limit 10000;",
        g_tableName);
    ret = readRecordAndNewColTextIPCompareNoBlobTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放内存
    TestFreeTagC3Int8C4StrAlloc(obj, count);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
17.创建时序逻辑表，插入数据，进行语句查询，新增列为text类型，插入数据，新增列为inet类型，插入数据，新增列为blob类型，插入数据，
使用原先语句进行查询，使用新增两列（text、inet）进行过滤
**************************************************************************** */
TEST_F(tsdbAlterEnh_001_test, Timing_054_005_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    system("rm -rf /home/<USER>/");
    char tableName[1024] = {0};
    constexpr int64_t count = 20;
    C3Int8C4StrT *obj = NULL;
    ret = TestAllocTagC3Int8C4Str(&obj, count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char ip[count][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[count][64] = {"david", "nut中", "bob中", "olivia，", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc",
        "lucy", "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    GenerateRandomAndChinString(obj->ns, count);
    for (int i = 0; i < count; i++) {
        obj->id[i] = i;
        obj->time[i] = 1704067200 + i;
        if (i % 20 == 0) {
            memcpy_s(obj->ip + i * 33, 33 * 20, (char *)ip, 33 * 20);
            memcpy_s(obj->name + i * 64, 64 * 20, (char *)name, 64 * 20);
        }
        obj->messageLen[i] = 160;
        (void)snprintf(obj->message[i], obj->messageLen[i], "this is 中%d，zhong,中;", i);
    }

    // 创建逻辑表
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet,"
        " message blob(160), ns text, ip1 inet)"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '5000 MB',"
        " compression = 'fast(rapidlz)');",
        g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入数据
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C4StrSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询并校验
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "select * from %s order by id;", g_tableName);
    ret = readRecordAndCompareTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol text;", g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "select * from %s order by id;", g_tableName);
    ret = readRecordAndCompareTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 再次插入数据，新增text列插入数据和原先text列数据一样
    for (int i = 0; i < count; i++) {
        obj->id[i] = i + count;
    }
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C5StrTextSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "select * from %s where newcol like '%%中%%' and id >= 20 order by "
        "id, newcol limit 10000;",
        g_tableName);
    ret = readRecordAndNewColTextCompareTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 新增inet类型
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol2 inet;", g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 再次插入数据，新增ip列插入数据和原先ip列数据一样
    for (int i = 0; i < count; i++) {
        obj->id[i] = i + count * 2;
    }
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C6StrTextIpSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 新增blob类型
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol3 blob;", g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 再次插入数据，新增blob列插入数据和原先blob列数据一样
    for (int i = 0; i < count; i++) {
        obj->id[i] = i + count * 3;
    }
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C7StrTextIpBlobSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select id, name, newcol2 from %s order by id;\" -s %s",
        g_tableName, g_connServerTsdb);
    ret = executeCommand(g_command, "id: 59", "name: John", "newcol2: f0d0c0a0124332543668542246548622");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "select * from %s where newcol like '%%中%%' and newcol2 != '00000000' "
        "and id >= 60 order by id, newcol limit 10000;",
        g_tableName);
    ret = readRecordAndNewColTextIPBlobCompareTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放内存
    TestFreeTagC3Int8C4StrAlloc(obj, count);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
18.创建时序逻辑表，插入数据，进行语句查询，新增列为text类型，插入数据，新增列为inet类型，插入数据，新增列为blob类型，插入数据，
使用原先语句进行查询，使用新增两列（text、inet）进行group by和order by
**************************************************************************** */
TEST_F(tsdbAlterEnh_001_test, Timing_054_005_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    system("rm -rf /home/<USER>/");
    char tableName[1024] = {0};
    constexpr int64_t count = 20;
    C3Int8C4StrT *obj = NULL;
    ret = TestAllocTagC3Int8C4Str(&obj, count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char ip[count][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[count][64] = {"david", "nut中", "bob中", "olivia，", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc",
        "lucy", "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    GenerateRandomAndChinString(obj->ns, count);
    for (int i = 0; i < count; i++) {
        obj->id[i] = i;
        obj->time[i] = 1704067200 + i;
        if (i % 20 == 0) {
            memcpy_s(obj->ip + i * 33, 33 * 20, (char *)ip, 33 * 20);
            memcpy_s(obj->name + i * 64, 64 * 20, (char *)name, 64 * 20);
        }
        obj->messageLen[i] = 160;
        (void)snprintf(obj->message[i], obj->messageLen[i], "this is 中%d，zhong,中;", i);
    }

    // 创建逻辑表
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet,"
        " message blob(160), ns text, ip1 inet)"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '5000 MB',"
        " compression = 'fast(rapidlz)');",
        g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入数据
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C4StrSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询并校验
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "select * from %s order by id;", g_tableName);
    ret = readRecordAndCompareTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol text;", g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "select * from %s order by id;", g_tableName);
    ret = readRecordAndCompareTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 再次插入数据，新增text列插入数据和原先text列数据一样
    for (int i = 0; i < count; i++) {
        obj->id[i] = i + count;
    }
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C5StrTextSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "select * from %s where newcol like '%%中%%' and id >= 20 order by "
        "id, newcol limit 10000;",
        g_tableName);
    ret = readRecordAndNewColTextCompareTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 新增inet类型
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol2 inet;", g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 再次插入数据，新增ip列插入数据和原先ip列数据一样
    for (int i = 0; i < count; i++) {
        obj->id[i] = i + count * 2;
    }
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C6StrTextIpSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 新增blob类型
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol3 blob;", g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 再次插入数据，新增blob列插入数据和原先blob列数据一样
    for (int i = 0; i < count; i++) {
        obj->id[i] = i + count * 3;
    }
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C7StrTextIpBlobSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select id, name, newcol2 from %s order by id;\" -s %s",
        g_tableName, g_connServerTsdb);
    ret = executeCommand(g_command, "id: 59", "name: John", "newcol2: f0d0c0a0124332543668542246548622");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "select id, time, name, ip, ns, ip1, newcol, newcol2 from %s where newcol like '%%中%%' and newcol2 != "
        "'00000000' and id >= 60 group by newcol, newcol2, id order by id, newcol, newcol2 limit 10000;",
        g_tableName);
    ret = readRecordAndNewColTextIPCompareNoBlobTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放内存
    TestFreeTagC3Int8C4StrAlloc(obj, count);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
19.创建时序逻辑表1，插入2次每批29条数据，进行语句查询，新增列为text类型，插入数据2次29条数据，触发磁盘整理，新增列为inet类型，
插入数据29次29条数据，新增列为blob类型，插入数据29条数据，触发磁盘整理
**************************************************************************** */
TEST_F(tsdbAlterEnh_001_test, Timing_054_005_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    system("rm -rf /home/<USER>/");
    char tableName[1024] = {0};
    constexpr int64_t count = 29, cpCnt = 20;
    int tmp = count;
    C3Int8C4StrT *obj = NULL;
    ret = TestAllocTagC3Int8C4Str(&obj, count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char ip[cpCnt][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[cpCnt][64] = {"david", "nut中", "bob中", "olivia，", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc",
        "lucy", "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    GenerateRandomAndChinString(obj->ns, count);
    for (int i = 0; i < count; i++) {
        obj->id[i] = i;
        obj->time[i] = 1704067200 + i;
        if ((i % cpCnt == 0) && (tmp >= cpCnt)) {
            tmp -= cpCnt;
            memcpy_s(obj->ip + i * 33, 33 * cpCnt, (char *)ip, 33 * cpCnt);
            memcpy_s(obj->name + i * 64, 64 * cpCnt, (char *)name, 64 * cpCnt);
        } else if ((i % 20 == 0) && (tmp > 0)) {
            memcpy_s(obj->ip + i * 33, 33 * tmp, (char *)ip, 33 * tmp);
            memcpy_s(obj->name + i * 64, 64 * tmp, (char *)name, 64 * tmp);
        }
        obj->messageLen[i] = 160;
        (void)snprintf(obj->message[i], obj->messageLen[i], "this is 中%d，zhong,中;", i);
    }

    // 创建逻辑表
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet,"
        " message blob(160), ns text)"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '5000 MB',"
        " compression = 'fast(rapidlz)');",
        g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入数据
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C4StrSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        obj->id[i] = i + count;
    }
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C4StrSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select count(*), id from %s order by id;\" -s %s",
        g_tableName, g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "id");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol text;", g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 触发磁盘整理,插入2次每批29
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C2Int8C4StrTextSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C2Int8C4StrTextSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE';\" -s %s", g_connServerTsdb);
    ret = executeCommand(g_command, "CU_CNT: 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 新增inet类型
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol2 inet;", g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        obj->id[i] = i + count * 2;
    }
    // 再次插入数据，新增ip列插入数据和原先ip列数据一样
    for (int j = 0; j < 29; j++) {
        ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C5StrTextIpSet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE';\" -s %s", g_connServerTsdb);
    ret = executeCommand(g_command, "CU_CNT: 31");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 新增blob类型
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol3 blob;", g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 再次插入数据，新增blob列插入数据和原先blob列数据一样
    for (int i = 0; i < count; i++) {
        obj->id[i] = i + count * 3;
    }
    // 触发磁盘整理，最终CU块数量是4个
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C6StrTextIpBlobSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE';\" -s %s", g_connServerTsdb);
    ret = executeCommand(g_command, "CU_CNT: 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 释放内存
    TestFreeTagC3Int8C4StrAlloc(obj, count);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
20.创建2个时序逻辑表（表结构不同），连接1对第一个逻辑表插入2批少量数据，连接2对第二个逻辑表插入2批少量数据，
连接2对两个逻辑表alter，连接1对逻辑表1插入数据触发落盘
**************************************************************************** */
TEST_F(tsdbAlterEnh_001_test, Timing_054_005_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = 0;
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /home/<USER>/");
    char tableName[1024] = {0};
    constexpr int64_t count = 29, cpCnt = 20;
    int tmp = count;
    C3Int8C4StrT *obj = NULL;
    ret = TestAllocTagC3Int8C4Str(&obj, count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char ip[cpCnt][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[cpCnt][64] = {"david", "nut中", "bob中", "olivia，", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc",
        "lucy", "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    GenerateRandomAndChinString(obj->ns, count);
    for (int i = 0; i < count; i++) {
        obj->id[i] = i;
        obj->time[i] = 1704067200 + i;
        if ((i % cpCnt == 0) && (tmp >= cpCnt)) {
            tmp -= cpCnt;
            memcpy_s(obj->ip + i * 33, 33 * cpCnt, (char *)ip, 33 * cpCnt);
            memcpy_s(obj->name + i * 64, 64 * cpCnt, (char *)name, 64 * cpCnt);
        } else if ((i % 20 == 0) && (tmp > 0)) {
            memcpy_s(obj->ip + i * 33, 33 * tmp, (char *)ip, 33 * tmp);
            memcpy_s(obj->name + i * 64, 64 * tmp, (char *)name, 64 * tmp);
        }
        obj->messageLen[i] = 160;
        (void)snprintf(obj->message[i], obj->messageLen[i], "this is 中%d，zhong,中;", i);
    }

    // 创建逻辑表
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet,"
        " message blob(160), ns text)"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '5000 MB',"
        " compression = 'fast(rapidlz)');",
        g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入数据
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C4StrSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        obj->id[i] = i + count;
    }
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C4StrSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select count(*), id from %s order by id;\" -s %s",
        g_tableName, g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "id");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 创建时序逻辑表2
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet,"
        " message text, ns blob(160), ns1 text)"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '5000 MB',"
        " compression = 'fast(rapidlz)');",
        g_tableName2);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 连接2对逻辑表2插入少量数据
    for (int j = 0; j < 2; j++) {
        ret = writeRecordTs(conn, stmt, g_tableName2, obj, count, C2Int8C5StrTextSet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 连接2 alter两个逻辑表
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol inet;", g_tableName);
    ret = GmcExecDirect(stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol text;", g_tableName2);
    ret = GmcExecDirect(stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE';\" -s %s", g_connServerTsdb);
    ret = executeCommand(g_command, "TABLE_NAME: tsdb1", "CU_CNT: 2", "ROW_CNT: 58");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 连接1对逻辑表1插入数据触发落盘
    for (int i = 0; i < count; i++) {
        obj->id[i] = i + count * 2;
    }
    for (int j = 0; j < 30; j++) {
        ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C2Int8C5StrTextIpSet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE';\" -s %s", g_connServerTsdb);
    ret = executeCommand(g_command, "TABLE_NAME: tsdb1", "CU_CNT: 2", "ROW_CNT: 928");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 删除tsdb2
    ret = DropCmTable(g_stmt, g_tableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 断开连接
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放内存
    TestFreeTagC3Int8C4StrAlloc(obj, count);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
21.创建2个时序逻辑表（表结构不同），连接1对第一个逻辑表插入2批少量数据，连接2对第二个逻辑表插入2批少量数据，
连接2对两个逻辑表alter，连接1对逻辑表2插入数据触发落盘
**************************************************************************** */
TEST_F(tsdbAlterEnh_001_test, Timing_054_005_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = 0;
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /home/<USER>/");
    char tableName[1024] = {0};
    constexpr int64_t count = 29, cpCnt = 20;
    int tmp = count;
    C3Int8C4StrT *obj = NULL;
    ret = TestAllocTagC3Int8C4Str(&obj, count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char ip[cpCnt][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[cpCnt][64] = {"david", "nut中", "bob中", "olivia，", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc",
        "lucy", "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    GenerateRandomAndChinString(obj->ns, count);
    for (int i = 0; i < count; i++) {
        obj->id[i] = i;
        obj->time[i] = 1704067200 + i;
        if ((i % cpCnt == 0) && (tmp >= cpCnt)) {
            tmp -= cpCnt;
            memcpy_s(obj->ip + i * 33, 33 * cpCnt, (char *)ip, 33 * cpCnt);
            memcpy_s(obj->name + i * 64, 64 * cpCnt, (char *)name, 64 * cpCnt);
        } else if ((i % 20 == 0) && (tmp > 0)) {
            memcpy_s(obj->ip + i * 33, 33 * tmp, (char *)ip, 33 * tmp);
            memcpy_s(obj->name + i * 64, 64 * tmp, (char *)name, 64 * tmp);
        }
        obj->messageLen[i] = 160;
        (void)snprintf(obj->message[i], obj->messageLen[i], "this is 中%d，zhong,中;", i);
    }

    // 创建逻辑表
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet,"
        " message blob(160), ns text)"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '5000 MB',"
        " compression = 'fast(rapidlz)');",
        g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入数据
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C4StrSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        obj->id[i] = i + count;
    }
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C4StrSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select count(*), id from %s order by id;\" -s %s",
        g_tableName, g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "id");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 创建时序逻辑表2
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet,"
        " message text, ns blob(160), ns1 text)"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '5000 MB',"
        " compression = 'fast(rapidlz)');",
        g_tableName2);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 连接2对逻辑表2插入少量数据
    for (int j = 0; j < 2; j++) {
        ret = writeRecordTs(conn, stmt, g_tableName2, obj, count, C2Int8C5StrTextSet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 连接2 alter两个逻辑表
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol inet;", g_tableName);
    ret = GmcExecDirect(stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol text;", g_tableName2);
    ret = GmcExecDirect(stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE';\" -s %s", g_connServerTsdb);
    ret = executeCommand(g_command, "TABLE_NAME: tsdb1", "CU_CNT: 2", "ROW_CNT: 58");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 连接1对逻辑表2插入数据触发落盘
    for (int i = 0; i < count; i++) {
        obj->id[i] = i + count * 2;
    }
    for (int j = 0; j < 30; j++) {
        ret = writeRecordTs(g_conn, g_stmt, g_tableName2, obj, count, C2Int8C6StrTextSet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE';\" -s %s", g_connServerTsdb);
    ret = executeCommand(g_command, "TABLE_NAME: tsdb2", "CU_CNT: 2", "ROW_CNT: 928");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 删除tsdb2
    ret = DropCmTable(g_stmt, g_tableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 断开连接
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放内存
    TestFreeTagC3Int8C4StrAlloc(obj, count);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
22.创建2个时序逻辑表（表结构不同），连接1对第一个逻辑表插入2批少量数据，连接2对第二个逻辑表插入2批少量数据，
连接2对两个逻辑表alter，连接1对逻辑表2插入数据触发落盘，连接2对逻辑表1插入少量数据触发落盘
**************************************************************************** */
TEST_F(tsdbAlterEnh_001_test, Timing_054_005_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = 0;
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /home/<USER>/");
    char tableName[1024] = {0};
    constexpr int64_t count = 29, cpCnt = 20;
    int tmp = count;
    C3Int8C4StrT *obj = NULL;
    ret = TestAllocTagC3Int8C4Str(&obj, count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char ip[cpCnt][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[cpCnt][64] = {"david", "nut中", "bob中", "olivia，", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc",
        "lucy", "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    GenerateRandomAndChinString(obj->ns, count);
    for (int i = 0; i < count; i++) {
        obj->id[i] = i;
        obj->time[i] = 1704067200 + i;
        if ((i % cpCnt == 0) && (tmp >= cpCnt)) {
            tmp -= cpCnt;
            memcpy_s(obj->ip + i * 33, 33 * cpCnt, (char *)ip, 33 * cpCnt);
            memcpy_s(obj->name + i * 64, 64 * cpCnt, (char *)name, 64 * cpCnt);
        } else if ((i % 20 == 0) && (tmp > 0)) {
            memcpy_s(obj->ip + i * 33, 33 * tmp, (char *)ip, 33 * tmp);
            memcpy_s(obj->name + i * 64, 64 * tmp, (char *)name, 64 * tmp);
        }
        obj->messageLen[i] = 160;
        (void)snprintf(obj->message[i], obj->messageLen[i], "this is 中%d，zhong,中;", i);
    }

    // 创建逻辑表
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet,"
        " message blob(160), ns text)"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '5000 MB',"
        " compression = 'fast(rapidlz)');",
        g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入数据
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C4StrSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        obj->id[i] = i + count;
    }
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C4StrSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select count(*), id from %s order by id;\" -s %s",
        g_tableName, g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "id");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 创建时序逻辑表2
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet,"
        " message text, ns blob(160), ns1 text)"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '5000 MB',"
        " compression = 'fast(rapidlz)');",
        g_tableName2);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 连接2对逻辑表2插入少量数据
    for (int j = 0; j < 2; j++) {
        ret = writeRecordTs(conn, stmt, g_tableName2, obj, count, C2Int8C5StrTextSet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 连接2 alter两个逻辑表
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol inet;", g_tableName);
    ret = GmcExecDirect(stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol text;", g_tableName2);
    ret = GmcExecDirect(stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE';\" -s %s", g_connServerTsdb);
    ret = executeCommand(g_command, "TABLE_NAME: tsdb1", "CU_CNT: 2", "ROW_CNT: 58");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 连接1对逻辑表2插入数据触发落盘和连接2对逻辑表1插入数据触发落盘
    for (int i = 0; i < count; i++) {
        obj->id[i] = i + count * 2;
    }
    for (int j = 0; j < 30; j++) {
        ret = writeRecordTs(g_conn, g_stmt, g_tableName2, obj, count, C2Int8C6StrTextSet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C2Int8C5StrTextIpSet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE';\" -s %s", g_connServerTsdb);
    ret = executeCommand(g_command, "TABLE_NAME: tsdb2", "CU_CNT: 2", "ROW_CNT: 928");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "TABLE_NAME: tsdb1", "CU_CNT: 2", "ROW_CNT: 928");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 删除tsdb2
    ret = DropCmTable(g_stmt, g_tableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 断开连接
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放内存
    TestFreeTagC3Int8C4StrAlloc(obj, count);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
23.创建,2个逻辑表（表结构不同），插入数据，新增列，truncate（表1），truncate(表1)，对表2插入数据，
查询符合预期，truncate（表2），truncate(表2)，对表1插入数据，查询符合预期
**************************************************************************** */
TEST_F(tsdbAlterEnh_001_test, Timing_054_005_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = 0;
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /home/<USER>/");
    char tableName[1024] = {0};
    constexpr int64_t count = 29, cpCnt = 20;
    int tmp = count;
    C3Int8C4StrT *obj = NULL;
    ret = TestAllocTagC3Int8C4Str(&obj, count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char ip[cpCnt][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[cpCnt][64] = {"david", "nut中", "bob中", "olivia，", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc",
        "lucy", "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    GenerateRandomAndChinString(obj->ns, count);
    for (int i = 0; i < count; i++) {
        obj->id[i] = i;
        obj->time[i] = 1704067200 + i;
        if ((i % cpCnt == 0) && (tmp >= cpCnt)) {
            tmp -= cpCnt;
            memcpy_s(obj->ip + i * 33, 33 * cpCnt, (char *)ip, 33 * cpCnt);
            memcpy_s(obj->name + i * 64, 64 * cpCnt, (char *)name, 64 * cpCnt);
        } else if ((i % 20 == 0) && (tmp > 0)) {
            memcpy_s(obj->ip + i * 33, 33 * tmp, (char *)ip, 33 * tmp);
            memcpy_s(obj->name + i * 64, 64 * tmp, (char *)name, 64 * tmp);
        }
        obj->messageLen[i] = 160;
        (void)snprintf(obj->message[i], obj->messageLen[i], "this is 中%d，zhong,中;", i);
    }

    // 创建逻辑表1
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet,"
        " message blob(160), ns text)"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '5000 MB',"
        " compression = 'fast(rapidlz)');",
        g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建时序逻辑表2
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet,"
        " message text, ns blob(160), ns1 text)"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '5000 MB',"
        " compression = 'fast(rapidlz)');",
        g_tableName2);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入数据
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C4StrSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // alter
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol inet;", g_tableName);
    ret = GmcExecDirect(stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "truncate table %s;", g_tableName);
    ret = GmcExecDirect(stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对表2插入数据
    ret = writeRecordTs(conn, stmt, g_tableName2, obj, count, C2Int8C5StrTextSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询表2数据
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select id, count(*), name from %s order by id;\" -s %s",
        g_tableName2, g_connServerTsdb);
    ret = executeCommand(g_command, "id: 0", "count(*): 29", "name: david");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "truncate table %s;", g_tableName2);
    ret = GmcExecDirect(stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 对表1插入数据
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C2Int8C5StrTextIpSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmsysview -sql \"select id, count(*), newcol, name from %s order by id;\" -s %s", g_tableName,
        g_connServerTsdb);
    ret = executeCommand(g_command, "id: 0", "count(*): 29", "newcol: 10101040");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除tsdb2
    ret = DropCmTable(g_stmt, g_tableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 断开连接
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放内存
    TestFreeTagC3Int8C4StrAlloc(obj, count);
    AW_FUN_Log(LOG_STEP, "test end.");
}
