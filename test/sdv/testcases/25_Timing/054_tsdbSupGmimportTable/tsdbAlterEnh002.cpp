/*****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2024. All rights reserved.
 Description  : GMDB 506.1.0 迭代1 alter加固测试--特性耦合测试
 Notes        :
 History      :
 Author       : luyang/l00618033
 Create       : [2025.7.16]
*****************************************************************************/
#include "gtest/gtest.h"
#include "t_rd_ts.h"
#include "tsdbSupGmimport.h"

class tsdbAlterEnh_002_test : public testing::Test {
protected:
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}

public:
    virtual void SetUp();
    virtual void TearDown();
};

void tsdbAlterEnh_002_test::SetUp()
{
    InitTsCiCfg();
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建epoll监听线程
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN();
    ret = TestTsGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void tsdbAlterEnh_002_test::TearDown()
{
    int ret = 0;
    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 断开连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_END();

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    RecoverTsCiCfg();
    system("sh $TEST_HOME/tools/stop.sh -ts");
}

class tsdbAlterEnh_002_test2 : public testing::Test {
protected:
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}

public:
    virtual void SetUp();
    virtual void TearDown();
};

void tsdbAlterEnh_002_test2::SetUp()
{
    RecoverTsCiCfg();
    system("sh $TEST_HOME/tools/stop.sh -ts");
    TsDefulatDbFileClean();
    system("sh ${TEST_HOME}/tools/modifyCfg.sh -ts \"tsLcmCheckPeriod=3\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建epoll监听线程
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN();
    ret = TestTsGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void tsdbAlterEnh_002_test2::TearDown()
{
    int ret = 0;
    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 断开连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_END();

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    RecoverTsCiCfg();
    system("sh $TEST_HOME/tools/stop.sh -ts");
}

class tsdbAlterEnh_002_test3 : public testing::Test {
protected:
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}

public:
    virtual void SetUp();
    virtual void TearDown();
};

void tsdbAlterEnh_002_test3::SetUp()
{
    RecoverTsCiCfg();
    system("sh $TEST_HOME/tools/stop.sh -ts");
    TsDefulatDbFileClean();
    system("sh ${TEST_HOME}/tools/modifyCfg.sh -ts \"tsAllowDiskClean=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建epoll监听线程
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN();
    ret = TestTsGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void tsdbAlterEnh_002_test3::TearDown()
{
    int ret = 0;
    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 断开连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_END();

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    RecoverTsCiCfg();
    system("sh $TEST_HOME/tools/stop.sh -ts");
}

/* ****************************************************************************
 Description  :
01.创建时序逻辑表，连接1prepare，绑定数据，连接2 alter加列；连接1再执行execute插入数据
**************************************************************************** */
TEST_F(tsdbAlterEnh_002_test, Timing_054_006_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = 0;
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /home/<USER>/");
    char tableName[1024] = {0};
    constexpr int64_t count = 29, cpCnt = 20;
    int tmp = count;
    C3Int8C4StrT *obj = NULL;
    ret = TestAllocTagC3Int8C4Str(&obj, count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char ip[cpCnt][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[cpCnt][64] = {"david", "nut中", "bob中", "olivia，", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc",
        "lucy", "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    GenerateRandomAndChinString(obj->ns, count);
    for (int i = 0; i < count; i++) {
        obj->id[i] = i;
        obj->time[i] = 1704067200 + i;
        if ((i % cpCnt == 0) && (tmp >= cpCnt)) {
            tmp -= cpCnt;
            memcpy_s(obj->ip + i * 33, 33 * cpCnt, (char *)ip, 33 * cpCnt);
            memcpy_s(obj->name + i * 64, 64 * cpCnt, (char *)name, 64 * cpCnt);
        } else if ((i % 20 == 0) && (tmp > 0)) {
            memcpy_s(obj->ip + i * 33, 33 * tmp, (char *)ip, 33 * tmp);
            memcpy_s(obj->name + i * 64, 64 * tmp, (char *)name, 64 * tmp);
        }
        obj->messageLen[i] = 160;
        (void)snprintf(obj->message[i], obj->messageLen[i], "this is 中%d，zhong,中;", i);
    }

    // 创建逻辑表1
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet,"
        " message blob(160), ns text)"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '5000 MB',"
        " compression = 'fast(rapidlz)');",
        g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 绑定数据
    ret = GmcPrepareStmtByLabelName(g_stmt, g_tableName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStmtAttr(g_stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, obj->id, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, obj->time, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 2, (GmcDataTypeE)DB_DATATYPE_FIXED, obj->name, 64, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 3, (GmcDataTypeE)DB_DATATYPE_FIXED, obj->ip, 33, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret =
        GmcBindCol(g_stmt, 4, (GmcDataTypeE)DB_DATATYPE_BYTES, obj->message, sizeof(obj->message[0]), obj->messageLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 5, (GmcDataTypeE)DB_DATATYPE_STRING, obj->ns, sizeof(obj->ns[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接2 alter加列
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol inet;", g_tableName);
    ret = GmcExecDirect(stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询表1数据
    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmsysview -sql \"select id, count(*), newcol, name from %s order by id;\" -s %s", g_tableName,
        g_connServerTsdb);
    ret = executeCommand(g_command, "id: 0", "count(*): 29", "newcol: 00000000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 断开连接
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放内存
    TestFreeTagC3Int8C4StrAlloc(obj, count);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
02.创建时序逻辑表，连接1prepare bind列 设置快照（GmcGetStmtView），连接1 alter 加列；连接1获取快照（GmcSetStmtView），
执行execute，查看结果数据
**************************************************************************** */
TEST_F(tsdbAlterEnh_002_test, Timing_054_006_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    GmcStmtViewT *stmtView = NULL;
    int ret = 0;
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /home/<USER>/");
    char tableName[1024] = {0};
    constexpr int64_t count = 29, cpCnt = 20;
    int tmp = count;
    C3Int8C4StrT *obj = NULL;
    ret = TestAllocTagC3Int8C4Str(&obj, count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char ip[cpCnt][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[cpCnt][64] = {"david", "nut中", "bob中", "olivia，", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc",
        "lucy", "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    GenerateRandomAndChinString(obj->ns, count);
    for (int i = 0; i < count; i++) {
        obj->id[i] = i;
        obj->time[i] = 1704067200 + i;
        if ((i % cpCnt == 0) && (tmp >= cpCnt)) {
            tmp -= cpCnt;
            memcpy_s(obj->ip + i * 33, 33 * cpCnt, (char *)ip, 33 * cpCnt);
            memcpy_s(obj->name + i * 64, 64 * cpCnt, (char *)name, 64 * cpCnt);
        } else if ((i % 20 == 0) && (tmp > 0)) {
            memcpy_s(obj->ip + i * 33, 33 * tmp, (char *)ip, 33 * tmp);
            memcpy_s(obj->name + i * 64, 64 * tmp, (char *)name, 64 * tmp);
        }
        obj->messageLen[i] = 160;
        (void)snprintf(obj->message[i], obj->messageLen[i], "this is 中%d，zhong,中;", i);
    }

    // 创建逻辑表1
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet,"
        " message blob(160), ns text)"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '5000 MB',"
        " compression = 'fast(rapidlz)');",
        g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 绑定数据
    ret = GmcPrepareStmtByLabelName(g_stmt, g_tableName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStmtAttr(g_stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, obj->id, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, obj->time, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 2, (GmcDataTypeE)DB_DATATYPE_FIXED, obj->name, 64, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 3, (GmcDataTypeE)DB_DATATYPE_FIXED, obj->ip, 33, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret =
        GmcBindCol(g_stmt, 4, (GmcDataTypeE)DB_DATATYPE_BYTES, obj->message, sizeof(obj->message[0]), obj->messageLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 5, (GmcDataTypeE)DB_DATATYPE_STRING, obj->ns, sizeof(obj->ns[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置快照
    ret = GmcGetStmtView(g_stmt, &stmtView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接1 alter加列
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol inet;", g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 获取快照
    ret = GmcSetStmtView(g_stmt, stmtView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecute(g_stmt));

    // 接口校验数据
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "select * from %s order by id;", g_tableName);
    ret = readRecordAndCompareTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询表1数据
    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmsysview -sql \"select id, count(*), newcol, name from %s order by id;\" -s %s", g_tableName,
        g_connServerTsdb);
    ret = executeCommand(g_command, "id: 0", "count(*): 29", "newcol: 00000000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 释放快照
    GmcFreeStmtView(stmtView);
    // 断开连接
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放内存
    TestFreeTagC3Int8C4StrAlloc(obj, count);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
03.创建时序逻辑表，连接1prepare bind列 设置快照（GmcGetStmtView），连接1 alter 加列；连接1获取快照（GmcSetStmtView），
再绑定新增列，执行execute，查看结果数据
**************************************************************************** */
TEST_F(tsdbAlterEnh_002_test, Timing_054_006_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    GmcStmtViewT *stmtView = NULL;
    int ret = 0;
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /home/<USER>/");
    char tableName[1024] = {0};
    constexpr int64_t count = 29, cpCnt = 20;
    int tmp = count;
    C3Int8C4StrT *obj = NULL;
    ret = TestAllocTagC3Int8C4Str(&obj, count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char ip[cpCnt][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[cpCnt][64] = {"david", "nut中", "bob中", "olivia，", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc",
        "lucy", "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    GenerateRandomAndChinString(obj->ns, count);
    for (int i = 0; i < count; i++) {
        obj->id[i] = i;
        obj->time[i] = 1704067200 + i;
        if ((i % cpCnt == 0) && (tmp >= cpCnt)) {
            tmp -= cpCnt;
            memcpy_s(obj->ip + i * 33, 33 * cpCnt, (char *)ip, 33 * cpCnt);
            memcpy_s(obj->name + i * 64, 64 * cpCnt, (char *)name, 64 * cpCnt);
        } else if ((i % 20 == 0) && (tmp > 0)) {
            memcpy_s(obj->ip + i * 33, 33 * tmp, (char *)ip, 33 * tmp);
            memcpy_s(obj->name + i * 64, 64 * tmp, (char *)name, 64 * tmp);
        }
        obj->messageLen[i] = 160;
        (void)snprintf(obj->message[i], obj->messageLen[i], "this is 中%d，zhong,中;", i);
    }

    // 创建逻辑表1
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet,"
        " message blob(160), ns text)"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '5000 MB',"
        " compression = 'fast(rapidlz)');",
        g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 绑定数据
    ret = GmcPrepareStmtByLabelName(g_stmt, g_tableName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStmtAttr(g_stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, obj->id, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, obj->time, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 2, (GmcDataTypeE)DB_DATATYPE_FIXED, obj->name, 64, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 3, (GmcDataTypeE)DB_DATATYPE_FIXED, obj->ip, 33, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret =
        GmcBindCol(g_stmt, 4, (GmcDataTypeE)DB_DATATYPE_BYTES, obj->message, sizeof(obj->message[0]), obj->messageLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 5, (GmcDataTypeE)DB_DATATYPE_STRING, obj->ns, sizeof(obj->ns[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置快照
    ret = GmcGetStmtView(g_stmt, &stmtView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接1 alter加列
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol inet;", g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 获取快照并绑定新增数据
    ret = GmcSetStmtView(g_stmt, stmtView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 6, (GmcDataTypeE)DB_DATATYPE_FIXED, obj->ip, 33, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecute(g_stmt));

    // 接口校验数据
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "select * from %s order by id;", g_tableName);
    ret = readRecordAndCompareTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询表1数据
    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmsysview -sql \"select id, count(*), newcol, name from %s order by id;\" -s %s", g_tableName,
        g_connServerTsdb);
    ret = executeCommand(g_command, "id: 0", "count(*): 29", "newcol: 10101040");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 释放快照
    GmcFreeStmtView(stmtView);
    // 断开连接
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放内存
    TestFreeTagC3Int8C4StrAlloc(obj, count);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
04.创建时序逻辑表，连接1prepare bind列 设置快照（GmcGetStmtView），连接2 alter 加列；连接1获取快照（GmcSetStmtView），
连接1再绑定新增列，执行execute，查看结果数据
**************************************************************************** */
TEST_F(tsdbAlterEnh_002_test, Timing_054_006_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    GmcStmtViewT *stmtView = NULL;
    int ret = 0;
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /home/<USER>/");
    char tableName[1024] = {0};
    constexpr int64_t count = 29, cpCnt = 20;
    int tmp = count;
    C3Int8C4StrT *obj = NULL;
    ret = TestAllocTagC3Int8C4Str(&obj, count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char ip[cpCnt][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[cpCnt][64] = {"david", "nut中", "bob中", "olivia，", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc",
        "lucy", "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    GenerateRandomAndChinString(obj->ns, count);
    for (int i = 0; i < count; i++) {
        obj->id[i] = i;
        obj->time[i] = 1704067200 + i;
        if ((i % cpCnt == 0) && (tmp >= cpCnt)) {
            tmp -= cpCnt;
            memcpy_s(obj->ip + i * 33, 33 * cpCnt, (char *)ip, 33 * cpCnt);
            memcpy_s(obj->name + i * 64, 64 * cpCnt, (char *)name, 64 * cpCnt);
        } else if ((i % 20 == 0) && (tmp > 0)) {
            memcpy_s(obj->ip + i * 33, 33 * tmp, (char *)ip, 33 * tmp);
            memcpy_s(obj->name + i * 64, 64 * tmp, (char *)name, 64 * tmp);
        }
        obj->messageLen[i] = 160;
        (void)snprintf(obj->message[i], obj->messageLen[i], "this is 中%d，zhong,中;", i);
    }

    // 创建逻辑表1
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet,"
        " message blob(160), ns text)"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '5000 MB',"
        " compression = 'fast(rapidlz)');",
        g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 绑定数据
    ret = GmcPrepareStmtByLabelName(g_stmt, g_tableName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStmtAttr(g_stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, obj->id, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, obj->time, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 2, (GmcDataTypeE)DB_DATATYPE_FIXED, obj->name, 64, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 3, (GmcDataTypeE)DB_DATATYPE_FIXED, obj->ip, 33, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret =
        GmcBindCol(g_stmt, 4, (GmcDataTypeE)DB_DATATYPE_BYTES, obj->message, sizeof(obj->message[0]), obj->messageLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 5, (GmcDataTypeE)DB_DATATYPE_STRING, obj->ns, sizeof(obj->ns[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置快照
    ret = GmcGetStmtView(g_stmt, &stmtView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接2 alter加列
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol inet;", g_tableName);
    ret = GmcExecDirect(stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 获取快照并绑定新增数据
    ret = GmcSetStmtView(g_stmt, stmtView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 6, (GmcDataTypeE)DB_DATATYPE_FIXED, obj->ip, 33, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecute(g_stmt));

    // 接口校验数据
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "select * from %s order by id;", g_tableName);
    ret = readRecordAndCompareTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询表1数据
    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmsysview -sql \"select id, count(*), newcol, name from %s order by id;\" -s %s", g_tableName,
        g_connServerTsdb);
    ret = executeCommand(g_command, "id: 0", "count(*): 29", "newcol: 10101040");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 释放快照
    GmcFreeStmtView(stmtView);
    // 断开连接
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放内存
    TestFreeTagC3Int8C4StrAlloc(obj, count);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
05.创建时序逻辑表，连接1prepare bind列 设置快照（GmcGetStmtView），连接2 alter 加列；独立重启，
连接1获取快照（GmcSetStmtView），连接1再绑定新增列，执行execute，查看结果数据
**************************************************************************** */
TEST_F(tsdbAlterEnh_002_test, Timing_054_006_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    GmcStmtViewT *stmtView = NULL;
    int ret = 0;
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /home/<USER>/");
    char tableName[1024] = {0};
    constexpr int64_t count = 29, cpCnt = 20;
    int tmp = count;
    C3Int8C4StrT *obj = NULL;
    ret = TestAllocTagC3Int8C4Str(&obj, count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char ip[cpCnt][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[cpCnt][64] = {"david", "nut中", "bob中", "olivia，", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc",
        "lucy", "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    GenerateRandomAndChinString(obj->ns, count);
    for (int i = 0; i < count; i++) {
        obj->id[i] = i;
        obj->time[i] = 1704067200 + i;
        if ((i % cpCnt == 0) && (tmp >= cpCnt)) {
            tmp -= cpCnt;
            memcpy_s(obj->ip + i * 33, 33 * cpCnt, (char *)ip, 33 * cpCnt);
            memcpy_s(obj->name + i * 64, 64 * cpCnt, (char *)name, 64 * cpCnt);
        } else if ((i % 20 == 0) && (tmp > 0)) {
            memcpy_s(obj->ip + i * 33, 33 * tmp, (char *)ip, 33 * tmp);
            memcpy_s(obj->name + i * 64, 64 * tmp, (char *)name, 64 * tmp);
        }
        obj->messageLen[i] = 160;
        (void)snprintf(obj->message[i], obj->messageLen[i], "this is 中%d，zhong,中;", i);
    }

    // 创建逻辑表1
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet,"
        " message blob(160), ns text)"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '5000 MB',"
        " compression = 'fast(rapidlz)');",
        g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 绑定数据
    ret = GmcPrepareStmtByLabelName(g_stmt, g_tableName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStmtAttr(g_stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, obj->id, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, obj->time, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 2, (GmcDataTypeE)DB_DATATYPE_FIXED, obj->name, 64, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 3, (GmcDataTypeE)DB_DATATYPE_FIXED, obj->ip, 33, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret =
        GmcBindCol(g_stmt, 4, (GmcDataTypeE)DB_DATATYPE_BYTES, obj->message, sizeof(obj->message[0]), obj->messageLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 5, (GmcDataTypeE)DB_DATATYPE_STRING, obj->ns, sizeof(obj->ns[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置快照
    ret = GmcGetStmtView(g_stmt, &stmtView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接2 alter加列
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol inet;", g_tableName);
    ret = GmcExecDirect(stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 独立重启
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    // 重新建立同步连接
    ret = TestTsGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 获取快照并绑定新增数据
    ret = GmcSetStmtView(g_stmt, stmtView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 6, (GmcDataTypeE)DB_DATATYPE_FIXED, obj->ip, 33, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecute(g_stmt));

    // 接口校验数据
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "select * from %s order by id;", g_tableName);
    ret = readRecordAndCompareTs(g_stmt, g_tableName, obj, count, (char *)g_sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询表1数据
    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmsysview -sql \"select id, count(*), newcol, name from %s order by id;\" -s %s", g_tableName,
        g_connServerTsdb);
    ret = executeCommand(g_command, "id: 0", "count(*): 29", "newcol: 10101040");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 释放快照
    GmcFreeStmtView(stmtView);
    // 断开连接
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放内存
    TestFreeTagC3Int8C4StrAlloc(obj, count);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
06.创建时序逻辑表，插入连续29次每批29条数据，连接1prepare bind列 设置快照（GmcGetStmtView），连接2 alter 加列；
独立重启，连接1获取快照（GmcSetStmtView），连接1再绑定新增列，执行execute，查看结果数据不会触发磁盘整理，查询时序相关系统视图
**************************************************************************** */
TEST_F(tsdbAlterEnh_002_test, Timing_054_006_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    GmcStmtViewT *stmtView = NULL;
    int ret = 0;
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /home/<USER>/");
    char tableName[1024] = {0};
    constexpr int64_t count = 29, cpCnt = 20;
    int tmp = count;
    C3Int8C4StrT *obj = NULL;
    ret = TestAllocTagC3Int8C4Str(&obj, count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char ip[cpCnt][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[cpCnt][64] = {"david", "nut中", "bob中", "olivia，", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc",
        "lucy", "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    GenerateRandomAndChinString(obj->ns, count);
    for (int i = 0; i < count; i++) {
        obj->id[i] = i;
        obj->time[i] = 1704067200 + i;
        if ((i % cpCnt == 0) && (tmp >= cpCnt)) {
            tmp -= cpCnt;
            memcpy_s(obj->ip + i * 33, 33 * cpCnt, (char *)ip, 33 * cpCnt);
            memcpy_s(obj->name + i * 64, 64 * cpCnt, (char *)name, 64 * cpCnt);
        } else if ((i % 20 == 0) && (tmp > 0)) {
            memcpy_s(obj->ip + i * 33, 33 * tmp, (char *)ip, 33 * tmp);
            memcpy_s(obj->name + i * 64, 64 * tmp, (char *)name, 64 * tmp);
        }
        obj->messageLen[i] = 160;
        (void)snprintf(obj->message[i], obj->messageLen[i], "this is 中%d，zhong,中;", i);
    }

    // 创建逻辑表1
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet,"
        " message blob(160), ns text)"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '5000 MB',"
        " compression = 'fast(rapidlz)');",
        g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入29次每批29条数据
    for (int j = 0; j < 29; j++) {
        ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C4StrSet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 绑定数据
    ret = GmcPrepareStmtByLabelName(g_stmt, g_tableName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStmtAttr(g_stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, obj->id, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, obj->time, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 2, (GmcDataTypeE)DB_DATATYPE_FIXED, obj->name, 64, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 3, (GmcDataTypeE)DB_DATATYPE_FIXED, obj->ip, 33, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret =
        GmcBindCol(g_stmt, 4, (GmcDataTypeE)DB_DATATYPE_BYTES, obj->message, sizeof(obj->message[0]), obj->messageLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 5, (GmcDataTypeE)DB_DATATYPE_STRING, obj->ns, sizeof(obj->ns[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置快照
    ret = GmcGetStmtView(g_stmt, &stmtView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接2 alter加列
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol inet;", g_tableName);
    ret = GmcExecDirect(stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 独立重启
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    // 重新建立同步连接
    ret = TestTsGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE';\" -s %s", g_connServerTsdb);
    ret = executeCommand(g_command, "TABLE_NAME: tsdb1", "CU_CNT: 29", "ROW_CNT: 841");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 获取快照并绑定新增数据
    ret = GmcSetStmtView(g_stmt, stmtView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 6, (GmcDataTypeE)DB_DATATYPE_FIXED, obj->ip, 33, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecute(g_stmt));

    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE';\" -s %s", g_connServerTsdb);
    ret = executeCommand(g_command, "TABLE_NAME: tsdb1", "CU_CNT: 30", "ROW_CNT: 870");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 查询表1数据
    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmsysview -sql \"select id, count(*), newcol, name from %s order by id;\" -s %s", g_tableName,
        g_connServerTsdb);
    ret = executeCommand(g_command, "id: 0", "count(*): 870");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 释放快照
    GmcFreeStmtView(stmtView);
    // 断开连接
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放内存
    TestFreeTagC3Int8C4StrAlloc(obj, count);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
07.创建时序逻辑表，连接1插入连续29次每批29条数据，连接2alter新增列，连接1prepare bind列（含新增列）
设置快照（GmcGetStmtView）连接1alter新增列；独立重启，连接1获取快照（GmcSetStmtView），连接1再绑定新增列，执行execute，插入多批少量数据，触发磁盘整理，查看结果数据
**************************************************************************** */
TEST_F(tsdbAlterEnh_002_test, Timing_054_006_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    GmcStmtViewT *stmtView = NULL;
    int ret = 0;
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /home/<USER>/");
    char tableName[1024] = {0};
    constexpr int64_t count = 29, cpCnt = 20;
    int tmp = count;
    C3Int8C4StrT *obj = NULL;
    ret = TestAllocTagC3Int8C4Str(&obj, count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char ip[cpCnt][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[cpCnt][64] = {"david", "nut中", "bob中", "olivia，", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc",
        "lucy", "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    GenerateRandomAndChinString(obj->ns, count);
    for (int i = 0; i < count; i++) {
        obj->id[i] = i;
        obj->time[i] = 1704067200 + i;
        if ((i % cpCnt == 0) && (tmp >= cpCnt)) {
            tmp -= cpCnt;
            memcpy_s(obj->ip + i * 33, 33 * cpCnt, (char *)ip, 33 * cpCnt);
            memcpy_s(obj->name + i * 64, 64 * cpCnt, (char *)name, 64 * cpCnt);
        } else if ((i % 20 == 0) && (tmp > 0)) {
            memcpy_s(obj->ip + i * 33, 33 * tmp, (char *)ip, 33 * tmp);
            memcpy_s(obj->name + i * 64, 64 * tmp, (char *)name, 64 * tmp);
        }
        obj->messageLen[i] = 160;
        (void)snprintf(obj->message[i], obj->messageLen[i], "this is 中%d，zhong,中;", i);
    }

    // 创建逻辑表1
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet,"
        " message blob(160), ns text)"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '5000 MB',"
        " compression = 'fast(rapidlz)');",
        g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入29次每批29条数据
    for (int j = 0; j < 29; j++) {
        ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C4StrSet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 连接2 alter加列
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol inet;", g_tableName);
    ret = GmcExecDirect(stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 绑定数据
    ret = GmcPrepareStmtByLabelName(g_stmt, g_tableName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStmtAttr(g_stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, obj->id, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, obj->time, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 2, (GmcDataTypeE)DB_DATATYPE_FIXED, obj->name, 64, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 3, (GmcDataTypeE)DB_DATATYPE_FIXED, obj->ip, 33, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret =
        GmcBindCol(g_stmt, 4, (GmcDataTypeE)DB_DATATYPE_BYTES, obj->message, sizeof(obj->message[0]), obj->messageLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 5, (GmcDataTypeE)DB_DATATYPE_STRING, obj->ns, sizeof(obj->ns[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 6, (GmcDataTypeE)DB_DATATYPE_FIXED, obj->ip, 33, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置快照
    ret = GmcGetStmtView(g_stmt, &stmtView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 连接1 alter加列
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol2 text;", g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 独立重启
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    // 重新建立同步连接
    ret = TestTsGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE';\" -s %s", g_connServerTsdb);
    ret = executeCommand(g_command, "TABLE_NAME: tsdb1", "CU_CNT: 29", "ROW_CNT: 841");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 获取快照并绑定新增数据
    ret = GmcSetStmtView(g_stmt, stmtView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 7, (GmcDataTypeE)DB_DATATYPE_STRING, obj->ns, sizeof(obj->ns[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecute(g_stmt));

    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE';\" -s %s", g_connServerTsdb);
    ret = executeCommand(g_command, "TABLE_NAME: tsdb1", "CU_CNT: 30", "ROW_CNT: 870");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 再次连续插入29次每批29条数据，触发磁盘整理
    for (int j = 0; j < 29; j++) {
        for (int i = 0; i < count; i++) {
            obj->id[i] = i + j * 29;
        }
        ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C2Int8C6StrIpTextSet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE';\" -s %s", g_connServerTsdb);
    ret = executeCommand(g_command, "TABLE_NAME: tsdb1", "CU_CNT: 30", "ROW_CNT: 1711");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 查询表1数据
    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmsysview -sql \"select id, count(*), newcol, name from %s order by id;\" -s %s", g_tableName,
        g_connServerTsdb);
    ret = executeCommand(g_command, "id: 0", "count(*): 1711");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 释放快照
    GmcFreeStmtView(stmtView);
    // 断开连接
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放内存
    TestFreeTagC3Int8C4StrAlloc(obj, count);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
08.alter加列，alter TTL，alter disklimit，功能正常，独立重启，查时序系统视图和catalog视图，插入数据，校验数据
**************************************************************************** */
TEST_F(tsdbAlterEnh_002_test2, Timing_054_006_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 仿真环境屏蔽错误码1016001错误码
#ifdef ENV_RTOSV2
    AddWhiteList(GMERR_CONNECTION_RESET_BY_PEER);
#endif
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    GmcStmtViewT *stmtView = NULL;
    int ret = 0;
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /home/<USER>/");
    char tableName[1024] = {0};
    constexpr int64_t count = 29, cpCnt = 20;
    int tmp = count;
    C3Int8C4StrT *obj = NULL;
    ret = TestAllocTagC3Int8C4Str(&obj, count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char ip[cpCnt][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[cpCnt][64] = {"david", "nut中", "bob中", "olivia，", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc",
        "lucy", "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    GenerateRandomAndChinString(obj->ns, count);
    for (int i = 0; i < count; i++) {
        obj->id[i] = i;
        obj->time[i] = 1704067200 + i;
        if ((i % cpCnt == 0) && (tmp >= cpCnt)) {
            tmp -= cpCnt;
            memcpy_s(obj->ip + i * 33, 33 * cpCnt, (char *)ip, 33 * cpCnt);
            memcpy_s(obj->name + i * 64, 64 * cpCnt, (char *)name, 64 * cpCnt);
        } else if ((i % 20 == 0) && (tmp > 0)) {
            memcpy_s(obj->ip + i * 33, 33 * tmp, (char *)ip, 33 * tmp);
            memcpy_s(obj->name + i * 64, 64 * tmp, (char *)name, 64 * tmp);
        }
        obj->messageLen[i] = 160;
        (void)snprintf(obj->message[i], obj->messageLen[i], "this is 中%d，zhong,中;", i);
    }

    // 创建逻辑表1
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet,"
        " message blob(160), ns text)"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '5000 MB',"
        " compression = 'fast(rapidlz)');",
        g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入29次每批29条数据
    for (int j = 0; j < 29; j++) {
        ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C4StrSet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 连接2 alter加列
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol inet;", g_tableName);
    ret = GmcExecDirect(stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置ttl让数据全部老化干净
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s SET (ttl = '1 hour');", g_tableName);
    ret = GmcExecDirect(stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置diskmit
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s SET (disk_limit = '20 MB');", g_tableName);
    ret = GmcExecDirect(stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sleep(5);

    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmsysview -sql \"select id, count(*), newcol, name from %s order by id;\" -s %s", g_tableName,
        g_connServerTsdb);
    ret = executeCommand(g_command, "id: ", "count(*): 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE';\" -s %s", g_connServerTsdb);
    ret = executeCommand(g_command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE';\" -s %s", g_connServerTsdb);
    ret = executeCommand(g_command, "COL_DISK_USAGE: 0", "ROW_CNT: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmsysview -sql \"select * from 'V\\$TS_TBL_PROPS';\" -s %s", g_connServerTsdb);
    ret = executeCommand(g_command, "TTL: 1", "TTL_UNIT: hour", "DISK_LIMIT: 20971520");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmsysview -sql \"select * from 'V\\$TS_TBL_OPER_STATIS';\" -s %s", g_connServerTsdb);
    ret = executeCommand(g_command, "DML_SUCCESS_CNT: 29");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 独立重启
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    // 重新建立同步连接
    ret = TestTsGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int j = 0; j < 29; j++) {
        ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C4StrSet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 等待数据老化干净
    sleep(5);
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE';\" -s %s", g_connServerTsdb);
    ret = executeCommand(g_command, "COL_DISK_USAGE: 0", "ROW_CNT: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 查询时序系统视图及catalog视图
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmsysview -sql \"select * from 'V\\$TS_TBL_PROPS';\" -s %s", g_connServerTsdb);
    ret = executeCommand(g_command, "TTL: 1", "TTL_UNIT: hour", "DISK_LIMIT: 20971520");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmsysview -sql \"select * from 'V\\$TS_TBL_OPER_STATIS';\" -s %s", g_connServerTsdb);
    ret = executeCommand(g_command, "DML_SUCCESS_CNT: 29");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmsysview -sql \"select * from 'V\\$CATA_VERTEX_LABEL_INFO' where "
        "VERTEX_LABEL_NAME = '%s';\" -s %s",
        g_tableName, g_connServerTsdb);
    ret = executeCommand(g_command, "VERTEX_LABEL_NAME: tsdb1", "\"name\": \"newcol\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 修改逻辑表属性
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s SET (ttl = '0');", g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置diskmit
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s SET (disk_limit = '5000 MB');", g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmsysview -sql \"select * from 'V\\$TS_TBL_PROPS';\" -s %s", g_connServerTsdb);
    ret = executeCommand(g_command, "TTL: 0", "TTL_UNIT: ", "DISK_LIMIT: 5242880000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    for (int j = 0; j < 29; j++) {
        ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C4StrSet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    sleep(5);
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE';\" -s %s", g_connServerTsdb);
    ret = executeCommand(g_command, "PHY_TBL_NUM: 1", "ROW_CNT: 841");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 断开连接
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放内存
    TestFreeTagC3Int8C4StrAlloc(obj, count);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
09.tsAllowDiskClean为0，alter加列触发disklimit，观测结果
**************************************************************************** */
TEST_F(tsdbAlterEnh_002_test3, Timing_054_006_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    GmcStmtViewT *stmtView = NULL;
    int ret = 0, beforedisk = 0, afterdisk = 0;
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /home/<USER>/");
    char tableName[1024] = {0};
    constexpr int64_t count = 29, cpCnt = 20;
    int tmp = count;
    C3Int8C4StrT *obj = NULL;
    ret = TestAllocTagC3Int8C4Str(&obj, count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char ip[cpCnt][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[cpCnt][64] = {"david", "nut中", "bob中", "olivia，", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc",
        "lucy", "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    GenerateRandomAndChinString(obj->ns, count);
    for (int i = 0; i < count; i++) {
        obj->id[i] = i;
        obj->time[i] = 1704067200 + i;
        if ((i % cpCnt == 0) && (tmp >= cpCnt)) {
            tmp -= cpCnt;
            memcpy_s(obj->ip + i * 33, 33 * cpCnt, (char *)ip, 33 * cpCnt);
            memcpy_s(obj->name + i * 64, 64 * cpCnt, (char *)name, 64 * cpCnt);
        } else if ((i % 20 == 0) && (tmp > 0)) {
            memcpy_s(obj->ip + i * 33, 33 * tmp, (char *)ip, 33 * tmp);
            memcpy_s(obj->name + i * 64, 64 * tmp, (char *)name, 64 * tmp);
        }
        obj->messageLen[i] = 160;
        (void)snprintf(obj->message[i], obj->messageLen[i], "this is 中%d，zhong,中;", i);
    }

    // 创建逻辑表1
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet,"
        " message blob(160), ns text)"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '40 MB',"
        " compression = 'no');",
        g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入29次每批29条数据
    for (int j = 0; j < 29; j++) {
        ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C4StrSet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE';\" -s %s", g_connServerTsdb);
    ret = executeCommand(g_command, "PHY_TBL_NUM: 1", "ROW_CNT: 841");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    TestGetDiskUsageVal(&beforedisk, g_tableName, (char *)"DISK_USAGE");

    // 连接2 alter加列
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol text;", g_tableName);
    ret = GmcExecDirect(stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol2 inet;", g_tableName);
    ret = GmcExecDirect(stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 两者无差异
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE';\" -s %s", g_connServerTsdb);
    ret = executeCommand(g_command, "PHY_TBL_NUM: 1", "ROW_CNT: 841");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    TestGetDiskUsageVal(&afterdisk, g_tableName, (char *)"DISK_USAGE");
    AW_FUN_Log(LOG_INFO, "beforedisk is %d, afterdisk is %d", beforedisk, afterdisk);
    AW_MACRO_EXPECT_EQ_INT(beforedisk, afterdisk);

    // 断开连接
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放内存
    TestFreeTagC3Int8C4StrAlloc(obj, count);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
10.tsAllowDiskClean为1，alter加列触发disklimit，观测结果
**************************************************************************** */
TEST_F(tsdbAlterEnh_002_test, Timing_054_006_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    GmcStmtViewT *stmtView = NULL;
    int ret = 0, beforedisk = 0, afterdisk = 0;
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /home/<USER>/");
    char tableName[1024] = {0};
    constexpr int64_t count = 29, cpCnt = 20;
    int tmp = count;
    C3Int8C4StrT *obj = NULL;
    ret = TestAllocTagC3Int8C4Str(&obj, count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char ip[cpCnt][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[cpCnt][64] = {"david", "nut中", "bob中", "olivia，", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc",
        "lucy", "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    GenerateRandomAndChinString(obj->ns, count);
    for (int i = 0; i < count; i++) {
        obj->id[i] = i;
        obj->time[i] = 1704067200 + i;
        if ((i % cpCnt == 0) && (tmp >= cpCnt)) {
            tmp -= cpCnt;
            memcpy_s(obj->ip + i * 33, 33 * cpCnt, (char *)ip, 33 * cpCnt);
            memcpy_s(obj->name + i * 64, 64 * cpCnt, (char *)name, 64 * cpCnt);
        } else if ((i % 20 == 0) && (tmp > 0)) {
            memcpy_s(obj->ip + i * 33, 33 * tmp, (char *)ip, 33 * tmp);
            memcpy_s(obj->name + i * 64, 64 * tmp, (char *)name, 64 * tmp);
        }
        obj->messageLen[i] = 160;
        (void)snprintf(obj->message[i], obj->messageLen[i], "this is 中%d，zhong,中;", i);
    }

    // 创建逻辑表1
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet,"
        " message blob(160), ns text)"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '40 MB',"
        " compression = 'no');",
        g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入29次每批29条数据
    for (int j = 0; j < 29; j++) {
        ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C4StrSet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE';\" -s %s", g_connServerTsdb);
    ret = executeCommand(g_command, "PHY_TBL_NUM: 1", "ROW_CNT: 841");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    TestGetDiskUsageVal(&beforedisk, g_tableName, (char *)"DISK_USAGE");

    // 连接2 alter加列
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol text;", g_tableName);
    ret = GmcExecDirect(stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol2 inet(100000);", g_tableName);
    ret = GmcExecDirect(stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol3 blob(65535);", g_tableName);
    ret = GmcExecDirect(stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 两者无差异
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE';\" -s %s", g_connServerTsdb);
    ret = executeCommand(g_command, "PHY_TBL_NUM: 1", "ROW_CNT: 841");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    TestGetDiskUsageVal(&afterdisk, g_tableName, (char *)"DISK_USAGE");
    AW_FUN_Log(LOG_INFO, "beforedisk is %d, afterdisk is %d", beforedisk, afterdisk);
    AW_MACRO_EXPECT_EQ_INT(beforedisk, afterdisk);

    // 断开连接
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放内存
    TestFreeTagC3Int8C4StrAlloc(obj, count);
    AW_FUN_Log(LOG_STEP, "test end.");
}
