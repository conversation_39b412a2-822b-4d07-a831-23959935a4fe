/*****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2024. All rights reserved.
 Description  : GMDB 506.1.0 迭代1 alter加固测试--可靠性测试
 Notes        :
 History      :
 Author       : luyang/l00618033
 Create       : [2025.7.17]
*****************************************************************************/
#include "gtest/gtest.h"
#include "t_rd_ts.h"
#include "tsdbSupGmimport.h"

class tsdbAlterEnh_003_test : public testing::Test {
protected:
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}

public:
    virtual void SetUp();
    virtual void TearDown();
};

void tsdbAlterEnh_003_test::SetUp()
{
    InitTsCiCfg();
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建epoll监听线程
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN();
    ret = TestTsGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void tsdbAlterEnh_003_test::TearDown()
{
    int ret = 0;
    // 断开连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_END();

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    RecoverTsCiCfg();
    system("sh $TEST_HOME/tools/stop.sh -ts");
}

class tsdbAlterEnh_003_test2 : public testing::Test {
protected:
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}

public:
    virtual void SetUp();
    virtual void TearDown();
};

void tsdbAlterEnh_003_test2::SetUp()
{
    RecoverTsCiCfg();
    system("sh $TEST_HOME/tools/stop.sh -ts");
    TsDefulatDbFileClean();
    system("sh ${TEST_HOME}/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    (void)sprintf(g_dataFilePath1, "%s/data/gmdb1", g_filePath);
    (void)sprintf(g_dataFilePath, "%s", TABLE_PATH);
    system("rm -rf ./data/gmdb1");
    system("mkdir -p ./data/gmdb1");
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建epoll监听线程
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN();
    ret = TestTsGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void tsdbAlterEnh_003_test2::TearDown()
{
    int ret = 0;
    // 断开连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_END();

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("rm -rf ./data/gmdb1");
    RecoverTsCiCfg();
    system("sh $TEST_HOME/tools/stop.sh -ts");
}

/* ****************************************************************************
 Description  :
01.创建一个逻辑表含table_path和易失性路径，插入数据，alter新增列，删除table_path路径，独立重启，
查看表结构是最新表结构，插入数据，查询符合预期
**************************************************************************** */
TEST_F(tsdbAlterEnh_003_test, Timing_054_007_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = 0;
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /home/<USER>/");
    char tableName[1024] = {0};
    constexpr int64_t count = 29, cpCnt = 20;
    int tmp = count;
    C3Int8C4StrT *obj = NULL;
    ret = TestAllocTagC3Int8C4Str(&obj, count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char ip[cpCnt][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[cpCnt][64] = {"david", "nut中", "bob中", "olivia，", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc",
        "lucy", "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    GenerateRandomAndChinString(obj->ns, count);
    for (int i = 0; i < count; i++) {
        obj->id[i] = i;
        obj->time[i] = 1704067200 + i;
        if ((i % cpCnt == 0) && (tmp >= cpCnt)) {
            tmp -= cpCnt;
            memcpy_s(obj->ip + i * 33, 33 * cpCnt, (char *)ip, 33 * cpCnt);
            memcpy_s(obj->name + i * 64, 64 * cpCnt, (char *)name, 64 * cpCnt);
        } else if ((i % 20 == 0) && (tmp > 0)) {
            memcpy_s(obj->ip + i * 33, 33 * tmp, (char *)ip, 33 * tmp);
            memcpy_s(obj->name + i * 64, 64 * tmp, (char *)name, 64 * tmp);
        }
        obj->messageLen[i] = 160;
        (void)snprintf(obj->message[i], obj->messageLen[i], "this is 中%d，zhong,中;", i);
    }

    // 创建逻辑表1
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet,"
        " message blob(160), ns text)"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '5000 MB',"
        " compression = 'fast(rapidlz)', table_path = '/home/<USER>/', is_volatile_label = 'true');",
        g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C4StrSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 加列
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol inet;", g_tableName);
    ret = GmcExecDirect(stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询表1数据
    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmsysview -sql \"select id, count(*), newcol, name from %s order by id;\" -s %s", g_tableName,
        g_connServerTsdb);
    ret = executeCommand(g_command, "id: 0", "count(*): 29", "newcol: 00000000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除路径
    system("rm -rf /home/<USER>/");

    // 独立重启
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    // 重新建立同步连接
    ret = TestTsGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmsysview -sql \"select * from 'V\\$CATA_VERTEX_LABEL_INFO' where "
        "VERTEX_LABEL_NAME = '%s';\" -s %s",
        g_tableName, g_connServerTsdb);
    ret = executeCommand(g_command, "VERTEX_LABEL_NAME: tsdb1", "\"name\": \"newcol\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C2Int8C5StrTextIpSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmsysview -sql \"select id, count(*), newcol, name from %s order by id;\" -s %s", g_tableName,
        g_connServerTsdb);
    ret = executeCommand(g_command, "id: 0", "count(*): 29", "newcol: 10101040");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 断开连接
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放内存
    TestFreeTagC3Int8C4StrAlloc(obj, count);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
02.创建一个逻辑表含table_path,插入数据，新增列，目录切换到B，再切换到原路径，表结构是含新增列，插入数据，查询符合预期
**************************************************************************** */
TEST_F(tsdbAlterEnh_003_test2, Timing_054_007_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = 0;
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /home/<USER>/");
    char tableName[1024] = {0};
    constexpr int64_t count = 29, cpCnt = 20;
    int tmp = count;
    C3Int8C4StrT *obj = NULL;
    ret = TestAllocTagC3Int8C4Str(&obj, count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char ip[cpCnt][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[cpCnt][64] = {"david", "nut中", "bob中", "olivia，", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc",
        "lucy", "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    GenerateRandomAndChinString(obj->ns, count);
    for (int i = 0; i < count; i++) {
        obj->id[i] = i;
        obj->time[i] = 1704067200 + i;
        if ((i % cpCnt == 0) && (tmp >= cpCnt)) {
            tmp -= cpCnt;
            memcpy_s(obj->ip + i * 33, 33 * cpCnt, (char *)ip, 33 * cpCnt);
            memcpy_s(obj->name + i * 64, 64 * cpCnt, (char *)name, 64 * cpCnt);
        } else if ((i % 20 == 0) && (tmp > 0)) {
            memcpy_s(obj->ip + i * 33, 33 * tmp, (char *)ip, 33 * tmp);
            memcpy_s(obj->name + i * 64, 64 * tmp, (char *)name, 64 * tmp);
        }
        obj->messageLen[i] = 160;
        (void)snprintf(obj->message[i], obj->messageLen[i], "this is 中%d，zhong,中;", i);
    }

    // 创建逻辑表1
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet,"
        " message blob(160), ns text)"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '5000 MB',"
        " compression = 'fast(rapidlz)', table_path = '/home/<USER>/', is_volatile_label = 'true');",
        g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C4StrSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 加列
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol inet;", g_tableName);
    ret = GmcExecDirect(stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询表1数据
    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmsysview -sql \"select id, count(*), newcol, name from %s order by id;\" -s %s", g_tableName,
        g_connServerTsdb);
    ret = executeCommand(g_command, "id: 0", "count(*): 29", "newcol: 00000000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 目录切换到B
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 切换到原路径
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmsysview -sql \"select * from 'V\\$CATA_VERTEX_LABEL_INFO' where "
        "VERTEX_LABEL_NAME = '%s';\" -s %s",
        g_tableName, g_connServerTsdb);
    ret = executeCommand(g_command, "VERTEX_LABEL_NAME: tsdb1", "\"name\": \"newcol\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C2Int8C5StrTextIpSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmsysview -sql \"select id, count(*), newcol, name from %s order by id;\" -s %s", g_tableName,
        g_connServerTsdb);
    ret = executeCommand(g_command, "id: 0", "count(*): 58", "newcol: 00000000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 断开连接
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放内存
    TestFreeTagC3Int8C4StrAlloc(obj, count);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
03.创建一个逻辑表含table_path路径和易失性路径，插入数据，alter新增列，目录切换到B，删除table_path所在路径，
再切换到原路径，查看表结构是最新表结构，插入数据，查询符合预期
**************************************************************************** */
TEST_F(tsdbAlterEnh_003_test2, Timing_054_007_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = 0;
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /home/<USER>/");
    char tableName[1024] = {0};
    constexpr int64_t count = 29, cpCnt = 20;
    int tmp = count;
    C3Int8C4StrT *obj = NULL;
    ret = TestAllocTagC3Int8C4Str(&obj, count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char ip[cpCnt][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[cpCnt][64] = {"david", "nut中", "bob中", "olivia，", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc",
        "lucy", "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    GenerateRandomAndChinString(obj->ns, count);
    for (int i = 0; i < count; i++) {
        obj->id[i] = i;
        obj->time[i] = 1704067200 + i;
        if ((i % cpCnt == 0) && (tmp >= cpCnt)) {
            tmp -= cpCnt;
            memcpy_s(obj->ip + i * 33, 33 * cpCnt, (char *)ip, 33 * cpCnt);
            memcpy_s(obj->name + i * 64, 64 * cpCnt, (char *)name, 64 * cpCnt);
        } else if ((i % 20 == 0) && (tmp > 0)) {
            memcpy_s(obj->ip + i * 33, 33 * tmp, (char *)ip, 33 * tmp);
            memcpy_s(obj->name + i * 64, 64 * tmp, (char *)name, 64 * tmp);
        }
        obj->messageLen[i] = 160;
        (void)snprintf(obj->message[i], obj->messageLen[i], "this is 中%d，zhong,中;", i);
    }

    // 创建逻辑表1
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet,"
        " message blob(160), ns text)"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '5000 MB',"
        " compression = 'fast(rapidlz)', table_path = '/home/<USER>/', is_volatile_label = 'true');",
        g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C4StrSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 加列
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol inet;", g_tableName);
    ret = GmcExecDirect(stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询表1数据
    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmsysview -sql \"select id, count(*), newcol, name from %s order by id;\" -s %s", g_tableName,
        g_connServerTsdb);
    ret = executeCommand(g_command, "id: 0", "count(*): 29", "newcol: 00000000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 目录切换到B
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删除table_path路径
    system("rm -rf /home/<USER>/");
    // 切换到原路径
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmsysview -sql \"select * from 'V\\$CATA_VERTEX_LABEL_INFO' where "
        "VERTEX_LABEL_NAME = '%s';\" -s %s",
        g_tableName, g_connServerTsdb);
    ret = executeCommand(g_command, "VERTEX_LABEL_NAME: tsdb1", "\"name\": \"newcol\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C2Int8C5StrTextIpSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmsysview -sql \"select id, count(*), newcol, name from %s order by id;\" -s %s", g_tableName,
        g_connServerTsdb);
    ret = executeCommand(g_command, "id: 0", "count(*): 29", "newcol: 10101040");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 断开连接
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放内存
    TestFreeTagC3Int8C4StrAlloc(obj, count);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
04.创建一个逻辑表,插入29次每批29条数据，新增列，目录切换到B，创建一个逻辑表（表结构和原先不同），
插入29次每批29条数据，新增列，再插入30次少量数据，触发磁盘整理；再切换到原路径，再插入30条数据，触发磁盘整理，30个cu
**************************************************************************** */
TEST_F(tsdbAlterEnh_003_test2, Timing_054_007_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = 0;
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /home/<USER>/");
    char tableName[1024] = {0};
    constexpr int64_t count = 29, cpCnt = 20;
    int tmp = count;
    C3Int8C4StrT *obj = NULL;
    ret = TestAllocTagC3Int8C4Str(&obj, count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char ip[cpCnt][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[cpCnt][64] = {"david", "nut中", "bob中", "olivia，", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc",
        "lucy", "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    GenerateRandomAndChinString(obj->ns, count);
    for (int i = 0; i < count; i++) {
        obj->id[i] = i;
        obj->time[i] = 1704067200 + i;
        if ((i % cpCnt == 0) && (tmp >= cpCnt)) {
            tmp -= cpCnt;
            memcpy_s(obj->ip + i * 33, 33 * cpCnt, (char *)ip, 33 * cpCnt);
            memcpy_s(obj->name + i * 64, 64 * cpCnt, (char *)name, 64 * cpCnt);
        } else if ((i % 20 == 0) && (tmp > 0)) {
            memcpy_s(obj->ip + i * 33, 33 * tmp, (char *)ip, 33 * tmp);
            memcpy_s(obj->name + i * 64, 64 * tmp, (char *)name, 64 * tmp);
        }
        obj->messageLen[i] = 160;
        (void)snprintf(obj->message[i], obj->messageLen[i], "this is 中%d，zhong,中;", i);
    }

    // 创建逻辑表1
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet,"
        " message blob(160), ns text)"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '5000 MB',"
        " compression = 'fast(rapidlz)', table_path = '/home/<USER>/', is_volatile_label = 'true');",
        g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 29; i++) {
        ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C4StrSet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 加列
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol inet;", g_tableName);
    ret = GmcExecDirect(stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询表1数据
    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmsysview -sql \"select id, count(*), newcol, name from %s order by id;\" -s %s", g_tableName,
        g_connServerTsdb);
    ret = executeCommand(g_command, "id: 0", "count(*): 841", "newcol: 00000000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 目录切换到B
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建逻辑表2
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet,"
        " message text, ns blob(160), ns1 text)"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '5000 MB',"
        " compression = 'fast(rapidlz)');",
        g_tableName2);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 连接2对逻辑表2插入少量数据
    for (int j = 0; j < 29; j++) {
        ret = writeRecordTs(conn, stmt, g_tableName2, obj, count, C2Int8C5StrTextSet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol text;", g_tableName2);
    ret = GmcExecDirect(stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE';\" -s %s", g_connServerTsdb);
    ret = executeCommand(g_command, "TABLE_NAME: tsdb2", "CU_CNT: 29", "ROW_CNT: 841");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    for (int i = 0; i < count; i++) {
        obj->id[i] = i + count * 2;
    }
    for (int j = 0; j < 30; j++) {
        ret = writeRecordTs(g_conn, g_stmt, g_tableName2, obj, count, C2Int8C6StrTextSet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE';\" -s %s", g_connServerTsdb);
    ret = executeCommand(g_command, "TABLE_NAME: tsdb2", "CU_CNT: 2", "ROW_CNT: 1711");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 切换到原路径
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmsysview -sql \"select * from 'V\\$CATA_VERTEX_LABEL_INFO' where "
        "VERTEX_LABEL_NAME = '%s';\" -s %s",
        g_tableName, g_connServerTsdb);
    ret = executeCommand(g_command, "VERTEX_LABEL_NAME: tsdb1", "\"name\": \"newcol\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    for (int i = 0; i < 29; i++) {
        ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C2Int8C5StrTextIpSet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE';\" -s %s", g_connServerTsdb);
    ret = executeCommand(g_command, "TABLE_NAME: tsdb1", "CU_CNT: 58", "ROW_CNT: 1682");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C2Int8C5StrTextIpSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "TABLE_NAME: tsdb1", "CU_CNT: 30", "ROW_CNT: 1711");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmsysview -sql \"select id, count(*), newcol, name from %s order by id;\" -s %s", g_tableName,
        g_connServerTsdb);
    ret = executeCommand(g_command, "id: 0", "count(*): 1711", "newcol: 00000000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 断开连接
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放内存
    TestFreeTagC3Int8C4StrAlloc(obj, count);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
05.创建一个逻辑表,插入2次每批29条数据，新增列，prepare和bind并设置快照，目录切换到B，创建一个逻辑表（表结构和原先不同），
插入29次每批29条数据，新增列，再插入30次少量数据，触发磁盘整理；再切换到原路径，获取快照并绑定新列，插入数据，不会触发磁盘整理，3个CU
**************************************************************************** */
TEST_F(tsdbAlterEnh_003_test2, Timing_054_007_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    GmcStmtViewT *stmtView = NULL;
    int ret = 0;
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /home/<USER>/");
    char tableName[1024] = {0};
    constexpr int64_t count = 29, cpCnt = 20;
    int tmp = count;
    C3Int8C4StrT *obj = NULL;
    ret = TestAllocTagC3Int8C4Str(&obj, count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char ip[cpCnt][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[cpCnt][64] = {"david", "nut中", "bob中", "olivia，", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc",
        "lucy", "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    GenerateRandomAndChinString(obj->ns, count);
    for (int i = 0; i < count; i++) {
        obj->id[i] = i;
        obj->time[i] = 1704067200 + i;
        if ((i % cpCnt == 0) && (tmp >= cpCnt)) {
            tmp -= cpCnt;
            memcpy_s(obj->ip + i * 33, 33 * cpCnt, (char *)ip, 33 * cpCnt);
            memcpy_s(obj->name + i * 64, 64 * cpCnt, (char *)name, 64 * cpCnt);
        } else if ((i % 20 == 0) && (tmp > 0)) {
            memcpy_s(obj->ip + i * 33, 33 * tmp, (char *)ip, 33 * tmp);
            memcpy_s(obj->name + i * 64, 64 * tmp, (char *)name, 64 * tmp);
        }
        obj->messageLen[i] = 160;
        (void)snprintf(obj->message[i], obj->messageLen[i], "this is 中%d，zhong,中;", i);
    }

    // 创建逻辑表1
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet,"
        " message blob(160), ns text)"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '5000 MB',"
        " compression = 'fast(rapidlz)', table_path = '/home/<USER>/', is_volatile_label = 'true');",
        g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 2; i++) {
        ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C4StrSet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 加列
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol inet;", g_tableName);
    ret = GmcExecDirect(stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询表1数据
    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmsysview -sql \"select id, count(*), newcol, name from %s order by id;\" -s %s", g_tableName,
        g_connServerTsdb);
    ret = executeCommand(g_command, "id: 0", "count(*): 58", "newcol: 00000000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 绑定数据
    ret = GmcPrepareStmtByLabelName(g_stmt, g_tableName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStmtAttr(g_stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, obj->id, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, obj->time, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 2, (GmcDataTypeE)DB_DATATYPE_FIXED, obj->name, 64, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 3, (GmcDataTypeE)DB_DATATYPE_FIXED, obj->ip, 33, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret =
        GmcBindCol(g_stmt, 4, (GmcDataTypeE)DB_DATATYPE_BYTES, obj->message, sizeof(obj->message[0]), obj->messageLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 5, (GmcDataTypeE)DB_DATATYPE_STRING, obj->ns, sizeof(obj->ns[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置快照
    ret = GmcGetStmtView(g_stmt, &stmtView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 目录切换到B
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建逻辑表2
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet,"
        " message text, ns blob(160), ns1 text)"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '5000 MB',"
        " compression = 'fast(rapidlz)');",
        g_tableName2);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 连接2对逻辑表2插入少量数据
    for (int j = 0; j < 29; j++) {
        ret = writeRecordTs(conn, stmt, g_tableName2, obj, count, C2Int8C5StrTextSet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol text;", g_tableName2);
    ret = GmcExecDirect(stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE';\" -s %s", g_connServerTsdb);
    ret = executeCommand(g_command, "TABLE_NAME: tsdb2", "CU_CNT: 29", "ROW_CNT: 841");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    for (int i = 0; i < count; i++) {
        obj->id[i] = i + count * 2;
    }
    for (int j = 0; j < 30; j++) {
        ret = writeRecordTs(g_conn, g_stmt, g_tableName2, obj, count, C2Int8C6StrTextSet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE';\" -s %s", g_connServerTsdb);
    ret = executeCommand(g_command, "TABLE_NAME: tsdb2", "CU_CNT: 2", "ROW_CNT: 1711");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 切换到原路径
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 获取快照并绑定新列
    ret = GmcSetStmtView(g_stmt, stmtView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 6, (GmcDataTypeE)DB_DATATYPE_FIXED, obj->ip, 33, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecute(g_stmt));

    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmsysview -sql \"select * from 'V\\$CATA_VERTEX_LABEL_INFO' where "
        "VERTEX_LABEL_NAME = '%s';\" -s %s",
        g_tableName, g_connServerTsdb);
    ret = executeCommand(g_command, "VERTEX_LABEL_NAME: tsdb1", "\"name\": \"newcol\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE';\" -s %s", g_connServerTsdb);
    ret = executeCommand(g_command, "TABLE_NAME: tsdb1", "CU_CNT: 3", "ROW_CNT: 87");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmsysview -sql \"select id, count(*), newcol, name from %s order by id;\" -s %s", g_tableName,
        g_connServerTsdb);
    ret = executeCommand(g_command, "id: 0", "count(*): 87", "newcol: 00000000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 断开连接
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放内存
    TestFreeTagC3Int8C4StrAlloc(obj, count);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
06.100个线程对同一个逻辑表并发加列，预期无问题
**************************************************************************** */
TEST_F(tsdbAlterEnh_003_test2, Timing_054_007_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = 0;
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /home/<USER>/");
    char tableName[1024] = {0};
    constexpr int64_t count = 29, cpCnt = 20;
    int tmp = count;
    C3Int8C4StrT *obj = NULL;
    ret = TestAllocTagC3Int8C4Str(&obj, count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char ip[cpCnt][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[cpCnt][64] = {"david", "nut中", "bob中", "olivia，", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc",
        "lucy", "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    GenerateRandomAndChinString(obj->ns, count);
    for (int i = 0; i < count; i++) {
        obj->id[i] = i;
        obj->time[i] = 1704067200 + i;
        if ((i % cpCnt == 0) && (tmp >= cpCnt)) {
            tmp -= cpCnt;
            memcpy_s(obj->ip + i * 33, 33 * cpCnt, (char *)ip, 33 * cpCnt);
            memcpy_s(obj->name + i * 64, 64 * cpCnt, (char *)name, 64 * cpCnt);
        } else if ((i % 20 == 0) && (tmp > 0)) {
            memcpy_s(obj->ip + i * 33, 33 * tmp, (char *)ip, 33 * tmp);
            memcpy_s(obj->name + i * 64, 64 * tmp, (char *)name, 64 * tmp);
        }
        obj->messageLen[i] = 160;
        (void)snprintf(obj->message[i], obj->messageLen[i], "this is 中%d，zhong,中;", i);
    }

    // 创建逻辑表1
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet,"
        " message blob(160), ns text)"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '5000 MB',"
        " compression = 'fast(rapidlz)', table_path = '/home/<USER>/', is_volatile_label = 'true');",
        g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 2; i++) {
        ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C4StrSet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 100个线程并发alter
    int threadNum = 100;
    ThreadStructAlterT objT[threadNum];
    memset(objT, 0, sizeof(ThreadStructAlterT));
    pthread_t thr_arr[threadNum];
    for (int i = 0; i < threadNum; i++) {
        objT[i].id = i;
        objT[i].labelName = g_tableName;
        ret = pthread_create(&thr_arr[i], NULL, ThreadAlterTable, (void *)&objT[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    for (int i = 0; i < threadNum; i++) {
        pthread_join(thr_arr[i], NULL);
    }
    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmsysview -sql \"select * from 'V\\$CATA_VERTEX_LABEL_INFO' where "
        "VERTEX_LABEL_NAME = '%s';\" -s %s",
        g_tableName, g_connServerTsdb);
    ret = executeCommand(g_command, "VERTEX_LABEL_NAME: tsdb1", "\"name\": \"newcol0\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C3Int8C4StrSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 断开连接
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放内存
    TestFreeTagC3Int8C4StrAlloc(obj, count);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
07.3个线程对不同的逻辑表插入数据（2次少量数据），插入含空列触发磁盘整理，插入2次少量数据，新增alter，插入少量数据（不含空列）
**************************************************************************** */
TEST_F(tsdbAlterEnh_003_test, Timing_054_007_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = 0;
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /home/<USER>/");
    char tableName[1024] = {0};
    constexpr int64_t count = 29, cpCnt = 20;
    int tmp = count;
    C3Int8C4StrT *obj = NULL;
    ret = TestAllocTagC3Int8C4Str(&obj, count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char ip[cpCnt][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[cpCnt][64] = {"david", "nut中", "bob中", "olivia，", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc",
        "lucy", "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    GenerateRandomAndChinString(obj->ns, count);
    for (int i = 0; i < count; i++) {
        obj->id[i] = i;
        obj->time[i] = 1704067200 + i;
        if ((i % cpCnt == 0) && (tmp >= cpCnt)) {
            tmp -= cpCnt;
            memcpy_s(obj->ip + i * 33, 33 * cpCnt, (char *)ip, 33 * cpCnt);
            memcpy_s(obj->name + i * 64, 64 * cpCnt, (char *)name, 64 * cpCnt);
        } else if ((i % 20 == 0) && (tmp > 0)) {
            memcpy_s(obj->ip + i * 33, 33 * tmp, (char *)ip, 33 * tmp);
            memcpy_s(obj->name + i * 64, 64 * tmp, (char *)name, 64 * tmp);
        }
        obj->messageLen[i] = 160;
        (void)snprintf(obj->message[i], obj->messageLen[i], "this is 中%d，zhong,中;", i);
    }

    // 3个线程并发对不同的表，插入数据，alter，插入少量数据
    int threadNum = 3;
    for (int i = 0; i < threadNum; i++) {
        (void)snprintf(tableName, MAX_CMD_SIZE, "test%d", i);
        (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
            "create table %s(id integer, time integer, name char(64), ip inet,"
            " message blob(160), ns text)"
            " with (time_col = 'time', interval = '1 hour', disk_limit = '5000 MB',"
            " compression = 'fast(rapidlz)', table_path = '/home/<USER>/', is_volatile_label = 'true');",
            tableName);
        ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = writeRecordTs(g_conn, g_stmt, tableName, obj, count, C3Int8C4StrSet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 2分区，每个分区29次，每批次29条
        for (int j = 0; j < 2; j++) {
            for (int k = 0; k < count; k++) {
                obj->time[k] = 1704067200 + j * 3600 + k;
            }
            for (int k = 0; k < 29; k++) {
                ret = writeRecordTs(g_conn, g_stmt, tableName, obj, count, C3Int8C4StrSet);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            }
        }
    }

    ThreadStructAlterT objT[threadNum];
    memset(objT, 0, sizeof(ThreadStructAlterT));
    pthread_t thr_arr[threadNum];
    for (int i = 0; i < threadNum; i++) {
        objT[i].labelName = (char *)malloc(MAX_CMD_SIZE * sizeof(char));
        if (objT[i].labelName == NULL) {
            AW_FUN_Log(LOG_INFO, "malloc failed.");
        }
        (void)snprintf(objT[i].labelName, MAX_CMD_SIZE, "test%d", i);
        objT[i].id = i;
        objT[i].obj = obj;
        ret = pthread_create(&thr_arr[i], NULL, ThreadAlterTableAndinsert, (void *)&objT[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    for (int i = 0; i < threadNum; i++) {
        pthread_join(thr_arr[i], NULL);
    }
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select * from 'V\\$CATA_VERTEX_LABEL_INFO';\" -s %s",
        g_connServerTsdb);
    ret = executeCommand(g_command, "VERTEX_LABEL_NAME: test0", "\"name\": \"newcol0\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE';\" -s %s", g_connServerTsdb);
    ret = executeCommand(g_command, "PHY_TBL_NUM: 2", "ROW_CNT: 4292");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    for (int i = 0; i < threadNum; i++) {
        (void)snprintf(tableName, MAX_CMD_SIZE, "test%d", i);
        ret = DropCmTable(g_stmt, tableName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (objT[i].labelName) {
            free(objT[i].labelName);
        }
    }

    // 查看磁盘情况
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE';\" -s %s", g_connServerTsdb);
    ret = executeCommand(g_command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE';\" -s %s", g_connServerTsdb);
    ret = executeCommand(g_command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 断开连接
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放内存
    TestFreeTagC3Int8C4StrAlloc(obj, count);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
08.8个线程对不同的逻辑表插入数据（2次少量数据），插入含空列触发磁盘整理，插入2次少量数据，新增alter，插入少量数据（含空列）
**************************************************************************** */
TEST_F(tsdbAlterEnh_003_test, Timing_054_007_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = 0;
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /home/<USER>/");
    char tableName[1024] = {0};
    constexpr int64_t count = 29, cpCnt = 20;
    int tmp = count;
    C3Int8C4StrT *obj = NULL;
    ret = TestAllocTagC3Int8C4Str(&obj, count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char ip[cpCnt][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[cpCnt][64] = {"david", "nut中", "bob中", "olivia，", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc",
        "lucy", "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    GenerateRandomAndChinString(obj->ns, count, true);
    for (int i = 0; i < count; i++) {
        obj->id[i] = i;
        obj->time[i] = 1704067200 + i;
        if ((i % cpCnt == 0) && (tmp >= cpCnt)) {
            tmp -= cpCnt;
            memcpy_s(obj->ip + i * 33, 33 * cpCnt, (char *)ip, 33 * cpCnt);
            memcpy_s(obj->name + i * 64, 64 * cpCnt, (char *)name, 64 * cpCnt);
        } else if ((i % 20 == 0) && (tmp > 0)) {
            memcpy_s(obj->ip + i * 33, 33 * tmp, (char *)ip, 33 * tmp);
            memcpy_s(obj->name + i * 64, 64 * tmp, (char *)name, 64 * tmp);
        }
        obj->messageLen[i] = 160;
        (void)snprintf(obj->message[i], obj->messageLen[i], "this is 中%d，zhong,中;", i);
    }

    // 8个线程并发对不同的表，插入数据，alter，插入少量数据
    int threadNum = 8;
    for (int i = 0; i < threadNum; i++) {
        (void)snprintf(tableName, MAX_CMD_SIZE, "test%d", i);
        (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
            "create table %s(id integer, time integer, name char(64), ip inet,"
            " message blob(160), ns text)"
            " with (time_col = 'time', interval = '1 hour', disk_limit = '5000 MB',"
            " compression = 'fast(rapidlz)', table_path = '/home/<USER>/', is_volatile_label = 'true');",
            tableName);
        ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = writeRecordTs(g_conn, g_stmt, tableName, obj, count, C3Int8C4StrSet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ThreadStructAlterT objT[threadNum];
    memset(objT, 0, sizeof(ThreadStructAlterT));
    pthread_t thr_arr[threadNum];
    for (int i = 0; i < threadNum; i++) {
        objT[i].labelName = (char *)malloc(MAX_CMD_SIZE * sizeof(char));
        if (objT[i].labelName == NULL) {
            AW_FUN_Log(LOG_INFO, "malloc failed.");
        }
        (void)snprintf(objT[i].labelName, MAX_CMD_SIZE, "test%d", i);
        objT[i].id = i;
        objT[i].obj = obj;
        ret = pthread_create(&thr_arr[i], NULL, ThreadAlterTableAndinsertNoBindCol, (void *)&objT[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    for (int i = 0; i < threadNum; i++) {
        pthread_join(thr_arr[i], NULL);
    }
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select * from 'V\\$CATA_VERTEX_LABEL_INFO';\" -s %s",
        g_connServerTsdb);
    ret = executeCommand(g_command, "VERTEX_LABEL_NAME: test0", "\"name\": \"newcol0\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 查看磁盘情况
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE';\" -s %s", g_connServerTsdb);
    ret = executeCommand(g_command, "CU_CNT: 33", "ROW_CNT: 2610", "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    for (int i = 0; i < threadNum; i++) {
        (void)snprintf(tableName, MAX_CMD_SIZE, "test%d", i);
        ret = DropCmTable(g_stmt, tableName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (objT[i].labelName) {
            free(objT[i].labelName);
        }
    }

    // 断开连接
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放内存
    TestFreeTagC3Int8C4StrAlloc(obj, count);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
09.创建逻辑表，分别新增integer，char(65535)，text(变长)，blob(65535)，ip，插入含各自列边界值，一批插入5w条数据，
全表查询，校验数据，不超过32MB；触发查询32MB大结果集，走临时文件
**************************************************************************** */
TEST_F(tsdbAlterEnh_003_test, Timing_054_007_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = 0;
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /home/<USER>/");
    char tableName[1024] = {0};
    constexpr int64_t count = 200, cpCnt = 20;
    int tmp = count;
    C3Int8C4StrT *obj = NULL;
    ret = TestAllocTagC3Int8C4Str(&obj, count, 65535);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char ip[cpCnt][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[cpCnt][65535] = {"david", "nut中", "bob中", "olivia，", "tim", "lucy", "apple", "bob", "bob", "omgd",
        "cc", "lucy", "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    GenerateRandomAndChinString(obj->ns, count);
    for (int i = 0; i < count; i++) {
        obj->id[i] = i;
        obj->time[i] = 1704067200 + i;
        if ((i % cpCnt == 0) && (tmp >= cpCnt)) {
            tmp -= cpCnt;
            memcpy_s(obj->ip + i * 33, 33 * cpCnt, (char *)ip, 33 * cpCnt);
            memcpy_s(obj->name + i * 65535, 65535 * cpCnt, (char *)name, 65535 * cpCnt);
        } else if ((i % 20 == 0) && (tmp > 0)) {
            memcpy_s(obj->ip + i * 33, 33 * tmp, (char *)ip, 33 * tmp);
            memcpy_s(obj->name + i * 65535, 65535 * tmp, (char *)name, 65535 * tmp);
        }
        obj->messageLen[i] = 65535;
        (void)snprintf(obj->message[i], obj->messageLen[i], "this is 中%d，zhong,中;", i);
    }
    // 覆盖边界值
    obj->messageLen[0] = 0;
    obj->id[0] = -9223372036854775808;
    obj->id[count - 1] = 9223372036854775807;

    // 创建逻辑表1
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(65535), ip inet,"
        " message blob(65535), ns text)"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '5000 MB',"
        " compression = 'fast(rapidlz)', table_path = '/home/<USER>/', is_volatile_label = 'true');",
        g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int j = 0; j < 120; j++) {
        for (int k = 0; k < count; k++) {
            obj->time[k] = 1704067200 + j * 3600 + k;
        }
        ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C2Int8C4BoundStrSet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 加列
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol inet;", g_tableName);
    ret = GmcExecDirect(stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询表1数据
    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmsysview -sql \"select id, newcol, *, newcol, name from %s order by id limit 10000;\" -s %s", g_tableName,
        g_connServerTsdb);
    ret = executeCommand(g_command, "id: -9223372036854775808", "newcol: 00000000", "id: -9223372036854775808");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 断开连接
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放内存
    TestFreeTagC3Int8C4StrAlloc(obj, count);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
10.创建2个时序逻辑表，分别插入1w条数据，两个线程并发测试insert into（A->B；B->A）
**************************************************************************** */
TEST_F(tsdbAlterEnh_003_test, Timing_054_007_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = 0;
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /home/<USER>/");
    char tableName[1024] = {0};
    constexpr int64_t count = 200, cpCnt = 20;
    int tmp = count;
    C3Int8C4StrT *obj = NULL;
    ret = TestAllocTagC3Int8C4Str(&obj, count, 65535);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char ip[cpCnt][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[cpCnt][65535] = {"david", "nut中", "bob中", "olivia，", "tim", "lucy", "apple", "bob", "bob", "omgd",
        "cc", "lucy", "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    GenerateRandomAndChinString(obj->ns, count);
    for (int i = 0; i < count; i++) {
        obj->id[i] = i;
        obj->time[i] = 1704067200 + i;
        if ((i % cpCnt == 0) && (tmp >= cpCnt)) {
            tmp -= cpCnt;
            memcpy_s(obj->ip + i * 33, 33 * cpCnt, (char *)ip, 33 * cpCnt);
            memcpy_s(obj->name + i * 65535, 65535 * cpCnt, (char *)name, 65535 * cpCnt);
        } else if ((i % 20 == 0) && (tmp > 0)) {
            memcpy_s(obj->ip + i * 33, 33 * tmp, (char *)ip, 33 * tmp);
            memcpy_s(obj->name + i * 65535, 65535 * tmp, (char *)name, 65535 * tmp);
        }
        obj->messageLen[i] = 65535;
        (void)snprintf(obj->message[i], obj->messageLen[i], "this is 中%d，zhong,中;", i);
    }
    // 覆盖边界值
    obj->messageLen[0] = 0;
    obj->id[0] = -9223372036854775808;
    obj->id[count - 1] = 9223372036854775807;

    // 创建逻辑表1
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(65535), ip inet,"
        " message blob(65535), ns text)"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '5000 MB',"
        " compression = 'fast(rapidlz)', table_path = '/home/<USER>/', is_volatile_label = 'true');",
        g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(65535), ip inet,"
        " message blob(65535), ns text)"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '5000 MB',"
        " compression = 'fast(rapidlz)', table_path = '/home/<USER>/', is_volatile_label = 'true');",
        g_tableName2);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int j = 0; j < 50; j++) {
        for (int k = 0; k < count; k++) {
            obj->time[k] = 1704067200 + j * 3600 + k;
        }
        ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C2Int8C4BoundStrSet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = writeRecordTs(g_conn, g_stmt, g_tableName2, obj, count, C2Int8C4BoundStrSet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 加列
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol inet;", g_tableName);
    ret = GmcExecDirect(stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol inet;", g_tableName2);
    ret = GmcExecDirect(stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询表1数据
    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmsysview -sql \"select id, newcol, *, newcol, name from %s order by id limit 10000;\" -s %s", g_tableName,
        g_connServerTsdb);
    ret = executeCommand(g_command, "id: -9223372036854775808", "newcol: 00000000", "id: -9223372036854775808");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 并发insert into
    int threadNum = 2;
    ThreadStructInsertIntoT objT[threadNum];
    memset(objT, 0, sizeof(ThreadStructInsertIntoT));
    pthread_t thr_arr[threadNum];
    for (int i = 0; i < threadNum; i++) {
        if (i % 2) {
            objT[i].sourcelabelName = g_tableName;
            objT[i].destlabelName = g_tableName2;
        } else {
            objT[i].sourcelabelName = g_tableName2;
            objT[i].destlabelName = g_tableName;
        }
        objT[i].id = i;
        ret = pthread_create(&thr_arr[i], NULL, ThreadInsertInto, (void *)&objT[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (int i = 0; i < threadNum; i++) {
        pthread_join(thr_arr[i], NULL);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropCmTable(g_stmt, g_tableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 断开连接
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放内存
    TestFreeTagC3Int8C4StrAlloc(obj, count);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 内存表内存统计有误
// DTS2025072413046, DTS2025072423757
/* ****************************************************************************
 Description  :
11.时序内存表设置diskLimit为100M，实际内存统计超过100M，使用接口查系统视图（10000次），无内存泄漏
**************************************************************************** */
TEST_F(tsdbAlterEnh_003_test, Timing_054_007_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = 0;
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int shareMem01 = 0, shareMem02 = 0, dynMem01 = 0, dynMem02 = 0;
    system("rm -rf /home/<USER>/");
    char tableName[1024] = {0};
    constexpr int64_t count = 100, cpCnt = 20;
    int tmp = count;
    C3Int8C4StrT *obj = NULL;
    ret = TestAllocTagC3Int8C4Str(&obj, count, 65535);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char ip[cpCnt][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[cpCnt][65535] = {"david", "nut中", "bob中", "olivia，", "tim", "lucy", "apple", "bob", "bob", "omgd",
        "cc", "lucy", "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    GenerateRandomAndChinString(obj->ns, count);
    for (int i = 0; i < count; i++) {
        obj->id[i] = i;
        obj->time[i] = 1704067200 + i;
        if ((i % cpCnt == 0) && (tmp >= cpCnt)) {
            tmp -= cpCnt;
            memcpy_s(obj->ip + i * 33, 33 * cpCnt, (char *)ip, 33 * cpCnt);
            memcpy_s(obj->name + i * 65535, 65535 * cpCnt, (char *)name, 65535 * cpCnt);
        } else if ((i % 20 == 0) && (tmp > 0)) {
            memcpy_s(obj->ip + i * 33, 33 * tmp, (char *)ip, 33 * tmp);
            memcpy_s(obj->name + i * 65535, 65535 * tmp, (char *)name, 65535 * tmp);
        }
        obj->messageLen[i] = 528;
        (void)snprintf(obj->message[i], obj->messageLen[i], "this is 中%d，zhong,中;", i);
    }
    // 覆盖边界值
    obj->id[0] = -9223372036854775808;
    obj->id[count - 1] = 9223372036854775807;

    // 创建内存表1
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(log_id INTEGER, log_type INTEGER, log_time INTEGER, file_type INTEGER, "
        "trans_first INTEGER, pkt_len INTEGER, tv_sec INTEGER, tv_usec INTEGER, pkt_id CHAR(532), pkt_buf BLOB(528), "
        "index memidx(pkt_id), index memidx2(pkt_id), index pktbufid(pkt_buf)) WITH (interval = '1 hour', "
        "disk_limit = '104857600 B', engine = 'memory',  time_col = 'log_time', max_size = '10000000000');",
        g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建逻辑表1
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(log_id INTEGER, log_type INTEGER, log_time INTEGER, file_type INTEGER, "
        "trans_first INTEGER, pkt_len INTEGER, tv_sec INTEGER, tv_usec INTEGER, pkt_id CHAR(532), pkt_buf BLOB(1600)) "
        "WITH (interval = '1 hour', disk_limit = '104857600 B', time_col = 'log_time');",
        g_tableName2);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int j = 0; j < 920; j++) {
        for (int k = 0; k < count; k++) {
            obj->time[k] = 1704067200 + j * 36 + k;
        }
        ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C8Int8C2StrSet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (ret) {
            AW_FUN_Log(LOG_INFO, "mem table ttl j is %d", j);
        }
        ret = writeRecordTs(g_conn, g_stmt, g_tableName2, obj, count, C8Int8C2StrSet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "select * from 'V$COM_DYN_CTX';");
    ret = GmcExecDirect(stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过接口查询系统视图
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select * from 'V\\$COM_DYN_CTX' where "
        "CTX_NAME = 'Top Dynamic Memory Context';\" -s %s", g_connServerTsdb);
    system(g_command);

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select * from 'V\\$COM_MEM_SUMMARY';\" -s %s",
        g_connServerTsdb);
    system(g_command);

    // 获取动态内存和共享内存值
    ret = TestGetMemVal(&shareMem01, (char *)"COM_MEM_SUMMARY", (char *)"STORAGE_MEM_USED");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGetMemVal(&dynMem01, (char *)"COM_MEM_SUMMARY", (char *)"GLOBAL_DYN_MEM_ALLOCSIZE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < 10000; i++) {
        ret = GmcExecDirect(stmt, g_sqlCmd, strlen(g_sqlCmd));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select * from 'V\\$COM_DYN_CTX' where "
        "CTX_NAME = 'Top Dynamic Memory Context';\" -s %s", g_connServerTsdb);
    system(g_command);
    
    // 查询内存相关视图
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select * from 'V\\$COM_MEM_SUMMARY';\" -s %s",
        g_connServerTsdb);
    system(g_command);

    // 获取动态内存和共享内存值
    ret = TestGetMemVal(&shareMem02, (char *)"COM_MEM_SUMMARY", (char *)"STORAGE_MEM_USED");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGetMemVal(&dynMem02, (char *)"COM_MEM_SUMMARY", (char *)"GLOBAL_DYN_MEM_ALLOCSIZE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_DEBUG, "shareMem01 is %d, shareMem02 is %d, dynMem01 is %d, dynMem02 is %d", shareMem01,
        shareMem02, dynMem01, dynMem02);
    
    // 校验值，小于200k
    EXPECT_LE(shareMem02 - shareMem01, 202400);
    EXPECT_LE(dynMem02 - dynMem01, 202400);
    // 小于150M
    EXPECT_LE(shareMem02, 157286400);

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropCmTable(g_stmt, g_tableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 断开连接
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放内存
    TestFreeTagC3Int8C4StrAlloc(obj, count);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// DTS2025072416361
/* ****************************************************************************
 Description  :
12.时序内存表只设置diskLimit为100M，不设置max_size，插入数据最大数量不应该最大是50000
**************************************************************************** */
TEST_F(tsdbAlterEnh_003_test, Timing_054_007_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = 0;
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int shareMem01 = 0, shareMem02 = 0, dynMem01 = 0, dynMem02 = 0;
    system("rm -rf /home/<USER>/");
    char tableName[1024] = {0};
    constexpr int64_t count = 100, cpCnt = 20;
    int tmp = count;
    C3Int8C4StrT *obj = NULL;
    ret = TestAllocTagC3Int8C4Str(&obj, count, 65535);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char ip[cpCnt][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[cpCnt][65535] = {"david", "nut中", "bob中", "olivia，", "tim", "lucy", "apple", "bob", "bob", "omgd",
        "cc", "lucy", "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    GenerateRandomAndChinString(obj->ns, count);
    for (int i = 0; i < count; i++) {
        obj->id[i] = i;
        obj->time[i] = 1704067200 + i;
        if ((i % cpCnt == 0) && (tmp >= cpCnt)) {
            tmp -= cpCnt;
            memcpy_s(obj->ip + i * 33, 33 * cpCnt, (char *)ip, 33 * cpCnt);
            memcpy_s(obj->name + i * 65535, 65535 * cpCnt, (char *)name, 65535 * cpCnt);
        } else if ((i % 20 == 0) && (tmp > 0)) {
            memcpy_s(obj->ip + i * 33, 33 * tmp, (char *)ip, 33 * tmp);
            memcpy_s(obj->name + i * 65535, 65535 * tmp, (char *)name, 65535 * tmp);
        }
        obj->messageLen[i] = 528;
        (void)snprintf(obj->message[i], obj->messageLen[i], "this is 中%d，zhong,中;", i);
    }
    // 覆盖边界值
    obj->id[0] = -9223372036854775808;
    obj->id[count - 1] = 9223372036854775807;

    // 创建内存表1
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(log_id INTEGER, log_type INTEGER, log_time INTEGER, file_type INTEGER, "
        "trans_first INTEGER, pkt_len INTEGER, tv_sec INTEGER, tv_usec INTEGER, pkt_id CHAR(532), pkt_buf BLOB(528), "
        "index memidx(pkt_id), index memidx2(pkt_id), index pktbufid(pkt_buf)) WITH (interval = '1 hour', "
        "disk_limit = '104857600 B', engine = 'memory',  time_col = 'log_time');",
        g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int j = 0; j < 600; j++) {
        for (int k = 0; k < count; k++) {
            obj->time[k] = 1704067200 + j * 36 + k;
        }
        ret = writeRecordTs(g_conn, g_stmt, g_tableName, obj, count, C8Int8C2StrSet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (ret) {
            AW_FUN_Log(LOG_INFO, "mem table ttl j is %d", j);
        }
    }

    // 查询内存表数据量
    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmsysview -sql \"select count(*) from %s limit 10000;\" -s %s", g_tableName,
        g_connServerTsdb);
    ret = executeCommand(g_command, ":Count(*)");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 断开连接
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放内存
    TestFreeTagC3Int8C4StrAlloc(obj, count);
    AW_FUN_Log(LOG_STEP, "test end.");
}
