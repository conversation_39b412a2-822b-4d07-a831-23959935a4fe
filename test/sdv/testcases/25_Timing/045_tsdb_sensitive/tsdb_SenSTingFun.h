/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: tsdb_SenSTingFun.h
 * Description: SenSTingFun头文件
 * Author: yang<PERSON>wen ywx1060383
 * Create: 2024-08-10
 */

#ifndef SENSTINGFUN_H
#define SENSTINGFUN_H
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"

#define MAX_CMD_SIZE 1024

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
char g_tableName[] = "sensitivetest";
char g_command[MAX_CMD_SIZE];

typedef struct {
    GmcStmtT *stmt;
    char *tableName;
    int stepTime;
    int stepCount;
    int maxSize;
} StreamTableBody;

int CreateStreamTableAndInsert(StreamTableBody streamTableBody)
{
    int ret = 0;
    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64)) with (time_col = "
        "'time', is_stream = 'true', step_time = '%d s', step_count = %d, max_size = %d, interval ='1 hour', "
        "sensitive_col = 'id, time, name');",
        streamTableBody.tableName, streamTableBody.stepTime, streamTableBody.stepCount, streamTableBody.maxSize);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    RETURN_IFERR(ret);

    constexpr int64_t count = 10;
    int64_t time[count] = {0};
    int64_t id[count] = {1, 1, 1, 1, 2, 2, 2, 3, 3, 4};
    char name[count][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};

    for (int i = 0; i < count; i++) {
        time[i] = 1695042000 + i;
    }
    ret = BlukInsert_char(streamTableBody.stmt, streamTableBody.tableName, count, 3, id, time, name);
    return ret;
}
#endif
