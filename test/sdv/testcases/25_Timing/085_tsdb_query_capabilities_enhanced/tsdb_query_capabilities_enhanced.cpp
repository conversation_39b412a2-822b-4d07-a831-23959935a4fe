/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2025. All rights reserved.
 */

extern "C" {}
#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include <sys/time.h>
#include <time.h>
#include <sys/ipc.h>
#include <sys/shm.h>
#include <malloc.h>

#include "gtest/gtest.h"
#include "tsdb_query_capabilities_enhanced.h"

// 内存申请大小限制
#if defined ENV_RTOSV2X
#define MEGABYTE (10 * 1024)
#else
#define MEGABYTE (1024 * 1024 * 1024)
#endif

int ret = 0;
int thr_count = 0;

class tsdb_query_capabilities_enhanced : public testing::Test {
public:
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}
    virtual void SetUp()
    {
        system("ipcrm -a");  // 避免被其他用例影响
        InitTsCiCfg();
        ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建epoll监听线程
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        char sqlCmd[256] = {0};
        (void)sprintf(sqlCmd, "drop table %s;", g_tableName);
        uint32_t cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);

        char ddlCommand[512];
        snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
            "create table %s(name char(64),  id integer, worktime integer,vsysId TEXT) with "
            "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)');",
            g_tableName);

        ret = GmcExecDirect(g_stmt_sync, ddlCommand, 512);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    }
    virtual void TearDown()
    {
        char sqlCmd[256] = {0};
        (void)sprintf(sqlCmd, "drop table %s;", "testdb");
        uint32_t cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

        ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        system("sh $TEST_HOME/tools/modifyCfg.sh -ts recover");
        close_epoll_thread();
        testEnvClean();
    }
};

// 001、查询四张视图，过滤条件为 TABLE_NAME = '表名'或LOGIC_LABEL_NAME = '表名'
TEST_F(tsdb_query_capabilities_enhanced, Timing_085_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd, "drop table %s;", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    int tableNum = 200;
    int count = 10000;

    for (int i = 0; i < tableNum; i++) {
        (void)sprintf(g_tableName, "testdb%d", i);
        if (i % 50 == 0) {
            AW_FUN_Log(LOG_STEP, "create table num %d table_name=%s.\n", i, g_tableName);
        }

        (void)sprintf(sqlCmd, "drop table %s;", g_tableName);
        uint32_t cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);

        (void)snprintf(sqlCmd, MAX_CMD_SIZE,
            "create table %s(name char(64),  id integer, worktime integer,vsysId TEXT) with "
            "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)');",
            g_tableName);
        ret = GmcExecDirect(g_stmt_sync, sqlCmd, strlen(sqlCmd));
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

        int64_t ids[count];
        (void)memset_s(ids, count * sizeof(int64_t), 0, count * sizeof(int64_t));
        int64_t worktimes[count];
        (void)memset_s(worktimes, count * sizeof(int64_t), 0, count * sizeof(int64_t));
        char *names = (char *)malloc(count * 64);
        char nameSource[10][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
        char **vsysId = (char **)malloc(count * sizeof(char *));
        if (vsysId == NULL) {
            AW_FUN_Log(LOG_INFO, "Malloc unsucc.");
        }

        for (int i = 0; i < count; i++) {
            ids[i] = i + 1;
            worktimes[i] = 1695042000 + i;
            if (i % 10 == 0) {
                memcpy((names + i * 64), (char *)nameSource, 640);
            }
            vsysId[i] = (char *)malloc(TEXT_LEN * sizeof(char));
            (void)memset(vsysId[i], 0, TEXT_LEN);
        }

        GenerateRandomString(vsysId, count);

        uint32_t rowNum = count;
        int ret = GmcPrepareStmtByLabelName(g_stmt_sync, g_tableName, GMC_OPERATION_SQL_INSERT);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(g_stmt_sync, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, names, 64, NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(g_stmt_sync, 1, (GmcDataTypeE)DB_DATATYPE_INT64, ids, sizeof(ids[0]), NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(g_stmt_sync, 2, (GmcDataTypeE)DB_DATATYPE_INT64, worktimes, sizeof(worktimes[0]), NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(g_stmt_sync, 3, (GmcDataTypeE)DB_DATATYPE_STRING, vsysId, sizeof(char *), 0);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcSetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcExecute(g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        FreeHeapMemory(names);
        for (int i = 0; i < count; i++) {
            FreeHeapMemory(vsysId[i]);
        }
        FreeHeapMemory(vsysId);
    }

    // 查询STORAGE_DISK_USAGE
    snprintf(g_command, 1024,
        "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where TABLE_NAME='testdb100' \" -s %s | grep time "
        "|awk -F ':' '{print $2}'",
        g_connServerTsdb);
    AW_FUN_Log(LOG_STEP, "g_command=%s\n", g_command);
    int32_t TimeValue1 = 0;
    TimeValue1 = GetViewFieldResultValue(g_command);
    AW_FUN_Log(LOG_STEP, "TimeValue1=%d\n", TimeValue1);
    EXPECT_LE(TimeValue1, 100);

    // 查询PHY_TBL_DISK_USAGE
    snprintf(g_command, 1024,
        "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE' where TABLE_NAME='testdb100' \" -s %s | grep time "
        "|awk -F ':' '{print $2}'",
        g_connServerTsdb);
    AW_FUN_Log(LOG_STEP, "g_command=%s\n", g_command);
    int32_t TimeValue2 = 0;
    TimeValue2 = GetViewFieldResultValue(g_command);
    AW_FUN_Log(LOG_STEP, "TimeValue2=%d\n", TimeValue2);
    EXPECT_LE(TimeValue2, 100);

    // 查询TS_TBL_OPER_STATIS
    snprintf(g_command, 1024,
        "gmsysview -sql \"select * from 'V\\$TS_TBL_OPER_STATIS' where TABLE_NAME='testdb100' \" -s %s | grep time "
        "|awk -F ':' '{print $2}'",
        g_connServerTsdb);
    AW_FUN_Log(LOG_STEP, "g_command=%s\n", g_command);
    int32_t TimeValue3 = 0;
    TimeValue3 = GetViewFieldResultValue(g_command);
    AW_FUN_Log(LOG_STEP, "TimeValue3=%d\n", TimeValue3);
    EXPECT_LE(TimeValue3, 100);

    // 查询TS_TBL_PROPS
    snprintf(g_command, 1024,
        "gmsysview -sql \"select * from 'V\\$TS_TBL_PROPS' where TABLE_NAME='testdb100' \" -s %s | grep time "
        "|awk -F ':' '{print $2}'",
        g_connServerTsdb);
    AW_FUN_Log(LOG_STEP, "g_command=%s\n", g_command);
    int32_t TimeValue4 = 0;
    TimeValue4 = GetViewFieldResultValue(g_command);
    AW_FUN_Log(LOG_STEP, "TimeValue4=%d\n", TimeValue4);
    EXPECT_LE(TimeValue4, 100);

    for (int i = 0; i < tableNum; i++) {
        (void)sprintf(g_tableName, "testdb%d", i);
        if (i % 50 == 0) {
            AW_FUN_Log(LOG_STEP, "drop table num %d table_name=%s.\n", i, g_tableName);
        }
        (void)sprintf(sqlCmd, "drop table %s;", g_tableName);
        uint32_t cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    }

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(64),  id integer, worktime integer,vsysId TEXT) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)');",
        "testdb");

    ret = GmcExecDirect(g_stmt_sync, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
}

// 002、查询四张视图，过滤条件为 TABLE_NAME = '表名'或LOGIC_LABEL_NAME = '表名' AND 其他过滤条件
TEST_F(tsdb_query_capabilities_enhanced, Timing_085_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd, "drop table %s;", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    int tableNum = 200;
    int count = 10000;

    for (int i = 0; i < tableNum; i++) {
        (void)sprintf(g_tableName, "testdb%d", i);
        if (i % 50 == 0) {
            AW_FUN_Log(LOG_STEP, "create table num %d table_name=%s.\n", i, g_tableName);
        }

        (void)sprintf(sqlCmd, "drop table %s;", g_tableName);
        uint32_t cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);

        (void)snprintf(sqlCmd, MAX_CMD_SIZE,
            "create table %s(name char(64),  id integer, worktime integer,vsysId TEXT) with "
            "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)');",
            g_tableName);
        ret = GmcExecDirect(g_stmt_sync, sqlCmd, strlen(sqlCmd));
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

        int64_t ids[count];
        (void)memset_s(ids, count * sizeof(int64_t), 0, count * sizeof(int64_t));
        int64_t worktimes[count];
        (void)memset_s(worktimes, count * sizeof(int64_t), 0, count * sizeof(int64_t));
        char *names = (char *)malloc(count * 64);
        char nameSource[10][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
        char **vsysId = (char **)malloc(count * sizeof(char *));
        if (vsysId == NULL) {
            AW_FUN_Log(LOG_INFO, "Malloc unsucc.");
        }

        for (int i = 0; i < count; i++) {
            ids[i] = i + 1;
            worktimes[i] = 1695042000 + i;
            if (i % 10 == 0) {
                memcpy((names + i * 64), (char *)nameSource, 640);
            }
            vsysId[i] = (char *)malloc(TEXT_LEN * sizeof(char));
            (void)memset(vsysId[i], 0, TEXT_LEN);
        }

        GenerateRandomString(vsysId, count);

        uint32_t rowNum = count;
        int ret = GmcPrepareStmtByLabelName(g_stmt_sync, g_tableName, GMC_OPERATION_SQL_INSERT);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(g_stmt_sync, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, names, 64, NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(g_stmt_sync, 1, (GmcDataTypeE)DB_DATATYPE_INT64, ids, sizeof(ids[0]), NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(g_stmt_sync, 2, (GmcDataTypeE)DB_DATATYPE_INT64, worktimes, sizeof(worktimes[0]), NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(g_stmt_sync, 3, (GmcDataTypeE)DB_DATATYPE_STRING, vsysId, sizeof(char *), 0);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcSetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcExecute(g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        FreeHeapMemory(names);
        for (int i = 0; i < count; i++) {
            FreeHeapMemory(vsysId[i]);
        }
        FreeHeapMemory(vsysId);
    }

    // 查询STORAGE_DISK_USAGE
    snprintf(g_command, 1024,
        "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where TABLE_NAME='testdb100' and ROW_CNT=1000\" -s %s "
        "| grep time "
        "|awk -F ':' '{print $2}'",
        g_connServerTsdb);
    AW_FUN_Log(LOG_STEP, "g_command=%s\n", g_command);
    int32_t TimeValue1 = 0;
    TimeValue1 = GetViewFieldResultValue(g_command);
    AW_FUN_Log(LOG_STEP, "TimeValue1=%d\n", TimeValue1);
    EXPECT_LE(TimeValue1, 100);

    // 查询PHY_TBL_DISK_USAGE
    snprintf(g_command, 1024,
        "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE' where TABLE_NAME='testdb100' and ROW_CNT=1000\" -s %s "
        "| grep time "
        "|awk -F ':' '{print $2}'",
        g_connServerTsdb);
    AW_FUN_Log(LOG_STEP, "g_command=%s\n", g_command);
    int32_t TimeValue2 = 0;
    TimeValue2 = GetViewFieldResultValue(g_command);
    AW_FUN_Log(LOG_STEP, "TimeValue2=%d\n", TimeValue2);
    EXPECT_LE(TimeValue2, 100);

    // 查询TS_TBL_OPER_STATIS
    snprintf(g_command, 1024,
        "gmsysview -sql \"select * from 'V\\$TS_TBL_OPER_STATIS' where TABLE_NAME='testdb100' and DML_SUCCESS_CNT=1\" "
        "-s %s | grep time "
        "|awk -F ':' '{print $2}'",
        g_connServerTsdb);
    AW_FUN_Log(LOG_STEP, "g_command=%s\n", g_command);
    int32_t TimeValue3 = 0;
    TimeValue3 = GetViewFieldResultValue(g_command);
    AW_FUN_Log(LOG_STEP, "TimeValue3=%d\n", TimeValue3);
    EXPECT_LE(TimeValue3, 100);

    // 查询TS_TBL_PROPS
    snprintf(g_command, 1024,
        "gmsysview -sql \"select * from 'V\\$TS_TBL_PROPS' where TABLE_NAME='testdb100'and INTERVAL=1 \" -s %s | grep "
        "time "
        "|awk -F ':' '{print $2}'",
        g_connServerTsdb);
    AW_FUN_Log(LOG_STEP, "g_command=%s\n", g_command);
    int32_t TimeValue4 = 0;
    TimeValue4 = GetViewFieldResultValue(g_command);
    AW_FUN_Log(LOG_STEP, "TimeValue4=%d\n", TimeValue4);
    EXPECT_LE(TimeValue4, 100);

    for (int i = 0; i < tableNum; i++) {
        (void)sprintf(g_tableName, "testdb%d", i);
        if (i % 50 == 0) {
            AW_FUN_Log(LOG_STEP, "drop table num %d table_name=%s.\n", i, g_tableName);
        }
        (void)sprintf(sqlCmd, "drop table %s;", g_tableName);
        uint32_t cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    }

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(64),  id integer, worktime integer,vsysId TEXT) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)');",
        "testdb");

    ret = GmcExecDirect(g_stmt_sync, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
}

// 003、查询四张视图，过滤条件为 TABLE_NAME = '表名'或LOGIC_LABEL_NAME = '表名' OR 其他过滤条件
TEST_F(tsdb_query_capabilities_enhanced, Timing_085_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd, "drop table %s;", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    int tableNum = 200;
    int count = 10000;

    for (int i = 0; i < tableNum; i++) {
        (void)sprintf(g_tableName, "testdb%d", i);
        if (i % 50 == 0) {
            AW_FUN_Log(LOG_STEP, "create table num %d table_name=%s.\n", i, g_tableName);
        }

        (void)sprintf(sqlCmd, "drop table %s;", g_tableName);
        uint32_t cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);

        (void)snprintf(sqlCmd, MAX_CMD_SIZE,
            "create table %s(name char(64),  id integer, worktime integer,vsysId TEXT) with "
            "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)');",
            g_tableName);
        ret = GmcExecDirect(g_stmt_sync, sqlCmd, strlen(sqlCmd));
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

        int64_t ids[count];
        (void)memset_s(ids, count * sizeof(int64_t), 0, count * sizeof(int64_t));
        int64_t worktimes[count];
        (void)memset_s(worktimes, count * sizeof(int64_t), 0, count * sizeof(int64_t));
        char *names = (char *)malloc(count * 64);
        char nameSource[10][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
        char **vsysId = (char **)malloc(count * sizeof(char *));
        if (vsysId == NULL) {
            AW_FUN_Log(LOG_INFO, "Malloc unsucc.");
        }

        for (int i = 0; i < count; i++) {
            ids[i] = i + 1;
            worktimes[i] = 1695042000 + i;
            if (i % 10 == 0) {
                memcpy((names + i * 64), (char *)nameSource, 640);
            }
            vsysId[i] = (char *)malloc(TEXT_LEN * sizeof(char));
            (void)memset(vsysId[i], 0, TEXT_LEN);
        }

        GenerateRandomString(vsysId, count);

        uint32_t rowNum = count;
        int ret = GmcPrepareStmtByLabelName(g_stmt_sync, g_tableName, GMC_OPERATION_SQL_INSERT);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(g_stmt_sync, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, names, 64, NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(g_stmt_sync, 1, (GmcDataTypeE)DB_DATATYPE_INT64, ids, sizeof(ids[0]), NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(g_stmt_sync, 2, (GmcDataTypeE)DB_DATATYPE_INT64, worktimes, sizeof(worktimes[0]), NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(g_stmt_sync, 3, (GmcDataTypeE)DB_DATATYPE_STRING, vsysId, sizeof(char *), 0);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcSetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcExecute(g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        FreeHeapMemory(names);
        for (int i = 0; i < count; i++) {
            FreeHeapMemory(vsysId[i]);
        }
        FreeHeapMemory(vsysId);
    }

    // 查询STORAGE_DISK_USAGE
    snprintf(g_command, 1024,
        "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where TABLE_NAME='testdb100' or ROW_CNT=1000\" -s %s "
        "| "
        "grep time "
        "|awk -F ':' '{print $2}'",
        g_connServerTsdb);
    AW_FUN_Log(LOG_STEP, "g_command=%s\n", g_command);
    int32_t TimeValue1 = 0;
    TimeValue1 = GetViewFieldResultValue(g_command);
    AW_FUN_Log(LOG_STEP, "TimeValue1=%d\n", TimeValue1);
    EXPECT_LE(TimeValue1, 4000);

    // 查询PHY_TBL_DISK_USAGE
    snprintf(g_command, 1024,
        "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE' where TABLE_NAME='testdb100' or ROW_CNT=1000\" -s %s "
        "| "
        "grep time "
        "|awk -F ':' '{print $2}'",
        g_connServerTsdb);
    AW_FUN_Log(LOG_STEP, "g_command=%s\n", g_command);
    int32_t TimeValue2 = 0;
    TimeValue2 = GetViewFieldResultValue(g_command);
    AW_FUN_Log(LOG_STEP, "TimeValue2=%d\n", TimeValue2);
    EXPECT_LE(TimeValue2, 2000);

    // 查询TS_TBL_OPER_STATIS
    snprintf(g_command, 1024,
        "gmsysview -sql \"select * from 'V\\$TS_TBL_OPER_STATIS' where TABLE_NAME='testdb100' or DML_SUCCESS_CNT=1\" "
        "-s %s | grep time "
        "|awk -F ':' '{print $2}'",
        g_connServerTsdb);
    AW_FUN_Log(LOG_STEP, "g_command=%s\n", g_command);
    int32_t TimeValue3 = 0;
    TimeValue3 = GetViewFieldResultValue(g_command);
    AW_FUN_Log(LOG_STEP, "TimeValue3=%d\n", TimeValue3);
    EXPECT_LE(TimeValue3, 2000);

    // 查询TS_TBL_PROPS
    snprintf(g_command, 1024,
        "gmsysview -sql \"select * from 'V\\$TS_TBL_PROPS' where TABLE_NAME='testdb100'or INTERVAL=1 \" -s %s | grep "
        "time "
        "|awk -F ':' '{print $2}'",
        g_connServerTsdb);
    AW_FUN_Log(LOG_STEP, "g_command=%s\n", g_command);
    int32_t TimeValue4 = 0;
    TimeValue4 = GetViewFieldResultValue(g_command);
    AW_FUN_Log(LOG_STEP, "TimeValue4=%d\n", TimeValue4);
    EXPECT_LE(TimeValue4, 2000);

    for (int i = 0; i < tableNum; i++) {
        (void)sprintf(g_tableName, "testdb%d", i);
        if (i % 50 == 0) {
            AW_FUN_Log(LOG_STEP, "drop table num %d table_name=%s.\n", i, g_tableName);
        }
        (void)sprintf(sqlCmd, "drop table %s;", g_tableName);
        uint32_t cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    }

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(64),  id integer, worktime integer,vsysId TEXT) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)');",
        "testdb");

    ret = GmcExecDirect(g_stmt_sync, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
}

// 004、查询V$COM_TABLE_MEM_SUMMARY视图，过滤条件为TABLE_NAME =  '表名'
TEST_F(tsdb_query_capabilities_enhanced, Timing_085_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd, "drop table %s;", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    int tableNum = 200;
    int count = 10000;

    for (int i = 0; i < tableNum; i++) {
        (void)sprintf(g_tableName, "testdb%d", i);
        if (i % 50 == 0) {
            AW_FUN_Log(LOG_STEP, "create table num %d table_name=%s.\n", i, g_tableName);
        }

        (void)sprintf(sqlCmd, "drop table %s;", g_tableName);
        uint32_t cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);

        (void)snprintf(sqlCmd, MAX_CMD_SIZE,
            "create table %s(name char(64),  id integer, worktime integer,vsysId TEXT) with "
            "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)');",
            g_tableName);
        ret = GmcExecDirect(g_stmt_sync, sqlCmd, strlen(sqlCmd));
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

        int64_t ids[count];
        (void)memset_s(ids, count * sizeof(int64_t), 0, count * sizeof(int64_t));
        int64_t worktimes[count];
        (void)memset_s(worktimes, count * sizeof(int64_t), 0, count * sizeof(int64_t));
        char *names = (char *)malloc(count * 64);
        char nameSource[10][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
        char **vsysId = (char **)malloc(count * sizeof(char *));
        if (vsysId == NULL) {
            AW_FUN_Log(LOG_INFO, "Malloc unsucc.");
        }

        for (int i = 0; i < count; i++) {
            ids[i] = i + 1;
            worktimes[i] = 1695042000 + i;
            if (i % 10 == 0) {
                memcpy((names + i * 64), (char *)nameSource, 640);
            }
            vsysId[i] = (char *)malloc(TEXT_LEN * sizeof(char));
            (void)memset(vsysId[i], 0, TEXT_LEN);
        }

        GenerateRandomString(vsysId, count);

        uint32_t rowNum = count;
        int ret = GmcPrepareStmtByLabelName(g_stmt_sync, g_tableName, GMC_OPERATION_SQL_INSERT);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(g_stmt_sync, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, names, 64, NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(g_stmt_sync, 1, (GmcDataTypeE)DB_DATATYPE_INT64, ids, sizeof(ids[0]), NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(g_stmt_sync, 2, (GmcDataTypeE)DB_DATATYPE_INT64, worktimes, sizeof(worktimes[0]), NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(g_stmt_sync, 3, (GmcDataTypeE)DB_DATATYPE_STRING, vsysId, sizeof(char *), 0);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcSetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcExecute(g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        FreeHeapMemory(names);
        for (int i = 0; i < count; i++) {
            FreeHeapMemory(vsysId[i]);
        }
        FreeHeapMemory(vsysId);
    }

    // 查询
    snprintf(g_command, 1024,
        "gmsysview -sql \"select * from 'V\\$COM_TABLE_MEM_SUMMARY' where TABLE_NAME='testdb100' \" -s %s | grep time "
        "|awk -F ':' '{print $2}'",
        g_connServerTsdb);
    AW_FUN_Log(LOG_STEP, "g_command=%s\n", g_command);
    int32_t TimeValue = 0;
    TimeValue = GetViewFieldResultValue(g_command);
    AW_FUN_Log(LOG_STEP, "TimeValue=%d\n", TimeValue);
    EXPECT_LE(TimeValue, 100);

    for (int i = 0; i < tableNum; i++) {
        (void)sprintf(g_tableName, "testdb%d", i);
        if (i % 50 == 0) {
            AW_FUN_Log(LOG_STEP, "drop table num %d table_name=%s.\n", i, g_tableName);
        }
        (void)sprintf(sqlCmd, "drop table %s;", g_tableName);
        uint32_t cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    }

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(64),  id integer, worktime integer,vsysId TEXT) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)');",
        "testdb");

    ret = GmcExecDirect(g_stmt_sync, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
}

// 005、单表多分区，通过gmsysview  -analyze分析查询指定分区的执行信息
TEST_F(tsdb_query_capabilities_enhanced, Timing_085_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = WriteDataDualPartition(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 查询
    snprintf(g_command, 1024,
        "gmsysview -analyze \"select * from 'V\\$PHY_TBL_DISK_USAGE' where TABLE_NAME='testdb100' and ROW_CNT=3660\" "
        "-s %s "
        "| "
        "grep time "
        "|awk -F ':' '{print $2}'",
        g_connServerTsdb);
    AW_FUN_Log(LOG_STEP, "g_command=%s\n", g_command);
    int32_t TimeValue = 0;
    TimeValue = GetViewFieldResultValue(g_command);
    AW_FUN_Log(LOG_STEP, "TimeValue=%d\n", TimeValue);
    EXPECT_LE(TimeValue, 50);
}

// 006、单表多分区，通过gmsysview  -analyze分析查询全表的执行信息
TEST_F(tsdb_query_capabilities_enhanced, Timing_085_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = WriteDataDualPartition(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 查询
    snprintf(g_command, 1024,
        "gmsysview -analyze \"select * from testdb\" -s %s "
        "| "
        "grep time "
        "|awk -F ':' '{print $2}'",
        g_connServerTsdb);
    AW_FUN_Log(LOG_STEP, "g_command=%s\n", g_command);
    int32_t TimeValue = 0;
    TimeValue = GetViewFieldResultValue(g_command);
    AW_FUN_Log(LOG_STEP, "TimeValue=%d\n", TimeValue);
    EXPECT_LE(TimeValue, 50);
}

// 007、单表多分区，通过gmsysview  -analyze分析查询全表叠加排序的执行信息
TEST_F(tsdb_query_capabilities_enhanced, Timing_085_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = WriteDataDualPartition(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 查询
    snprintf(g_command, 1024,
        "gmsysview -analyze \"select * from testdb group by id order by id \" -s %s "
        "| "
        "grep time "
        "|awk -F ':' '{print $2}'",
        g_connServerTsdb);
    AW_FUN_Log(LOG_STEP, "g_command=%s\n", g_command);
    int32_t TimeValue = 0;
    TimeValue = GetViewFieldResultValue(g_command);
    AW_FUN_Log(LOG_STEP, "TimeValue=%d\n", TimeValue);
    EXPECT_LE(TimeValue, 200);
}

// 008、单表多分区，通过gmsysview  -analyze分析查询全表叠加排序和limit的执行信息
TEST_F(tsdb_query_capabilities_enhanced, Timing_085_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = WriteDataDualPartition(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 查询
    snprintf(g_command, 1024,
        "gmsysview -analyze \"select * from testdb group by id order by id limit 5\" -s %s "
        "| "
        "grep time "
        "|awk -F ':' '{print $2}'",
        g_connServerTsdb);
    AW_FUN_Log(LOG_STEP, "g_command=%s\n", g_command);
    int32_t TimeValue = 0;
    TimeValue = GetViewFieldResultValue(g_command);
    AW_FUN_Log(LOG_STEP, "TimeValue=%d\n", TimeValue);
    EXPECT_LE(TimeValue, 150);
}

// 009、单表多分区，通过gmsysview  -analyze分析查询全表叠加排序、limit、offset的执行信息
TEST_F(tsdb_query_capabilities_enhanced, Timing_085_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = WriteDataDualPartition(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 查询
    snprintf(g_command, 1024,
        "gmsysview -analyze \"select * from testdb group by id order by id limit 5 offset 2\" -s %s "
        "| "
        "grep time "
        "|awk -F ':' '{print $2}'",
        g_connServerTsdb);
    AW_FUN_Log(LOG_STEP, "g_command=%s\n", g_command);
    int32_t TimeValue = 0;
    TimeValue = GetViewFieldResultValue(g_command);
    AW_FUN_Log(LOG_STEP, "TimeValue=%d\n", TimeValue);
    EXPECT_LE(TimeValue, 150);
}

// 010、单表多分区，通过gmsysview  -analyze分析查询select count(*) from  table的执行信息
TEST_F(tsdb_query_capabilities_enhanced, Timing_085_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = WriteDataDualPartition(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 查询
    snprintf(g_command, 1024,
        "gmsysview -analyze \"select count(*) from testdb\" -s %s "
        "| "
        "grep time "
        "|awk -F ':' '{print $2}'",
        g_connServerTsdb);
    AW_FUN_Log(LOG_STEP, "g_command=%s\n", g_command);
    int32_t TimeValue = 0;
    TimeValue = GetViewFieldResultValue(g_command);
    AW_FUN_Log(LOG_STEP, "TimeValue=%d\n", TimeValue);
    EXPECT_LE(TimeValue, 150);
}

// 011、单表多分区，通过gmsysview  -analyze分析max.min,length聚合函数、group by、order by 、limit offset的执行信息
TEST_F(tsdb_query_capabilities_enhanced, Timing_085_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = WriteDataDualPartition(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 查询
    snprintf(g_command, 1024,
        "gmsysview -analyze \"select max(id),min(worktime),length(vsysId) from testdb group by id order by id limit 5 "
        "offset 2\" -s %s "
        "| "
        "grep time "
        "|awk -F ':' '{print $2}'",
        g_connServerTsdb);
    AW_FUN_Log(LOG_STEP, "g_command=%s\n", g_command);
    int32_t TimeValue = 0;
    TimeValue = GetViewFieldResultValue(g_command);
    AW_FUN_Log(LOG_STEP, "TimeValue=%d\n", TimeValue);
    EXPECT_LE(TimeValue, 150);
}

// 012、单表多分区，通过gmsysview  -analyze分析first、last、sum、avg聚合函数、group by、order by 、limit
// offset的执行信息
TEST_F(tsdb_query_capabilities_enhanced, Timing_085_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = WriteDataDualPartition(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 查询
    snprintf(g_command, 1024,
        "gmsysview -analyze \"select first(id),last(worktime),sum(1)  from testdb group by id order by id limit 5 "
        "offset 2\" -s %s "
        "| "
        "grep time "
        "|awk -F ':' '{print $2}'",
        g_connServerTsdb);
    AW_FUN_Log(LOG_STEP, "g_command=%s\n", g_command);
    int32_t TimeValue = 0;
    TimeValue = GetViewFieldResultValue(g_command);
    AW_FUN_Log(LOG_STEP, "TimeValue=%d\n", TimeValue);
    EXPECT_LE(TimeValue, 100);
}

// 013、单表多分区，通过gmsysview  -analyze分析高基数分组时的执行信息
TEST_F(tsdb_query_capabilities_enhanced, Timing_085_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = WriteDataDualPartition(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 查询
    snprintf(g_command, 1024,
        "gmsysview -analyze \"select id from testdb group by id order by id\" -s %s "
        "| "
        "grep time "
        "|awk -F ':' '{print $2}'",
        g_connServerTsdb);
    AW_FUN_Log(LOG_STEP, "g_command=%s\n", g_command);
    int32_t TimeValue = 0;
    TimeValue = GetViewFieldResultValue(g_command);
    AW_FUN_Log(LOG_STEP, "TimeValue=%d\n", TimeValue);
    EXPECT_LE(TimeValue, 100);
}

// 014、单表多分区，通过gmsysview  -analyze分析高基数分组叠加排序的执行信息
TEST_F(tsdb_query_capabilities_enhanced, Timing_085_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = WriteDataDualPartition(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 查询
    snprintf(g_command, 1024,
        "gmsysview -analyze \"select id from testdb group by id order by id group by id\" -s %s "
        "| "
        "grep time "
        "|awk -F ':' '{print $2}'",
        g_connServerTsdb);
    AW_FUN_Log(LOG_STEP, "g_command=%s\n", g_command);
    int32_t TimeValue = 0;
    TimeValue = GetViewFieldResultValue(g_command);
    AW_FUN_Log(LOG_STEP, "TimeValue=%d\n", TimeValue);
    EXPECT_LE(TimeValue, 50);
}

// 015、单表多分区，通过gmsysview  -analyze分析高基数分组叠加排序，limit、offset执行信息
TEST_F(tsdb_query_capabilities_enhanced, Timing_085_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = WriteDataDualPartition(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 查询
    snprintf(g_command, 1024,
        "gmsysview -analyze \"select id from testdb group by id order by id limit 5 offset 3\" -s %s "
        "| "
        "grep time "
        "|awk -F ':' '{print $2}'",
        g_connServerTsdb);
    AW_FUN_Log(LOG_STEP, "g_command=%s\n", g_command);
    int32_t TimeValue = 0;
    TimeValue = GetViewFieldResultValue(g_command);
    AW_FUN_Log(LOG_STEP, "TimeValue=%d\n", TimeValue);
    EXPECT_LE(TimeValue, 50);
}

// 016、单表多分区，通过gmsysview  -analyze分析copy to中select带a+b列，where a+b > 1的执行信息
TEST_F(tsdb_query_capabilities_enhanced, Timing_085_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = WriteDataDualPartition(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // COPY TO
    char g_command[256] = {0};
    (void)sprintf(g_command, "COPY ( select * from testdb) TO'./data.csv';");

    char gmsysview[] = "gmsysview";
    char q[] = "-explain";
    char s[] = "-s";
    char *argv[5] = {gmsysview, q, g_command, s, g_connServerTsdb};
    ret = GmcSysview(5, argv, TestPrintfDefault);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    AW_FUN_Log(LOG_STEP, "g_command=%s\n", g_command);
    int32_t TimeValue = 0;
    TimeValue = GetViewFieldResultValue(g_command);
    AW_FUN_Log(LOG_STEP, "TimeValue=%d\n", TimeValue);
    EXPECT_LE(TimeValue, 50);
}

// 017、单表多分区，通过gmsysview  -analyze分析insert to中select带a+b列，where a+b > 1的执行信息
TEST_F(tsdb_query_capabilities_enhanced, Timing_085_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd, "drop table %s;", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    int tableNum = 2;
    int count = 10000;

    for (int i = 0; i < tableNum; i++) {
        (void)sprintf(g_tableName, "testdb%d", i);
        if (i % 50 == 0) {
            AW_FUN_Log(LOG_STEP, "create table num %d table_name=%s.\n", i, g_tableName);
        }

        (void)sprintf(sqlCmd, "drop table %s;", g_tableName);
        uint32_t cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);

        (void)snprintf(sqlCmd, MAX_CMD_SIZE,
            "create table %s(name char(64),  id integer, worktime integer,vsysId TEXT) with "
            "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)');",
            g_tableName);
        ret = GmcExecDirect(g_stmt_sync, sqlCmd, strlen(sqlCmd));
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

        int64_t ids[count];
        (void)memset_s(ids, count * sizeof(int64_t), 0, count * sizeof(int64_t));
        int64_t worktimes[count];
        (void)memset_s(worktimes, count * sizeof(int64_t), 0, count * sizeof(int64_t));
        char *names = (char *)malloc(count * 64);
        char nameSource[10][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
        char **vsysId = (char **)malloc(count * sizeof(char *));
        if (vsysId == NULL) {
            AW_FUN_Log(LOG_INFO, "Malloc unsucc.");
        }

        for (int i = 0; i < count; i++) {
            ids[i] = i + 1;
            worktimes[i] = 1695042000 + i;
            if (i % 10 == 0) {
                memcpy((names + i * 64), (char *)nameSource, 640);
            }
            vsysId[i] = (char *)malloc(TEXT_LEN * sizeof(char));
            (void)memset(vsysId[i], 0, TEXT_LEN);
        }

        GenerateRandomString(vsysId, count);

        uint32_t rowNum = count;
        int ret = GmcPrepareStmtByLabelName(g_stmt_sync, g_tableName, GMC_OPERATION_SQL_INSERT);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(g_stmt_sync, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, names, 64, NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(g_stmt_sync, 1, (GmcDataTypeE)DB_DATATYPE_INT64, ids, sizeof(ids[0]), NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(g_stmt_sync, 2, (GmcDataTypeE)DB_DATATYPE_INT64, worktimes, sizeof(worktimes[0]), NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(g_stmt_sync, 3, (GmcDataTypeE)DB_DATATYPE_STRING, vsysId, sizeof(char *), 0);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcSetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcExecute(g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        FreeHeapMemory(names);
        for (int i = 0; i < count; i++) {
            FreeHeapMemory(vsysId[i]);
        }
        FreeHeapMemory(vsysId);
    }

    // insert_into
    (void)sprintf(g_command, "insert into %s select * from %s;", "testdb1", "testdb0");

    char gmsysview[] = "gmsysview";
    char q[] = "-explain";
    char s[] = "-s";
    char *argv[5] = {gmsysview, q, g_command, s, g_connServerTsdb};
    ret = GmcSysview(5, argv, TestPrintfDefault);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    AW_FUN_Log(LOG_STEP, "g_command=%s\n", g_command);
    int32_t TimeValue = 0;
    TimeValue = GetViewFieldResultValue(g_command);
    AW_FUN_Log(LOG_STEP, "TimeValue=%d\n", TimeValue);
    EXPECT_LE(TimeValue, 50);

    for (int i = 0; i < tableNum; i++) {
        (void)sprintf(g_tableName, "testdb%d", i);
        if (i % 50 == 0) {
            AW_FUN_Log(LOG_STEP, "drop table num %d table_name=%s.\n", i, g_tableName);
        }
        (void)sprintf(sqlCmd, "drop table %s;", g_tableName);
        uint32_t cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    }

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(64),  id integer, worktime integer,vsysId TEXT) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)');",
        "testdb");

    ret = GmcExecDirect(g_stmt_sync, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
}

// 018、创建内存表，通过gmsysview  -analyze分析select带a+b列 from 内存表，where a+b > 1 group by其他列与a列b列order by
// sum(a+b) limit 1000 offset 20
TEST_F(tsdb_query_capabilities_enhanced, Timing_085_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd, "drop table %s;", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    int tableNum = 1;
    int count = 10000;

    for (int i = 0; i < tableNum; i++) {
        (void)sprintf(g_tableName, "testdb%d", i);
        if (i % 50 == 0) {
            AW_FUN_Log(LOG_STEP, "create table num %d table_name=%s.\n", i, g_tableName);
        }

        (void)sprintf(sqlCmd, "drop table %s;", g_tableName);
        uint32_t cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);

        (void)snprintf(sqlCmd, MAX_CMD_SIZE,
            "create table %s(name char(64),  id integer, worktime integer,vsysId TEXT) with "
            "(engine = 'memory', max_size =50000,time_col = 'worktime', interval = '1 hour', compression = "
            "'fast(rapidlz)');",
            g_tableName);
        ret = GmcExecDirect(g_stmt_sync, sqlCmd, strlen(sqlCmd));
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

        int64_t ids[count];
        (void)memset_s(ids, count * sizeof(int64_t), 0, count * sizeof(int64_t));
        int64_t worktimes[count];
        (void)memset_s(worktimes, count * sizeof(int64_t), 0, count * sizeof(int64_t));
        char *names = (char *)malloc(count * 64);
        char nameSource[10][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
        char **vsysId = (char **)malloc(count * sizeof(char *));
        if (vsysId == NULL) {
            AW_FUN_Log(LOG_INFO, "Malloc unsucc.");
        }

        for (int i = 0; i < count; i++) {
            ids[i] = i + 1;
            worktimes[i] = 1695042000 + i;
            if (i % 10 == 0) {
                memcpy((names + i * 64), (char *)nameSource, 640);
            }
            vsysId[i] = (char *)malloc(TEXT_LEN * sizeof(char));
            (void)memset(vsysId[i], 0, TEXT_LEN);
        }

        GenerateRandomString(vsysId, count);

        uint32_t rowNum = count;
        int ret = GmcPrepareStmtByLabelName(g_stmt_sync, g_tableName, GMC_OPERATION_SQL_INSERT);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(g_stmt_sync, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, names, 64, NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(g_stmt_sync, 1, (GmcDataTypeE)DB_DATATYPE_INT64, ids, sizeof(ids[0]), NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(g_stmt_sync, 2, (GmcDataTypeE)DB_DATATYPE_INT64, worktimes, sizeof(worktimes[0]), NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(g_stmt_sync, 3, (GmcDataTypeE)DB_DATATYPE_STRING, vsysId, sizeof(char *), 0);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcSetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcExecute(g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        FreeHeapMemory(names);
        for (int i = 0; i < count; i++) {
            FreeHeapMemory(vsysId[i]);
        }
        FreeHeapMemory(vsysId);
    }

    // 查询
    snprintf(g_command, 1024,
        "gmsysview -analyze \"select id+worktime from testdb where id+worktime >1 group by id order by "
        "sum(id+worktime) limit 1000 offset 20\" -s %s "
        "| "
        "grep time "
        "|awk -F ':' '{print $2}'",
        g_connServerTsdb);
    AW_FUN_Log(LOG_STEP, "g_command=%s\n", g_command);
    int32_t TimeValue = 0;
    TimeValue = GetViewFieldResultValue(g_command);
    AW_FUN_Log(LOG_STEP, "TimeValue=%d\n", TimeValue);
    EXPECT_LE(TimeValue, 50);

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(64),  id integer, worktime integer,vsysId TEXT) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)');",
        "testdb");

    ret = GmcExecDirect(g_stmt_sync, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
}

// 019、通过gmsysview  -analyze分析 alter  disk_limit的执行信息
TEST_F(tsdb_query_capabilities_enhanced, Timing_085_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = WriteDataDualPartition(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 在线修改disk_limit
    char g_command[256] = {0};
    (void)sprintf(g_command, "alter table testdb set (disk_limit = '4 MB');");

    char gmsysview[] = "gmsysview";
    char q[] = "-explain";
    char s[] = "-s";
    char *argv[5] = {gmsysview, q, g_command, s, g_connServerTsdb};
    ret = GmcSysview(5, argv, TestPrintfDefault);
    EXPECT_EQ(1003000, ret);
}

// 020、通过gmsysview  -analyze分析 alter  ttl的执行信息
TEST_F(tsdb_query_capabilities_enhanced, Timing_085_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = WriteDataDualPartition(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 在线修改ttl
    char sqlCmd[256] = {0};
    (void)sprintf(g_command, "alter table testdb set (ttl = '2 H');");

    char gmsysview[] = "gmsysview";
    char q[] = "-explain";
    char s[] = "-s";
    char *argv[5] = {gmsysview, q, g_command, s, g_connServerTsdb};
    ret = GmcSysview(5, argv, TestPrintfDefault);
    EXPECT_EQ(1003000, ret);
}

// 021、通过gmsysview  -analyze分析 select  tsdb_aging的执行信息
TEST_F(tsdb_query_capabilities_enhanced, Timing_085_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = WriteDataDualPartition(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // tsdb_aging
    char g_command[256] = {0};
    (void)sprintf(g_command, "SELECT tsdb_aging('testdb');");

    char gmsysview[] = "gmsysview";
    char q[] = "-explain";
    char s[] = "-s";
    char *argv[5] = {gmsysview, q, g_command, s, g_connServerTsdb};
    ret = GmcSysview(5, argv, TestPrintfDefault);
    EXPECT_EQ(1003000, ret);
}

// 022、单表多分区，通过gmsysview  -analyze分析select 表名 as 别名  叠加 group by  order by  limit  offset
TEST_F(tsdb_query_capabilities_enhanced, Timing_085_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = WriteDataDualPartition(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 查询
    snprintf(g_command, 1024,
        "gmsysview -analyze \"select testdb.id as ids from testdb group by id order by id limit 5 offset 3\" -s %s "
        "| "
        "grep time "
        "|awk -F ':' '{print $2}'",
        g_connServerTsdb);
    AW_FUN_Log(LOG_STEP, "g_command=%s\n", g_command);
    int32_t TimeValue = 0;
    TimeValue = GetViewFieldResultValue(g_command);
    AW_FUN_Log(LOG_STEP, "TimeValue=%d\n", TimeValue);
    EXPECT_LE(TimeValue, 50);
}

// 023、GmcSysview接口中使用gmsysview  -analyze分析高基数分组时的执行信息
TEST_F(tsdb_query_capabilities_enhanced, Timing_085_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = WriteDataDualPartition(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 查询
    char g_command[256] = {0};
    (void)sprintf(g_command, "select id from testdb group by id;");

    char gmsysview[] = "gmsysview";
    char q[] = "-explain";
    char s[] = "-s";
    char *argv[5] = {gmsysview, q, g_command, s, g_connServerTsdb};
    ret = GmcSysview(5, argv, TestPrintfDefault);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "g_command=%s\n", g_command);
    int32_t TimeValue = 0;
    TimeValue = GetViewFieldResultValue(g_command);
    AW_FUN_Log(LOG_STEP, "TimeValue=%d\n", TimeValue);
    EXPECT_LE(TimeValue, 50);
}

// 024、GmcSysview接口中使用gmsysview  -analyze分析max.min,length聚合函数、group by、order by 、limit offset的执行信息
TEST_F(tsdb_query_capabilities_enhanced, Timing_085_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = WriteDataDualPartition(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 查询
    char g_command[256] = {0};
    (void)sprintf(
        g_command, "select max(id),min(worktime),length(vsysId) from testdb group by id order by id limit 5 offset 3;");

    char gmsysview[] = "gmsysview";
    char q[] = "-explain";
    char s[] = "-s";
    char *argv[5] = {gmsysview, q, g_command, s, g_connServerTsdb};
    ret = GmcSysview(5, argv, TestPrintfDefault);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "g_command=%s\n", g_command);
    int32_t TimeValue = 0;
    TimeValue = GetViewFieldResultValue(g_command);
    AW_FUN_Log(LOG_STEP, "TimeValue=%d\n", TimeValue);
    EXPECT_LE(TimeValue, 50);
}

// 025、GmcSysview接口中使用gmsysview  -analyze分析first、last、sum、avg聚合函数、group by、order by 、limit
// offset的执行信息
TEST_F(tsdb_query_capabilities_enhanced, Timing_085_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = WriteDataDualPartition(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 查询
    char g_command[256] = {0};
    (void)sprintf(
        g_command, "select first(id),last(worktime),sum(1)  from testdb group by id order by id limit 5 offset 3;");

    char gmsysview[] = "gmsysview";
    char q[] = "-explain";
    char s[] = "-s";
    char *argv[5] = {gmsysview, q, g_command, s, g_connServerTsdb};
    ret = GmcSysview(5, argv, TestPrintfDefault);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "g_command=%s\n", g_command);
    int32_t TimeValue = 0;
    TimeValue = GetViewFieldResultValue(g_command);
    AW_FUN_Log(LOG_STEP, "TimeValue=%d\n", TimeValue);
    EXPECT_LE(TimeValue, 50);
}

// 026、gmsysview -sql 查询全表
TEST_F(tsdb_query_capabilities_enhanced, Timing_085_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = WriteDataDualPartition(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 查询
    snprintf(g_command, 1024,
        "gmsysview -sql \"select * from testdb\" -s %s "
        "| "
        "grep time "
        "|awk -F ':' '{print $2}'",
        g_connServerTsdb);
    AW_FUN_Log(LOG_STEP, "g_command=%s\n", g_command);
    int32_t TimeValue = 0;
    TimeValue = GetViewFieldResultValue(g_command);
    AW_FUN_Log(LOG_STEP, "TimeValue=%d\n", TimeValue);
    EXPECT_LE(TimeValue, 2000);
}

// 027、gmsysview -sql 查询高基数语法
TEST_F(tsdb_query_capabilities_enhanced, Timing_085_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = WriteDataDualPartition(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 查询
    snprintf(g_command, 1024,
        "gmsysview -sql \"select id from testdb group by id\" -s %s "
        "| "
        "grep time "
        "|awk -F ':' '{print $2}'",
        g_connServerTsdb);
    AW_FUN_Log(LOG_STEP, "g_command=%s\n", g_command);
    int32_t TimeValue = 0;
    TimeValue = GetViewFieldResultValue(g_command);
    AW_FUN_Log(LOG_STEP, "TimeValue=%d\n", TimeValue);
    EXPECT_LE(TimeValue, 2000);
}

// 028、gmsysview -sql 查询语句包含聚合函数、group by、order by 、limit offset
TEST_F(tsdb_query_capabilities_enhanced, Timing_085_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = WriteDataDualPartition(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 查询
    snprintf(g_command, 1024,
        "gmsysview -sql \"select id from testdb group by id order by id limit 5 offset 3\" -s %s "
        "| "
        "grep time "
        "|awk -F ':' '{print $2}'",
        g_connServerTsdb);
    AW_FUN_Log(LOG_STEP, "g_command=%s\n", g_command);
    int32_t TimeValue = 0;
    TimeValue = GetViewFieldResultValue(g_command);
    AW_FUN_Log(LOG_STEP, "TimeValue=%d\n", TimeValue);
    EXPECT_LE(TimeValue, 50);
}

// 029、使用GmcExecDirect接口，查询逻辑表，对char、text类型字段过滤英文单引号
TEST_F(tsdb_query_capabilities_enhanced, Timing_085_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int64_t count = 10000;
    int64_t ids[count];
    (void)memset_s(ids, count * sizeof(int64_t), 0, count * sizeof(int64_t));
    int64_t worktimes[count];
    (void)memset_s(worktimes, count * sizeof(int64_t), 0, count * sizeof(int64_t));
    char *names = (char *)malloc(count * 64);
    char nameSource[10][64] = {"dav'id", "nut'", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
    char **vsysId = (char **)malloc(count * sizeof(char *));
    if (vsysId == NULL) {
        AW_FUN_Log(LOG_INFO, "Malloc unsucc.");
    }

    for (int i = 0; i < count; i++) {
        ids[i] = i + 1;
        worktimes[i] = 1695042000 + i;
        if (i % 10 == 0) {
            memcpy((names + i * 64), (char *)nameSource, 640);
        }
        vsysId[i] = (char *)malloc(TEXT_LEN * sizeof(char));
        (void)memset(vsysId[i], 0, TEXT_LEN);
    }

    GenerateRandomString(vsysId, count);

    uint32_t rowNum = count;
    int ret = GmcPrepareStmtByLabelName(g_stmt_sync, g_tableName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(g_stmt_sync, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, names, 64, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(g_stmt_sync, 1, (GmcDataTypeE)DB_DATATYPE_INT64, ids, sizeof(ids[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(g_stmt_sync, 2, (GmcDataTypeE)DB_DATATYPE_INT64, worktimes, sizeof(worktimes[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(g_stmt_sync, 3, (GmcDataTypeE)DB_DATATYPE_STRING, vsysId, sizeof(char *), 0);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    FreeHeapMemory(names);
    for (int i = 0; i < count; i++) {
        FreeHeapMemory(vsysId[i]);
    }
    FreeHeapMemory(vsysId);

    // 查询
    const char *queryCommand = "select * from testdb  where  name='nut''' or name='dav''id';";
    ret = GmcExecDirect(g_stmt_sync, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    AW_FUN_Log(LOG_STEP, "g_command=%s\n", queryCommand);
    int32_t TimeValue = 0;
    TimeValue = GetViewFieldResultValue(queryCommand);
    AW_FUN_Log(LOG_STEP, "TimeValue=%d\n", TimeValue);
    EXPECT_LE(TimeValue, 50);
}

// 030、使用gmsysview -sql，查询逻辑表，对char、text类型字段过滤英文单引号
TEST_F(tsdb_query_capabilities_enhanced, Timing_085_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int64_t count = 10000;
    int64_t ids[count];
    (void)memset_s(ids, count * sizeof(int64_t), 0, count * sizeof(int64_t));
    int64_t worktimes[count];
    (void)memset_s(worktimes, count * sizeof(int64_t), 0, count * sizeof(int64_t));
    char *names = (char *)malloc(count * 64);
    char nameSource[10][64] = {"dav'id", "nut'", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
    char **vsysId = (char **)malloc(count * sizeof(char *));
    if (vsysId == NULL) {
        AW_FUN_Log(LOG_INFO, "Malloc unsucc.");
    }

    for (int i = 0; i < count; i++) {
        ids[i] = i + 1;
        worktimes[i] = 1695042000 + i;
        if (i % 10 == 0) {
            memcpy((names + i * 64), (char *)nameSource, 640);
        }
        vsysId[i] = (char *)malloc(TEXT_LEN * sizeof(char));
        (void)memset(vsysId[i], 0, TEXT_LEN);
    }

    GenerateRandomString(vsysId, count);

    uint32_t rowNum = count;
    int ret = GmcPrepareStmtByLabelName(g_stmt_sync, g_tableName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(g_stmt_sync, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, names, 64, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(g_stmt_sync, 1, (GmcDataTypeE)DB_DATATYPE_INT64, ids, sizeof(ids[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(g_stmt_sync, 2, (GmcDataTypeE)DB_DATATYPE_INT64, worktimes, sizeof(worktimes[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(g_stmt_sync, 3, (GmcDataTypeE)DB_DATATYPE_STRING, vsysId, sizeof(char *), 0);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    FreeHeapMemory(names);
    for (int i = 0; i < count; i++) {
        FreeHeapMemory(vsysId[i]);
    }
    FreeHeapMemory(vsysId);

    // 查询
    snprintf(g_command, 1024,
        "gmsysview -sql \"select *  from testdb where  name='nut''' or name='dav''id'\" -s %s "
        "| "
        "grep time "
        "|awk -F ':' '{print $2}'",
        g_connServerTsdb);
    AW_FUN_Log(LOG_STEP, "g_command=%s\n", g_command);
    int32_t TimeValue = 0;
    TimeValue = GetViewFieldResultValue(g_command);
    AW_FUN_Log(LOG_STEP, "TimeValue=%d\n", TimeValue);
    EXPECT_LE(TimeValue, 50);

    // 查询
    snprintf(g_command, 1024,
        "gmsysview -analyze \"select *  from testdb where  name='nut''' or name='dav''id'\" -s %s "
        "| "
        "grep time "
        "|awk -F ':' '{print $2}'",
        g_connServerTsdb);
    AW_FUN_Log(LOG_STEP, "g_command=%s\n", g_command);
    TimeValue = 0;
    TimeValue = GetViewFieldResultValue(g_command);
    AW_FUN_Log(LOG_STEP, "TimeValue=%d\n", TimeValue);
    EXPECT_LE(TimeValue, 50);
}

// 031、多表多分区使用gmsysview -sql查询视图，逻辑条件有全 and 和包含or， 过滤出的结果一样，对比两者查询时间
TEST_F(tsdb_query_capabilities_enhanced, Timing_085_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int startTime = 1695042000;
    int ret = 0;
    int createTableNum = 10;
    ret = WriteDataDualPartition(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    BatchCreateTable(g_stmt_sync, createTableNum);
    char tableName[20] = {0};
    for (int i = 0; i < createTableNum; i++) {
        (void)sprintf(tableName, "testdb%d", i);
        ret = WriteDataDualPartitionByName(g_stmt_sync, tableName);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    }
    
    // 查询
    snprintf(g_command, 1024,
        "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE' where TABLE_NAME='testdb' or ROW_CNT > 0\" -s %s "
        "| grep time |awk -F ':' '{print $2}'",
        g_connServerTsdb);
    system(g_command);
    AW_FUN_Log(LOG_STEP, "g_command=%s\n", g_command);
    int32_t TimeValue2 = 0;
    TimeValue2 = GetViewFieldResultValue(g_command);
    AW_FUN_Log(LOG_STEP, "TimeValue2=%d\n", TimeValue2);

    // 查询
    snprintf(g_command, 1024,
        "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE' where TABLE_NAME='testdb' and ROW_CNT > 0\" -s %s "
        "| grep time |awk -F ':' '{print $2}'",
        g_connServerTsdb);
    system(g_command);
    AW_FUN_Log(LOG_STEP, "g_command=%s\n", g_command);
    int32_t TimeValue1 = 0;
    TimeValue1 = GetViewFieldResultValue(g_command);
    AW_FUN_Log(LOG_STEP, "TimeValue1=%d\n", TimeValue1);

    EXPECT_LE(TimeValue1, TimeValue2);
    ret = DropCmTable(g_stmt_sync, createTableNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
}

// 032、like查询，中英文混合单引号，下划线或%----ab'' ，模糊查询和等值查询各种混合单引号
TEST_F(tsdb_query_capabilities_enhanced, Timing_085_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int64_t count = 10000;
    int64_t ids[count];
    (void)memset_s(ids, count * sizeof(int64_t), 0, count * sizeof(int64_t));
    int64_t worktimes[count];
    (void)memset_s(worktimes, count * sizeof(int64_t), 0, count * sizeof(int64_t));
    char *names = (char *)malloc(count * 64);
    char nameSource[10][64] = {"dav'id", "nut'", "bob哈", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
    char **vsysId = (char **)malloc(count * sizeof(char *));
    if (vsysId == NULL) {
        AW_FUN_Log(LOG_INFO, "Malloc unsucc.");
    }

    for (int i = 0; i < count; i++) {
        ids[i] = i + 1;
        worktimes[i] = 1695042000 + i;
        if (i % 10 == 0) {
            memcpy((names + i * 64), (char *)nameSource, 640);
        }
        vsysId[i] = (char *)malloc(TEXT_LEN * sizeof(char));
        (void)memset(vsysId[i], 0, TEXT_LEN);
    }

    GenerateRandomString(vsysId, count);

    uint32_t rowNum = count;
    int ret = GmcPrepareStmtByLabelName(g_stmt_sync, g_tableName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(g_stmt_sync, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, names, 64, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(g_stmt_sync, 1, (GmcDataTypeE)DB_DATATYPE_INT64, ids, sizeof(ids[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(g_stmt_sync, 2, (GmcDataTypeE)DB_DATATYPE_INT64, worktimes, sizeof(worktimes[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(g_stmt_sync, 3, (GmcDataTypeE)DB_DATATYPE_STRING, vsysId, sizeof(char *), 0);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    FreeHeapMemory(names);
    for (int i = 0; i < count; i++) {
        FreeHeapMemory(vsysId[i]);
    }
    FreeHeapMemory(vsysId);

    // 查询
    snprintf(g_command, 1024,
        "gmsysview -sql \"select *  from testdb where name like 'nut''%%' or  name like 'dav''%%' or name='bob哈'\" -s "
        "%s "
        "| "
        "grep time "
        "|awk -F ':' '{print $2}'",
        g_connServerTsdb);
    AW_FUN_Log(LOG_STEP, "g_command=%s\n", g_command);
    int32_t TimeValue = 0;
    TimeValue = GetViewFieldResultValue(g_command);
    AW_FUN_Log(LOG_STEP, "TimeValue=%d\n", TimeValue);
    EXPECT_LE(TimeValue, 500);

    // 查询
    snprintf(g_command, 1024,
        "gmsysview -analyze \"select *  from testdb where name like 'nut''%%' or  name like 'dav''%%' or "
        "name='bob哈'\" "
        "-s %s "
        "| "
        "grep time "
        "|awk -F ':' '{print $2}'",
        g_connServerTsdb);
    AW_FUN_Log(LOG_STEP, "g_command=%s\n", g_command);
    TimeValue = 0;
    TimeValue = GetViewFieldResultValue(g_command);
    AW_FUN_Log(LOG_STEP, "TimeValue=%d\n", TimeValue);
    EXPECT_LE(TimeValue, 500);
}

// 033、逻辑表中，设置某列为table_name或LOGIC_LABEL_NAME，gmsysview -sql 查询语句中使用 TABLE_NAME =
// '表名'或LOGIC_LABEL_NAME = '表名'进行过滤
TEST_F(tsdb_query_capabilities_enhanced, Timing_085_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd, "drop table %s;", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    (void)snprintf(sqlCmd, MAX_CMD_SIZE,
        "create table %s(testdb char(64),  id integer, worktime integer,vsysId TEXT) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)');",
        g_tableName);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, strlen(sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    int64_t count = 10000;
    int64_t ids[count];
    (void)memset_s(ids, count * sizeof(int64_t), 0, count * sizeof(int64_t));
    int64_t worktimes[count];
    (void)memset_s(worktimes, count * sizeof(int64_t), 0, count * sizeof(int64_t));
    char *testdbs = (char *)malloc(count * 64);
    char testdbSource[10][64] = {"dav'id", "nut'", "bob哈", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
    char **vsysId = (char **)malloc(count * sizeof(char *));
    if (vsysId == NULL) {
        AW_FUN_Log(LOG_INFO, "Malloc unsucc.");
    }

    for (int i = 0; i < count; i++) {
        ids[i] = i + 1;
        worktimes[i] = 1695042000 + i;
        if (i % 10 == 0) {
            memcpy((testdbs + i * 64), (char *)testdbSource, 640);
        }
        vsysId[i] = (char *)malloc(TEXT_LEN * sizeof(char));
        (void)memset(vsysId[i], 0, TEXT_LEN);
    }

    GenerateRandomString(vsysId, count);

    uint32_t rowNum = count;
    int ret = GmcPrepareStmtByLabelName(g_stmt_sync, g_tableName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(g_stmt_sync, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, testdbs, 64, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(g_stmt_sync, 1, (GmcDataTypeE)DB_DATATYPE_INT64, ids, sizeof(ids[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(g_stmt_sync, 2, (GmcDataTypeE)DB_DATATYPE_INT64, worktimes, sizeof(worktimes[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(g_stmt_sync, 3, (GmcDataTypeE)DB_DATATYPE_STRING, vsysId, sizeof(char *), 0);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    FreeHeapMemory(testdbs);
    for (int i = 0; i < count; i++) {
        FreeHeapMemory(vsysId[i]);
    }
    FreeHeapMemory(vsysId);

    // 查询
    snprintf(g_command, 1024,
        "gmsysview -sql \"select *  from testdb where testdb like 'nut''%%' or  testdb like 'dav''%%' or "
        "testdb='bob哈'\" -s "
        "%s "
        "| "
        "grep time "
        "|awk -F ':' '{print $2}'",
        g_connServerTsdb);
    AW_FUN_Log(LOG_STEP, "g_command=%s\n", g_command);
    int32_t TimeValue = 0;
    TimeValue = GetViewFieldResultValue(g_command);
    AW_FUN_Log(LOG_STEP, "TimeValue=%d\n", TimeValue);
    EXPECT_LE(TimeValue, 500);

    // 查询
    snprintf(g_command, 1024,
        "gmsysview -analyze \"select *  from testdb where testdb like 'nut''%%' or  testdb like 'dav''%%' or "
        "testdb='bob哈'\" "
        "-s %s "
        "| "
        "grep time "
        "|awk -F ':' '{print $2}'",
        g_connServerTsdb);
    AW_FUN_Log(LOG_STEP, "g_command=%s\n", g_command);
    TimeValue = 0;
    TimeValue = GetViewFieldResultValue(g_command);
    AW_FUN_Log(LOG_STEP, "TimeValue=%d\n", TimeValue);
    EXPECT_LE(TimeValue, 500);
}

// 034、gmsysview -analyze中analyze关键字错误
TEST_F(tsdb_query_capabilities_enhanced, Timing_085_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = WriteDataDualPartition(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 查询
    snprintf(g_command, 1024, "gmsysview -analyz \"select * from testdb\" -s %s ", g_connServerTsdb);
    AW_FUN_Log(LOG_STEP, "g_command=%s\n", g_command);
    ret = executeCommand(g_command, "ret = 1009006");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 035、gmsysview -analyze中analyze关键字存在多个
TEST_F(tsdb_query_capabilities_enhanced, Timing_085_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = WriteDataDualPartition(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 查询
    snprintf(g_command, 1024, "gmsysview -analyze -analyze \"select * from testdb\" -s %s ", g_connServerTsdb);
    AW_FUN_Log(LOG_STEP, "g_command=%s\n", g_command);
    ret = executeCommand(g_command, "ret = 1009006");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 036、gmsysview -analyze "select diagnostic_view()" -s <server_locator>
TEST_F(tsdb_query_capabilities_enhanced, Timing_085_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = WriteDataDualPartition(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 查询
    const char *queryCommand = "select * from testdb where name='nut'';";
    ret = GmcExecDirect(g_stmt_sync, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, 1009000);
}

// 037、gmsysview 只有一个单引号的场景
TEST_F(tsdb_query_capabilities_enhanced, Timing_085_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = WriteDataDualPartition(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 查询
    snprintf(
        g_command, 1024, "gmsysview -analyze  \"select * from testdb where name='nut''\" -s %s ", g_connServerTsdb);
    AW_FUN_Log(LOG_STEP, "g_command=%s\n", g_command);
    ret = executeCommand(g_command, "ret = 1009000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 038、通过接口查询 只有一个单引号的场景
TEST_F(tsdb_query_capabilities_enhanced, Timing_085_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = WriteDataDualPartition(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 查询
    // 查询
    const char *queryCommand = "select * from testdb where name='nut'';";
    ret = GmcExecDirect(g_stmt_sync, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, 1009000);
}

// 039、gmsysview 多一个单引号的场景
TEST_F(tsdb_query_capabilities_enhanced, Timing_085_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = WriteDataDualPartition(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 查询
    snprintf(
        g_command, 1024, "gmsysview -analyze  \"select * from testdb where name='nut''''\" -s %s ", g_connServerTsdb);
    AW_FUN_Log(LOG_STEP, "g_command=%s\n", g_command);
    ret = executeCommand(g_command, "ret = 1009000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 040、接口多一个单引号的场景
TEST_F(tsdb_query_capabilities_enhanced, Timing_085_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = WriteDataDualPartition(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 查询
    // 查询
    const char *queryCommand = "select * from testdb where name='nut'''';";
    ret = GmcExecDirect(g_stmt_sync, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, 1009000);
}

// 041、构造视图服务端超时场景
TEST_F(tsdb_query_capabilities_enhanced, Timing_085_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = WriteDataDualPartition(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 查询
    snprintf(
        g_command, 1024, "gmsysview -analyze  \"select * from testdb where name='nut''''\" -s %s ", g_connServerTsdb);
    AW_FUN_Log(LOG_STEP, "g_command=%s\n", g_command);
    ret = executeCommand(g_command, "ret = 1009000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 042、-analyz和-explain 、-sql、-fmt混用
TEST_F(tsdb_query_capabilities_enhanced, Timing_085_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = WriteDataDualPartition(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 查询
    snprintf(g_command, 1024, "gmsysview -analyze  \"select * from testdb\"  -sql  \"select * from testdb\" -s %s ",
        g_connServerTsdb);
    AW_FUN_Log(LOG_STEP, "g_command=%s\n", g_command);
    ret = executeCommand(g_command, "1009000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询
    snprintf(g_command, 1024, "gmsysview -analyze  \"select * from testdb\"  -explain  \"select * from testdb\" -s %s ",
        g_connServerTsdb);
    AW_FUN_Log(LOG_STEP, "g_command=%s\n", g_command);
    ret = executeCommand(g_command, "1009000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询
    snprintf(g_command, 1024, "gmsysview -analyze  \"select * from testdb\"  -fmt hex  \"select * from testdb\" -s %s ",
        g_connServerTsdb);
    AW_FUN_Log(LOG_STEP, "g_command=%s\n", g_command);
#if defined(FEATURE_MULTI_TS)
    ret = executeCommand(g_command, "1009000");
#else
    ret = executeCommand(g_command, "1009006");
#endif
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
