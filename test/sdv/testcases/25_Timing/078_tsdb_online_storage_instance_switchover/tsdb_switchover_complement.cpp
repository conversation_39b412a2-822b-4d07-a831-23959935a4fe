/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: 目录切换加固验证
 * Author: jiangjincheng
 * Create: 2025-05-06
 */
#include "gtest/gtest.h"
#include "t_rd_ts.h"
#include "tsdb_storage_instance_switchover.h"

static GmcConnOptionsT *connOptions = NULL;
static GmcConnT *conn = NULL;
static GmcStmtT *stmt = NULL;
char g_cStoreDir[64] = {0};
char *dir = getenv("GMDB_HOME");

class TsdbSwitchoverComplement : public testing::Test {
protected:
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}

public:
    virtual void SetUp();
    virtual void TearDown();
};

void TsdbSwitchoverComplement::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    (void)sprintf(g_dataFilePath1, "%s/data/gmdb1", g_filePath);
    (void)sprintf(g_dataFilePath2, "%s/data/gmdb2", g_filePath);
    (void)sprintf(g_dataFilePath3, "%s/data/gmdb3", g_filePath);
    (void)sprintf(g_dataFilePath, "%s", TABLE_PATH);
    TsDefulatDbFileClean();
    system("rm -rf ./data/");
    system("rm -rf ./gmdb/");
    system("mkdir -p ./data/gmdb1");
    system("mkdir -p ./data/gmdb2");
    int ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret =
        TestGetResultCommand("cat ../common/logicTablePath.txt | tr -d '$\r'", NULL, g_cStoreDir, sizeof(g_cStoreDir));
    EXPECT_EQ(0, ret);
}

void TsdbSwitchoverComplement::TearDown()
{
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_CHECK_LOG_END();
    GmcConnOptionsDestroy(connOptions);
    GmcFreeStmt(stmt);
    GmcDisconnect(conn);
    testEnvClean();
    RecoverTsCiCfg();
    // 断连后检测临时文件是否被清理
    sleep(5);
    int ret = GetTempFileNumber(TEMPFILE_PATH);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
}

// 001.建时序表并发切换目录  预期：表均可创建成功，全部表可以正常写数据，删表。磁盘占用视图无异常和内存无明显增
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    char keyValue[256] = {0};
    char cutSymbol[256] = {0};
    char tableName[256] = {0};
    char defaultFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    uint32_t cmdLen = 0;
    int threadCount = 4;
    pthread_t tid[threadCount];
    InitTsCiCfgModify();
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    CreateInfo createInfo0 = {20, 0, 0};
    CreateInfo createInfo1 = {20, 1, 20};
    CreateInfo createInfo2 = {20, 2, 40};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam, &createInfo2);
    sleep(3);
    pthread_create(&tid[3], NULL, ConcurrentSwapDir, &g_dataFilePath1);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    pthread_join(tid[3], NULL);

    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s |grep TABLE_NAME | wc -l",
        g_connServerTsdb);
    ret = GetViewFieldResultValue(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    sleep(3);
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);

    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$TS_TBL_PROPS'\"");
    (void)memset(cutSymbol, 0, 256);
    (void)sprintf(cutSymbol, "wc -l");
    ret = GetKeyWordValue(sqlCmd, g_dataFilePath1, cutSymbol);
    AW_MACRO_ASSERT_EQ_INT(40, ret);
    (void)memset(keyValue, 0, 256);
    (void)sprintf(keyValue, "%s/gmdb", g_filePath);
    ret = GetKeyWordValue(sqlCmd, keyValue, cutSymbol);
    AW_MACRO_ASSERT_EQ_INT(20, ret);
    (void)memset(keyValue, 0, 256);
    (void)sprintf(keyValue, "'TABLE_TYPE: memory'");
    ret = GetKeyWordValue(sqlCmd, keyValue, cutSymbol);
    AW_MACRO_ASSERT_EQ_INT(20, ret);

    for (int i = 0; i < 60; i++) {
        (void)sprintf(tableName, "testdb%d", i);
        ret = DropTsTable(stmt, tableName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s |grep TABLE_NAME | wc -l",
        g_connServerTsdb);
    ret = GetViewFieldResultValue(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(0, ret);

    pthread_create(&tid[3], NULL, ConcurrentSwapDir, &defaultFilePath);
    pthread_join(tid[3], NULL);

    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$TS_TBL_PROPS' where table_name = 'testdb20'\"");
    ret = GetKeyWordValue(sqlCmd, defaultFilePath, cutSymbol);
    AW_MACRO_ASSERT_EQ_INT(1, ret);
    (void)memset(keyValue, 0, 256);
    (void)sprintf(keyValue, "%s/gmdb", g_filePath);
    ret = GetKeyWordValue(sqlCmd, keyValue, cutSymbol);
    AW_MACRO_ASSERT_EQ_INT(1, ret);

    for (int i = 0; i < 60; i++) {
        (void)sprintf(tableName, "testdb%d", i);
        ret = DropTsTable(stmt, tableName);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_STEP, "drop table failed, tableName is %s, ret = %d", tableName, ret);
            continue;
        }
    }
    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s |grep TABLE_NAME | wc -l",
        g_connServerTsdb);
    ret = GetViewFieldResultValue(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002.删时序表并发切换目录  预期：全部表均可删除成功，全行列存文件全部删除,磁盘占用视图无异常
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    char keyValue[256] = {0};
    char cutSymbol[256] = {0};
    char tableName[256] = {0};
    char defaultFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    uint32_t cmdLen = 0;
    int threadCount = 4;
    pthread_t tid[threadCount];

    InitTsCiCfgModify();
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo0 = {50, 0, 0};
    CreateInfo createInfo1 = {50, 1, 50};
    CreateInfo createInfo2 = {20, 2, 100};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    constexpr int64_t startTime = 1262275200;
    for (int i = 0; i < 120; i++) {
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, 10000, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    CreateInfo createInfo3 = {120, 0, 0};
    // 改为异步后，不需要等待
    pthread_create(&tid[0], NULL, CurrentDropTable, &createInfo3);
    pthread_create(&tid[3], NULL, ConcurrentSwapDir, &g_dataFilePath1);
    pthread_join(tid[0], NULL);
    pthread_join(tid[3], NULL);

    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$TS_TBL_PROPS'\"");
    (void)memset(cutSymbol, 0, 256);
    (void)sprintf(cutSymbol, "wc -l");
    ret = GetKeyWordValue(sqlCmd, g_dataFilePath1, cutSymbol);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    (void)memset(keyValue, 0, 256);
    (void)sprintf(keyValue, "%s/gmdb", g_filePath);
    ret = GetKeyWordValue(sqlCmd, keyValue, cutSymbol);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    (void)memset(keyValue, 0, 256);
    (void)sprintf(keyValue, "'TABLE_TYPE: memory'");
    ret = GetKeyWordValue(sqlCmd, keyValue, cutSymbol);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    ConcurrentSwapDir(&defaultFilePath);

    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$TS_TBL_PROPS'\"");
    ret = GetKeyWordValue(sqlCmd, defaultFilePath, cutSymbol);
    if (ret != 0) {
        (void)memset(sqlCmd, 0, 256);
        (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$TS_TBL_PROPS' where table_name = 'testdb99'\"");
        ret = GetKeyWordValue(sqlCmd, defaultFilePath, cutSymbol);
        AW_MACRO_ASSERT_EQ_INT(1, ret);
        (void)memset(keyValue, 0, 256);
        (void)sprintf(keyValue, "%s/gmdb", g_filePath);
        ret = GetKeyWordValue(sqlCmd, keyValue, cutSymbol);
        AW_MACRO_ASSERT_EQ_INT(1, ret);
        for (int i = 0; i < 100; i++) {
            (void)sprintf(tableName, "testdb%d", i);
            ret = DropTsTable(stmt, tableName);
            if (ret != GMERR_OK) {
                AW_FUN_Log(LOG_STEP, "drop table failed, tableName is %s, ret = %d", tableName, ret);
                continue;
            }
        }
    }
    sleep(10);
    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$TS_TBL_PROPS' where table_name != '99'\" -s %s"
        " |grep TABLE_NAME | wc -l", g_connServerTsdb);
    ret = GetViewFieldResultValue(sqlCmd);
    if (ret != GMERR_OK) {
        EXPECT_GE(20, ret);
    }

    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name != '99'\" -s %s"
        " |grep TABLE_NAME | wc -l", g_connServerTsdb);
    ret = GetViewFieldResultValue(sqlCmd);
    if (ret != GMERR_OK) {
        EXPECT_GE(20, ret);
    }

    for (int i = 100; i < 120; i++) {
        (void)sprintf(tableName, "testdb%d", i);
        ret = DropTsTable(stmt, tableName);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_STEP, "drop table failed, tableName is %s, ret = %d", tableName, ret);
            continue;
        }
    }
    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name != '99'\" -s %s"
        " |grep TABLE_NAME | wc -l", g_connServerTsdb);
    ret = GetViewFieldResultValue(sqlCmd);
    for (int i = 0; i < 10; i++) {
        if (ret != GMERR_OK) {
            ret = GetViewFieldResultValue(sqlCmd);
            sleep(1);
        } else {
            break;
        }
    }
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003.删内存表并发切换目录  预期：全部表均可删除成功，全行列存文件全部删除,磁盘占用视图无异常
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    char keyValue[256] = {0};
    char cutSymbol[256] = {0};
    char tableName[256] = {0};
    char defaultFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    uint32_t cmdLen = 0;
    int threadCount = 4;
    pthread_t tid[threadCount];

    InitTsCiCfgModify();
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo0 = {10, 0, 0};
    CreateInfo createInfo1 = {10, 1, 10};
    CreateInfo createInfo2 = {100, 2, 20};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    constexpr int64_t startTime = 1262275200;
    for (int i = 0; i < 120; i++) {
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, 10000, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    CreateInfo createInfo3 = {120, 2, 0};
    pthread_create(&tid[0], NULL, CurrentDropTable, &createInfo3);
    // 等待删表进行
    sleep(2);
    pthread_create(&tid[3], NULL, ConcurrentSwapDir, &g_dataFilePath1);
    pthread_join(tid[0], NULL);
    pthread_join(tid[3], NULL);

    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$TS_TBL_PROPS'\"");
    (void)memset(cutSymbol, 0, 256);
    (void)sprintf(cutSymbol, "wc -l");
    ret = GetKeyWordValue(sqlCmd, g_dataFilePath1, cutSymbol);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    (void)memset(keyValue, 0, 256);
    (void)sprintf(keyValue, "%s/gmdb", g_filePath);
    ret = GetKeyWordValue(sqlCmd, keyValue, cutSymbol);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    (void)memset(keyValue, 0, 256);
    (void)sprintf(keyValue, "'TABLE_TYPE: memory'");
    ret = GetKeyWordValue(sqlCmd, keyValue, cutSymbol);
    AW_MACRO_ASSERT_EQ_INT(0, ret);

    ConcurrentSwapDir(&defaultFilePath);

    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$TS_TBL_PROPS'\"");
    ret = GetKeyWordValue(sqlCmd, defaultFilePath, cutSymbol);
    if (ret != 0) {
        (void)memset(sqlCmd, 0, 256);
        (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$TS_TBL_PROPS' where table_name = 'testdb19'\"");
        ret = GetKeyWordValue(sqlCmd, defaultFilePath, cutSymbol);
        if (ret == 1) {
            (void)memset(keyValue, 0, 256);
            (void)sprintf(keyValue, "%s/gmdb", g_filePath);
            ret = GetKeyWordValue(sqlCmd, keyValue, cutSymbol);
            AW_MACRO_ASSERT_EQ_INT(1, ret);
        }
    }
    for (int i = 0; i < 100; i++) {
        (void)sprintf(tableName, "testdb%d", i);
        ret = DropTsTable(stmt, tableName);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_STEP, "drop table failed, tableName is %s, ret = %d", tableName, ret);
            continue;
        }
    }
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$TS_TBL_PROPS' where table_name != '99'\" -s %s"
        " |grep TABLE_NAME | wc -l", g_connServerTsdb);
    ret = GetViewFieldResultValue(sqlCmd);
    if (ret != GMERR_OK) {
        EXPECT_GE(20, ret);
    }

    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name != '99'\" -s %s"
        " |grep TABLE_NAME | wc -l", g_connServerTsdb);
    ret = GetViewFieldResultValue(sqlCmd);
    if (ret != GMERR_OK) {
        EXPECT_GE(20, ret);
    }

    for (int i = 100; i < 120; i++) {
        (void)sprintf(tableName, "testdb%d", i);
        // 偶现删表太快导致无法校验表中数据
        ret = DropTsTable(stmt, tableName);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_STEP, "drop table failed, tableName is %s, ret = %d", tableName, ret);
            continue;
        }
    }
    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name != '99'\" -s %s"
        " |grep TABLE_NAME | wc -l", g_connServerTsdb);
    ret = GetViewFieldResultValue(sqlCmd);
    for (int i = 0; i < 10; i++) {
        if (ret != GMERR_OK) {
            ret = GetViewFieldResultValue(sqlCmd);
            sleep(1);
        } else {
            break;
        }
    }
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004.循环建表、删表、切换目录  预期：全部动作均可执行成功
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 先建表，循环100次,写入数据,切换目录,最后一轮循环后,调用删表动作,查看视图
    char sqlCmd[256] = {0};
    char tableName[256] = {0};
    char defaultFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    uint32_t cmdLen = 0;
    int threadCount = 4;
    pthread_t tid[threadCount];

    InitTsCiCfgModify();
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo0 = {10, 0, 0};
    CreateInfo createInfo1 = {10, 1, 10};
    CreateInfo createInfo2 = {10, 2, 20};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);

    constexpr int64_t startTime = 1262275200;
    CreateInfo createInfo3 = {30, 2, 0};
    for (int j = 0; j < 2; j++) {
        if (j % 2 == 0) {
            pthread_create(&tid[3], NULL, ConcurrentSwapDir, &g_dataFilePath1);
        } else {
            pthread_create(&tid[3], NULL, ConcurrentSwapDir, &defaultFilePath);
        }
        pthread_join(tid[3], NULL);
        (void)memset(sqlCmd, 0, 256);
        (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s |grep TABLE_NAME | wc -l",
            g_connServerTsdb);
        ret = GetViewFieldResultValue(sqlCmd);
        if (ret == 0) {
            pthread_create(&tid[0], NULL, CurrentDropTable, &createInfo3);
            pthread_join(tid[0], NULL);
        }
        pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
        pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
        pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam, &createInfo2);
        pthread_join(tid[0], NULL);
        pthread_join(tid[1], NULL);
        pthread_join(tid[2], NULL);
        for (int i = 0; i < 30; i++) {
            (void)memset(tableName, 0, 256);
            (void)sprintf(tableName, "testdb%d", i);
            ret = rowInsertData(stmt, tableName, 1000, startTime);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    pthread_create(&tid[0], NULL, CurrentDropTable, &createInfo3);
    pthread_join(tid[0], NULL);
    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s |grep TABLE_NAME | wc -l",
        g_connServerTsdb);
    ret = GetViewFieldResultValue(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005.ttl和切换目录顺序执行 预期：修改前ttl检测时数据不清理，修改后ttl数据可以正常清理
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 先建表，循环100次,写入数据,切换目录,最后一轮循环后,调用删表动作,查看视图
    char sqlCmd[256] = {0};
    char tableName[256] = {0};
    char defaultFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    uint32_t cmdLen = 0;
    int threadCount = 4;
    pthread_t tid[threadCount];

// euler和仿真环境清共享内存，清理原有DB文件
#if defined RUN_INDEPENDENT
    system("sh $TEST_HOME/tools/stop.sh -f");
#endif
    system("sh $TEST_HOME/tools/stop.sh -ts");
    TsDefulatDbFileClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts recover");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"tsLcmCheckPeriod=15\"");
    system("sh $TEST_HOME/tools/start.sh -ts");

    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo0 = {3, 0, 0};
    CreateInfo createInfo1 = {3, 1, 3};
    CreateInfo createInfo2 = {3, 2, 6};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam1, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T - 36000;
    for (int i = 0; i < 9; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, 1000, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (i % 3 == 0) {
            (void)sprintf(tableName, "ALTER TABLE testdb%d  SET (ttl = '1 hour');", i);
            ret = GmcExecDirect(stmt, tableName, strlen(tableName));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    ConcurrentSwapDir(&g_dataFilePath1);

    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam1, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    int res = 3;
    for (int i = 0; i < 9; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, 1000, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
    }
    sleep(35);
    
    for (int i = 0; i < 9; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        res = returnDataCount(stmt, tableName);
        AW_MACRO_EXPECT_EQ_INT(1000, res);
    }
    for (int i = 0; i < 9; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        if (i % 3 == 0) {
            (void)sprintf(tableName, "ALTER TABLE testdb%d  SET (ttl = '1 hour');", i);
            ret = GmcExecDirect(stmt, tableName, strlen(tableName));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    sleep(35);
    for (int i = 0; i < 9; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        if (i % 3 == 0) {
            res = returnDataCount(stmt, tableName);
            AW_MACRO_EXPECT_EQ_INT(0, res);
        } else {
            res = returnDataCount(stmt, tableName);
            AW_MACRO_EXPECT_EQ_INT(1000, res);
        }
    }

    ConcurrentSwapDir(&defaultFilePath);
    sleep(35);
    for (int i = 0; i < 9; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        if (i % 3 == 0 || i >= 6) {
            res = returnDataCount(stmt, tableName);
            AW_MACRO_EXPECT_EQ_INT(0, res);
        } else {
            res = returnDataCount(stmt, tableName);
            AW_MACRO_EXPECT_EQ_INT(1000, res);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006.disk_limit和切换目录顺序执行 预期：修改前ttl检测时数据不清理，修改后disk_limit数据可以正常清理
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 先建表，循环100次,写入数据,切换目录,最后一轮循环后,调用删表动作,查看视图
    char sqlCmd[256] = {0};
    char tableName[256] = {0};
    char defaultFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    uint32_t cmdLen = 0;
    int threadCount = 4;
    pthread_t tid[threadCount];

    InitTsCiCfgModify();
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo0 = {3, 0, 0};
    CreateInfo createInfo1 = {3, 1, 3};
    CreateInfo createInfo2 = {3, 2, 6};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam1, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T - 36000;
    for (int i = 0; i < 9; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, 1000, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (i % 3 == 0) {
            (void)sprintf(tableName, "ALTER TABLE testdb%d  SET (disk_limit = '4 mb');", i);
            ret = GmcExecDirect(stmt, tableName, strlen(tableName));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    ConcurrentSwapDir(&g_dataFilePath1);

    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam1, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    int res = 3;
    for (int i = 0; i < 9; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, 1000, 1262275200);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        res = returnDataCount(stmt, tableName);
        AW_MACRO_EXPECT_EQ_INT(1000, res);
    }    
    for (int i = 0; i < 9; i++) {
        (void)memset(tableName, 0, 256);
        if (i % 3 == 0) {
            (void)sprintf(tableName, "ALTER TABLE testdb%d  SET (disk_limit = '4 mb');", i);
            ret = GmcExecDirect(stmt, tableName, strlen(tableName));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    for (int i = 0; i < 9; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        if (i == 6) {
            res = returnDataCount(stmt, tableName);
            AW_MACRO_EXPECT_EQ_INT(0, res);
            continue;
        }
        if (i % 3 == 0) {
            res = returnDataCount(stmt, tableName);
            AW_MACRO_EXPECT_EQ_INT(0, res);
        } else {
            res = returnDataCount(stmt, tableName);
            AW_MACRO_EXPECT_EQ_INT(1000, res);
        }
    }

    
    ConcurrentSwapDir(&defaultFilePath);
    for (int i = 0; i < 9; i++) {
        (void)memset(sqlCmd, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        if (i % 3 == 0 || i > 5) {
            res = returnDataCount(stmt, tableName);
            AW_MACRO_EXPECT_EQ_INT(0, res);
        } else {
            res = returnDataCount(stmt, tableName);
            AW_MACRO_EXPECT_EQ_INT(1000, res);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007.新增列和切换目录顺序执行 预期：修改前ttl检测时数据不清理，修改后ttl数据可以正常清理
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 先建表，循环100次,写入数据,切换目录,最后一轮循环后,调用删表动作,查看视图
    char sqlCmd[256] = {0};
    char tableName[256] = {0};
    char defaultFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    uint32_t cmdLen = 0;
    int threadCount = 4;
    pthread_t tid[threadCount];

    InitTsCiCfgModify();
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo0 = {3, 0, 0};
    CreateInfo createInfo1 = {3, 1, 3};
    CreateInfo createInfo2 = {3, 2, 6};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam1, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T - 36000;
    for (int i = 0; i < 9; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, 1000, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (i % 3 == 0) {
            (void)sprintf(tableName, "ALTER TABLE testdb%d  add id2 integer;", i);
            ret = GmcExecDirect(stmt, tableName, strlen(tableName));
            if (i == 6) {
                AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
            } else {
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            }
        }
    }
    ConcurrentSwapDir(&g_dataFilePath1);

    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam1, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    int res = 3;
    for (int i = 0; i < 9; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "select id2 from testdb%d", i);
        ret = GmcExecDirect(stmt, tableName, strlen(tableName));
        AW_MACRO_EXPECT_EQ_INT(GMERR_SEMANTIC_ERROR, ret);
    }    
    for (int i = 0; i < 9; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        if (i % 3 == 0) {
            (void)sprintf(tableName, "ALTER TABLE testdb%d  add id2 integer;", i);
            ret = GmcExecDirect(stmt, tableName, strlen(tableName));
            if (i == 6) {
                AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
            } else {
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            }
        }
    }
    for (int i = 0; i < 9; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "select id2 from testdb%d", i);
        ret = GmcExecDirect(stmt, tableName, strlen(tableName));
        if (i >= 6 || i % 3 != 0) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_SEMANTIC_ERROR, ret);
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }

    ConcurrentSwapDir(&defaultFilePath);
    for (int i = 0; i < 9; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "select id2 from testdb%d", i);
        ret = GmcExecDirect(stmt, tableName, strlen(tableName));
        if (i >= 6 || i % 3 != 0) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_SEMANTIC_ERROR, ret);
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008.并发tsdb_aging和切换目录 预期：切换目录时tsdb_aging执行失败，再次切换目录回来后，tsdb_aging执行成功，查询结果均符合预期
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 先建表，循环100次,写入数据,切换目录,最后一轮循环后,调用删表动作,查看视图
    char sqlCmd[256] = {0};
    char tableName[256] = {0};
    char defaultFilePath[256] = {0};
    int res = 0;
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    uint32_t cmdLen = 0;
    int threadCount = 4;
    pthread_t tid[threadCount];

    InitTsCiCfgModify();
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo0 = {3, 0, 0};
    CreateInfo createInfo1 = {3, 1, 3};
    CreateInfo createInfo2 = {3, 2, 6};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam1, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);

    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T - 36000;
    for (int i = 0; i < 9; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, 30000, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    CreateInfo createInfo3 = {9, 2, 0};
    pthread_create(&tid[3], NULL, ConcurrentTsdbAging, &createInfo3);
    ConcurrentSwapDir(&g_dataFilePath1);
    
    pthread_join(tid[3], NULL);
    
    for (int i = 0; i < 9; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, 30000, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    }
    
    ConcurrentSwapDir(&defaultFilePath);

    pthread_create(&tid[3], NULL, ConcurrentTsdbAging, &createInfo3);    
    pthread_join(tid[3], NULL);

    for (int i = 0; i < 9; i++) {
        (void)memset(sqlCmd, 0, 256);
        (void)sprintf(sqlCmd, "select * from testdb%d", i);
        ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        res = returnDataCount(stmt, tableName);
        if (i >= 6 ) {
            AW_MACRO_EXPECT_EQ_INT(0, res);
        } else {
            AW_MACRO_EXPECT_NE_INT(0, res);
        }
        
        ret = DropTable(stmt, tableName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 009.循环执行tsdb_aging和切换目录 预期：切换目录时tsdb_aging执行失败，再次切换目录回来后，tsdb_aging执行成功，查询结果均符合预期
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int res = 0;
    char sqlCmd[256] = {0};
    char tableName[256] = {0};
    char defaultFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    uint32_t cmdLen = 0;
    int threadCount = 4;
    pthread_t tid[threadCount];

    InitTsCiCfgModify();
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo0 = {10, 0, 0};
    CreateInfo createInfo1 = {10, 1, 10};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);

    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T - 36000;
    for (int i = 0; i < 20; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, 30000, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    CreateInfo createInfo3 = {20, 2, 0};
    pthread_create(&tid[3], NULL, ConcurrentTsdbAging, &createInfo3);
    ConcurrentSwapDir(&g_dataFilePath1);
    
    pthread_join(tid[3], NULL);
    if (g_concurrentrStatus != GMERR_OK) {
        // 避免切换等待时间过长导致切换失败
        ConcurrentSwapDir(&g_dataFilePath1);
    }
    

    ConcurrentSwapDir(&defaultFilePath);
    // 切回去后，数据不应被全部清理
    for (int i = 0; i < 20; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        res = returnDataCount(stmt, tableName);
        AW_MACRO_EXPECT_NE_INT(0, res);
    }
    // 循环切目录，以及执行tsdb_aging,直至数据被清理完成
    for (int i = 0; i < 30; i++) {
        ConcurrentSwapDir(&g_dataFilePath1);
        ConcurrentSwapDir(&defaultFilePath);
        pthread_create(&tid[3], NULL, ConcurrentTsdbAging, &createInfo3);    
        pthread_join(tid[3], NULL);
    }
    // 数据应该为0
    for (int i = 0; i < 20; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        res = returnDataCount(stmt, tableName);
        AW_MACRO_EXPECT_EQ_INT(0, res);
        ret = DropTable(stmt, tableName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010.批量注入内存表与切换目录并发 预期：切换目录时tsdb_aging执行失败，再次切换目录回来后，tsdb_aging执行成功，查询结果均符合预期
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int res = 0;
    char sqlCmd[256] = {0};
    char tableName[256] = {0};
    char defaultFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    int64_t dataCount = 100000;
    uint32_t cmdLen = 0;
    int threadCount = 4;
    int memoryBefore = 0;
    int memoryAfter = 0;
    pthread_t tid[threadCount];

    InitTsCiCfgModify();
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo0 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_join(tid[0], NULL);
    memoryBefore = GetKeyWordValue(NULL, NULL, NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T - 36000;
    (void)memset(tableName, 0, 256);
    (void)sprintf(tableName, "testdb2");
    InsertQueryDataType constructDataType_1 = {stmt, tableName, dataCount, startTime};

    pthread_create(&tid[0], NULL, InsertDataToTable, &constructDataType_1);
    // 等待注入开始
    sleep(3);
    ConcurrentSwapDir(&g_dataFilePath1);
    pthread_join(tid[0], NULL);

    createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam1, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    
    constructDataType_1 = {stmt, tableName, dataCount, startTime};
    pthread_create(&tid[0], NULL, InsertDataToTable, &constructDataType_1);
    pthread_join(tid[0], NULL);

    ConcurrentSwapDir(&defaultFilePath);

    constructDataType_1 = {stmt, tableName, dataCount, startTime};
    pthread_create(&tid[0], NULL, InsertDataToTable, &constructDataType_1);
    pthread_join(tid[0], NULL);

    
    for (int i = 0; i < 8; i++) {
        ConcurrentSwapDir(&g_dataFilePath1);
        ConcurrentSwapDir(&defaultFilePath);
        pthread_create(&tid[0], NULL, InsertDataToTable, &constructDataType_1);
        pthread_join(tid[0], NULL);
    }

    ret = DropTable(stmt, tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    memoryAfter = GetKeyWordValue(NULL, NULL, NULL);
    AW_FUN_Log(LOG_STEP, "test end.");   
}

// 011.批量注入逻辑表与切换目录并发 预期：切换目录时tsdb_aging执行失败，再次切换目录回来后，tsdb_aging执行成功，查询结果均符合预期
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int res = 0;
    char sqlCmd[256] = {0};
    char tableName[256] = {0};
    char defaultFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    int64_t dataCount = 100000;
    uint32_t cmdLen = 0;
    int threadCount = 4;
    int memoryBefore = 0;
    int memoryAfter = 0;
    pthread_t tid[threadCount];

    InitTsCiCfgModify();
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo0 = {1, 0, 0};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_join(tid[0], NULL);
    memoryBefore = GetKeyWordValue(NULL, NULL, NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T - 36000;
    (void)memset(tableName, 0, 256);
    (void)sprintf(tableName, "testdb0");
    InsertQueryDataType constructDataType_1 = {stmt, tableName, dataCount, startTime};

    pthread_create(&tid[0], NULL, InsertDataToTable, &constructDataType_1);
    // 等待注入开始
    sleep(3);
    ConcurrentSwapDir(&g_dataFilePath1);
    pthread_join(tid[0], NULL);

    

    createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam1, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    
    constructDataType_1 = {stmt, tableName, dataCount, startTime};
    pthread_create(&tid[0], NULL, InsertDataToTable, &constructDataType_1);
    pthread_join(tid[0], NULL);

    ConcurrentSwapDir(&defaultFilePath);

    constructDataType_1 = {stmt, tableName, dataCount, startTime};
    pthread_create(&tid[0], NULL, InsertDataToTable, &constructDataType_1);
    pthread_join(tid[0], NULL);

    
    for (int i = 0; i < 8; i++) {
        ConcurrentSwapDir(&g_dataFilePath1);
        ConcurrentSwapDir(&defaultFilePath);
        pthread_create(&tid[0], NULL, InsertDataToTable, &constructDataType_1);
        pthread_join(tid[0], NULL);
    }

    ret = DropTable(stmt, tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    memoryAfter = GetKeyWordValue(NULL, NULL, NULL);
}

// 012.批量注入逻辑表带table_path与切换目录并发 预期：切换目录时tsdb_aging执行失败，再次切换目录回来后，tsdb_aging执行成功，查询结果均符合预期
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int res = 0;
    char sqlCmd[256] = {0};
    char tableName[256] = {0};
    char defaultFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    int64_t dataCount = 100000;
    uint32_t cmdLen = 0;
    int threadCount = 4;
    int memoryBefore = 0;
    int memoryAfter = 0;
    pthread_t tid[threadCount];

    InitTsCiCfgModify();
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo0 = {1, 1, 1};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_join(tid[0], NULL);
    memoryBefore = GetKeyWordValue(NULL, NULL, NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T - 36000;
    (void)memset(tableName, 0, 256);
    (void)sprintf(tableName, "testdb1");
    InsertQueryDataType constructDataType_1 = {stmt, tableName, dataCount, startTime};

    pthread_create(&tid[0], NULL, InsertDataToTable, &constructDataType_1);
    // 等待注入开始
    sleep(3);
    ConcurrentSwapDir(&g_dataFilePath1);
    pthread_join(tid[0], NULL);

    

    createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam1, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    
    constructDataType_1 = {stmt, tableName, dataCount, startTime};
    pthread_create(&tid[0], NULL, InsertDataToTable, &constructDataType_1);
    pthread_join(tid[0], NULL);

    ConcurrentSwapDir(&defaultFilePath);

    constructDataType_1 = {stmt, tableName, dataCount, startTime};
    pthread_create(&tid[0], NULL, InsertDataToTable, &constructDataType_1);
    pthread_join(tid[0], NULL);

    
    for (int i = 0; i < 8; i++) {
        ConcurrentSwapDir(&g_dataFilePath1);
        ConcurrentSwapDir(&defaultFilePath);
        pthread_create(&tid[0], NULL, InsertDataToTable, &constructDataType_1);
        pthread_join(tid[0], NULL);
    }

    ret = DropTable(stmt, tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    memoryAfter = GetKeyWordValue(NULL, NULL, NULL);    
}

// 013.批量注入带cache_size逻辑表与切换目录并发 预期：切换目录时tsdb_aging执行失败，再次切换目录回来后，tsdb_aging执行成功，查询结果均符合预期
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int res = 0;
    char sqlCmd[256] = {0};
    char tableName[20] = {0};
    char tableName1[20] = {0};
    char tableName2[20] = {0};
    char defaultFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    int64_t dataCount = 100000;
    uint32_t cmdLen = 0;
    int threadCount = 4;
    int memoryBefore = 0;
    int memoryAfter = 0;
    pthread_t tid[threadCount];

    InitTsCiCfgModify();
    static GmcConnT *conn1 = NULL;
    static GmcStmtT *stmt1 = NULL;
    static GmcConnT *conn2 = NULL;
    static GmcStmtT *stmt2 = NULL;
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestTsGmcConnect(&conn1, &stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestTsGmcConnect(&conn2, &stmt2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    memoryBefore = GetKeyWordValue(NULL, NULL, NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T - 36000;
    (void)memset(tableName, 0, 20);
    (void)memset(tableName1, 0, 20);
    (void)memset(tableName2, 0, 20);
    (void)sprintf(tableName, "testdb0");
    (void)sprintf(tableName1, "testdb1");
    (void)sprintf(tableName2, "testdb2");
    InsertQueryDataType constructDataType_1 = {stmt, tableName, dataCount, startTime};
    InsertQueryDataType constructDataType_2 = {stmt1, tableName1, dataCount, startTime};
    pthread_create(&tid[0], NULL, InsertDataToTable, &constructDataType_1);
    pthread_create(&tid[1], NULL, InsertDataToTable, &constructDataType_2);
    // 等待注入开始
    sleep(3);
    ConcurrentSwapDir(&g_dataFilePath1);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);

    createInfo0 = {1, 0, 0};
    createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    
    InsertQueryDataType constructDataType_3 = {stmt2, tableName2, dataCount, startTime};
    pthread_create(&tid[0], NULL, InsertDataToTable, &constructDataType_1);
    pthread_create(&tid[1], NULL, InsertDataToTable, &constructDataType_2);
    pthread_create(&tid[2], NULL, InsertDataToTable, &constructDataType_3);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);

    ConcurrentSwapDir(&defaultFilePath);

    constructDataType_1 = {stmt, tableName, dataCount, startTime};
    pthread_create(&tid[0], NULL, InsertDataToTable, &constructDataType_1);
    pthread_create(&tid[1], NULL, InsertDataToTable, &constructDataType_2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);

    dataCount = 30;
    ret = rowInsertData(stmt, "testdb0", dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    res = returnDataCount(stmt, "testdb0");

    char tempSqlCmd1[256] = {"ROW_CNT"};
    char tempSqlCmd3[256] = {"awk -F ':' '{print $2}'"};
    int dataNum = 0;

    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = 'testdb0'\"");
    dataNum = GetKeyWordValue(sqlCmd, tempSqlCmd1, tempSqlCmd3);
    // 视图不统计未落盘数据
    EXPECT_GT(res, dataNum);

    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    system(sqlCmd);
    (void)memset(tableName, 0, 20);
    (void)memset(tableName1, 0, 20);
    (void)sprintf(tableName, "testdb0");
    (void)sprintf(tableName1, "testdb1");
    ret = DropTable(stmt, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, tableName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memoryAfter = GetKeyWordValue(NULL, NULL, NULL);
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn2, stmt2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

}

// 014.修改ckptPeriod配置项为1，然后并发写数据和swap 预期：无异常
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int res = 0;
    char sqlCmd[256] = {0};
    char tableName[256] = {0};
    char tableName1[256] = {0};
    char tableName2[256] = {0};
    char defaultFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    int64_t dataCount = 100000;
    uint32_t cmdLen = 0;
    int threadCount = 4;
    int memoryBefore = 0;
    int memoryAfter = 0;
    pthread_t tid[threadCount];

// euler和仿真环境清共享内存，清理原有DB文件
#if defined RUN_INDEPENDENT
    system("sh $TEST_HOME/tools/stop.sh -f");
#endif
    system("sh $TEST_HOME/tools/stop.sh -ts");
    TsDefulatDbFileClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts recover");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"checkpoint=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    static GmcConnT *conn1 = NULL;
    static GmcStmtT *stmt1 = NULL;
    static GmcConnT *conn2 = NULL;
    static GmcStmtT *stmt2 = NULL;
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestTsGmcConnect(&conn1, &stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestTsGmcConnect(&conn2, &stmt2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    memoryBefore = GetKeyWordValue(NULL, NULL, NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T - 36000;
    (void)memset(tableName, 0, 256);
    (void)memset(tableName1, 0, 256);
    (void)memset(tableName2, 0, 256);
    (void)sprintf(tableName, "testdb0");
    (void)sprintf(tableName1, "testdb1");
    (void)sprintf(tableName2, "testdb2");
    InsertQueryDataType constructDataType_1 = {stmt, tableName, dataCount, startTime};
    InsertQueryDataType constructDataType_2 = {stmt1, tableName1, dataCount, startTime};
    pthread_create(&tid[0], NULL, InsertDataToTable, &constructDataType_1);
    pthread_create(&tid[1], NULL, InsertDataToTable, &constructDataType_2);
    // 等待注入开始
    sleep(3);
    ConcurrentSwapDir(&g_dataFilePath1);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);

    createInfo0 = {1, 0, 0};
    createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    
    InsertQueryDataType constructDataType_3 = {stmt2, tableName2, dataCount, startTime};
    pthread_create(&tid[0], NULL, InsertDataToTable, &constructDataType_1);
    pthread_create(&tid[1], NULL, InsertDataToTable, &constructDataType_2);
    pthread_create(&tid[2], NULL, InsertDataToTable, &constructDataType_3);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);

    ConcurrentSwapDir(&defaultFilePath);

    constructDataType_1 = {stmt, tableName, dataCount, startTime};
    pthread_create(&tid[0], NULL, InsertDataToTable, &constructDataType_1);
    pthread_create(&tid[1], NULL, InsertDataToTable, &constructDataType_2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);

    
    for (int i = 0; i < 8; i++) {
        ConcurrentSwapDir(&g_dataFilePath1);
        pthread_create(&tid[0], NULL, InsertDataToTable, &constructDataType_1);
        pthread_create(&tid[1], NULL, InsertDataToTable, &constructDataType_2);
        ConcurrentSwapDir(&defaultFilePath);
        pthread_join(tid[0], NULL);
        pthread_join(tid[1], NULL);
    }

    ret = DropTable(stmt, tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, tableName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    memoryAfter = GetKeyWordValue(NULL, NULL, NULL);    
}

// 015.insert into内存表与切换目录并发
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int res = 0;
    char sqlCmd[256] = {0};
    char tableName[256] = {0};
    char defaultFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    int64_t dataCount = 100000;
    uint32_t cmdLen = 0;
    int threadCount = 4;
    int memoryBefore = 0;
    int memoryAfter = 0;
    pthread_t tid[threadCount];

    InitTsCiCfgModify();
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    memoryBefore = GetKeyWordValue(NULL, NULL, NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T - 36000;
    (void)memset(tableName, 0, 256);
    (void)sprintf(tableName, "testdb2");
    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset(tableName, 0, 256);
    (void)sprintf(tableName, "testdb0");
    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo3 = {2, 2, 0};
    pthread_create(&tid[0], NULL, ConcurrentInsertInto, &createInfo3);
    // 等待注入开始
    sleep(3);
    ConcurrentSwapDir(&g_dataFilePath1);
    pthread_join(tid[0], NULL);

    createInfo0 = {1, 0, 0};
    createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    
    pthread_create(&tid[0], NULL, ConcurrentInsertInto, &createInfo3);
    pthread_join(tid[0], NULL);

    ConcurrentSwapDir(&defaultFilePath);

    pthread_create(&tid[0], NULL, ConcurrentInsertInto, &createInfo3);
    pthread_join(tid[0], NULL);

    
    for (int i = 0; i < 8; i++) {
        ConcurrentSwapDir(&g_dataFilePath1);
        pthread_create(&tid[0], NULL, ConcurrentInsertInto, &createInfo3);
        ConcurrentSwapDir(&defaultFilePath);
        pthread_join(tid[0], NULL);
    }

    ret = DropTable(stmt, tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    memoryAfter = GetKeyWordValue(NULL, NULL, NULL);
}

// 016.insert into逻辑表与切换目录并发 
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int res = 0;
    char sqlCmd[256] = {0};
    char tableName[256] = {0};
    char defaultFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    int64_t dataCount = 100000;
    uint32_t cmdLen = 0;
    int threadCount = 4;
    int memoryBefore = 0;
    int memoryAfter = 0;
    pthread_t tid[threadCount];

    InitTsCiCfgModify();
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    memoryBefore = GetKeyWordValue(NULL, NULL, NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T - 36000;
    (void)memset(tableName, 0, 256);
    (void)sprintf(tableName, "testdb1");
    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset(tableName, 0, 256);
    (void)sprintf(tableName, "testdb0");
    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo3 = {1, 2, 0};
    pthread_create(&tid[0], NULL, ConcurrentInsertInto, &createInfo3);
    // 等待注入开始
    sleep(3);
    ConcurrentSwapDir(&g_dataFilePath1);
    pthread_join(tid[0], NULL);

    createInfo0 = {1, 0, 0};
    createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    
    pthread_create(&tid[0], NULL, ConcurrentInsertInto, &createInfo3);
    pthread_join(tid[0], NULL);

    ConcurrentSwapDir(&defaultFilePath);

    pthread_create(&tid[0], NULL, ConcurrentInsertInto, &createInfo3);
    pthread_join(tid[0], NULL);

    
    for (int i = 0; i < 8; i++) {
        ConcurrentSwapDir(&g_dataFilePath1);
        pthread_create(&tid[0], NULL, ConcurrentInsertInto, &createInfo3);
        ConcurrentSwapDir(&defaultFilePath);
        pthread_join(tid[0], NULL);
    }

    ret = DropTable(stmt, tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    memoryAfter = GetKeyWordValue(NULL, NULL, NULL);
}

// 017.insert into逻辑表带table_path与切换目录并发 
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int res = 0;
    char sqlCmd[256] = {0};
    char tableName[256] = {0};
    char defaultFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    int64_t dataCount = 100000;
    uint32_t cmdLen = 0;
    int threadCount = 4;
    int memoryBefore = 0;
    int memoryAfter = 0;
    pthread_t tid[threadCount];

    InitTsCiCfgModify();
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    memoryBefore = GetKeyWordValue(NULL, NULL, NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T - 36000;
    (void)memset(tableName, 0, 256);
    (void)sprintf(tableName, "testdb1");
    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset(tableName, 0, 256);
    (void)sprintf(tableName, "testdb0");
    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo3 = {0, 2, 1};
    pthread_create(&tid[0], NULL, ConcurrentInsertInto, &createInfo3);
    // 等待注入开始
    sleep(3);
    ConcurrentSwapDir(&g_dataFilePath1);
    pthread_join(tid[0], NULL);

    createInfo0 = {1, 0, 0};
    createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    (void)memset(tableName, 0, 256);
    (void)sprintf(tableName, "testdb2");
    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo4 = {2, 2, 1};
    pthread_create(&tid[0], NULL, ConcurrentInsertInto, &createInfo3);
    pthread_create(&tid[1], NULL, ConcurrentInsertInto, &createInfo4);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);

    ConcurrentSwapDir(&defaultFilePath);

    pthread_create(&tid[0], NULL, ConcurrentInsertInto, &createInfo3);
    pthread_join(tid[0], NULL);

    
    for (int i = 0; i < 8; i++) {
        ConcurrentSwapDir(&g_dataFilePath1);
        pthread_create(&tid[0], NULL, ConcurrentInsertInto, &createInfo3);
        pthread_create(&tid[1], NULL, ConcurrentInsertInto, &createInfo4);
        ConcurrentSwapDir(&defaultFilePath);
        pthread_join(tid[0], NULL);
        pthread_join(tid[1], NULL);
    }

    (void)memset(tableName, 0, 256);
    (void)sprintf(tableName, "testdb1");
    ret = DropTable(stmt, tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset(tableName, 0, 256);
    (void)sprintf(tableName, "testdb0");
    ret = DropTable(stmt, tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    memoryAfter = GetKeyWordValue(NULL, NULL, NULL);
}

// 018.insert into带cache_size逻辑表与切换目录并发 
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int res = 0;
    char sqlCmd[256] = {0};
    char tableName[256] = {0};
    char defaultFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    int64_t dataCount = 100000;
    uint32_t cmdLen = 0;
    int threadCount = 4;
    int memoryBefore = 0;
    int memoryAfter = 0;
    pthread_t tid[threadCount];

    InitTsCiCfgModify();
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    memoryBefore = GetKeyWordValue(NULL, NULL, NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T - 36000;
    (void)memset(tableName, 0, 256);
    (void)sprintf(tableName, "testdb1");
    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset(tableName, 0, 256);
    (void)sprintf(tableName, "testdb0");
    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo3 = {0, 2, 1};
    pthread_create(&tid[0], NULL, ConcurrentInsertInto, &createInfo3);
    // 等待注入开始
    sleep(3);
    ConcurrentSwapDir(&g_dataFilePath1);
    pthread_join(tid[0], NULL);

    createInfo0 = {1, 0, 0};
    createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    (void)memset(tableName, 0, 256);
    (void)sprintf(tableName, "testdb2");
    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo4 = {2, 2, 1};
    pthread_create(&tid[0], NULL, ConcurrentInsertInto, &createInfo3);
    pthread_create(&tid[1], NULL, ConcurrentInsertInto, &createInfo4);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);

    ConcurrentSwapDir(&defaultFilePath);

    pthread_create(&tid[0], NULL, ConcurrentInsertInto, &createInfo3);
    pthread_join(tid[0], NULL);

    
    for (int i = 0; i < 8; i++) {
        ConcurrentSwapDir(&g_dataFilePath1);
        pthread_create(&tid[0], NULL, ConcurrentInsertInto, &createInfo3);
        pthread_create(&tid[1], NULL, ConcurrentInsertInto, &createInfo4);
        ConcurrentSwapDir(&defaultFilePath);
        pthread_join(tid[0], NULL);
        pthread_join(tid[1], NULL);
    }

    (void)memset(tableName, 0, 256);
    (void)sprintf(tableName, "testdb1");
    ret = DropTable(stmt, tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset(tableName, 0, 256);
    (void)sprintf(tableName, "testdb0");
    ret = DropTable(stmt, tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    memoryAfter = GetKeyWordValue(NULL, NULL, NULL);
}

// 019.修改ckptPeriod配置项为1，然后insert into数据和swap 预期：无异常
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int res = 0;
    char sqlCmd[256] = {0};
    char tableName[256] = {0};
    char tableName1[256] = {0};
    char tableName2[256] = {0};
    char defaultFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    int64_t dataCount = 100000;
    uint32_t cmdLen = 0;
    int threadCount = 4;
    int memoryBefore = 0;
    int memoryAfter = 0;
    pthread_t tid[threadCount];

// euler和仿真环境清共享内存，清理原有DB文件
#if defined RUN_INDEPENDENT
    system("sh $TEST_HOME/tools/stop.sh -f");
#endif
    system("sh $TEST_HOME/tools/stop.sh -ts");
    TsDefulatDbFileClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts recover");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"checkpoint=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    static GmcConnT *conn1 = NULL;
    static GmcStmtT *stmt1 = NULL;
    static GmcConnT *conn2 = NULL;
    static GmcStmtT *stmt2 = NULL;
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestTsGmcConnect(&conn1, &stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestTsGmcConnect(&conn2, &stmt2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    memoryBefore = GetKeyWordValue(NULL, NULL, NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T - 36000;
    (void)memset(tableName, 0, 256);
    (void)memset(tableName1, 0, 256);
    (void)memset(tableName2, 0, 256);
    (void)sprintf(tableName, "testdb0");
    (void)sprintf(tableName1, "testdb1");
    (void)sprintf(tableName2, "testdb2");
    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName1, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    CreateInfo createInfo3 = {0, 2, 1};
    pthread_create(&tid[0], NULL, ConcurrentInsertInto, &createInfo3);
    // 等待注入开始
    sleep(3);
    ConcurrentSwapDir(&g_dataFilePath1);
    pthread_join(tid[0], NULL);

    createInfo0 = {1, 0, 0};
    createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    
    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName1, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName2, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    pthread_create(&tid[0], NULL, ConcurrentInsertInto, &createInfo3);
    pthread_join(tid[0], NULL);

    ConcurrentSwapDir(&defaultFilePath);

    pthread_create(&tid[0], NULL, ConcurrentInsertInto, &createInfo3);
    pthread_join(tid[0], NULL);

    
    for (int i = 0; i < 8; i++) {
        ConcurrentSwapDir(&g_dataFilePath1);
        pthread_create(&tid[0], NULL, ConcurrentInsertInto, &createInfo3);
        ConcurrentSwapDir(&defaultFilePath);
        pthread_join(tid[0], NULL);
    }

    ret = DropTable(stmt, tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, tableName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    memoryAfter = GetKeyWordValue(NULL, NULL, NULL);    
}

// 020.copy to内存表与切换目录并发
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf ./data.csv");
    int res = 0;
    char sqlCmd[256] = {0};
    char tableName[256] = {0};
    char defaultFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    int64_t dataCount = 100000;
    uint32_t cmdLen = 0;
    int threadCount = 4;
    int memoryBefore = 0;
    int memoryAfter = 0;
    pthread_t tid[threadCount];

    InitTsCiCfgModify();
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    memoryBefore = GetKeyWordValue(NULL, NULL, NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T - 36000;
    (void)memset(tableName, 0, 256);
    (void)sprintf(tableName, "testdb2");
    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset(tableName, 0, 256);
    (void)sprintf(tableName, "testdb0");
    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo3 = {2, 2, 0};
    pthread_create(&tid[0], NULL, ConcurrentCopyTo, &createInfo3);
    // 等待注入开始
    sleep(3);
    ConcurrentSwapDir(&g_dataFilePath1);
    pthread_join(tid[0], NULL);

    createInfo0 = {1, 0, 0};
    createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    
    pthread_create(&tid[0], NULL, ConcurrentCopyTo, &createInfo3);
    pthread_join(tid[0], NULL);

    ConcurrentSwapDir(&defaultFilePath);

    pthread_create(&tid[0], NULL, ConcurrentCopyTo, &createInfo3);
    pthread_join(tid[0], NULL);

    
    for (int i = 0; i < 8; i++) {
        ConcurrentSwapDir(&g_dataFilePath1);
        pthread_create(&tid[0], NULL, ConcurrentCopyTo, &createInfo3);
        ConcurrentSwapDir(&defaultFilePath);
        pthread_join(tid[0], NULL);
    }

    ret = DropTable(stmt, tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    memoryAfter = GetKeyWordValue(NULL, NULL, NULL);
}

// 021.copy to逻辑表与切换目录并发 
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf ./data.csv");
    int res = 0;
    char sqlCmd[256] = {0};
    char tableName[256] = {0};
    char defaultFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    int64_t dataCount = 100000;
    uint32_t cmdLen = 0;
    int threadCount = 4;
    int memoryBefore = 0;
    int memoryAfter = 0;
    pthread_t tid[threadCount];

    InitTsCiCfgModify();
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    memoryBefore = GetKeyWordValue(NULL, NULL, NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T - 36000;
    (void)memset(tableName, 0, 256);
    (void)sprintf(tableName, "testdb1");
    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset(tableName, 0, 256);
    (void)sprintf(tableName, "testdb0");
    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo3 = {1, 2, 0};
    pthread_create(&tid[0], NULL, ConcurrentCopyTo, &createInfo3);
    // 等待注入开始
    sleep(3);
    ConcurrentSwapDir(&g_dataFilePath1);
    pthread_join(tid[0], NULL);

    createInfo0 = {1, 0, 0};
    createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    
    pthread_create(&tid[0], NULL, ConcurrentCopyTo, &createInfo3);
    pthread_join(tid[0], NULL);

    ConcurrentSwapDir(&defaultFilePath);

    pthread_create(&tid[0], NULL, ConcurrentCopyTo, &createInfo3);
    pthread_join(tid[0], NULL);

    
    for (int i = 0; i < 8; i++) {
        ConcurrentSwapDir(&g_dataFilePath1);
        pthread_create(&tid[0], NULL, ConcurrentCopyTo, &createInfo3);
        ConcurrentSwapDir(&defaultFilePath);
        pthread_join(tid[0], NULL);
    }

    ret = DropTable(stmt, tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    memoryAfter = GetKeyWordValue(NULL, NULL, NULL);
}

// 022.copy to逻辑表带table_path与切换目录并发 
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf ./data.csv");
    int res = 0;
    char sqlCmd[256] = {0};
    char tableName[256] = {0};
    char defaultFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    int64_t dataCount = 100000;
    uint32_t cmdLen = 0;
    int threadCount = 4;
    int memoryBefore = 0;
    int memoryAfter = 0;
    pthread_t tid[threadCount];

    InitTsCiCfgModify();
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    memoryBefore = GetKeyWordValue(NULL, NULL, NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T - 36000;
    (void)memset(tableName, 0, 256);
    (void)sprintf(tableName, "testdb1");
    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset(tableName, 0, 256);
    (void)sprintf(tableName, "testdb0");
    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo3 = {0, 2, 1};
    pthread_create(&tid[0], NULL, ConcurrentCopyTo, &createInfo3);
    // 等待注入开始
    sleep(3);
    ConcurrentSwapDir(&g_dataFilePath1);
    pthread_join(tid[0], NULL);

    createInfo0 = {1, 0, 0};
    createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    (void)memset(tableName, 0, 256);
    (void)sprintf(tableName, "testdb2");
    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo4 = {2, 2, 1};
    pthread_create(&tid[0], NULL, ConcurrentCopyTo, &createInfo3);
    pthread_create(&tid[1], NULL, ConcurrentCopyTo, &createInfo4);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);

    ConcurrentSwapDir(&defaultFilePath);

    pthread_create(&tid[0], NULL, ConcurrentInsertInto, &createInfo3);
    pthread_join(tid[0], NULL);

    
    for (int i = 0; i < 8; i++) {
        ConcurrentSwapDir(&g_dataFilePath1);
        pthread_create(&tid[0], NULL, ConcurrentCopyTo, &createInfo3);
        pthread_create(&tid[1], NULL, ConcurrentCopyTo, &createInfo4);
        ConcurrentSwapDir(&defaultFilePath);
        pthread_join(tid[0], NULL);
        pthread_join(tid[1], NULL);
    }

    (void)memset(tableName, 0, 256);
    (void)sprintf(tableName, "testdb1");
    ret = DropTable(stmt, tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset(tableName, 0, 256);
    (void)sprintf(tableName, "testdb0");
    ret = DropTable(stmt, tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    memoryAfter = GetKeyWordValue(NULL, NULL, NULL);
}

// 023.copy to带cache_size逻辑表与切换目录并发
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf ./data.csv");
    int res = 0;
    char sqlCmd[256] = {0};
    char tableName[256] = {0};
    char defaultFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    int64_t dataCount = 100000;
    uint32_t cmdLen = 0;
    int threadCount = 4;
    int memoryBefore = 0;
    int memoryAfter = 0;
    pthread_t tid[threadCount];

    InitTsCiCfgModify();
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    memoryBefore = GetKeyWordValue(NULL, NULL, NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T - 36000;
    (void)memset(tableName, 0, 256);
    (void)sprintf(tableName, "testdb1");
    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset(tableName, 0, 256);
    (void)sprintf(tableName, "testdb0");
    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo3 = {0, 2, 1};
    pthread_create(&tid[0], NULL, ConcurrentCopyTo, &createInfo3);
    // 等待注入开始
    sleep(3);
    ConcurrentSwapDir(&g_dataFilePath1);
    pthread_join(tid[0], NULL);

    createInfo0 = {1, 0, 0};
    createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    (void)memset(tableName, 0, 256);
    (void)sprintf(tableName, "testdb2");
    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo4 = {2, 2, 1};
    pthread_create(&tid[0], NULL, ConcurrentCopyTo, &createInfo3);
    pthread_create(&tid[1], NULL, ConcurrentCopyTo, &createInfo4);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);

    ConcurrentSwapDir(&defaultFilePath);

    pthread_create(&tid[0], NULL, ConcurrentCopyTo, &createInfo3);
    pthread_join(tid[0], NULL);

    
    for (int i = 0; i < 8; i++) {
        ConcurrentSwapDir(&g_dataFilePath1);
        pthread_create(&tid[0], NULL, ConcurrentCopyTo, &createInfo3);
        pthread_create(&tid[1], NULL, ConcurrentCopyTo, &createInfo4);
        ConcurrentSwapDir(&defaultFilePath);
        pthread_join(tid[0], NULL);
        pthread_join(tid[1], NULL);
    }

    (void)memset(tableName, 0, 256);
    (void)sprintf(tableName, "testdb1");
    ret = DropTable(stmt, tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset(tableName, 0, 256);
    (void)sprintf(tableName, "testdb0");
    ret = DropTable(stmt, tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    memoryAfter = GetKeyWordValue(NULL, NULL, NULL);
}

// 024.修改ckptPeriod配置项为1，然后copy to数据和swap 预期：无异常
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf ./data.csv");
    int res = 0;
    char sqlCmd[256] = {0};
    char tableName[256] = {0};
    char tableName1[256] = {0};
    char tableName2[256] = {0};
    char defaultFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    int64_t dataCount = 100000;
    uint32_t cmdLen = 0;
    int threadCount = 4;
    int memoryBefore = 0;
    int memoryAfter = 0;
    pthread_t tid[threadCount];

// euler和仿真环境清共享内存，清理原有DB文件
#if defined RUN_INDEPENDENT
    system("sh $TEST_HOME/tools/stop.sh -f");
#endif
    system("sh $TEST_HOME/tools/stop.sh -ts");
    TsDefulatDbFileClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts recover");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"checkpoint=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    static GmcConnT *conn1 = NULL;
    static GmcStmtT *stmt1 = NULL;
    static GmcConnT *conn2 = NULL;
    static GmcStmtT *stmt2 = NULL;
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestTsGmcConnect(&conn1, &stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestTsGmcConnect(&conn2, &stmt2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    memoryBefore = GetKeyWordValue(NULL, NULL, NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T - 36000;
    (void)memset(tableName, 0, 256);
    (void)memset(tableName1, 0, 256);
    (void)memset(tableName2, 0, 256);
    (void)sprintf(tableName, "testdb0");
    (void)sprintf(tableName1, "testdb1");
    (void)sprintf(tableName2, "testdb2");
    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName1, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    CreateInfo createInfo3 = {0, 2, 1};
    pthread_create(&tid[0], NULL, ConcurrentCopyTo, &createInfo3);
    // 等待注入开始
    sleep(3);
    ConcurrentSwapDir(&g_dataFilePath1);
    pthread_join(tid[0], NULL);

    createInfo0 = {1, 0, 0};
    createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    
    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName1, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName2, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    pthread_create(&tid[0], NULL, ConcurrentCopyTo, &createInfo3);
    pthread_join(tid[0], NULL);

    ConcurrentSwapDir(&defaultFilePath);

    pthread_create(&tid[0], NULL, ConcurrentCopyTo, &createInfo3);
    pthread_join(tid[0], NULL);

    
    for (int i = 0; i < 8; i++) {
        ConcurrentSwapDir(&g_dataFilePath1);
        pthread_create(&tid[0], NULL, ConcurrentCopyTo, &createInfo3);
        ConcurrentSwapDir(&defaultFilePath);
        pthread_join(tid[0], NULL);
    }

    ret = DropTable(stmt, tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, tableName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    memoryAfter = GetKeyWordValue(NULL, NULL, NULL);    
}

// 025.select内存表与切换目录并发
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int res = 0;
    char sqlCmd[256] = {0};
    char tableName[256] = {"testdb0"};
    char tableName1[256] = {"testdb1"};
    char tableName2[256] = {"testdb2"};
    char defaultFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    int64_t dataCount = 100000;
    uint32_t cmdLen = 0;
    int threadCount = 4;
    int memoryBefore = 0;
    int memoryAfter = 0;
    pthread_t tid[threadCount];

    InitTsCiCfgModify();
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    memoryBefore = GetKeyWordValue(NULL, NULL, NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T - 36000;
    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName2, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    InsertQueryDataType constructDataType_1 = {stmt, tableName2, dataCount, startTime};
    pthread_create(&tid[0], NULL, QueryDataToTable, &constructDataType_1);
    // 等待注入开始
    sleep(3);
    ConcurrentSwapDir(&g_dataFilePath1);
    pthread_join(tid[0], NULL);

    createInfo0 = {1, 0, 0};
    createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    
    pthread_create(&tid[0], NULL, QueryDataToTable, &constructDataType_1);
    pthread_join(tid[0], NULL);

    ConcurrentSwapDir(&defaultFilePath);

    pthread_create(&tid[0], NULL, QueryDataToTable, &constructDataType_1);
    pthread_join(tid[0], NULL);

    
    for (int i = 0; i < 8; i++) {
        ConcurrentSwapDir(&g_dataFilePath1);
        pthread_create(&tid[0], NULL, QueryDataToTable, &constructDataType_1);
        ConcurrentSwapDir(&defaultFilePath);
        pthread_join(tid[0], NULL);
    }

    ret = DropTable(stmt, tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    memoryAfter = GetKeyWordValue(NULL, NULL, NULL);
}

// 026.select逻辑表与切换目录并发 
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int res = 0;
    char sqlCmd[256] = {0};
    char tableName[256] = {"testdb0"};
    char tableName1[256] = {"testdb1"};
    char tableName2[256] = {"testdb2"};
    char defaultFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    int64_t dataCount = 100000;
    uint32_t cmdLen = 0;
    int threadCount = 4;
    int memoryBefore = 0;
    int memoryAfter = 0;
    pthread_t tid[threadCount];

    InitTsCiCfgModify();
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    memoryBefore = GetKeyWordValue(NULL, NULL, NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T - 36000;
    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName1, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    InsertQueryDataType constructDataType_1 = {stmt, tableName, dataCount, startTime};
    pthread_create(&tid[0], NULL, QueryDataToTable, &constructDataType_1);
    // 等待注入开始
    sleep(3);
    ConcurrentSwapDir(&g_dataFilePath1);
    pthread_join(tid[0], NULL);

    createInfo0 = {1, 0, 0};
    createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    
    pthread_create(&tid[0], NULL, QueryDataToTable, &constructDataType_1);
    pthread_join(tid[0], NULL);

    ConcurrentSwapDir(&defaultFilePath);

    pthread_create(&tid[0], NULL, QueryDataToTable, &constructDataType_1);
    pthread_join(tid[0], NULL);

    
    for (int i = 0; i < 8; i++) {
        ConcurrentSwapDir(&g_dataFilePath1);
        pthread_create(&tid[0], NULL, QueryDataToTable, &constructDataType_1);
        ConcurrentSwapDir(&defaultFilePath);
        pthread_join(tid[0], NULL);
    }

    ret = DropTable(stmt, tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, tableName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    memoryAfter = GetKeyWordValue(NULL, NULL, NULL);
}

// 027.select逻辑表带table_path与切换目录并发 
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int res = 0;
    char sqlCmd[256] = {0};
    char tableName[256] = {"testdb0"};
    char tableName1[256] = {"testdb1"};
    char tableName2[256] = {"testdb2"};
    char defaultFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    int64_t dataCount = 100000;
    uint32_t cmdLen = 0;
    int threadCount = 4;
    int memoryBefore = 0;
    int memoryAfter = 0;
    pthread_t tid[threadCount];

    InitTsCiCfgModify();
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    memoryBefore = GetKeyWordValue(NULL, NULL, NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T - 36000;
    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName1, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    InsertQueryDataType constructDataType_1 = {stmt, tableName1, dataCount, startTime};
    pthread_create(&tid[0], NULL, QueryDataToTable, &constructDataType_1);
    // 等待注入开始
    sleep(3);
    ConcurrentSwapDir(&g_dataFilePath1);
    pthread_join(tid[0], NULL);

    createInfo0 = {1, 0, 0};
    createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    
    pthread_create(&tid[0], NULL, QueryDataToTable, &constructDataType_1);
    pthread_join(tid[0], NULL);

    ConcurrentSwapDir(&defaultFilePath);

    pthread_create(&tid[0], NULL, QueryDataToTable, &constructDataType_1);
    pthread_join(tid[0], NULL);

    
    for (int i = 0; i < 8; i++) {
        ConcurrentSwapDir(&g_dataFilePath1);
        pthread_create(&tid[0], NULL, QueryDataToTable, &constructDataType_1);
        ConcurrentSwapDir(&defaultFilePath);
        pthread_join(tid[0], NULL);
    }

    ret = DropTable(stmt, tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, tableName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    memoryAfter = GetKeyWordValue(NULL, NULL, NULL);
}

// 028.select带cache_size逻辑表与切换目录并发 
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int res = 0;
    char sqlCmd[256] = {0};
    char tableName[256] = {"testdb0"};
    char tableName1[256] = {"testdb1"};
    char tableName2[256] = {"testdb2"};
    char defaultFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    int64_t dataCount = 100000;
    uint32_t cmdLen = 0;
    int threadCount = 4;
    int memoryBefore = 0;
    int memoryAfter = 0;
    pthread_t tid[threadCount];

    InitTsCiCfgModify();
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    static GmcConnT *conn1 = NULL;
    static GmcStmtT *stmt1 = NULL;
    ret = TestTsGmcConnect(&conn1, &stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    memoryBefore = GetKeyWordValue(NULL, NULL, NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T - 36000;
    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName1, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    InsertQueryDataType constructDataType_1 = {stmt, tableName, dataCount, startTime};
    InsertQueryDataType constructDataType_2 = {stmt1, tableName1, dataCount, startTime};
    pthread_create(&tid[0], NULL, QueryDataToTable, &constructDataType_1);
    pthread_create(&tid[1], NULL, QueryDataToTable, &constructDataType_2);
    // 等待注入开始
    sleep(3);
    ConcurrentSwapDir(&g_dataFilePath1);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);

    

    createInfo0 = {1, 0, 0};
    createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    
    pthread_create(&tid[0], NULL, QueryDataToTable, &constructDataType_1);
    pthread_join(tid[0], NULL);
    ConcurrentSwapDir(&defaultFilePath);

    pthread_create(&tid[0], NULL, QueryDataToTable, &constructDataType_1);
    pthread_join(tid[0], NULL);

    
    for (int i = 0; i < 8; i++) {
        ConcurrentSwapDir(&g_dataFilePath1);
        pthread_create(&tid[0], NULL, QueryDataToTable, &constructDataType_1);
        pthread_create(&tid[1], NULL, QueryDataToTable, &constructDataType_2);
        ConcurrentSwapDir(&defaultFilePath);
        pthread_join(tid[0], NULL);
        pthread_join(tid[1], NULL);
    }
    ret = DropTable(stmt, tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, tableName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memoryAfter = GetKeyWordValue(NULL, NULL, NULL);
}

// 029.外排和切换目录并发，先触发外排 
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int res = 0;
    char sqlCmd[256] = {0};
    char tableName[256] = {"testdb0"};
    char tableName1[256] = {"testdb1"};
    char tableName2[256] = {"testdb2"};
    char defaultFilePath[256] = {0};
    char tempFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    (void)sprintf(tempFilePath, "%s", TEMPFILE_PATH);
    int64_t dataCount = 1000000;
    uint32_t cmdLen = 0;
    int threadCount = 4;
    int memoryBefore = 0;
    int memoryAfter = 0;
    pthread_t tid[threadCount];

    InitTsCiCfgModify();
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    memoryBefore = GetKeyWordValue(NULL, NULL, NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T - 36000;
    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName1, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    pthread_create(&tid[0], NULL, ConcurrentQueryDataWithOutOrder, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentQueryDataWithOutOrder, &createInfo1);
    // 等待注入开始
    sleep(3);
    ConcurrentSwapDir(&g_dataFilePath1);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);

    createInfo0 = {1, 0, 0};
    createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    
    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName1, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName2, 100000, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    pthread_create(&tid[0], NULL, ConcurrentQueryDataWithOutOrder, &createInfo0);
    int fetchTimes = 0;
    while (true) {
        ret = GetTempFileNumber(tempFilePath);
        if (ret != 0 || fetchTimes > 100) {
            break;
        }
        fetchTimes++;
    }
    pthread_join(tid[0], NULL);

    ConcurrentSwapDir(&defaultFilePath);
    ret = GetTempFileNumber(tempFilePath);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    pthread_create(&tid[0], NULL, ConcurrentQueryDataWithOutOrder, &createInfo1);
    fetchTimes = 0;
    while (true) {
        ret = GetTempFileNumber(tempFilePath);
        if (ret != 0 || fetchTimes > 100) {
            break;
        }
        fetchTimes++;
    }
    pthread_join(tid[0], NULL);

    
    for (int i = 0; i < 2; i++) {
        ConcurrentSwapDir(&g_dataFilePath1);
        pthread_create(&tid[0], NULL, ConcurrentQueryDataWithOutOrder, &createInfo0);
        pthread_create(&tid[0], NULL, ConcurrentQueryDataWithOutOrder, &createInfo1);
        ConcurrentSwapDir(&defaultFilePath);
        pthread_join(tid[0], NULL);
        pthread_join(tid[1], NULL);
    }

    ret = DropTable(stmt, tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, tableName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    memoryAfter = GetKeyWordValue(NULL, NULL, NULL);
}

// 030.大报文查询内存表与切换目录并发
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int res = 0;
    char sqlCmd[256] = {0};
    char tableName[256] = {"testdb0"};
    char tableName1[256] = {"testdb1"};
    char tableName2[256] = {"testdb2"};
    char defaultFilePath[256] = {0};
    char tempFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    (void)sprintf(tempFilePath, "%s", TEMPFILE_PATH);
    int64_t dataCount = 1000000;
    uint32_t cmdLen = 0;
    int threadCount = 4;
    int memoryBefore = 0;
    int memoryAfter = 0;
    pthread_t tid[threadCount];

    InitTsCiCfgModify();
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    memoryBefore = GetKeyWordValue(NULL, NULL, NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T - 36000;
    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName2, 100000, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    pthread_create(&tid[0], NULL, ConcurrentQueryDataWithOutOrder, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentQueryDataWithOutOrder, &createInfo1);
    // 等待注入开始
    sleep(3);
    ConcurrentSwapDir(&g_dataFilePath1);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);

    createInfo0 = {1, 0, 0};
    createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    
    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName1, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName2, 100000, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    pthread_create(&tid[0], NULL, ConcurrentQueryDataWithOutOrder, &createInfo2);
    int fetchTimes = 0;
    while (true) {
        ret = GetTempFileNumber(tempFilePath);
        if (ret != 0 || fetchTimes > 100) {
            break;
        }
        fetchTimes++;
    }
    pthread_join(tid[0], NULL);

    ConcurrentSwapDir(&defaultFilePath);
    ret = GetTempFileNumber(tempFilePath);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    pthread_create(&tid[0], NULL, ConcurrentQueryDataWithOutOrder, &createInfo2);
    fetchTimes = 0;
    while (true) {
        ret = GetTempFileNumber(tempFilePath);
        if (ret != 0 || fetchTimes > 100) {
            break;
        }
        fetchTimes++;
    }
    pthread_join(tid[0], NULL);

    
    for (int i = 0; i < 2; i++) {
        ConcurrentSwapDir(&g_dataFilePath1);
        pthread_create(&tid[0], NULL, ConcurrentQueryDataWithOutOrder, &createInfo0);
        pthread_create(&tid[0], NULL, ConcurrentQueryDataWithOutOrder, &createInfo2);
        ConcurrentSwapDir(&defaultFilePath);
        pthread_join(tid[0], NULL);
        pthread_join(tid[1], NULL);
    }

    ret = DropTable(stmt, tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    memoryAfter = GetKeyWordValue(NULL, NULL, NULL);
}

// 031.大报文查询逻辑表与切换目录并发 
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int res = 0;
    char sqlCmd[256] = {0};
    char tableName[256] = {"testdb0"};
    char tableName1[256] = {"testdb1"};
    char tableName2[256] = {"testdb2"};
    char defaultFilePath[256] = {0};
    char tempFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    (void)sprintf(tempFilePath, "%s", TEMPFILE_PATH);
    int64_t dataCount = 1000000;
    uint32_t cmdLen = 0;
    int threadCount = 4;
    int memoryBefore = 0;
    int memoryAfter = 0;
    pthread_t tid[threadCount];

    InitTsCiCfgModify();
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    memoryBefore = GetKeyWordValue(NULL, NULL, NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T - 36000;
    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName1, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    pthread_create(&tid[0], NULL, ConcurrentQueryDataWithOutOrder, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentQueryDataWithOutOrder, &createInfo1);
    // 等待注入开始
    sleep(3);
    ConcurrentSwapDir(&g_dataFilePath1);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);

    createInfo0 = {1, 0, 0};
    createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    
    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName1, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName2, 100000, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    pthread_create(&tid[0], NULL, ConcurrentQueryDataWithOutOrder, &createInfo0);
    int fetchTimes = 0;
    while (true) {
        ret = GetTempFileNumber(tempFilePath);
        if (ret != 0 || fetchTimes > 100) {
            break;
        }
        fetchTimes++;
    }
    pthread_join(tid[0], NULL);

    ConcurrentSwapDir(&defaultFilePath);
    ret = GetTempFileNumber(tempFilePath);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    pthread_create(&tid[0], NULL, ConcurrentQueryDataWithOutOrder, &createInfo0);
    fetchTimes = 0;
    while (true) {
        ret = GetTempFileNumber(tempFilePath);
        if (ret != 0 || fetchTimes > 100) {
            break;
        }
        fetchTimes++;
    }
    pthread_join(tid[0], NULL);

    
    for (int i = 0; i < 2; i++) {
        ConcurrentSwapDir(&g_dataFilePath1);
        pthread_create(&tid[0], NULL, ConcurrentQueryDataWithOutOrder, &createInfo0);
        pthread_create(&tid[0], NULL, ConcurrentQueryDataWithOutOrder, &createInfo1);
        ConcurrentSwapDir(&defaultFilePath);
        pthread_join(tid[0], NULL);
        pthread_join(tid[1], NULL);
    }

    ret = DropTable(stmt, tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, tableName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    memoryAfter = GetKeyWordValue(NULL, NULL, NULL);
}

// 032.大报文查询逻辑表带table_path与切换目录并发 
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int res = 0;
    char sqlCmd[256] = {0};
    char tableName[256] = {"testdb0"};
    char tableName1[256] = {"testdb1"};
    char tableName2[256] = {"testdb2"};
    char defaultFilePath[256] = {0};
    char tempFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    (void)sprintf(tempFilePath, "%s", TEMPFILE_PATH);
    int64_t dataCount = 1000000;
    uint32_t cmdLen = 0;
    int threadCount = 4;
    int memoryBefore = 0;
    int memoryAfter = 0;
    pthread_t tid[threadCount];

    InitTsCiCfgModify();
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    memoryBefore = GetKeyWordValue(NULL, NULL, NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T - 36000;
    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName1, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    pthread_create(&tid[0], NULL, ConcurrentQueryDataWithOutOrder, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentQueryDataWithOutOrder, &createInfo1);
    // 等待注入开始
    sleep(3);
    ConcurrentSwapDir(&g_dataFilePath1);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);

    createInfo0 = {1, 0, 0};
    createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    
    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName1, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName2, 100000, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    pthread_create(&tid[0], NULL, ConcurrentQueryDataWithOutOrder, &createInfo1);
    int fetchTimes = 0;
    while (true) {
        ret = GetTempFileNumber(tempFilePath);
        if (ret != 0 || fetchTimes > 100) {
            break;
        }
        fetchTimes++;
    }
    pthread_join(tid[0], NULL);

    ConcurrentSwapDir(&defaultFilePath);
    ret = GetTempFileNumber(tempFilePath);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    pthread_create(&tid[0], NULL, ConcurrentQueryDataWithOutOrder, &createInfo1);
    fetchTimes = 0;
    while (true) {
        ret = GetTempFileNumber(tempFilePath);
        if (ret != 0 || fetchTimes > 100) {
            break;
        }
        fetchTimes++;
    }
    pthread_join(tid[0], NULL);

    
    for (int i = 0; i < 2; i++) {
        ConcurrentSwapDir(&g_dataFilePath1);
        pthread_create(&tid[0], NULL, ConcurrentQueryDataWithOutOrder, &createInfo0);
        pthread_create(&tid[0], NULL, ConcurrentQueryDataWithOutOrder, &createInfo1);
        ConcurrentSwapDir(&defaultFilePath);
        pthread_join(tid[0], NULL);
        pthread_join(tid[1], NULL);
    }

    ret = DropTable(stmt, tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, tableName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    memoryAfter = GetKeyWordValue(NULL, NULL, NULL);
}

// 033.select带cache_size逻辑表与切换目录并发 
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int res = 0;
    char sqlCmd[256] = {0};
    char tableName[256] = {"testdb0"};
    char tableName1[256] = {"testdb1"};
    char tableName2[256] = {"testdb2"};
    char defaultFilePath[256] = {0};
    char tempFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    (void)sprintf(tempFilePath, "%s", TEMPFILE_PATH);
    int64_t dataCount = 1000000;
    uint32_t cmdLen = 0;
    int threadCount = 4;
    int memoryBefore = 0;
    int memoryAfter = 0;
    pthread_t tid[threadCount];

    InitTsCiCfgModify();
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    memoryBefore = GetKeyWordValue(NULL, NULL, NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T - 36000;
    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName1, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    pthread_create(&tid[0], NULL, ConcurrentQueryDataWithOutOrder, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentQueryDataWithOutOrder, &createInfo1);
    // 等待注入开始
    sleep(3);
    ConcurrentSwapDir(&g_dataFilePath1);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);

    createInfo0 = {1, 0, 0};
    createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    
    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName1, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName2, 100000, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    pthread_create(&tid[0], NULL, ConcurrentQueryDataWithOutOrder, &createInfo1);
    int fetchTimes = 0;
    while (true) {
        ret = GetTempFileNumber(tempFilePath);
        if (ret != 0 || fetchTimes > 100) {
            break;
        }
        fetchTimes++;
    }
    pthread_join(tid[0], NULL);

    ConcurrentSwapDir(&defaultFilePath);
    ret = GetTempFileNumber(tempFilePath);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    pthread_create(&tid[0], NULL, ConcurrentQueryDataWithOutOrder, &createInfo1);
    fetchTimes = 0;
    while (true) {
        ret = GetTempFileNumber(tempFilePath);
        if (ret != 0 || fetchTimes > 100) {
            break;
        }
        fetchTimes++;
    }
    pthread_join(tid[0], NULL);

    
    for (int i = 0; i < 2; i++) {
        ConcurrentSwapDir(&g_dataFilePath1);
        pthread_create(&tid[0], NULL, ConcurrentQueryDataWithOutOrder, &createInfo0);
        pthread_create(&tid[0], NULL, ConcurrentQueryDataWithOutOrder, &createInfo1);
        ConcurrentSwapDir(&defaultFilePath);
        pthread_join(tid[0], NULL);
        pthread_join(tid[1], NULL);
    }

    ret = DropTable(stmt, tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, tableName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    memoryAfter = GetKeyWordValue(NULL, NULL, NULL);
}

// 034.gmsysciew查询视图与切换目录并发
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int res = 0;
    char sqlCmd[256] = {0};
    char sqlCmd1[256] = {0};
    char sqlCmd2[256] = {0};
    char tableName[256] = {"testdb0"};
    char tableName1[256] = {"testdb1"};
    char tableName2[256] = {"testdb2"};
    char defaultFilePath[256] = {0};
    char tempFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    (void)sprintf(tempFilePath, "%s", TEMPFILE_PATH);
    int64_t dataCount = 3000;
    uint32_t cmdLen = 0;
    int threadCount = 4;
    int memoryBefore = 0;
    int memoryAfter = 0;
    pthread_t tid[threadCount];

    InitTsCiCfgModify();
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    memoryBefore = GetKeyWordValue(NULL, NULL, NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T - 36000;
    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName1, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName2, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    (void)sprintf(sqlCmd1, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE'\" -s %s", g_connServerTsdb);
    (void)sprintf(sqlCmd2, "gmsysview -sql \"select * from 'V\\$TS_TBL_OPER_STATIS'\" -s %s", g_connServerTsdb);
    InsertQueryDataType constructDataType_1 = {stmt, sqlCmd, dataCount, startTime};
    InsertQueryDataType constructDataType_2 = {stmt, sqlCmd1, dataCount, startTime};
    InsertQueryDataType constructDataType_3 = {stmt, sqlCmd2, dataCount, startTime};
    pthread_create(&tid[0], NULL, ConcurrentGmsysview, &constructDataType_1);
    pthread_create(&tid[1], NULL, ConcurrentGmsysview, &constructDataType_2);
    pthread_create(&tid[2], NULL, ConcurrentGmsysview, &constructDataType_3);
    ConcurrentSwapDir(&g_dataFilePath1);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);

    
    pthread_create(&tid[0], NULL, ConcurrentGmsysview, &constructDataType_1);
    pthread_join(tid[0], NULL);
    AW_MACRO_ASSERT_EQ_INT(g_concurrentrStatus, ret);

    ConcurrentSwapDir(&defaultFilePath);

    pthread_create(&tid[0], NULL, ConcurrentGmsysview, &constructDataType_1);
    pthread_join(tid[0], NULL);

    
    for (int i = 0; i < 2; i++) {
        ConcurrentSwapDir(&g_dataFilePath1);
        pthread_create(&tid[0], NULL, ConcurrentGmsysview, &constructDataType_1);
        pthread_create(&tid[0], NULL, ConcurrentGmsysview, &constructDataType_2);
        ConcurrentSwapDir(&defaultFilePath);
        pthread_join(tid[0], NULL);
        pthread_join(tid[1], NULL);
    }

    ret = DropTable(stmt, tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, tableName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    memoryAfter = GetKeyWordValue(NULL, NULL, NULL);
}

// 035.truncate内存表与切换目录并发 
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int res = 0;
    char sqlCmd[256] = {0};
    char tableName[256] = {"testdb0"};
    char tableName1[256] = {"testdb1"};
    char tableName2[256] = {"testdb2"};
    char defaultFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    int64_t dataCount = 3000;
    uint32_t cmdLen = 0;
    int threadCount = 4;
    int memoryBefore = 0;
    int memoryAfter = 0;
    pthread_t tid[threadCount];

    InitTsCiCfgModify();
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo0 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_join(tid[0], NULL);
    memoryBefore = GetKeyWordValue(NULL, NULL, NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T - 36000;
    ret = rowInsertData(stmt, tableName2, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    InsertQueryDataType constructDataType_1 = {stmt, tableName2, dataCount, startTime};

    pthread_create(&tid[0], NULL, ConcurrentTruncate, &createInfo0);
    ConcurrentSwapDir(&g_dataFilePath1);
    pthread_join(tid[0], NULL);

    createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam1, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);

    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName1, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName2, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    pthread_create(&tid[0], NULL, ConcurrentTruncate, &createInfo0);
    pthread_join(tid[0], NULL);
    AW_MACRO_ASSERT_EQ_INT(g_concurrentrStatus, GMERR_OK);


    ConcurrentSwapDir(&defaultFilePath);

    pthread_create(&tid[0], NULL, ConcurrentTruncate, &createInfo2);
    pthread_join(tid[0], NULL);
    AW_MACRO_ASSERT_EQ_INT(g_concurrentrStatus, GMERR_OK);

    
    for (int i = 0; i < 8; i++) {
        ConcurrentSwapDir(&g_dataFilePath1);
        pthread_create(&tid[0], NULL, ConcurrentTruncate, &createInfo2);
        ConcurrentSwapDir(&defaultFilePath);
        pthread_join(tid[0], NULL);
    }

    ret = DropTable(stmt, tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    memoryAfter = GetKeyWordValue(NULL, NULL, NULL);    
}

// 036.truncate逻辑表与切换目录并发
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int res = 0;
    char sqlCmd[256] = {0};
    char tableName[256] = {"testdb0"};
    char tableName1[256] = {"testdb1"};
    char tableName2[256] = {"testdb2"};
    char defaultFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    int64_t dataCount = 3000;
    uint32_t cmdLen = 0;
    int threadCount = 4;
    int memoryBefore = 0;
    int memoryAfter = 0;
    pthread_t tid[threadCount];

    InitTsCiCfgModify();
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo0 = {1, 0, 0};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_join(tid[0], NULL);
    memoryBefore = GetKeyWordValue(NULL, NULL, NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T - 36000;
    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    pthread_create(&tid[0], NULL, ConcurrentTruncate, &createInfo0);
    ConcurrentSwapDir(&g_dataFilePath1);
    pthread_join(tid[0], NULL);

    createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam1, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    
    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName1, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName2, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    pthread_create(&tid[0], NULL, ConcurrentTruncate, &createInfo0);
    pthread_join(tid[0], NULL);
    AW_MACRO_ASSERT_EQ_INT(g_concurrentrStatus, GMERR_OK);


    ConcurrentSwapDir(&defaultFilePath);

    pthread_create(&tid[0], NULL, ConcurrentTruncate, &createInfo0);
    pthread_join(tid[0], NULL);
    AW_MACRO_ASSERT_EQ_INT(g_concurrentrStatus, GMERR_OK);

    
    for (int i = 0; i < 8; i++) {
        ConcurrentSwapDir(&g_dataFilePath1);
        pthread_create(&tid[0], NULL, ConcurrentTruncate, &createInfo0);
        ConcurrentSwapDir(&defaultFilePath);
        pthread_join(tid[0], NULL);
    }

    ret = DropTable(stmt, tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    memoryAfter = GetKeyWordValue(NULL, NULL, NULL);    
}

// 037.truncate逻辑表带table_path与切换目录并发
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int res = 0;
    char sqlCmd[256] = {0};
    char tableName[256] = {"testdb0"};
    char tableName1[256] = {"testdb1"};
    char tableName2[256] = {"testdb2"};
    char defaultFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    int64_t dataCount = 3000;
    uint32_t cmdLen = 0;
    int threadCount = 4;
    int memoryBefore = 0;
    int memoryAfter = 0;
    pthread_t tid[threadCount];

    InitTsCiCfgModify();
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo0 = {1, 1, 1};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_join(tid[0], NULL);
    memoryBefore = GetKeyWordValue(NULL, NULL, NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T - 36000;
    ret = rowInsertData(stmt, tableName1, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    pthread_create(&tid[0], NULL, ConcurrentTruncate, &createInfo0);
    ConcurrentSwapDir(&g_dataFilePath1);
    pthread_join(tid[0], NULL);

    createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam1, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    
    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName1, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName2, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    pthread_create(&tid[0], NULL, ConcurrentTruncate, &createInfo1);
    pthread_join(tid[0], NULL);
    AW_MACRO_ASSERT_EQ_INT(g_concurrentrStatus, GMERR_OK);


    ConcurrentSwapDir(&defaultFilePath);

    pthread_create(&tid[0], NULL, ConcurrentTruncate, &createInfo1);
    pthread_join(tid[0], NULL);
    AW_MACRO_ASSERT_EQ_INT(g_concurrentrStatus, GMERR_OK);

    
    for (int i = 0; i < 8; i++) {
        ConcurrentSwapDir(&g_dataFilePath1);
        pthread_create(&tid[0], NULL, ConcurrentTruncate, &createInfo1);
        ConcurrentSwapDir(&defaultFilePath);
        pthread_join(tid[0], NULL);
    }

    ret = DropTable(stmt, tableName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    memoryAfter = GetKeyWordValue(NULL, NULL, NULL);
}

// 038.truncate带cache_size逻辑表与切换目录并发
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int res = 0;
    char sqlCmd[256] = {0};
    char tableName[256] = {"testdb0"};
    char tableName1[256] = {"testdb1"};
    char tableName2[256] = {"testdb2"};
    char defaultFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    int64_t dataCount = 3000;
    uint32_t cmdLen = 0;
    int threadCount = 4;
    int memoryBefore = 0;
    int memoryAfter = 0;
    pthread_t tid[threadCount];

    InitTsCiCfgModify();
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo0 = {1, 1, 1};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_join(tid[0], NULL);
    memoryBefore = GetKeyWordValue(NULL, NULL, NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T - 36000;
    ret = rowInsertData(stmt, tableName1, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    pthread_create(&tid[0], NULL, ConcurrentTruncate, &createInfo0);
    ConcurrentSwapDir(&g_dataFilePath1);
    pthread_join(tid[0], NULL);

    createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam1, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    
    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName1, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName2, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    pthread_create(&tid[0], NULL, ConcurrentTruncate, &createInfo1);
    pthread_join(tid[0], NULL);
    AW_MACRO_ASSERT_EQ_INT(g_concurrentrStatus, GMERR_OK);


    ConcurrentSwapDir(&defaultFilePath);

    pthread_create(&tid[0], NULL, ConcurrentTruncate, &createInfo1);
    pthread_join(tid[0], NULL);
    AW_MACRO_ASSERT_EQ_INT(g_concurrentrStatus, GMERR_OK);

    
    for (int i = 0; i < 8; i++) {
        ConcurrentSwapDir(&g_dataFilePath1);
        pthread_create(&tid[0], NULL, ConcurrentTruncate, &createInfo1);
        ConcurrentSwapDir(&defaultFilePath);
        pthread_join(tid[0], NULL);
    }

    ret = DropTable(stmt, tableName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    memoryAfter = GetKeyWordValue(NULL, NULL, NULL);
}

// 039.反复truncate表表与切换目录并发
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int res = 0;
    char sqlCmd[256] = {0};
    char tableName[256] = {"testdb0"};
    char tableName1[256] = {"testdb1"};
    char tableName2[256] = {"testdb2"};
    char defaultFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    int64_t dataCount = 3000;
    uint32_t cmdLen = 0;
    int threadCount = 4;
    int memoryBefore = 0;
    int memoryAfter = 0;
    pthread_t tid[threadCount];

    InitTsCiCfgModify();
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);

    memoryBefore = GetKeyWordValue(NULL, NULL, NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T - 36000;
    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName1, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName2, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo3 = {3, 2, 0};
    pthread_create(&tid[0], NULL, ConcurrentTruncate, &createInfo3);
    ConcurrentSwapDir(&g_dataFilePath1);
    pthread_join(tid[0], NULL);

    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam1, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    
    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName1, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName2, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    pthread_create(&tid[0], NULL, ConcurrentTruncate, &createInfo1);
    pthread_join(tid[0], NULL);
    AW_MACRO_ASSERT_EQ_INT(g_concurrentrStatus, GMERR_OK);


    ConcurrentSwapDir(&defaultFilePath);
    for (int i = 0; i < 8; i++) {
        ConcurrentSwapDir(&g_dataFilePath1);
        pthread_create(&tid[0], NULL, ConcurrentTruncate, &createInfo3);
        ConcurrentSwapDir(&defaultFilePath);
        pthread_join(tid[0], NULL);
    }

    pthread_create(&tid[0], NULL, ConcurrentTruncate, &createInfo3);
    pthread_join(tid[0], NULL);
    AW_MACRO_ASSERT_EQ_INT(g_concurrentrStatus, GMERR_OK);

    ret = DropTable(stmt, tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, tableName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    memoryAfter = GetKeyWordValue(NULL, NULL, NULL);
}
// ??????
// 040.cache_size场景与切换目录并发
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int res = 0;
    char sqlCmd[512] = {0};
    char tableName[20] = {"testdb0"};
    char tableName1[20] = {"testdb1"};
    char tableName2[20] = {"testdb2"};
    char defaultFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    int64_t dataCount = 5;
    uint32_t cmdLen = 0;
    int threadCount = 4;
    int memoryBefore = 0;
    int memoryAfter = 0;
    int64_t startTime = 1262275200;
    pthread_t tid[threadCount];

    InitTsCiCfgModify();
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(sqlCmd,
        "create table if not exists %s(id integer, time integer, name char(64), ip inet, message blob(160), "
        "description text, id1 integer, time1 integer) with (time_col = 'time', interval = '1 hour', disk_limit = "
        "'500 MB', max_size = 1000000, cache_size = 20);",
        tableName);
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(sqlCmd,
        "create table if not exists %s(id integer, time integer, name char(64), ip inet, message blob(160), "
        "description text, id1 integer, time1 integer) with (time_col = 'time', interval = '1 hour', disk_limit = "
        "'500 MB', max_size = 1000000, cache_size = 20, table_path = '%s/gmdb/');",
        tableName1, g_filePath);
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$TS_TBL_PROPS'\" -s %s", g_connServerTsdb);
    system(sqlCmd);


    for (int i = 0; i < 6; i++) {
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = rowInsertData(stmt, tableName1, dataCount, startTime);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    char tempSqlCmd1[256] = {"ROW_CNT"};
    char tempSqlCmd2[256] = {"PHY_TBL_NUM"};
    char tempSqlCmd3[256] = {"awk -F ':' '{print $2}'"};

    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where TABLE_NAME = 'testdb0'\"");
    ret = GetKeyWordValue(sqlCmd, tempSqlCmd2, tempSqlCmd3);
    AW_MACRO_ASSERT_EQ_INT(1, ret);
    ret = GetKeyWordValue(sqlCmd, tempSqlCmd1, tempSqlCmd3);
    // 视图不统计未落盘数据，下同
    AW_MACRO_ASSERT_EQ_INT(25, ret);

    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where TABLE_NAME = 'testdb1'\"");
    ret = GetKeyWordValue(sqlCmd, tempSqlCmd2, tempSqlCmd3);
    AW_MACRO_ASSERT_EQ_INT(1, ret);
    ret = GetKeyWordValue(sqlCmd, tempSqlCmd1, tempSqlCmd3);
    AW_MACRO_ASSERT_EQ_INT(25, ret);

    ConcurrentSwapDir(&g_dataFilePath1);
    
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where TABLE_NAME = 'testdb1' or "
        "TABLE_NAME = 'testdb0'\"");
    ret = GetKeyWordValue(sqlCmd, tempSqlCmd2, tempSqlCmd3);
    AW_MACRO_ASSERT_EQ_INT(0, ret);

    ConcurrentSwapDir(&defaultFilePath);
    for (int i = 0; i < 6; i++) {
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = rowInsertData(stmt, tableName1, dataCount, startTime);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where TABLE_NAME = 'testdb0'\"");
    ret = GetKeyWordValue(sqlCmd, tempSqlCmd2, tempSqlCmd3);
    AW_MACRO_ASSERT_EQ_INT(1, ret);
    ret = GetKeyWordValue(sqlCmd, tempSqlCmd1, tempSqlCmd3);
    AW_MACRO_ASSERT_EQ_INT(50, ret);

    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where TABLE_NAME = 'testdb1'\"");
    ret = GetKeyWordValue(sqlCmd, tempSqlCmd2, tempSqlCmd3);
    AW_MACRO_ASSERT_EQ_INT(1, ret);
    ret = GetKeyWordValue(sqlCmd, tempSqlCmd1, tempSqlCmd3);
    AW_MACRO_ASSERT_EQ_INT(50, ret);
    ret = DropTable(stmt, tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, tableName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    memoryAfter = GetKeyWordValue(NULL, NULL, NULL);
}

// 041.带table_path的表，切换前删除table_path指定目录
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int res = 0;
    char sqlCmd[256] = {0};
    char tableName[256] = {"testdb0"};
    char tableName1[256] = {"testdb1"};
    char tableName2[256] = {"testdb2"};
    char defaultFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    int64_t dataCount = 3000;
    uint32_t cmdLen = 0;
    int threadCount = 4;
    int memoryBefore = 0;
    int memoryAfter = 0;
    pthread_t tid[threadCount];

    InitTsCiCfgModify();
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);

   
    memoryBefore = GetKeyWordValue(NULL, NULL, NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T - 36000;
    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName1, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName2, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 切换前删除table_path指定目录
    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "rm -rf %s/gmdb/", g_filePath);
    system(sqlCmd);

    ConcurrentSwapDir(&g_dataFilePath1);
    

    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam1, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    
    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName1, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName2, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(stmt, tableName1);
    AW_MACRO_ASSERT_EQ_INT(dataCount, ret);
    
    ConcurrentSwapDir(&defaultFilePath);

    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName1, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName2, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(stmt, tableName1);
    AW_MACRO_ASSERT_EQ_INT(dataCount, ret);

    ret = DropTable(stmt, tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, tableName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    memoryAfter = GetKeyWordValue(NULL, NULL, NULL);
}

// 042.带table_path的表，切换后删除table_path指定目录
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int res = 0;
    char sqlCmd[256] = {0};
    char tableName[256] = {"testdb0"};
    char tableName1[256] = {"testdb1"};
    char tableName2[256] = {"testdb2"};
    char defaultFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    int64_t dataCount = 3000;
    uint32_t cmdLen = 0;
    int threadCount = 4;
    int memoryBefore = 0;
    int memoryAfter = 0;
    pthread_t tid[threadCount];

    InitTsCiCfgModify();
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);

   
    memoryBefore = GetKeyWordValue(NULL, NULL, NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T - 36000;
    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName1, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName2, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ConcurrentSwapDir(&g_dataFilePath1);
    

    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam1, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    
    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName1, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName2, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(stmt, tableName1);
    AW_MACRO_ASSERT_EQ_INT(dataCount, ret);
    

    // 切换后删除table_path指定目录
    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "rm -rf %s/gmdb/", g_filePath);
    system(sqlCmd);

    ConcurrentSwapDir(&defaultFilePath);

    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName1, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName2, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(stmt, tableName1);
    AW_MACRO_ASSERT_EQ_INT(dataCount, ret);

    ret = DropTable(stmt, tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, tableName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    memoryAfter = GetKeyWordValue(NULL, NULL, NULL);
}

// 043.删除易失性路径，小范围破坏默认路径下列存文件
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int res = 0;
    char sqlCmd[256] = {0};
    char tableName[256] = {"testdb0"};
    char tableName1[256] = {"testdb1"};
    char tableName2[256] = {"testdb2"};
    char defaultFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    int64_t dataCount = 3000;
    uint32_t cmdLen = 0;
    int threadCount = 4;
    int memoryBefore = 0;
    int memoryAfter = 0;
    pthread_t tid[threadCount];
#if defined RUN_INDEPENDENT
    system("sh $TEST_HOME/tools/stop.sh -f");
#endif
    system("sh $TEST_HOME/tools/stop.sh -ts");
    TsDefulatDbFileClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 此处需要设置db启动目录为用例目录，从而进行在用例目录下建表
    ret = GmcSetPersistPath(g_dataFilePath1, 2);
    if (ret != GMERR_OK) {
        // 还原默认配置
        ret = GmcSetPersistPath(TABLE_PATH, 2);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = emptyPathRecover(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TsDefulatDbFileClean();
    conn = NULL;
    stmt = NULL;
    char sqlCmd2[512] = {0};
    (void)memset(sqlCmd2, 0, 512);
    (void)sprintf(
        sqlCmd2, "sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    // 构造目录B下存在表数据
    ret = emptyPathStartup(&conn, &stmt, g_dataFilePath1, sqlCmd2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam1, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 固定开始时间，避免构造     //     1262275190
    int64_t startTime = 1262274000;
    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName1, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName2, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 还原回默认值
    ret = GmcSetPersistPath(TABLE_PATH, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/stop.sh -ts");
    // 删除易失性路径
    (void)memset(sqlCmd2, 0, 512);
    (void)sprintf(sqlCmd2, "rm -rf %s/gmdb/", g_filePath);
    system(sqlCmd2);
    (void)memset(sqlCmd2, 0, 512);
    (void)sprintf(sqlCmd2, "%s/", g_dataFilePath1);
    // 少量损坏cuFile文件
    DamageDataFile(false, 2, false, sqlCmd2);
    system("sh $TEST_HOME/tools/start.sh -ts");

    conn = NULL;
    stmt = NULL;
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    ConcurrentSwapDir(&g_dataFilePath1);
    
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(sqlCmd, "select * from testdb0;");
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_CORRUPTION, ret);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(stmt, "testdb0");

    char tempSqlCmd1[256] = {"ROW_CNT"};
    char tempSqlCmd3[256] = {"awk -F ':' '{print $2}'"};
    int dataNum = 0;
    int dataCount1 = 30;
    for (int i = 0; i < 3; i++) {
        (void)memset(sqlCmd, 0, 512);
        (void)sprintf(sqlCmd,
            "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = 'testdb%d'\"", i);
        dataNum = GetKeyWordValue(sqlCmd, tempSqlCmd1, tempSqlCmd3);
        if (i == 0) {
            system("gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = 'testdb0'\" -s usocket:/run/verona/unix_emserver_tsdb");
            AW_MACRO_EXPECT_NE_INT(0, dataNum);
            AW_MACRO_EXPECT_NE_INT(dataCount, dataNum);
        } else {
            AW_MACRO_EXPECT_EQ_INT(0, dataNum);
        }
        (void)memset(tableName, 0, 512);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount1, startTime);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = returnDataCount(stmt, tableName);
        if (i == 0) {
            // 视图查询时，会统计被损坏的数据，查询时实际大于全表查询的数据
            EXPECT_GT(dataNum + dataCount1, ret);
        } else {
            AW_MACRO_EXPECT_EQ_INT(ret, dataNum + dataCount1);
        }
        ret = GetKeyWordValue(sqlCmd, tempSqlCmd1, tempSqlCmd3);
        AW_MACRO_EXPECT_EQ_INT(ret, dataNum + dataCount1);
    }

    // 少量损坏cuMetaFile文件
    DamageDataFile(true, 1, false, sqlCmd2);
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(sqlCmd, "select * from testdb0;");
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_CORRUPTION, ret);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(stmt, "testdb0");

    memoryBefore = GetKeyWordValue(NULL, NULL, NULL);
    // 还原回默认值
    ret = GmcSetPersistPath(TABLE_PATH, 2);
    EXPECT_EQ(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_DATA_CORRUPTION);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 044.小范围破坏易失性路径下列存文件
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int res = 0;
    char sqlCmd[256] = {0};
    char tableName[256] = {"testdb0"};
    char tableName1[256] = {"testdb1"};
    char tableName2[256] = {"testdb2"};
    char defaultFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    int64_t dataCount = 3000;
    uint32_t cmdLen = 0;
    int threadCount = 4;
    int memoryBefore = 0;
    int memoryAfter = 0;
    pthread_t tid[threadCount];
#if defined RUN_INDEPENDENT
    system("sh $TEST_HOME/tools/stop.sh -f");
#endif
    system("sh $TEST_HOME/tools/stop.sh -ts");
    TsDefulatDbFileClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 此处需要设置db启动目录为用例目录，从而进行在用例目录下建表
    ret = GmcSetPersistPath(g_dataFilePath1, 2);
    if (ret != GMERR_OK) {
        // 还原默认配置
        ret = GmcSetPersistPath(TABLE_PATH, 2);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = emptyPathRecover(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TsDefulatDbFileClean();
    conn = NULL;
    stmt = NULL;
    char sqlCmd2[512] = {0};
    (void)memset(sqlCmd2, 0, 512);
    (void)sprintf(
        sqlCmd2, "sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    // 构造目录B下存在表数据
    ret = emptyPathStartup(&conn, &stmt, g_dataFilePath1, sqlCmd2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam1, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 固定开始时间，避免构造     //     1262275190
    int64_t startTime = 1262274000;
    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName1, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName2, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 还原回默认值
    ret = GmcSetPersistPath(TABLE_PATH, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/stop.sh -ts");
    (void)memset(sqlCmd2, 0, 512);
    (void)sprintf(sqlCmd2, "%s/gmdb/", g_filePath);
    // 少量损坏cuFile文件
    DamageDataFile(false, 2, false, sqlCmd2);
    system("sh $TEST_HOME/tools/start.sh -ts");

    conn = NULL;
    stmt = NULL;
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    ConcurrentSwapDir(&g_dataFilePath1);
    
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(sqlCmd, "select * from testdb1;");
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_CORRUPTION, ret);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(stmt, "testdb1");

    char tempSqlCmd1[256] = {"ROW_CNT"};
    char tempSqlCmd3[256] = {"awk -F ':' '{print $2}'"};
    int dataNum = 0;
    for (int i = 0; i < 3; i++) {
        (void)memset(sqlCmd, 0, 512);
        (void)sprintf(sqlCmd,
            "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = 'testdb%d'\"", i);
        dataNum = GetKeyWordValue(sqlCmd, tempSqlCmd1, tempSqlCmd3);
        if (i == 1) {
            system("gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = 'testdb1'\" -s usocket:/run/verona/unix_emserver_tsdb");
            AW_MACRO_EXPECT_NE_INT(0, dataNum);
            AW_MACRO_EXPECT_NE_INT(dataCount, dataNum);
        } else if(i == 0) {
            AW_MACRO_EXPECT_EQ_INT(dataCount, dataNum);
        } else {
            AW_MACRO_EXPECT_EQ_INT(0, dataNum);
        }
        (void)memset(tableName, 0, 512);
        (void)sprintf(tableName, "testdb%d", i);
        dataCount = 30;
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = returnDataCount(stmt, tableName);
        AW_MACRO_EXPECT_EQ_INT(ret, dataNum + dataCount);
        ret = GetKeyWordValue(sqlCmd, tempSqlCmd1, tempSqlCmd3);
        AW_MACRO_EXPECT_EQ_INT(ret, dataNum + dataCount);
    }

    // 少量损坏cuMetaFile文件
    DamageDataFile(true, 1, false, sqlCmd2);
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(sqlCmd, "select * from testdb1;");
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_CORRUPTION, ret);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(stmt, "testdb0");

    memoryBefore = GetKeyWordValue(NULL, NULL, NULL);
    // 还原回默认值
    ret = GmcSetPersistPath(TABLE_PATH, 2);
    EXPECT_EQ(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_DATA_CORRUPTION);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 045.删除易失性路径，删除默认路径下行存文件
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int res = 0;
    char sqlCmd[256] = {0};
    char tableName[256] = {"testdb0"};
    char tableName1[256] = {"testdb1"};
    char tableName2[256] = {"testdb2"};
    char defaultFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    int64_t dataCount = 3000;
    uint32_t cmdLen = 0;
    int threadCount = 4;
    int memoryBefore = 0;
    int memoryAfter = 0;
    pthread_t tid[threadCount];
#if defined RUN_INDEPENDENT
    system("sh $TEST_HOME/tools/stop.sh -f");
#endif
    system("sh $TEST_HOME/tools/stop.sh -ts");
    TsDefulatDbFileClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 此处需要设置db启动目录为用例目录，从而进行在用例目录下建表
    ret = GmcSetPersistPath(g_dataFilePath1, 2);
    if (ret != GMERR_OK) {
        // 还原默认配置
        ret = GmcSetPersistPath(TABLE_PATH, 2);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = emptyPathRecover(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TsDefulatDbFileClean();
    conn = NULL;
    stmt = NULL;
    char sqlCmd2[512] = {0};
    (void)memset(sqlCmd2, 0, 512);
    (void)sprintf(
        sqlCmd2, "sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    // 构造目录B下存在表数据
    ret = emptyPathStartup(&conn, &stmt, g_dataFilePath1, sqlCmd2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam1, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 固定开始时间，避免构造     //     1262275190
    int64_t startTime = 1262274000;
    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName1, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName2, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 还原回默认值
    ret = GmcSetPersistPath(TABLE_PATH, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/stop.sh -ts");
    // 删除易失性路径
    (void)memset(sqlCmd2, 0, 512);
    (void)sprintf(sqlCmd2, "rm -rf %s/gmdb/", g_filePath);
    system(sqlCmd2);
    // 移除行存文件
    (void)memset(sqlCmd2, 0, 512);
    (void)sprintf(sqlCmd2, "rm -rf %s/tsrow*", g_dataFilePath1);
    system(sqlCmd2);
    system("sh $TEST_HOME/tools/start.sh -ts");

    conn = NULL;
    stmt = NULL;
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    ConcurrentSwapDir(&g_dataFilePath1);
    AW_MACRO_ASSERT_EQ_INT(g_concurrentrStatus, 1001000);
    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$TS_TBL_PROPS'\" -s %s", g_connServerTsdb);
    ret = executeCommand(sqlCmd, "Can not exec sysview sql", "1019004");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    ConcurrentSwapDir(&defaultFilePath);
    AW_MACRO_EXPECT_EQ_INT(g_concurrentrStatus, GMERR_OK);
    
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam1, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName1, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName2, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    memoryBefore = GetKeyWordValue(NULL, NULL, NULL);
    // 还原回默认值
    ret = GmcSetPersistPath(TABLE_PATH, 2);
    EXPECT_EQ(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_DATA_CORRUPTION);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 046.破坏易失性路径下行存文件
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int res = 0;
    char sqlCmd[256] = {0};
    char tableName[256] = {"testdb0"};
    char tableName1[256] = {"testdb1"};
    char tableName2[256] = {"testdb2"};
    char defaultFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    int64_t dataCount = 3000;
    uint32_t cmdLen = 0;
    int threadCount = 4;
    int memoryBefore = 0;
    int memoryAfter = 0;
    pthread_t tid[threadCount];
#if defined RUN_INDEPENDENT
    system("sh $TEST_HOME/tools/stop.sh -f");
#endif
    system("sh $TEST_HOME/tools/stop.sh -ts");
    TsDefulatDbFileClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 此处需要设置db启动目录为用例目录，从而进行在用例目录下建表
    ret = GmcSetPersistPath(g_dataFilePath1, 2);
    if (ret != GMERR_OK) {
        // 还原默认配置
        ret = GmcSetPersistPath(TABLE_PATH, 2);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = emptyPathRecover(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TsDefulatDbFileClean();
    conn = NULL;
    stmt = NULL;
    char sqlCmd2[512] = {0};
    (void)memset(sqlCmd2, 0, 512);
    (void)sprintf(
        sqlCmd2, "sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    // 构造目录B下存在表数据
    ret = emptyPathStartup(&conn, &stmt, g_dataFilePath1, sqlCmd2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam1, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 固定开始时间，避免构造     //     1262275190
    int64_t startTime = 1262274000;
    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName1, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName2, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 还原回默认值
    ret = GmcSetPersistPath(TABLE_PATH, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/stop.sh -ts");
    // 移除易失性行存文件
    (void)memset(sqlCmd2, 0, 512);
    (void)sprintf(sqlCmd2, "rm -rf %s/gmdb/tsrow*", g_filePath);
    system(sqlCmd2);
    system("sh $TEST_HOME/tools/start.sh -ts");

    conn = NULL;
    stmt = NULL;
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    ConcurrentSwapDir(&g_dataFilePath1);
    AW_MACRO_ASSERT_EQ_INT(g_concurrentrStatus, GMERR_OK);
    
    char tempSqlCmd1[256] = {"ROW_CNT"};
    char tempSqlCmd3[256] = {"awk -F ':' '{print $2}'"};
    int dataNum = 0;
    int dataCount1 = 30;
    for (int i = 0; i < 3; i++) {
        (void)memset(sqlCmd, 0, 512);
        (void)sprintf(sqlCmd,
            "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = 'testdb%d'\"", i);
        dataNum = GetKeyWordValue(sqlCmd, tempSqlCmd1, tempSqlCmd3);
        if (i == 0) {
            AW_MACRO_EXPECT_EQ_INT(dataCount, dataNum);
        } else {
            AW_MACRO_EXPECT_EQ_INT(0, dataNum);
        }
        (void)memset(sqlCmd2, 0, 512);
        (void)sprintf(sqlCmd2, "testdb%d", i);
        ret = rowInsertData(stmt, sqlCmd2, dataCount1, startTime);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = returnDataCount(stmt, sqlCmd2);
        AW_MACRO_EXPECT_EQ_INT(ret, dataNum + dataCount1);
        ret = GetKeyWordValue(sqlCmd, tempSqlCmd1, tempSqlCmd3);
        AW_MACRO_EXPECT_EQ_INT(ret, dataNum + dataCount1);
    }

    ConcurrentSwapDir(&defaultFilePath);
    AW_MACRO_EXPECT_EQ_INT(g_concurrentrStatus, GMERR_OK);
    
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam1, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    for (int i = 0; i < 3; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    memoryBefore = GetKeyWordValue(NULL, NULL, NULL);
    // 还原回默认值
    ret = GmcSetPersistPath(TABLE_PATH, 2);
    EXPECT_EQ(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_DATA_CORRUPTION);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 047.列存磁盘整理
// 打开配置项cuCompactEnable,建2张逻辑表,一张带table_path,一张不带,分别向两张表中写20批次10条数据,
// 查看disk_usage视图,切换目录后再切换回去,查看disk_usage视图,分别向两张表中写50批次10条数据,查看disk_usage视图
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int res = 0;
    char sqlCmd[256] = {0};
    char tableName[256] = {"testdb0"};
    char tableName1[256] = {"testdb1"};
    char tableName2[256] = {"testdb2"};
    char defaultFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    int64_t dataCount = 10;
    uint32_t cmdLen = 0;
    int threadCount = 4;
    int memoryBefore = 0;
    int memoryAfter = 0;
    pthread_t tid[threadCount];
#if defined RUN_INDEPENDENT
    system("sh $TEST_HOME/tools/stop.sh -f");
#endif
    system("sh $TEST_HOME/tools/stop.sh -ts");
    TsDefulatDbFileClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\" \"cuCompactEnable=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    CreateInfo createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 起始时间2030-1-1 0:0:0
    int64_t startTime = 1893427200;
    for (int i = 0; i < 20; i++) {
        startTime = startTime + 3600 * i;
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = rowInsertData(stmt, tableName1, dataCount, startTime);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = rowInsertData(stmt, tableName2, dataCount, startTime);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    

    char tempSqlCmd1[256] = {"CU_CNT"};
    char tempSqlCmd3[256] = {"awk -F ':' '{print $2}'"};
    int dataNum = 0;

    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE' where LOGIC_LABEL_NAME = 'testdb0'\"");
    dataNum = GetKeyWordValue(sqlCmd, tempSqlCmd1, tempSqlCmd3);

    memoryBefore = GetKeyWordValue(NULL, NULL, NULL);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_DATA_CORRUPTION);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 048.列存磁盘整理
// 打开配置项cuCompactEnable，建2张逻辑表，一张带table_path，一张不带，分别向两张表中30批次10条数据，
// 立刻触发切换目录后再切换回去，查看disk_usage视图，分别向两张表中写50批次10条数据，查看disk_usage视图
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int res = 0;
    char sqlCmd[512] = {0};
    char tableName[256] = {"testdb0"};
    char tableName1[256] = {"testdb1"};
    char tableName2[256] = {"testdb2"};
    char defaultFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    int64_t dataCount = 10;
    uint32_t cmdLen = 0;
    int threadCount = 4;
    int memoryBefore = 0;
    int memoryAfter = 0;
    pthread_t tid[threadCount];
#if defined RUN_INDEPENDENT
    system("sh $TEST_HOME/tools/stop.sh -f");
#endif
    system("sh $TEST_HOME/tools/stop.sh -ts");
    TsDefulatDbFileClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\" \"cuCompactEnable=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    CreateInfo createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 起始时间2030-1-1 0:0:0
    int64_t startTime = 1893427200;
    for (int i = 0; i < 30; i++) {
        startTime = startTime + 3600 * i;
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = rowInsertData(stmt, tableName1, dataCount, startTime);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = rowInsertData(stmt, tableName2, dataCount, startTime);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ConcurrentSwapDir(g_dataFilePath1);
    ConcurrentSwapDir(g_dataFilePath);
    char tempSqlCmd1[256] = {"CU_CNT"};
    char tempSqlCmd3[256] = {"awk -F ':' '{print $2}'"};
    int dataNum = 0;
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE' where LOGIC_LABEL_NAME = 'testdb0'\"");
    dataNum = GetKeyWordValue(sqlCmd, tempSqlCmd1, tempSqlCmd3);
    memoryBefore = GetKeyWordValue(NULL, NULL, NULL);

    for (int i = 0; i < 50; i++) {
        startTime = startTime + 3600 * i;
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = rowInsertData(stmt, tableName1, dataCount, startTime);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = rowInsertData(stmt, tableName2, dataCount, startTime);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE' where LOGIC_LABEL_NAME = 'testdb0'\"");
    dataNum = GetKeyWordValue(sqlCmd, tempSqlCmd1, tempSqlCmd3);
    memoryBefore = GetKeyWordValue(NULL, NULL, NULL);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_DATA_CORRUPTION);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 050.下游典型使用场景1
/**
 *A目录启动，切换至空目录B，创建逻辑表*80，内存表*1，设置AllowDiskClean=0，插入数据；循环以下动作：
 *->设置AllowDiskClean=1，切换回目录A，设置AllowDiskClean=1，创建逻辑表*80，内存表*1； ->
 *设置AllowDiskClean=1，切换到目录B，恢复所有表，设置AllowDiskClean=1，触发主动老化删除逻辑表。
 */
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int res = 0;
    char sqlCmd[256] = {0};
    char tableName[256] = {"testdb0"};
    char defaultFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    int64_t dataCount = 200000;
    uint32_t cmdLen = 0;
    int threadCount = 4;
    int memoryBefore = 0;
    int memoryAfter = 0;
    pthread_t tid[threadCount];
#if defined RUN_INDEPENDENT
    system("sh $TEST_HOME/tools/stop.sh -f");
#endif
    system("sh $TEST_HOME/tools/stop.sh -ts");
    TsDefulatDbFileClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    uint32_t ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t setConfigValue = 0;
    ret = GmcSetCfg(stmt, "tsAllowDiskClean", GMC_DATATYPE_INT32, &setConfigValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CreateInfo createInfo0 = {40, 0, 0};
    CreateInfo createInfo1 = {40, 1, 40};
    CreateInfo createInfo2 = {1, 2, 80};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);

    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    int64_t startTime = time_T - 36000;

    // 全部表写入数据并且触发disklimit报1009022
    for (int i = 0; i < 1; i++) {

        (void)memset(sqlCmd, 0, 256);
        (void)sprintf(sqlCmd, "ALTER TABLE testdb%d  SET (disk_limit = '6 mb');", i);
        ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_TABLE_EXCEED_DISK_LIMIT, ret);
    }
    // 修改AllowDiskClean为1
    setConfigValue = 1;
    ret = GmcSetCfg(stmt, "tsAllowDiskClean", GMC_DATATYPE_INT32, &setConfigValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ConcurrentSwapDir(g_dataFilePath1);
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);

    // 全部表写入数据
    for (int i = 0; i < 81; i++) {
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 修改AllowDiskClean为1
    setConfigValue = 1;
    ret = GmcSetCfg(stmt, "tsAllowDiskClean", GMC_DATATYPE_INT32, &setConfigValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dataCount = 50000;

    for (int j = 0; j < 20; j++) {
        ConcurrentSwapDir(g_dataFilePath);
        for (int i = 0; i < 1; i++) {
            (void)memset(sqlCmd, 0, 256);
            (void)sprintf(sqlCmd,
                "gmsysview -sql \"select * from 'V\\$TS_TBL_PROPS' where table_name = 'testdb%d'\" -s %s", i, g_connServerTsdb);
            system(sqlCmd);
            (void)memset(sqlCmd, 0, 256);
            (void)sprintf(sqlCmd,
                "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = 'testdb%d'\" -s %s", i, g_connServerTsdb);
            system(sqlCmd);
            (void)memset(sqlCmd, 0, 256);
            (void)sprintf(sqlCmd, "select tsdb_aging('testdb%d');", i);
            ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            (void)sprintf(tableName, "testdb%d", i);
            ret = rowInsertData(stmt, tableName, dataCount, startTime);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ConcurrentSwapDir(g_dataFilePath1);
        for (int i = 0; i < 1; i++) {
            (void)memset(sqlCmd, 0, 256);
            (void)sprintf(sqlCmd,
                "gmsysview -sql \"select * from 'V\\$TS_TBL_PROPS' where table_name = 'testdb%d'\" -s %s", i, g_connServerTsdb);
            system(sqlCmd);
            (void)memset(sqlCmd, 0, 256);
            (void)sprintf(sqlCmd,
                "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = 'testdb%d'\" -s %s", i, g_connServerTsdb);
            system(sqlCmd);
            (void)memset(sqlCmd, 0, 256);
            (void)sprintf(sqlCmd, "select tsdb_aging('testdb%d');", i);
            ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            (void)sprintf(tableName, "testdb%d", i);
            ret = rowInsertData(stmt, tableName, dataCount, startTime);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    } 
}

// 051.下游典型使用场景2
/**
A目录启动，切换至空目录B，创建逻辑表*80，内存表*1，设置AllowDiskClean=0，插入数据； gmcUninit，Kill DB，
删除目录A，ipcrm -a，在A目录启动DB，GmcInit -> 设置AllowDiskClean=1，切换到目录B，恢复所有表，
设置AllowDiskClean=1，触发主动老化删除逻辑表。
 */
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int res = 0;
    char sqlCmd[256] = {0};
    char tableName[256] = {"testdb0"};
    char defaultFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    int64_t dataCount = 200000;
    uint32_t cmdLen = 0;
    int threadCount = 4;
    int memoryBefore = 0;
    int memoryAfter = 0;
    pthread_t tid[threadCount];
#if defined RUN_INDEPENDENT
    system("sh $TEST_HOME/tools/stop.sh -f");
#endif
    system("sh $TEST_HOME/tools/stop.sh -ts");
    TsDefulatDbFileClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\" \"tsAllowDiskClean=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ConcurrentSwapDir(g_dataFilePath1);
    CreateInfo createInfo0 = {40, 0, 0};
    CreateInfo createInfo1 = {40, 1, 40};
    CreateInfo createInfo2 = {1, 2, 80};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    int64_t startTime = time_T - 36000;
    // 修改AllowDiskClean为0
    uint32_t setConfigValue = 0;
    ret = GmcSetCfg(stmt, "tsAllowDiskClean", GMC_DATATYPE_INT32, &setConfigValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 全部表写入数据
    for (int i = 0; i < 1; i++) {
        (void)memset(sqlCmd, 0, 256);
        (void)sprintf(sqlCmd, "ALTER TABLE testdb%d  SET (disk_limit = '6 mb');", i);
        ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_TABLE_EXCEED_DISK_LIMIT, ret);
    }

    GmcUnInit();
    system("pkill gmserver_ts");
    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "rm -rf %s", g_dataFilePath);
    system(sqlCmd);
    system("ipcrm -a");
    // A目录启动DB
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\" \"tsAllowDiskClean=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    GmcInit();
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 修改AllowDiskClean为1
    setConfigValue = 1;
    ret = GmcSetCfg(stmt, "tsAllowDiskClean", GMC_DATATYPE_INT32, &setConfigValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 切换到目录B
    ConcurrentSwapDir(g_dataFilePath1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam, &createInfo2);
    pthread_join(tid[2], NULL);
    setConfigValue = 1;
    ret = GmcSetCfg(stmt, "tsAllowDiskClean", GMC_DATATYPE_INT32, &setConfigValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 全部表写入数据
    for (int i = 0; i < 81; i++) {
        (void)memset(sqlCmd, 0, 256);
        (void)sprintf(sqlCmd, "ALTER TABLE testdb%d  SET (disk_limit = '100 mb');", i);
        ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
        (void)memset(sqlCmd, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = 'testdb0'\" -s %s", g_connServerTsdb);
    system(sqlCmd);
    for (int j = 0; j < 20; j++) {
        (void)memset(sqlCmd, 0, 256);
        (void)sprintf(sqlCmd, "select tsdb_aging('testdb%d');", j);
        ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
        if (ret != 0) {
            (void)memset(sqlCmd, 0, 256);
            (void)sprintf(sqlCmd,
                "gmsysview -sql \"select * from 'V\\$TS_TBL_PROPS' where table_name = 'testdb%d'\" -s %s", j, g_connServerTsdb);
            system(sqlCmd);
            (void)memset(sqlCmd, 0, 256);
            (void)sprintf(sqlCmd,
                "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = 'testdb%d'\" -s %s", j, g_connServerTsdb);
            system(sqlCmd);
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        (void)sprintf(tableName, "testdb%d", j);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

// 052.下游典型使用场景3
/**
A目录启动，切换至空目录B，创建逻辑表*4，目录逻辑表（指定目录C）*80，内存表*1，设置AllowDiskClean=0，插入数据；
gmcUninit，Kill DB，ipcrm -a，在A目录启动DB，GmcInit -> 设置AllowDiskClean=1，
切换到目录B，恢复所有表，设置AllowDiskClean=1，触发主动老化删除逻辑表。
 */
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int res = 0;
    char sqlCmd[256] = {0};
    char tableName[256] = {"testdb0"};
    char defaultFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    int64_t dataCount = 200000;
    uint32_t cmdLen = 0;
    int threadCount = 4;
    int memoryBefore = 0;
    int memoryAfter = 0;
    pthread_t tid[threadCount];
#if defined RUN_INDEPENDENT
    system("sh $TEST_HOME/tools/stop.sh -f");
#endif
    system("sh $TEST_HOME/tools/stop.sh -ts");
    TsDefulatDbFileClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\" \"tsAllowDiskClean=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ConcurrentSwapDir(g_dataFilePath1);
    CreateInfo createInfo0 = {4, 0, 0};
    CreateInfo createInfo1 = {76, 1, 4};
    CreateInfo createInfo2 = {1, 2, 80};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);

    // 修改AllowDiskClean为0
    uint32_t setConfigValue = 0;
    ret = GmcSetCfg(stmt, "tsAllowDiskClean", GMC_DATATYPE_INT32, &setConfigValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    int64_t startTime = time_T - 36000;
    
    // 全部表写入数据并且触发disklimit报1009022
    for (int i = 0; i < 81; i++) {
        (void)memset(sqlCmd, 0, 256);
        (void)sprintf(sqlCmd, "ALTER TABLE testdb%d  SET (disk_limit = '6 mb');", i);
        ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_TABLE_EXCEED_DISK_LIMIT, ret);
    }

    GmcUnInit();
    system("pkill gmserver_ts");
    system("ipcrm -a");
    // A目录启动DB
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\" \"tsAllowDiskClean=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    GmcInit();
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 修改AllowDiskClean为1
    setConfigValue = 1;
    ret = GmcSetCfg(stmt, "tsAllowDiskClean", GMC_DATATYPE_INT32, &setConfigValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 切换到目录B
    ConcurrentSwapDir(g_dataFilePath1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, g_concurrentrStatus);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam, &createInfo2);
    pthread_join(tid[2], NULL);

    ret = GmcSetCfg(stmt, "tsAllowDiskClean", GMC_DATATYPE_INT32, &setConfigValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dataCount = 50000;
    for (int j = 0; j < 20; j++) {
        (void)memset(sqlCmd, 0, 256);
        (void)sprintf(sqlCmd, "select tsdb_aging('testdb%d');", j);
        ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        (void)sprintf(tableName, "testdb%d", j);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

// 053.下游典型使用场景4
/**
A目录启动，切换至空目录B，创建逻辑表*4，目录逻辑表（指定目录C）*80，内存表*1，设置AllowDiskClean=0，插入数据；
循环以下动作：-> gmcUninit，Kill DB，ipcrm -a，删除目录A、C，在A目录启动DB，
GmcInit -> 设置AllowDiskClean=1，切换到目录B，恢复所有表，设置AllowDiskClean=1，触发主动老化删除逻辑表。
 */
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int res = 0;
    char sqlCmd[256] = {0};
    char tableName[256] = {"testdb0"};
    char defaultFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    int64_t dataCount = 200000;
    uint32_t cmdLen = 0;
    int threadCount = 4;
    int memoryBefore = 0;
    int memoryAfter = 0;
    pthread_t tid[threadCount];
#if defined RUN_INDEPENDENT
    system("sh $TEST_HOME/tools/stop.sh -f");
#endif
    system("sh $TEST_HOME/tools/stop.sh -ts");
    TsDefulatDbFileClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\" \"tsAllowDiskClean=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ConcurrentSwapDir(g_dataFilePath1);
    CreateInfo createInfo0 = {4, 0, 0};
    CreateInfo createInfo1 = {76, 1, 4};
    CreateInfo createInfo2 = {1, 2, 80};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);

    // 修改AllowDiskClean为0
    uint32_t setConfigValue = 0;
    ret = GmcSetCfg(stmt, "tsAllowDiskClean", GMC_DATATYPE_INT32, &setConfigValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    int64_t startTime = time_T - 36000;
    
    // 全部表写入数据并且触发disklimit报1009022
    for (int i = 0; i < 81; i++) {
        (void)memset(sqlCmd, 0, 256);
        (void)sprintf(sqlCmd, "ALTER TABLE testdb%d  SET (disk_limit = '6 mb');", i);
        ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_TABLE_EXCEED_DISK_LIMIT, ret);
    }

    for (int j = 0; j < 10; j++) {
        GmcUnInit();
        system("pkill gmserver_ts");
        system("ipcrm -a");
        (void)memset(sqlCmd, 0, 256);
        (void)sprintf(sqlCmd, "rm -rf %s", g_dataFilePath);
        system(sqlCmd);
        (void)memset(sqlCmd, 0, 256);
        (void)sprintf(sqlCmd, "rm -rf %s/gmdb1/", g_dataFilePath1);
        system(sqlCmd);
        // A目录启动DB
        system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\" \"tsAllowDiskClean=0\"");
        system("sh $TEST_HOME/tools/start.sh -ts");
        GmcInit();

        ret = TestTsGmcConnect(&conn, &stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 修改AllowDiskClean为1
        setConfigValue = 1;
        ret = GmcSetCfg(stmt, "tsAllowDiskClean", GMC_DATATYPE_INT32, &setConfigValue, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 切换到目录B
        ConcurrentSwapDir(g_dataFilePath1);
        // 恢复所有表
        pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
        pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
        pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam, &createInfo2);
        pthread_join(tid[0], NULL);
        pthread_join(tid[1], NULL);
        pthread_join(tid[2], NULL);
        // 修改AllowDiskClean为1
        setConfigValue = 1;
        ret = GmcSetCfg(stmt, "tsAllowDiskClean", GMC_DATATYPE_INT32, &setConfigValue, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        dataCount = 50000;
        for (int j = 0; j < 20; j++) {
            (void)sprintf(tableName, "testdb%d", j);
            ret = rowInsertData(stmt, tableName, dataCount, startTime);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            (void)memset(sqlCmd, 0, 256);
            (void)sprintf(sqlCmd, "select tsdb_aging('testdb%d');", j);
            ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
}

// 054.下游典型使用场景5
/**
A目录启动，切换至空目录B，创建逻辑表*80，内存表*1，设置AllowDiskClean=0，插入数据；
触发锁库（待确认如何构造场景），
循环以下动作： ->设置AllowDiskClean=1，切换回目录A，设置AllowDiskClean=1，创建逻辑表*80，内存表*1
设置AllowDiskClean=1，切换到目录B，恢复所有表，设置AllowDiskClean=1，触发主动老化删除逻辑表。
 */
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_054)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf ./tempTsrowFile");
    system("mkdir -p tempTsrowFile");
    int res = 0;
    char sqlCmd[256] = {0};
    char tableName[256] = {"testdb0"};
    char defaultFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    int64_t dataCount = 5000;
    uint32_t cmdLen = 0;
    int threadCount = 4;
    int memoryBefore = 0;
    int memoryAfter = 0;
    pthread_t tid[threadCount];
#if defined RUN_INDEPENDENT
    system("sh $TEST_HOME/tools/stop.sh -f");
#endif
    system("sh $TEST_HOME/tools/stop.sh -ts");
    TsDefulatDbFileClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\" \"tsAllowDiskClean=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    CreateInfo createInfo0 = {4, 0, 0};
    CreateInfo createInfo1 = {76, 1, 4};
    CreateInfo createInfo2 = {1, 2, 80};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);

    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    int64_t startTime = time_T - 36000;

    // 全部表写入数据并且触发disklimit报1009022
    for (int i = 0; i < 81; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ConcurrentSwapDir(g_dataFilePath1);

    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);

    // 修改AllowDiskClean为0
    uint32_t setConfigValue = 0;
    ret = GmcSetCfg(stmt, "tsAllowDiskClean", GMC_DATATYPE_INT32, &setConfigValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dataCount = 200000;
    // 全部表写入数据并且触发disklimit报1009022
    for (int i = 0; i < 81; i++) {
        (void)memset(sqlCmd, 0, 256);
        (void)sprintf(sqlCmd, "ALTER TABLE testdb%d  SET (disk_limit = '6 mb');", i);
        ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_TABLE_EXCEED_DISK_LIMIT, ret);
    }

    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "mv %s/tsrow* ./tempTsrowFile", defaultFilePath);
    system(sqlCmd);

    ConcurrentSwapDir(defaultFilePath);
    
    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "mv ./tempTsrowFile/tsrow* %s", defaultFilePath);
    system(sqlCmd);
    
    ConcurrentSwapDir(defaultFilePath);
    
    for (int j = 0; j < 10; j++) {
        setConfigValue = 1;
        ret = GmcSetCfg(stmt, "tsAllowDiskClean", GMC_DATATYPE_INT32, &setConfigValue, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam, &createInfo2);
        pthread_join(tid[2], NULL);

        // 切换到目录B
        ConcurrentSwapDir(g_dataFilePath1);
        // 恢复所有表
        pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam, &createInfo2);
        pthread_join(tid[2], NULL);
        // 修改AllowDiskClean为1
        setConfigValue = 1;
        ret = GmcSetCfg(stmt, "tsAllowDiskClean", GMC_DATATYPE_INT32, &setConfigValue, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        dataCount = 50000;
        for (int j = 0; j < 20; j++) {
            (void)sprintf(tableName, "testdb%d", j);
            ret = rowInsertData(stmt, tableName, dataCount, startTime);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            (void)memset(sqlCmd, 0, 256);
            (void)sprintf(sqlCmd, "select tsdb_aging('testdb%d');", j);
            ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
}

// 053.下游典型使用场景6
/**
A目录启动，切换至空目录B，创建逻辑表*4，目录逻辑表（指定目录C）*80，内存表*1，设置AllowDiskClean=0，插入数据；
循环以下动作：-> gmcUninit，Kill DB，ipcrm -a，删除目录A、C，在A目录启动DB，
GmcInit -> 设置AllowDiskClean=1，切换到目录B，恢复所有表，设置AllowDiskClean=1，触发主动老化删除逻辑表。
 */
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_055)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int res = 0;
    char sqlCmd[256] = {0};
    char tableName[256] = {"testdb0"};
    char defaultFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    int64_t dataCount = 200000;
    uint32_t cmdLen = 0;
    int threadCount = 4;
    int memoryBefore = 0;
    int memoryAfter = 0;
    pthread_t tid[threadCount];
#if defined RUN_INDEPENDENT
    system("sh $TEST_HOME/tools/stop.sh -f");
#endif
    system("sh $TEST_HOME/tools/stop.sh -ts");
    TsDefulatDbFileClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\" \"tsAllowDiskClean=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ConcurrentSwapDir(g_dataFilePath1);
    CreateInfo createInfo0 = {4, 0, 0};
    CreateInfo createInfo1 = {76, 1, 4};
    CreateInfo createInfo2 = {1, 2, 80};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);

    // 修改AllowDiskClean为0
    uint32_t setConfigValue = 0;
    ret = GmcSetCfg(stmt, "tsAllowDiskClean", GMC_DATATYPE_INT32, &setConfigValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    int64_t startTime = time_T - 36000;
    
    // 全部表写入数据并且触发disklimit报1009022
    for (int i = 0; i < 81; i++) {
        (void)memset(sqlCmd, 0, 256);
        (void)sprintf(sqlCmd, "ALTER TABLE testdb%d  SET (disk_limit = '6 mb');", i);
        ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_TABLE_EXCEED_DISK_LIMIT, ret);
    }

    for (int j = 0; j < 10; j++) {
        GmcUnInit();
        system("pkill gmserver_ts");
        system("ipcrm -a");
        (void)memset(sqlCmd, 0, 256);
        (void)sprintf(sqlCmd, "rm -rf %s", g_dataFilePath);
        system(sqlCmd);
        (void)memset(sqlCmd, 0, 256);
        (void)sprintf(sqlCmd, "rm -rf %s/gmdb1/", g_dataFilePath1);
        system(sqlCmd);
        // A目录启动DB
        system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\" \"tsAllowDiskClean=0\"");
        system("sh $TEST_HOME/tools/start.sh -ts");
        GmcInit();

        ret = TestTsGmcConnect(&conn, &stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 修改AllowDiskClean为1
        setConfigValue = 1;
        ret = GmcSetCfg(stmt, "tsAllowDiskClean", GMC_DATATYPE_INT32, &setConfigValue, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 切换到目录B
        ConcurrentSwapDir(g_dataFilePath1);
        // 恢复所有表
        pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
        pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
        pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam, &createInfo2);
        pthread_join(tid[0], NULL);
        pthread_join(tid[1], NULL);
        pthread_join(tid[2], NULL);
        // 修改AllowDiskClean为1
        setConfigValue = 1;
        ret = GmcSetCfg(stmt, "tsAllowDiskClean", GMC_DATATYPE_INT32, &setConfigValue, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        dataCount = 50000;
        for (int j = 0; j < 20; j++) {
            (void)sprintf(tableName, "testdb%d", j);
            ret = rowInsertData(stmt, tableName, dataCount, startTime);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            (void)memset(sqlCmd, 0, 256);
            (void)sprintf(sqlCmd, "ALTER TABLE testdb%d  SET (ttl = '1 hour');", j);
            ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            sleep(3);
        }
    }
}

// 056.下游典型使用场景7
/**
A目录启动，切换至空目录B，创建逻辑表*80，内存表*1，设置AllowDiskClean=0，插入数据；
触发锁库（待确认如何构造场景），
循环以下动作： ->设置AllowDiskClean=1，切换回目录A，设置AllowDiskClean=1，创建逻辑表*80，内存表*1
设置AllowDiskClean=1，切换到目录B，恢复所有表，设置AllowDiskClean=1，触发主动老化删除逻辑表。
 */
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_056)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf ./tempTsrowFile");
    system("mkdir -p tempTsrowFile");
    int res = 0;
    char sqlCmd[256] = {0};
    char tableName[256] = {"testdb0"};
    char defaultFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    int64_t dataCount = 5000;
    uint32_t cmdLen = 0;
    int threadCount = 4;
    int memoryBefore = 0;
    int memoryAfter = 0;
    pthread_t tid[threadCount];
#if defined RUN_INDEPENDENT
    system("sh $TEST_HOME/tools/stop.sh -f");
#endif
    system("sh $TEST_HOME/tools/stop.sh -ts");
    TsDefulatDbFileClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\" \"tsAllowDiskClean=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    CreateInfo createInfo0 = {4, 0, 0};
    CreateInfo createInfo1 = {76, 1, 4};
    CreateInfo createInfo2 = {1, 2, 80};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);

    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    int64_t startTime = time_T - 36000;

    // 全部表写入数据并且触发disklimit报1009022
    for (int i = 0; i < 81; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ConcurrentSwapDir(g_dataFilePath1);

    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);

    // 修改AllowDiskClean为0
    uint32_t setConfigValue = 0;
    ret = GmcSetCfg(stmt, "tsAllowDiskClean", GMC_DATATYPE_INT32, &setConfigValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dataCount = 200000;
    // 全部表写入数据并且触发disklimit报1009022
    for (int i = 0; i < 81; i++) {
        (void)memset(sqlCmd, 0, 256);
        (void)sprintf(sqlCmd, "ALTER TABLE testdb%d  SET (disk_limit = '6 mb');", i);
        ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_TABLE_EXCEED_DISK_LIMIT, ret);
    }

    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "mv %s/tsrow* ./tempTsrowFile", defaultFilePath);
    system(sqlCmd);

    ConcurrentSwapDir(defaultFilePath);
    
    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "mv ./tempTsrowFile/tsrow* %s", defaultFilePath);
    system(sqlCmd);
    
    ConcurrentSwapDir(defaultFilePath);
    
    for (int j = 0; j < 10; j++) {
        setConfigValue = 1;
        ret = GmcSetCfg(stmt, "tsAllowDiskClean", GMC_DATATYPE_INT32, &setConfigValue, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam, &createInfo2);
        pthread_join(tid[2], NULL);

        // 切换到目录B
        ConcurrentSwapDir(g_dataFilePath1);
        // 恢复所有表
        pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam, &createInfo2);
        pthread_join(tid[2], NULL);
        // 修改AllowDiskClean为1
        setConfigValue = 1;
        ret = GmcSetCfg(stmt, "tsAllowDiskClean", GMC_DATATYPE_INT32, &setConfigValue, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        dataCount = 50000;
        for (int j = 0; j < 20; j++) {
            (void)sprintf(tableName, "testdb%d", j);
            ret = rowInsertData(stmt, tableName, dataCount, startTime);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            (void)memset(sqlCmd, 0, 256);
            (void)sprintf(sqlCmd, "TRUNCATE TABLE testdb%d;", j);
            ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
}

// 57.删除易失性路径，小范围破坏默认路径下列存文件
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_057)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int res = 0;
    char sqlCmd[256] = {0};
    char tableName[256] = {"testdb0"};
    char tableName1[256] = {"testdb1"};
    char tableName2[256] = {"testdb2"};
    char defaultFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    int64_t dataCount = 3000;
    uint32_t cmdLen = 0;
    int threadCount = 4;
    int memoryBefore = 0;
    int memoryAfter = 0;
    pthread_t tid[threadCount];
#if defined RUN_INDEPENDENT
    system("sh $TEST_HOME/tools/stop.sh -f");
#endif
    system("sh $TEST_HOME/tools/stop.sh -ts");
    TsDefulatDbFileClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(2);
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    CreateInfo createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 固定开始时间，避免构造     //     
    int64_t startTime = 1262275190;
    ret = rowInsertData(stmt, tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName1, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, tableName2, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 删除易失性路径
    char sqlCmd2[512] = {0};
    (void)memset(sqlCmd2, 0, 512);
    (void)sprintf(sqlCmd2, "rm -rf %s/gmdb/", g_filePath);
    system(sqlCmd2);
    (void)memset(sqlCmd2, 0, 512);
    (void)sprintf(sqlCmd2, TABLE_PATH);
    // 少量损坏cuFile文件
    DamageDataFile(false, 2, false, sqlCmd2);
    
    
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(sqlCmd, "select * from testdb0;");
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_CORRUPTION, ret);

    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(sqlCmd, "select * from testdb0;");
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_CORRUPTION, ret);

    dataCount = 30;
    ret = rowInsertData(stmt, "testdb0", dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(stmt, "testdb0");
    AW_MACRO_EXPECT_NE_INT(3030, res);
    memoryBefore = GetKeyWordValue(NULL, NULL, NULL);
    // 还原回默认值
    ret = GmcSetPersistPath(TABLE_PATH, 2);
    EXPECT_EQ(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_DATA_CORRUPTION);
    AW_FUN_Log(LOG_STEP, "test end.");
}


// 058.AllowDiskClean设为0,建100张表,指定disk_limit,向表中写入数据，
// 修改AllowDiskClean为1,立刻进行目录切换,切换目录后再次切换至原目录,进行插入和查询后删除全表
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_058)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int res = 0;
    char sqlCmd[256] = {0};
    char tableName[256] = {"testdb0"};
    char tableName1[256] = {"testdb1"};
    char tableName2[256] = {"testdb2"};
    char defaultFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    int64_t dataCount = 100000;
    uint32_t cmdLen = 0;
    int threadCount = 4;
    int memoryBefore = 0;
    int memoryAfter = 0;
    pthread_t tid[threadCount];
#if defined RUN_INDEPENDENT
    system("sh $TEST_HOME/tools/stop.sh -f");
#endif
    system("sh $TEST_HOME/tools/stop.sh -ts");
    TsDefulatDbFileClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\" \"tsAllowDiskClean=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(2);
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    CreateInfo createInfo0 = {50, 0, 0};
    CreateInfo createInfo1 = {50, 1, 50};
    CreateInfo createInfo2 = {5, 2, 100};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam1, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 固定开始时间，避免构造     //     
    int64_t startTime = time_T - 3600;

    for (int j = 0; j < 105; j++) {
        (void)sprintf(tableName, "testdb%d", j);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        if (j >= 100) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_TABLE_EXCEED_DISK_LIMIT, ret);
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    uint32_t setConfigValue = 1;
    ret = GmcSetCfg(stmt, "tsAllowDiskClean", GMC_DATATYPE_INT32, &setConfigValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 切换到目录B
    ConcurrentSwapDir(g_dataFilePath1);
    // 切换到原目录
    ConcurrentSwapDir(defaultFilePath);
    dataCount = 1000;
    for (int j = 0; j < 100; j++) {
        (void)sprintf(tableName, "testdb%d", j);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = DropTsTable(stmt, tableName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_TABLE_EXCEED_DISK_LIMIT);
}

// 059.AllowDiskClean设为0，建100张表，指定disk_limit和table_path，向表中写入数据，
// 修改AllowDiskClean为1，立刻进行目录切换，删除table_path指定目录后，
// 切换目录后再次切换至原目录，查表无数据，再次建表，进行插入和查询后删除全表
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_059)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int res = 0;
    char sqlCmd[256] = {0};
    char tableName[256] = {"testdb0"};
    char tableName1[256] = {"testdb1"};
    char tableName2[256] = {"testdb2"};
    char defaultFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    int64_t dataCount = 100000;
    uint32_t cmdLen = 0;
    int threadCount = 4;
    int memoryBefore = 0;
    int memoryAfter = 0;
    pthread_t tid[threadCount];
#if defined RUN_INDEPENDENT
    system("sh $TEST_HOME/tools/stop.sh -f");
#endif
    system("sh $TEST_HOME/tools/stop.sh -ts");
    TsDefulatDbFileClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\" \"tsAllowDiskClean=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(2);
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    CreateInfo createInfo0 = {50, 0, 0};
    CreateInfo createInfo1 = {50, 1, 50};
    CreateInfo createInfo2 = {5, 2, 100};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam1, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 固定开始时间，避免构造     //     
    int64_t startTime = time_T - 3600;

    for (int j = 0; j < 105; j++) {
        (void)sprintf(tableName, "testdb%d", j);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        if (j >= 100) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_TABLE_EXCEED_DISK_LIMIT, ret);
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    uint32_t setConfigValue = 1;
    ret = GmcSetCfg(stmt, "tsAllowDiskClean", GMC_DATATYPE_INT32, &setConfigValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 切换到目录B
    ConcurrentSwapDir(g_dataFilePath1);
    // 切换前删除table_path指定目录
    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "rm -rf %s/gmdb/", g_filePath);
    system(sqlCmd);

    // 切换到原目录
    ConcurrentSwapDir(defaultFilePath);

    dataCount = 1000;
    for (int j = 0; j < 50; j++) {
        (void)sprintf(tableName, "testdb%d", j);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = DropTsTable(stmt, tableName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    for (int j = 50; j < 100; j++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", j);
        res = returnDataCount(stmt, tableName);
        AW_MACRO_EXPECT_EQ_INT(0, res);
        ret = DropTsTable(stmt, tableName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_TABLE_EXCEED_DISK_LIMIT);
}

// 060.AllowDiskClean设为0，建100张表，指定disk_limit，向表中写入数据，
// 修改AllowDiskClean为1，立刻进行目录切换，指定目的路径为当前目录，进行插入和查询后删除全表
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_060)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int res = 0;
    char sqlCmd[256] = {0};
    char tableName[256] = {"testdb0"};
    char tableName1[256] = {"testdb1"};
    char tableName2[256] = {"testdb2"};
    char defaultFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    int64_t dataCount = 100000;
    uint32_t cmdLen = 0;
    int threadCount = 4;
    int memoryBefore = 0;
    int memoryAfter = 0;
    pthread_t tid[threadCount];
#if defined RUN_INDEPENDENT
    system("sh $TEST_HOME/tools/stop.sh -f");
#endif
    system("sh $TEST_HOME/tools/stop.sh -ts");
    TsDefulatDbFileClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\" \"tsAllowDiskClean=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(2);
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    CreateInfo createInfo0 = {50, 0, 0};
    CreateInfo createInfo1 = {50, 1, 50};
    CreateInfo createInfo2 = {5, 2, 100};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    int64_t startTime = time_T - 3600;
    for (int j = 0; j < 105; j++) {
        (void)sprintf(tableName, "testdb%d", j);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        if (j >= 100) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_TABLE_EXCEED_DISK_LIMIT, ret);
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    uint32_t setConfigValue = 1;
    ret = GmcSetCfg(stmt, "tsAllowDiskClean", GMC_DATATYPE_INT32, &setConfigValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 切换到原目录
    ConcurrentSwapDir(defaultFilePath);
    dataCount = 1000;
    for (int j = 0; j < 100; j++) {
        (void)sprintf(tableName, "testdb%d", j);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = DropTsTable(stmt, tableName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_TABLE_EXCEED_DISK_LIMIT);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 061.建表，构造上万个物理表进行目录切换场景，验证切换时间
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_061)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int res = 0;
    char sqlCmd[256] = {0};
    char tableName[256] = {"testdb0"};
    char tableName1[256] = {"testdb1"};
    char tableName2[256] = {"testdb2"};
    char defaultFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    int64_t dataCount = 100000;
    uint32_t cmdLen = 0;
    int threadCount = 4;
    int memoryBefore = 0;
    int memoryAfter = 0;
    pthread_t tid[threadCount];
#if defined RUN_INDEPENDENT
    system("sh $TEST_HOME/tools/stop.sh -f");
#endif
    system("sh $TEST_HOME/tools/stop.sh -ts");
    TsDefulatDbFileClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\" \"tsAllowDiskClean=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(2);
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    CreateInfo createInfo0 = {50, 0, 0};
    CreateInfo createInfo1 = {50, 1, 50};
    CreateInfo createInfo2 = {5, 2, 100};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam1, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 固定开始时间，避免构造   
    int64_t startTime = time_T - 3600;

    for (int j = 0; j < 105; j++) {
        (void)sprintf(tableName, "testdb%d", j);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        if (j >= 100) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_TABLE_EXCEED_DISK_LIMIT, ret);
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    uint32_t setConfigValue = 1;
    ret = GmcSetCfg(stmt, "tsAllowDiskClean", GMC_DATATYPE_INT32, &setConfigValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 切换到目录B
    ConcurrentSwapDir(g_dataFilePath1);
    // 统计切换前后时间差，验证切换时间
    time_t time_After;
    // 等待5s确认
    sleep(5);
    // 切换到原目录
    time_T = time(NULL);
    ConcurrentSwapDir(defaultFilePath);
    // 目录切换后记录时间
    time_After = time(NULL);
    AW_FUN_Log(LOG_INFO, "8000+ phy_table swap cost %ds",  time_After - time_T);
    EXPECT_GE(3, time_After - time_T);

    dataCount = 1000;
    for (int j = 0; j < 102; j++) {
        (void)sprintf(tableName, "testdb%d", j);
        ret = DropTsTable(stmt, tableName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_TABLE_EXCEED_DISK_LIMIT);
}

// 062.建连和目录切换并发场景
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_062)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int res = 0;
    char sqlCmd[256] = {0};
    char sqlCmd1[256] = {0};
    char sqlCmd2[256] = {0};
    char tableName[256] = {"testdb0"};
    char tableName1[256] = {"testdb1"};
    char tableName2[256] = {"testdb2"};
    char defaultFilePath[256] = {0};
    (void)sprintf(defaultFilePath, "%s", TABLE_PATH);
    int64_t dataCount = 100000;
    uint32_t cmdLen = 0;
    int threadCount = 8;
    pthread_t tid[threadCount];
#if defined RUN_INDEPENDENT
    system("sh $TEST_HOME/tools/stop.sh -f");
#endif
    system("sh $TEST_HOME/tools/stop.sh -ts");
    TsDefulatDbFileClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\" \"tsAllowDiskClean=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    CreateInfo createInfo0 = {10, 0, 0};
    CreateInfo createInfo1 = {10, 1, 10};
    CreateInfo createInfo2 = {5, 2, 20};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    char sqlCmd3[256] = {0};
    for (int i = 0; i < 20; i++) {
        pthread_create(&tid[4], NULL, ConcurrentConnect, NULL);
        pthread_create(&tid[0], NULL, ConcurrentSwapDir, &g_dataFilePath1);
        pthread_join(tid[4], NULL);
        pthread_join(tid[0], NULL);
        (void)memset(sqlCmd3, 0, 256);
        (void)sprintf(sqlCmd3, "select * from testdb0;");
        cmdLen = strlen(sqlCmd3);
        ret = GmcExecDirect(stmt, sqlCmd3, cmdLen);
        AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
        pthread_create(&tid[4], NULL, ConcurrentConnect, NULL);
        pthread_create(&tid[1], NULL, ConcurrentSwapDir, &defaultFilePath);
        pthread_join(tid[1], NULL);
        pthread_join(tid[4], NULL);
        (void)sprintf(sqlCmd3, "select * from testdb0;");
        cmdLen = strlen(sqlCmd3);
        ret = GmcExecDirect(stmt, sqlCmd3, cmdLen);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNDEFINED_TABLE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 063.内存表写数据时触发后台线程删除数据
TEST_F(TsdbSwitchoverComplement, Timing_078_Conplement_063)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int res = 0;
    char sqlCmd[256] = {0};
    int64_t dataCount = 10000;
    uint32_t cmdLen = 0;
    int threadCount = 4;
    int memoryBefore = 0;
    int memoryAfter = 0;
    pthread_t tid[threadCount];
    InitTsCiCfgModify();
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    GmcConnT *conn2 = NULL;
    GmcStmtT *stmt2 = NULL;
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestTsGmcConnect(&conn1, &stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestTsGmcConnect(&conn2, &stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    CreateInfo createInfo0 = {2, 2, 0};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_join(tid[0], NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T - 360000000;
    int64_t insertTimes = 5;
    InsertQueryDataType constructDataType_1 = {stmt1, g_tableName, dataCount, startTime};
    InsertQueryDataType constructDataType_2 = {stmt2, g_tableName2, dataCount, startTime};
    g_isInsertDataSuccessed = true;
    pthread_create(&tid[1], NULL, InsertDataTimeAdd, &constructDataType_1);
    pthread_create(&tid[2], NULL, InsertDataTimeAdd, &constructDataType_2);
    int ttlTemp = 0;
    int diskTemp = 0;
    while (g_isInsertDataSuccessed) {
        for (int i = 0; i < 5; i++) {
            ttlTemp = rand()%10000 + 100;
            diskTemp = rand()%1024 + 512;
            if (i % 4 != 0) {
                (void)memset(sqlCmd, 0, 256);
                (void)sprintf(sqlCmd, "ALTER TABLE testdb0 SET (disk_limit = '%d MB');", diskTemp);
                ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
                if (ret != GMERR_OK) {
                    AW_MACRO_EXPECT_EQ_INT(GMERR_LOCK_NOT_AVAILABLE, ret);
                }
                (void)memset(sqlCmd, 0, 256);
                (void)sprintf(sqlCmd, "ALTER TABLE testdb1 SET (disk_limit = '%d MB');", diskTemp);
                ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
                if (ret != GMERR_OK) {
                    AW_MACRO_EXPECT_EQ_INT(GMERR_LOCK_NOT_AVAILABLE, ret);
                }
            } else {
                (void)memset(sqlCmd, 0, 256);
                (void)sprintf(sqlCmd, "ALTER TABLE testdb0 SET (ttl = '%d H');", ttlTemp);
                ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
                if (ret != GMERR_OK) {
                    AW_MACRO_EXPECT_EQ_INT(GMERR_LOCK_NOT_AVAILABLE, ret);
                }
                (void)memset(sqlCmd, 0, 256);
                (void)sprintf(sqlCmd, "ALTER TABLE testdb1 SET (ttl = '%d H');", ttlTemp);
                ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
                if (ret != GMERR_OK) {
                    AW_MACRO_EXPECT_EQ_INT(GMERR_LOCK_NOT_AVAILABLE, ret);
                }
            }
            // 并发
            sleep(1);
            (void)memset(sqlCmd, 0, 256);
            (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s",
                g_connServerTsdb);
            system(sqlCmd);
        }
        g_isInsertDataSuccessed = false;
    }
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    // 等待后台线程清理数据
    sleep(10);
    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "select * from %s where time < 0", g_tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount1 = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount1, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "select * from %s where time >= 0", g_tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dataCount1 = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount1, sizeof(int));
    EXPECT_GT(60000, ret);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, g_tableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn2, stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
