/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * File Name: tsdb_dts_enhanced.h
 * Description: tsdb_dts_enhanced头文件
 * Author:
 * Create: 2025-07-10
 */

#ifndef TSDB_DTS_ENHANCED_H
#define TSDB_DTS_ENHANCED_H
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "gmc_ts_persist.h"
#include "t_datacom_lite.h"

#define MAX_CMD_SIZE 1024

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
char g_tableName1[] = "tsTable1";
char g_tableName2[] = "tsTable2";
char g_command[MAX_CMD_SIZE];
int g_threadWait = 0;

#if ENABLE_INFO
#define TEST_INFO(log, args...)                                                                 \
    do {                                                                                        \
        fprintf(stdout, "Info: pid-%d %s:%d: " log "\n", getpid(), __FILE__, __LINE__, ##args); \
    } while (0)
#else
#define TEST_INFO(log, args...)
#endif

#define TEST_ERROR(log, args...)                                                                \
    do {                                                                                        \
        fprintf(stdout, "Error: pid-%d %s:%d " log "\n", getpid(), __FILE__, __LINE__, ##args); \
    } while (0)

void InitTsCiCfg1()
{
// euler和仿真环境清共享内存，清理原有DB文件
#if defined RUN_INDEPENDENT
    system("sh $TEST_HOME/tools/stop.sh -f");
#endif
    system("sh $TEST_HOME/tools/stop.sh -ts");
    TsDefulatDbFileClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts recover");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"tsLcmCheckPeriod=3\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
}

void InitTsCiCfg2()
{
// euler和仿真环境清共享内存，清理原有DB文件
#if defined RUN_INDEPENDENT
    system("sh $TEST_HOME/tools/stop.sh -f");
#endif
    system("sh $TEST_HOME/tools/stop.sh -ts");
    TsDefulatDbFileClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts recover");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"deviceSize=1\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"extendSize=1024\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
}

// 获取当前表空间大小
int TestGetSpaceInfoCnt(int *value)
{
    char command[1024] = {0};
    int ret = snprintf(command, 1024,
        "gmsysview -sql \"select * from 'V\\$STORAGE_SPACE_INFO'\" -s %s"
        "| grep index | wc -l;",
        g_connServerTsdb);
    if (ret <= 0) {
        return FAILED;
    }
    ret = TestGetResultCommand(command, value);
    if (ret) {
        return FAILED;
    }
    return 0;
}

string GetStr(const char *cmd, const char *startKeyWord = "", const char *endKeyWord = "\n")
{
    string str = "";
    char cmdOutPut[MAX_CMD_SIZE] = {0};
    FILE *pf = popen(cmd, "r");
    while (fgets(cmdOutPut, sizeof(cmdOutPut), pf) != NULL) {
        str.assign(cmdOutPut);
        string::size_type idx = str.find(startKeyWord);
        if (idx != string::npos) {
            str = str.substr(str.find(startKeyWord) + strlen(startKeyWord),
                str.find(endKeyWord) - str.find(startKeyWord) - strlen(startKeyWord));
            pclose(pf);
            return str;
        }
    }
    pclose(pf);
    return "-1Str";
}

// [out] result: 执行系统调用的结果, 使用结束后必须调用free()释放内存
int GtExecSystemCmd(char **result, const char *format, ...)
{
    int ret;
    errno = 0;
    va_list args;
    va_start(args, format);
    char cmd[1024] = {0};
    ret = vsnprintf(cmd, sizeof(cmd), format, args);
    if (ret < 0) {
        TEST_ERROR("execute vsnprintf failed, ret = %d, %s.", ret, strerror(errno));
        va_end(args);
        return FAILED;
    }
    va_end(args);

    TEST_INFO("cmd = \"%s\"", cmd);
    FILE *fd = popen(cmd, "r");
    if (fd == NULL) {
        TEST_ERROR("popen failed, %s.", strerror(errno));
        return FAILED;
    }

    // XXX 优化为动态获取流长度
    int size = 1024 * 100;
    char *tmpResult = (char *)malloc(sizeof(char) * size);
    if (tmpResult == NULL) {
        (void)pclose(fd);
        TEST_ERROR("malloc failed, %s.", strerror(errno));
        return FAILED;
    }
    memset(tmpResult, 0, size);

    char buf[1024] = {0};
    while (fgets(buf, sizeof(buf), fd) != NULL) {
        strcat(tmpResult, buf);
    }

    ret = pclose(fd);
    if (ret == -1) {
        TEST_ERROR("pclose failed, %s.", strerror(errno));
        free(tmpResult);
        return FAILED;
    }
    *result = tmpResult;
    return GMERR_OK;
}

int CreateTsTable(GmcStmtT *stmt, char *tsTableName, bool isMemory = false)
{
    int ret = 0;
    char sqlCmd[256] = {0};
    if (isMemory == false) {
        (void)snprintf(sqlCmd,
            MAX_CMD_SIZE,
            "create table if not exists %s(id integer, time integer, num1 integer) with (time_col = 'time', interval = "
            "'1 "
            "hour', compression = 'fast(rapidlz)');",
            tsTableName);
    } else {
        (void)snprintf(sqlCmd,
            MAX_CMD_SIZE,
            "create table if not exists %s(id integer, time integer, num1 integer) with (engine = 'memory', time_col = "
            "'time', interval = "
            "'1 "
            "hour', compression = 'fast(rapidlz)', max_size = '100000000');",
            tsTableName);
    }

    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    return ret;
}

int CreateTsTable2(GmcStmtT *stmt, char *tsTableName, bool isMemory = false)
{
    int ret = 0;
    char sqlCmd[256] = {0};
    if (isMemory == false) {
        (void)snprintf(sqlCmd,
            MAX_CMD_SIZE,
            "create table if not exists %s(id integer, time integer, num1 integer, name text) with (time_col = 'time', "
            "interval = "
            "'1 "
            "hour', compression = 'fast(rapidlz)');",
            tsTableName);
    } else {
        (void)snprintf(sqlCmd,
            MAX_CMD_SIZE,
            "create table if not exists %s(id integer, time integer, num1 integer, name text) with (engine = 'memory', "
            "time_col = "
            "'time', interval = "
            "'1 "
            "hour', compression = 'fast(rapidlz)');",
            tsTableName);
    }

    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    return ret;
}

int CreateTsTable3(GmcStmtT *stmt, char *tsTableName, bool isMemory = false)
{
    int ret = 0;
    char sqlCmd[256] = {0};
    if (isMemory == false) {
        (void)snprintf(sqlCmd,
            MAX_CMD_SIZE,
            "create table if not exists %s(id integer, time integer, num1 integer) with (time_col = 'time', interval = "
            "'1 "
            "hour', compression = 'no', disk_limit = '4500 KB');",
            tsTableName);
    } else {
        (void)snprintf(sqlCmd,
            MAX_CMD_SIZE,
            "create table if not exists %s(id integer, time integer, num1 integer) with (engine = 'memory', time_col = "
            "'time', interval = "
            "'1 "
            "hour', disk_limit = '4 KB');",
            tsTableName);
    }

    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    return ret;
}

int CreateTsTable5(GmcStmtT *stmt, char *tsTableName, bool isMemory = false)
{
    int ret = 0;
    char sqlCmd[256] = {0};
    if (isMemory == false) {
        (void)snprintf(sqlCmd,
            MAX_CMD_SIZE,
            "create table if not exists %s(id integer, time integer, num1 integer) with (time_col = 'time', interval = "
            "'1 "
            "min', compression = 'fast(rapidlz)', ttl = '1 min');",
            tsTableName);
    } else {
        (void)snprintf(sqlCmd,
            MAX_CMD_SIZE,
            "create table if not exists %s(id integer, time integer, num1 integer) with (engine = 'memory', time_col = "
            "'time', interval = "
            "'1 "
            "min', compression = 'fast(rapidlz)', ttl = '1 min');",
            tsTableName);
    }

    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    return ret;
}

int TruncateTsTable(GmcStmtT *stmt, char *tsTableName)
{
    int ret = 0;
    char sqlCmd[256] = {0};
    (void)snprintf(sqlCmd, MAX_CMD_SIZE, "truncate table %s;", tsTableName);

    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    return ret;
}

int InsertData(GmcStmtT *stmt, char *tsTable1, int64_t batchCount, int64_t insertCount = 50000)
{
    int ret = 0;
    int64_t count = insertCount;
    int64_t id[count];
    memset(id, 0, sizeof(id));
    int64_t time[count];
    memset(time, 0, sizeof(time));
    int64_t num1[count];
    memset(num1, 0, sizeof(num1));

    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1704038400 + i;
        num1[i] = i;
    }

    for (int i = 0; i < batchCount; i++) {
        ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, 0, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, time, 0, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, num1, 0, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcPrepareStmtByLabelName(stmt, tsTable1, GMC_OPERATION_SQL_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}

int InsertData(GmcStmtT *stmt, char *tsTable1, char *tsTable2, int64_t batchCount)
{
    int ret = 0;
    int64_t count = 50000;
    int64_t id[count];
    memset(id, 0, sizeof(id));
    int64_t time[count];
    memset(time, 0, sizeof(time));
    int64_t num1[count];
    memset(num1, 0, sizeof(num1));

    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1704038400 + i;
        num1[i] = i;
    }

    for (int i = 0; i < batchCount; i++) {
        ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, 0, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, time, 0, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, num1, 0, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcPrepareStmtByLabelName(stmt, tsTable1, GMC_OPERATION_SQL_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, 0, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, time, 0, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, num1, 0, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcPrepareStmtByLabelName(stmt, tsTable2, GMC_OPERATION_SQL_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}

void *DropTsTableThread(void *args)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char tsTableName[MAX_CMD_SIZE] = {0};
    for (int64_t i = 1; i <= 100; ++i) {
        (void)snprintf(tsTableName, MAX_CMD_SIZE, "tsTable%lld", i);
        ret = DropTsTable(stmt, tsTableName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    g_threadWait++;
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

void *InsertDataThread(void *args)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t batchCount = 9;
    ret = InsertData(stmt, g_tableName1, batchCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_threadWait++;
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

void *InsertDataThread2(void *args)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t batchCount = 1;
    ret = InsertData(stmt, g_tableName1, batchCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(3);
    g_threadWait++;
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

void *SwapDataDirThread(void *args)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *pwdPath = getenv("PWD");
    AW_FUN_Log(LOG_STEP, "创建路径2");
    ret = system("mkdir gmdb2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char dataFilePath2[150] = {0};
    (void)snprintf(dataFilePath2, MAX_CMD_SIZE, "%s/gmdb2", pwdPath);

    while (g_threadWait < 1) {  // 等待另一个线程触发再切换路径
        usleep(100);
    }

    ret = GmcSwapDataDir(stmt, dataFilePath2, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

void *MemoryTableInsertDataThread(void *args)
{
    int ret = 0;
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    ret = TestTsGmcConnect(&conn1, &stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t batchCount = 1;
    int64_t insertCount = 170;
    int64_t tryCnt1 = 0;
    while (tryCnt1 < 1000) {
        ret = InsertData(stmt1, g_tableName1, batchCount, insertCount);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        usleep(1000);
        tryCnt1++;
    }
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

void *LogicalTableInsertDataThread(void *args)
{
    int ret = 0;
    GmcConnT *conn2 = NULL;
    GmcStmtT *stmt2 = NULL;
    ret = TestTsGmcConnect(&conn2, &stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t batchCount = 1;
    int64_t tryCnt2 = 0;
    while (tryCnt2 < 1000) {
        ret = InsertData(g_stmt, g_tableName2, batchCount);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        usleep(1000);
        tryCnt2++;
    }
    ret = testGmcDisconnect(conn2, stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}
#endif
