/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * File Name: tsdb_dts_enhanced.cpp
 * Description: 长稳问题单加固用例(GmcSwapDataDir)
 * Author: chenbangjun
 * Create: 2025-07-10
 */
#include "gtest/gtest.h"
#include "t_rd_ts.h"
#include "tsdb_dts_enhanced.h"

class TsdbDtsEnhanced : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        InitTsCiCfg1();
        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        close_epoll_thread();
        testEnvClean();
        RecoverTsCiCfg();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void TsdbDtsEnhanced::SetUp()
{
    system("rm -rf gmdb1");
    system("rm -rf gmdb2");
    AW_ADD_FOLD_WHITE_LIST(1, "Db would go through recover process");
    AW_ADD_FOLD_WHITE_LIST(1, "Operate successfully. ctrl File");
    AW_CHECK_LOG_BEGIN();
    g_threadWait = 0;
    int ret = 0;
    ret = TestTsGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TsdbDtsEnhanced::TearDown()
{
    int ret = 0;
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_END();
}

class TsdbDtsEnhanced2 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        InitTsCiCfg2();
        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        close_epoll_thread();
        testEnvClean();
        RecoverTsCiCfg();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void TsdbDtsEnhanced2::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    int ret = 0;
    ret = TestTsGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TsdbDtsEnhanced2::TearDown()
{
    int ret = 0;
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_END();
}

// 001.内存表和逻辑表插入数据（100w），先truncate再swap；内存表和逻辑表插入数据（100w）覆盖插入100W再drop，切换到原路径
// DTS2025051922082
TEST_F(TsdbDtsEnhanced, Timing_078_Dts_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "创建路径1，插入100W数据");
    ret = system("mkdir gmdb1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *pwdPath = getenv("PWD");
    char dataFilePath1[150] = {0};
    (void)snprintf(dataFilePath1, MAX_CMD_SIZE, "%s/gmdb1", pwdPath);
    ret = GmcSwapDataDir(g_stmt, dataFilePath1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = CreateTsTable(g_stmt, g_tableName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateTsTable(g_stmt, g_tableName2, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t batchCount = 20;
    ret = InsertData(g_stmt, g_tableName1, g_tableName2, batchCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TruncateTsTable(g_stmt, g_tableName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TruncateTsTable(g_stmt, g_tableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "创建路径2，插入100W数据");
    ret = system("mkdir gmdb2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char dataFilePath2[150] = {0};
    (void)snprintf(dataFilePath2, MAX_CMD_SIZE, "%s/gmdb2", pwdPath);
    ret = GmcSwapDataDir(g_stmt, dataFilePath2, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = CreateTsTable(g_stmt, g_tableName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateTsTable(g_stmt, g_tableName2, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InsertData(g_stmt, g_tableName1, g_tableName2, batchCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTsTable(g_stmt, g_tableName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTsTable(g_stmt, g_tableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int spaceCount = 0;
    ret = TestGetSpaceInfoCnt(&spaceCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        if (spaceCount != 3) {
            AW_FUN_Log(LOG_DEBUG, "spaceCount is %d\n", spaceCount);
            usleep(200000);
            ret = TestGetSpaceInfoCnt(&spaceCount);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            break;
        }
    }
    // 表已被删除
    AW_MACRO_EXPECT_EQ_INT(3, spaceCount);

    AW_FUN_Log(LOG_STEP, "切换回路径1，清理环境");
    ret = GmcSwapDataDir(g_stmt, dataFilePath1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTsTable(g_stmt, g_tableName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTsTable(g_stmt, g_tableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("rm -rf gmdb1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("rm -rf gmdb2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 002.多次swap切换（切换100次），查看动态内存和共享内存无明显上涨（COM_DYN_CTX、COM_SHMEM_CTX）
// DTS2025051509893
TEST_F(TsdbDtsEnhanced, Timing_078_Dts_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);
    AddWhiteList(GMERR_DATABASE_NOT_AVAILABLE);
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "创建路径1，插入数据");
    ret = system("mkdir gmdb1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *pwdPath = getenv("PWD");
    char dataFilePath1[150] = {0};
    (void)snprintf(dataFilePath1, MAX_CMD_SIZE, "%s/gmdb1", pwdPath);
    ret = GmcSwapDataDir(g_stmt, dataFilePath1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateTsTable(g_stmt, g_tableName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateTsTable(g_stmt, g_tableName2, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t batchCount = 1;
    ret = InsertData(g_stmt, g_tableName1, g_tableName2, batchCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "创建路径2，插入数据");
    ret = system("mkdir gmdb2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char dataFilePath2[150] = {0};
    (void)snprintf(dataFilePath2, MAX_CMD_SIZE, "%s/gmdb2", pwdPath);
    ret = GmcSwapDataDir(g_stmt, dataFilePath2, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateTsTable(g_stmt, g_tableName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateTsTable(g_stmt, g_tableName2, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InsertData(g_stmt, g_tableName1, g_tableName2, batchCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "切换前查询视图");
    char cmd1[MAX_CMD_SIZE];
    char cmd2[MAX_CMD_SIZE];
    (void)snprintf(cmd1,
        MAX_CMD_SIZE,
        "gmsysview -sql \"select * from 'V\\$COM_DYN_CTX'\" -s %s | grep \"index = 0\" -C 20 |grep GLOBAL_PHY_SIZE",
        g_connServerTsdb);
    (void)snprintf(cmd2,
        MAX_CMD_SIZE,
        "gmsysview -sql \"select * from 'V\\$COM_SHMEM_CTX'\" -s %s | grep \"index = 0\" -C 20 |grep GLOBAL_PHY_SIZE",
        g_connServerTsdb);

    string dynPhySizeStringM1 = GetStr(cmd1, "GLOBAL_PHY_SIZE: [", "] MB");
    int dynPhySizeM1 = 0;
    if (dynPhySizeStringM1 != "") {
        dynPhySizeM1 = stoi(dynPhySizeStringM1);
    }
    string dynPhySizeStringK1 = GetStr(cmd1, "MB [", "] KB");
    int dynPhySizeK1 = 0;
    if (dynPhySizeStringK1 != "") {
        dynPhySizeK1 = stoi(dynPhySizeStringK1);
    }
    int totalDynSize1 = dynPhySizeM1 * 1024 + dynPhySizeK1;

    string shmPhySizeStringM1 = GetStr(cmd2, "GLOBAL_PHY_SIZE: [", "] MB");
    int shmPhySizeM1 = 0;
    if (shmPhySizeStringM1 != "") {
        shmPhySizeM1 = stoi(shmPhySizeStringM1);
    }
    string shmPhySizeStringK1 = GetStr(cmd2, "MB [", "] KB");
    int shmPhySizeK1 = 0;
    if (shmPhySizeStringK1 != "") {
        int shmPhySizeK1 = stoi(shmPhySizeStringK1);
    }
    int totalshmSize1 = shmPhySizeM1 * 1024 + shmPhySizeK1;
    system(cmd1);
    system(cmd2);

    system("gmsysview -sql \"select * from 'V\\$COM_DYN_CTX'\" -s usocket:/run/verona/unix_emserver_tsdb >dyn1.txt");
    system("gmsysview -sql \"select * from 'V\\$COM_SHMEM_CTX'\" -s usocket:/run/verona/unix_emserver_tsdb >shm1.txt");

    for (int64_t i = 1; i <= 100; ++i) {
        ret = GmcSwapDataDir(g_stmt, dataFilePath1, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSwapDataDir(g_stmt, dataFilePath2, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "切换后查询视图");
    system(cmd1);
    system(cmd2);
        system("gmsysview -sql \"select * from 'V\\$COM_DYN_CTX'\" -s usocket:/run/verona/unix_emserver_tsdb >dyn2.txt");
        system("gmsysview -sql \"select * from 'V\\$COM_SHMEM_CTX'\" -s usocket:/run/verona/unix_emserver_tsdb >shm2.txt");
    string dynPhySizeStringM2 = GetStr(cmd1, "GLOBAL_PHY_SIZE: [", "] MB");
    int dynPhySizeM2 = 0;
    if (dynPhySizeStringM2 != "") {
        dynPhySizeM2 = stoi(dynPhySizeStringM2);
    }
    string dynPhySizeStringK2 = GetStr(cmd1, "MB [", "] KB");
    int dynPhySizeK2 = 0;
    if (dynPhySizeStringK2 != "") {
        dynPhySizeK2 = stoi(dynPhySizeStringK2);
    }
    int totalDynSize2 = dynPhySizeM2 * 1024 + dynPhySizeK2;

    string shmPhySizeStringM2 = GetStr(cmd2, "GLOBAL_PHY_SIZE: [", "] MB");
    int shmPhySizeM2 = 0;
    if (shmPhySizeStringM2 != "") {
        shmPhySizeM2 = stoi(shmPhySizeStringM2);
    }
    string shmPhySizeStringK2 = GetStr(cmd2, "MB [", "] KB");
    int shmPhySizeK2 = 0;
    if (shmPhySizeStringK2 != "") {
        shmPhySizeK2 = stoi(shmPhySizeStringK2);
    }
    int totalshmSize2 = shmPhySizeM2 * 1024 + shmPhySizeK2;
#if defined ENV_RTOSV2
    EXPECT_LT(totalDynSize2 - totalDynSize1, 1024 * 4);
#else
    EXPECT_LT(totalDynSize2 - totalDynSize1, 1024 * 3);
#endif
    EXPECT_LT(totalshmSize2 - totalshmSize1, 1024 * 2);

    AW_FUN_Log(LOG_STEP, "切换回路径1，清理环境");
    ret = GmcSwapDataDir(g_stmt, dataFilePath1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTsTable(g_stmt, g_tableName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTsTable(g_stmt, g_tableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int spaceCount = 0;
    ret = TestGetSpaceInfoCnt(&spaceCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        if (spaceCount != 3) {
            AW_FUN_Log(LOG_DEBUG, "spaceCount is %d\n", spaceCount);
            usleep(200000);
            ret = TestGetSpaceInfoCnt(&spaceCount);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            break;
        }
    }
    // 表已被删除
    AW_MACRO_EXPECT_EQ_INT(3, spaceCount);

    ret = system("rm -rf gmdb1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("rm -rf gmdb2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 003.内存表触发disk_limit并发swap
// DTS2025043027230
TEST_F(TsdbDtsEnhanced, Timing_078_Dts_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);
    AddWhiteList(GMERR_DATABASE_NOT_AVAILABLE);
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "创建路径1，插入数据");
    ret = system("mkdir gmdb1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *pwdPath = getenv("PWD");
    char dataFilePath1[150] = {0};
    (void)snprintf(dataFilePath1, MAX_CMD_SIZE, "%s/gmdb1", pwdPath);
    ret = GmcSwapDataDir(g_stmt, dataFilePath1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateTsTable3(g_stmt, g_tableName1, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_t thrArr[2];
    ret = pthread_create(&thrArr[0], NULL, InsertDataThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thrArr[1], NULL, SwapDataDirThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_join(thrArr[0], NULL);
    pthread_join(thrArr[1], NULL);

    AW_FUN_Log(LOG_STEP, "切换回路径1，清理环境");
    ret = GmcSwapDataDir(g_stmt, dataFilePath1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTsTable(g_stmt, g_tableName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int spaceCount = 0;
    ret = TestGetSpaceInfoCnt(&spaceCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        if (spaceCount != 3) {
            AW_FUN_Log(LOG_DEBUG, "spaceCount is %d\n", spaceCount);
            usleep(200000);
            ret = TestGetSpaceInfoCnt(&spaceCount);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            break;
        }
    }
    // 表已被删除
    AW_MACRO_EXPECT_EQ_INT(3, spaceCount);

    ret = system("rm -rf gmdb1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("rm -rf gmdb2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 004.逻辑表触发disk_limit并发swap
TEST_F(TsdbDtsEnhanced, Timing_078_Dts_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);
    AddWhiteList(GMERR_DATABASE_NOT_AVAILABLE);
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "创建路径1，插入数据");
    ret = system("mkdir gmdb1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *pwdPath = getenv("PWD");
    char dataFilePath1[150] = {0};
    (void)snprintf(dataFilePath1, MAX_CMD_SIZE, "%s/gmdb1", pwdPath);
    ret = GmcSwapDataDir(g_stmt, dataFilePath1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateTsTable3(g_stmt, g_tableName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_t thrArr[2];
    ret = pthread_create(&thrArr[0], NULL, InsertDataThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thrArr[1], NULL, SwapDataDirThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_join(thrArr[0], NULL);
    pthread_join(thrArr[1], NULL);

    AW_FUN_Log(LOG_STEP, "切换回路径1，清理环境");
    ret = GmcSwapDataDir(g_stmt, dataFilePath1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTsTable(g_stmt, g_tableName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int spaceCount = 0;
    ret = TestGetSpaceInfoCnt(&spaceCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        if (spaceCount != 3) {
            AW_FUN_Log(LOG_DEBUG, "spaceCount is %d\n", spaceCount);
            usleep(200000);
            ret = TestGetSpaceInfoCnt(&spaceCount);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            break;
        }
    }
    // 表已被删除
    AW_MACRO_EXPECT_EQ_INT(3, spaceCount);

    ret = system("rm -rf gmdb1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("rm -rf gmdb2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 005.内存表触发TTL并发swap
TEST_F(TsdbDtsEnhanced, Timing_078_Dts_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "创建路径1，插入数据");
    ret = system("mkdir gmdb1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *pwdPath = getenv("PWD");
    char dataFilePath1[150] = {0};
    (void)snprintf(dataFilePath1, MAX_CMD_SIZE, "%s/gmdb1", pwdPath);
    ret = GmcSwapDataDir(g_stmt, dataFilePath1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateTsTable5(g_stmt, g_tableName1, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_t thrArr[2];
    ret = pthread_create(&thrArr[0], NULL, InsertDataThread2, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thrArr[1], NULL, SwapDataDirThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_join(thrArr[0], NULL);
    pthread_join(thrArr[1], NULL);

    AW_FUN_Log(LOG_STEP, "切换回路径1，清理环境");
    ret = GmcSwapDataDir(g_stmt, dataFilePath1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTsTable(g_stmt, g_tableName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int spaceCount = 0;
    ret = TestGetSpaceInfoCnt(&spaceCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        if (spaceCount != 3) {
            AW_FUN_Log(LOG_DEBUG, "spaceCount is %d\n", spaceCount);
            usleep(200000);
            ret = TestGetSpaceInfoCnt(&spaceCount);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            break;
        }
    }
    // 表已被删除
    AW_MACRO_EXPECT_EQ_INT(3, spaceCount);

    ret = system("rm -rf gmdb1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("rm -rf gmdb2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 006.逻辑表触发TTL并发swap
TEST_F(TsdbDtsEnhanced, Timing_078_Dts_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);
    AddWhiteList(GMERR_DATABASE_NOT_AVAILABLE);
    AddWhiteList(GMERR_REQUEST_TIME_OUT);
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "创建路径1，插入数据");
    ret = system("mkdir gmdb1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *pwdPath = getenv("PWD");
    char dataFilePath1[150] = {0};
    (void)snprintf(dataFilePath1, MAX_CMD_SIZE, "%s/gmdb1", pwdPath);
    ret = GmcSwapDataDir(g_stmt, dataFilePath1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateTsTable5(g_stmt, g_tableName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_t thrArr[2];
    ret = pthread_create(&thrArr[0], NULL, InsertDataThread2, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thrArr[1], NULL, SwapDataDirThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_join(thrArr[0], NULL);
    pthread_join(thrArr[1], NULL);

    AW_FUN_Log(LOG_STEP, "切换回路径1，清理环境");
    ret = GmcSwapDataDir(g_stmt, dataFilePath1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTsTable(g_stmt, g_tableName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int spaceCount = 0;
    ret = TestGetSpaceInfoCnt(&spaceCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        if (spaceCount != 3) {
            AW_FUN_Log(LOG_DEBUG, "spaceCount is %d\n", spaceCount);
            sleep(1);
            ret = TestGetSpaceInfoCnt(&spaceCount);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            break;
        }
    }
    // 表已被删除
    AW_MACRO_EXPECT_EQ_INT(3, spaceCount);

    ret = system("rm -rf gmdb1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("rm -rf gmdb2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 007.drop和swap并发出现问题，构造drop100张逻辑表后swap的用例
// DTS2025041831403
TEST_F(TsdbDtsEnhanced, Timing_078_Dts_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);
    AddWhiteList(GMERR_DATABASE_NOT_AVAILABLE);

    int ret = 0;
    AW_FUN_Log(LOG_STEP, "创建路径1，插入100张表");
    ret = system("mkdir gmdb1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *pwdPath = getenv("PWD");
    char dataFilePath1[150] = {0};
    (void)snprintf(dataFilePath1, MAX_CMD_SIZE, "%s/gmdb1", pwdPath);
    ret = GmcSwapDataDir(g_stmt, dataFilePath1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int64_t batchCount = 20;
    char tsTableName[MAX_CMD_SIZE] = {0};
    for (int64_t i = 1; i <= 100; ++i) {
        (void)snprintf(tsTableName, MAX_CMD_SIZE, "tsTable%lld", i);
        ret = CreateTsTable(g_stmt, tsTableName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = InsertData(g_stmt, tsTableName, batchCount);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    pthread_t thrArr[2];
    ret = pthread_create(&thrArr[0], NULL, DropTsTableThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thrArr[1], NULL, SwapDataDirThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_join(thrArr[0], NULL);
    pthread_join(thrArr[1], NULL);

    int spaceCount = 0;
    ret = TestGetSpaceInfoCnt(&spaceCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        if (spaceCount != 3) {
            AW_FUN_Log(LOG_DEBUG, "spaceCount is %d\n", spaceCount);
            usleep(200000);
            ret = TestGetSpaceInfoCnt(&spaceCount);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            break;
        }
    }
    // 表已被删除
    AW_MACRO_EXPECT_EQ_INT(3, spaceCount);

    AW_FUN_Log(LOG_STEP, "切换回路径1，清理环境");
    ret = GmcSwapDataDir(g_stmt, dataFilePath1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("rm -rf gmdb1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("rm -rf gmdb2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 008.目录A和目录B建表逻辑表和内存表，写入数据，查询磁盘占用数据。切换到新目录，再切回来，再次查询磁盘占用数据。
// DTS2025041756343
TEST_F(TsdbDtsEnhanced, Timing_078_Dts_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);
    AddWhiteList(GMERR_DATABASE_NOT_AVAILABLE);
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "创建路径1，插入数据");
    ret = system("mkdir gmdb1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *pwdPath = getenv("PWD");
    char dataFilePath1[150] = {0};
    (void)snprintf(dataFilePath1, MAX_CMD_SIZE, "%s/gmdb1", pwdPath);
    ret = GmcSwapDataDir(g_stmt, dataFilePath1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateTsTable(g_stmt, g_tableName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateTsTable(g_stmt, g_tableName2, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t batchCount = 1;
    ret = InsertData(g_stmt, g_tableName1, g_tableName2, batchCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "创建路径2，插入数据");
    ret = system("mkdir gmdb2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char dataFilePath2[150] = {0};
    (void)snprintf(dataFilePath2, MAX_CMD_SIZE, "%s/gmdb2", pwdPath);
    ret = GmcSwapDataDir(g_stmt, dataFilePath2, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateTsTable(g_stmt, g_tableName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateTsTable(g_stmt, g_tableName2, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InsertData(g_stmt, g_tableName1, g_tableName2, batchCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *beforeFileSize1 = NULL;
    char *beforeFileSize2 = NULL;
    char *afterFileSize1 = NULL;
    char *afterFileSize2 = NULL;
    ret = GtExecSystemCmd(&beforeFileSize1, "ls -l |grep gmdb1 | awk {'print $5'}");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSystemCmd(&beforeFileSize2, "ls -l |grep gmdb2 | awk {'print $5'}");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSwapDataDir(g_stmt, dataFilePath1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSwapDataDir(g_stmt, dataFilePath2, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GtExecSystemCmd(&afterFileSize1, "ls -l |grep gmdb1 | awk {'print $5'}");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSystemCmd(&afterFileSize2, "ls -l |grep gmdb2 | awk {'print $5'}");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t bfFileSize1 = atoi(beforeFileSize1);
    int32_t afFileSize1 = atoi(afterFileSize1);
    int32_t bfFileSize2 = atoi(beforeFileSize2);
    int32_t afFileSize2 = atoi(afterFileSize2);
    AW_MACRO_EXPECT_EQ_INT(afFileSize1, bfFileSize1);
    AW_MACRO_EXPECT_EQ_INT(afFileSize2, bfFileSize2);
    free(beforeFileSize1);
    free(beforeFileSize2);
    free(afterFileSize1);
    free(afterFileSize2);

    AW_FUN_Log(LOG_STEP, "清理环境");
    ret = DropTsTable(g_stmt, g_tableName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTsTable(g_stmt, g_tableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int spaceCount = 0;
    ret = TestGetSpaceInfoCnt(&spaceCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        if (spaceCount != 3) {
            AW_FUN_Log(LOG_DEBUG, "spaceCount is %d\n", spaceCount);
            usleep(200000);
            ret = TestGetSpaceInfoCnt(&spaceCount);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            break;
        }
    }
    // 表已被删除
    AW_MACRO_EXPECT_EQ_INT(3, spaceCount);

    ret = GmcSwapDataDir(g_stmt, dataFilePath1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTsTable(g_stmt, g_tableName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTsTable(g_stmt, g_tableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    spaceCount = 0;
    ret = TestGetSpaceInfoCnt(&spaceCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        if (spaceCount != 3) {
            AW_FUN_Log(LOG_DEBUG, "spaceCount is %d\n", spaceCount);
            usleep(200000);
            ret = TestGetSpaceInfoCnt(&spaceCount);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            break;
        }
    }
    // 表已被删除
    AW_MACRO_EXPECT_EQ_INT(3, spaceCount);

    ret = system("rm -rf gmdb1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("rm -rf gmdb2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 009.(循环20次)多次切换目录的同时，对表插入数据目录A（表A）->目录B
// DTS2025041629240
TEST_F(TsdbDtsEnhanced, Timing_078_Dts_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);
    int ret = 0;
    ret = system("mkdir gmdb1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir gmdb2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *pwdPath = getenv("PWD");
    char dataFilePath1[150] = {0};
    (void)snprintf(dataFilePath1, MAX_CMD_SIZE, "%s/gmdb1", pwdPath);
    ret = GmcSwapDataDir(g_stmt, dataFilePath1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateTsTable(g_stmt, g_tableName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateTsTable(g_stmt, g_tableName2, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char dataFilePath2[150] = {0};
    (void)snprintf(dataFilePath2, MAX_CMD_SIZE, "%s/gmdb2", pwdPath);
    ret = GmcSwapDataDir(g_stmt, dataFilePath2, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateTsTable(g_stmt, g_tableName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateTsTable(g_stmt, g_tableName2, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int64_t batchCount = 1;
    int64_t cycleTime = 20;
    for (int i = 0; i < cycleTime; i++) {
        ret = GmcSwapDataDir(g_stmt, dataFilePath1, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = InsertData(g_stmt, g_tableName1, g_tableName2, batchCount);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSwapDataDir(g_stmt, dataFilePath2, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = InsertData(g_stmt, g_tableName1, g_tableName2, batchCount);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = DropTsTable(g_stmt, g_tableName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTsTable(g_stmt, g_tableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int spaceCount = 0;
    ret = TestGetSpaceInfoCnt(&spaceCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        if (spaceCount != 3) {
            AW_FUN_Log(LOG_DEBUG, "spaceCount is %d\n", spaceCount);
            usleep(200000);
            ret = TestGetSpaceInfoCnt(&spaceCount);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            break;
        }
    }
    // 表已被删除
    AW_MACRO_EXPECT_EQ_INT(3, spaceCount);

    AW_FUN_Log(LOG_STEP, "切换回路径1，清理环境");
    ret = GmcSwapDataDir(g_stmt, dataFilePath1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTsTable(g_stmt, g_tableName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTsTable(g_stmt, g_tableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    spaceCount = 0;
    ret = TestGetSpaceInfoCnt(&spaceCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        if (spaceCount != 3) {
            AW_FUN_Log(LOG_DEBUG, "spaceCount is %d\n", spaceCount);
            usleep(200000);
            ret = TestGetSpaceInfoCnt(&spaceCount);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            break;
        }
    }
    // 表已被删除
    AW_MACRO_EXPECT_EQ_INT(3, spaceCount);

    ret = system("rm -rf gmdb1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("rm -rf gmdb2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 010.对内存表插入数据时，inet列未绑定数据时(inet类型应该填充8个0)，预期插入成功
// DTS2025031033023
TEST_F(TsdbDtsEnhanced, Timing_078_Dts_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char sqlCmd[256] = {0};
    (void)snprintf(sqlCmd,
        MAX_CMD_SIZE,
        "create table if not exists %s(id integer, time integer, num1 integer, ip inet) with (engine = 'memory', "
        "time_col = "
        "'time', interval = "
        "'1 "
        "hour', compression = 'fast(rapidlz)');",
        g_tableName1);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int64_t batchCount = 1;
    int64_t insertCount = 100;
    ret = InsertData(g_stmt, g_tableName1, batchCount, insertCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command,
        MAX_CMD_SIZE,
        "%s/gmsysview -sql \"select * from %s\" -s %s |grep \"ip: 00000000\" |wc -l",
        g_toolPath,
        g_tableName1,
        g_connServerTsdb);
    ret = executeCommand(g_command, "100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = DropTsTable(g_stmt, g_tableName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int spaceCount = 0;
    ret = TestGetSpaceInfoCnt(&spaceCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        if (spaceCount != 3) {
            AW_FUN_Log(LOG_DEBUG, "spaceCount is %d\n", spaceCount);
            usleep(200000);
            ret = TestGetSpaceInfoCnt(&spaceCount);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            break;
        }
    }
    // 表已被删除
    AW_MACRO_EXPECT_EQ_INT(3, spaceCount);
}

// 011.表中无数据，执行select length(text类型列) from table where length(text类型列) > 3 生成buildin算子时报错,
// 时序逻辑表和内存表无数据，执行上面的语句，预期不报错
// DTS2025011723021
TEST_F(TsdbDtsEnhanced, Timing_078_Dts_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateTsTable2(g_stmt, g_tableName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateTsTable2(g_stmt, g_tableName2, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "执行sql");
    (void)snprintf(g_command,
        MAX_CMD_SIZE,
        "%s/gmsysview -sql \"select length(name) from %s where length(name) > 3\" -s %s",
        g_toolPath,
        g_tableName1,
        g_connServerTsdb);
    ret = executeCommand(g_command, "gmsysview query total consumed time:");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    (void)snprintf(g_command,
        MAX_CMD_SIZE,
        "%s/gmsysview -sql \"select length(name) from %s where length(name) > 3\" -s %s",
        g_toolPath,
        g_tableName2,
        g_connServerTsdb);
    ret = executeCommand(g_command, "gmsysview query total consumed time:");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    ret = DropTsTable(g_stmt, g_tableName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTsTable(g_stmt, g_tableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int spaceCount = 0;
    ret = TestGetSpaceInfoCnt(&spaceCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        if (spaceCount != 3) {
            AW_FUN_Log(LOG_DEBUG, "spaceCount is %d\n", spaceCount);
            usleep(200000);
            ret = TestGetSpaceInfoCnt(&spaceCount);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            break;
        }
    }
    // 表已被删除
    AW_MACRO_EXPECT_EQ_INT(3, spaceCount);
}

// 012.内存表，多个device情况下插入数据, 查询数据,truncate清空数据, drop表；
TEST_F(TsdbDtsEnhanced2, Timing_078_Dts_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateTsTable(g_stmt, g_tableName2, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t batchCount = 200;
    ret = InsertData(g_stmt, g_tableName2, batchCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char cmd1[MAX_CMD_SIZE];
    char cmd2[MAX_CMD_SIZE];
    (void)snprintf(cmd1,
        MAX_CMD_SIZE,
        "gmsysview -sql \"select * from 'V\\$STORAGE_MEMDATA_STAT'\" -s %s |grep DEVICE_COUNT |grep -v "
        "IDLE_DEVICE_COUNT",
        g_connServerTsdb);
    (void)snprintf(
        cmd2, MAX_CMD_SIZE, "gmsysview -sql \"select count(*) from %s\" -s %s", g_tableName2, g_connServerTsdb);
    system(cmd1);
    system(cmd2);

    string deviceCountString = GetStr(cmd1, "DEVICE_COUNT: ");
    int deviceCount = stoi(deviceCountString);
    EXPECT_LT(900, deviceCount);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -sql \"select count(*) from %s\" -s %s", g_toolPath, g_tableName2,
        g_connServerTsdb);
    ret = executeCommand(g_command, "10000000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // STORAGE_VERTEX_COUNT查视图
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -sql \"select * from 'V\\$STORAGE_VERTEX_COUNT'\" -s %s", g_toolPath,
        g_connServerTsdb);
    system(g_command);

    ret = TruncateTsTable(g_stmt, g_tableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTsTable(g_stmt, g_tableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int spaceCount = 0;
    ret = TestGetSpaceInfoCnt(&spaceCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        if (spaceCount != 3) {
            AW_FUN_Log(LOG_DEBUG, "spaceCount is %d\n", spaceCount);
            usleep(200000);
            ret = TestGetSpaceInfoCnt(&spaceCount);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            break;
        }
    }
    // 表已被删除
    AW_MACRO_EXPECT_EQ_INT(3, spaceCount);
}

// 013.内存表和逻辑表，一直并发触发disklimit，两个线程，线程1循环插入内存表，线程2循环插入逻辑表；1000次
TEST_F(TsdbDtsEnhanced, Timing_078_Dts_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateTsTable3(g_stmt, g_tableName1, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateTsTable3(g_stmt, g_tableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int64_t batchCount = 1;
    int64_t insertCount = 170;
    ret = InsertData(g_stmt, g_tableName1, batchCount, insertCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InsertData(g_stmt, g_tableName2, batchCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char cmd1[MAX_CMD_SIZE];
    (void)snprintf(
        cmd1, MAX_CMD_SIZE, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s ", g_connServerTsdb);
    system(cmd1);

    pthread_t thrArr[2];
    ret = pthread_create(&thrArr[0], NULL, MemoryTableInsertDataThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thrArr[1], NULL, LogicalTableInsertDataThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_join(thrArr[0], NULL);
    pthread_join(thrArr[1], NULL);

    ret = DropTsTable(g_stmt, g_tableName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTsTable(g_stmt, g_tableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int spaceCount = 0;
    ret = TestGetSpaceInfoCnt(&spaceCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        if (spaceCount != 3) {
            AW_FUN_Log(LOG_DEBUG, "spaceCount is %d\n", spaceCount);
            usleep(200000);
            ret = TestGetSpaceInfoCnt(&spaceCount);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            break;
        }
    }
    // 表已被删除
    AW_MACRO_EXPECT_EQ_INT(3, spaceCount);
}
