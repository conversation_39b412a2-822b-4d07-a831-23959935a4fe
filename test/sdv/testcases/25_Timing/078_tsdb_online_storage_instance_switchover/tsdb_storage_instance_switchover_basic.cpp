/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: 时序交付，TSDB支持在线存储实例切换
 * Author: chenbangjun
 * Create: 2025-02-27
 */
#include "gtest/gtest.h"
#include "t_rd_ts.h"
#include "tsdb_storage_instance_switchover.h"

static GmcConnOptionsT *connOptions = NULL;
static GmcConnT *conn = NULL;
static GmcStmtT *stmt = NULL;
char g_cStoreDir[64] = {0};
char *dir = getenv("GMDB_HOME");
Status ret = 0;

class TsdbOnlineSwitchStorageBasic : public testing::Test {
protected:
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}

public:
    virtual void SetUp();
    virtual void TearDown();
};

void TsdbOnlineSwitchStorageBasic::SetUp()
{
// euler和仿真环境清共享内存，清理原有DB文件
#if defined RUN_INDEPENDENT
    system("sh $TEST_HOME/tools/stop.sh -f");
#endif
    AW_CHECK_LOG_BEGIN();
    (void)sprintf(g_dataFilePath1, "%s/data/gmdb1", g_filePath);
    (void)sprintf(g_dataFilePath2, "%s/data/gmdb2", g_filePath);
    (void)sprintf(g_dataFilePath3, "%s/data/gmdb3", g_filePath);
    (void)sprintf(g_dataFilePath, "%s", TABLE_PATH);
    TsDefulatDbFileClean();
    system("rm -rf ./data/gmdb1");
    system("rm -rf ./data/gmdb2");
    system("rm -rf ./data/gmdb3");
    system("mkdir -p ./data/gmdb1");
    system("mkdir -p ./data/gmdb2");
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建epoll监听线程
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    ret =
        TestGetResultCommand("cat ../common/logicTablePath.txt | tr -d '$\r'", NULL, g_cStoreDir, sizeof(g_cStoreDir));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TsdbOnlineSwitchStorageBasic::TearDown()
{
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_CHECK_LOG_END();
    GmcConnOptionsDestroy(connOptions);
    GmcFreeStmt(stmt);
    GmcDisconnect(conn);
    close_epoll_thread();
    testEnvClean();
    RecoverTsCiCfg();
}

// 001.在目录A启动服务，切换至空目录B
// 预期：切换成功，建表注入数据成功，目录A下无表文件目录B下有对应表文件
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 补充diskLessBoot等于0时切换失败锁库场景，以及重启后各项操作均正常
// euler和仿真环境清共享内存，清理原有DB文件
#if defined RUN_INDEPENDENT
    system("sh $TEST_HOME/tools/stop.sh -f");
#endif
    system("sh $TEST_HOME/tools/stop.sh -ts");
    TsDefulatDbFileClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts recover");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
// euler和仿真环境清共享内存，清理原有DB文件
#if defined RUN_INDEPENDENT
    system("sh $TEST_HOME/tools/stop.sh -f");
#endif
    system("sh $TEST_HOME/tools/stop.sh -ts");
    // 恢复空目录
    system("rm -rf ./data/gmdb1");
    system("mkdir -p ./data/gmdb1");
    InitTsCiCfgModify();
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t dataCount = 1000;
    constexpr int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(
        sqlCmd, "select iD + iD1, time + tiMe1, ip, name, message, iD1, time  from %s", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int64_t ids[dataCount] = {0};
    int64_t checkTimes[dataCount] = {0};
    int64_t ids2[dataCount] = {0};
    int64_t checkTimes2[dataCount] = {0};
    for (int i = 0; i < dataCount; i++) {
        ids[i] = i * 2;
        checkTimes[i] = startTime * 2 + i * 6;
        ids2[i] = i;
        checkTimes2[i] = startTime + i * 3;
    }
    QueryData data1 = {.ids = ids,
        .checkTimes = checkTimes,
        .names = g_name,
        .ips = g_ip,
        .blobs = g_message,
        .ids2 = ids2,
        .times2 = checkTimes2,
        .coefficients = 2,
        .dataCount = dataCount};
    checkQueryData(stmt, sqlCmd, data1);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002.在目录A启动服务，建表A写数据A，切换至空目录B，切换成功后新建表A
// 预期：切换成功后建表A成功，查询表A的数据为空。目录B下新增了表A的持久化文件
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    InitTsCiCfgModify();
    char fdCmd[256] = {0};
    (void)sprintf(fdCmd, "ls -l /proc/`pidof gmserver_ts`/fd |wc -l");
    int32_t fdBefore = 0;
    int32_t fdAfter = 0;
    fdBefore = GetViewFieldResultValue(fdCmd);
    // 在目录/data/gmdb启动建表
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 1, fdAfter);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t dataCount = 1000;
    constexpr int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 2, fdAfter);
    g_dataSize1Before = GetDirSize(g_dataFilePath);
    // 切换目录
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 1, fdAfter);
    g_dataSize2Before = GetDirSize(g_dataFilePath1);
    // 切换目录后新建表A
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 2, fdAfter);
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(
        sqlCmd, "select iD + iD1, time + tiMe1, ip, name, message, iD1, time  from %s", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int64_t ids[dataCount] = {0};
    int64_t checkTimes[dataCount] = {0};
    int64_t ids2[dataCount] = {0};
    int64_t checkTimes2[dataCount] = {0};
    for (int i = 0; i < dataCount; i++) {
        ids[i] = i * 2;
        checkTimes[i] = startTime * 2 + i * 6;
        ids2[i] = i;
        checkTimes2[i] = startTime + i * 3;
    }
    QueryData data1 = {.ids = ids,
        .checkTimes = checkTimes,
        .names = g_name,
        .ips = g_ip,
        .blobs = g_message,
        .ids2 = ids2,
        .times2 = checkTimes2,
        .coefficients = 2,
        .dataCount = dataCount};
    checkQueryData(stmt, sqlCmd, data1);
    g_dataSize1After = GetDirSize(g_dataFilePath);
    g_dataSize2After = GetDirSize(g_dataFilePath1);
    AW_MACRO_ASSERT_EQ_INT(g_dataSize1Before, g_dataSize1After);
    AW_MACRO_ASSERT_NE_INT(g_dataSize2Before, g_dataSize2After);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 1, fdAfter);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003.在目录A启动服务，建表A写数据A，切换至空目录B，切换成功后删除表A
// 预期：切换成功，删除表A失败，目录B不存在表A的持久化文件目录A下存在表A的持久化文件
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    InitTsCiCfgModify();
    // 在目录/data/gmdb启动建表
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t dataCount = 1000;
    constexpr int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_dataSize1Before = GetDirSize(g_dataFilePath);
    // 切换目录
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_dataSize2Before = GetDirSize(g_dataFilePath1);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    g_dataSize1After = GetDirSize(g_dataFilePath);
    g_dataSize2After = GetDirSize(g_dataFilePath1);
    AW_MACRO_ASSERT_EQ_INT(g_dataSize1Before, g_dataSize1After);
    AW_MACRO_ASSERT_EQ_INT(g_dataSize2Before, g_dataSize2After);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNDEFINED_TABLE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004.在目录A启动服务，建表A写入数据A，切换至空目录B，切换成功后建表A注入数据B
// 预期：切换成功，查询时只有数据B，注入数据B后目录A前后文件不变，目录B增加了数据B的持久化文件
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    InitTsCiCfgModify();
    // 初始化环境，设置dataFileDirPath为对应环境地址
    ret = GmcSetPersistPath(TABLE_PATH, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(2);
    // 在目录/data/gmdb启动建表
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t dataCount = 1000;
    constexpr int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_dataSize1Before = GetDirSize(g_dataFilePath);
    // 切换目录
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, NULL);
    if (ret != GMERR_OK) {
        // 若切换失败，还原默认配置
        ret = GmcSetPersistPath(TABLE_PATH, 2);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_dataSize2Before = GetDirSize(g_dataFilePath1);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // 注入数据B
    constexpr int64_t startTime2 = 1288630830;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime2);
    EXPECT_EQ(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(
        sqlCmd, "select iD + iD1, time + tiMe1, ip, name, message, count(message), time  from %s", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    g_dataSize1After = GetDirSize(g_dataFilePath);
    g_dataSize2After = GetDirSize(g_dataFilePath1);
    EXPECT_EQ(g_dataSize1Before, g_dataSize1After);
    EXPECT_NE(g_dataSize2Before, g_dataSize2After);
    int64_t ids[dataCount] = {0};
    int64_t checkTimes[dataCount] = {0};
    int64_t ids2[dataCount] = {0};
    int64_t checkTimes2[dataCount] = {0};
    for (int i = 0; i < 1; i++) {
        ids[i] = i * 2;
        checkTimes[i] = 2577261660 + i * 6;
        ids2[i] = dataCount;
        checkTimes2[i] = 1288630830 + i * 3;
    }
    QueryData data1 = {.ids = ids,
        .checkTimes = checkTimes,
        .names = g_name,
        .ips = g_ip,
        .blobs = g_message,
        .ids2 = ids2,
        .times2 = checkTimes2,
        .coefficients = 2,
        .dataCount = 1};
    checkQueryData(stmt, sqlCmd, data1);

    ret = DropTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    // 还原回默认值
    ret = GmcSetPersistPath(TABLE_PATH, 2);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005.在目录A启动服务，建表A写入数据A，切换至空目录B，切换成功后建表A注入数据A
// 预期：切换成功， 查询时只有一份数据A，注入数据A后目录A前后文件不变，目录B增加了数据A的持久化文件
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    InitTsCiCfgModify();
    // 在目录/data/gmdb启动建表
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t dataCount = 1000;
    constexpr int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_dataSize1Before = GetDirSize(g_dataFilePath);
    // 切换目录
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_dataSize2Before = GetDirSize(g_dataFilePath1);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 注入数据B
    constexpr int64_t startTime2 = 1288630830;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(
        sqlCmd, "select iD + iD1, time + tiMe1, ip, name, message, count(message), time  from %s", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 校验目录前后数据变化
    g_dataSize1After = GetDirSize(g_dataFilePath);
    g_dataSize2After = GetDirSize(g_dataFilePath1);
    AW_MACRO_ASSERT_EQ_INT(g_dataSize1Before, g_dataSize1After);
    AW_MACRO_ASSERT_NE_INT(g_dataSize2Before, g_dataSize2After);
    int64_t ids[dataCount] = {0};
    int64_t checkTimes[dataCount] = {0};
    int64_t ids2[dataCount] = {0};
    int64_t checkTimes2[dataCount] = {0};
    for (int i = 0; i < 1; i++) {
        ids[i] = i * 2;
        checkTimes[i] = 2577261660 + i * 6;
        ids2[i] = dataCount;
        checkTimes2[i] = 1288630830 + i * 3;
    }
    QueryData data1 = {.ids = ids,
        .checkTimes = checkTimes,
        .names = g_name,
        .ips = g_ip,
        .blobs = g_message,
        .ids2 = ids2,
        .times2 = checkTimes2,
        .coefficients = 2,
        .dataCount = 1};
    checkQueryData(stmt, sqlCmd, data1);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006.在目录A启动服务，建表A写入数据A，切换至空目录B，切换成功后删除表A新建表A注入数据B
// 预期：切换成功，删表失败新建表成功，目录A前后文件不变，注入数据A后目录A前后文件不变，目录B增加了数据A的持久化文件
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    InitTsCiCfgModify();
    // 在目录/data/gmdb启动建表
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t dataCount = 1000;
    constexpr int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_dataSize1Before = GetDirSize(g_dataFilePath);
    // 切换目录
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_dataSize2Before = GetDirSize(g_dataFilePath1);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 注入数据B
    constexpr int64_t startTime2 = 1288630830;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(
        sqlCmd, "select iD + iD1, time + tiMe1, ip, name, message, count(message), time  from %s", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_dataSize1After = GetDirSize(g_dataFilePath);
	g_dataSize2After = GetDirSize(g_dataFilePath1);
	AW_MACRO_ASSERT_EQ_INT(g_dataSize1Before, g_dataSize1After);
	AW_MACRO_ASSERT_NE_INT(g_dataSize2Before, g_dataSize2After);
    int64_t ids[dataCount] = {0};
    int64_t checkTimes[dataCount] = {0};
    int64_t ids2[dataCount] = {0};
    int64_t checkTimes2[dataCount] = {0};
    for (int i = 0; i < 1; i++) {
        ids[i] = i * 2;
        checkTimes[i] = 2577261660 + i * 6;
        ids2[i] = dataCount;
        checkTimes2[i] = 1288630830 + i * 3;
    }
    QueryData data1 = {.ids = ids,
        .checkTimes = checkTimes,
        .names = g_name,
        .ips = g_ip,
        .blobs = g_message,
        .ids2 = ids2,
        .times2 = checkTimes2,
        .coefficients = 2,
        .dataCount = 1};
    checkQueryData(stmt, sqlCmd, data1);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNDEFINED_TABLE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007.在目录A启动服务，建表A写入数据A，切换至目录B，且目录B下存在表A数据A的文件
// 预期：切换成功，查询数据时只有一份数据A的数据
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
#if defined RUN_INDEPENDENT
    system("sh $TEST_HOME/tools/stop.sh -f");
#endif
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(2);
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 此处需要设置db启动目录为用例目录，从而进行在用例目录下建表
    ret = GmcSetPersistPath(g_dataFilePath1, 2);
    if (ret != GMERR_OK) {
        // 还原默认配置
        ret = GmcSetPersistPath(TABLE_PATH, 2);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = emptyPathRecover(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TsDefulatDbFileClean();
    conn = NULL;
    stmt = NULL;
    char sqlCmd2[512] = {0};
    (void)memset(sqlCmd2, 0, 512);
    (void)sprintf(
        sqlCmd2, "sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    // 构造目录B下存在表数据
    ret = emptyPathStartup(&conn, &stmt, g_dataFilePath1, sqlCmd2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t dataCount = 1000;
    constexpr int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 还原回默认值
    ret = GmcSetPersistPath(TABLE_PATH, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = emptyPathRecover(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 构造后重置环境
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    conn = NULL;
    stmt = NULL;
    InitTsCiCfgModify();
    // 在目录/data/gmdb启动建表
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    g_dataSize1Before = GetDirSize(g_dataFilePath);
    g_dataSize2Before = GetDirSize(g_dataFilePath1);
    // 切换目录
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, NULL);
    if (ret != GMERR_OK) {
        // 还原默认配置
        ret = GmcSetPersistPath(TABLE_PATH, 2);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);
    g_dataSize1After = GetDirSize(g_dataFilePath);
	g_dataSize2After = GetDirSize(g_dataFilePath1);
	EXPECT_EQ(g_dataSize1Before, g_dataSize1After);
	// 受rebo日志写影响，此处不进行校验，EXPECT_NE(g_dataSize2Before, g_dataSize2After);
    char sqlCmd[512] = {0};
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(
        sqlCmd, "select iD + iD1, time + tiMe1, ip, name, message, count(message), time  from %s", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    int64_t ids[dataCount] = {0};
    int64_t checkTimes[dataCount] = {0};
    int64_t ids2[dataCount] = {0};
    int64_t checkTimes2[dataCount] = {0};
    for (int i = 0; i < 1; i++) {
        ids[i] = i * 2;
        checkTimes[i] = startTime * 2 + i * 6;
        ids2[i] = dataCount;
        checkTimes2[i] = startTime + i * 3;
    }
    QueryData data1 = {.ids = ids,
        .checkTimes = checkTimes,
        .names = g_name,
        .ips = g_ip,
        .blobs = g_message,
        .ids2 = ids2,
        .times2 = checkTimes2,
        .coefficients = 2,
        .dataCount = 1};
    checkQueryData(stmt, sqlCmd, data1);

    ret = DropTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    // 还原回默认值
    ret = GmcSetPersistPath(TABLE_PATH, 2);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008.在目录A启动服务，建表A写入数据A，切换至目录B，且目录B下存在表A数据B的文件
// 预期：切换成功，查询数据时只有一份数据A的数据
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 构造目录B下存在表数据
    ret = emptyPathStartup(&conn, &stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t dataCount = 1000;
    constexpr int64_t startTime2 = 1288630830;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = emptyPathRecover(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 构造后重置环境
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    conn = NULL;
    stmt = NULL;
    InitTsCiCfgModify();
    char fdCmd[256] = {0};
    (void)sprintf(fdCmd, "ls -l /proc/`pidof gmserver_ts`/fd |wc -l");
    int32_t fdBefore = 0;
    int32_t fdAfter = 0;
    fdBefore = GetViewFieldResultValue(fdCmd);
    // 在目录/data/gmdb启动建表
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 1, fdAfter);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 2, fdAfter);
    g_dataSize1Before = GetDirSize(g_dataFilePath);
    g_dataSize2Before = GetDirSize(g_dataFilePath1);
    // 切换目录
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 2, fdAfter);
    g_dataSize1After = GetDirSize(g_dataFilePath);
	g_dataSize2After = GetDirSize(g_dataFilePath1);
	AW_MACRO_ASSERT_EQ_INT(g_dataSize1Before, g_dataSize1After);
    char sqlCmd[512] = {0};
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(
        sqlCmd, "select iD + iD1, time + tiMe1, ip, name, message, id1, time  from %s", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int64_t ids[dataCount] = {0};
    int64_t checkTimes[dataCount] = {0};
    int64_t ids2[dataCount] = {0};
    int64_t checkTimes2[dataCount] = {0};
    for (int i = 0; i < dataCount; i++) {
        ids[i] = i * 2;
        checkTimes[i] = startTime2 * 2 + i * 6;
        ids2[i] = i;
        checkTimes2[i] = startTime2 + i * 3;
    }
    QueryData data1 = {.ids = ids,
        .checkTimes = checkTimes,
        .names = g_name,
        .ips = g_ip,
        .blobs = g_message,
        .ids2 = ids2,
        .times2 = checkTimes2,
        .coefficients = 2,
        .dataCount = dataCount};
    checkQueryData(stmt, sqlCmd, data1);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 1, fdAfter);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 009.在目录A启动服务，建表A写入数据A，切换至目录B，且目录B下存在同名表A但是time_col配置不一样
// 预期：切换成功，查询时数据为空
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 构造目录B下存在表数据
    ret = emptyPathStartup(&conn, &stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(sqlCmd,
            "create table %s(id integer, time integer, name char(64), ip inet, message blob(160),"
            " description text, id1 integer, time1 integer)with (time_col = 'time1', interval = '%s', "
            " disk_limit = '1024 MB', compression = 'fast(rapidlz)');",
            g_tableName, g_intervalHour);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = emptyPathRecover(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 构造后重置环境
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    conn = NULL;
    stmt = NULL;
    InitTsCiCfgModify();
    // 在目录/data/gmdb启动建表
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t dataCount = 1000;
    constexpr int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 切换目录
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(
        sqlCmd, "select iD + iD1, time + tiMe1, ip, name, message, id1, time  from %s", g_tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int64_t ids[dataCount] = {0};
    int64_t checkTimes[dataCount] = {0};
    int64_t ids2[dataCount] = {0};
    int64_t checkTimes2[dataCount] = {0};
    for (int i = 0; i < dataCount; i++) {
        ids[i] = i * 2;
        checkTimes[i] = startTime * 2 + i * 6;
        ids2[i] = i;
        checkTimes2[i] = startTime + i * 3;
    }
    QueryData data1 = {.ids = ids,
        .checkTimes = checkTimes,
        .names = g_name,
        .ips = g_ip,
        .blobs = g_message,
        .ids2 = ids2,
        .times2 = checkTimes2,
        .coefficients = 2,
        .dataCount = 0};
    checkQueryData(stmt, sqlCmd, data1);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010.在目录A启动服务，建表A写入数据A，切换至目录B，且目录B下存在同名表A但是interval配置不一样
// 预期：切换成功，查询时数据为空
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 构造目录B下存在表数据
    ret = emptyPathStartup(&conn, &stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(sqlCmd,
            "create table %s(id integer, time integer, name char(64), ip inet, message blob(160),"
            " description text, id1 integer, time1 integer)with (time_col = 'time', interval = '%s', "
            " disk_limit = '1024 MB', compression = 'fast(rapidlz)');",
            g_tableName, g_intervalDay);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = emptyPathRecover(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 构造后重置环境
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    conn = NULL;
    stmt = NULL;
    InitTsCiCfgModify();
    // 在目录/data/gmdb启动建表
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t dataCount = 1000;
    constexpr int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 切换目录
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(
        sqlCmd, "select iD + iD1, time + tiMe1, ip, name, message, id1, time  from %s", g_tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int64_t ids[dataCount] = {0};
    int64_t checkTimes[dataCount] = {0};
    int64_t ids2[dataCount] = {0};
    int64_t checkTimes2[dataCount] = {0};
    for (int i = 0; i < dataCount; i++) {
        ids[i] = i * 2;
        checkTimes[i] = startTime * 2 + i * 6;
        ids2[i] = i;
        checkTimes2[i] = startTime + i * 3;
    }
    QueryData data1 = {.ids = ids,
        .checkTimes = checkTimes,
        .names = g_name,
        .ips = g_ip,
        .blobs = g_message,
        .ids2 = ids2,
        .times2 = checkTimes2,
        .coefficients = 2,
        .dataCount = 0};
    checkQueryData(stmt, sqlCmd, data1);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 011.在目录A启动服务，建表A写入数据A，切换至目录B，且目录B下存在同名表A但是ttl配置不一样
// 预期：切换成功，查询时数据为空
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t fdServerBeforeOffset1 = 0;
    int32_t fdServerBeforeOffset2 = 0;
    char g_fdClient[512] = "ls -l /proc/`pidof tsdb_storage_instance_switchover_basic`/fd |wc -l";
    // 构造目录B下存在表数据
    ret = emptyPathStartup(&conn, &stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(sqlCmd,
            "create table %s(id integer, time integer, name char(64), ip inet, message blob(160),"
            " description text, id1 integer, time1 integer)with (time_col = 'time', interval = '%s', "
            " disk_limit = '1024 MB', compression = 'fast(rapidlz)', TTL = '1 DAY');",
            g_tableName, g_intervalHour);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 构造目录A下存在内存表C
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(sqlCmd, "create table %s(id integer, time integer,  name char(64), ip inet, message blob(160),  "
        " description text, id1 integer, time1 integer, INDEX idx1(time1))"
        " with (enGine = 'mEmOry', max_size =10000, time_col = 'time', interval = '1 hour');",
        g_tableName3); 
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t count = 1000;
    ret = insertOutOfOrderData(stmt, g_tableName3, count);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(stmt, g_tableName3);
    AW_MACRO_ASSERT_EQ_INT(count, ret);
    ret = emptyPathRecover(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 构造后重置环境
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    conn = NULL;
    stmt = NULL;
    InitTsCiCfgModify();
    char fdCmd[256] = {0};
    (void)sprintf(fdCmd, "ls -l /proc/`pidof gmserver_ts`/fd |wc -l");
    int32_t fdBefore = 0;
    int32_t fdAfter = 0;
    fdServerBeforeOffset1 = GetViewFieldResultValue(g_fdClient);
    fdBefore = GetViewFieldResultValue(fdCmd);
    // 在目录/data/gmdb启动建表
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 1, fdAfter);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 2, fdAfter);
    constexpr int64_t dataCount = 1000;
    constexpr int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 切换目录
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 切换目录存在同名表，fd+1???
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 2, fdAfter);
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(
        sqlCmd, "select iD + iD1, time + tiMe1, ip, name, message, id1, time  from %s", g_tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int64_t ids[dataCount] = {0};
    int64_t checkTimes[dataCount] = {0};
    int64_t ids2[dataCount] = {0};
    int64_t checkTimes2[dataCount] = {0};
    for (int i = 0; i < dataCount; i++) {
        ids[i] = i * 2;
        checkTimes[i] = startTime * 2 + i * 6;
        ids2[i] = i;
        checkTimes2[i] = startTime + i * 3;
    }
    QueryData data1 = {.ids = ids,
        .checkTimes = checkTimes,
        .names = g_name,
        .ips = g_ip,
        .blobs = g_message,
        .ids2 = ids2,
        .times2 = checkTimes2,
        .coefficients = 2,
        .dataCount = 0};
    checkQueryData(stmt, sqlCmd, data1);
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(sqlCmd, "select * from %s", g_tableName3);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 2, fdAfter);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    fdServerBeforeOffset2 = GetViewFieldResultValue(g_fdClient);
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore - fdServerBeforeOffset1 + 2, fdAfter - fdServerBeforeOffset2);
    ret = DropTable(stmt, g_tableName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore - fdServerBeforeOffset1 + 2, fdAfter - fdServerBeforeOffset2);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore - fdServerBeforeOffset1 + 1, fdAfter - fdServerBeforeOffset2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 012.在目录A启动服务，建表A写入数据A，切换至目录B，且目录B下存在同名表A但是disk_limit配置不一样
// 预期：切换成功，查询时数据为空
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t fdServerBeforeOffset1 = 0;
    int32_t fdServerBeforeOffset2 = 0;
    char g_fdClient[512] = "ls -l /proc/`pidof tsdb_storage_instance_switchover_basic`/fd |wc -l";
    // 构造目录B下存在表数据
    ret = emptyPathStartup(&conn, &stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(sqlCmd,
            "create table %s(id integer, time integer, name char(64), ip inet, message blob(160),"
            " description text, id1 integer, time1 integer)with (time_col = 'time', interval = '%s', "
            " disk_limit = '2 GB', compression = 'fast(rapidlz)');",
            g_tableName, g_intervalHour);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = emptyPathRecover(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 构造后重置环境
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    conn = NULL;
    stmt = NULL;
    InitTsCiCfgModify();
    char fdCmd[256] = {0};
    (void)sprintf(fdCmd, "ls -l /proc/`pidof gmserver_ts`/fd |wc -l");
    int32_t fdBefore = 0;
    int32_t fdAfter = 0;
    fdServerBeforeOffset1 = GetViewFieldResultValue(g_fdClient);
    fdBefore = GetViewFieldResultValue(fdCmd);
    // 在目录/data/gmdb启动建表
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 1, fdAfter);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 2, fdAfter);
    constexpr int64_t dataCount = 1000;
    constexpr int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建内存表C
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(sqlCmd, "create table %s(id integer, time integer,  name char(64), ip inet, message blob(160),  "
        " description text, id1 integer, time1 integer, INDEX idx1(time1))"
        " with (enGine = 'mEmOry', max_size =10000, time_col = 'time', interval = '1 hour');",
        g_tableName3); 
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 2, fdAfter);
    int64_t count = 1000;
    ret = insertOutOfOrderData(stmt, g_tableName3, count);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(stmt, g_tableName3);
    AW_MACRO_ASSERT_EQ_INT(count, ret);

    // 切换目录
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 2, fdAfter);
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(
        sqlCmd, "select iD + iD1, time + tiMe1, ip, name, message, id1, time  from %s", g_tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int64_t ids[dataCount] = {0};
    int64_t checkTimes[dataCount] = {0};
    int64_t ids2[dataCount] = {0};
    int64_t checkTimes2[dataCount] = {0};
    for (int i = 0; i < dataCount; i++) {
        ids[i] = i * 2;
        checkTimes[i] = startTime * 2 + i * 6;
        ids2[i] = i;
        checkTimes2[i] = startTime + i * 3;
    }
    QueryData data1 = {.ids = ids,
        .checkTimes = checkTimes,
        .names = g_name,
        .ips = g_ip,
        .blobs = g_message,
        .ids2 = ids2,
        .times2 = checkTimes2,
        .coefficients = 2,
        .dataCount = 0};
    checkQueryData(stmt, sqlCmd, data1);

    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(sqlCmd, "select * from %s", g_tableName3);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 2, fdAfter);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    fdServerBeforeOffset2 = GetViewFieldResultValue(g_fdClient);
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore - fdServerBeforeOffset1 + 2, fdAfter - fdServerBeforeOffset2);
    ret = DropTable(stmt, g_tableName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore - fdServerBeforeOffset1 + 2, fdAfter - fdServerBeforeOffset2);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore - fdServerBeforeOffset1 + 1, fdAfter - fdServerBeforeOffset2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 013.在目录A启动服务，建表A写入数据A，切换至目录B，且目录B下存在表B
// 预期：切换成功，查询时数据为空
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 构造目录B下存在表数据
    ret = emptyPathStartup(&conn, &stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(sqlCmd,
            "create table %s(id integer, time integer, name char(64), ip inet, message blob(160),"
            " description text, id1 integer, time1 integer)with (time_col = 'time', interval = '%s', "
            " disk_limit = '2 GB', compression = 'fast(rapidlz)');",
            g_tableName2, g_intervalHour);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t dataCount = 1000;
    constexpr int64_t startTime2 = 1288630830;
    ret = rowInsertData(stmt, g_tableName2, dataCount, startTime2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 构造目录A下存在内存表C
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(sqlCmd, "create table %s(id integer, time integer,  name char(64), ip inet, message blob(160),  "
        " description text, id1 integer, time1 integer, INDEX idx1(time1))"
        " with (enGine = 'mEmOry', max_size =10000, time_col = 'time', interval = '1 hour');",
        g_tableName3); 
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t count = 1000;
    ret = insertOutOfOrderData(stmt, g_tableName3, count);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(stmt, g_tableName3);
    AW_MACRO_ASSERT_EQ_INT(count, ret);
    ret = emptyPathRecover(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 构造后重置环境
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    conn = NULL;
    stmt = NULL;
    InitTsCiCfgModify();
    char fdCmd[256] = {0};
    (void)sprintf(fdCmd, "ls -l /proc/`pidof gmserver_ts`/fd |wc -l");
    int32_t fdBefore = 0;
    int32_t fdAfter = 0;
    fdBefore = GetViewFieldResultValue(fdCmd);
    // 在目录/data/gmdb启动建表
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 1, fdAfter);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 2, fdAfter);
    constexpr int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 切换目录
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 2, fdAfter);
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(
        sqlCmd, "select iD + iD1, time + tiMe1, ip, name, message, id1, time  from %s", g_tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(
        sqlCmd, "select iD + iD1, time + tiMe1, ip, name, message, id1, time  from %s", g_tableName2);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t ids[dataCount] = {0};
    int64_t checkTimes[dataCount] = {0};
    int64_t ids2[dataCount] = {0};
    int64_t checkTimes2[dataCount] = {0};
    for (int i = 0; i < dataCount; i++) {
        ids[i] = i * 2;
        checkTimes[i] = startTime2 * 2 + i * 6;
        ids2[i] = i;
        checkTimes2[i] = startTime2 + i * 3;
    }
    QueryData data1 = {.ids = ids,
        .checkTimes = checkTimes,
        .names = g_name,
        .ips = g_ip,
        .blobs = g_message,
        .ids2 = ids2,
        .times2 = checkTimes2,
        .coefficients = 2,
        .dataCount = dataCount};
    checkQueryData(stmt, sqlCmd, data1);
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(sqlCmd, "select * from %s", g_tableName3);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 2, fdAfter);
    ret = DropTable(stmt, g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 1, fdAfter);
    ret = DropTable(stmt, g_tableName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    // 内存表不占fd
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 1, fdAfter);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNDEFINED_TABLE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 014.在目录A启动服务，建表A写入数据A，切换至目录B，且目录B下存在197张表
// 预期：切换成功，查询时数据为空
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(
        sqlCmd, "sh $TEST_HOME/tools/modifyCfg.sh -ts \"dbFilesMaxCnt=200\" \"spaceMaxNum=200\" ");
    // 构造目录B下存在表197张表
    ret = emptyPathStartup(&conn, &stmt, g_dataFilePath1, sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 建表达上限1021(系统会占3个，目前只能建到1017张表，因为bufferPoolSize设置的不够大，6月后会调整一下)
    // 建1017张耗时太长，此处修改配置项使得建满197张表后就达到限制
    uint32_t cmdLen = 0;
    for (int i = 0; i < 197; i++) {
        (void)memset(sqlCmd, 0, 512);
        (void)sprintf(sqlCmd,
            "create table testdb%ld(id integer, time integer, name char(64), ip inet, message blob(160),"
            " description text, id1 integer, time1 integer)with (time_col = 'time', interval = '1 hour', "
            " disk_limit = '1024 MB', compression = 'fast(rapidlz)');",
            i);
        cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
        if (i == 198){
            AW_MACRO_EXPECT_EQ_INT(GMERR_INSUFFICIENT_RESOURCES, ret);
        } else {
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }
    constexpr int64_t dataCount = 1000;
    constexpr int64_t startTime2 = 1288630830;
    ret = rowInsertData(stmt, g_tableName2, dataCount, startTime2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = emptyPathRecover(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 构造后重置环境
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    conn = NULL;
    stmt = NULL;
// euler和仿真环境清共享内存，清理原有DB文件
#if defined RUN_INDEPENDENT
    system("sh $TEST_HOME/tools/stop.sh -f");
#endif
    system("sh $TEST_HOME/tools/stop.sh -ts");
    TsDefulatDbFileClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts recover");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\" \"dbFilesMaxCnt=200\" \"spaceMaxNum=200\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(2);
    // 在目录/data/gmdb启动
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 切换目录
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 196; i++) {
        (void)memset(sqlCmd, 0, 512);
        (void)sprintf(sqlCmd,
            "select * from testdb%ld;", i);
        cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(
        sqlCmd, "select iD + iD1, time + tiMe1, ip, name, message, id1, time  from %s", g_tableName2);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t ids[dataCount] = {0};
    int64_t checkTimes[dataCount] = {0};
    int64_t ids2[dataCount] = {0};
    int64_t checkTimes2[dataCount] = {0};
    for (int i = 0; i < dataCount; i++) {
        ids[i] = i * 2;
        checkTimes[i] = startTime2 * 2 + i * 6;
        ids2[i] = i;
        checkTimes2[i] = startTime2 + i * 3;
    }
    QueryData data1 = {.ids = ids,
        .checkTimes = checkTimes,
        .names = g_name,
        .ips = g_ip,
        .blobs = g_message,
        .ids2 = ids2,
        .times2 = checkTimes2,
        .coefficients = 2,
        .dataCount = dataCount};
    checkQueryData(stmt, sqlCmd, data1);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNDEFINED_TABLE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 015.在目录A启动服务，建197张表，切换至空目录B
// 预期：切换成功，查询时数据为空
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(
        sqlCmd, "sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\" \"dbFilesMaxCnt=200\" \"spaceMaxNum=200\"");
    // 构造目录A下存在表197张表
    ret = emptyPathStartup(&conn, &stmt, NULL, sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 建表达上限1021(系统会占3个，目前只能建到1017张表，因为bufferPoolSize设置的不够大，6月后会调整一下)
    // 建1017张耗时太长，此处修改配置项使得建满197张表后就达到限制
    uint32_t cmdLen = 0;
    for (int i = 0; i < 197; i++) {
        (void)memset(sqlCmd, 0, 512);
        (void)sprintf(sqlCmd,
            "create table testdb%ld(id integer, time integer, name char(64), ip inet, message blob(160),"
            " description text, id1 integer, time1 integer)with (time_col = 'time', interval = '1 hour', "
            " disk_limit = '1024 MB', compression = 'fast(rapidlz)');",
            i);
        cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
        if (i == 198){
            AW_MACRO_EXPECT_EQ_INT(GMERR_INSUFFICIENT_RESOURCES, ret);
        } else {
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }
    constexpr int64_t dataCount = 1000;
    constexpr int64_t startTime2 = 1288630830;
    ret = rowInsertData(stmt, g_tableName2, dataCount, startTime2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 切换目录
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 196; i++) {
        (void)memset(sqlCmd, 0, 512);
        (void)sprintf(sqlCmd,
            "select * from testdb%ld;", i);
        cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
        AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    }
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(
        sqlCmd, "select iD + iD1, time + tiMe1, ip, name, message, id1, time  from %s", g_tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t ids[dataCount] = {0};
    int64_t checkTimes[dataCount] = {0};
    int64_t ids2[dataCount] = {0};
    int64_t checkTimes2[dataCount] = {0};
    for (int i = 0; i < dataCount; i++) {
        ids[i] = i * 2;
        checkTimes[i] = startTime * 2 + i * 6;
        ids2[i] = i;
        checkTimes2[i] = startTime + i * 3;
    }
    QueryData data1 = {.ids = ids,
        .checkTimes = checkTimes,
        .names = g_name,
        .ips = g_ip,
        .blobs = g_message,
        .ids2 = ids2,
        .times2 = checkTimes2,
        .coefficients = 2,
        .dataCount = dataCount};
    checkQueryData(stmt, sqlCmd, data1);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNDEFINED_TABLE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 016.在目录A启动服务，建197张表，切换至目录B，且目录B下存在197张不同表
// 预期：切换成功，查询时数据为空
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(
        sqlCmd, "sh $TEST_HOME/tools/modifyCfg.sh -ts \"dbFilesMaxCnt=200\" \"spaceMaxNum=200\"");
    // 构造目录B下存在表197张表
    ret = emptyPathStartup(&conn, &stmt, g_dataFilePath1, sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 建表达上限1021(系统会占3个，目前只能建到1017张表，因为bufferPoolSize设置的不够大，6月后会调整一下)
    // 建1017张耗时太长，此处修改配置项使得建满197张表后就达到限制
    uint32_t cmdLen = 0;
    for (int i = 0; i < 197; i++) {
        (void)memset(sqlCmd, 0, 512);
        (void)sprintf(sqlCmd,
            "create table testdb%ld(id integer, time integer, name char(64), ip inet, message blob(160),"
            " description text, id1 integer, time1 integer)with (time_col = 'time', interval = '1 hour', "
            " disk_limit = '1024 MB', compression = 'fast(rapidlz)');",
            i);
        cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
        if (i == 198){
            AW_MACRO_EXPECT_EQ_INT(GMERR_INSUFFICIENT_RESOURCES, ret);
        } else {
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }
    constexpr int64_t dataCount = 1000;
    constexpr int64_t startTime2 = 1288630830;
    ret = rowInsertData(stmt, g_tableName2, dataCount, startTime2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = emptyPathRecover(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 构造后重置环境
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    conn = NULL;
    stmt = NULL;
// euler和仿真环境清共享内存，清理原有DB文件
#if defined RUN_INDEPENDENT
    system("sh $TEST_HOME/tools/stop.sh -f");
#endif
    system("sh $TEST_HOME/tools/stop.sh -ts");
    TsDefulatDbFileClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts recover");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\" \"dbFilesMaxCnt=200\" \"spaceMaxNum=200\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(2);
    // 在目录/data/gmdb启动
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 197; i++) {
        (void)memset(sqlCmd, 0, 512);
        (void)sprintf(sqlCmd,
            "create table testdb_TEST%ld(id integer, time integer, name char(64), ip inet, message blob(160),"
            " description text, id1 integer, time1 integer)with (time_col = 'time', interval = '%s', "
            " disk_limit = '1024 MB', compression = 'fast(rapidlz)');",
            i, g_intervalHour);
        cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
        if (i == 198){
            AW_MACRO_EXPECT_EQ_INT(GMERR_INSUFFICIENT_RESOURCES, ret);
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    constexpr int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName3, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 切换目录
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 196; i++) {
        (void)memset(sqlCmd, 0, 512);
        (void)sprintf(sqlCmd,
            "select * from testdb%ld;", i);
        cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    for (int i = 0; i < 196; i++) {
        (void)memset(sqlCmd, 0, 512);
        (void)sprintf(sqlCmd,
            "select * from testdb_TEST%ld;", i);
        cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
        AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    }
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(
        sqlCmd, "select iD + iD1, time + tiMe1, ip, name, message, id1, time  from %s", g_tableName2);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t ids[dataCount] = {0};
    int64_t checkTimes[dataCount] = {0};
    int64_t ids2[dataCount] = {0};
    int64_t checkTimes2[dataCount] = {0};
    for (int i = 0; i < dataCount; i++) {
        ids[i] = i * 2;
        checkTimes[i] = startTime2 * 2 + i * 6;
        ids2[i] = i;
        checkTimes2[i] = startTime2 + i * 3;
    }
    QueryData data1 = {.ids = ids,
        .checkTimes = checkTimes,
        .names = g_name,
        .ips = g_ip,
        .blobs = g_message,
        .ids2 = ids2,
        .times2 = checkTimes2,
        .coefficients = 2,
        .dataCount = dataCount};
    checkQueryData(stmt, sqlCmd, data1);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNDEFINED_TABLE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 017.在目录A启动服务，建逻辑表A和内存表B，切换至目录B，且目录B下没有任何文件
// 预期：切换成功，查询时数据为空
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    InitTsCiCfgModify();
    // 在目录/data/gmdb启动建表
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t dataCount = 1000;
    constexpr int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)sprintf(sqlCmd, "create table %s(id integer, time integer, name char(64), ip inet,"
        " description text, message blob(160),  id1 integer, time1 integer, " 
        "INDEX idx1(time))with (enGine = 'mEmOry', max_size =1000000, time_col = 'time1', interval = '1 hour');",
        g_tableName2);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 切换目录
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(
        sqlCmd, "select * from %s", g_tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(
        sqlCmd, "select * from %s", g_tableName2);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNDEFINED_TABLE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 018.在目录A启动服务，建逻辑表A和内存表B，切换至目录B，且目录B下存在和目录A下同名的逻辑表B
// 预期：切换成功，查询时只有逻辑表B
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    (void)memset(sqlCmd, 0, 512);
    ret = emptyPathStartup(&conn, &stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = CreateTable(stmt, g_tableName2, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t dataCount = 50000;
    constexpr int64_t startTime = 1262275200;
    constexpr int64_t startTime2 = 1288630830;
    ret = rowInsertData(stmt, g_tableName2, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = emptyPathRecover(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 构造后重置环境
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    conn = NULL;
    stmt = NULL;
    InitTsCiCfgModify();
    // 在目录/data/gmdb启动
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(sqlCmd, "create table %s(id integer, time integer, name char(64), ip inet,"
        " description text, message blob(160), time1 integer, " 
        "INDEX idx1(time))with (enGine = 'mEmOry', max_size =1000000, time_col = 'time1', interval = '1 hour');",
        g_tableName2);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 切换目录
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(
        sqlCmd, "select iD + iD1, time + tiMe1, ip, name, message, id1, time  from %s", g_tableName2);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t ids[dataCount] = {0};
    int64_t checkTimes[dataCount] = {0};
    int64_t ids2[dataCount] = {0};
    int64_t checkTimes2[dataCount] = {0};
    for (int i = 0; i < dataCount; i++) {
        ids[i] = i * 2;
        checkTimes[i] = startTime * 2 + i * 6;
        ids2[i] = i;
        checkTimes2[i] = startTime + i * 3;
    }
    QueryData data1 = {.ids = ids,
        .checkTimes = checkTimes,
        .names = g_name,
        .ips = g_ip,
        .blobs = g_message,
        .ids2 = ids2,
        .times2 = checkTimes2,
        .coefficients = 2,
        .dataCount = dataCount};
    checkQueryData(stmt, sqlCmd, data1);

    ret = DropTable(stmt, g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 019.在目录A启动服务，建逻辑表A，切换至空目录B。切换过程中保持对A表注入大批量数据
// 预期：切换成功，注入数据失败
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    (void)memset(sqlCmd, 0, 512);
    constexpr int64_t dataCount = 1000000;
    constexpr int64_t startTime = 1262275200;
    constexpr int64_t startTime2 = 1288630830;    
    InitTsCiCfgModify();
    // 在目录/data/gmdb启动
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    ret = TestTsGmcConnect(&conn1, &stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_concurrentrStatus = 0;
    pthread_t tid;
    InsertQueryDataType constructDataType_1 = {stmt1, g_tableName, dataCount, startTime2};
    pthread_create(&tid, NULL, InsertDataToTable, &constructDataType_1);
    // 切换目录
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(
        sqlCmd, "select iD + iD1, time + tiMe1, ip, name, message, id1, time  from %s", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    pthread_join(tid, NULL);
    AW_MACRO_ASSERT_EQ_INT(g_concurrentrStatus, GMERR_LOCK_NOT_AVAILABLE);
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 020.在目录A启动服务，建逻辑表A，切换至目录B,目录B下有同名同列表A。切换过程中保持对A表注入数据
// 预期：切换成功，注入数据失败
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    (void)memset(sqlCmd, 0, 512);
    ret = emptyPathStartup(&conn, &stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t dataCount = 1000000;
    constexpr int64_t startTime = 1262275200;
    constexpr int64_t startTime2 = 1288630830;
    ret = emptyPathRecover(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 构造后重置环境
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    conn = NULL;
    stmt = NULL;
    InitTsCiCfgModify();
    // 在目录/data/gmdb启动
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    ret = TestTsGmcConnect(&conn1, &stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_concurrentrStatus = 0;
    pthread_t tid;
    InsertQueryDataType constructDataType_1 = {stmt1, g_tableName, dataCount, startTime2};
    pthread_create(&tid, NULL, InsertDataToTable, &constructDataType_1);
    // 切换目录
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    pthread_join(tid, NULL);
    AW_MACRO_ASSERT_EQ_INT(g_concurrentrStatus, GMERR_LOCK_NOT_AVAILABLE);
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(
        sqlCmd, "select iD + iD1, time + tiMe1, ip, name, message, id1, time  from %s", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 021.在目录A启动服务，建逻辑表A，切换至目录B,目录B下有同名不同列表A（构造注入数据会报错）。切换过程中保持对A表注入数据
// 预期：切换成功，注入数据失败
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    (void)memset(sqlCmd, 0, 512);
    ret = emptyPathStartup(&conn, &stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = CreateTable(stmt, g_tableName2, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)sprintf(sqlCmd,
        "create table %s(id inet, time integer, name char(64), ip inet, message blob(160),"
        " description text, id1 integer, time1 integer)with (time_col = 'time', interval = '%s', "
        " disk_limit = '1024 mb', compression = 'fast(rapidlz)');",
        g_tableName, g_intervalHour);

    constexpr int64_t dataCount = 1000000;
    constexpr int64_t startTime = 1262275200;
    constexpr int64_t startTime2 = 1288630830;
    ret = emptyPathRecover(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 构造后重置环境
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    conn = NULL;
    stmt = NULL;
    InitTsCiCfgModify();
    // 在目录/data/gmdb启动
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    ret = TestTsGmcConnect(&conn1, &stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_concurrentrStatus = 0;
    pthread_t tid;
    InsertQueryDataType constructDataType_1 = {stmt1, g_tableName, dataCount, startTime2};
    pthread_create(&tid, NULL, InsertDataToTable, &constructDataType_1);
    // 切换目录
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    pthread_join(tid, NULL);
    AW_MACRO_ASSERT_EQ_INT(g_concurrentrStatus, GMERR_LOCK_NOT_AVAILABLE);
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(
        sqlCmd, "select iD + iD1, time + tiMe1, ip, name, message, id1, time  from %s", g_tableName2);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 适配arm32环境超时
// 建立时序服务端链接
int CreateTsConnect(GmcConnT **conn_ts, GmcStmtT **stmt_ts)
{
    int ret = 0;
    YangConnOptionT tsConnOptions = {0};
    tsConnOptions.isCsMode = true;
    tsConnOptions.msgReadTimeout = 180 * 1000;
    tsConnOptions.serverLocator = g_connServerTsdb;
    ret = TestYangGmcConnect(conn_ts, stmt_ts, 0, &tsConnOptions);
    return ret;
}

// 022.在目录A启动服务，建逻辑表A，切换至空目录B。切换过程中保持对A表进行查询
// 预期：切换失败，切换接口被阻塞
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    (void)memset(sqlCmd, 0, 512);
    // 2025-03-27 swap接口等待时间由3秒改为10秒，此处增加数据量避免切换前查询完成
    constexpr int64_t dataCount = 500000;
    constexpr int64_t startTime = 1262275200;
    constexpr int64_t startTime2 = 1288630830;
    InitTsCiCfgModify();
    // 在目录/data/gmdb启动
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    ret = CreateTsConnect(&conn1, &stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    pthread_t tid;
    InsertQueryDataType constructDataType_1 = {stmt1, g_tableName, dataCount, startTime2};
    pthread_create(&tid, NULL, QueryDataToTable, &constructDataType_1);
    // 切换目录
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_LOCK_NOT_AVAILABLE, ret);
    pthread_join(tid, NULL);
    // 校验query ret返回值
    AW_MACRO_ASSERT_EQ_INT(g_concurrentrStatus, GMERR_OK);
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 023.在目录A启动服务，建逻辑表A，切换至空目录B。切换过程中保持对A表进行copy  to
// 预期：切换失败，切换接口被阻塞，copy to成功
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf ./data1.csv");
    char sqlCmd[512] = {0};
    (void)memset(sqlCmd, 0, 512);
    // 2025-03-27 swap接口等待时间由3秒改为10秒，此处增加数据量避免切换前copy to执行完成
    constexpr int64_t dataCount = 3000000;
    constexpr int64_t startTime = 1262275200;
    constexpr int64_t startTime2 = 1288630830;
    InitTsCiCfgModify();
    // 在目录/data/gmdb启动
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    ret = CreateTsConnect(&conn1, &stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_concurrentrStatus = 0;
    pthread_t tid;
    InsertQueryDataType constructDataType_1 = {stmt1, g_tableName, dataCount, startTime2};
    pthread_create(&tid, NULL, CopyToDataToTable, &constructDataType_1);
    // 切换目录
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_LOCK_NOT_AVAILABLE, ret);
    pthread_join(tid, NULL);
    AW_MACRO_ASSERT_EQ_INT(g_concurrentrStatus, GMERR_OK);
    // 校验copy to 返回值
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 024、在目录A启动服务，建逻辑表A,B，切换至空目录B。切换过程中保持对A表B表进行insert  into
// 预期：切换成功，insert  into 失败
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    (void)memset(sqlCmd, 0, 512);
    constexpr int64_t dataCount = 1000000;
    constexpr int64_t startTime = 1262275200;
    constexpr int64_t startTime2 = 1288630830;
    InitTsCiCfgModify();
    // 在目录/data/gmdb启动
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName2, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    ret = TestTsGmcConnect(&conn1, &stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_concurrentrStatus = 0;
    pthread_t tid;
    InsertQueryDataType constructDataType_1 = {stmt1, g_tableName, dataCount, startTime2};
    pthread_create(&tid, NULL, InsertIntoDataToTable, &constructDataType_1);
    // 切换目录
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    pthread_join(tid, NULL);
    AW_MACRO_ASSERT_EQ_INT(g_concurrentrStatus, GMERR_LOCK_NOT_AVAILABLE);
    // 校验insert into 返回值
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 025、在目录A启动服务，循环建立100张表，此时切换至空目录B
// 预期：切换成功,建表直接失败
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    (void)memset(sqlCmd, 0, 512);
    InitTsCiCfgModify();
    // 在目录/data/gmdb启动
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    ret = TestTsGmcConnect(&conn1, &stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_concurrentrStatus = 0;
    pthread_t tid;
    InsertQueryDataType constructDataType_1 = {stmt1, g_tableName, 0, 0};
    pthread_create(&tid, NULL, ConcurrentCreateTable, &constructDataType_1);
    // 切换目录
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    pthread_join(tid, NULL);
    AW_MACRO_ASSERT_EQ_INT(g_concurrentrStatus, GMERR_LOCK_NOT_AVAILABLE);
    // 校验insert into 返回值
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 切换失败，服务端core
// 026、在目录A启动服务，循环建立100张表后，删除这100张表，此时切换至空目录B
// 预期：切换成功，删表失败
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    
    InitTsCiCfgModify();
    // 在目录/data/gmdb启动
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 100; i++) {
        (void)memset(sqlCmd, 0, 512);
        (void)sprintf(sqlCmd,
            "create table testdb%ld(id integer, time integer, name char(64), ip inet, message blob(160),"
            " description text, id1 integer, time1 integer)with (time_col = 'time', interval = '%s', "
            " disk_limit = '1024 MB', compression = 'fast(rapidlz)');",
            i, g_intervalHour);
        ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    ret = TestTsGmcConnect(&conn1, &stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_concurrentrStatus = 0;
    pthread_t tid;
    InsertQueryDataType constructDataType_1 = {stmt1, g_tableName, 0, 0};
    pthread_create(&tid, NULL, ConcurrentDeleteTable, &constructDataType_1);
    // 切换目录
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    pthread_join(tid, NULL);
    AW_MACRO_ASSERT_EQ_INT(g_concurrentrStatus, GMERR_OK);
    // 校验insert into 返回值
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 027.在目录A启动服务，查询大结果集，切换至空目录B，且不切换临时文件位置
// 预期：切换失败，临时文件生成，正常查询
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(
        sqlCmd, "sh $TEST_HOME/tools/modifyCfg.sh -ts \"forceUseTempFileForQueryResult=1\" \"diskLessBoot=1\"");
    ret = emptyPathStartup(&conn, &stmt, NULL, sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t dataCount = 1000000;
    constexpr int64_t startTime2 = 1288630830;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    ret = TestTsGmcConnectTime(&conn1, &stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    pthread_t tid;
    InsertQueryDataType constructDataType_1 = {stmt1, g_tableName, dataCount, 0};
    pthread_create(&tid, NULL, QueryDataToTable, &constructDataType_1);

    // 切换目录,长事务阻塞路径切换
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_LOCK_NOT_AVAILABLE, ret);
    pthread_join(tid, NULL);
    
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_LOCK_NOT_AVAILABLE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 028.在目录A启动服务，查询大结果集，A，切换至空目录B，且切换临时文件位置B至空目录
// 预期：切换成功，临时文件生成，正常查询
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(
        sqlCmd, "sh $TEST_HOME/tools/modifyCfg.sh -ts \"forceUseTempFileForQueryResult=1\" \"diskLessBoot=1\"");
    ret = emptyPathStartup(&conn, &stmt, NULL, sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t dataCount = 1000000;
    constexpr int64_t startTime2 = 1288630830;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    ret = TestTsGmcConnectTime(&conn1, &stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    pthread_t tid;
    InsertQueryDataType constructDataType_1 = {stmt1, g_tableName, dataCount, 0};
    pthread_create(&tid, NULL, QueryDataToTable, &constructDataType_1);
    // 切换目录
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, g_dataFilePath2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_LOCK_NOT_AVAILABLE, ret);
    pthread_join(tid, NULL);
    
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_LOCK_NOT_AVAILABLE);
    AW_FUN_Log(LOG_STEP, "test end.");
}
