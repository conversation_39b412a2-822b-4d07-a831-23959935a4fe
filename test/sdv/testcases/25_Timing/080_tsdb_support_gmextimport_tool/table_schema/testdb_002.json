{"src_file": "Testdb", "src_table": "t_testdb", "dst_table": "t_testdb_tsdb", "chunk_capacity": 100, "time_col": "logTime", "disk_limit": "1024 MB", "interval": "1 hour", "is_volatile_label": "false", "src_def": [{"src_name": "log_id", "src_type": "GMEI_I64_"}, {"src_name": "log_time", "src_type": "GMEI_I64_"}, {"src_name": "log_int", "src_type": "GMEI_I64_"}, {"src_name": "log_char", "src_type": "GMEI_STR_"}, {"src_name": "log_blob", "src_type": "GMEI_BLOB_"}, {"src_name": "log_ip", "src_type": "GMEI_STR_"}, {"src_name": "log_text", "src_type": "GMEI_STR_"}], "dst_def": [{"dst_name": "logId", "dst_type": "GMEI_I64_", "max_size": 0, "src_data": "log_id"}, {"dst_name": "logTime", "dst_type": "GMEI_I64_", "max_size": 0, "src_data": "log_time"}, {"dst_name": "logInt", "dst_type": "GMEI_I64_", "max_size": 0, "src_data": "log_int"}, {"dst_name": "logChar", "dst_type": "GMEI_STR_", "max_size": 64, "src_data": "log_char"}, {"dst_name": "logBlob", "dst_type": "GMEI_BLOB_", "max_size": 160, "src_data": "log_blob"}, {"dst_name": "logIp", "dst_type": "GMEI_INET_", "max_size": 33, "src_data": "log_ip"}, {"dst_name": "logText", "dst_type": "GMEI_TEXT_", "max_size": 10, "src_data": "log_text"}]}