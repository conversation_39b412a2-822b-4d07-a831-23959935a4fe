{"src_file": "Dbtest_Org", "src_table": "t_dbtest", "dst_table": "t_dbtest_tsdb", "chunk_capacity": 9000, "time_col": "log_time1", "cache_size": "0", "compression": "fast", "disk_limit": "50 MB", "interval": "1 hour", "is_volatile_label": "false", "src_def": [{"src_name": "log_id", "src_type": "GMEI_I64_"}, {"src_name": "log_time", "src_type": "GMEI_I64_"}, {"src_name": "temp_int1", "src_type": "GMEI_I64_"}, {"src_name": "temp_int2", "src_type": "GMEI_I64_"}, {"src_name": "temp_int3", "src_type": "GMEI_I64_"}, {"src_name": "temp_char1", "src_type": "GMEI_STR_"}, {"src_name": "temp_char2", "src_type": "GMEI_STR_"}, {"src_name": "temp_char3", "src_type": "GMEI_STR_"}, {"src_name": "temp_blob1", "src_type": "GMEI_BLOB_"}, {"src_name": "temp_blob2", "src_type": "GMEI_BLOB_"}, {"src_name": "temp_blob3", "src_type": "GMEI_BLOB_"}, {"src_name": "temp_charint1", "src_type": "GMEI_STR_"}, {"src_name": "temp_charint2", "src_type": "GMEI_STR_"}, {"src_name": "temp_charint3", "src_type": "GMEI_STR_"}, {"src_name": "temp_charipv4_1", "src_type": "GMEI_STR_"}, {"src_name": "temp_charipv4_2", "src_type": "GMEI_STR_"}, {"src_name": "temp_charipv4_3", "src_type": "GMEI_STR_"}, {"src_name": "temp_intip1", "src_type": "GMEI_I64_"}, {"src_name": "temp_intip2", "src_type": "GMEI_I64_"}, {"src_name": "temp_intip3", "src_type": "GMEI_I64_"}, {"src_name": "temp_charip1", "src_type": "GMEI_STR_"}, {"src_name": "temp_charip2", "src_type": "GMEI_STR_"}, {"src_name": "temp_charip3", "src_type": "GMEI_STR_"}], "dst_def": [{"dst_name": "log_id1", "dst_type": "GMEI_I64_", "max_size": 0, "src_data": "log_id"}, {"dst_name": "log_time1", "dst_type": "GMEI_I64_", "max_size": 0, "src_data": "log_time"}, {"dst_name": "char1", "dst_type": "GMEI_STR_", "max_size": 64, "src_data": "temp_char1"}, {"dst_name": "char2", "dst_type": "GMEI_STR_", "max_size": 64, "src_data": "temp_char2"}, {"dst_name": "char3", "dst_type": "GMEI_STR_", "max_size": 64, "src_data": "temp_char3"}]}