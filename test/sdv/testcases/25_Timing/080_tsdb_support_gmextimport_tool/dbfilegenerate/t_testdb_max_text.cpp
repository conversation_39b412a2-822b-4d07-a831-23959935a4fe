#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <sqlite3.h>
#include <arpa/inet.h>
#include "sqlite_table.h"

int main(int argc, char *argv[])
{
    if (argc > MAX_ARG_NUM) {
        printf("Usage: ./t_testdb -p [cmd file path] -n [total row num]\n");
        return -1;
    }
    char *filePath = NULL;
    int num_records = 1500000;
    if (argc > 1) {
        uint32_t i = 1;
        while (i < argc) {
            if (strcmp(argv[i], "-p") == 0 && i + 1 < argc) {
                filePath = argv[i + 1];
                i += 2;
                continue;
            } else if (strcmp(argv[i], "-n") == 0 && i + 1 < argc) {
                num_records = atoi(argv[i + 1]);
                i += 2;
                continue;
            } else {
                printf("Usage: ./t_testdb -p [cmd file path] -n [total row num]\n");
                return -1;
            }
        }
    }
    sqlite3 *db;
    char *err_msg = 0;
    int rc;

    // 打开 SQLite 数据库
    if (filePath == NULL) {
        rc = sqlite3_open("Testdb.db", &db);
    } else {
        rc = sqlite3_open(filePath, &db);
    }
    if (rc != SQLITE_OK) {
        fprintf(stderr, "无法打开数据库: %s\n", sqlite3_errmsg(db));
        sqlite3_close(db);
        return 1;
    }

    // 创建 t_testdb 表的 SQL 语句
    char *sql_create_table = "CREATE TABLE IF NOT EXISTS t_testdb("
                             "log_id INTEGER PRIMARY KEY,"
                             "log_time INTEGER,"
                             "log_int INTEGER,"
                             "log_char TEXT,"
                             "log_blob BLOB,"
                             "log_ip TEXT,"
                             "log_text TEXT"
                             ");";

    // 执行创建表的 SQL 语句
    rc = sqlite3_exec(db, sql_create_table, 0, 0, &err_msg);
    if (rc != SQLITE_OK) {
        fprintf(stderr, "SQL 错误: %s\n", err_msg);
        sqlite3_free(err_msg);
        sqlite3_close(db);
        return 1;
    }

    // 随机text源
    char maxText[65535];
    for (int i = 0; i < 65534; i++){
        maxText[i] = 'a';
    }
    maxText[65534] = '\0';
    int insertCnt = 10000;
    int maxLen = 65535;
    int minLen = 1;
    char **randomText = (char **)malloc(insertCnt * sizeof(char *));
    if (randomText == NULL) {
        printf("malloc failed\n");
    }
    for (int i = 0; i < insertCnt; i++) {
        randomText[i] = (char *)malloc(maxLen * sizeof(char));
        if (randomText[i] == NULL) {
            printf("malloc failed\n");
        }
    }
    // 把text列每一个字符设为'a'
    for (int i = 0; i < insertCnt; i++) {
        (void)memcpy(randomText[i], maxText, sizeof(maxText));
    }

    // 随机blob源
    maxLen = 160;
    minLen = 20;
    char **randomBlob = (char **)malloc(insertCnt * sizeof(char *));
    if (randomBlob == NULL) {
        printf("malloc failed\n");
    }
    for (int i = 0; i < insertCnt; i++) {
        randomBlob[i] = (char *)malloc(maxLen * sizeof(char));
        if (randomBlob[i] == NULL) {
            printf("malloc failed\n");
        }
    }
    for (int i = 0; i < insertCnt; i++) {
        (void)memset(randomBlob[i], 0, sizeof(randomBlob[i]));
    }
    // 构造全随机长度的blob字符串
    GetRandomString(randomBlob, insertCnt, maxLen, minLen);

    char g_ip[20][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111", "33333333",
        "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "123456789a9887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char g_name[30][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "apple", "bob", "bob", "omgd", 
        "cc", "lucy", "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John",
        "3qwe", "3wer", "3ert", "3rty", "3tyu", "3yui", "3uio", "3iop", "3qwe", "3wer"};
    // 生成数据
    int log_id = 0;
    // 起始时间点2030-01-01 00:00:00
    int64_t log_time = 1893427200;
    int64_t startTime = 1893427200;
    int64_t log_int = 0;
    char log_char[64];
    char log_blob[160];
    char log_ip[33];
    char log_text[65535];

    // 准备插入数据的 SQL 语句
    char *sql_insert = "INSERT INTO t_testdb ("
                    "log_time, log_int, log_char, log_blob, log_ip, log_text"
                    ") VALUES (?,?,?,?,?,?)";
    sqlite3_exec(db, "begin;", 0, 0, 0);
    sqlite3_stmt *stmt;
    rc = sqlite3_prepare_v2(db, sql_insert, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        fprintf(stderr, "无法准备语句: %s\n", sqlite3_errmsg(db));
        sqlite3_close(db);
        return 1;
    }
    // 插入随机数据
    for (int i = 0; i < num_records; i++) {
        // 生成数据
        log_id++;
        log_time = startTime + log_id / 10;
        log_int = rand() % LLONG_MAX;
        (void)strcpy(log_char, g_name[i % 30]);
        (void)strcpy(log_blob, randomBlob[i % insertCnt]);
        (void)strcpy(log_ip, g_ip[i % 20]);
        (void)strcpy(log_text, randomText[i % insertCnt]);
        // 绑定参数
        sqlite3_bind_int(stmt, 1, log_time);
        sqlite3_bind_int(stmt, 2, log_int);
        sqlite3_bind_text(stmt, 3, log_char, -1, SQLITE_TRANSIENT);
        sqlite3_bind_blob(stmt, 4, log_blob, 160, SQLITE_TRANSIENT);
        sqlite3_bind_text(stmt, 5, log_ip, -1, SQLITE_TRANSIENT);
        sqlite3_bind_text(stmt, 6, log_text, -1, SQLITE_TRANSIENT);
        // 执行插入操作
        rc = sqlite3_step(stmt);
        if (rc != SQLITE_DONE) {
            fprintf(stderr, "执行失败: %s\n", sqlite3_errmsg(db));
        }
        // 重置语句以便下一次插入
        sqlite3_reset(stmt);
    }
    // 释放
    for (int i = 0; i < insertCnt; i++) {
        free(randomText[i]);
        free(randomBlob[i]);
    }
    free(randomText);
    free(randomBlob);

    // 结束语句并关闭数据库连接
    sqlite3_finalize(stmt);
    sqlite3_exec(db, "commit;", 0, 0, 0);
    sqlite3_close(db);

    printf("随机数据已成功插入到 t_testdb 表中。\n");

    return 0;
}
