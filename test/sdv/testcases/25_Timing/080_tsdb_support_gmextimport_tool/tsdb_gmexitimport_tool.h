/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: 【时序交付】TSDB支持在线存储实例切换
 */
#ifndef TSDB_GMEXTIMPORT_TOOL_H
#define TSDB_GMEXTIMPORT_TOOL_H

#include <limits.h>
#include "gtest/gtest.h"
#include "t_rd_ts.h"
#include "gmc_test.h"
#include "gmc_sysview.h"
#include "gmc_persist.h"
#include "gmc_ts_persist.h"

char g_tableName[] = "testdb0";
char g_tableName2[] = "testdb1";
char g_tableName3[] = "testdb_TEST1";
bool g_isTrue = false;
bool g_isInsertDataSuccessed = true;
int64_t g_insertTimes = 0;
char g_intervalHour[] = "1 hour";
char g_intervalDay[] = "1 Day";
char g_intervalMonth[] = "1 Month";
char g_intervalYear[] = "1 year";
static char g_dataFilePath[150] = {0};
static char g_dataFilePath1[150] = {0};
static char g_dataFilePath2[150] = {0};
static char g_dataFilePath3[150] = {0};
int64_t g_dataSize1Before = 0;
int64_t g_dataSize1After = 0;
int64_t g_dataSize2Before = 0;
int64_t g_dataSize2After = 0;
int64_t g_concurrentrStatus = 0;
char *g_filePath = getenv("PWD");

#ifdef RUN_INDEPENDENT
#define TEMPFILE_PATH "/data/gmdb/temp/large_result"
#define TABLE_PATH "/data/gmdb/"
#else
#define TEMPFILE_PATH "/mnt/hdd/data/gmdb/temp/large_result"
#define TABLE_PATH "/mnt/hdd/data/gmdb/"
#endif

char g_ip[20][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111", "33333333",
    "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
    "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
    "123456789a9887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
    "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
char g_name[20][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc", "lucy",
    "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
char g_message[20][160] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537", "0071 3521",
    "0010 0001", "", "3102 0022", "0000 0001", "9021 6538", "0071 3522", "0010 0002", "0010 0000", "3102 0023",
    "0000 0002", "9021 6539", "0071 3523", "0010 0003"};
char g_desText[20][64] = {"test data of the text type:0", "test data of the text type:1",
    "test data of the text type:2", "test data of the text type:3", "test data of the text type:4",
    "test data of the text type:5", "test data of the text type:6", "test data of the text type:7",
    "test data of the text type:8", "test data of the text type:9", "test data of the text type:0",
    "test data of the text type:1", "test data of the text type:2", "test data of the text type:3",
    "test data of the text type:4", "test data of the text type:5", "test data of the text type:6",
    "test data of the text type:7", "test data of the text type:8", "test data of the text type:9"};

#ifdef __cplusplus
extern "C" {
#endif

struct TableInfo {
    char tableName[20];
    bool isMemoryTable = false;
    char tablePath[256];
    bool isVolatile = false;
    int diskLimit;
    int ttl;
    int cacheSize;
};

// 生成随机字符串
void GetRandomString(char **textData, int dataCount, int maxSize = 65535, int minSize = 20)
{
    time_t timeT;
    timeT = time(NULL);
    (void)srand((uint32_t)timeT);
    char charset[] = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    int textLength = 0;
    char str[65535] = {0};
    for (int i = 0; i < dataCount; i++) {
        textLength = (rand() % maxSize + 1) > minSize ? (rand() % maxSize + 1) : minSize;
        (void)memset(str, 0, 65535);
        for (int n = 0; n < textLength - 1; n++) {
            int key = rand() % (int)(sizeof(charset) - 1);
            str[n] = charset[key];
        }
        str[textLength - 1] = '\0';
        (void)sprintf(*(textData + i), str, sizeof(str));
    }
    // 定义用于生成随机字符串的字符集
}

int CreateTable(GmcStmtT *stmt, char *tableName, char *intervalValue, char *diskLimitValue, char *ttlValue)
{
    int ret = 0;
    char sqlCmd[512] = {0};
    char tempDiskLimit[20] = {0};
    if (diskLimitValue == NULL) {
        (void)sprintf(tempDiskLimit, "1024 MB");
    } else {
        (void)sprintf(tempDiskLimit, "%s", diskLimitValue);
    }
    if (ttlValue == NULL) {
        (void)sprintf(sqlCmd,
            "create table %s(id integer, time integer, name char(64), ip inet, message blob(160),"
            " description text, id1 integer, time1 integer)with (time_col = 'time', interval = '%s', "
            " disk_limit = '%s', compression = 'fast(rapidlz)');",
            tableName, intervalValue, tempDiskLimit);
    } else {
        (void)sprintf(sqlCmd,
            "create table %s(id integer, time integer, name char(64), ip inet, message blob(160),"
            " description text, id1 integer, time1 integer)with (time_col = 'time', interval = '%s',  "
            " disk_limit = '%s', ttl ='%s', compression = 'fast(rapidlz)');",
            tableName, intervalValue, tempDiskLimit, ttlValue);
    }
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    RETURN_IFERR(ret);
    return ret;
}

// 时序接口客户端建连设置msgReadTimeout
int TestTsGmcConnectTime(GmcConnT **conn, GmcStmtT **stmt, int syncMode = 0, char *connName = NULL)
{
    int ret = 0;
    YangConnOptionT tsConnOptions = {0};
    tsConnOptions.msgReadTimeout = 180 * 1000;
    tsConnOptions.isCsMode = true;
    tsConnOptions.serverLocator = g_connServerTsdb;
    ret = TestYangGmcConnect(conn, stmt, syncMode, &tsConnOptions);
    return ret;
}

// 注入顺序数据
int rowInsertData(GmcStmtT *stmt, char *tableName, int64_t dataCount, int64_t startTime)
{
    int ret = 0;
    int64_t count = dataCount > 50000 ? 50000 : dataCount;
    int64_t *id;
    int64_t *id1;
    int64_t *time;
    int64_t *timet;
    // 申请内存
    char *nameList = (char *)malloc(count * 64);
    if (nameList == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    char *ipList = (char *)malloc(count * 33);
    if (ipList == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    char **textList = (char **)malloc(count * sizeof(char *));
    if (textList == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    for (int i = 0; i < count; i++) {
        textList[i] = (char *)malloc(64 * sizeof(char));
        (void)memset(textList[i], 0, 64);
    }
    id = (int64_t *)malloc(sizeof(int64_t) * count);
    if (id == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    id1 = (int64_t *)malloc(sizeof(int64_t) * count);
    if (id == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    time = (int64_t *)malloc(sizeof(int64_t) * count);
    if (time == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    timet = (int64_t *)malloc(sizeof(int64_t) * count);
    if (timet == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    char **blobList = (char **)malloc(count * sizeof(char *));
    if (blobList == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    for (int i = 0; i < count; i++) {
        blobList[i] = (char *)malloc(160 * sizeof(char));
        (void)memset(blobList[i], 0, 160);
    }
    // 构造数据
    for (int i = 0; i < count; i++) {
        id[i] = i;
        id1[i] = i;
        // 1288630830  2010年11月2日 01：00：30 开始
        time[i] = startTime + i / 10;   // 2010年1月1日0点 1262275200
        timet[i] = startTime + i / 10;  // 2010年1月1日0点 1262275200
        int j = i % 20;
        memcpy((ipList + i * 33), (char *)g_ip[j], 33);
        memcpy((nameList + i * 64), (char *)g_name[j], 64);
        (void)sprintf(*(textList + i), "%s", (char *)g_desText[j], 64);
        // 释放内存时不能直接赋值，需要复制后才能释放
        (void)strcpy(blobList[i], g_message[j]);
    }

    uint32_t rowNum = dataCount > 50000 ? 50000 : dataCount;
    uint32_t loopNum = dataCount > 50000 ? (dataCount / 50000) : 1;
    // AW_FUN_Log(LOG_STEP, "插入数据开始");
    for (int i = 0; i < loopNum; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
        if (ret != 0) {
            printf("tableName is %s\n", tableName);
            goto freeAndReturn;
        }
        ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(int64_t));
        ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), 0);
        ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, time, sizeof(time[0]), 0);
        ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_FIXED, nameList, 64, NULL);
        ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_FIXED, ipList, 33, NULL);
        ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_BYTES, blobList, sizeof(blobList[0]), NULL);
        ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_STRING, textList, sizeof(char *), 0);
        ret = GmcBindCol(stmt, 6, (GmcDataTypeE)DB_DATATYPE_INT64, id1, sizeof(id1[0]), 0);
        ret = GmcBindCol(stmt, 7, (GmcDataTypeE)DB_DATATYPE_INT64, timet, sizeof(timet[0]), 0);
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            goto freeAndReturn;
        }
    }
    // AW_FUN_Log(LOG_STEP, "插入数据完成");
freeAndReturn:
    // 释放内存
    free(id);
    free(id1);
    free(time);
    free(timet);
    free(nameList);
    free(ipList);
    for (int i = 0; i < count; i++) {
        free(blobList[i]);
        free(textList[i]);
    }
    free(blobList);
    free(textList);
    return ret;
}

// 往t_testdb表中写数据
int insertToToolTable(GmcStmtT *stmt, char *tableName, int64_t dataCount, int64_t startTime)
{
    int ret = 0;
    int64_t count = dataCount > 50000 ? 50000 : dataCount;
    int64_t *logId;
    int64_t *logTime;
    int64_t *logInt;
    // 申请内存
    char *logChar = (char *)malloc(count * 64);
    if (logChar == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    char *logIp = (char *)malloc(count * 33);
    if (logIp == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }

    logId = (int64_t *)malloc(sizeof(int64_t) * count);
    if (logId == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    logTime = (int64_t *)malloc(sizeof(int64_t) * count);
    if (logTime == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    logInt = (int64_t *)malloc(sizeof(int64_t) * count);
    if (logInt == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    int maxLen = 65535;
    int minLen = 1;
    char **logText = (char **)malloc(count * sizeof(char *));
    if (logText == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    for (int i = 0; i < count; i++) {
        logText[i] = (char *)malloc(maxLen * sizeof(char));
        (void)memset(logText[i], 0, maxLen);
    }
    GetRandomString(logText, count, maxLen, minLen);
    maxLen = 160;
    minLen = 20;
    char **logBlob = (char **)malloc(count * sizeof(char *));
    if (logBlob == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    for (int i = 0; i < count; i++) {
        logBlob[i] = (char *)malloc(maxLen * sizeof(char));
        (void)memset(logBlob[i], 0, maxLen);
    }
    GetRandomString(logBlob, count, maxLen, minLen);

    // 构造数据
    for (int i = 0; i < count; i++) {
        logId[i] = i;
        // 1288630830  2010年11月2日 01：00：30 开始
        logTime[i] = startTime + i / 100;  // 2010年1月1日0点 1262275200
        logInt[i] = rand() % LLONG_MAX;
        int j = i % 20;
        memcpy((logIp + i * 33), (char *)g_ip[j], 33);
        memcpy((logChar + i * 64), (char *)g_name[j], 64);
    }

    uint32_t rowNum = dataCount > 50000 ? 50000 : dataCount;
    uint32_t loopNum = dataCount > 50000 ? (dataCount / 50000) : 1;
    AW_FUN_Log(LOG_STEP, "插入数据开始");
    for (int i = 0; i < loopNum; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
        if (ret != 0) {
            printf("tableName is %s\n", tableName);
            goto freeTestdb;
        }
        ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(int64_t));
        ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, logId, sizeof(logId[0]), 0);
        ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, logTime, sizeof(logTime[0]), 0);
        ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, logInt, sizeof(logInt[0]), 0);
        ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_FIXED, logChar, 64, NULL);
        ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_BYTES, logBlob, sizeof(logBlob[0]), NULL);
        ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_FIXED, logIp, 33, NULL);
        ret = GmcBindCol(stmt, 6, (GmcDataTypeE)DB_DATATYPE_STRING, logText, sizeof(char *), 0);
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            goto freeTestdb;
        }
    }
    printf("insert data to tool table success\n");
    AW_FUN_Log(LOG_STEP, "插入数据完成");
freeTestdb:
    // 释放内存
    free(logId);
    free(logTime);
    free(logInt);
    free(logChar);
    free(logIp);
    for (int i = 0; i < count; i++) {
        free(logBlob[i]);
        free(logText[i]);
    }
    free(logBlob);
    free(logText);
    return ret;
}

// 往t_testdb表中写缺列数据
int insertLackDataToToolTable(GmcStmtT *stmt, char *tableName, int64_t dataCount, int64_t startTime)
{
    int ret = 0;
    int64_t count = dataCount > 50000 ? 50000 : dataCount;
    int64_t *logId;
    int64_t *logTime;
    // 申请内存
    char *logChar = (char *)malloc(count * 64);
    if (logChar == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    char *logIp = (char *)malloc(count * 33);
    if (logIp == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }

    logId = (int64_t *)malloc(sizeof(int64_t) * count);
    if (logId == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    logTime = (int64_t *)malloc(sizeof(int64_t) * count);
    if (logTime == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    int maxLen = 65535;
    int minLen = 1;
    char **logText = (char **)malloc(count * sizeof(char *));
    if (logText == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    for (int i = 0; i < count; i++) {
        logText[i] = (char *)malloc(maxLen * sizeof(char));
        (void)memset(logText[i], 0, maxLen);
    }
    GetRandomString(logText, count, maxLen, minLen);
    maxLen = 160;
    minLen = 20;
    char **logBlob = (char **)malloc(count * sizeof(char *));
    if (logBlob == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    for (int i = 0; i < count; i++) {
        logBlob[i] = (char *)malloc(maxLen * sizeof(char));
        (void)memset(logBlob[i], 0, maxLen);
    }
    GetRandomString(logBlob, count, maxLen, minLen);

    // 构造数据
    for (int i = 0; i < count; i++) {
        logId[i] = i;
        // 1288630830  2010年11月2日 01：00：30 开始
        logTime[i] = startTime + i / 100;  // 2010年1月1日0点 1262275200
        int j = i % 20;
        memcpy((logIp + i * 33), (char *)g_ip[j], 33);
        memcpy((logChar + i * 64), (char *)g_name[j], 64);
    }

    uint32_t rowNum = dataCount > 50000 ? 50000 : dataCount;
    uint32_t loopNum = dataCount > 50000 ? (dataCount / 50000) : 1;
    AW_FUN_Log(LOG_STEP, "插入数据开始");
    for (int i = 0; i < loopNum; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
        if (ret != 0) {
            printf("tableName is %s\n", tableName);
            goto freeTestdb;
        }
        ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(int64_t));
        ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, logId, sizeof(logId[0]), 0);
        ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, logTime, sizeof(logTime[0]), 0);
        ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_FIXED, logChar, 64, NULL);
        ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_BYTES, logBlob, sizeof(logBlob[0]), NULL);
        ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_FIXED, logIp, 33, NULL);
        ret = GmcBindCol(stmt, 6, (GmcDataTypeE)DB_DATATYPE_STRING, logText, sizeof(char *), 0);
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            goto freeTestdb;
        }
    }
    printf("insert data to tool table success\n");
    AW_FUN_Log(LOG_STEP, "插入数据完成");
freeTestdb:
    // 释放内存
    free(logId);
    free(logTime);
    free(logChar);
    free(logIp);
    for (int i = 0; i < count; i++) {
        free(logBlob[i]);
        free(logText[i]);
    }
    free(logBlob);
    free(logText);
    return ret;
}

typedef struct {
    GmcStmtT *stmt;
    char *tableName;
    int64_t dataCount;
    int64_t startTime;
} InsertQueryDataType;

// 并发写数据
void *InsertDataToTable(void *arg)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = TestTsGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    InsertQueryDataType constructData = *(InsertQueryDataType *)arg;
    ret = rowInsertData(stmt, constructData.tableName, constructData.dataCount, constructData.startTime);
    AW_FUN_Log(LOG_STEP, "插入数据完成");
    g_concurrentrStatus = ret;
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return nullptr;
}

// 并发查询数据
void *QueryDataToTable(void *arg)
{
    AW_FUN_Log(LOG_STEP, "查询开始");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    char *sqlCmd = (char *)arg;
    int ret = TestTsGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    if (ret == GMERR_OK) {
        AW_FUN_Log(LOG_STEP, "查询完成");
    } else {
        AW_FUN_Log(LOG_STEP, "查询失败,ret is %d", ret);
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return nullptr;
}

// 并发copy to
void *CopyToDataToTable(void *arg)
{
    AW_FUN_Log(LOG_STEP, "copy to开始");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    char *tableName = (char *)arg;
    int ret = TestTsGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *dir1 = getenv("PWD");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd,
        "COPY (select * from %s) TO "
        " '%s/data.csv';",
        tableName, dir1);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_FUN_Log(LOG_STEP, "copy to完成");
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return nullptr;
}

typedef struct {
    char *tableNameIn;
    char *tableNameOut;
} InsertIntoBody;

// 并发insertinto
void *InsertIntoDataToTable(void *arg)
{
    AW_FUN_Log(LOG_STEP, "insert into开始");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    InsertIntoBody insertIntoBody = *(InsertIntoBody *)arg;
    int ret = TestTsGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *dir1 = getenv("PWD");
    if (dir1 == NULL) {
        return nullptr;
    }
    char sqlCmd[512] = {0};
    (void)memset(sqlCmd, 0, 512);
    // 执行insert into语句
    (void)sprintf(sqlCmd,
        "insert into %s(id, time, ip, name, description, id1, time1)"
        "select * from %s order by time limit 200",
        insertIntoBody.tableNameIn, insertIntoBody.tableNameOut);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_FUN_Log(LOG_STEP, "insert into完成");
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return nullptr;
}

// 并发建表
void *ConcurrentCreateTable(void *arg)
{
    AW_FUN_Log(LOG_STEP, "建表开始");
    InsertQueryDataType constructData = *(InsertQueryDataType *)arg;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int startId = constructData.startTime;
    int createTableCount = constructData.dataCount;
    char sqlCmd[512] = {0};
    uint32_t cmdLen = 0;
    for (int i = startId; i < startId + createTableCount; i++) {
        (void)memset(sqlCmd, 0, 512);
        (void)sprintf(sqlCmd,
            "create table %s%ld(id integer, time integer, name char(64), ip inet, message blob(160),"
            " description text, id1 integer, time1 integer)with (time_col = 'time', interval = '%s', "
            " disk_limit = '1024 MB', compression = 'fast(rapidlz)');",
            constructData.tableName, i, g_intervalHour);
        cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
        if (ret != 0) {
            AW_FUN_Log(LOG_STEP, "失败, ret is %ld", ret);
            break;
        }
    }
    AW_FUN_Log(LOG_STEP, "建表完成");
    g_concurrentrStatus = ret;
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return nullptr;
}

// 并发删表
void *ConcurrentDeleteTable(void *arg)
{
    AW_FUN_Log(LOG_STEP, "删表开始");
    InsertQueryDataType constructData = *(InsertQueryDataType *)arg;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int startId = constructData.startTime;
    int deleteTableCount = constructData.dataCount;
    char sqlCmd[512] = {0};
    uint32_t cmdLen = 0;
    for (int i = startId; i < startId + deleteTableCount; i++) {
        (void)memset(sqlCmd, 0, 512);
        (void)sprintf(sqlCmd, "drop table %s%ld", constructData.tableName, i);
        cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
        if (ret != 0) {
            AW_FUN_Log(LOG_STEP, "失败, ret is %ld", ret);
            break;
        }
    }
    AW_FUN_Log(LOG_STEP, "删表完成");
    g_concurrentrStatus = ret;
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return nullptr;
}

struct CreateInfo {
    int tableCount;
    // 0代表为普通逻辑表，1代表为易失性路径逻辑表，2代表为内存表
    int tableType;
    int startId;
};

// 并发tsdb_aging
void *ConcurrentTsdbAging(void *arg)
{
    CreateInfo createInfo = *(CreateInfo *)arg;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    int tableCount = createInfo.tableCount;
    int tableType = createInfo.tableType;
    int startId = createInfo.startId;
    TableInfo tableInfos[tableCount];
    memset(tableInfos, 0, sizeof(TableInfo) * tableCount);
    for (int i = 0; i < tableCount; i++) {
        (void)sprintf(tableInfos[i].tableName, "testdb%d", i + startId);
        (void)sprintf(sqlCmd, "select tsdb_aging('%s');", tableInfos[i].tableName);
        ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_STEP, "tsdb_aging failed, tableName is %s", tableInfos[i].tableName);
            continue;
        }
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return nullptr;
}

// 并发Truncate
void *ConcurrentTruncate(void *arg)
{
    CreateInfo createInfo = *(CreateInfo *)arg;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    int tableCount = createInfo.tableCount;
    int startId = createInfo.startId;
    TableInfo tableInfos[tableCount];
    memset(tableInfos, 0, sizeof(TableInfo) * tableCount);
    for (int i = 0; i < tableCount; i++) {
        (void)sprintf(tableInfos[i].tableName, "testdb%d", i + startId);
        (void)sprintf(sqlCmd, "TRUNCATE TABLE %s;", tableInfos[i].tableName);
        ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_STEP, "Truncate failed, tableName is %s ret is %d", tableInfos[i].tableName, ret);
            continue;
        }
    }
    g_concurrentrStatus = ret;
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return nullptr;
}

// 并发写数据
void *InsertDataTimeAdd(void *arg)
{
    InsertQueryDataType constructData = *(InsertQueryDataType *)arg;
    GmcStmtT *stmttemp = constructData.stmt;
    int ret = 0;
    int tempStartTime = constructData.startTime;
    // 获取当前时间
    time_t timeT;
    timeT = time(NULL);
    while (g_isInsertDataSuccessed) {
        if (tempStartTime > timeT + 36000) {
            tempStartTime = constructData.startTime;
        }
        ret = rowInsertData(stmttemp, constructData.tableName, constructData.dataCount, tempStartTime);
        if (ret != GMERR_OK && ret != GMERR_OUT_OF_MEMORY) {
            AW_FUN_Log(LOG_STEP, "ret is %d", ret);
        }
        tempStartTime += 3600;
        sleep(1);
    }
    AW_FUN_Log(LOG_STEP, "插入数据完成");
    return nullptr;
}

typedef struct {
    GmcStmtT *stmt;
    char *sqlCmd;
    int64_t cycleNum;
} ExecType;

// 建t_testdb表
int CreateToolTable(char *tableName, int blobSize)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = TestTsGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    if (blobSize == 160) {
        (void)sprintf(sqlCmd,
            "create table if not exists %s(logId integer, logTime integer, logInt integer, logChar char(64), logBlob "
            "blob(160), logIp inet, logText text) with (time_col = 'logTime', interval = '1 hour');",
            tableName);
    } else {
        (void)sprintf(sqlCmd,
            "create table if not exists %s(logId integer, logTime integer, logInt integer, logChar char(64), logBlob "
            "blob, logIp inet, logText text) with (time_col = 'logTime', interval = '1 hour');",
            tableName);
    }
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 并发删t_testdb表
void *CurrentDropTestTable(void *arg)
{
    char *toolTableName = (char *)arg;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = TestTsGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 获取当前时间
    char sqlCmd[256] = {0};
    uint32_t cmdLen = 0;
    // 建表
    (void)sprintf(sqlCmd, "drop table %s", toolTableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_STEP, "删表失败, tableName is %s", toolTableName);
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return nullptr;
}

bool flag = false;
// 并发工具导表
void *ToolImportTable(void *arg)
{
    if (flag) {
        AW_FUN_Log(LOG_STEP, "已经有工具线程");
    }
    flag = true;
    char *sqlCmd = (char *)arg;
    int ret = executeCommand(sqlCmd, "Using singlethread mode");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return nullptr;
}

// 获取视图结果关键字value
int GetViewFieldResultValue(int result[], const char *viewStatement, const char *key)
{
    constexpr int maxSize = 512;
    char cmdOutput[maxSize] = {0};
    int i = 0;
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));

    char command[maxSize] = {0};
    (void)snprintf(command, maxSize, "%s |grep %s |awk -F ':' '{print $2}'", viewStatement, key);

    FILE *pf = popen(command, "r");
    if (pf == NULL) {
        printf("popen(%s) error./n", command);
    }
    EXPECT_NE((void *)NULL, pf);
    while (NULL != fgets(cmdOutput, maxSize, pf) && i < maxSize) {
        result[i] = atoi(cmdOutput);
        i++;
    };
    pclose(pf);
    return GMERR_OK;
}

// 并发写数据到导入工具表t_testdb表
void *InsertDateToToolTable(void *arg)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = TestTsGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    InsertQueryDataType constructData = *(InsertQueryDataType *)arg;
    ret = insertToToolTable(stmt, constructData.tableName, constructData.dataCount, constructData.startTime);
    AW_FUN_Log(LOG_STEP, "插入数据完成");
    g_concurrentrStatus = ret;
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return nullptr;
}

// 并发写缺列数据到导入工具表t_testdb表
void *InsertLackDateToToolTable(void *arg)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = TestTsGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    InsertQueryDataType constructData = *(InsertQueryDataType *)arg;
    ret = insertLackDataToToolTable(stmt, constructData.tableName, constructData.dataCount, constructData.startTime);
    AW_FUN_Log(LOG_STEP, "插入数据完成");
    g_concurrentrStatus = ret;
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return nullptr;
}

// 并发alter新增列
void *CurrentAlterTableAddCol(void *arg)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = TestTsGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *tableName = (char *)arg;
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "alter table %s add newCol text;", tableName);
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return nullptr;
}

// 并发alter修改老化配置
void *CurrentAlterTableSetConfigure(void *arg)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = TestTsGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *tableName = (char *)arg;
    char sqlCmd[512] = {0};
    int randDiskValue = 20 + rand() % 20;
    (void)sprintf(sqlCmd, "alter table %s set (disk_limit = '%d MB');", tableName, randDiskValue);
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return nullptr;
}

#ifdef __cplusplus
}
#endif

#endif /* TSDB_GMEXTIMPORT_TOOL_H */
