/*****************************************************************************
 Description  : 持久化文件一致性DFX
 Notes        : 
            001 按需页压缩,开启crc,破坏持久化文件,校验GmcVerifyData
            002 按需页压缩,开启sha,破坏持久化文件,校验GmcVerifyData
            003 按需device压缩,开启crc,破坏持久化文件,校验GmcVerifyData
            004 按需device压缩,开启sha,破坏持久化文件,校验GmcVerifyData
            005 增量页压缩,开启crc,破坏持久化文件,校验GmcVerifyData
            006 增量页压缩,开启sha256,破坏持久化文件,校验GmcVerifyData


 History      :
 Author       : 潘鹏 pwx860460
 Modification :
 Date         : 2024-09-11
*****************************************************************************/

#include "../incre_pst_common.h"

#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];
int g_beginIndex = 0;
int g_endIndex = 200;

class PstCompressTest : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {
    }
    static void TearDownTestCase() {}
};

int DisAndClean()
{
    int ret = testGmcDisconnect(g_conn, g_stmt);
    RETURN_IFERR(ret);
    ret = GmcUnInit();
    RETURN_IFERR(ret);
    ret = close_epoll_thread();
    RETURN_IFERR(ret);
    testEnvClean();
    return T_OK;
}


void PstCompressTest::SetUp()
{
    printf("[INFO] Incremental Persistence cfg test Start.\n");
    system("sh $TEST_HOME/tools/stop.sh -f");
    AW_ADD_ERRNUM_WHITE_LIST(3, GMERR_CONNECTION_FAILURE, GMERR_CRC_CHECK_FAILED, GMERR_CRC_CHECK_FAILED);
    int ret;
    memset(g_command, 0, sizeof(g_command));// 适配用例干扰配置
    snprintf(g_command, MAX_CMD_SIZE, "\\cp %s ./gmserver.iniback -rf ", g_sysGMDBCfg);
    system(g_command);
    system("rm gmdb* -rf");
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        printf("get env PWD fail.\n");
    }
    (void)sprintf(g_dbFilePath, "%s/gmdb", pwdDir);
    (void)Rmdir(g_dbFilePath);
    ret = mkdir(g_dbFilePath, S_IRUSR | S_IWUSR);
    AW_FUN_Log(LOG_STEP, "mkdir %s ret = %d", g_dbFilePath, ret);
    Addini((char *)"persistentCompressMode", (char *)"0");
    ret = ChangeGmserverCfg((char *)"dataFileDirPath", g_dbFilePath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)sprintf(g_dbFileCtrlPath, "%s/gmdb/dbCtrlFile", pwdDir);
    AW_CHECK_LOG_BEGIN();
}

void PstCompressTest::TearDown()
{
    int ret;
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_FILE_OPERATE_FAILED);
    AW_CHECK_LOG_END();
    system("chattr -R -i gmdb/");
    printf("[INFO] Incremental Persistence cfg test End.\n");
    memset(g_command, 0, sizeof(g_command));// 适配用例干扰配置
    snprintf(g_command, MAX_CMD_SIZE, "\\cp ./gmserver.iniback %s -rf", g_sysGMDBCfg);
    system(g_command);
    system("sh $TEST_HOME/tools/stop.sh -f");
    // 停掉服务，恢复配置，清理持久化文件
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    char expectCmd[100] = {0};
    char *homePath = getenv("HOME");
    (void)snprintf(expectCmd, sizeof(expectCmd), "rm %s/../data/gmdb/* -rf", homePath);
    system(expectCmd);
}

// 按需页压缩,开启crc,破坏持久化文件,校验GmcVerifyData
TEST_F(PstCompressTest, Timing_053_003_001)
{
    int ret = ChangeGmserverCfg((char *)"redoFlushByTrx", (char *)"1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"0");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"persistentCompressMode", (char *)"1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"crcCheckEnable", (char *)"1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSystemCmd("./compress %d %d", g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 重启
    ret = AdapAndStartServer(COMPRESS);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 落盘
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcVerifyPersistenceData(g_stmt, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  验证数据
    char indexName[] = "PrimaryKey";
    char *cond = (char *)"A0(int32)=%i{1}";
    ret = TestSelVertexCount(g_stmt, g_simpleLabel, indexName, cond, g_endIndex, g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestSelVertexCount(g_stmt, g_complexLabel, indexName, cond, g_endIndex, g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 破坏
    DestoryCtrlFileContent(g_dbFileCtrlPath, 1, 100);
    ret = GmcVerifyPersistenceData(g_stmt, NULL);
    AW_MACRO_EXPECT_EQ_INT(1013007, ret);

    ret = DisConnAndClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 按需页压缩,开启sha,破坏持久化文件,校验GmcVerifyData
TEST_F(PstCompressTest, Timing_053_003_002)
{
    int ret = ChangeGmserverCfg((char *)"redoFlushByTrx", (char *)"1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"0");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"persistentCompressMode", (char *)"1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"shaCheckEnable", (char *)"1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSystemCmd("./compress %d %d", g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 重启
    ret = AdapAndStartServer(COMPRESS);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 落盘
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcVerifyPersistenceData(g_stmt, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  验证数据
    char indexName[] = "PrimaryKey";
    char *cond = (char *)"A0(int32)=%i{1}";
    ret = TestSelVertexCount(g_stmt, g_simpleLabel, indexName, cond, g_endIndex, g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestSelVertexCount(g_stmt, g_complexLabel, indexName, cond, g_endIndex, g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 破坏
    DestoryCtrlFileContent(g_dbFileCtrlPath, 1, 100);
    ret = GmcVerifyPersistenceData(g_stmt, NULL);
    AW_MACRO_EXPECT_EQ_INT(1013007, ret);

    ret = DisConnAndClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 按需device压缩,开启crc,破坏持久化文件,校验GmcVerifyData
TEST_F(PstCompressTest, Timing_053_003_003)
{
    int ret = ChangeGmserverCfg((char *)"redoFlushByTrx", (char *)"1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"0");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"persistentCompressMode", (char *)"2");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"crcCheckEnable", (char *)"1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSystemCmd("./compress %d %d", g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 重启
    ret = AdapAndStartServer(COMPRESS);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 落盘
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcVerifyPersistenceData(g_stmt, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  验证数据
    char indexName[] = "PrimaryKey";
    char *cond = (char *)"A0(int32)=%i{1}";
    ret = TestSelVertexCount(g_stmt, g_simpleLabel, indexName, cond, g_endIndex, g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestSelVertexCount(g_stmt, g_complexLabel, indexName, cond, g_endIndex, g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 破坏
    DestoryCtrlFileContent(g_dbFileCtrlPath, 1, 100);
    ret = GmcVerifyPersistenceData(g_stmt, NULL);
    AW_MACRO_EXPECT_EQ_INT(1013007, ret);

    ret = DisConnAndClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 按需device压缩,开启sha,破坏持久化文件,校验GmcVerifyData
TEST_F(PstCompressTest, Timing_053_003_004)
{
    int ret = ChangeGmserverCfg((char *)"redoFlushByTrx", (char *)"1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"0");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"persistentCompressMode", (char *)"2");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"shaCheckEnable", (char *)"1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSystemCmd("./compress %d %d", g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 重启
    ret = AdapAndStartServer(COMPRESS);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 落盘
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcVerifyPersistenceData(g_stmt, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  验证数据
    char indexName[] = "PrimaryKey";
    char *cond = (char *)"A0(int32)=%i{1}";
    ret = TestSelVertexCount(g_stmt, g_simpleLabel, indexName, cond, g_endIndex, g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestSelVertexCount(g_stmt, g_complexLabel, indexName, cond, g_endIndex, g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 破坏
    DestoryCtrlFileContent(g_dbFileCtrlPath, 1, 100);
    ret = GmcVerifyPersistenceData(g_stmt, NULL);
    AW_MACRO_EXPECT_EQ_INT(1013007, ret);

    ret = DisConnAndClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 增量页压缩,开启crc,破坏持久化文件,校验GmcVerifyData
TEST_F(PstCompressTest, Timing_053_003_005)
{
    int ret = ChangeGmserverCfg((char *)"redoFlushByTrx", (char *)"1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"persistentCompressMode", (char *)"1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"crcCheckEnable", (char *)"1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSystemCmd("./compress %d %d", g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 重启
    ret = AdapAndStartServer(COMPRESS);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 落盘
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcVerifyPersistenceData(g_stmt, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //  验证数据
    char indexName[] = "PrimaryKey";
    char *cond = (char *)"A0(int32)=%i{1}";
    ret = TestSelVertexCount(g_stmt, g_simpleLabel, indexName, cond, g_endIndex, g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestSelVertexCount(g_stmt, g_complexLabel, indexName, cond, g_endIndex, g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 破坏
    DestoryCtrlFileContent(g_dbFileCtrlPath, 1, 100);
    ret = GmcVerifyPersistenceData(g_stmt, NULL);
    // 增量持久化 1126重构后的预期适配
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = DisConnAndClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 增量页压缩,开启sha256,破坏持久化文件,校验GmcVerifyData
TEST_F(PstCompressTest, Timing_053_003_006)
{
    int ret = ChangeGmserverCfg((char *)"redoFlushByTrx", (char *)"1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"persistentCompressMode", (char *)"1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"shaCheckEnable", (char *)"1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSystemCmd("./compress %d %d", g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 重启
    ret = AdapAndStartServer(COMPRESS);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 落盘
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcVerifyPersistenceData(g_stmt, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  验证数据
    char indexName[] = "PrimaryKey";
    char *cond = (char *)"A0(int32)=%i{1}";
    ret = TestSelVertexCount(g_stmt, g_simpleLabel, indexName, cond, g_endIndex, g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestSelVertexCount(g_stmt, g_complexLabel, indexName, cond, g_endIndex, g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 破坏
    DestoryCtrlFileContent(g_dbFileCtrlPath, 1, 100);
    ret = GmcVerifyPersistenceData(g_stmt, NULL);
    // 增量持久化 1126重构后的预期适配
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = DisConnAndClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

