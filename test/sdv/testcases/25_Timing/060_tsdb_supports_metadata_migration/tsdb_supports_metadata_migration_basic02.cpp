/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description:【交付增强】DB支持持久化的元数据迁移
 * Author: chenbangjun
 * Create: 2024-10-30
 */
#include "gtest/gtest.h"
#include "t_rd_ts.h"
#include "t_datacom_lite.h"

static GmcConnOptionsT *connOptions = NULL;
static GmcConnT *conn = NULL;
static GmcStmtT *stmt = NULL;
GmcStmtViewT *stmtView = NULL;
char g_cStoreDir[64] = {0};
char g_tableName[] = "testdb";
char g_tableName2[] = "testtable";
char g_viewName[] = "testdb_view";
static char g_dataFilePath[250] = {0};
static char g_dataFilePath2[250] = {0};
static char g_dataFilePath3[250] = {0};
Status ret = 0;
char *dir = getenv("GMDB_HOME");
bool isTheLogExists = false;
static GmcConnT *subConn = NULL;
static GmcStmtT *subStmt = NULL;
const char *g_subConnName = "subConnName";
const char *g_subName = "subQueryC1";
static bool g_testDataInserted = false;
static uint32_t g_winCnt = 0;
static int fetchTimes = 0;
static int idArrays[10];

char g_ips[20][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111", "33333333",
    "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
    "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
    "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
    "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
char g_names[20][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc", "lucy",
    "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
char g_message[20][160] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537", "0071 3521",
    "0010 0001", "", "3102 0022", "0000 0001", "9021 6538", "0071 3522", "0010 0002", "0010 0000", "3102 0023",
    "0000 0002", "9021 6539", "0071 3523", "0010 0003"};
char g_desText[20][128] = {"test data of the text type", "test", "data", "of", "the", "text", "type",
    "test data of the text type", "test data of the text type", "test data of the text type",
    "test data of the text type", "test data of the text type", "test data of the text type",
    "test data of the text type", "test data of the text type", "test data of the text type",
    "test data of the text type", "test data of the text type", "test data of the text type",
    "test data of the text type"};
int64_t g_dataSize1Before = 0;
int64_t g_dataSize1After = 0;

class TsdbMetadataMigration : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        // 创建epoll监听线程
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        const char *cmd = "pwd";
        FILE *fptr = popen(cmd, "r");
        if (fptr == NULL) {
            ASSERT_TRUE(false);
        }
        char tempFilePath[250] = {0};
        fgets(tempFilePath, sizeof(tempFilePath), fptr);
        fclose(fptr);
        tempFilePath[strlen(tempFilePath) - 1] = '\0';  //替换结尾换行符
        (void)sprintf(g_dataFilePath, "%s/data/gmdb", tempFilePath);
        (void)sprintf(g_dataFilePath2, "%s/data/gmdb2", tempFilePath);
        (void)sprintf(g_dataFilePath3, "%s/data/gmdb3", tempFilePath);
        TsDefulatDbFileClean();
        system("rm -rf ./data/gmdb");
        system("rm -rf ./data/gmdb2");
        system("rm -rf ./data/gmdb3");
        system("ipcrm -a");
    }
    static void TearDownTestCase()
    {
        ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        GmcConnOptionsDestroy(connOptions);
        GmcFreeStmt(stmt);
        GmcDisconnect(conn);
        testEnvClean();
        RecoverTsCiCfg();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void TsdbMetadataMigration::SetUp()
{
    AW_CHECK_LOG_BEGIN();
}

void TsdbMetadataMigration::TearDown()
{
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_CHECK_LOG_END();
}

void TearDownDisconnect()
{
    GmcConnOptionsDestroy(connOptions);
    GmcFreeStmt(stmt);
    GmcDisconnect(conn);
    testEnvClean();
    RecoverTsCiCfg();
}

// 建立时序服务端链接
int CreateTsConnect(GmcConnT **conn_ts, GmcStmtT **stmt_ts)
{
    int ret = 0;
    YangConnOptionT tsConnOptions = {0};
    tsConnOptions.isCsMode = true;
    tsConnOptions.serverLocator = g_connServerTsdb;
    ret = TestYangGmcConnect(conn_ts, stmt_ts, 0, &tsConnOptions);
    return ret;
}

// 获取当前表空间大小
int TestGetSpaceInfoCnt(int *value)
{
    char command[1024] = {0};
    int ret = snprintf(command, 1024,
        "gmsysview -sql \"select * from 'V\\$STORAGE_SPACE_INFO'\" -s %s"
        "| grep index | wc -l;",
        g_connServerTsdb);
    if (ret <= 0) {
        return FAILED;
    }
    ret = TestGetResultCommand(command, value);
    if (ret) {
        return FAILED;
    }
    return 0;
}

int DropTable(GmcStmtT *stmt, char *tableName)
{
    int ret = 0;
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "drop table %s;", tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    if (ret != 0) {
        AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNDEFINED_TABLE);
        return ret;
    }
    // 删表变成异步进程，需等待删表结束后才能再建表
    int spaceCount = 0;
    int spaceCountBefore = 0;
    ret = TestGetSpaceInfoCnt(&spaceCountBefore);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        ret = TestGetSpaceInfoCnt(&spaceCount);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (spaceCount == spaceCountBefore) {
            AW_FUN_Log(LOG_DEBUG, "spaceCount is %d\n", spaceCount);
            usleep(200000);
        } else {
            break;
        }
    }
    return 0;
}

void StopTsServer()
{
    system("kill -9 $(pidof gmserver_ts)");
}

void RecoverTsDisk()
{
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts recover");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
}

void RecoverTsini()
{
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts recover");
}

void StartTsServer()
{
    char sysCmd[128] = {0};
    (void)sprintf(sysCmd, "cd %s;gmserver_ts -b -p /usr/local/file/gmserver_ts.ini > /dev/null 2>&1", dir);
    system(sysCmd);
}

int CreateTable(GmcStmtT *stmt, char *tableName)
{
    int ret = 0;
    char sqlCmd[512] = {0};
    (void)DropTable(stmt, tableName);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNDEFINED_TABLE);
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(160),"
        " description text)with (time_col = 'time', interval = '1 hour', disk_limit = '50 MB',"
        " compression = 'fast(rapidlz)');",
        tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    return ret;
}

// 行式存储,注入顺序数据
int rowInsertData(GmcStmtT *stmt, char *tableName, int64_t dataCount)
{
    int ret = 0;
    int64_t count = dataCount;
    int64_t *id;
    int64_t *time;
    // 申请内存
    char *nameList = (char *)malloc(count * 64);
    if (nameList == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    char *ipList = (char *)malloc(count * 33);
    if (ipList == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    char **blobList = (char **)malloc(count * sizeof(char *));
    if (blobList == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    for (int i = 0; i < count; i++) {
        blobList[i] = (char *)malloc(160 * sizeof(char));
        (void)memset(blobList[i], 0, 160);
    }
    id = (int64_t *)malloc(sizeof(int64_t) * count);
    if (id == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    time = (int64_t *)malloc(sizeof(int64_t) * count);
    if (time == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    // 构造数据
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 915148800 + i;
        int j = i % 20;
        memcpy((ipList + i * 33), (char *)g_ips[j], 33);
        memcpy((nameList + i * 64), (char *)g_names[j], 64);
        blobList[i] = g_message[j];
    }
    char *description[20] = {0};
    for (int i = 0; i < 20; ++i) {
        description[i] = g_desText[i];
    }
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    RETURN_IFERR(ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int64_t));
    RETURN_IFERR(ret);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, 0, 0);
    RETURN_IFERR(ret);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, time, 0, 0);
    RETURN_IFERR(ret);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_FIXED, nameList, 64, NULL);
    RETURN_IFERR(ret);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_FIXED, ipList, 33, NULL);
    RETURN_IFERR(ret);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_BYTES, blobList, sizeof(blobList[0]), NULL);
    RETURN_IFERR(ret);
    ret = GmcExecute(stmt);
    // 释放内存
    free(id);
    free(time);
    free(nameList);
    free(ipList);
    free(blobList);
    return ret;
}

// 服务端进程退出
int32_t GMKill(GmcConnT **conn, GmcStmtT **stmt)
{
    sleep(1);
    GmcUnInit();
    system("kill -9 $(pidof gmserver_ts)");
    int ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh -ts");
    RETURN_IFERR(ret);
    GmcInit();
    ret = CreateTsConnect(conn, stmt);
    RETURN_IFERR(ret);
    return ret;
}

typedef struct {
    int64_t *ids;
    int64_t *times;
    char (*names)[64];
    char (*ips)[33];
    char (*blobs)[160];
    char (*texts)[128];
    int64_t dataCount;
} QueryData;

void checkQueryData(GmcStmtT *stmt, QueryData querydata)
{
    // 查询过滤后的数据是否正确
    bool eof = false;
    bool isNull = false;
    int64_t cId = 0;
    int64_t cTime = 0;
    char cIp[33] = {0};
    char cName[64] = {0};
    char cBlob[160] = {0};
    char cText[128] = {0};
    uint32_t sizeInt = sizeof(int64_t);
    uint32_t sizeCharIp = sizeof(cIp);
    uint32_t sizeCharNmae = sizeof(cName);
    uint32_t sizeCharBolb = sizeof(cBlob);
    uint32_t sizeCharText = sizeof(cText);
    int fetchTimes = 0;
    int64_t count = 0;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (eof) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &cId, &sizeInt, &isNull);
        AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_ASSERT_EQ_INT(querydata.ids[fetchTimes], cId);
        count = cId % 20;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &cTime, &sizeInt, &isNull);
        AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_ASSERT_EQ_INT(querydata.times[fetchTimes], cTime);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &cName, &sizeCharNmae, &isNull);
        AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_ASSERT_EQ_STR((const char *)querydata.names[count], cName);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &cIp, &sizeCharIp, &isNull);
        AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_ASSERT_EQ_STR((const char *)querydata.ips[count], cIp);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &cBlob, &sizeCharBolb, &isNull);
        AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_ASSERT_EQ_STR((const char *)querydata.blobs[count], cBlob);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 5, &cText, &sizeCharText, &isNull);
        AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_ASSERT_EQ_STR((const char *)querydata.texts[count], cText);
        fetchTimes++;
    }
    AW_MACRO_ASSERT_EQ_INT(fetchTimes, querydata.dataCount);
}

// 036.无盘启动，迁入目录下有与迁出目录表结构不同的持久化文件，interval配置项不同
// 预期：启动成功,不校验配置项
TEST_F(TsdbMetadataMigration, Timing_060_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    StopTsServer();
    RecoverTsDisk();
    StartTsServer();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 无盘启动服务会等待，此时建连失败
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(3);
    system("mkdir -p ./data/gmdb2");
    ret = GmcSetPersistPath(g_dataFilePath2, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    g_dataSize1Before = GetDirSize(g_dataFilePath2);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(1);
    g_dataSize1After = GetDirSize(g_dataFilePath2);
    AW_MACRO_ASSERT_NE_INT(g_dataSize1Before, g_dataSize1After);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 导出持久化文件至迁入目录
    ret = system("mkdir -p ./data/gmdb3");
    EXPECT_EQ(ret, GMERR_OK);
    char syscmd[512] = {0};
    (void)sprintf(syscmd, "gmexport -c systbl_data -f ./data/gmdb3 -s %s", g_connServerTsdb);
    ret = system(syscmd);
    EXPECT_EQ(ret, GMERR_OK);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 删表变成异步进程，需等待删表结束后才能再建表
    int spaceCount = 0;
    ret = TestGetSpaceInfoCnt(&spaceCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        if (spaceCount == 4) {
            AW_FUN_Log(LOG_DEBUG, "spaceCount is %d\n", spaceCount);
            usleep(200000);
            ret = TestGetSpaceInfoCnt(&spaceCount);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(3, spaceCount);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(160),"
        " description text)with (time_col = 'time', interval = '1 day', disk_limit = '50 MB',"
        " compression = 'fast(rapidlz)');",
        g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = system("cp -rf ./data/gmdb2/*  ./data/gmdb3");
    EXPECT_EQ(ret, GMERR_OK);
    sleep(3);

    ret = GmcSetPersistPath(g_dataFilePath3, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_dataSize1Before = GetDirSize(g_dataFilePath3);
    GmcUnInit();
    system("kill -9 $(pidof gmserver_ts)");
    int ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh -ts");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcInit();
    sleep(3);
    g_dataSize1After = GetDirSize(g_dataFilePath3);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char queryCommand[128] = {0};
    (void)sprintf(queryCommand, "select * from %s ", g_tableName);
    uint32_t sqlStateLen = strlen(queryCommand);
    ret = GmcExecDirect(stmt, queryCommand, sqlStateLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    int64_t dataCount = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, 0);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 037.无盘启动，迁入目录下有与迁出目录表结构不同的持久化文件，disk_limit配置项不同
// 预期：启动成功,不校验配置项
TEST_F(TsdbMetadataMigration, Timing_060_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    StopTsServer();
    RecoverTsDisk();
    StartTsServer();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 无盘启动服务会等待，此时建连失败
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(3);
    system("mkdir -p ./data/gmdb2");
    ret = GmcSetPersistPath(g_dataFilePath2, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    g_dataSize1Before = GetDirSize(g_dataFilePath2);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(1);
    g_dataSize1After = GetDirSize(g_dataFilePath2);
    AW_MACRO_ASSERT_NE_INT(g_dataSize1Before, g_dataSize1After);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 导出持久化文件至迁入目录
    ret = system("mkdir -p ./data/gmdb3");
    EXPECT_EQ(ret, GMERR_OK);
    char syscmd[512] = {0};
    (void)sprintf(syscmd, "gmexport -c systbl_data -f ./data/gmdb3 -s %s", g_connServerTsdb);
    ret = system(syscmd);
    EXPECT_EQ(ret, GMERR_OK);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(160),"
        " description text)with (time_col = 'time', interval = '1 hour', disk_limit = '500 MB',"
        " compression = 'fast(rapidlz)');",
        g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = system("cp -rf ./data/gmdb2/*  ./data/gmdb3");
    EXPECT_EQ(ret, GMERR_OK);
    sleep(3);

    ret = GmcSetPersistPath(g_dataFilePath3, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_dataSize1Before = GetDirSize(g_dataFilePath3);
    GmcUnInit();
    system("kill -9 $(pidof gmserver_ts)");
    int ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh -ts");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcInit();
    sleep(3);
    g_dataSize1After = GetDirSize(g_dataFilePath3);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char queryCommand[128] = {0};
    (void)sprintf(queryCommand, "select * from %s ", g_tableName);
    uint32_t sqlStateLen = strlen(queryCommand);
    ret = GmcExecDirect(stmt, queryCommand, sqlStateLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    int64_t dataCount = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, 0);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 038.无盘启动，迁入目录下有与迁出目录表结构不同的持久化文件，ttl配置项不同
// 预期：启动成功,不校验配置项
TEST_F(TsdbMetadataMigration, Timing_060_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    StopTsServer();
    RecoverTsDisk();
    StartTsServer();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 无盘启动服务会等待，此时建连失败
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(3);
    system("mkdir -p ./data/gmdb2");
    ret = GmcSetPersistPath(g_dataFilePath2, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    g_dataSize1Before = GetDirSize(g_dataFilePath2);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(1);
    g_dataSize1After = GetDirSize(g_dataFilePath2);
    AW_MACRO_ASSERT_NE_INT(g_dataSize1Before, g_dataSize1After);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 导出持久化文件至迁入目录
    ret = system("mkdir -p ./data/gmdb3");
    EXPECT_EQ(ret, GMERR_OK);
    char syscmd[512] = {0};
    (void)sprintf(syscmd, "gmexport -c systbl_data -f ./data/gmdb3 -s %s", g_connServerTsdb);
    ret = system(syscmd);
    EXPECT_EQ(ret, GMERR_OK);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(160),"
        " description text)with (time_col = 'time', interval = '1 hour', disk_limit = '50 MB', ttl = '100 hours',"
        " compression = 'fast(rapidlz)');",
        g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = system("cp -rf ./data/gmdb2/*  ./data/gmdb3");
    EXPECT_EQ(ret, GMERR_OK);
    sleep(3);

    ret = GmcSetPersistPath(g_dataFilePath3, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_dataSize1Before = GetDirSize(g_dataFilePath3);
    GmcUnInit();
    system("kill -9 $(pidof gmserver_ts)");
    int ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh -ts");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcInit();
    sleep(3);
    g_dataSize1After = GetDirSize(g_dataFilePath3);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char queryCommand[128] = {0};
    (void)sprintf(queryCommand, "select * from %s ", g_tableName);
    uint32_t sqlStateLen = strlen(queryCommand);
    ret = GmcExecDirect(stmt, queryCommand, sqlStateLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    int64_t dataCount = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, 0);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 039.无盘启动，迁入目录下有与迁出目录表结构不同的持久化文件，compression配置项不同
// 预期：启动成功,不校验配置项
TEST_F(TsdbMetadataMigration, Timing_060_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    StopTsServer();
    RecoverTsDisk();
    StartTsServer();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 无盘启动服务会等待，此时建连失败
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(3);
    system("mkdir -p ./data/gmdb2");
    ret = GmcSetPersistPath(g_dataFilePath2, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    g_dataSize1Before = GetDirSize(g_dataFilePath2);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(1);
    g_dataSize1After = GetDirSize(g_dataFilePath2);
    AW_MACRO_ASSERT_NE_INT(g_dataSize1Before, g_dataSize1After);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 导出持久化文件至迁入目录
    ret = system("mkdir -p ./data/gmdb3");
    EXPECT_EQ(ret, GMERR_OK);
    char syscmd[512] = {0};
    (void)sprintf(syscmd, "gmexport -c systbl_data -f ./data/gmdb3 -s %s", g_connServerTsdb);
    ret = system(syscmd);
    EXPECT_EQ(ret, GMERR_OK);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(160),"
        " description text)with (time_col = 'time', interval = '1 hour', disk_limit = '50 MB',"
        " compression = 'fast');",
        g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = system("cp -rf ./data/gmdb2/*  ./data/gmdb3");
    EXPECT_EQ(ret, GMERR_OK);
    sleep(3);

    ret = GmcSetPersistPath(g_dataFilePath3, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_dataSize1Before = GetDirSize(g_dataFilePath3);
    GmcUnInit();
    system("kill -9 $(pidof gmserver_ts)");
    int ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh -ts");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcInit();
    sleep(3);
    g_dataSize1After = GetDirSize(g_dataFilePath3);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char queryCommand[128] = {0};
    (void)sprintf(queryCommand, "select * from %s ", g_tableName);
    uint32_t sqlStateLen = strlen(queryCommand);
    ret = GmcExecDirect(stmt, queryCommand, sqlStateLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    int64_t dataCount = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, 0);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 040.无盘启动，迁入目录下有与迁出目录表结构不同的持久化文件，table_path配置项不同
// 预期：启动成功,不校验配置项
TEST_F(TsdbMetadataMigration, Timing_060_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    StopTsServer();
    RecoverTsDisk();
    StartTsServer();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 无盘启动服务会等待，此时建连失败
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(3);
    system("mkdir -p ./data/gmdb2");
    ret = GmcSetPersistPath(g_dataFilePath2, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    g_dataSize1Before = GetDirSize(g_dataFilePath2);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(1);
    g_dataSize1After = GetDirSize(g_dataFilePath2);
    AW_MACRO_ASSERT_NE_INT(g_dataSize1Before, g_dataSize1After);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 导出持久化文件至迁入目录
    ret = system("mkdir -p ./data/gmdb3");
    EXPECT_EQ(ret, GMERR_OK);
    char syscmd[512] = {0};
    (void)sprintf(syscmd, "gmexport -c systbl_data -f ./data/gmdb3 -s %s", g_connServerTsdb);
    ret = system(syscmd);
    EXPECT_EQ(ret, GMERR_OK);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(160),"
        " description text)with (time_col = 'time', interval = '1 hour', disk_limit = '50 MB',"
        " compression = 'fast(rapidlz)', table_path = '/data/gmdb/');",
        g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = system("cp -rf ./data/gmdb2/*  ./data/gmdb3");
    EXPECT_EQ(ret, GMERR_OK);
    sleep(3);

    ret = GmcSetPersistPath(g_dataFilePath3, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_dataSize1Before = GetDirSize(g_dataFilePath3);
    GmcUnInit();
    system("kill -9 $(pidof gmserver_ts)");
    int ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh -ts");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcInit();
    sleep(3);
    g_dataSize1After = GetDirSize(g_dataFilePath3);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char queryCommand[128] = {0};
    (void)sprintf(queryCommand, "select * from %s ", g_tableName);
    uint32_t sqlStateLen = strlen(queryCommand);
    ret = GmcExecDirect(stmt, queryCommand, sqlStateLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    int64_t dataCount = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, 0);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 041.无盘启动，启动成功后，导入持久化文件至迁出目录，重启后新建同名表
// 预期：重启后新建同名表失败
TEST_F(TsdbMetadataMigration, Timing_060_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    StopTsServer();
    RecoverTsDisk();
    StartTsServer();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 无盘启动服务会等待，此时建连失败
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(3);
    system("mkdir -p ./data/gmdb2");
    ret = GmcSetPersistPath(g_dataFilePath2, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    g_dataSize1Before = GetDirSize(g_dataFilePath2);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(1);
    g_dataSize1After = GetDirSize(g_dataFilePath2);
    AW_MACRO_ASSERT_NE_INT(g_dataSize1Before, g_dataSize1After);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 导出持久化文件至迁入目录
    ret = system("mkdir -p ./data/gmdb3");
    EXPECT_EQ(ret, GMERR_OK);
    char syscmd[512] = {0};
    (void)sprintf(syscmd, "gmexport -c systbl_data -f ./data/gmdb3 -s %s", g_connServerTsdb);
    ret = system(syscmd);
    EXPECT_EQ(ret, GMERR_OK);
    sleep(3);

    ret = GmcSetPersistPath(g_dataFilePath3, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_dataSize1Before = GetDirSize(g_dataFilePath3);
    GmcUnInit();
    system("kill -9 $(pidof gmserver_ts)");
    int ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh -ts");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcInit();
    sleep(3);
    g_dataSize1After = GetDirSize(g_dataFilePath3);
    AW_MACRO_ASSERT_NE_INT(g_dataSize1Before, g_dataSize1After);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char queryCommand[128] = {0};
    (void)sprintf(queryCommand, "select * from %s ", g_tableName);
    uint32_t sqlStateLen = strlen(queryCommand);
    ret = GmcExecDirect(stmt, queryCommand, sqlStateLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    int64_t dataCount = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, 0);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(160),"
        " description text)with (time_col = 'time', interval = '1 hour', disk_limit = '50 MB',"
        " compression = 'fast(rapidlz)');",
        g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DUPLICATE_TABLE, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_DUPLICATE_TABLE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 042.无盘启动，启动成功后，导入持久化文件至迁出目录，重启后对同名表新增同名列
// 预期：新增同名列失败
TEST_F(TsdbMetadataMigration, Timing_060_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    StopTsServer();
    RecoverTsDisk();
    StartTsServer();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 无盘启动服务会等待，此时建连失败
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(3);
    system("mkdir -p ./data/gmdb2");
    ret = GmcSetPersistPath(g_dataFilePath2, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    g_dataSize1Before = GetDirSize(g_dataFilePath2);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(1);
    g_dataSize1After = GetDirSize(g_dataFilePath2);
    AW_MACRO_ASSERT_NE_INT(g_dataSize1Before, g_dataSize1After);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 导出持久化文件至迁入目录
    ret = system("mkdir -p ./data/gmdb3");
    EXPECT_EQ(ret, GMERR_OK);
    char syscmd[512] = {0};
    (void)sprintf(syscmd, "gmexport -c systbl_data -f ./data/gmdb3 -s %s", g_connServerTsdb);
    ret = system(syscmd);
    EXPECT_EQ(ret, GMERR_OK);
    sleep(3);

    ret = GmcSetPersistPath(g_dataFilePath3, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_dataSize1Before = GetDirSize(g_dataFilePath3);
    GmcUnInit();
    system("kill -9 $(pidof gmserver_ts)");
    int ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh -ts");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcInit();
    sleep(3);
    g_dataSize1After = GetDirSize(g_dataFilePath3);
    AW_MACRO_ASSERT_NE_INT(g_dataSize1Before, g_dataSize1After);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char queryCommand[128] = {0};
    (void)sprintf(queryCommand, "select * from %s ", g_tableName);
    uint32_t sqlStateLen = strlen(queryCommand);
    ret = GmcExecDirect(stmt, queryCommand, sqlStateLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    int64_t dataCount = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, 0);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "alter table %s add id integer;", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DUPLICATE_COLUMN, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_DUPLICATE_COLUMN);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 043.无盘启动，启动成功后，导入持久化文件至迁出目录，重启后对同名表新增其他列
// 预期：新增其他列成功
TEST_F(TsdbMetadataMigration, Timing_060_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    StopTsServer();
    RecoverTsDisk();
    StartTsServer();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 无盘启动服务会等待，此时建连失败
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(3);
    system("mkdir -p ./data/gmdb2");
    ret = GmcSetPersistPath(g_dataFilePath2, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    g_dataSize1Before = GetDirSize(g_dataFilePath2);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(1);
    g_dataSize1After = GetDirSize(g_dataFilePath2);
    AW_MACRO_ASSERT_NE_INT(g_dataSize1Before, g_dataSize1After);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 导出持久化文件至迁入目录
    ret = system("mkdir -p ./data/gmdb3");
    EXPECT_EQ(ret, GMERR_OK);
    char syscmd[512] = {0};
    (void)sprintf(syscmd, "gmexport -c systbl_data -f ./data/gmdb3 -s %s", g_connServerTsdb);
    ret = system(syscmd);
    EXPECT_EQ(ret, GMERR_OK);
    sleep(3);

    ret = GmcSetPersistPath(g_dataFilePath3, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_dataSize1Before = GetDirSize(g_dataFilePath3);
    GmcUnInit();
    system("kill -9 $(pidof gmserver_ts)");
    int ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh -ts");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcInit();
    sleep(3);
    g_dataSize1After = GetDirSize(g_dataFilePath3);
    AW_MACRO_ASSERT_NE_INT(g_dataSize1Before, g_dataSize1After);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char queryCommand[128] = {0};
    (void)sprintf(queryCommand, "select * from %s ", g_tableName);
    uint32_t sqlStateLen = strlen(queryCommand);
    ret = GmcExecDirect(stmt, queryCommand, sqlStateLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    int64_t dataCount = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, 0);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "alter table %s add number integer;", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 044.无盘启动，启动成功后，导入持久化文件至迁出目录，重启后对同名表变更disk_limit
// 预期：变更disk_limit成功
TEST_F(TsdbMetadataMigration, Timing_060_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    StopTsServer();
    RecoverTsDisk();
    StartTsServer();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 无盘启动服务会等待，此时建连失败
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(3);
    system("mkdir -p ./data/gmdb2");
    ret = GmcSetPersistPath(g_dataFilePath2, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    g_dataSize1Before = GetDirSize(g_dataFilePath2);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(1);
    g_dataSize1After = GetDirSize(g_dataFilePath2);
    AW_MACRO_ASSERT_NE_INT(g_dataSize1Before, g_dataSize1After);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 导出持久化文件至迁入目录
    ret = system("mkdir -p ./data/gmdb3");
    EXPECT_EQ(ret, GMERR_OK);
    char syscmd[512] = {0};
    (void)sprintf(syscmd, "gmexport -c systbl_data -f ./data/gmdb3 -s %s", g_connServerTsdb);
    ret = system(syscmd);
    EXPECT_EQ(ret, GMERR_OK);
    sleep(3);

    ret = GmcSetPersistPath(g_dataFilePath3, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_dataSize1Before = GetDirSize(g_dataFilePath3);
    GmcUnInit();
    system("kill -9 $(pidof gmserver_ts)");
    int ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh -ts");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcInit();
    sleep(3);
    g_dataSize1After = GetDirSize(g_dataFilePath3);
    AW_MACRO_ASSERT_NE_INT(g_dataSize1Before, g_dataSize1After);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char queryCommand[128] = {0};
    (void)sprintf(queryCommand, "select * from %s ", g_tableName);
    uint32_t sqlStateLen = strlen(queryCommand);
    ret = GmcExecDirect(stmt, queryCommand, sqlStateLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    int64_t dataCount = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, 0);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "alter table %s set (disk_limit = ' 50 MB ')", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 045.无盘启动，启动成功后，导入持久化文件至迁出目录，重启后对同名表执行主动老化函数
// 预期：主动老化成功
TEST_F(TsdbMetadataMigration, Timing_060_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    StopTsServer();
    RecoverTsDisk();
    StartTsServer();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 无盘启动服务会等待，此时建连失败
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(3);
    system("mkdir -p ./data/gmdb2");
    ret = GmcSetPersistPath(g_dataFilePath2, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    g_dataSize1Before = GetDirSize(g_dataFilePath2);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(1);
    g_dataSize1After = GetDirSize(g_dataFilePath2);
    AW_MACRO_ASSERT_NE_INT(g_dataSize1Before, g_dataSize1After);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 导出持久化文件至迁入目录
    ret = system("mkdir -p ./data/gmdb3");
    EXPECT_EQ(ret, GMERR_OK);
    char syscmd[512] = {0};
    (void)sprintf(syscmd, "gmexport -c systbl_data -f ./data/gmdb3 -s %s", g_connServerTsdb);
    ret = system(syscmd);
    EXPECT_EQ(ret, GMERR_OK);
    // 复制持久化文件至迁入目录
    ret = system("cp -rf ./data/gmdb2/*  ./data/gmdb3");
    EXPECT_EQ(ret, GMERR_OK);
    sleep(3);

    ret = GmcSetPersistPath(g_dataFilePath3, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_dataSize1Before = GetDirSize(g_dataFilePath3);
    GmcUnInit();
    system("kill -9 $(pidof gmserver_ts)");
    int ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh -ts");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcInit();
    sleep(3);
    g_dataSize1After = GetDirSize(g_dataFilePath3);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char queryCommand[128] = {0};
    (void)sprintf(queryCommand, "select * from %s ", g_tableName);
    uint32_t sqlStateLen = strlen(queryCommand);
    ret = GmcExecDirect(stmt, queryCommand, sqlStateLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    int64_t dataCount = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, 10);

    (void)sprintf(queryCommand, "SELECT tsdb_aging('%s');", g_tableName);
    sqlStateLen = strlen(queryCommand);
    ret = GmcExecDirect(stmt, queryCommand, sqlStateLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);

    (void)sprintf(queryCommand, "select * from %s ", g_tableName);
    sqlStateLen = strlen(queryCommand);
    ret = GmcExecDirect(stmt, queryCommand, sqlStateLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    dataCount = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, 0);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 行式存储,注入顺序数据，时间列递增
int rowInsertDataTimeAdd(GmcStmtT *stmt, char *tableName, int64_t dataCount, int64_t loopNum)
{
    int ret = 0;
    int64_t count = dataCount;
    int64_t *id;
    int64_t *time;
    // 申请内存
    char *nameList = (char *)malloc(count * 64);
    if (nameList == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    char *ipList = (char *)malloc(count * 33);
    if (ipList == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    char **blobList = (char **)malloc(count * sizeof(char *));
    if (blobList == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    for (int i = 0; i < count; i++) {
        blobList[i] = (char *)malloc(160 * sizeof(char));
        (void)memset(blobList[i], 0, 160);
    }
    id = (int64_t *)malloc(sizeof(int64_t) * count);
    if (id == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    time = (int64_t *)malloc(sizeof(int64_t) * count);
    if (time == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    for (int j = 0; j < loopNum; j++) {
        // 构造数据
        for (int i = 0; i < count; i++) {
            id[i] = i;
            time[i] = 915148800 + i + j * 3660; // 构造每批数据比前一批次递增
            int j = i % 20;
            memcpy((ipList + i * 33), (char *)g_ips[j], 33);
            memcpy((nameList + i * 64), (char *)g_names[j], 64);
            // 释放内存时不能直接赋值，需要复制后才能释放
            (void)strcpy(blobList[i], g_message[j]);
        }
        char *description[20] = {0};
        for (int i = 0; i < 20; ++i) {
            description[i] = g_desText[i];
        }
        ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
        RETURN_IFERR(ret);
        ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int64_t));
        RETURN_IFERR(ret);
        ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, 0, 0);
        RETURN_IFERR(ret);
        ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, time, 0, 0);
        RETURN_IFERR(ret);
        ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_FIXED, nameList, 64, NULL);
        RETURN_IFERR(ret);
        ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_FIXED, ipList, 33, NULL);
        RETURN_IFERR(ret);
        ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_BYTES, blobList, sizeof(blobList[0]), NULL);
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
    }
    
    // 释放内存
    free(id);
    free(time);
    free(nameList);
    free(ipList);
    for (int i = 0; i < count; i++) {
        free(blobList[i]);
    }
    free(blobList);
    return ret;
}

// 046.无盘启动，启动成功后，导入持久化文件至迁出目录，重启后对同名表注入超过disk_limit限制的数据
// 预期：插入数据后会触发disk_limit清理线程
TEST_F(TsdbMetadataMigration, Timing_060_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    StopTsServer();
    RecoverTsDisk();
    StartTsServer();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 无盘启动服务会等待，此时建连失败
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(3);
    system("mkdir -p ./data/gmdb2");
    ret = GmcSetPersistPath(g_dataFilePath2, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    g_dataSize1Before = GetDirSize(g_dataFilePath2);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(1);
    g_dataSize1After = GetDirSize(g_dataFilePath2);
    AW_MACRO_ASSERT_NE_INT(g_dataSize1Before, g_dataSize1After);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)DropTable(stmt, g_tableName);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNDEFINED_TABLE);
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(160),"
        " description text)with (time_col = 'time', interval = '1 hour', disk_limit = '5 MB',"
        " compression = 'fast(rapidlz)');",
        g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 导出持久化文件至迁入目录
    ret = system("mkdir -p ./data/gmdb3");
    EXPECT_EQ(ret, GMERR_OK);
    char syscmd[512] = {0};
    (void)sprintf(syscmd, "gmexport -c systbl_data -f ./data/gmdb3 -s %s", g_connServerTsdb);
    ret = system(syscmd);
    EXPECT_EQ(ret, GMERR_OK);
    // 复制持久化文件至迁入目录
    ret = system("cp -rf ./data/gmdb2/*  ./data/gmdb3");
    EXPECT_EQ(ret, GMERR_OK);
    sleep(3);

    ret = GmcSetPersistPath(g_dataFilePath3, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_dataSize1Before = GetDirSize(g_dataFilePath3);
    GmcUnInit();
    system("kill -9 $(pidof gmserver_ts)");
    int ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh -ts");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcInit();
    sleep(3);
    g_dataSize1After = GetDirSize(g_dataFilePath3);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char queryCommand[128] = {0};
    (void)sprintf(queryCommand, "select * from %s where id < 11;", g_tableName);
    uint32_t sqlStateLen = strlen(queryCommand);
    ret = GmcExecDirect(stmt, queryCommand, sqlStateLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    int64_t dataCount = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, 10);
    // 构造时间列递增，避免后续查询时disklimit清理线程未完成导致查询失败
    ret = rowInsertDataTimeAdd(stmt, g_tableName, 50000, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)sprintf(queryCommand, "select * from %s where id < 11;", g_tableName);
    sqlStateLen = strlen(queryCommand);
    ret = GmcExecDirect(stmt, queryCommand, sqlStateLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    dataCount = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, 0);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 047.无盘启动，启动成功后，导入持久化文件至迁出目录，重启后对同名表进行insert into操作
// 预期：insert into成功
TEST_F(TsdbMetadataMigration, Timing_060_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    StopTsServer();
    RecoverTsDisk();
    StartTsServer();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 无盘启动服务会等待，此时建连失败
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(3);
    system("mkdir -p ./data/gmdb2");
    ret = GmcSetPersistPath(g_dataFilePath2, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    g_dataSize1Before = GetDirSize(g_dataFilePath2);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(1);
    g_dataSize1After = GetDirSize(g_dataFilePath2);
    AW_MACRO_ASSERT_NE_INT(g_dataSize1Before, g_dataSize1After);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName2, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 导出持久化文件至迁入目录
    ret = system("mkdir -p ./data/gmdb3");
    EXPECT_EQ(ret, GMERR_OK);
    char syscmd[512] = {0};
    (void)sprintf(syscmd, "gmexport -c systbl_data -f ./data/gmdb3 -s %s", g_connServerTsdb);
    ret = system(syscmd);
    EXPECT_EQ(ret, GMERR_OK);
    // 复制持久化文件至迁入目录
    ret = system("cp -rf ./data/gmdb2/*  ./data/gmdb3");
    EXPECT_EQ(ret, GMERR_OK);
    sleep(3);

    ret = GmcSetPersistPath(g_dataFilePath3, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_dataSize1Before = GetDirSize(g_dataFilePath3);
    GmcUnInit();
    system("kill -9 $(pidof gmserver_ts)");
    int ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh -ts");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcInit();
    sleep(3);
    g_dataSize1After = GetDirSize(g_dataFilePath3);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char queryCommand[128] = {0};
    char sqlCmd[128] = {0};
    // 执行insert into语句
    (void)sprintf(sqlCmd, "insert into %s select * from %s;", g_tableName, g_tableName2);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)sprintf(queryCommand, "select * from %s;", g_tableName);
    uint32_t sqlStateLen = strlen(queryCommand);
    ret = GmcExecDirect(stmt, queryCommand, sqlStateLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    int64_t dataCount = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, 20);

    AW_FUN_Log(LOG_STEP, "test end.");
}

bool isDataCsvExist()
{
    if (FILE *file = fopen("data.csv", "r")) {
        fclose(file);
        return true;
    }
    return false;
}

void GetCsvFileContent(char returnValue[])
{
    FILE *file = fopen("data.csv", "r");
    char row[128];
    while (fgets(row, 128, file) != NULL) {
        strcat(returnValue, row);
    }
    fclose(file);
}

// 048.无盘启动，启动成功后，导入持久化文件至迁出目录，重启后对同名表进行copy to操作
// 预期：copy to 成功
TEST_F(TsdbMetadataMigration, Timing_060_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf data.csv");
    StopTsServer();
    RecoverTsDisk();
    StartTsServer();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 无盘启动服务会等待，此时建连失败
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(3);
    system("mkdir -p ./data/gmdb2");
    ret = GmcSetPersistPath(g_dataFilePath2, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    g_dataSize1Before = GetDirSize(g_dataFilePath2);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(1);
    g_dataSize1After = GetDirSize(g_dataFilePath2);
    AW_MACRO_ASSERT_NE_INT(g_dataSize1Before, g_dataSize1After);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 导出持久化文件至迁入目录
    ret = system("mkdir -p ./data/gmdb3");
    EXPECT_EQ(ret, GMERR_OK);
    char syscmd[512] = {0};
    (void)sprintf(syscmd, "gmexport -c systbl_data -f ./data/gmdb3 -s %s", g_connServerTsdb);
    ret = system(syscmd);
    EXPECT_EQ(ret, GMERR_OK);
    // 复制持久化文件至迁入目录
    ret = system("cp -rf ./data/gmdb2/*  ./data/gmdb3");
    EXPECT_EQ(ret, GMERR_OK);
    sleep(3);

    ret = GmcSetPersistPath(g_dataFilePath3, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_dataSize1Before = GetDirSize(g_dataFilePath3);
    GmcUnInit();
    system("kill -9 $(pidof gmserver_ts)");
    int ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh -ts");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcInit();
    sleep(3);
    g_dataSize1After = GetDirSize(g_dataFilePath3);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char queryCommand[128] = {0};
    char sqlCmd[256] = {0};
    sprintf(sqlCmd,
        "COPY (SELECT id, time FROM testdb WHERE id <= 2 ) TO"
        "'%s/test/sdv/testcases/25_Timing/060_tsdb_supports_metadata_migration/data.csv';",
        dir);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, 256);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *expectContent = R"(0,915148800
1,915148801
2,915148802
)";
    // 检查csv文件是否生成
    AW_MACRO_ASSERT_EQ_INT(true, isDataCsvExist());
    char actualContent[1024] = {0};
    (void)GetCsvFileContent(actualContent);
    // 检查获取到内容是否正确
    AW_MACRO_ASSERT_EQ_STR(expectContent, actualContent);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 049.无盘启动，启动成功后，建pull方式流式表插入数据，导入持久化文件至迁出目录
// 预期：表存在数据不存在
TEST_F(TsdbMetadataMigration, Timing_060_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf data.csv");
    StopTsServer();
    RecoverTsDisk();
    StartTsServer();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 无盘启动服务会等待，此时建连失败
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 流失表逻辑不需要了
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 050.无盘启动，启动成功后，建pull方式流式表插入数据，导入持久化文件至迁出目录
// 预期：表存在数据不存在，重新插入数据后功能正常
TEST_F(TsdbMetadataMigration, Timing_060_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf data.csv");
    StopTsServer();
    RecoverTsDisk();
    StartTsServer();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 无盘启动服务会等待，此时建连失败
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

void SubCallback1(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userdata)
{
    uint32_t i = 0;
    int64_t cId = 0;
    int64_t cTime = 0;
    char cName[64] = {0};
    int64_t dataCount = 0;
    bool isNull = false;
    bool eof = false;
    uint32_t sizeChar = sizeof(cName);
    uint32_t sizeInt = sizeof(int64_t);

    while (true) {
        Status ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyById(subStmt, 0, &cId, sizeInt, &isNull);
        AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
        ret = GmcGetVertexPropertyById(subStmt, 1, &cTime, sizeInt, &isNull);
        AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
        ret = GmcGetVertexPropertyById(subStmt, 2, &cName, sizeChar, &isNull);
        AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
        dataCount++;
    }
    idArrays[g_winCnt] = dataCount;
    g_winCnt++;
    if (g_winCnt == fetchTimes) {
        g_testDataInserted = false;
    }
}

void checkData(int64_t *id)
{
    // 避免执行报错后陷入死循环
    int stopNum = 0;
    while (g_testDataInserted && stopNum <= 20) {
        sleep(1);
        stopNum++;
    }
    for (int i = 0; i < fetchTimes; i++) {
        AW_MACRO_ASSERT_EQ_INT(idArrays[i], id[i]);
    }
    AW_MACRO_ASSERT_EQ_INT(g_winCnt, fetchTimes);
    sleep(1);
}

// 051.无盘启动，启动成功后，建push方式流式表插入数据，导入持久化文件至迁出目录
// 预期：表存在数据不存在，重新插入数据后功能正常
TEST_F(TsdbMetadataMigration, Timing_060_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf data.csv");
    StopTsServer();
    RecoverTsDisk();
    StartTsServer();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 无盘启动服务会等待，此时建连失败
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 052.无盘启动，启动成功后，通过gmimport建静态表，导出持久化文件至迁入目录
// 预期：表存在数据不存在
TEST_F(TsdbMetadataMigration, Timing_060_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    StopTsServer();
    RecoverTsDisk();
    StartTsServer();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 无盘启动服务会等待，此时建连失败
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(3);
    system("mkdir -p ./data/gmdb2");
    ret = GmcSetPersistPath(g_dataFilePath2, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    g_dataSize1Before = GetDirSize(g_dataFilePath2);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(1);
    g_dataSize1After = GetDirSize(g_dataFilePath2);
    AW_MACRO_ASSERT_NE_INT(g_dataSize1Before, g_dataSize1After);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = system("rm -rf ./data/gmdb3/");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(160),"
        " description text)with (time_col = 'time', interval = '1 day', disk_limit = '50 MB',"
        " compression = 'fast(rapidlz)', table_path = '/data/gmdb/');",
        g_tableName);
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 导出持久化文件至迁入目录
    ret = system("mkdir -p ./data/gmdb");
    EXPECT_EQ(ret, GMERR_OK);
    char syscmd[512] = {0};
    (void)sprintf(syscmd, "gmexport -c systbl_data -f ./data/gmdb -s %s", g_connServerTsdb);
    ret = system(syscmd);
    EXPECT_EQ(ret, GMERR_OK);

    ret = system("cp -rf ./data/gmdb2/*  ./data/gmdb");
    EXPECT_EQ(ret, GMERR_OK);
    sleep(3);

    ret = GmcSetPersistPath(g_dataFilePath, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_dataSize1Before = GetDirSize(g_dataFilePath);
    GmcUnInit();
    system("kill -9 $(pidof gmserver_ts)");
    int ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh -ts");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcInit();

    sleep(3);
    g_dataSize1After = GetDirSize(g_dataFilePath);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char queryCommand[128] = {0};
    (void)sprintf(queryCommand, "select * from %s ", g_tableName);
    uint32_t sqlStateLen = strlen(queryCommand);
    ret = GmcExecDirect(stmt, queryCommand, sqlStateLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    int64_t dataCount = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, 10);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 053.无盘启动，启动成功后，通过gmimport建静态表，table_path不存在。导出持久化文件至迁入目录
// 预期：表存在数据不存在
TEST_F(TsdbMetadataMigration, Timing_060_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    StopTsServer();
    RecoverTsDisk();
    StartTsServer();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 无盘启动服务会等待，此时建连失败
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(3);
    system("mkdir -p ./data/gmdb2");
    ret = GmcSetPersistPath(g_dataFilePath2, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    g_dataSize1Before = GetDirSize(g_dataFilePath2);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(1);
    g_dataSize1After = GetDirSize(g_dataFilePath2);
    AW_MACRO_ASSERT_NE_INT(g_dataSize1Before, g_dataSize1After);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = system("rm -rf ./data/gmdb3/");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(160),"
        " description text)with (time_col = 'time', interval = '1 day', disk_limit = '50 MB',"
        " compression = 'fast(rapidlz)', table_path = '/data/gmdb/');",
        g_tableName);
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 导出持久化文件至迁入目录
    ret = system("mkdir -p ./data/gmdb");
    EXPECT_EQ(ret, GMERR_OK);
    char syscmd[512] = {0};
    (void)sprintf(syscmd, "gmexport -c systbl_data -f ./data/gmdb -s %s", g_connServerTsdb);
    ret = system(syscmd);
    EXPECT_EQ(ret, GMERR_OK);

    ret = system("cp -rf ./data/gmdb2/*  ./data/gmdb");
    EXPECT_EQ(ret, GMERR_OK);
    sleep(3);

    ret = GmcSetPersistPath(g_dataFilePath, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("rm -rf /data/gmdb/");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcUnInit();
    system("kill -9 $(pidof gmserver_ts)");
    int ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh -ts");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcInit();

    sleep(3);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_CONNECTION_FAILURE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 054.无盘启动，迁入目录下有与迁出目录表字段相同但是顺序不同的同名表
// 预期：表存在数据存在
TEST_F(TsdbMetadataMigration, Timing_060_054)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    StopTsServer();
    RecoverTsDisk();
    StartTsServer();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 无盘启动服务会等待，此时建连失败
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(3);
    system("mkdir -p ./data/gmdb2");
    ret = GmcSetPersistPath(g_dataFilePath2, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    g_dataSize1Before = GetDirSize(g_dataFilePath2);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(1);
    g_dataSize1After = GetDirSize(g_dataFilePath2);
    AW_MACRO_ASSERT_NE_INT(g_dataSize1Before, g_dataSize1After);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName2, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 插入数据之后将持久化文件复制到迁入目录
    ret = system("mkdir -p ./data/gmdb3/");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("cp -rf ./data/gmdb2/*  ./data/gmdb3/");
    EXPECT_EQ(ret, GMERR_OK);

    char sqlCmd[512] = {0};
    (void)DropTable(stmt, g_tableName);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNDEFINED_TABLE);
    (void)sprintf(sqlCmd,
        "create table %s(id integer, name char(64), time integer, ip inet, message blob(160),"
        " description text)with (time_col = 'time', interval = '1 hour', disk_limit = '50 MB',"
        " compression = 'fast(rapidlz)');",
        g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char syscmd[512] = {0};
    (void)sprintf(syscmd, "gmexport -c systbl_data -f ./data/gmdb3 -s %s", g_connServerTsdb);
    ret = system(syscmd);
    EXPECT_EQ(ret, GMERR_OK);
    sleep(3);

    ret = GmcSetPersistPath(g_dataFilePath3, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcUnInit();
    system("kill -9 $(pidof gmserver_ts)");
    int ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh -ts");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcInit();
    sleep(3);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_CONNECTION_FAILURE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 055.无盘启动，拉起服务后第30秒时写入有效路径。等待3秒后写入不同的有效路径，
// 关闭服务,迁入目录写入.db文件，导出持久化文件至迁入目录后启动
// 预期：第一次启动成功，第二次启动成功
TEST_F(TsdbMetadataMigration, Timing_060_055)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    StopTsServer();
    RecoverTsDisk();
    StartTsServer();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 无盘启动服务会等待，此时建连失败
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(3);
    system("mkdir -p ./data/gmdb2");
    ret = GmcSetPersistPath(g_dataFilePath2, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    g_dataSize1Before = GetDirSize(g_dataFilePath2);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(1);
    g_dataSize1After = GetDirSize(g_dataFilePath2);
    AW_MACRO_ASSERT_NE_INT(g_dataSize1Before, g_dataSize1After);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("mkdir ./data/gmdb3");
    char syscmd[512] = {0};
    (void)sprintf(syscmd, "gmexport -c systbl_data -f ./data/gmdb3 -s %s", g_connServerTsdb);
    ret = system(syscmd);
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("cp -rf ./data/gmdb2/* ./data/gmdb3");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("cp Traffic_Org.db ./data/gmdb3");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(3);

    GmcUnInit();
    system("kill -9 $(pidof gmserver_ts)");
    int ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh -ts");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetPersistPath(g_dataFilePath3, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcInit();

    sleep(1);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char queryCommand[128] = {0};
    (void)sprintf(queryCommand, "select * from %s ", g_tableName);
    uint32_t sqlStateLen = strlen(queryCommand);
    ret = GmcExecDirect(stmt, queryCommand, sqlStateLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    int64_t dataCount = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, 0);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_CONNECTION_FAILURE);
    AW_FUN_Log(LOG_STEP, "test end.");
}
