/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description:【交付增强】DB支持持久化的元数据迁移
 * Author: chenbangjun
 * Create: 2024-10-30
 */
#include "gtest/gtest.h"
#include "t_rd_ts.h"
#include "t_datacom_lite.h"
#include "t_rd_common.h"

static GmcConnOptionsT *connOptions = NULL;
static GmcConnT *conn = NULL;
static GmcStmtT *stmt = NULL;
GmcStmtViewT *stmtView = NULL;
char g_cStoreDir[64] = {0};
char g_tableName[] = "testdb";
char g_tableName2[] = "testtable";
char g_viewName[] = "testdb_view";
static char g_dataFilePath[250] = {0};
static char g_dataFilePath2[250] = {0};
static char g_dataFilePath3[250] = {0};
Status ret = 0;
char *dir = getenv("GMDB_HOME");
bool isTheLogExists = false;
static GmcConnT *subConn = NULL;
static GmcStmtT *subStmt = NULL;
const char *g_subConnName = "subConnName";
const char *g_subName = "subQueryC1";

int32_t GetViewFieldResultValue(const char *viewName)
{
    sleep(3);
    int maxCmdSize = 256;
    char cmdOutput[maxCmdSize];
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));

    char command[maxCmdSize];
    (void)snprintf(command, maxCmdSize, "%s", viewName);

    FILE *pf = popen(command, "r");
    if (pf == NULL) {
        printf("popen(%s) error./n", command);
    }
    EXPECT_NE((void *)NULL, pf);
    while (NULL != fgets(cmdOutput, maxCmdSize, pf)) {
    };
    pclose(pf);

    return atoi(cmdOutput);
}

// 获取当前表空间大小
int TestGetSpaceInfoCnt(int *value)
{
    char command[1024] = {0};
    int ret = snprintf(command, 1024,
        "gmsysview -sql \"select * from 'V\\$STORAGE_SPACE_INFO'\" -s %s"
        "| grep index | wc -l;",
        g_connServerTsdb);
    if (ret <= 0) {
        return FAILED;
    }
    ret = TestGetResultCommand(command, value);
    if (ret) {
        return FAILED;
    }
    return 0;
}

char g_ips[20][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111", "33333333",
    "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
    "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
    "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
    "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
char g_names[20][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc", "lucy",
    "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
char g_message[20][160] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537", "0071 3521",
    "0010 0001", "", "3102 0022", "0000 0001", "9021 6538", "0071 3522", "0010 0002", "0010 0000", "3102 0023",
    "0000 0002", "9021 6539", "0071 3523", "0010 0003"};
char g_desText[20][128] = {"test data of the text type", "test", "data", "of", "the", "text", "type",
    "test data of the text type", "test data of the text type", "test data of the text type",
    "test data of the text type", "test data of the text type", "test data of the text type",
    "test data of the text type", "test data of the text type", "test data of the text type",
    "test data of the text type", "test data of the text type", "test data of the text type",
    "test data of the text type"};
int64_t g_dataSize1Before = 0;
int64_t g_dataSize1After = 0;

class TsdbMetadataMigration : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        // 创建epoll监听线程
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        const char *cmd = "pwd";
        FILE *fptr = popen(cmd, "r");
        if (fptr == NULL) {
            ASSERT_TRUE(false);
        }
        char tempFilePath[250] = {0};
        fgets(tempFilePath, sizeof(tempFilePath), fptr);
        fclose(fptr);
        tempFilePath[strlen(tempFilePath) - 1] = '\0';  //替换结尾换行符
        (void)sprintf(g_dataFilePath, "%s/data/gmdb", tempFilePath);
        (void)sprintf(g_dataFilePath2, "%s/data/gmdb2", tempFilePath);
        (void)sprintf(g_dataFilePath3, "%s/data/gmdb3", tempFilePath);
        TsDefulatDbFileClean();
        system("rm -rf ./data/gmdb");
        system("rm -rf ./data/gmdb2");
        system("rm -rf ./data/gmdb3");
        system("ipcrm -a");
    }
    static void TearDownTestCase()
    {
        ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        GmcConnOptionsDestroy(connOptions);
        GmcFreeStmt(stmt);
        GmcDisconnect(conn);
        testEnvClean();
        RecoverTsCiCfg();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void TsdbMetadataMigration::SetUp()
{
    AW_CHECK_LOG_BEGIN();
}

void TsdbMetadataMigration::TearDown()
{
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_CHECK_LOG_END();
}

void TearDownDisconnect()
{
    GmcConnOptionsDestroy(connOptions);
    GmcFreeStmt(stmt);
    GmcDisconnect(conn);
    testEnvClean();
    RecoverTsCiCfg();
}

// 建立时序服务端链接
int CreateTsConnect(GmcConnT **conn_ts, GmcStmtT **stmt_ts)
{
    int ret = 0;
    YangConnOptionT tsConnOptions = {0};
    tsConnOptions.isCsMode = true;
    tsConnOptions.serverLocator = g_connServerTsdb;
    ret = TestYangGmcConnect(conn_ts, stmt_ts, 0, &tsConnOptions);
    return ret;
}

int DropTable(GmcStmtT *stmt, char *tableName)
{
    int ret = 0;
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "drop table %s;", tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    if (ret != 0) {
        AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNDEFINED_TABLE);
        return ret;
    }
    // 删表变成异步进程，需等待删表结束后才能再建表
    int spaceCount = 0;
    int spaceCountBefore = 0;
    ret = TestGetSpaceInfoCnt(&spaceCountBefore);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        ret = TestGetSpaceInfoCnt(&spaceCount);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (spaceCount == spaceCountBefore) {
            AW_FUN_Log(LOG_DEBUG, "spaceCountBefore is %d\n", spaceCount);
            usleep(200000);
        } else {
            break;
        }
    }
    return ret;
}

void StopTsServer()
{
    system("kill -9 $(pidof gmserver_ts)");
}

void RecoverTsDisk()
{
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts recover");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
}

void RecoverTsini()
{
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts recover");
}

void StartTsServer()
{
    char sysCmd[128] = {0};
    (void)sprintf(sysCmd, "cd %s;gmserver_ts -b -p /usr/local/file/gmserver_ts.ini > /dev/null 2>&1", dir);
    system(sysCmd);
}

int CreateTable(GmcStmtT *stmt, char *tableName)
{
    int ret = 0;
    char sqlCmd[512] = {0};
    (void)DropTable(stmt, tableName);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNDEFINED_TABLE);
    // 此处建表时删除ttl配置项，避免遇到整点执行清空数据，下同
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(160),"
        " description text)with (time_col = 'time', interval = '1 hour', disk_limit = '50 MB',"
        " compression = 'fast(rapidlz)');",
        tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    return ret;
}

// 行式存储,注入顺序数据
int rowInsertData(GmcStmtT *stmt, char *tableName, int64_t dataCount)
{
    int ret = 0;
    int64_t count = dataCount;
    int64_t *id;
    int64_t *time;
    // 申请内存
    char *nameList = (char *)malloc(count * 64);
    if (nameList == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    char *ipList = (char *)malloc(count * 33);
    if (ipList == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    char **blobList = (char **)malloc(count * sizeof(char *));
    if (blobList == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    for (int i = 0; i < count; i++) {
        blobList[i] = (char *)malloc(160 * sizeof(char));
        (void)memset(blobList[i], 0, 160);
    }
    char *textList = (char *)malloc(count * 128);
    if (textList == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    id = (int64_t *)malloc(sizeof(int64_t) * count);
    if (id == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    time = (int64_t *)malloc(sizeof(int64_t) * count);
    if (time == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    // 构造数据
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 915148800 + i;
        int j = i % 20;
        memcpy((ipList + i * 33), (char *)g_ips[j], 33);
        memcpy((nameList + i * 64), (char *)g_names[j], 64);
        // 释放内存时不能直接赋值，需要复制后才能释放
        (void)strcpy(blobList[i], g_message[j]);
        memcpy((textList + i * 128), (char *)g_desText[j], 128);
    }
    char *description[20] = {0};
    for (int i = 0; i < 20; ++i) {
        description[i] = g_desText[i];
    }
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    RETURN_IFERR(ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int64_t));
    RETURN_IFERR(ret);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, 0, 0);
    RETURN_IFERR(ret);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, time, 0, 0);
    RETURN_IFERR(ret);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_FIXED, nameList, 64, NULL);
    RETURN_IFERR(ret);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_FIXED, ipList, 33, NULL);
    RETURN_IFERR(ret);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_BYTES, blobList, sizeof(blobList[0]), NULL);
    RETURN_IFERR(ret);
    ret = GmcExecute(stmt);
    // 释放内存
    free(id);
    free(time);
    free(nameList);
    free(ipList);
    for (int i = 0; i < count; i++) {
        free(blobList[i]);
    }
    free(blobList);
    free(textList);
    return ret;
}

// 行式存储,注入顺序数据
int rowInsertData2(GmcStmtT *stmt, char *tableName, int64_t dataCount)
{
    int ret = 0;
    int64_t count = dataCount;
    int64_t *id;
    int64_t *time;
    // 申请内存
    char *nameList = (char *)malloc(count * 64);
    if (nameList == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    char *ipList = (char *)malloc(count * 33);
    if (ipList == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    char **blobList = (char **)malloc(count * sizeof(char *));
    if (blobList == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    for (int i = 0; i < count; i++) {
        blobList[i] = (char *)malloc(160 * sizeof(char));
        (void)memset(blobList[i], 0, 160);
    }
    char *textList = (char *)malloc(count * 128);
    if (textList == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    id = (int64_t *)malloc(sizeof(int64_t) * count);
    if (id == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    time = (int64_t *)malloc(sizeof(int64_t) * count);
    if (time == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    // 构造数据
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 915148800 + i;
        int j = i % 20;
        memcpy((ipList + i * 33), (char *)g_ips[j], 33);
        memcpy((nameList + i * 64), (char *)g_names[j], 64);
        // 释放内存时不能直接赋值，需要复制后才能释放
        (void)strcpy(blobList[i], g_message[j]);
        memcpy((textList + i * 128), (char *)g_desText[j], 128);
    }
    char *description[20] = {0};
    for (int i = 0; i < 20; ++i) {
        description[i] = g_desText[i];
    }
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    RETURN_IFERR(ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int64_t));
    RETURN_IFERR(ret);
    ret = GmcBindCol(stmt, 6, (GmcDataTypeE)DB_DATATYPE_INT64, id, 0, 0);
    RETURN_IFERR(ret);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, time, 0, 0);
    RETURN_IFERR(ret);
    ret = GmcExecute(stmt);
    // 释放内存
    free(id);
    free(time);
    free(nameList);
    free(ipList);
    for (int i = 0; i < count; i++) {
        free(blobList[i]);
    }
    free(blobList);
    free(textList);
    return ret;
}

typedef struct {
    int64_t *ids;
    int64_t *times;
    char (*names)[64];
    char (*ips)[33];
    char (*blobs)[160];
    char (*texts)[128];
    int64_t dataCount;
} QueryData;

void checkQueryData(GmcStmtT *stmt, QueryData querydata)
{
    // 查询过滤后的数据是否正确
    bool eof = false;
    bool isNull = false;
    int64_t cId = 0;
    int64_t cTime = 0;
    char cIp[33] = {0};
    char cName[64] = {0};
    char cBlob[160] = {0};
    char cText[128] = {0};
    uint32_t sizeInt = sizeof(int64_t);
    uint32_t sizeCharIp = sizeof(cIp);
    uint32_t sizeCharNmae = sizeof(cName);
    uint32_t sizeCharBolb = sizeof(cBlob);
    uint32_t sizeCharText = sizeof(cText);
    int fetchTimes = 0;
    int64_t count = 0;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (eof) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &cId, &sizeInt, &isNull);
        AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_ASSERT_EQ_INT(querydata.ids[fetchTimes], cId);
        count = cId % 20;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &cTime, &sizeInt, &isNull);
        AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_ASSERT_EQ_INT(querydata.times[fetchTimes], cTime);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &cName, &sizeCharNmae, &isNull);
        AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_ASSERT_EQ_STR((const char *)querydata.names[count], cName);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &cIp, &sizeCharIp, &isNull);
        AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_ASSERT_EQ_STR((const char *)querydata.ips[count], cIp);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &cBlob, &sizeCharBolb, &isNull);
        AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_ASSERT_EQ_STR((const char *)querydata.blobs[count], cBlob);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 5, &cText, &sizeCharText, &isNull);
        AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_ASSERT_EQ_STR((const char *)querydata.texts[count], cText);
        fetchTimes++;
    }
    AW_MACRO_ASSERT_EQ_INT(fetchTimes, querydata.dataCount);
}

// 001.DiskLessBoot配置项为True且dataFilePath路径为空，拉起服务
// 预期：建连失败
TEST_F(TsdbMetadataMigration, Timing_060_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    StopTsServer();
    RecoverTsDisk();
    StartTsServer();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 2025-03-18,开发代码变更，无盘启动不设置路径以配置文件路径启动db
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002.DiskLessBoot配置项为True且dataFilePath路径非法，拉起服务
// 预期：建连失败
TEST_F(TsdbMetadataMigration, Timing_060_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    StopTsServer();
    RecoverTsDisk();
    StartTsServer();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 无盘启动服务会等待，此时建连失败
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003.DiskLessBoot配置项为True且路径为空，拉起服务后第50秒时写入有效路径
// 预期：写入有效路径后建连成功
TEST_F(TsdbMetadataMigration, Timing_060_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    StopTsServer();
    RecoverTsDisk();
    StartTsServer();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 无盘启动服务会等待，此时建连失败
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004.DiskLessBoot配置项为True且路径为空，拉起服务后第30秒时写入无效路径
// 预期：写入无效路径后服务进入等待，60s后启动失败
TEST_F(TsdbMetadataMigration, Timing_060_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    StopTsServer();
    RecoverTsDisk();
    StartTsServer();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 无盘启动服务会等待，此时建连失败
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005.DiskLessBoot配置项为True且路径为空，拉起服务后第30秒时写入无效路径。50秒时写入有效路径
// 预期：写入无效路径后服务进入等待，写入有效路径后服务启动成功
TEST_F(TsdbMetadataMigration, Timing_060_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    StopTsServer();
    RecoverTsDisk();
    StartTsServer();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 无盘启动服务会等待，此时建连失败
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("mkdir -p ./data/gmdb");
    ret = GmcSetPersistPath(g_dataFilePath, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_dataSize1Before = GetDirSize(g_dataFilePath);
    sleep(1);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(1);
    g_dataSize1After = GetDirSize(g_dataFilePath);
    AW_MACRO_ASSERT_NE_INT(g_dataSize1Before, g_dataSize1After);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006.DiskLessBoot配置项为True且路径为空，拉起服务后第30秒时写入有效路径。等待3秒后写入无效路径
// 预期：以第一次设置的路径启动，第二次设置无效
TEST_F(TsdbMetadataMigration, Timing_060_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    StopTsServer();
    RecoverTsDisk();
    StartTsServer();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 无盘启动服务会等待，此时建连失败
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(3);
    ret = system("mkdir -p ./data/gmdb2");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetPersistPath(g_dataFilePath2, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_dataSize1Before = GetDirSize(g_dataFilePath2);
    sleep(1);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(1);
    g_dataSize1After = GetDirSize(g_dataFilePath2);
    AW_MACRO_ASSERT_NE_INT(g_dataSize1Before, g_dataSize1After);

    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(3);
    ret = GmcSetPersistPath("", 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(3);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(1);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_dataSize1Before = GetDirSize(g_dataFilePath2);
    AW_MACRO_ASSERT_NE_INT(g_dataSize1After, g_dataSize1Before);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007.DiskLessBoot配置项为True且路径为空，拉起服务后第30秒时写入有效路径。等待3秒后写入相同有效路径
// 预期：均在设置的目录下启动服务
TEST_F(TsdbMetadataMigration, Timing_060_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    StopTsServer();
    RecoverTsDisk();
    StartTsServer();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 无盘启动服务会等待，此时建连失败
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(3);
    ret = system("mkdir -p ./data/gmdb2");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetPersistPath(g_dataFilePath2, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(1);
    g_dataSize1Before = GetDirSize(g_dataFilePath2);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(1);
    g_dataSize1After = GetDirSize(g_dataFilePath2);
    AW_MACRO_ASSERT_NE_INT(g_dataSize1Before, g_dataSize1After);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(3);
    ret = GmcSetPersistPath(g_dataFilePath2, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 构造异常重启
    GmcUnInit();
    system("kill -9 $(pidof gmserver_ts)");
    int ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh -ts");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcInit();
    sleep(1);

    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName2, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_CONNECTION_FAILURE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008.无盘启动，拉起服务后第30秒时写入有效路径，等待3秒后写入不同的有效路径路径下没有文件,后重启
// 预期：重启后服务在新目录下启动
TEST_F(TsdbMetadataMigration, Timing_060_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // usg设备上通过gmserver_ts -b -p启动失败，现在无盘启动会以默认路径启动不会导致在增量编译下启动服务失败，此处修改为脚本启动服务
// euler和仿真环境清共享内存，清理原有DB文件
#if defined RUN_INDEPENDENT
    system("sh $TEST_HOME/tools/stop.sh -f");
#endif
    system("sh $TEST_HOME/tools/stop.sh -ts");
    TsDefulatDbFileClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts recover");
    // 设置diskLessBoot避免不同环境可能存在配置项不一致
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 无盘启动服务会等待，此时建连失败
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./data/gmdb2/");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./data/gmdb3/");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetPersistPath(g_dataFilePath2, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(1);
    g_dataSize1Before = GetDirSize(g_dataFilePath2);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(1);
    g_dataSize1After = GetDirSize(g_dataFilePath2);
    AW_MACRO_ASSERT_NE_INT(g_dataSize1Before, g_dataSize1After);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetPersistPath(g_dataFilePath3, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // // 正常重启服务
    GmcFreeStmt(stmt);
    GmcDisconnect(conn);
    GmcUnInit();
    g_dataSize1Before = GetDirSize(g_dataFilePath3);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    // 等待2s确保检查机制已经触发
    sleep(2);
    g_dataSize1After = GetDirSize(g_dataFilePath3);
    AW_MACRO_ASSERT_NE_INT(g_dataSize1Before, g_dataSize1After);
    conn = NULL;
    stmt = NULL;
    ret = GmcInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(3);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = CreateTable(stmt, g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName2, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_CONNECTION_FAILURE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 009.无盘启动，拉起服务后第30秒时写入有效路径。等待3秒后写入不同的有效路径，关闭服务,导出持久化文件至迁入目录后启动
// 预期：重启后持久化文件搬迁至新目录
TEST_F(TsdbMetadataMigration, Timing_060_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    StopTsServer();
    RecoverTsDisk();
    StartTsServer();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 无盘启动服务会等待，此时建连失败
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./data/gmdb2");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetPersistPath(g_dataFilePath2, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(1);
    g_dataSize1Before = GetDirSize(g_dataFilePath2);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(1);
    g_dataSize1After = GetDirSize(g_dataFilePath2);
    AW_MACRO_ASSERT_NE_INT(g_dataSize1Before, g_dataSize1After);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./data/gmdb3");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char syscmd[512] = {0};
    (void)sprintf(syscmd, "gmexport -c systbl_data -f ./data/gmdb3 -s %s", g_connServerTsdb);
    ret = system(syscmd);
    EXPECT_EQ(ret, GMERR_OK);
    sleep(3);

    ret = GmcSetPersistPath(g_dataFilePath3, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_dataSize1Before = GetDirSize(g_dataFilePath3);
    GmcUnInit();
    system("kill -9 $(pidof gmserver_ts)");
    int ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh -ts");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcInit();
    // 等待一秒后建连
    sleep(1);
    g_dataSize1After = GetDirSize(g_dataFilePath3);
    AW_MACRO_ASSERT_NE_INT(g_dataSize1Before, g_dataSize1After);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char queryCommand[128] = {0};
    (void)sprintf(queryCommand, "select * from %s ", g_tableName);
    uint32_t sqlStateLen = strlen(queryCommand);
    ret = GmcExecDirect(stmt, queryCommand, sqlStateLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    int64_t dataCount = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, 0);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_CONNECTION_FAILURE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*第二次启动成功？？,与持久化文件无关指存在和db后缀相同的无效文件*/
// 010.DiskLessBoot配置项为True且路径为空，拉起服务后第30秒时写入有效路径。等待3秒后写入不同的有效路径，
// 关闭服务,迁入目录写入txt文件，导出持久化文件至迁入目录后启动
// 预期：均在设置的目录启动
TEST_F(TsdbMetadataMigration, Timing_060_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    StopTsServer();
    RecoverTsDisk();
    StartTsServer();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 无盘启动服务会等待，此时建连失败
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(3);
    system("mkdir -p ./data/gmdb2");
    ret = GmcSetPersistPath(g_dataFilePath2, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    g_dataSize1Before = GetDirSize(g_dataFilePath2);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(1);
    g_dataSize1After = GetDirSize(g_dataFilePath2);
    AW_MACRO_ASSERT_NE_INT(g_dataSize1Before, g_dataSize1After);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("mkdir -p ./data/gmdb3");
    char syscmd[512] = {0};
    (void)sprintf(syscmd, "gmexport -c systbl_data -f ./data/gmdb3 -s %s", g_connServerTsdb);
    ret = system(syscmd);
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("echo 1111 > ./data/gmdb3/test.txt");
    EXPECT_EQ(ret, GMERR_OK);
    sleep(3);
    GmcUnInit();
    system("kill -9 $(pidof gmserver_ts)");
    int ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh -ts");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcInit();
    ret = GmcSetPersistPath(g_dataFilePath3, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(1);
    g_dataSize1Before = GetDirSize(g_dataFilePath3);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(1);
    g_dataSize1After = GetDirSize(g_dataFilePath3);
    AW_MACRO_ASSERT_NE_INT(g_dataSize1Before, g_dataSize1After);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char queryCommand[128] = {0};
    (void)sprintf(queryCommand, "select * from %s ", g_tableName);
    uint32_t sqlStateLen = strlen(queryCommand);
    ret = GmcExecDirect(stmt, queryCommand, sqlStateLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    int64_t dataCount = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, 0);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_CONNECTION_FAILURE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 011.无盘启动，迁入目录下有与迁出目录相同表结构但数据为空的持久化文件
// 预期：表存在数据不存在
TEST_F(TsdbMetadataMigration, Timing_060_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    StopTsServer();
    RecoverTsDisk();
    StartTsServer();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建epoll监听线程
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    // 无盘启动服务会等待，此时建连失败
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(3);
    system("mkdir -p ./data/gmdb2");
    ret = GmcSetPersistPath(g_dataFilePath2, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    g_dataSize1Before = GetDirSize(g_dataFilePath2);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(1);
    g_dataSize1After = GetDirSize(g_dataFilePath2);
    AW_MACRO_ASSERT_NE_INT(g_dataSize1Before, g_dataSize1After);
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 插入数据之前将持久化文件复制到迁入目录
    ret = system("mkdir -p ./data/gmdb3");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("cp -rf ./data/gmdb2/*  ./data/gmdb3");
    EXPECT_EQ(ret, GMERR_OK);

    ret = rowInsertData(stmt, g_tableName, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName2, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char syscmd[512] = {0};
    (void)sprintf(syscmd, "gmexport -c systbl_data -f ./data/gmdb3 -s %s", g_connServerTsdb);
    ret = system(syscmd);
    EXPECT_EQ(ret, GMERR_OK);
    sleep(3);

    ret = GmcSetPersistPath(g_dataFilePath3, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_dataSize1Before = GetDirSize(g_dataFilePath3);
    GmcUnInit();
    system("kill -9 $(pidof gmserver_ts)");
    int ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh -ts");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcInit();
    sleep(3);
    g_dataSize1After = GetDirSize(g_dataFilePath3);
    // 受redo文件影响，不进行校验AW_MACRO_ASSERT_NE_INT(g_dataSize1Before, g_dataSize1After);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char queryCommand[128] = {0};
    (void)sprintf(queryCommand, "select * from %s ", g_tableName);
    uint32_t sqlStateLen = strlen(queryCommand);
    ret = GmcExecDirect(stmt, queryCommand, sqlStateLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    int64_t dataCount = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, 0);
    (void)sprintf(queryCommand, "select * from %s ", g_tableName2);
    sqlStateLen = strlen(queryCommand);
    ret = GmcExecDirect(stmt, queryCommand, sqlStateLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    dataCount = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, 0);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 012.无盘启动，迁入目录下有与迁出目录相同表结构相同数据的持久化文件
// 预期：表存在数据存在
TEST_F(TsdbMetadataMigration, Timing_060_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    StopTsServer();
    RecoverTsDisk();
    StartTsServer();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char fdCmd[256] = {0};
    (void)sprintf(fdCmd, "ls -l /proc/`pidof gmserver_ts`/fd |wc -l");
    int32_t fdBefore = 0;
    int32_t fdAfter = 0;
    fdBefore = GetViewFieldResultValue(fdCmd);
    // 无盘启动服务会等待，此时建连失败
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 1, fdAfter);
    sleep(3);
    system("mkdir -p ./data/gmdb2");
    ret = GmcSetPersistPath(g_dataFilePath2, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 1, fdAfter);
    sleep(2);
    g_dataSize1Before = GetDirSize(g_dataFilePath2);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(1);
    g_dataSize1After = GetDirSize(g_dataFilePath2);
    AW_MACRO_ASSERT_NE_INT(g_dataSize1Before, g_dataSize1After);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 3, fdAfter);
    ret = CreateTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName2, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 5, fdAfter);
    // 插入数据之后将持久化文件复制到迁入目录
    ret = system("mkdir -p ./data/gmdb3/");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("cp -rf ./data/gmdb2/*  ./data/gmdb3/");
    EXPECT_EQ(ret, GMERR_OK);
    char syscmd[512] = {0};
    (void)sprintf(syscmd, "gmexport -c systbl_data -f ./data/gmdb3 -s %s", g_connServerTsdb);
    ret = system(syscmd);
    EXPECT_EQ(ret, GMERR_OK);
    sleep(3);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 5, fdAfter);
    ret = GmcSetPersistPath(g_dataFilePath3, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_dataSize1Before = GetDirSize(g_dataFilePath3);
    GmcUnInit();
    system("kill -9 $(pidof gmserver_ts)");
    int ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh -ts");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcInit();
    sleep(3);
    g_dataSize1After = GetDirSize(g_dataFilePath3);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 3, fdAfter);
    char queryCommand[128] = {0};
    (void)sprintf(queryCommand, "select * from %s ", g_tableName);
    uint32_t sqlStateLen = strlen(queryCommand);
    ret = GmcExecDirect(stmt, queryCommand, sqlStateLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    int64_t dataCount = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, 10);
    (void)sprintf(queryCommand, "select * from %s ", g_tableName2);
    sqlStateLen = strlen(queryCommand);
    ret = GmcExecDirect(stmt, queryCommand, sqlStateLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    dataCount = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, 10);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 2, fdAfter);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 013.无盘启动，迁入目录下有与迁出目录相同表结构不同数据的持久化文件
// 预期：表存在数据存在，数据以迁入目录的数据为准
TEST_F(TsdbMetadataMigration, Timing_060_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    StopTsServer();
    RecoverTsDisk();
    StartTsServer();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 无盘启动服务会等待，此时建连失败
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(3);
    system("mkdir -p ./data/gmdb2");
    ret = GmcSetPersistPath(g_dataFilePath2, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    g_dataSize1Before = GetDirSize(g_dataFilePath2);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(1);
    g_dataSize1After = GetDirSize(g_dataFilePath2);
    AW_MACRO_ASSERT_NE_INT(g_dataSize1Before, g_dataSize1After);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName2, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 创建磁盘目录，因为GmcSetPersistPath 不具备设置磁盘目录功能
    ret = system("mkdir -p ./data/gmdb3");
    EXPECT_EQ(ret, GMERR_OK);
    // 插入数据之后将持久化文件导出到迁入目录
    char syscmd[512] = {0};
    (void)sprintf(syscmd, "gmexport -c systbl_data -f ./data/gmdb3 -s %s", g_connServerTsdb);
    ret = system(syscmd);
    EXPECT_EQ(ret, GMERR_OK);
    ret = rowInsertData(stmt, g_tableName, 20);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName2, 20);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 再次插入数据之后，复制持久化文件至迁入目录
    ret = system("cp -rf ./data/gmdb2/*  ./data/gmdb3");
    EXPECT_EQ(ret, GMERR_OK);
    sleep(3);
    // 设置新的磁盘目录
    ret = GmcSetPersistPath(g_dataFilePath3, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_dataSize1Before = GetDirSize(g_dataFilePath3);
    GmcUnInit();
    system("kill -9 $(pidof gmserver_ts)");
    int ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh -ts");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcInit();
    // 等待一秒，让DB切换至新的磁盘目录，必须在一秒之内完成切换
    sleep(1);
    g_dataSize1After = GetDirSize(g_dataFilePath3);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char queryCommand[128] = {0};
    (void)sprintf(queryCommand, "select * from %s ", g_tableName);
    uint32_t sqlStateLen = strlen(queryCommand);
    ret = GmcExecDirect(stmt, queryCommand, sqlStateLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    int64_t dataCount = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, 30);
    (void)sprintf(queryCommand, "select * from %s ", g_tableName2);
    sqlStateLen = strlen(queryCommand);
    ret = GmcExecDirect(stmt, queryCommand, sqlStateLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    dataCount = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, 30);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 014.无盘启动，迁入目录下有与迁出目录表结构不同没有数据的持久化文件，迁入目录下比迁出导出的持久化文件多字段
// 预期：存在冲突,服务启动失败
TEST_F(TsdbMetadataMigration, Timing_060_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    StopTsServer();
    RecoverTsDisk();
    StartTsServer();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 无盘启动服务会等待，此时建连失败
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(3);
    system("mkdir -p ./data/gmdb2");
    ret = GmcSetPersistPath(g_dataFilePath2, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    g_dataSize1Before = GetDirSize(g_dataFilePath2);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(1);
    g_dataSize1After = GetDirSize(g_dataFilePath2);
    AW_MACRO_ASSERT_NE_INT(g_dataSize1Before, g_dataSize1After);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 创建磁盘目录，因为GmcSetPersistPath 不具备设置磁盘目录功能
    ret = system("mkdir -p ./data/gmdb3");
    EXPECT_EQ(ret, GMERR_OK);
    // 建表后将持久化文件导出到迁入目录
    char syscmd[512] = {0};
    (void)sprintf(syscmd, "gmexport -c systbl_data -f ./data/gmdb3 -s %s", g_connServerTsdb);
    ret = system(syscmd);
    EXPECT_EQ(ret, GMERR_OK);

    char sqlCmd[128] = {0};
    (void)sprintf(sqlCmd, "alter table %s add num integer", g_tableName2);
    uint32_t sqlCmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, sqlCmdLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    // 修改表结构之后，复制持久化文件至迁入目录
    ret = system("cp -rf ./data/gmdb2/*  ./data/gmdb3");
    EXPECT_EQ(ret, GMERR_OK);
    sleep(3);
    // 设置新的磁盘目录
    ret = GmcSetPersistPath(g_dataFilePath3, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcUnInit();
    system("kill -9 $(pidof gmserver_ts)");
    int ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh -ts");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcInit();
    // 等待一秒，让DB切换至新的磁盘目录，必须在一秒之内完成切换
    sleep(3);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_CONNECTION_FAILURE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 015.无盘启动，迁入目录下有与迁出目录表结构不同有数据的持久化文件，迁入目录下比迁出导出的持久化文件多字段
// 预期：存在冲突,服务启动失败
TEST_F(TsdbMetadataMigration, Timing_060_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    StopTsServer();
    RecoverTsDisk();
    StartTsServer();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 无盘启动服务会等待，此时建连失败
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(3);
    system("mkdir -p ./data/gmdb2");
    ret = GmcSetPersistPath(g_dataFilePath2, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    g_dataSize1Before = GetDirSize(g_dataFilePath2);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(1);
    g_dataSize1After = GetDirSize(g_dataFilePath2);
    AW_MACRO_ASSERT_NE_INT(g_dataSize1Before, g_dataSize1After);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 创建磁盘目录，因为GmcSetPersistPath 不具备设置磁盘目录功能
    ret = system("mkdir -p ./data/gmdb3");
    EXPECT_EQ(ret, GMERR_OK);
    // 建表后将持久化文件导出到迁入目录
    char syscmd[512] = {0};
    (void)sprintf(syscmd, "gmexport -c systbl_data -f ./data/gmdb3 -s %s", g_connServerTsdb);
    ret = system(syscmd);
    EXPECT_EQ(ret, GMERR_OK);
    char sqlCmd[128] = {0};
    (void)sprintf(sqlCmd, "alter table %s add num integer", g_tableName2);
    uint32_t sqlCmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, sqlCmdLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    ret = rowInsertData(stmt, g_tableName, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName2, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 插入数据之后，复制持久化文件至迁入目录
    ret = system("cp -rf ./data/gmdb2/*  ./data/gmdb3");
    EXPECT_EQ(ret, GMERR_OK);
    sleep(3);
    // 设置新的磁盘目录
    ret = GmcSetPersistPath(g_dataFilePath3, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcUnInit();
    system("kill -9 $(pidof gmserver_ts)");
    int ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh -ts");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcInit();
    // 等待一秒，让DB切换至新的磁盘目录，必须在一秒之内完成切换
    sleep(3);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
    // 建连失败进行置空，避免free失败
    conn = NULL;
    stmt = NULL;
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_CONNECTION_FAILURE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 016.无盘启动，迁入目录下有与迁出目录表结构不同没有数据的持久化文件，迁入目录下比导出的持久化文件少字段
// 预期：表存在数据存在，数据以迁入目录的数据为准
TEST_F(TsdbMetadataMigration, Timing_060_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    StopTsServer();
    RecoverTsDisk();
    StartTsServer();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 无盘启动服务会等待，此时建连失败
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(3);
    system("mkdir -p ./data/gmdb2");
    ret = GmcSetPersistPath(g_dataFilePath2, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    g_dataSize1Before = GetDirSize(g_dataFilePath2);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(1);
    g_dataSize1After = GetDirSize(g_dataFilePath2);
    AW_MACRO_ASSERT_NE_INT(g_dataSize1Before, g_dataSize1After);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[128] = {0};
    (void)sprintf(sqlCmd, "alter table %s add num integer", g_tableName2);
    uint32_t sqlCmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, sqlCmdLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);

    // 创建磁盘目录，因为GmcSetPersistPath 不具备设置磁盘目录功能
    ret = system("mkdir -p ./data/gmdb3");
    EXPECT_EQ(ret, GMERR_OK);
    // 建表后将持久化文件导出到迁入目录
    char syscmd[512] = {0};
    (void)sprintf(syscmd, "gmexport -c systbl_data -f ./data/gmdb3 -s %s", g_connServerTsdb);
    ret = system(syscmd);
    EXPECT_EQ(ret, GMERR_OK);
    // 重新建表构造迁入目录比导出文件少字段场景
    ret = CreateTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 复制持久化文件至迁入目录
    ret = system("cp -rf ./data/gmdb2/*  ./data/gmdb3");
    EXPECT_EQ(ret, GMERR_OK);
    sleep(3);
    // 设置新的磁盘目录
    ret = GmcSetPersistPath(g_dataFilePath3, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_dataSize1Before = GetDirSize(g_dataFilePath3);
    GmcUnInit();
    system("kill -9 $(pidof gmserver_ts)");
    int ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh -ts");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcInit();
    // 等待一秒，让DB切换至新的磁盘目录，必须在一秒之内完成切换
    sleep(1);
    g_dataSize1After = GetDirSize(g_dataFilePath3);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char queryCommand[128] = {0};
    (void)sprintf(queryCommand, "select * from %s ", g_tableName);
    uint32_t sqlStateLen = strlen(queryCommand);
    ret = GmcExecDirect(stmt, queryCommand, sqlStateLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    int64_t dataCount = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, 0);
    (void)sprintf(queryCommand, "select * from %s ", g_tableName2);
    sqlStateLen = strlen(queryCommand);
    ret = GmcExecDirect(stmt, queryCommand, sqlStateLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    dataCount = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, 0);

    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_CONNECTION_FAILURE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 017.无盘启动，迁入目录下有与迁出目录表结构不同有数据的持久化文件，迁入目录下比导出的持久化文件少字段
// 预期：表存在数据存在，数据以迁入目录的数据为准
TEST_F(TsdbMetadataMigration, Timing_060_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    StopTsServer();
    RecoverTsDisk();
    StartTsServer();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 无盘启动服务会等待，此时建连失败
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(3);
    system("mkdir -p ./data/gmdb2");
    ret = GmcSetPersistPath(g_dataFilePath2, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    g_dataSize1Before = GetDirSize(g_dataFilePath2);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(1);
    g_dataSize1After = GetDirSize(g_dataFilePath2);
    AW_MACRO_ASSERT_NE_INT(g_dataSize1Before, g_dataSize1After);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[128] = {0};
    (void)sprintf(sqlCmd, "alter table %s add num integer", g_tableName2);
    uint32_t sqlCmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, sqlCmdLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);

    // 创建磁盘目录，因为GmcSetPersistPath 不具备设置磁盘目录功能
    ret = system("mkdir -p ./data/gmdb3");
    EXPECT_EQ(ret, GMERR_OK);
    // 建表后将持久化文件导出到迁入目录
    char syscmd[512] = {0};
    (void)sprintf(syscmd, "gmexport -c systbl_data -f ./data/gmdb3 -s %s", g_connServerTsdb);
    ret = system(syscmd);
    EXPECT_EQ(ret, GMERR_OK);
    // 重新建表构造迁入目录比导出文件少字段场景
    ret = CreateTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName2, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 复制持久化文件至迁入目录
    ret = system("cp -rf ./data/gmdb2/*  ./data/gmdb3");
    EXPECT_EQ(ret, GMERR_OK);
    sleep(3);
    // 设置新的磁盘目录
    ret = GmcSetPersistPath(g_dataFilePath3, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_dataSize1Before = GetDirSize(g_dataFilePath3);
    GmcUnInit();
    system("kill -9 $(pidof gmserver_ts)");
    int ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh -ts");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcInit();
    // 等待一秒，让DB切换至新的磁盘目录，必须在一秒之内完成切换
    sleep(1);
    g_dataSize1After = GetDirSize(g_dataFilePath3);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char queryCommand[128] = {0};
    (void)sprintf(queryCommand, "select * from %s ", g_tableName);
    uint32_t sqlStateLen = strlen(queryCommand);
    ret = GmcExecDirect(stmt, queryCommand, sqlStateLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    int64_t dataCount = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, 10);
    (void)sprintf(queryCommand, "select * from %s ", g_tableName2);
    sqlStateLen = strlen(queryCommand);
    ret = GmcExecDirect(stmt, queryCommand, sqlStateLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    dataCount = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, 10);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_CONNECTION_FAILURE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 018.无盘启动，迁入目录下有与迁出目录表结构不同没有数据的持久化文件，迁入目录下比导出的持久化文件存在字段相同类型不同
// 预期：存在冲突,服务启动失败
TEST_F(TsdbMetadataMigration, Timing_060_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    StopTsServer();
    RecoverTsDisk();
    StartTsServer();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 无盘启动服务会等待，此时建连失败
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(3);
    system("mkdir -p ./data/gmdb2");
    ret = GmcSetPersistPath(g_dataFilePath2, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    g_dataSize1Before = GetDirSize(g_dataFilePath2);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(1);
    g_dataSize1After = GetDirSize(g_dataFilePath2);
    AW_MACRO_ASSERT_NE_INT(g_dataSize1Before, g_dataSize1After);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 创建磁盘目录，因为GmcSetPersistPath 不具备设置磁盘目录功能
    ret = system("mkdir -p ./data/gmdb3");
    EXPECT_EQ(ret, GMERR_OK);
    // 建表后将持久化文件导出到迁入目录
    char syscmd[512] = {0};
    (void)sprintf(syscmd, "gmexport -c systbl_data -f ./data/gmdb3 -s %s", g_connServerTsdb);
    ret = system(syscmd);
    EXPECT_EQ(ret, GMERR_OK);
    // 重新建表构造迁入目录比导出文件少字段场景
    ret = CreateTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)DropTable(stmt, g_tableName2);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNDEFINED_TABLE);
    // 删表变成异步进程，需等待删表结束后才能再建表
    int spaceCount = 0;
    ret = TestGetSpaceInfoCnt(&spaceCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        if (spaceCount == 5) {
            AW_FUN_Log(LOG_DEBUG, "spaceCount is %d\n", spaceCount);
            usleep(200000);
            ret = TestGetSpaceInfoCnt(&spaceCount);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(4, spaceCount);
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(160),"
        " description integer)with (time_col = 'time', interval = '1 hour', disk_limit = '50 MB',"
        " compression = 'fast(rapidlz)');",
        g_tableName2);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 复制持久化文件至迁入目录
    ret = system("cp -rf ./data/gmdb2/*  ./data/gmdb3");
    EXPECT_EQ(ret, GMERR_OK);
    sleep(3);
    // 设置新的磁盘目录
    ret = GmcSetPersistPath(g_dataFilePath3, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcUnInit();
    system("kill -9 $(pidof gmserver_ts)");
    int ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh -ts");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcInit();
    // 等待一秒，让DB切换至新的磁盘目录，必须在一秒之内完成切换
    sleep(1);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
    conn = NULL;
    stmt = NULL;
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_CONNECTION_FAILURE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 019.无盘启动，迁入目录下有与迁出目录表结构不同有数据的持久化文件，迁入目录下比导出的持久化文件存在字段相同类型不同
// 预期：存在冲突,服务启动失败
TEST_F(TsdbMetadataMigration, Timing_060_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    StopTsServer();
    RecoverTsDisk();
    StartTsServer();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 无盘启动服务会等待，此时建连失败
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(3);
    system("mkdir -p ./data/gmdb2");
    ret = GmcSetPersistPath(g_dataFilePath2, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    g_dataSize1Before = GetDirSize(g_dataFilePath2);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(1);
    g_dataSize1After = GetDirSize(g_dataFilePath2);
    AW_MACRO_ASSERT_NE_INT(g_dataSize1Before, g_dataSize1After);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 创建磁盘目录，因为GmcSetPersistPath 不具备设置磁盘目录功能
    ret = system("mkdir -p ./data/gmdb3");
    EXPECT_EQ(ret, GMERR_OK);
    // 建表后将持久化文件导出到迁入目录
    char syscmd[512] = {0};
    (void)sprintf(syscmd, "gmexport -c systbl_data -f ./data/gmdb3 -s %s", g_connServerTsdb);
    ret = system(syscmd);
    EXPECT_EQ(ret, GMERR_OK);
    // 重新建表构造迁入目录比导出文件少字段场景
    ret = CreateTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)DropTable(stmt, g_tableName2);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNDEFINED_TABLE);
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(160),"
        " description integer)with (time_col = 'time', interval = '1 hour', disk_limit = '50 MB',"
        " compression = 'fast(rapidlz)');",
        g_tableName2);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName2, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 复制持久化文件至迁入目录
    ret = system("cp -rf ./data/gmdb2/*  ./data/gmdb3");
    EXPECT_EQ(ret, GMERR_OK);
    sleep(3);
    // 设置新的磁盘目录
    ret = GmcSetPersistPath(g_dataFilePath3, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcUnInit();
    system("kill -9 $(pidof gmserver_ts)");
    int ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh -ts");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcInit();
    // 等待一秒，让DB切换至新的磁盘目录，必须在一秒之内完成切换
    sleep(1);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
    // 建连失败进行置空，避免free失败
    conn = NULL;
    stmt = NULL;
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_CONNECTION_FAILURE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 020.无盘启动，迁入目录下有与迁出目录表结构不同的持久化文件，迁入目录下比导出的持久化文件少一张表结构
// 预期：以迁入目录的数据为准
TEST_F(TsdbMetadataMigration, Timing_060_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    StopTsServer();
    RecoverTsDisk();
    StartTsServer();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 无盘启动服务会等待，此时建连失败
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(3);
    system("mkdir -p ./data/gmdb2");
    ret = GmcSetPersistPath(g_dataFilePath2, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    g_dataSize1Before = GetDirSize(g_dataFilePath2);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(1);
    g_dataSize1After = GetDirSize(g_dataFilePath2);
    AW_MACRO_ASSERT_NE_INT(g_dataSize1Before, g_dataSize1After);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建磁盘目录，因为GmcSetPersistPath 不具备设置磁盘目录功能
    ret = system("mkdir -p ./data/gmdb3");
    EXPECT_EQ(ret, GMERR_OK);
    // 建表后将持久化文件导出到迁入目录
    char syscmd[512] = {0};
    (void)sprintf(syscmd, "gmexport -c systbl_data -f ./data/gmdb3 -s %s", g_connServerTsdb);
    ret = system(syscmd);
    EXPECT_EQ(ret, GMERR_OK);
    ret = DropTable(stmt, g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 删表变成异步进程，需等待删表结束后才能再建表
    int spaceCount = 0;
    ret = TestGetSpaceInfoCnt(&spaceCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        if (spaceCount == 5) {
            AW_FUN_Log(LOG_DEBUG, "spaceCount is %d\n", spaceCount);
            usleep(200000);
            ret = TestGetSpaceInfoCnt(&spaceCount);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(4, spaceCount);
    // 复制持久化文件至迁入目录
    ret = system("cp -rf ./data/gmdb2/*  ./data/gmdb3");
    EXPECT_EQ(ret, GMERR_OK);
    sleep(3);
    // 设置新的磁盘目录
    ret = GmcSetPersistPath(g_dataFilePath3, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_dataSize1Before = GetDirSize(g_dataFilePath3);
    GmcUnInit();
    system("kill -9 $(pidof gmserver_ts)");
    int ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh -ts");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcInit();
    // 等待一秒，让DB切换至新的磁盘目录，必须在一秒之内完成切换
    sleep(1);
    g_dataSize1After = GetDirSize(g_dataFilePath3);
    AW_MACRO_ASSERT_NE_INT(g_dataSize1Before, g_dataSize1After);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char queryCommand[128] = {0};
    (void)sprintf(queryCommand, "select * from %s ", g_tableName2);
    uint32_t sqlStateLen = strlen(queryCommand);
    ret = GmcExecDirect(stmt, queryCommand, sqlStateLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    int64_t dataCount = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, 0);
    (void)sprintf(queryCommand, "select * from %s ", g_tableName);
    sqlStateLen = strlen(queryCommand);
    ret = GmcExecDirect(stmt, queryCommand, sqlStateLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    dataCount = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, 10);

    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_CONNECTION_FAILURE, GMERR_UNDEFINED_TABLE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//  GmcSetPersistPath 设置时 diskLessBoot 必须为 True
// 021.DiskLessBoot配置项为False,启动服务后，通过GmcSetPersistPath设置dataFilePath设置为有效路径
// 预期：设置失败，异常重启后表和数据均存在
TEST_F(TsdbMetadataMigration, Timing_060_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    StopTsServer();
    RecoverTsini();
    StartTsServer();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName2, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    sleep(3);
    ret = system("mkdir -p ./data/gmdb2");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetPersistPath(g_dataFilePath2, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);

    // 服务端重启
    GmcUnInit();
    system("kill -9 $(pidof gmserver_ts)");
    int ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh -ts");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcInit();
    sleep(2);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char queryCommand[128] = {0};
    (void)sprintf(queryCommand, "select * from %s ", g_tableName);
    uint32_t sqlStateLen = strlen(queryCommand);
    ret = GmcExecDirect(stmt, queryCommand, sqlStateLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    int64_t dataCount = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, 10);
    (void)sprintf(queryCommand, "select * from %s ", g_tableName2);
    sqlStateLen = strlen(queryCommand);
    ret = GmcExecDirect(stmt, queryCommand, sqlStateLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    dataCount = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, 10);

    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_FEATURE_NOT_SUPPORTED);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 022.DiskLessBoot配置项为False,启动服务后，通过GmcSetPersistPath设置dataFilePath设置为无效路径
// 预期：设置失败，异常重启后表和数据均存在
TEST_F(TsdbMetadataMigration, Timing_060_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    StopTsServer();
    RecoverTsini();
    StartTsServer();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName2, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    sleep(3);
    ret = GmcSetPersistPath("", 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);

    // 服务端重启
    GmcUnInit();
    system("kill -9 $(pidof gmserver_ts)");
    int ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh -ts");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcInit();
    sleep(2);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char queryCommand[128] = {0};
    (void)sprintf(queryCommand, "select * from %s ", g_tableName);
    uint32_t sqlStateLen = strlen(queryCommand);
    ret = GmcExecDirect(stmt, queryCommand, sqlStateLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    int64_t dataCount = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, 10);
    (void)sprintf(queryCommand, "select * from %s ", g_tableName2);
    sqlStateLen = strlen(queryCommand);
    ret = GmcExecDirect(stmt, queryCommand, sqlStateLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    dataCount = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, 10);

    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_FEATURE_NOT_SUPPORTED);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 023.DiskLessBoot配置项为False,启动服务后，通过modifyCfg脚本dataFilePath设置为有效路径
// 目录下没有任何文件
// 预期：设置成功，重启后表和数据均不存在
TEST_F(TsdbMetadataMigration, Timing_060_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    StopTsServer();
    RecoverTsini();
    StartTsServer();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName2, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = system("mkdir -p ./data/gmdb2");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 服务端重启
    GmcUnInit();
    system("kill -9 $(pidof gmserver_ts)");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ChangeTsGmserverCfg((char *)"dataFileDirPath", g_dataFilePath2));
    int ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh -ts");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcInit();
    sleep(2);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char queryCommand[128] = {0};
    (void)sprintf(queryCommand, "select * from %s ", g_tableName);
    uint32_t sqlStateLen = strlen(queryCommand);
    ret = GmcExecDirect(stmt, queryCommand, sqlStateLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_UNDEFINED_TABLE);
    int64_t dataCount = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, 0);
    (void)sprintf(queryCommand, "select * from %s ", g_tableName2);
    sqlStateLen = strlen(queryCommand);
    ret = GmcExecDirect(stmt, queryCommand, sqlStateLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_UNDEFINED_TABLE);
    dataCount = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, 0);

    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_UNDEFINED_TABLE, GMERR_FEATURE_NOT_SUPPORTED);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 024.DiskLessBoot配置项为False,启动服务后，通过modifyCfg脚本dataFilePath设置为有效路径
// 导入持久化文件至迁入目录
// 预期：设置成功，重启后表存在，数据不存在
TEST_F(TsdbMetadataMigration, Timing_060_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    StopTsServer();
    RecoverTsini();
    StartTsServer();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName2, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = system("mkdir -p ./data/gmdb2");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char syscmd[512] = {0};
    (void)sprintf(syscmd, "gmexport -c systbl_data -f ./data/gmdb2 -s %s", g_connServerTsdb);
    ret = system(syscmd);
    EXPECT_EQ(ret, GMERR_OK);

    // 服务端重启
    GmcUnInit();
    system("kill -9 $(pidof gmserver_ts)");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ChangeTsGmserverCfg((char *)"dataFileDirPath", g_dataFilePath2));
    int ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh -ts");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcInit();
    sleep(2);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char queryCommand[128] = {0};
    (void)sprintf(queryCommand, "select * from %s ", g_tableName);
    uint32_t sqlStateLen = strlen(queryCommand);
    ret = GmcExecDirect(stmt, queryCommand, sqlStateLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    int64_t dataCount = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, 0);
    (void)sprintf(queryCommand, "select * from %s ", g_tableName2);
    sqlStateLen = strlen(queryCommand);
    ret = GmcExecDirect(stmt, queryCommand, sqlStateLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    dataCount = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, 0);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 025.DiskLessBoot配置项为False,启动服务后，通过modifyCfg脚本dataFilePath设置为有效路径
// 导入持久化文件至迁入目录,迁入目录下写入txt文件
// 预期：设置成功，重启后表存在，数据不存在
TEST_F(TsdbMetadataMigration, Timing_060_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    StopTsServer();
    RecoverTsini();
    StartTsServer();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName2, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = system("mkdir -p ./data/gmdb2");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char syscmd[512] = {0};
    (void)sprintf(syscmd, "gmexport -c systbl_data -f ./data/gmdb2 -s %s", g_connServerTsdb);
    ret = system(syscmd);
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("echo 1111 > ./data/gmdb2/test.txt");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 服务端重启
    GmcUnInit();
    system("kill -9 $(pidof gmserver_ts)");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ChangeTsGmserverCfg((char *)"dataFileDirPath", g_dataFilePath2));
    int ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh -ts");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcInit();
    sleep(2);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char queryCommand[128] = {0};
    (void)sprintf(queryCommand, "select * from %s ", g_tableName);
    uint32_t sqlStateLen = strlen(queryCommand);
    ret = GmcExecDirect(stmt, queryCommand, sqlStateLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    int64_t dataCount = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, 0);
    (void)sprintf(queryCommand, "select * from %s ", g_tableName2);
    sqlStateLen = strlen(queryCommand);
    ret = GmcExecDirect(stmt, queryCommand, sqlStateLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    dataCount = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, 0);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 026.DiskLessBoot配置项为False,启动服务后，通过modifyCfg脚本dataFilePath设置为有效路径，
// 迁入目录下有与迁出目录相同表结构但数据为空的持久化文件
// 预期：表存在数据不存在
TEST_F(TsdbMetadataMigration, Timing_060_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    StopTsServer();
    RecoverTsini();
    StartTsServer();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 插入数据之前将持久化文件复制到迁入目录
    ret = system("mkdir -p ./data/gmdb2");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("cp -rf /data/gmdb/*  ./data/gmdb2");
    EXPECT_EQ(ret, GMERR_OK);

    ret = rowInsertData(stmt, g_tableName, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName2, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char syscmd[512] = {0};
    (void)sprintf(syscmd, "gmexport -c systbl_data -f ./data/gmdb2 -s %s", g_connServerTsdb);
    ret = system(syscmd);
    EXPECT_EQ(ret, GMERR_OK);
    sleep(3);

    GmcUnInit();
    system("kill -9 $(pidof gmserver_ts)");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ChangeTsGmserverCfg((char *)"dataFileDirPath", g_dataFilePath2));
    int ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh -ts");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcInit();
    sleep(3);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char queryCommand[128] = {0};
    (void)sprintf(queryCommand, "select * from %s ", g_tableName);
    uint32_t sqlStateLen = strlen(queryCommand);
    ret = GmcExecDirect(stmt, queryCommand, sqlStateLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    int64_t dataCount = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, 0);
    (void)sprintf(queryCommand, "select * from %s ", g_tableName2);
    sqlStateLen = strlen(queryCommand);
    ret = GmcExecDirect(stmt, queryCommand, sqlStateLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    dataCount = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, 0);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 027.DiskLessBoot配置项为False,启动服务后，通过modifyCfg脚本dataFilePath设置为有效路径，
// 迁入目录下有与迁出目录相同表结构相同数据的持久化文件
// 预期：表存在数据存在
TEST_F(TsdbMetadataMigration, Timing_060_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    StopTsServer();
    RecoverTsini();
    StartTsServer();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName2, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 插入数据之后将持久化文件复制到迁入目录
    ret = system("mkdir -p ./data/gmdb2");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("cp -rf /data/gmdb/*  ./data/gmdb2");
    EXPECT_EQ(ret, GMERR_OK);
    char syscmd[512] = {0};
    (void)sprintf(syscmd, "gmexport -c systbl_data -f ./data/gmdb2 -s %s", g_connServerTsdb);
    ret = system(syscmd);
    EXPECT_EQ(ret, GMERR_OK);
    sleep(3);

    GmcUnInit();
    system("kill -9 $(pidof gmserver_ts)");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ChangeTsGmserverCfg((char *)"dataFileDirPath", g_dataFilePath2));
    int ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh -ts");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcInit();
    sleep(3);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char queryCommand[128] = {0};
    (void)sprintf(queryCommand, "select * from %s ", g_tableName);
    uint32_t sqlStateLen = strlen(queryCommand);
    ret = GmcExecDirect(stmt, queryCommand, sqlStateLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    int64_t dataCount = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, 10);
    (void)sprintf(queryCommand, "select * from %s ", g_tableName2);
    sqlStateLen = strlen(queryCommand);
    ret = GmcExecDirect(stmt, queryCommand, sqlStateLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    dataCount = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, 10);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 028.DiskLessBoot配置项为False,启动服务后，通过modifyCfg脚本dataFilePath设置为有效路径，
// 迁入目录下有与迁出目录相同表结构不同数据的持久化文件
// 预期：表存在数据存在，数据以迁入目录的数据为准
TEST_F(TsdbMetadataMigration, Timing_060_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    StopTsServer();
    RecoverTsini();
    StartTsServer();
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName2, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 创建磁盘目录，因为GmcSetPersistPath 不具备设置磁盘目录功能
    ret = system("mkdir -p ./data/gmdb2");
    EXPECT_EQ(ret, GMERR_OK);
    // 插入数据之后将持久化文件导出到迁入目录
    char syscmd[512] = {0};
    (void)sprintf(syscmd, "gmexport -c systbl_data -f ./data/gmdb2 -s %s", g_connServerTsdb);
    ret = system(syscmd);
    EXPECT_EQ(ret, GMERR_OK);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName, 30);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName2, 30);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 再次插入数据之后，复制持久化文件至迁入目录
    ret = system("cp -rf /data/gmdb/*  ./data/gmdb2");
    EXPECT_EQ(ret, GMERR_OK);
    sleep(3);

    GmcUnInit();
    system("kill -9 $(pidof gmserver_ts)");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ChangeTsGmserverCfg((char *)"dataFileDirPath", g_dataFilePath2));
    int ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh -ts");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcInit();
    // 等待一秒，让DB切换至新的磁盘目录，必须在一秒之内完成切换
    sleep(1);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char queryCommand[128] = {0};
    (void)sprintf(queryCommand, "select * from %s ", g_tableName);
    uint32_t sqlStateLen = strlen(queryCommand);
    ret = GmcExecDirect(stmt, queryCommand, sqlStateLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    int64_t dataCount = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, 30);
    (void)sprintf(queryCommand, "select * from %s ", g_tableName2);
    sqlStateLen = strlen(queryCommand);
    ret = GmcExecDirect(stmt, queryCommand, sqlStateLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    dataCount = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, 30);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 029.DiskLessBoot配置项为False,启动服务后，通过modifyCfg脚本dataFilePath设置为有效路径，
// 迁入目录下有与迁出目录表结构不同没有数据的持久化文件，迁入目录下比迁出导出的持久化文件多字段
// 预期：存在冲突，服务启动失败
TEST_F(TsdbMetadataMigration, Timing_060_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    StopTsServer();
    RecoverTsini();
    StartTsServer();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 创建磁盘目录
    ret = system("mkdir -p ./data/gmdb2");
    EXPECT_EQ(ret, GMERR_OK);
    // 建表后将持久化文件导出到迁入目录
    char syscmd[512] = {0};
    (void)sprintf(syscmd, "gmexport -c systbl_data -f ./data/gmdb2 -s %s", g_connServerTsdb);
    ret = system(syscmd);
    EXPECT_EQ(ret, GMERR_OK);
    char sqlCmd[128] = {0};
    (void)sprintf(sqlCmd, "alter table %s add num integer", g_tableName2);
    uint32_t sqlCmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, sqlCmdLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    // 修改表结构之后，复制持久化文件至迁入目录
    ret = system("cp -rf /data/gmdb/*  ./data/gmdb2");
    EXPECT_EQ(ret, GMERR_OK);
    // 构造服务端重启
    GmcUnInit();
    system("kill -9 $(pidof gmserver_ts)");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ChangeTsGmserverCfg((char *)"dataFileDirPath", g_dataFilePath2));
    int ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh -ts");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcInit();
    // 等待一秒，让DB切换至新的磁盘目录，必须在一秒之内完成切换
    sleep(3);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);

    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_CONNECTION_FAILURE);

    // 用例优化，建连失败后恢复目录查看是否可以重新建连
    ret = system("rm -rf ./data/gmdb2/*");
    EXPECT_EQ(ret, GMERR_OK);
    GmcUnInit();
    system("kill -9 $(pidof gmserver_ts)");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ChangeTsGmserverCfg((char *)"dataFileDirPath", g_dataFilePath2));
    ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh -ts");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcInit();
    // 等待一秒，让DB切换至新的磁盘目录，必须在一秒之内完成切换
    sleep(1);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 030.DiskLessBoot配置项为False,启动服务后，通过modifyCfg脚本dataFilePath设置为有效路径，
// 迁入目录下有与迁出目录表结构不同有数据的持久化文件，迁入目录下比迁出导出的持久化文件多字段
// 预期：存在冲突，服务启动失败
TEST_F(TsdbMetadataMigration, Timing_060_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    StopTsServer();
    RecoverTsini();
    StartTsServer();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 创建磁盘目录，因为GmcSetPersistPath 不具备设置磁盘目录功能
    ret = system("mkdir -p ./data/gmdb2");
    EXPECT_EQ(ret, GMERR_OK);
    // 建表后将持久化文件导出到迁入目录
    char syscmd[512] = {0};
    (void)sprintf(syscmd, "gmexport -c systbl_data -f ./data/gmdb2 -s %s", g_connServerTsdb);
    ret = system(syscmd);
    EXPECT_EQ(ret, GMERR_OK);
    char sqlCmd[128] = {0};
    (void)sprintf(sqlCmd, "alter table %s add num integer", g_tableName2);
    uint32_t sqlCmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, sqlCmdLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    ret = rowInsertData(stmt, g_tableName, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName2, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 插入数据之后，复制持久化文件至迁入目录
    ret = system("cp -rf /data/gmdb/*  ./data/gmdb2");
    EXPECT_EQ(ret, GMERR_OK);
    GmcUnInit();
    system("kill -9 $(pidof gmserver_ts)");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ChangeTsGmserverCfg((char *)"dataFileDirPath", g_dataFilePath2));
    int ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh -ts");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcInit();
    // 等待一秒，让DB切换至新的磁盘目录，必须在一秒之内完成切换
    sleep(3);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);

    GmcUnInit();
    system("kill -9 $(pidof gmserver_ts)");
    // 删除数据重新建连,避免TearDownTestCase中释放空stmt导致core
    ret = system("rm -rf ./data/gmdb2");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GtExecSystemCmd("sh $TEST_HOME/tools/start.sh -ts");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcInit();
    sleep(3);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_CONNECTION_FAILURE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 031.DiskLessBoot配置项为False,启动服务后，通过modifyCfg脚本dataFilePath设置为有效路径，
// 迁入目录下有与迁出目录表结构不同没有数据的持久化文件，迁入目录下比导出的持久化文件少字段
// 预期：表存在，数据以迁入目录的数据为准
TEST_F(TsdbMetadataMigration, Timing_060_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    StopTsServer();
    RecoverTsini();
    StartTsServer();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[128] = {0};
    (void)sprintf(sqlCmd, "alter table %s add num integer", g_tableName2);
    uint32_t sqlCmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, sqlCmdLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);

    // 创建磁盘目录，因为GmcSetPersistPath 不具备设置磁盘目录功能
    ret = system("mkdir -p ./data/gmdb2");
    EXPECT_EQ(ret, GMERR_OK);
    // 建表后将持久化文件导出到迁入目录
    char syscmd[512] = {0};
    (void)sprintf(syscmd, "gmexport -c systbl_data -f ./data/gmdb2 -s %s", g_connServerTsdb);
    ret = system(syscmd);
    EXPECT_EQ(ret, GMERR_OK);
    // 重新建表构造迁入目录比导出文件少字段场景
    ret = CreateTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 复制持久化文件至迁入目录
    ret = system("cp -rf /data/gmdb/*  ./data/gmdb2");
    EXPECT_EQ(ret, GMERR_OK);
    sleep(3);

    GmcUnInit();
    system("kill -9 $(pidof gmserver_ts)");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ChangeTsGmserverCfg((char *)"dataFileDirPath", g_dataFilePath2));
    int ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh -ts");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcInit();
    // 等待一秒，让DB切换至新的磁盘目录，必须在一秒之内完成切换
    sleep(1);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char queryCommand[128] = {0};
    (void)sprintf(queryCommand, "select * from %s ", g_tableName);
    uint32_t sqlStateLen = strlen(queryCommand);
    ret = GmcExecDirect(stmt, queryCommand, sqlStateLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    int64_t dataCount = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, 0);
    (void)sprintf(queryCommand, "select * from %s ", g_tableName2);
    sqlStateLen = strlen(queryCommand);
    ret = GmcExecDirect(stmt, queryCommand, sqlStateLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    dataCount = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, 0);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 032.DiskLessBoot配置项为False,启动服务后，通过modifyCfg脚本dataFilePath设置为有效路径，
// 迁入目录下有与迁出目录表结构不同有数据的持久化文件，迁入目录下比导出的持久化文件少字段
// 预期：表存在，数据以迁入目录的数据为准
TEST_F(TsdbMetadataMigration, Timing_060_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    StopTsServer();
    RecoverTsini();
    StartTsServer();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[128] = {0};
    (void)sprintf(sqlCmd, "alter table %s add num integer", g_tableName2);
    uint32_t sqlCmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, sqlCmdLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    ret = rowInsertData(stmt, g_tableName, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData2(stmt, g_tableName, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 创建磁盘目录，因为GmcSetPersistPath 不具备设置磁盘目录功能
    ret = system("mkdir -p ./data/gmdb2");
    EXPECT_EQ(ret, GMERR_OK);
    // 建表后将持久化文件导出到迁入目录
    char syscmd[512] = {0};
    (void)sprintf(syscmd, "gmexport -c systbl_data -f ./data/gmdb2 -s %s", g_connServerTsdb);
    ret = system(syscmd);
    EXPECT_EQ(ret, GMERR_OK);
    // 重新建表构造迁入目录比导出文件少字段场景
    ret = CreateTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName2, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 复制持久化文件至迁入目录
    ret = system("cp -rf /data/gmdb/*  ./data/gmdb2");
    EXPECT_EQ(ret, GMERR_OK);
    sleep(3);

    GmcUnInit();
    system("kill -9 $(pidof gmserver_ts)");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ChangeTsGmserverCfg((char *)"dataFileDirPath", g_dataFilePath2));
    int ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh -ts");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcInit();
    // 等待一秒，让DB切换至新的磁盘目录，必须在一秒之内完成切换
    sleep(3);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char queryCommand[128] = {0};
    (void)sprintf(queryCommand, "select * from %s ", g_tableName);
    uint32_t sqlStateLen = strlen(queryCommand);
    ret = GmcExecDirect(stmt, queryCommand, sqlStateLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    int64_t dataCount = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, 10);
    (void)sprintf(queryCommand, "select * from %s ", g_tableName2);
    sqlStateLen = strlen(queryCommand);
    ret = GmcExecDirect(stmt, queryCommand, sqlStateLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    dataCount = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, 10);

    (void)sprintf(sqlCmd, "alter table %s add num integer", g_tableName2);
    sqlCmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, sqlCmdLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_DUPLICATE_COLUMN);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_DUPLICATE_COLUMN);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 033.DiskLessBoot配置项为False,启动服务后，通过modifyCfg脚本dataFilePath设置为有效路径，
// 迁入目录下有与迁出目录表结构不同没有数据的持久化文件，迁入目录下比导出的持久化文件存在字段相同类型不同
// 预期：存在冲突，服务启动失败
TEST_F(TsdbMetadataMigration, Timing_060_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    StopTsServer();
    RecoverTsini();
    StartTsServer();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 创建磁盘目录，因为GmcSetPersistPath 不具备设置磁盘目录功能
    ret = system("mkdir -p ./data/gmdb2");
    EXPECT_EQ(ret, GMERR_OK);
    // 建表后将持久化文件导出到迁入目录
    char syscmd[512] = {0};
    (void)sprintf(syscmd, "gmexport -c systbl_data -f ./data/gmdb2 -s %s", g_connServerTsdb);
    ret = system(syscmd);
    EXPECT_EQ(ret, GMERR_OK);
    // 重新建表构造迁入目录比导出文件少字段场景
    ret = CreateTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)DropTable(stmt, g_tableName2);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNDEFINED_TABLE);
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(160),"
        " description integer)with (time_col = 'time', interval = '1 hour', disk_limit = '50 MB',"
        " compression = 'fast(rapidlz)');",
        g_tableName2);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 复制持久化文件至迁入目录
    ret = system("cp -rf /data/gmdb/*  ./data/gmdb2");
    EXPECT_EQ(ret, GMERR_OK);
    sleep(3);

    GmcUnInit();
    system("kill -9 $(pidof gmserver_ts)");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ChangeTsGmserverCfg((char *)"dataFileDirPath", g_dataFilePath2));
    int ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh -ts");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcInit();
    // 等待一秒，让DB切换至新的磁盘目录，必须在一秒之内完成切换
    sleep(1);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_CONNECTION_FAILURE);
    // 用例优化，建连失败后恢复目录查看是否可以重新建连
    ret = system("rm -rf ./data/gmdb2/*");
    EXPECT_EQ(ret, GMERR_OK);
    GmcUnInit();
    system("kill -9 $(pidof gmserver_ts)");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ChangeTsGmserverCfg((char *)"dataFileDirPath", g_dataFilePath2));
    ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh -ts");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcInit();
    // 等待一秒，让DB切换至新的磁盘目录，必须在一秒之内完成切换
    sleep(1);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 034.DiskLessBoot配置项为False,启动服务后，通过modifyCfg脚本dataFilePath设置为有效路径，
// 迁入目录下有与迁出目录表结构不同有数据的持久化文件，迁入目录下比导出的持久化文件存在字段相同类型不同
// 预期：存在冲突，服务启动失败
TEST_F(TsdbMetadataMigration, Timing_060_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    StopTsServer();
    RecoverTsini();
    StartTsServer();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 创建磁盘目录，因为GmcSetPersistPath 不具备设置磁盘目录功能
    ret = system("mkdir -p ./data/gmdb2");
    EXPECT_EQ(ret, GMERR_OK);
    // 建表后将持久化文件导出到迁入目录
    char syscmd[512] = {0};
    (void)sprintf(syscmd, "gmexport -c systbl_data -f ./data/gmdb2 -s %s", g_connServerTsdb);
    ret = system(syscmd);
    EXPECT_EQ(ret, GMERR_OK);
    // 重新建表构造迁入目录比导出文件少字段场景
    ret = CreateTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)DropTable(stmt, g_tableName2);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNDEFINED_TABLE);
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(160),"
        " description integer)with (time_col = 'time', interval = '1 hour', disk_limit = '50 MB',"
        " compression = 'fast(rapidlz)');",
        g_tableName2);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName2, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 复制持久化文件至迁入目录
    ret = system("cp -rf /data/gmdb/*  ./data/gmdb2");
    EXPECT_EQ(ret, GMERR_OK);
    sleep(3);

    GmcUnInit();
    system("kill -9 $(pidof gmserver_ts)");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ChangeTsGmserverCfg((char *)"dataFileDirPath", g_dataFilePath2));
    int ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh -ts");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcInit();
    // 等待一秒，让DB切换至新的磁盘目录，必须在一秒之内完成切换
    sleep(1);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);

    GmcUnInit();
    system("kill -9 $(pidof gmserver_ts)");
    // 删除数据重新建连,避免TearDownTestCase中释放空stmt导致core
    ret = system("rm -rf ./data/gmdb2");
    EXPECT_EQ(ret, GMERR_OK);
    ret = GtExecSystemCmd("sh $TEST_HOME/tools/start.sh -ts");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcInit();
    // 等待一秒，让DB切换至新的磁盘目录，必须在一秒之内完成切换
    sleep(1);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_CONNECTION_FAILURE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 035.DiskLessBoot配置项为False,启动服务后，通过modifyCfg脚本dataFilePath设置为有效路径，
// 迁入目录下有与迁出目录表结构不同的持久化文件，迁入目录下比导出的持久化文件少一张表结构
// 预期：表以导出文件为准，数据以迁入目录为准
TEST_F(TsdbMetadataMigration, Timing_060_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    StopTsServer();
    RecoverTsini();
    StartTsServer();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName2, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 创建磁盘目录，因为GmcSetPersistPath 不具备设置磁盘目录功能
    ret = system("mkdir -p ./data/gmdb2");
    EXPECT_EQ(ret, GMERR_OK);
    // 建表后将持久化文件导出到迁入目录
    char syscmd[512] = {0};
    (void)sprintf(syscmd, "gmexport -c systbl_data -f ./data/gmdb2 -s %s", g_connServerTsdb);
    ret = system(syscmd);
    EXPECT_EQ(ret, GMERR_OK);
    ret = DropTable(stmt, g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 该用例偶现了复制文件至新目录出错问题，所以先建对应目录避免后续复制出错
    ret = system("mkdir -p /data/gmdb");
    EXPECT_EQ(ret, GMERR_OK);
    // 复制持久化文件至迁入目录
    ret = system("cp -rf /data/gmdb/*  ./data/gmdb2");
    EXPECT_EQ(ret, GMERR_OK);
    sleep(3);

    GmcUnInit();
    system("kill -9 $(pidof gmserver_ts)");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ChangeTsGmserverCfg((char *)"dataFileDirPath", g_dataFilePath2));
    int ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh -ts");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcInit();
    // 等待一秒，让DB切换至新的磁盘目录，必须在一秒之内完成切换
    sleep(1);
    ret = CreateTsConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char queryCommand[128] = {0};
    (void)sprintf(queryCommand, "select * from %s ", g_tableName);
    uint32_t sqlStateLen = strlen(queryCommand);
    ret = GmcExecDirect(stmt, queryCommand, sqlStateLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    int64_t dataCount = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, 10);
    (void)sprintf(queryCommand, "select * from %s ", g_tableName2);
    sqlStateLen = strlen(queryCommand);
    ret = GmcExecDirect(stmt, queryCommand, sqlStateLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    dataCount = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, 0);
    AW_FUN_Log(LOG_STEP, "test end.");
}
