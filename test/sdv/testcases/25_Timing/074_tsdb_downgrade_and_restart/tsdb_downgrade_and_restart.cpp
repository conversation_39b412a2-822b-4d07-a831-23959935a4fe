/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2030. All rights reserved.
 * Description: 【技术转交付】TSDB 测试
 * Author: qinjianhua
 * Create: 2025-03-03·
 */

#include "gtest/gtest.h"
#include "t_rd_common.h"
#include "../../common/include/component/t_rd_ts.h"
#include "tools.h"

char tabelName[] = "testdb";
static GmcConnOptionsT *connOptions = NULL;
static GmcConnT *conn = NULL;
static GmcStmtT *stmt = NULL;
Status ret = 0;
bool eof = false;
bool isNull = false;
#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];

typedef struct {
    char (*name)[10];
    int64_t *age;
    int64_t *id;
    int64_t *worktime;
    int64_t *salary;
    uint32_t rowNum;
} Data;

typedef struct {
    char (*name)[64];
    int64_t *id;
    int64_t *worktime;
    uint32_t rowNum;
} Data2;

typedef struct {
    char (*name)[10];
    int64_t *age;
    int64_t *WORKTIME;
    int64_t *worktime;
    int64_t *WorkTime;
    uint32_t rowNum;
} Data3;

int32_t GetViewFieldResultValue(const char *viewName)
{
    sleep(5);
    int maxCmdSize = 256;
    char cmdOutput[maxCmdSize];
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));

    char command[maxCmdSize];
    (void)snprintf(command, maxCmdSize, "%s", viewName);

    FILE *pf = popen(command, "r");
    if (pf == NULL) {
        printf("popen(%s) error./n", command);
    }
    EXPECT_NE((void *)NULL, pf);
    while (NULL != fgets(cmdOutput, maxCmdSize, pf)) {
    };
    pclose(pf);

    return atoi(cmdOutput);
}

void ExecQueryCmd(GmcStmtT *stmt, const char *queryCommand)
{
    ret = GmcExecDirect(stmt, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
}

int DropCmTable(char *tableName)
{
    int ret = 0;
    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd, "drop table %s;", tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);

    return ret;
}

int InsertData(uint32_t num = 6)
{
    char names[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t ages[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t ids[] = {1, 2, 4, 9, 8, 14};
    int64_t worktimes[] = {24, 24, 11, 11, 12, 11};
    int64_t salaries[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message[][65535] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    uint32_t rowNum = num;
    uint8_t *newmessage[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage[j] = message[j];
    }

    Data data = {.name = names, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    ret = GmcPrepareStmtByLabelName(stmt, "testdb", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, data.name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &data.rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, data.age, sizeof(data.age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, data.id, sizeof(data.id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, data.worktime, sizeof(data.worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, data.salary, sizeof(data.salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage, sizeof(newmessage[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    return ret;
}

int CheckData(uint32_t num = 6)
{
    int ret = 0;
    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[65535] = {0};
    int64_t resultWorktime[100] = {24, 24, 11, 11, 12, 11};
    uint8_t message[][65535] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select message, worktime from testdb;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = 65535;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &messageRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message[i], 65535), 0);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);

        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)num);

    return ret;
}

int AgData(uint32_t num = 0)
{
    char ddlCommand[512];
    // 老化
    (void)sprintf(ddlCommand, "SELECT tsdb_aging('%s');", "testdb");
    uint32_t cmdLen = strlen(ddlCommand);
    ret = GmcExecDirect(stmt, ddlCommand, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

class tsdb_downgrade_and_restart : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("rm -rf /ts/volatile/");
        system("rm -rf /ts/volatile2/");
        InitTsCiCfg();
        ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建epoll监听线程
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        ret = TestTsGmcConnect(&conn, &stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        DropCmTable("testdb");
        ret = testGmcDisconnect(conn, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        close_epoll_thread();
        testEnvClean();
        RecoverTsCiCfg();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void tsdb_downgrade_and_restart::SetUp()
{
    AW_CHECK_LOG_BEGIN();
}

void tsdb_downgrade_and_restart::TearDown()
{
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_CHECK_LOG_END();
}

// 001.易失性正常重启，继续执行dml操作
TEST_F(tsdb_downgrade_and_restart, Timing_074_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message blob) with "
        "(is_volatile_label = 'true', time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)',  "
        "table_path = '/ts/volatile/');",
        "testdb");

    ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 写数据
    ret = InsertData();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    // 重启服务
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -ts");
    sleep(1);
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(1);
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询
    ret = CheckData();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 老化
    ret = AgData();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询
    ret = CheckData(0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    ret = InsertData();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询
    ret = CheckData();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 002.易失性删路径重启，继续执行dml操作
TEST_F(tsdb_downgrade_and_restart, Timing_074_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message blob) with "
        "(is_volatile_label = 'true', time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)',  "
        "table_path = '/ts/volatile/');",
        "testdb");

    ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 写数据
    ret = InsertData();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    // 重启服务
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -ts");
    // 删除易失性路径
    sleep(1);
    system("rm -rf /ts/volatile/");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(1);
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询
    ret = CheckData(0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    ret = InsertData();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询
    ret = CheckData();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 003.易失性cache_size重启
TEST_F(tsdb_downgrade_and_restart, Timing_074_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 写数据要超过cache_size设置的值数据才会落盘db
    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message blob) with "
        "(is_volatile_label = 'true', time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)',  "
        "table_path = '/ts/volatile/',cache_size ='1');",
        "testdb");

    ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 写数据
    ret = InsertData();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    // 重启服务
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -ts");
    sleep(1);
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(1);
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询
    ret = CheckData();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 004.多表单易失性路径正常重启，继续执行dml操作
TEST_F(tsdb_downgrade_and_restart, Timing_074_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message blob) with "
        "(is_volatile_label = 'true', time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)',  "
        "table_path = '/ts/volatile/');",
        "testdb");

    ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message blob) with "
        "(is_volatile_label = 'true', time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)',  "
        "table_path = '/ts/volatile/');",
        "testdb2");

    ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 写数据
    ret = InsertData();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char names[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t ages[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t ids[] = {1, 2, 4, 9, 8, 14};
    int64_t worktimes[] = {24, 24, 11, 11, 12, 11};
    int64_t salaries[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message[][65535] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    uint32_t rowNum = 6;
    uint8_t *newmessage[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage[j] = message[j];
    }

    Data data = {.name = names, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    ret = GmcPrepareStmtByLabelName(stmt, "testdb2", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, data.name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &data.rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, data.age, sizeof(data.age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, data.id, sizeof(data.id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, data.worktime, sizeof(data.worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, data.salary, sizeof(data.salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage, sizeof(newmessage[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    // 重启服务
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -ts");
    sleep(1);
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(1);
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询
    ret = CheckData();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int ret = 0;
    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[65535] = {0};
    int64_t resultWorktime[100] = {24, 24, 11, 11, 12, 11};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select message, worktime from testdb2;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = 65535;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &messageRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message[i], 65535), 0);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);

        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)6);
}

// 005.多表多易失性路径正常重启，继续执行dml操作
TEST_F(tsdb_downgrade_and_restart, Timing_074_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message blob) with "
        "(is_volatile_label = 'true', time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)',  "
        "table_path = '/ts/volatile/');",
        "testdb");

    ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message blob) with "
        "(is_volatile_label = 'true', time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)',  "
        "table_path = '/ts/volatile2/');",
        "testdb2");

    ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 写数据
    ret = InsertData();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char names[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t ages[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t ids[] = {1, 2, 4, 9, 8, 14};
    int64_t worktimes[] = {24, 24, 11, 11, 12, 11};
    int64_t salaries[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message[][65535] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    uint32_t rowNum = 6;
    uint8_t *newmessage[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage[j] = message[j];
    }

    Data data = {.name = names, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    ret = GmcPrepareStmtByLabelName(stmt, "testdb2", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, data.name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &data.rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, data.age, sizeof(data.age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, data.id, sizeof(data.id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, data.worktime, sizeof(data.worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, data.salary, sizeof(data.salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage, sizeof(newmessage[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    // 重启服务
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -ts");
    sleep(1);
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(1);
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询
    ret = CheckData();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int ret = 0;
    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[65535] = {0};
    int64_t resultWorktime[100] = {24, 24, 11, 11, 12, 11};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select message, worktime from testdb2;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = 65535;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &messageRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message[i], 65535), 0);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);

        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)6);
}

// 006.多表多易失性路径删除部分路径重启，继续执行dml操作
TEST_F(tsdb_downgrade_and_restart, Timing_074_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char fdCmd[256] = {0};
    (void)sprintf(fdCmd, "ls -l /proc/`pidof gmserver_ts`/fd |wc -l");
    int32_t fdBefore = 0;
    int32_t fdAfter = 0;
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 循环5次判断fd是否有增加
    for (int k = 0; k < 5; k++) {
        ret = TestTsGmcConnect(&conn, &stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char ddlCommand[512];
        snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
            "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message blob) "
            "with "
            "(is_volatile_label = 'true', time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', "
            "table_path = '/ts/volatile/');",
            "testdb");

        ret = GmcExecDirect(stmt, ddlCommand, 512);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

        snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
            "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message blob) "
            "with "
            "(is_volatile_label = 'true', time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', "
            "table_path = '/ts/volatile2/');",
            "testdb2");

        ret = GmcExecDirect(stmt, ddlCommand, 512);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

        // 写数据
        ret = InsertData();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        char names[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
        int64_t ages[] = {29, 30, 19, 23, 45, INT64_MAX};
        int64_t ids[] = {1, 2, 4, 9, 8, 14};
        int64_t worktimes[] = {24, 24, 11, 11, 12, 11};
        int64_t salaries[] = {20000, 10000, 4000, 30000, 31000, 10000};
        uint8_t message[][65535] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
        uint32_t rowNum = 6;
        uint8_t *newmessage[rowNum];
        for (uint32_t j = 0; j < rowNum; j++) {
            newmessage[j] = message[j];
        }

        Data data = {
            .name = names, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

        ret = GmcPrepareStmtByLabelName(stmt, "testdb2", GMC_OPERATION_SQL_INSERT);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, data.name, 10, NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &data.rowNum, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, data.age, sizeof(data.age[0]), NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, data.id, sizeof(data.id[0]), NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, data.worktime, sizeof(data.worktime[0]), NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, data.salary, sizeof(data.salary[0]), NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage, sizeof(newmessage[0]), NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

        ret = testGmcDisconnect(conn, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testEnvClean();  // 重启之前释放锁，清理环境等
        // 重启服务
        // system("ps -ef |grep gmserver");
        system("sh $TEST_HOME/tools/stop.sh -ts");
        sleep(1);
        // 删除易失性路径
        system("rm -rf /ts/volatile/");
        system("sh $TEST_HOME/tools/start.sh -ts");
        sleep(1);
        // system("ps -ef |grep gmserver");
        ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestTsGmcConnect(&conn, &stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 查询
        ret = CheckData(0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        int ret = 0;
        uint32_t propSize = 0;
        bool eof = false;

        int64_t worktimeRes = 0;
        uint8_t messageRes[65535] = {0};
        int64_t resultWorktime[100] = {24, 24, 11, 11, 12, 11};

        bool isNull = false;
        uint32_t i = 0;

        const char *queryCommand = "select message, worktime from testdb2;";
        ExecQueryCmd(stmt, queryCommand);

        eof = false;
        while (!eof) {
            ret = GmcFetch(stmt, &eof);
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
            if (eof) {
                break;
            }
            propSize = 65535;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &messageRes, &propSize, &isNull);
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
            propSize = sizeof(int64_t);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
            AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message[i], 65535), 0);
            AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);

            i++;
        }
        AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)6);
        DropCmTable("testdb");
        DropCmTable("testdb2");
        ret = testGmcDisconnect(conn, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        if (k == 0) {
            fdBefore = GetViewFieldResultValue(fdCmd);
        } else {
            fdBefore = GetViewFieldResultValue(fdCmd);
            AW_MACRO_EXPECT_EQ_INT(fdBefore, fdAfter);
        }
        fdAfter = fdBefore;
        AW_MACRO_EXPECT_NE_INT(fdBefore, 0);
    }
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 结束后建表避免测试套中删表失败
    char ddlCommand1[512];
    (void)sprintf(ddlCommand1,
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message blob) with "
        "(is_volatile_label = 'true', time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)',  "
        "table_path = '/ts/volatile/');",
        "testdb");
    ret = GmcExecDirect(stmt, ddlCommand1, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
}

// 007.易失性路径表和易非失性路径表正常重启，继续执行dml操作
TEST_F(tsdb_downgrade_and_restart, Timing_074_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char fdCmd[256] = {0};
    (void)sprintf(fdCmd, "ls -l /proc/`pidof gmserver_ts`/fd |wc -l");
    int32_t fdBefore = 0;
    int32_t fdAfter = 0;
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 循环5次判断fd是否有增加
    for (int k = 0; k < 5; k++) {
        ret = TestTsGmcConnect(&conn, &stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char ddlCommand[512];
        snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
            "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message blob) "
            "with "
            "(is_volatile_label = 'true', time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', "
            "table_path = '/ts/volatile/');",
            "testdb");

        ret = GmcExecDirect(stmt, ddlCommand, 512);

        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

        snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
            "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message blob) "
            "with "
            "(is_volatile_label = 'false', time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', "
            "ttl "
            "= "
            "'3 hours', table_path = '/ts/volatile/');",
            "testdb2");

        ret = GmcExecDirect(stmt, ddlCommand, 512);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        // 写数据
        ret = InsertData();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        char names[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
        int64_t ages[] = {29, 30, 19, 23, 45, INT64_MAX};
        int64_t ids[] = {1, 2, 4, 9, 8, 14};
        int64_t worktimes[] = {24, 24, 11, 11, 12, 11};
        int64_t salaries[] = {20000, 10000, 4000, 30000, 31000, 10000};
        uint8_t message[][65535] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
        uint32_t rowNum = 6;
        uint8_t *newmessage[rowNum];
        for (uint32_t j = 0; j < rowNum; j++) {
            newmessage[j] = message[j];
        }

        Data data = {
            .name = names, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

        ret = GmcPrepareStmtByLabelName(stmt, "testdb2", GMC_OPERATION_SQL_INSERT);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, data.name, 10, NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &data.rowNum, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, data.age, sizeof(data.age[0]), NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, data.id, sizeof(data.id[0]), NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, data.worktime, sizeof(data.worktime[0]), NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, data.salary, sizeof(data.salary[0]), NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage, sizeof(newmessage[0]), NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

        ret = testGmcDisconnect(conn, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testEnvClean();  // 重启之前释放锁，清理环境等
        // 重启服务
        system("ps -ef |grep gmserver");
        system("sh $TEST_HOME/tools/stop.sh -ts");
        sleep(1);
        system("sh $TEST_HOME/tools/start.sh -ts");
        ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestTsGmcConnect(&conn, &stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 查询
        ret = CheckData();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        int ret = 0;
        uint32_t propSize = 0;
        bool eof = false;

        int64_t worktimeRes = 0;
        uint8_t messageRes[65535] = {0};
        int64_t resultWorktime[100] = {24, 24, 11, 11, 12, 11};

        bool isNull = false;
        uint32_t i = 0;

        const char *queryCommand = "select message, worktime from testdb2;";
        ExecQueryCmd(stmt, queryCommand);

        eof = false;
        while (!eof) {
            ret = GmcFetch(stmt, &eof);
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
            if (eof) {
                break;
            }
            propSize = 65535;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &messageRes, &propSize, &isNull);
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
            propSize = sizeof(int64_t);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
            AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message[i], 65535), 0);
            AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);

            i++;
        }
        AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)6);
        DropCmTable("testdb");
        DropCmTable("testdb2");
        ret = testGmcDisconnect(conn, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (k == 0) {
            fdBefore = GetViewFieldResultValue(fdCmd);
        } else {
            fdBefore = GetViewFieldResultValue(fdCmd);
            AW_MACRO_EXPECT_EQ_INT(fdBefore, fdAfter);
        }
        fdAfter = fdBefore;
        AW_MACRO_EXPECT_NE_INT(fdBefore, 0);
    }
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 结束后建表避免测试套中删表失败
    char ddlCommand1[512];
    (void)sprintf(ddlCommand1,
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message blob) with "
        "(is_volatile_label = 'true', time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)',  "
        "table_path = '/ts/volatile/');",
        "testdb");
    ret = GmcExecDirect(stmt, ddlCommand1, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
}

// 008.易失性路径表和易非失性路径表删除路径重启，继续执行dml操作
TEST_F(tsdb_downgrade_and_restart, Timing_074_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message blob) with "
        "(is_volatile_label = 'true', time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)',  "
        "table_path = '/ts/volatile/');",
        "testdb");

    ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message blob) with "
        "(is_volatile_label = 'false', time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl "
        "= "
        "'3 hours', table_path = '/ts/volatile/');",
        "testdb2");

    ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 写数据
    ret = InsertData();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char names[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t ages[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t ids[] = {1, 2, 4, 9, 8, 14};
    int64_t worktimes[] = {24, 24, 11, 11, 12, 11};
    int64_t salaries[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message[][65535] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    uint32_t rowNum = 6;
    uint8_t *newmessage[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage[j] = message[j];
    }

    Data data = {.name = names, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    ret = GmcPrepareStmtByLabelName(stmt, "testdb2", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, data.name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &data.rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, data.age, sizeof(data.age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, data.id, sizeof(data.id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, data.worktime, sizeof(data.worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, data.salary, sizeof(data.salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage, sizeof(newmessage[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    // 重启服务
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -ts");
    // 删除易失性路径
    sleep(1);
    system("rm -rf /ts/volatile/");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(1);
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(1002002, ret);
    AW_ADD_ERRNUM_WHITE_LIST(3, GMERR_CONNECTION_FAILURE, GMERR_NO_DATA, GMERR_DATA_CORRUPTION);
}

// 009.易失性嵌套正常重启，继续执行dml操作
TEST_F(tsdb_downgrade_and_restart, Timing_074_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message blob) with "
        "(is_volatile_label = 'true', time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl "
        "= '3 hours', table_path = '/data/');",
        "testdb");

    ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, 1015000);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1015000);
}

// 010.非易失性重启
TEST_F(tsdb_downgrade_and_restart, Timing_074_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message blob) with "
        "(is_volatile_label = 'false', time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl "
        "= "
        "'3 hours', table_path = '/ts/volatile/');",
        "testdb");

    ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 写数据
    ret = InsertData();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    // 重启服务
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -ts");
    // 删除易失性路径
    sleep(1);
    system("rm -rf /ts/volatile/");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(1);
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(1002002, ret);

    system("mkdir /ts/volatile/");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(1);
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(1002002, ret);

    AW_ADD_ERRNUM_WHITE_LIST(3, GMERR_CONNECTION_FAILURE, GMERR_NO_DATA, GMERR_DATA_CORRUPTION);
}
// 011.非易失性重启
TEST_F(tsdb_downgrade_and_restart, Timing_074_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message blob) with "
        "(is_volatile_label = 'false', time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl "
        "= "
        "'3 hours', table_path = '/ts/volatile/');",
        "testdb");

    ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 写数据
    ret = InsertData();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    // 重启服务
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -ts");
    sleep(1);
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(1);
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询
    ret = CheckData();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 老化
    ret = AgData();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询
    ret = CheckData(0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    ret = InsertData();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询
    ret = CheckData();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 012.易失性重启，只删除行存文件
TEST_F(tsdb_downgrade_and_restart, Timing_074_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char fdCmd[256] = {0};
    (void)sprintf(fdCmd, "ls -l /proc/`pidof gmserver_ts`/fd |wc -l");
    int32_t fdBefore = 0;
    int32_t fdAfter = 0;
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 循环5次判断fd是否有增加
    for (int k = 0; k < 3; k++) {
        ret = TestTsGmcConnect(&conn, &stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char ddlCommand[512];
        snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
            "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message blob) "
            "with "
            "(is_volatile_label = 'true', time_col = 'worktime', interval = '1 hour', compression = "
            "'fast(rapidlz)',table_path = '/ts/volatile/');",
            "testdb");

        ret = GmcExecDirect(stmt, ddlCommand, 512);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

        // 写数据
        ret = InsertData();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcDisconnect(conn, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testEnvClean();  // 重启之前释放锁，清理环境等
        // 重启服务
        system("ps -ef |grep gmserver");
        system("sh $TEST_HOME/tools/stop.sh -ts");
        // 删除易失性路径
        sleep(1);
        system("rm -rf /ts/volatile/tsrow*");
        system("sh $TEST_HOME/tools/start.sh -ts");
        ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestTsGmcConnect(&conn, &stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 查询
        ret = CheckData(0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 写数据
        ret = InsertData();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 查询
        ret = CheckData();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcDisconnect(conn, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testEnvClean();  // 重启之前释放锁，清理环境等
        // 重启服务
        system("ps -ef |grep gmserver");
        system("sh $TEST_HOME/tools/stop.sh -ts");
        // 删除易失性路径
        sleep(1);
        system("rm -rf /ts/volatile/cstore*");
        system("sh $TEST_HOME/tools/start.sh -ts");
        sleep(1);
        system("ps -ef |grep gmserver");
        ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestTsGmcConnect(&conn, &stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 查询
        const char *queryCommand = "select message, worktime from testdb;";
        ret = GmcExecDirect(stmt, queryCommand, 150);
        AW_MACRO_EXPECT_EQ_INT(ret, 1019003);
        ret = DropCmTable("testdb");
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = testGmcDisconnect(conn, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (k == 0) {
            fdBefore = GetViewFieldResultValue(fdCmd);
        } else {
            fdBefore = GetViewFieldResultValue(fdCmd);
            AW_MACRO_EXPECT_EQ_INT(fdBefore, fdAfter);
        }
        fdAfter = fdBefore;
        AW_MACRO_EXPECT_NE_INT(fdBefore, 0);
    }
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 结束后建表避免测试套中删表失败
    char ddlCommand1[512];
    (void)sprintf(ddlCommand1,
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message blob) with "
        "(is_volatile_label = 'true', time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)',  "
        "table_path = '/ts/volatile/');",
        "testdb");
    ret = GmcExecDirect(stmt, ddlCommand1, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_ADD_ERRNUM_WHITE_LIST(2, 1019003, 1013000);
}

// 013.is_volatile_label为true时未指定space自定义路径（table_path）
TEST_F(tsdb_downgrade_and_restart, Timing_074_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message blob) with "
        "(is_volatile_label = 'true', time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)');",
        "testdb");

    ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, 1015000);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1015000);
}

// 014.is_volatile_label为true时指定space自定义路径（table_path）与db默认的持久化路径（dataFileDirPath）相同
TEST_F(tsdb_downgrade_and_restart, Timing_074_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message blob) with "
        "(is_volatile_label = 'true', time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl "
        "= '3 hours', table_path = '/data/gmdb/');",
        "testdb");

    ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, 1015000);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1015000);
}

// 015.易失性路径设置多路径(分号隔开)
TEST_F(tsdb_downgrade_and_restart, Timing_074_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message blob) with "
        "(is_volatile_label = 'true', time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)',  "
        "table_path = '/ts/volatile/', table_path = '/ts/volatile2/');",
        "testdb");

    ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, 1004004);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1004004);
}

// 016.多次设置易失性路径为true和false
TEST_F(tsdb_downgrade_and_restart, Timing_074_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message blob) with "
        "(is_volatile_label = 'true',is_volatile_label = 'false', time_col = 'worktime', interval = '1 hour', "
        "compression = 'fast(rapidlz)',  table_path = '/ts/volatile/');",
        "testdb");

    ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, 1004004);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1004004);
}

// 017.-r指定易失性路径重启
TEST_F(tsdb_downgrade_and_restart, Timing_074_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message blob) with "
        "(is_volatile_label = 'true', time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)',  "
        "table_path = '/ts/volatile/');",
        "testdb");

    ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    // 重启服务
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -ts");
    // 删除易失性路径
    sleep(1);
    system("rm -rf /ts/volatile/cstore*");
    system("gmserver_ts -b -p /usr/local/file/gmserver_ts.ini -r /ts/volatile/");
    sleep(1);
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(1002002, ret);
    AW_ADD_ERRNUM_WHITE_LIST(2, 1002002, 1015000);
}

// 018.is_volatile_label为true时加空格，true1、false1等未指定space自定义路径（table_path）
TEST_F(tsdb_downgrade_and_restart, Timing_074_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message blob) with "
        "(is_volatile_label = ' true', time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl "
        "= "
        "'3 hours', table_path = '/ts/volatile/');",
        "testdb");

    ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, 1004000);

    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message blob) with "
        "(is_volatile_label = 'true1', time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl "
        "= "
        "'3 hours', table_path = '/ts/volatile/');",
        "testdb");

    ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, 1004000);

    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message blob) with "
        "(is_volatile_label = 'true', time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)',  "
        "table_path = '');",
        "testdb");

    ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, 1015000);

    AW_ADD_ERRNUM_WHITE_LIST(2, 1004000, 1015000);
}

void *thread_tsdb_downgrade_and_restart_019_001(void *args)
{
    int ret1 = 0;
    int m = *(int *)args;
    GmcConnT *g_conn_sync1 = NULL;  //  conn 句柄
    GmcStmtT *g_stmt_sync = NULL;   //  stmt 句柄
    YangConnOptionT tsConnOptions = {0};
    tsConnOptions.isCsMode = true;

    ret1 = TestTsGmcConnect(&g_conn_sync1, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);

    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd, "drop table %s;", "testdb");
    uint32_t cmdLen = strlen(sqlCmd);
    ret1 = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);

    ret1 = testGmcDisconnect(g_conn_sync1, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);

    return 0;
}

void *thread_tsdb_downgrade_and_restart_019_002(void *args)
{
    int ret2 = 0;
    int m = *(int *)args;
    GmcConnT *g_conn_sync2 = NULL;  //  conn 句柄
    GmcStmtT *g_stmt_sync = NULL;   //  stmt 句柄
    YangConnOptionT tsConnOptions = {0};
    tsConnOptions.isCsMode = true;

    // 重启服务
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(1);
    system("ps -ef |grep gmserver");
    ret2 = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret2);

    ret2 = TestTsGmcConnect(&g_conn_sync2, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret2);

    ret2 = testGmcDisconnect(g_conn_sync2, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret2);
    return 0;
}

// 019.易失性正常重启与drop并发
TEST_F(tsdb_downgrade_and_restart, Timing_074_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message blob) with "
        "(is_volatile_label = 'true', time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)',  "
        "table_path = '/ts/volatile/');",
        "testdb");

    ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 写数据
    ret = InsertData();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等

    pthread_t thr1, thr2;
    int ret1 = 0;
    int thr_num = 1;
    int index[thr_num];
    for (int k = 0; k < thr_num; k++) {
        index[k] = k;
        ret1 = pthread_create(&thr1, NULL, thread_tsdb_downgrade_and_restart_019_001, (void *)&index[k]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);
        ret1 = pthread_join(thr1, NULL);
        ret1 = pthread_create(&thr2, NULL, thread_tsdb_downgrade_and_restart_019_002, (void *)&index[k]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);
        ret1 = pthread_join(thr2, NULL);
    }

    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message blob) with "
        "(is_volatile_label = 'true', time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)',  "
        "table_path = '/ts/volatile/');",
        "testdb");

    ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 查询
    ret = CheckData(0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void *thread_tsdb_downgrade_and_restart_020_001(void *args)
{
    int ret1 = 0;
    int m = *(int *)args;
    GmcConnT *g_conn_sync1 = NULL;  //  conn 句柄
    GmcStmtT *g_stmt_sync = NULL;   //  stmt 句柄
    YangConnOptionT tsConnOptions = {0};
    tsConnOptions.isCsMode = true;

    ret1 = TestTsGmcConnect(&g_conn_sync1, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);

    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd, "drop table %s;", "testdb");
    uint32_t cmdLen = strlen(sqlCmd);
    ret1 = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);

    ret1 = testGmcDisconnect(g_conn_sync1, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);

    return 0;
}

void *thread_tsdb_downgrade_and_restart_020_002(void *args)
{
    int ret2 = 0;
    int m = *(int *)args;
    GmcConnT *g_conn_sync2 = NULL;  //  conn 句柄
    GmcStmtT *g_stmt_sync = NULL;   //  stmt 句柄
    YangConnOptionT tsConnOptions = {0};
    tsConnOptions.isCsMode = true;

    // 重启服务
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -ts");
    // 删除易失性路径
    sleep(1);
    system("rm -rf /ts/volatile/");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(1);
    system("ps -ef |grep gmserver");
    ret2 = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret2);

    ret2 = TestTsGmcConnect(&g_conn_sync2, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret2);

    ret2 = testGmcDisconnect(g_conn_sync2, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret2);

    return 0;
}

// 020.易失性删路径重启与drop并发
TEST_F(tsdb_downgrade_and_restart, Timing_074_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message blob) with "
        "(is_volatile_label = 'true', time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)',  "
        "table_path = '/ts/volatile/');",
        "testdb");

    ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 写数据
    ret = InsertData();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等

    pthread_t thr1, thr2;
    int ret1 = 0;
    int thr_num = 1;
    int index[thr_num];
    for (int k = 0; k < thr_num; k++) {
        index[k] = k;
        ret1 = pthread_create(&thr1, NULL, thread_tsdb_downgrade_and_restart_020_001, (void *)&index[k]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);
        ret1 = pthread_join(thr1, NULL);
        ret1 = pthread_create(&thr2, NULL, thread_tsdb_downgrade_and_restart_020_002, (void *)&index[k]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);
        ret1 = pthread_join(thr2, NULL);
    }

    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message blob) with "
        "(is_volatile_label = 'true', time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)',  "
        "table_path = '/ts/volatile/');",
        "testdb");

    ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 查询
    ret = CheckData(0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void *thread_tsdb_downgrade_and_restart_021_001(void *args)
{
    int ret1 = 0;
    int m = *(int *)args;
    GmcConnT *g_conn_sync1 = NULL;  //  conn 句柄
    GmcStmtT *g_stmt_sync = NULL;   //  stmt 句柄
    YangConnOptionT tsConnOptions = {0};
    tsConnOptions.isCsMode = true;

    ret1 = TestTsGmcConnect(&g_conn_sync1, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);

    char sqlCmd[256] = {0};
    // truncate
    char sqlCmdTruncate[256] = "truncate table testdb;";
    ret = GmcExecDirect(g_stmt_sync, sqlCmdTruncate, 256);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);

    ret1 = testGmcDisconnect(g_conn_sync1, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);

    return 0;
}

void *thread_tsdb_downgrade_and_restart_021_002(void *args)
{
    int ret2 = 0;
    int m = *(int *)args;
    GmcConnT *g_conn_sync2 = NULL;  //  conn 句柄
    GmcStmtT *g_stmt_sync = NULL;   //  stmt 句柄
    YangConnOptionT tsConnOptions = {0};
    tsConnOptions.isCsMode = true;

    // 重启服务
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(1);
    system("ps -ef |grep gmserver");
    ret2 = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret2);

    ret2 = TestTsGmcConnect(&g_conn_sync2, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret2);

    ret2 = testGmcDisconnect(g_conn_sync2, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret2);
    return 0;
}

// 021.易失性正常重启与truncate并发
TEST_F(tsdb_downgrade_and_restart, Timing_074_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message blob) with "
        "(is_volatile_label = 'true', time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)',  "
        "table_path = '/ts/volatile/');",
        "testdb");

    ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 写数据
    ret = InsertData();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等

    pthread_t thr1, thr2;
    int ret1 = 0;
    int thr_num = 1;
    int index[thr_num];
    for (int k = 0; k < thr_num; k++) {
        index[k] = k;
        ret1 = pthread_create(&thr1, NULL, thread_tsdb_downgrade_and_restart_021_001, (void *)&index[k]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);
        ret1 = pthread_join(thr1, NULL);
        ret1 = pthread_create(&thr2, NULL, thread_tsdb_downgrade_and_restart_021_002, (void *)&index[k]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);
        ret1 = pthread_join(thr2, NULL);
    }

    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询
    ret = CheckData(0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void *thread_tsdb_downgrade_and_restart_022_001(void *args)
{
    int ret1 = 0;
    int m = *(int *)args;
    GmcConnT *g_conn_sync1 = NULL;  //  conn 句柄
    GmcStmtT *g_stmt_sync = NULL;   //  stmt 句柄
    YangConnOptionT tsConnOptions = {0};
    tsConnOptions.isCsMode = true;

    ret1 = TestTsGmcConnect(&g_conn_sync1, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);

    char sqlCmd[256] = {0};
    // truncate
    char sqlCmdTruncate[256] = "truncate table testdb;";
    ret = GmcExecDirect(g_stmt_sync, sqlCmdTruncate, 256);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);

    ret1 = testGmcDisconnect(g_conn_sync1, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);

    return 0;
}

void *thread_tsdb_downgrade_and_restart_022_002(void *args)
{
    int ret2 = 0;
    int m = *(int *)args;
    GmcConnT *g_conn_sync2 = NULL;  //  conn 句柄
    GmcStmtT *g_stmt_sync = NULL;   //  stmt 句柄
    YangConnOptionT tsConnOptions = {0};
    tsConnOptions.isCsMode = true;

    // 重启服务
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -ts");
    // 删除易失性路径
    sleep(1);
    system("rm -rf /ts/volatile/");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(1);
    system("ps -ef |grep gmserver");
    ret2 = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret2);

    ret2 = TestTsGmcConnect(&g_conn_sync2, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret2);

    ret2 = testGmcDisconnect(g_conn_sync2, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret2);

    return 0;
}

// 022.易失性删路径重启与truncate并发
TEST_F(tsdb_downgrade_and_restart, Timing_074_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message blob) with "
        "(is_volatile_label = 'true', time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)',  "
        "table_path = '/ts/volatile/');",
        "testdb");

    ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 写数据
    ret = InsertData();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等

    pthread_t thr1, thr2;
    int ret1 = 0;
    int thr_num = 1;
    int index[thr_num];
    for (int k = 0; k < thr_num; k++) {
        index[k] = k;
        ret1 = pthread_create(&thr1, NULL, thread_tsdb_downgrade_and_restart_022_001, (void *)&index[k]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);
        ret1 = pthread_join(thr1, NULL);
        ret1 = pthread_create(&thr2, NULL, thread_tsdb_downgrade_and_restart_022_002, (void *)&index[k]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);
        ret1 = pthread_join(thr2, NULL);
    }

    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询
    ret = CheckData(0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void *thread_tsdb_downgrade_and_restart_023_001(void *args)
{
    int ret1 = 0;
    int m = *(int *)args;
    GmcConnT *g_conn_sync1 = NULL;  //  conn 句柄
    GmcStmtT *g_stmt_sync = NULL;   //  stmt 句柄
    YangConnOptionT tsConnOptions = {0};
    tsConnOptions.isCsMode = true;

    ret1 = TestTsGmcConnect(&g_conn_sync1, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);

    char ddlCommand[512];
    // 老化
    (void)sprintf(ddlCommand, "SELECT tsdb_aging('%s');", "testdb");
    uint32_t cmdLen = strlen(ddlCommand);
    ret = GmcExecDirect(g_stmt_sync, ddlCommand, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);

    ret1 = testGmcDisconnect(g_conn_sync1, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);

    return 0;
}

void *thread_tsdb_downgrade_and_restart_023_002(void *args)
{
    int ret2 = 0;
    int m = *(int *)args;
    GmcConnT *g_conn_sync2 = NULL;  //  conn 句柄
    GmcStmtT *g_stmt_sync = NULL;   //  stmt 句柄
    YangConnOptionT tsConnOptions = {0};
    tsConnOptions.isCsMode = true;

    // 重启服务
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(1);
    system("ps -ef |grep gmserver");
    ret2 = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret2);

    ret2 = TestTsGmcConnect(&g_conn_sync2, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret2);

    ret2 = testGmcDisconnect(g_conn_sync2, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret2);
    return 0;
}

// 023.易失性正常重启与老化并发
TEST_F(tsdb_downgrade_and_restart, Timing_074_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message blob) with "
        "(is_volatile_label = 'true', time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)',  "
        "table_path = '/ts/volatile/');",
        "testdb");

    ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 写数据
    ret = InsertData();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等

    pthread_t thr1, thr2;
    int ret1 = 0;
    int thr_num = 1;
    int index[thr_num];
    for (int k = 0; k < thr_num; k++) {
        index[k] = k;
        ret1 = pthread_create(&thr1, NULL, thread_tsdb_downgrade_and_restart_023_001, (void *)&index[k]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);
        ret1 = pthread_join(thr1, NULL);
        ret1 = pthread_create(&thr2, NULL, thread_tsdb_downgrade_and_restart_023_002, (void *)&index[k]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);
        ret1 = pthread_join(thr2, NULL);
    }

    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询
    ret = CheckData(0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void *thread_tsdb_downgrade_and_restart_024_001(void *args)
{
    int ret1 = 0;
    int m = *(int *)args;
    GmcConnT *g_conn_sync1 = NULL;  //  conn 句柄
    GmcStmtT *g_stmt_sync = NULL;   //  stmt 句柄
    YangConnOptionT tsConnOptions = {0};
    tsConnOptions.isCsMode = true;

    ret1 = TestTsGmcConnect(&g_conn_sync1, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);
    char ddlCommand[512];
    // 老化
    (void)sprintf(ddlCommand, "SELECT tsdb_aging('%s');", "testdb");
    uint32_t cmdLen = strlen(ddlCommand);
    ret = GmcExecDirect(g_stmt_sync, ddlCommand, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);

    ret1 = testGmcDisconnect(g_conn_sync1, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);

    return 0;
}

void *thread_tsdb_downgrade_and_restart_024_002(void *args)
{
    int ret2 = 0;
    int m = *(int *)args;
    GmcConnT *g_conn_sync2 = NULL;  //  conn 句柄
    GmcStmtT *g_stmt_sync = NULL;   //  stmt 句柄
    YangConnOptionT tsConnOptions = {0};
    tsConnOptions.isCsMode = true;

    // 重启服务
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -ts");
    // 删除易失性路径
    sleep(1);
    system("rm -rf /ts/volatile/");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(1);
    system("ps -ef |grep gmserver");
    ret2 = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret2);

    ret2 = TestTsGmcConnect(&g_conn_sync2, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret2);

    ret2 = testGmcDisconnect(g_conn_sync2, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret2);

    return 0;
}

// 024.易失性删路径重启与老化并发
TEST_F(tsdb_downgrade_and_restart, Timing_074_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message blob) with "
        "(is_volatile_label = 'true', time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)',  "
        "table_path = '/ts/volatile/');",
        "testdb");

    ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 写数据
    ret = InsertData();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等

    pthread_t thr1, thr2;
    int ret1 = 0;
    int thr_num = 1;
    int index[thr_num];
    for (int k = 0; k < thr_num; k++) {
        index[k] = k;
        ret1 = pthread_create(&thr1, NULL, thread_tsdb_downgrade_and_restart_024_001, (void *)&index[k]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);
        ret1 = pthread_join(thr1, NULL);
        ret1 = pthread_create(&thr2, NULL, thread_tsdb_downgrade_and_restart_024_002, (void *)&index[k]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);
        ret1 = pthread_join(thr2, NULL);
    }

    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询
    ret = CheckData(0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
