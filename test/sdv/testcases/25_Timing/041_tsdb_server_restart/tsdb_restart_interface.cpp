/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: 【交付增强】时序支持独立重启
 * Author: jiangjjincheng
 * Create: 2024-08-31
 */
#include "gtest/gtest.h"
#include "t_rd_ts.h"

static GmcConnOptionsT *connOptions = NULL;
static GmcConnT *conn = NULL;
static GmcStmtT *stmt = NULL;
Status ret = 0;
char g_cStoreDir[64] = {0};
char tableName[] = "testdb";

class TsdbRestartInterface : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        InitTsCiCfg();
        ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建epoll监听线程
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        ret = TestTsGmcConnect(&conn, &stmt, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        GmcConnOptionsDestroy(connOptions);
        GmcFreeStmt(stmt);
        GmcDisconnect(conn);
        close_epoll_thread();
        testEnvClean();
        RecoverTsCiCfg();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void TsdbRestartInterface::SetUp()
{
    AW_CHECK_LOG_BEGIN();
}

void TsdbRestartInterface::TearDown()
{
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_CHECK_LOG_END();
}

int DropCmTable(char *tableName)
{
    int ret = 0;
    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd, "drop table %s;", tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    return ret;
}

// 001.GmcConnOptionsSetCsMode正常使用  预期：接口均调用成功
TEST_F(TsdbRestartInterface, Timing_041_TsdbRestartInterface_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn_1 = NULL;
    GmcStmtT *stmt_1 = NULL;
    GmcConnOptionsT *connOptions_1 = NULL;

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcConnOptionsCreate(&connOptions_1));
    AW_MACRO_EXPECT_EQ_INT(
        GMERR_OK, GmcConnOptionsSetServerLocator(connOptions_1, g_connServerTsdb));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcConnOptionsSetCSRead(connOptions_1));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcConnOptionsSetCSMode(connOptions_1));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcConnect(GMC_CONN_TYPE_SYNC, connOptions_1, &conn_1));
    GmcConnOptionsDestroy(connOptions_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcAllocStmt(conn_1, &stmt_1));
    GmcFreeStmt(stmt_1);
    GmcDisconnect(conn_1);
}

// 002.GmcConnOptionsSetCsMode在执行GmcConnOptionsCreate前执行  预期：执行失败，返回错误
TEST_F(TsdbRestartInterface, Timing_041_TsdbRestartInterface_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn_1 = NULL;
    GmcStmtT *stmt_1 = NULL;
    GmcConnOptionsT *connOptions_1 = NULL;

    ret = GmcConnOptionsSetCSMode(connOptions_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcConnOptionsCreate(&connOptions_1));
    AW_MACRO_EXPECT_EQ_INT(
        GMERR_OK, GmcConnOptionsSetServerLocator(connOptions_1, g_connServerTsdb));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcConnOptionsSetCSRead(connOptions_1));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcConnOptionsSetCSMode(connOptions_1));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcConnect(GMC_CONN_TYPE_SYNC, connOptions_1, &conn_1));
    GmcConnOptionsDestroy(connOptions_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcAllocStmt(conn_1, &stmt_1));
    GmcFreeStmt(stmt_1);
    GmcDisconnect(conn_1);
}

// 003.GmcConnOptionsSetCsMode在执行GmcConnOptionsDestroy后执行  预期：执行失败，返回错误
TEST_F(TsdbRestartInterface, Timing_041_TsdbRestartInterface_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn_1 = NULL;
    GmcStmtT *stmt_1 = NULL;
    GmcConnOptionsT *connOptions_1 = NULL;

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcConnOptionsCreate(&connOptions_1));
    AW_MACRO_EXPECT_EQ_INT(
        GMERR_OK, GmcConnOptionsSetServerLocator(connOptions_1, g_connServerTsdb));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcConnOptionsSetCSRead(connOptions_1));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcConnOptionsSetCSMode(connOptions_1));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcConnect(GMC_CONN_TYPE_SYNC, connOptions_1, &conn_1));
    GmcConnOptionsDestroy(connOptions_1);
    connOptions_1 = NULL;
    ret = GmcConnOptionsSetCSMode(connOptions_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = GmcAllocStmt(conn_1, &stmt_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(stmt_1);
    GmcDisconnect(conn_1);
}

// 004.GmcGetStmtView和GmcFreeStmtView正常使用  预期：接口均调用成功
TEST_F(TsdbRestartInterface, Timing_041_TsdbRestartInterface_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn_1 = NULL;
    GmcStmtT *stmt_1 = NULL;
    GmcStmtViewT *stmtView = NULL;
    GmcConnOptionsT *connOptions_1 = NULL;

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcConnOptionsCreate(&connOptions_1));
    AW_MACRO_EXPECT_EQ_INT(
        GMERR_OK, GmcConnOptionsSetServerLocator(connOptions_1, g_connServerTsdb));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcConnOptionsSetCSRead(connOptions_1));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcConnOptionsSetCSMode(connOptions_1));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcConnect(GMC_CONN_TYPE_SYNC, connOptions_1, &conn_1));
    GmcConnOptionsDestroy(connOptions_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcAllocStmt(conn_1, &stmt_1));
    char sqlCmd[256] = {0};
    uint32_t cmdLen = 0;
    (void)sprintf(
        sqlCmd, "create table %s(id integer, time integer) with (time_col = 'time', interval = '1 hour');", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt_1, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt_1, tableName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetStmtView(stmt_1, &stmtView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStmtView(stmt_1, stmtView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmtView(stmtView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropCmTable(tableName));
    GmcFreeStmt(stmt_1);
    GmcDisconnect(conn_1);
}

// 005.连续多次执行GmcGetStmtView并且使用相同参数  预期：接口均调用成功
TEST_F(TsdbRestartInterface, Timing_041_TsdbRestartInterface_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn_1 = NULL;
    GmcStmtT *stmt_1 = NULL;
    GmcStmtViewT *stmtView = NULL;
    GmcConnOptionsT *connOptions_1 = NULL;

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcConnOptionsCreate(&connOptions_1));
    AW_MACRO_EXPECT_EQ_INT(
        GMERR_OK, GmcConnOptionsSetServerLocator(connOptions_1, g_connServerTsdb));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcConnOptionsSetCSRead(connOptions_1));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcConnOptionsSetCSMode(connOptions_1));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcConnect(GMC_CONN_TYPE_SYNC, connOptions_1, &conn_1));
    GmcConnOptionsDestroy(connOptions_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcAllocStmt(conn_1, &stmt_1));
    char sqlCmd[256] = {0};
    uint32_t cmdLen = 0;
    (void)sprintf(
        sqlCmd, "create table %s(id integer, time integer) with (time_col = 'time', interval = '1 hour');", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt_1, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt_1, tableName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetStmtView(stmt_1, &stmtView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtView(stmt_1, &stmtView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtView(stmt_1, &stmtView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmtView(stmtView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropCmTable(tableName));
    GmcFreeStmt(stmt_1);
    GmcDisconnect(conn_1);
}

// 006.在释放句柄快照前多次使用GmcGetStmtView，每次入参stmt状态不一致  预期：接口均调用成功
TEST_F(TsdbRestartInterface, Timing_041_TsdbRestartInterface_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn_1 = NULL;
    GmcStmtT *stmt_1 = NULL;
    GmcStmtViewT *stmtView = NULL;
    GmcConnOptionsT *connOptions_1 = NULL;

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcConnOptionsCreate(&connOptions_1));
    AW_MACRO_EXPECT_EQ_INT(
        GMERR_OK, GmcConnOptionsSetServerLocator(connOptions_1, g_connServerTsdb));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcConnOptionsSetCSRead(connOptions_1));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcConnOptionsSetCSMode(connOptions_1));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcConnect(GMC_CONN_TYPE_SYNC, connOptions_1, &conn_1));
    GmcConnOptionsDestroy(connOptions_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcAllocStmt(conn_1, &stmt_1));

    char sqlCmd[256] = {0};
    uint32_t cmdLen = 0;
    (void)sprintf(
        sqlCmd, "create table %s(id integer, time integer) with (time_col = 'time', interval = '1 hour');", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt_1, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 第一次获取快照
    ret = GmcGetStmtView(stmt_1, &stmtView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 注入数据
    constexpr int64_t count = 10;
    int64_t id[count] = {0};
    int64_t time[count] = {0};

    for (int i = 0; i < count; i++) {
        id[i] = i + 11;
        time[i] = 1695042010 + i;
    }
    ret = GmcPrepareStmtByLabelName(stmt_1, tableName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 第二次获取快照
    ret = GmcGetStmtView(stmt_1, &stmtView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcSetStmtAttr(stmt_1, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int)));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBindCol(stmt_1, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), 0));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBindCol(stmt_1, 1, (GmcDataTypeE)DB_DATATYPE_INT64, time, sizeof(time[0]), 0));
    // 第三次获取快照
    ret = GmcGetStmtView(stmt_1, &stmtView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecute(stmt_1));
    // 第四次获取快照
    ret = GmcGetStmtView(stmt_1, &stmtView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmtView(stmtView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropCmTable(tableName));
    GmcFreeStmt(stmt_1);
    GmcDisconnect(conn_1);
}

// 007.创建多个stmt，在释放句柄快照前多次使用GmcGetStmtView，每次使用不同的stmt  预期：接口均调用成功
TEST_F(TsdbRestartInterface, Timing_041_TsdbRestartInterface_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn_1 = NULL;
    GmcStmtT *stmt_1 = NULL;
    GmcStmtViewT *stmtView = NULL;
    GmcConnT *conn_2 = NULL;
    GmcStmtT *stmt_2 = NULL;

    ret = TestTsGmcConnect(&conn_1, &stmt_1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestTsGmcConnect(&conn_2, &stmt_2, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcAllocStmt(conn_1, &stmt_1));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcAllocStmt(conn_2, &stmt_2));
    char sqlCmd[256] = {0};
    uint32_t cmdLen = 0;
    (void)sprintf(
        sqlCmd, "create table %s(id integer, time integer) with (time_col = 'time', interval = '1 hour');", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt_1, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 第一次获取快照
    ret = GmcGetStmtView(stmt_1, &stmtView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 注入数据
    constexpr int64_t count = 10;
    int64_t id[count] = {0};
    int64_t time[count] = {0};

    for (int i = 0; i < count; i++) {
        id[i] = i + 11;
        time[i] = 1695042010 + i;
    }
    ret = GmcPrepareStmtByLabelName(stmt_2, tableName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 第二次获取快照
    ret = GmcGetStmtView(stmt_2, &stmtView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcSetStmtAttr(stmt_2, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int)));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBindCol(stmt_2, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), 0));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBindCol(stmt_2, 1, (GmcDataTypeE)DB_DATATYPE_INT64, time, sizeof(time[0]), 0));
    // 第三次获取快照
    ret = GmcGetStmtView(stmt_2, &stmtView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecute(stmt_2));
    // 第四次获取快照
    ret = GmcGetStmtView(stmt_2, &stmtView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmtView(stmtView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropCmTable(tableName));

    GmcFreeStmt(stmt_1);
    GmcDisconnect(conn_1);
    GmcFreeStmt(stmt_2);
    GmcDisconnect(conn_2);
}

// 008.使用空值作为GmcGetStmtView入参stmt  预期：接口均调用成功
TEST_F(TsdbRestartInterface, Timing_041_TsdbRestartInterface_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn_1 = NULL;
    GmcStmtT *stmt_1 = NULL;
    GmcStmtViewT *stmtView = NULL;
    GmcConnOptionsT *connOptions_1 = NULL;

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcConnOptionsCreate(&connOptions_1));
    AW_MACRO_EXPECT_EQ_INT(
        GMERR_OK, GmcConnOptionsSetServerLocator(connOptions_1, g_connServerTsdb));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcConnOptionsSetCSRead(connOptions_1));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcConnOptionsSetCSMode(connOptions_1));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcConnect(GMC_CONN_TYPE_SYNC, connOptions_1, &conn_1));
    GmcConnOptionsDestroy(connOptions_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcAllocStmt(conn_1, &stmt_1));
    // 使用空值作为入参
    ret = GmcGetStmtView(stmt_1, &stmtView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStmtView(stmt, stmtView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmtView(stmtView);
    GmcFreeStmt(stmt_1);
    GmcDisconnect(conn_1);
}

// 009.连续多次执行GmcGetStmtView设置快照立刻GmcFreeStmtView释放快照  预期：接口均调用成功
TEST_F(TsdbRestartInterface, Timing_041_TsdbRestartInterface_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn_1 = NULL;
    GmcStmtT *stmt_1 = NULL;
    GmcStmtViewT *stmtView = NULL;
    GmcConnOptionsT *connOptions_1 = NULL;

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcConnOptionsCreate(&connOptions_1));
    AW_MACRO_EXPECT_EQ_INT(
        GMERR_OK, GmcConnOptionsSetServerLocator(connOptions_1, g_connServerTsdb));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcConnOptionsSetCSRead(connOptions_1));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcConnOptionsSetCSMode(connOptions_1));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcConnect(GMC_CONN_TYPE_SYNC, connOptions_1, &conn_1));
    GmcConnOptionsDestroy(connOptions_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcAllocStmt(conn_1, &stmt_1));
    char sqlCmd[256] = {0};
    uint32_t cmdLen = 0;
    (void)sprintf(
        sqlCmd, "create table %s(id integer, time integer) with (time_col = 'time', interval = '1 hour');", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt_1, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连续多次执行GmcGetStmtView设置快照立刻GmcFreeStmtView释放快照
    for (int i = 0; i < 50; i++) {
        ret = GmcPrepareStmtByLabelName(stmt_1, tableName, GMC_OPERATION_SQL_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStmtView(stmt_1, &stmtView);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcFreeStmtView(stmtView);
    }

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropCmTable(tableName));
    GmcFreeStmt(stmt_1);
    GmcDisconnect(conn_1);
}

// 010.GmcSetStmtView正常使用  预期：接口均调用成功
TEST_F(TsdbRestartInterface, Timing_041_TsdbRestartInterface_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn_1 = NULL;
    GmcStmtT *stmt_1 = NULL;
    GmcStmtViewT *stmtView = NULL;
    GmcConnOptionsT *connOptions_1 = NULL;

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcConnOptionsCreate(&connOptions_1));
    AW_MACRO_EXPECT_EQ_INT(
        GMERR_OK, GmcConnOptionsSetServerLocator(connOptions_1, g_connServerTsdb));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcConnOptionsSetCSRead(connOptions_1));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcConnOptionsSetCSMode(connOptions_1));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcConnect(GMC_CONN_TYPE_SYNC, connOptions_1, &conn_1));
    GmcConnOptionsDestroy(connOptions_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcAllocStmt(conn_1, &stmt_1));
    char sqlCmd[256] = {0};
    uint32_t cmdLen = 0;
    (void)sprintf(
        sqlCmd, "create table %s(id integer, time integer) with (time_col = 'time', interval = '1 hour');", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt_1, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 注入数据
    constexpr int64_t count = 10;
    int64_t id[count] = {0};
    int64_t time[count] = {0};

    for (int i = 0; i < count; i++) {
        id[i] = i + 11;
        time[i] = 1695042010 + i;
    }
    ret = GmcPrepareStmtByLabelName(stmt_1, tableName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 获取快照
    ret = GmcGetStmtView(stmt_1, &stmtView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcSetStmtAttr(stmt_1, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int)));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBindCol(stmt_1, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), 0));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBindCol(stmt_1, 1, (GmcDataTypeE)DB_DATATYPE_INT64, time, sizeof(time[0]), 0));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecute(stmt_1));
    // 设置快照后执行
    ret = GmcGetStmtView(stmt_1, &stmtView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)sprintf(sqlCmd, "select * from %s;", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt_1, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmtView(stmtView);
    // 删表
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropCmTable(tableName));
    GmcFreeStmt(stmt_1);
    GmcDisconnect(conn_1);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);
}

// 011.GmcSetStmtView使用未初始化的句柄快照  预期：接口均调用成功
TEST_F(TsdbRestartInterface, Timing_041_TsdbRestartInterface_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn_1 = NULL;
    GmcStmtT *stmt_1 = NULL;
    GmcStmtViewT *stmtView = NULL;
    GmcConnOptionsT *connOptions_1 = NULL;

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcConnOptionsCreate(&connOptions_1));
    AW_MACRO_EXPECT_EQ_INT(
        GMERR_OK, GmcConnOptionsSetServerLocator(connOptions_1, g_connServerTsdb));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcConnOptionsSetCSRead(connOptions_1));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcConnOptionsSetCSMode(connOptions_1));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcConnect(GMC_CONN_TYPE_SYNC, connOptions_1, &conn_1));
    GmcConnOptionsDestroy(connOptions_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcAllocStmt(conn_1, &stmt_1));
    char sqlCmd[256] = {0};
    uint32_t cmdLen = 0;
    (void)sprintf(
        sqlCmd, "create table %s(id integer, time integer) with (time_col = 'time', interval = '1 hour');", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt_1, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 获取快照
    ret = GmcGetStmtView(stmt_1, &stmtView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 注入数据
    constexpr int64_t count = 10;
    int64_t id[count] = {0};
    int64_t time[count] = {0};

    for (int i = 0; i < count; i++) {
        id[i] = i + 11;
        time[i] = 1695042010 + i;
    }
    ret = GmcPrepareStmtByLabelName(stmt_1, tableName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置快照
    ret = GmcSetStmtView(stmt_1, stmtView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置快照后绑定，不执行prepare，直接执行注入动作，预期注入动作执行失败
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcSetStmtAttr(stmt_1, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int)));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBindCol(stmt_1, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), 0));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBindCol(stmt_1, 1, (GmcDataTypeE)DB_DATATYPE_INT64, time, sizeof(time[0]), 0));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, GmcExecute(stmt_1));

    // 设置快照
    ret = GmcSetStmtView(stmt_1, stmtView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置快照后立刻执行绑定
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcSetStmtAttr(stmt_1, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int)));
    // 重新执行prepare和绑定
    ret = GmcPrepareStmtByLabelName(stmt_1, tableName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcSetStmtAttr(stmt_1, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int)));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBindCol(stmt_1, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), 0));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBindCol(stmt_1, 1, (GmcDataTypeE)DB_DATATYPE_INT64, time, sizeof(time[0]), 0));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecute(stmt_1));

    GmcFreeStmtView(stmtView);
    // 删表
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropCmTable(tableName));
    GmcFreeStmt(stmt_1);
    GmcDisconnect(conn_1);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);
}

// 012.csMode参数设置为true  预期：接口均调用成功
TEST_F(TsdbRestartInterface, Timing_041_TsdbRestartInterface_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn_1 = NULL;
    GmcStmtT *stmt_1 = NULL;
    GmcConnOptionsT *connOptions_1 = NULL;

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcConnOptionsCreate(&connOptions_1));
    AW_MACRO_EXPECT_EQ_INT(
        GMERR_OK, GmcConnOptionsSetServerLocator(connOptions_1, g_connServerTsdb));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcConnOptionsSetCSRead(connOptions_1));
    // 设置CSMode为true
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcConnOptionsSetCSMode(connOptions_1));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcConnect(GMC_CONN_TYPE_SYNC, connOptions_1, &conn_1));
    GmcConnOptionsDestroy(connOptions_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcAllocStmt(conn_1, &stmt_1));
    GmcFreeStmt(stmt_1);
    GmcDisconnect(conn_1);
}

// 013.isReuseAddr设置为0，启动服务  预期：接口均调用成功
TEST_F(TsdbRestartInterface, Timing_041_TsdbRestartInterface_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enableChannelReuse=0\"");
    TsDefulatDbFileClean();
    system("sh $TEST_HOME/tools/start.sh -ts");
    GmcConnT *conn_1 = NULL;
    GmcStmtT *stmt_1 = NULL;
    ret = TestTsGmcConnect(&conn_1, &stmt_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 014.isReuseAddr设置为1，启动服务  预期：接口均调用成功
TEST_F(TsdbRestartInterface, Timing_041_TsdbRestartInterface_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enableChannelReuse=1\"");
    TsDefulatDbFileClean();
    system("sh $TEST_HOME/tools/start.sh -ts");
    GmcConnT *conn_1 = NULL;
    GmcStmtT *stmt_1 = NULL;
    ret = TestTsGmcConnect(&conn_1, &stmt_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 015.isReuseAddr设置为非法值，启动服务  预期：服务器启动失败
TEST_F(TsdbRestartInterface, Timing_041_TsdbRestartInterface_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enableChannelReuse=1\"");
    TsDefulatDbFileClean();
    system("sh $TEST_HOME/tools/start.sh -ts");
    GmcConnT *conn_3 = NULL;
    GmcStmtT *stmt_3 = NULL;
    ret = TestTsGmcConnect(&conn_3, &stmt_3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 断连
    ret = testGmcDisconnect(conn_3, stmt_3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enableChannelReuse=-1\"");
    TsDefulatDbFileClean();
    system("sh $TEST_HOME/tools/start.sh -ts");
    GmcConnT *conn_1 = NULL;
    GmcStmtT *stmt_1 = NULL;
    ret = TestTsGmcConnect(&conn_1, &stmt_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);

    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enableChannelReuse=2\"");
    TsDefulatDbFileClean();
    system("sh $TEST_HOME/tools/start.sh -ts");
    GmcConnT *conn_2 = NULL;
    GmcStmtT *stmt_2 = NULL;
    ret = TestTsGmcConnect(&conn_2, &stmt_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts recover");
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_CONNECTION_FAILURE);
}
