CFEPath="/root/CFE_Tool/cfe/"

#系统cpu过载

#将多个CPU使用率达到55%
function rCPU_Overloadal_Value55() 
{
    echo "rCPU_Overloadal_Value 55"
    $CFE "inject rCPU_Overloadal (cpuid1, cpuid2, usage) values (0, 3, 55)"
    echo "rCPU_Overloadal"
    return 0
}

#将多个CPU使用率达到65%
function rCPU_Overloadal_Value65() 
{
    echo "rCPU_Overloadal_Value 65"
    $CFE "inject rCPU_Overloadal (cpuid1, cpuid2, usage) values (0, 3, 65)"
    echo "rCPU_Overloadal"
    return 0
}

#将多个CPU使用率达到75%
function rCPU_Overloadal_Value75() 
{
    echo "rCPU_Overloadal_Value 75"
    $CFE "inject rCPU_Overloadal (cpuid1, cpuid2, usage) values (0, 3, 75)"
    echo "rCPU_Overloadal"
    return 0
}

#将多个CPU使用率达到82%
function rCPU_Overloadal_Value82() 
{
    echo "rCPU_Overloadal_Value 82"
    cd $CFE
    ./cfe "inject rCPU_Overloadal (cpuid1, cpuid2, usage) values (0, 3, 90)"
    echo "rCPU_Overloadal"
    return 0
}

#将多个CPU使用率达到82%
function rCPU_Overloadal_Value85() 
{
    echo "rCPU_Overloadal_Value 82"
    $CFE "inject rCPU_Overloadl (cpuid, usage) values(0, 82)"
    $CFE "inject rCPU_Overloadl (cpuid, usage) values(1, 82)"
    $CFE "inject rCPU_Overloadl (cpuid, usage) values(2, 82)"
    $CFE "inject rCPU_Overloadl (cpuid, usage) values(3, 4, 82)"
    echo "rCPU_Overloadal"
    return 0
}

function rCPU_Overloadal_recover() 
{
    $CFE "clean rCPU_Overloadal"
    echo "clean rCPU_Overloadal"
    return 0
}

#将CPU1使用率达到100% 等待20s后恢复
function rCPU_Overloadl_value() 
{
    $CFE "inject rCPU_Overloadl (cpuid, usage) values(0, 100)"
    echo "rCPU_Overloadl"
    sleep 20
    $CFE "clean rCPU_Overloadl"
    echo "clean rCPU_Overloadl"
    return 0
}
#Oracle共享内存池耗尽 
function rORA_shared_pool() 
{
    $CFE "inject rORA_shared_pool"
    echo "rORA_shared_pool"
    sleep 20
    $CFE "clean rORA_shared_pool"
    echo "clean rORA_shared_pool"
    return 0
}
#rSysClockJump_60 
function rSysClockJump_60() 
{
    $CFE "inject rSysClockJump (DIRECTION,OFFSET) values (-,60)"
    echo "inject rSysClockJump (DIRECTION,OFFSET) values (-,60)"
    sleep 10
    $CFE "clean rSysClockJump where DIRECTION=- and OFFSET=60"
    echo "clean rSysClockJump where DIRECTION=- and OFFSET=60"
    return 0
}
#rSysClockJump+60 
function rSysClockJump_plus60() 
{
    $CFE "inject rSysClockJump (DIRECTION,OFFSET) values (+,60)"
    echo "inject rSysClockJump (DIRECTION,OFFSET) values (+,60)"
    sleep 5
    $CFE "clean rSysClockJump where DIRECTION=+ and OFFSET=60"
    echo "clean rSysClockJump where DIRECTION=+ and OFFSET=60"
    return 0
}
#rProc_k
function rProc_k() 
{
    echo "AAAAAAAAAAAAAAAAAAAAAAAA"
    sleep 20
    echo "BBBBBBBBBBBBBBBBBBBB"
    kill -9 `pidof gmserver`
    return 0
}
#rProc_k
function rProc_k_clent() 
{
    echo "AAAAAAAAAAAAAAAAAAAAAAAA"
    sleep 20
    echo "BBBBBBBBBBBBBBBBBBBB"
    kill -9 `pidof $(pwd)/rel_ddl_client`
    return 0
}
function rProc_kill19_18_server() 
{
    sleep 10
    echo "AAAAAAAAAAAAAAAAAAAAAAAA"
    kill -19 `pidof gmserver`
    sleep 80
    kill -18 `pidof gmserver`
    echo "bbbbbbbbbbbbbbbb"
    return 0
}
function rProc_kill19_18_clent() 
{
    sleep 10
    echo "AAAAAAAAAAAAAAAAAAAAAAAA"
    kill -19 `pidof gmserver`
    sleep 80
    kill -18 `pidof gmserver`
    echo "bbbbbbbbbbbbbbbb"
    return 0
}
function rProc_SIGILL() 
{
    sleep 10
    echo "AAAAAAAAAAAAAAAAAAAAAAAA"
    kill -4 `pidof gmserver`
    return 0
}
function rProc_SIGINT() 
{
    sleep 10
    echo "AAAAAAAAAAAAAAAAAAAAAAAA"
    kill -2 `pidof gmserver`
    return 0
}
function rProc_SIGPIPE() 
{
    sleep 10
    echo "AAAAAAAAAAAAAAAAAAAAAAAA"
    kill -13 `pidof gmserver`
    sleep 10
    echo "BBBBBBBBBBBBBBBBBBBBBB"
    return 0
}
#执行rCPU_Overloadl_value90
if [ "$1" = rCPU_Overloadl_value90 ]
then
rCPU_Overloadl_value90
fi
#执行rCPU_Overloadl_value100
if [ "$1" = rCPU_Overloadl_value ]
then
rCPU_Overloadl_value
fi
#执行rORA_shared_pool
if [ "$1" = rORA_shared_pool ]
then
rORA_shared_pool
fi
#执行rSysClockJump_60
if [ "$1" = rSysClockJump_60 ]
then
rSysClockJump_60
fi
#执行rSysClockJump+60
if [ "$1" = rSysClockJump_plus60 ]
then
rSysClockJump_plus60
fi
#执行rSysClockJump+60
if [ "$1" = rProc_k ]
then
rProc_k
fi
#执行rSysClockJump+60
if [ "$1" = rProc_k_clent ]
then
rProc_k_clent
fi
#执行rProc_kill19_18
if [ "$1" = rProc_kill19_18_server ]
then
rProc_kill19_18_server
fi
#执行rProc_kill19_18
if [ "$1" = rProc_kill19_18_clent ]
then
rProc_kill19_18_clent
fi
#执行rProc_SIGILL
if [ "$1" = rProc_SIGILL ]
then
rProc_SIGILL
fi
#执行rProc_SIGINT
if [ "$1" = rProc_SIGINT ]
then
rProc_SIGINT
fi
#执行rProc_SIGPIPE
if [ "$1" = rProc_SIGPIPE ]
then
rProc_SIGPIPE
fi
