/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: upsert into mini ref func
 * Author: yushijin
 * Create: 2024-12-27
 */

#include <string>
#include <mutex>
#include <condition_variable>

using namespace std;

extern mutex mtx;  // 用于保护共享变量
extern condition_variable cv;  // 条件变量
extern bool funcCompleted;  // 表示线程1是否执行完func

#ifndef STREAM_TABLE_STRUCT_H
#define STREAM_TABLE_STRUCT_H 1
#include "rd_feature_stream.h"
#include "gmc_stream.h"

#define RD_STREAM_TABLE_T1_NAME_SIZE 50
#define RD_STREAM_TABLE_FIX_FIELD_SIZE 50
#define RD_STREAM_TABLE_FIX_FIELD_MIN_SIZE 2
#define RD_STREAM_TABLE_FIX_FIELD_MAX_SIZE 60000
#define STREAM_REFERENCE_MAX_SQL_LEN 512

#pragma pack(1)
typedef struct {
    int64_t id;
    char name[RD_STREAM_TABLE_FIX_FIELD_SIZE];
    int64_t water_mark;
    int64_t event_time;
}RdStreamTable2DataT;

typedef struct {
    int64_t id;
    char name[RD_STREAM_TABLE_FIX_FIELD_MIN_SIZE];
    int64_t water_mark;
    int64_t event_time;
}RdStreamTable2DataT2;

typedef struct {
    int64_t id;
    char name[RD_STREAM_TABLE_FIX_FIELD_MAX_SIZE];
    int64_t water_mark;
    int64_t event_time;
}RdStreamTable2DataT3;

#pragma pack()

// 异步写
static void RdAsyncWriteStreamTable(const char *tableName, int64_t rowNum,
    int64_t id[], int64_t water_mark[], int64_t event_time[], uint16_t instanceId = 1)
{
    uint32_t ret = 0;
    RdStreamTable2DataT *v1 = NULL;
    for (int64_t i = 0; i < rowNum; ++i) {
        ret = GmcStreamBeginOneWayWrite(1, tableName, sizeof(RdStreamTable2DataT), (void **)&v1);
        ASSERT_EQ(GMERR_OK, ret);
        v1->id = id[i];
        v1->water_mark = water_mark[i];
        v1->event_time = event_time[i];
        (void)snprintf_s((char *)v1->name, RD_STREAM_TABLE_FIX_FIELD_SIZE, RD_STREAM_TABLE_FIX_FIELD_SIZE - 1,
            "name_%0ld", id[i]);
        GmcStreamEndOneWayWrite(false);
        usleep(10 * 1000);
    }
}

// 结构化写
RdStreamTable2DataT *RdStreamT2PrepareData(int64_t id, int64_t water_mark, int64_t event_time)
{
    RdStreamTable2DataT *t1 = (RdStreamTable2DataT *)malloc(sizeof(RdStreamTable2DataT));
    if (t1 == NULL) {
        RD_ERROR("Unable to malloc stream data.");
        return NULL;
    }
    (void)memset_s(t1, sizeof(RdStreamTable2DataT), 0x0, sizeof(RdStreamTable2DataT));

    *t1 = (RdStreamTable2DataT) {
        .id = id,
        .name = {0},
        .water_mark = water_mark,
        .event_time = event_time,
    };
    (void)snprintf_s(t1->name, RD_STREAM_TABLE_T1_NAME_SIZE, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", id);
    return t1;
}

void  RdStructWriteStreamTable(GmcStmtT *stmt, RdVertexLabelT *vertexLabel, uint32_t rowNum, int64_t id[],
    int64_t water_mark[], int64_t event_time[])
{
    uint32_t modelType = GMC_MODEL_STREAM;
    uint32_t ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamTable2DataT *t1 = NULL;
    for (uint32_t i = 0; i < rowNum; i++) {
        t1 = RdStreamT2PrepareData(id[i], water_mark[i], event_time[i]);
        if (t1 == NULL) {
            RD_ERROR("Unable to prepare data, i = %d.", i);
            ret = RD_FAILED;
            break;
        }
        ret = RdStreamSetVertexWithBuf(stmt, vertexLabel, t1);
        RD_LOG_AND_BREAK_IF_ERROR(ret, "Unable to prepare data, i = %d.", i);
        ret = GmcExecute(stmt);
        RD_LOG_AND_BREAK_IF_ERROR(ret, "Unable execute, i = %d.", i);
        free(t1);
        t1 = NULL;
    }
    if (t1 != NULL) {
        free(t1);
    }
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void  RdStructWriteStreamTable2(GmcStmtT *stmt, RdVertexLabelT *vertexLabel, uint32_t rowNum, int64_t id[],
    int64_t water_mark[], int64_t event_time[], bool DefaultInfo[])
{
    uint32_t modelType = GMC_MODEL_STREAM;
    uint32_t ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamTable2DataT *t1 = NULL;
    for (uint32_t i = 0; i < rowNum; i++) {
        t1 = RdStreamT2PrepareData(id[i], water_mark[i], event_time[i]);
        if (t1 == NULL) {
            RD_ERROR("Unable to prepare data, i = %d.", i);
            ret = RD_FAILED;
            break;
        }
        ret = RdStreamSetVertexWithBuf(stmt, vertexLabel, t1, DefaultInfo);
        RD_LOG_AND_BREAK_IF_ERROR(ret, "Unable to prepare data, i = %d.", i);
        ret = GmcExecute(stmt);
        RD_LOG_AND_BREAK_IF_ERROR(ret, "Unable execute, i = %d.", i);
        free(t1);
        t1 = NULL;
    }
    if (t1 != NULL) {
        free(t1);
    }
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// check data
void RdCheckDataInTSTableOfStreamTable(GmcStmtT *stmt, char *sqlCmd, uint32_t expRowNum, uint32_t expColNum,
    int64_t id[], int64_t water_mark[], int64_t event_time[], uint32_t tryTime = 100)
{
    // 查询ts1表中数据
    uint32_t modelType = GMC_MODEL_TS;
    uint32_t ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType));
    ASSERT_EQ(GMERR_OK, ret);

    // 检查记录数
    uint32_t rowsCount = 0;
    for (uint32_t tryNum = 0; tryNum < tryTime; ++tryNum) {
        ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &rowsCount, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (tryNum != tryTime - 1 && rowsCount != expRowNum) {
            usleep(500 * 1000);
            continue;
        }
        AW_MACRO_ASSERT_EQ_INT(expRowNum, rowsCount);
    }

    // 检查字段数
    uint32_t colsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &colsCount, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(expColNum, colsCount);

    uint32_t i = 0;
    bool eof = false;
    int64_t val = 0;
    uint32_t size = 0;
    bool isNull = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        AW_MACRO_ASSERT_EQ_INT((uint32_t)8, size);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // check id
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &val, &size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(id[i], val);

        // check name
        size = 50;
        char name[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        char expName[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        (void)snprintf_s(expName, RD_STREAM_TABLE_T1_NAME_SIZE, RD_STREAM_TABLE_T1_NAME_SIZE - 1, "name_%0ld", id[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &name, &size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(0, strcmp(expName, name));

        // check water_mark
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &val, &size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(water_mark[i], val);

        // check event_time
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &val, &size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(event_time[i], val);

        i++;
    }
}

void RdCheckDataInTSTableOfStreamTable2(GmcStmtT *stmt, char *sqlCmd, uint32_t expRowNum, uint32_t expColNum,
    int64_t id[], int64_t water_mark[], int64_t event_time[], int64_t func[], uint32_t tryTime = 10)
{
    // 查询ts1表中数据
    uint32_t modelType = GMC_MODEL_TS;
    uint32_t ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType));
    ASSERT_EQ(GMERR_OK, ret);

    for (uint32_t tryNum = 0; tryNum < tryTime; ++tryNum) {
        ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 检查记录数
        uint32_t rowsCount = 0;
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &rowsCount, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (tryNum != tryTime - 1 && rowsCount != expRowNum) {
            usleep(500 * 1000);
            continue;
        }
        AW_MACRO_ASSERT_EQ_INT(expRowNum, rowsCount);
    }

    // 检查字段数
    uint32_t colsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &colsCount, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(expColNum, colsCount);

    uint32_t i = 0;
    bool eof = false;
    int64_t val = 0;
    uint32_t size = 0;
    bool isNull = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        AW_MACRO_ASSERT_EQ_INT((uint32_t)8, size);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // check id
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &val, &size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(id[i], val);

        // check name
        size = 50;
        char name[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        char expName[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        (void)snprintf_s(expName, RD_STREAM_TABLE_T1_NAME_SIZE, RD_STREAM_TABLE_T1_NAME_SIZE - 1, "name_%0ld", id[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &name, &size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(0, strcmp(expName, name));

        // check water_mark
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &val, &size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(water_mark[i], val);

        // check event_time
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &val, &size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(event_time[i], val);

        // check seq_distinct_count/ref
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &val, &size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(func[i], val);

        i++;
    }
}

// 查询ts表中数据，查到相应数据条数才继续执行后面
void RdCheckDataInTSTableOfStreamTableRow(GmcStmtT *stmt, char *sqlCmd, uint32_t expRowNum, uint32_t tryTime = 100)
{
    // 检查记录数
    uint32_t rowsCount = 0;
    int32_t ret;
    for (uint32_t tryNum = 0; tryNum < tryTime; ++tryNum) {
        ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &rowsCount, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        printf("rowsCount::%d\n", rowsCount);
        if (tryNum != tryTime - 1 && rowsCount != expRowNum) {
            usleep(500 * 1000);
            continue;
        }
        if (rowsCount = expRowNum) {
            break;
        }
        AW_MACRO_ASSERT_EQ_INT(expRowNum, rowsCount);
    }
}

// thread function
typedef struct {
    string tableName;
    int64_t beginIdx;
    int64_t endIdx;
    GmcStmtT *stmt = NULL;
}RdWriteStreamTable;

void *StreamPollingWriteSimpleTuple(void *args)
{
    RdWriteStreamTable *testArgs = static_cast<RdWriteStreamTable *>(args);
    Status ret;
    RdStreamTable2DataT *v1 = NULL;
    for (int64_t value = testArgs->beginIdx; value <= testArgs->endIdx; value++) {
        ret = GmcStreamBeginOneWayWrite(1, testArgs->tableName.c_str(), sizeof(RdStreamTable2DataT), (void **)&v1);
        if (ret != GMERR_OK) {
            uint32_t i = 0;
            while (ret != GMERR_OK && i < 1000000) {
                usleep(200);
                ret = GmcStreamBeginOneWayWrite(1, testArgs->tableName.c_str(), sizeof(RdStreamTable2DataT),
                    (void **)&v1);
                i++;
            }
        }
        EXPECT_EQ(GMERR_OK, ret);
        v1->id = value;
        v1->water_mark = value;
        v1->event_time = value;
        (void)snprintf_s((char *)v1->name, RD_STREAM_TABLE_FIX_FIELD_SIZE, RD_STREAM_TABLE_FIX_FIELD_SIZE - 1,
            "name_%0ld", value);
        GmcStreamEndOneWayWrite(false);
        usleep(5 * 1000);
    }
}

void *StreamPollingWriteTestBegin1(void *args)
{
    RdWriteStreamTable *testArgs = static_cast<RdWriteStreamTable *>(args);
    Status ret;
    RdStreamTable2DataT *v1 = NULL;
    int64_t tryTime = 10;
    while (tryTime > 0) {
        ret = GmcStreamBeginOneWayWrite(1, testArgs->tableName.c_str(), sizeof(RdStreamTable2DataT), (void **)&v1);
        if (ret == GMERR_OK) {
            break;
        }
        usleep(100 * 1000);
        --tryTime;
    }
    EXPECT_EQ(GMERR_OK, ret);
    lock_guard<mutex> lock(mtx);  // 加锁
    funcCompleted = true;  // 设置条件为true
    cv.notify_one();
}

void *StreamPollingWriteTestBegin2(void *args)
{
    {
        // 等待线程1完成func
        unique_lock<mutex> lock(mtx);  // 加锁
        cv.wait(lock, [&] { return funcCompleted; });  // 等待条件变量
    }

    RdWriteStreamTable *testArgs = static_cast<RdWriteStreamTable *>(args);
    Status ret;
    RdStreamTable2DataT *v1 = NULL;
    for (int64_t value = testArgs->beginIdx; value <= testArgs->endIdx; value++) {
        ret = GmcStreamBeginOneWayWrite(1, testArgs->tableName.c_str(), sizeof(RdStreamTable2DataT), (void **)&v1);
        EXPECT_EQ(GMERR_CONNECTION_SEND_BUFFER_FULL, ret);
        ret = GmcStreamBeginOneWayWrite(1, testArgs->tableName.c_str(), sizeof(RdStreamTable2DataT), (void **)&v1);
        EXPECT_EQ(GMERR_CONNECTION_SEND_BUFFER_FULL, ret);
        ret = GmcStreamBeginOneWayWrite(1, testArgs->tableName.c_str(), sizeof(RdStreamTable2DataT), (void **)&v1);
        EXPECT_EQ(GMERR_CONNECTION_SEND_BUFFER_FULL, ret);
        sleep(2);
        int64_t tryTime = 10;
        while (tryTime > 0) {
            ret = GmcStreamBeginOneWayWrite(1, testArgs->tableName.c_str(), sizeof(RdStreamTable2DataT), (void **)&v1);
            if (ret == GMERR_OK) {
                break;
            }
            usleep(100 * 1000);
            --tryTime;
        }
        v1->id = value;
        v1->water_mark = value;
        v1->event_time = value;
        (void)snprintf_s((char *)v1->name, RD_STREAM_TABLE_FIX_FIELD_SIZE, RD_STREAM_TABLE_FIX_FIELD_SIZE - 1,
            "name_%0ld", value);
        GmcStreamEndOneWayWrite(false);
    }
}

void *StreamPollingWriteTestEnd1(void *args)
{
    RdWriteStreamTable *testArgs = static_cast<RdWriteStreamTable *>(args);
    Status ret;
    RdStreamTable2DataT *v1 = NULL;
    for (uint32_t i = 0; i < 1000; ++i) {
        GmcStreamEndOneWayWrite(false);
    }
}

void *StreamPollingWriteTestEnd2(void *args)
{
    RdWriteStreamTable *testArgs = static_cast<RdWriteStreamTable *>(args);
    Status ret;
    RdStreamTable2DataT *v1 = NULL;
    for (int64_t value = testArgs->beginIdx; value <= testArgs->endIdx; value++) {
        ret = GmcStreamBeginOneWayWrite(1, testArgs->tableName.c_str(), sizeof(RdStreamTable2DataT), (void **)&v1);
        EXPECT_EQ(GMERR_OK, ret);
        v1->id = value;
        v1->water_mark = value;
        v1->event_time = value;
        (void)snprintf_s((char *)v1->name, RD_STREAM_TABLE_FIX_FIELD_SIZE, RD_STREAM_TABLE_FIX_FIELD_SIZE - 1,
            "name_%0ld", value);
        GmcStreamEndOneWayWrite(false);
    }
}

#endif /* end of MINI_STREAM_REFERENCE_STRUCT_H */
