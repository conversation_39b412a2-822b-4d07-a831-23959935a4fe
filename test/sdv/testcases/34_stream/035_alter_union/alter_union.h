/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: 支持alter union 功能测试
 * Author: y<PERSON>ijin
 * Create: 2025-2-26
 */
#ifndef STREAM_TABLE_STRUCT_H
#define STREAM_TABLE_STRUCT_H 1
#include "rd_feature_stream.h"

#define RD_STREAM_TABLE_T1_NAME_SIZE 50
#define RD_STREAM_TABLE_FIX_FIELD_SIZE 50
#define STREAM_REFERENCE_MAX_SQL_LEN 512

/**
 * @brief 定义结构化表数据
 * @attention 定义结构体必须满足既定约定，否则行为未定义
 *  1、通过#pragma pack(1)控制结构体按1字节对齐
 *  2、变长字段的上一个成员是该字段的长度，长度必须使用uint16定义
 *  3、定长字段不需要定义长度
 *  4、fixed类型（即流表中的CHAR类型）不需要定义长度，但必须使用数组，且数组长度和表定义的大小一致
 *  5、结构体必须和表定义中的列一一对应，否则行为未定义
 *  6、变长字段申请的内存必须在写入数据后手动释放，否则将导致内存泄露
 */

#pragma pack(1)

typedef struct {
    RdVertexLabelT *vertexLabel;
    int32_t status;
    int64_t rowStart;
    int64_t rowNum;
    int64_t colNum;
} RdStreamStructWriteCtxT;

typedef struct {
    int64_t id;
    char name[RD_STREAM_TABLE_FIX_FIELD_SIZE]; // 定长类型，使用定长数组，不需要单独定义长度
    int64_t water_mark;
    int64_t event_time;
    int64_t ref_key;
    uint16_t descriptionLen;
    char *description;
} RdStreamTable2DataT;

typedef struct {
    int64_t id;
    char name[RD_STREAM_TABLE_FIX_FIELD_SIZE]; // 定长类型，使用定长数组，不需要单独定义长度
    int64_t system_time;
    char hostname[RD_STREAM_TABLE_T1_NAME_SIZE];
    int64_t ref_key;
    uint16_t descriptionLen;
    char *description;
} RdStreamTable2DataT2;
#pragma pack()

// 结构化写入数据StreamTable
void  RdStructWriteStreamTable(GmcStmtT *stmt, RdVertexLabelT *vertexLabel, int64_t rowNum,
    int64_t id[], int64_t water_mark[], int64_t event_time[], int64_t ref_key[],
    uint16_t descriptionLen[], char *description[], bool nullInfo[])
{
    int32_t ret;
    ret = GmcPrepareStmtByLabelName(stmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamTable2DataT *t1 = NULL;
    for (uint32_t i = 0; i < rowNum; i++) {
        t1 = (RdStreamTable2DataT *)malloc(sizeof(RdStreamTable2DataT));
        if (t1 == NULL) {
            RD_ERROR("Unable to prepare data, i = %d.", i);
            ret = RD_FAILED;
            break;
        }
        (void)memset_s(t1, sizeof(RdStreamTable2DataT), 0x0, sizeof(RdStreamTable2DataT));
        
        *t1 = (RdStreamTable2DataT) {
            .id = id[i],
            .name = {0},
            .water_mark = water_mark[i],
            .event_time = event_time[i],
            .ref_key = ref_key[i],
            .descriptionLen = descriptionLen[i],
            .description = description[i],
        };
        (void)snprintf_s(t1->name, RD_STREAM_TABLE_FIX_FIELD_SIZE, RD_STREAM_TABLE_FIX_FIELD_SIZE, "name_%0ld", id[i]);

        ret = RdStreamSetVertexWithBuf(stmt, vertexLabel, t1, nullInfo);
        RD_LOG_AND_BREAK_IF_ERROR(ret, "Unable to prepare data, i = %d.", i);
        ret = GmcExecute(stmt);
        RD_LOG_AND_BREAK_IF_ERROR(ret, "Unable execute, i = %d.", i);
        free(t1);
        t1 = NULL;
    }
    if (t1 != NULL) {
        free(t1);
    }
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void  RdStructWriteStreamTable2(GmcStmtT *stmt, RdVertexLabelT *vertexLabel, int64_t rowNum,
    int64_t id[], int64_t ref_key[], uint16_t descriptionLen[], char *description[], bool nullInfo[])
{
    int32_t ret;
    ret = GmcPrepareStmtByLabelName(stmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamTable2DataT2 *t1 = NULL;
    for (uint32_t i = 0; i < rowNum; i++) {
        t1 = (RdStreamTable2DataT2 *)malloc(sizeof(RdStreamTable2DataT2));
        if (t1 == NULL) {
            RD_ERROR("Unable to prepare data, i = %d.", i);
            ret = RD_FAILED;
            break;
        }
        (void)memset_s(t1, sizeof(RdStreamTable2DataT2), 0x0, sizeof(RdStreamTable2DataT2));
        
        //*t1 = (RdStreamTable2DataT) {
            t1->id = id[i],
            t1->ref_key = ref_key[i],
            t1->descriptionLen = descriptionLen[i],
            t1->description = description[i],
        //};
        (void)snprintf_s(t1->name, RD_STREAM_TABLE_FIX_FIELD_SIZE, RD_STREAM_TABLE_FIX_FIELD_SIZE, "name_%0ld", id[i]);

        ret = RdStreamSetVertexWithBuf(stmt, vertexLabel, t1, nullInfo);
        RD_LOG_AND_BREAK_IF_ERROR(ret, "Unable to prepare data, i = %d.", i);
        ret = GmcExecute(stmt);
        RD_LOG_AND_BREAK_IF_ERROR(ret, "Unable execute, i = %d.", i);
        free(t1);
        t1 = NULL;
    }
    if (t1 != NULL) {
        free(t1);
    }
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 校验
void RdCheckDataInTSTableOfStreamTable(GmcStmtT *stmt, char *sqlCmd, uint32_t expRowNum, uint32_t expColNum,
    int64_t id[], int64_t water_mark[], int64_t event_time[], int64_t ref_key[],
    uint16_t descriptionLen[], char *description[])
{
    // 查询ts1表中数据
    uint32_t modelType = GMC_MODEL_TS;
    uint32_t ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    // 检查记录数
    uint32_t rowsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &rowsCount, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(expRowNum, rowsCount);

    // 检查字段数
    uint32_t colsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &colsCount, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(expColNum, colsCount);

    uint32_t i = 0;
    bool eof = false;
    int64_t val = 0;
    uint32_t size = 0;
    bool isNull = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        AW_MACRO_ASSERT_EQ_INT((uint32_t)8, size);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // check id
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &val, &size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(id[i], val);

        // check name
        size = 50;
        char name[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        char expName[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        (void)snprintf_s(expName, RD_STREAM_TABLE_T1_NAME_SIZE, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", id[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &name, &size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(0, strcmp(expName, name));

        // check water_mark
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &val, &size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(water_mark[i], val);
        
        // check event_time
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &val, &size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(event_time[i], val);

        // check ref_key
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &val, &size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(ref_key[i], val);

        // check description
        size = descriptionLen[i];
        char getDescription[size] = {0};
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 5, &getDescription, &size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(0, strcmp(getDescription, description[i]));

        i++;
    }
}

void RdCheckDataInTSTableOfStreamTable2(GmcStmtT *stmt, char *sqlCmd, uint32_t expRowNum, uint32_t expColNum,
    int64_t id[], int64_t water_mark[], int64_t event_time[], int64_t ref_key[], int64_t ref_val[],
    uint16_t descriptionLen[], char *description[])
{
    // 查询ts1表中数据
    uint32_t modelType = GMC_MODEL_TS;
    uint32_t ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    // 检查记录数
    uint32_t rowsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &rowsCount, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(expRowNum, rowsCount);

    // 检查字段数
    uint32_t colsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &colsCount, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(expColNum, colsCount);

    uint32_t i = 0;
    bool eof = false;
    int64_t val = 0;
    uint32_t size = 0;
    bool isNull = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        AW_MACRO_ASSERT_EQ_INT((uint32_t)8, size);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // check id
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &val, &size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(id[i], val);

        // check name
        size = 50;
        char name[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        char expName[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        (void)snprintf_s(expName, RD_STREAM_TABLE_T1_NAME_SIZE, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", id[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &name, &size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(0, strcmp(expName, name));

        // check water_mark
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &val, &size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(water_mark[i], val);
        
        // check event_time
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &val, &size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(event_time[i], val);

        // check ref_key
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &val, &size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(ref_key[i], val);

        // check ref_val
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 5, &val, &size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(ref_val[i], val);

        // check description
        size = descriptionLen[i];
        char getDescription[size] = {0};
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 6, &getDescription, &size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(0, strcmp(getDescription, description[i]));

        i++;
    }
}

void RdCheckDataInTSTableOfStreamTable3(GmcStmtT *stmt, char *sqlCmd, uint32_t expRowNum, uint32_t expColNum,
    int64_t id[], int64_t system_time, char *expHostname, int64_t ref_key[],
    uint16_t descriptionLen[], char *description[])
{
    // 查询ts1表中数据
    uint32_t modelType = GMC_MODEL_TS;
    uint32_t ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    // 检查记录数
    uint32_t rowsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &rowsCount, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(expRowNum, rowsCount);

    // 检查字段数
    uint32_t colsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &colsCount, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(expColNum, colsCount);

    uint32_t i = 0;
    bool eof = false;
    int64_t val = 0;
    uint32_t size = 0;
    bool isNull = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        AW_MACRO_ASSERT_EQ_INT((uint32_t)8, size);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // check id
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &val, &size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(id[i], val);

        // check name
        size = 50;
        char name[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        char expName[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        (void)snprintf_s(expName, RD_STREAM_TABLE_T1_NAME_SIZE, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", id[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &name, &size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(0, strcmp(expName, name));

        // check system_time
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &val, &size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ASSERT_LE(system_time - val, 3);
        
        // check hostname
        size = 50;
        char hostname[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &hostname, &size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(0, strcmp(expHostname, hostname));

        // check ref_key
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &val, &size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(ref_key[i], val);

        // check description
        size = descriptionLen[i];
        char getDescription[size] = {0};
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 5, &getDescription, &size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(0, strcmp(getDescription, description[i]));

        i++;
    }
}

void RdCheckDataInTSTableOfStreamTable4(GmcStmtT *stmt, char *sqlCmd, uint32_t expRowNum, uint32_t expColNum,
    int64_t id[], int64_t water_mark[], int64_t event_time[], int64_t ref_key[],
    uint16_t descriptionLen[], char *description[], int64_t seq_distinct_count[])
{
    // 查询ts1表中数据
    uint32_t modelType = GMC_MODEL_TS;
    uint32_t ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    // 检查记录数
    uint32_t rowsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &rowsCount, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(expRowNum, rowsCount);

    // 检查字段数
    uint32_t colsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &colsCount, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(expColNum, colsCount);

    uint32_t i = 0;
    bool eof = false;
    int64_t val = 0;
    uint32_t size = 0;
    bool isNull = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        AW_MACRO_ASSERT_EQ_INT((uint32_t)8, size);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // check id
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &val, &size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(id[i], val);

        // check name
        size = 50;
        char name[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        char expName[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        (void)snprintf_s(expName, RD_STREAM_TABLE_T1_NAME_SIZE, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", id[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &name, &size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(0, strcmp(expName, name));

        // check water_mark
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &val, &size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(water_mark[i], val);
        
        // check event_time
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &val, &size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(event_time[i], val);

        // check ref_key
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &val, &size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(ref_key[i], val);

        // check description
        size = descriptionLen[i];
        char getDescription[size] = {0};
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 5, &getDescription, &size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(0, strcmp(getDescription, description[i]));

        // check seq_distinct_count
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 6, &val, &size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(seq_distinct_count[i], val);

        i++;
    }
}

void RdCheckDataInTSTableOfStreamTable5(GmcStmtT *stmt, char *sqlCmd, uint32_t expectRowsCount, uint32_t expectColsCount,
    int64_t id[], int64_t water_mark, int64_t event_time[], int64_t system_time[], uint32_t timeDiff)
{
    // 查询ts1表中数据
    uint32_t modelType = GMC_MODEL_TS;
    uint32_t ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查记录数
    uint32_t rowsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &rowsCount, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(expectRowsCount, rowsCount);

    // 检查字段数
    uint32_t colsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &colsCount, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(expectColsCount, colsCount);

    uint32_t i = 0, j = 0;
    bool eof = false;
    int64_t val = 0;
    uint32_t size = 0;
    bool isNull = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        AW_MACRO_ASSERT_EQ_INT((uint32_t)8, size);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        
        // check id
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &val, &size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(id[i], val);

        // check name
        size = 50;
        char name[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        char expectName[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        (void)snprintf_s(expectName, RD_STREAM_TABLE_T1_NAME_SIZE, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld",
            id[i] + 0);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &name, &size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(0, strcmp(expectName, name));

        // check water_mark
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &val, &size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(water_mark, val);

        // check event_time
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &val, &size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(event_time[i], val);

        // check system_time
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &val, &size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(system_time[i], val);

        i++;
    }

}

#define RD_STREAM_WORKER_RETURN_IF_ERROR(ret, ctx, format, ...)                                                        \
    do {                                                                                                               \
        if ((ret) != 0) {                                                                                              \
            RD_ERROR("[" #ret "-%d] " format "", ret, ##__VA_ARGS__);                                                  \
            (ctx)->status = ret;                                                                                       \
            return NULL;                                                                                               \
        }                                                                                                              \
    } while (0)

void *RdStreamStructWriteWorker(void *args)
{
    RdStreamStructWriteCtxT *ctx = (RdStreamStructWriteCtxT *)args;
    RD_INFO("vertexLabel = %p, name = %s", ctx->vertexLabel, ctx->vertexLabel->topRecordName);
    int32_t ret;
    /**/GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = RdGmcConnect(&conn, &stmt);
    RD_STREAM_WORKER_RETURN_IF_ERROR(ret, ctx, "Unabel to connect to db.");
    uint32_t modelStream = GMC_MODEL_STREAM;
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelStream, sizeof(modelStream));
    RD_STREAM_WORKER_RETURN_IF_ERROR(ret, ctx, "Unabel to set stmt model.");

    ret = GmcPrepareStmtByLabelName(stmt, ctx->vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    int64_t id[ctx->rowNum] = {0};
    int64_t water_mark[ctx->rowNum] = {0};
    int64_t event_time[ctx->rowNum] = {0};
    int64_t ref_key[ctx->rowNum] = {0};
    char desText[][128] = {"test data: 1", "test data: 12", "test data: 123", "test data: 1234", "test data: 12345"};
    char *description[ctx->rowNum] = {0};
    uint16_t descriptionLen[ctx->rowNum] = {0};
    for (int i = 0; i < ctx->rowNum; ++i) {
        description[i] = desText[i];
        descriptionLen[i] = strlen(desText[i]) + 1;
    }
    bool nullInfo[ctx->colNum] = {false, false, false, false, false, false};
    for (int64_t i = ctx->rowStart, j = 0; i < ctx->rowStart + ctx->rowNum; i++, j++) {
        id[j] = i;
        water_mark[j] = i;
        event_time[j] = i;
        ref_key[j] = i;
    }
    RdStructWriteStreamTable(stmt, ctx->vertexLabel, ctx->rowNum, id, water_mark, event_time, ref_key, descriptionLen, description, nullInfo);

    ret = RdGmcDisconnect(conn, stmt);
    RD_STREAM_WORKER_RETURN_IF_ERROR(ret, ctx, "Unabel to disconnect from db.");
    return NULL;
}

#endif /* end of MINI_STREAM_REFERENCE_STRUCT_H */
