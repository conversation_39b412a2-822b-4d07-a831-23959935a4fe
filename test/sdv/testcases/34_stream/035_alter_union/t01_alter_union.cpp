/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: 支持alter union 功能测试
 * Author: yushijin
 * Create: 2025-2-26
 */
#include "gtest/gtest.h"
#include "securec.h"
#include "gmc_sql.h"
#include "gmc_persist.h"
#include "alter_union.h"
#include <sys/time.h>
#include <string>
#include <mutex>
#include <condition_variable>

using namespace std;

mutex mtx;                   // 用于保护共享变量
condition_variable cv;       // 条件变量
bool funcCompleted = false;  // 表示线程1是否执行完func

class t01_alter_union : public testing::Test {
public:
    static GmcConnT *conn;
    static GmcStmtT *stmt;

    static void SetUpTestCase();
    static void TearDownTestCase();
    virtual void SetUp();
    virtual void TearDown();
};

GmcConnT *t01_alter_union::conn = NULL;
GmcStmtT *t01_alter_union::stmt = NULL;

void t01_alter_union::SetUpTestCase()
{
    int32_t ret;
    ret = RdStreamEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void t01_alter_union::TearDownTestCase()
{
    int32_t ret = RdStreamEnvClean();
    EXPECT_EQ(GMERR_OK, ret);
}

void t01_alter_union::SetUp()
{
    int32_t ret = RdGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void t01_alter_union::TearDown()
{
    int32_t ret = RdGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// view union两个schema相同的stream表，使用alter union删除一个，写入数据
TEST_F(t01_alter_union, STREAM_035_001)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer, ref_key integer, "
         "description text) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');",
            GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text);"},
        {"create stream table stream2 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text);"},
        {"create stream view view1 AS select * FROM TABLE(UNION(TABLE stream1, TABLE stream2));"},
        {"create stream sink sink1 AS select * from view1 into tsdb(ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    string alterCmd = "alter stream view view1 alter from_union drop stream2;";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel1 = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel1);

    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 6;
    int64_t id[rowNum] = {1, 2, 3, 4, 5};
    int64_t water_mark[rowNum] = {11, 12, 13, 14, 15};
    int64_t event_time[rowNum] = {21, 22, 23, 24, 25};
    int64_t ref_key[rowNum] = {1, 1, 1, 1, 1};
    char desText[][128] = {"test data: 1", "test data: 12", "test data: 123", "test data: 1234", "test data: 12345"};
    char *description[rowNum] = {0};
    uint16_t descriptionLen[rowNum] = {0};
    for (int i = 0; i < rowNum; ++i) {
        description[i] = desText[i];
        descriptionLen[i] = strlen(desText[i]) + 1;
    }
    bool nullInfo[colNum] = {false, false, false, false, false, false};
    RdStructWriteStreamTable(
        stmt, vertexLabel1, rowNum, id, water_mark, event_time, ref_key, descriptionLen, description, nullInfo);

    // 查询ts表中数据
    char *qryCmd = (char *)"select * from ts1 order by id;";
    uint32_t expRowNum = 5;
    uint32_t expColNum = 6;
    int64_t exp_id[expRowNum] = {1, 2, 3, 4, 5};
    int64_t exp_water_mark[expRowNum] = {11, 12, 13, 14, 15};
    int64_t exp_event_time[expRowNum] = {21, 22, 23, 24, 25};
    int64_t exp_ref_key[expRowNum] = {1, 1, 1, 1, 1};
    char *exp_description[expRowNum] = {desText[0], desText[1], desText[2], desText[3], desText[4]};
    uint16_t exp_descriptionLen[expRowNum] = {
        descriptionLen[0], descriptionLen[1], descriptionLen[2], descriptionLen[3], descriptionLen[4]};
    RdCheckDataInTSTableOfStreamTable(stmt, qryCmd, expRowNum, expColNum, exp_id, exp_water_mark, exp_event_time,
        exp_ref_key, exp_descriptionLen, exp_description);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop stream table stream2;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel1);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// view映射一个stream表，使用alter union新增一个相同schema的stream表，写入数据
TEST_F(t01_alter_union, STREAM_035_002)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer, ref_key integer, "
         "description text) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');",
            GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text);"},
        {"create stream table stream2 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text);"},
        {"create stream view view1 AS select * FROM stream1;"},
        {"create stream sink sink1 AS select * from view1 into tsdb(ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    string alterCmd = "alter stream view view1 alter from_union add stream2;";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel1 = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel1);
    RdVertexLabelT *vertexLabel2 = RdStreamParseTableSchema(creates[2].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel2);

    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 6;
    int64_t id[rowNum] = {1, 2, 3, 4, 5};
    int64_t water_mark[rowNum] = {11, 12, 13, 14, 15};
    int64_t event_time[rowNum] = {21, 22, 23, 24, 25};
    int64_t ref_key[rowNum] = {1, 1, 1, 1, 1};
    char desText[][128] = {"test data: 1", "test data: 12", "test data: 123", "test data: 1234", "test data: 12345"};
    char *description[rowNum] = {0};
    uint16_t descriptionLen[rowNum] = {0};
    for (int i = 0; i < rowNum; ++i) {
        description[i] = desText[i];
        descriptionLen[i] = strlen(desText[i]) + 1;
    }
    bool nullInfo[colNum] = {false, false, false, false, false, false};
    RdStructWriteStreamTable(
        stmt, vertexLabel1, rowNum, id, water_mark, event_time, ref_key, descriptionLen, description, nullInfo);

    int64_t id2[rowNum] = {6, 7, 8, 9, 10};
    int64_t water_mark2[rowNum] = {16, 17, 18, 19, 20};
    int64_t event_time2[rowNum] = {26, 27, 28, 29, 30};
    int64_t ref_key2[rowNum] = {2, 2, 2, 2, 2};
    RdStructWriteStreamTable(
        stmt, vertexLabel2, rowNum, id2, water_mark2, event_time2, ref_key2, descriptionLen, description, nullInfo);

    // 查询ts表中数据
    char *qryCmd = (char *)"select * from ts1 order by id;";
    uint32_t expRowNum = 10;
    uint32_t expColNum = 6;
    int64_t exp_id[expRowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    int64_t exp_water_mark[expRowNum] = {11, 12, 13, 14, 15, 16, 17, 18, 19, 20};
    int64_t exp_event_time[expRowNum] = {21, 22, 23, 24, 25, 26, 27, 28, 29, 30};
    int64_t exp_ref_key[expRowNum] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2};
    char *exp_description[expRowNum] = {desText[0], desText[1], desText[2], desText[3], desText[4], desText[0],
        desText[1], desText[2], desText[3], desText[4]};
    uint16_t exp_descriptionLen[expRowNum] = {descriptionLen[0], descriptionLen[1], descriptionLen[2],
        descriptionLen[3], descriptionLen[4], descriptionLen[0], descriptionLen[1], descriptionLen[2],
        descriptionLen[3], descriptionLen[4]};
    RdCheckDataInTSTableOfStreamTable(stmt, qryCmd, expRowNum, expColNum, exp_id, exp_water_mark, exp_event_time,
        exp_ref_key, exp_descriptionLen, exp_description);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop stream table stream2;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel1);
    RdStreamFreeTableSchema(vertexLabel2);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// sink union两个相同schema的view，使用alter union删除一个view，写入数据
TEST_F(t01_alter_union, STREAM_035_003)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer, ref_key integer, "
         "description text) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');",
            GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text);"},
        {"create stream view view1 AS select * FROM stream1;"},
        {"create stream view view2 AS select * FROM stream1;"},
        {"create stream sink sink1 AS select * from TABLE(UNION(TABLE view1, TABLE view2)) into tsdb(ts1) with "
         "(batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    string alterCmd = "alter stream sink sink1 alter from_union drop view2;";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel1 = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel1);

    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 6;
    int64_t id[rowNum] = {1, 2, 3, 4, 5};
    int64_t water_mark[rowNum] = {11, 12, 13, 14, 15};
    int64_t event_time[rowNum] = {21, 22, 23, 24, 25};
    int64_t ref_key[rowNum] = {1, 1, 1, 1, 1};
    char desText[][128] = {"test data: 1", "test data: 12", "test data: 123", "test data: 1234", "test data: 12345"};
    char *description[rowNum] = {0};
    uint16_t descriptionLen[rowNum] = {0};
    for (int i = 0; i < rowNum; ++i) {
        description[i] = desText[i];
        descriptionLen[i] = strlen(desText[i]) + 1;
    }
    bool nullInfo[colNum] = {false, false, false, false, false, false};
    RdStructWriteStreamTable(
        stmt, vertexLabel1, rowNum, id, water_mark, event_time, ref_key, descriptionLen, description, nullInfo);

    // 查询ts表中数据
    char *qryCmd = (char *)"select * from ts1 order by id;";
    uint32_t expRowNum = 5;
    uint32_t expColNum = 6;
    int64_t exp_id[expRowNum] = {1, 2, 3, 4, 5};
    int64_t exp_water_mark[expRowNum] = {11, 12, 13, 14, 15};
    int64_t exp_event_time[expRowNum] = {21, 22, 23, 24, 25};
    int64_t exp_ref_key[expRowNum] = {1, 1, 1, 1, 1};
    char *exp_description[expRowNum] = {desText[0], desText[1], desText[2], desText[3], desText[4]};
    uint16_t exp_descriptionLen[expRowNum] = {
        descriptionLen[0], descriptionLen[1], descriptionLen[2], descriptionLen[3], descriptionLen[4]};
    RdCheckDataInTSTableOfStreamTable(stmt, qryCmd, expRowNum, expColNum, exp_id, exp_water_mark, exp_event_time,
        exp_ref_key, exp_descriptionLen, exp_description);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream view view2"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel1);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// sink映射一个view表，使用alter union新增一个相同schema的view，写入数据
TEST_F(t01_alter_union, STREAM_035_004)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer, ref_key integer, "
         "description text) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');",
            GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text);"},
        {"create stream view view1 AS select * FROM stream1;"},
        {"create stream view view2 AS select * FROM stream1;"},
        {"create stream sink sink1 AS select * from view1 into tsdb(ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    string alterCmd = "alter stream sink sink1 alter from_union add view2;";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel1 = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel1);

    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 6;
    int64_t id[rowNum] = {1, 2, 3, 4, 5};
    int64_t water_mark[rowNum] = {11, 12, 13, 14, 15};
    int64_t event_time[rowNum] = {21, 22, 23, 24, 25};
    int64_t ref_key[rowNum] = {1, 1, 1, 1, 1};
    char desText[][128] = {"test data: 1", "test data: 12", "test data: 123", "test data: 1234", "test data: 12345"};
    char *description[rowNum] = {0};
    uint16_t descriptionLen[rowNum] = {0};
    for (int i = 0; i < rowNum; ++i) {
        description[i] = desText[i];
        descriptionLen[i] = strlen(desText[i]) + 1;
    }
    bool nullInfo[colNum] = {false, false, false, false, false, false};
    RdStructWriteStreamTable(
        stmt, vertexLabel1, rowNum, id, water_mark, event_time, ref_key, descriptionLen, description, nullInfo);

    // 查询ts表中数据
    char *qryCmd = (char *)"select * from ts1 order by id;";
    uint32_t expRowNum = 10;
    uint32_t expColNum = 6;
    int64_t exp_id[expRowNum] = {1, 1, 2, 2, 3, 3, 4, 4, 5, 5};
    int64_t exp_water_mark[expRowNum] = {11, 11, 12, 12, 13, 13, 14, 14, 15, 15};
    int64_t exp_event_time[expRowNum] = {21, 21, 22, 22, 23, 23, 24, 24, 25, 25};
    int64_t exp_ref_key[expRowNum] = {1, 1, 1, 1, 1, 1, 1, 1, 1, 1};
    char *exp_description[expRowNum] = {desText[0], desText[0], desText[1], desText[1], desText[2], desText[2],
        desText[3], desText[3], desText[4], desText[4]};
    uint16_t exp_descriptionLen[expRowNum] = {descriptionLen[0], descriptionLen[0], descriptionLen[1],
        descriptionLen[1], descriptionLen[2], descriptionLen[2], descriptionLen[3], descriptionLen[3],
        descriptionLen[4], descriptionLen[4]};
    RdCheckDataInTSTableOfStreamTable(stmt, qryCmd, expRowNum, expColNum, exp_id, exp_water_mark, exp_event_time,
        exp_ref_key, exp_descriptionLen, exp_description);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream view view2"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel1);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// sink映射一个流表，使用alter union添加一个相同schema的流表，写入数据
TEST_F(t01_alter_union, STREAM_035_005)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer, ref_key integer, "
         "description text) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');",
            GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text);"},
        {"create stream table stream2 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text);"},
        {"create stream sink sink1 AS select * from stream1 into tsdb(ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    string alterCmd = "alter stream sink sink1 alter from_union add stream2;";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel1 = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel1);
    RdVertexLabelT *vertexLabel2 = RdStreamParseTableSchema(creates[2].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel2);

    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 6;
    int64_t id[rowNum] = {1, 2, 3, 4, 5};
    int64_t water_mark[rowNum] = {11, 12, 13, 14, 15};
    int64_t event_time[rowNum] = {21, 22, 23, 24, 25};
    int64_t ref_key[rowNum] = {1, 1, 1, 1, 1};
    char desText[][128] = {"test data: 1", "test data: 12", "test data: 123", "test data: 1234", "test data: 12345"};
    char *description[rowNum] = {0};
    uint16_t descriptionLen[rowNum] = {0};
    for (int i = 0; i < rowNum; ++i) {
        description[i] = desText[i];
        descriptionLen[i] = strlen(desText[i]) + 1;
    }
    bool nullInfo[colNum] = {false, false, false, false, false, false};
    RdStructWriteStreamTable(
        stmt, vertexLabel1, rowNum, id, water_mark, event_time, ref_key, descriptionLen, description, nullInfo);

    int64_t id2[rowNum] = {6, 7, 8, 9, 10};
    int64_t water_mark2[rowNum] = {16, 17, 18, 19, 20};
    int64_t event_time2[rowNum] = {26, 27, 28, 29, 30};
    int64_t ref_key2[rowNum] = {2, 2, 2, 2, 2};
    RdStructWriteStreamTable(
        stmt, vertexLabel2, rowNum, id2, water_mark2, event_time2, ref_key2, descriptionLen, description, nullInfo);

    // 查询ts表中数据
    char *qryCmd = (char *)"select * from ts1 order by id;";
    uint32_t expRowNum = 10;
    uint32_t expColNum = 6;
    int64_t exp_id[expRowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    int64_t exp_water_mark[expRowNum] = {11, 12, 13, 14, 15, 16, 17, 18, 19, 20};
    int64_t exp_event_time[expRowNum] = {21, 22, 23, 24, 25, 26, 27, 28, 29, 30};
    int64_t exp_ref_key[expRowNum] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2};
    char *exp_description[expRowNum] = {desText[0], desText[1], desText[2], desText[3], desText[4], desText[0],
        desText[1], desText[2], desText[3], desText[4]};
    uint16_t exp_descriptionLen[expRowNum] = {descriptionLen[0], descriptionLen[1], descriptionLen[2],
        descriptionLen[3], descriptionLen[4], descriptionLen[0], descriptionLen[1], descriptionLen[2],
        descriptionLen[3], descriptionLen[4]};
    RdCheckDataInTSTableOfStreamTable(stmt, qryCmd, expRowNum, expColNum, exp_id, exp_water_mark, exp_event_time,
        exp_ref_key, exp_descriptionLen, exp_description);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream table stream1;"},
        {"drop stream table stream2;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel1);
    RdStreamFreeTableSchema(vertexLabel2);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// sink union多个流表，alter union删除其中一个，向剩余流表写入数据
TEST_F(t01_alter_union, STREAM_035_006)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer, ref_key integer, "
         "description text) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');",
            GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text);"},
        {"create stream table stream2 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text);"},
        {"create stream sink sink1 AS select * from TABLE(UNION(TABLE stream1, TABLE stream2)) into tsdb(ts1) with "
         "(batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    string alterCmd = "alter stream sink sink1 alter from_union drop stream2;";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel1 = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel1);

    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 6;
    int64_t id[rowNum] = {1, 2, 3, 4, 5};
    int64_t water_mark[rowNum] = {11, 12, 13, 14, 15};
    int64_t event_time[rowNum] = {21, 22, 23, 24, 25};
    int64_t ref_key[rowNum] = {1, 1, 1, 1, 1};
    char desText[][128] = {"test data: 1", "test data: 12", "test data: 123", "test data: 1234", "test data: 12345"};
    char *description[rowNum] = {0};
    uint16_t descriptionLen[rowNum] = {0};
    for (int i = 0; i < rowNum; ++i) {
        description[i] = desText[i];
        descriptionLen[i] = strlen(desText[i]) + 1;
    }
    bool nullInfo[colNum] = {false, false, false, false, false, false};
    RdStructWriteStreamTable(
        stmt, vertexLabel1, rowNum, id, water_mark, event_time, ref_key, descriptionLen, description, nullInfo);

    // 查询ts表中数据
    char *qryCmd = (char *)"select * from ts1 order by id;";
    uint32_t expRowNum = 5;
    uint32_t expColNum = 6;
    int64_t exp_id[expRowNum] = {1, 2, 3, 4, 5};
    int64_t exp_water_mark[expRowNum] = {11, 12, 13, 14, 15};
    int64_t exp_event_time[expRowNum] = {21, 22, 23, 24, 25};
    int64_t exp_ref_key[expRowNum] = {1, 1, 1, 1, 1};
    char *exp_description[expRowNum] = {desText[0], desText[1], desText[2], desText[3], desText[4]};
    uint16_t exp_descriptionLen[expRowNum] = {
        descriptionLen[0], descriptionLen[1], descriptionLen[2], descriptionLen[3], descriptionLen[4]};
    RdCheckDataInTSTableOfStreamTable(stmt, qryCmd, expRowNum, expColNum, exp_id, exp_water_mark, exp_event_time,
        exp_ref_key, exp_descriptionLen, exp_description);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream table stream1;"},
        {"drop stream table stream2;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel1);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// view带with子句，使用alter union添加schema相同的新表
TEST_F(t01_alter_union, STREAM_035_007)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer, mini_ref_key integer, "
         "description text) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');",
            GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer, mini_ref_key "
         "integer, description text);"},
        {"create stream table stream2 (id integer, name char(50), water_mark integer, event_time integer, mini_ref_key "
         "integer, description text);"},
        {"create stream view view1 AS select * FROM stream1 with (tuple_buffer_size = 15);"},
        {"create stream sink sink1 AS select * from view1 into tsdb(ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    string alterCmd = "alter stream view view1 alter from_union add stream2;";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel1 = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel1);
    RdVertexLabelT *vertexLabel2 = RdStreamParseTableSchema(creates[2].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel2);

    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 6;
    int64_t id[rowNum] = {1, 2, 3, 4, 5};
    int64_t water_mark[rowNum] = {11, 12, 13, 14, 15};
    int64_t event_time[rowNum] = {21, 22, 23, 24, 25};
    int64_t ref_key[rowNum] = {1, 1, 1, 1, 1};
    char desText[][128] = {"test data: 1", "test data: 12", "test data: 123", "test data: 1234", "test data: 12345"};
    char *description[rowNum] = {0};
    uint16_t descriptionLen[rowNum] = {0};
    for (int i = 0; i < rowNum; ++i) {
        description[i] = desText[i];
        descriptionLen[i] = strlen(desText[i]) + 1;
    }
    bool nullInfo[colNum] = {false, false, false, false, false, false};
    RdStructWriteStreamTable(
        stmt, vertexLabel1, rowNum, id, water_mark, event_time, ref_key, descriptionLen, description, nullInfo);

    int64_t id2[rowNum] = {6, 7, 8, 9, 10};
    int64_t water_mark2[rowNum] = {16, 17, 18, 19, 20};
    int64_t event_time2[rowNum] = {26, 27, 28, 29, 30};
    int64_t ref_key2[rowNum] = {2, 2, 2, 2, 2};
    RdStructWriteStreamTable(
        stmt, vertexLabel2, rowNum, id2, water_mark2, event_time2, ref_key2, descriptionLen, description, nullInfo);

    // 查询ts表中数据
    char *qryCmd = (char *)"select * from ts1 order by id;";
    uint32_t expRowNum = 10;
    uint32_t expColNum = 6;
    int64_t exp_id[expRowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    int64_t exp_water_mark[expRowNum] = {11, 12, 13, 14, 15, 16, 17, 18, 19, 20};
    int64_t exp_event_time[expRowNum] = {21, 22, 23, 24, 25, 26, 27, 28, 29, 30};
    int64_t exp_ref_key[expRowNum] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2};
    char *exp_description[expRowNum] = {desText[0], desText[1], desText[2], desText[3], desText[4], desText[0],
        desText[1], desText[2], desText[3], desText[4]};
    uint16_t exp_descriptionLen[expRowNum] = {descriptionLen[0], descriptionLen[1], descriptionLen[2],
        descriptionLen[3], descriptionLen[4], descriptionLen[0], descriptionLen[1], descriptionLen[2],
        descriptionLen[3], descriptionLen[4]};
    RdCheckDataInTSTableOfStreamTable(stmt, qryCmd, expRowNum, expColNum, exp_id, exp_water_mark, exp_event_time,
        exp_ref_key, exp_descriptionLen, exp_description);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop stream table stream2;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel1);
    RdStreamFreeTableSchema(vertexLabel2);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// alter union向view添加schema相同，大小写不同的新表
TEST_F(t01_alter_union, STREAM_035_008)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer, mini_ref_key integer, "
         "description text) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');",
            GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer, mini_ref_key "
         "integer, description text);"},
        {"create stream table StrEam2 (ID InteGer, Name Char(50), Water_Mark Integer, event_TIME inteGer, Mini_ref_key "
         "integer, DEScription text);"},
        {"create stream view view1 AS select * FROM stream1 with (tuple_buffer_size = 15);"},
        {"create stream sink sink1 AS select * from view1 into tsdb(ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    string alterCmd = "alter stream view view1 alter from_union add stream2;";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel1 = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel1);
    RdVertexLabelT *vertexLabel2 = RdStreamParseTableSchema(creates[2].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel2);

    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 6;
    int64_t id[rowNum] = {1, 2, 3, 4, 5};
    int64_t water_mark[rowNum] = {11, 12, 13, 14, 15};
    int64_t event_time[rowNum] = {21, 22, 23, 24, 25};
    int64_t ref_key[rowNum] = {1, 1, 1, 1, 1};
    char desText[][128] = {"test data: 1", "test data: 12", "test data: 123", "test data: 1234", "test data: 12345"};
    char *description[rowNum] = {0};
    uint16_t descriptionLen[rowNum] = {0};
    for (int i = 0; i < rowNum; ++i) {
        description[i] = desText[i];
        descriptionLen[i] = strlen(desText[i]) + 1;
    }
    bool nullInfo[colNum] = {false, false, false, false, false, false};
    RdStructWriteStreamTable(
        stmt, vertexLabel1, rowNum, id, water_mark, event_time, ref_key, descriptionLen, description, nullInfo);

    int64_t id2[rowNum] = {6, 7, 8, 9, 10};
    int64_t water_mark2[rowNum] = {16, 17, 18, 19, 20};
    int64_t event_time2[rowNum] = {26, 27, 28, 29, 30};
    int64_t ref_key2[rowNum] = {2, 2, 2, 2, 2};
    RdStructWriteStreamTable(
        stmt, vertexLabel2, rowNum, id2, water_mark2, event_time2, ref_key2, descriptionLen, description, nullInfo);

    // 查询ts表中数据
    char *qryCmd = (char *)"select * from ts1 order by id;";
    uint32_t expRowNum = 10;
    uint32_t expColNum = 6;
    int64_t exp_id[expRowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    int64_t exp_water_mark[expRowNum] = {11, 12, 13, 14, 15, 16, 17, 18, 19, 20};
    int64_t exp_event_time[expRowNum] = {21, 22, 23, 24, 25, 26, 27, 28, 29, 30};
    int64_t exp_ref_key[expRowNum] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2};
    char *exp_description[expRowNum] = {desText[0], desText[1], desText[2], desText[3], desText[4], desText[0],
        desText[1], desText[2], desText[3], desText[4]};
    uint16_t exp_descriptionLen[expRowNum] = {descriptionLen[0], descriptionLen[1], descriptionLen[2],
        descriptionLen[3], descriptionLen[4], descriptionLen[0], descriptionLen[1], descriptionLen[2],
        descriptionLen[3], descriptionLen[4]};
    RdCheckDataInTSTableOfStreamTable(stmt, qryCmd, expRowNum, expColNum, exp_id, exp_water_mark, exp_event_time,
        exp_ref_key, exp_descriptionLen, exp_description);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop stream table stream2;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel1);
    RdStreamFreeTableSchema(vertexLabel2);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// alter union向sink添加schema相同，大小写不同的新表
TEST_F(t01_alter_union, STREAM_035_009)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer, mini_ref_key integer, "
         "description text) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');",
            GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer, mini_ref_key "
         "integer, description text);"},
        {"create stream table StrEam2 (ID InteGer, Name Char(50), Water_Mark Integer, event_TIME inteGer, Mini_ref_key "
         "integer, DEScription text);"},
        {"create stream sink sink1 AS select * from stream1 into tsdb(ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    string alterCmd = "alter stream sink sink1 alter from_union add stream2;";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel1 = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel1);
    RdVertexLabelT *vertexLabel2 = RdStreamParseTableSchema(creates[2].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel2);

    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 6;
    int64_t id[rowNum] = {1, 2, 3, 4, 5};
    int64_t water_mark[rowNum] = {11, 12, 13, 14, 15};
    int64_t event_time[rowNum] = {21, 22, 23, 24, 25};
    int64_t ref_key[rowNum] = {1, 1, 1, 1, 1};
    char desText[][128] = {"test data: 1", "test data: 12", "test data: 123", "test data: 1234", "test data: 12345"};
    char *description[rowNum] = {0};
    uint16_t descriptionLen[rowNum] = {0};
    for (int i = 0; i < rowNum; ++i) {
        description[i] = desText[i];
        descriptionLen[i] = strlen(desText[i]) + 1;
    }
    bool nullInfo[colNum] = {false, false, false, false, false, false};
    RdStructWriteStreamTable(
        stmt, vertexLabel1, rowNum, id, water_mark, event_time, ref_key, descriptionLen, description, nullInfo);

    int64_t id2[rowNum] = {6, 7, 8, 9, 10};
    int64_t water_mark2[rowNum] = {16, 17, 18, 19, 20};
    int64_t event_time2[rowNum] = {26, 27, 28, 29, 30};
    int64_t ref_key2[rowNum] = {2, 2, 2, 2, 2};
    RdStructWriteStreamTable(
        stmt, vertexLabel2, rowNum, id2, water_mark2, event_time2, ref_key2, descriptionLen, description, nullInfo);

    // 查询ts表中数据
    char *qryCmd = (char *)"select * from ts1 order by id;";
    uint32_t expRowNum = 10;
    uint32_t expColNum = 6;
    int64_t exp_id[expRowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    int64_t exp_water_mark[expRowNum] = {11, 12, 13, 14, 15, 16, 17, 18, 19, 20};
    int64_t exp_event_time[expRowNum] = {21, 22, 23, 24, 25, 26, 27, 28, 29, 30};
    int64_t exp_ref_key[expRowNum] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2};
    char *exp_description[expRowNum] = {desText[0], desText[1], desText[2], desText[3], desText[4], desText[0],
        desText[1], desText[2], desText[3], desText[4]};
    uint16_t exp_descriptionLen[expRowNum] = {descriptionLen[0], descriptionLen[1], descriptionLen[2],
        descriptionLen[3], descriptionLen[4], descriptionLen[0], descriptionLen[1], descriptionLen[2],
        descriptionLen[3], descriptionLen[4]};
    RdCheckDataInTSTableOfStreamTable(stmt, qryCmd, expRowNum, expColNum, exp_id, exp_water_mark, exp_event_time,
        exp_ref_key, exp_descriptionLen, exp_description);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream table stream1;"},
        {"drop stream table stream2;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel1);
    RdStreamFreeTableSchema(vertexLabel2);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// alter union向view添加schema相同，表名、列名有中文的新表
TEST_F(t01_alter_union, STREAM_035_010)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer, mini_ref_key integer, "
         "description text) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');",
            GMC_MODEL_TS},
        {"create stream table 流表1 (id编号 integer, name名字 char(50), water_mark水位 integer, event_time事件时间 "
         "integer, mini_ref_key integer, description描述 text);"},
        {"create stream table 流表2 (ID编号 InteGer, Name名字 Char(50), Water_Mark水位 Integer, event_TIME事件时间 "
         "inteGer, Mini_ref_key integer, DEScription描述 text);"},
        {"create stream view view1 AS select * FROM 流表1 with (tuple_buffer_size = 15);"},
        {"create stream sink sink1 AS select * from view1 into tsdb(ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    string alterCmd = "alter stream view view1 alter from_union add 流表2;";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel1 = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel1);
    RdVertexLabelT *vertexLabel2 = RdStreamParseTableSchema(creates[2].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel2);

    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 6;
    int64_t id[rowNum] = {1, 2, 3, 4, 5};
    int64_t water_mark[rowNum] = {11, 12, 13, 14, 15};
    int64_t event_time[rowNum] = {21, 22, 23, 24, 25};
    int64_t ref_key[rowNum] = {1, 1, 1, 1, 1};
    char desText[][128] = {"test data: 1", "test data: 12", "test data: 123", "test data: 1234", "test data: 12345"};
    char *description[rowNum] = {0};
    uint16_t descriptionLen[rowNum] = {0};
    for (int i = 0; i < rowNum; ++i) {
        description[i] = desText[i];
        descriptionLen[i] = strlen(desText[i]) + 1;
    }
    bool nullInfo[colNum] = {false, false, false, false, false, false};
    RdStructWriteStreamTable(
        stmt, vertexLabel1, rowNum, id, water_mark, event_time, ref_key, descriptionLen, description, nullInfo);

    int64_t id2[rowNum] = {6, 7, 8, 9, 10};
    int64_t water_mark2[rowNum] = {16, 17, 18, 19, 20};
    int64_t event_time2[rowNum] = {26, 27, 28, 29, 30};
    int64_t ref_key2[rowNum] = {2, 2, 2, 2, 2};
    RdStructWriteStreamTable(
        stmt, vertexLabel2, rowNum, id2, water_mark2, event_time2, ref_key2, descriptionLen, description, nullInfo);

    // 查询ts表中数据
    char *qryCmd = (char *)"select * from ts1 order by id;";
    uint32_t expRowNum = 10;
    uint32_t expColNum = 6;
    int64_t exp_id[expRowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    int64_t exp_water_mark[expRowNum] = {11, 12, 13, 14, 15, 16, 17, 18, 19, 20};
    int64_t exp_event_time[expRowNum] = {21, 22, 23, 24, 25, 26, 27, 28, 29, 30};
    int64_t exp_ref_key[expRowNum] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2};
    char *exp_description[expRowNum] = {desText[0], desText[1], desText[2], desText[3], desText[4], desText[0],
        desText[1], desText[2], desText[3], desText[4]};
    uint16_t exp_descriptionLen[expRowNum] = {descriptionLen[0], descriptionLen[1], descriptionLen[2],
        descriptionLen[3], descriptionLen[4], descriptionLen[0], descriptionLen[1], descriptionLen[2],
        descriptionLen[3], descriptionLen[4]};
    RdCheckDataInTSTableOfStreamTable(stmt, qryCmd, expRowNum, expColNum, exp_id, exp_water_mark, exp_event_time,
        exp_ref_key, exp_descriptionLen, exp_description);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table 流表1;"},
        {"drop stream table 流表2;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel1);
    RdStreamFreeTableSchema(vertexLabel2);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// alter union向sink添加schema相同，表名、列名有中文的新表
TEST_F(t01_alter_union, STREAM_035_011)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer, mini_ref_key integer, "
         "description text) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');",
            GMC_MODEL_TS},
        {"create stream table 流表1 (id编号 integer, name名字 char(50), water_mark水位 integer, event_time事件时间 "
         "integer, mini_ref_key integer, description描述 text);"},
        {"create stream table 流表2 (ID编号 InteGer, Name名字 Char(50), Water_Mark水位 Integer, event_TIME事件时间 "
         "inteGer, Mini_ref_key integer, DEScription描述 text);"},
        {"create stream sink sink1 AS select * from 流表1 into tsdb(ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    string alterCmd = "alter stream sink sink1 alter from_union add 流表2;";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel1 = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel1);
    RdVertexLabelT *vertexLabel2 = RdStreamParseTableSchema(creates[2].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel2);

    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 6;
    int64_t id[rowNum] = {1, 2, 3, 4, 5};
    int64_t water_mark[rowNum] = {11, 12, 13, 14, 15};
    int64_t event_time[rowNum] = {21, 22, 23, 24, 25};
    int64_t ref_key[rowNum] = {1, 1, 1, 1, 1};
    char desText[][128] = {"test data: 1", "test data: 12", "test data: 123", "test data: 1234", "test data: 12345"};
    char *description[rowNum] = {0};
    uint16_t descriptionLen[rowNum] = {0};
    for (int i = 0; i < rowNum; ++i) {
        description[i] = desText[i];
        descriptionLen[i] = strlen(desText[i]) + 1;
    }
    bool nullInfo[colNum] = {false, false, false, false, false, false};
    RdStructWriteStreamTable(
        stmt, vertexLabel1, rowNum, id, water_mark, event_time, ref_key, descriptionLen, description, nullInfo);

    int64_t id2[rowNum] = {6, 7, 8, 9, 10};
    int64_t water_mark2[rowNum] = {16, 17, 18, 19, 20};
    int64_t event_time2[rowNum] = {26, 27, 28, 29, 30};
    int64_t ref_key2[rowNum] = {2, 2, 2, 2, 2};
    RdStructWriteStreamTable(
        stmt, vertexLabel2, rowNum, id2, water_mark2, event_time2, ref_key2, descriptionLen, description, nullInfo);

    // 查询ts表中数据
    char *qryCmd = (char *)"select * from ts1 order by id;";
    uint32_t expRowNum = 10;
    uint32_t expColNum = 6;
    int64_t exp_id[expRowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    int64_t exp_water_mark[expRowNum] = {11, 12, 13, 14, 15, 16, 17, 18, 19, 20};
    int64_t exp_event_time[expRowNum] = {21, 22, 23, 24, 25, 26, 27, 28, 29, 30};
    int64_t exp_ref_key[expRowNum] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2};
    char *exp_description[expRowNum] = {desText[0], desText[1], desText[2], desText[3], desText[4], desText[0],
        desText[1], desText[2], desText[3], desText[4]};
    uint16_t exp_descriptionLen[expRowNum] = {descriptionLen[0], descriptionLen[1], descriptionLen[2],
        descriptionLen[3], descriptionLen[4], descriptionLen[0], descriptionLen[1], descriptionLen[2],
        descriptionLen[3], descriptionLen[4]};
    RdCheckDataInTSTableOfStreamTable(stmt, qryCmd, expRowNum, expColNum, exp_id, exp_water_mark, exp_event_time,
        exp_ref_key, exp_descriptionLen, exp_description);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream table 流表1;"},
        {"drop stream table 流表2;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel1);
    RdStreamFreeTableSchema(vertexLabel2);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 带有ref表的view，使用alter union添加相同schema的流表，写入数据
TEST_F(t01_alter_union, STREAM_035_012)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer, ref_key integer, "
         "ref_value integer, description text) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');",
            GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text);"},
        {"create stream table stream2 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text);"},
        {"create stream reference ref1(integer, integer) with (mini = 'true');"},
        {"upsert into streamref ref1 values (1, 0);"},
        {"upsert into streamref ref1 values (2, 1);"},
        {"create stream view view1 AS select id, name, water_mark, event_time, ref_key, REF['ref1'][ref_key], "
         "description "
         "FROM TABLE(UNION(TABLE stream1, TABLE stream2));"},
        {"create stream sink sink1 AS select * from view1 into tsdb(ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    string alterCmd = "alter stream view view1 alter from_union drop stream2;";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    ASSERT_EQ(GMERR_OK, ret);

    alterCmd = "alter stream view view1 alter from_union add stream2;";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel1 = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel1);
    RdVertexLabelT *vertexLabel2 = RdStreamParseTableSchema(creates[2].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel2);

    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 6;
    int64_t id[rowNum] = {1, 2, 3, 4, 5};
    int64_t water_mark[rowNum] = {11, 12, 13, 14, 15};
    int64_t event_time[rowNum] = {21, 22, 23, 24, 25};
    int64_t ref_key[rowNum] = {1, 1, 1, 1, 1};
    char desText[][128] = {"test data: 1", "test data: 12", "test data: 123", "test data: 1234", "test data: 12345"};
    char *description[rowNum] = {0};
    uint16_t descriptionLen[rowNum] = {0};
    for (int i = 0; i < rowNum; ++i) {
        description[i] = desText[i];
        descriptionLen[i] = strlen(desText[i]) + 1;
    }
    bool nullInfo[colNum] = {false, false, false, false, false, false};
    RdStructWriteStreamTable(
        stmt, vertexLabel1, rowNum, id, water_mark, event_time, ref_key, descriptionLen, description, nullInfo);

    int64_t id2[rowNum] = {6, 7, 8, 9, 10};
    int64_t water_mark2[rowNum] = {16, 17, 18, 19, 20};
    int64_t event_time2[rowNum] = {26, 27, 28, 29, 30};
    int64_t ref_key2[rowNum] = {2, 2, 2, 2, 2};
    RdStructWriteStreamTable(
        stmt, vertexLabel2, rowNum, id2, water_mark2, event_time2, ref_key2, descriptionLen, description, nullInfo);

    // 查询ts表中数据
    char *qryCmd = (char *)"select * from ts1 order by water_mark;";
    uint32_t expRowNum = 10;
    uint32_t expColNum = 7;
    int64_t exp_id[expRowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    int64_t exp_water_mark[expRowNum] = {11, 12, 13, 14, 15, 16, 17, 18, 19, 20};
    int64_t exp_event_time[expRowNum] = {21, 22, 23, 24, 25, 26, 27, 28, 29, 30};
    int64_t exp_ref_key[expRowNum] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2};
    int64_t exp_ref_val[expRowNum] = {0, 0, 0, 0, 0, 1, 1, 1, 1, 1};
    char *exp_description[expRowNum] = {desText[0], desText[1], desText[2], desText[3], desText[4], desText[0],
        desText[1], desText[2], desText[3], desText[4]};
    uint16_t exp_descriptionLen[expRowNum] = {descriptionLen[0], descriptionLen[1], descriptionLen[2],
        descriptionLen[3], descriptionLen[4], descriptionLen[0], descriptionLen[1], descriptionLen[2],
        descriptionLen[3], descriptionLen[4]};
    RdCheckDataInTSTableOfStreamTable2(stmt, qryCmd, expRowNum, expColNum, exp_id, exp_water_mark, exp_event_time,
        exp_ref_key, exp_ref_val, exp_descriptionLen, exp_description);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream reference ref1;"},
        {"drop stream table stream1;"},
        {"drop stream table stream2;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel1);
    RdStreamFreeTableSchema(vertexLabel2);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 带有ref表的sink，使用alter union添加相同schema的流表，写入数据
TEST_F(t01_alter_union, STREAM_035_013)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer, ref_key integer, "
         "ref_value integer, description text) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');",
            GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text);"},
        {"create stream table stream2 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text);"},
        {"create stream reference ref1(integer, integer) with (mini = 'true');"},
        {"upsert into streamref ref1 values (1, 0);"},
        {"upsert into streamref ref1 values (2, 1);"},
        {"create stream sink sink1 AS select id, name, water_mark, event_time, ref_key, REF['ref1'][ref_key], "
         "description "
         "FROM TABLE(UNION(TABLE stream1, TABLE stream2)) into tsdb(ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    string alterCmd = "alter stream sink sink1 alter from_union drop stream2;";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    ASSERT_EQ(GMERR_OK, ret);

    alterCmd = "alter stream sink sink1 alter from_union add stream2;";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel1 = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel1);
    RdVertexLabelT *vertexLabel2 = RdStreamParseTableSchema(creates[2].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel2);

    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 6;
    int64_t id[rowNum] = {1, 2, 3, 4, 5};
    int64_t water_mark[rowNum] = {11, 12, 13, 14, 15};
    int64_t event_time[rowNum] = {21, 22, 23, 24, 25};
    int64_t ref_key[rowNum] = {1, 1, 1, 1, 1};
    char desText[][128] = {"test data: 1", "test data: 12", "test data: 123", "test data: 1234", "test data: 12345"};
    char *description[rowNum] = {0};
    uint16_t descriptionLen[rowNum] = {0};
    for (int i = 0; i < rowNum; ++i) {
        description[i] = desText[i];
        descriptionLen[i] = strlen(desText[i]) + 1;
    }
    bool nullInfo[colNum] = {false, false, false, false, false, false};
    RdStructWriteStreamTable(
        stmt, vertexLabel1, rowNum, id, water_mark, event_time, ref_key, descriptionLen, description, nullInfo);

    int64_t id2[rowNum] = {6, 7, 8, 9, 10};
    int64_t water_mark2[rowNum] = {16, 17, 18, 19, 20};
    int64_t event_time2[rowNum] = {26, 27, 28, 29, 30};
    int64_t ref_key2[rowNum] = {2, 2, 2, 2, 2};
    RdStructWriteStreamTable(
        stmt, vertexLabel2, rowNum, id2, water_mark2, event_time2, ref_key2, descriptionLen, description, nullInfo);

    // 查询ts表中数据
    char *qryCmd = (char *)"select * from ts1 order by water_mark;";
    uint32_t expRowNum = 10;
    uint32_t expColNum = 7;
    int64_t exp_id[expRowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    int64_t exp_water_mark[expRowNum] = {11, 12, 13, 14, 15, 16, 17, 18, 19, 20};
    int64_t exp_event_time[expRowNum] = {21, 22, 23, 24, 25, 26, 27, 28, 29, 30};
    int64_t exp_ref_key[expRowNum] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2};
    int64_t exp_ref_val[expRowNum] = {0, 0, 0, 0, 0, 1, 1, 1, 1, 1};
    char *exp_description[expRowNum] = {desText[0], desText[1], desText[2], desText[3], desText[4], desText[0],
        desText[1], desText[2], desText[3], desText[4]};
    uint16_t exp_descriptionLen[expRowNum] = {descriptionLen[0], descriptionLen[1], descriptionLen[2],
        descriptionLen[3], descriptionLen[4], descriptionLen[0], descriptionLen[1], descriptionLen[2],
        descriptionLen[3], descriptionLen[4]};
    RdCheckDataInTSTableOfStreamTable2(stmt, qryCmd, expRowNum, expColNum, exp_id, exp_water_mark, exp_event_time,
        exp_ref_key, exp_ref_val, exp_descriptionLen, exp_description);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream reference ref1;"},
        {"drop stream table stream1;"},
        {"drop stream table stream2;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel1);
    RdStreamFreeTableSchema(vertexLabel2);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 使用alter union在view新增1k个相同schema的流表，写入数据
TEST_F(t01_alter_union, STREAM_035_014)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer, ref_key integer, "
         "description text) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');",
            GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel[64] = {0};

    // 创建1k个流表
    for (uint32_t i = 0; i < 64; ++i) {
        char crtStream[256] = {0};
        sprintf_s(crtStream, 256,
            "create stream table stream%03d (id integer, name char(50), water_mark integer, "
            "event_time integer, ref_key integer, description text);",
            i);
        ret = GmcExecDirect(stmt, crtStream, strlen(crtStream));
        ASSERT_EQ(GMERR_OK, ret);
        vertexLabel[i] = RdStreamParseTableSchema(crtStream);
        ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel[i]);
    }

    string crtView = "create stream view view1 AS select * FROM stream000;";
    ret = GmcExecDirect(stmt, crtView.c_str(), crtView.length());
    ASSERT_EQ(GMERR_OK, ret);

    for (uint32_t i = 1; i < 64; ++i) {
        char altUn[128] = {0};
        sprintf_s(altUn, 128, "alter stream view view1 alter from_union add stream%03d;", i);
        ret = GmcExecDirect(stmt, altUn, strlen(altUn));
        ASSERT_EQ(GMERR_OK, ret);
    }

    string crtSink = "create stream sink sink1 AS select * from view1 into tsdb(ts1) with (batch_window_size = 1);";
    ret = GmcExecDirect(stmt, crtSink.c_str(), crtSink.length());
    ASSERT_EQ(GMERR_OK, ret);

    // 结构化写入数据
    int64_t id[1] = {0};
    int64_t water_mark[1] = {0};
    int64_t event_time[1] = {0};
    int64_t ref_key[1] = {0};
    bool nullInfo[6] = {false, false, false, false, false, false};
    for (uint32_t i = 0; i < 64; ++i) {
        id[0] = i;
        water_mark[0] = i;
        event_time[0] = i;
        ref_key[0] = i;
        char temp[128] = {0};
        sprintf_s(temp, 128, "text data: %03d", i);
        char *description[1] = {0};
        description[0] = temp;
        uint16_t descriptionLen[1] = {0};
        descriptionLen[0] = strlen(temp) + 1;
        RdStructWriteStreamTable(
            stmt, vertexLabel[i], 1, id, water_mark, event_time, ref_key, descriptionLen, description, nullInfo);
    }

    // 查询ts表中数据
    char *qryCmd = (char *)"select * from ts1 order by id;";
    uint32_t expRowNum = 64;
    uint32_t expColNum = 6;
    int64_t exp_id[expRowNum] = {0};
    int64_t exp_water_mark[expRowNum] = {0};
    int64_t exp_event_time[expRowNum] = {0};
    int64_t exp_ref_key[expRowNum] = {0};
    char *exp_description[expRowNum] = {0};
    char desText[64][128] = {0};
    uint16_t exp_descriptionLen[expRowNum] = {0};
    for (uint32_t i = 0; i < 64; ++i) {
        exp_id[i] = i;
        exp_water_mark[i] = i;
        exp_event_time[i] = i;
        exp_ref_key[i] = i;
        sprintf_s(desText[i], 128, "text data: %03d", i);
        exp_description[i] = desText[i];
        exp_descriptionLen[i] = strlen(desText[i]) + 1;
    }
    RdCheckDataInTSTableOfStreamTable(stmt, qryCmd, expRowNum, expColNum, exp_id, exp_water_mark, exp_event_time,
        exp_ref_key, exp_descriptionLen, exp_description);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 64; ++i) {
        char dropStream[128] = {0};
        sprintf_s(dropStream, 128, "drop stream table stream%03d;", i);
        ret = GmcExecDirect(stmt, dropStream, strlen(dropStream));
        ASSERT_EQ(GMERR_OK, ret);
        RdStreamFreeTableSchema(vertexLabel[i]);
    }

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 使用alter union在sink新增1k个相同schema的流表，写入数据
TEST_F(t01_alter_union, STREAM_035_015)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer, ref_key integer, "
         "description text) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');",
            GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel[64] = {0};

    // 创建1k个流表
    for (uint32_t i = 0; i < 64; ++i) {
        char crtStream[256] = {0};
        sprintf_s(crtStream, 256,
            "create stream table stream%03d (id integer, name char(50), water_mark integer, "
            "event_time integer, ref_key integer, description text);",
            i);
        ret = GmcExecDirect(stmt, crtStream, strlen(crtStream));
        ASSERT_EQ(GMERR_OK, ret);
        vertexLabel[i] = RdStreamParseTableSchema(crtStream);
        ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel[i]);
    }

    string crtSink = "create stream sink sink1 AS select * FROM stream000 into tsdb(ts1) with (batch_window_size = 1);";
    ret = GmcExecDirect(stmt, crtSink.c_str(), crtSink.length());
    ASSERT_EQ(GMERR_OK, ret);

    for (uint32_t i = 1; i < 64; ++i) {
        char altUn[128] = {0};
        sprintf_s(altUn, 128, "alter stream sink sink1 alter from_union add stream%03d;", i);
        ret = GmcExecDirect(stmt, altUn, strlen(altUn));
        ASSERT_EQ(GMERR_OK, ret);
    }

    // 结构化写入数据
    int64_t id[1] = {0};
    int64_t water_mark[1] = {0};
    int64_t event_time[1] = {0};
    int64_t ref_key[1] = {0};
    bool nullInfo[6] = {false, false, false, false, false, false};
    for (uint32_t i = 0; i < 64; ++i) {
        id[0] = i;
        water_mark[0] = i;
        event_time[0] = i;
        ref_key[0] = i;
        char temp[128] = {0};
        sprintf_s(temp, 128, "text data: %03d", i);
        char *description[1] = {0};
        description[0] = temp;
        uint16_t descriptionLen[1] = {0};
        descriptionLen[0] = strlen(temp) + 1;
        RdStructWriteStreamTable(
            stmt, vertexLabel[i], 1, id, water_mark, event_time, ref_key, descriptionLen, description, nullInfo);
    }
    // 查询ts表中数据
    char *qryCmd = (char *)"select * from ts1 order by id;";
    uint32_t expRowNum = 64;
    uint32_t expColNum = 6;
    int64_t exp_id[expRowNum] = {0};
    int64_t exp_water_mark[expRowNum] = {0};
    int64_t exp_event_time[expRowNum] = {0};
    int64_t exp_ref_key[expRowNum] = {0};
    char *exp_description[expRowNum] = {0};
    char desText[64][128] = {0};
    uint16_t exp_descriptionLen[expRowNum] = {0};
    for (uint32_t i = 0; i < 64; ++i) {
        exp_id[i] = i;
        exp_water_mark[i] = i;
        exp_event_time[i] = i;
        exp_ref_key[i] = i;
        sprintf_s(desText[i], 128, "text data: %03d", i);
        exp_description[i] = desText[i];
        exp_descriptionLen[i] = strlen(desText[i]) + 1;
    }
    RdCheckDataInTSTableOfStreamTable(stmt, qryCmd, expRowNum, expColNum, exp_id, exp_water_mark, exp_event_time,
        exp_ref_key, exp_descriptionLen, exp_description);
    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 64; ++i) {
        char dropStream[128] = {0};
        sprintf_s(dropStream, 128, "drop stream table stream%03d;", i);
        ret = GmcExecDirect(stmt, dropStream, strlen(dropStream));
        ASSERT_EQ(GMERR_OK, ret);
        RdStreamFreeTableSchema(vertexLabel[i]);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// union 1k个相同schema的流表，alter union删除999个，写入数据
TEST_F(t01_alter_union, STREAM_035_016)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer, mini_ref_key integer, "
         "description text) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');",
            GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = NULL;
    // 创建1k个流表
    for (uint32_t i = 0; i < 64; ++i) {
        char crtStream[256] = {0};
        sprintf_s(crtStream, 256,
            "create stream table stream%03d (id integer, name char(50), water_mark integer, "
            "event_time integer, mini_ref_key integer, description text);",
            i);
        ret = GmcExecDirect(stmt, crtStream, strlen(crtStream));
        ASSERT_EQ(GMERR_OK, ret);
        if (i == 0) {
            vertexLabel = RdStreamParseTableSchema(crtStream);
            ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);
        }
    }

    string crtView = "create stream view view1 AS select * FROM TABLE(UNION(TABLE stream000, ";
    for (uint32_t i = 1; i < 63; ++i) {
        char temp[32] = {0};
        sprintf_s(temp, 32, "TABLE stream%03d, ", i);
        crtView += temp;
    }
    crtView += "TABLE stream063));";
    ret = GmcExecDirect(stmt, crtView.c_str(), crtView.length());
    ASSERT_EQ(GMERR_OK, ret);

    string crtSink = "create stream sink sink1 AS select * from view1 into tsdb(ts1) with (batch_window_size = 1);";
    ret = GmcExecDirect(stmt, crtSink.c_str(), crtSink.length());
    ASSERT_EQ(GMERR_OK, ret);

    for (uint32_t i = 1; i <= 63; ++i) {
        char altUn[128] = {0};
        sprintf_s(altUn, 128, "alter stream view view1 alter from_union drop stream%03d;", i);
        ret = GmcExecDirect(stmt, altUn, strlen(altUn));
        ASSERT_EQ(GMERR_OK, ret);
    }

    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 6;
    int64_t id[rowNum] = {1, 2, 3, 4, 5};
    int64_t water_mark[rowNum] = {11, 12, 13, 14, 15};
    int64_t event_time[rowNum] = {21, 22, 23, 24, 25};
    int64_t ref_key[rowNum] = {1, 1, 1, 1, 1};
    char desText[][128] = {"test data: 1", "test data: 12", "test data: 123", "test data: 1234", "test data: 12345"};
    char *description[rowNum] = {0};
    uint16_t descriptionLen[rowNum] = {0};
    for (int i = 0; i < rowNum; ++i) {
        description[i] = desText[i];
        descriptionLen[i] = strlen(desText[i]) + 1;
    }
    bool nullInfo[colNum] = {false, false, false, false, false, false};
    RdStructWriteStreamTable(
        stmt, vertexLabel, rowNum, id, water_mark, event_time, ref_key, descriptionLen, description, nullInfo);

    // 查询ts表中数据
    char *qryCmd = (char *)"select * from ts1 order by id;";
    uint32_t expRowNum = 5;
    uint32_t expColNum = 6;
    int64_t exp_id[expRowNum] = {1, 2, 3, 4, 5};
    int64_t exp_water_mark[expRowNum] = {11, 12, 13, 14, 15};
    int64_t exp_event_time[expRowNum] = {21, 22, 23, 24, 25};
    int64_t exp_ref_key[expRowNum] = {1, 1, 1, 1, 1};
    char *exp_description[expRowNum] = {desText[0], desText[1], desText[2], desText[3], desText[4]};
    uint16_t exp_descriptionLen[expRowNum] = {
        descriptionLen[0], descriptionLen[1], descriptionLen[2], descriptionLen[3], descriptionLen[4]};
    RdCheckDataInTSTableOfStreamTable(stmt, qryCmd, expRowNum, expColNum, exp_id, exp_water_mark, exp_event_time,
        exp_ref_key, exp_descriptionLen, exp_description);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 64; ++i) {
        char dropStream[128] = {0};
        sprintf_s(dropStream, 128, "drop stream table stream%03d;", i);
        ret = GmcExecDirect(stmt, dropStream, strlen(dropStream));
        ASSERT_EQ(GMERR_OK, ret);
    }
    RdStreamFreeTableSchema(vertexLabel);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// alter union在view添加带有hostname和system_time的流表，写入数据
TEST_F(t01_alter_union, STREAM_035_017)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), system_time integer, hostname char(50), ref_key integer, "
         "description text) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');",
            GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), system_time integer DEFAULT current_time_second(), "
         "hostname char(50) DEFAULT get_hostname(), ref_key integer, description text);"},
        {"create stream table stream2 (id integer, name char(50), system_time integer DEFAULT current_time_second(), "
         "hostname char(50) DEFAULT get_hostname(), ref_key integer, description text);"},
        {"create stream view view1 AS select * FROM stream1;"},
        {"create stream sink sink1 AS select * from view1 into tsdb(ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    string alterCmd = "alter stream view view1 alter from_union add stream2;";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel1 = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel1);
    RdVertexLabelT *vertexLabel2 = RdStreamParseTableSchema(creates[2].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel2);

    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 6;
    int64_t id[rowNum] = {1, 2, 3, 4, 5};
    int64_t ref_key[rowNum] = {1, 1, 1, 1, 1};
    char desText[][128] = {"test data: 1", "test data: 12", "test data: 123", "test data: 1234", "test data: 12345"};
    char *description[rowNum] = {0};
    uint16_t descriptionLen[rowNum] = {0};
    for (int i = 0; i < rowNum; ++i) {
        description[i] = desText[i];
        descriptionLen[i] = strlen(desText[i]) + 1;
    }
    bool nullInfo[colNum] = {false, false, true, true, false, false};
    RdStructWriteStreamTable2(stmt, vertexLabel1, rowNum, id, ref_key, descriptionLen, description, nullInfo);

    int64_t id2[rowNum] = {6, 7, 8, 9, 10};
    int64_t ref_key2[rowNum] = {2, 2, 2, 2, 2};
    RdStructWriteStreamTable2(stmt, vertexLabel2, rowNum, id2, ref_key2, descriptionLen, description, nullInfo);

    // 查询ts表中数据
    char *qryCmd = (char *)"select * from ts1 order by id;";
    uint32_t expRowNum = 10;
    uint32_t expColNum = 6;
    int64_t exp_id[expRowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    struct timeval tv;
    gettimeofday(&tv, NULL);
    char expHostname[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
    gethostname(expHostname, RD_STREAM_TABLE_T1_NAME_SIZE);
    int64_t exp_ref_key[expRowNum] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2};
    char *exp_description[expRowNum] = {desText[0], desText[1], desText[2], desText[3], desText[4], desText[0],
        desText[1], desText[2], desText[3], desText[4]};
    uint16_t exp_descriptionLen[expRowNum] = {descriptionLen[0], descriptionLen[1], descriptionLen[2],
        descriptionLen[3], descriptionLen[4], descriptionLen[0], descriptionLen[1], descriptionLen[2],
        descriptionLen[3], descriptionLen[4]};
    RdCheckDataInTSTableOfStreamTable3(stmt, qryCmd, expRowNum, expColNum, exp_id, tv.tv_sec, expHostname, exp_ref_key,
        exp_descriptionLen, exp_description);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop stream table stream2;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel1);
    RdStreamFreeTableSchema(vertexLabel2);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// alter union在sink添加带有hostname和system_time的流表，写入数据
TEST_F(t01_alter_union, STREAM_035_018)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), system_time integer, hostname char(50), ref_key integer, "
         "description text) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');",
            GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), system_time integer DEFAULT current_time_second(), "
         "hostname char(50) DEFAULT get_hostname(), ref_key integer, description text);"},
        {"create stream table stream2 (id integer, name char(50), system_time integer DEFAULT current_time_second(), "
         "hostname char(50) DEFAULT get_hostname(), ref_key integer, description text);"},
        {"create stream sink sink1 AS select * from stream1 into tsdb(ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    string alterCmd = "alter stream sink sink1 alter from_union add stream2;";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel1 = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel1);
    RdVertexLabelT *vertexLabel2 = RdStreamParseTableSchema(creates[2].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel2);

    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 6;
    int64_t id[rowNum] = {1, 2, 3, 4, 5};
    int64_t ref_key[rowNum] = {1, 1, 1, 1, 1};
    char desText[][128] = {"test data: 1", "test data: 12", "test data: 123", "test data: 1234", "test data: 12345"};
    char *description[rowNum] = {0};
    uint16_t descriptionLen[rowNum] = {0};
    for (int i = 0; i < rowNum; ++i) {
        description[i] = desText[i];
        descriptionLen[i] = strlen(desText[i]) + 1;
    }
    bool nullInfo[colNum] = {false, false, true, true, false, false};
    RdStructWriteStreamTable2(stmt, vertexLabel1, rowNum, id, ref_key, descriptionLen, description, nullInfo);

    int64_t id2[rowNum] = {6, 7, 8, 9, 10};
    int64_t ref_key2[rowNum] = {2, 2, 2, 2, 2};
    RdStructWriteStreamTable2(stmt, vertexLabel2, rowNum, id2, ref_key2, descriptionLen, description, nullInfo);

    // 查询ts表中数据
    char *qryCmd = (char *)"select * from ts1 order by id;";
    uint32_t expRowNum = 10;
    uint32_t expColNum = 6;
    int64_t exp_id[expRowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    struct timeval tv;
    gettimeofday(&tv, NULL);
    char expHostname[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
    gethostname(expHostname, RD_STREAM_TABLE_T1_NAME_SIZE);
    int64_t exp_ref_key[expRowNum] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2};
    char *exp_description[expRowNum] = {desText[0], desText[1], desText[2], desText[3], desText[4], desText[0],
        desText[1], desText[2], desText[3], desText[4]};
    uint16_t exp_descriptionLen[expRowNum] = {descriptionLen[0], descriptionLen[1], descriptionLen[2],
        descriptionLen[3], descriptionLen[4], descriptionLen[0], descriptionLen[1], descriptionLen[2],
        descriptionLen[3], descriptionLen[4]};
    RdCheckDataInTSTableOfStreamTable3(stmt, qryCmd, expRowNum, expColNum, exp_id, tv.tv_sec, expHostname, exp_ref_key,
        exp_descriptionLen, exp_description);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream table stream1;"},
        {"drop stream table stream2;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel1);
    RdStreamFreeTableSchema(vertexLabel2);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// view带where子句做> <= and 运算，使用alter union添加新表
TEST_F(t01_alter_union, STREAM_035_019)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer, ref_key integer, "
         "description text) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');",
            GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text);"},
        {"create stream table stream2 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text);"},
        {"create stream view view1 AS select * FROM stream1 where id > 3 and id <= 9;"},
        {"create stream sink sink1 AS select * from view1 into tsdb(ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    string alterCmd = "alter stream view view1 alter from_union add stream2;";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel1 = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel1);
    RdVertexLabelT *vertexLabel2 = RdStreamParseTableSchema(creates[2].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel2);

    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 6;
    int64_t id[rowNum] = {1, 2, 3, 4, 5};
    int64_t water_mark[rowNum] = {11, 12, 13, 14, 15};
    int64_t event_time[rowNum] = {21, 22, 23, 24, 25};
    int64_t ref_key[rowNum] = {1, 1, 1, 1, 1};
    char desText[][128] = {"test data: 1", "test data: 12", "test data: 123", "test data: 1234", "test data: 12345"};
    char *description[rowNum] = {0};
    uint16_t descriptionLen[rowNum] = {0};
    for (int i = 0; i < rowNum; ++i) {
        description[i] = desText[i];
        descriptionLen[i] = strlen(desText[i]) + 1;
    }
    bool nullInfo[colNum] = {false, false, false, false, false, false};
    RdStructWriteStreamTable(
        stmt, vertexLabel1, rowNum, id, water_mark, event_time, ref_key, descriptionLen, description, nullInfo);

    int64_t id2[rowNum] = {6, 7, 8, 9, 10};
    int64_t water_mark2[rowNum] = {16, 17, 18, 19, 20};
    int64_t event_time2[rowNum] = {26, 27, 28, 29, 30};
    int64_t ref_key2[rowNum] = {2, 2, 2, 2, 2};
    RdStructWriteStreamTable(
        stmt, vertexLabel2, rowNum, id2, water_mark2, event_time2, ref_key2, descriptionLen, description, nullInfo);

    // 查询ts表中数据
    char *qryCmd = (char *)"select * from ts1 order by id;";
    uint32_t expRowNum = 6;
    uint32_t expColNum = 6;
    int64_t exp_id[expRowNum] = {4, 5, 6, 7, 8, 9};
    int64_t exp_water_mark[expRowNum] = {14, 15, 16, 17, 18, 19};
    int64_t exp_event_time[expRowNum] = {24, 25, 26, 27, 28, 29};
    int64_t exp_ref_key[expRowNum] = {1, 1, 2, 2, 2, 2};
    char *exp_description[expRowNum] = {desText[3], desText[4], desText[0], desText[1], desText[2], desText[3]};
    uint16_t exp_descriptionLen[expRowNum] = {descriptionLen[3], descriptionLen[4], descriptionLen[0],
        descriptionLen[1], descriptionLen[2], descriptionLen[3]};
    RdCheckDataInTSTableOfStreamTable(stmt, qryCmd, expRowNum, expColNum, exp_id, exp_water_mark, exp_event_time,
        exp_ref_key, exp_descriptionLen, exp_description);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop stream table stream2;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel1);
    RdStreamFreeTableSchema(vertexLabel2);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// view带where子句做> >= or 运算，使用alter union添加新表
TEST_F(t01_alter_union, STREAM_035_020)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer, ref_key integer, "
         "description text) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');",
            GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text);"},
        {"create stream table stream2 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text);"},
        {"create stream view view1 AS select * FROM stream1 where id > 3 or event_time >= 23;"},
        {"create stream sink sink1 AS select * from view1 into tsdb(ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    string alterCmd = "alter stream view view1 alter from_union add stream2;";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel1 = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel1);
    RdVertexLabelT *vertexLabel2 = RdStreamParseTableSchema(creates[2].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel2);

    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 6;
    int64_t id[rowNum] = {1, 2, 3, 4, 5};
    int64_t water_mark[rowNum] = {11, 12, 13, 14, 15};
    int64_t event_time[rowNum] = {21, 22, 23, 24, 25};
    int64_t ref_key[rowNum] = {1, 1, 1, 1, 1};
    char desText[][128] = {"test data: 1", "test data: 12", "test data: 123", "test data: 1234", "test data: 12345"};
    char *description[rowNum] = {0};
    uint16_t descriptionLen[rowNum] = {0};
    for (int i = 0; i < rowNum; ++i) {
        description[i] = desText[i];
        descriptionLen[i] = strlen(desText[i]) + 1;
    }
    bool nullInfo[colNum] = {false, false, false, false, false, false};
    RdStructWriteStreamTable(
        stmt, vertexLabel1, rowNum, id, water_mark, event_time, ref_key, descriptionLen, description, nullInfo);

    int64_t id2[rowNum] = {6, 7, 8, 9, 10};
    int64_t water_mark2[rowNum] = {16, 17, 18, 19, 20};
    int64_t event_time2[rowNum] = {26, 27, 28, 29, 30};
    int64_t ref_key2[rowNum] = {2, 2, 2, 2, 2};
    RdStructWriteStreamTable(
        stmt, vertexLabel2, rowNum, id2, water_mark2, event_time2, ref_key2, descriptionLen, description, nullInfo);

    // 查询ts表中数据
    char *qryCmd = (char *)"select * from ts1 order by id;";
    uint32_t expRowNum = 8;
    uint32_t expColNum = 6;
    int64_t exp_id[expRowNum] = {3, 4, 5, 6, 7, 8, 9, 10};
    int64_t exp_water_mark[expRowNum] = {13, 14, 15, 16, 17, 18, 19, 20};
    int64_t exp_event_time[expRowNum] = {23, 24, 25, 26, 27, 28, 29, 30};
    int64_t exp_ref_key[expRowNum] = {1, 1, 1, 2, 2, 2, 2, 2};
    char *exp_description[expRowNum] = {
        desText[2], desText[3], desText[4], desText[0], desText[1], desText[2], desText[3], desText[4]};
    uint16_t exp_descriptionLen[expRowNum] = {descriptionLen[2], descriptionLen[3], descriptionLen[4],
        descriptionLen[0], descriptionLen[1], descriptionLen[2], descriptionLen[3], descriptionLen[4]};
    RdCheckDataInTSTableOfStreamTable(stmt, qryCmd, expRowNum, expColNum, exp_id, exp_water_mark, exp_event_time,
        exp_ref_key, exp_descriptionLen, exp_description);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop stream table stream2;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel1);
    RdStreamFreeTableSchema(vertexLabel2);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// view带where子句做like模糊匹配，使用alter union添加新表
TEST_F(t01_alter_union, STREAM_035_021)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer, ref_key integer, "
         "description text) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');",
            GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text);"},
        {"create stream table stream2 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text);"},
        {"create stream view view1 AS select * FROM stream1 where description like 'testx%';"},
        {"create stream sink sink1 AS select * from view1 into tsdb(ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    string alterCmd = "alter stream view view1 alter from_union add stream2;";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel1 = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel1);
    RdVertexLabelT *vertexLabel2 = RdStreamParseTableSchema(creates[2].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel2);

    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 6;
    int64_t id[rowNum] = {1, 2, 3, 4, 5};
    int64_t water_mark[rowNum] = {11, 12, 13, 14, 15};
    int64_t event_time[rowNum] = {21, 22, 23, 24, 25};
    int64_t ref_key[rowNum] = {1, 1, 1, 1, 1};
    char desText[][128] = {"testx data: 1", "test data: 12", "testx data: 123", "test data: 1234", "testx data: 12345"};
    char *description[rowNum] = {0};
    uint16_t descriptionLen[rowNum] = {0};
    for (int i = 0; i < rowNum; ++i) {
        description[i] = desText[i];
        descriptionLen[i] = strlen(desText[i]) + 1;
    }
    bool nullInfo[colNum] = {false, false, false, false, false, false};
    RdStructWriteStreamTable(
        stmt, vertexLabel1, rowNum, id, water_mark, event_time, ref_key, descriptionLen, description, nullInfo);

    int64_t id2[rowNum] = {6, 7, 8, 9, 10};
    int64_t water_mark2[rowNum] = {16, 17, 18, 19, 20};
    int64_t event_time2[rowNum] = {26, 27, 28, 29, 30};
    int64_t ref_key2[rowNum] = {2, 2, 2, 2, 2};
    RdStructWriteStreamTable(
        stmt, vertexLabel2, rowNum, id2, water_mark2, event_time2, ref_key2, descriptionLen, description, nullInfo);

    // 查询ts表中数据
    char *qryCmd = (char *)"select * from ts1 order by id;";
    uint32_t expRowNum = 6;
    uint32_t expColNum = 6;
    int64_t exp_id[expRowNum] = {1, 3, 5, 6, 8, 10};
    int64_t exp_water_mark[expRowNum] = {11, 13, 15, 16, 18, 20};
    int64_t exp_event_time[expRowNum] = {21, 23, 25, 26, 28, 30};
    int64_t exp_ref_key[expRowNum] = {1, 1, 1, 2, 2, 2};
    char *exp_description[expRowNum] = {desText[0], desText[2], desText[4], desText[0], desText[2], desText[4]};
    uint16_t exp_descriptionLen[expRowNum] = {descriptionLen[0], descriptionLen[2], descriptionLen[4],
        descriptionLen[0], descriptionLen[2], descriptionLen[4]};
    RdCheckDataInTSTableOfStreamTable(stmt, qryCmd, expRowNum, expColNum, exp_id, exp_water_mark, exp_event_time,
        exp_ref_key, exp_descriptionLen, exp_description);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop stream table stream2;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel1);
    RdStreamFreeTableSchema(vertexLabel2);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// view带where子句做like模糊匹配，使用alter union添加新表
TEST_F(t01_alter_union, STREAM_035_022)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer, ref_key integer, "
         "description text) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');",
            GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text);"},
        {"create stream table stream2 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text);"},
        {"create stream view view1 AS select * FROM stream1 where description like '%datax%';"},
        {"create stream sink sink1 AS select * from view1 into tsdb(ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    string alterCmd = "alter stream view view1 alter from_union add stream2;";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel1 = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel1);
    RdVertexLabelT *vertexLabel2 = RdStreamParseTableSchema(creates[2].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel2);

    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 6;
    int64_t id[rowNum] = {1, 2, 3, 4, 5};
    int64_t water_mark[rowNum] = {11, 12, 13, 14, 15};
    int64_t event_time[rowNum] = {21, 22, 23, 24, 25};
    int64_t ref_key[rowNum] = {1, 1, 1, 1, 1};
    char desText[][128] = {"test datax: 1", "test data: 12", "test datax: 123", "test data: 1234", "test datax: 12345"};
    char *description[rowNum] = {0};
    uint16_t descriptionLen[rowNum] = {0};
    for (int i = 0; i < rowNum; ++i) {
        description[i] = desText[i];
        descriptionLen[i] = strlen(desText[i]) + 1;
    }
    bool nullInfo[colNum] = {false, false, false, false, false, false};
    RdStructWriteStreamTable(
        stmt, vertexLabel1, rowNum, id, water_mark, event_time, ref_key, descriptionLen, description, nullInfo);

    int64_t id2[rowNum] = {6, 7, 8, 9, 10};
    int64_t water_mark2[rowNum] = {16, 17, 18, 19, 20};
    int64_t event_time2[rowNum] = {26, 27, 28, 29, 30};
    int64_t ref_key2[rowNum] = {2, 2, 2, 2, 2};
    RdStructWriteStreamTable(
        stmt, vertexLabel2, rowNum, id2, water_mark2, event_time2, ref_key2, descriptionLen, description, nullInfo);

    // 查询ts表中数据
    char *qryCmd = (char *)"select * from ts1 order by id;";
    uint32_t expRowNum = 6;
    uint32_t expColNum = 6;
    int64_t exp_id[expRowNum] = {1, 3, 5, 6, 8, 10};
    int64_t exp_water_mark[expRowNum] = {11, 13, 15, 16, 18, 20};
    int64_t exp_event_time[expRowNum] = {21, 23, 25, 26, 28, 30};
    int64_t exp_ref_key[expRowNum] = {1, 1, 1, 2, 2, 2};
    char *exp_description[expRowNum] = {desText[0], desText[2], desText[4], desText[0], desText[2], desText[4]};
    uint16_t exp_descriptionLen[expRowNum] = {descriptionLen[0], descriptionLen[2], descriptionLen[4],
        descriptionLen[0], descriptionLen[2], descriptionLen[4]};
    RdCheckDataInTSTableOfStreamTable(stmt, qryCmd, expRowNum, expColNum, exp_id, exp_water_mark, exp_event_time,
        exp_ref_key, exp_descriptionLen, exp_description);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop stream table stream2;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel1);
    RdStreamFreeTableSchema(vertexLabel2);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// view带seq_distinct_count，使用alter union添加新表
TEST_F(t01_alter_union, STREAM_035_023)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer, ref_key integer, "
         "description text, seq_d_c_id integer) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');",
            GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text);"},
        {"create stream table stream2 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text);"},
        {"create stream view view1 AS select *, seq_distinct_count(id) FROM stream1;"},
        {"create stream sink sink1 AS select id, name, water_mark, event_time, ref_key, description, "
         "seq_distinct_count_id from view1 into tsdb(ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    string alterCmd = "alter stream view view1 alter from_union add stream2;";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel1 = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel1);
    RdVertexLabelT *vertexLabel2 = RdStreamParseTableSchema(creates[2].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel2);

    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 6;
    int64_t id[rowNum] = {1, 2, 1, 1, 5};
    int64_t water_mark[rowNum] = {11, 12, 13, 14, 15};
    int64_t event_time[rowNum] = {21, 22, 23, 24, 25};
    int64_t ref_key[rowNum] = {1, 1, 1, 1, 1};
    char desText[][128] = {"test data: 1", "test data: 12", "test data: 123", "test data: 1234", "test data: 12345"};
    char *description[rowNum] = {0};
    uint16_t descriptionLen[rowNum] = {0};
    for (int i = 0; i < rowNum; ++i) {
        description[i] = desText[i];
        descriptionLen[i] = strlen(desText[i]) + 1;
    }
    bool nullInfo[colNum] = {false, false, false, false, false, false};
    RdStructWriteStreamTable(
        stmt, vertexLabel1, rowNum, id, water_mark, event_time, ref_key, descriptionLen, description, nullInfo);

    int64_t id2[rowNum] = {6, 7, 7, 7, 10};
    int64_t water_mark2[rowNum] = {16, 17, 18, 19, 20};
    int64_t event_time2[rowNum] = {26, 27, 28, 29, 30};
    int64_t ref_key2[rowNum] = {2, 2, 2, 2, 2};
    RdStructWriteStreamTable(
        stmt, vertexLabel2, rowNum, id2, water_mark2, event_time2, ref_key2, descriptionLen, description, nullInfo);

    // 查询ts表中数据
    char *qryCmd = (char *)"select * from ts1 order by id;";
    uint32_t expRowNum = 6;
    uint32_t expColNum = 7;
    int64_t exp_id[expRowNum] = {1, 1, 2, 5, 6, 7};
    int64_t exp_water_mark[expRowNum] = {11, 13, 12, 15, 16, 17};
    int64_t exp_event_time[expRowNum] = {21, 23, 22, 25, 26, 27};
    int64_t exp_ref_key[expRowNum] = {1, 1, 1, 1, 2, 2};
    char *exp_description[expRowNum] = {desText[0], desText[2], desText[1], desText[4], desText[0], desText[1]};
    uint16_t exp_descriptionLen[expRowNum] = {descriptionLen[0], descriptionLen[2], descriptionLen[1],
        descriptionLen[4], descriptionLen[0], descriptionLen[1]};
    int64_t exp_distinct[expRowNum] = {1, 2, 1, 1, 1, 3};
    RdCheckDataInTSTableOfStreamTable4(stmt, qryCmd, expRowNum, expColNum, exp_id, exp_water_mark, exp_event_time,
        exp_ref_key, exp_descriptionLen, exp_description, exp_distinct);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop stream table stream2;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel1);
    RdStreamFreeTableSchema(vertexLabel2);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// view1对stream table1，创建table2，view2映射table2，使用alter union添加view1
TEST_F(t01_alter_union, STREAM_035_024)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer, ref_key integer, "
         "description text) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');",
            GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text);"},
        {"create stream table stream2 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text);"},
        {"create stream view view1 AS select * FROM stream1;"},
        {"create stream view view2 AS select * FROM stream2;"},
        {"create stream sink sink1 AS select * from view2 into tsdb(ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    string alterCmd = "alter stream view view2 alter from_union add view1;";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel1 = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel1);
    RdVertexLabelT *vertexLabel2 = RdStreamParseTableSchema(creates[2].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel2);

    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 6;
    int64_t id[rowNum] = {1, 2, 3, 4, 5};
    int64_t water_mark[rowNum] = {11, 12, 13, 14, 15};
    int64_t event_time[rowNum] = {21, 22, 23, 24, 25};
    int64_t ref_key[rowNum] = {1, 1, 1, 1, 1};
    char desText[][128] = {"test data: 1", "test data: 12", "test data: 123", "test data: 1234", "test data: 12345"};
    char *description[rowNum] = {0};
    uint16_t descriptionLen[rowNum] = {0};
    for (int i = 0; i < rowNum; ++i) {
        description[i] = desText[i];
        descriptionLen[i] = strlen(desText[i]) + 1;
    }
    bool nullInfo[colNum] = {false, false, false, false, false, false};
    RdStructWriteStreamTable(
        stmt, vertexLabel1, rowNum, id, water_mark, event_time, ref_key, descriptionLen, description, nullInfo);

    int64_t id2[rowNum] = {6, 7, 8, 9, 10};
    int64_t water_mark2[rowNum] = {16, 17, 18, 19, 20};
    int64_t event_time2[rowNum] = {26, 27, 28, 29, 30};
    int64_t ref_key2[rowNum] = {2, 2, 2, 2, 2};
    RdStructWriteStreamTable(
        stmt, vertexLabel2, rowNum, id2, water_mark2, event_time2, ref_key2, descriptionLen, description, nullInfo);

    // 查询ts表中数据
    char *qryCmd = (char *)"select * from ts1 order by id;";
    uint32_t expRowNum = 10;
    uint32_t expColNum = 6;
    int64_t exp_id[expRowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    int64_t exp_water_mark[expRowNum] = {11, 12, 13, 14, 15, 16, 17, 18, 19, 20};
    int64_t exp_event_time[expRowNum] = {21, 22, 23, 24, 25, 26, 27, 28, 29, 30};
    int64_t exp_ref_key[expRowNum] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2};
    char *exp_description[expRowNum] = {desText[0], desText[1], desText[2], desText[3], desText[4], desText[0],
        desText[1], desText[2], desText[3], desText[4]};
    uint16_t exp_descriptionLen[expRowNum] = {descriptionLen[0], descriptionLen[1], descriptionLen[2],
        descriptionLen[3], descriptionLen[4], descriptionLen[0], descriptionLen[1], descriptionLen[2],
        descriptionLen[3], descriptionLen[4]};
    RdCheckDataInTSTableOfStreamTable(stmt, qryCmd, expRowNum, expColNum, exp_id, exp_water_mark, exp_event_time,
        exp_ref_key, exp_descriptionLen, exp_description);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view2"},
        {"drop stream view view1"},
        {"drop stream table stream2;"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel1);
    RdStreamFreeTableSchema(vertexLabel2);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// view1对stream table1，view2映射view1，使用alter union添加table 1
TEST_F(t01_alter_union, STREAM_035_025)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer, ref_key integer, "
         "description text) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');",
            GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text);"},
        {"create stream view view1 AS select * FROM stream1;"},
        {"create stream view view2 AS select * FROM view1;"},
        {"create stream sink sink1 AS select * from view2 into tsdb(ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    string alterCmd = "alter stream view view2 alter from_union add stream1;";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel1 = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel1);

    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 6;
    int64_t id[rowNum] = {1, 2, 3, 4, 5};
    int64_t water_mark[rowNum] = {11, 12, 13, 14, 15};
    int64_t event_time[rowNum] = {21, 22, 23, 24, 25};
    int64_t ref_key[rowNum] = {1, 1, 1, 1, 1};
    char desText[][128] = {"test data: 1", "test data: 12", "test data: 123", "test data: 1234", "test data: 12345"};
    char *description[rowNum] = {0};
    uint16_t descriptionLen[rowNum] = {0};
    for (int i = 0; i < rowNum; ++i) {
        description[i] = desText[i];
        descriptionLen[i] = strlen(desText[i]) + 1;
    }
    bool nullInfo[colNum] = {false, false, false, false, false, false};
    RdStructWriteStreamTable(
        stmt, vertexLabel1, rowNum, id, water_mark, event_time, ref_key, descriptionLen, description, nullInfo);

    // 查询ts表中数据
    char *qryCmd = (char *)"select * from ts1 order by id;";
    uint32_t expRowNum = 10;
    uint32_t expColNum = 6;
    int64_t exp_id[expRowNum] = {1, 1, 2, 2, 3, 3, 4, 4, 5, 5};
    int64_t exp_water_mark[expRowNum] = {11, 11, 12, 12, 13, 13, 14, 14, 15, 15};
    int64_t exp_event_time[expRowNum] = {21, 21, 22, 22, 23, 23, 24, 24, 25, 25};
    int64_t exp_ref_key[expRowNum] = {1, 1, 1, 1, 1, 1, 1, 1, 1, 1};
    char *exp_description[expRowNum] = {desText[0], desText[0], desText[1], desText[1], desText[2], desText[2],
        desText[3], desText[3], desText[4], desText[4]};
    uint16_t exp_descriptionLen[expRowNum] = {descriptionLen[0], descriptionLen[0], descriptionLen[1],
        descriptionLen[1], descriptionLen[2], descriptionLen[2], descriptionLen[3], descriptionLen[3],
        descriptionLen[4], descriptionLen[4]};
    RdCheckDataInTSTableOfStreamTable(stmt, qryCmd, expRowNum, expColNum, exp_id, exp_water_mark, exp_event_time,
        exp_ref_key, exp_descriptionLen, exp_description);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view2"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel1);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// view1带窗口，view2映射view1，字段一致，使用alter union添加table 1
TEST_F(t01_alter_union, STREAM_035_026)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer, ref_key integer, "
         "description text) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');",
            GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text, "
         "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
        {"create stream view view1 AS select id, name, water_mark, event_time, ref_key, description "
         "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));"},
        {"create stream view view2 AS select id, name, water_mark, event_time, ref_key, description FROM view1;"},
        {"create stream sink sink1 AS select * from view2 into tsdb(ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    string alterCmd = "alter stream view view2 alter from_union add stream1;";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel1 = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel1);

    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 6;
    int64_t id[rowNum] = {1, 2, 3, 4, 5};
    int64_t water_mark[rowNum] = {11, 12, 13, 14, 15};
    int64_t event_time[rowNum] = {21, 22, 23, 24, 25};
    int64_t ref_key[rowNum] = {1, 1, 1, 1, 1};
    char desText[][128] = {"test data: 1", "test data: 12", "test data: 123", "test data: 1234", "test data: 12345"};
    char *description[rowNum] = {0};
    uint16_t descriptionLen[rowNum] = {0};
    for (int i = 0; i < rowNum; ++i) {
        description[i] = desText[i];
        descriptionLen[i] = strlen(desText[i]) + 1;
    }
    bool nullInfo[colNum] = {false, false, false, false, false, false};
    RdStructWriteStreamTable(
        stmt, vertexLabel1, rowNum, id, water_mark, event_time, ref_key, descriptionLen, description, nullInfo);

    // 查询ts表中数据
    char *qryCmd = (char *)"select * from ts1 order by id;";
    uint32_t expRowNum = 15;
    uint32_t expColNum = 6;
    int64_t exp_id[expRowNum] = {1, 1, 1, 2, 2, 2, 3, 3, 3, 4, 4, 4, 5, 5, 5};
    int64_t exp_water_mark[expRowNum] = {11, 11, 11, 12, 12, 12, 13, 13, 13, 14, 14, 14, 15, 15, 15};
    int64_t exp_event_time[expRowNum] = {21, 21, 21, 22, 22, 22, 23, 23, 23, 24, 24, 24, 25, 25, 25};
    int64_t exp_ref_key[expRowNum] = {1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1};
    char *exp_description[expRowNum] = {desText[0], desText[0], desText[0], desText[1], desText[1], desText[1],
        desText[2], desText[2], desText[2], desText[3], desText[3], desText[3], desText[4], desText[4], desText[4]};
    uint16_t exp_descriptionLen[expRowNum] = {descriptionLen[0], descriptionLen[0], descriptionLen[0],
        descriptionLen[1], descriptionLen[1], descriptionLen[1], descriptionLen[2], descriptionLen[2],
        descriptionLen[2], descriptionLen[3], descriptionLen[3], descriptionLen[3], descriptionLen[4],
        descriptionLen[4], descriptionLen[4]};
    RdCheckDataInTSTableOfStreamTable(stmt, qryCmd, expRowNum, expColNum, exp_id, exp_water_mark, exp_event_time,
        exp_ref_key, exp_descriptionLen, exp_description);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view2"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel1);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建两个流表，schema相同，一个表某列设置watermark，另一表不设，view映射table1，使用alter union添加table2
TEST_F(t01_alter_union, STREAM_035_027)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer, ref_key integer, "
         "description text) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');",
            GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text, "
         "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
        {"create stream table stream2 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text);"},
        {"create stream view view1 AS select id, name, water_mark, event_time, ref_key, description FROM stream1;"},
        {"create stream sink sink1 AS select * from view1 into tsdb(ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    string alterCmd = "alter stream view view1 alter from_union add stream2;";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel1 = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel1);
    RdVertexLabelT *vertexLabel2 = RdStreamParseTableSchema(creates[2].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel2);

    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 6;
    int64_t id[rowNum] = {1, 2, 3, 4, 5};
    int64_t water_mark[rowNum] = {11, 12, 13, 14, 15};
    int64_t event_time[rowNum] = {21, 22, 23, 24, 25};
    int64_t ref_key[rowNum] = {1, 1, 1, 1, 1};
    char desText[][128] = {"test data: 1", "test data: 12", "test data: 123", "test data: 1234", "test data: 12345"};
    char *description[rowNum] = {0};
    uint16_t descriptionLen[rowNum] = {0};
    for (int i = 0; i < rowNum; ++i) {
        description[i] = desText[i];
        descriptionLen[i] = strlen(desText[i]) + 1;
    }
    bool nullInfo[colNum] = {false, false, false, false, false, false};
    RdStructWriteStreamTable(
        stmt, vertexLabel1, rowNum, id, water_mark, event_time, ref_key, descriptionLen, description, nullInfo);

    int64_t id2[rowNum] = {6, 7, 8, 9, 10};
    int64_t water_mark2[rowNum] = {16, 17, 18, 19, 20};
    int64_t event_time2[rowNum] = {26, 27, 28, 29, 30};
    int64_t ref_key2[rowNum] = {2, 2, 2, 2, 2};
    RdStructWriteStreamTable(
        stmt, vertexLabel2, rowNum, id2, water_mark2, event_time2, ref_key2, descriptionLen, description, nullInfo);

    // 查询ts表中数据
    char *qryCmd = (char *)"select * from ts1 order by id;";
    uint32_t expRowNum = 10;
    uint32_t expColNum = 6;
    int64_t exp_id[expRowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    int64_t exp_water_mark[expRowNum] = {11, 12, 13, 14, 15, 16, 17, 18, 19, 20};
    int64_t exp_event_time[expRowNum] = {21, 22, 23, 24, 25, 26, 27, 28, 29, 30};
    int64_t exp_ref_key[expRowNum] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2};
    char *exp_description[expRowNum] = {desText[0], desText[1], desText[2], desText[3], desText[4], desText[0],
        desText[1], desText[2], desText[3], desText[4]};
    uint16_t exp_descriptionLen[expRowNum] = {descriptionLen[0], descriptionLen[1], descriptionLen[2],
        descriptionLen[3], descriptionLen[4], descriptionLen[0], descriptionLen[1], descriptionLen[2],
        descriptionLen[3], descriptionLen[4]};
    RdCheckDataInTSTableOfStreamTable(stmt, qryCmd, expRowNum, expColNum, exp_id, exp_water_mark, exp_event_time,
        exp_ref_key, exp_descriptionLen, exp_description);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream2;"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel1);
    RdStreamFreeTableSchema(vertexLabel2);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建两个流表，schema相同，watermark一致，view映射table1，使用alter union添加table2
TEST_F(t01_alter_union, STREAM_035_028)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer, ref_key integer, "
         "description text) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');",
            GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text, "
         "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
        {"create stream table stream2 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text, "
         "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
        {"create stream view view1 AS select id, name, water_mark, event_time, ref_key, description FROM stream1;"},
        {"create stream sink sink1 AS select * from view1 into tsdb(ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    string alterCmd = "alter stream view view1 alter from_union add stream2;";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel1 = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel1);
    RdVertexLabelT *vertexLabel2 = RdStreamParseTableSchema(creates[2].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel2);

    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 6;
    int64_t id[rowNum] = {1, 2, 3, 4, 5};
    int64_t water_mark[rowNum] = {11, 12, 13, 14, 15};
    int64_t event_time[rowNum] = {21, 22, 23, 24, 25};
    int64_t ref_key[rowNum] = {1, 1, 1, 1, 1};
    char desText[][128] = {"test data: 1", "test data: 12", "test data: 123", "test data: 1234", "test data: 12345"};
    char *description[rowNum] = {0};
    uint16_t descriptionLen[rowNum] = {0};
    for (int i = 0; i < rowNum; ++i) {
        description[i] = desText[i];
        descriptionLen[i] = strlen(desText[i]) + 1;
    }
    bool nullInfo[colNum] = {false, false, false, false, false, false};
    RdStructWriteStreamTable(
        stmt, vertexLabel1, rowNum, id, water_mark, event_time, ref_key, descriptionLen, description, nullInfo);

    int64_t id2[rowNum] = {6, 7, 8, 9, 10};
    int64_t water_mark2[rowNum] = {16, 17, 18, 19, 20};
    int64_t event_time2[rowNum] = {26, 27, 28, 29, 30};
    int64_t ref_key2[rowNum] = {2, 2, 2, 2, 2};
    RdStructWriteStreamTable(
        stmt, vertexLabel2, rowNum, id2, water_mark2, event_time2, ref_key2, descriptionLen, description, nullInfo);

    // 查询ts表中数据
    char *qryCmd = (char *)"select * from ts1 order by id;";
    uint32_t expRowNum = 10;
    uint32_t expColNum = 6;
    int64_t exp_id[expRowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    int64_t exp_water_mark[expRowNum] = {11, 12, 13, 14, 15, 16, 17, 18, 19, 20};
    int64_t exp_event_time[expRowNum] = {21, 22, 23, 24, 25, 26, 27, 28, 29, 30};
    int64_t exp_ref_key[expRowNum] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2};
    char *exp_description[expRowNum] = {desText[0], desText[1], desText[2], desText[3], desText[4], desText[0],
        desText[1], desText[2], desText[3], desText[4]};
    uint16_t exp_descriptionLen[expRowNum] = {descriptionLen[0], descriptionLen[1], descriptionLen[2],
        descriptionLen[3], descriptionLen[4], descriptionLen[0], descriptionLen[1], descriptionLen[2],
        descriptionLen[3], descriptionLen[4]};
    RdCheckDataInTSTableOfStreamTable(stmt, qryCmd, expRowNum, expColNum, exp_id, exp_water_mark, exp_event_time,
        exp_ref_key, exp_descriptionLen, exp_description);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream2;"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel1);
    RdStreamFreeTableSchema(vertexLabel2);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建两个流表，schema相同，table1不带watermark，view1映射table1，table2带watermark，view2映射table2，使用alter
// union在view2添加view1
TEST_F(t01_alter_union, STREAM_035_029)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer, ref_key integer, "
         "description text) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');",
            GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text);"},
        {"create stream table stream2 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text, "
         "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
        {"create stream view view1 AS select id, name, water_mark, event_time, ref_key, description FROM stream1;"},
        {"create stream view view2 AS select id, name, water_mark, event_time, ref_key, description FROM stream2;"},
        {"create stream sink sink1 AS select * from view2 into tsdb(ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    string alterCmd = "alter stream view view2 alter from_union add view1;";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel1 = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel1);
    RdVertexLabelT *vertexLabel2 = RdStreamParseTableSchema(creates[2].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel2);

    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 6;
    int64_t id[rowNum] = {1, 2, 3, 4, 5};
    int64_t water_mark[rowNum] = {11, 12, 13, 14, 15};
    int64_t event_time[rowNum] = {21, 22, 23, 24, 25};
    int64_t ref_key[rowNum] = {1, 1, 1, 1, 1};
    char desText[][128] = {"test data: 1", "test data: 12", "test data: 123", "test data: 1234", "test data: 12345"};
    char *description[rowNum] = {0};
    uint16_t descriptionLen[rowNum] = {0};
    for (int i = 0; i < rowNum; ++i) {
        description[i] = desText[i];
        descriptionLen[i] = strlen(desText[i]) + 1;
    }
    bool nullInfo[colNum] = {false, false, false, false, false, false};
    RdStructWriteStreamTable(
        stmt, vertexLabel1, rowNum, id, water_mark, event_time, ref_key, descriptionLen, description, nullInfo);

    int64_t id2[rowNum] = {6, 7, 8, 9, 10};
    int64_t water_mark2[rowNum] = {16, 17, 18, 19, 20};
    int64_t event_time2[rowNum] = {26, 27, 28, 29, 30};
    int64_t ref_key2[rowNum] = {2, 2, 2, 2, 2};
    RdStructWriteStreamTable(
        stmt, vertexLabel2, rowNum, id2, water_mark2, event_time2, ref_key2, descriptionLen, description, nullInfo);

    // 查询ts表中数据
    char *qryCmd = (char *)"select * from ts1 order by id;";
    uint32_t expRowNum = 10;
    uint32_t expColNum = 6;
    int64_t exp_id[expRowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    int64_t exp_water_mark[expRowNum] = {11, 12, 13, 14, 15, 16, 17, 18, 19, 20};
    int64_t exp_event_time[expRowNum] = {21, 22, 23, 24, 25, 26, 27, 28, 29, 30};
    int64_t exp_ref_key[expRowNum] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2};
    char *exp_description[expRowNum] = {desText[0], desText[1], desText[2], desText[3], desText[4], desText[0],
        desText[1], desText[2], desText[3], desText[4]};
    uint16_t exp_descriptionLen[expRowNum] = {descriptionLen[0], descriptionLen[1], descriptionLen[2],
        descriptionLen[3], descriptionLen[4], descriptionLen[0], descriptionLen[1], descriptionLen[2],
        descriptionLen[3], descriptionLen[4]};
    RdCheckDataInTSTableOfStreamTable(stmt, qryCmd, expRowNum, expColNum, exp_id, exp_water_mark, exp_event_time,
        exp_ref_key, exp_descriptionLen, exp_description);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view2"},
        {"drop stream view view1"},
        {"drop stream table stream2;"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel1);
    RdStreamFreeTableSchema(vertexLabel2);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// view中union三个dispatch，使用alter union删除其中两个，写入数据
TEST_F(t01_alter_union, STREAM_035_030)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer, ref_key integer, "
         "description text) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');",
            GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text) dispatch by id, event_time;"},
        {"create stream table stream2 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text) dispatch by id, event_time;"},
        {"create stream table stream3 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text) dispatch by id, event_time;"},
        {"create stream view view1 AS select id, name, water_mark, event_time, ref_key, description "
         "FROM TABLE(UNION(TABLE(dispatch(table stream1, 1, 21)), TABLE(dispatch(table stream2, 1, 21)), "
         "TABLE(dispatch(table stream3, 1, 21))));"},
        {"create stream sink sink1 AS select * from view1 into tsdb(ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    string alterCmd = "alter stream view view1 alter from_union drop stream2;";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    ASSERT_EQ(GMERR_OK, ret);
    alterCmd = "alter stream view view1 alter from_union drop stream3;";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel1 = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel1);

    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 6;
    int64_t id[rowNum] = {1, 2, 3, 4, 5};
    int64_t water_mark[rowNum] = {11, 12, 13, 14, 15};
    int64_t event_time[rowNum] = {21, 22, 23, 24, 25};
    int64_t ref_key[rowNum] = {1, 1, 1, 1, 1};
    char desText[][128] = {"test data: 1", "test data: 12", "test data: 123", "test data: 1234", "test data: 12345"};
    char *description[rowNum] = {0};
    uint16_t descriptionLen[rowNum] = {0};
    for (int i = 0; i < rowNum; ++i) {
        description[i] = desText[i];
        descriptionLen[i] = strlen(desText[i]) + 1;
    }
    bool nullInfo[colNum] = {false, false, false, false, false, false};
    RdStructWriteStreamTable(
        stmt, vertexLabel1, rowNum, id, water_mark, event_time, ref_key, descriptionLen, description, nullInfo);

    // 查询ts表中数据
    char *qryCmd = (char *)"select * from ts1 order by id;";
    uint32_t expRowNum = 1;
    uint32_t expColNum = 6;
    int64_t exp_id[expRowNum] = {1};
    int64_t exp_water_mark[expRowNum] = {11};
    int64_t exp_event_time[expRowNum] = {21};
    int64_t exp_ref_key[expRowNum] = {1};
    char *exp_description[expRowNum] = {desText[0]};
    uint16_t exp_descriptionLen[expRowNum] = {descriptionLen[0]};
    RdCheckDataInTSTableOfStreamTable(stmt, qryCmd, expRowNum, expColNum, exp_id, exp_water_mark, exp_event_time,
        exp_ref_key, exp_descriptionLen, exp_description);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream3;"},
        {"drop stream table stream2;"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel1);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// sink中union三个dispatch，使用alter union删除其中两个，写入数据
TEST_F(t01_alter_union, STREAM_035_031)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer, ref_key integer, "
         "description text) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');",
            GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text) dispatch by id, event_time;"},
        {"create stream table stream2 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text) dispatch by id, event_time;"},
        {"create stream table stream3 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text) dispatch by id, event_time;"},
        {"create stream sink sink1 AS select * FROM "
         "TABLE(UNION(TABLE(dispatch(table stream1, 1, 21)), "
         "TABLE(dispatch(table stream2, 1, 21)), TABLE(dispatch(table stream3, 1, 21)))) "
         "into tsdb(ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    string alterCmd = "alter stream sink sink1 alter from_union drop stream2;";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    ASSERT_EQ(GMERR_OK, ret);
    alterCmd = "alter stream sink sink1 alter from_union drop stream3;";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel1 = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel1);

    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 6;
    int64_t id[rowNum] = {1, 2, 3, 4, 5};
    int64_t water_mark[rowNum] = {11, 12, 13, 14, 15};
    int64_t event_time[rowNum] = {21, 22, 23, 24, 25};
    int64_t ref_key[rowNum] = {1, 1, 1, 1, 1};
    char desText[][128] = {"test data: 1", "test data: 12", "test data: 123", "test data: 1234", "test data: 12345"};
    char *description[rowNum] = {0};
    uint16_t descriptionLen[rowNum] = {0};
    for (int i = 0; i < rowNum; ++i) {
        description[i] = desText[i];
        descriptionLen[i] = strlen(desText[i]) + 1;
    }
    bool nullInfo[colNum] = {false, false, false, false, false, false};
    RdStructWriteStreamTable(
        stmt, vertexLabel1, rowNum, id, water_mark, event_time, ref_key, descriptionLen, description, nullInfo);

    // 查询ts表中数据
    char *qryCmd = (char *)"select * from ts1 order by id;";
    uint32_t expRowNum = 1;
    uint32_t expColNum = 6;
    int64_t exp_id[expRowNum] = {1};
    int64_t exp_water_mark[expRowNum] = {11};
    int64_t exp_event_time[expRowNum] = {21};
    int64_t exp_ref_key[expRowNum] = {1};
    char *exp_description[expRowNum] = {desText[0]};
    uint16_t exp_descriptionLen[expRowNum] = {descriptionLen[0]};
    RdCheckDataInTSTableOfStreamTable(stmt, qryCmd, expRowNum, expColNum, exp_id, exp_water_mark, exp_event_time,
        exp_ref_key, exp_descriptionLen, exp_description);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream table stream3;"},
        {"drop stream table stream2;"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel1);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// view映射dispatch，使用alter union新增一个dispatch，dispatch by列不同，写入数据
TEST_F(t01_alter_union, STREAM_035_032)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer, ref_key integer, "
         "description text) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');",
            GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text) dispatch by id, event_time;"},
        {"create stream table stream2 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text) dispatch by id, event_time;"},
        {"create stream table stream3 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text) dispatch by id, event_time;"},
        {"create stream view view1 AS select id, name, water_mark, event_time, ref_key, description "
         "FROM TABLE(UNION(TABLE(dispatch(table stream1, 1, 21)), TABLE(dispatch(table stream2, 1, 21))));"},
        {"create stream sink sink1 AS select * from view1 into tsdb(ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    string alterCmd = "alter stream view view1 alter from_union add TABLE(DISPATCH(table stream3, 6, 26));";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel1 = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel1);
    RdVertexLabelT *vertexLabel2 = RdStreamParseTableSchema(creates[2].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel2);
    RdVertexLabelT *vertexLabel3 = RdStreamParseTableSchema(creates[3].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel3);

    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 6;
    int64_t id[rowNum] = {1, 2, 3, 4, 5};
    int64_t water_mark[rowNum] = {11, 12, 13, 14, 15};
    int64_t event_time[rowNum] = {21, 22, 23, 24, 25};
    int64_t ref_key[rowNum] = {1, 1, 1, 1, 1};
    char desText[][128] = {"test data: 1", "test data: 12", "test data: 123", "test data: 1234", "test data: 12345"};
    char *description[rowNum] = {0};
    uint16_t descriptionLen[rowNum] = {0};
    for (int i = 0; i < rowNum; ++i) {
        description[i] = desText[i];
        descriptionLen[i] = strlen(desText[i]) + 1;
    }
    bool nullInfo[colNum] = {false, false, false, false, false, false};
    RdStructWriteStreamTable(
        stmt, vertexLabel1, rowNum, id, water_mark, event_time, ref_key, descriptionLen, description, nullInfo);
    RdStructWriteStreamTable(
        stmt, vertexLabel2, rowNum, id, water_mark, event_time, ref_key, descriptionLen, description, nullInfo);

    int64_t id2[rowNum] = {6, 7, 8, 9, 10};
    int64_t water_mark2[rowNum] = {16, 17, 18, 19, 20};
    int64_t event_time2[rowNum] = {26, 27, 28, 29, 30};
    int64_t ref_key2[rowNum] = {2, 2, 2, 2, 2};
    RdStructWriteStreamTable(
        stmt, vertexLabel3, rowNum, id2, water_mark2, event_time2, ref_key2, descriptionLen, description, nullInfo);

    // 查询ts表中数据
    char *qryCmd = (char *)"select * from ts1 order by id;";
    uint32_t expRowNum = 3;
    uint32_t expColNum = 6;
    int64_t exp_id[expRowNum] = {1, 1, 6};
    int64_t exp_water_mark[expRowNum] = {11, 11, 16};
    int64_t exp_event_time[expRowNum] = {21, 21, 26};
    int64_t exp_ref_key[expRowNum] = {1, 1, 2};
    char *exp_description[expRowNum] = {desText[0], desText[0], desText[0]};
    uint16_t exp_descriptionLen[expRowNum] = {descriptionLen[0], descriptionLen[0], descriptionLen[0]};
    RdCheckDataInTSTableOfStreamTable(stmt, qryCmd, expRowNum, expColNum, exp_id, exp_water_mark, exp_event_time,
        exp_ref_key, exp_descriptionLen, exp_description);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream3;"},
        {"drop stream table stream2;"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel1);
    RdStreamFreeTableSchema(vertexLabel2);
    RdStreamFreeTableSchema(vertexLabel3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// sink映射dispatch，使用alter union新增一个dispatch，dispatch by列不同，写入数据
TEST_F(t01_alter_union, STREAM_035_033)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer, ref_key integer, "
         "description text) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');",
            GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text) dispatch by id, event_time;"},
        {"create stream table stream2 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text) dispatch by id, event_time;"},
        {"create stream table stream3 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text) dispatch by id, event_time;"},
        {"create stream sink sink1 AS select * from TABLE(UNION(TABLE(dispatch(table stream1, 1, 21)), "
         "TABLE(dispatch(table stream2, 1, 21)))) into tsdb(ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    string alterCmd = "alter stream sink sink1 alter from_union add TABLE(DISPATCH(table stream3, 6, 26));";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel1 = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel1);
    RdVertexLabelT *vertexLabel2 = RdStreamParseTableSchema(creates[2].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel2);
    RdVertexLabelT *vertexLabel3 = RdStreamParseTableSchema(creates[3].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel3);

    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 6;
    int64_t id[rowNum] = {1, 2, 3, 4, 5};
    int64_t water_mark[rowNum] = {11, 12, 13, 14, 15};
    int64_t event_time[rowNum] = {21, 22, 23, 24, 25};
    int64_t ref_key[rowNum] = {1, 1, 1, 1, 1};
    char desText[][128] = {"test data: 1", "test data: 12", "test data: 123", "test data: 1234", "test data: 12345"};
    char *description[rowNum] = {0};
    uint16_t descriptionLen[rowNum] = {0};
    for (int i = 0; i < rowNum; ++i) {
        description[i] = desText[i];
        descriptionLen[i] = strlen(desText[i]) + 1;
    }
    bool nullInfo[colNum] = {false, false, false, false, false, false};
    RdStructWriteStreamTable(
        stmt, vertexLabel1, rowNum, id, water_mark, event_time, ref_key, descriptionLen, description, nullInfo);
    RdStructWriteStreamTable(
        stmt, vertexLabel2, rowNum, id, water_mark, event_time, ref_key, descriptionLen, description, nullInfo);

    int64_t id2[rowNum] = {6, 7, 8, 9, 10};
    int64_t water_mark2[rowNum] = {16, 17, 18, 19, 20};
    int64_t event_time2[rowNum] = {26, 27, 28, 29, 30};
    int64_t ref_key2[rowNum] = {2, 2, 2, 2, 2};
    RdStructWriteStreamTable(
        stmt, vertexLabel3, rowNum, id2, water_mark2, event_time2, ref_key2, descriptionLen, description, nullInfo);

    // 查询ts表中数据
    char *qryCmd = (char *)"select * from ts1 order by id;";
    uint32_t expRowNum = 3;
    uint32_t expColNum = 6;
    int64_t exp_id[expRowNum] = {1, 1, 6};
    int64_t exp_water_mark[expRowNum] = {11, 11, 16};
    int64_t exp_event_time[expRowNum] = {21, 21, 26};
    int64_t exp_ref_key[expRowNum] = {1, 1, 2};
    char *exp_description[expRowNum] = {desText[0], desText[0], desText[0]};
    uint16_t exp_descriptionLen[expRowNum] = {descriptionLen[0], descriptionLen[0], descriptionLen[0]};
    RdCheckDataInTSTableOfStreamTable(stmt, qryCmd, expRowNum, expColNum, exp_id, exp_water_mark, exp_event_time,
        exp_ref_key, exp_descriptionLen, exp_description);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream table stream3;"},
        {"drop stream table stream2;"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel1);
    RdStreamFreeTableSchema(vertexLabel2);
    RdStreamFreeTableSchema(vertexLabel3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// alter union添加带dispatch的表，原表无dispatch
TEST_F(t01_alter_union, STREAM_035_034)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer, ref_key integer, "
         "description text) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');",
            GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text);"},
        {"create stream table stream2 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text) dispatch by id, event_time;"},
        {"create stream view view1 AS select id, name, water_mark, event_time, ref_key, description FROM stream1;"},
        {"create stream sink sink1 AS select * from view1 into tsdb(ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    string alterCmd = "alter stream view view1 alter from_union add TABLE(DISPATCH(table stream2, 6, 26));";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel1 = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel1);
    RdVertexLabelT *vertexLabel2 = RdStreamParseTableSchema(creates[2].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel2);

    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 6;
    int64_t id[rowNum] = {1, 2, 3, 4, 5};
    int64_t water_mark[rowNum] = {11, 12, 13, 14, 15};
    int64_t event_time[rowNum] = {21, 22, 23, 24, 25};
    int64_t ref_key[rowNum] = {1, 1, 1, 1, 1};
    char desText[][128] = {"test data: 1", "test data: 12", "test data: 123", "test data: 1234", "test data: 12345"};
    char *description[rowNum] = {0};
    uint16_t descriptionLen[rowNum] = {0};
    for (int i = 0; i < rowNum; ++i) {
        description[i] = desText[i];
        descriptionLen[i] = strlen(desText[i]) + 1;
    }
    bool nullInfo[colNum] = {false, false, false, false, false, false};
    RdStructWriteStreamTable(
        stmt, vertexLabel1, rowNum, id, water_mark, event_time, ref_key, descriptionLen, description, nullInfo);

    int64_t id2[rowNum] = {6, 7, 8, 9, 10};
    int64_t water_mark2[rowNum] = {16, 17, 18, 19, 20};
    int64_t event_time2[rowNum] = {26, 27, 28, 29, 30};
    int64_t ref_key2[rowNum] = {2, 2, 2, 2, 2};
    RdStructWriteStreamTable(
        stmt, vertexLabel2, rowNum, id2, water_mark2, event_time2, ref_key2, descriptionLen, description, nullInfo);

    // 查询ts表中数据
    char *qryCmd = (char *)"select * from ts1 order by id;";
    uint32_t expRowNum = 6;
    uint32_t expColNum = 6;
    int64_t exp_id[expRowNum] = {1, 2, 3, 4, 5, 6};
    int64_t exp_water_mark[expRowNum] = {11, 12, 13, 14, 15, 16};
    int64_t exp_event_time[expRowNum] = {21, 22, 23, 24, 25, 26};
    int64_t exp_ref_key[expRowNum] = {1, 1, 1, 1, 1, 2};
    char *exp_description[expRowNum] = {desText[0], desText[1], desText[2], desText[3], desText[4], desText[0]};
    uint16_t exp_descriptionLen[expRowNum] = {descriptionLen[0], descriptionLen[1], descriptionLen[2],
        descriptionLen[3], descriptionLen[4], descriptionLen[0]};
    RdCheckDataInTSTableOfStreamTable(stmt, qryCmd, expRowNum, expColNum, exp_id, exp_water_mark, exp_event_time,
        exp_ref_key, exp_descriptionLen, exp_description);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream2;"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel1);
    RdStreamFreeTableSchema(vertexLabel2);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// alter union添加不带dispatch的表，原表有dispatch
TEST_F(t01_alter_union, STREAM_035_035)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer, ref_key integer, "
         "description text) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');",
            GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text) dispatch by id, event_time;"},
        {"create stream table stream2 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text);"},
        {"create stream view view1 AS select id, name, water_mark, event_time, ref_key, description FROM "
         "TABLE(DISPATCH(table stream1, 1, 21));"},
        {"create stream sink sink1 AS select * from view1 into tsdb(ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    string alterCmd = "alter stream view view1 alter from_union add stream2;";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel1 = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel1);
    RdVertexLabelT *vertexLabel2 = RdStreamParseTableSchema(creates[2].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel2);

    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 6;
    int64_t id[rowNum] = {1, 2, 3, 4, 5};
    int64_t water_mark[rowNum] = {11, 12, 13, 14, 15};
    int64_t event_time[rowNum] = {21, 22, 23, 24, 25};
    int64_t ref_key[rowNum] = {1, 1, 1, 1, 1};
    char desText[][128] = {"test data: 1", "test data: 12", "test data: 123", "test data: 1234", "test data: 12345"};
    char *description[rowNum] = {0};
    uint16_t descriptionLen[rowNum] = {0};
    for (int i = 0; i < rowNum; ++i) {
        description[i] = desText[i];
        descriptionLen[i] = strlen(desText[i]) + 1;
    }
    bool nullInfo[colNum] = {false, false, false, false, false, false};
    RdStructWriteStreamTable(
        stmt, vertexLabel1, rowNum, id, water_mark, event_time, ref_key, descriptionLen, description, nullInfo);

    int64_t id2[rowNum] = {6, 7, 8, 9, 10};
    int64_t water_mark2[rowNum] = {16, 17, 18, 19, 20};
    int64_t event_time2[rowNum] = {26, 27, 28, 29, 30};
    int64_t ref_key2[rowNum] = {2, 2, 2, 2, 2};
    RdStructWriteStreamTable(
        stmt, vertexLabel2, rowNum, id2, water_mark2, event_time2, ref_key2, descriptionLen, description, nullInfo);

    // 查询ts表中数据
    char *qryCmd = (char *)"select * from ts1 order by id;";
    uint32_t expRowNum = 6;
    uint32_t expColNum = 6;
    int64_t exp_id[expRowNum] = {1, 6, 7, 8, 9, 10};
    int64_t exp_water_mark[expRowNum] = {11, 16, 17, 18, 19, 20};
    int64_t exp_event_time[expRowNum] = {21, 26, 27, 28, 29, 30};
    int64_t exp_ref_key[expRowNum] = {1, 2, 2, 2, 2, 2};
    char *exp_description[expRowNum] = {desText[0], desText[0], desText[1], desText[2], desText[3], desText[4]};
    uint16_t exp_descriptionLen[expRowNum] = {descriptionLen[0], descriptionLen[0], descriptionLen[1],
        descriptionLen[2], descriptionLen[3], descriptionLen[4]};
    RdCheckDataInTSTableOfStreamTable(stmt, qryCmd, expRowNum, expColNum, exp_id, exp_water_mark, exp_event_time,
        exp_ref_key, exp_descriptionLen, exp_description);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream2;"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel1);
    RdStreamFreeTableSchema(vertexLabel2);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// sink中union一个流表和该流表的view，alter union添加一个schema相同的流表，写入数据
TEST_F(t01_alter_union, STREAM_035_036)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer, mini_ref_key integer, "
         "description text) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');",
            GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer, mini_ref_key "
         "integer, description text);"},
        {"create stream table stream2 (id integer, name char(50), water_mark integer, event_time integer, mini_ref_key "
         "integer, description text);"},
        {"create stream view view1 AS select * FROM stream1;"},
        {"create stream sink sink1 AS select * from TABLE(UNION(TABLE stream1, TABLE view1)) into tsdb(ts1) with "
         "(batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    string alterCmd = "alter stream sink sink1 alter from_union add stream2;";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel1 = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel1);
    RdVertexLabelT *vertexLabel2 = RdStreamParseTableSchema(creates[2].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel2);

    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 6;
    int64_t id[rowNum] = {1, 2, 3, 4, 5};
    int64_t water_mark[rowNum] = {11, 12, 13, 14, 15};
    int64_t event_time[rowNum] = {21, 22, 23, 24, 25};
    int64_t ref_key[rowNum] = {1, 1, 1, 1, 1};
    char desText[][128] = {"test data: 1", "test data: 12", "test data: 123", "test data: 1234", "test data: 12345"};
    char *description[rowNum] = {0};
    uint16_t descriptionLen[rowNum] = {0};
    for (int i = 0; i < rowNum; ++i) {
        description[i] = desText[i];
        descriptionLen[i] = strlen(desText[i]) + 1;
    }
    bool nullInfo[colNum] = {false, false, false, false, false, false};
    RdStructWriteStreamTable(
        stmt, vertexLabel1, rowNum, id, water_mark, event_time, ref_key, descriptionLen, description, nullInfo);

    int64_t id2[rowNum] = {6, 7, 8, 9, 10};
    int64_t water_mark2[rowNum] = {16, 17, 18, 19, 20};
    int64_t event_time2[rowNum] = {26, 27, 28, 29, 30};
    int64_t ref_key2[rowNum] = {2, 2, 2, 2, 2};
    RdStructWriteStreamTable(
        stmt, vertexLabel2, rowNum, id2, water_mark2, event_time2, ref_key2, descriptionLen, description, nullInfo);

    // 查询ts表中数据
    char *qryCmd = (char *)"select * from ts1 order by id;";
    uint32_t expRowNum = 15;
    uint32_t expColNum = 6;
    int64_t exp_id[expRowNum] = {1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 7, 8, 9, 10};
    int64_t exp_water_mark[expRowNum] = {11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 16, 17, 18, 19, 20};
    int64_t exp_event_time[expRowNum] = {21, 21, 22, 22, 23, 23, 24, 24, 25, 25, 26, 27, 28, 29, 30};
    int64_t exp_ref_key[expRowNum] = {1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2};
    char *exp_description[expRowNum] = {desText[0], desText[0], desText[1], desText[1], desText[2], desText[2],
        desText[3], desText[3], desText[4], desText[4], desText[0], desText[1], desText[2], desText[3], desText[4]};
    uint16_t exp_descriptionLen[expRowNum] = {descriptionLen[0], descriptionLen[0], descriptionLen[1],
        descriptionLen[1], descriptionLen[2], descriptionLen[2], descriptionLen[3], descriptionLen[3],
        descriptionLen[4], descriptionLen[4], descriptionLen[0], descriptionLen[1], descriptionLen[2],
        descriptionLen[3], descriptionLen[4]};
    RdCheckDataInTSTableOfStreamTable(stmt, qryCmd, expRowNum, expColNum, exp_id, exp_water_mark, exp_event_time,
        exp_ref_key, exp_descriptionLen, exp_description);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop stream table stream2;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel1);
    RdStreamFreeTableSchema(vertexLabel2);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// sink中union一个流表和该流表的view，alter union添加一个schema相同的流表的view，写入数据
TEST_F(t01_alter_union, STREAM_035_037)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer, mini_ref_key integer, "
         "description text) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');",
            GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer, mini_ref_key "
         "integer, description text);"},
        {"create stream table stream2 (id integer, name char(50), water_mark integer, event_time integer, mini_ref_key "
         "integer, description text);"},
        {"create stream view view1 AS select * FROM stream1;"},
        {"create stream view view2 AS select * FROM stream2;"},
        {"create stream sink sink1 AS select * from TABLE(UNION(TABLE stream1, TABLE view1)) into tsdb(ts1) with "
         "(batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    string alterCmd = "alter stream sink sink1 alter from_union add view2;";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel1 = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel1);
    RdVertexLabelT *vertexLabel2 = RdStreamParseTableSchema(creates[2].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel2);

    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 6;
    int64_t id[rowNum] = {1, 2, 3, 4, 5};
    int64_t water_mark[rowNum] = {11, 12, 13, 14, 15};
    int64_t event_time[rowNum] = {21, 22, 23, 24, 25};
    int64_t ref_key[rowNum] = {1, 1, 1, 1, 1};
    char desText[][128] = {"test data: 1", "test data: 12", "test data: 123", "test data: 1234", "test data: 12345"};
    char *description[rowNum] = {0};
    uint16_t descriptionLen[rowNum] = {0};
    for (int i = 0; i < rowNum; ++i) {
        description[i] = desText[i];
        descriptionLen[i] = strlen(desText[i]) + 1;
    }
    bool nullInfo[colNum] = {false, false, false, false, false, false};
    RdStructWriteStreamTable(
        stmt, vertexLabel1, rowNum, id, water_mark, event_time, ref_key, descriptionLen, description, nullInfo);

    int64_t id2[rowNum] = {6, 7, 8, 9, 10};
    int64_t water_mark2[rowNum] = {16, 17, 18, 19, 20};
    int64_t event_time2[rowNum] = {26, 27, 28, 29, 30};
    int64_t ref_key2[rowNum] = {2, 2, 2, 2, 2};
    RdStructWriteStreamTable(
        stmt, vertexLabel2, rowNum, id2, water_mark2, event_time2, ref_key2, descriptionLen, description, nullInfo);

    // 查询ts表中数据
    char *qryCmd = (char *)"select * from ts1 order by id;";
    uint32_t expRowNum = 15;
    uint32_t expColNum = 6;
    int64_t exp_id[expRowNum] = {1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 7, 8, 9, 10};
    int64_t exp_water_mark[expRowNum] = {11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 16, 17, 18, 19, 20};
    int64_t exp_event_time[expRowNum] = {21, 21, 22, 22, 23, 23, 24, 24, 25, 25, 26, 27, 28, 29, 30};
    int64_t exp_ref_key[expRowNum] = {1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2};
    char *exp_description[expRowNum] = {desText[0], desText[0], desText[1], desText[1], desText[2], desText[2],
        desText[3], desText[3], desText[4], desText[4], desText[0], desText[1], desText[2], desText[3], desText[4]};
    uint16_t exp_descriptionLen[expRowNum] = {descriptionLen[0], descriptionLen[0], descriptionLen[1],
        descriptionLen[1], descriptionLen[2], descriptionLen[2], descriptionLen[3], descriptionLen[3],
        descriptionLen[4], descriptionLen[4], descriptionLen[0], descriptionLen[1], descriptionLen[2],
        descriptionLen[3], descriptionLen[4]};
    RdCheckDataInTSTableOfStreamTable(stmt, qryCmd, expRowNum, expColNum, exp_id, exp_water_mark, exp_event_time,
        exp_ref_key, exp_descriptionLen, exp_description);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream view view2"},
        {"drop stream table stream1;"},
        {"drop stream table stream2;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel1);
    RdStreamFreeTableSchema(vertexLabel2);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 使用alter union添加schema相同的表，多个线程向表中写入数据
TEST_F(t01_alter_union, STREAM_035_038)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer, ref_key integer, "
         "description text) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');",
            GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text);"},
        {"create stream table stream2 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text);"},
        {"create stream view view1 AS select id, name, water_mark, event_time, ref_key, description FROM stream1;"},
        {"create stream sink sink1 AS select id, name, water_mark, event_time, ref_key, description "
         "FROM view1 into tsdb(ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    string alterCmd = "alter stream view view1 alter from_union add stream2;";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel1 = RdStreamParseTableSchema(creates[2].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel1);

    uint32_t threadNum = 3;
    uint32_t rowNum = 5;
    uint32_t colNum = 6;
    pthread_t threads[threadNum] = {0};
    RdStreamStructWriteCtxT writeCtx[threadNum] = {0};
    for (uint32_t i = 0; i < threadNum; ++i) {
        writeCtx[i] = {
            .vertexLabel = vertexLabel1,
            .status = GMERR_OK,
            .rowStart = 1,
            .rowNum = rowNum,
            .colNum = colNum,
        };
        ret = pthread_create(&threads[i], NULL, RdStreamStructWriteWorker, &writeCtx[i]);
        ASSERT_EQ(0, ret);
    }

    for (uint32_t i = 0; i < threadNum; i++) {
        ret = pthread_join(threads[i], NULL);
        ASSERT_EQ(0, ret);
    }

    // 结构化写入数据
    char desText[][128] = {"test data: 1", "test data: 12", "test data: 123", "test data: 1234", "test data: 12345"};
    char *description[rowNum] = {0};
    uint16_t descriptionLen[rowNum] = {0};
    for (int i = 0; i < rowNum; ++i) {
        description[i] = desText[i];
        descriptionLen[i] = strlen(desText[i]) + 1;
    }

    // 查询ts表中数据
    char *qryCmd = (char *)"select * from ts1 order by id;";
    uint32_t expRowNum = 15;
    uint32_t expColNum = 6;
    int64_t exp_id[expRowNum] = {1, 1, 1, 2, 2, 2, 3, 3, 3, 4, 4, 4, 5, 5, 5};
    int64_t exp_water_mark[expRowNum] = {1, 1, 1, 2, 2, 2, 3, 3, 3, 4, 4, 4, 5, 5, 5};
    int64_t exp_event_time[expRowNum] = {1, 1, 1, 2, 2, 2, 3, 3, 3, 4, 4, 4, 5, 5, 5};
    int64_t exp_ref_key[expRowNum] = {1, 1, 1, 2, 2, 2, 3, 3, 3, 4, 4, 4, 5, 5, 5};
    char *exp_description[expRowNum] = {desText[0], desText[0], desText[0], desText[1], desText[1], desText[1],
        desText[2], desText[2], desText[2], desText[3], desText[3], desText[3], desText[4], desText[4], desText[4]};
    uint16_t exp_descriptionLen[expRowNum] = {
        descriptionLen[0],
        descriptionLen[0],
        descriptionLen[0],
        descriptionLen[1],
        descriptionLen[1],
        descriptionLen[1],
        descriptionLen[2],
        descriptionLen[2],
        descriptionLen[2],
        descriptionLen[3],
        descriptionLen[3],
        descriptionLen[3],
        descriptionLen[4],
        descriptionLen[4],
        descriptionLen[4],
    };
    RdCheckDataInTSTableOfStreamTable(stmt, qryCmd, expRowNum, expColNum, exp_id, exp_water_mark, exp_event_time,
        exp_ref_key, exp_descriptionLen, exp_description);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop stream table stream2;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel1);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// alter union 在view/sink中添加不存在的节点
TEST_F(t01_alter_union, STREAM_035_039)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer, mini_ref_key integer, "
         "description text) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');",
            GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer, mini_ref_key "
         "integer, description text);"},
        {"create stream view view1 AS select * FROM stream1;"},
        {"create stream sink sink1 AS select * from view1 into tsdb(ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    string alterCmd = "alter stream view view1 alter from_union add stream2;";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    ASSERT_EQ(GMERR_UNDEFINED_TABLE, ret);

    alterCmd = "alter stream sink sink1 alter from_union add view2;";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    ASSERT_EQ(GMERR_UNDEFINED_TABLE, ret);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// alter union 在view/sink中删除不存在的节点
TEST_F(t01_alter_union, STREAM_035_040)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer, mini_ref_key integer, "
         "description text) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');",
            GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer, mini_ref_key "
         "integer, description text);"},
        {"create stream table stream2 (id integer, name char(50), water_mark integer, event_time integer, mini_ref_key "
         "integer, description text);"},
        {"create stream table stream3 (id integer, name char(50), water_mark integer, event_time integer, mini_ref_key "
         "integer, description text);"},
        {"create stream view view1 AS select * FROM TABLE(UNION(TABLE stream1, TABLE stream2));"},
        {"create stream sink sink1 AS select * FROM view1 into tsdb(ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    string alterCmd = "alter stream view view1 alter from_union drop stream3;";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    ASSERT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop stream table stream2;"},
        {"drop stream table stream3;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// alter union 在view/sink中添加schema不相同的节点
TEST_F(t01_alter_union, STREAM_035_041)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer, mini_ref_key integer, "
         "description text) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');",
            GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer, mini_ref_key "
         "integer, description text);"},
        {"create stream table stream2 (id integer, name char(50), water_mark integer, event_time integer, mini_ref_key "
         "integer, description text);"},
        {"create stream table stream3 (id integer, name char(51), water_mark integer, event_time integer, mini_ref_key "
         "integer, description text);"},
        {"create stream view view1 AS select * FROM TABLE(UNION(TABLE stream1, TABLE stream2));"},
        {"create stream sink sink1 AS select * FROM view1 into tsdb(ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    string alterCmd = "alter stream view view1 alter from_union add stream3;";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    ASSERT_EQ(GMERR_DATATYPE_MISMATCH, ret);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop stream table stream2;"},
        {"drop stream table stream3;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// alter union删除view/sink中的最后一个节点
TEST_F(t01_alter_union, STREAM_035_042)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer, mini_ref_key integer, "
         "description text) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');",
            GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer, mini_ref_key "
         "integer, description text);"},
        {"create stream table stream2 (id integer, name char(50), water_mark integer, event_time integer, mini_ref_key "
         "integer, description text);"},
        {"create stream view view1 AS select * FROM TABLE(UNION(TABLE stream1, TABLE stream2));"},
        {"create stream sink sink1 AS select * FROM view1 into tsdb(ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    string alterCmd = "alter stream view view1 alter from_union drop stream2;";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    ASSERT_EQ(GMERR_OK, ret);

    alterCmd = "alter stream view view1 alter from_union drop stream1;";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop stream table stream2;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// stream/view有最大数量（32）个后继节点，使用alter union将其添加到第33个后继节点
TEST_F(t01_alter_union, STREAM_035_043)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer, mini_ref_key integer, "
         "description text) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');",
            GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer, mini_ref_key "
         "integer, description text);"},
        {"create stream table stream2 (id integer, name char(50), water_mark integer, event_time integer, mini_ref_key "
         "integer, description text);"},
        {"create stream view view32 AS select * FROM stream2;"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 32; ++i) {
        char temp[128] = {0};
        sprintf_s(temp, 128, "create stream view view%03d AS select * FROM stream1;", i);
        ret = GmcExecDirect(stmt, temp, strlen(temp));
        ASSERT_EQ(GMERR_OK, ret);
    }

    string alterCmd = "alter stream view view32 alter from_union add stream1;";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    ASSERT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);

    // 释放资源
    for (uint32_t i = 0; i < 32; ++i) {
        char temp[128] = {0};
        sprintf_s(temp, 128, "drop stream view view%03d;", i);
        ret = GmcExecDirect(stmt, temp, strlen(temp));
        ASSERT_EQ(GMERR_OK, ret);
    }
    RdStreamExecDescT drops[] = {
        {"drop stream view view32"},
        {"drop stream table stream1;"},
        {"drop stream table stream2;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// alter union向view/sink添加相同的dispatch参数（表名和列）
TEST_F(t01_alter_union, STREAM_035_044)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer, ref_key integer, "
         "description text) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');",
            GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text) dispatch by id, event_time;"},
        {"create stream table stream2 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text) dispatch by id, event_time;"},
        {"create stream view view1 AS select id, name, water_mark, event_time, ref_key, description "
         "FROM TABLE(UNION(TABLE(dispatch(table stream1, 1, 21)), TABLE(dispatch(table stream2, 1, 21))));"},
        {"create stream sink sink1 AS select * from view1 into tsdb(ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    string alterCmd = "alter stream view view1 alter from_union add TABLE(DISPATCH(table stream2, 1, 21));";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    ASSERT_EQ(GMERR_DUPLICATE_TABLE, ret);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream2;"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// union table1后，alter union添加dispatch(table1, 1...)
TEST_F(t01_alter_union, STREAM_035_045)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer, ref_key integer, "
         "description text) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');",
            GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text) dispatch by id, event_time;"},
        {"create stream table stream2 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text) dispatch by id, event_time;"},
        {"create stream view view1 AS select id, name, water_mark, event_time, ref_key, description "
         "FROM TABLE(UNION(TABLE stream1, TABLE stream2));"},
        {"create stream sink sink1 AS select * from view1 into tsdb(ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    string alterCmd = "alter stream view view1 alter from_union add TABLE(DISPATCH(table stream2, 1, 21));";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    ASSERT_EQ(GMERR_DATATYPE_MISMATCH, ret);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream2;"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// dispatch(table1, 1...)后，alter union添加union table1
TEST_F(t01_alter_union, STREAM_035_046)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer, ref_key integer, "
         "description text) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');",
            GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text) dispatch by id, event_time;"},
        {"create stream table stream2 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text) dispatch by id, event_time;"},
        {"create stream view view1 AS select id, name, water_mark, event_time, ref_key, description "
         "FROM TABLE(UNION(TABLE(dispatch(table stream1, 1, 21)), TABLE(dispatch(table stream2, 1, 21))));"},
        {"create stream sink sink1 AS select * from view1 into tsdb(ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    string alterCmd = "alter stream view view1 alter from_union add stream2;";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    ASSERT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream2;"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 在sink中alter union添加同一stream的不同view
TEST_F(t01_alter_union, STREAM_035_047)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer, ref_key integer, "
         "description text) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');",
            GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text);"},
        {"create stream view view1 AS select id, name, water_mark, event_time, ref_key, description FROM stream1;"},
        {"create stream view view2 AS select id, name, water_mark, ref_key, description FROM stream1;"},
        {"create stream sink sink1 AS select * from view1 into tsdb(ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    string alterCmd = "alter stream sink sink1 alter from_union add view2;";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    ASSERT_EQ(GMERR_DATATYPE_MISMATCH, ret);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream view view2"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// alter union添加一个列名相同，类型不同的流表
TEST_F(t01_alter_union, STREAM_035_048)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer, ref_key integer, "
         "description text) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');",
            GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text);"},
        {"create stream table stream2 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description char(50));"},
        {"create stream view view1 AS select * FROM stream1;"},
        {"create stream sink sink1 AS select * from view1 into tsdb(ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    string alterCmd = "alter stream view view1 alter from_union add stream2;";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    ASSERT_EQ(GMERR_DATATYPE_MISMATCH, ret);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop stream table stream2;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// alter union添加一个char长度不同，其他相同的流表
TEST_F(t01_alter_union, STREAM_035_049)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer, ref_key integer, "
         "description text) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');",
            GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text);"},
        {"create stream table stream2 (id integer, name char(51), water_mark integer, event_time integer, ref_key "
         "integer, description text);"},
        {"create stream view view1 AS select * FROM stream1;"},
        {"create stream sink sink1 AS select * from view1 into tsdb(ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    string alterCmd = "alter stream view view1 alter from_union add stream2;";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    ASSERT_EQ(GMERR_DATATYPE_MISMATCH, ret);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop stream table stream2;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// alter union添加一个列名和类型相同，顺序不同的流表
TEST_F(t01_alter_union, STREAM_035_050)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer, ref_key integer, "
         "description text) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');",
            GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text);"},
        {"create stream table stream2 (id integer, name char(50), event_time integer, water_mark integer, ref_key "
         "integer, description text);"},
        {"create stream view view1 AS select * FROM stream1;"},
        {"create stream sink sink1 AS select * from view1 into tsdb(ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    string alterCmd = "alter stream view view1 alter from_union add stream2;";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    ASSERT_EQ(GMERR_DATATYPE_MISMATCH, ret);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop stream table stream2;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// stream1-view1-sink1-ts1，stream2-view2-sink1-ts1建好写入数据，切断view2-sink1，写入数据（ts1查不到），恢复view2-sink1，写入数据（ts1表能查到）
TEST_F(t01_alter_union, STREAM_035_051)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer, ref_key integer, "
         "description text) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');",
            GMC_MODEL_TS},
        {"create table ts2 (id integer, name char(50), water_mark integer, event_time integer, ref_key integer, "
         "description text) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');",
            GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text);"},
        {"create stream table stream2 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text);"},
        {"create stream view view1 AS select * FROM stream1;"},
        {"create stream view view2 AS select * FROM stream2;"},
        {"create stream sink sink1 AS select * from view1 into tsdb(ts1) with (batch_window_size = 1);"},
        {"create stream sink sink2 AS select * from view2 into tsdb(ts2) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    string alterCmd = "alter stream sink sink1 alter from_union add view2;";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel1 = RdStreamParseTableSchema(creates[2].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel1);
    RdVertexLabelT *vertexLabel2 = RdStreamParseTableSchema(creates[3].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel2);

    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 6;
    int64_t id[rowNum] = {1, 2, 3, 4, 5};
    int64_t water_mark[rowNum] = {11, 12, 13, 14, 15};
    int64_t event_time[rowNum] = {21, 22, 23, 24, 25};
    int64_t ref_key[rowNum] = {1, 1, 1, 1, 1};
    char desText[][128] = {"test data: 1", "test data: 12", "test data: 123", "test data: 1234", "test data: 12345"};
    char *description[rowNum] = {0};
    uint16_t descriptionLen[rowNum] = {0};
    for (int i = 0; i < rowNum; ++i) {
        description[i] = desText[i];
        descriptionLen[i] = strlen(desText[i]) + 1;
    }
    bool nullInfo[colNum] = {false, false, false, false, false, false};
    RdStructWriteStreamTable(
        stmt, vertexLabel1, rowNum, id, water_mark, event_time, ref_key, descriptionLen, description, nullInfo);

    int64_t id2[rowNum] = {6, 7, 8, 9, 10};
    int64_t water_mark2[rowNum] = {16, 17, 18, 19, 20};
    int64_t event_time2[rowNum] = {26, 27, 28, 29, 30};
    int64_t ref_key2[rowNum] = {2, 2, 2, 2, 2};
    RdStructWriteStreamTable(
        stmt, vertexLabel2, rowNum, id2, water_mark2, event_time2, ref_key2, descriptionLen, description, nullInfo);

    // 查询ts1表中数据
    char *qryCmd = (char *)"select * from ts1 order by id;";
    uint32_t expRowNum = 10;
    uint32_t expColNum = 6;
    int64_t exp_id[expRowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    int64_t exp_water_mark[expRowNum] = {11, 12, 13, 14, 15, 16, 17, 18, 19, 20};
    int64_t exp_event_time[expRowNum] = {21, 22, 23, 24, 25, 26, 27, 28, 29, 30};
    int64_t exp_ref_key[expRowNum] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2};
    char *exp_description[expRowNum] = {desText[0], desText[1], desText[2], desText[3], desText[4], desText[0],
        desText[1], desText[2], desText[3], desText[4]};
    uint16_t exp_descriptionLen[expRowNum] = {descriptionLen[0], descriptionLen[1], descriptionLen[2],
        descriptionLen[3], descriptionLen[4], descriptionLen[0], descriptionLen[1], descriptionLen[2],
        descriptionLen[3], descriptionLen[4]};
    RdCheckDataInTSTableOfStreamTable(stmt, qryCmd, expRowNum, expColNum, exp_id, exp_water_mark, exp_event_time,
        exp_ref_key, exp_descriptionLen, exp_description);

    // 切断view2-sink1
    alterCmd = "alter stream sink sink1 alter from_union drop view2;";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    ASSERT_EQ(GMERR_OK, ret);

    // 写入数据
    int64_t id3[rowNum] = {11, 12, 13, 14, 15};
    int64_t water_mark3[rowNum] = {21, 22, 23, 24, 25};
    int64_t event_time3[rowNum] = {31, 32, 33, 34, 35};
    int64_t ref_key3[rowNum] = {3, 3, 3, 3, 3};
    RdStructWriteStreamTable(
        stmt, vertexLabel2, rowNum, id3, water_mark3, event_time3, ref_key3, descriptionLen, description, nullInfo);

    // 查询ts1表中数据，查不到新数据
    RdCheckDataInTSTableOfStreamTable(stmt, qryCmd, expRowNum, expColNum, exp_id, exp_water_mark, exp_event_time,
        exp_ref_key, exp_descriptionLen, exp_description);

    // 恢复view2-sink1
    alterCmd = "alter stream sink sink1 alter from_union add view2;";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    ASSERT_EQ(GMERR_OK, ret);

    // 写入数据
    RdStructWriteStreamTable(
        stmt, vertexLabel2, rowNum, id3, water_mark3, event_time3, ref_key3, descriptionLen, description, nullInfo);

    // 查询ts1表中数据
    expRowNum = 15;
    expColNum = 6;
    int64_t exp_id2[expRowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15};
    int64_t exp_water_mark2[expRowNum] = {11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25};
    int64_t exp_event_time2[expRowNum] = {21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35};
    int64_t exp_ref_key2[expRowNum] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3};
    char *exp_description2[expRowNum] = {desText[0], desText[1], desText[2], desText[3], desText[4], desText[0],
        desText[1], desText[2], desText[3], desText[4], desText[0], desText[1], desText[2], desText[3], desText[4]};
    uint16_t exp_descriptionLen2[expRowNum] = {descriptionLen[0], descriptionLen[1], descriptionLen[2],
        descriptionLen[3], descriptionLen[4], descriptionLen[0], descriptionLen[1], descriptionLen[2],
        descriptionLen[3], descriptionLen[4], descriptionLen[0], descriptionLen[1], descriptionLen[2],
        descriptionLen[3], descriptionLen[4]};
    RdCheckDataInTSTableOfStreamTable(stmt, qryCmd, expRowNum, expColNum, exp_id2, exp_water_mark2, exp_event_time2,
        exp_ref_key2, exp_descriptionLen2, exp_description2);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream sink sink2;"},
        {"drop stream view view1"},
        {"drop stream view view2"},
        {"drop stream table stream1;"},
        {"drop stream table stream2;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel1);
    RdStreamFreeTableSchema(vertexLabel2);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// view成环场景
TEST_F(t01_alter_union, STREAM_035_052)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer, ref_key integer, "
         "description text) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');",
            GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text);"},
        {"create stream view view1 AS select * FROM stream1;"},
        {"create stream view view2 AS select * FROM view1;"},
        {"create stream view view3 AS select * FROM view2;"},
        {"create stream sink sink1 AS select * from view3 into tsdb(ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    string alterCmd = "alter stream view view1 alter from_union add view3;";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    ASSERT_EQ(GMERR_SEMANTIC_ERROR, ret);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view3"},
        {"drop stream view view2"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// source作为后继节点，sink作为前驱节点
TEST_F(t01_alter_union, STREAM_035_053)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer, ref_key integer, "
         "description text) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');",
            GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text);"},
        {"create stream view view1 AS select * FROM stream1;"},
        {"create stream sink sink1 AS select * from view1 into tsdb(ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    string alterCmd = "alter stream stream stream1 alter from_union add sink1;";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    ASSERT_EQ(GMERR_SYNTAX_ERROR, ret);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 深度超过最大128
TEST_F(t01_alter_union, STREAM_035_054)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer, ref_key integer, "
         "description text) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');",
            GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text);"},
        {"create stream table stream2 (id integer, name char(50), water_mark integer, event_time integer, ref_key "
         "integer, description text);"},
        {"create stream view view001 AS select * FROM stream1;"},
        {"create stream view tbl2_view1 AS select * FROM stream2;"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t deep = 126;
    for (uint32_t i = 1; i < deep; ++i) {
        char temp[128] = {0};
        sprintf_s(temp, 128, "create stream view view%03d AS select * FROM view%03d;", i + 1, i);
        ret = GmcExecDirect(stmt, temp, strlen(temp));
        ASSERT_EQ(GMERR_OK, ret);
    }

    string alterCmd = "alter stream view view001 alter from_union add tbl2_view1;";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    ASSERT_EQ(GMERR_SEMANTIC_ERROR, ret);

    // 释放资源
    for (uint32_t i = deep; i > 1; --i) {
        char temp[128] = {0};
        sprintf_s(temp, 128, "drop stream view view%03d;", i);
        ret = GmcExecDirect(stmt, temp, strlen(temp));
        ASSERT_EQ(GMERR_OK, ret);
    }
    RdStreamExecDescT drops[] = {
        {"drop stream view view001"},
        {"drop stream view tbl2_view1"},
        {"drop stream table stream1;"},
        {"drop stream table stream2;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// alter一个不存在的view
TEST_F(t01_alter_union, STREAM_035_055)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer, ref_key integer, "
         "description text) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');"},
        {"create stream table t1(id integer, name char(256), sex char(50), age integer, job_type char(50));"},
        {"CREATE STREAM VIEW v1 AS select id as new_id, name as new_name, sex, age as new_age, job_type from t1 "
         "where age > 30 and id <= 100 or sex = 'boy';"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    string alterCmd = "alter stream view v2 alter where as new_age > 30 and new_id <= 100 or sex = 'girl';";
    ret = GmcExecDirect(stmt, alterCmd.c_str(), alterCmd.length());
    ASSERT_EQ(GMERR_UNDEFINED_TABLE, ret);

    RdStreamExecDescT drops[] = {
        {"drop stream view v1"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
