/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description:嵌入式环境下流计算支持批量结构化写异常用例
 * Author: moxiaotong
 * Create: 2025-07-02
 */
#include "gtest/gtest.h"
#include "text_util.h"

class embedded_batch_write_interact : public testing::Test {
public:
    static void SetUpTestCase();
    static void TearDownTestCase();
    virtual void SetUp();
    virtual void TearDown();
};

void embedded_batch_write_interact::SetUpTestCase()
{
    int32_t ret;
    char sysGMDBCfg[MAX_CMD_SIZE];
    system("stop.sh -f");
    (void)snprintf(sysGMDBCfg, MAX_CMD_SIZE, "%s/gmserver.ini", g_sysGMDBCfgPath);
    ret = GmeOpen(sysGMDBCfg, GME_OPEN_CREATE, &conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void embedded_batch_write_interact::TearDownTestCase()
{
    int32_t ret;
    ret = GmeClose(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void embedded_batch_write_interact::SetUp()
{
    g_batchcount = 0;
    g_datacount = 0;
}

void embedded_batch_write_interact::TearDown()
{}

// 035后继结点为包含四则运算的sink/view，批量结构化写入数据到该表
// 	true
TEST_F(embedded_batch_write_interact, STREAM_044_035)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char *errMsg = NULL;
    ret = GmeSqlExecute(
        conn, "create stream table t1(id integer, time integer, age real, name char(50));", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(
        conn, "create stream view v1 as select id % 2, time, age + 3.35, name from t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM sink s1 AS select * FROM v1 INTO embedded_callback with "
        "(batch_window_size = '50');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *streamName = "t1";
    const char *sinkName = "s1";
    ret = GmeStreamRegisterCallBack(conn, sinkName, StEmbStreamGetActQryResultGet8);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t rowNum = 50;
    int64_t batchNum = 1;
    int64_t *id = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        id[i] = i + 1;
    }
    int64_t *time = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        time[i] = i + 10;
    }
    double *age = (double *)malloc(rowNum * sizeof(double));
    for (int64_t i = 0; i < rowNum; i++) {
        age[i] = i + 0.4;
    }
    if (id != NULL && time != NULL && age != NULL) {
        RdBatchWriteStreamTable(streamName, rowNum, id, time, age);
    }
    AW_MACRO_EXPECT_EQ_INT(batchNum, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(batchNum * rowNum, g_datacount);
    ret = GmeSqlExecute(conn, "drop stream sink s1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream view v1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream table t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(id);
    free(time);
    free(age);
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}

// 036后继结点为包含ref的sink/view，批量结构化写入数据到该表
// 	true
TEST_F(embedded_batch_write_interact, STREAM_044_036)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char *errMsg = NULL;
    ret = GmeSqlExecute(
        conn, "create stream table t1(id integer, time integer, age real, name char(50));", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "CREATE STREAM reference ref1 (integer, integer);", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(
        conn, "create stream view v1 as select REF['ref1'][id], time, age, name from t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM sink s1 AS select * FROM v1 INTO embedded_callback with "
        "(batch_window_size = '50');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *streamName = "t1";
    const char *sinkName = "s1";
    ret = GmeStreamRegisterCallBack(conn, sinkName, StEmbStreamGetActQryResultGet4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t rowNum = 50;
    int64_t batchNum = 1;
    int64_t *id = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        id[i] = i + 1;
    }
    int64_t *time = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        time[i] = i + 10;
    }
    double *age = (double *)malloc(rowNum * sizeof(double));
    for (int64_t i = 0; i < rowNum; i++) {
        age[i] = i + 0.4;
    }
    for (int64_t i = 0; i < rowNum; i++) {
        char insertRefSql[1024];
        sprintf(insertRefSql, "upsert into streamref ref1 values (%d, %d);", i + 1, i + 1);
        uint32_t cmdLen = strlen(insertRefSql);
        ret = GmeSqlExecute(conn, insertRefSql, NULL, NULL, &errMsg);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    if (id != NULL && time != NULL && age != NULL) {
        RdBatchWriteStreamTable(streamName, rowNum, id, time, age);
    }
    AW_MACRO_EXPECT_EQ_INT(batchNum, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(batchNum * rowNum, g_datacount);
    ret = GmeSqlExecute(conn, "drop stream sink s1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream view v1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream reference ref1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream table t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(id);
    free(time);
    free(age);
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}

// 037后继结点为包含mini ref的sink/view，批量结构化写入数据到该表
// 	true
TEST_F(embedded_batch_write_interact, STREAM_044_037)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char *errMsg = NULL;
    ret = GmeSqlExecute(
        conn, "create stream table t1(id integer, time integer, age real, name char(50));", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(
        conn, "CREATE STREAM reference ref1 (integer, integer) with (mini = 'true');", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(
        conn, "create stream view v1 as select REF['ref1'][id], time, age, name from t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM sink s1 AS select * FROM v1 INTO embedded_callback with "
        "(batch_window_size = '50');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *streamName = "t1";
    const char *sinkName = "s1";
    ret = GmeStreamRegisterCallBack(conn, sinkName, StEmbStreamGetActQryResultGet4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t rowNum = 50;
    int64_t batchNum = 1;
    int64_t *id = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        id[i] = i + 1;
    }
    int64_t *time = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        time[i] = i + 10;
    }
    double *age = (double *)malloc(rowNum * sizeof(double));
    for (int64_t i = 0; i < rowNum; i++) {
        age[i] = i + 0.4;
    }
    for (int64_t i = 0; i < rowNum; i++) {
        char insertRefSql[1024];
        sprintf(insertRefSql, "upsert into streamref ref1 values (%d, %d);", i + 1, i + 1);
        uint32_t cmdLen = strlen(insertRefSql);
        ret = GmeSqlExecute(conn, insertRefSql, NULL, NULL, &errMsg);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    if (id != NULL && time != NULL && age != NULL) {
        RdBatchWriteStreamTable(streamName, rowNum, id, time, age);
    }
    AW_MACRO_EXPECT_EQ_INT(batchNum, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(rowNum * batchNum, g_datacount);

    ret = GmeSqlExecute(conn, "drop stream sink s1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream view v1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream reference ref1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream table t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(id);
    free(time);
    free(age);
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}

// 038后继结点为包含投影去重函数的sink/view，批量结构化写入数据到该表
// 	true
TEST_F(embedded_batch_write_interact, STREAM_044_038)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char *errMsg = NULL;
    ret = GmeSqlExecute(
        conn, "create stream table t1(id integer, time integer, age real, name char(50));", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(
        conn, "create stream view v1 as select id, seq_distinct_count(time), age, name from t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM sink s1 AS select * FROM v1 INTO embedded_callback with "
        "(batch_window_size = '1');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *streamName = "t1";
    const char *sinkName = "s1";
    ret = GmeStreamRegisterCallBack(conn, sinkName, StEmbStreamGetActQryResultGet9);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t rowNum = 30;
    int64_t batchNum = rowNum - 1;
    int64_t *id = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        id[i] = i + 1;
    }
    int64_t *time = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        time[i] = i + 10;
        // time[i] = 5;
    }
    double *age = (double *)malloc(rowNum * sizeof(double));
    for (int64_t i = 0; i < rowNum; i++) {
        age[i] = i + 0.4;
    }
    if (id != NULL && time != NULL && age != NULL) {
        RdBatchWriteStreamTable(streamName, rowNum, id, time, age);
    }
    AW_MACRO_EXPECT_EQ_INT(batchNum, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(batchNum, g_datacount);

    ret = GmeSqlExecute(conn, "drop stream sink s1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream view v1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream table t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(id);
    free(time);
    free(age);
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}

// 039后继结点为包含format格式化的sink/view，批量结构化写入数据到该表
// 	true
TEST_F(embedded_batch_write_interact, STREAM_044_039)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char *errMsg = NULL;
    ret = GmeSqlExecute(
        conn, "create stream table t1(id integer, time integer, age real, name char(50));", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "create stream view v1 as select id, time, age, format('this id is %d', id) from t1;",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM sink s1 AS select * FROM v1 INTO embedded_callback with "
        "(batch_window_size = '50');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *streamName = "t1";
    const char *sinkName = "s1";
    ret = GmeStreamRegisterCallBack(conn, sinkName, StEmbStreamGetActQryResultGet10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t rowNum = 50;
    int64_t batchNum = 1;
    int64_t *id = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        id[i] = i + 1;
    }
    int64_t *time = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        time[i] = i + 10;
    }
    double *age = (double *)malloc(rowNum * sizeof(double));
    for (int64_t i = 0; i < rowNum; i++) {
        age[i] = i + 0.4;
    }
    if (id != NULL && time != NULL && age != NULL) {
        RdBatchWriteStreamTable(streamName, rowNum, id, time, age);
    }
    AW_MACRO_EXPECT_EQ_INT(batchNum, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(rowNum * batchNum, g_datacount);
    ret = GmeSqlExecute(conn, "drop stream sink s1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream view v1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream table t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(id);
    free(time);
    free(age);
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}

// 040创建流表设置默认值，批量结构化写入数据到该表
// 	true
TEST_F(embedded_batch_write_interact, STREAM_044_040)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char *errMsg = NULL;
    ret = GmeSqlExecute(conn,
        "create stream table t1(id integer default 10, time integer, age real default 6.3, name char(50));", NULL, NULL,
        &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "create stream view v1 as select * from t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM sink s1 AS select * FROM v1 INTO embedded_callback with "
        "(batch_window_size = '50');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *streamName = "t1";
    const char *sinkName = "s1";
    ret = GmeStreamRegisterCallBack(conn, sinkName, StEmbStreamGetActQryResultGet4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t rowNum = 50;
    int64_t batchNum = 1;
    int64_t *id = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        id[i] = i + 1;
    }
    int64_t *time = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        time[i] = i + 10;
    }
    double *age = (double *)malloc(rowNum * sizeof(double));
    for (int64_t i = 0; i < rowNum; i++) {
        age[i] = i + 0.4;
    }
    if (id != NULL && time != NULL && age != NULL) {
        RdBatchWriteStreamTable(streamName, rowNum, id, time, age);
    }
    AW_MACRO_EXPECT_EQ_INT(batchNum, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(rowNum * batchNum, g_datacount);
    ret = GmeSqlExecute(conn, "drop stream sink s1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream view v1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream table t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(id);
    free(time);
    free(age);
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}

// 041创建流表，设置默认填充hostname，批量结构化写入数据到该表
// 	true
TEST_F(embedded_batch_write_interact, STREAM_044_041)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char *errMsg = NULL;
    ret = GmeSqlExecute(conn,
        "create stream table t1(id integer, time integer, age real, name char(50) DEFAULT get_hostname());", NULL, NULL,
        &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "create stream view v1 as select * from t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM sink s1 AS select * FROM v1 INTO embedded_callback with "
        "(batch_window_size = '50');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *streamName = "t1";
    const char *sinkName = "s1";
    ret = GmeStreamRegisterCallBack(conn, sinkName, StEmbStreamGetActQryResultGet4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t rowNum = 50;
    int64_t batchNum = 1;
    int64_t *id = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        id[i] = i + 1;
    }
    int64_t *time = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        time[i] = i + 10;
    }
    double *age = (double *)malloc(rowNum * sizeof(double));
    for (int64_t i = 0; i < rowNum; i++) {
        age[i] = i + 0.4;
    }
    if (id != NULL && time != NULL && age != NULL) {
        RdBatchWriteStreamTable(streamName, rowNum, id, time, age);
    }
    AW_MACRO_EXPECT_EQ_INT(batchNum, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(rowNum * batchNum, g_datacount);
    ret = GmeSqlExecute(conn, "drop stream sink s1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream view v1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream table t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(id);
    free(time);
    free(age);
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}

// 042创建流表，设置默认填充时间列，批量结构化写入数据到该表
// 	true
TEST_F(embedded_batch_write_interact, STREAM_044_042)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char *errMsg = NULL;
    ret = GmeSqlExecute(conn,
        "create stream table t1(id integer, time integer DEFAULT "
        "current_time_second(), age real, name char(50));",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "create stream view v1 as select * from t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM sink s1 AS select * FROM v1 INTO embedded_callback with "
        "(batch_window_size = '50');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *streamName = "t1";
    const char *sinkName = "s1";
    ret = GmeStreamRegisterCallBack(conn, sinkName, StEmbStreamGetActQryResultGet4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t rowNum = 50;
    int64_t batchNum = 1;
    int64_t *id = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        id[i] = i + 1;
    }
    int64_t *time = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        time[i] = i + 10;
    }
    double *age = (double *)malloc(rowNum * sizeof(double));
    for (int64_t i = 0; i < rowNum; i++) {
        age[i] = i + 0.4;
    }
    if (id != NULL && time != NULL && age != NULL) {
        RdBatchWriteStreamTable(streamName, rowNum, id, time, age);
    }
    AW_MACRO_EXPECT_EQ_INT(batchNum, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(rowNum * batchNum, g_datacount);
    ret = GmeSqlExecute(conn, "drop stream sink s1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream view v1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream table t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(id);
    free(time);
    free(age);
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}

// 043后继结点为使用in/not in 的左表达式的sink/view，批量结构化写入数据到该表
// 	true
TEST_F(embedded_batch_write_interact, STREAM_044_043)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char *errMsg = NULL;
    ret = GmeSqlExecute(
        conn, "create stream table t1(id integer, time integer, age real, name char(50));", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret =
        GmeSqlExecute(conn, "create stream view v1 as select * from t1 where id in (1, 2, 3, 4);", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM sink s1 AS select * FROM v1 INTO embedded_callback with "
        "(batch_window_size = '4');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *streamName = "t1";
    const char *sinkName = "s1";
    ret = GmeStreamRegisterCallBack(conn, sinkName, StEmbStreamGetActQryResultGet4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t rowNum = 50;
    int64_t batchNum = 1;
    int64_t *id = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        id[i] = i + 1;
    }
    int64_t *time = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        time[i] = i + 10;
    }
    double *age = (double *)malloc(rowNum * sizeof(double));
    for (int64_t i = 0; i < rowNum; i++) {
        age[i] = i + 0.4;
    }
    if (id != NULL && time != NULL && age != NULL) {
        RdBatchWriteStreamTable(streamName, rowNum, id, time, age);
    }
    AW_MACRO_EXPECT_EQ_INT(batchNum, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(4, g_datacount);
    ret = GmeSqlExecute(conn, "drop stream sink s1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream view v1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream table t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(id);
    free(time);
    free(age);
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}

// 044创建流表，后继结点包含union的sink/view，批量结构化写入数据到该表
// 	true
TEST_F(embedded_batch_write_interact, STREAM_044_044)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char *errMsg = NULL;
    ret = GmeSqlExecute(
        conn, "create stream table t1(id integer, time integer, age real, name char(50));", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "create stream view v1 as select * from t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "create stream view v2 as select * from t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM sink s1 AS select * FROM TABLE(UNION(TABLE v1, TABLE v2)) INTO embedded_callback with "
        "(batch_window_size = '50');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *streamName = "t1";
    const char *sinkName = "s1";
    ret = GmeStreamRegisterCallBack(conn, sinkName, StEmbStreamGetActQryResultGet4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t rowNum = 50;
    int64_t batchNum = 2;
    int64_t *id = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        id[i] = i + 1;
    }
    int64_t *time = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        time[i] = i + 10;
    }
    double *age = (double *)malloc(rowNum * sizeof(double));
    for (int64_t i = 0; i < rowNum; i++) {
        age[i] = i + 0.4;
    }
    if (id != NULL && time != NULL && age != NULL) {
        RdBatchWriteStreamTable(streamName, rowNum, id, time, age);
    }
    AW_MACRO_EXPECT_EQ_INT(batchNum, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(rowNum * batchNum, g_datacount);
    ret = GmeSqlExecute(conn, "drop stream sink s1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream view v1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream view v2;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream table t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(id);
    free(time);
    free(age);
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}

// 045创建流表，alter union一个包含union的的sink/view，批量结构化写入数据到该表
// 	true
TEST_F(embedded_batch_write_interact, STREAM_044_045)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char *errMsg = NULL;
    ret = GmeSqlExecute(
        conn, "create stream table t1(id integer, time integer, age real, name char(50));", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "create stream view v1 as select * from t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "create stream view v2 as select * from t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM sink s1 AS select * FROM TABLE(UNION(TABLE v1, TABLE v2)) INTO embedded_callback with "
        "(batch_window_size = '50');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "alter stream sink s1 alter from_union drop v2;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *streamName = "t1";
    const char *sinkName = "s1";
    ret = GmeStreamRegisterCallBack(conn, sinkName, StEmbStreamGetActQryResultGet4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t rowNum = 50;
    int64_t batchNum = 1;
    int64_t *id = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        id[i] = i + 1;
    }
    int64_t *time = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        time[i] = i + 10;
    }
    double *age = (double *)malloc(rowNum * sizeof(double));
    for (int64_t i = 0; i < rowNum; i++) {
        age[i] = i + 0.4;
    }
    if (id != NULL && time != NULL && age != NULL) {
        RdBatchWriteStreamTable(streamName, rowNum, id, time, age);
    }
    AW_MACRO_EXPECT_EQ_INT(batchNum, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(rowNum * batchNum, g_datacount);
    ret = GmeSqlExecute(conn, "drop stream sink s1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream view v1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream view v2;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream table t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(id);
    free(time);
    free(age);
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}

// 046流表带dispatch by，批量结构化写入数据到该表
// 	true
TEST_F(embedded_batch_write_interact, STREAM_044_046)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char *errMsg = NULL;
    ret = GmeSqlExecute(conn,
        "create stream table t1(id integer, time integer, age real, name char(50)) dispatch by time;", NULL, NULL,
        &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "create stream view v1 as select * from table(dispatch(table t1, "
        "10));",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM sink s1 AS select * FROM v1 INTO embedded_callback with "
        "(batch_window_size = '1');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *streamName = "t1";
    const char *sinkName = "s1";
    ret = GmeStreamRegisterCallBack(conn, sinkName, StEmbStreamGetActQryResultGet4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t rowNum = 50;
    int64_t batchNum = 1;
    int64_t *id = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        id[i] = i + 1;
    }
    int64_t *time = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        time[i] = i + 10;
    }
    double *age = (double *)malloc(rowNum * sizeof(double));
    for (int64_t i = 0; i < rowNum; i++) {
        age[i] = i + 0.4;
    }
    if (id != NULL && time != NULL && age != NULL) {
        RdBatchWriteStreamTable(streamName, rowNum, id, time, age);
    }
    AW_MACRO_EXPECT_EQ_INT(batchNum, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(1, g_datacount);
    ret = GmeSqlExecute(conn, "drop stream sink s1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream view v1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream table t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(id);
    free(time);
    free(age);
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}

// 047后继结点为包含dispatch by的sink/view，批量结构化写入数据到该表
// 	true
TEST_F(embedded_batch_write_interact, STREAM_044_047)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char *errMsg = NULL;
    ret = GmeSqlExecute(
        conn, "create stream table t1(id integer, time integer, age real, name char(50));", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "create stream view v1 as select * from t1 dispatch by id;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM sink s1 AS select * FROM table(dispatch(table v1, "
        "10)) INTO embedded_callback with "
        "(batch_window_size = '1');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *streamName = "t1";
    const char *sinkName = "s1";
    ret = GmeStreamRegisterCallBack(conn, sinkName, StEmbStreamGetActQryResultGet11);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t rowNum = 50;
    int64_t batchNum = 1;
    int64_t *id = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        id[i] = i + 1;
    }
    int64_t *time = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        time[i] = i + 10;
    }
    double *age = (double *)malloc(rowNum * sizeof(double));
    for (int64_t i = 0; i < rowNum; i++) {
        age[i] = i + 0.4;
    }
    if (id != NULL && time != NULL && age != NULL) {
        RdBatchWriteStreamTable(streamName, rowNum, id, time, age);
    }
    AW_MACRO_EXPECT_EQ_INT(batchNum, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(1, g_datacount);
    ret = GmeSqlExecute(conn, "drop stream sink s1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream view v1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream table t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(id);
    free(time);
    free(age);
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}

// 048后继结点为包含alter where的sink/view，批量结构化写入数据到该表
// 	true
TEST_F(embedded_batch_write_interact, STREAM_044_048)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char *errMsg = NULL;
    ret = GmeSqlExecute(
        conn, "create stream table t1(id integer, time integer, age real, name char(50));", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "create stream view v1 as select * from t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM sink s1 AS select * FROM v1 INTO embedded_callback with "
        "(batch_window_size = '1');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "alter stream view v1 alter where as id < 10;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *streamName = "t1";
    const char *sinkName = "s1";
    ret = GmeStreamRegisterCallBack(conn, sinkName, StEmbStreamGetActQryResultGet5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t rowNum = 50;
    int64_t batchNum = 9;
    int64_t *id = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        id[i] = i + 1;
    }
    int64_t *time = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        time[i] = i + 10;
    }
    double *age = (double *)malloc(rowNum * sizeof(double));
    for (int64_t i = 0; i < rowNum; i++) {
        age[i] = i + 0.4;
    }
    if (id != NULL && time != NULL && age != NULL) {
        RdBatchWriteStreamTable(streamName, rowNum, id, time, age);
    }
    AW_MACRO_EXPECT_EQ_INT(batchNum, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(batchNum, g_datacount);
    ret = GmeSqlExecute(conn, "drop stream sink s1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream view v1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream table t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(id);
    free(time);
    free(age);
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}

// 049后继结点为包含比较运算符左表达式（< > != ≥ ≤）的sink/view，批量结构化写入数据到该表
// 	true
TEST_F(embedded_batch_write_interact, STREAM_044_049)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char *errMsg = NULL;
    ret = GmeSqlExecute(
        conn, "create stream table t1(id integer, time integer, age real, name char(50));", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "create stream view v1 as select * from t1 where id != 50;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM sink s1 AS select * FROM v1 INTO embedded_callback with "
        "(batch_window_size = '1');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *streamName = "t1";
    const char *sinkName = "s1";
    ret = GmeStreamRegisterCallBack(conn, sinkName, StEmbStreamGetActQryResultGet5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t rowNum = 50;
    int64_t batchNum = 49;
    int64_t *id = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        id[i] = i + 1;
    }
    int64_t *time = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        time[i] = i + 10;
    }
    double *age = (double *)malloc(rowNum * sizeof(double));
    for (int64_t i = 0; i < rowNum; i++) {
        age[i] = i + 0.4;
    }
    if (id != NULL && time != NULL && age != NULL) {
        RdBatchWriteStreamTable(streamName, rowNum, id, time, age);
    }
    AW_MACRO_EXPECT_EQ_INT(batchNum, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(batchNum, g_datacount);
    ret = GmeSqlExecute(conn, "drop stream sink s1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream view v1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream table t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(id);
    free(time);
    free(age);
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}

// 050后继结点为包含逻辑运算符左表达式(and、or)的sink/view，批量结构化写入数据到该表
// 	true
TEST_F(embedded_batch_write_interact, STREAM_044_050)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char *errMsg = NULL;
    ret = GmeSqlExecute(
        conn, "create stream table t1(id integer, time integer, age real, name char(50));", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(
        conn, "create stream view v1 as select * from t1 where id < 50 and id > 10;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM sink s1 AS select * FROM v1 INTO embedded_callback with "
        "(batch_window_size = '1');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *streamName = "t1";
    const char *sinkName = "s1";
    ret = GmeStreamRegisterCallBack(conn, sinkName, StEmbStreamGetActQryResultGet12);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t rowNum = 50;
    int64_t batchNum = 39;
    int64_t *id = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        id[i] = i + 1;
    }
    int64_t *time = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        time[i] = i + 10;
    }
    double *age = (double *)malloc(rowNum * sizeof(double));
    for (int64_t i = 0; i < rowNum; i++) {
        age[i] = i + 0.4;
    }
    if (id != NULL && time != NULL && age != NULL) {
        RdBatchWriteStreamTable(streamName, rowNum, id, time, age);
    }
    AW_MACRO_EXPECT_EQ_INT(batchNum, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(batchNum, g_datacount);
    ret = GmeSqlExecute(conn, "drop stream sink s1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream view v1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream table t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(id);
    free(time);
    free(age);
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}

// 047后继结点为包含模糊查询左表达式(like)的sink/view，批量结构化写入数据到该表
// 	true
TEST_F(embedded_batch_write_interact, STREAM_044_051)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char *errMsg = NULL;
    ret = GmeSqlExecute(
        conn, "create stream table t1(id integer, time integer, age real, name char(50));", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret =
        GmeSqlExecute(conn, "create stream view v1 as select * from t1 where name like 'name_1';", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM sink s1 AS select * FROM v1 INTO embedded_callback with "
        "(batch_window_size = '1');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *streamName = "t1";
    const char *sinkName = "s1";
    ret = GmeStreamRegisterCallBack(conn, sinkName, StEmbStreamGetActQryResultGet4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t rowNum = 50;
    int64_t batchNum = 1;
    int64_t *id = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        id[i] = i + 1;
    }
    int64_t *time = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        time[i] = i + 10;
    }
    double *age = (double *)malloc(rowNum * sizeof(double));
    for (int64_t i = 0; i < rowNum; i++) {
        age[i] = i + 0.4;
    }
    if (id != NULL && time != NULL && age != NULL) {
        RdBatchWriteStreamTable(streamName, rowNum, id, time, age);
    }
    AW_MACRO_EXPECT_EQ_INT(batchNum, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(1, g_datacount);
    ret = GmeSqlExecute(conn, "drop stream sink s1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream view v1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream table t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(id);
    free(time);
    free(age);
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}

// 052后继结点为包含over聚合的窗口view，批量结构化写入数据到该表
// 	true
TEST_F(embedded_batch_write_interact, STREAM_044_052)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char *errMsg = NULL;
    ret = GmeSqlExecute(conn,
        "create stream table t1(id integer, time integer, age real, name char(50), "
        "WATERMARK FOR time AS time);",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "create stream view v1 as select id, time, age, name, sum(id) over(PARTITION by "
        "window_start, window_end) "
        "FROM TABLE(HOP(TABLE t1, time, INTERVAL '5' SECONDS, INTERVAL '5' SECONDS));",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM sink s1 AS select id, time, age, name, sum_id FROM v1 INTO embedded_callback with "
        "(batch_window_size = '1');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *streamName = "t1";
    const char *sinkName = "s1";
    ret = GmeStreamRegisterCallBack(conn, sinkName, StEmbStreamGetActQryResultGet13);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t rowNum = 7;
    int64_t batchNum = 5;
    int64_t *id = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        id[i] = i + 10;
    }
    int64_t *time = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        time[i] = i;
    }
    double *age = (double *)malloc(rowNum * sizeof(double));
    for (int64_t i = 0; i < rowNum; i++) {
        age[i] = i + 0.4;
    }
    for (int64_t i = 0; i < rowNum; i++) {
        RdBatchWriteStreamTable2(streamName, 1, id, time, age, i);
    }
    AW_MACRO_EXPECT_EQ_INT(batchNum, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(batchNum, g_datacount);

    ret = GmeSqlExecute(conn, "drop stream sink s1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream view v1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream table t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}

// 053后继结点为包含group by聚合的窗口view，批量结构化写入数据到该表
// 	true
TEST_F(embedded_batch_write_interact, STREAM_044_053)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char *errMsg = NULL;
    ret = GmeSqlExecute(conn,
        "create stream table t1(id integer, time integer, age real, name char(50), "
        "WATERMARK FOR time AS time);",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "create stream view v1 as select id, time, age, name, sum(id) "
        "FROM TABLE(HOP(TABLE t1, time, INTERVAL '5' SECONDS, INTERVAL '5' SECONDS)) "
        "group by window_start, window_end with (tuple_buffer_size = 1);",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM sink s1 AS select first_id, first_time, first_age, first_name, "
        "sum_id FROM v1 INTO embedded_callback with "
        "(batch_window_size = '1');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *streamName = "t1";
    const char *sinkName = "s1";
    ret = GmeStreamRegisterCallBack(conn, sinkName, StEmbStreamGetActQryResultGet13);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t rowNum = 7;
    int64_t batchNum = 1;
    int64_t *id = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        id[i] = i + 10;
    }
    int64_t *time = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        time[i] = i;
    }
    double *age = (double *)malloc(rowNum * sizeof(double));
    for (int64_t i = 0; i < rowNum; i++) {
        age[i] = i + 0.4;
    }
    for (int64_t i = 0; i < rowNum; i++) {
        RdBatchWriteStreamTable2(streamName, 1, id, time, age, i);
    }
    AW_MACRO_EXPECT_EQ_INT(batchNum, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(batchNum, g_datacount);

    ret = GmeSqlExecute(conn, "drop stream sink s1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream view v1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream table t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}
