/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description:嵌入式环境下流计算支持批量结构化写并发用例
 * Author: moxiaotong
 * Create: 2025-07-01
 */
#include "gtest/gtest.h"
#include "text_util.h"

class embedded_batch_write_concurrency : public testing::Test {
public:
    static void SetUpTestCase();
    static void TearDownTestCase();
    virtual void SetUp();
    virtual void TearDown();
};

void embedded_batch_write_concurrency::SetUpTestCase()
{
    int32_t ret;
    char sysGMDBCfg[MAX_CMD_SIZE];
    system("stop.sh -f");
    (void)snprintf(sysGMDBCfg, MAX_CMD_SIZE, "%s/gmserver.ini", g_sysGMDBCfgPath);
    ret = GmeOpen(sysGMDBCfg, GME_OPEN_CREATE, &conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void embedded_batch_write_concurrency::TearDownTestCase()
{
    int32_t ret;
    ret = GmeClose(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void embedded_batch_write_concurrency::SetUp()
{
    g_batchcount = 0;
    g_datacount = 0;
}

void embedded_batch_write_concurrency::TearDown()
{}

void *writeTable1(void *args)
{
    const char *streamName = "t1";
    int64_t rowNum = 50;
    int64_t *id = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        id[i] = i + 1;
    }
    int64_t *time = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        time[i] = i + 10;
    }
    double *age = (double *)malloc(rowNum * sizeof(double));
    for (int64_t i = 0; i < rowNum; i++) {
        age[i] = i + 0.4;
    }
    if (id != NULL && time != NULL && age != NULL) {
        RdBatchWriteStreamTable(streamName, rowNum, id, time, age);
    }
    free(id);
    free(time);
    free(age);
}

void *writeTable2(void *args)
{
    int64_t j = *(int64_t *)&args;
    char *streamName;
    int64_t rowNum = 50;
    int64_t *id = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        id[i] = i + 1;
    }
    int64_t *time = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        time[i] = i + 10;
    }
    double *age = (double *)malloc(rowNum * sizeof(double));
    for (int64_t i = 0; i < rowNum; i++) {
        age[i] = i + 0.4;
    }
    (void)snprintf(streamName, RD_STREAM_TABLE_T1_NAME_SIZE, "t%d", j);
    if (id != NULL && time != NULL && age != NULL) {
        RdBatchWriteStreamTable(streamName, rowNum, id, time, age);
    }
    free(id);
    free(time);
    free(age);
}
// 054五个线程同时对一个流表写
// 	true
TEST_F(embedded_batch_write_concurrency, STREAM_044_054)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char *errMsg = NULL;
    ret = GmeSqlExecute(
        conn, "create stream table t1(id integer, time integer, age real, name char(50));", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "create stream view v1 as select * from t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        " create stream sink s1 AS select * from v1 "
        "INTO embedded_callback with(batch_window_size = '50') ",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *sinkName = "s1";
    ret = GmeStreamRegisterCallBack(conn, sinkName, StEmbStreamGetActQryResultGet4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int64_t batchNum = 5;
    int64_t rowNum = 50;
    static const uint32_t threadNum = 5;
    pthread_t threadsWrite[threadNum] = {0};
    for (int32_t i = 0; i < threadNum; i++) {
        ret = pthread_create(&threadsWrite[i], NULL, writeTable1, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (int32_t i = 0; i < threadNum; i++) {
        ret = pthread_join(threadsWrite[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_MACRO_EXPECT_EQ_INT(batchNum, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(batchNum * rowNum, g_datacount);

    ret = GmeSqlExecute(conn, "drop stream sink s1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream view v1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream table t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}

// 055五个线程同时对五个流表写
// 	true
TEST_F(embedded_batch_write_concurrency, STREAM_044_055)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char *errMsg = NULL;
    uint32_t tableNum = 5;
    char create[MAX_LINE_LENGTH] = {0};
    char drop[MAX_LINE_LENGTH] = {0};
    for (uint32_t i = 0; i < tableNum; i++) {
        (void)snprintf(
            create, MAX_LINE_LENGTH, "create stream table t%d(id integer, time integer, age real, name char(50));", i);
        ret = GmeSqlExecute(conn, create, NULL, NULL, &errMsg);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    string crtView = "create stream view v1 AS select * FROM TABLE(UNION(TABLE t0, ";
    for (int32_t i = 1; i < tableNum - 1; ++i) {
        char temp[32] = {0};
        (void)sprintf(temp, "TABLE t%d, ", i);
        crtView += temp;
    }
    crtView += "TABLE t4));";
    const char *sqlStatement = crtView.c_str();
    ret = GmeSqlExecute(conn, sqlStatement, NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmeSqlExecute(conn,
        " create stream sink s1 AS select * from v1 "
        "INTO embedded_callback with(batch_window_size = '50') ",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *sinkName = "s1";
    ret = GmeStreamRegisterCallBack(conn, sinkName, StEmbStreamGetActQryResultGet4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int64_t batchNum = 5;
    int64_t rowNum = 50;
    static const uint32_t threadNum = 5;
    pthread_t threadsWrite[threadNum] = {0};
    for (int32_t i = 0; i < threadNum; i++) {
        ret = pthread_create(&threadsWrite[i], NULL, writeTable2, (void *)i);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (int32_t i = 0; i < threadNum; i++) {
        ret = pthread_join(threadsWrite[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_MACRO_EXPECT_EQ_INT(batchNum, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(batchNum * rowNum, g_datacount);

    ret = GmeSqlExecute(conn, "drop stream sink s1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream view v1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < tableNum; i++) {
        (void)snprintf(drop, MAX_LINE_LENGTH, "drop stream table t%u;", i);
        ret = GmeSqlExecute(conn, drop, NULL, NULL, &errMsg);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}

// 056三十二个线程同时对32个流表写
// 	true
TEST_F(embedded_batch_write_concurrency, STREAM_044_056)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char *errMsg = NULL;
    uint32_t tableNum = 32;
    char create[MAX_LINE_LENGTH] = {0};
    char drop[MAX_LINE_LENGTH] = {0};
    for (uint32_t i = 0; i < tableNum; i++) {
        (void)snprintf(
            create, MAX_LINE_LENGTH, "create stream table t%d(id integer, time integer, age real, name char(50));", i);
        ret = GmeSqlExecute(conn, create, NULL, NULL, &errMsg);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    string crtView = "create stream view v1 AS select * FROM TABLE(UNION(TABLE t0, ";
    for (int32_t i = 1; i < tableNum - 1; ++i) {
        char temp[32] = {0};
        (void)sprintf(temp, "TABLE t%d, ", i);
        crtView += temp;
    }
    crtView += "TABLE t31));";
    const char *sqlStatement = crtView.c_str();
    ret = GmeSqlExecute(conn, sqlStatement, NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmeSqlExecute(conn,
        " create stream sink s1 AS select * from v1 "
        "INTO embedded_callback with(batch_window_size = '50') ",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *sinkName = "s1";
    ret = GmeStreamRegisterCallBack(conn, sinkName, StEmbStreamGetActQryResultGet4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int64_t batchNum = 32;
    int64_t rowNum = 50;
    static const uint32_t threadNum = 32;
    pthread_t threadsWrite[threadNum] = {0};
    for (int32_t i = 0; i < threadNum; i++) {
        ret = pthread_create(&threadsWrite[i], NULL, writeTable2, (void *)i);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    for (int32_t i = 0; i < threadNum; i++) {
        ret = pthread_join(threadsWrite[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_MACRO_EXPECT_EQ_INT(batchNum, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(batchNum * rowNum, g_datacount);

    ret = GmeSqlExecute(conn, "drop stream sink s1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream view v1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < tableNum; i++) {
        (void)snprintf(drop, MAX_LINE_LENGTH, "drop stream table t%u;", i);
        ret = GmeSqlExecute(conn, drop, NULL, NULL, &errMsg);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}
