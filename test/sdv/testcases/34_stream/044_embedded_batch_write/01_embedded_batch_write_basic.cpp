/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description:嵌入式环境下流计算支持批量结构化写门槛、基本用例
 * Author: moxiaotong
 * Create: 2025-06-26
 */
#include "gtest/gtest.h"
#include "text_util.h"

class embedded_batch_write_basic : public testing::Test {
public:
    static void SetUpTestCase();
    static void TearDownTestCase();
    virtual void SetUp();
    virtual void TearDown();
};

void embedded_batch_write_basic::SetUpTestCase()
{
    int32_t ret;
    char sysGMDBCfg[MAX_CMD_SIZE];
    system("stop.sh -f");
    (void)snprintf(sysGMDBCfg, MAX_CMD_SIZE, "%s/gmserver.ini", g_sysGMDBCfgPath);
    ret = GmeOpen(sysGMDBCfg, GME_OPEN_CREATE, &conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void embedded_batch_write_basic::TearDownTestCase()
{
    int32_t ret;
    ret = GmeClose(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void embedded_batch_write_basic::SetUp()
{
    g_batchcount = 0;
    g_datacount = 0;
}

void embedded_batch_write_basic::TearDown()
{}

// 001创建包含INTEGER、CHAR、DOUBLE类型列的流表，批量结构化写入数据到该表
// true
TEST_F(embedded_batch_write_basic, STREAM_044_001)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char *errMsg = NULL;
    ret = GmeSqlExecute(
        conn, "create stream table t1(id integer, time integer, age real, name char(50));", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "create stream view v1 as select * from t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        " create stream sink s1 AS select * from v1 "
        "INTO embedded_callback with(batch_window_size = '5') ",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *streamName = "t1";
    const char *sinkName = "s1";
    ret = GmeStreamRegisterCallBack(conn, sinkName, StEmbStreamGetActQryResultGet1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t rowNum = 5;
    int64_t batchNum = 1;
    int64_t id[rowNum] = {1, 2, 3, 4, 5};
    int64_t time[rowNum] = {0, 1, 2, 3, 4};
    double age[rowNum] = {6.2, 1.7, 8.4, 6.9, 3.0};
    RdBatchWriteStreamTable(streamName, rowNum, id, time, age);
    AW_MACRO_EXPECT_EQ_INT(batchNum, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(rowNum * batchNum, g_datacount);

    ret = GmeSqlExecute(conn, "drop stream sink s1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream view v1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream table t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}

// 002批量插入1条相同数据，连续10次到该表
// 	true
TEST_F(embedded_batch_write_basic, STREAM_044_002)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char *errMsg = NULL;
    ret = GmeSqlExecute(
        conn, "create stream table t1(id integer, time integer, age real, name char(50));", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "create stream view v1 as select * from t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM sink s1 AS select * FROM v1 INTO embedded_callback with "
        "(batch_window_size = '1');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *streamName = "t1";
    const char *sinkName = "s1";
    ret = GmeStreamRegisterCallBack(conn, sinkName, StEmbStreamGetActQryResultGet2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t rowNum = 1;
    int64_t batchNum = 10;
    int64_t id[rowNum] = {1};
    int64_t time[rowNum] = {3};
    double age[rowNum] = {8.4};
    for (int32_t j = 0; j < batchNum; j++) {
        RdBatchWriteStreamTable(streamName, rowNum, id, time, age);
    }
    AW_MACRO_EXPECT_EQ_INT(batchNum, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(batchNum * rowNum, g_datacount);
    ret = GmeSqlExecute(conn, "drop stream sink s1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream view v1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream table t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}
// 003批量插入1024条相同数据，连续10次到该表
// 	true
TEST_F(embedded_batch_write_basic, STREAM_044_003)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char *errMsg = NULL;
    ret = GmeSqlExecute(
        conn, "create stream table t1(id integer, time integer, age real, name char(50));", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM sink s1 AS select * FROM t1 INTO embedded_callback with "
        "(batch_window_size = '1024');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *streamName = "t1";
    const char *sinkName = "s1";
    ret = GmeStreamRegisterCallBack(conn, sinkName, StEmbStreamGetActQryResultGet3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t rowNum = 1024;
    int64_t batchNum = 10;
    int64_t *id = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        id[i] = 11;
    }
    int64_t *time = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        time[i] = 26;
    }
    double *age = (double *)malloc(rowNum * sizeof(double));
    for (int64_t i = 0; i < rowNum; i++) {
        age[i] = 12.4;
    }
    if (id != NULL && time != NULL && age != NULL) {
        for (int32_t j = 0; j < batchNum; j++) {
            RdBatchWriteStreamTable(streamName, rowNum, id, time, age);
        }
    }
    AW_MACRO_EXPECT_EQ_INT(batchNum, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(batchNum * rowNum, g_datacount);
    ret = GmeSqlExecute(conn, "drop stream sink s1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream table t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(id);
    free(time);
    free(age);
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}

// 004批量插入1024条不同数据，连续10次到该表
// 	true
TEST_F(embedded_batch_write_basic, STREAM_044_004)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char *errMsg = NULL;
    ret = GmeSqlExecute(
        conn, "create stream table t1(id integer, time integer, age real, name char(50));", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "create stream view v1 as select * from t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM sink s1 AS select * FROM v1 INTO embedded_callback with "
        "(batch_window_size = '1024');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *streamName = "t1";
    const char *sinkName = "s1";
    ret = GmeStreamRegisterCallBack(conn, sinkName, StEmbStreamGetActQryResultGet4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t rowNum = 1024;
    int64_t batchNum = 10;
    int64_t *id = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        id[i] = i + 1;
    }
    int64_t *time = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        time[i] = i + 10;
    }
    double *age = (double *)malloc(rowNum * sizeof(double));
    for (int64_t i = 0; i < rowNum; i++) {
        age[i] = i + 0.4;
    }
    if (id != NULL && time != NULL && age != NULL) {
        for (int32_t j = 0; j < 10; j++) {
            RdBatchWriteStreamTable(streamName, rowNum, id, time, age);
        }
    }
    AW_MACRO_EXPECT_EQ_INT(batchNum, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(batchNum * rowNum, g_datacount);
    ret = GmeSqlExecute(conn, "drop stream sink s1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream view v1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream table t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(id);
    free(time);
    free(age);
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}

// 005批量插入1023条相同数据，连续10次到该表
// 	true
TEST_F(embedded_batch_write_basic, STREAM_044_005)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char *errMsg = NULL;
    ret = GmeSqlExecute(
        conn, "create stream table t1(id integer, time integer, age real, name char(50));", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM sink s1 AS select * FROM t1 INTO embedded_callback with "
        "(batch_window_size = '1023');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *streamName = "t1";
    const char *sinkName = "s1";
    ret = GmeStreamRegisterCallBack(conn, sinkName, StEmbStreamGetActQryResultGet3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t rowNum = 1023;
    int64_t batchNum = 10;
    int64_t *id = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        id[i] = 11;
    }
    int64_t *time = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        time[i] = 26;
    }
    double *age = (double *)malloc(rowNum * sizeof(double));
    for (int64_t i = 0; i < rowNum; i++) {
        age[i] = 12.4;
    }
    if (id != NULL && time != NULL && age != NULL) {
        for (int32_t j = 0; j < 10; j++) {
            RdBatchWriteStreamTable(streamName, rowNum, id, time, age);
        }
    }
    AW_MACRO_EXPECT_EQ_INT(batchNum, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(batchNum * rowNum, g_datacount);
    ret = GmeSqlExecute(conn, "drop stream sink s1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream table t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(id);
    free(time);
    free(age);
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}

// 006批量插入1023条不同数据，连续10次到该表
// 	true
TEST_F(embedded_batch_write_basic, STREAM_044_006)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char *errMsg = NULL;
    ret = GmeSqlExecute(
        conn, "create stream table t1(id integer, time integer, age real, name char(50));", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM sink s1 AS select * FROM t1 INTO embedded_callback with "
        "(batch_window_size = '1023');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *streamName = "t1";
    const char *sinkName = "s1";
    ret = GmeStreamRegisterCallBack(conn, sinkName, StEmbStreamGetActQryResultGet4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t rowNum = 1023;
    int64_t batchNum = 10;
    int64_t *id = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        id[i] = i + 1;
    }
    int64_t *time = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        time[i] = i + 10;
    }
    double *age = (double *)malloc(rowNum * sizeof(double));
    for (int64_t i = 0; i < rowNum; i++) {
        age[i] = i + 0.4;
    }
    if (id != NULL && time != NULL && age != NULL) {
        for (int32_t j = 0; j < 10; j++) {
            RdBatchWriteStreamTable(streamName, rowNum, id, time, age);
        }
    }
    AW_MACRO_EXPECT_EQ_INT(batchNum, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(batchNum * rowNum, g_datacount);
    ret = GmeSqlExecute(conn, "drop stream sink s1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream table t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(id);
    free(time);
    free(age);
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}

// 007批量插入数据，batch_window_size为100，触发callback函数
// 	true
TEST_F(embedded_batch_write_basic, STREAM_044_007)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char *errMsg = NULL;
    ret = GmeSqlExecute(
        conn, "create stream table t1(id integer, time integer, age real, name char(50));", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM sink s1 AS select * FROM t1 INTO embedded_callback with "
        "(batch_window_size = '100');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *streamName = "t1";
    const char *sinkName = "s1";
    ret = GmeStreamRegisterCallBack(conn, sinkName, StEmbStreamGetActQryResultGet5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t rowNum = 200;
    int64_t batchNum = 2;
    int64_t *id = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        id[i] = i + 1;
    }
    int64_t *time = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        time[i] = i + 10;
    }
    double *age = (double *)malloc(rowNum * sizeof(double));
    for (int64_t i = 0; i < rowNum; i++) {
        age[i] = i + 0.4;
    }
    if (id != NULL && time != NULL && age != NULL) {
        RdBatchWriteStreamTable(streamName, rowNum, id, time, age);
    }
    AW_MACRO_EXPECT_EQ_INT(batchNum, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(rowNum, g_datacount);
    ret = GmeSqlExecute(conn, "drop stream sink s1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream table t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(id);
    free(time);
    free(age);
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}

// 008批量插入数据，batch_window_size为100，不触发callback函数
// 	true
TEST_F(embedded_batch_write_basic, STREAM_044_008)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char *errMsg = NULL;
    ret = GmeSqlExecute(
        conn, "create stream table t1(id integer, time integer, age real, name char(50));", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM sink s1 AS select * FROM t1 INTO embedded_callback with "
        "(batch_window_size = '100');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *streamName = "t1";
    const char *sinkName = "s1";
    ret = GmeStreamRegisterCallBack(conn, sinkName, StEmbStreamGetActQryResultGet4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t rowNum = 50;
    int64_t batchNum = 1;
    int64_t *id = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        id[i] = i + 1;
    }
    int64_t *time = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        time[i] = i + 10;
    }
    double *age = (double *)malloc(rowNum * sizeof(double));
    for (int64_t i = 0; i < rowNum; i++) {
        age[i] = i + 0.4;
    }
    if (id != NULL && time != NULL && age != NULL) {
        RdBatchWriteStreamTable(streamName, rowNum, id, time, age);
    }
    AW_MACRO_EXPECT_EQ_INT(0, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(0, g_datacount);
    ret = GmeSqlExecute(conn, "drop stream sink s1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream table t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(id);
    free(time);
    free(age);
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}

// 009批量插入数据超过窗口大小，batch_window_size为1，触发callback函数
// 	true
TEST_F(embedded_batch_write_basic, STREAM_044_009)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char *errMsg = NULL;
    ret = GmeSqlExecute(
        conn, "create stream table t1(id integer, time integer, age real, name char(50));", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM sink s1 AS select * FROM t1 INTO embedded_callback with "
        "(batch_window_size = '1');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *streamName = "t1";
    const char *sinkName = "s1";
    ret = GmeStreamRegisterCallBack(conn, sinkName, StEmbStreamGetActQryResultGet5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t rowNum = 50;
    int64_t batchNum = 50;
    int64_t *id = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        id[i] = i + 1;
    }
    int64_t *time = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        time[i] = i + 10;
    }
    double *age = (double *)malloc(rowNum * sizeof(double));
    for (int64_t i = 0; i < rowNum; i++) {
        age[i] = i + 0.4;
    }
    if (id != NULL && time != NULL && age != NULL) {
        RdBatchWriteStreamTable(streamName, rowNum, id, time, age);
    }
    AW_MACRO_EXPECT_EQ_INT(batchNum, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(rowNum, g_datacount);
    ret = GmeSqlExecute(conn, "drop stream sink s1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream table t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(id);
    free(time);
    free(age);
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}

// 010GmeStreamStructWrite插入负数
// 	true
TEST_F(embedded_batch_write_basic, STREAM_044_010)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char *errMsg = NULL;
    ret = GmeSqlExecute(
        conn, "create stream table t1(id integer, time integer, age real, name char(50));", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM sink s1 AS select * FROM t1 INTO embedded_callback with "
        "(batch_window_size = '50');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *streamName = "t1";
    const char *sinkName = "s1";
    ret = GmeStreamRegisterCallBack(conn, sinkName, StEmbStreamGetActQryResultGet6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t rowNum = 50;
    int64_t batchNum = 1;
    int64_t *id = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        id[i] = -1;
    }
    int64_t *time = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        time[i] = -30;
    }
    double *age = (double *)malloc(rowNum * sizeof(double));
    for (int64_t i = 0; i < rowNum; i++) {
        age[i] = -2.4;
    }
    if (id != NULL && time != NULL && age != NULL) {
        RdBatchWriteStreamTable(streamName, rowNum, id, time, age);
    }
    AW_MACRO_EXPECT_EQ_INT(batchNum, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(batchNum * rowNum, g_datacount);
    ret = GmeSqlExecute(conn, "drop stream sink s1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream table t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(id);
    free(time);
    free(age);
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}

// 011一个流表后面连两个view，每个view后面都跟两个sink
// 1流表 - 2view - 4SINK
// 	true
TEST_F(embedded_batch_write_basic, STREAM_044_011)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char *errMsg = NULL;
    ret = GmeSqlExecute(
        conn, "create stream table t1(id integer, time integer, age real, name char(50));", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "create stream view v1 as select * from t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "create stream view v2 as select * from t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM sink s1 AS select * FROM v1 INTO embedded_callback with "
        "(batch_window_size = '50');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM sink sink2 AS select * FROM v1 INTO embedded_callback with "
        "(batch_window_size = '50');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM sink sink3 AS select * FROM v2 INTO embedded_callback with "
        "(batch_window_size = '50');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM sink sink4 AS select * FROM v2 INTO embedded_callback with "
        "(batch_window_size = '50');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *streamName = "t1";
    const char *sinkName1 = "s1";
    const char *sinkName2 = "sink2";
    const char *sinkName3 = "sink3";
    const char *sinkName4 = "sink4";
    ret = GmeStreamRegisterCallBack(conn, sinkName1, StEmbStreamGetActQryResultGet4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeStreamRegisterCallBack(conn, sinkName2, StEmbStreamGetActQryResultGet4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeStreamRegisterCallBack(conn, sinkName3, StEmbStreamGetActQryResultGet4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeStreamRegisterCallBack(conn, sinkName4, StEmbStreamGetActQryResultGet4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int64_t rowNum = 50;
    int64_t batchNum = 4;
    int64_t *id = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        id[i] = i + 1;
    }
    int64_t *time = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        time[i] = i + 10;
    }
    double *age = (double *)malloc(rowNum * sizeof(double));
    for (int64_t i = 0; i < rowNum; i++) {
        age[i] = i + 0.4;
    }
    if (id != NULL && time != NULL && age != NULL) {
        RdBatchWriteStreamTable(streamName, rowNum, id, time, age);
    }
    AW_MACRO_EXPECT_EQ_INT(batchNum, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(batchNum * rowNum, g_datacount);
    ret = GmeSqlExecute(conn, "drop stream sink s1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream sink sink2;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream sink sink3;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream sink sink4;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream view v1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream view v2;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream table t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(id);
    free(time);
    free(age);
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}

// 012一个流表后面连32个sink
// 	true
TEST_F(embedded_batch_write_basic, STREAM_044_012)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char create[MAX_LINE_LENGTH] = {0};
    char drop[MAX_LINE_LENGTH] = {0};
    uint32_t tableNum = 32;
    char *errMsg = NULL;
    ret = GmeSqlExecute(
        conn, "create stream table t1(id integer, time integer, age real, name char(50));", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < tableNum; i++) {
        (void)snprintf(create, MAX_LINE_LENGTH,
            "create stream sink sink%d as select * from t1 into embedded_callback "
            "with (batch_window_size = 50);",
            i);
        ret = GmeSqlExecute(conn, create, NULL, NULL, &errMsg);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    const char *streamName = "t1";
    char sinkName[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
    for (uint32_t i = 0; i < tableNum; i++) {
        (void)snprintf(sinkName, RD_STREAM_TABLE_T1_NAME_SIZE, "sink%d", i);
        ret = GmeStreamRegisterCallBack(conn, sinkName, StEmbStreamGetActQryResultGet4);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    int64_t rowNum = 50;
    int64_t batchNum = 32;
    int64_t *id = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        id[i] = i + 1;
    }
    int64_t *time = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        time[i] = i + 10;
    }
    double *age = (double *)malloc(rowNum * sizeof(double));
    for (int64_t i = 0; i < rowNum; i++) {
        age[i] = i + 0.4;
    }
    if (id != NULL && time != NULL && age != NULL) {
        RdBatchWriteStreamTable(streamName, rowNum, id, time, age);
    }
    AW_MACRO_EXPECT_EQ_INT(batchNum, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(batchNum * rowNum, g_datacount);
    for (uint32_t i = 0; i < tableNum; i++) {
        (void)snprintf(drop, MAX_LINE_LENGTH, "drop stream sink sink%u;", i);
        ret = GmeSqlExecute(conn, drop, NULL, NULL, &errMsg);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmeSqlExecute(conn, "drop stream table t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(id);
    free(time);
    free(age);
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}
// 013一个view有32个后继结点sink
// 	true
TEST_F(embedded_batch_write_basic, STREAM_044_013)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char create[MAX_LINE_LENGTH] = {0};
    char drop[MAX_LINE_LENGTH] = {0};
    uint32_t tableNum = 32;
    char *errMsg = NULL;
    ret = GmeSqlExecute(
        conn, "create stream table t1(id integer, time integer, age real, name char(50));", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(
        conn, "create stream view v1 as select * from t1 with (tuple_buffer_size = 1200);", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < tableNum; i++) {
        (void)snprintf(create, MAX_LINE_LENGTH,
            "create stream sink sink%u as select * from v1 into embedded_callback "
            "with (batch_window_size = 50);",
            i);
        ret = GmeSqlExecute(conn, create, NULL, NULL, &errMsg);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    const char *streamName = "t1";
    char sinkName[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
    for (uint32_t i = 0; i < tableNum; i++) {
        (void)snprintf(sinkName, RD_STREAM_TABLE_T1_NAME_SIZE, "sink%d", i);
        ret = GmeStreamRegisterCallBack(conn, sinkName, StEmbStreamGetActQryResultGet4);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    int64_t rowNum = 50;
    int64_t batchNum = 32;
    int64_t *id = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        id[i] = i + 1;
    }
    int64_t *time = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        time[i] = i + 10;
    }
    double *age = (double *)malloc(rowNum * sizeof(double));
    for (int64_t i = 0; i < rowNum; i++) {
        age[i] = i + 0.4;
    }
    if (id != NULL && time != NULL && age != NULL) {
        RdBatchWriteStreamTable(streamName, rowNum, id, time, age);
    }
    AW_MACRO_EXPECT_EQ_INT(batchNum, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(batchNum * rowNum, g_datacount);
    for (uint32_t i = 0; i < tableNum; i++) {
        (void)snprintf(drop, MAX_LINE_LENGTH, "drop stream sink sink%u;", i);
        ret = GmeSqlExecute(conn, drop, NULL, NULL, &errMsg);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmeSqlExecute(conn, "drop stream view v1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream table t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(id);
    free(time);
    free(age);
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}

// 014创建流表，sink，写入50条数据，删除流表，sink，再次创建流表，sink，写入100条数据
// 	true
TEST_F(embedded_batch_write_basic, STREAM_044_014)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char *errMsg = NULL;
    ret = GmeSqlExecute(
        conn, "create stream table t1(id integer, time integer, age real, name char(50));", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM sink s1 AS select * FROM t1 INTO embedded_callback with "
        "(batch_window_size = '50');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *streamName = "t1";
    const char *sinkName = "s1";
    ret = GmeStreamRegisterCallBack(conn, sinkName, StEmbStreamGetActQryResultGet4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t rowNum = 50;
    int64_t batchNum = 1;
    int64_t *id = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        id[i] = i + 1;
    }
    int64_t *time = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        time[i] = i + 10;
    }
    double *age = (double *)malloc(rowNum * sizeof(double));
    for (int64_t i = 0; i < rowNum; i++) {
        age[i] = i + 0.4;
    }
    if (id != NULL && time != NULL && age != NULL) {
        RdBatchWriteStreamTable(streamName, rowNum, id, time, age);
    }
    AW_MACRO_EXPECT_EQ_INT(batchNum, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(batchNum * rowNum, g_datacount);

    ret = GmeSqlExecute(conn, "drop stream sink s1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream table t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmeSqlExecute(
        conn, "create stream table t1(id integer, time integer, age real, name char(50));", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM sink s1 AS select * FROM t1 INTO embedded_callback with "
        "(batch_window_size = '100');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_batchcount = 0;
    g_datacount = 0;
    ret = GmeStreamRegisterCallBack(conn, sinkName, StEmbStreamGetActQryResultGet4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    rowNum = 100;
    id = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        id[i] = i + 1;
    }
    time = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        time[i] = i + 10;
    }
    age = (double *)malloc(rowNum * sizeof(double));
    for (int64_t i = 0; i < rowNum; i++) {
        age[i] = i + 0.4;
    }
    if (id != NULL && time != NULL && age != NULL) {
        RdBatchWriteStreamTable(streamName, rowNum, id, time, age);
    }
    AW_MACRO_EXPECT_EQ_INT(batchNum, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(batchNum * rowNum, g_datacount);
    ret = GmeSqlExecute(conn, "drop stream sink s1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream table t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(id);
    free(time);
    free(age);
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}

// 015后继结点为投影数字常量的view，批量结构化写入数据到该表
// 	true
TEST_F(embedded_batch_write_basic, STREAM_044_015)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char *errMsg = NULL;
    ret = GmeSqlExecute(
        conn, "create stream table t1(id integer, time integer, age real, name char(50));", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret =
        GmeSqlExecute(conn, "create stream view v1 as select 43, time, 3.1415926, name from t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM sink s1 AS select * FROM v1 INTO embedded_callback with "
        "(batch_window_size = '50');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *streamName = "t1";
    const char *sinkName = "s1";
    ret = GmeStreamRegisterCallBack(conn, sinkName, StEmbStreamGetActQryResultGet7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t rowNum = 50;
    int64_t batchNum = 1;
    int64_t *id = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        id[i] = i + 1;
    }
    int64_t *time = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        time[i] = i + 10;
    }
    double *age = (double *)malloc(rowNum * sizeof(double));
    for (int64_t i = 0; i < rowNum; i++) {
        age[i] = i + 0.4;
    }
    if (id != NULL && time != NULL && age != NULL) {
        RdBatchWriteStreamTable(streamName, rowNum, id, time, age);
    }
    AW_MACRO_EXPECT_EQ_INT(batchNum, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(batchNum * rowNum, g_datacount);
    ret = GmeSqlExecute(conn, "drop stream sink s1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream view v1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream table t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(id);
    free(time);
    free(age);
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}
