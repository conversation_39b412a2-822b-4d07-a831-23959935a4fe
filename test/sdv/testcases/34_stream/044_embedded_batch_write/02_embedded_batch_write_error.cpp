/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description:嵌入式环境下流计算支持批量结构化写异常用例
 * Author: moxiaotong
 * Create: 2025-07-01
 */
#include "gtest/gtest.h"
#include "text_util.h"

class embedded_batch_write_error : public testing::Test {
public:
    static void SetUpTestCase();
    static void TearDownTestCase();
    virtual void SetUp();
    virtual void TearDown();
};

void embedded_batch_write_error::SetUpTestCase()
{
    int32_t ret;
    char sysGMDBCfg[MAX_CMD_SIZE];
    system("stop.sh -f");
    (void)snprintf(sysGMDBCfg, MAX_CMD_SIZE, "%s/gmserver.ini", g_sysGMDBCfgPath);
    ret = GmeOpen(sysGMDBCfg, GME_OPEN_CREATE, &conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void embedded_batch_write_error::TearDownTestCase()
{
    int32_t ret;
    ret = GmeClose(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void embedded_batch_write_error::SetUp()
{
    g_batchcount = 0;
    g_datacount = 0;
}

void embedded_batch_write_error::TearDown()
{}

// 016批量插入1025条相同数据
// 	false
TEST_F(embedded_batch_write_error, STREAM_044_016)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char *errMsg = NULL;
    ret = GmeSqlExecute(
        conn, "create stream table t1(id integer, time integer, age real, name char(50));", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "create stream view v1 as select * from t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM sink s1 AS select * FROM v1 INTO embedded_callback with "
        "(batch_window_size = '1025');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *streamName = "t1";
    const char *sinkName = "s1";
    ret = GmeStreamRegisterCallBack(conn, sinkName, StEmbStreamGetActQryResultGet4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t rowNum = 1025;
    int64_t *id = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        id[i] = i + 1;
    }
    int64_t *time = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        time[i] = i + 10;
    }
    double *age = (double *)malloc(rowNum * sizeof(double));
    for (int64_t i = 0; i < rowNum; i++) {
        age[i] = i + 0.4;
    }
    if (id != NULL && time != NULL && age != NULL) {
        RdBatchWriteStreamTable(streamName, rowNum, id, time, age);
    }
    AW_MACRO_EXPECT_EQ_INT(0, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(0, g_datacount);
    ret = GmeSqlExecute(conn, "drop stream sink s1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream view v1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream table t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(id);
    free(time);
    free(age);
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}

// 017创建包含TEXT类型列的流表，批量结构化写入数据到该表
// 	false
TEST_F(embedded_batch_write_error, STREAM_044_017)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char *errMsg = NULL;
    ret =
        GmeSqlExecute(conn, "create stream table t1(id integer, time integer, age real, name char(50), address text);",
            NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}

// 018没有注册callback的表插入数据之后查询不到数据，批量结构化写入数据到该表
// 	false
TEST_F(embedded_batch_write_error, STREAM_044_018)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char *errMsg = NULL;
    ret = GmeSqlExecute(
        conn, "create stream table t1(id integer, time integer, age real, name char(50));", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "create stream view v1 as select * from t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM sink s1 AS select * FROM v1 INTO embedded_callback with "
        "(batch_window_size = '50');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *streamName = "t1";
    const char *sinkName = "s1";
    int64_t rowNum = 50;
    int64_t *id = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        id[i] = i + 1;
    }
    int64_t *time = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        time[i] = i + 10;
    }
    double *age = (double *)malloc(rowNum * sizeof(double));
    for (int64_t i = 0; i < rowNum; i++) {
        age[i] = i + 0.4;
    }
    if (id != NULL && time != NULL && age != NULL) {
        RdBatchWriteStreamTable(streamName, rowNum, id, time, age);
    }
    AW_MACRO_EXPECT_EQ_INT(0, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(0, g_datacount);
    ret = GmeSqlExecute(conn, "drop stream sink s1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream view v1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream table t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(id);
    free(time);
    free(age);
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}

// 019GmeStreamStructWrite接口第一个参数传NULL，其余参数正常
// 	false
TEST_F(embedded_batch_write_error, STREAM_044_019)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char *errMsg = NULL;
    ret = GmeSqlExecute(
        conn, "create stream table t1(id integer, time integer, age real, name char(50));", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "create stream view v1 as select * from t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM sink s1 AS select * FROM v1 INTO embedded_callback with "
        "(batch_window_size = '50');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *streamName = "t1";
    const char *sinkName = "s1";
    ret = GmeStreamRegisterCallBack(conn, sinkName, StEmbStreamGetActQryResultGet4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t rowNum = 50;
    int64_t *id = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        id[i] = i + 1;
    }
    int64_t *time = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        time[i] = i + 10;
    }
    double *age = (double *)malloc(rowNum * sizeof(double));
    for (int64_t i = 0; i < rowNum; i++) {
        age[i] = i + 0.4;
    }
    uint8_t *buf = (uint8_t *)malloc(sizeof(RdStreamTable2DataT) * rowNum);
    uint8_t *tmpBuf = buf;
    if (id != NULL && time != NULL && age != NULL) {
        RdStreamTable2DataT *v1 = NULL;
        for (int64_t i = 0; i < rowNum; ++i) {
            v1 = (RdStreamTable2DataT *)tmpBuf;
            v1->id = id[i];
            v1->waterMark = time[i];
            v1->eventTime = age[i];
            (void)snprintf((char *)v1->name, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", id[i]);
            tmpBuf += sizeof(RdStreamTable2DataT);
        }
        ret = GmeStreamStructWrite(NULL, streamName, buf, sizeof(RdStreamTable2DataT) * rowNum, rowNum, &errMsg);
        AW_MACRO_EXPECT_EQ_INT(GMERR_UNEXPECTED_NULL_VALUE, ret);
    }
    AW_MACRO_EXPECT_EQ_INT(0, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(0, g_datacount);
    ret = GmeSqlExecute(conn, "drop stream sink s1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream view v1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream table t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(id);
    free(time);
    free(age);
    free(buf);
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}

// 020GmeStreamStructWrite接口第二个参数传NULL，其余参数正常
// 	false
TEST_F(embedded_batch_write_error, STREAM_044_020)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char *errMsg = NULL;
    ret = GmeSqlExecute(
        conn, "create stream table t1(id integer, time integer, age real, name char(50));", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "create stream view v1 as select * from t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM sink s1 AS select * FROM v1 INTO embedded_callback with "
        "(batch_window_size = '50');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *streamName = "t1";
    const char *sinkName = "s1";
    ret = GmeStreamRegisterCallBack(conn, sinkName, StEmbStreamGetActQryResultGet4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t rowNum = 50;
    int64_t *id = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        id[i] = i + 1;
    }
    int64_t *time = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        time[i] = i + 10;
    }
    double *age = (double *)malloc(rowNum * sizeof(double));
    for (int64_t i = 0; i < rowNum; i++) {
        age[i] = i + 0.4;
    }
    uint8_t *buf = (uint8_t *)malloc(sizeof(RdStreamTable2DataT) * rowNum);
    uint8_t *tmpBuf = buf;
    if (id != NULL && time != NULL && age != NULL) {
        RdStreamTable2DataT *v1 = NULL;
        for (int64_t i = 0; i < rowNum; ++i) {
            v1 = (RdStreamTable2DataT *)tmpBuf;
            v1->id = id[i];
            v1->waterMark = time[i];
            v1->eventTime = age[i];
            (void)snprintf((char *)v1->name, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", id[i]);
            tmpBuf += sizeof(RdStreamTable2DataT);
        }
        ret = GmeStreamStructWrite(conn, NULL, buf, sizeof(RdStreamTable2DataT) * rowNum, rowNum, &errMsg);
        AW_MACRO_EXPECT_EQ_INT(GMERR_UNEXPECTED_NULL_VALUE, ret);
    }
    AW_MACRO_EXPECT_EQ_INT(0, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(0, g_datacount);
    ret = GmeSqlExecute(conn, "drop stream sink s1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream view v1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream table t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(id);
    free(time);
    free(age);
    free(buf);
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}

// 021GmeStreamStructWrite接口第三个参数传NULL，其余参数正常
// 	false
TEST_F(embedded_batch_write_error, STREAM_044_021)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char *errMsg = NULL;
    ret = GmeSqlExecute(
        conn, "create stream table t1(id integer, time integer, age real, name char(50));", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "create stream view v1 as select * from t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM sink s1 AS select * FROM v1 INTO embedded_callback with "
        "(batch_window_size = '50');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *streamName = "t1";
    const char *sinkName = "s1";
    ret = GmeStreamRegisterCallBack(conn, sinkName, StEmbStreamGetActQryResultGet4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t rowNum = 50;
    int64_t *id = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        id[i] = i + 1;
    }
    int64_t *time = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        time[i] = i + 10;
    }
    double *age = (double *)malloc(rowNum * sizeof(double));
    for (int64_t i = 0; i < rowNum; i++) {
        age[i] = i + 0.4;
    }
    uint8_t *buf = (uint8_t *)malloc(sizeof(RdStreamTable2DataT) * rowNum);
    uint8_t *tmpBuf = buf;
    if (id != NULL && time != NULL && age != NULL) {
        RdStreamTable2DataT *v1 = NULL;
        for (int64_t i = 0; i < rowNum; ++i) {
            v1 = (RdStreamTable2DataT *)tmpBuf;
            v1->id = id[i];
            v1->waterMark = time[i];
            v1->eventTime = age[i];
            (void)snprintf((char *)v1->name, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", id[i]);
            tmpBuf += sizeof(RdStreamTable2DataT);
        }
        ret = GmeStreamStructWrite(conn, streamName, NULL, sizeof(RdStreamTable2DataT) * rowNum, rowNum, &errMsg);
        AW_MACRO_EXPECT_EQ_INT(GMERR_UNEXPECTED_NULL_VALUE, ret);
    }
    AW_MACRO_EXPECT_EQ_INT(0, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(0, g_datacount);
    ret = GmeSqlExecute(conn, "drop stream sink s1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream view v1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream table t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(id);
    free(time);
    free(age);
    free(buf);
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}

// 022GmeStreamStructWrite接口第四个参数传NULL，其余参数正常
// 	false
TEST_F(embedded_batch_write_error, STREAM_044_022)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char *errMsg = NULL;
    ret = GmeSqlExecute(
        conn, "create stream table t1(id integer, time integer, age real, name char(50));", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "create stream view v1 as select * from t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM sink s1 AS select * FROM v1 INTO embedded_callback with "
        "(batch_window_size = '50');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *streamName = "t1";
    const char *sinkName = "s1";
    ret = GmeStreamRegisterCallBack(conn, sinkName, StEmbStreamGetActQryResultGet4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t rowNum = 50;
    int64_t *id = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        id[i] = i + 1;
    }
    int64_t *time = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        time[i] = i + 10;
    }
    double *age = (double *)malloc(rowNum * sizeof(double));
    for (int64_t i = 0; i < rowNum; i++) {
        age[i] = i + 0.4;
    }
    uint8_t *buf = (uint8_t *)malloc(sizeof(RdStreamTable2DataT) * rowNum);
    uint8_t *tmpBuf = buf;
    if (id != NULL && time != NULL && age != NULL) {
        RdStreamTable2DataT *v1 = NULL;
        for (int64_t i = 0; i < rowNum; ++i) {
            v1 = (RdStreamTable2DataT *)tmpBuf;
            v1->id = id[i];
            v1->waterMark = time[i];
            v1->eventTime = age[i];
            (void)snprintf((char *)v1->name, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", id[i]);
            tmpBuf += sizeof(RdStreamTable2DataT);
        }
        ret = GmeStreamStructWrite(conn, streamName, buf, NULL, rowNum, &errMsg);
        AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    }
    AW_MACRO_EXPECT_EQ_INT(0, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(0, g_datacount);
    ret = GmeSqlExecute(conn, "drop stream sink s1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream view v1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream table t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(id);
    free(time);
    free(age);
    free(buf);
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}

// 023GmeStreamStructWrite接口第五个参数传NULL，其余参数正常
// 	false
TEST_F(embedded_batch_write_error, STREAM_044_023)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char *errMsg = NULL;
    ret = GmeSqlExecute(
        conn, "create stream table t1(id integer, time integer, age real, name char(50));", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "create stream view v1 as select * from t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM sink s1 AS select * FROM v1 INTO embedded_callback with "
        "(batch_window_size = '50');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *streamName = "t1";
    const char *sinkName = "s1";
    ret = GmeStreamRegisterCallBack(conn, sinkName, StEmbStreamGetActQryResultGet4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t rowNum = 50;
    int64_t *id = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        id[i] = i + 1;
    }
    int64_t *time = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        time[i] = i + 10;
    }
    double *age = (double *)malloc(rowNum * sizeof(double));
    for (int64_t i = 0; i < rowNum; i++) {
        age[i] = i + 0.4;
    }
    uint8_t *buf = (uint8_t *)malloc(sizeof(RdStreamTable2DataT) * rowNum);
    uint8_t *tmpBuf = buf;
    if (id != NULL && time != NULL && age != NULL) {
        RdStreamTable2DataT *v1 = NULL;
        for (int64_t i = 0; i < rowNum; ++i) {
            v1 = (RdStreamTable2DataT *)tmpBuf;
            v1->id = id[i];
            v1->waterMark = time[i];
            v1->eventTime = age[i];
            (void)snprintf((char *)v1->name, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", id[i]);
            tmpBuf += sizeof(RdStreamTable2DataT);
        }
        ret = GmeStreamStructWrite(conn, streamName, buf, sizeof(RdStreamTable2DataT) * rowNum, NULL, &errMsg);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_MACRO_EXPECT_EQ_INT(0, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(0, g_datacount);
    ret = GmeSqlExecute(conn, "drop stream sink s1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream view v1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream table t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(id);
    free(time);
    free(age);
    free(buf);
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}

// 024GmeStreamStructWrite接口第六个参数传NULL，其余参数正常
// 	false
TEST_F(embedded_batch_write_error, STREAM_044_024)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char *errMsg = NULL;
    ret = GmeSqlExecute(
        conn, "create stream table t1(id integer, time integer, age real, name char(50));", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "create stream view v1 as select * from t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM sink s1 AS select * FROM v1 INTO embedded_callback with "
        "(batch_window_size = '50');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *streamName = "t1";
    const char *sinkName = "s1";
    ret = GmeStreamRegisterCallBack(conn, sinkName, StEmbStreamGetActQryResultGet4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t rowNum = 50;
    int64_t batchNum = 1;
    int64_t *id = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        id[i] = i + 1;
    }
    int64_t *time = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        time[i] = i + 10;
    }
    double *age = (double *)malloc(rowNum * sizeof(double));
    for (int64_t i = 0; i < rowNum; i++) {
        age[i] = i + 0.4;
    }
    uint8_t *buf = (uint8_t *)malloc(sizeof(RdStreamTable2DataT) * rowNum);
    uint8_t *tmpBuf = buf;
    if (id != NULL && time != NULL && age != NULL) {
        RdStreamTable2DataT *v1 = NULL;
        for (int64_t i = 0; i < rowNum; ++i) {
            v1 = (RdStreamTable2DataT *)tmpBuf;
            v1->id = id[i];
            v1->waterMark = time[i];
            v1->eventTime = age[i];
            (void)snprintf((char *)v1->name, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", id[i]);
            tmpBuf += sizeof(RdStreamTable2DataT);
        }
        ret = GmeStreamStructWrite(conn, streamName, buf, sizeof(RdStreamTable2DataT) * rowNum, rowNum, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_MACRO_EXPECT_EQ_INT(batchNum, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(batchNum * rowNum, g_datacount);
    ret = GmeSqlExecute(conn, "drop stream sink s1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream view v1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream table t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(id);
    free(time);
    free(age);
    free(buf);
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}

// 025GmeStreamStructWrite接口第一个参数传未连接的con，其余参数正常
// 	false
TEST_F(embedded_batch_write_error, STREAM_044_025)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char *errMsg = NULL;
    ret = GmeSqlExecute(
        conn, "create stream table t1(id integer, time integer, age real, name char(50));", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "create stream view v1 as select * from t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM sink s1 AS select * FROM v1 INTO embedded_callback with "
        "(batch_window_size = '50');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *streamName = "t1";
    const char *sinkName = "s1";
    ret = GmeStreamRegisterCallBack(conn, sinkName, StEmbStreamGetActQryResultGet4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t rowNum = 50;
    int64_t *id = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        id[i] = i + 1;
    }
    int64_t *time = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        time[i] = i + 10;
    }
    double *age = (double *)malloc(rowNum * sizeof(double));
    for (int64_t i = 0; i < rowNum; i++) {
        age[i] = i + 0.4;
    }
    uint8_t *buf = (uint8_t *)malloc(sizeof(RdStreamTable2DataT) * rowNum);
    uint8_t *tmpBuf = buf;
    if (id != NULL && time != NULL && age != NULL) {
        RdStreamTable2DataT *v1 = NULL;
        for (int64_t i = 0; i < rowNum; ++i) {
            v1 = (RdStreamTable2DataT *)tmpBuf;
            v1->id = id[i];
            v1->waterMark = time[i];
            v1->eventTime = age[i];
            (void)snprintf((char *)v1->name, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", id[i]);
            tmpBuf += sizeof(RdStreamTable2DataT);
        }
        ret = GmeStreamStructWrite(unconn, streamName, buf, sizeof(RdStreamTable2DataT) * rowNum, rowNum, &errMsg);
        AW_MACRO_EXPECT_EQ_INT(GMERR_UNEXPECTED_NULL_VALUE, ret);
    }
    AW_MACRO_EXPECT_EQ_INT(0, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(0, g_datacount);
    ret = GmeSqlExecute(conn, "drop stream sink s1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream view v1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream table t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(id);
    free(time);
    free(age);
    free(buf);
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}

// 026GmeStreamStructWrite接口第二个参数传不存在的流表，其余参数正常
// 	false
TEST_F(embedded_batch_write_error, STREAM_044_026)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char *errMsg = NULL;
    ret = GmeSqlExecute(
        conn, "create stream table t1(id integer, time integer, age real, name char(50));", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "create stream view v1 as select * from t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM sink s1 AS select * FROM v1 INTO embedded_callback with "
        "(batch_window_size = '50');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *streamName = "t2";
    const char *sinkName = "s1";
    ret = GmeStreamRegisterCallBack(conn, sinkName, StEmbStreamGetActQryResultGet4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t rowNum = 50;
    int64_t *id = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        id[i] = i + 1;
    }
    int64_t *time = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        time[i] = i + 10;
    }
    double *age = (double *)malloc(rowNum * sizeof(double));
    for (int64_t i = 0; i < rowNum; i++) {
        age[i] = i + 0.4;
    }
    uint8_t *buf = (uint8_t *)malloc(sizeof(RdStreamTable2DataT) * rowNum);
    uint8_t *tmpBuf = buf;
    if (id != NULL && time != NULL && age != NULL) {
        RdStreamTable2DataT *v1 = NULL;
        for (int64_t i = 0; i < rowNum; ++i) {
            v1 = (RdStreamTable2DataT *)tmpBuf;
            v1->id = id[i];
            v1->waterMark = time[i];
            v1->eventTime = age[i];
            (void)snprintf((char *)v1->name, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", id[i]);
            tmpBuf += sizeof(RdStreamTable2DataT);
        }
        ret = GmeStreamStructWrite(conn, streamName, buf, sizeof(RdStreamTable2DataT) * rowNum, rowNum, &errMsg);
        AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    }
    AW_MACRO_EXPECT_EQ_INT(0, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(0, g_datacount);
    ret = GmeSqlExecute(conn, "drop stream sink s1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream view v1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream table t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(id);
    free(time);
    free(age);
    free(buf);
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}

// 027GmeStreamStructWrite接口第二个参数传sink表，其余参数正常
// 	false
TEST_F(embedded_batch_write_error, STREAM_044_027)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char *errMsg = NULL;
    ret = GmeSqlExecute(
        conn, "create stream table t1(id integer, time integer, age real, name char(50));", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "create stream view v1 as select * from t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM sink s1 AS select * FROM v1 INTO embedded_callback with "
        "(batch_window_size = '50');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *streamName = "t1";
    const char *sinkName = "s1";
    ret = GmeStreamRegisterCallBack(conn, sinkName, StEmbStreamGetActQryResultGet4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t rowNum = 50;
    int64_t *id = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        id[i] = i + 1;
    }
    int64_t *time = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        time[i] = i + 10;
    }
    double *age = (double *)malloc(rowNum * sizeof(double));
    for (int64_t i = 0; i < rowNum; i++) {
        age[i] = i + 0.4;
    }
    uint8_t *buf = (uint8_t *)malloc(sizeof(RdStreamTable2DataT) * rowNum);
    uint8_t *tmpBuf = buf;
    if (id != NULL && time != NULL && age != NULL) {
        RdStreamTable2DataT *v1 = NULL;
        for (int64_t i = 0; i < rowNum; ++i) {
            v1 = (RdStreamTable2DataT *)tmpBuf;
            v1->id = id[i];
            v1->waterMark = time[i];
            v1->eventTime = age[i];
            (void)snprintf((char *)v1->name, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", id[i]);
            tmpBuf += sizeof(RdStreamTable2DataT);
        }
        ret = GmeStreamStructWrite(conn, sinkName, buf, sizeof(RdStreamTable2DataT) * rowNum, rowNum, &errMsg);
        AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    }
    AW_MACRO_EXPECT_EQ_INT(0, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(0, g_datacount);
    ret = GmeSqlExecute(conn, "drop stream sink s1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream view v1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream table t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(id);
    free(time);
    free(age);
    free(buf);
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}

// 028GmeStreamStructWrite接口第二个参数传view表，其余参数正常
// 	false
TEST_F(embedded_batch_write_error, STREAM_044_028)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char *errMsg = NULL;
    ret = GmeSqlExecute(
        conn, "create stream table t1(id integer, time integer, age real, name char(50));", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "create stream view v1 as select * from t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM sink s1 AS select * FROM v1 INTO embedded_callback with "
        "(batch_window_size = '50');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *streamName = "t1";
    const char *viewName = "v1";
    const char *sinkName = "s1";
    ret = GmeStreamRegisterCallBack(conn, sinkName, StEmbStreamGetActQryResultGet4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t rowNum = 50;
    int64_t *id = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        id[i] = i + 1;
    }
    int64_t *time = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        time[i] = i + 10;
    }
    double *age = (double *)malloc(rowNum * sizeof(double));
    for (int64_t i = 0; i < rowNum; i++) {
        age[i] = i + 0.4;
    }
    uint8_t *buf = (uint8_t *)malloc(sizeof(RdStreamTable2DataT) * rowNum);
    uint8_t *tmpBuf = buf;
    if (id != NULL && time != NULL && age != NULL) {
        RdStreamTable2DataT *v1 = NULL;
        for (int64_t i = 0; i < rowNum; ++i) {
            v1 = (RdStreamTable2DataT *)tmpBuf;
            v1->id = id[i];
            v1->waterMark = time[i];
            v1->eventTime = age[i];
            (void)snprintf((char *)v1->name, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", id[i]);
            tmpBuf += sizeof(RdStreamTable2DataT);
        }
        ret = GmeStreamStructWrite(conn, viewName, buf, sizeof(RdStreamTable2DataT) * rowNum, rowNum, &errMsg);
        AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    }
    AW_MACRO_EXPECT_EQ_INT(0, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(0, g_datacount);
    ret = GmeSqlExecute(conn, "drop stream sink s1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream view v1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream table t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(id);
    free(time);
    free(age);
    free(buf);
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}

// 029GmeStreamStructWrite接口第一个参数传0，其余参数正常
// 	false
TEST_F(embedded_batch_write_error, STREAM_044_029)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char *errMsg = NULL;
    ret = GmeSqlExecute(
        conn, "create stream table t1(id integer, time integer, age real, name char(50));", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "create stream view v1 as select * from t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM sink s1 AS select * FROM v1 INTO embedded_callback with "
        "(batch_window_size = '50');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *streamName = "t1";
    const char *sinkName = "s1";
    ret = GmeStreamRegisterCallBack(conn, sinkName, StEmbStreamGetActQryResultGet4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t rowNum = 50;
    int64_t *id = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        id[i] = i + 1;
    }
    int64_t *time = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        time[i] = i + 10;
    }
    double *age = (double *)malloc(rowNum * sizeof(double));
    for (int64_t i = 0; i < rowNum; i++) {
        age[i] = i + 0.4;
    }
    uint8_t *buf = (uint8_t *)malloc(sizeof(RdStreamTable2DataT) * rowNum);
    uint8_t *tmpBuf = buf;
    if (id != NULL && time != NULL && age != NULL) {
        RdStreamTable2DataT *v1 = NULL;
        for (int64_t i = 0; i < rowNum; ++i) {
            v1 = (RdStreamTable2DataT *)tmpBuf;
            v1->id = id[i];
            v1->waterMark = time[i];
            v1->eventTime = age[i];
            (void)snprintf((char *)v1->name, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", id[i]);
            tmpBuf += sizeof(RdStreamTable2DataT);
        }
        ret = GmeStreamStructWrite(0, streamName, buf, sizeof(RdStreamTable2DataT) * rowNum, rowNum, &errMsg);
        AW_MACRO_EXPECT_EQ_INT(GMERR_UNEXPECTED_NULL_VALUE, ret);
    }
    AW_MACRO_EXPECT_EQ_INT(0, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(0, g_datacount);
    ret = GmeSqlExecute(conn, "drop stream sink s1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream view v1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream table t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(id);
    free(time);
    free(age);
    free(buf);
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}

// 030GmeStreamStructWrite接口第二个参数传0，其余参数正常
// 	false
TEST_F(embedded_batch_write_error, STREAM_044_030)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char *errMsg = NULL;
    ret = GmeSqlExecute(
        conn, "create stream table t1(id integer, time integer, age real, name char(50));", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "create stream view v1 as select * from t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM sink s1 AS select * FROM v1 INTO embedded_callback with "
        "(batch_window_size = '50');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *streamName = "t1";
    const char *sinkName = "s1";
    ret = GmeStreamRegisterCallBack(conn, sinkName, StEmbStreamGetActQryResultGet4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t rowNum = 50;
    int64_t *id = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        id[i] = i + 1;
    }
    int64_t *time = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        time[i] = i + 10;
    }
    double *age = (double *)malloc(rowNum * sizeof(double));
    for (int64_t i = 0; i < rowNum; i++) {
        age[i] = i + 0.4;
    }

    uint8_t *buf = (uint8_t *)malloc(sizeof(RdStreamTable2DataT) * rowNum);
    uint8_t *tmpBuf = buf;
    if (id != NULL && time != NULL && age != NULL) {
        RdStreamTable2DataT *v1 = NULL;
        for (int64_t i = 0; i < rowNum; ++i) {
            v1 = (RdStreamTable2DataT *)tmpBuf;
            v1->id = id[i];
            v1->waterMark = time[i];
            v1->eventTime = age[i];
            (void)snprintf((char *)v1->name, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", id[i]);
            tmpBuf += sizeof(RdStreamTable2DataT);
        }
        ret = GmeStreamStructWrite(conn, 0, buf, sizeof(RdStreamTable2DataT) * rowNum, rowNum, &errMsg);
        AW_MACRO_EXPECT_EQ_INT(GMERR_UNEXPECTED_NULL_VALUE, ret);
    }
    AW_MACRO_EXPECT_EQ_INT(0, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(0, g_datacount);
    ret = GmeSqlExecute(conn, "drop stream sink s1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream view v1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream table t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(id);
    free(time);
    free(age);
    free(buf);
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}

// 031GmeStreamStructWrite接口第三个参数传0，其余参数正常
// 	false
TEST_F(embedded_batch_write_error, STREAM_044_031)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char *errMsg = NULL;
    ret = GmeSqlExecute(
        conn, "create stream table t1(id integer, time integer, age real, name char(50));", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "create stream view v1 as select * from t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM sink s1 AS select * FROM v1 INTO embedded_callback with "
        "(batch_window_size = '50');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *streamName = "t1";
    const char *sinkName = "s1";
    ret = GmeStreamRegisterCallBack(conn, sinkName, StEmbStreamGetActQryResultGet4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t rowNum = 50;
    int64_t *id = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        id[i] = i + 1;
    }
    int64_t *time = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        time[i] = i + 10;
    }
    double *age = (double *)malloc(rowNum * sizeof(double));
    for (int64_t i = 0; i < rowNum; i++) {
        age[i] = i + 0.4;
    }
    uint8_t *buf = (uint8_t *)malloc(sizeof(RdStreamTable2DataT) * rowNum);
    uint8_t *tmpBuf = buf;
    if (id != NULL && time != NULL && age != NULL) {
        RdStreamTable2DataT *v1 = NULL;
        for (int64_t i = 0; i < rowNum; ++i) {
            v1 = (RdStreamTable2DataT *)tmpBuf;
            v1->id = id[i];
            v1->waterMark = time[i];
            v1->eventTime = age[i];
            (void)snprintf((char *)v1->name, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", id[i]);
            tmpBuf += sizeof(RdStreamTable2DataT);
        }
        ret = GmeStreamStructWrite(conn, streamName, 0, sizeof(RdStreamTable2DataT) * rowNum, rowNum, &errMsg);
        AW_MACRO_EXPECT_EQ_INT(GMERR_UNEXPECTED_NULL_VALUE, ret);
    }
    AW_MACRO_EXPECT_EQ_INT(0, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(0, g_datacount);
    ret = GmeSqlExecute(conn, "drop stream sink s1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream view v1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream table t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(id);
    free(time);
    free(age);
    free(buf);
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}

// 032GmeStreamStructWrite接口第四个参数传0，其余参数正常
// false
TEST_F(embedded_batch_write_error, STREAM_044_032)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char *errMsg = NULL;
    ret = GmeSqlExecute(
        conn, "create stream table t1(id integer, time integer, age real, name char(50));", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "create stream view v1 as select * from t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM sink s1 AS select * FROM v1 INTO embedded_callback with "
        "(batch_window_size = '50');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *streamName = "t1";
    const char *sinkName = "s1";
    ret = GmeStreamRegisterCallBack(conn, sinkName, StEmbStreamGetActQryResultGet4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t rowNum = 50;
    int64_t *id = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        id[i] = i + 1;
    }
    int64_t *time = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        time[i] = i + 10;
    }
    double *age = (double *)malloc(rowNum * sizeof(double));
    for (int64_t i = 0; i < rowNum; i++) {
        age[i] = i + 0.4;
    }
    uint8_t *buf = (uint8_t *)malloc(sizeof(RdStreamTable2DataT) * rowNum);
    uint8_t *tmpBuf = buf;
    if (id != NULL && time != NULL && age != NULL) {
        RdStreamTable2DataT *v1 = NULL;
        for (int64_t i = 0; i < rowNum; ++i) {
            v1 = (RdStreamTable2DataT *)tmpBuf;
            v1->id = id[i];
            v1->waterMark = time[i];
            v1->eventTime = age[i];
            (void)snprintf((char *)v1->name, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", id[i]);
            tmpBuf += sizeof(RdStreamTable2DataT);
        }
        ret = GmeStreamStructWrite(conn, streamName, buf, 0, rowNum, &errMsg);
        AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    }
    AW_MACRO_EXPECT_EQ_INT(0, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(0, g_datacount);
    ret = GmeSqlExecute(conn, "drop stream sink s1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream view v1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream table t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(id);
    free(time);
    free(age);
    free(buf);
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}

// 033GmeStreamStructWrite接口第五个参数传0，其余参数正常
// 	false
TEST_F(embedded_batch_write_error, STREAM_044_033)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char *errMsg = NULL;
    ret = GmeSqlExecute(
        conn, "create stream table t1(id integer, time integer, age real, name char(50));", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "create stream view v1 as select * from t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM sink s1 AS select * FROM v1 INTO embedded_callback with "
        "(batch_window_size = '50');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *streamName = "t1";
    const char *sinkName = "s1";
    ret = GmeStreamRegisterCallBack(conn, sinkName, StEmbStreamGetActQryResultGet4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t rowNum = 50;
    int64_t *id = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        id[i] = i + 1;
    }
    int64_t *time = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        time[i] = i + 10;
    }
    double *age = (double *)malloc(rowNum * sizeof(double));
    for (int64_t i = 0; i < rowNum; i++) {
        age[i] = i + 0.4;
    }
    uint8_t *buf = (uint8_t *)malloc(sizeof(RdStreamTable2DataT) * rowNum);
    uint8_t *tmpBuf = buf;
    if (id != NULL && time != NULL && age != NULL) {
        RdStreamTable2DataT *v1 = NULL;
        for (int64_t i = 0; i < rowNum; ++i) {
            v1 = (RdStreamTable2DataT *)tmpBuf;
            v1->id = id[i];
            v1->waterMark = time[i];
            v1->eventTime = age[i];
            (void)snprintf((char *)v1->name, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", id[i]);
            tmpBuf += sizeof(RdStreamTable2DataT);
        }
        ret = GmeStreamStructWrite(conn, streamName, buf, sizeof(RdStreamTable2DataT) * rowNum, 0, &errMsg);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_MACRO_EXPECT_EQ_INT(0, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(0, g_datacount);
    ret = GmeSqlExecute(conn, "drop stream sink s1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream view v1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream table t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(id);
    free(time);
    free(age);
    free(buf);
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}

// 034GmeStreamStructWrite接口第六个参数传0，其余参数正常
// false
TEST_F(embedded_batch_write_error, STREAM_044_034)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    char *errMsg = NULL;
    ret = GmeSqlExecute(
        conn, "create stream table t1(id integer, time integer, age real, name char(50));", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "create stream view v1 as select * from t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn,
        "CREATE STREAM sink s1 AS select * FROM v1 INTO embedded_callback with "
        "(batch_window_size = '50');",
        NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *streamName = "t1";
    const char *sinkName = "s1";
    ret = GmeStreamRegisterCallBack(conn, sinkName, StEmbStreamGetActQryResultGet4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t rowNum = 50;
    int64_t batchNum = 1;
    int64_t *id = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        id[i] = i + 1;
    }
    int64_t *time = (int64_t *)malloc(rowNum * sizeof(int64_t));
    for (int64_t i = 0; i < rowNum; i++) {
        time[i] = i + 10;
    }
    double *age = (double *)malloc(rowNum * sizeof(double));
    for (int64_t i = 0; i < rowNum; i++) {
        age[i] = i + 0.4;
    }
    uint8_t *buf = (uint8_t *)malloc(sizeof(RdStreamTable2DataT) * rowNum);
    uint8_t *tmpBuf = buf;
    if (id != NULL && time != NULL && age != NULL) {
        RdStreamTable2DataT *v1 = NULL;
        for (int64_t i = 0; i < rowNum; ++i) {
            v1 = (RdStreamTable2DataT *)tmpBuf;
            v1->id = id[i];
            v1->waterMark = time[i];
            v1->eventTime = age[i];
            (void)snprintf((char *)v1->name, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", id[i]);
            tmpBuf += sizeof(RdStreamTable2DataT);
        }
        ret = GmeStreamStructWrite(conn, streamName, buf, sizeof(RdStreamTable2DataT) * rowNum, rowNum, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_MACRO_EXPECT_EQ_INT(batchNum, g_batchcount);
    AW_MACRO_EXPECT_EQ_INT(batchNum * rowNum, g_datacount);
    ret = GmeSqlExecute(conn, "drop stream sink s1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream view v1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmeSqlExecute(conn, "drop stream table t1;", NULL, NULL, &errMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(id);
    free(time);
    free(age);
    free(buf);
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    AW_FUN_Log(LOG_STEP, "start end.");
}
