/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: 嵌入式环境下流计算支持批量结构化写
 * Author: moxiaotong
 * Create: 2025-07-01
 */

#ifndef TEXT_UTIL_H
#define TEXT_UTIL_H
#include <string>
#include <iostream>
#include <vector>
#include <algorithm>
#include "t_rd_stream_emb.h"
using namespace std;
#define MAX_CMD_SIZE 1024
#define RD_STREAM_TABLE_T1_NAME_SIZE 50
#define RD_STREAM_TABLE_T1_NAME_SIZEF 100
#define EMB_STREAM_TABLE_T1_NAME_SIZE 50
#define EMB_STREAM_TABLE_FIX_FIELD_SIZE 50
#define EMB_STREAM_TABLE_CHAR_FIX_FIELD_SIZE 1024
#define MAX_LINE_LENGTH 256
GmeConnT *conn = NULL;
GmeConnT *unconn = NULL;
uint32_t g_Cnt = 0;

uint32_t g_batchcount = 0;
uint32_t g_datacount = 0;
uint32_t g_callbackValue = 1;
#pragma pack(1)

// 基本上都是用这个数据结构插入跟获取
struct RdStreamTable2DataT {
    int64_t id;
    int64_t waterMark;
    double eventTime;
    char name[RD_STREAM_TABLE_T1_NAME_SIZE];
};

// format获取数据的时候长度为1024
struct RdStreamTable3DataT {
    int64_t id;
    int64_t waterMark;
    double eventTime;
    char name[EMB_STREAM_TABLE_CHAR_FIX_FIELD_SIZE];
};

// 聚合
struct RdStreamTable4DataT {
    int64_t id;
    int64_t waterMark;
    double eventTime;
    char name[RD_STREAM_TABLE_T1_NAME_SIZE];
    int64_t sumNum;
};

#pragma pack()

int StEmbStreamGetActQryResultGet1(void *data, uint32_t tupleSize, uint8_t *colValues)
{
    RdStreamTable2DataT *result;
    result = (RdStreamTable2DataT *)colValues;
    int64_t rowNum = 5;
    int64_t expectId[rowNum] = {1, 2, 3, 4, 5};
    int64_t expectTime[rowNum] = {0, 1, 2, 3, 4};
    double expectAge[rowNum] = {6.2, 1.7, 8.4, 6.9, 3.0};
    char name[RD_STREAM_TABLE_T1_NAME_SIZE];  // 定长类型，使用定长数组，不需要单独定义长度
    for (uint32_t i = 0; i < tupleSize; ++i) {
        AW_MACRO_EXPECT_EQ_INT(expectId[i], result->id);
        AW_MACRO_EXPECT_EQ_INT(expectTime[i], result->waterMark);
        AW_MACRO_EXPECT_EQ_DOUBLE(expectAge[i], result->eventTime);
        char expectName[RD_STREAM_TABLE_T1_NAME_SIZE];
        (void)snprintf((char *)expectName, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", expectId[i]);
        AW_MACRO_EXPECT_EQ_STR(expectName, result->name);
        result++;
        g_datacount++;
    }
    g_batchcount++;
    return GMERR_OK;
}

int StEmbStreamGetActQryResultGet2(void *data, uint32_t tupleSize, uint8_t *colValues)
{
    RdStreamTable2DataT *result;
    result = (RdStreamTable2DataT *)colValues;
    for (uint32_t i = 0; i < tupleSize; ++i) {
        AW_MACRO_EXPECT_EQ_INT(1, result->id);
        AW_MACRO_EXPECT_EQ_INT(3, result->waterMark);
        AW_MACRO_EXPECT_EQ_DOUBLE(8.4, result->eventTime);
        char expectName[RD_STREAM_TABLE_T1_NAME_SIZE] = "name_1";
        AW_MACRO_EXPECT_EQ_STR(expectName, result->name);
        result++;
        g_datacount++;
    }
    g_batchcount++;
    return GMERR_OK;
}

int StEmbStreamGetActQryResultGet3(void *data, uint32_t tupleSize, uint8_t *colValues)
{
    RdStreamTable2DataT *result;
    result = (RdStreamTable2DataT *)colValues;
    for (uint32_t i = 0; i < tupleSize; ++i) {
        AW_MACRO_EXPECT_EQ_INT(11, result->id);
        AW_MACRO_EXPECT_EQ_INT(26, result->waterMark);
        AW_MACRO_EXPECT_EQ_DOUBLE(12.4, result->eventTime);
        char expectName[RD_STREAM_TABLE_T1_NAME_SIZE] = "name_11";
        AW_MACRO_EXPECT_EQ_STR(expectName, result->name);
        result++;
        g_datacount++;
    }
    g_batchcount++;
    return GMERR_OK;
}

int StEmbStreamGetActQryResultGet4(void *data, uint32_t tupleSize, uint8_t *colValues)
{
    RdStreamTable2DataT *result;
    result = (RdStreamTable2DataT *)colValues;
    for (uint32_t i = 0; i < tupleSize; ++i) {
        AW_MACRO_EXPECT_EQ_INT(i + 1, result->id);
        AW_MACRO_EXPECT_EQ_INT(i + 10, result->waterMark);
        AW_MACRO_EXPECT_EQ_DOUBLE(i + 0.4, result->eventTime);
        char expectName[RD_STREAM_TABLE_T1_NAME_SIZE];
        (void)snprintf((char *)expectName, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", i + 1);
        AW_MACRO_EXPECT_EQ_STR(expectName, result->name);
        result++;
        g_datacount++;
    }
    g_batchcount++;
    return GMERR_OK;
}

// 插入数据大于batch批处理窗口
int StEmbStreamGetActQryResultGet5(void *data, uint32_t tupleSize, uint8_t *colValues)
{
    RdStreamTable2DataT *result;
    result = (RdStreamTable2DataT *)colValues;
    for (uint32_t i = 0; i < tupleSize; ++i) {
        AW_MACRO_EXPECT_EQ_INT(g_datacount + 1, result->id);
        AW_MACRO_EXPECT_EQ_INT(g_datacount + 10, result->waterMark);
        AW_MACRO_EXPECT_EQ_DOUBLE(g_datacount + 0.4, result->eventTime);
        char expectName[RD_STREAM_TABLE_T1_NAME_SIZE];
        (void)snprintf((char *)expectName, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", g_datacount + 1);
        AW_MACRO_EXPECT_EQ_STR(expectName, result->name);
        result++;
        g_datacount++;
    }
    g_batchcount++;
    return GMERR_OK;
}

int StEmbStreamGetActQryResultGet6(void *data, uint32_t tupleSize, uint8_t *colValues)
{
    RdStreamTable2DataT *result;
    result = (RdStreamTable2DataT *)colValues;
    for (uint32_t i = 0; i < tupleSize; ++i) {
        AW_MACRO_EXPECT_EQ_INT(-1, result->id);
        AW_MACRO_EXPECT_EQ_INT(-30, result->waterMark);
        AW_MACRO_EXPECT_EQ_DOUBLE(-2.4, result->eventTime);
        char expectName[RD_STREAM_TABLE_T1_NAME_SIZE] = "name_-1";
        AW_MACRO_EXPECT_EQ_STR(expectName, result->name);
        result++;
        g_datacount++;
    }
    g_batchcount++;
    return GMERR_OK;
}

int StEmbStreamGetActQryResultGet7(void *data, uint32_t tupleSize, uint8_t *colValues)
{
    RdStreamTable2DataT *result;
    result = (RdStreamTable2DataT *)colValues;
    for (uint32_t i = 0; i < tupleSize; ++i) {
        AW_MACRO_EXPECT_EQ_INT(43, result->id);
        AW_MACRO_EXPECT_EQ_INT(i + 10, result->waterMark);
        AW_MACRO_EXPECT_EQ_DOUBLE(double(3.1415926), result->eventTime);
        char expectName[RD_STREAM_TABLE_T1_NAME_SIZE];
        (void)snprintf((char *)expectName, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", i + 1);
        AW_MACRO_EXPECT_EQ_STR(expectName, result->name);
        result++;
        g_datacount++;
    }
    g_batchcount++;
    return GMERR_OK;
}

int StEmbStreamGetActQryResultGet8(void *data, uint32_t tupleSize, uint8_t *colValues)
{
    RdStreamTable2DataT *result;
    result = (RdStreamTable2DataT *)colValues;
    for (uint32_t i = 0; i < tupleSize; ++i) {
        AW_MACRO_EXPECT_EQ_INT((i + 1) % 2, result->id);
        AW_MACRO_EXPECT_EQ_INT(i + 10, result->waterMark);
        AW_MACRO_EXPECT_EQ_DOUBLE(i + 0.4 + 3.35, result->eventTime);
        char expectName[RD_STREAM_TABLE_T1_NAME_SIZE];
        (void)snprintf((char *)expectName, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", i + 1);
        AW_MACRO_EXPECT_EQ_STR(expectName, result->name);
        result++;
        g_datacount++;
    }
    g_batchcount++;
    return GMERR_OK;
}

// 插入数据大于batch批处理窗口
int StEmbStreamGetActQryResultGet9(void *data, uint32_t tupleSize, uint8_t *colValues)
{
    RdStreamTable2DataT *result;
    result = (RdStreamTable2DataT *)colValues;
    for (uint32_t i = 0; i < tupleSize; ++i) {
        AW_MACRO_EXPECT_EQ_INT(g_datacount + 1, result->id);
        AW_MACRO_EXPECT_EQ_INT(1, result->waterMark);
        AW_MACRO_EXPECT_EQ_DOUBLE(g_datacount + 0.4, result->eventTime);
        char expectName[RD_STREAM_TABLE_T1_NAME_SIZE];
        (void)snprintf((char *)expectName, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", g_datacount + 1);
        AW_MACRO_EXPECT_EQ_STR(expectName, result->name);
        result++;
        g_datacount++;
    }
    g_batchcount++;
    return GMERR_OK;
}

// format
int StEmbStreamGetActQryResultGet10(void *data, uint32_t tupleSize, uint8_t *colValues)
{
    RdStreamTable3DataT *result;
    result = (RdStreamTable3DataT *)colValues;
    for (uint32_t i = 0; i < tupleSize; ++i) {
        AW_MACRO_EXPECT_EQ_INT(i + 1, result->id);
        AW_MACRO_EXPECT_EQ_INT(i + 10, result->waterMark);
        AW_MACRO_EXPECT_EQ_DOUBLE(i + 0.4, result->eventTime);
        char expectName[EMB_STREAM_TABLE_CHAR_FIX_FIELD_SIZE];
        (void)snprintf((char *)expectName, EMB_STREAM_TABLE_CHAR_FIX_FIELD_SIZE, "this id is %d", i + 1);
        AW_MACRO_EXPECT_EQ_STR(expectName, result->name);
        result++;
        g_datacount++;
    }
    g_batchcount++;
    return GMERR_OK;
}

// dispatch by
int StEmbStreamGetActQryResultGet11(void *data, uint32_t tupleSize, uint8_t *colValues)
{
    RdStreamTable2DataT *result;
    result = (RdStreamTable2DataT *)colValues;
    for (uint32_t i = 0; i < tupleSize; ++i) {
        AW_MACRO_EXPECT_EQ_INT(9 + 1, result->id);
        AW_MACRO_EXPECT_EQ_INT(9 + 10, result->waterMark);
        AW_MACRO_EXPECT_EQ_DOUBLE(9 + 0.4, result->eventTime);
        char expectName[RD_STREAM_TABLE_T1_NAME_SIZE];
        (void)snprintf((char *)expectName, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", 9 + 1);
        AW_MACRO_EXPECT_EQ_STR(expectName, result->name);
        result++;
        g_datacount++;
    }
    g_batchcount++;
    return GMERR_OK;
}

// 插入数据大于batch批处理窗口，and or
int StEmbStreamGetActQryResultGet12(void *data, uint32_t tupleSize, uint8_t *colValues)
{
    RdStreamTable2DataT *result;
    result = (RdStreamTable2DataT *)colValues;
    for (uint32_t i = 0; i < tupleSize; ++i) {
        AW_MACRO_EXPECT_EQ_INT(g_datacount + 1 + 10, result->id);
        AW_MACRO_EXPECT_EQ_INT(g_datacount + 10 + 10, result->waterMark);
        AW_MACRO_EXPECT_EQ_DOUBLE(g_datacount + 0.4 + 10, result->eventTime);
        char expectName[RD_STREAM_TABLE_T1_NAME_SIZE];
        (void)snprintf((char *)expectName, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", g_datacount + 1 + 10);
        AW_MACRO_EXPECT_EQ_STR(expectName, result->name);
        result++;
        g_datacount++;
    }
    g_batchcount++;
    return GMERR_OK;
}

int StEmbStreamGetActQryResultGet13(void *data, uint32_t tupleSize, uint8_t *colValues)
{
    RdStreamTable4DataT *result;
    result = (RdStreamTable4DataT *)colValues;
    for (uint32_t i = 0; i < tupleSize; ++i) {
        AW_MACRO_EXPECT_EQ_INT(g_datacount + 10, result->id);
        AW_MACRO_EXPECT_EQ_INT(g_datacount, result->waterMark);
        AW_MACRO_EXPECT_EQ_DOUBLE(g_datacount + 0.4, result->eventTime);
        char expectName[RD_STREAM_TABLE_T1_NAME_SIZE];
        (void)snprintf((char *)expectName, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", g_datacount + 10);
        AW_MACRO_EXPECT_EQ_STR(expectName, result->name);
        AW_MACRO_EXPECT_EQ_INT(60, result->sumNum);
        result++;
        g_datacount++;
    }
    g_batchcount++;
    return GMERR_OK;
}

// 批量插入数据，四个值，传id，time，age列表，两个int类型，一个double类型，一个char类型字符串
static void RdBatchWriteStreamTable(
    const char *tableName, int64_t rowNum, int64_t id[], int64_t eventTime[], double age[])
{
    uint32_t ret;
    char *errMsg = NULL;
    uint8_t *buf = (uint8_t *)malloc(sizeof(RdStreamTable2DataT) * rowNum);
    if (buf != NULL) {
        uint8_t *tmpBuf = buf;
        RdStreamTable2DataT *v1 = NULL;
        for (int64_t i = 0; i < rowNum; ++i) {
            v1 = (RdStreamTable2DataT *)tmpBuf;
            v1->id = id[i];
            v1->waterMark = eventTime[i];
            v1->eventTime = age[i];
            (void)snprintf((char *)v1->name, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", id[i]);
            tmpBuf += sizeof(RdStreamTable2DataT);
        }
        ret = GmeStreamStructWrite(conn, tableName, buf, sizeof(RdStreamTable2DataT) * rowNum, rowNum, &errMsg);
        if (rowNum <= 1024) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
        }
    } else {
        AW_FUN_Log(LOG_DEBUG, "buf malloc failed !");
    }
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    free(buf);
}

// 批量插入数据，四个值，传id，time，age列表，两个int类型，一个double类型，一个char类型字符串，over聚合
static void RdBatchWriteStreamTable2(
    const char *tableName, int64_t rowNum, int64_t id[], int64_t eventTime[], double age[], int64_t count)
{
    uint32_t ret;
    char *errMsg = NULL;
    RdStreamTable2DataT *v1 = (RdStreamTable2DataT *)malloc(sizeof(RdStreamTable2DataT) * rowNum);
    if (v1 != NULL) {
        v1->id = id[count];
        v1->waterMark = eventTime[count];
        v1->eventTime = age[count];
        (void)snprintf((char *)v1->name, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", id[count]);
        ret = GmeStreamStructWrite(conn, tableName, v1, sizeof(RdStreamTable2DataT) * rowNum, rowNum, &errMsg);
        if (rowNum <= 1024) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
        }
    } else {
        AW_FUN_Log(LOG_DEBUG, "v1 malloc failed !");
    }
    if (errMsg != NULL) {
        errMsg = NULL;
    }
    free(v1);
}

#endif /* end of TEXT_UTIL_H */
