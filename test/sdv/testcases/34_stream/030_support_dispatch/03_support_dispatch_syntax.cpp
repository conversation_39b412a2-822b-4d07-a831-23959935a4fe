#include "gtest/gtest.h"
#include "securec.h"
#include "gmc_sql.h"
#include "t_rd_sn.h"
#include "rd_feature_stream.h"
#include "rd_feature_ts.h"

#include "dispatch_util.h"


class SupportDispatch : public testing::Test {
public:
    static GmcConnT *conn;
    static GmcStmtT *stmt;

    bool hasFinishTest = false;
    void RdFinishTest();

    static void SetUpTestCase();
    static void TearDownTestCase();
    virtual void SetUp();
    virtual void TearDown();
};

GmcConnT *SupportDispatch::conn = NULL;
GmcStmtT *SupportDispatch::stmt = NULL;

void SupportDispatch::SetUpTestCase()
{
    int32_t ret;
    ret = RdStreamEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void SupportDispatch::TearDownTestCase()
{
    int32_t ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = RdStreamEnvClean();
    EXPECT_EQ(GMERR_OK, ret);
}

void SupportDispatch::SetUp()
{   
    // 创建客户端连接
    int32_t ret = RdGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void SupportDispatch::TearDown()
{
    int32_t ret = RdGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// Dispatch By在流表ddl中括号的里面 预期：失败
TEST_F(SupportDispatch, STREAM_030_SYNTAX_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    char *create = (char *)"create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
                           "watermark for time as time - interval '10' seconds strict DISPATCH BY id,time);";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);

    create = (char *)"create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
                     "DISPATCH BY id,time watermark for time as time - interval '10' seconds strict);";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);

    create = (char *)"create stream table t1 (id integer, name text, time integer, age integer, address char(50) DISPATCH BY id, time);";
    ret = GmcExecDirect(stmt, create, strlen(create));
    ASSERT_EQ(GMERR_SYNTAX_ERROR, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// Dispatch By在视图/sink结点ddl中with子句的后面 预期：失败
TEST_F(SupportDispatch, STREAM_030_SYNTAX_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50)) "
            "DISPATCH BY id,time;"
        },
        {
            "create stream table t2 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time - interval '10' seconds strict) "
            "DISPATCH BY id,time;"
        }, 
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    char *create = (char *) "create stream view v1 as select * from table(dispatch(table t1, 1, 2)) "
                            "with (tuple_buffer_size = 1) "
                            "DISPATCH BY id, time;";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);

    create = (char *) "create stream sink s1 as select * from table(dispatch(table t1, 1, 1)) "
                      "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1') "
                      "DISPATCH BY id, time;";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);

    create = (char *) "create stream view v1 as select id, name, time from "
                      "table(hop(table t1, time, interval '20' seconds, interval '10' seconds)) "
                      "with (tuple_buffer_size = 1) "
                      "DISPATCH BY id,name,time,address;";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);
    
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream table t1;"},
        {"drop stream table t2;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// Dispatch By在视图/sink结点ddl中where子句的前面 预期：失败
TEST_F(SupportDispatch, STREAM_030_SYNTAX_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50)) "
            "DISPATCH BY id,time;"
        },
        {
            "create stream table t2 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time - interval '10' seconds strict) "
            "DISPATCH BY id,time;"
        }, 
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    char *create = (char *) "create stream view v1 as select * from table(dispatch(table t1, 1, 2)) "
                            "DISPATCH BY id, time "
                            "where id > 10"
                            "with (tuple_buffer_size = 1);";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);

    create = (char *) "create stream sink s1 as select * from table(dispatch(table t1, 1, 1)) "
                      "into tsdb(ts1) "
                      "DISPATCH BY id, time "
                      "where id > 10 "
                      "with (batch_window_size = '1', tuple_buffer_size = '1');";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);

    create = (char *) "create stream view v1 as select id, name, time from "
                      "table(hop(table t1, time, interval '20' seconds, interval '10' seconds)) "
                      "DISPATCH BY id,name,time,address "
                      "where id > 10 "
                      "with (tuple_buffer_size = 1)";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);
    
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream table t1;"},
        {"drop stream table t2;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 关键字Dispatch/by/table拼写错误 预期：失败
TEST_F(SupportDispatch, STREAM_030_SYNTAX_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50)) "
            "DISPATCH BY id,time;"
        },
        {
            "create stream table t2 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time - interval '10' seconds strict) "
            "DISPATCH BY id,time;"
        }, 
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // DISPATCH拼写错误
    char *create = (char *) "create stream view v1 as select * from table(dispatch(table t1, 1, 2)) "
                            "DISPATCHh BY id, time "
                            "with (tuple_buffer_size = 1);";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);

    // table拼写错误
    create = (char *) "create stream sink s1 as select * from table(dispatch(taable t1, 1, 1)) "
                      "into tsdb(ts1) "
                      "DISPATCH BY id, time "
                      "with (batch_window_size = '1', tuple_buffer_size = '1');";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);

    // BY拼写错误
    create = (char *) "create stream view v1 as select id, name, time from "
                      "table(hop(table t1, time, interval '20' seconds, interval '10' seconds)) "
                      "DISPATCH BYy id,name,time,address "
                      "with (tuple_buffer_size = 1)";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);
    
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream table t1;"},
        {"drop stream table t2;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// Dispatch By包含的字段名称中间逗号缺失 预期：失败
TEST_F(SupportDispatch, STREAM_030_SYNTAX_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50)) "
            "DISPATCH BY id,time;"
        },
        {
            "create stream table t2 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time - interval '10' seconds strict) "
            "DISPATCH BY id,time;"
        }, 
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    char *create = (char *) "create stream view v1 as select * from table(dispatch(table t1, 1, 2)) "
                            "DISPATCH BY id time "
                            "with (tuple_buffer_size = 1);";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);

    create = (char *) "create stream sink s1 as select * from table(dispatch(table t1 1 1)) "
                      "into tsdb(ts1) "
                      "DISPATCH BY id, time "
                      "with (batch_window_size = '1', tuple_buffer_size = '1');";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);

    create = (char *) "create stream sink s1 as select * from table(dispatch(table t1, 1 1)) "
                      "into tsdb(ts1) "
                      "DISPATCH BY id, time "
                      "where id > 10 "
                      "with (batch_window_size = '1', tuple_buffer_size = '1');";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);
    
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream table t1;"},
        {"drop stream table t2;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// FROM TABLE(DISPATCH(t1, 1, 18)) 中的括号缺失/冗余 预期：失败
TEST_F(SupportDispatch, STREAM_030_SYNTAX_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50)) "
            "DISPATCH BY id,time;"
        },
        {
            "create stream table t2 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time - interval '10' seconds strict) "
            "DISPATCH BY id,time;"
        }, 
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    char *create = (char *) "create stream view v1 as select * from table dispatch(table t1, 1, 2) "
                            "with (tuple_buffer_size = 1);";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);

    create = (char *) "create stream sink s1 as select * from table(dispatch table t1, 1, 1) "
                      "into tsdb(ts1) "
                      "with (batch_window_size = '1', tuple_buffer_size = '1');";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);

    create = (char *) "create stream sink s1 as select * from table dispatch table t1, 1, 1 "
                      "into tsdb(ts1) "
                      "with (batch_window_size = '1', tuple_buffer_size = '1');";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);

    create = (char *) "create stream sink s1 as select * from table dispatch(table t1, 1, 1)) "
                      "into tsdb(ts1) "
                      "with (batch_window_size = '1', tuple_buffer_size = '1');";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);

    create = (char *) "create stream sink s1 as select * from table(dispatch table t1, 1, 1)) "
                      "into tsdb(ts1) "
                      "with (batch_window_size = '1', tuple_buffer_size = '1');";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);

    create = (char *) "create stream sink s1 as select * from table(dispatch(table t1, 1, 1) "
                      "into tsdb(ts1) "
                      "with (batch_window_size = '1', tuple_buffer_size = '1');";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);

    create = (char *) "create stream sink s1 as select * from table((dispatch(table t1, 1, 1))) "
                      "into tsdb(ts1) "
                      "with (batch_window_size = '1', tuple_buffer_size = '1');";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);

    create = (char *) "create stream sink s1 as select * from table((dispatch((table t1, 1, 1)))) "
                      "into tsdb(ts1) "
                      "with (batch_window_size = '1', tuple_buffer_size = '1');";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);

    create = (char *) "create stream sink s1 as select * from table((dispatch((table t1, 1, 1)) "
                      "into tsdb(ts1) "
                      "with (batch_window_size = '1', tuple_buffer_size = '1');";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);

    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream table t1;"},
        {"drop stream table t2;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 关键字Dispatch/by/table缺失 预期：失败
TEST_F(SupportDispatch, STREAM_030_SYNTAX_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50)) "
            "DISPATCH BY id,time;"
        },
        {
            "create stream table t2 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time - interval '10' seconds strict) "
            "DISPATCH BY id,time;"
        }, 
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // table缺失
    char *create = (char *) "create stream view v1 as select * from (dispatch(table t1, 1, 2)) "
                            "DISPATCHh BY id, time "
                            "with (tuple_buffer_size = 1);";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);

    // table()缺失
    create = (char *) "create stream view v1 as select * from dispatch(table t1, 1, 2) "
                            "DISPATCHh BY id, time "
                            "with (tuple_buffer_size = 1);";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);

    // dispatch缺失
    create = (char *) "create stream sink s1 as select * from table((table t1, 1, 1)) "
                      "into tsdb(ts1) "
                      "DISPATCH BY id, time "
                      "with (batch_window_size = '1', tuple_buffer_size = '1');";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);

    // dispatch()缺失
    create = (char *) "create stream sink s1 as select * from table(table t1, 1, 1) "
                      "into tsdb(ts1) "
                      "DISPATCH BY id, time "
                      "with (batch_window_size = '1', tuple_buffer_size = '1');";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);

    // table(dispatch())缺失
    create = (char *) "create stream sink s1 as select * from table t1, 1, 1 "
                      "into tsdb(ts1) "
                      "DISPATCH BY id, time "
                      "with (batch_window_size = '1', tuple_buffer_size = '1');";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);

    // 第二个table缺失
    create = (char *) "create stream sink s1 as select * from table(t1, 1, 1) "
                      "into tsdb(ts1) "
                      "DISPATCH BY id, time "
                      "with (batch_window_size = '1', tuple_buffer_size = '1');";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);

    // DISPATCH BY 的DISPATCH缺失
    create = (char *) "create stream sink s1 as select * from t1 "
                      "into tsdb(ts1) "
                      "BY id, time "
                      "with (batch_window_size = '1', tuple_buffer_size = '1');";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);

    // DISPATCH BY 的DISPATCH缺失
    create = (char *) "create stream sink s1 as select * from t1 "
                      "into tsdb(ts1) "
                      "DISPATCH id, time "
                      "with (batch_window_size = '1', tuple_buffer_size = '1');";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);

    // DISPATCH BY 的DISPATCH BY缺失
    create = (char *) "create stream sink s1 as select * from t1 "
                      "into tsdb(ts1) "
                      "id, time "
                      "with (batch_window_size = '1', tuple_buffer_size = '1');";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);
    
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream table t1;"},
        {"drop stream table t2;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 字符串类型字段的值引号缺失 预期：失败
TEST_F(SupportDispatch, STREAM_030_SYNTAX_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50)) "
            "DISPATCH BY id,name,address;"
        },
        {
            "create stream table t2 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time - interval '10' seconds strict) "
            "DISPATCH BY id,name,address;"
        }, 
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    char *create = (char *) "create stream view v1 as select * from table(dispatch(table t1, 1, name, 'address')) "
                            "with (tuple_buffer_size = 1);";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);

    create = (char *) "create stream sink s1 as select * from table(dispatch(table t1, , 'name', address)) "
                      "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret); 
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream table t1;"},
        {"drop stream table t2;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 关键字大小写混合 预期：成功
TEST_F(SupportDispatch, STREAM_030_SYNTAX_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id,time;"
        },
        {
            "create stream view v1 as select * from table(Dispatch(taBle t1, 1, 2)) "
            "DisPaTcH By id,time "
            "with (tuple_buffer_size = 1);"
        }, 
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}









