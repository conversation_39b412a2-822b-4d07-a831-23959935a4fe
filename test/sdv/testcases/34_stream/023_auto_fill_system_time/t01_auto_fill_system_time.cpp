/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: 支持默认填充当前时间 功能测试
 * Author: yushijin
 * Create: 2024-11-25
 */
#include "gtest/gtest.h"
#include "securec.h"
#include "gmc_sql.h"
#include "gmc_persist.h"
#include "rd_feature_stream.h"
#include "auto_fill_system_time.h"
#include <sys/time.h>

#define STREAM_REFERENCE_MAX_NAME_LEN 256
#define STREAM_REFERENCE_MAX_SQL_LEN 512

class t01_auto_fill_system_time : public testing::Test {
public:
    static GmcConnT *conn;
    static GmcStmtT *stmt;

    static void SetUpTestCase();
    static void TearDownTestCase();
    virtual void SetUp();
    virtual void TearDown();
};

GmcConnT *t01_auto_fill_system_time::conn = NULL;
GmcStmtT *t01_auto_fill_system_time::stmt = NULL;

void t01_auto_fill_system_time::SetUpTestCase()
{
    int32_t ret;
    ret = RdStreamEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void t01_auto_fill_system_time::TearDownTestCase()
{
    int32_t ret = RdStreamEnvClean();
    EXPECT_EQ(GMERR_OK, ret);
}

void t01_auto_fill_system_time::SetUp()
{
    int32_t ret = RdGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void t01_auto_fill_system_time::TearDown()
{
    int32_t ret = RdGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 创建包含默认填充当前时间的流表，写入数据，并校验
TEST_F(t01_auto_fill_system_time, STREAM_023_001)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), water_mark integer, event_time integer, system_time integer) "
         "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour',  COMPRESSION = 'no');"},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer DEFAULT 1, event_time integer, "
         "system_time integer DEFAULT current_time_second());"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, water_mark, event_time, system_time FROM stream1;"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, water_mark, event_time, system_time "
         "FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 5;
    int64_t id[rowNum] = {0, 1, 2, 3, 4};
    int64_t event_time[rowNum] = {0, 1, 2, 3, 4};
    bool nullInfo[colNum] = {false, false, true, false, true};
    struct timeval tv1;
    gettimeofday(&tv1, NULL);
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, event_time, nullInfo);
    struct timeval tv2;
    gettimeofday(&tv2, NULL);
    // 校验ts表中数据
    char *selectTsName = (char *)"select * from ts1 order by id;";
    uint32_t expectRowsCount = 5;
    uint32_t expectColsCount = 5;
    RdCheckDataInTSTableOfStreamTable1(
        stmt, selectTsName, expectRowsCount, expectColsCount, id, 1, event_time, tv1.tv_sec, tv2.tv_sec);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// (int, int)表，int字段设置默认填充当前时间
TEST_F(t01_auto_fill_system_time, STREAM_023_002)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (event_time integer, system_time integer) "
         "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour',  COMPRESSION = 'no');"},
        {"CREATE STREAM TABLE stream1 (event_time integer, system_time integer DEFAULT current_time_second());"},
        {"CREATE STREAM VIEW view1 AS SELECT event_time, system_time FROM stream1;"},
        {"CREATE STREAM SINK sink1 AS SELECT event_time, system_time "
         "FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 10;
    uint32_t colNum = 2;
    int64_t event_time[rowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    bool nullInfo[colNum] = {false, true};
    struct timeval tv1;
    gettimeofday(&tv1, NULL);
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, event_time, nullInfo);
    struct timeval tv2;
    gettimeofday(&tv2, NULL);
    // 校验ts表中数据
    char *selectTsName = (char *)"select * from ts1 order by event_time;";
    uint32_t expectRowsCount = 10;
    uint32_t expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable2(
        stmt, selectTsName, expectRowsCount, expectColsCount, event_time, tv1.tv_sec, tv2.tv_sec);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// (char, int)表，int字段设置默认填充当前时间
TEST_F(t01_auto_fill_system_time, STREAM_023_003)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (name char(50), system_time integer) "
         "WITH (TIME_COL = 'system_time', INTERVAL= '1 hour',  COMPRESSION = 'no');"},
        {"CREATE STREAM TABLE stream1 (name char(50), system_time integer DEFAULT current_time_second());"},
        {"CREATE STREAM VIEW view1 AS SELECT name, system_time FROM stream1;"},
        {"CREATE STREAM SINK sink1 AS SELECT name, system_time "
         "FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 10;
    uint32_t colNum = 2;
    bool nullInfo[colNum] = {false, true};
    struct timeval tv1;
    gettimeofday(&tv1, NULL);
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, nullInfo);
    struct timeval tv2;
    gettimeofday(&tv2, NULL);
    // 校验ts表中数据
    char *selectTsName = (char *)"select * from ts1 order by name;";
    uint32_t expectRowsCount = 10;
    uint32_t expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable3(stmt, selectTsName, expectRowsCount, expectColsCount, tv1.tv_sec, tv2.tv_sec);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 多个int字段均设置默认填充当前时间
TEST_F(t01_auto_fill_system_time, STREAM_023_004)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), event_time integer, "
         "system_time1 integer, system_time2 integer, system_time3 integer) "
         "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour',  COMPRESSION = 'no');"},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), event_time integer, "
         "system_time1 integer DEFAULT current_time_second(), "
         "system_time2 integer DEFAULT current_time_second(), "
         "system_time3 integer DEFAULT current_time_second());"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, event_time, "
         "system_time1, system_time2, system_time3 FROM stream1;"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, event_time, "
         "system_time1, system_time2, system_time3 "
         "FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 10;
    uint32_t colNum = 6;
    int64_t id[rowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    int64_t event_time[rowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    bool nullInfo[colNum] = {false, false, false, true, true, true};
    struct timeval tv1;
    gettimeofday(&tv1, NULL);
    RdStructWriteStreamTable2(stmt, vertexLabel, rowNum, id, event_time, nullInfo);
    struct timeval tv2;
    gettimeofday(&tv2, NULL);
    // 校验ts表中数据
    char *selectTsName = (char *)"select * from ts1 order by id;";
    uint32_t expectRowNum = 10;
    uint32_t expectColNum = 6;
    DATATYPE dt[expectColNum] = {INTEGER, FIXED, INTEGER, INTEGER, INTEGER, INTEGER};
    RdCheckDataInTSTableOfStreamTable4(
        stmt, selectTsName, expectRowNum, expectColNum, dt, id, event_time, tv1.tv_sec, tv2.tv_sec);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// (char, int)表，char字段设置默认填充hostname，int字段设置默认填充当前时间
TEST_F(t01_auto_fill_system_time, STREAM_023_005)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), hostname char(50), event_time integer, system_time integer) "
         "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour',  COMPRESSION = 'no');"},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), "
         "hostname char(50) DEFAULT get_hostname(), "
         "event_time integer, "
         "system_time integer DEFAULT current_time_second());"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, hostname, event_time, system_time FROM stream1;"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, hostname, event_time, system_time "
         "FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 5;
    int64_t id[rowNum] = {0, 1, 2, 3, 4};
    int64_t event_time[rowNum] = {0, 1, 2, 3, 4};
    bool nullInfo[colNum] = {false, false, true, false, true};
    struct timeval tv1;
    gettimeofday(&tv1, NULL);
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, event_time, nullInfo);
    struct timeval tv2;
    gettimeofday(&tv2, NULL);
    // 校验ts表中数据
    char *selectTsName = (char *)"select * from ts1 order by id;";
    uint32_t expectRowsCount = 5;
    uint32_t expectColsCount = 5;
    RdCheckDataInTSTableOfStreamTable5(
        stmt, selectTsName, expectRowsCount, expectColsCount, id, event_time, tv1.tv_sec, tv2.tv_sec);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// (char, text, int)表，char/text字段设置默认填充hostname，int字段设置默认填充当前时间
TEST_F(t01_auto_fill_system_time, STREAM_023_006)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, event_time integer, hostname1 char(50), hostname2 text, system_time integer) "
         "WITH (TIME_COL = 'id', INTERVAL= '1 hour',  COMPRESSION = 'no');"},
        {"CREATE STREAM TABLE stream1 (id integer, event_time integer, "
         "hostname1 char(50) DEFAULT get_hostname(), "
         "hostname2 text DEFAULT get_hostname(), "
         "system_time integer DEFAULT current_time_second());"},
        {"CREATE STREAM VIEW view1 AS SELECT id, event_time, hostname1, hostname2, system_time FROM stream1;"},
        {"CREATE STREAM SINK sink1 AS SELECT id, event_time, hostname1, hostname2, system_time "
         "FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 5;
    int64_t id[rowNum] = {0, 1, 2, 3, 4};
    int64_t event_time[rowNum] = {0, 1, 2, 3, 4};
    bool nullInfo[colNum] = {false, false, true, true, true};
    struct timeval tv1;
    gettimeofday(&tv1, NULL);
    RdStructWriteStreamTable4(stmt, vertexLabel, rowNum, id, event_time, nullInfo);
    struct timeval tv2;
    gettimeofday(&tv2, NULL);
    // 校验ts表中数据
    char *selectTsName = (char *)"select * from ts1 order by id;";
    uint32_t expectRowsCount = 5;
    uint32_t expectColsCount = 5;
    RdCheckDataInTSTableOfStreamTable5(
        stmt, selectTsName, expectRowsCount, expectColsCount, id, event_time, tv1.tv_sec, tv2.tv_sec);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 流表包含默认填充当前时间列，view未映射该列
TEST_F(t01_auto_fill_system_time, STREAM_023_007)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), water_mark integer, event_time integer) "
         "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour',  COMPRESSION = 'no');"},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer DEFAULT 1, event_time integer, "
         "system_time integer DEFAULT current_time_second());"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, water_mark, event_time FROM stream1;"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, water_mark, event_time "
         "FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // get system time
    struct timeval tv;
    gettimeofday(&tv, NULL);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 10;
    uint32_t colNum = 5;
    int64_t id[rowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    int64_t event_time[rowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    bool nullInfo[colNum] = {false, false, true, false, true};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, event_time, nullInfo);

    // 校验ts表中数据
    char *selectTsName = (char *)"select * from ts1 order by id;";
    uint32_t expectRowsCount = 10;
    uint32_t expectColsCount = 4;
    RdCheckDataInTSTableOfStreamTable6(stmt, selectTsName, expectRowsCount, expectColsCount, id, 1, event_time); 

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 流表包含默认填充当前时间列，sink未映射该列
TEST_F(t01_auto_fill_system_time, STREAM_023_008)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), water_mark integer, event_time integer) "
         "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour',  COMPRESSION = 'no');"},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer DEFAULT 1, event_time integer, "
         "system_time integer DEFAULT current_time_second());"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, water_mark, event_time, system_time FROM stream1;"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, water_mark, event_time "
         "FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // get system time
    struct timeval tv;
    gettimeofday(&tv, NULL);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 10;
    uint32_t colNum = 5;
    int64_t id[rowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    int64_t event_time[rowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    bool nullInfo[colNum] = {false, false, true, false, true};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, event_time, nullInfo);

    // 校验ts表中数据
    char *selectTsName = (char *)"select * from ts1 order by id;";
    uint32_t expectRowsCount = 10;
    uint32_t expectColsCount = 4;
    RdCheckDataInTSTableOfStreamTable6(stmt, selectTsName, expectRowsCount, expectColsCount, id, 1, event_time); 

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 向默认填充当前时间的列写入数据并校验（数据应为写入数据）
TEST_F(t01_auto_fill_system_time, STREAM_023_009)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), event_time integer, "
         "system_time1 integer, system_time2 integer, system_time3 integer) "
         "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour',  COMPRESSION = 'no');"},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), event_time integer, "
         "system_time1 integer DEFAULT current_time_second(), "
         "system_time2 integer DEFAULT current_time_second(), "
         "system_time3 integer DEFAULT current_time_second());"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, event_time, "
         "system_time1, system_time2, system_time3 FROM stream1;"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, event_time, "
         "system_time1, system_time2, system_time3 "
         "FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 10;
    uint32_t colNum = 6;
    int64_t id[rowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    int64_t event_time[rowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    int64_t system_time1[rowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    bool nullInfo[colNum] = {false, false, false, false, true, true};
    struct timeval tv1;
    gettimeofday(&tv1, NULL);
    RdStructWriteStreamTable2(stmt, vertexLabel, rowNum, id, event_time, system_time1, nullInfo);
    struct timeval tv2;
    gettimeofday(&tv2, NULL);

    // 校验ts表中数据
    char *selectTsName = (char *)"select * from ts1 order by id;";
    uint32_t expectRowNum = 10;
    uint32_t expectColNum = 6;
    DATATYPE dt[expectColNum] = {INTEGER, FIXED, INTEGER, INTEGER, INTEGER, INTEGER};
    RdCheckDataInTSTableOfStreamTable4(
        stmt, selectTsName, expectRowNum, expectColNum, dt, id, event_time, system_time1, tv1.tv_sec, tv2.tv_sec);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 包含默认填充时间列的表，写入1w条数据
TEST_F(t01_auto_fill_system_time, STREAM_023_010)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), water_mark integer, event_time integer, system_time integer) "
         "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour',  COMPRESSION = 'no');"},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer DEFAULT 1, event_time integer, "
         "system_time integer DEFAULT current_time_second());"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, water_mark, event_time, system_time FROM stream1;"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, water_mark, event_time, system_time "
         "FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 10000;
    uint32_t colNum = 5;
    int64_t id[rowNum] = {0};
    int64_t water_mark = 1;
    int64_t event_time[rowNum] = {0};
    for (uint32_t i = 0; i < rowNum; ++i) {
        id[i] = i;
        event_time[i] = i;
    }
    bool nullInfo[colNum] = {false, false, true, false, true};
    struct timeval tv1;
    gettimeofday(&tv1, NULL);
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, event_time, nullInfo);
    struct timeval tv2;
    gettimeofday(&tv2, NULL);

    // 校验ts表中数据
    char *selectTsName = (char *)"select * from ts1 order by id;";
    DATATYPE dt[colNum] = {INTEGER, FIXED, INTEGER, INTEGER, INTEGER, INTEGER};
    RdCheckDataInTSTableOfStreamTable1(
        stmt, selectTsName, rowNum, colNum, id, water_mark, event_time, tv1.tv_sec, tv2.tv_sec);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 对包含默认填充当前时间列的表执行sum聚合函数计算
TEST_F(t01_auto_fill_system_time, STREAM_023_011)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50),  window_start integer, window_end integer, event_time integer, "
         "system_time integer, "
         "idSum integer) WITH (TIME_COL = 'event_time', INTERVAL= '1 hour',  COMPRESSION = 'no');"},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, system_time "
         "integer DEFAULT current_time_second(), "
         "WATERMARK FOR event_time AS event_time TOLERANT);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, system_time, "
         "sum(id) OVER(PARTITION BY window_start, window_end, water_mark) "
         "FROM TABLE(TUMBLE(TABLE stream1, event_time, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, system_time, "
         "sum_id FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 11;
    uint32_t colNum = 5;
    int64_t id[rowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11};
    int64_t water_mark[rowNum] = {1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11};
    int64_t event_time[rowNum] = {10, 10, 11, 11, 11, 20, 20, 25, 25, 25, 1000};
    bool nullInfo[colNum] = {false, false, false, false, true};
    struct timeval tv1;
    gettimeofday(&tv1, NULL);
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time, nullInfo);
    struct timeval tv2;
    gettimeofday(&tv2, NULL);

    // 校验ts表中数据
    char *selectTsName = (char *)"select * from ts1 order by window_end, id;";
    uint32_t expectRowsCount = 10;
    uint32_t expectColsCount = 7;
    int64_t mapping_id[expectRowsCount] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    int64_t mapping_window_start[expectRowsCount] = {10, 10, 10, 10, 10, 20, 20, 20, 20, 20};
    int64_t mapping_window_end[expectRowsCount] = {20, 20, 20, 20, 20, 30, 30, 30, 30, 30};
    int64_t mapping_event_time[expectRowsCount] = {10, 10, 11, 11, 11, 20, 20, 25, 25, 25};
    int64_t mapping_aggregate[expectRowsCount] = {3, 3, 7, 7, 5, 6, 15, 15, 19, 19};
    RdCheckDataInTSTableOfStreamTable7(stmt, selectTsName, expectRowsCount, expectColsCount, mapping_id,
        mapping_window_start, mapping_window_end, mapping_event_time, mapping_aggregate, tv1.tv_sec, tv2.tv_sec);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 对包含默认填充当前时间列的表执行count聚合函数计算
TEST_F(t01_auto_fill_system_time, STREAM_023_012)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50),  window_start integer, window_end integer, event_time integer, "
         "system_time integer, "
         "count_id integer) WITH (TIME_COL = 'event_time', INTERVAL= '1 hour',  COMPRESSION = 'no');"},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, system_time "
         "integer DEFAULT current_time_second(), "
         "WATERMARK FOR event_time AS event_time TOLERANT);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, system_time, "
         "count(id) OVER(PARTITION BY window_start, window_end, water_mark) "
         "FROM TABLE(TUMBLE(TABLE stream1, event_time, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, system_time, "
         "count_id FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 11;
    uint32_t colNum = 5;
    int64_t id[rowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11};
    int64_t water_mark[rowNum] = {1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11};
    int64_t event_time[rowNum] = {10, 10, 11, 11, 11, 20, 20, 25, 25, 25, 1000};
    bool nullInfo[colNum] = {false, false, false, false, true};
    struct timeval tv1;
    gettimeofday(&tv1, NULL);
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time, nullInfo);
    struct timeval tv2;
    gettimeofday(&tv2, NULL);

    // 校验ts表中数据
    char *sqlCmd = (char *)"select * from ts1 order by window_end, id;";
    uint32_t expRowNum = 10;
    uint32_t expectColsCount = 7;
    int64_t map_id[expRowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    int64_t map_window_start[expRowNum] = {10, 10, 10, 10, 10, 20, 20, 20, 20, 20};
    int64_t map_window_end[expRowNum] = {20, 20, 20, 20, 20, 30, 30, 30, 30, 30};
    int64_t map_event_time[expRowNum] = {10, 10, 11, 11, 11, 20, 20, 25, 25, 25};
    int64_t map_aggregate[expRowNum] = {2, 2, 2, 2, 1, 1, 2, 2, 2, 2};
    RdCheckDataInTSTableOfStreamTable7(stmt, sqlCmd, expRowNum, expectColsCount, map_id, map_window_start,
        map_window_end, map_event_time, map_aggregate, tv1.tv_sec, tv2.tv_sec);
    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 对包含默认填充当前时间列的表执行max聚合函数计算
TEST_F(t01_auto_fill_system_time, STREAM_023_013)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50),  window_start integer, window_end integer, event_time integer, "
         "system_time integer, "
         "max_id integer) WITH (TIME_COL = 'event_time', INTERVAL= '1 hour',  COMPRESSION = 'no');"},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, system_time "
         "integer DEFAULT current_time_second(), "
         "WATERMARK FOR event_time AS event_time TOLERANT);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, system_time, "
         "max(id) OVER(PARTITION BY window_start, window_end, water_mark) "
         "FROM TABLE(TUMBLE(TABLE stream1, event_time, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, system_time, "
         "max_id FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 11;
    uint32_t colNum = 5;
    int64_t id[rowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11};
    int64_t water_mark[rowNum] = {1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11};
    int64_t event_time[rowNum] = {10, 10, 11, 11, 11, 20, 20, 25, 25, 25, 1000};
    bool nullInfo[colNum] = {false, false, false, false, true};
    struct timeval tv1;
    gettimeofday(&tv1, NULL);
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time, nullInfo);
    struct timeval tv2;
    gettimeofday(&tv2, NULL);

    // 校验ts表中数据
    char *sqlCmd = (char *)"select * from ts1 order by window_end, id;";
    uint32_t expRowNum = 10;
    uint32_t expectColsCount = 7;
    int64_t map_id[expRowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    int64_t map_window_start[expRowNum] = {10, 10, 10, 10, 10, 20, 20, 20, 20, 20};
    int64_t map_window_end[expRowNum] = {20, 20, 20, 20, 20, 30, 30, 30, 30, 30};
    int64_t map_event_time[expRowNum] = {10, 10, 11, 11, 11, 20, 20, 25, 25, 25};
    int64_t map_aggregate[expRowNum] = {2, 2, 4, 4, 5, 6, 8, 8, 10, 10};
    RdCheckDataInTSTableOfStreamTable7(stmt, sqlCmd, expRowNum, expectColsCount, map_id, map_window_start,
        map_window_end, map_event_time, map_aggregate, tv1.tv_sec, tv2.tv_sec);
    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 对包含默认填充当前时间列的表执行min聚合函数计算
TEST_F(t01_auto_fill_system_time, STREAM_023_014)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50),  window_start integer, window_end integer, event_time integer, "
         "system_time integer, "
         "min_id integer) WITH (TIME_COL = 'event_time', INTERVAL= '1 hour',  COMPRESSION = 'no');"},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, system_time "
         "integer DEFAULT current_time_second(), "
         "WATERMARK FOR event_time AS event_time TOLERANT);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, system_time, "
         "min(id) OVER(PARTITION BY window_start, window_end, water_mark) "
         "FROM TABLE(TUMBLE(TABLE stream1, event_time, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, system_time, "
         "min_id FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 11;
    uint32_t colNum = 5;
    int64_t id[rowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11};
    int64_t water_mark[rowNum] = {1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11};
    int64_t event_time[rowNum] = {10, 10, 11, 11, 11, 20, 20, 25, 25, 25, 1000};
    bool nullInfo[colNum] = {false, false, false, false, true};
    struct timeval tv1;
    gettimeofday(&tv1, NULL);
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time, nullInfo);
    struct timeval tv2;
    gettimeofday(&tv2, NULL);

    // 校验ts表中数据
    char *sqlCmd = (char *)"select * from ts1 order by window_end, id;";
    uint32_t expRowNum = 10;
    uint32_t expectColsCount = 7;
    int64_t map_id[expRowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    int64_t map_window_start[expRowNum] = {10, 10, 10, 10, 10, 20, 20, 20, 20, 20};
    int64_t map_window_end[expRowNum] = {20, 20, 20, 20, 20, 30, 30, 30, 30, 30};
    int64_t map_event_time[expRowNum] = {10, 10, 11, 11, 11, 20, 20, 25, 25, 25};
    int64_t map_aggregate[expRowNum] = {1, 1, 3, 3, 5, 6, 7, 7, 9, 9};
    RdCheckDataInTSTableOfStreamTable7(stmt, sqlCmd, expRowNum, expectColsCount, map_id, map_window_start,
        map_window_end, map_event_time, map_aggregate, tv1.tv_sec, tv2.tv_sec);
    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 流表包含默认填充当前时间列，创建view带with子句，创建sink，写入数据并校验
TEST_F(t01_auto_fill_system_time, STREAM_023_015)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), water_mark integer, event_time integer, system_time integer) "
         "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour',  COMPRESSION = 'no');"},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer DEFAULT 1, event_time integer, "
         "system_time integer DEFAULT current_time_second());"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, water_mark, event_time, system_time FROM stream1 with "
         "(tuple_buffer_size = 15);"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, water_mark, event_time, system_time "
         "FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 5;
    int64_t id[rowNum] = {0, 1, 2, 3, 4};
    int64_t event_time[rowNum] = {0, 1, 2, 3, 4};
    bool nullInfo[colNum] = {false, false, true, false, true};
    struct timeval tv1;
    gettimeofday(&tv1, NULL);
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, event_time, nullInfo);
    struct timeval tv2;
    gettimeofday(&tv2, NULL);
    // 校验ts表中数据
    char *selectTsName = (char *)"select * from ts1 order by id;";
    uint32_t expectRowsCount = 5;
    uint32_t expectColsCount = 5;
    RdCheckDataInTSTableOfStreamTable1(
        stmt, selectTsName, expectRowsCount, expectColsCount, id, 1, event_time, tv1.tv_sec, tv2.tv_sec);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 流表包含默认填充当前时间列，基于滚动窗口创建view，创建sink，写入数据并校验
TEST_F(t01_auto_fill_system_time, STREAM_023_016)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50),  window_start integer, window_end integer, event_time integer, "
         "system_time integer) "
         "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour',  COMPRESSION = 'no');"},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, system_time "
         "integer DEFAULT current_time_second(), "
         "WATERMARK FOR event_time AS event_time TOLERANT);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, system_time "
         "FROM TABLE(TUMBLE(TABLE stream1, event_time, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, system_time "
         "FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 10;
    uint32_t colNum = 5;
    int64_t id[rowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    int64_t water_mark[rowNum] = {1, 1, 3, 3, 5, 5, 7, 7, 9, 9};
    int64_t event_time[rowNum] = {10, 10, 11, 11, 11, 20, 20, 25, 25, 25};
    bool nullInfo[colNum] = {false, false, false, false, true};
    struct timeval tv1;
    gettimeofday(&tv1, NULL);
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time, nullInfo);
    struct timeval tv2;
    gettimeofday(&tv2, NULL);
    // 校验ts表中数据
    char *selectTsName = (char *)"select * from ts1 order by window_end, id;";
    uint32_t expectRowsCount = 10;
    uint32_t expectColsCount = 6;
    int64_t mapping_id[expectRowsCount] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    int64_t mapping_window_start[expectRowsCount] = {10, 10, 10, 10, 10, 20, 20, 20, 20, 20};
    int64_t mapping_window_end[expectRowsCount] = {20, 20, 20, 20, 20, 30, 30, 30, 30, 30};
    int64_t mapping_event_time[expectRowsCount] = {10, 10, 11, 11, 11, 20, 20, 25, 25, 25};
    RdCheckDataInTSTableOfStreamTable8(stmt, selectTsName, expectRowsCount, expectColsCount, mapping_id,
        mapping_window_start, mapping_window_end, mapping_event_time, tv1.tv_sec, tv2.tv_sec);
    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 流表包含默认填充当前时间列，基于滑动窗口创建view，创建sink，写入数据并校验
TEST_F(t01_auto_fill_system_time, STREAM_023_017)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
         "system_time integer) "
         "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour',  COMPRESSION = 'no');"},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, system_time "
         "integer DEFAULT current_time_second(), "
         "WATERMARK FOR event_time AS event_time STRICT);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, system_time "
         "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, system_time "
         "FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 10;
    uint32_t colNum = 5;
    int64_t id[rowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    int64_t water_mark[rowNum] = {1, 1, 3, 3, 5, 5, 7, 7, 9, 9};
    int64_t event_time[rowNum] = {0, 1, 2, 3, 4, 10, 11, 12, 13, 14};
    bool nullInfo[colNum] = {false, false, false, false, true};
    struct timeval tv1;
    gettimeofday(&tv1, NULL);
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time, nullInfo);
    struct timeval tv2;
    gettimeofday(&tv2, NULL);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 20;
    uint32_t expectColsCount = 6;
    int64_t mapping_id[expectRowsCount] = {1, 2, 3, 4, 5, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 6, 7, 8, 9, 10};
    int64_t mapping_window_start[expectRowsCount] = {
        -5, -5, -5, -5, -5, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10};
    int64_t mapping_window_end[expectRowsCount] = {
        5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 15, 15, 15, 15, 15, 20, 20, 20, 20, 20};
    int64_t mapping_event_time[expectRowsCount] = {
        0, 1, 2, 3, 4, 0, 1, 2, 3, 4, 10, 11, 12, 13, 14, 10, 11, 12, 13, 14};
    RdCheckDataInTSTableOfStreamTable8(stmt, sqlCmd, expectRowsCount, expectColsCount, mapping_id, mapping_window_start,
        mapping_window_end, mapping_event_time, tv1.tv_sec, tv2.tv_sec); /**/

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 流表包含默认填充当前时间列，创建view对该字段进行>运算
TEST_F(t01_auto_fill_system_time, STREAM_023_018)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), water_mark integer, event_time integer, system_time integer) "
         "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour',  COMPRESSION = 'no');"},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer DEFAULT 1, event_time integer, "
         "system_time integer DEFAULT current_time_second());"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, water_mark, event_time, system_time FROM stream1 where "
         "system_time > 100;"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, water_mark, event_time, system_time "
         "FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 5;
    int64_t id[rowNum] = {0, 1, 2, 3, 4};
    int64_t event_time[rowNum] = {0, 1, 2, 3, 4};
    bool nullInfo[colNum] = {false, false, true, false, true};
    struct timeval tv1;
    gettimeofday(&tv1, NULL);
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, event_time, nullInfo);

    int64_t id2[rowNum] = {5, 6, 7, 8, 9};
    int64_t event_time2[rowNum] = {5, 6, 7, 8, 9};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id2, event_time2, nullInfo);
    struct timeval tv2;
    gettimeofday(&tv2, NULL);
    // 校验ts表中数据
    char *sqlCmd = (char *)"select * from ts1 order by id;";
    uint32_t expRowNum = 10;
    uint32_t expColNum = 5;
    int64_t exp_id[expRowNum] = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9};
    int64_t exp_event_time[expRowNum] = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9};
    RdCheckDataInTSTableOfStreamTable1(
        stmt, sqlCmd, expRowNum, expColNum, exp_id, 1, exp_event_time, tv1.tv_sec, tv2.tv_sec);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 流表包含默认填充当前时间列，创建view对该字段进行<运算
TEST_F(t01_auto_fill_system_time, STREAM_023_019)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), water_mark integer, event_time integer, system_time integer) "
         "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour',  COMPRESSION = 'no');"},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer DEFAULT 1, event_time integer, "
         "system_time integer DEFAULT current_time_second());"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, water_mark, event_time, system_time FROM stream1 where "
         "system_time < 1000000000;"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, water_mark, event_time, system_time "
         "FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 5;
    int64_t id[rowNum] = {0, 1, 2, 3, 4};
    int64_t event_time[rowNum] = {0, 1, 2, 3, 4};
    bool nullInfo[colNum] = {false, false, true, false, true};
    struct timeval tv1;
    gettimeofday(&tv1, NULL);
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, event_time, nullInfo);

    int64_t id2[rowNum] = {5, 6, 7, 8, 9};
    int64_t event_time2[rowNum] = {5, 6, 7, 8, 9};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id2, event_time2, nullInfo);
    struct timeval tv2;
    gettimeofday(&tv2, NULL);
    // 校验ts表中数据
    char *sqlCmd = (char *)"select * from ts1 order by id;";
    uint32_t expRowNum = 0;
    uint32_t expColNum = 0;
    int64_t exp_id[expRowNum] = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9};
    int64_t exp_event_time[expRowNum] = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9};
    RdCheckDataInTSTableOfStreamTable1(
        stmt, sqlCmd, expRowNum, expColNum, exp_id, 1, exp_event_time, tv1.tv_sec, tv2.tv_sec);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 流表包含默认填充当前时间列，创建view对该字段进行!=运算
TEST_F(t01_auto_fill_system_time, STREAM_023_020)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), water_mark integer, event_time integer, system_time integer) "
         "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour',  COMPRESSION = 'no');"},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer DEFAULT 1, event_time integer, "
         "system_time integer DEFAULT current_time_second());"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, water_mark, event_time, system_time FROM stream1 where "
         "system_time != 0;"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, water_mark, event_time, system_time "
         "FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 5;
    int64_t id[rowNum] = {0, 1, 2, 3, 4};
    int64_t event_time[rowNum] = {0, 1, 2, 3, 4};
    bool nullInfo[colNum] = {false, false, true, false, true};
    struct timeval tv1;
    gettimeofday(&tv1, NULL);
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, event_time, nullInfo);

    int64_t id2[rowNum] = {5, 6, 7, 8, 9};
    int64_t event_time2[rowNum] = {5, 6, 7, 8, 9};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id2, event_time2, nullInfo);
    struct timeval tv2;
    gettimeofday(&tv2, NULL);
    // 校验ts表中数据
    char *sqlCmd = (char *)"select * from ts1 order by id;";
    uint32_t expRowNum = 10;
    uint32_t expColNum = 5;
    int64_t exp_id[expRowNum] = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9};
    int64_t exp_event_time[expRowNum] = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9};
    RdCheckDataInTSTableOfStreamTable1(
        stmt, sqlCmd, expRowNum, expColNum, exp_id, 1, exp_event_time, tv1.tv_sec, tv2.tv_sec);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 流表包含默认填充当前时间列，创建view对该字段进行and运算
TEST_F(t01_auto_fill_system_time, STREAM_023_021)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), water_mark integer, event_time integer, system_time integer) "
         "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour',  COMPRESSION = 'no');"},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer DEFAULT 1, event_time integer, "
         "system_time integer DEFAULT current_time_second());"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, water_mark, event_time, system_time FROM stream1 where "
         "system_time > 0 and system_time < 3000000000;"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, water_mark, event_time, system_time "
         "FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 5;
    int64_t id[rowNum] = {0, 1, 2, 3, 4};
    int64_t event_time[rowNum] = {0, 1, 2, 3, 4};
    bool nullInfo[colNum] = {false, false, true, false, true};
    struct timeval tv1;
    gettimeofday(&tv1, NULL);
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, event_time, nullInfo);

    int64_t id2[rowNum] = {5, 6, 7, 8, 9};
    int64_t event_time2[rowNum] = {5, 6, 7, 8, 9};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id2, event_time2, nullInfo);
    struct timeval tv2;
    gettimeofday(&tv2, NULL);
    // 校验ts表中数据
    char *sqlCmd = (char *)"select * from ts1 order by id;";
    uint32_t expRowNum = 10;
    uint32_t expColNum = 5;
    int64_t exp_id[expRowNum] = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9};
    int64_t exp_event_time[expRowNum] = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9};
    RdCheckDataInTSTableOfStreamTable1(
        stmt, sqlCmd, expRowNum, expColNum, exp_id, 1, exp_event_time, tv1.tv_sec, tv2.tv_sec);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 流表包含默认填充当前时间列，创建view对该字段进行or运算
TEST_F(t01_auto_fill_system_time, STREAM_023_022)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), water_mark integer, event_time integer, system_time integer) "
         "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour',  COMPRESSION = 'no');"},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer DEFAULT 1, event_time integer, "
         "system_time integer DEFAULT current_time_second());"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, water_mark, event_time, system_time FROM stream1 where "
         "system_time > 0 or system_time < 1000000000;"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, water_mark, event_time, system_time "
         "FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 5;
    int64_t id[rowNum] = {0, 1, 2, 3, 4};
    int64_t event_time[rowNum] = {0, 1, 2, 3, 4};
    bool nullInfo[colNum] = {false, false, true, false, true};
    struct timeval tv1;
    gettimeofday(&tv1, NULL);
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, event_time, nullInfo);

    int64_t id2[rowNum] = {5, 6, 7, 8, 9};
    int64_t event_time2[rowNum] = {5, 6, 7, 8, 9};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id2, event_time2, nullInfo);
    struct timeval tv2;
    gettimeofday(&tv2, NULL);
    // 校验ts表中数据
    char *sqlCmd = (char *)"select * from ts1 order by id;";
    uint32_t expRowNum = 10;
    uint32_t expColNum = 5;
    int64_t exp_id[expRowNum] = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9};
    int64_t exp_event_time[expRowNum] = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9};
    RdCheckDataInTSTableOfStreamTable1(
        stmt, sqlCmd, expRowNum, expColNum, exp_id, 1, exp_event_time, tv1.tv_sec, tv2.tv_sec);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 对含有默认填充当前时间列的表使用ref引用
TEST_F(t01_auto_fill_system_time, STREAM_023_023)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), water_mark integer, event_time integer, system_time integer, "
         "ref1_id integer) "
         "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour',  COMPRESSION = 'no');"},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer DEFAULT 1, event_time integer, "
         "system_time integer DEFAULT current_time_second());"},
        {"create stream reference ref1(integer, integer);"},
        {"upsert into streamref ref1 values (1, 0);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, water_mark, event_time, system_time, REF['ref1'][id] FROM "
         "stream1;"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, water_mark, event_time, system_time, REF_ref1_id "
         "FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 5;
    int64_t id[rowNum] = {1, 1, 1, 1, 1};
    int64_t event_time[rowNum] = {0, 1, 2, 3, 4};
    bool nullInfo[colNum] = {false, false, true, false, true};
    struct timeval tv1;
    gettimeofday(&tv1, NULL);
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, event_time, nullInfo);
    struct timeval tv2;
    gettimeofday(&tv2, NULL);
    // 校验ts表中数据
    char *selectTsName = (char *)"select * from ts1 order by id;";
    uint32_t expectRowsCount = 5;
    uint32_t expectColsCount = 6;
    int64_t ref_value = 0;
    RdCheckDataInTSTableOfStreamTable9(
        stmt, selectTsName, expectRowsCount, expectColsCount, id, 1, event_time, tv1.tv_sec, tv2.tv_sec, ref_value);
    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 对含有默认填充当前时间列的表使用like
TEST_F(t01_auto_fill_system_time, STREAM_023_024)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), water_mark integer, event_time integer, system_time integer) "
         "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour',  COMPRESSION = 'no');"},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer DEFAULT 1, event_time integer, "
         "system_time integer DEFAULT current_time_second());"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, water_mark, event_time, system_time FROM stream1 where name "
         "like 'na%d';"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, water_mark, event_time, system_time "
         "FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 5;
    int64_t id[rowNum] = {0, 1, 2, 3, 4};
    int64_t event_time[rowNum] = {0, 1, 2, 3, 4};
    bool nullInfo[colNum] = {false, false, true, false, true};
    struct timeval tv1;
    gettimeofday(&tv1, NULL);
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, event_time, nullInfo);
    struct timeval tv2;
    gettimeofday(&tv2, NULL);
    // 校验ts表中数据
    char *selectTsName = (char *)"select * from ts1 order by id;";
    uint32_t expectRowsCount = 0;
    uint32_t expectColsCount = 0;
    RdCheckDataInTSTableOfStreamTable1(
        stmt, selectTsName, expectRowsCount, expectColsCount, id, 1, event_time, tv1.tv_sec, tv2.tv_sec);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// seq_distinct_count，依照默认时间列去重
TEST_F(t01_auto_fill_system_time, STREAM_023_025)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), water_mark integer, event_time integer, system_time integer, "
         "seq_distinct_count_system_time integer) "
         "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour',  COMPRESSION = 'no');"},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer DEFAULT 1, event_time integer, "
         "system_time integer DEFAULT current_time_second());"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, water_mark, event_time, system_time, "
         "seq_distinct_count(system_time) FROM stream1;"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, water_mark, event_time, system_time, "
         "seq_distinct_count_system_time "
         "FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 1;
    uint32_t colNum = 5;
    int64_t id[rowNum] = {0};
    int64_t event_time[rowNum] = {0};
    bool nullInfo[colNum] = {false, false, true, false, true};
    struct timeval tv1;
    gettimeofday(&tv1, NULL);
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, event_time, nullInfo);
    struct timeval tv2;
    gettimeofday(&tv2, NULL);
    sleep(1);
    int64_t id2[rowNum] = {5};
    int64_t event_time2[rowNum] = {5};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id2, event_time2, nullInfo);

    // 校验ts表中数据
    char *selectTsName = (char *)"select * from ts1 order by id;";
    uint32_t expRowNum = 1;
    uint32_t expColNum = 6;
    int64_t exp_id[expRowNum] = {0};
    int64_t exp_event_time[expRowNum] = {0};
    int64_t exp_seq_d_c_system_time[expRowNum] = {1};
    RdCheckDataInTSTableOfStreamTable10(stmt, selectTsName, expRowNum, expColNum, exp_id, 1, exp_event_time, tv1.tv_sec,
        tv2.tv_sec, exp_seq_d_c_system_time);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// seq_distinct_count，不依照默认时间列去重
TEST_F(t01_auto_fill_system_time, STREAM_023_026)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), water_mark integer, event_time integer, system_time integer, "
         "seq_distinct_count_id integer) "
         "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour',  COMPRESSION = 'no');"},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer DEFAULT 1, event_time integer, "
         "system_time integer DEFAULT current_time_second());"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, water_mark, event_time, system_time, seq_distinct_count(id) "
         "FROM stream1;"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, water_mark, event_time, system_time, seq_distinct_count_id "
         "FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 5;
    int64_t id[rowNum] = {0, 1, 0, 0, 4};
    int64_t event_time[rowNum] = {0, 1, 2, 3, 4};
    bool nullInfo[colNum] = {false, false, true, false, true};
    struct timeval tv1;
    gettimeofday(&tv1, NULL);
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, event_time, nullInfo);

    int64_t id2[rowNum] = {5, 5, 5, 5, 9};
    int64_t event_time2[rowNum] = {5, 6, 7, 8, 9};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id2, event_time2, nullInfo);
    struct timeval tv2;
    gettimeofday(&tv2, NULL);
    // 校验ts表中数据
    char *selectTsName = (char *)"select * from ts1 order by id, seq_distinct_count_id;";
    uint32_t expRowNum = 5;
    uint32_t expColNum = 6;
    int64_t exp_id[expRowNum] = {0, 0, 1, 4, 5};
    int64_t exp_event_time[expRowNum] = {0, 2, 1, 4, 5};
    int64_t exp_seq_d_c_id[expRowNum] = {1, 2, 1, 1, 4};
    RdCheckDataInTSTableOfStreamTable10(
        stmt, selectTsName, expRowNum, expColNum, exp_id, 1, exp_event_time, tv1.tv_sec, tv2.tv_sec, exp_seq_d_c_id);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 默认时间列做sum参数
TEST_F(t01_auto_fill_system_time, STREAM_023_027)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50),  window_start integer, window_end integer, event_time integer, "
         "system_time integer, "
         "sum_system_time integer) WITH (TIME_COL = 'event_time', INTERVAL= '1 hour',  COMPRESSION = 'no');"},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, system_time "
         "integer DEFAULT current_time_second(), "
         "WATERMARK FOR event_time AS event_time TOLERANT);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, system_time, "
         "sum(system_time) OVER(PARTITION BY window_start, window_end, water_mark) "
         "FROM TABLE(TUMBLE(TABLE stream1, event_time, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, system_time, "
         "sum_system_time FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 11;
    uint32_t colNum = 5;
    int64_t id[rowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11};
    int64_t water_mark[rowNum] = {1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11};
    int64_t event_time[rowNum] = {10, 10, 11, 11, 11, 20, 20, 25, 25, 25, 1000};
    bool nullInfo[colNum] = {false, false, false, false, true};
    struct timeval tv1;
    gettimeofday(&tv1, NULL);
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time, nullInfo);
    struct timeval tv2;
    gettimeofday(&tv2, NULL);
    // 校验ts表中数据
    char *sqlCmd = (char *)"select * from ts1 order by window_end, id;";
    uint32_t expRowNum = 10;
    uint32_t expColNum = 7;
    int64_t map_id[expRowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    int64_t map_window_start[expRowNum] = {10, 10, 10, 10, 10, 20, 20, 20, 20, 20};
    int64_t map_window_end[expRowNum] = {20, 20, 20, 20, 20, 30, 30, 30, 30, 30};
    int64_t map_event_time[expRowNum] = {10, 10, 11, 11, 11, 20, 20, 25, 25, 25};
    int64_t map_sum_system_time1[expRowNum] = {tv1.tv_sec * 2, tv1.tv_sec * 2, tv1.tv_sec * 2, tv1.tv_sec * 2,
        tv1.tv_sec, tv1.tv_sec, tv1.tv_sec * 2, tv1.tv_sec * 2, tv1.tv_sec * 2, tv1.tv_sec * 2};
    int64_t map_sum_system_time2[expRowNum] = {tv2.tv_sec * 2, tv2.tv_sec * 2, tv2.tv_sec * 2, tv2.tv_sec * 2,
        tv2.tv_sec, tv2.tv_sec, tv2.tv_sec * 2, tv2.tv_sec * 2, tv2.tv_sec * 2, tv2.tv_sec * 2};
    RdCheckDataInTSTableOfStreamTable11(stmt, sqlCmd, expRowNum, expColNum, map_id, map_window_start, map_window_end,
        map_event_time, map_sum_system_time1, map_sum_system_time2, tv1.tv_sec, tv2.tv_sec);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 默认时间列做count参数
TEST_F(t01_auto_fill_system_time, STREAM_023_028)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50),  window_start integer, window_end integer, event_time integer, "
         "system_time integer, "
         "count_system_time integer) WITH (TIME_COL = 'event_time', INTERVAL= '1 hour',  COMPRESSION = 'no');"},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, system_time "
         "integer DEFAULT current_time_second(), "
         "WATERMARK FOR event_time AS event_time TOLERANT);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, system_time, "
         "count(system_time) OVER(PARTITION BY window_start, window_end, water_mark) "
         "FROM TABLE(TUMBLE(TABLE stream1, event_time, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, system_time, "
         "count_system_time FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 11;
    uint32_t colNum = 5;
    int64_t id[rowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11};
    int64_t water_mark[rowNum] = {1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11};
    int64_t event_time[rowNum] = {10, 10, 11, 11, 11, 20, 20, 25, 25, 25, 1000};
    bool nullInfo[colNum] = {false, false, false, false, true};
    struct timeval tv1;
    gettimeofday(&tv1, NULL);
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time, nullInfo);
    struct timeval tv2;
    gettimeofday(&tv2, NULL);
    // 校验ts表中数据
    char *sqlCmd = (char *)"select * from ts1 order by window_end, id;";
    uint32_t expRowNum = 10;
    uint32_t expColNum = 7;
    int64_t map_id[expRowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    int64_t map_window_start[expRowNum] = {10, 10, 10, 10, 10, 20, 20, 20, 20, 20};
    int64_t map_window_end[expRowNum] = {20, 20, 20, 20, 20, 30, 30, 30, 30, 30};
    int64_t map_event_time[expRowNum] = {10, 10, 11, 11, 11, 20, 20, 25, 25, 25};
    int64_t map_count_system_time[expRowNum] = {2, 2, 2, 2, 1, 1, 2, 2, 2, 2};
    RdCheckDataInTSTableOfStreamTable7(stmt, sqlCmd, expRowNum, expColNum, map_id, map_window_start, map_window_end,
        map_event_time, map_count_system_time, tv1.tv_sec, tv2.tv_sec);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 默认时间列做max参数
TEST_F(t01_auto_fill_system_time, STREAM_023_029)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50),  window_start integer, window_end integer, event_time integer, "
         "system_time integer, "
         "max_system_time integer) WITH (TIME_COL = 'event_time', INTERVAL= '1 hour',  COMPRESSION = 'no');"},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, system_time "
         "integer DEFAULT current_time_second(), "
         "WATERMARK FOR event_time AS event_time TOLERANT);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, system_time, "
         "max(system_time) OVER(PARTITION BY window_start, window_end, water_mark) "
         "FROM TABLE(TUMBLE(TABLE stream1, event_time, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, system_time, "
         "max_system_time FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 11;
    uint32_t colNum = 5;
    int64_t id[rowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11};
    int64_t water_mark[rowNum] = {1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11};
    int64_t event_time[rowNum] = {10, 10, 11, 11, 11, 20, 20, 25, 25, 25, 1000};
    bool nullInfo[colNum] = {false, false, false, false, true};
    struct timeval tv1;
    gettimeofday(&tv1, NULL);
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time, nullInfo);
    struct timeval tv2;
    gettimeofday(&tv2, NULL);

    // 校验ts表中数据
    char *sqlCmd = (char *)"select * from ts1 order by window_end, id;";
    uint32_t expRowNum = 10;
    uint32_t expColNum = 7;
    int64_t map_id[expRowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    int64_t map_window_start[expRowNum] = {10, 10, 10, 10, 10, 20, 20, 20, 20, 20};
    int64_t map_window_end[expRowNum] = {20, 20, 20, 20, 20, 30, 30, 30, 30, 30};
    int64_t map_event_time[expRowNum] = {10, 10, 11, 11, 11, 20, 20, 25, 25, 25};
    int64_t map_count_system_time1[expRowNum] = {tv1.tv_sec, tv1.tv_sec, tv1.tv_sec, tv1.tv_sec, tv1.tv_sec, tv1.tv_sec,
        tv1.tv_sec, tv1.tv_sec, tv1.tv_sec, tv1.tv_sec};
    int64_t map_count_system_time2[expRowNum] = {tv2.tv_sec, tv2.tv_sec, tv2.tv_sec, tv2.tv_sec, tv2.tv_sec, tv2.tv_sec,
        tv2.tv_sec, tv2.tv_sec, tv2.tv_sec, tv2.tv_sec};
    RdCheckDataInTSTableOfStreamTable11(stmt, sqlCmd, expRowNum, expColNum, map_id, map_window_start, map_window_end,
        map_event_time, map_count_system_time1, map_count_system_time2, tv1.tv_sec, tv2.tv_sec);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 改系统时间，再写数据
TEST_F(t01_auto_fill_system_time, STREAM_023_030)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), water_mark integer, event_time integer, system_time integer) "
         "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour',  COMPRESSION = 'no');"},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer DEFAULT 1, event_time integer, "
         "system_time integer DEFAULT current_time_second());"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, water_mark, event_time, system_time FROM stream1;"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, water_mark, event_time, system_time "
         "FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    struct timeval newtime;
    // 设置新的时间
    newtime.tv_sec = 1640995200;  // 2022 年 1 月 1 日 12:00:00 的时间戳
    newtime.tv_usec = 0;
    // 设置系统时间
    if (settimeofday(&newtime, NULL) == -1) {
        perror("settimeofday");
    }

    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 5;
    int64_t id[rowNum] = {0, 1, 2, 3, 4};
    int64_t event_time[rowNum] = {0, 1, 2, 3, 4};
    bool nullInfo[colNum] = {false, false, true, false, true};
    struct timeval tv1;
    gettimeofday(&tv1, NULL);
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, event_time, nullInfo);
    struct timeval tv2;
    gettimeofday(&tv2, NULL);
    // 校验ts表中数据
    char *selectTsName = (char *)"select * from ts1 order by id;";
    uint32_t expectRowsCount = 5;
    uint32_t expectColsCount = 5;
    RdCheckDataInTSTableOfStreamTable1(
        stmt, selectTsName, expectRowsCount, expectColsCount, id, 1, event_time, tv1.tv_sec, tv2.tv_sec);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 默认时间列对应到ts表的TIME_COL列
TEST_F(t01_auto_fill_system_time, STREAM_023_031)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), water_mark integer, event_time integer, system_time integer) "
         "WITH (TIME_COL = 'system_time', INTERVAL= '1 hour',  COMPRESSION = 'no');"},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer DEFAULT 1, event_time integer, "
         "system_time integer DEFAULT current_time_second());"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, water_mark, event_time, system_time FROM stream1;"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, water_mark, event_time, system_time "
         "FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 5;
    int64_t id[rowNum] = {0, 1, 2, 3, 4};
    int64_t event_time[rowNum] = {0, 1, 2, 3, 4};
    bool nullInfo[colNum] = {false, false, true, false, true};
    struct timeval tv1;
    gettimeofday(&tv1, NULL);
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, event_time, nullInfo);
    struct timeval tv2;
    gettimeofday(&tv2, NULL);
    // 校验ts表中数据
    char *selectTsName = (char *)"select * from ts1 order by id;";
    uint32_t expectRowsCount = 5;
    uint32_t expectColsCount = 5;
    RdCheckDataInTSTableOfStreamTable1(
        stmt, selectTsName, expectRowsCount, expectColsCount, id, 1, event_time, tv1.tv_sec, tv2.tv_sec);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 多线程并发向包含默认填充时间列的流表写入数据领
TEST_F(t01_auto_fill_system_time, STREAM_023_032)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), water_mark integer, event_time integer, system_time integer) "
         "WITH (TIME_COL = 'system_time', INTERVAL= '1 hour',  COMPRESSION = 'no');"},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer DEFAULT 1, event_time integer, "
         "system_time integer DEFAULT current_time_second());"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, water_mark, event_time, system_time FROM stream1;"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, water_mark, event_time, system_time "
         "FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    uint32_t threadNum = 4;
    uint32_t rowNum = 100;
    uint32_t colNum = 5;
    pthread_t threads[threadNum] = {0};
    RdStreamStructWriteCtxT writeCtx[threadNum] = {0};
    struct timeval tv1;
    gettimeofday(&tv1, NULL);
    for (uint32_t i = 0; i < threadNum; ++i) {
        writeCtx[i] = {
            .vertexLabel = vertexLabel,
            .status = GMERR_OK,
            .rowStart = 0 + i * rowNum,
            .rowNum = rowNum,
            .colNum = colNum,
        };
        ret = pthread_create(&threads[i], NULL, RdStreamStructWriteWorker, &writeCtx[i]);
        ASSERT_EQ(0, ret);
    }

    for (uint32_t i = 0; i < threadNum; i++) {
        ret = pthread_join(threads[i], NULL);
        ASSERT_EQ(0, ret);
    }
    struct timeval tv2;
    gettimeofday(&tv2, NULL);
    // 校验ts表中数据
    char *sqlCmd = (char *)"select * from ts1 order by id;";
    uint32_t expRowNum = rowNum * threadNum;
    uint32_t expColNum = 5;
    int64_t id[rowNum * threadNum] = {0};
    int64_t event_time[rowNum * threadNum] = {0};
    for (int64_t i = 0; i < expRowNum; ++i) {
        id[i] = i;
        event_time[i] = i;
    }
    RdCheckDataInTSTableOfStreamTable1(stmt, sqlCmd, expRowNum, expColNum, id, 1, event_time, tv1.tv_sec, tv2.tv_sec);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

void *RandModiTime(void *p)
{
    srand(time(NULL));
    for (uint32_t i = 0; i < 10; ++i) {
        int randomNum = random() % 10;
        struct timeval newtime;
        // 设置新的时间
        newtime.tv_sec = 1710995200 - randomNum * 10000000;
        newtime.tv_usec = 0;
        // 设置系统时间
        if (settimeofday(&newtime, NULL) == -1) {
            perror("settimeofday");
        }
    }
}

// 多线程并发向包含默认填充时间列的流表写入数据领，期间另一个线程随机修改时间
TEST_F(t01_auto_fill_system_time, STREAM_023_033)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), water_mark integer, event_time integer, system_time integer) "
         "WITH (TIME_COL = 'system_time', INTERVAL= '1 hour',  COMPRESSION = 'no');"},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer DEFAULT 1, event_time integer, "
         "system_time integer DEFAULT current_time_second());"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, water_mark, event_time, system_time FROM stream1;"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, water_mark, event_time, system_time "
         "FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    uint32_t threadNum = 4;
    uint32_t rowNum = 100;
    uint32_t colNum = 5;
    pthread_t threads[threadNum + 1] = {0};
    RdStreamStructWriteCtxT writeCtx[threadNum] = {0};
    for (uint32_t i = 0; i < threadNum; ++i) {
        writeCtx[i] = {
            .vertexLabel = vertexLabel,
            .status = GMERR_OK,
            .rowStart = -100 + i * rowNum,
            .rowNum = rowNum,
            .colNum = colNum,
        };
        ret = pthread_create(&threads[i], NULL, RdStreamStructWriteWorker, &writeCtx[i]);
        ASSERT_EQ(0, ret);
    }

    ret = pthread_create(&threads[threadNum], NULL, RandModiTime, NULL);
    ASSERT_EQ(0, ret);

    for (uint32_t i = 0; i < threadNum + 1; i++) {
        ret = pthread_join(threads[i], NULL);
        ASSERT_EQ(0, ret);
    }

    // 校验ts表中数据
    char *sqlCmd = (char *)"select * from ts1 order by id;";
    uint32_t expRowNum = rowNum * threadNum;
    uint32_t expColNum = 5;
    RdCheckDataInTSTableOfStreamTable12(stmt, sqlCmd, expRowNum, expColNum);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// sql命令全部使用大写
TEST_F(t01_auto_fill_system_time, STREAM_023_034)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE TS1 (ID INTEGER, NAME CHAR(50), WATER_MARK INTEGER, EVENT_TIME INTEGER, SYSTEM_TIME INTEGER) "
         "WITH (TIME_COL = 'EVENT_TIME', INTERVAL= '1 HOUR',  COMPRESSION = 'NO');"},
        {"CREATE STREAM TABLE STREAM1 (ID INTEGER, NAME CHAR(50), WATER_MARK INTEGER DEFAULT 1, EVENT_TIME INTEGER, "
         "SYSTEM_TIME INTEGER DEFAULT CURRENT_TIME_SECOND());"},
        {"CREATE STREAM VIEW VIEW1 AS SELECT ID, NAME, WATER_MARK, EVENT_TIME, SYSTEM_TIME FROM STREAM1;"},
        {"CREATE STREAM SINK SINK1 AS SELECT ID, NAME, WATER_MARK, EVENT_TIME, SYSTEM_TIME "
         "FROM VIEW1 INTO TSDB(TS1) WITH (BATCH_WINDOW_SIZE = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 5;
    int64_t id[rowNum] = {0, 1, 2, 3, 4};
    int64_t event_time[rowNum] = {0, 1, 2, 3, 4};
    bool nullInfo[colNum] = {false, false, true, false, true};
    struct timeval tv1;
    gettimeofday(&tv1, NULL);
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, event_time, nullInfo);
    struct timeval tv2;
    gettimeofday(&tv2, NULL);
    // 校验ts表中数据
    char *selectTsName = (char *)"select * from ts1 order by id;";
    uint32_t expectRowsCount = 5;
    uint32_t expectColsCount = 5;
    RdCheckDataInTSTableOfStreamTable1(
        stmt, selectTsName, expectRowsCount, expectColsCount, id, 1, event_time, tv1.tv_sec, tv2.tv_sec);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// sql命令全部使用小写
TEST_F(t01_auto_fill_system_time, STREAM_023_035)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer, system_time integer) "
         "with (time_col = 'event_time', interval= '1 hour',  compression = 'no');"},
        {"create stream table stream1 (id integer, name char(50), water_mark integer default 1, event_time integer, "
         "system_time integer default current_time_second());"},
        {"create stream view view1 as select id, name, water_mark, event_time, system_time from stream1;"},
        {"create stream sink sink1 as select id, name, water_mark, event_time, system_time "
         "from view1 into tsdb(ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 5;
    int64_t id[rowNum] = {0, 1, 2, 3, 4};
    int64_t event_time[rowNum] = {0, 1, 2, 3, 4};
    bool nullInfo[colNum] = {false, false, true, false, true};
    struct timeval tv1;
    gettimeofday(&tv1, NULL);
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, event_time, nullInfo);
    struct timeval tv2;
    gettimeofday(&tv2, NULL);
    // 校验ts表中数据
    char *selectTsName = (char *)"select * from ts1 order by id;";
    uint32_t expectRowsCount = 5;
    uint32_t expectColsCount = 5;
    RdCheckDataInTSTableOfStreamTable1(
        stmt, selectTsName, expectRowsCount, expectColsCount, id, 1, event_time, tv1.tv_sec, tv2.tv_sec);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// (char, int)表，char字段设置默认填充当前时间
TEST_F(t01_auto_fill_system_time, STREAM_023_036)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (name char(50), system_time integer) "
         "WITH (TIME_COL = 'system_time', INTERVAL= '1 hour',  COMPRESSION = 'no');"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd =
        "CREATE STREAM TABLE stream1 (name char(50) DEFAULT current_time_second(), system_time integer);";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_DATATYPE_MISMATCH, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// (char, text, int)表，text字段设置默认填充当前时间
TEST_F(t01_auto_fill_system_time, STREAM_023_037)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (name char(50), name2 text, system_time integer) "
         "WITH (TIME_COL = 'system_time', INTERVAL= '1 hour',  COMPRESSION = 'no');"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd =
        "CREATE STREAM TABLE stream1 (name char(50), name2 text DEFAULT current_time_second(), system_time integer);";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_DATATYPE_MISMATCH, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

