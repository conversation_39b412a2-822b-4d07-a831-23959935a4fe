/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2023. All rights reserved.
 * Description: 持久化规格
 * Author: pwx860460
 * Create: 2023-11-08
 * History:
 */

#include "./common/incre_pst_common.h"
#include "t_datacom_lite.h"
#include "../../../../testcases/03_DML/077_BigObject/VertexLabelTypical.h"
#include "../../../02_DDL/048_ClusteredEnhance/ClusEnhance.h"
#include "gmc_dlr.h"


int g_beginIndex = 0;
int g_endIndex = 100;

class PersistBasicTest : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase() {}
    static void TearDownTestCase() {}
};

void PersistBasicTest::SetUp()
{
    printf("[INFO] Incremental Persistence cfg test Start.\n");
    system("rm ./gmdb -rf");
    int ret = ChangeGmserverCfg((char *)"recover", NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        printf("get env PWD fail.\n");
    }
    (void)sprintf(g_dbFilePath, "%s/gmdb", pwdDir);
    (void)Rmdir(g_dbFilePath);
    ret = mkdir(g_dbFilePath, S_IRUSR | S_IWUSR);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"dataFileDirPath", g_dbFilePath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"0");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
    char errorMsg1[1024] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_CONNECTION_RESET_BY_PEER);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    char errorMsg2[1024] = {};
    (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_WRONG_STMT_OBJECT);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg2);
    char errorMsg3[128] = {};
    (void)snprintf(errorMsg3, sizeof(errorMsg3), "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg3);
    char errorMsg4[128] = {};
    (void)snprintf(errorMsg4, sizeof(errorMsg4), "GMERR-%d", GMERR_DATATYPE_MISMATCH);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg4);
    char errorMsg5[128] = {};
    (void)snprintf(errorMsg5, sizeof(errorMsg5), "GMERR-%d", GMERR_INTERNAL_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg4);
    char errorMsg6[128] = {};
    (void)snprintf(errorMsg6, sizeof(errorMsg6), "GMERR-%d", GMERR_DUPLICATE_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg6);
    char errorMsg7[128] = {};
    (void)snprintf(errorMsg7, sizeof(errorMsg7), "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg7);
    char errorMsg8[128] = {};
    (void)snprintf(errorMsg8, sizeof(errorMsg8), "GMERR-%d", GMERR_RESTRICT_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg8);
    char errorMsg9[128] = {};
    (void)snprintf(errorMsg9, sizeof(errorMsg9), "GMERR-%d", GMERR_CONNECTION_FAILURE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg9);
    char errorMsg10[128] = {};
    (void)snprintf(errorMsg10, sizeof(errorMsg10), "GMERR-%d", GMERR_CONFIG_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg10);
    
}

void PersistBasicTest::TearDown()
{
    int ret = ChangeGmserverCfg((char *)"recover", NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_FILE_OPERATE_FAILED);
    AW_CHECK_LOG_END();
    printf("[INFO] Incremental Persistence cfg test End.\n");
}

// 持久化pageSize配置项，最小支持8kB，设置成8kb 预期成功
TEST_F(PersistBasicTest, Basicspecifications_001_019_001_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // pageSize : 8k
    system("sh $TEST_HOME/tools/stop.sh -f");
    int ret = ChangeGmserverCfg((char *)"pageSize", (char *)"8");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = CommonPstCheck(g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    ret = ChangeGmserverCfg((char *)"recover", NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 持久化pageSize配置项，最小支持8kB，设置成4kb 预期失败 GMERR_WRONG_STMT_OBJECT
TEST_F(PersistBasicTest, Basicspecifications_001_019_001_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // pageSize : 8k
    system("sh $TEST_HOME/tools/stop.sh -f");
    int ret = ChangeGmserverCfg((char *)"pageSize", (char *)"4");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // durable支持pageSize最小4k
    ret = CommonPstCheck(g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    ret = ChangeGmserverCfg((char *)"recover", NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 持久化设置maxSeMem(8) < 4*deviceSize(4)  预期失败 建连失败
TEST_F(PersistBasicTest, Basicspecifications_001_019_001_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // pageSize : 8k
    system("sh $TEST_HOME/tools/stop.sh -f");
    int ret = ChangeGmserverCfg((char *)"maxSeMem", (char *)"8");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = CommonPstCheck(g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);

    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    ret = ChangeGmserverCfg((char *)"recover", NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 持久化不支持superfiled  GMERR_DATATYPE_MISMATCH
TEST_F(PersistBasicTest, Basicspecifications_001_019_001_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    system("gmadmin -cfgName compatibleV3 -cfgVal 0");
    const char *labelName = "Other_001_superfiledParse_015";
    char *superfield_label_schema = NULL;
    readJanssonFile("schema/Superfiled_schema_1024.gmjson", &superfield_label_schema);
    ASSERT_NE((void *)NULL, superfield_label_schema);

    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit(-1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //  superfiled包含1024个field
    ret = GmcDropVertexLabel(g_stmt, labelName);
    ret = GmcCreateVertexLabel(g_stmt, superfield_label_schema, NULL);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    free(superfield_label_schema);
    system("gmadmin -cfgName compatibleV3 -cfgVal 1");
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end.");
}
const char *SN_tableConfig = "{\"max_record_count\" : 10000000, \"isFastReadUncommitted\":true,"
                             "\"status_merge_sub\":true}";

// 持久化不支持hashcluster GMERR_FEATURE_NOT_SUPPORTED
TEST_F(PersistBasicTest, Basicspecifications_001_019_001_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
   
    const char *labelName = "V_1";
    char *hashcluster_label_schema = NULL;
    readJanssonFile("./schema/NewSubV_1.gmjson", &hashcluster_label_schema);
    system("sh ${TEST_HOME}/tools/start.sh -f");
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建表包含hashcluster
    ret = GmcCreateVertexLabel(g_stmt, hashcluster_label_schema, SN_tableConfig);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    free(hashcluster_label_schema);
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end.");
}
string adjust_member_key_num(int key_num)
{
    const char *p_1 = "[{\"type\":\"record\",\"name\":\"label\",\"fields\":[{\"name\":\"A\",\"type\": \"record\", "
                      "\"vector\": true,\"fields\": [";
    const char *p_2 = "]}],\"keys\":[";
    const char *p_3 = "]}]";
    string str_fields = "";
    string str_keys = "";
    string field[key_num];
    string key[key_num];
    for (int i = 0; i < key_num; i++) {
        field[i] = "{\"name\":\"a" + to_string(i) + "\", \"type\":\"int32\"}";
        key[i] = "{\"node\":\"A\",\"name\":\"key_" + to_string(i) + "\",\"fields\":[\"a" + to_string(i) +
                 "\"],\"index\":{\"type\":\"none\"},\"constraints\":{\"unique\":true}}";
        str_fields += field[i];
        str_keys += key[i];
        if (i < key_num - 1) {
            str_fields += ",";
            str_keys += ",";
        }
    }
    string str = p_1 + str_fields + p_2 + str_keys + p_3;
    return str;
}

// 持久化不支持索引memeber key
TEST_F(PersistBasicTest, Basicspecifications_001_019_001_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *g_labelName006 = (const char *)"member_key_test";
    char *vertexLabel_schema = NULL;

    // 建立连接
    GmcConnT *g_conn = NULL;
    GmcStmtT *g_stmt = NULL;
    ret = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    readJanssonFile("schema/member_key_update_test.gmjson", &vertexLabel_schema);
    ASSERT_NE((void *)NULL, vertexLabel_schema);
    GmcDropVertexLabel(g_stmt, g_labelName006);
    ret = GmcCreateVertexLabel(g_stmt, vertexLabel_schema, NULL);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    free(vertexLabel_schema);

    // 释放连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    // 清理环境
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 持久化不支持索引过滤  
TEST_F(PersistBasicTest, Basicspecifications_001_019_001_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 建立连接
    GmcConnT *g_conn = NULL;
    GmcStmtT *g_stmt = NULL;
    ret = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    
    const char *labelName = "IndexPartialSpec";
    char *label_schema = NULL;
    readJanssonFile("./schema/Vertex_07.gmjson", &label_schema);
    ret = GmcCreateVertexLabel(g_stmt, label_schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    
    // 落盘
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit(-1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, NULL);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);
    free(label_schema);

    // 删除
    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);

    // 释放连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    // 清理环境
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 持久化不支持特性datalog 编译开发代码没有gmprecompiler工具 证明不支持datalog
TEST_F(PersistBasicTest, Basicspecifications_001_019_001_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
#if defined FEATURE_DATALOG
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog()); // 用例执行前确认队列已清空
#endif
    int ret = 0;
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 建立连接
    GmcConnT *g_conn = NULL;
    GmcStmtT *g_stmt = NULL;
    ret = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    char fileName[] = "base_prefile/normal.d";
    char libName[] = "base_prefile/normal.so";
    char labelName_in[] = "ns1.A";
    char labelName_out[] = "ns1.B";

    // 查询gmprecompiler工具是否存在
    char temp_command[1024];
    snprintf(temp_command, MAX_CMD_SIZE, "ls ../../../../../../output/euler/aarch64/bin/|grep gmprecompiler |wc -l");
    system(temp_command);
    ret = executeCommand(temp_command, "0");
    if (ret != GMERR_OK) {
        TEST_ERROR("connect failed, ret = %d", ret);
    }

    // 释放连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    // 清理环境
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end.");
}

GmcStmtT *g_stmt_sync[10] = {0};
GmcConnT *g_conn_async = NULL;
GmcStmtT *g_stmt_async = NULL;
GmcStmtT *g_stmt_root = NULL;
GmcStmtT *g_stmt_con[10] = {0};
GmcStmtT *g_stmt_list[10] = {0};
GmcStmtT *g_stmt_choice[10] = {0};
GmcStmtT *g_stmt_case[10] = {0};

// 持久化不支持yang表 创建edge 返回GMERR_FEATURE_NOT_SUPPORTED
TEST_F(PersistBasicTest, Basicspecifications_001_019_001_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 建立连接
    GmcConnT *g_conn = NULL;
    GmcStmtT *g_stmt = NULL;
    ret = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    char *normal_graph_edge_label_schema = NULL;
    char *normal_graph_vertex_label_schema = NULL;
    int start_num = 0;
    int end_num = 100;
    char Label_config009[] = "{\"max_record_count\":1000000,\"isFastReadUncommitted\":0}";

    // get schema
    readJanssonFile("schema/YangGraphVertexLabel.gmjson", &normal_graph_vertex_label_schema);
    ASSERT_NE((void *)NULL, normal_graph_vertex_label_schema);
    readJanssonFile("schema/YangGraphEdgeLabel.gmjson", &normal_graph_edge_label_schema);
    ASSERT_NE((void *)NULL, normal_graph_edge_label_schema);

    GmcDropVertexLabel(g_stmt, "vsys");
    // 创建批量vertexLabel
    ret = GmcCreateGraphVertexLabel(g_stmt, normal_graph_vertex_label_schema, Label_config009);
    ASSERT_EQ(GMERR_OK, ret);
    // 创建批量edgeLabel
    ret = GmcCreateEdgeLabel(g_stmt, normal_graph_edge_label_schema, Label_config009);
    ASSERT_EQ(GMERR_OK, ret);// 目前只支持按需
    free(normal_graph_vertex_label_schema);
    free(normal_graph_edge_label_schema);
    // 释放连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    // 清理环境
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end.");
}

const char *KVTable_Name = "KVTable_01";
const char *MS_config = "{\"max_record_count\" : 20000}";
void testCreateKVTable(GmcStmtT *stmt)
{
    int ret = 0;

    // 创建kv表
    ret = GmcKvCreateTable(stmt, KVTable_Name, MS_config);
    EXPECT_EQ(GMERR_OK, ret);
}

void testDropKVTable(GmcStmtT *stmt)
{
    int ret = 0;

    // 删除kv表
    ret = GmcKvDropTable(stmt, KVTable_Name);
    EXPECT_EQ(GMERR_OK, ret);
}
void testInsertKVTable(GmcStmtT *stmt, uint32_t times, uint32_t initValue, const char *labelname)
{
    int ret = 0;
    uint32_t i;
    uint32_t value;

    // set KV
    for (i = 0; i < times; i++) {
        ret = GmcKvPrepareStmtByLabelName(stmt, labelname);
        EXPECT_EQ(GMERR_OK, ret);
        GmcKvTupleT kvInfo = {0};
        value = initValue + i;
        char key[32];
        sprintf_s(key, 30, "zhangsan_%d", value);
        // 设置k-v值
        ret = GmcKvSet(stmt, key, strlen(key), &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }
}

// 持久化不支持kv表 ，创建kv表持久化 后创建kv表成功，持久化后kv表不存在了
TEST_F(PersistBasicTest, Basicspecifications_001_019_001_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f");
    int ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 建立连接
    GmcConnT *g_conn = NULL;
    GmcStmtT *g_stmt = NULL;
    ret = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // 创建KV表
    testCreateKVTable(g_stmt);

    uint32_t initValue = 0;
    uint32_t times = 100;
    uint32_t succNum = 0;

    // 插入数据
    testInsertKVTable(g_stmt, times, initValue, KVTable_Name);

    // 落盘
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit(-1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建kv表 持久化后表不存在了
    ret = GmcKvCreateTable(g_stmt, KVTable_Name, MS_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 清理环境
    // 删除KV表
    testDropKVTable(g_stmt);

    // 释放连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    // 清理环境
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 持久化不支持资源池 持久化配置拉起服务 创建资源池失败 GMERR_FEATURE_NOT_SUPPORTED
TEST_F(PersistBasicTest, Basicspecifications_001_019_001_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 建立连接
    GmcConnT *g_conn = NULL;
    GmcStmtT *g_stmt = NULL;
    ret = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // Label包含资源字段
    const char *resLabelName = "resource_label";
    const char *resLabelSchema = R"([{
        "type": "record",
        "name": "resource_label",
        "fields": [
            { "name": "F0", "type": "int32" },
            { "name": "R0", "type": "resource", "nullable": false },
            { "name": "R1", "type": "resource", "nullable": false }
        ],
        "keys": [
            {
                "node": "dw_resource_label",
                "name": "resource_label_K0",
                "fields": ["F0"],
                "index": { "type": "primary" },
                "constraints": { "unique": true }
            }
        ]
    }])";
    const char *resPoolName = "dw_res_pool";
    const char *resPoolSchema = R"({
        "name": "dw_res_pool",
        "pool_id": 100,
        "start_id": 100,
        "capacity": 1000,
        "order": 0,
        "alloc_type": 0
    })";

    // 创建资源池
    ret = GmcCreateResPool(g_stmt, resPoolSchema);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);

    // 落盘
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit(-1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    // 清理环境
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end.");
}

const char *labelJson012 = R"(
        [{
    "type":"record",
    "name":"T20_all_type",
    "fields":[
        {"name":"F7", "type":"uint32",  "nullable":false},
	    {"name":"F0", "type":"char",    "nullable":true},
	    {"name":"F1", "type":"uchar",   "nullable":true},
        {"name":"F2", "type":"int8",    "nullable":true},
        {"name":"F3", "type":"uint8",   "nullable":true},
        {"name":"F4", "type":"int16",   "nullable":true},
        {"name":"F5", "type":"uint16",  "nullable":true},
        {"name":"F6", "type":"int32",   "nullable":true},     
        {"name":"F8", "type":"boolean", "nullable":true},
        {"name":"F9", "type":"int64",   "nullable":true},
        {"name":"F10", "type":"uint64", "nullable":true},
        {"name":"F11", "type":"float",  "nullable":true},
        {"name":"F12", "type":"double", "nullable":true},
        {"name":"F13", "type":"time",   "nullable":true},
        {"name":"F14", "type":"string", "nullable":true, "size":100} ,
        {"name":"F15", "type":"bytes",  "nullable":true, "size":10 },
        {"name":"F16", "type":"fixed",  "nullable":true, "size":5 },
        {"name":"F17", "type":"uint32", "nullable":true}
    ],
    "keys":[
       {
            "node":"T20_all_type",
            "name":"T20_PK",
            "fields":["F7"],
            "index":{"type":"primary"},
            "constraints":{"unique":true}
        }
    ]
}
]
    )";

const char *subInfo0 = R"(
        {
    "name":"subVertexLabel0",
    "label_name":"T20_all_type",
    "comment":"VertexLabel subscription",
    "type":"before_commit",
    "events":
        [
            {"type":"insert", "msgTypes":["new object", "old object"]},
            {"type":"update", "msgTypes":["new object", "old object"]},
            {"type":"delete", "msgTypes":["new object", "old object"]},
            {"type":"replace", "msgTypes":["new object", "old object"]}
        ],
    "is_path":false,
    "retry":true
}
    )";

const char *g_configJson = "{\"max_record_num\" : 10000}";
const char *g_label_name = "T20_all_type";

void sn_callback_NULL(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{}

void test_setVertexProperty(GmcStmtT *stmt, int pk = 0)
{
    int ret;
    char teststr0 = 'a';
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_CHAR, &teststr0, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);
    unsigned char teststr1 = 'b';
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UCHAR, &teststr1, sizeof(unsigned char));
    EXPECT_EQ(GMERR_OK, ret);
    int8_t value2 = (1 + pk);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &value2, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint16_t value5 = (1000 + pk);
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &value5, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    int32_t value6 = pk;  // 联合索引时F6是PK
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, &value6, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t value10 = 1000 + pk;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_UINT64, &value10, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    float value11 = (float)1.2 + (float)pk;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_FLOAT, &value11, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);
    double value12 = 10.86 + pk;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_DOUBLE, &value12, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t value13 = 1000 + pk;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_TIME, &value13, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    char teststr14[] = "string";
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, teststr14, (strlen(teststr14)));
    EXPECT_EQ(GMERR_OK, ret);
    char teststr15[10] = "bytes";
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_BYTES, teststr15, 5);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value17 = (1000 + pk);
    ret = GmcSetVertexProperty(stmt, "F17", GMC_DATATYPE_UINT32, &value17, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value7 = pk;  // F7是PK
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT32, &value7, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    bool value8 = false;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &value8, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);
    int64_t value9 = 1000 + pk;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &value9, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t value3 = (10 + pk);
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &value3, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    int16_t value4 = (100 + pk);
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &value4, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);
    char teststr16[6] = "fixed";
    EXPECT_EQ(6, strlen(teststr16) + 1);
    ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_FIXED, teststr16, strlen(teststr16));
    EXPECT_EQ(GMERR_OK, ret);
}
// 持久化不支持订阅 持久化后订阅关系不存在
TEST_F(PersistBasicTest, Basicspecifications_001_019_001_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f");
    int ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 建立连接
    GmcConnT *g_conn = NULL;
    GmcStmtT *g_stmt = NULL;
    ret = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // 创建表
    GmcDropVertexLabel(g_stmt, "T20_all_type");
    ret = GmcCreateVertexLabel(g_stmt, labelJson012, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建订阅关系
    const int errCodeLen = 1024;
    char errorMsg[errCodeLen] = {0};
    (void)snprintf(errorMsg, errCodeLen, "GMERR-%d", GMERR_DUPLICATE_OBJECT);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    GmcConnT *subChannel = NULL;
    GmcStmtT *subStmt = NULL;
    const char *subName = "subVertexLabel0";
    const char *subChannelName = "subChannel0";
    ret = testSubConnect(&subChannel, &subStmt, 1, g_epoll_reg_info, subChannelName);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, ret);
    GmcSubConfigT tmp_subInfo0;
    tmp_subInfo0.subsName = subName;
    tmp_subInfo0.configJson = subInfo0;
    ret = GmcSubscribe(g_stmt, &tmp_subInfo0, subChannel, sn_callback_NULL, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // 触发订阅
    for (int i = 0; i < 50; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, "T20_all_type", GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        test_setVertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // 断开连接
    // 落盘
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit(-1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 下发同名订阅关系失败
    ret = testSubConnect(&subChannel, &subStmt, 1, g_epoll_reg_info, subChannelName);
    EXPECT_EQ(GMERR_OK, ret);
    tmp_subInfo0.subsName = subName;
    tmp_subInfo0.configJson = subInfo0;
    ret = GmcSubscribe(g_stmt, &tmp_subInfo0, subChannel, sn_callback_NULL, NULL);
    EXPECT_EQ(GMERR_OK, ret);// 持久化后订阅关系丢失需要重新订阅
    testGmcGetLastError();
    // 删除表
    GmcDropVertexLabel(g_stmt, "T20_all_type");
    // 释放连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
    
    // 清理环境
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/stop.sh -f");
    AW_FUN_Log(LOG_STEP, "test end.");
}

void getScanAbnormalTest(GmcStmtT *stmt, char *labelName)
{
    // scan 全表扫
    int ret = 0;
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, DB_MAX_UINT32, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    int cnt = 0;
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        cnt++;

        uint32_t bufSize = 1024;
        char buf[1024] = {0};
        memset(buf, sizeof(char) * bufSize, 0);
        GmcDlrDataBufT dataBufs;
        dataBufs.bufSize = bufSize;
        dataBufs.buf = buf;
        dataBufs.writtenLength = 0;
        ret = GmcDlrDataBufInit(&dataBufs, buf, bufSize);
        if (ret != 0) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            break;
        }
        bool getEof = false;
        while (!getEof) {
            ret = GmcGetScanDlrDataBuf(stmt, &dataBufs, &getEof);
            EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
            AW_ADD_ERR_WHITE_LIST(1, "GMERR-1003000");
            if (ret != 0) {
                ret = testGmcGetLastError(NULL);
                EXPECT_EQ(GMERR_OK, ret);
                break;
            }
        }
    }
    AW_FUN_Log(LOG_DEBUG, "[INFO]LABEL_CNT: %d", cnt);
}


// 持久化不支持DLR  GMERR_FEATURE_NOT_SUPPORTED
TEST_F(PersistBasicTest, Basicspecifications_001_019_001_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 建立连接
    GmcConnT *g_conn = NULL;
    GmcStmtT *g_stmt = NULL;
    ret = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // create vertex
    ret = 0;
    int32_t startNum = 0;
    int32_t endNum = 10;
    char usrName[] = "usr";
    const char *testNamespace = "test_namespace";
    ret = GmcCreateNamespace(g_stmt, testNamespace, usrName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, testNamespace);
    EXPECT_EQ(GMERR_OK, ret);

    char configJson[128] = "{\"max_record_count\" : 100000}";
    char *labelSchema = NULL;
    char labelName[] = "simpleLabel";
    readJanssonFile("schema/simpleLabel13.gmjson", &labelSchema);
    ASSERT_NE((void *)NULL, labelSchema);
    // 创建简单表
    ret = GmcCreateVertexLabel(g_stmt, labelSchema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelSchema);
    getScanAbnormalTest(g_stmt, labelName);

    // 删除
    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);

    // 释放连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    // 清理环境
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end.");
}

GmcCheckInfoT *checkInfo, *checkInfo2;
GmcCheckStatusE checkStatus, checkStatus2;
const char *labelname = "OP_T0";
char pk_name[] = "pk";
#define FULLTABLE 0xff
int g_data_num = 10, affectRows = 0;
#if defined ENV_RTOSV2X
#define END_NUM 1000
#else
#define END_NUM 10000
#endif

void set_VertexProperty_PK(GmcStmtT *stmt, int i)
{
    int ret = 0;
    uint32_t F7Value = i;
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT32, &F7Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
}

void set_VertexProperty(GmcStmtT *stmt, int i)
{
    int ret = 0;
    char F0Value = i;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_CHAR, &F0Value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);
    unsigned char F1Value = i;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UCHAR, &F1Value, sizeof(unsigned char));
    EXPECT_EQ(GMERR_OK, ret);
    int8_t F2Value = i;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &F2Value, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t F3Value = i;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &F3Value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    int16_t F4Value = i;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &F4Value, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint16_t F5Value = i;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &F5Value, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    int32_t F6Value = i;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, &F6Value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    bool F8Value = false;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &F8Value, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);
}

void query_VertexProperty(GmcStmtT *stmt, int i, const char *labelName)
{
    int ret = 0;

    // 读
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t priK = i;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &priK, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, pk_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 检查读取的数据是否无误
    while (true) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        // Get F0
        char F0Value = i;
        ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_CHAR, &F0Value);
        EXPECT_EQ(GMERR_OK, ret);
        // Get F1
        unsigned char F1Value = i;
        ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_UCHAR, &F1Value);
        EXPECT_EQ(GMERR_OK, ret);
        // Get F2
        int8_t F2Value = i;
        ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_INT8, &F2Value);
        EXPECT_EQ(GMERR_OK, ret);
        // Get F3
        uint8_t F3Value = i;
        ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_UINT8, &F3Value);
        EXPECT_EQ(GMERR_OK, ret);
        // Get F4
        int16_t F4Value = i;
        ret = queryPropertyAndCompare(stmt, "F4", GMC_DATATYPE_INT16, &F4Value);
        EXPECT_EQ(GMERR_OK, ret);
        // Get F5
        uint16_t F5Value = i;
        ret = queryPropertyAndCompare(stmt, "F5", GMC_DATATYPE_UINT16, &F5Value);
        EXPECT_EQ(GMERR_OK, ret);
        // Get F6
        int32_t F6Value = i;
        ret = queryPropertyAndCompare(stmt, "F6", GMC_DATATYPE_INT32, &F6Value);
        EXPECT_EQ(GMERR_OK, ret);
        // Get F8
        bool F8Value = false;
        ret = queryPropertyAndCompare(stmt, "F8", GMC_DATATYPE_BOOL, &F8Value);
        EXPECT_EQ(GMERR_OK, ret);
    }
}
// 持久化不支持对账 开启老化 持久化重启 老化CHECKING状态消失
TEST_F(PersistBasicTest, Basicspecifications_001_019_001_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 重启连接建表
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f");
    int ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 建立连接
    GmcConnT *g_conn = NULL;
    GmcStmtT *g_stmt = NULL;
    ret = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // 创建表
    GmcDropVertexLabel(g_stmt, "OP_T0");
    char *label_schema14 = NULL;
    readJanssonFile("schema/schema_0014.gmjson", &label_schema14);
    ASSERT_NE((void *)NULL, label_schema14);
    ret = GmcCreateVertexLabel(g_stmt, label_schema14, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(label_schema14);

     // insert
    for (int i = 0; i < END_NUM; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelname, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);

        // 校验数据是否插入成功
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1, affectRows);

        // 读
        query_VertexProperty(g_stmt, i, labelname);
    }

    // 开启对账
    ret = GmcBeginCheck(g_stmt, labelname, FULLTABLE);// 20250206适配持久化支持对账老化
    EXPECT_EQ(GMERR_OK, ret);
    
    // 落盘
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit(-1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启对账
    ret = GmcBeginCheck(g_stmt, labelname, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);

    // 清理
    GmcDropVertexLabel(g_stmt, "OP_T0");
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end.");
}


// 持久化不支持权限操作 设置权限后无法创建连接 GMERR_INSUFFICIENT_PRIVILEGE
TEST_F(PersistBasicTest, Basicspecifications_001_019_001_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    system("sh $TEST_HOME/tools/stop.sh -f");
    //权限校验改为强制模式
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"userPolicyMode=2\"");
    system("sh $TEST_HOME/tools/start.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 系统权限用来建表
    const char *sys_policy_file0 = "../../001_SpecificationBaseline_V5/012_Authentication/policyFile/systemPolicy1.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file0,
        g_connServer);
    ret = executeCommand(g_command, "successfully");
    EXPECT_EQ(GMERR_OK, ret);

    // 建立连接
    GmcConnT *g_conn = NULL;
    GmcStmtT *g_stmt = NULL;
    ret = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_INSUFFICIENT_PRIVILEGE, ret);

    // 释放连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    // 清理环境
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 持久化不支持横向隔离相关功能 不支持namespace
TEST_F(PersistBasicTest, Basicspecifications_001_019_001_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建连接
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    ret = testGmcConnect(&conn1, &stmt1, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateNamespace(stmt1, "tempspace", NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);

    // 释放连接
    ret = testGmcDisconnect(conn1, stmt1);
    EXPECT_EQ(GMERR_OK, ret);
    conn1 = NULL;
    stmt1 = NULL;

    // 清理环境
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 持久化不支持系统表的DDL/DML 已有用例覆盖Other_063_001_013

const char *g_configJson018 = "{\"max_record_count\" : 10000000}";
// 持久化不支持大对象  GMERR_FEATURE_NOT_SUPPORTED
TEST_F(PersistBasicTest, Basicspecifications_001_019_001_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = "V_1";
    char *bigobj_label_schema = NULL;
    system("gmadmin -cfgName compatibleV3 -cfgVal 0");
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建表
    uint32_t perTypeCount = 10;
    uint32_t totalCount = perTypeCount * 5;
    uint32_t childCount = 60;
    int32_t startVal = 10;
    readJanssonFile("./schema/VertexLabelTypical01.gmjson", &bigobj_label_schema);
    ret = GmcCreateVertexLabel(g_stmt, bigobj_label_schema, g_configJson018);
    ASSERT_EQ(GMERR_OK, ret);
    free(bigobj_label_schema);
    testGmcGetLastError();
    GtTypicalVertexCfgT vertexCfg = {startVal, totalCount, 10, childCount, 0, GMC_OPERATION_REPLACE};
    ret = GtReplaceTypicalVertex(g_stmt, vertexCfg);
    ASSERT_EQ(GMERR_OK, ret);// 持久化迭代三支持大对象
    // 断开连接
    // 落盘
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit(-1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t count;
    ret = GmcGetVertexCount(g_stmt, gLabelTypicalName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(perTypeCount * 5, count);// 持久化迭代三支持大对象
    // 清理
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    system("gmadmin -cfgName compatibleV3 -cfgVal 1");
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 持久化不支持savepoint 
TEST_F(PersistBasicTest, Basicspecifications_001_019_001_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建连接
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    GmcConnT *conn2 = NULL;
    GmcStmtT *stmt2 = NULL;
    AsyncUserDataT data1 = {0};
    AsyncUserDataT data2 = {0};
    ret = testGmcConnect(&conn1, &stmt1, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn2, &stmt2, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateNamespace(stmt1, "tempspace", NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);

    ret = GmcTransCreateSavepointAsync(conn1, "public", NULL, &data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放连接
    ret = testGmcDisconnect(conn1, stmt1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn2, stmt2);
    EXPECT_EQ(GMERR_OK, ret);
    conn1 = NULL;
    stmt1 = NULL;
    conn2 = NULL;
    stmt2 = NULL;

    // 清理环境
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end.");
}
const char *g_config020 = R"(
{
    "max_record_count":1000000,
    "defragmentation":false
})";

// 持久化不支持聚簇容器 持久化触发聚簇容器缩容 校验视图缩容失败
TEST_F(PersistBasicTest, Basicspecifications_001_019_001_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = RECORD_NUM_003;
    uint32_t expectNum = 0;
    uint32_t keyValue = 0;
    uint64_t count = 0;

    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"enableClusterHash=1\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"memCompactEnable=1\"");

    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 创建Vertex
    char *label_schema020 = NULL;
    readJanssonFile("schema/Vertex_20.gmjson", &label_schema020);
    ASSERT_NE((void *)NULL, label_schema020);
    ret = GmcCreateVertexLabel(g_stmt, label_schema020, g_config020);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(label_schema020);

    // 触发缩容
    const char *labelName = "Vertex_01";
    const char *keyName = "Vertex_pk";

    // 插入数据
    TestInsertVertexLabel(g_stmt, times, initValue, labelName);
    sleep(1);

    // 删除75%数据
    times = (RECORD_NUM_003 / 4) * 3;
    TestDeleteVertexLabel(g_stmt, keyName, times, initValue, labelName);

    // 等待缩容完成
    sleep(61);

    // 查询视图
    uint32_t compactCount = 0;
    GetClusteredStat("SCALE_IN_ACTUAL_BEGIN_COUNT", &compactCount);
    EXPECT_LE(compactCount, 1);

    // 清理环境
    ret = GmcDropVertexLabel(g_stmt, "Vertex_01");
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
