[{"type": "record", "name": "respool_label", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "resource", "nullable": false}, {"name": "F4", "type": "resource", "nullable": false}], "keys": [{"node": "respool_label", "name": "PK", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]