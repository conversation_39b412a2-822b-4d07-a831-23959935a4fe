/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * Description: 慢操作统计强化测试用例 -- 典型慢操作场景测试
 * Author: ynw
 * Create: Copyright (c) Huawei Technologies Co., Ltd. 2021-2022. All rights reserved.
 * History: 第二次改版
 */
#include "SlowOperationStatisticsEnhanced.h"
using namespace std;
#ifndef FAILED
#define FAILED 1
#else
#endif

#if defined(ENV_RTOSV2X)
    bool CONN_CHECK = false;
    int32_t INSERTNUM = 5;
#else
    bool CONN_CHECK = true;
    int32_t INSERTNUM = 1000;
#endif

#if defined(ENV_RTOSV2X)
    uint32_t NAMESPACE_CHECK = 1048784;
    bool NM_CHECK = false;
#else
    uint32_t NAMESPACE_CHECK = 1048577;
    bool NM_CHECK = true;
#endif

#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];
GmcConnT *gConn = NULL;
GmcStmtT *gStmt = NULL;
GmcConnT *g_conn[64];
GmcStmtT *g_stmt[64];

// 通用全局变量
const char *gLabelName = "VertexLabel";
const char *gLabelName_long = "VertexLabel_1234567890123456789012345678901234567890"
                                     "123456789012345678901234567890123456789012345678901234567890";
const char *gLabelConfigJson = R"({"max_record_num":100000})";
const char *IndexName_long = "LocalHashKey_1234567890123456789012345678901234567890"
                                    "1234567890123456789012345678901234567890123456789012345678901234567890";
const char *gLabelSchemaJson =
    R"([{
        "type":"record",
        "name":"VertexLabel",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"uint32", "nullable":false},
                {"name":"F3", "type":"uint32", "nullable":false},
                {"name":"F4", "type":"uint32", "nullable":false}
            ],
        "keys":
            [
                {
                    "node":"VertexLabel",
                    "name":"PrimaryKey",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                },
                {
                    "node":"VertexLabel",
                    "name":"LocalHashKey",
                    "fields":["F1"],
                    "index":{"type":"localhash"},
                    "constraints":{"unique":false}
                },
                {
                    "node":"VertexLabel",
                    "name":"HashClusterKey",
                    "fields":["F2"],
                    "index":{"type":"hashcluster"},
                    "constraints":{"unique":false}
                },
                {
                    "node":"VertexLabel",
                    "name":"LocalKey",
                    "fields":["F3"],
                    "index":{"type":"local"},
                    "constraints":{"unique":false}
                }
            ]
        }])";
char const *cfglogName = "enableLogFold";
int recoverSvalue = 1;
int changeSvalue = 0;

bool envAnos = false;
// 日志最新变更[2022-12-16]：1.所有日志校验时直接关闭所有折叠 2.用例结束时打开所有折叠 
// 3.下游环境校验时关闭其他所有模块输出日志
// 4.anos包使用hpecli控件控制输出路径做了变更，release包日志输出diag.log诊断日志
// 5.diag.log诊断日志当前暂时不做校验，诊断日志使用设备存在非用例日志和其他业务海量日志折叠问题
// 日志折叠关闭海量日志导致SOHO设备复位，以及对应日志形成压缩文件问题
class SlowOperationEnhanced001 : public testing::Test {
public:
    static void SetUpTestCase()
    {
        int ret;
        system("mkdir tmplog");
        system("sh $TEST_HOME/tools/modifyCfg.sh  \"isFastReadUncommitted=0\"");
        system("sh $TEST_HOME/tools/modifyCfg.sh  \"enableLogFold=0\"");
        system("sh $TEST_HOME/tools/modifyCfg.sh  \"longProcTimeThreshold=0\""); // logLengthMax
        system("${TEST_HOME}/tools/start.sh");
#if defined(ENV_RTOSV2X)
        system("chmod -R 777 /root"); // SOHO构建实质上就是777，减少日志中对权限报错条数干扰
        if ( 0 == access("/hpe/libdblog_adapter.so", 0)) {
            envAnos = false;
        } else {
            envAnos = true;
        }
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -s %s -cfgName %s -cfgVal %d\n", g_toolPath,
                 g_connServer, cfglogName, changeSvalue);
        system(g_command);
        memset(g_command, 0, sizeof(g_command));
        system("$TEST_HOME/../../../../../../bin/hpecli log level mod 4");
        system("$TEST_HOME/../../../../../../bin/hpecli log level 0 mod 0");
        system("$TEST_HOME/../../../../../../bin/hpecli log level 0 mod 1");
        system("$TEST_HOME/../../../../../../bin/hpecli log level 0 mod 2");
        system("$TEST_HOME/../../../../../../bin/hpecli log level 0 mod 3");
        system("$TEST_HOME/../../../../../../bin/hpecli log level 4 mod 4");
        system("$TEST_HOME/../../../../../../bin/hpecli log level mod 4");
#else
#endif
#if defined(ENV_RTOSV2) && (!RUN_SIMULATE)
        system("${TEST_HOME}/../../../../usr/local/bin/hpecli log level mod 4");
        system("$TEST_HOME/../../../../../../bin/hpecli log level 0 mod 0");
        system("$TEST_HOME/../../../../../../bin/hpecli log level 0 mod 1");
        system("$TEST_HOME/../../../../../../bin/hpecli log level 0 mod 2");
        system("$TEST_HOME/../../../../../../bin/hpecli log level 0 mod 3");
        system("$TEST_HOME/../../../../../../bin/hpecli log level 4 mod 4");
        system("${TEST_HOME}/../../../../usr/local/bin/hpecli log level mod 4");
#else
#endif
        ret = testEnvInit();
        ASSERT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        ASSERT_EQ(GMERR_OK, ret);
    }

    static void TearDownTestCase()
    {
        int ret;
        ret = close_epoll_thread();
        ASSERT_EQ(GMERR_OK, ret);
        ret = testEnvClean();
        EXPECT_EQ(GMERR_OK, ret);

        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
#if defined(ENV_RTOSV2X)
        memset(g_command, 0, sizeof(g_command));
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -s %s -cfgName %s -cfgVal %d\n", g_toolPath,
                 g_connServer, cfglogName, recoverSvalue);
        system(g_command);
        memset(g_command, 0, sizeof(g_command));
        system("$TEST_HOME/../../../../../../bin/hpecli log level mod 4");
        system("$TEST_HOME/../../../../../../bin/hpecli log level 2 mod 0");
        system("$TEST_HOME/../../../../../../bin/hpecli log level 2 mod 1");
        system("$TEST_HOME/../../../../../../bin/hpecli log level 2 mod 2");
        system("$TEST_HOME/../../../../../../bin/hpecli log level 2 mod 3");
        system("$TEST_HOME/../../../../../../bin/hpecli log level 2 mod 4");
        system("$TEST_HOME/../../../../../../bin/hpecli log level mod 4");
#else
#endif
#if defined(ENV_RTOSV2) && (!RUN_SIMULATE)
        system("${TEST_HOME}/../../../../usr/local/bin/hpecli log level mod 4");
        system("$TEST_HOME/../../../../../../bin/hpecli log level 2 mod 0");
        system("$TEST_HOME/../../../../../../bin/hpecli log level 2 mod 1");
        system("$TEST_HOME/../../../../../../bin/hpecli log level 2 mod 2");
        system("$TEST_HOME/../../../../../../bin/hpecli log level 2 mod 3");
        system("$TEST_HOME/../../../../../../bin/hpecli log level 2 mod 4");
        system("${TEST_HOME}/../../../../usr/local/bin/hpecli log level mod 4");
#else
#endif
    }

    virtual void SetUp()
    {
        AW_CHECK_LOG_BEGIN();
    }

    virtual void TearDown()
    {
        AW_CHECK_LOG_END();
    }
};

// 默认配置, 写入大量相同 localhash  索引的数据, 再通过非唯一 localhash 更新数据
TEST_F(SlowOperationEnhanced001, DFX_053_001_001)
{
#if defined(ENV_RTOSV2X)
    GtExecSystemCmd("echo [CLEAR_LOG] > ${TEST_HOME}/testcases/07_DFX/"
                    "053_SlowOperationEnhanced/Slow53_01.log");
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName enableLogFold -cfgVal 0\n", g_toolPath);
    system(g_command);
#else
    ClearSlowLog();
#endif
    int ret;
    char startHpeTime[30];
    char endHpeTime[30];
    GetHpeTime(startHpeTime, 30);
    
    int len1 = strlen(startHpeTime);
    startHpeTime[len1 - 1] = '\0';

    int32_t startPrimaryKey = 10;
    int32_t properyVal = 8;
    int32_t insertNum = INSERTNUM;
    ret = testGmcConnect_slow(&gConn, &gStmt, 10000); // 适配hash缓存造成性能提升
    ASSERT_EQ(GMERR_OK, ret);
    char const *fold_cfg_name = "enableLogFold";
    int modify_fold_mode_value = 0;
    int recover_fold_mode_value = 3;

    do
    {
        GmcDropVertexLabel(gStmt, gLabelName);
        ret = GmcCreateVertexLabel(gStmt, gLabelSchemaJson, gLabelConfigJson);
        BREAK_IFERR(ret);
        // 写入数据
        for (int i = startPrimaryKey; i < startPrimaryKey + insertNum; i++)
        {
            ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_INSERT);
            BREAK_IFERR(ret);
            ret = GtSetInsertVertexProperyty(gStmt, i, properyVal, properyVal, properyVal, properyVal);
            BREAK_IFERR(ret);
            ret = GmcExecute(gStmt);
            BREAK_IFERR(ret);
            ret = GtCheckAffectRows(gStmt, 1);
            BREAK_IFERR(ret);
        }
        BREAK_IFERR(ret);

        // 通过 localhash key 更新数据
        ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_UPDATE);
        BREAK_IFERR(ret);
        int32_t updateVal = 128;
        ret = GtSetUpdateVertexProperyty(gStmt, updateVal, updateVal, updateVal, updateVal);
        BREAK_IFERR(ret);
        int32_t keyVal = properyVal;
        ret = GmcSetIndexKeyValue(gStmt, 0, GMC_DATATYPE_INT32, &keyVal, sizeof(keyVal));
        BREAK_IFERR(ret);

        ret = GmcSetIndexKeyName(gStmt, "LocalHashKey");
        BREAK_IFERR(ret);
        double startTime = GtGetCurrentTime();
        ret = GmcExecute(gStmt);
        double spendTime = GtGetCurrentTime() - startTime;
        printf("\n  [INFO] Excute update vertex by local hash key, spend %.6f ms \n", spendTime);
        BREAK_IFERR(ret);
        ret = GtCheckAffectRows(gStmt, insertNum);
        BREAK_IFERR(ret);

        uint32_t server_times = 0;
        sleep(2);
        GetHpeTimeInterval(endHpeTime, 30);
        
        int len2 = strlen(endHpeTime);

        endHpeTime[len2 - 1] = '\0';
        char Logsearch[1024];
        if (g_envType == 2) {
            system("hpecli log dump");
        }
        snprintf(Logsearch, 1024, "'/%s/,/%s/p'", startHpeTime, endHpeTime);
        

        // GtCheckServerSlowLog ( 整体耗时 , 操作类型 ， 命名空间 ， 表名 ， 连接编号 ， 连接类型 ， 索引种类 ，
        // 空间开关 ， 名字开关 ， 连接开关 ，
        // 复数日志开关 ， 操作数开关 ， 最长信息开关 ， 默认操作数 ， 默认最长信息 )
        printf("\n  [INFO]CHECH SERVER LOG \n");
        if (g_envType == 0) {
            ret = GtCheckServerSlowLog(&server_times, (char *)" Update Vertex", NAMESPACE_CHECK, " \"VertexLabel\"", 0,
                                           (char *)" SYNC", (char *)" \"LocalHashKey\"", true, true,
                                           CONN_CHECK, true, true, true, true, true, 1, (char *)" qry_exec_time");
        } else if (envAnos){
            ret = GtCheckServerSlowLog_new(&server_times, (char *)" Update Vertex", NAMESPACE_CHECK, " \"VertexLabel\"", 0,
                                           (char *)" SYNC", (char *)" \"LocalHashKey\"", NM_CHECK, true,
                                           CONN_CHECK, true, true, true, true, true, 1, (char *)" qry_exec_time",
                                           (char *)"Vertex", 2, 0, true, (char *)Logsearch);
        } else if (!envAnos){
            ret = GtCheckServerSlowLog_new(&server_times, (char *)" Update Vertex", NAMESPACE_CHECK, " \"VertexLabel\"", 0,
                                           (char *)" SYNC", (char *)" \"LocalHashKey\"", NM_CHECK, true,
                                           CONN_CHECK, true, true, true, true, true, 1, (char *)" qry_exec_time",
                                           (char *)"Vertex", 2, 0, true, (char *)Logsearch);
        }
        printf("\n  [INFO]total_time is :%d\n", server_times);
        if (server_times < 0 || server_times > (spendTime + 8) * 1000)
        {
            EXPECT_EQ(0, 1);
            printf("[INFO]server total time is not normal:%d\n", server_times);
        }
        if (g_envType == 0) {
            printf("\n  [INFO]CHECH CLIENT NORMAL LOG \n");
            uint32_t opWholeTime = 0;
            uint32_t ConcatMsgTime = 0;
            uint32_t RspTransmissionTime = 0;
            uint32_t cltPostOpTime = 0;

            // GtCheckClientSlowLog_KV_Graph ( 操作类型 ， 命名空间 ， 表名 ， 连接编号 ， 索引种类 ， 索引开关 ， 空间开关 ，
            // 名字开关 ， 连接开关 ， 复数日志开关 ，整体时间 ， 连接消息时间 ， 接受时间 ， 控制时间 )

            GtCheckClientSlowLog_KV_Graph((char *)" Update Vertex", NAMESPACE_CHECK,
                                            (char *)" \"VertexLabel\"", 0, " \"LocalHashKey\"", true,
                                        NM_CHECK, true, CONN_CHECK, true, &opWholeTime, &ConcatMsgTime,
                                        &RspTransmissionTime, &cltPostOpTime);
            printf("opWholeTime is :%d\n", opWholeTime);
            printf("ConcatMsgTime is :%d\n", ConcatMsgTime);
            printf("RspTransmissionTime is :%d\n", RspTransmissionTime);
            printf("cltPostOpTime is :%d\n", cltPostOpTime);
        }
    } while (0);
    EXPECT_EQ(GMERR_OK, ret);

    GmcDropVertexLabel(gStmt, gLabelName);
    ret = testGmcDisconnect(gConn, gStmt);
    ASSERT_EQ(GMERR_OK, ret);
    sleep(5);
#if defined(CLOSE)&& CLOSE==1
    GtCheckLog_Folded("long", SERVER_SLOW_OPER_LOG_PATH, 6);
#endif
#if defined(ENV_RTOSV2X)
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName enableLogFold -cfgVal 1\n", g_toolPath);
    system(g_command);
#endif
    ClearSlowLog();
}

// 默认配置, 写入大量相同 localhash 索引的数据, 再通过非唯一 localhash 扫描数据
TEST_F(SlowOperationEnhanced001, DFX_053_001_002)
{
#if defined(ENV_RTOSV2X)
    GtExecSystemCmd("echo [CLEAR_LOG] > ${TEST_HOME}/testcases/07_DFX/"
                    "053_SlowOperationEnhanced/Slow53_01.log");
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName enableLogFold -cfgVal 0\n", g_toolPath);
    system(g_command);
#else
    ClearSlowLog();
#endif
    int ret;
    char startHpeTime[30];
    char endHpeTime[30];
    GetHpeTime(startHpeTime, 30);
    
    int len1 = strlen(startHpeTime);
    startHpeTime[len1 - 1] = '\0';
    int32_t startPrimaryKey = 10;
    int32_t properyVal = 8;
    int32_t insertNum = INSERTNUM;
    ret = testGmcConnect_slow(&gConn, &gStmt, 0);
    ASSERT_EQ(GMERR_OK, ret);
    char const *fold_cfg_name = "enableLogFold";
    int modify_fold_mode_value = 0;
    int recover_fold_mode_value = 3;

    do
    {
        ret = GmcCreateVertexLabel(gStmt, gLabelSchemaJson, gLabelConfigJson);
        BREAK_IFERR(ret);

        // 写入数据
        for (int i = startPrimaryKey; i < startPrimaryKey + insertNum; i++)
        {
            ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_INSERT);
            BREAK_IFERR(ret);
            ret = GtSetInsertVertexProperyty(gStmt, i, properyVal, properyVal, properyVal, properyVal);
            BREAK_IFERR(ret);
            ret = GmcExecute(gStmt);
            BREAK_IFERR(ret);
            ret = GtCheckAffectRows(gStmt, 1);
            BREAK_IFERR(ret);
        }
        BREAK_IFERR(ret);

        // 通过 localhash key 扫描数据
        ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_SCAN);
        BREAK_IFERR(ret);
        int32_t keyVal = properyVal;
        ret = GmcSetIndexKeyValue(gStmt, 0, GMC_DATATYPE_INT32, &keyVal, sizeof(keyVal));
        BREAK_IFERR(ret);
        ret = GmcSetIndexKeyName(gStmt, "LocalHashKey");
        BREAK_IFERR(ret);
        double startTime = GtGetCurrentTime();
        ret = GmcExecute(gStmt);
        double spendTime = GtGetCurrentTime() - startTime;
        printf("\n  [INFO] Excute delete vertex by local hash key, spend %.6f ms \n", spendTime);
        BREAK_IFERR(ret);

        bool isFinished = false;
        int32_t scanCount = 0;
        while (!isFinished)
        {
            ret = GmcFetch(gStmt, &isFinished);
            BREAK_IFERR(ret);
            if (isFinished)
            {
                break;
            }

            ret = GtGetAndCheckProperty(gStmt, properyVal, properyVal, properyVal, properyVal);
            BREAK_IFERR(ret);
            scanCount++;
        }
        BREAK_IFERR(ret);

        if (scanCount != insertNum)
        {
            TEST_ERROR("scan failed, scanCount = %d, insertNum = %d", scanCount, insertNum);
            ret = FAILED;
            break;
        }
        uint32_t server_times = 0;
        sleep(2);
        GetHpeTimeInterval(endHpeTime, 30);
        
        int len2 = strlen(endHpeTime);

        endHpeTime[len2 - 1] = '\0';
        char Logsearch[1024];
        if (g_envType == 2) {
            system("hpecli log dump");
        }
        snprintf(Logsearch, 1024, "'/%s/,/%s/p'", startHpeTime, endHpeTime);
        
        // 此处检查Insert的服务日志，因为没有其他耗时操作，进行了直连读
        // GtCheckServerSlowLog ( 整体耗时 , 操作类型 ， 命名空间 ， 表名 ， 连接编号 ， 连接类型 ， 索引种类 ，
        // 空间开关 ， 名字开关 ， 连接开关 ，
        // 复数日志开关 ， 操作数开关 ， 最长信息开关 ， 默认操作数 ， 默认最长信息 )
        printf("\n  [INFO]CHECH SERVER LOG \n");
        if (g_envType == 0) {
            ret = GtCheckServerSlowLog(&server_times, (char *)" Insert Vertex", NAMESPACE_CHECK, " \"VertexLabel\"", 0,
                                    (char *)" SYNC", (char *)" \"\"", true, true,
                                    CONN_CHECK, true, true, true, true, true, 1, (char *)" qry_exec_time");
        } else if (envAnos){
            ret = GtCheckServerSlowLog_new(&server_times, (char *)" Insert Vertex", NAMESPACE_CHECK, " \"VertexLabel\"", 0,
                                    (char *)" SYNC", (char *)" \"\"", NM_CHECK, true,
                                    CONN_CHECK, true, true, true, true, true, 1, (char *)" qry_exec_time",
                                    (char *)"Vertex", 2, 0, true, (char *)Logsearch);
        } else if (!envAnos){
            ret = GtCheckServerSlowLog_new(&server_times, (char *)" Insert Vertex", NAMESPACE_CHECK, " \"VertexLabel\"", 0,
                                    (char *)" SYNC", (char *)" \"\"", NM_CHECK, true,
                                    CONN_CHECK, true, true, true, true, true, 1, (char *)" qry_exec_time",
                                    (char *)"Vertex", 2, 0, true, (char *)Logsearch);
        }
        printf("\n  [INFO]total_time is :%d\n", server_times);
        if (server_times < 0 || server_times > (spendTime + 8) * 1000)
        {
            EXPECT_EQ(0, 1);
            printf("[INFO]server total time is not normal:%d\n", server_times);
        }
        printf("\n  [INFO]CHECH CLIENT DIRECT LOG \n");
        // 此处检查客户端的直连读日志
        uint32_t check_directtimes = 0;
        // GtCheckClientSlowLog_Direct ( 命名空间 ， 表名 ， 空间开关 ， 名字开关 ， 整体时间 , 索引名 ， 索引名开关)
        ret = GtCheckClientSlowLog_Direct(NAMESPACE_CHECK, " \"VertexLabel\"", NM_CHECK,
        true, &check_directtimes, (char *)" \"LocalHashKey\"", true);
        printf("Direct opWholeTime is :%d\n", check_directtimes);

        LOG_IFERR(ret, "delete vertex by local hash key, spend %.6f ms", spendTime);
        BREAK_IFERR(ret);

    } while (0);
    EXPECT_EQ(GMERR_OK, ret);

    GmcDropVertexLabel(gStmt, gLabelName);
    ret = testGmcDisconnect(gConn, gStmt);
    ASSERT_EQ(GMERR_OK, ret);

#if defined(ENV_RTOSV2X)
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName enableLogFold -cfgVal 1\n", g_toolPath);
    system(g_command);
#endif
    ClearSlowLog();
}
// 默认配置 , 写入大量相同 hashcluster 索引的数据 ,  再通过非唯一 hashcluster删除数据

TEST_F(SlowOperationEnhanced001, DFX_053_001_003)
{
#if defined(ENV_RTOSV2X)
    GtExecSystemCmd("echo [CLEAR_LOG] > ${TEST_HOME}/testcases/07_DFX/"
                    "053_SlowOperationEnhanced/Slow53_01.log");
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName enableLogFold -cfgVal 0\n", g_toolPath);
    system(g_command);
#else
    ClearSlowLog();
#endif
    int ret;
    char startHpeTime[30];
    char endHpeTime[30];
    GetHpeTime(startHpeTime, 30);
    
    int len1 = strlen(startHpeTime);
    startHpeTime[len1 - 1] = '\0';

    int32_t startPrimaryKey = 10;
    int32_t properyVal = 8;
    int32_t insertNum = INSERTNUM;
    double spendTime = 0;
    ret = testGmcConnect_slow(&gConn, &gStmt, 0);
    ASSERT_EQ(GMERR_OK, ret);
    char const *fold_cfg_name = "enableLogFold";
    int modify_fold_mode_value = 0;
    int recover_fold_mode_value = 3;

    do
    {
        ret = GmcCreateVertexLabel(gStmt, gLabelSchemaJson, gLabelConfigJson);
        BREAK_IFERR(ret);

        // 写入数据
        for (int i = startPrimaryKey; i < startPrimaryKey + insertNum; i++)
        {
            ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_INSERT);
            BREAK_IFERR(ret);
            ret = GtSetInsertVertexProperyty(gStmt, i, properyVal, properyVal, properyVal, properyVal);
            BREAK_IFERR(ret);
            ret = GmcExecute(gStmt);
            BREAK_IFERR(ret);
            ret = GtCheckAffectRows(gStmt, 1);
            BREAK_IFERR(ret);
        }
        BREAK_IFERR(ret);
        sleep(5);
        // 通过 localhash key 删除数据
        ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_DELETE);
        BREAK_IFERR(ret);
        int32_t keyVal = properyVal;
        ret = GmcSetIndexKeyValue(gStmt, 0, GMC_DATATYPE_INT32, &keyVal, sizeof(keyVal));
        BREAK_IFERR(ret);

        ret = GmcSetIndexKeyName(gStmt, "LocalHashKey");
        BREAK_IFERR(ret);
        double startTime = GtGetCurrentTime();
        ret = GmcExecute(gStmt);
        spendTime = GtGetCurrentTime() - startTime;
        printf("\n  [INFO] Excute delete vertex by local hash key, spend %.6f ms \n", spendTime);
        BREAK_IFERR(ret);
        ret = GtCheckAffectRows(gStmt, insertNum);
        BREAK_IFERR(ret);

        LOG_IFERR(ret, "delete vertex by local hash key, spend %.6f ms", spendTime);
        BREAK_IFERR(ret);
    } while (0);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t server_times = 0;
    sleep(5);
    GetHpeTimeInterval(endHpeTime, 30);
    
    int len2 = strlen(endHpeTime);

    endHpeTime[len2 - 1] = '\0';
    char Logsearch[1024];
    if (g_envType == 2) {
        system("hpecli log dump");
    }
    snprintf(Logsearch, 1024, "'/%s/,/%s/p'", startHpeTime, endHpeTime);
    
    // GtCheckServerSlowLog ( 整体耗时 , 操作类型 ， 命名空间 ， 表名 ， 连接编号 ， 连接类型 ， 索引种类 ，
    // 空间开关 ， 名字开关 ， 连接开关 ，
    // 复数日志开关 ， 操作数开关 ， 最长信息开关 ， 默认操作数 ， 默认最长信息 )
    printf("\n  [INFO]CHECH SERVER LOG \n");
    if (g_envType == 0) {
        ret = GtCheckServerSlowLog(&server_times, (char *)" Delete Vertex", NAMESPACE_CHECK, " \"VertexLabel\"", 0,
                                (char *)" SYNC", (char *)" \"LocalHashKey\"", true, true,
                                CONN_CHECK, true, true, true, true, true, 1, (char *)" qry_exec_time");
    } else if (envAnos){
        ret = GtCheckServerSlowLog_new(&server_times, (char *)" Delete Vertex", NAMESPACE_CHECK, " \"VertexLabel\"", 0,
                                (char *)" SYNC", (char *)" \"LocalHashKey\"", NM_CHECK, true,
                                CONN_CHECK, true, true, true, true, true, 1, (char *)" qry_exec_time",
                                (char *)"Vertex", 2, 0, true, (char *)Logsearch);
    } else if (!envAnos){
        ret = GtCheckServerSlowLog_new(&server_times, (char *)" Delete Vertex", NAMESPACE_CHECK, " \"VertexLabel\"", 0,
                                (char *)" SYNC", (char *)" \"LocalHashKey\"", NM_CHECK, true,
                                CONN_CHECK, true, true, true, true, true, 1, (char *)" qry_exec_time",
                                (char *)"Vertex", 2, 0, true, (char *)Logsearch);
    }
    printf("\n  [INFO]total_time is :%d\n", server_times);
    if (server_times < 0 || server_times > (spendTime + 8) * 1000)
    {
        EXPECT_EQ(0, 1);
        printf("[INFO]server total time is not normal:%d\n", server_times);
    }
    printf("\n  [INFO]CHECH CLIENT NORMAL LOG \n");
    uint32_t opWholeTime = 0;
    uint32_t ConcatMsgTime = 0;
    uint32_t RspTransmissionTime = 0;
    uint32_t cltPostOpTime = 0;

    // GtCheckClientSlowLog_KV_Graph ( 操作类型 ， 命名空间 ， 表名 ， 连接编号 ， 索引种类 ， 索引开关 ， 空间开关 ，
    // 名字开关 ， 连接开关 ， 复数日志开关 ，整体时间 ， 连接消息时间 ， 接受时间 ， 控制时间 )

    GtCheckClientSlowLog_KV_Graph((char *)" Delete Vertex", NAMESPACE_CHECK,
                                (char *)" \"VertexLabel\"", 0, " \"LocalHashKey\"", true,
                                  NM_CHECK, true, CONN_CHECK, true, &opWholeTime, &ConcatMsgTime,
                                  &RspTransmissionTime, &cltPostOpTime);
    printf("opWholeTime is :%d\n", opWholeTime);
    printf("ConcatMsgTime is :%d\n", ConcatMsgTime);
    printf("RspTransmissionTime is :%d\n", RspTransmissionTime);
    printf("cltPostOpTime is :%d\n", cltPostOpTime);

    GmcDropVertexLabel(gStmt, gLabelName);
    ret = testGmcDisconnect(gConn, gStmt);
    ASSERT_EQ(GMERR_OK, ret);

#if defined(ENV_RTOSV2X)
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName enableLogFold -cfgVal 1\n", g_toolPath);
    system(g_command);
#endif
    ClearSlowLog();
}

// 默认配置, 写入大量相同 hashcluster索引的数据, 再通过非唯一 hashcluster更新数据
TEST_F(SlowOperationEnhanced001, DFX_053_001_004)
{
#if defined(ENV_RTOSV2X)
    GtExecSystemCmd("echo [CLEAR_LOG] > ${TEST_HOME}/testcases/07_DFX/"
                    "053_SlowOperationEnhanced/Slow53_01.log");
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName enableLogFold -cfgVal 0\n", g_toolPath);
    system(g_command);
#else
    ClearSlowLog();
#endif
    int ret;
    char startHpeTime[30];
    char endHpeTime[30];
    GetHpeTime(startHpeTime, 30);
    
    int len1 = strlen(startHpeTime);
    startHpeTime[len1 - 1] = '\0';

    int32_t startPrimaryKey = 10;
    int32_t properyVal = 8;
    int32_t insertNum = INSERTNUM;
    double spendTime = 0;
    ret = testGmcConnect_slow(&gConn, &gStmt, 0);
    ASSERT_EQ(GMERR_OK, ret);
    char const *fold_cfg_name = "enableLogFold";
    int modify_fold_mode_value = 0;
    int recover_fold_mode_value = 3;

    do
    {
        ret = GmcCreateVertexLabel(gStmt, gLabelSchemaJson, gLabelConfigJson);
        BREAK_IFERR(ret);

        // 写入数据
        for (int i = startPrimaryKey; i < startPrimaryKey + insertNum; i++)
        {
            ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_INSERT);
            BREAK_IFERR(ret);
            ret = GtSetInsertVertexProperyty(gStmt, i, properyVal, properyVal, properyVal, properyVal);
            BREAK_IFERR(ret);
            ret = GmcExecute(gStmt);
            BREAK_IFERR(ret);
            ret = GtCheckAffectRows(gStmt, 1);
            BREAK_IFERR(ret);
        }
        BREAK_IFERR(ret);
        sleep(5);
        // 通过 hashcluster key 更新数据
        ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_UPDATE);
        BREAK_IFERR(ret);
        int32_t updateVal = 128;
        ret = GtSetUpdateVertexProperyty(gStmt, updateVal, updateVal, updateVal, updateVal);
        BREAK_IFERR(ret);
        int32_t keyVal = properyVal;
        ret = GmcSetIndexKeyValue(gStmt, 0, GMC_DATATYPE_UINT32, &keyVal, sizeof(keyVal));
        BREAK_IFERR(ret);

        ret = GmcSetIndexKeyName(gStmt, "HashClusterKey");
        BREAK_IFERR(ret);
        double startTime = GtGetCurrentTime();
        ret = GmcExecute(gStmt);
        spendTime = GtGetCurrentTime() - startTime;
        printf("\n  [INFO] Excute update vertex by HashClusterKey, spend %.6f ms \n", spendTime);
        BREAK_IFERR(ret);
        ret = GtCheckAffectRows(gStmt, insertNum);
        BREAK_IFERR(ret);
        // 检查日志
    } while (0);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t server_times = 0;
    sleep(2);
    GetHpeTimeInterval(endHpeTime, 30);
    
    int len2 = strlen(endHpeTime);

    endHpeTime[len2 - 1] = '\0';
    char Logsearch[1024];
    if (g_envType == 2) {
        system("hpecli log dump");
    }
    snprintf(Logsearch, 1024, "'/%s/,/%s/p'", startHpeTime, endHpeTime);
    
    // GtCheckServerSlowLog ( 整体耗时 , 操作类型 ， 命名空间 ， 表名 ， 连接编号 ， 连接类型 ， 索引种类 ，
    // 空间开关 ， 名字开关 ， 连接开关 ，
    // 复数日志开关 ， 操作数开关 ， 最长信息开关 ， 默认操作数 ， 默认最长信息 )
    printf("\n  [INFO]CHECK SERVER LOG \n");
    if (g_envType == 0) {
        ret = GtCheckServerSlowLog(&server_times, (char *)" Update Vertex", NAMESPACE_CHECK, " \"VertexLabel\"", 0,
                                (char *)" SYNC", (char *)" \"HashClusterKey\"", true, true,
                                CONN_CHECK, true, true, true, true, true, 1, (char *)" qry_exec_time");
    } else if (envAnos){
        ret = GtCheckServerSlowLog_new(&server_times, (char *)" Update Vertex", NAMESPACE_CHECK, " \"VertexLabel\"", 0,
                                (char *)" SYNC", (char *)" \"HashClusterKey\"", NM_CHECK, true,
                                CONN_CHECK, true, true, true, true, true, 1, (char *)" qry_exec_time",
                                (char *)"Vertex", 2, 0, true, (char *)Logsearch);
    } else if (!envAnos){
        ret = GtCheckServerSlowLog_new(&server_times, (char *)" Update Vertex", NAMESPACE_CHECK, " \"VertexLabel\"", 0,
                                (char *)" SYNC", (char *)" \"HashClusterKey\"", NM_CHECK, true,
                                CONN_CHECK, true, true, true, true, true, 1, (char *)" qry_exec_time",
                                (char *)"Vertex", 2, 0, true, (char *)Logsearch);
    }
    printf("\n  [INFO]total_time is :%d\n", server_times);
    if (server_times < 0 || server_times > (spendTime + 8) * 1000)
    {
        EXPECT_EQ(0, 1);
        printf("[INFO]server total time is not normal:%d\n", server_times);
    }

    printf("\n  [INFO]CHECH CLIENT NORMAL LOG \n");
    uint32_t opWholeTime = 0;
    uint32_t ConcatMsgTime = 0;
    uint32_t RspTransmissionTime = 0;
    uint32_t cltPostOpTime = 0;

    // GtCheckClientSlowLog_KV_Graph ( 操作类型 ， 命名空间 ， 表名 ， 连接编号 ， 索引种类 ， 索引开关 ， 空间开关 ，
    // 名字开关 ， 连接开关 ， 复数日志开关 ，整体时间 ， 连接消息时间 ， 接受时间 ， 控制时间 )

    GtCheckClientSlowLog_KV_Graph((char *)" Update Vertex", NAMESPACE_CHECK,
                                    (char *)" \"VertexLabel\"", 0, " \"HashClusterKey\"", true,
                                  NM_CHECK, true, CONN_CHECK, true, &opWholeTime, &ConcatMsgTime,
                                  &RspTransmissionTime, &cltPostOpTime);
    printf("opWholeTime is :%d\n", opWholeTime);
    printf("ConcatMsgTime is :%d\n", ConcatMsgTime);
    printf("RspTransmissionTime is :%d\n", RspTransmissionTime);
    printf("cltPostOpTime is :%d\n", cltPostOpTime);

    GmcDropVertexLabel(gStmt, gLabelName);
    ret = testGmcDisconnect(gConn, gStmt);
    ASSERT_EQ(GMERR_OK, ret);

#if defined(ENV_RTOSV2X)
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName enableLogFold -cfgVal 1\n", g_toolPath);
    system(g_command);
#endif
    ClearSlowLog();
}

// 默认配置, 写入大量相同 hashcluster索引的数据, 再通过非唯一 hashcluster扫描数据
TEST_F(SlowOperationEnhanced001, DFX_053_001_005)
{
#if defined(ENV_RTOSV2X)
    GtExecSystemCmd("echo [CLEAR_LOG] > ${TEST_HOME}/testcases/07_DFX/"
                    "053_SlowOperationEnhanced/Slow53_01.log");
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName enableLogFold -cfgVal 0\n", g_toolPath);
    system(g_command);
#else
    ClearSlowLog();
#endif
    int ret;
    char startHpeTime[30];
    char endHpeTime[30];
    GetHpeTime(startHpeTime, 30);
    
    int len1 = strlen(startHpeTime);
    startHpeTime[len1 - 1] = '\0';

    int32_t startPrimaryKey = 10;
    int32_t properyVal = 8;
    int32_t insertNum = INSERTNUM;
    double spendTime = 0;
    ret = testGmcConnect_slow(&gConn, &gStmt, 10);
    ASSERT_EQ(GMERR_OK, ret);
    char const *fold_cfg_name = "enableLogFold";
    int modify_fold_mode_value = 0;
    int recover_fold_mode_value = 3;

    do
    {
        ret = GmcCreateVertexLabel(gStmt, gLabelSchemaJson, gLabelConfigJson);
        BREAK_IFERR(ret);

        // 写入数据
        for (int i = startPrimaryKey; i < startPrimaryKey + insertNum; i++)
        {
            ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_INSERT);
            BREAK_IFERR(ret);
            ret = GtSetInsertVertexProperyty(gStmt, i, properyVal, properyVal, properyVal, properyVal);
            BREAK_IFERR(ret);
            ret = GmcExecute(gStmt);
            BREAK_IFERR(ret);
            ret = GtCheckAffectRows(gStmt, 1);
            BREAK_IFERR(ret);
        }
        BREAK_IFERR(ret);

        // 通过 hashcluster key 扫描数据
        ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_SCAN);
        BREAK_IFERR(ret);
        int32_t keyVal = properyVal;
        ret = GmcSetIndexKeyValue(gStmt, 0, GMC_DATATYPE_UINT32, &keyVal, sizeof(keyVal));
        BREAK_IFERR(ret);

        ret = GmcSetIndexKeyName(gStmt, "HashClusterKey");
        BREAK_IFERR(ret);
        double startTime = GtGetCurrentTime();
        ret = GmcExecute(gStmt);
        spendTime = GtGetCurrentTime() - startTime;
        printf("\n  [INFO]scan vertex by hash cluster key, spend %.6f ms", spendTime);
        BREAK_IFERR(ret);

        bool isFinished = false;
        int32_t scanCount = 0;
        while (!isFinished)
        {
            ret = GmcFetch(gStmt, &isFinished);
            BREAK_IFERR(ret);
            if (isFinished)
            {
                break;
            }

            ret = GtGetAndCheckProperty(gStmt, properyVal, properyVal, properyVal, properyVal);
            BREAK_IFERR(ret);
            scanCount++;
        }
        BREAK_IFERR(ret);

        if (scanCount != insertNum)
        {
            TEST_ERROR("scan failed, scanCount = %d, insertNum = %d", scanCount, insertNum);
            ret = FAILED;
            break;
        }

        // 检查日志 (扫描较快, 未触发慢操作日志打印)

    } while (0);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t server_times = 0;
    sleep(2);
    GetHpeTimeInterval(endHpeTime, 30);
    
    int len2 = strlen(endHpeTime);

    endHpeTime[len2 - 1] = '\0';
    char Logsearch[1024];
    if (g_envType == 2) {
        system("hpecli log dump");
    }
    snprintf(Logsearch, 1024, "'/%s/,/%s/p'", startHpeTime, endHpeTime);
    
    // GtCheckServerSlowLog ( 整体耗时 , 操作类型 ， 命名空间 ， 表名 ， 连接编号 ， 连接类型 ， 索引种类 ，
    // 空间开关 ， 名字开关 ， 连接开关 ，
    // 复数日志开关 ， 操作数开关 ， 最长信息开关 ， 默认操作数 ， 默认最长信息 )
    printf("\n  [INFO]CHECK SERVER LOG \n");
    if (g_envType == 0) {
        ret = GtCheckServerSlowLog(&server_times, (char *)" Insert Vertex", NAMESPACE_CHECK, " \"VertexLabel\"", 0,
                                (char *)" SYNC", (char *)" \"\"",
                                true, true,
                                CONN_CHECK, true, true, true, true, true, 1, (char *)" qry_exec_time");
    } else if (envAnos){
        ret = GtCheckServerSlowLog_new(&server_times, (char *)" Insert Vertex", NAMESPACE_CHECK, " \"VertexLabel\"", 0,
                                (char *)" SYNC", (char *)" \"\"",
                                NM_CHECK, true,
                                CONN_CHECK, true, true, true, true, true, 1, (char *)" qry_exec_time",
                                (char *)"Vertex", 2, 0, true, (char *)Logsearch);
    } else if (!envAnos){
        ret = GtCheckServerSlowLog_new(&server_times, (char *)" Insert Vertex", NAMESPACE_CHECK, " \"VertexLabel\"", 0,
                                (char *)" SYNC", (char *)" \"\"",
                                NM_CHECK, true,
                                CONN_CHECK, true, true, true, true, true, 1, (char *)" qry_exec_time",
                                (char *)"Vertex", 2, 0, true, (char *)Logsearch);
    }
    printf("\n  [INFO]total_time is :%d\n", server_times);
    if (server_times < 0 || server_times > (spendTime + 8) * 1000)
    {
        EXPECT_EQ(0, 1);
        printf("[INFO]server total time is not normal:%d\n", server_times);
    }

    printf("\n  [INFO]CHECH CLIENT DIRECT LOG \n");
    // 此处检查客户端的直连读日志
    uint32_t check_directtimes = 0;
    // GtCheckClientSlowLog_Direct ( 命名空间 ， 表名 ， 空间开关 ， 名字开关 ， 整体时间 , 索引名 ， 索引名开关)
    ret = GtCheckClientSlowLog_Direct(NAMESPACE_CHECK, " \"VertexLabel\"", NM_CHECK, true,
                    &check_directtimes, (char *)" \"HashClusterKey\"", true);
    printf("Direct opWholeTime is :%d\n", check_directtimes);

    GmcDropVertexLabel(gStmt, gLabelName);
    ret = testGmcDisconnect(gConn, gStmt);
    ASSERT_EQ(GMERR_OK, ret);

#if defined(ENV_RTOSV2X)
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName enableLogFold -cfgVal 1\n", g_toolPath);
    system(g_command);
#endif
    ClearSlowLog();
}

// 默认配置, 写入大量相同 hashcluster索引的数据, 再通过非唯一 hashcluster删除数据
TEST_F(SlowOperationEnhanced001, DFX_053_001_006)
{
#if defined(ENV_RTOSV2X)
    GtExecSystemCmd("echo [CLEAR_LOG] > ${TEST_HOME}/testcases/07_DFX/"
                    "053_SlowOperationEnhanced/Slow53_01.log");
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName enableLogFold -cfgVal 0\n", g_toolPath);
    system(g_command);
#else
    ClearSlowLog();
#endif
    int ret;
    char startHpeTime[30];
    char endHpeTime[30];
    GetHpeTime(startHpeTime, 30);
    
    int len1 = strlen(startHpeTime);
    startHpeTime[len1 - 1] = '\0';
    int32_t startPrimaryKey = 10;
    int32_t properyVal = 8;
    int32_t insertNum = INSERTNUM;
    ret = testGmcConnect_slow(&gConn, &gStmt, 0);
    ASSERT_EQ(GMERR_OK, ret);
    char const *fold_cfg_name = "enableLogFold";
    int modify_fold_mode_value = 0;
    int recover_fold_mode_value = 3;

    do
    {
        ret = GmcCreateVertexLabel(gStmt, gLabelSchemaJson, gLabelConfigJson);
        BREAK_IFERR(ret);

        // 写入数据
        for (int i = startPrimaryKey; i < startPrimaryKey + insertNum; i++)
        {
            ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_INSERT);
            BREAK_IFERR(ret);
            ret = GtSetInsertVertexProperyty(gStmt, i, properyVal, properyVal, properyVal, properyVal);
            BREAK_IFERR(ret);
            ret = GmcExecute(gStmt);
            BREAK_IFERR(ret);
            ret = GtCheckAffectRows(gStmt, 1);
            BREAK_IFERR(ret);
        }
        BREAK_IFERR(ret);
        sleep(5);
        // 通过 hashcluster key 删除数据
        ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_DELETE);
        BREAK_IFERR(ret);
        int32_t keyVal = properyVal;
        ret = GmcSetIndexKeyValue(gStmt, 0, GMC_DATATYPE_UINT32, &keyVal, sizeof(keyVal));
        BREAK_IFERR(ret);

        ret = GmcSetIndexKeyName(gStmt, "HashClusterKey");
        BREAK_IFERR(ret);
        double startTime = GtGetCurrentTime();
        ret = GmcExecute(gStmt);
        double spendTime = GtGetCurrentTime() - startTime;

        printf("\n  [INFO] Excute delete vertex by hash cluster key, spend %.6f ms", spendTime);
        BREAK_IFERR(ret);
        ret = GtCheckAffectRows(gStmt, insertNum);
        BREAK_IFERR(ret);

        uint32_t server_times = 0;
        sleep(2);
        GetHpeTimeInterval(endHpeTime, 30);
        
        int len2 = strlen(endHpeTime);

        endHpeTime[len2 - 1] = '\0';
        char Logsearch[1024];
        if (g_envType == 2) {
            system("hpecli log dump");
        }
        snprintf(Logsearch, 1024, "'/%s/,/%s/p'", startHpeTime, endHpeTime);
        
        // GtCheckServerSlowLog ( 整体耗时 , 操作类型 ， 命名空间 ， 表名 ， 连接编号 ， 连接类型 ， 索引种类 ，
        // 空间开关 ， 名字开关 ， 连接开关 ，
        // 复数日志开关 ， 操作数开关 ， 最长信息开关 ， 默认操作数 ， 默认最长信息 )
        printf("\n  [INFO]CHECH SERVER LOG \n");
        if (g_envType == 0) {
            ret = GtCheckServerSlowLog(&server_times, (char *)" Delete Vertex", NAMESPACE_CHECK, " \"VertexLabel\"", 0,
                                    (char *)" SYNC", (char *)" \"HashClusterKey\"", true, true,
                                    CONN_CHECK, true, true, true, true, true, 1, (char *)" qry_exec_time");
        } else if (envAnos){
            ret = GtCheckServerSlowLog_new(&server_times, (char *)" Delete Vertex", NAMESPACE_CHECK, " \"VertexLabel\"", 0,
                                    (char *)" SYNC", (char *)" \"HashClusterKey\"", NM_CHECK, true,
                                    CONN_CHECK, true, true, true, true, true, 1, (char *)" qry_exec_time",
                                    (char *)"Vertex", 2, 0, true, (char *)Logsearch);
        } else if (!envAnos){
            ret = GtCheckServerSlowLog_new(&server_times, (char *)" Delete Vertex", NAMESPACE_CHECK, " \"VertexLabel\"", 0,
                                    (char *)" SYNC", (char *)" \"HashClusterKey\"", NM_CHECK, true,
                                    CONN_CHECK, true, true, true, true, true, 1, (char *)" qry_exec_time",
                                    (char *)"Vertex", 2, 0, true, (char *)Logsearch);
        }
        printf("\n  [INFO]total_time is :%d\n", server_times);
        if (server_times < 0 || server_times > (spendTime + 8) * 1000)
        {
            EXPECT_EQ(0, 1);
            printf("[INFO]server total time is not normal:%d\n", server_times);
        }

        printf("\n  [INFO]CHECH CLIENT NORMAL LOG \n");
        uint32_t opWholeTime = 0;
        uint32_t ConcatMsgTime = 0;
        uint32_t RspTransmissionTime = 0;
        uint32_t cltPostOpTime = 0;

        // GtCheckClientSlowLog_KV_Graph ( 操作类型 ， 命名空间 ， 表名 ， 连接编号 ， 索引种类 ， 索引开关 ， 空间开关 ，
        // 名字开关 ， 连接开关 ， 复数日志开关 ，整体时间 ， 连接消息时间 ， 接受时间 ， 控制时间 )

        GtCheckClientSlowLog_KV_Graph((char *)" Delete Vertex", NAMESPACE_CHECK,
        (char *)" \"VertexLabel\"", 0, " \"HashClusterKey\"", true,
                                      NM_CHECK, true, CONN_CHECK, true, &opWholeTime, &ConcatMsgTime,
                                      &RspTransmissionTime, &cltPostOpTime);
        printf("opWholeTime is :%d\n", opWholeTime);
        printf("ConcatMsgTime is :%d\n", ConcatMsgTime);
        printf("RspTransmissionTime is :%d\n", RspTransmissionTime);
        printf("cltPostOpTime is :%d\n", cltPostOpTime);
    } while (0);
    EXPECT_EQ(GMERR_OK, ret);

    GmcDropVertexLabel(gStmt, gLabelName);
    ret = testGmcDisconnect(gConn, gStmt);
    ASSERT_EQ(GMERR_OK, ret);

#if defined(ENV_RTOSV2X)
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName enableLogFold -cfgVal 1\n", g_toolPath);
    system(g_command);
#endif
    ClearSlowLog();
}

// 默认配置, 写入大量相同 local 索引的数据, 再通过非唯一 local 扫描数据
TEST_F(SlowOperationEnhanced001, DFX_053_001_007)
{
#if defined(ENV_RTOSV2X)
    GtExecSystemCmd("echo [CLEAR_LOG] > ${TEST_HOME}/testcases/07_DFX/"
                    "053_SlowOperationEnhanced/Slow53_01.log");
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName enableLogFold -cfgVal 0\n", g_toolPath);
    system(g_command);
#else
    ClearSlowLog();
#endif
    int ret;
    char startHpeTime[30];
    char endHpeTime[30];
    GetHpeTime(startHpeTime, 30);
    
    int len1 = strlen(startHpeTime);
    startHpeTime[len1 - 1] = '\0';
    int32_t startPrimaryKey = 10;
    int32_t properyVal = 8;
    int32_t insertNum = INSERTNUM;
    ret = testGmcConnect_slow(&gConn, &gStmt, 10);
    ASSERT_EQ(GMERR_OK, ret);
    char const *fold_cfg_name = "enableLogFold";
    int modify_fold_mode_value = 0;
    int recover_fold_mode_value = 3;

    do
    {
        ret = GmcCreateVertexLabel(gStmt, gLabelSchemaJson, gLabelConfigJson);
        BREAK_IFERR(ret);

        // 写入数据
        for (int i = startPrimaryKey; i < startPrimaryKey + insertNum; i++)
        {
            ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_INSERT);
            BREAK_IFERR(ret);
            ret = GtSetInsertVertexProperyty(gStmt, i, properyVal, properyVal, properyVal, properyVal);
            BREAK_IFERR(ret);
            ret = GmcExecute(gStmt);
            BREAK_IFERR(ret);
            ret = GtCheckAffectRows(gStmt, 1);
            BREAK_IFERR(ret);
        }
        BREAK_IFERR(ret);

        // 通过 local key 扫描
        ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_SCAN);
        BREAK_IFERR(ret);
        uint32_t leftVal = properyVal;
        GmcPropValueT left;
        left.type = GMC_DATATYPE_UINT32;
        left.size = sizeof(uint32_t);
        left.value = &leftVal;

        uint32_t rightVal = properyVal + 1;
        GmcPropValueT right;
        right.type = GMC_DATATYPE_UINT32;
        right.size = sizeof(rightVal);
        right.value = &rightVal;

        unsigned int arrLen = 1;
        GmcRangeItemT items[arrLen];
        items[0].lValue = &left;
        items[0].rValue = &right;
        items[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
        items[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
        items[0].order = GMC_ORDER_ASC;

        ret = GmcSetKeyRange(gStmt, items, arrLen);
        BREAK_IFERR(ret);

        ret = GmcSetIndexKeyName(gStmt, "LocalKey");
        BREAK_IFERR(ret);
        double startTime = GtGetCurrentTime();
        ret = GmcExecute(gStmt);
        double spendTime = GtGetCurrentTime() - startTime;
        printf("\n  [INFO] Excute scan vertex by local key, spend %.6f ms", spendTime);
        BREAK_IFERR(ret);

        bool isFinished = false;
        int32_t scanCount = 0;
        while (!isFinished)
        {
            ret = GmcFetch(gStmt, &isFinished);
            BREAK_IFERR(ret);
            if (isFinished)
            {
                break;
            }

            ret = GtGetAndCheckProperty(gStmt, properyVal, properyVal, properyVal, properyVal);
            BREAK_IFERR(ret);
            scanCount++;
        }
        BREAK_IFERR(ret);

        if (scanCount != insertNum)
        {
            TEST_ERROR("scan failed, scanCount = %d, insertNum = %d", scanCount, insertNum);
            ret = FAILED;
            break;
        }

        uint32_t server_times = 0;
        sleep(2);
        GetHpeTimeInterval(endHpeTime, 30);
        
        int len2 = strlen(endHpeTime);

        endHpeTime[len2 - 1] = '\0';
        char Logsearch[1024];
        if (g_envType == 2) {
            system("hpecli log dump");
        }
        snprintf(Logsearch, 1024, "'/%s/,/%s/p'", startHpeTime, endHpeTime);
        
        // 此处检查Insert的服务日志，因为没有其他耗时操作，进行了直连读
        // GtCheckServerSlowLog ( 整体耗时 , 操作类型 ， 命名空间 ， 表名 ， 连接编号 ， 连接类型 ， 索引种类 ，
        // 空间开关 ， 名字开关 ， 连接开关 ，
        // 复数日志开关 ， 操作数开关 ， 最长信息开关 ， 默认操作数 ， 默认最长信息 )
        printf("\n  [INFO]CHECH SERVER LOG \n");
        if (g_envType == 0) {
            ret = GtCheckServerSlowLog(&server_times, (char *)" Insert Vertex", NAMESPACE_CHECK, " \"VertexLabel\"", 0,
                                    (char *)" SYNC", (char *)" \"\"",
                                    true, true,
                                    CONN_CHECK, true, true, true, true, true, 1, (char *)" qry_exec_time");
        } else if (envAnos){
            ret = GtCheckServerSlowLog_new(&server_times, (char *)" Insert Vertex", NAMESPACE_CHECK, " \"VertexLabel\"", 0,
                                    (char *)" SYNC", (char *)" \"\"",
                                    NM_CHECK, true,
                                    CONN_CHECK, true, true, true, true, true, 1, (char *)" qry_exec_time",
                                    (char *)"Vertex", 2, 0, true, (char *)Logsearch);
        } else if (!envAnos){
            ret = GtCheckServerSlowLog_new(&server_times, (char *)" Insert Vertex", NAMESPACE_CHECK, " \"VertexLabel\"", 0,
                                    (char *)" SYNC", (char *)" \"\"",
                                    NM_CHECK, true,
                                    CONN_CHECK, true, true, true, true, true, 1, (char *)" qry_exec_time",
                                    (char *)"Vertex", 2, 0, true, (char *)Logsearch);
        }
        printf("\n  [INFO]total_time is :%d\n", server_times);
        if (server_times < 0 || server_times > (spendTime + 8) * 1000)
        {
            EXPECT_EQ(0, 1);
            printf("[INFO]server total time is not normal:%d\n", server_times);
        }
        printf("\n  [INFO]CHECH CLIENT DIRECT LOG \n");
        // 此处检查客户端的直连读日志
        uint32_t check_directtimes = 0;
        // GtCheckClientSlowLog_Direct ( 命名空间 ， 表名 ， 空间开关 ， 名字开关 ， 整体时间 , 索引名 ， 索引名开关)
        ret = GtCheckClientSlowLog_Direct(NAMESPACE_CHECK, " \"VertexLabel\"",
            NM_CHECK, true, &check_directtimes, (char *)" \"LocalKey\"", true);
        printf("Direct opWholeTime is :%d\n", check_directtimes);
    } while (0);
    EXPECT_EQ(GMERR_OK, ret);

    GmcDropVertexLabel(gStmt, gLabelName);
    ret = testGmcDisconnect(gConn, gStmt);
    ASSERT_EQ(GMERR_OK, ret);

#if defined(ENV_RTOSV2X)
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName enableLogFold -cfgVal 1\n", g_toolPath);
    system(g_command);
#endif
    ClearSlowLog();
}

// 默认配置, 写入大量相同 local 索引的数据, 再通过非唯一 local 删除数据
TEST_F(SlowOperationEnhanced001, DFX_053_001_008)
{
#if defined(ENV_RTOSV2X)
    GtExecSystemCmd("echo [CLEAR_LOG] > ${TEST_HOME}/testcases/07_DFX/"
                    "053_SlowOperationEnhanced/Slow53_01.log");
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName enableLogFold -cfgVal 0\n", g_toolPath);
    system(g_command);
#else
    ClearSlowLog();
#endif
    int ret;
    char startHpeTime[30];
    char endHpeTime[30];
    GetHpeTime(startHpeTime, 30);
    
    int len1 = strlen(startHpeTime);
    startHpeTime[len1 - 1] = '\0';
    int32_t startPrimaryKey = 10;
    int32_t properyVal = 8;
    int32_t insertNum = INSERTNUM;
    ret = testGmcConnect_slow(&gConn, &gStmt, 0);
    ASSERT_EQ(GMERR_OK, ret);
    char const *fold_cfg_name = "enableLogFold";
    int modify_fold_mode_value = 0;
    int recover_fold_mode_value = 3;

    do
    {
        ret = GmcCreateVertexLabel(gStmt, gLabelSchemaJson, gLabelConfigJson);
        BREAK_IFERR(ret);

        // 写入数据
        for (int i = startPrimaryKey; i < startPrimaryKey + insertNum; i++)
        {
            ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_INSERT);
            BREAK_IFERR(ret);
            ret = GtSetInsertVertexProperyty(gStmt, i, properyVal, properyVal, properyVal, properyVal);
            BREAK_IFERR(ret);
            ret = GmcExecute(gStmt);
            BREAK_IFERR(ret);
            ret = GtCheckAffectRows(gStmt, 1);
            BREAK_IFERR(ret);
        }
        BREAK_IFERR(ret);
        sleep(5);

        // 通过 local key 删除数据
        ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_DELETE);
        BREAK_IFERR(ret);
        uint32_t leftVal = properyVal;
        GmcPropValueT left;
        left.type = GMC_DATATYPE_UINT32;
        left.size = sizeof(uint32_t);
        left.value = &leftVal;

        uint32_t rightVal = properyVal + 1;
        GmcPropValueT right;
        right.type = GMC_DATATYPE_UINT32;
        right.size = sizeof(rightVal);
        right.value = &rightVal;

        unsigned int arrLen = 1;
        GmcRangeItemT items[arrLen];
        items[0].lValue = &left;
        items[0].rValue = &right;
        items[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
        items[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
        items[0].order = GMC_ORDER_ASC;

        ret = GmcSetKeyRange(gStmt, items, arrLen);
        BREAK_IFERR(ret);

        ret = GmcSetIndexKeyName(gStmt, "LocalKey");
        BREAK_IFERR(ret);
        double startTime = GtGetCurrentTime();
        ret = GmcExecute(gStmt);
        double spendTime = GtGetCurrentTime() - startTime;
        printf("\n  [INFO] delete vertex by local key, spend %.6f ms", spendTime);
        BREAK_IFERR(ret);

        ret = GtCheckAffectRows(gStmt, insertNum);
        BREAK_IFERR(ret);

        // 检查日志
        uint32_t server_times = 0;
        sleep(2);
        GetHpeTimeInterval(endHpeTime, 30);
        
        int len2 = strlen(endHpeTime);

        endHpeTime[len2 - 1] = '\0';
        char Logsearch[1024];
        if (g_envType == 2) {
            system("hpecli log dump");
        }
        snprintf(Logsearch, 1024, "'/%s/,/%s/p'", startHpeTime, endHpeTime);
        
        // GtCheckServerSlowLog ( 整体耗时 , 操作类型 ， 命名空间 ， 表名 ， 连接编号 ， 连接类型 ， 索引种类 ，
        // 空间开关 ， 名字开关 ， 连接开关 ，
        // 复数日志开关 ， 操作数开关 ， 最长信息开关 ， 默认操作数 ， 默认最长信息 )
        printf("\n  [INFO]CHECH SERVER LOG \n");
        if (g_envType == 0) {
            ret = GtCheckServerSlowLog(&server_times, (char *)" Delete Vertex", NAMESPACE_CHECK, " \"VertexLabel\"", 0,
                                    (char *)" SYNC", (char *)" \"LocalKey\"", true, true,
                                    CONN_CHECK, true, true, true, true, true, 1, (char *)" qry_exec_time");
        } else if (envAnos){
            ret = GtCheckServerSlowLog_new(&server_times, (char *)" Delete Vertex", NAMESPACE_CHECK, " \"VertexLabel\"", 0,
                                    (char *)" SYNC", (char *)" \"LocalKey\"", NM_CHECK, true,
                                    CONN_CHECK, true, true, true, true, true, 1, (char *)" qry_exec_time",
                                    (char *)"Vertex", 2, 0, true, (char *)Logsearch);
        } else if (!envAnos){
            ret = GtCheckServerSlowLog_new(&server_times, (char *)" Delete Vertex", NAMESPACE_CHECK, " \"VertexLabel\"", 0,
                                    (char *)" SYNC", (char *)" \"LocalKey\"", NM_CHECK, true,
                                    CONN_CHECK, true, true, true, true, true, 1, (char *)" qry_exec_time",
                                    (char *)"Vertex", 2, 0, true, (char *)Logsearch);
        }
        printf("\n  [INFO]total_time is :%d\n", server_times);
        if (server_times < 0 || server_times > (spendTime + 8) * 1000)
        {
            EXPECT_EQ(0, 1);
            printf("[INFO]server total time is not normal:%d\n", server_times);
        }
        printf("\n  [INFO]CHECH CLIENT NORMAL LOG \n");
        uint32_t opWholeTime = 0;
        uint32_t ConcatMsgTime = 0;
        uint32_t RspTransmissionTime = 0;
        uint32_t cltPostOpTime = 0;

        // GtCheckClientSlowLog_KV_Graph ( 操作类型 ， 命名空间 ， 表名 ， 连接编号 ， 索引种类 ， 索引开关 ， 空间开关 ，
        // 名字开关 ， 连接开关 ， 复数日志开关 ，整体时间 ， 连接消息时间 ， 接受时间 ， 控制时间 )

        GtCheckClientSlowLog_KV_Graph((char *)" Delete Vertex", NAMESPACE_CHECK,
                                    (char *)" \"VertexLabel\"", 0, " \"LocalKey\"", true,
                                      NM_CHECK, true, CONN_CHECK, true, &opWholeTime, &ConcatMsgTime,
                                      &RspTransmissionTime, &cltPostOpTime);
        printf("opWholeTime is :%d\n", opWholeTime);
        printf("ConcatMsgTime is :%d\n", ConcatMsgTime);
        printf("RspTransmissionTime is :%d\n", RspTransmissionTime);
        printf("cltPostOpTime is :%d\n", cltPostOpTime);
    } while (0);
    EXPECT_EQ(GMERR_OK, ret);

    GmcDropVertexLabel(gStmt, gLabelName);
    ret = testGmcDisconnect(gConn, gStmt);
    ASSERT_EQ(GMERR_OK, ret);

#if defined(ENV_RTOSV2X)
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName enableLogFold -cfgVal 1\n", g_toolPath);
    system(g_command);
#endif
    ClearSlowLog();
}

// 默认配置，写入大量数据后进行全表扫描
TEST_F(SlowOperationEnhanced001, DFX_053_001_009)
{
#if defined(ENV_RTOSV2X)
    GtExecSystemCmd("echo [CLEAR_LOG] > ${TEST_HOME}/testcases/07_DFX/"
                    "053_SlowOperationEnhanced/Slow53_01.log");
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName enableLogFold -cfgVal 0\n", g_toolPath);
    system(g_command);
#else
    ClearSlowLog();
#endif
    int ret;
    char startHpeTime[30];
    char endHpeTime[30];
    GetHpeTime(startHpeTime, 30);
    
    int len1 = strlen(startHpeTime);
    startHpeTime[len1 - 1] = '\0';
    char const *fold_cfg_name = "enableLogFold";
    int modify_fold_mode_value = 0;
    int recover_fold_mode_value = 3;

    int32_t startPrimaryKey = 10;
    int32_t properyVal = 8;
    int32_t insertNum = INSERTNUM;
    ret = testGmcConnect_slow(&gConn, &gStmt, 10);
    ASSERT_EQ(GMERR_OK, ret);
    do
    {
        ret = GmcCreateVertexLabel(gStmt, gLabelSchemaJson, gLabelConfigJson);
        BREAK_IFERR(ret);

        // 写入数据
        for (int i = startPrimaryKey; i < startPrimaryKey + insertNum; i++)
        {
            ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_INSERT);
            BREAK_IFERR(ret);
            ret = GtSetInsertVertexProperyty(gStmt, i, properyVal, properyVal, properyVal, properyVal);
            BREAK_IFERR(ret);
            ret = GmcExecute(gStmt);
            BREAK_IFERR(ret);
            ret = GtCheckAffectRows(gStmt, 1);
            BREAK_IFERR(ret);
        }
        BREAK_IFERR(ret);

        // 扫描很快, 不会打印日志
        ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_SCAN);
        BREAK_IFERR(ret);
        ret = GmcSetIndexKeyName(gStmt, NULL);
        BREAK_IFERR(ret);
        double startTime = GtGetCurrentTime();
        ret = GmcExecute(gStmt);
        double spendTime = GtGetCurrentTime() - startTime;
        TEST_INFO("scan vertex label, spend %.6f ms", spendTime);
        BREAK_IFERR(ret);

        bool isFinished = false;
        int32_t scanCount = 0;
        while (!isFinished)
        {
            ret = GmcFetch(gStmt, &isFinished);
            BREAK_IFERR(ret);
            if (isFinished)
            {
                break;
            }

            ret = GtGetAndCheckProperty(gStmt, properyVal, properyVal, properyVal, properyVal);
            BREAK_IFERR(ret);
            scanCount++;
        }
        BREAK_IFERR(ret);

        if (scanCount != insertNum)
        {
            TEST_ERROR("scan failed, scanCount = %d, insertNum = %d", scanCount, insertNum);
            ret = FAILED;
            break;
        }

        uint32_t server_times = 0;
        sleep(2);
        GetHpeTimeInterval(endHpeTime, 30);
        
        int len2 = strlen(endHpeTime);

        endHpeTime[len2 - 1] = '\0';
        char Logsearch[1024];
        if (g_envType == 2) {
            system("hpecli log dump");
        }
        snprintf(Logsearch, 1024, "'/%s/,/%s/p'", startHpeTime, endHpeTime);
        
        // 此处检查Insert的服务日志，因为没有其他耗时操作，进行了直连读
        // GtCheckServerSlowLog ( 整体耗时 , 操作类型 ， 命名空间 ， 表名 ， 连接编号 ， 连接类型 ， 索引种类 ，
        // 空间开关 ， 名字开关 ， 连接开关 ，
        // 复数日志开关 ， 操作数开关 ， 最长信息开关 ， 默认操作数 ， 默认最长信息 )
        printf("\n  [INFO]CHECH SERVER LOG \n");
        if (g_envType == 0) {
            ret = GtCheckServerSlowLog(&server_times, (char *)" Insert Vertex", NAMESPACE_CHECK, " \"VertexLabel\"", 0,
                                    (char *)" SYNC", (char *)" \"\"",
                                    true, true,
                                    CONN_CHECK, true, true, true, true, true, 1, (char *)" qry_exec_time");
        } else if (envAnos){
            ret = GtCheckServerSlowLog_new(&server_times, (char *)" Insert Vertex", NAMESPACE_CHECK, " \"VertexLabel\"", 0,
                                    (char *)" SYNC", (char *)" \"\"",
                                    NM_CHECK, true,
                                    CONN_CHECK, true, true, true, true, true, 1, (char *)" qry_exec_time",
                                    (char *)"Vertex", 2, 0, true, (char *)Logsearch);
        } else if (!envAnos){
            ret = GtCheckServerSlowLog_new(&server_times, (char *)" Insert Vertex", NAMESPACE_CHECK, " \"VertexLabel\"", 0,
                                    (char *)" SYNC", (char *)" \"\"",
                                    NM_CHECK, true,
                                    CONN_CHECK, true, true, true, true, true, 1, (char *)" qry_exec_time",
                                    (char *)"Vertex", 2, 0, true, (char *)Logsearch);
        }
        printf("\n  [INFO]total_time is :%d\n", server_times);
        if (server_times < 0 || server_times > (spendTime + 8) * 1000)
        {
            EXPECT_EQ(0, 1);
            printf("[INFO]server total time is not normal:%d\n", server_times);
        }
        printf("\n  [INFO]CHECH CLIENT DIRECT LOG \n");
        // 此处检查客户端的直连读日志
        uint32_t check_directtimes = 0;
        // GtCheckClientSlowLog_Direct ( 命名空间 ， 表名 ， 空间开关 ， 名字开关 ， 整体时间 , 索引名 ， 索引名开关)
        ret = GtCheckClientSlowLog_Direct(NAMESPACE_CHECK, " \"VertexLabel\"",
            NM_CHECK, true, &check_directtimes, (char *)" \"\"", true);
        printf("Direct opWholeTime is :%d\n", check_directtimes);
    } while (0);
    EXPECT_EQ(GMERR_OK, ret);

    GmcDropVertexLabel(gStmt, gLabelName);
    ret = testGmcDisconnect(gConn, gStmt);
    ASSERT_EQ(GMERR_OK, ret);

#if defined(ENV_RTOSV2X)
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName enableLogFold -cfgVal 1\n", g_toolPath);
    system(g_command);
#endif
    ClearSlowLog();
}

// 默认配置，写入大量数据后进行过滤扫描
TEST_F(SlowOperationEnhanced001, DFX_053_001_010)
{
#if defined(ENV_RTOSV2X)
    GtExecSystemCmd("echo [CLEAR_LOG] > ${TEST_HOME}/testcases/07_DFX/"
                    "053_SlowOperationEnhanced/Slow53_01.log");
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName enableLogFold -cfgVal 0\n", g_toolPath);
    system(g_command);
#else
    ClearSlowLog();
#endif
    int ret;
    char startHpeTime[30];
    char endHpeTime[30];
    GetHpeTime(startHpeTime, 30);
    
    int len1 = strlen(startHpeTime);
    startHpeTime[len1 - 1] = '\0';
    char const *fold_cfg_name = "enableLogFold";
    int modify_fold_mode_value = 0;
    int recover_fold_mode_value = 3;

    int32_t startPrimaryKey = 10;
    int32_t properyVal = 8;
    int32_t insertNum = INSERTNUM;
    ret = testGmcConnect_slow(&gConn, &gStmt, 10);
    ASSERT_EQ(GMERR_OK, ret);
    do
    {
        ret = GmcCreateVertexLabel(gStmt, gLabelSchemaJson, gLabelConfigJson);
        BREAK_IFERR(ret);

        // 写入数据
        for (int i = startPrimaryKey; i < startPrimaryKey + insertNum; i++)
        {
            ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_INSERT);
            BREAK_IFERR(ret);
            ret = GtSetInsertVertexProperyty(gStmt, i, properyVal, properyVal, properyVal, properyVal);
            BREAK_IFERR(ret);
            ret = GmcExecute(gStmt);
            BREAK_IFERR(ret);
            ret = GtCheckAffectRows(gStmt, 1);
            BREAK_IFERR(ret);
        }
        BREAK_IFERR(ret);

        // 范围过滤扫描
        ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_SCAN);
        BREAK_IFERR(ret);
        uint32_t leftVal = properyVal;
        GmcPropValueT left;
        left.type = GMC_DATATYPE_UINT32;
        left.size = sizeof(uint32_t);
        left.value = &leftVal;

        uint32_t rightVal = properyVal + 1;
        GmcPropValueT right;
        right.type = GMC_DATATYPE_UINT32;
        right.size = sizeof(rightVal);
        right.value = &rightVal;

        unsigned int arrLen = 1;
        GmcRangeItemT items[arrLen];
        items[0].lValue = &left;
        items[0].rValue = &right;
        items[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
        items[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
        items[0].order = GMC_ORDER_ASC;

        ret = GmcSetKeyRange(gStmt, items, arrLen);
        BREAK_IFERR(ret);
        ret = GmcSetIndexKeyName(gStmt, "LocalKey");
        BREAK_IFERR(ret);
        double startTime = GtGetCurrentTime();
        ret = GmcExecute(gStmt);
        double spendTime = GtGetCurrentTime() - startTime;
        TEST_INFO("scan vertex by local key, spend %.6f ms", spendTime);
        BREAK_IFERR(ret);

        bool isFinished = false;
        int32_t scanCount = 0;
        while (!isFinished)
        {
            ret = GmcFetch(gStmt, &isFinished);
            BREAK_IFERR(ret);
            if (isFinished)
            {
                break;
            }

            ret = GtGetAndCheckProperty(gStmt, properyVal, properyVal, properyVal, properyVal);
            BREAK_IFERR(ret);
            scanCount++;
        }
        BREAK_IFERR(ret);

        if (scanCount != insertNum)
        {
            TEST_ERROR("scan failed, scanCount = %d, insertNum = %d", scanCount, insertNum);
            ret = FAILED;
            break;
        }

        uint32_t server_times = 0;
        sleep(2);
        GetHpeTimeInterval(endHpeTime, 30);
        
        int len2 = strlen(endHpeTime);

        endHpeTime[len2 - 1] = '\0';
        char Logsearch[1024];
        if (g_envType == 2) {
            system("hpecli log dump");
        }
        snprintf(Logsearch, 1024, "'/%s/,/%s/p'", startHpeTime, endHpeTime);
        
        // 此处检查Insert的服务日志，因为没有其他耗时操作，进行了直连读
        // GtCheckServerSlowLog ( 整体耗时 , 操作类型 ， 命名空间 ， 表名 ， 连接编号 ， 连接类型 ， 索引种类 ，
        // 空间开关 ， 名字开关 ， 连接开关 ，
        // 复数日志开关 ， 操作数开关 ， 最长信息开关 ， 默认操作数 ， 默认最长信息 )
        printf("\n  [INFO]CHECH SERVER LOG \n");
        if (g_envType == 0) {
            ret = GtCheckServerSlowLog(&server_times, (char *)" Insert Vertex", NAMESPACE_CHECK, " \"VertexLabel\"", 0,
                                    (char *)" SYNC", (char *)" \"\"",
                                    true, true,
                                    CONN_CHECK, true, true, true, true, true, 1, (char *)" qry_exec_time");
        } else if (envAnos){
            ret = GtCheckServerSlowLog_new(&server_times, (char *)" Insert Vertex", NAMESPACE_CHECK, " \"VertexLabel\"", 0,
                                    (char *)" SYNC", (char *)" \"\"",
                                    NM_CHECK, true,
                                    CONN_CHECK, true, true, true, true, true, 1, (char *)" qry_exec_time",
                                    (char *)"Vertex", 2, 0, true, (char *)Logsearch);
        } else if (!envAnos){
            ret = GtCheckServerSlowLog_new(&server_times, (char *)" Insert Vertex", NAMESPACE_CHECK, " \"VertexLabel\"", 0,
                                    (char *)" SYNC", (char *)" \"\"",
                                    NM_CHECK, true,
                                    CONN_CHECK, true, true, true, true, true, 1, (char *)" qry_exec_time",
                                    (char *)"Vertex", 2, 0, true, (char *)Logsearch);
        }
        printf("\n  [INFO]total_time is :%d\n", server_times);
        if (server_times < 0 || server_times > (spendTime + 8) * 1000)
        {
            EXPECT_EQ(0, 1);
            printf("[INFO]server total time is not normal:%d\n", server_times);
        }
        printf("\n  [INFO]CHECH CLIENT DIRECT LOG \n");
        // 此处检查客户端的直连读日志
        uint32_t check_directtimes = 0;
        // GtCheckClientSlowLog_Direct ( 命名空间 ， 表名 ， 空间开关 ， 名字开关 ， 整体时间 , 索引名 ， 索引名开关)
        ret = GtCheckClientSlowLog_Direct(NAMESPACE_CHECK, " \"VertexLabel\"",
        NM_CHECK, true, &check_directtimes, (char *)" \"LocalKey\"", true);
        printf("Direct opWholeTime is :%d\n", check_directtimes);
    } while (0);
    EXPECT_EQ(GMERR_OK, ret);

    GmcDropVertexLabel(gStmt, gLabelName);
    ret = testGmcDisconnect(gConn, gStmt);
    ASSERT_EQ(GMERR_OK, ret);

#if defined(ENV_RTOSV2X)
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName enableLogFold -cfgVal 1\n", g_toolPath);
    system(g_command);
#endif
    ClearSlowLog();
}

// 默认配置，写入大量数据后 truncate 数据
TEST_F(SlowOperationEnhanced001, DFX_053_001_011)
{
#if defined(ENV_RTOSV2X)
    GtExecSystemCmd("echo [CLEAR_LOG] > ${TEST_HOME}/testcases/07_DFX/"
                    "053_SlowOperationEnhanced/Slow53_01.log");
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName enableLogFold -cfgVal 0\n", g_toolPath);
    system(g_command);
#else
    ClearSlowLog();
#endif
    int ret;
    char startHpeTime[30];
    char endHpeTime[30];
    GetHpeTime(startHpeTime, 30);
    
    int len1 = strlen(startHpeTime);
    startHpeTime[len1 - 1] = '\0';
    char const *fold_cfg_name = "enableLogFold";
    int modify_fold_mode_value = 0;
    int recover_fold_mode_value = 3;

    int32_t startPrimaryKey = 10;
    int32_t properyVal = 8;
    int32_t insertNum = INSERTNUM;
    ret = testGmcConnect_slow(&gConn, &gStmt, 0);
    ASSERT_EQ(GMERR_OK, ret);
    do
    {
        ret = GmcCreateVertexLabel(gStmt, gLabelSchemaJson, gLabelConfigJson);
        BREAK_IFERR(ret);

        // 写入数据
        for (int i = startPrimaryKey; i < startPrimaryKey + insertNum; i++)
        {
            ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_INSERT);
            BREAK_IFERR(ret);
            ret = GtSetInsertVertexProperyty(gStmt, i, properyVal, properyVal, properyVal, properyVal);
            BREAK_IFERR(ret);
            ret = GmcExecute(gStmt);
            BREAK_IFERR(ret);
            ret = GtCheckAffectRows(gStmt, 1);
            BREAK_IFERR(ret);
        }
        BREAK_IFERR(ret);

        // truncate 表数据
        double startTime = GtGetCurrentTime();
        ret = GmcTruncateVertexLabel(gStmt, gLabelName);
        double spendTime = GtGetCurrentTime() - startTime;
        TEST_INFO("truncate table, spend %.6f ms", spendTime);
        BREAK_IFERR(ret);

        uint32_t server_times = 0;
        sleep(2);
        GetHpeTimeInterval(endHpeTime, 30);
        
        int len2 = strlen(endHpeTime);

        endHpeTime[len2 - 1] = '\0';
        char Logsearch[1024];
        if (g_envType == 2) {
            system("hpecli log dump");
        }
        snprintf(Logsearch, 1024, "'/%s/,/%s/p'", startHpeTime, endHpeTime);
        
        // 此处检查Insert的服务日志，因为没有其他耗时操作，进行了直连读
        // GtCheckServerSlowLog ( 整体耗时 , 操作类型 ， 命名空间 ， 表名 ， 连接编号 ， 连接类型 ， 索引种类 ，
        // 空间开关 ， 名字开关 ， 连接开关 ，
        // 复数日志开关 ， 操作数开关 ， 最长信息开关 ， 默认操作数 ， 默认最长信息 )
        printf("\n  [INFO]CHECH SERVER LOG \n");
        if (g_envType == 0) {
            ret = GtCheckServerSlowLog(&server_times, (char *)" Insert Vertex", NAMESPACE_CHECK, " \"VertexLabel\"", 0,
                                    (char *)" SYNC", (char *)" \"\"",
                                    true, true,
                                    CONN_CHECK, true, true, true, true, true, 1, (char *)" qry_exec_time");
        } else if (envAnos){
            ret = GtCheckServerSlowLog_new(&server_times, (char *)" Insert Vertex", NAMESPACE_CHECK, " \"VertexLabel\"", 0,
                                    (char *)" SYNC", (char *)" \"\"",
                                    NM_CHECK, true,
                                    CONN_CHECK, true, true, true, true, true, 1, (char *)" qry_exec_time",
                                    (char *)"Vertex", 2, 0, true, (char *)Logsearch);
        } else if (!envAnos){
            ret = GtCheckServerSlowLog_new(&server_times, (char *)" Insert Vertex", NAMESPACE_CHECK, " \"VertexLabel\"", 0,
                                    (char *)" SYNC", (char *)" \"\"",
                                    NM_CHECK, true,
                                    CONN_CHECK, true, true, true, true, true, 1, (char *)" qry_exec_time",
                                    (char *)"Vertex", 2, 0, true, (char *)Logsearch);
        }
        printf("\n  [INFO]total_time is :%d\n", server_times);
        if (server_times < 0 || server_times > (spendTime + 8) * 1000)
        {
            EXPECT_EQ(0, 1);
            printf("[INFO]server total time is not normal:%d\n", server_times);
        }
        printf("\n  [INFO]CHECH CLIENT NORMAL LOG \n");
        uint32_t opWholeTime = 0;
        uint32_t ConcatMsgTime = 0;
        uint32_t RspTransmissionTime = 0;
        uint32_t cltPostOpTime = 0;

        // GtCheckClientSlowLog_KV_Graph ( 操作类型 ， 命名空间 ， 表名 ， 连接编号 ， 索引种类 ， 索引开关 ， 空间开关 ，
        // 名字开关 ， 连接开关 ， 复数日志开关 ，整体时间 ， 连接消息时间 ， 接受时间 ， 控制时间 )

        GtCheckClientSlowLog_KV_Graph((char *)" Insert Vertex", NAMESPACE_CHECK, (char *)" \"VertexLabel\"", 0,
                                      (char *)" \"\"",
                                      true,
                                      NM_CHECK, true, CONN_CHECK, true, &opWholeTime, &ConcatMsgTime,
                                      &RspTransmissionTime, &cltPostOpTime);
        printf("opWholeTime is :%d\n", opWholeTime);
        printf("ConcatMsgTime is :%d\n", ConcatMsgTime);
        printf("RspTransmissionTime is :%d\n", RspTransmissionTime);
        printf("cltPostOpTime is :%d\n", cltPostOpTime);
    } while (0);
    EXPECT_EQ(GMERR_OK, ret);

    GmcDropVertexLabel(gStmt, gLabelName);
    ret = testGmcDisconnect(gConn, gStmt);
    ASSERT_EQ(GMERR_OK, ret);

#if defined(ENV_RTOSV2X)
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName enableLogFold -cfgVal 1\n", g_toolPath);
    system(g_command);
#endif
    ClearSlowLog();
}

// 默认配置，写入大量数据后 drop 数据
TEST_F(SlowOperationEnhanced001, DFX_053_001_012)
{
#if defined(ENV_RTOSV2X)
    GtExecSystemCmd("echo [CLEAR_LOG] > ${TEST_HOME}/testcases/07_DFX/"
                    "053_SlowOperationEnhanced/Slow53_01.log");
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName enableLogFold -cfgVal 0\n", g_toolPath);
    system(g_command);
#else
    ClearSlowLog();
#endif
    int ret;
    char startHpeTime[30];
    char endHpeTime[30];
    GetHpeTime(startHpeTime, 30);
    
    int len1 = strlen(startHpeTime);
    startHpeTime[len1 - 1] = '\0';
    char const *fold_cfg_name = "enableLogFold";
    int modify_fold_mode_value = 0;
    int recover_fold_mode_value = 3;

    int32_t startPrimaryKey = 10;
    int32_t properyVal = 8;
    int32_t insertNum = INSERTNUM;
    ret = testGmcConnect_slow(&gConn, &gStmt, 10);
    ASSERT_EQ(GMERR_OK, ret);
    do
    {
        ret = GmcCreateVertexLabel(gStmt, gLabelSchemaJson, gLabelConfigJson);
        BREAK_IFERR(ret);

        // 写入数据
        for (int i = startPrimaryKey; i < startPrimaryKey + insertNum; i++)
        {
            ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_INSERT);
            BREAK_IFERR(ret);
            ret = GtSetInsertVertexProperyty(gStmt, i, properyVal, properyVal, properyVal, properyVal);
            BREAK_IFERR(ret);
            ret = GmcExecute(gStmt);
            BREAK_IFERR(ret);
            ret = GtCheckAffectRows(gStmt, 1);
            BREAK_IFERR(ret);
        }
        BREAK_IFERR(ret);

        // drop label
        double startTime = GtGetCurrentTime();
        ret = GmcDropVertexLabel(gStmt, gLabelName);
        double spendTime = GtGetCurrentTime() - startTime;
        TEST_INFO("drop label, spend %.6f ms", spendTime);
        BREAK_IFERR(ret);

        uint32_t server_times = 0;
        sleep(2);
        GetHpeTimeInterval(endHpeTime, 30);
        
        int len2 = strlen(endHpeTime);

        endHpeTime[len2 - 1] = '\0';
        char Logsearch[1024];
        if (g_envType == 2) {
            system("hpecli log dump");
        }
        snprintf(Logsearch, 1024, "'/%s/,/%s/p'", startHpeTime, endHpeTime);
        
        printf("\n  [INFO]CHECH SERVER LOG \n");
        if (g_envType == 0) {
            ret = GtCheckServerSlowLog(&server_times, (char *)" Insert Vertex", NAMESPACE_CHECK, " \"VertexLabel\"", 0,
                                    (char *)" SYNC", (char *)" \"\"",
                                    true, true,
                                    CONN_CHECK, true, true, true, true, true, 1, (char *)" qry_exec_time");
        } else if (envAnos){
            ret = GtCheckServerSlowLog_new(&server_times, (char *)" Insert Vertex", NAMESPACE_CHECK, " \"VertexLabel\"", 0,
                                    (char *)" SYNC", (char *)" \"\"",
                                    NM_CHECK, true,
                                    CONN_CHECK, true, true, true, true, true, 1, (char *)" qry_exec_time",
                                    (char *)"Vertex", 2, 0, true, (char *)Logsearch);
        } else if (!envAnos){
            ret = GtCheckServerSlowLog_new(&server_times, (char *)" Insert Vertex", NAMESPACE_CHECK, " \"VertexLabel\"", 0,
                                    (char *)" SYNC", (char *)" \"\"",
                                    NM_CHECK, true,
                                    CONN_CHECK, true, true, true, true, true, 1, (char *)" qry_exec_time",
                                    (char *)"Vertex", 2, 0, true, (char *)Logsearch);
        }
        printf("\n  [INFO]total_time is :%d\n", server_times);
        if (server_times < 0 || server_times > (spendTime + 8) * 1000)
        {
            EXPECT_EQ(0, 1);
            printf("[INFO]server total time is not normal:%d\n", server_times);
        }
        printf("\n  [INFO]CHECH CLIENT NORMAL LOG \n");
        uint32_t opWholeTime = 0;
        uint32_t ConcatMsgTime = 0;
        uint32_t RspTransmissionTime = 0;
        uint32_t cltPostOpTime = 0;

        GtCheckClientSlowLog_KV_Graph((char *)" Insert Vertex", NAMESPACE_CHECK, (char *)" \"VertexLabel\"", 0,
                                      " \"\"",
                                      true,
                                      NM_CHECK, true, CONN_CHECK, true, &opWholeTime, &ConcatMsgTime,
                                      &RspTransmissionTime, &cltPostOpTime);
        printf("opWholeTime is :%d\n", opWholeTime);
        printf("ConcatMsgTime is :%d\n", ConcatMsgTime);
        printf("RspTransmissionTime is :%d\n", RspTransmissionTime);
        printf("cltPostOpTime is :%d\n", cltPostOpTime);
    } while (0);
    EXPECT_EQ(GMERR_OK, ret);

    GmcDropVertexLabel(gStmt, gLabelName);
    ret = testGmcDisconnect(gConn, gStmt);
    ASSERT_EQ(GMERR_OK, ret);

#if defined(ENV_RTOSV2X)
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName enableLogFold -cfgVal 1\n", g_toolPath);
    system(g_command);
#endif
    ClearSlowLog();
}

// 默认配置，开启事务并通过localhash 索引更新大量数据后回滚事务
TEST_F(SlowOperationEnhanced001, DFX_053_001_013)
{
#if defined(ENV_RTOSV2X)
    GtExecSystemCmd("echo [CLEAR_LOG] > ${TEST_HOME}/testcases/07_DFX/"
                    "053_SlowOperationEnhanced/Slow53_01.log");
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName enableLogFold -cfgVal 0\n", g_toolPath);
    system(g_command);
#else
    ClearSlowLog();
#endif
    int ret;
    char startHpeTime[30];
    char endHpeTime[30];
    GetHpeTime(startHpeTime, 30);
    
    int len1 = strlen(startHpeTime);
    startHpeTime[len1 - 1] = '\0';
    char const *fold_cfg_name = "enableLogFold";
    int modify_fold_mode_value = 0;
    int recover_fold_mode_value = 3;

    int32_t startPrimaryKey = 10;
    int32_t properyVal = 8;
    int32_t insertNum = INSERTNUM;
    char Label_config[] = "{\"max_record_num\":1000000, \"isFastReadUncommitted\":0}";
    ret = testGmcConnect_slow(&gConn, &gStmt, 0);
    ASSERT_EQ(GMERR_OK, ret);
    do
    {
        ret = GmcCreateVertexLabel(gStmt, gLabelSchemaJson, Label_config);
        BREAK_IFERR(ret);

        // 写入数据
        for (int i = startPrimaryKey; i < startPrimaryKey + insertNum; i++)
        {
            ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_INSERT);
            BREAK_IFERR(ret);
            ret = GtSetInsertVertexProperyty(gStmt, i, properyVal, properyVal, properyVal, properyVal);
            BREAK_IFERR(ret);
            ret = GmcExecute(gStmt);
            BREAK_IFERR(ret);
            ret = GtCheckAffectRows(gStmt, 1);
            BREAK_IFERR(ret);
        }
        BREAK_IFERR(ret);
        sleep(5);
        // 开启事务
        GmcTxConfigT txConfig;
        txConfig.readOnly = false;
        txConfig.transMode = GMC_TRANS_USED_IN_CS;
        txConfig.type = GMC_TX_ISOLATION_COMMITTED;
        txConfig.trxType = GMC_DEFAULT_TRX;
        ret = GmcTransStart(gConn, &txConfig);
        BREAK_IFERR(ret);

        // 通过 localhash key 更新数据
        ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_UPDATE);
        BREAK_IFERR(ret);
        int32_t updateVal = 128;
        ret = GtSetUpdateVertexProperyty(gStmt, updateVal, updateVal, updateVal, updateVal);
        BREAK_IFERR(ret);
        int32_t keyVal = properyVal;
        ret = GmcSetIndexKeyValue(gStmt, 0, GMC_DATATYPE_INT32, &keyVal, sizeof(keyVal));
        BREAK_IFERR(ret);
        ret = GmcSetIndexKeyName(gStmt, "LocalHashKey");
        BREAK_IFERR(ret);
        ret = GmcExecute(gStmt);
        BREAK_IFERR(ret);
        ret = GtCheckAffectRows(gStmt, insertNum);
        BREAK_IFERR(ret);

        double startTime = GtGetCurrentTime();
        ret = GmcTransRollBack(gConn);
        double spendTime = GtGetCurrentTime() - startTime;

        TEST_INFO("trans roll back, spend %.6f ms", spendTime);
        BREAK_IFERR(ret);

        uint32_t server_times = 0;
        sleep(2);
        GetHpeTimeInterval(endHpeTime, 30);
        
        int len2 = strlen(endHpeTime);

        endHpeTime[len2 - 1] = '\0';
        char Logsearch[1024];
        if (g_envType == 2) {
            system("hpecli log dump");
        }
        snprintf(Logsearch, 1024, "'/%s/,/%s/p'", startHpeTime, endHpeTime);
        
        // GtCheckServerSlowLog ( 整体耗时 , 操作类型 ， 命名空间 ， 表名 ， 连接编号 ， 连接类型 ， 索引种类 ，
        // 空间开关 ， 名字开关 ， 连接开关 ，
        // 复数日志开关 ， 操作数开关 ， 最长信息开关 ， 默认操作数 ， 默认最长信息 )
        printf("\n  [INFO]CHECK SERVER LOG \n");
        if (g_envType == 0) {
            ret = GtCheckServerSlowLog(&server_times, (char *)" Insert Vertex", NAMESPACE_CHECK, " \"VertexLabel\"", 0,
                                    (char *)" SYNC", (char *)" \"\"",
                                    true, true,
                                    CONN_CHECK, true, true, true, true, true, 1, (char *)" qry_exec_time");
        } else if (envAnos){
            ret = GtCheckServerSlowLog_new(&server_times, (char *)" Insert Vertex", NAMESPACE_CHECK, " \"VertexLabel\"", 0,
                                    (char *)" SYNC", (char *)" \"\"",
                                    NM_CHECK, true,
                                    CONN_CHECK, true, true, true, true, true, 1, (char *)" qry_exec_time",
                                    (char *)"Vertex", 2, 0, true, (char *)Logsearch);
        } else if (!envAnos){
            ret = GtCheckServerSlowLog_new(&server_times, (char *)" Insert Vertex", NAMESPACE_CHECK, " \"VertexLabel\"", 0,
                                    (char *)" SYNC", (char *)" \"\"",
                                    NM_CHECK, true,
                                    CONN_CHECK, true, true, true, true, true, 1, (char *)" qry_exec_time",
                                    (char *)"Vertex", 2, 0, true, (char *)Logsearch);
        }
        printf("\n  [INFO]total_time is :%d\n", server_times);
        if (server_times < 0 || server_times > (spendTime + 8) * 1000)
        {
            EXPECT_EQ(0, 1);
            printf("[INFO]server total time is not normal:%d\n", server_times);
        }
        char *log_nums = 0;
        ret = GtExecSystemCmd(&log_nums, "grep -r 'Insert Vertex' ./multi_server.txt|wc -l");
        EXPECT_EQ(GMERR_OK, ret);
        if (strncmp(log_nums, "1800", 4) != 0) {
            AW_FUN_Log(LOG_INFO, "wc -l ERROR\n");
        }
        AW_FUN_Log(LOG_INFO, "log of 'Insert Vertex' have %s items\n", log_nums);
        free(log_nums);
        printf("\n  [INFO]CHECH CLIENT NORMAL LOG \n");
        uint32_t opWholeTime = 0;
        uint32_t ConcatMsgTime = 0;
        uint32_t RspTransmissionTime = 0;
        uint32_t cltPostOpTime = 0;

        // GtCheckClientSlowLog_KV_Graph ( 操作类型 ， 命名空间 ， 表名 ， 连接编号 ， 索引种类 ， 索引开关 ， 空间开关 ，
        // 名字开关 ， 连接开关 ， 复数日志开关 ，整体时间 ， 连接消息时间 ， 接受时间 ， 控制时间 )

        GtCheckClientSlowLog_KV_Graph((char *)" Update Vertex", NAMESPACE_CHECK,
                                    (char *)" \"VertexLabel\"", 0, " \"LocalHashKey\"", true,
                                      NM_CHECK, true, CONN_CHECK, true, &opWholeTime, &ConcatMsgTime,
                                      &RspTransmissionTime, &cltPostOpTime);
        printf("opWholeTime is :%d\n", opWholeTime);
        printf("ConcatMsgTime is :%d\n", ConcatMsgTime);
        printf("RspTransmissionTime is :%d\n", RspTransmissionTime);
        printf("cltPostOpTime is :%d\n", cltPostOpTime);

    } while (0);
    EXPECT_EQ(GMERR_OK, ret);

    GmcDropVertexLabel(gStmt, gLabelName);
    ret = testGmcDisconnect(gConn, gStmt);
    ASSERT_EQ(GMERR_OK, ret);

#if defined(ENV_RTOSV2X)
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName enableLogFold -cfgVal 1\n", g_toolPath);
    system(g_command);
#endif
    ClearSlowLog();
}

// 默认配置，开启事务并通过hashcluster 索引更新大量数据后回滚事务
TEST_F(SlowOperationEnhanced001, DFX_053_001_014)
{
#if defined(ENV_RTOSV2X)
    GtExecSystemCmd("echo [CLEAR_LOG] > ${TEST_HOME}/testcases/07_DFX/"
                    "053_SlowOperationEnhanced/Slow53_01.log");
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName enableLogFold -cfgVal 0\n", g_toolPath);
    system(g_command);
#else
    ClearSlowLog();
#endif
    int ret;
    char startHpeTime[30];
    char endHpeTime[30];
    GetHpeTime(startHpeTime, 30);
    
    int len1 = strlen(startHpeTime);
    startHpeTime[len1 - 1] = '\0';
    char const *fold_cfg_name = "enableLogFold";
    int modify_fold_mode_value = 0;
    int recover_fold_mode_value = 3;

    int32_t startPrimaryKey = 10;
    int32_t properyVal = 8;
    int32_t insertNum = INSERTNUM;
    char Label_config[] = "{\"max_record_num\":1000000, \"isFastReadUncommitted\":0}";
    ret = testGmcConnect_slow(&gConn, &gStmt, 0);
    ASSERT_EQ(GMERR_OK, ret);
    do
    {
        ret = GmcCreateVertexLabel(gStmt, gLabelSchemaJson, Label_config);
        ASSERT_EQ(GMERR_OK, ret);

        // 写入数据
        for (int i = startPrimaryKey; i < startPrimaryKey + insertNum; i++)
        {
            ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_INSERT);
            ASSERT_EQ(GMERR_OK, ret);
            ret = GtSetInsertVertexProperyty(gStmt, i, properyVal, properyVal, properyVal, properyVal);
            ASSERT_EQ(GMERR_OK, ret);
            ret = GmcExecute(gStmt);
            ASSERT_EQ(GMERR_OK, ret);
            ret = GtCheckAffectRows(gStmt, 1);
            ASSERT_EQ(GMERR_OK, ret);
        }
        ASSERT_EQ(GMERR_OK, ret);
        sleep(5);
        // 开启事务
        GmcTxConfigT txConfig;
        txConfig.readOnly = false;
        txConfig.transMode = GMC_TRANS_USED_IN_CS;
        txConfig.type = GMC_TX_ISOLATION_COMMITTED;
        txConfig.trxType = GMC_DEFAULT_TRX;
        ret = GmcTransStart(gConn, &txConfig);
        ASSERT_EQ(GMERR_OK, ret);

        // 通过 hashcluster key 更新数据
        ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_UPDATE);
        ASSERT_EQ(GMERR_OK, ret);
        int32_t updateVal = 128;
        ret = GtSetUpdateVertexProperyty(gStmt, updateVal, updateVal, updateVal, updateVal);
        ASSERT_EQ(GMERR_OK, ret);
        int32_t keyVal = properyVal;
        ret = GmcSetIndexKeyValue(gStmt, 0, GMC_DATATYPE_UINT32, &keyVal, sizeof(keyVal));
        ASSERT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(gStmt, "HashClusterKey");
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcExecute(gStmt);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GtCheckAffectRows(gStmt, insertNum);
        ASSERT_EQ(GMERR_OK, ret);

        double startTime = GtGetCurrentTime();
        ret = GmcTransRollBack(gConn);
        double spendTime = GtGetCurrentTime() - startTime;
        ASSERT_EQ(GMERR_OK, ret);
        uint32_t server_times = 0;
        sleep(2);
        GetHpeTimeInterval(endHpeTime, 30);
        
        int len2 = strlen(endHpeTime);

        endHpeTime[len2 - 1] = '\0';
        char Logsearch[1024];
        if (g_envType == 2) {
            system("hpecli log dump");
        }
        snprintf(Logsearch, 1024, "'/%s/,/%s/p'", startHpeTime, endHpeTime);
        
        // GtCheckServerSlowLog ( 整体耗时 , 操作类型 ， 命名空间 ， 表名 ， 连接编号 ， 连接类型 ， 索引种类 ，
        // 空间开关 ， 名字开关 ， 连接开关 ，
        // 复数日志开关 ， 操作数开关 ， 最长信息开关 ， 默认操作数 ， 默认最长信息 )
        printf("\n  [INFO]CHECK SERVER LOG \n");
        if (g_envType == 0) {
            ret = GtCheckServerSlowLog(&server_times, (char *)" Insert Vertex", NAMESPACE_CHECK, " \"VertexLabel\"", 0,
                                    (char *)" SYNC", (char *)" \"\"",
                                    true, true,
                                    CONN_CHECK, true, true, true, true, true, 1, (char *)" qry_exec_time");
        } else if (envAnos){
            ret = GtCheckServerSlowLog_new(&server_times, (char *)" Insert Vertex", NAMESPACE_CHECK, " \"VertexLabel\"", 0,
                                    (char *)" SYNC", (char *)" \"\"",
                                    NM_CHECK, true,
                                    CONN_CHECK, true, true, true, true, true, 1, (char *)" qry_exec_time",
                                    (char *)"Vertex", 2, 0, true, (char *)Logsearch);
        } else if (!envAnos){
            ret = GtCheckServerSlowLog_new(&server_times, (char *)" Insert Vertex", NAMESPACE_CHECK, " \"VertexLabel\"", 0,
                                    (char *)" SYNC", (char *)" \"\"",
                                    NM_CHECK, true,
                                    CONN_CHECK, true, true, true, true, true, 1, (char *)" qry_exec_time",
                                    (char *)"Vertex", 2, 0, true, (char *)Logsearch);
        }
        printf("\n  [INFO]total_time is :%d\n", server_times);
        if (server_times < 0 || server_times > (spendTime + 8) * 1000)
        {
            EXPECT_EQ(0, 1);
            printf("[INFO]server total time is not normal:%d\n", server_times);
        }
        char *log_nums = 0;
        ret = GtExecSystemCmd(&log_nums, "grep -r 'Insert Vertex' ./multi_server.txt|wc -l");
        EXPECT_EQ(GMERR_OK, ret);
        if (strncmp(log_nums, "1800", 4) != 0) {
            AW_FUN_Log(LOG_INFO, "wc -l ERROR\n");
        }
        AW_FUN_Log(LOG_INFO, "log of 'Insert Vertex' have %s items\n", log_nums);
        free(log_nums);
        printf("\n  [INFO]CHECH CLIENT NORMAL LOG \n");
        uint32_t opWholeTime = 0;
        uint32_t ConcatMsgTime = 0;
        uint32_t RspTransmissionTime = 0;
        uint32_t cltPostOpTime = 0;

        // GtCheckClientSlowLog_KV_Graph ( 操作类型 ， 命名空间 ， 表名 ， 连接编号 ， 索引种类 ， 索引开关 ， 空间开关 ，
        // 名字开关 ， 连接开关 ， 复数日志开关 ，整体时间 ， 连接消息时间 ， 接受时间 ， 控制时间 )

        GtCheckClientSlowLog_KV_Graph((char *)" Update Vertex", NAMESPACE_CHECK,
                        (char *)" \"VertexLabel\"", 0, " \"HashClusterKey\"", true,
                                      NM_CHECK, true, CONN_CHECK, true, &opWholeTime, &ConcatMsgTime,
                                      &RspTransmissionTime, &cltPostOpTime);
        printf("opWholeTime is :%d\n", opWholeTime);
        printf("ConcatMsgTime is :%d\n", ConcatMsgTime);
        printf("RspTransmissionTime is :%d\n", RspTransmissionTime);
        printf("cltPostOpTime is :%d\n", cltPostOpTime);
    } while (0);
    EXPECT_EQ(GMERR_OK, ret);

    GmcDropVertexLabel(gStmt, gLabelName);
    ret = testGmcDisconnect(gConn, gStmt);
    ASSERT_EQ(GMERR_OK, ret);

#if defined(ENV_RTOSV2X)
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName enableLogFold -cfgVal 1\n", g_toolPath);
    system(g_command);
#endif
    ClearSlowLog();
}
