[{"type": "record", "name": "T30", "fields": [{"name": "F10", "type": "uint64", "nullable": false}, {"name": "F0", "type": "char", "nullable": false}, {"name": "F1", "type": "uchar", "nullable": false}, {"name": "F2", "type": "int8", "nullable": false}, {"name": "F3", "type": "uint8", "nullable": false}, {"name": "F4", "type": "int16", "nullable": false}, {"name": "F5", "type": "uint16", "nullable": false}, {"name": "F6", "type": "int32", "nullable": false}, {"name": "F7", "type": "uint32", "nullable": false}, {"name": "F8", "type": "boolean", "nullable": false}, {"name": "F9", "type": "int64", "nullable": false}, {"name": "F11", "type": "float", "nullable": false}, {"name": "F12", "type": "double", "nullable": false}, {"name": "F13", "type": "time", "nullable": false}, {"name": "F16", "type": "fixed", "size": 7, "nullable": true}, {"name": "F17", "type": "uint8: 8", "nullable": false}, {"name": "F18", "type": "uint16: 12", "nullable": false}, {"name": "F19", "type": "uint32: 28", "nullable": false}, {"name": "F20", "type": "uint64: 58", "nullable": false}, {"name": "FFF", "type": "uint32", "nullable": true, "default": 90, "comment": "备注"}, {"name": "FF", "type": "uint32", "nullable": true}, {"name": "F21", "type": "partition", "nullable": false}, {"name": "F22", "type": "uint64", "nullable": false}, {"name": "F23", "type": "uint64", "nullable": false}, {"name": "F24", "type": "uint64", "nullable": false}, {"name": "F25", "type": "uint64", "nullable": false}, {"name": "F26", "type": "uint64", "nullable": false}, {"name": "F27", "type": "uint64", "nullable": false}, {"name": "F28", "type": "uint64", "nullable": false}, {"name": "F29", "type": "uint64", "nullable": false}, {"name": "F30", "type": "uint64", "nullable": false}, {"name": "F31", "type": "uint64", "nullable": false}, {"name": "F32", "type": "uint64", "nullable": false}, {"name": "F33", "type": "uint64", "nullable": false}, {"name": "F34", "type": "uint64", "nullable": false}, {"name": "F35", "type": "uint64", "nullable": false}, {"name": "F36", "type": "uint64", "nullable": false}, {"name": "F37", "type": "uint64", "nullable": false}, {"name": "vr_id", "type": "uint32", "comment": "Vs索引"}, {"name": "vrf_index", "type": "uint32", "comment": "VpnInstace索引"}, {"name": "dest_ip_addr", "type": "uint32", "comment": "目的地址"}, {"name": "mask_len", "type": "uint8", "comment": "掩码长度"}], "keys": [{"node": "T30", "name": "T30_PK", "fields": ["F10"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "T30", "name": "hashcluster_key1", "index": {"type": "hashcluster"}, "fields": ["F22"], "constraints": {"unique": true}}, {"node": "T30", "name": "hashcluster_key2", "index": {"type": "hashcluster"}, "fields": ["F23"], "constraints": {"unique": true}}, {"node": "T30", "name": "hashcluster_key3", "index": {"type": "hashcluster"}, "fields": ["F24"], "constraints": {"unique": true}}, {"node": "T30", "name": "hashcluster_key4", "index": {"type": "hashcluster"}, "fields": ["F25"], "constraints": {"unique": true}}, {"node": "T30", "name": "hashcluster_key5", "index": {"type": "hashcluster"}, "fields": ["F26"], "constraints": {"unique": true}}, {"node": "T30", "name": "hashcluster_key6", "index": {"type": "hashcluster"}, "fields": ["F27"], "constraints": {"unique": true}}, {"node": "T30", "name": "hashcluster_key7", "index": {"type": "hashcluster"}, "fields": ["F27"], "constraints": {"unique": true}}, {"node": "T30", "name": "hashcluster_key8", "index": {"type": "hashcluster"}, "fields": ["F27"], "constraints": {"unique": true}}, {"node": "T30", "name": "hashcluster_key9", "index": {"type": "hashcluster"}, "fields": ["F27"], "constraints": {"unique": false}}, {"node": "T30", "name": "hashcluster_key10", "index": {"type": "hashcluster"}, "fields": ["F27"], "constraints": {"unique": false}}, {"node": "T30", "name": "hashcluster_key11", "index": {"type": "hashcluster"}, "fields": ["F27"], "constraints": {"unique": false}}, {"node": "T30", "name": "hashcluster_key12", "index": {"type": "hashcluster"}, "fields": ["F27"], "constraints": {"unique": false}}, {"node": "T30", "name": "hashcluster_key13", "index": {"type": "hashcluster"}, "fields": ["F27"], "constraints": {"unique": false}}, {"node": "T30", "name": "hashcluster_key14", "index": {"type": "hashcluster"}, "fields": ["F27"], "constraints": {"unique": false}}, {"node": "T30", "name": "hashcluster_key15", "index": {"type": "hashcluster"}, "fields": ["F27"], "constraints": {"unique": false}}, {"node": "T30", "name": "hashcluster_key16", "index": {"type": "hashcluster"}, "fields": ["F27"], "constraints": {"unique": false}}, {"node": "T30", "name": "ip4forward_lpm", "index": {"type": "lpm4_tree_bitmap"}, "fields": ["vr_id", "vrf_index", "dest_ip_addr", "mask_len"], "constraints": {"unique": true}}]}]