/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: dml_common.h.h
 * Author:
 * Create:
 */
#ifndef INCRE_PST_COMMON_H
#define INCRE_PST_COMMON_H

#include <sys/ipc.h>
#include <sys/shm.h>
#include "gtest/gtest.h"
#include "t_light.h"

#ifdef __cplusplus1
extern "C" {
#endif

#define LOG_IFERR(ret)                                                                                        \
    do {                                                                                                      \
        if ((ret) != T_OK) {                                                                                  \
            fprintf(stdout, "Error: %s:%d func:%s " #ret " = %d\n", __FILE__, __LINE__, __FUNCTION__, (ret)); \
        }                                                                                                     \
    } while (0)

#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];

// 同步连接
GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;

GmcStmtT *g_stmtAsync = NULL;
GmcConnT *g_connAsync = NULL;
const char *g_simpleLabel = "vl_simple";
const char *g_complexLabel = "vl_general_complex";
char *g_simpleSchema = NULL;
char *g_complexSchema = NULL;
char g_dbFilePath[1024] = {0};
char g_newDbFilePath[1024] = {0};

uint8_t *g_aptAddr = NULL;
uint32_t g_aptMemSize = 0;
pthread_t g_svThreadId = 0;
int g_shmid;

const char *g_config = "{\"max_record_num\" : 5000000, \"isFastReadUncommitted\":1, \"defragmentation\":true }";

const char *g_config2 = R"(
    {
        "max_record_count":1000000,
        "auto_increment":1,
        isFastReadUncommitted:1,
        defragmentation:true
    }
)";

char *g_simpleSet = (char *)R"({"A0": %i{1},
"A1": %i{10},
"A2": %i{100},
"A3": %i{1000},
"A4": %i{1,100000,0.5},
"A5": %i{10,100000,0.5}
})";

char *g_complexSet = (char *)R"({
"A0": %i{1},
"A1": %i{10},
"A2": %i{100},
"A3": %i{1000},
"A4": %i{1,100000,0.5},
"A5": %i{10,100000,0.5},
"A6": "%f{16}0",
"A7": "0x%f{32}0",
"A8": "0x%f{32}0",
"A9": "%f{20}c",
"M0": [
{ "B0": %i{1}, "B1": %i{10}, "B2": "0x%f{8}0", "B3": "0x%f{16}0", "B4": "%f{50}x1", "B5": "%f{10}t1" },
{ "B0": %i{2}, "B1": %i{20}, "B2": "0x%f{16}1", "B3": "0x%f{32}1", "B4": "%f{50}x2", "B5": "%f{10}t2" },
{ "B0": %i{3}, "B1": %i{30}, "B2": "0x%f{32}1", "B3": "0x%f{64}1", "B4": "%f{50}x3", "B5": "%f{10}t3" }
]
})";

int RestartGmserver()
{
    int ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/stop.sh -f");
    RETURN_IFERR(ret);
    ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh");
    RETURN_IFERR(ret);
    return GMERR_OK;
}

int CommonDropTable()
{
    int ret = GmcDropVertexLabel(g_stmt, g_complexLabel);
    RETURN_IFERR(ret);
    ret = GmcDropVertexLabel(g_stmt, g_simpleLabel);
    RETURN_IFERR(ret);
    return T_OK;
}

int CommonDDL()
{
    (void)GmcDropVertexLabel(g_stmt, g_simpleLabel);
    (void)GmcDropVertexLabel(g_stmt, g_complexLabel);
    // 简单表
    readJanssonFile("schema/vl_simple.gmjson", &g_simpleSchema);
    if (g_simpleSchema == NULL) {
        return T_FAILED;
    }
    int ret = GmcCreateVertexLabel(g_stmt, g_simpleSchema, g_config);
    RETURN_IFERR(ret);
    // 复杂表
    readJanssonFile("schema/vl_general_complex.gmjson", &g_complexSchema);
    if (g_complexSchema == NULL) {
        return T_FAILED;
    }
    ret = GmcCreateVertexLabel(g_stmt, g_complexSchema, g_config);
    RETURN_IFERR(ret);

    free(g_simpleSchema);
    free(g_complexSchema);
    g_simpleSchema = NULL;
    g_complexSchema = NULL;

    return T_OK;
}

int CommonDDLWithOutLocaLKey()
{
    (void)GmcDropVertexLabel(g_stmt, g_simpleLabel);
    (void)GmcDropVertexLabel(g_stmt, g_complexLabel);
    // 简单表
    readJanssonFile("../schema/vl_simple.gmjson", &g_simpleSchema);
    if (g_simpleSchema == NULL) {
        return T_FAILED;
    }
    int ret = GmcCreateVertexLabel(g_stmt, g_simpleSchema, g_config);
    RETURN_IFERR(ret);
    // 复杂表
    readJanssonFile("../schema/vl_general_complex2.gmjson", &g_complexSchema);
    if (g_complexSchema == NULL) {
        return T_FAILED;
    }
    ret = GmcCreateVertexLabel(g_stmt, g_complexSchema, g_config);
    RETURN_IFERR(ret);

    free(g_simpleSchema);
    free(g_complexSchema);
    g_simpleSchema = NULL;
    g_complexSchema = NULL;

    return T_OK;
}

int CommonDDL2()
{
    (void)GmcDropVertexLabel(g_stmt, g_simpleLabel);
    (void)GmcDropVertexLabel(g_stmt, g_complexLabel);
    // 简单表
    readJanssonFile("../schema/vl_simple.gmjson", &g_simpleSchema);
    if (g_simpleSchema == NULL) {
        return T_FAILED;
    }
    int ret = GmcCreateVertexLabel(g_stmt, g_simpleSchema, g_config);
    RETURN_IFERR(ret);
    // 复杂表
    readJanssonFile("../schema/vl_general_complex3.gmjson", &g_complexSchema);
    if (g_complexSchema == NULL) {
        return T_FAILED;
    }
    ret = GmcCreateVertexLabel(g_stmt, g_complexSchema, g_config2);
    RETURN_IFERR(ret);

    free(g_simpleSchema);
    free(g_complexSchema);
    g_simpleSchema = NULL;
    g_complexSchema = NULL;

    return T_OK;
}

int CommonInsert(int beginIndex, int endIndex)
{
    int ret = TestInsVertexSync(g_stmt, g_simpleLabel, g_simpleSet, beginIndex, endIndex);
    RETURN_IFERR(ret);
    ret = TestInsVertexSync(g_stmt, g_complexLabel, g_complexSet, beginIndex, endIndex);
    RETURN_IFERR(ret);
    return T_OK;
}

int CommonUpdate(int beginIndex, int endIndex)
{
    char indexName[] = "PrimaryKey";
    char *cond = (char *)"A0(int32)=%i{1}";
    char *updateFormat = (char *)"A1(int64)=%i{100};A2(uint32)=%i{1};A5(double)=%i{1,100000,0.5}";
    int ret = TestUpdVertexSync(g_stmt, g_simpleLabel, indexName, cond, updateFormat, beginIndex, endIndex);
    RETURN_IFERR(ret);
    updateFormat = (char *)"A1(int64)=%i{100};A2(uint32)=%i{1};A5(double)=%i{1,100000,0.5};M0[0].B0(int32)=%i{10};M0[2]"
        ".B5(string)=%f{20}f3";
    ret = TestUpdVertexSync(g_stmt, g_complexLabel, indexName, cond, updateFormat, beginIndex, endIndex);
    RETURN_IFERR(ret);
    return T_OK;
}

int CommonDelete(int beginIndex, int endIndex)
{
    char indexName[] = "PrimaryKey";
    char *cond = (char *)"A0(int32)=%i{1}";
    int ret = TestdelVertexSync(g_stmt, g_simpleLabel, indexName, cond, beginIndex, endIndex);
    RETURN_IFERR(ret);
    ret = TestdelVertexSync(g_stmt, g_complexLabel, indexName, cond, beginIndex, endIndex);
    RETURN_IFERR(ret);
    return T_OK;
}

int CommonCheckInCount(int expectCount, int beginIndex, int endIndex)
{
    char indexName[] = "PrimaryKey";
    char *cond = (char *)"A0(int32)=%i{1}";
    int ret = TestSelVertexCount(g_stmt, g_simpleLabel, indexName, cond, expectCount, beginIndex, endIndex);
    RETURN_IFERR(ret);
    ret = TestSelVertexCount(g_stmt, g_complexLabel, indexName, cond, expectCount, beginIndex, endIndex);
    RETURN_IFERR(ret);
    return T_OK;
}

int CommonCheckRecord(int beginIndex, int endIndex)
{
    char indexName[] = "PrimaryKey";
    char *cond = (char *)"A0(int32)=%i{1}";
    char *checkFormat = (char *)"A1(int64)=%i{10};A2(uint32)=%i{100};A5(double)=%i{10,100000,0.5}";
    int ret = TestSelVertexRecord(g_stmt, g_simpleLabel, indexName, cond, checkFormat, beginIndex, endIndex);
    RETURN_IFERR(ret);
    checkFormat =
        (char *)"A1(int64)=%i{10};A5(double)=%i{10,100000,0.5};M0[0].B0(int32)=%i{1};M0[2].B5(string)=%f{1000}t3";
    ret = TestSelVertexRecord(g_stmt, g_complexLabel, indexName, cond, checkFormat, beginIndex, endIndex);
    RETURN_IFERR(ret);
    return T_OK;
}

int CommonCheckUpdRecord(int beginIndex, int endIndex)
{
    char indexName[] = "PrimaryKey";
    char *cond = (char *)"A0(int32)=%i{1}";
    char *checkFormat = (char *)"A1(int64)=%i{100};A2(uint32)=%i{1};A5(double)=%i{1,100000,0.5}";
    int ret = TestSelVertexRecord(g_stmt, g_simpleLabel, indexName, cond, checkFormat, beginIndex, endIndex);
    RETURN_IFERR(ret);
    checkFormat = (char *)"A1(int64)=%i{100};A2(uint32)=%i{1};A5(double)=%i{1,100000,0.5};M0[0].B0(int32)=%i{10};M0[2]."
        "B5(string)=%f{20}f3";
    ret = TestSelVertexRecord(g_stmt, g_complexLabel, indexName, cond, checkFormat, beginIndex, endIndex);
    RETURN_IFERR(ret);
    return T_OK;
}

int BuildAndDml(int beginIndex, int endIndex)
{
    // 建表
    int ret = CommonDDL();
    RETURN_IFERR(ret);
    // 写入数据
    ret = CommonInsert(beginIndex, endIndex);
    RETURN_IFERR(ret);
    // 更新
    ret = CommonUpdate(beginIndex, endIndex);
    RETURN_IFERR(ret);
    // 删除部分
    ret = CommonDelete(beginIndex, endIndex / 2);
    RETURN_IFERR(ret);
    // 查询数据量
    ret = CommonCheckInCount(endIndex / 2 - beginIndex, beginIndex, endIndex);
    RETURN_IFERR(ret);
    // 查询更新结果
    ret = CommonCheckUpdRecord(beginIndex, endIndex / 2);
    RETURN_IFERR(ret);
    return T_OK;
}

int ReCheckDml(int beginIndex, int endIndex)
{
    // 查询数据量
    int ret = CommonCheckInCount(endIndex / 2 - beginIndex, beginIndex, endIndex);
    RETURN_IFERR(ret);
    // 查询结果
    ret = CommonCheckUpdRecord(beginIndex, endIndex / 2);
    RETURN_IFERR(ret);
    return T_OK;
}

int CommonPstCheck(int beginIndex, int endIndex)
{
    int ret = testGmcConnect(&g_conn, &g_stmt);
    RETURN_IFERR(ret);
    // 建表跑业务操作
    ret = BuildAndDml(beginIndex, endIndex);

    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    RETURN_IFERR(ret);
    testEnvClean();
    ret = RestartGmserver();
    RETURN_IFERR(ret);
    ret = testEnvInit(-1, false);
    RETURN_IFERR(ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    RETURN_IFERR(ret);

    // 重建表，验证数据
    ret = ReCheckDml(beginIndex, endIndex);
    RETURN_IFERR(ret);

    ret = CommonDropTable();
    RETURN_IFERR(ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    RETURN_IFERR(ret);
    testEnvClean();
    return T_OK;
}

bool RedoIsExists(char *dataFilePath)
{
    char temp[512] = {0};
    (void)sprintf(temp, "%s/gmdb_redo_0", dataFilePath);
    return FileIsExist(temp);
}

int InitAndConn()
{
    int ret = testEnvInit(-1, false);
    RETURN_IFERR(ret);
    ret = create_epoll_thread();
    RETURN_IFERR(ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    RETURN_IFERR(ret);
    return T_OK;
}

int RestartAndConn()
{
    // 重启
    int ret = testGmcDisconnect(g_conn, g_stmt);
    RETURN_IFERR(ret);
    testEnvClean();
    ret = RestartGmserver();
    RETURN_IFERR(ret);
    ret = testEnvInit(-1, false);
    RETURN_IFERR(ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    RETURN_IFERR(ret);
    return T_OK;
}

int DisConnAndClean()
{
    int ret = CommonDropTable();
    RETURN_IFERR(ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    RETURN_IFERR(ret);
    ret = close_epoll_thread();
    RETURN_IFERR(ret);
    testEnvClean();
    return T_OK;
}

/* ****** ipv4地址 转换为 uint32 ****** */
uint32_t TransIp(const char *ipStr)
{
    char strIpIndex[4] = {'\0'};
    uint32_t ipInt;
    uint32_t ipAdd = 0;
    int j = 0, a = 3;
    for (uint32_t i = 0; i <= strlen(ipStr); i++) { // 要用到'\0'
        if (ipStr[i] == '\0' || ipStr[i] == '.') {
            ipInt = atoi(strIpIndex);
            if (ipInt < 0 || ipInt > 255) {
                printf("IP地址有误\n");
                system("pause");
                return 0;
            }
            ipAdd += (ipInt * ((uint32_t)pow(256.0, a)));
            a--;
            memset(strIpIndex, 0, sizeof(strIpIndex));
            j = 0;
            continue;
        }
        strIpIndex[j] = ipStr[i];
        j++;
    }
    return ipAdd;
}

uint32_t TruncateFile(char *fileName, float percentage, char direction)
{
    FILE *fp = fopen(fileName, "rb+");
    if (fp == NULL) {
        printf("Failed to open file %s", fileName);
        return T_FAILED;
    }

    // 获取文件大小
    (void)fseek(fp, 0L, SEEK_END);
    long int fileSize = ftell(fp);
    if (fileSize == 0) {
        return T_FAILED;
    }
    // 计算截断位置
    long int sizeCount;
    char *buffer = NULL;
    long int truncatePos = fileSize * percentage;

    if (direction == 'b') {
        (void)fseek(fp, truncatePos, SEEK_SET);
        sizeCount = fileSize - truncatePos;
        buffer = (char *)malloc(fileSize - truncatePos);
        if (buffer == NULL) {
            (void)fclose(fp);
            return T_FAILED;
        }
        (void)fread(buffer, sizeof(char), fileSize - truncatePos, fp);
    } else if (direction == 'a') {
        (void)fseek(fp, 0, SEEK_SET);
        sizeCount = truncatePos;
        buffer = (char *)malloc(truncatePos);
        if (buffer == NULL) {
            (void)fclose(fp);
            return T_FAILED;
        }
        (void)fread(buffer, sizeof(char), truncatePos, fp);
    } else {
        printf("Invalid direction %c", direction);
        (void)fclose(fp);
        return T_FAILED;
    }
    (void)fseek(fp, 0, SEEK_SET);
    (void)ftruncate(fileno(fp), 0);
    (void)fwrite(buffer, sizeof(char), sizeCount, fp);
    (void)fclose(fp);
    return T_OK;
}

int CreateShareMem()
{
    key_t key = ftok(".", 'R');
    g_shmid = shmget(key, g_aptMemSize, IPC_CREAT | 0666);
    if (g_shmid == -1) {
        return T_FAILED;
    }
    g_aptAddr = (uint8_t *)shmat(g_shmid, NULL, 0);
    if (g_aptAddr == NULL) {
        return T_FAILED;
    }
    (void)memset(g_aptAddr, 0, g_aptMemSize);
    return T_OK;
}

int CleanShareMem()
{
    (void)memset(g_aptAddr, 0, g_aptMemSize);
    int ret = shmdt(g_aptAddr);
    RETURN_IFERR(ret);
    shmctl(g_shmid, IPC_RMID, NULL);
    return T_OK;
}

#ifdef FEATURE_CLT_SERVER_SAME_PROCESS
int GetRedoBufferRsm(uint8_t **addr, uint32_t *size, bool *forRecovery)
{
    *addr = g_aptAddr;
    *size = g_aptMemSize;
    *forRecovery = true;
    return T_OK;
}

int PstCompress(uint8_t *dest, uint32_t *destSize, const uint8_t *src, uint32_t srcSize)
{
    uint32_t dstLen = 0;
    uint32_t i = 0;
    while (i < srcSize) {
        uint8_t count = 1;
        while (i + count < srcSize && src[i] == src[i + count] && count < 255) {
            count++;
        }
        if (count > 1 || src[i] == 0xFF) {
            // 如果有重复的字节或者是0xFF，则需要进行压缩
            dest[dstLen++] = 0xFF;
            dest[dstLen++] = count;
            dest[dstLen++] = src[i];
            i += count;
        } else {
            // 否则直接复制
            dest[dstLen++] = src[i++];
        }
    }
    *destSize = dstLen;
    return T_OK;
}

int PstUnCompress(uint8_t *dest, uint32_t *destSize, const uint8_t *src, uint32_t srcSize)
{
    uint32_t dstLen = 0;
    uint32_t i = 0;
    while (i < srcSize) {
        if (src[i] == 0xFF) {
            // 如果是0xFF，则需要进行解压
            uint8_t count = src[i + 1];
            uint8_t value = src[i + 2];
            for (int j = 0; j < count; j++) {
                dest[dstLen++] = value;
            }
            i += 3;
        } else {
            // 否则直接复制
            dest[dstLen++] = src[i++];
        }
    }
    *destSize = dstLen;
    return T_OK;
}
#endif

typedef enum AdaptFunc {
    NONE,
    RSM,
    COMPRESS,
    RSM_AND_COMPRESS,
} AdaptFuncE;

int AdapAndStartServer(AdaptFuncE func)
{
    int ret = 0;
    if (g_runStat == RUN_STAT_SUCC) {
        return 0;
    }
    if (g_runStat == RUN_STAT_INIT) {
        while (ret < 500 && g_runStat == RUN_STAT_INIT) {
            ++ret;
            usleep(10000);
        }
        return 0;
    }
    g_runStat = RUN_STAT_INIT;
    pthread_mutex_init(&g_logLockTest, NULL);
    pthread_mutex_init(&g_logLockClient, NULL);
    pthread_mutex_init(&g_connLock, NULL);
    pthread_mutex_init(&g_connConcurrent, NULL);
    memset(g_logFilter, 0, sizeof(g_logFilter));
    if (g_isReadConfig == false) {
        getSysConfig();
    }

    system("ipcrm -a");
#ifdef FEATURE_CLT_SERVER_SAME_PROCESS
    GmsAdptFuncsT adpt = { 0 };
    if (func == RSM || func == RSM_AND_COMPRESS) {
        adpt.getReservedMemFunc = (GmsGetRedoBufferReservedSegmentFuncT)GetRedoBufferRsm;
    }
    if (func == COMPRESS || func == RSM_AND_COMPRESS) {
        adpt.persistCompressFunc = PstCompress;
        adpt.persistDecompressFunc = PstUnCompress;
    }
    ret = GmsRegAdaptFuncs(&adpt);
    if (ret != 0) {
        AW_FUN_Log(LOG_INFO, "[testInit1] GmsRegAdaptFuncs failed, ret = %d.\n", ret);
    }

    char *cmdString[4] = {(char *)"gmserver", (char *)"-p", (char *)g_sysGMDBCfg};
    ret = GmsServerMain(3, cmdString);
    if (ret != 0) {
        AW_FUN_Log(LOG_INFO, "[testEnvInit] GmsServerMain failed, ret = %d.\n", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
#endif
    ret = OpenEpollFunFromHPE();
    if (ret != 0) {
        AW_FUN_Log(LOG_INFO, "[testInit1] OpenEpollFunFromHPE failed, ret = %d.\n", ret);
        g_runStat = RUN_STAT_FAIL;
        return FAILED;
    }
    ret = GmcInit();
    if (ret != 0) {
        AW_FUN_Log(LOG_INFO, "[testInit1] GmcInit failed, ret = %d.\n", ret);
        g_runStat = RUN_STAT_FAIL;
        return FAILED;
    }
    ret = testPrepareNameSpace();
    if (ret != 0) {
        AW_FUN_Log(LOG_INFO, "[testInit1] testPrepareNameSpace failed, ret = %d.\n", ret);
        g_runStat = RUN_STAT_FAIL;
        return FAILED;
    }
    g_runStat = RUN_STAT_SUCC;
    return 0;
}

int executeCmd(char *cmd, char *v1 = NULL)
{
    FILE *pf = popen(cmd, "r");
    if (pf == NULL) {
        printf("[executeCmd] popen(%s) error.\n", cmd);
        return -1;
    }
    int len = 0;
    char cmdOutput[4096] = {0};
    while (NULL != fgets(cmdOutput + len, 4000 - len, pf)) {
        len = strlen(cmdOutput);
    }
    pclose(pf);
    if (v1) {
        snprintf(v1, strlen(cmdOutput), "%s", cmdOutput);
    }
    return 0;
}

int CheckFetchNone(char *fileName)
{
    char alarmnum[64];
    char cmd[80];
    int length = strlen(fileName);
    (void)snprintf(cmd, 33 + length, "cat ./%s |grep 'index' |wc -l", fileName);
    printf("check fetch_none num is:%s\n", cmd);
    executeCmd(cmd, (char *)alarmnum);
    int num = atoi(alarmnum);
    return num;
}

int checkSuoRong(const char *selectLabel, bool noneSysview = true, bool checkCnt = true,
    char *checkCntValue = (char *)"1", bool checkUnstable = false, int checkErrorValue = 2, int ctl_multi = 0)
{
    int ret = 0;
    char aa[80] = "0 ./check.txt";
    char *sysview = NULL;
    char const *viewname = "V\\$STORAGE_HEAP_STAT";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f %s >check.txt", g_toolPath, viewname, selectLabel);
    printf("%s\n", g_command);
    system(g_command);
    memset(g_command, 0, sizeof(g_command));
    readJanssonFile("check.txt", &sysview);
    printf("\033[0;1;31m[%s:%d]\n label_schema:%s\n\033[0m", __func__, __LINE__, sysview);
    sleep(1);
    if (CheckFetchNone((char *)"check.txt") == 0 && noneSysview == true) {
        EXPECT_EQ(ctl_multi, 1);
        printf("[INFO]there is no sysview\n");
    }
    FILE *pf = fopen("./check.txt", "r");
    if (pf == NULL) {
        return -1;
    }
    int length;
    char defragmntationcnt[512];
    int actcnt = 0;
    int size = 0;
    char tmpBuff[512];
    char str[3][512];
    while (fgets(tmpBuff, sizeof(tmpBuff), pf) != NULL) {
        if (tmpBuff[0] == '#') {
            continue;
        }
        length = strlen(tmpBuff);
        while (length > 0 && (tmpBuff[length - 1] == '\n' || tmpBuff[length - 1] == '\r')) {
            tmpBuff[length - 1] = '\0';
            --length;
        }
        (void)sscanf(tmpBuff, "%s %s", str[0], str[2]);
        if (str[0][0] == ' ' || str[2][0] == '\0') {
            continue;
        }
        if (strcmp(str[0], "defragmntationcnt:") == 0) {
            memset(aa, 0, 80);
            length = strlen(checkCntValue);
            actcnt = atof(str[2]);
            int expcnt = atof(checkCntValue);
            printf("actual defragmntationcnt is :%d\n", actcnt);
            printf("expect defragmntationcnt is :%d\n", expcnt);
            if (checkCnt) {
                if (!checkUnstable) {
                    EXPECT_EQ(actcnt, expcnt);
                } else {
                    // EXPECT_GE(actcnt, expcnt);//开启宽容校验误差，可以设置允许出现的波动值
                    if (actcnt >= expcnt - checkErrorValue && actcnt <= expcnt + checkErrorValue) {
                        EXPECT_EQ(0, 0);
                    } else {
                        EXPECT_EQ(0, 1);
                    }
                }
            }
        }
    }
    (void)fclose(pf);
    system("rm -r -f check.txt");
    free(sysview);
    return GMERR_OK;
}

int checkSuoRongEnableClusterHash(const char *selectLabel, bool noneSysview = true, bool checkCnt = true,
    char *checkCntValue = (char *)"1", bool checkUnstable = false, int checkErrorValue = 2, int ctl_multi = 0)
{
    int ret = 0;
    char aa[80] = "0 ./check.txt";
    char *sysview = NULL;
    char const *viewname = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f %s >check.txt", g_toolPath, viewname, selectLabel);
    printf("%s\n", g_command);
    system(g_command);
    memset(g_command, 0, sizeof(g_command));
    readJanssonFile("check.txt", &sysview);
    printf("\033[0;1;31m[%s:%d]\n label_schema:%s\n\033[0m", __func__, __LINE__, sysview);
    sleep(1);
    if (CheckFetchNone((char *)"check.txt") == 0 && noneSysview == true) {
        EXPECT_EQ(ctl_multi, 1);
        printf("[INFO]there is no sysview\n");
    }
    FILE *pf = fopen("./check.txt", "r");
    if (pf == NULL) {
        return -1;
    }
    int length;
    char defragmntationcnt[512];
    int actcnt = 0;
    int size = 0;
    char tmpBuff[512];
    char str[3][512];
    while (fgets(tmpBuff, sizeof(tmpBuff), pf) != NULL) {
        if (tmpBuff[0] == '#') {
            continue;
        }
        length = strlen(tmpBuff);
        while (length > 0 && (tmpBuff[length - 1] == '\n' || tmpBuff[length - 1] == '\r')) {
            tmpBuff[length - 1] = '\0';
            --length;
        }
        (void)sscanf(tmpBuff, "%s %s", str[0], str[2]);
        if (str[0][0] == ' ' || str[2][0] == '\0') {
            continue;
        }
        if (strcmp(str[0], "SCALE_IN_THREHOLD_MEET_COUNT:") == 0) {
            memset(aa, 0, 80);
            length = strlen(checkCntValue);
            actcnt = atof(str[2]);
            int expcnt = atof(checkCntValue);
            printf("actual SCALE_IN_THREHOLD_MEET_COUNT is :%d\n", actcnt);
            printf("expect SCALE_IN_THREHOLD_MEET_COUNT is :%d\n", expcnt);
            if (checkCnt) {
                if (!checkUnstable) {
                    EXPECT_EQ(actcnt, expcnt);
                } else {
                    // EXPECT_GE(actcnt, expcnt);//开启宽容校验误差，可以设置允许出现的波动值
                    if (actcnt >= expcnt - checkErrorValue && actcnt <= expcnt + checkErrorValue) {
                        EXPECT_EQ(0, 0);
                    } else {
                        EXPECT_EQ(0, 1);
                    }
                }
            }
        }
    }
    (void)fclose(pf);
    system("rm -r -f check.txt");
    free(sysview);
    return GMERR_OK;
}

#ifdef __cplusplus_1
}
#endif

#endif /* INCRE_PST_COMMON_H */
