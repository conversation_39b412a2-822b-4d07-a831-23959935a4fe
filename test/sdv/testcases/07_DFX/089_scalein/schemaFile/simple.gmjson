[{"comment": "全字段简单表", "version": "2.0", "type": "record", "name": "simple", "config": {"check_validity": true}, "fields": [{"name": "rfield_int8", "type": "int8", "nullable": false}, {"name": "rfield_int16", "type": "int16", "nullable": false}, {"name": "rfield_int32", "type": "int32", "nullable": false}, {"name": "rfield_int64", "type": "int64", "nullable": false}, {"name": "rfield_uint8", "type": "uint8", "nullable": false}, {"name": "rfield_uint16", "type": "uint16", "nullable": false}, {"name": "rfield_uint32", "type": "uint32", "nullable": false}, {"name": "rfield_uint64", "type": "uint64", "nullable": false}, {"name": "rfield_float", "type": "float", "nullable": false}, {"name": "rfield_double", "type": "double", "nullable": false}, {"name": "rfield_boolean", "type": "boolean", "nullable": false}, {"name": "rfield_fixed", "type": "fixed", "size": 8, "nullable": false}, {"name": "rfield_bitmap", "type": "bitmap", "size": 8, "nullable": false}, {"name": "rfield_time", "type": "time", "nullable": false}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "simple", "fields": ["rfield_int8", "rfield_int16", "rfield_int32", "rfield_int64"], "constraints": {"unique": true}, "comment": "主键索引"}, {"name": "local_key", "index": {"type": "local"}, "node": "simple", "fields": ["rfield_uint64"], "constraints": {"unique": false}, "comment": "local索引"}, {"name": "localhash_key", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "simple", "fields": ["rfield_uint64"], "constraints": {"unique": true}, "comment": "local hash索引"}, {"name": "hashcluster_key", "index": {"type": "hashcluster"}, "node": "simple", "fields": ["rfield_uint64"], "constraints": {"unique": false}, "comment": "hash cluster索引"}]}]