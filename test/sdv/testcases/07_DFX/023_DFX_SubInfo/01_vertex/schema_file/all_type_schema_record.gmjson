[{"type": "record", "name": "T20_all_type_record", "fields": [{"name": "R7", "type": "uint32", "nullable": false}, {"name": "R0", "type": "char", "nullable": true}, {"name": "R1", "type": "uchar", "nullable": true}, {"name": "R2", "type": "int8", "nullable": true}, {"name": "R3", "type": "uint8", "nullable": true}, {"name": "R4", "type": "int16", "nullable": true}, {"name": "R5", "type": "uint16", "nullable": true}, {"name": "R6", "type": "int32", "nullable": true}, {"name": "R8", "type": "boolean", "nullable": true}, {"name": "R9", "type": "int64", "nullable": true}, {"name": "R10", "type": "uint64", "nullable": true}, {"name": "R11", "type": "float", "nullable": true}, {"name": "R12", "type": "double", "nullable": true}, {"name": "R13", "type": "time", "nullable": true}, {"name": "R14", "type": "string", "nullable": true, "size": 100}, {"name": "R15", "type": "bytes", "nullable": true, "size": 10}, {"name": "R16", "type": "fixed", "nullable": true, "size": 5}, {"name": "R17", "type": "uint32", "nullable": true}], "keys": [{"node": "T20_all_type_record", "name": "T20_PK_record", "fields": ["R7"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]