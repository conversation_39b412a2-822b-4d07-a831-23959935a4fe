//頭文件
extern "C" {
}

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#define FULLTABLE 0xff
//同步连接
GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
char *MS_VLabel_schema = NULL;
char *NoneKey_VLabel_schema = NULL;
char *PriKey_VLabel_schema = NULL;
char *lhKey_VLabel_schema = NULL;
char *hcKey_VLabel_schema = NULL;
char *lcKey_VLabel_schema = NULL;
char *lpmKey_VLabel_schema = NULL;
char *lpm6Key_VLabel_schema = NULL;
char *SKey_VLabel_schema = NULL;
char *Pri<PERSON>ey_ELabel_schema = NULL;
char *PriKey_SrcLabel_schema = NULL;
char *PriKey_DstLabel_schema = NULL;
char *suorong_Label_schema = NULL;
char *PriKey_Source_VLabel_schema = NULL;
const char *normal_config_json = "{\"max_record_num\":10000000,\"defragmentation\":true }";
const char *S_MS_VLabel_Name = "LABEL_NAME=\\'vertex_ms\\'";
const char *V_config = "{\"max_record_num\":100000000}";
GmcConnT *s_conn[MAX_CONN_SIZE];
GmcStmtT *s_stmt[MAX_CONN_SIZE];
void *g_vertexLabel[MAX_CONN_SIZE];
int ret = 0;
unsigned int len = 0;
int affectRows = 0;
GmcTxConfigT MSTrxConfig;
const char *sLabelName = "VertexLabel";
const char *MS_VLabel_Name = "vertex_ms";
const char *MS_VLabel_SF_Name = "vertex_ms_superfields";
const char *MS_VLabel_Key_Name = "vertex_ms_key";
const char *NoneKey_Label_Name = "vertex_NoneKey";
const char *PriKey_Label_Name = "vertex_PriKey";
const char *lsKey_Label_Name = "vertex_lhKey";
const char *hcKey_Label_Name = "vertex_hcKey";
const char *lcKey_Label_Name = "vertex_lcKey";
const char *lpmKey_Label_Name = "ip4forward_2";
const char *lpm6Key_Label_Name = "ip6forward_2";
const char *suorongKey_Label_Name = "suorong";
const char *SKey_Label_Name = "vertex_SKey";
const char *T_PriKey_Label_Name = "Tree_PriKey";
const char *PriKey_ELabel_Name = "edge_ms";
const char *PriKey_SrcLabel_Name = "vertex_ms_src";
const char *PriKey_DstLabel_Name = "vertex_ms_dst";
const char *MPriKey_Label_Name = "vertex_morepriKey";
const char *PriKey_Source_Label_Name = "vertex_PriKey_Source";

static const char *sLabelSchemaJson =
    R"([{
        "type":"record",
        "name":"VertexLabel",
        "fields":
            [
                {"name":"F0", "type":"uint32", "nullable":false},
                {"name":"F1", "type":"uint32", "nullable":false},
                {"name":"F2", "type":"uint32", "nullable":false},
                {"name":"F3", "type":"uint32", "nullable":false},
                {"name":"F4", "type":"uint32", "nullable":false}
            ],
        "keys":
            [
                {
                    "node":"VertexLabel",
                    "name":"PrimaryKey",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                },
                {
                    "node":"VertexLabel",
                    "name":"LocalHashKey",
                    "fields":["F1"],
                    "index":{"type":"localhash"},
                    "constraints":{"unique":false}
                },
                {
                    "node":"VertexLabel",
                    "name":"HashClusterKey",
                    "fields":["F2"],
                    "index":{"type":"hashcluster"},
                    "constraints":{"unique":false}
                },
                {
                    "node":"VertexLabel",
                    "name":"LocalKey",
                    "fields":["F3"],
                    "index":{"type":"local"},
                    "constraints":{"unique":false}
                }
            ]
        }])";
typedef enum tagRunMode { MODE_EULER = 0, MODE_DOPRA = 1, MODE_HONGMENG = 2 } GtRunModeE;

#define COMPARE_NE(expect_value, actual_value)                                \
    do {                                                                      \
        if ((expect_value) == (actual_value)) {                               \
            printf("[Error file: %s, line: %d]\n", __FILE__, __LINE__);       \
            printf("Value of: " #actual_value " = %p\n", (actual_value));     \
            printf("Not Expected: " #expect_value " = %p\n", (expect_value)); \
            return -1;                                                        \
        };                                                                    \
    } while (0)

// 配置stlm日志
int GtSetStlmLog(bool isDisableSuppres)
{
    if (g_runMode != MODE_DOPRA) {
        return GMERR_OK;
    }
    int ret;
    if (isDisableSuppres) {
        // 修改日志抑制条件 (使stlm日志不容易被抑制)
        ret = system("/usr/local/bin/stlmbox --logsuppressparamset --suppress_level 1 --suppress_interval 1 "
                     "--suppress_threshhold 4098 --suppress_timecount 60");
        return ret;
    } else {
        // 恢复日志抑制条件
        ret = system("/usr/local/bin/stlmbox --logsuppressparamset --suppress_level 0 --suppress_interval 1 "
                     "--suppress_threshhold 500 --suppress_timecount 3600");
        return ret;
    }
    return GMERR_OK;
}

int compare_file_content(char *expect_file_path, char *actual_file_path, int lenth = 3)
{
    int ret = 0;
    char *expect_value = NULL;
    ret = readJanssonFile(expect_file_path, &expect_value);
    COMPARE_NE((char *)NULL, expect_value);

    char *actual_value = NULL;
    ret = readJanssonFile(actual_file_path, &actual_value);
    COMPARE_NE((char *)NULL, (char *)actual_value);

    ret = strncmp(expect_value, actual_value, lenth);
    if (ret != 0) {
        printf("[Error file: %s, line: %d]\n", __FILE__, __LINE__);
        printf("Value of: actual_value : \n%s\n", actual_value);
        printf("Expected: expect_value : \n%s\n", expect_value);
    };

    free(expect_value);
    free(actual_value);
    return ret;
}

// 定制化校验日志
int checkLog(char *ecpectLog, int expectNum, int lenth = 3)
{
    int ret = 0;
    char string[100];
    FILE *fp;
    char *actual = (char *)"temp.log";
    char *expect = (char *)"expect.log";

    system("touch expect.log");
    fp = fopen("expect.log", "r+");
    if (fp == NULL)  //判断如果文件指针为空
    {
        printf("File cannot open!\n");
        return -1;
    }
    fprintf(fp, "%d", expectNum);
    fclose(fp);
    memset(string, 0, 100);
    if (g_runMode == MODE_EULER) {
        sprintf(string, "cat log/run/rgmserver/* |grep \"%s\" |wc -l > temp.log", ecpectLog);
        ret = system(string);
        EXPECT_EQ(GMERR_OK, ret);
        ret = compare_file_content(expect, actual, lenth);
        system("cat /dev/null > log/run/rgmserver/rgmserver.1.log");
    } else if (g_runMode == MODE_DOPRA) {
        sprintf(string, "cat /var/log/hlog/info/hloglog.csv |grep \"%s\" |wc -l > temp.log", ecpectLog);
        ret = system(string);
        EXPECT_EQ(GMERR_OK, ret);
        // ret = compare_file_content(expect, actual);
        // system("rm -rf /opt/vrpv8/var/log/stlm/info/stlmlog.csv");
    }

    system("rm -rf expect.log");
    return ret;
}
int findLog(char *ecpectLog)
{
    int ret = 0;
    char string[100];
    FILE *fp;
    char *actual = (char *)"temp.log";
    char *expect = (char *)"expect.log";

    memset(string, 0, 100);
    if (g_runMode == MODE_EULER) {
        sprintf(string, "cat log/run/rgmserver/* |grep \"%s\" > temp.log", ecpectLog);
        ret = system(string);
        EXPECT_EQ(GMERR_OK, ret);
        char *actual_value = NULL;
        ret = readJanssonFile(actual, &actual_value);
        COMPARE_NE((char *)NULL, actual_value);
        char *ter;
        ter = strstr(actual_value, ecpectLog);
        free(actual_value);
        if (ter != NULL) {
            printf("find %s keywords:%d\n", ecpectLog, ret);
            return 0;
        } else {
            return 666;  //表示没有找到日志内容
        }
        // system("cat /dev/null > log/run/rgmserver/rgmserver.1.log");
    } else if (g_runMode == MODE_DOPRA) {
        sprintf(string, "cat /var/log/hlog/info/hloglog.csv |grep \"%s\" |wc -l > temp.log", ecpectLog);
        ret = system(string);
        EXPECT_EQ(GMERR_OK, ret);
        char *actual_value = NULL;
        ret = readJanssonFile(actual, &actual_value);
        COMPARE_NE((char *)NULL, actual_value);
        char *ter;
        ter = strstr(actual_value, ecpectLog);
        free(actual_value);
        if (ter != NULL) {
            printf("find %s keywords:%d\n", ecpectLog, ret);
            return 0;
        } else {
            return 666;  //表示失败
        }
    }
    return NULL;
}
void clearlog()
{
    if (g_runMode == MODE_EULER) {
        system("cat /dev/null > log/run/rgmserver/rgmserver.1.log");
    } else {
        system("rm -rf /var/log/hlog/info/hloglog.csv");
    }
}
int checkPartition(GmcStmtT *stmt, char *labelName, uint8_t partitionStart, uint8_t partitionEnd)
{
    int ret = 0;
    bool isAbnormal = false;
    for (uint8_t i = partitionStart; i < partitionEnd; i++) {
        ret = GmcBeginCheck(stmt, labelName, i);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcEndCheck(stmt, labelName, i, isAbnormal);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return ret;
}
void TestGmcSetNodePropertyByName_PK(GmcNodeT *root, int i)
{
    int ret = 0;
    int64_t f0_value = i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
}

void TestGmcSetNodePropertyByName_R(
    GmcNodeT *root, int i, bool bool_value, char *f14_value, bool isPartition = true, bool isBitmap = true)
{
    int ret = 0;
    uint64_t f1_value = i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    int32_t f2_value = i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t f3_value = i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    int16_t f4_value = i & 0x7FFF;
    ret = GmcNodeSetPropertyByName(root, (char *)"F4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint16_t f5_value = i & 0xFFFF;
    ret = GmcNodeSetPropertyByName(root, (char *)"F5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);
    int8_t f6_value = i & 0x7F;
    ret = GmcNodeSetPropertyByName(root, (char *)"F6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint8_t f7_value = i & 0xFF;
    ret = GmcNodeSetPropertyByName(root, (char *)"F7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(root, (char *)"F8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);
    float f9_value = i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);
    double f10_value = i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t f11_value = i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    char f12_value = 'a' + (i & 0x1A);
    ret = GmcNodeSetPropertyByName(root, (char *)"F12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);
    unsigned char f13_value = 'A' + (i & 0x1A);
    ret = GmcNodeSetPropertyByName(root, (char *)"F13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(root, (char *)"F14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(root, (char *)"F15", GMC_DATATYPE_BYTES, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(root, (char *)"F16", GMC_DATATYPE_FIXED, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);
    uint8_t f17_value = i & 0xF;
    ret = GmcNodeSetPropertyByName(root, (char *)"F17", GMC_DATATYPE_BITFIELD8, &f17_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint16_t f18_value = i & 0xF;
    ret = GmcNodeSetPropertyByName(root, (char *)"F18", GMC_DATATYPE_BITFIELD16, &f18_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t f19_value = i & 0xFF;
    ret = GmcNodeSetPropertyByName(root, (char *)"F19", GMC_DATATYPE_BITFIELD32, &f19_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t f20_value = i & 0xFFFF;
    ret = GmcNodeSetPropertyByName(root, (char *)"F20", GMC_DATATYPE_BITFIELD64, &f20_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    if (isPartition) {
        uint8_t partition = i % 16;
        ret = GmcNodeSetPropertyByName(root, (char *)"F21", GMC_DATATYPE_PARTITION, &partition, sizeof(uint8_t));
        ASSERT_EQ(GMERR_OK, ret);
    }
    if (isBitmap) {
        GmcBitMapT bitMap = {0, 127, NULL};  // 两个int类型，存储起始位共8个字节，一个字节存储结束符
        uint8_t bits[128 / 8];
        memset(bits, 0, 128 / 8);
        bits[128 / 8 - 1] = '\0';
        bitMap.bits = bits;
        ret = GmcNodeSetPropertyByName(root, (char *)"F22", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
        ASSERT_EQ(GMERR_OK, ret);
    }
}

void TestGmcSetNodePropertyByName_P(GmcNodeT *stmt, int i, bool bool_value, char *f14_value, bool isBitmap = true)
{
    int ret = 0;
    int64_t f0_value = i;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"P0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t f1_value = i;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"P1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    int32_t f2_value = i;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"P2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t f3_value = i;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"P3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    int16_t f4_value = i & 0x7FFF;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"P4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint16_t f5_value = i & 0xFFFF;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"P5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);
    int8_t f6_value = i & 0x7F;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"P6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint8_t f7_value = i & 0xFF;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"P7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"P8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);
    float f9_value = i;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"P9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);
    double f10_value = i;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"P10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t f11_value = i;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"P11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    char f12_value = 'a' + (i & 0x1A);
    ret = GmcNodeSetPropertyByName(stmt, (char *)"P12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);
    unsigned char f13_value = 'A' + (i & 0x1A);
    ret = GmcNodeSetPropertyByName(stmt, (char *)"P13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(stmt, (char *)"P14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(stmt, (char *)"P15", GMC_DATATYPE_BYTES, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(stmt, (char *)"P16", GMC_DATATYPE_FIXED, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);
    uint8_t f17_value = i & 0xF;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"P17", GMC_DATATYPE_BITFIELD8, &f17_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint16_t f18_value = i & 0xF;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"P18", GMC_DATATYPE_BITFIELD16, &f18_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t f19_value = i & 0xFF;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"P19", GMC_DATATYPE_BITFIELD32, &f19_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t f20_value = i & 0xFFFF;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"P20", GMC_DATATYPE_BITFIELD64, &f20_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    if (isBitmap) {
        GmcBitMapT bitMap = {0, 127, NULL};  // 两个int类型，存储起始位共8个字节，一个字节存储结束符
        uint8_t bits[128 / 8];
        memset(bits, 0, 128 / 8);
        bits[128 / 8 - 1] = '\0';
        bitMap.bits = bits;
        ret = GmcNodeSetPropertyByName(stmt, (char *)"P22", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
        ASSERT_EQ(GMERR_OK, ret);
    }
}

void TestGmcSetNodePropertyByName_A(GmcNodeT *stmt, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    int64_t f0_value = i;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"A0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t f1_value = i;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"A1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    int32_t f2_value = i;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"A2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t f3_value = i;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"A3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    int16_t f4_value = i & 0x7FFF;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"A4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint16_t f5_value = i & 0xFFFF;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"A5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);
    int8_t f6_value = i & 0x7F;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"A6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint8_t f7_value = i & 0xFF;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"A7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"A8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);
    float f9_value = i;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"A9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);
    double f10_value = i;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"A10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t f11_value = i;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"A11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    char f12_value = 'a' + (i & 0x1A);
    ret = GmcNodeSetPropertyByName(stmt, (char *)"A12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);
    unsigned char f13_value = 'A' + (i & 0x1A);
    ret = GmcNodeSetPropertyByName(stmt, (char *)"A13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(stmt, (char *)"A14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(stmt, (char *)"A15", GMC_DATATYPE_BYTES, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(stmt, (char *)"A16", GMC_DATATYPE_FIXED, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);
}

void TestGmcSetNodePropertyByName_V(GmcNodeT *stmt, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    int64_t f0_value = i;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"V0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t f1_value = i;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"V1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    int32_t f2_value = i;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"V2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t f3_value = i;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"V3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    int16_t f4_value = i & 0x7FFF;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"V4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint16_t f5_value = i & 0xFFFF;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"V5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);
    int8_t f6_value = i & 0x7F;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"V6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint8_t f7_value = i & 0xFF;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"V7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"V8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);
    float f9_value = i;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"V9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);
    double f10_value = i;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"V10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t f11_value = i;
    ret = GmcNodeSetPropertyByName(stmt, (char *)"V11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    char f12_value = 'a' + (i & 0x1A);
    ret = GmcNodeSetPropertyByName(stmt, (char *)"V12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);
    unsigned char f13_value = 'A' + (i & 0x1A);
    ret = GmcNodeSetPropertyByName(stmt, (char *)"V13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(stmt, (char *)"V14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(stmt, (char *)"V15", GMC_DATATYPE_BYTES, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(stmt, (char *)"V16", GMC_DATATYPE_FIXED, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);
}

// tree表写数据
void subTreeWrite(GmcStmtT *stmt, char *labelName, int recordCount, int index, bool bool_value, char *f14_value,
    int array_num, int vector_num, bool isSub = false, bool isPartition = true, bool isBitmap = true)
{
    int32_t ret = 0;
    int affectRows;
    GmcNodeT *root, *T1, *T2, *T3;

    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < recordCount; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        // 插入顶点
        TestGmcSetNodePropertyByName_PK(root, i);
        TestGmcSetNodePropertyByName_R(root, i, bool_value, f14_value, isPartition, isBitmap);

        TestGmcSetNodePropertyByName_P(T1, i, bool_value, f14_value, isBitmap);
        // 插入array节点
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcSetNodePropertyByName_A(T2, i, bool_value, f14_value);
            ret = GmcNodeGetNextElement(T2, &T2);
            EXPECT_EQ(GMERR_OK, ret);
        }
        // 插入vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(T3, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_V(T3, i, bool_value, f14_value);
        }
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1, affectRows);
    }
}

int createVertexLabel(char *file, GmcStmtT *stmt, char *config)
{
    int ret = 0;
    char *schema = NULL;
    readJanssonFile(file, &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(stmt, schema, config);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);
    return ret;
}

void testCreateLabelMS(GmcStmtT *stmt)
{
    ret = GmcCreateVertexLabel(stmt, sLabelSchemaJson, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    char *MS_VLabel_schema = NULL;
    readJanssonFile("schema_file/Vertex_ms.gmjson", &MS_VLabel_schema);
    ASSERT_NE((void *)NULL, MS_VLabel_schema);
    ret = GmcCreateVertexLabel(stmt, MS_VLabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    char *NoneKey_VLabel_schema = NULL;
    readJanssonFile("schema_file/NoneKey.gmjson", &NoneKey_VLabel_schema);
    ASSERT_NE((void *)NULL, NoneKey_VLabel_schema);
    ret = GmcCreateVertexLabel(stmt, NoneKey_VLabel_schema, V_config);
    EXPECT_EQ(GMERR_OK, ret);

    char *PriKey_VLabel_schema = NULL;
    readJanssonFile("schema_file/Primary.gmjson", &PriKey_VLabel_schema);
    ASSERT_NE((void *)NULL, PriKey_VLabel_schema);
    ret = GmcCreateVertexLabel(stmt, PriKey_VLabel_schema, V_config);
    EXPECT_EQ(GMERR_OK, ret);

    char *lhKey_VLabel_schema = NULL;
    readJanssonFile("schema_file/LocalHash.gmjson", &lhKey_VLabel_schema);
    ASSERT_NE((void *)NULL, lhKey_VLabel_schema);
    ret = GmcCreateVertexLabel(stmt, lhKey_VLabel_schema, V_config);
    EXPECT_EQ(GMERR_OK, ret);

    char *hcKey_VLabel_schema = NULL;
    readJanssonFile("schema_file/HashCluster.gmjson", &hcKey_VLabel_schema);
    ASSERT_NE((void *)NULL, hcKey_VLabel_schema);
    ret = GmcCreateVertexLabel(stmt, hcKey_VLabel_schema, V_config);
    EXPECT_EQ(GMERR_OK, ret);

    char *lcKey_VLabel_schema = NULL;
    readJanssonFile("schema_file/LocalKey.gmjson", &lcKey_VLabel_schema);
    ASSERT_NE((void *)NULL, lcKey_VLabel_schema);
    ret = GmcCreateVertexLabel(stmt, lcKey_VLabel_schema, V_config);
    EXPECT_EQ(GMERR_OK, ret);

    char *lpmKey_VLabel_schema = NULL;
    readJanssonFile("schema_file/Lpm.gmjson", &lpmKey_VLabel_schema);
    ASSERT_NE((void *)NULL, lpmKey_VLabel_schema);
    ret = GmcCreateVertexLabel(stmt, lpmKey_VLabel_schema, V_config);
    EXPECT_EQ(GMERR_OK, ret);

    char *lpm6Key_VLabel_schema = NULL;
    readJanssonFile("schema_file/Lpm6.gmjson", &lpm6Key_VLabel_schema);
    ASSERT_NE((void *)NULL, lpm6Key_VLabel_schema);
    ret = GmcCreateVertexLabel(stmt, lpm6Key_VLabel_schema, V_config);
    EXPECT_EQ(GMERR_OK, ret);

    char *SKey_VLabel_schema = NULL;
    readJanssonFile("schema_file/S_Type.gmjson", &SKey_VLabel_schema);
    ASSERT_NE((void *)NULL, SKey_VLabel_schema);
    ret = GmcCreateVertexLabel(stmt, SKey_VLabel_schema, V_config);
    EXPECT_EQ(GMERR_OK, ret);

    char *PriKey_TLabel_schema = NULL;
    readJanssonFile("schema_file/Tree.gmjson", &PriKey_TLabel_schema);
    ASSERT_NE((void *)NULL, PriKey_TLabel_schema);
    ret = GmcCreateVertexLabel(stmt, PriKey_TLabel_schema, V_config);
    EXPECT_EQ(GMERR_OK, ret);

    char *MKey_TLabel_schema = NULL;
    readJanssonFile("schema_file/super.gmjson", &MKey_TLabel_schema);
    ASSERT_NE((void *)NULL, MKey_TLabel_schema);
    ret = GmcCreateVertexLabel(stmt, MKey_TLabel_schema, V_config);
    EXPECT_EQ(GMERR_OK, ret);

    char *PriKey_ELabel_schema = NULL;
    char *PriKey_SrcLabel_schema = NULL;
    char *PriKey_DstLabel_schema = NULL;
    //注意先创建点再创建边
    char vertexLabel_config[] = "{\"max_record_num\":100000, \"isFastReadUncommitted\":0}";

    readJanssonFile("schema_file/Src.gmjson", &PriKey_SrcLabel_schema);
    ASSERT_NE((void *)NULL, PriKey_SrcLabel_schema);
    ret = GmcCreateVertexLabel(stmt, PriKey_SrcLabel_schema, vertexLabel_config);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schema_file/Dst.gmjson", &PriKey_DstLabel_schema);
    ASSERT_NE((void *)NULL, PriKey_DstLabel_schema);
    ret = GmcCreateVertexLabel(stmt, PriKey_DstLabel_schema, vertexLabel_config);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schema_file/Edge.gmjson", &PriKey_ELabel_schema);
    ASSERT_NE((void *)NULL, PriKey_ELabel_schema);
    ret = GmcCreateEdgeLabel(stmt, PriKey_ELabel_schema, vertexLabel_config);
    EXPECT_EQ(GMERR_OK, ret);
    free(MS_VLabel_schema);
    free(PriKey_VLabel_schema);
    free(lpmKey_VLabel_schema);
    free(NoneKey_VLabel_schema);
    free(lhKey_VLabel_schema);
    free(hcKey_VLabel_schema);
    free(lcKey_VLabel_schema);
    free(lpm6Key_VLabel_schema);
    free(SKey_VLabel_schema);
    free(PriKey_TLabel_schema);
    free(PriKey_ELabel_schema);
    free(PriKey_SrcLabel_schema);
    free(PriKey_DstLabel_schema);
    free(MKey_TLabel_schema);
}
void testDropLabelMS(GmcStmtT *stmt)
{
    ret = GmcDropVertexLabel(stmt, sLabelName);
    if ((GMERR_UNDEFINED_TABLE != ret) and (GMERR_OK != ret)) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(stmt, MS_VLabel_Name);
    if ((GMERR_UNDEFINED_TABLE != ret) and (GMERR_OK != ret)) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(stmt, NoneKey_Label_Name);
    if ((GMERR_UNDEFINED_TABLE != ret) and (GMERR_OK != ret)) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(stmt, PriKey_Label_Name);
    if ((GMERR_UNDEFINED_TABLE != ret) and (GMERR_OK != ret)) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(stmt, lsKey_Label_Name);
    if ((GMERR_UNDEFINED_TABLE != ret) and (GMERR_OK != ret)) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(stmt, hcKey_Label_Name);
    if ((GMERR_UNDEFINED_TABLE != ret) and (GMERR_OK != ret)) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(stmt, lcKey_Label_Name);
    if ((GMERR_UNDEFINED_TABLE != ret) and (GMERR_OK != ret)) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(stmt, lpmKey_Label_Name);
    if ((GMERR_UNDEFINED_TABLE != ret) and (GMERR_OK != ret)) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(stmt, lpm6Key_Label_Name);
    if ((GMERR_UNDEFINED_TABLE != ret) and (GMERR_OK != ret)) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(stmt, SKey_Label_Name);
    if ((GMERR_UNDEFINED_TABLE != ret) and (GMERR_OK != ret)) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(stmt, T_PriKey_Label_Name);
    if ((GMERR_UNDEFINED_TABLE != ret) and (GMERR_OK != ret)) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcDropEdgeLabel(stmt, PriKey_ELabel_Name);
    if ((GMERR_UNDEFINED_TABLE != ret) and (GMERR_OK != ret)) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(stmt, PriKey_SrcLabel_Name);
    if ((GMERR_UNDEFINED_TABLE != ret) and (GMERR_OK != ret)) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(stmt, PriKey_DstLabel_Name);
    if ((GMERR_UNDEFINED_TABLE != ret) and (GMERR_OK != ret)) {
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(stmt, MPriKey_Label_Name);
    if ((GMERR_UNDEFINED_TABLE != ret) and (GMERR_OK != ret)) {
        EXPECT_EQ(GMERR_OK, ret);
    }
}

void testInsertVertex(GmcStmtT *stmt, uint32_t times, uint32_t initValue, const char *propname)
{
    uint32_t i = 0;
    uint32_t value = 0;

    ASSERT_NE((void *)NULL, stmt);

    // insert vertex
    for (i = 0; i < times; i++) {
        value = initValue + i;
        ret = GmcSetVertexProperty(stmt, propname, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);
    }
}
void testInsertVertex_long(GmcStmtT *stmt, uint32_t times, uint32_t initValue, uint32_t prop_value)
{
    uint32_t i = 0;
    uint32_t value = 0;
    uint32_t normal_value = prop_value;
    ASSERT_NE((void *)NULL, stmt);

    // insert vertex
    for (i = 0; i < times; i++) {
        value = initValue + i;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &normal_value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &normal_value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT32, &normal_value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_UINT32, &normal_value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);
    }
}
void testDeleteVertex(GmcStmtT *stmt, const char *KeyName, uint32_t times, void *VLabel, uint32_t initValue)
{
    uint32_t i = 0;
    uint32_t value = 0;

    ASSERT_NE((void *)NULL, stmt);
    ASSERT_NE((void *)NULL, KeyName);
    ASSERT_NE((void *)NULL, VLabel);

    // delete vertex
    for (i = 0; i < times; i++) {
        value = initValue + i;
        // 设置Filter
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // 删除
        ret = GmcSetIndexKeyName(stmt, KeyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

void testInsertVertex_Super(GmcStmtT *stmt, const char *SuperFiledName, uint32_t times, uint32_t initValue)
{
    uint32_t i = 0;
    uint32_t value = 0;

    ASSERT_NE((void *)NULL, stmt);
    ASSERT_NE((void *)NULL, SuperFiledName);

    // insert vertex
    for (i = 0; i < times; i++) {
        //写主键和hash索引数据
        value = initValue + i;
        ret = GmcSetVertexProperty(stmt, "PK0", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(GMERR_OK,ret);
        ret = GmcSetVertexProperty(stmt, "PK1", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "Hash0", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "Hash1", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        //写fixed数据
        // superfileds的size = 256 * 16（属性数量）
        uint32_t SuperSize = 256 * 16;
        char *SuperValue = (char *)malloc(SuperSize);
        memset(SuperValue, 'A', SuperSize);

        ret = GmcSetSuperfieldByName(stmt, SuperFiledName, SuperValue, SuperSize);
        EXPECT_EQ(GMERR_OK, ret);
        free(SuperValue);

        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);

        ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);
    }
}
void testDeleteVertex_Super(GmcStmtT *stmt, const char *KeyName, uint32_t times, void *VLabel, uint32_t initValue)
{
    uint32_t i = 0;
    uint32_t value = 0;

    ASSERT_NE((void *)NULL, stmt);
    ASSERT_NE((void *)NULL, KeyName);

    // delete vertex
    for (i = 0; i < times; i++) {
        value = initValue + i;
        // 设置Filter
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // 删除
        ret = GmcSetIndexKeyName(stmt, KeyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret != 0) {
            testGmcGetLastError(NULL);
        } else {
            EXPECT_EQ(GMERR_OK, ret);
        }
        // ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        // EXPECT_EQ(GMERR_OK, ret);
    }
}

void testUpdateVertex(GmcStmtT *stmt, const char *KeyName, uint32_t times)
{
    uint32_t i = 0;
    uint32_t value = 0;

    ASSERT_NE((void *)NULL, stmt);
    ASSERT_NE((void *)NULL, KeyName);
    // update vertex
    for (i = 0; i < times; i++) {
        value = i;
        // 设置Filter
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        // update
        ret = GmcSetIndexKeyName(stmt, KeyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);

        // ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        // EXPECT_EQ(GMERR_OK, ret);
    }
}
#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];
void getSTORAGE_MEMDATA_STAT()
{
    char const *view_name = "V\\$STORAGE_MEMDATA_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -u %s -s %s -p %s  -q %s", g_toolPath, g_userName, g_connServer,
        g_passwd, view_name);
    printf("%s\n", g_command);
    system(g_command);
    memset(g_command, 0, sizeof(g_command));
}
void getSTORAGE_HEAP_STAT_PURE()
{
    char const *view_name = "V\\$STORAGE_HEAP_STAT";
    // snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -u %s -s %s -p %s  -q %s -f %s", g_toolPath, g_userName,
    // g_connServer, g_passwd,    view_name,Select_label);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -u %s -s %s -p %s  -q %s ", g_toolPath, g_userName, g_connServer,
        g_passwd, view_name);
    printf("%s\n", g_command);
    system(g_command);
    memset(g_command, 0, sizeof(g_command));
}
void getSTORAGE_HEAP_STAT(const char *Select_label)
{
    char const *view_name = "V\\$STORAGE_HEAP_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -u %s -s %s -p %s  -q %s -f %s", g_toolPath, g_userName,
        g_connServer, g_passwd, view_name, Select_label);
    // snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -u %s -s %s -p %s  -q %s ", g_toolPath, g_userName, g_connServer,
    // g_passwd,    view_name);
    printf("%s\n", g_command);
    system(g_command);
    memset(g_command, 0, sizeof(g_command));
}
void getSERVER_MEMORY_OVERHEAD()
{
    char const *view_name = "V\\$SERVER_MEMORY_OVERHEAD";
    // snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -u %s -s %s -p %s  -q %s -f %s", g_toolPath, g_userName,
    // g_connServer, g_passwd,    view_name,Select_label);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -u %s -s %s -p %s  -q %s ", g_toolPath, g_userName, g_connServer,
        g_passwd, view_name);
    printf("%s\n", g_command);
    system(g_command);
    memset(g_command, 0, sizeof(g_command));
}
void getCOM_DYN_CTX_SELECT(const char *Select_label)
{
    char const *view_name = "V\\$COM_DYN_CTX";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -u %s -s %s -p %s  -q %s -f %s", g_toolPath, g_userName,
        g_connServer, g_passwd, view_name, Select_label);
    // snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -u %s -s %s -p %s  -q %s ", g_toolPath, g_userName, g_connServer,
    // g_passwd,    view_name);
    printf("%s\n", g_command);
    system(g_command);
    memset(g_command, 0, sizeof(g_command));
}
void getCOM_DYN_CTX()
{
    char const *view_name = "V\\$COM_DYN_CTX";
    // snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -u %s -s %s -p %s  -q %s -f %s", g_toolPath, g_userName,
    // g_connServer, g_passwd,    view_name,Select_label);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -u %s -s %s -p %s  -q %s ", g_toolPath, g_userName, g_connServer,
        g_passwd, view_name);
    printf("%s\n", g_command);
    system(g_command);
    memset(g_command, 0, sizeof(g_command));
}
