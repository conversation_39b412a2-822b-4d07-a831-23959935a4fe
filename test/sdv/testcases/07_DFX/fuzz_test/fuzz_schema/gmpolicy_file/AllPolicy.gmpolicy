{"system_privilege_config": [{"users": [{"user": "root", "process": "python"}, {"user": "root", "process": "gmlog"}, {"user": "root", "process": "gmsysview"}, {"user": "root", "process": "gmexport"}, {"user": "root", "process": "gmimport"}, {"user": "root", "process": "fuzz_test"}, {"user": "root", "process": "tree_label_inse"}, {"user": "root", "process": "connTest"}, {"user": "root", "process": "yang<PERSON><PERSON><PERSON>"}], "privs": [{"obj_type": "VERTEX_LABEL", "privs_type": ["CREATE", "DROP", "GET", "TRUNCATE", "INSERT_ANY", "UPDATE_ANY", "DELETE_ANY", "MERGE_ANY", "REPLACE_ANY", "SELECT_ANY"]}, {"obj_type": "EDGE_LABEL", "privs_type": ["CREATE", "ALTER", "DROP", "GET"]}, {"obj_type": "KV_TABLE", "privs_type": ["CREATE", "DROP", "GET", "TRUNCATE", "DELETE_ANY", "REPLACE_ANY", "SELECT_ANY"]}, {"obj_type": "NAMESPACE", "privs_type": ["CREATE", "DROP", "USE"]}, {"obj_type": "TABLESPACE", "privs_type": ["CREATE", "DROP", "BIND"]}, {"obj_type": "RESOURCE", "privs_type": ["CREATE", "DROP", "BIND", "UNBIND", "GET"]}, {"obj_type": "BINARY_FILE", "privs_type": ["OPEN", "CLOSE"]}, {"obj_type": "DATALOG_UDF", "privs_type": ["CREATE", "DROP", "INVOKE_ANY"]}]}]}