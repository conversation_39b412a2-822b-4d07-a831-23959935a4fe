/* ****************************************************************************
 Description  : 索引冲突视图特性交互测试
001.不存在localhash，存在hashcluster,local,lpm4时查视图
002.不存在localhash，存在hashcluster,local,lpm6时查视图
003.存在pk,localhash,hashcluster,local和lpm4索引进行视图的查询
004.启显示事务，进行插入数据1.事务提交前进行视图查询;2.事务提交commit后再查询视图
005.进行插入数据，开启显示事务，更新数据(更新主键、localhash唯一与非唯一)，1.事务提交前进行视图查询;2.事务提交commit后再查询视图
006.进行插入数据，开启显示事务，删除数据，1.事务提交前进行视图查询;2.事务提交commit后再查询视图
007.启显示事务，进行插入数据1.事务提交前进行视图查询;2.事务提交rollback后再查询视图
008.进行插入数据，开启显示事务，更新数据(更新主键、localhash唯一与非唯一)，1.事务提交前进行视图查询;2.事务提交rollback后再查询视图
009.进行插入数据，开启显示事务，删除数据，1.事务提交前进行视图查询;2.事务提交rollback后再查询视图
010.yang表中含pk，localhash唯一与非唯一索引时进行插入数据查询视图
011.yang表中含pk，localhash唯一与非唯一索引时进行插入数据更新数据查询视图
012.yang表中含pk，localhash唯一与非唯一索引时进行插入数据1.删除一部分数据查询视图，2.删除所有数据查询视图

 Author       : wuxiaochun wx753022
 Modification :
 Date         : 2021/06/04
**************************************************************************** */

#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include <sys/time.h>
#include <time.h>

#include "gtest/gtest.h"
#include "indexCollision.h"

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
char g_labelName1[128] = "tree_hex_default_value_test";
char g_pkName1[64] = "testPK1";
char g_localhashName1[64] = "localhash_unique_key1";
char g_localhashName2[64] = "localhash_key1";
char g_label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";
GmcTxConfigT g_config;

class indexCollisionMutualTest : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void indexCollisionMutualTest::SetUpTestCase()
{
    if (g_envType == 0) {
        system("sh $TEST_HOME/tools/stop.sh -f");
        system("sh $TEST_HOME/tools/start.sh -f");
    } else {
        system("sh $TEST_HOME/tools/start.sh");
    }
    int ret = 0;
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    ASSERT_EQ(GMERR_OK, ret);
}

void indexCollisionMutualTest::TearDownTestCase()
{
    int ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    testEnvClean();
}

void indexCollisionMutualTest::SetUp()
{
    int ret = 0;
    ret = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void indexCollisionMutualTest::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
}

// 001.不存在localhash，存在hashcluster,local,lpm4时查视图
TEST_F(indexCollisionMutualTest, DFX_014_003_001)
{
    int ret = 0;
    char *schema = NULL;

    char *labelName = (char *)"OP_T0";

    readJanssonFile("schemaFile/TreeModelLpm4.gmjson", &schema);
    ASSERT_NE((void *)NULL, schema);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret == GMERR_UNDEFINED_TABLE ? GMERR_OK : ret);
    ret = GmcCreateVertexLabel(g_stmt, schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //查视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s -f LABEL_NAME=%s", g_toolPath, g_connServer,
             g_viewname, labelName);
    ret = testIndexCollisionLpm(g_command, 0, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 002.不存在localhash，存在hashcluster,local,lpm6时查视图
TEST_F(indexCollisionMutualTest, DFX_014_003_002)
{
    int ret = 0;
    char *schema = NULL;

    char *labelName = (char *)"OP_T0";

    readJanssonFile("schemaFile/TreeModelLpm6.gmjson", &schema);
    ASSERT_NE((void *)NULL, schema);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    ret = GmcCreateVertexLabel(g_stmt, schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    //查视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s -f LABEL_NAME=%s", g_toolPath, g_connServer,
             g_viewname, labelName);
    ret = testIndexCollisionLpm(g_command, 0, false);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
}
// 003.存在pk,localhash,hashcluster,local和lpm4索引进行视图的查询
TEST_F(indexCollisionMutualTest, DFX_014_003_003)
{
    int ret = 0;
    char *schema = NULL;

    char *labelName = (char *)"OP_T0";

    readJanssonFile("schemaFile/TreeModelSchema.gmjson", &schema);
    ASSERT_NE((void *)NULL, schema);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    ret = GmcCreateVertexLabel(g_stmt, schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    //查视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s -f LABEL_NAME=%s", g_toolPath, g_connServer,
             g_viewname, labelName);
    ret = testIndexCollisionLpm2(g_command, 0, false);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 004.启显示事务，进行插入数据1.事务提交前进行视图查询;2.事务提交commit后再查询视图
TEST_F(indexCollisionMutualTest, DFX_014_003_004)
{
    int ret = 0;
    char *schema = NULL;

    bool bool_value = false;
    char *str_value = (char *)"vertextest";
    int start_num = 0;
    int end_num = 1000;
    g_config.transMode = GMC_TRANS_USED_IN_CS;
    g_config.type = GMC_TX_ISOLATION_COMMITTED;
    g_config.readOnly = false;
    uint8_t bit8_value_default = 0x1f;
    uint16_t bit16_value_default = 0x3ff;
    uint32_t bit32_value_default = 0x1ffff;
    uint64_t bit64_value_default = 0x7ffffffff;
    uint8_t fixed_value_default[9] = {0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff};
    uint8_t bytes_value_default[10] = {0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff};

    readJanssonFile("schemaFile/TreeModel_fixedBytesBits_hex_default_value.gmjson", &schema);
    ASSERT_NE((void *)NULL, schema);

    ret = GmcDropVertexLabel(g_stmt, g_labelName1);
    ret = GmcCreateVertexLabel(g_stmt, schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);
    ret = GmcTransStart(g_conn, &g_config);
    ASSERT_EQ(GMERR_OK, ret);
    //插入
    TestGmcInsertVertex_Tree(g_stmt, g_labelName1, bool_value, str_value, start_num, end_num, 3, 3);
    //查视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s -f LABEL_NAME=%s", g_toolPath, g_connServer,
        g_viewname, g_labelName1);
    ret = testIndexCollision(g_command, (end_num - start_num), (end_num - start_num), (end_num - start_num), false);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcTransCommit(g_conn);
    ASSERT_EQ(GMERR_OK, ret);
    //事务提交再次查视图
    ret = testIndexCollision(g_command, (end_num - start_num), (end_num - start_num), (end_num - start_num), false);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_labelName1);
    ASSERT_EQ(GMERR_OK, ret);
}

// 005.进行插入数据，开启显示事务，更新数据，1.事务提交前进行视图查询;2.事务提交commit后再查询视图
TEST_F(indexCollisionMutualTest, DFX_014_003_005)
{
    int ret = 0;
    char *schema = NULL;

    bool bool_value = false;
    char *str_value = (char *)"vertextest";
    int start_num = 0;
    int end_num = 1000;
    g_config.transMode = GMC_TRANS_USED_IN_CS;
    g_config.type = GMC_TX_ISOLATION_COMMITTED;
    g_config.readOnly = false;
    uint8_t bit8_value_default = 0x1f;
    uint16_t bit16_value_default = 0x3ff;
    uint32_t bit32_value_default = 0x1ffff;
    uint64_t bit64_value_default = 0x7ffffffff;
    uint8_t fixed_value_default[9] = {0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff};
    uint8_t bytes_value_default[10] = {0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff};

    readJanssonFile("schemaFile/TreeModel_fixedBytesBits_hex_default_value.gmjson", &schema);
    ASSERT_NE((void *)NULL, schema);

    ret = GmcDropVertexLabel(g_stmt, g_labelName1);
    ret = GmcCreateVertexLabel(g_stmt, schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);
    //插入
    TestGmcInsertVertex_Tree(g_stmt, g_labelName1, bool_value, str_value, start_num, end_num, 3, 3);
    //开启事务
    ret = GmcTransStart(g_conn, &g_config);
    ASSERT_EQ(GMERR_OK, ret);
    GmcNodeT *root = NULL, *T1 = NULL, *T2 = NULL, *T3 = NULL;

    //更新：主键，localhash索引唯一与非唯一都更新
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName1, GMC_OPERATION_UPDATE);
        ASSERT_EQ(GMERR_OK, ret);
        TestGetRootAndChild_V(g_stmt, &root, &T1, &T2, &T3);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
        ASSERT_EQ(GMERR_OK, ret);
        uint64_t f1_value = i + 1000;
        // TestGmcNodeSetPropertyByName_PK(root,f1_value);
        TestGmcNodeSetPropertyByName_LocalhashUnique(root, f1_value);
        uint32_t f3_value = 1000;
        ret = GmcNodeSetPropertyByName(root, (char *)"F3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_pkName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);
    }
    //查视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s -f LABEL_NAME=%s", g_toolPath, g_connServer,
        g_viewname, g_labelName1);
    ret = testIndexCollision(
        g_command, (end_num - start_num), (end_num - start_num) * 2, (end_num - start_num) * 2, false);
    ASSERT_EQ(GMERR_OK, ret);
    //事务提交
    ret = GmcTransCommit(g_conn);
    ASSERT_EQ(GMERR_OK, ret);
    //事务提交再次查视图
    ret = testIndexCollision(
        g_command, (end_num - start_num), (end_num - start_num) * 2, (end_num - start_num) * 2, false);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_labelName1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 006.进行插入数据，开启显示事务，删除数据，1.事务提交前进行视图查询;2.事务提交commit后再查询视图
TEST_F(indexCollisionMutualTest, DFX_014_003_006)
{
    int ret = 0;
    char *schema = NULL;

    bool bool_value = false;
    char *str_value = (char *)"vertextest";
    int start_num = 0;
    int end_num = 1000;
    g_config.transMode = GMC_TRANS_USED_IN_CS;
    g_config.type = GMC_TX_ISOLATION_COMMITTED;
    g_config.readOnly = false;
    uint8_t bit8_value_default = 0x1f;
    uint16_t bit16_value_default = 0x3ff;
    uint32_t bit32_value_default = 0x1ffff;
    uint64_t bit64_value_default = 0x7ffffffff;
    uint8_t fixed_value_default[9] = {0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff};
    uint8_t bytes_value_default[10] = {0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff};

    readJanssonFile("schemaFile/TreeModel_fixedBytesBits_hex_default_value.gmjson", &schema);
    ASSERT_NE((void *)NULL, schema);

    ret = GmcDropVertexLabel(g_stmt, g_labelName1);
    ret = GmcCreateVertexLabel(g_stmt, schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);
    //插入
    TestGmcInsertVertex_Tree(g_stmt, g_labelName1, bool_value, str_value, start_num, end_num, 3, 3);
    //开启事务
    ret = GmcTransStart(g_conn, &g_config);
    ASSERT_EQ(GMERR_OK, ret);
    //删除数据
    TestPkDelete_Tree(g_stmt, start_num, end_num, g_labelName1, g_pkName1);
    //查视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s -f LABEL_NAME=%s", g_toolPath, g_connServer,
        g_viewname, g_labelName1);
    ret = testIndexCollision(g_command, (end_num - start_num), (end_num - start_num), (end_num - start_num), false);
    ASSERT_EQ(GMERR_OK, ret);
    //事务提交
    ret = GmcTransCommit(g_conn);
    ASSERT_EQ(GMERR_OK, ret);
    //事务提交再次查视图
    ret = testIndexCollision(g_command, (end_num - start_num), (end_num - start_num), (end_num - start_num), false);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_labelName1);
    EXPECT_EQ(GMERR_OK, ret);
}
// 007.启显示事务，进行插入数据1.事务提交前进行视图查询;2.事务提交rollback后再查询视图
TEST_F(indexCollisionMutualTest, DFX_014_003_007)
{
    int ret = 0;
    char *schema = NULL;

    bool bool_value = false;
    char *str_value = (char *)"vertextest";
    int start_num = 0;
    int end_num = 1000;
    g_config.transMode = GMC_TRANS_USED_IN_CS;
    g_config.type = GMC_TX_ISOLATION_COMMITTED;
    g_config.readOnly = false;
    uint8_t bit8_value_default = 0x1f;
    uint16_t bit16_value_default = 0x3ff;
    uint32_t bit32_value_default = 0x1ffff;
    uint64_t bit64_value_default = 0x7ffffffff;
    uint8_t fixed_value_default[9] = {0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff};
    uint8_t bytes_value_default[10] = {0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff};

    readJanssonFile("schemaFile/TreeModel_fixedBytesBits_hex_default_value.gmjson", &schema);
    ASSERT_NE((void *)NULL, schema);

    ret = GmcDropVertexLabel(g_stmt, g_labelName1);
    ret = GmcCreateVertexLabel(g_stmt, schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);
    ret = GmcTransStart(g_conn, &g_config);
    ASSERT_EQ(GMERR_OK, ret);
    // 插入
    TestGmcInsertVertex_Tree(g_stmt, g_labelName1, bool_value, str_value, start_num, end_num, 3, 3);
    // 查视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s -f LABEL_NAME=%s", g_toolPath, g_connServer,
        g_viewname, g_labelName1);
    ret = testIndexCollision(g_command, (end_num - start_num), (end_num - start_num), (end_num - start_num), false);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcTransRollBack(g_conn);
    ASSERT_EQ(GMERR_OK, ret);
    // 事务提交再次查视图
    ret = testIndexCollision(g_command, (end_num - start_num), (end_num - start_num), (end_num - start_num), false);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_labelName1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 008.进行插入数据，开启显示事务，更新数据，1.事务提交前进行视图查询;2.事务提交rollback后再查询视图
TEST_F(indexCollisionMutualTest, DFX_014_003_008)
{
    int ret = 0;
    char *schema = NULL;

    bool bool_value = false;
    char *str_value = (char *)"vertextest";
    int start_num = 0;
    int end_num = 1000;
    g_config.transMode = GMC_TRANS_USED_IN_CS;
    g_config.type = GMC_TX_ISOLATION_COMMITTED;
    g_config.readOnly = false;
    uint8_t bit8_value_default = 0x1f;
    uint16_t bit16_value_default = 0x3ff;
    uint32_t bit32_value_default = 0x1ffff;
    uint64_t bit64_value_default = 0x7ffffffff;
    uint8_t fixed_value_default[9] = {0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff};
    uint8_t bytes_value_default[10] = {0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff};

    readJanssonFile("schemaFile/TreeModel_fixedBytesBits_hex_default_value.gmjson", &schema);
    ASSERT_NE((void *)NULL, schema);

    ret = GmcDropVertexLabel(g_stmt, g_labelName1);
    ret = GmcCreateVertexLabel(g_stmt, schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);
    //插入
    TestGmcInsertVertex_Tree(g_stmt, g_labelName1, bool_value, str_value, start_num, end_num, 3, 3);
    //开启事务
    ret = GmcTransStart(g_conn, &g_config);
    ASSERT_EQ(GMERR_OK, ret);
    GmcNodeT *root = NULL, *T1 = NULL, *T2 = NULL, *T3 = NULL;

    //更新：主键，localhash索引全部更新
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName1, GMC_OPERATION_UPDATE);
        ASSERT_EQ(GMERR_OK, ret);
        TestGetRootAndChild_V(g_stmt, &root, &T1, &T2, &T3);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
        ASSERT_EQ(GMERR_OK, ret);
        uint64_t f1_value = i + 1000;
        // TestGmcNodeSetPropertyByName_PK(root,f1_value);
        TestGmcNodeSetPropertyByName_LocalhashUnique(root, f1_value);
        uint32_t f3_value = 1000;
        ret = GmcNodeSetPropertyByName(root, (char *)"F3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_pkName1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);
    }
    //查视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s -f LABEL_NAME=%s", g_toolPath, g_connServer,
        g_viewname, g_labelName1);
    ret = testIndexCollision(
        g_command, (end_num - start_num), (end_num - start_num) * 2, (end_num - start_num) * 2, false);
    ASSERT_EQ(GMERR_OK, ret);
    //事务提交
    ret = GmcTransRollBack(g_conn);
    ASSERT_EQ(GMERR_OK, ret);
    //事务提交再次查视图
    ret = testIndexCollision(
        g_command, (end_num - start_num), (end_num - start_num) * 3, (end_num - start_num) * 3, false);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_labelName1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 009.进行插入数据，开启显示事务，删除数据，1.事务提交前进行视图查询;2.事务提交rollback后再查询视图
TEST_F(indexCollisionMutualTest, DFX_014_003_009)
{
    int ret = 0;
    char *schema = NULL;

    bool bool_value = false;
    char *str_value = (char *)"vertextest";
    int start_num = 0;
    int end_num = 1000;
    g_config.transMode = GMC_TRANS_USED_IN_CS;
    g_config.type = GMC_TX_ISOLATION_COMMITTED;
    g_config.readOnly = false;
    uint8_t bit8_value_default = 0x1f;
    uint16_t bit16_value_default = 0x3ff;
    uint32_t bit32_value_default = 0x1ffff;
    uint64_t bit64_value_default = 0x7ffffffff;
    uint8_t fixed_value_default[9] = {0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff};
    uint8_t bytes_value_default[10] = {0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff};

    readJanssonFile("schemaFile/TreeModel_fixedBytesBits_hex_default_value.gmjson", &schema);
    ASSERT_NE((void *)NULL, schema);

    ret = GmcDropVertexLabel(g_stmt, g_labelName1);
    ret = GmcCreateVertexLabel(g_stmt, schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);
    //插入
    TestGmcInsertVertex_Tree(g_stmt, g_labelName1, bool_value, str_value, start_num, end_num, 3, 3, g_labelName1);
    //开启事务
    ret = GmcTransStart(g_conn, &g_config);
    ASSERT_EQ(GMERR_OK, ret);
    //删除数据
    TestPkDelete_Tree(g_stmt, start_num, end_num, g_labelName1, g_pkName1);
    //查视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s -f LABEL_NAME=%s", g_toolPath, g_connServer,
        g_viewname, g_labelName1);
    ret = testIndexCollision(g_command, (end_num - start_num), (end_num - start_num), (end_num - start_num), false);
    ASSERT_EQ(GMERR_OK, ret);
    //事务提交
    ret = GmcTransRollBack(g_conn);
    ASSERT_EQ(GMERR_OK, ret);
    //事务提交再次查视图
    ret = testIndexCollision(
        g_command, (end_num - start_num) * 2, (end_num - start_num) * 2, (end_num - start_num) * 2, false);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_labelName1);
    EXPECT_EQ(GMERR_OK, ret);
}

