/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: DlgSpeCon.h
 * Description: 表结构体
 * Author: youwanyong ywx1157510
 * Create: 2023-10-10
 */

#ifndef __STRUCTDATALOGTABLE_H__
#define __STRUCTDATALOGTABLE_H__

/*----------------------------------------表结构--------------------------------------------*/
#pragma pack(1)
typedef struct {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int8_t a;
    int16_t b;
    int32_t c;
    int64_t d;
    int8_t e[1];
    int8_t f[128];
    int8_t g[256];
    uint8_t a1;
    uint16_t b1;
    uint32_t c1;
    uint64_t d1;
    uint8_t e1 : 8;
    uint16_t f1 : 16;
    uint32_t g1 : 32;
    uint64_t a2 : 64;
    int32_t a3len;
    char *a3buf;
    uint16_t a4len;
    uint8_t *a4buf;
} NewALLTypeTableStruct;
#pragma pack(0)

#pragma pack(1)
typedef struct {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    uint8_t a;
    int64_t b;
    uint16_t c : 16;
} Uint216TypeTableStruct;
#pragma pack(0)

#endif   /* __STRUCTDATALOGTABLE_H__ */
