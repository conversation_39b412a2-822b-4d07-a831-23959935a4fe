/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */
#include "gm_udf.h"

typedef struct hprC1 {
    int32_t a;
    int32_t b;
} HprC1;

int32_t dtl_serialize_hpr_tbl_hprC1(void *tuple, GmcHprKeyT *key, GmcShmemPtrT *value, GmUdfShmCtxT *memCtx)
{
    HprC1 *origin = (HprC1 *)tuple;
    key->linearKey.index = origin->a;
    HprC1 *dest = (HprC1 *)GmUdfShmMemAlloc(memCtx, sizeof(HprC1), value);
    if (dest == NULL) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    dest->a = origin->a;
    dest->b = origin->b;
    return GMERR_OK;
}
