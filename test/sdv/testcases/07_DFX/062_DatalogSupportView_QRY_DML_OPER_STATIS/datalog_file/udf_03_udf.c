/*  版权所有 (c) 华为技术有限公司 2021-2022 */
#include "gm_udf.h"

#pragma pack(1)
typedef struct A {
    int32_t count;
    int32_t updateVersion;
    int32_t a;
    int32_t b;
} A;

typedef struct Func {
    int32_t count;
    int32_t a;
    int32_t b;
} Func;

#pragma pack(0)

// 写读表A
int32_t dtl_ext_func_ns1_wr_func(void *tuple, GmUdfCtxT *ctx)
{
    Func *b = (Func *)tuple;
    b->b = b->a + 1;

    // read input's input table
    GmUdfReaderT *reader = NULL;
    int32_t ret = GmUdfCreateCurrentReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }

    A *a = NULL;
    int32_t sum_a = 0, sum_b = 0, cnt = 0;

    while (ret = GmUdfGetNext(reader, (void **)&a), ret == GMERR_OK) {
        sum_a += a->a;
        sum_b += a->b;
        cnt++;
    }

    GmUdfDestroyReader(ctx, reader);

    // write input's input table
    GmUdfWriterT *writer = NULL;
    ret = GmUdfGetDeltaWriter(ctx, 0, &writer);
    if (ret != GMERR_OK) {
        return ret;
    }

    A a0 = {};
    a0.a = cnt ? (sum_a / cnt) : 0;
    a0.b = cnt ? (sum_b / cnt) : 0;
    a0.count = 1;
    a0.updateVersion = 1;
    ret = GmUdfAppend(writer, sizeof(A), &a0);
    if (ret != GMERR_OK) {
        return ret;
    }

    return GMERR_OK;
}
