/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */
#include "gm_udf.h"

typedef struct hprC2 {
    int32_t a;
    int32_t b;
} HprC2;

typedef struct {
    int32_t a;
    int32_t b;
} KeyHashFuncJoin;

int32_t dtl_serialize_hpr_tbl_hprC2(void *tuple, GmcHprKeyT *key, GmcShmemPtrT *value, GmUdfShmCtxT *memCtx)
{
    HprC2 *origin = (HprC2 *)tuple;
    GmcShmemPtrT keyShm;
    KeyHashFuncJoin *keyAddr =
        (KeyHashFuncJoin *)GmUdfShmMemAlloc(memCtx, sizeof(KeyHashFuncJoin) + sizeof(HprC2), &keyShm);
    keyAddr->a = origin->a;
    keyAddr->b = origin->b;
    key->hashKey.keyAddr = keyShm;
    key->hashKey.keyLen = sizeof(KeyHashFuncJoin);
    HprC2 *dest = (HprC2 *)((uint8_t *)keyAddr + sizeof(KeyHashFuncJoin));
    *value = keyShm;
    if (dest == NULL) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    dest->a = origin->a;
    dest->b = origin->b;
    return GMERR_OK;
}
