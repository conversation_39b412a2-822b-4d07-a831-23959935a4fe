/*  版权所有 (c) 华为技术有限公司 2021-2022 */
#include "gm_udf.h"

#pragma pack(1)
typedef struct A {
    int32_t count;
    int32_t updateVersion;
    int32_t a;
    int32_t b;
} A;

typedef struct Func {
    int32_t count;
    int32_t a;
    int32_t b;
} Func;

#pragma pack(0)

// function 无option
int32_t dtl_ext_func_ns1_func(void *tuple, GmUdfCtxT *ctx)
{
    // 用于实现b到c的计算
    Func *b = (Func *)tuple;
    b->b = b->a + 1;
    return GMERR_OK;  // 没有返回，编译阶段不会报错但是执行写数据之后会报错
}
