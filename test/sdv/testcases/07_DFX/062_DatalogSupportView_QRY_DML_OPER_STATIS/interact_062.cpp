/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2022. All rights reserved.
 Description  : dalalog支持QRY_DML_INFO视图统计
 Notes        : 001.批写订阅普通输出表，向输入表写数据，获取推送数据，查视图

                002.单写订阅pubsub资源表，向输入表写数据，获取推送数据，查视图

 History      :
 Author       : youwanyong ywx1157510
 Modification : 2022/10/22
**************************************************************************** */
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <atomic>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "QRY_DML_OPER_STATIS_interface.h"

class DatalogSupportView_QRY_DML_OPER_STATIS_interact : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);

        // 创建epoll监听线程
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret;
        ret = close_epoll_thread();
        EXPECT_EQ(0, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void DatalogSupportView_QRY_DML_OPER_STATIS_interact::SetUp()
{
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so

    int ret;
    // 创建连接
    g_conn = NULL;
    g_stmt = NULL;
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}
void DatalogSupportView_QRY_DML_OPER_STATIS_interact::TearDown()
{
    AW_CHECK_LOG_END();
    int ret;
    // 断开同步连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 001.批写订阅普通输出表，向输入表写数据，获取推送数据，查视图
TEST_F(DatalogSupportView_QRY_DML_OPER_STATIS_interact, DFX_062_Interact_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char nsName[128] = "interact01";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char labelName_in[] = "ns1.A";
    char labelName_mid[] = "ns1.rsc0";
    char labelName_out[] = "ns1.B";

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *subConn;
    GmcStmtT *subStmt;
    const char *subConnName = "testSub";
    ret = testSubConnect(&subConn, NULL, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_info = NULL;
    readJanssonFile((char *)"./schema_file/pubsub_interact_ns1.gmjson", &sub_info);
    EXPECT_NE((void *)NULL, sub_info);
    const char *subName = "testSub_ns1.B";
    GmcSubConfigT tmp_sub_info;
    tmp_sub_info.subsName = subName;
    tmp_sub_info.configJson = sub_info;
    SnUserDataWithFuncT *userData = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userData->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData->data, 0, sizeof(SnUserDataT));
    userData->funcType = 0;
    userData->tableType = 0;
    userData->readIdFunc = DoubleInt4_getId;
    userData->startid = 0;
    userData->endid = 1;
    userData->count = 1;
    ret = GmcSubscribe(stmt, &tmp_sub_info, subConn, snCallback, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info);

    int startid = userData->startid;
    int endid = userData->endid;
    int32_t count = 1;
    AW_FUN_Log(LOG_STEP, "写数据");
    ret = writeRecordId(conn, stmt, labelName_in, startid, endid, count, SingleInt4_setId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    // A 表插入一条数据
    ret = readRecordId(conn, stmt, labelName_in, startid, endid, count, SingleInt4_getId, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData->data, GMC_SUB_EVENT_INSERT, 1, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int readCount = endid - startid;
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = 1;
        Obj_mid[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, 1, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_mid);

    readCount = 1;
    SingleInt4St *Obj_out = (SingleInt4St *)malloc(sizeof(SingleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_in, Obj_out, readCount, SingleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    // // 查视图，预期资源视图
    AW_FUN_Log(LOG_STEP, "视图校验");

    queryView("ns1.A", "DML_TYPE: INSERT_VERTEX", "SUCCESS_COUNT: 1", "FAIL_COUNT: 0", "TOTAL_COUNT: 1");
    queryView("ns1.B", "DML_TYPE: INSERT_VERTEX", "SUCCESS_COUNT: 0", "FAIL_COUNT: 0", "TOTAL_COUNT: 0");
    queryView("ns1.rsc0", "DML_TYPE: INSERT_VERTEX", "SUCCESS_COUNT: 0", "FAIL_COUNT: 0", "TOTAL_COUNT: 0");

    ret = GmcUnSubscribe(stmt, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(userData->data);
    free(userData);

    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(subConn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 002.单写订阅pubsub资源表，向输入表写数据，获取推送数据，查视图
TEST_F(DatalogSupportView_QRY_DML_OPER_STATIS_interact, DFX_062_Interact_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char nsName[128] = "interact02";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char labelName_in[] = "ns1.A";
    char labelName_mid[] = "ns1.rsc0";
    char labelName_out[] = "ns1.B";

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *subConn;
    GmcStmtT *subStmt;
    const char *subConnName = "testSub";
    ret = testSubConnect(&subConn, NULL, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_info = NULL;
    readJanssonFile((char *)"./schema_file/pubResource_ns1.gmjson", &sub_info);
    EXPECT_NE((void *)NULL, sub_info);
    const char *subName = "testSub_ns1.rsc0";
    GmcSubConfigT tmp_sub_info;
    tmp_sub_info.subsName = subName;
    tmp_sub_info.configJson = sub_info;
    SnUserDataWithFuncT *userData = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userData->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData->data, 0, sizeof(SnUserDataT));
    userData->funcType = 0;
    userData->tableType = 1;
    userData->readIdFunc = DoubleInt4_getId;
    userData->startid = 0;
    userData->endid = 1;
    userData->count = 1;
    ret = GmcSubscribe(stmt, &tmp_sub_info, subConn, snCallback, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info);

    int startid = userData->startid;
    int endid = userData->endid;
    int32_t count = 1;
    AW_FUN_Log(LOG_STEP, "写数据");
    ret = writeRecordId(conn, stmt, labelName_in, startid, endid, count, SingleInt4_setId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    ret = readRecordId(conn, stmt, labelName_in, startid, endid, count, SingleInt4_getId, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData->data, GMC_SUB_EVENT_INSERT, 1, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int readCount = endid - startid;
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = -1;
        Obj_mid[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, 1, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_mid);

    readCount = 1;
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = -1;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_out, Obj_out, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);

    // 查视图，预期资源视图
    AW_FUN_Log(LOG_STEP, "视图校验");
    queryView("ns1.A", "DML_TYPE: INSERT_VERTEX", "SUCCESS_COUNT: 1", "FAIL_COUNT: 0", "TOTAL_COUNT: 1");
    queryView("ns1.B", "DML_TYPE: INSERT_VERTEX", "SUCCESS_COUNT: 0", "FAIL_COUNT: 0", "TOTAL_COUNT: 0");
    queryView("ns1.rsc0", "DML_TYPE: INSERT_VERTEX", "SUCCESS_COUNT: 0", "FAIL_COUNT: 0", "TOTAL_COUNT: 0");

    ret = GmcUnSubscribe(stmt, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(userData->data);
    free(userData);

    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(subConn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
