#ifndef _GM_BASIC_005_TOOLS_H_
#define _GM_BASIC_005_TOOLS_H_

#include "gm_v5_utils.h"

#ifdef __cplusplus
extern "C" {
#endif

int func_create_sub_relation(GmcStmtT *stmt, GmcConnT *sub_conn, char *file_path, GmcSubCallbackT callback,
    SnUserDataT *user_data, const char *sub_name)
{
    int ret = 0;
    pthread_mutex_lock(&LockSubChannel);
    // 创建3种订阅关系类型，property_value=1111 or property_value=2222
    char *sub_info = NULL;
    readJanssonFile(file_path, &sub_info);
    COMPARE_NE((void *)NULL, sub_info);
    GmcSubConfigT tmp_sub_info;
    tmp_sub_info.subsName = sub_name;
    tmp_sub_info.configJson = sub_info;
    ret = GmcSubscribe(stmt, &tmp_sub_info, sub_conn, callback, user_data);
    free(sub_info);
    pthread_mutex_unlock(&LockSubChannel);
    return ret;
}

// 同步创建VertexLabel, 需注意label_name 必须是schema中的name一致
int func_create_vertex_label_sync(char *file_path, GmcStmtT *stmt, const char *g_configJson, char *label_name)
{
    int32_t ret = 0;
    test_schema = NULL;
    GmcDropVertexLabel(stmt, label_name);
    pthread_mutex_lock(&LockSubChannel);
    readJanssonFile(file_path, &test_schema);
    COMPARE_NE((void *)NULL, test_schema);
    ret = GmcCreateVertexLabel(stmt, test_schema, g_configJson);
    free(test_schema);
    test_schema = NULL;
    pthread_mutex_unlock(&LockSubChannel);
    return ret;
}

// 同步创建VertexLabel, 需注意label_name 必须是schema中的name一致
int func_create_vertex_label_with_name_sync(char *file_path, GmcStmtT *stmt, const char *g_configJson, char *label_name)
{
    int32_t ret = 0;
    test_schema = NULL;
    GmcDropVertexLabel(stmt, label_name);
    pthread_mutex_lock(&LockSubChannel);
    readJanssonFile(file_path, &test_schema);
    COMPARE_NE((void *)NULL, test_schema);
    ret = GmcCreateVertexLabelWithName(stmt, test_schema, g_configJson, label_name);
    free(test_schema);
    test_schema = NULL;
    pthread_mutex_unlock(&LockSubChannel);
    return ret;
}

// 异步创建VertexLabel
int func_create_vertex_label_async(
    char *file_path, GmcStmtT *stmt, char *g_configJson, char *label_name, int32_t expect = 0)
{
    int32_t ret = 0;
    AsyncUserDataT data = {0};
    test_schema = NULL;
    GmcDropVertexLabel(stmt, label_name);
    pthread_mutex_lock(&LockSubChannel);
    readJanssonFile(file_path, &test_schema);
    COMPARE_NE((void *)NULL, test_schema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, test_schema, g_configJson, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(expect, data.status);
    free(test_schema);
    test_schema = NULL;
    pthread_mutex_unlock(&LockSubChannel);
    return ret;
}
void set_ip4forward_field_value(GmcStmtT *stmt, int loop)
{
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &loop, sizeof(loop));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &loop, sizeof(loop));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_UINT32, &loop, sizeof(loop));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "nhp_group_flag", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT32, &loop, sizeof(loop));
    EXPECT_EQ(GMERR_OK, ret);
    // hash index: unique = true
    ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT32, &loop, sizeof(loop));
    EXPECT_EQ(GMERR_OK, ret);
    // hash index: unique = false
    ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "path_flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "status_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "status_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "errcode_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "errcode_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    EXPECT_EQ(GMERR_OK, ret);
    char wr_fixed[34] = "write";
    ret = GmcSetVertexProperty(stmt, "svc_ctx_high_prio", GMC_DATATYPE_FIXED, wr_fixed, 34);
    EXPECT_EQ(GMERR_OK, ret);
    char wr1_fixed[5000] = "write";
    ret = GmcSetVertexProperty(stmt, "svc_ctx_normal_prio", GMC_DATATYPE_FIXED, wr1_fixed, 5000);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "table_smooth_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "trace", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "route_flags", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "reserved", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "test_str", GMC_DATATYPE_STRING, string_tmp, strlen(string_tmp));
    EXPECT_EQ(GMERR_OK, ret);
}

int test_read_ip4forward_by_pk(GmcConnT *conn, const char *keyName, int read_begin, int read_end, GmcDataTypeE datatype,
    int thread_id, unsigned short qos_profile_id_value = 1111, unsigned char mask_len_value = '1')
{
    int ret = 0;
    unsigned int valueSize;
    bool isNull;
    bool isFinish = true;
    ret = GmcAllocStmt(conn, &g_stmt_rd[thread_id]);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_rd[thread_id], label_name02, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int loop = read_begin; loop < read_end; loop++) {
        ret = GmcSetIndexKeyValue(g_stmt_rd[thread_id], 0, datatype, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_rd[thread_id], 1, datatype, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_rd[thread_id], 2, datatype, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_rd[thread_id], keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_rd[thread_id]);
        EXPECT_EQ(GMERR_OK, ret);
        while (isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish)
                break;
            unsigned int rd_vr_id;
            ret = GmcGetVertexPropertySizeByName(g_stmt_rd[thread_id], "vr_id", &valueSize);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(g_stmt_rd[thread_id], "vr_id", &rd_vr_id, valueSize, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(4, valueSize);
            EXPECT_EQ(loop, rd_vr_id);
            EXPECT_EQ(0, isNull);

            unsigned int rd_dest_ip_addr;
            ret = GmcGetVertexPropertySizeByName(g_stmt_rd[thread_id], "dest_ip_addr", &valueSize);
            EXPECT_EQ(GMERR_OK, ret);
            ret =
                GmcGetVertexPropertyByName(g_stmt_rd[thread_id], "dest_ip_addr", &rd_dest_ip_addr, valueSize, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(4, valueSize);
            EXPECT_EQ(loop, rd_dest_ip_addr);
            EXPECT_EQ(0, isNull);

            unsigned char rd_mask_len;
            ret = GmcGetVertexPropertySizeByName(g_stmt_rd[thread_id], "mask_len", &valueSize);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(g_stmt_rd[thread_id], "mask_len", &rd_mask_len, valueSize, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(1, valueSize);
            EXPECT_EQ(mask_len_value, rd_mask_len);
            EXPECT_EQ(0, isNull);

            unsigned short rd_qos_profile_id;
            ret = GmcGetVertexPropertySizeByName(g_stmt_rd[thread_id], "qos_profile_id", &valueSize);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(
                g_stmt_rd[thread_id], "qos_profile_id", &rd_qos_profile_id, valueSize, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(2, valueSize);
            EXPECT_EQ(qos_profile_id_value, rd_qos_profile_id);
            EXPECT_EQ(0, isNull);

            unsigned short rd_primary_label;
            ret = GmcGetVertexPropertySizeByName(g_stmt_rd[thread_id], "primary_label", &valueSize);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(
                g_stmt_rd[thread_id], "primary_label", &rd_primary_label, valueSize, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(4, valueSize);
            EXPECT_EQ(loop, rd_primary_label);
            EXPECT_EQ(0, isNull);

            char rd_svc_ctx_high_prio[35] = {0};
            ret = GmcGetVertexPropertySizeByName(g_stmt_rd[thread_id], "svc_ctx_high_prio", &valueSize);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(
                g_stmt_rd[thread_id], "svc_ctx_high_prio", &rd_svc_ctx_high_prio, valueSize, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(34, valueSize);
            // EXPECT_EQ("write", rd_svc_ctx_high_prio);  // 此处转换存在问题, 需补充一下
            EXPECT_EQ(0, isNull);

            unsigned long long rd_app_obj_id;
            ret = GmcGetVertexPropertySizeByName(g_stmt_rd[thread_id], "app_obj_id", &valueSize);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(g_stmt_rd[thread_id], "app_obj_id", &rd_app_obj_id, valueSize, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(8, valueSize);
            EXPECT_EQ(11111111, rd_app_obj_id);
            EXPECT_EQ(0, isNull);

            char rd_test_str[50] = {0};
            ret = GmcGetVertexPropertySizeByName(g_stmt_rd[thread_id], "test_str", &valueSize);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(g_stmt_rd[thread_id], "test_str", &rd_test_str, valueSize, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(strlen(string_tmp) + 1, valueSize);
            EXPECT_STREQ(string_tmp, rd_test_str);
            EXPECT_EQ(0, isNull);
        }
        GmcFreeIndexKey(g_stmt_rd[thread_id]);  // allocpkindex, 需要释放
    }
    ret = GmcResetVertex(g_stmt_rd[thread_id], false);
    GmcFreeStmt(g_stmt_rd[thread_id]);  // stmt申请了 也要注意释放, 是从连接那里alloc
    return ret;
}

// hashcluster索引 ip4forward 表 扫描
int func_ip4forward_hashcluster_scan(GmcConnT *conn, int thread_id, int expect_count, unsigned short qos_profile_id)
{
    int ret = 0;
    unsigned int scan_count = 0;
    unsigned int isNull;
    bool isFinish = false;
    ret = GmcAllocStmt(conn, &g_stmt_rd[thread_id]);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_rd[thread_id], label_name02, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt_rd[thread_id], 0, GMC_DATATYPE_UINT16, &qos_profile_id, sizeof(qos_profile_id));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_rd[thread_id], 1, GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(g_stmt_rd[thread_id], "hashcluster_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_rd[thread_id]);
    EXPECT_EQ(GMERR_OK, ret);
    while (isFinish == false) {
        ret = GmcFetch(g_stmt_rd[thread_id], &isFinish);
        if (isFinish == true || ret != 0) {
            printf("fetch times: %d, status is %d \n", scan_count, ret);
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
        scan_count++;
    }
    EXPECT_EQ(expect_count, scan_count);
    ret = GmcResetVertex(g_stmt_rd[thread_id], false);
    EXPECT_EQ(GMERR_OK, ret);
    GmcFreeIndexKey(g_stmt_rd[thread_id]);
    GmcFreeStmt(g_stmt_rd[thread_id]);
    return ret;
}

// 同步 hashcluster 索引 ip4forward 表删除
int func_hashcluster_ip4forward_del(
    GmcConnT *conn, int thread_id, int expect_count, bool is_batch = 0, unsigned short qos_profile_id = 1111)
{
    int ret = 0, affectRows;
    unsigned int valueSize;
    unsigned int isNull;
    // unsigned short up_qos_profile_id = 2222;
    char up_fixed[36] = "update";
    unsigned char up_uint8 = '2';
    unsigned int len;
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcAllocStmt(conn, &g_stmt_rd[thread_id]);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_rd[thread_id], label_name02, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_rd[thread_id], 0, GMC_DATATYPE_UINT16, &qos_profile_id, sizeof(qos_profile_id));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_rd[thread_id], 1, GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(g_stmt_rd[thread_id], "hashcluster_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_rd[thread_id]);
    EXPECT_EQ(ret == GMERR_OK || ret == GMERR_OUT_OF_MEMORY || ret == GMERR_REQUEST_TIME_OUT, true);
    if (ret == GMERR_OUT_OF_MEMORY || ret == GMERR_REQUEST_TIME_OUT) {
        printf("the ret is %d.\n", ret);
        ret = GmcResetVertex(g_stmt_rd[thread_id], false);
        EXPECT_EQ(GMERR_OK, ret);
        GmcFreeIndexKey(g_stmt_rd[thread_id]);
        GmcFreeStmt(g_stmt_rd[thread_id]);
        return ret;
    }
    // get affect row
    ret = GmcGetStmtAttr(g_stmt_rd[thread_id], GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(expect_count, affectRows);

    ret = GmcResetVertex(g_stmt_rd[thread_id], false);
    EXPECT_EQ(GMERR_OK, ret);
    GmcFreeIndexKey(g_stmt_rd[thread_id]);
    GmcFreeStmt(g_stmt_rd[thread_id]);
    return ret;
}

int func_ip4forward_pk_update(GmcStmtT *stmt, int start_id, int end_id)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name02, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    char *string_tmp = (char *)"peiyanupdate";
    for (unsigned int loop = start_id; loop < end_id; loop++) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "test_str", GMC_DATATYPE_STRING, string_tmp, strlen(string_tmp));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "primary_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
    }
    return ret;
}

void sn_callback_simple(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int index, i, ret = 0;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;
    SnUserDataT *user_data = (SnUserDataT *)userData;

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        if (ret != GMERR_OK || eof == true) {
            if (ret != GMERR_OK && info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
                user_data->scanEofNum++;
            }
            break;
        }
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE_INSERT: {
                user_data->replaceInsertNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
        }
    }
}

#ifdef __cplusplus
}
#endif
#endif
