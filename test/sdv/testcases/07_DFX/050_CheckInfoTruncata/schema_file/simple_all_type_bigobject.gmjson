[{"type": "record", "name": "TEST_T3", "fields": [{"name": "F0", "type": "int64", "nullable": false}, {"name": "F1", "type": "uint64", "nullable": true}, {"name": "F2", "type": "int32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "fixed", "size": 13312, "nullable": true}, {"name": "F5", "type": "fixed", "size": 13312, "nullable": true}, {"name": "F6", "type": "fixed", "size": 13312, "nullable": true}], "keys": [{"node": "TEST_T3", "name": "TEST_PK", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]