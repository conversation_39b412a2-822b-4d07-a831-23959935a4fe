#ifndef _GM_BASIC_005_TOOLS_H_
#define _GM_BASIC_005_TOOLS_H_

#include "gm_v5_utils.h"
#include "t_datacom_lite.h"

#ifdef __cplusplus
extern "C" {
#endif

char g_pk_name[] = "TEST_PK";

int func_create_sub_relation(GmcStmtT *stmt, GmcConnT *sub_conn, char *file_path, GmcSubCallbackT callback,
    SnUserDataT *user_data, const char *sub_name)
{
    int ret = 0;
    pthread_mutex_lock(&LockSubChannel);
    // 创建3种订阅关系类型，property_value=1111 or property_value=2222
    char *sub_info = NULL;
    readJanssonFile(file_path, &sub_info);
    COMPARE_NE((void *)NULL, sub_info);
    GmcSubConfigT tmp_sub_info;
    tmp_sub_info.subsName = sub_name;
    tmp_sub_info.configJson = sub_info;
    ret = GmcSubscribe(stmt, &tmp_sub_info, sub_conn, callback, user_data);
    free(sub_info);
    pthread_mutex_unlock(&LockSubChannel);
    return ret;
}

// 同步创建VertexLabel, 需注意label_name 必须是schema中的name一致
int func_create_vertex_label_sync(char *file_path, GmcStmtT *stmt, const char *g_configJson, char *label_name)
{
    int32_t ret = 0;
    test_schema = NULL;
    GmcDropVertexLabel(stmt, label_name);
    pthread_mutex_lock(&LockSubChannel);
    readJanssonFile(file_path, &test_schema);
    COMPARE_NE((void *)NULL, test_schema);
    ret = GmcCreateVertexLabel(stmt, test_schema, g_configJson);
    free(test_schema);
    test_schema = NULL;
    pthread_mutex_unlock(&LockSubChannel);
    return ret;
}

// 同步创建VertexLabel, 需注意label_name 必须是schema中的name一致
int func_create_vertex_label_with_name_sync(char *file_path, GmcStmtT *stmt, const char *g_configJson, char *label_name)
{
    int32_t ret = 0;
    test_schema = NULL;
    GmcDropVertexLabel(stmt, label_name);
    pthread_mutex_lock(&LockSubChannel);
    readJanssonFile(file_path, &test_schema);
    COMPARE_NE((void *)NULL, test_schema);
    ret = GmcCreateVertexLabelWithName(stmt, test_schema, g_configJson, label_name);
    free(test_schema);
    test_schema = NULL;
    pthread_mutex_unlock(&LockSubChannel);
    return ret;
}

// 异步创建VertexLabel
int func_create_vertex_label_async(
    char *file_path, GmcStmtT *stmt, char *g_configJson, char *label_name, int32_t expect = 0)
{
    int32_t ret = 0;
    AsyncUserDataT data = {0};
    test_schema = NULL;
    GmcDropVertexLabel(stmt, label_name);
    pthread_mutex_lock(&LockSubChannel);
    readJanssonFile(file_path, &test_schema);
    COMPARE_NE((void *)NULL, test_schema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, test_schema, g_configJson, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(expect, data.status);
    free(test_schema);
    test_schema = NULL;
    pthread_mutex_unlock(&LockSubChannel);
    return ret;
}

#pragma pack(1)
struct TEST_T1_struct_t {
    int64_t F0;
    uint64_t F1;
    int32_t F2;
    uint32_t F3;
    int16_t F4;
    uint16_t F5;
    int8_t F6;
    uint8_t F7;
    bool F8;
    float F9;
    double F10;
    uint64_t F11;
    char F12;
    unsigned char F13;
    int8_t F14[16];
    uint8_t F15;
};

struct TEST_T3_struct_t {
    int64_t F0;
    uint64_t F1;
    int32_t F2;
    uint32_t F3;
    int8_t F4[STRING_MAX_SIZE];
    int8_t F5[STRING_MAX_SIZE];
    int8_t F6[STRING_MAX_SIZE];
};

struct TEST_T4_struct_t {
    int64_t F0;
    uint64_t F1;
    int32_t F2;
    uint32_t F3;
    int16_t F4;
    uint16_t F5;
    int8_t F6;
    uint8_t F7;
    bool F8;
    float F9;
    double F10;
    uint64_t F11;
    char F12;
    unsigned char F13;
    int8_t F14[16];
    uint8_t F15;
    uint64_t F16;
};

#pragma pack()

static const char *gResPoolTest =
    R"({
        "name" : "resource_pool_test",
        "pool_id" : 65535,
        "start_id" : 0,
        "capacity" : 1000000,
        "order" : 0,
        "alloc_type" : 0
    })";
static const char *resPoolTestName = "resource_pool_test";

// 结构化写各种类型的定长字段简单表
int func_struct_write_vertex_label_sync(GmcStmtT *stmt, int32_t start_id, int32_t end_id)
{
    int ret = 0;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name03, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name03, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int32_t i = start_id; i < end_id; i++) {
        obj.F0 = (int64_t)i;                                           // int64   8
        obj.F1 = (uint64_t)i + 0xFFFFFFFF;                             // uint64  8
        obj.F2 = i;                                                    // int32   4
        obj.F3 = i;                                                    // uint32  4
        obj.F4 = i & 0x7FFF;                                           // int16   2
        obj.F5 = i & 0xFFFF;                                           // uint16  2
        obj.F6 = i & 0x7F;                                             // int8    1
        obj.F7 = i & 0xFF;                                             // uint8   1
        obj.F8 = 0;                                                    // boolean 1
        obj.F9 = i;                                                    // float   4
        obj.F10 = i;                                                   // double  8
        obj.F11 = i + 0xFFFFFFFF;                                      // time    8
        obj.F12 = 'a' + (i & 0x1A);                                    // char    1  1A=26
        obj.F13 = 'A' + (i & 0x1A);                                    // uchar   1
        snprintf((char *)obj.F14, sizeof(obj.F14), "aaaaaaa%08d", i);  // fixed  16
        obj.F15 = i & 0xF;                                             // partition  1
        ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return ret;
}

void test_read_simple_table(GmcStmtT *stmt, int index, char *label_name)
{
    int ret;
    int64_t F0Value = (int64_t)index;
    uint64_t F1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t F2Value = index;
    uint32_t F3Value = index;
    int16_t F4Value = index & 0x7FFF;
    uint16_t F5Value = index & 0xFFFF;
    int8_t F6Value = index & 0x7F;
    uint8_t F7Value = index & 0xFF;
    bool F8Value = false;
    float F9Value = index;
    double F10Value = index;
    uint64_t F11Value = index + 0xFFFFFFFF;
    char F12Value = 'a' + (index & 0x1A);
    unsigned char F13Value = 'A' + (index & 0x1A);
    char F14Value[16] = {0};
    snprintf((char *)F14Value, sizeof(F14Value), "aaaaaaa%08d", index);
    uint8_t F15Value = index & 0xF;

    ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_INT64, &F0Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_UINT64, &F1Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_INT32, &F2Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_UINT32, &F3Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F4", GMC_DATATYPE_INT16, &F4Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F5", GMC_DATATYPE_UINT16, &F5Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F6", GMC_DATATYPE_INT8, &F6Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F7", GMC_DATATYPE_UINT8, &F7Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F8", GMC_DATATYPE_BOOL, &F8Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F9", GMC_DATATYPE_FLOAT, &F9Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F10", GMC_DATATYPE_DOUBLE, &F10Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F11", GMC_DATATYPE_TIME, &F11Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F12", GMC_DATATYPE_CHAR, &F12Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F13", GMC_DATATYPE_UCHAR, &F13Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F14", GMC_DATATYPE_FIXED, F14Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F15", GMC_DATATYPE_PARTITION, &F15Value);
    EXPECT_EQ(GMERR_OK, ret);
}

// 主键读取数据
int func_normal_pkread_vertex_label_sync(GmcStmtT *stmt, int32_t start_id, int32_t end_id)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name03, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (int32_t i = start_id; i < end_id; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, g_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            // 聚簇容器变更，不关注顺序
            cnt++;
        }
        EXPECT_EQ(1, cnt);
    }
    return ret;
}

// 主键读取数据
int func_normal_pkread_resource_sync(GmcStmtT *stmt, int32_t start_id, int32_t end_id)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name06, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (int32_t i = start_id; i < end_id; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, g_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            // 聚簇容器变更，不关注顺序
            bool isNull;
            uint32_t resSize;
            uint64_t resVal;
            ret = GmcGetVertexPropertySizeByName(stmt, "F16", &resSize);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(stmt, "F16", &resVal, resSize, &isNull);
            EXPECT_EQ(GMERR_OK, ret);

            uint16_t tmpCount;
            uint32_t tmpStartIndex;
            ret = GmcGetCountResource(resVal, &tmpCount);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetStartIdxResource(resVal, &tmpStartIndex);
            EXPECT_EQ(GMERR_OK, ret);

            EXPECT_EQ(1, tmpCount);
            EXPECT_EQ(i, tmpStartIndex);
            cnt++;
        }
        EXPECT_EQ(1, cnt);
    }
    return ret;
}

void test_struct_read_simple_table(TEST_T1_struct_t *d, int index, char *label_name)
{
    int ret = 0;
    int64_t F0Value = (int64_t)index;
    uint64_t F1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t F2Value = index;
    uint32_t F3Value = index;
    int16_t F4Value = index & 0x7FFF;
    uint16_t F5Value = index & 0xFFFF;
    int8_t F6Value = index & 0x7F;
    uint8_t F7Value = index & 0xFF;
    bool F8Value = false;
    float F9Value = index;
    double F10Value = index;
    uint64_t F11Value = index + 0xFFFFFFFF;
    char F12Value = 'a' + (index & 0x1A);
    unsigned char F13Value = 'A' + (index & 0x1A);
    char F14Value[16] = {0};
    snprintf((char *)F14Value, sizeof(F14Value), "aaaaaaa%08d", index);
    uint8_t F15Value = index & 0xF;

    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT64, &F0Value, &d->F0, sizeof(F0Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT64, &F1Value, &d->F1, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT32, &F2Value, &d->F2, sizeof(F2Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &F3Value, &d->F3, sizeof(F3Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT16, &F4Value, &d->F4, sizeof(F4Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT16, &F5Value, &d->F5, sizeof(F5Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT8, &F6Value, &d->F6, sizeof(F6Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT8, &F7Value, &d->F7, sizeof(F7Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BOOL, &F8Value, &d->F8, sizeof(F8Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FLOAT, &F9Value, &d->F9, sizeof(F9Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_DOUBLE, &F10Value, &d->F10, sizeof(F10Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_TIME, &F11Value, &d->F11, sizeof(F11Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_CHAR, &F12Value, &d->F12, sizeof(F12Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UCHAR, &F13Value, &d->F13, sizeof(F13Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, F14Value, d->F14, sizeof(F14Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_PARTITION, &F15Value, &d->F15, sizeof(F15Value));
    EXPECT_EQ(GMERR_OK, ret);
}

// 结构化读数据
int func_struct_pkread_vertex_label_sync(
    GmcStmtT *stmt, int32_t start_id, int32_t end_id, char *tablename = label_name03)
{
    int ret = 0;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {tablename, 0, g_testNameSpace};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(stmt, &obj, &deseri, &deseriCtx, false, &labelInfo);

    ret = testGmcPrepareStmtByLabelName(stmt, tablename, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (int32_t i = start_id; i < end_id; i++) {
        obj.F0 = (int64_t)i;
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, 0, NULL, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            ret = testStructGetVertexDeseri(stmt, &deseri);
            EXPECT_EQ(GMERR_OK, ret);
            test_struct_read_simple_table(&obj, i, tablename);
            cnt++;
        }
        EXPECT_EQ(1, cnt);
    }
    deSeriFreeDynMem(&deseriCtx, true);
    return ret;
}

// 结构化读数据
int func_struct_pkread_resource_sync(GmcStmtT *stmt, int32_t start_id, int32_t end_id)
{
    int ret = 0;
    TEST_T4_struct_t obj = (TEST_T4_struct_t){0};
    TestLabelInfoT labelInfo = {label_name06, 0, g_testNameSpace};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(stmt, &obj, &deseri, &deseriCtx, false, &labelInfo);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name06, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (int32_t i = start_id; i < end_id; i++) {
        obj.F0 = (int64_t)i;
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, 0, NULL, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            ret = testStructGetVertexDeseri(stmt, &deseri);
            EXPECT_EQ(GMERR_OK, ret);

            uint64_t resVal = obj.F16;
            uint16_t  tmpCount;
            uint32_t tmpStartIndex;
            ret = GmcGetCountResource(resVal, &tmpCount);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetStartIdxResource(resVal, &tmpStartIndex);
            EXPECT_EQ(GMERR_OK, ret);

            EXPECT_EQ(1, tmpCount);
            EXPECT_EQ(i, tmpStartIndex);
            cnt++;
        }
        EXPECT_EQ(1, cnt);
    }
    deSeriFreeDynMem(&deseriCtx, true);
    return ret;
}

// 通过主键获取记录数
int func_struct_pkgetcount_vertex_label_sync(GmcStmtT *stmt, int32_t start_id, int32_t end_id)
{
    int ret, i;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name03, 0, g_testNameSpace};

    for (int32_t i = start_id; i < end_id; i++) {
        obj.F0 = (int64_t)i;
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, 0, NULL, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        uint64_t count = 0;
        ret = GmcGetVertexCount(stmt, label_name03, g_pk_name, &count);
        EXPECT_EQ(1, count);
    }
    return ret;
}

void test_set_vertex_property(GmcStmtT *stmt, int index, bool bool_value)
{
    int ret;
    uint64_t F1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t F2Value = index;
    uint32_t F3Value = index;
    int16_t F4Value = index & 0x7FFF;
    uint16_t F5Value = index & 0xFFFF;
    int8_t F6Value = index & 0x7F;
    uint8_t F7Value = index & 0xFF;
    bool F8Value = bool_value;
    float F9Value = index;
    double F10Value = index;
    uint64_t F11Value = index + 0xFFFFFFFF;
    char F12Value = 'a' + (index & 0x1A);
    unsigned char F13Value = 'A' + (index & 0x1A);
    char F14Value[16] = {0};
    snprintf((char *)F14Value, sizeof(F14Value), "aaaaaaa%08d", index);
    uint8_t F15Value = index & 0xF;

    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT64, &F1Value, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT32, &F3Value, sizeof(F3Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &F4Value, sizeof(F4Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &F5Value, sizeof(F5Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT8, &F6Value, sizeof(F6Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT8, &F7Value, sizeof(F7Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &F8Value, sizeof(F8Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_FLOAT, &F9Value, sizeof(F9Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_DOUBLE, &F10Value, sizeof(F10Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_TIME, &F11Value, sizeof(F11Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_CHAR, &F12Value, sizeof(F12Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_UCHAR, &F13Value, sizeof(F13Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_FIXED, F14Value, sizeof(F14Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_PARTITION, &F15Value, sizeof(F15Value));
    EXPECT_EQ(GMERR_OK, ret);
}

// 结构化merge各种类型的定长字段简单表
int func_struct_merge_vertex_label_sync(GmcStmtT *stmt, int32_t start_id, int32_t end_id, uint32_t keyId)
{
    int ret = 0;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name03, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name03, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int32_t i = start_id; i < end_id; i++) {
        obj.F0 = (int64_t)i;
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, keyId, NULL, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        test_set_vertex_property(stmt, i, 0);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return ret;
}

// 结构化写各种类型的定长字段简单表
int func_struct_replace_vertex_label_sync(
    GmcStmtT *stmt, int32_t start_id, int32_t end_id, char *tablename = label_name03)
{
    int ret = 0;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {tablename, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, tablename, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int32_t i = start_id; i < end_id; i++) {
        obj.F0 = (int64_t)i;                                           // int64   8
        obj.F1 = (uint64_t)i + 0xFFFFFFFF;                             // uint64  8
        obj.F2 = i;                                                    // int32   4
        obj.F3 = i;                                                    // uint32  4
        obj.F4 = i & 0x7FFF;                                           // int16   2
        obj.F5 = i & 0xFFFF;                                           // uint16  2
        obj.F6 = i & 0x7F;                                             // int8    1
        obj.F7 = i & 0xFF;                                             // uint8   1
        obj.F8 = 0;                                                    // boolean 1
        obj.F9 = i;                                                    // float   4
        obj.F10 = i;                                                   // double  8
        obj.F11 = i + 0xFFFFFFFF;                                      // time    8
        obj.F12 = 'a' + (i & 0x1A);                                    // char    1  1A=26
        obj.F13 = 'A' + (i & 0x1A);                                    // uchar   1
        snprintf((char *)obj.F14, sizeof(obj.F14), "aaaaaaa%08d", i);  // fixed  16
        obj.F15 = i & 0xF;                                             // partition  1
        ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return ret;
}

// 结构化写各种类型的定长字段简单表
int func_struct_replace_vertex_label_async(GmcStmtT *stmt, int32_t start_id, int32_t end_id)
{
    int ret = 0;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name03, 0, g_testNameSpace};
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name03, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int32_t i = start_id; i < end_id; i++) {
        obj.F0 = (int64_t)i;                                           // int64   8
        obj.F1 = (uint64_t)i + 0xFFFFFFFF;                             // uint64  8
        obj.F2 = i;                                                    // int32   4
        obj.F3 = i;                                                    // uint32  4
        obj.F4 = i & 0x7FFF;                                           // int16   2
        obj.F5 = i & 0xFFFF;                                           // uint16  2
        obj.F6 = i & 0x7F;                                             // int8    1
        obj.F7 = i & 0xFF;                                             // uint8   1
        obj.F8 = 0;                                                    // boolean 1
        obj.F9 = i;                                                    // float   4
        obj.F10 = i;                                                   // double  8
        obj.F11 = i + 0xFFFFFFFF;                                      // time    8
        obj.F12 = 'a' + (i & 0x1A);                                    // char    1  1A=26
        obj.F13 = 'A' + (i & 0x1A);                                    // uchar   1
        snprintf((char *)obj.F14, sizeof(obj.F14), "aaaaaaa%08d", i);  // fixed  16
        obj.F15 = i & 0xF;                                             // partition  1
        ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = replace_vertex_callback;
        insertRequestCtx.userData = &data;
        ret = GmcExecuteAsync(stmt, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }
    return ret;
}

// 结构化主键删除简单表
int func_struct_del_vertex_label_sync(
    GmcStmtT *stmt, int32_t start_id, int32_t end_id, uint32_t keyId, char *tablename = label_name03)
{
    int ret = 0;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {tablename, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, tablename, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int32_t i = start_id; i < end_id; i++) {
        obj.F0 = (int64_t)i;
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, keyId, NULL, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return ret;
}

// 结构化主键删除简单表
int func_struct_del_resource_sync(GmcStmtT *stmt, int32_t start_id, int32_t end_id, uint32_t keyId)
{
    int ret = 0;
    TEST_T4_struct_t obj = (TEST_T4_struct_t){0};
    TestLabelInfoT labelInfo = {label_name06, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name06, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int32_t i = start_id; i < end_id; i++) {
        obj.F0 = (int64_t)i;
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, keyId, NULL, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return ret;
}

int func_update_vertex_label_sync(GmcStmtT *stmt, int32_t start_id, int32_t end_id, uint32_t keyId, char *tablename)
{
    int ret;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {tablename, 0, g_testNameSpace};

    for (int i = start_id; i < end_id; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, tablename, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        obj.F0 = (int64_t)i;
        uint32_t F3Value = i + 90000;
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, keyId, NULL, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT32, &F3Value, sizeof(F3Value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return ret;
}

// 结构化主键删除简单表
int func_struct_del_vertex_label_async(GmcStmtT *stmt, int32_t start_id, int32_t end_id, uint32_t keyId)
{
    int ret = 0;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name03, 0, g_testNameSpace};
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name03, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int32_t i = start_id; i < end_id; i++) {
        obj.F0 = (int64_t)i;
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, keyId, NULL, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT deleteRequestCtx;
        deleteRequestCtx.deleteCb = delete_vertex_callback;
        deleteRequestCtx.userData = &data;
        ret = GmcExecuteAsync(stmt, &deleteRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }
    return ret;
}

// 结构化写各种类型的定长字段简单表
int func_struct_batchwrite_vertex_label_sync(GmcStmtT *stmt, int32_t start_id, int32_t end_id)
{
    int ret = 0;
    int num_per_batch = 1024;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name04, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name04, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    EXPECT_EQ(GMERR_OK, ret);

    for (int32_t loop = 0; loop < end_id / num_per_batch; loop++) {
        for (int32_t pk_index = 0; pk_index < num_per_batch; pk_index++) {
            int32_t i = num_per_batch * loop + pk_index;
            obj.F0 = (int64_t)i;                                           // int64   8
            obj.F1 = (uint64_t)i + 0xFFFFFFFF;                             // uint64  8
            obj.F2 = i;                                                    // int32   4
            obj.F3 = i;                                                    // uint32  4
            obj.F4 = i & 0x7FFF;                                           // int16   2
            obj.F5 = i & 0xFFFF;                                           // uint16  2
            obj.F6 = i & 0x7F;                                             // int8    1
            obj.F7 = i & 0xFF;                                             // uint8   1
            obj.F8 = 0;                                                    // boolean 1
            obj.F9 = i;                                                    // float   4
            obj.F10 = i;                                                   // double  8
            obj.F11 = i + 0xFFFFFFFF;                                      // time    8
            obj.F12 = 'a' + (i & 0x1A);                                    // char    1  1A=26
            obj.F13 = 'A' + (i & 0x1A);                                    // uchar   1
            snprintf((char *)obj.F14, sizeof(obj.F14), "aaaaaaa%08d", i);  // fixed  16
            obj.F15 = i & 0xF;                                             // partition  1
            ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcBatchAddDML(batch, stmt);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcBatchExecute(batch, &batchRet);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t totalNum, successNum;
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, totalNum);
        EXPECT_EQ(1, successNum);
    }
    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    return ret;
}

// 结构化读---主键非唯一
int func_struct_pkreadmulti_vertex_label_sync(GmcStmtT *stmt, int32_t start_id, int32_t end_id)
{
    int ret = 0;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name04, 0, g_testNameSpace};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(stmt, &obj, &deseri, &deseriCtx, false, &labelInfo);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name04, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (int32_t i = start_id; i < end_id; i++) {
        obj.F0 = (int64_t)i;
        obj.F1 = (uint64_t)i + 0xFFFFFFFF;
        obj.F2 = i;
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, 0, NULL, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            ret = testStructGetVertexDeseri(stmt, &deseri);
            EXPECT_EQ(GMERR_OK, ret);
            test_struct_read_simple_table(&obj, i, label_name04);
            cnt++;
        }
        EXPECT_EQ(1, cnt);
    }
    deSeriFreeDynMem(&deseriCtx, true);
    return ret;
}

// 结构化主键删除简单表
int func_struct_delmulti_vertex_label_sync(GmcStmtT *stmt, int32_t start_id, int32_t end_id, uint32_t keyId)
{
    int ret = 0;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name04, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name04, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int32_t i = start_id; i < end_id; i++) {
        obj.F0 = (int64_t)i;
        obj.F1 = (uint64_t)i + 0xFFFFFFFF;
        obj.F2 = i;
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, keyId, NULL, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return ret;
}

// 结构化replace各种类型的定长字段简单表
int func_struct_batchreplace_vertex_label_sync(GmcStmtT *stmt, int32_t start_id, int32_t end_id)
{
    int ret = 0;
    int num_per_batch = 1024;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name04, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name04, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    EXPECT_EQ(GMERR_OK, ret);

    for (int32_t loop = 0; loop < end_id / num_per_batch; loop++) {
        for (int32_t pk_index = 0; pk_index < num_per_batch; pk_index++) {
            int32_t i = num_per_batch * loop + pk_index;
            obj.F0 = (int64_t)i;                                           // int64   8
            obj.F1 = (uint64_t)i + 0xFFFFFFFF;                             // uint64  8
            obj.F2 = i;                                                    // int32   4
            obj.F3 = i;                                                    // uint32  4
            obj.F4 = i & 0x7FFF;                                           // int16   2
            obj.F5 = i & 0xFFFF;                                           // uint16  2
            obj.F6 = i & 0x7F;                                             // int8    1
            obj.F7 = i & 0xFF;                                             // uint8   1
            obj.F8 = 0;                                                    // boolean 1
            obj.F9 = i;                                                    // float   4
            obj.F10 = i;                                                   // double  8
            obj.F11 = i + 0xFFFFFFFF;                                      // time    8
            obj.F12 = 'a' + (i & 0x1A);                                    // char    1  1A=26
            obj.F13 = 'A' + (i & 0x1A);                                    // uchar   1
            snprintf((char *)obj.F14, sizeof(obj.F14), "aaaaaaa%08d", i);  // fixed  16
            obj.F15 = i & 0xF;                                             // partition  1
            ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcBatchAddDML(batch, stmt);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcBatchExecute(batch, &batchRet);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t totalNum, successNum;
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, totalNum);
        EXPECT_EQ(1, successNum);
    }
    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    return ret;
}

// 批量结构化主键删除简单表
int func_struct_batchdelmulti_vertex_label_sync(GmcStmtT *stmt, int32_t start_id, int32_t end_id)
{
    int ret = 0;
    int num_per_batch = 1024;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name04, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name04, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    EXPECT_EQ(GMERR_OK, ret);

    for (int32_t loop = 0; loop < end_id / num_per_batch; loop++) {
        for (int32_t pk_index = 0; pk_index < num_per_batch; pk_index++) {
            int32_t i = num_per_batch * loop + pk_index;
            obj.F0 = (int64_t)i;                // int64   8
            obj.F1 = (uint64_t)i + 0xFFFFFFFF;  // uint64  8
            obj.F2 = i;                         // int32   4
            ret = testStructSetIndexKeyWithBuf(stmt, &obj, 0, NULL, &labelInfo);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcBatchAddDML(batch, stmt);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcBatchExecute(batch, &batchRet);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t totalNum, successNum;
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, totalNum);
        EXPECT_EQ(1, successNum);
    }
    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    return ret;
}

// 异步批量结构化主键删除简单表
int func_struct_batchdelmulti_vertex_label_async(GmcStmtT *stmt, int32_t start_id, int32_t end_id)
{
    int ret = 0;
    int num_per_batch = 1024;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    AsyncUserDataT data = {0};
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name04, 0, g_testNameSpace};

    ret = testGmcPrepareStmtByLabelName(stmt, label_name04, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    EXPECT_EQ(GMERR_OK, ret);

    for (int32_t loop = 0; loop < end_id / num_per_batch; loop++) {
        for (int32_t pk_index = 0; pk_index < num_per_batch; pk_index++) {
            int32_t i = num_per_batch * loop + pk_index;
            obj.F0 = (int64_t)i;                // int64   8
            obj.F1 = (uint64_t)i + 0xFFFFFFFF;  // uint64  8
            obj.F2 = i;                         // int32   4
            ret = testStructSetIndexKeyWithBuf(stmt, &obj, 0, NULL, &labelInfo);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcBatchAddDML(batch, stmt);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(1, data.totalNum);
        EXPECT_EQ(1, data.succNum);
    }
    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    return ret;
}

void test_set_TEST_T3_value(TEST_T3_struct_t *d, int v)
{
    int i;
    char field_value[STRING_MAX_SIZE] = {0};
    d->F0 = (int64_t)v;                // int64   8
    d->F1 = (uint64_t)v + 0xFFFFFFFF;  // uint64  8
    d->F2 = v;                         // int32   4
    d->F3 = v;                         // uint32  4
    for (i = 0; i < STRING_MAX_SIZE - 1; i++) {
        field_value[i] = 'A';
    };
    snprintf((char *)d->F4, sizeof(d->F4), "%s", field_value);
    snprintf((char *)d->F5, sizeof(d->F5), "%s", field_value);
    snprintf((char *)d->F6, sizeof(d->F6), "%s", field_value);
}
// 大对象简单表结构化写入
int32_t db_test_struct_write_big_obj_table(GmcStmtT *stmt, int32_t start_id, int32_t end_id)
{
    int32_t ret = 0;
    unsigned int len;
    TEST_T3_struct_t *obj = (TEST_T3_struct_t *)malloc(sizeof(TEST_T3_struct_t));
    memset(obj, 0, sizeof(TEST_T3_struct_t));
    TestLabelInfoT labelInfo = {label_name05, 0, g_testNameSpace};

    ret = testGmcPrepareStmtByLabelName(stmt, label_name05, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = start_id; i < end_id; i++) {
        test_set_TEST_T3_value(obj, i);
        ret = testStructSetVertexWithBuf(stmt, obj, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    free(obj);
    return ret;
}

// 大对象简单表结构化replace写入
int32_t db_test_struct_replace_big_obj_table(GmcStmtT *stmt, int32_t start_id, int32_t end_id)
{
    int32_t ret = 0;
    unsigned int len;
    TEST_T3_struct_t *obj = (TEST_T3_struct_t *)malloc(sizeof(TEST_T3_struct_t));
    memset(obj, 0, sizeof(TEST_T3_struct_t));
    TestLabelInfoT labelInfo = {label_name05, 0, g_testNameSpace};

    ret = testGmcPrepareStmtByLabelName(stmt, label_name05, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = start_id; i < end_id; i++) {
        test_set_TEST_T3_value(obj, i);
        ret = testStructSetVertexWithBuf(stmt, obj, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    free(obj);
    return ret;
}

// 大对象简单表结构化replace写入
int32_t db_test_struct_replace_big_obj_table_async(GmcStmtT *stmt, int32_t start_id, int32_t end_id)
{
    int32_t ret = 0;
    unsigned int len;
    AsyncUserDataT data = {0};
    TEST_T3_struct_t *obj = (TEST_T3_struct_t *)malloc(sizeof(TEST_T3_struct_t));
    memset(obj, 0, sizeof(TEST_T3_struct_t));
    TestLabelInfoT labelInfo = {label_name05, 0, g_testNameSpace};

    ret = testGmcPrepareStmtByLabelName(stmt, label_name05, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = start_id; i < end_id; i++) {
        test_set_TEST_T3_value(obj, i);
        ret = testStructSetVertexWithBuf(stmt, obj, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    free(obj);
    return ret;
}

void test_read_big_obj_table(GmcStmtT *stmt, int index, char *label_name)
{
    int ret, i;
    char field_value[STRING_MAX_SIZE] = {0};
    int64_t F0Value = (int64_t)index;
    uint64_t F1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t F2Value = index;
    uint32_t F3Value = index;
    for (i = 0; i < STRING_MAX_SIZE - 1; i++) {
        field_value[i] = 'A';
    };

    ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_INT64, &F0Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_UINT64, &F1Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_INT32, &F2Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_UINT32, &F3Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F4", GMC_DATATYPE_FIXED, field_value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F5", GMC_DATATYPE_FIXED, field_value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F6", GMC_DATATYPE_FIXED, field_value);
    EXPECT_EQ(GMERR_OK, ret);
}

// 主键读取大对象数据
int func_normal_pkread_big_obj_sync(GmcStmtT *stmt, int32_t start_id, int32_t end_id)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name05, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (int32_t i = start_id; i < end_id; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, g_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            test_read_big_obj_table(stmt, i, label_name05);
            cnt++;
        }
        EXPECT_EQ(1, cnt);
    }
    return ret;
}

void test_struct_read_bigobject_table(TEST_T3_struct_t *d, int index, char *label_name)
{
    int ret, i;
    char field_value[STRING_MAX_SIZE] = {0};
    int64_t F0Value = (int64_t)index;
    uint64_t F1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t F2Value = index;
    uint32_t F3Value = index;
    for (i = 0; i < STRING_MAX_SIZE - 1; i++) {
        field_value[i] = 'A';
    };

    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT64, &F0Value, &d->F0, sizeof(F0Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT64, &F1Value, &d->F1, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT32, &F2Value, &d->F2, sizeof(F2Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &F3Value, &d->F3, sizeof(F3Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, field_value, &d->F4, sizeof(field_value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, field_value, &d->F5, sizeof(field_value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, field_value, &d->F6, sizeof(field_value));
    EXPECT_EQ(GMERR_OK, ret);
}
// 结构化读大对象数据
int func_struct_pkread_big_obj_sync(GmcStmtT *stmt, int32_t start_id, int32_t end_id)
{
    int ret = 0;
    TEST_T3_struct_t obj = (TEST_T3_struct_t){0};
    TestLabelInfoT labelInfo = {label_name05, 0, g_testNameSpace};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(stmt, &obj, &deseri, &deseriCtx, false, &labelInfo);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name05, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (int32_t i = start_id; i < end_id; i++) {
        obj.F0 = (int64_t)i;
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, 0, NULL, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            ret = testStructGetVertexDeseri(stmt, &deseri);
            EXPECT_EQ(GMERR_OK, ret);
            test_struct_read_bigobject_table(&obj, i, label_name05);
            cnt++;
        }
        EXPECT_EQ(1, cnt);
    }
    deSeriFreeDynMem(&deseriCtx, true);
    return ret;
}

// 结构化主键删除大对象
int func_struct_del_bigobject_sync(GmcStmtT *stmt, int32_t start_id, int32_t end_id, uint32_t keyId)
{
    int ret = 0;
    TEST_T3_struct_t obj = (TEST_T3_struct_t){0};
    TestLabelInfoT labelInfo = {label_name05, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name05, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int32_t i = start_id; i < end_id; i++) {
        obj.F0 = (int64_t)i;
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, keyId, NULL, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return ret;
}

// 结构化写带有资源字段的简单表
int func_struct_replace_resources_sync(GmcStmtT *stmt, int32_t start_id, int32_t end_id)
{
    int ret = 0;
    TEST_T4_struct_t obj = (TEST_T4_struct_t){0};
    TestLabelInfoT labelInfo = {label_name06, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name06, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t resId;
    uint32_t resCnt = 1;
    for (int32_t i = start_id; i < end_id; i++) {
        ret = GmcSetPoolIdResource(AUTO_POOL_ID, &resId);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetCountResource(resCnt, &resId);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetStartIdxResource(AUTO_START_IDX, &resId);
        EXPECT_EQ(GMERR_OK, ret);
        obj.F0 = (int64_t)i;                                           // int64   8
        obj.F1 = (uint64_t)i + 0xFFFFFFFF;                             // uint64  8
        obj.F2 = i;                                                    // int32   4
        obj.F3 = i;                                                    // uint32  4
        obj.F4 = i & 0x7FFF;                                           // int16   2
        obj.F5 = i & 0xFFFF;                                           // uint16  2
        obj.F6 = i & 0x7F;                                             // int8    1
        obj.F7 = i & 0xFF;                                             // uint8   1
        obj.F8 = 0;                                                    // boolean 1
        obj.F9 = i;                                                    // float   4
        obj.F10 = i;                                                   // double  8
        obj.F11 = i + 0xFFFFFFFF;                                      // time    8
        obj.F12 = 'a' + (i & 0x1A);                                    // char    1  1A=26
        obj.F13 = 'A' + (i & 0x1A);                                    // uchar   1
        snprintf((char *)obj.F14, sizeof(obj.F14), "aaaaaaa%08d", i);  // fixed  16
        obj.F15 = i & 0xF;                                             // partition  1
        obj.F16 = resId;
        ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return ret;
}

uint32_t g_start_index = RECORD_COUNT / 2;
void replace_vertex_callback_with_resource(
    void *userData, uint32_t affectedRows, GmcResourceInfoT *resInfo, int32_t status, const char *errMsg)
{
    int ret;
    for (uint32_t i = 0; i < resInfo->resIdNum; i++) {
        uint16_t count = 1;
        uint32_t startIndex = 0;
        ret = GmcGetCountResource(resInfo->resIdBuf[i], &count);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStartIdxResource(resInfo->resIdBuf[i], &startIndex);

        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1U, count);
        EXPECT_EQ(g_start_index++, startIndex);
    }

    //原有的回调
    AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
    user_data->status = status;
    user_data->affectRows = affectedRows;
    user_data->recvNum++;
}

// 异步结构化写带有资源字段的简单表
int func_struct_replace_resources_async(GmcStmtT *stmt, int32_t start_id, int32_t end_id)
{
    int ret = 0;
    TEST_T4_struct_t obj = (TEST_T4_struct_t){0};
    TestLabelInfoT labelInfo = {label_name06, 0, g_testNameSpace};
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name06, GMC_OPERATION_REPLACE_WITH_RESOURCE);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t resId;
    uint32_t resCnt = 1;
    for (int32_t i = start_id; i < end_id; i++) {
        ret = GmcSetPoolIdResource(AUTO_POOL_ID, &resId);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetCountResource(resCnt, &resId);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetStartIdxResource(AUTO_START_IDX, &resId);
        EXPECT_EQ(GMERR_OK, ret);
        obj.F0 = (int64_t)i;                                           // int64   8
        obj.F1 = (uint64_t)i + 0xFFFFFFFF;                             // uint64  8
        obj.F2 = i;                                                    // int32   4
        obj.F3 = i;                                                    // uint32  4
        obj.F4 = i & 0x7FFF;                                           // int16   2
        obj.F5 = i & 0xFFFF;                                           // uint16  2
        obj.F6 = i & 0x7F;                                             // int8    1
        obj.F7 = i & 0xFF;                                             // uint8   1
        obj.F8 = 0;                                                    // boolean 1
        obj.F9 = i;                                                    // float   4
        obj.F10 = i;                                                   // double  8
        obj.F11 = i + 0xFFFFFFFF;                                      // time    8
        obj.F12 = 'a' + (i & 0x1A);                                    // char    1  1A=26
        obj.F13 = 'A' + (i & 0x1A);                                    // uchar   1
        snprintf((char *)obj.F14, sizeof(obj.F14), "aaaaaaa%08d", i);  // fixed  16
        obj.F15 = i & 0xF;                                             // partition  1
        obj.F16 = resId;
        ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT RequestCtx;
        RequestCtx.replaceWithResourceCb = replace_vertex_callback_with_resource;
        RequestCtx.userData = &data;
        ret = GmcExecuteAsync(stmt, &RequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }
    return ret;
}

void sn_callback(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret;
    int index, i;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;
    SnUserDataT *user_data = (SnUserDataT *)userData;

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        if (ret != GMERR_OK && info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
            printf("[info]GMC_SUB_EVENT_INITIAL_LOAD_EOF\n");
            user_data->scanEofNum++;
            break;
        } else if (eof == true) {
            break;
        }

        for (i = 0; i < info->labelCount; i++) {
            // i 始终是 0
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(strlen(labelName), labelNameLen);

            //推送new object和old object
            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    //读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    TEST_LOG("GMC_SUB_EVENT_INSERT", index, 1000, 0);
                    // 聚簇容器变更，不关注顺序
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    //读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->old_value)[user_data->subIndex];
                    TEST_LOG("GMC_SUB_EVENT_DELETE", index, 1000, 0);
                    // 聚簇容器变更，不关注顺序
                    break;
                }
                case GMC_SUB_EVENT_UPDATE: {
                    //读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    TEST_LOG("GMC_SUB_EVENT_UPDATE new_value", index, 1000, 0);

                    //读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->old_value)[user_data->subIndex];
                    TEST_LOG("GMC_SUB_EVENT_UPDATE old_value", index, 1000, 0);

                    break;
                }
                case GMC_SUB_EVENT_REPLACE: {
                    //读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    TEST_LOG("GMC_SUB_EVENT_REPLACE new_value", index, 1000, 0);
                    // 聚簇容器变更，不关注顺序

                    //读old
                    if (((bool *)user_data->isReplace_insert)[user_data->subIndex]) {
                        TEST_LOG("GMC_SUB_EVENT_REPLACE insert old_value", index, 1000, 0);
                        ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                        EXPECT_EQ(GMERR_OK, ret);
                        // 聚簇容器变更，不关注顺序
                    } else {
                        ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                        EXPECT_EQ(GMERR_OK, ret);
                        index = ((int *)user_data->old_value)[user_data->subIndex];
                        TEST_LOG("GMC_SUB_EVENT_REPLACE update old_value", index, 1000, 0);
                    }
                    break;
                }
                case GMC_SUB_EVENT_INITIAL_LOAD: {
                    //读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    TEST_LOG("GMC_SUB_EVENT_INITIAL_LOAD", index, 1000, 0);
                    // 聚簇容器变更，不关注顺序
                    break;
                }
                default: {
                    printf("default: invalid eventType\r\n");
                    break;
                }
            }
            break;
        }
        user_data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
        }
    }
}

void sn_callback_count(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret;
    int index, i;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;
    SnUserDataT *user_data = (SnUserDataT *)userData;

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        if (ret != GMERR_OK && info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
            printf("[info]GMC_SUB_EVENT_INITIAL_LOAD_EOF\n");
            user_data->scanEofNum++;
            break;
        } else if (eof == true) {
            break;
        }
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
        }
    }
}

#ifdef __cplusplus
}
#endif
#endif
