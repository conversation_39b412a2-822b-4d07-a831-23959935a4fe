/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2022. All rights reserved.
 Description  : dalalog支持QRY_DML_INFO视图统计
 Notes        : 001.不带过滤字段的,json打印
                C(1,b,c,d,e,f）:-A(a,b,c,d,e,1)：B(a,b,c,d,e,f),)
                可更新表A自然连接普通表B,向A插入一条数据投影到B查视图,插入多条数据查视图

                002.带过滤字段的,flat_full打印
                C(1,b,c,d,e,f）:-A(a,b,c,d,e,1)：B(a,b,c,d,e,f),)
                可更新表A自然连接普通表B,向A插入一条数据投影到B查视图,插入多条数据查视图

                003.带过滤字段的,flat_truncate打印
                C(1,b,c,d,e,f）:-A(a,b,c,d,e,1)：B(a,b,c,d,e,f),)
                可更新表A自然连接普通表B,向A插入一条数据投影到B查视图,插入多条数据查视图

                004.不带过滤字段的asc排序
                C(1,b,c,d,e,f）:-A(a,b,c,d,e,1)：B(a,b,c,d,e,f),)
                可更新表A自然连接普通表B,向A插入一条数据投影到B查视图,插入多条数据查视图

                005.带过滤字段的desc排序
                C(1,b,c,d,e,f）:-A(a,b,c,d,e,1)：B(a,b,c,d,e,f),)
                更新表A自然连接普通表B,向A插入一条数据投影到B查视图,插入多条数据查视图

                006.带预留连接rc
                .C(1,b,c,d,e,f）:-A(a,b,c,d,e,1)：B(a,b,c,d,e,f),)
                可更新表A自然连接普通表B,向A插入一条数据投影到B查视图,插入多条数据查视图

                007.(当前场景不支持显示开启事务已下架）开启悲观可串行事务,对datalog输入表进行批操作,查视图,事务提交,查视图

                008.(当前场景不支持显示开启事务已下架）开启悲观可串行事务,对datalog输入表表进行批操作,查视图,事务回滚,查视图

                009.hpr线性表含忽略,写输入表,查视图

                010.hpr哈希表含常量,写输入表,查视图

                011.ipv4 hpr表含join,写输入表,查视图

                012.ipv6 hpr表忽略,常量,join,写输入表,查视图

                013.读hpr表,一直写输入表,不结束,直到rcu内存写满

 History      :
 Author       : youwanyong ywx1157510
 Modification : 2022/10/22
**************************************************************************** */
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <atomic>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "QRY_DML_INFO_interface.h"

class DatalogSupportView_QRY_DML_INFO_function : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalShmSize=4096\" \"maxSysShmSize=512\" "
               "\"maxTotalDynSize=16000\" \"maxSysDynSize=4096\"");
        system("sh ${TEST_HOME}/tools/stop.sh");
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);

        // 创建epoll监听线程
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret;
        ret = close_epoll_thread();
        EXPECT_EQ(0, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        system("sh ${TEST_HOME}/tools/stop.sh");
        system("sh ${TEST_HOME}/tools/start.sh");
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void DatalogSupportView_QRY_DML_INFO_function::SetUp()
{
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so

    int ret;
    // 创建连接

    g_conn = NULL;
    g_stmt = NULL;
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 修改配置项
    system(" gmadmin -cfgName enableDmlOperStat -cfgval 1");
    system(" gmadmin -cfgName enableDmlPerfStat -cfgval 1");
    AW_CHECK_LOG_BEGIN();
}
void DatalogSupportView_QRY_DML_INFO_function::TearDown()
{
    AW_CHECK_LOG_END();
    int ret;
    // 修改配置项
    system(" gmadmin -cfgName enableDmlPerfStat -cfgval 0");
    // 断开同步连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 001.不带过滤字段的,json打印
                C(1,b,c,d,e,f）:-A(a,b,c,d,e,1),B(a,b,c,d,e,f),)
                可更新表A自然连接普通表B,向A插入一条数据投影到B查视图,插入多条数据查视图
 Author       : youwanyong
**************************************************************************** */
TEST_F(DatalogSupportView_QRY_DML_INFO_function, DFX_061_Function_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *expect = NULL;
    int hundredArray[100][7] = {0};
    for (int i = 0; i < 100; i++) {
        for (int j = 0; j < 7; j++) {
            hundredArray[i][j] = i + 1;
            if (i == 0 && j == 6) {
                hundredArray[i][j] = -1;
            }
        }
    }
    char nsName[128] = "function";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char tableA1[] = "A1";
    char tableB1[] = "B1";
    char tableC1[] = "C1";
    /**************************************第一次向表A1插入一条数据查询视图**************************************************/
    AW_FUN_Log(LOG_STEP, "1.第一次向表A1插入一条数据查询视图");
    int32_t count1[][7] = {{1, 1, 1, 1, 1, 1, 1}};
    // 向A1输入表写入数据
    ret = batchFunctionA1OrB1ForDtlQueue(g_stmt, tableA1, count1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待消息处理完成查询数据
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // scan C1
    int record1 = 0;
    int32_t result1[][7] = {{1, 1, 1, 1, 1, 1, 1}};
    readOutFunctionC1(g_stmt, tableC1, record1, result1);

    // 校验数据的正确性
    ret = CheckDataMatchFunction(record1, result1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufFunctionTest, 0, 100);

    // 查询QRY_DML_INFO视图
    queryView("A1", "DML_TYPE: INSERT_VERTEX", "PREPARE_COUNT: 1", "EXECUTE_COUNT: 1");
    queryView("B1", "DML_TYPE: INSERT_VERTEX", "PREPARE_COUNT: 1", "EXECUTE_COUNT: 0");
    queryView("C1", "DML_TYPE: INSERT_VERTEX", "PREPARE_COUNT: 1", "EXECUTE_COUNT: 0");

    /**************************************第一次向表B1插入一条数据查询视图**************************************************/
    AW_FUN_Log(LOG_STEP, "2.第一次向表B1插入一条数据查询视图");
    int32_t count2[][7] = {{1, 1, 1, 1, 1, 1, 1}};

    // 向A1输入表写入数据
    ret = batchFunctionA1OrB1ForDtlQueue(g_stmt, tableB1, count2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待消息处理完成查询数据
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // scan C1
    int record2 = 1;
    int32_t result2[][7] = {{1, 1, 1, 1, 1, 1, 1}};
    readOutFunctionC1(g_stmt, tableC1, record2, result2);

    // 校验数据的正确性
    ret = CheckDataMatchFunction(record2, result2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufFunctionTest, 0, 100);

    // 查询QRY_DML_INFO视图
    queryView("A1", "DML_TYPE: INSERT_VERTEX", "PREPARE_COUNT: 2", "EXECUTE_COUNT: 1");
    queryView("B1", "DML_TYPE: INSERT_VERTEX", "PREPARE_COUNT: 2", "EXECUTE_COUNT: 1");
    queryView("C1", "DML_TYPE: INSERT_VERTEX", "PREPARE_COUNT: 2", "EXECUTE_COUNT: 0");

    /**************************************再向表A1插入100条数据数据查询视图**************************************************/
    AW_FUN_Log(LOG_STEP, "3.再向表A1插入100条数据数据查询视图");  // A表为空
    // 向A1输入表写入数据
    ret = batchFunctionA1OrB1ForDtlQueue(g_stmt, tableA1, hundredArray, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待消息处理完成 查询数据
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    // scan C1
    int record3 = 0;
    int32_t result3[][7] = {};
    readOutFunctionC1(g_stmt, tableC1, record3, result3);

    // 校验数据的正确性
    ret = CheckDataMatchFunction(record3, result3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufFunctionTest, 0, 100);

    // 查询QRY_DML_INFO视图
    AW_FUN_Log(LOG_STEP, "视图校验");
    queryView("A1", "DML_TYPE: INSERT_VERTEX", "PREPARE_COUNT: 3", "EXECUTE_COUNT: 2");
    queryView("B1", "DML_TYPE: INSERT_VERTEX", "PREPARE_COUNT: 3", "EXECUTE_COUNT: 1");
    queryView("C1", "DML_TYPE: INSERT_VERTEX", "PREPARE_COUNT: 3", "EXECUTE_COUNT: 0");

    /**************************************再向表B1插入100条数据数据查询视图**************************************************/
    AW_FUN_Log(LOG_STEP, "4.再向表B1插入100条数据数据查询视图");  // B表有99条数据
    // 向A1输入表写入数据
    ret = batchFunctionA1OrB1ForDtlQueue(g_stmt, tableB1, hundredArray, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待消息处理完成 查询数据
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // scan C1
    int record4 = 0;
    int32_t result4[][7] = {};
    readOutFunctionC1(g_stmt, tableC1, record4, result4);

    // 校验数据的正确性
    ret = CheckDataMatchFunction(record4, result4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufFunctionTest, 0, 100);

    // 查询QRY_DML_INFO视图
    AW_FUN_Log(LOG_STEP, "视图校验");
    queryView("A1", "DML_TYPE: INSERT_VERTEX", "PREPARE_COUNT: 4", "EXECUTE_COUNT: 2");
    queryView("B1", "DML_TYPE: INSERT_VERTEX", "PREPARE_COUNT: 4", "EXECUTE_COUNT: 2");
    queryView("C1", "DML_TYPE: INSERT_VERTEX", "PREPARE_COUNT: 4", "EXECUTE_COUNT: 0");

    AW_FUN_Log(LOG_STEP, "向表A1 插入一百条可过滤数据");
    // 向表A 插入一百条可过滤数据
    for (int i = 0; i < 100; i++) {
        hundredArray[i][5] = 1;
    }

    /**************************************再向表A1插入100条可过滤数据查询视图**************************************************/
    // C(1,b,c,d,e,f）:-A(a,b,c,d,e,1),B(a,b,c,d,e,f),)
    AW_FUN_Log(LOG_STEP, "5.再向表A1插入100条可过滤数据查询视图");

    // 此时A表含有100条数据
    // 向A1输入表写入数据C(1,b,c,d,e,f）:-A(a,b,c,d,e,1),B(a,b,c,d,e,f),)
    ret = batchFunctionA1OrB1ForDtlQueue(g_stmt, tableA1, hundredArray, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待消息处理完成 查询数据
    // 预期A表和B表会产生99条join数据写进c表
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    int record5 = 99;
    // join后数据f值变为B表值 a值为1
    for (int i = 0; i < 100; i++) {
        hundredArray[i][5] = i + 1;
        hundredArray[i][0] = 1;
        hundredArray[i][6] = 1;
    }
    int32_t result5[99][7] = {};
    for (int i = 0; i < 99; i++) {
        for (int j = 0; j < 7; j++) {
            result5[i][j] = hundredArray[i + 1][j];
        }
    }
    // scan C1
    readOutFunctionC1(g_stmt, tableC1, record5, result5);

    // 校验数据的正确性
    ret = CheckDataMatchFunction(record5, result5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufFunctionTest, 0, 100);

    // 查询QRY_DML_INFO视图
    queryView("A1", "DML_TYPE: INSERT_VERTEX", "PREPARE_COUNT: 5", "EXECUTE_COUNT: 3");
    queryView("B1", "DML_TYPE: INSERT_VERTEX", "PREPARE_COUNT: 5", "EXECUTE_COUNT: 2");
    queryView("C1", "DML_TYPE: INSERT_VERTEX", "PREPARE_COUNT: 5", "EXECUTE_COUNT: 0");
    // 向表A 插入一百条可过滤数据
    for (int i = 0; i < 100; i++) {
        hundredArray[i][6] = -(i + 1);
        hundredArray[i][0] = i + 1;
    }
    /**************************************向表B1 插入一百条数据**************************************************/
    AW_FUN_Log(LOG_STEP, "6.向表B1 插入一百条数据");
    // 向A1输入表写入数据

    ret = batchFunctionA1OrB1ForDtlQueue(g_stmt, tableB1, hundredArray, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待消息处理完成 查询数据
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // scan C1
    int record6 = 0;
    int32_t result6[1][7] = {};
    // 预期表C1
    readOutFunctionC1(g_stmt, tableC1, record6, result6);

    // 校验数据的正确性
    ret = CheckDataMatchFunction(record6, result6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufFunctionTest, 0, 100);

    // 查询QRY_DML_INFO视图
    AW_FUN_Log(LOG_STEP, "视图校验");
    queryView("A1", "DML_TYPE: INSERT_VERTEX", "PREPARE_COUNT: 6", "EXECUTE_COUNT: 3");
    queryView("B1", "DML_TYPE: INSERT_VERTEX", "PREPARE_COUNT: 6", "EXECUTE_COUNT: 3");
    queryView("C1", "DML_TYPE: INSERT_VERTEX", "PREPARE_COUNT: 6", "EXECUTE_COUNT: 0");

    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 002.带过滤字段的,flat_full打印
                C(1,b,c,d,e,f）:-A(a,b,c,d,e,1)：B(a,b,c,d,e,f),)
                可更新表A自然连接普通表B,向A插入一条数据投影到B查视图,插入多条数据查视图
 Author       : youwanyong
**************************************************************************** */
TEST_F(DatalogSupportView_QRY_DML_INFO_function, DFX_061_Function_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *expect = NULL;
    int hundredArray[100][7] = {0};
    for (int i = 0; i < 100; i++) {
        for (int j = 0; j < 7; j++) {
            hundredArray[i][j] = i + 1;
            if (i == 0 && j == 6) {
                hundredArray[i][j] = -1;
            }
        }
    }
    char nsName[128] = "function";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char tableA1[] = "A1";
    char tableB1[] = "B1";
    char tableC1[] = "C1";
    /**************************************第一次向表A1插入一条数据查询视图**************************************************/
    AW_FUN_Log(LOG_STEP, "1.第一次向表A1插入一条数据查询视图");
    int32_t count1[][7] = {{1, 1, 1, 1, 1, 1, 1}};
    // 向A1输入表写入数据
    ret = batchFunctionA1OrB1ForDtlQueue(g_stmt, tableA1, count1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待消息处理完成查询数据
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // scan C1
    int record1 = 0;
    int32_t result1[][7] = {};
    readOutFunctionC1(g_stmt, tableC1, record1, result1);

    // 校验数据的正确性
    ret = CheckDataMatchFunction(record1, result1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufFunctionTest, 0, 100);

    /**************************************第一次向表B1插入一条数据查询视图**************************************************/
    AW_FUN_Log(LOG_STEP, "2.第一次向表B1插入一条数据查询视图");
    int32_t count2[][7] = {{1, 1, 1, 1, 1, 1, 1}};

    // 向A1输入表写入数据
    ret = batchFunctionA1OrB1ForDtlQueue(g_stmt, tableB1, count2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待消息处理完成查询数据
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // scan C1
    int record2 = 1;
    int32_t result2[][7] = {{1, 1, 1, 1, 1, 1, 1}};
    readOutFunctionC1(g_stmt, tableC1, record2, result2);

    // 校验数据的正确性
    ret = CheckDataMatchFunction(record2, result2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufFunctionTest, 0, 100);

    /**************************************再向表A1插入100条数据数据查询视图**************************************************/
    AW_FUN_Log(LOG_STEP, "3.再向表A1插入100条数据数据查询视图");  // A表为空
    // 向A1输入表写入数据
    ret = batchFunctionA1OrB1ForDtlQueue(g_stmt, tableA1, hundredArray, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待消息处理完成 查询数据
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    // scan C1
    int record3 = 0;
    int32_t result3[][7] = {};
    readOutFunctionC1(g_stmt, tableC1, record3, result3);

    // 校验数据的正确性
    ret = CheckDataMatchFunction(record3, result3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufFunctionTest, 0, 100);

    /**************************************再向表B1插入100条数据数据查询视图**************************************************/
    AW_FUN_Log(LOG_STEP, "4.再向表B1插入100条数据数据查询视图");  // B表有99条数据
    // 向A1输入表写入数据
    ret = batchFunctionA1OrB1ForDtlQueue(g_stmt, tableB1, hundredArray, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待消息处理完成 查询数据
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // scan C1
    int record4 = 0;
    int32_t result4[][7] = {};
    readOutFunctionC1(g_stmt, tableC1, record4, result4);

    // 校验数据的正确性
    ret = CheckDataMatchFunction(record4, result4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufFunctionTest, 0, 100);

    AW_FUN_Log(LOG_STEP, "向表A1 插入一百条可过滤数据");
    // 向表A 插入一百条可过滤数据
    for (int i = 0; i < 100; i++) {
        hundredArray[i][5] = 1;
    }

    /**************************************再向表A1插入100条可过滤数据查询视图**************************************************/
    // C(1,b,c,d,e,f）:-A(a,b,c,d,e,1),B(a,b,c,d,e,f),)
    AW_FUN_Log(LOG_STEP, "5.再向表A1插入100条可过滤数据查询视图");

    // 此时A表含有100条数据
    // 向A1输入表写入数据C(1,b,c,d,e,f）:-A(a,b,c,d,e,1),B(a,b,c,d,e,f),)
    ret = batchFunctionA1OrB1ForDtlQueue(g_stmt, tableA1, hundredArray, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待消息处理完成 查询数据
    // 预期A表和B表会产生99条join数据写进c表
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    int record5 = 99;
    // join后数据f值变为B表值 a值为1
    for (int i = 0; i < 100; i++) {
        hundredArray[i][5] = i + 1;
        hundredArray[i][0] = 1;
        hundredArray[i][6] = 1;
    }
    int32_t result5[99][7] = {};
    for (int i = 0; i < 99; i++) {
        for (int j = 0; j < 7; j++) {
            result5[i][j] = hundredArray[i + 1][j];
        }
    }
    // scan C1
    readOutFunctionC1(g_stmt, tableC1, record5, result5);

    // 校验数据的正确性
    ret = CheckDataMatchFunction(record5, result5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufFunctionTest, 0, 100);

    // 向表A 插入一百条可过滤数据
    for (int i = 0; i < 100; i++) {
        hundredArray[i][6] = -(i + 1);
        hundredArray[i][0] = i + 1;
    }
    /**************************************向表B1 插入一百条数据**************************************************/
    AW_FUN_Log(LOG_STEP, "6.向表B1 插入一百条数据");
    // 向A1输入表写入数据

    ret = batchFunctionA1OrB1ForDtlQueue(g_stmt, tableB1, hundredArray, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待消息处理完成 查询数据
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // scan C1
    int record6 = 0;
    int32_t result6[1][7] = {};
    // 预期表C1
    readOutFunctionC1(g_stmt, tableC1, record6, result6);

    // 校验数据的正确性
    ret = CheckDataMatchFunction(record6, result6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufFunctionTest, 0, 100);

    // 查询QRY_DML_INFO视图
    AW_FUN_Log(LOG_STEP, "视图校验");
    char expectResult[200] = "";
    sprintf(expectResult, "sysview get records unsucc, ret = %d", GMERR_FEATURE_NOT_SUPPORTED);
    expextViewResultLog(expectResult, 2, 63488);

    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 003.带过滤字段的,flat_truncate打印
                C(1,b,c,d,e,f）:-A(a,b,c,d,e,1)：B(a,b,c,d,e,f),)
                可更新表A自然连接普通表B,向A插入一条数据投影到B查视图,插入多条数据查视图
 Author       : youwanyong
**************************************************************************** */
TEST_F(DatalogSupportView_QRY_DML_INFO_function, DFX_061_Function_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *expect = NULL;
    int hundredArray[100][7] = {0};
    for (int i = 0; i < 100; i++) {
        for (int j = 0; j < 7; j++) {
            hundredArray[i][j] = i + 1;
            if (i == 0 && j == 6) {
                hundredArray[i][j] = -1;
            }
        }
    }
    char nsName[128] = "function";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char tableA1[] = "A1";
    char tableB1[] = "B1";
    char tableC1[] = "C1";
    /**************************************第一次向表A1插入一条数据查询视图**************************************************/
    AW_FUN_Log(LOG_STEP, "1.第一次向表A1插入一条数据查询视图");
    int32_t count1[][7] = {{1, 1, 1, 1, 1, 1, 1}};
    // 向A1输入表写入数据
    ret = batchFunctionA1OrB1ForDtlQueue(g_stmt, tableA1, count1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待消息处理完成查询数据
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // scan C1
    int record1 = 0;
    int32_t result1[][7] = {};
    readOutFunctionC1(g_stmt, tableC1, record1, result1);

    // 校验数据的正确性
    ret = CheckDataMatchFunction(record1, result1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufFunctionTest, 0, 100);

    /**************************************第一次向表B1插入一条数据查询视图**************************************************/
    AW_FUN_Log(LOG_STEP, "2.第一次向表B1插入一条数据查询视图");
    int32_t count2[][7] = {{1, 1, 1, 1, 1, 1, 1}};

    // 向A1输入表写入数据
    ret = batchFunctionA1OrB1ForDtlQueue(g_stmt, tableB1, count2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待消息处理完成查询数据
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // scan C1
    int record2 = 1;
    int32_t result2[][7] = {{1, 1, 1, 1, 1, 1, 1}};
    readOutFunctionC1(g_stmt, tableC1, record2, result2);

    // 校验数据的正确性
    ret = CheckDataMatchFunction(record2, result2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufFunctionTest, 0, 100);

    /**************************************再向表A1插入100条数据数据查询视图**************************************************/
    AW_FUN_Log(LOG_STEP, "3.再向表A1插入100条数据数据查询视图");  // A表为空
    // 向A1输入表写入数据
    ret = batchFunctionA1OrB1ForDtlQueue(g_stmt, tableA1, hundredArray, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待消息处理完成 查询数据
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    // scan C1
    int record3 = 0;
    int32_t result3[][7] = {};
    readOutFunctionC1(g_stmt, tableC1, record3, result3);

    // 校验数据的正确性
    ret = CheckDataMatchFunction(record3, result3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufFunctionTest, 0, 100);

    /**************************************再向表B1插入100条数据数据查询视图**************************************************/
    AW_FUN_Log(LOG_STEP, "4.再向表B1插入100条数据数据查询视图");  // B表有99条数据
    // 向A1输入表写入数据
    ret = batchFunctionA1OrB1ForDtlQueue(g_stmt, tableB1, hundredArray, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待消息处理完成 查询数据
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // scan C1
    int record4 = 0;
    int32_t result4[][7] = {};
    readOutFunctionC1(g_stmt, tableC1, record4, result4);

    // 校验数据的正确性
    ret = CheckDataMatchFunction(record4, result4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufFunctionTest, 0, 100);

    AW_FUN_Log(LOG_STEP, "向表A1 插入一百条可过滤数据");
    // 向表A 插入一百条可过滤数据
    for (int i = 0; i < 100; i++) {
        hundredArray[i][5] = 1;
    }

    /**************************************再向表A1插入100条可过滤数据查询视图**************************************************/
    // C(1,b,c,d,e,f）:-A(a,b,c,d,e,1),B(a,b,c,d,e,f),)
    AW_FUN_Log(LOG_STEP, "5.再向表A1插入100条可过滤数据查询视图");

    // 此时A表含有100条数据
    // 向A1输入表写入数据C(1,b,c,d,e,f）:-A(a,b,c,d,e,1),B(a,b,c,d,e,f),)
    ret = batchFunctionA1OrB1ForDtlQueue(g_stmt, tableA1, hundredArray, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待消息处理完成 查询数据
    // 预期A表和B表会产生99条join数据写进c表
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    int record5 = 99;
    // join后数据f值变为B表值 a值为1
    for (int i = 0; i < 100; i++) {
        hundredArray[i][5] = i + 1;
        hundredArray[i][0] = 1;
        hundredArray[i][6] = 1;
    }
    int32_t result5[99][7] = {};
    for (int i = 0; i < 99; i++) {
        for (int j = 0; j < 7; j++) {
            result5[i][j] = hundredArray[i + 1][j];
        }
    }
    // scan C1
    readOutFunctionC1(g_stmt, tableC1, record5, result5);

    // 校验数据的正确性
    ret = CheckDataMatchFunction(record5, result5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufFunctionTest, 0, 100);

    // 向表A 插入一百条可过滤数据
    for (int i = 0; i < 100; i++) {
        hundredArray[i][6] = -(i + 1);
        hundredArray[i][0] = i + 1;
    }
    /**************************************向表B1 插入一百条数据**************************************************/
    AW_FUN_Log(LOG_STEP, "6.向表B1 插入一百条数据");
    // 向A1输入表写入数据

    ret = batchFunctionA1OrB1ForDtlQueue(g_stmt, tableB1, hundredArray, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待消息处理完成 查询数据
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // scan C1
    int record6 = 0;
    int32_t result6[1][7] = {};
    // 预期表C1
    readOutFunctionC1(g_stmt, tableC1, record6, result6);

    // 校验数据的正确性
    ret = CheckDataMatchFunction(record6, result6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufFunctionTest, 0, 100);

    // 查询QRY_DML_INFO视图
    AW_FUN_Log(LOG_STEP, "视图校验");
    char expectResult[200] = "";
    sprintf(expectResult, "sysview get records unsucc, ret = %d", GMERR_FEATURE_NOT_SUPPORTED);
    expextViewResultLog(expectResult, 3, 63488);

    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 004.不带过滤字段的asc排序
                C(1,b,c,d,e,f）:-A(a,b,c,d,e,1)：B(a,b,c,d,e,f),)
                可更新表A自然连接普通表B,向A插入一条数据投影到B查视图,插入多条数据查视图

 Author       : youwanyong
**************************************************************************** */
TEST_F(DatalogSupportView_QRY_DML_INFO_function, DFX_061_Function_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *expect = NULL;
    int hundredArray[100][7] = {0};
    for (int i = 0; i < 100; i++) {
        for (int j = 0; j < 7; j++) {
            hundredArray[i][j] = i + 1;
            if (i == 0 && j == 6) {
                hundredArray[i][j] = -1;
            }
        }
    }
    char nsName[128] = "function";
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char tableA1[] = "A1";
    char tableB1[] = "B1";
    char tableC1[] = "C1";
    /**************************************第一次向表A1插入一条数据查询视图**************************************************/
    AW_FUN_Log(LOG_STEP, "1.第一次向表A1插入一条数据查询视图");
    int32_t count1[][7] = {{1, 1, 1, 1, 1, 1, 1}};
    // 向A1输入表写入数据
    ret = batchFunctionA1OrB1ForDtlQueue(g_stmt, tableA1, count1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待消息处理完成查询数据
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // scan C1
    int record1 = 0;
    int32_t result1[][7] = {};
    readOutFunctionC1(g_stmt, tableC1, record1, result1);

    // 校验数据的正确性
    ret = CheckDataMatchFunction(record1, result1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufFunctionTest, 0, 100);

    /**************************************第一次向表B1插入一条数据查询视图**************************************************/
    AW_FUN_Log(LOG_STEP, "2.第一次向表B1插入一条数据查询视图");
    int32_t count2[][7] = {{1, 1, 1, 1, 1, 1, 1}};

    // 向A1输入表写入数据
    ret = batchFunctionA1OrB1ForDtlQueue(g_stmt, tableB1, count2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待消息处理完成查询数据
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // scan C1
    int record2 = 1;
    int32_t result2[][7] = {{1, 1, 1, 1, 1, 1, 1}};
    readOutFunctionC1(g_stmt, tableC1, record2, result2);

    // 校验数据的正确性
    ret = CheckDataMatchFunction(record2, result2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufFunctionTest, 0, 100);

    /**************************************再向表A1插入100条数据数据查询视图**************************************************/
    AW_FUN_Log(LOG_STEP, "3.再向表A1插入100条数据数据查询视图");  // A表为空
    // 向A1输入表写入数据
    ret = batchFunctionA1OrB1ForDtlQueue(g_stmt, tableA1, hundredArray, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待消息处理完成 查询数据
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    // scan C1
    int record3 = 0;
    int32_t result3[][7] = {};
    readOutFunctionC1(g_stmt, tableC1, record3, result3);

    // 校验数据的正确性
    ret = CheckDataMatchFunction(record3, result3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufFunctionTest, 0, 100);

    /**************************************再向表B1插入100条数据数据查询视图**************************************************/
    AW_FUN_Log(LOG_STEP, "4.再向表B1插入100条数据数据查询视图");  // B表有99条数据
    // 向A1输入表写入数据
    ret = batchFunctionA1OrB1ForDtlQueue(g_stmt, tableB1, hundredArray, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待消息处理完成 查询数据
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // scan C1
    int record4 = 0;
    int32_t result4[][7] = {};
    readOutFunctionC1(g_stmt, tableC1, record4, result4);

    // 校验数据的正确性
    ret = CheckDataMatchFunction(record4, result4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufFunctionTest, 0, 100);

    AW_FUN_Log(LOG_STEP, "向表A1 插入一百条可过滤数据");
    // 向表A 插入一百条可过滤数据
    for (int i = 0; i < 100; i++) {
        hundredArray[i][5] = 1;
    }

    /**************************************再向表A1插入100条可过滤数据查询视图**************************************************/
    // C(1,b,c,d,e,f）:-A(a,b,c,d,e,1),B(a,b,c,d,e,f),)
    AW_FUN_Log(LOG_STEP, "5.再向表A1插入100条可过滤数据查询视图");

    // 此时A表含有100条数据
    // 向A1输入表写入数据C(1,b,c,d,e,f）:-A(a,b,c,d,e,1),B(a,b,c,d,e,f),)
    ret = batchFunctionA1OrB1ForDtlQueue(g_stmt, tableA1, hundredArray, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待消息处理完成 查询数据
    // 预期A表和B表会产生99条join数据写进c表
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    int record5 = 99;
    // join后数据f值变为B表值 a值为1
    for (int i = 0; i < 100; i++) {
        hundredArray[i][5] = i + 1;
        hundredArray[i][0] = 1;
        hundredArray[i][6] = 1;
    }
    int32_t result5[99][7] = {};
    for (int i = 0; i < 99; i++) {
        for (int j = 0; j < 7; j++) {
            result5[i][j] = hundredArray[i + 1][j];
        }
    }
    // scan C1
    readOutFunctionC1(g_stmt, tableC1, record5, result5);

    // 校验数据的正确性
    ret = CheckDataMatchFunction(record5, result5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufFunctionTest, 0, 100);

    // 向表A 插入一百条可过滤数据
    for (int i = 0; i < 100; i++) {
        hundredArray[i][6] = -(i + 1);
        hundredArray[i][0] = i + 1;
    }
    /**************************************向表B1 插入一百条数据**************************************************/
    AW_FUN_Log(LOG_STEP, "6.向表B1 插入一百条数据");
    // 向A1输入表写入数据

    ret = batchFunctionA1OrB1ForDtlQueue(g_stmt, tableB1, hundredArray, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待消息处理完成 查询数据
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // scan C1
    int record6 = 0;
    int32_t result6[1][7] = {};
    // 预期表C1
    readOutFunctionC1(g_stmt, tableC1, record6, result6);

    // 校验数据的正确性
    ret = CheckDataMatchFunction(record6, result6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufFunctionTest, 0, 100);

    // 查询QRY_DML_INFO视图
    AW_FUN_Log(LOG_STEP, "视图校验");
    char expectResult[200] = "";
    expextViewResultLog(expectResult, 0, 0, false, 1);

    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 005.带过滤字段的desc排序
                C(1,b,c,d,e,f）:-A(a,b,c,d,e,1)：B(a,b,c,d,e,f),)
                更新表A自然连接普通表B,向A插入一条数据投影到B查视图,插入多条数据查视图

 Author       : youwanyong
**************************************************************************** */
TEST_F(DatalogSupportView_QRY_DML_INFO_function, DFX_061_Function_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *expect = NULL;
    int hundredArray[100][7] = {0};
    for (int i = 0; i < 100; i++) {
        for (int j = 0; j < 7; j++) {
            hundredArray[i][j] = i + 1;
            if (i == 0 && j == 6) {
                hundredArray[i][j] = -1;
            }
        }
    }
    char nsName[128] = "function";
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char tableA1[] = "A1";
    char tableB1[] = "B1";
    char tableC1[] = "C1";
    /**************************************第一次向表A1插入一条数据查询视图**************************************************/
    AW_FUN_Log(LOG_STEP, "1.第一次向表A1插入一条数据查询视图");
    int32_t count1[][7] = {{1, 1, 1, 1, 1, 1, 1}};
    // 向A1输入表写入数据
    ret = batchFunctionA1OrB1ForDtlQueue(g_stmt, tableA1, count1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待消息处理完成查询数据
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // scan C1
    int record1 = 0;
    int32_t result1[][7] = {};
    readOutFunctionC1(g_stmt, tableC1, record1, result1);

    // 校验数据的正确性
    ret = CheckDataMatchFunction(record1, result1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufFunctionTest, 0, 100);

    /**************************************第一次向表B1插入一条数据查询视图**************************************************/
    AW_FUN_Log(LOG_STEP, "2.第一次向表B1插入一条数据查询视图");
    int32_t count2[][7] = {{1, 1, 1, 1, 1, 1, 1}};

    // 向A1输入表写入数据
    ret = batchFunctionA1OrB1ForDtlQueue(g_stmt, tableB1, count2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待消息处理完成查询数据
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // scan C1
    int record2 = 1;
    int32_t result2[][7] = {{1, 1, 1, 1, 1, 1, 1}};
    readOutFunctionC1(g_stmt, tableC1, record2, result2);

    // 校验数据的正确性
    ret = CheckDataMatchFunction(record2, result2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufFunctionTest, 0, 100);

    /**************************************再向表A1插入100条数据数据查询视图**************************************************/
    AW_FUN_Log(LOG_STEP, "3.再向表A1插入100条数据数据查询视图");  // A表为空
    // 向A1输入表写入数据
    ret = batchFunctionA1OrB1ForDtlQueue(g_stmt, tableA1, hundredArray, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待消息处理完成 查询数据
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    // scan C1
    int record3 = 0;
    int32_t result3[][7] = {};
    readOutFunctionC1(g_stmt, tableC1, record3, result3);

    // 校验数据的正确性
    ret = CheckDataMatchFunction(record3, result3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufFunctionTest, 0, 100);

    /**************************************再向表B1插入100条数据数据查询视图**************************************************/
    AW_FUN_Log(LOG_STEP, "4.再向表B1插入100条数据数据查询视图");  // B表有99条数据
    // 向A1输入表写入数据
    ret = batchFunctionA1OrB1ForDtlQueue(g_stmt, tableB1, hundredArray, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待消息处理完成 查询数据
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // scan C1
    int record4 = 0;
    int32_t result4[][7] = {};
    readOutFunctionC1(g_stmt, tableC1, record4, result4);

    // 校验数据的正确性
    ret = CheckDataMatchFunction(record4, result4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufFunctionTest, 0, 100);

    AW_FUN_Log(LOG_STEP, "向表A1 插入一百条可过滤数据");
    // 向表A 插入一百条可过滤数据
    for (int i = 0; i < 100; i++) {
        hundredArray[i][5] = 1;
    }

    /**************************************再向表A1插入100条可过滤数据查询视图**************************************************/
    // C(1,b,c,d,e,f）:-A(a,b,c,d,e,1),B(a,b,c,d,e,f),)
    AW_FUN_Log(LOG_STEP, "5.再向表A1插入100条可过滤数据查询视图");

    // 此时A表含有100条数据
    // 向A1输入表写入数据C(1,b,c,d,e,f）:-A(a,b,c,d,e,1),B(a,b,c,d,e,f),)
    ret = batchFunctionA1OrB1ForDtlQueue(g_stmt, tableA1, hundredArray, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待消息处理完成 查询数据
    // 预期A表和B表会产生99条join数据写进c表
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    int record5 = 99;
    // join后数据f值变为B表值 a值为1
    for (int i = 0; i < 100; i++) {
        hundredArray[i][5] = i + 1;
        hundredArray[i][0] = 1;
        hundredArray[i][6] = 1;
    }
    int32_t result5[99][7] = {};
    for (int i = 0; i < 99; i++) {
        for (int j = 0; j < 7; j++) {
            result5[i][j] = hundredArray[i + 1][j];
        }
    }
    // scan C1
    readOutFunctionC1(g_stmt, tableC1, record5, result5);

    // 校验数据的正确性
    ret = CheckDataMatchFunction(record5, result5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufFunctionTest, 0, 100);

    // 向表A 插入一百条可过滤数据
    for (int i = 0; i < 100; i++) {
        hundredArray[i][6] = -(i + 1);
        hundredArray[i][0] = i + 1;
    }
    /**************************************向表B1 插入一百条数据**************************************************/
    AW_FUN_Log(LOG_STEP, "6.向表B1 插入一百条数据");
    // 向A1输入表写入数据

    ret = batchFunctionA1OrB1ForDtlQueue(g_stmt, tableB1, hundredArray, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待消息处理完成 查询数据
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // scan C1
    int record6 = 0;
    int32_t result6[1][7] = {};
    // 预期表C1
    readOutFunctionC1(g_stmt, tableC1, record6, result6);

    // 校验数据的正确性
    ret = CheckDataMatchFunction(record6, result6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufFunctionTest, 0, 100);

    // 查询QRY_DML_INFO视图
    AW_FUN_Log(LOG_STEP, "视图校验");
    char expectResult[200] = "";
    expextViewResultLog(expectResult, 0, 0, false, 2);

    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 006.带预留连接rc
                .C(1,b,c,d,e,f）:-A(a,b,c,d,e,1)：B(a,b,c,d,e,f),)
                可更新表A自然连接普通表B,向A插入一条数据投影到B查视图,插入多条数据查视图

 Author       : youwanyong
**************************************************************************** */
TEST_F(DatalogSupportView_QRY_DML_INFO_function, DFX_061_Function_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *expect = NULL;
    int hundredArray[100][7] = {0};
    for (int i = 0; i < 100; i++) {
        for (int j = 0; j < 7; j++) {
            hundredArray[i][j] = i + 1;
            if (i == 0 && j == 6) {
                hundredArray[i][j] = -1;
            }
        }
    }
    char nsName[128] = "function";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // 修改配置项 导入用户白名单
    system(" gmrule -c import_allowlist -f ./datalog_file/root.gmuser ");
    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char tableA1[] = "A1";
    char tableB1[] = "B1";
    char tableC1[] = "C1";
    /**************************************第一次向表A1插入一条数据查询视图**************************************************/
    AW_FUN_Log(LOG_STEP, "1.第一次向表A1插入一条数据查询视图");
    int32_t count1[][7] = {{1, 1, 1, 1, 1, 1, 1}};
    // 向A1输入表写入数据
    ret = batchFunctionA1OrB1ForDtlQueue(g_stmt, tableA1, count1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待消息处理完成查询数据
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // scan C1
    int record1 = 0;
    int32_t result1[][7] = {};
    readOutFunctionC1(g_stmt, tableC1, record1, result1);

    // 校验数据的正确性
    ret = CheckDataMatchFunction(record1, result1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufFunctionTest, 0, 100);

    /**************************************第一次向表B1插入一条数据查询视图**************************************************/
    AW_FUN_Log(LOG_STEP, "2.第一次向表B1插入一条数据查询视图");
    int32_t count2[][7] = {{1, 1, 1, 1, 1, 1, 1}};

    // 向A1输入表写入数据
    ret = batchFunctionA1OrB1ForDtlQueue(g_stmt, tableB1, count2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待消息处理完成查询数据
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // scan C1
    int record2 = 1;
    int32_t result2[][7] = {{1, 1, 1, 1, 1, 1, 1}};
    readOutFunctionC1(g_stmt, tableC1, record2, result2);

    // 校验数据的正确性
    ret = CheckDataMatchFunction(record2, result2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufFunctionTest, 0, 100);

    /**************************************再向表A1插入100条数据数据查询视图**************************************************/
    AW_FUN_Log(LOG_STEP, "3.再向表A1插入100条数据数据查询视图");  // A表为空
    // 向A1输入表写入数据
    ret = batchFunctionA1OrB1ForDtlQueue(g_stmt, tableA1, hundredArray, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待消息处理完成 查询数据
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    // scan C1
    int record3 = 0;
    int32_t result3[][7] = {};
    readOutFunctionC1(g_stmt, tableC1, record3, result3);

    // 校验数据的正确性
    ret = CheckDataMatchFunction(record3, result3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufFunctionTest, 0, 100);

    /**************************************再向表B1插入100条数据数据查询视图**************************************************/
    AW_FUN_Log(LOG_STEP, "4.再向表B1插入100条数据数据查询视图");  // B表有99条数据
    // 向A1输入表写入数据
    ret = batchFunctionA1OrB1ForDtlQueue(g_stmt, tableB1, hundredArray, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待消息处理完成 查询数据
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // scan C1
    int record4 = 0;
    int32_t result4[][7] = {};
    readOutFunctionC1(g_stmt, tableC1, record4, result4);

    // 校验数据的正确性
    ret = CheckDataMatchFunction(record4, result4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufFunctionTest, 0, 100);

    AW_FUN_Log(LOG_STEP, "向表A1 插入一百条可过滤数据");
    // 向表A 插入一百条可过滤数据
    for (int i = 0; i < 100; i++) {
        hundredArray[i][5] = 1;
    }

    /**************************************再向表A1插入100条可过滤数据查询视图**************************************************/
    // C(1,b,c,d,e,f）:-A(a,b,c,d,e,1),B(a,b,c,d,e,f),)
    AW_FUN_Log(LOG_STEP, "5.再向表A1插入100条可过滤数据查询视图");

    // 此时A表含有100条数据
    // 向A1输入表写入数据C(1,b,c,d,e,f）:-A(a,b,c,d,e,1),B(a,b,c,d,e,f),)
    ret = batchFunctionA1OrB1ForDtlQueue(g_stmt, tableA1, hundredArray, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待消息处理完成 查询数据
    // 预期A表和B表会产生99条join数据写进c表
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    int record5 = 99;
    // join后数据f值变为B表值 a值为1
    for (int i = 0; i < 100; i++) {
        hundredArray[i][5] = i + 1;
        hundredArray[i][0] = 1;
        hundredArray[i][6] = 1;
    }
    int32_t result5[99][7] = {};
    for (int i = 0; i < 99; i++) {
        for (int j = 0; j < 7; j++) {
            result5[i][j] = hundredArray[i + 1][j];
        }
    }
    // scan C1
    readOutFunctionC1(g_stmt, tableC1, record5, result5);

    // 校验数据的正确性
    ret = CheckDataMatchFunction(record5, result5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufFunctionTest, 0, 100);

    // 向表A 插入一百条可过滤数据
    for (int i = 0; i < 100; i++) {
        hundredArray[i][6] = -(i + 1);
        hundredArray[i][0] = i + 1;
    }
    /**************************************向表B1 插入一百条数据**************************************************/
    AW_FUN_Log(LOG_STEP, "6.向表B1 插入一百条数据");
    // 向A1输入表写入数据

    ret = batchFunctionA1OrB1ForDtlQueue(g_stmt, tableB1, hundredArray, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待消息处理完成 查询数据
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // scan C1
    int record6 = 0;
    int32_t result6[1][7] = {};
    // 预期表C1
    readOutFunctionC1(g_stmt, tableC1, record6, result6);

    // 校验数据的正确性
    ret = CheckDataMatchFunction(record6, result6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufFunctionTest, 0, 100);

    // 查询QRY_DML_INFO视图
    AW_FUN_Log(LOG_STEP, "视图校验");
    const char *expect6 = R"(C1)";
    expextViewResultLog(expect6, 0, 0, false, 3);

    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
    // 删除用户白名单
    system(" gmrule -c remove_allowlist -f ./datalog_file/root.gmuser ");
}

/* ****************************************************************************
 Description  : 007.开启悲观可串行事务,对datalog输入表进行批操作,查视图,事务提交,查视图
 Author       : youwanyong
**************************************************************************** */
TEST_F(DatalogSupportView_QRY_DML_INFO_function, DISABLED_DFX_061_Function_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *expect = NULL;
    int hundredArray[100][7] = {0};
    for (int i = 0; i < 100; i++) {
        for (int j = 0; j < 7; j++) {
            hundredArray[i][j] = i + 1;
            if (i == 0 && j == 6) {
                hundredArray[i][j] = -1;
            }
        }
    }
    char nsName[128] = "function";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char tableA1[] = "A1";
    char tableB1[] = "B1";
    char tableC1[] = "C1";

    // 开启悲观可串行事务
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_SERIALIZABLE;
    config.readOnly = false;
    config.trxType = GMC_PESSIMISITIC_TRX;
    // 开启事务
    ret = GmcTransStart(g_conn, &config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    /**************************************第一次向表A1插入一条数据查询视图**************************************************/
    AW_FUN_Log(LOG_STEP, "1.第一次向表A1插入一条数据查询视图");
    int32_t count1[][7] = {{1, 1, 1, 1, 1, 1, 1}};
    // 向A1输入表写入数据
    ret = batchFunctionA1OrB1(g_stmt, tableA1, count1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待消息处理完成查询数据
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // scan C1
    int record1 = 0;
    int32_t result1[][7] = {};
    readOutFunctionC1(g_stmt, tableC1, record1, result1);

    // 校验数据的正确性
    ret = CheckDataMatchFunction(record1, result1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufFunctionTest, 0, 100);

    /**************************************第一次向表B1插入一条数据查询视图**************************************************/
    AW_FUN_Log(LOG_STEP, "2.第一次向表B1插入一条数据查询视图");
    int32_t count2[][7] = {{1, 1, 1, 1, 1, 1, 1}};

    // 向A1输入表写入数据
    ret = batchFunctionA1OrB1(g_stmt, tableB1, count2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待消息处理完成查询数据
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // scan C1
    int record2 = 1;
    int32_t result2[][7] = {{1, 1, 1, 1, 1, 1, 1}};
    readOutFunctionC1(g_stmt, tableC1, record2, result2);

    // 校验数据的正确性
    ret = CheckDataMatchFunction(record2, result2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufFunctionTest, 0, 100);

    /**************************************再向表A1插入100条数据数据查询视图**************************************************/
    AW_FUN_Log(LOG_STEP, "3.再向表A1插入100条数据数据查询视图");  // A表为空
    // 向A1输入表写入数据
    ret = batchFunctionA1OrB1(g_stmt, tableA1, hundredArray, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待消息处理完成 查询数据
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    // scan C1
    int record3 = 0;
    int32_t result3[][7] = {};
    readOutFunctionC1(g_stmt, tableC1, record3, result3);

    // 校验数据的正确性
    ret = CheckDataMatchFunction(record3, result3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufFunctionTest, 0, 100);

    /**************************************再向表B1插入100条数据数据查询视图**************************************************/
    AW_FUN_Log(LOG_STEP, "4.再向表B1插入100条数据数据查询视图");  // B表有99条数据
    // 向A1输入表写入数据
    ret = batchFunctionA1OrB1(g_stmt, tableB1, hundredArray, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待消息处理完成 查询数据
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // scan C1
    int record4 = 0;
    int32_t result4[][7] = {};
    readOutFunctionC1(g_stmt, tableC1, record4, result4);

    // 校验数据的正确性
    ret = CheckDataMatchFunction(record4, result4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufFunctionTest, 0, 100);

    AW_FUN_Log(LOG_STEP, "向表A1 插入一百条可过滤数据");
    // 向表A 插入一百条可过滤数据
    for (int i = 0; i < 100; i++) {
        hundredArray[i][5] = 1;
    }

    /**************************************再向表A1插入100条可过滤数据查询视图**************************************************/
    // C(1,b,c,d,e,f）:-A(a,b,c,d,e,1),B(a,b,c,d,e,f),)
    AW_FUN_Log(LOG_STEP, "5.再向表A1插入100条可过滤数据查询视图");

    // 此时A表含有100条数据
    // 向A1输入表写入数据C(1,b,c,d,e,f）:-A(a,b,c,d,e,1),B(a,b,c,d,e,f),)
    ret = batchFunctionA1OrB1(g_stmt, tableA1, hundredArray, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待消息处理完成 查询数据
    // 预期A表和B表会产生99条join数据写进c表
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    int record5 = 99;
    // join后数据f值变为B表值 a值为1
    for (int i = 0; i < 100; i++) {
        hundredArray[i][5] = i + 1;
        hundredArray[i][0] = 1;
        hundredArray[i][6] = 1;
    }
    int32_t result5[99][7] = {};
    for (int i = 0; i < 99; i++) {
        for (int j = 0; j < 7; j++) {
            result5[i][j] = hundredArray[i + 1][j];
        }
    }
    // scan C1
    readOutFunctionC1(g_stmt, tableC1, record5, result5);

    // 校验数据的正确性
    ret = CheckDataMatchFunction(record5, result5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufFunctionTest, 0, 100);

    // 向表A 插入一百条可过滤数据
    for (int i = 0; i < 100; i++) {
        hundredArray[i][6] = -(i + 1);
        hundredArray[i][0] = i + 1;
    }
    /**************************************向表B1 插入一百条数据**************************************************/
    AW_FUN_Log(LOG_STEP, "6.向表B1 插入一百条数据");
    // 向A1输入表写入数据

    ret = batchFunctionA1OrB1(g_stmt, tableB1, hundredArray, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待消息处理完成 查询数据
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // 事务提交
    ret = GmcTransCommit(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan C1
    int record6 = 0;
    int32_t result6[1][7] = {};
    // 预期表C1
    readOutFunctionC1(g_stmt, tableC1, record6, result6);

    // 校验数据的正确性
    ret = CheckDataMatchFunction(record6, result6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufFunctionTest, 0, 100);

    // 查询QRY_DML_INFO视图
    AW_FUN_Log(LOG_STEP, "视图校验");
    queryView("A1", "DML_TYPE: INSERT_VERTEX", "PREPARE_COUNT: 6", "EXECUTE_COUNT: 3");
    queryView("B1", "DML_TYPE: INSERT_VERTEX", "PREPARE_COUNT: 6", "EXECUTE_COUNT: 3");
    queryView("C1", "DML_TYPE: INSERT_VERTEX", "PREPARE_COUNT: 6", "EXECUTE_COUNT: 6");

    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 008.开启悲观可串行事务,对datalog输入表表进行批操作,查视图,事务回滚,查视图
 Author       : youwanyong
**************************************************************************** */
TEST_F(DatalogSupportView_QRY_DML_INFO_function, DISABLED_DFX_061_Function_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *expect = NULL;
    int hundredArray[100][7] = {0};
    for (int i = 0; i < 100; i++) {
        for (int j = 0; j < 7; j++) {
            hundredArray[i][j] = i + 1;
            if (i == 0 && j == 6) {
                hundredArray[i][j] = -1;
            }
        }
    }
    char nsName[128] = "function";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char tableA1[] = "A1";
    char tableB1[] = "B1";
    char tableC1[] = "C1";

    // 开启悲观可重复读事务
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_SERIALIZABLE;
    config.readOnly = false;
    config.trxType = GMC_PESSIMISITIC_TRX;
    // 开启事务
    ret = GmcTransStart(g_conn, &config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    /**************************************第一次向表A1插入一条数据查询视图**************************************************/
    AW_FUN_Log(LOG_STEP, "1.第一次向表A1插入一条数据查询视图");
    int32_t count1[][7] = {{1, 1, 1, 1, 1, 1, 1}};
    // 向A1输入表写入数据
    ret = batchFunctionA1OrB1(g_stmt, tableA1, count1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待消息处理完成查询数据
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // scan C1
    int record1 = 0;
    int32_t result1[][7] = {};
    readOutFunctionC1(g_stmt, tableC1, record1, result1);

    // 校验数据的正确性
    ret = CheckDataMatchFunction(record1, result1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufFunctionTest, 0, 100);

    /**************************************第一次向表B1插入一条数据查询视图**************************************************/
    AW_FUN_Log(LOG_STEP, "2.第一次向表B1插入一条数据查询视图");
    int32_t count2[][7] = {{1, 1, 1, 1, 1, 1, 1}};

    // 向A1输入表写入数据
    ret = batchFunctionA1OrB1(g_stmt, tableB1, count2, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待消息处理完成查询数据
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // scan C1
    int record2 = 1;
    int32_t result2[][7] = {{1, 1, 1, 1, 1, 1, 1}};
    readOutFunctionC1(g_stmt, tableC1, record2, result2);

    // 校验数据的正确性
    ret = CheckDataMatchFunction(record2, result2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufFunctionTest, 0, 100);

    /**************************************再向表A1插入100条数据数据查询视图**************************************************/
    AW_FUN_Log(LOG_STEP, "3.再向表A1插入100条数据数据查询视图");  // A表为空
    // 向A1输入表写入数据
    ret = batchFunctionA1OrB1(g_stmt, tableA1, hundredArray, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待消息处理完成 查询数据
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    // scan C1
    int record3 = 0;
    int32_t result3[][7] = {};
    readOutFunctionC1(g_stmt, tableC1, record3, result3);

    // 校验数据的正确性
    ret = CheckDataMatchFunction(record3, result3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufFunctionTest, 0, 100);

    /**************************************再向表B1插入100条数据数据查询视图**************************************************/
    AW_FUN_Log(LOG_STEP, "4.再向表B1插入100条数据数据查询视图");  // B表有99条数据
    // 向A1输入表写入数据
    ret = batchFunctionA1OrB1(g_stmt, tableB1, hundredArray, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待消息处理完成 查询数据
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    // scan C1
    int record4 = 0;
    int32_t result4[][7] = {};
    readOutFunctionC1(g_stmt, tableC1, record4, result4);

    // 校验数据的正确性
    ret = CheckDataMatchFunction(record4, result4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufFunctionTest, 0, 100);

    AW_FUN_Log(LOG_STEP, "向表A1 插入一百条可过滤数据");
    // 向表A 插入一百条可过滤数据
    for (int i = 0; i < 100; i++) {
        hundredArray[i][5] = 1;
    }

    /**************************************再向表A1插入100条可过滤数据查询视图**************************************************/
    // C(1,b,c,d,e,f）:-A(a,b,c,d,e,1),B(a,b,c,d,e,f),)
    AW_FUN_Log(LOG_STEP, "5.再向表A1插入100条可过滤数据查询视图");

    // 此时A表含有100条数据
    // 向A1输入表写入数据C(1,b,c,d,e,f）:-A(a,b,c,d,e,1),B(a,b,c,d,e,f),)
    ret = batchFunctionA1OrB1(g_stmt, tableA1, hundredArray, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待消息处理完成 查询数据
    // 预期A表和B表会产生99条join数据写进c表
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    int record5 = 99;
    // join后数据f值变为B表值 a值为1
    for (int i = 0; i < 100; i++) {
        hundredArray[i][5] = i + 1;
        hundredArray[i][0] = 1;
        hundredArray[i][6] = 1;
    }
    int32_t result5[99][7] = {};
    for (int i = 0; i < 99; i++) {
        for (int j = 0; j < 7; j++) {
            result5[i][j] = hundredArray[i + 1][j];
        }
    }
    // scan C1
    readOutFunctionC1(g_stmt, tableC1, record5, result5);

    // 校验数据的正确性
    ret = CheckDataMatchFunction(record5, result5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufFunctionTest, 0, 100);

    // 向表A 插入一百条可过滤数据
    for (int i = 0; i < 100; i++) {
        hundredArray[i][6] = -(i + 1);
        hundredArray[i][0] = i + 1;
    }
    /**************************************向表B1 插入一百条数据**************************************************/
    AW_FUN_Log(LOG_STEP, "6.向表B1 插入一百条数据");
    // 向A1输入表写入数据

    ret = batchFunctionA1OrB1(g_stmt, tableB1, hundredArray, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待消息处理完成 查询数据
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    // 事务回滚
    ret = GmcTransRollBack(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan C1
    int record6 = 0;
    int32_t result6[1][7] = {};
    // 预期表C1
    readOutFunctionC1(g_stmt, tableC1, record6, result6);

    // 校验数据的正确性
    ret = CheckDataMatchFunction(record6, result6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_bufFunctionTest, 0, 100);

    // 查询QRY_DML_INFO视图
    AW_FUN_Log(LOG_STEP, "视图校验");
    queryView("A1", "DML_TYPE: INSERT_VERTEX", "PREPARE_COUNT: 6", "EXECUTE_COUNT: 3");
    queryView("B1", "DML_TYPE: INSERT_VERTEX", "PREPARE_COUNT: 6", "EXECUTE_COUNT: 3");
    queryView("C1", "DML_TYPE: INSERT_VERTEX", "PREPARE_COUNT: 6", "EXECUTE_COUNT: 6");

    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
