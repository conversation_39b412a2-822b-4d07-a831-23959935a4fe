/*  版权所有 (c) 华为技术有限公司 2021-2022 */
#include "gm_udf.h"

#pragma pack(1)
typedef struct A {
    int32_t count;
    int32_t updateVersion;
    int32_t a;
    int32_t b;
} A;

typedef struct Func {
    int32_t count;
    int32_t a;
    int32_t b;
} Func;

#pragma pack(0)

// 读读表A
int32_t dtl_ext_func_ns1_rr_func(void *tuple, GmUdfCtxT *ctx)
{
    Func *b = (Func *)tuple;
    b->b = b->a + 1;

    // read input's input table
    GmUdfReaderT *reader = NULL;
    int32_t ret = GmUdfCreateDeltaReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }

    A *a = NULL;
    while (ret = GmUdfGetNext(reader, (void **)&a), ret == GMERR_OK) {
    }

    GmUdfDestroyReader(ctx, reader);

    ret = GmUdfCreateCurrentReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }

    while (ret = GmUdfGetNext(reader, (void **)&a), ret == GMERR_OK) {
    }

    GmUdfDestroyReader(ctx, reader);

    return GMERR_OK;
}
