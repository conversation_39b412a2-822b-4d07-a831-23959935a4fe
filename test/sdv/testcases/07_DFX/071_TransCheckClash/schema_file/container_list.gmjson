[{"type": "container", "name": "root", "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "string", "size": 7, "nullable": true}, {"type": "container", "name": "C1", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "string", "size": 7, "nullable": true}]}], "keys": [{"node": "root", "name": "PK", "fields": ["ID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "T1", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32"}, {"name": "F2", "type": "string", "size": 7, "nullable": true}, {"type": "container", "name": "P1", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "string", "size": 7, "nullable": true}]}], "keys": [{"node": "T1", "name": "PK", "fields": ["PID", "F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "T2", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32"}, {"name": "F2", "type": "string", "size": 7, "nullable": true}, {"type": "container", "name": "P2", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "string", "size": 7, "nullable": true}]}], "keys": [{"node": "T2", "name": "PK", "fields": ["PID", "F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "T3", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32"}, {"name": "F2", "type": "string", "size": 7, "nullable": true}, {"type": "container", "name": "P3", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "string", "size": 7, "nullable": true}]}], "keys": [{"node": "T3", "name": "PK", "fields": ["PID", "F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "T4", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32"}, {"name": "F2", "type": "string", "size": 7, "nullable": true}, {"type": "container", "name": "P4", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "string", "size": 7, "nullable": true}]}], "keys": [{"node": "T4", "name": "PK", "fields": ["PID", "F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "T5", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32"}, {"name": "F2", "type": "string", "size": 7, "nullable": true}, {"type": "container", "name": "P5", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "string", "size": 7, "nullable": true}]}], "keys": [{"node": "T5", "name": "PK", "fields": ["PID", "F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]