/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */
#ifndef TRANSCHECKCLASH
#define TRANSCHECKCLASH
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <string.h>
#include <string>
#include <atomic>
#include <vector>
#include <iostream>
#include <cmath>
#include <chrono>
#include <sys/time.h>
#include <sys/prctl.h>
#include <sys/socket.h>
#include <sys/epoll.h>
#include <pthread.h>
#include <assert.h>
#include <errno.h>
#include <stdint.h>
#include <typeinfo>
#include <limits.h>
#include "gtest/gtest.h"

#include "t_datacom_lite.h"
#include "jansson.h"
#define CMD_LENGTH 1024u
#define MAX_CMD_SIZE 1024
#define FILE_PATH 512
char g_outputDir[FILE_PATH] = "datalog_file";
char g_command[MAX_CMD_SIZE];
GmcConnT *g_conn_sync = NULL;
GmcStmtT *g_stmt_sync = NULL;
GmcConnT *g_conn_async = NULL;
GmcStmtT *g_stmt_async = NULL;
GmcConnT *g_conn_async1 = NULL;
GmcStmtT *g_stmt_async1 = NULL;
GmcNodeT *g_rootNode = NULL;
const char *g_keyName = "PK";
GmcNodeT *g_childNode[40] = {0};
GmcTxConfigT g_trxConfig;
GmcTxConfigT g_trxConfig1;
const char *g_namespace = (const char *)"Yang";
const char *g_nsUserName = (const char *)"root";
const char *g_msConfigJson = R"(
{
    "max_record_count":10000,
    "isFastReadUncommitted":0,
    "auto_increment":1,
    "yang_model":1
})";

const char *g_schemaJson1 = R"([ {
    "type": "record",
    "name": "A11",
    "fields": [
        {
            "name": "dtlReservedCount",
            "type": "int32"
        },
        {
            "name": "upgradeVersion",
            "type": "int32"
        },
        {
            "name": "a",
            "type": "int32"
        },
        {
            "name": "b",
            "type": "int32"
        }
    ],
    "keys": [
        {
            "name": "0",
            "index": {
                "type": "primary"
            },
            "node": "A11",
            "constraints": {
                "unique": true
            },
            "fields": [
                "upgradeVersion",
                "a",
                "b"
            ]
        }
    ]
}])";

const char *g_schemaJson2 = R"([ {
    "type": "record",
    "name": "B11",
    "fields": [
        {
            "name": "dtlReservedCount",
            "type": "int32"
        },
        {
            "name": "upgradeVersion",
            "type": "int32"
        },
        {
            "name": "a",
            "type": "int32"
        },
        {
            "name": "b",
            "type": "int32"
        }
    ],
    "keys": [
        {
            "name": "0",
            "index": {
                "type": "primary"
            },
            "node": "B11",
            "constraints": {
                "unique": true
            },
            "fields": [
                "upgradeVersion",
                "a",
                "b"
            ]
        }
    ]
}])";

const char *g_schemaJson3 = R"([ {
    "type": "record",
    "name": "A11B11",
    "fields": [
        {
            "name": "dtlReservedCount",
            "type": "int32"
        },
        {
            "name": "upgradeVersion",
            "type": "int32"
        },
        {
            "name": "a",
            "type": "int32"
        },
        {
            "name": "b",
            "type": "int32"
        },
        {
            "name": "c",
            "type": "int32"
        },
        {
            "name": "d",
            "type": "int32"
        }
    ],
    "keys": [
        {
            "name": "0",
            "index": {
                "type": "primary"
            },
            "node": "A11B11",
            "constraints": {
                "unique": true
            },
            "fields": [
                "upgradeVersion",
                "a",
                "b",
                "c",
                "d"
            ]
        }
    ]
}])";

const char *g_labelconfig = "{\"max_record_count\" : 10000000000, \"isFastReadUncommitted\":0}";
#define MAX_LABELNAME_LEN 128
bool gIsSnCallbackWait = false;
static int32_t InsertVertex(GmcStmtT *stmt, int32_t indexValue, int32_t value)
{
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &indexValue, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    return ret;
}
static int32_t InsertBigObjVertex(GmcStmtT *stmt, int32_t indexValue, int32_t value, uint64_t strValue = 10,
    const char *p = "a")
{
    int ret = 0;
    char str[1024 * strValue];
    memset(str, 0x00, 1024 * strValue * sizeof(char));
    for (int i = 0; i < 1023 * strValue; i++) {
        strcat(str, p);
    }
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &indexValue, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_STRING, str, strlen(str));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_STRING, str, strlen(str));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_STRING, str, strlen(str));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_STRING, str, strlen(str));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_STRING, str, strlen(str));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_STRING, str, strlen(str));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_STRING, str, strlen(str));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_STRING, str, strlen(str));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, str, strlen(str));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    return ret;
}
static int32_t InsertVertexAsync(GmcStmtT *stmt, int32_t indexValue, int32_t value)
{
    int ret = 0;
    AsyncUserDataT data = {0};
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &indexValue, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcAsyncRequestDoneContextT replaceRequestCtx;
    replaceRequestCtx.insertCb = replace_vertex_callback;
    replaceRequestCtx.userData = &data;
    ret = GmcExecuteAsync(stmt, &replaceRequestCtx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    return data.status;
}
static int32_t InsertBigObjVertexAsync(GmcStmtT *stmt, int32_t indexValue, int32_t value, uint64_t strValue = 10,
    const char *p = "a")
{
    int ret = 0;
    AsyncUserDataT data = {0};
    char str[1024 * strValue];
    memset(str, 0x00, 1024 * strValue * sizeof(char));
    for (int i = 0; i < 1023 * strValue; i++) {
        strcat(str, p);
    }
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &indexValue, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_STRING, str, strlen(str));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_STRING, str, strlen(str));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_STRING, str, strlen(str));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_STRING, str, strlen(str));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_STRING, str, strlen(str));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_STRING, str, strlen(str));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_STRING, str, strlen(str));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_STRING, str, strlen(str));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, str, strlen(str));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcAsyncRequestDoneContextT replaceRequestCtx;
    replaceRequestCtx.insertCb = replace_vertex_callback;
    replaceRequestCtx.userData = &data;
    ret = GmcExecuteAsync(stmt, &replaceRequestCtx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    return data.status;
}
static int32_t testTransStartAsync(GmcConnT *conn, GmcTxConfigT config)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = GmcTransStartAsync(conn, &config, trans_start_callback, &data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return data.status;
}
static int32_t testTransCommitAsync(GmcConnT *conn)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = GmcTransCommitAsync(conn, trans_commit_callback, &data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return data.status;
}

static int32_t testTransRollBackAsync(GmcConnT *conn)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = GmcTransRollBackAsync(conn, trans_rollback_callback, &data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return data.status;
}

#ifdef FEATURE_YANG
static int32_t testBatchPrepareAndSetDiff(GmcConnT *conn, GmcBatchT **batch, GmcBatchTypeE batchType = GMC_BATCH_YANG,
    GmcYangDiffTypeE diffType = GMC_YANG_DIFF_OFF)
{
    int ret = 0;
    GmcBatchOptionT batchOption;

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, batchType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBatchOptionSetDiffType(&batchOption, diffType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}
#endif

static int32_t testBatchExecuteAndWait(GmcBatchT *batch, AsyncUserDataT data, int totalNum, int succNum)
{
    int ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecv(&data);
    if (data.status == GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(totalNum, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(succNum, data.succNum);
    }
    return data.status;
}
static void GetViewFieldResultFilter(
    const char *viewName, const char *fieldName, const char *filter, char *result, int resLen)
{
    char command[1024];
    if (filter) {
        (void)snprintf_s(command, CMD_LENGTH, CMD_LENGTH - 1,
            "gmsysview -q %s -f %s |grep -E '%s' |awk -F '[:,]' '{print $2}'", viewName, filter, fieldName);
        AW_FUN_Log(LOG_INFO, "%s\n", command);
    } else {
        (void)snprintf_s(command, CMD_LENGTH, CMD_LENGTH - 1,
            "gmsysview -q %s |grep -E '%s' |awk -F '[:,]' '{print $2}'", viewName, fieldName);
        AW_FUN_Log(LOG_INFO, "%s\n", command);
    }
    FILE *pf = popen(command, "r");
    if (pf == NULL) {
        AW_FUN_Log(LOG_INFO, "popen(%s) error./n", command);
    }
    EXPECT_NE((void *)NULL, pf);
    while (fgets(result, resLen, pf) != NULL) {
    }
    int ret = pclose(pf);
    if (ret == -1) {
        AW_FUN_Log(LOG_INFO, "fclose error\n");
        return;
    }
}
static int32_t PresetVertexData()
{
    int ret;
    return ret;
}
static void CreateTree_Container_List(GmcStmtT *stmt, AsyncUserDataT data)
{
    char *vertexSchema = NULL;
    char *edgeSchema = NULL;
    int ret;

    readJanssonFile("schema_file/container_list.gmjson", &vertexSchema);
    AW_MACRO_ASSERT_NOTNULL(vertexSchema);
    readJanssonFile("schema_file/container_list_edge.gmjson", &edgeSchema);
    AW_MACRO_ASSERT_NOTNULL(edgeSchema);

    ret = GmcCreateVertexLabelAsync(stmt, vertexSchema, g_msConfigJson, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcCreateEdgeLabelAsync(stmt, edgeSchema, g_msConfigJson, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    free(edgeSchema);
    free(vertexSchema);
}

static void testInitPropValue(GmcPropValueT *propValue, const char *name, GmcDataTypeE type, void *value, uint32_t size)
{
    if (propValue == NULL) {
        return;
    }
    strcpy_s(propValue->propertyName, strlen(name) + 1, name);
    propValue->type = type;
    propValue->size = size;
    propValue->value = (void *)value;
}

#ifdef FEATURE_YANG
static int32_t testYangSetNodeProperty(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE opType, bool isList = false)
{
    int ret = 0;
    GmcPropValueT propValue = {0};
    uint32_t valueF0 = i;
    uint32_t valueF1 = i;
    char valueF2[8] = "string";
    if (!isList) {
        testInitPropValue(&propValue, "F0", GMC_DATATYPE_UINT32, &valueF0, sizeof(valueF0));
        ret = GmcYangSetNodeProperty(node, &propValue, opType);
        if (ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            testGmcGetLastError();
            return ret;
        }
    }
    testInitPropValue(&propValue, "F1", GMC_DATATYPE_UINT32, &valueF1, sizeof(valueF1));
    ret = GmcYangSetNodeProperty(node, &propValue, opType);
    if (ret) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    testInitPropValue(&propValue, "F2", GMC_DATATYPE_STRING, valueF2, strlen(valueF2));
    ret = GmcYangSetNodeProperty(node, &propValue, opType);
    if (ret) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    return ret;
}
static void testYangPresetDataContainerList(GmcConnT *conn, GmcStmtT *stmt, uint32_t count = 10)
{
    // 启动事务
    GmcStmtT *stmt_1 = NULL;
    GmcStmtT *stmt_2 = NULL;
    GmcStmtT *stmt_3 = NULL;
    GmcStmtT *stmt_4 = NULL;
    GmcStmtT *stmt_5 = NULL;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    int ret = testTransStartAsync(conn, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *rootNode = NULL;
    GmcNodeT *rootNode1 = NULL;
    ret = GmcGetRootNode(stmt, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    ret = testYangSetNodeProperty(rootNode, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *childNode = NULL;
    GmcNodeT *childNode1 = NULL;
    // 设置node节点T0
    ret = GmcYangEditChildNode(rootNode, "C1", GMC_OPERATION_INSERT, &childNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetNodeProperty(childNode, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < count; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, "T1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(rootNode1, "P1", GMC_OPERATION_INSERT, &childNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(childNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 批处理提交
    ret = testBatchExecuteAndWait(batch, data, count + 1, count + 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDestroy(batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = testTransCommitAsync(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(stmt_1);
    GmcFreeStmt(stmt_2);
    GmcFreeStmt(stmt_3);
    GmcFreeStmt(stmt_4);
    GmcFreeStmt(stmt_5);
}
#endif

static void DropTree_container_list(GmcStmtT *stmt, AsyncUserDataT data)
{
    int ret;

    ret = GmcDropEdgeLabelAsync(stmt, "root_to_T1", drop_edge_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropEdgeLabelAsync(stmt, "root_to_T2", drop_edge_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropEdgeLabelAsync(stmt, "root_to_T3", drop_edge_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropEdgeLabelAsync(stmt, "root_to_T4", drop_edge_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropEdgeLabelAsync(stmt, "root_to_T5", drop_edge_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcDropVertexLabelAsync(stmt, "root", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropVertexLabelAsync(stmt, "T1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropVertexLabelAsync(stmt, "T2", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropVertexLabelAsync(stmt, "T3", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropVertexLabelAsync(stmt, "T4", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropVertexLabelAsync(stmt, "T5", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
}

void *TrxVertexThread(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    AsyncUserDataT data = {0};
    int ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespaceAsync(stmt, "yang", use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcTransStartAsync(conn, &g_trxConfig, trans_start_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = testGmcPrepareStmtByLabelName(stmt, "bigobject", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 100; i++) {
        ret = InsertVertexAsync(stmt, i, 10);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcTransCheckOptimisticTrxConflictAsync(conn, trans_checkConflict_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    if (data.status == GMERR_RESTRICT_VIOLATION) {
        ret = GmcTransCommitAsync(conn, trans_commit_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, data.status);
        ret = GmcTransRollBackAsync(conn, trans_rollback_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        ret = GmcTransCommitAsync(conn, trans_commit_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        if (data.status == GMERR_RESTRICT_VIOLATION) {
            ret = GmcTransRollBackAsync(conn, trans_rollback_callback, &data);
            EXPECT_EQ(GMERR_OK, ret);
            ret = testWaitAsyncRecv(&data);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        }
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

#ifdef FEATURE_YANG
void *TrxYangThread(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmt1 = NULL;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    int ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespaceAsync(stmt, "yang", use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcTransStartAsync(conn, &g_trxConfig, trans_start_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    // 设置批处理batch参数
    ret = testBatchPrepareAndSetDiff(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *rootNode1 = NULL;
    GmcNodeT *childNode1 = NULL;
    ret = GmcGetRootNode(stmt, &rootNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点P1
    ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_NONE, &childNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *ListNode1 = NULL;
    for (int i = 0; i < 100; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt1, "T1", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt1, &ListNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt1, 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt1, "PK");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(ListNode1, 200, GMC_YANG_PROPERTY_OPERATION_MERGE, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testBatchExecuteAndWait(batch, data, 101, 101);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransCheckOptimisticTrxConflictAsync(conn, trans_checkConflict_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    if (data.status == GMERR_RESTRICT_VIOLATION) {
        ret = GmcTransCommitAsync(conn, trans_commit_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, data.status);
        ret = GmcTransRollBackAsync(conn, trans_rollback_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        ret = GmcTransCommitAsync(conn, trans_commit_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        if (data.status == GMERR_RESTRICT_VIOLATION) {
            ret = GmcTransRollBackAsync(conn, trans_rollback_callback, &data);
            EXPECT_EQ(GMERR_OK, ret);
            ret = testWaitAsyncRecv(&data);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        }
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
#endif

// B(a, b) :- A(a, b).
#pragma pack(1)
typedef struct {
    int32_t a;
    int32_t b;
    int32_t dtlReservedCount;
} TEST_INPUT_01;
#pragma pack()

void TestInput01Value(TEST_INPUT_01 *d, int value[])
{
    d->a = value[0];
    d->b = value[1];
    d->dtlReservedCount = value[2];
}

// 第二种视图校验方式
void queryView(
    const char *v1 = NULL, const char *v2 = NULL, const char *v3 = NULL, const char *v4 = NULL, const char *v5 = NULL)
{
    char *viewName1 = (char *)"V\\$QRY_DML_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q %s -f LABEL_NAME=%s", viewName1, v1);
    system(g_command);
    int ret = executeCommand(g_command, v1, v2, v3, v4, v5);
    ASSERT_EQ(GMERR_OK, ret);
}

#if defined FEATURE_DATALOG
char g_soNameTest[MAX_CMD_SIZE] = {0};
void LoadPrepare(char *fileName)
{
    (void)snprintf(g_soNameTest, MAX_CMD_SIZE, "./datalog_file/%s.so", fileName);
}
#endif

#pragma pack(1)
typedef struct {
    int32_t a;
    int32_t b;
    int32_t dtlReservedCount;
} REL_CASE_A11;
#pragma pack()
// set A value
void RelCaseA11Value(REL_CASE_A11 *d, int value[])
{
    d->a = value[0];
    d->b = value[1];
    d->dtlReservedCount = value[2];
}
// 结构化批量写 A11
int batchRelA11(GmcStmtT *stmt, char *label, char *outA, int32_t count[][3], int dataNum, int32_t value)
{
    int ret = 0;
    char schemaJson1[1024] = {0};
    char schemaJson2[1024] = {0};
    char schemaJson3[1024] = {0};
    (void)sprintf(schemaJson1, g_schemaJson1, label, label);
    ret = TestGetVertexLabelFromSchema(schemaJson1, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)sprintf(schemaJson2, g_schemaJson2, label, label);
    ret = TestGetVertexLabelFromSchema(schemaJson2, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)sprintf(schemaJson3, g_schemaJson3, label, label);
    ret = TestGetVertexLabelFromSchema(schemaJson3, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {(char *)label, 0, g_testNameSpace};
    REL_CASE_A11 objIn = (REL_CASE_A11){0};
    EXPECT_EQ(GMERR_OK, ret);
    // insert
    ret = testGmcPrepareStmtByLabelName(stmt, label, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    // prepare
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < dataNum; i++) {
        // set value
        count[i][0] = value; // 始终改变第一个字段的值
        RelCaseA11Value(&objIn, count[i]);
        ret = testStructSetVertexWithBuf(stmt, &objIn, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    return ret;
}
#endif

