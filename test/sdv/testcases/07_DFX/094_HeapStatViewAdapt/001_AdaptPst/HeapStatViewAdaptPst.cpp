/*
 * @Author: z00619264 <EMAIL>
 * @Date: 2024-03-13 15:34:17
 * @LastEditors: z00619264 <EMAIL>
 * @LastEditTime: 2024-06-28 10:12:57
 * @FilePath: \GMDBV5\test\sdv\testcases\07_DFX\094_HeapStatViewAdapt\HeapStatViewAdaptPst.cpp
 * @Description: HEAP_STAT持久化视图适配
  功能测试:
        001、按需持久化模式下查询视图V$STORAGE_HEAP_STAT
        002、按需持久化模式下，插入记录，不刷盘，重启，查询视图V$STORAGE_HEAP_STAT
        003、按需持久化模式下，插入及删除记录，刷盘，重启前后查询视图V$STORAGE_HEAP_STAT
        004、增量持久化模式下查询视图V$STORAGE_HEAP_STAT
        005、增量持久化模式下，插入记录，重启，查询视图V$STORAGE_HEAP_STAT
        006、增量持久化模式下，插入及删除记录，重启，查询视图V$STORAGE_HEAP_STAT
        007、按需持久化，并发场景下，循环查询视图V$STORAGE_HEAP_STAT
        008、增量持久化，并发场景下，循环查询视图V$STORAGE_HEAP_STAT
 */
#include "../HeapStatViewAdapt.h"

class HeapStatViewAdaptPst : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        int ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ChangeGmserverCfg((char *)"recover", NULL));
    };
public:
    virtual void SetUp();
    virtual void TearDown();
};
void HeapStatViewAdaptPst::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    char errorMsg1[128] = {};
    char errorMsg2[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_UNDEFINED_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
}

void HeapStatViewAdaptPst::TearDown()
{
    int ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_END();
}

// 001、按需持久化模式下查询视图V$STORAGE_HEAP_STAT
TEST_F(HeapStatViewAdaptPst, DFX_094_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    PersistModeChange((char *)"0");

    char *schema = NULL;
    // 创建客户端连接
    int ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建VertexLabel
    readJanssonFile("../schema_file/vl_01.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    GmcDropVertexLabel(g_stmtSync, g_generalCplxVLTableName1);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmtSync, schema, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema);
    schema = NULL;
    // 创建kv表
    readJanssonFile("../schema_file/kv_table_01.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcKvDropTable(g_stmtSync, g_kvTableName1);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = GmcKvCreateTable(g_stmtSync, g_kvTableName1, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);

    // 查询视图，校验表信息、HEAP_SEGID、HEAP_OFFSET、WRITE_BYTES、DELETE_BYTES、碎片整理等参数
    const char *metchStr[] = {\
    "LABEL_NAME: TEST_GC_VC_T1", "LABEL_TYPE: VERTEX", "LABEL_NAME: TEST_KV_T1", "LABEL_TYPE: KV", \
    "HEAP_SEGID", "HEAP_OFFSET", \
    "LATEST_DEFRAG_FAIL_CNT: 0", "LAST_DEFRAG_TASK_TIME: null", "LAST_DEFRAG_TIME: null",\
    "OPEN_CURSOR_COUNT: 0", "name: TOP3_LONG_TERM_CURSOR"};
    // 存在7张系统表，加上新增的vl表，"LABEL_TYPE: VERTEX"计数为8;
    // 存在全局kv表T_GMDB，加上新增的kv表，"LABEL_TYPE: KV"计数为2;
    // HEAP_SEGID、HEAP_OFFSET参数不再显示,计数为0;
    // 碎片整理参数持久化模式下显示为0或者null
    // 迭代三新增两张系统表 user role
    int check_num[] = {1, 10, 1, 2, 0, 0, 12, 12, 12, 12, 12};
    for(int i=0; i<sizeof(metchStr)/sizeof(metchStr[0]); i++){
        EXPECT_EQ(GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", metchStr[i]), check_num[i]);
        if(GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", metchStr[i]) != check_num[i]){
            system("gmsysview -q V\\$STORAGE_HEAP_STAT");
        }
    }
    
    //环境清理
    ret = GmcDropVertexLabel(g_stmtSync, g_generalCplxVLTableName1);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmtSync, g_kvTableName1);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("mkdir -p /data/gmdb/; rm -rf /data/gmdb/*");
    free(schema);
    schema = NULL;
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002、按需持久化模式下，插入记录，不刷盘，重启，查询视图V$STORAGE_HEAP_STAT
TEST_F(HeapStatViewAdaptPst, DFX_094_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    PersistModeChange((char *)"0");

    char *schema = NULL;
    // 创建客户端连接
    int ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创表前查询视图
    system("gmsysview -q V\\$STORAGE_HEAP_STAT");
    system("gmsysview -q V\\$DB_SERVER");
    // 创建VertexLabel
    readJanssonFile("../schema_file/vl_01.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    GmcDropVertexLabel(g_stmtSync, g_generalCplxVLTableName1);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmtSync, schema, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema);
    schema = NULL;
    // 创建kv表
    readJanssonFile("../schema_file/kv_table_01.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcKvDropTable(g_stmtSync, g_kvTableName1);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = GmcKvCreateTable(g_stmtSync, g_kvTableName1, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);

    // 查询视图，校验表信息、HEAP_SEGID、HEAP_OFFSET、WRITE_BYTES、DELETE_BYTES、碎片整理等参数
    const char *metchStr[] = {\
    "LABEL_NAME: TEST_GC_VC_T1", "LABEL_TYPE: VERTEX", "LABEL_NAME: TEST_KV_T1", "LABEL_TYPE: KV", \
    "HEAP_SEGID", "HEAP_OFFSET", \
    "LATEST_DEFRAG_FAIL_CNT: 0", "LAST_DEFRAG_TASK_TIME: null", "LAST_DEFRAG_TIME: null",\
    "OPEN_CURSOR_COUNT: 0", "name: TOP3_LONG_TERM_CURSOR"};
    // 存在7张系统表，加上新增的vl表，"LABEL_TYPE: VERTEX"计数为8;
    // 存在全局kv表T_GMDB，加上新增的kv表，"LABEL_TYPE: KV"计数为2;
    // HEAP_SEGID、HEAP_OFFSET参数不再显示,计数为0;
    // 碎片整理参数持久化模式下显示为0或者null
    // 迭代三新增两张系统表 user role
    int check_num[] = {1, 10, 1, 2, 0, 0, 12, 12, 12, 12, 12};
    for(int i=0; i<sizeof(metchStr)/sizeof(metchStr[0]); i++){
        EXPECT_EQ(GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", metchStr[i]), check_num[i]);
        if(GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", metchStr[i]) != check_num[i]){
            system("gmsysview -q V\\$STORAGE_HEAP_STAT");
        }
    }

    // vl表插入10条数据
    VlComplexRecordCtxT vertexCfg = {
        .opStart = 0,
        .opCount = 10,
        .startMkVal = 0,
        .childCount = 10,
        .labelName = "TEST_GC_VC_T1",
    };
    ret = VlComplexInsert(g_stmtSync, vertexCfg);
    ASSERT_EQ(GMERR_OK, ret);
    // kv表插入10条数据
    KvInsert(g_stmtSync, 0, 10, 0, "TEST_KV_T1");
    // 落盘
    ret = GmcFlushData(g_stmtSync, NULL, false);
    ASSERT_EQ(GMERR_OK, ret);
    // 重启
    ret = RestartAndConn(&g_connSync, &g_stmtSync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 重启后查询视图
    const char *metchStr1[] = {\
    "LABEL_NAME: TEST_GC_VC_T1", "LABEL_TYPE: VERTEX", "LABEL_NAME: TEST_KV_T1", "LABEL_TYPE: KV", \
    "HEAP_SEGID", "HEAP_OFFSET", "CUR_ITEM_NUM: 10", "PHY_ITEM_NUM: 10", \
    "LATEST_DEFRAG_FAIL_CNT: 0", "LAST_DEFRAG_TASK_TIME: null", "LAST_DEFRAG_TIME: null",\
    "OPEN_CURSOR_COUNT: 0", "name: TOP3_LONG_TERM_CURSOR"};
    // 存在7张系统表，加上新增的vl表，"LABEL_TYPE: VERTEX"计数为8;
    // 存在全局kv表T_GMDB，kv表不持久化，"LABEL_TYPE: KV"计数为1;
    // HEAP_SEGID、HEAP_OFFSET参数不再显示,计数为0;
    // 碎片整理参数持久化模式下显示为0或者null
    int check_num1[] = {1, 10, 0, 1, 0, 0, 1, 1, 11, 11, 11, 11, 11};
    for(int i=0; i<sizeof(metchStr1)/sizeof(metchStr1[0]); i++){
        EXPECT_EQ(GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", metchStr1[i]), check_num1[i]);
        if(GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", metchStr1[i]) != check_num1[i]){
            system("gmsysview -q V\\$STORAGE_HEAP_STAT");
        }
    }

    //环境清理
    ret = GmcDropVertexLabel(g_stmtSync, g_generalCplxVLTableName1);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmtSync, g_kvTableName1);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("mkdir -p /data/gmdb/; rm -rf /data/gmdb/*");
    free(schema);
    schema = NULL;
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003、按需持久化模式下，插入及删除记录，刷盘，重启前后查询视图V$STORAGE_HEAP_STAT
TEST_F(HeapStatViewAdaptPst, DFX_094_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    PersistModeChange((char *)"0");

    char *schema = NULL;
    // 创建客户端连接
    int ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建VertexLabel
    readJanssonFile("../schema_file/vl_01.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    GmcDropVertexLabel(g_stmtSync, g_generalCplxVLTableName1);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmtSync, schema, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema);
    schema = NULL;
    // 创建kv表
    readJanssonFile("../schema_file/kv_table_01.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcKvDropTable(g_stmtSync, g_kvTableName1);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = GmcKvCreateTable(g_stmtSync, g_kvTableName1, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);

    // 查询视图，校验表信息、HEAP_SEGID、HEAP_OFFSET、WRITE_BYTES、DELETE_BYTES、碎片整理等参数
    const char *metchStr[] = {\
    "LABEL_NAME: TEST_GC_VC_T1", "LABEL_TYPE: VERTEX", "LABEL_NAME: TEST_KV_T1", "LABEL_TYPE: KV", \
    "HEAP_SEGID", "HEAP_OFFSET", \
    "LATEST_DEFRAG_FAIL_CNT: 0", "LAST_DEFRAG_TASK_TIME: null", "LAST_DEFRAG_TIME: null",\
    "OPEN_CURSOR_COUNT: 0", "name: TOP3_LONG_TERM_CURSOR"};
    // 存在7张系统表，加上新增的vl表，"LABEL_TYPE: VERTEX"计数为8;
    // 存在全局kv表T_GMDB，加上新增的kv表，"LABEL_TYPE: KV"计数为2;
    // HEAP_SEGID、HEAP_OFFSET参数不再显示,计数为0;
    // 碎片整理参数持久化模式下显示为0或者null
    // 迭代三新增两张系统表 user role
    int check_num[] = {1, 10, 1, 2, 0, 0, 12, 12, 12, 12, 12};
    for(int i=0; i<sizeof(metchStr)/sizeof(metchStr[0]); i++){
        EXPECT_EQ(GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", metchStr[i]), check_num[i]);
        if(GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", metchStr[i]) != check_num[i]){
            system("gmsysview -q V\\$STORAGE_HEAP_STAT");
        }
    }

    // vl表插入10条数据
    VlComplexRecordCtxT vertexCfg = {
        .opStart = 0,
        .opCount = 10,
        .startMkVal = 0,
        .childCount = 10,
        .labelName = "TEST_GC_VC_T1",
    };
    ret = VlComplexInsert(g_stmtSync, vertexCfg);
    ASSERT_EQ(GMERR_OK, ret);
    // vl表删除5条数据
    vertexCfg.opStart = 0;
    vertexCfg.opCount = 5;
    ret = VlComplexDeleteByIndex(g_stmtSync, vertexCfg, "PrimaryKey");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // kv表插入10条数据
    KvInsert(g_stmtSync, 0, 10, 0, "TEST_KV_T1");
    // kv表删除3条数据
    KvRemove(g_stmtSync, 0, 5, 0, "TEST_KV_T1");
    // 落盘
    ret = GmcFlushData(g_stmtSync, NULL, false);
    ASSERT_EQ(GMERR_OK, ret);
    // 重启
    ret = RestartAndConn(&g_connSync, &g_stmtSync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 重启后查询视图
    const char *metchStr1[] = {\
    "LABEL_NAME: TEST_GC_VC_T1", "LABEL_TYPE: VERTEX", "LABEL_NAME: TEST_KV_T1", "LABEL_TYPE: KV", \
    "HEAP_SEGID", "HEAP_OFFSET", "CUR_ITEM_NUM: 5", "PHY_ITEM_NUM: 5", \
    "LATEST_DEFRAG_FAIL_CNT: 0", "LAST_DEFRAG_TASK_TIME: null", "LAST_DEFRAG_TIME: null",\
    "OPEN_CURSOR_COUNT: 0", "name: TOP3_LONG_TERM_CURSOR"};
    // 存在7张系统表，加上新增的vl表，"LABEL_TYPE: VERTEX"计数为8;
    // 存在全局kv表T_GMDB，kv表不持久化，"LABEL_TYPE: KV"计数为1;
    // HEAP_SEGID、HEAP_OFFSET参数不再显示,计数为0;
    // 碎片整理参数持久化模式下显示为0或者null
    int check_num1[] = {1, 10, 0, 1, 0, 0, 1, 1, 11, 11, 11, 11, 11};
    for(int i=0; i<sizeof(metchStr1)/sizeof(metchStr1[0]); i++){
        EXPECT_EQ(GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", metchStr1[i]), check_num1[i]);
        if(GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", metchStr1[i]) != check_num1[i]){
            system("gmsysview -q V\\$STORAGE_HEAP_STAT");
        }
    }

    //环境清理
    ret = GmcDropVertexLabel(g_stmtSync, g_generalCplxVLTableName1);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmtSync, g_kvTableName1);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("mkdir -p /data/gmdb/; rm -rf /data/gmdb/*");
    free(schema);
    schema = NULL;
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004、增量持久化模式下查询视图V$STORAGE_HEAP_STAT
TEST_F(HeapStatViewAdaptPst, DFX_094_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    PersistModeChange((char *)"1");

    char *schema = NULL;
    // 创建客户端连接
    int ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建VertexLabel
    readJanssonFile("../schema_file/vl_01.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    GmcDropVertexLabel(g_stmtSync, g_generalCplxVLTableName1);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmtSync, schema, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema);
    schema = NULL;
    // 创建kv表
    readJanssonFile("../schema_file/kv_table_01.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcKvDropTable(g_stmtSync, g_kvTableName1);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = GmcKvCreateTable(g_stmtSync, g_kvTableName1, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);

    // 查询视图，校验表信息、HEAP_SEGID、HEAP_OFFSET、WRITE_BYTES、DELETE_BYTES、碎片整理等参数
    const char *metchStr[] = {\
    "LABEL_NAME: TEST_GC_VC_T1", "LABEL_TYPE: VERTEX", "LABEL_NAME: TEST_KV_T1", "LABEL_TYPE: KV", \
    "HEAP_SEGID", "HEAP_OFFSET", \
    "LATEST_DEFRAG_FAIL_CNT: 0", "LAST_DEFRAG_TASK_TIME: null", "LAST_DEFRAG_TIME: null",\
    "OPEN_CURSOR_COUNT: 0", "name: TOP3_LONG_TERM_CURSOR"};
    // 存在7张系统表，加上新增的vl表，"LABEL_TYPE: VERTEX"计数为8;
    // 存在全局kv表T_GMDB，加上新增的kv表，"LABEL_TYPE: KV"计数为2;
    // HEAP_SEGID、HEAP_OFFSET参数不再显示,计数为0;
    // 碎片整理参数持久化模式下显示为0或者null
    // 迭代三新增两张系统表 user role
    int check_num[] = {1, 10, 1, 2, 0, 0, 12, 12, 12, 12, 12};
    for(int i=0; i<sizeof(metchStr)/sizeof(metchStr[0]); i++){
        EXPECT_EQ(GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", metchStr[i]), check_num[i]);
        if(GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", metchStr[i]) != check_num[i]){
            system("gmsysview -q V\\$STORAGE_HEAP_STAT");
        }
    }
    
    //环境清理
    ret = GmcDropVertexLabel(g_stmtSync, g_generalCplxVLTableName1);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmtSync, g_kvTableName1);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("mkdir -p /data/gmdb/; rm -rf /data/gmdb/*");
    free(schema);
    schema = NULL;
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005、增量持久化模式下，插入记录，重启，查询视图V$STORAGE_HEAP_STAT
TEST_F(HeapStatViewAdaptPst, DFX_094_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    PersistModeChange((char *)"1");

    char *schema = NULL;
    // 创建客户端连接
    int ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建VertexLabel
    readJanssonFile("../schema_file/vl_01.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    GmcDropVertexLabel(g_stmtSync, g_generalCplxVLTableName1);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmtSync, schema, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema);
    schema = NULL;
    // 创建kv表
    readJanssonFile("../schema_file/kv_table_01.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcKvDropTable(g_stmtSync, g_kvTableName1);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = GmcKvCreateTable(g_stmtSync, g_kvTableName1, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);

    // 查询视图，校验表信息、HEAP_SEGID、HEAP_OFFSET、WRITE_BYTES、DELETE_BYTES、碎片整理等参数
    const char *metchStr[] = {\
    "LABEL_NAME: TEST_GC_VC_T1", "LABEL_TYPE: VERTEX", "LABEL_NAME: TEST_KV_T1", "LABEL_TYPE: KV", \
    "HEAP_SEGID", "HEAP_OFFSET", \
    "LATEST_DEFRAG_FAIL_CNT: 0", "LAST_DEFRAG_TASK_TIME: null", "LAST_DEFRAG_TIME: null",\
    "OPEN_CURSOR_COUNT: 0", "name: TOP3_LONG_TERM_CURSOR"};
    // 存在7张系统表，加上新增的vl表，"LABEL_TYPE: VERTEX"计数为8;
    // 存在全局kv表T_GMDB，加上新增的kv表，"LABEL_TYPE: KV"计数为2;
    // HEAP_SEGID、HEAP_OFFSET参数不再显示,计数为0;
    // 碎片整理参数持久化模式下显示为0或者null
    // 迭代三新增两张系统表 user role
    int check_num[] = {1, 10, 1, 2, 0, 0, 12, 12, 12, 12, 12};
    for(int i=0; i<sizeof(metchStr)/sizeof(metchStr[0]); i++){
        EXPECT_EQ(GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", metchStr[i]), check_num[i]);
        if(GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", metchStr[i]) != check_num[i]){
            system("gmsysview -q V\\$STORAGE_HEAP_STAT");
        }
    }

    // vl表插入10条数据
    VlComplexRecordCtxT vertexCfg = {
        .opStart = 0,
        .opCount = 10,
        .startMkVal = 0,
        .childCount = 10,
        .labelName = "TEST_GC_VC_T1",
    };
    ret = VlComplexInsert(g_stmtSync, vertexCfg);
    ASSERT_EQ(GMERR_OK, ret);
    // kv表插入10条数据
    KvInsert(g_stmtSync, 0, 10, 0, "TEST_KV_T1");
    // 落盘
    ret = GmcFlushData(g_stmtSync, NULL, false);
    ASSERT_EQ(GMERR_OK, ret);
    // 重启
    ret = RestartAndConn(&g_connSync, &g_stmtSync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 重启后查询视图
    const char *metchStr1[] = {\
    "LABEL_NAME: TEST_GC_VC_T1", "LABEL_TYPE: VERTEX", "LABEL_NAME: TEST_KV_T1", "LABEL_TYPE: KV", \
    "HEAP_SEGID", "HEAP_OFFSET", "CUR_ITEM_NUM: 10", "PHY_ITEM_NUM: 10", \
    "LATEST_DEFRAG_FAIL_CNT: 0", "LAST_DEFRAG_TASK_TIME: null", "LAST_DEFRAG_TIME: null",\
    "OPEN_CURSOR_COUNT: 0", "name: TOP3_LONG_TERM_CURSOR"};
    // 存在7张系统表，加上新增的vl表，"LABEL_TYPE: VERTEX"计数为8;
    // 存在全局kv表T_GMDB，kv表不持久化，"LABEL_TYPE: KV"计数为1;
    // HEAP_SEGID、HEAP_OFFSET参数不再显示,计数为0;
    // 碎片整理参数持久化模式下显示为0或者null
    // 迭代三新增两张系统表 user role
    int check_num1[] = {1, 10, 0, 1, 0, 0, 1, 1, 11, 11, 11, 11, 11};
    for(int i=0; i<sizeof(metchStr1)/sizeof(metchStr1[0]); i++){
        EXPECT_EQ(GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", metchStr1[i]), check_num1[i]);
        if(GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", metchStr1[i]) != check_num1[i]){
            system("gmsysview -q V\\$STORAGE_HEAP_STAT");
        }
    }

    //环境清理
    ret = GmcDropVertexLabel(g_stmtSync, g_generalCplxVLTableName1);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmtSync, g_kvTableName1);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("mkdir -p /data/gmdb/; rm -rf /data/gmdb/*");
    free(schema);
    schema = NULL;
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006、增量持久化模式下，插入及删除记录，重启，查询视图V$STORAGE_HEAP_STAT
TEST_F(HeapStatViewAdaptPst, DFX_094_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    PersistModeChange((char *)"1");

    char *schema = NULL;
    // 创建客户端连接
    int ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建VertexLabel
    readJanssonFile("../schema_file/vl_01.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    GmcDropVertexLabel(g_stmtSync, g_generalCplxVLTableName1);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmtSync, schema, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema);
    schema = NULL;
    // 创建kv表
    readJanssonFile("../schema_file/kv_table_01.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcKvDropTable(g_stmtSync, g_kvTableName1);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = GmcKvCreateTable(g_stmtSync, g_kvTableName1, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);

    // 查询视图，校验表信息、HEAP_SEGID、HEAP_OFFSET、WRITE_BYTES、DELETE_BYTES、碎片整理等参数
    const char *metchStr[] = {\
    "LABEL_NAME: TEST_GC_VC_T1", "LABEL_TYPE: VERTEX", "LABEL_NAME: TEST_KV_T1", "LABEL_TYPE: KV", \
    "HEAP_SEGID", "HEAP_OFFSET", \
    "LATEST_DEFRAG_FAIL_CNT: 0", "LAST_DEFRAG_TASK_TIME: null", "LAST_DEFRAG_TIME: null",\
    "OPEN_CURSOR_COUNT: 0", "name: TOP3_LONG_TERM_CURSOR"};
    // 存在7张系统表，加上新增的vl表，"LABEL_TYPE: VERTEX"计数为8;
    // 存在全局kv表T_GMDB，加上新增的kv表，"LABEL_TYPE: KV"计数为2;
    // HEAP_SEGID、HEAP_OFFSET参数不再显示,计数为0;
    // 碎片整理参数持久化模式下显示为0或者null
    // 迭代三新增两张系统表 user role
    int check_num[] = {1, 10, 1, 2, 0, 0, 12, 12, 12, 12, 12};
    for(int i=0; i<sizeof(metchStr)/sizeof(metchStr[0]); i++){
        EXPECT_EQ(GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", metchStr[i]), check_num[i]);
        if(GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", metchStr[i]) != check_num[i]){
            system("gmsysview -q V\\$STORAGE_HEAP_STAT");
        }
    }

    // vl表插入10条数据
    VlComplexRecordCtxT vertexCfg = {
        .opStart = 0,
        .opCount = 10,
        .startMkVal = 0,
        .childCount = 10,
        .labelName = "TEST_GC_VC_T1",
    };
    ret = VlComplexInsert(g_stmtSync, vertexCfg);
    ASSERT_EQ(GMERR_OK, ret);
    // vl表删除5条数据
    vertexCfg.opStart = 0;
    vertexCfg.opCount = 5;
    ret = VlComplexDeleteByIndex(g_stmtSync, vertexCfg, "PrimaryKey");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // kv表插入10条数据
    KvInsert(g_stmtSync, 0, 10, 0, "TEST_KV_T1");
    // kv表删除3条数据
    KvRemove(g_stmtSync, 0, 5, 0, "TEST_KV_T1");
    // 落盘
    ret = GmcFlushData(g_stmtSync, NULL, false);
    ASSERT_EQ(GMERR_OK, ret);
    // 重启
    ret = RestartAndConn(&g_connSync, &g_stmtSync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 重启后查询视图
    const char *metchStr1[] = {\
    "LABEL_NAME: TEST_GC_VC_T1", "LABEL_TYPE: VERTEX", "LABEL_NAME: TEST_KV_T1", "LABEL_TYPE: KV", \
    "HEAP_SEGID", "HEAP_OFFSET", "CUR_ITEM_NUM: 5", "PHY_ITEM_NUM: 5", \
    "LATEST_DEFRAG_FAIL_CNT: 0", "LAST_DEFRAG_TASK_TIME: null", "LAST_DEFRAG_TIME: null",\
    "OPEN_CURSOR_COUNT: 0", "name: TOP3_LONG_TERM_CURSOR"};
    // 存在7张系统表，加上新增的vl表，"LABEL_TYPE: VERTEX"计数为8;
    // 存在全局kv表T_GMDB，kv表不持久化，"LABEL_TYPE: KV"计数为1;
    // HEAP_SEGID、HEAP_OFFSET参数不再显示,计数为0;
    // 碎片整理参数持久化模式下显示为0或者null
    // 迭代三新增两张系统表 user role
    int check_num1[] = {1, 10, 0, 1, 0, 0, 1, 1, 11, 11, 11, 11, 11};
    for(int i=0; i<sizeof(metchStr1)/sizeof(metchStr1[0]); i++){
        EXPECT_EQ(GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", metchStr1[i]), check_num1[i]);
        if(GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", metchStr1[i]) != check_num1[i]){
            system("gmsysview -q V\\$STORAGE_HEAP_STAT");
        }
    }

    //环境清理
    ret = GmcDropVertexLabel(g_stmtSync, g_generalCplxVLTableName1);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmtSync, g_kvTableName1);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    ASSERT_EQ(GMERR_OK, ret);
    system("mkdir -p /data/gmdb/; rm -rf /data/gmdb/*");
    free(schema);
    schema = NULL;
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007、按需持久化，并发场景下，循环查询视图V$STORAGE_HEAP_STAT
TEST_F(HeapStatViewAdaptPst, DFX_094_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    PersistModeChange((char *)"0");

    char *schema = NULL;
    // 创建客户端连接
    int ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建kv表
    readJanssonFile("../schema_file/kv_table_01.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcKvDropTable(g_stmtSync, g_kvTableName1);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = GmcKvCreateTable(g_stmtSync, g_kvTableName1, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);

    int tdNum = 2;
    pthread_t kvTable1[tdNum];
    // 并发做插入操作，循环查询视图，校验CUR_ITEM_NUM、PHY_ITEM_NUM信息
    const char *metchStr[] = { "CUR_ITEM_NUM: 10", "PHY_ITEM_NUM: 10", "CUR_ITEM_NUM: 20", "PHY_ITEM_NUM: 20" };
    int check_num[] = {1, 1, 1, 1};
    int j = 0;
    for (int i = 0; i < tdNum; i++){
        ret = pthread_create(&kvTable1[i], NULL, ThreadSyncKvInsert, NULL);
        EXPECT_EQ(GMERR_OK, ret);
        for (; j <= i * 2 + 1; j++) {
            EXPECT_EQ(GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", metchStr[j]), check_num[j]);
            if (GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", metchStr[j]) != check_num[j]) {
                system("gmsysview -q V\\$STORAGE_KV_COUNT");
            }
        }
    }
    for (int i = 0; i < tdNum; i++) {
        pthread_join(kvTable1[i], NULL);
    }

    // //环境清理
    ret = GmcKvDropTable(g_stmtSync, g_kvTableName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    ASSERT_EQ(GMERR_OK, ret);
    system("mkdir -p /data/gmdb/; rm -rf /data/gmdb/*");
    free(schema);
    schema = NULL;
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008、增量持久化，并发场景下，循环查询视图V$STORAGE_HEAP_STAT
TEST_F(HeapStatViewAdaptPst, DFX_094_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    PersistModeChange((char *)"1");

    char *schema = NULL;
    // 创建客户端连接
    int ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建kv表
    readJanssonFile("../schema_file/kv_table_01.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcKvDropTable(g_stmtSync, g_kvTableName1);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = GmcKvCreateTable(g_stmtSync, g_kvTableName1, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);

    int tdNum = 2;
    pthread_t kvTable1[tdNum];
    // 并发做插入操作，循环查询视图，校验CUR_ITEM_NUM、PHY_ITEM_NUM信息
    const char *metchStr[] = { "CUR_ITEM_NUM: 10", "PHY_ITEM_NUM: 10", "CUR_ITEM_NUM: 20", "PHY_ITEM_NUM: 20" };
    int check_num[] = {1, 1, 1, 1};
    int j = 0;
    for (int i = 0; i < tdNum; i++){
        ret = pthread_create(&kvTable1[i], NULL, ThreadSyncKvInsert, NULL);
        EXPECT_EQ(GMERR_OK, ret);
        for (; j <= i * 2 + 1; j++) {
            EXPECT_EQ(GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", metchStr[j]), check_num[j]);
            if (GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", metchStr[j]) != check_num[j]) {
                system("gmsysview -q V\\$STORAGE_KV_COUNT");
            }
        }
    }
    for (int i = 0; i < tdNum; i++) {
        pthread_join(kvTable1[i], NULL);
    }

    // //环境清理
    ret = GmcKvDropTable(g_stmtSync, g_kvTableName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    ASSERT_EQ(GMERR_OK, ret);
    system("mkdir -p /data/gmdb/; rm -rf /data/gmdb/*");
    free(schema);
    schema = NULL;
    AW_FUN_Log(LOG_STEP, "test end.");
}
