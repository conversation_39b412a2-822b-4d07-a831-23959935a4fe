/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2022. All rights reserved.
 * Description: 服务端导表功能支持基于共享内存导表测试头文件
 * Author: ywy
 * Create: Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * History:
 */
#ifndef IMPORTFROMSHM_H
#define IMPORTFROMSHM_H
#include <sys/shm.h>
#include <stdint.h>
#include <stdio.h>
#include "t_datacom_lite.h"


#define JSON_KEY 123123
#define POLICY_KEY 123126

char g_filePath[200] = "./";

void SystemSnprintf(const char *format, ...)
{
    char command[1024] = {0};
    va_list p;
    va_start(p, format);
    (void)vsnprintf(command, 1024, format, p);
    va_end(p);
    system(command);
}

void LoadFileToShmem(char *jsonFile, char *policyFile, int32_t *gmjsonId = NULL, int32_t *gmpolicyId = NULL)
{
    uint32_t gmjsonKey = JSON_KEY;
    uint32_t gmpolicyKey = POLICY_KEY;
    FILE *gmjsonFileFd = fopen(jsonFile, "r");
    FILE *gmPolicyFileFd = fopen(policyFile, "r");
    EXPECT_EQ(true, gmjsonFileFd != NULL);
    EXPECT_EQ(true, gmPolicyFileFd != NULL);
    (void)fseek(gmjsonFileFd, 0, SEEK_END);
    long jsonFileSize = ftell(gmjsonFileFd);
    if (jsonFileSize < 0) {
        return;
    }
    rewind(gmjsonFileFd);

    (void)fseek(gmPolicyFileFd, 0, SEEK_END);
    long policyFileSize = ftell(gmPolicyFileFd);
    if (policyFileSize < 0) {
        return;
    }
    rewind(gmPolicyFileFd);

    // 创建共享内存
    int32_t gmjsonShmId = shmget(gmjsonKey, jsonFileSize + 1024, IPC_CREAT | 0640);
    int32_t gmpolicyShmId = shmget(gmpolicyKey, policyFileSize + 1024, IPC_CREAT | 0640);
    if (gmjsonId) {
        *gmjsonId = gmjsonShmId;
        *gmpolicyId = gmpolicyShmId;
    }

    EXPECT_EQ(true, gmjsonShmId != -1);
    EXPECT_EQ(true, gmpolicyShmId != -1);
    void *gmjsonAddr = shmat(gmjsonShmId, NULL, 0);
    void *gmpolicyAddr = shmat(gmpolicyShmId, NULL, 0);
    EXPECT_EQ(true, gmjsonAddr != NULL);
    EXPECT_EQ(true, gmpolicyAddr != NULL);

    int32_t jsonRet = fread(gmjsonAddr, jsonFileSize, 1, gmjsonFileFd);
    int32_t policyRet = fread(gmpolicyAddr, policyFileSize, 1, gmPolicyFileFd);
    EXPECT_EQ(true, jsonRet == 1);
    EXPECT_EQ(true, policyRet == 1);

    shmdt(gmjsonAddr);
    shmdt(gmpolicyAddr);
    (void)fclose(gmjsonFileFd);
    (void)fclose(gmPolicyFileFd);
    system("ipcs");
}

void ReadyAndStartServer()
{
    sleep(1);
    system("gmserver -p /usr/local/file/gmserver.ini -b &");
    sleep(1);
}

#endif
