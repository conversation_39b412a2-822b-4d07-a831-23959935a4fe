#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"

GmcConnT *g_conn;
GmcStmtT *g_stmt;
int ret = 0;

#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];

class DFX_028 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh");
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        ASSERT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void DFX_028::SetUp()
{
    ret = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}
void DFX_028::TearDown()
{
    AW_CHECK_LOG_END();
    GmcFreeStmt(g_stmt);
    ret = testGmcDisconnect(g_conn);
    EXPECT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 * Description  : 001 使用gmsysview工具查看所有server配置信息
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.05.28
 * Modification : Create function
 *
 * *****************************************************************************/
TEST_F(DFX_028, DFX_028_001)
{
    char const *view_name = "V\\$CONFIG_PARAMETERS";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s > cfgName0.txt\n",
            g_toolPath, g_connServer,view_name);
    system(g_command);
    snprintf(g_command, MAX_CMD_SIZE, "grep -rE 'NAME:' cfgName0.txt > cfgName1.txt\n");
    system(g_command);
    snprintf(g_command, MAX_CMD_SIZE, "cat cfgName1.txt |cut -d ':' -f 2 > cfgName.txt");
    system(g_command);
    FILE *fp;
    char cfgNameArray[60] = {};

    fp = fopen("cfgName.txt", "r");
    if (fp == NULL) {
        LOG("open cfgName.txt failed");
        ASSERT_EQ(0, 1);
    }

    while (fgets(cfgNameArray, 60, fp) != NULL) {
        LOG("%s", cfgNameArray);
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s\n", g_toolPath, g_connServer,view_name);
        ret = executeCommand(g_command, cfgNameArray);
        ASSERT_EQ(GMERR_OK, ret);
    }
    pclose(fp);
}


/*****************************************************************************
 * Description  : 002 使用-f NAME过滤特定的server配置信息
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.05.28
 * Modification : Create function
 *
 * *****************************************************************************/
TEST_F(DFX_028, DFX_028_002)
{
    char const *view_name = "V\\$CONFIG_PARAMETERS";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s -f NAME=\'maxTotalShmSize\'\n", g_toolPath,
        g_connServer,  view_name);
    LOG("g_command = %s", g_command);

    ret = executeCommand(g_command, "maxTotalShmSize");
    ASSERT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 * Description  : 003 使用-f NAME过滤不存在的server配置信息
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.05.28
 * Modification : Create function
 *
 * *****************************************************************************/
TEST_F(DFX_028, DFX_028_003)
{
    char const *view_name = "V\\$CONFIG_PARAMETERS";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s -f NAME=\'aaa\'\n", g_toolPath,
        g_connServer,  view_name);
    LOG("g_command = %s", g_command);

    ret = executeCommand(g_command, "aaa");
    ASSERT_EQ(-1, ret);
}

/*****************************************************************************
 * Description  : 004 使用$TEST_HOME/tools/modifyCfg.sh 工具修改server配置信息；查看修改后的server配置信息
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.05.28
 * Modification : Create function
 *
 * *****************************************************************************/
TEST_F(DFX_028, DFX_028_004)
{
    char const *view_name = "V\\$CONFIG_PARAMETERS";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s -f NAME=\'maxTotalShmSize\'\n", g_toolPath,
        g_connServer,  view_name);
    LOG("g_command = %s", g_command);

    system("sh $TEST_HOME/tools/modifyCfg.sh maxTotalShmSize=512");
    ret = executeCommand(g_command, "maxTotalShmSize", "2048");
    system("sh $TEST_HOME/tools/modifyCfg.sh maxTotalShmSize=2048");
    ASSERT_EQ(GMERR_OK, ret);
}

class DFX_028_0 : public testing::Test {
protected:
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}

public:
    virtual void SetUp();
    virtual void TearDown();
};

void DFX_028_0::SetUp()
{}
void DFX_028_0::TearDown()
{}
/*****************************************************************************
 * Description  : 005 使用$TEST_HOME/tools/modifyCfg.sh 工具修改server配置信息；查看修改后的server配置信息
                  使用$TEST_HOME/tools/modifyCfg.sh recover恢复server配置信息；
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.05.28
 * Modification : Create function
 *
 * *****************************************************************************/
TEST_F(DFX_028_0, DFX_028_005)
{
    system("sh $TEST_HOME/tools/start.sh");
    sleep(1);

    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);

    system("ipcs");

    char const *view_name = "V\\$CONFIG_PARAMETERS";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s -f NAME=\'maxTotalShmSize\'\n", g_toolPath,
        g_connServer,  view_name);
    LOG("g_command = %s", g_command);

    system("sh $TEST_HOME/tools/modifyCfg.sh maxTotalShmSize=512");
    ret = executeCommand(g_command, "maxTotalShmSize", "512");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("sh $TEST_HOME/tools/modifyCfg.sh maxTotalShmSize=2048");
    ret = executeCommand(g_command, "maxTotalShmSize", "2048");
    ASSERT_EQ(GMERR_OK, ret);

    sleep(1);
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}

/*****************************************************************************
 * Description  : 006 单线程循环查看server配置信息10000次
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.05.28
 * Modification : Create function
 *
 * *****************************************************************************/
TEST_F(DFX_028, DFX_028_006)
{
    char const *view_name = "V\\$CONFIG_PARAMETERS";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s\n", g_toolPath, g_connServer,
         view_name);
    LOG("g_command = %s", g_command);
    system("sh getConfigName.sh");
    FILE *fp;
    char cfgNameArray[60] = {};

    for (int32_t i = 0; i < 10; i++) {
        /*fp = fopen("cfgName.txt", "r");
        if (fp == NULL) {
            LOG("open cfgName.txt failed");
            ASSERT_EQ(0, 1);
        }

        while (fgets(cfgNameArray, 60, fp) != NULL) {
            if (i % 200 == 0) {
                LOG("%s", cfgNameArray);
            }
            ret = executeCommand(g_command, cfgNameArray);
            ASSERT_EQ(GMERR_OK, ret);
        }
        LOG("%d", i);
        pclose(fp);*/
        ret = executeCommand(g_command);
        ASSERT_EQ(GMERR_OK, ret);
    }
}

void *Thread_DFX_028(void *args)
{
    char const *view_name = "V\\$CONFIG_PARAMETERS";
    char command[MAX_CMD_SIZE];
    snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s\n", g_toolPath, g_connServer,
        view_name);
    LOG("command = %s", command);

    uint32_t arg = *(uint32_t *)args;
    for (int32_t i = 0; i < 100; i++) {
        ret = executeCommand(command);
        EXPECT_EQ(GMERR_OK, ret);
        if (i % 20 == 0) {
            LOG("thread%d:%d", arg, i);
        }
    }
    return NULL;
}

/*****************************************************************************
 * Description  : 007 多线程循环查看server配置信息1000次
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.05.28
 * Modification : Create function
 *
 * *****************************************************************************/
TEST_F(DFX_028, DFX_028_007)
{
#if defined ENV_RTOSV2X
#define TDNUM 1
#else
#define TDNUM 5
#endif

    pthread_t sameNameth[TDNUM];
    uint32_t a[5];
    for (int i = 0; i < TDNUM; i++) {
        a[i] = i;
        ret = pthread_create(&sameNameth[i], NULL, Thread_DFX_028, (void *)&a[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < TDNUM; i++) {
        pthread_join(sameNameth[i], NULL);
    }
}

class DFX_028_001 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh");
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        ASSERT_EQ(GMERR_OK, ret);
        system("sh changeCfgFileName.sh 1");
    }
    static void TearDownTestCase()
    {
        system("sh changeCfgFileName.sh 2");
        int ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void DFX_028_001::SetUp()
{
    ret = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
}
void DFX_028_001::TearDown()
{
    GmcFreeStmt(g_stmt);
    ret = testGmcDisconnect(g_conn);
    EXPECT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 * Description  : 008 删除server配置文件；使用gmsysview工具查看所有server配置信息
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.05.28
 * Modification : Create function
 *
 * *****************************************************************************/
TEST_F(DFX_028_001, DFX_028_008)
{
    char const *view_name = "V\\$CONFIG_PARAMETERS";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s\n", g_toolPath, g_connServer,
         view_name);
    LOG("g_command = %s", g_command);

    system("sh changeCfgFileName.sh 1");
    ret = executeCommand(g_command);
    ASSERT_EQ(GMERR_OK, ret);
    system("sh changeCfgFileName.sh 2");
}

/*****************************************************************************
 * Description  : 009 使用gmsysview工具查看所有server配置信息，并校验配置信息
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.05.28
 * Modification : Create function
 *
 * *****************************************************************************/
TEST_F(DFX_028, DFX_028_009)
{
    char const *view_name = "V\\$CONFIG_PARAMETERS";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s\n", g_toolPath, g_connServer,
         view_name);
    LOG("g_command = %s", g_command);
    ret = executeCommand(g_command, "NAME: maxTotalShmSize", "VALUE: 2048", "TYPE: INT32");
    EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand(g_command, "MAX: 16384", "DEFAULT_VALUE: 2048", "CHANGE_MODE: DB_CFG_CHANGE_NOT_ALLOWED");
    EXPECT_EQ(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s > cfgName0.txt\n",
            g_toolPath, g_connServer,view_name);
    system(g_command);
    snprintf(g_command, MAX_CMD_SIZE, "grep -rE 'NAME:' cfgName0.txt > cfgName1.txt\n");
    system(g_command);
    snprintf(g_command, MAX_CMD_SIZE, "cat cfgName1.txt |cut -d ':' -f 2 > cfgName.txt");
    system(g_command);

    FILE *fp;
    char cfgNameArray[60] = {};

    fp = fopen("cfgName.txt", "r");
    if (fp == NULL) {
        LOG("open cfgName.txt failed");
        ASSERT_EQ(0, 1);
    }

    while (fgets(cfgNameArray, 60, fp) != NULL) {
        LOG("%s", cfgNameArray);
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s\n", g_toolPath, g_connServer,view_name);
        ret = executeCommand(g_command, cfgNameArray);
        ASSERT_EQ(GMERR_OK, ret);
    }
    pclose(fp);
}
