[{"type": "record", "name": "array1024", "fields": [{"name": "F0", "type": "uint64", "nullable": false}, {"name": "F1", "type": "uint64", "nullable": true}, {"name": "F2", "type": "int32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "int16", "nullable": true}, {"name": "F5", "type": "uint16", "nullable": true}, {"name": "F6", "type": "int8", "nullable": true}, {"name": "F7", "type": "uint8", "nullable": true}, {"name": "F8", "type": "boolean", "nullable": true}, {"name": "F9", "type": "float", "nullable": true}, {"name": "F10", "type": "double", "nullable": true}, {"name": "F11", "type": "time", "nullable": true}, {"name": "F12", "type": "char", "nullable": true}, {"name": "F13", "type": "uchar", "nullable": true}, {"name": "F14", "type": "string", "size": 100, "nullable": true}, {"name": "F15", "type": "bytes", "size": 7, "nullable": true}, {"name": "F16", "type": "fixed", "size": 7, "nullable": true}, {"name": "T1", "type": "record", "fields": [{"name": "P0", "type": "int64", "nullable": true}, {"name": "P1", "type": "uint64", "nullable": true}, {"name": "P2", "type": "int32", "nullable": true}, {"name": "P3", "type": "uint32", "nullable": true}, {"name": "P4", "type": "int16", "nullable": true}, {"name": "P5", "type": "uint16", "nullable": true}, {"name": "P6", "type": "int8", "nullable": true}, {"name": "P7", "type": "uint8", "nullable": true}, {"name": "P8", "type": "boolean", "nullable": true}, {"name": "P9", "type": "float", "nullable": true}, {"name": "P10", "type": "double", "nullable": true}, {"name": "P11", "type": "time", "nullable": true}, {"name": "P12", "type": "char", "nullable": true}, {"name": "P13", "type": "uchar", "nullable": true}, {"name": "P14", "type": "string", "size": 100, "nullable": true}, {"name": "P15", "type": "bytes", "size": 7, "nullable": true}, {"name": "P16", "type": "fixed", "size": 7, "nullable": true}, {"name": "T2", "type": "record", "fixed_array": true, "size": 1024, "fields": [{"name": "A1", "type": "uint8", "nullable": true}]}]}], "keys": [{"node": "array1024", "name": "pk", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "array1024", "name": "localhash_unique_key", "index": {"type": "<PERSON><PERSON><PERSON>"}, "fields": ["F1"], "constraints": {"unique": true}}, {"node": "array1024", "name": "localhash_key", "fields": ["F3"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": false}}]}]