[{"type": "record", "name": "T39", "schema_version": 2, "fields": [{"name": "F0", "type": "int32", "nullable": false}, {"name": "F1", "type": "int32", "nullable": false}, {"name": "F2", "type": "int32", "nullable": false}, {"name": "F3", "type": "int32", "nullable": true}, {"name": "vrId", "type": "uint32", "comment": "Vs索引", "nullable": false}, {"name": "vrfIndex", "type": "uint32", "comment": "VpnInstace索引", "nullable": false}, {"name": "destIpAddr", "type": "uint32", "comment": "目的地址", "nullable": false}, {"name": "maskLen", "type": "uint8", "comment": "掩码长度", "nullable": false}, {"name": "F4", "type": "int32", "nullable": true, "default": 32}], "keys": [{"node": "T39", "name": "T39_K0", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "T39", "name": "T39_K1", "fields": ["F1"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": true}}, {"node": "T39", "name": "T39_K2", "fields": ["F2"], "index": {"type": "hashcluster"}, "constraints": {"unique": false}}, {"node": "T39", "name": "T39_K3", "fields": ["F3"], "index": {"type": "local"}, "constraints": {"unique": false}}, {"node": "T39", "name": "T39_K4", "fields": ["vrId", "vrfIndex", "destIpAddr", "maskLen"], "index": {"type": "lpm4_tree_bitmap"}, "constraints": {"unique": true}}]}]