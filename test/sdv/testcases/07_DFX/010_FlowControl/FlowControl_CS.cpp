/*****************************************************************************
 Description  : 可信DFX-过载流控反压
 Notes        : 异步插入数据触发过载，调用接口测试返回错误码
 History      :
 Author       : 林健 lwx734521
 Modification :
 Date         : 2021/03/20
*****************************************************************************/
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "jansson.h"

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
GmcStmtT *g_stmt_async = NULL;
GmcConnT *g_conn_async = NULL;

int vertexCount = 50000;
int affectRows;
unsigned int len;
char *normal_vertexlabel_schema = NULL;
char *normal_graph_vertex_label_schema = NULL;
char *normal_graph_edge_label_schema = NULL;
const char *normal_config_json = R"(
    {
        "max_record_num":1000000
    }
)";
const char *g_normal_vertexlabel_name = "T39_all_type";
const char *g_normal_pk_name = "T39_K0";
const char *g_normal_sk_name = "T39_hash";
const char *FlowCtlErrInfo = "Overflow control. Sending request reached limit";

class FlowControl_CS : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        //修改流控相关配置项
        if (g_runMode == 0) {
            system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalShmSize=256\" \"maxTotalDynSize=512\" "
                   "\"clientServerFlowControl=1;1;1;1\" \"flowControlSleepTime=0,0,0\" "
                   "\"overloadThreshold=cpu:8,10,12,15,17,20;dynamicMemory:8,10,12,15,17,20;shareMemory:8,10,12,15,17,"
                   "20;subscribeQueue:8,10,12,15,17,20\" "
                   "\"maxSeMem=128\" \"maxSysShmSize=32\" \"maxSysDynSize=500\"  ");
        } else {
            system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalShmSize=64\" \"maxTotalDynSize=512\" "
                   "\"clientServerFlowControl=1;1;1;1\" \"flowControlSleepTime=0,0,0\" "
                   "\"overloadThreshold=cpu:8,10,12,15,17,20;dynamicMemory:8,10,12,15,17,20;shareMemory:8,10,12,15,17,"
                   "20;subscribeQueue:8,10,12,15,17,20\" "
                   "\"maxSeMem=128\" \"maxSysShmSize=32\" \"maxSysDynSize=500\" ");
        }
        system("sh $TEST_HOME/tools/modifyCfg.sh \"compatibleV3=0\"");
        system("sh $TEST_HOME/tools/start.sh -f");  //触发流控之后表无法删除，需重启server才能接着执行下一个用例
        //还原配置
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        int ret = 0;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret;
        ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void FlowControl_CS::SetUp()
{
    printf("[INFO] FlowControl_CS Start.\n");
    int ret = 0;
    char errorCode1[128] = {0};
    char errorCode2[128] = {0};
    char errorCode3[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_COMMON_STREAM_OVERLOAD);
    (void)snprintf(errorCode2, 128, "GMERR-%d", GMERR_PRIMARY_KEY_VIOLATION);
    (void)snprintf(errorCode3, 128, "GMERR-%d", GMERR_INSUFFICIENT_RESOURCES);
    AW_ADD_ERR_WHITE_LIST(3, errorCode1, errorCode2, errorCode3);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schemaFile/NormalVertexLabel.gmjson", &normal_vertexlabel_schema);
    ASSERT_NE((void *)NULL, normal_vertexlabel_schema);
    AW_CHECK_LOG_BEGIN();
}

void FlowControl_CS::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    free(normal_vertexlabel_schema);
    printf("[INFO] FlowControl_CS End.\n");
}

void set_VertexProperty_PK(GmcStmtT *stmt, int i)
{
    int ret = 0;
    uint32_t F7Value = i;
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT32, &F7Value, sizeof(uint32_t));
    // EXPECT_EQ(GMERR_OK, ret);
}

void set_VertexProperty_SK(GmcStmtT *stmt, int i)
{
    int ret = 0;
    int64_t F9Value = i;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &F9Value, sizeof(int64_t));
    // EXPECT_EQ(GMERR_OK, ret);
}

void set_VertexProperty(GmcStmtT *stmt, int i)
{
    int ret = 0;
    char F0Value = i;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_CHAR, &F0Value, sizeof(char));
    // EXPECT_EQ(GMERR_OK,ret);
    unsigned char F1Value = i;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UCHAR, &F1Value, sizeof(unsigned char));
    // EXPECT_EQ(GMERR_OK,ret);
    int8_t F2Value = i;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &F2Value, sizeof(int8_t));
    // EXPECT_EQ(GMERR_OK,ret);
    uint8_t F3Value = i;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &F3Value, sizeof(uint8_t));
    // EXPECT_EQ(GMERR_OK,ret);
    int16_t F4Value = i;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &F4Value, sizeof(int16_t));
    // EXPECT_EQ(GMERR_OK,ret);
    uint16_t F5Value = i;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &F5Value, sizeof(uint16_t));
    // EXPECT_EQ(GMERR_OK,ret);
    int32_t F6Value = i;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, &F6Value, sizeof(int32_t));
    // EXPECT_EQ(GMERR_OK,ret);
    bool F8Value = false;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &F8Value, sizeof(bool));
    // EXPECT_EQ(GMERR_OK,ret);
    uint64_t F10Value = i;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_UINT64, &F10Value, sizeof(uint64_t));
    // EXPECT_EQ(GMERR_OK,ret);
    float F11Value = i;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_FLOAT, &F11Value, sizeof(float));
    // EXPECT_EQ(GMERR_OK,ret);
    double F12Value = i;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_DOUBLE, &F12Value, sizeof(double));
    // EXPECT_EQ(GMERR_OK,ret);
    uint64_t F13Value = i;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_TIME, &F13Value, sizeof(uint64_t));
    // EXPECT_EQ(GMERR_OK,ret);
    char F14Value[] = "testver";
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, F14Value, (strlen(F14Value)));
    // EXPECT_EQ(GMERR_OK,ret);
    char F15Value[12] = "12";
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_BYTES, F15Value, 12);
    // EXPECT_EQ(GMERR_OK, ret);
    char F16Value[12] = "13";
    ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_FIXED, F16Value, 12);
    // EXPECT_EQ(GMERR_OK, ret);
}

void query_VertexProperty(GmcStmtT *stmt, int i)
{
    int ret = 0;
    // Get F0
    char F0Value = i;
    ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_CHAR, &F0Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F1
    unsigned char F1Value = i;
    ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_UCHAR, &F1Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F2
    int8_t F2Value = i;
    ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_INT8, &F2Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F3
    uint8_t F3Value = i;
    ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_UINT8, &F3Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F4
    int16_t F4Value = i;
    ret = queryPropertyAndCompare(stmt, "F4", GMC_DATATYPE_INT16, &F4Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F5
    uint16_t F5Value = i;
    ret = queryPropertyAndCompare(stmt, "F5", GMC_DATATYPE_UINT16, &F5Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F6
    int32_t F6Value = i;
    ret = queryPropertyAndCompare(stmt, "F6", GMC_DATATYPE_INT32, &F6Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F8
    bool F8Value = false;
    ret = queryPropertyAndCompare(stmt, "F8", GMC_DATATYPE_BOOL, &F8Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F10
    uint64_t F10Value = i;
    ret = queryPropertyAndCompare(stmt, "F10", GMC_DATATYPE_UINT64, &F10Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F11
    float F11Value = i;
    ret = queryPropertyAndCompare(stmt, "F11", GMC_DATATYPE_FLOAT, &F11Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F12
    double F12Value = i;
    ret = queryPropertyAndCompare(stmt, "F12", GMC_DATATYPE_DOUBLE, &F12Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F13
    uint64_t F13Value = i;
    ret = queryPropertyAndCompare(stmt, "F13", GMC_DATATYPE_TIME, &F13Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F14
    char F14Value[] = "testver";
    ret = queryPropertyAndCompare(stmt, "F14", GMC_DATATYPE_STRING, F14Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F15
    char F15Value[12] = "12";
    ret = queryPropertyAndCompare(stmt, "F15", GMC_DATATYPE_BYTES, F15Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F16
    char F16Value[12] = "13";
    ret = queryPropertyAndCompare(stmt, "F16", GMC_DATATYPE_FIXED, F16Value);
    EXPECT_EQ(GMERR_OK, ret);
}

void TestInsertVertexByJson(GmcStmtT *stmt, const char *jsonFile)
{
    int ret = 0;
    json_t *data_json;
    json_error_t data_json_error;
    data_json = json_load_file(jsonFile, 0, &data_json_error);
    if (json_is_array(data_json)) {
        size_t array_size = json_array_size(data_json);
        size_t i;
        printf("Insert %d vertex by json.\n", array_size);
        for (i = 0; i < array_size; i++) {
            json_t *data_json_item = json_array_get(data_json, i);
            char *jStr = json_dumps(data_json_item, JSON_INDENT(0));
            ret = GmcSetVertexByJson(stmt, GMC_JSON_REJECT_DUPLICATES, jStr);
            EXPECT_EQ(GMERR_OK, ret);
            free(jStr);
            ret = GmcExecute(stmt);
            EXPECT_EQ(GMERR_OK, ret);
            ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
            EXPECT_EQ(GMERR_OK, ret);
        }
    } else if (json_is_object(data_json)) {
        printf("Insert 1 vertex by json.\n");
        char *jStr = json_dumps(data_json, JSON_INDENT(0));
        ret = GmcSetVertexByJson(stmt, GMC_JSON_REJECT_DUPLICATES, jStr);
        EXPECT_EQ(GMERR_OK, ret);
        free(jStr);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);
    }
    json_decref(data_json);
}

int TriggerFlowControl(int count)
{
    // async insert vertex
    int ret;
    AsyncUserDataT data = {0};
    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    bool flag = true;
    // for(int i=0;i<count;i++){
    uint32_t pk = count;
    int val = count;
    int64_t sk = count;
    set_VertexProperty_PK(g_stmt_async, pk);
    set_VertexProperty_SK(g_stmt_async, sk);
    set_VertexProperty(g_stmt_async, val);
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = insert_vertex_callback;
    insertRequestCtx.userData = &data;
    ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
    if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
        ret = testGmcGetLastError(FlowCtlErrInfo);
        EXPECT_EQ(GMERR_OK, ret);
        if (flag) {
            printf("[INFO]trigger flow control\n");
            flag = false;
        }
        return GMERR_COMMON_STREAM_OVERLOAD;
    } else {
        EXPECT_EQ(GMERR_OK, ret);
        if (ret == GMERR_PROGRAM_LIMIT_EXCEEDED) {
            return GMERR_PROGRAM_LIMIT_EXCEEDED;
        }
        ret = testWaitAsyncRecv(&data);
    }
    if ((count % 25000 == 0) && (count > 0)) {
        printf("insert_num: %d\n", count);
    }
    return ret;
}

// 删除数据：有利于流控降下来
int FlowControlDown(GmcStmtT *stmt, const char *labelName)
{
    int ret;
    ret = GmcTruncateVertexLabel(stmt, labelName);
    if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
        while (ret == GMERR_COMMON_STREAM_OVERLOAD) {
            ret = GmcTruncateVertexLabel(stmt, labelName);
        }
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        EXPECT_EQ(GMERR_OK, ret);
    }
    return ret;
}

// 001. 创建vertexLabel,插数据至server过载触发流控，调用GmcCreateVertexLabel返回流控错误码
TEST_F(FlowControl_CS, DFX_010_FlowControl_CS_001)
{
#ifdef DIRECT_WRITE
#else
    int ret = 0;
	char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_COMMON_STREAM_OVERLOAD);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    //触发流控
    int write_num = 0;
    while (ret != GMERR_COMMON_STREAM_OVERLOAD) {
        ret = TriggerFlowControl(write_num);
        if (ret == GMERR_PROGRAM_LIMIT_EXCEEDED) {
            break;
        }
        write_num++;
    }
    // printf("ret=%d\n", ret);
    // create vertexlabel
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int successCount = 0;
    int vetexlabel_count = 20000;
    char labelName[20] = "";
    char g_label_schema[1024] = "";
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t num = 0; num < vetexlabel_count; num++) {
        snprintf(g_label_schema, 1024,
            "[{\"type\":\"record\", \"name\":\"T%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
            "{\"name\":\"F1\", \"type\":\"int32\"}],"
            "\"keys\":[{\"node\":\"T%d\", \"name\":\"T%d_K0\", \"fields\":[\"F0\"], "
            "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
            num, num, num);
        snprintf(labelName, 20, "T%d", num);
        ret = GmcCreateVertexLabel(g_stmt, g_label_schema, NULL);
        if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
            ret = testGmcGetLastError(FlowCtlErrInfo);
            EXPECT_EQ(GMERR_OK, ret);
            successCount++;
        } else {
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    if (successCount < 1) {
        EXPECT_EQ(0, 1);
    } else {
        printf("GmcCreateVertexLabel count:%d, trigger flowcontrol count:%d\n", vetexlabel_count, successCount);
    }

    // 流控降下来
    ret = FlowControlDown(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
    // drop vertexLabel
    successCount = 0;
    for (uint32_t num = 0; num < vetexlabel_count; num++) {
        snprintf(labelName, 20, "T%d", num);
        ret = GmcDropVertexLabel(g_stmt, labelName);
        if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
            while (ret == GMERR_COMMON_STREAM_OVERLOAD) {
                ret = GmcDropVertexLabel(g_stmt, labelName);
            }
            EXPECT_EQ(true, ret == GMERR_OK || ret == GMERR_UNDEFINED_TABLE); // drop返回9010是因为流控触发建表失败
        } else {
            EXPECT_EQ(true, ret == GMERR_OK || ret == GMERR_UNDEFINED_TABLE);
        }
    }
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // free
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
#endif
}

// 002. 创建vertexLabel,插数据至server过载触发流控，调用GmcCreateVertexLabelAsync返回流控错误码
TEST_F(FlowControl_CS, DFX_010_FlowControl_CS_002)
{
#ifdef DIRECT_WRITE
#else
    int ret = 0;
	char errorCode1[128] = {0};
    char errorCode2[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_COMMON_STREAM_OVERLOAD);
    (void)snprintf(errorCode2, 128, "GMERR-%d", GMERR_PROGRAM_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(2, errorCode1, errorCode2);
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    //触发流控
    int write_num = 0;
    while (ret != GMERR_COMMON_STREAM_OVERLOAD) {
        ret = TriggerFlowControl(write_num);
        if (ret == GMERR_PROGRAM_LIMIT_EXCEEDED) {
            break;
        }
        write_num++;
    }

    // create vertexlabel
    AsyncUserDataT data = {0};
    int successCount = 0;
    int vetexlabel_count = vertexCount;
    char labelName[20] = "";
    char g_label_schema[1024] = "";

    for (uint32_t num = 0; num < vetexlabel_count; num++) {
        snprintf(g_label_schema, 1024,
            "[{\"type\":\"record\", \"name\":\"T%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
            "{\"name\":\"F1\", \"type\":\"int32\"}],"
            "\"keys\":[{\"node\":\"T%d\", \"name\":\"T%d_K0\", \"fields\":[\"F0\"], "
            "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
            num, num, num);
        snprintf(labelName, 20, "T%d", num);
        ret = GmcCreateVertexLabelAsync(g_stmt_async, g_label_schema, NULL, create_vertex_label_callback, &data);
        if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
            ret = testGmcGetLastError(FlowCtlErrInfo);
            EXPECT_EQ(GMERR_OK, ret);
            successCount++;
        } else {
            ret = testWaitAsyncRecv(&data);
        }
    }
    printf("GmcCreateVertexLabelAsync count:%d, trigger flowcontrol count:%d\n", vetexlabel_count, successCount);

    // 流控降下来
    ret = FlowControlDown(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
    // drop vertexLabel
    successCount = 0;
    for (uint32_t num = 0; num < vetexlabel_count; num++) {
        snprintf(labelName, 20, "T%d", num);
        ret = GmcDropVertexLabel(g_stmt, labelName);
        while (ret == GMERR_COMMON_STREAM_OVERLOAD) {
            ret = GmcDropVertexLabel(g_stmt, labelName);
            usleep(10);
        }
        EXPECT_EQ(GMERR_OK, ret == GMERR_UNDEFINED_TABLE ? GMERR_OK : ret); // drop返回9010是因为流控触发建表失败
    }

    // free
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    while (ret == GMERR_COMMON_STREAM_OVERLOAD) {
        ret = GmcDropVertexLabel(g_stmt, labelName);
        usleep(10);
    }
    EXPECT_EQ(GMERR_OK, ret == GMERR_UNDEFINED_TABLE ? GMERR_OK : ret);
#endif
}

// 003. 创建vertexLabel,插数据至server过载触发流控，调用GmcDropVertexLabel返回流控错误码
TEST_F(FlowControl_CS, DFX_010_FlowControl_CS_003)
{
#ifdef DIRECT_WRITE
#else
    AddWhiteList(GMERR_PROGRAM_LIMIT_EXCEEDED);
    int ret = 0;
	char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_COMMON_STREAM_OVERLOAD);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    //触发流控
    int write_num = 0;
    while (ret != GMERR_COMMON_STREAM_OVERLOAD) {
        ret = TriggerFlowControl(write_num);
        if (ret == GMERR_PROGRAM_LIMIT_EXCEEDED) {
            break;
        }
        write_num++;
    }

    // create vertexlabel
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int successCount = 0;
    int vetexlabel_count = vertexCount;
    char labelName[20] = "";
    char g_label_schema[1024] = "";
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t num = 0; num < vetexlabel_count; num++) {
        snprintf(g_label_schema, 1024,
            "[{\"type\":\"record\", \"name\":\"T%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
            "{\"name\":\"F1\", \"type\":\"int32\"}],"
            "\"keys\":[{\"node\":\"T%d\", \"name\":\"T%d_K0\", \"fields\":[\"F0\"], "
            "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
            num, num, num);
        snprintf(labelName, 20, "T%d", num);
        ret = GmcCreateVertexLabel(stmt, g_label_schema, NULL);
        if (ret == GMERR_PROGRAM_LIMIT_EXCEEDED) {
            break;
        }
        if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
            ret = testGmcGetLastError(FlowCtlErrInfo);
            EXPECT_EQ(GMERR_OK, ret);
            successCount++;
        } else {
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    if (successCount < 1) {
        EXPECT_EQ(0, 1);
    } else {
        printf("GmcCreateVertexLabel count:%d, trigger flowcontrol count:%d\n", vetexlabel_count, successCount);
    }
    // drop vertexLabel
    successCount = 0;
    for (uint32_t num = 0; num < vetexlabel_count; num++) {
        snprintf(labelName, 20, "T%d", num);
        ret = GmcDropVertexLabel(g_stmt, labelName);
        if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
            ret = testGmcGetLastError(FlowCtlErrInfo);
            EXPECT_EQ(GMERR_OK, ret);
            successCount++;
        }
    }
    if (successCount < 1) {
        EXPECT_EQ(0, 1);
    } else {
        printf("GmcDropVertexLabel count:%d, trigger flowcontrol count:%d\n", vetexlabel_count, successCount);
    }

    // 流控降下来
    ret = FlowControlDown(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
    // drop vertexLabel
    successCount = 0;
    for (uint32_t num = 0; num < vetexlabel_count; num++) {
        snprintf(labelName, 20, "T%d", num);
        ret = GmcDropVertexLabel(g_stmt, labelName);
        if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
            while (ret == GMERR_COMMON_STREAM_OVERLOAD) {
                ret = GmcDropVertexLabel(g_stmt, labelName);
            }
            EXPECT_EQ(true, ret == GMERR_OK || ret == GMERR_UNDEFINED_TABLE); // drop返回9010是因为流控触发建表失败
        } else {
            EXPECT_EQ(true, ret == GMERR_OK || ret == GMERR_UNDEFINED_TABLE);
        }
    }

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // free
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
#endif
}

// 004. 创建vertexLabel,插数据至server过载触发流控，调用GmcCreateEdgeLabel返回流控错误码
TEST_F(FlowControl_CS, DFX_010_FlowControl_CS_004)
{
#ifdef DIRECT_WRITE
#else
    int ret = 0;
	char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_COMMON_STREAM_OVERLOAD);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    char Label_config[] = "{\"max_record_num\":1000000, \"isFastReadUncommitted\":0}";

    char *vertexLabelJson1 = NULL;
    char *vertexLabelJson2 = NULL;
    const char *vertexLabelName1 = "M1";
    const char *vertexLabelName2 = "M2";
    readJanssonFile("schemaFile/VertexLabel1.gmjson", &vertexLabelJson1);
    ASSERT_NE((void *)NULL, vertexLabelJson1);
    ret = GmcCreateVertexLabel(g_stmt, vertexLabelJson1, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    readJanssonFile("schemaFile/VertexLabel2.gmjson", &vertexLabelJson2);
    ASSERT_NE((void *)NULL, vertexLabelJson2);
    ret = GmcCreateVertexLabel(g_stmt, vertexLabelJson2, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    free(vertexLabelJson1);
    free(vertexLabelJson2);
    //触发流控
    int write_num = 0;
    while (ret != GMERR_COMMON_STREAM_OVERLOAD) {
        ret = TriggerFlowControl(write_num);
        if (ret == GMERR_PROGRAM_LIMIT_EXCEEDED) {
            break;
        }
        write_num++;
    }

    // crateEdgeLabel
    char edgeLabelName[32] = "testT1";
    char edgeLabelJson[1024];
    int successCount = 0;
    int edgeLabelCount = 10000;
    for (uint i = 0; i < edgeLabelCount; i++) {
        sprintf(edgeLabelJson,
            "[{\"name\":\"testT%d\",\"source_vertex_label\":\"M1\",\"comment\": \"the edge "
            "xxx\",\"dest_vertex_label\":\"M2\","
            "\"constraint\":{\"operator_type\":\"and\",\"conditions\":[{\"source_property\": \"F7\",\"dest_property\": "
            "\"F7\"},"
            "{\"source_property\": \"F9\",\"dest_property\": \"F9\"}]}}]",
            i);
        sprintf(edgeLabelName, "testT%d", i);
        ret = GmcCreateEdgeLabel(g_stmt, edgeLabelJson, Label_config);
        if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
            ret = testGmcGetLastError(FlowCtlErrInfo);
            EXPECT_EQ(GMERR_OK, ret);
            successCount++;
        } else {
            EXPECT_EQ(GMERR_OK, ret);
        }
    }

    if (successCount < 1) {
        EXPECT_EQ(0, 1);
    } else {
        printf("GmcCreateEdgeLabel count:%d, trigger flowcontrol count:%d\n", edgeLabelCount, successCount);
    }

    // 流控降下来
    ret = FlowControlDown(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "重试 删edgeLabel");
    for (int i = 0; i < edgeLabelCount; i++) {
        sprintf(edgeLabelName, "testT%d", i);
        ret = GmcDropEdgeLabel(g_stmt, edgeLabelName);
        while (ret == GMERR_COMMON_STREAM_OVERLOAD) {
            ret = GmcDropEdgeLabel(g_stmt, edgeLabelName);
            if (ret == GMERR_OK) {
                AW_FUN_Log(LOG_STEP, "重试 删edgeLabel: %s", edgeLabelName);
                break;
            }
        }
    }

    // drop edgeLabel
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    while (ret == GMERR_COMMON_STREAM_OVERLOAD) {
        ret = GmcDropVertexLabel(g_stmt, vertexLabelName1);
        usleep(10);
    }
    EXPECT_EQ(GMERR_OK, ret == GMERR_UNDEFINED_TABLE ? GMERR_OK : ret);
    ret = GmcDropVertexLabel(g_stmt, vertexLabelName1);
    while (ret == GMERR_COMMON_STREAM_OVERLOAD) {
        ret = GmcDropVertexLabel(g_stmt, vertexLabelName1);
        usleep(10);
    }
    EXPECT_EQ(GMERR_OK, ret == GMERR_UNDEFINED_TABLE ? GMERR_OK : ret);
    ret = GmcDropVertexLabel(g_stmt, vertexLabelName2);
    EXPECT_EQ(GMERR_OK, ret);
#endif
}

// 005. 创建vertexLabel,插数据至server过载触发流控，调用GmcDropEdgeLabel返回流控错误码
TEST_F(FlowControl_CS, DFX_010_FlowControl_CS_005)
{
#ifdef DIRECT_WRITE
#else
    int ret = 0;
	char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_COMMON_STREAM_OVERLOAD);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    char Label_config[] = "{\"max_record_num\":1000000, \"isFastReadUncommitted\":0}";

    char *vertexLabelJson1 = NULL;
    char *vertexLabelJson2 = NULL;
    const char *vertexLabelName1 = "M1";
    const char *vertexLabelName2 = "M2";
    readJanssonFile("schemaFile/VertexLabel1.gmjson", &vertexLabelJson1);
    ASSERT_NE((void *)NULL, vertexLabelJson1);
    ret = GmcCreateVertexLabel(g_stmt, vertexLabelJson1, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    readJanssonFile("schemaFile/VertexLabel2.gmjson", &vertexLabelJson2);
    ASSERT_NE((void *)NULL, vertexLabelJson2);
    ret = GmcCreateVertexLabel(g_stmt, vertexLabelJson2, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    free(vertexLabelJson1);
    free(vertexLabelJson2);

    // 触发流控
    int writeNum = 0;
    while (ret != GMERR_COMMON_STREAM_OVERLOAD) {
        ret = TriggerFlowControl(writeNum);
        if (ret == GMERR_PROGRAM_LIMIT_EXCEEDED) {
            break;
        }
        writeNum++;
    }

    // crateEdgeLabel
    char edgeLabelName[32] = "testT1";
    char edgeLabelJson[1024];
    int successCount = 0;
    int edgeLabelCount = 10000;
    for (uint i = 0; i < edgeLabelCount; i++) {
        sprintf(edgeLabelJson,
            "[{\"name\":\"testT%d\",\"source_vertex_label\":\"M1\",\"comment\": \"the edge "
            "xxx\",\"dest_vertex_label\":\"M2\","
            "\"constraint\":{\"operator_type\":\"and\",\"conditions\":[{\"source_property\": \"F7\",\"dest_property\": "
            "\"F7\"},"
            "{\"source_property\": \"F9\",\"dest_property\": \"F9\"}]}}]",
            i);
        sprintf(edgeLabelName, "testT%d", i);
        ret = GmcCreateEdgeLabel(g_stmt, edgeLabelJson, Label_config);
        if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
            ret = testGmcGetLastError(FlowCtlErrInfo);
            EXPECT_EQ(GMERR_OK, ret);
            successCount++;
        } else {
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    printf("GmcCreateEdgeLabel count:%d, trigger flowcontrol count:%d\n", edgeLabelCount, successCount);

    // dropEdgeLabel
    successCount = 0;
    for (int i = 0; i < edgeLabelCount; i++) {
        sprintf(edgeLabelName, "testT%d", i);
        ret = GmcDropEdgeLabel(g_stmt, edgeLabelName);
        if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
            ret = testGmcGetLastError(FlowCtlErrInfo);
            EXPECT_EQ(GMERR_OK, ret);
            successCount++;
        } else {
            EXPECT_EQ((ret == GMERR_UNDEFINED_TABLE) ? GMERR_UNDEFINED_TABLE : GMERR_OK, ret);
        }
    }
    if (successCount < 1) {
        EXPECT_EQ(0, 1);
    } else {
        printf("GmcDropEdgeLabel count:%d, trigger flowcontrol count:%d\n", edgeLabelCount, successCount);
    }

    // 流控降下来
    ret = FlowControlDown(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "重试 删edgeLabel");
    for (int i = 0; i < edgeLabelCount; i++) {
        sprintf(edgeLabelName, "testT%d", i);
        ret = GmcDropEdgeLabel(g_stmt, edgeLabelName);
        while (ret == GMERR_COMMON_STREAM_OVERLOAD) {
            ret = GmcDropEdgeLabel(g_stmt, edgeLabelName);
            if (ret == GMERR_OK) {
                AW_FUN_Log(LOG_STEP, "重试 删edgeLabel: %s", edgeLabelName);
                break;
            }
        }
    }

    // drop edgeLabel
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    while (ret == GMERR_COMMON_STREAM_OVERLOAD) {
        ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
        usleep(10);
    }
    EXPECT_EQ(GMERR_OK, ret == GMERR_UNDEFINED_TABLE ? GMERR_OK : ret);
    ret = GmcDropVertexLabel(g_stmt, vertexLabelName1);
    while (ret == GMERR_COMMON_STREAM_OVERLOAD) {
        ret = GmcDropVertexLabel(g_stmt, vertexLabelName1);
        usleep(10);
    }
    EXPECT_EQ(GMERR_OK, ret == GMERR_UNDEFINED_TABLE ? GMERR_OK : ret);
    ret = GmcDropVertexLabel(g_stmt, vertexLabelName2);
    EXPECT_EQ(GMERR_OK, ret);
#endif
}

// 009. 创建vertexLabel,插数据至server过载触发流控，调用GmcInsertVertex返回流控错误码
TEST_F(FlowControl_CS, DFX_010_FlowControl_CS_009)
{
#ifdef DIRECT_WRITE
#else
    int ret = 0;
	char errorCode1[128] = {0};
    char errorCode2[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_COMMON_STREAM_OVERLOAD);
    (void)snprintf(errorCode2, 128, "GMERR-%d", GMERR_PROGRAM_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(2, errorCode1, errorCode2);
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    //触发流控
    int write_num = 0;
    while (ret != GMERR_COMMON_STREAM_OVERLOAD) {
        ret = TriggerFlowControl(write_num);
        if (ret == GMERR_PROGRAM_LIMIT_EXCEEDED) {
            break;
        }
        write_num++;
    }

    // insertvertex
    int vCount = vertexCount * 5;
    int successCount = 0;
    const char *ErrInfo = "Flow control. Failed to insert vertex.";
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    for (int i = vertexCount; i < (vertexCount + vCount); i++) {
        uint32_t pk = i;
        int val = i;
        int64_t sk = i;
        set_VertexProperty_PK(g_stmt, pk);
        set_VertexProperty_SK(g_stmt, sk);
        set_VertexProperty(g_stmt, val);
        ret = GmcExecute(g_stmt);
        if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
            ret = testGmcGetLastError(FlowCtlErrInfo);
            EXPECT_EQ(GMERR_OK, ret);
            successCount++;
        }
    }
    if (successCount < 1) {
        EXPECT_EQ(0, 1);
    } else {
        printf("GmcInsertVertex count:%d, trigger flowcontrol count:%d\n", vCount, successCount);
    }

    // 流控降下来
    ret = FlowControlDown(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);

    // free
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
        while (ret == GMERR_COMMON_STREAM_OVERLOAD) {
            ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
        }
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        EXPECT_EQ(GMERR_OK, ret);
    }
#endif
}

// 010. 创建vertexLabel,插数据至server过载触发流控，调用GmcInsertVertexAsync返回流控错误码
TEST_F(FlowControl_CS, DFX_010_FlowControl_CS_010)
{
#ifdef DIRECT_WRITE
#else
    int ret = 0;
	char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_COMMON_STREAM_OVERLOAD);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    int count = 10 * vertexCount;
    int successCount = 0;
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        uint32_t pk = i;
        int val = i;
        int64_t sk = i;
        set_VertexProperty_PK(g_stmt_async, pk);
        set_VertexProperty_SK(g_stmt_async, sk);
        set_VertexProperty(g_stmt_async, val);
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
            ret = testGmcGetLastError(FlowCtlErrInfo);
            EXPECT_EQ(GMERR_OK, ret);
            successCount++;
        } else {
            ret = testWaitAsyncRecv(&data);
        }
    }
    if (successCount < 1) {
        EXPECT_EQ(0, 1);
    } else {
        printf("GmcInsertVertexAsync count:%d, trigger flowcontrol count:%d\n", count, successCount);
    }

    // 流控降下来
    ret = FlowControlDown(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);

    // free
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
        while (ret == GMERR_COMMON_STREAM_OVERLOAD) {
            ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
        }
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        EXPECT_EQ(GMERR_OK, ret);
    }
#endif
}


// 012. 创建vertexLabel,插数据至server过载触发流控，调用GmcMergeVertex返回流控错误码
TEST_F(FlowControl_CS, DFX_010_FlowControl_CS_012)
{
#ifdef DIRECT_WRITE
#else
    int ret = 0;
	char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_COMMON_STREAM_OVERLOAD);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    //触发流控
    int write_num = 0;
    while (ret != GMERR_COMMON_STREAM_OVERLOAD) {
        ret = TriggerFlowControl(write_num);
        if (ret == GMERR_PROGRAM_LIMIT_EXCEEDED) {
            break;
        }
        write_num++;
    }

    // mergevertex
    int vCount = vertexCount * 3;
    int successCount = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
    for (int i = vertexCount; i < (vertexCount + vCount); i++) {
        uint32_t pk = i;
        int val = i;
        int64_t sk = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "T39_K0");
        EXPECT_EQ(GMERR_OK, ret);
        // set_VertexProperty_PK(g_stmt, pk);
        set_VertexProperty_SK(g_stmt, sk);
        set_VertexProperty(g_stmt, val);
        ret = GmcExecute(g_stmt);
        if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
            ret = testGmcGetLastError(FlowCtlErrInfo);
            EXPECT_EQ(GMERR_OK, ret);
            successCount++;
        }
    }
    if (successCount < 1) {
        EXPECT_EQ(0, 1);
    } else {
        printf("GmcMergeVertex count:%d, trigger flowcontrol count:%d\n", vCount, successCount);
    }
    // 流控降下来
    ret = FlowControlDown(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);

    // free
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
        while (ret == GMERR_COMMON_STREAM_OVERLOAD) {
            ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
        }
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        EXPECT_EQ(GMERR_OK, ret);
    }
#endif
}

// 013. 创建vertexLabel,插数据至server过载触发流控，调用GmcReplaceVertex返回流控错误码
TEST_F(FlowControl_CS, DFX_010_FlowControl_CS_013)
{
#ifdef DIRECT_WRITE
#else
    int ret = 0;
	char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_COMMON_STREAM_OVERLOAD);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    //触发流控
    int write_num = 0;
    while (ret != GMERR_COMMON_STREAM_OVERLOAD) {
        ret = TriggerFlowControl(write_num);
        if (ret == GMERR_PROGRAM_LIMIT_EXCEEDED) {
            break;
        }
        write_num++;
    }

    // replacevetex
    int vCount = vertexCount * 3;
    int successCount = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
    for (int i = vertexCount; i < (vertexCount + vCount); i++) {
        uint32_t pk = i;
        int val = i;
        int64_t sk = i;
        set_VertexProperty_PK(g_stmt, pk);
        set_VertexProperty_SK(g_stmt, sk);
        set_VertexProperty(g_stmt, val);
        ret = GmcExecute(g_stmt);
        if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
            ret = testGmcGetLastError(FlowCtlErrInfo);
            EXPECT_EQ(GMERR_OK, ret);
            successCount++;
        }
    }
    if (successCount < 1) {
        EXPECT_EQ(0, 1);
    } else {
        printf("GmcReplaceVertex count:%d, trigger flowcontrol count:%d\n", vCount, successCount);
    }
    // 流控降下来
    ret = FlowControlDown(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);

    // free
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
        while (ret == GMERR_COMMON_STREAM_OVERLOAD) {
            ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
        }
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        EXPECT_EQ(GMERR_OK, ret);
    }
#endif
}

// 014. 创建vertexLabel,插数据至server过载触发流控，调用GmcUpdateVertexByIndexKey返回流控错误码
TEST_F(FlowControl_CS, DFX_010_FlowControl_CS_014)
{
#ifdef DIRECT_WRITE
#else
    int ret = 0;
	char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_COMMON_STREAM_OVERLOAD);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    //触发流控
    int write_num = 0;
    while (ret != GMERR_COMMON_STREAM_OVERLOAD) {
        ret = TriggerFlowControl(write_num);
        if (ret == GMERR_PROGRAM_LIMIT_EXCEEDED) {
            break;
        }
        write_num++;
    }

    // GmcUpdateVertexByIndexKey
    int vCount = vertexCount * 3;
    int successCount = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < vCount; i++) {
        uint32_t pk = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        bool F8Value = true;
        ret = GmcSetVertexProperty(g_stmt, "F8", GMC_DATATYPE_BOOL, &F8Value, sizeof(bool));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
            ret = testGmcGetLastError(FlowCtlErrInfo);
            EXPECT_EQ(GMERR_OK, ret);
            successCount++;
        } else {
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    if (successCount < 1) {
        EXPECT_EQ(0, 1);
    } else {
        printf("GmcUpdateVertexByIndexKey count:%d, trigger flowcontrol count:%d\n", vCount, successCount);
    }
    // 流控降下来
    ret = FlowControlDown(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);

    // free
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
        while (ret == GMERR_COMMON_STREAM_OVERLOAD) {
            ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
        }
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        EXPECT_EQ(GMERR_OK, ret);
    }
#endif
}

class High_maxSeMem : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        //修改流控相关配置项
        if (g_runMode == 0) {
            system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=512\" "
                   "\"clientServerFlowControl=1;1;1;1\" \"flowControlSleepTime=0,0,0\" "
                   "\"overloadThreshold=cpu:8,10,12,15,17,20;dynamicMemory:8,10,12,15,17,20;shareMemory:8,10,12,15,17,"
                   "20;subscribeQueue:8,10,12,15,17,20\" "
                   "\"maxSeMem=1844\" \"maxSysShmSize=32\" \"maxSysDynSize=500\"  ");
        } else {
            system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalShmSize=64\" \"maxTotalDynSize=512\" "
                   "\"clientServerFlowControl=1;1;1;1\" \"flowControlSleepTime=0,0,0\" "
                   "\"overloadThreshold=cpu:8,10,12,15,17,20;dynamicMemory:8,10,12,15,17,20;shareMemory:8,10,12,15,17,"
                   "20;subscribeQueue:8,10,12,15,17,20\" "
                   "\"maxSeMem=128\" \"maxSysShmSize=32\" \"maxSysDynSize=500\" ");
        }
        system("sh $TEST_HOME/tools/modifyCfg.sh \"compatibleV3=0\"");
        system("sh $TEST_HOME/tools/start.sh -f");  //触发流控之后表无法删除，需重启server才能接着执行下一个用例
        //还原配置
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        int ret = 0;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret;
        ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void High_maxSeMem::SetUp()
{
    printf("[INFO] High_maxSeMem Start.\n");
    int ret = 0;
    char errorCode1[128] = {0};
    char errorCode2[128] = {0};
    char errorCode3[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_COMMON_STREAM_OVERLOAD);
    (void)snprintf(errorCode2, 128, "GMERR-%d", GMERR_PRIMARY_KEY_VIOLATION);
    (void)snprintf(errorCode3, 128, "GMERR-%d", GMERR_INSUFFICIENT_RESOURCES);
    AW_ADD_ERR_WHITE_LIST(3, errorCode1, errorCode2, errorCode3);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schemaFile/NormalVertexLabel.gmjson", &normal_vertexlabel_schema);
    ASSERT_NE((void *)NULL, normal_vertexlabel_schema);
    AW_CHECK_LOG_BEGIN();
}

void High_maxSeMem::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    free(normal_vertexlabel_schema);
    printf("[INFO] High_maxSeMem End.\n");
}

// 015. 创建vertexLabel,插数据至server过载触发流控，调用GmcUpdateVertexByIndexKeyAsync返回流控错误码
TEST_F(High_maxSeMem, DFX_010_FlowControl_CS_015)
{
#ifdef DIRECT_WRITE
#else
    int ret = 0;
	char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_COMMON_STREAM_OVERLOAD);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    //触发流控
    int write_num = 0;
    while (ret != GMERR_COMMON_STREAM_OVERLOAD) {
        ret = TriggerFlowControl(write_num);
        if (ret == GMERR_PROGRAM_LIMIT_EXCEEDED) {
            break;
        }
        write_num++;
    }

    // GmcUpdateVertexByIndexKeyAsync
    int vCount = vertexCount;
    int successCount = 0;
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < vCount; i++) {
        uint32_t pk = i;
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        bool F8Value = true;
        ret = GmcSetVertexProperty(g_stmt_async, "F8", GMC_DATATYPE_BOOL, &F8Value, sizeof(bool));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_async, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT updateRequestCtx;
        updateRequestCtx.updateCb = update_vertex_callback;
        updateRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &updateRequestCtx);
        if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
            ret = testGmcGetLastError(FlowCtlErrInfo);
            EXPECT_EQ(GMERR_OK, ret);
            successCount++;
        } else {
            EXPECT_EQ(GMERR_OK, ret);
            ret = testWaitAsyncRecv(&data);
        }
    }
    if (successCount < 1) {
        EXPECT_EQ(0, 1);
    } else {
        printf("GmcUpdateVertexByIndexKeyAsync count:%d, trigger flowcontrol count:%d\n", vCount, successCount);
    }
    // 流控降下来
    ret = FlowControlDown(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);

    // free
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
        while (ret == GMERR_COMMON_STREAM_OVERLOAD) {
            ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
        }
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        EXPECT_EQ(GMERR_OK, ret);
    }
#endif
}

// 016. 创建vertexLabel,插数据至server过载触发流控，调用GmcDeleteVertexByIndexKey返回流控错误码
TEST_F(FlowControl_CS, DFX_010_FlowControl_CS_016)
{
#ifdef DIRECT_WRITE
#else
    int ret = 0;
	char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_COMMON_STREAM_OVERLOAD);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    //触发流控
    int write_num = 0;
    while (ret != GMERR_COMMON_STREAM_OVERLOAD) {
        ret = TriggerFlowControl(write_num);
        if (ret == GMERR_PROGRAM_LIMIT_EXCEEDED) {
            break;
        }
        write_num++;
    }

    // GmcDeleteVertexByIndexKey
    int vCount = vertexCount * 3;
    int successCount = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < vCount; i++) {
        uint32_t pk = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
            ret = testGmcGetLastError(FlowCtlErrInfo);
            EXPECT_EQ(GMERR_OK, ret);
            successCount++;
        } else {
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    printf("GmcDeleteVertexByIndexKey count:%d, trigger flowcontrol count:%d\n", vCount, successCount);
    // 流控降下来
    ret = FlowControlDown(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);

    // free
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
        while (ret == GMERR_COMMON_STREAM_OVERLOAD) {
            ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
        }
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        EXPECT_EQ(GMERR_OK, ret);
    }
#endif
}

// 017. 创建vertexLabel,插数据至server过载触发流控，调用GmcDeleteVertexByIndexKeyAsync返回流控错误码
TEST_F(FlowControl_CS, DFX_010_FlowControl_CS_017)
{
#ifdef DIRECT_WRITE
#else
    int ret = 0;
	char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_COMMON_STREAM_OVERLOAD);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    //触发流控
    int write_num = 0;
    while (ret != GMERR_COMMON_STREAM_OVERLOAD) {
        ret = TriggerFlowControl(write_num);
        if (ret == GMERR_PROGRAM_LIMIT_EXCEEDED) {
            break;
        }
        write_num++;
    }

    // GmcDeleteVertexByIndexKeyAsync
    int vCount = vertexCount * 3;
    int successCount = 0;
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < vCount; i++) {
        uint32_t pk = i;
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_async, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT deleteRequestCtx;
        deleteRequestCtx.deleteCb = delete_vertex_callback;
        deleteRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &deleteRequestCtx);
        if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
            ret = testGmcGetLastError(FlowCtlErrInfo);
            EXPECT_EQ(GMERR_OK, ret);
            successCount++;
        } else {
            EXPECT_EQ(GMERR_OK, ret);
            ret = testWaitAsyncRecv(&data);
        }
    }
    if (successCount < 1) {
        EXPECT_EQ(0, 1);
    } else {
        printf("GmcDeleteVertexByIndexKeyAsync count:%d, trigger flowcontrol count:%d\n", vCount, successCount);
    }
    // 流控降下来
    ret = FlowControlDown(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);

    // free
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
        while (ret == GMERR_COMMON_STREAM_OVERLOAD) {
            ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
        }
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        EXPECT_EQ(GMERR_OK, ret);
    }
#endif
}

// 018. 创建vertexLabel,插数据至server过载触发流控，调用GmcTransStart返回流控错误码
TEST_F(FlowControl_CS, DFX_010_FlowControl_CS_018)
{
#ifdef DIRECT_WRITE
#else
    int ret = 0;
	char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_COMMON_STREAM_OVERLOAD);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    char Label_config[] = "{\"max_record_num\":1000000, \"isFastReadUncommitted\":0}";

    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    //触发流控
    int write_num = 0;
    while (ret != GMERR_COMMON_STREAM_OVERLOAD) {
        ret = TriggerFlowControl(write_num);
        if (ret == GMERR_PROGRAM_LIMIT_EXCEEDED) {
            break;
        }
        write_num++;
    }

    // GmcTransStart
    int vCount = vertexCount;
    int successCount = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    const char *ErrInfo = "Overflow control. Starting transaction reached limit";
    for (int i = vertexCount; i < (vertexCount + vCount); i++) {
        ret = GmcTransStart(g_conn, &config);
        if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
            ret = testGmcGetLastError(ErrInfo);
            EXPECT_EQ(GMERR_OK, ret);
            successCount++;
        }
        ret = GmcTransCommit(g_conn); // 仅验证GmcTransStart返回流控错误码
    }
    if (successCount < 1) {
        EXPECT_EQ(0, 1);
    } else {
        printf("GmcTransStart count:%d, trigger flowcontrol count:%d\n", vCount, successCount);
    }
    // 流控降下来
    ret = FlowControlDown(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);

    // free
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
        while (ret == GMERR_COMMON_STREAM_OVERLOAD) {
            ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
        }
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        EXPECT_EQ(GMERR_OK, ret);
    }
#endif
}

// 019. 创建vertexLabel,插数据至server过载触发流控，调用GmcDirectFetchVertex返回流控错误码
TEST_F(FlowControl_CS, DFX_010_FlowControl_CS_019)
{
#ifdef DIRECT_WRITE
#else
    int ret = 0;
	char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_COMMON_STREAM_OVERLOAD);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    //触发流控
    int write_num = 0;
    while (ret != GMERR_COMMON_STREAM_OVERLOAD) {
        ret = TriggerFlowControl(write_num);
        if (ret == GMERR_PROGRAM_LIMIT_EXCEEDED) {
            break;
        }
        write_num++;
    }

    // GmcDirectFetchVertex
    int vCount = vertexCount * 2;
    int successCount = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < vCount; i++) {
        uint32_t pk = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
            ret = testGmcGetLastError(FlowCtlErrInfo);
            EXPECT_EQ(GMERR_OK, ret);
            successCount++;
        }
    }
    if (successCount >
        0) {  // GmcDirectFetchVertex接口与服务端无发送消息接收消息交互不涉及流控，确认人：徐映宇 00428073 2021/04/07
        EXPECT_EQ(0, 1);
    } else {
        printf("GmcDirectFetchVertex count:%d, trigger flowcontrol count:%d\n", vCount, successCount);
    }
    // 流控降下来
    ret = FlowControlDown(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);

    // free
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
        while (ret == GMERR_COMMON_STREAM_OVERLOAD) {
            ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
        }
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        EXPECT_EQ(GMERR_OK, ret);
    }
#endif
}

// 020. 创建vertexLabel,插数据至server过载触发流控，调用GmcExecScanVertex返回流控错误码
TEST_F(FlowControl_CS, DFX_010_FlowControl_CS_020)
{
#ifdef DIRECT_WRITE
#else
    int ret = 0;
	char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_COMMON_STREAM_OVERLOAD);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    //触发流控
    int write_num = 0;
    while (ret != GMERR_COMMON_STREAM_OVERLOAD) {
        ret = TriggerFlowControl(write_num);
        if (ret == GMERR_PROGRAM_LIMIT_EXCEEDED) {
            break;
        }
        write_num++;
    }

    // GmcExecScanVertex
    int count = vertexCount;
    int successCount = 0;
    for (int i = 0; i < count; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
            ret = testGmcGetLastError(FlowCtlErrInfo);
            EXPECT_EQ(GMERR_OK, ret);
            successCount++;
        } else {
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    if (successCount >= 1) {  // 全表扫走直连读：直连读进程在业务侧, 故不会返回流控错误码
        EXPECT_EQ(0, 1);
    } else {
        printf("GmcExecScanVertex count:%d, trigger flowcontrol count:%d\n", count, successCount);
    }

    // 流控降下来
    ret = FlowControlDown(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);

    // free
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
        while (ret == GMERR_COMMON_STREAM_OVERLOAD) {
            ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
        }
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        EXPECT_EQ(GMERR_OK, ret);
    }
#endif
}

// 021. 创建vertexLabel,插数据至server过载触发流控，调用GmcExecScanVertexByCmd返回流控错误码
TEST_F(FlowControl_CS, DFX_010_FlowControl_CS_021)
{
#ifdef DIRECT_WRITE
#else
    int ret = 0;
	char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_COMMON_STREAM_OVERLOAD);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    //触发流控
    int write_num = 0;
    while (ret != GMERR_COMMON_STREAM_OVERLOAD) {
        ret = TriggerFlowControl(write_num);
        if (ret == GMERR_PROGRAM_LIMIT_EXCEEDED) {
            break;
        }
        write_num++;
    }

    // GmcExecScanVertexByCmd
    int count = vertexCount;
    int successCount = 0;
    const char *condStr = "F2<2";
    for (int i = 0; i < count; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetFilter(g_stmt, condStr);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetOutputFormat(g_stmt, NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
            ret = testGmcGetLastError(FlowCtlErrInfo);
            EXPECT_EQ(GMERR_OK, ret);
            successCount++;
        } else {
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    if (successCount < 1) {
        EXPECT_EQ(0, 1);
    } else {
        printf("GmcExecScanVertexByCmd count:%d, trigger flowcontrol count:%d\n", count, successCount);
    }

    // 流控降下来
    ret = FlowControlDown(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);

    // free
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
        while (ret == GMERR_COMMON_STREAM_OVERLOAD) {
            ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
        }
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        EXPECT_EQ(GMERR_OK, ret);
    }
#endif
}


// 026. 创建vertexLabel,插数据至server过载触发流控，批量DML操作返回流控错误码
TEST_F(FlowControl_CS, DFX_010_FlowControl_CS_026)
{
#ifdef DIRECT_WRITE
#else
    int ret = 0;
	char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_COMMON_STREAM_OVERLOAD);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // batch insert
    int vCount = vertexCount;
    int successCount = 0;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    //触发流控
    int write_num = 0;
    while (ret != GMERR_COMMON_STREAM_OVERLOAD) {
        ret = TriggerFlowControl(write_num);
        if (ret == GMERR_PROGRAM_LIMIT_EXCEEDED) {
            break;
        }
        write_num++;
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
    int i = vertexCount;
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    do {
        uint32_t pk = i;
        int val = i;
        int64_t sk = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "T39_K0");
        EXPECT_EQ(GMERR_OK, ret);
        // set_VertexProperty_PK(g_stmt, pk);
        set_VertexProperty_SK(g_stmt, sk);
        set_VertexProperty(g_stmt, val);
        ret = GmcBatchAddDML(batch, g_stmt);
        // EXPECT_EQ(GMERR_OK, ret);
        if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
            break;
        }
        unsigned int totalNum = 0;
        unsigned int successNum = 0;
        // ret = GmcBatchExecute(g_stmt, &totalNum, &successNum);
        ret = GmcBatchExecute(batch, &batchRet);
        // EXPECT_EQ(GMERR_OK, ret);
        if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
            break;
        }
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        // ASSERT_EQ(GMERR_INVALID_PROPERTY, ret);
        if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
            break;
        }
        i++;
        ret = GmcBatchReset(batch);
        EXPECT_EQ(GMERR_OK, ret);
    } while (i < vertexCount * 5 && ret == GMERR_OK);
    EXPECT_EQ(GMERR_COMMON_STREAM_OVERLOAD, ret);
    ret = testGmcGetLastError("Overflow control. Sending request reached limit");
    EXPECT_EQ(GMERR_OK, ret);
    printf("GmcBatchExecute DML count:%d\n", i);
    // free
    GmcBatchDestroy(batch);
    // 流控降下来
    ret = FlowControlDown(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);

    // free
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
        while (ret == GMERR_COMMON_STREAM_OVERLOAD) {
            ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
        }
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        EXPECT_EQ(GMERR_OK, ret);
    }
#endif
}

// 027. 创建vertexLabel,插数据至server过载触发流控，批量DDL操作返回流控错误码
TEST_F(FlowControl_CS, DFX_010_FlowControl_CS_027)
{
#ifdef DIRECT_WRITE
#else
    int ret = 0;
	char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_COMMON_STREAM_OVERLOAD);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // batch insert
    int vCount = vertexCount;
    int successCount = 0;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    //触发流控
    int write_num = 0;
    while (ret != GMERR_COMMON_STREAM_OVERLOAD) {
        ret = TriggerFlowControl(write_num);
        if (ret == GMERR_PROGRAM_LIMIT_EXCEEDED) {
            break;
        }
        write_num++;
    }
    char labelName[20] = "";
    char g_label_schema[1024] = "";
    for (int num = vertexCount; num < (vertexCount + vCount); num++) {
        ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
        EXPECT_EQ(GMERR_OK, ret);
        snprintf(g_label_schema, 1024,
            "[{\"type\":\"record\", \"name\":\"T%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
            "{\"name\":\"F1\", \"type\":\"int32\"}],"
            "\"keys\":[{\"node\":\"T%d\", \"name\":\"T%d_K0\", \"fields\":[\"F0\"], "
            "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
            num, num, num);
        snprintf(labelName, 20, "T%d", num);
        ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, labelName, g_label_schema, normal_config_json);
        if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
            ret = testGmcGetLastError("Flow control. Overflow control. Sending request reached limit");
            EXPECT_EQ(GMERR_OK, ret);
            successCount++;
            break;
        }
        unsigned int totalNum = 0;
        unsigned int successNum = 0;
        // ret = GmcBatchExecute(g_stmt, &totalNum, &successNum);
        ret = GmcBatchExecute(batch, &batchRet);
        if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
            ret = testGmcGetLastError("Overflow control. Sending request reached limit");
            EXPECT_EQ(GMERR_OK, ret);
            successCount++;
            break;
        }
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
            ret = testGmcGetLastError("Overflow control. Sending request reached limit");
            EXPECT_EQ(GMERR_OK, ret);
            successCount++;
            break;
        } else {
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcBatchReset(batch);
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (successCount < 1) {
        EXPECT_EQ(0, 1);
    } else {
        printf("GmcBatchExecute DDL count:%d, trigger flowcontrol count:%d\n", vCount, successCount);
    }
    // free
    GmcBatchDestroy(batch);

    // 流控降下来
    ret = FlowControlDown(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
    // drop vertexLabel
    for (uint32_t num = 0; num < (vertexCount + vCount); num++) {
        snprintf(labelName, 20, "T%d", num);
        ret = GmcDropVertexLabel(g_stmt, labelName);
        if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
            while (ret == GMERR_COMMON_STREAM_OVERLOAD) {
                ret = GmcDropVertexLabel(g_stmt, labelName);
            }
            EXPECT_EQ(true, ret == GMERR_OK || ret == GMERR_UNDEFINED_TABLE); // drop返回9010是因为流控触发建表失败
        } else {
            EXPECT_EQ(true, ret == GMERR_OK || ret == GMERR_UNDEFINED_TABLE);
        }
    }

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
#endif
}

// 028. 创建vertexLabel,插数据至server过载触发流控，返回1-3级错误码
//错误码已不区分流控等级
TEST_F(FlowControl_CS, DFX_010_FlowControl_CS_028)
{
#ifdef DIRECT_WRITE
#else
    int ret = 0;
	char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_COMMON_STREAM_OVERLOAD);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    int count = 100000;
    int l1_count = 0;
    int l2_count = 0;
    int l3_count = 0;
    int ok_count = 0;
    int fail_count = 0;
    for (int i = 0; i < count; i++) {
        uint32_t pk = i;
        int val = i;
        int64_t sk = i;
        set_VertexProperty_PK(g_stmt_async, pk);
        set_VertexProperty_SK(g_stmt_async, sk);
        set_VertexProperty(g_stmt_async, val);
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        switch (ret) {
            case GMERR_COMMON_STREAM_OVERLOAD:
                ret = testGmcGetLastError(FlowCtlErrInfo);
                EXPECT_EQ(GMERR_OK, ret);
                l1_count++;
                break;
            case GMERR_OK:
                ret = testWaitAsyncRecv(&data);
                ok_count++;
                break;
            default:
                fail_count++;
        }
    }

    // 流控降下来
    ret = FlowControlDown(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);

    // free
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
        while (ret == GMERR_COMMON_STREAM_OVERLOAD) {
            ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
        }
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        EXPECT_EQ(GMERR_OK, ret);
    }
#endif
}

// 029. 创建vertexLabel,插数据至server过载触发流控，异步批量DML操作返回流控错误码
TEST_F(FlowControl_CS, DFX_010_FlowControl_CS_029)
{
#ifdef DIRECT_WRITE
#else
    int ret = 0;
	char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_COMMON_STREAM_OVERLOAD);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // batch insert
    int vCount = vertexCount * 3;
    int successCount = 0;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    //触发流控
    int write_num = 0;
    while (ret != GMERR_COMMON_STREAM_OVERLOAD) {
        ret = TriggerFlowControl(write_num);
        if (ret == GMERR_PROGRAM_LIMIT_EXCEEDED) {
            break;
        }
        write_num++;
    }
    // TriggerFlowControl(vertexCount*3);
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = vertexCount; i < (vertexCount + vCount); i++) {
        ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
        ASSERT_EQ(GMERR_OK, ret);
        printf("i: %d\n", i);
        uint32_t pk = i;
        int val = i;
        int64_t sk = i;
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_async, "T39_K0");
        EXPECT_EQ(GMERR_OK, ret);
        // set_VertexProperty_PK(g_stmt_async, pk);
        set_VertexProperty_SK(g_stmt_async, sk);
        set_VertexProperty(g_stmt_async, val);
        ret = GmcBatchAddDML(batch, g_stmt_async);
        if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
            printf("GmcBatchAddDML\n");
            ret = testGmcGetLastError(FlowCtlErrInfo);
            EXPECT_EQ(GMERR_OK, ret);
            successCount++;
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
        unsigned int totalNum = 0;
        unsigned int successNum = 0;
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        if (ret == GMERR_COMMON_STREAM_OVERLOAD) {  // 异步批量接口获取流控
            printf("GmcBatchExecuteAsync\n");
            ret = testGmcGetLastError(FlowCtlErrInfo);
            EXPECT_EQ(GMERR_OK, ret);
            successCount++;
            break;
        } else {
            EXPECT_EQ(GMERR_OK, ret);
            ret = testWaitAsyncRecv(&data);
        }
        ret = GmcBatchReset(batch);
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (successCount < 1) {
        EXPECT_EQ(0, 1);
    } else {
        printf("GmcBatchExecute DML count:%d, trigger flowcontrol count:%d\n", vCount, successCount);
    }
    // free
    GmcBatchDestroy(batch);
    // 流控降下来
    ret = FlowControlDown(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);

    // free
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
        while (ret == GMERR_COMMON_STREAM_OVERLOAD) {
            ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
        }
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        EXPECT_EQ(GMERR_OK, ret);
    }
#endif
}

// 030. 创建vertexLabel,插数据至server过载触发流控，异步批量DDL操作返回流控错误码
TEST_F(FlowControl_CS, DFX_010_FlowControl_CS_030)
{
#ifdef DIRECT_WRITE
#else
    int ret = 0;
	char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_COMMON_STREAM_OVERLOAD);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // batch insert
    int vCount = vertexCount;
    int successCount = 0;
    //触发流控
    int write_num = 0;
    while (ret != GMERR_COMMON_STREAM_OVERLOAD) {
        ret = TriggerFlowControl(write_num);
        if (ret == GMERR_PROGRAM_LIMIT_EXCEEDED) {
            break;
        }
        write_num++;
    }
    AsyncUserDataT data = {0};
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    const char *json = R"([{"type":"record", "name":"T0", "fields":[{"name":"F0", "type":"int32"},
            {"name":"F1", "type":"int32"}],
            "keys":[{"node":"T0", "name":"T0_K0", "fields":["F0"],
            "index":{"type":"primary"},"constraints":{"unique":true}}]}])";
    while (true) {
        ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, "T0", json, normal_config_json);
        if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
            printf("GmcBatchAddDDL\n");
            ret = testGmcGetLastError(FlowCtlErrInfo);
            EXPECT_EQ(GMERR_OK, ret);
            successCount++;
            break;
        }
        unsigned int totalNum = 0;
        unsigned int successNum = 0;
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
            printf("GmcBatchExecuteAsync\n");
            ret = testGmcGetLastError(FlowCtlErrInfo);
            EXPECT_EQ(GMERR_OK, ret);
            successCount++;
            break;
        } else {
            EXPECT_EQ(GMERR_OK, ret);
            ret = testWaitAsyncRecv(&data);
            (g_conn_async);
        }
        ret = GmcBatchReset(batch);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcDropVertexLabel(g_stmt, "T0");
        while (ret == GMERR_COMMON_STREAM_OVERLOAD) {
            ret = GmcDropVertexLabel(g_stmt, "T0");
        }
        EXPECT_EQ(GMERR_OK, ret);
    }
    if (successCount < 1) {
        EXPECT_EQ(0, 1);
    } else {
        printf("GmcBatchExecute DDL count:%d, trigger flowcontrol count:%d\n", vCount, successCount);
    }

    // free
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchDestroy(batch);
#endif
}
