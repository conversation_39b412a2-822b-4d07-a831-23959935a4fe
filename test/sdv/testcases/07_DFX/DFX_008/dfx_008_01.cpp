#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"

GmcConnT *g_conn;
GmcStmtT *g_stmt;
int ret = 0;

GmcConnT *g_connAsync = NULL;
GmcStmtT *g_stmtAsync = NULL;
const char *g_nspName = "DML008";

void testClearNsp(GmcStmtT *stmt, const char *namespaceName)
{
    AsyncUserDataT asynData = {0};
    int ret = GmcUseNamespaceAsync(stmt, namespaceName, use_namespace_callback, &asynData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asynData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asynData.status);
    ret = GmcClearNamespaceAsync(stmt, namespaceName, drop_namespace_callback, &asynData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asynData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asynData.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&asynData, 0, sizeof(AsyncUserDataT));
}
 
void useNameSpaceAsync(GmcStmtT *asyncStmt, const char *nameSpaceName)
{
    AsyncUserDataT asynData = {0};
    int ret = GmcUseNamespaceAsync(asyncStmt, nameSpaceName, use_namespace_callback, &asynData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asynData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asynData.status);
    memset(&asynData, 0, sizeof(AsyncUserDataT));
}

#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];

const char *g_label_config = R"(
        {
            "max_record_num_check":false,
            "max_record_num":100000,
            "isFastReadUncommitted":0
        })";

class DFX_008_01 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh");
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        ASSERT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void DFX_008_01::SetUp()
{
    ret = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_connAsync, &g_stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespace(g_stmt, g_nspName, g_userName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_nspName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}
void DFX_008_01::TearDown()
{
    AW_CHECK_LOG_END();
    testClearNsp(g_stmtAsync, g_nspName);
    ret = GmcDropNamespace(g_stmt, g_nspName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connAsync, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

const char *g_label_name = "test_schema";

const char *edgeLabelName = "edgelable";
const char *cfgJson = R"({"max_record_num":1000, "isFastReadUncommitted":0})";
const char *edgeSrcVertexLabelName = "srcVertexLabel";
const char *srccfgJson1 = R"({"max_record_num":1000, "isFastReadUncommitted":0})";
const char *edgeDstVertexLabelName = "dstVertexLabel";
const char *dstcfgJson1 = R"({"max_record_num":1000, "isFastReadUncommitted":0})";

/*****************************************************************************
 * Description  : 001 创建1个边，查看视图
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2020.11.21
 * Modification : Create function
 *
 * *****************************************************************************/

TEST_F(DFX_008_01, DFX_008_01_001)
{
    char *src_vertex_schema = NULL;
    char *dst_vertex_schema = NULL;
    char *edge_schema = NULL;

    //读取label schema
    readJanssonFile("schema_file/srcLabel.gmjson", &src_vertex_schema);
    ASSERT_NE((void *)NULL, src_vertex_schema);
    printf(RED("src_vertex_schema:%s\n"), src_vertex_schema);

    readJanssonFile("schema_file/dstLabel.gmjson", &dst_vertex_schema);
    ASSERT_NE((void *)NULL, dst_vertex_schema);
    printf(RED("dst_vertex_schema:%s\n"), dst_vertex_schema);

    //读取edge schema
    readJanssonFile("schema_file/edgeLabel.gmjson", &edge_schema);
    ASSERT_NE((void *)NULL, edge_schema);
    printf(RED("edge_schema:%s\n"), edge_schema);

    // 创建 src vertexLable
    ret = GmcCreateVertexLabel(g_stmt, src_vertex_schema, srccfgJson1);
    ASSERT_EQ(GMERR_OK, ret);
    // 创建 dest vertexLable
    ret = GmcCreateVertexLabel(g_stmt, dst_vertex_schema, dstcfgJson1);
    ASSERT_EQ(GMERR_OK, ret);
    // 创建 edgeLable
    ret = GmcCreateEdgeLabel(g_stmt, edge_schema, cfgJson);
    ASSERT_EQ(GMERR_OK, ret);

    // set srcVertex for row data
    ret = testGmcPrepareStmtByLabelName(g_stmt, edgeSrcVertexLabelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    int F0Value = 12;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
    ASSERT_EQ(GMERR_OK, ret);
    int F1Value = 13;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
    ASSERT_EQ(GMERR_OK, ret);
    int F2Value = 132;
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    ASSERT_EQ(GMERR_OK, ret);
    int F3Value = 321;
    ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_INT32, &F3Value, sizeof(F3Value));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, edgeSrcVertexLabelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    int value = 12;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "srcVertexLabel_PK0");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    bool eof = false;
    ret = GmcFetch(g_stmt, &eof);
    ASSERT_EQ(GMERR_OK, ret);

    // set dstVertex for row data
    ret = testGmcPrepareStmtByLabelName(g_stmt, edgeDstVertexLabelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    F0Value = 22;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
    ASSERT_EQ(GMERR_OK, ret);
    F1Value = 23;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
    ASSERT_EQ(GMERR_OK, ret);
    F2Value = 132;
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    ASSERT_EQ(GMERR_OK, ret);
    F3Value = 321;
    ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_INT32, &F3Value, sizeof(F3Value));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, edgeDstVertexLabelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    value = 22;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "dstVertexLabel_PK0");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    eof = false;
    ret = GmcFetch(g_stmt, &eof);
    ASSERT_EQ(GMERR_OK, ret);


    char const *view_name = "V\\$STORAGE_EDGE_LABEL_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s\n", g_toolPath, g_connServer,
        view_name);
    printf(GREEN("g_command = %s\n"), g_command);
    ret = executeCommand(
        g_command, "EDGELABEL_ID", "EDGELABEL_NAME", "FIXED_HEAP_PAGE_HEAD_SIZE", "FIXED_HEAP_ROW_HEAD_SIZE");
    ASSERT_EQ(GMERR_OK, ret);


    free(src_vertex_schema);
    free(dst_vertex_schema);
    free(edge_schema);
}

/*****************************************************************************
 * Description  : 002 创建10000个边，查看视图
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2020.11.21
 * Modification : Create function
 *
 * ./gmsysview -q  V\$ STORAGE_EDGE_LABEL_STAT  -e  RTOS  –s  usocket:/run/verona/unix_emserver
 * *****************************************************************************/

TEST_F(DFX_008_01, DFX_008_01_002)
{
    char *src_vertex_schema = NULL;
    char *dst_vertex_schema = NULL;
    char *edge_schema = NULL;
    void *srcVertexLabel = NULL;
    void *dstVertexLabel = NULL;

    //读取label schema
    readJanssonFile("schema_file/srcLabel.gmjson", &src_vertex_schema);
    ASSERT_NE((void *)NULL, src_vertex_schema);
    printf(RED("src_vertex_schema:%s\n"), src_vertex_schema);

    readJanssonFile("schema_file/dstLabel.gmjson", &dst_vertex_schema);
    ASSERT_NE((void *)NULL, dst_vertex_schema);
    printf(RED("dst_vertex_schema:%s\n"), dst_vertex_schema);

    //读取edge schema
    readJanssonFile("schema_file/edgeLabel.gmjson", &edge_schema);
    ASSERT_NE((void *)NULL, edge_schema);
    printf(RED("edge_schema:%s\n"), edge_schema);

    // 创建 src vertexLable
    ret = GmcCreateVertexLabel(g_stmt, src_vertex_schema, srccfgJson1);
    ASSERT_EQ(GMERR_OK, ret);
    // 创建 dest vertexLable
    ret = GmcCreateVertexLabel(g_stmt, dst_vertex_schema, dstcfgJson1);
    ASSERT_EQ(GMERR_OK, ret);
    // 创建 edgeLable
    ret = GmcCreateEdgeLabel(g_stmt, edge_schema, cfgJson);
    ASSERT_EQ(GMERR_OK, ret);

    // set srcVertex for row data
    // set dstVertex for row data
    int32_t i;
    int32_t value;
    for (i = 0; i < 1; i++) {
        printf(RED("loop i = %d\n"), i);
        ret = testGmcPrepareStmtByLabelName(g_stmt, edgeSrcVertexLabelName, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);

        int F0Value = i;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        ASSERT_EQ(GMERR_OK, ret);
        int F1Value = i;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
        ASSERT_EQ(GMERR_OK, ret);
        int F2Value = i;
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
        ASSERT_EQ(GMERR_OK, ret);
        int F3Value = i;
        ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_INT32, &F3Value, sizeof(F3Value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmt, edgeSrcVertexLabelName, GMC_OPERATION_SCAN);
        ASSERT_EQ(GMERR_OK, ret);
        value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "srcVertexLabel_PK0");
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool eof = false;
        ret = GmcFetch(g_stmt, &eof);
        ASSERT_EQ(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmt, edgeDstVertexLabelName, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        F0Value = i;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        ASSERT_EQ(GMERR_OK, ret);
        F1Value = i;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
        ASSERT_EQ(GMERR_OK, ret);
        F2Value = i;
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
        ASSERT_EQ(GMERR_OK, ret);
        F3Value = i;
        ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_INT32, &F3Value, sizeof(F3Value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmt, edgeDstVertexLabelName, GMC_OPERATION_SCAN);
        ASSERT_EQ(GMERR_OK, ret);
        value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "dstVertexLabel_PK0");
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);
        eof = false;
        ret = GmcFetch(g_stmt, &eof);
        ASSERT_EQ(GMERR_OK, ret);
    }

    printf(RED("loop i end"));
    

    char const *view_name = "V\\$STORAGE_EDGE_LABEL_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s\n", g_toolPath, g_connServer,
        view_name);
    printf(GREEN("g_command = %s\n"), g_command);
    ret = executeCommand(
        g_command, "EDGELABEL_ID", "EDGELABEL_NAME", "FIXED_HEAP_PAGE_HEAD_SIZE", "FIXED_HEAP_ROW_HEAD_SIZE");
    ASSERT_EQ(GMERR_OK, ret);


    free(src_vertex_schema);
    free(dst_vertex_schema);
    free(edge_schema);
}

/*****************************************************************************
 * Description  : 003 循环创建一个边，查看视图
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2020.11.21
 * Modification : Create function
 *
 * ./gmsysview -q  V\$ STORAGE_EDGE_LABEL_STAT  -e  RTOS  –s  usocket:/run/verona/unix_emserver
 * *****************************************************************************/

TEST_F(DFX_008_01, DFX_008_01_003)
{
    char *src_vertex_schema = NULL;
    char *dst_vertex_schema = NULL;
    char *edge_schema = NULL;

    //读取label schema
    readJanssonFile("schema_file/srcLabel.gmjson", &src_vertex_schema);
    ASSERT_NE((void *)NULL, src_vertex_schema);
    printf(RED("src_vertex_schema:%s\n"), src_vertex_schema);

    readJanssonFile("schema_file/dstLabel.gmjson", &dst_vertex_schema);
    ASSERT_NE((void *)NULL, dst_vertex_schema);
    printf(RED("dst_vertex_schema:%s\n"), dst_vertex_schema);

    //读取edge schema
    readJanssonFile("schema_file/edgeLabel.gmjson", &edge_schema);
    ASSERT_NE((void *)NULL, edge_schema);
    printf(RED("edge_schema:%s\n"), edge_schema);

    // 创建 src vertexLable
    ret = GmcCreateVertexLabel(g_stmt, src_vertex_schema, srccfgJson1);
    ASSERT_EQ(GMERR_OK, ret);
    // 创建 dest vertexLable
    ret = GmcCreateVertexLabel(g_stmt, dst_vertex_schema, dstcfgJson1);
    ASSERT_EQ(GMERR_OK, ret);
    // 创建 edgeLable
    ret = GmcCreateEdgeLabel(g_stmt, edge_schema, cfgJson);
    ASSERT_EQ(GMERR_OK, ret);

    char const *view_name = "V\\$STORAGE_EDGE_LABEL_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s\n", g_toolPath, g_connServer,
        view_name);
    printf(GREEN("g_command = %s\n"), g_command);

    // set srcVertex for row data
    // set dstVertex for row data
    int32_t i;
    int32_t value;
    for (i = 0; i < 100; i++) {
        printf(GREEN("i = %d\n"), i);
        ret = testGmcPrepareStmtByLabelName(g_stmt, edgeSrcVertexLabelName, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);

        int F0Value = i;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        ASSERT_EQ(GMERR_OK, ret);
        int F1Value = i;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
        ASSERT_EQ(GMERR_OK, ret);
        int F2Value = i;
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
        ASSERT_EQ(GMERR_OK, ret);
        int F3Value = i;
        ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_INT32, &F3Value, sizeof(F3Value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmt, edgeSrcVertexLabelName, GMC_OPERATION_SCAN);
        ASSERT_EQ(GMERR_OK, ret);
        value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "srcVertexLabel_PK0");
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool eof = false;
        ret = GmcFetch(g_stmt, &eof);
        ASSERT_EQ(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmt, edgeDstVertexLabelName, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        F0Value = i;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        ASSERT_EQ(GMERR_OK, ret);
        F1Value = i;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
        ASSERT_EQ(GMERR_OK, ret);
        F2Value = i;
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
        ASSERT_EQ(GMERR_OK, ret);
        F3Value = i;
        ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_INT32, &F3Value, sizeof(F3Value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmt, edgeDstVertexLabelName, GMC_OPERATION_SCAN);
        ASSERT_EQ(GMERR_OK, ret);
        value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "dstVertexLabel_PK0");
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);
        eof = false;
        ret = GmcFetch(g_stmt, &eof);
        ASSERT_EQ(GMERR_OK, ret);

        ret = executeCommand(
            g_command, "EDGELABEL_ID", "EDGELABEL_NAME", "FIXED_HEAP_PAGE_HEAD_SIZE", "FIXED_HEAP_ROW_HEAD_SIZE");
        ASSERT_EQ(GMERR_OK, ret);
    }

    free(src_vertex_schema);
    free(dst_vertex_schema);
    free(edge_schema);
}

void *Thread_DFX_008_04(void *args)
{
    char command[MAX_CMD_SIZE];
    char const *view_name = "V\\$STORAGE_EDGE_LABEL_STAT";
    snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s\n", g_toolPath, g_connServer,
        view_name);
    printf(GREEN("command = %s\n"), command);
    for (uint32_t i = 0; i < 3; i++) {
        printf(GREEN("i = %d\n"), i);
        ret = executeCommand(
            command, "EDGELABEL_ID", "EDGELABEL_NAME", "FIXED_HEAP_PAGE_HEAD_SIZE", "FIXED_HEAP_ROW_HEAD_SIZE");
        EXPECT_EQ(GMERR_OK, ret);
    }
    return NULL;
}
/*****************************************************************************
 * Description  : 004 创建一个边，多线程循环查看视图
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2020.11.21
 * Modification : Create function
 *
 * ./gmsysview -q  V\$ STORAGE_EDGE_LABEL_STAT  -e  RTOS  –s  usocket:/run/verona/unix_emserver
 * *****************************************************************************/
TEST_F(DFX_008_01, DFX_008_01_004)
{
    char *src_vertex_schema = NULL;
    char *dst_vertex_schema = NULL;
    char *edge_schema = NULL;

    //读取label schema
    readJanssonFile("schema_file/srcLabel.gmjson", &src_vertex_schema);
    ASSERT_NE((void *)NULL, src_vertex_schema);
    printf(RED("src_vertex_schema:%s\n"), src_vertex_schema);

    readJanssonFile("schema_file/dstLabel.gmjson", &dst_vertex_schema);
    ASSERT_NE((void *)NULL, dst_vertex_schema);
    printf(RED("dst_vertex_schema:%s\n"), dst_vertex_schema);

    //读取edge schema
    readJanssonFile("schema_file/edgeLabel.gmjson", &edge_schema);
    ASSERT_NE((void *)NULL, edge_schema);
    printf(RED("edge_schema:%s\n"), edge_schema);

    // 创建 src vertexLable
    ret = GmcCreateVertexLabel(g_stmt, src_vertex_schema, srccfgJson1);
    ASSERT_EQ(GMERR_OK, ret);
    // 创建 dest vertexLable
    ret = GmcCreateVertexLabel(g_stmt, dst_vertex_schema, dstcfgJson1);
    ASSERT_EQ(GMERR_OK, ret);
    // 创建 edgeLable
    ret = GmcCreateEdgeLabel(g_stmt, edge_schema, cfgJson);
    ASSERT_EQ(GMERR_OK, ret);

    // set srcVertex for row data
    ret = testGmcPrepareStmtByLabelName(g_stmt, edgeSrcVertexLabelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    int F0Value = 12;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
    ASSERT_EQ(GMERR_OK, ret);
    int F1Value = 13;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
    ASSERT_EQ(GMERR_OK, ret);
    int F2Value = 132;
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    ASSERT_EQ(GMERR_OK, ret);
    int F3Value = 321;
    ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_INT32, &F3Value, sizeof(F3Value));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, edgeSrcVertexLabelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    int value = 12;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "srcVertexLabel_PK0");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    bool eof = false;
    ret = GmcFetch(g_stmt, &eof);
    ASSERT_EQ(GMERR_OK, ret);

    // set dstVertex for row data
    ret = testGmcPrepareStmtByLabelName(g_stmt, edgeDstVertexLabelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    F0Value = 22;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
    ASSERT_EQ(GMERR_OK, ret);
    F1Value = 23;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
    ASSERT_EQ(GMERR_OK, ret);
    F2Value = 132;
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    ASSERT_EQ(GMERR_OK, ret);
    F3Value = 321;
    ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_INT32, &F3Value, sizeof(F3Value));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, edgeDstVertexLabelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    value = 22;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "dstVertexLabel_PK0");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    eof = false;
    ret = GmcFetch(g_stmt, &eof);
    ASSERT_EQ(GMERR_OK, ret);

    free(src_vertex_schema);
    free(dst_vertex_schema);
    free(edge_schema);
}

/*****************************************************************************
 * Description  : 005 创建1个顶点，查看视图
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2020.11.21
 * Modification : Create function
 *
 * ./gmsysview -q  V\$ STORAGE_EDGE_LABEL_STAT  -e  RTOS  –s  usocket:/run/verona/unix_emserver
 * *****************************************************************************/
TEST_F(DFX_008_01, DFX_008_01_005)
{
    int32_t value0 = 1;
    uint32_t value1 = 10;
    char *test_schema = NULL;
    void *vertexLabel = NULL;

    readJanssonFile("schema_file/test_schema.gmjson", &test_schema);
    ASSERT_NE((void *)NULL, test_schema);
    printf(RED("test_schema:%s\n"), test_schema);

    ret = GmcCreateVertexLabel(g_stmt, test_schema, g_label_config);
    ASSERT_EQ(GMERR_OK, ret);

    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    value0 = 1;
    value1 = 1;
    //设置F0属性值
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &value0, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    //设置F1属性值
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &value1, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    //插入顶点
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    char const *view_name = "V\\$STORAGE_HEAP_VERTEX_LABEL_STAT";
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name, GMC_OPERATION_INSERT);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name, GMC_OPERATION_INSERT);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s\n", g_toolPath, g_connServer,
        view_name);
    printf(GREEN("g_command = %s\n"), g_command);
    ret = executeCommand(g_command, "VERTEXLABEL_ID", "VERTEXLABEL_NAME", "HEAP_PAGE_HEAD_SIZE", "HEAP_ROW_HEAD_SIZE");
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, g_label_name);
    ASSERT_EQ(GMERR_OK, ret);
    free(test_schema);
}


void *Thread_DFX_008_08(void *args)
{
    char command[MAX_CMD_SIZE];
    char const *view_name = "V\\$STORAGE_HEAP_VERTEX_LABEL_STAT";
    snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s\n", g_toolPath, g_connServer,
        view_name);
    printf(GREEN("command = %s\n"), command);
    for (uint32_t i = 0; i < 10; i++) {
        ret =
            executeCommand(command, "VERTEXLABEL_ID", "VERTEXLABEL_NAME", "HEAP_PAGE_HEAD_SIZE", "HEAP_ROW_HEAD_SIZE");
        EXPECT_EQ(GMERR_OK, ret);
    }
    return NULL;
}


/*****************************************************************************
 * Description  : 009 插入一个表 插入一个顶点 更新一个顶点 查询个顶点  删除一个顶点，查看undolog的内存信息输出
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2020.11.21
 * Modification : Create function
 *
 * *****************************************************************************/

TEST_F(DFX_008_01, DFX_008_01_009)
{
    int32_t value0 = 1;
    uint32_t value1 = 10;
    char *test_schema = NULL;
    void *vertexLabel = NULL;

    readJanssonFile("schema_file/test_schema.gmjson", &test_schema);
    ASSERT_NE((void *)NULL, test_schema);
    printf(RED("test_schema:%s\n"), test_schema);

    ret = GmcCreateVertexLabel(g_stmt, test_schema, g_label_config);
    ASSERT_EQ(GMERR_OK, ret);

    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    value0 = 1;
    value1 = 1;
    //设置F0属性值
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &value0, sizeof(int32_t));
    //设置F1属性值
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &value1, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    //插入顶点
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    value1 = 2;
    //更新数据前先reset
    ret = GmcResetVertex(g_stmt, false);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &value0, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    ASSERT_EQ(GMERR_OK, ret);
    //设置F1属性值
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &value1, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    //更新顶点
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &value0, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    ASSERT_EQ(GMERR_OK, ret);
    unsigned int sizeF1;
    uint32_t valueF1;
    bool isNull;

    //查询顶点
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    bool eof = false;
    ret = GmcFetch(g_stmt, &eof);
    ASSERT_EQ(GMERR_OK, ret);
    GmcGetVertexPropertySizeByName(g_stmt, "F1", &sizeF1);
    ret = GmcGetVertexPropertyByName(g_stmt, "F1", &valueF1, sizeF1, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    printf(GREEN("valueF1 = %d, value1 = %d, isNull = %d\n"), valueF1, value1, isNull);
    if (isNull == 0) {
        ret = 1;
        if (valueF1 == value1) {
            ret = 0;
        }
        ASSERT_EQ(0, ret);  //预期获取插入值
    } else {
        ASSERT_EQ(0, 1);
    }

    //删除顶点
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &value0, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeIndexKey(g_stmt);

    char const *view_name = "V\\$STORAGE_UNDO_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s\n", g_toolPath, g_connServer,
        view_name);
    printf(GREEN("g_command = %s\n"), g_command);
    ret = executeCommand(g_command, "UNDOLOG_ID", "ROLLBACK_SEGMENT_PAGE_NUM", "UNDOLOG_PAGE_NUM", "PAGE_SIZE",
        "TOTAL_INSERT_UNDOLOG_SIZE");
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, g_label_name);
    ASSERT_EQ(GMERR_OK, ret);

    free(test_schema);
}

/*****************************************************************************
 * Description  : 010 插入一个顶点，循环查看视图100次,查看undolog的内存信息输出
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2020.11.21
 * Modification : Create function
 *
 * *****************************************************************************/

TEST_F(DFX_008_01, DFX_008_01_010)
{
    int32_t value0 = 1;
    uint32_t value1 = 10;
    char *test_schema = NULL;
    void *vertexLabel = NULL;

    readJanssonFile("schema_file/test_schema.gmjson", &test_schema);
    ASSERT_NE((void *)NULL, test_schema);
    printf(RED("test_schema:%s\n"), test_schema);

    int ret = GmcCreateVertexLabel(g_stmt, test_schema, g_label_config);
    ASSERT_EQ(GMERR_OK, ret);

    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    value0 = 1;
    value1 = 1;
    //设置F0属性值
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &value0, sizeof(int32_t));
    //设置F1属性值
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &value1, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    //插入顶点
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    char const *view_name = "V\\$STORAGE_UNDO_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s\n", g_toolPath, g_connServer,
        view_name);
    printf(GREEN("g_command = %s\n"), g_command);
    for (uint32_t i = 0; i < 100; i++) {
        ret = executeCommand(g_command, "UNDOLOG_ID", "ROLLBACK_SEGMENT_PAGE_NUM", "UNDOLOG_PAGE_NUM", "PAGE_SIZE",
            "TOTAL_INSERT_UNDOLOG_SIZE");
        ASSERT_EQ(GMERR_OK, ret);
    }

    ret = GmcDropVertexLabel(g_stmt, g_label_name);
    ASSERT_EQ(GMERR_OK, ret);

    free(test_schema);
}

void *Thread_DFX_008_11(void *args)
{
    char command[MAX_CMD_SIZE];
    char const *view_name = "V\\$STORAGE_UNDO_STAT";
    snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s\n", g_toolPath, g_connServer,
        view_name);
    printf(GREEN("command = %s\n"), command);
    for (uint32_t i = 0; i < 10; i++) {
        printf(GREEN("i = %d\n"), i);
        ret = executeCommand(command, "UNDOLOG_ID", "ROLLBACK_SEGMENT_PAGE_NUM", "UNDOLOG_PAGE_NUM", "PAGE_SIZE",
            "TOTAL_INSERT_UNDOLOG_SIZE");
        EXPECT_EQ(GMERR_OK, ret);
    }
    return NULL;
}

/*****************************************************************************
 * Description  : 011 创建一个表，多线程循环查看视图,查看undolog的内存信息输出
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2020.11.21
 * Modification : Create function
 *
 * *****************************************************************************/

TEST_F(DFX_008_01, DFX_008_01_011)
{
    int32_t value0 = 1;
    uint32_t value1 = 10;
    char *test_schema = NULL;
    void *vertexLabel = NULL;

    readJanssonFile("schema_file/test_schema.gmjson", &test_schema);
    ASSERT_NE((void *)NULL, test_schema);
    printf(RED("test_schema:%s\n"), test_schema);

    int ret = GmcCreateVertexLabel(g_stmt, test_schema, g_label_config);
    ASSERT_EQ(GMERR_OK, ret);

    int tdNum = 5;
    pthread_t sameNameth[tdNum];
    for (int i = 0; i < tdNum; i++) {
        ret = pthread_create(&sameNameth[i], NULL, Thread_DFX_008_11, NULL);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < tdNum; i++) {
        pthread_join(sameNameth[i], NULL);
    }

    ret = GmcDropVertexLabel(g_stmt, g_label_name);
    ASSERT_EQ(GMERR_OK, ret);

    free(test_schema);
    printf(GREEN("label is dropped"));
}

/*****************************************************************************
 * Description  : 012 创建一个二级索引的表，查看视图，观察非唯一索引的存储开销
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2020.11.21
 * Modification : Create function
 *
 * *****************************************************************************/

TEST_F(DFX_008_01, DFX_008_01_012)
{
    int32_t value0 = 1;
    uint32_t value1 = 10;
    char *test_schema = NULL;
    void *vertexLabel = NULL;

    readJanssonFile("schema_file/local_index_schema.gmjson", &test_schema);
    ASSERT_NE((void *)NULL, test_schema);
    printf(RED("test_schema:%s\n"), test_schema);

    int ret = GmcCreateVertexLabel(g_stmt, test_schema, g_label_config);
    ASSERT_EQ(GMERR_OK, ret);

    char const *view_name = "V\\$STORAGE_HASH_LINKLIST_INDEX_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s\n", g_toolPath, g_connServer,
        view_name);
    printf(GREEN("g_command = %s\n"), g_command);
    ret = executeCommand(
        g_command, "NONUNIQUEINDEX_ID", "NONUNIQUEINDEX_NAME", "BUCKET_COUNT", "BUCKET_SIZE", "GROUP_COUNT");
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, g_label_name);
    ASSERT_EQ(GMERR_OK, ret);

    free(test_schema);
}

/*****************************************************************************
 * Description  : 013 创建一个二级索引的表，循环查看视图，观察非唯一索引的存储开销
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2020.11.21
 * Modification : Create function
 *
 * *****************************************************************************/

TEST_F(DFX_008_01, DFX_008_01_013)
{
    int32_t value0 = 1;
    uint32_t value1 = 10;
    char *test_schema = NULL;
    void *vertexLabel = NULL;

    readJanssonFile("schema_file/local_index_schema.gmjson", &test_schema);
    ASSERT_NE((void *)NULL, test_schema);
    printf(RED("test_schema:%s\n"), test_schema);

    ret = GmcCreateVertexLabel(g_stmt, test_schema, g_label_config);
    ASSERT_EQ(GMERR_OK, ret);

    char const *view_name = "V\\$STORAGE_HASH_LINKLIST_INDEX_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s\n", g_toolPath, g_connServer,
        view_name);
    printf(GREEN("g_command = %s\n"), g_command);
    for (unsigned int i = 0; i < 100; i++) {
        printf(GREEN("loop i = %d\n"), i);
        ret = executeCommand(
            g_command, "NONUNIQUEINDEX_ID", "NONUNIQUEINDEX_NAME", "BUCKET_COUNT", "BUCKET_SIZE", "GROUP_COUNT");
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(g_stmt, g_label_name);
    ASSERT_EQ(GMERR_OK, ret);

    free(test_schema);
}

void *Thread_DFX_008_14(void *args)
{
    char const *view_name = "V\\$STORAGE_HASH_LINKLIST_INDEX_STAT";
    char command[MAX_CMD_SIZE];
    snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s\n", g_toolPath, g_connServer,
        view_name);
    printf(GREEN("command = %s\n"), command);
    for (uint32_t i = 0; i < 10; i++) {
        ret = executeCommand(
            command, "NONUNIQUEINDEX_ID", "NONUNIQUEINDEX_NAME", "BUCKET_COUNT", "BUCKET_SIZE", "GROUP_COUNT");
        EXPECT_EQ(GMERR_OK, ret);
    }
    return NULL;
}
/*****************************************************************************
 * Description  : 014 创建一个二级索引的表，多线程循环查看视图，观察非唯一索引的存储开销
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2020.11.21
 * Modification : Create function
 *
 * *****************************************************************************/
TEST_F(DFX_008_01, DFX_008_01_014)
{
    int32_t value0 = 1;
    uint32_t value1 = 10;
    char *test_schema = NULL;
    void *vertexLabel = NULL;

    readJanssonFile("schema_file/local_index_schema.gmjson", &test_schema);
    ASSERT_NE((void *)NULL, test_schema);
    printf(RED("test_schema:%s\n"), test_schema);
    ret = GmcCreateVertexLabel(g_stmt, test_schema, g_label_config);
    ASSERT_EQ(GMERR_OK, ret);

    int tdNum = 4;
    pthread_t sameNameth[tdNum];
    for (int i = 0; i < tdNum; i++) {
        ret = pthread_create(&sameNameth[i], NULL, Thread_DFX_008_14, NULL);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < tdNum; i++) {
        pthread_join(sameNameth[i], NULL);
    }
    ret = GmcDropVertexLabel(g_stmt, g_label_name);
    ASSERT_EQ(GMERR_OK, ret);
    free(test_schema);
}

/*****************************************************************************
 * Description  : 015 创建一个主键索引的表，查看视图，观察唯一索引的存储开销
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2020.11.21
 * Modification : Create function
 *
 * *****************************************************************************/

TEST_F(DFX_008_01, DFX_008_01_015)
{
    int32_t value0 = 1;
    uint32_t value1 = 10;
    char *test_schema = NULL;
    void *vertexLabel = NULL;

    readJanssonFile("schema_file/pk_indedx_schema.gmjson", &test_schema);
    ASSERT_NE((void *)NULL, test_schema);
    printf(RED("test_schema:%s\n"), test_schema);
    ret = GmcCreateVertexLabel(g_stmt, test_schema, g_label_config);
    ASSERT_EQ(GMERR_OK, ret);

    char const *view_name = "V\\$STORAGE_HASH_INDEX_STAT ";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s\n", g_toolPath, g_connServer,
        view_name);
    printf(GREEN("g_command = %s\n"), g_command);
    ret = executeCommand(g_command, "UNIQUEINDEX_ID", "UNIQUEINDEX_NAME", "ENTRY_CAPACITY", "ENTRY_USED", "PAGE_COUNT");
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, g_label_name);
    ASSERT_EQ(GMERR_OK, ret);
    free(test_schema);
}

/*****************************************************************************
 * Description  : 016 创建一个主键索引的表，循环查看视图，观察唯一索引的存储开销
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2020.11.21
 * Modification : Create function
 *
 * *****************************************************************************/

TEST_F(DFX_008_01, DFX_008_01_016)
{
    int32_t value0 = 1;
    uint32_t value1 = 10;
    char *test_schema = NULL;
    void *vertexLabel = NULL;

    readJanssonFile("schema_file/pk_indedx_schema.gmjson", &test_schema);
    ASSERT_NE((void *)NULL, test_schema);
    printf(RED("test_schema:%s\n"), test_schema);

    ret = GmcCreateVertexLabel(g_stmt, test_schema, g_label_config);
    ASSERT_EQ(GMERR_OK, ret);

    char const *view_name = "V\\$STORAGE_HASH_INDEX_STAT ";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s\n", g_toolPath, g_connServer,
        view_name);
    printf(GREEN("g_command = %s\n"), g_command);
    for (uint32_t i = 0; i < 200; i++) {
        ret = executeCommand(
            g_command, "UNIQUEINDEX_ID", "UNIQUEINDEX_NAME", "ENTRY_CAPACITY", "ENTRY_USED", "PAGE_COUNT");
        ASSERT_EQ(GMERR_OK, ret);
    }

    ret = GmcDropVertexLabel(g_stmt, g_label_name);
    ASSERT_EQ(GMERR_OK, ret);
    free(test_schema);
}

void *Thread_DFX_008_17(void *args)
{
    char const *view_name = "V\\$STORAGE_HASH_INDEX_STAT ";
    char command[MAX_CMD_SIZE];
    snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s\n", g_toolPath, g_connServer,
        view_name);
    printf(GREEN("command = %s\n"), command);
    for (uint32_t i = 0; i < 10; i++) {
        ret =
            executeCommand(command, "UNIQUEINDEX_ID", "UNIQUEINDEX_NAME", "ENTRY_CAPACITY", "ENTRY_USED", "PAGE_COUNT");
        EXPECT_EQ(GMERR_OK, ret);
        printf(GREEN("excute %d\n"), i);
    }
    return NULL;
}

/*****************************************************************************
 * Description  : 017 创建一个主键索引的表，多线程循环查看视图，观察唯一索引的存储开销
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2020.11.21
 * Modification : Create function
 *
 * *****************************************************************************/

TEST_F(DFX_008_01, DFX_008_01_017)
{
    int32_t value0 = 1;
    uint32_t value1 = 10;
    char *test_schema = NULL;
    void *vertexLabel = NULL;

    readJanssonFile("schema_file/pk_indedx_schema.gmjson", &test_schema);
    ASSERT_NE((void *)NULL, test_schema);
    printf(RED("test_schema:%s\n"), test_schema);

    ret = GmcCreateVertexLabel(g_stmt, test_schema, g_label_config);
    ASSERT_EQ(GMERR_OK, ret);

    int tdNum = 2;
    pthread_t sameNameth[tdNum];
    for (int i = 0; i < tdNum; i++) {
        ret = pthread_create(&sameNameth[i], NULL, Thread_DFX_008_17, NULL);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < tdNum; i++) {
        pthread_join(sameNameth[i], NULL);
    }

    ret = GmcDropVertexLabel(g_stmt, g_label_name);
    ASSERT_EQ(GMERR_OK, ret);

    free(test_schema);
}

void *Thread_DFX_008_18(void *args)
{
    char command[MAX_CMD_SIZE];
    char const *view_name;
    uint32_t input = *(uint32_t *)args;
    if (input) {
        view_name = "V\\$STORAGE_EDGE_LABEL_STAT";
        snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s\n", g_toolPath, g_connServer,
            view_name);
        printf(GREEN("command = %s\n"), command);
        for (uint32_t i = 0; i < 10; i++) {
            ret = executeCommand(
                command, "EDGELABEL_ID", "EDGELABEL_NAME", "FIXED_HEAP_PAGE_HEAD_SIZE", "FIXED_HEAP_ROW_HEAD_SIZE");
            EXPECT_EQ(GMERR_OK, ret);
        }
    } else {
        view_name = "V\\$STORAGE_HEAP_VERTEX_LABEL_STAT";
        snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s\n", g_toolPath, g_connServer,
            view_name);
        printf(GREEN("command = %s\n"), command);
        for (uint32_t i = 0; i < 10; i++) {
            ret = executeCommand(
                command, "VERTEXLABEL_ID", "VERTEXLABEL_NAME", "HEAP_PAGE_HEAD_SIZE", "HEAP_ROW_HEAD_SIZE");
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    return NULL;
}
/*****************************************************************************
 * Description  : 018 混合场景：并发查看边和点视图
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2020.11.21
 * Modification : Create function
 * *****************************************************************************/
TEST_F(DFX_008_01, DFX_008_01_018)
{
    char *src_vertex_schema = NULL;
    char *dst_vertex_schema = NULL;
    char *edge_schema = NULL;
    void *srcVertexLabel = NULL;
    void *dstVertexLabel = NULL;

    //读取label schema
    readJanssonFile("schema_file/srcLabel.gmjson", &src_vertex_schema);
    ASSERT_NE((void *)NULL, src_vertex_schema);
    printf(RED("src_vertex_schema:%s\n"), src_vertex_schema);

    readJanssonFile("schema_file/dstLabel.gmjson", &dst_vertex_schema);
    ASSERT_NE((void *)NULL, dst_vertex_schema);
    printf(RED("dst_vertex_schema:%s\n"), dst_vertex_schema);

    //读取edge schema
    readJanssonFile("schema_file/edgeLabel.gmjson", &edge_schema);
    ASSERT_NE((void *)NULL, edge_schema);
    printf(RED("edge_schema:%s\n"), edge_schema);

    // 创建 src vertexLable
    ret = GmcCreateVertexLabel(g_stmt, src_vertex_schema, srccfgJson1);
    ASSERT_EQ(GMERR_OK, ret);
    // 创建 dest vertexLable
    ret = GmcCreateVertexLabel(g_stmt, dst_vertex_schema, dstcfgJson1);
    ASSERT_EQ(GMERR_OK, ret);
    // 创建 edgeLable
    ret = GmcCreateEdgeLabel(g_stmt, edge_schema, cfgJson);
    ASSERT_EQ(GMERR_OK, ret);

    // set srcVertex for row data
    ret = testGmcPrepareStmtByLabelName(g_stmt, edgeSrcVertexLabelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    int F0Value = 12;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
    ASSERT_EQ(GMERR_OK, ret);
    int F1Value = 13;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
    ASSERT_EQ(GMERR_OK, ret);
    int F2Value = 132;
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    ASSERT_EQ(GMERR_OK, ret);
    int F3Value = 321;
    ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_INT32, &F3Value, sizeof(F3Value));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, edgeSrcVertexLabelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    int value = 12;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "srcVertexLabel_PK0");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    bool eof = false;
    ret = GmcFetch(g_stmt, &eof);
    ASSERT_EQ(GMERR_OK, ret);

    // set dstVertex for row data
    ret = testGmcPrepareStmtByLabelName(g_stmt, edgeDstVertexLabelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    F0Value = 22;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
    ASSERT_EQ(GMERR_OK, ret);
    F1Value = 23;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
    ASSERT_EQ(GMERR_OK, ret);
    F2Value = 132;
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    ASSERT_EQ(GMERR_OK, ret);
    F3Value = 321;
    ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_INT32, &F3Value, sizeof(F3Value));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, edgeDstVertexLabelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    value = 22;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "dstVertexLabel_PK0");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    eof = false;
    ret = GmcFetch(g_stmt, &eof);
    ASSERT_EQ(GMERR_OK, ret);

    int tdNum = 2;
    uint32_t a[2];
    pthread_t sameNameth[tdNum];
    for (int i = 0; i < tdNum; i++) {
        a[i] = i;
        ret = pthread_create(&sameNameth[i], NULL, Thread_DFX_008_18, (void *)&a[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < tdNum; i++) {
        pthread_join(sameNameth[i], NULL);
    }

    free(src_vertex_schema);
    free(dst_vertex_schema);
    free(edge_schema);
}

void *Thread_DFX_008_19(void *args)
{
    char command[MAX_CMD_SIZE];
    char const *view_name;
    uint32_t input = *(uint32_t *)args;
    if (input) {
        view_name = "V\\$STORAGE_EDGE_LABEL_STAT";
        snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s\n", g_toolPath, g_connServer,
            view_name);
        printf(GREEN("command = %s\n"), command);
        for (uint32_t i = 0; i < 10; i++) {
            ret = executeCommand(
                command, "EDGELABEL_ID", "EDGELABEL_NAME", "FIXED_HEAP_PAGE_HEAD_SIZE", "FIXED_HEAP_ROW_HEAD_SIZE");
            EXPECT_EQ(GMERR_OK, ret);
        }
    } else {
        view_name = "V\\$STORAGE_UNDO_STAT";
        snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s\n", g_toolPath, g_connServer,
            view_name);
        printf(GREEN("command = %s\n"), command);
        for (uint32_t i = 0; i < 10; i++) {
            printf(GREEN("i = %d\n"), i);
            ret = executeCommand(command, "UNDOLOG_ID", "ROLLBACK_SEGMENT_PAGE_NUM", "UNDOLOG_PAGE_NUM", "PAGE_SIZE",
                "TOTAL_INSERT_UNDOLOG_SIZE");
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    return NULL;
}
/*****************************************************************************
 * Description  : 019 混合场景：并发查看边和undolog的内存信息输出的视图
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2020.11.21
 * Modification : Create function
 * *****************************************************************************/
TEST_F(DFX_008_01, DFX_008_01_019)
{
    char *src_vertex_schema = NULL;
    char *dst_vertex_schema = NULL;
    char *edge_schema = NULL;
    void *srcVertexLabel = NULL;
    void *dstVertexLabel = NULL;

    //读取label schema
    readJanssonFile("schema_file/srcLabel.gmjson", &src_vertex_schema);
    ASSERT_NE((void *)NULL, src_vertex_schema);
    printf(RED("src_vertex_schema:%s\n"), src_vertex_schema);

    readJanssonFile("schema_file/dstLabel.gmjson", &dst_vertex_schema);
    ASSERT_NE((void *)NULL, dst_vertex_schema);
    printf(RED("dst_vertex_schema:%s\n"), dst_vertex_schema);

    //读取edge schema
    readJanssonFile("schema_file/edgeLabel.gmjson", &edge_schema);
    ASSERT_NE((void *)NULL, edge_schema);
    printf(RED("edge_schema:%s\n"), edge_schema);

    // 创建 src vertexLable
    ret = GmcCreateVertexLabel(g_stmt, src_vertex_schema, srccfgJson1);
    ASSERT_EQ(GMERR_OK, ret);
    // 创建 dest vertexLable
    ret = GmcCreateVertexLabel(g_stmt, dst_vertex_schema, dstcfgJson1);
    ASSERT_EQ(GMERR_OK, ret);
    // 创建 edgeLable
    ret = GmcCreateEdgeLabel(g_stmt, edge_schema, cfgJson);
    ASSERT_EQ(GMERR_OK, ret);

    // set srcVertex for row data
    ret = testGmcPrepareStmtByLabelName(g_stmt, edgeSrcVertexLabelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    int F0Value = 12;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
    ASSERT_EQ(GMERR_OK, ret);
    int F1Value = 13;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
    ASSERT_EQ(GMERR_OK, ret);
    int F2Value = 132;
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    ASSERT_EQ(GMERR_OK, ret);
    int F3Value = 321;
    ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_INT32, &F3Value, sizeof(F3Value));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, edgeSrcVertexLabelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    int value = 12;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "srcVertexLabel_PK0");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    bool eof = false;
    ret = GmcFetch(g_stmt, &eof);
    ASSERT_EQ(GMERR_OK, ret);

    // set dstVertex for row data
    ret = testGmcPrepareStmtByLabelName(g_stmt, edgeDstVertexLabelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    F0Value = 22;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
    ASSERT_EQ(GMERR_OK, ret);
    F1Value = 23;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
    ASSERT_EQ(GMERR_OK, ret);
    F2Value = 132;
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    ASSERT_EQ(GMERR_OK, ret);
    F3Value = 321;
    ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_INT32, &F3Value, sizeof(F3Value));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, edgeDstVertexLabelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    value = 22;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "dstVertexLabel_PK0");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    eof = false;
    ret = GmcFetch(g_stmt, &eof);
    ASSERT_EQ(GMERR_OK, ret);


    int tdNum = 2;
    uint32_t a[2];
    pthread_t sameNameth[tdNum];
    for (int i = 0; i < tdNum; i++) {
        a[i] = i;
        ret = pthread_create(&sameNameth[i], NULL, Thread_DFX_008_18, (void *)&a[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < tdNum; i++) {
        pthread_join(sameNameth[i], NULL);
    }

    free(src_vertex_schema);
    free(dst_vertex_schema);
    free(edge_schema);
}

void *Thread_DFX_008_20(void *args)
{
    char command[MAX_CMD_SIZE];
    char const *view_name;
    uint32_t input = *(uint32_t *)args;
    if (input) {
        view_name = "V\\$STORAGE_HEAP_VERTEX_LABEL_STAT";
        snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s\n", g_toolPath, g_connServer,
            view_name);
        printf(GREEN("command = %s\n"), command);
        for (uint32_t i = 0; i < 10; i++) {
            ret = executeCommand(
                command, "VERTEXLABEL_ID", "VERTEXLABEL_NAME", "HEAP_PAGE_HEAD_SIZE", "HEAP_ROW_HEAD_SIZE");
            EXPECT_EQ(GMERR_OK, ret);
        }
    } else {
        view_name = "V\\$STORAGE_UNDO_STAT";
        snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s\n", g_toolPath, g_connServer,
            view_name);
        printf(GREEN("command = %s\n"), command);
        for (uint32_t i = 0; i < 10; i++) {
            ret = executeCommand(command, "UNDOLOG_ID", "ROLLBACK_SEGMENT_PAGE_NUM", "UNDOLOG_PAGE_NUM", "PAGE_SIZE",
                "TOTAL_INSERT_UNDOLOG_SIZE");
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    return NULL;
}


void *Thread_DFX_008_21(void *args)
{
    char command[MAX_CMD_SIZE];
    char const *view_name;
    uint32_t input = *(uint32_t *)args;
    if (input == 0) {
        view_name = "V\\$STORAGE_HEAP_VERTEX_LABEL_STAT";
        snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s\n", g_toolPath, g_connServer,
            view_name);
        printf(GREEN("command = %s\n"), command);
        for (uint32_t i = 0; i < 10; i++) {
                ret = testScanSysview((char *)"V$STORAGE_HEAP_VERTEX_LABEL_STAT", (char *)"VERTEXLABEL_ID", (char *)"default", (char *)"NAMESPACE_NAME: public", false, false);
                EXPECT_EQ(GMERR_OK, ret);
                ret = testScanSysview((char *)"V$STORAGE_HEAP_VERTEX_LABEL_STAT", (char *)"VERTEXLABEL_NAME", (char *)"1048577", (char *)"LABEL_COUNT: 5", false, false, (char*)"uint64_t");
                EXPECT_EQ(GMERR_OK, ret);
                ret = testScanSysview((char *)"V$STORAGE_HEAP_VERTEX_LABEL_STAT", (char *)"HEAP_PAGE_HEAD_SIZE", (char *)"default", (char *)"NAMESPACE_NAME: public", false, false);
                EXPECT_EQ(GMERR_OK, ret);
                ret = testScanSysview((char *)"V$STORAGE_HEAP_VERTEX_LABEL_STAT", (char *)"HEAP_ROW_HEAD_SIZE", (char *)"1048577", (char *)"LABEL_COUNT: 5", false, false, (char*)"uint64_t");
                EXPECT_EQ(GMERR_OK, ret);
        }
    } else if (input == 1) {
        view_name = "V\\$STORAGE_UNDO_STAT";
        snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s\n", g_toolPath, g_connServer,
            view_name);
        printf(GREEN("command = %s\n"), command);
        for (uint32_t i = 0; i < 10; i++) {
		ret = testScanSysview((char *)"V$STORAGE_UNDO_STAT", (char *)"UNDOLOG_ID", (char *)"default", (char *)"NAMESPACE_NAME: public", false, false);
                EXPECT_EQ(GMERR_OK, ret);
                ret = testScanSysview((char *)"V$STORAGE_UNDO_STAT", (char *)"ROLLBACK_SEGMENT_PAGE_NUM", (char *)"391", (char *)"NAMESPACE_NAME: gmdbtest", false, false, (char*)"uint64_t");
                EXPECT_EQ(GMERR_OK, ret);
                ret = testScanSysview((char *)"V$STORAGE_UNDO_STAT", (char *)"UNDOLOG_PAGE_NUM", (char *)"1048577", (char *)"LABEL_COUNT: 5", false, false, (char*)"uint64_t");
                EXPECT_EQ(GMERR_OK, ret);
                ret = testScanSysview((char *)"V$STORAGE_UNDO_STAT", (char *)"PAGE_SIZE", (char *)"default", (char *)"NAMESPACE_NAME: public", false, false);
                EXPECT_EQ(GMERR_OK, ret);
                ret = testScanSysview((char *)"V$STORAGE_UNDO_STAT", (char *)"TOTAL_INSERT_UNDOLOG_SIZE", (char *)"1048577", (char *)"LABEL_COUNT: 5", false, false, (char*)"uint64_t");
                EXPECT_EQ(GMERR_OK, ret);
        }
    } else if (input == 2) {
        view_name = "V\\$STORAGE_EDGE_LABEL_STAT";
        snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s\n", g_toolPath, g_connServer,
            view_name);
        printf(GREEN("command = %s\n"), command);
        for (uint32_t i = 0; i < 10; i++) {
		ret = testScanSysview((char *)"V$STORAGE_EDGE_LABEL_STAT", (char *)"EDGELABEL_ID", (char *)"default", (char *)"NAMESPACE_NAME: public", false, false);
                EXPECT_EQ(GMERR_OK, ret);
                ret = testScanSysview((char *)"V$STORAGE_EDGE_LABEL_STAT", (char *)"EDGELABEL_NAME", (char *)"391", (char *)"NAMESPACE_NAME: gmdbtest", false, false, (char*)"uint64_t");
                EXPECT_EQ(GMERR_OK, ret);
                ret = testScanSysview((char *)"V$STORAGE_EDGE_LABEL_STAT", (char *)"FIXED_HEAP_PAGE_HEAD_SIZE", (char *)"1048577", (char *)"LABEL_COUNT: 5", false, false, (char*)"uint64_t");
                EXPECT_EQ(GMERR_OK, ret);
                ret = testScanSysview((char *)"V$STORAGE_EDGE_LABEL_STAT", (char *)"FIXED_HEAP_ROW_HEAD_SIZE", (char *)"default", (char *)"NAMESPACE_NAME: public", false, false);
                EXPECT_EQ(GMERR_OK, ret);
        }
    } else {
        EXPECT_EQ(0, 1);
    }
    return NULL;
}

/*****************************************************************************
 * Description  : 021  混合场景：并发查看点、边和undolog的内存信息输出的视图
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2020.11.21
 * Modification : Create function
 *
 * ./gmsysview -q  V\$ STORAGE_EDGE_LABEL_STAT  -e  RTOS  –s  usocket:/run/verona/unix_emserver
 * *****************************************************************************/

TEST_F(DFX_008_01, DFX_008_01_021)
{
    char *src_vertex_schema = NULL;
    char *dst_vertex_schema = NULL;
    char *edge_schema = NULL;
    void *srcVertexLabel = NULL;
    void *dstVertexLabel = NULL;

    //读取label schema
    readJanssonFile("schema_file/srcLabel.gmjson", &src_vertex_schema);
    ASSERT_NE((void *)NULL, src_vertex_schema);
    printf(RED("src_vertex_schema:%s\n"), src_vertex_schema);

    readJanssonFile("schema_file/dstLabel.gmjson", &dst_vertex_schema);
    ASSERT_NE((void *)NULL, dst_vertex_schema);
    printf(RED("dst_vertex_schema:%s\n"), dst_vertex_schema);

    //读取edge schema
    readJanssonFile("schema_file/edgeLabel.gmjson", &edge_schema);
    ASSERT_NE((void *)NULL, edge_schema);
    printf(RED("edge_schema:%s\n"), edge_schema);

    // 创建 src vertexLable
    ret = GmcCreateVertexLabel(g_stmt, src_vertex_schema, srccfgJson1);
    ASSERT_EQ(GMERR_OK, ret);
    // 创建 dest vertexLable
    ret = GmcCreateVertexLabel(g_stmt, dst_vertex_schema, dstcfgJson1);
    ASSERT_EQ(GMERR_OK, ret);
    // 创建 edgeLable
    ret = GmcCreateEdgeLabel(g_stmt, edge_schema, cfgJson);
    ASSERT_EQ(GMERR_OK, ret);

    // set srcVertex for row data
    ret = testGmcPrepareStmtByLabelName(g_stmt, edgeSrcVertexLabelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    int F0Value = 12;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
    ASSERT_EQ(GMERR_OK, ret);
    int F1Value = 13;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
    ASSERT_EQ(GMERR_OK, ret);
    int F2Value = 132;
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    ASSERT_EQ(GMERR_OK, ret);
    int F3Value = 321;
    ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_INT32, &F3Value, sizeof(F3Value));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, edgeSrcVertexLabelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    int value = 12;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "srcVertexLabel_PK0");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    bool eof = false;
    ret = GmcFetch(g_stmt, &eof);
    ASSERT_EQ(GMERR_OK, ret);

    // set dstVertex for row data
    ret = testGmcPrepareStmtByLabelName(g_stmt, edgeDstVertexLabelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    F0Value = 22;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
    ASSERT_EQ(GMERR_OK, ret);
    F1Value = 23;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
    ASSERT_EQ(GMERR_OK, ret);
    F2Value = 132;
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    ASSERT_EQ(GMERR_OK, ret);
    F3Value = 321;
    ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_INT32, &F3Value, sizeof(F3Value));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, edgeDstVertexLabelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    value = 22;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "dstVertexLabel_PK0");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    eof = false;
    ret = GmcFetch(g_stmt, &eof);
    ASSERT_EQ(GMERR_OK, ret);


    int tdNum = 3;
    uint32_t a[tdNum];
    pthread_t sameNameth[tdNum];
    for (int i = 0; i < tdNum; i++) {
        a[i] = i;
        ret = pthread_create(&sameNameth[i], NULL, Thread_DFX_008_21, (void *)&a[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < tdNum; i++) {
        pthread_join(sameNameth[i], NULL);
    }

    free(src_vertex_schema);
    free(dst_vertex_schema);
    free(edge_schema);
}

void *Thread_DFX_008_22(void *args)
{
    char command[MAX_CMD_SIZE];
    char const *view_name;
    uint32_t input = *(uint32_t *)args;
    if (input) {
        view_name = "V\\$STORAGE_HASH_INDEX_STAT";
        snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s\n", g_toolPath, g_connServer,
            view_name);
        printf(GREEN("command = %s\n"), command);
        for (uint32_t i = 0; i < 10; i++) {
            ret = executeCommand(
                command, "UNIQUEINDEX_ID", "UNIQUEINDEX_NAME", "ENTRY_CAPACITY", "ENTRY_USED", "PAGE_COUNT");
            EXPECT_EQ(GMERR_OK, ret);
        }
    } else {
        view_name = "V\\$STORAGE_HASH_LINKLIST_INDEX_STAT";
        snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s\n", g_toolPath, g_connServer,
            view_name);
        printf(GREEN("command = %s\n"), command);
        for (uint32_t i = 0; i < 10; i++) {
            ret = executeCommand(
                command, "NONUNIQUEINDEX_ID", "NONUNIQUEINDEX_NAME", "BUCKET_COUNT", "BUCKET_SIZE", "GROUP_COUNT");
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    return NULL;
}

/*****************************************************************************
 * Description  : 022 混合场景：并发查看主键索引的表和二级索引的表的视图
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2020.11.21
 * Modification : Create function
 *
 * *****************************************************************************/

TEST_F(DFX_008_01, DFX_008_01_022)
{
    int32_t value0 = 1;
    uint32_t value1 = 10;
    char *test_schema = NULL;
    void *vertexLabel = NULL;
    readJanssonFile("schema_file/pk_indedx_schema.gmjson", &test_schema);
    ASSERT_NE((void *)NULL, test_schema);
    printf(RED("pk_indedx_schema:%s\n"), test_schema);
    ret = GmcCreateVertexLabel(g_stmt, test_schema, g_label_config);
    ASSERT_EQ(GMERR_OK, ret);
    free(test_schema);

    readJanssonFile("schema_file/local_index_schema1.gmjson", &test_schema);
    ASSERT_NE((void *)NULL, test_schema);
    printf(RED("local_index_schema:%s\n"), test_schema);
    ret = GmcCreateVertexLabel(g_stmt, test_schema, g_label_config);
    ASSERT_EQ(GMERR_OK, ret);
    free(test_schema);

    int tdNum = 2;
    uint32_t a[2];
    pthread_t sameNameth[tdNum];
    for (int i = 0; i < tdNum; i++) {
        a[i] = i;
        ret = pthread_create(&sameNameth[i], NULL, Thread_DFX_008_22, (void *)&a[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < tdNum; i++) {
        pthread_join(sameNameth[i], NULL);
    }

    ret = GmcDropVertexLabel(g_stmt, g_label_name);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "test_schema1");
    ASSERT_EQ(GMERR_OK, ret);
}

void *Thread_DFX_008_23(void *args)
{
    char command[MAX_CMD_SIZE];
    char const *view_name;
    uint32_t input = *(uint32_t *)args;
    if (input == 0) {
        view_name = "V\\$STORAGE_HEAP_VERTEX_LABEL_STAT";
        snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s\n", g_toolPath, g_connServer,
            view_name);
        printf(GREEN("command = %s\n"), command);
        for (uint32_t i = 0; i < 10; i++) {
            ret = executeCommand(
                command, "VERTEXLABEL_ID", "VERTEXLABEL_NAME", "HEAP_PAGE_HEAD_SIZE", "HEAP_ROW_HEAD_SIZE");
            EXPECT_EQ(GMERR_OK, ret);
        }
    } else if (input == 1) {
        view_name = "V\\$STORAGE_UNDO_STAT";
        snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s\n", g_toolPath, g_connServer,
            view_name);
        printf(GREEN("command = %s\n"), command);
        for (uint32_t i = 0; i < 10; i++) {
            ret = executeCommand(command, "UNDOLOG_ID", "ROLLBACK_SEGMENT_PAGE_NUM", "UNDOLOG_PAGE_NUM", "PAGE_SIZE",
                "TOTAL_INSERT_UNDOLOG_SIZE");
            EXPECT_EQ(GMERR_OK, ret);
        }
    } else if (input == 2) {
        view_name = "V\\$STORAGE_EDGE_LABEL_STAT";
        snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s\n", g_toolPath, g_connServer,
            view_name);
        printf(GREEN("command = %s\n"), command);
        for (uint32_t i = 0; i < 10; i++) {
            ret = executeCommand(
                command, "EDGELABEL_ID", "EDGELABEL_NAME", "FIXED_HEAP_PAGE_HEAD_SIZE", "FIXED_HEAP_ROW_HEAD_SIZE");
            EXPECT_EQ(GMERR_OK, ret);
        }
    } else if (input = 3) {
        view_name = "V\\$STORAGE_HASH_INDEX_STAT";
        snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s\n", g_toolPath, g_connServer,
            view_name);
        printf(GREEN("command = %s\n"), command);
        for (uint32_t i = 0; i < 10; i++) {
            ret = executeCommand(
                command, "UNIQUEINDEX_ID", "UNIQUEINDEX_NAME", "ENTRY_CAPACITY", "ENTRY_USED", "PAGE_COUNT");
            EXPECT_EQ(GMERR_OK, ret);
        }
    } else if (input = 4) {
        view_name = "V\\$STORAGE_HASH_LINKLIST_INDEX_STAT";
        snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s\n", g_toolPath, g_connServer,
            view_name);
        printf(GREEN("command = %s\n"), command);
        for (uint32_t i = 0; i < 10; i++) {
            ret = executeCommand(
                command, "NONUNIQUEINDEX_ID", "NONUNIQUEINDEX_NAME", "BUCKET_COUNT", "BUCKET_SIZE", "GROUP_COUNT");
            EXPECT_EQ(GMERR_OK, ret);
        }
    } else {
        EXPECT_EQ(0, 1);
    }
    return NULL;
}

/*****************************************************************************
 * Description  : 023  混合场景：并发查看点、边、undolog的内存信息输出、主键索引的表和二级索引的表的视图
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2020.11.21
 * Modification : Create function
 *
 * ./gmsysview -q  V\$ STORAGE_EDGE_LABEL_STAT  -e  RTOS  –s  usocket:/run/verona/unix_emserver
 * *****************************************************************************/

TEST_F(DFX_008_01, DFX_008_01_023)
{
    char *src_vertex_schema = NULL;
    char *dst_vertex_schema = NULL;
    char *edge_schema = NULL;
    void *srcVertexLabel = NULL;
    void *dstVertexLabel = NULL;

    //读取label schema
    readJanssonFile("schema_file/srcLabel.gmjson", &src_vertex_schema);
    ASSERT_NE((void *)NULL, src_vertex_schema);
    printf(RED("src_vertex_schema:%s\n"), src_vertex_schema);

    readJanssonFile("schema_file/dstLabel.gmjson", &dst_vertex_schema);
    ASSERT_NE((void *)NULL, dst_vertex_schema);
    printf(RED("dst_vertex_schema:%s\n"), dst_vertex_schema);

    //读取edge schema
    readJanssonFile("schema_file/edgeLabel.gmjson", &edge_schema);
    ASSERT_NE((void *)NULL, edge_schema);
    printf(RED("edge_schema:%s\n"), edge_schema);

    // 创建 src vertexLable
    ret = GmcCreateVertexLabel(g_stmt, src_vertex_schema, srccfgJson1);
    ASSERT_EQ(GMERR_OK, ret);
    // 创建 dest vertexLable
    ret = GmcCreateVertexLabel(g_stmt, dst_vertex_schema, dstcfgJson1);
    ASSERT_EQ(GMERR_OK, ret);
    // 创建 edgeLable
    ret = GmcCreateEdgeLabel(g_stmt, edge_schema, cfgJson);
    ASSERT_EQ(GMERR_OK, ret);

    // set srcVertex for row data
    ret = testGmcPrepareStmtByLabelName(g_stmt, edgeSrcVertexLabelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    int F0Value = 12;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
    ASSERT_EQ(GMERR_OK, ret);
    int F1Value = 13;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
    ASSERT_EQ(GMERR_OK, ret);
    int F2Value = 132;
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    ASSERT_EQ(GMERR_OK, ret);
    int F3Value = 321;
    ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_INT32, &F3Value, sizeof(F3Value));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, edgeSrcVertexLabelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "srcVertexLabel_PK0");
    ASSERT_EQ(GMERR_OK, ret);
    int value = 12;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    bool eof = false;
    ret = GmcFetch(g_stmt, &eof);
    ASSERT_EQ(GMERR_OK, ret);

    // set dstVertex for row data
    ret = testGmcPrepareStmtByLabelName(g_stmt, edgeDstVertexLabelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    F0Value = 22;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
    ASSERT_EQ(GMERR_OK, ret);
    F1Value = 23;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
    ASSERT_EQ(GMERR_OK, ret);
    F2Value = 132;
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    ASSERT_EQ(GMERR_OK, ret);
    F3Value = 321;
    ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_INT32, &F3Value, sizeof(F3Value));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, edgeDstVertexLabelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    value = 22;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "dstVertexLabel_PK0");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    eof = false;
    ret = GmcFetch(g_stmt, &eof);
    ASSERT_EQ(GMERR_OK, ret);


    char *test_schema = NULL;
    readJanssonFile("schema_file/pk_indedx_schema1.gmjson", &test_schema);
    ASSERT_NE((void *)NULL, test_schema);
    printf(RED("pk_indedx_schema:%s\n"), test_schema);
    int ret = GmcCreateVertexLabel(g_stmt, test_schema, g_label_config);
    ASSERT_EQ(GMERR_OK, ret);
    free(test_schema);

    readJanssonFile("schema_file/local_index_schema1.gmjson", &test_schema);
    ASSERT_NE((void *)NULL, test_schema);
    printf(RED("local_index_schema:%s\n"), test_schema);
    ret = GmcCreateVertexLabel(g_stmt, test_schema, g_label_config);
    ASSERT_EQ(GMERR_OK, ret);

    int tdNum = 5;
    uint32_t a[tdNum];
    pthread_t sameNameth[tdNum];
    for (int i = 0; i < tdNum; i++) {
        a[i] = i;
        ret = pthread_create(&sameNameth[i], NULL, Thread_DFX_008_23, (void *)&a[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < tdNum; i++) {
        pthread_join(sameNameth[i], NULL);
    }

    free(src_vertex_schema);
    free(dst_vertex_schema);
    free(edge_schema);
    free(test_schema);
}

void *Thread_DFX_008_24(void *args)
{
    char command[MAX_CMD_SIZE];
    char const *view_name;
    uint32_t input = *(uint32_t *)args;
    for (uint32_t j = 0; j < 5; j++) {
        if (input == 0) {
            view_name = "V\\$STORAGE_HEAP_VERTEX_LABEL_STAT";
            snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s\n", g_toolPath,
                g_connServer, view_name);
            // printf(GREEN("command = %s\n"), command);
            printf(GREEN("input = %d, j = %d\n"), input, j);
            for (uint32_t i = 0; i < 10; i++) {
                ret = testScanSysview((char *)"V$STORAGE_HEAP_VERTEX_LABEL_STAT", (char *)"VERTEXLABEL_ID", (char *)"default", (char *)"NAMESPACE_NAME: public", false, false);
                EXPECT_EQ(GMERR_OK, ret);
                ret = testScanSysview((char *)"V$STORAGE_HEAP_VERTEX_LABEL_STAT", (char *)"VERTEXLABEL_NAME", (char *)"1048577", (char *)"LABEL_COUNT: 5", false, false, (char*)"uint64_t");
                EXPECT_EQ(GMERR_OK, ret);
                ret = testScanSysview((char *)"V$STORAGE_HEAP_VERTEX_LABEL_STAT", (char *)"HEAP_PAGE_HEAD_SIZE", (char *)"default", (char *)"NAMESPACE_NAME: public", false, false);
                EXPECT_EQ(GMERR_OK, ret);
                ret = testScanSysview((char *)"V$STORAGE_HEAP_VERTEX_LABEL_STAT", (char *)"HEAP_ROW_HEAD_SIZE", (char *)"1048577", (char *)"LABEL_COUNT: 5", false, false, (char*)"uint64_t");
                EXPECT_EQ(GMERR_OK, ret);
            }
        } else if (input == 1) {
            view_name = "V\\$STORAGE_UNDO_STAT";
            snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s\n", g_toolPath,
                g_connServer, view_name);
            // printf(GREEN("command = %s\n"), command);
            printf(GREEN("input = %d, j = %d\n"), input, j);
            for (uint32_t i = 0; i < 10; i++) {
                ret = testScanSysview((char *)"V$STORAGE_UNDO_STAT", (char *)"UNDOLOG_ID", (char *)"default", (char *)"NAMESPACE_NAME: public", false, false);
                EXPECT_EQ(GMERR_OK, ret);
                ret = testScanSysview((char *)"V$STORAGE_UNDO_STAT", (char *)"ROLLBACK_SEGMENT_PAGE_NUM", (char *)"391", (char *)"NAMESPACE_NAME: gmdbtest", false, false, (char*)"uint64_t");
                EXPECT_EQ(GMERR_OK, ret);
                ret = testScanSysview((char *)"V$STORAGE_UNDO_STAT", (char *)"UNDOLOG_PAGE_NUM", (char *)"1048577", (char *)"LABEL_COUNT: 5", false, false, (char*)"uint64_t");
                EXPECT_EQ(GMERR_OK, ret);
                ret = testScanSysview((char *)"V$STORAGE_UNDO_STAT", (char *)"PAGE_SIZE", (char *)"default", (char *)"NAMESPACE_NAME: public", false, false);
                EXPECT_EQ(GMERR_OK, ret);
                ret = testScanSysview((char *)"V$STORAGE_UNDO_STAT", (char *)"TOTAL_INSERT_UNDOLOG_SIZE", (char *)"1048577", (char *)"LABEL_COUNT: 5", false, false, (char*)"uint64_t");
                EXPECT_EQ(GMERR_OK, ret);
            }
        } else if (input == 2) {
            view_name = "V\\$STORAGE_EDGE_LABEL_STAT";
            snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s\n", g_toolPath,
                g_connServer, view_name);
            // printf(GREEN("command = %s\n"), command);
            printf(GREEN("input = %d, j = %d\n"), input, j);
            for (uint32_t i = 0; i < 10; i++) {
                ret = testScanSysview((char *)"V$STORAGE_EDGE_LABEL_STAT", (char *)"EDGELABEL_ID", (char *)"default", (char *)"NAMESPACE_NAME: public", false, false);
                EXPECT_EQ(GMERR_OK, ret);
                ret = testScanSysview((char *)"V$STORAGE_EDGE_LABEL_STAT", (char *)"EDGELABEL_NAME", (char *)"391", (char *)"NAMESPACE_NAME: gmdbtest", false, false, (char*)"uint64_t");
                EXPECT_EQ(GMERR_OK, ret);
                ret = testScanSysview((char *)"V$STORAGE_EDGE_LABEL_STAT", (char *)"FIXED_HEAP_PAGE_HEAD_SIZE", (char *)"1048577", (char *)"LABEL_COUNT: 5", false, false, (char*)"uint64_t");
                EXPECT_EQ(GMERR_OK, ret);
                ret = testScanSysview((char *)"V$STORAGE_EDGE_LABEL_STAT", (char *)"FIXED_HEAP_ROW_HEAD_SIZE", (char *)"default", (char *)"NAMESPACE_NAME: public", false, false);
                EXPECT_EQ(GMERR_OK, ret);
            }
        } else {
            EXPECT_EQ(0, 1);
        }
    }
    return NULL;
}

/*****************************************************************************
 * Description  : 024  混合场景：并发循环查看点、边和undolog的内存信息输出的视图50次
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2020.11.21
 * Modification : Create function
 *
 * ./gmsysview -q  V\$ STORAGE_EDGE_LABEL_STAT  -e  RTOS  –s  usocket:/run/verona/unix_emserver
 * *****************************************************************************/

TEST_F(DFX_008_01, DFX_008_01_024)
{
    char *src_vertex_schema = NULL;
    char *dst_vertex_schema = NULL;
    char *edge_schema = NULL;
    void *srcVertexLabel = NULL;
    void *dstVertexLabel = NULL;

    //读取label schema
    readJanssonFile("schema_file/srcLabel.gmjson", &src_vertex_schema);
    ASSERT_NE((void *)NULL, src_vertex_schema);
    printf(RED("src_vertex_schema:%s\n"), src_vertex_schema);

    readJanssonFile("schema_file/dstLabel.gmjson", &dst_vertex_schema);
    ASSERT_NE((void *)NULL, dst_vertex_schema);
    printf(RED("dst_vertex_schema:%s\n"), dst_vertex_schema);

    //读取edge schema
    readJanssonFile("schema_file/edgeLabel.gmjson", &edge_schema);
    ASSERT_NE((void *)NULL, edge_schema);
    printf(RED("edge_schema:%s\n"), edge_schema);

    // 创建 src vertexLable
    ret = GmcCreateVertexLabel(g_stmt, src_vertex_schema, srccfgJson1);
    ASSERT_EQ(GMERR_OK, ret);
    // 创建 dest vertexLable
    ret = GmcCreateVertexLabel(g_stmt, dst_vertex_schema, dstcfgJson1);
    ASSERT_EQ(GMERR_OK, ret);
    // 创建 edgeLable
    ret = GmcCreateEdgeLabel(g_stmt, edge_schema, cfgJson);
    ASSERT_EQ(GMERR_OK, ret);

    // set srcVertex for row data
    ret = testGmcPrepareStmtByLabelName(g_stmt, edgeSrcVertexLabelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    int F0Value = 12;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
    ASSERT_EQ(GMERR_OK, ret);
    int F1Value = 13;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
    ASSERT_EQ(GMERR_OK, ret);
    int F2Value = 132;
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    ASSERT_EQ(GMERR_OK, ret);
    int F3Value = 321;
    ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_INT32, &F3Value, sizeof(F3Value));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, edgeSrcVertexLabelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    int value = 12;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "srcVertexLabel_PK0");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    bool eof = false;
    ret = GmcFetch(g_stmt, &eof);
    ASSERT_EQ(GMERR_OK, ret);

    // set dstVertex for row data
    ret = testGmcPrepareStmtByLabelName(g_stmt, edgeDstVertexLabelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    F0Value = 22;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
    ASSERT_EQ(GMERR_OK, ret);
    F1Value = 23;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
    ASSERT_EQ(GMERR_OK, ret);
    F2Value = 132;
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    ASSERT_EQ(GMERR_OK, ret);
    F3Value = 321;
    ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_INT32, &F3Value, sizeof(F3Value));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, edgeDstVertexLabelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    value = 22;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "dstVertexLabel_PK0");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    eof = false;
    ret = GmcFetch(g_stmt, &eof);
    ASSERT_EQ(GMERR_OK, ret);


    int tdNum = 3;
    uint32_t a[tdNum];
    pthread_t sameNameth[tdNum];
    for (int i = 0; i < tdNum; i++) {
        a[i] = i;
        ret = pthread_create(&sameNameth[i], NULL, Thread_DFX_008_24, (void *)&a[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < tdNum; i++) {
        pthread_join(sameNameth[i], NULL);
    }

    free(src_vertex_schema);
    free(dst_vertex_schema);
    free(edge_schema);
}

void *Thread_DFX_008_25(void *args)
{
    char command[MAX_CMD_SIZE];
    char const *view_name;
    uint32_t input = *(uint32_t *)args;
    for (uint32_t j = 0; j < 5; j++) {
        if (input) {
            view_name = "V\\$STORAGE_HASH_INDEX_STAT";
            snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s\n", g_toolPath,
                g_connServer, view_name);
            printf(GREEN("command = %s\n"), command);
            for (uint32_t i = 0; i < 10; i++) {
                ret = executeCommand(
                    command, "UNIQUEINDEX_ID", "UNIQUEINDEX_NAME", "ENTRY_CAPACITY", "ENTRY_USED", "PAGE_COUNT");
                EXPECT_EQ(GMERR_OK, ret);
            }
        } else {
            view_name = "V\\$STORAGE_HASH_LINKLIST_INDEX_STAT";
            snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s\n", g_toolPath,
                g_connServer, view_name);
            printf(GREEN("command = %s\n"), command);
            for (uint32_t i = 0; i < 10; i++) {
                ret = executeCommand(
                    command, "NONUNIQUEINDEX_ID", "NONUNIQUEINDEX_NAME", "BUCKET_COUNT", "BUCKET_SIZE", "GROUP_COUNT");
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
    }
    return NULL;
}

/*****************************************************************************
 * Description  : 025 混合场景：并发循环查看主键索引的表和二级索引的表的视图50次
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2020.11.21
 * Modification : Create function
 *
 * *****************************************************************************/

TEST_F(DFX_008_01, DFX_008_01_025)
{
    int32_t value0 = 1;
    uint32_t value1 = 10;
    char *test_schema = NULL;
    void *vertexLabel = NULL;
    readJanssonFile("schema_file/pk_indedx_schema.gmjson", &test_schema);
    ASSERT_NE((void *)NULL, test_schema);
    printf(RED("pk_indedx_schema:%s\n"), test_schema);
    int ret = GmcCreateVertexLabel(g_stmt, test_schema, g_label_config);
    ASSERT_EQ(GMERR_OK, ret);
    free(test_schema);

    readJanssonFile("schema_file/local_index_schema1.gmjson", &test_schema);
    ASSERT_NE((void *)NULL, test_schema);
    printf(RED("local_index_schema:%s\n"), test_schema);
    ret = GmcCreateVertexLabel(g_stmt, test_schema, g_label_config);
    ASSERT_EQ(GMERR_OK, ret);

    int tdNum = 2;
    uint32_t a[2];
    pthread_t sameNameth[tdNum];
    for (int i = 0; i < tdNum; i++) {
        a[i] = i;
        ret = pthread_create(&sameNameth[i], NULL, Thread_DFX_008_25, (void *)&a[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < tdNum; i++) {
        pthread_join(sameNameth[i], NULL);
    }

    ret = GmcDropVertexLabel(g_stmt, g_label_name);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "test_schema1");
    ASSERT_EQ(GMERR_OK, ret);

    free(test_schema);
}

void *Thread_DFX_008_26(void *args)
{
    char command[MAX_CMD_SIZE];
    char const *view_name;
    uint32_t input = *(uint32_t *)args;
    for (int32_t j = 0; j < 5; j++) {
        if (input == 0) {
            view_name = "V\\$STORAGE_HEAP_VERTEX_LABEL_STAT";
            snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s\n", g_toolPath,
                g_connServer, view_name);
            // printf(GREEN("command = %s\n"), command);
            for (uint32_t i = 0; i < 10; i++) {
                ret = executeCommand(
                    command, "VERTEXLABEL_ID", "VERTEXLABEL_NAME", "HEAP_PAGE_HEAD_SIZE", "HEAP_ROW_HEAD_SIZE");
                EXPECT_EQ(GMERR_OK, ret);
            }
        } else if (input == 1) {
            view_name = "V\\$STORAGE_UNDO_STAT";
            snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s\n", g_toolPath,
                g_connServer, view_name);
            // printf(GREEN("command = %s\n"), command);
            for (uint32_t i = 0; i < 10; i++) {
                ret = executeCommand(command, "UNDOLOG_ID", "ROLLBACK_SEGMENT_PAGE_NUM", "UNDOLOG_PAGE_NUM",
                    "PAGE_SIZE", "TOTAL_INSERT_UNDOLOG_SIZE");
                EXPECT_EQ(GMERR_OK, ret);
            }
        } else if (input == 2) {
            view_name = "V\\$STORAGE_EDGE_LABEL_STAT";
            snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s\n", g_toolPath,
                g_connServer, view_name);
            // printf(GREEN("command = %s\n"), command);
            for (uint32_t i = 0; i < 10; i++) {
                ret = executeCommand(
                    command, "EDGELABEL_ID", "EDGELABEL_NAME", "FIXED_HEAP_PAGE_HEAD_SIZE", "FIXED_HEAP_ROW_HEAD_SIZE");
                EXPECT_EQ(GMERR_OK, ret);
            }
        } else if (input = 3) {
            view_name = "V\\$STORAGE_HASH_INDEX_STAT";
            snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s\n", g_toolPath,
                g_connServer, view_name);
            // printf(GREEN("command = %s\n"), command);
            for (uint32_t i = 0; i < 10; i++) {
                ret = executeCommand(
                    command, "UNIQUEINDEX_ID", "UNIQUEINDEX_NAME", "ENTRY_CAPACITY", "ENTRY_USED", "PAGE_COUNT");
                EXPECT_EQ(GMERR_OK, ret);
            }
        } else if (input = 4) {
            view_name = "V\\$STORAGE_HASH_LINKLIST_INDEX_STAT";
            snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s\n", g_toolPath,
                g_connServer, view_name);
            for (uint32_t i = 0; i < 10; i++) {
                ret = executeCommand(
                    command, "NONUNIQUEINDEX_ID", "NONUNIQUEINDEX_NAME", "BUCKET_COUNT", "BUCKET_SIZE", "GROUP_COUNT");
                EXPECT_EQ(GMERR_OK, ret);
            }
        } else {
            EXPECT_EQ(0, 1);
        }
    }
    return NULL;
}

/*****************************************************************************
 * Description  : 026 混合场景：并发循环查看点、边、undolog的内存信息输出、主键索引的表和二级索引的表的视图50次
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2020.11.21
 * Modification : Create function
 *
 * ./gmsysview -q  V\$ STORAGE_EDGE_LABEL_STAT  -e  RTOS  –s  usocket:/run/verona/unix_emserver
 * *****************************************************************************/

TEST_F(DFX_008_01, DFX_008_01_026)
{
    char *src_vertex_schema = NULL;
    char *dst_vertex_schema = NULL;
    char *edge_schema = NULL;
    void *srcVertexLabel = NULL;
    void *dstVertexLabel = NULL;

    //读取label schema
    readJanssonFile("schema_file/srcLabel.gmjson", &src_vertex_schema);
    ASSERT_NE((void *)NULL, src_vertex_schema);
    printf(RED("src_vertex_schema:%s\n"), src_vertex_schema);

    readJanssonFile("schema_file/dstLabel.gmjson", &dst_vertex_schema);
    ASSERT_NE((void *)NULL, dst_vertex_schema);
    printf(RED("dst_vertex_schema:%s\n"), dst_vertex_schema);

    //读取edge schema
    readJanssonFile("schema_file/edgeLabel.gmjson", &edge_schema);
    ASSERT_NE((void *)NULL, edge_schema);
    printf(RED("edge_schema:%s\n"), edge_schema);

    // 创建 src vertexLable
    ret = GmcCreateVertexLabel(g_stmt, src_vertex_schema, srccfgJson1);
    ASSERT_EQ(GMERR_OK, ret);
    // 创建 dest vertexLable
    ret = GmcCreateVertexLabel(g_stmt, dst_vertex_schema, dstcfgJson1);
    ASSERT_EQ(GMERR_OK, ret);
    // 创建 edgeLable
    ret = GmcCreateEdgeLabel(g_stmt, edge_schema, cfgJson);
    ASSERT_EQ(GMERR_OK, ret);

    // set srcVertex for row data
    ret = testGmcPrepareStmtByLabelName(g_stmt, edgeSrcVertexLabelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    int F0Value = 12;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
    ASSERT_EQ(GMERR_OK, ret);
    int F1Value = 13;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
    ASSERT_EQ(GMERR_OK, ret);
    int F2Value = 132;
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    ASSERT_EQ(GMERR_OK, ret);
    int F3Value = 321;
    ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_INT32, &F3Value, sizeof(F3Value));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, edgeSrcVertexLabelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    int value = 12;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "srcVertexLabel_PK0");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    bool eof = false;
    ret = GmcFetch(g_stmt, &eof);
    ASSERT_EQ(GMERR_OK, ret);

    // set dstVertex for row data
    ret = testGmcPrepareStmtByLabelName(g_stmt, edgeDstVertexLabelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    F0Value = 22;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
    ASSERT_EQ(GMERR_OK, ret);
    F1Value = 23;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
    ASSERT_EQ(GMERR_OK, ret);
    F2Value = 132;
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    ASSERT_EQ(GMERR_OK, ret);
    F3Value = 321;
    ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_INT32, &F3Value, sizeof(F3Value));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, edgeDstVertexLabelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    value = 22;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "dstVertexLabel_PK0");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    eof = false;
    ret = GmcFetch(g_stmt, &eof);
    ASSERT_EQ(GMERR_OK, ret);


    char *test_schema = NULL;
    readJanssonFile("schema_file/pk_indedx_schema1.gmjson", &test_schema);
    ASSERT_NE((void *)NULL, test_schema);
    printf(RED("pk_indedx_schema:%s\n"), test_schema);
    int ret = GmcCreateVertexLabel(g_stmt, test_schema, g_label_config);
    ASSERT_EQ(GMERR_OK, ret);
    free(test_schema);

    readJanssonFile("schema_file/local_index_schema1.gmjson", &test_schema);
    ASSERT_NE((void *)NULL, test_schema);
    printf(RED("local_index_schema:%s\n"), test_schema);
    ret = GmcCreateVertexLabel(g_stmt, test_schema, g_label_config);
    ASSERT_EQ(GMERR_OK, ret);

    int tdNum = 5;
    uint32_t a[tdNum];
    pthread_t sameNameth[tdNum];
    for (int i = 0; i < tdNum; i++) {
        a[i] = i;
        ret = pthread_create(&sameNameth[i], NULL, Thread_DFX_008_26, (void *)&a[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < tdNum; i++) {
        pthread_join(sameNameth[i], NULL);
    }

    ret = GmcDropVertexLabel(g_stmt, "pk_indedx_schema1");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "test_schema1");
    ASSERT_EQ(GMERR_OK, ret);

    free(src_vertex_schema);
    free(dst_vertex_schema);
    free(edge_schema);
    free(test_schema);
}

/*****************************************************************************
 * Description  : 027 创建一个表，包含多个二级索引，查看视图，观察每个二级索引存储开销
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2020.11.21
 * Modification : Create function
 *
 * *****************************************************************************/

TEST_F(DFX_008_01, DFX_008_01_027)
{
    int32_t value0 = 1;
    uint32_t value1 = 10;
    char *test_schema = NULL;
    void *vertexLabel = NULL;

    readJanssonFile("schema_file/multi_index_schema.gmjson", &test_schema);
    ASSERT_NE((void *)NULL, test_schema);
    printf(RED("test_schema:%s\n"), test_schema);

    int ret = GmcCreateVertexLabel(g_stmt, test_schema, g_label_config);
    ASSERT_EQ(GMERR_OK, ret);

    char const *view_name = "V\\$STORAGE_HASH_LINKLIST_INDEX_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s\n", g_toolPath, g_connServer,
        view_name);
    printf(GREEN("g_command = %s\n"), g_command);
    ret = executeCommand(g_command, "NONUNIQUEINDEX_ID", "NONUNIQUEINDEX_NAME", "local1", "local2", "GROUP_COUNT");
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, g_label_name);
    ASSERT_EQ(GMERR_OK, ret);

    free(test_schema);
}
