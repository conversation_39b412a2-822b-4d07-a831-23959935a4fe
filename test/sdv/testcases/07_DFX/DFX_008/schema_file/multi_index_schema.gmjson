[{"type": "record", "name": "test_schema", "fields": [{"name": "F0", "type": "int32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}], "keys": [{"node": "test_schema", "name": "PK", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "test_schema", "name": "local1", "fields": ["F1"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": false}}, {"node": "test_schema", "name": "local2", "fields": ["F2"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": false}}]}]