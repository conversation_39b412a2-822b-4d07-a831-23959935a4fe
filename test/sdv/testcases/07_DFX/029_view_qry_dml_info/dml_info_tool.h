extern "C" {
}
#include <stdarg.h>
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"

typedef struct tag_op_num {
    uint32_t prepare_avg_cost;
    uint32_t prepare_count;
    uint32_t execute_avg_cost;
    uint32_t execute_rows;
} op_num_t;

typedef enum { EM_INSERT = 0, EM_DELETE, EM_UPDATE, EM_REPLACE, EM_MERAGE, EM_MAX } dml_op_e;

typedef struct tag_dml_info {
    bool flag;
    op_num_t op_info[EM_MAX];
} dml_info_t;

// 3 tables
dml_info_t g_dml_info[3];
int g_info_index = -1;

#define TEST_ERROR(log, args...)                                               \
    do {                                                                       \
        fprintf(stdout, "Error: %s:%d " log "\n", __FILE__, __LINE__, ##args); \
    } while (0)

void deal_str(char *buf, const char *name1, const char *name2, const char *name3)
{
    int &index = g_info_index;
   
    static dml_op_e op = EM_MAX;

    if (name1 != NULL) {
        if (strlen(name1) > 0 && NULL != strstr(buf, name1) && g_dml_info[0].flag == 0) {
            index = 0;
        }
    }
    if (name2 != NULL) {
        if (strlen(name2) > 0 && NULL != strstr(buf, name2) && g_dml_info[0].flag == 1) {
            index = 1;
        }
    }
    if (name3 != NULL) {
        if (strlen(name3) > 0 && NULL != strstr(buf, name3) && g_dml_info[1].flag == 1) {
            index = 2;
        }
    }
    if (NULL != strstr(buf, "index")) {
        index = -1;
        op = EM_MAX;
    }

    if (index == -1) {
        return;
    }
    g_dml_info[index].flag = 1;

    if (NULL != strstr(buf, "INSERT_VERTEX") || NULL != strstr(buf, "SET_KV")) {
        op = EM_INSERT;
    }

    if (NULL != strstr(buf, "DELETE_VERTEX") || NULL != strstr(buf, "REMOVE_KV")) {
        op = EM_DELETE;
    }
    if (NULL != strstr(buf, "UPDATE_VERTEX")) {
        op = EM_UPDATE;
    }
    if (NULL != strstr(buf, "REPLACE_VERTEX")) {
        op = EM_REPLACE;
    }
    if (NULL != strstr(buf, "MERGE_VERTEX")) {
        op = EM_MERAGE;
    }

    if (op == EM_MAX) {
        return;
    }

    if (NULL != strstr(buf, "PREPARE_COUNT")) {
        // printf("%d %d %s",index, op, buf );
        char *result = strtok(buf, ":");
        g_dml_info[index].op_info[op].prepare_count = atoi(strtok(NULL, ":"));
    }

    if (NULL != strstr(buf, "EXECUTE_COUNT")) {
        // printf("%d %d %s",index, op, buf );
        char *result = strtok(buf, ":");
        g_dml_info[index].op_info[op].execute_rows = atoi(strtok(NULL, ":"));
    }

    if (NULL != strstr(buf, "AVRAGE_PREPARE")) {
        // printf("%d %d %s",index, op, buf );
        char *result = strtok(buf, ":");
        g_dml_info[index].op_info[op].prepare_avg_cost = atoi(strtok(NULL, ":"));
    }

    if (NULL != strstr(buf, "AVRAGE_EXECUTE")) {
        // printf("%d %d %s",index, op, buf );
        char *result = strtok(buf, ":");
        g_dml_info[index].op_info[op].execute_avg_cost = atoi(strtok(NULL, ":"));
    }
}

void prinf_dml_info()
{
    for (int i = 0; i < 3; i++) {
        printf("index: %d\n", i);
        printf(" insert: %u, %u avg_cost:%u %u\n", g_dml_info[i].op_info[EM_INSERT].prepare_count,
            g_dml_info[i].op_info[EM_INSERT].execute_rows, g_dml_info[i].op_info[EM_INSERT].prepare_avg_cost,
            g_dml_info[i].op_info[EM_INSERT].execute_avg_cost);
        printf(" delete: %u, %u avg_cost:%u %u\n", g_dml_info[i].op_info[EM_DELETE].prepare_count,
            g_dml_info[i].op_info[EM_DELETE].execute_rows, g_dml_info[i].op_info[EM_DELETE].prepare_avg_cost,
            g_dml_info[i].op_info[EM_DELETE].execute_avg_cost);
        printf(" update: %u, %u avg_cost:%u %u\n", g_dml_info[i].op_info[EM_UPDATE].prepare_count,
            g_dml_info[i].op_info[EM_UPDATE].execute_rows, g_dml_info[i].op_info[EM_UPDATE].prepare_avg_cost,
            g_dml_info[i].op_info[EM_UPDATE].execute_avg_cost);
        printf("replace: %u, %u avg_cost:%u %u\n", g_dml_info[i].op_info[EM_REPLACE].prepare_count,
            g_dml_info[i].op_info[EM_REPLACE].execute_rows, g_dml_info[i].op_info[EM_REPLACE].prepare_avg_cost,
            g_dml_info[i].op_info[EM_REPLACE].execute_avg_cost);
        printf("  merge: %u, %u avg_cost:%u %u\n", g_dml_info[i].op_info[EM_MERAGE].prepare_count,
            g_dml_info[i].op_info[EM_MERAGE].execute_rows, g_dml_info[i].op_info[EM_MERAGE].prepare_avg_cost,
            g_dml_info[i].op_info[EM_MERAGE].execute_avg_cost);
    }
}

uint64_t get_dml_cost(int index, dml_op_e op)
{
    if (index > 2 || index < 0) {
        TEST_ERROR("index error: %d.", index);
        return 0;
    }

    if (!g_dml_info[index].flag) {
        TEST_ERROR("no data of index: %d.", index);
        return 0;
    }

    return ((g_dml_info[index].op_info[op].prepare_avg_cost * g_dml_info[index].op_info[op].prepare_count) +
            (g_dml_info[index].op_info[op].execute_avg_cost * g_dml_info[index].op_info[op].execute_rows));
}

uint64_t get_dml_cost_all(int index)
{
    if (index > 2 || index < 0) {
        TEST_ERROR("index error: %d.", index);
        return 0;
    }

    if (!g_dml_info[index].flag) {
        TEST_ERROR("no data of index: %d.", index);
        return 0;
    }

    return (
        (g_dml_info[index].op_info[EM_INSERT].prepare_avg_cost * g_dml_info[index].op_info[EM_INSERT].prepare_count) +
        (g_dml_info[index].op_info[EM_INSERT].execute_avg_cost * g_dml_info[index].op_info[EM_INSERT].execute_rows) +
        (g_dml_info[index].op_info[EM_DELETE].prepare_avg_cost * g_dml_info[index].op_info[EM_DELETE].prepare_count) +
        (g_dml_info[index].op_info[EM_DELETE].execute_avg_cost * g_dml_info[index].op_info[EM_DELETE].execute_rows) +
        (g_dml_info[index].op_info[EM_UPDATE].prepare_avg_cost * g_dml_info[index].op_info[EM_UPDATE].prepare_count) +
        (g_dml_info[index].op_info[EM_UPDATE].execute_avg_cost * g_dml_info[index].op_info[EM_UPDATE].execute_rows) +
        (g_dml_info[index].op_info[EM_REPLACE].prepare_avg_cost * g_dml_info[index].op_info[EM_REPLACE].prepare_count) +
        (g_dml_info[index].op_info[EM_REPLACE].execute_avg_cost * g_dml_info[index].op_info[EM_REPLACE].execute_rows) +
        (g_dml_info[index].op_info[EM_MERAGE].prepare_avg_cost * g_dml_info[index].op_info[EM_MERAGE].prepare_count) +
        (g_dml_info[index].op_info[EM_MERAGE].execute_avg_cost * g_dml_info[index].op_info[EM_MERAGE].execute_rows));
}

int check_dml_info_null(int index)
{
    if (index > 2 || index < 0) {
        TEST_ERROR("index error: %d.", index);
        return 1;
    }

    if (!g_dml_info[index].flag) {
        return 0;
    }

    TEST_ERROR("index error: %d.", index);
    return 1;
}

// ds 场景，relpace操作，prepare_count 和 execute_rows不相等
int check_dml_info(int index, uint32_t insert_num, uint32_t delete_num, uint32_t update_num, uint32_t replace_num,
    uint32_t merge_num, uint32_t ds_flag = 0, uint32_t p_flag = 1)
{
    if (index > 2 || index < 0) {
        TEST_ERROR("index error: %d.", index);
        return 1;
    }

    if (!g_dml_info[index].flag) {
        TEST_ERROR("no data of index: %d.", index);
        return 1;
    }

    if (g_dml_info[index].op_info[EM_INSERT].execute_rows != insert_num) {
        TEST_ERROR("index: %d insert execute count:%u expect:%u not equal.", index,
            g_dml_info[index].op_info[EM_INSERT].execute_rows, insert_num);
        return 1;
    }

    if (g_dml_info[index].op_info[EM_DELETE].execute_rows != delete_num) {
        TEST_ERROR("index: %d delete execute count:%u expect:%u not equal.", index,
            g_dml_info[index].op_info[EM_DELETE].execute_rows, delete_num);
        return 1;
    }

    if (g_dml_info[index].op_info[EM_UPDATE].execute_rows != update_num) {
        TEST_ERROR("index: %d update execute count:%u expect:%u not equal.", index,
            g_dml_info[index].op_info[EM_UPDATE].execute_rows, update_num);
        return 1;
    }

    if (g_dml_info[index].op_info[EM_REPLACE].execute_rows != replace_num) {
        TEST_ERROR("index: %d replace execute count:%u expect:%u not equal.", index,
            g_dml_info[index].op_info[EM_REPLACE].execute_rows, replace_num);
        return 1;
    }

    if (g_dml_info[index].op_info[EM_MERAGE].execute_rows != merge_num) {
        TEST_ERROR("index: %d insert merge count:%u expect:%u not equal.", index,
            g_dml_info[index].op_info[EM_MERAGE].execute_rows, merge_num);
        return 1;
    }

    if (p_flag == 1) {
        printf(">>> check dml info success.\n");
    }
    return 0;
}

int ExecSystemCmd(char **result, const char *name1, const char *name2, const char *name3, const char *format, ...)
{
    int ret = 0;
    va_list args;
    va_start(args, format);
    char cmd[1024] = {0};
    ret = vsnprintf(cmd, sizeof(cmd), format, args);
    if (ret < 0) {
        TEST_ERROR("execute vsnprintf failed, ret = %d.", ret);
        va_end(args);
        return FAILED;
    }
    va_end(args);

    FILE *fd = popen(cmd, "r");
    if (fd == NULL) {
        TEST_ERROR("popen failed, errno = %d.", errno);
        return FAILED;
    }

    // XXX 优化为动态获取流长度
    int size = 10240;
    *result = (char *)malloc(sizeof(char) * size);
    if (result == NULL) {
        TEST_ERROR("malloc failed, errno = %d.", errno);
        return FAILED;
    }
    memset(*result, 0, size);

    char buf[1024] = {0};
    while (fgets(buf, sizeof(buf), fd) != NULL) {
        strcat((char *)*result, buf);
        deal_str(buf, name1, name2, name3);
    }

    ret = pclose(fd);
    if (ret == -1) {
        TEST_ERROR("pclose failed, errno = %d.", errno);
        return FAILED;
    }

    return  GMERR_OK;
}

int freeSystemCmd(char *result)
{
    free(result);
    return NULL;
}

#define CHECK_OK_RET(ret, format, ...)              \
    do {                                            \
        if (ret != 0) {                             \
            fprintf(stderr,                         \
                "["                                 \
                "Test"                              \
                "]["                                \
                "Error"                             \
                "][%s:%d, ret=%d] ",                \
                __FILE__, __LINE__, ret);           \
            fprintf(stderr, format, ##__VA_ARGS__); \
            fprintf(stderr, "\n");                  \
        }                                           \
    } while (0)

#define IP4_MEM_MAX_MASK_LEN_16 1000
#define IP4_MEM_MAX_MASK_LEN_24 2501000
#define IP4_MEM_HASH_CONFILICT_SIZE (15625)

int ip4forward00000_set_obj(GmcStmtT *t_stmt, uint64_t uiVrIndex, uint32_t confilict = 15625)
{
    int ret = 0;
    uint8_t value_u8 = uiVrIndex & 0xff;
    uint16_t value_u16 = uiVrIndex & 0xffff;
    uint32_t value_u32 = uiVrIndex & 0xffffffff;
    uint64_t value_u64 = uiVrIndex;
    uint32_t vrfIndex = 0;
    uint32_t vrid = 0;
    uint32_t dest_ip_addr = 0;
    uint8_t mask_len = 0;
    if (uiVrIndex <= IP4_MEM_MAX_MASK_LEN_16) {
        dest_ip_addr = ((uiVrIndex + 2) << 16);
        mask_len = ((16) & 0xff);
    } else if (uiVrIndex > IP4_MEM_MAX_MASK_LEN_16 && uiVrIndex <= IP4_MEM_MAX_MASK_LEN_24) {
        dest_ip_addr = ((uiVrIndex + 2) << 8);
        mask_len = ((24) & 0xff);
    } else {
        dest_ip_addr = ((uiVrIndex + 2));
        mask_len = ((32) & 0xff);
    }

    ret = GmcSetVertexProperty(t_stmt, "vr_id", GMC_DATATYPE_UINT32, &vrid, sizeof(vrid));
    CHECK_OK_RET(ret, "GmcSetVertexProperty vr_id.");

    ret = GmcSetVertexProperty(t_stmt, "vrf_index", GMC_DATATYPE_UINT32, &vrfIndex, sizeof(vrfIndex));
    CHECK_OK_RET(ret, "GmcSetVertexProperty vrf_index.");

    ret = GmcSetVertexProperty(t_stmt, "dest_ip_addr", GMC_DATATYPE_UINT32, &dest_ip_addr, sizeof(dest_ip_addr));
    CHECK_OK_RET(ret, "GmcSetVertexProperty dest_ip_addr.");

    ret = GmcSetVertexProperty(t_stmt, "mask_len", GMC_DATATYPE_UINT8, &mask_len, sizeof(mask_len));
    CHECK_OK_RET(ret, "GmcSetVertexProperty mask_len.");

    ret = GmcSetVertexProperty(t_stmt, "nhp_group_flag", GMC_DATATYPE_UINT8, &value_u8, sizeof(value_u8));
    CHECK_OK_RET(ret, "GmcSetVertexProperty nhp_group_flag.");

    ret = GmcSetVertexProperty(t_stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &value_u16, sizeof(value_u16));
    CHECK_OK_RET(ret, "GmcSetVertexProperty qos_profile_id.");

    ret = GmcSetVertexProperty(t_stmt, "primary_label", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
    CHECK_OK_RET(ret, "GmcSetVertexProperty primary_label.");

    ret = GmcSetVertexProperty(t_stmt, "attribute_id", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
    CHECK_OK_RET(ret, "GmcSetVertexProperty attribute_id.");

    // uint32_t nhp_group_id = value_u32 % IP4_MEM_HASH_CONFILICT_SIZE;
    uint32_t nhp_group_id = value_u32 % confilict;
    ret = GmcSetVertexProperty(t_stmt, "nhp_group_id", GMC_DATATYPE_UINT32, &nhp_group_id, sizeof(nhp_group_id));
    CHECK_OK_RET(ret, "GmcSetVertexProperty nhp_group_id.");

    ret = GmcSetVertexProperty(t_stmt, "path_flags", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
    CHECK_OK_RET(ret, "GmcSetVertexProperty path_flags.");

    ret = GmcSetVertexProperty(t_stmt, "flags", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
    CHECK_OK_RET(ret, "GmcSetVertexProperty flags.");

    ret = GmcSetVertexProperty(t_stmt, "status_high_prio", GMC_DATATYPE_UINT8, &value_u8, sizeof(value_u8));
    CHECK_OK_RET(ret, "GmcSetVertexProperty status_high_prio.");

    ret = GmcSetVertexProperty(t_stmt, "status_normal_prio", GMC_DATATYPE_UINT8, &value_u8, sizeof(value_u8));
    CHECK_OK_RET(ret, "GmcSetVertexProperty status_normal_prio.");

    ret = GmcSetVertexProperty(t_stmt, "errcode_high_prio", GMC_DATATYPE_UINT8, &value_u8, sizeof(value_u8));
    CHECK_OK_RET(ret, "GmcSetVertexProperty errcode_high_prio.");

    ret = GmcSetVertexProperty(t_stmt, "errcode_normal_prio", GMC_DATATYPE_UINT8, &value_u8, sizeof(value_u8));
    CHECK_OK_RET(ret, "GmcSetVertexProperty errcode_normal_prio.");

    ret = GmcSetVertexProperty(t_stmt, "app_source_id", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
    CHECK_OK_RET(ret, "GmcSetVertexProperty app_source_id.");

    uint32_t smooth_id = value_u32 % 10;
    ret = GmcSetVertexProperty(t_stmt, "table_smooth_id", GMC_DATATYPE_UINT32, &smooth_id, sizeof(smooth_id));
    CHECK_OK_RET(ret, "GmcSetVertexProperty table_smooth_id.");

    ret = GmcSetVertexProperty(t_stmt, "app_obj_id", GMC_DATATYPE_UINT64, &value_u64, sizeof(value_u64));
    CHECK_OK_RET(ret, "GmcSetVertexProperty app_obj_id.");

    ret = GmcSetVertexProperty(t_stmt, "app_version", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
    CHECK_OK_RET(ret, "GmcSetVertexProperty app_version.");

    ret = GmcSetVertexProperty(t_stmt, "trace", GMC_DATATYPE_UINT64, &value_u64, sizeof(value_u64));
    CHECK_OK_RET(ret, "GmcSetVertexProperty trace.");

    ret = GmcSetVertexProperty(t_stmt, "route_flags", GMC_DATATYPE_UINT16, &value_u16, sizeof(value_u16));
    CHECK_OK_RET(ret, "GmcSetVertexProperty route_flags.");

    ret = GmcSetVertexProperty(t_stmt, "reserved", GMC_DATATYPE_UINT16, &value_u16, sizeof(value_u16));
    CHECK_OK_RET(ret, "GmcSetVertexProperty reserved.");

    ret = GmcSetVertexProperty(t_stmt, "reserved", GMC_DATATYPE_UINT16, &value_u16, sizeof(value_u16));
    CHECK_OK_RET(ret, "GmcSetVertexProperty reserved.");

    ret = GmcSetVertexProperty(t_stmt, "time_stamp_create", GMC_DATATYPE_TIME, &value_u64, sizeof(value_u64));
    CHECK_OK_RET(ret, "GmcSetVertexProperty time_stamp_create.");

    ret = GmcSetVertexProperty(t_stmt, "time_stamp_smooth", GMC_DATATYPE_TIME, &value_u64, sizeof(value_u64));
    CHECK_OK_RET(ret, "GmcSetVertexProperty trace.");

    return ret;
}
int ip4forward00000_merge_obj(GmcStmtT *t_stmt, uint64_t uiVrIndex, uint32_t confilict = 15625)
{
    int ret = 0;
    uint8_t value_u8 = uiVrIndex & 0xff;
    uint16_t value_u16 = uiVrIndex & 0xffff;
    uint32_t value_u32 = uiVrIndex & 0xffffffff;
    uint64_t value_u64 = uiVrIndex;
    uint32_t vrfIndex = 0;
    uint32_t vrid = 0;
    uint32_t dest_ip_addr = 0;
    uint8_t mask_len = 0;
    if (uiVrIndex <= IP4_MEM_MAX_MASK_LEN_16) {
        dest_ip_addr = ((uiVrIndex + 2) << 16);
        mask_len = ((16) & 0xff);
    } else if (uiVrIndex > IP4_MEM_MAX_MASK_LEN_16 && uiVrIndex <= IP4_MEM_MAX_MASK_LEN_24) {
        dest_ip_addr = ((uiVrIndex + 2) << 8);
        mask_len = ((24) & 0xff);
    } else {
        dest_ip_addr = ((uiVrIndex + 2));
        mask_len = ((32) & 0xff);
    }
    ret = GmcSetIndexKeyValue(t_stmt, 0, GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(t_stmt, 1, GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(t_stmt, 2, GMC_DATATYPE_UINT32, &dest_ip_addr, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(t_stmt, 3, GMC_DATATYPE_UINT8, &mask_len, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(t_stmt, "ip4_key");
    EXPECT_EQ(GMERR_OK, ret);
    /*
    ret = GmcSetVertexProperty(t_stmt, "vr_id", GMC_DATATYPE_UINT32, &vrid, sizeof(vrid));
    CHECK_OK_RET(ret, "GmcSetVertexProperty vr_id.");

    uint32_t vrfIndex = 0;
    ret = GmcSetVertexProperty(t_stmt, "vrf_index", GMC_DATATYPE_UINT32, &vrfIndex, sizeof(vrfIndex));
    CHECK_OK_RET(ret, "GmcSetVertexProperty vrf_index.");

    ret = GmcSetVertexProperty(t_stmt, "dest_ip_addr", GMC_DATATYPE_UINT32, &dest_ip_addr, sizeof(dest_ip_addr));
    CHECK_OK_RET(ret, "GmcSetVertexProperty dest_ip_addr.");

    ret = GmcSetVertexProperty(t_stmt, "mask_len", GMC_DATATYPE_UINT8, &mask_len, sizeof(mask_len));
    CHECK_OK_RET(ret, "GmcSetVertexProperty mask_len.");*/

    ret = GmcSetVertexProperty(t_stmt, "nhp_group_flag", GMC_DATATYPE_UINT8, &value_u8, sizeof(value_u8));
    CHECK_OK_RET(ret, "GmcSetVertexProperty nhp_group_flag.");

    ret = GmcSetVertexProperty(t_stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &value_u16, sizeof(value_u16));
    CHECK_OK_RET(ret, "GmcSetVertexProperty qos_profile_id.");

    ret = GmcSetVertexProperty(t_stmt, "primary_label", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
    CHECK_OK_RET(ret, "GmcSetVertexProperty primary_label.");

    ret = GmcSetVertexProperty(t_stmt, "attribute_id", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
    CHECK_OK_RET(ret, "GmcSetVertexProperty attribute_id.");

    // uint32_t nhp_group_id = value_u32 % IP4_MEM_HASH_CONFILICT_SIZE;
    uint32_t nhp_group_id = value_u32 % confilict;
    ret = GmcSetVertexProperty(t_stmt, "nhp_group_id", GMC_DATATYPE_UINT32, &nhp_group_id, sizeof(nhp_group_id));
    CHECK_OK_RET(ret, "GmcSetVertexProperty nhp_group_id.");

    ret = GmcSetVertexProperty(t_stmt, "path_flags", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
    CHECK_OK_RET(ret, "GmcSetVertexProperty path_flags.");

    ret = GmcSetVertexProperty(t_stmt, "flags", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
    CHECK_OK_RET(ret, "GmcSetVertexProperty flags.");

    ret = GmcSetVertexProperty(t_stmt, "status_high_prio", GMC_DATATYPE_UINT8, &value_u8, sizeof(value_u8));
    CHECK_OK_RET(ret, "GmcSetVertexProperty status_high_prio.");

    ret = GmcSetVertexProperty(t_stmt, "status_normal_prio", GMC_DATATYPE_UINT8, &value_u8, sizeof(value_u8));
    CHECK_OK_RET(ret, "GmcSetVertexProperty status_normal_prio.");

    ret = GmcSetVertexProperty(t_stmt, "errcode_high_prio", GMC_DATATYPE_UINT8, &value_u8, sizeof(value_u8));
    CHECK_OK_RET(ret, "GmcSetVertexProperty errcode_high_prio.");

    ret = GmcSetVertexProperty(t_stmt, "errcode_normal_prio", GMC_DATATYPE_UINT8, &value_u8, sizeof(value_u8));
    CHECK_OK_RET(ret, "GmcSetVertexProperty errcode_normal_prio.");

    ret = GmcSetVertexProperty(t_stmt, "app_source_id", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
    CHECK_OK_RET(ret, "GmcSetVertexProperty app_source_id.");

    uint32_t smooth_id = value_u32 % 10;
    ret = GmcSetVertexProperty(t_stmt, "table_smooth_id", GMC_DATATYPE_UINT32, &smooth_id, sizeof(smooth_id));
    CHECK_OK_RET(ret, "GmcSetVertexProperty table_smooth_id.");

    ret = GmcSetVertexProperty(t_stmt, "app_obj_id", GMC_DATATYPE_UINT64, &value_u64, sizeof(value_u64));
    CHECK_OK_RET(ret, "GmcSetVertexProperty app_obj_id.");

    ret = GmcSetVertexProperty(t_stmt, "app_version", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
    CHECK_OK_RET(ret, "GmcSetVertexProperty app_version.");

    ret = GmcSetVertexProperty(t_stmt, "trace", GMC_DATATYPE_UINT64, &value_u64, sizeof(value_u64));
    CHECK_OK_RET(ret, "GmcSetVertexProperty trace.");

    ret = GmcSetVertexProperty(t_stmt, "route_flags", GMC_DATATYPE_UINT16, &value_u16, sizeof(value_u16));
    CHECK_OK_RET(ret, "GmcSetVertexProperty route_flags.");

    ret = GmcSetVertexProperty(t_stmt, "reserved", GMC_DATATYPE_UINT16, &value_u16, sizeof(value_u16));
    CHECK_OK_RET(ret, "GmcSetVertexProperty reserved.");

    ret = GmcSetVertexProperty(t_stmt, "reserved", GMC_DATATYPE_UINT16, &value_u16, sizeof(value_u16));
    CHECK_OK_RET(ret, "GmcSetVertexProperty reserved.");

    ret = GmcSetVertexProperty(t_stmt, "time_stamp_create", GMC_DATATYPE_TIME, &value_u64, sizeof(value_u64));
    CHECK_OK_RET(ret, "GmcSetVertexProperty time_stamp_create.");

    ret = GmcSetVertexProperty(t_stmt, "time_stamp_smooth", GMC_DATATYPE_TIME, &value_u64, sizeof(value_u64));
    CHECK_OK_RET(ret, "GmcSetVertexProperty trace.");

    return ret;
}

int ip4forward00000_pri_key_set(GmcStmtT *t_stmt, uint64_t uiVrIndex)
{
    int ret = 0;

    uint32_t vrid = 0;
    ret = GmcSetIndexKeyValue(t_stmt, 0, GMC_DATATYPE_UINT32, &vrid, sizeof(vrid));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue vr_id.");

    uint32_t vrfIndex = 0;
    ret = GmcSetIndexKeyValue(t_stmt, 1, GMC_DATATYPE_UINT32, &vrfIndex, sizeof(vrfIndex));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue vrf_index.");

    uint32_t dest_ip_addr = 0;
    uint8_t mask_len = 0;
    if (uiVrIndex <= IP4_MEM_MAX_MASK_LEN_16) {
        dest_ip_addr = ((uiVrIndex + 2) << 16);
        mask_len = ((16) & 0xff);
    } else if (uiVrIndex > IP4_MEM_MAX_MASK_LEN_16 && uiVrIndex <= IP4_MEM_MAX_MASK_LEN_24) {
        dest_ip_addr = ((uiVrIndex + 2) << 8);
        mask_len = ((24) & 0xff);
    } else {
        dest_ip_addr = ((uiVrIndex + 2));
        mask_len = ((32) & 0xff);
    }

    ret = GmcSetIndexKeyValue(t_stmt, 2, GMC_DATATYPE_UINT32, &dest_ip_addr, sizeof(dest_ip_addr));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue dest_ip_addr.");

    ret = GmcSetIndexKeyValue(t_stmt, 3, GMC_DATATYPE_UINT8, &mask_len, sizeof(mask_len));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue mask_len.");

    return ret;
}

int ip4forward00000_read_by_key(GmcStmtT *t_stmt, void *vertexLabel, uint64_t uiVrIndex, void *strVal, uint32_t strSize)
{
    int ret = ip4forward00000_pri_key_set(t_stmt, uiVrIndex);
    if (ret) {
        printf("ip4forward00000_pri_key_set index:%llu .\n", uiVrIndex);
        return -1;
    }

    ret = GmcSetIndexKeyName(t_stmt, "ip4_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(t_stmt);
    if (ret) {
        printf("GmcDirectFetchVertex index:%llu .\n", uiVrIndex);
        return -1;
    }

    GmcNodeT *root;
    uint32_t vrid = 0;
    bool isNull = 0;
    ret = GmcGetRootNode(t_stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);

    // ret = GmcGetNodePropertyByName(t_stmt, (char *)"vr_id", &vrid, sizeof(uint32_t), &isNull);
    // CHECK_OK_RET(ret, "GmcGetNodePropertyByName vr_id.");
    ret = GmcNodeGetPropertyByName(root, (char *)"vr_id", &vrid, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);

    return ret;
}

int ip4forward00000_lpm_by_key(GmcStmtT *t_stmt, void *vertexLabel, uint64_t uiVrIndex, void *strVal, uint32_t strSize)
{
    int ret = ip4forward00000_pri_key_set(t_stmt, uiVrIndex);
    if (ret) {
        printf("ip4forward00000_pri_key_set index:%llu .\n", uiVrIndex);
        return -1;
    }

    // qry vertex
    ret = GmcSetIndexKeyName(t_stmt, "ip4_lpm");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(t_stmt);
    if (ret) {
        printf("GmcExecScanVertex index:%llu .\n", uiVrIndex);
        return -1;
    }

    for (unsigned int cnt = 0;; ++cnt) {
        bool isFinish;
        ret = GmcFetch(t_stmt, &isFinish);
        if (ret) {
            printf("GmcFetch index:%llu .\n", uiVrIndex);
            return -1;
        }

        if (isFinish) {
            if (cnt != 1) {
                printf("GmcFetch index:%llu cnt:%u.\n", uiVrIndex, cnt);
            }
            break;
        }
    }

    return ret;
}

void TestGmcSetNodePropertyByName_PK(GmcNodeT *root, int i)
{
    int ret = 0;
    int64_t f0_value = i;
    // ret = GmcSetNodePropertyByName(stmt, (char *)"F0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    // ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(root, (char *)"F0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
}

void TestGmcSetNodePropertyByName_R(GmcNodeT *root, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    int8_t value_8 = i % 128;
    uint8_t value_u8 = i % 256;
    int16_t value_16 = i % 32768;
    uint16_t value_u16 = i % 65566;

    uint64_t f1_value = i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    int32_t f2_value = 2 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t f3_value = 3 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    int16_t f4_value = value_16;
    ret = GmcNodeSetPropertyByName(root, (char *)"F4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint16_t f5_value = value_u16;
    ret = GmcNodeSetPropertyByName(root, (char *)"F5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    int8_t f6_value = value_8;
    ret = GmcNodeSetPropertyByName(root, (char *)"F6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t f7_value = value_u8;
    ret = GmcNodeSetPropertyByName(root, (char *)"F7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(root, (char *)"F8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);

    float f9_value = 9 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);

    double f10_value = 10 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    char f12_value = value_8;
    ret = GmcNodeSetPropertyByName(root, (char *)"F12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);

    unsigned char f13_value = value_u8;
    ret = GmcNodeSetPropertyByName(root, (char *)"F13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(root, (char *)"F14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(root, (char *)"F15", GMC_DATATYPE_BYTES, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(root, (char *)"F16", GMC_DATATYPE_FIXED, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);
}

void TestGmcSetNodePropertyByName_P(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    int8_t value_8 = i % 128;
    uint8_t value_u8 = i % 256;
    int16_t value_16 = i % 32768;
    uint16_t value_u16 = i % 65566;

    int64_t f0_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f1_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    int32_t f2_value = 2 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t f3_value = 3 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    int16_t f4_value = value_16;
    ret = GmcNodeSetPropertyByName(node, (char *)"P4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint16_t f5_value = value_u16;
    ret = GmcNodeSetPropertyByName(node, (char *)"P5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    int8_t f6_value = value_8;
    ret = GmcNodeSetPropertyByName(node, (char *)"P6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t f7_value = value_u8;
    ret = GmcNodeSetPropertyByName(node, (char *)"P7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(node, (char *)"P8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);

    float f9_value = 9 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);

    double f10_value = 10 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    char f12_value = value_8;
    ret = GmcNodeSetPropertyByName(node, (char *)"P12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);

    unsigned char f13_value = value_u8;
    ret = GmcNodeSetPropertyByName(node, (char *)"P13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"P14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"P15", GMC_DATATYPE_BYTES, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"P16", GMC_DATATYPE_FIXED, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);
}

void TestGmcSetNodePropertyByName_A(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    int8_t value_8 = i % 128;
    uint8_t value_u8 = i % 256;
    int16_t value_16 = i % 32768;
    uint16_t value_u16 = i % 65566;

    int64_t f0_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f1_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    int32_t f2_value = 2 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t f3_value = 3 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    int16_t f4_value = value_16;
    ret = GmcNodeSetPropertyByName(node, (char *)"A4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint16_t f5_value = value_u16;
    ret = GmcNodeSetPropertyByName(node, (char *)"A5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    int8_t f6_value = value_8;
    ret = GmcNodeSetPropertyByName(node, (char *)"A6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t f7_value = value_u8;
    ret = GmcNodeSetPropertyByName(node, (char *)"A7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(node, (char *)"A8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);

    float f9_value = 9 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);

    double f10_value = 10 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    char f12_value = value_8;
    ret = GmcNodeSetPropertyByName(node, (char *)"A12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);

    unsigned char f13_value = value_u8;
    ret = GmcNodeSetPropertyByName(node, (char *)"A13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"A14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"A15", GMC_DATATYPE_BYTES, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"A16", GMC_DATATYPE_FIXED, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);
}

void TestGmcSetNodePropertyByName_V(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    int8_t value_8 = i % 128;
    uint8_t value_u8 = i % 256;
    int16_t value_16 = i % 32768;
    uint16_t value_u16 = i % 65566;

    int64_t f0_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f1_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    int32_t f2_value = 2 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t f3_value = 3 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    int16_t f4_value = value_16;
    ret = GmcNodeSetPropertyByName(node, (char *)"V4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint16_t f5_value = value_u16;
    ret = GmcNodeSetPropertyByName(node, (char *)"V5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    int8_t f6_value = value_8;
    ret = GmcNodeSetPropertyByName(node, (char *)"V6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t f7_value = value_u8;
    ret = GmcNodeSetPropertyByName(node, (char *)"V7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(node, (char *)"V8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);

    float f9_value = 9 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);

    double f10_value = 10 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    char f12_value = value_8;
    ret = GmcNodeSetPropertyByName(node, (char *)"V12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);

    unsigned char f13_value = value_u8;
    ret = GmcNodeSetPropertyByName(node, (char *)"V13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"V14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"V15", GMC_DATATYPE_BYTES, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"V16", GMC_DATATYPE_FIXED, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);
}

void TestGmcSetNodePropertyByName_V_T4(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    int8_t value_8 = i % 128;
    uint8_t value_u8 = i % 256;
    int16_t value_16 = i % 32768;
    uint16_t value_u16 = i % 65566;

    int64_t f0_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f1_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    int32_t f2_value = 2 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t f3_value = 3 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    int16_t f4_value = value_16;
    ret = GmcNodeSetPropertyByName(node, (char *)"V4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint16_t f5_value = value_u16;
    ret = GmcNodeSetPropertyByName(node, (char *)"V5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    int8_t f6_value = value_8;
    ret = GmcNodeSetPropertyByName(node, (char *)"V6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t f7_value = value_u8;
    ret = GmcNodeSetPropertyByName(node, (char *)"V7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(node, (char *)"V8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);

    float f9_value = 9 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);

    double f10_value = 10 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    char f12_value = value_8;
    ret = GmcNodeSetPropertyByName(node, (char *)"V12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);

    unsigned char f13_value = value_u8;
    ret = GmcNodeSetPropertyByName(node, (char *)"V13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"V14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"V15", GMC_DATATYPE_BYTES, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"V16", GMC_DATATYPE_FIXED, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);
}

void TestGmcSetNodePropertyByName_V_T1_T5(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    int8_t value_8 = i % 128;
    uint8_t value_u8 = i % 256;
    int16_t value_16 = i % 32768;
    uint16_t value_u16 = i % 65566;

    int64_t f0_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f1_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    int32_t f2_value = 2 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t f3_value = 3 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    int16_t f4_value = value_16;
    ret = GmcNodeSetPropertyByName(node, (char *)"V4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint16_t f5_value = value_u16;
    ret = GmcNodeSetPropertyByName(node, (char *)"V5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    int8_t f6_value = value_8;
    ret = GmcNodeSetPropertyByName(node, (char *)"V6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t f7_value = value_u8;
    ret = GmcNodeSetPropertyByName(node, (char *)"V7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(node, (char *)"V8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);

    float f9_value = 9 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);

    double f10_value = 10 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    char f12_value = value_8;
    ret = GmcNodeSetPropertyByName(node, (char *)"V12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);

    unsigned char f13_value = value_u8;
    ret = GmcNodeSetPropertyByName(node, (char *)"V13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"V14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"V15", GMC_DATATYPE_BYTES, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"V16", GMC_DATATYPE_FIXED, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);
}

void Tree_Vector_set_obj(GmcStmtT *stmt, int index, bool bool_value, char *f14_value, int array_num, int vector_num)
{
    // 插入顶点
    int i = 1;

    GmcNodeT *root;
    int ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);

    TestGmcSetNodePropertyByName_PK(root, i * index);
    TestGmcSetNodePropertyByName_R(root, i * index, bool_value, f14_value);

    GmcNodeT *node_T1;
    ret = GmcNodeGetChild(root, "T1", &node_T1);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcSetNodePropertyByName_P(node_T1, i * index, bool_value, f14_value);

    // 插入array节点
    GmcNodeT *node_T1_T2;
    ret = GmcNodeGetChild(node_T1, "T2", &node_T1_T2);
    EXPECT_EQ(GMERR_OK, ret);

    // int ret = GmcSetNodeRecordIndex(stmt, (char *)"T1.T2", 0);
    // ASSERT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < array_num; j++) {
        TestGmcSetNodePropertyByName_A(node_T1_T2, i * index, bool_value, f14_value);
        // ret = GmcGetNodeNextRecord(stmt, (char *)"T1.T2");
        ret = GmcNodeGetNextElement(node_T1_T2, &node_T1_T2);
    }

    // 插入vector节点1
    GmcNodeT *node_T3;
    ret = GmcNodeGetChild(root, "T3", &node_T3);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < vector_num; j++) {
        // ret = GmcAddNodeVectorRecord(stmt, (char *)"T3");
        // ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(node_T3, &node_T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_V(node_T3, i * index, bool_value, f14_value);
    }

    // 插入vector节点2
    GmcNodeT *node_T4;
    ret = GmcNodeGetChild(root, "T4", &node_T4);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < vector_num; j++) {
        // ret = GmcAddNodeVectorRecord(stmt, (char *)"T4");
        // ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(node_T4, &node_T4);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_V_T4(node_T4, i * index, bool_value, f14_value);
    }
    // 插入vector节点3
    GmcNodeT *node_T1_T5;
    ret = GmcNodeGetChild(node_T1, "T5", &node_T1_T5);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < vector_num; j++) {
        // ret = GmcAddNodeVectorRecord(stmt, (char *)"T1.T5");
        // ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(node_T1_T5, &node_T1_T5);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_V_T1_T5(node_T1_T5, i * index, bool_value, f14_value);
    }
}

int Tree_Vector_pri_key_set(GmcStmtT *t_stmt, int uiVrIndex)
{
    int ret = 0;

    int64_t f0_value = uiVrIndex;
    ret = GmcSetIndexKeyValue(t_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue F0.");

    return ret;
}

void Tree_Vector_merge_obj(GmcStmtT *stmt, int index, bool bool_value, char *f14_value, int array_num, int vector_num)
{
    // 插入顶点
    int i = 1;
    Tree_Vector_pri_key_set(stmt, i * index);
    int ret1 = GmcSetIndexKeyName(stmt, "Tree_Vector_PK");
    EXPECT_EQ(GMERR_OK, ret1);

    GmcNodeT *root;
    int ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);

    //  TestGmcSetNodePropertyByName_PK(root, i * index);
    TestGmcSetNodePropertyByName_R(root, i * index, bool_value, f14_value);

    GmcNodeT *node_T1;
    ret = GmcNodeGetChild(root, "T1", &node_T1);
    EXPECT_EQ(GMERR_OK, ret);
    TestGmcSetNodePropertyByName_P(node_T1, i * index, bool_value, f14_value);

    // 插入array节点
    GmcNodeT *node_T1_T2;
    ret = GmcNodeGetChild(node_T1, "T2", &node_T1_T2);
    EXPECT_EQ(GMERR_OK, ret);

    // int ret = GmcSetNodeRecordIndex(stmt, (char *)"T1.T2", 0);
    // ASSERT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < array_num; j++) {
        TestGmcSetNodePropertyByName_A(node_T1_T2, i * index, bool_value, f14_value);
        // ret = GmcGetNodeNextRecord(stmt, (char *)"T1.T2");
        ret = GmcNodeGetNextElement(node_T1_T2, &node_T1_T2);
    }

    // 插入vector节点1
    GmcNodeT *node_T3;
    ret = GmcNodeGetChild(root, "T3", &node_T3);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < vector_num; j++) {
        // ret = GmcAddNodeVectorRecord(stmt, (char *)"T3");
        // ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(node_T3, &node_T3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_V(node_T3, i * index, bool_value, f14_value);
    }

    // 插入vector节点2
    GmcNodeT *node_T4;
    ret = GmcNodeGetChild(root, "T4", &node_T4);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < vector_num; j++) {
        // ret = GmcAddNodeVectorRecord(stmt, (char *)"T4");
        // ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(node_T4, &node_T4);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_V_T4(node_T4, i * index, bool_value, f14_value);
    }
    // 插入vector节点3
    GmcNodeT *node_T1_T5;
    ret = GmcNodeGetChild(node_T1, "T5", &node_T1_T5);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < vector_num; j++) {
        // ret = GmcAddNodeVectorRecord(stmt, (char *)"T1.T5");
        // ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(node_T1_T5, &node_T1_T5);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_V_T1_T5(node_T1_T5, i * index, bool_value, f14_value);
    }
}

int32_t TestSetVectorUpdateIndex(GmcStmtT *stmt, char *nodeName, uint32_t index)
{
    int32_t ret;
    // void *batch = NULL;
    // uint32_t indexTmp = index;

    // ret = GmcDynamicArrayAlloc(stmt, &batch, GMC_INDEX_LIST);
    // EXPECT_EQ(GMERR_OK, ret);
    // ret = GmcDynamicArrayAppend(batch, GMC_DATATYPE_UINT32, (void *)&indexTmp, sizeof(uint32_t));
    // EXPECT_EQ(GMERR_OK, ret);
    // ret = GmcDeltaVectorUpdate(stmt, nodeName, batch);
    // EXPECT_EQ(GMERR_OK, ret);
    // GmcDynamicArrayDestroy(batch);
    // batch = NULL;
    // return ret;
    return NULL;
}

int32_t TestSetVectorRemoveIndex(GmcStmtT *stmt, char *nodeName, uint32_t index)
{
    int32_t ret;
    // void *batch = NULL;
    // uint32_t indexTmp = index;
    // ret = GmcDynamicArrayAlloc(stmt, &batch, GMC_INDEX_LIST);
    // EXPECT_EQ(GMERR_OK, ret);
    // ret = GmcDynamicArrayAppend(batch, GMC_DATATYPE_UINT32, (void *)&indexTmp, sizeof(uint32_t));
    // EXPECT_EQ(GMERR_OK, ret);
    // ret = GmcDeltaVectorRemove(stmt, nodeName, batch);
    // EXPECT_EQ(GMERR_OK, ret);
    // GmcDynamicArrayDestroy(batch);
    // batch = NULL;

    GmcNodeT *root;
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);

    GmcNodeT *node;
    ret = GmcNodeGetChild(root, nodeName, &node);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcNodeRemoveElementByIndex(node, index);
    EXPECT_EQ(GMERR_OK, ret);

    return ret;
}

// int32_t TestSetVectorAppendIndex(GmcStmtT *stmt, char *nodeName, uint32_t index)
// {
//     int32_t ret;
//     void *batch = NULL;
//     uint32_t indexTmp = index;
//     ret = GmcDynamicArrayAlloc(stmt, &batch, GMC_INDEX_LIST);
//     EXPECT_EQ(GMERR_OK, ret);
//     ret = GmcDynamicArrayAppend(batch, GMC_DATATYPE_UINT32, (void *)&indexTmp, sizeof(uint32_t));
//     EXPECT_EQ(GMERR_OK, ret);
//     ret = GmcDeltaVectorAppend(stmt, nodeName, batch);
//     EXPECT_EQ(GMERR_OK, ret);
//     GmcDynamicArrayDestroy(batch);
//     batch = NULL;
//     return ret;
// }

void normal_insert_layer1(GmcStmtT *stmtVsys, int v1_id)
{
    // insert
    int ret = GmcSetVertexProperty(stmtVsys, "id", GMC_DATATYPE_INT32, &v1_id, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmtVsys);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(stmtVsys, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);
}

void normal_insert_layer2(GmcStmtT *stmtRule, int v2_vsys_id, int v2_id, void *v2_name)
{
    // insert
    int ret = GmcSetVertexProperty(stmtRule, "vsys::id", GMC_DATATYPE_INT32, &v2_vsys_id, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmtRule, "id", GMC_DATATYPE_INT32, &v2_id, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmtRule, "name", GMC_DATATYPE_STRING, v2_name, strlen((const char *)v2_name));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmtRule);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(stmtRule, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);
}

void normal_insert_layer3(GmcStmtT *stmtS_ip, int v3_vsys_id, int v3_rule_id, int v3_ipLen, int v3_masklen)
{
    // 插入insert
    int ret = GmcSetVertexProperty(stmtS_ip, "rule::vsys::id", GMC_DATATYPE_INT32, &v3_vsys_id, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmtS_ip, "rule::id", GMC_DATATYPE_INT32, &v3_rule_id, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmtS_ip, "ipLen", GMC_DATATYPE_INT32, &v3_ipLen, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmtS_ip, "maskLen", GMC_DATATYPE_INT32, &v3_masklen, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmtS_ip);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(stmtS_ip, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);
}

void normal_delete_layer1(GmcStmtT *stmtVsys, void *vsysLabel, int v1_id)
{
    int ret = GmcSetIndexKeyValue(stmtVsys, 0, GMC_DATATYPE_INT32, &v1_id, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmtVsys, "id");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmtVsys);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(stmtVsys, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);
}

void normal_delete_layer2(GmcStmtT *stmtRule, void *ruleLabel, int v2_vsys_id, int v2_id)
{
    int ret = GmcSetIndexKeyValue(stmtRule, 0, GMC_DATATYPE_INT32, &v2_vsys_id, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmtRule, 1, GMC_DATATYPE_INT32, &v2_id, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmtRule, "vsys::id_id");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmtRule);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(stmtRule, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);
}

void normal_delete_layer3(
    GmcStmtT *stmtS_ip, void *s_ipLabel, int v3_vsys_id, int v3_rule_id, int v3_ipLen, int v3_masklen)
{
    int ret = GmcSetIndexKeyValue(stmtS_ip, 0, GMC_DATATYPE_INT32, &v3_vsys_id, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmtS_ip, 1, GMC_DATATYPE_INT32, &v3_rule_id, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmtS_ip, 2, GMC_DATATYPE_INT32, &v3_ipLen, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmtS_ip, 3, GMC_DATATYPE_INT32, &v3_masklen, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmtS_ip, "vsys.rule.source_ip_K0");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmtS_ip);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(stmtS_ip, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);
}

#define MAX_CMD_SIZE 1024
char g_user[] = "user123";
char g_password[] = "password.123";
char g_command[MAX_CMD_SIZE];

int dml_sysview_query(const char *table, const char *name1, const char *name2, const char *name3, int p_flag = 1)
{
    // 查询视图报错
    char *result = NULL;
    char const *view_name = "V\\$QRY_DML_INFO ";
    memset(g_command, 0, sizeof(g_command));

    if (table == NULL) {
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s ", g_toolPath, view_name);
    } else {
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f LABEL_NAME=\'%s\'", g_toolPath, view_name, table);
    }

    g_info_index = -1;
    memset(g_dml_info, 0, sizeof(dml_info_t) * 3);
    int ret = ExecSystemCmd(&result, name1, name2, name3, g_command);
    if (p_flag == 1) {
        printf(" cmd: %s\n", g_command);
        printf("len:%d %s", strlen(result), result);
        (void)prinf_dml_info();
    }
    (void)free(result);
    return ret;
}

// 异步回调函数处理batchRet, 将batchRet内需要导出的内容导出至userData中
// callback 为用户定义函数，这里可以按照需求获取需要的
// 结果需要在callback中处理完
void BatchAsyncCallBack(void *userData, GmcBatchRetT *batchRet, int32_t status, const char *errMsg)
{
    AsyncUserDataT *data = (AsyncUserDataT *)userData;
    data->status = status;
    uint32_t t_totalNum;
    uint32_t t_succNum;
    int ret = GmcBatchDeparseRet(batchRet, &t_totalNum, &t_succNum);
    if (ret != GMERR_OK) {
        return;
    }
    data->totalNum = t_totalNum;
    data->succNum = t_succNum;
    data->historyRecvNum++;
    data->recvNum++;

#if 0
    if (data->successNum != data->totalNum) {
        ret = GmcBatchGetFailedNum(batchRet, &data->failedNum);
        uint32_t failedIndexList[data->failedNum];
        uint32_t failedErrorCodeList[data->failedNum];
        ret = GmcBatchGetFailedList(batchRet, failedIndexList, failedErrorCodeList);
        // 这里解析失败的index和失败的opcode
        for (int i = 0; i < data->failedNum; i++) {
            printf("failed task index is : %d", failedIndexList[i]); // on contine mode here we can print failed index
            printf("failed task Opcode is : %d", failedErrorCodeList[i]);
        }
    }


    // 这里展示如果有resource表的情况
    uint32_t bufLen;
    GmcBatchGetResIdNum(batchRet, data->labelName, &bufLen);
    // 使用栈上的动态数组承接返回值实现内存高效使用
    uint64_t resId[bufLen];
    // resource 返回值resource表适用, 不含有resource的表执行GmcBatchGetResIdInfo会直接报错
    // batch支持多表后需要提供表名获得对应的resource
    // 这里将resource和stmt彻底分隔从batchRet中获取resourceId
    // data->labelName为NULL全部返回
    ret = GmcBatchGetResIdInfo(batchRet, data->labelName, resId);
    if (ret != GMERR_OK) {
        return;
    }
    for (uint32_t i = 0; i < bufLen; i++) {
        uint16_t poolId = 0;
        uint16_t count = 0;
        uint32_t startIndex = 0;
        ret = GmcGetCountResource (resId[i], &count);
        ret = GmcGetPoolIdResource (resId[i], &poolId);
        ret = GmcGetStartIdxResource (resId[i], &startIndex);
        printf("resoruce pool id is : %u", poolId);
        printf("count is : %u", count);
        printf("sart index is : %u", startIndex);
    }
    (*data->received)++;
#endif
}
