extern "C" {
}
#include <stdarg.h>
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "dml_info_tool.h"

GmcConnT *g_conn = NULL;  // conn
GmcStmtT *g_stmt = NULL;  // stmt

char g_label_config[] = "{\"max_record_count\":1000}";

using namespace std;

class dml_info_config_test : public testing::Test {
protected:
    static void SetUpTestCase()
    {}
    static void TearDownTestCase(){

    };

public:
    virtual void SetUp();
    virtual void TearDown();
};

void dml_info_config_test::SetUp()
{
    printf("\n======================TEST:START========================\n");
    AW_CHECK_LOG_BEGIN();
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
}

void dml_info_config_test::TearDown()
{
    AW_CHECK_LOG_END();
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
    printf("\n======================TEST:END========================\n");
}

// 未配置性能统计的开关参数，启动服务后，没有表，查视图，预期报错
TEST_F(dml_info_config_test, DFX_029_01_001)
{
    system("sh $TEST_HOME/tools/stop.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
    system("sh checkCfgItem.sh \"enableDmlPerfStat=1\"");  // check
    system("sh delCfgItem.sh \"enableDmlPerfStat=\"");     // 删除配置项

    system("sh $TEST_HOME/tools/start.sh");
    int ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);

    // 查询视图报错
    char const *view_name = "V\\$QRY_DML_INFO ";
    memset(g_command, 0, sizeof(g_command));
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview    -s %s -q %s ", g_toolPath,
        g_connServer, view_name);
    printf(">>> cmd: %s\n", g_command);

    char *result = NULL;
    ret = ExecSystemCmd(&result, NULL, NULL, NULL, g_command);
    const char *strError = "unsucc";
    char *s = strstr(result, strError);
    EXPECT_STRNE(s, NULL);
    (void)free(result);

    // ret = dml_sysview_query(NULL, NULL, NULL, NULL);
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}

// 配置性能统计的开关参数为0，启动服务后，没有表，查视图, 报错
TEST_F(dml_info_config_test, DFX_029_01_002)
{
    system("sh $TEST_HOME/tools/stop.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
    system("sh checkCfgItem.sh \"enableDmlPerfStat=1\"");                // check
    system("sh $TEST_HOME/tools/modifyCfg.sh \"enableDmlPerfStat=0\"");  // 修改配置项

    system("sh $TEST_HOME/tools/start.sh");
    int ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);

    // 查询视图报错
    char *result = NULL;
    char const *view_name = "V\\$QRY_DML_INFO ";
    memset(g_command, 0, sizeof(g_command));
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s ", g_toolPath,
        g_connServer, view_name);
    printf(">>> cmd: %s\n", g_command);
    ret = ExecSystemCmd(&result, NULL, NULL, NULL, g_command);

    const char *strError = "unsucc";
    char *s = strstr(result, strError);
    EXPECT_STRNE(s, NULL);

    (void)free(result);
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}

// 配置性能统计的开关参数为1，启动服务后，没有表，查视图为空
TEST_F(dml_info_config_test, DFX_029_01_003)
{
    system("sh $TEST_HOME/tools/stop.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
    system("sh checkCfgItem.sh \"enableDmlPerfStat=1\"");                // check
    system("sh $TEST_HOME/tools/modifyCfg.sh \"enableDmlPerfStat=1\"");  // 修改配置项

    system("sh $TEST_HOME/tools/start.sh");
    int ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);

    // 查询视图报错
    char *result = NULL;
    char const *view_name = "V\\$QRY_DML_INFO ";
    memset(g_command, 0, sizeof(g_command));
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s ", g_toolPath,
        g_connServer, view_name);
    printf(">>> cmd: %s\n", g_command);
    ret = ExecSystemCmd(&result, NULL, NULL, NULL, g_command);

    const char *strError = "unsucc";
    char *s = strstr(result, strError);
    EXPECT_STREQ(s, NULL);

    (void)free(result);
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}

GmcConnT *conn = NULL;
GmcStmtT *stmt = NULL;

char g_configJson[128] = "{\"max_record_count\" : 999999}";

// 004 未配置性能统计的开关参数，启动服务后，建vertex表，有dml操作，查视图
TEST_F(dml_info_config_test, DFX_029_01_004)
{
    system("sh $TEST_HOME/tools/stop.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
    system("sh checkCfgItem.sh \"enableDmlPerfStat=1\"");  // check
    system("sh delCfgItem.sh \"enableDmlPerfStat\"");      // 删除配置项

    system("sh $TEST_HOME/tools/start.sh");
    int ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // 建表，dml操作
    char labelName[128] = "ip4forward00000";
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // open vertexLabel
    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    printf(">> insert \n");
    uint64_t i = 0;
    for (i = 0; i < 1000; i++) {
        ret = ip4forward00000_set_obj(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(stmt);
        EXPECT_EQ(ret, GMERR_OK);
    }

    // 查询视图报错
    char *result = NULL;
    char const *view_name = "V\\$QRY_DML_INFO ";
    memset(g_command, 0, sizeof(g_command));
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s ", g_toolPath,
        g_connServer, view_name);
    printf(">>> cmd: %s\n", g_command);
    ret = ExecSystemCmd(&result, NULL, NULL, NULL, g_command);

    const char *strError = "unsucc";
    char *s = strstr(result, strError);
    EXPECT_STRNE(s, NULL);
    (void)free(result);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    GmcDetachAllShmSeg();
    testEnvClean();
}

// 005 配置性能统计的开关参数为0，启动服务后，建vertex表，有dml操作，查视图
TEST_F(dml_info_config_test, DFX_029_01_005)
{
    system("sh $TEST_HOME/tools/stop.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
    system("sh checkCfgItem.sh \"enableDmlPerfStat=1\"");                // check
    system("sh $TEST_HOME/tools/modifyCfg.sh \"enableDmlPerfStat=0\"");  // 修改配置项

    system("sh $TEST_HOME/tools/start.sh");
    int ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // 建表，dml操作
    char labelName[128] = "ip4forward00000";
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // open vertexLabel
    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    printf(">> insert \n");
    uint64_t i = 0;
    for (i = 0; i < 1000; i++) {
        ret = ip4forward00000_set_obj(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(stmt);
        EXPECT_EQ(ret, GMERR_OK);
    }

    // 查询视图报错
    char *result = NULL;
    char const *view_name = "V\\$QRY_DML_INFO ";
    memset(g_command, 0, sizeof(g_command));
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s ", g_toolPath,
        g_connServer, view_name);
    printf(">>> cmd: %s\n", g_command);
    ret = ExecSystemCmd(&result, NULL, NULL, NULL, g_command);

    const char *strError = "unsucc";
    char *s = strstr(result, strError);
    EXPECT_STRNE(s, NULL);

    (void)free(result);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}

const char *MS_KVTable_Name_01 = "KVTable_01";
const char *MS_config = "{\"max_record_count\" : 10000}";

// 006  未配置性能统计的开关参数，启动服务后，建kv表，有dml操作，查视图
TEST_F(dml_info_config_test, DFX_029_01_006)
{
    system("sh $TEST_HOME/tools/stop.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
    system("sh checkCfgItem.sh \"enableDmlPerfStat=1\"");  // check
    system("sh delCfgItem.sh \"enableDmlPerfStat\"");      // 删除配置项

    system("sh $TEST_HOME/tools/start.sh");
    int ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // 建kv表，dml操作
    // create KV table
    ret = GmcKvCreateTable(stmt, MS_KVTable_Name_01, MS_config);
    EXPECT_EQ(GMERR_OK, ret);

    void *tableLabel = NULL;
    ret = GmcKvPrepareStmtByLabelName(stmt, MS_KVTable_Name_01);
    EXPECT_EQ(GMERR_OK, ret);

    // insert
    printf(">> insert kv\n");
    uint32_t value = 0;
    char Key_name[10] = {0};
    GmcKvTupleT kvInfo = {0};

    for (uint32_t i = 0; i < 1000; i++) {
        memset(Key_name, 0, 10);
        snprintf(Key_name, sizeof(Key_name), "Key_%d", i);
        value = i;

        memset(&kvInfo, 0, sizeof(GmcKvTupleT));
        kvInfo.key = Key_name;
        kvInfo.keyLen = strlen(Key_name);
        kvInfo.value = &value;
        kvInfo.valueLen = sizeof(uint32_t);

        ret = GmcKvSet(stmt, Key_name, strlen(Key_name), &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 查询视图报错
    char *result = NULL;
    char const *view_name = "V\\$QRY_DML_INFO ";
    memset(g_command, 0, sizeof(g_command));
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s ", g_toolPath,
        g_connServer, view_name);
    printf(">>> cmd: %s\n", g_command);
    ret = ExecSystemCmd(&result, NULL, NULL, NULL, g_command);

    const char *strError = "unsucc";
    char *s = strstr(result, strError);
    EXPECT_STRNE(s, NULL);

    (void)free(result);
    ret = GmcKvDropTable(stmt, MS_KVTable_Name_01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    GmcDetachAllShmSeg();
    testEnvClean();
}

// 007 配置性能统计的开关参数为0，启动服务后，建kv表，有dml操作，查视图
TEST_F(dml_info_config_test, DFX_029_01_007)
{
    system("sh $TEST_HOME/tools/stop.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
    system("sh checkCfgItem.sh \"enableDmlPerfStat=1\"");                // check
    system("sh $TEST_HOME/tools/modifyCfg.sh \"enableDmlPerfStat=0\"");  // 修改配置项

    system("sh $TEST_HOME/tools/start.sh");
    int ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);

    // 建kv表，dml操作
    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // 建kv表，dml操作
    // create KV table
    ret = GmcKvCreateTable(stmt, MS_KVTable_Name_01, MS_config);
    EXPECT_EQ(GMERR_OK, ret);

    void *tableLabel = NULL;
    ret = GmcKvPrepareStmtByLabelName(stmt, MS_KVTable_Name_01);
    EXPECT_EQ(GMERR_OK, ret);

    // insert
    printf(">> insert kv\n");
    uint32_t value = 0;
    char Key_name[10] = {0};
    GmcKvTupleT kvInfo = {0};

    for (uint32_t i = 0; i < 1000; i++) {
        memset(Key_name, 0, 10);
        snprintf(Key_name, sizeof(Key_name), "Key_%d", i);
        value = i;

        memset(&kvInfo, 0, sizeof(GmcKvTupleT));
        kvInfo.key = Key_name;
        kvInfo.keyLen = strlen(Key_name);
        kvInfo.value = &value;
        kvInfo.valueLen = sizeof(uint32_t);

        ret = GmcKvSet(stmt, Key_name, strlen(Key_name), &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 查询视图报错
    char *result = NULL;
    char const *view_name = "V\\$QRY_DML_INFO ";
    memset(g_command, 0, sizeof(g_command));
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s ", g_toolPath,
        g_connServer, view_name);
    printf(">>> cmd: %s\n", g_command);
    ret = ExecSystemCmd(&result, NULL, NULL, NULL, g_command);

    const char *strError = "unsucc";
    char *s = strstr(result, strError);
    EXPECT_STRNE(s, NULL);

    (void)free(result);
    ret = GmcKvDropTable(stmt, MS_KVTable_Name_01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    GmcDetachAllShmSeg();
    testEnvClean();
}


const char *MS_KVTable_Name_02 = "KVTable_02";

// 011  配置性能统计的开关参数为1，启动服务后，建2个verttex， 2个kv，分别drop1个，查视图
TEST_F(dml_info_config_test, DFX_029_01_011)
{
    system("sh $TEST_HOME/tools/stop.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
    system("sh checkCfgItem.sh \"enableDmlPerfStat=1\"");                // check
    system("sh $TEST_HOME/tools/modifyCfg.sh \"enableDmlPerfStat=1\"");  // 修改配置项

    system("sh $TEST_HOME/tools/start.sh");
    int ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // 建2个verttex， 2个kv，分别drop1个，查视图
    // create KV table
    ret = GmcKvCreateTable(stmt, MS_KVTable_Name_01, MS_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt, MS_KVTable_Name_02, MS_config);
    EXPECT_EQ(GMERR_OK, ret);

    void *tableLabel = NULL;
    ret = GmcKvPrepareStmtByLabelName(stmt, MS_KVTable_Name_01);
    EXPECT_EQ(GMERR_OK, ret);

    // insert
    printf(">> insert kv\n");
    uint32_t value = 0;
    char Key_name[10] = {0};
    GmcKvTupleT kvInfo = {0};
    for (uint32_t i = 0; i < 1000; i++) {
        memset(Key_name, 0, 10);
        snprintf(Key_name, sizeof(Key_name), "Key_%d", i);
        value = i;

        memset(&kvInfo, 0, sizeof(GmcKvTupleT));
        kvInfo.key = Key_name;
        kvInfo.keyLen = strlen(Key_name);
        kvInfo.value = &value;
        kvInfo.valueLen = sizeof(uint32_t);

        ret = GmcKvSet(stmt, Key_name, strlen(Key_name), &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }

    // ret = GmcCloseKvTable(stmt);
    // EXPECT_EQ(GMERR_OK, ret);

    tableLabel = NULL;
    ret = GmcKvPrepareStmtByLabelName(stmt, MS_KVTable_Name_02);
    EXPECT_EQ(GMERR_OK, ret);

    // insert
    printf(">> insert kv\n");
    value = 0;
    memset(Key_name, 0, sizeof(Key_name));
    memset(&kvInfo, 0, sizeof(GmcKvTupleT));
    for (uint32_t i = 0; i < 1000; i++) {
        memset(Key_name, 0, 10);
        snprintf(Key_name, sizeof(Key_name), "Key_%d", i);
        value = i;

        memset(&kvInfo, 0, sizeof(GmcKvTupleT));
        kvInfo.key = Key_name;
        kvInfo.keyLen = strlen(Key_name);
        kvInfo.value = &value;
        kvInfo.valueLen = sizeof(uint32_t);

        ret = GmcKvSet(stmt, Key_name, strlen(Key_name), &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }

    // ret = GmcCloseKvTable(stmt);
    // EXPECT_EQ(GMERR_OK, ret);

    //创建 vertexLabel_1
    char labelName[128] = "ip4forward00000";
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    uint64_t count;

    // check dml info all
    ret = dml_sysview_query(NULL, MS_KVTable_Name_01, NULL, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info(0, 1000, 0, 0, 0, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_sysview_query(NULL, MS_KVTable_Name_02, NULL, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info(0, 1000, 0, 0, 0, 0);
    EXPECT_EQ(ret, 0);

    // open vertexLabel
    void *vertexLabel = NULL;
    printf(">> insert \n");
    uint64_t i = 0;
    for (i = 0; i < 1000; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);

        ret = ip4forward00000_set_obj(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(stmt);
        EXPECT_EQ(ret, GMERR_OK);
    }

    // close vertexLabel
    ASSERT_EQ(GMERR_OK, ret);

    //创建 vertexLabel_2   Tree_Vector.gmjson
    schema_json = NULL;
    readJanssonFile("./schema_file/Tree_Vector.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // open vertexLabel
    vertexLabel = NULL;
    char labelNameTree[128] = "Tree_Vector";

    char f14_value[8] = "string";
    int array_num = 1;
    int vector_num = 1;
    printf(">> insert sync\n");

    for (i = 0; i < 100; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelNameTree, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);

        Tree_Vector_set_obj(stmt, i, false, f14_value, array_num, vector_num);

        ret = GmcExecute(stmt);
        EXPECT_EQ(ret, GMERR_OK);
    }

    ret = GmcGetVertexCount(stmt, labelNameTree, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    // check dml info all
    ret = dml_sysview_query(MS_KVTable_Name_01, MS_KVTable_Name_01, NULL, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info(0, 1000, 0, 0, 0, 0);
    EXPECT_EQ(ret, 0);
    // check dml info all
    ret = dml_sysview_query(MS_KVTable_Name_02, MS_KVTable_Name_02, NULL, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info(0, 1000, 0, 0, 0, 0);
    EXPECT_EQ(ret, 0);
    // check dml info all
    ret = dml_sysview_query(labelName, labelName, NULL, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info(0, 1000, 0, 0, 0, 0);
    EXPECT_EQ(ret, 0);
    // check dml info all
    ret = dml_sysview_query(labelNameTree, labelNameTree, NULL, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info(0, 100, 0, 0, 0, 0);
    EXPECT_EQ(ret, 0);

    ret = GmcDropVertexLabel(stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, (char *)MS_KVTable_Name_01);
    EXPECT_EQ(GMERR_OK, ret);

    // check dml info all
    ret = dml_sysview_query(MS_KVTable_Name_01, MS_KVTable_Name_01, NULL, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info_null(0);
    EXPECT_EQ(ret, 0);
    // check dml info all
    ret = dml_sysview_query(MS_KVTable_Name_02, MS_KVTable_Name_02, NULL, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info(0, 1000, 0, 0, 0, 0);
    EXPECT_EQ(ret, 0);
    // check dml info all
    ret = dml_sysview_query(labelName, labelName, NULL, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info_null(0);
    EXPECT_EQ(ret, 0);
    // check dml info all
    ret = dml_sysview_query(labelNameTree, labelNameTree, NULL, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info(0, 100, 0, 0, 0, 0);
    EXPECT_EQ(ret, 0);
    ret = GmcDropVertexLabel(stmt, labelNameTree);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, (char *)MS_KVTable_Name_02);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}
