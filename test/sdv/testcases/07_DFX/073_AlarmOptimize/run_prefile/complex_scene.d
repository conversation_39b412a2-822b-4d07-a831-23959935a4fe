// 1.not join
namespace ns1 {
%table A(a: int4, b: int4)
%table B(a: int4, b: int4)
%table C(a: int4, b: int4)

B(a, b) :- A(a, b), NOT C(a, b).
}

// 2.aggregate
namespace ns2 {
%table A(a: int4, b: int4)
%table B(a: int4, b: int4)
%aggregate funcA(a: int4 -> b: int4)

B(a, c) :- A(a, b) GROUP-BY(a) funcA(b, c).
null(0) :- B(a, c) .
}

// 3.transient field
namespace ns3 {
%table A(a:int4 , b:int4 , t:int4)
%table B(a:int4 , b:int4)
%table C(a:int4 , b:int4){transient(field(b))}

C(a,0) :- B(a,-).
C(a,1) :- B(a,-).
A(a,b,t) :- B(a,b) , C(a,t).
}

// 4.resource
namespace ns4 {
%table A(a: int4, b: int4)
%table B(a: int4, b: int4)
%resource rsc0(a: int4 -> b: int4) { sequential(max_size(10)) }

rsc0(a, -) :- A(a, -).
B(a, b) :- rsc0(a, b).
}

// 5.aggregate out table from other rule
namespace ns5 {
%table A(a: int4, b: int4)
%table B(a: int4, b: int4)
%table C(a: int4, b: int4)
%aggregate funcA(a: int4 -> b: int4)

C(a, c) :- A(a, b) GROUP-BY(a) funcA(b, c).
null(0) :- C(a, c) .
C(a, c) :- B(a, c).
}

// 6.resource in right with ignore
namespace ns6 {
%table A(a: int4, b: int4)
%table B(a: int4, b: int4)
%table C(a: int4, b: int4)
%table D(a: int4, b: int4)
%resource rsc0(a: int4 -> b: int4) { sequential(max_size(10)) }

rsc0(a, -) :- C(a, b), D(a, b).
B(a, b) :- A(a, b), rsc0(a, -).
}
