[{"type": "record", "name": "max_memory_check", "fields": [{"name": "F0", "type": "int32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}, {"name": "F11", "type": "uint32", "nullable": true}, {"name": "F12", "type": "uint32", "nullable": true}, {"name": "F13", "type": "uint32", "nullable": true}, {"name": "F14", "type": "uint32", "nullable": true}, {"name": "F15", "type": "uint32", "nullable": true}, {"name": "F16", "type": "uint32", "nullable": true}, {"name": "F17", "type": "uint32", "nullable": true}, {"name": "F18", "type": "uint32", "nullable": true}, {"name": "F19", "type": "uint32", "nullable": true}, {"name": "F20", "type": "uint32", "nullable": true}, {"name": "F21", "type": "uint32", "nullable": true}, {"name": "F22", "type": "uint32", "nullable": true}, {"name": "F23", "type": "uint32", "nullable": true}, {"name": "F24", "type": "uint32", "nullable": true}, {"name": "F25", "type": "uint32", "nullable": true}, {"name": "F26", "type": "uint32", "nullable": true}, {"name": "F27", "type": "uint32", "nullable": true}, {"name": "F28", "type": "uint32", "nullable": true}, {"name": "F29", "type": "uint32", "nullable": true}, {"name": "F30", "type": "uint32", "nullable": true}, {"name": "F31", "type": "uint32", "nullable": true}, {"name": "F32", "type": "uint32", "nullable": true}, {"name": "F33", "type": "uint32", "nullable": true}], "keys": [{"node": "max_memory_check", "name": "PK", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]