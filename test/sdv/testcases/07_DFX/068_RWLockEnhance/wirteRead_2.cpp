/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

#include "./op.h"

#define TREAD_NUM 50
void *fun01(void *args)
{
    char labelName[] = "simpleLabel";
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    while (1) {
        for (int i = 0; i < 1000; i++) {
            ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            EXPECT_EQ(GMERR_OK, ret);
            bool isFinish = true;
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);

            bool isNull;
            uint64_t f1Val;
            ret = GmcGetVertexPropertyByName(stmt, "F1", &f1Val, sizeof(f1Val), &isNull);
        }
    }
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}
#if !defined(RUN_DATACOM_HPE)
int main(void)
{
    int ret;
    pthread_t proThr[TREAD_NUM];
    int index[TREAD_NUM] = {0};
    ret = TestTryRegisterSignal(TestCrashHandler);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < TREAD_NUM; i++) {
        index[i] = i;
        ret = pthread_create(&proThr[i], NULL, fun01, (void *)&index[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < TREAD_NUM; i++) {
        pthread_join(proThr[i], NULL);
    }
    return 0;
}
#endif
