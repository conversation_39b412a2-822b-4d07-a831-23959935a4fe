[{"type": "record", "name": "DML_011_scanVertexTest_001", "fields": [{"name": "F0", "type": "int32"}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "int32"}, {"name": "F3", "type": "int32"}], "super_fields": [{"name": "superfield0", "comment": "test", "fields": ["F0", "F1", "F2", "F3"]}], "keys": [{"node": "DML_011_scanVertexTest_001", "name": "DML_011_scanVertexTest_K0", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]