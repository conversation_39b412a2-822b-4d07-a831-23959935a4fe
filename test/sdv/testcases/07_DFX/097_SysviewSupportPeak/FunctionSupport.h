/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: FunctionSupport.h
 * Description: FunctionSupport.h
 * Author: qibingsen 00880292
 * Create: 2024-09-29
 */

#ifndef __FUNCTIONSUPPORT_H__
#define __FUNCTIONSUPPORT_H__

#include <errno.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <unistd.h>
#include <stdarg.h>
#include <regex.h>
#include <signal.h>
#include <pthread.h>
#include <sys/sem.h>
#include <sys/wait.h>
#include <sys/time.h>
#include <dirent.h>
#include <semaphore.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"

#define MAX_CMD_SIZE 1024
#define MAX_NAME_LENGTH 512
#define MAX_FILE_PATH 512
#define MULTI_THREAD_NUM 128
#define MB_SIZE 1048576
#define KB_SIZE 1024
#define BYTE 1
#define MAX_LENGH_VALUE 1024
#define LOG_MAX_SIZE_OF_ERROR_MSG 128
#define CONN_TIMES 10
#define MAX_CONN MAX_CONN_SIZE
#define LONG_SCHEMA_LENGTH 524288
#define MAX_LOOP_NUM 100
#define LOGCHECKFAILED (-1)
#define FAILED (-1)

GmcConnT *g_conn = NULL, *g_connSub = NULL;
GmcStmtT *g_stmt = NULL;
char g_configJson[MAX_NAME_LENGTH] = "{\"max_record_count\" : 30000}";
char g_namespace[MAX_NAMESPACE_LENGTH] = "public";
char g_shmemview[MAX_NAME_LENGTH] = "COM_SHMEM_CTX";
char g_dynview[MAX_NAME_LENGTH] = "COM_DYN_CTX";
char g_shmemviewDevMgrMemctx[MAX_NAME_LENGTH] = "devMgrMemctx";
char g_shmemviewCompressorMemCtx[MAX_NAME_LENGTH] = "CompressorMemCtx";
char g_shmemviewCatalogShareMemoryContext[MAX_NAME_LENGTH] = "'catalog share memory context'";
char g_shmemviewCatalogReusableShareMemoryContext[MAX_NAME_LENGTH] = "'catalog reusable share memory context'";
char g_dynviewCatalogDynamicMemoryContext[MAX_NAME_LENGTH] = "'catalog dynamic memory context'";
char g_dynviewCatalogLabelDynamicMemoryContext[MAX_NAME_LENGTH] = "'catalog label dynamic memory context'";

typedef int (*FuncWrite)(GmcStmtT *stmt, int32_t v);

typedef struct TaglabelCfg {
    GmcOperationTypeE opType;
    char *labelName;
    int32_t startVal;  // 主键或其他非成员索引的起始值
    int32_t count;     // 主键或其他非成员索引的数量
    bool isBatch;
    bool isStruct;
    char *lnamespace;
} GtlabelCfgT;

typedef struct CheckNumofPeakCfg {
    bool isFinished;
    bool isSuccess;
    int32_t checkNumofPeakMemory;
}CheckNumofPeakCfgT;

int CheckFile(const char *generateTxtFile, const char *originTxt)
{
    char diffCmd[MAX_CMD_SIZE];
    (void)snprintf(diffCmd, MAX_CMD_SIZE, "diff -b -B -a %s %s", generateTxtFile, originTxt);
    char catCmd[MAX_CMD_SIZE] = { 0 };
    (void)snprintf(catCmd, MAX_CMD_SIZE, "cat %s", generateTxtFile);
    if (access(originTxt, F_OK) != -1 && access(generateTxtFile, F_OK) != -1) {
        int ret = executeCommand(diffCmd, "");
        if (ret != -1) {
            system(diffCmd);
            return -1;
        }
    } else {
        system(catCmd);
        return -1;
    }
    return 0;
}

int CheckFileExist(const char *fileName)
{
    FILE *file = fopen(fileName, "r");
    if (file == NULL) {
        return -1;
    }
    (void)fclose(file);
    return 0;
}
void RmTxt(const char *txtFile)
{
    char rmCmd[MAX_CMD_SIZE] = { 0 };
    (void)snprintf(rmCmd, MAX_CMD_SIZE, "rm %s", txtFile);
    system(rmCmd);
}

int CheckNumofPeakMemory(char *memoryContext)
{
    char ctxnamecmd[MAX_CMD_SIZE] = { 0 };
    char peakctxcmd[MAX_CMD_SIZE] = { 0 };
    int32_t ctxnamenum = 0, peakctxnum = 0;
    (void)snprintf(ctxnamecmd, MAX_CMD_SIZE, "gmsysview -q V\\$%s | grep -c ' CTX_NAME:'", memoryContext);
    (void)snprintf(peakctxcmd, MAX_CMD_SIZE, "gmsysview -q V\\$%s | grep -c ' PEAK_ALLOC_SIZE:'", memoryContext);
    int ret = TestGetResultCommand(ctxnamecmd, &ctxnamenum);
    if (ret) {
        return FAILED;
    }
    ret = TestGetResultCommand(peakctxcmd, &peakctxnum);
    if (ret) {
        return FAILED;
    }
    if (peakctxnum != ctxnamenum) {
        return FAILED;
    }
    return 0;
}

int CheckNumofPeakConn()
{
    char ctxnamecmd[MAX_CMD_SIZE] = { 0 };
    int32_t ctxnamenum = 0, peakctxnum = 0;
    (void)snprintf(ctxnamecmd, MAX_CMD_SIZE, "gmsysview -q V\\$DRT_COM_STAT | grep -c ' PEAK_CONN_NUM:'");
    int ret = TestGetResultCommand(ctxnamecmd, &ctxnamenum);
    if (ret) {
        return FAILED;
    }
    return 0;
}

int GetSizeofPeakDynMemory(int *value, char *ctxName)
{
    char ctxMBcmd[MAX_CMD_SIZE] = { 0 };
    char ctxKBcmd[MAX_CMD_SIZE] = { 0 };
    char ctxBytecmd[MAX_CMD_SIZE] = { 0 };
    (void)snprintf(ctxMBcmd, MAX_CMD_SIZE,
                   "gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=%s | awk -F'\\\\[|\\\\]' '/PEAK_ALLOC_SIZE/ {print $2}'",
                   ctxName);
    (void)snprintf(ctxKBcmd, MAX_CMD_SIZE,
                   "gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=%s | awk -F'\\\\[|\\\\]' '/PEAK_ALLOC_SIZE/ {print $4}'",
                   ctxName);
    (void)snprintf(ctxBytecmd, MAX_CMD_SIZE,
                   "gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=%s | awk -F'\\\\[|\\\\]' '/PEAK_ALLOC_SIZE/ {print $6}'",
                   ctxName);
    int peakMb = 0;
    int peakKb = 0;
    int peakByte = 0;
    int ret = TestGetResultCommand(ctxMBcmd, &peakMb);
    if (ret) {
        return FAILED;
    }
    ret = TestGetResultCommand(ctxKBcmd, &peakKb);
    if (ret) {
        return FAILED;
    }
    ret = TestGetResultCommand(ctxBytecmd, &peakByte);
    if (ret) {
        return FAILED;
    }
    *value = peakMb * MB_SIZE + peakKb * KB_SIZE + peakByte;
    return 0;
}

int GetSizeofPeakDynMemoryinfile(int *value)
{
    char ctxMBcmd[MAX_CMD_SIZE] = { 0 };
    char ctxKBcmd[MAX_CMD_SIZE] = { 0 };
    char ctxBytecmd[MAX_CMD_SIZE] = { 0 };
    (void)snprintf(ctxMBcmd, MAX_CMD_SIZE,
                   "cat /root/_datalog_/TbmRunLog.txt | awk -F'\\\\[|\\\\]' '/PEAK_ALLOC_SIZE/ {print $2}'");
    (void)snprintf(ctxKBcmd, MAX_CMD_SIZE,
                   "cat /root/_datalog_/TbmRunLog.txt | awk -F'\\\\[|\\\\]' '/PEAK_ALLOC_SIZE/ {print $4}'");
    (void)snprintf(ctxBytecmd, MAX_CMD_SIZE,
                   "cat /root/_datalog_/TbmRunLog.txt | awk -F'\\\\[|\\\\]' '/PEAK_ALLOC_SIZE/ {print $6}'");
    int peakMb = 0;
    int peakKb = 0;
    int peakByte = 0;
    int ret = TestGetResultCommand(ctxMBcmd, &peakMb);
    if (ret) {
        return FAILED;
    }
    ret = TestGetResultCommand(ctxKBcmd, &peakKb);
    if (ret) {
        return FAILED;
    }
    ret = TestGetResultCommand(ctxBytecmd, &peakByte);
    if (ret) {
        return FAILED;
    }
    *value = peakMb * MB_SIZE + peakKb * KB_SIZE + peakByte;

    return 0;
}

int GetSizeofPeakShareMemory(int *value, char *ctxName)
{
    char ctxMBcmd[MAX_CMD_SIZE] = { 0 };
    char ctxKBcmd[MAX_CMD_SIZE] = { 0 };
    char ctxBytecmd[MAX_CMD_SIZE] = { 0 };
    (void)snprintf(ctxMBcmd, MAX_CMD_SIZE,
                   "gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=%s | awk -F'\\\\[|\\\\]' '/PEAK_ALLOC_SIZE/ {print $2}'",
                   ctxName);
    (void)snprintf(ctxKBcmd, MAX_CMD_SIZE,
                   "gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=%s | awk -F'\\\\[|\\\\]' '/PEAK_ALLOC_SIZE/ {print $4}'",
                   ctxName);
    (void)snprintf(ctxBytecmd, MAX_CMD_SIZE,
                   "gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=%s | awk -F'\\\\[|\\\\]' '/PEAK_ALLOC_SIZE/ {print $6}'",
                   ctxName);
    int peakMb = 0;
    int peakKb = 0;
    int peakByte = 0;
    int ret = TestGetResultCommand(ctxMBcmd, &peakMb);
    if (ret) {
        return FAILED;
    }
    ret = TestGetResultCommand(ctxKBcmd, &peakKb);
    if (ret) {
        return FAILED;
    }
    ret = TestGetResultCommand(ctxBytecmd, &peakByte);
    if (ret) {
        return FAILED;
    }
    *value = peakMb * MB_SIZE + peakKb * KB_SIZE + peakByte;
    return 0;
}


void *CheckPeakThread(void *args)
{
    int ret = 0;
    CheckNumofPeakCfgT *cfg = (CheckNumofPeakCfgT *)args;
    int32_t catalogmemctx = 0;
    int32_t cnt = 0;
    char  *memoryContext = "dtl_ext_func_funcmemalloc";
    while (!cfg->isFinished && cnt < 1000) {
        usleep(50000);
        ret = GetSizeofPeakDynMemory(&catalogmemctx, memoryContext);
        if (cfg->checkNumofPeakMemory == catalogmemctx) {
            cfg->isSuccess = true;
            cfg->isFinished = true;
            break;
        }
        cnt++;
    }
    return NULL;
}

int GetSizeofPeakShareMemoryofMB(int *value, char *ctxName)
{
    char ctxMBcmd[MAX_CMD_SIZE] = { 0 };
    (void)snprintf(ctxMBcmd, MAX_CMD_SIZE,
                   "gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=%s | awk -F'\\\\[|\\\\]' '/PEAK_ALLOC_SIZE/ {print $2}'",
                   ctxName);
    int peakMb = 0;
    int ret = TestGetResultCommand(ctxMBcmd, &peakMb);
    if (ret) {
        return FAILED;
    }
    *value = peakMb + 1;
    return 0;
}

int GetNumofPeakConn(int *value)
{
    char conncmd[MAX_CMD_SIZE] = { 0 };
    (void)snprintf(conncmd, sizeof(conncmd),
                   "%s/gmsysview -s %s -q V\\$DRT_COM_STAT | grep PEAK_CONN_NUM | awk '{print $2}'", g_toolPath,
                   g_connServer);
    int ret = TestGetResultCommand(conncmd, value);
    if (ret) {
        return FAILED;
    }
    return 0;
}

int BatchPrepare(GmcConnT *conn, GmcBatchT **batch)
{
    GmcBatchOptionT batchOption;
    int ret = GmcBatchOptionInit(&batchOption);
    RETURN_IFERR(ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    RETURN_IFERR(ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    RETURN_IFERR(ret);
    ret = GmcBatchPrepare(conn, &batchOption, batch);
    RETURN_IFERR(ret);
    return ret;
}

int WriteTable(GmcConnT *conn, GmcStmtT *stmt, GtlabelCfgT vertexCfg, FuncWrite func)
{
    char *labelName = vertexCfg.labelName;
    GmcOperationTypeE opType = vertexCfg.opType;
    int32_t startPkVal = vertexCfg.startVal;
    int32_t vertexCount = vertexCfg.count;
    bool isBatch = vertexCfg.isBatch;
    bool isStruct = vertexCfg.isStruct;
    char *lnamespace = vertexCfg.lnamespace;

    int ret = testGmcPrepareStmtByLabelName(stmt, labelName, opType);
    RETURN_IFERR(ret);

    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    if (isBatch) {
        ret = BatchPrepare(conn, &batch);
        RETURN_IFERR(ret);
        if (isStruct) {
            ret = GmcBatchBindStmt(batch, stmt);
            RETURN_IFERR(ret);
        }
    }

    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = func(stmt, i);
        RETURN_IFERR(ret);

        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            RETURN_IFERR(ret);
        } else {
            ret = GmcExecute(stmt);
            RETURN_IFERR(ret);
        }
    }
    if (isBatch) {
        ret = GmcBatchExecute(batch, &batchRet);
        RETURN_IFERR(ret);
        uint32_t totalNum, successNum;
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        RETURN_IFERR(ret);
        RETURN_IFERR((int32_t)totalNum != vertexCount);
        RETURN_IFERR((int32_t)successNum != vertexCount);
        if (isStruct) {
            GmcBatchUnbindStmt(batch, stmt);
        }
        GmcBatchDestroy(batch);
    }
    return ret;
}

int SetbatchvertexValue(GmcStmtT *stmt, int32_t v)
{
    int32_t f1 = v;
    uint8_t bytes[MAX_LENGH_VALUE] = { 0 };
    int ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &f1, sizeof(int32_t));
    RETURN_IFERR(ret);
    ret = memset_s(bytes, MAX_LENGH_VALUE, 0xff, MAX_LENGH_VALUE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char vertexProperty[MAX_NAME_LENGTH] = { 0 };
    for (int i = 2; i <= 128; i++) {
        (void)snprintf(vertexProperty, MAX_NAME_LENGTH, "F%d", i);
        ret = GmcSetVertexProperty(stmt, vertexProperty, GMC_DATATYPE_BYTES, &bytes, sizeof(bytes));
        RETURN_IFERR(ret);
    }
    return ret;
}

int SetnormalvertexValue(GmcStmtT *stmt, int32_t v)
{
    int32_t f1 = v;
    uint8_t bytes[MAX_LENGH_VALUE] = { 0 };
    int ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &f1, sizeof(int32_t));
    RETURN_IFERR(ret);
    ret = memset_s(bytes, MAX_LENGH_VALUE, 0xff, MAX_LENGH_VALUE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char vertexProperty[MAX_NAME_LENGTH] = { 0 };
    for (int i = 2; i <= 64; i++) {
        (void)snprintf(vertexProperty, MAX_NAME_LENGTH, "F%d", i);
        ret = GmcSetVertexProperty(stmt, vertexProperty, GMC_DATATYPE_BYTES, &bytes, sizeof(bytes));
        RETURN_IFERR(ret);
    }
    return ret;
}

int SetalloctableValue_alloc(GmcStmtT *stmt, int32_t v)
{
    int64_t a = v;
    int64_t b = v;
    int64_t c = 1;
    int32_t dtlReservedCount = 1;

    int ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT64, &a, sizeof(a));
    RETURN_IFERR(ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &b, sizeof(b));
    RETURN_IFERR(ret);
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT64, &c, sizeof(c));
    RETURN_IFERR(ret);
    ret =
        GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &dtlReservedCount, sizeof(dtlReservedCount));
    RETURN_IFERR(ret);
    return ret;
}

int SetalloctableValue_free(GmcStmtT *stmt, int32_t v)
{
    int64_t a = v;
    int64_t b = v;
    int64_t c = 0;
    int32_t dtlReservedCount = 1;

    int ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT64, &a, sizeof(a));
    RETURN_IFERR(ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &b, sizeof(b));
    RETURN_IFERR(ret);
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT64, &c, sizeof(c));
    RETURN_IFERR(ret);
    ret =
        GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &dtlReservedCount, sizeof(dtlReservedCount));
    RETURN_IFERR(ret);
    return ret;
}

#endif
