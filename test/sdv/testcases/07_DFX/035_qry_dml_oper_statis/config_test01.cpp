extern "C" {
}
#include <stdarg.h>
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "dml_oper_statis_tool.h"
#include "t_datacom_lite.h"

int ret = 0;
GmcConnT *g_conn = NULL;  //  g_conn
GmcStmtT *g_stmt = NULL;  // g_stmt

const char *g_vertexLabel_Name = "ip4forward";
char g_label_config[] = "{\"max_record_count\":1000}";

using namespace std;

class dml_oper_statis_config_test : public testing::Test {
protected:
    static void SetUpTestCase(){}
    static void TearDownTestCase(){};

public:
    virtual void SetUp();
    virtual void TearDown();
};

void dml_oper_statis_config_test::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    memset(g_dml_info, 0, sizeof(dml_info_t) * 3);
}

void dml_oper_statis_config_test::TearDown()
{
    AW_CHECK_LOG_END();
    system("sh modifyCfg.sh \"recover\"");
}

char g_configJson[128] = "{\"max_record_count\" : 999999}";

// 未配置dml统计的配置项，启动服务后，没有表，dml统计接口查询，以及视图查询
TEST_F(dml_oper_statis_config_test, dml_oper_statis_config_test_001)
{
    system("sh $TEST_HOME/tools/start.sh");
    system("gmadmin -cfgName compatibleV3 -cfgVal 0");
    system("gmadmin -cfgName enableDmlOperStat -cfgVal 0");
    // system("ipcs");
    int ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);

    // 接口查询报错；
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // // 建表，dml操作
    char labelName[128] = "ip4forward00000";
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    GmcDropVertexLabel(g_stmt, labelName);
    ret = GmcCreateVertexLabel(g_stmt, schema_json, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema_json);

    ret = dml_statis_check(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    // check dml statis view
    ret = dml_sysview_query(NULL, labelName, NULL, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info(0);
    EXPECT_EQ(ret, 0);

    // check dml statis view
    ret = dml_sysview_query(labelName, labelName, NULL, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info(0);
    EXPECT_EQ(ret, 0);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("gmadmin -cfgName compatibleV3 -cfgVal 1");
    system("gmadmin -cfgName enableDmlOperStat -cfgVal 1");
}

// 配置dml统计的配置项为0，启动服务后，没有表，接口查询，以及dml统计视图查询
TEST_F(dml_oper_statis_config_test, dml_oper_statis_config_test_002)
{
    system("sh $TEST_HOME/tools/start.sh");
    system("gmadmin -cfgName compatibleV3 -cfgVal 0");
    system("gmadmin -cfgName enableDmlOperStat -cfgVal 1");

    int ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);

    // 接口查询正常
    ret = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // 建表，dml操作
    char labelName[128] = "ip4forward00000";
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    GmcDropVertexLabel(g_stmt, labelName);
    ret = GmcCreateVertexLabel(g_stmt, schema_json, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema_json);

    ret = dml_statis_check(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    // check dml statis view
    ret = dml_sysview_query(NULL, labelName, NULL, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info(0);
    EXPECT_EQ(ret, 0);

    // check dml statis view
    ret = dml_sysview_query(labelName, labelName, NULL, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info(0);
    EXPECT_EQ(ret, 0);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();

    system("gmadmin -cfgName compatibleV3 -cfgVal 1");
    system("gmadmin -cfgName enableDmlOperStat -cfgVal 1");
}

// 配置dml统计的配置项为1，启动服务后，没有表，dml统计接口查询，以及视图查询
TEST_F(dml_oper_statis_config_test, dml_oper_statis_config_test_003)
{
    system("sh $TEST_HOME/tools/start.sh");
    system("gmadmin -cfgName compatibleV3 -cfgVal 0");
    system("gmadmin -cfgName enableDmlOperStat -cfgVal 1");
    int ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);

    // 接口查询报错；
    ret = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    char labelName[128] = "ip4forward00000";
    GmcDropVertexLabel(g_stmt, labelName);
    ret = dml_statis_check(g_stmt, labelName, NULL, GMERR_UNDEFINED_TABLE);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);

    ret = dml_statis_check(g_stmt, "T_GMDB", labelName, GMERR_UNDEFINED_TABLE);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);

    // check dml statis view
    ret = dml_sysview_query(NULL, labelName, NULL, NULL);
    EXPECT_NE(ret, 0);
    ret = check_dml_info_null(0);
    EXPECT_EQ(ret, 0);

    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();

    system("gmadmin -cfgName compatibleV3 -cfgVal 1");
    system("gmadmin -cfgName enableDmlOperStat -cfgVal 1");
}

// 未配置dml统计的配置项，启动服务后，建vertex表，有dml操作，dml统计接口查询，以及视图查询
// 0812改为默认打开
TEST_F(dml_oper_statis_config_test, dml_oper_statis_config_test_004)
{
    system("sh $TEST_HOME/tools/stop.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
    system("sh checkCfgItem.sh \"enableDmlOperStat=1\"");  // check
    system("sh delCfgItem.sh \"enableDmlOperStat\"");      // 删除配置项
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"compatibleV3=0\"");

    system("sh $TEST_HOME/tools/start.sh");
    system("sh delCfgItem.sh \"recover\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
    // system("ipcs");
    int ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // 建表，dml操作
    char labelName[128] = "ip4forward00000";
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    GmcDropVertexLabel(g_stmt, labelName);
    ret = GmcCreateVertexLabel(g_stmt, schema_json, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema_json);

    // open vertexLabel
    void *vertexLabel = NULL;

    printf(">> insert \n");

    uint64_t i = 0;
    for (i = 0; i < 1000; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = ip4forward00000_set_obj(g_stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    set_count_value(1000);
    ret = dml_statis_check(g_stmt, labelName, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = dml_statis_check(g_stmt, labelName, "T_GMDB");
    EXPECT_EQ(GMERR_OK, ret);

    system("gmsysview -q V\\$QRY_DML_OPER_STATIS");

    // check dml statis view
    ret = dml_sysview_query(NULL, labelName, NULL, NULL);
    EXPECT_EQ(ret, 0);
#ifdef DIRECT_WRITE
    set_count_value(0);
#endif
    ret = check_dml_info(0, 1000);
    EXPECT_EQ(ret, 0);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}

// 005 配置dml统计的配置项为0，启动服务后，建vertex表，有dml操作，dml统计接口查询，以及视图查询
TEST_F(dml_oper_statis_config_test, dml_oper_statis_config_test_005)
{
    system("sh $TEST_HOME/tools/start.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"enableDmlOperStat=0\" \"compatibleV3=0\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");

    // system("ipcs");
    int ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    clear_count();
    ret = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // 建表，dml操作
    char labelName[128] = "ip4forward00000";
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    GmcDropVertexLabel(g_stmt, labelName);
    ret = GmcCreateVertexLabel(g_stmt, schema_json, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema_json);

    // open vertexLabel
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    printf(">> insert \n");
    uint64_t i = 0;
    for (i = 0; i < 1000; i++) {
        ret = ip4forward00000_set_obj(g_stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(g_stmt);
        EXPECT_EQ(ret, GMERR_OK);
    }

    uint64_t count;
    ret = GmcGetVertexCount(g_stmt, labelName, NULL, &count);
    EXPECT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);
    ret = dml_statis_check(g_stmt, labelName, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = dml_statis_check(g_stmt, "T_GMDB", labelName);
    EXPECT_EQ(GMERR_OK, ret);

    // check dml statis view
    ret = dml_sysview_query(NULL, labelName, NULL, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info(0);
    EXPECT_EQ(ret, 0);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();

    system("gmadmin -cfgName compatibleV3 -cfgVal 1");
    system("gmadmin -cfgName enableDmlOperStat -cfgVal 1");
}

const char *MS_KVTable_Name_01 = "KVTable_07_035";
const char *MS_config = "{\"max_record_count\" : 10000}";

// 006  未配置dml统计的配置项，启动服务后，建kv表，有dml操作，dml统计接口查询，以及视图查询
TEST_F(dml_oper_statis_config_test, dml_oper_statis_config_test_006)
{
    system("sh $TEST_HOME/tools/stop.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
    system("sh checkCfgItem.sh \"enableDmlOperStat=1\"");  // check
    system("sh delCfgItem.sh \"enableDmlOperStat\"");      // 删除配置项
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"compatibleV3=0\"");
    system("sh $TEST_HOME/tools/start.sh");
    system("sh delCfgItem.sh \"recover\"");  
    // system("ipcs");
    int ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // 建kv表，dml操作
    // create KV table
    ret = GmcKvCreateTable(g_stmt, MS_KVTable_Name_01, MS_config);
    EXPECT_EQ(GMERR_OK, ret);

    void *tableLabel = NULL;
    ret = GmcKvPrepareStmtByLabelName(g_stmt, MS_KVTable_Name_01);
    EXPECT_EQ(GMERR_OK, ret);

    // insert
    printf(">> insert kv\n");
    uint32_t value = 0;
    char Key_name[10] = {0};
    GmcKvTupleT kvInfo = {0};

    for (uint32_t i = 0; i < 1000; i++) {
        memset(Key_name, 0, 10);
        snprintf(Key_name, sizeof(Key_name), "Key_%d", i);
        value = i;

        memset(&kvInfo, 0, sizeof(GmcKvTupleT));
        kvInfo.key = Key_name;
        kvInfo.keyLen = strlen(Key_name);
        kvInfo.value = &value;
        kvInfo.valueLen = sizeof(uint32_t);

        ret = GmcKvSet(g_stmt, Key_name, strlen(Key_name), &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }

    set_count_value(1000);
    ret = dml_statis_check_kv(g_stmt, MS_KVTable_Name_01, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = dml_statis_check_kv(g_stmt, MS_KVTable_Name_01, "T_GMDB");
    EXPECT_EQ(GMERR_OK, ret);

    set_total_value(1000);
    ret = dml_statis_check_total(g_stmt, MS_KVTable_Name_01, "T_GMDB");
    EXPECT_EQ(GMERR_OK, ret);

    // check dml statis view
    ret = dml_sysview_query(NULL, MS_KVTable_Name_01, NULL, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info(0);
    EXPECT_EQ(ret, 0);

    ret = GmcKvDropTable(g_stmt, MS_KVTable_Name_01);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh checkCfgItem.sh \"enableDmlOperStat=1\"");
}

// 007 配置dml统计的开关参数为0，启动服务后，建kv表，有dml操作，查视图
TEST_F(dml_oper_statis_config_test, dml_oper_statis_config_test_007)
{
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("sh $TEST_HOME/tools/start.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"enableDmlOperStat=0\" \"compatibleV3=0\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");

    int ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    clear_count();
    // 建kv表，dml操作
    ret = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // 建kv表，dml操作
    // create KV table
    ret = GmcKvCreateTable(g_stmt, MS_KVTable_Name_01, MS_config);
    EXPECT_EQ(GMERR_OK, ret);

    void *tableLabel = NULL;
    ret = GmcKvPrepareStmtByLabelName(g_stmt, MS_KVTable_Name_01);
    EXPECT_EQ(GMERR_OK, ret);

    // insert
    printf(">> insert kv\n");
    uint32_t value = 0;
    char Key_name[10] = {0};
    GmcKvTupleT kvInfo = {0};

    for (uint32_t i = 0; i < 1000; i++) {
        memset(Key_name, 0, 10);
        snprintf(Key_name, sizeof(Key_name), "Key_%d", i);
        value = i;

        memset(&kvInfo, 0, sizeof(GmcKvTupleT));
        kvInfo.key = Key_name;
        kvInfo.keyLen = strlen(Key_name);
        kvInfo.value = &value;
        kvInfo.valueLen = sizeof(uint32_t);

        ret = GmcKvSet(g_stmt, Key_name, strlen(Key_name), &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = dml_statis_check_kv(g_stmt, MS_KVTable_Name_01, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // 首次获取全局kv表数据
    clear_count();
    ret = dml_statis_check_T_GMDB(g_stmt, "T_GMDB");
    EXPECT_EQ(GMERR_OK, ret);
    ret = dml_statis_check_kv(g_stmt, "T_GMDB", MS_KVTable_Name_01);
    EXPECT_EQ(GMERR_OK, ret);

    ret = dml_statis_check_total(g_stmt, "T_GMDB", MS_KVTable_Name_01);
    EXPECT_EQ(GMERR_OK, ret);

    // check dml statis view
    clear_count();
    ret = dml_sysview_query(NULL, MS_KVTable_Name_01, NULL, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info(0);
    EXPECT_EQ(ret, 0);

    ret = GmcKvDropTable(g_stmt, MS_KVTable_Name_01);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("gmadmin -cfgName compatibleV3 -cfgVal 1");
    system("gmadmin -cfgName enableDmlOperStat -cfgVal 1");
}


const char *MS_KVTable_Name_02 = "KVTable_02";

// 011  配置dml统计的开关参数为1，启动服务后，建2个verttex， 2个kv，分别drop1个，查视图
TEST_F(dml_oper_statis_config_test, dml_oper_statis_config_test_011)
{
    system("sh $TEST_HOME/tools/start.sh");
    system("gmadmin -cfgName compatibleV3 -cfgVal 0");
    system("gmadmin -cfgName enableDmlOperStat -cfgVal 1");
    int ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // 建2个verttex， 2个kv，分别drop1个，查视图
    // create KV table
    ret = GmcKvCreateTable(g_stmt, MS_KVTable_Name_01, MS_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(g_stmt, MS_KVTable_Name_02, MS_config);
    EXPECT_EQ(GMERR_OK, ret);

    void *tableLabel = NULL;
    ret = GmcKvPrepareStmtByLabelName(g_stmt, MS_KVTable_Name_01);
    EXPECT_EQ(GMERR_OK, ret);

    // insert
    printf(">> insert kv\n");
    uint32_t value = 0;
    char Key_name[10] = {0};
    GmcKvTupleT kvInfo = {0};
    for (uint32_t i = 0; i < 1000; i++) {
        memset(Key_name, 0, 10);
        snprintf(Key_name, sizeof(Key_name), "Key_%d", i);
        value = i;

        memset(&kvInfo, 0, sizeof(GmcKvTupleT));
        kvInfo.key = Key_name;
        kvInfo.keyLen = strlen(Key_name);
        kvInfo.value = &value;
        kvInfo.valueLen = sizeof(uint32_t);

        ret = GmcKvSet(g_stmt, Key_name, strlen(Key_name), &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }

    tableLabel = NULL;
    ret = GmcKvPrepareStmtByLabelName(g_stmt, MS_KVTable_Name_02);
    EXPECT_EQ(GMERR_OK, ret);

    // insert
    printf(">> insert kv\n");
    value = 0;
    memset(Key_name, 0, sizeof(Key_name));
    memset(&kvInfo, 0, sizeof(GmcKvTupleT));
    for (uint32_t i = 0; i < 1000; i++) {
        memset(Key_name, 0, 10);
        snprintf(Key_name, sizeof(Key_name), "Key_%d", i);
        value = i;

        memset(&kvInfo, 0, sizeof(GmcKvTupleT));
        kvInfo.key = Key_name;
        kvInfo.keyLen = strlen(Key_name);
        kvInfo.value = &value;
        kvInfo.valueLen = sizeof(uint32_t);

        ret = GmcKvSet(g_stmt, Key_name, strlen(Key_name), &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 创建 vertexLabel_1
    char labelName[128] = "ip4forward00000";
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    GmcDropVertexLabel(g_stmt, labelName);
    ret = GmcCreateVertexLabel(g_stmt, schema_json, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema_json);

    uint64_t count;
    printf(">> insert \n");
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t i = 0;
    for (i = 0; i < 1000; i++) {
        ret = ip4forward00000_set_obj(g_stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(g_stmt);
        EXPECT_EQ(ret, GMERR_OK);
    }

    // 创建 vertexLabel_2   Tree_Vector.gmjson
    schema_json = NULL;
    readJanssonFile("./schema_file/Tree_Vector.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    GmcDropVertexLabel(g_stmt, (char*)"Tree_Vector");
    ret = GmcCreateVertexLabel(g_stmt, schema_json, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema_json);

    // open vertexLabel
    char labelNameTree[128] = "Tree_Vector";

    char f14_value[8] = "string";
    int array_num = 1;
    int vector_num = 1;
    printf(">> insert sync\n");

    for (i = 0; i < 100; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelNameTree, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        Tree_Vector_set_obj(g_stmt, i, false, f14_value, array_num, vector_num);

        ret = GmcExecute(g_stmt);
        EXPECT_EQ(ret, GMERR_OK);
    }

    ret = GmcGetVertexCount(g_stmt, labelNameTree, NULL, &count);
    EXPECT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    // 接口查询报错；
    set_count_value(1000, 0, 0, 0, 0, 0);
    ret = dml_statis_check(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    set_count_value(100, 0, 0, 0, 0, 0);
    ret = dml_statis_check(g_stmt, labelNameTree);
    EXPECT_EQ(GMERR_OK, ret);

    // check dml statis view
    ret = dml_sysview_query(NULL, labelName, NULL, NULL);
    EXPECT_EQ(ret, 0);

    system("gmsysview -q V\\$QRY_DML_OPER_STATIS -f LABEL_NAME=ip4forward00000");

    set_count_value(1000, 0, 0, 0, 0, 0);
#ifdef DIRECT_WRITE
    set_count_value(0, 0, 0, 0, 0, 0);
#endif
    ret = check_dml_info(0);
    EXPECT_EQ(ret, 0);
    ret = dml_sysview_query(NULL, labelNameTree, NULL, NULL, 0);
    EXPECT_EQ(ret, 0);
    set_count_value(100, 0, 0, 0, 0, 0);
#ifdef DIRECT_WRITE
    set_count_value(0, 0, 0, 0, 0, 0);
#endif
    ret = check_dml_info(0);
    EXPECT_EQ(ret, 0);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelNameTree);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmt, (char *)MS_KVTable_Name_01);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcKvDropTable(g_stmt, (char *)MS_KVTable_Name_02);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();

    system("gmadmin -cfgName compatibleV3 -cfgVal 1");
    system("gmadmin -cfgName enableDmlOperStat -cfgVal 1");
}

GmcStmtT *stmt_async = NULL;
GmcConnT *conn_async = NULL;
// 012  配置dml统计的配置项为1，启动服务后，建vertex表，有dml操作，dml统计接口传入异步stmt查询
TEST_F(dml_oper_statis_config_test, dml_oper_statis_config_test_012)
{
    system("sh $TEST_HOME/tools/start.sh");
    system("gmadmin -cfgName compatibleV3 -cfgVal 0");
    system("gmadmin -cfgName enableDmlOperStat -cfgVal 1");
    int ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // 创建异步连接
    ret = testGmcConnect(&conn_async, &stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建 vertexLabel_1
    char labelName[128] = "ip4forward00000";
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    GmcDropVertexLabel(g_stmt, labelName);
    ret = GmcCreateVertexLabel(g_stmt, schema_json, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema_json);

    // open vertexLabel
    printf(">> insert \n");
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t i = 0;
    for (i = 0; i < 1000; i++) {
        ret = ip4forward00000_set_obj(g_stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(g_stmt);
        EXPECT_EQ(ret, GMERR_OK);
    }

    set_count_value(1000, 0, 0, 0, 0, 0);
    ret = dml_statis_check(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    set_count_value(1000, 0, 0, 0, 0, 0);
    ret = dml_statis_check(stmt_async, labelName, NULL, GMERR_FEATURE_NOT_SUPPORTED);
    printf("ret: %d\n", ret);
    EXPECT_NE(GMERR_OK, ret);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn_async, stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();

    system("gmadmin -cfgName compatibleV3 -cfgVal 1");
    system("gmadmin -cfgName enableDmlOperStat -cfgVal 1");
}

// 013  配置dml统计的配置项为1，启动服务后，建vertex表，有dml操作，dml统计接口传入type为SET/REMOVE查询
TEST_F(dml_oper_statis_config_test, dml_oper_statis_config_test_013)
{
    system("sh $TEST_HOME/tools/start.sh");
    system("gmadmin -cfgName compatibleV3 -cfgVal 0");
    system("gmadmin -cfgName enableDmlOperStat -cfgVal 1");

    int ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // 创建异步连接

    ret = testGmcConnect(&conn_async, &stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建 vertexLabel_1
    char labelName[128] = "ip4forward00000";
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    GmcDropVertexLabel(g_stmt, labelName);
    ret = GmcCreateVertexLabel(g_stmt, schema_json, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema_json);

    // open vertexLabel
    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    printf(">> insert \n");
    uint64_t i = 0;
    for (i = 0; i < 1000; i++) {
        ret = ip4forward00000_set_obj(g_stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(g_stmt);
        EXPECT_EQ(ret, GMERR_OK);
    }

    set_count_value(1000, 0, 0, 0, 0, 0);
    ret = dml_statis_check(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    set_count_value(0, 0, 0, 0, 0, 0);
    ret = dml_statis_check_kv(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn_async, stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();

    system("gmadmin -cfgName compatibleV3 -cfgVal 1");
    system("gmadmin -cfgName enableDmlOperStat -cfgVal 1");
}

// 014
// 配置dml统计的配置项为1，启动服务后，建kv表，有dml操作，dml统计接口传入type为INSERT/DELETE/UPDATE/REPLACE/MERGE查询
TEST_F(dml_oper_statis_config_test, dml_oper_statis_config_test_014)
{
    system("sh $TEST_HOME/tools/start.sh");
    system("gmadmin -cfgName compatibleV3 -cfgVal 0");
    system("gmadmin -cfgName enableDmlOperStat -cfgVal 1");
    int ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // 建kv表，查视图
    //  create KV table
    ret = GmcKvCreateTable(g_stmt, MS_KVTable_Name_01, MS_config);
    EXPECT_EQ(GMERR_OK, ret);

    void *tableLabel = NULL;
    ret = GmcKvPrepareStmtByLabelName(g_stmt, MS_KVTable_Name_01);
    EXPECT_EQ(GMERR_OK, ret);

    // insert
    printf(">> insert kv\n");
    uint32_t value = 0;
    char Key_name[10] = {0};
    GmcKvTupleT kvInfo = {0};
    for (uint32_t i = 0; i < 1000; i++) {
        memset(Key_name, 0, 10);
        snprintf(Key_name, sizeof(Key_name), "Key_%d", i);
        value = i;

        memset(&kvInfo, 0, sizeof(GmcKvTupleT));
        kvInfo.key = Key_name;
        kvInfo.keyLen = strlen(Key_name);
        kvInfo.value = &value;
        kvInfo.valueLen = sizeof(uint32_t);

        ret = GmcKvSet(g_stmt, Key_name, strlen(Key_name), &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }

    // ret = GmcCloseKvTable(g_stmt);
    // EXPECT_EQ(GMERR_OK, ret);

    set_count_value(1000, 0, 0, 0, 0, 0);
    ret = dml_statis_check_kv(g_stmt, MS_KVTable_Name_01);
    EXPECT_EQ(GMERR_OK, ret);

    set_count_value(0, 0, 0, 0, 0, 0);
    ret = dml_statis_check(g_stmt, MS_KVTable_Name_01);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcKvDropTable(g_stmt, MS_KVTable_Name_01);
    EXPECT_EQ(GMERR_OK, ret);

    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    GmcDetachAllShmSeg();
    testEnvClean();

    system("gmadmin -cfgName compatibleV3 -cfgVal 1");
    system("gmadmin -cfgName enableDmlOperStat -cfgVal 1");
}
