extern "C" {
}
#include <stdarg.h>
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "dml_oper_statis_tool.h"
#include "t_datacom_lite.h"

#define LABELNAME_MAX_LENGTH 128

GmcConnT *g_conn = NULL;  // conn
GmcStmtT *g_stmt = NULL;  // stmt

GmcConnT *conn = NULL;
GmcStmtT *stmt = NULL;

GmcStmtT *g_stmt_async = NULL;
GmcConnT *g_conn_async = NULL;

char *schema_json = NULL;
char g_labelName[LABELNAME_MAX_LENGTH] = "ip4forward00000";
char g_labelNameTree[LABELNAME_MAX_LENGTH] = "Tree_Vector";

char g_configJson[128] = "{\"max_record_count\":999999, \"isFastReadUncommitted\":0}";
using namespace std;

struct timeval timeBegin, timeEnd;
uint64_t sec;
uint64_t usec;
uint64_t allTime;  // 单位是微妙
float ops;
uint64_t costTime;

class dml_oper_statis_test : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh");
        int res = testEnvInit();
        EXPECT_EQ(GMERR_OK, res);
    }

    static void TearDownTestCase()
    {
        testEnvClean();
    };
public:
    virtual void SetUp();
    virtual void TearDown();
};

void dml_oper_statis_test::SetUp()
{
    printf("\n======================TEST:BEGIN======================\n");
    int ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);

    // 创建同步连接
    conn = NULL;
    stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // 创建异步连接
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);

    //
    (void)GmcDropVertexLabel(stmt, g_labelName);
    (void)GmcDropVertexLabel(stmt, g_labelNameTree);
    (void)GmcKvDropTable(stmt, (char *)"KVTable_01");

    clear_count();
    AW_CHECK_LOG_BEGIN();
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_PRIMARY_KEY_VIOLATION);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1,errorMsg2);
    memset(g_dml_info, 0, sizeof(dml_info_t) * 3);
}

void dml_oper_statis_test::TearDown()
{
    printf("\n======================TEST:END========================\n");
    AW_CHECK_LOG_END();

    // 断开同步连接
    int ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // printf(">> dis conn\n");
    // int ret = dml_sysview_query(NULL, g_labelName, NULL, NULL);
    // EXPECT_EQ(ret, 0);
    // (void)prinf_dml_info();

    // 断开异步连接
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    int res = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, res);
}

#define END_GET_TIME()                              \
    do {                                            \
        gettimeofday(&timeEnd, NULL);               \
        sec = timeEnd.tv_sec - timeBegin.tv_sec;    \
        usec = timeEnd.tv_usec - timeBegin.tv_usec; \
        allTime = sec * 1000000 + usec;             \
    } while (0)

#define START_GET_TIME() \
    do {                 \
        RESET_COUNT();   \
    } while (0)

uint32_t count_succ = 0;
uint32_t count_fail = 0;

#define RESET_COUNT()   \
    do {                \
        count_succ = 0; \
        count_fail = 0; \
    } while (0)

//printf("id:%llu ret:%d\n", i, ret);  \

#define CHECK_SET_COUNT(ret) \
    if (ret) {               \
        count_fail++;        \
    } else {                 \
        count_succ++;        \
    }

#define SET_COUNT(cn_succ, cn_fail)                                                                       \
    do {                                                                                                  \
        cn_succ = count_succ;                                                                             \
        cn_fail = count_fail;                                                                             \
        printf(">  total: %u success: %u failed: %u\n", count_succ + count_fail, count_succ, count_fail); \
    } while (0)

#define SET_BATCH_COUNT(totalNum, succNum)                                                                 \
    do {                                                                                                   \
        count_succ = succNum;                                                                              \
        count_fail = totalNum - succNum;                                                                   \
        printf(">  batch_exec total: %u success: %u failed: %u\n", totalNum, succNum, totalNum - succNum); \
        if (count_fail > 1) {                                                                              \
            count_fail = 1;                                                                                \
        }                                                                                                  \
    } while (0)

// printf("vr_id:%u, vrf_index:%u ip:%u len:%u \n",rd_primary_label,rd_attribute_id, rd_dest_ip_addr, rd_mask_len);

#define IP4FORWARD_SCAN()                                                                                        \
    do {                                                                                                         \
        ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);                                  \
        ASSERT_EQ(GMERR_OK, ret);                                                                                \
        ret = GmcExecute(stmt);                                                                                  \
        EXPECT_EQ(GMERR_OK, ret);                                                                                \
        bool isFinish = false;                                                                                   \
        unsigned int cnt = 0;                                                                                    \
        while (!isFinish) {                                                                                      \
            ret = GmcFetch(stmt, &isFinish);                                                                     \
            EXPECT_EQ(GMERR_OK, ret);                                                                            \
            if (isFinish == true) {                                                                              \
                break;                                                                                           \
            }                                                                                                    \
            uint32_t rd_primary_label = 0;                                                                       \
            uint32_t rd_vrf_index = 0;                                                                           \
            uint32_t rd_dest_ip_addr = 0;                                                                        \
            uint8_t rd_mask_len = 0;                                                                             \
            uint32_t rd_attribute_id = 0;                                                                        \
            uint32_t rd_app_version = 0;                                                                         \
            uint32_t rd_nhp_group_id = 0;                                                                        \
            bool isNull;                                                                                         \
            ret = GmcGetVertexPropertyByName(stmt, "vr_id", &rd_primary_label, sizeof(uint32_t), &isNull);       \
            ret = GmcGetVertexPropertyByName(stmt, "vrf_index", &rd_vrf_index, sizeof(uint32_t), &isNull);       \
            ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", &rd_dest_ip_addr, sizeof(uint32_t), &isNull); \
            ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, sizeof(uint8_t), &isNull);          \
            ret = GmcGetVertexPropertyByName(stmt, "app_version", &rd_app_version, sizeof(uint32_t), &isNull);   \
            ret = GmcGetVertexPropertyByName(stmt, "attribute_id", &rd_attribute_id, sizeof(uint32_t), &isNull); \
            ret = GmcGetVertexPropertyByName(stmt, "nhp_group_id", &rd_nhp_group_id, sizeof(uint32_t), &isNull); \
            printf("vr_id:%u, vrf_index:%u ip:%u len:%u app_version:%u attribute_id:%u nhp_group_id:%u\n",       \
                rd_primary_label, rd_vrf_index, rd_dest_ip_addr, rd_mask_len, rd_app_version, rd_attribute_id,   \
                rd_nhp_group_id);                                                                                \
            cnt++;                                                                                               \
        }                                                                                                        \
        printf("scan cnt:%lu \n", cnt);                                                                          \
        GmcResetVertex(stmt, false);                                                                             \
    } while (0);

int check_count(uint32_t expSucc, uint32_t expFail)
{
    int ret = 0;
    EXPECT_EQ(count_succ, expSucc);
    if (expSucc != count_succ) {
        ret = -1;
    }

    EXPECT_EQ(count_fail, expFail);
    if (expFail != count_fail) {
        ret = -1;
    }

    return ret;
}

// 001. 配置性能统计的开关参数为1，创建vertex表ip4forward，同步insert 1000条数据，update 1000条，
// replace1000条，merge 1000条，delete 1000条；
TEST_F(dml_oper_statis_test, dml_oper_statis_test_001)
{
    // 创建 vertexLabel_1
    char labelName[128] = "ip4forward00000";

    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    int ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    uint64_t count;

    // open vertexLabel
    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    printf(">> insert \n");
    uint64_t i = 0;
    uint64_t j = 0;
    RESET_COUNT();
    for (i = 0; i < 100; i++) {
        j = i % 90;
        ret = ip4forward00000_set_obj(stmt, j);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(stmt);
        CHECK_SET_COUNT(ret);
    }

    // check dml_statis interface
    SET_COUNT(cn_insert_succ[0], cn_insert_fail[0]);
    ret = check_count(90, 10);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, labelName, NULL);
    EXPECT_EQ(ret, 0);

    // IP4FORWARD_SCAN();
    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    printf(">> update \n");
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);
    RESET_COUNT();
    for (i = 0; i < 80; i++) {
        ret = ip4forward00000_pri_key_set(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        uint32_t value_u32 = 90 + i;
        if (i >= 70) {
            value_u32 = 10 + i;
        }
        ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        CHECK_OK_RET(ret, "GmcSetVertexProperty app_version.");

        ret = GmcSetIndexKeyName(stmt, "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        CHECK_SET_COUNT(ret);
    }

    // check dml_statis interface
    SET_COUNT(cn_update_succ[0], cn_update_fail[0]);
    ret = check_count(70, 10);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, labelName, NULL);
    EXPECT_EQ(ret, 0);

    // check dml statis view
    ret = dml_sysview_query(NULL, g_labelName, NULL, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info(0);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    printf(">> replace \n");
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_REPLACE);
    ASSERT_EQ(GMERR_OK, ret);
    RESET_COUNT();
    for (i = 0; i < 80; i++) {
        ret = ip4forward00000_set_obj(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        uint64_t value_u64 = 90 + i;
        if (i >= 70) {
            value_u64 = 10 + i;
        }
        ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &value_u64, sizeof(value_u64));
        CHECK_OK_RET(ret, "GmcSetVertexProperty app_obj_id.");

        ret = GmcExecute(stmt);
        CHECK_SET_COUNT(ret);
    }

    // check dml_statis interface
    SET_COUNT(cn_replace_succ[0], cn_replace_fail[0]);
    ret = check_count(70, 10);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, labelName, NULL);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    // IP4FORWARD_SCAN();

    printf(">> merge \n");
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_MERGE);
    ASSERT_EQ(GMERR_OK, ret);
    RESET_COUNT();
    for (i = 0; i < 80; i++) {
        ret = ip4forward00000_merge_obj(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        uint32_t value_u32 = 90 + i;
        if (i >= 70) {
            value_u32 = 10 + i;
        }
        ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        CHECK_OK_RET(ret, "GmcSetVertexProperty attribute_id.");

        ret = GmcExecute(stmt);
        CHECK_SET_COUNT(ret);
    }
    // IP4FORWARD_SCAN();
    // check dml_statis interface
    SET_COUNT(cn_merge_succ[0], cn_merge_fail[0]);
    ret = check_count(70, 10);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, labelName, NULL);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    printf(">> delete \n");
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    RESET_COUNT();
    for (i = 0; i < 200; i++) {
        ret = ip4forward00000_pri_key_set(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcSetIndexKeyName(stmt, "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        CHECK_SET_COUNT(ret);
    }
    // check dml_statis interface
    SET_COUNT(cn_delete_succ[0], cn_delete_fail[0]);
    ret = check_count(200, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, labelName, NULL);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    // 清空数据
    printf(">> truncate table \n");
    ret = GmcTruncateVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);

    ret = dml_statis_check(stmt, labelName, NULL);
    EXPECT_EQ(ret, 0);

    // check dml statis view
    ret = dml_sysview_query(NULL, g_labelName, NULL, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info(0);
    EXPECT_EQ(ret, 0);

    printf(">> drop table \n");
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);

    ret = dml_statis_check(stmt, labelName, NULL, GMERR_UNDEFINED_TABLE);
    EXPECT_EQ(ret, GMERR_UNDEFINED_TABLE);

    // check dml statis view
    ret = dml_sysview_query(NULL, g_labelName, NULL, NULL);
    EXPECT_NE(ret, 0);
    ret = check_dml_info_null(0);
    EXPECT_EQ(ret, 0);

    ////查询顶点视图
    // const char * tmp = "gmsysview -q  V\\$QRY_DML_OPER_STATIS -e RTOS -s
    // usocket:/run/verona/unix_emserver"; printf("%s\n", tmp); ret = system(tmp); ASSERT_EQ(ret, GMERR_OK);
}

// 002. 配置性能统计的开关参数为1，创建vertex表ip4forward，异步insert 1000条数据，
//       update 1000条，replace1000条，merge 1000条，delete 1000条；
TEST_F(dml_oper_statis_test, dml_oper_statis_test_002)
{
    // 异步创建vertexLabel
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);

    AsyncUserDataT data = {0};
    int ret = GmcCreateVertexLabelAsync(g_stmt_async, schema_json, NULL, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(schema_json);

    // open vertex async
    void *vertexLabelAsync = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t count;
    printf(">> insert \n");
    uint64_t i = 0;
    uint64_t j = 0;
    RESET_COUNT();
    for (i = 0; i < 100; i++) {
        j = i % 90;
        ret = ip4forward00000_set_obj(g_stmt_async, j);
        EXPECT_EQ(ret, GMERR_OK);

        data = {0};
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(GMERR_OK, data.status);
        // EXPECT_EQ(1, data.affectRows);
        CHECK_SET_COUNT(data.status);

        // printf("row is %d\n", data.affectRows);
    }

    // check dml_statis interface
    SET_COUNT(cn_insert_succ[0], cn_insert_fail[0]);
    ret = check_count(90, 10);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelName, NULL);
    EXPECT_EQ(ret, 0);

    // check dml statis view
    ret = dml_sysview_query(NULL, g_labelName, NULL, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info(0);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    printf(">> update \n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    RESET_COUNT();
    for (i = 0; i < 80; i++) {
        ret = ip4forward00000_pri_key_set(g_stmt_async, i);
        EXPECT_EQ(ret, GMERR_OK);

        uint32_t value_u32 = 90 + i;
        if (i >= 70) {
            value_u32 = 10 + i;
        }
        ret = GmcSetVertexProperty(g_stmt_async, "app_version", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        CHECK_OK_RET(ret, "GmcSetVertexProperty app_version.");

        data = {0};
        ret = GmcSetIndexKeyName(g_stmt_async, (char *)"ip4_key");
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT updateRequestCtx;
        updateRequestCtx.updateCb = update_vertex_callback;
        updateRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &updateRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(GMERR_OK, data.status);
        // EXPECT_EQ(1, data.affectRows);
        CHECK_SET_COUNT(data.status);

        // printf("row is %d\n", data.affectRows);
    }

    // check dml_statis interface
    SET_COUNT(cn_update_succ[0], cn_update_fail[0]);
    ret = check_count(70, 10);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelName, NULL);
    EXPECT_EQ(ret, 0);

    // check dml statis view
    ret = dml_sysview_query(NULL, g_labelName, NULL, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info(0);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    printf(">> replace \n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    RESET_COUNT();
    for (i = 0; i < 80; i++) {
        ret = ip4forward00000_set_obj(g_stmt_async, i);
        EXPECT_EQ(ret, GMERR_OK);

        uint64_t value_u64 = 90 + i;
        if (i >= 70) {
            value_u64 = 10 + i;
        }
        ret = GmcSetVertexProperty(g_stmt_async, "app_obj_id", GMC_DATATYPE_UINT64, &value_u64, sizeof(value_u64));
        CHECK_OK_RET(ret, "GmcSetVertexProperty app_obj_id.");

        data = {0};
        GmcAsyncRequestDoneContextT replaceRequestCtx;
        replaceRequestCtx.replaceCb = replace_vertex_callback;
        replaceRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &replaceRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(GMERR_OK, data.status);
        // EXPECT_EQ(1, data.affectRows);
        CHECK_SET_COUNT(data.status);
        // printf("row is %d\n", data.affectRows);
    }

    // check dml_statis interface
    SET_COUNT(cn_replace_succ[0], cn_replace_fail[0]);
    ret = check_count(70, 10);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelName, NULL);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    printf(">> merge \n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    RESET_COUNT();
    for (i = 0; i < 80; i++) {
        ret = ip4forward00000_merge_obj(g_stmt_async, i);
        EXPECT_EQ(ret, GMERR_OK);

        uint32_t value_u32 = 90 + i;
        if (i >= 70) {
            value_u32 = 10 + i;
        }
        ret = GmcSetVertexProperty(g_stmt_async, "attribute_id", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        CHECK_OK_RET(ret, "GmcSetVertexProperty attribute_id.");

        data = {0};
        GmcAsyncRequestDoneContextT replaceRequestCtx;
        replaceRequestCtx.mergeCb = merge_vertex_callback;
        replaceRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &replaceRequestCtx);

        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(GMERR_OK, data.status);
        // EXPECT_EQ(1, data.affectRows);
        CHECK_SET_COUNT(data.status);

        // printf("row is %d\n", data.affectRows);
    }

    // check dml_statis interface
    SET_COUNT(cn_merge_succ[0], cn_merge_fail[0]);
    ret = check_count(70, 10);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelName, NULL);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    printf(">> delete \n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    RESET_COUNT();
    for (i = 0; i < 90; i++) {
        ret = ip4forward00000_pri_key_set(g_stmt_async, i);
        EXPECT_EQ(ret, GMERR_OK);

        data = {0};
        ret = GmcSetIndexKeyName(g_stmt_async, (char *)"ip4_key");
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT deleteRequestCtx;
        deleteRequestCtx.deleteCb = delete_vertex_callback;
        deleteRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &deleteRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(GMERR_OK, data.status);
        // EXPECT_EQ(1, data.affectRows);
        CHECK_SET_COUNT(data.status);

        // printf("row is %d\n", data.affectRows);
    }

    // check dml_statis interface
    SET_COUNT(cn_delete_succ[0], cn_delete_fail[0]);
    ret = check_count(90, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelName, NULL);
    EXPECT_EQ(ret, 0);

    // check dml statis view
    ret = dml_sysview_query(NULL, g_labelName, NULL, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info(0);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    EXPECT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    // // close vertexLabel
    // ret = GmcCloseVertexLabel(g_stmt_async, vertexLabelAsync);
    // EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 003. 配置性能统计的开关参数为1，创建vertex表ip4forward，
//      同步批量insert 1000条数据，update 1000条， replace1000条，merge 1000条，delete 1000条；
TEST_F(dml_oper_statis_test, dml_oper_statis_test_003)
{
    // 创建 vertexLabel_1
    char labelName[128] = "ip4forward00000";

    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    int ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    uint64_t count;
    unsigned int totalNum;
    unsigned int successNum;

    // open vertexLabel
    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    printf(">> insert \n");
    RESET_COUNT();
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t i = 0;
    uint64_t j = 0;
    RESET_COUNT();
    for (i = 0; i < 100; i++) {
        j = i % 90;
        ret = ip4forward00000_set_obj(stmt, j);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcBatchAddDML(batch, stmt);  // , GMC_CMD_INSERT_VERTEX);
        ASSERT_EQ(GMERR_OK, ret);
    }
    GmcBatchRetT batchRet;
    ret = GmcBatchExecute(batch, &batchRet);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);
    batch = NULL;
    // EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(100, totalNum);
    EXPECT_EQ(90, successNum);

    // check dml_statis interface
    SET_BATCH_COUNT(totalNum, successNum);
    SET_COUNT(cn_insert_succ[0], cn_insert_fail[0]);
    ret = check_count(90, 1);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelName, NULL);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    printf(">> update \n");
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);
    RESET_COUNT();

    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (i = 0; i < 80; i++) {
        ret = ip4forward00000_pri_key_set(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        uint32_t value_u32 = 90 + i;
        if (i >= 70) {
            value_u32 = 10 + i;
        }
        ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        CHECK_OK_RET(ret, "GmcSetVertexProperty app_version.");

        ret = GmcSetIndexKeyName(stmt, "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);  // ,GMC_CMD_UPDATE_VERTEX);
        ASSERT_EQ(GMERR_OK, ret);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);
    batch = NULL;
    // EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(80, totalNum);
    EXPECT_EQ(70, successNum);

    // check dml_statis interface
    SET_BATCH_COUNT(totalNum, successNum);
    SET_COUNT(cn_update_succ[0], cn_update_fail[0]);
    ret = check_count(70, 1);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelName, NULL);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    // check dml statis view
    ret = dml_sysview_query(NULL, g_labelName, NULL, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info(0);
    EXPECT_EQ(ret, 0);

    printf(">> replace \n");
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    RESET_COUNT();

    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (i = 0; i < 80; i++) {
        ret = ip4forward00000_set_obj(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        uint64_t value_u64 = 90 + i;
        if (i >= 70) {
            value_u64 = 10 + i;
        }
        ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &value_u64, sizeof(value_u64));
        CHECK_OK_RET(ret, "GmcSetVertexProperty app_obj_id.");

        ret = GmcBatchAddDML(batch, stmt);  // , GMC_CMD_REPLACE_VERTEX);
        ASSERT_EQ(GMERR_OK, ret);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);
    batch = NULL;
    // EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(80, totalNum);
    EXPECT_EQ(70, successNum);
    SET_BATCH_COUNT(totalNum, successNum);
    SET_COUNT(cn_replace_succ[0], cn_replace_fail[0]);
    ret = check_count(70, 1);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelName, NULL);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    printf(">> merge \n");
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_MERGE);
    ASSERT_EQ(GMERR_OK, ret);
    RESET_COUNT();

    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (i = 0; i < 80; i++) {
        ret = ip4forward00000_merge_obj(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        uint32_t value_u32 = 90 + i;
        if (i >= 70) {
            value_u32 = 10 + i;
        }
        ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        CHECK_OK_RET(ret, "GmcSetVertexProperty attribute_id.");

        ret = GmcBatchAddDML(batch, stmt);  // ,GMC_CMD_MERGE_VERTEX);
        ASSERT_EQ(GMERR_OK, ret);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);
    batch = NULL;
    // EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(80, totalNum);
    EXPECT_EQ(70, successNum);

    // check dml_statis interface
    SET_BATCH_COUNT(totalNum, successNum);
    SET_COUNT(cn_merge_succ[0], cn_merge_fail[0]);
    ret = check_count(70, 1);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelName, NULL);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    // check dml statis view
    ret = dml_sysview_query(NULL, g_labelName, NULL, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info(0);
    EXPECT_EQ(ret, 0);

    printf(">> delete \n");
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    RESET_COUNT();

    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (i = 0; i < 80; i++) {
        ret = ip4forward00000_pri_key_set(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcSetIndexKeyName(stmt, "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcBatchAddDML(batch, stmt);  // ,GMC_CMD_DELETE_VERTEX);
        ASSERT_EQ(GMERR_OK, ret);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);
    batch = NULL;
    // EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(80, totalNum);
    EXPECT_EQ(80, successNum);
    SET_BATCH_COUNT(totalNum, successNum);
    SET_COUNT(cn_delete_succ[0], cn_delete_fail[0]);
    ret = check_count(80, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelName, NULL);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    // drop vertexLabel
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 004. 配置性能统计的开关参数为1，创建vertex表ip4forward，异步批量insert 1000条数据，update 1000条，
//      replace1000条，merge 1000条，delete 1000条；
TEST_F(dml_oper_statis_test, dml_oper_statis_test_004)
{
    // 异步创建vertexLabel
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);

    AsyncUserDataT data = {0};
    int ret = GmcCreateVertexLabelAsync(g_stmt_async, schema_json, NULL, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(schema_json);

    // open vertex async
    void *vertexLabelAsync = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t count;
    printf(">> insert \n");
    RESET_COUNT();
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t i = 0;
    uint64_t j = 0;
    for (i = 0; i < 100; i++) {
        j = i % 90;
        ret = ip4forward00000_set_obj(g_stmt_async, j);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcBatchAddDML(batch, g_stmt_async);  // GMC_CMD_INSERT_VERTEX);
        ASSERT_EQ(GMERR_OK, ret);
    }
    data = {0};
    ret = GmcBatchExecuteAsync(batch, BatchAsyncCallBack, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, ret);
    // EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(100, data.totalNum);
    EXPECT_EQ(90, data.succNum);
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);

    // check dml_statis interface
    SET_BATCH_COUNT(data.totalNum, data.succNum);
    SET_COUNT(cn_insert_succ[0], cn_insert_fail[0]);
    ret = check_count(90, 1);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelName, NULL);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    printf(">> update \n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);
    RESET_COUNT();
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < 80; i++) {
        ret = ip4forward00000_pri_key_set(g_stmt_async, i);
        EXPECT_EQ(ret, GMERR_OK);

        uint32_t value_u32 = 90 + i;
        if (i >= 70) {
            value_u32 = 10 + i;
        }
        ret = GmcSetVertexProperty(g_stmt_async, "app_version", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        CHECK_OK_RET(ret, "GmcSetVertexProperty app_version.");

        ret = GmcSetIndexKeyName(g_stmt_async, "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_async);  // GMC_CMD_UPDATE_VERTEX);
        ASSERT_EQ(GMERR_OK, ret);
    }

    data = {0};
    ret = GmcBatchExecuteAsync(batch, BatchAsyncCallBack, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, ret);
    // EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(80, data.totalNum);
    EXPECT_EQ(70, data.succNum);
    SET_BATCH_COUNT(data.totalNum, data.succNum);
    SET_COUNT(cn_update_succ[0], cn_update_fail[0]);
    ret = check_count(70, 1);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelName, NULL);
    EXPECT_EQ(ret, 0);
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    // check dml statis view
    ret = dml_sysview_query(NULL, g_labelName, NULL, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info(0);
    EXPECT_EQ(ret, 0);

    printf(">> replace \n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    RESET_COUNT();
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < 80; i++) {
        ret = ip4forward00000_set_obj(g_stmt_async, i);
        EXPECT_EQ(ret, GMERR_OK);

        uint64_t value_u64 = 90 + i;
        if (i >= 70) {
            value_u64 = 10 + i;
        }
        ret = GmcSetVertexProperty(g_stmt_async, "app_obj_id", GMC_DATATYPE_UINT64, &value_u64, sizeof(value_u64));
        CHECK_OK_RET(ret, "GmcSetVertexProperty app_obj_id.");

        ret = GmcBatchAddDML(batch, g_stmt_async);  // GMC_CMD_REPLACE_VERTEX);
        ASSERT_EQ(GMERR_OK, ret);
    }
    data = {0};
    ret = GmcBatchExecuteAsync(batch, BatchAsyncCallBack, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, ret);
    // EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(80, data.totalNum);
    EXPECT_EQ(70, data.succNum);
    SET_BATCH_COUNT(data.totalNum, data.succNum);
    SET_COUNT(cn_replace_succ[0], cn_replace_fail[0]);
    ret = check_count(70, 1);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelName, NULL);
    EXPECT_EQ(ret, 0);
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    // check dml statis view
    ret = dml_sysview_query(NULL, g_labelName, NULL, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info(0);
    EXPECT_EQ(ret, 0);

    printf(">> merge \n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_MERGE);
    ASSERT_EQ(GMERR_OK, ret);
    RESET_COUNT();
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < 80; i++) {
        ret = ip4forward00000_merge_obj(g_stmt_async, i);
        EXPECT_EQ(ret, GMERR_OK);

        uint32_t value_u32 = 90 + i;
        if (i >= 70) {
            value_u32 = 10 + i;
        }
        ret = GmcSetVertexProperty(g_stmt_async, "attribute_id", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        CHECK_OK_RET(ret, "GmcSetVertexProperty attribute_id.");

        ret = GmcBatchAddDML(batch, g_stmt_async);  // GMC_CMD_MERGE_VERTEX);
        ASSERT_EQ(GMERR_OK, ret);
    }
    data = {0};
    ret = GmcBatchExecuteAsync(batch, BatchAsyncCallBack, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, ret);
    // EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(80, data.totalNum);
    EXPECT_EQ(70, data.succNum);
    SET_BATCH_COUNT(data.totalNum, data.succNum);
    SET_COUNT(cn_merge_succ[0], cn_merge_fail[0]);
    ret = check_count(70, 1);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelName, NULL);
    EXPECT_EQ(ret, 0);
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    printf(">> delete \n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    RESET_COUNT();
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < 80; i++) {
        ret = ip4forward00000_pri_key_set(g_stmt_async, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcSetIndexKeyName(g_stmt_async, "ip4_key");

        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_async);  // GMC_CMD_DELETE_VERTEX);
        ASSERT_EQ(GMERR_OK, ret);
    }
    data = {0};
    ret = GmcBatchExecuteAsync(batch, BatchAsyncCallBack, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, ret);
    // EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(80, data.totalNum);
    EXPECT_EQ(80, data.succNum);
    SET_BATCH_COUNT(data.totalNum, data.succNum);
    SET_COUNT(cn_delete_succ[0], cn_delete_fail[0]);
    ret = check_count(80, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelName, NULL);
    EXPECT_EQ(ret, 0);

    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    // check dml statis view
    ret = dml_sysview_query(NULL, g_labelName, NULL, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info(0);
    EXPECT_EQ(ret, 0);

    // close vertexLabel
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 005. 配置性能统计的开关参数为1，创建vertex表ip4forward，同步insert 1000条数据，异步update 1000条，
//      同步批量replace1000条，异步批量merge 1000条，delete 1000条；
TEST_F(dml_oper_statis_test, dml_oper_statis_test_005)
{
    // 异步创建vertexLabel
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);

    AsyncUserDataT data = {0};
    int ret = GmcCreateVertexLabelAsync(g_stmt_async, schema_json, NULL, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(schema_json);

    // open vertex async
    void *vertexLabelAsync = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // open vertexLabel
    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t count;
    unsigned int totalNum;
    unsigned int successNum;

    printf(">> insert sync\n");
    RESET_COUNT();
    uint64_t i = 0;
    uint64_t j = 0;
    for (i = 0; i < 100; i++) {
        j = i % 90;
        ret = ip4forward00000_set_obj(stmt, j);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(stmt);
        CHECK_SET_COUNT(ret);
    }
    SET_COUNT(cn_insert_succ[0], cn_insert_fail[0]);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    // check dml_statis interface
    SET_COUNT(cn_insert_succ[0], cn_insert_fail[0]);
    ret = check_count(90, 10);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelName, NULL);
    EXPECT_EQ(ret, 0);

    printf(">> update async\n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    RESET_COUNT();
    for (i = 0; i < 80; i++) {
        ret = ip4forward00000_pri_key_set(g_stmt_async, i);
        EXPECT_EQ(ret, GMERR_OK);

        uint32_t value_u32 = 90 + i;
        if (i >= 70) {
            value_u32 = 10 + i;
        }
        ret = GmcSetVertexProperty(g_stmt_async, "app_version", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        CHECK_OK_RET(ret, "GmcSetVertexProperty app_version.");

        data = {0};
        ret = GmcSetIndexKeyName(g_stmt_async, (char *)"ip4_key");
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT updateRequestCtx;
        updateRequestCtx.updateCb = update_vertex_callback;
        updateRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &updateRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(GMERR_OK, data.status);
        CHECK_SET_COUNT(data.status);
        // EXPECT_EQ(1, data.affectRows);

        // printf("row is %d\n", data.affectRows);
    }

    // check dml_statis interface
    SET_COUNT(cn_update_succ[0], cn_update_fail[0]);
    ret = check_count(70, 10);
    EXPECT_EQ(ret, 0);

    ret = dml_statis_check(stmt, g_labelName, NULL);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    printf(">> replace  \n");
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    RESET_COUNT();
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < 80; i++) {
        ret = ip4forward00000_set_obj(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        uint64_t value_u64 = 90 + i;
        if (i >= 70) {
            value_u64 = 10 + i;
        }
        ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &value_u64, sizeof(value_u64));
        CHECK_OK_RET(ret, "GmcSetVertexProperty app_obj_id.");

        ret = GmcBatchAddDML(batch, stmt);  // ,GMC_CMD_REPLACE_VERTEX);
        ASSERT_EQ(GMERR_OK, ret);
    }
    GmcBatchRetT batchRet;
    ret = GmcBatchExecute(batch, &batchRet);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);

    // EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(80, totalNum);
    EXPECT_EQ(70, successNum);

    // check dml_statis interface
    SET_BATCH_COUNT(totalNum, successNum);
    SET_COUNT(cn_replace_succ[0], cn_replace_fail[0]);
    ret = check_count(70, 1);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelName, NULL);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    printf(">> merge \n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_MERGE);
    ASSERT_EQ(GMERR_OK, ret);
    RESET_COUNT();
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (i = 0; i < 80; i++) {
        ret = ip4forward00000_merge_obj(g_stmt_async, i);
        EXPECT_EQ(ret, GMERR_OK);

        uint32_t value_u32 = 90 + i;
        if (i >= 70) {
            value_u32 = 10 + i;
        }
        ret = GmcSetVertexProperty(g_stmt_async, "attribute_id", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        CHECK_OK_RET(ret, "GmcSetVertexProperty attribute_id.");

        ret = GmcBatchAddDML(batch, g_stmt_async);  // GMC_CMD_MERGE_VERTEX);
        ASSERT_EQ(GMERR_OK, ret);
    }
    data = {0};
    ret = GmcBatchExecuteAsync(batch, BatchAsyncCallBack, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, ret);
    // EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(80, data.totalNum);
    EXPECT_EQ(70, data.succNum);

    // check dml_statis interface
    SET_BATCH_COUNT(data.totalNum, data.succNum);
    SET_COUNT(cn_merge_succ[0], cn_merge_fail[0]);
    ret = check_count(70, 1);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelName, NULL);
    EXPECT_EQ(ret, 0);
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    printf(">> delete \n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    RESET_COUNT();
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < 80; i++) {
        ret = ip4forward00000_pri_key_set(g_stmt_async, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcSetIndexKeyName(g_stmt_async, "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcBatchAddDML(batch, g_stmt_async);  // GMC_CMD_DELETE_VERTEX);
        // ASSERT_EQ(GMERR_OK, ret);
    }
    data = {0};
    ret = GmcBatchExecuteAsync(batch, BatchAsyncCallBack, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, ret);
    // EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(80, data.totalNum);
    EXPECT_EQ(80, data.succNum);

    // check dml_statis interface
    SET_BATCH_COUNT(data.totalNum, data.succNum);
    SET_COUNT(cn_delete_succ[0], cn_delete_fail[0]);
    ret = check_count(80, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelName, NULL);
    EXPECT_EQ(ret, 0);
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    GmcFreeStmt(g_stmt_async);
    g_stmt_async = NULL;

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 006. 配置性能统计的开关参数为1，创建vertex表ip4forward，
//      同步一个批量操作里insert 10条，update 10条， replace10条，merge 10条，delete 10条；
TEST_F(dml_oper_statis_test, dml_oper_statis_test_006)
{
    // 创建 vertexLabel_1
    char labelName[128] = "ip4forward00000";

    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    int ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    uint64_t count;
    unsigned int totalNum;
    unsigned int successNum;

    // open vertexLabel
    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    printf(">> insert \n");
    RESET_COUNT();
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t i = 0;
    uint64_t j = 0;
    for (i = 0; i < 100; i++) {
        j = i % 90;
        ret = ip4forward00000_set_obj(stmt, j);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcBatchAddDML(batch, stmt);  // ,GMC_CMD_INSERT_VERTEX);
        ASSERT_EQ(GMERR_OK, ret);
    }

    printf(">> update \n");
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);
    for (i = 0; i < 80; i++) {
        ret = ip4forward00000_pri_key_set(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        uint32_t value_u32 = 90 + i;
        if (i >= 70) {
            value_u32 = 10 + i;
        }
        ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        CHECK_OK_RET(ret, "GmcSetVertexProperty app_version.");

        ret = GmcSetIndexKeyName(stmt, "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);  // ,GMC_CMD_UPDATE_VERTEX);
        ASSERT_EQ(GMERR_OK, ret);
    }

    printf(">> replace \n");
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < 80; i++) {
        ret = ip4forward00000_set_obj(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        uint64_t value_u64 = 90 + i;
        if (i >= 70) {
            value_u64 = 10 + i;
        }
        ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &value_u64, sizeof(value_u64));
        CHECK_OK_RET(ret, "GmcSetVertexProperty app_obj_id.");

        ret = GmcBatchAddDML(batch, stmt);  // ,GMC_CMD_REPLACE_VERTEX);
        ASSERT_EQ(GMERR_OK, ret);
    }

    printf(">> merge \n");
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_MERGE);
    ASSERT_EQ(GMERR_OK, ret);
    for (i = 0; i < 80; i++) {
        ret = ip4forward00000_merge_obj(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        uint32_t value_u32 = 90 + i;
        if (i >= 70) {
            value_u32 = 10 + i;
        }
        ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        CHECK_OK_RET(ret, "GmcSetVertexProperty attribute_id.");

        ret = GmcBatchAddDML(batch, stmt);  // ,GMC_CMD_MERGE_VERTEX);
        ASSERT_EQ(GMERR_OK, ret);
    }

    printf(">> delete \n");
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    for (i = 0; i < 90; i++) {
        ret = ip4forward00000_pri_key_set(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcSetIndexKeyName(stmt, "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);  // ,GMC_CMD_DELETE_VERTEX);
        ASSERT_EQ(GMERR_OK, ret);
    }

    GmcBatchRetT batchRet;
    ret = GmcBatchExecute(batch, &batchRet);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);
    batch = NULL;
    // EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(430, totalNum);
    EXPECT_EQ(90, successNum);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    // check dml_statis interface
    SET_BATCH_COUNT(totalNum, successNum);
    SET_COUNT(cn_insert_succ[0], cn_insert_fail[0]);
    ret = check_count(90, 1);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelName, NULL);
    EXPECT_EQ(ret, 0);

    // check dml statis view
    ret = dml_sysview_query(NULL, g_labelName, NULL, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info(0);
    EXPECT_EQ(ret, 0);

    // drop vertexLabel
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 007. 配置性能统计的开关参数为1，创建vertex表ip4forward，
//      异步一个批量操作里insert 10条，update 10条， replace10条，merge 10条，delete 10条；
TEST_F(dml_oper_statis_test, dml_oper_statis_test_007)
{
    // 创建 vertexLabel_1
    char labelName[128] = "ip4forward00000";

    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    int ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    uint64_t count;
    unsigned int totalNum;
    unsigned int successNum;

    // open vertex async
    AsyncUserDataT data = {0};
    void *vertexLabelAsync = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    printf(">> insert \n");
    RESET_COUNT();
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t i = 0;
    uint64_t j = 0;
    for (i = 0; i < 100; i++) {
        j = i % 90;
        ret = ip4forward00000_set_obj(g_stmt_async, j);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcBatchAddDML(batch, g_stmt_async);  // GMC_CMD_INSERT_VERTEX);
        ASSERT_EQ(GMERR_OK, ret);
    }

    printf(">> update \n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);
    for (i = 0; i < 80; i++) {
        ret = ip4forward00000_pri_key_set(g_stmt_async, i);
        EXPECT_EQ(ret, GMERR_OK);

        uint32_t value_u32 = 90 + i;
        if (i >= 70) {
            value_u32 = 10 + i;
        }
        ret = GmcSetVertexProperty(g_stmt_async, "app_version", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        CHECK_OK_RET(ret, "GmcSetVertexProperty app_version.");

        ret = GmcSetIndexKeyName(g_stmt_async, "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_async);  // GMC_CMD_UPDATE_VERTEX);
        ASSERT_EQ(GMERR_OK, ret);
    }

    printf(">> replace \n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < 80; i++) {
        ret = ip4forward00000_set_obj(g_stmt_async, i);
        EXPECT_EQ(ret, GMERR_OK);

        uint64_t value_u64 = 90 + i;
        if (i >= 70) {
            value_u64 = 10 + i;
        }
        ret = GmcSetVertexProperty(g_stmt_async, "app_obj_id", GMC_DATATYPE_UINT64, &value_u64, sizeof(value_u64));
        CHECK_OK_RET(ret, "GmcSetVertexProperty app_obj_id.");

        ret = GmcBatchAddDML(batch, g_stmt_async);  // GMC_CMD_REPLACE_VERTEX);
        ASSERT_EQ(GMERR_OK, ret);
    }

    printf(">> merge \n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_MERGE);
    ASSERT_EQ(GMERR_OK, ret);
    for (i = 0; i < 80; i++) {
        ret = ip4forward00000_merge_obj(g_stmt_async, i);
        EXPECT_EQ(ret, GMERR_OK);

        uint32_t value_u32 = 90 + i;
        if (i >= 70) {
            value_u32 = 10 + i;
        }
        ret = GmcSetVertexProperty(g_stmt_async, "attribute_id", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        CHECK_OK_RET(ret, "GmcSetVertexProperty attribute_id.");

        ret = GmcBatchAddDML(batch, g_stmt_async);  // GMC_CMD_MERGE_VERTEX);
        ASSERT_EQ(GMERR_OK, ret);
    }

    printf(">> delete \n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    for (i = 0; i < 90; i++) {
        ret = ip4forward00000_pri_key_set(g_stmt_async, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcSetIndexKeyName(g_stmt_async, "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_async);  // GMC_CMD_DELETE_VERTEX);
        ASSERT_EQ(GMERR_OK, ret);
    }

    data = {0};
    ret = GmcBatchExecuteAsync(batch, BatchAsyncCallBack, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, ret);
    // EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(430, data.totalNum);
    EXPECT_EQ(90, data.succNum);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    // check dml_statis interface
    SET_BATCH_COUNT(data.totalNum, data.succNum);
    SET_COUNT(cn_insert_succ[0], cn_insert_fail[0]);
    ret = check_count(90, 1);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelName, NULL);
    EXPECT_EQ(ret, 0);

    // check dml statis view
    ret = dml_sysview_query(NULL, g_labelName, NULL, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info(0);
    EXPECT_EQ(ret, 0);

    // close vertexLabel
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 008. 配置性能统计的开关参数为1，创建vertex表ip4forward，同步insert 1000条数据，
//      异步update 1000条，同步批量replace1000条，异步批量merge 1000条；Truncate 操作
TEST_F(dml_oper_statis_test, dml_oper_statis_test_008)
{
    // 异步创建vertexLabel
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);

    AsyncUserDataT data = {0};
    int ret = GmcCreateVertexLabelAsync(g_stmt_async, schema_json, NULL, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(schema_json);

    // open vertex async
    void *vertexLabelAsync = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // open vertexLabel
    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t count;
    unsigned int totalNum;
    unsigned int successNum;

    printf(">> insert sync\n");
    RESET_COUNT();
    uint64_t i = 0;
    uint64_t j = 0;
    for (i = 0; i < 100; i++) {
        j = i % 90;
        ret = ip4forward00000_set_obj(stmt, j);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(stmt);
        CHECK_SET_COUNT(ret);
    }
    SET_COUNT(cn_insert_succ[0], cn_insert_fail[0]);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    // check dml_statis interface
    SET_COUNT(cn_insert_succ[0], cn_insert_fail[0]);
    ret = check_count(90, 10);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelName, NULL);
    EXPECT_EQ(ret, 0);

    printf(">> update async\n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);
    RESET_COUNT();
    for (i = 0; i < 80; i++) {
        ret = ip4forward00000_pri_key_set(g_stmt_async, i);
        EXPECT_EQ(ret, GMERR_OK);

        uint32_t value_u32 = 90 + i;
        if (i >= 70) {
            value_u32 = 10 + i;
        }
        ret = GmcSetVertexProperty(g_stmt_async, "app_version", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        CHECK_OK_RET(ret, "GmcSetVertexProperty app_version.");

        data = {0};
        ret = GmcSetIndexKeyName(g_stmt_async, (char *)"ip4_key");
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT updateRequestCtx;
        updateRequestCtx.updateCb = update_vertex_callback;
        updateRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &updateRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(GMERR_OK, data.status);
        CHECK_SET_COUNT(data.status);
        // EXPECT_EQ(1, data.affectRows);

        // printf("row is %d\n", data.affectRows);
    }

    // check dml_statis interface
    SET_COUNT(cn_update_succ[0], cn_update_fail[0]);
    ret = check_count(70, 10);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelName, NULL);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    printf(">> replace  \n");
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    RESET_COUNT();
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < 80; i++) {
        ret = ip4forward00000_set_obj(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        uint64_t value_u64 = 90 + i;
        if (i >= 70) {
            value_u64 = 10 + i;
        }
        ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &value_u64, sizeof(value_u64));
        CHECK_OK_RET(ret, "GmcSetVertexProperty app_obj_id.");

        ret = GmcBatchAddDML(batch, stmt);  // ,GMC_CMD_REPLACE_VERTEX);
        ASSERT_EQ(GMERR_OK, ret);
    }
    GmcBatchRetT batchRet;
    ret = GmcBatchExecute(batch, &batchRet);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);
    batch = NULL;
    // EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(80, totalNum);
    EXPECT_EQ(70, successNum);

    // check dml_statis interface
    SET_BATCH_COUNT(totalNum, successNum);
    SET_COUNT(cn_replace_succ[0], cn_replace_fail[0]);
    ret = check_count(70, 1);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelName, NULL);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    printf(">> merge \n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_MERGE);
    ASSERT_EQ(GMERR_OK, ret);
    RESET_COUNT();
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (i = 0; i < 80; i++) {
        ret = ip4forward00000_merge_obj(g_stmt_async, i);
        EXPECT_EQ(ret, GMERR_OK);

        uint32_t value_u32 = 90 + i;
        if (i >= 70) {
            value_u32 = 10 + i;
        }
        ret = GmcSetVertexProperty(g_stmt_async, "attribute_id", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        CHECK_OK_RET(ret, "GmcSetVertexProperty attribute_id.");

        ret = GmcBatchAddDML(batch, g_stmt_async);  // GMC_CMD_MERGE_VERTEX);
        ASSERT_EQ(GMERR_OK, ret);
    }
    data = {0};
    ret = GmcBatchExecuteAsync(batch, BatchAsyncCallBack, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, ret);
    // EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(80, data.totalNum);
    EXPECT_EQ(70, data.succNum);

    // check dml_statis interface
    SET_BATCH_COUNT(data.totalNum, data.succNum);
    SET_COUNT(cn_merge_succ[0], cn_merge_fail[0]);
    ret = check_count(70, 1);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelName, NULL);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    printf(">> delete \n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    RESET_COUNT();
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < 80; i++) {
        ret = ip4forward00000_pri_key_set(g_stmt_async, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcSetIndexKeyName(g_stmt_async, "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcBatchAddDML(batch, g_stmt_async);  // GMC_CMD_DELETE_VERTEX);
        // ASSERT_EQ(GMERR_OK, ret);
    }
    data = {0};
    ret = GmcBatchExecuteAsync(batch, BatchAsyncCallBack, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, ret);
    // EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(80, data.totalNum);
    EXPECT_EQ(80, data.succNum);

    // check dml_statis interface
    SET_BATCH_COUNT(data.totalNum, data.succNum);
    SET_COUNT(cn_delete_succ[0], cn_delete_fail[0]);
    ret = check_count(80, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelName, NULL);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    // close vertexLabel
    // ret = GmcCloseVertexLabel(stmt, vertexLabel);
    ASSERT_EQ(GMERR_OK, ret);

    // close vertexLabel
    // ret = GmcCloseVertexLabel(g_stmt_async, vertexLabelAsync);
    GmcFreeStmt(g_stmt_async);
    g_stmt_async = NULL;
    ASSERT_EQ(GMERR_OK, ret);

    // 清空数据
    printf(">> truncate table \n");
    ret = GmcTruncateVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);

    // check dml_statis interface
    ret = dml_statis_check(stmt, g_labelName, NULL);
    EXPECT_EQ(ret, 0);

    // check dml statis view
    ret = dml_sysview_query(NULL, g_labelName, NULL, NULL);
    EXPECT_EQ(ret, 0);

    ret = check_dml_info(0);
#ifdef DIRECT_WRITE
    EXPECT_EQ(ret, 1);
#else
    EXPECT_EQ(ret, 0);
#endif

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 009. 配置性能统计的开关参数为1，创建vertex表ip4forward，同步insert 1000条数据，
//      异步update 1000条，同步批量replace1000条，异步批量merge 1000条；drop table 操作
TEST_F(dml_oper_statis_test, dml_oper_statis_test_009)
{
    // 异步创建vertexLabel
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);

    AsyncUserDataT data = {0};
    int ret = GmcCreateVertexLabelAsync(g_stmt_async, schema_json, NULL, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(schema_json);

    // open vertex async
    void *vertexLabelAsync = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // open vertexLabel
    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t count;
    unsigned int totalNum;
    unsigned int successNum;

    printf(">> insert sync\n");
    RESET_COUNT();
    uint64_t i = 0;
    uint64_t j = 0;
    for (i = 0; i < 100; i++) {
        j = i % 90;
        ret = ip4forward00000_set_obj(stmt, j);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(stmt);
        CHECK_SET_COUNT(ret);
    }
    SET_COUNT(cn_insert_succ[0], cn_insert_fail[0]);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    // check dml_statis interface
    SET_COUNT(cn_insert_succ[0], cn_insert_fail[0]);
    ret = check_count(90, 10);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelName, NULL);
    EXPECT_EQ(ret, 0);

    printf(">> update async\n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);
    RESET_COUNT();
    for (i = 0; i < 80; i++) {
        ret = ip4forward00000_pri_key_set(g_stmt_async, i);
        EXPECT_EQ(ret, GMERR_OK);

        uint32_t value_u32 = 90 + i;
        if (i >= 70) {
            value_u32 = 10 + i;
        }
        ret = GmcSetVertexProperty(g_stmt_async, "app_version", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        CHECK_OK_RET(ret, "GmcSetVertexProperty app_version.");

        data = {0};
        ret = GmcSetIndexKeyName(g_stmt_async, (char *)"ip4_key");
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT updateRequestCtx;
        updateRequestCtx.updateCb = update_vertex_callback;
        updateRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &updateRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(GMERR_OK, data.status);
        CHECK_SET_COUNT(data.status);
        // EXPECT_EQ(1, data.affectRows);

        // printf("row is %d\n", data.affectRows);
    }

    // check dml_statis interface
    SET_COUNT(cn_update_succ[0], cn_update_fail[0]);
    ret = check_count(70, 10);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelName, NULL);
    EXPECT_EQ(ret, 0);

    printf(">> update async\n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);
    // RESET_COUNT();
    for (i = 0; i < 80; i++) {
        ret = ip4forward00000_pri_key_set(g_stmt_async, i);
        EXPECT_EQ(ret, GMERR_OK);

        uint32_t value_u32 = 190 + i;
        ret = GmcSetVertexProperty(g_stmt_async, "app_version", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        CHECK_OK_RET(ret, "GmcSetVertexProperty app_version.");

        data = {0};
        ret = GmcSetIndexKeyName(g_stmt_async, (char *)"ip4_key");
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT updateRequestCtx;
        updateRequestCtx.updateCb = update_vertex_callback;
        updateRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &updateRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);

        // EXPECT_EQ(GMERR_OK, data.status);
        CHECK_SET_COUNT(data.status);
        // EXPECT_EQ(1, data.affectRows);

        // printf("row is %d\n", data.affectRows);
    }

    // check dml_statis interface
    SET_COUNT(cn_update_succ[0], cn_update_fail[0]);
    ret = check_count(150, 10);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelName, NULL);
    EXPECT_EQ(ret, 0);

    // check dml statis view
    ret = dml_sysview_query(NULL, g_labelName, NULL, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info(0);
#ifdef DIRECT_WRITE
    EXPECT_EQ(ret, 1);
    // 增加偶现报错定位信息
    if(ret==0){
        system("gmsysview -q V\\$QRY_DML_OPER_STATIS");
    }
#else
    EXPECT_EQ(ret, 0);
    // 增加偶现报错定位信息
    if(ret!=0){
        system("gmsysview -q V\\$QRY_DML_OPER_STATIS");
    }
#endif

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    printf(">> replace  \n");
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_REPLACE);
    ASSERT_EQ(GMERR_OK, ret);
    RESET_COUNT();
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < 80; i++) {
        ret = ip4forward00000_set_obj(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        uint64_t value_u64 = 90 + i;
        if (i >= 70) {
            value_u64 = 10 + i;
        }
        ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &value_u64, sizeof(value_u64));
        CHECK_OK_RET(ret, "GmcSetVertexProperty app_obj_id.");

        ret = GmcBatchAddDML(batch, stmt);  // ,GMC_CMD_REPLACE_VERTEX);
        ASSERT_EQ(GMERR_OK, ret);
    }
    GmcBatchRetT batchRet;
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_UNIQUE_VIOLATION, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);
    batch = NULL;
    // EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(80, totalNum);
    EXPECT_EQ(70, successNum);

    // check dml_statis interface
    SET_BATCH_COUNT(totalNum, successNum);
    SET_COUNT(cn_replace_succ[0], cn_replace_fail[0]);
    ret = check_count(70, 1);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelName, NULL);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    printf(">> merge \n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_MERGE);
    ASSERT_EQ(GMERR_OK, ret);
    RESET_COUNT();
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (i = 0; i < 80; i++) {
        ret = ip4forward00000_merge_obj(g_stmt_async, i);
        EXPECT_EQ(ret, GMERR_OK);

        uint32_t value_u32 = 90 + i;
        if (i >= 70) {
            value_u32 = 10 + i;
        }
        ret = GmcSetVertexProperty(g_stmt_async, "attribute_id", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        CHECK_OK_RET(ret, "GmcSetVertexProperty attribute_id.");

        ret = GmcBatchAddDML(batch, g_stmt_async);  // GMC_CMD_MERGE_VERTEX);
        ASSERT_EQ(GMERR_OK, ret);
    }
    data = {0};
    ret = GmcBatchExecuteAsync(batch, BatchAsyncCallBack, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, ret);
    // EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(80, data.totalNum);
    EXPECT_EQ(70, data.succNum);

    // check dml_statis interface
    SET_BATCH_COUNT(data.totalNum, data.succNum);
    SET_COUNT(cn_merge_succ[0], cn_merge_fail[0]);
    ret = check_count(70, 1);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelName, NULL);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    printf(">> delete \n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    RESET_COUNT();
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < 80; i++) {
        ret = ip4forward00000_pri_key_set(g_stmt_async, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcSetIndexKeyName(g_stmt_async, "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcBatchAddDML(batch, g_stmt_async);  // GMC_CMD_DELETE_VERTEX);
        // ASSERT_EQ(GMERR_OK, ret);
    }
    data = {0};
    ret = GmcBatchExecuteAsync(batch, BatchAsyncCallBack, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, ret);
    // EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(80, data.totalNum);
    EXPECT_EQ(80, data.succNum);

    // check dml_statis interface
    SET_BATCH_COUNT(data.totalNum, data.succNum);
    SET_COUNT(cn_delete_succ[0], cn_delete_fail[0]);
    ret = check_count(80, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelName, NULL);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    // drop table
    printf(">> drop table \n");
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);

    // check dml_statis interface
    ret = dml_statis_check(stmt, g_labelName, NULL, GMERR_UNDEFINED_TABLE);
    EXPECT_EQ(ret, GMERR_UNDEFINED_TABLE);

    // check dml statis view
    ret = dml_sysview_query(NULL, g_labelName, NULL, NULL);
    EXPECT_NE(ret, 0);
    ret = check_dml_info_null(0);
    EXPECT_EQ(ret, 0);
}

// 010. 配置性能统计的开关参数为1，创建vertex表ip4forward，insert 1000条数据，
//      local索引删除501条， 不支持更新;
TEST_F(dml_oper_statis_test, dml_oper_statis_test_010)
{
    // 创建 vertexLabel_1
    char labelName[128] = "ip4forward00000";

    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    int ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    uint64_t count;

    // open vertexLabel
    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    printf(">> insert \n");
    RESET_COUNT();
    uint64_t i = 0;
    for (i = 0; i < 1000; i++) {
        ret = ip4forward00000_set_obj(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(stmt);
        CHECK_SET_COUNT(ret);
    }

    // check dml_statis interface
    SET_COUNT(cn_insert_succ[0], cn_insert_fail[0]);
    ret = check_count(1000, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, labelName, NULL);
    EXPECT_EQ(ret, 0);

    // check dml statis view
    ret = dml_sysview_query(NULL, g_labelName, NULL, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info(0);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    printf(">> delete by local key\n");
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);

    // local 区间删除[0, 500]
    uint32_t l_val_del = 0;
    uint32_t r_val_del = 500;
    uint32_t l_val_del_1 = 50;
    uint32_t r_val_del_1 = 1000;
    unsigned int arrLen = 2;

    GmcPropValueT *leftKeyProps_del = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    leftKeyProps_del[0].type = GMC_DATATYPE_UINT32;
    leftKeyProps_del[0].value = &l_val_del;
    leftKeyProps_del[0].size = sizeof(l_val_del);
    leftKeyProps_del[1].type = GMC_DATATYPE_UINT32;
    leftKeyProps_del[1].value = &l_val_del_1;
    leftKeyProps_del[1].size = sizeof(l_val_del_1);

    GmcPropValueT *rightKeyProps_del = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    rightKeyProps_del[0].type = GMC_DATATYPE_UINT32;
    rightKeyProps_del[0].value = &r_val_del;
    rightKeyProps_del[0].size = sizeof(r_val_del);
    rightKeyProps_del[1].type = GMC_DATATYPE_UINT32;
    rightKeyProps_del[1].value = &r_val_del_1;
    rightKeyProps_del[1].size = sizeof(r_val_del_1);

    GmcRangeItemT items[arrLen];
    items[0].lValue = &leftKeyProps_del[0];
    items[0].rValue = &rightKeyProps_del[0];
    items[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].order = GMC_ORDER_ASC;
    items[1].lValue = &leftKeyProps_del[1];
    items[1].rValue = &rightKeyProps_del[1];
    items[1].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items[1].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items[1].order = GMC_ORDER_ASC;

    // 设置删除区间  区间更新[0, 500]
    ret = GmcSetKeyRange(stmt, items, arrLen);
    EXPECT_EQ(GMERR_OK, ret);
    // ret = GmcSetRangeScanParams(stmt, GMC_RANGE_TYPE_CLOSED, GMC_RANGE_TYPE_CLOSED, GMC_ORDER_ASC);
    // EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, "ip4_local");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    free(leftKeyProps_del);
    free(rightKeyProps_del);

    // get affect row
    int affectRows = 0;
    uint32_t len = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(501, affectRows);
    printf("\n[INFO] [ local delete ] affect row: %d \n\n", affectRows);

    // // qry vertex
    // ret = GmcExecScanVertex(stmt, vertexLabel, "ip4_local");
    // EXPECT_EQ(GMERR_OK, ret);
    // for (unsigned int cnt = 0;; ++cnt) {
    //     bool isFinish;
    //     ret = GmcFetch(stmt, &isFinish);
    //     EXPECT_EQ(GMERR_OK, ret);
    //     if (isFinish) {
    //         //EXPECT_EQ(501, cnt);
    //         break;
    //     }

    //     uint32_t rd_primary_label = 0;
    //     uint32_t rd_attribute_id = 0;
    //     unsigned int isNull;
    //     ret = GmcGetVertexPropertyByName(stmt, "nhp_group_id", &rd_primary_label, sizeof(rd_primary_label), &isNull);
    //     ret = GmcGetVertexPropertyByName(stmt, "app_source_id", &rd_attribute_id, sizeof(rd_attribute_id), &isNull);
    //     printf(">>>local: %u\n", rd_attribute_id);
    // }

    // check dml_statis interface
    SET_BATCH_COUNT(1, 1);
    SET_COUNT(cn_delete_succ[0], cn_delete_fail[0]);
    ret = check_count(1, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelName, NULL);
    EXPECT_EQ(ret, 0);

    // check dml statis view
    ret = dml_sysview_query(g_labelName, g_labelName, NULL, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info(0);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 011. 配置性能统计的开关参数为1，创建vertex表ip4forward，insert 1000条数据，
//      localhash索引更新1000条，localhash索引删除1000条 -- 唯一删除
TEST_F(dml_oper_statis_test, dml_oper_statis_test_011)
{
    // 创建 vertexLabel_1
    char labelName[128] = "ip4forward00000";

    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    int ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    uint64_t count;

    // open vertexLabel
    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    printf(">> insert \n");
    RESET_COUNT();
    uint64_t i = 0;
    uint64_t j = 0;
    for (i = 0; i < 100; i++) {
        j = i % 90;
        ret = ip4forward00000_set_obj(stmt, j);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(stmt);
        CHECK_SET_COUNT(ret);
    }

    // check dml_statis interface
    SET_COUNT(cn_insert_succ[0], cn_insert_fail[0]);
    ret = check_count(90, 10);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, labelName, NULL);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    printf(">> update by localhash key\n");  // 更新不存在的key 当做成功；
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);
    int affectRowsSum = 0;
    RESET_COUNT();
    for (i = 0; i < 110; i++) {
        uint32_t uint_32 = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &uint_32, sizeof(uint_32));
        CHECK_OK_RET(ret, "GmcSetIndexKeyValue nhp_group_id.");
        uint32_t vrid = 0;

        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrid, sizeof(vrid));
        CHECK_OK_RET(ret, "GmcSetIndexKeyValue vr_id.");

        uint32_t value_u32 = i + 1;
        ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        CHECK_OK_RET(ret, "GmcSetVertexProperty primary_label.");

        value_u32 = 90 + i;
        if (i >= 70) {
            value_u32 = 10 + i;
        }
        ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        CHECK_OK_RET(ret, "GmcSetVertexProperty app_version.");

        ret = GmcSetIndexKeyName(stmt, "ip4_localhash");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        // if (ret){
        //     printf("nhp_group_id:%u app_version:%u ret:%u\n", uint_32, value_u32, ret);
        // }
        CHECK_SET_COUNT(ret);

        // get affect row
        int affectRows = 0;
        uint32_t len = 0;
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        affectRowsSum += affectRows;
    }

    printf("\n[INFO] [ localhash update ] affect row: %d \n\n", affectRowsSum);

    // check dml_statis interface
    SET_COUNT(cn_update_succ[0], cn_update_fail[0]);
    ret = check_count(90, 20);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelName, NULL);
    EXPECT_EQ(ret, 0);

    // check dml statis view
    ret = dml_sysview_query(g_labelName, g_labelName, NULL, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info(0);
    EXPECT_EQ(ret, 0);

    printf(">> delete by localhash key\n");
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    RESET_COUNT();
    affectRowsSum = 0;
    for (i = 0; i < 110; i++) {
        uint32_t uint_32 = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &uint_32, sizeof(uint_32));
        CHECK_OK_RET(ret, "GmcSetIndexKeyValue nhp_group_id.");
        uint32_t vrid = 0;

        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrid, sizeof(vrid));
        CHECK_OK_RET(ret, "GmcSetIndexKeyValue vr_id.");

        ret = GmcSetIndexKeyName(stmt, "ip4_localhash");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        CHECK_SET_COUNT(ret);

        // get affect row
        int affectRows = 0;
        uint32_t len = 0;
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        affectRowsSum += affectRows;
    }

    printf("\n[INFO] [ localhash delete ] affect row: %d \n\n", affectRowsSum);

    // check dml_statis interface
    SET_COUNT(cn_delete_succ[0], cn_delete_fail[0]);
    ret = check_count(110, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, labelName, NULL);
    EXPECT_EQ(ret, 0);

    // check dml statis view
    ret = dml_sysview_query(g_labelName, g_labelName, NULL, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info(0);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    // close vertexLabel
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 012. 配置性能统计的开关参数为1，创建vertex表ip4forward，insert 1000条数据，
//      localhash索引更新100条，localhash索引删除1000条 -- 非唯一删除
TEST_F(dml_oper_statis_test, dml_oper_statis_test_012)
{
    // 创建 vertexLabel_1
    char labelName[128] = "ip4forward00000";

    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    int ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    uint64_t count;

    // open vertexLabel
    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    printf(">> insert \n");
    RESET_COUNT();
    uint64_t i = 0;
    for (i = 0; i < 200; i++) {
        ret = ip4forward00000_set_obj(stmt, i, 100);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(stmt);
        CHECK_SET_COUNT(ret);
    }

    // check dml_statis interface
    SET_COUNT(cn_insert_succ[0], cn_insert_fail[0]);
    ret = check_count(200, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, labelName, NULL);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);
    // IP4FORWARD_SCAN();
    printf(">> update by localhash key\n");
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);
    int affectRowsSum = 0;
    RESET_COUNT();
    for (i = 0; i < 100; i++) {
        uint32_t uint_32 = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &uint_32, sizeof(uint_32));
        CHECK_OK_RET(ret, "GmcSetIndexKeyValue app_source_id.");
        uint32_t vrid = 0;

        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrid, sizeof(vrid));
        CHECK_OK_RET(ret, "GmcSetIndexKeyValue vr_id.");

        uint32_t value_u32 = i + 1;
        ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        CHECK_OK_RET(ret, "GmcSetVertexProperty primary_label.");

        // // 对应2条，违反唯一限制
        // value_u32 = 200+i;
        // if(i >= 80) {value_u32 = 900+i;}
        // ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        // CHECK_OK_RET(ret, "GmcSetVertexProperty app_version.");

        ret = GmcSetIndexKeyName(stmt, "ip4_localhash");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        CHECK_SET_COUNT(ret);

        // get affect row
        int affectRows = 0;
        uint32_t len = 0;
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        affectRowsSum += affectRows;
    }

    printf("\n[INFO] [ localhash update ] affect row: %d \n\n", affectRowsSum);

    // check dml_statis interface
    SET_COUNT(cn_update_succ[0], cn_update_fail[0]);
    ret = check_count(100, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelName, NULL);
    EXPECT_EQ(ret, 0);

    printf(">> delete by localhash key\n");
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    affectRowsSum = 0;
    RESET_COUNT();
    for (i = 0; i < 300; i++) {
        uint32_t uint_32 = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &uint_32, sizeof(uint_32));
        CHECK_OK_RET(ret, "GmcSetIndexKeyValue app_source_id.");
        uint32_t vrid = 0;

        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrid, sizeof(vrid));
        CHECK_OK_RET(ret, "GmcSetIndexKeyValue vr_id.");

        ret = GmcSetIndexKeyName(stmt, "ip4_localhash");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        CHECK_SET_COUNT(ret);

        // get affect row
        int affectRows = 0;
        uint32_t len = 0;
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        affectRowsSum += affectRows;
    }

    printf("\n[INFO] [ localhash delete ] affect row: %d \n\n", affectRowsSum);

    // check dml_statis interface
    SET_COUNT(cn_delete_succ[0], cn_delete_fail[0]);
    ret = check_count(300, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, labelName, NULL);
    EXPECT_EQ(ret, 0);

    // check dml statis view
    ret = dml_sysview_query(g_labelName, g_labelName, NULL, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info(0);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    // close vertexLabel
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 013. 配置性能统计的开关参数为1，创建vertex表ip4forward，insert 1000条数据，
//      hashcluster索引更新1000条，hashcluster索引删除1000条
TEST_F(dml_oper_statis_test, dml_oper_statis_test_013)
{
    // 创建 vertexLabel_1
    char labelName[128] = "ip4forward00000";

    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward000001.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    int ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    uint64_t count;

    // open vertexLabel
    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    printf(">> insert \n");
    RESET_COUNT();
    uint64_t i = 0;
    uint64_t j = 0;
    for (i = 0; i < 100; i++) {
        j = i % 90;
        ret = ip4forward00000_set_obj(stmt, j);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(stmt);
        CHECK_SET_COUNT(ret);
    }

    // check dml_statis interface
    SET_COUNT(cn_insert_succ[0], cn_insert_fail[0]);
    ret = check_count(90, 10);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, labelName, NULL);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    printf(">> update by hashcluser key\n");
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);
    int affectRowsSum = 0;
    RESET_COUNT();
    for (i = 0; i < 90; i++) {
        uint32_t uint_32 = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &uint_32, sizeof(uint_32));
        CHECK_OK_RET(ret, "GmcSetIndexKeyValue nhp_group_id.");

        uint32_t vrid = 0;
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrid, sizeof(vrid));
        CHECK_OK_RET(ret, "GmcSetIndexKeyValue vr_id.");

        uint32_t vrindex = 0;
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &vrindex, sizeof(vrindex));
        CHECK_OK_RET(ret, "GmcSetIndexKeyValue vrf_index.");

        uint32_t value_u32 = i + 1;
        ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        CHECK_OK_RET(ret, "GmcSetVertexProperty primary_label.");

        value_u32 = 90 + i;
        if (i >= 70) {
            value_u32 = 10 + i;
        }
        ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        CHECK_OK_RET(ret, "GmcSetVertexProperty app_version.");

        ret = GmcSetIndexKeyName(stmt, "ip4_vrfid_hashcluster");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        CHECK_SET_COUNT(ret);

        // get affect row
        int affectRows = 0;
        uint32_t len = 0;
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        affectRowsSum += affectRows;
    }

    printf("\n[INFO] [ hashcluster update ] affect row: %d \n\n", affectRowsSum);

    // check dml_statis interface
    SET_COUNT(cn_update_succ[0], cn_update_fail[0]);
    ret = check_count(70, 20);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelName, NULL);
    EXPECT_EQ(ret, 0);

    printf(">> delete by localhash key\n");
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    affectRowsSum = 0;
    RESET_COUNT();
    for (i = 0; i < 110; i++) {
        uint32_t uint_32 = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &uint_32, sizeof(uint_32));
        CHECK_OK_RET(ret, "GmcSetIndexKeyValue app_source_id.");
        uint32_t vrid = 0;

        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrid, sizeof(vrid));
        CHECK_OK_RET(ret, "GmcSetIndexKeyValue vr_id.");

        ret = GmcSetIndexKeyName(stmt, "ip4_localhash");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        CHECK_SET_COUNT(ret);

        // get affect row
        int affectRows = 0;
        uint32_t len = 0;
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        affectRowsSum += affectRows;
    }

    printf("\n[INFO] [ hashcluster delete ] affect row: %d \n\n", affectRowsSum);

    // check dml_statis interface
    SET_COUNT(cn_delete_succ[0], cn_delete_fail[0]);
    ret = check_count(110, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, labelName, NULL);
    EXPECT_EQ(ret, 0);

    // check dml statis view
    ret = dml_sysview_query(g_labelName, g_labelName, NULL, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info(0);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    // close vertexLabel
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 014. 配置性能统计的开关参数为1，创建vertex表ip4forward，insert 1000条数据，
//      hashcluster索引更新100条，hashcluster索引删除100条 -- 非唯一
TEST_F(dml_oper_statis_test, dml_oper_statis_test_014)
{
    // 创建 vertexLabel_1
    char labelName[128] = "ip4forward00000";

    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward000001.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    int ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    uint64_t count;

    // open vertexLabel
    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    printf(">> insert \n");
    RESET_COUNT();
    uint64_t i = 0;
    for (i = 0; i < 200; i++) {
        ret = ip4forward00000_set_obj(stmt, i, 100);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(stmt);
        CHECK_SET_COUNT(ret);
    }

    // check dml_statis interface
    SET_COUNT(cn_insert_succ[0], cn_insert_fail[0]);
    ret = check_count(200, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, labelName, NULL);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);
    // IP4FORWARD_SCAN();
    printf(">> update by hashcluser key\n");
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);
    int affectRowsSum = 0;
    RESET_COUNT();
    for (i = 0; i < 10; i++) {
        uint32_t uint_32 = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &uint_32, sizeof(uint_32));
        CHECK_OK_RET(ret, "GmcSetIndexKeyValue nhp_group_id.");

        uint32_t vrid = 0;
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrid, sizeof(vrid));
        CHECK_OK_RET(ret, "GmcSetIndexKeyValue vr_id.");

        uint32_t vrindex = 0;
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &vrindex, sizeof(vrindex));
        CHECK_OK_RET(ret, "GmcSetIndexKeyValue vrf_index.");

        uint32_t value_u32 = i + 1;
        ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        CHECK_OK_RET(ret, "GmcSetVertexProperty primary_label.");

        // value_u32 = 90+i;
        // if(i >= 70) {value_u32 = 10+i;}
        // ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        // CHECK_OK_RET(ret, "GmcSetVertexProperty app_version.");

        ret = GmcSetIndexKeyName(stmt, "ip4_vrfid_hashcluster");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        CHECK_SET_COUNT(ret);

        // get affect row
        int affectRows = 0;
        uint32_t len = 0;
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);

        affectRowsSum += affectRows;
    }

    printf("\n[INFO] [ hashcluster update ] affect row: %d \n\n", affectRowsSum);

    // check dml_statis interface
    SET_COUNT(cn_update_succ[0], cn_update_fail[0]);
    ret = check_count(10, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelName, NULL);
    EXPECT_EQ(ret, 0);
    // IP4FORWARD_SCAN();
    printf(">> delete by localhash key\n");
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    affectRowsSum = 0;
    RESET_COUNT();
    for (i = 0; i < 300; i++) {
        uint32_t uint_32 = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &uint_32, sizeof(uint_32));
        CHECK_OK_RET(ret, "GmcSetIndexKeyValue nhp_group_id.");
        uint32_t vrid = 0;

        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrid, sizeof(vrid));
        CHECK_OK_RET(ret, "GmcSetIndexKeyValue vr_id.");

        ret = GmcSetIndexKeyName(stmt, "ip4_localhash");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret) {
            printf("i:%u ret:%u\n", i, ret);
        }
        CHECK_SET_COUNT(ret);

        // get affect row
        int affectRows = 0;
        uint32_t len = 0;
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        affectRowsSum += affectRows;
    }

    printf("\n[INFO] [ localhash delete ] affect row: %d \n\n", affectRowsSum);
    //  IP4FORWARD_SCAN();
    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    // check dml_statis interface
    SET_COUNT(cn_delete_succ[0], cn_delete_fail[0]);
    ret = check_count(300, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, labelName, NULL);
    EXPECT_EQ(ret, 0);

    // check dml statis view
    ret = dml_sysview_query(g_labelName, g_labelName, NULL, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info(0);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 015. 配置性能统计的开关参数为1，创建vertex表ip4forward，insert 1000条数据，
//     Cond更新100条，Cond删除100条
TEST_F(dml_oper_statis_test, dml_oper_statis_test_015)
{
    // 创建 vertexLabel_1
    char labelName[128] = "ip4forward00000";

    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    int ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    uint64_t count;

    // open vertexLabel
    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    printf(">> insert \n");
    uint64_t i = 0;
    RESET_COUNT();
    for (i = 0; i < 1000; i++) {
        ret = ip4forward00000_set_obj(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(stmt);
        CHECK_SET_COUNT(ret);
    }

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    // check dml_statis interface
    SET_COUNT(cn_insert_succ[0], cn_insert_fail[0]);
    ret = check_count(1000, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, labelName, NULL);
    EXPECT_EQ(ret, 0);

    const char *cond1 = (const char *)"ip4forward00000.app_source_id<100 and ip4forward00000.app_source_id>=0";

    printf(">> update by cond\n");
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);
    int affectRowsSum = 0;
    RESET_COUNT();
    uint32_t value_u32 = i + 1;
    ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
    CHECK_OK_RET(ret, "GmcSetVertexProperty primary_label.");

    // ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
    // CHECK_OK_RET(ret, "GmcSetVertexProperty attribute_id.");

    ret = GmcSetFilter(stmt, cond1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    CHECK_SET_COUNT(ret);

    // get affect row
    int affectRows = 0;
    uint32_t len = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);

    printf("\n[INFO] [ cond update ] affect row: %d \n\n", affectRows);

    SET_COUNT(cn_update_succ[0], cn_update_fail[0]);
    ret = check_count(1, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, labelName, NULL);
    EXPECT_EQ(ret, 0);

    printf(">> delete by cond\n");
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    affectRowsSum = 0;
    RESET_COUNT();
    ret = GmcSetFilter(stmt, cond1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    CHECK_SET_COUNT(ret);
    // get affect row
    affectRows = 0;
    len = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);

    printf("\n[INFO] [ cond delete ] affect row: %d \n\n", affectRows);

    SET_COUNT(cn_delete_succ[0], cn_delete_fail[0]);
    ret = check_count(1, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, labelName, NULL);
    EXPECT_EQ(ret, 0);

    // check dml statis view
    ret = dml_sysview_query(g_labelName, g_labelName, NULL, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info(0);
    EXPECT_EQ(ret, 0);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

#define TREE_SCAN()                                                                          \
    do {                                                                                     \
        ret = GmcExecScanVertex(stmt, vertexLabel, NULL);                                    \
        EXPECT_EQ(GMERR_OK, ret);                                                            \
        bool isFinish = false;                                                               \
        unsigned int cnt = 0;                                                                \
        while (!isFinish) {                                                                  \
            ret = GmcFetch(stmt, &isFinish);                                                 \
            EXPECT_EQ(GMERR_OK, ret);                                                        \
            if (isFinish == true) {                                                          \
                break;                                                                       \
            }                                                                                \
            int64_t rd_F1 = 0;                                                               \
            uint32_t rd_F3 = 0;                                                              \
            unsigned int isNull;                                                             \
            ret = GmcGetVertexPropertyByName(stmt, "F0", &rd_F1, sizeof(int64_t), &isNull);  \
            ret = GmcGetVertexPropertyByName(stmt, "F3", &rd_F3, sizeof(uint32_t), &isNull); \
            printf("F1:%lld, F3:%u \n", rd_F1, rd_F3);                                       \
            cnt++;                                                                           \
        }                                                                                    \
        printf("scan cnt:%lu \n", cnt);                                                      \
        GmcResetVertex(stmt, false);                                                         \
    } while (0);

// 016. 配置性能统计的开关参数为1，创建复杂表vertex表dhp_config，同步insert 1000条数据，
//      异步update 1000条，同步批量replace1000条，异步批量merge 1000条，delete 1000条；
TEST_F(dml_oper_statis_test, dml_oper_statis_test_016)
{
    // 异步创建vertexLabel
    char *schema_json = NULL;
    readJanssonFile("./schema_file/Tree_Vector.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);

    AsyncUserDataT data = {0};
    int ret = GmcCreateVertexLabelAsync(g_stmt_async, schema_json, NULL, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(schema_json);

    // open vertex async
    void *vertexLabelAsync = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelNameTree, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // open vertexLabel
    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelNameTree, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t count;
    unsigned int totalNum;
    unsigned int successNum;
    char f14_value[8] = "string";
    int array_num = 1;
    int vector_num = 1;

    printf(">> insert sync\n");
    int i = 0;
    uint64_t j = 0;
    RESET_COUNT();
    for (i = 0; i < 100; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_labelNameTree, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        j = i % 90;
        Tree_Vector_set_obj(stmt, j, false, f14_value, array_num, vector_num);

        ret = GmcExecute(stmt);
        CHECK_SET_COUNT(ret);
    }

    // check dml_statis interface
    SET_COUNT(cn_insert_succ[0], cn_insert_fail[0]);
    ret = check_count(90, 10);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelNameTree, NULL);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelNameTree, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    printf(">> update async\n");
    RESET_COUNT();
    for (i = 0; i < 80; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelNameTree, GMC_OPERATION_UPDATE);
        ASSERT_EQ(GMERR_OK, ret);
        ret = Tree_Vector_pri_key_set(g_stmt_async, i);
        EXPECT_EQ(ret, GMERR_OK);

        uint32_t value_u32 = 90 + i;
        if (i >= 70) {
            value_u32 = 10 + i;
        }
        ret = GmcSetVertexProperty(g_stmt_async, "F3", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        CHECK_OK_RET(ret, "GmcSetVertexProperty F3.");

        data = {0};
        ret = GmcSetIndexKeyName(g_stmt_async, (char *)"Tree_Vector_PK");
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT updateRequestCtx;
        updateRequestCtx.updateCb = update_vertex_callback;
        updateRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &updateRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        CHECK_SET_COUNT(data.status);
    }

    // check dml_statis interface
    SET_COUNT(cn_update_succ[0], cn_update_fail[0]);
    ret = check_count(70, 10);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelNameTree, NULL);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelNameTree, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    printf(">> replace \n");
    RESET_COUNT();
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < 80; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_labelNameTree, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        Tree_Vector_set_obj(stmt, i, false, f14_value, array_num, vector_num);

        uint32_t value_u32 = 200 + i;
        if (i >= 70) {
            value_u32 = 10 + i;
        }
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        CHECK_OK_RET(ret, "GmcSetVertexProperty F3.");

        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    GmcBatchRetT batchRet;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_UNIQUE_VIOLATION, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);
    batch = NULL;

    SET_BATCH_COUNT(totalNum, successNum);
    SET_COUNT(cn_replace_succ[0], cn_replace_fail[0]);
    ret = check_count(70, 1);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelNameTree, NULL);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelNameTree, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    //  TREE_SCAN();
    printf(">> merage async \n");
    RESET_COUNT();
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (i = 0; i < 100; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelNameTree, GMC_OPERATION_MERGE);
        EXPECT_EQ(GMERR_OK, ret);
        Tree_Vector_merge_obj(g_stmt_async, i, false, f14_value, array_num, vector_num);

        uint32_t value_u32 = 200 + i;
        if (i >= 70) {
            value_u32 = 10 + i;
        }
        ret = GmcSetVertexProperty(g_stmt_async, "F3", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        CHECK_OK_RET(ret, "GmcSetVertexProperty F3.");

        ret = GmcBatchAddDML(batch, g_stmt_async);  // GMC_CMD_MERGE_VERTEX);
        ASSERT_EQ(GMERR_OK, ret);
    }
    data = {0};
    ret = GmcBatchExecuteAsync(batch, BatchAsyncCallBack, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, ret);

    SET_BATCH_COUNT(data.totalNum, data.succNum);

    SET_COUNT(cn_merge_succ[0], cn_merge_fail[0]);
    ret = check_count(70, 1);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelNameTree, NULL);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelNameTree, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    // check dml statis view
    ret = dml_sysview_query(NULL, g_labelNameTree, NULL, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info(0);
#ifdef DIRECT_WRITE
    EXPECT_EQ(ret, 1);
#else
    EXPECT_EQ(ret, 0);
#endif

    printf(">> delete \n");
    RESET_COUNT();
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < 100; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelNameTree, GMC_OPERATION_DELETE);
        ASSERT_EQ(GMERR_OK, ret);

        ret = Tree_Vector_pri_key_set(g_stmt_async, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcSetIndexKeyName(g_stmt_async, "Tree_Vector_PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_async);  // GMC_CMD_DELETE_VERTEX);
        ASSERT_EQ(GMERR_OK, ret);
    }

    data = {0};
    ret = GmcBatchExecuteAsync(batch, BatchAsyncCallBack, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(100, data.totalNum);
    EXPECT_EQ(100, data.succNum);

    // check dml_statis interface
    SET_BATCH_COUNT(data.totalNum, data.succNum);
    SET_COUNT(cn_delete_succ[0], cn_delete_fail[0]);
    ret = check_count(100, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelNameTree, NULL);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelNameTree, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    ret = GmcGetVertexCount(stmt, g_labelNameTree, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    GmcFreeStmt(g_stmt_async);
    g_stmt_async = NULL;

    ret = GmcDropVertexLabel(stmt, g_labelNameTree);
    ASSERT_EQ(GMERR_OK, ret);
}

// 017. 配置性能统计的开关参数为1，创建复杂表vertex表dhp_config， insert 1000条数据，
//      增加字节点数据，更新子节点数据，删除某个record子节点数据
TEST_F(dml_oper_statis_test, dml_oper_statis_test_017)
{
    // 异步创建vertexLabel
    char *schema_json = NULL;
    readJanssonFile("./schema_file/Tree_Vector.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);

    AsyncUserDataT data = {0};
    int ret = GmcCreateVertexLabelAsync(g_stmt_async, schema_json, NULL, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(schema_json);

    // open vertex async
    void *vertexLabelAsync = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelNameTree, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // open vertexLabel
    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelNameTree, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t count;
    unsigned int totalNum;
    unsigned int successNum;
    char f14_value[8] = "string";
    int array_num = 2;
    int vector_num = 2;

    printf(">> insert sync\n");
    RESET_COUNT();
    int i = 0;
    for (i = 0; i < 100; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_labelNameTree, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        Tree_Vector_set_obj(stmt, i, false, f14_value, array_num, vector_num);

        ret = GmcExecute(stmt);
        CHECK_SET_COUNT(ret);
    }

    // check dml_statis interface
    SET_COUNT(cn_insert_succ[0], cn_insert_fail[0]);
    ret = check_count(100, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelNameTree, NULL);
    EXPECT_EQ(ret, 0);

    printf(">> update node\n");
    RESET_COUNT();
    for (int i = 0; i < 100; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_labelNameTree, GMC_OPERATION_UPDATE);
        ASSERT_EQ(GMERR_OK, ret);

        // 增量更新vector节点
        // update
        int newValue = i + 100;
        uint32_t updateindex = 0;
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        GmcNodeT *root;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);

        GmcNodeT *node_T3;
        ret = GmcNodeGetChild(root, (char *)"T3", &node_T3);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcNodeGetElementByIndex(node_T3, updateindex, &node_T3);
        EXPECT_EQ(GMERR_OK, ret);

        TestGmcSetNodePropertyByName_V(node_T3, newValue, 0, f14_value);

        ret = GmcSetIndexKeyName(stmt, "Tree_Vector_PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);

        CHECK_SET_COUNT(ret);
    }

    // check dml_statis interface
    SET_COUNT(cn_update_succ[0], cn_update_fail[0]);
    ret = check_count(100, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelNameTree, NULL);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelNameTree, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    // check dml statis view
    ret = dml_sysview_query(NULL, g_labelName, NULL, NULL);
    EXPECT_NE(ret, 0);
    ret = check_dml_info_null(0);
    EXPECT_EQ(ret, 0);

    printf(">> delete node -- update\n");
    for (int i = 0; i < 100; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_labelNameTree, GMC_OPERATION_UPDATE);
        ASSERT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(stmt, "Tree_Vector_PK");
        EXPECT_EQ(GMERR_OK, ret);

        // remove,  vector节点
        uint32_t updateindex = 1;
        ret = TestSetVectorRemoveIndex(stmt, (char *)"T3", updateindex);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(stmt, "Tree_Vector_PK");
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(stmt);
        CHECK_SET_COUNT(ret);
    }

    // check dml_statis interface
    SET_COUNT(cn_update_succ[0], cn_update_fail[0]);
    ret = check_count(200, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelNameTree, NULL);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelNameTree, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    printf(">> clear node\n");
    // RESET_COUNT();
    for (int i = 0; i < 100; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_labelNameTree, GMC_OPERATION_UPDATE);
        ASSERT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(stmt, "Tree_Vector_PK");
        EXPECT_EQ(GMERR_OK, ret);
        // clear vector节点
        GmcNodeT *root;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *node_T3;
        ret = GmcNodeGetChild(root, "T3", &node_T3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeClear(node_T3);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(stmt);
        CHECK_SET_COUNT(ret);
    }

    // check dml_statis interface
    SET_COUNT(cn_update_succ[0], cn_update_fail[0]);
    ret = check_count(300, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelNameTree, NULL);
    EXPECT_EQ(ret, 0);

    // check dml statis view
    ret = dml_sysview_query(NULL, g_labelName, NULL, NULL);
    EXPECT_NE(ret, 0);
    ret = check_dml_info_null(0);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelNameTree, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    ret = GmcGetVertexCount(stmt, g_labelNameTree, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    ret = GmcDropVertexLabel(stmt, g_labelNameTree);
    ASSERT_EQ(GMERR_OK, ret);
}

// 018.配置性能统计的开关参数为1，Name_space 1,创建vertex表ip4forward，Name_space 2 ,创建vertex表ip4forward；
// 分别对两个表做，insert 1000条数据，hashcluster索引更新1000条，hashcluster索引删除1000条
const char *g_nameSpace_userName = (const char *)"abc";
TEST_F(dml_oper_statis_test, dml_oper_statis_test_018)
{
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    // 创建 vertexLabel_1
    char labelName[128] = "ip4forward00000";

    const char *name1 = (const char *)"user011_a";
    const char *name2 = (const char *)"user011_b";

    GmcDropNamespace(stmt, name1);
    GmcDropNamespace(stmt, name2);
    int ret = GmcCreateNamespace(stmt, name1, g_nameSpace_userName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateNamespace(stmt, name2, g_nameSpace_userName);
    EXPECT_EQ(GMERR_OK, ret);
    // 服务器启动成功后，会自动创建一个全局KV表（表名为T_GMDB）；此外，每新创建一个NameSpace时，也会在该NameSpace下自动创建一个全局KV表（表名为T_GMDB）
    // namespace id
    char viewCmd[100] = "CATA_NAMESPACE_INFO -f NAMESPACE_NAME=YangCheckDFX";
    (void)sprintf(viewCmd, "CATA_NAMESPACE_INFO -f NAMESPACE_NAME=%s", name1);
    int name1Value = GetViewValueByField(viewCmd, "NAMESPACE_ID");

    (void)sprintf(viewCmd, "CATA_NAMESPACE_INFO -f NAMESPACE_NAME=%s", name2);
    int name2Value = GetViewValueByField(viewCmd, "NAMESPACE_ID");
    (void)sprintf(viewCmd, "gmsysview -q V\\$CATA_KV_TABLE_INFO -f BASE_LABEL/NAMESPACE_ID=%d", name1Value);
    ret = executeCommand(viewCmd, "T_GMDB");
    EXPECT_EQ(GMERR_OK, ret);

    (void)sprintf(viewCmd, "gmsysview -q V\\$CATA_KV_TABLE_INFO -f BASE_LABEL/NAMESPACE_ID=%d", name2Value);
    ret = executeCommand(viewCmd, "T_GMDB");
    EXPECT_EQ(GMERR_OK, ret);

    // namespace_1
    ret = GmcUseNamespace(stmt, name1);
    EXPECT_EQ(GMERR_OK, ret);

    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward000001.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);

    // namespace_2
    ret = GmcUseNamespace(stmt, name2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    uint64_t count;

    // namespace_1
    ret = GmcUseNamespace(stmt, name1);
    EXPECT_EQ(GMERR_OK, ret);
    printf(">> namespace_1 \n");

    // open vertexLabel
    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    printf(">> insert \n");
    uint64_t i = 0;
    RESET_COUNT();
    for (i = 0; i < 100; i++) {
        ret = ip4forward00000_set_obj(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(stmt);
        CHECK_SET_COUNT(ret);
    }

    // check dml_statis interface
    SET_COUNT(cn_insert_succ[0], cn_insert_fail[0]);
    ret = check_count(100, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, labelName, NULL);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    printf(">> update by hashcluser key\n");
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);
    RESET_COUNT();
    for (i = 0; i < 1000; i++) {
        uint32_t uint_32 = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &uint_32, sizeof(uint_32));
        CHECK_OK_RET(ret, "GmcSetIndexKeyValue app_source_id.");

        uint32_t vrid = 0;
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrid, sizeof(vrid));
        CHECK_OK_RET(ret, "GmcSetIndexKeyValue vr_id.");

        uint32_t vrindex = 0;
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &vrindex, sizeof(vrindex));
        CHECK_OK_RET(ret, "GmcSetIndexKeyValue vrf_index.");

        uint32_t value_u32 = i + 1;
        ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        CHECK_OK_RET(ret, "GmcSetVertexProperty primary_label.");

        // ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        // CHECK_OK_RET(ret, "GmcSetVertexProperty attribute_id.");

        ret = GmcSetIndexKeyName(stmt, "ip4_vrfid_hashcluster");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        CHECK_SET_COUNT(ret);
    }

    // check dml_statis interface
    SET_COUNT(cn_update_succ[0], cn_update_fail[0]);
    ret = check_count(1000, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelName, NULL);
    EXPECT_EQ(ret, 0);

    printf(">> delete by localhash key\n");
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    RESET_COUNT();
    for (i = 0; i < 1000; i++) {
        uint32_t uint_32 = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &uint_32, sizeof(uint_32));
        CHECK_OK_RET(ret, "GmcSetIndexKeyValue app_source_id.");
        uint32_t vrid = 0;

        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrid, sizeof(vrid));
        CHECK_OK_RET(ret, "GmcSetIndexKeyValue vr_id.");

        ret = GmcSetIndexKeyName(stmt, "ip4_localhash");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        CHECK_SET_COUNT(ret);
    }

    // check dml_statis interface
    SET_COUNT(cn_delete_succ[0], cn_delete_fail[0]);
    ret = check_count(1000, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, labelName, NULL);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    // ret = GmcDropVertexLabel(stmt, g_labelName);
    // ASSERT_EQ(GMERR_OK, ret);

    // namespace_2
    printf(">> namespace_2 \n");
    ret = GmcUseNamespace(stmt, name2);
    EXPECT_EQ(GMERR_OK, ret);

    clear_count();

    // open vertexLabel
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    printf(">> insert \n");
    i = 0;
    RESET_COUNT();
    for (i = 0; i < 200; i++) {
        ret = ip4forward00000_set_obj(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(stmt);
        CHECK_SET_COUNT(ret);
    }

    // check dml_statis interface
    SET_COUNT(cn_insert_succ[0], cn_insert_fail[0]);
    ret = check_count(200, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, labelName, NULL);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    printf(">> update by hashcluser key\n");
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);
    RESET_COUNT();
    for (i = 0; i < 1000; i++) {
        uint32_t uint_32 = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &uint_32, sizeof(uint_32));
        CHECK_OK_RET(ret, "GmcSetIndexKeyValue app_source_id.");

        uint32_t vrid = 0;
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrid, sizeof(vrid));
        CHECK_OK_RET(ret, "GmcSetIndexKeyValue vr_id.");

        uint32_t vrindex = 0;
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &vrindex, sizeof(vrindex));
        CHECK_OK_RET(ret, "GmcSetIndexKeyValue vrf_index.");

        uint32_t value_u32 = i + 1;
        ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        CHECK_OK_RET(ret, "GmcSetVertexProperty primary_label.");

        // ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        // CHECK_OK_RET(ret, "GmcSetVertexProperty attribute_id.");

        ret = GmcSetIndexKeyName(stmt, "ip4_vrfid_hashcluster");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        CHECK_SET_COUNT(ret);
    }

    // check dml_statis interface
    SET_COUNT(cn_update_succ[0], cn_update_fail[0]);
    ret = check_count(1000, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelName, NULL);
    EXPECT_EQ(ret, 0);

    printf(">> delete by localhash key\n");
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    RESET_COUNT();
    for (i = 0; i < 2000; i++) {
        uint32_t uint_32 = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &uint_32, sizeof(uint_32));
        CHECK_OK_RET(ret, "GmcSetIndexKeyValue app_source_id.");
        uint32_t vrid = 0;

        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrid, sizeof(vrid));
        CHECK_OK_RET(ret, "GmcSetIndexKeyValue vr_id.");

        ret = GmcSetIndexKeyName(stmt, "ip4_localhash");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        CHECK_SET_COUNT(ret);
    }

    // check dml_statis interface
    SET_COUNT(cn_delete_succ[0], cn_delete_fail[0]);
    ret = check_count(2000, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, labelName, NULL);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    // check dml statis view
    ret = dml_sysview_query(NULL, g_labelName, g_labelName, NULL);
    EXPECT_EQ(ret, 0);
    set_count_value(100, 1000, 1000);
    ret = check_dml_info(1);
    int ret1 = 0;
    ret1 = check_dml_info(0);
    if (ret != 0 && ret1 != 0) {
        EXPECT_EQ(1, 0);
    }

    set_count_value(200, 2000, 1000);
    ret = check_dml_info(1);
    ret1 = check_dml_info(0);
    if (ret != 0 && ret1 != 0) {
        EXPECT_EQ(1, 0);
    }

    // check dml statis view
    ret = dml_sysview_query(g_labelName, g_labelName, g_labelName, NULL);
    EXPECT_EQ(ret, 0);
    set_count_value(100, 1000, 1000);
    ret = check_dml_info(1);
    ret1 = check_dml_info(0);
    if (ret != 0 && ret1 != 0) {
        EXPECT_EQ(1, 0);
    }

    set_count_value(200, 2000, 1000);
    ret = check_dml_info(1);
    ret1 = check_dml_info(0);
    if (ret != 0 && ret1 != 0) {
        EXPECT_EQ(1, 0);
    }

    // name1
    ret = GmcUseNamespace(stmt, name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, g_labelName);
    EXPECT_EQ(GMERR_OK, ret);

    // name2
    ret = GmcUseNamespace(stmt, name2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, g_labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropNamespace(stmt, name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropNamespace(stmt, name2);
    EXPECT_EQ(GMERR_OK, ret);
}

// 019. 配置性能统计的开关参数为1，Name_space 1,创建复杂vertex表dhp_config，Name_space 2,
//     创建复杂vertex表dhp_config；分别对两个表做 同步insert 1000条数据，异步update 1000条，
//     同步批量replace1000条，异步批量merge 1000条，delete 1000条；
TEST_F(dml_oper_statis_test, dml_oper_statis_test_019)
{
    AsyncUserDataT data = {0};
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    const char *name1 = (const char *)"user011_a";
    const char *name2 = (const char *)"user011_b";
    system("gmadmin -cfgName enableDmlPerfStat -cfgVal 1");

    GmcDropNamespace(stmt, name1);
    GmcDropNamespace(stmt, name2);
    int ret = GmcCreateNamespace(stmt, name1, g_nameSpace_userName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateNamespace(stmt, name2, g_nameSpace_userName);
    EXPECT_EQ(GMERR_OK, ret);

    // namespace_1
    ret = GmcUseNamespace(stmt, name1);
    EXPECT_EQ(GMERR_OK, ret);

    char *schema_json = NULL;
    readJanssonFile("./schema_file/Tree_Vector.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);

    // namespace_2
    ret = GmcUseNamespace(stmt, name2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // namespace_1
    ret = GmcUseNamespaceAsync(g_stmt_async, name1, use_namespace_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    printf("--- namespace_1 async \n");

    ret = GmcUseNamespace(stmt, name1);
    EXPECT_EQ(GMERR_OK, ret);
    printf("--- namespace_1 \n");

    // open vertex async
    void *vertexLabelAsync = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelNameTree, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // open vertexLabel
    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelNameTree, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t count;
    unsigned int totalNum;
    unsigned int successNum;
    char f14_value[8] = "string";
    int array_num = 1;
    int vector_num = 1;

    printf(">> insert sync\n");
    RESET_COUNT();
    int i = 0;
    for (i = 0; i < 100; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_labelNameTree, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);

        Tree_Vector_set_obj(stmt, i, false, f14_value, array_num, vector_num);

        ret = GmcExecute(stmt);
        CHECK_SET_COUNT(ret);
    }

    // check dml_statis interface
    SET_COUNT(cn_insert_succ[0], cn_insert_fail[0]);
    ret = check_count(100, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelNameTree, NULL);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelNameTree, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    printf(">> update async\n");
    RESET_COUNT();
    for (i = 0; i < 200; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelNameTree, GMC_OPERATION_UPDATE);
        ASSERT_EQ(GMERR_OK, ret);

        ret = Tree_Vector_pri_key_set(g_stmt_async, i);
        EXPECT_EQ(ret, GMERR_OK);

        uint32_t value_u32 = i + 1000;
        ret = GmcSetVertexProperty(g_stmt_async, "F3", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        CHECK_OK_RET(ret, "GmcSetVertexProperty F3.");

        data = {0};
        ret = GmcSetIndexKeyName(g_stmt_async, (char *)"Tree_Vector_PK");
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT updateRequestCtx;
        updateRequestCtx.updateCb = update_vertex_callback;
        updateRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &updateRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        CHECK_SET_COUNT(data.status);
    }

    // check dml_statis interface
    SET_COUNT(cn_update_succ[0], cn_update_fail[0]);
    ret = check_count(200, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelNameTree, NULL);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelNameTree, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    printf(">> replace \n");
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelNameTree, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    RESET_COUNT();
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 100; i < 200; i++) {
        Tree_Vector_set_obj(stmt, i, false, f14_value, array_num, vector_num);

        ret = GmcBatchAddDML(batch, stmt);  // ,GMC_CMD_REPLACE_VERTEX);
        ASSERT_EQ(GMERR_OK, ret);

        GmcResetVertex(stmt, true);
    }
    GmcBatchRetT batchRet;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);
    batch = NULL;
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(100, totalNum);
    EXPECT_EQ(100, successNum);

    // check dml_statis interface
    SET_BATCH_COUNT(totalNum, successNum);
    SET_COUNT(cn_replace_succ[0], cn_replace_fail[0]);
    ret = check_count(100, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelNameTree, NULL);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelNameTree, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    printf(">> merage async \n");
    RESET_COUNT();
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelNameTree, GMC_OPERATION_MERGE);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (i = 100; i < 300; i++) {
        Tree_Vector_merge_obj(g_stmt_async, i, false, f14_value, array_num, vector_num);

        ret = GmcBatchAddDML(batch, g_stmt_async);  // GMC_CMD_MERGE_VERTEX);
        ASSERT_EQ(GMERR_OK, ret);

        GmcResetVertex(g_stmt_async, true);
    }
    data = {0};
    ret = GmcBatchExecuteAsync(batch, BatchAsyncCallBack, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(200, data.totalNum);
    EXPECT_EQ(200, data.succNum);

    // check dml_statis interface
    SET_BATCH_COUNT(data.totalNum, data.succNum);
    SET_COUNT(cn_merge_succ[0], cn_merge_fail[0]);
    ret = check_count(200, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelNameTree, NULL);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelNameTree, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    printf(">> delete \n");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelNameTree, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (i = 100; i < 200; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelNameTree, GMC_OPERATION_DELETE);
        ASSERT_EQ(GMERR_OK, ret);

        ret = Tree_Vector_pri_key_set(g_stmt_async, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcSetIndexKeyName(g_stmt_async, "Tree_Vector_PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_async);  // GMC_CMD_DELETE_VERTEX);
        ASSERT_EQ(GMERR_OK, ret);
    }

    data = {0};
    ret = GmcBatchExecuteAsync(batch, BatchAsyncCallBack, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(100, data.totalNum);
    EXPECT_EQ(100, data.succNum);

    // check dml_statis interface
    SET_BATCH_COUNT(data.totalNum, data.succNum);
    SET_COUNT(cn_delete_succ[0], cn_delete_fail[0]);
    ret = check_count(100, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelNameTree, NULL);
    EXPECT_EQ(ret, 0);

    // check dml statis view
    ret = dml_sysview_query(NULL, g_labelNameTree, g_labelNameTree, NULL);
    EXPECT_EQ(ret, 0);
    int ret1 = 0;
    ret = check_dml_info(1);
    ret1 = check_dml_info(0);
    if (ret != 0 && ret1 != 0) {
        EXPECT_EQ(1, 0);
    }

    ret = GmcGetVertexCount(stmt, g_labelNameTree, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    // close vertexLabel
    ASSERT_EQ(GMERR_OK, ret);

    // close vertexLabel
    ASSERT_EQ(GMERR_OK, ret);

    // 清空数据
    ret = GmcGetVertexCount(stmt, g_labelNameTree, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    // namespace_1
    printf("--- namespace_2 \n");
    ret = GmcUseNamespace(stmt, name2);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUseNamespaceAsync(g_stmt_async, name2, use_namespace_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    printf("--- namespace_2 async \n");
    clear_count();
    // open vertex async
    vertexLabelAsync = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelNameTree, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // open vertexLabel
    vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelNameTree, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    printf(">> insert sync\n");
    RESET_COUNT();
    for (i = 0; i < 200; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_labelNameTree, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);

        Tree_Vector_set_obj(stmt, i, false, f14_value, array_num, vector_num);

        ret = GmcExecute(stmt);
        CHECK_SET_COUNT(ret);
    }

    // check dml_statis interface
    SET_COUNT(cn_insert_succ[0], cn_insert_fail[0]);
    ret = check_count(200, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelNameTree, NULL);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelNameTree, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    printf(">> update async\n");
    RESET_COUNT();
    for (i = 0; i < 200; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelNameTree, GMC_OPERATION_UPDATE);
        ASSERT_EQ(GMERR_OK, ret);

        ret = Tree_Vector_pri_key_set(g_stmt_async, i);
        EXPECT_EQ(ret, GMERR_OK);

        uint32_t value_u32 = i + 1000;
        ret = GmcSetVertexProperty(g_stmt_async, "F3", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        CHECK_OK_RET(ret, "GmcSetVertexProperty F3.");

        data = {0};
        ret = GmcSetIndexKeyName(g_stmt_async, (char *)"Tree_Vector_PK");
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT updateRequestCtx;
        updateRequestCtx.updateCb = update_vertex_callback;
        updateRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &updateRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        CHECK_SET_COUNT(data.status);
    }
    // check dml_statis interface
    SET_COUNT(cn_update_succ[0], cn_update_fail[0]);
    ret = check_count(200, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelNameTree, NULL);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelNameTree, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    printf(">> replace \n");
    RESET_COUNT();
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (i = 100; i < 200; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_labelNameTree, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);

        Tree_Vector_set_obj(stmt, i, false, f14_value, array_num, vector_num);

        ret = GmcBatchAddDML(batch, stmt);  // ,GMC_CMD_REPLACE_VERTEX);
        ASSERT_EQ(GMERR_OK, ret);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);
    batch = NULL;
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(100, totalNum);
    EXPECT_EQ(100, successNum);

    // check dml_statis interface
    SET_BATCH_COUNT(totalNum, successNum);
    SET_COUNT(cn_replace_succ[0], cn_replace_fail[0]);
    ret = check_count(100, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelNameTree, NULL);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelNameTree, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    printf(">> merage async \n");
    RESET_COUNT();
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (i = 100; i < 300; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelNameTree, GMC_OPERATION_MERGE);
        EXPECT_EQ(GMERR_OK, ret);

        Tree_Vector_merge_obj(g_stmt_async, i, false, f14_value, array_num, vector_num);

        ret = GmcBatchAddDML(batch, g_stmt_async);  // GMC_CMD_MERGE_VERTEX);
        ASSERT_EQ(GMERR_OK, ret);
    }
    data = {0};
    ret = GmcBatchExecuteAsync(batch, BatchAsyncCallBack, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(200, data.totalNum);
    EXPECT_EQ(200, data.succNum);

    // check dml_statis interface
    SET_BATCH_COUNT(data.totalNum, data.succNum);
    SET_COUNT(cn_merge_succ[0], cn_merge_fail[0]);
    ret = check_count(200, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelNameTree, NULL);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelNameTree, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    printf(">> delete \n");
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (i = 100; i < 200; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelNameTree, GMC_OPERATION_DELETE);
        ASSERT_EQ(GMERR_OK, ret);
        ret = Tree_Vector_pri_key_set(g_stmt_async, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcSetIndexKeyName(g_stmt_async, "Tree_Vector_PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_async);  // GMC_CMD_DELETE_VERTEX);
        ASSERT_EQ(GMERR_OK, ret);
    }

    data = {0};
    ret = GmcBatchExecuteAsync(batch, BatchAsyncCallBack, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(100, data.totalNum);
    EXPECT_EQ(100, data.succNum);

    // check dml_statis interface
    SET_BATCH_COUNT(data.totalNum, data.succNum);
    SET_COUNT(cn_delete_succ[0], cn_delete_fail[0]);
    ret = check_count(100, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, g_labelNameTree, NULL);
    EXPECT_EQ(ret, 0);

    // check dml statis view
    ret = dml_sysview_query(NULL, g_labelNameTree, g_labelNameTree, NULL);
    EXPECT_EQ(ret, 0);
// 不同环境表顺序不同
#ifdef ENV_RTOSV2X
    ret = check_dml_info(0);
    ret1 = check_dml_info(1);

#else
    ret = check_dml_info(1);
    ret1 = check_dml_info(0);
#endif
    if (ret != 0 && ret1 != 0) {
        EXPECT_EQ(1, 0);
    }

    ret = GmcGetVertexCount(stmt, g_labelNameTree, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    // close vertexLabel
    ASSERT_EQ(GMERR_OK, ret);

    // close vertexLabel
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcGetVertexCount(stmt, g_labelNameTree, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    // check dml statis view
    ret = dml_sysview_query(g_labelNameTree, g_labelNameTree, g_labelNameTree, NULL);
    EXPECT_EQ(ret, 0);
    set_count_value(200, 100, 200, 100, 200);
    ret = check_dml_info(0);
    ret1 = check_dml_info(1);
    if (ret != 0 && ret1 != 0) {
        EXPECT_EQ(1, 0);
    }

    set_count_value(100, 100, 200, 100, 200);
    ret = check_dml_info(1);
    ret1 = check_dml_info(0);
    if (ret != 0 && ret1 != 0) {
        EXPECT_EQ(1, 0);
    }

    // name1
    ret = GmcUseNamespace(stmt, name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, g_labelNameTree);
    EXPECT_EQ(GMERR_OK, ret);

    // name2
    ret = GmcUseNamespace(stmt, name2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, g_labelNameTree);
    EXPECT_EQ(GMERR_OK, ret);
    // check dml statis view
    ret = dml_sysview_query(g_labelNameTree, g_labelNameTree, g_labelNameTree, NULL);
    EXPECT_EQ(ret, 256);
    ret = check_dml_info_null(0);
    EXPECT_EQ(ret, 0);

    ret = GmcDropNamespace(stmt, name1);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropNamespace(stmt, name2);
    EXPECT_EQ(GMERR_OK, ret);
    system("gmadmin -cfgName enableDmlPerfStat -cfgVal 0");
}

// 020. 配置性能统计的开关参数为1，创建vertex表ip4forward；开启事务，insert 10条，update 10条，
//      replace10条，merge 10条，delete 10条；commit
TEST_F(dml_oper_statis_test, dml_oper_statis_test_020)
{
    // 创建 vertexLabel_1
    char labelName[128] = "ip4forward00000";

    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";

    int ret = GmcCreateVertexLabel(stmt, schema_json, Label_config);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    uint64_t count;

    // open vertexLabel
    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    // 开启事务
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_PESSIMISITIC_TRX;
    ret = GmcTransStart(conn, &config);
    ASSERT_EQ(GMERR_OK, ret);

    printf(">> insert \n");
    uint64_t i = 0;
    uint64_t j = 0;
    RESET_COUNT();
    for (i = 0; i < 100; i++) {
        j = i % 100;  // 有失败，则事务失败；
        ret = ip4forward00000_set_obj(stmt, j);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(stmt);
        CHECK_SET_COUNT(ret);
    }

    // check dml_statis interface
    SET_COUNT(cn_insert_succ[0], cn_insert_fail[0]);
    ret = check_count(100, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, labelName, NULL);
    EXPECT_EQ(ret, 0);

    // 可以扫描，不能直连读；
    // ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    // EXPECT_EQ(GMERR_OK, ret);
    // printf(" count:%llu \n", count);

    printf(">> update \n");
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);
    RESET_COUNT();
    for (i = 0; i < 80; i++) {
        ret = ip4forward00000_pri_key_set(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        // uint32_t value_u32 = 90+i;
        // if(i >= 70) {value_u32 = 10+i;}
        // ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        // CHECK_OK_RET(ret, "GmcSetVertexProperty app_version.");

        ret = GmcSetIndexKeyName(stmt, "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        CHECK_SET_COUNT(ret);
    }

    // check dml_statis interface
    SET_COUNT(cn_update_succ[0], cn_update_fail[0]);
    ret = check_count(80, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, labelName, NULL);
    EXPECT_EQ(ret, 0);

    // ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    // ASSERT_EQ(GMERR_OK, ret);
    // printf(" count:%llu \n", count);

    printf(">> replace \n");
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    RESET_COUNT();
    for (i = 0; i < 80; i++) {
        ret = ip4forward00000_set_obj(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        // uint64_t value_u64 = 90+i;
        // if(i >= 70) {value_u64 = 10+i;}
        // ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &value_u64, sizeof(value_u64));
        // CHECK_OK_RET(ret, "GmcSetVertexProperty app_obj_id.");

        ret = GmcExecute(stmt);
        CHECK_SET_COUNT(ret);
    }

    // check dml_statis interface
    SET_COUNT(cn_replace_succ[0], cn_replace_fail[0]);
    ret = check_count(80, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, labelName, NULL);
    EXPECT_EQ(ret, 0);

    // ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    // ASSERT_EQ(GMERR_OK, ret);
    // printf(" count:%llu \n", count);

    printf(">> merge \n");
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_MERGE);
    ASSERT_EQ(GMERR_OK, ret);
    RESET_COUNT();
    for (i = 0; i < 80; i++) {
        ret = ip4forward00000_merge_obj(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        // uint32_t value_u32 = 90+i;
        // if(i >= 70) {value_u32 = 10+i;}
        // ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        // CHECK_OK_RET(ret, "GmcSetVertexProperty attribute_id.");

        ret = GmcExecute(stmt);
        CHECK_SET_COUNT(ret);
    }

    // ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    // ASSERT_EQ(GMERR_OK, ret);
    // printf(" count:%llu \n", count);

    // check dml_statis interface
    SET_COUNT(cn_merge_succ[0], cn_merge_fail[0]);
    ret = check_count(80, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, labelName, NULL);
    EXPECT_EQ(ret, 0);

    // 提交事务
    ret = GmcTransCommit(conn);
    ASSERT_EQ(GMERR_OK, ret);

    // check dml statis view
    ret = dml_sysview_query(NULL, g_labelName, NULL, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info(0);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    EXPECT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 021. 配置性能统计的开关参数为1，创建vertex表ip4forward；开启事务，insert 10条，update 10条，
//      replace10条，merge 10条，delete 10条；rollback
TEST_F(dml_oper_statis_test, dml_oper_statis_test_021)
{
    // 创建 vertexLabel_1
    char labelName[128] = "ip4forward00000";

    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";

    int ret = GmcCreateVertexLabel(stmt, schema_json, Label_config);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    uint64_t count;

    // open vertexLabel
    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    // 开启事务
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_PESSIMISITIC_TRX;
    ret = GmcTransStart(conn, &config);
    ASSERT_EQ(GMERR_OK, ret);

    printf(">> insert \n");
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t i = 0;
    uint64_t j = 0;
    RESET_COUNT();
    for (i = 0; i < 100; i++) {
        j = i % 100;  // 有失败，则事务失败；
        ret = ip4forward00000_set_obj(stmt, j);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(stmt);
        CHECK_SET_COUNT(ret);
    }

    // check dml_statis interface
    SET_COUNT(cn_insert_succ[0], cn_insert_fail[0]);
    ret = check_count(100, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, labelName, NULL);
    EXPECT_EQ(ret, 0);

    // 可以扫描，不能直连读；
    // ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    // EXPECT_EQ(GMERR_OK, ret);
    // printf(" count:%llu \n", count);

    printf(">> update \n");
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);
    RESET_COUNT();
    for (i = 0; i < 80; i++) {
        ret = ip4forward00000_pri_key_set(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        // uint32_t value_u32 = 90+i;
        // if(i >= 70) {value_u32 = 10+i;}
        // ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        // CHECK_OK_RET(ret, "GmcSetVertexProperty app_version.");

        ret = GmcSetIndexKeyName(stmt, "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        CHECK_SET_COUNT(ret);
    }

    // check dml_statis interface
    SET_COUNT(cn_update_succ[0], cn_update_fail[0]);
    ret = check_count(80, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, labelName, NULL);
    EXPECT_EQ(ret, 0);

    // ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    // ASSERT_EQ(GMERR_OK, ret);
    // printf(" count:%llu \n", count);

    printf(">> replace \n");
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_REPLACE);
    ASSERT_EQ(GMERR_OK, ret);
    RESET_COUNT();
    for (i = 0; i < 80; i++) {
        ret = ip4forward00000_set_obj(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        // uint64_t value_u64 = 90+i;
        // if(i >= 70) {value_u64 = 10+i;}
        // ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &value_u64, sizeof(value_u64));
        // CHECK_OK_RET(ret, "GmcSetVertexProperty app_obj_id.");

        ret = GmcExecute(stmt);
        CHECK_SET_COUNT(ret);
    }

    // check dml_statis interface
    SET_COUNT(cn_replace_succ[0], cn_replace_fail[0]);
    ret = check_count(80, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, labelName, NULL);
    EXPECT_EQ(ret, 0);

    // ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    // ASSERT_EQ(GMERR_OK, ret);
    // printf(" count:%llu \n", count);

    printf(">> merge \n");
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_MERGE);
    ASSERT_EQ(GMERR_OK, ret);
    RESET_COUNT();
    for (i = 0; i < 80; i++) {
        ret = ip4forward00000_merge_obj(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        // uint32_t value_u32 = 90+i;
        // if(i >= 70) {value_u32 = 10+i;}
        // ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        // CHECK_OK_RET(ret, "GmcSetVertexProperty attribute_id.");

        ret = GmcExecute(stmt);
        CHECK_SET_COUNT(ret);
    }

    // ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    // ASSERT_EQ(GMERR_OK, ret);
    // printf(" count:%llu \n", count);

    // check dml_statis interface
    SET_COUNT(cn_merge_succ[0], cn_merge_fail[0]);
    ret = check_count(80, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, labelName, NULL);
    EXPECT_EQ(ret, 0);

    // 提交事务
    ret = GmcTransRollBack(conn);
    ASSERT_EQ(GMERR_OK, ret);

    // check dml statis view
    ret = dml_sysview_query(NULL, g_labelName, NULL, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info(0);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    EXPECT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// KV table
const char *MS_KVTable_Name_01 = "KVTable_01";
const char *MS_KVTable_Name_02 = "KVTable_02";
const char *MS_KVTable_Name_03 = "KVTable_03";
const char *MS_KVTable_Name_04 = "KVTable_04";
const char *MS_config = "{\"max_record_count\" : 10000}";

// 022. 配置性能统计的开关参数为1，创建kv表kv_table，同步kv_set 1000条，kv_delete 100条
TEST_F(dml_oper_statis_test, dml_oper_statis_test_022)
{
    // create KV table
    int ret = GmcKvCreateTable(stmt, MS_KVTable_Name_01, MS_config);
    EXPECT_EQ(GMERR_OK, ret);

    void *tableLabel = NULL;
    ret = GmcKvPrepareStmtByLabelName(stmt, MS_KVTable_Name_01);
    EXPECT_EQ(GMERR_OK, ret);

    // insert
    printf(">> insert kv\n");
    RESET_COUNT();
    uint32_t value = 0;
    char Key_name[10] = {0};
    char Key_name_max[520] = {0};
    GmcKvTupleT kvInfo = {0};

    for (uint32_t i = 0; i < 210; i++) {
        uint32_t j = i % 100;
        value = j;

        memset(&kvInfo, 0, sizeof(GmcKvTupleT));
        if (i < 100) {
            memset(Key_name, 0, 10);
            snprintf(Key_name, sizeof(Key_name), "Key_%d", j);
            kvInfo.key = Key_name;
            kvInfo.keyLen = strlen(Key_name);
            kvInfo.value = &value;
            kvInfo.valueLen = sizeof(uint32_t);

            ret = GmcKvSet(stmt, Key_name, strlen(Key_name), &value, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            CHECK_SET_COUNT(ret);
        } else {
            memset(Key_name_max, 0, sizeof(Key_name_max));
            snprintf(Key_name_max, sizeof(Key_name_max), "Key_%0512d", j);
            kvInfo.key = Key_name_max;
            kvInfo.keyLen = strlen(Key_name_max);
            kvInfo.value = &value;
            kvInfo.valueLen = sizeof(uint32_t);

            ret = GmcKvSet(stmt, Key_name_max, strlen(Key_name_max), &value, sizeof(uint32_t));
            
            if (ret != GMERR_INVALID_VALUE) {
                CHECK_SET_COUNT(ret);
            }
        }
    }

    // check dml_statis interface
    SET_COUNT(cn_insert_succ[0], cn_insert_fail[0]);
    ret = check_count(100, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check_kv(stmt, MS_KVTable_Name_01, NULL);
    EXPECT_EQ(ret, 0);

    // select
    uint32_t count = 0;
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(100, count);
    printf(" count:%u\n", count);

    // read
    printf(">> read kv\n");
    for (uint32_t i = 0; i < 100; i++) {
        memset(Key_name, 0, 10);
        snprintf(Key_name, sizeof(Key_name), "Key_%d", i);

        // 查询表中的数据
        char output[128] = {0};
        uint32_t outputLen = sizeof(output);
        ret = GmcKvGet(stmt, Key_name, strlen(Key_name), output, &outputLen);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // delete
    printf(">> delete kv\n");
    RESET_COUNT();
    for (uint32_t i = 0; i < 120; i++) {
        if (i < 100) {
            memset(Key_name, 0, 10);
            snprintf(Key_name, sizeof(Key_name), "Key_%d", i);
            ret = GmcKvRemove(stmt, Key_name, strlen(Key_name));
            CHECK_SET_COUNT(ret);
        } else {
            memset(Key_name_max, 0, sizeof(Key_name_max));
            snprintf(Key_name_max, sizeof(Key_name_max), "Key_%0512d", i);
            ret = GmcKvRemove(stmt, Key_name_max, strlen(Key_name_max));
           
            if (ret != GMERR_INVALID_VALUE) {
                CHECK_SET_COUNT(ret);
            }
        }

    }

    // check dml_statis interface
    SET_COUNT(cn_delete_succ[0], cn_delete_fail[0]);
    ret = check_count(100, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check_kv(stmt, MS_KVTable_Name_01, NULL);
    EXPECT_EQ(ret, 0);

    // check dml statis view
    ret = dml_sysview_query(MS_KVTable_Name_01, MS_KVTable_Name_01, NULL, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info(0);
    EXPECT_EQ(ret, 0);

    // select
    count = 0;
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    printf(" count:%u\n", count);

    ret = GmcKvDropTable(stmt, (char *)MS_KVTable_Name_01);
    EXPECT_EQ(GMERR_OK, ret);
}

// 023. 配置性能统计的开关参数为1，创建kv表kv_table，异步kv_set 1000条，kv_delete 100条
TEST_F(dml_oper_statis_test, dml_oper_statis_test_023)
{
    // create KV table
    int ret = GmcKvCreateTable(stmt, MS_KVTable_Name_01, MS_config);
    EXPECT_EQ(GMERR_OK, ret);

    void *kvtable = NULL;
    AsyncUserDataT data = {0};

    // 同步打开kv表
    void *tableLabel = NULL;
    ret = GmcKvPrepareStmtByLabelName(stmt, MS_KVTable_Name_01);
    EXPECT_EQ(GMERR_OK, ret);

    // 异步打开kv表
    ret = GmcKvPrepareStmtByLabelName(g_stmt_async, MS_KVTable_Name_01);
    EXPECT_EQ(GMERR_OK, ret);

    // insert
    printf(">> insert kv\n");
    RESET_COUNT();
    uint32_t value = 0;
    char Key_name[10] = {0};
    GmcKvTupleT kvInfo = {0};

    for (uint32_t i = 0; i < 110; i++) {

        uint32_t j = i % 100;
        memset(Key_name, 0, 10);
        snprintf(Key_name, sizeof(Key_name), "Key_%d", j);
        value = j;

        memset(&kvInfo, 0, sizeof(GmcKvTupleT));
        kvInfo.key = Key_name;
        kvInfo.keyLen = strlen(Key_name);
        kvInfo.value = &value;
        kvInfo.valueLen = sizeof(uint32_t);

        ret = GmcKvSetAsync(g_stmt_async, &kvInfo, set_kv_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(GMERR_OK, data.status);
        CHECK_SET_COUNT(data.status);
    }

    // select
    uint32_t count = 0;
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    printf(" count:%u\n", count);

    // check dml_statis interface
    SET_COUNT(cn_insert_succ[0], cn_insert_fail[0]);
    ret = check_count(110, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check_kv(stmt, MS_KVTable_Name_01, NULL);
    EXPECT_EQ(ret, 0);

    // check dml statis view
    ret = dml_sysview_query(MS_KVTable_Name_01, MS_KVTable_Name_01, NULL, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info(0);
    EXPECT_EQ(ret, 0);

    // //read
    // printf(">> read kv\n");
    // for(uint32_t i = 0; i < 1000; i++){
    //     memset(Key_name, 0, 10);
    //     snprintf(Key_name, sizeof(Key_name), "Key_%d", i);

    //     ret = testGetKvAsync(testConnAsync, kvtable, Key_name, strlen(Key_name), get_kv_callback, &data);
    //     EXPECT_EQ(GMERR_OK, ret);
    //     ret = testWaitAsyncRecv(testConnAsync);
    //     EXPECT_EQ(GMERR_OK, ret);
    //     EXPECT_EQ(GMERR_OK, data.status);
    // }

    // select
    count = 0;
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(100, count);
    printf(" count:%u\n", count);

    // delete
    printf(">> delete kv\n");
    RESET_COUNT();
    for (uint32_t i = 0; i < 120; i++) {
        memset(Key_name, 0, 10);
        snprintf(Key_name, sizeof(Key_name), "Key_%d", i);
        ret = GmcKvRemoveAsync(g_stmt_async, Key_name, strlen(Key_name), delete_kv_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        CHECK_SET_COUNT(data.status);

        // //异步检查kv表中数据是否存在
        // ret = testIsKvExistAsync(testConnAsync, kvtable, Key_name, strlen(Key_name), is_kv_exist_callback, &data);
        // EXPECT_EQ(GMERR_OK, ret);
        // ret = testWaitAsyncRecv(testConnAsync);
        // EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(GMERR_OK, data.status);
        // EXPECT_EQ(0, data.kvIsExist);
    }

    // select
    count = 0;
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    printf(" count:%u\n", count);

    // check dml_statis interface
    SET_COUNT(cn_delete_succ[0], cn_delete_fail[0]);
    ret = check_count(120, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check_kv(stmt, MS_KVTable_Name_01, NULL);
    EXPECT_EQ(ret, 0);

    // check dml statis view
    ret = dml_sysview_query(NULL, MS_KVTable_Name_01, NULL, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info(0);
    EXPECT_EQ(ret, 0);

    // closekv
    // ret = GmcCloseKvTable(stmt);
    // EXPECT_EQ(GMERR_OK, ret);

    // closekv
    // ret = GmcCloseKvTable(g_stmt_async);
    // EXPECT_EQ(GMERR_OK, ret);

    // 异步删除kv表
    ret = GmcKvDropTableAsync(g_stmt_async, MS_KVTable_Name_01, drop_kv_table_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
}

// 024. 配置性能统计的开关参数为1，创建kv表kv_table，同步批量kv_set 1000条，kv_delete 100条
TEST_F(dml_oper_statis_test, dml_oper_statis_test_024)
{
    // create KV table
    int ret = GmcKvCreateTable(stmt, MS_KVTable_Name_01, MS_config);
    EXPECT_EQ(GMERR_OK, ret);

    void *tableLabel = NULL;
    ret = GmcKvPrepareStmtByLabelName(stmt, MS_KVTable_Name_01);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    // insert
    printf(">> insert kv\n");
    RESET_COUNT();
    uint32_t value = 0;
    char Key_name[10] = {0};
    GmcKvTupleT kvInfo = {0};
    for (uint32_t i = 0; i < 300; i++) {
        uint32_t j = i % 200;
        memset(Key_name, 0, 10);
        snprintf(Key_name, sizeof(Key_name), "Key_%d", j);
        value = i;

        memset(&kvInfo, 0, sizeof(GmcKvTupleT));
        kvInfo.key = Key_name;
        kvInfo.keyLen = strlen(Key_name);
        kvInfo.value = &value;
        kvInfo.valueLen = sizeof(uint32_t);

        // ret = GmcKvInputToStmt(stmt, &kvInfo);
        ret = GmcKvInputToStmt(stmt, Key_name, strlen(Key_name), &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddKvDML(batch, stmt, GMC_OPERATION_INSERT);
        CHECK_SET_COUNT(ret);
    }

    SET_COUNT(cn_insert_succ[0], cn_insert_fail[0]);

    // delete
    printf(">> delete kv\n");
    RESET_COUNT();
    for (uint32_t i = 0; i < 100; i++) {
        memset(Key_name, 0, 10);
        snprintf(Key_name, sizeof(Key_name), "Key_%d", i);

        memset(&kvInfo, 0, sizeof(GmcKvTupleT));
        kvInfo.key = Key_name;
        kvInfo.keyLen = strlen(Key_name);
        kvInfo.valueLen = 0;

        // ret = GmcKvInputToStmt(stmt, &kvInfo);
        ret = GmcKvInputToStmt(stmt, Key_name, strlen(Key_name), NULL, 0);
        EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcBatchAddKvDML(stmt,GMC_CMD_REMOVE_KV);
        ret = GmcBatchAddKvDML(batch, stmt, GMC_OPERATION_DELETE);
        CHECK_SET_COUNT(ret);
    }
    SET_COUNT(cn_delete_succ[0], cn_delete_fail[0]);

    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    GmcBatchRetT batchRet;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);
    batch = NULL;
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(totalNum, (uint32_t)400);
    EXPECT_EQ(successNum, (uint32_t)400);

    ret = dml_statis_check_kv(stmt, MS_KVTable_Name_01, NULL);
    EXPECT_EQ(ret, 0);

    // check dml statis view
    ret = dml_sysview_query(NULL, MS_KVTable_Name_01, NULL, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info(0);
    EXPECT_EQ(ret, 0);

    // select
    uint32_t count = 0;
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(100, count);
    printf(" count:%u\n", count);

    // read
    printf(">> read kv\n");
    for (uint32_t i = 100; i < 200; i++) {
        memset(Key_name, 0, 10);
        snprintf(Key_name, sizeof(Key_name), "Key_%d", i);

        // 查询表中的数据
        char output[128] = {0};
        uint32_t outputLen = sizeof(output);
        ret = GmcKvGet(stmt, Key_name, strlen(Key_name), output, &outputLen);
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(30, *(uint32_t*)output);
        // EXPECT_EQ(4, outputLen);
    }

    // check dml statis view
    ret = dml_sysview_query(MS_KVTable_Name_01, MS_KVTable_Name_01, NULL, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info(0);
    EXPECT_EQ(ret, 0);

    // select
    count = 0;
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    printf(" count:%u\n", count);

    // ret = GmcCloseKvTable(stmt);
    // EXPECT_EQ(GMERR_OK, ret);

    ret = GmcKvDropTable(stmt, (char *)MS_KVTable_Name_01);
    EXPECT_EQ(GMERR_OK, ret);
}

// 025. 配置性能统计的开关参数为1，创建kv表kv_table，异步批量kv_set 1000条，kv_delete 100条
TEST_F(dml_oper_statis_test, dml_oper_statis_test_025)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    // create KV table
    ret = GmcKvCreateTable(stmt, MS_KVTable_Name_01, MS_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 同步打开kv表
    void *tableLabel = NULL;
    ret = GmcKvPrepareStmtByLabelName(stmt, MS_KVTable_Name_01);
    EXPECT_EQ(GMERR_OK, ret);

    // 异步打开kv表
    void *tableLabelAsync = NULL;
    ret = GmcKvPrepareStmtByLabelName(g_stmt_async, MS_KVTable_Name_01);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    // insert
    printf(">> insert kv\n");
    RESET_COUNT();
    uint32_t value = 0;
    char Key_name[10] = {0};
    char Key_name_max[520] = {0};
    GmcKvTupleT kvInfo = {0};
    for (uint32_t i = 0; i < 300; i++) {
        uint32_t j = i % 200;
        memset(Key_name, 0, 10);
        snprintf(Key_name, sizeof(Key_name), "Key_%d", j);
        value = i;

        memset(&kvInfo, 0, sizeof(GmcKvTupleT));
        kvInfo.key = Key_name;
        kvInfo.keyLen = strlen(Key_name);
        kvInfo.value = &value;
        kvInfo.valueLen = sizeof(uint32_t);

        // ret = GmcKvInputToStmt(g_stmt_async, &kvInfo);
        ret = GmcKvInputToStmt(g_stmt_async, Key_name, strlen(Key_name), &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcBatchAddKvDML(g_stmt_async,GMC_CMD_SET_KV);
        ret = GmcBatchAddKvDML(batch, g_stmt_async, GMC_OPERATION_INSERT);
        CHECK_SET_COUNT(ret);
    }

    SET_COUNT(cn_insert_succ[0], cn_insert_fail[0]);

    // delete
    printf(">> delete kv\n");
    RESET_COUNT();
    for (uint32_t i = 100; i < 200; i++) {
        memset(Key_name, 0, 10);
        snprintf(Key_name, sizeof(Key_name), "Key_%d", i);

        memset(&kvInfo, 0, sizeof(GmcKvTupleT));
        kvInfo.key = Key_name;
        kvInfo.keyLen = strlen(Key_name);
        kvInfo.valueLen = 0;

        // ret = GmcKvInputToStmt(g_stmt_async, &kvInfo);
        ret = GmcKvInputToStmt(g_stmt_async, Key_name, strlen(Key_name), NULL, 0);
        EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcBatchAddKvDML(g_stmt_async,GMC_CMD_REMOVE_KV);
        ret = GmcBatchAddKvDML(batch, g_stmt_async, GMC_OPERATION_DELETE);
        CHECK_SET_COUNT(ret);
    }
    SET_COUNT(cn_delete_succ[0], cn_delete_fail[0]);
    memset(&data, 0, sizeof(data));
    ret = GmcBatchExecuteAsync(batch, BatchAsyncCallBack, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(400, data.totalNum);
    EXPECT_EQ(400, data.succNum);
    printf(" async batch pro num: %d\n", data.totalNum);

    // select
    uint32_t count = 0;
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    printf(" count:%u\n", count);

    ret = dml_statis_check_kv(stmt, MS_KVTable_Name_01, NULL);
    EXPECT_EQ(ret, 0);

    // check dml statis view
    ret = dml_sysview_query(MS_KVTable_Name_01, MS_KVTable_Name_01, NULL, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info(0);
    EXPECT_EQ(ret, 0);

    // closekv
    // ret = GmcCloseKvTable(stmt);
    // EXPECT_EQ(GMERR_OK, ret);

    // closekv
    // ret = GmcCloseKvTable(g_stmt_async);
    // EXPECT_EQ(ret, GMERR_OK);

    GmcFreeStmt(g_stmt_async);
    g_stmt_async = NULL;

    printf(">> truncate kv\n");
    // truncate kv table
    ret = GmcKvTruncateTable(stmt, MS_KVTable_Name_01);
    EXPECT_EQ(GMERR_OK, ret);

    ret = dml_statis_check_kv(stmt, MS_KVTable_Name_01, NULL);
    EXPECT_EQ(ret, 0);

    // check dml statis view
    ret = dml_sysview_query(NULL, MS_KVTable_Name_01, NULL, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info(0);
    EXPECT_EQ(ret, 0);

    // select
    count = 0;
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    printf(" count:%u\n", count);

    printf(">> drop kv\n");
    // drop kv table
    ret = GmcKvDropTable(stmt, (char *)MS_KVTable_Name_01);
    EXPECT_EQ(GMERR_OK, ret);

    // check dml info all
    ret = dml_sysview_query(NULL, MS_KVTable_Name_01, NULL, NULL);
    EXPECT_NE(ret, 0);
    ret = check_dml_info_null(0);
    EXPECT_EQ(ret, 0);
}

// 026. 配置性能统计的开关参数为1，创建kv表kv_table，开启事务，kv_set 100条，kv_delete 10条，commit
TEST_F(dml_oper_statis_test, dml_oper_statis_test_026)
{
    // create KV table
    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";

    int ret = GmcKvCreateTable(stmt, MS_KVTable_Name_01, Label_config);
    EXPECT_EQ(GMERR_OK, ret);

    void *tableLabel = NULL;
    ret = GmcKvPrepareStmtByLabelName(stmt, MS_KVTable_Name_01);
    EXPECT_EQ(GMERR_OK, ret);

    // 启动MS事务
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_PESSIMISITIC_TRX;
    ret = GmcTransStart(conn, &config);
    EXPECT_EQ(GMERR_OK, ret);

    // insert
    printf(">> insert kv\n");
    RESET_COUNT();
    uint32_t value = 0;
    char Key_name[10] = {0};
    GmcKvTupleT kvInfo = {0};
    for (uint32_t i = 0; i < 300; i++) {
        uint32_t j = i % 200;
        memset(Key_name, 0, 10);
        snprintf(Key_name, sizeof(Key_name), "Key_%d", i);
        value = i;

        memset(&kvInfo, 0, sizeof(GmcKvTupleT));
        kvInfo.key = Key_name;
        kvInfo.keyLen = strlen(Key_name);
        kvInfo.value = &value;
        kvInfo.valueLen = sizeof(uint32_t);

        ret = GmcKvSet(stmt, Key_name, strlen(Key_name), &value, sizeof(uint32_t));
        CHECK_SET_COUNT(ret);
    }

    // check dml_statis interface
    SET_COUNT(cn_insert_succ[0], cn_insert_fail[0]);
    ret = check_count(300, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check_kv(stmt, MS_KVTable_Name_01, NULL);
    EXPECT_EQ(ret, 0);

    // select
    uint32_t count = 0;
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(300, count);
    printf(" count:%u\n", count);

    // delete
    printf(">> delete kv\n");
    RESET_COUNT();
    for (uint32_t i = 0; i < 100; i++) {
        memset(Key_name, 0, 10);
        snprintf(Key_name, sizeof(Key_name), "Key_%d", i);
        ret = GmcKvRemove(stmt, Key_name, strlen(Key_name));
        CHECK_SET_COUNT(ret);

        bool isExist = 0;
        ret = GmcKvIsExist(stmt, Key_name, strlen(Key_name), &isExist);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isExist);
    }

    SET_COUNT(cn_delete_succ[0], cn_delete_fail[0]);
    ret = check_count(100, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check_kv(stmt, MS_KVTable_Name_01, NULL);
    EXPECT_EQ(ret, 0);

    // MS事务commit
    ret = GmcTransCommit(conn);
    EXPECT_EQ(GMERR_OK, ret);

    // select
    count = 0;
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(200, count);
    printf(" count:%u\n", count);

    // read
    printf(">> read kv\n");
    for (uint32_t i = 100; i < 200; i++) {
        memset(Key_name, 0, 10);
        snprintf(Key_name, sizeof(Key_name), "Key_%d", i);

        // 查询表中的数据
        char output[128] = {0};
        uint32_t outputLen = sizeof(output);
        ret = GmcKvGet(stmt, Key_name, strlen(Key_name), output, &outputLen);
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(30, *(uint32_t*)output);
        EXPECT_EQ(4, outputLen);
    }

    // check dml statis view
    ret = dml_sysview_query(MS_KVTable_Name_01, MS_KVTable_Name_01, NULL, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info(0);
    EXPECT_EQ(ret, 0);

    // count
    count = 0;
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    printf(" count:%u\n", count);

    // ret = GmcCloseKvTable(stmt);
    // EXPECT_EQ(GMERR_OK, ret);

    ret = GmcKvDropTable(stmt, (char *)MS_KVTable_Name_01);
    EXPECT_EQ(GMERR_OK, ret);
}

// 027. 配置性能统计的开关参数为1，创建kv表kv_table，开启事务，kv_set 100条，kv_delete 10条，rollback
TEST_F(dml_oper_statis_test, dml_oper_statis_test_027)
{
    // create KV table
    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";

    int ret = GmcKvCreateTable(stmt, MS_KVTable_Name_01, Label_config);
    EXPECT_EQ(GMERR_OK, ret);

    void *tableLabel = NULL;
    ret = GmcKvPrepareStmtByLabelName(stmt, MS_KVTable_Name_01);
    EXPECT_EQ(GMERR_OK, ret);

    // 启动MS事务
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_PESSIMISITIC_TRX;
    ret = GmcTransStart(conn, &config);
    EXPECT_EQ(GMERR_OK, ret);

    // insert
    printf(">> insert kv\n");
    RESET_COUNT();
    uint32_t value = 0;
    char Key_name[10] = {0};
    GmcKvTupleT kvInfo = {0};
    for (uint32_t i = 0; i < 300; i++) {
        uint32_t j = i % 200;
        memset(Key_name, 0, 10);
        snprintf(Key_name, sizeof(Key_name), "Key_%d", i);
        value = i;

        memset(&kvInfo, 0, sizeof(GmcKvTupleT));
        kvInfo.key = Key_name;
        kvInfo.keyLen = strlen(Key_name);
        kvInfo.value = &value;
        kvInfo.valueLen = sizeof(uint32_t);

        ret = GmcKvSet(stmt, Key_name, strlen(Key_name), &value, sizeof(uint32_t));
        CHECK_SET_COUNT(ret);
    }

    // check dml_statis interface
    SET_COUNT(cn_insert_succ[0], cn_insert_fail[0]);
    ret = check_count(300, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check_kv(stmt, MS_KVTable_Name_01, NULL);
    EXPECT_EQ(ret, 0);

    // select
    uint32_t count = 0;
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(300, count);
    printf(" count:%u\n", count);

    // delete
    printf(">> delete kv\n");
    RESET_COUNT();
    for (uint32_t i = 0; i < 100; i++) {
        memset(Key_name, 0, 10);
        snprintf(Key_name, sizeof(Key_name), "Key_%d", i);
        ret = GmcKvRemove(stmt, Key_name, strlen(Key_name));
        CHECK_SET_COUNT(ret);

        bool isExist = 0;
        ret = GmcKvIsExist(stmt, Key_name, strlen(Key_name), &isExist);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isExist);
    }

    SET_COUNT(cn_delete_succ[0], cn_delete_fail[0]);
    ret = check_count(100, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check_kv(stmt, MS_KVTable_Name_01, NULL);
    EXPECT_EQ(ret, 0);

    // MS事务commit
    ret = GmcTransRollBack(conn);
    EXPECT_EQ(GMERR_OK, ret);

    // select
    count = 0;
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    // EXPECT_EQ(200, count);
    printf(" count:%u\n", count);

    // read
    printf(">> read kv\n");
    for (uint32_t i = 100; i < 200; i++) {
        memset(Key_name, 0, 10);
        snprintf(Key_name, sizeof(Key_name), "Key_%d", i);

        // 查询表中的数据
        char output[128] = {0};
        uint32_t outputLen = sizeof(output);
        ret = GmcKvGet(stmt, Key_name, strlen(Key_name), output, &outputLen);
        // EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(30, *(uint32_t*)output);
        // EXPECT_EQ(4, outputLen);
    }

    // check dml statis view
    ret = dml_sysview_query(MS_KVTable_Name_01, MS_KVTable_Name_01, NULL, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info(0);
    EXPECT_EQ(ret, 0);

    // count
    count = 0;
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    printf(" count:%u\n", count);

    // ret = GmcCloseKvTable(stmt);
    // EXPECT_EQ(GMERR_OK, ret);

    ret = GmcKvDropTable(stmt, (char *)MS_KVTable_Name_01);
    EXPECT_EQ(GMERR_OK, ret);
}

// 028. 配置性能统计的开关参数为1，创建kv表kv_table，kv_set 100条，truncate
TEST_F(dml_oper_statis_test, dml_oper_statis_test_028)
{
    // create KV table
    int ret = GmcKvCreateTable(stmt, MS_KVTable_Name_01, MS_config);
    EXPECT_EQ(GMERR_OK, ret);

    void *tableLabel = NULL;
    ret = GmcKvPrepareStmtByLabelName(stmt, MS_KVTable_Name_01);
    EXPECT_EQ(GMERR_OK, ret);

    // insert
    printf(">> insert kv\n");
    RESET_COUNT();
    uint32_t value = 0;
    char Key_name[10] = {0};
    GmcKvTupleT kvInfo = {0};
    for (uint32_t i = 0; i < 200; i++) {
        memset(Key_name, 0, 10);
        snprintf(Key_name, sizeof(Key_name), "Key_%d", i);
        value = i;

        memset(&kvInfo, 0, sizeof(GmcKvTupleT));
        kvInfo.key = Key_name;
        kvInfo.keyLen = strlen(Key_name);
        kvInfo.value = &value;
        kvInfo.valueLen = sizeof(uint32_t);

        ret = GmcKvSet(stmt, Key_name, strlen(Key_name), &value, sizeof(uint32_t));
        CHECK_SET_COUNT(ret);
    }

    // check dml_statis interface
    SET_COUNT(cn_insert_succ[0], cn_insert_fail[0]);
    ret = check_count(200, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check_kv(stmt, MS_KVTable_Name_01, NULL);
    EXPECT_EQ(ret, 0);

    // count
    uint32_t count = 0;
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(200, count);
    printf(" count:%u\n", count);

    // delete
    printf(">> delete kv\n");
    RESET_COUNT();
    for (uint32_t i = 0; i < 100; i++) {
        memset(Key_name, 0, 10);
        snprintf(Key_name, sizeof(Key_name), "Key_%d", i);
        ret = GmcKvRemove(stmt, Key_name, strlen(Key_name));
        CHECK_SET_COUNT(ret);

        bool isExist = 0;
        ret = GmcKvIsExist(stmt, Key_name, strlen(Key_name), &isExist);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isExist);
    }

    // check dml_statis interface
    SET_COUNT(cn_delete_succ[0], cn_delete_fail[0]);
    ret = check_count(100, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check_kv(stmt, MS_KVTable_Name_01, NULL);
    EXPECT_EQ(ret, 0);

    // check dml statis view
    ret = dml_sysview_query(MS_KVTable_Name_01, MS_KVTable_Name_01, NULL, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info(0);
    EXPECT_EQ(ret, 0);

    // select
    count = 0;
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(100, count);
    printf(" count:%u\n", count);

    // read
    printf(">> read kv\n");
    for (uint32_t i = 100; i < 200; i++) {
        memset(Key_name, 0, 10);
        snprintf(Key_name, sizeof(Key_name), "Key_%d", i);

        // 查询表中的数据
        char output[128] = {0};
        uint32_t outputLen = sizeof(output);
        ret = GmcKvGet(stmt, Key_name, strlen(Key_name), output, &outputLen);
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(30, *(uint32_t*)output);
        EXPECT_EQ(4, outputLen);
    }

    // count
    count = 0;
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    printf(" count:%u\n", count);

    // ret = GmcCloseKvTable(stmt);
    // EXPECT_EQ(GMERR_OK, ret);

    ret = GmcKvTruncateTable(stmt, (char *)MS_KVTable_Name_01);
    EXPECT_EQ(GMERR_OK, ret);

    ret = dml_statis_check_kv(stmt, MS_KVTable_Name_01, NULL);
    EXPECT_EQ(ret, 0);

    // check dml statis view
    ret = dml_sysview_query(MS_KVTable_Name_01, MS_KVTable_Name_01, NULL, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info(0);
    EXPECT_EQ(ret, 0);

    ret = GmcKvDropTable(stmt, (char *)MS_KVTable_Name_01);
    EXPECT_EQ(GMERR_OK, ret);
}

// 029. 配置性能统计的开关参数为1，创建kv表kv_table，kv_set 100条，drop table
TEST_F(dml_oper_statis_test, dml_oper_statis_test_029)
{
    // create KV table
    int ret = GmcKvCreateTable(stmt, MS_KVTable_Name_01, MS_config);
    EXPECT_EQ(GMERR_OK, ret);

    void *tableLabel = NULL;
    ret = GmcKvPrepareStmtByLabelName(stmt, MS_KVTable_Name_01);
    EXPECT_EQ(GMERR_OK, ret);

    // insert
    printf(">> insert kv\n");
    uint32_t value = 0;
    char Key_name[10] = {0};
    GmcKvTupleT kvInfo = {0};
    for (uint32_t i = 0; i < 200; i++) {
        memset(Key_name, 0, 10);
        snprintf(Key_name, sizeof(Key_name), "Key_%d", i);
        value = i;

        memset(&kvInfo, 0, sizeof(GmcKvTupleT));
        kvInfo.key = Key_name;
        kvInfo.keyLen = strlen(Key_name);
        kvInfo.value = &value;
        kvInfo.valueLen = sizeof(uint32_t);

        ret = GmcKvSet(stmt, Key_name, strlen(Key_name), &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }

    // count
    uint32_t count = 0;
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(200, count);
    printf(" count:%u\n", count);

    // delete
    printf(">> delete kv\n");
    for (uint32_t i = 0; i < 100; i++) {
        memset(Key_name, 0, 10);
        snprintf(Key_name, sizeof(Key_name), "Key_%d", i);
        ret = GmcKvRemove(stmt, Key_name, strlen(Key_name));
        EXPECT_EQ(GMERR_OK, ret);

        bool isExist = 0;
        ret = GmcKvIsExist(stmt, Key_name, strlen(Key_name), &isExist);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isExist);
    }

    // select
    count = 0;
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(100, count);
    printf(" count:%u\n", count);

    // read
    printf(">> read kv\n");
    for (uint32_t i = 100; i < 200; i++) {
        memset(Key_name, 0, 10);
        snprintf(Key_name, sizeof(Key_name), "Key_%d", i);

        // 查询表中的数据
        char output[128] = {0};
        uint32_t outputLen = sizeof(output);
        ret = GmcKvGet(stmt, Key_name, strlen(Key_name), output, &outputLen);
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(30, *(uint32_t*)output);
        EXPECT_EQ(4, outputLen);
    }

    // count
    count = 0;
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    printf(" count:%u\n", count);

    // ret = GmcCloseKvTable(stmt);
    // EXPECT_EQ(GMERR_OK, ret);

    printf(">> drop kv table\n");
    ret = GmcKvDropTable(stmt, (char *)MS_KVTable_Name_01);
    EXPECT_EQ(GMERR_OK, ret);

    // check dml info all
    ret = dml_sysview_query(NULL, MS_KVTable_Name_01, NULL, NULL);
    EXPECT_NE(ret, 0);
    ret = check_dml_info_null(0);
    EXPECT_EQ(ret, 0);
}

// 030. 配置dml统计的配置项为1，创建kv表，ip4forard表；kv_table，同步kv_set 1000条，kv_delete 100条；
// 创建vertex表ip4forward，同步insert 1000条数据，update 1000条，replace1000条，merge 1000条，
// delete 1000条； 并各种操作失败100条；
TEST_F(dml_oper_statis_test, dml_oper_statis_test_030)
{
    // create KV table
    int ret = GmcKvCreateTable(stmt, MS_KVTable_Name_01, MS_config);
    EXPECT_EQ(GMERR_OK, ret);

    void *tableLabel = NULL;
    ret = GmcKvPrepareStmtByLabelName(stmt, MS_KVTable_Name_01);
    EXPECT_EQ(GMERR_OK, ret);

    // insert
    printf(">> insert kv\n");
    RESET_COUNT();
    uint32_t value = 0;
    char Key_name[10] = {0};
    char Key_name_max[520] = {0};
    GmcKvTupleT kvInfo = {0};

    for (uint32_t i = 0; i < 210; i++) {
        uint32_t j = i % 100;
        value = j;

        memset(&kvInfo, 0, sizeof(GmcKvTupleT));
        if (i < 100) {
            memset(Key_name, 0, 10);
            snprintf(Key_name, sizeof(Key_name), "Key_%d", j);
            kvInfo.key = Key_name;
            kvInfo.keyLen = strlen(Key_name);
            kvInfo.value = &value;
            kvInfo.valueLen = sizeof(uint32_t);

            ret = GmcKvSet(stmt, Key_name, strlen(Key_name), &value, sizeof(uint32_t));
            CHECK_SET_COUNT(ret);
        } else {
            memset(Key_name_max, 0, sizeof(Key_name_max));
            snprintf(Key_name_max, sizeof(Key_name_max), "Key_%0512d", j);
            kvInfo.key = Key_name_max;
            kvInfo.keyLen = strlen(Key_name_max);
            kvInfo.value = &value;
            kvInfo.valueLen = sizeof(uint32_t);

            ret = GmcKvSet(stmt, Key_name_max, strlen(Key_name_max), &value, sizeof(uint32_t));
            if (ret != GMERR_INVALID_VALUE) {
                CHECK_SET_COUNT(ret);
            }
        }
    }

    // check dml_statis interface
    SET_COUNT(cn_insert_succ[0], cn_insert_fail[0]);
    ret = check_count(100, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check_kv(stmt, MS_KVTable_Name_01, NULL);
    EXPECT_EQ(ret, 0);

    // select
    uint32_t count_kv = 0;
    ret = GmcKvTableRecordCount(stmt, &count_kv);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(100, count_kv);
    printf(" count_kv:%u\n", count_kv);

    // delete
    printf(">> delete kv\n");
    RESET_COUNT();
    for (uint32_t i = 0; i < 120; i++) {
        if (i < 100) {
            memset(Key_name, 0, 10);
            snprintf(Key_name, sizeof(Key_name), "Key_%d", i);
            ret = GmcKvRemove(stmt, Key_name, strlen(Key_name));
            CHECK_SET_COUNT(ret);
        } else {
            memset(Key_name_max, 0, sizeof(Key_name_max));
            snprintf(Key_name_max, sizeof(Key_name_max), "Key_%0512d", i);
            ret = GmcKvRemove(stmt, Key_name_max, strlen(Key_name_max));
            if (ret != GMERR_INVALID_VALUE) {
                CHECK_SET_COUNT(ret);
            }
        }

        // bool isExist = 0;
        // ret = GmcKvIsExist(stmt, Key_name, strlen(Key_name), &isExist);
        // EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(0, isExist);
    }

    // check dml_statis interface
    SET_COUNT(cn_delete_succ[0], cn_delete_fail[0]);
    ret = check_count(100, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check_kv(stmt, MS_KVTable_Name_01, NULL);
    EXPECT_EQ(ret, 0);

    // check dml statis view
    ret = dml_sysview_query(MS_KVTable_Name_01, MS_KVTable_Name_01, NULL, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info(0);
    EXPECT_EQ(ret, 0);

    // select
    count_kv = 0;
    ret = GmcKvTableRecordCount(stmt, &count_kv);
    EXPECT_EQ(GMERR_OK, ret);
    printf(" count_kv:%u\n", count_kv);

    // ret = GmcCloseKvTable(stmt);
    // EXPECT_EQ(GMERR_OK, ret);

    // 创建 vertexLabel_1
    char labelName[128] = "ip4forward00000";

    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    uint64_t count = 0;

    clear_count();
    // open vertexLabel
    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    printf(">> insert \n");
    uint64_t i = 0;
    uint64_t j = 0;
    RESET_COUNT();
    for (i = 0; i < 100; i++) {
        j = i % 90;
        ret = ip4forward00000_set_obj(stmt, j);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(stmt);
        CHECK_SET_COUNT(ret);
    }

    // check dml_statis interface
    SET_COUNT(cn_insert_succ[0], cn_insert_fail[0]);
    ret = check_count(90, 10);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, labelName, NULL);
    EXPECT_EQ(ret, 0);

    // IP4FORWARD_SCAN();
    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    printf(">> update \n");
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);
    RESET_COUNT();
    for (i = 0; i < 80; i++) {
        ret = ip4forward00000_pri_key_set(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        uint32_t value_u32 = 90 + i;
        if (i >= 70) {
            value_u32 = 10 + i;
        }
        ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        CHECK_OK_RET(ret, "GmcSetVertexProperty app_version.");

        ret = GmcSetIndexKeyName(stmt, "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        CHECK_SET_COUNT(ret);
    }

    // check dml_statis interface
    SET_COUNT(cn_update_succ[0], cn_update_fail[0]);
    ret = check_count(70, 10);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, labelName, NULL);
    EXPECT_EQ(ret, 0);

    // check dml statis view
    ret = dml_sysview_query(NULL, g_labelName, NULL, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info(0);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    printf(">> replace \n");
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_REPLACE);
    ASSERT_EQ(GMERR_OK, ret);
    RESET_COUNT();
    for (i = 0; i < 80; i++) {
        ret = ip4forward00000_set_obj(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        uint64_t value_u64 = 90 + i;
        if (i >= 70) {
            value_u64 = 10 + i;
        }
        ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &value_u64, sizeof(value_u64));
        CHECK_OK_RET(ret, "GmcSetVertexProperty app_obj_id.");

        ret = GmcExecute(stmt);
        CHECK_SET_COUNT(ret);
    }

    // check dml_statis interface
    SET_COUNT(cn_replace_succ[0], cn_replace_fail[0]);
    ret = check_count(70, 10);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, labelName, NULL);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    printf(">> merge \n");
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_MERGE);
    ASSERT_EQ(GMERR_OK, ret);
    RESET_COUNT();
    for (i = 0; i < 80; i++) {
        ret = ip4forward00000_merge_obj(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        uint32_t value_u32 = 90 + i;
        if (i >= 70) {
            value_u32 = 10 + i;
        }
        ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        CHECK_OK_RET(ret, "GmcSetVertexProperty attribute_id.");

        ret = GmcExecute(stmt);
        CHECK_SET_COUNT(ret);
    }

    // check dml_statis interface
    SET_COUNT(cn_merge_succ[0], cn_merge_fail[0]);
    ret = check_count(70, 10);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, labelName, NULL);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    printf(">> delete \n");
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    RESET_COUNT();
    for (i = 0; i < 200; i++) {
        ret = ip4forward00000_pri_key_set(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcSetIndexKeyName(stmt, "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        CHECK_SET_COUNT(ret);
    }
    // check dml_statis interface
    SET_COUNT(cn_delete_succ[0], cn_delete_fail[0]);
    ret = check_count(200, 0);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, labelName, NULL);
    EXPECT_EQ(ret, 0);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    // 清空数据
    printf(">> truncate table \n");
    ret = GmcTruncateVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);

    ret = dml_statis_check(stmt, labelName, NULL);
    EXPECT_EQ(ret, 0);

    // check dml statis view
    ret = dml_sysview_query(NULL, g_labelName, NULL, NULL);
    EXPECT_EQ(ret, 0);
    set_count_value(90, 200, 70, 70, 70);
    set_count_value_fail(10, 0, 10, 10, 10);
    ret = check_dml_info(0);
    EXPECT_EQ(ret, 0);
    ret = dml_sysview_query(NULL, MS_KVTable_Name_01, NULL, NULL);
    EXPECT_EQ(ret, 0);
    set_count_value(100, 100, 0, 0, 0);
    set_count_value_fail(0, 0, 0, 0, 0);
    ret = check_dml_info(0);
    EXPECT_EQ(ret, 0);

    printf(">> drop table \n");
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);

    ret = dml_statis_check(stmt, labelName, NULL, GMERR_UNDEFINED_TABLE);
    EXPECT_EQ(ret, GMERR_UNDEFINED_TABLE);

    ret = GmcKvDropTable(stmt, (char *)MS_KVTable_Name_01);
    EXPECT_EQ(GMERR_OK, ret);

    // check dml statis view
    ret = dml_sysview_query(NULL, g_labelName, NULL, NULL);
    EXPECT_NE(ret, 0);
    ret = check_dml_info_null(0);
    EXPECT_EQ(ret, 0);
}

// 031. 配置dml统计的配置项为1，创建vertex表ip4forward，insert 1000条数据，开启对账，结束对账；
TEST_F(dml_oper_statis_test, dml_oper_statis_test_031)
{

    // 创建 vertexLabel_1
    char labelName[128] = "ip4forward00000";

    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    int ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    uint64_t count;
    bool isAbnormal = false;
    // open vertexLabel
    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
#define FULLTABLE 0xff
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);

    printf(">> insert \n");
    uint64_t i = 0;
    uint64_t j = 0;
    RESET_COUNT();
    for (i = 0; i < 100; i++) {
        j = i % 90;
        ret = ip4forward00000_set_obj(stmt, j);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(stmt);
        CHECK_SET_COUNT(ret);
    }

    ret = GmcEndCheck(stmt, labelName, FULLTABLE, false);
    EXPECT_EQ(GMERR_OK, ret);

    // check dml_statis interface
    SET_COUNT(cn_insert_succ[0], cn_insert_fail[0]);
    ret = check_count(90, 10);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, labelName, NULL);
    EXPECT_EQ(ret, 0);

    // IP4FORWARD_SCAN();
    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    printf(">> drop table \n");
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 032. 配置dml统计的配置项为1，创建vertex表ip4forward，insert 1000条数据，开启对账，对账中止；
TEST_F(dml_oper_statis_test, dml_oper_statis_test_032)
{

    // 创建 vertexLabel_1
    char labelName[128] = "ip4forward00000";

    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    int ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    uint64_t count;

    // open vertexLabel
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_OK, ret);
#define FULLTABLE 0xff
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);

    printf(">> insert \n");
    uint64_t i = 0;
    uint64_t j = 0;
    RESET_COUNT();
    for (i = 0; i < 100; i++) {
        j = i % 90;
        ret = ip4forward00000_set_obj(stmt, j);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(stmt);
        CHECK_SET_COUNT(ret);
    }

    ret = GmcEndCheck(stmt, labelName, FULLTABLE, true);
    EXPECT_EQ(GMERR_OK, ret);

    // check dml_statis interface
    SET_COUNT(cn_insert_succ[0], cn_insert_fail[0]);
    ret = check_count(90, 10);
    EXPECT_EQ(ret, 0);
    ret = dml_statis_check(stmt, labelName, NULL);
    EXPECT_EQ(ret, 0);

    // IP4FORWARD_SCAN();
    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    // check dml statis view
    ret = dml_sysview_query(NULL, g_labelName, NULL, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info(0);
    EXPECT_EQ(ret, 0);

    printf(">> drop table \n");
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);

    // check dml statis view
    ret = dml_sysview_query(NULL, g_labelName, NULL, NULL);
    EXPECT_NE(ret, 0);
    ret = check_dml_info_null(0);
    EXPECT_EQ(ret, 0);
}

const char *test_delta_config_json = R"(
    {
        "max_record_count":10000,
        "delta_store_name":"dsdml1",
        "writers":"abc"     
    })";
const char *deltaStoreJsonNormalSingle = R"(
    {  
    "delta_stores":                        
        [{                                       
            "name": "dsdml1",    
            "init_mem_size": 10485760,         
            "max_mem_size": 20971520,          
            "extend_mem_size": 6291456,        
            "page_size": 16384                 
        }]                                       
})";

// 039. Ip4forward表，4m数据 replace,批量replace，update,delete操作
TEST_F(dml_oper_statis_test, dml_oper_statis_test_039)
{
    // 创建 vertexLabel_1
    char labelName[128] = "ip4forward00000";

    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    int ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    uint64_t count;
#if defined (ENV_RTOSV2X) || defined (FEATURE_PERSISTENCE)
#define MAX_COUNT_FIB 300
#elif defined ENV_RTOSV2
#define MAX_COUNT_FIB 30000
#else
#define MAX_COUNT_FIB 100000
#endif
    // open vertexLabel
    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    printf(">> insert \n");
    uint64_t i = 0;

    for (i = 0; i < MAX_COUNT_FIB; i++) {
        ret = ip4forward00000_set_obj(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(stmt);
        EXPECT_EQ(ret, GMERR_OK);
    }

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    // check dml info all
    ret = dml_sysview_query(g_labelName, g_labelName, NULL, NULL);
    EXPECT_EQ(ret, 0);
    set_count_value(MAX_COUNT_FIB, 0, 0, 0, 0);
    ret = check_dml_info(0);
    EXPECT_EQ(ret, 0);

    printf(">> update \n");
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);
    for (i = 0; i < MAX_COUNT_FIB; i++) {
        ret = ip4forward00000_pri_key_set(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        uint32_t value_u32 = i + 1;

        ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        CHECK_OK_RET(ret, "GmcSetVertexProperty primary_label.");

        // ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        // CHECK_OK_RET(ret, "GmcSetVertexProperty attribute_id.");

        ret = GmcSetIndexKeyName(stmt, "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(ret, GMERR_OK);
    }

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    // check dml info all
    ret = dml_sysview_query(g_labelName, g_labelName, NULL, NULL);
    EXPECT_EQ(ret, 0);
    set_count_value(MAX_COUNT_FIB, 0, MAX_COUNT_FIB, 0, 0);
    ret = check_dml_info(0);
    EXPECT_EQ(ret, 0);

    printf(">> replace \n");
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < MAX_COUNT_FIB; i++) {
        ret = ip4forward00000_set_obj(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(stmt);
        EXPECT_EQ(ret, GMERR_OK);
    }

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    // check dml info all
    ret = dml_sysview_query(g_labelName, g_labelName, NULL, NULL);
    EXPECT_EQ(ret, 0);
    set_count_value(MAX_COUNT_FIB, 0, MAX_COUNT_FIB, MAX_COUNT_FIB, 0);
    ret = check_dml_info(0);
    EXPECT_EQ(ret, 0);

    ret = dml_statis_check(stmt, g_labelName, NULL);
    EXPECT_EQ(ret, 0);

    printf(">> merge \n");
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_MERGE);
    ASSERT_EQ(GMERR_OK, ret);
    for (i = 0; i < MAX_COUNT_FIB; i++) {
        ret = ip4forward00000_merge_obj(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(stmt);
        EXPECT_EQ(ret, GMERR_OK);
    }

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    // check dml info all
    ret = dml_sysview_query(g_labelName, g_labelName, NULL, NULL);
    EXPECT_EQ(ret, 0);
    set_count_value(MAX_COUNT_FIB, 0, MAX_COUNT_FIB, MAX_COUNT_FIB, MAX_COUNT_FIB);
    ret = check_dml_info(0);
    EXPECT_EQ(ret, 0);

    printf(">> delete \n");
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    for (i = 0; i < MAX_COUNT_FIB; i++) {
        ret = ip4forward00000_pri_key_set(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcSetIndexKeyName(stmt, "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(ret, GMERR_OK);
    }

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    // check dml info all
    ret = dml_sysview_query(g_labelName, g_labelName, NULL, NULL);
    EXPECT_EQ(ret, 0);
    set_count_value(MAX_COUNT_FIB, MAX_COUNT_FIB, MAX_COUNT_FIB, MAX_COUNT_FIB, MAX_COUNT_FIB);
    ret = check_dml_info(0);
    EXPECT_EQ(ret, 0);

    ret = dml_statis_check(stmt, g_labelName, NULL);
    EXPECT_EQ(ret, 0);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 040.kv表，1m数据 replace，批量replace，update,delete操作
TEST_F(dml_oper_statis_test, dml_oper_statis_test_040)
{
    // create KV table
    int ret = GmcKvCreateTable(stmt, MS_KVTable_Name_01, MS_config);
    EXPECT_EQ(GMERR_OK, ret);

    void *tableLabel = NULL;
    ret = GmcKvPrepareStmtByLabelName(stmt, MS_KVTable_Name_01);
    EXPECT_EQ(GMERR_OK, ret);
#define MAX_COUNT_KV 10000

    // insert
    printf(">> insert kv\n");
    uint32_t value = 0;
    char Key_name[10] = {0};
    GmcKvTupleT kvInfo = {0};

    for (uint32_t i = 0; i < MAX_COUNT_KV; i++) {
        memset(Key_name, 0, 10);
        snprintf(Key_name, sizeof(Key_name), "Key_%d", i);
        value = i;

        memset(&kvInfo, 0, sizeof(GmcKvTupleT));
        kvInfo.key = Key_name;
        kvInfo.keyLen = strlen(Key_name);
        kvInfo.value = &value;
        kvInfo.valueLen = sizeof(uint32_t);

        ret = GmcKvSet(stmt, Key_name, strlen(Key_name), &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }

    // count
    uint32_t count = 0;
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    printf(" count:%u\n", count);

    // check dml info all
    ret = dml_sysview_query(NULL, MS_KVTable_Name_01, NULL, NULL);
    EXPECT_EQ(ret, 0);
    set_count_value(MAX_COUNT_KV, 0, 0, 0, 0);
    ret = check_dml_info(0);
    EXPECT_EQ(ret, 0);

    // read
    printf(">> read kv\n");
    for (uint32_t i = 0; i < MAX_COUNT_KV; i++) {
        memset(Key_name, 0, 10);
        snprintf(Key_name, sizeof(Key_name), "Key_%d", i);

        // 查询表中的数据
        char output[128] = {0};
        uint32_t outputLen = sizeof(output);
        ret = GmcKvGet(stmt, Key_name, strlen(Key_name), output, &outputLen);
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(30, *(uint32_t*)output);
        EXPECT_EQ(4, outputLen);
    }

    // delete
    printf(">> delete kv\n");

    for (uint32_t i = 0; i < MAX_COUNT_KV; i++) {
        memset(Key_name, 0, 10);
        snprintf(Key_name, sizeof(Key_name), "Key_%d", i);
        ret = GmcKvRemove(stmt, Key_name, strlen(Key_name));
        EXPECT_EQ(GMERR_OK, ret);

        bool isExist = 0;
        ret = GmcKvIsExist(stmt, Key_name, strlen(Key_name), &isExist);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isExist);
    }

    // select
    count = 0;
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    printf(" count:%u\n", count);

    // check dml info all
    ret = dml_sysview_query(MS_KVTable_Name_01, MS_KVTable_Name_01, NULL, NULL);
    EXPECT_EQ(ret, 0);
    set_count_value(MAX_COUNT_KV, MAX_COUNT_KV, 0, 0, 0);
    ret = check_dml_info(0);
    EXPECT_EQ(ret, 0);

    ret = dml_statis_check_kv(stmt, MS_KVTable_Name_01, NULL);
    EXPECT_EQ(ret, 0);

    // ret = GmcCloseKvTable(stmt);
    // EXPECT_EQ(GMERR_OK, ret);

    ret = GmcKvTruncateTable(stmt, (char *)MS_KVTable_Name_01);
    EXPECT_EQ(GMERR_OK, ret);

    // check dml info all
    ret = dml_sysview_query(MS_KVTable_Name_01, MS_KVTable_Name_01, NULL, NULL);
    EXPECT_EQ(ret, 0);
    set_count_value(MAX_COUNT_KV, MAX_COUNT_KV, 0, 0, 0);
    ret = check_dml_info(0);
    EXPECT_EQ(ret, 0);

    ret = dml_statis_check_kv(stmt, MS_KVTable_Name_01);
    EXPECT_EQ(ret, 0);

    ret = GmcKvDropTable(stmt, (char *)MS_KVTable_Name_01);
    EXPECT_EQ(GMERR_OK, ret);

    // check dml info all
    ret = dml_sysview_query(NULL, MS_KVTable_Name_01, NULL, NULL);
    EXPECT_NE(ret, 0);
    ret = check_dml_info_null(0);
    EXPECT_EQ(ret, 0);

    ret = dml_statis_check_kv(stmt, MS_KVTable_Name_01, NULL, GMERR_UNDEFINED_TABLE);
    EXPECT_EQ(ret, GMERR_UNDEFINED_TABLE);
}


// 42、	vertex插入数据后进行视图查询，查询10000次，预期dml性能展示数据不变；
TEST_F(dml_oper_statis_test, dml_oper_statis_test_042)
{
    // 创建 vertexLabel_1
    char labelName[128] = "ip4forward00000";

    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    int ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    uint64_t count;

    // open vertexLabel
    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    printf(">> insert \n");
    uint64_t i = 0;
    RESET_COUNT();
    for (i = 0; i < 1000; i++) {
        uint64_t j = i % 900;
        ret = ip4forward00000_set_obj(stmt, j);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(stmt);
        CHECK_SET_COUNT(ret);
    }

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);
    SET_COUNT(cn_insert_succ[0], cn_insert_fail[0]);
    ret = check_count(900, 100);
    EXPECT_EQ(ret, 0);

    // 查询10000次
    int32_t checkTimes = 100;
    // 查询视图次数
    int32_t checkViewTimes = 100;
#if defined(ENV_RTOSV2X)
    checkTimes = 30;
    checkViewTimes = 10;
#endif
    // 设备上全局查询视图容易超时需降低查询次数
    printf(">> check 100 times. \n");
    for (int i = 0; i < checkTimes; i++) {
        ret = dml_statis_check(stmt, g_labelName, NULL, 0, 0);
        EXPECT_EQ(ret, 0);
    }

    for (int i = 0; i < checkViewTimes; i++) {
        // check dml statis view
        ret = dml_sysview_query(NULL, g_labelName, NULL, NULL, 0);
        EXPECT_EQ(ret, 0);
        ret = check_dml_info(0, 0, 0);
        EXPECT_EQ(ret, 0);
    }

    // close vertexLabel
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 43、	vertex插入数据1K条查询视图然后删除所有数据查询视图，循环10000次，预期dml性能展示数据不变；
TEST_F(dml_oper_statis_test, dml_oper_statis_test_043)
{
    // 创建 vertexLabel_1
    char labelName[128] = "ip4forward00000";

    char *schema_json = NULL;
    int32_t checkTimes = 100;
#if defined(ENV_RTOSV2X)
    checkTimes = 1;
#endif
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    int ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    uint64_t count;

    // open vertexLabel
    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    printf(">> insert \n");
    uint64_t i = 0;
    RESET_COUNT();
    for (i = 0; i < 1000; i++) {
        uint64_t j = i % 900;
        ret = ip4forward00000_set_obj(stmt, j);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(stmt);
        CHECK_SET_COUNT(ret);
    }

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);
    SET_COUNT(cn_insert_succ[0], cn_insert_fail[0]);

    printf(">> delete \n");
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    RESET_COUNT();
    for (i = 1000; i < 3000; i++) {
        ret = ip4forward00000_pri_key_set(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcSetIndexKeyName(stmt, "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        CHECK_SET_COUNT(ret);
    }
    SET_COUNT(cn_delete_succ[0], cn_delete_fail[0]);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    // 查询1000次
    printf(">> check 100 times. \n");
    for (int32_t i = 0; i < checkTimes; i++) {
        ret = dml_statis_check(stmt, g_labelName, NULL, 0, 0);
        EXPECT_EQ(ret, 0);

        // check dml statis view
        ret = dml_sysview_query(NULL, g_labelName, NULL, NULL, 0);
        EXPECT_EQ(ret, 0);
        ret = check_dml_info(0, 0, 0);
        EXPECT_EQ(ret, 0);
    }

    // close vertexLabel
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);

    // 查询1000次
    printf(">> check 100 times. \n");
    clear_count();
    for (int32_t i = 0; i < checkTimes; i++) {
        // check dml info all
        ret = dml_sysview_query(NULL, g_labelName, NULL, NULL, 0);
        EXPECT_NE(ret, 0);
        ret = check_dml_info_null(0);
        EXPECT_EQ(ret, 0);

        ret = dml_statis_check(stmt, g_labelName, NULL, GMERR_UNDEFINED_TABLE, 0);
        EXPECT_EQ(ret, GMERR_UNDEFINED_TABLE);
    }
}

// 44、	kv插入数据后进行视图查询，查询10000次，预期dml性能展示数据不变；
TEST_F(dml_oper_statis_test, dml_oper_statis_test_044)
{
    // create KV table
    int ret = GmcKvCreateTable(stmt, MS_KVTable_Name_01, MS_config);
    EXPECT_EQ(GMERR_OK, ret);

    void *tableLabel = NULL;
    ret = GmcKvPrepareStmtByLabelName(stmt, MS_KVTable_Name_01);
    EXPECT_EQ(GMERR_OK, ret);
#define MAX_COUNT_KV 10000

    // insert
    printf(">> insert kv\n");
    uint32_t value = 0;
    char Key_name[10] = {0};
    GmcKvTupleT kvInfo = {0};

    for (uint32_t i = 0; i < MAX_COUNT_KV; i++) {
        memset(Key_name, 0, 10);
        snprintf(Key_name, sizeof(Key_name), "Key_%d", i);
        value = i;

        memset(&kvInfo, 0, sizeof(GmcKvTupleT));
        kvInfo.key = Key_name;
        kvInfo.keyLen = strlen(Key_name);
        kvInfo.value = &value;
        kvInfo.valueLen = sizeof(uint32_t);

        ret = GmcKvSet(stmt, Key_name, strlen(Key_name), &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }

    set_count_value(MAX_COUNT_KV);

    // count
    uint32_t count = 0;
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    printf(" count:%u\n", count);

    // 查询1000次
    int32_t checkTimes = 100;
#if defined(ENV_RTOSV2X)
    checkTimes = 5;
#endif
    printf(">> check 100 times. \n");
    for (int i = 0; i < checkTimes; i++) {
        ret = dml_statis_check_kv(stmt, MS_KVTable_Name_01, NULL, 0, 0);
        EXPECT_EQ(ret, 0);

        // check dml info all
        ret = dml_sysview_query(NULL, MS_KVTable_Name_01, NULL, NULL, 0);
        EXPECT_EQ(ret, 0);
        ret = check_dml_info(0, 0, 0);
        EXPECT_EQ(ret, 0);
    }

    // ret = GmcCloseKvTable(stmt);
    // EXPECT_EQ(GMERR_OK, ret);

    ret = GmcKvDropTable(stmt, (char *)MS_KVTable_Name_01);
    EXPECT_EQ(GMERR_OK, ret);
}

// 45、	kv插入数据1K条查询视图然后删除所有数据查询视图，循环10000次，预期dml性能展示数据不变；
TEST_F(dml_oper_statis_test, dml_oper_statis_test_045)
{
    // create KV table
    int ret = GmcKvCreateTable(stmt, MS_KVTable_Name_01, MS_config);
    EXPECT_EQ(GMERR_OK, ret);
    int32_t checkTimes = 100;
// 减少用例循环次数,避免执行超时
#if defined(ENV_RTOSV2X) || defined(ENV_RTOSV2)
    checkTimes = 1;
#endif
    void *tableLabel = NULL;
    ret = GmcKvPrepareStmtByLabelName(stmt, MS_KVTable_Name_01);
    EXPECT_EQ(GMERR_OK, ret);
#define MAX_COUNT_KV 10000

    // insert
    printf(">> insert kv\n");
    uint32_t value = 0;
    char Key_name[10] = {0};
    GmcKvTupleT kvInfo = {0};
    for (uint32_t i = 0; i < MAX_COUNT_KV; i++) {
        memset(Key_name, 0, 10);
        snprintf(Key_name, sizeof(Key_name), "Key_%d", i);
        value = i;

        memset(&kvInfo, 0, sizeof(GmcKvTupleT));
        kvInfo.key = Key_name;
        kvInfo.keyLen = strlen(Key_name);
        kvInfo.value = &value;
        kvInfo.valueLen = sizeof(uint32_t);

        ret = GmcKvSet(stmt, Key_name, strlen(Key_name), &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }

    // count
    uint32_t count = 0;
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    printf(" count:%u\n", count);

    // read
    printf(">> read kv\n");
    for (uint32_t i = 0; i < MAX_COUNT_KV; i++) {
        memset(Key_name, 0, 10);
        snprintf(Key_name, sizeof(Key_name), "Key_%d", i);

        // 查询表中的数据
        char output[128] = {0};
        uint32_t outputLen = sizeof(output);
        ret = GmcKvGet(stmt, Key_name, strlen(Key_name), output, &outputLen);
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(30, *(uint32_t*)output);
        EXPECT_EQ(4, outputLen);
    }

    // delete
    printf(">> delete kv\n");

    for (uint32_t i = 0; i < MAX_COUNT_KV; i++) {
        memset(Key_name, 0, 10);
        snprintf(Key_name, sizeof(Key_name), "Key_%d", i);
        ret = GmcKvRemove(stmt, Key_name, strlen(Key_name));
        EXPECT_EQ(GMERR_OK, ret);

        bool isExist = 0;
        ret = GmcKvIsExist(stmt, Key_name, strlen(Key_name), &isExist);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isExist);
    }

    set_count_value(MAX_COUNT_KV, MAX_COUNT_KV);

    // select
    count = 0;
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    printf(" count:%u\n", count);

    // 查询1000次；
    printf(">> check 100 times. \n");
    for (int32_t i = 0; i < checkTimes; i++) {
        ret = dml_statis_check_kv(stmt, MS_KVTable_Name_01, NULL, 0, 0);
        EXPECT_EQ(ret, 0);

        // check dml info all
        ret = dml_sysview_query(NULL, MS_KVTable_Name_01, NULL, NULL, 0);
        EXPECT_EQ(ret, 0);
        ret = check_dml_info(0, 0, 0);
        EXPECT_EQ(ret, 0);
    }

    // ret = GmcCloseKvTable(stmt);
    // EXPECT_EQ(GMERR_OK, ret);

    printf(">> truncate kv\n");
    // truncate kv table
    ret = GmcKvTruncateTable(stmt, MS_KVTable_Name_01);
    EXPECT_EQ(GMERR_OK, ret);

    // 查询100次；
    printf(">> check 100 times. \n");

    for (int32_t i = 0; i < checkTimes; i++) {
        ret = dml_statis_check_kv(stmt, MS_KVTable_Name_01, NULL, 0, 0);
        EXPECT_EQ(ret, 0);

        // check dml info all
        ret = dml_sysview_query(NULL, MS_KVTable_Name_01, NULL, NULL, 0);
        EXPECT_EQ(ret, 0);
        ret = check_dml_info(0, 0, 0);
        EXPECT_EQ(ret, 0);
    }

    ret = GmcKvDropTable(stmt, (char *)MS_KVTable_Name_01);
    EXPECT_EQ(GMERR_OK, ret);

    // 查询100次；
    printf(">> check 100 times. \n");
    for (int32_t i = 0; i < checkTimes; i++) {
        ret = dml_statis_check_kv(stmt, MS_KVTable_Name_01, NULL, GMERR_UNDEFINED_TABLE, 0);
        EXPECT_EQ(ret, GMERR_UNDEFINED_TABLE);

        // check dml info all
        ret = dml_sysview_query(NULL, MS_KVTable_Name_01, NULL, NULL, 0);
        EXPECT_NE(ret, 0);
        ret = check_dml_info_null(0);
        EXPECT_EQ(ret, 0);
    }
}

// 46 vertex插入数据1K条，kv插入数据1K条，接口查询，统计视图查询，各循环1000次，预期接口查询，dml统计展示数据不变；
TEST_F(dml_oper_statis_test, dml_oper_statis_test_046)
{
    // 创建 vertexLabel_1
    char labelName[128] = "ip4forward00000";
    int32_t checkTimes = 100;
#if defined(ENV_RTOSV2X)
    checkTimes = 1;
#endif
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    int ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    uint64_t count_1;

    // open vertexLabel
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    printf(">> insert \n");
    uint64_t i = 0;
    RESET_COUNT();
    for (i = 0; i < 1000; i++) {
        uint64_t j = i % 900;
        ret = ip4forward00000_set_obj(stmt, j);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(stmt);
        CHECK_SET_COUNT(ret);
    }

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count_1);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count_1);
    SET_COUNT(cn_insert_succ[0], cn_insert_fail[0]);

    printf(">> delete \n");
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    RESET_COUNT();
    for (i = 1000; i < 3000; i++) {
        ret = ip4forward00000_pri_key_set(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcSetIndexKeyName(stmt, "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        CHECK_SET_COUNT(ret);
    }
    SET_COUNT(cn_delete_succ[0], cn_delete_fail[0]);

    // create KV table
    ret = GmcKvCreateTable(stmt, MS_KVTable_Name_01, MS_config);
    EXPECT_EQ(GMERR_OK, ret);

    void *tableLabel = NULL;
    ret = GmcKvPrepareStmtByLabelName(stmt, MS_KVTable_Name_01);
    EXPECT_EQ(GMERR_OK, ret);
#define MAX_COUNT_KV 10000

    // insert
    printf(">> insert kv\n");
    uint32_t value = 0;
    char Key_name[10] = {0};
    GmcKvTupleT kvInfo = {0};
    for (uint32_t i = 0; i < MAX_COUNT_KV; i++) {
        memset(Key_name, 0, 10);
        snprintf(Key_name, sizeof(Key_name), "Key_%d", i);
        value = i;

        memset(&kvInfo, 0, sizeof(GmcKvTupleT));
        kvInfo.key = Key_name;
        kvInfo.keyLen = strlen(Key_name);
        kvInfo.value = &value;
        kvInfo.valueLen = sizeof(uint32_t);

        ret = GmcKvSet(stmt, Key_name, strlen(Key_name), &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }

    // count
    uint32_t count = 0;
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    printf(" count:%u\n", count);

    // read
    printf(">> read kv\n");
    for (uint32_t i = 0; i < MAX_COUNT_KV; i++) {
        memset(Key_name, 0, 10);
        snprintf(Key_name, sizeof(Key_name), "Key_%d", i);

        // 查询表中的数据
        char output[128] = {0};
        uint32_t outputLen = sizeof(output);
        ret = GmcKvGet(stmt, Key_name, strlen(Key_name), output, &outputLen);
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(30, *(uint32_t*)output);
        EXPECT_EQ(4, outputLen);
    }

    // delete
    printf(">> delete kv\n");

    for (uint32_t i = 0; i < MAX_COUNT_KV; i++) {
        memset(Key_name, 0, 10);
        snprintf(Key_name, sizeof(Key_name), "Key_%d", i);
        ret = GmcKvRemove(stmt, Key_name, strlen(Key_name));
        EXPECT_EQ(GMERR_OK, ret);

        bool isExist = 0;
        ret = GmcKvIsExist(stmt, Key_name, strlen(Key_name), &isExist);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, isExist);
    }

    set_count_value(MAX_COUNT_KV, MAX_COUNT_KV, 0, 0, 0, 1);

    // select
    count = 0;
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    printf(" count:%u\n", count);

    // 查询1000次；
    printf(">> check 100 times. \n");
    for (int i = 0; i < 1; i++) {
        ret = dml_statis_check_kv(stmt, MS_KVTable_Name_01, NULL, 0, 0, 0, 1);
        EXPECT_EQ(ret, 0);

        // check dml info all
        ret = dml_sysview_query(NULL, MS_KVTable_Name_01, NULL, NULL, 0);
        EXPECT_EQ(ret, 0);
        ret = check_dml_info(0, 0, 0, 1);
        EXPECT_EQ(ret, 0);
    }

    // ret = GmcCloseKvTable(stmt);
    // EXPECT_EQ(GMERR_OK, ret);

    printf(">> truncate kv\n");
    // truncate kv table
    ret = GmcKvTruncateTable(stmt, MS_KVTable_Name_01);
    EXPECT_EQ(GMERR_OK, ret);

    // 查询100次；
    printf(">> check 100 times. \n");

    for (int32_t i = 0; i < checkTimes; i++) {
        ret = dml_statis_check_kv(stmt, MS_KVTable_Name_01, g_labelName, 0, 0, 0, 1);
        EXPECT_EQ(ret, 0);

        ret = dml_statis_check(stmt, MS_KVTable_Name_01, g_labelName, 0, 0, 1, 0);
        EXPECT_EQ(ret, 0);

        // check dml info all
        ret = dml_sysview_query(NULL, g_labelName, MS_KVTable_Name_01, NULL);
        EXPECT_EQ(ret, 0);
        ret = check_dml_info(0, 0, 0, 0);
        EXPECT_EQ(ret, 0);
        ret = dml_sysview_query(NULL, MS_KVTable_Name_01, MS_KVTable_Name_01, NULL);
        EXPECT_EQ(ret, 0);
        ret = check_dml_info(0, 0, 0, 1);
        EXPECT_EQ(ret, 0);
    }

    // check dml statis view
    ret = dml_sysview_query(NULL, g_labelName, MS_KVTable_Name_01, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info(0, 0);
    EXPECT_EQ(ret, 0);
     ret = dml_sysview_query(NULL, MS_KVTable_Name_01, MS_KVTable_Name_01, NULL);
    EXPECT_EQ(ret, 0);
    ret = check_dml_info(0, 0, 1, 1);
    EXPECT_EQ(ret, 0);

    ret = GmcKvDropTable(stmt, (char *)MS_KVTable_Name_01);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);

    // 查询100次；
    printf(">> check 100 times. \n");
    for (int i = 0; i < 1; i++) {
        ret = dml_statis_check_kv(stmt, MS_KVTable_Name_01, NULL, GMERR_UNDEFINED_TABLE, 0);
        EXPECT_EQ(ret, GMERR_UNDEFINED_TABLE);

        // check dml info all
        ret = dml_sysview_query(NULL, MS_KVTable_Name_01, NULL, NULL, 0);
        EXPECT_NE(ret, 0);
        ret = check_dml_info_null(0);
        EXPECT_EQ(ret, 0);
    }
}
// 查询视图并校验
TEST_F(dml_oper_statis_test, DISABLED_Client1)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    g_needCheckWhenSucc = false;
    GmcConnT *conn_t = NULL;
    GmcStmtT *stmt_t = NULL;
    // 查询视图 100次
    sleep(10);
    int32_t ret = testGmcConnect(&conn_t, &stmt_t);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    printf(">>> process check 100 times.\n");
    // check dml info all
    for (int i = 0; i < 1; i++) {
        // 查询视图
        ret = dml_sysview_query(NULL, g_labelName, NULL, NULL, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 校验视图 
        ret = dml_statis_check(stmt_t, g_labelName, NULL, 0, 0);
    }

    ret = testGmcDisconnect(conn_t, stmt_t);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

void *ThreadStartClient1(void *args)
{
    int ret = system("./func_test02 --gtest_also_run_disabled_tests "
                     "--gtest_filter=dml_oper_statis_test.DISABLED_Client1 >Client1.txt");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}
// 47、
// 多进程并发：1个进程进行DDL(建表/删表/truntcate表)/DML操作，1个进程不断的进行dml性能视图查询，预期数据库服务正常，
// DDL/DML操作成功
TEST_F(dml_oper_statis_test, dml_oper_statis_test_047)
{
    // 创建 vertexLabel_1
    char labelName[128] = "ip4forward00000";

    char *schema_json = NULL;
    pthread_t thrArr[1];
     int ret = pthread_create(&thrArr[0], NULL, ThreadStartClient1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(5);
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
   ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);
    printf(">>> create table.\n");
    uint64_t count;
   
    // open vertexLabel
    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    printf(">> insert \n");
    uint64_t i = 0;
    for (i = 0; i < 1000; i++) {
        ret = ip4forward00000_set_obj(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(stmt);
        EXPECT_EQ(ret, GMERR_OK);
    }

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    printf(">> update \n");
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);
    for (i = 0; i < 1000; i++) {
        ret = ip4forward00000_pri_key_set(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);
        uint32_t value_u32 = i + 1;
        ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        CHECK_OK_RET(ret, "GmcSetVertexProperty primary_label.");

        // ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        // CHECK_OK_RET(ret, "GmcSetVertexProperty attribute_id.");

        ret = GmcSetIndexKeyName(stmt, "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(ret, GMERR_OK);
    }

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    printf(">> replace \n");
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 1000; i < 2000; i++) {
        ret = ip4forward00000_set_obj(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcExecute(stmt);
        EXPECT_EQ(ret, GMERR_OK);
    }

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    printf(">> merge \n");
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_MERGE);
    ASSERT_EQ(GMERR_OK, ret);
    for (i = 2000; i < 3000; i++) {
        ret = ip4forward00000_merge_obj(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(stmt);
        EXPECT_EQ(ret, GMERR_OK);
    }

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    printf(">> delete \n");
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    for (i = 1000; i < 3000; i++) {
        ret = ip4forward00000_pri_key_set(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcSetIndexKeyName(stmt, "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(ret, GMERR_OK);
    }

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);
    pthread_join(thrArr[0], NULL);

    printf(">> truncate \n");
    ret = GmcTruncateVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    EXPECT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 6、	多线程并发：2个线程进行DDL(建表/删表/truntcate表)/DML操作，2个线程不断的进行dml性能视图查询预期数据库服务正常，
// DDL/DML操作成功；线程结束完，主线程查询，可以有预期信息；
#define THR_NUM 2
GmcConnT *g_conn_tht[THR_NUM * 2];
GmcStmtT *g_stmt_tht[THR_NUM * 2];
void *g_vertexLabel_tht[THR_NUM * 2];

void *thread_dml_operation(void *args)
{
    int conn_id = *((int *)args);
    int res = testGmcConnect(&g_conn_tht[conn_id], &g_stmt_tht[conn_id]);
    EXPECT_EQ(GMERR_OK, res);
    int ret = testGmcPrepareStmtByLabelName(g_stmt_tht[conn_id], g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    printf("--- thread:%d.\n", conn_id);
    // open vertexLabel
    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt_tht[conn_id], g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    printf(">> thread:%d insert \n", conn_id);
    uint64_t i = 0;
    for (i = 0; i < 1000; i++) {
        ret = ip4forward00000_set_obj(g_stmt_tht[conn_id], i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(g_stmt_tht[conn_id]);
        // EXPECT_EQ(ret, GMERR_OK);
    }

    uint64_t count;
    ret = GmcGetVertexCount(g_stmt_tht[conn_id], g_labelName, NULL, &count);
    EXPECT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    printf(">> thrad:%d update \n", conn_id);
    for (i = 0; i < 1000; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_tht[conn_id], g_labelName, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        ret = ip4forward00000_pri_key_set(g_stmt_tht[conn_id], i);
        EXPECT_EQ(ret, GMERR_OK);

        uint32_t value_u32 = i + 1;

        ret = GmcSetVertexProperty(
            g_stmt_tht[conn_id], "primary_label", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        CHECK_OK_RET(ret, "GmcSetVertexProperty primary_label.");

        // ret = GmcSetVertexProperty(g_stmt_tht[conn_id], "attribute_id", GMC_DATATYPE_UINT32, &value_u32,
        // sizeof(value_u32)); CHECK_OK_RET(ret, "GmcSetVertexProperty attribute_id.");

        ret = GmcSetIndexKeyName(g_stmt_tht[conn_id], "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_tht[conn_id]);
        EXPECT_EQ(ret, GMERR_OK);
    }

    ret = GmcGetVertexCount(g_stmt_tht[conn_id], g_labelName, NULL, &count);
    EXPECT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    printf(">> thread:%d replace \n", conn_id);
    for (i = 1000; i < 2000; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_tht[conn_id], g_labelName, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);

        ret = ip4forward00000_set_obj(g_stmt_tht[conn_id], i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(g_stmt_tht[conn_id]);
        EXPECT_EQ(ret, GMERR_OK);
    }

    ret = GmcGetVertexCount(g_stmt_tht[conn_id], g_labelName, NULL, &count);
    EXPECT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    printf(">> thread:%d merge \n", conn_id);
    for (i = 2000; i < 3000; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_tht[conn_id], g_labelName, GMC_OPERATION_MERGE);
        EXPECT_EQ(GMERR_OK, ret);

        ret = ip4forward00000_merge_obj(g_stmt_tht[conn_id], i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(g_stmt_tht[conn_id]);
        EXPECT_EQ(ret, GMERR_OK);
    }

    ret = GmcGetVertexCount(g_stmt_tht[conn_id], g_labelName, NULL, &count);
    EXPECT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    printf(">> thread:%d delete \n", conn_id);
    for (i = 1000; i < 3000; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_tht[conn_id], g_labelName, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);

        ret = ip4forward00000_pri_key_set(g_stmt_tht[conn_id], i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcSetIndexKeyName(g_stmt_tht[conn_id], "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_tht[conn_id]);
        EXPECT_EQ(ret, GMERR_OK);
    }

    ret = GmcGetVertexCount(g_stmt_tht[conn_id], g_labelName, NULL, &count);
    EXPECT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);

    printf(">> truncate \n");
    ret = GmcTruncateVertexLabel(g_stmt_tht[conn_id], g_labelName);
    // EXPECT_EQ(GMERR_OK,ret);

    ret = GmcGetVertexCount(g_stmt_tht[conn_id], g_labelName, NULL, &count);
    EXPECT_EQ(GMERR_OK, ret);
    printf(" count:%llu \n", count);
    return ((void *)0);
}

void *thread_dml_query(void *args)
{
    int conn_id = *((int *)args);
    printf("--- thread:%d query.\n", conn_id);
    int32_t checkTimes = 100;
#if defined(ENV_RTOSV2X)
    checkTimes = 5;
#endif
    // 1000次视图查询
    printf(">>> process check 100 times.\n");
    // check dml info all
    
    for (int32_t i = 0; i < checkTimes; i++) {
        GmcConnT *conn_t = NULL;
        GmcStmtT *stmt_t = NULL;

        int ret = testGmcConnect(&conn_t, &stmt_t);
        EXPECT_EQ(GMERR_OK, ret);

        ret = dml_sysview_query(NULL, g_labelName, NULL, NULL, 0);
        EXPECT_EQ(ret, 0);

        ret = dml_statis_check(stmt_t, g_labelName, NULL, 0, 0);
        ret = testGmcDisconnect(conn_t, stmt_t);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return NULL;
}

TEST_F(dml_oper_statis_test, dml_oper_statis_test_048)
{
    // 创建 vertexLabel_1
    char labelName[128] = "ip4forward00000";
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    int ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);
    printf(">>> create table.\n");
    uint64_t count;

    // 多线程并发dml操作，视图查询
    pthread_t thr_arr[32];
    void *thr_ret[32];
    int index[32] = {0};
    memset(g_conn_tht, 0, sizeof(void *) * THR_NUM * 2);

    for (int i = 0; i < 2; i++) {
        index[i] = i;
        pthread_create(&thr_arr[i], NULL, thread_dml_operation, (void *)&index[i]);
        sleep(1);
    }
    for (int i = 2; i < 4; i++) {
        index[i] = i;
        pthread_create(&thr_arr[i], NULL, thread_dml_query, (void *)&index[i]);
        sleep(1);
    }

    for (int i = 0; i < 4; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }

    sleep(1);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 7、	占满1023个连接后进行视图查询，预期视图可查询并且查询正确
TEST_F(dml_oper_statis_test, dml_oper_statis_test_049)
{
    // 建立1023个链接
    // #define MAX_CONN_COUNT 1024
    AddWhiteList(GMERR_INSUFFICIENT_RESOURCES);
    AddWhiteList(GMERR_MEMORY_OPERATE_FAILED);
    AddWhiteList(GMERR_UNEXPECTED_NULL_VALUE);

    int32_t ret;
    GmcConnT **conn_array = (GmcConnT **)malloc(sizeof(void *) * (MAX_CONN_SIZE + 1));
    GmcStmtT **stmt_array = (GmcStmtT **)malloc(sizeof(void *) * (MAX_CONN_SIZE + 1));

    // 获取现存连接数
    uint32_t existConnNum = 0;
    ret = testGetConnNum(&existConnNum);
    EXPECT_EQ(0, ret);

    int32_t con_num = MAX_CONN_SIZE - 1 - existConnNum;
#ifdef ENV_RTOSV2X
    con_num -=1;
#endif
    AW_FUN_Log(LOG_STEP, "conn %d, create: %u.", existConnNum, con_num);
    for (int32_t i = 0; i < con_num; i++) {
        // 创建同步连接
        ret = testGmcConnect(&conn_array[i], &stmt_array[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "create conn %d .", con_num);
    sleep(3);

    // 100次视图查询
    AW_FUN_Log(LOG_STEP, ">>> process check 100 times.");
    // check dml info all
    for (int i = 0; i < 10; i++) {
        int ret = dml_sysview_query(NULL, NULL, NULL, NULL, 0);
        EXPECT_NE(ret, 0);
    }

    for (int32_t i = 0; i < con_num; i++) {
        // 断链同步连接
        ret = testGmcDisconnect(conn_array[i], stmt_array[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    free(conn_array);
    free(stmt_array);
}
