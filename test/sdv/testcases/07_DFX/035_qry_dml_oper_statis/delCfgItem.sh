#!/bin/bash


function usage
{
    echo "$1 help"
    echo "$1 recover"
    echo "$1 \"deviceSize=128\" \"pageSize=64\" ... "
    exit 1
}

fileName="${TEST_HOME}/cfg/sysconfig.ini"
if [ ! -f ${fileName} ]; then
    echo "file:${fileName} is not existant."
    exit 1
fi
cfgName=`grep -w "^sysGmdbCfg" ${TEST_HOME}/cfg/sysconfig.ini | awk '{print $3}'`
if [ "x" = "x${cfgName}" ]; then
    echo "get sysGmdbCfg from ${TEST_HOME}/cfg/sysconfig.ini failed."
    exit 1
fi
if [ ! -f ${cfgName} ]; then
    echo "file:${cfgName} is not existant."
    exit 1
fi
if [ ! -f ${cfgName}_bak ]; then
    cp ${cfgName} ${cfgName}_bak
fi

if [ $# -eq 0 ]; then
    usage $0
fi
if [ $# -eq 1 ]; then
    if [ "recover" = "$1" ]; then 
        echo "recover config file."
        cp ${cfgName}_bak ${cfgName} 
        exit 0
    elif [ "-h" = "$1" -o "help" = "$1" -o "-help" = "$1" ]; then
        usage $0
    fi
fi

for arg in $*                     
do
    keyName=`echo ${arg} | awk -F"=" '{print $1}' | tr -d ' '`
    keyVal=`echo ${arg} | awk -F"=" '{print $2}' | tr -d ' '`
    if [ "x" = "x${keyName}" ]; then
        echo "invalid parameter, msg:${arg}"
	cp ${cfgName}_bak ${cfgName}
	exit 1
    fi
    strBuf=`grep -w "^${keyName}" ${cfgName}`
    if [ $? -ne 0 ]; then
        echo "found key:${keyName} failed."
	cp ${cfgName}_bak ${cfgName}
	exit 1
    fi
    strVal=`grep -w "^${keyName}" ${cfgName} | awk -F"=" '{print $2}' | tr -d ' ' | tr -d '\r'`
    #sed -i "/^${keyName}/c${keyName} = ${keyVal}" ${cfgName}
    sed -i  "/^${keyName}/d" ${cfgName}
    strBuf=`cat ${cfgName} | grep -w "^${keyName}" | grep -w "${keyVal}"`
    if [ "x" = "x${strBuf}" ]; then
        echo "delete key:${keyName} success."
	exit 0
    fi
    cp ${cfgName}_bak ${cfgName}
    echo "delete key:${keyName} failed."
done

exit 0
