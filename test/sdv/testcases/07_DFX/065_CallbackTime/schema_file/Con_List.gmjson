[{"type": "container", "name": "Con_root", "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "string", "size": 7, "nullable": true}, {"name": "P1", "type": "uint32", "nullable": true}, {"name": "P2", "type": "uint32", "nullable": true}, {"name": "P3", "type": "uint32", "nullable": true}, {"name": "P4", "type": "uint32", "nullable": true}, {"name": "P5", "type": "uint32", "nullable": true}], "keys": [{"node": "Con_root", "name": "PK", "fields": ["ID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "Con_List_Child", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32"}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "string", "size": 7, "nullable": true}], "keys": [{"node": "Con_List_Child", "name": "PK", "fields": ["PID", "F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]