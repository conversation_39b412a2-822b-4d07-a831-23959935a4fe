/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 Description  :【DFX】基于表结构重新生成表文本信息
 Notes        :
 001.建一张VertexLabel，只含有必填字段
 002.建一张VertexLabel，version设置为1.0
 003.建一张VertexLabel，version设置为2.0
 004.建一张VertexLabel，schema_version设置为最小值
 005.建一张VertexLabel，schema_version设置为最大值
 006.建一张VertexLabel，type设置为record
 007.建一张VertexLabel，tablespace设置为public
 008.建一张VertexLabel，tablespace设置为非public
 009.建一张VertexLabel，id设置为最小值
 010.建一张VertexLabel，id设置为最大值
 011.建一张VertexLabel，comment设置为空字符串
 012.建一张VertexLabel，comment设置长度为最大值
 013.建一张特殊复杂表，special_complex设置为true
 014.建一张一般复杂表，special_complex设置为false
 015.建一张VertexLabel，max_record_count设置为最小值
 016.建一张VertexLabel，max_record_count设置为最大值
 017.建一张VertexLabel，config中的check_validity设置为true
 018.建一张VertexLabel，config中的check_validity设置为false
 019.建一张VertexLabel，含有super_fields字段
 020.建一张VertexLabel，含有keys字段
 021.建一张VertexLabel，fields中的type设置为long
 022.建一张VertexLabel，fields中的type设置为boolean
 023.建一张VertexLabel，fields中的type设置为uint64:35
 024.建一张VertexLabel，fields中的type设置为uint32:17
 025.建一张VertexLabel，fields中的type设置为uint16:10
 026.建一张VertexLabel，fields中的type设置为uint8:5
 027.建一张VertexLabel，fields中的type设置为string
 028.建一张VertexLabel，fields中的type设置为bytes
 029.建一张VertexLabel，fields中的type设置为fixed
 030.建一张VertexLabel，fields中的type设置为bitmap
 031.建一张VertexLabel，fields中含有default
 032.建一张VertexLabel，fields中的type设置为float且含有default
 033.建一张VertexLabel，fields中的type设置为double且含有default
 034.建一张VertexLabel，fields中的nullable设置为true
 035.建一张VertexLabel，fields中的nullable设置为false
 036.建一张VertexLabel，fields中的type设置为partition
 037.建一张VertexLabel，fields中的type设置为partition且nullable设置为false
 038.建一张VertexLabel，fields中的type设置为resource
 039.建一张VertexLabel，fields中的type设置为resource且nullable设置为false
 040.建一张VertexLabel，fields中的auto_increment设置为true
 041.建一张VertexLabel，fields中的sensitive设置为true
 042.建一张VertexLabel，fields中的comment设置为空字符串
 043.建一张VertexLabel，fields中的comment设置长度为最大值
 044.建一张VertexLabel，fields中的type设置为record
 045.建一张VertexLabel，fields中的type设置为record，定义fixed_array
 046.建一张VertexLabel，fields中的type设置为record，定义array/vector
 047.建一张VertexLabel，fields中的type设置为record，子fields中的comment设置为空字符串
 048.建一张VertexLabel，fields中的type设置为record，子fields中的comment设置长度为最大值
 049.建一张VertexLabel，fields中的type设置为record，子fields中含有super_fields
 050.建一张VertexLabel，含有keys，keys中的index设置为primary（正常事务）
 051.建一张VertexLabel，含有keys，keys中的index设置为primary（轻量化事务）
 052.建一张VertexLabel，含有keys，keys中的index设置为primary（表锁）
 053.建一张VertexLabel，含有keys，keys中的index设置为none
 054.建一张VertexLabel，含有keys，keys中的index设置为localhash（正常事务）
 055.建一张VertexLabel，含有keys，keys中的index设置为localhash（轻量化事务）
 056.建一张VertexLabel，含有keys，keys中的index设置为localhash（表锁）
 057.建一张VertexLabel，含有keys，keys中的index设置为local
 058.建一张VertexLabel，含有keys，keys中的index设置为hashcluster（正常事务）
 059.建一张VertexLabel，含有keys，keys中的index设置为hashcluster（轻量化事务）
 060.建一张VertexLabel，含有keys，keys中的index设置为hashcluster（表锁）
 061.建一张VertexLabel，含有keys，keys中的index设置为lpm4_tree_bitmap
 062.建一张VertexLabel，含有keys，keys中的index设置为lpm6_tree_bitmap
 063.建一张VertexLabel，含有keys，keys中的constraints设置unique为true
 064.建一张VertexLabel，含有keys，keys中的constraints设置unique为false
 065.建一张VertexLabel，含有keys，keys中设置filter
 066.建一张VertexLabel，含有keys，keys中设置comment
 Author       : duhu wx1257740
 Modification :
 create       : 2024/02/18
**************************************************************************** */
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <pthread.h>
#include <semaphore.h>
#include <errno.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"

using namespace std;

#define MAX_COMMENT_LENTH 512
#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];

int ret = 0;
GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
char g_configJson1[128] = "{\"isFastReadUncommitted\":0, \"enableTableLock\":0}";  // 正常事务配置
char g_configJson2[128] = "{\"isFastReadUncommitted\":1, \"enableTableLock\":0}";  // 轻量化事务配置
char g_configJson3[128] = "{\"isFastReadUncommitted\":1, \"enableTableLock\":1}";  // 表锁配置

class VertexLabel_SchemaInfo : public testing::Test {
public:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh -f");
        ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    };
    static void TearDownTestCase()
    {
        ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testEnvClean();
    };
    virtual void SetUp();
    virtual void TearDown();
};

void VertexLabel_SchemaInfo::SetUp()
{
    printf("\n====================TEST:BEGIN====================\n");
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void VertexLabel_SchemaInfo::TearDown()
{
    AW_CHECK_LOG_END();
    ret = testGmcDisconnect(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    printf("\n====================TEST:END====================\n");
}

class VertexLabel_SchemaInfo1 : public testing::Test {
public:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/modifyCfg.sh \"compatibleV3=0\"");
        system("sh $TEST_HOME/tools/start.sh -f");
        ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    };
    static void TearDownTestCase()
    {
        ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testEnvClean();
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    };
    virtual void SetUp();
    virtual void TearDown();
};

void VertexLabel_SchemaInfo1::SetUp()
{
    printf("\n====================TEST:BEGIN====================\n");
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void VertexLabel_SchemaInfo1::TearDown()
{
    AW_CHECK_LOG_END();
    ret = testGmcDisconnect(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    printf("\n====================TEST:END====================\n");
}

// 001.建一张VertexLabel，只含有必填字段
TEST_F(VertexLabel_SchemaInfo, DFX_091_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T90";
    readJanssonFile("schema_file/Vertex_01.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T90", g_toolPath, viewName);
    cout << g_command;
    system(g_command);
    // 校验视图必显示的信息
    ret = executeCommand(g_command, "\"schema_version\": 0", "\"type\": \"record\"", "\"tablespace\": \"public\"", 
                            "\"id\"", "\"name\": \"T90\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "\"special_complex\": false", "\"config\": {", "\"check_validity\": true", "}", 
                            "\"fields\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "\"max_record_count\"");  // 设置该字段不在SCHEMA_INFO中显示，在CONFIG_INFO中显示
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 逐一校验视图不设置则不显示的信息
    ret = executeCommand(g_command, "\"version\": \"1.0\"");
    AW_MACRO_EXPECT_EQ_INT(-1, ret);
    ret = executeCommand(g_command, "\"comment\"");
    AW_MACRO_EXPECT_EQ_INT(-1, ret);
    ret = executeCommand(g_command, "\"super_fields\"");
    AW_MACRO_EXPECT_EQ_INT(-1, ret);
    ret = executeCommand(g_command, "\"keys\"");
    AW_MACRO_EXPECT_EQ_INT(-1, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 002.建一张VertexLabel，version设置为1.0
TEST_F(VertexLabel_SchemaInfo, DFX_091_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T100";
    readJanssonFile("schema_file/Vertex_02.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T100", g_toolPath, viewName);
    cout << g_command;
    ret = executeCommand(g_command, "\"version\": \"1.0\"");
    AW_MACRO_EXPECT_EQ_INT(-1, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 003.建一张VertexLabel，version设置为2.0
TEST_F(VertexLabel_SchemaInfo, DFX_091_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T200";
    readJanssonFile("schema_file/Vertex_03.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T200", g_toolPath, viewName);
    cout << g_command;
    ret = executeCommand(g_command, "\"version\": \"2.0\"");
    AW_MACRO_EXPECT_EQ_INT(-1, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 004.建一张VertexLabel，schema_version设置为最小值
TEST_F(VertexLabel_SchemaInfo, DFX_091_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T100";
    readJanssonFile("schema_file/Vertex_02.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T100", g_toolPath, viewName);
    cout << g_command;
    ret = executeCommand(g_command, "\"schema_version\": 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 005.建一张VertexLabel，schema_version设置为最大值
TEST_F(VertexLabel_SchemaInfo, DFX_091_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T200";
    readJanssonFile("schema_file/Vertex_03.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T200", g_toolPath, viewName);
    cout << g_command;
    ret = executeCommand(g_command, "\"schema_version\": 4294967294");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 006.建一张VertexLabel，type设置为record
TEST_F(VertexLabel_SchemaInfo, DFX_091_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T100";
    readJanssonFile("schema_file/Vertex_02.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T100", g_toolPath, viewName);
    cout << g_command;
    ret = executeCommand(g_command, "\"type\": \"record\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 007.建一张VertexLabel，tablespace设置为public
TEST_F(VertexLabel_SchemaInfo, DFX_091_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T100";
    readJanssonFile("schema_file/Vertex_02.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T100", g_toolPath, viewName);
    cout << g_command;
    ret = executeCommand(g_command, "\"tablespace\": \"public\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 008.建一张VertexLabel，tablespace设置为非public
TEST_F(VertexLabel_SchemaInfo, DFX_091_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 创建表空间
    char tspName[128] = "tabspace1";
    GmcTspCfgT tspCfg;
    tspCfg.tablespaceName = tspName;
    tspCfg.initSize = 0;
    tspCfg.stepSize = 0;
    tspCfg.maxSize = 4;
    ret = GmcCreateTablespace(g_stmt, &tspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *testSchema = NULL;
    const char *labelName = "T150";
    readJanssonFile("schema_file/Vertex_05.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T150", g_toolPath, viewName);
    cout << g_command;
    ret = executeCommand(g_command, "\"tablespace\": \"tabspace1\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropTablespace(g_stmt, tspName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 009.建一张VertexLabel，id设置为最小值
TEST_F(VertexLabel_SchemaInfo, DFX_091_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T100";
    readJanssonFile("schema_file/Vertex_02.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T100", g_toolPath, viewName);
    cout << g_command;
    ret = executeCommand(g_command, "\"id\": 0");
    AW_MACRO_EXPECT_EQ_INT(-1, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 010.建一张VertexLabel，id设置为最大值
TEST_F(VertexLabel_SchemaInfo, DFX_091_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T300";
    readJanssonFile("schema_file/Vertex_04.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T300", g_toolPath, viewName);
    cout << g_command;
    ret = executeCommand(g_command, "\"id\": 983039");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 011.建一张VertexLabel，comment设置为空字符串
TEST_F(VertexLabel_SchemaInfo1, DFX_091_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T100";
    readJanssonFile("schema_file/Vertex_02.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T100", g_toolPath, viewName);
    cout << g_command;
    ret = executeCommand(g_command, "\"comment\": \"\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 012.建一张VertexLabel，comment设置长度为最大值
TEST_F(VertexLabel_SchemaInfo1, DFX_091_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *labelName = "T210";
    string testSchemaStr = R"([{
    "type": "record",
    "name": "T210",
    "comment": ")" + string(MAX_COMMENT_LENTH - 1, 'a') +
                           R"(",
    "fields":[
        {"name":"F0", "type":"uint32", "nullable":false},
        {"name":"F1", "type":"uint32", "nullable":true},
        {"name":"F2", "type":"uint32", "nullable":true}
        ]
    }])";
    char *testSchema = (char *)testSchemaStr.data();
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T210", g_toolPath, viewName);
    cout << g_command;
    string expectStr = "\"comment\": \"" + string(MAX_COMMENT_LENTH - 1, 'a') + "\"";
    const char *expectCh = expectStr.c_str();
    ret = executeCommand(g_command, expectCh);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 013.建一张特殊复杂表，special_complex设置为true
TEST_F(VertexLabel_SchemaInfo, DFX_091_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T200";
    readJanssonFile("schema_file/Vertex_03.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T200", g_toolPath, viewName);
    cout << g_command;
    ret = executeCommand(g_command, "\"special_complex\": true");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 014.建一张一般复杂表，special_complex设置为false
TEST_F(VertexLabel_SchemaInfo, DFX_091_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T100";
    readJanssonFile("schema_file/Vertex_02.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T100", g_toolPath, viewName);
    cout << g_command;
    ret = executeCommand(g_command, "\"special_complex\": false");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 015.建一张VertexLabel，max_record_count设置为最小值
TEST_F(VertexLabel_SchemaInfo, DFX_091_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T100";
    readJanssonFile("schema_file/Vertex_02.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T100", g_toolPath, viewName);
    cout << g_command;
    ret = executeCommand(g_command, "\"max_record_count\": \"1\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 016.建一张VertexLabel，max_record_count设置为最大值
TEST_F(VertexLabel_SchemaInfo, DFX_091_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T200";
    readJanssonFile("schema_file/Vertex_03.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T200", g_toolPath, viewName);
    cout << g_command;
    ret = executeCommand(g_command, "\"max_record_count\": \"9223372036854775807\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 017.建一张VertexLabel，config中的check_validity设置为true
TEST_F(VertexLabel_SchemaInfo, DFX_091_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T100";
    readJanssonFile("schema_file/Vertex_02.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T100", g_toolPath, viewName);
    cout << g_command;
    ret = executeCommand(g_command, "\"config\": {", "\"check_validity\": true", "}");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 018.建一张VertexLabel，config中的check_validity设置为false
TEST_F(VertexLabel_SchemaInfo, DFX_091_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T200";
    readJanssonFile("schema_file/Vertex_03.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T200", g_toolPath, viewName);
    cout << g_command;
    ret = executeCommand(g_command, "\"config\": {", "\"check_validity\": false", "}");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 019.建一张VertexLabel，含有super_fields字段
TEST_F(VertexLabel_SchemaInfo, DFX_091_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T310";
    readJanssonFile("schema_file/Vertex_06.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T310", g_toolPath, viewName);
    cout << g_command;
    ret = executeCommand(g_command, "\"super_fields\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 020.建一张VertexLabel，含有keys字段
TEST_F(VertexLabel_SchemaInfo, DFX_091_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T200";
    readJanssonFile("schema_file/Vertex_03.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T200", g_toolPath, viewName);
    cout << g_command;
    ret = executeCommand(g_command, "\"keys\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 021.建一张VertexLabel，fields中的type设置为long
TEST_F(VertexLabel_SchemaInfo1, DFX_091_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T310";
    readJanssonFile("schema_file/Vertex_06a.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T310", g_toolPath, viewName);
    cout << g_command;
    ret = executeCommand(g_command, "\"name\": \"F1\"", "\"type\": \"int64\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 022.建一张VertexLabel，fields中的type设置为boolean
TEST_F(VertexLabel_SchemaInfo, DFX_091_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T350";
    readJanssonFile("schema_file/Vertex_08.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T350", g_toolPath, viewName);
    cout << g_command;
    ret = executeCommand(g_command, "\"name\": \"F1\"", "\"type\": \"bool\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 023.建一张VertexLabel，fields中的type设置为uint64:35
TEST_F(VertexLabel_SchemaInfo, DFX_091_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T350";
    readJanssonFile("schema_file/Vertex_08.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T350", g_toolPath, viewName);
    cout << g_command;
    ret = executeCommand(g_command, "\"name\": \"F2\"", "\"type\": \"bitfield64\"", "\"size\": 35");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 024.建一张VertexLabel，fields中的type设置为uint32:17
TEST_F(VertexLabel_SchemaInfo, DFX_091_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T350";
    readJanssonFile("schema_file/Vertex_08.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T350", g_toolPath, viewName);
    cout << g_command;
    ret = executeCommand(g_command, "\"name\": \"F3\"", "\"type\": \"bitfield32\"", "\"size\": 17");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 025.建一张VertexLabel，fields中的type设置为uint16:10
TEST_F(VertexLabel_SchemaInfo, DFX_091_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T350";
    readJanssonFile("schema_file/Vertex_08.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T350", g_toolPath, viewName);
    cout << g_command;
    ret = executeCommand(g_command, "\"name\": \"F4\"", "\"type\": \"bitfield16\"", "\"size\": 10");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 026.建一张VertexLabel，fields中的type设置为uint8:5
TEST_F(VertexLabel_SchemaInfo, DFX_091_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T350";
    readJanssonFile("schema_file/Vertex_08.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T350", g_toolPath, viewName);
    cout << g_command;
    ret = executeCommand(g_command, "\"name\": \"F5\"", "\"type\": \"bitfield8\"", "\"size\": 5");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 027.建一张VertexLabel，fields中的type设置为string
TEST_F(VertexLabel_SchemaInfo, DFX_091_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T350";
    readJanssonFile("schema_file/Vertex_08.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T350", g_toolPath, viewName);
    cout << g_command;
    ret = executeCommand(g_command, "\"name\": \"F6\"", "\"type\": \"string\"", "\"size\": 512");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 028.建一张VertexLabel，fields中的type设置为bytes
TEST_F(VertexLabel_SchemaInfo, DFX_091_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T350";
    readJanssonFile("schema_file/Vertex_08.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T350", g_toolPath, viewName);
    cout << g_command;
    ret = executeCommand(g_command, "\"name\": \"F7\"", "\"type\": \"bytes\"", "\"size\": 12");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 029.建一张VertexLabel，fields中的type设置为fixed
TEST_F(VertexLabel_SchemaInfo, DFX_091_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T350";
    readJanssonFile("schema_file/Vertex_08.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T350", g_toolPath, viewName);
    cout << g_command;
    ret = executeCommand(g_command, "\"name\": \"F8\"", "\"type\": \"fixed\"", "\"size\": 15");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 030.建一张VertexLabel，fields中的type设置为bitmap
TEST_F(VertexLabel_SchemaInfo, DFX_091_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T350";
    readJanssonFile("schema_file/Vertex_08.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T350", g_toolPath, viewName);
    cout << g_command;
    ret = executeCommand(g_command, "\"name\": \"F9\"", "\"type\": \"bitmap\"", "\"size\": 1024");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 031.建一张VertexLabel，fields中含有default
TEST_F(VertexLabel_SchemaInfo, DFX_091_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T350";
    readJanssonFile("schema_file/Vertex_08.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T350", g_toolPath, viewName);
    cout << g_command;
    ret = executeCommand(g_command, "\"name\": \"F10\"", "\"type\": \"uint64\"", "\"default\": \"100\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 032.建一张VertexLabel，fields中的type设置为float且含有default
TEST_F(VertexLabel_SchemaInfo, DFX_091_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T350";
    readJanssonFile("schema_file/Vertex_08.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T350", g_toolPath, viewName);
    cout << g_command;
    ret = executeCommand(g_command, "\"name\": \"F11\"", "\"type\": \"float\"", "\"default\": \"10.500000\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 033.建一张VertexLabel，fields中的type设置为double且含有default
TEST_F(VertexLabel_SchemaInfo, DFX_091_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T350";
    readJanssonFile("schema_file/Vertex_08.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T350", g_toolPath, viewName);
    cout << g_command;
    ret = executeCommand(g_command, "\"name\": \"F12\"", "\"type\": \"double\"", "\"default\": \"11.000000\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 034.建一张VertexLabel，fields中的nullable设置为true
TEST_F(VertexLabel_SchemaInfo, DFX_091_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T350";
    readJanssonFile("schema_file/Vertex_08.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T350", g_toolPath, viewName);
    cout << g_command;
    ret = executeCommand(g_command, "\"name\": \"F1\"", "\"type\": \"bool\"", "\"nullable\": true");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 035.建一张VertexLabel，fields中的nullable设置为false
TEST_F(VertexLabel_SchemaInfo, DFX_091_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T350";
    readJanssonFile("schema_file/Vertex_08.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T350", g_toolPath, viewName);
    cout << g_command;
    ret = executeCommand(g_command, "\"name\": \"F0\"", "\"type\": \"uint32\"", "\"nullable\": false");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 036.建一张VertexLabel，fields中的type设置为partition
TEST_F(VertexLabel_SchemaInfo, DFX_091_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T330";
    readJanssonFile("schema_file/Vertex_07.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T330", g_toolPath, viewName);
    cout << g_command;
    ret = executeCommand(g_command, "\"name\": \"F2\"", "\"type\": \"partition\"", "\"nullable\": false");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 037.建一张VertexLabel，fields中的type设置为partition且nullable设置为false
TEST_F(VertexLabel_SchemaInfo, DFX_091_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T350";
    readJanssonFile("schema_file/Vertex_08.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T350", g_toolPath, viewName);
    cout << g_command;
    ret = executeCommand(g_command, "\"name\": \"F13\"", "\"type\": \"partition\"", "\"nullable\": false");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 038.建一张VertexLabel，fields中的type设置为resource
TEST_F(VertexLabel_SchemaInfo, DFX_091_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T350";
    readJanssonFile("schema_file/Vertex_08.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T350", g_toolPath, viewName);
    cout << g_command;
    ret = executeCommand(g_command, "\"name\": \"F14\"", "\"type\": \"resource\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 039.建一张VertexLabel，fields中的type设置为resource且nullable设置为false
TEST_F(VertexLabel_SchemaInfo, DFX_091_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T350";
    readJanssonFile("schema_file/Vertex_08.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T350", g_toolPath, viewName);
    cout << g_command;
    ret = executeCommand(g_command, "\"name\": \"F15\"", "\"type\": \"resource\"", "\"nullable\": false");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 040.建一张VertexLabel，fields中的auto_increment设置为true
TEST_F(VertexLabel_SchemaInfo, DFX_091_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T380";
    const char *configJson = R"({"max_record_count":100000, "auto_increment":100})";
    readJanssonFile("schema_file/Vertex_09.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T380", g_toolPath, viewName);
    cout << g_command;
    ret = executeCommand(g_command, "\"name\": \"F0\"", "\"type\": \"uint32\"", "\"auto_increment\": true");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 041.建一张VertexLabel，fields中的sensitive设置为true
TEST_F(VertexLabel_SchemaInfo, DFX_091_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T350";
    readJanssonFile("schema_file/Vertex_08.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T350", g_toolPath, viewName);
    cout << g_command;
    ret = executeCommand(g_command, "\"name\": \"F16\"", "\"type\": \"uint32\"", "\"sensitive\": true");
    AW_MACRO_EXPECT_EQ_INT(-1, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 042.建一张VertexLabel，fields中的comment设置为空字符串
TEST_F(VertexLabel_SchemaInfo1, DFX_091_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T350";
    readJanssonFile("schema_file/Vertex_08.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T350", g_toolPath, viewName);
    cout << g_command;
    ret = executeCommand(g_command, "\"name\": \"F17\"", "\"type\": \"uint32\"", "\"comment\": \"\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 043.建一张VertexLabel，fields中的comment设置长度为最大值
TEST_F(VertexLabel_SchemaInfo1, DFX_091_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *labelName = "T230";
    string testSchemaStr = R"([{
    "type": "record",
    "name": "T230",
    "fields":[
        {"name":"F0", "type":"uint32", "nullable":false},
        {"name":"F1", "type":"uint32", "nullable":true},
        {"name":"F2", "type":"uint32", "nullable":true, 
        "comment": ")" + string(MAX_COMMENT_LENTH - 1, 'a') +
                           R"("}
        ]
    }])";
    char *testSchema = (char *)testSchemaStr.data();
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T230", g_toolPath, viewName);
    cout << g_command;
    string expectStr = "\"comment\": \"" + string(MAX_COMMENT_LENTH - 1, 'a') + "\"";
    const char *expectCh = expectStr.c_str();
    ret = executeCommand(g_command, expectCh);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 044.建一张VertexLabel，fields中的type设置为record
TEST_F(VertexLabel_SchemaInfo, DFX_091_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T100";
    readJanssonFile("schema_file/Vertex_02.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T100", g_toolPath, viewName);
    cout << g_command;
    ret = executeCommand(g_command, "\"name\": \"c1\"", "\"type\": \"record\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 045.建一张VertexLabel，fields中的type设置为record，定义fixed_array
TEST_F(VertexLabel_SchemaInfo, DFX_091_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T100";
    readJanssonFile("schema_file/Vertex_02.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T100", g_toolPath, viewName);
    cout << g_command;
    ret = executeCommand(g_command, "\"name\": \"c1\"", "\"type\": \"record\"", "\"fixed_array\": true", 
                            "\"size\": 12");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 046.建一张VertexLabel，fields中的type设置为record，定义array/vector
TEST_F(VertexLabel_SchemaInfo, DFX_091_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T200";
    readJanssonFile("schema_file/Vertex_03.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T200", g_toolPath, viewName);
    cout << g_command;
    ret = executeCommand(g_command, "\"name\": \"c1\"", "\"type\": \"record\"", "\"vector\": true", "\"size\": 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 047.建一张VertexLabel，fields中的type设置为record，子fields中的comment设置为空字符串
TEST_F(VertexLabel_SchemaInfo1, DFX_091_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T100";
    readJanssonFile("schema_file/Vertex_02.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T100", g_toolPath, viewName);
    cout << g_command;
    ret = executeCommand(g_command, "\"name\": \"P0\"", "\"type\": \"uint32\"", "\"comment\": \"\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 048.建一张VertexLabel，fields中的type设置为record，子fields中的comment设置长度为最大值
TEST_F(VertexLabel_SchemaInfo1, DFX_091_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *labelName = "T260";
    string testSchemaStr = R"([{
    "type": "record",
    "name": "T260",
    "fields":[
        {"name":"F0", "type":"uint32", "nullable":false},
        {"name":"F1", "type":"uint32", "nullable":true},
        {"name":"c1", "type":"record",
        "fields": [
            {"name":"P0", "type":"uint32", "nullable":false,
            "comment": ")" + string(MAX_COMMENT_LENTH - 1, 'a') + R"("}
            ]
        }
    ]
    }])";
    char *testSchema = (char *)testSchemaStr.data();
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T260", g_toolPath, viewName);
    cout << g_command;
    string expectStr = "\"comment\": \"" + string(MAX_COMMENT_LENTH - 1, 'a') + "\"";
    const char *expectCh = expectStr.c_str();
    ret = executeCommand(g_command, expectCh);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 049.建一张VertexLabel，fields中的type设置为record，子fields中含有super_fields
TEST_F(VertexLabel_SchemaInfo, DFX_091_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T310";
    readJanssonFile("schema_file/Vertex_06.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T310", g_toolPath, viewName);
    cout << g_command;
    ret = executeCommand(g_command, "\"super_fields\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 050.建一张VertexLabel，含有keys，keys中的index设置为primary（正常事务）
TEST_F(VertexLabel_SchemaInfo, DFX_091_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T400";
    readJanssonFile("schema_file/Vertex_010.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, g_configJson1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T400", g_toolPath, viewName);
    cout << g_command;

#if defined ENV_RTOSV2X
    ret = executeCommand(g_command, "\"type\": \"primary\"", "\"hash_type\": \"CHAINED_HASH_INDEX\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#else
    ret = executeCommand(g_command, "\"type\": \"primary\"", "\"hash_type\": \"HASH_INDEX\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 051.建一张VertexLabel，含有keys，keys中的index设置为primary（轻量化事务）
TEST_F(VertexLabel_SchemaInfo, DFX_091_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T400";
    readJanssonFile("schema_file/Vertex_010.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, g_configJson2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T400", g_toolPath, viewName);
    cout << g_command;

#if defined ENV_RTOSV2X
    ret = executeCommand(g_command, "\"type\": \"primary\"", "\"hash_type\": \"CHAINED_HASH_INDEX\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#else
    ret = executeCommand(g_command, "\"type\": \"primary\"", "\"hash_type\": \"HASH_INDEX\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 052.建一张VertexLabel，含有keys，keys中的index设置为primary（表锁）
TEST_F(VertexLabel_SchemaInfo, DFX_091_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T400";
    readJanssonFile("schema_file/Vertex_010.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, g_configJson3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T400", g_toolPath, viewName);
    cout << g_command;
    ret = executeCommand(g_command, "\"type\": \"primary\"", "\"hash_type\": \"CHAINED_HASH_INDEX\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 053.建一张VertexLabel，含有keys，keys中的index设置为none
TEST_F(VertexLabel_SchemaInfo, DFX_091_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T400";
    readJanssonFile("schema_file/Vertex_010.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T400", g_toolPath, viewName);
    cout << g_command;
    ret = executeCommand(g_command, "\"type\": \"none\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 054.建一张VertexLabel，含有keys，keys中的index设置为localhash（正常事务）
TEST_F(VertexLabel_SchemaInfo, DFX_091_054)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T400";
    readJanssonFile("schema_file/Vertex_010.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, g_configJson1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T400", g_toolPath, viewName);
    cout << g_command;
    // 正常事务索引唯一
#if defined ENV_RTOSV2X
    ret = executeCommand(g_command, "\"name\": \"hash_key_uniq\"", "\"type\": \"CHAINED_HASH_INDEX\"", 
                            "\"unique\": true", "\"hash_type\": \"CHAINED_HASH_INDEX\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#else
    ret = executeCommand(g_command, "\"name\": \"hash_key_uniq\"", "\"type\": \"HASH_INDEX\"", "\"unique\": true", 
                            "\"hash_type\": \"HASH_INDEX\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
    // 正常事务索引非唯一
    ret = executeCommand(g_command, "\"type\": \"localhash\"", "\"unique\": false", 
                            "\"hash_type\": \"HASH_LINKLIST_INDEX\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 055.建一张VertexLabel，含有keys，keys中的index设置为localhash（轻量化事务）
TEST_F(VertexLabel_SchemaInfo, DFX_091_055)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T400";
    readJanssonFile("schema_file/Vertex_010.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, g_configJson2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T400", g_toolPath, viewName);
    cout << g_command;
    // 正常事务索引唯一
#if defined ENV_RTOSV2X
    ret = executeCommand(g_command, "\"name\": \"hash_key_uniq\"", "\"type\": \"CHAINED_HASH_INDEX\"", 
                            "\"unique\": true", "\"hash_type\": \"CHAINED_HASH_INDEX\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#else
    ret = executeCommand(g_command, "\"name\": \"hash_key_uniq\"", "\"type\": \"HASH_INDEX\"", "\"unique\": true", 
                            "\"hash_type\": \"HASH_INDEX\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
    // 正常事务索引非唯一
    ret = executeCommand(g_command, "\"type\": \"localhash\"", "\"unique\": false", 
                            "\"hash_type\": \"HASH_LINKLIST_INDEX\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 056.建一张VertexLabel，含有keys，keys中的index设置为localhash（表锁）
TEST_F(VertexLabel_SchemaInfo, DFX_091_056)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T400";
    readJanssonFile("schema_file/Vertex_010.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, g_configJson3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T400", g_toolPath, viewName);
    cout << g_command;
    // 正常事务索引唯一
    ret = executeCommand(g_command, "\"name\": \"hash_key_uniq\"", "\"type\": \"CHAINED_HASH_INDEX\"", 
                            "\"unique\": true", "\"hash_type\": \"CHAINED_HASH_INDEX\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 正常事务索引非唯一
    ret = executeCommand(g_command, "\"type\": \"localhash\"", "\"unique\": false", 
                            "\"hash_type\": \"HASH_LINKLIST_INDEX\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 057.建一张VertexLabel，含有keys，keys中的index设置为local
TEST_F(VertexLabel_SchemaInfo, DFX_091_057)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T400";
    readJanssonFile("schema_file/Vertex_010.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T400", g_toolPath, viewName);
    cout << g_command;
    ret = executeCommand(g_command, "\"type\": \"local\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 058.建一张VertexLabel，含有keys，keys中的index设置为hashcluster（正常事务）
TEST_F(VertexLabel_SchemaInfo1, DFX_091_058)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T400";
    readJanssonFile("schema_file/Vertex_010.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, g_configJson1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T400", g_toolPath, viewName);
    cout << g_command;
    // 正常事务索引唯一
    ret = executeCommand(g_command, "\"name\": \"hashcluster_key_uniq\"", "\"type\": \"HASH_INDEX\"", 
                            "\"unique\": true", "\"hash_type\": \"HASH_INDEX\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 正常事务索引非唯一
    ret = executeCommand(g_command, "\"type\": \"hashcluster\"", "\"unique\": false", 
                            "\"hash_type\": \"ART_INDEX_HASHCLUSTER\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 059.建一张VertexLabel，含有keys，keys中的index设置为hashcluster（轻量化事务）
TEST_F(VertexLabel_SchemaInfo1, DFX_091_059)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T400";
    readJanssonFile("schema_file/Vertex_010.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, g_configJson2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T400", g_toolPath, viewName);
    cout << g_command;
    // 正常事务索引唯一
    ret = executeCommand(g_command, "\"name\": \"hashcluster_key_uniq\"", "\"type\": \"HASH_INDEX\"", 
                            "\"unique\": true", "\"hash_type\": \"HASH_INDEX\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 正常事务索引非唯一
    ret = executeCommand(g_command, "\"type\": \"hashcluster\"", "\"unique\": false", 
                            "\"hash_type\": \"ART_INDEX_HASHCLUSTER\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 060.建一张VertexLabel，含有keys，keys中的index设置为hashcluster（表锁）
TEST_F(VertexLabel_SchemaInfo1, DFX_091_060)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T400";
    readJanssonFile("schema_file/Vertex_010.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, g_configJson3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T400", g_toolPath, viewName);
    cout << g_command;
    // 正常事务索引唯一
    ret = executeCommand(g_command, "\"name\": \"hashcluster_key_uniq\"", "\"type\": \"CHAINED_HASH_INDEX\"", 
                            "\"unique\": true", "\"hash_type\": \"CHAINED_HASH_INDEX\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 正常事务索引非唯一
    ret = executeCommand(g_command, "\"type\": \"hashcluster\"", "\"unique\": false", 
                            "\"hash_type\": \"ART_INDEX_HASHCLUSTER\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 061.建一张VertexLabel，含有keys，keys中的index设置为lpm4_tree_bitmap
TEST_F(VertexLabel_SchemaInfo, DFX_091_061)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T400";
    readJanssonFile("schema_file/Vertex_010.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T400", g_toolPath, viewName);
    cout << g_command;
    ret = executeCommand(g_command, "\"type\": \"lpm4_tree_bitmap\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 062.建一张VertexLabel，含有keys，keys中的index设置为lpm6_tree_bitmap
TEST_F(VertexLabel_SchemaInfo, DFX_091_062)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T450";
    readJanssonFile("schema_file/Vertex_011.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T450", g_toolPath, viewName);
    cout << g_command;
    ret = executeCommand(g_command, "\"type\": \"lpm6_tree_bitmap\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 063.建一张VertexLabel，含有keys，keys中的constraints设置unique为true
TEST_F(VertexLabel_SchemaInfo, DFX_091_063)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T400";
    readJanssonFile("schema_file/Vertex_010.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T400", g_toolPath, viewName);
    cout << g_command;
    ret = executeCommand(g_command, "\"type\": \"primary\"", "\"unique\": true");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 064.建一张VertexLabel，含有keys，keys中的constraints设置unique为false
TEST_F(VertexLabel_SchemaInfo, DFX_091_064)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T400";
    readJanssonFile("schema_file/Vertex_010.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T400", g_toolPath, viewName);
    cout << g_command;
    ret = executeCommand(g_command, "\"type\": \"local\"", "\"unique\": false");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 065.建一张VertexLabel，含有keys，keys中设置filter
TEST_F(VertexLabel_SchemaInfo, DFX_091_065)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T400";
    readJanssonFile("schema_file/Vertex_010.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T400", g_toolPath, viewName);
    cout << g_command;
    ret = executeCommand(g_command, "\"filter\"", "\"operator_type\": \"and\"", "\"property\": \"F4\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 066.建一张VertexLabel，含有keys，keys中设置comment
TEST_F(VertexLabel_SchemaInfo1, DFX_091_066)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char *testSchema = NULL;
    const char *labelName = "T450";
    readJanssonFile("schema_file/Vertex_011.gmjson", &testSchema);
    ASSERT_NE((void *)NULL, testSchema);
    ret = GmcCreateVertexLabel(g_stmt, testSchema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);

    const char *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=T450", g_toolPath, viewName);
    cout << g_command;
    ret = executeCommand(g_command, "\"type\": \"lpm6_tree_bitmap\"", "\"comment\": \"ipv6最长前缀匹配\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
