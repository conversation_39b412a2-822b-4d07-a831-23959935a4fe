#include "tools.h"

class memory_retrieve : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void memory_retrieve::SetUpTestCase(){}

void memory_retrieve::TearDownTestCase(){}

void memory_retrieve::SetUp()
{
    system("sh $TEST_HOME/tools/modifyCfg.sh \"compatibleV3=0\"");
    system("${TEST_HOME}/tools/modifyCfg.sh \"deviceSize=1\"");
    system("${TEST_HOME}/tools/modifyCfg.sh \"trxMonitorEnable=0\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"workerHungThreshold=6,200,300\"");
    system("sh $TEST_HOME/tools/start.sh");
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT data = {0};

    // 异步建连
    ret = testGmcConnect(&g_connAsync, &g_stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = g_namespace;
    nspCfg.tablespaceName = NULL;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观

    ret = GmcCreateNamespaceWithCfgAsync(g_stmtAsync, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // Yang事务配置为乐观+可重复读，需要和namespace保持一致
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = GmcUseNamespaceAsync(g_stmtAsync, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = GmcClearNamespaceAsync(g_stmtAsync, g_namespace, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    AW_CHECK_LOG_BEGIN();
}

void memory_retrieve::TearDown()
{
    AW_CHECK_LOG_END();
    AsyncUserDataT data = {0};
    // 异步删除namespace
    int ret = GmcDropNamespaceAsync(g_stmtAsync, g_namespace, drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 断连
    ret = testGmcDisconnect(g_connAsync, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    ret = close_epoll_thread();
	AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
	GmcDetachAllShmSeg();
	testEnvClean();
}

// 1.tree模型，开启事务，写数据，savepoint 1，视图查询，list表写数据，savepoint 2，视图查询，提交事务，sleep 几秒，视图查询，校验结果
TEST_F(memory_retrieve, DFX_066_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    AsyncUserDataT data = {0};

    const char *tablespaceName = "tsp1";
    GmcTspCfgT tspCfg;
    tspCfg.tablespaceName = tablespaceName;
    tspCfg.initSize = 0;
    tspCfg.stepSize = 0;
    tspCfg.maxSize = 1020;

    ret = GmcCreateTablespaceAsync(g_stmtAsync, &tspCfg, create_tablespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建表
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, g_labelJsonMain, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcCreateVertexLabelAsync(g_stmtAsync, label_json_list, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, label_json_edge, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    YangAllocAllStmt();

    // 开启事务
    ret = start_trans_async(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 准备批量操作
    GmcBatchOptionT batch_option;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batch_option);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batch_option, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batch_option, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batch_option, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_connAsync, &batch_option, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // main
    ret = testGmcPrepareStmtByLabelName(stmt_root, "label_main", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置root字段值
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_root, &node_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t con_F2_value = 1;
    uint32_t con_F3_value = 2;
    uint32_t con_F4_value = 3;
    ret = yang_set_property(node_root, &con_F2_value, "F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property(node_root, &con_F3_value, "F3", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property(node_root, &con_F4_value, "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置node_choice字段值
    ret = GmcYangEditChildNode(node_root, "node_choice", GMC_OPERATION_INSERT, &node_choice);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 设置node_case节点结构
    ret = GmcYangEditChildNode(node_choice, "node_case", GMC_OPERATION_INSERT, &node_case);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t caseF1Value = 4;
    uint32_t caseF2Value = 5;
    ret = yang_set_property(node_case, &caseF1Value, "case_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property(node_case, &caseF2Value, "case_F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加表1批操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list
    ret = testGmcPrepareStmtByLabelName(stmt_list, "label_list", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmt_root, stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_list, &node_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list字段值
    uint32_t list_F3_value = 6;
    uint32_t list_F4_value = 7;
    ret = yang_set_property(node_list, &list_F3_value, "F3", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property(node_list, &list_F4_value, "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加表2批操作
    ret = GmcBatchAddDML(batch, stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    
    // savepoint 1， 视图查询
    ret = GmcTransCreateSavepointAsync(g_connAsync, "sp_1", TransSavePointCb, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 循环merge操作直到CUR_USED_SIZE达到1M
    CurUsedToSize(1, batch);

    // list再写1条数据
    ret = testGmcPrepareStmtByLabelName(stmt_root, "label_main", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置root字段值
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt_list, "label_list", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmt_root, stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_list, &node_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list字段值
    uint32_t list_F3_value_2 = 8;
    uint32_t list_F4_value_2 = 9;
    ret = yang_set_property(node_list, &list_F3_value_2, "F3", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property(node_list, &list_F4_value_2, "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加表2批操作
    ret = GmcBatchAddDML(batch, stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);

    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    //savepoint 2， 视图查询
    ret = GmcTransCreateSavepointAsync(g_connAsync, "sp_2", TransSavePointCb, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system(g_viewCommand);
    int before_commit_mb = GetValueBetweenStr(g_viewCommand, "CUR_USED_SIZE: [", "] MB [");
    int before_commit_kb = GetValueBetweenStr(g_viewCommand, "] MB [", "] KB [");
    int before_commit_b = GetValueBetweenStr(g_viewCommand, "] KB [", "] Byte");
    int before_commit_count_b = 1024 * 1024 * before_commit_mb + 1024 * before_commit_kb + before_commit_b;

    // 提交事务
    ret = testTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sleep(2);
    system(g_viewCommand);
    int after_commit_mb = GetValueBetweenStr(g_viewCommand, "CUR_USED_SIZE: [", "] MB [");
    int after_commit_kb = GetValueBetweenStr(g_viewCommand, "] MB [", "] KB [");
    int after_commit_b = GetValueBetweenStr(g_viewCommand, "] KB [", "] Byte");
    int after_commit_count_b = 1024 * 1024 * after_commit_mb + 1024 * after_commit_kb + after_commit_b;

    // 校验
    EXPECT_LT(after_commit_count_b, before_commit_count_b);

    // 删表
    ret = GmcClearNamespaceAsync(g_stmtAsync, g_namespace, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcDropTablespaceAsync(g_stmtAsync, tablespaceName, drop_tablespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 2.tree模型，开启事务，写数据，savepoint 1，视图查询，merge数据，savepoint 2，视图查询，提交事务，sleep 几秒，视图查询，校验结果
TEST_F(memory_retrieve, DFX_066_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    AsyncUserDataT data = {0};

    const char *tablespaceName = "tsp1";
    GmcTspCfgT tspCfg;
    tspCfg.tablespaceName = tablespaceName;
    tspCfg.initSize = 0;
    tspCfg.stepSize = 0;
    tspCfg.maxSize = 1020;

    ret = GmcCreateTablespaceAsync(g_stmtAsync, &tspCfg, create_tablespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建表
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, g_labelJsonMain, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcCreateVertexLabelAsync(g_stmtAsync, label_json_list, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, label_json_edge, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    YangAllocAllStmt();

    // 开启事务
    ret = start_trans_async(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 准备批量操作
    GmcBatchOptionT batch_option;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batch_option);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batch_option, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batch_option, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batch_option, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_connAsync, &batch_option, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // main
    ret = testGmcPrepareStmtByLabelName(stmt_root, "label_main", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置root字段值
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_root, &node_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t con_F2_value = 1;
    uint32_t con_F3_value = 2;
    uint32_t con_F4_value = 3;
    ret = yang_set_property(node_root, &con_F2_value, "F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property(node_root, &con_F3_value, "F3", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property(node_root, &con_F4_value, "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置node_choice字段值
    ret = GmcYangEditChildNode(node_root, "node_choice", GMC_OPERATION_INSERT, &node_choice);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 设置node_case节点结构
    ret = GmcYangEditChildNode(node_choice, "node_case", GMC_OPERATION_INSERT, &node_case);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t caseF1Value = 4;
    uint32_t caseF2Value = 5;
    ret = yang_set_property(node_case, &caseF1Value, "case_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property(node_case, &caseF2Value, "case_F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加表1批操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list
    ret = testGmcPrepareStmtByLabelName(stmt_list, "label_list", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmt_root, stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_list, &node_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list字段值
    uint32_t list_F3_value = 6;
    uint32_t list_F4_value = 7;
    ret = yang_set_property(node_list, &list_F3_value, "F3", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property(node_list, &list_F4_value, "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加表2批操作
    ret = GmcBatchAddDML(batch, stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    
    // savepoint 1， 视图查询
    ret = GmcTransCreateSavepointAsync(g_connAsync, "sp_1", TransSavePointCb, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 循环merge操作直到CUR_USED_SIZE达到1M
    CurUsedToSize(1, batch);

    // merge 数据
    // main
    ret = testGmcPrepareStmtByLabelName(stmt_root, "label_main", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置root字段值
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_root, &node_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t newConF2Value = 1;
    uint32_t newConF3Value = 2;
    uint32_t newConF4Value = 3;
    ret = yang_set_property(node_root, &newConF2Value, "F2", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property(node_root, &newConF3Value, "F3", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property(node_root, &newConF4Value, "F4", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置node_choice字段值
    ret = GmcYangEditChildNode(node_root, "node_choice", GMC_OPERATION_MERGE, &node_choice);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 设置node_case节点结构
    ret = GmcYangEditChildNode(node_choice, "node_case", GMC_OPERATION_MERGE, &node_case);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t newCaseF1Value = 4;
    uint32_t newCaseF2Value = 5;
    ret = yang_set_property(node_case, &newCaseF1Value, "case_F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property(node_case, &newCaseF2Value, "case_F2", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加表1批操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list
    ret = testGmcPrepareStmtByLabelName(stmt_list, "label_list", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmt_root, stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_list, &node_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list字段值
    uint32_t new_list_F3_value = 6;
    uint32_t new_list_F4_value = 7;
    ret = list_set_key(stmt_list, new_list_F3_value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property(node_list, &new_list_F4_value, "F4", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加表2批操作
    ret = GmcBatchAddDML(batch, stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);

    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    //savepoint 2， 视图查询
    ret = GmcTransCreateSavepointAsync(g_connAsync, "sp_2", TransSavePointCb, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system(g_viewCommand);
    int before_commit_mb = GetValueBetweenStr(g_viewCommand, "CUR_USED_SIZE: [", "] MB [");
    int before_commit_kb = GetValueBetweenStr(g_viewCommand, "] MB [", "] KB [");
    int before_commit_b = GetValueBetweenStr(g_viewCommand, "] KB [", "] Byte");
    int before_commit_count_b = 1024 * 1024 * before_commit_mb + 1024 * before_commit_kb + before_commit_b;

    // 提交事务
    ret = testTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sleep(2);
    system(g_viewCommand);
    int after_commit_mb = GetValueBetweenStr(g_viewCommand, "CUR_USED_SIZE: [", "] MB [");
    int after_commit_kb = GetValueBetweenStr(g_viewCommand, "] MB [", "] KB [");
    int after_commit_b = GetValueBetweenStr(g_viewCommand, "] KB [", "] Byte");
    int after_commit_count_b = 1024 * 1024 * after_commit_mb + 1024 * after_commit_kb + after_commit_b;

    // 校验
    EXPECT_LT(after_commit_count_b, before_commit_count_b);

    ret = GmcClearNamespaceAsync(g_stmtAsync, g_namespace, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = GmcDropTablespaceAsync(g_stmtAsync, tablespaceName, drop_tablespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 3.tree模型，开启事务，写数据，savepoint 1，视图查询，replace数据，savepoint 2，视图查询，提交事务，sleep 几秒，视图查询，校验结果
TEST_F(memory_retrieve, DFX_066_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    AsyncUserDataT data = {0};

    const char *tablespaceName = "tsp1";
    GmcTspCfgT tspCfg;
    tspCfg.tablespaceName = tablespaceName;
    tspCfg.initSize = 0;
    tspCfg.stepSize = 0;
    tspCfg.maxSize = 1020;

    ret = GmcCreateTablespaceAsync(g_stmtAsync, &tspCfg, create_tablespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建表
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, g_labelJsonMain, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcCreateVertexLabelAsync(g_stmtAsync, label_json_list, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, label_json_edge, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    YangAllocAllStmt();

    // 开启事务
    ret = start_trans_async(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 准备批量操作
    GmcBatchOptionT batch_option;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batch_option);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batch_option, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batch_option, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batch_option, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_connAsync, &batch_option, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // main
    ret = testGmcPrepareStmtByLabelName(stmt_root, "label_main", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置root字段值
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_root, &node_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t con_F2_value = 1;
    uint32_t con_F3_value = 2;
    uint32_t con_F4_value = 3;
    ret = yang_set_property(node_root, &con_F2_value, "F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property(node_root, &con_F3_value, "F3", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property(node_root, &con_F4_value, "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置node_choice字段值
    ret = GmcYangEditChildNode(node_root, "node_choice", GMC_OPERATION_INSERT, &node_choice);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 设置node_case节点结构
    ret = GmcYangEditChildNode(node_choice, "node_case", GMC_OPERATION_INSERT, &node_case);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t caseF1Value = 4;
    uint32_t caseF2Value = 5;
    ret = yang_set_property(node_case, &caseF1Value, "case_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property(node_case, &caseF2Value, "case_F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加表1批操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list
    ret = testGmcPrepareStmtByLabelName(stmt_list, "label_list", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmt_root, stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_list, &node_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list字段值
    uint32_t list_F3_value = 6;
    uint32_t list_F4_value = 7;
    ret = yang_set_property(node_list, &list_F3_value, "F3", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property(node_list, &list_F4_value, "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加表2批操作
    ret = GmcBatchAddDML(batch, stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    
    // savepoint 1， 视图查询
    ret = GmcTransCreateSavepointAsync(g_connAsync, "sp_1", TransSavePointCb, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 循环merge操作直到CUR_USED_SIZE达到1M
    CurUsedToSize(1, batch);

    // replace 数据
    // main
    ret = testGmcPrepareStmtByLabelName(stmt_root, "label_main", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置root字段值
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_root, &node_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t newConF2Value = 1;
    uint32_t newConF3Value = 2;
    uint32_t newConF4Value = 3;
    ret = yang_set_property(node_root, &newConF2Value, "F2", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property(node_root, &newConF3Value, "F3", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property(node_root, &newConF4Value, "F4", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置node_choice字段值
    ret = GmcYangEditChildNode(node_root, "node_choice", GMC_OPERATION_INSERT, &node_choice);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 设置node_case节点结构
    ret = GmcYangEditChildNode(node_choice, "node_case", GMC_OPERATION_INSERT, &node_case);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t newCaseF1Value = 4;
    uint32_t newCaseF2Value = 5;
    ret = yang_set_property(node_case, &newCaseF1Value, "case_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property(node_case, &newCaseF2Value, "case_F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加表1批操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list
    ret = testGmcPrepareStmtByLabelName(stmt_list, "label_list", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmt_root, stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_list, &node_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list字段值
    uint32_t new_list_F3_value = 6;
    uint32_t new_list_F4_value = 7;
    ret = list_set_key(stmt_list, new_list_F3_value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property(node_list, &new_list_F4_value, "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加表2批操作
    ret = GmcBatchAddDML(batch, stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);

    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    //savepoint 2， 视图查询
    ret = GmcTransCreateSavepointAsync(g_connAsync, "sp_2", TransSavePointCb, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system(g_viewCommand);
    int before_commit_mb = GetValueBetweenStr(g_viewCommand, "CUR_USED_SIZE: [", "] MB [");
    int before_commit_kb = GetValueBetweenStr(g_viewCommand, "] MB [", "] KB [");
    int before_commit_b = GetValueBetweenStr(g_viewCommand, "] KB [", "] Byte");
    int before_commit_count_b = 1024 * 1024 * before_commit_mb + 1024 * before_commit_kb + before_commit_b;

    // 提交事务
    ret = testTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sleep(2);
    system(g_viewCommand);
    int after_commit_mb = GetValueBetweenStr(g_viewCommand, "CUR_USED_SIZE: [", "] MB [");
    int after_commit_kb = GetValueBetweenStr(g_viewCommand, "] MB [", "] KB [");
    int after_commit_b = GetValueBetweenStr(g_viewCommand, "] KB [", "] Byte");
    int after_commit_count_b = 1024 * 1024 * after_commit_mb + 1024 * after_commit_kb + after_commit_b;

    // 校验
    EXPECT_LT(after_commit_count_b, before_commit_count_b);

    ret = GmcClearNamespaceAsync(g_stmtAsync, g_namespace, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = GmcDropTablespaceAsync(g_stmtAsync, tablespaceName, drop_tablespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 4.tree模型，开启事务，写数据，savepoint 1，视图查询，delete数据，savepoint 2，视图查询，提交事务，sleep 几秒，视图查询，校验结果
TEST_F(memory_retrieve, DFX_066_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    AsyncUserDataT data = {0};

    const char *tablespaceName = "tsp1";
    GmcTspCfgT tspCfg;
    tspCfg.tablespaceName = tablespaceName;
    tspCfg.initSize = 0;
    tspCfg.stepSize = 0;
    tspCfg.maxSize = 1020;

    ret = GmcCreateTablespaceAsync(g_stmtAsync, &tspCfg, create_tablespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建表
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, g_labelJsonMain, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcCreateVertexLabelAsync(g_stmtAsync, label_json_list, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, label_json_edge, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    YangAllocAllStmt();

    // 开启事务
    ret = start_trans_async(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 准备批量操作
    GmcBatchOptionT batch_option;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batch_option);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batch_option, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batch_option, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batch_option, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_connAsync, &batch_option, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // main
    ret = testGmcPrepareStmtByLabelName(stmt_root, "label_main", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置root字段值
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_root, &node_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t con_F2_value = 1;
    uint32_t con_F3_value = 2;
    uint32_t con_F4_value = 3;
    ret = yang_set_property(node_root, &con_F2_value, "F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property(node_root, &con_F3_value, "F3", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property(node_root, &con_F4_value, "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置node_choice字段值
    ret = GmcYangEditChildNode(node_root, "node_choice", GMC_OPERATION_INSERT, &node_choice);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 设置node_case节点结构
    ret = GmcYangEditChildNode(node_choice, "node_case", GMC_OPERATION_INSERT, &node_case);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t caseF1Value = 4;
    uint32_t caseF2Value = 5;
    ret = yang_set_property(node_case, &caseF1Value, "case_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property(node_case, &caseF2Value, "case_F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加表1批操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list
    ret = testGmcPrepareStmtByLabelName(stmt_list, "label_list", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmt_root, stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_list, &node_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list字段值
    uint32_t list_F3_value = 6;
    uint32_t list_F4_value = 7;
    ret = yang_set_property(node_list, &list_F3_value, "F3", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property(node_list, &list_F4_value, "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加表2批操作
    ret = GmcBatchAddDML(batch, stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    
    // savepoint 1， 视图查询
    ret = GmcTransCreateSavepointAsync(g_connAsync, "sp_1", TransSavePointCb, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 循环merge操作直到CUR_USED_SIZE达到1M
    CurUsedToSize(1, batch);

    // delete 数据
    // main
    ret = testGmcPrepareStmtByLabelName(stmt_root, "label_main", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // // 设置root字段值
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加表1批操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);

    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    //savepoint 2， 视图查询
    ret = GmcTransCreateSavepointAsync(g_connAsync, "sp_2", TransSavePointCb, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system(g_viewCommand);
    int before_commit_mb = GetValueBetweenStr(g_viewCommand, "CUR_USED_SIZE: [", "] MB [");
    int before_commit_kb = GetValueBetweenStr(g_viewCommand, "] MB [", "] KB [");
    int before_commit_b = GetValueBetweenStr(g_viewCommand, "] KB [", "] Byte");
    int before_commit_count_b = 1024 * 1024 * before_commit_mb + 1024 * before_commit_kb + before_commit_b;

    // 提交事务
    ret = testTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sleep(2);
    system(g_viewCommand);
    int after_commit_mb = GetValueBetweenStr(g_viewCommand, "CUR_USED_SIZE: [", "] MB [");
    int after_commit_kb = GetValueBetweenStr(g_viewCommand, "] MB [", "] KB [");
    int after_commit_b = GetValueBetweenStr(g_viewCommand, "] KB [", "] Byte");
    int after_commit_count_b = 1024 * 1024 * after_commit_mb + 1024 * after_commit_kb + after_commit_b;

    // 校验
    EXPECT_LT(after_commit_count_b, before_commit_count_b);

    ret = GmcClearNamespaceAsync(g_stmtAsync, g_namespace, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = GmcDropTablespaceAsync(g_stmtAsync, tablespaceName, drop_tablespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 5.tree模型，开启事务，写数据，savepoint 1，视图查询，remove数据，savepoint 2，视图查询，提交事务，sleep 几秒，视图查询，校验结果
TEST_F(memory_retrieve, DFX_066_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    AsyncUserDataT data = {0};

    const char *tablespaceName = "tsp1";
    GmcTspCfgT tspCfg;
    tspCfg.tablespaceName = tablespaceName;
    tspCfg.initSize = 0;
    tspCfg.stepSize = 0;
    tspCfg.maxSize = 1020;

    ret = GmcCreateTablespaceAsync(g_stmtAsync, &tspCfg, create_tablespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建表
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, g_labelJsonMain, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcCreateVertexLabelAsync(g_stmtAsync, label_json_list, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, label_json_edge, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    YangAllocAllStmt();

    // 开启事务
    ret = start_trans_async(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 准备批量操作
    GmcBatchOptionT batch_option;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batch_option);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batch_option, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batch_option, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batch_option, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_connAsync, &batch_option, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // main
    ret = testGmcPrepareStmtByLabelName(stmt_root, "label_main", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置root字段值
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_root, &node_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t con_F2_value = 1;
    uint32_t con_F3_value = 2;
    uint32_t con_F4_value = 3;
    ret = yang_set_property(node_root, &con_F2_value, "F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property(node_root, &con_F3_value, "F3", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property(node_root, &con_F4_value, "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置node_choice字段值
    ret = GmcYangEditChildNode(node_root, "node_choice", GMC_OPERATION_INSERT, &node_choice);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 设置node_case节点结构
    ret = GmcYangEditChildNode(node_choice, "node_case", GMC_OPERATION_INSERT, &node_case);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t caseF1Value = 4;
    uint32_t caseF2Value = 5;
    ret = yang_set_property(node_case, &caseF1Value, "case_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property(node_case, &caseF2Value, "case_F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加表1批操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list
    ret = testGmcPrepareStmtByLabelName(stmt_list, "label_list", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmt_root, stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_list, &node_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list字段值
    uint32_t list_F3_value = 6;
    uint32_t list_F4_value = 7;
    ret = yang_set_property(node_list, &list_F3_value, "F3", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property(node_list, &list_F4_value, "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加表2批操作
    ret = GmcBatchAddDML(batch, stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    
    // savepoint 1， 视图查询
    ret = GmcTransCreateSavepointAsync(g_connAsync, "sp_1", TransSavePointCb, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 循环merge操作直到CUR_USED_SIZE达到1M
    CurUsedToSize(1, batch);

    // remove 数据
    // main
    ret = testGmcPrepareStmtByLabelName(stmt_root, "label_main", GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // // 设置root字段值
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加表1批操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);

    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    //savepoint 2， 视图查询
    ret = GmcTransCreateSavepointAsync(g_connAsync, "sp_2", TransSavePointCb, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system(g_viewCommand);
    int before_commit_mb = GetValueBetweenStr(g_viewCommand, "CUR_USED_SIZE: [", "] MB [");
    int before_commit_kb = GetValueBetweenStr(g_viewCommand, "] MB [", "] KB [");
    int before_commit_b = GetValueBetweenStr(g_viewCommand, "] KB [", "] Byte");
    int before_commit_count_b = 1024 * 1024 * before_commit_mb + 1024 * before_commit_kb + before_commit_b;

    // 提交事务
    ret = testTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sleep(2);
    system(g_viewCommand);
    int after_commit_mb = GetValueBetweenStr(g_viewCommand, "CUR_USED_SIZE: [", "] MB [");
    int after_commit_kb = GetValueBetweenStr(g_viewCommand, "] MB [", "] KB [");
    int after_commit_b = GetValueBetweenStr(g_viewCommand, "] KB [", "] Byte");
    int after_commit_count_b = 1024 * 1024 * after_commit_mb + 1024 * after_commit_kb + after_commit_b;

    // 校验
    EXPECT_LT(after_commit_count_b, before_commit_count_b);

    ret = GmcClearNamespaceAsync(g_stmtAsync, g_namespace, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = GmcDropTablespaceAsync(g_stmtAsync, tablespaceName, drop_tablespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 6.tree模型，开启事务，写数据，savepoint 1，视图查询，list表写数据，savepoint 2，视图查询，回滚事务，sleep 几秒，视图查询，校验结果
TEST_F(memory_retrieve, DFX_066_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    AsyncUserDataT data = {0};

    const char *tablespaceName = "tsp1";
    GmcTspCfgT tspCfg;
    tspCfg.tablespaceName = tablespaceName;
    tspCfg.initSize = 0;
    tspCfg.stepSize = 0;
    tspCfg.maxSize = 1020;

    ret = GmcCreateTablespaceAsync(g_stmtAsync, &tspCfg, create_tablespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建表
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, g_labelJsonMain, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcCreateVertexLabelAsync(g_stmtAsync, label_json_list, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, label_json_edge, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    YangAllocAllStmt();

    // 开启事务
    ret = start_trans_async(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 准备批量操作
    GmcBatchOptionT batch_option;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batch_option);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batch_option, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batch_option, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batch_option, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_connAsync, &batch_option, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // main
    ret = testGmcPrepareStmtByLabelName(stmt_root, "label_main", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置root字段值
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_root, &node_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t con_F2_value = 1;
    uint32_t con_F3_value = 2;
    uint32_t con_F4_value = 3;
    ret = yang_set_property(node_root, &con_F2_value, "F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property(node_root, &con_F3_value, "F3", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property(node_root, &con_F4_value, "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置node_choice字段值
    ret = GmcYangEditChildNode(node_root, "node_choice", GMC_OPERATION_INSERT, &node_choice);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 设置node_case节点结构
    ret = GmcYangEditChildNode(node_choice, "node_case", GMC_OPERATION_INSERT, &node_case);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t caseF1Value = 4;
    uint32_t caseF2Value = 5;
    ret = yang_set_property(node_case, &caseF1Value, "case_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property(node_case, &caseF2Value, "case_F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加表1批操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list
    ret = testGmcPrepareStmtByLabelName(stmt_list, "label_list", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmt_root, stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_list, &node_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list字段值
    uint32_t list_F3_value = 6;
    uint32_t list_F4_value = 7;
    ret = yang_set_property(node_list, &list_F3_value, "F3", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property(node_list, &list_F4_value, "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加表2批操作
    ret = GmcBatchAddDML(batch, stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    
    // savepoint 1， 视图查询
    ret = GmcTransCreateSavepointAsync(g_connAsync, "sp_1", TransSavePointCb, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 循环merge操作直到CUR_USED_SIZE达到1M
    CurUsedToSize(1, batch);

    // list再写1条数据
    ret = testGmcPrepareStmtByLabelName(stmt_root, "label_main", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置root字段值
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt_list, "label_list", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmt_root, stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_list, &node_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list字段值
    uint32_t list_F3_value_2 = 8;
    uint32_t list_F4_value_2 = 9;
    ret = yang_set_property(node_list, &list_F3_value_2, "F3", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property(node_list, &list_F4_value_2, "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加表2批操作
    ret = GmcBatchAddDML(batch, stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);

    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    //savepoint 2， 视图查询
    ret = GmcTransCreateSavepointAsync(g_connAsync, "sp_2", TransSavePointCb, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system(g_viewCommand);
    int before_commit_mb = GetValueBetweenStr(g_viewCommand, "CUR_USED_SIZE: [", "] MB [");
    int before_commit_kb = GetValueBetweenStr(g_viewCommand, "] MB [", "] KB [");
    int before_commit_b = GetValueBetweenStr(g_viewCommand, "] KB [", "] Byte");
    int before_commit_count_b = 1024 * 1024 * before_commit_mb + 1024 * before_commit_kb + before_commit_b;

    // 回滚事务
    ret = GmcTransRollBackAsync(g_connAsync, trans_rollback_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    sleep(2);
    system(g_viewCommand);
    int after_commit_mb = GetValueBetweenStr(g_viewCommand, "CUR_USED_SIZE: [", "] MB [");
    int after_commit_kb = GetValueBetweenStr(g_viewCommand, "] MB [", "] KB [");
    int after_commit_b = GetValueBetweenStr(g_viewCommand, "] KB [", "] Byte");
    int after_commit_count_b = 1024 * 1024 * after_commit_mb + 1024 * after_commit_kb + after_commit_b;

    // 校验
    EXPECT_LT(after_commit_count_b, before_commit_count_b);

    ret = GmcClearNamespaceAsync(g_stmtAsync, g_namespace, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = GmcDropTablespaceAsync(g_stmtAsync, tablespaceName, drop_tablespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 7.图模型，insert
TEST_F(memory_retrieve, DFX_066_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    AsyncUserDataT data = {0};

    const char *tablespaceName = "tsp1";
    GmcTspCfgT tspCfg;
    tspCfg.tablespaceName = tablespaceName;
    tspCfg.initSize = 0;
    tspCfg.stepSize = 0;
    tspCfg.maxSize = 1020;

    ret = GmcCreateTablespaceAsync(g_stmtAsync, &tspCfg, create_tablespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建表
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, label_json_container, g_labelConfigGraph, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, label_json_list, g_labelConfigGraph, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, graph_edge_label, g_labelConfigGraph, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    YangAllocAllStmt();

    // 开启事务
    ret = start_trans_async(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 准备批量操作
    GmcBatchOptionT batch_option;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batch_option);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batch_option, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batch_option, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batch_option, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_connAsync, &batch_option, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt_root, "label_container", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *conRootNode = NULL;
    ret = GmcGetRootNode(stmt_root, &conRootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置root字段值
    uint32_t conF1Value = 2;
    uint32_t con_F2_value = 3;
    ret = yang_set_property_graph(stmt_root, &conF1Value, "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property_graph(stmt_root, &con_F2_value, "F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加choice批操作
    GmcNodeT *ChildNode = NULL;
    ret = GmcYangEditChildNode(conRootNode, "label_choice", GMC_OPERATION_INSERT, &ChildNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *CaseChildNode = NULL;
    ret = GmcYangEditChildNode(ChildNode, "label_case", GMC_OPERATION_INSERT, &CaseChildNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置case字段值
    uint32_t caseF1Value = 5;
    uint32_t caseF2Value = 6;
    ret = yang_set_property_graph1(CaseChildNode, &caseF1Value, "case_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property_graph1(CaseChildNode, &caseF2Value, "case_F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加case批操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list
    ret = testGmcPrepareStmtByLabelName(stmt_list, "label_list", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmt_root, stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list字段值
    uint32_t list_F3_value = 7;
    uint32_t list_F4_value = 8;
    ret = list_set_key(stmt_list, list_F3_value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property_graph(stmt_list, &list_F4_value, "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加表list批操作
    ret = GmcBatchAddDML(batch, stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // savepoint 1， 视图查询
    ret = GmcTransCreateSavepointAsync(g_connAsync, "sp_1", TransSavePointCb, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CurUsedToSizeGraph(1, batch);

    // list再写入第一条数据
    ret = testGmcPrepareStmtByLabelName(stmt_root, "label_container", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list
    ret = testGmcPrepareStmtByLabelName(stmt_list, "label_list", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmt_root, stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list字段值
    uint32_t new_list_F3_value = 9;
    uint32_t new_list_F4_value = 10;
    ret = yang_set_property_graph(stmt_list, &new_list_F3_value, "F3", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property_graph(stmt_list, &new_list_F4_value, "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加表list批操作
    ret = GmcBatchAddDML(batch, stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);

    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    //savepoint 2， 视图查询
    ret = GmcTransCreateSavepointAsync(g_connAsync, "sp_2", TransSavePointCb, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system(g_viewCommand);
    int before_commit_mb = GetValueBetweenStr(g_viewCommand, "CUR_USED_SIZE: [", "] MB [");
    int before_commit_kb = GetValueBetweenStr(g_viewCommand, "] MB [", "] KB [");
    int before_commit_b = GetValueBetweenStr(g_viewCommand, "] KB [", "] Byte");
    int before_commit_count_b = 1024 * 1024 * before_commit_mb + 1024 * before_commit_kb + before_commit_b;

    // 提交事务
    ret = testTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sleep(2);
    system(g_viewCommand);
    int after_commit_mb = GetValueBetweenStr(g_viewCommand, "CUR_USED_SIZE: [", "] MB [");
    int after_commit_kb = GetValueBetweenStr(g_viewCommand, "] MB [", "] KB [");
    int after_commit_b = GetValueBetweenStr(g_viewCommand, "] KB [", "] Byte");
    int after_commit_count_b = 1024 * 1024 * after_commit_mb + 1024 * after_commit_kb + after_commit_b;

    // 校验
    EXPECT_LT(after_commit_count_b, before_commit_count_b);

    ret = GmcClearNamespaceAsync(g_stmtAsync, g_namespace, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = GmcDropTablespaceAsync(g_stmtAsync, tablespaceName, drop_tablespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 8.图模型，merge
TEST_F(memory_retrieve, DFX_066_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    AsyncUserDataT data = {0};

    const char *tablespaceName = "tsp1";
    GmcTspCfgT tspCfg;
    tspCfg.tablespaceName = tablespaceName;
    tspCfg.initSize = 0;
    tspCfg.stepSize = 0;
    tspCfg.maxSize = 1020;

    ret = GmcCreateTablespaceAsync(g_stmtAsync, &tspCfg, create_tablespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建表
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, label_json_container, g_labelConfigGraph, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, label_json_list, g_labelConfigGraph, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, graph_edge_label, g_labelConfigGraph, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    YangAllocAllStmt();

    // 开启事务
    ret = start_trans_async(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 准备批量操作
    GmcBatchOptionT batch_option;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batch_option);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batch_option, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batch_option, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batch_option, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_connAsync, &batch_option, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt_root, "label_container", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *conRootNode = NULL;
    ret = GmcGetRootNode(stmt_root, &conRootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置root字段值
    uint32_t conF1Value = 2;
    uint32_t con_F2_value = 3;
    ret = yang_set_property_graph(stmt_root, &conF1Value, "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property_graph(stmt_root, &con_F2_value, "F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加choice批操作
    GmcNodeT *ChildNode = NULL;
    ret = GmcYangEditChildNode(conRootNode, "label_choice", GMC_OPERATION_INSERT, &ChildNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *CaseChildNode = NULL;
    ret = GmcYangEditChildNode(ChildNode, "label_case", GMC_OPERATION_INSERT, &CaseChildNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置case字段值
    uint32_t caseF1Value = 5;
    uint32_t caseF2Value = 6;
    ret = yang_set_property_graph1(CaseChildNode, &caseF1Value, "case_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property_graph1(CaseChildNode, &caseF2Value, "case_F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加case批操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list
    ret = testGmcPrepareStmtByLabelName(stmt_list, "label_list", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmt_root, stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list字段值
    uint32_t list_F3_value = 7;
    uint32_t list_F4_value = 8;
    ret = list_set_key(stmt_list, list_F3_value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property_graph(stmt_list, &list_F4_value, "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加表list批操作
    ret = GmcBatchAddDML(batch, stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // savepoint 1， 视图查询
    ret = GmcTransCreateSavepointAsync(g_connAsync, "sp_1", TransSavePointCb, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CurUsedToSizeGraph(1, batch);

    // merge
    ret = testGmcPrepareStmtByLabelName(stmt_root, "label_container", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    conRootNode = NULL;
    ret = GmcGetRootNode(stmt_root, &conRootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置root字段值
    uint32_t newConF1Value = 2;
    uint32_t newConF2Value = 3;
    ret = yang_set_property_graph(stmt_root, &newConF1Value, "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property_graph(stmt_root, &newConF2Value, "F2", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加choice批操作
    ChildNode = NULL;
    ret = GmcYangEditChildNode(conRootNode, "label_choice", GMC_OPERATION_MERGE, &ChildNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CaseChildNode = NULL;
    ret = GmcYangEditChildNode(ChildNode, "label_case", GMC_OPERATION_MERGE, &CaseChildNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置case字段值
    uint32_t newCaseF1Value = 5;
    uint32_t newCaseF2Value = 6;
    ret = yang_set_property_graph1(CaseChildNode, &newCaseF1Value, "case_F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property_graph1(CaseChildNode, &newCaseF2Value, "case_F2", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加case批操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list
    ret = testGmcPrepareStmtByLabelName(stmt_list, "label_list", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmt_root, stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list字段值
    uint32_t new_list_F3_value = 7;
    uint32_t new_list_F4_value = 8;
    ret = list_set_key(stmt_list, new_list_F3_value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property_graph(stmt_list, &new_list_F4_value, "F4", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加表list批操作
    ret = GmcBatchAddDML(batch, stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //savepoint 2， 视图查询
    ret = GmcTransCreateSavepointAsync(g_connAsync, "sp_2", TransSavePointCb, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system(g_viewCommand);
    int before_commit_mb = GetValueBetweenStr(g_viewCommand, "CUR_USED_SIZE: [", "] MB [");
    int before_commit_kb = GetValueBetweenStr(g_viewCommand, "] MB [", "] KB [");
    int before_commit_b = GetValueBetweenStr(g_viewCommand, "] KB [", "] Byte");
    int before_commit_count_b = 1024 * 1024 * before_commit_mb + 1024 * before_commit_kb + before_commit_b;

    // 提交事务
    ret = testTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sleep(2);
    system(g_viewCommand);
    int after_commit_mb = GetValueBetweenStr(g_viewCommand, "CUR_USED_SIZE: [", "] MB [");
    int after_commit_kb = GetValueBetweenStr(g_viewCommand, "] MB [", "] KB [");
    int after_commit_b = GetValueBetweenStr(g_viewCommand, "] KB [", "] Byte");
    int after_commit_count_b = 1024 * 1024 * after_commit_mb + 1024 * after_commit_kb + after_commit_b;

    // 校验
    EXPECT_LT(after_commit_count_b, before_commit_count_b);

    ret = GmcClearNamespaceAsync(g_stmtAsync, g_namespace, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = GmcDropTablespaceAsync(g_stmtAsync, tablespaceName, drop_tablespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 9.图模型，replace
TEST_F(memory_retrieve, DFX_066_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    AsyncUserDataT data = {0};

    const char *tablespaceName = "tsp1";
    GmcTspCfgT tspCfg;
    tspCfg.tablespaceName = tablespaceName;
    tspCfg.initSize = 0;
    tspCfg.stepSize = 0;
    tspCfg.maxSize = 1020;

    ret = GmcCreateTablespaceAsync(g_stmtAsync, &tspCfg, create_tablespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建表
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, label_json_container, g_labelConfigGraph, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, label_json_list, g_labelConfigGraph, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, graph_edge_label, g_labelConfigGraph, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    YangAllocAllStmt();

    // 开启事务
    ret = start_trans_async(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 准备批量操作
    GmcBatchOptionT batch_option;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batch_option);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batch_option, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batch_option, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batch_option, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_connAsync, &batch_option, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt_root, "label_container", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *conRootNode = NULL;
    ret = GmcGetRootNode(stmt_root, &conRootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置root字段值
    uint32_t conF1Value = 2;
    uint32_t con_F2_value = 3;
    ret = yang_set_property_graph(stmt_root, &conF1Value, "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property_graph(stmt_root, &con_F2_value, "F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加choice批操作
    GmcNodeT *ChildNode = NULL;
    ret = GmcYangEditChildNode(conRootNode, "label_choice", GMC_OPERATION_INSERT, &ChildNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *CaseChildNode = NULL;
    ret = GmcYangEditChildNode(ChildNode, "label_case", GMC_OPERATION_INSERT, &CaseChildNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置case字段值
    uint32_t caseF1Value = 5;
    uint32_t caseF2Value = 6;
    ret = yang_set_property_graph1(CaseChildNode, &caseF1Value, "case_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property_graph1(CaseChildNode, &caseF2Value, "case_F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加case批操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list
    ret = testGmcPrepareStmtByLabelName(stmt_list, "label_list", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmt_root, stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list字段值
    uint32_t list_F3_value = 7;
    uint32_t list_F4_value = 8;
    ret = list_set_key(stmt_list, list_F3_value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property_graph(stmt_list, &list_F4_value, "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加表list批操作
    ret = GmcBatchAddDML(batch, stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // savepoint 1， 视图查询
    ret = GmcTransCreateSavepointAsync(g_connAsync, "sp_1", TransSavePointCb, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CurUsedToSizeGraph(1, batch);

    // replace
    ret = testGmcPrepareStmtByLabelName(stmt_root, "label_container", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    conRootNode = NULL;
    ret = GmcGetRootNode(stmt_root, &conRootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置root字段值
    uint32_t newConF1Value = 2;
    uint32_t newConF2Value = 3;
    ret = yang_set_property_graph(stmt_root, &newConF1Value, "F1", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property_graph(stmt_root, &newConF2Value, "F2", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加choice批操作
    ChildNode = NULL;
    ret = GmcYangEditChildNode(conRootNode, "label_choice", GMC_OPERATION_INSERT, &ChildNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CaseChildNode = NULL;
    ret = GmcYangEditChildNode(ChildNode, "label_case", GMC_OPERATION_INSERT, &CaseChildNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置case字段值
    uint32_t newCaseF1Value = 5;
    uint32_t newCaseF2Value = 6;
    ret = yang_set_property_graph1(CaseChildNode, &caseF1Value, "case_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property_graph1(CaseChildNode, &caseF2Value, "case_F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加case批操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list
    ret = testGmcPrepareStmtByLabelName(stmt_list, "label_list", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmt_root, stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list字段值
    uint32_t new_list_F3_value = 7;
    uint32_t new_list_F4_value = 8;
    ret = list_set_key(stmt_list, new_list_F3_value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property_graph(stmt_list, &new_list_F4_value, "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加表list批操作
    ret = GmcBatchAddDML(batch, stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //savepoint 2， 视图查询
    ret = GmcTransCreateSavepointAsync(g_connAsync, "sp_2", TransSavePointCb, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system(g_viewCommand);
    int before_commit_mb = GetValueBetweenStr(g_viewCommand, "CUR_USED_SIZE: [", "] MB [");
    int before_commit_kb = GetValueBetweenStr(g_viewCommand, "] MB [", "] KB [");
    int before_commit_b = GetValueBetweenStr(g_viewCommand, "] KB [", "] Byte");
    int before_commit_count_b = 1024 * 1024 * before_commit_mb + 1024 * before_commit_kb + before_commit_b;

    // 提交事务
    ret = testTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sleep(2);
    system(g_viewCommand);
    int after_commit_mb = GetValueBetweenStr(g_viewCommand, "CUR_USED_SIZE: [", "] MB [");
    int after_commit_kb = GetValueBetweenStr(g_viewCommand, "] MB [", "] KB [");
    int after_commit_b = GetValueBetweenStr(g_viewCommand, "] KB [", "] Byte");
    int after_commit_count_b = 1024 * 1024 * after_commit_mb + 1024 * after_commit_kb + after_commit_b;

    // 校验
    EXPECT_LT(after_commit_count_b, before_commit_count_b);

    ret = GmcClearNamespaceAsync(g_stmtAsync, g_namespace, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = GmcDropTablespaceAsync(g_stmtAsync, tablespaceName, drop_tablespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 10.图模型，delete
TEST_F(memory_retrieve, DFX_066_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    AsyncUserDataT data = {0};

    const char *tablespaceName = "tsp1";
    GmcTspCfgT tspCfg;
    tspCfg.tablespaceName = tablespaceName;
    tspCfg.initSize = 0;
    tspCfg.stepSize = 0;
    tspCfg.maxSize = 1020;

    ret = GmcCreateTablespaceAsync(g_stmtAsync, &tspCfg, create_tablespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建表
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, label_json_container, g_labelConfigGraph, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, label_json_list, g_labelConfigGraph, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, graph_edge_label, g_labelConfigGraph, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    YangAllocAllStmt();

    // 开启事务
    ret = start_trans_async(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 准备批量操作
    GmcBatchOptionT batch_option;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batch_option);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batch_option, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batch_option, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batch_option, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_connAsync, &batch_option, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt_root, "label_container", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *conRootNode = NULL;
    ret = GmcGetRootNode(stmt_root, &conRootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置root字段值
    uint32_t conF1Value = 2;
    uint32_t con_F2_value = 3;
    ret = yang_set_property_graph(stmt_root, &conF1Value, "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property_graph(stmt_root, &con_F2_value, "F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加choice批操作
    GmcNodeT *ChildNode = NULL;
    ret = GmcYangEditChildNode(conRootNode, "label_choice", GMC_OPERATION_INSERT, &ChildNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *CaseChildNode = NULL;
    ret = GmcYangEditChildNode(ChildNode, "label_case", GMC_OPERATION_INSERT, &CaseChildNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置case字段值
    uint32_t caseF1Value = 5;
    uint32_t caseF2Value = 6;
    ret = yang_set_property_graph1(CaseChildNode, &caseF1Value, "case_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property_graph1(CaseChildNode, &caseF2Value, "case_F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加case批操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list
    ret = testGmcPrepareStmtByLabelName(stmt_list, "label_list", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmt_root, stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list字段值
    uint32_t list_F3_value = 7;
    uint32_t list_F4_value = 8;
    ret = list_set_key(stmt_list, list_F3_value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property_graph(stmt_list, &list_F4_value, "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加表list批操作
    ret = GmcBatchAddDML(batch, stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // savepoint 1， 视图查询
    ret = GmcTransCreateSavepointAsync(g_connAsync, "sp_1", TransSavePointCb, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CurUsedToSizeGraph(1, batch);

    // delete
    ret = testGmcPrepareStmtByLabelName(stmt_root, "label_container", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加container批操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //savepoint 2， 视图查询
    ret = GmcTransCreateSavepointAsync(g_connAsync, "sp_2", TransSavePointCb, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system(g_viewCommand);
    int before_commit_mb = GetValueBetweenStr(g_viewCommand, "CUR_USED_SIZE: [", "] MB [");
    int before_commit_kb = GetValueBetweenStr(g_viewCommand, "] MB [", "] KB [");
    int before_commit_b = GetValueBetweenStr(g_viewCommand, "] KB [", "] Byte");
    int before_commit_count_b = 1024 * 1024 * before_commit_mb + 1024 * before_commit_kb + before_commit_b;

    // 提交事务
    ret = testTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sleep(2);
    system(g_viewCommand);
    int after_commit_mb = GetValueBetweenStr(g_viewCommand, "CUR_USED_SIZE: [", "] MB [");
    int after_commit_kb = GetValueBetweenStr(g_viewCommand, "] MB [", "] KB [");
    int after_commit_b = GetValueBetweenStr(g_viewCommand, "] KB [", "] Byte");
    int after_commit_count_b = 1024 * 1024 * after_commit_mb + 1024 * after_commit_kb + after_commit_b;

    // 校验
    EXPECT_LT(after_commit_count_b, before_commit_count_b);

    ret = GmcClearNamespaceAsync(g_stmtAsync, g_namespace, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = GmcDropTablespaceAsync(g_stmtAsync, tablespaceName, drop_tablespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 11.图模型，remove
TEST_F(memory_retrieve, DFX_066_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    AsyncUserDataT data = {0};

    const char *tablespaceName = "tsp1";
    GmcTspCfgT tspCfg;
    tspCfg.tablespaceName = tablespaceName;
    tspCfg.initSize = 0;
    tspCfg.stepSize = 0;
    tspCfg.maxSize = 1020;

    ret = GmcCreateTablespaceAsync(g_stmtAsync, &tspCfg, create_tablespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建表
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, label_json_container, g_labelConfigGraph, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, label_json_list, g_labelConfigGraph, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, graph_edge_label, g_labelConfigGraph, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    YangAllocAllStmt();

    // 开启事务
    ret = start_trans_async(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 准备批量操作
    GmcBatchOptionT batch_option;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batch_option);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batch_option, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batch_option, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batch_option, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_connAsync, &batch_option, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt_root, "label_container", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *conRootNode = NULL;
    ret = GmcGetRootNode(stmt_root, &conRootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置root字段值
    uint32_t conF1Value = 2;
    uint32_t con_F2_value = 3;
    ret = yang_set_property_graph(stmt_root, &conF1Value, "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property_graph(stmt_root, &con_F2_value, "F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加choice批操作
    GmcNodeT *ChildNode = NULL;
    ret = GmcYangEditChildNode(conRootNode, "label_choice", GMC_OPERATION_INSERT, &ChildNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *CaseChildNode = NULL;
    ret = GmcYangEditChildNode(ChildNode, "label_case", GMC_OPERATION_INSERT, &CaseChildNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置case字段值
    uint32_t caseF1Value = 5;
    uint32_t caseF2Value = 6;
    ret = yang_set_property_graph1(CaseChildNode, &caseF1Value, "case_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property_graph1(CaseChildNode, &caseF2Value, "case_F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加case批操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list
    ret = testGmcPrepareStmtByLabelName(stmt_list, "label_list", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmt_root, stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list字段值
    uint32_t list_F3_value = 7;
    uint32_t list_F4_value = 8;
    ret = list_set_key(stmt_list, list_F3_value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property_graph(stmt_list, &list_F4_value, "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加表list批操作
    ret = GmcBatchAddDML(batch, stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // savepoint 1， 视图查询
    ret = GmcTransCreateSavepointAsync(g_connAsync, "sp_1", TransSavePointCb, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CurUsedToSizeGraph(1, batch);

    // delete
    ret = testGmcPrepareStmtByLabelName(stmt_root, "label_container", GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加container批操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //savepoint 2， 视图查询
    ret = GmcTransCreateSavepointAsync(g_connAsync, "sp_2", TransSavePointCb, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system(g_viewCommand);
    int before_commit_mb = GetValueBetweenStr(g_viewCommand, "CUR_USED_SIZE: [", "] MB [");
    int before_commit_kb = GetValueBetweenStr(g_viewCommand, "] MB [", "] KB [");
    int before_commit_b = GetValueBetweenStr(g_viewCommand, "] KB [", "] Byte");
    int before_commit_count_b = 1024 * 1024 * before_commit_mb + 1024 * before_commit_kb + before_commit_b;

    // 提交事务
    ret = testTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sleep(2);
    system(g_viewCommand);
    int after_commit_mb = GetValueBetweenStr(g_viewCommand, "CUR_USED_SIZE: [", "] MB [");
    int after_commit_kb = GetValueBetweenStr(g_viewCommand, "] MB [", "] KB [");
    int after_commit_b = GetValueBetweenStr(g_viewCommand, "] KB [", "] Byte");
    int after_commit_count_b = 1024 * 1024 * after_commit_mb + 1024 * after_commit_kb + after_commit_b;

    // 校验
    EXPECT_LT(after_commit_count_b, before_commit_count_b);

    ret = GmcClearNamespaceAsync(g_stmtAsync, g_namespace, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = GmcDropTablespaceAsync(g_stmtAsync, tablespaceName, drop_tablespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 12. 仅euler环境校验归还os
TEST_F(memory_retrieve, DFX_066_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);
    #if defined ENV_EULER
    int ret = 0;
    AsyncUserDataT data = {0};

    const char *tablespaceName = "tsp1";
    GmcTspCfgT tspCfg;
    tspCfg.tablespaceName = tablespaceName;
    tspCfg.initSize = 0;
    tspCfg.stepSize = 0;
    tspCfg.maxSize = 512;

    ret = GmcCreateTablespaceAsync(g_stmtAsync, &tspCfg, create_tablespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建表
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, g_labelJsonMain, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcCreateVertexLabelAsync(g_stmtAsync, label_json_list, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, label_json_edge, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    YangAllocAllStmt();

    // 开启事务
    ret = start_trans_async(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 准备批量操作
    GmcBatchOptionT batch_option;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batch_option);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batch_option, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batch_option, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batch_option, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_connAsync, &batch_option, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // main
    ret = testGmcPrepareStmtByLabelName(stmt_root, "label_main", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置root字段值
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_root, &node_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t con_F2_value = 1;
    uint32_t con_F3_value = 2;
    uint32_t con_F4_value = 3;
    ret = yang_set_property(node_root, &con_F2_value, "F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property(node_root, &con_F3_value, "F3", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property(node_root, &con_F4_value, "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置node_choice字段值
    ret = GmcYangEditChildNode(node_root, "node_choice", GMC_OPERATION_INSERT, &node_choice);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 设置node_case节点结构
    ret = GmcYangEditChildNode(node_choice, "node_case", GMC_OPERATION_INSERT, &node_case);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t caseF1Value = 4;
    uint32_t caseF2Value = 5;
    ret = yang_set_property(node_case, &caseF1Value, "case_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property(node_case, &caseF2Value, "case_F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加表1批操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list
    ret = testGmcPrepareStmtByLabelName(stmt_list, "label_list", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmt_root, stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_list, &node_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list字段值
    uint32_t list_F3_value = 6;
    uint32_t list_F4_value = 7;
    ret = yang_set_property(node_list, &list_F3_value, "F3", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property(node_list, &list_F4_value, "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加表2批操作
    ret = GmcBatchAddDML(batch, stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    
    // savepoint 1， 视图查询
    ret = GmcTransCreateSavepointAsync(g_connAsync, "sp_1", TransSavePointCb, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 循环merge操作直到VmRSS达到100M
    g_strSize = 2048;
    int gmserverId = GetGmserverId();
    snprintf(g_command, MAX_CMD_SIZE, "cat /proc/%d/status", gmserverId);
    VmRSSToSize(g_command, 117 * 1024, batch);

    // list再写1条数据
    ret = testGmcPrepareStmtByLabelName(stmt_root, "label_main", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置root字段值
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt_list, "label_list", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmt_root, stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_list, &node_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list字段值
    uint32_t list_F3_value_2 = 8;
    uint32_t list_F4_value_2 = 9;
    ret = yang_set_property(node_list, &list_F3_value_2, "F3", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property(node_list, &list_F4_value_2, "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加表2批操作
    ret = GmcBatchAddDML(batch, stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);

    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    //savepoint 2， 视图查询
    ret = GmcTransCreateSavepointAsync(g_connAsync, "sp_2", TransSavePointCb, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int before_commit_mb = GetValueBetweenStr(g_viewCommand, "CUR_USED_SIZE: [", "] MB [");
    int before_commit_kb = GetValueBetweenStr(g_viewCommand, "] MB [", "] KB [");
    int before_commit_b = GetValueBetweenStr(g_viewCommand, "] KB [", "] Byte");
    int before_commit_count_b = 1024 * 1024 * before_commit_mb + 1024 * before_commit_kb + before_commit_b;
    int before_commit_rss = GetValueBetweenStr(g_command, "VmRSS:", "kB", "");
    system(g_viewCommand);
    system(g_command);

    // 提交事务
    ret = testTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int wait_time = MultiCheck(g_command, before_commit_rss, before_commit_count_b);
    AW_MACRO_EXPECT_NE_INT(-1, wait_time);
    cout << "You have waited " << wait_time << " seconds and rss and undo memory have decreased." << endl;

    system(g_viewCommand);
    system(g_command);

    int timeout = 0;
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);
    ret = GmcClearNamespaceAsync(g_stmtAsync, g_namespace, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    while (data.status == GMERR_LOCK_NOT_AVAILABLE && timeout < 5) {
        sleep(1);
        ret = GmcClearNamespaceAsync(g_stmtAsync, g_namespace, ClearNSCallbak, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        timeout++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = GmcDropTablespaceAsync(g_stmtAsync, tablespaceName, drop_tablespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    #endif
}
//在一个乐观事务中保存1024个savepoint，使用STORAGE_TRX_STAT视图查询savepoint状况，再进行一次操作，报错1005001和1009017，再进行查询。
TEST_F(memory_retrieve, DFX_066_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_DATA_EXCEPTION);
    AW_ADD_TRUNCATION_WHITE_LIST(1, "sp_1|sp_1|");
    int ret = 0;
 
    AsyncUserDataT data = {0};
    int record_size = 1024;
 
    const char *tablespaceName = "tsp1";
    GmcTspCfgT tspCfg;
    tspCfg.tablespaceName = tablespaceName;
    tspCfg.initSize = 0;
    tspCfg.stepSize = 0;
    tspCfg.maxSize = 1020;
 
    ret = GmcCreateTablespaceAsync(g_stmtAsync, &tspCfg, create_tablespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
 
    // 建表
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, g_labelJsonMain, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
 
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, label_json_list, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
 
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, label_json_edge, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
 
    YangAllocAllStmt();
 
    // 开启事务
    ret = start_trans_async(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
 
    // 准备批量操作
    GmcBatchOptionT batch_option;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batch_option);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batch_option, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batch_option, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batch_option, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_connAsync, &batch_option, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
 
    // main
    ret = testGmcPrepareStmtByLabelName(stmt_root, "label_main", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
 
    // 设置root字段值
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_root, &node_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t con_F2_value = 1;
    uint32_t con_F3_value = 2;
    uint32_t con_F4_value = 3;
    ret = yang_set_property(node_root, &con_F2_value, "F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property(node_root, &con_F3_value, "F3", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property(node_root, &con_F4_value, "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
 
    // 设置node_choice字段值
    ret = GmcYangEditChildNode(node_root, "node_choice", GMC_OPERATION_INSERT, &node_choice);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 设置node_case节点结构
    ret = GmcYangEditChildNode(node_choice, "node_case", GMC_OPERATION_INSERT, &node_case);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
 
    uint32_t caseF1Value = 4;
    uint32_t caseF2Value = 5;
    ret = yang_set_property(node_case, &caseF1Value, "case_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property(node_case, &caseF2Value, "case_F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
 
    // 添加表1批操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
 
    // list
    ret = testGmcPrepareStmtByLabelName(stmt_list, "label_list", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmt_root, stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_list, &node_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
 
    // 设置list字段值
    uint32_t list_F3_value = 6;
    uint32_t list_F4_value = 7;
    ret = yang_set_property(node_list, &list_F3_value, "F3", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = yang_set_property(node_list, &list_F4_value, "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
 
    // 添加表2批操作
    ret = GmcBatchAddDML(batch, stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
 
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    
    // savepoint 1024次，视图查询
    EXPECT_EQ(GMERR_OK, ret);
    for(int32_t i = 1; i <= record_size; i++){
        ret = GmcTransCreateSavepointAsync(g_connAsync, "sp_1", TransSavePointCb, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
 
    ret = snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$STORAGE_TRX_DETAIL", g_toolPath);
    ret = executeCommand(g_command, "SAVE_POINT_ID: 1024");
 
    //加白名单
    char g_errorCode01[MAX_CMD_SIZE] = {0};
    char g_errorCode02[MAX_CMD_SIZE] = {0};
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_RESTRICT_VIOLATION);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(2,g_errorCode01,g_errorCode02);
 
    //再savepoint一次
    ret = GmcTransCreateSavepointAsync(g_connAsync, "sp_1", TransSavePointCb, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "SAVE_POINT_ID: 1024");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "SAVE_POINT_ID: 1025");
    AW_MACRO_EXPECT_EQ_INT(-1, ret);
 
    ret = GmcClearNamespaceAsync(g_stmtAsync, g_namespace, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
 
    ret = GmcDropTablespaceAsync(g_stmtAsync, tablespaceName, drop_tablespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
