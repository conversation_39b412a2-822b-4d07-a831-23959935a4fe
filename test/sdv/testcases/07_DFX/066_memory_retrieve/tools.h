/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: included by is_config_filter_async_test.cpp & is_config_filter_test.cpp
 * Author: liwenhai
 * Create: 2022-8-11
 */
#ifndef TOOL_H
#define TOOL_H
#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/prctl.h>
#include <sys/socket.h>
#include <sys/epoll.h>
#include <assert.h>
#include <errno.h>
#include <stdint.h>
#include <typeinfo>
#include <limits.h>
#include <iostream>
#include <fstream>
#include "gtest/gtest.h"
#include "jansson.h"
#include "t_datacom_lite.h"
using namespace std;

#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];
GmcConnT *conn = NULL;
GmcStmtT *stmt = NULL;
GmcConnT *g_connAsync = NULL;
GmcStmtT *g_stmtAsync = NULL;
GmcTxConfigT g_mSTrxConfig;
const char *g_namespace = "NamespaceA";
const char *g_namespaceUserName = "abc";
int g_strSize = 300;
const char *g_viewCommand = "gmsysview -q V\\$CATA_TABLESPACE_INFO";

// config
const char *g_labelConfig = R"(
{
    "max_record_count":10000,
    "isFastReadUncommitted":0,
    "auto_increment":1,
    "yang_model":1
})";

// config
static const char *g_labelConfigGraph =
    R"({"max_record_count":99999, "auto_increment":1, "isFastReadUncommitted":0, "yang_model":1})";

// tree
const char *g_labelJsonMain = R"(
{
    "type":"container",
    "name":"label_main",
    "tablespace": "tsp1",
    "fields":[
        {"name":"F1", "type":"uint32", "nullable":false},
        {"name":"F2", "type":"uint32", "nullable":false},
        {"name":"F3", "type":"uint32", "nullable":false},
        {"name":"F4", "type":"uint32", "nullable":false},
        {
            "type":"choice",
            "name":"node_choice",
            "fields":[
                {
                    "type":"case",
                    "name":"node_case",
                    "fields":[
                        {"name":"case_F1", "type":"uint32", "nullable":false},
                        {"name":"case_F2", "type":"uint32", "nullable":true}
                    ]
                }
            ]
        }
    ],
    "keys":[
        {
            "node":"label_main",
            "name":"pk",
            "fields":["F1"],
            "index":{"type":"primary"},
            "constraints":{"unique":true}
        }
    ]
}
)";

const char *label_json_list = R"(
{
    "type":"list",
    "name":"label_list",
    "tablespace": "tsp1",
    "fields":[
        {"name":"F1", "type":"uint32", "nullable":false, "auto_increment": true},
        {"name":"F2", "type":"uint32", "nullable":false},
        {"name":"F3", "type":"uint32", "nullable":false},
        {"name":"F4", "type":"uint32", "nullable":false},
        {"name":"F5_str", "type":"string", "nullable":true},
        {"name":"F6_str", "type":"string", "nullable":true},
        {"name":"F7_str", "type":"string", "nullable":true},
        {"name":"F8_str", "type":"string", "nullable":true},
        {"name":"F9_str", "type":"string", "nullable":true},
        {"name":"F10_str", "type":"string", "nullable":true}
    ],
    "keys":[
        {
            "node":"label_list",
            "name":"pk",
            "fields":["F2", "F3"],
            "index":{"type":"primary"},
            "constraints":{"unique":true}
        }
    ]
}
)";

const char *label_json_edge = R"(
[
    {
        "name":"edge_label",
        "tablespace": "tsp1",
        "source_vertex_label":"label_main",
        "dest_vertex_label":"label_list",
        "source_node_path":"/",
        "dest_node_path":"/",
        "constraint":{
            "operator_type":"and",
            "conditions":[
                {
                    "source_property": "F1",
                    "dest_property": "F2"
                }
            ]
        }
    }
]
)";

// 图
const char *label_json_container = R"(
    {
        "type":"container",
        "name":"label_container",
        "tablespace": "tsp1",
        "fields":[
            {"name":"ID", "type":"uint32", "nullable":false},
            {"name":"F1", "type":"uint32", "nullable":false},
            {"name":"F2", "type":"uint32", "nullable":false},
            {
                "type":"choice",
                "name":"label_choice",
                "tablespace": "tsp1",
                "fields":[
                    {
                        "type":"case",
                        "name":"label_case",
                        "tablespace": "tsp1",
                        "fields":[
                            {"name":"case_F1", "type":"uint32", "nullable":false},
                            {"name":"case_F2", "type":"uint32", "nullable":true}
                        ]
                    }
                ]
            }
        ],
        "keys":[
            {
                "node":"label_container",
                "name":"pk",
                "fields":["ID"],
                "index":{"type":"primary"},
                "constraints":{"unique":true}
            }
        ]
    }
)";

const char *graph_edge_label = R"(
[
    {
        "name":"container_to_list",
        "tablespace": "tsp1",
        "source_vertex_label":"label_container",
        "dest_vertex_label":"label_list",
        "constraint":{
            "operator_type":"and",
            "conditions":[
                {
                    "source_property": "ID",
                    "dest_property": "F2"
                }
            ]
        }
    }
]
)";

// 异步启动事务
int start_trans_async(GmcConnT *conn, GmcTxConfigT Config)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = GmcTransStartAsync(conn, &Config, trans_start_callback, &data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = testWaitAsyncRecv(&data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        return ret;
    }
}

// 异步提交事务
int testTransCommitAsync(GmcConnT *conn_async)
{
    AsyncUserDataT data = {0};
    int ret = GmcTransCommitAsync(conn_async, trans_commit_callback, &data);
    RETURN_IFERR(ret);
    ret = testWaitAsyncRecv(&data);
    RETURN_IFERR(ret);
    RETURN_IFERR(data.status);
    return ret;
}

GmcStmtT *stmt_root = NULL;
GmcStmtT *stmt_choice = NULL;
GmcStmtT *stmt_case = NULL;
GmcStmtT *stmt_list = NULL;

GmcNodeT *node_root = NULL;
GmcNodeT *node_choice = NULL;
GmcNodeT *node_case = NULL;
GmcNodeT *node_list = NULL;

void YangAllocAllStmt()
{
    int ret = GmcAllocStmt(g_connAsync, &stmt_root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(g_connAsync, &stmt_choice);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(g_connAsync, &stmt_case);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(g_connAsync, &stmt_list);
    EXPECT_EQ(GMERR_OK, ret);
}

int yang_set_property(GmcNodeT *node, void *value, const char *fieldName, GmcYangPropOpTypeE optype)
{
    GmcPropValueT propValue;
    memcpy(propValue.propertyName, fieldName, (strlen(fieldName) + 1));
    propValue.type = GMC_DATATYPE_UINT32;
    propValue.value = value;
    propValue.size = sizeof(uint32_t);
    int ret = GmcYangSetNodeProperty(node, &propValue, optype);
    return ret;
}

string WriteStringToSpecialSize(int length, string &str)
{
    while (str.length() < length) {
        str += "a";
    }
    return str;
}

int yang_set_property_str(GmcNodeT *node, void *value, const char *fieldName, GmcYangPropOpTypeE optype)
{
    GmcPropValueT propValue;
    memcpy(propValue.propertyName, fieldName, (strlen(fieldName) + 1));
    propValue.type = GMC_DATATYPE_STRING;
    propValue.value = value;
    propValue.size = g_strSize;
    int ret = GmcYangSetNodeProperty(node, &propValue, optype);
    return ret;
}

int yang_set_property_graph(GmcStmtT *stmt, void *value, const char *fieldName, GmcYangPropOpTypeE optype)
{
    GmcPropValueT propValue;
    memcpy(propValue.propertyName, fieldName, (strlen(fieldName) + 1));
    propValue.type = GMC_DATATYPE_UINT32;
    propValue.value = value;
    propValue.size = sizeof(uint32_t);
    int ret = GmcYangSetVertexProperty(stmt, &propValue, optype);
    return ret;
}

int yang_set_property_graph1(GmcNodeT *node, void *value, const char *fieldName, GmcYangPropOpTypeE optype)
{
    GmcPropValueT propValue;
    memcpy(propValue.propertyName, fieldName, (strlen(fieldName) + 1));
    propValue.type = GMC_DATATYPE_UINT32;
    propValue.value = value;
    propValue.size = sizeof(uint32_t);
    int ret = GmcYangSetNodeProperty(node, &propValue, optype);
    return ret;
}

int yang_set_property_str_graph(GmcStmtT *stmt, void *value, const char *fieldName, GmcYangPropOpTypeE optype)
{
    GmcPropValueT propValue;
    memcpy(propValue.propertyName, fieldName, (strlen(fieldName) + 1));
    propValue.type = GMC_DATATYPE_STRING;
    propValue.value = value;
    propValue.size = g_strSize;
    int ret = GmcYangSetVertexProperty(stmt, &propValue, optype);
    return ret;
}

int get_value(const char *command, string key_word)
{
    string value_str;
    int value;
    char cmdOutPut[1024] = {0};
    FILE *pf = popen(command, "r");
    if (pf == NULL) {
        cout << endl << "WRONG!" << endl;
        return -1;
    }
    while (fgets(cmdOutPut, sizeof(cmdOutPut), pf) != NULL) {
        value_str.assign(cmdOutPut);
        string::size_type idx = value_str.find(key_word);
        if (idx != string::npos) {
            value_str = value_str.substr(value_str.find(key_word) + key_word.length());
            value = stoi(value_str);
            break;
        }
    }
    pclose(pf);

    return value;
}

// set_key
int list_set_key(GmcStmtT *stmt, uint32_t keyvalue)
{
    int ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &keyvalue, sizeof(uint32_t));
    RETURN_IFERR(ret);
    ret = GmcSetIndexKeyName(stmt, "pk");
    RETURN_IFERR(ret);
    return ret;
}

int container_set_key(GmcStmtT *stmt, uint32_t keyvalue)
{
    int ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &keyvalue, sizeof(uint32_t));
    RETURN_IFERR(ret);
    ret = GmcSetIndexKeyName(stmt, "pk");
    RETURN_IFERR(ret);
    return ret;
}

// 函数
string trim(string &str)
{
    str.erase(0, str.find_first_not_of(" "));
    str.erase(str.find_last_not_of(" ") + 1);
    return str;
}

int GetValueBetweenStr(const char *command, string head_str, string tail_str, string start_mark = "public")
{
    int value = 0;
    char cmdOutPut[1024] = {0};
    string str;
    FILE *pf = popen(command, "r");
    bool start = false;
    while (fgets(cmdOutPut, sizeof(cmdOutPut), pf) != NULL) {
        str.assign(cmdOutPut);
        if (!start && str.find(start_mark) != string::npos) {
            start = true;
        }
        if (start && str.find(head_str) != string::npos && str.find(tail_str) != string::npos) {
            str = str.substr(str.find(head_str) + head_str.length());
            str = str.substr(0, str.find(tail_str));
            str = trim(str);
            value = stoi(str);
            pclose(pf);
            return value;
        }
    }
    pclose(pf);
    return value;
}

int Merge(GmcBatchT *batch)
{
    AsyncUserDataT data = {0};
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt_root, "label_main", GMC_OPERATION_MERGE);
    RETURN_IFERR(ret);
    // 设置root字段值
    ret = GmcYangSetRoot(batch, stmt_root);
    RETURN_IFERR(ret);
    ret = GmcGetRootNode(stmt_root, &node_root);
    RETURN_IFERR(ret);
    uint32_t newConF2Value = 101;
    uint32_t newConF3Value = 102;
    uint32_t newConF4Value = 103;
    ret = yang_set_property(node_root, &newConF2Value, "F2", GMC_YANG_PROPERTY_OPERATION_MERGE);
    RETURN_IFERR(ret);
    ret = yang_set_property(node_root, &newConF3Value, "F3", GMC_YANG_PROPERTY_OPERATION_MERGE);
    RETURN_IFERR(ret);
    ret = yang_set_property(node_root, &newConF4Value, "F4", GMC_YANG_PROPERTY_OPERATION_MERGE);
    RETURN_IFERR(ret);

    // 设置node_choice字段值
    ret = GmcYangEditChildNode(node_root, "node_choice", GMC_OPERATION_MERGE, &node_choice);
    RETURN_IFERR(ret);

    // 设置node_case节点结构
    ret = GmcYangEditChildNode(node_choice, "node_case", GMC_OPERATION_MERGE, &node_case);
    RETURN_IFERR(ret);

    uint32_t newCaseF1Value = 104;
    uint32_t newCaseF2Value = 105;
    ret = yang_set_property(node_case, &newCaseF1Value, "case_F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    RETURN_IFERR(ret);
    ret = yang_set_property(node_case, &newCaseF2Value, "case_F2", GMC_YANG_PROPERTY_OPERATION_MERGE);
    RETURN_IFERR(ret);

    // 添加表1批操作
    ret = GmcBatchAddDML(batch, stmt_root);
    RETURN_IFERR(ret);

    // list
    ret = testGmcPrepareStmtByLabelName(stmt_list, "label_list", GMC_OPERATION_MERGE);
    RETURN_IFERR(ret);
    ret = GmcYangBindChild(batch, stmt_root, stmt_list);
    RETURN_IFERR(ret);
    ret = GmcGetRootNode(stmt_list, &node_list);
    RETURN_IFERR(ret);

    // 设置list字段值
    uint32_t newListF3Value = 106;
    uint32_t newListF4Value = 107;
    string mark = "";
    string listStrValueStr = WriteStringToSpecialSize(g_strSize, mark);
    char *listStrValue = (char *)listStrValueStr.c_str();
    ret = list_set_key(stmt_list, newListF3Value);
    RETURN_IFERR(ret);
    ret = yang_set_property(node_list, &newListF4Value, "F4", GMC_YANG_PROPERTY_OPERATION_MERGE);
    RETURN_IFERR(ret);
    ret = yang_set_property_str(node_list, listStrValue, "F5_str", GMC_YANG_PROPERTY_OPERATION_MERGE);
    RETURN_IFERR(ret);
    ret = yang_set_property_str(node_list, listStrValue, "F6_str", GMC_YANG_PROPERTY_OPERATION_MERGE);
    RETURN_IFERR(ret);
    ret = yang_set_property_str(node_list, listStrValue, "F7_str", GMC_YANG_PROPERTY_OPERATION_MERGE);
    RETURN_IFERR(ret);
    ret = yang_set_property_str(node_list, listStrValue, "F8_str", GMC_YANG_PROPERTY_OPERATION_MERGE);
    RETURN_IFERR(ret);
    ret = yang_set_property_str(node_list, listStrValue, "F9_str", GMC_YANG_PROPERTY_OPERATION_MERGE);
    RETURN_IFERR(ret);
    ret = yang_set_property_str(node_list, listStrValue, "F10_str", GMC_YANG_PROPERTY_OPERATION_MERGE);
    RETURN_IFERR(ret);

    // 添加表2批操作
    ret = GmcBatchAddDML(batch, stmt_list);
    RETURN_IFERR(ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    RETURN_IFERR(ret);
    ret = testWaitAsyncRecv(&data);
    RETURN_IFERR(ret);
    RETURN_IFERR(data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    return ret;
}

int CurUsedToSize(int aimMbSize, GmcBatchT *batch)
{
    int mbSize = 0;
    int ret = 0;
    AsyncUserDataT data = {0};
    while (mbSize < aimMbSize) {
        for (int i = 0; i < 50; i++) {
            // savepoint 1， 视图查询
            ret = GmcTransCreateSavepointAsync(g_connAsync, "sp_1", TransSavePointCb, &data);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testWaitAsyncRecv(&data);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            Merge(batch);
            ret = GmcTransReleaseSavepointAsync(g_connAsync, "sp_1", trans_start_callback, &data);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testWaitAsyncRecv(&data);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        }
        mbSize = GetValueBetweenStr(g_viewCommand, "CUR_USED_SIZE: [", "] MB [");
    }
    return 0;
}

int VmRSSToSize(const char *command, int aimSize, GmcBatchT *batch)
{
    int vmRSSSize = 0;
    int ret = 0;
    AsyncUserDataT data = {0};
    while (vmRSSSize < aimSize) {
        for (int i = 0; i < 50; i++) {
            ret = GmcTransCreateSavepointAsync(g_connAsync, "sp_1", TransSavePointCb, &data);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testWaitAsyncRecv(&data);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            Merge(batch);
            ret = GmcTransReleaseSavepointAsync(g_connAsync, "sp_1", trans_start_callback, &data);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testWaitAsyncRecv(&data);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        }

        ret = GmcTransCreateSavepointAsync(g_connAsync, "sp_1", TransSavePointCb, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        vmRSSSize = GetValueBetweenStr(command, "VmRSS:", "kB", "");
    }
    return 0;
}

int GetGmserverId()
{
    string str;
    int gmserverId;
    char cmdOutPut[1024] = {0};
    const char *command = "pgrep gmserver";
    FILE *pf = popen(command, "r");
    while (fgets(cmdOutPut, sizeof(cmdOutPut), pf) != NULL) {
        str.assign(cmdOutPut);
        gmserverId = stoi(str);
    }
    pclose(pf);
    return gmserverId;
}

int CurUsedToSizeGraph(int aimMbSize, GmcBatchT *batch)
{
    int mbSize = 0;
    int ret = 0;
    AsyncUserDataT data = {0};
    int times = 0;
    while (mbSize < aimMbSize) {
        ret = GmcTransCreateSavepointAsync(g_connAsync, "sp_1", TransSavePointCb, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(stmt_root, "label_container", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, stmt_root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *conRootNode = NULL;
        ret = GmcGetRootNode(stmt_root, &conRootNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置root字段值
        uint32_t conPIDValue = 101;
        uint32_t conF1Value = 102;
        uint32_t conF2Value = 103;
        ret = yang_set_property_graph(stmt_root, &conF1Value, "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = yang_set_property_graph(stmt_root, &conF2Value, "F2", GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加choice批操作
        GmcNodeT *ChildNode = NULL;
        ret = GmcYangEditChildNode(conRootNode, "label_choice", GMC_OPERATION_MERGE, &ChildNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        GmcNodeT *CaseChildNode = NULL;
        ret = GmcYangEditChildNode(ChildNode, "label_case", GMC_OPERATION_MERGE, &CaseChildNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置case字段值
        uint32_t casePIDValue = 104;
        uint32_t caseF1Value = 105;
        uint32_t caseF2Value = 106;
        ret = yang_set_property_graph1(CaseChildNode, &caseF1Value, "case_F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = yang_set_property_graph1(CaseChildNode, &caseF2Value, "case_F2", GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加case批操作
        ret = GmcBatchAddDML(batch, stmt_root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // list
        ret = testGmcPrepareStmtByLabelName(stmt_list, "label_list", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt_root, stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置list字段值
        uint32_t listF3Value = 107;
        uint32_t listF4Value = 108;
        string mark = "";
        string listStrValueStr = WriteStringToSpecialSize(g_strSize, mark);
        char *listStrValue = (char *)listStrValueStr.c_str();
        ret = list_set_key(stmt_list, listF3Value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = yang_set_property_graph(stmt_list, &listF4Value, "F4", GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = yang_set_property_str_graph(stmt_list, listStrValue, "F5_str", GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = yang_set_property_str_graph(stmt_list, listStrValue, "F6_str", GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = yang_set_property_str_graph(stmt_list, listStrValue, "F7_str", GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = yang_set_property_str_graph(stmt_list, listStrValue, "F8_str", GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = yang_set_property_str_graph(stmt_list, listStrValue, "F9_str", GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = yang_set_property_str_graph(stmt_list, listStrValue, "F10_str", GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加表list批操作
        ret = GmcBatchAddDML(batch, stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);

        mbSize = GetValueBetweenStr(g_viewCommand, "CUR_USED_SIZE: [", "] MB [");
        times++;
    }
    return times;
}

int WaitRetrieveToOs(const char *command, int beforeCommitRss)
{
    int time = 0;
    int newRSS = beforeCommitRss;
    while (newRSS >= beforeCommitRss) {
        sleep(1);
        newRSS = GetValueBetweenStr(command, "VmRSS:", "kB");
        time++;
        system(command);
        cout << "You have waited " << time << " seconds and rss has not decreased." << endl;
    }
    return time;
}

int MultiCheck(const char *command, int beforeCommitRss, int beforeCommitByte, int timeOut = 300)
{
    int time = 0;
    int newRSS = beforeCommitRss;
    int newMb = 0;
    int newKb = 0;
    int newB = 0;
    int newByte = beforeCommitByte;
    while (newRSS >= beforeCommitRss || newByte >= beforeCommitByte) {
        sleep(1);
        newRSS = GetValueBetweenStr(command, "VmRSS:", "kB", "");
        newMb = GetValueBetweenStr(g_viewCommand, "CUR_USED_SIZE: [", "] MB [");
        newKb = GetValueBetweenStr(g_viewCommand, "] MB [", "] KB [");
        newB = GetValueBetweenStr(g_viewCommand, "] KB [", "] Byte");
        newByte = 1024 * 1024 * newMb + 1024 * newKb + newB;
        time++;
        cout << endl << "You have waited " << time << " seconds and rss or undo bytes has not decreased." << endl;
        if (time > timeOut) {
            cout << endl << "Time Out!" << endl;
            return -1;
        }
    }
    return time;
}

#endif
