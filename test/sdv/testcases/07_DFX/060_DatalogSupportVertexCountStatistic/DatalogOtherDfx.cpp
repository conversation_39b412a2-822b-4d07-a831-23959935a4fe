/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: DatalogOtherDfx_test.cpp
 * Description: Datalog其它视图查询
 * Author: yang<PERSON>wen ywx1060383
 * Create: 2022-10-28
 */
#include "tools_join_int.h"
#include "t_datacom_lite.h"

using namespace std;

#define MAX_CMD_SIZE 1024
#define FILE_PATH 512

char g_command[MAX_CMD_SIZE];

char g_hFile[FILE_PATH] = "../../../../../pub/include/";

class JoinVertexCount_test : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);

        // 创建epoll监听线程
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret;
        ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void JoinVertexCount_test::SetUp()
{
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1009017");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so

    int ret;
    // 创建连接
    g_conn = NULL;
    g_stmt = NULL;
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}
void JoinVertexCount_test::TearDown()
{
    AW_CHECK_LOG_END();
    int ret;
    // 断开同步连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 034.加载含resource，多个ns，多个table的.d文件，查询CATA_GENERAL_INFO视图
TEST_F(JoinVertexCount_test, DFX_060_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 查询CATA_GENERAL_INFO视图
    char const *view_name = "V\\$CATA_GENERAL_INFO";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, view_name, g_connServer);
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    system(g_command);
    int existVertexCount = get_value(g_command, "VERTEX_LABEL_NUM: ");
    int existLabelSub = get_value(g_command, "LABEL_SUBS_NUM: ");
    int existKvCount = get_value(g_command, "KV_TABLE_NUM: ");
    int existNsCount = get_value(g_command, "NAMESPACE_NUM: ");
    int existUserCount = get_value(g_command, "USER_NUM: ");
    int existRoleCount = get_value(g_command, "ROLE_NUM: ");
    int existEdgeLabelCount = get_value(g_command, "EDGE_LABEL_NUM: ");
    int existResourceCount = get_value(g_command, "RESOURCE_POOL_NUM: ");
    int VertexCount = existVertexCount + 15;     // 存在的Vertex + 创建的15个Vertex
    int ResourceCount = existResourceCount + 2;  // 存在的Resource + 创建的2个
    int KvCount = existKvCount;
    int NsCount = existNsCount;

    // .d文件加载：创建表和连接规则
    TestLoadDatalog("./other_dfx_file/all_type_table.so");
    int ret = 0;

    // 查询CATA_GENERAL_INFO视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, view_name, g_connServer);
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    system(g_command);

    char string[100] = {0};
    sprintf(string, "VERTEX_LABEL_NUM: %d", VertexCount);
    char stringLabelSub[100] = {0};
    sprintf(stringLabelSub, "LABEL_SUBS_NUM: %d", existLabelSub);
    char stringKvNum[100] = {0};
    sprintf(stringKvNum, "KV_TABLE_NUM: %d", KvCount);
    char stringNsNum[100] = {0};
    sprintf(stringNsNum, "NAMESPACE_NUM: %d", NsCount);
    char stringUserNum[100] = {0};
    sprintf(stringUserNum, "USER_NUM: %d", existUserCount);
    char stringRoleNum[100] = {0};
    sprintf(stringRoleNum, "ROLE_NUM: %d", existRoleCount);
    char stringResourceNum[100] = {0};
    sprintf(stringResourceNum, "RESOURCE_POOL_NUM: %d", ResourceCount);
    char stringEdgeLabelNum[100] = {0};
    sprintf(stringEdgeLabelNum, "EDGE_LABEL_NUM: %d", existEdgeLabelCount);
    ret = executeCommand(g_command, string, stringEdgeLabelNum, stringLabelSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, stringKvNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand(g_command, stringResourceNum, stringNsNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand(g_command, stringUserNum, stringRoleNum);
    EXPECT_EQ(GMERR_OK, ret);

    // 卸载
    TestUninstallDatalog("all_type_table", NULL, false);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 036.加载含resource，多个ns，多个table的.d文件，查询CATA_VERTEX_LABEL_INFO视图
TEST_F(JoinVertexCount_test, DFX_060_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // .d文件加载：创建表和连接规则
    TestLoadDatalog("./other_dfx_file/all_type_table.so");
    int ret = 0;

    char const *view_name = "V\\$CATA_VERTEX_LABEL_INFO";
    // 查询CATA_GENERAL_INFO视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, view_name, g_connServer);
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    system(g_command);

    // 15张表+系统2张
    ret = executeCommand(g_command, "index = 16");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "VERTEX_LABEL_NAME: ns_01.A1", "MAX_RECORD_COUNT: 2");
    EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand(g_command, "rsc10");
    EXPECT_EQ(GMERR_OK, ret);

    // 卸载
    TestUninstallDatalog("all_type_table", NULL, false);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 038.加载含resource的.d文件，查询STORAGE_EXTERN_RESOURCE_POOL_STAT视图
TEST_F(JoinVertexCount_test, DFX_060_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // .d文件加载：创建表和连接规则
    TestLoadDatalog("./other_dfx_file/all_type_table.so");
    int ret = 0;

    char const *view_name = "V\\$STORAGE_EXTERN_RESOURCE_POOL_STAT";
    // 查询CATA_GENERAL_INFO视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f rsc10 -s %s", g_toolPath, view_name, g_connServer);
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    system(g_command);

    ret = executeCommand(g_command, "sysview get records unsucc, ret = 1004004");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    TestUninstallDatalog("all_type_table", NULL, false);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 039.加载含resource的.d文件，查询STORAGE_RESOURCE_ALL_POOL_STAT视图
TEST_F(JoinVertexCount_test, DFX_060_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // .d文件加载：创建表和连接规则
    TestLoadDatalog("./other_dfx_file/all_type_table.so");
    int ret = 0;

    // 获取资源池的名字
    char *resPoolName1 = NULL;
    char *resPoolName2 = NULL;
    ret =
        GtExecSysviewCmd(&resPoolName1, "V$STORAGE_RESOURCE_ALL_POOL_STAT", "| grep POOL_NAME | awk NR==1'{print $2}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret =
        GtExecSysviewCmd(&resPoolName2, "V$STORAGE_RESOURCE_ALL_POOL_STAT", "| grep POOL_NAME | awk NR==2'{print $2}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char const *view_name = "V\\$STORAGE_RESOURCE_ALL_POOL_STAT";

    // 查询CATA_GENERAL_INFO视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, view_name, g_connServer);
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    system(g_command);

    ret = executeCommand(g_command, resPoolName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand(g_command, resPoolName2);
    EXPECT_EQ(GMERR_OK, ret);
    free(resPoolName1);
    free(resPoolName2);

    // 卸载
    TestUninstallDatalog("all_type_table", NULL, false);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 040.加载含resource的.d文件，查询STORAGE_RESOURCE_BIND_TO_LABEL_STAT视图
TEST_F(JoinVertexCount_test, DFX_060_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // .d文件加载：创建表和连接规则
    TestLoadDatalog("./other_dfx_file/all_type_table.so");
    int ret = 0;

    char const *view_name = "V\\$STORAGE_RESOURCE_BIND_TO_LABEL_STAT";
    // 查询CATA_GENERAL_INFO视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f rsc10 -s %s", g_toolPath, view_name, g_connServer);
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    system(g_command);

    ret = executeCommand(g_command, "sysview get records unsucc, ret = 1004004");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    TestUninstallDatalog("all_type_table", NULL, false);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 041.加载含resource的.d文件，查询STORAGE_RESOURCE_RESID_STAT视图
TEST_F(JoinVertexCount_test, DFX_060_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // .d文件加载：创建表和连接规则
    TestLoadDatalog("./other_dfx_file/all_type_table.so");
    int ret = 0;

    char *resPoolName1 = NULL;
    char *resPoolName2 = NULL;
    ret =
        GtExecSysviewCmd(&resPoolName1, "V$STORAGE_RESOURCE_ALL_POOL_STAT", "| grep POOL_NAME | awk NR==1'{print $2}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret =
        GtExecSysviewCmd(&resPoolName2, "V$STORAGE_RESOURCE_ALL_POOL_STAT", "| grep POOL_NAME | awk NR==2'{print $2}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询STORAGE_RESOURCE_ALL_POOL_STAT视图
    char const *view_name1 = "V\\$STORAGE_RESOURCE_ALL_POOL_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, view_name1, g_connServer);
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    system(g_command);

    // 获取rsc9的POOLID
    char const *view_name3 = "V\\$STORAGE_RESOURCE_SINGLE_POOL_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f POOL_NAME=%s -s %s", g_toolPath, view_name1, resPoolName1,
        g_connServer);
    uint64_t poolId9 = get_value(g_command, "POOL_ID: ");
    uint64_t resourceId9 = poolId9 << 48;

    // 获取rsc10的POOLID
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f POOL_NAME=%s -s %s", g_toolPath, view_name1, resPoolName2,
        g_connServer);
    uint64_t poolId10 = get_value(g_command, "POOL_ID: ");
    uint64_t resourceId10 = poolId10 << 48;

    // 查询rsc9的STORAGE_RESOURCE_RESID_STAT视图
    char const *view_name2 = "V\\$STORAGE_RESOURCE_RESID_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f [r]%llu -s %s", g_toolPath, view_name2, resourceId9,
        g_connServer);
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    system(g_command);
    ret = executeCommand(g_command, resPoolName1);
    EXPECT_EQ(GMERR_OK, ret);

    // 查询rsc10的STORAGE_RESOURCE_RESID_STAT视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f [r]%llu -s %s", g_toolPath, view_name2, resourceId10,
        g_connServer);
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    system(g_command);
    ret = executeCommand(g_command, resPoolName2);
    EXPECT_EQ(GMERR_OK, ret);

    free(resPoolName1);
    free(resPoolName2);
    // 卸载
    TestUninstallDatalog("all_type_table", NULL, false);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 042.加载含resource的.d文件，查询STORAGE_RESOURCE_SINGLE_POOL_BITMAP_STAT视图
TEST_F(JoinVertexCount_test, DFX_060_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // .d文件加载：创建表和连接规则
    TestLoadDatalog("./other_dfx_file/all_type_table.so");
    int ret = 0;

    char *resPoolName1 = NULL;
    ret =
        GtExecSysviewCmd(&resPoolName1, "V$STORAGE_RESOURCE_ALL_POOL_STAT", "| grep POOL_NAME | awk NR==1'{print $2}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char const *view_name = "V\\$STORAGE_RESOURCE_SINGLE_POOL_BITMAP_STAT";
    // 查询STORAGE_RESOURCE_SINGLE_POOL_BITMAP_STAT视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f POOL_NAME=%s -s %s", g_toolPath, view_name, resPoolName1,
        g_connServer);
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    system(g_command);

    ret = executeCommand(g_command, resPoolName1, "POOL_ID", "START_ID", "ORDER", "FREE");
    EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand(g_command, "REFERENCE", "RANGE", "ALLOC", "EXTERN_POOL_ID", "SHARE_MEM_SIZE");
    EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand(g_command, "DYNAMIC_MEM_SIZE", "BITMAP");
    EXPECT_EQ(GMERR_OK, ret);

    free(resPoolName1);
    // 卸载
    TestUninstallDatalog("all_type_table", NULL, false);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 043.加载含resource的.d文件，查询STORAGE_RESOURCE_SINGLE_POOL_STAT视图
TEST_F(JoinVertexCount_test, DFX_060_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // .d文件加载：创建表和连接规则
    TestLoadDatalog("./other_dfx_file/all_type_table.so");
    int ret = 0;

    char *resPoolName1 = NULL;
    char *resPoolName2 = NULL;
    ret =
        GtExecSysviewCmd(&resPoolName1, "V$STORAGE_RESOURCE_ALL_POOL_STAT", "| grep POOL_NAME | awk NR==1'{print $2}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret =
        GtExecSysviewCmd(&resPoolName2, "V$STORAGE_RESOURCE_ALL_POOL_STAT", "| grep POOL_NAME | awk NR==2'{print $2}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char const *view_name = "V\\$STORAGE_RESOURCE_SINGLE_POOL_STAT";

    // 查所有
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, view_name, g_connServer);
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    system(g_command);
    ret = executeCommand(g_command, resPoolName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand(g_command, resPoolName2);
    EXPECT_EQ(GMERR_OK, ret);

    // 只查询rsc9
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f POOL_NAME=%s -s %s", g_toolPath, view_name, resPoolName1,
        g_connServer);
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    system(g_command);
    ret = executeCommand(g_command, resPoolName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand(g_command, resPoolName2);
    EXPECT_EQ(-1, ret);

    // 只查询rsc10
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f POOL_NAME=%s -s %s", g_toolPath, view_name, resPoolName2,
        g_connServer);
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    system(g_command);
    ret = executeCommand(g_command, resPoolName1);
    EXPECT_EQ(-1, ret);
    ret = executeCommand(g_command, resPoolName2);
    EXPECT_EQ(GMERR_OK, ret);

    free(resPoolName1);
    free(resPoolName2);
    // 卸载
    TestUninstallDatalog("all_type_table", NULL, false);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 044.加载含resource的.d文件，查询STORAGE_RESOURCE_START_INDEX_USED_STAT视图
TEST_F(JoinVertexCount_test, DFX_060_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // .d文件加载：创建表和连接规则
    TestLoadDatalog("./other_dfx_file/all_type_table.so");
    int ret = 0;

    char *resPoolName1 = NULL;
    ret =
        GtExecSysviewCmd(&resPoolName1, "V$STORAGE_RESOURCE_ALL_POOL_STAT", "| grep POOL_NAME | awk NR==1'{print $2}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char const *view_name = "V\\$STORAGE_RESOURCE_START_INDEX_USED_STAT";
    // 查询STORAGE_RESOURCE_START_INDEX_USED_STAT视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, view_name, g_connServer);
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    system(g_command);
    ret = executeCommand(g_command, "ErrorCodeDescription: Not normal parameter value.");
    EXPECT_EQ(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -f [p]%s[s]0[n]%s -s %s", g_toolPath, view_name, resPoolName1,
        g_testNameSpace, g_connServer);
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    system(g_command);

    ret = executeCommand(g_command, resPoolName1, "POOL_ID", "START_INDEX", "COUNT", "STATUS");
    EXPECT_EQ(GMERR_OK, ret);
    free(resPoolName1);

    // 卸载
    TestUninstallDatalog("all_type_table", NULL, false);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 045.含function表，查询CATA_GENERAL_INFO，CATA_VERTEX_LABEL_INFO视图
TEST_F(JoinVertexCount_test, DFX_060_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 加载
    TestLoadDatalog("./project_file/datalog_udf_filter_and_write.so");
    // 运行
    int ret = 0;

    char const *view_name1 = "V\\$CATA_GENERAL_INFO";
    // 查询CATA_GENERAL_INFO视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, view_name1, g_connServer);
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    system(g_command);

    char const *view_name2 = "V\\$CATA_VERTEX_LABEL_INFO";
    // 查询CATA_GENERAL_INFO视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, view_name2, g_connServer);
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    system(g_command);
    ret = executeCommand(g_command, "index = 5");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 找不到func
    ret = executeCommand(g_command, "VERTEX_LABEL_NAME: func");
    EXPECT_EQ(-1, ret);

    // 卸载
    TestUninstallDatalog("datalog_udf_filter_and_write", NULL, false);
    AW_FUN_Log(LOG_STEP, "test end.");
}
