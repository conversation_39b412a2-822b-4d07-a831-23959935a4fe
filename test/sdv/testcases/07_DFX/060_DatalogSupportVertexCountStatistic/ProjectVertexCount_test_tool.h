/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: ProjectVertexCount_test_tool.h
 * Description: 投影
 * Author: ya<PERSON><PERSON><PERSON> ywx1060383
 * Create: 2022-10-28
 */
#ifndef PROJECTVERTEXCOUNT_TEST_TOOL_H
#define PROJECTVERTEXCOUNT_TEST_TOOL_H
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"

const char *g_schemaJson1 = R"([ {
        "type" : "record",
        "name" : "%s",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "a", "type" : "int32"},
            {"name" : "b", "type" : "int32"}
        ],
        "keys" : [
            {
                "node" : "%s",
                "name" : "0",
                "fields" : ["upgradeVersion", "a", "b"],
                "index" : {"type" : "primary"},
                "constraints" : {"unique" : true}
            }
        ]
    } ])";

const char *g_schemaJson2 = R"([ {
        "type" : "record",
        "name" : "%s",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "a", "type" : "int32"},
            {"name" : "b", "type" : "int32"},
            {"name" : "c", "type" : "int32"}
        ],
        "keys" : [
            {
                "node" : "%s",
                "name" : "0",
                "fields" : ["upgradeVersion", "a", "b", "c"],
                "index" : {"type" : "primary"},
                "constraints" : {"unique" : true}
            }
        ]
    } ])";

// input 1
// B(a, b) :- A(a, b).
#pragma pack(1)
typedef struct {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int32_t a;
    int32_t b;
} TEST_INPUT_01;
#pragma pack()

void TestInput01Value(TEST_INPUT_01 *d, int value[])
{
    d->a = value[0];
    d->b = value[1];
    d->dtlReservedCount = value[2];
    d->upgradeVersion = value[3];
}

// input 2
// B(a, b) :- A(a, b).
#pragma pack(1)
typedef struct {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int32_t a;
    int32_t b;
    int32_t c;
} TEST_INPUT_02;
#pragma pack()

void TestInput02Value(TEST_INPUT_02 *d, int value[])
{
    d->a = value[0];
    d->b = value[1];
    d->c = value[2];
    d->dtlReservedCount = value[3];
    d->upgradeVersion = value[4];
}

#endif
