//15张表，2个资源表
// normal
namespace ns_01{
%table A1(a:int4, b:int4){
max_size(2)
}
%readonly A1
}
%table B1(a:int4, b:int4)
B1(a, b) :- ns_01.A1(a, b).

// transient
namespace ns_02{
%table A2(a:int4, b:int4){
transient(tuple)
}
%readonly A2
}
%table B2(a:int4, b:int4)
{
transient(field(a))
}
%table C2(a:int4, b:int4)
B2(a, b) :- ns_02.A2(a, b).
C2(a, b) :- B2(a, b).

// timeout
namespace ns_03{
%table A3(a:int8, b:int8)
{
    timeout(field(a))
}
%readonly A3
}
%table B3(a:int8, b:int8)
B3(a, b) :- ns_03.A3(a, b).

// update
%table A4(a:int4, b:int4) {
index(0(a)),
update
}
%table B4(a:int4, b:int4)
B4(a, b) :- A4(a, b).

// resource1
%table A9(a: int4)
%table B9(a: int4, b: int4)
%resource rsc9(a: int4 -> b: int4) { index(0(a)), sequential(max_size(2)) }
rsc9(a, -) :- A9(a).
B9(a, b) :- rsc9(a, b).

// resource2
%table A10(a: int4)
%table B10(a: int4, b: int4)
%resource rsc10(a: int4 -> b: int4) { index(0(a)), sequential(max_size(2)) }
rsc10(a, -) :- A10(a).
B10(a, b) :- rsc10(a, b).
