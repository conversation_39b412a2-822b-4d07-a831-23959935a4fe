#include "tools.h"

// 需要可以让主程序断言失败，以此代替AW_MACRO_EXPECT_EQ_INT
bool isServer = false;
#define CHECK_EXPECT(expect) CheckExpect(expect, "test_026_client.cpp", __LINE__, isServer)

int main(int argc, char *argv[])
{
    int ret = 0;
    char info[MAX_CMD_SIZE];
    GmcConnT *conn;
    GmcStmtT *stmt;

    // 初始化
    ret = testEnvInit(-1, false);
    CHECK_EXPECT(ret == GMERR_OK);

    ret = testGmcConnect(&conn, &stmt);
    while (ret != GMERR_OK) {
        ret = testGmcConnect(&conn, &stmt);
        CHECK_EXPECT(ret == GMERR_OK || ret == GMERR_CONNECTION_FAILURE);
        (void)snprintf(info, MAX_CMD_SIZE, "action:testGmcConnect, ret:%d", ret);
        WriteInFile(g_messageFilePath, info);
        usleep(100);
    }

    ret = testGmcDisconnect(conn, stmt);
    CHECK_EXPECT(ret == GMERR_OK);
    (void)snprintf(info, MAX_CMD_SIZE, "action:testGmcDisconnect, ret:%d", ret);
    WriteInFile(g_messageFilePath, info);
}
