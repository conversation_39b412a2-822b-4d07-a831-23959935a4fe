/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: 057_PessimisiticRR
 * Author: hanyang
 * Create: 2023-9-13
 */
#include "PessRR.h"

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;

class PessimisiticRR : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void PessimisiticRR::SetUpTestCase()
{
}

void PessimisiticRR::TearDownTestCase()
{
}

void PessimisiticRR::SetUp()
{
    AW_CHECK_LOG_BEGIN();
}

void PessimisiticRR::TearDown()
{
    AW_CHECK_LOG_END();
}

/*****************************************************************************
 Description  : 001.连接满场景下，每个连接都开启事务，进行DML操作，事务提交和回滚随机
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Rel_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh \"isFastReadUncommitted=0\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"defaultTransactionType=0\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"defaultIsolationLevel=2\"");
    system("sh $TEST_HOME/tools/start.sh -f");

    sleep(1);
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn[MAX_CONN_SIZE] = {NULL};
    GmcStmtT *stmt[MAX_CONN_SIZE] = {NULL};
    uint32_t existConnNum = 0;
    ret = testGetConnNum(&existConnNum);
    AW_MACRO_ASSERT_EQ_INT(0, ret);

    for (int i = 0; i < (MAX_CONN_SIZE - 4 - existConnNum); i++) {
        AW_FUN_Log(LOG_DEBUG, "testGmcConnect:%d\n", i);
        ret = testGmcConnect(&conn[i], &stmt[i]);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_DEBUG, "i:%d ret:%d\n", i, ret);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }

    // 创建Vertex表
    TestCreateLabelTrx(stmt[0]);

    // 悲观+可重复读事务配置
    g_msTrxCfgRR.transMode = GMC_TRANS_USED_IN_CS;
    g_msTrxCfgRR.type = GMC_TX_ISOLATION_REPEATABLE;
    g_msTrxCfgRR.readOnly = false;
    g_msTrxCfgRR.trxType = GMC_PESSIMISITIC_TRX;

    for (uint32_t i = 0; i < (MAX_CONN_SIZE - 4 - existConnNum); i++) {
        // 启动事务
        ret = GmcTransStart(conn[i], &g_msTrxCfgRR);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 事务1 insert
        initValue = i;
        TestInsertVertexLabel(stmt[i], times, initValue, "Vertex_01");

        if ((i % 2) == 0) {
            // 提交事务
            ret = GmcTransCommit(conn[i]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            // 回滚事务
            ret = GmcTransRollBack(conn[i]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }

    // 删除Vertex
    TestDropLabelAll(stmt[0]);

    for (int i = 0; i < (MAX_CONN_SIZE - 4 - existConnNum); i++) {
        ret = testGmcDisconnect(conn[i], stmt[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
}

/*****************************************************************************
 Description  : 002.内存满场景下，开启事务，写数据到内存满，事务提交
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Rel_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh \"isFastReadUncommitted=0\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"defaultTransactionType=0\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"defaultIsolationLevel=2\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSeMem=300\"");
    system("sh $TEST_HOME/tools/start.sh -f");

    sleep(1);
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 建立连接
    GmcConnT *g_conn = NULL;
    GmcStmtT *g_stmt = NULL;
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 创建Vertex表
    TestCreateLabelTrx(g_stmt);

    // 悲观+可重复读事务配置
    g_msTrxCfgRR.transMode = GMC_TRANS_USED_IN_CS;
    g_msTrxCfgRR.type = GMC_TX_ISOLATION_REPEATABLE;
    g_msTrxCfgRR.readOnly = false;
    g_msTrxCfgRR.trxType = GMC_PESSIMISITIC_TRX;

    // 启动事务
    ret = GmcTransStart(g_conn, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务 insert
    ret = testGmcPrepareStmtByLabelName(g_stmt, "Vertex_01", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert vertex
    uint32_t i;
    uint32_t value;
    times = 250;
    for (i = 0; i < times; i++) {
        // 写数据
        value = initValue + i;

        // set pk
        TestGmcSetVertexProperty_PK(g_stmt, value);

        TestGmcSetVertexProperty(g_stmt, value);
        TestGmcSetVertexPropertyMem(g_stmt, value);

        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestGmcGetStmtAttr(g_stmt, 1, i);
    }

    // 提交事务
    ret = GmcTransCommit(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    TestScanLabelCount(g_stmt, "Vertex_01", times);

    // 删除Vertex
    TestDropLabelAll(g_stmt);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
}

/*****************************************************************************
 Description  : 003.内存满场景下，开启事务，写数据到内存满，事务回滚
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Rel_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh \"isFastReadUncommitted=0\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"defaultTransactionType=0\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"defaultIsolationLevel=2\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSeMem=300\"");
    system("sh $TEST_HOME/tools/start.sh -f");

    sleep(1);
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 建立连接
    GmcConnT *g_conn = NULL;
    GmcStmtT *g_stmt = NULL;
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 创建Vertex表
    TestCreateLabelTrx(g_stmt);

    // 悲观+可重复读事务配置
    g_msTrxCfgRR.transMode = GMC_TRANS_USED_IN_CS;
    g_msTrxCfgRR.type = GMC_TX_ISOLATION_REPEATABLE;
    g_msTrxCfgRR.readOnly = false;
    g_msTrxCfgRR.trxType = GMC_PESSIMISITIC_TRX;

    // 启动事务
    ret = GmcTransStart(g_conn, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务 insert
    ret = testGmcPrepareStmtByLabelName(g_stmt, "Vertex_01", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert vertex
    uint32_t i;
    uint32_t value;
    times = 250;
    for (i = 0; i < times; i++) {
        // 写数据
        value = initValue + i;

        // set pk
        TestGmcSetVertexProperty_PK(g_stmt, value);

        TestGmcSetVertexProperty(g_stmt, value);
        TestGmcSetVertexPropertyMem(g_stmt, value);

        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestGmcGetStmtAttr(g_stmt, 1, i);
    }

    // 回滚事务
    ret = GmcTransRollBack(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    TestScanLabelCount(g_stmt, "Vertex_01", 0);

    // 删除Vertex
    TestDropLabelAll(g_stmt);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
}

/*****************************************************************************
 Description  : 004.先写入到内存满，开启事务，逐条删除之后提交，能全部删除成功
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Rel_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 1;
    uint32_t expectNum = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh \"isFastReadUncommitted=0\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"defaultTransactionType=0\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"defaultIsolationLevel=2\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSeMem=300\"");
    system("sh $TEST_HOME/tools/start.sh -f");

    sleep(1);
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 建立连接
    GmcConnT *g_conn = NULL;
    GmcStmtT *g_stmt = NULL;
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 创建Vertex表
    TestCreateLabelTrx(g_stmt);

    // 悲观+可重复读事务配置
    g_msTrxCfgRR.transMode = GMC_TRANS_USED_IN_CS;
    g_msTrxCfgRR.type = GMC_TX_ISOLATION_REPEATABLE;
    g_msTrxCfgRR.readOnly = false;
    g_msTrxCfgRR.trxType = GMC_PESSIMISITIC_TRX;

    // 启动事务
    ret = GmcTransStart(g_conn, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务 insert
    ret = testGmcPrepareStmtByLabelName(g_stmt, "Vertex_01", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert vertex
    uint32_t i;
    uint32_t value;
    times = 250;
    for (i = 0; i < times; i++) {
        // 写数据
        value = initValue + i;

        // set pk
        TestGmcSetVertexProperty_PK(g_stmt, value);

        TestGmcSetVertexProperty(g_stmt, value);
        TestGmcSetVertexPropertyMem(g_stmt, value);

        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestGmcGetStmtAttr(g_stmt, 1, i);
    }

    // 提交事务
    ret = GmcTransCommit(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // scan
    TestScanLabelCount(g_stmt, "Vertex_01", times);

    for (i = 0; i < times; i++) {
        // 启动事务
        ret = GmcTransStart(g_conn, &g_msTrxCfgRR);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        initValue = i;
        TestDeleteVertexLabel(g_stmt, "Vertex_pk", 1, initValue, "Vertex_01");

        // 提交事务
        ret = GmcTransCommit(g_conn);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // scan
    TestScanLabelCount(g_stmt, "Vertex_01", 0);

    // 删除Vertex
    TestDropLabelAll(g_stmt);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
}

/*****************************************************************************
 Description  : 005.开启事务，进行DML操作，事务提交和回滚随机，循环执行10000次
 Author       : hanyang
*****************************************************************************/
TEST_F(PessimisiticRR, Other_057_Rel_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 10;
    uint32_t expectNum = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh \"isFastReadUncommitted=0\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"defaultTransactionType=0\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"defaultIsolationLevel=2\"");
    system("sh $TEST_HOME/tools/start.sh -f");

    sleep(1);
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 建立连接
    GmcConnT *g_conn = NULL;
    GmcStmtT *g_stmt = NULL;
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 创建Vertex表
    char *vLabelSchema = NULL;

    readJanssonFile("schema_file/Vertex_05.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabel(g_stmt, vLabelSchema, g_msConfigTrx);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(vLabelSchema);

    // 悲观+可重复读事务配置
    g_msTrxCfgRR.transMode = GMC_TRANS_USED_IN_CS;
    g_msTrxCfgRR.type = GMC_TX_ISOLATION_REPEATABLE;
    g_msTrxCfgRR.readOnly = false;
    g_msTrxCfgRR.trxType = GMC_PESSIMISITIC_TRX;

    for (uint32_t i = 0; i < RECORD_NUM_003; i++) {
        if ((i % 100) == 0) {
            AW_FUN_Log(LOG_DEBUG, "The number of cycles is %d.\n", i);
        }

        // 启动事务
        ret = GmcTransStart(g_conn, &g_msTrxCfgRR);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 事务 insert
        initValue = i * 10;
        ret = testGmcPrepareStmtByLabelName(g_stmt, "Vertex_05", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // insert vertex
        uint32_t j;
        uint32_t value;
        for (j = 0; j < times; j++) {
            // 写数据
            value = initValue + j;

            // set pk
            TestGmcSetVertexProperty_PK(g_stmt, value);

            ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetVertexProperty(g_stmt, "F4", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            ret = GmcExecute(g_stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            TestGmcGetStmtAttr(g_stmt, 1, j);
        }

        if ((i % 2) == 0) {
            // 提交事务
            ret = GmcTransCommit(g_conn);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            // 回滚事务
            ret = GmcTransRollBack(g_conn);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }

    // 删除Vertex
    TestDropLabel(g_stmt, "Vertex_05");

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
}

/*****************************************************************************
 Description  : 006.多线程并发，开启事务，进行DML操作，有的线程成功，有的线程失败回滚，
                循环执行10000次，记录成功和失败的次数，并校验
 Author       : hanyang
*****************************************************************************/
uint32_t succNum = 0;
uint32_t failNum = 0;
void *Thread_006(void *args)
{
    int ret;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    uint32_t initValue = 0;
    uint32_t times = 1;

    uint32_t connId = *((uint32_t *)args);

    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = GmcTransStart(conn, &g_msTrxCfgRR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务 insert
    initValue = succNum;
    ret = testGmcPrepareStmtByLabelName(stmt, "Vertex_05", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert vertex
    // set pk
    TestGmcSetVertexProperty_PK(stmt, initValue);

    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &initValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &initValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT32, &initValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_UINT32, &initValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    if (ret == GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestGmcGetStmtAttr(stmt, 1, 0);

        // 提交事务
        ret = GmcTransCommit(conn);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        succNum++;
    } else {
        // 回滚事务
        ret = GmcTransRollBack(conn);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        failNum++;
    }

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

// main
TEST_F(PessimisiticRR, Other_057_Rel_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 10;
    uint32_t expectNum = 0;

    system("sh $TEST_HOME/tools/modifyCfg.sh \"isFastReadUncommitted=0\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"defaultTransactionType=0\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"defaultIsolationLevel=2\"");
    system("sh $TEST_HOME/tools/start.sh -f");

    sleep(1);
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 建立连接
    GmcConnT *g_conn = NULL;
    GmcStmtT *g_stmt = NULL;
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 创建Vertex表
    char *vLabelSchema = NULL;

    readJanssonFile("schema_file/Vertex_05.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabel(g_stmt, vLabelSchema, g_msConfigTrx);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(vLabelSchema);

    // 悲观+可重复读事务配置
    g_msTrxCfgRR.transMode = GMC_TRANS_USED_IN_CS;
    g_msTrxCfgRR.type = GMC_TX_ISOLATION_REPEATABLE;
    g_msTrxCfgRR.readOnly = false;
    g_msTrxCfgRR.trxType = GMC_PESSIMISITIC_TRX;

    for (uint32_t i = 0; i < RECORD_NUM_003; i++) {
        if ((i % 100) == 0) {
            AW_FUN_Log(LOG_DEBUG, "The number of cycles is %d.\n", i);
        }

        // 并发操作
        pthread_t tid[10];
        uint32_t index[10] = {0};

        for (uint32_t j = 0; j < 2; j++) {
            index[j] = j;
            pthread_create(&tid[j], NULL, Thread_006, (void *)&index[j]);
        }

        for (uint32_t j = 0; j < 2; j++) {
            pthread_join(tid[j], NULL);
        }
    }

    AW_FUN_Log(LOG_INFO, "============succ is %d, fail is %d.================\n", succNum, failNum);
    AW_MACRO_EXPECT_EQ_INT((succNum+failNum), (RECORD_NUM_003 * 2));
    AddWhiteList(GMERR_PRIMARY_KEY_VIOLATION);
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);

    // 删除Vertex
    TestDropLabel(g_stmt, "Vertex_05");

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
}
