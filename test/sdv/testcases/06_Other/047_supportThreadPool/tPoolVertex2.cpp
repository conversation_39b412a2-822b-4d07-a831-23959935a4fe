
extern "C" {
}
#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "tPoolTest.h"
#include "tPoolThread.h"
 
#define MAX_VERTEX_NUM 10000
GmcConnT *g_conn = NULL, *g_conn_async = NULL;
GmcStmtT *g_stmt = NULL, *g_stmt_async = NULL;


class tPoolVertex2 : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void tPoolVertex2::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"isFastReadUncommitted=0\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"scheduleMode=2\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxWorkerNum=1024\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"permanentWorkerNum=1024\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"trxLockTimeOut=2000\"");

    // 设置为：3,4,5 并发场景下争不到锁，会导致服务hung掉，属于正常现象；
    system("sh $TEST_HOME/tools/modifyCfg.sh \"workerHungThreshold=20,299,300\"");

    // 长事务监控关闭，开启后，在并发事务多的情况下，可能导致拿到锁的事务超时回滚，导致所有事务都失败
    system("sh $TEST_HOME/tools/modifyCfg.sh \"trxMonitorEnable=0\"");

    system("sh $TEST_HOME/tools/start.sh");

    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_threadNum = 200;
}

void tPoolVertex2::TearDownTestCase()
{
    int ret;
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    testEnvClean();
    system("sh $TEST_HOME/tools/stop.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}

void tPoolVertex2::SetUp()
{
    int ret = 0;

    // 创建同步连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)GmcDropVertexLabel(g_stmt, g_labelName);

    // 创建异步连接
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);

    system("rm -rf \"../../../log/run/rgmserver/rgmserver.log\"");

    AW_CHECK_LOG_BEGIN();
}

void tPoolVertex2::TearDown()
{
    // 并发replace merage可能会报主键冲突错误
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_PRIMARY_KEY_VIOLATION);
    char errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_CONFIGURATION_LIMIT_EXCEEDED);
    char errorMsg3[errCodeLen] = {0};
    (void)snprintf(errorMsg3, errCodeLen, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    char errorMsg4[errCodeLen] = {0};
    (void)snprintf(errorMsg4, errCodeLen, "GMERR-%d", GMERR_REQUEST_TIME_OUT);
    AW_ADD_ERR_WHITE_LIST(4, errorMsg1, errorMsg2, errorMsg3, errorMsg4);

    AW_CHECK_LOG_END();

    int ret = 0;
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
}


// 100个并发耗费 tPoolVertex2.Other_047_002_001 (45048 ms)
// 250个并发耗费 tPoolVertex2.Other_047_002_001 (264985 ms)
// 300个并发耗费 tPoolVertex2.Other_047_002_001 (378404 ms)
// 001.vertex表，maxWorkerNum设置10,同步连接，20个多线程并发单步dml操作
TEST_F(tPoolVertex2, Other_047_002_011)
{
    AW_FUN_Log(LOG_STEP, "test_start");

    //创建 vertexLabel_1
    char labelName[128] = "ip4forward00000";

    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    int ret = GmcCreateVertexLabel(g_stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);
    AW_FUN_Log(LOG_INFO, ">>> create table.\n");
    uint64_t count;

    // 多线程并发dml操作，视图查询
    pthread_t thr_arr[g_threadNum];
    void *thr_ret[g_threadNum];
    int index[g_threadNum];
    memset(index, 0x00, g_threadNum * sizeof(int));
    memset(g_conn_tht, 0, sizeof(void *) * THR_NUM * 2);

    for (int i = 0; i < g_threadNum; i++) {
        index[i] = i;
        pthread_create(&thr_arr[i], NULL, ThreadVertexDml, (void *)&index[i]);
    }
 
    for (int i = 0; i < g_threadNum; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }

    sleep(1);

    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);

    // 光启就是有单独的心跳连接，发统计包的时候连接被断联了，就会出现这个；是正常的，加白名单。-- 沈俊辉
    #ifdef ENV_SUSE
        AddWhiteList(GMERR_NO_DATA);
        AddWhiteList(GMERR_NULL_VALUE_NOT_ALLOWED);
    #endif
    // 事务锁超时是正常现象 -- 卢家豪，毛金勇
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);

    // 仿真配置较小，偶现资源不足错误日志属于正常现象 -- 董荣超
#if defined(RUN_SIMULATE)
    AddWhiteList(GMERR_INSUFFICIENT_RESOURCES);
#endif
}


// 100个线程并发 tPoolVertex2.Other_047_002_002 (3313 ms)
// 300个线程并发 tPoolVertex2.Other_047_002_002 (10722 ms)
// 500个线程并发 tPoolVertex2.Other_047_002_002 (17007 ms)
// 800个线程并发  tPoolVertex2.Other_047_002_002 (27258 ms)
// 002.vertex表，maxWorkerNum设置10,同步连接，20个多线程并发批量dml操作
TEST_F(tPoolVertex2, Other_047_002_012)
{
    AW_FUN_Log(LOG_STEP, "test_start");

    //创建 vertexLabel_1
    char labelName[128] = "ip4forward00000";

    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    int ret = GmcCreateVertexLabel(g_stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);
    AW_FUN_Log(LOG_INFO, ">>> create table.\n");
    uint64_t count;

    // 多线程并发dml操作，视图查询
    pthread_t thr_arr[g_threadNum];
    void *thr_ret[g_threadNum];
    int index[g_threadNum];
    memset(index, 0x00, g_threadNum * sizeof(int));
    memset(g_conn_tht, 0, sizeof(void *) * THR_NUM * 2);

    for (int i = 0; i < g_threadNum; i++) {
        index[i] = i;
        pthread_create(&thr_arr[i], NULL, ThreadVertexBatchDml, (void *)&index[i]);
    }
 
    for (int i = 0; i < g_threadNum; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }

    sleep(1);

    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);

    // 光启就是有单独的心跳连接，发统计包的时候连接被断联了，就会出现这个；是正常的，加白名单。-- 沈俊辉
    #ifdef ENV_SUSE
        AddWhiteList(GMERR_NO_DATA);
        AddWhiteList(GMERR_NULL_VALUE_NOT_ALLOWED);
    #endif
    // 有可能出现后一个请求拿到前一个请求的应答消息
    AddWhiteList(GMERR_DATA_EXCEPTION);
}


// 003.vertex表，maxWorkerNum设置10,同步连接，20个多线程并发开启悲观读已提交事务，单步dml操作
TEST_F(tPoolVertex2, Other_047_002_013)
{
    AW_FUN_Log(LOG_STEP, "test_start");

    //创建 vertexLabel_1
    char labelName[128] = "ip4forward00000";

    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    int ret = GmcCreateVertexLabel(g_stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);
    AW_FUN_Log(LOG_INFO, ">>> create table.\n");
    uint64_t count;

    // 多线程并发dml操作，视图查询
    pthread_t thr_arr[g_threadNum];
    void *thr_ret[g_threadNum];
    int index[g_threadNum];
    memset(index, 0x00, g_threadNum * sizeof(int));
    memset(g_conn_tht, 0, sizeof(void *) * THR_NUM * 2);

    for (int i = 0; i < g_threadNum; i++) {
        index[i] = i;
        pthread_create(&thr_arr[i], NULL, ThreadVertexDmlTrans, (void *)&index[i]);
    }
 
    for (int i = 0; i < g_threadNum; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }

    AW_FUN_Log(LOG_INFO, ">>> trans commit succ count: %d\n", g_dmlTransSuccCount);
    EXPECT_GE(g_dmlTransSuccCount, 1);

    sleep(1);

    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_TRANSACTION_ROLLBACK);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    // 光启就是有单独的心跳连接，发统计包的时候连接被断联了，就会出现这个；是正常的，加白名单。-- 沈俊辉
    #ifdef ENV_SUSE
        AddWhiteList(GMERR_NO_DATA);
        AddWhiteList(GMERR_NULL_VALUE_NOT_ALLOWED);
    #endif

    // 仿真配置较小，偶现资源不足错误日志属于正常现象 -- 董荣超
#if defined(RUN_SIMULATE)
    AddWhiteList(GMERR_INSUFFICIENT_RESOURCES);
#endif
}
