
/*****************************************************************************
 Description  : 按需持久化 
 Notes        : 
            dts2025061230446_001 重启后，查询视图，最大记录数正常
            dts2025061230446_002 删除同名表1、表2表3表4，一般表1一般表2，重启后，表1、表2表3表4符合预期值，一般表值，不变化 max_record_count
            dts2025061230446_003 删除同名表1、表2表3表4表5表6，一般表1一般表2，重启后，表1、表2表3表4符合预期值，一般表值，不变化 max_record_count，表5表6不变化
            dts2025061230446_004 创建表，查询COUNT1，DDL、DML，超过count值，报错退出，后查询COUNT2，重启后查询COUNT3，预期COUNT1=COUNT2=COUNT3

表1 hw-ont-clk-prof:port-data-set.1
表2 hw-energy-mgnt-an:energy-saving-mode.1
表3 hw-energy-mgnt-an:method.1
表4 hw-energy-mgnt-an:parameter.1

备注：
   写入数据超过max_record_count,commit失败后 需要主动回滚，才能刷盘

 History      :
 Author       : 潘鹏 pwx860460
 Modification :
 Date         : 
*****************************************************************************/

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "dml_common.h"
#include <time.h>
#include <sys/time.h>

int g_beginIndex = 0;
int g_endIndex = 5000;
GmcTxConfigT g_trxConfig;
GmcConnT *g_conn2 = NULL;
GmcStmtT *g_stmt2 = NULL;
bool g_isRollbackReady = false;

char vl1_first[MAX_CMD_SIZE]; 
char vl2_first[MAX_CMD_SIZE]; 
char vl3_first[MAX_CMD_SIZE]; 
char vl4_first[MAX_CMD_SIZE]; 
char vl5_first[MAX_CMD_SIZE]; 
char vl6_first[MAX_CMD_SIZE]; 
char vl1_second[MAX_CMD_SIZE];
char vl2_second[MAX_CMD_SIZE];
char vl3_second[MAX_CMD_SIZE];
char vl4_second[MAX_CMD_SIZE];
char vl5_second[MAX_CMD_SIZE];
char vl6_second[MAX_CMD_SIZE];


class dts2025061230446 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {
        char *pwdDir = getenv("PWD");
        if (pwdDir == NULL) {
            printf("get env PWD fail.\n");
        }
        (void)sprintf(g_dbFilePath, "%s/gmdb", pwdDir);
        (void)Rmdir(g_dbFilePath);
        (void)sprintf(g_newDbFilePath, "%s/new_gmdb", pwdDir);
        char cmd_1[1024];
        sprintf(cmd_1, "mkdir %s", g_dbFilePath);
        system(cmd_1);

        system("sh $TEST_HOME/tools/stop.sh -f");
        int ret = ChangeGmserverCfg((char *)"recover", NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"0");
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = ChangeGmserverCfg((char *)"dataFileDirPath", g_dbFilePath);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = ChangeGmserverCfg((char *)"redoFlushByTrx", (char *)"1");
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = ChangeGmserverCfg((char *)"defaultTransactionType", (char *)"1");
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = ChangeGmserverCfg((char *)"defaultIsolationLevel", (char *)"2");
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        

        system("sh $TEST_HOME/tools/start.sh");
        ret = testEnvInit(-1, false);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        g_trxConfig.transMode = GMC_TRANS_USED_IN_CS;
        g_trxConfig.readOnly = false;
        g_trxConfig.type = GMC_TX_ISOLATION_REPEATABLE; // RR
        g_trxConfig.trxType = GMC_OPTIMISTIC_TRX;       // 乐观事务
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        testEnvClean();
        system("sh $TEST_HOME/tools/stop.sh -f");
        // 停掉服务，恢复配置，清理持久化文件
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        memset(g_command, 0, sizeof(g_command));
        char *homePath = getenv("HOME");
        (void)snprintf(g_command, MAX_CMD_SIZE, "rm %s/../data/gmdb/* -rf;rm gmdb -rf", homePath);
        system(g_command);
    }
};

void dts2025061230446::SetUp()
{
    (void)Rmdir(g_newDbFilePath);
    int ret = mkdir(g_newDbFilePath, S_IRUSR | S_IWUSR);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    AW_CHECK_LOG_BEGIN(0);
}

void dts2025061230446::TearDown()
{
    int ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERR_WHITE_LIST(1, "0");
    AW_CHECK_LOG_END();
}

const char *g_complexLabel1 = "hw-ont-clk-prof:port-data-set.1";
const char *g_complexLabel2 = "hw-energy-mgnt-an:energy-saving-mode.1";
const char *g_complexLabel3 = "hw-energy-mgnt-an:method.1";
const char *g_complexLabel4 = "hw-energy-mgnt-an:parameter.1";
const char *g_complexLabel5 = "vl_special_complex5";
const char *g_complexLabel6 = "vl_special_complex6";
char *g_complexSchema1 = NULL;
char *g_complexSchema2 = NULL;
char *g_complexSchema3 = NULL;
char *g_complexSchema4 = NULL;
char *g_complexSchema5 = NULL;
char *g_complexSchema6 = NULL;

 void create_vl() {
    int ret;
    // 建表
    (void)GmcDropVertexLabel(g_stmt, g_complexLabel1);
    readJanssonFile("schema/vl_lable1.gmjson", &g_complexSchema1);
    ret = GmcCreateVertexLabel(g_stmt, g_complexSchema1, g_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_complexSchema1);
    (void)GmcDropVertexLabel(g_stmt, g_complexLabel2);
    readJanssonFile("schema/vl_lable2.gmjson", &g_complexSchema2);
    ret = GmcCreateVertexLabel(g_stmt, g_complexSchema2, g_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_complexSchema2);
    (void)GmcDropVertexLabel(g_stmt, g_complexLabel3);
    readJanssonFile("schema/vl_lable3.gmjson", &g_complexSchema3);
    ret = GmcCreateVertexLabel(g_stmt, g_complexSchema3, g_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_complexSchema3);
    (void)GmcDropVertexLabel(g_stmt, g_complexLabel4);
    readJanssonFile("schema/vl_lable4.gmjson", &g_complexSchema4);
    ret = GmcCreateVertexLabel(g_stmt, g_complexSchema4, g_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_complexSchema4);
    (void)GmcDropVertexLabel(g_stmt, g_complexLabel5);
    readJanssonFile("schema/vl_lable5.gmjson", &g_complexSchema5);
    ret = GmcCreateVertexLabel(g_stmt, g_complexSchema5, g_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_complexSchema5);
    (void)GmcDropVertexLabel(g_stmt, g_complexLabel6);
    readJanssonFile("schema/vl_lable6.gmjson", &g_complexSchema6);
    ret = GmcCreateVertexLabel(g_stmt, g_complexSchema6, g_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_complexSchema6);
 }

 static int32_t GetCmdResult(const char *cmd, char *result, uint32_t len)
{
    if (result == NULL) {
        return 1;
    }
    FILE *pf = popen(cmd, "r");
    if (pf == NULL) {
        printf("popen(%s) error./n", cmd);
        return 1;
    }

    char *cmdOutput = result;

    while (fgets(cmdOutput, len, pf) != NULL) {
    }
    for (uint32_t i = 0; i < len; i++) {
        if (cmdOutput[i] == '\n') {
            cmdOutput[i] = '\0';
        }
        if (cmdOutput[i] == '\0') {
            break;
        }
    }
    pclose(pf);
    return 0;
}


// dts2025061230446_001 重启后，查询视图，最大记录数正常
TEST_F(dts2025061230446, dts2025061230446_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char max_record_count[MAX_CMD_SIZE] = "5000000"; 
    int ret;
    // 建表
    create_vl();
    char *lableName = (char *)"vl_special_complex5";
    // 开启事务
    ret = GmcTransStart(g_conn, &g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    // 写入数据
    ret = TestInsVertexSync(g_stmt, lableName, g_complexSet, g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 更新
    char indexName[] = "LocalHashKey";
    char *cond = (char *)"A3(uint64)=%i{10000}";
    char *updateFormat = (char *)"A1(int64)=%i{100};A2(uint32)=%i{1};A5(int64)=%i{1000};M0[0].B0(int32)=%i{10};M0[2]"
        ".B5(string)=%f{20}f3";
    ret = TestUpdVertexSync(g_stmt, lableName, indexName, cond, updateFormat, g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 提交事务
    ret = GmcTransCommit(g_conn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 重启
    ret = RestartAndConn();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "second check ");
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=hw-ont-clk-prof:port-data-set.1 |tee second1.txt");
    memset(vl1_second, 0, sizeof(vl1_second));
    ret = GetCmdResult("cat second1.txt |grep MAX_RECORD_COUNT:|awk '{print $2}'", vl1_second, MAX_CMD_SIZE);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_STR(vl1_second,max_record_count);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=hw-energy-mgnt-an:energy-saving-mode.1 |tee second2.txt");
    ret = GetCmdResult("cat second2.txt |grep MAX_RECORD_COUNT:|awk '{print $2}'", vl2_second, MAX_CMD_SIZE);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_STR(vl2_second,max_record_count);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=hw-energy-mgnt-an:method.1 |tee second3.txt");
    ret = GetCmdResult("cat second3.txt |grep MAX_RECORD_COUNT:|awk '{print $2}'", vl3_second, MAX_CMD_SIZE);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_STR(vl3_second,max_record_count);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=hw-energy-mgnt-an:parameter.1 |tee second4.txt");
    ret = GetCmdResult("cat second4.txt |grep MAX_RECORD_COUNT:|awk '{print $2}'", vl4_second, MAX_CMD_SIZE);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_STR(vl4_second,max_record_count);
    // 删除部分
    ret = TestdelVertexSync(g_stmt, lableName, indexName, cond, g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验内容
    ret=TestSelVertexRecord(g_stmt, lableName, indexName, cond, updateFormat, g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

}
// dts2025061230446_002 删除同名表1、表2表3表4，一般表1一般表2，重启后，表1、表2表3表4符合预期值，一般表值，不变化 max_record_count
TEST_F(dts2025061230446, dts2025061230446_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char max_record_count[MAX_CMD_SIZE] = "5000000"; 
    int ret;
    // 建表
    create_vl();
    char *lableName = (char *)"vl_special_complex5";
    // 开启事务
    ret = GmcTransStart(g_conn, &g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    // 写入数据
    ret = TestInsVertexSync(g_stmt, lableName, g_complexSet, g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 更新
    char indexName[] = "LocalHashKey";
    char *cond = (char *)"A3(uint64)=%i{10000}";
    char *updateFormat = (char *)"A1(int64)=%i{100};A2(uint32)=%i{1};A5(int64)=%i{1000};M0[0].B0(int32)=%i{10};M0[2]"
        ".B5(string)=%f{20}f3";
    ret = TestUpdVertexSync(g_stmt, lableName, indexName, cond, updateFormat, g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "first check");
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=hw-ont-clk-prof:port-data-set.1 |tee first1.txt");
    memset(vl1_first, 0, sizeof(vl1_first));
    ret = GetCmdResult("cat first1.txt |grep MAX_RECORD_COUNT:|awk '{print $2}'", vl1_first, MAX_CMD_SIZE);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_STR(vl1_first,max_record_count);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=hw-energy-mgnt-an:energy-saving-mode.1 |tee first2.txt");
    memset(vl2_first, 0, sizeof(vl2_first));
    ret = GetCmdResult("cat first2.txt |grep MAX_RECORD_COUNT:|awk '{print $2}'", vl2_first, MAX_CMD_SIZE);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_STR(vl2_first,max_record_count);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=hw-energy-mgnt-an:method.1 |tee first3.txt");
    memset(vl3_first, 0, sizeof(vl3_first));
    ret = GetCmdResult("cat first3.txt |grep MAX_RECORD_COUNT:|awk '{print $2}'", vl3_first, MAX_CMD_SIZE);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_STR(vl3_first,max_record_count);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=hw-energy-mgnt-an:parameter.1 |tee first4.txt");
    memset(vl4_first, 0, sizeof(vl4_first));
    ret = GetCmdResult("cat first4.txt |grep MAX_RECORD_COUNT:|awk '{print $2}'", vl4_first, MAX_CMD_SIZE);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_STR(vl4_first,max_record_count);
    // 提交事务
    ret = GmcTransCommit(g_conn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 重启
    ret = RestartAndConn();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "second check ");
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=hw-ont-clk-prof:port-data-set.1 |tee second1.txt");
    memset(vl1_second, 0, sizeof(vl1_second));
    ret = GetCmdResult("cat second1.txt |grep MAX_RECORD_COUNT:|awk '{print $2}'", vl1_second, MAX_CMD_SIZE);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_STR(vl1_second,max_record_count);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=hw-energy-mgnt-an:energy-saving-mode.1 |tee second2.txt");
    ret = GetCmdResult("cat second2.txt |grep MAX_RECORD_COUNT:|awk '{print $2}'", vl2_second, MAX_CMD_SIZE);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_STR(vl2_second,max_record_count);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=hw-energy-mgnt-an:method.1 |tee second3.txt");
    ret = GetCmdResult("cat second3.txt |grep MAX_RECORD_COUNT:|awk '{print $2}'", vl3_second, MAX_CMD_SIZE);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_STR(vl3_second,max_record_count);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=hw-energy-mgnt-an:parameter.1 |tee second4.txt");
    ret = GetCmdResult("cat second4.txt |grep MAX_RECORD_COUNT:|awk '{print $2}'", vl4_second, MAX_CMD_SIZE);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_STR(vl4_second,max_record_count);
    // 删除部分
    ret = TestdelVertexSync(g_stmt, lableName, indexName, cond, g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验内容
    ret=TestSelVertexRecord(g_stmt, lableName, indexName, cond, updateFormat, g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

}
// dts2025061230446_003 删除同名表1、表2表3表4表5表6，一般表1一般表2，重启后，表1、表2表3表4符合预期值，一般表值，不变化 max_record_count，表5表6不变化
TEST_F(dts2025061230446, dts2025061230446_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char max_record_count[MAX_CMD_SIZE] = "5000000"; 
    int ret;
    // 建表
    create_vl();
    char *lableName = (char *)"vl_special_complex5";
    // 开启事务
    ret = GmcTransStart(g_conn, &g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    // 写入数据
    ret = TestInsVertexSync(g_stmt, lableName, g_complexSet, g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 更新
    char indexName[] = "LocalHashKey";
    char *cond = (char *)"A3(uint64)=%i{10000}";
    char *updateFormat = (char *)"A1(int64)=%i{100};A2(uint32)=%i{1};A5(int64)=%i{1000};M0[0].B0(int32)=%i{10};M0[2]"
        ".B5(string)=%f{20}f3";
    ret = TestUpdVertexSync(g_stmt, lableName, indexName, cond, updateFormat, g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "first check");
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=hw-ont-clk-prof:port-data-set.1 |tee first1.txt");
    memset(vl1_first, 0, sizeof(vl1_first));
    ret = GetCmdResult("cat first1.txt |grep MAX_RECORD_COUNT:|awk '{print $2}'", vl1_first, MAX_CMD_SIZE);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_STR(vl1_first,max_record_count);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=hw-energy-mgnt-an:energy-saving-mode.1 |tee first2.txt");
    memset(vl2_first, 0, sizeof(vl2_first));
    ret = GetCmdResult("cat first2.txt |grep MAX_RECORD_COUNT:|awk '{print $2}'", vl2_first, MAX_CMD_SIZE);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_STR(vl2_first,max_record_count);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=hw-energy-mgnt-an:method.1 |tee first3.txt");
    memset(vl3_first, 0, sizeof(vl3_first));
    ret = GetCmdResult("cat first3.txt |grep MAX_RECORD_COUNT:|awk '{print $2}'", vl3_first, MAX_CMD_SIZE);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_STR(vl3_first,max_record_count);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=hw-energy-mgnt-an:parameter.1 |tee first4.txt");
    memset(vl4_first, 0, sizeof(vl4_first));
    ret = GetCmdResult("cat first4.txt |grep MAX_RECORD_COUNT:|awk '{print $2}'", vl4_first, MAX_CMD_SIZE);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_STR(vl4_first,max_record_count);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=vl_special_complex5 |tee first5.txt");
    memset(vl5_first, 0, sizeof(vl5_first));
    ret = GetCmdResult("cat first5.txt |grep MAX_RECORD_COUNT:|awk '{print $2}'", vl5_first, MAX_CMD_SIZE);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_STR(vl5_first,max_record_count);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=vl_special_complex6 |tee first6.txt");
    memset(vl6_first, 0, sizeof(vl6_first));
    ret = GetCmdResult("cat first6.txt |grep MAX_RECORD_COUNT:|awk '{print $2}'", vl6_first, MAX_CMD_SIZE);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_STR(vl6_first,max_record_count);
    // 提交事务
    ret = GmcTransCommit(g_conn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 重启
    ret = RestartAndConn();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "second check ");
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=hw-ont-clk-prof:port-data-set.1 |tee second1.txt");
    memset(vl1_second, 0, sizeof(vl1_second));
    ret = GetCmdResult("cat second1.txt |grep MAX_RECORD_COUNT:|awk '{print $2}'", vl1_second, MAX_CMD_SIZE);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_STR(vl1_second,max_record_count);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=hw-energy-mgnt-an:energy-saving-mode.1 |tee second2.txt");
    ret = GetCmdResult("cat second2.txt |grep MAX_RECORD_COUNT:|awk '{print $2}'", vl2_second, MAX_CMD_SIZE);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_STR(vl2_second,max_record_count);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=hw-energy-mgnt-an:method.1 |tee second3.txt");
    ret = GetCmdResult("cat second3.txt |grep MAX_RECORD_COUNT:|awk '{print $2}'", vl3_second, MAX_CMD_SIZE);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_STR(vl3_second,max_record_count);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=hw-energy-mgnt-an:parameter.1 |tee second4.txt");
    ret = GetCmdResult("cat second4.txt |grep MAX_RECORD_COUNT:|awk '{print $2}'", vl4_second, MAX_CMD_SIZE);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_STR(vl4_second,max_record_count);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=vl_special_complex5 |tee first5.txt");
    memset(vl5_first, 0, sizeof(vl5_first));
    ret = GetCmdResult("cat first5.txt |grep MAX_RECORD_COUNT:|awk '{print $2}'", vl5_first, MAX_CMD_SIZE);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_STR(vl5_first,max_record_count);

    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=vl_special_complex6 |tee first6.txt");
    memset(vl6_first, 0, sizeof(vl6_first));
    ret = GetCmdResult("cat first6.txt |grep MAX_RECORD_COUNT:|awk '{print $2}'", vl6_first, MAX_CMD_SIZE);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_STR(vl6_first,max_record_count);
    // 删除部分
    ret = TestdelVertexSync(g_stmt, lableName, indexName, cond, g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验内容
    ret=TestSelVertexRecord(g_stmt, lableName, indexName, cond, updateFormat, g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

}
// dts2025061230446_004 创建表，查询COUNT1，DDL、DML，超过count值，报错退出，后查询COUNT2，重启后查询COUNT3，预期COUNT1=COUNT2=COUNT3
TEST_F(dts2025061230446, dts2025061230446_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char max_record_count[MAX_CMD_SIZE] = "500"; 
    int ret;
    char *lableName = (char *)"vl_special_complex";
    // 建表
    (void)GmcDropVertexLabel(g_stmt, lableName);
    readJanssonFile("schema/vl_special_complex_uniq.gmjson", &g_complexSchema);
    const char *g_config_new = "{\"max_record_count\" : 500, \"isFastReadUncommitted\":0, \"defragmentation\":false, \"max_record_count_check\":true }";
    ret = GmcCreateVertexLabel(g_stmt, g_complexSchema, g_config_new);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_complexSchema);
    g_complexSchema = NULL;
    
    // 开启事务
    ret = GmcTransStart(g_conn, &g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    ret = TestInsVertexSync(g_stmt, lableName, g_complexSet, g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 更新
    char indexName[] = "LocalHashKey";
    char *cond = (char *)"A3(uint64)=%i{10000}";
    char *updateFormat = (char *)"A1(int64)=%i{100};A2(uint32)=%i{1};A5(int64)=%i{1000};M0[0].B0(int32)=%i{10};M0[2]"
        ".B5(string)=%f{20}f3";
    ret = TestUpdVertexSync(g_stmt, lableName, indexName, cond, updateFormat, g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "first check");
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=vl_special_complex |tee first1.txt");
    memset(vl1_first, 0, sizeof(vl1_first));
    ret = GetCmdResult("cat first1.txt |grep MAX_RECORD_COUNT:|awk '{print $2}'", vl1_first, MAX_CMD_SIZE);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_STR(vl1_first,max_record_count);

    // 提交事务,超限回退
    ret = GmcTransCommit(g_conn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_RECORD_COUNT_LIMIT_EXCEEDED, ret);
    ret = GmcTransRollBack(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    system("gmsysview count");
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO");
    // 重启
    ret = RestartAndConn();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_beginIndex = 0;
    g_endIndex = 500;
    // 开启事务
    ret = GmcTransStart(g_conn, &g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    ret = TestInsVertexSync(g_stmt, lableName, g_complexSet, g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 提交事务
    ret = GmcTransCommit(g_conn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "second check ");
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=vl_special_complex |tee second1.txt");
    memset(vl1_second, 0, sizeof(vl1_second));
    ret = GetCmdResult("cat second1.txt |grep MAX_RECORD_COUNT:|awk '{print $2}'", vl1_second, MAX_CMD_SIZE);
    EXPECT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_STR(vl1_second,max_record_count);

    // 校验内容
    ret=TestSelVertexRecord(g_stmt, lableName, indexName, cond, updateFormat, g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

}
