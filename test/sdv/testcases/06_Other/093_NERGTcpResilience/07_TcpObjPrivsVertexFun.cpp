/*****************************************************************************
 Description  : Vertex对象权限check
 Notes        :
 History      :
 Author       : <PERSON><PERSON>hao 30021737
 Modification :
 Date         : 2025/4/8
*****************************************************************************/
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "TcpObjPrivsTest.h"
#include "TcpRegPolicy.h"

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
GmcStmtT *g_stmt_async = NULL;
GmcConnT *g_conn_async = NULL;
int ret = 0;
char *config_json = NULL;
char *normal_vertexlabel_schema = NULL;

const char *g_normal_vertexlabel_name = "T39_all_type";
const char *g_normal_pk_name = "T39_K0";
const char *g_normal_sk_name = "T39_hash";

#define MAX_CMD_SIZE 2048
char g_command[MAX_CMD_SIZE] = {0};

class TcpObjPrivsVertexFun : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        // 打开鉴权模式
        system("sh $TEST_HOME/tools/modifyCfg.sh \"userPolicyMode=2\"");
        system("sh $TEST_HOME/tools/stop.sh -f");
        system("sh $TEST_HOME/tools/start.sh -f");
        int ret = testEnvInit();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        testEnvClean();
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void TcpObjPrivsVertexFun::SetUp()
{
    // 导入白名单
    char allow_list_file[128] = "./allow_list/allow_list.gmuser";
    char sys_policy_file[128] = "./gmpolicy_file/SysPrivsAll.gmpolicy";
    AllowlistGmruleImport(allow_list_file, expectValue1);
    PolicyGmruleImport(sys_policy_file, expectValue1);

    readJanssonFile("schema/configvertex.gmconfig", &config_json);
    ASSERT_NE((void *)NULL, config_json);
    readJanssonFile("schema/ObjVertexLabel.gmjson", &normal_vertexlabel_schema);
    ASSERT_NE((void *)NULL, normal_vertexlabel_schema);
    ret = testGmcConnect(&g_conn, &g_stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL, -1, 0,
        &g_epollData.userEpollFd, false, g_ConnUser);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info, NULL, NULL, NULL, NULL,
        -1, 0, &g_epollData.userEpollFd, false, g_ConnUser);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN();
}

void TcpObjPrivsVertexFun::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 添加删除白名单操作
    char allow_list_file[128] = "./allow_list/allow_list.gmuser";
    char sys_policy_file[128] = "./gmpolicy_file/SysPrivsAll.gmpolicy";
    AllowlistGmruleremove(allow_list_file, expectValue1);
    PolicyGmruleRevoke(sys_policy_file, expectValue1);
    free(config_json);
    free(normal_vertexlabel_schema);
}

// 001.Vertex对象权限Insert校验，同步插入，权限导入前返回错误码，导入后操作成功
TEST_F(TcpObjPrivsVertexFun, Other_093_081)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_INSUFFICIENT_PRIVILEGE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // grant other
    char obj_policy_file1[128] = "./gmpolicy_file/ObjVertexNoInsert.gmpolicy";
    PolicyGmruleImport(obj_policy_file1, expectValue1);

    // before grant
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    expectAffectRows = 0;
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
        ret = testGmcGetLastError(
            "Have no privilege. Insufficient privilege, labelName: T39_all_type, user: (TcpConnUser :  : ).");
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, 0);

    // grant insert
    char obj_policy_file2[128] = "./gmpolicy_file/ObjVertexInsert.gmpolicy";
    PolicyGmruleImport(obj_policy_file2, expectValue1);
    // insert vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    expectAffectRows = 1;
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, count);
    PolicyGmruleRevoke(obj_policy_file1, expectValue1);
    PolicyGmruleRevoke(obj_policy_file2, expectValue1);

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 002. Vertex对象权限Insert校验，异步插入，权限导入前返回错误码，导入后操作成功
TEST_F(TcpObjPrivsVertexFun, Other_093_082)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    void *vertexLabel1 = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // grant other
    char obj_policy_file1[128] = "./gmpolicy_file/ObjVertexNoInsert.gmpolicy";
    PolicyGmruleImport(obj_policy_file1, expectValue1);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // before grant
    expectAffectRows = 0;
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt_async, i);
        set_VertexProperty_F9(g_stmt_async, i);
        set_VertexProperty(g_stmt_async, i);
        data = {0};
        data.lastError =
            "Have no privilege. Insufficient privilege, labelName: T39_all_type, user: (TcpConnUser :  : ).";
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
        AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, data.status);
        AW_MACRO_ASSERT_EQ_INT(expectAffectRows, data.affectRows);
    }

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, 0);

    // grant insert
    char obj_policy_file2[128] = "./gmpolicy_file/ObjVertexInsert.gmpolicy";
    PolicyGmruleImport(obj_policy_file2, expectValue1);
    // insert vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    expectAffectRows = 1;
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt_async, i);
        set_VertexProperty_F9(g_stmt_async, i);
        set_VertexProperty(g_stmt_async, i);
        data = {0};
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_ASSERT_EQ_INT(expectAffectRows, data.affectRows);
    }
    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, count);

    PolicyGmruleRevoke(obj_policy_file1, expectValue1);
    PolicyGmruleRevoke(obj_policy_file2, expectValue1);

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 003. Vertex对象权限Insert校验，通过json插入，权限导入前返回错误码，导入后操作成功
TEST_F(TcpObjPrivsVertexFun, Other_093_083)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // grant other
    char obj_policy_file1[128] = "./gmpolicy_file/ObjVertexNoInsert.gmpolicy";
    PolicyGmruleImport(obj_policy_file1, expectValue1);
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
    // before grant
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    TestInsertVertexByJson(g_stmt, "schema/T39_all_type.vertexdata", GMERR_INSUFFICIENT_PRIVILEGE);
    ret = testGmcGetLastError(
        "Have no privilege. Insufficient privilege, labelName: T39_all_type, user: (TcpConnUser :  : ).");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, 0);

    // grant insert
    char obj_policy_file2[128] = "./gmpolicy_file/ObjVertexInsert.gmpolicy";
    PolicyGmruleImport(obj_policy_file2, expectValue1);

    // insert vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    TestInsertVertexByJson(g_stmt, "schema/T39_all_type.vertexdata", GMERR_OK);

    scan_verex_test(g_stmt, g_normal_vertexlabel_name, count);

    PolicyGmruleRevoke(obj_policy_file1, expectValue1);
    PolicyGmruleRevoke(obj_policy_file2, expectValue1);

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 004. Vertex对象权限Insert校验，批量插入，权限导入前返回错误码，导入后操作成功
TEST_F(TcpObjPrivsVertexFun, Other_093_084)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int count = 100;
    int expectAffectRows = 1;
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // grant other
    char obj_policy_file1[128] = "./gmpolicy_file/ObjVertexNoInsert.gmpolicy";
    PolicyGmruleImport(obj_policy_file1, expectValue1);
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
    // before grant
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcGetLastError(VERTEX_PRIV_ERROR);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(count, totalNum);
    AW_MACRO_ASSERT_EQ_INT(0, successNum);

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, 0);

    // grant insert
    char obj_policy_file2[128] = "./gmpolicy_file/ObjVertexInsert.gmpolicy";
    PolicyGmruleImport(obj_policy_file2, expectValue1);
    // insert
    ret = GmcBatchReset(batch);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(count, totalNum);
    AW_MACRO_ASSERT_EQ_INT(count, successNum);

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, count);
    GmcBatchDestroy(batch);

    PolicyGmruleRevoke(obj_policy_file1, expectValue1);
    PolicyGmruleRevoke(obj_policy_file2, expectValue1);

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 005. Vertex对象权限update校验，同步更新，权限导入前返回错误码，导入后操作成功
TEST_F(TcpObjPrivsVertexFun, Other_093_085)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // grant other
    char obj_policy_file1[128] = "./gmpolicy_file/ObjVertexNoUpdate.gmpolicy";
    PolicyGmruleImport(obj_policy_file1, expectValue1);

    // insert
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // before grant
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    expectAffectRows = 0;
    for (int i = 0; i < count; i++) {
        uint32_t pk = i;
        int NewVal = i + count;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty(g_stmt, NewVal);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
        AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
        ret = testGmcGetLastError(VERTEX_PRIV_ERROR);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, count);
    GmcResetStmt(g_stmt);

    // grant update
    char obj_policy_file2[128] = "./gmpolicy_file/ObjVertexUpdate.gmpolicy";
    PolicyGmruleImport(obj_policy_file2, expectValue1);

    // update vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    expectAffectRows = 1;
    for (int i = 0; i < count; i++) {
        uint32_t pk = i;
        int NewVal = i + count;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty(g_stmt, NewVal);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, count, true);
    PolicyGmruleRevoke(obj_policy_file1, expectValue1);
    PolicyGmruleRevoke(obj_policy_file2, expectValue1);

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 006. Vertex对象权限update校验，异步更新，权限导入前返回错误码，导入后操作成功
TEST_F(TcpObjPrivsVertexFun, Other_093_086)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    void *vertexLabel1 = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // grant other
    char obj_policy_file1[128] = "./gmpolicy_file/ObjVertexNoUpdate.gmpolicy";
    PolicyGmruleImport(obj_policy_file1, expectValue1);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // insert
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // before grant
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    expectAffectRows = 0;
    for (int i = 0; i < count; i++) {
        uint32_t pk = i;
        int NewVal = i + count;
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty(g_stmt_async, NewVal);
        data = {0};
        data.lastError = VERTEX_PRIV_ERROR;
        ret = GmcSetIndexKeyName(g_stmt_async, g_normal_pk_name);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT updateRequestCtx;
        updateRequestCtx.updateCb = update_vertex_callback;
        updateRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &updateRequestCtx);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
        AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, data.status);
        AW_MACRO_ASSERT_EQ_INT(expectAffectRows, data.affectRows);
    }

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, count);

    // grant update
    char obj_policy_file2[128] = "./gmpolicy_file/ObjVertexUpdate.gmpolicy";
    PolicyGmruleImport(obj_policy_file2, expectValue1);
    // update vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    expectAffectRows = 1;
    for (int i = 0; i < count; i++) {
        uint32_t pk = i;
        int NewVal = i + count;
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty(g_stmt_async, NewVal);
        data = {0};
        ret = GmcSetIndexKeyName(g_stmt_async, g_normal_pk_name);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT updateRequestCtx;
        updateRequestCtx.updateCb = update_vertex_callback;
        updateRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &updateRequestCtx);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_ASSERT_EQ_INT(expectAffectRows, data.affectRows);
    }

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, count, true);

    PolicyGmruleRevoke(obj_policy_file1, expectValue1);
    PolicyGmruleRevoke(obj_policy_file2, expectValue1);

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 007. Vertex对象权限update校验，同步条件更新，权限导入前返回错误码，导入后操作成功
TEST_F(TcpObjPrivsVertexFun, Other_093_087)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int NewVal = 2;
    int count = 100;
    int expectAffectRows = 1;
    const char *cond = "F7<100";
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // grant other
    char obj_policy_file1[128] = "./gmpolicy_file/ObjVertexNoUpdate.gmpolicy";
    PolicyGmruleImport(obj_policy_file1, expectValue1);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // insert
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
    // before grant
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    expectAffectRows = 0;
    set_VertexProperty(g_stmt, NewVal);
    ret = GmcSetFilter(g_stmt, cond);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(VERTEX_PRIV_ERROR);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // scan vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        int64_t SKValue = cnt;
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (isFinish == true || ret != GMERR_OK) {
            break;
        }
        query_VertexProperty(g_stmt, cnt);
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &SKValue);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        cnt++;
    }
    AW_MACRO_ASSERT_EQ_INT(count, cnt);
    AW_FUN_Log(LOG_STEP, "[INFO] scan cnt:%d\n", cnt);

    GmcResetStmt(g_stmt);

    // grant update
    char obj_policy_file2[128] = "./gmpolicy_file/ObjVertexUpdate.gmpolicy";
    PolicyGmruleImport(obj_policy_file2, expectValue1);
    // update vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    expectAffectRows = count;
    set_VertexProperty(g_stmt, NewVal);
    ret = GmcSetFilter(g_stmt, cond);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // scan vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    isFinish = false;
    cnt = 0;
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    while (!isFinish) {
        int64_t SKValue = cnt;
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (isFinish == true || ret != GMERR_OK) {
            break;
        }
        query_VertexProperty(g_stmt, NewVal);
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &SKValue);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        cnt++;
    }
    AW_MACRO_ASSERT_EQ_INT(count, cnt);
    AW_FUN_Log(LOG_STEP, "[INFO] scan cnt:%d\n", cnt);

    PolicyGmruleRevoke(obj_policy_file1, expectValue1);
    PolicyGmruleRevoke(obj_policy_file2, expectValue1);

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 008.Vertex对象权限update校验，异步条件更新，权限导入前返回错误码，导入后操作成功
TEST_F(TcpObjPrivsVertexFun, Other_093_088)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int count = 100;
    int NewVal = 2;
    const char *cond = "F7<100";
    int expectAffectRows = 1;
    void *vertexLabel = NULL;
    void *vertexLabel1 = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // grant other
    char obj_policy_file1[128] = "./gmpolicy_file/ObjVertexNoUpdate.gmpolicy";
    PolicyGmruleImport(obj_policy_file1, expectValue1);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // insert
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
    // before grant
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    expectAffectRows = 0;
    set_VertexProperty(g_stmt_async, NewVal);
    data = {0};
    data.lastError = VERTEX_PRIV_ERROR;
    ret = GmcSetFilter(g_stmt_async, cond);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcAsyncRequestDoneContextT updateByCondRequestCtx;
    updateByCondRequestCtx.updateCb = update_vertex_by_cond_callback;
    updateByCondRequestCtx.userData = &data;
    ret = GmcExecuteAsync(g_stmt_async, &updateByCondRequestCtx);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, data.status);
    AW_MACRO_ASSERT_EQ_INT(expectAffectRows, data.affectRows);

    // scan vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        int64_t SKValue = cnt;
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (isFinish == true || ret != GMERR_OK) {
            break;
        }
        query_VertexProperty(g_stmt, cnt);
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &SKValue);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        cnt++;
    }
    AW_MACRO_ASSERT_EQ_INT(count, cnt);
    AW_FUN_Log(LOG_STEP, "[INFO] scan cnt:%d\n", cnt);

    // grant update
    char obj_policy_file2[128] = "./gmpolicy_file/ObjVertexUpdate.gmpolicy";
    PolicyGmruleImport(obj_policy_file2, expectValue1);

    // update vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    expectAffectRows = count;
    set_VertexProperty(g_stmt_async, NewVal);
    data = {0};
    ret = GmcSetFilter(g_stmt_async, cond);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // GmcAsyncRequestDoneContextT updateByCondRequestCtx;
    updateByCondRequestCtx.updateCb = update_vertex_by_cond_callback;
    updateByCondRequestCtx.userData = &data;
    ret = GmcExecuteAsync(g_stmt_async, &updateByCondRequestCtx);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_ASSERT_EQ_INT(expectAffectRows, data.affectRows);

    // scan vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    isFinish = false;
    cnt = 0;
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    while (!isFinish) {
        int64_t SKValue = cnt;
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (isFinish == true || ret != GMERR_OK) {
            break;
        }
        query_VertexProperty(g_stmt, NewVal);
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &SKValue);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        cnt++;
    }
    AW_MACRO_ASSERT_EQ_INT(count, cnt);
    AW_FUN_Log(LOG_STEP, "[INFO] scan cnt:%d\n", cnt);

    PolicyGmruleRevoke(obj_policy_file1, expectValue1);
    PolicyGmruleRevoke(obj_policy_file2, expectValue1);

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 009. Vertex对象权限update校验，批量更新，权限导入前返回错误码，导入后操作成功
TEST_F(TcpObjPrivsVertexFun, Other_093_089)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int count = 100;
    int expectAffectRows = 1;
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // grant other
    char obj_policy_file1[128] = "./gmpolicy_file/ObjVertexNoUpdate.gmpolicy";
    PolicyGmruleImport(obj_policy_file1, expectValue1);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // insert
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
    // before grant
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty(g_stmt, i + count);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcGetLastError(VERTEX_PRIV_ERROR);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(count, totalNum);
    AW_MACRO_ASSERT_EQ_INT(0, successNum);

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, count);

    // grant update
    char obj_policy_file2[128] = "./gmpolicy_file/ObjVertexUpdate.gmpolicy";
    PolicyGmruleImport(obj_policy_file2, expectValue1);
    // update vertex
    ret = GmcBatchReset(batch);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty(g_stmt, i + count);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(count, totalNum);
    AW_MACRO_ASSERT_EQ_INT(count, successNum);

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, count, true);
    GmcBatchDestroy(batch);

    PolicyGmruleRevoke(obj_policy_file1, expectValue1);
    PolicyGmruleRevoke(obj_policy_file2, expectValue1);

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 010. Vertex对象权限delete校验，同步删除，权限导入前返回错误码，导入后操作成功
TEST_F(TcpObjPrivsVertexFun, Other_093_090)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // grant other
    char obj_policy_file1[128] = "./gmpolicy_file/ObjVertexNoDelete.gmpolicy";
    PolicyGmruleImport(obj_policy_file1, expectValue1);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // insert
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // before grant
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    expectAffectRows = 0;
    for (int i = 0; i < count; i++) {
        uint32_t pk = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
        AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
        ret = testGmcGetLastError(VERTEX_PRIV_ERROR);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, count);

    // grant delete
    char obj_policy_file2[128] = "./gmpolicy_file/ObjVertexDelete.gmpolicy";
    PolicyGmruleImport(obj_policy_file2, expectValue1);

    // delete vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    expectAffectRows = 1;
    for (int i = 0; i < count; i++) {
        uint32_t pk = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, 0);

    PolicyGmruleRevoke(obj_policy_file1, expectValue1);
    PolicyGmruleRevoke(obj_policy_file2, expectValue1);

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 011. Vertex对象权限delete校验，异步删除，权限导入前返回错误码，导入后操作成功
TEST_F(TcpObjPrivsVertexFun, Other_093_091)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    void *vertexLabel1 = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // grant other
    char obj_policy_file1[128] = "./gmpolicy_file/ObjVertexNoDelete.gmpolicy";
    PolicyGmruleImport(obj_policy_file1, expectValue1);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // insert
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // before grant
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    expectAffectRows = 0;
    for (int i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        data = {0};
        data.lastError = VERTEX_PRIV_ERROR;
        ret = GmcSetIndexKeyName(g_stmt_async, g_normal_pk_name);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT deleteRequestCtx;
        deleteRequestCtx.deleteCb = delete_vertex_callback;
        deleteRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &deleteRequestCtx);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
        AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, data.status);
        AW_MACRO_ASSERT_EQ_INT(expectAffectRows, data.affectRows);
    }

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, count);

    // grant delete
    char obj_policy_file2[128] = "./gmpolicy_file/ObjVertexDelete.gmpolicy";
    PolicyGmruleImport(obj_policy_file2, expectValue1);
    // delete vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    expectAffectRows = 1;
    for (int i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        data = {0};
        ret = GmcSetIndexKeyName(g_stmt_async, g_normal_pk_name);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT deleteRequestCtx;
        deleteRequestCtx.deleteCb = delete_vertex_callback;
        deleteRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &deleteRequestCtx);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_ASSERT_EQ_INT(expectAffectRows, data.affectRows);
    }

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, 0);

    PolicyGmruleRevoke(obj_policy_file1, expectValue1);
    PolicyGmruleRevoke(obj_policy_file2, expectValue1);

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 012. Vertex对象权限delete校验，同步条件删除，权限导入前返回错误码，导入后操作成功
TEST_F(TcpObjPrivsVertexFun, Other_093_092)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int NewVal = 2;
    int count = 100;
    int expectAffectRows = 1;
    const char *cond = "F7<100";
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // grant other
    char obj_policy_file1[128] = "./gmpolicy_file/ObjVertexNoDelete.gmpolicy";
    PolicyGmruleImport(obj_policy_file1, expectValue1);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // insert
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // before grant
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    expectAffectRows = 0;
    set_VertexProperty(g_stmt, NewVal);
    ret = GmcSetFilter(g_stmt, cond);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError(VERTEX_PRIV_ERROR);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, count);

    // grant delete
    char obj_policy_file2[128] = "./gmpolicy_file/ObjVertexDelete.gmpolicy";
    PolicyGmruleImport(obj_policy_file2, expectValue1);

    // delete vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    expectAffectRows = count;
    ret = GmcSetFilter(g_stmt, cond);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, 0);

    PolicyGmruleRevoke(obj_policy_file1, expectValue1);
    PolicyGmruleRevoke(obj_policy_file2, expectValue1);

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 013. Vertex对象权限delete校验，异步条件删除，权限导入前返回错误码，导入后操作成功
TEST_F(TcpObjPrivsVertexFun, Other_093_093)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int count = 100;
    int NewVal = 2;
    const char *cond = "F7<100";
    int expectAffectRows = 1;
    void *vertexLabel = NULL;
    void *vertexLabel1 = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // grant other
    char obj_policy_file1[128] = "./gmpolicy_file/ObjVertexNoDelete.gmpolicy";
    PolicyGmruleImport(obj_policy_file1, expectValue1);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // insert
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // before grant
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    expectAffectRows = 0;
    data = {0};
    data.lastError = VERTEX_PRIV_ERROR;
    ret = GmcSetFilter(g_stmt_async, cond);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcAsyncRequestDoneContextT deleteByCondRequestCtx;
    deleteByCondRequestCtx.deleteCb = delete_vertex_by_cond_callback;
    deleteByCondRequestCtx.userData = &data;
    ret = GmcExecuteAsync(g_stmt_async, &deleteByCondRequestCtx);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, data.status);
    AW_MACRO_ASSERT_EQ_INT(expectAffectRows, data.affectRows);

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, count);

    // grant delete
    char obj_policy_file2[128] = "./gmpolicy_file/ObjVertexDelete.gmpolicy";
    PolicyGmruleImport(obj_policy_file2, expectValue1);

    // delete vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    expectAffectRows = count;
    data = {0};
    ret = GmcSetFilter(g_stmt_async, cond);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // GmcAsyncRequestDoneContextT deleteByCondRequestCtx;
    deleteByCondRequestCtx.deleteCb = delete_vertex_by_cond_callback;
    deleteByCondRequestCtx.userData = &data;
    ret = GmcExecuteAsync(g_stmt_async, &deleteByCondRequestCtx);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_ASSERT_EQ_INT(expectAffectRows, data.affectRows);

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, 0);

    PolicyGmruleRevoke(obj_policy_file1, expectValue1);
    PolicyGmruleRevoke(obj_policy_file2, expectValue1);

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}


// 015. Vertex对象权限delete校验，批量删除顶点，权限导入前返回错误码，导入后操作成功
TEST_F(TcpObjPrivsVertexFun, Other_093_095)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int count = 100;
    int expectAffectRows = 1;
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // grant other
    char obj_policy_file1[128] = "./gmpolicy_file/ObjVertexNoDelete.gmpolicy";
    PolicyGmruleImport(obj_policy_file1, expectValue1);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // insert
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // before grant
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcGetLastError(VERTEX_PRIV_ERROR);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(count, totalNum);
    AW_MACRO_ASSERT_EQ_INT(0, successNum);

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, count);

    // grant delete
    char obj_policy_file2[128] = "./gmpolicy_file/ObjVertexDelete.gmpolicy";
    PolicyGmruleImport(obj_policy_file2, expectValue1);

    // delete vertex
    ret = GmcBatchReset(batch);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(count, totalNum);
    AW_MACRO_ASSERT_EQ_INT(count, successNum);

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, 0);
    GmcBatchDestroy(batch);

    PolicyGmruleRevoke(obj_policy_file1, expectValue1);
    PolicyGmruleRevoke(obj_policy_file2, expectValue1);

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 016.创建多个vertexLabel，check只有授权对象权限的veterxlabel的DML操作成功
TEST_F(TcpObjPrivsVertexFun, Other_093_102)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // grant T39_all_type all obj privs, T39_1 no obj privs
    char obj_policy_file1[128] = "./gmpolicy_file/ObjVertexAll.gmpolicy";
    PolicyGmruleImport(obj_policy_file1, expectValue1);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // check T39_all_type obj privs
    // insert
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // merge
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int NewVal = 2;
    expectAffectRows = 2;
    for (int i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "T39_K0");
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // replace
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    NewVal = 3;
    expectAffectRows = 2;
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, NewVal);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // update
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    NewVal = 4;
    expectAffectRows = 1;
    for (int i = 0; i < count; i++) {
        uint32_t pk = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // delete
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        uint32_t pk = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // scan
    scan_verex(g_stmt, g_normal_vertexlabel_name, 0);

    // check T39_1 obj privs
    void *vertexLabel1 = NULL;
    const char *labelName = "T39_1";
    char *labelJson = NULL;
    readJanssonFile("schema/VertexLabel1.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    ret = GmcCreateVertexLabel(g_stmt, labelJson, config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(labelJson);
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // insert
    expectAffectRows = 0;
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
        AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // merge
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_MERGE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    NewVal = 2;
    for (int i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "T39_K0");
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
        AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
    }

    // replace
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    NewVal = 3;
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, NewVal);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
        AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // update
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    NewVal = 4;
    for (int i = 0; i < count; i++) {
        uint32_t pk = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
        AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
    }

    // delete
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        uint32_t pk = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
        AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // scan
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");

    PolicyGmruleRevoke(obj_policy_file1, expectValue1);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 017.创建多张表，分多次导入逐个赋予用户这几张表的对象权限，check权限
TEST_F(TcpObjPrivsVertexFun, Other_093_103)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // grant T39_all_type all obj privs
    char obj_policy_file1[128] = "./gmpolicy_file/ObjVertexAll.gmpolicy";
    PolicyGmruleImport(obj_policy_file1, expectValue1);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // check T39_all_type obj privs
    // insert
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // merge
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int NewVal = 2;
    expectAffectRows = 2;
    for (int i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "T39_K0");
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, NewVal);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // replace
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    NewVal = 3;
    expectAffectRows = 2;
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, NewVal);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // update
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    NewVal = 4;
    expectAffectRows = 1;
    for (int i = 0; i < count; i++) {
        uint32_t pk = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty(g_stmt, NewVal);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // delete
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        uint32_t pk = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // scan
    scan_verex(g_stmt, g_normal_vertexlabel_name, 0);

    // check T39_1 obj privs
    void *vertexLabel1 = NULL;
    const char *labelName = "T39_1";
    char *labelJson = NULL;
    readJanssonFile("schema/VertexLabel1.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    ret = GmcCreateVertexLabel(g_stmt, labelJson, config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    // grant T39_1 all obj privs
    char obj_policy_file2[128] = "./gmpolicy_file/ObjVertexT39All.gmpolicy";
    PolicyGmruleImport(obj_policy_file2, expectValue1);

    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // insert
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // merge
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_MERGE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    NewVal = 2;
    expectAffectRows = 2;
    for (int i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "T39_K0");
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, NewVal);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // replace
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    NewVal = 3;
    expectAffectRows = 2;
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, NewVal);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // update
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    NewVal = 4;
    expectAffectRows = 1;
    for (int i = 0; i < count; i++) {
        uint32_t pk = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty(g_stmt, NewVal);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // delete
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        uint32_t pk = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // scan
    scan_verex(g_stmt, labelName, 0);

    PolicyGmruleRevoke(obj_policy_file1, expectValue1);
    PolicyGmruleRevoke(obj_policy_file2, expectValue1);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 018.Vertex对象权限replace校验，同步replace，权限导入前返回错误码，导入后操作成功
TEST_F(TcpObjPrivsVertexFun, Other_093_104)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // grant other
    char obj_policy_file1[128] = "./gmpolicy_file/ObjVertexNoReplace.gmpolicy";
    PolicyGmruleImport(obj_policy_file1, expectValue1);

    // before grant
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    expectAffectRows = 0;
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
        AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
        ret = testGmcGetLastError(VERTEX_PRIV_ERROR);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, 0);

    // grant replace
    char obj_policy_file2[128] = "./gmpolicy_file/ObjVertexReplace.gmpolicy";
    PolicyGmruleImport(obj_policy_file2, expectValue1);

    // replace vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    expectAffectRows = 1;
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, count);

    PolicyGmruleRevoke(obj_policy_file1, expectValue1);
    PolicyGmruleRevoke(obj_policy_file2, expectValue1);

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 019. Vertex对象权限replace校验，异步replace，权限导入前返回错误码，导入后操作成功
TEST_F(TcpObjPrivsVertexFun, Other_093_105)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    void *vertexLabel1 = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // grant other
    char obj_policy_file1[128] = "./gmpolicy_file/ObjVertexNoReplace.gmpolicy";
    PolicyGmruleImport(obj_policy_file1, expectValue1);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    // before grant
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    expectAffectRows = 0;
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt_async, i);
        set_VertexProperty_F9(g_stmt_async, i);
        set_VertexProperty(g_stmt_async, i);
        data = {0};
        data.lastError = VERTEX_PRIV_ERROR;
        GmcAsyncRequestDoneContextT replaceRequestCtx;
        replaceRequestCtx.replaceCb = replace_vertex_callback;
        replaceRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &replaceRequestCtx);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, data.status);
        AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
        AW_MACRO_ASSERT_EQ_INT(expectAffectRows, data.affectRows);
    }

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, 0);

    // grant replace
    char obj_policy_file2[128] = "./gmpolicy_file/ObjVertexReplace.gmpolicy";
    PolicyGmruleImport(obj_policy_file2, expectValue1);

    // replace vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    expectAffectRows = 1;
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt_async, i);
        set_VertexProperty_F9(g_stmt_async, i);
        set_VertexProperty(g_stmt_async, i);
        data = {0};
        GmcAsyncRequestDoneContextT replaceRequestCtx;
        replaceRequestCtx.replaceCb = replace_vertex_callback;
        replaceRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &replaceRequestCtx);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_ASSERT_EQ_INT(expectAffectRows, data.affectRows);
    }
    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, count);

    PolicyGmruleRevoke(obj_policy_file1, expectValue1);
    PolicyGmruleRevoke(obj_policy_file2, expectValue1);

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 020. Vertex对象权限replace校验，批量replace，权限导入前返回错误码，导入后操作成功
TEST_F(TcpObjPrivsVertexFun, Other_093_106)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int count = 100;
    int expectAffectRows = 1;
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // grant other
    char obj_policy_file1[128] = "./gmpolicy_file/ObjVertexNoReplace.gmpolicy";
    PolicyGmruleImport(obj_policy_file1, expectValue1);

    // before grant
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcGetLastError(VERTEX_PRIV_ERROR);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(count, totalNum);
    AW_MACRO_ASSERT_EQ_INT(0, successNum);

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, 0);

    // grant replace
    char obj_policy_file2[128] = "./gmpolicy_file/ObjVertexReplace.gmpolicy";
    PolicyGmruleImport(obj_policy_file2, expectValue1);

    // replace
    ret = GmcBatchReset(batch);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(count, totalNum);
    AW_MACRO_ASSERT_EQ_INT(count, successNum);

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, count);
    GmcBatchDestroy(batch);

    PolicyGmruleRevoke(obj_policy_file1, expectValue1);
    PolicyGmruleRevoke(obj_policy_file2, expectValue1);

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 021.Vertex对象权限merge校验，同步merge，权限导入前返回错误码，导入后操作成功
TEST_F(TcpObjPrivsVertexFun, Other_093_107)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // grant other
    char obj_policy_file1[128] = "./gmpolicy_file/ObjVertexNoMerge.gmpolicy";
    PolicyGmruleImport(obj_policy_file1, expectValue1);

    // before grant
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    expectAffectRows = 0;
    for (int i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "T39_K0");
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
        AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
        ret = testGmcGetLastError(VERTEX_PRIV_ERROR);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, 0);

    // grant merge
    char obj_policy_file2[128] = "./gmpolicy_file/ObjVertexMerge.gmpolicy";
    PolicyGmruleImport(obj_policy_file2, expectValue1);

    // merge vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    expectAffectRows = 1;
    for (int i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "T39_K0");
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, count);

    PolicyGmruleRevoke(obj_policy_file1, expectValue1);
    PolicyGmruleRevoke(obj_policy_file2, expectValue1);

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 022. Vertex对象权限merge校验，异步merge，权限导入前返回错误码，导入后操作成功
TEST_F(TcpObjPrivsVertexFun, Other_093_108)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    void *vertexLabel1 = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // grant other
    char obj_policy_file1[128] = "./gmpolicy_file/ObjVertexNoMerge.gmpolicy";
    PolicyGmruleImport(obj_policy_file1, expectValue1);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    // before grant
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    expectAffectRows = 0;
    for (int i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_async, "T39_K0");
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty_F9(g_stmt_async, i);
        set_VertexProperty(g_stmt_async, i);
        data = {0};
        data.lastError = VERTEX_PRIV_ERROR;
        GmcAsyncRequestDoneContextT mergeRequestCtx;
        mergeRequestCtx.mergeCb = merge_vertex_callback;
        mergeRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &mergeRequestCtx);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, data.status);
        AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
    }

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, 0);

    // grant merge
    char obj_policy_file2[128] = "./gmpolicy_file/ObjVertexMerge.gmpolicy";
    PolicyGmruleImport(obj_policy_file2, expectValue1);

    // merge vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    expectAffectRows = 1;
    for (int i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_async, "T39_K0");
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty_F9(g_stmt_async, i);
        set_VertexProperty(g_stmt_async, i);
        data = {0};
        GmcAsyncRequestDoneContextT mergeRequestCtx;
        mergeRequestCtx.mergeCb = merge_vertex_callback;
        mergeRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &mergeRequestCtx);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (data.status != GMERR_INSUFFICIENT_PRIVILEGE) {
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
        }
        AW_ADD_ERR_WHITE_LIST(1, "GMERR-1005000");
    }

    PolicyGmruleRevoke(obj_policy_file1, expectValue1);
    PolicyGmruleRevoke(obj_policy_file2, expectValue1);

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 023. Vertex对象权限merge校验，批量merge，权限导入前返回错误码，导入后操作成功
TEST_F(TcpObjPrivsVertexFun, Other_093_109)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int count = 100;
    int expectAffectRows = 1;
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // grant other
    char obj_policy_file1[128] = "./gmpolicy_file/ObjVertexNoMerge.gmpolicy";
    PolicyGmruleImport(obj_policy_file1, expectValue1);

    // before grant
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "T39_K0");
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcGetLastError(VERTEX_PRIV_ERROR);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(count, totalNum);
    AW_MACRO_ASSERT_EQ_INT(0, successNum);

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, 0);

    // grant merge
    char obj_policy_file2[128] = "./gmpolicy_file/ObjVertexMerge.gmpolicy";
    PolicyGmruleImport(obj_policy_file2, expectValue1);

    // merge
    ret = GmcBatchReset(batch);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "T39_K0");
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(count, totalNum);
    AW_MACRO_ASSERT_EQ_INT(count, successNum);

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, count);
    GmcBatchDestroy(batch);

    PolicyGmruleRevoke(obj_policy_file1, expectValue1);
    PolicyGmruleRevoke(obj_policy_file2, expectValue1);

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 024. Vertex对象权限select校验，全表扫描，权限导入前返回错误码，导入后操作成功
TEST_F(TcpObjPrivsVertexFun, Other_093_110)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // grant other
    char obj_policy_file1[128] = "./gmpolicy_file/ObjVertexNoSlect.gmpolicy";
    PolicyGmruleImport(obj_policy_file1, expectValue1);

    // replace
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // before grant
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
    ret = testGmcGetLastError(
        "Have no privilege. Insufficient privilege, labelName: T39_all_type, user: (TcpConnUser :  : ).");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // grant select
    char obj_policy_file2[128] = "./gmpolicy_file/ObjVertexSlect.gmpolicy";
    PolicyGmruleImport(obj_policy_file2, expectValue1);

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, count);

    PolicyGmruleRevoke(obj_policy_file1, expectValue1);
    PolicyGmruleRevoke(obj_policy_file2, expectValue1);

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 025. Vertex对象权限select校验，条件扫描，权限导入前返回错误码，导入后操作成功
TEST_F(TcpObjPrivsVertexFun, Other_093_111)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // grant other
    char obj_policy_file1[128] = "./gmpolicy_file/ObjVertexNoSlect.gmpolicy";
    PolicyGmruleImport(obj_policy_file1, expectValue1);
    // replace
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // before grant
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *condStr = "F7<100";
    ret = GmcSetFilter(g_stmt, condStr);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetOutputFormat(g_stmt, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");

    // grant select
    char obj_policy_file2[128] = "./gmpolicy_file/ObjVertexSlect.gmpolicy";
    PolicyGmruleImport(obj_policy_file2, expectValue1);

    // scan vertex
    scan_verex(g_stmt, g_normal_vertexlabel_name, count);

    PolicyGmruleRevoke(obj_policy_file1, expectValue1);
    PolicyGmruleRevoke(obj_policy_file2, expectValue1);

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 026. Vertex对象权限select校验，主键读，权限导入前返回错误码
TEST_F(TcpObjPrivsVertexFun, Other_093_112)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // grant other
    char obj_policy_file1[128] = "./gmpolicy_file/ObjVertexNoSlect.gmpolicy";
    PolicyGmruleImport(obj_policy_file1, expectValue1);

    // replace
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // before grant
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < count; i++) {
        uint32_t pk = i;
        int val = i;
        int64_t sk = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
        AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
    }

    PolicyGmruleRevoke(obj_policy_file1, expectValue1);

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 027.Vertex对象权限select校验，获取变更记录数，权限导入前返回错误码，导入后操作成功
TEST_F(TcpObjPrivsVertexFun, Other_093_113)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // grant other
    char obj_policy_file1[128] = "./gmpolicy_file/ObjVertexNoSlect.gmpolicy";
    PolicyGmruleImport(obj_policy_file1, expectValue1);

    // replace
    for (int i = 0; i < count; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // before grant
    unsigned int CountNum = 1;
    uint64_t opCount[CountNum];
    const char *batch[1] = {};
    batch[0] = g_normal_vertexlabel_name;
    ret = GmcGetOperStatsCnt(g_stmt, batch, GMC_STATISTICS_TYPE_REPLACE, opCount, CountNum);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);  // 适配DTS2021122808195 CATA_VERTEX_LABEL_CHECK_INFO字段名称修改

    // grant select
    char obj_policy_file2[128] = "./gmpolicy_file/ObjVertexSlect.gmpolicy";
    PolicyGmruleImport(obj_policy_file2, expectValue1);

    // GmcGetOperStatsCnt
    ret = GmcGetOperStatsCnt(g_stmt, batch, GMC_STATISTICS_TYPE_REPLACE, opCount, CountNum);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(count, opCount[0]);  // enableDmlOperStat参数默认开启
    memset(opCount, 0, sizeof(uint64_t));

    PolicyGmruleRevoke(obj_policy_file1, expectValue1);
    PolicyGmruleRevoke(obj_policy_file2, expectValue1);

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}


// 029. Vertex对象权限select校验，获取记录数，权限导入前返回错误码，导入后操作成功
TEST_F(TcpObjPrivsVertexFun, Other_093_115)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // grant other
    char obj_policy_file1[128] = "./gmpolicy_file/ObjVertexNoSlect.gmpolicy";
    PolicyGmruleImport(obj_policy_file1, expectValue1);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // before grant
    uint64_t result;
    ret = GmcGetVertexCount(g_stmt, g_normal_vertexlabel_name, NULL, &result);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
    ret = testGmcGetLastError(VERTEX_PRIV_ERROR);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // grant select
    char obj_policy_file2[128] = "./gmpolicy_file/ObjVertexSlect.gmpolicy";
    PolicyGmruleImport(obj_policy_file2, expectValue1);

    // get record count
    ret = GmcGetVertexCount(g_stmt, g_normal_vertexlabel_name, NULL, &result);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(count, result);

    PolicyGmruleRevoke(obj_policy_file1, expectValue1);
    PolicyGmruleRevoke(obj_policy_file2, expectValue1);

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 030. Vertex对象权限混合校验，授权所有对象权限之后进行相关操作
TEST_F(TcpObjPrivsVertexFun, Other_093_116)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // grant T39_all_type all obj privs
    char obj_policy_file1[128] = "./gmpolicy_file/ObjVertexAll.gmpolicy";
    PolicyGmruleImport(obj_policy_file1, expectValue1);

    // insert
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // merge
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int NewVal = 2;
    expectAffectRows = 2;
    for (int i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "T39_K0");
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, NewVal);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // replace
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    NewVal = 3;
    expectAffectRows = 2;
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, NewVal);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // update
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    NewVal = 4;
    expectAffectRows = 1;
    for (int i = 0; i < count; i++) {
        uint32_t pk = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty(g_stmt, NewVal);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // delete
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        uint32_t pk = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // scan
    scan_verex(g_stmt, g_normal_vertexlabel_name, 0);

    PolicyGmruleRevoke(obj_policy_file1, expectValue1);

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 031. Vertex对象权限select校验，主键读，权限导入后操作OK
TEST_F(TcpObjPrivsVertexFun, Other_093_117)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // grant other
    char obj_policy_file1[128] = "./gmpolicy_file/ObjVertexNoSlect.gmpolicy";
    PolicyGmruleImport(obj_policy_file1, expectValue1);

    // grant select
    char obj_policy_file2[128] = "./gmpolicy_file/ObjVertexSlect.gmpolicy";
    PolicyGmruleImport(obj_policy_file2, expectValue1);

    // 授权后再开表，否则用的是授权前缓存的对象权限
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // replace
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 直连读
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < count; i++) {
        uint32_t pk = i;
        int val = i;
        int64_t sk = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        query_VertexProperty(g_stmt, val);
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &sk);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    PolicyGmruleRevoke(obj_policy_file1, expectValue1);
    PolicyGmruleRevoke(obj_policy_file2, expectValue1);

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 016.创建多个vertexLabel，check只有授权对象权限的veterxlabel的DML操作成功
TEST_F(TcpObjPrivsVertexFun, Other_093_121)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // grant T39_all_type all obj privs, T39_1 no obj privs
    char obj_policy_file1[128] = "./gmpolicy_file/ObjVertexAll.gmpolicy";
    PolicyGmruleImport(obj_policy_file1, expectValue1);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // check T39_all_type obj privs
    // insert
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // merge
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int NewVal = 2;
    expectAffectRows = 2;
    for (int i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "T39_K0");
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // replace
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    NewVal = 3;
    expectAffectRows = 2;
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, NewVal);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // update
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    NewVal = 4;
    expectAffectRows = 1;
    for (int i = 0; i < count; i++) {
        uint32_t pk = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // delete
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        uint32_t pk = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // scan
    scan_verex(g_stmt, g_normal_vertexlabel_name, 0);

    // check T39_1 obj privs
    void *vertexLabel1 = NULL;
    const char *labelName = "T39_1";
    char *labelJson = NULL;
    readJanssonFile("schema/VertexLabel1.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    ret = GmcCreateVertexLabel(g_stmt, labelJson, config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(labelJson);
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // insert
    expectAffectRows = 0;
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
        AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // merge
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_MERGE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    NewVal = 2;
    for (int i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "T39_K0");
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
        AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
    }

    // replace
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    NewVal = 3;
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, NewVal);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
        AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // update
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    NewVal = 4;
    for (int i = 0; i < count; i++) {
        uint32_t pk = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
        AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
    }

    // delete
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < count; i++) {
        uint32_t pk = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
        AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // scan
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");

    PolicyGmruleRevoke(obj_policy_file1, expectValue1);
    // insert
    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
        AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // merge
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    NewVal = 2;
    expectAffectRows = 2;
    for (int i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "T39_K0");
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
        AW_ADD_ERR_WHITE_LIST(1, "GMERR-1018004");
    }

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
