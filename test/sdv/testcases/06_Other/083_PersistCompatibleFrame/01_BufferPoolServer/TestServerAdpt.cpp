#include <iostream>
#include <gm_adpt.h>
#include "gms.h"
#include "t_rd_common.h"

int main(int argc, char *argv[])
{
    // 判断是否包含 -v 或 -h 参数
    int skipLoop = 0;
    for (int i = 1; i < argc; ++i) {
        if (strcmp(argv[i], "-v") == 0 || strcmp(argv[i], "-h") == 0) {
            skipLoop = 1;
            break;
        }
    }
    // Call GmsRegAdaptFuncsProc
    int32_t expectRetParam = 0;
    TestBufferpoolRegAdaptFuncsProc(expectRetParam);

    int ret = GmsServerMain(argc, argv);
    if (ret != 0) {
        printf("[tcpStartServer] GmsServerMain failed, ret = %d.\n", ret);
        return ret;
    }

    // 如果没有 -v 或 -h 参数，才进入 while 循环
    if (!skipLoop) {
        while (1) {
            sleep(1);
        }
    }

    return 0;
}
