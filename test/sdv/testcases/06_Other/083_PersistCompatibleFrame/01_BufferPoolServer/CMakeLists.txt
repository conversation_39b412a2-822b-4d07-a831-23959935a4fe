cmake_minimum_required(VERSION 3.14.1)

project(testcase)
set(EXECUTABLE_OUTPUT_PATH ${CMAKE_CURRENT_SOURCE_DIR})
link_directories(${CMAKE_CURRENT_SOURCE_DIR}/exec/third_party/lib)
link_libraries(z)

file(GLOB fileList RELATIVE "${CMAKE_CURRENT_SOURCE_DIR}" "*.cpp")
foreach(sinFile IN LISTS fileList)
    get_filename_component(mainName ${sinFile} NAME_WE)
    set(executableName ${mainName})
    if(${mainName} STREQUAL "TestServerAdpt")
        set(executableName gmserver)
    else()
        set(executableName ${mainName})
    endif()
    add_executable(${executableName} ${sinFile})
    
endforeach()

