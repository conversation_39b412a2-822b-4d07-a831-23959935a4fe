#!/bin/bash
# for multi-sub

CUR_DIR=`pwd`


if [ $# -lt 1 ];then
    echo "usage:$0 sh [create sub nums]"
    exit  1
fi

## 数据清除及准备  $CUR_DIR/multi-sub文件夹
cd $CUR_DIR
rm -r multi_sub > /dev/null 2>&1
mkdir multi_sub > /dev/null 2>&1
cp $TEST_HOME/testcases/06_Other/035_memoryRecycle/schemaFile/all_type_schema_subinfo.gmjson ./multi_sub/all_type_schema_subinfo.gmjson
sleep 1

# 构造多个 subinfo
cd $CUR_DIR/multi_sub
#echo $1 $2

for i in $(seq 1 $1) 
do	
	cp all_type_schema_subinfo.gmjson all_type_schema_subinfo_$i.gmjson
	sed -i "s/\"name\":\"subVertexLabel\"/\"name\": \"subVertexLabel_"$i"\"/g" ./all_type_schema_subinfo_$i.gmjson
    sed -i "s/\"label_name\":\"label\"/\"label_name\": \"label"$i"\"/g" ./all_type_schema_subinfo_$i.gmjson
        
done
