[{"type": "container", "name": "ContainerOne", "alias": "alias_Container<PERSON>ne", "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true, "default": 555}, {"name": "F6", "type": "uint32", "nullable": true, "default": 666}, {"name": "F7", "type": "uint32", "nullable": true, "default": 777}, {"name": "F8", "type": "uint32", "nullable": true, "default": 888}, {"name": "F9", "type": "uint32", "nullable": true, "default": 999}, {"name": "F10", "type": "string", "size": 10, "nullable": true}, {"name": "F12", "type": "uint16", "nullable": true}, {"name": "F13", "type": "uint32", "nullable": true}, {"name": "F14", "type": "int64", "nullable": true}, {"name": "F15", "type": "uint64", "nullable": true}, {"name": "F16", "type": "time", "nullable": true}, {"name": "F17", "type": "uint8: 4", "nullable": true}, {"name": "F18", "type": "uint16: 15", "nullable": true}, {"name": "F19", "type": "uint32: 31", "nullable": true}, {"name": "F20", "type": "bytes", "size": 7, "nullable": true}, {"name": "F21", "type": "fixed", "size": 7, "nullable": true}, {"name": "F22", "type": "bitmap", "size": 128, "nullable": true}, {"name": "F23", "type": "uint64: 59", "nullable": true}, {"name": "F24", "type": "int32", "nullable": true}, {"type": "container", "name": "ContainerTwo", "presence": true, "fields": [{"name": "F0", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/alias_ContainerOne/alias_ListOne[F0=$TARGET_LIST-RECORD]/F1 = current()"}]}, {"name": "F1", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "not(/ContainerOne/alias_ListOne[F0=$TARGET_LIST-RECORD]/F1 = 100)"}]}, {"name": "F2", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "count(/alias_ContainerOne/alias_ListOne) != 1"}]}, {"name": "F3", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/alias_ContainerOne/alias_ListOne[F0=$TARGET_LIST-RECORD]/F4 != true()"}]}, {"name": "F4", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/alias_ContainerOne/alias_ListOne[F0=$TARGET_LIST-RECORD]/F4 != false()"}]}, {"name": "F5", "type": "uint32", "nullable": true, "default": 555, "clause": [{"type": "when", "formula": "string(/alias_ContainerOne/alias_ListOne[F0=$TARGET_LIST-RECORD]/F1) = '120'"}]}, {"name": "F6", "type": "uint32", "nullable": true, "default": 666, "clause": [{"type": "when", "formula": "number(/alias_ContainerOne/alias_ListOne[F0=$TARGET_LIST-RECORD]/F6) = 1"}]}, {"name": "F7", "type": "uint32", "nullable": true, "default": 777, "clause": [{"type": "when", "formula": "translate(/alias_ContainerOne/alias_ListOne[F0=$TARGET_LIST-RECORD]/F6, 'a', 'b') = 'bbdbdd'"}]}, {"name": "F8", "type": "uint32", "nullable": true, "default": 888, "clause": [{"type": "when", "formula": "starts-with('aaa', 'a')"}]}, {"name": "F9", "type": "uint32", "nullable": true, "default": 999, "clause": [{"type": "when", "formula": "/alias_ContainerOne/F0 + /alias_ContainerOne/F1 > 100"}, {"type": "when", "formula": "/alias_ContainerOne/F0 - /alias_ContainerOne/F1 < 100"}, {"type": "when", "formula": "/alias_ContainerOne/F0 * /alias_ContainerOne/F1 > 100"}, {"type": "when", "formula": "/alias_ContainerOne/F0 div 100 = 1"}, {"type": "when", "formula": "/alias_ContainerOne/F0 mod 100 = 0"}]}, {"name": "F10", "type": "string", "size": 10, "nullable": true, "clause": [{"type": "when", "formula": "/alias_ContainerOne/F0 = 100 and /alias_ContainerOne/F1 <= 100"}, {"type": "when", "formula": "/alias_ContainerOne/F2 > 0 or /alias_ContainerOne/F3 < 0"}]}, {"name": "F11", "type": "string", "size": 10, "nullable": true, "default": "default11", "clause": [{"type": "when", "formula": "/alias_ContainerOne/F0 >= 100 and /alias_ContainerOne/F1 <= 100"}]}, {"name": "F12", "type": "string", "size": 10, "nullable": true, "default": "default12"}, {"name": "F13", "type": "string", "size": 10, "nullable": true, "default": "default13"}]}, {"type": "container", "name": "Container<PERSON>hree", "presence": false, "fields": [{"name": "F0", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/alias_ContainerOne/ListOne[F0=$TARGET_LIST-RECORD]/F1 != current()"}]}, {"name": "F1", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "not(/alias_ContainerOne/alias_ListOne[F0=$TARGET_LIST-RECORD]/F1 != 100)"}]}, {"name": "F2", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "count(/alias_ContainerOne/alias_ListOne) = 2"}]}, {"name": "F3", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/alias_ContainerOne/alias_ListOne[F0=$TARGET_LIST-RECORD]/F4 = true()"}]}, {"name": "F4", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/alias_ContainerOne/alias_ListOne[F0=$TARGET_LIST-RECORD]/F4 = false()"}]}, {"name": "F5", "type": "uint32", "nullable": true, "default": 555, "clause": [{"type": "when", "formula": "string(/alias_ContainerOne/alias_ListOne[F0=$TARGET_LIST-RECORD]/F1) != '120'"}]}, {"name": "F6", "type": "uint32", "nullable": true, "default": 666, "clause": [{"type": "when", "formula": "number(/alias_ContainerOne/alias_ListOne[F0=$TARGET_LIST-RECORD]/F6) != 1"}]}, {"name": "F7", "type": "uint32", "nullable": true, "default": 777, "clause": [{"type": "when", "formula": "translate(/alias_ContainerOne/alias_ListOne[F0=$TARGET_LIST-RECORD]/F6, 'a', 'b') = 'bbbddd1'"}]}, {"name": "F8", "type": "uint32", "nullable": true, "default": 888, "clause": [{"type": "when", "formula": "starts-with('aaa', 'b')"}]}, {"name": "F9", "type": "uint32", "nullable": true, "default": 999, "clause": [{"type": "when", "formula": "/alias_ContainerOne/F0 + /alias_ContainerOne/F1 < 100"}, {"type": "when", "formula": "/alias_ContainerOne/F0 - /alias_ContainerOne/F1 > 100"}, {"type": "when", "formula": "/alias_ContainerOne/F0 * /alias_ContainerOne/F1 < 100"}, {"type": "when", "formula": "/alias_ContainerOne/F0 div 100 != 1"}, {"type": "when", "formula": "/alias_ContainerOne/F0 mod 100 != 0"}]}, {"name": "F10", "type": "string", "size": 10, "nullable": true, "clause": [{"type": "when", "formula": "/alias_ContainerOne/F0 != 100 and /alias_ContainerOne/F1 > 100"}, {"type": "when", "formula": "/alias_ContainerOne/F2 < 0 or /alias_ContainerOne/F3 < 0"}]}, {"name": "F11", "type": "string", "size": 10, "nullable": true, "default": "default11", "clause": [{"type": "when", "formula": "/alias_ContainerOne/F0 >= 200 and /alias_ContainerOne/F1 <= 200"}]}, {"name": "F12", "type": "string", "size": 10, "nullable": true, "default": "default12"}, {"name": "F13", "type": "string", "size": 10, "nullable": true, "default": "default13"}, {"type": "container", "name": "ContainerFour", "fields": [{"name": "F0", "type": "uint32"}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}]}, {"type": "choice", "name": "Choice", "fields": [{"type": "case", "name": "CaseOne", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}, {"type": "container", "name": "CaseContainerOne", "presence": true, "fields": [{"name": "F0", "type": "uint32"}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}, {"type": "container", "name": "CaseContainerTwo", "presence": false, "fields": [{"name": "F0", "type": "uint32"}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}]}]}], "keys": [{"node": "ContainerOne", "name": "PK", "fields": ["ID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "ListOne", "alias": "alias_<PERSON><PERSON>ne", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean", "default": true}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string", "default": "1"}, {"name": "F7", "type": "int32"}, {"type": "container", "name": "ListContainerOne", "presence": true, "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}, {"type": "container", "name": "ListContainerthree", "presence": false, "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}]}, {"type": "choice", "name": "Listchoice", "fields": [{"type": "case", "name": "ListchoiceCase", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}]}, {"type": "container", "name": "ListContainerTwo", "presence": false, "fields": [{"name": "F0", "type": "uint32"}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}], "keys": [{"fields": ["PID", "F0"], "node": "ListOne", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "ListOne", "name": "<PERSON><PERSON><PERSON>", "fields": ["PID", "F1"], "index": {"type": "list_localhash"}, "constraints": {"unique": true, "null_check": true}}]}, {"type": "leaf-list", "name": "LeafList", "alias": "alias_LeafList", "clause": [{"type": "when", "formula": "/ContainerOne/F0 = 50"}], "min-elements": 0, "max-elements": 10000, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false, "default": [2, 3]}], "keys": [{"fields": ["PID", "F0"], "node": "LeafList", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "ListTwo", "alias": "alias_ListTwo", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}], "keys": [{"fields": ["PID", "F0"], "node": "ListTwo", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "TABLE_YANG_REMOVED_DEFAULT_RECORD", "type": "list", "np_access": true, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "labelId", "type": "uint32", "nullable": false}, {"name": "nodeId", "type": "uint16", "nullable": false}, {"name": "propeId", "type": "uint32", "nullable": false}], "keys": [{"node": "TABLE_YANG_REMOVED_DEFAULT_RECORD", "name": "k0", "fields": [":id", ":pid", "labelId", "nodeId", "propeId"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]