/*****************************************************************************
 Description  : Test Error code.
 Author       : liuli lwx1035319
 Modification : [2021.06].
*****************************************************************************/
extern "C" {
}

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <dlfcn.h>
#include <time.h>
#include "gtest/gtest.h"
#include "normative_error_code.h"
#include "t_datacom_lite.h"

int ret;
GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];

using namespace std;

class NormativeErrorCode : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh ");
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcConnect(&g_conn, &g_stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        GmcDropVertexLabel(g_stmt, "TestResourceLable");
        GmcDropVertexLabel(g_stmt, "test_data_type");
        ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcDisconnect(g_conn, g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        g_conn = NULL;
        g_stmt = NULL;
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void NormativeErrorCode::SetUp()
{
    AW_CHECK_LOG_BEGIN(1);
}
void NormativeErrorCode::TearDown()
{
    AW_CHECK_LOG_END();
}

void TestSetPropertyDataTypeFileds(GmcStmtT *stmt, int i, bool bool_value)
{
    int ret = 0;

    uint64_t f1_value = i;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    int32_t f2_value = 2 * i;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t f3_value = 3 * i;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    int16_t f4_value = 4 * i;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint16_t f5_value = 5 * i;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    int8_t f6_value = 6 * i;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t f7_value = 7 * i;
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);

    float f9_value = 9 * i;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);

    double f10_value = 10 * i;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    char f12_value = 12 * i;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);

    unsigned char f13_value = 13 * i;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);

    char f14_value[10];
    snprintf(f14_value, 10, "%d", i);
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);
}

void TestWriteFiledProperty(GmcStmtT *stmt, int index)
{
    int ret = 0;
    uint32_t f0_value = 1 * index;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &f0_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
}

void TestSetVertexFiledsPropertyByName(GmcNodeT *node, int i, bool bool_value, char *string_value)
{
    int ret = 0;

    int64_t f0_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f1_value = i + 1;
    ret = GmcNodeSetPropertyByName(node, (char *)"F1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    int32_t f2_value = 2 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t f3_value = 3 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    int16_t f4_value = (4 + i) % 32768;
    ret = GmcNodeSetPropertyByName(node, (char *)"F4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint16_t f5_value = (5 + i) % 65536;
    ret = GmcNodeSetPropertyByName(node, (char *)"F5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    int8_t f6_value = (6 + i) % 128;
    ret = GmcNodeSetPropertyByName(node, (char *)"F6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t f7_value = (7 + i) % 256;
    ret = GmcNodeSetPropertyByName(node, (char *)"F7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(node, (char *)"F8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);

    float f9_value = 9.11 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);

    double f10_value = 10.68 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f11_value = 11 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    char f12_value = (i) % 128;
    ret = GmcNodeSetPropertyByName(node, (char *)"F12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);

    unsigned char f13_value = (13 + i) % 256;
    ret = GmcNodeSetPropertyByName(node, (char *)"F13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F14", GMC_DATATYPE_STRING, string_value, (strlen(string_value)));
    ASSERT_EQ(GMERR_OK, ret);
}

void TestGetNodePropertyAndSizeByName(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    bool isNull = 0;

    int64_t f0_value = 0;
    ret = GmcNodeGetPropertyByName(node, (char *)"F0", &f0_value, sizeof(int64_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((unsigned int)0, isNull);
    EXPECT_EQ(f0_value, i);

    uint64_t f1_value = 0;
    ret = GmcNodeGetPropertyByName(node, (char *)"F1", &f1_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(1 + i, f1_value);

    int32_t f2_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F2", &f2_value, sizeof(int32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(2 + i, f2_value);

    uint32_t f3_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F3", &f3_value, sizeof(uint32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(3 + i, f3_value);

    int16_t f4_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F4", &f4_value, sizeof(int16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ((4 + i) % 32768, f4_value);

    uint16_t f5_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F5", &f5_value, sizeof(uint16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ((5 + i) % 65536, f5_value);

    int8_t f6_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F6", &f6_value, sizeof(int8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ((6 + i) % 128, f6_value);

    uint8_t f7_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F7", &f7_value, sizeof(uint8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ((7 + i) % 256, f7_value);

    bool f8_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F8", &f8_value, sizeof(bool), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(bool_value, f8_value);

    float f9_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F9", &f9_value, sizeof(float), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ret = (labs((float)(9.11 + i) - f9_value) < __FLT_EPSILON__) ? 0 : 1;
    ASSERT_EQ(GMERR_OK, ret);

    double f10_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F10", &f10_value, sizeof(double), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ret = (labs((double)(10.68 + i) - f10_value) < __DBL_EPSILON__) ? 0 : 1;
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F11", &f11_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(11 + i, f11_value);

    char f12_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F12", &f12_value, sizeof(char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ((i) % 128, f12_value);

    unsigned char f13_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F13", &f13_value, sizeof(unsigned char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    unsigned char k = (unsigned char)(13 + i) % 256;
    ASSERT_EQ(k, f13_value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"F14", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(propSize, strlen(f14_value) + 1);

    char string_value[100] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F14", &string_value, propSize, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);
}

void TestGmcNodeGetPropertyByNameFileds(GmcNodeT *node, char *protpert_name, void *insert_value, unsigned int length)
{
    int ret = 0;
    bool isNull = 0;
    char *query_value = (char *)malloc(length);
    ASSERT_NE((void *)NULL, query_value);
    memset(query_value, 0, length);

    ret = GmcNodeGetPropertyByName(node, protpert_name, query_value, length, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ret = memcmp((char *)insert_value, query_value, length);
    if (ret != 0) {
        printf(
            "protpert_name:%s insert_value:0x%s query_value:0x%s\n", protpert_name, (char *)insert_value, query_value);
    }
    ASSERT_EQ(GMERR_OK, ret);
    free(query_value);
}

void TestFetchVertexFiledsByName(GmcStmtT *stmt, bool bool_value, int32_t i, char *f14_value, void *fixed_vaule,
    const char *label_name, const char *keyName)
{
    int32_t ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_FIXED, fixed_vaule, DATA_FILEDS_VALUE_LENGTH);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    bool isFinish = true;
    ret = GmcFetch(g_stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(isFinish, false);
    GmcNodeT *root;
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    TestGetNodePropertyAndSizeByName(root, i, bool_value, f14_value);
    TestGmcNodeGetPropertyByNameFileds(root, (char *)"F15", fixed_vaule, DATA_FILEDS_VALUE_LENGTH);
}

//连接不存在
TEST_F(NormativeErrorCode, Other_019_001)
{
    int runMode = -1;
    int syncMode = 0;
    bool needEpoll = 0;

    GmcConnT *conn;
    GmcConnT **connOut = NULL;
    const char *connName = NULL;
    const void *chanRingLen = NULL;
    const void *subMaxSize = NULL;
    GmcEpollRegWithUserDataT epollReg = NULL;
    ConnOptionT *connOptions = NULL;

    int ret = testEnvInit(runMode);
    ASSERT_EQ(GMERR_OK, ret);

    GmcConnOptionsT *connOptionsInnner;
    ret = GmcConnOptionsCreate(&connOptionsInnner);
    ASSERT_EQ(GMERR_OK, ret);

    const char *p1 = g_connServer;
    if (connOptions && connOptions->serverLocator) {
        p1 = connOptions->serverLocator;
    }
    const char *p3 = g_passwd;
    if (connOptions && connOptions->passwd) {
        p3 = connOptions->passwd;
    }

    ret = GmcConnOptionsSetServerLocator(connOptionsInnner, p1);
    if (ret != GMERR_OK) {
        GmcConnOptionsDestroy(connOptionsInnner);
    }
    ASSERT_EQ(GMERR_OK, ret);

    if (connName) {
        ret = GmcConnOptionsSetConnName(connOptionsInnner, connName);
        if (ret != GMERR_OK) {
            GmcConnOptionsDestroy(connOptionsInnner);
        }
        ASSERT_EQ(GMERR_OK, ret);
    }

    if (needEpoll) {
        ret = GmcConnOptionsSetEpollRegFuncWithUserData(connOptionsInnner, epollReg, &g_epollData.userEpollFd);
        ASSERT_EQ(GMERR_OK, ret);
    }

    if (connOptions && connOptions->requestTimeout) {
        ret = GmcConnOptionsSetRequestTimeout(connOptionsInnner, connOptions->requestTimeout);
        if (ret != GMERR_OK) {
            GmcConnOptionsDestroy(connOptionsInnner);
        }
        ASSERT_EQ(GMERR_OK, ret);
    }

    if (connOptions && connOptions->msgReadTimeout) {
        ret = GmcConnOptionsSetMsgReadTimeout(connOptionsInnner, connOptions->msgReadTimeout);
        if (ret != GMERR_OK) {
            GmcConnOptionsDestroy(connOptionsInnner);
        }
        ASSERT_EQ(GMERR_OK, ret);
    }

    if (connOptions && connOptions->msgWriteTimeout) {
        ret = GmcConnOptionsSetMsgWriteTimeout(connOptionsInnner, connOptions->msgWriteTimeout);
        if (ret != GMERR_OK) {
            GmcConnOptionsDestroy(connOptionsInnner);
        }
        ASSERT_EQ(GMERR_OK, ret);
    }
#if 0  // 接口删除
    if (subMaxSize) {
        ret = GmcConnOptionsSetMsgShmMemMaxSize(connOptionsInnner, *(uint32_t *)subMaxSize);
        if (ret != GMERR_OK) {
            GmcConnOptionsDestroy(connOptionsInnner);
        }
        ASSERT_EQ(GMERR_OK, ret);
    }
#endif
    for (int n = 5; n > 0; n--) {
        ret = GmcConnect((GmcConnTypeE)syncMode, connOptionsInnner, &conn);
        if (ret != GMERR_CONNECTION_FAILURE) {
            expect = NULL;
            ret = testGmcGetLastError(expect);
            ASSERT_EQ(GMERR_OK, ret);
            break;
        }
        usleep(300000);
    }

    if (ret != 0) {
        GmcConnOptionsDestroy(connOptionsInnner);
        ASSERT_EQ(GMERR_OK, ret);
    }
    GmcConnOptionsDestroy(connOptionsInnner);

    GmcStmtT *st;
    conn = NULL;
    ret = GmcAllocStmt(conn, &st);
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(conn);
    EXPECT_EQ(GMERR_OK, ret);
}

//客户端无法建立连接
TEST_F(NormativeErrorCode, Other_019_002)
{
    GmcConnT *conn;
    GmcConnT **connOut = NULL;
    const char *connName = NULL;
    const void *chanRingLen = NULL;
    const void *subMaxSize = NULL;
    GmcEpollRegWithUserDataT epollReg = NULL;
    ConnOptionT *connOptions = NULL;

    int runMode = -1;
    int syncMode = 0;
    bool needEpoll = 0;

    int ret = testEnvInit(runMode);
    ASSERT_EQ(GMERR_OK, ret);

    GmcConnOptionsT *connOptionsInnner;
    ret = GmcConnOptionsCreate(&connOptionsInnner);
    ASSERT_EQ(GMERR_OK, ret);

    const char *p1 = g_connServer;
    if (connOptions && connOptions->serverLocator) {
        p1 = connOptions->serverLocator;
    }
    const char *p3 = nullptr;
    if (connOptions && connOptions->passwd) {
        p3 = connOptions->passwd;
    }

    ret = GmcConnOptionsSetServerLocator(connOptionsInnner, p1);
    if (ret != GMERR_OK) {
        GmcConnOptionsDestroy(connOptionsInnner);
    }
    ASSERT_EQ(GMERR_OK, ret);

    if (connName) {
        ret = GmcConnOptionsSetConnName(connOptionsInnner, connName);
        if (ret != GMERR_OK) {
            GmcConnOptionsDestroy(connOptionsInnner);
        }
        ASSERT_EQ(GMERR_OK, ret);
    }

    if (needEpoll) {
        ret = GmcConnOptionsSetEpollRegFuncWithUserData(connOptionsInnner, epollReg, &g_epollData.userEpollFd);
        ASSERT_EQ(GMERR_OK, ret);
    }

    if (connOptions && connOptions->requestTimeout) {
        ret = GmcConnOptionsSetRequestTimeout(connOptionsInnner, connOptions->requestTimeout);
        if (ret != GMERR_OK) {
            GmcConnOptionsDestroy(connOptionsInnner);
        }
        ASSERT_EQ(GMERR_OK, ret);
    }

    if (connOptions && connOptions->msgReadTimeout) {
        ret = GmcConnOptionsSetMsgReadTimeout(connOptionsInnner, connOptions->msgReadTimeout);
        if (ret != GMERR_OK) {
            GmcConnOptionsDestroy(connOptionsInnner);
        }
        ASSERT_EQ(GMERR_OK, ret);
    }

    if (connOptions && connOptions->msgWriteTimeout) {
        ret = GmcConnOptionsSetMsgWriteTimeout(connOptionsInnner, connOptions->msgWriteTimeout);
        if (ret != GMERR_OK) {
            GmcConnOptionsDestroy(connOptionsInnner);
        }
        ASSERT_EQ(GMERR_OK, ret);
    }
#if 0  // 接口删除
    if (subMaxSize) {
        ret = GmcConnOptionsSetMsgShmMemMaxSize(connOptionsInnner, *(uint32_t *)subMaxSize);
        if (ret != GMERR_OK) {
            GmcConnOptionsDestroy(connOptionsInnner);
        }
        ASSERT_EQ(GMERR_OK, ret);
    }
#endif
    for (int n = 5; n > 0; n--) {
        ret = GmcConnect((GmcConnTypeE)syncMode, connOptionsInnner, &conn);
        if (ret != GMERR_CONNECTION_FAILURE) {
            expect = NULL;
            ret = testGmcGetLastError(expect);
            ASSERT_EQ(GMERR_OK, ret);
            break;
        }
        usleep(300000);
    }

    if (ret != 0) {
        GmcConnOptionsDestroy(connOptionsInnner);
        ASSERT_EQ(GMERR_OK, ret);
    }

    GmcStmtT *st;
    conn = NULL;
    ret = GmcAllocStmt(conn, &st);
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(conn);
    EXPECT_EQ(GMERR_OK, ret);
}

//不支持此特性
TEST_F(NormativeErrorCode, Other_019_003)
{
    int ret;

    const char *JsonString =
        R"([{
        "type":"testErrorCode",
        "name":"VertexTestErrorCode",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"bitmap", "size":8, "nullable":false}
            ],
        "keys":
            [
                {
                    "node":"VertexTestErrorCode",
                    "name":"PK",
                    "fields":["F0", "F1"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    ret = GmcCreateVertexLabel(g_stmt, JsonString, g_label_config);
    EXPECT_EQ(GMERR_INVALID_TABLE_DEFINITION, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    EXPECT_EQ(GMERR_OK, ret);

    expect = NULL;
    ret = testGmcGetLastError(expect);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, g_label_name);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    EXPECT_EQ(GMERR_OK, ret);
}

//非法参数值
TEST_F(NormativeErrorCode, Other_019_005)
{
    int ret;
    uint64_t respoolId = 1;
    uint64_t count = 2;
    uint64_t startIndex = 1;
    void *gVertexLabel = NULL;
    const char *gLableConfig = R"({"max_record_num":1000})";
    const char *g_label_name = "ResourceNormativeErrorCode";
    const char *gLabelSchema =
        R"([{
        "type":"record",
        "name":"ResourceNormativeErrorCode",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"int32", "nullable":false},
                {"name":"F3", "type":"resource", "nullable":false}
            ],
        "keys":
            [
                {
                    "node":"ResourceNormativeErrorCode",
                    "name":"T35_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    GmcDestroyResPool(g_stmt, "resource_test_error_code");
    ret = GmcCreateResPool(g_stmt, g_ResPoolTest);
    ASSERT_EQ(GMERR_RESOURCE_POOL_ERROR, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(g_stmt, gLabelSchema, gLableConfig);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcBindResPoolToLabel(g_stmt, g_resource_pool_name, g_label_name);
    ASSERT_EQ(GMERR_RESOURCE_POOL_ERROR, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetCountResource(count, &tmpResIdx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcUnbindResPoolFromLabel(g_stmt, g_label_name);
    ASSERT_EQ(GMERR_RESOURCE_POOL_ERROR, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDestroyResPool(g_stmt, g_resource_pool_name);
    ASSERT_EQ(GMERR_RESOURCE_POOL_ERROR, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, g_label_name);
    ASSERT_EQ(GMERR_OK, ret);
}

//不允许NULL值
TEST_F(NormativeErrorCode, Other_019_006)
{
    int ret;
    char *pJson = NULL;

    const char *ResPoolName = "respool";
    const char *ResPoolTest =
        R"({
        "name" : "respool",
        "pool_id" : 65535,
        "start_id" : 0,
        "capacity" : 400,
        "order" : 0,
        "alloc_type" : 0
    })";

    ret = GmcCreateResPool(g_stmt, ResPoolTest);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcGetResPool(g_stmt, NULL, &pJson);
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDestroyResPool(g_stmt, ResPoolName);
    ASSERT_EQ(GMERR_OK, ret);
}

//非法的Json内容
TEST_F(NormativeErrorCode, Other_019_007)
{
    const char *JsonString =
        R"([{
        "type":"record",
        "name":"VertexTestErrorCode",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "size":8, "nullable":false},
                {"name":"F2", "type":"int32", "nullable":false},
                {"name":"F3", "type":"int32", "nullable":false},
            ],
        "keys":
            [
                {
                    "node":"VertexTestErrorCode",
                    "name":"PK",
                    "fields":["F0", "F1"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    ret = GmcCreateVertexLabel(g_stmt, JsonString, g_label_config);
    EXPECT_EQ(GMERR_INVALID_JSON_CONTENT, ret);  // F2,F3
    expect = NULL;
    ret = testGmcGetLastError(expect);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    EXPECT_EQ(GMERR_OK, ret);

    expect = NULL;
    ret = testGmcGetLastError(expect);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, g_label_name);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    EXPECT_EQ(GMERR_OK, ret);
}

//违反唯一性约束
TEST_F(NormativeErrorCode, Other_019_008)
{
    int ret = 0;

    const char *string_schema =
        "[{\"name\":\"TK08\", \"version\":\"2.0\", \"type\":\"record\","
        "\"fields\":["
        "{ \"name\":\"F0\", \"type\":\"int8\",\"nullable\":false},"
        "{ \"name\":\"F1\", \"type\":\"string\", \"size\":8,\"nullable\":false},"
        "{ \"name\":\"F2\", \"type\":\"int16\" ,\"nullable\":false},"
        "{ \"name\":\"F3\", \"type\":\"fixed\", \"size\":13312,\"nullable\":true},"
        "{ \"type\":\"record\", \"name\":\"T82\" ,\"nullable\": true,\"array\":true, \"size\":900, \"fields\":["
        "{ \"name\":\"F0\", \"type\":\"uint32\",\"nullable\":true},"
        "{ \"name\":\"F1\", \"type\":\"uint32\",\"nullable\":true},"
        "{ \"name\":\"F2\", \"type\":\"uint32\" ,\"nullable\":true},"
        "{ \"name\":\"F3\", \"type\":\"uint32\" ,\"nullable\":true}"
        "]"
        "}"
        "],"
        "\"keys\":["
        "{ \"name\":\"PK\", \"node\":\"TK08\", \"fields\":[\"F0\",\"F1\",\"F2\"],"
        "\"index\": { \"type\":\"primary\" },\"constraints\": {\"unique\": false}},"
        "{ \"name\": \"T82_K1\", \"node\": \"T82\", \"fields\": [\"F3\"],"
        "\"index\": { \"type\": \"none\" },\"constraints\": {\"unique\": true}}"
        "]"
        "}]";

    ret = GmcCreateVertexLabel(g_stmt, string_schema, NULL);
    ASSERT_EQ(GMERR_INVALID_TABLE_DEFINITION, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, "T100");
    ASSERT_EQ(GMERR_UNDEFINED_TABLE, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    EXPECT_EQ(GMERR_OK, ret);
}

//依赖性对象依然存在
TEST_F(NormativeErrorCode, Other_019_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    void *VertexLabel = NULL;
    const char *LableConfig = R"({"max_record_num":1000})";
    const char *ResPoolName = "respool_test";
    const char *ResPoolTest =
        R"({
        "name" : "respool_test",
        "pool_id" : 65535,
        "start_id" : 1,
        "capacity" : 200,
        "order" : 1,
        "alloc_type" : 0
    })";
    const char *LabelName = "ResourceTypeLableName";
    const char *LabelSchema =
        R"([{
        "type":"record",
        "name":"ResourceTypeLableName",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"int32", "nullable":false},
                {"name":"F3", "type":"resource", "nullable":false}
            ],
        "keys":
            [
                {
                    "node":"ResourceTypeLableName",
                    "name":"T35_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    GmcDestroyResPool(g_stmt, "respool_test");
    ret = GmcCreateResPool(g_stmt, ResPoolTest);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(g_stmt, LabelSchema, LableConfig);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcBindResPoolToLabel(g_stmt, ResPoolName, LabelName);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcUnbindResPoolFromLabel(g_stmt, LabelName);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, LabelName);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDestroyResPool(g_stmt, "respool_test");
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//非法属性值
TEST_F(NormativeErrorCode, Other_019_010)
{
    void *vertexLabel = NULL;
    uint32_t value = 100;
    char *teststr = (char *)"testver";

    char const *label_name_field = "T48_fields_test";
    char *this_schema_fileds = NULL;
    readJanssonFile("schema_file/fields_property_schema.gmjson", &this_schema_fileds);
    ASSERT_NE((void *)NULL, this_schema_fileds);

    GmcDropVertexLabel(g_stmt, "T48_fields_test");
    ret = GmcCreateVertexLabel(g_stmt, this_schema_fileds, g_label_config);
    ASSERT_EQ(GMERR_OK, ret);
    free(this_schema_fileds);

    ret = testGmcPrepareStmtByLabelName(g_stmt, label_name_field, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(g_stmt, "F0000", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_INVALID_PROPERTY, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F0.", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_INVALID_PROPERTY, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_INVALID_PROPERTY, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "0", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_INVALID_PROPERTY, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt,
        "F3qwertyuiopasdfghjklzxcvbnmqwertyuiopasdfghjklzxcvbnmqwertyuiopasdfghjklzxcvbnmqwertyuiopasdfghjklzxcvbnmqwer"
        "tyuiopasdfghjklzxcvbnmqwertyuiopasdfghjklzxcvbnm",
        GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_INVALID_PROPERTY, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(g_stmt, "F4qwertyuiopasdfghjklzxcvbnmqwertyuiopasdfghjklzxcvbnmqwertyuio",
        GMC_DATATYPE_STRING, teststr, (strlen(teststr)));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, "T48_fields_test");
    ASSERT_EQ(GMERR_OK, ret);
}

//名字太长
TEST_F(NormativeErrorCode, Other_019_011)
{
    int ret;
    void *VertexLabel = NULL;
    const char *LableConfig = R"({"max_record_num":1000})";
    const char *ResPoolName = "test_respool";
    const char *ResPoolTest =
        R"({
        "name" : "test_respool_string_respool_string_respool_string_respool_string_respool_string_respool_string_respool_string_respool_string_respool_string_respool_string_respool_string_respool_string_respool_string_respool_string_respool_string_respool_string_respool_string_respool_string",
        "pool_id" : 65535,
        "start_id" : 1,
        "capacity" : 8000,
        "order" : 0,
        "alloc_type" : 0
    })";
    const char *LabelName = "ResourceOfLable";
    const char *LabelSchema =
        R"([{
        "type":"record",
        "name":"ResourceOfLable",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"resource", "nullable":false}
            ],
        "keys":
            [
                {
                    "node":"ResourceOfLable",
                    "name":"T96_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    ret = GmcCreateResPool(g_stmt, ResPoolTest);
    ASSERT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(g_stmt, LabelSchema, LableConfig);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcBindResPoolToLabel(g_stmt, ResPoolName, LabelName);
    ASSERT_EQ(GMERR_RESOURCE_POOL_ERROR, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnbindResPoolFromLabel(g_stmt, LabelName);
    ASSERT_EQ(GMERR_RESOURCE_POOL_ERROR, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDestroyResPool(g_stmt, ResPoolName);
    ASSERT_EQ(GMERR_RESOURCE_POOL_ERROR, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, LabelName);
    ASSERT_EQ(GMERR_OK, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    EXPECT_EQ(GMERR_OK, ret);
}

//数据类型不匹配
TEST_F(NormativeErrorCode, Other_019_012)
{
    uint32_t value = 100;
    char *string_schema = NULL;
    void *vertexLabel = NULL;
    char const *label_name = "T39";
    char *teststr = (char *)"testver";

    readJanssonFile("schema_file/insert_property_schema.gmjson", &string_schema);
    ASSERT_NE((void *)NULL, string_schema);

    ret = GmcCreateVertexLabel(g_stmt, string_schema, g_label_config);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_RESOURCE, &value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_BYTES, &value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_FIXED, teststr, (strlen(teststr)));
    ASSERT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_BYTES, teststr, (strlen(teststr)));
    ASSERT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_STRING, teststr, (strlen(teststr)));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, label_name);
    ASSERT_EQ(GMERR_OK, ret);

    free(string_schema);
    string_schema = NULL;
}

//错误的操作对象
TEST_F(NormativeErrorCode, Other_019_013)
{
    int32_t ret = 0;
    void *label = NULL;
    char *string_schema = NULL;
    char LableNamePK[8] = "OP_PK1";
    int start_num = 0, end_num = 2, array_num = 3, vector_num = 3;

    readJanssonFile("schema_file/test_insert_node_array.gmjson", &string_schema);
    ASSERT_NE((void *)NULL, string_schema);
    GmcDropVertexLabel(g_stmt, op_label_name);
    ret = GmcCreateVertexLabel(g_stmt, string_schema, g_label_config);
    ASSERT_EQ(ret, GMERR_OK);

    ret = TestInsertArrayNodeVertex(
        g_stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, op_label_name);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, op_label_name, GMC_OPERATION_SCAN);

    GmcNodeT *root, *t1, *t2, *t3;

    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, LableNamePK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(isFinish, false);
        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByIndex(root, 0);
        ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t nodeSize;
        ret = GmcNodeGetElementCount(t2, &nodeSize);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(3, nodeSize);

        ret = GmcNodeGetElementCount(t3, &nodeSize);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(3, nodeSize);
    }
    GmcFreeIndexKey(g_stmt);

    ret = GmcDropVertexLabel(g_stmt, op_label_name);
    ASSERT_EQ(GMERR_OK, ret);
    free(string_schema);
    string_schema = NULL;
}


//非法属性值
TEST_F(NormativeErrorCode, Other_019_015)
{
    void *vertex_handle = NULL;
    char *label_fstream = NULL;
    const char *labelName = "type_increment";

    readJanssonFile("schema_file/test_increment_schema.gmjson", &label_fstream);
    ASSERT_NE((void *)NULL, label_fstream);

    ret = GmcCreateVertexLabel(g_stmt, label_fstream, g_label_config_compatible);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    int32_t i = 1;

    GmcNodeT *root, *t1;
    ret = GmcGetRootNode(g_stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);

    TestSetPropertyDataTypeInc(root, i);
    TestSetPropertyDataTypeFileds(g_stmt, i, 0);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
    free(label_fstream);
}

//违反限制
TEST_F(NormativeErrorCode, Other_019_022)
{
    int ret = 0;
    void *plabel = NULL;
    char *p_schema = NULL;
    char labelName[4] = "T72";
    GmcDropVertexLabel(g_stmt, labelName);

    readJanssonFile("schema_file/fileds_base_data_string.gmjson", &p_schema);
    EXPECT_NE((void *)NULL, p_schema);
    // create
    ret = GmcCreateVertexLabel(g_stmt, p_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);
    free(p_schema);

    int value_f0 = 65;
    int value_f1 = 43;
    int value_f2 = 58;
    int value_f3 = 198;
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    TestWriteFiledProperty(g_stmt, value_f0);
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_BITFIELD8, &value_f1, sizeof(uint8_t));
    EXPECT_EQ(GMERR_INVALID_VALUE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_BITFIELD8, &value_f2, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_BITFIELD8, &value_f3, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);  // bitfield现在是定长,不是变长
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // delete
    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
}

//对象在使用中
TEST_F(NormativeErrorCode, Other_019_023)
{
    void *vertexLabel = NULL;
    char *string_data_label_schema = NULL;

    readJanssonFile("schema_file/test_label_data_schema.gmjson", &string_data_label_schema);
    ASSERT_NE((void *)NULL, string_data_label_schema);

    int ret = GmcCreateVertexLabel(g_stmt, string_data_label_schema, g_label_config);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, label_data_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    TestInsertVertexNodeFileds(g_stmt, label_data_name, 20, keyNameString);

    ret = GmcTruncateVertexLabel(g_stmt, label_data_name);
    EXPECT_EQ(GMERR_OK, ret);

    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, label_data_name);
    ASSERT_EQ(GMERR_OK, ret);

    free(string_data_label_schema);
    string_data_label_schema = NULL;
}

//保留名字
TEST_F(NormativeErrorCode, Other_019_024)
{
    int ret = -1;
    GmcStmtT *stmt = NULL;
    const char *p_user_name = (const char *)"server";

    ret = GmcAllocStmt(g_conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcCreateNamespace(stmt, p_name_str1, p_user_name);
    ASSERT_EQ(GMERR_INVALID_NAME, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcCreateNamespace(stmt, p_name_str2, p_user_name);
    ASSERT_EQ(GMERR_INVALID_NAME, ret);
    GmcFreeStmt(stmt);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    EXPECT_EQ(GMERR_OK, ret);
}

//重复的对象
TEST_F(NormativeErrorCode, Other_019_025)
{
    void *vertexLabel = NULL;
    bool bool_value = false;
    char *vertexLabel_schema = NULL;
    int ret = 0, start_num = 0, end_num = 10;

    readJanssonFile("schema_file/test_vertex_property.gmjson", &vertexLabel_schema);
    ASSERT_NE((void *)NULL, vertexLabel_schema);

    ret = GmcDropVertexLabel(g_stmt, data_type_label_name);
    ret = GmcCreateVertexLabel(g_stmt, vertexLabel_schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(vertexLabel_schema);

    ret = testGmcPrepareStmtByLabelName(g_stmt, data_type_label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    GmcNodeT *root;
    ret = GmcGetRootNode(g_stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);

    TestSetVertexFiledsPropertyByName(root, 0, bool_value, set_string_value);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, data_type_label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcGetRootNode(g_stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);

    TestSetVertexFiledsPropertyByName(root, 1, bool_value, set_string_value);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_PRIMARY_KEY_VIOLATION, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    TestFetchVertexFiledsByName(
        g_stmt, bool_value, 0, set_string_value, fileds_val_data, data_type_label_name, string_key_name);

    ASSERT_EQ(GMERR_OK, ret);
}

//没有数据场景
TEST_F(NormativeErrorCode, Other_019_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint64_t number = 1;
    uint64_t count_pool = 1;
    uint64_t index_respool = g_val_capacity;
    void *vertexLabel = NULL;
    GmcDropVertexLabel(g_stmt, "TestResourceLable");
    ret = GmcCreateVertexLabel(g_stmt, g_schema_label_string, g_label_config);
    ret = (ret == GMERR_DUPLICATE_OBJECT ? GMERR_OK : ret);
    ret = testGmcGetLastError(NULL);
    ASSERT_EQ(GMERR_OK, ret);
    do {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_string, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ASSET_AFTER_BREAK(ret, "operate open vertex \"%s\"", g_label_string);
        GmcDestroyResPool(g_stmt, "TestResourcePool");
        ret = GmcCreateResPool(g_stmt, ResourcePoolJson);
        ASSET_AFTER_BREAK(ret, "create resource pool");
        ret = GmcBindResPoolToLabel(g_stmt, g_name_resource_pool, g_label_string);
        ASSET_AFTER_BREAK(ret, "bind resource pool \"%s\" to label \"%s\"", g_name_resource_pool, g_label_string);
    } while (0);

    for (int32_t i = 0; ret == GMERR_OK && i < number; i++) {
        int32_t f0_data_set = i;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &f0_data_set, sizeof(f0_data_set));
        ASSET_AFTER_BREAK(ret, "add to lable attribute operate");
        uint64_t tmpResIdx = 0;
        ret = GmcSetPoolIdResource(gResPoolId, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetCountResource(count_pool, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetStartIdxResource(index_respool, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

        ASSET_AFTER_BREAK(ret, "add to resource pool property");
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_INT32, &f0_data_set, sizeof(f0_data_set));
        ASSET_AFTER_BREAK(ret, "add to lable attribute operate");
        ret = GmcExecute(g_stmt);
        if (ret != GMERR_RESOURCE_POOL_ERROR) {
            expect = NULL;
            ret = testGmcGetLastError(expect);
            EXPECT_EQ(GMERR_OK, ret);
            TEST_ERROR("insert vertex, expect ret = %d, actual ret = %d", GMERR_RESOURCE_POOL_ERROR, ret);
            ret = RETURN_FAIL;
            break;
        }
        ret = TestCheckOutDataValue(g_stmt, 0);
        ASSET_AFTER_BREAK(ret, "check affect rows");
    }

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_string, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    for (int32_t i = 0; ret == GMERR_OK && i < number; i++) {
        int32_t pk_value_data = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &pk_value_data, sizeof(pk_value_data));
        ASSET_AFTER_BREAK(ret, "add to index key value");
        ret = GmcSetIndexKeyName(g_stmt, "PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        if (ret != GMERR_OK) {  // GMERR_NO_DATA
            bool isFinish = true;
            ret = GmcFetch(g_stmt, &isFinish);
            ASSERT_EQ(GMERR_OK, ret);
            ASSERT_EQ(isFinish, true);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            TEST_ERROR("direct fetch vertex, expect failed, actual ret = %d", ret);
            ret = RETURN_FAIL;
            break;
        } else {
            ret = GMERR_OK;
        }
    }
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_string, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    for (int32_t i = 0; i < number; i++) {
        int32_t pk_value_data = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &pk_value_data, sizeof(pk_value_data));
        ASSET_AFTER_BREAK(ret, "add to index key value");
        ret = GmcSetIndexKeyName(g_stmt, "PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        ASSET_AFTER_BREAK(ret, "delete vertex by index key");
    }
    if (ret != GMERR_OK) {
        testGmcGetLastError();
    }

    ret = GmcUnbindResPoolFromLabel(g_stmt, g_label_string);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDestroyResPool(g_stmt, g_name_resource_pool);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// GmcGetLastErrorInfo接口获得是否为最后一次的报错信息
TEST_F(NormativeErrorCode, Other_019_027)
{
    int ret;
    uint64_t respoolId = 1;
    uint64_t count = 2;
    uint64_t startIndex = 1;
    void *gVertexLabel = NULL;
    const char *gLableConfig = R"({"max_record_num":1000})";
    const char *p_resource_pool_name = "TestResourcePool";
    const char *ResPoolErrorCode =
        R"({
        "name" : "ResPoolErrorCode",
        "pool_id" : 65535,
        "start_id" : 0,
        "capacity" : 0,
        "order" : 1,
        "alloc_type" : 0
    })";
    const char *g_label_name = "LableTest";
    const char *gLabelSchema =
        R"([{
        "type":"record",
        "name":"LableTest",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"int32", "nullable":false},
                {"name":"F3", "type":"int32", "nullable":false},
                {"name":"F4", "type":"resource", "nullable":false}
            ],
        "keys":
            [
                {
                    "node":"LableTest",
                    "name":"T58_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    GmcDestroyResPool(g_stmt, "ResPoolErrorCode");
    ret = GmcCreateResPool(g_stmt, ResPoolErrorCode);
    ASSERT_EQ(GMERR_RESOURCE_POOL_ERROR, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(g_stmt, gLabelSchema, gLableConfig);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcCreateResPool(g_stmt, ResPoolErrorCode);
    ASSERT_EQ(GMERR_RESOURCE_POOL_ERROR, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBindResPoolToLabel(g_stmt, p_resource_pool_name, g_label_name);
    ASSERT_EQ(GMERR_RESOURCE_POOL_ERROR, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetCountResource(count, &tmpResIdx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F4", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

    ASSERT_EQ(GMERR_OK, ret);

    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcBindResPoolToLabel(g_stmt, p_resource_pool_name, g_label_name);
    ASSERT_EQ(GMERR_RESOURCE_POOL_ERROR, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnbindResPoolFromLabel(g_stmt, g_label_name);
    ASSERT_EQ(GMERR_RESOURCE_POOL_ERROR, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDestroyResPool(g_stmt, p_resource_pool_name);
    ASSERT_EQ(GMERR_RESOURCE_POOL_ERROR, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcCreateResPool(g_stmt, ResPoolErrorCode);
    ASSERT_EQ(GMERR_RESOURCE_POOL_ERROR, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, g_label_name);
    ASSERT_EQ(GMERR_OK, ret);
}

// GmcGetLastErrorInfo接口 errorInfo传入赋初值再取地址的二级指针
TEST_F(NormativeErrorCode, Other_019_028)
{
    const char *expect01 = "The value of capacity should be in the range : [0, 4294967295].";
    const char *resPoolName = "res_pool_max_overflow";
    const char *ResPoolTest =
        R"({
        "name" : "res_pool_max_overflow",
        "pool_id" : 65535,
        "start_id" : 0,
        "capacity" : 987654321010,
        "order" : 1,
        "alloc_type" : 0
    })";

    ret = GmcCreateResPool(g_stmt, ResPoolTest);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    expect01 = GmcGetLastError();

    ret = GmcDestroyResPool(g_stmt, resPoolName);
    EXPECT_EQ(GMERR_RESOURCE_POOL_ERROR, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    EXPECT_EQ(GMERR_OK, ret);
}

// GmcGetLastErrorInfo接口 errorInfo传入特殊值
TEST_F(NormativeErrorCode, Other_019_029)
{
    const char *expect01 = "~!@#$%^&&*(){}:<>?";
    const char *resPoolName = "res_pool_max_overflow";
    const char *ResPoolTest =
        R"({
        "name" : "res_pool_max_overflow",
        "pool_id" : 65535,
        "start_id" : 0,
        "capacity" : 987654321010987654321,
        "order" : 1,
        "alloc_type" : 0
    })";

    ret = GmcCreateResPool(g_stmt, ResPoolTest);
    EXPECT_EQ(GMERR_INVALID_JSON_CONTENT, ret);

    expect01 = GmcGetLastError();

    printf("expect01=%s\n", expect01);

    ret = GmcDestroyResPool(g_stmt, resPoolName);
    EXPECT_EQ(GMERR_RESOURCE_POOL_ERROR, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    EXPECT_EQ(GMERR_OK, ret);
}

// GmcGetLastErrorInfo接口 errorInfo使用完后置空
TEST_F(NormativeErrorCode, Other_019_030)
{
    const char *LastErrorInfo = NULL;
    const char *expect01 = "LASTERR (3) novalid resource pool capacity, (capacity: 0)";
    const char *resPoolName = "res_pool_max_zero";
    const char *ResPoolTest =
        R"({
        "name" : "res_pool_max_zero",
        "pool_id" : 65535,
        "start_id" : 0,
        "capacity" : 0,
        "order" : 0,
        "alloc_type" : 0
    })";

    GmcDestroyResPool(g_stmt, "res_pool_max_zero");
    ret = GmcCreateResPool(g_stmt, ResPoolTest);
    EXPECT_EQ(GMERR_RESOURCE_POOL_ERROR, ret);
    LastErrorInfo = GmcGetLastError();
    EXPECT_STRNE(NULL, strstr(LastErrorInfo, expect01));

    LastErrorInfo = NULL;
    GmcDestroyResPool(g_stmt, "res_pool_max_zero");
    ret = GmcCreateResPool(g_stmt, ResPoolTest);
    EXPECT_EQ(GMERR_RESOURCE_POOL_ERROR, ret);
    LastErrorInfo = GmcGetLastError();
    EXPECT_STRNE(NULL, strstr(LastErrorInfo, expect01));

    ret = GmcDestroyResPool(g_stmt, resPoolName);
    EXPECT_EQ(GMERR_RESOURCE_POOL_ERROR, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    EXPECT_EQ(GMERR_OK, ret);
}

void *last_error_thread1(void *args)
{
    GmcConnT *thread_conn = NULL;
    GmcStmtT *thread_stmt = NULL;
    char *pjson = NULL;
    const char *resPoolName = "res_pool_thread1";
    const char *ResPoolTest =
        R"({
        "name" : "res_pool_max_zero",
        "pool_id" : 65535,
        "start_id" : 0,
        "capacity" : 0,
        "order" : 0,
        "alloc_type" : 0
    })";
    int ret = testGmcConnect(&thread_conn, &thread_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcCreateResPool(thread_stmt, ResPoolTest);
    EXPECT_EQ(GMERR_RESOURCE_POOL_ERROR, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDestroyResPool(thread_stmt, resPoolName);
    EXPECT_EQ(GMERR_RESOURCE_POOL_ERROR, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(thread_conn, thread_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

void *last_error_thread2(void *args)
{
    GmcConnT *thread_conn = NULL;
    GmcStmtT *thread_stmt = NULL;
    char *pjson = NULL;
    const char *resPoolName = "res_pool_thread2";
    const char *ResPoolTest =
        R"({
        "name" : "res_pool_max_zero",
        "pool_id" : 65535,
        "start_id" : 0,
        "capacity" : 0,
        "order" : 0,
        "alloc_type" : 0
    })";
    int ret = testGmcConnect(&thread_conn, &thread_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcCreateResPool(thread_stmt, ResPoolTest);
    EXPECT_EQ(GMERR_RESOURCE_POOL_ERROR, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDestroyResPool(thread_stmt, resPoolName);
    EXPECT_EQ(GMERR_RESOURCE_POOL_ERROR, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(thread_conn, thread_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

void *last_error_thread3(void *args)
{
    GmcConnT *thread_conn = NULL;
    GmcStmtT *thread_stmt = NULL;
    char *pjson = NULL;
    const char *resPoolName = "res_pool_thread3";
    const char *ResPoolTest =
        R"({
        "name" : "res_pool_thread3",
        "pool_id" : 65535,
        "start_id" : 3,
        "capacity" : 0,
        "order" : 1,
        "alloc_type" : 0
    })";
    int ret = testGmcConnect(&thread_conn, &thread_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcCreateResPool(thread_stmt, ResPoolTest);
    EXPECT_EQ(GMERR_RESOURCE_POOL_ERROR, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDestroyResPool(thread_stmt, resPoolName);
    EXPECT_EQ(GMERR_RESOURCE_POOL_ERROR, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(thread_conn, thread_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

void *last_error_thread4(void *args)
{
    GmcConnT *thread_conn = NULL;
    GmcStmtT *thread_stmt = NULL;
    char *pjson = NULL;
    const char *resPoolName = "res_pool_thread4";
    const char *ResPoolTest =
        R"({
        "name" : "res_pool_thread4",
        "pool_id" : 65535,
        "start_id" : 4,
        "capacity" : 0,
        "order" : 1,
        "alloc_type" : 0
    })";
    int ret = testGmcConnect(&thread_conn, &thread_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcCreateResPool(thread_stmt, ResPoolTest);
    EXPECT_EQ(GMERR_RESOURCE_POOL_ERROR, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDestroyResPool(thread_stmt, resPoolName);
    EXPECT_EQ(GMERR_RESOURCE_POOL_ERROR, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(thread_conn, thread_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

void *last_error_thread5(void *args)
{
    GmcConnT *thread_conn = NULL;
    GmcStmtT *thread_stmt = NULL;
    char *pjson = NULL;
    const char *resPoolName = "res_pool_thread5";
    const char *ResPoolTest =
        R"({
        "name" : "res_pool_thread5",
        "pool_id" : 65535,
        "start_id" : 5,
        "capacity" : 0,
        "order" : 1,
        "alloc_type" : 0
    })";
    int ret = testGmcConnect(&thread_conn, &thread_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcCreateResPool(thread_stmt, ResPoolTest);
    EXPECT_EQ(GMERR_RESOURCE_POOL_ERROR, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDestroyResPool(thread_stmt, resPoolName);
    EXPECT_EQ(GMERR_RESOURCE_POOL_ERROR, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(thread_conn, thread_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

//并发GmcGetLastErrorInfo获取的每一个conn返回结果
TEST_F(NormativeErrorCode, Other_019_031)
{
    int i = 0;
    pthread_t client_thr[5];
    void *thr_ret[5] = {0};

    ret = pthread_create(&client_thr[i++], NULL, last_error_thread1, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = pthread_create(&client_thr[i++], NULL, last_error_thread2, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = pthread_create(&client_thr[i++], NULL, last_error_thread3, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = pthread_create(&client_thr[i++], NULL, last_error_thread4, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = pthread_create(&client_thr[i++], NULL, last_error_thread5, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < 5; i++) {
        pthread_join(client_thr[i], &thr_ret[i]);
    }
}
