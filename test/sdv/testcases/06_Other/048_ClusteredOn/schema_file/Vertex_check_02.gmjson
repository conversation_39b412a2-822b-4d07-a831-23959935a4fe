[{"version": "2.0", "type": "record", "name": "Vertex_check_02", "fields": [{"name": "PK", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": false}, {"name": "F2", "type": "uint32", "nullable": false}, {"name": "F3", "type": "uint32", "nullable": false}, {"name": "F4", "type": "string", "size": 20, "nullable": true}, {"name": "F5", "type": "partition", "nullable": false}], "keys": [{"name": "Vertex_pk", "index": {"type": "primary"}, "node": "Vertex_check_02", "fields": ["PK"], "constraints": {"unique": true}}]}]