/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * Description: 客户端进程异常退出资源回收测试用例
 * Author: guopanpan
 * Create: 2021-04-28
 * History:
 */
#include "ClearResWithClientExitAbnormally.h"
#include "t_datacom_lite.h"

using namespace std;

typedef struct tagProcessResT {
    int32_t processIdx;
    int32_t resNum;
} GtProcessResT;

class ClearResWithClientExitAbnormally005 : public testing::Test {
public:
    static void SetUpTestCase()
    {
        system("mkdir -p /data/gmdb/; rm -rf /data/gmdb/*");
        GtExecSystemCmd("start.sh");
        AW_CHECK_LOG_BEGIN();
    }

    static void TearDownTestCase()
    {
        AW_CHECK_LOG_END();
        GtExecSystemCmd("stop.sh");
    }

    virtual void SetUp()
    {
        system("rm -rf a.log");
        system("rm -rf b.log");
        int ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }

    virtual void TearDown()
    {
        int ret;
        ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
        system("rm -rf a.log");
        system("rm -rf b.log");
    }
};

class ClearResWithClientExitAbnormally005_1 : public testing::Test {};

/*************************************************** Latch锁资源回收
 * ***************************************************/

void *GtThreadDirectRead(void *resNum)
{
    int ret;
    int64_t dataNum = (int64_t)resNum;

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    do {
        ret = testGmcConnect(&conn, &stmt);
        CHECK_AND_BREAK(ret, "connect");
    } while (0);

    int32_t loopNum = 10;
    for (int loop = 0; ret == GMERR_OK && loop < loopNum; loop++) {
        for (int32_t i = 0; ret == GMERR_OK && i < dataNum; i++) {
            ret = testGmcPrepareStmtByLabelName(stmt, gLabelName, GMC_OPERATION_SCAN);
            CHECK_AND_BREAK(ret, "open vertex label by name");
            uint32_t isNull;
            int32_t PKValue = i;
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &PKValue, sizeof(PKValue));
            CHECK_AND_BREAK(ret, "set index key value");
            ret = GmcSetIndexKeyName(stmt, "PK");
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            CHECK_AND_BREAK(ret, "fetch vertex");
            bool isFinished;
            ret = GmcFetch(stmt, &isFinished);
            CHECK_AND_BREAK(ret, "fetch record from stmt");
            ret = GtGetAndCheckProperty(stmt, i, i, i * 2);
            CHECK_AND_BREAK(ret, "get and check property");
        }
    }

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    conn = NULL;
    stmt = NULL;
    return (void *)(int64_t)ret;
}

int GtProcessDirectRead(void *argu)
{
    int32_t dataNum = 1000;

    int ret, semId;

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    do {
        ret = testEnvInit();
        CHECK_AND_BREAK(ret, "init test env");
        ret = create_epoll_thread();
        CHECK_AND_BREAK(ret, "create heart epoll thread");
        ret = testGmcConnect(&conn, &stmt);
        CHECK_AND_BREAK(ret, "connect");
        ret = GmcCreateVertexLabel(stmt, gLabelSchemaJson, gLabelConfig);
        ret = ret == GMERR_DUPLICATE_TABLE ? GMERR_OK : ret;
        CHECK_AND_BREAK(ret, "create vertex label");
    } while (0);

    for (int32_t i = 0; ret == GMERR_OK && i < dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, gLabelName, GMC_OPERATION_INSERT);
        CHECK_AND_BREAK(ret, "open vertex label");
        ret = GtSetInsertProperty(stmt, i, i, i * 2);
        if (ret == GMERR_COMMON_STREAM_OVERLOAD) {  // IOT上会触发过载流控
            usleep(100000);
            --i;
            continue;
        }

        CHECK_AND_BREAK(ret, "set property");
        ret = GmcExecute(stmt);
        if (ret == GMERR_PRIMARY_KEY_VIOLATION) {
            ret = GMERR_OK;
            continue;
        }
        CHECK_AND_BREAK(ret, "insert vertex");
        ret = GtCheckAffectRows(stmt, 1);
        CHECK_AND_BREAK(ret, "check effect rows");
    }

    pthread_t tids[10];
    int threadNum = sizeof(tids) / sizeof(tids[0]);
    int tmpRet;
    for (int i = 0; i < threadNum; i++) {
        tmpRet = pthread_create(&tids[i], NULL, GtThreadDirectRead, (void *)(int64_t)dataNum);
        SET_RET_IFERR(ret, tmpRet, "create thread");
    }

    system("cat signalFile/signal1_true.txt >>a.log");

    AW_FUN_Log(LOG_STEP, "GtProcessDirectRead wait for be kill.");
    while (1) {
        sleep(1);
    }

    return ret;
}

int GtProcessDirectRead2(void *argu)
{
    int32_t dataNum = 1000;

    int ret, semId;

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    do {
        ret = testEnvInit();
        CHECK_AND_BREAK(ret, "init test env");
        ret = create_epoll_thread();
        CHECK_AND_BREAK(ret, "create heart epoll thread");
        ret = testGmcConnect(&conn, &stmt);
        CHECK_AND_BREAK(ret, "connect");
        ret = GmcCreateVertexLabel(stmt, gLabelSchemaJson, gLabelConfig);
        ret = ret == GMERR_DUPLICATE_TABLE ? GMERR_OK : ret;
        CHECK_AND_BREAK(ret, "create vertex label");
    } while (0);

    for (int32_t i = 0; ret == GMERR_OK && i < dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, gLabelName, GMC_OPERATION_INSERT);
        CHECK_AND_BREAK(ret, "open vertex label");
        ret = GtSetInsertProperty(stmt, i, i, i * 2);
        if (ret == GMERR_COMMON_STREAM_OVERLOAD) {  // IOT上会触发过载流控
            usleep(100000);
            --i;
            continue;
        }

        CHECK_AND_BREAK(ret, "set property");
        ret = GmcExecute(stmt);
        if (ret == GMERR_PRIMARY_KEY_VIOLATION) {
            ret = GMERR_OK;
            continue;
        }
        CHECK_AND_BREAK(ret, "insert vertex");
        ret = GtCheckAffectRows(stmt, 1);
        CHECK_AND_BREAK(ret, "check effect rows");
    }

    // 清理资源后退出
    if (ret != GMERR_OK) {
        GmcDropVertexLabel(stmt, gLabelName);
        ret = testGmcDisconnect(conn, stmt);
        EXPECT_EQ(GMERR_OK, ret);
        close_epoll_thread();
        g_needCheckWhenSucc = false;
        testEnvClean();

        return ret;
    }

    pthread_t tids[10];
    int threadNum = sizeof(tids) / sizeof(tids[0]);
    int tmpRet;
    for (int i = 0; i < threadNum; i++) {
        tmpRet = pthread_create(&tids[i], NULL, GtThreadDirectRead, (void *)(int64_t)dataNum);
        SET_RET_IFERR(ret, tmpRet, "create thread");
    }

    for (int i = 0; i < threadNum; i++) {
        void *thRet = NULL;
        tmpRet = pthread_join(tids[i], &thRet);
        SET_RET_IFERR(ret, tmpRet, "join thread");
        SET_RET_IFERR(ret, (int64_t)thRet, "thread exit status");
    }

    ret = GmcDropVertexLabel(stmt, gLabelName);
    tmpRet = (tmpRet == GMERR_UNDEFINED_TABLE ? GMERR_OK : ret);
    SET_RET_IFERR(ret, tmpRet, "drop vertex label, labelName = %s", gLabelName);
    ret = close_epoll_thread();
    SET_RET_IFERR(ret, tmpRet, "close heart epoll thread");
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    conn = NULL;
    stmt = NULL;
    ret = testEnvClean();
    SET_RET_IFERR(ret, tmpRet, "clean test env");
    return ret;
}

void *ThreadStartGtProcessDirectRead(void *args)
{
    system("./ClearRsCliExit5 --gtest_also_run_disabled_tests "
           "--gtest_filter=ClearResWithClientExitAbnormally005_1.DISABLED_GtProcessDirectRead >Read.txt");
    return NULL;
}

void *ThreadStartGtProcessDirectRead2(void *args)
{
    int ret = system("./ClearRsCliExit5 --gtest_also_run_disabled_tests "
                     "--gtest_filter=ClearResWithClientExitAbnormally005_1.DISABLED_GtProcessDirectRead2 >Read2.txt");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

// 客户端并发直连读，中途异常退出客户端，另起客户端执行DB业务 ，查询latch锁状态
TEST_F(ClearResWithClientExitAbnormally005, Other_014_038)
{
    const int32_t dataNum = 1000;
    int ret, semsId;

    pthread_t thr_arr[2];
    ret = pthread_create(&thr_arr[0], NULL, ThreadStartGtProcessDirectRead, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "等待客户端启动");
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat a.log");
    ret = executeCommand(g_command, "true");
    while (ret != GMERR_OK) {
        sleep(1);
        ret = executeCommand(g_command, "true");
    }

    char *result = NULL;
#if defined(RUN_INDEPENDENT) || defined(ENV_RTOSV2)
    ret = GtExecSystemCmd(
        &result, "ps -ef |grep gtest_also_run_disabled_tests |grep -v grep |grep -v sh |awk '{print $2}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("ps -ef |grep gtest_also_run_disabled_tests |grep -v grep");
#else
    ret = GtExecSystemCmd(&result, "ps |grep gtest_also* |grep -v grep |grep -v sh |awk '{print $1}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("ps |grep gtest_also* |grep -v grep");
#endif
    int32_t pid = atoi(result);
    free(result);

    ret = kill(pid, SIGKILL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GtCheckLatchStatus(0);
    EXPECT_EQ(GMERR_OK, ret);

    // 另起子进程验证资源状态正常
    ret = pthread_create(&thr_arr[1], NULL, ThreadStartGtProcessDirectRead2, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_join(thr_arr[1], NULL);

    ret = GtCheckLatchStatus(0);
    EXPECT_EQ(GMERR_OK, ret);
}

// 客户端并发直连读，中途异常退出客户端，另起客户端执行DB业务 ，查询latch锁状态，循环执行多次
TEST_F(ClearResWithClientExitAbnormally005, Other_014_039)
{
    const int32_t dataNum = 1000;
    int ret, semsId;
    pthread_t thr_arr[2];

    for (int i = 0; i < MAX_LOOP_NUM; i++) {
        ret = pthread_create(&thr_arr[0], NULL, ThreadStartGtProcessDirectRead, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        AW_FUN_Log(LOG_STEP, "等待客户端启动");
        (void)snprintf(g_command, MAX_CMD_SIZE, "cat a.log");
        ret = executeCommand(g_command, "true");
        while (ret != GMERR_OK) {
            sleep(1);
            ret = executeCommand(g_command, "true");
        }

        char *result = NULL;
#if defined(RUN_INDEPENDENT) || defined(ENV_RTOSV2)
        ret = GtExecSystemCmd(
            &result, "ps -ef |grep gtest_also_run_disabled_tests |grep -v grep |grep -v sh |awk '{print $2}'");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        system("ps -ef |grep gtest_also_run_disabled_tests |grep -v grep");
#else
        ret = GtExecSystemCmd(&result, "ps |grep gtest_also* |grep -v grep |grep -v sh |awk '{print $1}'");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        system("ps |grep gtest_also* |grep -v grep");
#endif
        int32_t pid = atoi(result);
        free(result);

        ret = kill(pid, SIGKILL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GtCheckLatchStatus(0);
        EXPECT_EQ(GMERR_OK, ret);

        // 另起子进程验证资源状态正常
        ret = pthread_create(&thr_arr[1], NULL, ThreadStartGtProcessDirectRead2, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        pthread_join(thr_arr[1], NULL);
        ret = GtCheckLatchStatus(0);
        EXPECT_EQ(GMERR_OK, ret);
        system("rm -rf a.log");
    }
    ret = GtCheckLatchStatus(0);
    EXPECT_EQ(GMERR_OK, ret);
}

/************************************************* 表资源引用计数回收 *************************************************/
int GtProcessTableRef(void *args)
{
    AW_FUN_Log(LOG_STEP, "GtProcessTableRef start042.");
    int32_t refCnt = 1;

    int ret, semId;

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    do {
        ret = testEnvInit();
        CHECK_AND_BREAK(ret, "init test env");
        ret = create_epoll_thread();
        CHECK_AND_BREAK(ret, "create heart epoll thread");
        ret = testGmcConnect(&conn, &stmt);
        CHECK_AND_BREAK(ret, "connect");
        ret = GmcCreateVertexLabel(stmt, gLabelSchemaJson, gLabelConfig);
        ret = ret == GMERR_DUPLICATE_TABLE ? GMERR_OK : ret;
        CHECK_AND_BREAK(ret, "create vertex label");
    } while (0);

    int32_t dataNum = 1000;
    for (int32_t i = 0; ret == GMERR_OK && i < dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, gLabelName, GMC_OPERATION_INSERT);
        CHECK_AND_BREAK(ret, "open vertex label");
        ret = GtSetInsertProperty(stmt, i, i, i * 2);
        CHECK_AND_BREAK(ret, "set property");
        ret = GmcExecute(stmt);
        if (ret == GMERR_PRIMARY_KEY_VIOLATION) {
            ret = GMERR_OK;
            continue;
        }
        CHECK_AND_BREAK(ret, "insert vertex");
        ret = GtCheckAffectRows(stmt, 1);
        CHECK_AND_BREAK(ret, "check effect rows");
    }

    // 清理资源后退出
    if (ret != GMERR_OK) {
        GmcDropVertexLabel(stmt, gLabelName);
        ret = testGmcDisconnect(conn, stmt);
        EXPECT_EQ(GMERR_OK, ret);
        close_epoll_thread();
        g_needCheckWhenSucc = false;
        testEnvClean();

        return ret;
    }
    AW_FUN_Log(LOG_STEP, "GtProcessTableRef end042.");
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO >RefVERTEX.txt");
    system("cat signalFile/signal1_true.txt >>a.log");

    AW_FUN_Log(LOG_STEP, "GtProcessTableRef wait for be kill.");
    while (1) {
        sleep(1);
    }

    return ret;
}

int GtProcessTableRef1(void *args)
{
    AW_FUN_Log(LOG_STEP, "GtProcessTableRef1 start042.");
    int32_t refCnt = 1;

    int ret, semId;

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    do {
        ret = testEnvInit();
        CHECK_AND_BREAK(ret, "init test env");
        ret = create_epoll_thread();
        CHECK_AND_BREAK(ret, "create heart epoll thread");
        ret = testGmcConnect(&conn, &stmt);
        CHECK_AND_BREAK(ret, "connect");
        ret = GmcCreateVertexLabel(stmt, gLabelSchemaJson, gLabelConfig);
        ret = ret == GMERR_DUPLICATE_TABLE ? GMERR_OK : ret;
        CHECK_AND_BREAK(ret, "create vertex label");
    } while (0);
    int32_t count = 0;
    int32_t dataNum = 1000;
    for (int32_t i = 0; ret == GMERR_OK && i < dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, gLabelName, GMC_OPERATION_INSERT);
        CHECK_AND_BREAK(ret, "open vertex label");
        ret = GtSetInsertProperty(stmt, i, i, i * 2);
        CHECK_AND_BREAK(ret, "set property");
        ret = GmcExecute(stmt);
        if (ret == GMERR_PRIMARY_KEY_VIOLATION) {
            ret = GMERR_OK;
            continue;
        }
        CHECK_AND_BREAK(ret, "insert vertex");
        ret = GtCheckAffectRows(stmt, 1);
        CHECK_AND_BREAK(ret, "check effect rows");
        count++;
    }

    // 清理资源后退出
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_STEP, "count is %d", count);
        system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO >Ref2VERTEX.txt");
        GmcDropVertexLabel(stmt, gLabelName);
        ret = testGmcDisconnect(conn, stmt);
        EXPECT_EQ(GMERR_OK, ret);
        close_epoll_thread();
        g_needCheckWhenSucc = false;
        testEnvClean();

        return ret;
    }
    AW_FUN_Log(LOG_STEP, "GtProcessTableRef1 end042.");
    system("cat signalFile/signal1_true.txt >>b.log");

    AW_FUN_Log(LOG_STEP, "GtProcessTableRef1 wait for be kill.");
    while (1) {
        sleep(1);
    }

    return ret;
}

int GtProcessCheckTableRefCnt2(void *expectRefCnt)
{
    int64_t refCnt = (int64_t)expectRefCnt;

    int ret;

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    do {
        ret = testEnvInit();
        CHECK_AND_BREAK(ret, "init test env");
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcConnect(&conn, &stmt);
        CHECK_AND_BREAK(ret, "connect");
        ret = GmcTruncateVertexLabel(stmt, gLabelName);
        CHECK_AND_BREAK(ret, "truncate vertex label, label name = %s", gLabelName);
    } while (0);

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    conn = NULL;
    stmt = NULL;
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    g_needCheckWhenSucc = false;
    testEnvClean();

    return ret;
}

int GtProcessTableRef2(void *args)
{
    int32_t refCnt = 1;

    int ret, semId;

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    do {
        ret = testEnvInit();
        CHECK_AND_BREAK(ret, "init test env");
        ret = create_epoll_thread();
        CHECK_AND_BREAK(ret, "create heart epoll thread");
        ret = testGmcConnect(&conn, &stmt);
        CHECK_AND_BREAK(ret, "connect");
        ret = GmcCreateVertexLabel(stmt, gLabelSchemaJson, gLabelConfig);
        ret = ret == GMERR_DUPLICATE_TABLE ? GMERR_OK : ret;
        CHECK_AND_BREAK(ret, "create vertex label");
    } while (0);

    int32_t dataNum = 1000;
    for (int32_t i = 0; ret == GMERR_OK && i < dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, gLabelName, GMC_OPERATION_INSERT);
        CHECK_AND_BREAK(ret, "open vertex label");
        ret = GtSetInsertProperty(stmt, i, i, i * 2);
        CHECK_AND_BREAK(ret, "set property");
        ret = GmcExecute(stmt);
        if (ret == GMERR_PRIMARY_KEY_VIOLATION) {
            ret = GMERR_OK;
            continue;
        }
        CHECK_AND_BREAK(ret, "insert vertex");
        ret = GtCheckAffectRows(stmt, 1);
        CHECK_AND_BREAK(ret, "check effect rows");
    }

    // 清理资源后退出
    if (ret != GMERR_OK) {
        GmcDropVertexLabel(stmt, gLabelName);
        ret = testGmcDisconnect(conn, stmt);
        EXPECT_EQ(GMERR_OK, ret);
        close_epoll_thread();
        g_needCheckWhenSucc = false;
        testEnvClean();

        return ret;
    }

    int tmpRet = GmcDropVertexLabel(stmt, gLabelName);
    tmpRet = (tmpRet == GMERR_UNDEFINED_TABLE ? GMERR_OK : tmpRet);
    SET_RET_IFERR(ret, tmpRet, "drop vertex label, label name = %s", gLabelName);
    tmpRet = close_epoll_thread();
    SET_RET_IFERR(ret, tmpRet, "close heart epoll thread");
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    conn = NULL;
    stmt = NULL;
    tmpRet = testEnvClean();
    SET_RET_IFERR(ret, tmpRet, "clean test env");
    return ret;
}

void *ThreadStartGtProcessTableRef(void *args)
{
    system("./ClearRsCliExit5 --gtest_also_run_disabled_tests "
           "--gtest_filter=ClearResWithClientExitAbnormally005_1.DISABLED_GtProcessTableRef >TableRef.txt");
    return NULL;
}

void *ThreadStartGtProcessTableRef1(void *args)
{
    system("./ClearRsCliExit5 --gtest_also_run_disabled_tests "
           "--gtest_filter=ClearResWithClientExitAbnormally005_1.DISABLED_GtProcessTableRef1 >TableRef1.txt");
    return NULL;
}

void *ThreadStartGtProcessTableRef2(void *args)
{
    int ret = system("./ClearRsCliExit5 --gtest_also_run_disabled_tests "
           "--gtest_filter=ClearResWithClientExitAbnormally005_1.DISABLED_GtProcessTableRef2 >TableRef2.txt");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

// 客户端单线程增加表资源的引用计数，异常退出客户端进程，另起客户端进程查询表资源的引用计数状态
TEST_F(ClearResWithClientExitAbnormally005, Other_014_040)
{
    const int32_t refCnt = 1;
    int ret, semsId;

    pthread_t thr_arr[2];
    ret = pthread_create(&thr_arr[0], NULL, ThreadStartGtProcessTableRef, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "等待客户端启动");
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat a.log");
    ret = executeCommand(g_command, "true");
    while (ret != GMERR_OK) {
        sleep(1);
        ret = executeCommand(g_command, "true");
    }

    char *result = NULL;
#if defined(RUN_INDEPENDENT) || defined(ENV_RTOSV2)
    ret = GtExecSystemCmd(
        &result, "ps -ef |grep gtest_also_run_disabled_tests |grep -v grep |grep -v sh |awk '{print $2}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("ps -ef |grep gtest_also_run_disabled_tests |grep -v grep");
#else
    ret = GtExecSystemCmd(&result, "ps |grep gtest_also* |grep -v grep |grep -v sh |awk '{print $1}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("ps |grep gtest_also* |grep -v grep");
#endif
    int32_t pid = atoi(result);
    free(result);

    ret = kill(pid, SIGKILL);
    EXPECT_EQ(GMERR_OK, ret);

    system("./ClearRsCliExit5 --gtest_also_run_disabled_tests "
           "--gtest_filter=ClearResWithClientExitAbnormally005_1.DISABLED_GtProcessCheckTableRefCnt2 >RefCnt2.txt");

    // 另起子进程验证资源状态正常
    ret = pthread_create(&thr_arr[1], NULL, ThreadStartGtProcessTableRef2, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_join(thr_arr[1], NULL);
}

// 客户端单线程增加表资源的引用计数，异常退出客户端进程，另起客户端进程查询表资源的引用计数状态，循环执行多次
TEST_F(ClearResWithClientExitAbnormally005, Other_014_041)
{
    const int32_t refCnt = 1;
    int ret, semsId;
    pthread_t thr_arr[2];

    for (int i = 0; i < MAX_LOOP_NUM; i++) {
        ret = pthread_create(&thr_arr[0], NULL, ThreadStartGtProcessTableRef, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        AW_FUN_Log(LOG_STEP, "等待客户端启动");
        (void)snprintf(g_command, MAX_CMD_SIZE, "cat a.log");
        ret = executeCommand(g_command, "true");
        while (ret != GMERR_OK) {
            sleep(1);
            ret = executeCommand(g_command, "true");
        }

        char *result = NULL;
#if defined(RUN_INDEPENDENT) || defined(ENV_RTOSV2)
        ret = GtExecSystemCmd(
            &result, "ps -ef |grep gtest_also_run_disabled_tests |grep -v grep |grep -v sh |awk '{print $2}'");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        system("ps -ef |grep gtest_also_run_disabled_tests |grep -v grep");
#else
        ret = GtExecSystemCmd(&result, "ps |grep gtest_also* |grep -v grep |awk '{print $1}'");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        system("ps |grep gtest_also* |grep -v grep");
#endif
        int32_t pid = atoi(result);
        free(result);

        ret = kill(pid, SIGKILL);
        EXPECT_EQ(GMERR_OK, ret);

        // 另起子进程验证资源状态正常
        ret = pthread_create(&thr_arr[1], NULL, ThreadStartGtProcessTableRef2, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        pthread_join(thr_arr[1], NULL);
        system("rm -rf a.log");
    }
}

// 多客户端并发增加表资源的引用计数，异常退出客户端进程，另起客户端进程查询表资源的引用计数状态
TEST_F(ClearResWithClientExitAbnormally005, Other_014_042)
{
    const int32_t processNum = 2;
    const int32_t perCliTableRefCnt = 10;
    pthread_t thr_arr[4];

    // 创建子进程并使其异常退出
    int ret, semsId;
    ret = pthread_create(&thr_arr[0], NULL, ThreadStartGtProcessTableRef, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thr_arr[1], NULL, ThreadStartGtProcessTableRef1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "等待客户端启动");
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat a.log");
    ret = executeCommand(g_command, "true");
    while (ret != GMERR_OK) {
        sleep(1);
        system(g_command);
        ret = executeCommand(g_command, "true");
    }
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat b.log");
    ret = executeCommand(g_command, "true");
    while (ret != GMERR_OK) {
        sleep(1);
        system(g_command);
        ret = executeCommand(g_command, "true");
    }
    AW_FUN_Log(LOG_STEP, "资源申请完成");

    char *result1 = NULL;
    char *result2 = NULL;
#if defined(RUN_INDEPENDENT) || defined(ENV_RTOSV2)
    ret = GtExecSystemCmd(
        &result1, "ps -ef |grep gtest_also_run_disabled_tests |grep -v grep |grep -v sh |awk NR==1'{print $2}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSystemCmd(
        &result2, "ps -ef |grep gtest_also_run_disabled_tests |grep -v grep |grep -v sh |awk NR==2'{print $2}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("ps -ef |grep gtest_also_run_disabled_tests |grep -v grep");
#else
    ret = GtExecSystemCmd(&result1, "ps |grep gtest_also* |grep -v grep |grep -v sh |awk NR==1'{print $1}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSystemCmd(&result2, "ps |grep gtest_also* |grep -v grep |grep -v sh |awk NR==2'{print $1}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("ps |grep gtest_also* |grep -v grep");
#endif
    int32_t pid1 = atoi(result1);
    int32_t pid2 = atoi(result2);
    free(result1);
    free(result2);

    ret = kill(pid1, SIGKILL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = kill(pid2, SIGKILL);
    EXPECT_EQ(GMERR_OK, ret);

    system("./ClearRsCliExit5 --gtest_also_run_disabled_tests "
           "--gtest_filter=ClearResWithClientExitAbnormally005_1.DISABLED_GtProcessCheckTableRefCnt2 >RefCnt2.txt");

    // 另起客户端检查latch资源状态
    ret = pthread_create(&thr_arr[2], NULL, ThreadStartGtProcessTableRef2, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thr_arr[3], NULL, ThreadStartGtProcessTableRef2, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_join(thr_arr[2], NULL);
    pthread_join(thr_arr[3], NULL);
}

/**************************************************** 事务资源回收
 * *****************************************************/
int GtProcessTrans(void *res)
{
    int32_t cliIdx = 1;
    int32_t lockNum = 1;

    int ret, semId;
    char vertexLabel_config[] = "{\"max_record_num\":100000, \"isFastReadUncommitted\":0}";
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    do {
        ret = testEnvInit();
        CHECK_AND_BREAK(ret, "init test env");
        ret = create_epoll_thread();
        CHECK_AND_BREAK(ret, "create heart epoll thread");
        ret = testGmcConnect(&conn, &stmt);
        CHECK_AND_BREAK(ret, "connect");
        ret = GmcCreateVertexLabel(stmt, gLabelSchemaJson, vertexLabel_config);
        ret = ret == GMERR_DUPLICATE_TABLE ? GMERR_OK : ret;
        CHECK_AND_BREAK(ret, "create vertex label");

        // 开启事务
        GmcTxConfigT txConfig = {0};
        txConfig.readOnly = false;
        txConfig.transMode = GMC_TRANS_USED_IN_CS;
        txConfig.type = GMC_TX_ISOLATION_COMMITTED;
        ret = GmcTransStart(conn, &txConfig);
        CHECK_AND_BREAK(ret, "start trans");
    } while (0);

    int32_t startVal = cliIdx * lockNum;
    TEST_INFO("cliIdx = %d, startVal = %d, endVal = %d", cliIdx, startVal, startVal + lockNum);
    for (int32_t i = startVal; ret == GMERR_OK && i < lockNum + startVal; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, gLabelName, GMC_OPERATION_INSERT);
        CHECK_AND_BREAK(ret, "open vertex label");
        int32_t F0Value = i;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        int32_t F1Value = i;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        int64_t F2Value = i * 2;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT64, &F2Value, sizeof(F2Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        ret = GmcExecute(stmt);
        CHECK_AND_BREAK(ret, "insert the %dth vertex", i - startVal);
        ret = GtCheckAffectRows(stmt, 1);
        CHECK_AND_BREAK(ret, "check effect rows");
    };

    system("cat signalFile/signal1_true.txt >>a.log");
    AW_FUN_Log(LOG_STEP, "GtProcessDirectRead wait for be kill.");
    while (1) {
        sleep(1);
    }

    return ret;
}

int GtProcessTrans2(void *res)
{
    int32_t cliIdx = 2;
    int32_t lockNum = 1;

    int ret, semId;
    char vertexLabel_config[] = "{\"max_record_num\":100000, \"isFastReadUncommitted\":0}";
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    do {
        ret = testEnvInit();
        CHECK_AND_BREAK(ret, "init test env");
        ret = create_epoll_thread();
        CHECK_AND_BREAK(ret, "create heart epoll thread");
        ret = testGmcConnect(&conn, &stmt);
        CHECK_AND_BREAK(ret, "connect");
        ret = GmcCreateVertexLabel(stmt, gLabelSchemaJson, vertexLabel_config);
        ret = ret == GMERR_DUPLICATE_TABLE ? GMERR_OK : ret;
        CHECK_AND_BREAK(ret, "create vertex label");

        // 开启事务
        GmcTxConfigT txConfig = {0};
        txConfig.readOnly = false;
        txConfig.transMode = GMC_TRANS_USED_IN_CS;
        txConfig.type = GMC_TX_ISOLATION_COMMITTED;
        ret = GmcTransStart(conn, &txConfig);
        CHECK_AND_BREAK(ret, "start trans");
    } while (0);

    int32_t startVal = cliIdx * lockNum;
    TEST_INFO("cliIdx = %d, startVal = %d, endVal = %d", cliIdx, startVal, startVal + lockNum);
    for (int32_t i = startVal; ret == GMERR_OK && i < lockNum + startVal; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, gLabelName, GMC_OPERATION_INSERT);
        CHECK_AND_BREAK(ret, "open vertex label");
        int32_t F0Value = i;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        int32_t F1Value = i;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        int64_t F2Value = i * 2;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT64, &F2Value, sizeof(F2Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        ret = GmcExecute(stmt);
        CHECK_AND_BREAK(ret, "insert the %dth vertex", i - startVal);
        ret = GtCheckAffectRows(stmt, 1);
        CHECK_AND_BREAK(ret, "check effect rows");
    };

    system("cat signalFile/signal1_true.txt >>a.log");
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat b.log");
    ret = executeCommand(g_command, "true");
    while (ret != GMERR_OK) {
        sleep(1);
        ret = executeCommand(g_command, "true");
    }

    int tmpRet = GmcTransCommit(conn);
    SET_RET_IFERR(ret, tmpRet, "commit trans");

    SET_RET_IFERR(ret, tmpRet, "drop vertex label");
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    conn = NULL;
    stmt = NULL;
    tmpRet = close_epoll_thread();
    SET_RET_IFERR(ret, tmpRet, "close heart epoll trhead");
    g_needCheckWhenSucc = false;
    tmpRet = testEnvClean();
    SET_RET_IFERR(ret, tmpRet, "clean test env");
    return ret;
}

int GtProcessTrans3(void *res)
{
    int32_t cliIdx = 1;
    int32_t lockNum = 2048;

    int ret, semId;
    char vertexLabel_config[] = "{\"max_record_num\":100000, \"isFastReadUncommitted\":0}";
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    do {
        ret = testEnvInit();
        CHECK_AND_BREAK(ret, "init test env");
        ret = create_epoll_thread();
        CHECK_AND_BREAK(ret, "create heart epoll thread");
        ret = testGmcConnect(&conn, &stmt);
        CHECK_AND_BREAK(ret, "connect");
        ret = GmcCreateVertexLabel(stmt, gLabelSchemaJson, vertexLabel_config);
        ret = ret == GMERR_DUPLICATE_TABLE ? GMERR_OK : ret;
        CHECK_AND_BREAK(ret, "create vertex label");

        // 开启事务
        GmcTxConfigT txConfig = {0};
        txConfig.readOnly = false;
        txConfig.transMode = GMC_TRANS_USED_IN_CS;
        txConfig.type = GMC_TX_ISOLATION_COMMITTED;
        ret = GmcTransStart(conn, &txConfig);
        CHECK_AND_BREAK(ret, "start trans");
    } while (0);

    int32_t startVal = cliIdx * lockNum;
    TEST_INFO("cliIdx = %d, startVal = %d, endVal = %d", cliIdx, startVal, startVal + lockNum);
    for (int32_t i = startVal; ret == GMERR_OK && i < lockNum + startVal; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, gLabelName, GMC_OPERATION_INSERT);
        CHECK_AND_BREAK(ret, "open vertex label");
        int32_t F0Value = i;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        int32_t F1Value = i;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        int64_t F2Value = i * 2;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT64, &F2Value, sizeof(F2Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        ret = GmcExecute(stmt);
        CHECK_AND_BREAK(ret, "insert the %dth vertex", i - startVal);
        ret = GtCheckAffectRows(stmt, 1);
        CHECK_AND_BREAK(ret, "check effect rows");
    };

    system("cat signalFile/signal1_true.txt >>a.log");

    AW_FUN_Log(LOG_STEP, "GtProcessDirectRead wait for be kill.");
    while (1) {
        sleep(1);
    }

    return ret;
}

int GtProcessTrans4(void *res)
{
    int32_t cliIdx = 2;
    int32_t lockNum = 2048;

    int ret, semId;
    char vertexLabel_config[] = "{\"max_record_num\":100000, \"isFastReadUncommitted\":0}";
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    do {
        ret = testEnvInit();
        CHECK_AND_BREAK(ret, "init test env");
        ret = create_epoll_thread();
        CHECK_AND_BREAK(ret, "create heart epoll thread");
        ret = testGmcConnect(&conn, &stmt);
        CHECK_AND_BREAK(ret, "connect");
        ret = GmcCreateVertexLabel(stmt, gLabelSchemaJson, vertexLabel_config);
        ret = ret == GMERR_DUPLICATE_TABLE ? GMERR_OK : ret;
        CHECK_AND_BREAK(ret, "create vertex label");

        // 开启事务
        GmcTxConfigT txConfig = {0};
        txConfig.readOnly = false;
        txConfig.transMode = GMC_TRANS_USED_IN_CS;
        txConfig.type = GMC_TX_ISOLATION_COMMITTED;
        ret = GmcTransStart(conn, &txConfig);
        CHECK_AND_BREAK(ret, "start trans");
    } while (0);

    int32_t startVal = cliIdx * lockNum;
    TEST_INFO("cliIdx = %d, startVal = %d, endVal = %d", cliIdx, startVal, startVal + lockNum);
    for (int32_t i = startVal; ret == GMERR_OK && i < lockNum + startVal; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, gLabelName, GMC_OPERATION_INSERT);
        CHECK_AND_BREAK(ret, "open vertex label");
        int32_t F0Value = i;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        int32_t F1Value = i;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        int64_t F2Value = i * 2;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT64, &F2Value, sizeof(F2Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        ret = GmcExecute(stmt);
        CHECK_AND_BREAK(ret, "insert the %dth vertex", i - startVal);
        ret = GtCheckAffectRows(stmt, 1);
        CHECK_AND_BREAK(ret, "check effect rows");
    };

    system("cat signalFile/signal1_true.txt >>a.log");
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat b.log");
    ret = executeCommand(g_command, "true");
    while (ret != GMERR_OK) {
        sleep(1);
        ret = executeCommand(g_command, "true");
    }

    int tmpRet = GmcTransCommit(conn);
    SET_RET_IFERR(ret, tmpRet, "commit trans");

    SET_RET_IFERR(ret, tmpRet, "drop vertex label");
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    conn = NULL;
    stmt = NULL;
    tmpRet = close_epoll_thread();
    SET_RET_IFERR(ret, tmpRet, "close heart epoll trhead");
    g_needCheckWhenSucc = false;
    tmpRet = testEnvClean();
    SET_RET_IFERR(ret, tmpRet, "clean test env");
    return ret;
}

int GtProcessTrans5(void *res)
{
    int32_t cliIdx = 1;
    int32_t lockNum = 4897;

    int ret, semId;
    char vertexLabel_config[] = "{\"max_record_num\":100000, \"isFastReadUncommitted\":0}";
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    do {
        ret = testEnvInit();
        CHECK_AND_BREAK(ret, "init test env");
        ret = create_epoll_thread();
        CHECK_AND_BREAK(ret, "create heart epoll thread");
        ret = testGmcConnect(&conn, &stmt);
        CHECK_AND_BREAK(ret, "connect");
        ret = GmcCreateVertexLabel(stmt, gLabelSchemaJson, vertexLabel_config);
        ret = ret == GMERR_DUPLICATE_TABLE ? GMERR_OK : ret;
        CHECK_AND_BREAK(ret, "create vertex label");

        // 开启事务
        GmcTxConfigT txConfig = {0};
        txConfig.readOnly = false;
        txConfig.transMode = GMC_TRANS_USED_IN_CS;
        txConfig.type = GMC_TX_ISOLATION_COMMITTED;
        ret = GmcTransStart(conn, &txConfig);
        CHECK_AND_BREAK(ret, "start trans");
    } while (0);

    int32_t startVal = cliIdx * lockNum;
    TEST_INFO("cliIdx = %d, startVal = %d, endVal = %d", cliIdx, startVal, startVal + lockNum);
    for (int32_t i = startVal; ret == GMERR_OK && i < lockNum + startVal; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, gLabelName, GMC_OPERATION_INSERT);
        CHECK_AND_BREAK(ret, "open vertex label");
        int32_t F0Value = i;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        int32_t F1Value = i;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        int64_t F2Value = i * 2;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT64, &F2Value, sizeof(F2Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        ret = GmcExecute(stmt);
        CHECK_AND_BREAK(ret, "insert the %dth vertex", i - startVal);
        ret = GtCheckAffectRows(stmt, 1);
        CHECK_AND_BREAK(ret, "check effect rows");
    };

    system("cat signalFile/signal1_true.txt >>a.log");

    AW_FUN_Log(LOG_STEP, "GtProcessDirectRead wait for be kill.");
    while (1) {
        sleep(1);
    }

    return ret;
}

int GtProcessTrans6(void *res)
{
    int32_t cliIdx = 2;
    int32_t lockNum = 4897;

    int ret, semId;
    char vertexLabel_config[] = "{\"max_record_num\":100000, \"isFastReadUncommitted\":0}";
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    do {
        ret = testEnvInit();
        CHECK_AND_BREAK(ret, "init test env");
        ret = create_epoll_thread();
        CHECK_AND_BREAK(ret, "create heart epoll thread");
        ret = testGmcConnect(&conn, &stmt);
        CHECK_AND_BREAK(ret, "connect");
        ret = GmcCreateVertexLabel(stmt, gLabelSchemaJson, vertexLabel_config);
        ret = ret == GMERR_DUPLICATE_TABLE ? GMERR_OK : ret;
        CHECK_AND_BREAK(ret, "create vertex label");

        // 开启事务
        GmcTxConfigT txConfig = {0};
        txConfig.readOnly = false;
        txConfig.transMode = GMC_TRANS_USED_IN_CS;
        txConfig.type = GMC_TX_ISOLATION_COMMITTED;
        ret = GmcTransStart(conn, &txConfig);
        CHECK_AND_BREAK(ret, "start trans");
    } while (0);

    int32_t startVal = cliIdx * lockNum;
    TEST_INFO("cliIdx = %d, startVal = %d, endVal = %d", cliIdx, startVal, startVal + lockNum);
    for (int32_t i = startVal; ret == GMERR_OK && i < lockNum + startVal; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, gLabelName, GMC_OPERATION_INSERT);
        CHECK_AND_BREAK(ret, "open vertex label");
        int32_t F0Value = i;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        int32_t F1Value = i;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        int64_t F2Value = i * 2;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT64, &F2Value, sizeof(F2Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        ret = GmcExecute(stmt);
        CHECK_AND_BREAK(ret, "insert the %dth vertex", i - startVal);
        ret = GtCheckAffectRows(stmt, 1);
        CHECK_AND_BREAK(ret, "check effect rows");
    };

    system("cat signalFile/signal1_true.txt >>a.log");
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat b.log");
    ret = executeCommand(g_command, "true");
    while (ret != GMERR_OK) {
        sleep(1);
        ret = executeCommand(g_command, "true");
    }

    int tmpRet = GmcTransCommit(conn);
    SET_RET_IFERR(ret, tmpRet, "commit trans");

    SET_RET_IFERR(ret, tmpRet, "drop vertex label");
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    conn = NULL;
    stmt = NULL;
    tmpRet = close_epoll_thread();
    SET_RET_IFERR(ret, tmpRet, "close heart epoll trhead");
    g_needCheckWhenSucc = false;
    tmpRet = testEnvClean();
    SET_RET_IFERR(ret, tmpRet, "clean test env");
    return ret;
}

int GtProcessTrans7(void *res)
{
    int32_t cliIdx = 1;
    int32_t lockNum = 10;

    int ret, semId;
    char vertexLabel_config[] = "{\"max_record_num\":100000, \"isFastReadUncommitted\":0}";
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    do {
        ret = testEnvInit();
        CHECK_AND_BREAK(ret, "init test env");
        ret = create_epoll_thread();
        CHECK_AND_BREAK(ret, "create heart epoll thread");
        ret = testGmcConnect(&conn, &stmt);
        CHECK_AND_BREAK(ret, "connect");
        ret = GmcCreateVertexLabel(stmt, gLabelSchemaJson, vertexLabel_config);
        ret = ret == GMERR_DUPLICATE_TABLE ? GMERR_OK : ret;
        CHECK_AND_BREAK(ret, "create vertex label");

        // 开启事务
        GmcTxConfigT txConfig = {0};
        txConfig.readOnly = false;
        txConfig.transMode = GMC_TRANS_USED_IN_CS;
        txConfig.type = GMC_TX_ISOLATION_COMMITTED;
        ret = GmcTransStart(conn, &txConfig);
        CHECK_AND_BREAK(ret, "start trans");
    } while (0);

    int32_t startVal = cliIdx * lockNum;
    TEST_INFO("cliIdx = %d, startVal = %d, endVal = %d", cliIdx, startVal, startVal + lockNum);
    for (int32_t i = startVal; ret == GMERR_OK && i < lockNum + startVal; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, gLabelName, GMC_OPERATION_INSERT);
        CHECK_AND_BREAK(ret, "open vertex label");
        int32_t F0Value = i;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        int32_t F1Value = i;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        int64_t F2Value = i * 2;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT64, &F2Value, sizeof(F2Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        ret = GmcExecute(stmt);
        CHECK_AND_BREAK(ret, "insert the %dth vertex", i - startVal);
        ret = GtCheckAffectRows(stmt, 1);
        CHECK_AND_BREAK(ret, "check effect rows");
    };

    system("cat signalFile/signal1_true.txt >>a.log");

    AW_FUN_Log(LOG_STEP, "GtProcessDirectRead wait for be kill.");
    while (1) {
        sleep(1);
    }

    return ret;
}

int GtProcessTrans8(void *res)
{
    int32_t cliIdx = 2;
    int32_t lockNum = 10;

    int ret, semId;
    char vertexLabel_config[] = "{\"max_record_num\":100000, \"isFastReadUncommitted\":0}";
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    do {
        ret = testEnvInit();
        CHECK_AND_BREAK(ret, "init test env");
        ret = create_epoll_thread();
        CHECK_AND_BREAK(ret, "create heart epoll thread");
        ret = testGmcConnect(&conn, &stmt);
        CHECK_AND_BREAK(ret, "connect");
        ret = GmcCreateVertexLabel(stmt, gLabelSchemaJson, vertexLabel_config);
        ret = ret == GMERR_DUPLICATE_TABLE ? GMERR_OK : ret;
        CHECK_AND_BREAK(ret, "create vertex label");

        // 开启事务
        GmcTxConfigT txConfig = {0};
        txConfig.readOnly = false;
        txConfig.transMode = GMC_TRANS_USED_IN_CS;
        txConfig.type = GMC_TX_ISOLATION_COMMITTED;
        ret = GmcTransStart(conn, &txConfig);
        CHECK_AND_BREAK(ret, "start trans");
    } while (0);

    int32_t startVal = cliIdx * lockNum;
    TEST_INFO("cliIdx = %d, startVal = %d, endVal = %d", cliIdx, startVal, startVal + lockNum);
    for (int32_t i = startVal; ret == GMERR_OK && i < lockNum + startVal; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, gLabelName, GMC_OPERATION_INSERT);
        CHECK_AND_BREAK(ret, "open vertex label");
        int32_t F0Value = i;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        int32_t F1Value = i;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        int64_t F2Value = i * 2;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT64, &F2Value, sizeof(F2Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        ret = GmcExecute(stmt);
        CHECK_AND_BREAK(ret, "insert the %dth vertex", i - startVal);
        ret = GtCheckAffectRows(stmt, 1);
        CHECK_AND_BREAK(ret, "check effect rows");
    };

    system("cat signalFile/signal1_true.txt >>a.log");

    AW_FUN_Log(LOG_STEP, "GtProcessDirectRead wait for be kill.");
    while (1) {
        sleep(1);
    }

    return ret;
}

int GtProcessTrans9(void *res)
{
    int32_t cliIdx = 1;
    int32_t lockNum = 10;

    int ret, semId;
    char vertexLabel_config[] = "{\"max_record_num\":100000, \"isFastReadUncommitted\":0}";
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    do {
        ret = testEnvInit();
        CHECK_AND_BREAK(ret, "init test env");
        ret = create_epoll_thread();
        CHECK_AND_BREAK(ret, "create heart epoll thread");
        ret = testGmcConnect(&conn, &stmt);
        CHECK_AND_BREAK(ret, "connect");
        ret = GmcCreateVertexLabel(stmt, gLabelSchemaJson, vertexLabel_config);
        ret = ret == GMERR_DUPLICATE_TABLE ? GMERR_OK : ret;
        CHECK_AND_BREAK(ret, "create vertex label");

        // 开启事务
        GmcTxConfigT txConfig = {0};
        txConfig.readOnly = false;
        txConfig.transMode = GMC_TRANS_USED_IN_CS;
        txConfig.type = GMC_TX_ISOLATION_COMMITTED;
        ret = GmcTransStart(conn, &txConfig);
        CHECK_AND_BREAK(ret, "start trans");
    } while (0);

    int32_t startVal = cliIdx * lockNum;
    TEST_INFO("cliIdx = %d, startVal = %d, endVal = %d", cliIdx, startVal, startVal + lockNum);
    for (int32_t i = startVal; ret == GMERR_OK && i < lockNum + startVal; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, gLabelName, GMC_OPERATION_INSERT);
        CHECK_AND_BREAK(ret, "open vertex label");
        int32_t F0Value = i;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        int32_t F1Value = i;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        int64_t F2Value = i * 2;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT64, &F2Value, sizeof(F2Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        ret = GmcExecute(stmt);
        CHECK_AND_BREAK(ret, "insert the %dth vertex", i - startVal);
        ret = GtCheckAffectRows(stmt, 1);
        CHECK_AND_BREAK(ret, "check effect rows");
    };

    int tmpRet = GmcTransCommit(conn);
    SET_RET_IFERR(ret, tmpRet, "commit trans");

    SET_RET_IFERR(ret, tmpRet, "drop vertex label");
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    conn = NULL;
    stmt = NULL;
    tmpRet = close_epoll_thread();
    SET_RET_IFERR(ret, tmpRet, "close heart epoll trhead");
    g_needCheckWhenSucc = false;
    tmpRet = testEnvClean();
    SET_RET_IFERR(ret, tmpRet, "clean test env");
    return ret;
}

int GtProcessTrans10(void *res)
{
    int32_t cliIdx = 2;
    int32_t lockNum = 10;

    int ret, semId;
    char vertexLabel_config[] = "{\"max_record_num\":100000, \"isFastReadUncommitted\":0}";
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    do {
        ret = testEnvInit();
        CHECK_AND_BREAK(ret, "init test env");
        ret = create_epoll_thread();
        CHECK_AND_BREAK(ret, "create heart epoll thread");
        ret = testGmcConnect(&conn, &stmt);
        CHECK_AND_BREAK(ret, "connect");
        ret = GmcCreateVertexLabel(stmt, gLabelSchemaJson, vertexLabel_config);
        ret = ret == GMERR_DUPLICATE_TABLE ? GMERR_OK : ret;
        CHECK_AND_BREAK(ret, "create vertex label");

        // 开启事务
        GmcTxConfigT txConfig = {0};
        txConfig.readOnly = false;
        txConfig.transMode = GMC_TRANS_USED_IN_CS;
        txConfig.type = GMC_TX_ISOLATION_COMMITTED;
        ret = GmcTransStart(conn, &txConfig);
        CHECK_AND_BREAK(ret, "start trans");
    } while (0);

    int32_t startVal = cliIdx * lockNum;
    TEST_INFO("cliIdx = %d, startVal = %d, endVal = %d", cliIdx, startVal, startVal + lockNum);
    for (int32_t i = startVal; ret == GMERR_OK && i < lockNum + startVal; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, gLabelName, GMC_OPERATION_INSERT);
        CHECK_AND_BREAK(ret, "open vertex label");
        int32_t F0Value = i;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        int32_t F1Value = i;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        int64_t F2Value = i * 2;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT64, &F2Value, sizeof(F2Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        ret = GmcExecute(stmt);
        CHECK_AND_BREAK(ret, "insert the %dth vertex", i - startVal);
        ret = GtCheckAffectRows(stmt, 1);
        CHECK_AND_BREAK(ret, "check effect rows");
    };

    int tmpRet = GmcTransCommit(conn);
    SET_RET_IFERR(ret, tmpRet, "commit trans");

    SET_RET_IFERR(ret, tmpRet, "drop vertex label");
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    conn = NULL;
    stmt = NULL;
    tmpRet = close_epoll_thread();
    SET_RET_IFERR(ret, tmpRet, "close heart epoll trhead");
    g_needCheckWhenSucc = false;
    tmpRet = testEnvClean();
    SET_RET_IFERR(ret, tmpRet, "clean test env");
    return ret;
}

void *ThreadStartGtProcessTrans(void *args)
{
    system("./ClearRsCliExit5 --gtest_also_run_disabled_tests "
           "--gtest_filter=ClearResWithClientExitAbnormally005_1.DISABLED_GtProcessTrans >Trans.txt");
    return NULL;
}

void *ThreadStartGtProcessTrans2(void *args)
{
    int ret = system("./ClearRsCliExit5 --gtest_also_run_disabled_tests "
           "--gtest_filter=ClearResWithClientExitAbnormally005_1.DISABLED_GtProcessTrans2 >Trans2.txt");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

void *ThreadStartGtProcessTrans3(void *args)
{
    system("./ClearRsCliExit5 --gtest_also_run_disabled_tests "
           "--gtest_filter=ClearResWithClientExitAbnormally005_1.DISABLED_GtProcessTrans3 >Trans3.txt");
    return NULL;
}

void *ThreadStartGtProcessTrans4(void *args)
{
    int ret = system("./ClearRsCliExit5 --gtest_also_run_disabled_tests "
           "--gtest_filter=ClearResWithClientExitAbnormally005_1.DISABLED_GtProcessTrans4 >Trans4.txt");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

void *ThreadStartGtProcessTrans5(void *args)
{
    system("./ClearRsCliExit5 --gtest_also_run_disabled_tests "
           "--gtest_filter=ClearResWithClientExitAbnormally005_1.DISABLED_GtProcessTrans5 >Trans5.txt");
    return NULL;
}

void *ThreadStartGtProcessTrans6(void *args)
{
    int ret = system("./ClearRsCliExit5 --gtest_also_run_disabled_tests "
           "--gtest_filter=ClearResWithClientExitAbnormally005_1.DISABLED_GtProcessTrans6 >Trans6.txt");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

void *ThreadStartGtProcessTrans7(void *args)
{
    system("./ClearRsCliExit5 --gtest_also_run_disabled_tests "
           "--gtest_filter=ClearResWithClientExitAbnormally005_1.DISABLED_GtProcessTrans7 >Trans7.txt");
    return NULL;
}

void *ThreadStartGtProcessTrans8(void *args)
{
    system("./ClearRsCliExit5 --gtest_also_run_disabled_tests "
           "--gtest_filter=ClearResWithClientExitAbnormally005_1.DISABLED_GtProcessTrans8 >Trans8.txt");
    return NULL;
}

void *ThreadStartGtProcessTrans9(void *args)
{
    int ret = system("./ClearRsCliExit5 --gtest_also_run_disabled_tests "
           "--gtest_filter=ClearResWithClientExitAbnormally005_1.DISABLED_GtProcessTrans9 >Trans9.txt");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

void *ThreadStartGtProcessTrans10(void *args)
{
    int ret = system("./ClearRsCliExit5 --gtest_also_run_disabled_tests "
           "--gtest_filter=ClearResWithClientExitAbnormally005_1.DISABLED_GtProcessTrans10 >Trans10.txt");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

// 客户端单线程开启事务并占用1个事务锁 ，异常退出客户端进程，另起客户端进程查询事务资源状态
TEST_F(ClearResWithClientExitAbnormally005, Other_014_043)
{
    const int32_t lockNum = 1;
    int ret, semsId, BeforeLockCnt, BeforeTransCnt, AfterLockCnt, AfterTransCnt;

    // 创建子进程前先查事务资源视图
    BeforeLockCnt = CheckTransLock();
    BeforeTransCnt = CheckTransSlot();

    pthread_t thr_arr[2];
    ret = pthread_create(&thr_arr[0], NULL, ThreadStartGtProcessTrans, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "等待客户端启动");
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat a.log");
    ret = executeCommand(g_command, "true");
    while (ret != GMERR_OK) {
        sleep(1);
        ret = executeCommand(g_command, "true");
    }

    sleep(1);
    ret = GtCheckTransStatus(lockNum + 1, 1);  // 查询锁状态的视图本身开启事务占用了锁，1个行锁和额外1个表的意向锁
    EXPECT_EQ(GMERR_OK, ret);

    char *result = NULL;
#if defined(RUN_INDEPENDENT) || defined(ENV_RTOSV2)
    ret = GtExecSystemCmd(
        &result, "ps -ef |grep gtest_also_run_disabled_tests |grep -v grep |grep -v sh |awk '{print $2}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("ps -ef |grep gtest_also_run_disabled_tests |grep -v grep");
#else
    ret = GtExecSystemCmd(&result, "ps |grep gtest_also* |grep -v grep |grep -v sh |awk '{print $1}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("ps |grep gtest_also* |grep -v grep");
#endif
    int32_t pid = atoi(result);
    free(result);

    ret = kill(pid, SIGKILL);
    EXPECT_EQ(GMERR_OK, ret);

    // 异常退出后和创建子进程前的事务资源一致
    AfterLockCnt = CheckTransLock();
    AfterTransCnt = CheckTransSlot();
    EXPECT_EQ(BeforeLockCnt, AfterLockCnt);
    int memCompactEnable = 0;
    ret = TestGetConfigValueInt("memCompactEnable", &memCompactEnable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (memCompactEnable == 0) {
        EXPECT_EQ(BeforeTransCnt, AfterTransCnt);
    } else {
        EXPECT_EQ(BeforeTransCnt, AfterTransCnt - 1); // memCompactEnable开启会有后台事务
    }

    // 删表后再验证
    system("rm -rf a.log");
    system("./ClearRsCliExit5 --gtest_also_run_disabled_tests "
           "--gtest_filter=ClearResWithClientExitAbnormally005_1.DISABLED_GtProcessToCleanRes >>CleanRes.txt");

    // 另起子进程验证资源状态正常
    BeforeLockCnt = CheckTransLock();
    BeforeTransCnt = CheckTransSlot();

    ret = pthread_create(&thr_arr[1], NULL, ThreadStartGtProcessTrans2, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "等待客户端启动");
    ret = executeCommand(g_command, "true");
    while (ret != GMERR_OK) {
        sleep(1);
        ret = executeCommand(g_command, "true");
    }

    ret = GtCheckTransStatus(lockNum + 1, 1);
    EXPECT_EQ(GMERR_OK, ret);
    system("cat signalFile/signal1_true.txt >>b.log");

    pthread_join(thr_arr[1], NULL);
    AfterLockCnt = CheckTransLock();
    AfterTransCnt = CheckTransSlot();
    EXPECT_EQ(BeforeLockCnt, AfterLockCnt);
    EXPECT_EQ(BeforeTransCnt, AfterTransCnt);
    system("./ClearRsCliExit5 --gtest_also_run_disabled_tests "
           "--gtest_filter=ClearResWithClientExitAbnormally005_1.DISABLED_GtProcessToCleanRes >>CleanRes.txt");
}

// 客户端单线程开启事务并占用1个事务锁，异常退出客户端进程，另起客户端进程查询事务资源状态，循环执行多次
TEST_F(ClearResWithClientExitAbnormally005, Other_014_044)
{
    const int32_t lockNum = 1;
    int ret, semsId, BeforeLockCnt, BeforeTransCnt, AfterLockCnt, AfterTransCnt;
    pthread_t thr_arr[2];
    for (int i = 0; i < MAX_LOOP_NUM; i++) {
        // 创建子进程前先查事务资源视图
        BeforeLockCnt = CheckTransLock();
        BeforeTransCnt = CheckTransSlot();

        ret = pthread_create(&thr_arr[0], NULL, ThreadStartGtProcessTrans, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        AW_FUN_Log(LOG_STEP, "等待客户端启动");
        (void)snprintf(g_command, MAX_CMD_SIZE, "cat a.log");
        ret = executeCommand(g_command, "true");
        while (ret != GMERR_OK) {
            sleep(1);
            ret = executeCommand(g_command, "true");
        }

        ret = GtCheckTransStatus(lockNum + 1, 1);
        EXPECT_EQ(GMERR_OK, ret);

        char *result = NULL;
#if defined(RUN_INDEPENDENT) || defined(ENV_RTOSV2)
        ret = GtExecSystemCmd(
            &result, "ps -ef |grep gtest_also_run_disabled_tests |grep -v grep |grep -v sh |awk '{print $2}'");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        system("ps -ef |grep gtest_also_run_disabled_tests |grep -v grep");
#else
        ret = GtExecSystemCmd(&result, "ps |grep gtest_also* |grep -v grep |grep -v sh |awk '{print $1}'");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        system("ps |grep gtest_also* |grep -v grep");
#endif
        int32_t pid = atoi(result);
        free(result);

        ret = kill(pid, SIGKILL);
        EXPECT_EQ(GMERR_OK, ret);

        // 异常退出后和创建子进程前的事务资源一致
        AfterLockCnt = CheckTransLock();
        AfterTransCnt = CheckTransSlot();
        EXPECT_EQ(BeforeLockCnt, AfterLockCnt);
        int memCompactEnable = 0;
        ret = TestGetConfigValueInt("memCompactEnable", &memCompactEnable);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (memCompactEnable == 0) {
            EXPECT_EQ(BeforeTransCnt, AfterTransCnt);
        } else {
            EXPECT_EQ(BeforeTransCnt, AfterTransCnt - 1);  // memCompactEnable开启会有后台事务
        }

        // 删表后再验证
        system("rm -rf a.log");
        system("./ClearRsCliExit5 --gtest_also_run_disabled_tests "
               "--gtest_filter=ClearResWithClientExitAbnormally005_1.DISABLED_GtProcessToCleanRes >CleanRes.txt");

        // 创建子进程前先查事务资源视图
        BeforeLockCnt = CheckTransLock();
        BeforeTransCnt = CheckTransSlot();

        // 另起子进程验证资源状态正常
        ret = pthread_create(&thr_arr[1], NULL, ThreadStartGtProcessTrans2, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        AW_FUN_Log(LOG_STEP, "等待客户端启动");
        ret = executeCommand(g_command, "true");
        while (ret != GMERR_OK) {
            sleep(1);
            ret = executeCommand(g_command, "true");
        }

        ret = GtCheckTransStatus(lockNum + 1, 1);
        EXPECT_EQ(GMERR_OK, ret);
        system("cat signalFile/signal1_true.txt >>b.log");

        pthread_join(thr_arr[1], NULL);
        // 异常退出后和创建子进程前的事务资源一致
        AfterLockCnt = CheckTransLock();
        AfterTransCnt = CheckTransSlot();
        EXPECT_EQ(BeforeLockCnt, AfterLockCnt);
        EXPECT_EQ(BeforeTransCnt, AfterTransCnt);
        system("./ClearRsCliExit5 --gtest_also_run_disabled_tests "
               "--gtest_filter=ClearResWithClientExitAbnormally005_1.DISABLED_GtProcessToCleanRes >CleanRes.txt");
        system("rm -rf a.log");
        system("rm -rf b.log");
    }
}

// 客户端单线程开启多个事务并占用事务锁达到行锁上限(4897)，异常退出客户端进程，另起客户端进程查询事务资源状态
// FIXME 锁升级为新特性, 待合入转测后验证, 当前占用2048个锁报错锁资源不足(30702)
TEST_F(ClearResWithClientExitAbnormally005, Other_014_045)
{
    const int32_t lockNum = 2048;  // 4087
    int ret, semsId, BeforeLockCnt, BeforeTransCnt, AfterLockCnt, AfterTransCnt;

    // 创建子进程前先查事务资源视图
    BeforeLockCnt = CheckTransLock();
    BeforeTransCnt = CheckTransSlot();

    // 创建子进程并使其异常退出
    pthread_t thr_arr[2];
    ret = pthread_create(&thr_arr[0], NULL, ThreadStartGtProcessTrans3, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "等待客户端启动");
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat a.log");
    ret = executeCommand(g_command, "true");
    while (ret != GMERR_OK) {
        sleep(1);
        ret = executeCommand(g_command, "true");
    }

    ret = GtCheckTransStatus(lockNum + 1, 1);
    EXPECT_EQ(GMERR_OK, ret);

    char *result = NULL;
#if defined(RUN_INDEPENDENT) || defined(ENV_RTOSV2)
    ret = GtExecSystemCmd(
        &result, "ps -ef |grep gtest_also_run_disabled_tests |grep -v grep |grep -v sh |awk '{print $2}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("ps -ef |grep gtest_also_run_disabled_tests |grep -v grep");
#else
    ret = GtExecSystemCmd(&result, "ps |grep gtest_also* |grep -v grep |grep -v sh |awk '{print $1}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("ps |grep gtest_also* |grep -v grep");
#endif
    int32_t pid = atoi(result);
    free(result);

    ret = kill(pid, SIGKILL);
    EXPECT_EQ(GMERR_OK, ret);

    // 异常退出后和创建子进程前的事务资源一致
    AfterLockCnt = CheckTransLock();
    AfterTransCnt = CheckTransSlot();
    int tryCount = 0;
    while (AfterLockCnt != BeforeLockCnt && tryCount < 5) {
        sleep(1);
        AfterLockCnt = CheckTransLock();
        tryCount++;
    }
    tryCount = 0;
    while (AfterTransCnt != BeforeTransCnt && tryCount < 5) {
        sleep(1);
        AfterTransCnt = CheckTransSlot();
        tryCount++;
    }
    EXPECT_EQ(BeforeLockCnt, AfterLockCnt);
    EXPECT_EQ(BeforeTransCnt, AfterTransCnt);

    // 删表后再验证
    system("rm -rf a.log");
    system("./ClearRsCliExit5 --gtest_also_run_disabled_tests "
           "--gtest_filter=ClearResWithClientExitAbnormally005_1.DISABLED_GtProcessToCleanRes >>CleanRes.txt");

    // 创建子进程前先查事务资源视图
    BeforeLockCnt = CheckTransLock();
    BeforeTransCnt = CheckTransSlot();

    // 另起子进程验证资源状态正常
    ret = pthread_create(&thr_arr[1], NULL, ThreadStartGtProcessTrans4, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "等待客户端启动");
    ret = executeCommand(g_command, "true");
    while (ret != GMERR_OK) {
        sleep(1);
        ret = executeCommand(g_command, "true");
    }

    ret = GtCheckTransStatus(lockNum + 1, 1);
    EXPECT_EQ(GMERR_OK, ret);
    system("cat signalFile/signal1_true.txt >>b.log");

    // 异常退出后和创建子进程前的事务资源一致
    pthread_join(thr_arr[1], NULL);
    AfterLockCnt = CheckTransLock();
    AfterTransCnt = CheckTransSlot();
    EXPECT_EQ(BeforeLockCnt, AfterLockCnt);
    EXPECT_EQ(BeforeTransCnt, AfterTransCnt);

    system("./ClearRsCliExit5 --gtest_also_run_disabled_tests "
           "--gtest_filter=ClearResWithClientExitAbnormally005_1.DISABLED_GtProcessToCleanRes >>CleanRes.txt");
}

// 客户端单线程开启多个事务并占用事务锁使锁升级(4897 + 1)，异常退出客户端进程，另起客户端进程查询事务资源状态
// FIXME 锁升级为新特性, 待合入转测后验证
TEST_F(ClearResWithClientExitAbnormally005, Other_014_046)
{
    const int32_t lockNum = 4897;
    int ret, semsId, BeforeLockCnt, BeforeTransCnt, AfterLockCnt, AfterTransCnt;

    // 创建子进程前先查事务资源视图
    BeforeLockCnt = CheckTransLock();
    BeforeTransCnt = CheckTransSlot();

    // 创建子进程并使其异常退出
    pthread_t thr_arr[2];
    ret = pthread_create(&thr_arr[0], NULL, ThreadStartGtProcessTrans5, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "等待客户端启动");
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat a.log");
    ret = executeCommand(g_command, "true");
    while (ret != GMERR_OK) {
        sleep(1);
        ret = executeCommand(g_command, "true");
    }

    ret = GtCheckTransStatus(1, 1);
    EXPECT_EQ(GMERR_OK, ret);

    char *result = NULL;
#if defined(RUN_INDEPENDENT) || defined(ENV_RTOSV2)
    ret = GtExecSystemCmd(
        &result, "ps -ef |grep gtest_also_run_disabled_tests |grep -v grep |grep -v sh |awk '{print $2}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("ps -ef |grep gtest_also_run_disabled_tests |grep -v grep");
#else
    ret = GtExecSystemCmd(&result, "ps |grep gtest_also* |grep -v grep |grep -v sh |awk '{print $1}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("ps |grep gtest_also* |grep -v grep");
#endif
    int32_t pid = atoi(result);
    free(result);

    ret = kill(pid, SIGKILL);
    EXPECT_EQ(GMERR_OK, ret);

    // 异常退出后和创建子进程前的事务资源一致
    AfterLockCnt = CheckTransLock();
    AfterTransCnt = CheckTransSlot();
    int tryCount = 0;
    while (AfterLockCnt != BeforeLockCnt && tryCount < 5) {
        sleep(1);
        AfterLockCnt = CheckTransLock();
        tryCount++;
    }
    tryCount = 0;
    while (AfterTransCnt != BeforeTransCnt && tryCount < 5) {
        sleep(1);
        AfterTransCnt = CheckTransSlot();
        tryCount++;
    }
    EXPECT_EQ(BeforeLockCnt, AfterLockCnt);
    EXPECT_EQ(BeforeTransCnt, AfterTransCnt);

    // 删表后再验证
    system("rm -rf a.log");
    system("./ClearRsCliExit5 --gtest_also_run_disabled_tests "
           "--gtest_filter=ClearResWithClientExitAbnormally005_1.DISABLED_GtProcessToCleanRes >>CleanRes.txt");

    // 创建子进程前先查事务资源视图
    BeforeLockCnt = CheckTransLock();
    BeforeTransCnt = CheckTransSlot();

    // 另起子进程验证资源状态正常
    ret = pthread_create(&thr_arr[1], NULL, ThreadStartGtProcessTrans6, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "等待客户端启动");
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat a.log");
    ret = executeCommand(g_command, "true");
    while (ret != GMERR_OK) {
        sleep(1);
        ret = executeCommand(g_command, "true");
    }

    ret = GtCheckTransStatus(1, 1);
    EXPECT_EQ(GMERR_OK, ret);
    system("cat signalFile/signal1_true.txt >>b.log");

    // 异常退出后和创建子进程前的事务资源一致
    pthread_join(thr_arr[1], NULL);
    AfterLockCnt = CheckTransLock();
    AfterTransCnt = CheckTransSlot();
    EXPECT_EQ(BeforeLockCnt, AfterLockCnt);
    EXPECT_EQ(BeforeTransCnt, AfterTransCnt);
    system("./ClearRsCliExit5 --gtest_also_run_disabled_tests "
           "--gtest_filter=ClearResWithClientExitAbnormally005_1.DISABLED_GtProcessToCleanRes >>CleanRes.txt");
}

// 多客户端并发开启事务，异常退出客户端进程，另起客户端进程查询事务资源状态
TEST_F(ClearResWithClientExitAbnormally005, Other_014_047)
{
    const int32_t processNum = 2;
    const int32_t perCliLockNum = 10;
    int BeforeLockCnt, BeforeTransCnt, AfterLockCnt, AfterTransCnt;

    // 创建子进程前先查事务资源视图
    BeforeLockCnt = CheckTransLock();
    BeforeTransCnt = CheckTransSlot();

    // 创建子进程并使其异常退出
    int ret, semsId;

    pthread_t thr_arr[4];
    ret = pthread_create(&thr_arr[0], NULL, ThreadStartGtProcessTrans7, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thr_arr[1], NULL, ThreadStartGtProcessTrans8, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "等待客户端启动");
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat a.log |wc -l");
    ret = executeCommand(g_command, "2");
    while (ret != GMERR_OK) {
        sleep(1);
        ret = executeCommand(g_command, "2");
    }

    ret = GtCheckTransStatus(processNum * perCliLockNum + 1, processNum);
    EXPECT_EQ(GMERR_OK, ret);

    char *result1 = NULL;
    char *result2 = NULL;
#if defined(RUN_INDEPENDENT) || defined(ENV_RTOSV2)
    ret = GtExecSystemCmd(
        &result1, "ps -ef |grep gtest_also_run_disabled_tests |grep -v grep |grep -v sh |awk NR==1'{print $2}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSystemCmd(
        &result2, "ps -ef |grep gtest_also_run_disabled_tests |grep -v grep |grep -v sh |awk NR==2'{print $2}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("ps -ef |grep gtest_also_run_disabled_tests |grep -v grep");
#else
    ret = GtExecSystemCmd(&result1, "ps |grep gtest_also* |grep -v grep |grep -v sh |awk NR==1'{print $1}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSystemCmd(&result2, "ps |grep gtest_also* |grep -v grep |grep -v sh |awk NR==2'{print $1}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("ps |grep gtest_also* |grep -v grep");
#endif
    int32_t pid1 = atoi(result1);
    int32_t pid2 = atoi(result2);
    free(result1);
    free(result2);

    ret = kill(pid1, SIGKILL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = kill(pid2, SIGKILL);
    EXPECT_EQ(GMERR_OK, ret);

    // 异常退出后和创建子进程前的事务资源一致
    AfterLockCnt = CheckTransLock();
    AfterTransCnt = CheckTransSlot();
    int tryCount = 0;
    while (AfterLockCnt != BeforeLockCnt && tryCount < 5) {
        sleep(1);
        AfterLockCnt = CheckTransLock();
        tryCount++;
    }
    EXPECT_EQ(BeforeLockCnt, AfterLockCnt);
    int memCompactEnable = 0;
    ret = TestGetConfigValueInt("memCompactEnable", &memCompactEnable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (memCompactEnable == 0) {
        EXPECT_EQ(BeforeTransCnt, AfterTransCnt);
    } else {
        EXPECT_EQ(BeforeTransCnt, AfterTransCnt - 1); // memCompactEnable开启会有后台事务
    }

    // 删表后再验证
    system("rm -rf a.log");
    system("./ClearRsCliExit5 --gtest_also_run_disabled_tests "
           "--gtest_filter=ClearResWithClientExitAbnormally005_1.DISABLED_GtProcessToCleanRes >>CleanRes.txt");

    // 创建子进程前先查事务资源视图
    BeforeLockCnt = CheckTransLock();
    BeforeTransCnt = CheckTransSlot();

    // 另起客户端检查latch资源状态
    ret = pthread_create(&thr_arr[2], NULL, ThreadStartGtProcessTrans9, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thr_arr[3], NULL, ThreadStartGtProcessTrans10, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_join(thr_arr[2], NULL);
    pthread_join(thr_arr[3], NULL);

    // 异常退出后和创建子进程前的事务资源一致
    AfterLockCnt = CheckTransLock();
    AfterTransCnt = CheckTransSlot();
    tryCount = 0;
    while (AfterLockCnt != BeforeLockCnt && tryCount < 5) {
        sleep(1);
        AfterLockCnt = CheckTransLock();
        tryCount++;
    }
    EXPECT_EQ(BeforeLockCnt, AfterLockCnt);
    EXPECT_EQ(BeforeTransCnt, AfterTransCnt);
    system("./ClearRsCliExit5 --gtest_also_run_disabled_tests "
           "--gtest_filter=ClearResWithClientExitAbnormally005_1.DISABLED_GtProcessToCleanRes >>CleanRes.txt");
}

/**************************************************** 订阅资源回收 ****************************************************/
int GtProcessSn(void *res)
{
    int32_t subNum = 1;
    int cliIdx = 1;
    int ret, semId;
    GmcConnT *subConn = NULL;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    do {
        ret = testEnvInit();
        CHECK_AND_RETURN(ret, "init test env");
        ret = create_epoll_thread();
        CHECK_AND_BREAK(ret, "create heart epoll thread");
        ret = testGmcConnect(&conn, &stmt);
        CHECK_AND_BREAK(ret, "connect");
        ret = GmcCreateVertexLabel(stmt, gLabelSchemaJson, gLabelConfig);
        ret = (ret == GMERR_DUPLICATE_TABLE ? GMERR_OK : ret);
        CHECK_AND_BREAK(ret, "create vertex label");

        char subConnName[MAX_NAME_LENGTH] = {0};
        ret = snprintf(subConnName, MAX_NAME_LENGTH, "SubConn_%d", cliIdx);
        if (ret <= 0) {
            TEST_ERROR("set conn name failed, ret = %d, %s", ret, strerror(errno));
            ret = FAILED;
            break;
        }
        ret = testSubConnect(&subConn, NULL, 1, g_epoll_reg_info, subConnName, &gSubChanRingLen);
        CHECK_AND_BREAK(ret, "create sub connection");
    } while (0);

    // 订阅名称使用 %s 占位, 在循环内设置有效名称
    const char *preSubInfo =
        R"({
            "name":"%s",
            "label_name":"VertexLabel",
            "type":"before_commit",
            "events":
                [
                    {"type":"insert", "msgTypes": ["new object", "key"]},
                    {"type":"update", "msgTypes": ["new object", "old object"]},
                    {"type":"delete", "msgTypes": ["old object", "key"]},
                    {"type":"replace", "msgTypes":["new object", "old object"]}
                ],
            "is_path":false,
            "retry":false,
            "persist":false,
            "constraint":
                {
                    "operator_type":"or",
                    "conditions":
                        [
                            {
                                "property": "F2"
                            }
                        ]
                }
        })";

    for (int i = 0; ret == GMERR_OK && i < subNum; i++) {
        char subName[MAX_NAME_LENGTH] = {0};
        ret = snprintf(subName, MAX_NAME_LENGTH, "SubVertexLabel_%d_%d", cliIdx, i);
        if (ret <= 0) {
            TEST_ERROR("set sub name failed, ret = %d, %s", ret, strerror(errno));
            ret = FAILED;
            break;
        }

        char subInfo[1024] = {0};
        ret = snprintf(subInfo, sizeof(subInfo), preSubInfo, subName);
        if (ret <= 0) {
            TEST_ERROR("set sub info json failed, ret = %d, %s", ret, strerror(errno));
            ret = FAILED;
            break;
        }
        // 当前测试场景下，异常退出会删除订阅关系，无需手动取消
        GmcSubConfigT tmp_subInfo;
        tmp_subInfo.subsName = subName;
        tmp_subInfo.configJson = subInfo;
        ret = GmcSubscribe(stmt, &tmp_subInfo, subConn, GtSnCallback, &gSnUserData);
        CHECK_AND_BREAK(ret, "subscribe， i = %d", i);
    }

    gIsSnCallbackWait = true;
    int32_t dataNum = 10;
    int32_t startVal = cliIdx * dataNum;
    TEST_INFO("cliIdx = %d, startVal = %d, endVal = %d", cliIdx, startVal, startVal + dataNum);
    for (int32_t i = startVal; ret == GMERR_OK && i < dataNum + startVal; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, gLabelName, GMC_OPERATION_INSERT);
        CHECK_AND_BREAK(ret, "open vertex label");
        ret = GtSetInsertProperty(stmt, i, i, i * 2);
        CHECK_AND_BREAK(ret, "set property");
        ret = GmcExecute(stmt);
        CHECK_AND_BREAK(ret, "insert the %dth vertex", i);
        ret = GtCheckAffectRows(stmt, 1);
        CHECK_AND_BREAK(ret, "check effect rows failed");
    }

    system("cat signalFile/signal1_true.txt >>a.log");
    AW_FUN_Log(LOG_STEP, "GtProcessSn wait for be kill.");
    while (1) {
        sleep(1);
    }

    return ret;
}

int GtProcessSn2(void *res)
{
    int32_t subNum = 1;
    int cliIdx = 1;

    int ret, semId;
    GmcConnT *subConn = NULL;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    do {
        ret = testEnvInit();
        CHECK_AND_RETURN(ret, "init test env");
        ret = create_epoll_thread();
        CHECK_AND_BREAK(ret, "create heart epoll thread");
        ret = testGmcConnect(&conn, &stmt);
        CHECK_AND_BREAK(ret, "connect");
        ret = GmcCreateVertexLabel(stmt, gLabelSchemaJson, gLabelConfig);
        ret = (ret == GMERR_DUPLICATE_TABLE ? GMERR_OK : ret);
        CHECK_AND_BREAK(ret, "create vertex label");

        char subConnName[MAX_NAME_LENGTH] = {0};
        ret = snprintf(subConnName, MAX_NAME_LENGTH, "SubConn_%d", cliIdx);
        if (ret <= 0) {
            TEST_ERROR("set conn name failed, ret = %d, %s", ret, strerror(errno));
            ret = FAILED;
            break;
        }
        ret = testSubConnect(&subConn, NULL, 1, g_epoll_reg_info, subConnName, &gSubChanRingLen);
        CHECK_AND_BREAK(ret, "create sub connection");
    } while (0);

    // 订阅名称使用 %s 占位, 在循环内设置有效名称
    const char *preSubInfo =
        R"({
            "name":"%s",
            "label_name":"VertexLabel",
            "type":"before_commit",
            "events":
                [
                    {"type":"insert", "msgTypes": ["new object", "key"]},
                    {"type":"update", "msgTypes": ["new object", "old object"]},
                    {"type":"delete", "msgTypes": ["old object", "key"]},
                    {"type":"replace", "msgTypes":["new object", "old object"]}
                ],
            "is_path":false,
            "retry":false,
            "persist":false,
            "constraint":
                {
                    "operator_type":"or",
                    "conditions":
                        [
                            {
                                "property": "F2"
                            }
                        ]
                }
        })";

    for (int i = 0; ret == GMERR_OK && i < subNum; i++) {
        char subName[MAX_NAME_LENGTH] = {0};
        ret = snprintf(subName, MAX_NAME_LENGTH, "SubVertexLabel_%d_%d", cliIdx, i);
        if (ret <= 0) {
            TEST_ERROR("set sub name failed, ret = %d, %s", ret, strerror(errno));
            ret = FAILED;
            break;
        }

        char subInfo[1024] = {0};
        ret = snprintf(subInfo, sizeof(subInfo), preSubInfo, subName);
        if (ret <= 0) {
            TEST_ERROR("set sub info json failed, ret = %d, %s", ret, strerror(errno));
            ret = FAILED;
            break;
        }
        // 当前测试场景下，异常退出会删除订阅关系，无需手动取消
        GmcSubConfigT tmp_subInfo;
        tmp_subInfo.subsName = subName;
        tmp_subInfo.configJson = subInfo;
        ret = GmcSubscribe(stmt, &tmp_subInfo, subConn, GtSnCallback, &gSnUserData);
        CHECK_AND_BREAK(ret, "subscribe， i = %d", i);
    }

    system("cat signalFile/signal1_true.txt >>a.log");
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat b.log");
    ret = executeCommand(g_command, "true");
    while (ret != GMERR_OK) {
        sleep(1);
        ret = executeCommand(g_command, "true");
    }

    gIsSnCallbackWait = true;
    int32_t dataNum = 10;
    int32_t startVal = cliIdx * dataNum;
    TEST_INFO("cliIdx = %d, startVal = %d, endVal = %d", cliIdx, startVal, startVal + dataNum);
    for (int32_t i = startVal; ret == GMERR_OK && i < dataNum + startVal; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, gLabelName, GMC_OPERATION_INSERT);
        CHECK_AND_BREAK(ret, "open vertex label");
        ret = GtSetInsertProperty(stmt, i, i, i * 2);
        CHECK_AND_BREAK(ret, "set property");
        ret = GmcExecute(stmt);
        CHECK_AND_BREAK(ret, "insert the %dth vertex", i);
        ret = GtCheckAffectRows(stmt, 1);
        CHECK_AND_BREAK(ret, "check effect rows failed");
    }

    int tmpRet;

    gIsSnCallbackWait = false;
    for (int i = 0; i < subNum; i++) {
        char subName[MAX_NAME_LENGTH] = {0};
        tmpRet = snprintf(subName, MAX_NAME_LENGTH, "SubVertexLabel_%d_%d", cliIdx, i);
        if (tmpRet <= 0) {
            TEST_ERROR("set sub name failed, ret = %d, %s", tmpRet, strerror(errno));
            ret = FAILED;
            break;
        }
        tmpRet = GmcUnSubscribe(stmt, subName);
        SET_RET_IFERR(ret, tmpRet, "unsubscribe, subName = %s", subName);
    }

    if (subConn != NULL) {
        tmpRet = testSubDisConnect(subConn);
        SET_RET_IFERR(ret, tmpRet, "sub disconect");
        subConn = NULL;
    }
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    conn = NULL;
    stmt = NULL;
    tmpRet = close_epoll_thread();
    SET_RET_IFERR(ret, tmpRet, "close heart epoll thread");
    g_needCheckWhenSucc = false;
    tmpRet = testEnvClean();
    SET_RET_IFERR(ret, tmpRet, "clean test env");
    return ret;
}

int GtProcessSn3(void *res)
{
    int cliIdx = 1;

    int ret, semId;
    uint32_t existSubsNum = 0;
    ret = testGetSubsNum(&existSubsNum);
    EXPECT_EQ(GMERR_OK, ret);
    const int32_t subNum = 1020 - existSubsNum;
    GmcConnT *subConn = NULL;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    do {
        ret = testEnvInit();
        CHECK_AND_RETURN(ret, "init test env");
        ret = create_epoll_thread();
        CHECK_AND_BREAK(ret, "create heart epoll thread");
        ret = testGmcConnect(&conn, &stmt);
        CHECK_AND_BREAK(ret, "connect");
        ret = GmcCreateVertexLabel(stmt, gLabelSchemaJson, gLabelConfig);
        ret = (ret == GMERR_DUPLICATE_TABLE ? GMERR_OK : ret);
        CHECK_AND_BREAK(ret, "create vertex label");

        char subConnName[MAX_NAME_LENGTH] = {0};
        ret = snprintf(subConnName, MAX_NAME_LENGTH, "SubConn_%d", cliIdx);
        if (ret <= 0) {
            TEST_ERROR("set conn name failed, ret = %d, %s", ret, strerror(errno));
            ret = FAILED;
            break;
        }
        ret = testSubConnect(&subConn, NULL, 1, g_epoll_reg_info, subConnName, &gSubChanRingLen);
        CHECK_AND_BREAK(ret, "create sub connection");
    } while (0);

    // 订阅名称使用 %s 占位, 在循环内设置有效名称
    const char *preSubInfo =
        R"({
            "name":"%s",
            "label_name":"VertexLabel",
            "type":"before_commit",
            "events":
                [
                    {"type":"insert", "msgTypes": ["new object", "key"]},
                    {"type":"update", "msgTypes": ["new object", "old object"]},
                    {"type":"delete", "msgTypes": ["old object", "key"]},
                    {"type":"replace", "msgTypes":["new object", "old object"]}
                ],
            "is_path":false,
            "retry":false,
            "persist":false,
            "constraint":
                {
                    "operator_type":"or",
                    "conditions":
                        [
                            {
                                "property": "F2"
                            }
                        ]
                }
        })";

    for (int i = 0; ret == GMERR_OK && i < subNum; i++) {
        char subName[MAX_NAME_LENGTH] = {0};
        ret = snprintf(subName, MAX_NAME_LENGTH, "SubVertexLabel_%d_%d", cliIdx, i);
        if (ret <= 0) {
            TEST_ERROR("set sub name failed, ret = %d, %s", ret, strerror(errno));
            ret = FAILED;
            break;
        }

        char subInfo[1024] = {0};
        ret = snprintf(subInfo, sizeof(subInfo), preSubInfo, subName);
        if (ret <= 0) {
            TEST_ERROR("set sub info json failed, ret = %d, %s", ret, strerror(errno));
            ret = FAILED;
            break;
        }
        // 当前测试场景下，异常退出会删除订阅关系，无需手动取消
        GmcSubConfigT tmp_subInfo;
        tmp_subInfo.subsName = subName;
        tmp_subInfo.configJson = subInfo;
        ret = GmcSubscribe(stmt, &tmp_subInfo, subConn, GtSnCallback, &gSnUserData);
        CHECK_AND_BREAK(ret, "subscribe， i = %d", i);
    }

    gIsSnCallbackWait = true;
    int32_t dataNum = 10;
    int32_t startVal = cliIdx * dataNum;
    TEST_INFO("cliIdx = %d, startVal = %d, endVal = %d", cliIdx, startVal, startVal + dataNum);
    for (int32_t i = startVal; ret == GMERR_OK && i < dataNum + startVal; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, gLabelName, GMC_OPERATION_INSERT);
        CHECK_AND_BREAK(ret, "open vertex label");
        ret = GtSetInsertProperty(stmt, i, i, i * 2);
        CHECK_AND_BREAK(ret, "set property");
        ret = GmcExecute(stmt);
        CHECK_AND_BREAK(ret, "insert the %dth vertex", i);
        ret = GtCheckAffectRows(stmt, 1);
        CHECK_AND_BREAK(ret, "check effect rows failed");
    }

    system("cat signalFile/signal1_true.txt >>a.log");

    AW_FUN_Log(LOG_STEP, "GtProcessSn3 wait for be kill.");
    while (1) {
        sleep(1);
    }

    return ret;
}

int GtProcessSn4(void *res)
{
    int cliIdx = 1;

    int ret, semId;
    uint32_t existSubsNum = 0;
    ret = testGetSubsNum(&existSubsNum);
    EXPECT_EQ(GMERR_OK, ret);
    const int32_t subNum = 1020 - existSubsNum;

    GmcConnT *subConn = NULL;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    do {
        ret = testEnvInit();
        CHECK_AND_RETURN(ret, "init test env");
        ret = create_epoll_thread();
        CHECK_AND_BREAK(ret, "create heart epoll thread");
        ret = testGmcConnect(&conn, &stmt);
        CHECK_AND_BREAK(ret, "connect");
        ret = GmcCreateVertexLabel(stmt, gLabelSchemaJson, gLabelConfig);
        ret = (ret == GMERR_DUPLICATE_TABLE ? GMERR_OK : ret);
        CHECK_AND_BREAK(ret, "create vertex label");

        char subConnName[MAX_NAME_LENGTH] = {0};
        ret = snprintf(subConnName, MAX_NAME_LENGTH, "SubConn_%d", cliIdx);
        if (ret <= 0) {
            TEST_ERROR("set conn name failed, ret = %d, %s", ret, strerror(errno));
            ret = FAILED;
            break;
        }
        ret = testSubConnect(&subConn, NULL, 1, g_epoll_reg_info, subConnName, &gSubChanRingLen);
        CHECK_AND_BREAK(ret, "create sub connection");
    } while (0);

    // 订阅名称使用 %s 占位, 在循环内设置有效名称
    const char *preSubInfo =
        R"({
            "name":"%s",
            "label_name":"VertexLabel",
            "type":"before_commit",
            "events":
                [
                    {"type":"insert", "msgTypes": ["new object", "key"]},
                    {"type":"update", "msgTypes": ["new object", "old object"]},
                    {"type":"delete", "msgTypes": ["old object", "key"]},
                    {"type":"replace", "msgTypes":["new object", "old object"]}
                ],
            "is_path":false,
            "retry":false,
            "persist":false,
            "constraint":
                {
                    "operator_type":"or",
                    "conditions":
                        [
                            {
                                "property": "F2"
                            }
                        ]
                }
        })";

    for (int i = 0; ret == GMERR_OK && i < subNum; i++) {
        char subName[MAX_NAME_LENGTH] = {0};
        ret = snprintf(subName, MAX_NAME_LENGTH, "SubVertexLabel_%d_%d", cliIdx, i);
        if (ret <= 0) {
            TEST_ERROR("set sub name failed, ret = %d, %s", ret, strerror(errno));
            ret = FAILED;
            break;
        }

        char subInfo[1024] = {0};
        ret = snprintf(subInfo, sizeof(subInfo), preSubInfo, subName);
        if (ret <= 0) {
            TEST_ERROR("set sub info json failed, ret = %d, %s", ret, strerror(errno));
            ret = FAILED;
            break;
        }
        // 当前测试场景下，异常退出会删除订阅关系，无需手动取消
        GmcSubConfigT tmp_subInfo;
        tmp_subInfo.subsName = subName;
        tmp_subInfo.configJson = subInfo;
        ret = GmcSubscribe(stmt, &tmp_subInfo, subConn, GtSnCallback, &gSnUserData);
        CHECK_AND_BREAK(ret, "subscribe， i = %d", i);
    }

    gIsSnCallbackWait = true;
    int32_t dataNum = 10;
    int32_t startVal = cliIdx * dataNum;
    TEST_INFO("cliIdx = %d, startVal = %d, endVal = %d", cliIdx, startVal, startVal + dataNum);
    for (int32_t i = startVal; ret == GMERR_OK && i < dataNum + startVal; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, gLabelName, GMC_OPERATION_INSERT);
        CHECK_AND_BREAK(ret, "open vertex label");
        ret = GtSetInsertProperty(stmt, i, i, i * 2);
        CHECK_AND_BREAK(ret, "set property");
        ret = GmcExecute(stmt);
        CHECK_AND_BREAK(ret, "insert the %dth vertex", i);
        ret = GtCheckAffectRows(stmt, 1);
        CHECK_AND_BREAK(ret, "check effect rows failed");
    }

    system("cat signalFile/signal1_true.txt >>a.log");
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat b.log");
    ret = executeCommand(g_command, "true");
    while (ret != GMERR_OK) {
        sleep(1);
        ret = executeCommand(g_command, "true");
    }

    int tmpRet;
    gIsSnCallbackWait = false;
    for (int i = 0; i < subNum; i++) {
        char subName[MAX_NAME_LENGTH] = {0};
        tmpRet = snprintf(subName, MAX_NAME_LENGTH, "SubVertexLabel_%d_%d", cliIdx, i);
        if (tmpRet <= 0) {
            TEST_ERROR("set sub name failed, ret = %d, %s", tmpRet, strerror(errno));
            ret = FAILED;
            break;
        }
        tmpRet = GmcUnSubscribe(stmt, subName);
        SET_RET_IFERR(ret, tmpRet, "unsubscribe, subName = %s", subName);
    }

    if (subConn != NULL) {
        tmpRet = testSubDisConnect(subConn);
        SET_RET_IFERR(ret, tmpRet, "sub disconect");
        subConn = NULL;
    }
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    conn = NULL;
    stmt = NULL;
    tmpRet = close_epoll_thread();
    SET_RET_IFERR(ret, tmpRet, "close heart epoll thread");
    g_needCheckWhenSucc = false;
    tmpRet = testEnvClean();
    SET_RET_IFERR(ret, tmpRet, "clean test env");
    return ret;
}

int GtProcessSn5(void *res)
{
    int cliIdx = 1;
    int ret, semId;
    const int32_t subNum = 2;

    GmcConnT *subConn = NULL;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    do {
        ret = testEnvInit();
        CHECK_AND_RETURN(ret, "init test env");
        ret = create_epoll_thread();
        CHECK_AND_BREAK(ret, "create heart epoll thread");
        ret = testGmcConnect(&conn, &stmt);
        CHECK_AND_BREAK(ret, "connect");
        ret = GmcCreateVertexLabel(stmt, gLabelSchemaJson, gLabelConfig);
        ret = (ret == GMERR_DUPLICATE_TABLE ? GMERR_OK : ret);
        CHECK_AND_BREAK(ret, "create vertex label");

        char subConnName[MAX_NAME_LENGTH] = {0};
        ret = snprintf(subConnName, MAX_NAME_LENGTH, "SubConn_%d", cliIdx);
        if (ret <= 0) {
            TEST_ERROR("set conn name failed, ret = %d, %s", ret, strerror(errno));
            ret = FAILED;
            break;
        }
        ret = testSubConnect(&subConn, NULL, 1, g_epoll_reg_info, subConnName, &gSubChanRingLen);
        CHECK_AND_BREAK(ret, "create sub connection");
    } while (0);

    // 订阅名称使用 %s 占位, 在循环内设置有效名称
    const char *preSubInfo =
        R"({
            "name":"%s",
            "label_name":"VertexLabel",
            "type":"before_commit",
            "events":
                [
                    {"type":"insert", "msgTypes": ["new object", "key"]},
                    {"type":"update", "msgTypes": ["new object", "old object"]},
                    {"type":"delete", "msgTypes": ["old object", "key"]},
                    {"type":"replace", "msgTypes":["new object", "old object"]}
                ],
            "is_path":false,
            "retry":false,
            "persist":false,
            "constraint":
                {
                    "operator_type":"or",
                    "conditions":
                        [
                            {
                                "property": "F2"
                            }
                        ]
                }
        })";

    for (int i = 0; ret == GMERR_OK && i < subNum; i++) {
        char subName[MAX_NAME_LENGTH] = {0};
        ret = snprintf(subName, MAX_NAME_LENGTH, "SubVertexLabel_%d_%d", cliIdx, i);
        if (ret <= 0) {
            TEST_ERROR("set sub name failed, ret = %d, %s", ret, strerror(errno));
            ret = FAILED;
            break;
        }

        char subInfo[1024] = {0};
        ret = snprintf(subInfo, sizeof(subInfo), preSubInfo, subName);
        if (ret <= 0) {
            TEST_ERROR("set sub info json failed, ret = %d, %s", ret, strerror(errno));
            ret = FAILED;
            break;
        }
        // 当前测试场景下，异常退出会删除订阅关系，无需手动取消
        GmcSubConfigT tmp_subInfo;
        tmp_subInfo.subsName = subName;
        tmp_subInfo.configJson = subInfo;
        ret = GmcSubscribe(stmt, &tmp_subInfo, subConn, GtSnCallback, &gSnUserData);
        CHECK_AND_BREAK(ret, "subscribe， i = %d", i);
    }

    gIsSnCallbackWait = true;
    int32_t dataNum = 10;
    int32_t startVal = cliIdx * dataNum;
    TEST_INFO("cliIdx = %d, startVal = %d, endVal = %d", cliIdx, startVal, startVal + dataNum);
    for (int32_t i = startVal; ret == GMERR_OK && i < dataNum + startVal; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, gLabelName, GMC_OPERATION_INSERT);
        CHECK_AND_BREAK(ret, "open vertex label");
        ret = GtSetInsertProperty(stmt, i, i, i * 2);
        CHECK_AND_BREAK(ret, "set property");
        ret = GmcExecute(stmt);
        CHECK_AND_BREAK(ret, "insert the %dth vertex", i);
        ret = GtCheckAffectRows(stmt, 1);
        CHECK_AND_BREAK(ret, "check effect rows failed");
    }

    system("cat signalFile/signal1_true.txt >>a.log");
    AW_FUN_Log(LOG_STEP, "GtProcessSn5 wait for be kill.");
    while (1) {
        sleep(1);
    }

    return ret;
}

int GtProcessSn6(void *res)
{
    int cliIdx = 2;
    int ret, semId;
    const int32_t subNum = 2;

    GmcConnT *subConn = NULL;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    do {
        ret = testEnvInit();
        CHECK_AND_RETURN(ret, "init test env");
        ret = create_epoll_thread();
        CHECK_AND_BREAK(ret, "create heart epoll thread");
        ret = testGmcConnect(&conn, &stmt);
        CHECK_AND_BREAK(ret, "connect");
        ret = GmcCreateVertexLabel(stmt, gLabelSchemaJson, gLabelConfig);
        ret = (ret == GMERR_DUPLICATE_TABLE ? GMERR_OK : ret);
        CHECK_AND_BREAK(ret, "create vertex label");

        char subConnName[MAX_NAME_LENGTH] = {0};
        ret = snprintf(subConnName, MAX_NAME_LENGTH, "SubConn_%d", cliIdx);
        if (ret <= 0) {
            TEST_ERROR("set conn name failed, ret = %d, %s", ret, strerror(errno));
            ret = FAILED;
            break;
        }
        ret = testSubConnect(&subConn, NULL, 1, g_epoll_reg_info, subConnName, &gSubChanRingLen);
        CHECK_AND_BREAK(ret, "create sub connection");
    } while (0);

    // 订阅名称使用 %s 占位, 在循环内设置有效名称
    const char *preSubInfo =
        R"({
            "name":"%s",
            "label_name":"VertexLabel",
            "type":"before_commit",
            "events":
                [
                    {"type":"insert", "msgTypes": ["new object", "key"]},
                    {"type":"update", "msgTypes": ["new object", "old object"]},
                    {"type":"delete", "msgTypes": ["old object", "key"]},
                    {"type":"replace", "msgTypes":["new object", "old object"]}
                ],
            "is_path":false,
            "retry":false,
            "persist":false,
            "constraint":
                {
                    "operator_type":"or",
                    "conditions":
                        [
                            {
                                "property": "F2"
                            }
                        ]
                }
        })";

    for (int i = 0; ret == GMERR_OK && i < subNum; i++) {
        char subName[MAX_NAME_LENGTH] = {0};
        ret = snprintf(subName, MAX_NAME_LENGTH, "SubVertexLabel_%d_%d", cliIdx, i);
        if (ret <= 0) {
            TEST_ERROR("set sub name failed, ret = %d, %s", ret, strerror(errno));
            ret = FAILED;
            break;
        }

        char subInfo[1024] = {0};
        ret = snprintf(subInfo, sizeof(subInfo), preSubInfo, subName);
        if (ret <= 0) {
            TEST_ERROR("set sub info json failed, ret = %d, %s", ret, strerror(errno));
            ret = FAILED;
            break;
        }
        // 当前测试场景下，异常退出会删除订阅关系，无需手动取消
        GmcSubConfigT tmp_subInfo;
        tmp_subInfo.subsName = subName;
        tmp_subInfo.configJson = subInfo;
        ret = GmcSubscribe(stmt, &tmp_subInfo, subConn, GtSnCallback, &gSnUserData);
        CHECK_AND_BREAK(ret, "subscribe， i = %d", i);
    }

    gIsSnCallbackWait = true;
    int32_t dataNum = 10;
    int32_t startVal = cliIdx * dataNum;
    TEST_INFO("cliIdx = %d, startVal = %d, endVal = %d", cliIdx, startVal, startVal + dataNum);
    for (int32_t i = startVal; ret == GMERR_OK && i < dataNum + startVal; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, gLabelName, GMC_OPERATION_INSERT);
        CHECK_AND_BREAK(ret, "open vertex label");
        ret = GtSetInsertProperty(stmt, i, i, i * 2);
        CHECK_AND_BREAK(ret, "set property");
        ret = GmcExecute(stmt);
        CHECK_AND_BREAK(ret, "insert the %dth vertex", i);
        ret = GtCheckAffectRows(stmt, 1);
        CHECK_AND_BREAK(ret, "check effect rows failed");
    }

    system("cat signalFile/signal1_true.txt >>a.log");
    AW_FUN_Log(LOG_STEP, "GtProcessSn6 wait for be kill.");
    while (1) {
        sleep(1);
    }

    return ret;
}

int GtProcessSn7(void *res)
{
    int cliIdx = 1;
    int ret, semId;
    const int32_t subNum = 2;

    GmcConnT *subConn = NULL;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    do {
        ret = testEnvInit();
        CHECK_AND_RETURN(ret, "init test env");
        ret = create_epoll_thread();
        CHECK_AND_BREAK(ret, "create heart epoll thread");
        ret = testGmcConnect(&conn, &stmt);
        CHECK_AND_BREAK(ret, "connect");
        ret = GmcCreateVertexLabel(stmt, gLabelSchemaJson, gLabelConfig);
        ret = (ret == GMERR_DUPLICATE_TABLE ? GMERR_OK : ret);
        CHECK_AND_BREAK(ret, "create vertex label");

        char subConnName[MAX_NAME_LENGTH] = {0};
        ret = snprintf(subConnName, MAX_NAME_LENGTH, "SubConn_%d", cliIdx);
        if (ret <= 0) {
            TEST_ERROR("set conn name failed, ret = %d, %s", ret, strerror(errno));
            ret = FAILED;
            break;
        }
        ret = testSubConnect(&subConn, NULL, 1, g_epoll_reg_info, subConnName, &gSubChanRingLen);
        CHECK_AND_BREAK(ret, "create sub connection");
    } while (0);

    // 订阅名称使用 %s 占位, 在循环内设置有效名称
    const char *preSubInfo =
        R"({
            "name":"%s",
            "label_name":"VertexLabel",
            "type":"before_commit",
            "events":
                [
                    {"type":"insert", "msgTypes": ["new object", "key"]},
                    {"type":"update", "msgTypes": ["new object", "old object"]},
                    {"type":"delete", "msgTypes": ["old object", "key"]},
                    {"type":"replace", "msgTypes":["new object", "old object"]}
                ],
            "is_path":false,
            "retry":false,
            "persist":false,
            "constraint":
                {
                    "operator_type":"or",
                    "conditions":
                        [
                            {
                                "property": "F2"
                            }
                        ]
                }
        })";

    for (int i = 0; ret == GMERR_OK && i < subNum; i++) {
        char subName[MAX_NAME_LENGTH] = {0};
        ret = snprintf(subName, MAX_NAME_LENGTH, "SubVertexLabel_%d_%d", cliIdx, i);
        if (ret <= 0) {
            TEST_ERROR("set sub name failed, ret = %d, %s", ret, strerror(errno));
            ret = FAILED;
            break;
        }

        char subInfo[1024] = {0};
        ret = snprintf(subInfo, sizeof(subInfo), preSubInfo, subName);
        if (ret <= 0) {
            TEST_ERROR("set sub info json failed, ret = %d, %s", ret, strerror(errno));
            ret = FAILED;
            break;
        }
        // 当前测试场景下，异常退出会删除订阅关系，无需手动取消
        GmcSubConfigT tmp_subInfo;
        tmp_subInfo.subsName = subName;
        tmp_subInfo.configJson = subInfo;
        ret = GmcSubscribe(stmt, &tmp_subInfo, subConn, GtSnCallback, &gSnUserData);
        CHECK_AND_BREAK(ret, "subscribe， i = %d", i);
    }

    gIsSnCallbackWait = true;
    int32_t dataNum = 10;
    int32_t startVal = cliIdx * dataNum;
    TEST_INFO("cliIdx = %d, startVal = %d, endVal = %d", cliIdx, startVal, startVal + dataNum);
    for (int32_t i = startVal; ret == GMERR_OK && i < dataNum + startVal; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, gLabelName, GMC_OPERATION_INSERT);
        CHECK_AND_BREAK(ret, "open vertex label");
        ret = GtSetInsertProperty(stmt, i, i, i * 2);
        CHECK_AND_BREAK(ret, "set property");
        ret = GmcExecute(stmt);
        CHECK_AND_BREAK(ret, "insert the %dth vertex", i);
        ret = GtCheckAffectRows(stmt, 1);
        CHECK_AND_BREAK(ret, "check effect rows failed");
    }

    system("cat signalFile/signal1_true.txt >>a.log");
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat b.log");
    ret = executeCommand(g_command, "true");
    while (ret != GMERR_OK) {
        sleep(1);
        ret = executeCommand(g_command, "true");
    }

    int tmpRet;
    gIsSnCallbackWait = false;
    for (int i = 0; i < subNum; i++) {
        char subName[MAX_NAME_LENGTH] = {0};
        tmpRet = snprintf(subName, MAX_NAME_LENGTH, "SubVertexLabel_%d_%d", cliIdx, i);
        if (tmpRet <= 0) {
            TEST_ERROR("set sub name failed, ret = %d, %s", tmpRet, strerror(errno));
            ret = FAILED;
            break;
        }
        tmpRet = GmcUnSubscribe(stmt, subName);
        SET_RET_IFERR(ret, tmpRet, "unsubscribe, subName = %s", subName);
    }

    if (subConn != NULL) {
        tmpRet = testSubDisConnect(subConn);
        SET_RET_IFERR(ret, tmpRet, "sub disconect");
        subConn = NULL;
    }
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    conn = NULL;
    stmt = NULL;
    tmpRet = close_epoll_thread();
    SET_RET_IFERR(ret, tmpRet, "close heart epoll thread");
    g_needCheckWhenSucc = false;
    tmpRet = testEnvClean();
    SET_RET_IFERR(ret, tmpRet, "clean test env");
    return ret;
}

int GtProcessSn8(void *res)
{
    int cliIdx = 2;
    int ret, semId;
    const int32_t subNum = 2;

    GmcConnT *subConn = NULL;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    do {
        ret = testEnvInit();
        CHECK_AND_RETURN(ret, "init test env");
        ret = create_epoll_thread();
        CHECK_AND_BREAK(ret, "create heart epoll thread");
        ret = testGmcConnect(&conn, &stmt);
        CHECK_AND_BREAK(ret, "connect");
        ret = GmcCreateVertexLabel(stmt, gLabelSchemaJson, gLabelConfig);
        ret = (ret == GMERR_DUPLICATE_TABLE ? GMERR_OK : ret);
        CHECK_AND_BREAK(ret, "create vertex label");

        char subConnName[MAX_NAME_LENGTH] = {0};
        ret = snprintf(subConnName, MAX_NAME_LENGTH, "SubConn_%d", cliIdx);
        if (ret <= 0) {
            TEST_ERROR("set conn name failed, ret = %d, %s", ret, strerror(errno));
            ret = FAILED;
            break;
        }
        ret = testSubConnect(&subConn, NULL, 1, g_epoll_reg_info, subConnName, &gSubChanRingLen);
        CHECK_AND_BREAK(ret, "create sub connection");
    } while (0);

    // 订阅名称使用 %s 占位, 在循环内设置有效名称
    const char *preSubInfo =
        R"({
            "name":"%s",
            "label_name":"VertexLabel",
            "type":"before_commit",
            "events":
                [
                    {"type":"insert", "msgTypes": ["new object", "key"]},
                    {"type":"update", "msgTypes": ["new object", "old object"]},
                    {"type":"delete", "msgTypes": ["old object", "key"]},
                    {"type":"replace", "msgTypes":["new object", "old object"]}
                ],
            "is_path":false,
            "retry":false,
            "persist":false,
            "constraint":
                {
                    "operator_type":"or",
                    "conditions":
                        [
                            {
                                "property": "F2"
                            }
                        ]
                }
        })";

    for (int i = 0; ret == GMERR_OK && i < subNum; i++) {
        char subName[MAX_NAME_LENGTH] = {0};
        ret = snprintf(subName, MAX_NAME_LENGTH, "SubVertexLabel_%d_%d", cliIdx, i);
        if (ret <= 0) {
            TEST_ERROR("set sub name failed, ret = %d, %s", ret, strerror(errno));
            ret = FAILED;
            break;
        }

        char subInfo[1024] = {0};
        ret = snprintf(subInfo, sizeof(subInfo), preSubInfo, subName);
        if (ret <= 0) {
            TEST_ERROR("set sub info json failed, ret = %d, %s", ret, strerror(errno));
            ret = FAILED;
            break;
        }
        // 当前测试场景下，异常退出会删除订阅关系，无需手动取消
        GmcSubConfigT tmp_subInfo;
        tmp_subInfo.subsName = subName;
        tmp_subInfo.configJson = subInfo;
        ret = GmcSubscribe(stmt, &tmp_subInfo, subConn, GtSnCallback, &gSnUserData);
        CHECK_AND_BREAK(ret, "subscribe， i = %d", i);
    }

    gIsSnCallbackWait = true;
    int32_t dataNum = 10;
    int32_t startVal = cliIdx * dataNum;
    TEST_INFO("cliIdx = %d, startVal = %d, endVal = %d", cliIdx, startVal, startVal + dataNum);
    for (int32_t i = startVal; ret == GMERR_OK && i < dataNum + startVal; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, gLabelName, GMC_OPERATION_INSERT);
        CHECK_AND_BREAK(ret, "open vertex label");
        ret = GtSetInsertProperty(stmt, i, i, i * 2);
        CHECK_AND_BREAK(ret, "set property");
        ret = GmcExecute(stmt);
        CHECK_AND_BREAK(ret, "insert the %dth vertex", i);
        ret = GtCheckAffectRows(stmt, 1);
        CHECK_AND_BREAK(ret, "check effect rows failed");
    }

    system("cat signalFile/signal1_true.txt >>a.log");
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat b.log");
    ret = executeCommand(g_command, "true");
    while (ret != GMERR_OK) {
        sleep(1);
        ret = executeCommand(g_command, "true");
    }

    int tmpRet;
    gIsSnCallbackWait = false;
    for (int i = 0; i < subNum; i++) {
        char subName[MAX_NAME_LENGTH] = {0};
        tmpRet = snprintf(subName, MAX_NAME_LENGTH, "SubVertexLabel_%d_%d", cliIdx, i);
        if (tmpRet <= 0) {
            TEST_ERROR("set sub name failed, ret = %d, %s", tmpRet, strerror(errno));
            ret = FAILED;
            break;
        }
        tmpRet = GmcUnSubscribe(stmt, subName);
        SET_RET_IFERR(ret, tmpRet, "unsubscribe, subName = %s", subName);
    }

    if (subConn != NULL) {
        tmpRet = testSubDisConnect(subConn);
        SET_RET_IFERR(ret, tmpRet, "sub disconect");
        subConn = NULL;
    }
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    conn = NULL;
    stmt = NULL;
    tmpRet = close_epoll_thread();
    SET_RET_IFERR(ret, tmpRet, "close heart epoll thread");
    g_needCheckWhenSucc = false;
    tmpRet = testEnvClean();
    SET_RET_IFERR(ret, tmpRet, "clean test env");
    return ret;
}

void *ThreadStartGtProcessSn(void *args)
{
    system("./ClearRsCliExit5 --gtest_also_run_disabled_tests "
           "--gtest_filter=ClearResWithClientExitAbnormally005_1.DISABLED_GtProcessSn >Sn.txt");
    return NULL;
}

void *ThreadStartGtProcessSn2(void *args)
{
    int ret = system("./ClearRsCliExit5 --gtest_also_run_disabled_tests "
                     "--gtest_filter=ClearResWithClientExitAbnormally005_1.DISABLED_GtProcessSn2 >Sn2.txt");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

void *ThreadStartGtProcessSn3(void *args)
{
    system("./ClearRsCliExit5 --gtest_also_run_disabled_tests "
           "--gtest_filter=ClearResWithClientExitAbnormally005_1.DISABLED_GtProcessSn3 >Sn3.txt");
    return NULL;
}

void *ThreadStartGtProcessSn4(void *args)
{
    int ret = system("./ClearRsCliExit5 --gtest_also_run_disabled_tests "
                     "--gtest_filter=ClearResWithClientExitAbnormally005_1.DISABLED_GtProcessSn4 >Sn4.txt");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

void *ThreadStartGtProcessSn5(void *args)
{
    system("./ClearRsCliExit5 --gtest_also_run_disabled_tests "
           "--gtest_filter=ClearResWithClientExitAbnormally005_1.DISABLED_GtProcessSn5 >Sn5.txt");
    return NULL;
}

void *ThreadStartGtProcessSn6(void *args)
{
    system("./ClearRsCliExit5 --gtest_also_run_disabled_tests "
           "--gtest_filter=ClearResWithClientExitAbnormally005_1.DISABLED_GtProcessSn6 >Sn6.txt");
    return NULL;
}

void *ThreadStartGtProcessSn7(void *args)
{
    int ret = system("./ClearRsCliExit5 --gtest_also_run_disabled_tests "
                     "--gtest_filter=ClearResWithClientExitAbnormally005_1.DISABLED_GtProcessSn7 >Sn7.txt");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

void *ThreadStartGtProcessSn8(void *args)
{
    int ret = system("./ClearRsCliExit5 --gtest_also_run_disabled_tests "
                     "--gtest_filter=ClearResWithClientExitAbnormally005_1.DISABLED_GtProcessSn8 >Sn8.txt");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

// 客户端单线程创建订阅关系，异常退出客户端进程，另起客户端进程查询订阅推送资源状态
TEST_F(ClearResWithClientExitAbnormally005, Other_014_048)
{
    const int32_t subNum = 1;
    int ret, semsId;
    uint32_t existSubsNum = 0;
    ret = testGetSubsNum(&existSubsNum);
    EXPECT_EQ(GMERR_OK, ret);

    pthread_t thr_arr[2];
    ret = pthread_create(&thr_arr[0], NULL, ThreadStartGtProcessSn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "等待客户端启动");
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat a.log");
    ret = executeCommand(g_command, "true");
    while (ret != GMERR_OK) {
        sleep(1);
        ret = executeCommand(g_command, "true");
    }

    ret = GtCheckSnStatus(existSubsNum + subNum);
    EXPECT_EQ(GMERR_OK, ret);

    char *result = NULL;
#if defined(RUN_INDEPENDENT) || defined(ENV_RTOSV2)
    ret = GtExecSystemCmd(
        &result, "ps -ef |grep gtest_also_run_disabled_tests |grep -v grep |grep -v sh |awk '{print $2}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("ps -ef |grep gtest_also_run_disabled_tests |grep -v grep");
#else
    ret = GtExecSystemCmd(&result, "ps |grep gtest_also* |grep -v grep |grep -v sh |awk '{print $1}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("ps |grep gtest_also* |grep -v grep");
#endif
    int32_t pid = atoi(result);
    free(result);

    ret = kill(pid, SIGKILL);
    EXPECT_EQ(GMERR_OK, ret);

    // HISTORY 正常断链或异常退出后，资源回收存在一定的延时，属于正常现象，参考问题单： DTS2021110514060
    sleep(1);
    ret = GtCheckSnStatus(existSubsNum);
    EXPECT_EQ(GMERR_OK, ret);

    // 删表后再验证
    system("rm -rf a.log");
    system("./ClearRsCliExit5 --gtest_also_run_disabled_tests "
           "--gtest_filter=ClearResWithClientExitAbnormally005_1.DISABLED_GtProcessToCleanRes >>CleanRes.txt");

    // 另起子进程验证资源状态正常
    ret = pthread_create(&thr_arr[1], NULL, ThreadStartGtProcessSn2, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "等待客户端启动");
    ret = executeCommand(g_command, "true");
    while (ret != GMERR_OK) {
        sleep(1);
        ret = executeCommand(g_command, "true");
    }

    ret = GtCheckSnStatus(existSubsNum + subNum);
    EXPECT_EQ(GMERR_OK, ret);
    system("cat signalFile/signal1_true.txt >>b.log");

    pthread_join(thr_arr[1], NULL);
    sleep(1);

    ret = GtCheckSnStatus(existSubsNum);
    EXPECT_EQ(GMERR_OK, ret);

    system("./ClearRsCliExit5 --gtest_also_run_disabled_tests "
           "--gtest_filter=ClearResWithClientExitAbnormally005_1.DISABLED_GtProcessToCleanRes >>CleanRes.txt");
}

// 客户端单线程创建订阅关系，异常退出客户端进程，另起客户端进程查询订阅推送资源状态，循环执行多次
TEST_F(ClearResWithClientExitAbnormally005, Other_014_049)
{
    const int32_t subNum = 1;
    int ret, semsId;
    uint32_t existSubsNum = 0;
    ret = testGetSubsNum(&existSubsNum);
    EXPECT_EQ(GMERR_OK, ret);
    pthread_t thr_arr[2];

    for (int i = 0; i < MAX_LOOP_NUM; i++) {
        ret = pthread_create(&thr_arr[0], NULL, ThreadStartGtProcessSn, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        AW_FUN_Log(LOG_STEP, "等待客户端启动");
        (void)snprintf(g_command, MAX_CMD_SIZE, "cat a.log");
        ret = executeCommand(g_command, "true");
        while (ret != GMERR_OK) {
            sleep(1);
            ret = executeCommand(g_command, "true");
        }

        ret = GtCheckSnStatus(existSubsNum + subNum);
        EXPECT_EQ(GMERR_OK, ret);

        char *result = NULL;
#if defined(RUN_INDEPENDENT) || defined(ENV_RTOSV2)
        ret = GtExecSystemCmd(
            &result, "ps -ef |grep gtest_also_run_disabled_tests |grep -v grep |grep -v sh |awk '{print $2}'");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        system("ps -ef |grep gtest_also_run_disabled_tests |grep -v grep");
#else
        ret = GtExecSystemCmd(&result, "ps |grep gtest_also* |grep -v grep |grep -v sh |awk '{print $1}'");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        system("ps |grep gtest_also* |grep -v grep");
#endif
        int32_t pid = atoi(result);
        free(result);

        ret = kill(pid, SIGKILL);
        EXPECT_EQ(GMERR_OK, ret);

        sleep(1);
        ret = GtCheckSnStatus(existSubsNum);
        EXPECT_EQ(GMERR_OK, ret);

        // 删表后再验证
        system("rm -rf a.log");
        system("./ClearRsCliExit5 --gtest_also_run_disabled_tests "
               "--gtest_filter=ClearResWithClientExitAbnormally005_1.DISABLED_GtProcessToCleanRes >>CleanRes.txt");

        // 另起子进程验证资源状态正常
        ret = pthread_create(&thr_arr[1], NULL, ThreadStartGtProcessSn2, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        AW_FUN_Log(LOG_STEP, "等待客户端启动");
        (void)snprintf(g_command, MAX_CMD_SIZE, "cat a.log");
        ret = executeCommand(g_command, "true");
        while (ret != GMERR_OK) {
            sleep(1);
            ret = executeCommand(g_command, "true");
        }

        ret = GtCheckSnStatus(existSubsNum + subNum);
        EXPECT_EQ(GMERR_OK, ret);
        system("cat signalFile/signal1_true.txt >>b.log");

        pthread_join(thr_arr[1], NULL);
        sleep(1);
        ret = GtCheckSnStatus(existSubsNum);
        EXPECT_EQ(GMERR_OK, ret);

        system("./ClearRsCliExit5 --gtest_also_run_disabled_tests "
               "--gtest_filter=ClearResWithClientExitAbnormally005_1.DISABLED_GtProcessToCleanRes >>CleanRes.txt");
        system("rm -rf a.log");
        system("rm -rf b.log");
    }
}

// 客户端单线程创建订阅关系达到上限 ，异常退出客户端进程，另起客户端进程查询订阅推送资源状态
TEST_F(ClearResWithClientExitAbnormally005, Other_014_050)
{
    int ret, semsId;
    uint32_t existSubsNum = 0;
    ret = testGetSubsNum(&existSubsNum);
    EXPECT_EQ(GMERR_OK, ret);
    const int32_t subNum = 1020 - existSubsNum;

    pthread_t thr_arr[2];
    ret = pthread_create(&thr_arr[0], NULL, ThreadStartGtProcessSn3, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "等待客户端启动");
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat a.log");
    ret = executeCommand(g_command, "true");
    while (ret != GMERR_OK) {
        sleep(1);
        ret = executeCommand(g_command, "true");
    }

    ret = GtCheckSnStatus(existSubsNum + subNum);
    EXPECT_EQ(GMERR_OK, ret);

    char *result = NULL;
#if defined(RUN_INDEPENDENT) || defined(ENV_RTOSV2)
    ret = GtExecSystemCmd(
        &result, "ps -ef |grep gtest_also_run_disabled_tests |grep -v grep |grep -v sh |awk '{print $2}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("ps -ef |grep gtest_also_run_disabled_tests |grep -v grep");
#else
    ret = GtExecSystemCmd(&result, "ps |grep gtest_also* |grep -v grep |grep -v sh |awk '{print $1}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("ps |grep gtest_also* |grep -v grep");
#endif
    int32_t pid = atoi(result);
    free(result);

    ret = kill(pid, SIGKILL);
    EXPECT_EQ(GMERR_OK, ret);

    sleep(1);
    ret = GtCheckSnStatus(existSubsNum);
    EXPECT_EQ(GMERR_OK, ret);

    // 删表后再验证
    system("rm -rf a.log");
    system("./ClearRsCliExit5 --gtest_also_run_disabled_tests "
           "--gtest_filter=ClearResWithClientExitAbnormally005_1.DISABLED_GtProcessToCleanRes >>CleanRes.txt");

    // 另起子进程验证资源状态正常
    ret = pthread_create(&thr_arr[1], NULL, ThreadStartGtProcessSn4, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "等待客户端启动");
    ret = executeCommand(g_command, "true");
    while (ret != GMERR_OK) {
        sleep(1);
        ret = executeCommand(g_command, "true");
    }

    ret = GtCheckSnStatus(existSubsNum + subNum);
    EXPECT_EQ(GMERR_OK, ret);
    system("cat signalFile/signal1_true.txt >>b.log");

    pthread_join(thr_arr[1], NULL);
    sleep(1);
    ret = GtCheckSnStatus(existSubsNum);
    EXPECT_EQ(GMERR_OK, ret);

    system("./ClearRsCliExit5 --gtest_also_run_disabled_tests "
           "--gtest_filter=ClearResWithClientExitAbnormally005_1.DISABLED_GtProcessToCleanRes >>CleanRes.txt");
}

// 多客户端并发创建订阅关系，异常退出客户端进程，另起客户端进程查询订阅推送资源状态
TEST_F(ClearResWithClientExitAbnormally005, Other_014_051)
{
    const int32_t processNum = 2;
    const int32_t perCliSubNum = 2;
    pthread_t thr_arr[4];

    int ret, semsId;
    uint32_t existSubsNum = 0;
    ret = testGetSubsNum(&existSubsNum);
    EXPECT_EQ(GMERR_OK, ret);

    ret = pthread_create(&thr_arr[0], NULL, ThreadStartGtProcessSn5, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thr_arr[1], NULL, ThreadStartGtProcessSn6, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "等待客户端启动");
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat a.log");
    ret = executeCommand(g_command, "true");
    while (ret != GMERR_OK) {
        sleep(1);
        ret = executeCommand(g_command, "true");
    }

    ret = GtCheckSnStatus(existSubsNum + (processNum * perCliSubNum));
    EXPECT_EQ(GMERR_OK, ret);

    char *result1 = NULL;
    char *result2 = NULL;
#if defined(RUN_INDEPENDENT) || defined(ENV_RTOSV2)
    ret = GtExecSystemCmd(
        &result1, "ps -ef |grep gtest_also_run_disabled_tests |grep -v grep |grep -v sh |awk NR==1'{print $2}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSystemCmd(
        &result2, "ps -ef |grep gtest_also_run_disabled_tests |grep -v grep |grep -v sh |awk NR==2'{print $2}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("ps -ef |grep gtest_also_run_disabled_tests |grep -v grep");
#else
    ret = GtExecSystemCmd(&result1, "ps |grep gtest_also* |grep -v grep |grep -v sh |awk NR==1'{print $1}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSystemCmd(&result2, "ps |grep gtest_also* |grep -v grep |grep -v sh |awk NR==2'{print $1}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("ps |grep gtest_also* |grep -v grep");
#endif
    int32_t pid1 = atoi(result1);
    int32_t pid2 = atoi(result2);
    free(result1);
    free(result2);

    ret = kill(pid1, SIGTERM);
    EXPECT_EQ(GMERR_OK, ret);
    ret = kill(pid2, SIGTERM);
    EXPECT_EQ(GMERR_OK, ret);

    sleep(1);
    ret = GtCheckSnStatus(existSubsNum);
    EXPECT_EQ(GMERR_OK, ret);

    // 删表后再验证
    system("rm -rf a.log");
    system("./ClearRsCliExit5 --gtest_also_run_disabled_tests "
           "--gtest_filter=ClearResWithClientExitAbnormally005_1.DISABLED_GtProcessToCleanRes >>CleanRes.txt");

    // 另起子进程验证资源状态正常
    ret = pthread_create(&thr_arr[2], NULL, ThreadStartGtProcessSn7, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thr_arr[3], NULL, ThreadStartGtProcessSn8, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "等待客户端启动");
    ret = executeCommand(g_command, "true");
    while (ret != GMERR_OK) {
        sleep(1);
        ret = executeCommand(g_command, "true");
    }

    ret = GtCheckSnStatus(existSubsNum + (processNum * perCliSubNum));
    EXPECT_EQ(GMERR_OK, ret);
    system("cat signalFile/signal1_true.txt >>b.log");

    pthread_join(thr_arr[2], NULL);
    pthread_join(thr_arr[3], NULL);
    sleep(1);
    ret = GtCheckSnStatus(existSubsNum);
    EXPECT_EQ(GMERR_OK, ret);

    system("./ClearRsCliExit5 --gtest_also_run_disabled_tests "
           "--gtest_filter=ClearResWithClientExitAbnormally005_1.DISABLED_GtProcessToCleanRes >>CleanRes.txt");
}

/**************************************************** 通信资源回收 ****************************************************/
int GtProcessConn(void *res)
{
    int32_t connNum = 1;

    int ret, semId;
    ret = testEnvInit();
    CHECK_AND_RETURN(ret, "init test env");
    ret = create_epoll_thread();
    CHECK_AND_RETURN(ret, "create epoll");
    g_needCheckWhenSucc = false;
    GmcConnT **conns = (GmcConnT **)malloc(sizeof(GmcConnT *) * connNum);
    if (conns == NULL) {
        TEST_ERROR("malloc conns failed, %s", strerror(errno));
        testEnvClean();
        return FAILED;
    }
    memset(conns, 0, sizeof(GmcConnT *) * connNum);

    for (int i = 0; i < connNum; i++) {
        ret = testGmcConnect(&conns[i]);
        CHECK_AND_BREAK(ret, "connect, i = %d", i);
    }

    AW_FUN_Log(LOG_STEP, "GtProcessConn wait for be kill.");
    system("cat signalFile/signal1_true.txt >>a.log");

    while (1) {
        sleep(1);
    }

    return ret;
}

int GtProcessConn2(void *res)
{
    int32_t connNum = 1;

    int ret, semId;
    ret = testEnvInit();
    CHECK_AND_RETURN(ret, "init test env");
    ret = create_epoll_thread();
    CHECK_AND_RETURN(ret, "create epoll");
    g_needCheckWhenSucc = false;
    GmcConnT **conns = (GmcConnT **)malloc(sizeof(GmcConnT *) * connNum);
    if (conns == NULL) {
        TEST_ERROR("malloc conns failed, %s", strerror(errno));
        testEnvClean();
        return FAILED;
    }
    memset(conns, 0, sizeof(GmcConnT *) * connNum);

    for (int i = 0; i < connNum; i++) {
        ret = testGmcConnect(&conns[i]);
        CHECK_AND_BREAK(ret, "connect, i = %d", i);
    }

    system("cat signalFile/signal1_true.txt >>a.log");
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat b.log");
    ret = executeCommand(g_command, "true");
    while (ret != GMERR_OK) {
        sleep(1);
        ret = executeCommand(g_command, "true");
    }

    for (int i = 0; i < connNum; i++) {
        ret = testGmcDisconnect(conns[i]);
        EXPECT_EQ(GMERR_OK, ret);
        conns[i] = 0;
    }
    free(conns);
    conns = NULL;

    ret = close_epoll_thread();
    CHECK_AND_RETURN(ret, "close epoll");
    int tmpRet = testEnvClean();
    SET_RET_IFERR(ret, tmpRet, "clean test env");
    return ret;
}

int GtProcessConn3(void *res)
{
    uint32_t existConnNum = 0;
    int ret = testGetConnNum(&existConnNum);
    EXPECT_EQ(0, ret);
    int32_t connNum;
    if (g_envType == 2) {
        connNum = MAX_CONN_SIZE - existConnNum - 1; //预留一个连接查视图;
    } else {
        connNum = 100;
    }

    ret = testEnvInit();
    CHECK_AND_RETURN(ret, "init test env");
    ret = create_epoll_thread();
    CHECK_AND_RETURN(ret, "create epoll");
    g_needCheckWhenSucc = false;
    GmcConnT **conns = (GmcConnT **)malloc(sizeof(GmcConnT *) * connNum);
    if (conns == NULL) {
        TEST_ERROR("malloc conns failed, %s", strerror(errno));
        testEnvClean();
        return FAILED;
    }
    memset(conns, 0, sizeof(GmcConnT *) * connNum);

    for (int i = 0; i < connNum; i++) {
        ret = testGmcConnect(&conns[i]);
        CHECK_AND_BREAK(ret, "connect, i = %d", i);
    }

    system("cat signalFile/signal1_true.txt >>a.log");

    AW_FUN_Log(LOG_STEP, "GtProcessConn3 wait for be kill.");
    while (1) {
        sleep(1);
    }

    return ret;
}

int GtProcessConn4(void *res)
{
    uint32_t existConnNum = 0;
    int ret = testGetConnNum(&existConnNum);
    EXPECT_EQ(0, ret);
    int32_t connNum;
    if (g_envType == 2) {
        connNum = MAX_CONN_SIZE - existConnNum - 1; //预留一个连接查视图;
    } else {
        connNum = 100;
    }

    ret = testEnvInit();
    CHECK_AND_RETURN(ret, "init test env");
    ret = create_epoll_thread();
    CHECK_AND_RETURN(ret, "create epoll");
    g_needCheckWhenSucc = false;
    GmcConnT **conns = (GmcConnT **)malloc(sizeof(GmcConnT *) * connNum);
    if (conns == NULL) {
        TEST_ERROR("malloc conns failed, %s", strerror(errno));
        testEnvClean();
        return FAILED;
    }
    memset(conns, 0, sizeof(GmcConnT *) * connNum);

    for (int i = 0; i < connNum; i++) {
        ret = testGmcConnect(&conns[i]);
        CHECK_AND_BREAK(ret, "connect, i = %d", i);
    }

    system("cat signalFile/signal1_true.txt >>a.log");
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat b.log");
    ret = executeCommand(g_command, "true");
    while (ret != GMERR_OK) {
        sleep(1);
        ret = executeCommand(g_command, "true");
    }

    for (int i = 0; i < connNum; i++) {
        ret = testGmcDisconnect(conns[i]);
        EXPECT_EQ(GMERR_OK, ret);
        conns[i] = 0;
    }
    free(conns);
    conns = NULL;

    ret = close_epoll_thread();
    CHECK_AND_RETURN(ret, "close epoll");
    int tmpRet = testEnvClean();
    SET_RET_IFERR(ret, tmpRet, "clean test env");
    return ret;
}

int GtProcessConn5(void *res)
{
    uint32_t existConnNum = 0;
    int ret = testGetConnNum(&existConnNum);
    EXPECT_EQ(0, ret);
    int32_t connNum;
#ifdef ENV_SUSE
    connNum = MAX_CONN_SIZE - existConnNum - 2; //光启预留2个连接;
#else
    connNum = MAX_CONN_SIZE - existConnNum - 1; //预留一个连接查视图;
#endif

    ret = testEnvInit();
    CHECK_AND_RETURN(ret, "init test env");
    ret = create_epoll_thread();
    CHECK_AND_RETURN(ret, "create epoll");
    g_needCheckWhenSucc = false;
    GmcConnT **conns = (GmcConnT **)malloc(sizeof(GmcConnT *) * connNum);
    if (conns == NULL) {
        TEST_ERROR("malloc conns failed, %s", strerror(errno));
        testEnvClean();
        return FAILED;
    }
    memset(conns, 0, sizeof(GmcConnT *) * connNum);

    for (int i = 0; i < connNum; i++) {
        ret = testGmcConnect(&conns[i]);
        CHECK_AND_BREAK(ret, "connect, i = %d", i);
    }

    system("cat signalFile/signal1_true.txt >>a.log");

    AW_FUN_Log(LOG_STEP, "GtProcessConn3 wait for be kill.");
    while (1) {
        sleep(1);
    }

    return ret;
}

int GtProcessConn6(void *res)
{
    uint32_t existConnNum = 0;
    int ret = testGetConnNum(&existConnNum);
    EXPECT_EQ(0, ret);
    int32_t connNum;
#ifdef ENV_SUSE
    connNum = MAX_CONN_SIZE - existConnNum - 2; //光启预留2个连接;
#else
    connNum = MAX_CONN_SIZE - existConnNum - 1; //预留一个连接查视图;
#endif

    ret = testEnvInit();
    CHECK_AND_RETURN(ret, "init test env");
    ret = create_epoll_thread();
    CHECK_AND_RETURN(ret, "create epoll");
    g_needCheckWhenSucc = false;
    GmcConnT **conns = (GmcConnT **)malloc(sizeof(GmcConnT *) * connNum);
    if (conns == NULL) {
        TEST_ERROR("malloc conns failed, %s", strerror(errno));
        testEnvClean();
        return FAILED;
    }
    memset(conns, 0, sizeof(GmcConnT *) * connNum);

    for (int i = 0; i < connNum; i++) {
        ret = testGmcConnect(&conns[i]);
        CHECK_AND_BREAK(ret, "connect, i = %d", i);
    }

    system("cat signalFile/signal1_true.txt >>a.log");
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat b.log");
    ret = executeCommand(g_command, "true");
    while (ret != GMERR_OK) {
        sleep(1);
        ret = executeCommand(g_command, "true");
    }

    for (int i = 0; i < connNum; i++) {
        ret = testGmcDisconnect(conns[i]);
        EXPECT_EQ(GMERR_OK, ret);
        conns[i] = 0;
    }
    free(conns);
    conns = NULL;

    ret = close_epoll_thread();
    CHECK_AND_RETURN(ret, "close epoll");
    int tmpRet = testEnvClean();
    SET_RET_IFERR(ret, tmpRet, "clean test env");
    return ret;
}

int GtProcessConn7(void *res)
{
    int ret;
    int32_t connNum = 0;
    FILE *file;
    file = fopen("perCliConnNum.txt", "r");
    if (file == NULL) {
        return -1;
    }

    if (fscanf(file, "%d", &connNum) != 1) {
        AW_FUN_Log(LOG_STEP, "文件中没有有效的整数");
        fclose(file);
        return -1;
    }
    fclose(file);
    AW_FUN_Log(LOG_STEP, "thread connNum is %d", connNum);

    ret = testEnvInit();
    CHECK_AND_RETURN(ret, "init test env");
    ret = create_epoll_thread();
    CHECK_AND_RETURN(ret, "create epoll");
    g_needCheckWhenSucc = false;
    GmcConnT **conns = (GmcConnT **)malloc(sizeof(GmcConnT *) * connNum);
    if (conns == NULL) {
        TEST_ERROR("malloc conns failed, %s", strerror(errno));
        testEnvClean();
        return FAILED;
    }
    memset(conns, 0, sizeof(GmcConnT *) * connNum);

    for (int i = 0; i < connNum; i++) {
        ret = testGmcConnect(&conns[i]);
        CHECK_AND_BREAK(ret, "connect, i = %d", i);
    }

    system("cat signalFile/signal1_true.txt >>a.log");

    AW_FUN_Log(LOG_STEP, "GtProcessConn3 wait for be kill.");
    while (1) {
        sleep(1);
    }

    return ret;
}

int GtProcessConn8(void *res)
{
    int ret;
    int32_t connNum = 0;
    FILE *file;
    file = fopen("perCliConnNum.txt", "r");
    if (file == NULL) {
        return -1;
    }

    if (fscanf(file, "%d", &connNum) != 1) {
        AW_FUN_Log(LOG_STEP, "文件中没有有效的整数");
        fclose(file);
        return -1;
    }
    fclose(file);
    AW_FUN_Log(LOG_STEP, "thread connNum is %d", connNum);

    ret = testEnvInit();
    CHECK_AND_RETURN(ret, "init test env");
    ret = create_epoll_thread();
    CHECK_AND_RETURN(ret, "create epoll");
    g_needCheckWhenSucc = false;
    GmcConnT **conns = (GmcConnT **)malloc(sizeof(GmcConnT *) * connNum);
    if (conns == NULL) {
        TEST_ERROR("malloc conns failed, %s", strerror(errno));
        testEnvClean();
        return FAILED;
    }
    memset(conns, 0, sizeof(GmcConnT *) * connNum);

    for (int i = 0; i < connNum; i++) {
        ret = testGmcConnect(&conns[i]);
        CHECK_AND_BREAK(ret, "connect, i = %d", i);
    }

    system("cat signalFile/signal1_true.txt >>a.log");
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat b.log");
    ret = executeCommand(g_command, "true");
    while (ret != GMERR_OK) {
        sleep(1);
        ret = executeCommand(g_command, "true");
    }

    for (int i = 0; i < connNum; i++) {
        ret = testGmcDisconnect(conns[i]);
        EXPECT_EQ(GMERR_OK, ret);
        conns[i] = 0;
    }
    free(conns);
    conns = NULL;

    ret = close_epoll_thread();
    CHECK_AND_RETURN(ret, "close epoll");
    int tmpRet = testEnvClean();
    SET_RET_IFERR(ret, tmpRet, "clean test env");
    return ret;
}

void *ThreadStartGtProcessConn(void *args)
{
    system("./ClearRsCliExit5 --gtest_also_run_disabled_tests "
           "--gtest_filter=ClearResWithClientExitAbnormally005_1.DISABLED_GtProcessConn >Conn.txt");
    return NULL;
}

void *ThreadStartGtProcessConn2(void *args)
{
    int ret = system("./ClearRsCliExit5 --gtest_also_run_disabled_tests "
           "--gtest_filter=ClearResWithClientExitAbnormally005_1.DISABLED_GtProcessConn2 >Conn2.txt");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

void *ThreadStartGtProcessConn3(void *args)
{
    system("./ClearRsCliExit5 --gtest_also_run_disabled_tests "
           "--gtest_filter=ClearResWithClientExitAbnormally005_1.DISABLED_GtProcessConn3 >Conn3.txt");
    return NULL;
}

void *ThreadStartGtProcessConn4(void *args)
{
    int ret = system("./ClearRsCliExit5 --gtest_also_run_disabled_tests "
           "--gtest_filter=ClearResWithClientExitAbnormally005_1.DISABLED_GtProcessConn4 >Conn4.txt");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

void *ThreadStartGtProcessConn5(void *args)
{
    system("./ClearRsCliExit5 --gtest_also_run_disabled_tests "
           "--gtest_filter=ClearResWithClientExitAbnormally005_1.DISABLED_GtProcessConn5 >Conn5.txt");
    return NULL;
}

void *ThreadStartGtProcessConn6(void *args)
{
    int ret = system("./ClearRsCliExit5 --gtest_also_run_disabled_tests "
           "--gtest_filter=ClearResWithClientExitAbnormally005_1.DISABLED_GtProcessConn6 >Conn6.txt");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

void *ThreadStartGtProcessConn7(void *args)
{
    system("./ClearRsCliExit5 --gtest_also_run_disabled_tests "
           "--gtest_filter=ClearResWithClientExitAbnormally005_1.DISABLED_GtProcessConn7 >Conn7.txt");
    return NULL;
}

void *ThreadStartGtProcessConn8(void *args)
{
    int ret = system("./ClearRsCliExit5 --gtest_also_run_disabled_tests "
           "--gtest_filter=ClearResWithClientExitAbnormally005_1.DISABLED_GtProcessConn8 >Conn8.txt");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

// 客户端单线程创建1个连接，异常退出客户端进程，另起客户端进程查询连接状态
TEST_F(ClearResWithClientExitAbnormally005, Other_014_052)
{
    uint32_t existConnNum = 0;
    int ret = testGetConnNum(&existConnNum);
    EXPECT_EQ(0, ret);
    const int32_t connNum = 1;
    int semsId;

    pthread_t thr_arr[2];
    ret = pthread_create(&thr_arr[0], NULL, ThreadStartGtProcessConn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "等待客户端启动");
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat a.log");
    ret = executeCommand(g_command, "true");
    while (ret != GMERR_OK) {
        sleep(1);
        ret = executeCommand(g_command, "true");
    }

#ifdef ENV_SUSE
    ret = GtCheckConnStatus(existConnNum + connNum + 1);  // 光启1个进程自动创建1个连接
    EXPECT_EQ(GMERR_OK, ret);
#else
    if (g_tcpType) {
        ret = GtCheckConnStatus(existConnNum + connNum + 1);  // NERG光启1个进程自动创建1个连接
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        ret = GtCheckConnStatus(existConnNum + 1);
        EXPECT_EQ(GMERR_OK, ret);
    }
#endif

    char *result = NULL;
#if defined(RUN_INDEPENDENT) || defined(ENV_RTOSV2)
    ret = GtExecSystemCmd(
        &result, "ps -ef |grep gtest_also_run_disabled_tests |grep -v grep |grep -v sh |awk '{print $2}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("ps -ef |grep gtest_also_run_disabled_tests |grep -v grep");
#else
    ret = GtExecSystemCmd(&result, "ps |grep gtest_also* |grep -v grep |grep -v sh |awk '{print $1}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("ps |grep gtest_also* |grep -v grep");
#endif
    int32_t pid = atoi(result);
    free(result);

    ret = kill(pid, SIGKILL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GtCheckConnStatus(existConnNum);
    EXPECT_EQ(GMERR_OK, ret);

    // 另起进程验证资源状态正常
    system("rm -rf a.log");
    ret = pthread_create(&thr_arr[1], NULL, ThreadStartGtProcessConn2, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "等待客户端启动");
    ret = executeCommand(g_command, "true");
    while (ret != GMERR_OK) {
        sleep(1);
        ret = executeCommand(g_command, "true");
    }

#ifdef ENV_SUSE
    ret = GtCheckConnStatus(existConnNum + connNum + 1);  // 光启1个进程自动创建1个连接
    EXPECT_EQ(GMERR_OK, ret);
#else
    if (g_tcpType) {
        ret = GtCheckConnStatus(existConnNum + connNum + 1);  // NERG光启1个进程自动创建1个连接
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        ret = GtCheckConnStatus(existConnNum + 1);
        EXPECT_EQ(GMERR_OK, ret);
    }
#endif

    system("cat signalFile/signal1_true.txt >>b.log");
    pthread_join(thr_arr[1], NULL);
    ret = GtCheckConnStatus(existConnNum);
    EXPECT_EQ(GMERR_OK, ret);
}

// 客户端单线程创建1个连接，异常退出客户端进程，另起客户端进程查询连接状态，循环执行多次
TEST_F(ClearResWithClientExitAbnormally005, Other_014_053)
{
    uint32_t existConnNum = 0;
    int ret = testGetConnNum(&existConnNum);
    EXPECT_EQ(0, ret);
    const int32_t connNum = 1;
    int semsId;
    pthread_t thr_arr[2];

    for (int i = 0; i < MAX_LOOP_NUM; i++) {
        ret = pthread_create(&thr_arr[0], NULL, ThreadStartGtProcessConn, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        AW_FUN_Log(LOG_STEP, "等待客户端启动");
        (void)snprintf(g_command, MAX_CMD_SIZE, "cat a.log");
        ret = executeCommand(g_command, "true");
        while (ret != GMERR_OK) {
            sleep(1);
            ret = executeCommand(g_command, "true");
        }

#ifdef ENV_SUSE
        ret = GtCheckConnStatus(existConnNum + connNum + 1);  // 光启1个进程自动创建1个连接
        EXPECT_EQ(GMERR_OK, ret);
#else
        if (g_tcpType) {
            ret = GtCheckConnStatus(existConnNum + connNum + 1);  // NERG光启1个进程自动创建1个连接
            EXPECT_EQ(GMERR_OK, ret);
        } else {
            ret = GtCheckConnStatus(existConnNum + 1);
            EXPECT_EQ(GMERR_OK, ret);
        }
#endif

        char *result = NULL;
#if defined(RUN_INDEPENDENT) || defined(ENV_RTOSV2)
        ret = GtExecSystemCmd(
            &result, "ps -ef |grep gtest_also_run_disabled_tests |grep -v grep |grep -v sh |awk '{print $2}'");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        system("ps -ef |grep gtest_also_run_disabled_tests |grep -v grep");
#else
        ret = GtExecSystemCmd(&result, "ps |grep gtest_also* |grep -v grep |grep -v sh |awk '{print $1}'");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        system("ps |grep gtest_also* |grep -v grep");
#endif
        int32_t pid = atoi(result);
        free(result);

        ret = kill(pid, SIGKILL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GtCheckConnStatus(existConnNum);
        EXPECT_EQ(GMERR_OK, ret);

        // 另起子进程验证资源状态正常
        system("rm -rf a.log");
        ret = pthread_create(&thr_arr[1], NULL, ThreadStartGtProcessConn2, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        AW_FUN_Log(LOG_STEP, "等待客户端启动");
        ret = executeCommand(g_command, "true");
        while (ret != GMERR_OK) {
            sleep(1);
            ret = executeCommand(g_command, "true");
        }

#ifdef ENV_SUSE
        ret = GtCheckConnStatus(existConnNum + connNum + 1);  // 光启1个进程自动创建1个连接
        EXPECT_EQ(GMERR_OK, ret);
#else
        if (g_tcpType) {
            ret = GtCheckConnStatus(existConnNum + connNum + 1);  // NERG光启1个进程自动创建1个连接
            EXPECT_EQ(GMERR_OK, ret);
        } else {
            ret = GtCheckConnStatus(existConnNum + 1);
            EXPECT_EQ(GMERR_OK, ret);
        }
#endif
        system("cat signalFile/signal1_true.txt >>b.log");
        pthread_join(thr_arr[1], NULL);
        ret = GtCheckConnStatus(existConnNum);
        EXPECT_EQ(GMERR_OK, ret);
        system("rm -rf a.log");
        system("rm -rf b.log");
    }
}

// 客户端单线程创建100个连接，异常退出客户端进程，另起客户端进程查询连接状态
TEST_F(ClearResWithClientExitAbnormally005, Other_014_054)
{
    uint32_t existConnNum = 0;
    int ret = testGetConnNum(&existConnNum);
    EXPECT_EQ(0, ret);
    int32_t connNum;
    if (g_envType == 2) {
        connNum = MAX_CONN_SIZE - existConnNum - 1; //预留一个连接查视图
    } else {
        connNum = 100;
    }
    int semsId;

    pthread_t thr_arr[2];
    ret = pthread_create(&thr_arr[0], NULL, ThreadStartGtProcessConn3, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "等待客户端启动");
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat a.log");
    ret = executeCommand(g_command, "true");
    while (ret != GMERR_OK) {
        sleep(1);
        ret = executeCommand(g_command, "true");
    }

#ifdef ENV_SUSE
    ret = GtCheckConnStatus(existConnNum + connNum + 1);  // 光启1个进程自动创建1个连接
    EXPECT_EQ(GMERR_OK, ret);
#else
    if (g_tcpType) {
        ret = GtCheckConnStatus(existConnNum + connNum + 1);  // NERG光启1个进程自动创建1个连接
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        ret = GtCheckConnStatus(existConnNum + connNum);
        EXPECT_EQ(GMERR_OK, ret);
    }
#endif

    char *result = NULL;
#if defined(RUN_INDEPENDENT) || defined(ENV_RTOSV2)
    ret = GtExecSystemCmd(
        &result, "ps -ef |grep gtest_also_run_disabled_tests |grep -v grep |grep -v sh |awk '{print $2}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("ps -ef |grep gtest_also_run_disabled_tests |grep -v grep");
#else
    ret = GtExecSystemCmd(&result, "ps |grep gtest_also* |grep -v grep |grep -v sh |awk '{print $1}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("ps |grep gtest_also* |grep -v grep");
#endif
    int32_t pid = atoi(result);
    free(result);

    ret = kill(pid, SIGKILL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GtCheckConnStatus(existConnNum);
    EXPECT_EQ(GMERR_OK, ret);

    // 另起子进程验证资源状态正常
    system("rm -rf a.log");
    ret = pthread_create(&thr_arr[1], NULL, ThreadStartGtProcessConn4, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "等待客户端启动");
    ret = executeCommand(g_command, "true");
    while (ret != GMERR_OK) {
        sleep(1);
        ret = executeCommand(g_command, "true");
    }

#ifdef ENV_SUSE
    ret = GtCheckConnStatus(existConnNum + connNum + 1);  // 光启1个进程自动创建1个连接
    EXPECT_EQ(GMERR_OK, ret);
#else
    if (g_tcpType) {
        ret = GtCheckConnStatus(existConnNum + connNum + 1);  // NERG光启1个进程自动创建1个连接
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        ret = GtCheckConnStatus(existConnNum + connNum);
        EXPECT_EQ(GMERR_OK, ret);
    }
#endif

    system("cat signalFile/signal1_true.txt >>b.log");
    pthread_join(thr_arr[1], NULL);
    ret = GtCheckConnStatus(existConnNum);
    EXPECT_EQ(GMERR_OK, ret);
}

// 客户端单线程创建1024个连接，异常退出客户端进程，另起客户端进程查询连接状态
TEST_F(ClearResWithClientExitAbnormally005, Other_014_055)
{
    uint32_t existConnNum = 0;
    int ret = testGetConnNum(&existConnNum);
    EXPECT_EQ(0, ret);
    int32_t connNum;
#ifdef ENV_SUSE
    connNum = MAX_CONN_SIZE - existConnNum - 2; //光启预留2个连接;
#else
    connNum = MAX_CONN_SIZE - existConnNum - 1; //预留一个连接查视图;
#endif

    pthread_t thr_arr[2];
    ret = pthread_create(&thr_arr[0], NULL, ThreadStartGtProcessConn5, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "等待客户端启动");
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat a.log");
    ret = executeCommand(g_command, "true");
    while (ret != GMERR_OK) {
        sleep(1);
        ret = executeCommand(g_command, "true");
    }

#ifdef ENV_SUSE
    ret = GtCheckConnStatus(existConnNum + connNum + 1);  // 光启1个进程自动创建1个连接
    EXPECT_EQ(GMERR_OK, ret);
#else
    if (g_tcpType) {
    ret = GtCheckConnStatus(existConnNum + connNum + 1);  // NERT光启1个进程自动创建1个连接
    EXPECT_EQ(GMERR_OK, ret);
    } else {
        ret = GtCheckConnStatus(existConnNum + connNum);
        EXPECT_EQ(GMERR_OK, ret);
    }
#endif

    char *result = NULL;
#if defined(RUN_INDEPENDENT) || defined(ENV_RTOSV2)
    ret = GtExecSystemCmd(
        &result, "ps -ef |grep gtest_also_run_disabled_tests |grep -v grep |grep -v sh |awk '{print $2}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("ps -ef |grep gtest_also_run_disabled_tests |grep -v grep");
#else
    ret = GtExecSystemCmd(&result, "ps |grep gtest_also* |grep -v grep |grep -v sh |awk '{print $1}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("ps |grep gtest_also* |grep -v grep");
#endif

    int32_t pid = atoi(result);
    free(result);

    ret = kill(pid, SIGKILL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GtCheckConnStatus(existConnNum);
    EXPECT_EQ(GMERR_OK, ret);

    // 另起子进程验证资源状态正常
    system("rm -rf a.log");
    ret = pthread_create(&thr_arr[1], NULL, ThreadStartGtProcessConn6, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "等待客户端启动");
    ret = executeCommand(g_command, "true");
    while (ret != GMERR_OK) {
        sleep(1);
        ret = executeCommand(g_command, "true");
    }

#ifdef ENV_SUSE
    ret = GtCheckConnStatus(existConnNum + connNum + 1);  // 光启1个进程自动创建1个连接
    EXPECT_EQ(GMERR_OK, ret);
#else
    if (g_tcpType) {
        ret = GtCheckConnStatus(existConnNum + connNum + 1);  // NERT光启1个进程自动创建1个连接
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        ret = GtCheckConnStatus(existConnNum + connNum);
        EXPECT_EQ(GMERR_OK, ret);
    }
#endif

    system("cat signalFile/signal1_true.txt >>b.log");
    pthread_join(thr_arr[1], NULL);
    ret = GtCheckConnStatus(existConnNum);
    EXPECT_EQ(GMERR_OK, ret);
}

// 多客户端并发创建连接，异常退出客户端进程，另起客户端进程查询锁状态
TEST_F(ClearResWithClientExitAbnormally005, Other_014_056)
{
    uint32_t existConnNum = 0;
    int ret = testGetConnNum(&existConnNum);
    EXPECT_EQ(0, ret);
    const int32_t processNum = 2;
#if defined ENV_RTOSV2X
    const int32_t perCliConnNum = (MAX_CONN_SIZE - existConnNum) / 2 - 1;
#else
    const int32_t perCliConnNum = 10;
#endif
    AW_FUN_Log(LOG_STEP, "perCliConnNum is %d", perCliConnNum);
    (void)snprintf(g_command, MAX_CMD_SIZE, "echo %d >perCliConnNum.txt", perCliConnNum);
    system(g_command);
    pthread_t thr_arr[4];

    ret = pthread_create(&thr_arr[0], NULL, ThreadStartGtProcessConn7, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thr_arr[1], NULL, ThreadStartGtProcessConn7, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "等待客户端启动");
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat a.log |wc -l");
    ret = executeCommand(g_command, "2");
    while (ret != GMERR_OK) {
        sleep(1);
        ret = executeCommand(g_command, "2");
    }

#ifdef ENV_SUSE
    ret = GtCheckConnStatus(existConnNum + (processNum * perCliConnNum) + 2);  // 光启1个进程自动创建1个连接
    EXPECT_EQ(GMERR_OK, ret);
#else
    if (g_tcpType) {
        ret = GtCheckConnStatus(existConnNum + (processNum * perCliConnNum) + 2);  // NERG光启1个进程自动创建1个连接
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        ret = GtCheckConnStatus(existConnNum + (processNum * perCliConnNum));
        EXPECT_EQ(GMERR_OK, ret);
    }
#endif

    char *result1 = NULL;
    char *result2 = NULL;
#if defined(RUN_INDEPENDENT) || defined(ENV_RTOSV2)
    ret = GtExecSystemCmd(
        &result1, "ps -ef |grep gtest_also_run_disabled_tests |grep -v grep |grep -v sh |awk NR==1'{print $2}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSystemCmd(
        &result2, "ps -ef |grep gtest_also_run_disabled_tests |grep -v grep |grep -v sh |awk NR==2'{print $2}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("ps -ef |grep gtest_also_run_disabled_tests |grep -v grep");
#else
    ret = GtExecSystemCmd(&result1, "ps |grep gtest_also* |grep -v grep |grep -v sh |awk NR==1'{print $1}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSystemCmd(&result2, "ps |grep gtest_also* |grep -v grep |grep -v sh |awk NR==2'{print $1}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("ps |grep gtest_also* |grep -v grep");
#endif
    int32_t pid1 = atoi(result1);
    int32_t pid2 = atoi(result2);
    free(result1);
    free(result2);

    ret = kill(pid1, SIGKILL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = kill(pid2, SIGKILL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GtCheckConnStatus(existConnNum);
    EXPECT_EQ(GMERR_OK, ret);

    // 另起客户端检查连接资源状态
    system("rm -rf a.log");
    ret = pthread_create(&thr_arr[2], NULL, ThreadStartGtProcessConn8, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thr_arr[3], NULL, ThreadStartGtProcessConn8, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "等待客户端启动");
    ret = executeCommand(g_command, "2");
    while (ret != GMERR_OK) {
        sleep(1);
        ret = executeCommand(g_command, "2");
    }

#ifdef ENV_SUSE
    ret = GtCheckConnStatus(existConnNum + (processNum * perCliConnNum) + 2);  // 光启1个进程自动创建1个连接
    EXPECT_EQ(GMERR_OK, ret);
#else
    if (g_tcpType) {
        ret = GtCheckConnStatus(existConnNum + (processNum * perCliConnNum) + 2);  // NERG光启1个进程自动创建1个连接
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        ret = GtCheckConnStatus(existConnNum + (processNum * perCliConnNum));
        EXPECT_EQ(GMERR_OK, ret);
    }
#endif

    system("cat signalFile/signal1_true.txt >>b.log");
    pthread_join(thr_arr[2], NULL);
    pthread_join(thr_arr[3], NULL);
    ret = GtCheckConnStatus(existConnNum);
    EXPECT_EQ(GMERR_OK, ret);
}

// 客户端删除表资源
TEST_F(ClearResWithClientExitAbnormally005_1, DISABLED_GtProcessToCleanRes)
{
    AW_FUN_Log(LOG_STEP, "client start clean table.");
    int ret = GtProcessToCleanRes(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 客户端写1000条数据，并发10个线程读，，等待被kill
TEST_F(ClearResWithClientExitAbnormally005_1, DISABLED_GtProcessDirectRead)
{
    AW_FUN_Log(LOG_STEP, "GtProcessDirectRead start.");
    int ret = GtProcessDirectRead(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 客户端写1000条数据，并发10个线程读，删表清理资源
TEST_F(ClearResWithClientExitAbnormally005_1, DISABLED_GtProcessDirectRead2)
{
    AW_FUN_Log(LOG_STEP, "GtProcessDirectRead2 start.");
    int ret = GtProcessDirectRead2(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 客户端写1000条数据，等待被kill
TEST_F(ClearResWithClientExitAbnormally005_1, DISABLED_GtProcessTableRef)
{
    AW_FUN_Log(LOG_STEP, "GtProcessTableRef start.");
    int ret = GtProcessTableRef(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "GtProcessTableRef end.");
}

// 客户端写1000条数据，等待被kill
TEST_F(ClearResWithClientExitAbnormally005_1, DISABLED_GtProcessTableRef1)
{
    AW_FUN_Log(LOG_STEP, "GtProcessTableRef1 start.");
    int ret = GtProcessTableRef1(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "GtProcessTableRef1 end.");
}

// 客户端写1000条数据，删表
TEST_F(ClearResWithClientExitAbnormally005_1, DISABLED_GtProcessTableRef2)
{
    AW_FUN_Log(LOG_STEP, "GtProcessTableRef2 start.");
    int ret = GtProcessTableRef2(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "GtProcessTableRef2 end.");
}

// CheckTableRefCnt
TEST_F(ClearResWithClientExitAbnormally005_1, DISABLED_GtProcessCheckTableRefCnt2)
{
    AW_FUN_Log(LOG_STEP, "GtProcessCheckTableRefCnt start.");
    int ret = GtProcessCheckTableRefCnt2(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 创建1个订阅关系，等待被kill
TEST_F(ClearResWithClientExitAbnormally005_1, DISABLED_GtProcessSn)
{
    AW_FUN_Log(LOG_STEP, "DISABLED_GtProcessSn start.");
    int ret = GtProcessSn(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 创建1个订阅关系，释放1个订阅关系
TEST_F(ClearResWithClientExitAbnormally005_1, DISABLED_GtProcessSn2)
{
    AW_FUN_Log(LOG_STEP, "DISABLED_GtProcessSn2 start.");
    int ret = GtProcessSn2(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 创建1020个订阅关系，等待被kill
TEST_F(ClearResWithClientExitAbnormally005_1, DISABLED_GtProcessSn3)
{
    AW_FUN_Log(LOG_STEP, "DISABLED_GtProcessSn3 start.");
    int ret = GtProcessSn3(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 创建1020个订阅关系，释放1020个订阅关系
TEST_F(ClearResWithClientExitAbnormally005_1, DISABLED_GtProcessSn4)
{
    AW_FUN_Log(LOG_STEP, "DISABLED_GtProcessSn4 start.");
    int ret = GtProcessSn4(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 客户端1创建2个订阅关系，等待被kill
TEST_F(ClearResWithClientExitAbnormally005_1, DISABLED_GtProcessSn5)
{
    AW_FUN_Log(LOG_STEP, "DISABLED_GtProcessSn5 start.");
    int ret = GtProcessSn5(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 客户端2创建2个订阅关系，等待被kill
TEST_F(ClearResWithClientExitAbnormally005_1, DISABLED_GtProcessSn6)
{
    AW_FUN_Log(LOG_STEP, "DISABLED_GtProcessSn6 start.");
    int ret = GtProcessSn6(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 客户端1创建2个订阅关系，释放2个订阅关系
TEST_F(ClearResWithClientExitAbnormally005_1, DISABLED_GtProcessSn7)
{
    AW_FUN_Log(LOG_STEP, "DISABLED_GtProcessSn7 start.");
    int ret = GtProcessSn7(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 客户端2创建2个订阅关系，释放2个订阅关系
TEST_F(ClearResWithClientExitAbnormally005_1, DISABLED_GtProcessSn8)
{
    AW_FUN_Log(LOG_STEP, "DISABLED_GtProcessSn8 start.");
    int ret = GtProcessSn8(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 创建1个连接资源，等待被kill
TEST_F(ClearResWithClientExitAbnormally005_1, DISABLED_GtProcessConn)
{
    AW_FUN_Log(LOG_STEP, "DISABLED_GtProcessConn start.");
    int ret = GtProcessConn(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 创建1个连接资源，释放1个连接资源
TEST_F(ClearResWithClientExitAbnormally005_1, DISABLED_GtProcessConn2)
{
    AW_FUN_Log(LOG_STEP, "DISABLED_GtProcessConn2 start.");
    int ret = GtProcessConn2(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 创建100个连接资源，等待被kill
TEST_F(ClearResWithClientExitAbnormally005_1, DISABLED_GtProcessConn3)
{
    AW_FUN_Log(LOG_STEP, "DISABLED_GtProcessConn3 start.");
    int ret = GtProcessConn3(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 创建100个连接资源，释放100个连接资源
TEST_F(ClearResWithClientExitAbnormally005_1, DISABLED_GtProcessConn4)
{
    AW_FUN_Log(LOG_STEP, "DISABLED_GtProcessConn4 start.");
    int ret = GtProcessConn4(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 创建1020个连接资源，等待被kill
TEST_F(ClearResWithClientExitAbnormally005_1, DISABLED_GtProcessConn5)
{
    AW_FUN_Log(LOG_STEP, "DISABLED_GtProcessConn5 start.");
    int ret = GtProcessConn5(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 创建1020个连接资源，释放1020个连接资源
TEST_F(ClearResWithClientExitAbnormally005_1, DISABLED_GtProcessConn6)
{
    AW_FUN_Log(LOG_STEP, "DISABLED_GtProcessConn6 start.");
    int ret = GtProcessConn6(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 创建10个连接资源，等待被kill
TEST_F(ClearResWithClientExitAbnormally005_1, DISABLED_GtProcessConn7)
{
    AW_FUN_Log(LOG_STEP, "DISABLED_GtProcessConn7 start.");
    int ret = GtProcessConn7(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 创建10个连接资源，释放10个连接资源
TEST_F(ClearResWithClientExitAbnormally005_1, DISABLED_GtProcessConn8)
{
    AW_FUN_Log(LOG_STEP, "DISABLED_GtProcessConn8 start.");
    int ret = GtProcessConn8(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 创建1个事务，等待被kill
TEST_F(ClearResWithClientExitAbnormally005_1, DISABLED_GtProcessTrans)
{
    AW_FUN_Log(LOG_STEP, "DISABLED_GtProcessTrans start.");
    int ret = GtProcessTrans(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 创建1个事务，提交1个事务
TEST_F(ClearResWithClientExitAbnormally005_1, DISABLED_GtProcessTrans2)
{
    AW_FUN_Log(LOG_STEP, "DISABLED_GtProcessTrans2 start.");
    int ret = GtProcessTrans2(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 创建2048个事务，等待被kill
TEST_F(ClearResWithClientExitAbnormally005_1, DISABLED_GtProcessTrans3)
{
    AW_FUN_Log(LOG_STEP, "DISABLED_GtProcessTrans3 start.");
    int ret = GtProcessTrans3(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 创建2048个事务，提交2048个事务
TEST_F(ClearResWithClientExitAbnormally005_1, DISABLED_GtProcessTrans4)
{
    AW_FUN_Log(LOG_STEP, "DISABLED_GtProcessTrans4 start.");
    int ret = GtProcessTrans4(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 创建4897个事务，等待被kill
TEST_F(ClearResWithClientExitAbnormally005_1, DISABLED_GtProcessTrans5)
{
    AW_FUN_Log(LOG_STEP, "DISABLED_GtProcessTrans5 start.");
    int ret = GtProcessTrans5(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 创建4897个事务，提交4897个事务
TEST_F(ClearResWithClientExitAbnormally005_1, DISABLED_GtProcessTrans6)
{
    AW_FUN_Log(LOG_STEP, "DISABLED_GtProcessTrans6 start.");
    int ret = GtProcessTrans6(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 客户端1创建10个事务，等待被kill
TEST_F(ClearResWithClientExitAbnormally005_1, DISABLED_GtProcessTrans7)
{
    AW_FUN_Log(LOG_STEP, "DISABLED_GtProcessTrans7 start.");
    int ret = GtProcessTrans7(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 客户端2创建10个事务，等待被kill
TEST_F(ClearResWithClientExitAbnormally005_1, DISABLED_GtProcessTrans8)
{
    AW_FUN_Log(LOG_STEP, "DISABLED_GtProcessTrans8 start.");
    int ret = GtProcessTrans8(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 客户端1创建1个事务，提交1个事务
TEST_F(ClearResWithClientExitAbnormally005_1, DISABLED_GtProcessTrans9)
{
    AW_FUN_Log(LOG_STEP, "DISABLED_GtProcessTrans9 start.");
    int ret = GtProcessTrans9(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 客户端2创建1个事务，提交1个事务
TEST_F(ClearResWithClientExitAbnormally005_1, DISABLED_GtProcessTrans10)
{
    AW_FUN_Log(LOG_STEP, "DISABLED_GtProcessTrans10 start.");
    int ret = GtProcessTrans10(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
