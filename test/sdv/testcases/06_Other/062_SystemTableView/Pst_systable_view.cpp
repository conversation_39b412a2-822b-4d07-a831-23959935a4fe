/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2023. All rights reserved.
 * Description: 按需持久化--视图补齐系统表
 * Author: pwx860460
 * Create: 2023-09-20
 * History:
 */

#include "../053_OnDemandPersistence/Persistence_common.h"

#define GM_SYS_NSP "GM_SYS_NSP"
#define GM_SYS_VL "GM_SYS_VL"
#define GM_SYS_NODE "GM_SYS_NODE"
#define GM_SYS_PROP "GM_SYS_PROP"
#define GM_SYS_IDX "GM_SYS_IDX"

class Pst_system_table_view : public testing::Test {
protected:
    static void SetUpTestCase()
    {  
    }

    static void TearDownTestCase()
    {  
    };
public:
    virtual void SetUp();
    virtual void TearDown();
};
char g_errorCode04[MAX_CMD_SIZE] = {0};
char g_errorCode05[MAX_CMD_SIZE] = {0};
void Pst_system_table_view::SetUp()
{
    system("mkdir -p /data/gmdb/; rm -rf /data/gmdb/*");
    int ret = ChangeGmserverCfg((char *)"recover", NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"0");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit(-1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建客户端连接超时时间75秒
    uint32_t requestTimeout = 75 * 1000 * 1000;
    ConnOptionT *connOption;
    ret = testMallocConnOptions(&connOption, NULL, NULL, NULL, &requestTimeout, &requestTimeout, &requestTimeout);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt, GMC_CONN_TYPE_SYNC, true, g_epoll_reg_info, NULL, NULL, connOption);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testFreeConnOptions(connOption);
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_DUPLICATE_OBJECT);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_DUPLICATE_TABLE);
    (void)snprintf(g_errorCode03, MAX_CMD_SIZE, "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    (void)snprintf(g_errorCode04, MAX_CMD_SIZE, "GMERR-%d", GMERR_CONNECTION_RESET_BY_PEER);
    (void)snprintf(g_errorCode05, MAX_CMD_SIZE, "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(5, g_errorCode01, g_errorCode02, g_errorCode03, g_errorCode04, g_errorCode05);
    AW_CHECK_LOG_BEGIN();
}

void Pst_system_table_view::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"recover", NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/stop.sh -f");
}

// 除了默认3个nsp，无新建nsp 查看系统视图
TEST_F(Pst_system_table_view, Other_062_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcDropVertexLabel(g_stmt, VL_SIMPLE_NAME);
    int ret = GtCreateVertexLabel(g_stmt, VL_SIMPLE_JSON_PATH, CONFIG_JSON_PATH);
    ASSERT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview record GM_SYS_NSP -ns system | grep NSP_NAME");
    system(g_command);
    ret = executeCommand(g_command, "\"NSP_NAME\": \"nsp1\"");
    AW_MACRO_ASSERT_EQ_INT(-1, ret);
    // 查询systable ptree格式
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview systable vertex ptree -name GM_SYS_NSP");
    system(g_command);
    ret = executeCommand(g_command, "\"NSP_NAME\": \"nsp1\"");
    AW_MACRO_ASSERT_EQ_INT(-1, ret);

    // 查询systable precord格式
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview systable vertex precord -name GM_SYS_NSP");
    system(g_command);
    ret = executeCommand(g_command, "\"NSP_NAME\": \"nsp1\"");
    AW_MACRO_ASSERT_EQ_INT(-1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

const char *nameSpace = (const char *)"nsp1";
// 新建nsp，查看系统视图
TEST_F(Pst_system_table_view, Other_062_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建namespace
    int ret = GmcCreateNamespace(g_stmt, nameSpace, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, nameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDropVertexLabel(g_stmt, VL_GENERAL_COMPLEX_NAME);
    ret = GtCreateVertexLabel(g_stmt, VL_GENERAL_COMPLEX_JSON_PATH, VL_GENERAL_COMPLEX_CONFIG_PATH);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 校验NSP_NAME 、NSP_TRX_TYPE、NSP_ISO_LEVEL
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview record GM_SYS_NSP -ns system | grep NSP_NAME");
    system(g_command);
    ret = executeCommand(g_command, "\"NSP_NAME\": \"nsp1\"");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview record GM_SYS_NSP -ns system | grep NSP_TRX_TYPE");
    system(g_command);
    ret = executeCommand(g_command, "\"NSP_TRX_TYPE\": 0");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview record GM_SYS_NSP -ns system | grep NSP_ISO_LEVEL");
    system(g_command);
    ret = executeCommand(g_command, "\"NSP_ISO_LEVEL\": 1");

    // 查询systable ptree格式
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview systable vertex ptree -name %s", VL_GENERAL_COMPLEX_NAME);
    system(g_command);
    ret = executeCommand(g_command, "\"NSP_NAME\": \"nsp1\"");
    AW_MACRO_ASSERT_EQ_INT(-1, ret);
    // 查询systable ptree格式
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview systable vertex ptree -name %s |grep 'PROP_NAME:A' |wc -l", VL_GENERAL_COMPLEX_NAME);
    system(g_command);
    ret = executeCommand(g_command, "10");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 查询systable precord格式
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview systable vertex precord -name %s", VL_GENERAL_COMPLEX_NAME);
    system(g_command);
    ret = executeCommand(g_command, "\"NSP_NAME\": \"nsp1\"");
    AW_MACRO_ASSERT_EQ_INT(-1, ret);

    // 查询systable precord格式
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview systable vertex precord -name %s |grep 'vertex:' |wc -l", VL_GENERAL_COMPLEX_NAME);
    system(g_command);
    ret = executeCommand(g_command, "21");// 适配member key后减1
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(g_stmt, nameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtCreateVertexLabel(g_stmt, VL_GENERAL_COMPLEX_JSON_PATH, VL_GENERAL_COMPLEX_CONFIG_PATH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DUPLICATE_TABLE, ret);
    ret = GmcDropVertexLabel(g_stmt, VL_GENERAL_COMPLEX_NAME);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, nameSpace);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 删除nsp，查看系统视图
TEST_F(Pst_system_table_view, Other_062_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建namespace
    int ret = GmcCreateNamespace(g_stmt, nameSpace, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, nameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDropVertexLabel(g_stmt, VL_GENERAL_COMPLEX_NAME);
    ret = GtCreateVertexLabel(g_stmt, VL_GENERAL_COMPLEX_JSON_PATH, VL_GENERAL_COMPLEX_CONFIG_PATH);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 删除后查看
    ret = GmcDropVertexLabel(g_stmt, VL_GENERAL_COMPLEX_NAME);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, nameSpace);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 校验NSP_NAME 、NSP_TRX_TYPE、NSP_ISO_LEVEL
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview record GM_SYS_NSP -ns system | grep NSP_NAME");
    system(g_command);
    ret = executeCommand(g_command, "\"NSP_NAME\": \"nsp1\"");
    AW_MACRO_ASSERT_EQ_INT(-1, ret);

    // 查询systable ptree格式
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview systable vertex ptree -name %s", VL_GENERAL_COMPLEX_NAME);
    system(g_command);
    ret = executeCommand(g_command, "\"NSP_NAME\": \"nsp1\"");
    AW_MACRO_ASSERT_EQ_INT(-1, ret);

    // 查询systable precord格式
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview systable vertex precord -name %s", VL_GENERAL_COMPLEX_NAME);
    system(g_command);
    ret = executeCommand(g_command, "\"NSP_NAME\": \"nsp1\"");
    AW_MACRO_ASSERT_EQ_INT(-1, ret);
    
    AW_FUN_Log(LOG_STEP, "test end.");
}


// 创建nsp，查看系统视图 ptree/precord
TEST_F(Pst_system_table_view, Other_062_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建namespace
    int ret = GmcCreateNamespace(g_stmt, nameSpace, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, nameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDropVertexLabel(g_stmt, VL_GENERAL_COMPLEX_NAME);
    ret = GtCreateVertexLabel(g_stmt, VL_GENERAL_COMPLEX_JSON_PATH, VL_GENERAL_COMPLEX_CONFIG_PATH);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 删除失败后查看
    ret = GmcDropVertexLabel(g_stmt, "GM_SYS_NODE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = GmcDropVertexLabel(g_stmt, "GM_SYS_VL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = GmcDropVertexLabel(g_stmt, "GM_SYS_NSP");
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = GmcDropVertexLabel(g_stmt, "GM_SYS_PROP");
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = GmcDropVertexLabel(g_stmt, "GM_SYS_IDX");
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview record GM_SYS_NSP -ns system | grep NSP_NAME");
    system(g_command);
    ret = executeCommand(g_command, "\"NSP_NAME\": \"nsp1\"");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview record GM_SYS_NSP -ns system | grep NSP_TRX_TYPE");
    system(g_command);
    ret = executeCommand(g_command, "\"NSP_TRX_TYPE\": 0");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview record GM_SYS_NSP -ns system | grep NSP_ISO_LEVEL");
    system(g_command);
    ret = executeCommand(g_command, "\"NSP_ISO_LEVEL\": 1");

    // 查询systable ptree格式
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview systable vertex ptree -name %s", VL_GENERAL_COMPLEX_NAME);
    system(g_command);
    ret = executeCommand(g_command, "\"NSP_NAME\": \"nsp1\"");
    AW_MACRO_ASSERT_EQ_INT(-1, ret);
    // 查询systable ptree格式
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview systable vertex ptree -name %s |grep 'PROP_NAME:A' |wc -l", VL_GENERAL_COMPLEX_NAME);
    system(g_command);
    ret = executeCommand(g_command, "10");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 查询systable precord格式
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview systable vertex precord -name %s", VL_GENERAL_COMPLEX_NAME);
    system(g_command);
    ret = executeCommand(g_command, "\"NSP_NAME\": \"nsp1\"");
    AW_MACRO_ASSERT_EQ_INT(-1, ret);

    // 查询systable precord格式
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview systable vertex precord -name %s |grep 'vertex:' |wc -l", VL_GENERAL_COMPLEX_NAME);
    system(g_command);
    ret = executeCommand(g_command, "21");// 适配member key后减1
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, VL_GENERAL_COMPLEX_NAME);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, nameSpace);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建nsp1,重启后，查询gmsysview record GM_SYS_NSP
TEST_F(Pst_system_table_view, Other_062_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建namespace
    int ret = GmcCreateNamespace(g_stmt, nameSpace, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, nameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDropVertexLabel(g_stmt, VL_GENERAL_COMPLEX_NAME);
    ret = GtCreateVertexLabel(g_stmt, VL_GENERAL_COMPLEX_JSON_PATH, VL_GENERAL_COMPLEX_CONFIG_PATH);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 落盘
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    ret = GtGmserverRestart(SIGKILL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testEnvInit(-1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview record GM_SYS_NSP -ns system | grep NSP_NAME");
    system(g_command);
    ret = executeCommand(g_command, "\"NSP_NAME\": \"nsp1\"");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview record GM_SYS_NSP -ns system | grep NSP_TRX_TYPE");
    system(g_command);
    ret = executeCommand(g_command, "\"NSP_TRX_TYPE\": 0");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview record GM_SYS_NSP -ns system | grep NSP_ISO_LEVEL");
    system(g_command);
    ret = executeCommand(g_command, "\"NSP_ISO_LEVEL\": 1");

    // 查询systable ptree格式
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview systable vertex ptree -name %s", VL_GENERAL_COMPLEX_NAME);
    system(g_command);
    ret = executeCommand(g_command, "\"NSP_NAME\": \"nsp1\"");
    AW_MACRO_ASSERT_EQ_INT(-1, ret);
    // 查询systable ptree格式
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview systable vertex ptree -name %s |grep 'PROP_NAME:A' |wc -l", VL_GENERAL_COMPLEX_NAME);
    system(g_command);
    ret = executeCommand(g_command, "10");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 查询systable precord格式
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview systable vertex precord -name %s", VL_GENERAL_COMPLEX_NAME);
    system(g_command);
    ret = executeCommand(g_command, "\"NSP_NAME\": \"nsp1\"");
    AW_MACRO_ASSERT_EQ_INT(-1, ret);

    // 查询systable precord格式
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview systable vertex precord -name %s |grep 'vertex:' |wc -l", VL_GENERAL_COMPLEX_NAME);
    system(g_command);
    ret = executeCommand(g_command, "21");// 适配member key后减1
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    ret = GmcUseNamespace(g_stmt, nameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtCreateVertexLabel(g_stmt, VL_GENERAL_COMPLEX_JSON_PATH, VL_GENERAL_COMPLEX_CONFIG_PATH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DUPLICATE_TABLE, ret);
    ret = GmcDropVertexLabel(g_stmt, VL_GENERAL_COMPLEX_NAME);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, nameSpace);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}


// 创建nsp1,删除nsp1，重启后,查询gmsysview record GM_SYS_NSP
TEST_F(Pst_system_table_view, Other_062_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建namespace
    int ret = GmcCreateNamespace(g_stmt, nameSpace, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, nameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDropVertexLabel(g_stmt, VL_GENERAL_COMPLEX_NAME);
    ret = GtCreateVertexLabel(g_stmt, VL_GENERAL_COMPLEX_JSON_PATH, VL_GENERAL_COMPLEX_CONFIG_PATH);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 删除后查看
    ret = GmcDropVertexLabel(g_stmt, VL_GENERAL_COMPLEX_NAME);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, nameSpace);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 落盘
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    ret = GtGmserverRestart(SIGKILL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testEnvInit(-1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验NSP_NAME 、NSP_TRX_TYPE、NSP_ISO_LEVEL
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview record GM_SYS_NSP -ns system | grep NSP_NAME");
    system(g_command);
    ret = executeCommand(g_command, "\"NSP_NAME\": \"nsp1\"");
    AW_MACRO_ASSERT_EQ_INT(-1, ret);

    // 查询systable ptree格式
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview systable vertex ptree -name %s", VL_GENERAL_COMPLEX_NAME);
    system(g_command);
    ret = executeCommand(g_command, "\"NSP_NAME\": \"nsp1\"");
    AW_MACRO_ASSERT_EQ_INT(-1, ret);

    // 查询systable precord格式
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview systable vertex precord -name %s", VL_GENERAL_COMPLEX_NAME);
    system(g_command);
    ret = executeCommand(g_command, "\"NSP_NAME\": \"nsp1\"");
    AW_MACRO_ASSERT_EQ_INT(-1, ret);
    
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 不支持kvLabel gmsysview systable  kvLabel  ptree -name <name> 迭代三不支持
TEST_F(Pst_system_table_view, Other_062_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char g_configJson[128] = "{\"max_record_count\" : 10000}";
    char g_KvName[128] = "KV6";
    int ret = GmcKvCreateTable(g_stmt, g_KvName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview systable kvLabel precord -name %s |grep ErrorCodeDescription", g_KvName);
    system(g_command);
    ret = executeCommand(g_command, "ErrorCodeDescription: Not normal parameter value");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcKvDropTable(g_stmt, g_KvName);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// ptree展示格式
TEST_F(Pst_system_table_view, Other_062_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建namespace
    int ret = GmcCreateNamespace(g_stmt, nameSpace, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, nameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDropVertexLabel(g_stmt, VL_GENERAL_COMPLEX_NAME);
    ret = GtCreateVertexLabel(g_stmt, VL_GENERAL_COMPLEX_JSON_PATH, VL_GENERAL_COMPLEX_CONFIG_PATH);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    // 查询systable ptree格式
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview systable vertex ptree -name %s |grep 'PROP_NAME:A' |wc -l", VL_GENERAL_COMPLEX_NAME);
    system(g_command);
    ret = executeCommand(g_command, "10");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(g_stmt, nameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtCreateVertexLabel(g_stmt, VL_GENERAL_COMPLEX_JSON_PATH, VL_GENERAL_COMPLEX_CONFIG_PATH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DUPLICATE_TABLE, ret);
    ret = GmcDropVertexLabel(g_stmt, VL_GENERAL_COMPLEX_NAME);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, nameSpace);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

}

// precord格式展示格式
TEST_F(Pst_system_table_view, Other_062_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建namespace
    int ret = GmcCreateNamespace(g_stmt, nameSpace, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, nameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDropVertexLabel(g_stmt, VL_GENERAL_COMPLEX_NAME);
    ret = GtCreateVertexLabel(g_stmt, VL_GENERAL_COMPLEX_JSON_PATH, VL_GENERAL_COMPLEX_CONFIG_PATH);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 查询systable precord格式
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview systable vertex precord -name %s |grep 'vertex:' |wc -l", VL_GENERAL_COMPLEX_NAME);
    system(g_command);
    ret = executeCommand(g_command, "21");// 适配member key后减1
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(g_stmt, nameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtCreateVertexLabel(g_stmt, VL_GENERAL_COMPLEX_JSON_PATH, VL_GENERAL_COMPLEX_CONFIG_PATH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DUPLICATE_TABLE, ret);
    ret = GmcDropVertexLabel(g_stmt, VL_GENERAL_COMPLEX_NAME);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, nameSpace);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

}

// 关键字异常 不完全匹配
TEST_F(Pst_system_table_view, Other_062_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建namespace
    int ret = GmcCreateNamespace(g_stmt, nameSpace, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, nameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDropVertexLabel(g_stmt, VL_GENERAL_COMPLEX_NAME);
    ret = GtCreateVertexLabel(g_stmt, VL_GENERAL_COMPLEX_JSON_PATH, VL_GENERAL_COMPLEX_CONFIG_PATH);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 校验NSP_NAME 、NSP_TRX_TYPE、NSP_ISO_LEVEL
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview record GM_SYS_NSP -ns system | grep NSP_NAME");
    system(g_command);
    ret = executeCommand(g_command, "\"NSP_NAME\": \"nsp1\"");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 查询systable ptree格式
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview sys vertex ptree -name %s |grep ErrorCodeDescription |wc -l", VL_GENERAL_COMPLEX_NAME);
    system(g_command);
    ret = executeCommand(g_command, "1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview systable ver ptree -name %s |grep ErrorCodeDescription |wc -l", VL_GENERAL_COMPLEX_NAME);
    system(g_command);
    ret = executeCommand(g_command, "1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview systable vertex pte -name %s |grep ErrorCodeDescription |wc -l", VL_GENERAL_COMPLEX_NAME);
    system(g_command);
    ret = executeCommand(g_command, "1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview systable vertex ptree -na %s |grep ErrorCodeDescription |wc -l", VL_GENERAL_COMPLEX_NAME);
    system(g_command);
    ret = executeCommand(g_command, "1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(g_stmt, nameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtCreateVertexLabel(g_stmt, VL_GENERAL_COMPLEX_JSON_PATH, VL_GENERAL_COMPLEX_CONFIG_PATH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DUPLICATE_TABLE, ret);
    ret = GmcDropVertexLabel(g_stmt, VL_GENERAL_COMPLEX_NAME);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, nameSpace);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

}
// 关键字大小写 驼峰
TEST_F(Pst_system_table_view, Other_062_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建namespace
    const char *nameSpace = (const char *)"nsp1";
    int ret = GmcCreateNamespace(g_stmt, nameSpace, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, nameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDropVertexLabel(g_stmt, VL_GENERAL_COMPLEX_NAME);
    ret = GtCreateVertexLabel(g_stmt, VL_GENERAL_COMPLEX_JSON_PATH, VL_GENERAL_COMPLEX_CONFIG_PATH);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 校验NSP_NAME 、NSP_TRX_TYPE、NSP_ISO_LEVEL
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview record GM_SYS_NSP -ns system | grep NSP_NAME");
    system(g_command);
    ret = executeCommand(g_command, "\"NSP_NAME\": \"nsp1\"");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 查询systable ptree格式
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview syStable vertex ptree -name %s |grep ErrorCodeDescription | wc -l", VL_GENERAL_COMPLEX_NAME);
    system(g_command);
    ret = executeCommand(g_command, "0");
    system(g_command);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview systable veRtex ptree -name %s |grep ErrorCodeDescription", VL_GENERAL_COMPLEX_NAME);
    ret = executeCommand(g_command, "ErrorCodeDescription: Not normal parameter value");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview systable vertex Ptree -name %s |grep ErrorCodeDescription", VL_GENERAL_COMPLEX_NAME);
    ret = executeCommand(g_command, "ErrorCodeDescription: Not normal parameter value");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview systable vertex ptree -Name %s |grep ErrorCodeDescription |wc -l", VL_GENERAL_COMPLEX_NAME);
    ret = executeCommand(g_command, "0");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(g_stmt, nameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtCreateVertexLabel(g_stmt, VL_GENERAL_COMPLEX_JSON_PATH, VL_GENERAL_COMPLEX_CONFIG_PATH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DUPLICATE_TABLE, ret);
    ret = GmcDropVertexLabel(g_stmt, VL_GENERAL_COMPLEX_NAME);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, nameSpace);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 关键字缺失
TEST_F(Pst_system_table_view, Other_062_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建namespace
    const char *nameSpace = (const char *)"nsp1";
    int ret = GmcCreateNamespace(g_stmt, nameSpace, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, nameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDropVertexLabel(g_stmt, VL_GENERAL_COMPLEX_NAME);
    ret = GtCreateVertexLabel(g_stmt, VL_GENERAL_COMPLEX_JSON_PATH, VL_GENERAL_COMPLEX_CONFIG_PATH);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 校验NSP_NAME 、NSP_TRX_TYPE、NSP_ISO_LEVEL
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview record GM_SYS_NSP -ns system | grep NSP_NAME");
    system(g_command);
    ret = executeCommand(g_command, "\"NSP_NAME\": \"nsp1\"");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 查询systable ptree格式
    
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview  vertex ptree -name %s |grep ErrorCodeDescription", VL_GENERAL_COMPLEX_NAME);
    ret = executeCommand(g_command, "ErrorCodeDescription: Not normal option");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview systable  ptree -name %s |grep ErrorCodeDescription", VL_GENERAL_COMPLEX_NAME);
    ret = executeCommand(g_command, "ErrorCodeDescription: Not normal parameter value");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview systable vertex  -name %s |grep ErrorCodeDescription", VL_GENERAL_COMPLEX_NAME);
    ret = executeCommand(g_command, "ErrorCodeDescription: Not normal parameter value");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview systable vertex ptree %s |grep ErrorCodeDescription", VL_GENERAL_COMPLEX_NAME);
    ret = executeCommand(g_command, "ErrorCodeDescription: Not normal parameter value");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(g_stmt, nameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtCreateVertexLabel(g_stmt, VL_GENERAL_COMPLEX_JSON_PATH, VL_GENERAL_COMPLEX_CONFIG_PATH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DUPLICATE_TABLE, ret);
    ret = GmcDropVertexLabel(g_stmt, VL_GENERAL_COMPLEX_NAME);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, nameSpace);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

}

// 查询不存在的vertexlabel
TEST_F(Pst_system_table_view, Other_062_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *no_exist = (const char *)"nsp100";
    // 创建namespace
    const char *nameSpace = (const char *)"nsp1";
    int ret = GmcCreateNamespace(g_stmt, nameSpace, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, nameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDropVertexLabel(g_stmt, VL_GENERAL_COMPLEX_NAME);
    ret = GtCreateVertexLabel(g_stmt, VL_GENERAL_COMPLEX_JSON_PATH, VL_GENERAL_COMPLEX_CONFIG_PATH);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 校验NSP_NAME 、NSP_TRX_TYPE、NSP_ISO_LEVEL
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview record GM_SYS_NSP -ns system | grep NSP_NAME");
    system(g_command);
    ret = executeCommand(g_command, "\"NSP_NAME\": \"nsp1\"");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 查询systable ptree格式
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview systable vertex precord -name %s |grep ErrorCodeDescription", no_exist);
    ret = executeCommand(g_command, "ErrorCodeDescription: Not normal parameter value");
    AW_MACRO_ASSERT_EQ_INT(-1, ret);

    // 查询systable precord格式
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview systable vertex precord -name %s |grep ErrorCodeDescription", no_exist);
    ret = executeCommand(g_command, "ErrorCodeDescription: Not normal parameter value");
    AW_MACRO_ASSERT_EQ_INT(-1, ret);

    ret = GmcUseNamespace(g_stmt, nameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtCreateVertexLabel(g_stmt, VL_GENERAL_COMPLEX_JSON_PATH, VL_GENERAL_COMPLEX_CONFIG_PATH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DUPLICATE_TABLE, ret);
    ret = GmcDropVertexLabel(g_stmt, VL_GENERAL_COMPLEX_NAME);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, nameSpace);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

void *systableview(void *arg)
{
    char temp_command[1024];
    int ret = 0;
    snprintf(temp_command, MAX_CMD_SIZE, "gmsysview record GM_SYS_NSP -ns system | grep NSP_NAME");
    system(temp_command);
    ret = executeCommand(temp_command, "\"NSP_NAME\": \"nsp1\"");
    if (ret != GMERR_OK) {
        TEST_ERROR("connect failed, ret = %d", ret);
        return (void *)(int64_t)ret;
    }
    // 查询NSP
    snprintf(temp_command, MAX_CMD_SIZE, "gmsysview record GM_SYS_NSP -ns system | grep NSP_TRX_TYPE");
    system(temp_command);
    ret = executeCommand(temp_command, "\"NSP_TRX_TYPE\": 0");
    if (ret != GMERR_OK) {
        TEST_ERROR("connect failed, ret = %d", ret);
        return (void *)(int64_t)ret;
    }
    // 查询NSP
    snprintf(temp_command, MAX_CMD_SIZE, "gmsysview record GM_SYS_NSP -ns system | grep NSP_ISO_LEVEL");
    system(temp_command);
    ret = executeCommand(temp_command, "\"NSP_ISO_LEVEL\": 1");
    if (ret != GMERR_OK) {
        TEST_ERROR("connect failed, ret = %d", ret);
        return (void *)(int64_t)ret;
    }

    // 查询systable ptree格式
    snprintf(temp_command, MAX_CMD_SIZE, "gmsysview systable vertex ptree -name %s |grep 'PROP_NAME:A' |wc -l", VL_GENERAL_COMPLEX_NAME);
    system(temp_command);
    ret = executeCommand(temp_command, "10");
    if (ret != GMERR_OK) {
        TEST_ERROR("connect failed, ret = %d", ret);
        return (void *)(int64_t)ret;
    }

    // 查询systable precord格式
    snprintf(temp_command, MAX_CMD_SIZE, "gmsysview systable vertex precord -name %s |grep 'vertex:' |wc -l", VL_GENERAL_COMPLEX_NAME);
    system(temp_command);
    ret = executeCommand(temp_command, "21");// 适配member key后减1
    if (ret != GMERR_OK) {
        TEST_ERROR("connect failed, ret = %d", ret);
        return (void *)(int64_t)ret;
    }
    return (void *)(int64_t)ret;
}

// 多个线程查询同一个视图
TEST_F(Pst_system_table_view, Other_062_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建namespace
    const char *nameSpace = (const char *)"nsp1";
    int ret = GmcCreateNamespace(g_stmt, nameSpace, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, nameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDropVertexLabel(g_stmt, VL_GENERAL_COMPLEX_NAME);
    ret = GtCreateVertexLabel(g_stmt, VL_GENERAL_COMPLEX_JSON_PATH, VL_GENERAL_COMPLEX_CONFIG_PATH);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int32_t threadNum = 5;

    pthread_t threadId[threadNum];
    for (int32_t i = 0; i < threadNum; i++) {
        ret = pthread_create(&threadId[i], NULL, systableview, NULL);
        ASSERT_EQ(GMERR_OK, ret);
    }

    for (int32_t i = 0; i < threadNum; i++) {
        void *threadRet;
        ret = pthread_join(threadId[i], &threadRet);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(GMERR_OK, (int)(int64_t)threadRet);
    }
    ret = GmcUseNamespace(g_stmt, nameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtCreateVertexLabel(g_stmt, VL_GENERAL_COMPLEX_JSON_PATH, VL_GENERAL_COMPLEX_CONFIG_PATH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DUPLICATE_TABLE, ret);
    ret = GmcDropVertexLabel(g_stmt, VL_GENERAL_COMPLEX_NAME);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, nameSpace);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}


typedef struct SystemTbThreadCtx {
    uint32_t type;          // 查询视图类型
    sem_t sem;              // 线程信号量
} SystemTbThreadCtx;

void *systableview2(void *arg)
{
    SystemTbThreadCtx vertexCfg = *(SystemTbThreadCtx *)arg;
    int32_t temp_type = vertexCfg.type;
    int ret = 0;
    char temp_command[1024];
    switch (temp_type)
    {
    case 0:
        // 查询NSP
        snprintf(temp_command, MAX_CMD_SIZE, "gmsysview record GM_SYS_NSP -ns system | grep NSP_NAME");
        system(temp_command);
        ret = executeCommand(temp_command, "\"NSP_NAME\": \"nsp1\"");
        if (ret != GMERR_OK) {
             TEST_ERROR("connect failed, ret = %d", ret);
        }
        break;
    case 1:
        // 查询NSP
        snprintf(temp_command, MAX_CMD_SIZE, "gmsysview record GM_SYS_NSP -ns system | grep NSP_TRX_TYPE");
        system(temp_command);
        ret = executeCommand(temp_command, "\"NSP_TRX_TYPE\": 0");
        if (ret != GMERR_OK) {
            TEST_ERROR("connect failed, ret = %d", ret);
        }
        break;
    case 2:
        // 查询NSP
        snprintf(temp_command, MAX_CMD_SIZE, "gmsysview record GM_SYS_NSP -ns system | grep NSP_ISO_LEVEL");
        system(temp_command);
        system("gmsysview record GM_SYS_NSP -ns system |head -n 20");
        ret = executeCommand(temp_command, "\"NSP_ISO_LEVEL\": 1");
        if (ret != GMERR_OK) {
            TEST_ERROR("connect failed, ret = %d", ret);
        }
        break;
    case 3:
        // 查询systable ptree格式
        snprintf(temp_command, MAX_CMD_SIZE, "gmsysview systable vertex ptree -name %s |grep 'PROP_NAME:A' |wc -l", VL_GENERAL_COMPLEX_NAME);
        system(temp_command);
        system("gmsysview systable vertex ptree -name vl_general_complex");
        ret = executeCommand(temp_command, "10");
        if (ret != GMERR_OK) {
            TEST_ERROR("connect failed, ret = %d", ret);
        }
        break;
    case 4:
        // 查询systable precord格式
        snprintf(temp_command, MAX_CMD_SIZE, "gmsysview systable vertex precord -name %s |grep 'vertex:' |wc -l", VL_GENERAL_COMPLEX_NAME);
        system(temp_command);
        system("gmsysview systable vertex precord -name vl_general_complex");
        ret = executeCommand(temp_command, "21");// 适配member key后减1
        if (ret != GMERR_OK) {
            TEST_ERROR("connect failed, ret = %d", ret);
        }
        break;
    default:
        break;
    }
    return (void *)(int64_t)ret;
}

// 多个线程查询不同视图
TEST_F(Pst_system_table_view, Other_062_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建namespace
    const char *nameSpace = (const char *)"nsp1";
    int ret = GmcCreateNamespace(g_stmt, nameSpace, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, nameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDropVertexLabel(g_stmt, VL_GENERAL_COMPLEX_NAME);
    ret = GtCreateVertexLabel(g_stmt, VL_GENERAL_COMPLEX_JSON_PATH, VL_GENERAL_COMPLEX_CONFIG_PATH);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int32_t threadNum = 5;
    pthread_t threadId[threadNum];
    SystemTbThreadCtx ctx[threadNum] = {0};
    for (int32_t i = 0; i < threadNum; i++) {
        ctx[i].type = i;
        ret = pthread_create(&threadId[i], NULL, systableview2, &ctx[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int32_t i = 0; i < threadNum; i++) {
        void *threadRet;
        ret = pthread_join(threadId[i], &threadRet);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(GMERR_OK, (int)(int64_t)threadRet);
    }
    ret = GmcUseNamespace(g_stmt, nameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtCreateVertexLabel(g_stmt, VL_GENERAL_COMPLEX_JSON_PATH, VL_GENERAL_COMPLEX_CONFIG_PATH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DUPLICATE_TABLE, ret);
    ret = GmcDropVertexLabel(g_stmt, VL_GENERAL_COMPLEX_NAME);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, nameSpace);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 构造内存满,校验视图
TEST_F(Pst_system_table_view, Other_062_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *vertex_label_schema = NULL;
    char labelName[]="vertex_01";
    char g_configJson[128] = "{\"max_record_count\" : 10000000}";
    readJanssonFile("./schema_file/Vertex_01.gmjson", &vertex_label_schema);
    ASSERT_NE((void *)NULL, vertex_label_schema);
    GmcDropVertexLabel(g_stmt, labelName);
    int ret = GmcCreateVertexLabel(g_stmt, vertex_label_schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(vertex_label_schema);
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_OUT_OF_MEMORY);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);

    ret = PstInsertValueFull(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(3);
    // 查询systable ptree格式
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview systable vertex ptree -name %s", labelName);
    system(g_command);
    // 查询systable ptree格式
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview systable vertex ptree -name %s |grep 'PROP_NAME:' |wc -l", labelName);
    system(g_command);
    ret = executeCommand(g_command, "20");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 查询systable precord格式
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview systable vertex precord -name %s", labelName);
    system(g_command);
    // 查询systable precord格式
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview systable vertex precord -name %s |grep 'vertex:' |wc -l", labelName);
    system(g_command);
    ret = executeCommand(g_command, "22");// 适配member key后减1 sys_check_version兼容加1
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 构造内存满,重启后，再次校验视图
TEST_F(Pst_system_table_view, Other_062_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *vertex_label_schema = NULL;
    char labelName[]="vertex_01";
    char g_configJson[128] = "{\"max_record_count\" : 10000000}";
    readJanssonFile("./schema_file/Vertex_01.gmjson", &vertex_label_schema);
    ASSERT_NE((void *)NULL, vertex_label_schema);
    GmcDropVertexLabel(g_stmt, labelName);
    int ret = GmcCreateVertexLabel(g_stmt, vertex_label_schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(vertex_label_schema);
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_OUT_OF_MEMORY);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);

    ret = PstInsertValueFull(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(3);

    // 落盘
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    ret = GtGmserverRestart(SIGKILL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testEnvInit(-1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询systable ptree格式
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview systable vertex ptree -name %s", labelName);
    system(g_command);
    // 查询systable ptree格式
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview systable vertex ptree -name %s |grep 'PROP_NAME:' |wc -l", labelName);
    system(g_command);
    ret = executeCommand(g_command, "20");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 查询systable precord格式
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview systable vertex precord -name %s", labelName);
    system(g_command);
    // 查询systable precord格式
    snprintf(g_command, MAX_CMD_SIZE, "gmsysview systable vertex precord -name %s |grep 'vertex:' |wc -l", labelName);
    system(g_command);
    ret = executeCommand(g_command, "22");// 适配member key后减1，sys_check_version兼容加1
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
