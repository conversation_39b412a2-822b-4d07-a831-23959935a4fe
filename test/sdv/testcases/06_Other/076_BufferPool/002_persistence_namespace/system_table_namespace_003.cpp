/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: namespace系统表异常场景测试
 * Create: 2022-04-07
 */
#include "gtest/gtest.h"
#include "system_table_namespace.h"

GmcConnT *g_conn = NULL, *g_connAsync = NULL;
GmcStmtT *g_stmt = NULL, *g_stmtAsync = NULL;
const char *const g_nspName = "nsp_test";
const char *const g_nspUserName = "abc";

class system_table_namespace_003 : public testing::Test {
public:
    static void SetUpTestCase();
    static void TearDownTestCase();

    virtual void SetUp();
    virtual void TearDown();
};

void system_table_namespace_003::SetUpTestCase()
{
    int ret = GtPersistenceInit();
    ASSERT_EQ(GMERR_OK, ret);
    ret = GtGmserverStart(false);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testEnvInit();
    ASSERT_EQ(GMERR_OK, ret);
}

void system_table_namespace_003::TearDownTestCase()
{
    int ret = testEnvClean();
    EXPECT_EQ(GMERR_OK, ret);
    ret = GtPersistenceUninit();
    EXPECT_EQ(GMERR_OK, ret);
}

void system_table_namespace_003::SetUp()
{
    int ret = GtConnectWithCsMode(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcCreateNamespace(g_stmt, g_nspName, g_nspUserName);
    ASSERT_EQ(GMERR_OK, ret);
}

void system_table_namespace_003::TearDown()
{
    int ret = GmcDropNamespace(g_stmt, g_nspName);
    EXPECT_TRUE(ret == GMERR_OK || ret == GMERR_UNDEFINED_OBJECT);
    testGmcDisconnect(g_conn, g_stmt);
    g_stmt = NULL;
    g_conn = NULL;
}

// 用户namespace与public namespace互相切换
TEST_F(system_table_namespace_003, Other_076_002_015)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int ret = GmcUseNamespace(g_stmt, g_nspName);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, "public");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_nspName);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, "public");
    ASSERT_EQ(GMERR_OK, ret);
}

// 用户namespace之间互相切换
TEST_F(system_table_namespace_003, Other_076_002_016)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    const char *nspName = "nsp_005";
    int ret = GmcCreateNamespace(g_stmt, nspName, g_nspUserName);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcUseNamespace(g_stmt, g_nspName);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, nspName);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_nspName);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, nspName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 切换至不存在的namespace
TEST_F(system_table_namespace_003, Other_076_002_017)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    const char *nspName = "nsp_008";
    int ret = GmcUseNamespace(g_stmt, nspName);
    ASSERT_EQ(GMERR_UNDEFINED_OBJECT, ret);
    ret = GmcUseNamespace(g_stmt, "public");
    ASSERT_EQ(GMERR_OK, ret);
}

// 在指定namespace下创建vertexLabel
TEST_F(system_table_namespace_003, Other_076_002_018)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    char *labelJson = NULL;
    char *labelName = (char *)"namespace_test";
    int ret = GmcUseNamespace(g_stmt, g_nspName);
    ASSERT_EQ(GMERR_OK, ret);

    readJanssonFile("schema_file/vl_namespace_test.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    ret = GmcCreateVertexLabel(g_stmt, labelJson, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
    free(labelJson);
}

// 同一个namespace下重复创建同名vertexLabel
TEST_F(system_table_namespace_003, Other_076_002_019)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    char *labelJson = NULL;
    char *labelName = (char *)"namespace_test";
    int ret = GmcUseNamespace(g_stmt, g_nspName);
    ASSERT_EQ(GMERR_OK, ret);

    readJanssonFile("schema_file/vl_namespace_test.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    ret = GmcCreateVertexLabel(g_stmt, labelJson, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, labelJson, NULL);
    ASSERT_NE(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
    free(labelJson);
}


// 对不同namespace下的verteLabel创建edgeLabel
TEST_F(system_table_namespace_003, Other_076_002_021)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    char *edgeLabelJson = NULL;
    char *dstVertexLabelJson = NULL;
    char *srcVertexLabelJson = NULL;
    char srcVertexLabelName[] = "T80";
    char dstVertexLabelName[] = "T90";
    void *edgeLabe = NULL;
    char edgeLabelName[] = "from_T80_to_T90";

    readJanssonFile("schema_file/vl_insert_src_t80.gmjson", &srcVertexLabelJson);
    ASSERT_NE((void *)NULL, srcVertexLabelJson);
    readJanssonFile("schema_file/vl_insert_dst_t90.gmjson", &dstVertexLabelJson);
    ASSERT_NE((void *)NULL, dstVertexLabelJson);
    readJanssonFile("schema_file/el_insert_t80_to_t90.gmjson", &edgeLabelJson);
    ASSERT_NE((void *)NULL, edgeLabelJson);

    // 切换用户namespace, 创建src_vertex_label
    int ret = GmcUseNamespace(g_stmt, g_nspName);
    ASSERT_EQ(GMERR_OK, ret);
    (void)GmcDropVertexLabel(g_stmt, srcVertexLabelName);
    ret = GmcCreateVertexLabel(g_stmt, srcVertexLabelJson, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // 切换public namespace, 创建dst_vertex_label
    ret = GmcUseNamespace(g_stmt, "public");
    ASSERT_EQ(GMERR_OK, ret);
    (void)GmcDropVertexLabel(g_stmt, dstVertexLabelName);
    ret = GmcCreateVertexLabel(g_stmt, dstVertexLabelJson, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // 创建edgelabel
    (void)GmcDropEdgeLabel(g_stmt, edgeLabelName);
    ret = GmcCreateEdgeLabel(g_stmt, edgeLabelJson, NULL);
    ASSERT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError("Undefined table. Namespace:1048577, label:T80, cataCache:vertexLabelCache.");
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, dstVertexLabelName);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_nspName);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, srcVertexLabelName);
    free(srcVertexLabelJson);
    free(dstVertexLabelJson);
    free(edgeLabelJson);
}

// 在namespace a下访问namespace b下的表
TEST_F(system_table_namespace_003, Other_076_002_022)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    GmcConnT *conn[1024] = {0};
    char *vertexLabelJson = NULL;
    char VertexLabelName[] = "testdml";
    const char *nspName = "nsp_test";

    (void)GmcDropNamespace(g_stmt, nspName);
    int ret = GmcCreateNamespace(g_stmt, nspName, g_nspUserName);
    ASSERT_EQ(GMERR_OK, ret);

    // 切换用户namespace，创建vertex_label
    ret = GmcUseNamespace(g_stmt, nspName);
    ASSERT_EQ(GMERR_OK, ret);
    readJanssonFile("schema_file/vl_all_type.gmjson", &vertexLabelJson);
    ASSERT_NE((void *)NULL, vertexLabelJson);
    (void)GmcDropVertexLabel(g_stmt, VertexLabelName);
    ret = GmcCreateVertexLabel(g_stmt, vertexLabelJson, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    free(vertexLabelJson);

    // 切换public namespace，访问vertex label
    ret = GmcUseNamespace(g_stmt, (char *)"public");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, VertexLabelName);
    ASSERT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError("Undefined table. Namespace:1048577, label:testdml, cataCache:vertexLabelCache.");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(g_stmt, VertexLabelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError("Undefined table. Namespace id:1048577, label name: testdml, cataCache name: vertexLabelCache.");
    ASSERT_EQ(GMERR_OK, ret);

    // 切换用户namespace，访问vertex label
    ret = GmcUseNamespace(g_stmt, nspName);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, VertexLabelName);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, nspName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 在指定namespace下对vertexLabel执行DML操作
TEST_F(system_table_namespace_003, Other_076_002_023)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int num = 100;
    char *vertexLabelJson = NULL;
    char VertexLabelName[] = "testdml";

    int ret = GmcUseNamespace(g_stmt, g_nspName);
    ASSERT_EQ(GMERR_OK, ret);
    readJanssonFile("schema_file/vl_all_type.gmjson", &vertexLabelJson);
    ASSERT_NE((void *)NULL, vertexLabelJson);
    (void)GmcDropVertexLabel(g_stmt, VertexLabelName);
    ret = GmcCreateVertexLabel(g_stmt, vertexLabelJson, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    free(vertexLabelJson);

    VlAllTypeDml(g_stmt, num);
    VlAllTypeFullTableScan(g_stmt, VertexLabelName, num);
    ret = GmcDropVertexLabel(g_stmt, VertexLabelName);
    ASSERT_EQ(GMERR_OK, ret);
}


// 重复创建、删除空的namespace
TEST_F(system_table_namespace_003, Other_076_002_026)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret = 0;
    const char *nspName = "nsp_021";
    for (int i = 0; i < 1000; i++) {
        ret = GmcCreateNamespace(g_stmt, nspName, g_nspUserName);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcDropNamespace(g_stmt, nspName);
        ASSERT_EQ(GMERR_OK, ret);
    }
}

// 重复创建namespace、创建表、打开表、删表和删除namespace
TEST_F(system_table_namespace_003, Other_076_002_027)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret = 0;
    const char *nspName = "nsp_026";
    char vertexLabelName[] = "testdml";
    char *labelJson = NULL;
    void *vertexLabel = NULL;
    char pkName[] = "Tree_PK";
    int startNum = 0;
    int endNum = 10;
    char *testStr1 = (char *)"testve";

    for (int i = 0; i < 100; i++) {
        ret = GmcCreateNamespace(g_stmt, nspName, g_nspUserName);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcUseNamespace(g_stmt, nspName);
        ASSERT_EQ(GMERR_OK, ret);
        GmcDropVertexLabel(g_stmt, vertexLabelName);
        readJanssonFile("schema_file/vl_all_type.gmjson", &labelJson);
        ASSERT_NE((void *)NULL, labelJson);
        ret = GmcCreateVertexLabel(g_stmt, labelJson, NULL);
        ASSERT_EQ(GMERR_OK, ret);
        free(labelJson);

        int num = 50;
        VlAllTypeDml(g_stmt, num);
        VlAllTypeFullTableScan(g_stmt, vertexLabelName, num);

        ret = GmcDropVertexLabel(g_stmt, vertexLabelName);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcDropNamespace(g_stmt, nspName);
        ASSERT_EQ(GMERR_OK, ret);
    }
}

// 创建64个namespace
TEST_F(system_table_namespace_003, Other_076_002_028)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    // SetUp中已经创建一个namespace
    int32_t nspNum = 63;
    int ret;

    // 创建namespace
    for (int32_t i = 0; i < nspNum; i++) {
        char nspName[128] = {0};
        int len = snprintf(nspName, sizeof(nspName), "nsp_%03d", i);
        ASSERT_GT(len, 0);
        char user[128] = {0};
        len = snprintf(user, sizeof(user), "user_%03d", i);
        ASSERT_GT(len, 0);
        ret = GmcCreateNamespace(g_stmt, nspName, NULL);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // 创建第65个失败
    ret = GmcCreateNamespace(g_stmt, "nsp_tmp", NULL);
    ASSERT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);

    // 使用namespace，验证创建成功
    for (int32_t i = 0; i < nspNum; i++) {
        char nspName[128] = {0};
        int len = snprintf(nspName, sizeof(nspName), "nsp_%03d", i);
        ASSERT_GT(len, 0);
        ret = GmcUseNamespace(g_stmt, nspName);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // 删除namespace
    for (int32_t i = 0; i < nspNum; i++) {
        char nspName[128] = {0};
        int len = snprintf(nspName, sizeof(nspName), "nsp_%03d", i);
        ASSERT_GT(len, 0);
        ret = GmcDropNamespace(g_stmt, nspName);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // 使用namespace，验证删除成功
    for (int32_t i = 0; i < nspNum; i++) {
        char nspName[128] = {0};
        int len = snprintf(nspName, sizeof(nspName), "nsp_%03d", i);
        ASSERT_GT(len, 0);
        ret = GmcUseNamespace(g_stmt, nspName);
        ASSERT_EQ(GMERR_UNDEFINED_OBJECT, ret);
    }
}

// 非同一个连接句柄下访问指定namespace下的表
TEST_F(system_table_namespace_003, Other_076_002_029)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    char VertexLabelName[] = "testdml";
    char *labelJson = NULL;
    void *vertexLabel = NULL;

    // 连接1，创建表
    int ret = GmcUseNamespace(g_stmt, g_nspName);
    ASSERT_EQ(GMERR_OK, ret);
    GmcDropVertexLabel(g_stmt, VertexLabelName);
    readJanssonFile("schema_file/vl_all_type.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    ret = GmcCreateVertexLabel(g_stmt, labelJson, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    free(labelJson);

    // 连接2，访问表
    ret = GtConnectWithCsMode(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, VertexLabelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError("Undefined table. Namespace id:1048577, label name: testdml, cataCache name: vertexLabelCache.");
    ASSERT_EQ(GMERR_OK, ret);

    // 连接1，访问表
    ret = GmcPrepareStmtByLabelName(g_stmt, VertexLabelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, VertexLabelName);
    ASSERT_EQ(GMERR_OK, ret);

    testGmcDisconnect(conn, stmt);
}
