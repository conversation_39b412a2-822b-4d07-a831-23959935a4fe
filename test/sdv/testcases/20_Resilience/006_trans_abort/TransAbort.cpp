#include "tools.h"

class trans_abort_test : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
public:
    virtual void SetUp();
    virtual void TearDown();
};

void trans_abort_test::SetUpTestCase()
{
    //权限校验改为强制模式
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"userPolicyMode=2\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"DBA=root:gmrule;gmips\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"compatibleV3=0\"");
    system("sh $TEST_HOME/tools/start.sh -f");
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("gmrule -c import_allowlist -f ./allowlist/allowlist1.gmuser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("gmrule -c import_policy -f ./allowlist/sys_privilege_policy.gmpolicy");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void trans_abort_test::TearDownTestCase()
{
    // 添加删除白名单操作
    int ret = system("gmrule -c remove_allowlist -f allowlist/allowlist1.gmuser");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    //恢复校验模式
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}

void trans_abort_test::SetUp()
{}

void trans_abort_test::TearDown()
{}

using namespace std;

// 4.启事务，中断，回滚
TEST_F(trans_abort_test, Resilience_006_001_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建连
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 启事务
    ret = StartTrans(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 中断
    int trx_id = GetValue("gmsysview -q V\\$STORAGE_TRX_DETAIL", "TRX_ID: ");
    snprintf(g_command, MAX_CMD_SIZE, "gmips -trxAbort %d", trx_id);
    snprintf(g_expect, MAX_CMD_SIZE, "abort trans %d successfully", trx_id);
    ret = executeCommand(g_command, g_expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 回滚
    ret = GmcTransRollBack(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 断连
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 5.启事务，中断，提交
TEST_F(trans_abort_test, Resilience_006_001_005)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_TRANSACTION_ROLLBACK);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建连
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 启事务
    ret = StartTrans(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 中断
    int trx_id = GetValue("gmsysview -q V\\$STORAGE_TRX_DETAIL", "TRX_ID: ");
    snprintf(g_command, MAX_CMD_SIZE, "gmips -trxAbort %d", trx_id);
    snprintf(g_expect, MAX_CMD_SIZE, "abort trans %d successfully", trx_id);
    ret = executeCommand(g_command, g_expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 提交
    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 6.启事务，一系列DML操作，中断，再一系列DML操作，回滚
TEST_F(trans_abort_test, Resilience_006_001_006)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_TRANSACTION_ROLLBACK);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(stmt, g_schemaJson, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = InsertValue(stmt, "simple_label", 1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = StartTrans(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 一系列DML操作
    ret = InsertValue(stmt, "simple_label", 2, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = MergeValue(stmt, "simple_label", 1, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = UpdateValue(stmt, "simple_label", 1, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DeleteValue(stmt, "simple_label", 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 中断
    int trx_id = GetValue("gmsysview -q V\\$STORAGE_TRX_DETAIL", "TRX_ID: ");
    snprintf(g_command, MAX_CMD_SIZE, "gmips -trxAbort %d", trx_id);
    snprintf(g_expect, MAX_CMD_SIZE, "abort trans %d successfully", trx_id);
    ret = executeCommand(g_command, g_expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 一系列DML操作
    ret = InsertValue(stmt, "simple_label", 3, 6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, ret);
    ret = MergeValue(stmt, "simple_label", 4, 7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, ret);
    ret = UpdateValue(stmt, "simple_label", 1, 8);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, ret);
    ret = DeleteValue(stmt, "simple_label", 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, ret);

    ret = GmcTransRollBack(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanValue(stmt, "simple_label", 1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, "simple_label");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 7.启事务，一系列DML操作，中断，再一系列DML操作，提交
TEST_F(trans_abort_test, Resilience_006_001_007)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_TRANSACTION_ROLLBACK);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(stmt, g_schemaJson, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = InsertValue(stmt, "simple_label", 1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = StartTrans(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 一系列DML操作
    ret = InsertValue(stmt, "simple_label", 2, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = MergeValue(stmt, "simple_label", 1, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = UpdateValue(stmt, "simple_label", 1, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DeleteValue(stmt, "simple_label", 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 中断
    int trx_id = GetValue("gmsysview -q V\\$STORAGE_TRX_DETAIL", "TRX_ID: ");
    snprintf(g_command, MAX_CMD_SIZE, "gmips -trxAbort %d", trx_id);
    snprintf(g_expect, MAX_CMD_SIZE, "abort trans %d successfully", trx_id);
    ret = executeCommand(g_command, g_expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 一系列DML操作
    ret = InsertValue(stmt, "simple_label", 3, 6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, ret);
    ret = MergeValue(stmt, "simple_label", 4, 7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, ret);
    ret = UpdateValue(stmt, "simple_label", 1, 8);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, ret);
    ret = DeleteValue(stmt, "simple_label", 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, ret);

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, ret);

    GmcConnT *conn_2 = NULL;
    GmcStmtT *stmt_2 = NULL;
    ret = testGmcConnect(&conn_2, &stmt_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanValue(stmt_2, "simple_label", 1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt_2, "simple_label");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn_2, stmt_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 8.启事务，中断，启事务
TEST_F(trans_abort_test, Resilience_006_001_008)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_TRANSACTION_ROLLBACK);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = StartTrans(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 中断
    int trx_id = GetValue("gmsysview -q V\\$STORAGE_TRX_DETAIL", "TRX_ID: ");
    snprintf(g_command, MAX_CMD_SIZE, "gmips -trxAbort %d", trx_id);
    snprintf(g_expect, MAX_CMD_SIZE, "abort trans %d successfully", trx_id);
    ret = executeCommand(g_command, g_expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = StartTrans(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 9.启事务，一系列DML操作，中断，断连，用其他连接读
TEST_F(trans_abort_test, Resilience_006_001_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn_1 = NULL;
    GmcStmtT *stmt_1 = NULL;
    GmcConnT *conn_2 = NULL;
    GmcStmtT *stmt_2 = NULL;
    int ret = testGmcConnect(&conn_1, &stmt_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(stmt_1, g_schemaJson, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = InsertValue(stmt_1, "simple_label", 1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = StartTrans(conn_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 一系列DML操作
    ret = InsertValue(stmt_1, "simple_label", 2, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = MergeValue(stmt_1, "simple_label", 1, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = UpdateValue(stmt_1, "simple_label", 1, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DeleteValue(stmt_1, "simple_label", 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 中断
    int trx_id = GetValue("gmsysview -q V\\$STORAGE_TRX_DETAIL", "TRX_ID: ");
    snprintf(g_command, MAX_CMD_SIZE, "gmips -trxAbort %d", trx_id);
    snprintf(g_expect, MAX_CMD_SIZE, "abort trans %d successfully", trx_id);
    ret = executeCommand(g_command, g_expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 断连
    ret = testGmcDisconnect(conn_1, stmt_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn_2, &stmt_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanValue(stmt_2, "simple_label", 1, 2);

    ret = GmcDropVertexLabel(stmt_2, "simple_label");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn_2, stmt_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 10.启事务，一系列DML操作，中断，断连，重连，启事务，DML操作一些之前/新数据，提交，校验
TEST_F(trans_abort_test, Resilience_006_001_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(stmt, g_schemaJson, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = InsertValue(stmt, "simple_label", 1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = StartTrans(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 一系列DML操作
    ret = InsertValue(stmt, "simple_label", 2, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = MergeValue(stmt, "simple_label", 1, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = UpdateValue(stmt, "simple_label", 1, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DeleteValue(stmt, "simple_label", 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 中断
    int trx_id = GetValue("gmsysview -q V\\$STORAGE_TRX_DETAIL", "TRX_ID: ");
    snprintf(g_command, MAX_CMD_SIZE, "gmips -trxAbort %d", trx_id);
    snprintf(g_expect, MAX_CMD_SIZE, "abort trans %d successfully", trx_id);
    ret = executeCommand(g_command, g_expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 断连
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重连
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = InsertValue(stmt, "simple_label", 3, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = StartTrans(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 一系列DML操作
    ret = InsertValue(stmt, "simple_label", 4, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = MergeValue(stmt, "simple_label", 5, 6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = UpdateValue(stmt, "simple_label", 5, 7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DeleteValue(stmt, "simple_label", 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanValue(stmt, "simple_label", 1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ScanValue(stmt, "simple_label", 3, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ScanValue(stmt, "simple_label", 4, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, "simple_label");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 11.启事务，视图查询所有事务，中断，视图查询，提交，视图查询，回滚，视图查询
TEST_F(trans_abort_test, Resilience_006_001_011)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_TRANSACTION_ROLLBACK);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(stmt, g_schemaJson, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = StartTrans(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 一系列DML操作
    ret = InsertValue(stmt, "simple_label", 4, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = MergeValue(stmt, "simple_label", 5, 6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = UpdateValue(stmt, "simple_label", 5, 7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DeleteValue(stmt, "simple_label", 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    cout << "----first time----" << endl;
    system("gmsysview -q V\\$STORAGE_TRX_DETAIL");
    system("gmsysview -q V\\$STORAGE_UNDO_STAT");

    // 中断
    int trx_id = GetValue("gmsysview -q V\\$STORAGE_TRX_DETAIL", "TRX_ID: ");
    snprintf(g_command, MAX_CMD_SIZE, "gmips -trxAbort %d", trx_id);
    snprintf(g_expect, MAX_CMD_SIZE, "abort trans %d successfully", trx_id);
    ret = executeCommand(g_command, g_expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    cout << "----second time----" << endl;
    system("gmsysview -q V\\$STORAGE_TRX_DETAIL");
    system("gmsysview -q V\\$STORAGE_UNDO_STAT");

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, ret);

    cout << "----third time----" << endl;
    system("gmsysview -q V\\$STORAGE_TRX_DETAIL");
    system("gmsysview -q V\\$STORAGE_UNDO_STAT");

    ret = GmcTransRollBack(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    cout << "----fourth time----" << endl;
    system("gmsysview -q V\\$STORAGE_TRX_DETAIL");
    system("gmsysview -q V\\$STORAGE_UNDO_STAT");
    // 删表
    ret = GmcDropVertexLabel(stmt, "simple_label");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 12.写数据，readonly设置为true，启事务，读数据，中断，update数据，回滚
TEST_F(trans_abort_test, Resilience_006_001_012)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_TRANSACTION_ROLLBACK);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(stmt, g_schemaJson, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = InsertValue(stmt, "simple_label", 1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcTxConfigT config{0};
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = true;
    config.trxType = GMC_DEFAULT_TRX;
    ret = GmcTransStart(conn, &config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ScanValue(stmt, "simple_label", 1, 2);

    // 中断
    int trx_id = GetValue("gmsysview -q V\\$STORAGE_TRX_DETAIL", "TRX_ID: ");
    snprintf(g_command, MAX_CMD_SIZE, "gmips -trxAbort %d", trx_id);
    snprintf(g_expect, MAX_CMD_SIZE, "abort trans %d successfully", trx_id);
    ret = executeCommand(g_command, g_expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = UpdateValue(stmt, "simple_label", 1, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, ret);

    ret = GmcTransRollBack(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, "simple_label");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

const char *subtreeJson = R"(
    {
        "con_0_F0": 1
    }
)";
const char *expect_result;
// 13.开启yang事务，一系列DML操作，中断，再一系列DML操作，回滚
#ifdef FEATURE_YANG
TEST_F(trans_abort_test, Resilience_006_001_013)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_TRANSACTION_ROLLBACK);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    AsyncUserDataT data = {0};
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcConnT *conn_async = NULL;
    GmcStmtT *stmt_async = NULL;
    ret = testGmcConnect(&conn_async, &stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    ASSERT_EQ(GMERR_OK, ret);

    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = g_namespace;
    nspCfg.tablespaceName = NULL;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观

    ret = GmcCreateNamespaceWithCfgAsync(stmt_async, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = system("gmrule -c import_policy -f ./allowlist/allowlist1_namesps.gmpolicy");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Yang事务配置为乐观+可重复读，需要和namespace保持一致
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = GmcUseNamespaceAsync(stmt_async, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    TestyangallocAllstmt(conn_async);

    ret = GmcUseNamespace(stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(stmt, g_container0, g_yangLabelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启事务
    ret = testTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    // 设置根节点container_0层
    ret = testGmcPrepareStmtByLabelName(stmt_container_0, "container_0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *conRootNode = NULL;
    ret = GmcGetRootNode(stmt_container_0, &conRootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置container_1字段值
    uint32_t con_0_F0_value = 1;
    ret = YangSetProperty(conRootNode, "con_0_F0", &con_0_F0_value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 中断
    system("gmsysview -q V\\$STORAGE_TRX_DETAIL");
    int trx_id = GetValue("gmsysview -q V\\$STORAGE_TRX_DETAIL", "TRX_ID: ");
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -trxAbort %d", g_toolPath, trx_id);
    cout << endl << g_command << endl;
    system(g_command);

    // 设置child-Con节点
    GmcNodeT *ConConChildNode = NULL;
    ret = GmcYangEditChildNode(conRootNode, "container_1", GMC_OPERATION_INSERT, &ConConChildNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container_1字段值
    uint32_t con_1_F0_value = 2;
    ret = YangSetProperty(ConConChildNode, "con_1_F0", &con_1_F0_value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, data.status);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = GmcTransRollBackAsync(conn_async, trans_rollback_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 开启事务
    ret = testTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 过滤条件为default
    SubtreeFilterCbParam Data_2 = {0};
    Data_2.expectReplyJson = "~";
    Data_2.expectStatus = GMERR_OK;
    Data_2.step = 0;
    GmcSubtreeFilterItemT filter_default = {.rootName = "container_0",
        .subtree = {.json = subtreeJson},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = GMC_SUBTREE_FILTER_DEFAULT};
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter_default,
    };

    ret = GmcYangSubtreeFilterExecuteAsync(stmt_container_0, &filters, NULL, AsyncSubtreeFilterCb, &Data_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data_2.expectStatus);

    // 事务提交
    ret = testTransCommitAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, "container_0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步删除namespace
    ret = GmcDropNamespaceAsync(stmt_async, g_namespace, drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn_async, stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 14.开启yang事务，一系列DML操作，中断，再一系列DML操作，提交
TEST_F(trans_abort_test, Resilience_006_001_014)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_TRANSACTION_ROLLBACK);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    AsyncUserDataT data = {0};
    int ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcConnT *conn_async = NULL;
    GmcStmtT *stmt_async = NULL;
    ret = testGmcConnect(&conn_async, &stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    ASSERT_EQ(GMERR_OK, ret);

    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = g_namespace;
    nspCfg.tablespaceName = NULL;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观

    ret = GmcCreateNamespaceWithCfgAsync(stmt_async, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = system("gmrule -c import_policy -f ./allowlist/allowlist1_namesps.gmpolicy");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Yang事务配置为乐观+可重复读，需要和namespace保持一致
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = GmcUseNamespaceAsync(stmt_async, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    TestyangallocAllstmt(conn_async);

    ret = GmcUseNamespace(stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(stmt, g_container0, g_yangLabelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启事务
    ret = testTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    // 设置根节点container_0层
    ret = testGmcPrepareStmtByLabelName(stmt_container_0, "container_0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *conRootNode = NULL;
    ret = GmcGetRootNode(stmt_container_0, &conRootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置container_0字段值
    uint32_t con_0_F0_value = 1;
    ret = YangSetProperty(conRootNode, "con_0_F0", &con_0_F0_value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 中断
    system("gmsysview -q V\\$STORAGE_TRX_DETAIL");
    int trx_id = GetValue("gmsysview -q V\\$STORAGE_TRX_DETAIL", "TRX_ID: ");
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -trxAbort %d", g_toolPath, trx_id);
    cout << endl << g_command << endl;
    system(g_command);

    GmcNodeT *ConConChildNode = NULL;
    ret = GmcYangEditChildNode(conRootNode, "container_1", GMC_OPERATION_INSERT, &ConConChildNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container_1字段值
    uint32_t con_1_F0_value = 2;
    ret = YangSetProperty(ConConChildNode, "con_1_F0", &con_1_F0_value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt_container_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, data.status);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 事务提交
    ret = testTransCommitAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, ret);

    // 回滚事务
    ret = GmcTransRollBackAsync(conn_async, trans_rollback_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 开启事务
    ret = testTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 过滤条件为default
    SubtreeFilterCbParam Data_2 = {0};
    Data_2.expectReplyJson = "~";
    Data_2.expectStatus = GMERR_OK;
    Data_2.step = 0;
    GmcSubtreeFilterItemT filter_default = {.rootName = "container_0",
        .subtree = {.json = subtreeJson},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = GMC_SUBTREE_FILTER_DEFAULT};
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter_default,
    };

    ret = GmcYangSubtreeFilterExecuteAsync(stmt_container_0, &filters, NULL, AsyncSubtreeFilterCb, &Data_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data_2.expectStatus);

    // 事务提交
    ret = testTransCommitAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, "container_0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步删除namespace
    ret = GmcDropNamespaceAsync(stmt_async, g_namespace, drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn_async, stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
#endif

// 15.中断一个已提交的事务
TEST_F(trans_abort_test, Resilience_006_001_015)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_NO_ACTIVE_TRANSACTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建连
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 启事务
    ret = StartTrans(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int trx_id = GetValue("gmsysview -q V\\$STORAGE_TRX_DETAIL", "TRX_ID: ");
    // 提交事务
    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 中断
    snprintf(g_command, MAX_CMD_SIZE, "gmips -trxAbort %d", trx_id);
    ret = executeCommand(g_command, "ret = 1006002");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 断连
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 16.中断一个已回滚的事务
TEST_F(trans_abort_test, Resilience_006_001_016)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_NO_ACTIVE_TRANSACTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建连
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 启事务
    ret = StartTrans(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int trx_id = GetValue("gmsysview -q V\\$STORAGE_TRX_DETAIL", "TRX_ID: ");
    // 回滚
    ret = GmcTransRollBack(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 中断
    snprintf(g_command, MAX_CMD_SIZE, "gmips -trxAbort %d", trx_id);
    ret = executeCommand(g_command, "ret = 1006002");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 断连
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 17.开启两个事务，操作不同表，一个一系列DML操作期间中断，另一个一系列DML操作，然后中断后回滚，另一个提交
TEST_F(trans_abort_test, Resilience_006_001_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建连
    GmcConnT *conn_1 = NULL;
    GmcStmtT *stmt_1 = NULL;
    GmcConnT *conn_2 = NULL;
    GmcStmtT *stmt_2 = NULL;
    int ret = testGmcConnect(&conn_1, &stmt_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn_2, &stmt_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(stmt_1, g_schemaJson, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt_2, g_schemaJson2, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = InsertValue(stmt_1, "simple_label", 1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InsertValue(stmt_2, "simple_label_2", 1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启事务
    ret = StartTrans(conn_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int trx_id_1 = GetValue("gmsysview -q V\\$STORAGE_TRX_DETAIL", "TRX_ID: ");
    ret = StartTrans(conn_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 事务1一系列DML操作
    ret = InsertValue(stmt_1, "simple_label", 2, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = MergeValue(stmt_1, "simple_label", 1, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = UpdateValue(stmt_1, "simple_label", 1, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DeleteValue(stmt_1, "simple_label", 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 事务2一系列DML操作
    ret = InsertValue(stmt_2, "simple_label_2", 2, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = MergeValue(stmt_2, "simple_label_2", 1, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = UpdateValue(stmt_2, "simple_label_2", 1, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DeleteValue(stmt_2, "simple_label_2", 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 事务1中断
    snprintf(g_command, MAX_CMD_SIZE, "gmips -trxAbort %d", trx_id_1);
    snprintf(g_expect, MAX_CMD_SIZE, "abort trans %d successfully", trx_id_1);
    ret = executeCommand(g_command, g_expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 事务1回滚
    ret = GmcTransRollBack(conn_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 事务2提交
    ret = GmcTransCommit(conn_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    ret = ScanValue(stmt_1, "simple_label", 1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ScanValue(stmt_2, "simple_label_2", 2, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表、断连
    ret = GmcDropVertexLabel(stmt_1, "simple_label");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt_2, "simple_label_2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn_1, stmt_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn_2, stmt_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 18.开启两个事务，操作相同表的相同数据，一个一系列DML操作期间中断，另一个一系列DML操作，然后中断的回滚，另一个提交
TEST_F(trans_abort_test, Resilience_006_001_018)
{
    char errorMsg1[128] = {}, errorMsg2[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_TRANSACTION_ROLLBACK);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建连
    GmcConnT *conn_1 = NULL;
    GmcStmtT *stmt_1 = NULL;
    GmcConnT *conn_2 = NULL;
    GmcStmtT *stmt_2 = NULL;
    int ret = testGmcConnect(&conn_1, &stmt_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn_2, &stmt_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(stmt_1, g_schemaJson, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = InsertValue(stmt_1, "simple_label", 1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启事务
    ret = StartTrans(conn_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int trx_id_1 = 0;
    ret = StartTrans(conn_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int new_values[1] = {trx_id_1};
    int trx_id_2 = 0;
    // 事务1一系列DML操作
    ret = InsertValue(stmt_1, "simple_label", 2, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = MergeValue(stmt_1, "simple_label", 1, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = UpdateValue(stmt_1, "simple_label", 1, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DeleteValue(stmt_1, "simple_label", 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    trx_id_1 = GetValue("gmsysview -q V\\$STORAGE_TRX_DETAIL -f HOLD_LOCK_NUM=3", "TRX_ID: ");
    // 事务2一系列DML操作
    ret = MergeValue(stmt_2, "simple_label", 2, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_LOCK_NOT_AVAILABLE, ret);
    ret = UpdateValue(stmt_2, "simple_label", 1, 6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, ret);
    ret = DeleteValue(stmt_2, "simple_label", 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, ret);
    trx_id_2 = GetNewValue("gmsysview -q V\\$STORAGE_TRX_DETAIL -f HOLD_LOCK_NUM=1", "TRX_ID: ", 1, new_values);
    // 事务1中断
    snprintf(g_command, MAX_CMD_SIZE, "gmips -trxAbort %d", trx_id_1);
    snprintf(g_expect, MAX_CMD_SIZE, "abort trans %d successfully", trx_id_1);
    ret = executeCommand(g_command, g_expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 事务2一系列DML操作
    ret = MergeValue(stmt_2, "simple_label", 2, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, ret);
    ret = MergeValue(stmt_2, "simple_label", 3, 6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, ret);
    ret = UpdateValue(stmt_2, "simple_label", 1, 6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, ret);
    ret = DeleteValue(stmt_2, "simple_label", 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, ret);
    // 事务1回滚
    ret = GmcTransRollBack(conn_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 事务2中断
    snprintf(g_command, MAX_CMD_SIZE, "gmips -trxAbort %d", trx_id_2);
    snprintf(g_expect, MAX_CMD_SIZE, "abort trans %d successfully", trx_id_2);
    ret = executeCommand(g_command, g_expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 事务2回滚
    ret = GmcTransRollBack(conn_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    ret = ScanValue(stmt_1, "simple_label", 1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表、断连
    ret = GmcDropVertexLabel(stmt_1, "simple_label");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn_1, stmt_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn_2, stmt_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 19.开启两个事务，操作相同表相同数据，一个从第一至第N一系列DML操作，另一个从第N至第一一系列DML操作，都在操作不到一半时中断并提交
TEST_F(trans_abort_test, Resilience_006_001_019)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_TRANSACTION_ROLLBACK);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建连
    GmcConnT *conn_1 = NULL;
    GmcStmtT *stmt_1 = NULL;
    GmcConnT *conn_2 = NULL;
    GmcStmtT *stmt_2 = NULL;
    int ret = testGmcConnect(&conn_1, &stmt_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn_2, &stmt_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(stmt_1, g_schemaJson, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入5条数据
    ret = InsertValue(stmt_1, "simple_label", 1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InsertValue(stmt_1, "simple_label", 2, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InsertValue(stmt_1, "simple_label", 3, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InsertValue(stmt_1, "simple_label", 4, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InsertValue(stmt_1, "simple_label", 5, 6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启事务
    ret = StartTrans(conn_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int trx_id_1 = 0;
    ret = StartTrans(conn_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int new_values[1] = {trx_id_1};
    int trx_id_2 = 0;
    // 事务1 update 1~5,2后中断
    ret = UpdateValue(stmt_1, "simple_label", 1, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = UpdateValue(stmt_1, "simple_label", 2, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    trx_id_1 = GetValue("gmsysview -q V\\$STORAGE_TRX_DETAIL -f HOLD_LOCK_NUM=3", "TRX_ID: ");
    snprintf(g_command, MAX_CMD_SIZE, "gmips -trxAbort %d", trx_id_1);
    snprintf(g_expect, MAX_CMD_SIZE, "abort trans %d successfully", trx_id_1);
    ret = executeCommand(g_command, g_expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = UpdateValue(stmt_1, "simple_label", 3, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, ret);
    ret = UpdateValue(stmt_1, "simple_label", 4, 6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, ret);
    ret = UpdateValue(stmt_1, "simple_label", 5, 7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, ret);
    // 事务2 update 5~1,4后中断
    ret = UpdateValue(stmt_2, "simple_label", 5, 7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = UpdateValue(stmt_2, "simple_label", 4, 6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    trx_id_2 = GetNewValue("gmsysview -q V\\$STORAGE_TRX_DETAIL -f HOLD_LOCK_NUM=3", "TRX_ID: ", 1, new_values);
    snprintf(g_command, MAX_CMD_SIZE, "gmips -trxAbort %d", trx_id_2);
    snprintf(g_expect, MAX_CMD_SIZE, "abort trans %d successfully", trx_id_2);
    ret = executeCommand(g_command, g_expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = UpdateValue(stmt_2, "simple_label", 3, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, ret);
    ret = UpdateValue(stmt_2, "simple_label", 2, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, ret);
    ret = UpdateValue(stmt_2, "simple_label", 1, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, ret);
    // 事务1回滚
    ret = GmcTransRollBack(conn_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 事务2回滚
    ret = GmcTransRollBack(conn_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    ret = ScanValue(stmt_1, "simple_label", 1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ScanValue(stmt_1, "simple_label", 2, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ScanValue(stmt_1, "simple_label", 3, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ScanValue(stmt_1, "simple_label", 4, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ScanValue(stmt_1, "simple_label", 5, 6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表、断连
    ret = GmcDropVertexLabel(stmt_1, "simple_label");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn_1, stmt_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn_2, stmt_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
