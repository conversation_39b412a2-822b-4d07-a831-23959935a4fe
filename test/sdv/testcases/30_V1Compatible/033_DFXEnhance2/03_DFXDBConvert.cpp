/*
001 空表含自定义数据类型DB_BkpPhyEx导出小端数据后，TPC_Sysview接口转换txt文档，校验表名，预期成功
002 普通表写数据DB_BkpPhyEx导出小端数据后，TPC_Sysview接口转换txt文档，校验表名和数据条数，预期成功
003 含自定义数据类型写数据DB_BkpPhyEx导出小端数据后，TPC_Sysview接口转换txt文档，校验表名和数据条数，预期成功
004 临时表写数据DB_BkpPhyEx导出小端数据后，TPC_Sysview接口转换txt文档，校验表名，预期失败
005 DB只含描述信息DB_BkpPhyEx导出小端数据后，TPC_Sysview接口转换txt文档，校验描述信息，预期成功
006 空表含自定义数据类型TPC_Sysview导出，TPC_Sysview接口转换txt文档，预期转换失败
007 空表DB_BkpPhyEx导出小端数据后，TPC_Sysview接口转换txt文档，校验表名，预期成功
008 写一条数据DB_BkpPhyEx导出小端数据后转换校验，再次写入一条DB_BkpPhyEx导出小端数据后转换，预期数据有变化
009 写数据DB_BkpPhyEx导出小端数据后转换，修改最大记录数后再导出转换，预期最大记录数修改成功
010 写数据DB_BkpPhyEx导出小端数据后转换，更新后导出转换，预期成功
011 写数据DB_BkpPhyEx导出小端数据后转换，删除后导出转换，预期没数据
012 含默认值导出小端数据后转换，预期成功
013 含65535张表导出小端数据后转换，校验表个数，预期成功
014 含大对象的DB_BkpPhyEx导出小端数据后转换，校验表和数据，预期成功
015 表不含索引DB_BkpPhyEx导出小端数据后转换，校验表和数据，预期成功
016 表含hash，TTree，sort索引，DB_BkpPhyEx导出小端数据后转换，预期成功
017 空表含自定义数据类型DB_BkpPhyEx导出大端数据后，TPC_Sysview接口转换txt文档，校验表名，预期成功
018 普通表写数据DB_BkpPhyEx导出大端数据后，TPC_Sysview接口转换txt文档，校验表名和数据条数，预期成功
019 含自定义数据类型写数据DB_BkpPhyEx导出大端数据后，TPC_Sysview接口转换txt文档，校验表名和数据条数，预期成功
020 临时表写数据DB_BkpPhyEx导出大端数据后，TPC_Sysview接口转换txt文档，校验表名，预期失败
021 DB只含描述信息DB_BkpPhyEx导出大端数据后，TPC_Sysview接口转换txt文档，校验描述信息，预期成功
022 空表DB_BkpPhyEx导出大端数据后，TPC_Sysview接口转换txt文档，校验表名，预期成功
023 写一条数据DB_BkpPhyEx导出大端数据后转换校验，再次写入一条DB_BkpPhyEx导出大端数据后转换，预期数据有变化
024 写数据DB_BkpPhyEx导出大端数据后转换，修改最大记录数后再导出转换，预期最大记录数修改成功
025 写数据DB_BkpPhyEx导出大端数据后转换，更新后导出转换，预期成功
026 写数据DB_BkpPhyEx导出大端数据后转换，删除后导出转换，预期没数据
027 含默认值导出大端数据后转换，预期成功
028 含65535张表导出大端数据后转换，校验表个数，预期成功
029 含大对象的DB_BkpPhyEx导出大端数据后转换，校验表和数据，预期成功
030 表不含索引DB_BkpPhyEx导出大端数据后转换，校验表和数据，预期成功
031 表含hash，TTree，sort索引，DB_BkpPhyEx导出大端数据后转换，预期成功
*/
#include "DFXEnhanceTwo.h"

class DBConvert : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void DBConvert::SetUpTestCase()
{}

void DBConvert::TearDownTestCase()
{}

void DBConvert::SetUp()
{
    // 初始化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestDB_Init());
    system("echo 'This is persistent DB' > ./perFilePath/perDbPath");
    system("echo 'This is normal DB' > ./filePath/normalDbPath");
    system("rm -rf ./filePath/*.txt;rm -rf ./filePath/*.db;rm -rf ./filePath/output*");
    system("rm -rf ./perFilePath/*.txt");
    AW_CHECK_LOG_BEGIN();
    int ret;
    ret = DB_CreateDB((VOS_UINT8 *)g_dbName, NULL, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_dbName, &g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    g_perDbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = DB_CreateDB((VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, &g_perDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)g_perDbName, &g_perDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}

void DBConvert::TearDown()
{
    system("echo 'This is persistent DB' > ./perFilePath/perDbPath");
    system("echo 'This is normal DB' > ./filePath/normalDbPath");
    system("rm -rf ./filePath/*.txt;rm -rf ./filePath/*.db;rm -rf ./filePath/output*");
    system("rm -rf ./perFilePath/*.txt");
    // CLoseDB
    int ret = DB_CloseDB(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_CloseDB(g_perDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // DropDB
    ret = DB_DropDB((VOS_UINT8 *)g_dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_perDbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestDB_UnInit());
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_UnInit());
    AW_CHECK_LOG_END();
}

// 表配置修改为65535
class DBConfigConvert : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void DBConfigConvert::SetUpTestCase()
{}

void DBConfigConvert::TearDownTestCase()
{}

void DBConfigConvert::SetUp()
{
    AW_CHECK_LOG_BEGIN();
}

void DBConfigConvert::TearDown()
{
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestDB_UnInit());
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_UnInit());
    AW_CHECK_LOG_END();
}

// 001 空表含自定义数据类型DB_BkpPhyEx导出小端数据后，TPC_Sysview接口转换txt文档，校验表名，预期成功
TEST_F(DBConvert, V1Com_033_DBConvert_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/normal_001_with_custom.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step1 DB_BkpPhyEx导出小端数据 ========= */
    char filePath[64] = "./filePath/export.db";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    ret = DB_BkpPhyEx(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 转换 ========= */
    VOS_UINT8 *pucResult = NULL;
    DB_TPC_SYSVIEW_ARGS_STRU viewArgs = {0};
    viewArgs.pucFilePath = (VOS_UINT8 *)filePath;
    viewArgs.pucOutTxtPath = (VOS_UINT8 *)"./filePath/convertFile.txt";
    ret = TPC_Sysview(DB_TPC_SYSVIEW_CONVERT_DB_FILE, &viewArgs, &pucResult);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 文件比较
    ret = CompareFile("./dataFile/convert001.txt", "./filePath/convertFile.txt");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TPC_FreeSysviewResult(&pucResult);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002 普通表写数据DB_BkpPhyEx导出小端数据后，TPC_Sysview接口转换txt文档，校验表名和数据条数，预期成功
TEST_F(DBConvert, V1Com_033_DBConvert_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/normal_002_with_all_type.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        DbSelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhyEx导出小端数据 ========= */
    char filePath[64] = "./filePath/export.db";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    ret = DB_BkpPhyEx(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 转换 ========= */
    VOS_UINT8 *pucResult = NULL;
    DB_TPC_SYSVIEW_ARGS_STRU viewArgs = {0};
    viewArgs.pucFilePath = (VOS_UINT8 *)filePath;
    viewArgs.pucOutTxtPath = (VOS_UINT8 *)"./filePath/convertFile.txt";
    ret = TPC_Sysview(DB_TPC_SYSVIEW_CONVERT_DB_FILE, &viewArgs, &pucResult);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 文件比较
    ret = CompareFile("./dataFile/convert002.txt", "./filePath/convertFile.txt");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TPC_FreeSysviewResult(&pucResult);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003 含自定义数据类型写数据DB_BkpPhyEx导出小端数据后，TPC_Sysview接口转换txt文档，校验表名和数据条数，预期成功
TEST_F(DBConvert, V1Com_033_DBConvert_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/normal_001_with_custom.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllTypeCustom(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        DbSelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum, true);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhyEx导出小端数据 ========= */
    char filePath[64] = "./filePath/export.db";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    ret = DB_BkpPhyEx(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 转换 ========= */
    VOS_UINT8 *pucResult = NULL;
    DB_TPC_SYSVIEW_ARGS_STRU viewArgs = {0};
    viewArgs.pucFilePath = (VOS_UINT8 *)filePath;
    viewArgs.pucOutTxtPath = (VOS_UINT8 *)"./filePath/convertFile.txt";
    ret = TPC_Sysview(DB_TPC_SYSVIEW_CONVERT_DB_FILE, &viewArgs, &pucResult);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 文件比较
    ret = CompareFile("./dataFile/convert003.txt", "./filePath/convertFile.txt");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TPC_FreeSysviewResult(&pucResult);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004 临时表写数据DB_BkpPhyEx导出小端数据后，TPC_Sysview接口转换txt文档，校验表名，预期失败
TEST_F(DBConvert, V1Com_033_DBConvert_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/temp_001_with_all_type.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        DbSelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhyEx导出小端数据 ========= */
    char filePath[64] = "./filePath/export.db";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    ret = DB_BkpPhyEx(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 转换 ========= */
    VOS_UINT8 *pucResult = NULL;
    DB_TPC_SYSVIEW_ARGS_STRU viewArgs = {0};
    viewArgs.pucFilePath = (VOS_UINT8 *)filePath;
    viewArgs.pucOutTxtPath = (VOS_UINT8 *)"./filePath/convertFile.txt";
    ret = TPC_Sysview(DB_TPC_SYSVIEW_CONVERT_DB_FILE, &viewArgs, &pucResult);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验表名
    char command[1024];
    (void)sprintf_s(command, sizeof(command), "cat ./filePath/convertFile.txt");
    ret = executeCommand(command, "tbname: tempLabelNameA");
    EXPECT_NE(DB_SUCCESS_V1, ret);

    // 文件比较
    ret = CompareFile("./dataFile/convert004.txt", "./filePath/convertFile.txt");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TPC_FreeSysviewResult(&pucResult);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005 DB只含描述信息DB_BkpPhyEx导出小端数据后，TPC_Sysview接口转换txt文档，校验描述信息，预期成功
TEST_F(DBConvert, V1Com_033_DBConvert_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 设置DB描述信息
    char setDescInfo[] = "This is an empty DataBase.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_dbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step1 DB_BkpPhyEx导出小端数据 ========= */
    char filePath[64] = "./filePath/export.db";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    ret = DB_BkpPhyEx(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 转换 ========= */
    VOS_UINT8 *pucResult = NULL;
    DB_TPC_SYSVIEW_ARGS_STRU viewArgs = {0};
    viewArgs.pucFilePath = (VOS_UINT8 *)filePath;
    viewArgs.pucOutTxtPath = (VOS_UINT8 *)"./filePath/convertFile.txt";
    ret = TPC_Sysview(DB_TPC_SYSVIEW_CONVERT_DB_FILE, &viewArgs, &pucResult);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    
    FileEndAddSpaceLine("./filePath/convertFile.txt");
   
    // 文件比较
    ret = CompareFile("./dataFile/convert005.txt", "./filePath/convertFile.txt");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TPC_FreeSysviewResult(&pucResult);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006 空表含自定义数据类型TPC_Sysview导出，TPC_Sysview接口转换txt文档，预期转换失败
TEST_F(DBConvert, V1Com_033_DBConvert_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/normal_001_with_custom.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step1 DB_BkpPhyEx导出小端数据 ========= */
    // 获取表名
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs = {0};
    VOS_UINT8 *result = NULL;
    sysviewArgs.pucFilePath = (VOS_UINT8 *)"./filePath/output.txt";
    sysviewArgs.ulDbId = g_dbId;
    ret = TPC_Sysview(DB_TPC_SYSVIEW_EXPORT_TXT, &sysviewArgs, &result);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取当前目录的绝对路径
    char buf[1024];
    if (getcwd(buf, sizeof(buf)) == NULL) {
        perror("getcwd() error");
        exit(EXIT_FAILURE);
    }
    AW_FUN_Log(LOG_INFO, "absolute path:%s", buf);
    // 字符串匹配
    char command[1024];
    (void)sprintf_s(command, sizeof(command), "Db file(%s/filePath/output.txt) does not v5 format.", buf);
    AW_FUN_Log(LOG_INFO, "string info:%s", command);

    /* ========= step2 转换 ========= */
    VOS_UINT8 *pucResult = NULL;
    DB_TPC_SYSVIEW_ARGS_STRU viewArgs = {0};
    viewArgs.pucFilePath = (VOS_UINT8 *)"./filePath/output.txt";
    viewArgs.pucOutTxtPath = (VOS_UINT8 *)"./filePath/convertFile.txt";
    ret = TPC_Sysview(DB_TPC_SYSVIEW_CONVERT_DB_FILE, &viewArgs, &pucResult);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATAFILE, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_SysviewGetLastError(command, false));

    TPC_FreeSysviewResult(&result);
    TPC_FreeSysviewResult(&pucResult);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007 空表DB_BkpPhyEx导出小端数据后，TPC_Sysview接口转换txt文档，校验表名，预期成功
TEST_F(DBConvert, V1Com_033_DBConvert_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/normal_002_with_all_type.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step1 DB_BkpPhyEx导出小端数据 ========= */
    char filePath[64] = "./filePath/export.db";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    ret = DB_BkpPhyEx(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 转换 ========= */
    VOS_UINT8 *pucResult = NULL;
    DB_TPC_SYSVIEW_ARGS_STRU viewArgs = {0};
    viewArgs.pucFilePath = (VOS_UINT8 *)filePath;
    viewArgs.pucOutTxtPath = (VOS_UINT8 *)"./filePath/convertFile.txt";
    ret = TPC_Sysview(DB_TPC_SYSVIEW_CONVERT_DB_FILE, &viewArgs, &pucResult);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 文件比较
    ret = CompareFile("./dataFile/convert007.txt", "./filePath/convertFile.txt");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TPC_FreeSysviewResult(&pucResult);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008 写一条数据DB_BkpPhyEx导出小端数据后转换校验，再次写入一条DB_BkpPhyEx导出小端数据后转换，预期数据有变化
TEST_F(DBConvert, V1Com_033_DBConvert_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/normal_002_with_all_type.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    for (int i = 0; i < 2; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        uint32_t recNum = 0;
        DbSelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);

        /* ========= step1 DB_BkpPhyEx导出小端数据 ========= */
        char filePath[64] = "./filePath/export.db";
        DB_SAVE_OPTIONS pstFileSaveOpt;
        pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
        ret = DB_BkpPhyEx(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        if (i == 0) {
            VOS_UINT8 *pucResult = NULL;
            DB_TPC_SYSVIEW_ARGS_STRU viewArgs = {0};
            viewArgs.pucFilePath = (VOS_UINT8 *)filePath;
            viewArgs.pucOutTxtPath = (VOS_UINT8 *)"./filePath/convertFile.txt";
            ret = TPC_Sysview(DB_TPC_SYSVIEW_CONVERT_DB_FILE, &viewArgs, &pucResult);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

            // 文件比较
            ret = CompareFile("./dataFile/convert008_1.txt", "./filePath/convertFile.txt");
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            TPC_FreeSysviewResult(&pucResult);
        } else if (i == 1) {
            VOS_UINT8 *pucResult = NULL;
            DB_TPC_SYSVIEW_ARGS_STRU viewArgs = {0};
            viewArgs.pucFilePath = (VOS_UINT8 *)filePath;
            viewArgs.pucOutTxtPath = (VOS_UINT8 *)"./filePath/convertFile.txt";
            ret = TPC_Sysview(DB_TPC_SYSVIEW_CONVERT_DB_FILE, &viewArgs, &pucResult);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

            // 文件比较
            ret = CompareFile("./dataFile/convert008_2.txt", "./filePath/convertFile.txt");
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            TPC_FreeSysviewResult(&pucResult);
        }
    }

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 009 写数据DB_BkpPhyEx导出小端数据后转换，修改最大记录数后再导出转换，预期最大记录数修改成功
TEST_F(DBConvert, V1Com_033_DBConvert_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/normal_002_with_all_type.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        DbSelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhyEx导出小端数据 ========= */
    char filePath[64] = "./filePath/export.db";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    ret = DB_BkpPhyEx(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 转换 ========= */
    VOS_UINT8 *pucResult = NULL;
    DB_TPC_SYSVIEW_ARGS_STRU viewArgs = {0};
    viewArgs.pucFilePath = (VOS_UINT8 *)filePath;
    viewArgs.pucOutTxtPath = (VOS_UINT8 *)"./filePath/convertFile.txt";
    ret = TPC_Sysview(DB_TPC_SYSVIEW_CONVERT_DB_FILE, &viewArgs, &pucResult);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 文件比较
    ret = CompareFile("./dataFile/convert009_1.txt", "./filePath/convertFile.txt");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TPC_FreeSysviewResult(&pucResult);

    // 修改表的最大记录数
    ret = DB_SetMaxRecNumOfTable(g_dbId, usRelId, 0, 0, 12800);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表的最大记录数
    ret = DBDDL_GetRelMaxRecNum(g_dbId, usRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(12800, ret);

    // 再次导出
    ret = DB_BkpPhyEx(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 再次转换
    ret = TPC_Sysview(DB_TPC_SYSVIEW_CONVERT_DB_FILE, &viewArgs, &pucResult);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 文件比较
    ret = CompareFile("./dataFile/convert009_2.txt", "./filePath/convertFile.txt");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TPC_FreeSysviewResult(&pucResult);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010 写数据DB_BkpPhyEx导出小端数据后转换，更新后导出转换，预期成功
TEST_F(DBConvert, V1Com_033_DBConvert_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/normal_002_with_all_type.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        DbSelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhyEx导出小端数据 ========= */
    char filePath[64] = "./filePath/export.db";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    ret = DB_BkpPhyEx(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 转换 ========= */
    VOS_UINT8 *pucResult = NULL;
    DB_TPC_SYSVIEW_ARGS_STRU viewArgs = {0};
    viewArgs.pucFilePath = (VOS_UINT8 *)filePath;
    viewArgs.pucOutTxtPath = (VOS_UINT8 *)"./filePath/convertFile.txt";
    ret = TPC_Sysview(DB_TPC_SYSVIEW_CONVERT_DB_FILE, &viewArgs, &pucResult);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 文件比较
    ret = CompareFile("./dataFile/convert010_1.txt", "./filePath/convertFile.txt");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TPC_FreeSysviewResult(&pucResult);

    // 更新数据、导出、转换
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        DbUpdateAllType(g_dbId, usRelId, recBuf, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    ret = DB_BkpPhyEx(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_Sysview(DB_TPC_SYSVIEW_CONVERT_DB_FILE, &viewArgs, &pucResult);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 文件比较
    ret = CompareFile("./dataFile/convert010_2.txt", "./filePath/convertFile.txt");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TPC_FreeSysviewResult(&pucResult);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 011 写数据DB_BkpPhyEx导出小端数据后转换，删除后导出转换，预期没数据
TEST_F(DBConvert, V1Com_033_DBConvert_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/normal_002_with_all_type.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        DbSelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhyEx导出小端数据 ========= */
    char filePath[64] = "./filePath/export.db";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    ret = DB_BkpPhyEx(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 转换 ========= */
    VOS_UINT8 *pucResult = NULL;
    DB_TPC_SYSVIEW_ARGS_STRU viewArgs = {0};
    viewArgs.pucFilePath = (VOS_UINT8 *)filePath;
    viewArgs.pucOutTxtPath = (VOS_UINT8 *)"./filePath/convertFile.txt";
    ret = TPC_Sysview(DB_TPC_SYSVIEW_CONVERT_DB_FILE, &viewArgs, &pucResult);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 文件比较
    ret = CompareFile("./dataFile/convert011_1.txt", "./filePath/convertFile.txt");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TPC_FreeSysviewResult(&pucResult);

    // 删除一半数据、导出、转换
    uint32_t cnt = 0;
    DbDelete(g_dbId, usRelId, insertNum / 2, &cnt);
    V1_AW_MACRO_EXPECT_EQ_INT(insertNum / 2, cnt);

    ret = DB_BkpPhyEx(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_Sysview(DB_TPC_SYSVIEW_CONVERT_DB_FILE, &viewArgs, &pucResult);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 文件比较
    ret = CompareFile("./dataFile/convert011_2.txt", "./filePath/convertFile.txt");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TPC_FreeSysviewResult(&pucResult);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 012 含默认值导出小端数据后转换，预期成功
TEST_F(DBConvert, V1Com_033_DBConvert_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/normal_003_with_default_val.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step1 DB_BkpPhyEx导出小端数据 ========= */
    char filePath[64] = "./filePath/export.db";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    ret = DB_BkpPhyEx(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 转换 ========= */
    VOS_UINT8 *pucResult = NULL;
    DB_TPC_SYSVIEW_ARGS_STRU viewArgs = {0};
    viewArgs.pucFilePath = (VOS_UINT8 *)filePath;
    viewArgs.pucOutTxtPath = (VOS_UINT8 *)"./filePath/convertFile.txt";
    ret = TPC_Sysview(DB_TPC_SYSVIEW_CONVERT_DB_FILE, &viewArgs, &pucResult);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 文件比较
    ret = CompareFile("./dataFile/convert012.txt", "./filePath/convertFile.txt");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TPC_FreeSysviewResult(&pucResult);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 013 含65535张表导出小端数据后转换，校验表个数，预期成功
TEST_F(DBConfigConvert, V1Com_033_DBConvert_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxNormalTableNum=65535\"");
    int ret;
    int cfgVal = 65535, num = 65536;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestDB_Init());
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");

    ret = DB_CreateDB((VOS_UINT8 *)g_dbName, NULL, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_dbName, &g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t testSourceRelId[num] = {0};
    DB_REL_DEF_STRU *testRelDef = NULL;
    char tableName[num][DB_NAME_LEN] = {0};
    for (int i = 0; i < num; i++) {
        sprintf(tableName[i], "label%d", i);
        ret = TestDB_CreateTbl(
            g_dbId, "schemaFile/normal_002_with_all_type.json", &testSourceRelId[i], testRelDef, tableName[i]);
        if (ret != DB_SUCCESS_V1) {
            AW_FUN_Log(LOG_ERROR, "TestDB_CreateTbl failed with %d, i = %d", ret, i);
            V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_RELDESC_ALLOCFAILURE, ret);
            V1_AW_MACRO_EXPECT_EQ_INT(cfgVal, i);
            break;
        }
    }

    /* ========= step1 DB_BkpPhyEx导出小端数据 ========= */
    char filePath[64] = "./filePath/export.db";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    ret = DB_BkpPhyEx(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 转换 ========= */
    VOS_UINT8 *pucResult = NULL;
    DB_TPC_SYSVIEW_ARGS_STRU viewArgs = {0};
    viewArgs.pucFilePath = (VOS_UINT8 *)filePath;
    viewArgs.pucOutTxtPath = (VOS_UINT8 *)"./filePath/convertFile.txt";
    ret = TPC_Sysview(DB_TPC_SYSVIEW_CONVERT_DB_FILE, &viewArgs, &pucResult);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    char command[1024];
    (void)sprintf_s(command, sizeof(command), "cat ./filePath/convertFile.txt");
    ret = executeCommand(command, (const char *)"The Total table count : 65535");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TPC_FreeSysviewResult(&pucResult);

    // 删除DB
    ret = DB_CloseDB(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestDB_UnInit());
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 014 含大对象的DB_BkpPhyEx导出小端数据后转换，校验表和数据，预期成功
TEST_F(DBConvert, V1Com_033_DBConvert_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 创建自定义数据类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t relId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/tblBigObj.json", &relId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    int insertNum = 1;
    // 插入
    for (int i = 0; i < insertNum; i++) {
        ret = DbInsertTblBigObj(g_dbId, relId, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestDBS_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < insertNum; i++) {
        ret = DbSelectTblBigObj(g_dbId, relId, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    /* ========= step1 DB_BkpPhyEx导出小端数据 ========= */
    char filePath[64] = "./filePath/export.db";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    ret = DB_BkpPhyEx(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 转换 ========= */
    VOS_UINT8 *pucResult = NULL;
    DB_TPC_SYSVIEW_ARGS_STRU viewArgs = {0};
    viewArgs.pucFilePath = (VOS_UINT8 *)filePath;
    viewArgs.pucOutTxtPath = (VOS_UINT8 *)"./filePath/convertFile.txt";
    ret = TPC_Sysview(DB_TPC_SYSVIEW_CONVERT_DB_FILE, &viewArgs, &pucResult);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 文件比较
    ret = CompareFile("./dataFile/convert014.txt", "./filePath/convertFile.txt");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TPC_FreeSysviewResult(&pucResult);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 015 表不含索引DB_BkpPhyEx导出小端数据后转换，校验表和数据，预期成功
TEST_F(DBConvert, V1Com_033_DBConvert_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/normal_004_with_no_index.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        DbSelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhyEx导出小端数据 ========= */
    char filePath[64] = "./filePath/export.db";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    ret = DB_BkpPhyEx(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 转换 ========= */
    VOS_UINT8 *pucResult = NULL;
    DB_TPC_SYSVIEW_ARGS_STRU viewArgs = {0};
    viewArgs.pucFilePath = (VOS_UINT8 *)filePath;
    viewArgs.pucOutTxtPath = (VOS_UINT8 *)"./filePath/convertFile.txt";
    ret = TPC_Sysview(DB_TPC_SYSVIEW_CONVERT_DB_FILE, &viewArgs, &pucResult);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 文件比较
    ret = CompareFile("./dataFile/convert015.txt", "./filePath/convertFile.txt");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TPC_FreeSysviewResult(&pucResult);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 016 表含hash，TTree，sort索引，DB_BkpPhyEx导出小端数据后转换，预期成功
TEST_F(DBConvert, V1Com_033_DBConvert_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/normal_005_with_multi_index.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        DbSelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhyEx导出小端数据 ========= */
    char filePath[64] = "./filePath/export.db";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    ret = DB_BkpPhyEx(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 转换 ========= */
    VOS_UINT8 *pucResult = NULL;
    DB_TPC_SYSVIEW_ARGS_STRU viewArgs = {0};
    viewArgs.pucFilePath = (VOS_UINT8 *)filePath;
    viewArgs.pucOutTxtPath = (VOS_UINT8 *)"./filePath/convertFile.txt";
    ret = TPC_Sysview(DB_TPC_SYSVIEW_CONVERT_DB_FILE, &viewArgs, &pucResult);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 文件比较
    ret = CompareFile("./dataFile/convert016.txt", "./filePath/convertFile.txt");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TPC_FreeSysviewResult(&pucResult);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 017 空表含自定义数据类型DB_BkpPhyEx导出大端数据后，TPC_Sysview接口转换txt文档，校验表名，预期成功
TEST_F(DBConvert, V1Com_033_DBConvert_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/normal_001_with_custom.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step1 DB_BkpPhyEx导出大端数据 ========= */
    char filePath[64] = "./filePath/export.db";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    ret = DB_BkpPhyEx(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 转换 ========= */
    VOS_UINT8 *pucResult = NULL;
    DB_TPC_SYSVIEW_ARGS_STRU viewArgs = {0};
    viewArgs.pucFilePath = (VOS_UINT8 *)filePath;
    viewArgs.pucOutTxtPath = (VOS_UINT8 *)"./filePath/convertFile.txt";
    ret = TPC_Sysview(DB_TPC_SYSVIEW_CONVERT_DB_FILE, &viewArgs, &pucResult);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 文件比较
    ret = CompareFile("./dataFile/convert001.txt", "./filePath/convertFile.txt");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TPC_FreeSysviewResult(&pucResult);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 018 普通表写数据DB_BkpPhyEx导出大端数据后，TPC_Sysview接口转换txt文档，校验表名和数据条数，预期成功
TEST_F(DBConvert, V1Com_033_DBConvert_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/normal_002_with_all_type.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        DbSelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhyEx导出大端数据 ========= */
    char filePath[64] = "./filePath/export.db";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    ret = DB_BkpPhyEx(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 转换 ========= */
    VOS_UINT8 *pucResult = NULL;
    DB_TPC_SYSVIEW_ARGS_STRU viewArgs = {0};
    viewArgs.pucFilePath = (VOS_UINT8 *)filePath;
    viewArgs.pucOutTxtPath = (VOS_UINT8 *)"./filePath/convertFile.txt";
    ret = TPC_Sysview(DB_TPC_SYSVIEW_CONVERT_DB_FILE, &viewArgs, &pucResult);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 文件比较
    ret = CompareFile("./dataFile/convert002.txt", "./filePath/convertFile.txt");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TPC_FreeSysviewResult(&pucResult);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 019 含自定义数据类型写数据DB_BkpPhyEx导出大端数据后，TPC_Sysview接口转换txt文档，校验表名和数据条数，预期成功
TEST_F(DBConvert, V1Com_033_DBConvert_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/normal_001_with_custom.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllTypeCustom(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        DbSelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum, true);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhyEx导出大端数据 ========= */
    char filePath[64] = "./filePath/export.db";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    ret = DB_BkpPhyEx(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 转换 ========= */
    VOS_UINT8 *pucResult = NULL;
    DB_TPC_SYSVIEW_ARGS_STRU viewArgs = {0};
    viewArgs.pucFilePath = (VOS_UINT8 *)filePath;
    viewArgs.pucOutTxtPath = (VOS_UINT8 *)"./filePath/convertFile.txt";
    ret = TPC_Sysview(DB_TPC_SYSVIEW_CONVERT_DB_FILE, &viewArgs, &pucResult);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 文件比较
    ret = CompareFile("./dataFile/convert003.txt", "./filePath/convertFile.txt");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TPC_FreeSysviewResult(&pucResult);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 020 临时表写数据DB_BkpPhyEx导出大端数据后，TPC_Sysview接口转换txt文档，校验表名，预期失败
TEST_F(DBConvert, V1Com_033_DBConvert_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/temp_001_with_all_type.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        DbSelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhyEx导出大端数据 ========= */
    char filePath[64] = "./filePath/export.db";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    ret = DB_BkpPhyEx(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 转换 ========= */
    VOS_UINT8 *pucResult = NULL;
    DB_TPC_SYSVIEW_ARGS_STRU viewArgs = {0};
    viewArgs.pucFilePath = (VOS_UINT8 *)filePath;
    viewArgs.pucOutTxtPath = (VOS_UINT8 *)"./filePath/convertFile.txt";
    ret = TPC_Sysview(DB_TPC_SYSVIEW_CONVERT_DB_FILE, &viewArgs, &pucResult);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验表名
    char command[1024];
    (void)sprintf_s(command, sizeof(command), "cat ./filePath/convertFile.txt");
    ret = executeCommand(command, "tbname: tempLabelNameA");
    EXPECT_NE(DB_SUCCESS_V1, ret);

    TPC_FreeSysviewResult(&pucResult);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 021 DB只含描述信息DB_BkpPhyEx导出大端数据后，TPC_Sysview接口转换txt文档，校验描述信息，预期成功
TEST_F(DBConvert, V1Com_033_DBConvert_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 设置DB描述信息
    char setDescInfo[] = "This is an empty DataBase.";
    ret = DB_SetDBDesc((VOS_UINT8 *)g_dbName, (VOS_VOID *)setDescInfo, strlen(setDescInfo));
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step1 DB_BkpPhyEx导出大端数据 ========= */
    char filePath[64] = "./filePath/export.db";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    ret = DB_BkpPhyEx(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 转换 ========= */
    VOS_UINT8 *pucResult = NULL;
    DB_TPC_SYSVIEW_ARGS_STRU viewArgs = {0};
    viewArgs.pucFilePath = (VOS_UINT8 *)filePath;
    viewArgs.pucOutTxtPath = (VOS_UINT8 *)"./filePath/convertFile.txt";
    ret = TPC_Sysview(DB_TPC_SYSVIEW_CONVERT_DB_FILE, &viewArgs, &pucResult);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 文件末尾加空行
    FileEndAddSpaceLine("./filePath/convertFile.txt");

    // 文件比较
    ret = CompareFile("./dataFile/convert005.txt", "./filePath/convertFile.txt");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TPC_FreeSysviewResult(&pucResult);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 022 空表含自定义数据类型DB_BkpPhyEx导出大端数据后，TPC_Sysview接口转换txt文档，校验表名，预期成功
TEST_F(DBConvert, V1Com_033_DBConvert_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/normal_001_with_custom.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step1 DB_BkpPhyEx导出大端数据 ========= */
    char filePath[64] = "./filePath/export.db";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    ret = DB_BkpPhyEx(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 转换 ========= */
    VOS_UINT8 *pucResult = NULL;
    DB_TPC_SYSVIEW_ARGS_STRU viewArgs = {0};
    viewArgs.pucFilePath = (VOS_UINT8 *)filePath;
    viewArgs.pucOutTxtPath = (VOS_UINT8 *)"./filePath/convertFile.txt";
    ret = TPC_Sysview(DB_TPC_SYSVIEW_CONVERT_DB_FILE, &viewArgs, &pucResult);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 文件比较
    ret = CompareFile("./dataFile/convert001.txt", "./filePath/convertFile.txt");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TPC_FreeSysviewResult(&pucResult);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 023 写一条数据DB_BkpPhyEx导出大端数据后转换校验，再次写入一条DB_BkpPhyEx导出大端数据后转换，预期数据有变化
TEST_F(DBConvert, V1Com_033_DBConvert_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/normal_002_with_all_type.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    for (int i = 0; i < 2; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        uint32_t recNum = 0;
        DbSelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);

        /* ========= step1 DB_BkpPhyEx导出大端数据 ========= */
        char filePath[64] = "./filePath/export.db";
        DB_SAVE_OPTIONS pstFileSaveOpt;
        pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
        ret = DB_BkpPhyEx(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        if (i == 0) {
            VOS_UINT8 *pucResult = NULL;
            DB_TPC_SYSVIEW_ARGS_STRU viewArgs = {0};
            viewArgs.pucFilePath = (VOS_UINT8 *)filePath;
            viewArgs.pucOutTxtPath = (VOS_UINT8 *)"./filePath/convertFile.txt";
            ret = TPC_Sysview(DB_TPC_SYSVIEW_CONVERT_DB_FILE, &viewArgs, &pucResult);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

            // 文件比较
            ret = CompareFile("./dataFile/convert008_1.txt", "./filePath/convertFile.txt");
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            TPC_FreeSysviewResult(&pucResult);
        } else if (i == 1) {
            VOS_UINT8 *pucResult = NULL;
            DB_TPC_SYSVIEW_ARGS_STRU viewArgs = {0};
            viewArgs.pucFilePath = (VOS_UINT8 *)filePath;
            viewArgs.pucOutTxtPath = (VOS_UINT8 *)"./filePath/convertFile.txt";
            ret = TPC_Sysview(DB_TPC_SYSVIEW_CONVERT_DB_FILE, &viewArgs, &pucResult);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

            // 文件比较
            ret = CompareFile("./dataFile/convert008_2.txt", "./filePath/convertFile.txt");
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            TPC_FreeSysviewResult(&pucResult);
        }
    }

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 024 写数据DB_BkpPhyEx导出大端数据后转换，修改最大记录数后再导出转换，预期最大记录数修改成功
TEST_F(DBConvert, V1Com_033_DBConvert_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/normal_002_with_all_type.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        DbSelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhyEx导出大端数据 ========= */
    char filePath[64] = "./filePath/export.db";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    ret = DB_BkpPhyEx(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 转换 ========= */
    VOS_UINT8 *pucResult = NULL;
    DB_TPC_SYSVIEW_ARGS_STRU viewArgs = {0};
    viewArgs.pucFilePath = (VOS_UINT8 *)filePath;
    viewArgs.pucOutTxtPath = (VOS_UINT8 *)"./filePath/convertFile.txt";
    ret = TPC_Sysview(DB_TPC_SYSVIEW_CONVERT_DB_FILE, &viewArgs, &pucResult);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 文件比较
    ret = CompareFile("./dataFile/convert009_1.txt", "./filePath/convertFile.txt");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TPC_FreeSysviewResult(&pucResult);

    // 修改表的最大记录数
    ret = DB_SetMaxRecNumOfTable(g_dbId, usRelId, 0, 0, 12800);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表的最大记录数
    ret = DBDDL_GetRelMaxRecNum(g_dbId, usRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(12800, ret);

    // 再次导出
    ret = DB_BkpPhyEx(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 再次转换
    ret = TPC_Sysview(DB_TPC_SYSVIEW_CONVERT_DB_FILE, &viewArgs, &pucResult);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 文件比较
    ret = CompareFile("./dataFile/convert009_2.txt", "./filePath/convertFile.txt");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TPC_FreeSysviewResult(&pucResult);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 025 写数据DB_BkpPhyEx导出大端数据后转换，更新后导出转换，预期成功
TEST_F(DBConvert, V1Com_033_DBConvert_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/normal_002_with_all_type.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        DbSelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhyEx导出大端数据 ========= */
    char filePath[64] = "./filePath/export.db";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    ret = DB_BkpPhyEx(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 转换 ========= */
    VOS_UINT8 *pucResult = NULL;
    DB_TPC_SYSVIEW_ARGS_STRU viewArgs = {0};
    viewArgs.pucFilePath = (VOS_UINT8 *)filePath;
    viewArgs.pucOutTxtPath = (VOS_UINT8 *)"./filePath/convertFile.txt";
    ret = TPC_Sysview(DB_TPC_SYSVIEW_CONVERT_DB_FILE, &viewArgs, &pucResult);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 文件比较
    ret = CompareFile("./dataFile/convert010_1.txt", "./filePath/convertFile.txt");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TPC_FreeSysviewResult(&pucResult);

    // 更新数据、导出、转换
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        DbUpdateAllType(g_dbId, usRelId, recBuf, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    ret = DB_BkpPhyEx(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_Sysview(DB_TPC_SYSVIEW_CONVERT_DB_FILE, &viewArgs, &pucResult);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 文件比较
    ret = CompareFile("./dataFile/convert010_2.txt", "./filePath/convertFile.txt");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 026 写数据DB_BkpPhyEx导出大端数据后转换，删除后导出转换，预期没数据
TEST_F(DBConvert, V1Com_033_DBConvert_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/normal_002_with_all_type.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        DbSelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhyEx导出大端数据 ========= */
    char filePath[64] = "./filePath/export.db";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    ret = DB_BkpPhyEx(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 转换 ========= */
    VOS_UINT8 *pucResult = NULL;
    DB_TPC_SYSVIEW_ARGS_STRU viewArgs = {0};
    viewArgs.pucFilePath = (VOS_UINT8 *)filePath;
    viewArgs.pucOutTxtPath = (VOS_UINT8 *)"./filePath/convertFile.txt";
    ret = TPC_Sysview(DB_TPC_SYSVIEW_CONVERT_DB_FILE, &viewArgs, &pucResult);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 文件比较
    ret = CompareFile("./dataFile/convert011_1.txt", "./filePath/convertFile.txt");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TPC_FreeSysviewResult(&pucResult);

    // 删除一半数据、导出、转换
    uint32_t cnt = 0;
    DbDelete(g_dbId, usRelId, insertNum / 2, &cnt);
    V1_AW_MACRO_EXPECT_EQ_INT(insertNum / 2, cnt);

    ret = DB_BkpPhyEx(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_Sysview(DB_TPC_SYSVIEW_CONVERT_DB_FILE, &viewArgs, &pucResult);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 文件比较
    ret = CompareFile("./dataFile/convert011_2.txt", "./filePath/convertFile.txt");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 027 含默认值导出大端数据后转换，预期成功
TEST_F(DBConvert, V1Com_033_DBConvert_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/normal_003_with_default_val.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step1 DB_BkpPhyEx导出大端数据 ========= */
    char filePath[64] = "./filePath/export.db";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    ret = DB_BkpPhyEx(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 转换 ========= */
    VOS_UINT8 *pucResult = NULL;
    DB_TPC_SYSVIEW_ARGS_STRU viewArgs = {0};
    viewArgs.pucFilePath = (VOS_UINT8 *)filePath;
    viewArgs.pucOutTxtPath = (VOS_UINT8 *)"./filePath/convertFile.txt";
    ret = TPC_Sysview(DB_TPC_SYSVIEW_CONVERT_DB_FILE, &viewArgs, &pucResult);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 文件比较
    ret = CompareFile("./dataFile/convert012.txt", "./filePath/convertFile.txt");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TPC_FreeSysviewResult(&pucResult);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 028 含65535张表导出大端数据后转换，校验表个数，预期成功
TEST_F(DBConfigConvert, V1Com_033_DBConvert_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxNormalTableNum=65535\"");
    int ret;
    int cfgVal = 65535, num = 65536;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestDB_Init());
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");

    ret = DB_CreateDB((VOS_UINT8 *)g_dbName, NULL, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_dbName, &g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t testSourceRelId[num] = {0};
    DB_REL_DEF_STRU *testRelDef = NULL;
    char tableName[num][DB_NAME_LEN] = {0};
    for (int i = 0; i < num; i++) {
        sprintf(tableName[i], "label%d", i);
        ret = TestDB_CreateTbl(
            g_dbId, "schemaFile/normal_002_with_all_type.json", &testSourceRelId[i], testRelDef, tableName[i]);
        if (ret != DB_SUCCESS_V1) {
            AW_FUN_Log(LOG_ERROR, "TestDB_CreateTbl failed with %d, i = %d", ret, i);
            V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_RELDESC_ALLOCFAILURE, ret);
            V1_AW_MACRO_EXPECT_EQ_INT(cfgVal, i);
            break;
        }
    }

    /* ========= step1 DB_BkpPhyEx导出大端数据 ========= */
    char filePath[64] = "./filePath/export.db";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    ret = DB_BkpPhyEx(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 转换 ========= */
    VOS_UINT8 *pucResult = NULL;
    DB_TPC_SYSVIEW_ARGS_STRU viewArgs = {0};
    viewArgs.pucFilePath = (VOS_UINT8 *)filePath;
    viewArgs.pucOutTxtPath = (VOS_UINT8 *)"./filePath/convertFile.txt";
    ret = TPC_Sysview(DB_TPC_SYSVIEW_CONVERT_DB_FILE, &viewArgs, &pucResult);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    char command[1024];
    (void)sprintf_s(command, sizeof(command), "cat ./filePath/convertFile.txt");
    ret = executeCommand(command, (const char *)"The Total table count : 65535");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TPC_FreeSysviewResult(&pucResult);

    // 删除DB
    ret = DB_CloseDB(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestDB_UnInit());
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 029 含大对象的DB_BkpPhyEx导出大端数据后转换，校验表和数据，预期成功
TEST_F(DBConvert, V1Com_033_DBConvert_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 创建自定义数据类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t relId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/tblBigObj.json", &relId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    int insertNum = 256;
    // 插入
    for (int i = 0; i < insertNum; i++) {
        ret = DbInsertTblBigObj(g_dbId, relId, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestDBS_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < insertNum; i++) {
        ret = DbSelectTblBigObj(g_dbId, relId, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    /* ========= step1 DB_BkpPhyEx导出大端数据 ========= */
    char filePath[64] = "./filePath/export.db";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    ret = DB_BkpPhyEx(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 转换 ========= */
    VOS_UINT8 *pucResult = NULL;
    DB_TPC_SYSVIEW_ARGS_STRU viewArgs = {0};
    viewArgs.pucFilePath = (VOS_UINT8 *)filePath;
    viewArgs.pucOutTxtPath = (VOS_UINT8 *)"./filePath/convertFile.txt";
    ret = TPC_Sysview(DB_TPC_SYSVIEW_CONVERT_DB_FILE, &viewArgs, &pucResult);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验数据条数
    char command[1024];
    (void)sprintf_s(command, sizeof(command), "cat ./filePath/convertFile.txt");
    ret = executeCommand(command, "NO.256");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TPC_FreeSysviewResult(&pucResult);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 030 表不含索引DB_BkpPhyEx导出大端数据后转换，校验表和数据，预期成功
TEST_F(DBConvert, V1Com_033_DBConvert_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/normal_004_with_no_index.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        DbSelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhyEx导出大端数据 ========= */
    char filePath[64] = "./filePath/export.db";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    ret = DB_BkpPhyEx(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 转换 ========= */
    VOS_UINT8 *pucResult = NULL;
    DB_TPC_SYSVIEW_ARGS_STRU viewArgs = {0};
    viewArgs.pucFilePath = (VOS_UINT8 *)filePath;
    viewArgs.pucOutTxtPath = (VOS_UINT8 *)"./filePath/convertFile.txt";
    ret = TPC_Sysview(DB_TPC_SYSVIEW_CONVERT_DB_FILE, &viewArgs, &pucResult);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 文件比较
    ret = CompareFile("./dataFile/convert015.txt", "./filePath/convertFile.txt");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TPC_FreeSysviewResult(&pucResult);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 031 表含hash，TTree，sort索引，DB_BkpPhyEx导出大端数据后转换，预期成功
TEST_F(DBConvert, V1Com_033_DBConvert_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/normal_005_with_multi_index.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        DbSelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_BkpPhyEx导出大端数据 ========= */
    char filePath[64] = "./filePath/export.db";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    ret = DB_BkpPhyEx(g_dbId, (uint8_t *)filePath, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 转换 ========= */
    VOS_UINT8 *pucResult = NULL;
    DB_TPC_SYSVIEW_ARGS_STRU viewArgs = {0};
    viewArgs.pucFilePath = (VOS_UINT8 *)filePath;
    viewArgs.pucOutTxtPath = (VOS_UINT8 *)"./filePath/convertFile.txt";
    ret = TPC_Sysview(DB_TPC_SYSVIEW_CONVERT_DB_FILE, &viewArgs, &pucResult);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 文件比较
    ret = CompareFile("./dataFile/convert016.txt", "./filePath/convertFile.txt");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    TPC_FreeSysviewResult(&pucResult);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}
