/*
=====================  1 RDB和CDB混用场景  =====================
001 开启cdb事务读表A，RDB写表A，提交cdb，预期cdb提交成功
002 开启cdb事务写表A，RDB读表A，提交cdb，预期cdb提交成功
003 开启cdb事务读表A，RDB读表A，提交cdb，预期cdb提交成功
004 开启cdb事务写表A，RDB写表A，提交cdb，预期cdb提交失败
005 空表A，开启cdb事务，cdb删表A的数据，RDB删表A的数据，预期cdb提交成功
006 空表A，开启cdb事务，cdb删表A的数据，RDB更新表A的数据，预期cdb提交成功
007 空表A，开启cdb事务，cdb删表A的数据，RDB写表A，预期cdb提交失败
008 空表A，开启cdb事务，cdb写表A的数据，RDB删表A的数据，预期cdb提交失败
009 空表A，开启cdb事务，cdb写表A的数据，RDB更新表A的数据，预期cdb提交失败
010 空表A，开启cdb事务，RDB更新表A的数据，cdb写表A的数据，预期cdb提交成功
011 开启cdb事务读表A，RDB写表B，提交cdb，预期cdb提交成功
012 开启cdb事务写表B，RDB读表A，提交cdb，预期cdb提交成功

=====================  2 CDB场景  =====================
013 事务1和事务2对表A进行读操作，事务1先提交，预期都提交成功
014 事务1写表A，事务2读表A，事务1先提交，预期事务1提交成功、事务2提交成功
015 事务1写表A，事务2读表A，事务2先提交，预期事务1提交成功、事务2提交成功
016 事务1写表A，事务2写表A，事务1先提交，预期事务1提交成功，事务2提交失败
017 事务1读表A、写表A，事务2写表A，回滚事务1后提交事务2，预期提交事务2成功
018 事务1读表A、写表A，事务2读表A，回滚事务1后提交事务2，预期提交事务2成功
019 事务1删除表A的数据，事务2删表A的数据，事务1先提交，预期事务2提交失败
020 事务1读表A、写表B，事务2写表A，事务1先提交，预期事务1提交成功，事务2提交失败
021 事务1读表A、写表B，事务2写表A，事务2先提交，预期事务2提交成功，事务1提交失败
022 事务1读表A、写表B，事务2更新表A，事务2先提交，预期事务2提交成功，事务1提交失败
023 事务1读表A、写表B，事务2删除表A数据，事务2先提交，预期事务2提交成功，事务1提交失败
024 事务1读表A、写表B，事务2读表A，事务1先提交，预期事务1提交成功，事务2提交成功
025 事务1读表A、写表B，事务2读表A，事务2先提交，预期事务2提交成功，事务1提交成功
026 事务1读表A、写表B，事务2读表A、写表B，事务1先提交，预期事务1提交成功，事务2提交失败
027 事务1读表A、写表B，事务2读表A、写表C，事务1先提交，预期事务1提交成功，事务2提交成功
028 事务1读表A、更新表B，事务2读表A、更新表C，事务1先提交，预期事务1提交成功，事务2提交成功
029 事务1读表A、删除表B数据，事务2读表A、删除表C数据，事务1先提交，预期事务1提交成功，事务2提交成功
030 事务1读表A、读表B、写表C，事务2读表A、读表B、写表C，事务1先提交，预期事务1提交成功，事务2提交失败
031 事务1读表A、写表B，事务2写表A，回滚事务1后提交事务2，预期事务1回滚成功，事务2提交成功
032 空表A，事务1扫描表A、写表B，事务2删表A，先提交事务2，预期事务1提交失败
033 空表A，事务1扫描表A、写表B，事务2删表A，先提交事务1，预期事务2提交失败
034 空表A，事务1扫描表A、写表B，事务2更新A，先提交事务2，预期事务1提交失败

035 开启240个cdb，cdb1读表A和写表B，其余cdb都读表A，提交cdb1后再提交其余cdb，预期所有cdb提交成功
036 开启240个cdb，cdb1读表A和写表B，其余cdb都读表A，cdb1最后提交，预期所有cdb提交成功
037 开启240个cdb，cdb1读表A和写表B，其余cdb都写表A，提交cdb1后再提交其余cdb，预期只有cdb1提交成功，其余失败
038 事务1写表A，事务2写表A、表B，事务3写表B，先提交事务1，事务2（冲突），再强制提交事务2，预期事务3提交失败
039 空表A表B，cdb1空删表A数据，cdb2读表A写表B，提交cdb1、cdb2，预期cdb1提交成功，cdb2提交失败
*/

#include "ConflictReadAndWrite.h"

class NormalTable : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void NormalTable::SetUpTestCase()
{}

void NormalTable::TearDownTestCase()
{}

void NormalTable::SetUp()
{
    // 初始化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_Init());
    AW_CHECK_LOG_BEGIN();
    // 创建和开启DB
    int ret = TPC_CreateDB((VOS_UINT8 *)g_dbName, NULL, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_dbName, &g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}

void NormalTable::TearDown()
{
    AW_CHECK_LOG_END();
    // CLoseDB
    int ret = TPC_CloseDB(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)g_dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_UnInit());
}

// 001 开启cdb事务读表A，RDB写表A，提交cdb，预期cdb提交成功
TEST_F(NormalTable, V1Com_024_Normal_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 建表
    DB_REL_DEF_STRU relDef = {0};
    uint16_t relID = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_001.json", &relID, &relDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // RDB写数据0-99
    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    int insertNum = 100;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 开启cdb事务
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_dbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // cdb读表数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(cdbID, g_dbId, relID, relDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // RDB写数据100-199
    for (int i = insertNum; i < 2 * insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 提交cdb事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = TPC_DropTbl(g_dbId, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&relDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002 开启cdb事务写表A，RDB读表A，提交cdb，预期cdb提交成功
TEST_F(NormalTable, V1Com_024_Normal_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 建表
    DB_REL_DEF_STRU relDef = {0};
    uint16_t relID = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_001.json", &relID, &relDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // RDB写数据0-99
    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    int insertNum = 100;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 开启cdb事务
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_dbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // cdb写数据100-199
    for (int i = insertNum; i < 2 * insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(cdbID, g_dbId, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // RDB读表数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, g_dbId, relID, relDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 提交cdb事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = TPC_DropTbl(g_dbId, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&relDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003 开启cdb事务读表A，RDB读表A，提交cdb，预期cdb提交成功
TEST_F(NormalTable, V1Com_024_Normal_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 建表
    DB_REL_DEF_STRU relDef = {0};
    uint16_t relID = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_001.json", &relID, &relDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // RDB写数据0-99
    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    int insertNum = 100;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 开启cdb事务
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_dbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // cdb读表数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(cdbID, g_dbId, relID, relDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // RDB读表数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, g_dbId, relID, relDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 提交cdb事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = TPC_DropTbl(g_dbId, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&relDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004 开启cdb事务写表A，RDB写表A，提交cdb，预期cdb提交失败
TEST_F(NormalTable, V1Com_024_Normal_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 建表
    DB_REL_DEF_STRU relDef = {0};
    uint16_t relID = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_001.json", &relID, &relDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // RDB写数据0-99
    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    int insertNum = 100;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 开启cdb事务
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_dbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // cdb写数据100-199
    for (int i = insertNum; i < 2 * insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(cdbID, g_dbId, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // RDBx写数据100-199
    for (int i = insertNum; i < 2 * insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 提交cdb事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_TPC_RDB_MODIFIED, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1,
        TestTPC_SysviewGetLastError(
            "Violated the constraint restrictions.", false));

    // 回滚事务
    ret = TPC_RollbackCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = TPC_DropTbl(g_dbId, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&relDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005 空表A，开启cdb事务，cdb删表A的数据，RDB删表A的数据，预期cdb提交成功
TEST_F(NormalTable, V1Com_024_Normal_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 建表
    DB_REL_DEF_STRU relDef = {0};
    uint16_t relID = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_001.json", &relID, &relDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启cdb事务
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_dbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 空表cdb删除数据
    uint32_t recNum = 0;
    Delete(cdbID, g_dbId, relID, 0, &recNum);
    V1_AW_MACRO_EXPECT_EQ_INT(0, recNum);
    // 空表RDB删除数据  || 空表删和空表更新，RDB不开事务
    recNum = 0;
    Delete(TPC_GLOBAL_CDB, g_dbId, relID, 0, &recNum);
    V1_AW_MACRO_EXPECT_EQ_INT(0, recNum);

    // 提交cdb事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = TPC_DropTbl(g_dbId, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    FreeRelDef(&relDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006 空表A，开启cdb事务，cdb删表A的数据，RDB更新表A的数据，预期cdb提交成功
TEST_F(NormalTable, V1Com_024_Normal_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 建表
    DB_REL_DEF_STRU relDef = {0};
    uint16_t relID = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_001.json", &relID, &relDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启cdb事务
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_dbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 空表cdb删除数据
    uint32_t recNum = 0;
    Delete(cdbID, g_dbId, relID, 0, &recNum);
    V1_AW_MACRO_EXPECT_EQ_INT(0, recNum);

    // 空表RDB更新数据
    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int updateNum = 100;
    for (int i = 0; i < updateNum; i++) {
        uint32_t recNum = 0;
        UpdateAllType(TPC_GLOBAL_CDB, g_dbId, relID, recBuf, relDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(0, recNum);  // V1更新，成功的前提是表中有数据，表中没有数据更新时不会写入
    }
    for (int i = 0; i < updateNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, g_dbId, relID, relDef.pstFldLst, i + 10000, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(0, recNum);
    }

    // 提交cdb事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = TPC_DropTbl(g_dbId, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&relDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007 空表A，开启cdb事务，cdb删表A的数据，RDB写表A，预期cdb提交失败
TEST_F(NormalTable, V1Com_024_Normal_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 建表
    DB_REL_DEF_STRU relDef = {0};
    uint16_t relID = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_001.json", &relID, &relDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启cdb事务
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_dbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 空表cdb删除数据
    uint32_t recNum = 0;
    Delete(cdbID, g_dbId, relID, 0, &recNum);
    V1_AW_MACRO_EXPECT_EQ_INT(0, recNum);

    // 空表RDB写数据
    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int insertNum = 100;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, g_dbId, relID, relDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 提交cdb事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_TPC_RDB_MODIFIED, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1,
        TestTPC_SysviewGetLastError(
            "Violated the constraint restrictions.", false));

    // 回滚事务
    ret = TPC_RollbackCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = TPC_DropTbl(g_dbId, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&relDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008 空表A，开启cdb事务，cdb写表A的数据，RDB删表A的数据，预期cdb提交失败
TEST_F(NormalTable, V1Com_024_Normal_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 建表
    DB_REL_DEF_STRU relDef = {0};
    uint16_t relID = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_001.json", &relID, &relDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启cdb事务
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_dbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 空表cdb写数据
    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int insertNum = 100;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(cdbID, g_dbId, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 空表RDB删除数据
    uint32_t recNum = 0;
    Delete(TPC_GLOBAL_CDB, g_dbId, relID, 0, &recNum);
    V1_AW_MACRO_EXPECT_EQ_INT(0, recNum);

    // 提交cdb事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_TPC_RDB_MODIFIED, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1,
        TestTPC_SysviewGetLastError(
            "Violated the constraint restrictions.", false));

    // 回滚事务
    ret = TPC_RollbackCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = TPC_DropTbl(g_dbId, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&relDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 009 空表A，开启cdb事务，cdb写表A的数据，RDB更新表A的数据，预期cdb提交失败
TEST_F(NormalTable, V1Com_024_Normal_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 建表
    DB_REL_DEF_STRU relDef = {0};
    uint16_t relID = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_001.json", &relID, &relDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启cdb事务
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_dbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 空表cdb写数据
    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);
    int insertNum = 100;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(cdbID, g_dbId, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(cdbID, g_dbId, relID, relDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 空表RDB更新数据
    int updateNum = 100;
    for (int i = 0; i < updateNum; i++) {
        uint32_t recNum = 0;
        UpdateAllType(TPC_GLOBAL_CDB, g_dbId, relID, recBuf, relDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(0, recNum);
    }
    for (int i = 0; i < updateNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, g_dbId, relID, relDef.pstFldLst, i + 10000, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(0, recNum);
    }

    // 提交cdb事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_TPC_RDB_MODIFIED, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1,
        TestTPC_SysviewGetLastError(
            "Violated the constraint restrictions.", false));

    // 回滚事务
    ret = TPC_RollbackCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = TPC_DropTbl(g_dbId, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&relDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010 空表A，开启cdb事务，RDB更新表A的数据，cdb写表A的数据，预期cdb提交成功
TEST_F(NormalTable, V1Com_024_Normal_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 建表
    DB_REL_DEF_STRU relDef = {0};
    uint16_t relID = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_001.json", &relID, &relDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启cdb事务
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_dbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // 空表RDB写数据
    int updateNum = 100;
    for (int i = 0; i < updateNum; i++) {
        uint32_t recNum = 0;
        UpdateAllType(TPC_GLOBAL_CDB, g_dbId, relID, recBuf, relDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(0, recNum);
    }
    for (int i = 0; i < updateNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, g_dbId, relID, relDef.pstFldLst, i + 10000, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(0, recNum);
    }

    // 空表cdb更新数据
    int insertNum = 100;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(cdbID, g_dbId, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(cdbID, g_dbId, relID, relDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 提交cdb事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = TPC_DropTbl(g_dbId, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&relDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 011 开启cdb事务读表A，RDB写表B，提交cdb，预期cdb提交成功
TEST_F(NormalTable, V1Com_024_Normal_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 建表
    DB_REL_DEF_STRU relDef = {0};
    uint16_t relID = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_001.json", &relID, &relDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表A
    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    DB_REL_DEF_STRU relDef2 = {0};
    uint16_t relID2 = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_002.json", &relID2, &relDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表B
    uint16_t recLen2 = GetRecLen(&relDef2);
    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(recLen2);
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    // RDB写表A
    int insertNum = 100;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 开启cdb事务
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_dbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // cdb读表A数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(cdbID, g_dbId, relID, relDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // RDB写表B 100-199
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, relDef2.pstFldLst, i, recLen2);
        DB_DSBUF_STRU dsBuf = {.usRecLen = recLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, relID2, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 提交cdb事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = TPC_DropTbl(g_dbId, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(g_dbId, relID2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&relDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&relDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 012 开启cdb事务写表B，RDB读表A，提交cdb，预期cdb提交成功
TEST_F(NormalTable, V1Com_024_Normal_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 建表
    DB_REL_DEF_STRU relDef = {0};
    uint16_t relID = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_001.json", &relID, &relDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表A
    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    DB_REL_DEF_STRU relDef2 = {0};
    uint16_t relID2 = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_002.json", &relID2, &relDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表B
    uint16_t recLen2 = GetRecLen(&relDef2);
    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(recLen2);
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    // RDB写表A
    int insertNum = 100;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 开启cdb事务
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_dbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // cdb写表B 0-99
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, relDef2.pstFldLst, i, recLen2);
        DB_DSBUF_STRU dsBuf = {.usRecLen = recLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
        ret = TPC_InsertRec(cdbID, g_dbId, relID2, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // RDB读表A数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, g_dbId, relID, relDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 提交cdb事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = TPC_DropTbl(g_dbId, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(g_dbId, relID2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&relDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&relDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 02 cdb场景
// 013 事务1和事务2对表A进行读操作，事务1先提交，预期都提交成功
TEST_F(NormalTable, V1Com_024_Normal_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 建表
    DB_REL_DEF_STRU relDef = {0};
    uint16_t relID = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_001.json", &relID, &relDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表A
    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // RDB写表A
    int insertNum = 100;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 开启2个cdb事务
    uint32_t cdbID[2] = {0};
    for (int i = 0; i < 2; i++) {
        ret = TPC_BeginCDB(g_dbId, &cdbID[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // cdb0、cdb1都读表A数据
    for (int j = 0; j < 2; j++) {
        for (int i = 0; i < insertNum; i++) {
            uint32_t recNum = 0;
            SelectAllType(cdbID[j], g_dbId, relID, relDef.pstFldLst, i, &recNum);
            V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
        }
    }

    // 提交cdb事务
    for (int i = 0; i < 2; i++) {
        ret = TPC_CommitCDB(cdbID[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 删表
    ret = TPC_DropTbl(g_dbId, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&relDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 014 事务1写表A，事务2读表A，事务1先提交，预期事务1提交成功、事务2提交成功
TEST_F(NormalTable, V1Com_024_Normal_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 建表
    DB_REL_DEF_STRU relDef = {0};
    uint16_t relID = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_001.json", &relID, &relDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表A
    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // RDB写表A
    int insertNum = 100;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 开启2个cdb事务
    uint32_t cdbID[2] = {0};
    for (int i = 0; i < 2; i++) {
        ret = TPC_BeginCDB(g_dbId, &cdbID[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int j = 0; j < 2; j++) {
        if (j == 0) {  // cdb0写表A
            for (int i = insertNum; i < 2 * insertNum; i++) {
                InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
                DB_DSBUF_STRU dsBuf = {.usRecLen = recLen,
                    .usRecNum = 1,
                    .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
                ret = TPC_InsertRec(cdbID[j], g_dbId, relID, &dsBuf);
                V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            }
        } else {  // cdb1读表A数据
            for (int i = 0; i < insertNum; i++) {
                uint32_t recNum = 0;
                SelectAllType(cdbID[j], g_dbId, relID, relDef.pstFldLst, i, &recNum);
                V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
            }
        }
    }

    // 提交cdb事务，cdb0先提交
    for (int i = 0; i < 2; i++) {
        ret = TPC_CommitCDB(cdbID[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 删表
    ret = TPC_DropTbl(g_dbId, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&relDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 015 事务1写表A，事务2读表A，事务2先提交，预期事务1提交成功、事务2提交成功
TEST_F(NormalTable, V1Com_024_Normal_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 建表
    DB_REL_DEF_STRU relDef = {0};
    uint16_t relID = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_001.json", &relID, &relDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表A
    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // RDB写表A
    int insertNum = 100;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 开启2个cdb事务
    uint32_t cdbID[2] = {0};
    for (int i = 0; i < 2; i++) {
        ret = TPC_BeginCDB(g_dbId, &cdbID[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int j = 0; j < 2; j++) {
        if (j == 0) {  // cdb0写表A
            for (int i = insertNum; i < 2 * insertNum; i++) {
                InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
                DB_DSBUF_STRU dsBuf = {.usRecLen = recLen,
                    .usRecNum = 1,
                    .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
                ret = TPC_InsertRec(cdbID[j], g_dbId, relID, &dsBuf);
                V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            }
        } else {  // cdb1读表A数据
            for (int i = 0; i < insertNum; i++) {
                uint32_t recNum = 0;
                SelectAllType(cdbID[j], g_dbId, relID, relDef.pstFldLst, i, &recNum);
                V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
            }
        }
    }

    // 提交cdb事务,cdb1先提交
    ret = TPC_CommitCDB(cdbID[1]);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_CommitCDB(cdbID[0]);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = TPC_DropTbl(g_dbId, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&relDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 016 事务1写表A，事务2写表A，事务1先提交，预期事务1提交成功，事务2提交失败
TEST_F(NormalTable, V1Com_024_Normal_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 建表
    DB_REL_DEF_STRU relDef = {0};
    uint16_t relID = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_001.json", &relID, &relDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表A
    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // RDB写表A
    int insertNum = 100;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 开启2个cdb事务
    uint32_t cdbID[2] = {0};
    for (int i = 0; i < 2; i++) {
        ret = TPC_BeginCDB(g_dbId, &cdbID[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // cdb0、cdb1都写表A
    for (int j = 0; j < 2; j++) {
        if (j == 0) {  // cdb0写表A，100-200
            for (int i = insertNum; i < 2 * insertNum; i++) {
                InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
                DB_DSBUF_STRU dsBuf = {.usRecLen = recLen,
                    .usRecNum = 1,
                    .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
                ret = TPC_InsertRec(cdbID[j], g_dbId, relID, &dsBuf);
                V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            }
        } else {  // cdb1写表A数据，200-300
            for (int i = 2 * insertNum; i < 3 * insertNum; i++) {
                InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
                DB_DSBUF_STRU dsBuf = {.usRecLen = recLen,
                    .usRecNum = 1,
                    .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
                ret = TPC_InsertRec(cdbID[j], g_dbId, relID, &dsBuf);
                V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            }
        }
    }

    // 提交cdb事务,cdb1先提交
    ret = TPC_CommitCDB(cdbID[0]);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_CommitCDB(cdbID[1]);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_TPC_RDB_MODIFIED, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1,
        TestTPC_SysviewGetLastError(
            "Violated the constraint restrictions.", false));

    // 回滚cdb1前，校验写入的数据
    for (int i = 2 * insertNum; i < 3 * insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(cdbID[1], g_dbId, relID, relDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 回滚事务
    ret = TPC_RollbackCDB(cdbID[1]);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = TPC_DropTbl(g_dbId, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&relDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 017 事务1读表A、写表A，事务2写表A，回滚事务1后提交事务2，预期提交事务2成功
TEST_F(NormalTable, V1Com_024_Normal_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 建表
    DB_REL_DEF_STRU relDef = {0};
    uint16_t relID = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_001.json", &relID, &relDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表A
    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // RDB写表A
    int insertNum = 100;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 开启2个cdb事务
    uint32_t cdbID[2] = {0};
    for (int i = 0; i < 2; i++) {
        ret = TPC_BeginCDB(g_dbId, &cdbID[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int j = 0; j < 2; j++) {
        if (j == 0) {
            // cdb0 读表A
            for (int i = 0; i < insertNum; i++) {
                uint32_t recNum = 0;
                SelectAllType(cdbID[j], g_dbId, relID, relDef.pstFldLst, i, &recNum);
                V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
            }
            // cdb0 写表A 100-200
            for (int i = insertNum; i < 2 * insertNum; i++) {
                InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
                DB_DSBUF_STRU dsBuf = {.usRecLen = recLen,
                    .usRecNum = 1,
                    .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
                ret = TPC_InsertRec(cdbID[j], g_dbId, relID, &dsBuf);
                V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            }
        } else {  // cdb1写表A数据，200-300
            for (int i = 2 * insertNum; i < 3 * insertNum; i++) {
                InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
                DB_DSBUF_STRU dsBuf = {.usRecLen = recLen,
                    .usRecNum = 1,
                    .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
                ret = TPC_InsertRec(cdbID[j], g_dbId, relID, &dsBuf);
                V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            }
        }
    }

    // 回滚cdb0
    ret = TPC_RollbackCDB(cdbID[0]);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 提交cdb1
    ret = TPC_CommitCDB(cdbID[1]);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验写入的数据,0-99,200-299
    for (int i = 0; i < 3 * insertNum; i++) {
        uint32_t recNum = 0;
        if (i == insertNum) {
            i = 2 * insertNum;
        }
        SelectAllType(TPC_GLOBAL_CDB, g_dbId, relID, relDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 删表
    ret = TPC_DropTbl(g_dbId, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&relDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 018 事务1读表A、写表A，事务2读表A，回滚事务1后提交事务2，预期提交事务2成功
TEST_F(NormalTable, V1Com_024_Normal_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 建表
    DB_REL_DEF_STRU relDef = {0};
    uint16_t relID = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_001.json", &relID, &relDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表A
    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // RDB写表A
    int insertNum = 100;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 开启2个cdb事务
    uint32_t cdbID[2] = {0};
    for (int i = 0; i < 2; i++) {
        ret = TPC_BeginCDB(g_dbId, &cdbID[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int j = 0; j < 2; j++) {
        if (j == 0) {
            // cdb0 读表A
            for (int i = 0; i < insertNum; i++) {
                uint32_t recNum = 0;
                SelectAllType(cdbID[j], g_dbId, relID, relDef.pstFldLst, i, &recNum);
                V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
            }
            // cdb0 写表A 100-200
            for (int i = insertNum; i < 2 * insertNum; i++) {
                InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
                DB_DSBUF_STRU dsBuf = {.usRecLen = recLen,
                    .usRecNum = 1,
                    .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
                ret = TPC_InsertRec(cdbID[j], g_dbId, relID, &dsBuf);
                V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            }
        } else {  // cdb1 读表A数据
            for (int i = 0; i < insertNum; i++) {
                uint32_t recNum = 0;
                SelectAllType(cdbID[j], g_dbId, relID, relDef.pstFldLst, i, &recNum);
                V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
            }
        }
    }

    // 回滚cdb0
    ret = TPC_RollbackCDB(cdbID[0]);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 提交cdb1
    ret = TPC_CommitCDB(cdbID[1]);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = TPC_DropTbl(g_dbId, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&relDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 019 事务1删除表A的数据，事务2删表A的数据，事务1先提交，预期事务2提交失败
TEST_F(NormalTable, V1Com_024_Normal_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 建表
    DB_REL_DEF_STRU relDef = {0};
    uint16_t relID = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_001.json", &relID, &relDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表A
    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    // RDB写表A
    int insertNum = 100;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 开启2个cdb事务
    uint32_t cdbID[2] = {0};
    for (int i = 0; i < 2; i++) {
        ret = TPC_BeginCDB(g_dbId, &cdbID[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // cdb0、cdb1都删除表A数据
    for (int j = 0; j < 2; j++) {
        uint32_t recNum = 0;
        Delete(cdbID[j], g_dbId, relID, 0, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(insertNum, recNum);
    }

    // 提交cdb0
    ret = TPC_CommitCDB(cdbID[0]);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 提交cdb1失败，回滚
    ret = TPC_CommitCDB(cdbID[1]);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_TPC_RDB_MODIFIED, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1,
        TestTPC_SysviewGetLastError(
            "Violated the constraint restrictions.", false));

    ret = TPC_RollbackCDB(cdbID[1]);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = TPC_DropTbl(g_dbId, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&relDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 020 事务1读表A、写表B，事务2写表A，事务1先提交，预期事务1提交成功，事务2提交失败
TEST_F(NormalTable, V1Com_024_Normal_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 建表
    DB_REL_DEF_STRU relDef = {0};
    uint16_t relID = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_001.json", &relID, &relDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表A
    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    DB_REL_DEF_STRU relDef2 = {0};
    uint16_t relID2 = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_002.json", &relID2, &relDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表B
    uint16_t recLen2 = GetRecLen(&relDef2);
    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(recLen2);
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    // RDB写表A
    int insertNum = 100;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 开启2个cdb事务
    uint32_t cdbID[2] = {0};
    for (int i = 0; i < 2; i++) {
        ret = TPC_BeginCDB(g_dbId, &cdbID[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int j = 0; j < 2; j++) {
        if (j == 0) {
            // cdb0 读表A
            for (int i = 0; i < insertNum; i++) {
                uint32_t recNum = 0;
                SelectAllType(cdbID[j], g_dbId, relID, relDef.pstFldLst, i, &recNum);
                V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
            }
            // cdb0 写表B 0-99
            for (int i = 0; i < insertNum; i++) {
                InsertAllType(recBuf2, relDef2.pstFldLst, i, recLen2);
                DB_DSBUF_STRU dsBuf = {.usRecLen = recLen2,
                    .usRecNum = 1,
                    .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
                ret = TPC_InsertRec(cdbID[j], g_dbId, relID2, &dsBuf);
                V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            }
        } else {  // cdb1写表A数据，100-199
            for (int i = insertNum; i < 2 * insertNum; i++) {
                InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
                DB_DSBUF_STRU dsBuf = {.usRecLen = recLen,
                    .usRecNum = 1,
                    .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
                ret = TPC_InsertRec(cdbID[j], g_dbId, relID, &dsBuf);
                V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            }
        }
    }

    // 提交cdb0
    ret = TPC_CommitCDB(cdbID[0]);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 提交cdb1
    ret = TPC_CommitCDB(cdbID[1]);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_TPC_RDB_MODIFIED, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1,
        TestTPC_SysviewGetLastError(
            "Violated the constraint restrictions.", false));

    // 校验cdb1写入的数据, 100-199
    for (int i = insertNum; i < 2 * insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(cdbID[1], g_dbId, relID, relDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    ret = TPC_RollbackCDB(cdbID[1]);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验cdb0写入表B的数据, 0-99
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, g_dbId, relID2, relDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 删表
    ret = TPC_DropTbl(g_dbId, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(g_dbId, relID2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&relDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&relDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 021 事务1读表A、写表B，事务2写表A，事务2先提交，预期事务2提交成功，事务1提交失败
TEST_F(NormalTable, V1Com_024_Normal_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 建表
    DB_REL_DEF_STRU relDef = {0};
    uint16_t relID = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_001.json", &relID, &relDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表A
    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    DB_REL_DEF_STRU relDef2 = {0};
    uint16_t relID2 = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_002.json", &relID2, &relDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表B
    uint16_t recLen2 = GetRecLen(&relDef2);
    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(recLen2);
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    // RDB写表A
    int insertNum = 100;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 开启2个cdb事务
    uint32_t cdbID[2] = {0};
    for (int i = 0; i < 2; i++) {
        ret = TPC_BeginCDB(g_dbId, &cdbID[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int j = 0; j < 2; j++) {
        if (j == 0) {
            // cdb0 读表A
            for (int i = 0; i < insertNum; i++) {
                uint32_t recNum = 0;
                SelectAllType(cdbID[j], g_dbId, relID, relDef.pstFldLst, i, &recNum);
                V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
            }
            // cdb0 写表B 0-99
            for (int i = 0; i < insertNum; i++) {
                InsertAllType(recBuf2, relDef2.pstFldLst, i, recLen2);
                DB_DSBUF_STRU dsBuf = {.usRecLen = recLen2,
                    .usRecNum = 1,
                    .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
                ret = TPC_InsertRec(cdbID[j], g_dbId, relID2, &dsBuf);
                V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            }
        } else {  // cdb1写表A数据，100-199
            for (int i = insertNum; i < 2 * insertNum; i++) {
                InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
                DB_DSBUF_STRU dsBuf = {.usRecLen = recLen,
                    .usRecNum = 1,
                    .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
                ret = TPC_InsertRec(cdbID[j], g_dbId, relID, &dsBuf);
                V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            }
        }
    }

    // 提交cdb1
    ret = TPC_CommitCDB(cdbID[1]);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 提交cdb0
    ret = TPC_CommitCDB(cdbID[0]);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_TPC_RDB_MODIFIED, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1,
        TestTPC_SysviewGetLastError(
            "Violated the constraint restrictions.", false));

    // 校验cdb0写入的数据, 0-99
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(cdbID[0], g_dbId, relID2, relDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 回滚cdb0
    ret = TPC_RollbackCDB(cdbID[0]);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验cdb1写入的数据, 100-199
    for (int i = insertNum; i < 2 * insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, g_dbId, relID, relDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 删表
    ret = TPC_DropTbl(g_dbId, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(g_dbId, relID2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&relDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&relDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 022 事务1读表A、写表B，事务2更新表A，事务2先提交，预期事务2提交成功，事务1提交失败
TEST_F(NormalTable, V1Com_024_Normal_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 建表
    DB_REL_DEF_STRU relDef = {0};
    uint16_t relID = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_001.json", &relID, &relDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表A
    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    DB_REL_DEF_STRU relDef2 = {0};
    uint16_t relID2 = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_002.json", &relID2, &relDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表B
    uint16_t recLen2 = GetRecLen(&relDef2);
    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(recLen2);
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    // RDB写表A
    int insertNum = 100;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 开启2个cdb事务
    uint32_t cdbID[2] = {0};
    for (int i = 0; i < 2; i++) {
        ret = TPC_BeginCDB(g_dbId, &cdbID[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int j = 0; j < 2; j++) {
        if (j == 0) {
            // cdb0 读表A
            for (int i = 0; i < insertNum; i++) {
                uint32_t recNum = 0;
                SelectAllType(cdbID[j], g_dbId, relID, relDef.pstFldLst, i, &recNum);
                V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
            }
            // cdb0 写表B 0-99
            for (int i = 0; i < insertNum; i++) {
                InsertAllType(recBuf2, relDef2.pstFldLst, i, recLen2);
                DB_DSBUF_STRU dsBuf = {.usRecLen = recLen2,
                    .usRecNum = 1,
                    .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
                ret = TPC_InsertRec(cdbID[j], g_dbId, relID2, &dsBuf);
                V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            }
        } else {  // cdb1更新表A数据，将0-99更新至10000-10099
            for (int i = 0; i < insertNum; i++) {
                uint32_t recNum = 0;
                UpdateAllType(cdbID[j], g_dbId, relID, recBuf, relDef.pstFldLst, i, &recNum);
                V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
            }
        }
    }

    // 提交cdb1
    ret = TPC_CommitCDB(cdbID[1]);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 提交cdb0
    ret = TPC_CommitCDB(cdbID[0]);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_TPC_RDB_MODIFIED, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1,
        TestTPC_SysviewGetLastError(
            "Violated the constraint restrictions.", false));

    // 校验cdb0写入的数据, 0-99
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(cdbID[0], g_dbId, relID2, relDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 回滚cdb0
    ret = TPC_RollbackCDB(cdbID[0]);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验cdb1写入的数据, 100-199
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, g_dbId, relID, relDef.pstFldLst, i + 10000, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 删表
    ret = TPC_DropTbl(g_dbId, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(g_dbId, relID2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&relDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&relDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 023 事务1读表A、写表B，事务2删除表A数据，事务2先提交，预期事务2提交成功，事务1提交失败
TEST_F(NormalTable, V1Com_024_Normal_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 建表
    DB_REL_DEF_STRU relDef = {0};
    uint16_t relID = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_001.json", &relID, &relDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表A
    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    DB_REL_DEF_STRU relDef2 = {0};
    uint16_t relID2 = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_002.json", &relID2, &relDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表B
    uint16_t recLen2 = GetRecLen(&relDef2);
    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(recLen2);
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    // RDB写表A
    int insertNum = 100;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 开启2个cdb事务
    uint32_t cdbID[2] = {0};
    for (int i = 0; i < 2; i++) {
        ret = TPC_BeginCDB(g_dbId, &cdbID[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int j = 0; j < 2; j++) {
        if (j == 0) {
            // cdb0 读表A
            for (int i = 0; i < insertNum; i++) {
                uint32_t recNum = 0;
                SelectAllType(cdbID[j], g_dbId, relID, relDef.pstFldLst, i, &recNum);
                V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
            }
            // cdb0 写表B 0-99
            for (int i = 0; i < insertNum; i++) {
                InsertAllType(recBuf2, relDef2.pstFldLst, i, recLen2);
                DB_DSBUF_STRU dsBuf = {.usRecLen = recLen2,
                    .usRecNum = 1,
                    .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
                ret = TPC_InsertRec(cdbID[j], g_dbId, relID2, &dsBuf);
                V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            }
        } else {  // cdb1删除表A数据
            uint32_t recNum = 0;
            Delete(cdbID[j], g_dbId, relID, 0, &recNum);
            V1_AW_MACRO_EXPECT_EQ_INT(insertNum, recNum);
        }
    }

    // 提交cdb1
    ret = TPC_CommitCDB(cdbID[1]);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 提交cdb0
    ret = TPC_CommitCDB(cdbID[0]);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_TPC_RDB_MODIFIED, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1,
        TestTPC_SysviewGetLastError(
            "Violated the constraint restrictions.", false));

    // 校验cdb0写入的数据, 0-99
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(cdbID[0], g_dbId, relID2, relDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 回滚cdb0
    ret = TPC_RollbackCDB(cdbID[0]);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 数据删除后，校验
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, g_dbId, relID, relDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(0, recNum);
    }

    // 删表
    ret = TPC_DropTbl(g_dbId, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(g_dbId, relID2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&relDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&relDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 024 事务1读表A、写表B，事务2读表A，事务1先提交，预期事务1提交成功，事务2提交成功
TEST_F(NormalTable, V1Com_024_Normal_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 建表
    DB_REL_DEF_STRU relDef = {0};
    uint16_t relID = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_001.json", &relID, &relDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表A
    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    DB_REL_DEF_STRU relDef2 = {0};
    uint16_t relID2 = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_002.json", &relID2, &relDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表B
    uint16_t recLen2 = GetRecLen(&relDef2);
    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(recLen2);
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    // RDB写表A
    int insertNum = 100;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 开启2个cdb事务
    uint32_t cdbID[2] = {0};
    for (int i = 0; i < 2; i++) {
        ret = TPC_BeginCDB(g_dbId, &cdbID[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int j = 0; j < 2; j++) {
        if (j == 0) {
            // cdb0 读表A
            for (int i = 0; i < insertNum; i++) {
                uint32_t recNum = 0;
                SelectAllType(cdbID[j], g_dbId, relID, relDef.pstFldLst, i, &recNum);
                V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
            }
            // cdb0 写表B 0-99
            for (int i = 0; i < insertNum; i++) {
                InsertAllType(recBuf2, relDef2.pstFldLst, i, recLen2);
                DB_DSBUF_STRU dsBuf = {.usRecLen = recLen2,
                    .usRecNum = 1,
                    .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
                ret = TPC_InsertRec(cdbID[j], g_dbId, relID2, &dsBuf);
                V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            }
        } else {  // cdb1读表A数据
            for (int i = 0; i < insertNum; i++) {
                uint32_t recNum = 0;
                SelectAllType(cdbID[j], g_dbId, relID, relDef.pstFldLst, i, &recNum);
                V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
            }
        }
    }

    // 提交cdb1
    ret = TPC_CommitCDB(cdbID[0]);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 提交cdb0
    ret = TPC_CommitCDB(cdbID[1]);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验cdb0写入的数据, 0-99
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, g_dbId, relID2, relDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 删表
    ret = TPC_DropTbl(g_dbId, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(g_dbId, relID2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&relDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&relDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 025 事务1读表A、写表B，事务2读表A，事务2先提交，预期事务2提交成功，事务1提交成功
TEST_F(NormalTable, V1Com_024_Normal_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 建表
    DB_REL_DEF_STRU relDef = {0};
    uint16_t relID = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_001.json", &relID, &relDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表A
    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    DB_REL_DEF_STRU relDef2 = {0};
    uint16_t relID2 = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_002.json", &relID2, &relDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表B
    uint16_t recLen2 = GetRecLen(&relDef2);
    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(recLen2);
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    // RDB写表A
    int insertNum = 100;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 开启2个cdb事务
    uint32_t cdbID[2] = {0};
    for (int i = 0; i < 2; i++) {
        ret = TPC_BeginCDB(g_dbId, &cdbID[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int j = 0; j < 2; j++) {
        if (j == 0) {
            // cdb0 读表A
            for (int i = 0; i < insertNum; i++) {
                uint32_t recNum = 0;
                SelectAllType(cdbID[j], g_dbId, relID, relDef.pstFldLst, i, &recNum);
                V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
            }
            // cdb0 写表B 0-99
            for (int i = 0; i < insertNum; i++) {
                InsertAllType(recBuf2, relDef2.pstFldLst, i, recLen2);
                DB_DSBUF_STRU dsBuf = {.usRecLen = recLen2,
                    .usRecNum = 1,
                    .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
                ret = TPC_InsertRec(cdbID[j], g_dbId, relID2, &dsBuf);
                V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            }
        } else {  // cdb1读表A数据
            for (int i = 0; i < insertNum; i++) {
                uint32_t recNum = 0;
                SelectAllType(cdbID[j], g_dbId, relID, relDef.pstFldLst, i, &recNum);
                V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
            }
        }
    }

    // 提交cdb1
    ret = TPC_CommitCDB(cdbID[1]);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 提交cdb0
    ret = TPC_CommitCDB(cdbID[0]);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验cdb0写入的数据, 0-99
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, g_dbId, relID2, relDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 删表
    ret = TPC_DropTbl(g_dbId, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(g_dbId, relID2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&relDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&relDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 026 事务1读表A、写表B，事务2读表A、写表B，事务1先提交，预期事务1提交成功，事务2提交失败
TEST_F(NormalTable, V1Com_024_Normal_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 建表
    DB_REL_DEF_STRU relDef = {0};
    uint16_t relID = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_001.json", &relID, &relDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表A
    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    DB_REL_DEF_STRU relDef2 = {0};
    uint16_t relID2 = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_002.json", &relID2, &relDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表B
    uint16_t recLen2 = GetRecLen(&relDef2);
    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(recLen2);
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    // RDB写表A
    int insertNum = 100;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 开启2个cdb事务
    uint32_t cdbID[2] = {0};
    for (int i = 0; i < 2; i++) {
        ret = TPC_BeginCDB(g_dbId, &cdbID[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int j = 0; j < 2; j++) {
        if (j == 0) {
            // cdb0 读表A
            for (int i = 0; i < insertNum; i++) {
                uint32_t recNum = 0;
                SelectAllType(cdbID[j], g_dbId, relID, relDef.pstFldLst, i, &recNum);
                V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
            }
            // cdb0 写表B 0-99
            for (int i = 0; i < insertNum; i++) {
                InsertAllType(recBuf2, relDef2.pstFldLst, i, recLen2);
                DB_DSBUF_STRU dsBuf = {.usRecLen = recLen2,
                    .usRecNum = 1,
                    .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
                ret = TPC_InsertRec(cdbID[j], g_dbId, relID2, &dsBuf);
                V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            }
        } else {
            // cdb1 读表A数据
            for (int i = 0; i < insertNum; i++) {
                uint32_t recNum = 0;
                SelectAllType(cdbID[j], g_dbId, relID, relDef.pstFldLst, i, &recNum);
                V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
            }
            // cdb1 写表B 100-199
            for (int i = insertNum; i < 2 * insertNum; i++) {
                InsertAllType(recBuf2, relDef2.pstFldLst, i, recLen2);
                DB_DSBUF_STRU dsBuf = {.usRecLen = recLen2,
                    .usRecNum = 1,
                    .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
                ret = TPC_InsertRec(cdbID[j], g_dbId, relID2, &dsBuf);
                V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            }
        }
    }

    // 提交cdb1
    ret = TPC_CommitCDB(cdbID[0]);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 提交cdb0
    ret = TPC_CommitCDB(cdbID[1]);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_TPC_RDB_MODIFIED, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1,
        TestTPC_SysviewGetLastError(
            "Violated the constraint restrictions.", false));

    // 校验cdb1写入的数据, 100-199
    for (int i = insertNum; i < 2 * insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(cdbID[1], g_dbId, relID2, relDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 回滚cdb1
    ret = TPC_RollbackCDB(cdbID[1]);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验cdb0写入表B的数据0-99
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, g_dbId, relID2, relDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 删表
    ret = TPC_DropTbl(g_dbId, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(g_dbId, relID2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&relDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&relDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 027 事务1读表A、写表B，事务2读表A、写表C，事务1先提交，预期事务1提交成功，事务2提交成功
TEST_F(NormalTable, V1Com_024_Normal_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 建表
    DB_REL_DEF_STRU relDef = {0};
    uint16_t relID = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_001.json", &relID, &relDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表A
    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    DB_REL_DEF_STRU relDef2 = {0};
    uint16_t relID2 = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_002.json", &relID2, &relDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表B
    uint16_t recLen2 = GetRecLen(&relDef2);
    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(recLen2);
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    DB_REL_DEF_STRU relDef3 = {0};
    uint16_t relID3 = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_003.json", &relID3, &relDef3);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表C
    uint16_t recLen3 = GetRecLen(&relDef3);
    uint8_t *recBuf3 = (uint8_t *)TEST_V1_MALLOC(recLen3);
    (void)memset_s(recBuf3, recLen3, 0x00, recLen3);

    // RDB写表A
    int insertNum = 100;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 开启2个cdb事务
    uint32_t cdbID[2] = {0};
    for (int i = 0; i < 2; i++) {
        ret = TPC_BeginCDB(g_dbId, &cdbID[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int j = 0; j < 2; j++) {
        if (j == 0) {
            // cdb0 读表A
            for (int i = 0; i < insertNum; i++) {
                uint32_t recNum = 0;
                SelectAllType(cdbID[j], g_dbId, relID, relDef.pstFldLst, i, &recNum);
                V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
            }
            // cdb0 写表B 0-99
            for (int i = 0; i < insertNum; i++) {
                InsertAllType(recBuf2, relDef2.pstFldLst, i, recLen2);
                DB_DSBUF_STRU dsBuf = {.usRecLen = recLen2,
                    .usRecNum = 1,
                    .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
                ret = TPC_InsertRec(cdbID[j], g_dbId, relID2, &dsBuf);
                V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            }
        } else {
            // cdb1 读表A数据
            for (int i = 0; i < insertNum; i++) {
                uint32_t recNum = 0;
                SelectAllType(cdbID[j], g_dbId, relID, relDef.pstFldLst, i, &recNum);
                V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
            }
            // cdb1 写表C 0-99
            for (int i = 0; i < insertNum; i++) {
                InsertAllType(recBuf3, relDef3.pstFldLst, i, recLen3);
                DB_DSBUF_STRU dsBuf = {.usRecLen = recLen3,
                    .usRecNum = 1,
                    .StdBuf = {.ulBufLen = recLen3, .ulActLen = recLen3, .pucData = recBuf3}};
                ret = TPC_InsertRec(cdbID[j], g_dbId, relID3, &dsBuf);
                V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            }
        }
    }

    // 提交cdb0
    ret = TPC_CommitCDB(cdbID[0]);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 提交cdb1
    ret = TPC_CommitCDB(cdbID[1]);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验cdb0写入表B的数据, 0-99
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, g_dbId, relID2, relDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 校验cdb1写入表C的数据0-99
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, g_dbId, relID3, relDef3.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 删表
    ret = TPC_DropTbl(g_dbId, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(g_dbId, relID2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(g_dbId, relID3, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&relDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&relDef2);
    TEST_V1_FREE(recBuf3);
    FreeRelDef(&relDef3);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 028 事务1读表A、更新表B，事务2读表A、更新表C，事务1先提交，预期事务1提交成功，事务2提交成功
TEST_F(NormalTable, V1Com_024_Normal_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 建表
    DB_REL_DEF_STRU relDef = {0};
    uint16_t relID = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_001.json", &relID, &relDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表A
    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    DB_REL_DEF_STRU relDef2 = {0};
    uint16_t relID2 = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_002.json", &relID2, &relDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表B
    uint16_t recLen2 = GetRecLen(&relDef2);
    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(recLen2);
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    DB_REL_DEF_STRU relDef3 = {0};
    uint16_t relID3 = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_003.json", &relID3, &relDef3);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表C
    uint16_t recLen3 = GetRecLen(&relDef3);
    uint8_t *recBuf3 = (uint8_t *)TEST_V1_MALLOC(recLen3);
    (void)memset_s(recBuf3, recLen3, 0x00, recLen3);

    // RDB写表A
    int insertNum = 100;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // RDB写表B
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, relDef2.pstFldLst, i, recLen2);
        DB_DSBUF_STRU dsBuf = {.usRecLen = recLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, relID2, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // RDB写表C
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf3, relDef3.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = recLen3,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = recLen3, .ulActLen = recLen3, .pucData = recBuf3}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, relID3, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 开启2个cdb事务
    uint32_t cdbID[2] = {0};
    for (int i = 0; i < 2; i++) {
        ret = TPC_BeginCDB(g_dbId, &cdbID[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int j = 0; j < 2; j++) {
        if (j == 0) {
            // cdb0 读表A
            for (int i = 0; i < insertNum; i++) {
                uint32_t recNum = 0;
                SelectAllType(cdbID[j], g_dbId, relID, relDef.pstFldLst, i, &recNum);
                V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
            }
            // cdb0 更新表B 0-99至10000-10099
            for (int i = 0; i < insertNum; i++) {
                uint32_t recNum = 0;
                UpdateAllType(cdbID[j], g_dbId, relID2, recBuf2, relDef2.pstFldLst, i, &recNum);
                V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
            }
        } else {
            // cdb1 读表A数据
            for (int i = 0; i < insertNum; i++) {
                uint32_t recNum = 0;
                SelectAllType(cdbID[j], g_dbId, relID, relDef.pstFldLst, i, &recNum);
                V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
            }
            // cdb1 更新表C 0-99至10000-10099
            for (int i = 0; i < insertNum; i++) {
                uint32_t recNum = 0;
                UpdateAllType(cdbID[j], g_dbId, relID3, recBuf3, relDef3.pstFldLst, i, &recNum);
                V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
            }
        }
    }

    // 提交cdb0
    ret = TPC_CommitCDB(cdbID[0]);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 提交cdb1
    ret = TPC_CommitCDB(cdbID[1]);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验cdb0写入表B的数据, 0-99
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, g_dbId, relID2, relDef2.pstFldLst, i + 10000, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 校验cdb1写入表C的数据0-99
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, g_dbId, relID3, relDef3.pstFldLst, i + 10000, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 删表
    ret = TPC_DropTbl(g_dbId, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(g_dbId, relID2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(g_dbId, relID3, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&relDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&relDef2);
    TEST_V1_FREE(recBuf3);
    FreeRelDef(&relDef3);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 029 事务1读表A、删除表B数据，事务2读表A、删除表C数据，事务1先提交，预期事务1提交成功，事务2提交成功
TEST_F(NormalTable, V1Com_024_Normal_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 建表
    DB_REL_DEF_STRU relDef = {0};
    uint16_t relID = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_001.json", &relID, &relDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表A
    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    DB_REL_DEF_STRU relDef2 = {0};
    uint16_t relID2 = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_002.json", &relID2, &relDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表B
    uint16_t recLen2 = GetRecLen(&relDef2);
    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(recLen2);
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    DB_REL_DEF_STRU relDef3 = {0};
    uint16_t relID3 = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_003.json", &relID3, &relDef3);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表C
    uint16_t recLen3 = GetRecLen(&relDef3);
    uint8_t *recBuf3 = (uint8_t *)TEST_V1_MALLOC(recLen3);
    (void)memset_s(recBuf3, recLen3, 0x00, recLen3);

    // RDB写表A
    int insertNum = 100;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // RDB写表B
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, relDef2.pstFldLst, i, recLen2);
        DB_DSBUF_STRU dsBuf = {.usRecLen = recLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, relID2, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // RDB写表C
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf3, relDef3.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = recLen3,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = recLen3, .ulActLen = recLen3, .pucData = recBuf3}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, relID3, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 开启2个cdb事务
    uint32_t cdbID[2] = {0};
    for (int i = 0; i < 2; i++) {
        ret = TPC_BeginCDB(g_dbId, &cdbID[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int j = 0; j < 2; j++) {
        if (j == 0) {
            // cdb0 读表A
            for (int i = 0; i < insertNum; i++) {
                uint32_t recNum = 0;
                SelectAllType(cdbID[j], g_dbId, relID, relDef.pstFldLst, i, &recNum);
                V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
            }
            // cdb0 删除表B数据
            uint32_t recNum = 0;
            Delete(cdbID[j], g_dbId, relID2, 0, &recNum);
            V1_AW_MACRO_EXPECT_EQ_INT(insertNum, recNum);
        } else {
            // cdb1 读表A数据
            for (int i = 0; i < insertNum; i++) {
                uint32_t recNum = 0;
                SelectAllType(cdbID[j], g_dbId, relID, relDef.pstFldLst, i, &recNum);
                V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
            }
            // cdb1 删除表C数据
            uint32_t recNum = 0;
            Delete(cdbID[j], g_dbId, relID3, 0, &recNum);
            V1_AW_MACRO_EXPECT_EQ_INT(insertNum, recNum);
        }
    }

    // 提交cdb0
    ret = TPC_CommitCDB(cdbID[0]);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 提交cdb1
    ret = TPC_CommitCDB(cdbID[1]);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验表B的数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, g_dbId, relID2, relDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(0, recNum);
    }

    // 校验表C的数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, g_dbId, relID3, relDef3.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(0, recNum);
    }

    // 删表
    ret = TPC_DropTbl(g_dbId, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(g_dbId, relID2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(g_dbId, relID3, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&relDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&relDef2);
    TEST_V1_FREE(recBuf3);
    FreeRelDef(&relDef3);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 030 事务1读表A、读表B、写表C，事务2读表A、读表B、写表C，事务1先提交，预期事务1提交成功，事务2提交失败
TEST_F(NormalTable, V1Com_024_Normal_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 建表
    DB_REL_DEF_STRU relDef = {0};
    uint16_t relID = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_001.json", &relID, &relDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表A
    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    DB_REL_DEF_STRU relDef2 = {0};
    uint16_t relID2 = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_002.json", &relID2, &relDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表B
    uint16_t recLen2 = GetRecLen(&relDef2);
    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(recLen2);
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    DB_REL_DEF_STRU relDef3 = {0};
    uint16_t relID3 = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_003.json", &relID3, &relDef3);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表C
    uint16_t recLen3 = GetRecLen(&relDef3);
    uint8_t *recBuf3 = (uint8_t *)TEST_V1_MALLOC(recLen3);
    (void)memset_s(recBuf3, recLen3, 0x00, recLen3);

    // RDB写表A
    int insertNum = 100;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // RDB写表B
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, relDef2.pstFldLst, i, recLen2);
        DB_DSBUF_STRU dsBuf = {.usRecLen = recLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, relID2, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 开启2个cdb事务
    uint32_t cdbID[2] = {0};
    for (int i = 0; i < 2; i++) {
        ret = TPC_BeginCDB(g_dbId, &cdbID[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int j = 0; j < 2; j++) {
        // cdb[j] 读表A
        for (int i = 0; i < insertNum; i++) {
            uint32_t recNum = 0;
            SelectAllType(cdbID[j], g_dbId, relID, relDef.pstFldLst, i, &recNum);
            V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
        }
        // cdb[j] 读表B
        for (int i = 0; i < insertNum; i++) {
            uint32_t recNum = 0;
            SelectAllType(cdbID[j], g_dbId, relID2, relDef2.pstFldLst, i, &recNum);
            V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
        }
        // cdb[j] 写表C
        for (int i = insertNum; i < 2 * insertNum; i++) {
            InsertAllType(recBuf3, relDef3.pstFldLst, i, recLen3);
            DB_DSBUF_STRU dsBuf = {.usRecLen = recLen3,
                .usRecNum = 1,
                .StdBuf = {.ulBufLen = recLen3, .ulActLen = recLen3, .pucData = recBuf3}};
            ret = TPC_InsertRec(cdbID[j], g_dbId, relID3, &dsBuf);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    // 提交cdb0
    ret = TPC_CommitCDB(cdbID[0]);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 提交cdb1
    ret = TPC_CommitCDB(cdbID[1]);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_TPC_RDB_MODIFIED, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1,
        TestTPC_SysviewGetLastError(
            "Violated the constraint restrictions.", false));

    // 校验cdb1写入表C的数据100-199
    for (int i = insertNum; i < 2 * insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(cdbID[1], g_dbId, relID3, relDef3.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 回滚cdb1
    ret = TPC_RollbackCDB(cdbID[1]);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验cdb1写入表C的数据100-199
    for (int i = insertNum; i < 2 * insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, g_dbId, relID3, relDef3.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 删表
    ret = TPC_DropTbl(g_dbId, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(g_dbId, relID2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(g_dbId, relID3, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&relDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&relDef2);
    TEST_V1_FREE(recBuf3);
    FreeRelDef(&relDef3);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 031 事务1读表A、写表B，事务2写表A，回滚事务1后提交事务2，预期事务1回滚成功，事务2提交成功
TEST_F(NormalTable, V1Com_024_Normal_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 建表
    DB_REL_DEF_STRU relDef = {0};
    uint16_t relID = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_001.json", &relID, &relDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表A
    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    DB_REL_DEF_STRU relDef2 = {0};
    uint16_t relID2 = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_002.json", &relID2, &relDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表B
    uint16_t recLen2 = GetRecLen(&relDef2);
    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(recLen2);
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    // RDB写表A
    int insertNum = 100;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 开启2个cdb事务
    uint32_t cdbID[2] = {0};
    for (int i = 0; i < 2; i++) {
        ret = TPC_BeginCDB(g_dbId, &cdbID[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int j = 0; j < 2; j++) {
        if (j == 0) {
            // cdb0 读表A
            for (int i = 0; i < insertNum; i++) {
                uint32_t recNum = 0;
                SelectAllType(cdbID[j], g_dbId, relID, relDef.pstFldLst, i, &recNum);
                V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
            }
            // cdb0 写表B 0-99
            for (int i = 0; i < insertNum; i++) {
                InsertAllType(recBuf2, relDef2.pstFldLst, i, recLen2);
                DB_DSBUF_STRU dsBuf = {.usRecLen = recLen2,
                    .usRecNum = 1,
                    .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
                ret = TPC_InsertRec(cdbID[j], g_dbId, relID2, &dsBuf);
                V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            }
        } else {
            // cdb1 写表A 100-199
            for (int i = insertNum; i < 2 * insertNum; i++) {
                InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
                DB_DSBUF_STRU dsBuf = {.usRecLen = recLen,
                    .usRecNum = 1,
                    .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
                ret = TPC_InsertRec(cdbID[j], g_dbId, relID, &dsBuf);
                V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            }
        }
    }

    // 回滚cdb0
    ret = TPC_RollbackCDB(cdbID[0]);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 提交cdb1
    ret = TPC_CommitCDB(cdbID[1]);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验cdb1写入表A的数据100-199
    for (int i = insertNum; i < 2 * insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, g_dbId, relID, relDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 删表
    ret = TPC_DropTbl(g_dbId, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(g_dbId, relID2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&relDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&relDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 032 空表A，事务1扫描表A、写表B，事务2删表A，先提交事务2，预期事务1提交失败
TEST_F(NormalTable, V1Com_024_Normal_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 建表
    DB_REL_DEF_STRU relDef = {0};
    uint16_t relID = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_001.json", &relID, &relDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表A
    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    DB_REL_DEF_STRU relDef2 = {0};
    uint16_t relID2 = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_002.json", &relID2, &relDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表B
    uint16_t recLen2 = GetRecLen(&relDef2);
    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(recLen2);
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    // 开启2个cdb事务
    uint32_t cdbID[2] = {0};
    for (int i = 0; i < 2; i++) {
        ret = TPC_BeginCDB(g_dbId, &cdbID[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    int insertNum = 100;
    for (int j = 0; j < 2; j++) {
        if (j == 0) {
            // cdb0 读表A
            for (int i = 0; i < insertNum; i++) {
                uint32_t recNum = 0;
                SelectAllType(cdbID[j], g_dbId, relID, relDef.pstFldLst, i, &recNum);
                V1_AW_MACRO_EXPECT_EQ_INT(0, recNum);
            }
            // cdb0 写表B
            for (int i = 0; i < insertNum; i++) {
                InsertAllType(recBuf2, relDef2.pstFldLst, i, recLen2);
                DB_DSBUF_STRU dsBuf = {.usRecLen = recLen2,
                    .usRecNum = 1,
                    .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
                ret = TPC_InsertRec(cdbID[j], g_dbId, relID2, &dsBuf);
                V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            }
        } else {
            // cdb1 删除表A数据，空删
            uint32_t recNum = 0;
            Delete(cdbID[j], g_dbId, relID, 0, &recNum);
            V1_AW_MACRO_EXPECT_EQ_INT(0, recNum);
        }
    }

    // 提交cdb1
    ret = TPC_CommitCDB(cdbID[1]);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 提交cdb0，失败
    ret = TPC_CommitCDB(cdbID[0]);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_TPC_RDB_MODIFIED, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1,
        TestTPC_SysviewGetLastError(
            "Violated the constraint restrictions.", false));

    // 校验cdb0 写入表B的数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(cdbID[0], g_dbId, relID2, relDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 回滚cdb1
    ret = TPC_RollbackCDB(cdbID[0]);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = TPC_DropTbl(g_dbId, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(g_dbId, relID2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&relDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&relDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 033 空表A，事务1扫描表A、写表B，事务2删表A，先提交事务1，预期事务2提交失败
TEST_F(NormalTable, V1Com_024_Normal_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 建表
    DB_REL_DEF_STRU relDef = {0};
    uint16_t relID = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_001.json", &relID, &relDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表A
    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    DB_REL_DEF_STRU relDef2 = {0};
    uint16_t relID2 = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_002.json", &relID2, &relDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表B
    uint16_t recLen2 = GetRecLen(&relDef2);
    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(recLen2);
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    // 开启2个cdb事务
    uint32_t cdbID[2] = {0};
    for (int i = 0; i < 2; i++) {
        ret = TPC_BeginCDB(g_dbId, &cdbID[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    int insertNum = 100;
    for (int j = 0; j < 2; j++) {
        if (j == 0) {
            // cdb0 读表A
            for (int i = 0; i < insertNum; i++) {
                uint32_t recNum = 0;
                SelectAllType(cdbID[j], g_dbId, relID, relDef.pstFldLst, i, &recNum);
                V1_AW_MACRO_EXPECT_EQ_INT(0, recNum);
            }
            // cdb0 写表B
            for (int i = 0; i < insertNum; i++) {
                InsertAllType(recBuf2, relDef2.pstFldLst, i, recLen2);
                DB_DSBUF_STRU dsBuf = {.usRecLen = recLen2,
                    .usRecNum = 1,
                    .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
                ret = TPC_InsertRec(cdbID[j], g_dbId, relID2, &dsBuf);
                V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            }
        } else {
            // cdb1 删除表A数据，空删
            uint32_t recNum = 0;
            Delete(cdbID[j], g_dbId, relID, 0, &recNum);
            V1_AW_MACRO_EXPECT_EQ_INT(0, recNum);
        }
    }

    // 提交cdb1
    ret = TPC_CommitCDB(cdbID[0]);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 提交cdb0，失败
    ret = TPC_CommitCDB(cdbID[1]);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_TPC_RDB_MODIFIED, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1,
        TestTPC_SysviewGetLastError(
            "Violated the constraint restrictions.", false));

    // 回滚cdb1
    ret = TPC_RollbackCDB(cdbID[1]);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验cdb0 写入表b的数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, g_dbId, relID2, relDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 删表
    ret = TPC_DropTbl(g_dbId, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(g_dbId, relID2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&relDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&relDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 034 空表A，事务1扫描表A、写表B，事务2更新A，先提交事务2，预期事务1提交失败
TEST_F(NormalTable, V1Com_024_Normal_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 建表
    DB_REL_DEF_STRU relDef = {0};
    uint16_t relID = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_001.json", &relID, &relDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表A
    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    DB_REL_DEF_STRU relDef2 = {0};
    uint16_t relID2 = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_002.json", &relID2, &relDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表B
    uint16_t recLen2 = GetRecLen(&relDef2);
    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(recLen2);
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    // 开启2个cdb事务
    uint32_t cdbID[2] = {0};
    for (int i = 0; i < 2; i++) {
        ret = TPC_BeginCDB(g_dbId, &cdbID[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    int insertNum = 100;
    for (int j = 0; j < 2; j++) {
        if (j == 0) {
            // cdb0 读表A
            for (int i = 0; i < insertNum; i++) {
                uint32_t recNum = 0;
                SelectAllType(cdbID[j], g_dbId, relID, relDef.pstFldLst, i, &recNum);
                V1_AW_MACRO_EXPECT_EQ_INT(0, recNum);
            }
            // cdb0 写表B
            for (int i = 0; i < insertNum; i++) {
                InsertAllType(recBuf2, relDef2.pstFldLst, i, recLen2);
                DB_DSBUF_STRU dsBuf = {.usRecLen = recLen2,
                    .usRecNum = 1,
                    .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
                ret = TPC_InsertRec(cdbID[j], g_dbId, relID2, &dsBuf);
                V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            }
        } else {
            // cdb1 更新表A数据，相当于没写入
            for (int i = 0; i < insertNum; i++) {
                uint32_t recNum = 0;
                UpdateAllType(cdbID[j], g_dbId, relID, recBuf, relDef.pstFldLst, i, &recNum);
                V1_AW_MACRO_EXPECT_EQ_INT(0, recNum);
            }
        }
    }

    // 提交cdb1
    ret = TPC_CommitCDB(cdbID[1]);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 提交cdb0，失败
    ret = TPC_CommitCDB(cdbID[0]);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_TPC_RDB_MODIFIED, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1,
        TestTPC_SysviewGetLastError(
            "Violated the constraint restrictions.", false));

    // 校验cdb0 写入表B的数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(cdbID[0], g_dbId, relID2, relDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 回滚cdb0
    ret = TPC_RollbackCDB(cdbID[0]);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验cdb0 写入表A的数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, g_dbId, relID, relDef.pstFldLst, i + 10000, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(0, recNum);
    }

    // 删表
    ret = TPC_DropTbl(g_dbId, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(g_dbId, relID2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&relDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&relDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 035 开启240个cdb，cdb1读表A和写表B，其余cdb都读表A，提交cdb1后再提交其余cdb，预期所有cdb提交成功
TEST_F(NormalTable, V1Com_024_Normal_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 建表
    DB_REL_DEF_STRU relDef = {0};
    uint16_t relID = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_001.json", &relID, &relDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表A
    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    DB_REL_DEF_STRU relDef2 = {0};
    uint16_t relID2 = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_002.json", &relID2, &relDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表B
    uint16_t recLen2 = GetRecLen(&relDef2);
    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(recLen2);
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    // RDB写表A
    int insertNum = 100;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 开启240个cdb事务
    uint32_t cdbID[MAX_CDB_NUM] = {0};
    for (int i = 0; i < MAX_CDB_NUM; i++) {
        ret = TPC_BeginCDB(g_dbId, &cdbID[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int j = 0; j < MAX_CDB_NUM; j++) {
        if (j == 0) {
            // cdb0 读表A
            for (int i = 0; i < insertNum; i++) {
                uint32_t recNum = 0;
                SelectAllType(cdbID[j], g_dbId, relID, relDef.pstFldLst, i, &recNum);
                V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
            }
            // cdb0 写表B 0-99
            for (int i = 0; i < insertNum; i++) {
                InsertAllType(recBuf2, relDef2.pstFldLst, i, recLen2);
                DB_DSBUF_STRU dsBuf = {.usRecLen = recLen2,
                    .usRecNum = 1,
                    .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
                ret = TPC_InsertRec(cdbID[j], g_dbId, relID2, &dsBuf);
                V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            }
        } else {  // 其余cdb读表A数据
            for (int i = 0; i < insertNum; i++) {
                uint32_t recNum = 0;
                SelectAllType(cdbID[j], g_dbId, relID, relDef.pstFldLst, i, &recNum);
                V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
            }
        }
    }

    // 提交所有cdb
    for (int i = 0; i < MAX_CDB_NUM; i++) {
        ret = TPC_CommitCDB(cdbID[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 校验cdb0写入表B的数据, 0-99
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, g_dbId, relID2, relDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 删表
    ret = TPC_DropTbl(g_dbId, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(g_dbId, relID2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&relDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&relDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 036 开启240个cdb，cdb1读表A和写表B，其余cdb都读表A，cdb1最后提交，预期所有cdb提交成功
TEST_F(NormalTable, V1Com_024_Normal_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 建表
    DB_REL_DEF_STRU relDef = {0};
    uint16_t relID = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_001.json", &relID, &relDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表A
    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    DB_REL_DEF_STRU relDef2 = {0};
    uint16_t relID2 = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_002.json", &relID2, &relDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表B
    uint16_t recLen2 = GetRecLen(&relDef2);
    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(recLen2);
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    // RDB写表A
    int insertNum = 100;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 开启240个cdb事务
    uint32_t cdbID[MAX_CDB_NUM] = {0};
    for (int i = 0; i < MAX_CDB_NUM; i++) {
        ret = TPC_BeginCDB(g_dbId, &cdbID[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int j = 0; j < MAX_CDB_NUM; j++) {
        if (j == 0) {
            // cdb0 读表A
            for (int i = 0; i < insertNum; i++) {
                uint32_t recNum = 0;
                SelectAllType(cdbID[j], g_dbId, relID, relDef.pstFldLst, i, &recNum);
                V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
            }
            // cdb0 写表B 0-99
            for (int i = 0; i < insertNum; i++) {
                InsertAllType(recBuf2, relDef2.pstFldLst, i, recLen2);
                DB_DSBUF_STRU dsBuf = {.usRecLen = recLen2,
                    .usRecNum = 1,
                    .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
                ret = TPC_InsertRec(cdbID[j], g_dbId, relID2, &dsBuf);
                V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            }
        } else {  // 其余cdb读表A数据
            for (int i = 0; i < insertNum; i++) {
                uint32_t recNum = 0;
                SelectAllType(cdbID[j], g_dbId, relID, relDef.pstFldLst, i, &recNum);
                V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
            }
        }
    }

    // 除cdb0外，提交其余cdb
    for (int i = 1; i < MAX_CDB_NUM; i++) {
        ret = TPC_CommitCDB(cdbID[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 提交cdb0
    ret = TPC_CommitCDB(cdbID[0]);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验cdb0写入表B的数据, 0-99
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, g_dbId, relID2, relDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 删表
    ret = TPC_DropTbl(g_dbId, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(g_dbId, relID2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&relDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&relDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 037 开启240个cdb，cdb1读表A和写表B，其余cdb都写表A，提交cdb1后再提交其余cdb，预期只有cdb1提交成功，其余失败
TEST_F(NormalTable, V1Com_024_Normal_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 建表
    DB_REL_DEF_STRU relDef = {0};
    uint16_t relID = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_001.json", &relID, &relDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表A
    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    DB_REL_DEF_STRU relDef2 = {0};
    uint16_t relID2 = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_002.json", &relID2, &relDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表B
    uint16_t recLen2 = GetRecLen(&relDef2);
    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(recLen2);
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    // RDB写表A
    int insertNum = 100;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 开启240个cdb事务
    uint32_t cdbID[MAX_CDB_NUM] = {0};
    for (int i = 0; i < MAX_CDB_NUM; i++) {
        ret = TPC_BeginCDB(g_dbId, &cdbID[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    bool flag = 0;
    for (int j = 0; j < MAX_CDB_NUM; j++) {
        if (j == 0) {
            // cdb0 读表A
            for (int i = 0; i < insertNum; i++) {
                uint32_t recNum = 0;
                SelectAllType(cdbID[j], g_dbId, relID, relDef.pstFldLst, i, &recNum);
                V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
            }
            // cdb0 写表B 0-99
            for (int i = 0; i < insertNum; i++) {
                InsertAllType(recBuf2, relDef2.pstFldLst, i, recLen2);
                DB_DSBUF_STRU dsBuf = {.usRecLen = recLen2,
                    .usRecNum = 1,
                    .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
                ret = TPC_InsertRec(cdbID[j], g_dbId, relID2, &dsBuf);
                V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            }
        } else {  // 其余cdb写表A，100
            for (int i = insertNum; i < insertNum + 1; i++) {
                InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
                DB_DSBUF_STRU dsBuf = {.usRecLen = recLen,
                    .usRecNum = 1,
                    .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
                ret = TPC_InsertRec(cdbID[j], g_dbId, relID, &dsBuf);
                if (ret != DB_SUCCESS_V1) {
                    flag = 1;
                }
                V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            }
        }

        if (flag == 1) {
            VOS_UINT8 *pucResult = NULL;
            ret = TPC_Sysview(DB_TPC_SYSVIEW_GET_DYN_MEMCTX, NULL, &pucResult);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            AW_FUN_Log(LOG_INFO, "pucResult:%s\n", pucResult);
            TPC_FreeSysviewResult(&pucResult);
            AW_FUN_Log(LOG_INFO, "j=%d\n", j);
            break;
        }
    }

    // 除cdb0外，提交其余cdb
    for (int j = 0; j < MAX_CDB_NUM; j++) {
        ret = TPC_CommitCDB(cdbID[j]);
        if (j == 0) {
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        } else {
            V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_TPC_RDB_MODIFIED, ret);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1,
                TestTPC_SysviewGetLastError(
                    "Violated the constraint restrictions.", false));
            // 校验数据
            for (int i = insertNum; i < insertNum + 1; i++) {
                uint32_t recNum = 0;
                SelectAllType(cdbID[j], g_dbId, relID, relDef.pstFldLst, i, &recNum);
                V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
            }
        }
    }

    // 回滚除cdb0外的cdb
    for (int i = 1; i < MAX_CDB_NUM; i++) {
        ret = TPC_RollbackCDB(cdbID[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 校验cdb0写入表B的数据, 0-99
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, g_dbId, relID2, relDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 删表
    ret = TPC_DropTbl(g_dbId, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(g_dbId, relID2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&relDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&relDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 038 事务1写表A，事务2写表A、表B，事务3写表B，先提交事务1，事务2（冲突），再强制提交事务2，预期事务3提交失败
TEST_F(NormalTable, V1Com_024_Normal_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 建表
    DB_REL_DEF_STRU relDef = {0};
    uint16_t relID = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_001.json", &relID, &relDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表A
    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    DB_REL_DEF_STRU relDef2 = {0};
    uint16_t relID2 = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_002.json", &relID2, &relDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表B
    uint16_t recLen2 = GetRecLen(&relDef2);
    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(recLen2);
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    int insertNum = 10;
    // 开启3个cdb事务
    uint32_t cdbID[3] = {0};
    for (int i = 0; i < 3; i++) {
        ret = TPC_BeginCDB(g_dbId, &cdbID[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int j = 0; j < 3; j++) {
        if (j == 0) {
            // cdb0 写表A 0-9
            for (int i = 0; i < insertNum; i++) {
                InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
                DB_DSBUF_STRU dsBuf = {.usRecLen = recLen,
                    .usRecNum = 1,
                    .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
                ret = TPC_InsertRec(cdbID[j], g_dbId, relID, &dsBuf);
                V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            }
        } else if (j == 1) {
            // cdb1写表A，10-19
            for (int i = insertNum; i < 2 * insertNum; i++) {
                InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
                DB_DSBUF_STRU dsBuf = {.usRecLen = recLen,
                    .usRecNum = 1,
                    .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
                ret = TPC_InsertRec(cdbID[j], g_dbId, relID, &dsBuf);
                V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            }
            // 写表B 0-9
            for (int i = 0; i < insertNum; i++) {
                InsertAllType(recBuf2, relDef2.pstFldLst, i, recLen2);
                DB_DSBUF_STRU dsBuf = {.usRecLen = recLen2,
                    .usRecNum = 1,
                    .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
                ret = TPC_InsertRec(cdbID[j], g_dbId, relID2, &dsBuf);
                V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            }
        } else {
            // cdb2 写表B 10-19
            for (int i = insertNum; i < 2 * insertNum; i++) {
                InsertAllType(recBuf2, relDef2.pstFldLst, i, recLen2);
                DB_DSBUF_STRU dsBuf = {.usRecLen = recLen2,
                    .usRecNum = 1,
                    .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
                ret = TPC_InsertRec(cdbID[j], g_dbId, relID2, &dsBuf);
                V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            }
        }
    }

    // 先提交cdb0，预期成功
    ret = TPC_CommitCDB(cdbID[0]);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验cdb0写入表A的数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, g_dbId, relID, relDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 提交cdb1，预期失败
    ret = TPC_CommitCDB(cdbID[1]);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_TPC_RDB_MODIFIED, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1,
        TestTPC_SysviewGetLastError(
            "Violated the constraint restrictions. ", false));

    // RDB检验cdb1 写入表A和表B的数据，预期失败
    for (int i = insertNum; i < insertNum + 1; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, g_dbId, relID, relDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(0, recNum);
    }
    for (int i = 0; i < 1; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, g_dbId, relID2, relDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(0, recNum);  // 没有数据
    }

    // 强制提交cdb1
    ret = TPC_ForceCommitCDB(cdbID[1]);  // A 0-19  B 0-9
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < 2 * insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, g_dbId, relID, relDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, g_dbId, relID2, relDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 提交cdb2，预期失败
    ret = TPC_CommitCDB(cdbID[2]);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_TPC_RDB_MODIFIED, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1,
        TestTPC_SysviewGetLastError(
            "Violated the constraint restrictions.", false));

    // 校验cdb2写入的数据
    for (int i = insertNum; i < 2 * insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(cdbID[2], g_dbId, relID2, relDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }
    // 回滚cdb2
    ret = TPC_RollbackCDB(cdbID[2]);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = insertNum; i < 2 * insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, g_dbId, relID2, relDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(0, recNum);
    }

    // 删表
    ret = TPC_DropTbl(g_dbId, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(g_dbId, relID2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&relDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&relDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 039 空表A表B，cdb1空删表A数据，cdb2读表A写表B，提交cdb1、cdb2，预期cdb1提交成功，cdb2提交失败  || DTS2025042108150
TEST_F(NormalTable, V1Com_024_Normal_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 建表
    DB_REL_DEF_STRU relDef = {0};
    uint16_t relID = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_001.json", &relID, &relDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表A
    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    DB_REL_DEF_STRU relDef2 = {0};
    uint16_t relID2 = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_002.json", &relID2, &relDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表B
    uint16_t recLen2 = GetRecLen(&relDef2);
    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(recLen2);
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    int insertNum = 100;
    // 开启2个cdb事务
    uint32_t cdbID[2] = {0};
    for (int i = 0; i < 2; i++) {
        ret = TPC_BeginCDB(g_dbId, &cdbID[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // cdb1空删表A数据
    uint32_t recNum = 0;
    Delete(cdbID[0], g_dbId, relID, 0, &recNum);
    V1_AW_MACRO_EXPECT_EQ_INT(0, recNum);

    // cdb2 读空表A，写表B
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(cdbID[1], g_dbId, relID, relDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(0, recNum);
    }
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, relDef2.pstFldLst, i, recLen2);
        DB_DSBUF_STRU dsBuf = {.usRecLen = recLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
        ret = TPC_InsertRec(cdbID[1], g_dbId, relID2, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(cdbID[1], g_dbId, relID2, relDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 提交cdb0
    ret = TPC_CommitCDB(cdbID[0]);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 提交cdb1
    ret = TPC_CommitCDB(cdbID[1]);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_TPC_RDB_MODIFIED, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1,
        TestTPC_SysviewGetLastError(
            "Violated the constraint restrictions.", false));

    ret = TPC_RollbackCDB(cdbID[1]);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = TPC_DropTbl(g_dbId, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(g_dbId, relID2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&relDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&relDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 040 开启cdb操作自己的表，部分表操作失败，提交事务，预期事务提交成功  || DTS2025032535288
TEST_F(NormalTable, V1Com_024_Normal_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 建表
    DB_REL_DEF_STRU relDef = {0};
    uint16_t relID = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_001.json", &relID, &relDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表A
    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    DB_REL_DEF_STRU relDef2 = {0};
    uint16_t relID2 = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_002.json", &relID2, &relDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表B
    uint16_t recLen2 = GetRecLen(&relDef2);
    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(recLen2);
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    // RDB写表A
    int insertNum = 100;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, relID, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, g_dbId, relID, relDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 开启cdb事务
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_dbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // cdb写表A冲突
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, relDef.pstFldLst, i, recLen);
        DB_DSBUF_STRU dsBuf = {
            .usRecLen = recLen, .usRecNum = 1, .StdBuf = {.ulBufLen = recLen, .ulActLen = recLen, .pucData = recBuf}};
        ret = TPC_InsertRec(cdbID, g_dbId, relID, &dsBuf);
        if (ret != DB_SUCCESS_V1) {
            AW_FUN_Log(LOG_ERROR, "TPC_InsertRec failed with ret = %d in cdb,i = %d", ret, i);
            V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_KEYDUPLICATE, ret);
            break;
        }
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // cdb写表B成功
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, relDef2.pstFldLst, i, recLen2);
        DB_DSBUF_STRU dsBuf = {.usRecLen = recLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
        ret = TPC_InsertRec(cdbID, g_dbId, relID2, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 提交cdb
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验表A数据, 0-99
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, g_dbId, relID, relDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 校验表B的数据, 0-99
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, g_dbId, relID2, relDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 删表
    ret = TPC_DropTbl(g_dbId, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(g_dbId, relID2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&relDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&relDef2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 041 DB1和DB2都创建表1，开启两个cdb都操作DB2的表1，先后提交cdb1和cdb2，预期cdb1成功，cdb2提交失败后强制提交，DB1无数据，DB2有数据  || DTS2025031423407
TEST_F(NormalTable, V1Com_024_Normal_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    ret = TPC_CreateDB((VOS_UINT8 *)g_dbName2, NULL, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    DB_REL_DEF_STRU relDef = {0};
    uint16_t relID = 0;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/table_test_normal_001.json", &relID, &relDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表A
    uint16_t recLen = GetRecLen(&relDef);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(recLen);
    (void)memset_s(recBuf, recLen, 0x00, recLen);

    DB_REL_DEF_STRU relDef2 = {0};
    uint16_t relID2 = 0;
    ret = TestTPC_CreateTbl(g_dbId2, "schemaFile/table_test_normal_001.json", &relID2, &relDef2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表A
    uint16_t recLen2 = GetRecLen(&relDef2);
    uint8_t *recBuf2 = (uint8_t *)TEST_V1_MALLOC(recLen2);
    (void)memset_s(recBuf2, recLen2, 0x00, recLen2);

    // 开启2个cdb事务
    uint32_t cdbID[2] = {0};
    for (int i = 0; i < 2; i++) {
        ret = TPC_BeginCDB(g_dbId2, &cdbID[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    int insertNum = 100;
    // cdb1写DB2表1，0-99
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf2, relDef2.pstFldLst, i, recLen2);
        DB_DSBUF_STRU dsBuf = {.usRecLen = recLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
        ret = TPC_InsertRec(cdbID[0], g_dbId2, relID2, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // cdb2写DB2表1，100-199
    for (int i = insertNum; i < 2 * insertNum; i++) {
        InsertAllType(recBuf2, relDef2.pstFldLst, i, recLen2);
        DB_DSBUF_STRU dsBuf = {.usRecLen = recLen2,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = recLen2, .ulActLen = recLen2, .pucData = recBuf2}};
        ret = TPC_InsertRec(cdbID[1], g_dbId2, relID2, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 提交cdb1
    ret = TPC_CommitCDB(cdbID[0]);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, g_dbId2, relID2, relDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 提交cdb2
    ret = TPC_CommitCDB(cdbID[1]);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_TPC_RDB_MODIFIED, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1,
        TestTPC_SysviewGetLastError(
            "Violated the constraint restrictions. ", false));

    // 强制提交cdb2
    ret = TPC_ForceCommitCDB(cdbID[1]);  // A 100-199
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    
    // 校验DB1表1数据, 预期没有数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, g_dbId, relID, relDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(0, recNum);
    }

    // 校验DB2表1的数据, 0-199
    for (int i = 0; i < 2 * insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(TPC_GLOBAL_CDB, g_dbId2, relID2, relDef2.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 删表
    ret = TPC_DropTbl(g_dbId, relID, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(g_dbId2, relID2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TEST_V1_FREE(recBuf);
    FreeRelDef(&relDef);
    TEST_V1_FREE(recBuf2);
    FreeRelDef(&relDef2);
    ret = TPC_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)g_dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
