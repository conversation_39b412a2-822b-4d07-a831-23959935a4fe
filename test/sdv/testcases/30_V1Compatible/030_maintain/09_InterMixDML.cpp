/*
1 DB接口建DB
001 DB接口导出，TPC接口DISCARD导入后开启CDB且TPC接口进行DML操作，预期成功
002 DB接口导出删除DB，TPC接口REPLACE导入后DB接口DML操作，预期导入成功、DML成功
003 DB接口导出删除DB，TPC接口REPLACE导入后TPC接口DML操作，预期导入成功、DML成功
004 DB接口建DB、表和写数据，TPC接口导出，预期导出失败
005 DB接口导出，TPC接口DISCARD导入后DB接口DML操作，预期导入成功、DML成功
006 DB接口导出，TPC接口DISCARD导入后TPC接口DML操作，预期导入成功、DML失败
007 DB接口导出不删除DB，TPC接口REPLACE导入，预期导入失败
2 TPC接口建DB
008 TPC接口导出，DB接口导入并开启CDB，预期开启CDB失败
009 TPC接口导出，DB接口REPLACE导入后DB接口DML操作，预期导入成功、DML成功
010 TPC接口导出，DB接口REPLACE导入后TPC接口DML操作，预期导入成功、DML失败
011 TPC接口建DB、表和写数据，DB接口导出，TPC接口导入，预期导出导入成功
012 TPC接口导出，DB接口DISCARD导入后DB接口DML操作，预期导入成功、DML成功
013 TPC接口导出，DB接口DISCARD导入后TPC接口DML操作，预期导入成功、DML失败

3 DML接口混用
014 DB接口建表写数据，TPC接口进行增删改查等操作，预期失败
015 TPC接口建表写数据，DB接口进行增删改查等操作，预期成功
016 TPC接口建表不写数据，DB接口进行增删改查等操作，预期成功

4 beginSelect相关
017 DB接口建DB、表和写数据，TPC接口BeginSelect、FetchSelectRec和EndSelect，预期TPC操作失败
018 DB接口建DB、表和写数据，DB接口BeginSelect、TPC接口FetchSelectRec、DB接口EndSelect，预期TPC操作失败
019 TPC接口建DB、表和写数据，TPC接口BeginSelect、DB接口FetchSelectRec、TPC接口EndSelect，预期DB操作成功
020 DB接口建DB、表和写数据，DB接口BeginSelect、DB接口FetchSelectRec、TPC接口EndSelect，预期TPC操作成功
021 TPC接口建DB、表和写数据，TPC接口BeginSelect、TPC接口FetchSelectRec、DB接口EndSelect，预期DB操作成功
022 DB接口建DB、表和写数据，DB接口BeginSelect多次、TPC接口CloseAllHandles，预期TPC操作失败
023 TPC接口建DB、表和写数据，TPC接口BeginSelect多次、DB接口CloseAllHandles，预期TPC操作成功

5 plan和SelectAllRec2
024 TPC接口建DB、表和写数据，DB接口PrepareQueryPlan、TPC接口SelectAllRec2，预期DB接口操作成功
025 DB接口建DB、表和写数据，TPC接口PrepareQueryPlan、DB接口SelectAllRec2，预期TPC接口操作失败
026 TPC接口建DB、表和写数据，TPC接口PrepareQueryPlan、DB接口SelectAllRec2，预期DB接口操作成功
027 DB接口建DB、表和写数据，DB接口PrepareQueryPlan、TPC接口SelectAllRec2，预期TPC接口操作失败

6 事务混用
028 TPC接口建DB和表，开启cdb后DB接口写入数据，TPC接口写入相同的数据后提交，预期cdb提交失败
029 二阶段表中有数据时先使用一阶段开启handle，再开启CDB进行DML操作后查询
030 二阶段表中没有数据时先使用一阶段开启QueryPlan，再开启CDB进行DML操作后查询
031 二阶段表中有数据时先使用一阶段开启QueryPlan，再开启CDB进行DML操作后查询
032 二阶段表使用一阶段接口DML操作后，导入导出并校验导入后数据
*/

#include "maintain.h"

class InterMixDML : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void InterMixDML::SetUpTestCase()
{}

void InterMixDML::TearDownTestCase()
{}

void InterMixDML::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    // 初始化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_Init());
}

void InterMixDML::TearDown()
{
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_UnInit());
    AW_CHECK_LOG_END();
}

// 001 DB接口导出，TPC接口DISCARD导入后开启CDB且TPC接口进行DML操作，预期成功
TEST_F(InterMixDML, V1Com_030_InterMixDML_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    ret = DB_CreateDB((VOS_UINT8 *)g_dbName, NULL, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_dbName, &g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 校验数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DB接口导出
    char filePath[64] = "./filePath/export.txt";
    ret = DB_BkpPhy(g_dbId, (uint8_t *)filePath);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // TPC接口导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, NULL, DB_RESTORETYPE_DISCARD, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        TpcSelectAllType(TPC_GLOBAL_CDB, g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum, true);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 开启CDB
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_dbId2, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = insertNum; i < 2 * insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(cdbID, g_dbId2, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < 2 * insertNum; i++) {
        uint32_t recNum = 0;
        TpcSelectAllType(cdbID, g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum, true);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CloseDB(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002 DB接口导出删除DB，TPC接口REPLACE导入后DB接口DML操作，预期导入成功、DML成功
TEST_F(InterMixDML, V1Com_030_InterMixDML_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    ret = DB_CreateDB((VOS_UINT8 *)g_dbName, NULL, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_dbName, &g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 校验数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DB接口导出
    char filePath[64] = "./filePath/export.txt";
    ret = DB_BkpPhy(g_dbId, (uint8_t *)filePath);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删除源DB
    ret = DB_CloseDB(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // TPC接口导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_dbName, &g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DB 接口继续写入查询
    for (int i = insertNum; i < 2 * insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 校验数据
    for (int i = 0; i < 2 * insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    ret = DB_CloseDB(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003 DB接口导出删除DB，TPC接口REPLACE导入后TPC接口DML操作，预期导入成功、DML成功
TEST_F(InterMixDML, V1Com_030_InterMixDML_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    ret = DB_CreateDB((VOS_UINT8 *)g_dbName, NULL, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_dbName, &g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 校验数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DB接口导出
    char filePath[64] = "./filePath/export.txt";
    ret = DB_BkpPhy(g_dbId, (uint8_t *)filePath);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CloseDB(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // TPC接口导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_dbName, &g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        TpcSelectAllType(TPC_GLOBAL_CDB, g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum, true);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    for (int i = insertNum; i < 2 * insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, usRelId, &dsBuf);
        if (ret != DB_SUCCESS_V1) {
            AW_FUN_Log(LOG_ERROR, "TPC_InsertRec failed with %d, i = %d", ret, i);
            break;
        }
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < 2 * insertNum; i++) {
        uint32_t recNum = 0;
        TpcSelectAllType(TPC_GLOBAL_CDB, g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum, true);
        if (ret != DB_SUCCESS_V1) {
            AW_FUN_Log(LOG_ERROR, "TpcSelectAllType failed with recNum = %d, i = %d", recNum, i);
            break;
        }
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    ret = DB_CloseDB(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004 DB接口建DB、表和写数据，TPC接口导出，预期导出失败
TEST_F(InterMixDML, V1Com_030_InterMixDML_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    ret = DB_CreateDB((VOS_UINT8 *)g_dbName, NULL, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_dbName, &g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 校验数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // TPC接口导出
    char filePath[64] = "./filePath/export.txt";
    ret = TPC_BkpPhy(g_dbId, (uint8_t *)filePath);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);

    ret = DB_CloseDB(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005 DB接口导出，TPC接口DISCARD导入后DB接口DML操作，预期导入成功、DML成功
TEST_F(InterMixDML, V1Com_030_InterMixDML_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    ret = DB_CreateDB((VOS_UINT8 *)g_dbName, NULL, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_dbName, &g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 校验数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DB接口导出
    char filePath[64] = "./filePath/export.txt";
    ret = DB_BkpPhy(g_dbId, (uint8_t *)filePath);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // TPC接口导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, NULL, DB_RESTORETYPE_DISCARD, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        if (recNum != 1) {
            AW_FUN_Log(LOG_ERROR, "SelectAllType failed with recNum = %d, i = %d", recNum, i);
            break;
        }
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    for (int i = insertNum; i < 2 * insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId2, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 校验数据
    for (int i = 0; i < 2 * insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    ret = TPC_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CloseDB(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006 DB接口导出，TPC接口DISCARD导入后TPC接口DML操作，预期导入成功、DML失败
TEST_F(InterMixDML, V1Com_030_InterMixDML_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    ret = DB_CreateDB((VOS_UINT8 *)g_dbName, NULL, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_dbName, &g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 校验数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DB接口导出
    char filePath[64] = "./filePath/export.txt";
    ret = DB_BkpPhy(g_dbId, (uint8_t *)filePath);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // TPC接口导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, NULL, DB_RESTORETYPE_DISCARD, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        TpcSelectAllType(TPC_GLOBAL_CDB, g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum, true);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    for (int i = insertNum; i < 2 * insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId2, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < 2 * insertNum; i++) {
        uint32_t recNum = 0;
        TpcSelectAllType(TPC_GLOBAL_CDB, g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum, true);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    ret = TPC_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CloseDB(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007 DB接口导出不删除DB，TPC接口REPLACE导入，预期导入失败
TEST_F(InterMixDML, V1Com_030_InterMixDML_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    ret = DB_CreateDB((VOS_UINT8 *)g_dbName, NULL, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_dbName, &g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 校验数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DB接口导出
    char filePath[64] = "./filePath/export.txt";
    ret = DB_BkpPhy(g_dbId, (uint8_t *)filePath);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // TPC接口导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);

    ret = DB_CloseDB(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008 TPC接口导出，DB接口导入并开启CDB，预期开启CDB失败
TEST_F(InterMixDML, V1Com_030_InterMixDML_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    ret = TPC_CreateDB((VOS_UINT8 *)g_dbName, NULL, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_dbName, &g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        TpcSelectAllType(TPC_GLOBAL_CDB, g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum, true);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // TPC接口导出
    char filePath[64] = "./filePath/export.txt";
    ret = TPC_BkpPhy(g_dbId, (uint8_t *)filePath);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // DB接口DISCARD导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, NULL, DB_RESTORETYPE_DISCARD, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 开启CDB
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_dbId2, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);

    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CloseDB(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 009 TPC接口导出，DB接口REPLACE导入后DB接口DML操作，预期导入成功、DML成功
TEST_F(InterMixDML, V1Com_030_InterMixDML_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    ret = TPC_CreateDB((VOS_UINT8 *)g_dbName, NULL, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_dbName, &g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        TpcSelectAllType(TPC_GLOBAL_CDB, g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum, true);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // TPC接口导出
    char filePath[64] = "./filePath/export.txt";
    ret = TPC_BkpPhy(g_dbId, (uint8_t *)filePath);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // DB接口REPLACE导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DB 接口操作
    for (int i = insertNum; i < 2 * insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 校验数据
    for (int i = 0; i < 2 * insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    ret = DB_CloseDB(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010 TPC接口导出，DB接口REPLACE导入后TPC接口DML操作，预期导入成功、DML失败
TEST_F(InterMixDML, V1Com_030_InterMixDML_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    ret = TPC_CreateDB((VOS_UINT8 *)g_dbName, NULL, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_dbName, &g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        ret = TpcSelectAllType(TPC_GLOBAL_CDB, g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum, true);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // TPC接口导出
    char filePath[64] = "./filePath/export.txt";
    ret = TPC_BkpPhy(g_dbId, (uint8_t *)filePath);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // DB接口REPLACE导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        ret = TpcSelectAllType(TPC_GLOBAL_CDB, g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum, true);
        if (ret != DB_SUCCESS_V1) {
            V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);
            AW_FUN_Log(LOG_ERROR, "TpcSelectAllType failed with ret = %d, i = %d", ret, i);
            break;
        }
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // TPC 接口操作
    for (int i = insertNum; i < 2 * insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, usRelId, &dsBuf);
        if (ret != DB_SUCCESS_V1) {
            V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);
            AW_FUN_Log(LOG_ERROR, "TPC_InsertRec failed with ret = %d, i = %d", ret, i);
            break;
        }
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    ret = DB_CloseDB(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 011 TPC接口建DB、表和写数据，DB接口导出，TPC接口导入，预期导出导入成功
TEST_F(InterMixDML, V1Com_030_InterMixDML_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    ret = TPC_CreateDB((VOS_UINT8 *)g_dbName, NULL, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_dbName, &g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        TpcSelectAllType(TPC_GLOBAL_CDB, g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum, true);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DB接口导出
    char filePath[64] = "./filePath/export.txt";
    ret = DB_BkpPhy(g_dbId, (uint8_t *)filePath);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // DB接口REPLACE导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        TpcSelectAllType(TPC_GLOBAL_CDB, g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum, true);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // TPC 接口操作
    for (int i = insertNum; i < 2 * insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 校验数据
    for (int i = 0; i < 2 * insertNum; i++) {
        uint32_t recNum = 0;
        TpcSelectAllType(TPC_GLOBAL_CDB, g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum, true);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    ret = DB_CloseDB(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 012 TPC接口导出，DB接口DISCARD导入后DB接口DML操作，预期导入成功、DML成功
TEST_F(InterMixDML, V1Com_030_InterMixDML_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    ret = TPC_CreateDB((VOS_UINT8 *)g_dbName, NULL, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_dbName, &g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        TpcSelectAllType(TPC_GLOBAL_CDB, g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum, true);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // TPC接口导出
    char filePath[64] = "./filePath/export.txt";
    ret = TPC_BkpPhy(g_dbId, (uint8_t *)filePath);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // DB接口DISCARD导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, NULL, DB_RESTORETYPE_DISCARD, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DB 接口操作
    for (int i = insertNum; i < 2 * insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId2, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 校验数据
    for (int i = 0; i < 2 * insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CloseDB(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 013 TPC接口导出，DB接口DISCARD导入后TPC接口DML操作，预期导入成功、DML失败
TEST_F(InterMixDML, V1Com_030_InterMixDML_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    ret = TPC_CreateDB((VOS_UINT8 *)g_dbName, NULL, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_dbName, &g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        TpcSelectAllType(TPC_GLOBAL_CDB, g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum, true);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DB接口导出
    char filePath[64] = "./filePath/export.txt";
    ret = TPC_BkpPhy(g_dbId, (uint8_t *)filePath);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // DB接口DISCARD导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName2, NULL, DB_RESTORETYPE_DISCARD, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        ret = TpcSelectAllType(TPC_GLOBAL_CDB, g_dbId2, usRelId, stRelDef.pstFldLst, i, &recNum, true);
        if (ret != DB_SUCCESS_V1) {
            V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);
            AW_FUN_Log(LOG_ERROR, "TpcSelectAllType failed with ret = %d, i = %d", ret, i);
            break;
        }
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // TPC 接口操作
    for (int i = insertNum; i < 2 * insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId2, usRelId, &dsBuf);
        if (ret != DB_SUCCESS_V1) {
            V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);
            AW_FUN_Log(LOG_ERROR, "TPC_InsertRec failed with ret = %d, i = %d", ret, i);
            break;
        }
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    ret = DB_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CloseDB(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 014 DB接口建表写数据，TPC接口进行增删改查等操作，预期失败
TEST_F(InterMixDML, V1Com_030_InterMixDML_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    ret = DB_CreateDB((VOS_UINT8 *)g_dbName, NULL, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_dbName, &g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 校验数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 1 TPC_SelectAllRecEx
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        ret = TpcSelectAllType(TPC_GLOBAL_CDB, g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum, true);
        if (ret != DB_SUCCESS_V1) {
            V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);
            AW_FUN_Log(LOG_ERROR, "TpcSelectAllType failed with ret = %d, i = %d", ret, i);
            break;
        }
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 2 TPC_InsertRec
    for (int i = insertNum; i < 2 * insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, usRelId, &dsBuf);
        if (ret != DB_SUCCESS_V1) {
            V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);
            AW_FUN_Log(LOG_ERROR, "TpcSelectAllType failed with ret = %d, i = %d", ret, i);
            break;
        }
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 设置查询条件
    DB_COND_STRU pstCond = {.usCondNum = 1};
    uint32_t conVal = insertNum;
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_LESS, (VOS_UINT8 *)&conVal, sizeof(conVal));

    // 3 TPC_CountMatchingRecs
    VOS_UINT32 pulRecNum = 0;
    ret = TPC_CountMatchingRecs(TPC_GLOBAL_CDB, g_dbId, usRelId, &pstCond, &pulRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0, pulRecNum);

    // 4 TPC_DeleteRec
    uint32_t cnt = 0;
    ret = TpcDelete(TPC_GLOBAL_CDB, g_dbId, usRelId, insertNum, &cnt);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0, cnt);

    // 5 TPC_RecordExist
    conVal = 100;
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_EQUAL, (VOS_UINT8 *)&conVal, sizeof(conVal));
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_dbId, usRelId, &pstCond);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);

    // 6 TPC_SelectAllRec
    // 查询数据
    pstCond.usCondNum = 0;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_DSBUF_STRU pstDsBufGet;
    pstDsBufGet.usRecLen = tblRecLen;
    pstDsBufGet.usRecNum = insertNum;
    pstDsBufGet.StdBuf.ulBufLen = pstDsBufGet.usRecLen * pstDsBufGet.usRecNum;
    VOS_UINT8 *pucDataGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufGet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataGet);
    (void)memset_s(pucDataGet, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
    pstDsBufGet.StdBuf.pucData = pucDataGet;
    ret = TPC_SelectAllRec(TPC_GLOBAL_CDB, g_dbId, usRelId, &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(insertNum, pstDsBufGet.usRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(0, pstDsBufGet.StdBuf.ulActLen);
    TEST_V1_FREE(pucDataGet);

    // 7 TPC_SelectAllRecByOrder
    // 查询数据
    unsigned char sortFields[1] = {0};
    DB_SORT_STRU pstSort;
    pstSort.ucSortNum = 1;
    pstSort.enSortType = 1;
    pstSort.pSortFields = sortFields;

    pstDsBufGet.usRecLen = tblRecLen;
    pstDsBufGet.usRecNum = insertNum;
    pstDsBufGet.StdBuf.ulBufLen = pstDsBufGet.usRecLen * pstDsBufGet.usRecNum;
    pucDataGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufGet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataGet);
    (void)memset_s(pucDataGet, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
    pstDsBufGet.StdBuf.pucData = pucDataGet;
    ret = TPC_SelectAllRecByOrder(TPC_GLOBAL_CDB, g_dbId, usRelId, &pstSort, &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(insertNum, pstDsBufGet.usRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(0, pstDsBufGet.StdBuf.ulActLen);
    TEST_V1_FREE(pucDataGet);

    // 8 TPC_SelectAllRecByOrderEx
    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = tblRecLen;
    pstBufDataGet.ulRecNum = insertNum;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum + 1);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret = TPC_SelectAllRecByOrderEx(TPC_GLOBAL_CDB, g_dbId, usRelId, &pstSort, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(insertNum, pstBufDataGet.ulRecNum);
    TEST_V1_FREE(pBufGet);

    // 9 TPC_SelectFirstRec
    pstDsBufGet.usRecLen = tblRecLen;
    pstDsBufGet.usRecNum = insertNum;
    pstDsBufGet.StdBuf.ulBufLen = pstDsBufGet.usRecLen;
    pucDataGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufGet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataGet);
    (void)memset_s(pucDataGet, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
    pstDsBufGet.StdBuf.pucData = pucDataGet;
    ret = TPC_SelectFirstRec(TPC_GLOBAL_CDB, g_dbId, usRelId, &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(insertNum, pstDsBufGet.usRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(0, pstDsBufGet.StdBuf.ulActLen);
    TEST_V1_FREE(pucDataGet);

    // 10 TPC_SimpleSelectAllRec
    // 设置字段排序，不排序，接口不支持
    pstSort = {.ucSortNum = 0, .aucReserve = {0}, .enSortType = DB_SORTTYPE_ASCEND};

    // 获取查询计划
    pstCond = {.usCondNum = 1};
    conVal = 0;
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_EQUAL, (VOS_UINT8 *)&conVal, sizeof(conVal));

    DB_QUERY_PLAN_STRU pstQueryPlan;
    ret = TPC_PrepareQueryPlan(TPC_GLOBAL_CDB, g_dbId, usRelId, &pstCond, &pstQueryPlan);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);

    // 申请接口存放数据的buff
    DB_BUF_STRU pstBuf1;
    ret = TestMallocBuf(&pstBuf1, tblRecLen, insertNum, insertNum * tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    PvArgT pvArg = {.isAlreadyMalloc = true, .callCnt = 0, .bufAddr = &pstBuf1};

    // 申请回调使用的内存
    DB_BUF_STRU pstBuf2;
    ret = TestMallocBuf(&pstBuf2, tblRecLen, insertNum, insertNum * tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置参数
    DB_SELECT_STRU pstSelect = {.ulCdbId = TPC_GLOBAL_CDB,
        .ulDbId = g_dbId,
        .usRelNo = usRelId,
        .pstCond = &pstCond,
        .pstFldFilter = &pstFldFilter,
        .pstSort = &pstSort,
        .pstQueryPlan = NULL,
        .pfGetBuf = PfGetBufCallBack,
        .pvArg = (VOS_VOID *)&pvArg,
        .pstDataBuf = &pstBuf2};

    // 正常查询，保证参数正常
    ret = TPC_SimpleSelectAllRec(&pstSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(insertNum, pstBuf2.ulRecNum);

    // 释放内存
    TestFreeBuf(&pstBuf1);
    TestFreeBuf(&pstBuf2);

    // 11 TPC_UpdateRec
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        ret = TpcUpdateAllType(TPC_GLOBAL_CDB, g_dbId, usRelId, recBuf, stRelDef.pstFldLst, i, &recNum);
        if (ret != DB_SUCCESS_V1) {
            V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);
            AW_FUN_Log(LOG_ERROR, "TpcUpdateAllType failed with ret = %d, i = %d", ret, i);
            break;
        }
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    ret = DB_CloseDB(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 015 TPC接口建表写数据，DB接口进行增删改查等操作，预期成功
TEST_F(InterMixDML, V1Com_030_InterMixDML_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    ret = TPC_CreateDB((VOS_UINT8 *)g_dbName, NULL, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_dbName, &g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 校验数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        ret = TpcSelectAllType(TPC_GLOBAL_CDB, g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum, true);
        if (ret != DB_SUCCESS_V1) {
            V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);
            AW_FUN_Log(LOG_ERROR, "TpcSelectAllType failed with ret = %d, i = %d", ret, i);
            break;
        }
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 1 DB_SelectAllRecEx
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 2 DB_InsertRec
    for (int i = insertNum; i < 2 * insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        if (ret != DB_SUCCESS_V1) {
            V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);
            AW_FUN_Log(LOG_ERROR, "DB_InsertRec failed with ret = %d, i = %d", ret, i);
            break;
        }
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = insertNum; i < 2 * insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 设置查询条件
    DB_COND_STRU pstCond = {.usCondNum = 1};
    uint32_t conVal = insertNum * 10;
    TestSetCondVal(&pstCond.aCond[0], 11, DB_OP_LESS, (VOS_UINT8 *)&conVal, sizeof(conVal));

    // 3 DB_CountMatchingRecs
    VOS_UINT32 pulRecNum = 0;
    ret = DB_CountMatchingRecs(g_dbId, usRelId, &pstCond, &pulRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(insertNum * 2, pulRecNum);

    // 4 DB_DeleteRec
    uint32_t cnt = 0;
    Delete(g_dbId, usRelId, insertNum, &cnt);
    V1_AW_MACRO_EXPECT_EQ_INT(insertNum, cnt);

    // 5 DB_RecordExist
    conVal = insertNum;
    TestSetCondVal(&pstCond.aCond[0], 11, DB_OP_EQUAL, (VOS_UINT8 *)&conVal, sizeof(conVal));
    ret = DB_RecordExist(g_dbId, usRelId, &pstCond);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 6 DB_SelectAllRec
    // 查询数据
    pstCond.usCondNum = 0;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_DSBUF_STRU pstDsBufGet;
    pstDsBufGet.usRecLen = tblRecLen;
    pstDsBufGet.usRecNum = insertNum;
    pstDsBufGet.StdBuf.ulBufLen = pstDsBufGet.usRecLen * pstDsBufGet.usRecNum;
    VOS_UINT8 *pucDataGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufGet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataGet);
    (void)memset_s(pucDataGet, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
    pstDsBufGet.StdBuf.pucData = pucDataGet;
    ret = DB_SelectAllRec(g_dbId, usRelId, &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(insertNum, pstDsBufGet.usRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(pstDsBufGet.StdBuf.ulBufLen, pstDsBufGet.StdBuf.ulActLen);
    TEST_V1_FREE(pucDataGet);

    // 7 DB_SelectAllRecByOrder
    // 查询数据
    unsigned char sortFields[1] = {0};
    DB_SORT_STRU pstSort;
    pstSort.ucSortNum = 1;
    pstSort.enSortType = 1;
    pstSort.pSortFields = sortFields;

    pstDsBufGet.usRecLen = tblRecLen;
    pstDsBufGet.usRecNum = insertNum;
    pstDsBufGet.StdBuf.ulBufLen = pstDsBufGet.usRecLen * pstDsBufGet.usRecNum;
    pucDataGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufGet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataGet);
    (void)memset_s(pucDataGet, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
    pstDsBufGet.StdBuf.pucData = pucDataGet;
    ret = DB_SelectAllRecByOrder(g_dbId, usRelId, &pstSort, &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(insertNum, pstDsBufGet.usRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(pstDsBufGet.StdBuf.ulBufLen, pstDsBufGet.StdBuf.ulActLen);
    TEST_V1_FREE(pucDataGet);

    // 8 DB_SelectAllRecByOrderEx
    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = tblRecLen;
    pstBufDataGet.ulRecNum = insertNum;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum + 1);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret = DB_SelectAllRecByOrderEx(g_dbId, usRelId, &pstSort, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(insertNum, pstBufDataGet.ulRecNum);
    TEST_V1_FREE(pBufGet);

    // 9 DB_SelectFirstRec
    pstDsBufGet.usRecLen = tblRecLen;
    pstDsBufGet.usRecNum = insertNum;
    pstDsBufGet.StdBuf.ulBufLen = pstDsBufGet.usRecLen;
    pucDataGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufGet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataGet);
    (void)memset_s(pucDataGet, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
    pstDsBufGet.StdBuf.pucData = pucDataGet;
    ret = DB_SelectFirstRec(g_dbId, usRelId, &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(1, pstDsBufGet.usRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(pstDsBufGet.StdBuf.ulBufLen, pstDsBufGet.StdBuf.ulActLen);
    TEST_V1_FREE(pucDataGet);

    // 10 DB_SimpleSelectAllRec
    // 设置字段排序，不排序，接口不支持
    pstSort = {.ucSortNum = 0, .aucReserve = {0}, .enSortType = DB_SORTTYPE_ASCEND};

    // 获取查询计划
    pstCond = {.usCondNum = 1};
    conVal = 0;
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_EQUAL, (VOS_UINT8 *)&conVal, sizeof(conVal));

    DB_QUERY_PLAN_STRU pstQueryPlan;
    ret = DB_PrepareQueryPlan(g_dbId, usRelId, &pstCond, &pstQueryPlan);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 申请接口存放数据的buff
    DB_BUF_STRU pstBuf1;
    ret = TestMallocBuf(&pstBuf1, tblRecLen, insertNum, insertNum * tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    PvArgT pvArg = {.isAlreadyMalloc = true, .callCnt = 0, .bufAddr = &pstBuf1};

    // 申请回调使用的内存
    DB_BUF_STRU pstBuf2;
    ret = TestMallocBuf(&pstBuf2, tblRecLen, insertNum, insertNum * tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置参数
    DB_SELECT_STRU pstSelect = {.ulCdbId = TPC_GLOBAL_CDB,
        .ulDbId = g_dbId,
        .usRelNo = usRelId,
        .pstCond = &pstCond,
        .pstFldFilter = &pstFldFilter,
        .pstSort = &pstSort,
        .pstQueryPlan = &pstQueryPlan,
        .pfGetBuf = PfGetBufCallBack,
        .pvArg = (VOS_VOID *)&pvArg,
        .pstDataBuf = &pstBuf2};

    // 正常查询，保证参数正常
    ret = DB_SimpleSelectAllRec(&pstSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(1, pstBuf2.ulRecNum);

    // 释放内存
    TestFreeBuf(&pstBuf1);
    TestFreeBuf(&pstBuf2);

    // 11 DB_UpdateRec
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        UpdateAllType(g_dbId, usRelId, recBuf, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    ret = DB_CloseDB(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 016 TPC接口建表不写数据，DB接口进行增删改查等操作，预期成功
TEST_F(InterMixDML, V1Com_030_InterMixDML_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    ret = TPC_CreateDB((VOS_UINT8 *)g_dbName, NULL, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_dbName, &g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    // 1 DB_SelectAllRecEx
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(0, recNum);
    }

    // 2 DB_InsertRec
    for (int i = 0; i < 2 * insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        if (ret != DB_SUCCESS_V1) {
            V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);
            AW_FUN_Log(LOG_ERROR, "DB_InsertRec failed with ret = %d, i = %d", ret, i);
            break;
        }
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < 2 * insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 设置查询条件
    DB_COND_STRU pstCond = {.usCondNum = 1};
    uint32_t conVal = insertNum * 10;
    TestSetCondVal(&pstCond.aCond[0], 11, DB_OP_LESS, (VOS_UINT8 *)&conVal, sizeof(conVal));

    // 3 DB_CountMatchingRecs
    VOS_UINT32 pulRecNum = 0;
    ret = DB_CountMatchingRecs(g_dbId, usRelId, &pstCond, &pulRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(insertNum * 2, pulRecNum);

    // 4 DB_DeleteRec
    uint32_t cnt = 0;
    Delete(g_dbId, usRelId, insertNum, &cnt);
    V1_AW_MACRO_EXPECT_EQ_INT(insertNum, cnt);

    // 5 DB_RecordExist
    conVal = insertNum;
    TestSetCondVal(&pstCond.aCond[0], 11, DB_OP_EQUAL, (VOS_UINT8 *)&conVal, sizeof(conVal));
    ret = DB_RecordExist(g_dbId, usRelId, &pstCond);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 6 DB_SelectAllRec
    // 查询数据
    pstCond.usCondNum = 0;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_DSBUF_STRU pstDsBufGet;
    pstDsBufGet.usRecLen = tblRecLen;
    pstDsBufGet.usRecNum = insertNum;
    pstDsBufGet.StdBuf.ulBufLen = pstDsBufGet.usRecLen * pstDsBufGet.usRecNum;
    VOS_UINT8 *pucDataGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufGet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataGet);
    (void)memset_s(pucDataGet, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
    pstDsBufGet.StdBuf.pucData = pucDataGet;
    ret = DB_SelectAllRec(g_dbId, usRelId, &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(insertNum, pstDsBufGet.usRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(pstDsBufGet.StdBuf.ulBufLen, pstDsBufGet.StdBuf.ulActLen);
    TEST_V1_FREE(pucDataGet);

    // 7 DB_SelectAllRecByOrder
    // 查询数据
    unsigned char sortFields[1] = {0};
    DB_SORT_STRU pstSort;
    pstSort.ucSortNum = 1;
    pstSort.enSortType = 1;
    pstSort.pSortFields = sortFields;

    pstDsBufGet.usRecLen = tblRecLen;
    pstDsBufGet.usRecNum = insertNum;
    pstDsBufGet.StdBuf.ulBufLen = pstDsBufGet.usRecLen * pstDsBufGet.usRecNum;
    pucDataGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufGet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataGet);
    (void)memset_s(pucDataGet, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
    pstDsBufGet.StdBuf.pucData = pucDataGet;
    ret = DB_SelectAllRecByOrder(g_dbId, usRelId, &pstSort, &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(insertNum, pstDsBufGet.usRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(pstDsBufGet.StdBuf.ulBufLen, pstDsBufGet.StdBuf.ulActLen);
    TEST_V1_FREE(pucDataGet);

    // 8 DB_SelectAllRecByOrderEx
    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = tblRecLen;
    pstBufDataGet.ulRecNum = insertNum;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum + 1);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret = DB_SelectAllRecByOrderEx(g_dbId, usRelId, &pstSort, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(insertNum, pstBufDataGet.ulRecNum);
    TEST_V1_FREE(pBufGet);

    // 9 DB_SelectFirstRec
    pstDsBufGet.usRecLen = tblRecLen;
    pstDsBufGet.usRecNum = insertNum;
    pstDsBufGet.StdBuf.ulBufLen = pstDsBufGet.usRecLen;
    pucDataGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufGet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataGet);
    (void)memset_s(pucDataGet, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
    pstDsBufGet.StdBuf.pucData = pucDataGet;
    ret = DB_SelectFirstRec(g_dbId, usRelId, &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(1, pstDsBufGet.usRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(pstDsBufGet.StdBuf.ulBufLen, pstDsBufGet.StdBuf.ulActLen);
    TEST_V1_FREE(pucDataGet);

    // 10 DB_SimpleSelectAllRec
    // 设置字段排序，不排序，接口不支持
    pstSort = {.ucSortNum = 0, .aucReserve = {0}, .enSortType = DB_SORTTYPE_ASCEND};

    // 获取查询计划
    pstCond = {.usCondNum = 1};
    conVal = 0;
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_EQUAL, (VOS_UINT8 *)&conVal, sizeof(conVal));

    DB_QUERY_PLAN_STRU pstQueryPlan;
    ret = DB_PrepareQueryPlan(g_dbId, usRelId, &pstCond, &pstQueryPlan);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 申请接口存放数据的buff
    DB_BUF_STRU pstBuf1;
    ret = TestMallocBuf(&pstBuf1, tblRecLen, insertNum, insertNum * tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    PvArgT pvArg = {.isAlreadyMalloc = true, .callCnt = 0, .bufAddr = &pstBuf1};

    // 申请回调使用的内存
    DB_BUF_STRU pstBuf2;
    ret = TestMallocBuf(&pstBuf2, tblRecLen, insertNum, insertNum * tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置参数
    DB_SELECT_STRU pstSelect = {.ulCdbId = TPC_GLOBAL_CDB,
        .ulDbId = g_dbId,
        .usRelNo = usRelId,
        .pstCond = &pstCond,
        .pstFldFilter = &pstFldFilter,
        .pstSort = &pstSort,
        .pstQueryPlan = &pstQueryPlan,
        .pfGetBuf = PfGetBufCallBack,
        .pvArg = (VOS_VOID *)&pvArg,
        .pstDataBuf = &pstBuf2};

    // 正常查询，保证参数正常
    ret = DB_SimpleSelectAllRec(&pstSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(1, pstBuf2.ulRecNum);

    // 释放内存
    TestFreeBuf(&pstBuf1);
    TestFreeBuf(&pstBuf2);

    // 11 DB_UpdateRec
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        UpdateAllType(g_dbId, usRelId, recBuf, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    ret = DB_CloseDB(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 017 DB接口建DB、表和写数据，TPC接口BeginSelect、FetchSelectRec和EndSelect，预期TPC操作失败
TEST_F(InterMixDML, V1Com_030_InterMixDML_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    ret = DB_CreateDB((VOS_UINT8 *)g_dbName, NULL, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_dbName, &g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 10;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 校验数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    // 设置查询条件
    DB_COND_STRU pstCond = {.usCondNum = 1};
    uint32_t conVal = insertNum * 10;
    TestSetCondVal(&pstCond.aCond[0], 11, DB_OP_LESS, (VOS_UINT8 *)&conVal, sizeof(conVal));

    // 1 TPC_BeginSelect
    DB_SELHANDLE phSelect;
    ret = TPC_BeginSelect(TPC_GLOBAL_CDB, g_dbId, usRelId, &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);

    DB_DSBUF_STRU pstDsBufGet;
    ret = TestMallocDsBuf(&pstDsBufGet, tblRecLen, 2, tblRecLen * 2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, phSelect, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDHANDLE, ret);
    TestFreeDsBuf(&pstDsBufGet);

    // 结束查询
    ret = TPC_EndSelect(TPC_GLOBAL_CDB, phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDHANDLE, ret);

    ret = DB_CloseDB(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 018 DB接口建DB、表和写数据，DB接口BeginSelect、TPC接口FetchSelectRec、DB接口EndSelect，预期TPC操作失败
TEST_F(InterMixDML, V1Com_030_InterMixDML_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    ret = DB_CreateDB((VOS_UINT8 *)g_dbName, NULL, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_dbName, &g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 10;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 校验数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    // 设置查询条件
    DB_COND_STRU pstCond = {.usCondNum = 1};
    uint32_t conVal = insertNum * 10;
    TestSetCondVal(&pstCond.aCond[0], 11, DB_OP_LESS, (VOS_UINT8 *)&conVal, sizeof(conVal));

    // 1 DB_BeginSelect
    DB_SELHANDLE phSelect;
    ret = DB_BeginSelect(g_dbId, usRelId, &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // TPC_FetchSelectRec
    DB_DSBUF_STRU pstDsBufGet;
    ret = TestMallocDsBuf(&pstDsBufGet, tblRecLen, 2, tblRecLen * 2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, phSelect, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);
    TestFreeDsBuf(&pstDsBufGet);

    // 结束查询
    ret = DB_EndSelect(phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CloseDB(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 019 TPC接口建DB、表和写数据，TPC接口BeginSelect、DB接口FetchSelectRec、TPC接口EndSelect，预期DB操作成功
TEST_F(InterMixDML, V1Com_030_InterMixDML_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    ret = TPC_CreateDB((VOS_UINT8 *)g_dbName, NULL, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_dbName, &g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 10;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 校验数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    // 设置查询条件
    DB_COND_STRU pstCond = {.usCondNum = 1};
    uint32_t conVal = insertNum * 10;
    TestSetCondVal(&pstCond.aCond[0], 11, DB_OP_LESS, (VOS_UINT8 *)&conVal, sizeof(conVal));

    // 1 TPC_BeginSelect
    DB_SELHANDLE phSelect;
    ret = TPC_BeginSelect(TPC_GLOBAL_CDB, g_dbId, usRelId, &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DB_FetchSelectRec
    DB_DSBUF_STRU pstDsBufGet;
    ret = TestMallocDsBuf(&pstDsBufGet, tblRecLen, 2, tblRecLen * 2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FetchSelectRec(phSelect, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeDsBuf(&pstDsBufGet);

    // 结束查询
    ret = TPC_EndSelect(TPC_GLOBAL_CDB, phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CloseDB(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 020 DB接口建DB、表和写数据，DB接口BeginSelect、DB接口FetchSelectRec、TPC接口EndSelect，预期TPC操作成功
TEST_F(InterMixDML, V1Com_030_InterMixDML_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    ret = DB_CreateDB((VOS_UINT8 *)g_dbName, NULL, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_dbName, &g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 10;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 校验数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    // 设置查询条件
    DB_COND_STRU pstCond = {.usCondNum = 1};
    uint32_t conVal = insertNum * 10;
    TestSetCondVal(&pstCond.aCond[0], 11, DB_OP_LESS, (VOS_UINT8 *)&conVal, sizeof(conVal));

    // 1 DB_BeginSelect
    DB_SELHANDLE phSelect;
    ret = DB_BeginSelect(g_dbId, usRelId, &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DB_FetchSelectRec
    DB_DSBUF_STRU pstDsBufGet;
    ret = TestMallocDsBuf(&pstDsBufGet, tblRecLen, 2, tblRecLen * 2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FetchSelectRec(phSelect, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeDsBuf(&pstDsBufGet);

    // 结束查询
    ret = TPC_EndSelect(TPC_GLOBAL_CDB, phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CloseDB(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 021 TPC接口建DB、表和写数据，TPC接口BeginSelect、TPC接口FetchSelectRec、DB接口EndSelect，预期DB操作成功
TEST_F(InterMixDML, V1Com_030_InterMixDML_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    ret = TPC_CreateDB((VOS_UINT8 *)g_dbName, NULL, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_dbName, &g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 10;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 校验数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    // 设置查询条件
    DB_COND_STRU pstCond = {.usCondNum = 1};
    uint32_t conVal = insertNum * 10;
    TestSetCondVal(&pstCond.aCond[0], 11, DB_OP_LESS, (VOS_UINT8 *)&conVal, sizeof(conVal));

    // 1 TPC_BeginSelect
    DB_SELHANDLE phSelect;
    ret = TPC_BeginSelect(TPC_GLOBAL_CDB, g_dbId, usRelId, &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DB_FetchSelectRec
    DB_DSBUF_STRU pstDsBufGet;
    ret = TestMallocDsBuf(&pstDsBufGet, tblRecLen, 2, tblRecLen * 2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, phSelect, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeDsBuf(&pstDsBufGet);

    // 结束查询
    ret = DB_EndSelect(phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CloseDB(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 022 DB接口建DB、表和写数据，DB接口BeginSelect多次、TPC接口CloseAllHandles，预期TPC操作失败
TEST_F(InterMixDML, V1Com_030_InterMixDML_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    ret = DB_CreateDB((VOS_UINT8 *)g_dbName, NULL, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_dbName, &g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 10;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 校验数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    // 设置查询条件
    DB_COND_STRU pstCond = {.usCondNum = 1};
    uint32_t conVal = insertNum * 10;
    TestSetCondVal(&pstCond.aCond[0], 11, DB_OP_LESS, (VOS_UINT8 *)&conVal, sizeof(conVal));

    // 1 DB_BeginSelect
    DB_SELHANDLE phSelect;
    ret = DB_BeginSelect(g_dbId, usRelId, &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_BeginSelect(g_dbId, usRelId, &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_BeginSelect(g_dbId, usRelId, &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DB_FetchSelectRec
    DB_DSBUF_STRU pstDsBufGet;
    ret = TestMallocDsBuf(&pstDsBufGet, tblRecLen, 2, tblRecLen * 2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FetchSelectRec(phSelect, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeDsBuf(&pstDsBufGet);

    // 结束查询
    ret = TPC_CloseAllHandles(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);
    ret = DB_CloseAllHandles(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CloseDB(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 023 TPC接口建DB、表和写数据，TPC接口BeginSelect多次、DB接口CloseAllHandles，预期TPC操作成功
TEST_F(InterMixDML, V1Com_030_InterMixDML_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    ret = TPC_CreateDB((VOS_UINT8 *)g_dbName, NULL, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_dbName, &g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 10;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 校验数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    // 设置查询条件
    DB_COND_STRU pstCond = {.usCondNum = 1};
    uint32_t conVal = insertNum * 10;
    TestSetCondVal(&pstCond.aCond[0], 11, DB_OP_LESS, (VOS_UINT8 *)&conVal, sizeof(conVal));

    // 1 TPC_BeginSelect
    DB_SELHANDLE phSelect;
    ret = TPC_BeginSelect(TPC_GLOBAL_CDB, g_dbId, usRelId, &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_BeginSelect(TPC_GLOBAL_CDB, g_dbId, usRelId, &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_BeginSelect(TPC_GLOBAL_CDB, g_dbId, usRelId, &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DB_FetchSelectRec
    DB_DSBUF_STRU pstDsBufGet;
    ret = TestMallocDsBuf(&pstDsBufGet, tblRecLen, 2, tblRecLen * 2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, phSelect, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeDsBuf(&pstDsBufGet);

    // 结束查询
    ret = DB_CloseAllHandles(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CloseDB(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 024 TPC接口建DB、表和写数据，DB接口PrepareQueryPlan、TPC接口SelectAllRec2，预期DB接口操作成功
TEST_F(InterMixDML, V1Com_030_InterMixDML_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    ret = TPC_CreateDB((VOS_UINT8 *)g_dbName, NULL, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_dbName, &g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 校验数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        ret = TpcSelectAllType(TPC_GLOBAL_CDB, g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum, true);
        if (ret != DB_SUCCESS_V1) {
            V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);
            AW_FUN_Log(LOG_ERROR, "TpcSelectAllType failed with ret = %d, i = %d", ret, i);
            break;
        }
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 设置查询条件
    DB_COND_STRU pstCond = {.usCondNum = 1};
    uint32_t conVal = insertNum * 10;
    TestSetCondVal(&pstCond.aCond[0], 11, DB_OP_LESS, (VOS_UINT8 *)&conVal, sizeof(conVal));

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_SORT_STRU pstSort = {.ucSortNum = 0, .aucReserve = {0}, .enSortType = DB_SORTTYPE_ASCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {0};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 获取查询计划
    DB_QUERY_PLAN_STRU pstQueryPlan;
    ret = DB_PrepareQueryPlan(g_dbId, usRelId, &pstCond, &pstQueryPlan);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 申请接口存放数据的buff
    DB_BUF_STRU pstBuf1;
    ret = TestMallocBuf(&pstBuf1, tblRecLen, insertNum, insertNum * tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    PvArgT pvArg = {.isAlreadyMalloc = true, .callCnt = 0, .bufAddr = &pstBuf1};

    // 申请回调使用的内存
    DB_BUF_STRU pstBuf2;
    ret = TestMallocBuf(&pstBuf2, tblRecLen, insertNum, insertNum * tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置参数
    DB_SELECT_STRU pstSelect = {.ulCdbId = TPC_GLOBAL_CDB,
        .ulDbId = g_dbId,
        .usRelNo = usRelId,
        .pstCond = &pstCond,
        .pstFldFilter = &pstFldFilter,
        .pstSort = &pstSort,
        .pstQueryPlan = &pstQueryPlan,
        .pfGetBuf = PfGetBufCallBack,
        .pvArg = (VOS_VOID *)&pvArg,
        .pstDataBuf = &pstBuf2};

    // 正常查询，保证参数正常
    ret = TPC_SelectAllRec2(&pstSelect);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放内存
    TestFreeBuf(&pstBuf1);
    TestFreeBuf(&pstBuf2);

    ret = DB_CloseDB(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 025 DB接口建DB、表和写数据，TPC接口PrepareQueryPlan、DB接口SelectAllRec2，预期TPC接口操作失败
TEST_F(InterMixDML, V1Com_030_InterMixDML_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    ret = DB_CreateDB((VOS_UINT8 *)g_dbName, NULL, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_dbName, &g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 校验数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 设置查询条件
    DB_COND_STRU pstCond = {.usCondNum = 1};
    uint32_t conVal = insertNum * 10;
    TestSetCondVal(&pstCond.aCond[0], 11, DB_OP_LESS, (VOS_UINT8 *)&conVal, sizeof(conVal));

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_SORT_STRU pstSort = {.ucSortNum = 0, .aucReserve = {0}, .enSortType = DB_SORTTYPE_ASCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {0};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 获取查询计划
    DB_QUERY_PLAN_STRU pstQueryPlan;
    ret = TPC_PrepareQueryPlan(TPC_GLOBAL_CDB, g_dbId, usRelId, &pstCond, &pstQueryPlan);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);

    // 申请接口存放数据的buff
    DB_BUF_STRU pstBuf1;
    ret = TestMallocBuf(&pstBuf1, tblRecLen, insertNum, insertNum * tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    PvArgT pvArg = {.isAlreadyMalloc = true, .callCnt = 0, .bufAddr = &pstBuf1};

    // 申请回调使用的内存
    DB_BUF_STRU pstBuf2;
    ret = TestMallocBuf(&pstBuf2, tblRecLen, insertNum, insertNum * tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置参数
    DB_SELECT_STRU pstSelect = {.ulCdbId = TPC_GLOBAL_CDB,
        .ulDbId = g_dbId,
        .usRelNo = usRelId,
        .pstCond = &pstCond,
        .pstFldFilter = &pstFldFilter,
        .pstSort = &pstSort,
        .pstQueryPlan = NULL,
        .pfGetBuf = PfGetBufCallBack,
        .pvArg = (VOS_VOID *)&pvArg,
        .pstDataBuf = &pstBuf2};

    // 正常查询，保证参数正常
    ret = DB_SelectAllRec2(&pstSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放内存
    TestFreeBuf(&pstBuf1);
    TestFreeBuf(&pstBuf2);

    ret = DB_CloseDB(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 026 TPC接口建DB、表和写数据，TPC接口PrepareQueryPlan、DB接口SelectAllRec2，预期DB接口操作成功
TEST_F(InterMixDML, V1Com_030_InterMixDML_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    ret = TPC_CreateDB((VOS_UINT8 *)g_dbName, NULL, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_dbName, &g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 校验数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        ret = TpcSelectAllType(TPC_GLOBAL_CDB, g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum, true);
        if (ret != DB_SUCCESS_V1) {
            V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);
            AW_FUN_Log(LOG_ERROR, "TpcSelectAllType failed with ret = %d, i = %d", ret, i);
            break;
        }
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 设置查询条件
    DB_COND_STRU pstCond = {.usCondNum = 1};
    uint32_t conVal = insertNum * 10;
    TestSetCondVal(&pstCond.aCond[0], 11, DB_OP_LESS, (VOS_UINT8 *)&conVal, sizeof(conVal));

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_SORT_STRU pstSort = {.ucSortNum = 0, .aucReserve = {0}, .enSortType = DB_SORTTYPE_ASCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {0};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 获取查询计划
    DB_QUERY_PLAN_STRU pstQueryPlan;
    ret = TPC_PrepareQueryPlan(TPC_GLOBAL_CDB, g_dbId, usRelId, &pstCond, &pstQueryPlan);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 申请接口存放数据的buff
    DB_BUF_STRU pstBuf1;
    ret = TestMallocBuf(&pstBuf1, tblRecLen, insertNum, insertNum * tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    PvArgT pvArg = {.isAlreadyMalloc = true, .callCnt = 0, .bufAddr = &pstBuf1};

    // 申请回调使用的内存
    DB_BUF_STRU pstBuf2;
    ret = TestMallocBuf(&pstBuf2, tblRecLen, insertNum, insertNum * tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置参数
    DB_SELECT_STRU pstSelect = {.ulCdbId = TPC_GLOBAL_CDB,
        .ulDbId = g_dbId,
        .usRelNo = usRelId,
        .pstCond = &pstCond,
        .pstFldFilter = &pstFldFilter,
        .pstSort = &pstSort,
        .pstQueryPlan = &pstQueryPlan,
        .pfGetBuf = PfGetBufCallBack,
        .pvArg = (VOS_VOID *)&pvArg,
        .pstDataBuf = &pstBuf2};

    // 正常查询，保证参数正常
    ret = DB_SelectAllRec2(&pstSelect);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放内存
    TestFreeBuf(&pstBuf1);
    TestFreeBuf(&pstBuf2);

    ret = DB_CloseDB(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 027 DB接口建DB、表和写数据，DB接口PrepareQueryPlan、TPC接口SelectAllRec2，预期TPC接口操作失败
TEST_F(InterMixDML, V1Com_030_InterMixDML_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    ret = DB_CreateDB((VOS_UINT8 *)g_dbName, NULL, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_dbName, &g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestDB_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestDBGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 校验数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 设置查询条件
    DB_COND_STRU pstCond = {.usCondNum = 1};
    uint32_t conVal = insertNum * 10;
    TestSetCondVal(&pstCond.aCond[0], 11, DB_OP_LESS, (VOS_UINT8 *)&conVal, sizeof(conVal));

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_SORT_STRU pstSort = {.ucSortNum = 0, .aucReserve = {0}, .enSortType = DB_SORTTYPE_ASCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {0};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 获取查询计划
    DB_QUERY_PLAN_STRU pstQueryPlan;
    ret = DB_PrepareQueryPlan(g_dbId, usRelId, &pstCond, &pstQueryPlan);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 申请接口存放数据的buff
    DB_BUF_STRU pstBuf1;
    ret = TestMallocBuf(&pstBuf1, tblRecLen, insertNum, insertNum * tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    PvArgT pvArg = {.isAlreadyMalloc = true, .callCnt = 0, .bufAddr = &pstBuf1};

    // 申请回调使用的内存
    DB_BUF_STRU pstBuf2;
    ret = TestMallocBuf(&pstBuf2, tblRecLen, insertNum, insertNum * tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置参数
    DB_SELECT_STRU pstSelect = {.ulCdbId = TPC_GLOBAL_CDB,
        .ulDbId = g_dbId,
        .usRelNo = usRelId,
        .pstCond = &pstCond,
        .pstFldFilter = &pstFldFilter,
        .pstSort = &pstSort,
        .pstQueryPlan = &pstQueryPlan,
        .pfGetBuf = PfGetBufCallBack,
        .pvArg = (VOS_VOID *)&pvArg,
        .pstDataBuf = &pstBuf2};

    // 正常查询，保证参数正常
    ret = TPC_SelectAllRec2(&pstSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);
    // 释放内存
    TestFreeBuf(&pstBuf1);
    TestFreeBuf(&pstBuf2);

    ret = DB_CloseDB(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 028 TPC接口建DB和表，开启cdb后DB接口写入数据，TPC接口写入相同的数据后提交，预期cdb提交失败
TEST_F(InterMixDML, V1Com_030_InterMixDML_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    ret = TPC_CreateDB((VOS_UINT8 *)g_dbName, NULL, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_dbName, &g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    // 开启CDB
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_dbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DB接口写入0-99
    int insertNum = 100;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // TPC接口写入0-99，预期提交失败
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(cdbID, g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_TPC_RDB_MODIFIED, ret);
    // 回滚cdb
    ret = TPC_RollbackCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    ret = DB_CloseDB(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 029 二阶段表中有数据时先使用一阶段开启handle，再开启CDB进行DML操作后查询
TEST_F(InterMixDML, V1Com_030_InterMixDML_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    ret = TPC_CreateDB((VOS_UINT8 *)g_dbName, NULL, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_dbName, &g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    // TPC接口写入0-99
    int insertNum = 100;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 校验数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // DB_BeginSelect开启查询句柄
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 11;
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_INT32 *)pstCond.aCond[0].aucValue = 11;

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_SELHANDLE phSelect;

    ret = DB_BeginSelect(g_dbId, usRelId, &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启CDB
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_dbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = insertNum; i < 2 * insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(cdbID, g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验数据
    for (int i = 0; i < 2 * insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 查询数据
    DB_DSBUF_STRU DsBufGet;
    DsBufGet.usRecLen = tblRecLen;  // DBDDL_GetRelRecLen(g_dbId, usRelId);
    DsBufGet.usRecNum = 1;
    DsBufGet.StdBuf.ulBufLen = DsBufGet.usRecLen * (DsBufGet.usRecNum);
    VOS_UINT8 *pucDataGet = (VOS_UINT8 *)TEST_V1_MALLOC(DsBufGet.StdBuf.ulBufLen);
    (void)memset_s(pucDataGet, DsBufGet.StdBuf.ulBufLen, 0x00, DsBufGet.StdBuf.ulBufLen);
    DsBufGet.StdBuf.pucData = pucDataGet;

    ret = DB_FetchSelectRec(phSelect, &DsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(1, DsBufGet.usRecNum);
    ret = DB_EndSelect(phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(pucDataGet);

    ret = DB_CloseDB(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 030 二阶段表中没有数据时先使用一阶段开启QueryPlan，再开启CDB进行DML操作后查询
TEST_F(InterMixDML, V1Com_030_InterMixDML_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    ret = TPC_CreateDB((VOS_UINT8 *)g_dbName, NULL, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_dbName, &g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    int insertNum = 100;
    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    // 设置查询条件
    DB_COND_STRU pstCond = {.usCondNum = 1};
    uint32_t conVal = insertNum * 10;
    TestSetCondVal(&pstCond.aCond[0], 11, DB_OP_LESS, (VOS_UINT8 *)&conVal, sizeof(conVal));
    // 获取查询计划
    DB_QUERY_PLAN_STRU pstQueryPlan;
    ret = DB_PrepareQueryPlan(g_dbId, usRelId, &pstCond, &pstQueryPlan);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启CDB
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_dbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(cdbID, g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 校验数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        ret = TpcSelectAllType(cdbID, g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum, true);
        if (ret != DB_SUCCESS_V1) {
            V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);
            AW_FUN_Log(LOG_ERROR, "TpcSelectAllType failed with ret = %d, i = %d", ret, i);
            break;
        }
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }
    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_SORT_STRU pstSort = {.ucSortNum = 0, .aucReserve = {0}, .enSortType = DB_SORTTYPE_ASCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {0};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 申请接口存放数据的buff
    DB_BUF_STRU pstBuf1;
    ret = TestMallocBuf(&pstBuf1, tblRecLen, insertNum, insertNum * tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    PvArgT pvArg = {.isAlreadyMalloc = true, .callCnt = 0, .bufAddr = &pstBuf1};

    // 申请回调使用的内存
    DB_BUF_STRU pstBuf2;
    ret = TestMallocBuf(&pstBuf2, tblRecLen, insertNum, insertNum * tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置参数
    DB_SELECT_STRU pstSelect = {.ulCdbId = TPC_GLOBAL_CDB,
        .ulDbId = g_dbId,
        .usRelNo = usRelId,
        .pstCond = &pstCond,
        .pstFldFilter = &pstFldFilter,
        .pstSort = &pstSort,
        .pstQueryPlan = &pstQueryPlan,
        .pfGetBuf = PfGetBufCallBack,
        .pvArg = (VOS_VOID *)&pvArg,
        .pstDataBuf = &pstBuf2};

    // 正常查询，保证参数正常
    ret = DB_SelectAllRec2(&pstSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(insertNum, pstBuf2.ulRecNum);
    // 释放内存
    TestFreeBuf(&pstBuf1);
    TestFreeBuf(&pstBuf2);

    ret = DB_CloseDB(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 031 二阶段表中有数据时先使用一阶段开启QueryPlan，再开启CDB进行DML操作后查询
TEST_F(InterMixDML, V1Com_030_InterMixDML_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    ret = TPC_CreateDB((VOS_UINT8 *)g_dbName, NULL, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_dbName, &g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 100;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 校验数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        ret = TpcSelectAllType(TPC_GLOBAL_CDB, g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum, true);
        if (ret != DB_SUCCESS_V1) {
            V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);
            AW_FUN_Log(LOG_ERROR, "TpcSelectAllType failed with ret = %d, i = %d", ret, i);
            break;
        }
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 设置查询条件
    DB_COND_STRU pstCond = {.usCondNum = 1};
    uint32_t conVal = insertNum * 10;
    TestSetCondVal(&pstCond.aCond[0], 11, DB_OP_LESS, (VOS_UINT8 *)&conVal, sizeof(conVal));
    // 获取查询计划
    DB_QUERY_PLAN_STRU pstQueryPlan;
    ret = DB_PrepareQueryPlan(g_dbId, usRelId, &pstCond, &pstQueryPlan);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启CDB
    uint32_t cdbID = 0;
    ret = TPC_BeginCDB(g_dbId, &cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = insertNum; i < 2 * insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(cdbID, g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 校验数据
    for (int i = 0; i < 2 * insertNum; i++) {
        uint32_t recNum = 0;
        ret = TpcSelectAllType(cdbID, g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum, true);
        if (ret != DB_SUCCESS_V1) {
            V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);
            AW_FUN_Log(LOG_ERROR, "TpcSelectAllType failed with ret = %d, i = %d", ret, i);
            break;
        }
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }
    // 提交事务
    ret = TPC_CommitCDB(cdbID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_SORT_STRU pstSort = {.ucSortNum = 0, .aucReserve = {0}, .enSortType = DB_SORTTYPE_ASCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {0};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 申请接口存放数据的buff
    DB_BUF_STRU pstBuf1;
    ret = TestMallocBuf(&pstBuf1, tblRecLen, 2 * insertNum, 2 * insertNum * tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    PvArgT pvArg = {.isAlreadyMalloc = true, .callCnt = 0, .bufAddr = &pstBuf1};

    // 申请回调使用的内存
    DB_BUF_STRU pstBuf2;
    ret = TestMallocBuf(&pstBuf2, tblRecLen, 2 * insertNum, 2 * insertNum * tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置参数
    DB_SELECT_STRU pstSelect = {.ulCdbId = TPC_GLOBAL_CDB,
        .ulDbId = g_dbId,
        .usRelNo = usRelId,
        .pstCond = &pstCond,
        .pstFldFilter = &pstFldFilter,
        .pstSort = &pstSort,
        .pstQueryPlan = &pstQueryPlan,
        .pfGetBuf = PfGetBufCallBack,
        .pvArg = (VOS_VOID *)&pvArg,
        .pstDataBuf = &pstBuf2};

    // 正常查询，保证参数正常
    ret = DB_SelectAllRec2(&pstSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(insertNum * 2, pstBuf2.ulRecNum);
    // 释放内存
    TestFreeBuf(&pstBuf1);
    TestFreeBuf(&pstBuf2);

    ret = DB_CloseDB(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 032 二阶段表使用一阶段接口DML操作后，导入导出并校验导入后数据
TEST_F(InterMixDML, V1Com_030_InterMixDML_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    ret = TPC_CreateDB((VOS_UINT8 *)g_dbName, NULL, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_dbName, &g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 1000;
    // 1 DB_SelectAllRecEx
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(0, recNum);
    }

    // 2 DB_InsertRec
    for (int i = 0; i < 2 * insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(g_dbId, usRelId, &dsBuf);
        if (ret != DB_SUCCESS_V1) {
            V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DATABASE, ret);
            AW_FUN_Log(LOG_ERROR, "DB_InsertRec failed with ret = %d, i = %d", ret, i);
            break;
        }
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    for (int i = 0; i < 2 * insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    // 设置查询条件
    DB_COND_STRU pstCond = {.usCondNum = 1};
    uint32_t conVal = insertNum * 10;
    TestSetCondVal(&pstCond.aCond[0], 11, DB_OP_LESS, (VOS_UINT8 *)&conVal, sizeof(conVal));

    // 3 DB_CountMatchingRecs
    VOS_UINT32 pulRecNum = 0;
    ret = DB_CountMatchingRecs(g_dbId, usRelId, &pstCond, &pulRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(insertNum * 2, pulRecNum);

    // 4 DB_DeleteRec
    uint32_t cnt = 0;
    Delete(g_dbId, usRelId, insertNum, &cnt);
    V1_AW_MACRO_EXPECT_EQ_INT(insertNum, cnt);

    // 5 DB_RecordExist
    conVal = insertNum;
    TestSetCondVal(&pstCond.aCond[0], 11, DB_OP_EQUAL, (VOS_UINT8 *)&conVal, sizeof(conVal));
    ret = DB_RecordExist(g_dbId, usRelId, &pstCond);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 6 DB_SelectAllRec
    // 查询数据
    pstCond.usCondNum = 0;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_DSBUF_STRU pstDsBufGet;
    pstDsBufGet.usRecLen = tblRecLen;
    pstDsBufGet.usRecNum = insertNum;
    pstDsBufGet.StdBuf.ulBufLen = pstDsBufGet.usRecLen * pstDsBufGet.usRecNum;
    VOS_UINT8 *pucDataGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufGet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataGet);
    (void)memset_s(pucDataGet, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
    pstDsBufGet.StdBuf.pucData = pucDataGet;
    ret = DB_SelectAllRec(g_dbId, usRelId, &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(insertNum, pstDsBufGet.usRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(pstDsBufGet.StdBuf.ulBufLen, pstDsBufGet.StdBuf.ulActLen);
    TEST_V1_FREE(pucDataGet);

    // 7 DB_SelectAllRecByOrder
    // 查询数据
    unsigned char sortFields[1] = {0};
    DB_SORT_STRU pstSort;
    pstSort.ucSortNum = 1;
    pstSort.enSortType = 1;
    pstSort.pSortFields = sortFields;

    pstDsBufGet.usRecLen = tblRecLen;
    pstDsBufGet.usRecNum = insertNum;
    pstDsBufGet.StdBuf.ulBufLen = pstDsBufGet.usRecLen * pstDsBufGet.usRecNum;
    pucDataGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufGet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataGet);
    (void)memset_s(pucDataGet, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
    pstDsBufGet.StdBuf.pucData = pucDataGet;
    ret = DB_SelectAllRecByOrder(g_dbId, usRelId, &pstSort, &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(insertNum, pstDsBufGet.usRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(pstDsBufGet.StdBuf.ulBufLen, pstDsBufGet.StdBuf.ulActLen);
    TEST_V1_FREE(pucDataGet);

    // 8 DB_SelectAllRecByOrderEx
    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = tblRecLen;
    pstBufDataGet.ulRecNum = insertNum;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum + 1);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret = DB_SelectAllRecByOrderEx(g_dbId, usRelId, &pstSort, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(insertNum, pstBufDataGet.ulRecNum);
    TEST_V1_FREE(pBufGet);

    // 9 DB_SelectFirstRec
    pstDsBufGet.usRecLen = tblRecLen;
    pstDsBufGet.usRecNum = insertNum;
    pstDsBufGet.StdBuf.ulBufLen = pstDsBufGet.usRecLen;
    pucDataGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufGet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataGet);
    (void)memset_s(pucDataGet, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
    pstDsBufGet.StdBuf.pucData = pucDataGet;
    ret = DB_SelectFirstRec(g_dbId, usRelId, &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(1, pstDsBufGet.usRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(pstDsBufGet.StdBuf.ulBufLen, pstDsBufGet.StdBuf.ulActLen);
    TEST_V1_FREE(pucDataGet);

    // 10 DB_SimpleSelectAllRec
    // 设置字段排序，不排序，接口不支持
    pstSort = {.ucSortNum = 0, .aucReserve = {0}, .enSortType = DB_SORTTYPE_ASCEND};

    // 获取查询计划
    pstCond = {.usCondNum = 1};
    conVal = 0;
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_EQUAL, (VOS_UINT8 *)&conVal, sizeof(conVal));

    DB_QUERY_PLAN_STRU pstQueryPlan;
    ret = DB_PrepareQueryPlan(g_dbId, usRelId, &pstCond, &pstQueryPlan);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 申请接口存放数据的buff
    DB_BUF_STRU pstBuf1;
    ret = TestMallocBuf(&pstBuf1, tblRecLen, insertNum, insertNum * tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    PvArgT pvArg = {.isAlreadyMalloc = true, .callCnt = 0, .bufAddr = &pstBuf1};

    // 申请回调使用的内存
    DB_BUF_STRU pstBuf2;
    ret = TestMallocBuf(&pstBuf2, tblRecLen, insertNum, insertNum * tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置参数
    DB_SELECT_STRU pstSelect = {.ulCdbId = TPC_GLOBAL_CDB,
        .ulDbId = g_dbId,
        .usRelNo = usRelId,
        .pstCond = &pstCond,
        .pstFldFilter = &pstFldFilter,
        .pstSort = &pstSort,
        .pstQueryPlan = &pstQueryPlan,
        .pfGetBuf = PfGetBufCallBack,
        .pvArg = (VOS_VOID *)&pvArg,
        .pstDataBuf = &pstBuf2};

    // 正常查询，保证参数正常
    ret = DB_SimpleSelectAllRec(&pstSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(1, pstBuf2.ulRecNum);

    // 释放内存
    TestFreeBuf(&pstBuf1);
    TestFreeBuf(&pstBuf2);

    // 11 DB_UpdateRec
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        UpdateAllType(g_dbId, usRelId, recBuf, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    /* ========= step1 DB_PhyBkp2导出 ========= */
    char filePath[64] = "./filePath/export.txt";
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_DEFAULT_ENDIAN;
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &pstFileSaveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = true;
    ret = DB_PhyBkp2(g_dbId, (uint8_t *)filePath, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CRC校验
    ret = DB_CheckDataFile((VOS_UINT8 *)filePath, DB_CHECK_DETAILED);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    ret = DB_DropTbl(g_dbId, usRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    /* ========= step2 导入 ========= */
    // 1 DB_Restore导入
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = DB_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)g_dbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, 10000 + i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }

    ret = DB_CloseDB(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

typedef struct TagThrParaT {
    uint32_t dbId;
    uint16_t relId;
    VOS_UINT32 tblRecLen;
} ThrParaT;

void *ThreadHandle(void *arg)
{
    ThrParaT *para = (ThrParaT *)arg;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    // 设置查询条件
    DB_COND_STRU pstCond = {.usCondNum = 1};
    int insertNum = 10;
    uint32_t conVal = insertNum * 10;
    TestSetCondVal(&pstCond.aCond[0], 11, DB_OP_LESS, (VOS_UINT8 *)&conVal, sizeof(conVal));

    // 1 TPC_BeginSelect
    DB_SELHANDLE phSelect;
    int ret = TPC_BeginSelect(TPC_GLOBAL_CDB, para->dbId, para->relId, &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DB_FetchSelectRec
    DB_DSBUF_STRU pstDsBufGet;
    ret = TestMallocDsBuf(&pstDsBufGet, para->tblRecLen, 2, para->tblRecLen * 2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_FetchSelectRec(TPC_GLOBAL_CDB, phSelect, &pstDsBufGet);
    if (ret != DB_SUCCESS_V1) {
        V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDHANDLE, ret);
        TestFreeDsBuf(&pstDsBufGet);
    } else {
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        TestFreeDsBuf(&pstDsBufGet);
    }

    // 结束查询
    ret = TPC_CloseAllHandles(para->dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    return NULL;
}
// 033 多线程handle查询  || DTS2025031324562
TEST_F(InterMixDML, V1Com_030_InterMixDML_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    ret = TPC_CreateDB((VOS_UINT8 *)g_dbName, NULL, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_dbName, &g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据类型
    ret = CreateCustom();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 普通表
    uint16_t usRelId;
    DB_REL_DEF_STRU stRelDef;
    ret = TestTPC_CreateTbl(g_dbId, "./schemaFile/table_test_normal_001.json", &usRelId, &stRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_dbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);

    int insertNum = 10;
    for (int i = 0; i < insertNum; i++) {
        InsertAllType(recBuf, stRelDef.pstFldLst, i, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_dbId, usRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 校验数据
    for (int i = 0; i < insertNum; i++) {
        uint32_t recNum = 0;
        SelectAllType(g_dbId, usRelId, stRelDef.pstFldLst, i, &recNum);
        V1_AW_MACRO_EXPECT_EQ_INT(1, recNum);
    }
#if (defined ENV_RTOSV2 && defined CPU_BIT_32)
    int cnt = 100;
#else
    int cnt = 1000;
#endif
    pthread_t tid[cnt];
    // 多线程handle查询
    ThrParaT para1 = {.dbId = g_dbId, .relId = usRelId, .tblRecLen = tblRecLen};
    for (int i = 0; i < cnt; i++) {
        ret = pthread_create(&tid[i], NULL, ThreadHandle, &para1);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    for (int i = 0; i < cnt; i++) {
        pthread_join(tid[i], NULL);
    }
    ret = DB_CloseDB(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(recBuf);
    FreeRelDef(&stRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}
