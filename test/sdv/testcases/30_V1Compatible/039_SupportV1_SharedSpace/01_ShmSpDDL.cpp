/*
001 共享空间clone表后的DML操作校验
002 共享空间中持久化DBclose再open后校验数据
003 共享空间中删表后校验DB内存
004 创建表后查询是否使用共享空间
005 创建大对象表后查询是否使用共享空间
006 修改表名表id后查询是否使用共享空间
007 创建多个DB，每个DB的表写入少量数据后查询是否在不同共享空间内

2 查询
2.1 表无索引
008 对无索引的表使用GetRelAllRecords进行查询
009 对无索引的表使用CountMatchingRecs进行查询
010 对无索引的表使用FetchSelectRec进行查询
011 对无索引的表使用RecordExist进行查询
012 对无索引的表使用SelectAllRec进行查询
013 对无索引的表使用SelectAllRecEx进行查询
014 对无索引的表使用SelectAllRec2进行查询
015 对无索引的表使用SelectAllRecByOrder进行查询
016 对无索引的表使用SelectAllRecByOrderEx进行查询
017 对无索引的表使用SelectFirstRec进行查询
018 对无索引的表使用SimpleSelectAllRec进行查询

2.2 表含非唯一索引
019 对无唯一索引的表使用GetRelAllRecords进行查询
020 对无唯一索引的表使用CountMatchingRecs进行查询
021 对无唯一索引的表使用FetchSelectRec进行查询
022 对无唯一索引的表使用RecordExist进行查询
023 对无唯一索引的表使用SelectAllRec进行查询
024 对无唯一索引的表使用SelectAllRecEx进行查询
025 对无唯一索引的表使用SelectAllRec2进行查询
026 对无唯一索引的表使用SelectAllRecByOrder进行查询
027 对无唯一索引的表使用SelectAllRecByOrderEx进行查询
028 对无唯一索引的表使用SelectFirstRec进行查询
029 对无唯一索引的表使用SimpleSelectAllRec进行查询

2.3 表含唯一索引
030 对唯一索引的表使用GetRelAllRecords进行查询
031 对唯一索引的表使用CountMatchingRecs进行查询
032 对唯一索引的表使用FetchSelectRec进行查询
033 对唯一索引的表使用RecordExist进行查询
034 对唯一索引的表使用SelectAllRec进行查询
035 对唯一索引的表使用SelectAllRecEx进行查询
036 对唯一索引的表使用SelectAllRec2进行查询
037 对唯一索引的表使用SelectAllRecByOrder进行查询
038 对唯一索引的表使用SelectAllRecByOrderEx进行查询
039 对唯一索引的表使用SelectFirstRec进行查询
040 对唯一索引的表使用SimpleSelectAllRec进行查询

2.4 图查询
041 对所有表数据都存储在共享空间的图进行全路径查询
042 对所有表数据都存储在共享空间的图进行单跳查询
043 对部分表数据存储在共享空间的图进行全路径查询
044 对部分表数据存储在共享空间的图进行单跳查询
045 开启查询句柄后，删除数据至低于阈值后进行fetch数据
046 开启查询句柄后，插入数据至高于阈值后进行fetch数据
047 开启查询句柄后，在共享空间内增删改查后进行fetch数据
048 开启查询句柄后，删除数据至低于阈值后进行SelectAllRec2数据
049 开启查询句柄后，插入数据至高于阈值后进行SelectAllRec2数据
050 开启查询句柄后，在共享空间内增删改查后进行SelectAllRec2数据

3 导入导出
051 表定义数据长度大于pageSize/8 - 2，表无数据时导出导入
052 表定义数据长度等于pageSize/8 - 2，表无数据时导出导入
*/

#include "ShmSpOperation.h"

class ShmSpDDL : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void ShmSpDDL::SetUpTestCase()
{
    system("mkdir perFilePath;echo 'This is persistent DB' > ./perFilePath/perDbPath");
    system("mkdir filePath;echo 'This is normal DB' > ./filePath/normalDbPath");
}

void ShmSpDDL::TearDownTestCase()
{
    system("rm -rf ./filePath/ perFilePath/ check.txt");
}

void ShmSpDDL::SetUp()
{
    system("sh $TEST_HOME/tools/modifyCfg.sh \"pageSize=32\"");
    // 初始化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_Init());
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    AW_CHECK_LOG_BEGIN();
    int ret;

    ret = TPC_CreateDB((VOS_UINT8 *)g_dbName, NULL, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_dbName, &g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CreateDB((VOS_UINT8 *)g_dbName2, NULL, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    g_perDbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, &g_perDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)g_perDbName, &g_perDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}

void ShmSpDDL::TearDown()
{
    // CLoseDB
    int ret = TPC_CloseDB(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_CloseDB(g_perDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)g_dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_perDbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_UnInit());
    AW_CHECK_LOG_END();
}

// 001 共享空间clone表后的DML操作校验
TEST_F(ShmSpDDL, V1Com_039_ShmSpDDL_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    ret = DB_CreateDB((VOS_UINT8 *)g_dbName3, NULL, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_dbName3, &g_dbId3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_CreateDB((VOS_UINT8 *)g_dbName4, NULL, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_dbName4, &g_dbId4);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;  // 32条数据，约十六分之一的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1和DB2建表，唯一索引、非唯一索引和无索引
    for (int i = 0; i < 2; i++) {
        if (i == 0) {
            for (int j = 0; j < 3; j++) {
                ret = TestDB_CreateTbl(g_dbId3, filePath[j], &relId[j]);
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
                for (int k = 0; k < insertNum; k++) {
                    ret = DbInsertTbl64B(g_dbId3, relId[j], k);
                    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
                }
            }
        } else {
            for (int j = 0; j < 3; j++) {
                ret = TestDB_CreateTbl(g_dbId4, filePath[j], &relId[j + 3]);
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
                for (int k = 0; k < insertNum; k++) {
                    ret = DbInsertTbl64B(g_dbId4, relId[j + 3], k);
                    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
                }
            }
        }
    }

    // 修改tableA的表名为tableAA
    ret = DB_ModifyTblNameAndID(g_dbId3, relId[0], (VOS_UINT8 *)"tableAA", 0xffff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // clone，DB1中的tableA到DB2中
    uint16_t destRelID = 100;
    ret = DB_CloneTable(g_dbId3, relId[0], g_dbId4, destRelID);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验clone到DB2后的表数据
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId4, destRelID, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < insertNum; i++) {
        ret = DbSelectTbl64B(g_dbId4, destRelID, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 隔行删除
    for (int i = 0; i < insertNum; i = i + 2) {
        ret = DbDeleteTbl64B(g_dbId4, destRelID, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId4, destRelID, insertNum / 2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入先前删除的数据
    for (int i = 0; i < insertNum; i = i + 2) {
        ret = DbInsertTbl64B(g_dbId4, destRelID, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId4, destRelID, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 更新
    for (int i = 0; i < insertNum; i++) {
        ret = DbUpdateTbl64B(g_dbId4, destRelID, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId4, destRelID, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_CloseDB(g_dbId3);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_CloseDB(g_dbId4);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_dbName3, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_dbName4, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002 共享空间中持久化DBclose再open后校验数据
TEST_F(ShmSpDDL, V1Com_039_ShmSpDDL_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;  // 32条数据，约十六分之一的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // 持久化DB建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_CreateTbl(g_perDbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 写数据
    for (int j = 0; j < 3; j++) {
        for (uint32_t i = 0; i < insertNum; i++) {
            ret = InsertTbl64B(g_perDbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    // 查询
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_GetRelActRec(g_perDbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_perDbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }
    // close后open再次校验数据
    ret = TPC_CloseDB(g_perDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)g_perDbName, &g_perDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_GetRelActRec(g_perDbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_INVALIDREL, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_perDbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_INVALIDREL, ret);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003 共享空间中删表后校验DB内存
TEST_F(ShmSpDDL, V1Com_039_ShmSpDDL_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;  // 32条数据，约十六分之一的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // 持久化DB建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 写数据
    for (int j = 0; j < 3; j++) {
        for (int i = 0; i < insertNum; i++) {
            ret = InsertTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    // 查询
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    // TPC_Sysview查询和表明检验，预期表在共享空间 || 参数：dbid，表id，预期表在独立还是共享
    ret = TestSysviewAndCheckTbl(g_dbId, 0xFFFF,
        "index:0, table id: 0, table name: tableA, record num: 32, record len: 48, state: 0",
        "index:1, table id: 1, table name: tableB, record num: 32, record len: 48, state: 0",
        "index:2, table id: 2, table name: tableC, record num: 32, record len: 48, state: 0");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    for (int j = 0; j < 3; j++) {
        ret = TPC_DropTbl(g_dbId, relId[j], 0);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询DB数据内存
    DB_MEMUSAGE_INFO_STRU stMemInfo = {0};
    ret = DBDBG_GetDbMemUsageStats(g_dbId, &stMemInfo);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0, stMemInfo.ulDataSize);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004 创建表后查询是否使用共享空间
TEST_F(ShmSpDDL, V1Com_039_ShmSpDDL_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;  // 32条数据，约十六分之一的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // 持久化DB建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // TPC_Sysview查询和表明检验，预期表在共享空间 || 参数：dbid，表id，预期表在独立还是共享
    ret = TestSysviewAndCheckTbl(g_dbId, 0xFFFF,
        "index:0, table id: 0, table name: tableA, record num: 0, record len: 48, state: 0",
        "index:1, table id: 1, table name: tableB, record num: 0, record len: 48, state: 0",
        "index:2, table id: 2, table name: tableC, record num: 0, record len: 48, state: 0");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005 创建大对象表后查询是否使用共享空间
TEST_F(ShmSpDDL, V1Com_039_ShmSpDDL_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint16_t relId = 0;
    // 持久化DB建表，唯一索引、非唯一索引和无索引
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/tblBigObj.json", &relId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // TPC_Sysview查询和表明检验，预期表在独立空间
    ret = TestSysviewAndCheckTbl(
        g_dbId, relId, "common area details:table nums: 0, total data size: 0, total area size: 0");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006 修改表名表id后查询是否使用共享空间
TEST_F(ShmSpDDL, V1Com_039_ShmSpDDL_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;  // 32条数据，约十六分之一的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // 持久化DB建表，唯一索引、非唯一索引和无索引
    ret = TestTPC_CreateTbl(g_dbId, filePath[0], &relId[0]);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // TPC_Sysview查询和表明检验，预期表在共享空间
    ret = TestSysviewAndCheckTbl(
        g_dbId, relId[0], "index:0, table id: 0, table name: tableA, record num: 0, record len: 48, state: 0");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 修改表名和表id
    ret = TPC_ModifyTblNameAndID(g_dbId, relId[0], (VOS_UINT8 *)"tableAA", relId[0] + 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // TPC_Sysview查询和表明检验，预期表在共享空间
    ret = TestSysviewAndCheckTbl(
        g_dbId, relId[0] + 1, "index:0, table id: 1, table name: tableAA, record num: 0, record len: 48, state: 0");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007 创建多个DB，每个DB的表写入少量数据后查询是否在不同共享空间内
TEST_F(ShmSpDDL, V1Com_039_ShmSpDDL_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int cnt = 252;  // setUp创建了三个db，用户可以创建255个db
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;  // 32条数据，约十六分之一的数据
    uint16_t relId[cnt] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};

    char dbName[cnt] = {0};  // DB名字
    uint32_t dbId[cnt] = {0};
    DB_INST_CONFIG_STRU dbCfg = {0};  // DB的相关数据等配置
    for (uint32_t i = 0; i < cnt; i++) {
        (void)sprintf((char *)dbName, "dbName%d", i);
        ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &dbId[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        // 建表
        char tblName[cnt] = {0};
        (void)snprintf((char *)tblName, sizeof(tblName), "tblWithIndex%02d", i);
        ret = TestTPC_CreateTbl(dbId[i], filePath[0], &relId[i], NULL, tblName);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        // 写数据
        for (int j = 0; j < insertNum; j++) {
            ret = InsertTbl64B(dbId[i], relId[i], j, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        char cmd[1024];
        (void)sprintf_s(cmd, sizeof(cmd),
            "index:0, table id: %d, table name: %s, record num: 32, record len: 48, state: 0", relId[i], tblName);
        AW_FUN_Log(LOG_INFO, "string info:%s", cmd);
        // TPC_Sysview查询和表明检验，预期表在共享空间
        ret = TestSysviewAndCheckTbl(dbId[i], relId[i], cmd);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // close和drop
    for (int i = 0; i < cnt; i++) {
        (void)sprintf((char *)dbName, "dbName%d", i);
        ret = TPC_CloseDB(dbId[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TPC_DropDB((VOS_UINT8 *)dbName, 1);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008 对无索引的表使用GetRelAllRecords进行查询
TEST_F(ShmSpDDL, V1Com_039_ShmSpDDL_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint16_t pageNum = 16;
    uint16_t insertNum = REC_NUM_PER_PAGE / pageNum;  // 32条数据，约十六分之一的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_dbId, relId[2], &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen * insertNum);
    (void)memset_s(recBuf, tblRecLen * insertNum, 0x00, tblRecLen * insertNum);
    DB_DSBUF_STRU stBuff = {.usRecLen = (uint16_t)tblRecLen,
        .usRecNum = insertNum,
        .StdBuf = {.ulBufLen = tblRecLen * insertNum, .ulActLen = tblRecLen * insertNum, .pucData = recBuf}};

    ret = DBS_GetRelAllRecords(g_dbId, relId[2], &stBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(insertNum, stBuff.usRecNum);

    ret = TestTPC_GetRelActRec(g_dbId, relId[2], insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < insertNum; i++) {
        ret = SelectTbl64B(g_dbId, relId[2], i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 009 对无索引的表使用CountMatchingRecs进行查询
TEST_F(ShmSpDDL, V1Com_039_ShmSpDDL_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;  // 32条数据，约十六分之一的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    // 设置查询条件
    DB_COND_STRU pstCond = {.usCondNum = 1};
    uint32_t conVal = insertNum;
    // 查询字段F0小于insertNum的值
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_LESS, (VOS_UINT8 *)&conVal, sizeof(conVal));

    VOS_UINT32 pulRecNum = 0;
    ret = TPC_CountMatchingRecs(TPC_GLOBAL_CDB, g_dbId, relId[2], &pstCond, &pulRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(insertNum, pulRecNum);

    ret = TestTPC_GetRelActRec(g_dbId, relId[2], insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < insertNum; i++) {
        ret = SelectTbl64B(g_dbId, relId[2], i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010 对无索引的表使用FetchSelectRec进行查询
TEST_F(ShmSpDDL, V1Com_039_ShmSpDDL_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;  // 32条数据，约十六分之一的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    // 设置查询条件
    DB_COND_STRU pstCond = {.usCondNum = 1};
    uint32_t conVal = insertNum;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    // 查询字段F0小于insertNum的值
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_LESS, (VOS_UINT8 *)&conVal, sizeof(conVal));

    DB_SELHANDLE phSelect;
    ret = DB_BeginSelect(g_dbId, relId[2], &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_dbId, relId[2], &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_DSBUF_STRU pstDsBufGet;
    ret = TestMallocDsBuf(&pstDsBufGet, tblRecLen, insertNum, tblRecLen * insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FetchSelectRec(phSelect, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(insertNum, pstDsBufGet.usRecNum);
    TestFreeDsBuf(&pstDsBufGet);

    ret = DB_CloseAllHandles(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 011 对无索引的表使用RecordExist进行查询
TEST_F(ShmSpDDL, V1Com_039_ShmSpDDL_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;  // 32条数据，约十六分之一的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    // 设置查询条件
    DB_COND_STRU pstCond = {.usCondNum = 1};
    uint32_t conVal = insertNum;
    // 查询字段F0小于insertNum的值
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_LESS, (VOS_UINT8 *)&conVal, sizeof(conVal));
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_dbId, relId[2], &pstCond);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 012 对无索引的表使用SelectAllRec进行查询
TEST_F(ShmSpDDL, V1Com_039_ShmSpDDL_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;  // 32条数据，约十六分之一的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_dbId, relId[2], &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 设置查询条件
    DB_COND_STRU pstCond = {.usCondNum = 1};
    pstCond.usCondNum = 0;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_DSBUF_STRU pstDsBufGet;
    pstDsBufGet.usRecLen = tblRecLen;
    pstDsBufGet.usRecNum = insertNum;
    pstDsBufGet.StdBuf.ulBufLen = pstDsBufGet.usRecLen * pstDsBufGet.usRecNum;
    VOS_UINT8 *pucDataGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufGet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataGet);
    (void)memset_s(pucDataGet, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
    pstDsBufGet.StdBuf.pucData = pucDataGet;
    ret = TPC_SelectAllRec(TPC_GLOBAL_CDB, g_dbId, relId[2], &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(insertNum, pstDsBufGet.usRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(pstDsBufGet.StdBuf.ulBufLen, pstDsBufGet.StdBuf.ulActLen);
    TEST_V1_FREE(pucDataGet);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 013 对无索引的表使用SelectAllRecEx进行查询
TEST_F(ShmSpDDL, V1Com_039_ShmSpDDL_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;  // 32条数据，约十六分之一的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        if (j == 2) {
            ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            for (int i = 0; i < insertNum; i++) {
                ret = SelectTbl64B(g_dbId, relId[j], i, false);
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            }
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 014 对无索引的表使用SelectAllRec2进行查询
TEST_F(ShmSpDDL, V1Com_039_ShmSpDDL_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;  // 32条数据，约十六分之一的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_dbId, relId[2], &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 设置查询条件
    DB_COND_STRU pstCond = {.usCondNum = 1};
    uint32_t conVal = insertNum * 10;
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_LESS, (VOS_UINT8 *)&conVal, sizeof(conVal));

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_SORT_STRU pstSort = {.ucSortNum = 0, .aucReserve = {0}, .enSortType = DB_SORTTYPE_ASCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {0};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 获取查询计划
    DB_QUERY_PLAN_STRU pstQueryPlan;
    ret = TPC_PrepareQueryPlan(TPC_GLOBAL_CDB, g_dbId, relId[2], &pstCond, &pstQueryPlan);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 申请接口存放数据的buff
    DB_BUF_STRU pstBuf1;
    ret = TestMallocBuf(&pstBuf1, tblRecLen, insertNum, insertNum * tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    PvArgT pvArg = {.isAlreadyMalloc = true, .callCnt = 0, .bufAddr = &pstBuf1};

    // 申请回调使用的内存
    DB_BUF_STRU pstBuf2;
    ret = TestMallocBuf(&pstBuf2, tblRecLen, insertNum, insertNum * tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置参数
    DB_SELECT_STRU pstSelect = {.ulCdbId = TPC_GLOBAL_CDB,
        .ulDbId = g_dbId,
        .usRelNo = relId[2],
        .pstCond = &pstCond,
        .pstFldFilter = &pstFldFilter,
        .pstSort = &pstSort,
        .pstQueryPlan = &pstQueryPlan,
        .pfGetBuf = PfGetBufCallBack,
        .pvArg = (VOS_VOID *)&pvArg,
        .pstDataBuf = &pstBuf2};

    // 正常查询，保证参数正常
    ret = TPC_SelectAllRec2(&pstSelect);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放内存
    TestFreeBuf(&pstBuf1);
    TestFreeBuf(&pstBuf2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 015 对无索引的表使用SelectAllRecByOrder进行查询
TEST_F(ShmSpDDL, V1Com_039_ShmSpDDL_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;  // 32条数据，约十六分之一的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_dbId, relId[2], &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 设置查询条件
    DB_COND_STRU pstCond = {.usCondNum = 1};
    uint32_t conVal = insertNum;
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_LESS, (VOS_UINT8 *)&conVal, sizeof(conVal));
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    unsigned char sortFields[1] = {0};
    DB_SORT_STRU pstSort;
    pstSort.ucSortNum = 1;
    pstSort.enSortType = 1;
    pstSort.pSortFields = sortFields;

    DB_DSBUF_STRU pstDsBufGet;
    pstDsBufGet.usRecLen = tblRecLen;
    pstDsBufGet.usRecNum = insertNum;
    pstDsBufGet.StdBuf.ulBufLen = pstDsBufGet.usRecLen * pstDsBufGet.usRecNum;
    VOS_UINT8 *pucDataGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufGet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataGet);
    (void)memset_s(pucDataGet, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
    pstDsBufGet.StdBuf.pucData = pucDataGet;
    ret = TPC_SelectAllRecByOrder(TPC_GLOBAL_CDB, g_dbId, relId[2], &pstSort, &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(insertNum, pstDsBufGet.usRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(pstDsBufGet.StdBuf.ulBufLen, pstDsBufGet.StdBuf.ulActLen);
    TEST_V1_FREE(pucDataGet);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 016 对无索引的表使用SelectAllRecByOrderEx进行查询
TEST_F(ShmSpDDL, V1Com_039_ShmSpDDL_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;  // 32条数据，约十六分之一的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_dbId, relId[2], &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 设置查询条件
    DB_COND_STRU pstCond = {.usCondNum = 1};
    uint32_t conVal = insertNum;
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_LESS, (VOS_UINT8 *)&conVal, sizeof(conVal));
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    unsigned char sortFields[1] = {0};
    DB_SORT_STRU pstSort;
    pstSort.ucSortNum = 1;
    pstSort.enSortType = 1;
    pstSort.pSortFields = sortFields;

    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = tblRecLen;
    pstBufDataGet.ulRecNum = insertNum;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum + 1);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret =
        TPC_SelectAllRecByOrderEx(TPC_GLOBAL_CDB, g_dbId, relId[2], &pstSort, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(insertNum, pstBufDataGet.ulRecNum);
    TEST_V1_FREE(pBufGet);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 017 对无索引的表使用SelectFirstRec进行查询
TEST_F(ShmSpDDL, V1Com_039_ShmSpDDL_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;  // 32条数据，约十六分之一的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_dbId, relId[2], &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 设置查询条件
    DB_COND_STRU pstCond = {.usCondNum = 1};
    uint32_t conVal = insertNum;
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_LESS, (VOS_UINT8 *)&conVal, sizeof(conVal));
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_DSBUF_STRU pstDsBufGet;
    pstDsBufGet.usRecLen = tblRecLen;
    pstDsBufGet.usRecNum = insertNum;
    pstDsBufGet.StdBuf.ulBufLen = pstDsBufGet.usRecLen;
    VOS_UINT8 *pucDataGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufGet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataGet);
    (void)memset_s(pucDataGet, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
    pstDsBufGet.StdBuf.pucData = pucDataGet;
    ret = TPC_SelectFirstRec(TPC_GLOBAL_CDB, g_dbId, relId[2], &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(1, pstDsBufGet.usRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(pstDsBufGet.StdBuf.ulBufLen, pstDsBufGet.StdBuf.ulActLen);
    TEST_V1_FREE(pucDataGet);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 018 对无索引的表使用SimpleSelectAllRec进行查询
TEST_F(ShmSpDDL, V1Com_039_ShmSpDDL_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;  // 32条数据，约十六分之一的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_dbId, relId[2], &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 设置查询条件
    DB_COND_STRU pstCond = {.usCondNum = 1};
    uint32_t conVal = 0;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_EQUAL, (VOS_UINT8 *)&conVal, sizeof(conVal));

    DB_SORT_STRU pstSort;
    pstSort = {.ucSortNum = 0, .aucReserve = {0}, .enSortType = DB_SORTTYPE_ASCEND};

    DB_QUERY_PLAN_STRU pstQueryPlan;
    ret = TPC_PrepareQueryPlan(TPC_GLOBAL_CDB, g_dbId, relId[2], &pstCond, &pstQueryPlan);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 申请接口存放数据的buff
    DB_BUF_STRU pstBuf1;
    ret = TestMallocBuf(&pstBuf1, tblRecLen, insertNum, insertNum * tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    PvArgT pvArg = {.isAlreadyMalloc = true, .callCnt = 0, .bufAddr = &pstBuf1};

    // 申请回调使用的内存
    DB_BUF_STRU pstBuf2;
    ret = TestMallocBuf(&pstBuf2, tblRecLen, insertNum, insertNum * tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置参数
    DB_SELECT_STRU pstSelect = {.ulCdbId = TPC_GLOBAL_CDB,
        .ulDbId = g_dbId,
        .usRelNo = relId[2],
        .pstCond = &pstCond,
        .pstFldFilter = &pstFldFilter,
        .pstSort = &pstSort,
        .pstQueryPlan = &pstQueryPlan,
        .pfGetBuf = PfGetBufCallBack,
        .pvArg = (VOS_VOID *)&pvArg,
        .pstDataBuf = &pstBuf2};

    // 正常查询，保证参数正常
    ret = TPC_SimpleSelectAllRec(&pstSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(1, pstBuf2.ulRecNum);
    // 释放内存
    TestFreeBuf(&pstBuf1);
    TestFreeBuf(&pstBuf2);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 019 对无唯一索引的表使用GetRelAllRecords进行查询
TEST_F(ShmSpDDL, V1Com_039_ShmSpDDL_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint16_t pageNum = 16;
    uint16_t insertNum = REC_NUM_PER_PAGE / pageNum;  // 32条数据，约十六分之一的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_dbId, relId[1], &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen * insertNum);
    (void)memset_s(recBuf, tblRecLen * insertNum, 0x00, tblRecLen * insertNum);
    DB_DSBUF_STRU stBuff = {.usRecLen = (uint16_t)tblRecLen,
        .usRecNum = insertNum,
        .StdBuf = {.ulBufLen = tblRecLen * insertNum, .ulActLen = tblRecLen * insertNum, .pucData = recBuf}};

    ret = DBS_GetRelAllRecords(g_dbId, relId[1], &stBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(insertNum, stBuff.usRecNum);

    ret = TestTPC_GetRelActRec(g_dbId, relId[1], insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < insertNum; i++) {
        ret = SelectTbl64B(g_dbId, relId[1], i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 020 对无唯一索引的表使用CountMatchingRecs进行查询
TEST_F(ShmSpDDL, V1Com_039_ShmSpDDL_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;  // 32条数据，约十六分之一的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    // 设置查询条件
    DB_COND_STRU pstCond = {.usCondNum = 1};
    uint32_t conVal = insertNum;
    // 查询字段F0小于insertNum的值
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_LESS, (VOS_UINT8 *)&conVal, sizeof(conVal));

    VOS_UINT32 pulRecNum = 0;
    ret = TPC_CountMatchingRecs(TPC_GLOBAL_CDB, g_dbId, relId[1], &pstCond, &pulRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(insertNum, pulRecNum);

    ret = TestTPC_GetRelActRec(g_dbId, relId[1], insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < insertNum; i++) {
        ret = SelectTbl64B(g_dbId, relId[1], i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 021 对无唯一索引的表使用FetchSelectRec进行查询
TEST_F(ShmSpDDL, V1Com_039_ShmSpDDL_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;  // 32条数据，约十六分之一的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    // 设置查询条件
    DB_COND_STRU pstCond = {.usCondNum = 1};
    uint32_t conVal = insertNum;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    // 查询字段F0小于insertNum的值
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_LESS, (VOS_UINT8 *)&conVal, sizeof(conVal));

    DB_SELHANDLE phSelect;
    ret = DB_BeginSelect(g_dbId, relId[1], &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_dbId, relId[1], &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_DSBUF_STRU pstDsBufGet;
    ret = TestMallocDsBuf(&pstDsBufGet, tblRecLen, insertNum, tblRecLen * insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FetchSelectRec(phSelect, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(insertNum, pstDsBufGet.usRecNum);
    TestFreeDsBuf(&pstDsBufGet);

    ret = DB_CloseAllHandles(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 022 对无唯一索引的表使用RecordExist进行查询
TEST_F(ShmSpDDL, V1Com_039_ShmSpDDL_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;  // 32条数据，约十六分之一的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    // 设置查询条件
    DB_COND_STRU pstCond = {.usCondNum = 1};
    uint32_t conVal = insertNum;
    // 查询字段F0小于insertNum的值
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_LESS, (VOS_UINT8 *)&conVal, sizeof(conVal));
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_dbId, relId[1], &pstCond);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 023 对无唯一索引的表使用SelectAllRec进行查询
TEST_F(ShmSpDDL, V1Com_039_ShmSpDDL_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;  // 32条数据，约十六分之一的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_dbId, relId[1], &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 设置查询条件
    DB_COND_STRU pstCond = {.usCondNum = 1};
    pstCond.usCondNum = 0;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_DSBUF_STRU pstDsBufGet;
    pstDsBufGet.usRecLen = tblRecLen;
    pstDsBufGet.usRecNum = insertNum;
    pstDsBufGet.StdBuf.ulBufLen = pstDsBufGet.usRecLen * pstDsBufGet.usRecNum;
    VOS_UINT8 *pucDataGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufGet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataGet);
    (void)memset_s(pucDataGet, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
    pstDsBufGet.StdBuf.pucData = pucDataGet;
    ret = TPC_SelectAllRec(TPC_GLOBAL_CDB, g_dbId, relId[1], &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(insertNum, pstDsBufGet.usRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(pstDsBufGet.StdBuf.ulBufLen, pstDsBufGet.StdBuf.ulActLen);
    TEST_V1_FREE(pucDataGet);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 024 对无唯一索引的表使用SelectAllRecEx进行查询
TEST_F(ShmSpDDL, V1Com_039_ShmSpDDL_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;  // 32条数据，约十六分之一的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        if (j == 2) {
            ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            for (int i = 0; i < insertNum; i++) {
                ret = SelectTbl64B(g_dbId, relId[j], i, false);
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            }
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 025 对无唯一索引的表使用SelectAllRec2进行查询
TEST_F(ShmSpDDL, V1Com_039_ShmSpDDL_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;  // 32条数据，约十六分之一的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_dbId, relId[1], &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 设置查询条件
    DB_COND_STRU pstCond = {.usCondNum = 1};
    uint32_t conVal = insertNum * 10;
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_LESS, (VOS_UINT8 *)&conVal, sizeof(conVal));

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_SORT_STRU pstSort = {.ucSortNum = 0, .aucReserve = {0}, .enSortType = DB_SORTTYPE_ASCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {0};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 获取查询计划
    DB_QUERY_PLAN_STRU pstQueryPlan;
    ret = TPC_PrepareQueryPlan(TPC_GLOBAL_CDB, g_dbId, relId[1], &pstCond, &pstQueryPlan);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 申请接口存放数据的buff
    DB_BUF_STRU pstBuf1;
    ret = TestMallocBuf(&pstBuf1, tblRecLen, insertNum, insertNum * tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    PvArgT pvArg = {.isAlreadyMalloc = true, .callCnt = 0, .bufAddr = &pstBuf1};

    // 申请回调使用的内存
    DB_BUF_STRU pstBuf2;
    ret = TestMallocBuf(&pstBuf2, tblRecLen, insertNum, insertNum * tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置参数
    DB_SELECT_STRU pstSelect = {.ulCdbId = TPC_GLOBAL_CDB,
        .ulDbId = g_dbId,
        .usRelNo = relId[1],
        .pstCond = &pstCond,
        .pstFldFilter = &pstFldFilter,
        .pstSort = &pstSort,
        .pstQueryPlan = &pstQueryPlan,
        .pfGetBuf = PfGetBufCallBack,
        .pvArg = (VOS_VOID *)&pvArg,
        .pstDataBuf = &pstBuf2};

    // 正常查询，保证参数正常
    ret = TPC_SelectAllRec2(&pstSelect);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放内存
    TestFreeBuf(&pstBuf1);
    TestFreeBuf(&pstBuf2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 026 对无唯一索引的表使用SelectAllRecByOrder进行查询
TEST_F(ShmSpDDL, V1Com_039_ShmSpDDL_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;  // 32条数据，约十六分之一的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_dbId, relId[1], &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 设置查询条件
    DB_COND_STRU pstCond = {.usCondNum = 1};
    uint32_t conVal = insertNum;
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_LESS, (VOS_UINT8 *)&conVal, sizeof(conVal));
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    unsigned char sortFields[1] = {0};
    DB_SORT_STRU pstSort;
    pstSort.ucSortNum = 1;
    pstSort.enSortType = 1;
    pstSort.pSortFields = sortFields;

    DB_DSBUF_STRU pstDsBufGet;
    pstDsBufGet.usRecLen = tblRecLen;
    pstDsBufGet.usRecNum = insertNum;
    pstDsBufGet.StdBuf.ulBufLen = pstDsBufGet.usRecLen * pstDsBufGet.usRecNum;
    VOS_UINT8 *pucDataGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufGet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataGet);
    (void)memset_s(pucDataGet, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
    pstDsBufGet.StdBuf.pucData = pucDataGet;
    ret = TPC_SelectAllRecByOrder(TPC_GLOBAL_CDB, g_dbId, relId[1], &pstSort, &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(insertNum, pstDsBufGet.usRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(pstDsBufGet.StdBuf.ulBufLen, pstDsBufGet.StdBuf.ulActLen);
    TEST_V1_FREE(pucDataGet);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 027 对无唯一索引的表使用SelectAllRecByOrderEx进行查询
TEST_F(ShmSpDDL, V1Com_039_ShmSpDDL_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;  // 32条数据，约十六分之一的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_dbId, relId[1], &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 设置查询条件
    DB_COND_STRU pstCond = {.usCondNum = 1};
    uint32_t conVal = insertNum;
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_LESS, (VOS_UINT8 *)&conVal, sizeof(conVal));
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    unsigned char sortFields[1] = {0};
    DB_SORT_STRU pstSort;
    pstSort.ucSortNum = 1;
    pstSort.enSortType = 1;
    pstSort.pSortFields = sortFields;

    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = tblRecLen;
    pstBufDataGet.ulRecNum = insertNum;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum + 1);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret =
        TPC_SelectAllRecByOrderEx(TPC_GLOBAL_CDB, g_dbId, relId[1], &pstSort, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(insertNum, pstBufDataGet.ulRecNum);
    TEST_V1_FREE(pBufGet);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 028 对无唯一索引的表使用SelectFirstRec进行查询
TEST_F(ShmSpDDL, V1Com_039_ShmSpDDL_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;  // 32条数据，约十六分之一的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_dbId, relId[1], &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 设置查询条件
    DB_COND_STRU pstCond = {.usCondNum = 1};
    uint32_t conVal = insertNum;
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_LESS, (VOS_UINT8 *)&conVal, sizeof(conVal));
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_DSBUF_STRU pstDsBufGet;
    pstDsBufGet.usRecLen = tblRecLen;
    pstDsBufGet.usRecNum = insertNum;
    pstDsBufGet.StdBuf.ulBufLen = pstDsBufGet.usRecLen;
    VOS_UINT8 *pucDataGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufGet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataGet);
    (void)memset_s(pucDataGet, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
    pstDsBufGet.StdBuf.pucData = pucDataGet;
    ret = TPC_SelectFirstRec(TPC_GLOBAL_CDB, g_dbId, relId[1], &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(1, pstDsBufGet.usRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(pstDsBufGet.StdBuf.ulBufLen, pstDsBufGet.StdBuf.ulActLen);
    TEST_V1_FREE(pucDataGet);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 029 对无唯一索引的表使用SimpleSelectAllRec进行查询
TEST_F(ShmSpDDL, V1Com_039_ShmSpDDL_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;  // 32条数据，约十六分之一的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_dbId, relId[1], &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 设置查询条件
    DB_COND_STRU pstCond = {.usCondNum = 1};
    uint32_t conVal = 0;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_EQUAL, (VOS_UINT8 *)&conVal, sizeof(conVal));

    DB_SORT_STRU pstSort;
    pstSort = {.ucSortNum = 0, .aucReserve = {0}, .enSortType = DB_SORTTYPE_ASCEND};

    DB_QUERY_PLAN_STRU pstQueryPlan;
    ret = TPC_PrepareQueryPlan(TPC_GLOBAL_CDB, g_dbId, relId[1], &pstCond, &pstQueryPlan);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 申请接口存放数据的buff
    DB_BUF_STRU pstBuf1;
    ret = TestMallocBuf(&pstBuf1, tblRecLen, insertNum, insertNum * tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    PvArgT pvArg = {.isAlreadyMalloc = true, .callCnt = 0, .bufAddr = &pstBuf1};

    // 申请回调使用的内存
    DB_BUF_STRU pstBuf2;
    ret = TestMallocBuf(&pstBuf2, tblRecLen, insertNum, insertNum * tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置参数
    DB_SELECT_STRU pstSelect = {.ulCdbId = TPC_GLOBAL_CDB,
        .ulDbId = g_dbId,
        .usRelNo = relId[1],
        .pstCond = &pstCond,
        .pstFldFilter = &pstFldFilter,
        .pstSort = &pstSort,
        .pstQueryPlan = &pstQueryPlan,
        .pfGetBuf = PfGetBufCallBack,
        .pvArg = (VOS_VOID *)&pvArg,
        .pstDataBuf = &pstBuf2};

    // 正常查询，保证参数正常
    ret = TPC_SimpleSelectAllRec(&pstSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(1, pstBuf2.ulRecNum);
    // 释放内存
    TestFreeBuf(&pstBuf1);
    TestFreeBuf(&pstBuf2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 030 对唯一索引的表使用GetRelAllRecords进行查询
TEST_F(ShmSpDDL, V1Com_039_ShmSpDDL_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint16_t pageNum = 16;
    uint16_t insertNum = REC_NUM_PER_PAGE / pageNum;  // 32条数据，约十六分之一的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_dbId, relId[0], &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen * insertNum);
    (void)memset_s(recBuf, tblRecLen * insertNum, 0x00, tblRecLen * insertNum);
    DB_DSBUF_STRU stBuff = {.usRecLen = (uint16_t)tblRecLen,
        .usRecNum = insertNum,
        .StdBuf = {.ulBufLen = tblRecLen * insertNum, .ulActLen = tblRecLen * insertNum, .pucData = recBuf}};

    ret = DBS_GetRelAllRecords(g_dbId, relId[0], &stBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(insertNum, stBuff.usRecNum);

    ret = TestTPC_GetRelActRec(g_dbId, relId[0], insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < insertNum; i++) {
        ret = SelectTbl64B(g_dbId, relId[0], i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TEST_V1_FREE(recBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 031 对唯一索引的表使用CountMatchingRecs进行查询
TEST_F(ShmSpDDL, V1Com_039_ShmSpDDL_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;  // 32条数据，约十六分之一的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    // 设置查询条件
    DB_COND_STRU pstCond = {.usCondNum = 1};
    uint32_t conVal = insertNum;
    // 查询字段F0小于insertNum的值
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_LESS, (VOS_UINT8 *)&conVal, sizeof(conVal));

    VOS_UINT32 pulRecNum = 0;
    ret = TPC_CountMatchingRecs(TPC_GLOBAL_CDB, g_dbId, relId[0], &pstCond, &pulRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(insertNum, pulRecNum);

    ret = TestTPC_GetRelActRec(g_dbId, relId[0], insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < insertNum; i++) {
        ret = SelectTbl64B(g_dbId, relId[0], i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 032 对唯一索引的表使用FetchSelectRec进行查询
TEST_F(ShmSpDDL, V1Com_039_ShmSpDDL_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;  // 32条数据，约十六分之一的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    // 设置查询条件
    DB_COND_STRU pstCond = {.usCondNum = 1};
    uint32_t conVal = insertNum;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    // 查询字段F0小于insertNum的值
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_LESS, (VOS_UINT8 *)&conVal, sizeof(conVal));

    DB_SELHANDLE phSelect;
    ret = DB_BeginSelect(g_dbId, relId[0], &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_dbId, relId[0], &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_DSBUF_STRU pstDsBufGet;
    ret = TestMallocDsBuf(&pstDsBufGet, tblRecLen, insertNum, tblRecLen * insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FetchSelectRec(phSelect, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(insertNum, pstDsBufGet.usRecNum);
    TestFreeDsBuf(&pstDsBufGet);

    ret = DB_CloseAllHandles(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 033 对唯一索引的表使用RecordExist进行查询
TEST_F(ShmSpDDL, V1Com_039_ShmSpDDL_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;  // 32条数据，约十六分之一的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    // 设置查询条件
    DB_COND_STRU pstCond = {.usCondNum = 1};
    uint32_t conVal = insertNum;
    // 查询字段F0小于insertNum的值
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_LESS, (VOS_UINT8 *)&conVal, sizeof(conVal));
    ret = TPC_RecordExist(TPC_GLOBAL_CDB, g_dbId, relId[0], &pstCond);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 034 对唯一索引的表使用SelectAllRec进行查询
TEST_F(ShmSpDDL, V1Com_039_ShmSpDDL_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;  // 32条数据，约十六分之一的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_dbId, relId[0], &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 设置查询条件
    DB_COND_STRU pstCond = {.usCondNum = 1};
    pstCond.usCondNum = 0;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_DSBUF_STRU pstDsBufGet;
    pstDsBufGet.usRecLen = tblRecLen;
    pstDsBufGet.usRecNum = insertNum;
    pstDsBufGet.StdBuf.ulBufLen = pstDsBufGet.usRecLen * pstDsBufGet.usRecNum;
    VOS_UINT8 *pucDataGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufGet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataGet);
    (void)memset_s(pucDataGet, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
    pstDsBufGet.StdBuf.pucData = pucDataGet;
    ret = TPC_SelectAllRec(TPC_GLOBAL_CDB, g_dbId, relId[0], &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(insertNum, pstDsBufGet.usRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(pstDsBufGet.StdBuf.ulBufLen, pstDsBufGet.StdBuf.ulActLen);
    TEST_V1_FREE(pucDataGet);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 035 对唯一索引的表使用SelectAllRecEx进行查询
TEST_F(ShmSpDDL, V1Com_039_ShmSpDDL_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;  // 32条数据，约十六分之一的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        if (j == 2) {
            ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            for (int i = 0; i < insertNum; i++) {
                ret = SelectTbl64B(g_dbId, relId[j], i, false);
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            }
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 036 对唯一索引的表使用SelectAllRec2进行查询
TEST_F(ShmSpDDL, V1Com_039_ShmSpDDL_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;  // 32条数据，约十六分之一的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_dbId, relId[0], &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 设置查询条件
    DB_COND_STRU pstCond = {.usCondNum = 1};
    uint32_t conVal = insertNum * 10;
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_LESS, (VOS_UINT8 *)&conVal, sizeof(conVal));

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_SORT_STRU pstSort = {.ucSortNum = 0, .aucReserve = {0}, .enSortType = DB_SORTTYPE_ASCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {0};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 获取查询计划
    DB_QUERY_PLAN_STRU pstQueryPlan;
    ret = TPC_PrepareQueryPlan(TPC_GLOBAL_CDB, g_dbId, relId[0], &pstCond, &pstQueryPlan);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 申请接口存放数据的buff
    DB_BUF_STRU pstBuf1;
    ret = TestMallocBuf(&pstBuf1, tblRecLen, insertNum, insertNum * tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    PvArgT pvArg = {.isAlreadyMalloc = true, .callCnt = 0, .bufAddr = &pstBuf1};

    // 申请回调使用的内存
    DB_BUF_STRU pstBuf2;
    ret = TestMallocBuf(&pstBuf2, tblRecLen, insertNum, insertNum * tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置参数
    DB_SELECT_STRU pstSelect = {.ulCdbId = TPC_GLOBAL_CDB,
        .ulDbId = g_dbId,
        .usRelNo = relId[0],
        .pstCond = &pstCond,
        .pstFldFilter = &pstFldFilter,
        .pstSort = &pstSort,
        .pstQueryPlan = &pstQueryPlan,
        .pfGetBuf = PfGetBufCallBack,
        .pvArg = (VOS_VOID *)&pvArg,
        .pstDataBuf = &pstBuf2};

    // 正常查询，保证参数正常
    ret = TPC_SelectAllRec2(&pstSelect);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放内存
    TestFreeBuf(&pstBuf1);
    TestFreeBuf(&pstBuf2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 037 对唯一索引的表使用SelectAllRecByOrder进行查询
TEST_F(ShmSpDDL, V1Com_039_ShmSpDDL_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;  // 32条数据，约十六分之一的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_dbId, relId[0], &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 设置查询条件
    DB_COND_STRU pstCond = {.usCondNum = 1};
    uint32_t conVal = insertNum;
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_LESS, (VOS_UINT8 *)&conVal, sizeof(conVal));
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    unsigned char sortFields[1] = {0};
    DB_SORT_STRU pstSort;
    pstSort.ucSortNum = 1;
    pstSort.enSortType = 1;
    pstSort.pSortFields = sortFields;

    DB_DSBUF_STRU pstDsBufGet;
    pstDsBufGet.usRecLen = tblRecLen;
    pstDsBufGet.usRecNum = insertNum;
    pstDsBufGet.StdBuf.ulBufLen = pstDsBufGet.usRecLen * pstDsBufGet.usRecNum;
    VOS_UINT8 *pucDataGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufGet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataGet);
    (void)memset_s(pucDataGet, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
    pstDsBufGet.StdBuf.pucData = pucDataGet;
    ret = TPC_SelectAllRecByOrder(TPC_GLOBAL_CDB, g_dbId, relId[0], &pstSort, &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(insertNum, pstDsBufGet.usRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(pstDsBufGet.StdBuf.ulBufLen, pstDsBufGet.StdBuf.ulActLen);
    TEST_V1_FREE(pucDataGet);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 038 对唯一索引的表使用SelectAllRecByOrderEx进行查询
TEST_F(ShmSpDDL, V1Com_039_ShmSpDDL_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;  // 32条数据，约十六分之一的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_dbId, relId[0], &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 设置查询条件
    DB_COND_STRU pstCond = {.usCondNum = 1};
    uint32_t conVal = insertNum;
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_LESS, (VOS_UINT8 *)&conVal, sizeof(conVal));
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    unsigned char sortFields[1] = {0};
    DB_SORT_STRU pstSort;
    pstSort.ucSortNum = 1;
    pstSort.enSortType = 1;
    pstSort.pSortFields = sortFields;

    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = tblRecLen;
    pstBufDataGet.ulRecNum = insertNum;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum + 1);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret =
        TPC_SelectAllRecByOrderEx(TPC_GLOBAL_CDB, g_dbId, relId[0], &pstSort, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(insertNum, pstBufDataGet.ulRecNum);
    TEST_V1_FREE(pBufGet);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 039 对唯一索引的表使用SelectFirstRec进行查询
TEST_F(ShmSpDDL, V1Com_039_ShmSpDDL_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;  // 32条数据，约十六分之一的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_dbId, relId[0], &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 设置查询条件
    DB_COND_STRU pstCond = {.usCondNum = 1};
    uint32_t conVal = insertNum;
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_LESS, (VOS_UINT8 *)&conVal, sizeof(conVal));
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_DSBUF_STRU pstDsBufGet;
    pstDsBufGet.usRecLen = tblRecLen;
    pstDsBufGet.usRecNum = insertNum;
    pstDsBufGet.StdBuf.ulBufLen = pstDsBufGet.usRecLen;
    VOS_UINT8 *pucDataGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufGet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataGet);
    (void)memset_s(pucDataGet, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
    pstDsBufGet.StdBuf.pucData = pucDataGet;
    ret = TPC_SelectFirstRec(TPC_GLOBAL_CDB, g_dbId, relId[0], &pstCond, &pstFldFilter, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(1, pstDsBufGet.usRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(pstDsBufGet.StdBuf.ulBufLen, pstDsBufGet.StdBuf.ulActLen);
    TEST_V1_FREE(pucDataGet);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 040 对唯一索引的表使用SimpleSelectAllRec进行查询
TEST_F(ShmSpDDL, V1Com_039_ShmSpDDL_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;  // 32条数据，约十六分之一的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_dbId, relId[0], &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 设置查询条件
    DB_COND_STRU pstCond = {.usCondNum = 1};
    uint32_t conVal = 0;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_EQUAL, (VOS_UINT8 *)&conVal, sizeof(conVal));

    DB_SORT_STRU pstSort;
    pstSort = {.ucSortNum = 0, .aucReserve = {0}, .enSortType = DB_SORTTYPE_ASCEND};

    DB_QUERY_PLAN_STRU pstQueryPlan;
    ret = TPC_PrepareQueryPlan(TPC_GLOBAL_CDB, g_dbId, relId[0], &pstCond, &pstQueryPlan);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 申请接口存放数据的buff
    DB_BUF_STRU pstBuf1;
    ret = TestMallocBuf(&pstBuf1, tblRecLen, insertNum, insertNum * tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    PvArgT pvArg = {.isAlreadyMalloc = true, .callCnt = 0, .bufAddr = &pstBuf1};

    // 申请回调使用的内存
    DB_BUF_STRU pstBuf2;
    ret = TestMallocBuf(&pstBuf2, tblRecLen, insertNum, insertNum * tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置参数
    DB_SELECT_STRU pstSelect = {.ulCdbId = TPC_GLOBAL_CDB,
        .ulDbId = g_dbId,
        .usRelNo = relId[0],
        .pstCond = &pstCond,
        .pstFldFilter = &pstFldFilter,
        .pstSort = &pstSort,
        .pstQueryPlan = &pstQueryPlan,
        .pfGetBuf = PfGetBufCallBack,
        .pvArg = (VOS_VOID *)&pvArg,
        .pstDataBuf = &pstBuf2};

    // 正常查询，保证参数正常
    ret = TPC_SimpleSelectAllRec(&pstSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(1, pstBuf2.ulRecNum);
    // 释放内存
    TestFreeBuf(&pstBuf1);
    TestFreeBuf(&pstBuf2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 041 对所有表数据都存储在共享空间的图进行全路径查询
TEST_F(ShmSpDDL, V1Com_039_ShmSpDDL_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE - (REC_NUM_PER_PAGE / pageNum);  // 480条数据，约十六分之十五个人页的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    ret = TestCreateEdge(g_dbId, relId[0], relId[1]);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int j = 0; j < 3; j++) {
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    // 全路径查询
    ret = SelectEdge(g_dbId, relId[0], relId[1]);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 042 对所有表数据都存储在共享空间的图进行单跳查询
TEST_F(ShmSpDDL, V1Com_039_ShmSpDDL_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE - (REC_NUM_PER_PAGE / pageNum);  // 480条数据，约十六分之十五个人页的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    ret = TestCreateEdge(g_dbId, relId[0], relId[1]);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int j = 0; j < 3; j++) {
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    // 使用单跳查询
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 0;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_SELHANDLE phSelect;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    ret = TPC_BeginSelectByTopo(TPC_GLOBAL_CDB, g_dbId, relId[0], &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 fetchCount = insertNum / 2;
    // 查询表1的值
    ret = TPC_FetchSelectTopoRec(TPC_GLOBAL_CDB, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 从表1跳表2
    uint8_t field1[1] = {11};
    uint8_t field2[1] = {11};
    DB_EDGE_CON_STRU nextedge;
    SetPathInfoToSingle(&nextedge, relId[0], relId[1], &pstFldFilter, 1, field1, field2);
    ret = TPC_MoveNextByEdge(phSelect, nextedge);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_FetchSelectTopoRec(TPC_GLOBAL_CDB, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 调用接口DB_GetOpenHandleInfo进行查询
    DB_HANDLECTRL_INFO_STRU HandleCtrlInfo = {0};
    HandleCtrlInfo.ulHndlCnt = 1;
    HandleCtrlInfo.pstHndlInfo = (DB_HANDLEINFO_STRU *)TEST_V1_MALLOC(sizeof(DB_HANDLEINFO_STRU));
    ret = DB_GetOpenHandleInfo(g_dbId, &HandleCtrlInfo);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(1, HandleCtrlInfo.ulHndlCnt);
    V1_AW_MACRO_EXPECT_EQ_INT(phSelect, HandleCtrlInfo.pstHndlInfo[0].ulHndlId);
    V1_AW_MACRO_EXPECT_EQ_INT(TPC_GLOBAL_CDB, HandleCtrlInfo.pstHndlInfo[0].ulCDBId);
    V1_AW_MACRO_EXPECT_EQ_INT(relId[0], HandleCtrlInfo.pstHndlInfo[0].ulTableId);
    // 结束查询
    TEST_V1_FREE(HandleCtrlInfo.pstHndlInfo);
    TPC_FreeBufMemById(pstBuff->memId);
    ret = TPC_EndTopoSelect(TPC_GLOBAL_CDB, phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 043 对部分表数据存储在共享空间的图进行全路径查询
TEST_F(ShmSpDDL, V1Com_039_ShmSpDDL_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE - (REC_NUM_PER_PAGE / pageNum);  // 480条数据，约十六分之十五个人页的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    ret = TestCreateEdge(g_dbId, relId[0], relId[1]);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int j = 0; j < 3; j++) {
        if (j == 2) {
            insertNum = REC_NUM_PER_PAGE / pageNum;
        }
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    // 全路径查询
    ret = SelectEdge(g_dbId, relId[0], relId[1]);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 044 对部分表数据存储在共享空间的图进行单跳查询
TEST_F(ShmSpDDL, V1Com_039_ShmSpDDL_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE - (REC_NUM_PER_PAGE / pageNum);  // 480条数据，约十六分之十五个人页的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    ret = TestCreateEdge(g_dbId, relId[0], relId[1]);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int j = 0; j < 3; j++) {
        if (j == 2) {
            insertNum = REC_NUM_PER_PAGE / pageNum;
        }
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    // 使用单跳查询
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 0;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_SELHANDLE phSelect;
    DB_MUTIL_BUF_STRU *pstBuff = NULL;

    ret = TPC_BeginSelectByTopo(TPC_GLOBAL_CDB, g_dbId, relId[0], &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 fetchCount = insertNum / 2;
    // 查询表1的值
    ret = TPC_FetchSelectTopoRec(TPC_GLOBAL_CDB, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 从表1跳表2
    uint8_t field1[1] = {11};
    uint8_t field2[1] = {11};
    DB_EDGE_CON_STRU nextedge;
    SetPathInfoToSingle(&nextedge, relId[0], relId[1], &pstFldFilter, 1, field1, field2);
    ret = TPC_MoveNextByEdge(phSelect, nextedge);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_FetchSelectTopoRec(TPC_GLOBAL_CDB, phSelect, fetchCount, &pstBuff);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 调用接口DB_GetOpenHandleInfo进行查询
    DB_HANDLECTRL_INFO_STRU HandleCtrlInfo = {0};
    HandleCtrlInfo.ulHndlCnt = 1;
    HandleCtrlInfo.pstHndlInfo = (DB_HANDLEINFO_STRU *)TEST_V1_MALLOC(sizeof(DB_HANDLEINFO_STRU));
    ret = DB_GetOpenHandleInfo(g_dbId, &HandleCtrlInfo);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(1, HandleCtrlInfo.ulHndlCnt);
    V1_AW_MACRO_EXPECT_EQ_INT(phSelect, HandleCtrlInfo.pstHndlInfo[0].ulHndlId);
    V1_AW_MACRO_EXPECT_EQ_INT(TPC_GLOBAL_CDB, HandleCtrlInfo.pstHndlInfo[0].ulCDBId);
    V1_AW_MACRO_EXPECT_EQ_INT(relId[0], HandleCtrlInfo.pstHndlInfo[0].ulTableId);
    // 结束查询
    TEST_V1_FREE(HandleCtrlInfo.pstHndlInfo);
    TPC_FreeBufMemById(pstBuff->memId);
    ret = TPC_EndTopoSelect(TPC_GLOBAL_CDB, phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 045 开启查询句柄后，删除数据至低于阈值后进行fetch数据
TEST_F(ShmSpDDL, V1Com_039_ShmSpDDL_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE - (REC_NUM_PER_PAGE / pageNum);  // 480条数据，约十六分之十五个页的数据
    int deleteNum = REC_NUM_PER_PAGE / pageNum;                       // 32条数据，十六分之一个页的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {  // 表1、表2写入数据 0-479；表3写入数据 0-31
        if (j == 2) {
            insertNum = REC_NUM_PER_PAGE / pageNum;
        }
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    insertNum = REC_NUM_PER_PAGE - (REC_NUM_PER_PAGE / pageNum);
    // 设置查询条件
    DB_COND_STRU pstCond = {.usCondNum = 1};
    uint32_t conVal = deleteNum;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    // 查询字段F0小于insertNum的值
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_LESS, (VOS_UINT8 *)&conVal, sizeof(conVal));

    DB_SELHANDLE phSelect;
    ret = DB_BeginSelect(g_dbId, relId[0], &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = deleteNum; i < insertNum; i++) {  // 表1 删除 32-479，小于1/8个页
        ret = DeleteTbl64B(g_dbId, relId[0], i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_dbId, relId[0], &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_DSBUF_STRU pstDsBufGet;
    ret = TestMallocDsBuf(&pstDsBufGet, tblRecLen, deleteNum, tblRecLen * deleteNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FetchSelectRec(phSelect, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(deleteNum, pstDsBufGet.usRecNum);
    TestFreeDsBuf(&pstDsBufGet);

    ret = DB_CloseAllHandles(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < deleteNum; i++) {
        ret = SelectTbl64B(g_dbId, relId[0], i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 046 开启查询句柄后，插入数据至高于阈值后进行fetch数据
TEST_F(ShmSpDDL, V1Com_039_ShmSpDDL_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;  // 480条数据，约十六分之十五个页的数据
    int addNum = REC_NUM_PER_PAGE - (REC_NUM_PER_PAGE / pageNum);  // 32条数据，十六分之一个页的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {  // 表1、表2写入数据 0-31；表3写入数据 0-479
        if (j == 2) {
            insertNum = REC_NUM_PER_PAGE - (REC_NUM_PER_PAGE / pageNum);
        }
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    insertNum = REC_NUM_PER_PAGE / pageNum;
    // 设置查询条件
    DB_COND_STRU pstCond = {.usCondNum = 1};
    uint32_t conVal = addNum;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    // 查询字段F0小于insertNum的值
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_LESS, (VOS_UINT8 *)&conVal, sizeof(conVal));

    DB_SELHANDLE phSelect;
    ret = DB_BeginSelect(g_dbId, relId[0], &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int k = insertNum; k < addNum; k++) {  // 表1再次写入数据 32-479，大于7/8个页
        ret = InsertTbl64B(g_dbId, relId[0], k, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_dbId, relId[0], &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_DSBUF_STRU pstDsBufGet;
    ret = TestMallocDsBuf(&pstDsBufGet, tblRecLen, addNum, tblRecLen * addNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FetchSelectRec(phSelect, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(addNum, pstDsBufGet.usRecNum);
    TestFreeDsBuf(&pstDsBufGet);

    ret = DB_CloseAllHandles(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int i = 0; i < addNum; i++) {
        ret = SelectTbl64B(g_dbId, relId[0], i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 047 开启查询句柄后，在共享空间内增删改查后进行fetch数据
TEST_F(ShmSpDDL, V1Com_039_ShmSpDDL_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;  // 480条数据，约十六分之十五个页的数据
    int addNum = REC_NUM_PER_PAGE - (REC_NUM_PER_PAGE / pageNum);  // 32条数据，十六分之一个页的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {  // 表1、表2写入数据 0-31；表3写入数据 0-479
        if (j == 2) {
            insertNum = REC_NUM_PER_PAGE - (REC_NUM_PER_PAGE / pageNum);
        }
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    insertNum = REC_NUM_PER_PAGE / pageNum;
    // 设置查询条件
    DB_COND_STRU pstCond = {.usCondNum = 1};
    uint32_t conVal = insertNum;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    // 查询字段F0小于insertNum的值
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_LESS, (VOS_UINT8 *)&conVal, sizeof(conVal));

    DB_SELHANDLE phSelect;
    ret = DB_BeginSelect(g_dbId, relId[0], &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 更新
    for (int i = 0; i < insertNum; i++) {
        ret = UpdateTbl64B(g_dbId, relId[0], i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId[0], insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除
    for (int i = 0; i < insertNum; i++) {
        ret = DeleteTbl64B(g_dbId, relId[0], i + 1000, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 写入
    for (int k = 0; k < insertNum; k++) {
        ret = InsertTbl64B(g_dbId, relId[0], k, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_dbId, relId[0], &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_DSBUF_STRU pstDsBufGet;
    ret = TestMallocDsBuf(&pstDsBufGet, tblRecLen, insertNum, tblRecLen * insertNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_FetchSelectRec(phSelect, &pstDsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(insertNum, pstDsBufGet.usRecNum);

    for (int i = 0; i < insertNum; i++) {
        ret = SelectTbl64B(g_dbId, relId[0], i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    TestFreeDsBuf(&pstDsBufGet);
    ret = DB_CloseAllHandles(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 048 开启查询句柄后，删除数据至低于阈值后进行SelectAllRec2数据
TEST_F(ShmSpDDL, V1Com_039_ShmSpDDL_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE - (REC_NUM_PER_PAGE / pageNum);  // 32条数据，约十六分之一的数据
    int deleteNum = REC_NUM_PER_PAGE / pageNum;
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        if (j == 2) {
            insertNum = REC_NUM_PER_PAGE / pageNum;
        }
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_dbId, relId[0], &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 设置查询条件
    insertNum = REC_NUM_PER_PAGE - (REC_NUM_PER_PAGE / pageNum);
    DB_COND_STRU pstCond = {.usCondNum = 1};
    uint32_t conVal = deleteNum;
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_LESS, (VOS_UINT8 *)&conVal, sizeof(conVal));

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_SORT_STRU pstSort = {.ucSortNum = 0, .aucReserve = {0}, .enSortType = DB_SORTTYPE_ASCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {0};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 获取查询计划
    DB_QUERY_PLAN_STRU pstQueryPlan;
    ret = TPC_PrepareQueryPlan(TPC_GLOBAL_CDB, g_dbId, relId[0], &pstCond, &pstQueryPlan);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 申请接口存放数据的buff
    DB_BUF_STRU pstBuf1;
    ret = TestMallocBuf(&pstBuf1, tblRecLen, deleteNum, deleteNum * tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    PvArgT pvArg = {.isAlreadyMalloc = true, .callCnt = 0, .bufAddr = &pstBuf1};

    // 申请回调使用的内存
    DB_BUF_STRU pstBuf2;
    ret = TestMallocBuf(&pstBuf2, tblRecLen, deleteNum, deleteNum * tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置参数
    DB_SELECT_STRU pstSelect = {.ulCdbId = TPC_GLOBAL_CDB,
        .ulDbId = g_dbId,
        .usRelNo = relId[0],
        .pstCond = &pstCond,
        .pstFldFilter = &pstFldFilter,
        .pstSort = &pstSort,
        .pstQueryPlan = &pstQueryPlan,
        .pfGetBuf = PfGetBufCallBack,
        .pvArg = (VOS_VOID *)&pvArg,
        .pstDataBuf = &pstBuf2};

    for (int i = deleteNum; i < insertNum; i++) {  // 表1 删除 32-479，小于1/8个页
        ret = DeleteTbl64B(g_dbId, relId[0], i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 正常查询，保证参数正常
    ret = TPC_SelectAllRec2(&pstSelect);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放内存
    TestFreeBuf(&pstBuf1);
    TestFreeBuf(&pstBuf2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 049 开启查询句柄后，插入数据至高于阈值后进行SelectAllRec2数据
TEST_F(ShmSpDDL, V1Com_039_ShmSpDDL_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;  // 480条数据，约十六分之十五个页的数据
    int addNum = REC_NUM_PER_PAGE - (REC_NUM_PER_PAGE / pageNum);  // 32条数据，十六分之一个页的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        if (j == 2) {
            insertNum = REC_NUM_PER_PAGE - (REC_NUM_PER_PAGE / pageNum);
        }
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_dbId, relId[0], &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 设置查询条件
    insertNum = REC_NUM_PER_PAGE / pageNum;
    DB_COND_STRU pstCond = {.usCondNum = 1};
    uint32_t conVal = addNum;
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_LESS, (VOS_UINT8 *)&conVal, sizeof(conVal));

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_SORT_STRU pstSort = {.ucSortNum = 0, .aucReserve = {0}, .enSortType = DB_SORTTYPE_ASCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {0};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 获取查询计划
    DB_QUERY_PLAN_STRU pstQueryPlan;
    ret = TPC_PrepareQueryPlan(TPC_GLOBAL_CDB, g_dbId, relId[0], &pstCond, &pstQueryPlan);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 申请接口存放数据的buff
    DB_BUF_STRU pstBuf1;
    ret = TestMallocBuf(&pstBuf1, tblRecLen, addNum, addNum * tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    PvArgT pvArg = {.isAlreadyMalloc = true, .callCnt = 0, .bufAddr = &pstBuf1};

    // 申请回调使用的内存
    DB_BUF_STRU pstBuf2;
    ret = TestMallocBuf(&pstBuf2, tblRecLen, addNum, addNum * tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置参数
    DB_SELECT_STRU pstSelect = {.ulCdbId = TPC_GLOBAL_CDB,
        .ulDbId = g_dbId,
        .usRelNo = relId[0],
        .pstCond = &pstCond,
        .pstFldFilter = &pstFldFilter,
        .pstSort = &pstSort,
        .pstQueryPlan = &pstQueryPlan,
        .pfGetBuf = PfGetBufCallBack,
        .pvArg = (VOS_VOID *)&pvArg,
        .pstDataBuf = &pstBuf2};

    for (int k = insertNum; k < addNum; k++) {  // 表1再次写入数据 32-479，大于7/8个页
        ret = InsertTbl64B(g_dbId, relId[0], k, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 正常查询，保证参数正常
    ret = TPC_SelectAllRec2(&pstSelect);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放内存
    TestFreeBuf(&pstBuf1);
    TestFreeBuf(&pstBuf2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 050 开启查询句柄后，在共享空间内增删改查后进行SelectAllRec2数据
TEST_F(ShmSpDDL, V1Com_039_ShmSpDDL_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;  // 480条数据，约十六分之十五个页的数据
    int addNum = REC_NUM_PER_PAGE - (REC_NUM_PER_PAGE / pageNum);  // 32条数据，十六分之一个页的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        if (j == 2) {
            insertNum = REC_NUM_PER_PAGE - (REC_NUM_PER_PAGE / pageNum);
        }
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_dbId, relId[0], &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 设置查询条件
    insertNum = REC_NUM_PER_PAGE / pageNum;
    DB_COND_STRU pstCond = {.usCondNum = 1};
    uint32_t conVal = insertNum;
    TestSetCondVal(&pstCond.aCond[0], 0, DB_OP_LESS, (VOS_UINT8 *)&conVal, sizeof(conVal));

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_SORT_STRU pstSort = {.ucSortNum = 0, .aucReserve = {0}, .enSortType = DB_SORTTYPE_ASCEND};
    T_FIELD sortFlds[pstSort.ucSortNum] = {0};
    pstSort.pSortFields = (T_FIELD *)sortFlds;

    // 获取查询计划
    DB_QUERY_PLAN_STRU pstQueryPlan;
    ret = TPC_PrepareQueryPlan(TPC_GLOBAL_CDB, g_dbId, relId[0], &pstCond, &pstQueryPlan);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 申请接口存放数据的buff
    DB_BUF_STRU pstBuf1;
    ret = TestMallocBuf(&pstBuf1, tblRecLen, insertNum, insertNum * tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    PvArgT pvArg = {.isAlreadyMalloc = true, .callCnt = 0, .bufAddr = &pstBuf1};

    // 申请回调使用的内存
    DB_BUF_STRU pstBuf2;
    ret = TestMallocBuf(&pstBuf2, tblRecLen, insertNum, insertNum * tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 设置参数
    DB_SELECT_STRU pstSelect = {.ulCdbId = TPC_GLOBAL_CDB,
        .ulDbId = g_dbId,
        .usRelNo = relId[0],
        .pstCond = &pstCond,
        .pstFldFilter = &pstFldFilter,
        .pstSort = &pstSort,
        .pstQueryPlan = &pstQueryPlan,
        .pfGetBuf = PfGetBufCallBack,
        .pvArg = (VOS_VOID *)&pvArg,
        .pstDataBuf = &pstBuf2};

    // 更新
    for (int i = 0; i < insertNum; i++) {
        ret = UpdateTbl64B(g_dbId, relId[0], i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId[0], insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除
    for (int i = 0; i < insertNum; i++) {
        ret = DeleteTbl64B(g_dbId, relId[0], i + 1000, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 写入
    for (int k = 0; k < insertNum; k++) {
        ret = InsertTbl64B(g_dbId, relId[0], k, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 正常查询，保证参数正常
    ret = TPC_SelectAllRec2(&pstSelect);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放内存
    TestFreeBuf(&pstBuf1);
    TestFreeBuf(&pstBuf2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 051 表定义数据长度大于pageSize/8 - 2，表无数据时导出导入
TEST_F(ShmSpDDL, V1Com_039_ShmSpDDL_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;  // 32条数据，约十六分之一的数据
    uint16_t relId[6] = {0};
    // pageSize=32Kb, 表长度4095Byte
    for (int j = 0; j < 1; j++) {
        ret = TestTPC_CreateTbl(g_dbId, "schemaFile/tbl4095B.json", &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // TPC_Sysview查询和表明检验，预期表在共享空间
    ret = TestSysviewAndCheckTbl(
        g_dbId, relId[0], "common area details:table nums: 0, total data size: 0, total area size: 0");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导出
    char filePath[64] = "./filePath/export.txt";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};

    ret = TPC_BkpPhy(g_dbId, (uint8_t *)filePath);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)g_dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    ret = TPC_Restore((uint8_t *)filePath, (VOS_UINT8 *)g_dbName2, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // TPC_Sysview查询和表明检验，预期表在共享空间
    ret = TestSysviewAndCheckTbl(
        g_dbId2, relId[0], "common area details:table nums: 0, total data size: 0, total area size: 0");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CreateDB((VOS_UINT8 *)g_dbName, NULL, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_dbName, &g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 052 表定义数据长度等于pageSize/8 - 2，表无数据时导出导入
TEST_F(ShmSpDDL, V1Com_039_ShmSpDDL_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;  // 32条数据，约十六分之一的数据
    uint16_t relId[6] = {0};
    // pageSize=32Kb, 表长度4094Byte
    for (int j = 0; j < 1; j++) {
        ret = TestTPC_CreateTbl(g_dbId, "schemaFile/tbl4094B.json", &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // TPC_Sysview查询和表明检验，预期表在共享空间
    ret = TestSysviewAndCheckTbl(
        g_dbId, relId[0], "index:0, table id: 0, table name: tableF, record num: 0, record len: 4080, state: 0");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导出
    char filePath[64] = "./filePath/export.txt";
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};

    ret = TPC_BkpPhy(g_dbId, (uint8_t *)filePath);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CLoseDB
    ret = TPC_CloseDB(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)g_dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    ret = TPC_Restore((uint8_t *)filePath, (VOS_UINT8 *)g_dbName2, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // TPC_Sysview查询和表明检验，预期表在共享空间
    ret = TestSysviewAndCheckTbl(
        g_dbId2, relId[0], "index:0, table id: 0, table name: tableF, record num: 0, record len: 4080, state: 0");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CreateDB((VOS_UINT8 *)g_dbName, NULL, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_dbName, &g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
