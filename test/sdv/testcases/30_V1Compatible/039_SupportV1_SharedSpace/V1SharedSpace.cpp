/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 Description  : 支持多表（少数据量）共享存储空间
 Author       : herui h60035902
 Modification :
 create       : 2025/07/03
**************************************************************************** */
#include "V1SharedSpace.h"

int ret = 0;
class SharedSpace_Test : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        ret = system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        ret = TestTPC_Init();
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    };
    static void TearDownTestCase()
    {
        ret = TestTPC_UnInit();
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    };

public:
    virtual void SetUp();
    virtual void TearDown();
};

void SharedSpace_Test::SetUp()
{
    printf("\n====================TEST:BEGIN====================\n");
    AW_CHECK_LOG_BEGIN();
    ret = TPC_CreateDB((VOS_UINT8 *)g_testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
void SharedSpace_Test::TearDown()
{
    ret = TPC_CloseDB(g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_testDbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_CHECK_LOG_END();
    AW_FUN_Log(LOG_STEP, "test end.");
    printf("\n=====================TEST:END=====================\n");
}
class ModifyCfg_Test : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        ret = system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    };
    static void TearDownTestCase()
    {
        ret = TestTPC_UnInit();
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    };

public:
    virtual void SetUp();
    virtual void TearDown();
};

void ModifyCfg_Test::SetUp()
{
    printf("\n====================TEST:BEGIN====================\n");
    AW_CHECK_LOG_BEGIN();
}
void ModifyCfg_Test::TearDown()
{
    AW_CHECK_LOG_END();
    AW_FUN_Log(LOG_STEP, "test end.");
    printf("\n=====================TEST:END=====================\n");
}
// 080.独立空间中开启一次CDB对同一条数据多次增删改查，提交后校验数据
TEST_F(SharedSpace_Test, V1Com_039_080)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef);
    // 表写入大量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 2000, true);
    }
    // 开启CDB
    VOS_UINT32 CdbId = 0;
    ret = TPC_BeginCDB(g_testDbId, &CdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < 200; i++) {
        // CDB更新数据
        Update(testRelId[0], 0, 2000, true, 1, g_testDbId, CdbId);
        // CDB删除数据
        Delete(testRelId[0], 2000, true, 1, g_testDbId, CdbId);
        // CDB插入数据
        Insert(testRelId[0], 1, true, g_testDbId, CdbId);
        // CDB查询数据
        selectDataByAllWays(testRelId[0], 1, 0, true, CdbId);
    }
    // 提交CDB
    ret = TPC_CommitCDB(CdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验表的数据
    VOS_UINT32 expectvalue[2000] = {0};
    for (int i = 0; i < 2000; i++) {
        expectvalue[i] = i;
    }
    ValidateExpectData(testRelId[0], 2000, &testRelDef[0], expectvalue, true);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
}
// 081.独立空间中开启一次CDB对不同数据进行多次增删改查，提交后校验数据
TEST_F(SharedSpace_Test, V1Com_039_081)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef);
    // 表写入大量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 2000, true);
    }
    // 开启CDB
    VOS_UINT32 CdbId = 0;
    ret = TPC_BeginCDB(g_testDbId, &CdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < 200; i++) {
        // CDB更新数据
        Update(testRelId[0], i, 2000, true, 1, g_testDbId, CdbId);
        // CDB删除数据
        Delete(testRelId[0], 2000, true, 1, g_testDbId, CdbId);
        // CDB插入数据
        Insert(testRelId[0], 1, true, g_testDbId, CdbId, i);
        // CDB查询数据
        selectDataByAllWays(testRelId[0], 1, i, true, CdbId);
    }
    // 提交CDB
    ret = TPC_CommitCDB(CdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验表的数据
    VOS_UINT32 expectvalue[2000] = {0};
    for (int i = 0; i < 2000; i++) {
        expectvalue[i] = i;
    }
    ValidateExpectData(testRelId[0], 2000, &testRelDef[0], expectvalue, true);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
}
// 082.共享空间中多次开启CDB对不同表进行增删改查后校验数据
TEST_F(SharedSpace_Test, V1Com_039_082)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef);
    // 表写入大量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 200, true);
    }
    for (int i = 0; i < 200; i++) {
        // 开启CDB
        VOS_UINT32 CdbId = 0;
        ret = TPC_BeginCDB(g_testDbId, &CdbId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // CDB更新数据
        Update(testRelId[i % 9], i, 2000, true, 1, g_testDbId, CdbId);
        // CDB删除数据
        Delete(testRelId[i % 9], 2000, true, 1, g_testDbId, CdbId);
        // CDB插入数据
        Insert(testRelId[i % 9], 1, true, g_testDbId, CdbId, i);
        // CDB查询数据
        selectDataByAllWays(testRelId[i % 9], 1, i, true, CdbId);
        // 提交CDB
        ret = TPC_CommitCDB(CdbId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    VOS_UINT32 expectvalue[200] = {0};
    for (int j = 0; j < 200; j++) {
        expectvalue[j] = j;
    }
    // 校验表的数据
    for (int i = 0; i < 9; i++) {
        ValidateExpectData(testRelId[i], 200, &testRelDef[i / 3], expectvalue, true);
    }
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
}
// 083.开启CDB后开启查询句柄，删除数据至低于阈值后进行fetch数据后提交
TEST_F(SharedSpace_Test, V1Com_039_083)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef);
    // 表写入大量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 2000, true);
    }
    // 开启CDB
    VOS_UINT32 CdbId = 0;
    ret = TPC_BeginCDB(g_testDbId, &CdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 开启查询句柄
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 1;
    pstCond.aCond[0].enOp = DB_OP_LESSEQUAL;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_INT32 *)pstCond.aCond[0].aucValue = 2000;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_SELHANDLE phSelect;

    DB_DSBUF_STRU DsBufGet;
    DsBufGet.usRecLen = DBDDL_GetRelRecLen(g_testDbId, testRelId[0]);
    DsBufGet.usRecNum = 2000;
    DsBufGet.StdBuf.ulBufLen = DsBufGet.usRecLen * (DsBufGet.usRecNum);
    VOS_UINT8 *pucDataGet = (VOS_UINT8 *)TEST_V1_MALLOC(DsBufGet.StdBuf.ulBufLen);
    (void)memset_s(pucDataGet, DsBufGet.StdBuf.ulBufLen, 0x00, DsBufGet.StdBuf.ulBufLen);
    DsBufGet.StdBuf.pucData = pucDataGet;

    ret = TPC_BeginSelect(CdbId, g_testDbId, testRelId[0], &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除数据
    for (int i = 42; i < 2000; i++) {
        Delete(testRelId[0], i, true, 1, g_testDbId);
    }
    // fetch数据
    ret = TPC_FetchSelectRec(CdbId, phSelect, &DsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验是否迁入迁出
    SelectView(testRelId[0], false, DB_TPC_SYSVIEW_GET_COMMOM_AREA);
    // 校验数据
    VOS_UINT32 expectvalue[2000] = {0};
    for (int i = 0; i < 2000; i++) {
        expectvalue[i] = i;
    }
    testAllData(testRelId[0], 2000, &testRelDef[0], expectvalue, pucDataGet, g_testDbId);
    ret = TPC_EndSelect(CdbId, phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 提交CDB
    ret = TPC_CommitCDB(CdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 进行更新触发迁入迁出
    sleep(10);
    Update(testRelId[0], 0, 2001, true);
    SelectView(testRelId[0], true, DB_TPC_SYSVIEW_GET_COMMOM_AREA);
    TEST_V1_FREE(pucDataGet);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
}
// 084.开启CDB后开启查询句柄，插入数据至高于阈值后进行fetch数据后提交
TEST_F(SharedSpace_Test, V1Com_039_084)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef);
    // 表写入少量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 10, true);
    }

    // 开启CDB
    VOS_UINT32 CdbId = 0;
    ret = TPC_BeginCDB(g_testDbId, &CdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 开启查询句柄
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 1;
    pstCond.aCond[0].enOp = DB_OP_LESSEQUAL;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_INT32 *)pstCond.aCond[0].aucValue = 10;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_SELHANDLE phSelect;

    DB_DSBUF_STRU DsBufGet;
    DsBufGet.usRecLen = DBDDL_GetRelRecLen(g_testDbId, testRelId[0]);
    DsBufGet.usRecNum = 10;
    DsBufGet.StdBuf.ulBufLen = DsBufGet.usRecLen * (DsBufGet.usRecNum);
    VOS_UINT8 *pucDataGet = (VOS_UINT8 *)TEST_V1_MALLOC(DsBufGet.StdBuf.ulBufLen);
    (void)memset_s(pucDataGet, DsBufGet.StdBuf.ulBufLen, 0x00, DsBufGet.StdBuf.ulBufLen);
    DsBufGet.StdBuf.pucData = pucDataGet;

    ret = TPC_BeginSelect(CdbId, g_testDbId, testRelId[0], &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // CDB插入数据
    Insert(testRelId[0], 990, true, g_testDbId, TPC_GLOBAL_CDB, 10);

    // fetch数据
    ret = TPC_FetchSelectRec(CdbId, phSelect, &DsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验是否迁入迁出
    SelectView(testRelId[0], true, DB_TPC_SYSVIEW_GET_COMMOM_AREA);
    // 校验数据
    VOS_UINT32 expectvalue[10] = {0};
    for (int i = 0; i < 10; i++) {
        expectvalue[i] = i;
    }
    testAllData(testRelId[0], 10, &testRelDef[0], expectvalue, pucDataGet, g_testDbId);
    ret = TPC_EndSelect(CdbId, phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 提交CDB
    ret = TPC_CommitCDB(CdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 进行更新触发迁入迁出
    Update(testRelId[0], 0, 2001, true);
    SelectView(testRelId[0], false, DB_TPC_SYSVIEW_GET_COMMOM_AREA);
    TEST_V1_FREE(pucDataGet);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
}
// 085.开启CDB后开启查询句柄，在共享空间内增删改查后进行fetch数据后提交
TEST_F(SharedSpace_Test, V1Com_039_085)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef);
    // 表写入大量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 10, true);
    }
    // 开启CDB
    VOS_UINT32 CdbId = 0;
    ret = TPC_BeginCDB(g_testDbId, &CdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 开启查询句柄
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 1;
    pstCond.aCond[0].enOp = DB_OP_LESSEQUAL;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_INT32 *)pstCond.aCond[0].aucValue = 10;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_SELHANDLE phSelect;

    DB_DSBUF_STRU DsBufGet;
    DsBufGet.usRecLen = DBDDL_GetRelRecLen(g_testDbId, testRelId[0]);
    DsBufGet.usRecNum = 10;
    DsBufGet.StdBuf.ulBufLen = DsBufGet.usRecLen * (DsBufGet.usRecNum);
    VOS_UINT8 *pucDataGet = (VOS_UINT8 *)TEST_V1_MALLOC(DsBufGet.StdBuf.ulBufLen);
    (void)memset_s(pucDataGet, DsBufGet.StdBuf.ulBufLen, 0x00, DsBufGet.StdBuf.ulBufLen);
    DsBufGet.StdBuf.pucData = pucDataGet;

    ret = TPC_BeginSelect(CdbId, g_testDbId, testRelId[0], &pstCond, &pstFldFilter, &phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 更新数据
    Update(testRelId[0], 0, 2000, true);
    // 删除数据
    Delete(testRelId[0], 2000, true);
    // 插入数据
    Insert(testRelId[0], 1, true);
    // fetch数据
    ret = TPC_FetchSelectRec(CdbId, phSelect, &DsBufGet);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验数据
    VOS_UINT32 expectvalue[10] = {0};
    for (int i = 0; i < 10; i++) {
        expectvalue[i] = i;
    }
    testAllData(testRelId[0], 10, &testRelDef[0], expectvalue, pucDataGet, g_testDbId);
    ret = TPC_EndSelect(CdbId, phSelect);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 提交CDB
    ret = TPC_CommitCDB(CdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TEST_V1_FREE(pucDataGet);
    // 校验是否迁入迁出
    SelectView(testRelId[0], true, DB_TPC_SYSVIEW_GET_COMMOM_AREA);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
}
void *OpenCDBAndDML(void *args)
{
    DqlArgsT *dqlArg = (DqlArgsT *)args;
    // 开启CDB
    VOS_UINT32 CdbId = 0;
    ret = TPC_BeginCDB(g_testDbId, &CdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 进行多次增删改查操作
    for (int i = 0; i < 10; i++) {
        // 更新数据
        Update(dqlArg->testRelId, 0, 2001, true, 1, g_testDbId, CdbId);
        // 删除数据
        Delete(dqlArg->testRelId, 2001, true, 1, g_testDbId, CdbId);
        // 插入数据
        Insert(dqlArg->testRelId, 1, true, g_testDbId, CdbId);
        // 查询数据
        selectDataByAllWays(dqlArg->testRelId, 1, 0, true, CdbId);
    }
    // 提交CDB
    ret = TPC_ForceCommitCDB(CdbId);
    if (ret != DB_SUCCESS_V1) {
        ret = TPC_RollbackCDB(CdbId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
}
// 086.多线程开启CDB增删改查数据
TEST_F(SharedSpace_Test, V1Com_039_086)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef);
    // 所有表写入大量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 2000, true);
    }
    int threadnum = 200;
    pthread_t tid[threadnum];
    DqlArgsT ddlArg;
    ddlArg.testRelId = testRelId[1];
    ddlArg.Data = 0;
    ddlArg.astFlds = testRelDef[0].pstFldLst;
    for (int i = 0; i < threadnum; i++) {
        ret = pthread_create(&tid[i], NULL, OpenCDBAndDML, &ddlArg);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    for (int i = 0; i < threadnum; i++) {
        ret = pthread_join(tid[i], NULL);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 校验所有表的数据
    VOS_UINT32 expectvalue[2000] = {0};
    for (int i = 0; i < 2000; i++) {
        expectvalue[i] = i;
    }
    for (int i = 0; i < 9; i++) {
        ValidateExpectData(testRelId[i], 2000, &testRelDef[i / 3], expectvalue, true);
    }
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
}
// 087.CDB1插入数据至超出阈值，CDB2插入一条数据后强制提交
TEST_F(SharedSpace_Test, V1Com_039_087)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef);
    // 表写入少量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 10, true);
    }
    // 开启CDB
    VOS_UINT32 CdbId1 = 0;
    ret = TPC_BeginCDB(g_testDbId, &CdbId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // CDB1插入数据
    Insert(testRelId[0], 990, true, g_testDbId, CdbId1, 10);
    // 开启CDB
    VOS_UINT32 CdbId2 = 0;
    ret = TPC_BeginCDB(g_testDbId, &CdbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // CDB2插入一条数据
    Insert(testRelId[0], 1, true, g_testDbId, CdbId2, 1000);
    Validatediff(CdbId2, testRelId[0], 1000, 1, testRelDef[0].pstFldLst);
    // 提交CDB
    ret = TPC_ForceCommitCDB(CdbId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验是否迁入迁出
    SelectView(testRelId[0], true, DB_TPC_SYSVIEW_GET_COMMOM_AREA);
    // 提交CDB
    ret = TPC_ForceCommitCDB(CdbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验表的数据
    VOS_UINT32 expectvalue[1000] = {0};
    for (int i = 0; i < 1000; i++) {
        expectvalue[i] = i;
    }
    ValidateExpectData(testRelId[0], 1000, &testRelDef[0], expectvalue, true);
    // 进行更新触发迁入迁出
    Update(testRelId[0], 0, 2001, true);
    SelectView(testRelId[0], false, DB_TPC_SYSVIEW_GET_COMMOM_AREA);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
}
// 088.CDB1插入一条数据，CDB2插入一条数据强制提交
TEST_F(SharedSpace_Test, V1Com_039_088)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef);
    // 表写入大量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 2000, true);
    }
    // 开启CDB
    VOS_UINT32 CdbId1 = 0;
    ret = TPC_BeginCDB(g_testDbId, &CdbId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // CDB1插入数据
    Insert(testRelId[0], 1, true, g_testDbId, CdbId1, 2000);
    // 开启CDB
    VOS_UINT32 CdbId2 = 0;
    ret = TPC_BeginCDB(g_testDbId, &CdbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // CDB2插入一条数据
    Insert(testRelId[0], 1, true, g_testDbId, CdbId2, 2001);
    Validatediff(CdbId2, testRelId[0], 2001, 1, testRelDef[0].pstFldLst);
    // 提交CDB
    ret = TPC_ForceCommitCDB(CdbId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 提交CDB
    ret = TPC_ForceCommitCDB(CdbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验表的数据
    VOS_UINT32 expectvalue[2002] = {0};
    for (int i = 0; i < 2002; i++) {
        expectvalue[i] = i;
    }
    ValidateExpectData(testRelId[0], 2002, &testRelDef[0], expectvalue, true);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
}
// 089.CDB1插入一条数据，CDB2插入一条数据强制提交后超出阈值
TEST_F(SharedSpace_Test, V1Com_039_089)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef);
    // 表写入少量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 10, true);
    }
    // 开启CDB
    VOS_UINT32 CdbId1 = 0;
    ret = TPC_BeginCDB(g_testDbId, &CdbId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 CdbId2 = 0;
    ret = TPC_BeginCDB(g_testDbId, &CdbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // CDB1插入数据
    Insert(testRelId[0], g_Threshold - 10 - 1, true, g_testDbId, CdbId1, 10);
    // CDB2插入一条数据
    Insert(testRelId[0], 1, true, g_testDbId, CdbId2, g_Threshold - 1);
    Validatediff(CdbId2, testRelId[0], g_Threshold - 1, 1, testRelDef[0].pstFldLst);
    // 提交CDB
    ret = TPC_ForceCommitCDB(CdbId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 提交CDB
    ret = TPC_ForceCommitCDB(CdbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    SelectView(testRelId[0], true, DB_TPC_SYSVIEW_GET_COMMOM_AREA);
    // 校验表的数据
    VOS_UINT32 expectvalue[g_Threshold] = {0};
    for (int i = 0; i < g_Threshold; i++) {
        expectvalue[i] = i;
    }
    ValidateExpectData(testRelId[0], g_Threshold, &testRelDef[0], expectvalue, true);
    // 进行更新触发迁入迁出
    Update(testRelId[0], 0, 2001, true);
    SelectView(testRelId[0], false, DB_TPC_SYSVIEW_GET_COMMOM_AREA);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
}
// 090.CDB1插入一条数据，CDB2删除数据后低于阈值强制提交
TEST_F(SharedSpace_Test, V1Com_039_090)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef);
    // 表写入大量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 2000, true);
    }
    // 开启CDB
    VOS_UINT32 CdbId1 = 0;
    ret = TPC_BeginCDB(g_testDbId, &CdbId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 CdbId2 = 0;
    ret = TPC_BeginCDB(g_testDbId, &CdbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // CDB1插入数据
    Insert(testRelId[0], 1, true, g_testDbId, CdbId1, 2000);
    Validatediff(CdbId1, testRelId[0], 2000, 1, testRelDef[0].pstFldLst);
    // CDB2删除数据
    for (int i = 41; i < 2000; i++) {
        Delete(testRelId[0], i, true, 1, g_testDbId, CdbId2);
    }
    // 提交CDB
    ret = TPC_ForceCommitCDB(CdbId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 提交CDB
    ret = TPC_ForceCommitCDB(CdbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验是否迁入迁出
    SelectView(testRelId[0], false, DB_TPC_SYSVIEW_GET_COMMOM_AREA);
    // 校验表的数据
    VOS_UINT32 expectvalue[42] = {0};
    for (int i = 0; i < 42; i++) {
        expectvalue[i] = i;
    }
    expectvalue[41] = 2000;
    ValidateExpectData(testRelId[0], 42, &testRelDef[0], expectvalue, true);
    // 更新触发迁入迁出
    sleep(10);
    Update(testRelId[0], 0, 2001, true);
    SelectView(testRelId[0], true, DB_TPC_SYSVIEW_GET_COMMOM_AREA);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
}
// 091.CDB1插入一条数据至超出阈值，CDB2更新一条数据后强制提交
TEST_F(SharedSpace_Test, V1Com_039_091)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef);
    // 表写入少量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], g_Threshold - 1, true);
    }
    // 开启CDB
    VOS_UINT32 CdbId1 = 0;
    ret = TPC_BeginCDB(g_testDbId, &CdbId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 CdbId2 = 0;
    ret = TPC_BeginCDB(g_testDbId, &CdbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // CDB1插入数据
    Insert(testRelId[0], 1, true, g_testDbId, CdbId1, g_Threshold - 1);

    // CDB2更新数据
    Update(testRelId[0], 0, g_Threshold, true, 1, g_testDbId, CdbId2);
    Validatediff(CdbId2, testRelId[0], g_Threshold, 3, testRelDef[0].pstFldLst);
    // 提交CDB
    ret = TPC_ForceCommitCDB(CdbId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 提交CDB
    ret = TPC_ForceCommitCDB(CdbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验是否迁入迁出
    SelectView(testRelId[0], true, DB_TPC_SYSVIEW_GET_COMMOM_AREA);
    // 校验表的数据
    VOS_UINT32 expectvalue[g_Threshold] = {0};
    for (int i = 0; i < g_Threshold; i++) {
        expectvalue[i] = i + 1;
    }
    ValidateExpectData(testRelId[0], g_Threshold, &testRelDef[0], expectvalue, true);
    // 更新触发迁入迁出
    sleep(1);
    Update(testRelId[0], 1, 2001, true);
    SelectView(testRelId[0], false, DB_TPC_SYSVIEW_GET_COMMOM_AREA);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
}
// 092.CDB1插入一条数据，CDB2更新一条数据后强制提交
TEST_F(SharedSpace_Test, V1Com_039_092)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef);
    // 表写入大量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 2000, true);
    }
    // 开启CDB
    VOS_UINT32 CdbId1 = 0;
    ret = TPC_BeginCDB(g_testDbId, &CdbId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 CdbId2 = 0;
    ret = TPC_BeginCDB(g_testDbId, &CdbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // CDB1插入数据
    Insert(testRelId[0], 1, true, g_testDbId, CdbId1, 2000);

    // CDB2更新数据
    Update(testRelId[0], 0, 2001, true, 1, g_testDbId, CdbId2);
    Validatediff(CdbId2, testRelId[0], 2001, 3, testRelDef[0].pstFldLst);
    // 提交CDB
    ret = TPC_ForceCommitCDB(CdbId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 提交CDB
    ret = TPC_ForceCommitCDB(CdbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验表的数据
    VOS_UINT32 expectvalue[2001] = {0};
    for (int i = 0; i < 2001; i++) {
        expectvalue[i] = i + 1;
    }
    ValidateExpectData(testRelId[0], 2001, &testRelDef[0], expectvalue, true);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
}
// 093.CDB1更新数据，CDB2更新不同数据后强制提交
TEST_F(SharedSpace_Test, V1Com_039_093)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef);
    // 表写入大量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 2000, true);
    }
    // 开启CDB
    VOS_UINT32 CdbId1 = 0;
    ret = TPC_BeginCDB(g_testDbId, &CdbId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 CdbId2 = 0;
    ret = TPC_BeginCDB(g_testDbId, &CdbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // CDB1更新数据
    Update(testRelId[0], 0, 2000, true, 1, g_testDbId, CdbId1);
    Validatediff(CdbId1, testRelId[0], 2000, 3, testRelDef[0].pstFldLst);
    // CDB2更新数据
    Update(testRelId[0], 1, 2001, true, 1, g_testDbId, CdbId2);

    // 提交CDB
    ret = TPC_ForceCommitCDB(CdbId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 提交CDB
    ret = TPC_ForceCommitCDB(CdbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验表的数据
    VOS_UINT32 expectvalue[2000] = {0};
    for (int i = 0; i < 2000; i++) {
        expectvalue[i] = i + 2;
    }
    ValidateExpectData(testRelId[0], 2000, &testRelDef[0], expectvalue, true);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
}
// 094.CDB1更新数据，CDB2更新同一条数据为不同值后强制提交
TEST_F(SharedSpace_Test, V1Com_039_094)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef);
    // 表写入大量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 2000, true);
    }
    // 开启CDB
    VOS_UINT32 CdbId1 = 0;
    ret = TPC_BeginCDB(g_testDbId, &CdbId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 CdbId2 = 0;
    ret = TPC_BeginCDB(g_testDbId, &CdbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // CDB1更新数据
    Update(testRelId[0], 0, 2001, true, 1, g_testDbId, CdbId1);
    Validatediff(CdbId1, testRelId[0], 2001, 3, testRelDef[0].pstFldLst);
    // CDB2更新数据
    Update(testRelId[0], 0, 2000, true, 1, g_testDbId, CdbId2);

    // 提交CDB
    ret = TPC_ForceCommitCDB(CdbId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 提交CDB
    ret = TPC_ForceCommitCDB(CdbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验表的数据
    VOS_UINT32 expectvalue[2000] = {0};
    for (int i = 0; i < 2000; i++) {
        expectvalue[i] = i + 1;
    }
    ValidateExpectData(testRelId[0], 2000, &testRelDef[0], expectvalue, true);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
}
// 095.CDB1更新数据，CDB2插入值相同的一条数据
TEST_F(SharedSpace_Test, V1Com_039_095)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef);
    // 表写入大量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 2000, true);
    }
    // 开启CDB
    VOS_UINT32 CdbId1 = 0;
    ret = TPC_BeginCDB(g_testDbId, &CdbId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 CdbId2 = 0;
    ret = TPC_BeginCDB(g_testDbId, &CdbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // CDB1更新数据
    Update(testRelId[0], 0, 2000, true, 1, g_testDbId, CdbId1);
    Validatediff(CdbId1, testRelId[0], 2000, 3, testRelDef[0].pstFldLst);
    // CDB2插入数据
    Insert(testRelId[0], 1, true, g_testDbId, CdbId2, 2000);
    Validatediff(CdbId2, testRelId[0], 2000, 1, testRelDef[0].pstFldLst);
    // 提交CDB
    ret = TPC_ForceCommitCDB(CdbId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 提交CDB
    ret = TPC_ForceCommitCDB(CdbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_KEYDUPLICATE, ret);
    // 回滚CDB
    ret = TPC_RollbackCDB(CdbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验表的数据
    VOS_UINT32 expectvalue[2000] = {0};
    for (int i = 0; i < 2000; i++) {
        expectvalue[i] = i + 1;
    }
    ValidateExpectData(testRelId[0], 2000, &testRelDef[0], expectvalue, true);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
}
// 096.CDB1更新数据，CDB2插入一条数据后超出阈值
TEST_F(SharedSpace_Test, V1Com_039_096)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef);
    // 表写入少量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], g_Threshold - 1, true);
    }
    SelectView(testRelId[0], true, DB_TPC_SYSVIEW_GET_COMMOM_AREA);
    // 开启CDB
    VOS_UINT32 CdbId1 = 0;
    ret = TPC_BeginCDB(g_testDbId, &CdbId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 CdbId2 = 0;
    ret = TPC_BeginCDB(g_testDbId, &CdbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // CDB1更新数据
    Update(testRelId[0], 0, g_Threshold, true, 1, g_testDbId, CdbId1);
    Validatediff(CdbId1, testRelId[0], g_Threshold, 3, testRelDef[0].pstFldLst);
    // CDB2插入数据
    Insert(testRelId[0], 1, true, g_testDbId, CdbId2, g_Threshold - 1);
    Validatediff(CdbId2, testRelId[0], g_Threshold - 1, 1, testRelDef[0].pstFldLst);
    // 提交CDB
    ret = TPC_ForceCommitCDB(CdbId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 提交CDB
    ret = TPC_ForceCommitCDB(CdbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    SelectView(testRelId[0], true, DB_TPC_SYSVIEW_GET_COMMOM_AREA);
    // 校验表的数据
    VOS_UINT32 expectvalue[g_Threshold] = {0};
    for (int i = 0; i < g_Threshold; i++) {
        expectvalue[i] = i + 1;
    }
    ValidateExpectData(testRelId[0], g_Threshold, &testRelDef[0], expectvalue, true);
    // 进行更新触发迁入迁出
    sleep(1);
    Update(testRelId[0], 1, 2001, true);
    SelectView(testRelId[0], false, DB_TPC_SYSVIEW_GET_COMMOM_AREA);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
}
// 097.CDB1更新数据，CDB2插入一条数据
TEST_F(SharedSpace_Test, V1Com_039_097)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef);
    // 表写入大量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 2000, true);
    }
    // 开启CDB
    VOS_UINT32 CdbId1 = 0;
    ret = TPC_BeginCDB(g_testDbId, &CdbId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 CdbId2 = 0;
    ret = TPC_BeginCDB(g_testDbId, &CdbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // CDB1更新数据
    Update(testRelId[0], 0, 2001, true, 1, g_testDbId, CdbId1);
    Validatediff(CdbId1, testRelId[0], 2001, 3, testRelDef[0].pstFldLst);
    // CDB2插入数据
    Insert(testRelId[0], 1, true, g_testDbId, CdbId2, 2000);
    Validatediff(CdbId2, testRelId[0], 2000, 1, testRelDef[0].pstFldLst);
    // 提交CDB
    ret = TPC_ForceCommitCDB(CdbId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 提交CDB
    ret = TPC_ForceCommitCDB(CdbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验表的数据
    VOS_UINT32 expectvalue[2001] = {0};
    for (int i = 0; i < 2001; i++) {
        expectvalue[i] = i + 1;
    }
    ValidateExpectData(testRelId[0], 2001, &testRelDef[0], expectvalue, true);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
}
// 098.CDB1更新数据，CDB2删除一条数据
TEST_F(SharedSpace_Test, V1Com_039_098)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef);
    // 表写入大量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 2000, true);
    }
    // 开启CDB
    VOS_UINT32 CdbId1 = 0;
    ret = TPC_BeginCDB(g_testDbId, &CdbId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 CdbId2 = 0;
    ret = TPC_BeginCDB(g_testDbId, &CdbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // CDB1更新数据
    Update(testRelId[0], 0, 2000, true, 1, g_testDbId, CdbId1);
    Validatediff(CdbId1, testRelId[0], 2000, 3, testRelDef[0].pstFldLst);
    // CDB2删除数据
    Delete(testRelId[0], 1, true, 1, g_testDbId, CdbId2);
    Validatediff(CdbId2, testRelId[0], 1, 2, testRelDef[0].pstFldLst);
    // 提交CDB
    ret = TPC_ForceCommitCDB(CdbId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 提交CDB
    ret = TPC_ForceCommitCDB(CdbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验表的数据
    VOS_UINT32 expectvalue[1999] = {0};
    for (int i = 0; i < 1999; i++) {
        expectvalue[i] = i + 2;
    }
    ValidateExpectData(testRelId[0], 1999, &testRelDef[0], expectvalue, true);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
}
// 099.CDB1更新数据，CDB2删除被更新的那条数据
TEST_F(SharedSpace_Test, V1Com_039_099)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef);
    // 表写入大量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 2000, true);
    }
    // 开启CDB
    VOS_UINT32 CdbId1 = 0;
    ret = TPC_BeginCDB(g_testDbId, &CdbId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 CdbId2 = 0;
    ret = TPC_BeginCDB(g_testDbId, &CdbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // CDB1更新数据
    Update(testRelId[0], 0, 2000, true, 1, g_testDbId, CdbId1);
    Validatediff(CdbId1, testRelId[0], 2000, 3, testRelDef[0].pstFldLst);
    // CDB2删除数据
    Delete(testRelId[0], 0, true, 1, g_testDbId, CdbId2);
    Validatediff(CdbId2, testRelId[0], 0, 2, testRelDef[0].pstFldLst);
    // 提交CDB
    ret = TPC_ForceCommitCDB(CdbId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 提交CDB
    ret = TPC_ForceCommitCDB(CdbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验表的数据
    VOS_UINT32 expectvalue[1999] = {0};
    for (int i = 0; i < 1999; i++) {
        expectvalue[i] = i + 1;
    }
    ValidateExpectData(testRelId[0], 1999, &testRelDef[0], expectvalue, true);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
}
// 100.CDB1更新数据，CDB2删除被更新的那条数据后低于阈值
TEST_F(SharedSpace_Test, V1Com_039_100)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef);
    // 表写入少量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 2000, true);
    }
    // 开启CDB
    VOS_UINT32 CdbId1 = 0;
    ret = TPC_BeginCDB(g_testDbId, &CdbId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 CdbId2 = 0;
    ret = TPC_BeginCDB(g_testDbId, &CdbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // CDB1更新数据
    Update(testRelId[0], 0, 2000, true, 1, g_testDbId, CdbId1);
    Validatediff(CdbId1, testRelId[0], 2000, 3, testRelDef[0].pstFldLst);
    // CDB2删除数据
    for (int i = g_DeleteThreshold + 1; i < 2000; i++) {
        Delete(testRelId[0], i, true, 1, g_testDbId, CdbId2);
    }
    Delete(testRelId[0], 0, true, 1, g_testDbId, CdbId2);
    // 提交CDB
    ret = TPC_ForceCommitCDB(CdbId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 提交CDB
    ret = TPC_ForceCommitCDB(CdbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    SelectView(testRelId[0], false, DB_TPC_SYSVIEW_GET_COMMOM_AREA);
    // 校验表的数据
    VOS_UINT32 expectvalue[g_DeleteThreshold] = {0};
    for (int i = 0; i < g_DeleteThreshold; i++) {
        expectvalue[i] = i + 1;
    }
    ValidateExpectData(testRelId[0], g_DeleteThreshold, &testRelDef[0], expectvalue, true);
    // 进行更新触发迁入迁出
    sleep(10);
    Update(testRelId[0], 1, 2001, true);
    SelectView(testRelId[0], true, DB_TPC_SYSVIEW_GET_COMMOM_AREA);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
}
// 101.CDB1中删除一条数据，CDB2中插入一条数据后强制提交
TEST_F(SharedSpace_Test, V1Com_039_101)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef);
    // 表写入大量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 2000, true);
    }
    // 开启CDB
    VOS_UINT32 CdbId1 = 0;
    ret = TPC_BeginCDB(g_testDbId, &CdbId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 CdbId2 = 0;
    ret = TPC_BeginCDB(g_testDbId, &CdbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // CDB1删除数据
    Delete(testRelId[0], 0, true, 1, g_testDbId, CdbId1);
    Validatediff(CdbId1, testRelId[0], 0, 2, testRelDef[0].pstFldLst);

    // CDB2插入数据
    Insert(testRelId[0], 1, true, g_testDbId, CdbId2, 2000);
    Validatediff(CdbId2, testRelId[0], 2000, 1, testRelDef[0].pstFldLst);
    // 提交CDB
    ret = TPC_ForceCommitCDB(CdbId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 提交CDB
    ret = TPC_ForceCommitCDB(CdbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验表的数据
    VOS_UINT32 expectvalue[2000] = {0};
    for (int i = 0; i < 2000; i++) {
        expectvalue[i] = i + 1;
    }
    ValidateExpectData(testRelId[0], 2000, &testRelDef[0], expectvalue, true);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
}
// 102.CDB1中删除一条数据至低于阈值，CDB2中插入一条数据后强制提交
TEST_F(SharedSpace_Test, V1Com_039_102)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef);
    // 表写入少量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 2000, true);
    }
    // 开启CDB
    VOS_UINT32 CdbId1 = 0;
    ret = TPC_BeginCDB(g_testDbId, &CdbId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 CdbId2 = 0;
    ret = TPC_BeginCDB(g_testDbId, &CdbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // CDB1删除数据
    for (int i = g_DeleteThreshold + 1; i < 2000; i++) {
        Delete(testRelId[0], i, true, 1, g_testDbId, CdbId1);
    }
    Delete(testRelId[0], 0, true, 1, g_testDbId, CdbId1);
    // CDB2插入数据
    Insert(testRelId[0], 1, true, g_testDbId, CdbId2, 2000);
    Validatediff(CdbId2, testRelId[0], 2000, 1, testRelDef[0].pstFldLst);
    // 提交CDB
    ret = TPC_ForceCommitCDB(CdbId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 提交CDB
    ret = TPC_ForceCommitCDB(CdbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    SelectView(testRelId[0], false, DB_TPC_SYSVIEW_GET_COMMOM_AREA);
    // 校验表的数据
    VOS_UINT32 expectvalue[g_DeleteThreshold + 1] = {0};
    for (int i = 0; i < g_DeleteThreshold + 1; i++) {
        expectvalue[i] = i + 1;
    }
    expectvalue[g_DeleteThreshold] = 2000;
    ValidateExpectData(testRelId[0], g_DeleteThreshold + 1, &testRelDef[0], expectvalue, true);
    // 进行更新触发迁入迁出
    Update(testRelId[0], 1, 2001, true);
    SelectView(testRelId[0], false, DB_TPC_SYSVIEW_GET_COMMOM_AREA);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
}
// 103.CDB1中删除一条数据，CDB2中更新该数据后强制提交
TEST_F(SharedSpace_Test, V1Com_039_103)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef);
    // 表写入大量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 2000, true);
    }
    // 开启CDB
    VOS_UINT32 CdbId1 = 0;
    ret = TPC_BeginCDB(g_testDbId, &CdbId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 CdbId2 = 0;
    ret = TPC_BeginCDB(g_testDbId, &CdbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // CDB1删除数据
    Delete(testRelId[0], 0, true, 1, g_testDbId, CdbId1);
    Validatediff(CdbId1, testRelId[0], 0, 2, testRelDef[0].pstFldLst);
    // CDB2更新数据
    Update(testRelId[0], 0, 2000, true, 1, g_testDbId, CdbId2);
    Validatediff(CdbId2, testRelId[0], 2000, 3, testRelDef[0].pstFldLst);
    // 提交CDB
    ret = TPC_ForceCommitCDB(CdbId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 提交CDB
    ret = TPC_ForceCommitCDB(CdbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验表的数据
    VOS_UINT32 expectvalue[2000] = {0};
    for (int i = 0; i < 2000; i++) {
        expectvalue[i] = i + 1;
    }
    ValidateExpectData(testRelId[0], 2000, &testRelDef[0], expectvalue, true);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
}
// 104.CDB1中删除一条数据至低于阈值，CDB2中更新该数据后强制提交
TEST_F(SharedSpace_Test, V1Com_039_104)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef);
    // 表写入少量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 2000, true);
    }
    // 开启CDB
    VOS_UINT32 CdbId1 = 0;
    ret = TPC_BeginCDB(g_testDbId, &CdbId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 CdbId2 = 0;
    ret = TPC_BeginCDB(g_testDbId, &CdbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // CDB1删除数据
    for (int i = g_DeleteThreshold + 1; i < 2000; i++) {
        Delete(testRelId[0], i, true, 1, g_testDbId, CdbId1);
    }
    Delete(testRelId[0], 0, true, 1, g_testDbId, CdbId1);
    // CDB2更新数据
    Update(testRelId[0], 0, 2000, true, 1, g_testDbId, CdbId2);
    Validatediff(CdbId2, testRelId[0], 2000, 3, testRelDef[0].pstFldLst);
    // 提交CDB
    ret = TPC_ForceCommitCDB(CdbId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 提交CDB
    ret = TPC_ForceCommitCDB(CdbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    SelectView(testRelId[0], false, DB_TPC_SYSVIEW_GET_COMMOM_AREA);
    // 校验表的数据
    VOS_UINT32 expectvalue[g_DeleteThreshold + 1] = {0};
    for (int i = 0; i < g_DeleteThreshold + 1; i++) {
        expectvalue[i] = i + 1;
    }
    expectvalue[g_DeleteThreshold] = 2000;
    ValidateExpectData(testRelId[0], g_DeleteThreshold + 1, &testRelDef[0], expectvalue, true);
    // 进行更新触发迁入迁出
    Update(testRelId[0], 1, 2001, true);
    SelectView(testRelId[0], false, DB_TPC_SYSVIEW_GET_COMMOM_AREA);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
}
// 105.CDB1中删除一条数据，CDB2中删除同一条数据后强制提交
TEST_F(SharedSpace_Test, V1Com_039_105)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef);
    // 表写入大量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 2000, true);
    }
    // 开启CDB
    VOS_UINT32 CdbId1 = 0;
    ret = TPC_BeginCDB(g_testDbId, &CdbId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 CdbId2 = 0;
    ret = TPC_BeginCDB(g_testDbId, &CdbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // CDB1删除数据
    Delete(testRelId[0], 0, true, 1, g_testDbId, CdbId1);
    Validatediff(CdbId1, testRelId[0], 0, 2, testRelDef[0].pstFldLst);
    // CDB2删除数据
    Delete(testRelId[0], 0, true, 1, g_testDbId, CdbId2);
    Validatediff(CdbId2, testRelId[0], 0, 2, testRelDef[0].pstFldLst);
    // 提交CDB
    ret = TPC_ForceCommitCDB(CdbId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 提交CDB
    ret = TPC_ForceCommitCDB(CdbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验表的数据
    VOS_UINT32 expectvalue[1999] = {0};
    for (int i = 0; i < 1999; i++) {
        expectvalue[i] = i + 1;
    }
    ValidateExpectData(testRelId[0], 1999, &testRelDef[0], expectvalue, true);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
}
// 106.CDB1中删除一条数据至低于阈值，CDB2中删除同一条数据后强制提交
TEST_F(SharedSpace_Test, V1Com_039_106)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef);
    // 表写入少量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], g_Threshold, true);
    }
    // 表中删除数据
    for (int i = 0; i < 9; i++) {
        for (int j = g_DeleteThreshold + 1; j < g_Threshold; j++) {
            Delete(testRelId[i], j, true);
        }
    }
    // 开启CDB
    VOS_UINT32 CdbId1 = 0;
    ret = TPC_BeginCDB(g_testDbId, &CdbId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 CdbId2 = 0;
    ret = TPC_BeginCDB(g_testDbId, &CdbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // CDB1删除数据
    Delete(testRelId[0], 0, true, 1, g_testDbId, CdbId1);
    Validatediff(CdbId1, testRelId[0], 0, 2, testRelDef[0].pstFldLst);
    // CDB2删除数据
    Delete(testRelId[0], 0, true, 1, g_testDbId, CdbId2);
    Validatediff(CdbId2, testRelId[0], 0, 2, testRelDef[0].pstFldLst);
    // 提交CDB
    ret = TPC_ForceCommitCDB(CdbId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 提交CDB
    ret = TPC_ForceCommitCDB(CdbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    SelectView(testRelId[0], false, DB_TPC_SYSVIEW_GET_COMMOM_AREA);
    // 校验表的数据
    VOS_UINT32 expectvalue[g_DeleteThreshold] = {0};
    for (int i = 0; i < g_DeleteThreshold; i++) {
        expectvalue[i] = i + 1;
    }
    ValidateExpectData(testRelId[0], g_DeleteThreshold, &testRelDef[0], expectvalue, true);
    // 进行更新触发迁入迁出
    sleep(10);
    Update(testRelId[0], 1, 2001, true);
    SelectView(testRelId[0], true, DB_TPC_SYSVIEW_GET_COMMOM_AREA);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
}
// 107.CDB1中删除一条数据，CDB2中删除不同数据后低于阈值强制提交
TEST_F(SharedSpace_Test, V1Com_039_107)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef);
    // 表写入大量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 2000, true);
    }
    // 开启CDB
    VOS_UINT32 CdbId1 = 0;
    ret = TPC_BeginCDB(g_testDbId, &CdbId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 CdbId2 = 0;
    ret = TPC_BeginCDB(g_testDbId, &CdbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // CDB1删除数据
    for (int i = g_DeleteThreshold + 1; i < 2000; i++) {
        Delete(testRelId[0], i, true, 1, g_testDbId, CdbId1);
    }
    // CDB2删除数据
    Delete(testRelId[0], 0, true, 1, g_testDbId, CdbId2);
    Validatediff(CdbId2, testRelId[0], 0, 2, testRelDef[0].pstFldLst);
    // 提交CDB
    ret = TPC_ForceCommitCDB(CdbId1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 提交CDB
    ret = TPC_ForceCommitCDB(CdbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    SelectView(testRelId[0], false, DB_TPC_SYSVIEW_GET_COMMOM_AREA);
    // 校验表的数据
    VOS_UINT32 expectvalue[g_DeleteThreshold] = {0};
    for (int i = 0; i < g_DeleteThreshold; i++) {
        expectvalue[i] = i + 1;
    }
    ValidateExpectData(testRelId[0], g_DeleteThreshold, &testRelDef[0], expectvalue, true);
    // 进行更新触发迁入迁出
    sleep(10);
    Update(testRelId[0], 1, 2001, true);
    SelectView(testRelId[0], true, DB_TPC_SYSVIEW_GET_COMMOM_AREA);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
}
// 108.开启多个CDB增删改查操作不同的表后依次强制提交
TEST_F(SharedSpace_Test, V1Com_039_108)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef);
    // 表写入少量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 10, true);
    }
    VOS_UINT32 expectvalue[10] = {0};
    for (int i = 0; i < 10; i++) {
        expectvalue[i] = i;
    }
    // 开启CDB增删改查操作不同的表
    VOS_UINT32 CdbId[9] = {0};
    for (int i = 0; i < 9; i++) {
        ret = TPC_BeginCDB(g_testDbId, &CdbId[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 更新数据
        Update(testRelId[i], 0, 10, true, 1, g_testDbId, CdbId[i]);
        // 删除数据
        Delete(testRelId[i], 10, true, 1, g_testDbId, CdbId[i]);
        // 插入数据
        Insert(testRelId[i], 1, true, g_testDbId, CdbId[i]);
        // 查询数据
        selectDataByAllWays(testRelId[i], 1, 0, true, CdbId[i]);
    }
    // 提交CDB
    for (int i = 0; i < 9; i++) {
        ret = TPC_ForceCommitCDB(CdbId[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    SelectView(testRelId[0], true, DB_TPC_SYSVIEW_GET_COMMOM_AREA);
    // 校验表的数据
    for (int i = 0; i < 9; i++) {
        ValidateExpectData(testRelId[i], 10, &testRelDef[i / 3], expectvalue, true);
    }
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
}
// 109.开启多个CDB增删改查操作不同的表，部分表一直插入至超出阈值后依次强制提交
TEST_F(SharedSpace_Test, V1Com_039_109)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef);
    // 表写入少量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 10, true);
    }
    // 开启CDB增删改查操作不同的表
    VOS_UINT32 CdbId[9] = {0};
    for (int i = 0; i < 9; i++) {
        ret = TPC_BeginCDB(g_testDbId, &CdbId[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 更新数据
        Update(testRelId[i], 0, 10, true, 1, g_testDbId, CdbId[i]);
        // 删除数据
        Delete(testRelId[i], 10, true, 1, g_testDbId, CdbId[i]);
        // 插入数据
        Insert(testRelId[i], 1, true, g_testDbId, CdbId[i]);
        if (i % 2 == 0) {
            Insert(testRelId[i], g_Threshold - 10, true, g_testDbId, CdbId[i], 10);
        }
        // 查询数据
        selectDataByAllWays(testRelId[i], 1, 0, true, CdbId[i]);
    }
    // 提交CDB
    for (int i = 0; i < 9; i++) {
        ret = TPC_ForceCommitCDB(CdbId[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 校验是否迁入迁出
    SelectView(testRelId[0], true, DB_TPC_SYSVIEW_GET_COMMOM_AREA);
    SelectView(testRelId[1], true, DB_TPC_SYSVIEW_GET_COMMOM_AREA);
    // 校验表的数据
    for (int i = 0; i < 9; i++) {
        if (i % 2 == 0) {
            VOS_UINT32 expectvalue[g_Threshold] = {0};
            for (int i = 0; i < g_Threshold; i++) {
                expectvalue[i] = i;
            }
            ValidateExpectData(testRelId[i], g_Threshold, &testRelDef[i / 3], expectvalue, true);
        } else {
            VOS_UINT32 expectvalue[10] = {0};
            for (int i = 0; i < 10; i++) {
                expectvalue[i] = i;
            }
            ValidateExpectData(testRelId[i], 10, &testRelDef[i / 3], expectvalue, true);
        }
    }
    // 进行更新触发迁入迁出
    Update(testRelId[0], 1, 2001, true);
    SelectView(testRelId[0], false, DB_TPC_SYSVIEW_GET_COMMOM_AREA);
    Update(testRelId[1], 1, 2001, true);
    SelectView(testRelId[1], true, DB_TPC_SYSVIEW_GET_COMMOM_AREA);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
}
// 110.开启多个CDB增删改查操作不同的表，部分表一直插入至超出阈值后依次强制提交，插入数据的CDB进行回滚
TEST_F(SharedSpace_Test, V1Com_039_110)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef);
    // 表写入少量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 10, true);
    }
    // 开启CDB增删改查操作不同的表
    VOS_UINT32 CdbId[9] = {0};
    for (int i = 0; i < 9; i++) {
        ret = TPC_BeginCDB(g_testDbId, &CdbId[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 更新数据
        Update(testRelId[i], 0, 10, true, 1, g_testDbId, CdbId[i]);
        // 删除数据
        Delete(testRelId[i], 10, true, 1, g_testDbId, CdbId[i]);
        // 插入数据
        Insert(testRelId[i], 1, true, g_testDbId, CdbId[i]);
        if (i % 2 == 0) {
            Insert(testRelId[i], g_Threshold - 10, true, g_testDbId, CdbId[i], 10);
        }
        // 查询数据
        selectDataByAllWays(testRelId[i], 1, 0, true, CdbId[i]);
    }
    // 提交CDB
    for (int i = 0; i < 9; i++) {
        if (i % 2 == 0) {
            ret = TPC_RollbackCDB(CdbId[i]);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        } else {
            ret = TPC_ForceCommitCDB(CdbId[i]);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }
    SelectView(testRelId[0], true, DB_TPC_SYSVIEW_GET_COMMOM_AREA);
    VOS_UINT32 expectvalue[10] = {0};
    for (int i = 0; i < 10; i++) {
        expectvalue[i] = i;
    }
    // 校验表的数据
    for (int i = 0; i < 9; i++) {
        ValidateExpectData(testRelId[i], 10, &testRelDef[i / 3], expectvalue, true);
    }
    // 进行更新触发迁入迁出
    Update(testRelId[0], 1, 2001, true);
    SelectView(testRelId[0], true, DB_TPC_SYSVIEW_GET_COMMOM_AREA);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
}
// 111.开启多个CDB增删改查操作不同的表，部分表一直删除数据至低于阈值后依次强制提交
TEST_F(SharedSpace_Test, V1Com_039_111)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef);
    // 表写入大量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 2000, true);
    }
    // 开启CDB增删改查操作不同的表
    VOS_UINT32 CdbId[9] = {0};
    for (int i = 0; i < 9; i++) {
        ret = TPC_BeginCDB(g_testDbId, &CdbId[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 更新数据
        Update(testRelId[i], 0, 2000, true, 1, g_testDbId, CdbId[i]);
        // 删除数据
        Delete(testRelId[i], 2000, true, 1, g_testDbId, CdbId[i]);
        // 插入数据
        Insert(testRelId[i], 1, true, g_testDbId, CdbId[i]);
        if (i % 2 == 0) {
            for (int j = g_DeleteThreshold; j < 2000; j++) {
                Delete(testRelId[i], j, true, 1, g_testDbId, CdbId[i]);
            }
        }
        // 查询数据
        selectDataByAllWays(testRelId[i], 1, 0, true, CdbId[i]);
    }
    // 提交CDB
    for (int i = 0; i < 9; i++) {
        ret = TPC_ForceCommitCDB(CdbId[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    SelectView(testRelId[0], false, DB_TPC_SYSVIEW_GET_COMMOM_AREA);
    SelectView(testRelId[1], false, DB_TPC_SYSVIEW_GET_COMMOM_AREA);
    // 校验表的数据
    for (int i = 0; i < 9; i++) {
        if (i % 2 == 0) {
            VOS_UINT32 expectvalue[g_DeleteThreshold] = {0};
            for (int i = 0; i < g_DeleteThreshold; i++) {
                expectvalue[i] = i;
            }
            ValidateExpectData(testRelId[i], g_DeleteThreshold, &testRelDef[i / 3], expectvalue, true);
        } else {
            VOS_UINT32 expectvalue[2000] = {0};
            for (int i = 0; i < 2000; i++) {
                expectvalue[i] = i;
            }
            ValidateExpectData(testRelId[i], 2000, &testRelDef[i / 3], expectvalue, true);
        }
    }
    // 进行更新触发迁入迁出
    sleep(10);
    Update(testRelId[0], 1, 2001, true);
    SelectView(testRelId[0], true, DB_TPC_SYSVIEW_GET_COMMOM_AREA);
    Update(testRelId[1], 1, 2001, true);
    SelectView(testRelId[1], false, DB_TPC_SYSVIEW_GET_COMMOM_AREA);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
}
// 112.开启多个CDB增删改查操作不同的表，部分表一直删除数据至低于阈值后依次强制提交，删除数据的CDB进行回滚
TEST_F(SharedSpace_Test, V1Com_039_112)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef);
    // 表写入大量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 2000, true);
    }
    // 开启CDB增删改查操作不同的表
    VOS_UINT32 CdbId[9] = {0};
    for (int i = 0; i < 9; i++) {
        ret = TPC_BeginCDB(g_testDbId, &CdbId[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 更新数据
        Update(testRelId[i], 0, 2000, true, 1, g_testDbId, CdbId[i]);
        // 删除数据
        Delete(testRelId[i], 2000, true, 1, g_testDbId, CdbId[i]);
        // 插入数据
        Insert(testRelId[i], 1, true, g_testDbId, CdbId[i]);
        if (i % 2 == 0) {
            for (int j = g_DeleteThreshold; j < 2000; j++) {
                Delete(testRelId[i], j, true, 1, g_testDbId, CdbId[i]);
            }
        }
        // 查询数据
        selectDataByAllWays(testRelId[i], 1, 0, true, CdbId[i]);
    }
    // 提交CDB
    for (int i = 0; i < 9; i++) {
        if (i % 2 == 0) {
            ret = TPC_RollbackCDB(CdbId[i]);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        } else {
            ret = TPC_ForceCommitCDB(CdbId[i]);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }
    SelectView(testRelId[0], false, DB_TPC_SYSVIEW_GET_COMMOM_AREA);
    // 校验表的数据
    for (int i = 0; i < 9; i++) {
        VOS_UINT32 expectvalue[2000] = {0};
        for (int i = 0; i < 2000; i++) {
            expectvalue[i] = i;
        }
        ValidateExpectData(testRelId[i], 2000, &testRelDef[i / 3], expectvalue, true);
    }
    // 进行更新触发迁入迁出
    sleep(10);
    Update(testRelId[0], 1, 2001, true);
    SelectView(testRelId[0], false, DB_TPC_SYSVIEW_GET_COMMOM_AREA);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
}
// 113.持久化DB中部分表在共享空间时的导出，replace进行导入
TEST_F(SharedSpace_Test, V1Com_039_113)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    char testDbName[DB_NAME_LEN] = "testDb";
    g_testDbCfg.enPersistent = DB_CKP_COMPLETE;
    char filePath[64] = "./filePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    g_testDbCfg.enPersistent = DB_CKP_NONE;
    uint32_t testDbId = 0;
    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef, testDbId);
    // 部分表写入大量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 10, true, testDbId);
        if (i % 2 == 0) {
            Insert(testRelId[i], 1990, true, testDbId, TPC_GLOBAL_CDB, 10);
        }
    }
    // 导出DB
    char exportFile[64] = "./filePath/export.txt";
    ret = TPC_BkpPhy(testDbId, (uint8_t *)exportFile);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // replace进行导入DB
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = TPC_Restore(
        (uint8_t *)exportFile, (VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验数据
    for (int i = 0; i < 9; i++) {
        if (i % 2 == 0) {
            VOS_UINT32 expectvalue[2000] = {0};
            for (int i = 0; i < 2000; i++) {
                expectvalue[i] = i;
            }
            ValidateExpectData(testRelId[i], 2000, &testRelDef[i / 3], expectvalue, true, testDbId);
        } else {
            VOS_UINT32 expectvalue[10] = {0};
            for (int i = 0; i < 10; i++) {
                expectvalue[i] = i;
            }
            ValidateExpectData(testRelId[i], 10, &testRelDef[i / 3], expectvalue, true, testDbId);
        }
    }
    // 校验是否在共享空间中
    SelectView(testRelId[0], false, DB_TPC_SYSVIEW_GET_COMMOM_AREA, testDbId);
    SelectView(testRelId[1], true, DB_TPC_SYSVIEW_GET_COMMOM_AREA, testDbId);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
    // 关闭DB
    ret = TPC_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)testDbName, TPC_WAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
}
// 114.持久化DB中部分表在共享空间时的导出，discard进行导入
TEST_F(SharedSpace_Test, V1Com_039_114)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    char testDbName[DB_NAME_LEN] = "testDb";
    g_testDbCfg.enPersistent = DB_CKP_COMPLETE;
    char filePath[64] = "./filePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    g_testDbCfg.enPersistent = DB_CKP_NONE;
    uint32_t testDbId = 0;
    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef, testDbId);
    // 部分表写入大量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 10, true, testDbId);
        if (i % 2 == 0) {
            Insert(testRelId[i], 1990, true, testDbId, TPC_GLOBAL_CDB, 10);
        }
    }
    // 导出DB
    char exportFile[64] = "./filePath/export.txt";
    ret = TPC_BkpPhy(testDbId, (uint8_t *)exportFile);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 关闭DB
    ret = TPC_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)testDbName, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // discard进行导入DB
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = TPC_Restore(
        (uint8_t *)exportFile, (VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, DB_RESTORETYPE_DISCARD, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验数据
    for (int i = 0; i < 9; i++) {
        if (i % 2 == 0) {
            VOS_UINT32 expectvalue[2000] = {0};
            for (int i = 0; i < 2000; i++) {
                expectvalue[i] = i;
            }
            ValidateExpectData(testRelId[i], 2000, &testRelDef[i / 3], expectvalue, true, testDbId);
        } else {
            VOS_UINT32 expectvalue[10] = {0};
            for (int i = 0; i < 10; i++) {
                expectvalue[i] = i;
            }
            ValidateExpectData(testRelId[i], 10, &testRelDef[i / 3], expectvalue, true, testDbId);
        }
    }
    // 校验是否在共享空间中
    SelectView(testRelId[0], false, DB_TPC_SYSVIEW_GET_COMMOM_AREA, testDbId);
    SelectView(testRelId[1], true, DB_TPC_SYSVIEW_GET_COMMOM_AREA, testDbId);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
    // 关闭DB
    ret = TPC_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)testDbName, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
}
// 115.持久化DB中所有表在共享空间时的导出，replace进行导入
TEST_F(SharedSpace_Test, V1Com_039_115)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    char testDbName[DB_NAME_LEN] = "testDb";
    g_testDbCfg.enPersistent = DB_CKP_COMPLETE;
    char filePath[64] = "./filePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    g_testDbCfg.enPersistent = DB_CKP_NONE;
    uint32_t testDbId = 0;
    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef, testDbId);
    // 所有表写入少量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 10, true, testDbId);
    }
    // 导出DB
    char exportFile[64] = "./filePath/export.txt";
    ret = TPC_BkpPhy(testDbId, (uint8_t *)exportFile);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // replace进行导入DB
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = TPC_Restore(
        (uint8_t *)exportFile, (VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验数据
    for (int i = 0; i < 9; i++) {
        VOS_UINT32 expectvalue[10] = {0};
        for (int i = 0; i < 10; i++) {
            expectvalue[i] = i;
        }
        ValidateExpectData(testRelId[i], 10, &testRelDef[i / 3], expectvalue, true, testDbId);
    }
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
    // 关闭DB
    ret = TPC_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)testDbName, TPC_WAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
}
// 116.持久化DB中所有表在共享空间时的导出，discard进行导入
TEST_F(SharedSpace_Test, V1Com_039_116)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    char testDbName[DB_NAME_LEN] = "testDb";
    g_testDbCfg.enPersistent = DB_CKP_COMPLETE;
    char filePath[64] = "./filePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    g_testDbCfg.enPersistent = DB_CKP_NONE;
    uint32_t testDbId = 0;
    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef, testDbId);
    // 所有表写入少量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 10, true, testDbId);
    }
    // 导出DB
    char exportFile[64] = "./filePath/export.txt";
    ret = TPC_BkpPhy(testDbId, (uint8_t *)exportFile);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 关闭DB
    ret = TPC_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)testDbName, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // discard进行导入DB
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = TPC_Restore(
        (uint8_t *)exportFile, (VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, DB_RESTORETYPE_DISCARD, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验数据
    for (int i = 0; i < 9; i++) {
        VOS_UINT32 expectvalue[10] = {0};
        for (int i = 0; i < 10; i++) {
            expectvalue[i] = i;
        }
        ValidateExpectData(testRelId[i], 10, &testRelDef[i / 3], expectvalue, true, testDbId);
    }
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
    // 关闭DB
    ret = TPC_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)testDbName, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
}
// 117.持久化DB中所有表在共享空间时的导出，discard进行导入后进行增删改查操作
TEST_F(SharedSpace_Test, V1Com_039_117)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    char testDbName[DB_NAME_LEN] = "testDb";
    g_testDbCfg.enPersistent = DB_CKP_COMPLETE;
    char filePath[64] = "./filePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    g_testDbCfg.enPersistent = DB_CKP_NONE;
    uint32_t testDbId = 0;
    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef, testDbId);
    // 所有表写入少量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 10, true, testDbId);
    }
    // 导出DB
    char exportFile[64] = "./filePath/export.txt";
    ret = TPC_BkpPhy(testDbId, (uint8_t *)exportFile);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 关闭DB
    ret = TPC_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)testDbName, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // discard进行导入DB
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
    ret = TPC_Restore(
        (uint8_t *)exportFile, (VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, DB_RESTORETYPE_DISCARD, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验数据
    for (int i = 0; i < 9; i++) {
        VOS_UINT32 expectvalue[10] = {0};
        for (int i = 0; i < 10; i++) {
            expectvalue[i] = i;
        }
        ValidateExpectData(testRelId[i], 10, &testRelDef[i / 3], expectvalue, true, testDbId);
    }
    // 开启CDB增删改查操作不同的表
    VOS_UINT32 CdbId[6] = {0};
    for (int i = 0; i < 9; i++) {
        ret = TPC_BeginCDB(testDbId, &CdbId[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 更新数据
        Update(testRelId[i], 0, 10, true, 1, testDbId, CdbId[i]);
        // 删除数据
        Delete(testRelId[i], 10, true, 1, testDbId, CdbId[i]);
        // 插入数据
        Insert(testRelId[i], 1, true, testDbId, CdbId[i]);
        // 查询数据
        selectDataByAllWays(testRelId[i], 1, 0, true, CdbId[i]);
    }
    // 提交CDB
    for (int i = 0; i < 9; i++) {
        ret = TPC_ForceCommitCDB(CdbId[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 校验数据
    for (int i = 0; i < 9; i++) {
        VOS_UINT32 expectvalue[10] = {0};
        for (int i = 0; i < 10; i++) {
            expectvalue[i] = i;
        }
        ValidateExpectData(testRelId[i], 10, &testRelDef[i / 3], expectvalue, true, testDbId);
    }
    // 验证所有表是否在共享空间
    for (int i = 0; i < 9; i++) {
        SelectView(testRelId[i], true, DB_TPC_SYSVIEW_GET_COMMOM_AREA, testDbId);
    }

    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
    // 关闭DB
    ret = TPC_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)testDbName, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
}
// 118.持久化DB中部分表在共享空间时导出为大端数据，replace进行导入
TEST_F(SharedSpace_Test, V1Com_039_118)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    DB_REG_FEATURE_STRU pstFeature = {0};
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 1, 1, 1};
    SelectcfgInterface(&pstFeature, selectways);
    // 注册持久化DB
    DB_INST_CONFIG_STRU enConfigStructure = {0};
    enConfigStructure.enPersistent = DB_CKP_COMPLETE;
    ret = DB_RegDbConfigStructure(&enConfigStructure, &pstFeature);
    // 注册导入持久化
    ret = DB_RegDbRestoreConfig(true, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 注册导入方式为replace
    DB_RESTORETYPE_ENUM enRestoreType = DB_RESTORETYPE_REPLACE;
    ret = DB_RegDbRestoreTypeCfg(enRestoreType, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    char testDbName[DB_NAME_LEN] = "testDb";
    char filePath[64] = "./filePath/perDbPath";
    uint32_t testDbId = 0;
    // 建DB
    ret = DB_CreateDB2((VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(false, testRelId, testRelDef, testDbId);
    // 部分表写入大量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 10, false, testDbId);
        if (i % 2 == 0) {
            Insert(testRelId[i], 1990, false, testDbId, TPC_GLOBAL_CDB, 10);
        }
    }
    // 导出为大端数据
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    char exportFile[64] = "./filePath/export.db";
    ret = DB_BkpPhyEx(testDbId, (uint8_t *)exportFile, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 导入DB
    ret = DB_RestoreDB2((uint8_t *)exportFile, (VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 调用DB_CheckDBRel校验表的CRC值
    VOS_UINT16 usFailRelId = 0;
    VOS_UINT32 ulRelCount = 9;
    ret = DB_CheckDBRel(testDbId, testRelId, ulRelCount, &usFailRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0xFFFF, usFailRelId);
    for (int i = 0; i < 9; i++) {
        if (i % 2 == 0) {
            VOS_UINT32 expectvalue[2000] = {0};
            for (int i = 0; i < 2000; i++) {
                expectvalue[i] = i;
            }
            ValidateExpectData(testRelId[i], 2000, &testRelDef[i / 3], expectvalue, false, testDbId);
        } else {
            VOS_UINT32 expectvalue[10] = {0};
            for (int i = 0; i < 10; i++) {
                expectvalue[i] = i;
            }
            ValidateExpectData(testRelId[i], 10, &testRelDef[i / 3], expectvalue, false, testDbId);
        }
    }
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
    // 关闭DB
    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
}
// 119.持久化DB中部分表在共享空间时导出为小端数据，replace进行导入
TEST_F(SharedSpace_Test, V1Com_039_119)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    DB_REG_FEATURE_STRU pstFeature = {0};
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 1, 1, 1};
    SelectcfgInterface(&pstFeature, selectways);
    // 注册持久化DB
    DB_INST_CONFIG_STRU enConfigStructure = {0};
    enConfigStructure.enPersistent = DB_CKP_COMPLETE;
    ret = DB_RegDbConfigStructure(&enConfigStructure, &pstFeature);
    // 注册导入持久化
    ret = DB_RegDbRestoreConfig(true, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 注册导入方式为replace
    DB_RESTORETYPE_ENUM enRestoreType = DB_RESTORETYPE_REPLACE;
    ret = DB_RegDbRestoreTypeCfg(enRestoreType, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    char testDbName[DB_NAME_LEN] = "testDb";
    char filePath[64] = "./filePath/perDbPath";
    uint32_t testDbId = 0;
    // 建DB
    ret = DB_CreateDB2((VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(false, testRelId, testRelDef, testDbId);
    // 部分表写入大量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 10, false, testDbId);
        if (i % 2 == 0) {
            Insert(testRelId[i], 1990, false, testDbId, TPC_GLOBAL_CDB, 10);
        }
    }
    // 导出为小端数据
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char exportFile[64] = "./filePath/export.db";
    ret = DB_BkpPhyEx(testDbId, (uint8_t *)exportFile, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 导入DB
    ret = DB_RestoreDB2((uint8_t *)exportFile, (VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 调用DB_CheckDBRel校验表的CRC值
    VOS_UINT16 usFailRelId = 0;
    VOS_UINT32 ulRelCount = 9;
    ret = DB_CheckDBRel(testDbId, testRelId, ulRelCount, &usFailRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0xFFFF, usFailRelId);
    // 校验数据
    for (int i = 0; i < 9; i++) {
        if (i % 2 == 0) {
            VOS_UINT32 expectvalue[2000] = {0};
            for (int i = 0; i < 2000; i++) {
                expectvalue[i] = i;
            }
            ValidateExpectData(testRelId[i], 2000, &testRelDef[i / 3], expectvalue, false, testDbId);
        } else {
            VOS_UINT32 expectvalue[10] = {0};
            for (int i = 0; i < 10; i++) {
                expectvalue[i] = i;
            }
            ValidateExpectData(testRelId[i], 10, &testRelDef[i / 3], expectvalue, false, testDbId);
        }
    }
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
    // 关闭DB
    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
}
// 120.持久化DB中部分表在共享空间时导出为大端数据，discard进行导入
TEST_F(SharedSpace_Test, V1Com_039_120)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    DB_REG_FEATURE_STRU pstFeature = {0};
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 1, 1, 1};
    SelectcfgInterface(&pstFeature, selectways);
    // 注册持久化DB
    DB_INST_CONFIG_STRU enConfigStructure = {0};
    enConfigStructure.enPersistent = DB_CKP_COMPLETE;
    ret = DB_RegDbConfigStructure(&enConfigStructure, &pstFeature);
    // 注册导入持久化
    ret = DB_RegDbRestoreConfig(true, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    char testDbName[DB_NAME_LEN] = "testDb";
    char filePath[64] = "./filePath/perDbPath";
    uint32_t testDbId = 0;
    // 建DB
    ret = DB_CreateDB2((VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(false, testRelId, testRelDef, testDbId);
    // 部分表写入大量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 10, false, testDbId);
        if (i % 2 == 0) {
            Insert(testRelId[i], 1990, false, testDbId, TPC_GLOBAL_CDB, 10);
        }
    }
    // 导出为大端数据
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    char exportFile[64] = "./filePath/export.db";
    ret = DB_BkpPhyEx(testDbId, (uint8_t *)exportFile, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 关闭DB
    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 导入DB
    ret = DB_RestoreDB2((uint8_t *)exportFile, (VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 调用DB_CheckDBRel校验表的CRC值
    VOS_UINT16 usFailRelId = 0;
    VOS_UINT32 ulRelCount = 9;
    ret = DB_CheckDBRel(testDbId, testRelId, ulRelCount, &usFailRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0xFFFF, usFailRelId);
    // 校验数据
    for (int i = 0; i < 9; i++) {
        if (i % 2 == 0) {
            VOS_UINT32 expectvalue[2000] = {0};
            for (int i = 0; i < 2000; i++) {
                expectvalue[i] = i;
            }
            ValidateExpectData(testRelId[i], 2000, &testRelDef[i / 3], expectvalue, false, testDbId);
        } else {
            VOS_UINT32 expectvalue[10] = {0};
            for (int i = 0; i < 10; i++) {
                expectvalue[i] = i;
            }
            ValidateExpectData(testRelId[i], 10, &testRelDef[i / 3], expectvalue, false, testDbId);
        }
    }
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
    // 关闭DB
    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
}
// 121.持久化DB中部分表在共享空间时导出为小端数据，discard进行导入
TEST_F(SharedSpace_Test, V1Com_039_121)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    DB_REG_FEATURE_STRU pstFeature = {0};
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 1, 1, 1};
    SelectcfgInterface(&pstFeature, selectways);
    // 注册持久化DB
    DB_INST_CONFIG_STRU enConfigStructure = {0};
    enConfigStructure.enPersistent = DB_CKP_COMPLETE;
    ret = DB_RegDbConfigStructure(&enConfigStructure, &pstFeature);
    // 注册导入持久化
    ret = DB_RegDbRestoreConfig(true, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    char testDbName[DB_NAME_LEN] = "testDb";
    char filePath[64] = "./filePath/perDbPath";
    uint32_t testDbId = 0;
    // 建DB
    ret = DB_CreateDB2((VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(false, testRelId, testRelDef, testDbId);
    // 部分表写入大量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 10, false, testDbId);
        if (i % 2 == 0) {
            Insert(testRelId[i], 1990, false, testDbId, TPC_GLOBAL_CDB, 10);
        }
    }
    // 导出为小端数据
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char exportFile[64] = "./filePath/export.db";
    ret = DB_BkpPhyEx(testDbId, (uint8_t *)exportFile, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 关闭DB
    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 导入DB
    ret = DB_RestoreDB2((uint8_t *)exportFile, (VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 调用DB_CheckDBRel校验表的CRC值
    VOS_UINT16 usFailRelId = 0;
    VOS_UINT32 ulRelCount = 9;
    ret = DB_CheckDBRel(testDbId, testRelId, ulRelCount, &usFailRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0xFFFF, usFailRelId);
    // 校验数据
    for (int i = 0; i < 9; i++) {
        if (i % 2 == 0) {
            VOS_UINT32 expectvalue[2000] = {0};
            for (int i = 0; i < 2000; i++) {
                expectvalue[i] = i;
            }
            ValidateExpectData(testRelId[i], 2000, &testRelDef[i / 3], expectvalue, false, testDbId);
        } else {
            VOS_UINT32 expectvalue[10] = {0};
            for (int i = 0; i < 10; i++) {
                expectvalue[i] = i;
            }
            ValidateExpectData(testRelId[i], 10, &testRelDef[i / 3], expectvalue, false, testDbId);
        }
    }
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
    // 关闭DB
    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
}
// 122.持久化DB中所有表在共享空间时导出为大端数据，replace进行导入
TEST_F(SharedSpace_Test, V1Com_039_122)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    DB_REG_FEATURE_STRU pstFeature = {0};
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 1, 1, 1};
    SelectcfgInterface(&pstFeature, selectways);
    // 注册持久化DB
    DB_INST_CONFIG_STRU enConfigStructure = {0};
    enConfigStructure.enPersistent = DB_CKP_COMPLETE;
    ret = DB_RegDbConfigStructure(&enConfigStructure, &pstFeature);
    // 注册导入持久化
    ret = DB_RegDbRestoreConfig(true, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 注册导入方式为replace
    DB_RESTORETYPE_ENUM enRestoreType = DB_RESTORETYPE_REPLACE;
    ret = DB_RegDbRestoreTypeCfg(enRestoreType, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    char testDbName[DB_NAME_LEN] = "testDb";
    char filePath[64] = "./filePath/perDbPath";
    uint32_t testDbId = 0;
    // 建DB
    ret = DB_CreateDB2((VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(false, testRelId, testRelDef, testDbId);
    // 所有表写入少量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 10, false, testDbId);
    }
    // 导出为大端数据
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    char exportFile[64] = "./filePath/export.db";
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &pstFileSaveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = true;
    ret = DB_PhyBkp2(testDbId, (uint8_t *)exportFile, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // CRC校验
    ret = DB_CheckDataFile((VOS_UINT8 *)exportFile, DB_CHECK_BASIC);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 导入DB
    ret = DB_RestoreDB2((uint8_t *)exportFile, (VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 调用DB_CheckDBRel校验表的CRC值
    VOS_UINT16 usFailRelId = 0;
    VOS_UINT32 ulRelCount = 9;
    ret = DB_CheckDBRel(testDbId, testRelId, ulRelCount, &usFailRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0xFFFF, usFailRelId);
    VOS_UINT32 expectvalue[10] = {0};
    for (int i = 0; i < 10; i++) {
        expectvalue[i] = i;
    }
    // 校验数据
    for (int i = 0; i < 9; i++) {
        ValidateExpectData(testRelId[i], 10, &testRelDef[i / 3], expectvalue, false, testDbId);
    }
    // 验证所有表是否在共享空间
    for (int i = 0; i < 9; i++) {
        SelectView(testRelId[i], true, DB_TPC_SYSVIEW_GET_COMMOM_AREA, testDbId);
    }
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
    // 关闭DB
    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
}
// 123.持久化DB中所有表在共享空间时导出为小端数据，replace进行导入
TEST_F(SharedSpace_Test, V1Com_039_123)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    DB_REG_FEATURE_STRU pstFeature = {0};
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 1, 1, 1};
    SelectcfgInterface(&pstFeature, selectways);
    // 注册持久化DB
    DB_INST_CONFIG_STRU enConfigStructure = {0};
    enConfigStructure.enPersistent = DB_CKP_COMPLETE;
    ret = DB_RegDbConfigStructure(&enConfigStructure, &pstFeature);
    // 注册导入持久化
    ret = DB_RegDbRestoreConfig(true, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 注册导入方式为replace
    DB_RESTORETYPE_ENUM enRestoreType = DB_RESTORETYPE_REPLACE;
    ret = DB_RegDbRestoreTypeCfg(enRestoreType, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    char testDbName[DB_NAME_LEN] = "testDb";
    char filePath[64] = "./filePath/perDbPath";
    uint32_t testDbId = 0;
    // 建DB
    ret = DB_CreateDB2((VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(false, testRelId, testRelDef, testDbId);
    // 所有表写入少量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 10, false, testDbId);
    }
    // 导出为小端数据
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char exportFile[64] = "./filePath/export.db";
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &pstFileSaveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = true;
    ret = DB_PhyBkp2(testDbId, (uint8_t *)exportFile, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // CRC校验
    ret = DB_CheckDataFile((VOS_UINT8 *)exportFile, DB_CHECK_BASIC);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 导入DB
    ret = DB_RestoreDB2((uint8_t *)exportFile, (VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 调用DB_CheckDBRel校验表的CRC值
    VOS_UINT16 usFailRelId = 0;
    VOS_UINT32 ulRelCount = 9;
    ret = DB_CheckDBRel(testDbId, testRelId, ulRelCount, &usFailRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0xFFFF, usFailRelId);
    VOS_UINT32 expectvalue[10] = {0};
    for (int i = 0; i < 10; i++) {
        expectvalue[i] = i;
    }
    // 校验数据
    for (int i = 0; i < 9; i++) {
        ValidateExpectData(testRelId[i], 10, &testRelDef[i / 3], expectvalue, false, testDbId);
    }
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
    // 关闭DB
    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
}
// 124.持久化DB中所有表在共享空间时导出为大端数据，discard进行导入
TEST_F(SharedSpace_Test, V1Com_039_124)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    DB_REG_FEATURE_STRU pstFeature = {0};
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 1, 1, 1};
    SelectcfgInterface(&pstFeature, selectways);
    // 注册持久化DB
    DB_INST_CONFIG_STRU enConfigStructure = {0};
    enConfigStructure.enPersistent = DB_CKP_COMPLETE;
    ret = DB_RegDbConfigStructure(&enConfigStructure, &pstFeature);
    // 注册导入持久化
    ret = DB_RegDbRestoreConfig(true, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    char testDbName[DB_NAME_LEN] = "testDb";
    char filePath[64] = "./filePath/perDbPath";
    uint32_t testDbId = 0;
    // 建DB
    ret = DB_CreateDB2((VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(false, testRelId, testRelDef, testDbId);
    // 所有表写入少量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 10, false, testDbId);
    }
    // 导出为大端数据
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    char exportFile[64] = "./filePath/export.db";
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &pstFileSaveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = true;
    ret = DB_PhyBkp2(testDbId, (uint8_t *)exportFile, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // CRC校验
    ret = DB_CheckDataFile((VOS_UINT8 *)exportFile, DB_CHECK_BASIC);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 关闭DB
    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 导入DB
    ret = DB_RestoreDB2((uint8_t *)exportFile, (VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 调用DB_CheckDBRel校验表的CRC值
    VOS_UINT16 usFailRelId = 0;
    VOS_UINT32 ulRelCount = 9;
    ret = DB_CheckDBRel(testDbId, testRelId, ulRelCount, &usFailRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0xFFFF, usFailRelId);
    VOS_UINT32 expectvalue[10] = {0};
    for (int i = 0; i < 10; i++) {
        expectvalue[i] = i;
    }
    // 校验数据
    for (int i = 0; i < 9; i++) {
        ValidateExpectData(testRelId[i], 10, &testRelDef[i / 3], expectvalue, false, testDbId);
    }
    // 验证所有表是否在共享空间
    for (int i = 0; i < 9; i++) {
        SelectView(testRelId[i], true, DB_TPC_SYSVIEW_GET_COMMOM_AREA, testDbId);
    }
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
    // 关闭DB
    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
}
// 125.持久化DB中所有表在共享空间时导出为小端数据，discard进行导入
TEST_F(SharedSpace_Test, V1Com_039_125)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    DB_REG_FEATURE_STRU pstFeature = {0};
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 1, 1, 1};
    SelectcfgInterface(&pstFeature, selectways);
    // 注册持久化DB
    DB_INST_CONFIG_STRU enConfigStructure = {0};
    enConfigStructure.enPersistent = DB_CKP_COMPLETE;
    ret = DB_RegDbConfigStructure(&enConfigStructure, &pstFeature);
    // 注册导入持久化
    ret = DB_RegDbRestoreConfig(true, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    char testDbName[DB_NAME_LEN] = "testDb";
    char filePath[64] = "./filePath/perDbPath";
    uint32_t testDbId = 0;
    // 建DB
    ret = DB_CreateDB2((VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(false, testRelId, testRelDef, testDbId);
    // 所有表写入少量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 10, false, testDbId);
    }
    // 导出为小端数据
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char exportFile[64] = "./filePath/export.db";
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &pstFileSaveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = true;
    ret = DB_PhyBkp2(testDbId, (uint8_t *)exportFile, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // CRC校验
    ret = DB_CheckDataFile((VOS_UINT8 *)exportFile, DB_CHECK_BASIC);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 关闭DB
    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 导入DB
    ret = DB_RestoreDB2((uint8_t *)exportFile, (VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 调用DB_CheckDBRel校验表的CRC值
    VOS_UINT16 usFailRelId = 0;
    VOS_UINT32 ulRelCount = 9;
    ret = DB_CheckDBRel(testDbId, testRelId, ulRelCount, &usFailRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0xFFFF, usFailRelId);
    VOS_UINT32 expectvalue[10] = {0};
    for (int i = 0; i < 10; i++) {
        expectvalue[i] = i;
    }
    // 校验数据
    for (int i = 0; i < 9; i++) {
        ValidateExpectData(testRelId[i], 10, &testRelDef[i / 3], expectvalue, false, testDbId);
    }
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
    // 关闭DB
    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
}
VOS_UINT32 PersistEndianTransForBit32(VOS_UINT32 transVal)
{
    transVal = ((transVal << 8) & 0xFF00FF00) | ((transVal >> 8) & 0x00FF00FF);
    return (transVal >> 16) | (transVal << 16);
}
VOS_VOID CustomFld88ConvHook(VOS_UINT8 ucFldId, VOS_UINT16 usDataType, VOS_UINT32 ulStoredLen, VOS_UINT8 *pucData)
{
    // uint32_t uint32_t 分别做大小端转换
    VOS_UINT32 val1 = *((VOS_UINT32 *)pucData);
    *((VOS_UINT32 *)pucData) = PersistEndianTransForBit32(val1);
    VOS_UINT32 val2 = *((VOS_UINT32 *)(pucData + 4));
    *((VOS_UINT32 *)(pucData + 4)) = PersistEndianTransForBit32(val2);
}

uint16_t g_table1Id = 0;
DB_FldConvHook g_fldHookArray[4] = {NULL, NULL, CustomFld88ConvHook, CustomFld88ConvHook};
DB_FldConvHook *GetTblConvHook1(VOS_UINT16 usRelId)
{
    return g_fldHookArray;
}
// 126.持久化DB中存在自定义数据和block类型时，所有表在共享空间时导出为大端数据，replace进行导入
TEST_F(SharedSpace_Test, V1Com_039_126)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    // 创建自定义数据类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 32;
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    DB_REG_FEATURE_STRU pstFeature = {0};
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 1, 1, 1};
    SelectcfgInterface(&pstFeature, selectways);
    // 注册持久化DB
    DB_INST_CONFIG_STRU enConfigStructure = {0};
    enConfigStructure.enPersistent = DB_CKP_COMPLETE;
    ret = DB_RegDbConfigStructure(&enConfigStructure, &pstFeature);
    // 注册导入持久化
    ret = DB_RegDbRestoreConfig(true, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 注册导入方式为replace
    DB_RESTORETYPE_ENUM enRestoreType = DB_RESTORETYPE_REPLACE;
    ret = DB_RegDbRestoreTypeCfg(enRestoreType, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    char testDbName[DB_NAME_LEN] = "testDb";
    char filePath[64] = "./filePath/perDbPath";
    uint32_t testDbId = 0;
    // 建DB
    ret = DB_CreateDB2((VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建多张表
    uint16_t testRelId[10] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(false, testRelId, testRelDef, testDbId);
    // 创建自定义数据类型表
    DB_REL_DEF_STRU *testRelDef1 = NULL;
    ret = TestDB_CreateTbl(testDbId, "schema_file/vertexlabel4.json", &testRelId[9], testRelDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 所有表写入少量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 10, false, testDbId);
    }
    // 自定义数据类型表写入少量数据
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(testDbId, testRelId[9], &tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);
    for (int i = 0; i < 10; i++) {
        uint32_t fldVal[4];
        for (uint32_t j = 0; j < 4; j++) {
            fldVal[j] = i;
        }
        (void)memcpy_s(recBuf, tblRecLen, fldVal, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(testDbId, testRelId[9], &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 导出为大端数据
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    char exportFile[64] = "./filePath/export.db";
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook1;
    ret = DB_BkpPhyWithDataConvHook(testDbId, (uint8_t *)exportFile, &pstFileSaveOpt, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 导入DB
    ret = DB_RestoreDB2((uint8_t *)exportFile, (VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 调用DB_CheckDBRel校验表的CRC值
    VOS_UINT16 usFailRelId = 0;
    VOS_UINT32 ulRelCount = 9;
    ret = DB_CheckDBRel(testDbId, testRelId, ulRelCount, &usFailRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0xFFFF, usFailRelId);
    VOS_UINT32 expectvalue[10] = {0};
    for (int i = 0; i < 10; i++) {
        expectvalue[i] = i;
    }
    // 校验数据
    for (int i = 0; i < 9; i++) {
        ValidateExpectData(testRelId[i], 10, &testRelDef[i / 3], expectvalue, false, testDbId);
    }
    // 验证所有表是否在共享空间
    for (int i = 0; i < 9; i++) {
        SelectView(testRelId[i], true, DB_TPC_SYSVIEW_GET_COMMOM_AREA, testDbId);
    }
    TEST_V1_FREE(recBuf);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
    // 关闭DB
    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
}
// 127.持久化DB中存在自定义数据和block类型时，所有表在共享空间时导出为小端数据，replace进行导入
TEST_F(SharedSpace_Test, V1Com_039_127)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    // 创建自定义数据类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 32;
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    DB_REG_FEATURE_STRU pstFeature = {0};
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 1, 1, 1};
    SelectcfgInterface(&pstFeature, selectways);
    // 注册持久化DB
    DB_INST_CONFIG_STRU enConfigStructure = {0};
    enConfigStructure.enPersistent = DB_CKP_COMPLETE;
    ret = DB_RegDbConfigStructure(&enConfigStructure, &pstFeature);
    // 注册导入持久化
    ret = DB_RegDbRestoreConfig(true, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 注册导入方式为replace
    DB_RESTORETYPE_ENUM enRestoreType = DB_RESTORETYPE_REPLACE;
    ret = DB_RegDbRestoreTypeCfg(enRestoreType, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    char testDbName[DB_NAME_LEN] = "testDb";
    char filePath[64] = "./filePath/perDbPath";
    uint32_t testDbId = 0;
    // 建DB
    ret = DB_CreateDB2((VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建多张表
    uint16_t testRelId[10] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(false, testRelId, testRelDef, testDbId);
    // 创建自定义数据类型表
    DB_REL_DEF_STRU *testRelDef1 = NULL;
    ret = TestDB_CreateTbl(testDbId, "schema_file/vertexlabel4.json", &testRelId[9], testRelDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 所有表写入少量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 10, false, testDbId);
    }
    // 自定义数据类型表写入少量数据
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(testDbId, testRelId[9], &tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);
    for (int i = 0; i < 10; i++) {
        uint32_t fldVal[4];
        for (uint32_t j = 0; j < 4; j++) {
            fldVal[j] = i;
        }
        (void)memcpy_s(recBuf, tblRecLen, fldVal, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(testDbId, testRelId[9], &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 导出为小端数据
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char exportFile[64] = "./filePath/export.db";
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook1;
    ret = DB_BkpPhyWithDataConvHook(testDbId, (uint8_t *)exportFile, &pstFileSaveOpt, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 导入DB
    ret = DB_RestoreDB2((uint8_t *)exportFile, (VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 调用DB_CheckDBRel校验表的CRC值
    VOS_UINT16 usFailRelId = 0;
    VOS_UINT32 ulRelCount = 9;
    ret = DB_CheckDBRel(testDbId, testRelId, ulRelCount, &usFailRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0xFFFF, usFailRelId);
    VOS_UINT32 expectvalue[10] = {0};
    for (int i = 0; i < 10; i++) {
        expectvalue[i] = i;
    }
    // 校验数据
    for (int i = 0; i < 9; i++) {
        ValidateExpectData(testRelId[i], 10, &testRelDef[i / 3], expectvalue, false, testDbId);
    }
    TEST_V1_FREE(recBuf);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
    // 关闭DB
    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
}
// 128.持久化DB中存在自定义数据和block类型时，所有表在共享空间时导出为大端数据，discard进行导入
TEST_F(SharedSpace_Test, V1Com_039_128)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    // 创建自定义数据类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 32;
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    DB_REG_FEATURE_STRU pstFeature = {0};
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 1, 1, 1};
    SelectcfgInterface(&pstFeature, selectways);
    // 注册持久化DB
    DB_INST_CONFIG_STRU enConfigStructure = {0};
    enConfigStructure.enPersistent = DB_CKP_COMPLETE;
    ret = DB_RegDbConfigStructure(&enConfigStructure, &pstFeature);
    // 注册导入持久化
    ret = DB_RegDbRestoreConfig(true, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    char testDbName[DB_NAME_LEN] = "testDb";
    char filePath[64] = "./filePath/perDbPath";
    uint32_t testDbId = 0;
    // 建DB
    ret = DB_CreateDB2((VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建多张表
    uint16_t testRelId[10] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(false, testRelId, testRelDef, testDbId);
    // 创建自定义数据类型表
    DB_REL_DEF_STRU *testRelDef1 = NULL;
    ret = TestDB_CreateTbl(testDbId, "schema_file/vertexlabel4.json", &testRelId[9], testRelDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 所有表写入少量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 10, false, testDbId);
    }
    // 自定义数据类型表写入少量数据
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(testDbId, testRelId[9], &tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);
    for (int i = 0; i < 10; i++) {
        uint32_t fldVal[4];
        for (uint32_t j = 0; j < 4; j++) {
            fldVal[j] = i;
        }
        (void)memcpy_s(recBuf, tblRecLen, fldVal, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(testDbId, testRelId[9], &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 导出为大端数据
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    char exportFile[64] = "./filePath/export.db";
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook1;
    ret = DB_BkpPhyWithDataConvHook(testDbId, (uint8_t *)exportFile, &pstFileSaveOpt, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 关闭DB
    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 导入DB
    ret = DB_RestoreDB2((uint8_t *)exportFile, (VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 调用DB_CheckDBRel校验表的CRC值
    VOS_UINT16 usFailRelId = 0;
    VOS_UINT32 ulRelCount = 9;
    ret = DB_CheckDBRel(testDbId, testRelId, ulRelCount, &usFailRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0xFFFF, usFailRelId);
    VOS_UINT32 expectvalue[10] = {0};
    for (int i = 0; i < 10; i++) {
        expectvalue[i] = i;
    }
    // 校验数据
    for (int i = 0; i < 9; i++) {
        ValidateExpectData(testRelId[i], 10, &testRelDef[i / 3], expectvalue, false, testDbId);
    }
    // 验证所有表是否在共享空间
    for (int i = 0; i < 9; i++) {
        SelectView(testRelId[i], true, DB_TPC_SYSVIEW_GET_COMMOM_AREA, testDbId);
    }
    TEST_V1_FREE(recBuf);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
    // 关闭DB
    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
}
// 129.持久化DB中存在自定义数据和block类型时，所有表在共享空间时导出为小端数据，discard进行导入
TEST_F(SharedSpace_Test, V1Com_039_129)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    // 创建自定义数据类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 32;
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    DB_REG_FEATURE_STRU pstFeature = {0};
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 1, 1, 1};
    SelectcfgInterface(&pstFeature, selectways);
    // 注册持久化DB
    DB_INST_CONFIG_STRU enConfigStructure = {0};
    enConfigStructure.enPersistent = DB_CKP_COMPLETE;
    ret = DB_RegDbConfigStructure(&enConfigStructure, &pstFeature);
    // 注册导入持久化
    ret = DB_RegDbRestoreConfig(true, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    char testDbName[DB_NAME_LEN] = "testDb";
    char filePath[64] = "./filePath/perDbPath";
    uint32_t testDbId = 0;
    // 建DB
    ret = DB_CreateDB2((VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建多张表
    uint16_t testRelId[10] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(false, testRelId, testRelDef, testDbId);
    // 创建自定义数据类型表
    DB_REL_DEF_STRU *testRelDef1 = NULL;
    ret = TestDB_CreateTbl(testDbId, "schema_file/vertexlabel4.json", &testRelId[9], testRelDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 所有表写入少量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 10, false, testDbId);
    }
    // 自定义数据类型表写入少量数据
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(testDbId, testRelId[9], &tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);
    for (int i = 0; i < 10; i++) {
        uint32_t fldVal[4];
        for (uint32_t j = 0; j < 4; j++) {
            fldVal[j] = i;
        }
        (void)memcpy_s(recBuf, tblRecLen, fldVal, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(testDbId, testRelId[9], &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 导出为小端数据
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char exportFile[64] = "./filePath/export.db";
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook1;
    ret = DB_BkpPhyWithDataConvHook(testDbId, (uint8_t *)exportFile, &pstFileSaveOpt, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 关闭DB
    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 导入DB
    ret = DB_RestoreDB2((uint8_t *)exportFile, (VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 调用DB_CheckDBRel校验表的CRC值
    VOS_UINT16 usFailRelId = 0;
    VOS_UINT32 ulRelCount = 9;
    ret = DB_CheckDBRel(testDbId, testRelId, ulRelCount, &usFailRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0xFFFF, usFailRelId);
    VOS_UINT32 expectvalue[10] = {0};
    for (int i = 0; i < 10; i++) {
        expectvalue[i] = i;
    }
    // 校验数据
    for (int i = 0; i < 9; i++) {
        ValidateExpectData(testRelId[i], 10, &testRelDef[i / 3], expectvalue, false, testDbId);
    }
    TEST_V1_FREE(recBuf);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
    // 关闭DB
    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
}
// 130.部分表在共享空间时的导出，replace进行导入
TEST_F(SharedSpace_Test, V1Com_039_130)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    char testDbName[DB_NAME_LEN] = "testDb";
    char filePath[64] = "./filePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    uint32_t testDbId = 0;
    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef, testDbId);
    // 部分表写入大量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 10, true, testDbId);
        if (i % 2 == 0) {
            Insert(testRelId[i], 1990, true, testDbId, TPC_GLOBAL_CDB, 10);
        }
    }
    // 导出DB
    char exportFile[64] = "./filePath/export.txt";
    ret = TPC_BkpPhy(testDbId, (uint8_t *)exportFile);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // replace进行导入DB
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_Restore(
        (uint8_t *)exportFile, (VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验数据
    for (int i = 0; i < 9; i++) {
        if (i % 2 == 0) {
            VOS_UINT32 expectvalue[2000] = {0};
            for (int i = 0; i < 2000; i++) {
                expectvalue[i] = i;
            }
            ValidateExpectData(testRelId[i], 2000, &testRelDef[i / 3], expectvalue, true, testDbId);
        } else {
            VOS_UINT32 expectvalue[10] = {0};
            for (int i = 0; i < 10; i++) {
                expectvalue[i] = i;
            }
            ValidateExpectData(testRelId[i], 10, &testRelDef[i / 3], expectvalue, true, testDbId);
        }
    }
    // 校验是否在共享空间中
    SelectView(testRelId[0], false, DB_TPC_SYSVIEW_GET_COMMOM_AREA, testDbId);
    SelectView(testRelId[1], true, DB_TPC_SYSVIEW_GET_COMMOM_AREA, testDbId);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
    // 关闭DB
    ret = TPC_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)testDbName, TPC_WAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
}
// 131.部分表在共享空间时的导出，discard进行导入
TEST_F(SharedSpace_Test, V1Com_039_131)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    char testDbName[DB_NAME_LEN] = "testDb";
    char filePath[64] = "./filePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    uint32_t testDbId = 0;
    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef, testDbId);
    // 部分表写入大量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 10, true, testDbId);
        if (i % 2 == 0) {
            Insert(testRelId[i], 1990, true, testDbId, TPC_GLOBAL_CDB, 10);
        }
    }
    // 导出DB
    char exportFile[64] = "./filePath/export.txt";
    ret = TPC_BkpPhy(testDbId, (uint8_t *)exportFile);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 关闭DB
    ret = TPC_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)testDbName, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // discard进行导入DB
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_Restore(
        (uint8_t *)exportFile, (VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, DB_RESTORETYPE_DISCARD, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验数据
    for (int i = 0; i < 9; i++) {
        if (i % 2 == 0) {
            VOS_UINT32 expectvalue[2000] = {0};
            for (int i = 0; i < 2000; i++) {
                expectvalue[i] = i;
            }
            ValidateExpectData(testRelId[i], 2000, &testRelDef[i / 3], expectvalue, true, testDbId);
        } else {
            VOS_UINT32 expectvalue[10] = {0};
            for (int i = 0; i < 10; i++) {
                expectvalue[i] = i;
            }
            ValidateExpectData(testRelId[i], 10, &testRelDef[i / 3], expectvalue, true, testDbId);
        }
    }
    // 校验是否在共享空间中
    SelectView(testRelId[0], false, DB_TPC_SYSVIEW_GET_COMMOM_AREA, testDbId);
    SelectView(testRelId[1], true, DB_TPC_SYSVIEW_GET_COMMOM_AREA, testDbId);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
    // 关闭DB
    ret = TPC_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)testDbName, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
}
// 132.所有表在共享空间时的导出，replace进行导入
TEST_F(SharedSpace_Test, V1Com_039_132)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    char testDbName[DB_NAME_LEN] = "testDb";
    char filePath[64] = "./filePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    uint32_t testDbId = 0;
    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef, testDbId);
    // 所有表写入少量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 10, true, testDbId);
    }
    // 导出DB
    char exportFile[64] = "./filePath/export.txt";
    ret = TPC_BkpPhy(testDbId, (uint8_t *)exportFile);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // replace进行导入DB
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_Restore(
        (uint8_t *)exportFile, (VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验数据
    for (int i = 0; i < 9; i++) {
        VOS_UINT32 expectvalue[10] = {0};
        for (int i = 0; i < 10; i++) {
            expectvalue[i] = i;
        }
        ValidateExpectData(testRelId[i], 10, &testRelDef[i / 3], expectvalue, true, testDbId);
    }
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
    // 关闭DB
    ret = TPC_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)testDbName, TPC_WAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
}
// 133.所有表在共享空间时的导出，discard进行导入
TEST_F(SharedSpace_Test, V1Com_039_133)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    char testDbName[DB_NAME_LEN] = "testDb";
    char filePath[64] = "./filePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    uint32_t testDbId = 0;
    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef, testDbId);
    // 所有表写入少量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 10, true, testDbId);
    }
    // 导出DB
    char exportFile[64] = "./filePath/export.txt";
    ret = TPC_BkpPhy(testDbId, (uint8_t *)exportFile);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 关闭DB
    ret = TPC_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)testDbName, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // discard进行导入DB
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_Restore(
        (uint8_t *)exportFile, (VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, DB_RESTORETYPE_DISCARD, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验数据
    for (int i = 0; i < 9; i++) {
        VOS_UINT32 expectvalue[10] = {0};
        for (int i = 0; i < 10; i++) {
            expectvalue[i] = i;
        }
        ValidateExpectData(testRelId[i], 10, &testRelDef[i / 3], expectvalue, true, testDbId);
    }
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
    // 关闭DB
    ret = TPC_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)testDbName, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
}
// 134.所有表在共享空间时的导出，discard进行导入后进行增删改查操作
TEST_F(SharedSpace_Test, V1Com_039_134)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    char testDbName[DB_NAME_LEN] = "testDb";
    char filePath[64] = "./filePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    uint32_t testDbId = 0;
    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef, testDbId);
    // 所有表写入少量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 10, true, testDbId);
    }
    // 导出DB
    char exportFile[64] = "./filePath/export.txt";
    ret = TPC_BkpPhy(testDbId, (uint8_t *)exportFile);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 关闭DB
    ret = TPC_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)testDbName, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // discard进行导入DB
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_Restore(
        (uint8_t *)exportFile, (VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, DB_RESTORETYPE_DISCARD, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验数据
    for (int i = 0; i < 9; i++) {
        VOS_UINT32 expectvalue[10] = {0};
        for (int i = 0; i < 10; i++) {
            expectvalue[i] = i;
        }
        ValidateExpectData(testRelId[i], 10, &testRelDef[i / 3], expectvalue, true, testDbId);
    }
    // 开启CDB增删改查操作不同的表
    VOS_UINT32 CdbId[6] = {0};
    for (int i = 0; i < 9; i++) {
        ret = TPC_BeginCDB(testDbId, &CdbId[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 更新数据
        Update(testRelId[i], 0, 10, true, 1, testDbId, CdbId[i]);
        // 删除数据
        Delete(testRelId[i], 10, true, 1, testDbId, CdbId[i]);
        // 插入数据
        Insert(testRelId[i], 1, true, testDbId, CdbId[i]);
        // 查询数据
        selectDataByAllWays(testRelId[i], 1, 0, true, CdbId[i]);
    }
    // 提交CDB
    for (int i = 0; i < 9; i++) {
        ret = TPC_ForceCommitCDB(CdbId[i]);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 校验数据
    for (int i = 0; i < 9; i++) {
        VOS_UINT32 expectvalue[10] = {0};
        for (int i = 0; i < 10; i++) {
            expectvalue[i] = i;
        }
        ValidateExpectData(testRelId[i], 10, &testRelDef[i / 3], expectvalue, true, testDbId);
    }
    // 验证所有表是否在共享空间
    for (int i = 0; i < 9; i++) {
        SelectView(testRelId[i], true, DB_TPC_SYSVIEW_GET_COMMOM_AREA, testDbId);
    }
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
    // 关闭DB
    ret = TPC_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)testDbName, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
}
// 135.部分表在共享空间时导出为大端数据，replace进行导入
TEST_F(SharedSpace_Test, V1Com_039_135)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    DB_REG_FEATURE_STRU pstFeature = {0};
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 1, 1, 1};
    SelectcfgInterface(&pstFeature, selectways);
    // 注册导入方式为replace
    DB_RESTORETYPE_ENUM enRestoreType = DB_RESTORETYPE_REPLACE;
    ret = DB_RegDbRestoreTypeCfg(enRestoreType, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    char testDbName[DB_NAME_LEN] = "testDb";
    char filePath[64] = "./filePath/perDbPath";
    uint32_t testDbId = 0;
    // 建DB
    ret = DB_CreateDB2((VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(false, testRelId, testRelDef, testDbId);
    // 部分表写入大量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 10, false, testDbId);
        if (i % 2 == 0) {
            Insert(testRelId[i], 1990, false, testDbId, TPC_GLOBAL_CDB, 10);
        }
    }
    // 导出为大端数据
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    char exportFile[64] = "./filePath/export.db";
    ret = DB_BkpPhyEx(testDbId, (uint8_t *)exportFile, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 导入DB
    ret = DB_RestoreDB2((uint8_t *)exportFile, (VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 调用DB_CheckDBRel校验表的CRC值
    VOS_UINT16 usFailRelId = 0;
    VOS_UINT32 ulRelCount = 9;
    ret = DB_CheckDBRel(testDbId, testRelId, ulRelCount, &usFailRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0xFFFF, usFailRelId);
    for (int i = 0; i < 9; i++) {
        if (i % 2 == 0) {
            VOS_UINT32 expectvalue[2000] = {0};
            for (int i = 0; i < 2000; i++) {
                expectvalue[i] = i;
            }
            ValidateExpectData(testRelId[i], 2000, &testRelDef[i / 3], expectvalue, false, testDbId);
        } else {
            VOS_UINT32 expectvalue[10] = {0};
            for (int i = 0; i < 10; i++) {
                expectvalue[i] = i;
            }
            ValidateExpectData(testRelId[i], 10, &testRelDef[i / 3], expectvalue, false, testDbId);
        }
    }
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
    // 关闭DB
    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
}
// 136.部分表在共享空间时导出为小端数据，replace进行导入
TEST_F(SharedSpace_Test, V1Com_039_136)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    DB_REG_FEATURE_STRU pstFeature = {0};
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 1, 1, 1};
    SelectcfgInterface(&pstFeature, selectways);
    // 注册导入方式为replace
    DB_RESTORETYPE_ENUM enRestoreType = DB_RESTORETYPE_REPLACE;
    ret = DB_RegDbRestoreTypeCfg(enRestoreType, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    char testDbName[DB_NAME_LEN] = "testDb";
    char filePath[64] = "./filePath/perDbPath";
    uint32_t testDbId = 0;
    // 建DB
    ret = DB_CreateDB2((VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(false, testRelId, testRelDef, testDbId);
    // 部分表写入大量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 10, false, testDbId);
        if (i % 2 == 0) {
            Insert(testRelId[i], 1990, false, testDbId, TPC_GLOBAL_CDB, 10);
        }
    }
    // 导出为小端数据
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char exportFile[64] = "./filePath/export.db";
    ret = DB_BkpPhyEx(testDbId, (uint8_t *)exportFile, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 导入DB
    ret = DB_RestoreDB2((uint8_t *)exportFile, (VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 调用DB_CheckDBRel校验表的CRC值
    VOS_UINT16 usFailRelId = 0;
    VOS_UINT32 ulRelCount = 9;
    ret = DB_CheckDBRel(testDbId, testRelId, ulRelCount, &usFailRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0xFFFF, usFailRelId);
    // 校验数据
    for (int i = 0; i < 9; i++) {
        if (i % 2 == 0) {
            VOS_UINT32 expectvalue[2000] = {0};
            for (int i = 0; i < 2000; i++) {
                expectvalue[i] = i;
            }
            ValidateExpectData(testRelId[i], 2000, &testRelDef[i / 3], expectvalue, false, testDbId);
        } else {
            VOS_UINT32 expectvalue[10] = {0};
            for (int i = 0; i < 10; i++) {
                expectvalue[i] = i;
            }
            ValidateExpectData(testRelId[i], 10, &testRelDef[i / 3], expectvalue, false, testDbId);
        }
    }
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
    // 关闭DB
    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
}
// 137.部分表在共享空间时导出为大端数据，discard进行导入
TEST_F(SharedSpace_Test, V1Com_039_137)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    DB_REG_FEATURE_STRU pstFeature = {0};
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 1, 1, 1};
    SelectcfgInterface(&pstFeature, selectways);

    char testDbName[DB_NAME_LEN] = "testDb";
    char filePath[64] = "./filePath/perDbPath";
    uint32_t testDbId = 0;
    // 建DB
    ret = DB_CreateDB2((VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(false, testRelId, testRelDef, testDbId);
    // 部分表写入大量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 10, false, testDbId);
        if (i % 2 == 0) {
            Insert(testRelId[i], 1990, false, testDbId, TPC_GLOBAL_CDB, 10);
        }
    }
    // 导出为大端数据
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    char exportFile[64] = "./filePath/export.db";
    ret = DB_BkpPhyEx(testDbId, (uint8_t *)exportFile, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 关闭DB
    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 导入DB
    ret = DB_RestoreDB2((uint8_t *)exportFile, (VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 调用DB_CheckDBRel校验表的CRC值
    VOS_UINT16 usFailRelId = 0;
    VOS_UINT32 ulRelCount = 9;
    ret = DB_CheckDBRel(testDbId, testRelId, ulRelCount, &usFailRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0xFFFF, usFailRelId);
    // 校验数据
    for (int i = 0; i < 9; i++) {
        if (i % 2 == 0) {
            VOS_UINT32 expectvalue[2000] = {0};
            for (int i = 0; i < 2000; i++) {
                expectvalue[i] = i;
            }
            ValidateExpectData(testRelId[i], 2000, &testRelDef[i / 3], expectvalue, false, testDbId);
        } else {
            VOS_UINT32 expectvalue[10] = {0};
            for (int i = 0; i < 10; i++) {
                expectvalue[i] = i;
            }
            ValidateExpectData(testRelId[i], 10, &testRelDef[i / 3], expectvalue, false, testDbId);
        }
    }
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
    // 关闭DB
    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
}
// 138.部分表在共享空间时导出为小端数据，discard进行导入
TEST_F(SharedSpace_Test, V1Com_039_138)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    DB_REG_FEATURE_STRU pstFeature = {0};
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 1, 1, 1};
    SelectcfgInterface(&pstFeature, selectways);

    char testDbName[DB_NAME_LEN] = "testDb";
    char filePath[64] = "./filePath/perDbPath";
    uint32_t testDbId = 0;
    // 建DB
    ret = DB_CreateDB2((VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(false, testRelId, testRelDef, testDbId);
    // 部分表写入大量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 10, false, testDbId);
        if (i % 2 == 0) {
            Insert(testRelId[i], 1990, false, testDbId, TPC_GLOBAL_CDB, 10);
        }
    }
    // 导出为小端数据
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char exportFile[64] = "./filePath/export.db";
    ret = DB_BkpPhyEx(testDbId, (uint8_t *)exportFile, &pstFileSaveOpt);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 关闭DB
    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 导入DB
    ret = DB_RestoreDB2((uint8_t *)exportFile, (VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 调用DB_CheckDBRel校验表的CRC值
    VOS_UINT16 usFailRelId = 0;
    VOS_UINT32 ulRelCount = 9;
    ret = DB_CheckDBRel(testDbId, testRelId, ulRelCount, &usFailRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0xFFFF, usFailRelId);
    // 校验数据
    for (int i = 0; i < 9; i++) {
        if (i % 2 == 0) {
            VOS_UINT32 expectvalue[2000] = {0};
            for (int i = 0; i < 2000; i++) {
                expectvalue[i] = i;
            }
            ValidateExpectData(testRelId[i], 2000, &testRelDef[i / 3], expectvalue, false, testDbId);
        } else {
            VOS_UINT32 expectvalue[10] = {0};
            for (int i = 0; i < 10; i++) {
                expectvalue[i] = i;
            }
            ValidateExpectData(testRelId[i], 10, &testRelDef[i / 3], expectvalue, false, testDbId);
        }
    }
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
    // 关闭DB
    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
}
// 139.所有表在共享空间时导出为大端数据，replace进行导入
TEST_F(SharedSpace_Test, V1Com_039_139)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    DB_REG_FEATURE_STRU pstFeature = {0};
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 1, 1, 1};
    SelectcfgInterface(&pstFeature, selectways);
    // 注册导入方式为replace
    DB_RESTORETYPE_ENUM enRestoreType = DB_RESTORETYPE_REPLACE;
    ret = DB_RegDbRestoreTypeCfg(enRestoreType, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    char testDbName[DB_NAME_LEN] = "testDb";
    char filePath[64] = "./filePath/perDbPath";
    uint32_t testDbId = 0;
    // 建DB
    ret = DB_CreateDB2((VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(false, testRelId, testRelDef, testDbId);
    // 所有表写入少量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 10, false, testDbId);
    }
    // 导出为大端数据
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    char exportFile[64] = "./filePath/export.db";
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &pstFileSaveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = true;
    ret = DB_PhyBkp2(testDbId, (uint8_t *)exportFile, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // CRC校验
    ret = DB_CheckDataFile((VOS_UINT8 *)exportFile, DB_CHECK_BASIC);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 导入DB
    ret = DB_RestoreDB2((uint8_t *)exportFile, (VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 调用DB_CheckDBRel校验表的CRC值
    VOS_UINT16 usFailRelId = 0;
    VOS_UINT32 ulRelCount = 9;
    ret = DB_CheckDBRel(testDbId, testRelId, ulRelCount, &usFailRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0xFFFF, usFailRelId);
    VOS_UINT32 expectvalue[10] = {0};
    for (int i = 0; i < 10; i++) {
        expectvalue[i] = i;
    }
    // 校验数据
    for (int i = 0; i < 9; i++) {
        ValidateExpectData(testRelId[i], 10, &testRelDef[i / 3], expectvalue, false, testDbId);
    }
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
    // 关闭DB
    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
}
// 140.所有表在共享空间时导出为小端数据，replace进行导入
TEST_F(SharedSpace_Test, V1Com_039_140)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    DB_REG_FEATURE_STRU pstFeature = {0};
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 1, 1, 1};
    SelectcfgInterface(&pstFeature, selectways);
    // 注册导入方式为replace
    DB_RESTORETYPE_ENUM enRestoreType = DB_RESTORETYPE_REPLACE;
    ret = DB_RegDbRestoreTypeCfg(enRestoreType, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    char testDbName[DB_NAME_LEN] = "testDb";
    char filePath[64] = "./filePath/perDbPath";
    uint32_t testDbId = 0;
    // 建DB
    ret = DB_CreateDB2((VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(false, testRelId, testRelDef, testDbId);
    // 所有表写入少量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 10, false, testDbId);
    }
    // 导出为小端数据
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char exportFile[64] = "./filePath/export.db";
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &pstFileSaveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = true;
    ret = DB_PhyBkp2(testDbId, (uint8_t *)exportFile, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // CRC校验
    ret = DB_CheckDataFile((VOS_UINT8 *)exportFile, DB_CHECK_BASIC);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 导入DB
    ret = DB_RestoreDB2((uint8_t *)exportFile, (VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 调用DB_CheckDBRel校验表的CRC值
    VOS_UINT16 usFailRelId = 0;
    VOS_UINT32 ulRelCount = 9;
    ret = DB_CheckDBRel(testDbId, testRelId, ulRelCount, &usFailRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0xFFFF, usFailRelId);
    VOS_UINT32 expectvalue[10] = {0};
    for (int i = 0; i < 10; i++) {
        expectvalue[i] = i;
    }
    // 校验数据
    for (int i = 0; i < 9; i++) {
        ValidateExpectData(testRelId[i], 10, &testRelDef[i / 3], expectvalue, false, testDbId);
    }
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
    // 关闭DB
    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
}
// 141.所有表在共享空间时导出为大端数据，discard进行导入
TEST_F(SharedSpace_Test, V1Com_039_141)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    DB_REG_FEATURE_STRU pstFeature = {0};
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 1, 1, 1};
    SelectcfgInterface(&pstFeature, selectways);

    char testDbName[DB_NAME_LEN] = "testDb";
    char filePath[64] = "./filePath/perDbPath";
    uint32_t testDbId = 0;
    // 建DB
    ret = DB_CreateDB2((VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(false, testRelId, testRelDef, testDbId);
    // 所有表写入少量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 10, false, testDbId);
    }
    // 导出为大端数据
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    char exportFile[64] = "./filePath/export.db";
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &pstFileSaveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = true;
    ret = DB_PhyBkp2(testDbId, (uint8_t *)exportFile, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // CRC校验
    ret = DB_CheckDataFile((VOS_UINT8 *)exportFile, DB_CHECK_BASIC);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 关闭DB
    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 导入DB
    ret = DB_RestoreDB2((uint8_t *)exportFile, (VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 调用DB_CheckDBRel校验表的CRC值
    VOS_UINT16 usFailRelId = 0;
    VOS_UINT32 ulRelCount = 9;
    ret = DB_CheckDBRel(testDbId, testRelId, ulRelCount, &usFailRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0xFFFF, usFailRelId);
    VOS_UINT32 expectvalue[10] = {0};
    for (int i = 0; i < 10; i++) {
        expectvalue[i] = i;
    }
    // 校验数据
    for (int i = 0; i < 9; i++) {
        ValidateExpectData(testRelId[i], 10, &testRelDef[i / 3], expectvalue, false, testDbId);
    }
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
    // 关闭DB
    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
}
// 142.所有表在共享空间时导出为小端数据，discard进行导入
TEST_F(SharedSpace_Test, V1Com_039_142)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    DB_REG_FEATURE_STRU pstFeature = {0};
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 1, 1, 1};
    SelectcfgInterface(&pstFeature, selectways);

    char testDbName[DB_NAME_LEN] = "testDb";
    char filePath[64] = "./filePath/perDbPath";
    uint32_t testDbId = 0;
    // 建DB
    ret = DB_CreateDB2((VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(false, testRelId, testRelDef, testDbId);
    // 所有表写入少量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 10, false, testDbId);
    }
    // 导出为小端数据
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char exportFile[64] = "./filePath/export.db";
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
    DB_BAKPHY_OPTIONS_STRU option = {0};
    option.pstFileSaveOpt = &pstFileSaveOpt;
    option.pfnGetTblConvHook = pfnGetTblConvHook;
    option.bAddChecksum = true;
    ret = DB_PhyBkp2(testDbId, (uint8_t *)exportFile, &option);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // CRC校验
    ret = DB_CheckDataFile((VOS_UINT8 *)exportFile, DB_CHECK_BASIC);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 关闭DB
    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 导入DB
    ret = DB_RestoreDB2((uint8_t *)exportFile, (VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 调用DB_CheckDBRel校验表的CRC值
    VOS_UINT16 usFailRelId = 0;
    VOS_UINT32 ulRelCount = 9;
    ret = DB_CheckDBRel(testDbId, testRelId, ulRelCount, &usFailRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0xFFFF, usFailRelId);
    VOS_UINT32 expectvalue[10] = {0};
    for (int i = 0; i < 10; i++) {
        expectvalue[i] = i;
    }
    // 校验数据
    for (int i = 0; i < 9; i++) {
        ValidateExpectData(testRelId[i], 10, &testRelDef[i / 3], expectvalue, false, testDbId);
    }
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
    // 关闭DB
    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
}
// 143.存在自定义数据和block类型时，所有表在共享空间时导出为大端数据，replace进行导入
TEST_F(SharedSpace_Test, V1Com_039_143)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    // 创建自定义数据类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 32;
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    DB_REG_FEATURE_STRU pstFeature = {0};
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 1, 1, 1};
    SelectcfgInterface(&pstFeature, selectways);
    // 注册导入方式为replace
    DB_RESTORETYPE_ENUM enRestoreType = DB_RESTORETYPE_REPLACE;
    ret = DB_RegDbRestoreTypeCfg(enRestoreType, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    char testDbName[DB_NAME_LEN] = "testDb";
    char filePath[64] = "./filePath/perDbPath";
    uint32_t testDbId = 0;
    // 建DB
    ret = DB_CreateDB2((VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建多张表
    uint16_t testRelId[10] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(false, testRelId, testRelDef, testDbId);
    // 创建自定义数据类型表
    DB_REL_DEF_STRU *testRelDef1 = NULL;
    ret = TestDB_CreateTbl(testDbId, "schema_file/vertexlabel4.json", &testRelId[9], testRelDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 所有表写入少量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 10, false, testDbId);
    }
    // 自定义数据类型表写入少量数据
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(testDbId, testRelId[9], &tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);
    for (int i = 0; i < 10; i++) {
        uint32_t fldVal[4];
        for (uint32_t j = 0; j < 4; j++) {
            fldVal[j] = i;
        }
        (void)memcpy_s(recBuf, tblRecLen, fldVal, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(testDbId, testRelId[9], &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 导出为大端数据
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    char exportFile[64] = "./filePath/export.db";
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook1;
    ret = DB_BkpPhyWithDataConvHook(testDbId, (uint8_t *)exportFile, &pstFileSaveOpt, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 导入DB
    ret = DB_RestoreDB2((uint8_t *)exportFile, (VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 调用DB_CheckDBRel校验表的CRC值
    VOS_UINT16 usFailRelId = 0;
    VOS_UINT32 ulRelCount = 9;
    ret = DB_CheckDBRel(testDbId, testRelId, ulRelCount, &usFailRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0xFFFF, usFailRelId);
    VOS_UINT32 expectvalue[10] = {0};
    for (int i = 0; i < 10; i++) {
        expectvalue[i] = i;
    }
    // 校验数据
    for (int i = 0; i < 9; i++) {
        ValidateExpectData(testRelId[i], 10, &testRelDef[i / 3], expectvalue, false, testDbId);
    }
    TEST_V1_FREE(recBuf);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
    // 关闭DB
    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
}
// 144.存在自定义数据和block类型时，所有表在共享空间时导出为小端数据，replace进行导入
TEST_F(SharedSpace_Test, V1Com_039_144)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    // 创建自定义数据类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 32;
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    DB_REG_FEATURE_STRU pstFeature = {0};
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 1, 1, 1};
    SelectcfgInterface(&pstFeature, selectways);
    // 注册导入方式为replace
    DB_RESTORETYPE_ENUM enRestoreType = DB_RESTORETYPE_REPLACE;
    ret = DB_RegDbRestoreTypeCfg(enRestoreType, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    char testDbName[DB_NAME_LEN] = "testDb";
    char filePath[64] = "./filePath/perDbPath";
    uint32_t testDbId = 0;
    // 建DB
    ret = DB_CreateDB2((VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建多张表
    uint16_t testRelId[10] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(false, testRelId, testRelDef, testDbId);
    // 创建自定义数据类型表
    DB_REL_DEF_STRU *testRelDef1 = NULL;
    ret = TestDB_CreateTbl(testDbId, "schema_file/vertexlabel4.json", &testRelId[9], testRelDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 所有表写入少量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 10, false, testDbId);
    }
    // 自定义数据类型表写入少量数据
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(testDbId, testRelId[9], &tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);
    for (int i = 0; i < 10; i++) {
        uint32_t fldVal[4];
        for (uint32_t j = 0; j < 4; j++) {
            fldVal[j] = i;
        }
        (void)memcpy_s(recBuf, tblRecLen, fldVal, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(testDbId, testRelId[9], &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 导出为小端数据
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char exportFile[64] = "./filePath/export.db";
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook1;
    ret = DB_BkpPhyWithDataConvHook(testDbId, (uint8_t *)exportFile, &pstFileSaveOpt, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 导入DB
    ret = DB_RestoreDB2((uint8_t *)exportFile, (VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 调用DB_CheckDBRel校验表的CRC值
    VOS_UINT16 usFailRelId = 0;
    VOS_UINT32 ulRelCount = 9;
    ret = DB_CheckDBRel(testDbId, testRelId, ulRelCount, &usFailRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0xFFFF, usFailRelId);
    VOS_UINT32 expectvalue[10] = {0};
    for (int i = 0; i < 10; i++) {
        expectvalue[i] = i;
    }
    // 校验数据
    for (int i = 0; i < 9; i++) {
        ValidateExpectData(testRelId[i], 10, &testRelDef[i / 3], expectvalue, false, testDbId);
    }
    TEST_V1_FREE(recBuf);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
    // 关闭DB
    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
}
// 145.存在自定义数据和block类型时，所有表在共享空间时导出为大端数据，discard进行导入
TEST_F(SharedSpace_Test, V1Com_039_145)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    // 创建自定义数据类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 32;
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    DB_REG_FEATURE_STRU pstFeature = {0};
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 1, 1, 1};
    SelectcfgInterface(&pstFeature, selectways);

    char testDbName[DB_NAME_LEN] = "testDb";
    char filePath[64] = "./filePath/perDbPath";
    uint32_t testDbId = 0;
    // 建DB
    ret = DB_CreateDB2((VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建多张表
    uint16_t testRelId[10] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(false, testRelId, testRelDef, testDbId);
    // 创建自定义数据类型表
    DB_REL_DEF_STRU *testRelDef1 = NULL;
    ret = TestDB_CreateTbl(testDbId, "schema_file/vertexlabel4.json", &testRelId[9], testRelDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 所有表写入少量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 10, false, testDbId);
    }
    // 自定义数据类型表写入少量数据
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(testDbId, testRelId[9], &tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);
    for (int i = 0; i < 10; i++) {
        uint32_t fldVal[4];
        for (uint32_t j = 0; j < 4; j++) {
            fldVal[j] = i;
        }
        (void)memcpy_s(recBuf, tblRecLen, fldVal, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(testDbId, testRelId[9], &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 导出为大端数据
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_BIG_ENDIAN;
    char exportFile[64] = "./filePath/export.db";
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook1;
    ret = DB_BkpPhyWithDataConvHook(testDbId, (uint8_t *)exportFile, &pstFileSaveOpt, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 关闭DB
    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 导入DB
    ret = DB_RestoreDB2((uint8_t *)exportFile, (VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 调用DB_CheckDBRel校验表的CRC值
    VOS_UINT16 usFailRelId = 0;
    VOS_UINT32 ulRelCount = 9;
    ret = DB_CheckDBRel(testDbId, testRelId, ulRelCount, &usFailRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0xFFFF, usFailRelId);
    VOS_UINT32 expectvalue[10] = {0};
    for (int i = 0; i < 10; i++) {
        expectvalue[i] = i;
    }
    // 校验数据
    for (int i = 0; i < 9; i++) {
        ValidateExpectData(testRelId[i], 10, &testRelDef[i / 3], expectvalue, false, testDbId);
    }
    TEST_V1_FREE(recBuf);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
    // 关闭DB
    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
}
// 146.存在自定义数据和block类型时，所有表在共享空间时导出为小端数据，discard进行导入
TEST_F(SharedSpace_Test, V1Com_039_146)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    // 创建自定义数据类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 32;
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    DB_REG_FEATURE_STRU pstFeature = {0};
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 1, 1, 1};
    SelectcfgInterface(&pstFeature, selectways);

    char testDbName[DB_NAME_LEN] = "testDb";
    char filePath[64] = "./filePath/perDbPath";
    uint32_t testDbId = 0;
    // 建DB
    ret = DB_CreateDB2((VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建多张表
    uint16_t testRelId[10] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(false, testRelId, testRelDef, testDbId);
    // 创建自定义数据类型表
    DB_REL_DEF_STRU *testRelDef1 = NULL;
    ret = TestDB_CreateTbl(testDbId, "schema_file/vertexlabel4.json", &testRelId[9], testRelDef1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 所有表写入少量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 10, false, testDbId);
    }
    // 自定义数据类型表写入少量数据
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(testDbId, testRelId[9], &tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);
    for (int i = 0; i < 10; i++) {
        uint32_t fldVal[4];
        for (uint32_t j = 0; j < 4; j++) {
            fldVal[j] = i;
        }
        (void)memcpy_s(recBuf, tblRecLen, fldVal, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = DB_InsertRec(testDbId, testRelId[9], &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 导出为小端数据
    DB_SAVE_OPTIONS pstFileSaveOpt;
    pstFileSaveOpt.enFileFormat = DB_LITTLE_ENDIAN;
    char exportFile[64] = "./filePath/export.db";
    DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook1;
    ret = DB_BkpPhyWithDataConvHook(testDbId, (uint8_t *)exportFile, &pstFileSaveOpt, pfnGetTblConvHook);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 关闭DB
    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 导入DB
    ret = DB_RestoreDB2((uint8_t *)exportFile, (VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 调用DB_CheckDBRel校验表的CRC值
    VOS_UINT16 usFailRelId = 0;
    VOS_UINT32 ulRelCount = 9;
    ret = DB_CheckDBRel(testDbId, testRelId, ulRelCount, &usFailRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0xFFFF, usFailRelId);
    VOS_UINT32 expectvalue[10] = {0};
    for (int i = 0; i < 10; i++) {
        expectvalue[i] = i;
    }
    // 校验数据
    for (int i = 0; i < 9; i++) {
        ValidateExpectData(testRelId[i], 10, &testRelDef[i / 3], expectvalue, false, testDbId);
    }
    TEST_V1_FREE(recBuf);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
    // 关闭DB
    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
}
// 147.共享空间中的表插入数据至超出阈值后进行表内存查询
TEST_F(SharedSpace_Test, V1Com_039_147)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef);
    // 表写入少量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 10, true);
    }

    // 插入数据
    Insert(testRelId[0], g_Threshold - 10, true, g_testDbId, TPC_GLOBAL_CDB, 10);
    // 校验表的数据
    VOS_UINT32 expectvalue[g_Threshold] = {0};
    for (int i = 0; i < g_Threshold; i++) {
        expectvalue[i] = i;
    }
    ValidateExpectData(testRelId[0], g_Threshold, &testRelDef[0], expectvalue, true);
    // 校验表迁出
    Update(testRelId[0], 1, 2001, true);
    SelectView(testRelId[0], false, DB_TPC_SYSVIEW_GET_COMMOM_AREA);
    // 校验表内存
    VOS_UINT32 TblSize = 0;
    ret = DBDBG_GetTblMemSize(g_testDbId, testRelId[0], &TblSize);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    if (TblSize < 32 * 1024) {
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, -1);
    }
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
}
// 148.独立空间中的表删除数据至低于阈值后进行表内存查询
TEST_F(SharedSpace_Test, V1Com_039_148)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef);
    // 表写入大量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 1000, true);
    }
    // 删除数据
    for (int i = g_DeleteThreshold; i < 1000; i++) {
        Delete(testRelId[0], i, true);
    }
    // 校验表的数据
    VOS_UINT32 expectvalue[g_DeleteThreshold] = {0};
    for (int i = 0; i < g_DeleteThreshold; i++) {
        expectvalue[i] = i;
    }
    ValidateExpectData(testRelId[0], g_DeleteThreshold, &testRelDef[0], expectvalue, true);
    // 校验表迁入
    sleep(10);
    Update(testRelId[0], 1, 2001, true);
    SelectView(testRelId[0], true, DB_TPC_SYSVIEW_GET_COMMOM_AREA);
    // 校验表内存
    VOS_UINT32 TblSize = 0;
    ret = DBDBG_GetTblMemSize(g_testDbId, testRelId[0], &TblSize);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    if (TblSize >= 32 * 1024) {
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, -1);
    }
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
}
// 149.共享空间中的表进行多次增删改查后进行表内存查询
TEST_F(SharedSpace_Test, V1Com_039_149)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef);
    // 表写入少量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 10, true);
    }
    VOS_UINT32 oldTblSize = 0;
    ret = DBDBG_GetTblMemSize(g_testDbId, testRelId[0], &oldTblSize);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 进行多次增删改查操作
    for (int i = 0; i < 200; i++) {
        // 更新数据
        Update(testRelId[0], 0, 2001, true);
        // 删除数据
        Delete(testRelId[0], 2001, true);
        // 插入数据
        Insert(testRelId[0], 1, true);
        // 查询数据
        selectDataByAllWays(testRelId[0], 1, 0, true);
    }
    // 校验表的数据
    VOS_UINT32 expectvalue[10] = {0};
    for (int i = 0; i < 10; i++) {
        expectvalue[i] = i;
    }
    ValidateExpectData(testRelId[0], 10, &testRelDef[0], expectvalue, true);
    // 添加定时器,等待purger线程回收
    time_t startTime = time(NULL);
    time_t currentTime;
    int timeout = 1;
    int32_t compressTime = 0;
    // 压缩数据
    currentTime = time(NULL);
    while (currentTime - startTime < timeout && compressTime == 0) {
        currentTime = time(NULL);
        ret = Test_CompressTable(g_testDbId, testRelId[0], g_DeleteThreshold, true, &compressTime);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    if (compressTime == 0) {
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, -1);
    }
    // 校验表内存
    VOS_UINT32 TblSize = 0;
    ret = DBDBG_GetTblMemSize(g_testDbId, testRelId[0], &TblSize);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(oldTblSize, TblSize);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
}
// 150.在临界值循环插入删除数据后进行表内存查询
TEST_F(SharedSpace_Test, V1Com_039_150)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef);
    sleep(10);
    // 表写入少量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], g_DeleteThreshold - 1, true);
    }
    // 循环插入删除数据
    for (int i = 0; i < 3; i++) {
        // 插入数据至超出阈值
        Insert(
            testRelId[0], g_Threshold - g_DeleteThreshold + 2, true, g_testDbId, TPC_GLOBAL_CDB, g_DeleteThreshold - 1);
        // 校验表迁出
        SelectView(testRelId[0], false, DB_TPC_SYSVIEW_GET_COMMOM_AREA);
        // 删除数据至低于阈值
        for (int j = g_DeleteThreshold - 2; j < g_Threshold + 1; j++) {
            Delete(testRelId[0], j, true);
        }
        // 校验表迁入
        sleep(3);
        Insert(testRelId[0], 1, true, g_testDbId, TPC_GLOBAL_CDB, g_DeleteThreshold - 2);
        SelectView(testRelId[0], true, DB_TPC_SYSVIEW_GET_COMMOM_AREA);
    }
    // 校验表的数据
    VOS_UINT32 expectvalue[60] = {0};
    for (int i = 0; i < 60; i++) {
        expectvalue[i] = i;
    }
    ValidateExpectData(testRelId[0], 60, &testRelDef[0], expectvalue, true);
    // 校验表内存
    VOS_UINT32 TblSize = 0;
    ret = DBDBG_GetTblMemSize(g_testDbId, testRelId[0], &TblSize);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    if (TblSize >= 32 * 1024) {
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, -1);
    }
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
}
// 151.共享空间中对所有表进行多次增删改查后进行DB内存查询
TEST_F(SharedSpace_Test, V1Com_039_151)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef);
    // 表写入少量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 10, true);
    }
    VOS_UINT32 oldDbSize = 0;
    ret = DBDBG_GetDbMemSize(g_testDbId, &oldDbSize);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 进行多次增删改查操作
    for (int i = 0; i < 500; i++) {
        // 更新数据
        Update(testRelId[i % 9], 0, 2001, true);
        // 删除数据
        Delete(testRelId[i % 9], 2001, true);
        // 插入数据
        Insert(testRelId[i % 9], 1, true);
        // 查询数据
        selectDataByAllWays(testRelId[i % 9], 1, 0, true);
    }
    // 校验表的数据
    VOS_UINT32 expectvalue[10] = {0};
    for (int i = 0; i < 10; i++) {
        expectvalue[i] = i;
    }
    for (int i = 0; i < 9; i++) {
        ValidateExpectData(testRelId[i], 10, &testRelDef[i / 3], expectvalue, true);
    }
    // 校验DB内存
    VOS_UINT32 DbSize = 0;
    ret = DBDBG_GetDbMemSize(g_testDbId, &DbSize);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(oldDbSize, DbSize);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
}
// 152.共享空间中的表多次导入导出后进行DB内存查询
TEST_F(SharedSpace_Test, V1Com_039_152)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    char testDbName[DB_NAME_LEN] = "testDb";
    g_testDbCfg.enPersistent = DB_CKP_COMPLETE;
    char filePath[64] = "./filePath/perDbPath";
    ret = TPC_CreateDB((VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    g_testDbCfg.enPersistent = DB_CKP_NONE;
    uint32_t testDbId = 0;
    ret = TPC_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef, testDbId);
    // 所有表写入少量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 10, true, testDbId);
    }
    VOS_UINT32 oldDbSize = 0;
    ret = DBDBG_GetDbMemSize(testDbId, &oldDbSize);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < 50; i++) {
        // 导出DB
        char exportFile[64] = "./filePath/export.txt";
        ret = TPC_BkpPhy(testDbId, (uint8_t *)exportFile);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // replace进行导入DB
        DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = true};
        ret = TPC_Restore((uint8_t *)exportFile, (VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, DB_RESTORETYPE_REPLACE,
            &stDbConfig, 1);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 校验数据
    for (int i = 0; i < 9; i++) {
        VOS_UINT32 expectvalue[10] = {0};
        for (int i = 0; i < 10; i++) {
            expectvalue[i] = i;
        }
        ValidateExpectData(testRelId[i], 10, &testRelDef[i / 3], expectvalue, true, testDbId);
    }
    // 校验DB内存
    VOS_UINT32 DbSize = 0;
    ret = DBDBG_GetDbMemSize(testDbId, &DbSize);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(oldDbSize, DbSize);

    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
    // 关闭DB
    ret = TPC_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)testDbName, TPC_WAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
}
// 153.共享空间中对所有表进行隔行删除后进行DB内存查询
TEST_F(SharedSpace_Test, V1Com_039_153)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef);
    // 表写入少量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 300, true);
    }
    VOS_UINT32 oldDbSize = 0;
    ret = DBDBG_GetDbMemSize(g_testDbId, &oldDbSize);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 对所有表进行隔行删除
    for (int i = 0; i < 300; i++) {
        if (i % 2 == 0) {
            for (int j = 0; j < 9; j++) {
                Delete(testRelId[j], i, true);
            }
        }
    }
    // 校验DB内存
    VOS_UINT32 DbSize = 0;
    ret = DBDBG_GetDbMemSize(g_testDbId, &DbSize);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(oldDbSize, DbSize);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
}
// 154.开启CDB对独立空间中的表进行插入，未提交前查询表信息
TEST_F(SharedSpace_Test, V1Com_039_154)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef);
    // 表写入大量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 1000, true);
    }
    // 开启CDB
    VOS_UINT32 CdbId = 0;
    ret = TPC_BeginCDB(g_testDbId, &CdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入数据
    Insert(testRelId[0], 1, true, g_testDbId, CdbId, 1000);
    // 查询表信息
    DB_RELATION_INFO relationInfo = {0};
    ret = DB_GetTblInfo(g_testDbId, testRelId[0], &relationInfo);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(1000, relationInfo.ulActualRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(32, relationInfo.ulRecLength);
    // 校验表的数据
    VOS_UINT32 expectvalue[1000] = {0};
    for (int i = 0; i < 1000; i++) {
        expectvalue[i] = i;
    }
    ValidateExpectData(testRelId[0], 1000, &testRelDef[0], expectvalue, true);
    // 提交CDB
    ret = TPC_CommitCDB(CdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
}
// 155.开启CDB对独立空间中的表进行删除，未提交前查询表记录数
TEST_F(SharedSpace_Test, V1Com_039_155)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef);
    // 表写入大量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 1000, true);
    }
    // 开启CDB
    VOS_UINT32 CdbId = 0;
    ret = TPC_BeginCDB(g_testDbId, &CdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除数据
    for (int i = 60; i < 1000; i++) {
        Delete(testRelId[0], i, true, 1, g_testDbId, CdbId);
    }
    // 查询表记录数
    VOS_UINT32 actRec = 0;
    ret = DBS_GetRelActRec(g_testDbId, testRelId[0], &actRec);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(1000, actRec);
    // 校验表的数据
    VOS_UINT32 expectvalue[1000] = {0};
    for (int i = 0; i < 1000; i++) {
        expectvalue[i] = i;
    }
    ValidateExpectData(testRelId[0], 1000, &testRelDef[0], expectvalue, true);
    // 提交CDB
    ret = TPC_CommitCDB(CdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
}
// 156.开启CDB对独立空间中的表进行更新，未提交前查询表记录数
TEST_F(SharedSpace_Test, V1Com_039_156)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef);
    // 表写入大量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 1000, true);
    }
    // 开启CDB
    VOS_UINT32 CdbId = 0;
    ret = TPC_BeginCDB(g_testDbId, &CdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 更新数据
    Update(testRelId[0], 0, 2001, true, 1, g_testDbId, CdbId);
    // 查询表记录数
    VOS_UINT32 actRec = 0;
    ret = DBS_GetRelActRec(g_testDbId, testRelId[0], &actRec);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(1000, actRec);
    // 校验表的数据
    VOS_UINT32 expectvalue[1000] = {0};
    for (int i = 0; i < 1000; i++) {
        expectvalue[i] = i;
    }
    ValidateExpectData(testRelId[0], 1000, &testRelDef[0], expectvalue, true);
    // 提交CDB
    ret = TPC_CommitCDB(CdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
}
// 157.开启CDB对独立空间中的表进行插入，回滚后查询表记录数
TEST_F(SharedSpace_Test, V1Com_039_157)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef);
    // 表写入大量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 1000, true);
    }
    // 开启CDB
    VOS_UINT32 CdbId = 0;
    ret = TPC_BeginCDB(g_testDbId, &CdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入数据
    Insert(testRelId[0], 1, true, g_testDbId, CdbId, 1000);
    // 回滚CDB
    ret = TPC_RollbackCDB(CdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表记录数
    VOS_UINT32 actRec = 0;
    ret = DBS_GetRelActRec(g_testDbId, testRelId[0], &actRec);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(1000, actRec);
    // 校验表的数据
    VOS_UINT32 expectvalue[1000] = {0};
    for (int i = 0; i < 1000; i++) {
        expectvalue[i] = i;
    }
    ValidateExpectData(testRelId[0], 1000, &testRelDef[0], expectvalue, true);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
}
// 158.开启CDB对独立空间中的表进行删除，回滚后查询表记录数
TEST_F(SharedSpace_Test, V1Com_039_158)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef);
    // 表写入大量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 1000, true);
    }
    // 开启CDB
    VOS_UINT32 CdbId = 0;
    ret = TPC_BeginCDB(g_testDbId, &CdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除数据
    for (int i = 60; i < 1000; i++) {
        Delete(testRelId[0], i, true, 1, g_testDbId, CdbId);
    }
    // 回滚CDB
    ret = TPC_RollbackCDB(CdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表记录数
    VOS_UINT32 actRec = 0;
    ret = DBS_GetRelActRec(g_testDbId, testRelId[0], &actRec);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(1000, actRec);
    // 校验表的数据
    VOS_UINT32 expectvalue[1000] = {0};
    for (int i = 0; i < 1000; i++) {
        expectvalue[i] = i;
    }
    ValidateExpectData(testRelId[0], 1000, &testRelDef[0], expectvalue, true);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
}
// 159.开启CDB对独立空间中的表进行更新，回滚后查询表记录数
TEST_F(SharedSpace_Test, V1Com_039_159)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef);
    // 表写入大量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 1000, true);
    }
    // 开启CDB
    VOS_UINT32 CdbId = 0;
    ret = TPC_BeginCDB(g_testDbId, &CdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 更新数据
    Update(testRelId[0], 0, 2001, true, 1, g_testDbId, CdbId);
    // 回滚CDB
    ret = TPC_RollbackCDB(CdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表记录数
    VOS_UINT32 actRec = 0;
    ret = DBS_GetRelActRec(g_testDbId, testRelId[0], &actRec);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(1000, actRec);
    // 校验表的数据
    VOS_UINT32 expectvalue[1000] = {0};
    for (int i = 0; i < 1000; i++) {
        expectvalue[i] = i;
    }
    ValidateExpectData(testRelId[0], 1000, &testRelDef[0], expectvalue, true);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
}
// 160.开启CDB对独立空间中的表进行增删改查，提交后查询表记录数
TEST_F(SharedSpace_Test, V1Com_039_160)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef);
    // 表写入大量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 1000, true);
    }
    // 开启CDB
    VOS_UINT32 CdbId = 0;
    ret = TPC_BeginCDB(g_testDbId, &CdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 进行增删改查操作
    // 更新数据
    Update(testRelId[0], 0, 2001, true, 1, g_testDbId, CdbId);
    // 删除数据
    Delete(testRelId[0], 2001, true, 1, g_testDbId, CdbId);
    // 插入数据
    Insert(testRelId[0], 1, true, g_testDbId, CdbId);
    // 查询数据
    selectDataByAllWays(testRelId[0], 1, 0, true, CdbId);

    // 提交CDB
    ret = TPC_CommitCDB(CdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表记录数
    VOS_UINT32 actRec = 0;
    ret = DBS_GetRelActRec(g_testDbId, testRelId[0], &actRec);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(1000, actRec);
    // 校验表的数据
    VOS_UINT32 expectvalue[1000] = {0};
    for (int i = 0; i < 1000; i++) {
        expectvalue[i] = i;
    }
    ValidateExpectData(testRelId[0], 1000, &testRelDef[0], expectvalue, true);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
}
// 161.对独立空间中的表进行插入，查询表信息
TEST_F(SharedSpace_Test, V1Com_039_161)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    char testDbName[DB_NAME_LEN] = "testDb";
    char filePath[64] = "./filePath/perDbPath";
    uint32_t testDbId = 0;
    // 建DB
    ret = DB_CreateDB((VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(false, testRelId, testRelDef, testDbId);
    // 表写入大量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 1000, false, testDbId);
    }
    // 插入数据
    Insert(testRelId[0], 1, false, testDbId, TPC_GLOBAL_CDB, 1000);
    // 查询表信息
    DB_RELATION_INFO relationInfo = {0};
    ret = DB_GetTblInfo(testDbId, testRelId[0], &relationInfo);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(1001, relationInfo.ulActualRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(32, relationInfo.ulRecLength);
    // 校验表的数据
    VOS_UINT32 expectvalue[1001] = {0};
    for (int i = 0; i < 1001; i++) {
        expectvalue[i] = i;
    }
    ValidateExpectData(testRelId[0], 1001, &testRelDef[0], expectvalue, false, testDbId);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
    // 关闭DB
    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, DROP_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
}
// 162.对独立空间中的表进行删除，查询表记录数
TEST_F(SharedSpace_Test, V1Com_039_162)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    char testDbName[DB_NAME_LEN] = "testDb";
    char filePath[64] = "./filePath/perDbPath";
    uint32_t testDbId = 0;
    // 建DB
    ret = DB_CreateDB((VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(false, testRelId, testRelDef, testDbId);
    // 表写入大量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 1000, false, testDbId);
    }

    // 删除数据
    Delete(testRelId[0], 999, false, 1, testDbId, TPC_GLOBAL_CDB);
    // 查询表记录数
    VOS_UINT32 actRec = 0;
    ret = DBS_GetRelActRec(testDbId, testRelId[0], &actRec);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(999, actRec);
    // 校验表的数据
    VOS_UINT32 expectvalue[999] = {0};
    for (int i = 0; i < 999; i++) {
        expectvalue[i] = i;
    }
    ValidateExpectData(testRelId[0], 999, &testRelDef[0], expectvalue, false, testDbId);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
    // 关闭DB
    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, DROP_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
}
// 163.对独立空间中的表进行更新，查询表记录数
TEST_F(SharedSpace_Test, V1Com_039_163)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    char testDbName[DB_NAME_LEN] = "testDb";
    char filePath[64] = "./filePath/perDbPath";
    uint32_t testDbId = 0;
    // 建DB
    ret = DB_CreateDB((VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(false, testRelId, testRelDef, testDbId);
    // 表写入大量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 1000, false, testDbId);
    }
    // 更新数据
    Update(testRelId[0], 0, 2001, false, 1, testDbId, TPC_GLOBAL_CDB);
    // 查询表记录数
    VOS_UINT32 actRec = 0;
    ret = DBS_GetRelActRec(testDbId, testRelId[0], &actRec);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(1000, actRec);
    // 校验表的数据
    VOS_UINT32 expectvalue[1000] = {0};
    for (int i = 0; i < 1000; i++) {
        expectvalue[i] = i + 1;
    }
    expectvalue[999] = 2001;
    ValidateExpectData(testRelId[0], 1000, &testRelDef[0], expectvalue, false, testDbId);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
    // 关闭DB
    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, DROP_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
}
// 164.在临界值循环插入删除数据后查询表信息
TEST_F(SharedSpace_Test, V1Com_039_164)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    char testDbName[DB_NAME_LEN] = "testDb";
    char filePath[64] = "./filePath/perDbPath";
    uint32_t testDbId = 0;
    // 建DB
    ret = DB_CreateDB((VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(false, testRelId, testRelDef, testDbId);
    // 表写入大量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 60, false, testDbId);
    }
    // 循环插入删除数据
    for (int i = 0; i < 200; i++) {
        Insert(testRelId[0], 784, false, testDbId, TPC_GLOBAL_CDB, 60);
        for (int j = 60; j < 844; j++) {
            Delete(testRelId[0], j, false, 1, testDbId);
        }
    }
    // 查询表信息
    DB_RELATION_INFO relationInfo = {0};
    ret = DB_GetTblInfo(testDbId, testRelId[0], &relationInfo);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(60, relationInfo.ulActualRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(32, relationInfo.ulRecLength);
    // 校验表的数据
    VOS_UINT32 expectvalue[60] = {0};
    for (int i = 0; i < 60; i++) {
        expectvalue[i] = i;
    }
    ValidateExpectData(testRelId[0], 60, &testRelDef[0], expectvalue, false, testDbId);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
    // 关闭DB
    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, DROP_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
}
// 165.共享空间中的表导入导出后查询表记录数及CRC值
TEST_F(SharedSpace_Test, V1Com_039_165)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    DB_REG_FEATURE_STRU pstFeature = {0};
    VOS_UINT32 selectways[7] = {1, 1, 1, 1, 1, 1, 1};
    SelectcfgInterface(&pstFeature, selectways);
    // 注册导入方式为replace
    DB_RESTORETYPE_ENUM enRestoreType = DB_RESTORETYPE_REPLACE;
    ret = DB_RegDbRestoreTypeCfg(enRestoreType, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    char testDbName[DB_NAME_LEN] = "testDb";
    char filePath[64] = "./filePath/perDbPath";
    uint32_t testDbId = 0;
    // 建DB
    ret = DB_CreateDB2((VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(false, testRelId, testRelDef, testDbId);
    // 表写入少量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 10, false, testDbId);
    }
    // 导出DB
    char exportFile[64] = "./filePath/export.txt";
    ret = DB_BkpPhy(testDbId, (uint8_t *)exportFile);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // replace进行导入DB
    ret = DB_RestoreDB2((uint8_t *)exportFile, (VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &pstFeature);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询表记录数
    VOS_UINT32 actRec = 0;
    ret = DBS_GetRelActRec(testDbId, testRelId[0], &actRec);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(10, actRec);
    // 校验表的数据
    VOS_UINT32 expectvalue[10] = {0};
    for (int i = 0; i < 10; i++) {
        expectvalue[i] = i;
    }
    ValidateExpectData(testRelId[0], 10, &testRelDef[0], expectvalue, false, testDbId);
    // 调用DB_CheckDBRel校验表的CRC值
    VOS_UINT16 usFailRelId = 0;
    VOS_UINT32 ulRelCount = 9;
    ret = DB_CheckDBRel(testDbId, testRelId, ulRelCount, &usFailRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0xFFFF, usFailRelId);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
    // 关闭DB
    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, DROP_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
}
// 166.共享空间中对所有表进行隔行删除后全部进行表压缩
TEST_F(SharedSpace_Test, V1Com_039_166)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("mkdir filePath");
    char testDbName[DB_NAME_LEN] = "testDb";
    char filePath[64] = "./filePath/perDbPath";
    uint32_t testDbId = 0;
    // 建DB
    ret = DB_CreateDB((VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(false, testRelId, testRelDef, testDbId);
    // 表写入少量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], g_Threshold - 1, false, testDbId);
    }
    // 对所有表进行隔行删除
    for (int i = 0; i < g_Threshold - 1; i++) {
        if (i % 2 == 0) {
            for (int j = 0; j < 9; j++) {
                Delete(testRelId[j], i, false, 1, testDbId);
            }
        }
    }
    SelectView(testRelId[0], true, DB_TPC_SYSVIEW_GET_COMMOM_AREA, testDbId);
    VOS_UINT32 oldDbSize = 0;
    ret = DBDBG_GetDbMemSize(testDbId, &oldDbSize);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    int32_t compressTime = 0;
    // 对所有表进行表压缩
    for (int i = 0; i < 9; i++) {
        // 压缩数据
        ret = Test_CompressTable(testDbId, testRelId[i], 500, false, &compressTime);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    if (compressTime == 0) {
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, -1);
    }
    VOS_UINT32 DbSize = 0;
    ret = DBDBG_GetDbMemSize(g_testDbId, &DbSize);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    if (DbSize >= oldDbSize) {
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, -1);
    }
    // 校验表的数据
    VOS_UINT32 expectvalue[g_Threshold / 2] = {0};
    int count = 0;
    for (int i = 0; i < g_Threshold - 1; i++) {
        if (i % 2 != 0) {
            expectvalue[count] = i;
            count++;
        }
    }
    for (int i = 0; i < 9; i++) {
        ValidateExpectData(testRelId[i], g_Threshold / 2, &testRelDef[i / 3], expectvalue, false, testDbId);
    }
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
    // 关闭DB
    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, DROP_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
}
// 167.在独立空间中对表进行删除至低于阈值后进行表压缩
TEST_F(SharedSpace_Test, V1Com_039_167)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("mkdir filePath");
    char testDbName[DB_NAME_LEN] = "testDb";
    char filePath[64] = "./filePath/perDbPath";
    uint32_t testDbId = 0;
    // 建DB
    ret = DB_CreateDB((VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 打开DB
    ret = DB_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(false, testRelId, testRelDef, testDbId);
    sleep(10);
    // 表写入大量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 2000, false, testDbId);
    }
    // 对表进行删除
    for (int i = g_DeleteThreshold; i < 2000; i++) {
        Delete(testRelId[0], i, false, 1, testDbId);
    }
    // 校验表迁入
    sleep(10);
    Update(testRelId[0], 0, g_DeleteThreshold, false, 1, testDbId);
    SelectView(testRelId[0], true, DB_TPC_SYSVIEW_GET_COMMOM_AREA, testDbId);

    VOS_UINT32 oldDbSize = 0;
    ret = DBDBG_GetDbMemSize(testDbId, &oldDbSize);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    int32_t compressTime = 0;
    // 压缩数据
    ret = Test_CompressTable(testDbId, testRelId[0], 2000, false, &compressTime);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT32 DbSize = 0;
    ret = DBDBG_GetDbMemSize(testDbId, &DbSize);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(oldDbSize, DbSize);
    // 校验表的数据
    VOS_UINT32 expectvalue[g_DeleteThreshold] = {0};
    for (int i = 0; i < g_DeleteThreshold; i++) {
        expectvalue[i] = i + 1;
    }
    ValidateExpectData(testRelId[0], g_DeleteThreshold, &testRelDef[0], expectvalue, false, testDbId);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
    // 关闭DB
    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, DROP_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
}
// 168.在共享空间插入数据至超出阈值后进行隔行删除后进行表压缩
TEST_F(SharedSpace_Test, V1Com_039_168)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef);
    // 表写入大量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 10000, true);
    }
    // 对表进行隔行删除
    for (int i = 0; i < 10000; i++) {
        if (i % 2 == 0) {
            for (int j = 0; j < 9; j++) {
                Delete(testRelId[j], i, true);
            }
        }
    }
    SelectView(testRelId[0], false, DB_TPC_SYSVIEW_GET_COMMOM_AREA);
    VOS_UINT32 oldDbSize = 0;
    ret = DBDBG_GetDbMemSize(g_testDbId, &oldDbSize);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    int32_t compressTime = 0;
    for (int i = 0; i < 9; i++) {
        // 压缩数据
        ret = Test_CompressTable(g_testDbId, testRelId[i], 10000, true, &compressTime);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    if (compressTime == 0) {
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, -1);
    }
    VOS_UINT32 DbSize = 0;
    ret = DBDBG_GetDbMemSize(g_testDbId, &DbSize);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    if (DbSize >= oldDbSize) {
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, -1);
    }
    // 校验表的数据
    VOS_UINT32 expectvalue[5000] = {0};
    int count = 0;
    for (int i = 0; i < 10000; i++) {
        if (i % 2 != 0) {
            expectvalue[count] = i;
            count++;
        }
    }
    ValidateExpectData(testRelId[0], 5000, &testRelDef[0], expectvalue, true);
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
}
// 169.共享空间中导入导出，对表进行隔行删除后进行表压缩
TEST_F(SharedSpace_Test, V1Com_039_169)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    // 建多张表
    DB_REL_DEF_STRU *testRelDef = NULL;
    uint16_t testRelId[2000] = {0};
    char tblName[30] = {0};
    for (int i = 0; i < 2000; i++) {
        sprintf(tblName, "table_%d", i);
        ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId[i], testRelDef, tblName);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 表写入少量数据
    for (int i = 0; i < 2000; i++) {
        Insert(testRelId[i], g_DeleteThreshold, true);
    }
    // 导出DB
    char exportFile[64] = "./filePath/export.txt";
    ret = TPC_BkpPhy(g_testDbId, (uint8_t *)exportFile);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_CloseDB(g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_testDbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // replace进行导入DB
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_Restore((uint8_t *)exportFile, (VOS_UINT8 *)g_testDbName, NULL, DB_RESTORETYPE_DISCARD, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 对所有表进行隔行删除
    for (int i = 0; i < g_DeleteThreshold; i++) {
        if (i % 2 == 0) {
            for (int j = 0; j < 2000; j++) {
                Delete(testRelId[j], i, true, 1, g_testDbId);
            }
        }
    }
    SelectView(testRelId[0], true, DB_TPC_SYSVIEW_GET_COMMOM_AREA);
    VOS_UINT32 oldDbSize = 0;
    ret = DBDBG_GetDbMemSize(g_testDbId, &oldDbSize);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 添加定时器,等待purger线程回收
    time_t startTime = time(NULL);
    time_t currentTime;
    int timeout = 10;
    int32_t compressTime = 0;
    // 压缩数据
    currentTime = time(NULL);
    while (currentTime - startTime < timeout && compressTime == 0) {
        currentTime = time(NULL);
        ret = Test_CompressTable(g_testDbId, testRelId[5], g_DeleteThreshold, true, &compressTime);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    if (compressTime == 0) {
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, -1);
    }
    VOS_UINT32 DbSize = 0;
    ret = DBDBG_GetDbMemSize(g_testDbId, &DbSize);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    if (DbSize >= oldDbSize) {
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, -1);
    }
    SelectView(testRelId[0], true, DB_TPC_SYSVIEW_GET_COMMOM_AREA);
    system("rm -rf ./filePath");
}
// 170.独立空间内开启cdb1和cdb2，cdb1插入数据，cdb2可以查询到
TEST_F(SharedSpace_Test, V1Com_039_170)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 建表
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入数据
    Insert(testRelId, 2000, true);

    // 开启cdb1和cdb2
    uint32_t cdbId1 = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    uint32_t cdbId2 = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbId2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // cdb1插入数据
    Insert(testRelId, 1, true, g_testDbId, cdbId1, 2000);
    selectDataByAllWays(testRelId, 1, 2000, true, cdbId1);

    // cdb2查数据
    selectDataByAllWays(testRelId, 1, 2000, true, cdbId2);

    // 提交cdb1和cdb2
    ret = TPC_CommitCDB(cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_CommitCDB(cdbId2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TestFreeTblStructDef(&testRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 171.独立空间内开启cdb1和cdb2，cdb1更新数据，cdb2可以查询到
TEST_F(SharedSpace_Test, V1Com_039_171)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 建表
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入数据
    Insert(testRelId, 2000, true);

    // 开启cdb1和cdb2
    uint32_t cdbId1 = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    uint32_t cdbId2 = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbId2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // cdb1更新数据
    Update(testRelId, 0, 2000, true, 1, g_testDbId, cdbId1);
    selectDataByAllWays(testRelId, 1, 2000, true, cdbId1);

    // cdb2查数据
    selectDataByAllWays(testRelId, 1, 2000, true, cdbId2);

    // 提交cdb1和cdb2
    ret = TPC_CommitCDB(cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_CommitCDB(cdbId2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TestFreeTblStructDef(&testRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 172.独立空间内开启cdb1和cdb2，cdb1删除数据，cdb2可以查询不到
TEST_F(SharedSpace_Test, V1Com_039_172)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 建表
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入数据
    Insert(testRelId, 2000, true);

    // 开启cdb1和cdb2
    uint32_t cdbId1 = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    uint32_t cdbId2 = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbId2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // cdb1删除数据
    Delete(testRelId, 0, true, 1, g_testDbId, cdbId1);
    selectDataByAllWays(testRelId, 1, 0, true, cdbId1, VOS_ERRNO_DB_RECNOTEXIST);

    // cdb2查数据
    selectDataByAllWays(testRelId, 1, 0, true, cdbId2, VOS_ERRNO_DB_RECNOTEXIST);

    // 提交cdb1和cdb2
    ret = TPC_CommitCDB(cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_CommitCDB(cdbId2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    TestFreeTblStructDef(&testRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 173.独立空间内开启cdb1和cdb2，cdb1和cdb2插入不同数据，均插入成功
TEST_F(SharedSpace_Test, V1Com_039_173)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 建表
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入数据
    Insert(testRelId, 2000, true);

    // 开启cdb1
    uint32_t cdbId1 = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 开启cdb2
    uint32_t cdbId2 = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbId2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // cdb1插入数据
    Insert(testRelId, 1, true, g_testDbId, cdbId1, 2000);
    // cdb2插入数据
    Insert(testRelId, 1, true, g_testDbId, cdbId2, 2001);

    // cdb1查询
    selectDataByAllWays(testRelId, 1, 2000, true, cdbId1);
    selectDataByAllWays(testRelId, 1, 2001, true, cdbId1);
    // cdb2查询
    selectDataByAllWays(testRelId, 1, 2000, true, cdbId2);
    selectDataByAllWays(testRelId, 1, 2001, true, cdbId2);

    // 提交cdb1和cdb2
    ret = TPC_CommitCDB(cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_CommitCDB(cdbId2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // RDB查询
    selectDataByAllWays(testRelId, 1, 2000, true);
    selectDataByAllWays(testRelId, 1, 2001, true);

    TestFreeTblStructDef(&testRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 174.独立空间内开启cdb1和cdb2，cdb1和cdb2插入相同数据，cdb2插入时报错主键冲突
TEST_F(SharedSpace_Test, V1Com_039_174)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 建表
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入数据
    Insert(testRelId, 2000, true);

    // 开启cdb1
    uint32_t cdbId1 = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 开启cdb2
    uint32_t cdbId2 = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbId2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // cdb1插入数据
    Insert(testRelId, 1, true, g_testDbId, cdbId1, 2000);
    // cdb2插入数据
    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = DBS_GetRelRecLen(g_testDbId, g_testDbId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入数据
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = tblRecLen;
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    UniqueIndexTblDefT tblFieldDataDefSet1;
    testSetAllField(&tblFieldDataDefSet1, 2000, tblRecLen);
    memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &tblFieldDataDefSet1, pstDsBufSet.usRecLen);
    pstDsBufSet.StdBuf.pucData = pucDataSet;
    ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId, &pstDsBufSet);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_KEYDUPLICATE, ret);
    TEST_V1_FREE(pucDataSet);

    // cdb1查询
    selectDataByAllWays(testRelId, 1, 2000, true, cdbId1);
    // cdb2查询
    selectDataByAllWays(testRelId, 1, 2000, true, cdbId2);

    // 提交cdb1和cdb2
    ret = TPC_CommitCDB(cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_CommitCDB(cdbId2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // RDB查询
    selectDataByAllWays(testRelId, 1, 2000, true);

    TestFreeTblStructDef(&testRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 175.独立空间内开启cdb1和cdb2，cdb1插入和cdb2更新相同数据，均成功
TEST_F(SharedSpace_Test, V1Com_039_175)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 建表
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入数据
    Insert(testRelId, 2000, true);

    // 开启cdb1
    uint32_t cdbId1 = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 开启cdb2
    uint32_t cdbId2 = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbId2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // cdb1插入数据
    Insert(testRelId, 1, true, g_testDbId, cdbId1, 2000);
    // cdb2更新数据
    Update(testRelId, 2000, 2001, true, 1, g_testDbId, cdbId2);

    // cdb1查询
    selectDataByAllWays(testRelId, 1, 2000, true, cdbId1, VOS_ERRNO_DB_RECNOTEXIST);
    selectDataByAllWays(testRelId, 1, 2001, true, cdbId1);
    // cdb2查询
    selectDataByAllWays(testRelId, 1, 2000, true, cdbId2, VOS_ERRNO_DB_RECNOTEXIST);
    selectDataByAllWays(testRelId, 1, 2001, true, cdbId2);

    // 提交cdb1和cdb2
    ret = TPC_CommitCDB(cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_CommitCDB(cdbId2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // RDB查询
    selectDataByAllWays(testRelId, 1, 2000, true, TPC_GLOBAL_CDB, VOS_ERRNO_DB_RECNOTEXIST);
    selectDataByAllWays(testRelId, 1, 2001, true);

    TestFreeTblStructDef(&testRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 176.独立空间内开启cdb1和cdb2，cdb1插入和cdb2删除相同数据，均成功
TEST_F(SharedSpace_Test, V1Com_039_176)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 建表
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入数据
    Insert(testRelId, 2000, true);

    // 开启cdb1
    uint32_t cdbId1 = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 开启cdb2
    uint32_t cdbId2 = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbId2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // cdb1插入数据
    Insert(testRelId, 1, true, g_testDbId, cdbId1, 2000);
    // cdb2删除数据
    Delete(testRelId, 2000, true, 1, g_testDbId, cdbId2);

    // cdb1查询
    selectDataByAllWays(testRelId, 1, 2000, true, cdbId1, VOS_ERRNO_DB_RECNOTEXIST);
    // cdb2查询
    selectDataByAllWays(testRelId, 1, 2000, true, cdbId2, VOS_ERRNO_DB_RECNOTEXIST);

    // 提交cdb1和cdb2
    ret = TPC_CommitCDB(cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_CommitCDB(cdbId2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // RDB查询
    selectDataByAllWays(testRelId, 1, 2000, true, TPC_GLOBAL_CDB, VOS_ERRNO_DB_RECNOTEXIST);

    TestFreeTblStructDef(&testRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 177.独立空间内开启cdb1和cdb2，cdb1和cdb2更新不同数据，均成功
TEST_F(SharedSpace_Test, V1Com_039_177)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 建表
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入数据
    Insert(testRelId, 2000, true);

    // 开启cdb1和cdb2
    uint32_t cdbId1 = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    uint32_t cdbId2 = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbId2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // cdb1更新数据
    Update(testRelId, 0, 2000, true, 1, g_testDbId, cdbId1);
    // cdb2更新数据
    Update(testRelId, 1, 2001, true, 1, g_testDbId, cdbId2);

    // cdb1查询数据
    selectDataByAllWays(testRelId, 1, 0, true, cdbId1, VOS_ERRNO_DB_RECNOTEXIST);
    selectDataByAllWays(testRelId, 1, 2000, true, cdbId1);
    // cdb2查询数据
    selectDataByAllWays(testRelId, 1, 1, true, cdbId2, VOS_ERRNO_DB_RECNOTEXIST);
    selectDataByAllWays(testRelId, 1, 2001, true, cdbId2);

    // 提交cdb1和cdb2
    ret = TPC_CommitCDB(cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_CommitCDB(cdbId2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // RDB查询
    selectDataByAllWays(testRelId, 1, 2000, true);
    selectDataByAllWays(testRelId, 1, 2001, true);

    TestFreeTblStructDef(&testRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 178.独立空间内开启cdb1和cdb2，cdb1和cdb2更新不同数据为相同值，均成功
TEST_F(SharedSpace_Test, V1Com_039_178)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 建表
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入数据
    Insert(testRelId, 2000, true);

    // 开启cdb1和cdb2
    uint32_t cdbId1 = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    uint32_t cdbId2 = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbId2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // cdb1更新数据
    Update(testRelId, 0, 2000, true, 1, g_testDbId, cdbId1);
    // cdb2更新数据
    DB_COND_STRU pstCond1;
    pstCond1.usCondNum = 1;
    pstCond1.aCond[0].ucFieldId = 1;
    pstCond1.aCond[0].enOp = DB_OP_EQUAL;
    *(uint32_t *)pstCond1.aCond[0].aucValue = 1;

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = DBS_GetRelRecLen(g_testDbId, testRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = tblRecLen;
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    UniqueIndexTblDefT tblFieldDataDefSet1;

    testSetAllField(&tblFieldDataDefSet1, 2000, tblRecLen);
    memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &tblFieldDataDefSet1, pstDsBufSet.usRecLen);

    pstDsBufSet.StdBuf.pucData = pucDataSet;
    VOS_UINT32 udpRecNum = 0;
    ret = TPC_UpdateRec(cdbId2, g_testDbId, testRelId, &pstCond1, &pstFldFilter, &pstDsBufSet, &udpRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_CHGKEY, ret);
    TEST_V1_FREE(pucDataSet);

    // cdb1查询数据
    selectDataByAllWays(testRelId, 1, 0, true, cdbId1, VOS_ERRNO_DB_RECNOTEXIST);
    selectDataByAllWays(testRelId, 1, 2000, true, cdbId1);
    // cdb2查询数据
    selectDataByAllWays(testRelId, 1, 1, true, cdbId2);
    selectDataByAllWays(testRelId, 1, 2000, true, cdbId2);

    // 提交cdb1和cdb2
    ret = TPC_CommitCDB(cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_CommitCDB(cdbId2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // RDB查询
    selectDataByAllWays(testRelId, 1, 2000, true);
    selectDataByAllWays(testRelId, 1, 1, true);

    TestFreeTblStructDef(&testRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 179.独立空间内开启cdb1和cdb2，cdb1更新和cdb2删除相同数据，均成功
TEST_F(SharedSpace_Test, V1Com_039_179)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 建表
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入数据
    Insert(testRelId, 2000, true);

    // 开启cdb1和cdb2
    uint32_t cdbId1 = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    uint32_t cdbId2 = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbId2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // cdb1更新数据
    Update(testRelId, 0, 2000, true, 1, g_testDbId, cdbId1);
    // cdb2删除数据
    DB_COND_STRU stCond;
    stCond.usCondNum = 1;
    stCond.aCond[0].enOp = DB_OP_EQUAL;
    stCond.aCond[0].ucFieldId = 1;
    *(uint32_t *)stCond.aCond[0].aucValue = 0;

    uint32_t delRecNum = 0;
    ret = TPC_DeleteRec(cdbId2, g_testDbId, testRelId, &stCond, &delRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // cdb1查询数据
    selectDataByAllWays(testRelId, 1, 0, true, cdbId1, VOS_ERRNO_DB_RECNOTEXIST);
    selectDataByAllWays(testRelId, 1, 2000, true, cdbId1);
    // cdb2查询数据
    selectDataByAllWays(testRelId, 1, 0, true, cdbId2, VOS_ERRNO_DB_RECNOTEXIST);
    selectDataByAllWays(testRelId, 1, 2000, true, cdbId2);

    // 提交cdb1和cdb2
    ret = TPC_CommitCDB(cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_CommitCDB(cdbId2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // RDB查询
    selectDataByAllWays(testRelId, 1, 0, true, TPC_GLOBAL_CDB, VOS_ERRNO_DB_RECNOTEXIST);
    selectDataByAllWays(testRelId, 1, 2000, true);

    TestFreeTblStructDef(&testRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 180.独立空间内开启cdb1和cdb2，cdb1和cdb2删除不同数据，均删除成功
TEST_F(SharedSpace_Test, V1Com_039_180)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 建表
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入数据
    Insert(testRelId, 2000, true);

    // 开启cdb1和cdb2
    uint32_t cdbId1 = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    uint32_t cdbId2 = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbId2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // cdb1删除数据
    Delete(testRelId, 0, true, 1, g_testDbId, cdbId1);
    // cdb2删除数据
    Delete(testRelId, 1, true, 1, g_testDbId, cdbId2);

    // cdb1查询数据
    selectDataByAllWays(testRelId, 1, 0, true, cdbId1, VOS_ERRNO_DB_RECNOTEXIST);
    selectDataByAllWays(testRelId, 1, 1, true, cdbId1, VOS_ERRNO_DB_RECNOTEXIST);
    // cdb2查询数据
    selectDataByAllWays(testRelId, 1, 0, true, cdbId2, VOS_ERRNO_DB_RECNOTEXIST);
    selectDataByAllWays(testRelId, 1, 1, true, cdbId2, VOS_ERRNO_DB_RECNOTEXIST);

    // 提交cdb1和cdb2
    ret = TPC_CommitCDB(cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_CommitCDB(cdbId2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // RDB查询
    selectDataByAllWays(testRelId, 1, 0, true, TPC_GLOBAL_CDB, VOS_ERRNO_DB_RECNOTEXIST);
    selectDataByAllWays(testRelId, 1, 1, true, TPC_GLOBAL_CDB, VOS_ERRNO_DB_RECNOTEXIST);

    TestFreeTblStructDef(&testRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 181.独立空间内开启cdb1和cdb2，cdb1和cdb2删除相同数据，均删除成功
TEST_F(SharedSpace_Test, V1Com_039_181)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 建表
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入数据
    Insert(testRelId, 2000, true);

    // 开启cdb1和cdb2
    uint32_t cdbId1 = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    uint32_t cdbId2 = 0;
    ret = TPC_BeginCDB(g_testDbId, &cdbId2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // cdb1删除数据
    Delete(testRelId, 0, true, 1, g_testDbId, cdbId1);
    // cdb2删除数据
    DB_COND_STRU stCond;
    stCond.usCondNum = 1;
    stCond.aCond[0].enOp = DB_OP_EQUAL;
    stCond.aCond[0].ucFieldId = 1;
    *(uint32_t *)stCond.aCond[0].aucValue = 0;

    uint32_t delRecNum = 0;
    ret = TPC_DeleteRec(cdbId2, g_testDbId, testRelId, &stCond, &delRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // cdb1查询数据
    selectDataByAllWays(testRelId, 1, 0, true, cdbId1, VOS_ERRNO_DB_RECNOTEXIST);
    // cdb2查询数据
    selectDataByAllWays(testRelId, 1, 0, true, cdbId2, VOS_ERRNO_DB_RECNOTEXIST);

    // 提交cdb1和cdb2
    ret = TPC_CommitCDB(cdbId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_CommitCDB(cdbId2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // RDB查询
    selectDataByAllWays(testRelId, 1, 0, true, TPC_GLOBAL_CDB, VOS_ERRNO_DB_RECNOTEXIST);

    TestFreeTblStructDef(&testRelDef);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 187.部分表在共享空间时计算DB总内存和所有表内存查询
TEST_F(SharedSpace_Test, V1Com_039_187)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建多张表
    uint16_t testRelId[9] = {0};
    DB_REL_DEF_STRU *testRelDef = (DB_REL_DEF_STRU *)malloc(sizeof(DB_REL_DEF_STRU) * 3);
    CreateTestTb(true, testRelId, testRelDef);
    // 表写入少量数据
    for (int i = 0; i < 9; i++) {
        Insert(testRelId[i], 10, true);
        if (i % 2 == 0) {
            Insert(testRelId[i], 1000, true, g_testDbId, TPC_GLOBAL_CDB, 10);
        }
    }
    SelectView(0xFFFF, false, DB_TPC_SYSVIEW_GET_COMMOM_AREA);
    // 计算DB内存
    VOS_UINT32 DbSize = 0;
    ret = DBDBG_GetDbMemSize(g_testDbId, &DbSize);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 计算所有表内存
    VOS_UINT32 TblSize = 0;
    for (int i = 0; i < 9; i++) {
        VOS_UINT32 tblSize = 0;
        ret = DBDBG_GetTblMemSize(g_testDbId, testRelId[i], &tblSize);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        TblSize = TblSize + tblSize;
    }
    if (TblSize + 16576 >= DbSize) {
        V1_AW_MACRO_EXPECT_EQ_INT(TblSize + 16576, DbSize);
    }
    TestFreeTblStructDef(&testRelDef[0]);
    TestFreeTblStructDef(&testRelDef[1]);
    TestFreeTblStructDef(&testRelDef[2]);
}
// 188.表在共享空间中时，每插入一条数据查询一次表内存
TEST_F(SharedSpace_Test, V1Com_039_188)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表写入少量数据
    VOS_UINT32 lastTblSize = 0;
    for (int i = 0; i < 200; i++) {
        Insert(testRelId, 1, true, g_testDbId, TPC_GLOBAL_CDB, i);
        // 计算表内存
        VOS_UINT32 TblSize = 0;
        ret = DBDBG_GetTblMemSize(g_testDbId, testRelId, &TblSize);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        if (lastTblSize >= TblSize) {
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, -1);
        }
    }
    TestFreeTblStructDef(&testRelDef);
}
// 189.表在共享空间中时，每删除一条数据查询一次表内存
TEST_F(SharedSpace_Test, V1Com_039_189)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表写入少量数据
    Insert(testRelId, 200, true);
    // 计算表内存
    VOS_UINT32 lastTblSize = 0;
    ret = DBDBG_GetTblMemSize(g_testDbId, testRelId, &lastTblSize);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < 200; i++) {
        Delete(testRelId, i, true);
        // 计算表内存
        VOS_UINT32 TblSize = 0;
        ret = DBDBG_GetTblMemSize(g_testDbId, testRelId, &TblSize);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        if (lastTblSize <= TblSize) {
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, -1);
        }
    }
    TestFreeTblStructDef(&testRelDef);
}
// 190.无索引表在独立空间中查询索引内存
TEST_F(SharedSpace_Test, V1Com_039_190)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel2.json", &testRelId, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表写入大量数据
    Insert(testRelId, 1000, true);
    // 计算表索引内存
    DB_MEMUSAGE_INFO_STRU stMemInfo = {0};
    ret = DBDBG_GetDbMemUsageStats(g_testDbId, &stMemInfo);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(4480, stMemInfo.ulIndexSize);
    TestFreeTblStructDef(&testRelDef);
}
// 191.无索引且无法建立索引的表在共享空间中查询索引内存
TEST_F(SharedSpace_Test, V1Com_039_191)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建自定义数据类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 32;
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 建表
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel5.json", &testRelId, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表写入少量数据
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(g_testDbId, testRelId, &tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);
    for (int i = 0; i < 10; i++) {
        uint32_t fldVal[4];
        for (uint32_t j = 0; j < 4; j++) {
            fldVal[j] = i;
        }
        (void)memcpy_s(recBuf, tblRecLen, fldVal, tblRecLen);
        DB_DSBUF_STRU dsBuf = {.usRecLen = (VOS_UINT16)tblRecLen,
            .usRecNum = 1,
            .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_testDbId, testRelId, &dsBuf);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 计算表索引内存
    DB_MEMUSAGE_INFO_STRU stMemInfo = {0};
    ret = DBDBG_GetDbMemUsageStats(g_testDbId, &stMemInfo);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(448, stMemInfo.ulIndexSize);
    TEST_V1_FREE(recBuf);
    TestFreeTblStructDef(&testRelDef);
}
// 192.ulIntialSize*(变长头大小（24)+数据长度)略大于7/8页的情况，查询是否为共享空间，10s后再次查询
TEST_F(SharedSpace_Test, V1Com_039_192)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ulInitialSize = 0;
#if defined ENV_RTOSV2 && defined CPU_BIT_32
    ulInitialSize = 256;  // ARM32机器上table0，256初始值刚好等于阈值
#else
    ulInitialSize = 512;  // EULER机器上table0，512初始值刚好等于阈值
#endif
    // 建表
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(g_testDbId, testRelId, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    testRelDef.ulIntialSize = ulInitialSize + 1;
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表写入少量数据
    Insert(testRelId, 10, true);
    // 校验是否在共享空间中
    SelectView(testRelId, false, DB_TPC_SYSVIEW_GET_COMMOM_AREA);
    // 10s后校验是否在共享空间中
    sleep(10);
    Update(testRelId, 0, 2001, true);
    SelectView(testRelId, true, DB_TPC_SYSVIEW_GET_COMMOM_AREA);

    TestFreeTblStructDef(&testRelDef);
}
// 193.ulIntialSize*(变长头大小（24)+数据长度) = 7/8 页的情况，查询是否为共享空间
TEST_F(SharedSpace_Test, V1Com_039_193)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ulInitialSize = 0;
#if defined ENV_RTOSV2 && defined CPU_BIT_32
    ulInitialSize = 256;  // ARM32机器上table0，256初始值刚好等于阈值
#else
    ulInitialSize = 512;  // EULER机器上table0，512初始值刚好等于阈值
#endif
    // 建表
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(g_testDbId, testRelId, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    testRelDef.ulIntialSize = ulInitialSize;
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验是否在共享空间中
    SelectView(testRelId, true, DB_TPC_SYSVIEW_GET_COMMOM_AREA);
    TestFreeTblStructDef(&testRelDef);
}
// 194.ulIntialSize*(变长头大小（24)+数据长度)略小于 7/8 页的情况，查询是否为共享空间
TEST_F(SharedSpace_Test, V1Com_039_194)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ulInitialSize = 0;
#if defined ENV_RTOSV2 && defined CPU_BIT_32
    ulInitialSize = 256;  // ARM32机器上table0，256初始值刚好等于阈值
#else
    ulInitialSize = 512;  // EULER机器上table0，512初始值刚好等于阈值
#endif
    // 建表
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(g_testDbId, testRelId, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    testRelDef.ulIntialSize = ulInitialSize - 1;
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验是否在共享空间中
    SelectView(testRelId, true, DB_TPC_SYSVIEW_GET_COMMOM_AREA);
    TestFreeTblStructDef(&testRelDef);
}
// 195.ulIntialSize*(变长头大小（24)+数据长度)略大于 7/8 页的情况下clone表，查询是否为共享空间，10s后再次查询
TEST_F(SharedSpace_Test, V1Com_039_195)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ulInitialSize = 0;
#if defined ENV_RTOSV2 && defined CPU_BIT_32
    ulInitialSize = 256;  // ARM32机器上table0，256初始值刚好等于阈值
#else
    ulInitialSize = 512;  // EULER机器上table0，512初始值刚好等于阈值
#endif
    system("mkdir filePath");
    // 创建两个DB
    char testDbName[DB_NAME_LEN] = "testDb";
    char filePath[64] = "./filePath/perDbPath";
    ret = DB_CreateDB((VOS_UINT8 *)testDbName, (VOS_UINT8 *)filePath, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    uint32_t testDbId = 0;
    ret = DB_OpenDB((VOS_UINT8 *)filePath, (VOS_UINT8 *)testDbName, &testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 再创建一个DB
    char testDbName2[DB_NAME_LEN] = "testDb2";
    char filePath2[64] = "./filePath/perDbPath2";
    ret = DB_CreateDB((VOS_UINT8 *)testDbName2, (VOS_UINT8 *)filePath2, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    uint32_t testDbId2 = 0;
    ret = DB_OpenDB((VOS_UINT8 *)filePath2, (VOS_UINT8 *)testDbName2, &testDbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    uint16_t testRelId = 0;
    uint16_t testRelId2 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(testDbId, "schema_file/vertexlabel1.json", &testRelId, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropTbl(testDbId, testRelId, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    testRelDef.ulIntialSize = ulInitialSize + 1;
    ret = DB_CreateTbl(testDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表写入少量数据
    Insert(testRelId, 10, false, testDbId, TPC_GLOBAL_CDB);
    // clone表
    ret = DB_CloneTable(testDbId, testRelId, testDbId2, testRelId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 校验是否在共享空间中
    SelectView(testRelId2, false, DB_TPC_SYSVIEW_GET_COMMOM_AREA, testDbId2);
    // 10s后校验是否在共享空间中
    sleep(10);
    Update(testRelId2, 0, 2001, false, 1, testDbId2);
    SelectView(testRelId2, true, DB_TPC_SYSVIEW_GET_COMMOM_AREA, testDbId2);

    TestFreeTblStructDef(&testRelDef);
    // 关闭DB
    ret = DB_CloseDB(testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_CloseDB(testDbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName2, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
}
// 196.ulIntialSize*(变长头大小（24)+数据长度)略大于 7/8 页的情况下导入导出，等待10s后查询表内存并进行DML操作
TEST_F(SharedSpace_Test, V1Com_039_196)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ulInitialSize = 0;
#if defined ENV_RTOSV2 && defined CPU_BIT_32
    ulInitialSize = 256;  // ARM32机器上table0，256初始值刚好等于阈值
#else
    ulInitialSize = 512;  // EULER机器上table0，512初始值刚好等于阈值
#endif
    system("mkdir filePath");
    // 建表
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestTPC_CreateTbl(g_testDbId, "schema_file/vertexlabel1.json", &testRelId, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(g_testDbId, testRelId, TPC_NOWAIT);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    testRelDef.ulIntialSize = ulInitialSize + 1;
    ret = TPC_CreateTbl(g_testDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 表写入少量数据
    Insert(testRelId, 10, true);
    // 导出DB
    char exportFile[64] = "./filePath/export.db";
    ret = TPC_BkpPhy(g_testDbId, (uint8_t *)exportFile);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // replace进行导入DB
    DB_RESTORE_CONFIG_STRU stDbConfig = {.bPersistent = false};
    ret = TPC_Restore(
        (uint8_t *)exportFile, (VOS_UINT8 *)g_testDbName, NULL, DB_RESTORETYPE_REPLACE, &stDbConfig, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 10s后校验是否在共享空间中
    sleep(10);
    Update(testRelId, 0, 2001, true);
    Delete(testRelId, 2001, true);
    Insert(testRelId, 1, true);
    SelectView(testRelId, true, DB_TPC_SYSVIEW_GET_COMMOM_AREA);
    // 计算DB内存
    VOS_UINT32 TblSize = 0;
    ret = DBDBG_GetTblMemSize(g_testDbId, testRelId, &TblSize);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    if (TblSize > 32*1024) {
        V1_AW_MACRO_EXPECT_EQ_INT(32*1024, TblSize);
    }
    TestFreeTblStructDef(&testRelDef);
    system("rm -rf ./filePath");
}
