/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description:
 * Author: herui
 * Create: 2025-07-03
 */

#ifndef TESTOFDEBUGAPI_H
#define TESTOFDEBUGAPI_H
#include "t_rd_simplerel.h"

using namespace std;

int32_t g_compressCallbackTimes = 0;
uint32_t g_testDbId = 0;
DB_INST_CONFIG_STRU g_testDbCfg = {0};
DB_TABLE_TYPE_ENUM g_isPersist = DB_TABLE_NORMAL;
const char g_testDbName[DB_NAME_LEN] = "testDql";
#if defined ENV_RTOSV2 && defined CPU_BIT_32
int32_t g_DeleteThreshold = 42;  // ARM32机器上table0，42条数据刚好低于阈值
#else
int32_t g_DeleteThreshold = 85;  // EULER机器上table0，85条数据刚好低于阈值
#endif
#if defined ENV_RTOSV2 && defined CPU_BIT_32
int32_t g_Threshold = 299;  // ARM32机器上table0，299条数据刚好超过阈值
#else
int32_t g_Threshold = 598;       // EULER机器上table0，598条数据刚好超过阈值
#endif

typedef struct {
    uint16_t testRelId;
    DB_FIELD_DEF_STRU *astFlds;
    uint32_t Data = 0;
    uint32_t DbId = g_testDbId;
} DqlArgsT;

typedef struct tagUniqueIndexTblDefT {
    VOS_UINT32 f0;
    VOS_UINT32 f1;
    VOS_INT8 f2;
    VOS_INT8 f3;
    int16_t f4;
    VOS_UINT32 f5;
    int64_t f6;
    int64_t f7;
} UniqueIndexTblDefT;

typedef struct tagNoIndexTblDefT {
    VOS_UINT32 f0;
    VOS_UINT32 f1;
    VOS_INT8 f2;
    VOS_INT8 f3;
    int16_t f4;
    VOS_UINT32 f5;
    int64_t f6;
    int64_t f7;
    int64_t f8;
} NoIndexTblDefT;

typedef struct tagNonUniqueTblDefT {
    VOS_UINT32 f0;
    VOS_UINT32 f1;
    int16_t f2;
    VOS_INT8 f3;
    VOS_INT8 f4;
    float f5;
    int64_t f6;
} NonUniqueTblDefT;

// 数据类型
inline static uint16_t TestGetOneFieldLen(DB_FIELD_DEF_STRU *pstFld)
{
    const uint16_t size = pstFld->usSize;
    // v1数据类型对应字段大小
    const uint32_t fieldSizes[32] = {(uint32_t)((size + 1) / 2), sizeof(float), sizeof(double), 4, sizeof(double),
        (uint32_t)(size + 2), sizeof(DB_TIME_STRU), (uint32_t)(size + 1), size, sizeof(uint8_t), sizeof(uint16_t),
        sizeof(uint32_t), sizeof(int8_t), sizeof(int16_t), sizeof(int32_t), sizeof(DB_DATE_STRU), sizeof(uint32_t), 6,
        0, sizeof(int64_t), sizeof(uint64_t), sizeof(DB_IPV4PREFIX_STRUCT), sizeof(DB_IPV6ADDRESS_STRUCT),
        sizeof(DB_IPV6ADDRESS_PREFIX_STRU), sizeof(DB_DATETIME_STRU), sizeof(DB_TIMEZONE_STRU), (uint32_t)(size + 1),
        (uint32_t)(size + 2), 0, 0, 0, 0};
    return (uint16_t)fieldSizes[pstFld->enDataType];
}
// 设置普通表的所有字段值
void testSetAllField(void *tblFieldDataDef, VOS_UINT32 indexFieldVal, uint32_t tblRecLen)
{
    if (tblRecLen == 32) {
        UniqueIndexTblDefT *tblFieldData = (UniqueIndexTblDefT *)tblFieldDataDef;
        tblFieldData->f0 = indexFieldVal;
        tblFieldData->f1 = indexFieldVal;
        tblFieldData->f2 = (int8_t)(indexFieldVal);
        tblFieldData->f3 = (int8_t)(indexFieldVal);
        tblFieldData->f4 = (int16_t)(indexFieldVal);
        tblFieldData->f5 = indexFieldVal;
        tblFieldData->f6 = (int64_t)(indexFieldVal);
        tblFieldData->f7 = (int64_t)(indexFieldVal);
    }
    if (tblRecLen == 40) {
        NoIndexTblDefT *tblFieldData = (NoIndexTblDefT *)tblFieldDataDef;
        tblFieldData->f0 = indexFieldVal;
        tblFieldData->f1 = indexFieldVal;
        tblFieldData->f2 = (int8_t)(indexFieldVal);
        tblFieldData->f3 = (int8_t)(indexFieldVal);
        tblFieldData->f4 = (int16_t)(indexFieldVal);
        tblFieldData->f5 = indexFieldVal;
        tblFieldData->f6 = (int64_t)(indexFieldVal);
        tblFieldData->f7 = (int64_t)(indexFieldVal);
        tblFieldData->f8 = (int64_t)(indexFieldVal);
    }
    if (tblRecLen == 24) {
        NonUniqueTblDefT *tblFieldData = (NonUniqueTblDefT *)tblFieldDataDef;
        tblFieldData->f0 = indexFieldVal;
        tblFieldData->f1 = indexFieldVal;
        tblFieldData->f2 = (int16_t)(indexFieldVal);
        tblFieldData->f3 = (int8_t)(indexFieldVal);
        tblFieldData->f4 = (int8_t)(indexFieldVal);
        tblFieldData->f5 = (float)(indexFieldVal);
        tblFieldData->f6 = (int64_t)(indexFieldVal);
    }
}
void Delete(VOS_UINT16 usRelId, uint32_t data, bool isTPC, uint32_t indexField = 1, uint32_t DbId = g_testDbId,
    VOS_UINT32 cbdId = TPC_GLOBAL_CDB)
{
    int ret = 0;
    DB_COND_STRU stCond;
    stCond.usCondNum = 1;
    stCond.aCond[0].enOp = DB_OP_EQUAL;
    stCond.aCond[0].ucFieldId = indexField;
    *(uint32_t *)stCond.aCond[0].aucValue = data;

    uint32_t delRecNum = 0;
    if (isTPC) {
        ret = TPC_DeleteRec(cbdId, DbId, usRelId, &stCond, &delRecNum);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        V1_AW_MACRO_EXPECT_EQ_INT(1, delRecNum);
    } else {
        ret = DB_DeleteRec(DbId, usRelId, &stCond, &delRecNum);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        V1_AW_MACRO_EXPECT_EQ_INT(1, delRecNum);
    }
}
void Insert(VOS_UINT16 usRelId, uint32_t dataNum, bool isTPC, uint32_t DbId = g_testDbId,
    VOS_UINT32 cbdId = TPC_GLOBAL_CDB, VOS_UINT32 startNum = 0)
{
    int ret = 0;
    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = DBS_GetRelRecLen(DbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 插入数据
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = tblRecLen;
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    UniqueIndexTblDefT tblFieldDataDefSet1;
    NoIndexTblDefT tblFieldDataDefSet2;
    NonUniqueTblDefT tblFieldDataDefSet3;
    for (VOS_UINT32 i = startNum; i < startNum + dataNum; i++) {
        if (tblRecLen == 32) {
            testSetAllField(&tblFieldDataDefSet1, i, tblRecLen);
            memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &tblFieldDataDefSet1, pstDsBufSet.usRecLen);
        }
        if (tblRecLen == 40) {
            testSetAllField(&tblFieldDataDefSet2, i, tblRecLen);
            memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &tblFieldDataDefSet2, pstDsBufSet.usRecLen);
        }
        if (tblRecLen == 24) {
            testSetAllField(&tblFieldDataDefSet3, i, tblRecLen);
            memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &tblFieldDataDefSet3, pstDsBufSet.usRecLen);
        }
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        if (isTPC) {
            ret = TPC_InsertRec(cbdId, DbId, usRelId, &pstDsBufSet);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        } else {
            ret = DB_InsertRec(DbId, usRelId, &pstDsBufSet);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }
    TEST_V1_FREE(pucDataSet);
}
/*****用于更新数据(usRelId:表id，oldvalue:需要更新的数据，newvalue:更新后的数据值，
astFlds:初始表的字段定义，indexField:索引字段id，cbdId:事务id，DbId:数据库id)****/
void Update(VOS_UINT16 usRelId, uint32_t oldvalue, uint32_t newvalue, bool isTPC, uint32_t indexField = 1,
    uint32_t DbId = g_testDbId, VOS_UINT32 cbdId = TPC_GLOBAL_CDB)
{
    int ret = 0;
    DB_COND_STRU pstCond1;
    pstCond1.usCondNum = 1;
    pstCond1.aCond[0].ucFieldId = indexField;
    pstCond1.aCond[0].enOp = DB_OP_EQUAL;
    *(uint32_t *)pstCond1.aCond[0].aucValue = oldvalue;

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = DBS_GetRelRecLen(DbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = tblRecLen;
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    UniqueIndexTblDefT tblFieldDataDefSet1;
    NoIndexTblDefT tblFieldDataDefSet2;
    NonUniqueTblDefT tblFieldDataDefSet3;
    if (tblRecLen == 32) {
        testSetAllField(&tblFieldDataDefSet1, newvalue, tblRecLen);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &tblFieldDataDefSet1, pstDsBufSet.usRecLen);
    }
    if (tblRecLen == 40) {
        testSetAllField(&tblFieldDataDefSet2, newvalue, tblRecLen);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &tblFieldDataDefSet2, pstDsBufSet.usRecLen);
    }
    if (tblRecLen == 24) {
        testSetAllField(&tblFieldDataDefSet3, newvalue, tblRecLen);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &tblFieldDataDefSet3, pstDsBufSet.usRecLen);
    }
    pstDsBufSet.StdBuf.pucData = pucDataSet;
    VOS_UINT32 udpRecNum = 0;
    if (isTPC) {
        ret = TPC_UpdateRec(cbdId, DbId, usRelId, &pstCond1, &pstFldFilter, &pstDsBufSet, &udpRecNum);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        V1_AW_MACRO_EXPECT_EQ_INT(1, udpRecNum);
    } else {
        ret = DB_UpdateRec(DbId, usRelId, &pstCond1, &pstFldFilter, &pstDsBufSet, &udpRecNum);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        V1_AW_MACRO_EXPECT_EQ_INT(1, udpRecNum);
    }
    TEST_V1_FREE(pucDataSet);
}
void testAllData(VOS_UINT16 usRelId, VOS_UINT32 recordsNum, DB_REL_DEF_STRU *relDef, uint32_t *expectvalue,
    VOS_UINT8 *pstRecord, uint32_t DbId)
{
    VOS_UINT32 ulLoop;
    int ret = 0;
    uint8_t *pstRecord1;
    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(DbId, usRelId, &tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT8 *pstRecord2 = (VOS_UINT8 *)TEST_V1_MALLOC(tblRecLen * recordsNum);
    for (ulLoop = 0; ulLoop < recordsNum; ulLoop++) {
        pstRecord1 = pstRecord + tblRecLen * ulLoop;
        // 获取用户设置的数据
        testSetAllField(pstRecord2, expectvalue[ulLoop], tblRecLen);
        uint32_t index = 0;

        ret = memcmp(pstRecord1, pstRecord2, TestGetOneFieldLen(&relDef->pstFldLst[0]));
        if (ret != DB_SUCCESS_V1) {
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            break;
        }
        for (int i = 1; i < relDef->ulNCols; i++) {
            index = index + TestGetOneFieldLen(&relDef->pstFldLst[i - 1]);
            ret = memcmp(pstRecord1 + index, pstRecord2 + index, TestGetOneFieldLen(&relDef->pstFldLst[i]));
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }
    TEST_V1_FREE(pstRecord2);
}
// 校验预期的数据
void ValidateExpectData(VOS_UINT16 usRelId, VOS_UINT32 recordsNum, DB_REL_DEF_STRU *relDef, uint32_t *expectvalue,
    bool isTPC, uint32_t DbId = g_testDbId)
{
    VOS_UINT32 ulLoop;
    int ret = 0;
    uint8_t *pstRecord1;
    // 获取记录的长度
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(DbId, usRelId, &tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询数据
    unsigned char sortFields[1] = {1};
    DB_SORT_STRU pstSort;
    pstSort.ucSortNum = 1;
    pstSort.enSortType = DB_SORTTYPE_ASCEND;
    pstSort.pSortFields = sortFields;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 0;
    DB_DSBUF_STRU pstDsBufGet;
    pstDsBufGet.usRecLen = tblRecLen;
    pstDsBufGet.usRecNum = recordsNum;
    pstDsBufGet.StdBuf.ulBufLen = pstDsBufGet.usRecLen * recordsNum;
    VOS_UINT8 *pucDataGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufGet.StdBuf.ulBufLen);
    (void)memset_s(pucDataGet, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
    pstDsBufGet.StdBuf.pucData = pucDataGet;
    if (isTPC) {
        ret = TPC_SelectAllRecByOrder(TPC_GLOBAL_CDB, DbId, usRelId, &pstSort, &pstCond, &pstFldFilter, &pstDsBufGet);
        if (ret == VOS_ERRNO_DB_MEMALLOCFAILURE) {
            recordsNum = 0;
        } else {
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    } else {
        ret = DB_SelectAllRecByOrder(DbId, usRelId, &pstSort, &pstCond, &pstFldFilter, &pstDsBufGet);
        if (ret == VOS_ERRNO_DB_MEMALLOCFAILURE) {
            recordsNum = 0;
        } else {
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }
    // 校验数据
    testAllData(usRelId, recordsNum, relDef, expectvalue, pucDataGet, DbId);
    TEST_V1_FREE(pucDataGet);
}
// 查询某一条数据
void selectDataByAllWays(VOS_UINT16 usRelId, uint32_t indexField, uint32_t opVal, bool isTPC,
    VOS_UINT32 cbdId = TPC_GLOBAL_CDB, int expectresult = DB_SUCCESS_V1)
{
    int ret = 0;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = indexField;
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_INT32 *)pstCond.aCond[0].aucValue = opVal;

    unsigned char sortFields[1] = {1};
    DB_SORT_STRU pstSort;
    pstSort.ucSortNum = 1;
    pstSort.enSortType = 1;
    pstSort.pSortFields = sortFields;

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_DSBUF_STRU DsBufGet;
    DsBufGet.usRecLen = DBDDL_GetRelRecLen(g_testDbId, usRelId);
    DsBufGet.usRecNum = 1;
    DsBufGet.StdBuf.ulBufLen = DsBufGet.usRecLen * (DsBufGet.usRecNum);
    VOS_UINT8 *pucDataGet = (VOS_UINT8 *)TEST_V1_MALLOC(DsBufGet.StdBuf.ulBufLen);
    (void)memset_s(pucDataGet, DsBufGet.StdBuf.ulBufLen, 0x00, DsBufGet.StdBuf.ulBufLen);
    DsBufGet.StdBuf.pucData = pucDataGet;

    if (isTPC) {
        ret = TPC_SelectAllRecByOrder(cbdId, g_testDbId, usRelId, &pstSort, &pstCond, &pstFldFilter, &DsBufGet);
        V1_AW_MACRO_EXPECT_EQ_INT(expectresult, ret);
        if (ret == DB_SUCCESS_V1) {
            V1_AW_MACRO_EXPECT_EQ_INT(1, DsBufGet.usRecNum);
        }
    } else {
        ret = DB_SelectAllRecByOrder(g_testDbId, usRelId, &pstSort, &pstCond, &pstFldFilter, &DsBufGet);
        V1_AW_MACRO_EXPECT_EQ_INT(expectresult, ret);
        if (ret == DB_SUCCESS_V1) {
            V1_AW_MACRO_EXPECT_EQ_INT(1, DsBufGet.usRecNum);
        }
    }
    TEST_V1_FREE(pucDataGet);
}
// 比较函数，用于qsort
int Compare(const void *a, const void *b)
{
    return (*(VOS_UINT16 *)a - *(VOS_UINT16 *)b);
}
// 比较两个长度一致的数组内容是否一致
void isSameArray(VOS_UINT16 *array1, VOS_UINT16 *array2, uint32_t len)
{
    // 先对两个数组进行排序
    qsort(array1, len, sizeof(VOS_UINT16), Compare);
    qsort(array2, len, sizeof(VOS_UINT16), Compare);
    for (int i = 0; i < len; i++) {
        V1_AW_MACRO_EXPECT_EQ_INT(array1[i], array2[i]);
    }
}
// 查看视图
void SelectView(VOS_UINT16 usRelId, bool isSharedSpace,
    DB_TPC_SYSVIEW_TYPE_ENUM enViewType = DB_TPC_SYSVIEW_GET_TTREE_INFO, uint32_t DbId = g_testDbId)
{
    DB_TPC_SYSVIEW_ARGS_STRU sysviewArgs = {0};
    sysviewArgs.ulDbId = DbId;
    sysviewArgs.usRelNo = usRelId;
    VOS_UINT8 *result = NULL;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TPC_Sysview(enViewType, &sysviewArgs, &result));
    if (enViewType == DB_TPC_SYSVIEW_GET_TTREE_INFO) {
        cout << "DB_TPC_SYSVIEW_GET_TTREE_INFO :" << endl << result << endl;
    } else if (enViewType == DB_TPC_SYSVIEW_GET_COMMOM_AREA) {
        const char *keyword = "table id: ";
        int tableid = 0;
        // 1. 找到 "table id: " 的位置
        char *strPos = strstr((char *)result, keyword);
        if (strPos != NULL && isSharedSpace == false) {
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, -1);
        }
        if (strPos != NULL && isSharedSpace == true) {
            // 2. 移动指针到数字部分
            strPos += strlen(keyword);
            // 3. 提取数字
            if (sscanf(strPos, "%d", &tableid) == 1) {
                V1_AW_MACRO_EXPECT_EQ_INT(usRelId, tableid);
            }
        }
        if (strPos == NULL && isSharedSpace == true) {
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, -1);
        }
        cout << "DB_TPC_SYSVIEW_GET_COMMOM_AREA :" << endl << result << endl;
    }
}
// 建9张不同类型表
void CreateTestTb(bool isTPC, VOS_UINT16 *usRelId, DB_REL_DEF_STRU *testRelDef, uint32_t DbId = g_testDbId)
{
    int ret = 0;
    for (int i = 0; i < 9; i++) {
        char tblName[30] = {0};
        (void)sprintf(tblName, "table_%d", i);
        if (isTPC) {
            if (i < 3) {
                ret = TestTPC_CreateTbl(DbId, "schema_file/vertexlabel1.json", &usRelId[i], &testRelDef[0], tblName);
                V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            }
            if (i >= 3 && i < 6) {
                ret = TestTPC_CreateTbl(DbId, "schema_file/vertexlabel2.json", &usRelId[i], &testRelDef[1], tblName);
                V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            }
            if (i >= 6) {
                ret = TestTPC_CreateTbl(DbId, "schema_file/vertexlabel3.json", &usRelId[i], &testRelDef[2], tblName);
                V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            }
        } else {
            if (i < 3) {
                ret = TestDB_CreateTbl(DbId, "schema_file/vertexlabel1.json", &usRelId[i], &testRelDef[0], tblName);
                V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            }
            if (i >= 3 && i < 6) {
                ret = TestDB_CreateTbl(DbId, "schema_file/vertexlabel2.json", &usRelId[i], &testRelDef[1], tblName);
                V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            }
            if (i >= 6) {
                ret = TestDB_CreateTbl(DbId, "schema_file/vertexlabel3.json", &usRelId[i], &testRelDef[2], tblName);
                V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            }
        }
        if (i < 2) {
            TestFreeTblStructDef(&testRelDef[0]);
        }
        if (i >= 3 && i < 5) {
            TestFreeTblStructDef(&testRelDef[1]);
        }
        if (i >= 6 && i < 8) {
            TestFreeTblStructDef(&testRelDef[2]);
        }
    }
}
// 查询diff并校验
void Validatediff(VOS_UINT32 cdbId, VOS_UINT16 usRelId, VOS_UINT32 Data, uint32_t optWays, DB_FIELD_DEF_STRU *astFlds,
    VOS_UINT32 oldData = 0)
{
    int ret = 0;
    uint32_t pulInsrecNum;
    void *pInsRecList = NULL;
    uint32_t pulDelrecNum;
    void *pDelRecList = NULL;
    uint32_t pulUpdrecNum;
    void *pUpdRecList = NULL;
    VOS_UINT32 tblRecLen;
    ret = DBS_GetRelRecLen(g_testDbId, usRelId, &tblRecLen);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(tblRecLen);
    testSetAllField(pucDataSet, Data, tblRecLen);

    // 查询diff并校验
    ret = TPC_SelectCdbDiffData(
        cdbId, usRelId, &pulInsrecNum, &pInsRecList, &pulDelrecNum, &pDelRecList, &pulUpdrecNum, &pUpdRecList);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    if (optWays == 1) {
        V1_AW_MACRO_EXPECT_EQ_INT(1, pulInsrecNum);
        if (pulInsrecNum == 1) {
            ret = memcmp(pucDataSet, pInsRecList, tblRecLen);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }
    if (optWays == 2) {
        V1_AW_MACRO_EXPECT_EQ_INT(1, pulDelrecNum);
        if (pulDelrecNum == 1) {
            ret = memcmp(pucDataSet, pDelRecList, tblRecLen);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }
    if (optWays == 3) {
        V1_AW_MACRO_EXPECT_EQ_INT(2, pulUpdrecNum);
        if (pulUpdrecNum == 2) {
            ret = memcmp(pucDataSet, pUpdRecList, tblRecLen);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            // 旧数据验证
            testSetAllField(pucDataSet, oldData, tblRecLen);
            ret = memcmp(pucDataSet, pUpdRecList + tblRecLen, tblRecLen);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    TPC_FreeDiffData(pInsRecList, pDelRecList, pUpdRecList);
    TEST_V1_FREE(pucDataSet);
}
DB_ERR_CODE testScanAllData(uint32_t dbId, VOS_UINT16 pusRelId, VOS_UINT32 recordCnt, VOS_UINT32 opways = 1)
{
    DB_ERR_CODE ret = 0;
    VOS_UINT32 tblRecLen;
    ret = TestGetTblRecLen(dbId, pusRelId, &tblRecLen);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    if (true) {
        // 查询数据
        DB_COND_STRU pstCond;
        pstCond.usCondNum = 0;
        DB_FIELDFILTER_STRU pstFldFilter;
        pstFldFilter.ucFieldNum = DB_FIELD_ALL;
        DB_DSBUF_STRU pstDsBufGet;
        pstDsBufGet.usRecLen = tblRecLen;
        pstDsBufGet.usRecNum = recordCnt;
        pstDsBufGet.StdBuf.ulBufLen = pstDsBufGet.usRecLen * pstDsBufGet.usRecNum;
        VOS_UINT8 *pucDataGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufGet.StdBuf.ulBufLen);
        if (pucDataGet == NULL) {
            return -1;
        }
        (void)memset_s(pucDataGet, pstDsBufGet.StdBuf.ulBufLen, 0x00, pstDsBufGet.StdBuf.ulBufLen);
        pstDsBufGet.StdBuf.pucData = pucDataGet;
        ret = TPC_SelectAllRec(TPC_GLOBAL_CDB, dbId, pusRelId, &pstCond, &pstFldFilter, &pstDsBufGet);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        V1_AW_MACRO_EXPECT_EQ_INT(recordCnt, pstDsBufGet.usRecNum);
        V1_AW_MACRO_EXPECT_EQ_INT(pstDsBufGet.StdBuf.ulBufLen, pstDsBufGet.StdBuf.ulActLen);
        if (opways == 1) {
            // 获取所有记录的数据
            VOS_UINT32 bufLen = sizeof(NoIndexTblDefT) * pstDsBufGet.usRecNum;
            NoIndexTblDefT *tblFieldDataDefGet = (NoIndexTblDefT *)TEST_V1_MALLOC(bufLen);
            if (pucDataGet == NULL) {
                TEST_V1_FREE(pucDataGet);
                return -1;
            }
            for (VOS_UINT32 i = 0; i < pstDsBufGet.usRecNum; i++) {
                (void)memcpy_s(&tblFieldDataDefGet[i], pstDsBufGet.usRecLen, pucDataGet + (pstDsBufGet.usRecLen * i),
                    pstDsBufGet.usRecLen);
            }
            printf("=============== All Data ===============\n");
            printf("[ NO.    ]\n");
            for (VOS_UINT32 i = 0; i < pstDsBufGet.usRecNum; i++) {
                printf("[ %06u ] %-6u %-6u %-6d %-6u    %-6u    %-6u %-6d %-6u %-6u\n", i, tblFieldDataDefGet[i].f0,
                    tblFieldDataDefGet[i].f1, tblFieldDataDefGet[i].f2, tblFieldDataDefGet[i].f3,
                    tblFieldDataDefGet[i].f4, tblFieldDataDefGet[i].f5, tblFieldDataDefGet[i].f6,
                    tblFieldDataDefGet[i].f7, tblFieldDataDefGet[i].f8);
            }
            TEST_V1_FREE(tblFieldDataDefGet);
        }
        if (opways == 2) {
            VOS_UINT32 bufLen = sizeof(NonUniqueTblDefT) * pstDsBufGet.usRecNum;
            NonUniqueTblDefT *tblFieldDataDefGet = (NonUniqueTblDefT *)TEST_V1_MALLOC(bufLen);
            if (pucDataGet == NULL) {
                TEST_V1_FREE(pucDataGet);
                return -1;
            }
            for (VOS_UINT32 i = 0; i < pstDsBufGet.usRecNum; i++) {
                (void)memcpy_s(&tblFieldDataDefGet[i], pstDsBufGet.usRecLen, pucDataGet + (pstDsBufGet.usRecLen * i),
                    pstDsBufGet.usRecLen);
            }
            printf("=============== All Data ===============\n");
            printf("[ NO.    ]\n");
            for (VOS_UINT32 i = 0; i < pstDsBufGet.usRecNum; i++) {
                printf("[ %d ] %d %-6u %d %d    %-6u\n", i, tblFieldDataDefGet[i].f0, tblFieldDataDefGet[i].f1,
                    tblFieldDataDefGet[i].f2, tblFieldDataDefGet[i].f3, tblFieldDataDefGet[i].f4);
            }
            TEST_V1_FREE(tblFieldDataDefGet);
        }

        TEST_V1_FREE(pucDataGet);
    }
    return ret;
}
DB_FldConvHook *GetTblConvHook(VOS_UINT16 usRelId)
{
    return NULL;
}
// 选择调用七个注册接口
void SelectcfgInterface(DB_REG_FEATURE_STRU *pstFeature, uint32_t *selectways)
{
    int ret = 0;
    if (selectways[0] == 1) {
        ret = DB_RegCheckSumFeature(true, NULL, NULL, NULL, pstFeature);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    if (selectways[1] == 1) {
        DB_DATA_STORAGE_ENUM enStorage = DB_DATA_STORAGE_RAM;
        ret = DB_RegDbDataStorage(enStorage, pstFeature);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    if (selectways[2] == 1) {
        ret = DB_RegDbRestoreConfig(false, pstFeature);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    if (selectways[3] == 1) {
        DB_RESTORETYPE_ENUM enRestoreType = DB_RESTORETYPE_DISCARD;
        ret = DB_RegDbRestoreTypeCfg(enRestoreType, pstFeature);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    if (selectways[4] == 1) {
        DB_GetTblConvHook pfnGetTblConvHook = GetTblConvHook;
        ret = DB_RegTableConvFunc(pfnGetTblConvHook, pstFeature);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    if (selectways[5] == 1) {
        DB_INST_CONFIG_STRU enConfigStructure = {0};
        enConfigStructure.enPersistent = DB_CKP_NONE;
        enConfigStructure.ulMaxDBDescInfoSize = 20;
        enConfigStructure.ulInitialSize = 100;
        enConfigStructure.ulExtendSize = 100;
        enConfigStructure.ulRedoBufSize = 100;
        enConfigStructure.ulTempSize = 100;
        ret = DB_RegDbConfigStructure(&enConfigStructure, pstFeature);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    if (selectways[6] == 1) {
        ret = DB_RegFlexExtendFeature(10, 10, pstFeature);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
}
void CompressTblNotifyFunc(DB_COMPRESS_INFO_STRU *pstDataBlkInfo)
{
    g_compressCallbackTimes++;
}
// 表压缩接口
uint32_t Test_CompressTable(uint32_t dbId, VOS_UINT16 relId, uint32_t compRecNum, bool isTPC, int32_t *expCompRecNum,
    DB_COMPRESSTBL_NOTIFY_FUNC notifyFunc = CompressTblNotifyFunc)
{
    int ret = 0;
    if (isTPC) {
        ret = TPC_CompressTable(dbId, relId, compRecNum, notifyFunc);
    } else {
        ret = DB_CompressTable(dbId, relId, compRecNum, notifyFunc);
    }
    if (ret != DB_SUCCESS_V1) {
        return ret;
    }
    if (expCompRecNum >= 0) {
        AW_FUN_Log(
            LOG_INFO, "expCompRecNum : %d, g_compressCallbackTimes : %d", *expCompRecNum, g_compressCallbackTimes);
        *expCompRecNum = g_compressCallbackTimes;
    }
    return DB_SUCCESS_V1;
}
#endif
