/*
1.1 操作1张表
001 在临界值循环插入删除数据并校验
002 共享空间中插入索引冲突数据
003 共享空间中插入一条数据
004 共享空间中插入一条数据超出阈值后进行增删改查操作
005 共享空间中更新索引冲突数据
006 共享空间中更新一条数据
007 共享空间中一次性删除表中所有数据
008 共享空间中删除一条数据
009 独立空间中删除一条数据低于阈值后进行增删改查操作
010 共享空间中对同一条数据进行多次增删改查
011 共享空间中对不同的数据进行多次增删改查

012 pagesize为32K时，插入一条大小为pageSize / 8 -1的数据
013 修改pagesize为16K时，插入一条大小为pageSize / 8 -1的数据
014 修改pagesize为16K时，在临界值循环插入删除数据并校验
015 共享空间内多线程操作同一张表，一个线程插入大量数据足以使表迁出，一个线程删除大量数据足以使表迁入

1.2 编辑多张表
016 共享空间中对不同的表进行多次增删改查
017 多线程增删改查操作不同表
018 多线程增删改查操作不同表，其中一个表插入数据至超出阈值
019 多线程增删改查操作不同表，其中一个表删除数据至低于阈值
020 多线程增删改查不同表，使所有表频繁迁入迁出

2.1 单cdb
021 在CDB中插入数据至超过阈值后提交
022 在CDB插入数据至超过阈值后回滚
023 在CDB中插入数据至超过阈值后进行增删改查再提交
024 在CDB中删除数据至低于阈值后提交
025 在CDB中删除数据至低于阈值后回滚
026 在CDB中更新数据后校验
027 共享空间中CDB操作异常回滚后再次开启CDB进行增删改查操作
028 共享空间中多次开启CDB对同一条数据进行增删改查后校验数据
029 共享空间中多次开启CDB对不同数据进行增删改查后校验数据

1.3
030 无索引且无法建立索引的表在共享空间中进行增删改查
031 独立空间中一次性删除表中所有数据
032 共享空间中修改表名表id后进行增删改查
*/

#include "ShmSpOperation.h"

class ShmSpDML : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void ShmSpDML::SetUpTestCase()
{
    system("mkdir perFilePath;echo 'This is persistent DB' > ./perFilePath/perDbPath");
    system("mkdir filePath;echo 'This is normal DB' > ./filePath/normalDbPath");
}

void ShmSpDML::TearDownTestCase()
{
    system("rm -rf ./filePath/ perFilePath/ check.txt");
}

void ShmSpDML::SetUp()
{
    system("sh $TEST_HOME/tools/modifyCfg.sh \"pageSize=32\"");
    // 初始化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_Init());
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    AW_CHECK_LOG_BEGIN();
    int ret;
    // 创建自定义数据类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CreateDB((VOS_UINT8 *)g_dbName, NULL, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_dbName, &g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CreateDB((VOS_UINT8 *)g_dbName2, NULL, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    g_perDbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, &g_perDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)g_perDbName, &g_perDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}

void ShmSpDML::TearDown()
{
    // CLoseDB
    int ret = TPC_CloseDB(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_CloseDB(g_perDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)g_dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_perDbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_UnInit());
    AW_CHECK_LOG_END();
}

// 001 在临界值循环插入删除数据并校验
TEST_F(ShmSpDML, V1Com_039_ShmSpDML_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;                    // 32条数据，1/16个页的数据,2K
    int addNum = REC_NUM_PER_PAGE - (REC_NUM_PER_PAGE / pageNum);  // 480条数据，15/16个页的数据,30K

    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {  // 表1、表2写入数据 0-479；表3写入数据 0-31
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    // TPC_Sysview查询
    DB_TPC_SYSVIEW_ARGS_STRU viewArgs = {0};
    viewArgs.ulDbId = g_dbId;
    viewArgs.usRelNo = 0xFFFF;
    VOS_UINT8 *pucResult = NULL;

    // TPC_Sysview查询和表明检验，预期表在共享空间
    ret = TestSysviewAndCheckTbl(g_dbId, 0xFFFF,
        "index:0, table id: 0, table name: tableA, record num: 32, record len: 48, state: 0",
        "index:1, table id: 1, table name: tableB, record num: 32, record len: 48, state: 0",
        "index:2, table id: 2, table name: tableC, record num: 32, record len: 48, state: 0");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 循环写入删除后查询，需要等，减少循环次数
    for (int j = 0; j < 2; j++) {
        // 对表1进行写入操作，写入数据大于7/8个页
        for (int i = insertNum; i < addNum; i++) {
            ret = InsertTbl64B(g_dbId, relId[0], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[0], addNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < addNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[0], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }

        ret = TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &viewArgs, &pucResult);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 将结果字符串写入文件
        InfoInFile((char *)pucResult);
        // 共享空间可以查询到表名和表id等信息，校验表名
        char command[1024];
        (void)sprintf_s(command, sizeof(command), "cat check.txt");
        ret = executeCommand(
            command, "index:0, table id: 0, table name: tableA, record num: 32, record len: 48, state: 0");
        EXPECT_NE(DB_SUCCESS_V1, ret);
        TPC_FreeSysviewResult(&pucResult);

        // 删除数据至低于1/8个页
        for (int i = insertNum; i < addNum; i++) {  // 表1 删除 32-479，小于1/8个页
            ret = DeleteTbl64B(g_dbId, relId[0], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[0], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        sleep(10);
        for (int i = insertNum; i < insertNum + 1; i++) {
            ret = InsertTbl64B(g_dbId, relId[0], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }

        // TPC_Sysview查询和表明检验，预期表在共享空间
        ret = TestSysviewAndCheckTbl(g_dbId, 0xFFFF,
            "index:0, table id: 0, table name: tableA, record num: 33, record len: 48, state: 0",
            "index:1, table id: 1, table name: tableB, record num: 32, record len: 48, state: 0",
            "index:2, table id: 2, table name: tableC, record num: 32, record len: 48, state: 0");
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = insertNum; i < insertNum + 1; i++) {
            ret = DeleteTbl64B(g_dbId, relId[0], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002 共享空间中插入索引冲突数据
TEST_F(ShmSpDML, V1Com_039_ShmSpDML_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;                    // 32条数据，1/16个页的数据
    int addNum = REC_NUM_PER_PAGE - (REC_NUM_PER_PAGE / pageNum);  // 480条数据，15/16个页的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {  // 表1、表2写入数据 0-479；表3写入数据 0-31
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }
    // 对表1写入1条数据，主键冲突
    for (int i = 0; i < 1; i++) {
        ret = InsertTbl64B(g_dbId, relId[0], i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_KEYDUPLICATE, ret);
    }

    // 更新
    for (int i = 0; i < insertNum; i++) {
        ret = UpdateTbl64B(g_dbId, relId[0], i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId[0], insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除
    for (int i = 0; i < insertNum; i++) {
        ret = DeleteTbl64B(g_dbId, relId[0], i + 1000, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 写入
    for (int k = 0; k < insertNum; k++) {
        ret = InsertTbl64B(g_dbId, relId[0], k, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    ret = TestTPC_GetRelActRec(g_dbId, relId[0], insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < insertNum; i++) {
        ret = SelectTbl64B(g_dbId, relId[0], i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003 共享空间中插入一条数据
TEST_F(ShmSpDML, V1Com_039_ShmSpDML_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;  // 32条数据，1/16个页的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }
    // 对表1写入1条数据
    for (int i = insertNum; i < insertNum + 1; i++) {
        ret = InsertTbl64B(g_dbId, relId[0], i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    ret = TestTPC_GetRelActRec(g_dbId, relId[0], insertNum + 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < insertNum + 1; i++) {
        ret = SelectTbl64B(g_dbId, relId[0], i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004 共享空间中插入一条数据超出阈值后进行增删改查操作
TEST_F(ShmSpDML, V1Com_039_ShmSpDML_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 8;
    int deleteNum = REC_NUM_PER_PAGE / pageNum;                       // 64条数据，1/8个页的数据
    int insertNum = REC_NUM_PER_PAGE - (REC_NUM_PER_PAGE / pageNum);  // 448条数据，约7/8个页的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {  // 表1、表2写入数据 0-479；表3写入数据 0-31
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }
    // 对表1写入1条数据
    for (int i = insertNum; i < insertNum + 1; i++) {
        ret = InsertTbl64B(g_dbId, relId[0], i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 更新
    for (int i = 0; i < insertNum + 1; i++) {
        ret = UpdateTbl64B(g_dbId, relId[0], i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId[0], insertNum + 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除
    for (int i = 0; i < insertNum + 1; i++) {
        ret = DeleteTbl64B(g_dbId, relId[0], i + 1000, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 写入
    for (int k = 0; k < insertNum + 1; k++) {
        ret = InsertTbl64B(g_dbId, relId[0], k, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    ret = TestTPC_GetRelActRec(g_dbId, relId[0], insertNum + 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < insertNum + 1; i++) {
        ret = SelectTbl64B(g_dbId, relId[0], i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    VOS_UINT32 ulTblSize;
    ret = DBDBG_GetTblMemSize(g_dbId, relId[0], &ulTblSize);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_GE_INT(ulTblSize, 32768);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005 共享空间中更新索引冲突数据
TEST_F(ShmSpDML, V1Com_039_ShmSpDML_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 8;
    int insertNum = REC_NUM_PER_PAGE / pageNum;  // 64条数据，1/8个页的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {  // 表1、表2写入数据 0-479；表3写入数据 0-31
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }
    // 对表1更新1条数据，主键冲突
    for (int i = 0; i < 1; i++) {
        ret = UpdateTbl64B(g_dbId, relId[0], i, false, true);
        V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_CHGKEY, ret);
    }

    // 更新
    for (int i = 0; i < insertNum; i++) {
        ret = UpdateTbl64B(g_dbId, relId[0], i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId[0], insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除
    for (int i = 0; i < insertNum; i++) {
        ret = DeleteTbl64B(g_dbId, relId[0], i + 1000, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 写入
    for (int k = 0; k < insertNum; k++) {
        ret = InsertTbl64B(g_dbId, relId[0], k, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    ret = TestTPC_GetRelActRec(g_dbId, relId[0], insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < insertNum; i++) {
        ret = SelectTbl64B(g_dbId, relId[0], i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    VOS_UINT32 ulTblSize;
    ret = DBDBG_GetTblMemSize(g_dbId, relId[0], &ulTblSize);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006 共享空间中更新一条数据
TEST_F(ShmSpDML, V1Com_039_ShmSpDML_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 8;
    int insertNum = REC_NUM_PER_PAGE / pageNum;  // 64条数据，1/8个页的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }
    // 对表1更新1条数据，主键冲突
    for (int i = 0; i < 1; i++) {
        ret = UpdateTbl64B(g_dbId, relId[0], i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId[0], insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    int k = 1000;
    for (int i = 0; i < insertNum; i++) {
        if (i == 0) {
            ret = SelectTbl64B(g_dbId, relId[0], k, false);
        } else {
            ret = SelectTbl64B(g_dbId, relId[0], i, false);
        }
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    VOS_UINT32 ulTblSize;
    ret = DBDBG_GetTblMemSize(g_dbId, relId[0], &ulTblSize);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007 共享空间中一次性删除表中所有数据
TEST_F(ShmSpDML, V1Com_039_ShmSpDML_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 8;
    int insertNum = REC_NUM_PER_PAGE / pageNum;  // 64条数据，1/8个页的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {  // 表1、表2写入数据 0-479；表3写入数据 0-31
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }
    // 删除表1所有数据
    ret = DBS_DeleteRelAllRecords(g_dbId, relId[0]);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestTPC_GetRelActRec(g_dbId, relId[0], 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int j = 0; j < 3; j++) {
        if (j == 0) {
            for (int i = 0; i < insertNum; i++) {
                ret = SelectTbl64B(g_dbId, relId[j], i, false);
                if (ret != DB_SUCCESS_V1) {
                    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, ret);
                    break;
                }
            }
        } else {
            for (int i = 0; i < insertNum; i++) {
                ret = SelectTbl64B(g_dbId, relId[j], i, false);
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            }
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008 共享空间中删除一条数据
TEST_F(ShmSpDML, V1Com_039_ShmSpDML_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 8;
    int insertNum = REC_NUM_PER_PAGE / pageNum;  // 64条数据，1/8个页的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {  // 表1、表2写入数据 0-479；表3写入数据 0-31
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }
    // 删除表1条数据
    for (int i = 0; i < 1; i++) {
        ret = DeleteTbl64B(g_dbId, relId[0], i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    ret = TestTPC_GetRelActRec(g_dbId, relId[0], insertNum - 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 1; i < insertNum; i++) {
        ret = SelectTbl64B(g_dbId, relId[0], i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 009 独立空间中删除一条数据低于阈值后进行增删改查操作
TEST_F(ShmSpDML, V1Com_039_ShmSpDML_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 8;
    int insertNum = REC_NUM_PER_PAGE - (REC_NUM_PER_PAGE / pageNum) + 1;  // 448 + 1条数据，7/8个页的数据 多1条
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    DB_TPC_SYSVIEW_ARGS_STRU viewArgs = {0};
    viewArgs.ulDbId = g_dbId;
    viewArgs.usRelNo = 0xFFFF;
    VOS_UINT8 *pucResult = NULL;
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            if (k == insertNum - 2) {
                ret = TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &viewArgs, &pucResult);
                V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
                // 将结果字符串写入文件
                InfoInFile((char *)pucResult);
                const char *tblName[3] = {"tableA", "tableB", "tableC"};
                char cmd[1024];
                (void)sprintf_s(cmd, sizeof(cmd),
                    "index:0, table id: %d, table name: %s, record num: 448, record len: 48, state: 1", relId[j],
                    tblName[j]);  // state: 1 -- 表明满足迁出到独立空间条件，但是还在共享空间
                AW_FUN_Log(LOG_INFO, "string info:%s", cmd);
                // 共享空间可以查询到表名和表id等信息，校验表名
                char command[1024];
                (void)sprintf_s(command, sizeof(command), "cat check.txt");
                ret = executeCommand(command, cmd);
                V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
                TPC_FreeSysviewResult(&pucResult);
            } else if (k == insertNum - 1) {
                ret = TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &viewArgs, &pucResult);
                V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
                // 将结果字符串写入文件
                InfoInFile((char *)pucResult);
                // 共享空间可以查询到表名和表id等信息，校验表名
                char command[1024];
                (void)sprintf_s(command, sizeof(command), "cat check.txt");
                ret = executeCommand(
                    command, "common area details:table nums: 0, total data size: 0, total area size: 0");
                V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
                TPC_FreeSysviewResult(&pucResult);
            }
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }
    // 删除表1条数据，预期还是在独立空间，删除数据需要删除小于1/8个页才会迁移到共享，1/8至7/8为缓冲
    for (int i = 0; i < 1; i++) {
        ret = DeleteTbl64B(g_dbId, relId[0], i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 更新
    for (int i = 1; i < insertNum; i++) {
        ret = UpdateTbl64B(g_dbId, relId[0], i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // TPC_Sysview查询和表明检验，预期表在独立空间
    ret = TestSysviewAndCheckTbl(
        g_dbId, 0xFFFF, "common area details:table nums: 0, total data size: 0, total area size: 0");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 查询
    ret = TestTPC_GetRelActRec(g_dbId, relId[0], insertNum - 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除
    for (int i = 1; i < insertNum; i++) {
        ret = DeleteTbl64B(g_dbId, relId[0], i + 1000, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 写入
    for (int k = 0; k < insertNum; k++) {
        ret = InsertTbl64B(g_dbId, relId[0], k, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    VOS_UINT32 ulTblSize;
    ret = DBDBG_GetTblMemSize(g_dbId, relId[0], &ulTblSize);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_GE_INT(ulTblSize, 32768);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010 共享空间中对同一条数据进行多次增删改查
TEST_F(ShmSpDML, V1Com_039_ShmSpDML_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 8;
    int insertNum = 1;
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {  // 表1、表2写入数据 0-479；表3写入数据 0-31
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    for (int j = 0; j < 100; j++) {
        // 更新
        for (int i = 0; i < insertNum; i++) {
            ret = UpdateTbl64B(g_dbId, relId[0], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        // 查询
        ret = TestTPC_GetRelActRec(g_dbId, relId[0], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        // 删除
        for (int i = 0; i < insertNum; i++) {
            ret = DeleteTbl64B(g_dbId, relId[0], i + 1000, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        // 写入
        for (int i = 0; i < insertNum; i++) {
            ret = InsertTbl64B(g_dbId, relId[0], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }
    // 校验全表数据
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 011 共享空间中对不同的数据进行多次增删改查
TEST_F(ShmSpDML, V1Com_039_ShmSpDML_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 8;
    int insertNum = REC_NUM_PER_PAGE / pageNum;  // 32条数据，约1/16的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {  // 表1、表2写入数据 0-479；表3写入数据 0-31
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    for (int j = 0; j < 100; j++) {
        // 更新
        for (int i = 0; i < insertNum; i++) {
            ret = UpdateTbl64B(g_dbId, relId[0], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        // 查询
        ret = TestTPC_GetRelActRec(g_dbId, relId[0], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        // 删除
        for (int i = 0; i < insertNum; i++) {
            ret = DeleteTbl64B(g_dbId, relId[0], i + 1000, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        // 写入
        for (int i = 0; i < insertNum; i++) {
            ret = InsertTbl64B(g_dbId, relId[0], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }
    VOS_UINT32 ulTblSize;
    ret = DBDBG_GetTblMemSize(g_dbId, relId[0], &ulTblSize);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 012 pagesize为32K时，插入一条大小为pageSize / 8 - 1的数据
TEST_F(ShmSpDML, V1Com_039_ShmSpDML_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint16_t relId = 0;
    int insertNum = 1;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/tbl4097BWithIndex.json", &relId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 写入一条数据，Vbytes + 其他数据类型 || (4026+2) + 69 = 4097
    for (int k = 0; k < insertNum; k++) {
        ret = InsertTbl4097B(g_dbId, relId, k, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < insertNum; i++) {
        ret = SelectTbl4097B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // TPC_Sysview查询和表明检验，预期表在独立空间
    ret = TestSysviewAndCheckTbl(
        g_dbId, relId, "common area details:table nums: 0, total data size: 0, total area size: 0");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

class ShmSpDML2 : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void ShmSpDML2::SetUpTestCase()
{
    system("mkdir perFilePath;echo 'This is persistent DB' > ./perFilePath/perDbPath");
    system("mkdir filePath;echo 'This is normal DB' > ./filePath/normalDbPath");
}

void ShmSpDML2::TearDownTestCase()
{
    system("rm -rf ./filePath/ perFilePath/ check.txt");
}

void ShmSpDML2::SetUp()
{
    system("sh $TEST_HOME/tools/modifyCfg.sh \"pageSize=16\"");
    // 初始化
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_Init());
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    AW_CHECK_LOG_BEGIN();
    int ret;
    // 创建自定义数据类型
    DBTC_OPERATION_FUNC customCmpFunc[DB_OP_BUTT] = {0};
    customCmpFunc[DB_OP_EQUAL] = CmpEqual4CustomType;
    uint32_t dataTypeId = 100;
    ret = DB_CreateDataTypeByID(dataTypeId, customCmpFunc);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CreateDB((VOS_UINT8 *)g_dbName, NULL, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_dbName, &g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_CreateDB((VOS_UINT8 *)g_dbName2, NULL, &g_dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_dbName2, &g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    g_perDbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = TPC_CreateDB((VOS_UINT8 *)g_perDbName, (VOS_UINT8 *)g_perDbDir, &g_perDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB((VOS_UINT8 *)g_perDbDir, (VOS_UINT8 *)g_perDbName, &g_perDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}

void ShmSpDML2::TearDown()
{
    // CLoseDB
    int ret = TPC_CloseDB(g_dbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_CloseDB(g_dbId2);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_CloseDB(g_perDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // DropDB
    ret = TPC_DropDB((VOS_UINT8 *)g_dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_dbName2, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)g_perDbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_UnInit());
    AW_CHECK_LOG_END();
}

// 013 修改pagesize为16K时，插入一条大小为pageSize / 8 -1的数据
TEST_F(ShmSpDML2, V1Com_039_ShmSpDML_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint16_t relId = 0;
    int insertNum = 1;
    ret = TestTPC_CreateTbl(g_dbId, "schemaFile/tbl2049BWithIndex.json", &relId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 写入一条数据，Vbytes + 其他数据类型 || (1992+2) + 53 = 2047
    for (int k = 0; k < insertNum; k++) {
        ret = InsertTbl2047B(g_dbId, relId, k, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    ret = TestTPC_GetRelActRec(g_dbId, relId, insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < insertNum; i++) {
        ret = SelectTbl2047B(g_dbId, relId, i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // TPC_Sysview查询和表明检验，预期表在独立空间
    ret = TestSysviewAndCheckTbl(
        g_dbId, relId, "common area details:table nums: 0, total data size: 0, total area size: 0");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 014 修改pagesize为16K时，在临界值循环插入删除数据并校验
TEST_F(ShmSpDML2, V1Com_039_ShmSpDML_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 32;
    int insertNum = 16;  // 16条数据，1Kb，1/16个页的数据
    int addNum = 240;    // 240条数据，15.360Kb, 15/16个页的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {  // 表1、表2写入数据 0-479；表3写入数据 0-31
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    // TPC_Sysview查询和表明检验，预期表在独立空间
    ret = TestSysviewAndCheckTbl(g_dbId, 0xFFFF,
        "common area details:table nums: 3, total data size: 3552, total area size: 16384",
        "index:0, table id: 0, table name: tableA, record num: 16, record len: 48, state: 0",
        "index:1, table id: 1, table name: tableB, record num: 16, record len: 48, state: 0",
        "index:2, table id: 2, table name: tableC, record num: 16, record len: 48, state: 0");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 循环写入删除后查询
    for (int j = 0; j < 1; j++) {
        // 对表1进行写入操作，写入数据大于7/8个页
        for (int i = insertNum; i < addNum; i++) {
            ret = InsertTbl64B(g_dbId, relId[0], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[0], addNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < addNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[0], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }

        // TPC_Sysview查询和表明检验，预期表在独立空间
        ret = TestSysviewAndCheckTbl(g_dbId, 0xFFFF,
            "common area details:table nums: 2, total data size: 2368, total area size: 16384",
            "index:0, table id: 1, table name: tableB, record num: 16, record len: 48, state: 0",
            "index:1, table id: 2, table name: tableC, record num: 16, record len: 48, state: 0");
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

        // 删除数据至低于1/8个页
        for (int i = insertNum; i < addNum; i++) {  // 表1 删除 32-479，小于1/8个页
            ret = DeleteTbl64B(g_dbId, relId[0], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[0], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        sleep(10);
        for (int i = insertNum; i < insertNum + 1; i++) {
            ret = InsertTbl64B(g_dbId, relId[0], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }

        // TPC_Sysview查询和表明检验，预期表在共享空间
        ret = TestSysviewAndCheckTbl(g_dbId, 0xFFFF,
            "common area details:table nums: 3, total data size: 3626, total area size: 16384",
            "index:0, table id: 0, table name: tableA, record num: 17, record len: 48, state: 0",
            "index:1, table id: 1, table name: tableB, record num: 16, record len: 48, state: 0",
            "index:2, table id: 2, table name: tableC, record num: 16, record len: 48, state: 0");
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = insertNum; i < insertNum + 1; i++) {
            ret = DeleteTbl64B(g_dbId, relId[0], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 015 共享空间内多线程操作同一张表，一个线程插入大量数据足以使表迁出，一个线程删除大量数据足以使表迁入
TEST_F(ShmSpDML, V1Com_039_ShmSpDML_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;                    // 32条数据，1/16个页的数据,2K
    int addNum = REC_NUM_PER_PAGE - (REC_NUM_PER_PAGE / pageNum);  // 480条数据，15/16个页的数据,30K

    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        if (j == 2) {
            insertNum = REC_NUM_PER_PAGE / 2;  // 无索引表写入256条，16k的数据
        }
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    pthread_t tid[2];
    // 线程1，写入数据大于7/8
    ThrParaT para1 = {.threadId = 0, .dbId = g_dbId, .relId = relId[2]};
    ret = pthread_create(&tid[0], NULL, ThreadInsert, &para1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 线程2，删除所有数据
    ThrParaT para2 = {.threadId = 1, .dbId = g_dbId, .relId = relId[2]};
    ret = pthread_create(&tid[1], NULL, ThreadDelete, &para2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);

    uint32_t pulActRec = 0;
    ret = TPC_GetRelActRec(g_dbId, relId[2], &pulActRec);
    V1_AW_MACRO_EXPECT_GE_INT(pulActRec, 0);

    // TPC_Sysview查询和表明检验，预期表C可能在独立空间也可能共享空间
    ret = TestSysviewAndCheckTbl(g_dbId, 0xFFFF,
        "index:0, table id: 0, table name: tableA, record num: 32, record len: 48, state: 0",
        "index:1, table id: 1, table name: tableB, record num: 32, record len: 48, state: 0");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 016 共享空间中对不同的表进行多次增删改查
TEST_F(ShmSpDML, V1Com_039_ShmSpDML_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;                    // 32条数据，1/16个页的数据,2K
    int addNum = REC_NUM_PER_PAGE - (REC_NUM_PER_PAGE / pageNum);  // 480条数据，15/16个页的数据,30K

    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    // TPC_Sysview查询
    DB_TPC_SYSVIEW_ARGS_STRU viewArgs = {0};
    viewArgs.ulDbId = g_dbId;
    viewArgs.usRelNo = 0xFFFF;
    VOS_UINT8 *pucResult = NULL;

    // TPC_Sysview查询和表明检验，预期表在共享空间
    ret = TestSysviewAndCheckTbl(g_dbId, 0xFFFF,
        "index:0, table id: 0, table name: tableA, record num: 32, record len: 48, state: 0",
        "index:1, table id: 1, table name: tableB, record num: 32, record len: 48, state: 0",
        "index:2, table id: 2, table name: tableC, record num: 32, record len: 48, state: 0");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 循环写入删除后查询
    for (int j = 0; j < 1; j++) {
        for (int k = 0; k < 3; k++) {
            // 对表1进行写入操作，写入数据大于7/8个页
            for (int i = insertNum; i < addNum; i++) {
                ret = InsertTbl64B(g_dbId, relId[k], i, false);
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            }
            ret = TestSysviewAndCheckTbl(g_dbId, 0xFFFF, "common area details:table nums: 2");
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

            ret = TestTPC_GetRelActRec(g_dbId, relId[k], addNum);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            for (int i = insertNum; i < addNum; i++) {
                ret = SelectTbl64B(g_dbId, relId[k], i, false);
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            }

            for (int i = insertNum; i < addNum; i++) {
                ret = UpdateTbl64B(g_dbId, relId[k], i, false);
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            }

            // 删除数据至低于1/8个页
            for (int i = insertNum; i < addNum; i++) {
                ret = DeleteTbl64B(g_dbId, relId[k], i + 1000, false);
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            }
            uint32_t tblRecLen = 0;
            ret = TestGetTblRecLen(g_dbId, relId[k], &tblRecLen);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            sleep(10);
            for (int i = insertNum - 1; i < insertNum; i++) {
                ret = DeleteTbl64B(g_dbId, relId[k], i, false);
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            }
            for (int i = insertNum - 1; i < insertNum; i++) {
                ret = InsertTbl64B(g_dbId, relId[k], i, false);
                V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
            }
            ret = TestSysviewAndCheckTbl(g_dbId, 0xFFFF,
                "index:0, table id: 0, table name: tableA, record num: 32, record len: 48, state: 0",
                "index:1, table id: 1, table name: tableB, record num: 32, record len: 48, state: 0",
                "index:2, table id: 2, table name: tableC, record num: 32, record len: 48, state: 0");
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 017 多线程增删改查操作不同表
TEST_F(ShmSpDML, V1Com_039_ShmSpDML_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;                    // 32条数据，1/16个页的数据,2K
    int addNum = REC_NUM_PER_PAGE - (REC_NUM_PER_PAGE / pageNum);  // 480条数据，15/16个页的数据,30K

    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {  // 表1、表2写入数据 0-479；表3写入数据 0-31
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    pthread_t tid[3];
    // 线程1，对表1进行增删查改
    ThrParaT para1 = {.threadId = 0, .dbId = g_dbId, .relId = relId[0]};
    ret = pthread_create(&tid[0], NULL, ThreadDmlRound, &para1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 线程2，对表2进行增删查改
    ThrParaT para2 = {.threadId = 1, .dbId = g_dbId, .relId = relId[1]};
    ret = pthread_create(&tid[1], NULL, ThreadDmlRound, &para2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 线程3，对表3进行增删查改
    ThrParaT para3 = {.threadId = 2, .dbId = g_dbId, .relId = relId[2]};
    ret = pthread_create(&tid[2], NULL, ThreadDmlRound, &para3);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 018 多线程增删改查操作不同表，其中一个表插入数据至超出阈值
TEST_F(ShmSpDML, V1Com_039_ShmSpDML_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;                    // 32条数据，1/16个页的数据,2K
    int addNum = REC_NUM_PER_PAGE - (REC_NUM_PER_PAGE / pageNum);  // 480条数据，15/16个页的数据,30K

    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    pthread_t tid[3];
    // 线程1，对表1进行增删查改
    ThrParaT para1 = {.threadId = 0, .dbId = g_dbId, .relId = relId[0]};
    ret = pthread_create(&tid[0], NULL, ThreadDmlRound, &para1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 线程2，对表2进行增删查改
    ThrParaT para2 = {.threadId = 1, .dbId = g_dbId, .relId = relId[1]};
    ret = pthread_create(&tid[1], NULL, ThreadDmlRound, &para2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 线程3，对表3进行增删查改
    ThrParaT para3 = {.threadId = 2, .dbId = g_dbId, .relId = relId[2]};
    ret = pthread_create(&tid[2], NULL, ThreadInsertThreshold, &para3);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 019 多线程增删改查操作不同表，其中一个表删除数据至低于阈值
TEST_F(ShmSpDML, V1Com_039_ShmSpDML_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;                    // 32条数据，1/16个页的数据,2K
    int addNum = REC_NUM_PER_PAGE - (REC_NUM_PER_PAGE / pageNum);  // 480条数据，15/16个页的数据,30K

    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {  // 表1、表2写入数据 0-479；表3写入数据 0-31
        if (j == 2) {
            insertNum = addNum;
        }
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    pthread_t tid[3];
    // 线程1，对表1进行增删查改
    ThrParaT para1 = {.threadId = 0, .dbId = g_dbId, .relId = relId[0]};
    ret = pthread_create(&tid[0], NULL, ThreadDmlRound, &para1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 线程2，对表2进行增删查改
    ThrParaT para2 = {.threadId = 1, .dbId = g_dbId, .relId = relId[1]};
    ret = pthread_create(&tid[1], NULL, ThreadDmlRound, &para2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 线程3，对表3进行增删查改
    ThrParaT para3 = {.threadId = 2, .dbId = g_dbId, .relId = relId[2]};
    ret = pthread_create(&tid[2], NULL, ThreadDeleteThreshold, &para3);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 020 多线程增删改查不同表，使所有表频繁迁入迁出
TEST_F(ShmSpDML, V1Com_039_ShmSpDML_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE - (REC_NUM_PER_PAGE / pageNum);  // 480条数据，15/16个页的数据,30K

    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    pthread_t tid[3];
    // 线程1，对表1进行增删查改
    ThrParaT para1 = {.threadId = 0, .dbId = g_dbId, .relId = relId[0]};
    ret = pthread_create(&tid[0], NULL, ThreadDeleteThreshold, &para1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 线程2，对表2进行增删查改
    ThrParaT para2 = {.threadId = 1, .dbId = g_dbId, .relId = relId[1]};
    ret = pthread_create(&tid[1], NULL, ThreadDeleteThreshold, &para2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 线程3，对表3进行增删查改
    ThrParaT para3 = {.threadId = 2, .dbId = g_dbId, .relId = relId[2]};
    ret = pthread_create(&tid[2], NULL, ThreadDeleteThreshold, &para3);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 021 在CDB中插入数据至超过阈值后提交
TEST_F(ShmSpDML, V1Com_039_ShmSpDML_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;                    // 32条数据，1/16个页的数据,2K
    int addNum = REC_NUM_PER_PAGE - (REC_NUM_PER_PAGE / pageNum);  // 480条数据，15/16个页的数据,30K

    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    // 开启cdb事务
    uint32_t cdbId = 0;
    ret = TPC_BeginCDB(g_dbId, &cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 对表1写入数据至7/8个页
    for (int i = insertNum; i < addNum; i++) {
        ret = InsertTbl64B(g_dbId, relId[0], i, false, cdbId);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // TPC_Sysview查询
    ret = TestSysviewAndCheckTbl(g_dbId, relId[0],
        "common area details:table nums: 3, total data size: 7104, total area size: 65536",
        "index:0, table id: 0, table name: tableA, record num: 32, record len: 48, state: 0");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // diff查询
    uint32_t pulInsrecNum;
    void *ppInsRecList = NULL;
    uint32_t pulDelrecNum;
    void *ppDelRecList = NULL;
    uint32_t pulUpdrecNum;
    void *ppUpdRecList = NULL;

    ret = TPC_SelectCdbDiffData(
        cdbId, relId[0], &pulInsrecNum, &ppInsRecList, &pulDelrecNum, &ppDelRecList, &pulUpdrecNum, &ppUpdRecList);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(addNum - insertNum, pulInsrecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(0, pulDelrecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(0, pulUpdrecNum);

    // 提交cdb事务
    ret = TPC_CommitCDB(cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TestSysviewAndCheckTbl(g_dbId, relId[0],
        "common area details:table nums: 3, total data size: 40256, total area size: 65536",
        "index:0, table id: 0, table name: tableA, record num: 480, record len: 48, state: 1");  // 满足迁移条件
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取表1的内存
    VOS_UINT32 ulTblSize;
    ret = DBDBG_GetTblMemSize(g_dbId, relId[0], &ulTblSize);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_GE_INT(ulTblSize, 32768);

    // 校验表数据
    ret = TestTPC_GetRelActRec(g_dbId, relId[0], addNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < addNum; i++) {
        ret = SelectTbl64B(g_dbId, relId[0], i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    for (int j = 1; j < 3; j++) {
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    // 再次写入一条数据，触发迁移
    for (int i = addNum; i < addNum + 1; i++) {
        ret = InsertTbl64B(g_dbId, relId[0], i, true, cdbId);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    ret = TestSysviewAndCheckTbl(
        g_dbId, relId[0], "common area details:table nums: 2, total data size: 4736, total area size: 32768");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 释放CDB中插入、删除和更新的所有记录内存
    TPC_FreeDiffData(ppInsRecList, ppDelRecList, ppUpdRecList);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 022 在CDB插入数据至超过阈值后回滚
TEST_F(ShmSpDML, V1Com_039_ShmSpDML_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;                    // 32条数据，1/16个页的数据,2K
    int addNum = REC_NUM_PER_PAGE - (REC_NUM_PER_PAGE / pageNum);  // 480条数据，15/16个页的数据,30K

    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    // 开启cdb事务
    uint32_t cdbId = 0;
    ret = TPC_BeginCDB(g_dbId, &cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 对表1写入数据至7/8个页
    for (int i = insertNum; i < addNum; i++) {
        ret = InsertTbl64B(g_dbId, relId[0], i, false, cdbId);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // TPC_Sysview查询
    ret = TestSysviewAndCheckTbl(g_dbId, relId[0],
        "common area details:table nums: 3, total data size: 7104, total area size: 65536",
        "index:0, table id: 0, table name: tableA, record num: 32, record len: 48, state: 0");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 回滚事务
    ret = TPC_RollbackCDB(cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取表1的内存
    VOS_UINT32 ulTblSize;
    ret = DBDBG_GetTblMemSize(g_dbId, relId[0], &ulTblSize);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_LE_INT(ulTblSize, 32768);

    // 校验表数据
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 023 在CDB中插入数据至超过阈值后进行增删改查再提交
TEST_F(ShmSpDML, V1Com_039_ShmSpDML_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;                    // 32条数据，1/16个页的数据,2K
    int addNum = REC_NUM_PER_PAGE - (REC_NUM_PER_PAGE / pageNum);  // 480条数据，15/16个页的数据,30K

    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    AW_FUN_Log(LOG_INFO, "写数据后查询表占用空间");
    ret = TestSysviewAndCheckTbl(g_dbId, relId[0],
        "common area details:table nums: 3, total data size: 7104, total area size: 32768",
        "index:0, table id: 0, table name: tableA, record num: 32, record len: 48, state: 0");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启cdb事务
    uint32_t cdbId = 0;
    ret = TPC_BeginCDB(g_dbId, &cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 更新
    for (int i = 0; i < insertNum; i++) {
        ret = UpdateTbl64B(g_dbId, relId[0], i, false, false, cdbId);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 删除
    for (int i = 0; i < insertNum; i++) {
        ret = DeleteTbl64B(g_dbId, relId[0], i + 1000, false, true, cdbId);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 对表1写入数据大于7/8个页
    for (int i = 0; i < addNum; i++) {
        ret = InsertTbl64B(g_dbId, relId[0], i, false, cdbId);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 校验表数据
    for (int i = 0; i < addNum; i++) {
        ret = SelectTbl64B(g_dbId, relId[0], i, false, cdbId);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // TPC_Sysview查询
    AW_FUN_Log(LOG_INFO, "cdb内 增删改查后查询表占用空间");
    ret = TestSysviewAndCheckTbl(g_dbId, relId[0],
        "common area details:table nums: 3, total data size: 7104, total area size: 65536",
        "index:0, table id: 0, table name: tableA, record num: 32, record len: 48, state: 0");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // diff查询
    uint32_t pulInsrecNum;
    void *ppInsRecList = NULL;
    uint32_t pulDelrecNum;
    void *ppDelRecList = NULL;
    uint32_t pulUpdrecNum;
    void *ppUpdRecList = NULL;

    ret = TPC_SelectCdbDiffData(
        cdbId, relId[0], &pulInsrecNum, &ppInsRecList, &pulDelrecNum, &ppDelRecList, &pulUpdrecNum, &ppUpdRecList);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(addNum, pulInsrecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(insertNum, pulDelrecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(0, pulUpdrecNum);

    // 提交cdb事务
    ret = TPC_CommitCDB(cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取表1的内存
    VOS_UINT32 ulTblSize;
    ret = DBDBG_GetTblMemSize(g_dbId, relId[0], &ulTblSize);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_GE_INT(ulTblSize, 32768);

    for (int j = 0; j < 1; j++) {
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], addNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < addNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }
    for (int i = addNum; i < addNum + 1; i++) {
        ret = InsertTbl64B(g_dbId, relId[0], i, true, cdbId);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    AW_FUN_Log(LOG_INFO, "触发迁移后查询表占用空间");
    ret = TestSysviewAndCheckTbl(
        g_dbId, relId[0], "common area details:table nums: 2, total data size: 4736, total area size: 32768");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 释放CDB中插入、删除和更新的所有记录内存
    TPC_FreeDiffData(ppInsRecList, ppDelRecList, ppUpdRecList);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 024 在CDB中删除数据至低于阈值后提交
TEST_F(ShmSpDML, V1Com_039_ShmSpDML_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;                    // 32条数据，1/16个页的数据,2K
    int addNum = REC_NUM_PER_PAGE - (REC_NUM_PER_PAGE / pageNum);  // 480条数据，15/16个页的数据,30K

    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        if (j == 0) {
            insertNum = addNum;
        } else {
            insertNum = REC_NUM_PER_PAGE / pageNum;
        }
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    // TPC_Sysview查询
    ret = TestSysviewAndCheckTbl(g_dbId, 0xFFFF,
        "common area details:table nums: 2, total data size: 4736, total area size: 32768",
        "index:0, table id: 1, table name: tableB, record num: 32, record len: 48, state: 0",
        "index:1, table id: 2, table name: tableC, record num: 32, record len: 48, state: 0");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启cdb事务
    uint32_t cdbId = 0;
    ret = TPC_BeginCDB(g_dbId, &cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删除表1数据至1/8个页
    for (int i = insertNum; i < addNum; i++) {
        ret = DeleteTbl64B(g_dbId, relId[0], i, false, true, cdbId);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 校验表数据
    for (int i = 0; i < insertNum; i++) {
        ret = SelectTbl64B(g_dbId, relId[0], i, false, cdbId);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    ret = TestSysviewAndCheckTbl(g_dbId, 0xFFFF,
        "common area details:table nums: 2, total data size: 4736, total area size: 32768",
        "index:0, table id: 1, table name: tableB, record num: 32, record len: 48, state: 0",
        "index:1, table id: 2, table name: tableC, record num: 32, record len: 48, state: 0");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // diff查询
    uint32_t pulInsrecNum;
    void *ppInsRecList = NULL;
    uint32_t pulDelrecNum;
    void *ppDelRecList = NULL;
    uint32_t pulUpdrecNum;
    void *ppUpdRecList = NULL;

    ret = TPC_SelectCdbDiffData(
        cdbId, relId[0], &pulInsrecNum, &ppInsRecList, &pulDelrecNum, &ppDelRecList, &pulUpdrecNum, &ppUpdRecList);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0, pulInsrecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(addNum - insertNum, pulDelrecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(0, pulUpdrecNum);

    // 提交cdb事务
    ret = TPC_CommitCDB(cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int j = 0; j < 3; j++) {
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    sleep(10);
    // 再删除1条数据，触发迁移
    for (int i = 0; i < 1; i++) {
        ret = DeleteTbl64B(g_dbId, relId[0], i, true, true, cdbId);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 获取表1的内存
    VOS_UINT32 ulTblSize;
    ret = DBDBG_GetTblMemSize(g_dbId, relId[0], &ulTblSize);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_LE_INT(ulTblSize, 32768);

    ret = TestSysviewAndCheckTbl(g_dbId, 0xFFFF,
        "common area details:table nums: 3, total data size: 7030, total area size: 65536",
        "index:0, table id: 0, table name: tableA, record num: 31, record len: 48, state: 0",
        "index:1, table id: 1, table name: tableB, record num: 32, record len: 48, state: 0",
        "index:2, table id: 2, table name: tableC, record num: 32, record len: 48, state: 0");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 释放CDB中插入、删除和更新的所有记录内存
    TPC_FreeDiffData(ppInsRecList, ppDelRecList, ppUpdRecList);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 025 在CDB中删除数据至低于阈值后回滚
TEST_F(ShmSpDML, V1Com_039_ShmSpDML_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;                    // 32条数据，1/16个页的数据,2K
    int addNum = REC_NUM_PER_PAGE - (REC_NUM_PER_PAGE / pageNum);  // 480条数据，15/16个页的数据,30K

    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        if (j == 0) {
            insertNum = addNum;
        } else {
            insertNum = REC_NUM_PER_PAGE / pageNum;
        }
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    // TPC_Sysview查询
    ret = TestSysviewAndCheckTbl(g_dbId, 0xFFFF,
        "common area details:table nums: 2, total data size: 4736, total area size: 32768",
        "index:0, table id: 1, table name: tableB, record num: 32, record len: 48, state: 0",
        "index:1, table id: 2, table name: tableC, record num: 32, record len: 48, state: 0");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启cdb事务
    uint32_t cdbId = 0;
    ret = TPC_BeginCDB(g_dbId, &cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删除表1数据至1/8个页
    for (int i = insertNum; i < addNum; i++) {
        ret = DeleteTbl64B(g_dbId, relId[0], i, false, true, cdbId);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 校验表数据
    for (int i = 0; i < insertNum; i++) {
        ret = SelectTbl64B(g_dbId, relId[0], i, false, cdbId);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    ret = TestSysviewAndCheckTbl(g_dbId, 0xFFFF,
        "common area details:table nums: 2, total data size: 4736, total area size: 32768",
        "index:0, table id: 1, table name: tableB, record num: 32, record len: 48, state: 0",
        "index:1, table id: 2, table name: tableC, record num: 32, record len: 48, state: 0");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // diff查询
    uint32_t pulInsrecNum;
    void *ppInsRecList = NULL;
    uint32_t pulDelrecNum;
    void *ppDelRecList = NULL;
    uint32_t pulUpdrecNum;
    void *ppUpdRecList = NULL;

    ret = TPC_SelectCdbDiffData(
        cdbId, relId[0], &pulInsrecNum, &ppInsRecList, &pulDelrecNum, &ppDelRecList, &pulUpdrecNum, &ppUpdRecList);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0, pulInsrecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(addNum - insertNum, pulDelrecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(0, pulUpdrecNum);

    // 回滚事务
    ret = TPC_RollbackCDB(cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取表1的内存
    VOS_UINT32 ulTblSize;
    ret = DBDBG_GetTblMemSize(g_dbId, relId[0], &ulTblSize);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验表数据
    ret = TestTPC_GetRelActRec(g_dbId, relId[0], addNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < addNum; i++) {
        ret = SelectTbl64B(g_dbId, relId[0], i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    for (int j = 1; j < 3; j++) {
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }
    sleep(10);
    // 再删除1条数据，不触发迁移
    for (int i = 0; i < 1; i++) {
        ret = DeleteTbl64B(g_dbId, relId[0], i, true, true, cdbId);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    ret = TestSysviewAndCheckTbl(g_dbId, 0xFFFF,
        "common area details:table nums: 2, total data size: 4736, total area size: 32768",
        "index:0, table id: 1, table name: tableB, record num: 32, record len: 48, state: 0",
        "index:1, table id: 2, table name: tableC, record num: 32, record len: 48, state: 0");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 释放CDB中插入、删除和更新的所有记录内存
    TPC_FreeDiffData(ppInsRecList, ppDelRecList, ppUpdRecList);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 026 在CDB中更新数据后校验
TEST_F(ShmSpDML, V1Com_039_ShmSpDML_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;                    // 32条数据，1/16个页的数据,2K
    int addNum = REC_NUM_PER_PAGE - (REC_NUM_PER_PAGE / pageNum);  // 480条数据，15/16个页的数据,30K

    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    // 开启cdb事务
    uint32_t cdbId = 0;
    ret = TPC_BeginCDB(g_dbId, &cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 对表1更新
    for (int i = 0; i < insertNum; i++) {
        ret = UpdateTbl64B(g_dbId, relId[0], i, false, false, cdbId);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // diff查询
    uint32_t pulInsrecNum;
    void *ppInsRecList = NULL;
    uint32_t pulDelrecNum;
    void *ppDelRecList = NULL;
    uint32_t pulUpdrecNum;
    void *ppUpdRecList = NULL;

    ret = TPC_SelectCdbDiffData(
        cdbId, relId[0], &pulInsrecNum, &ppInsRecList, &pulDelrecNum, &ppDelRecList, &pulUpdrecNum, &ppUpdRecList);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0, pulInsrecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(0, pulDelrecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(insertNum * 2, pulUpdrecNum);  // 老数据 + 新数据

    // 提交cdb事务
    ret = TPC_CommitCDB(cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取表1的内存
    VOS_UINT32 ulTblSize;
    ret = DBDBG_GetTblMemSize(g_dbId, relId[0], &ulTblSize);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TestTPC_GetRelActRec(g_dbId, relId[0], insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < insertNum; i++) {
        ret = SelectTbl64B(g_dbId, relId[0], i + 1000, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    for (int j = 1; j < 3; j++) {
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    // 释放CDB中插入、删除和更新的所有记录内存
    TPC_FreeDiffData(ppInsRecList, ppDelRecList, ppUpdRecList);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 027 共享空间中CDB操作异常回滚后再次开启CDB进行增删改查操作
TEST_F(ShmSpDML, V1Com_039_ShmSpDML_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;                    // 32条数据，1/16个页的数据,2K
    int addNum = REC_NUM_PER_PAGE - (REC_NUM_PER_PAGE / pageNum);  // 480条数据，15/16个页的数据,30K

    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    // 开启cdb事务
    uint32_t cdbId = 0;
    ret = TPC_BeginCDB(g_dbId, &cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 对表1写入1条冲突数据后回滚
    for (int i = 0; i < 1; i++) {
        ret = InsertTbl64B(g_dbId, relId[0], i, false, cdbId);
        V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_KEYDUPLICATE, ret);
    }

    // 回滚事务
    ret = TPC_RollbackCDB(cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 再次开启cdb事务
    ret = TPC_BeginCDB(g_dbId, &cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 对表1写入
    for (int i = insertNum; i < addNum; i++) {
        ret = InsertTbl64B(g_dbId, relId[0], i, false, cdbId);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 校验表数据
    for (int i = 0; i < addNum; i++) {
        ret = SelectTbl64B(g_dbId, relId[0], i, false, cdbId);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 更新 32  insert  448   total 480
    for (int i = insertNum; i < addNum; i++) {
        ret = UpdateTbl64B(g_dbId, relId[0], i, false, false, cdbId);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 校验表数据
    for (int i = insertNum; i < addNum; i++) {
        ret = SelectTbl64B(g_dbId, relId[0], i + 1000, false, cdbId);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 删除
    for (int i = insertNum; i < addNum; i++) {
        ret = DeleteTbl64B(g_dbId, relId[0], i + 1000, false, true, cdbId);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 校验表数据
    ret = TestTPC_GetRelActRec(g_dbId, relId[0], insertNum);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < insertNum; i++) {
        ret = SelectTbl64B(g_dbId, relId[0], i, false, cdbId);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 提交cdb事务
    ret = TPC_CommitCDB(cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取表1的内存
    VOS_UINT32 ulTblSize;
    ret = DBDBG_GetTblMemSize(g_dbId, relId[0], &ulTblSize);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int j = 0; j < 3; j++) {
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 028 共享空间中多次开启CDB对同一条数据进行增删改查后校验数据
TEST_F(ShmSpDML, V1Com_039_ShmSpDML_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;                    // 32条数据，1/16个页的数据,2K
    int addNum = REC_NUM_PER_PAGE - (REC_NUM_PER_PAGE / pageNum);  // 480条数据，15/16个页的数据,30K

    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    // 1 开启cdb事务 || 写入1条数据
    uint32_t cdbId = 0;
    ret = TPC_BeginCDB(g_dbId, &cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 对表1写入1条冲突数据后回滚
    for (int i = insertNum; i < insertNum + 1; i++) {
        ret = InsertTbl64B(g_dbId, relId[0], i, false, cdbId);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 校验表数据
    for (int i = 0; i < insertNum + 1; i++) {
        ret = SelectTbl64B(g_dbId, relId[0], i, false, cdbId);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // diff查询
    uint32_t pulInsrecNum;
    void *ppInsRecList = NULL;
    uint32_t pulDelrecNum;
    void *ppDelRecList = NULL;
    uint32_t pulUpdrecNum;
    void *ppUpdRecList = NULL;

    ret = TPC_SelectCdbDiffData(
        cdbId, relId[0], &pulInsrecNum, &ppInsRecList, &pulDelrecNum, &ppDelRecList, &pulUpdrecNum, &ppUpdRecList);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(1, pulInsrecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(0, pulDelrecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(0, pulUpdrecNum);
    TPC_FreeDiffData(ppInsRecList, ppDelRecList, ppUpdRecList);

    // 提交cdb事务
    ret = TPC_CommitCDB(cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 再次开启cdb事务 || 更新刚写入的1条数据
    ret = TPC_BeginCDB(g_dbId, &cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 更新
    for (int i = insertNum; i < insertNum + 1; i++) {
        ret = UpdateTbl64B(g_dbId, relId[0], i, false, false, cdbId);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 校验表数据
    ret = TestTPC_GetRelActRec(g_dbId, relId[0], insertNum + 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = insertNum; i < insertNum + 1; i++) {
        ret = SelectTbl64B(g_dbId, relId[0], i + 1000, false, cdbId);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    ret = TPC_SelectCdbDiffData(
        cdbId, relId[0], &pulInsrecNum, &ppInsRecList, &pulDelrecNum, &ppDelRecList, &pulUpdrecNum, &ppUpdRecList);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0, pulInsrecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(0, pulDelrecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(2, pulUpdrecNum);
    TPC_FreeDiffData(ppInsRecList, ppDelRecList, ppUpdRecList);

    // 提交cdb事务
    ret = TPC_CommitCDB(cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 再次开启cdb事务 || 删除刚更新的1条数据
    ret = TPC_BeginCDB(g_dbId, &cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除
    for (int i = insertNum; i < insertNum + 1; i++) {
        ret = DeleteTbl64B(g_dbId, relId[0], i + 1000, false, true, cdbId);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 校验表数据
    for (int i = 0; i < insertNum; i++) {
        ret = SelectTbl64B(g_dbId, relId[0], i, false, cdbId);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    ret = TPC_SelectCdbDiffData(
        cdbId, relId[0], &pulInsrecNum, &ppInsRecList, &pulDelrecNum, &ppDelRecList, &pulUpdrecNum, &ppUpdRecList);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0, pulInsrecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(1, pulDelrecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(0, pulUpdrecNum);
    // 释放CDB中插入、删除和更新的所有记录内存
    TPC_FreeDiffData(ppInsRecList, ppDelRecList, ppUpdRecList);

    // 提交cdb事务
    ret = TPC_CommitCDB(cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int j = 0; j < 3; j++) {
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 029 共享空间中多次开启CDB对不同数据进行增删改查后校验数据
TEST_F(ShmSpDML, V1Com_039_ShmSpDML_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;                    // 32条数据，1/16个页的数据,2K
    int addNum = REC_NUM_PER_PAGE - (REC_NUM_PER_PAGE / pageNum);  // 480条数据，15/16个页的数据,30K

    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    uint32_t cdbId = 0;
    // 1 再次开启cdb事务 || 更新刚写入的1条数据
    ret = TPC_BeginCDB(g_dbId, &cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 更新
    for (int i = 0; i < insertNum; i++) {
        ret = UpdateTbl64B(g_dbId, relId[0], i, false, false, cdbId);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 校验表数据
    for (int i = insertNum; i < insertNum; i++) {
        ret = SelectTbl64B(g_dbId, relId[0], i + 1000, false, cdbId);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // diff查询
    uint32_t pulInsrecNum;
    void *ppInsRecList = NULL;
    uint32_t pulDelrecNum;
    void *ppDelRecList = NULL;
    uint32_t pulUpdrecNum;
    void *ppUpdRecList = NULL;

    ret = TPC_SelectCdbDiffData(
        cdbId, relId[0], &pulInsrecNum, &ppInsRecList, &pulDelrecNum, &ppDelRecList, &pulUpdrecNum, &ppUpdRecList);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0, pulInsrecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(0, pulDelrecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(insertNum * 2, pulUpdrecNum);
    TPC_FreeDiffData(ppInsRecList, ppDelRecList, ppUpdRecList);

    // 提交cdb事务
    ret = TPC_CommitCDB(cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 2 再次开启cdb事务 || 删除刚更新的1条数据
    ret = TPC_BeginCDB(g_dbId, &cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除
    for (int i = 0; i < insertNum; i++) {
        ret = DeleteTbl64B(g_dbId, relId[0], i + 1000, false, true, cdbId);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    ret = TPC_SelectCdbDiffData(
        cdbId, relId[0], &pulInsrecNum, &ppInsRecList, &pulDelrecNum, &ppDelRecList, &pulUpdrecNum, &ppUpdRecList);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(0, pulInsrecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(insertNum, pulDelrecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(0, pulUpdrecNum);
    // 释放CDB中插入、删除和更新的所有记录内存
    TPC_FreeDiffData(ppInsRecList, ppDelRecList, ppUpdRecList);

    // 提交cdb事务
    ret = TPC_CommitCDB(cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 3 开启cdb事务 || 写入1条数据
    ret = TPC_BeginCDB(g_dbId, &cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 对表1写入
    for (int i = 0; i < insertNum; i++) {
        ret = InsertTbl64B(g_dbId, relId[0], i, false, cdbId);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    // 校验表数据
    for (int i = 0; i < insertNum; i++) {
        ret = SelectTbl64B(g_dbId, relId[0], i, false, cdbId);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    ret = TPC_SelectCdbDiffData(
        cdbId, relId[0], &pulInsrecNum, &ppInsRecList, &pulDelrecNum, &ppDelRecList, &pulUpdrecNum, &ppUpdRecList);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(insertNum, pulInsrecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(0, pulDelrecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(0, pulUpdrecNum);
    TPC_FreeDiffData(ppInsRecList, ppDelRecList, ppUpdRecList);

    // 提交cdb事务
    ret = TPC_CommitCDB(cdbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    for (int j = 0; j < 3; j++) {
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 030 无索引且无法建立索引的表在共享空间中进行增删改查
TEST_F(ShmSpDML, V1Com_039_ShmSpDML_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;                    // 32条数据
    int addNum = REC_NUM_PER_PAGE - (REC_NUM_PER_PAGE / pageNum);  // 480条数据

    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 1; j++) {
        ret = TestTPC_CreateTbl(g_dbId, "schemaFile/tblBIT.json", &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTblBit(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    // TPC_Sysview查询和表明检验，预期表在共享空间
    ret = TestSysviewAndCheckTbl(
        g_dbId, 0xFFFF, "common area details:table nums: 0, total data size: 0, total area size: 0");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删除数据
    ret = DBS_DeleteRelAllRecords(g_dbId, relId[0]);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TestSysviewAndCheckTbl(
        g_dbId, 0xFFFF, "common area details:table nums: 0, total data size: 0, total area size: 0");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 031 独立空间中一次性删除表中所有数据
TEST_F(ShmSpDML, V1Com_039_ShmSpDML_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE - (REC_NUM_PER_PAGE / pageNum);  // 480条数据，15/16个页的数据
    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 3; j++) {  // 表1、表2写入数据 0-479；表3写入数据 0-31
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }
    ret = TestSysviewAndCheckTbl(
        g_dbId, 0xFFFF, "common area details:table nums: 0, total data size: 0, total area size: 0");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 删除表1所有数据
    ret = DBS_DeleteRelAllRecords(g_dbId, relId[0]);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 预期还在独立空间
    ret = TestSysviewAndCheckTbl(
        g_dbId, 0xFFFF, "common area details:table nums: 0, total data size: 0, total area size: 0");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TestTPC_GetRelActRec(g_dbId, relId[0], 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    sleep(10);
    for (int k = 0; k < 1; k++) {
        ret = InsertTbl64B(g_dbId, relId[0], k, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    ret = TestTPC_GetRelActRec(g_dbId, relId[0], 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    for (int i = 0; i < 1; i++) {
        ret = SelectTbl64B(g_dbId, relId[0], i, false);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    ret = TestSysviewAndCheckTbl(g_dbId, 0xFFFF,
        "common area details:table nums: 1, total data size: 74, total area size: 32768",
        "index:0, table id: 0, table name: tableA, record num: 1, record len: 48, state: 0");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 预期还在共享空间
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 032 共享空间中修改表名表id后进行增删改查
TEST_F(ShmSpDML, V1Com_039_ShmSpDML_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;                    // 32条数据，1/16个页的数据,2K
    int addNum = REC_NUM_PER_PAGE - (REC_NUM_PER_PAGE / pageNum);  // 480条数据，15/16个页的数据,30K

    uint16_t relId[6] = {0};
    const char *filePath[3] = {"schemaFile/tbl64BWithIndex.json", "schemaFile/tbl64BWithNonUniqueIndex.json",
        "schemaFile/tbl64BWithoutIndex.json"};
    // DB1建表，唯一索引、非唯一索引和无索引
    for (int j = 0; j < 1; j++) {
        ret = TestTPC_CreateTbl(g_dbId, filePath[j], &relId[j]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(g_dbId, relId[j], k, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(g_dbId, relId[j], insertNum);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(g_dbId, relId[j], i, false);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }

    // TPC_Sysview查询
    ret = TestSysviewAndCheckTbl(g_dbId, 0xFFFF,
        "common area details:table nums: 1, total data size: 2368, total area size: 32768",
        "index:0, table id: 0, table name: tableA, record num: 32, record len: 48, state: 0");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TPC_ModifyTblNameAndID(g_dbId, relId[0], (VOS_UINT8 *)"table2", relId[0] + 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = TestSysviewAndCheckTbl(g_dbId, 0xFFFF,
        "common area details:table nums: 1, total data size: 2368, total area size: 32768",
        "index:0, table id: 1, table name: table2, record num: 32, record len: 48, state: 0");
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
