/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: GMDBV5\test\sdv\testcases\30_V1Compatible\039_SupportV1_SharedSpace
 * Designer: 潘志刚 30044498
 * Author: tangguangming
 * Create: 2025-07-03
 */

#ifndef SHM_SP_OPERATION_H
#define SHM_SP_OPERATION_H
#include <stdio.h>
#include <stdint.h>
#include <arpa/inet.h>
#include <sys/stat.h>
#include <iostream>
#include <fstream>
#include "t_rd_simplerel.h"
#define MAX_CDB_NUM 240  // cdb可以开启的最大个数

uint32_t g_testDbId = 0;
char g_dbName[20] = "ShmSpName01";
char g_dbName2[20] = "ShmSpName02";
char g_dbName3[20] = "ShmSpName03";
char g_dbName4[20] = "ShmSpName04";
char g_perDbName[20] = "ShmSpName05";
char g_perDbName2[20] = "ShmSpName06";
VOS_UINT32 g_dbId = 0;
VOS_UINT32 g_dbId2 = 0;
VOS_UINT32 g_dbId3 = 0;
VOS_UINT32 g_dbId4 = 0;
VOS_UINT32 g_perDbId = 0;
VOS_UINT32 g_perDbId2 = 0;
DB_INST_CONFIG_STRU g_dbCfg = {0};
DB_INST_CONFIG_STRU g_perDbCfg = {0};
char g_perDbDir[64] = "./perFilePath/perDbPath";
char g_dbDir[64] = "./filePath/normalDbPath";
#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];

using namespace std;

#define REC_NUM_PER_PAGE 512     // 32K / 64B = 512

// 将字符串写入到文件
void InfoInFile(char *msg)
{
    system("rm -rf check.txt");
    std::ofstream write;
    write.open("check.txt", std::ios::app);
    write << msg << std::endl;
    write.close();
}

int TestSysviewAndCheckTbl(VOS_UINT32 ulDbId = g_dbId, VOS_UINT16 usRelNo = 0xFFFF, const char *v1 = NULL,
    const char *v2 = NULL, const char *v3 = NULL, const char *v4 = NULL, const char *v5 = NULL)
{
    int ret;
    // TPC_Sysview查询
    DB_TPC_SYSVIEW_ARGS_STRU viewArgs = {0};
    viewArgs.ulDbId = ulDbId;
    viewArgs.usRelNo = usRelNo;  // 0xFFFF为所有表信息，单独某张表的传表id
    VOS_UINT8 *pucResult = NULL;

    ret = TPC_Sysview(DB_TPC_SYSVIEW_GET_COMMOM_AREA, &viewArgs, &pucResult);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_INFO, "pucResult:\n%s", pucResult);
    // 将结果字符串写入文件
    InfoInFile((char *)pucResult);
    // 共享空间可以查询到表名和表id等信息，校验表名
    char command[1024];
    (void)sprintf_s(command, sizeof(command), "cat check.txt");
    ret = executeCommand(command, v1, v2, v3, v4, v5);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    TPC_FreeSysviewResult(&pucResult);
    return ret;
}
#pragma pack(1)
typedef struct TagTbl64BDefT {
    uint32_t f0;
    int8_t f1;
    double f2;
    int16_t f3;
    char f4[6];
    uint8_t f5[2];
    uint8_t f6[4];
    uint32_t f7;
    uint8_t f8[6];
    uint8_t f9[5];
    uint8_t f10[3];
    uint8_t f11[3];
} Tbl64BDefT;
#pragma pack()

void SetTbl64BValue(Tbl64BDefT *tblField, uint32_t value)
{
    // DBT_UINT32  4字节
    tblField->f0 = value;
    // DBT_SINT8  1字节
    tblField->f1 = value & 0x7F;
    // DBT_DOUBLE  8字节
    tblField->f2 = (double)value;
    // DBT_BCD  定义长度x=3字节, 存储(x+1)/2=2字节
    tblField->f3 = value & 0x7FFF;
    // DBT_STRING  定义长度x=5字节, 存储x+1=6字节
    (void)snprintf((char *)tblField->f4, sizeof(tblField->f4), "a%04d", value);
    // DBT_BLOCK 定义长度x=2字节, 存储x=2字节
    (void)snprintf((char *)tblField->f5, sizeof(tblField->f5), "aa");
    // DBT_DATE 定义长度4字节, 存储4字节
    (void)snprintf((char *)tblField->f6, sizeof(tblField->f6), "bbbb");
    // DBT_IP_ADDRESS 定义长度4字节, 存储4字节
    tblField->f7 = value;
    // DBT_MAC_ADDRESS 定义长度6字节, 存储6字节
    (void)snprintf((char *)tblField->f8, sizeof(tblField->f8), "a%05d", value);
    // DBT_IPV4PREFIX 定义长度5字节, 存储5字节
    (void)snprintf((char *)tblField->f9, sizeof(tblField->f9), "a%04d", value);
    // DBT_TIMEZONE 定义长度3字节, 存储3字节
    (void)snprintf((char *)tblField->f10, sizeof(tblField->f10), "ccc");
    // DBT_VBYTES 定义长度x=1字节, 存储x+2=3字节
    *(uint16_t *)tblField->f11 = 1;
    (void)snprintf((char *)(tblField->f11 + 2), 1, "a%03d", value);
}

// ============================ DB操作
uint32_t DbInsertTbl64B(uint32_t dbId, VOS_UINT16 relId, uint32_t value)
{
    uint32_t tblRecLen = 0;
    int ret = TestDBGetTblRecLen(dbId, relId, &tblRecLen);
    RETURN_IFERR(ret);
    if (tblRecLen != 48) {
        V1_AW_MACRO_EXPECT_EQ_INT(48, tblRecLen);
        RETURN_IFERR(T_FAILED);
    }
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);
    Tbl64BDefT tblField = {0};
    SetTbl64BValue(&tblField, value);
    if (tblRecLen != sizeof(tblField)) {
        V1_AW_MACRO_EXPECT_EQ_INT(tblRecLen, sizeof(tblField));
        TEST_V1_FREE(recBuf);
        RETURN_IFERR(T_FAILED);
    }
    (void)memcpy_s(recBuf, tblRecLen, &tblField, tblRecLen);
    uint32_t cdbId = TPC_GLOBAL_CDB;

    DB_DSBUF_STRU dsBuf = {.usRecLen = (uint16_t)tblRecLen,
        .usRecNum = 1,
        .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
    ret = DB_InsertRec(dbId, relId, &dsBuf);
    TEST_V1_FREE(recBuf);
    RETURN_IFERR(ret);
    return DB_SUCCESS_V1;
}

uint32_t DbDeleteTbl64B(uint32_t dbId, VOS_UINT16 relId, uint32_t value, bool hasCond = true)
{
    uint32_t cdbId = TPC_GLOBAL_CDB;
    DB_COND_STRU cond;
    if (hasCond) {
        cond.usCondNum = 1;
        cond.aCond[0].enOp = DB_OP_EQUAL;
        cond.aCond[0].ucFieldId = 0;
        *(uint32_t *)cond.aCond[0].aucValue = value;
    } else {
        cond.usCondNum = 0;
    }
    uint32_t actDelRecNum = 0, expDelRecNum = 1;
    int ret = DB_DeleteRec(dbId, relId, &cond, &actDelRecNum);
    if (ret != DB_SUCCESS_V1) {
        return ret;
    }
    if (hasCond) {
        if (actDelRecNum != expDelRecNum) {
            AW_FUN_Log(LOG_INFO, "expDelRecNum : %u, actDelRecNum : %u", actDelRecNum, expDelRecNum);
            RETURN_IFERR(T_FAILED);
        }
    }
    return DB_SUCCESS_V1;
}

uint32_t DbUpdateTbl64B(uint32_t dbId, VOS_UINT16 relId, uint32_t value)
{
    uint32_t tblRecLen = 0;
    int ret = TestDBGetTblRecLen(dbId, relId, &tblRecLen);
    RETURN_IFERR(ret);
    uint32_t cdbId = TPC_GLOBAL_CDB;
    DB_COND_STRU cond;
    cond.usCondNum = 1;
    cond.aCond[0].enOp = DB_OP_EQUAL;
    cond.aCond[0].ucFieldId = 0;
    *(uint32_t *)cond.aCond[0].aucValue = value;
    DB_FIELDFILTER_STRU fldFilter = {.ucFieldNum = DB_FIELD_ALL};
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);
    Tbl64BDefT tblField = {0};
    SetTbl64BValue(&tblField, value + 1000000);
    (void)memcpy_s(recBuf, tblRecLen, &tblField, tblRecLen);
    DB_DSBUF_STRU udpBuf = {.usRecLen = (uint16_t)tblRecLen,
        .usRecNum = 1,
        .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
    uint32_t actUpdtRecNum = 0, expUpdtRecNum = 1;
    ret = DB_UpdateRec(dbId, relId, &cond, &fldFilter, &udpBuf, &actUpdtRecNum);
    TEST_V1_FREE(recBuf);
    RETURN_IFERR(ret);
    if (actUpdtRecNum != expUpdtRecNum) {
        AW_FUN_Log(LOG_INFO, "expUpdtRecNum : %u, actUpdtRecNum : %u", expUpdtRecNum, actUpdtRecNum);
        RETURN_IFERR(T_FAILED);
    }
    return DB_SUCCESS_V1;
}

uint32_t DbSelectTbl64B(uint32_t dbId, VOS_UINT16 relId, uint32_t value)
{
    uint32_t tblRecLen = 0;  // 每条记录要再加5个字节, 即59(tblRecLen)+5=64
    int ret = TestDBGetTblRecLen(dbId, relId, &tblRecLen);
    RETURN_IFERR(ret);
    uint32_t cdbId = TPC_GLOBAL_CDB;
    DB_COND_STRU cond;
    cond.usCondNum = 1;
    cond.aCond[0].enOp = DB_OP_EQUAL;
    cond.aCond[0].ucFieldId = 0;
    *(uint32_t *)cond.aCond[0].aucValue = value;
    DB_FIELDFILTER_STRU fldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_BUF_STRU buff = {0};
    buff.usRecLen = tblRecLen;
    buff.ulBufLen = tblRecLen;
    buff.ulRecNum = DB_SELECT_ALL;
    buff.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(buff.ulBufLen);
    if (buff.pBuf == NULL) {
        AW_FUN_Log(LOG_INFO, "Malloc unsucc.");
    }
    (void)memset_s(buff.pBuf, buff.ulBufLen, 0x00, buff.ulBufLen);
    ret = DB_SelectAllRecEx(dbId, relId, &cond, &fldFilter, &buff);
    RETURN_IFERR(ret);

    // 校验查询结果
    Tbl64BDefT tblField = {0};
    SetTbl64BValue(&tblField, value);
    ret = memcmp(buff.pBuf, &tblField, tblRecLen);
    TEST_V1_FREE(buff.pBuf);
    RETURN_IFERR(ret);
    return DB_SUCCESS_V1;
}

// ============================ TPC 操作
uint32_t InsertTbl64B(uint32_t dbId, VOS_UINT16 relId, uint32_t value, bool isCdb = false, uint32_t externCdbId = -1)
{
    uint32_t tblRecLen = 0;
    int ret = TestGetTblRecLen(dbId, relId, &tblRecLen);
    RETURN_IFERR(ret);
    if (tblRecLen != 48) {
        V1_AW_MACRO_EXPECT_EQ_INT(48, tblRecLen);  // 每条记录长度 = 数据长度(48) + 固定长度(16), 即48+16=64
        RETURN_IFERR(T_FAILED);
    }
    Tbl64BDefT tblField = {0};
    SetTbl64BValue(&tblField, value);
    if (tblRecLen != sizeof(tblField)) {
        V1_AW_MACRO_EXPECT_EQ_INT(tblRecLen, sizeof(tblField));
        RETURN_IFERR(T_FAILED);
    }
    uint32_t cdbId = TPC_GLOBAL_CDB;
    if (externCdbId != -1) {
        cdbId = (uint32_t)externCdbId;
    }
    if (isCdb) {
        ret = TPC_BeginCDB(dbId, &cdbId);
        RETURN_IFERR(ret);
    }
    DB_DSBUF_STRU dsBuf = {.usRecLen = (uint16_t)tblRecLen,
        .usRecNum = 1,
        .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = (uint8_t *)&tblField}};
    ret = TPC_InsertRec(cdbId, dbId, relId, &dsBuf);
    RETURN_IFERR(ret);
    if (isCdb) {
        ret = TPC_CommitCDB(cdbId);
        RETURN_IFERR(ret);
    }
    return DB_SUCCESS_V1;
}

uint32_t DeleteTbl64B(
    uint32_t dbId, VOS_UINT16 relId, uint32_t value, bool isCdb = false, bool hasCond = true, int32_t externCdbId = -1)
{
    uint32_t cdbId = TPC_GLOBAL_CDB;
    if (externCdbId != -1) {
        cdbId = (uint32_t)externCdbId;
    }
    if (isCdb) {
        int ret = TPC_BeginCDB(dbId, &cdbId);
        RETURN_IFERR(ret);
    }
    DB_COND_STRU cond;
    if (hasCond) {
        cond.usCondNum = 1;
        cond.aCond[0].enOp = DB_OP_EQUAL;
        cond.aCond[0].ucFieldId = 0;
        *(uint32_t *)cond.aCond[0].aucValue = value;
    } else {
        cond.usCondNum = 0;
    }
    uint32_t actDelRecNum = 0, expDelRecNum = 1;
    int ret = TPC_DeleteRec(cdbId, dbId, relId, &cond, &actDelRecNum);
    if (ret != DB_SUCCESS_V1) {
        return ret;
    }
    if (hasCond) {
        if (actDelRecNum != expDelRecNum) {
            AW_FUN_Log(LOG_INFO, "expDelRecNum : %u, actDelRecNum : %u", actDelRecNum, expDelRecNum);
            RETURN_IFERR(T_FAILED);
        }
    }
    if (isCdb) {
        ret = TPC_CommitCDB(cdbId);
        RETURN_IFERR(ret);
    }
    return DB_SUCCESS_V1;
}

uint32_t UpdateTbl64B(uint32_t dbId, VOS_UINT16 relId, uint32_t value, bool isCdb = false, bool isKeyViolation = false,
    uint32_t externCdbId = -1)
{
    uint32_t tblRecLen = 0;
    int ret = TestGetTblRecLen(dbId, relId, &tblRecLen);
    RETURN_IFERR(ret);
    uint32_t cdbId = TPC_GLOBAL_CDB;
    if (externCdbId != -1) {
        cdbId = (uint32_t)externCdbId;
    }
    if (isCdb) {
        ret = TPC_BeginCDB(dbId, &cdbId);
        RETURN_IFERR(ret);
    }
    DB_COND_STRU cond;
    cond.usCondNum = 1;
    cond.aCond[0].enOp = DB_OP_EQUAL;
    cond.aCond[0].ucFieldId = 0;
    *(uint32_t *)cond.aCond[0].aucValue = value;
    DB_FIELDFILTER_STRU fldFilter = {.ucFieldNum = DB_FIELD_ALL};
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);
    Tbl64BDefT tblField = {0};
    if (isKeyViolation) {
        SetTbl64BValue(&tblField, value + 1);
    } else {
        SetTbl64BValue(&tblField, value + 1000);
    }
    (void)memcpy_s(recBuf, tblRecLen, &tblField, tblRecLen);
    DB_DSBUF_STRU udpBuf = {.usRecLen = (uint16_t)tblRecLen,
        .usRecNum = 1,
        .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
    uint32_t actUpdtRecNum = 0, expUpdtRecNum = 1;
    ret = TPC_UpdateRec(cdbId, dbId, relId, &cond, &fldFilter, &udpBuf, &actUpdtRecNum);
    TEST_V1_FREE(recBuf);
    RETURN_IFERR(ret);
    if (actUpdtRecNum != expUpdtRecNum) {
        AW_FUN_Log(LOG_INFO, "expUpdtRecNum : %u, actUpdtRecNum : %u", expUpdtRecNum, actUpdtRecNum);
        RETURN_IFERR(T_FAILED);
    }
    if (isCdb) {
        ret = TPC_CommitCDB(cdbId);
        RETURN_IFERR(ret);
    }
    return DB_SUCCESS_V1;
}

uint32_t SelectTbl64B(uint32_t dbId, VOS_UINT16 relId, uint32_t value, bool isCdb = false, int32_t externCdbId = -1)
{
    uint32_t tblRecLen = 0;
    int ret = TestGetTblRecLen(dbId, relId, &tblRecLen);
    RETURN_IFERR(ret);
    uint32_t cdbId = TPC_GLOBAL_CDB;
    if (externCdbId != -1) {
        cdbId = (uint32_t)externCdbId;
    }
    if (isCdb) {
        ret = TPC_BeginCDB(dbId, &cdbId);
        RETURN_IFERR(ret);
    }
    DB_COND_STRU cond;
    cond.usCondNum = 1;
    cond.aCond[0].enOp = DB_OP_EQUAL;
    cond.aCond[0].ucFieldId = 0;
    *(uint32_t *)cond.aCond[0].aucValue = value;
    DB_FIELDFILTER_STRU fldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_BUF_STRU buff = {0};
    buff.usRecLen = tblRecLen;
    buff.ulBufLen = tblRecLen;
    buff.ulRecNum = DB_SELECT_ALL;
    buff.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(buff.ulBufLen);
    if (buff.pBuf == NULL) {
        AW_FUN_Log(LOG_INFO, "Malloc unsucc.");
    }
    (void)memset_s(buff.pBuf, buff.ulBufLen, 0x00, buff.ulBufLen);
    ret = TPC_SelectAllRecEx(cdbId, dbId, relId, &cond, &fldFilter, &buff);
    if (ret != DB_SUCCESS_V1) {
        TEST_V1_FREE(buff.pBuf);
    }
    RETURN_IFERR(ret);

    // 校验查询结果
    Tbl64BDefT tblField = {0};
    SetTbl64BValue(&tblField, value);
    ret = memcmp(buff.pBuf, &tblField, tblRecLen);
    TEST_V1_FREE(buff.pBuf);
    RETURN_IFERR(ret);
    if (isCdb) {
        ret = TPC_CommitCDB(cdbId);
        RETURN_IFERR(ret);
    }
    return DB_SUCCESS_V1;
}

void DeleteAllData(
    VOS_UINT32 dbId, VOS_UINT16 usRelId, uint32_t data, uint32_t *recNum, DB_OPTYPE_ENUM enOp = DB_OP_LARGEREQUAL)
{
    int ret = 0;
    DB_COND_STRU stCond;
    stCond.usCondNum = 1;
    stCond.aCond[0].enOp = enOp;
    stCond.aCond[0].ucFieldId = 0;
    *(uint32_t *)stCond.aCond[0].aucValue = data;

    uint32_t delRecNum = 0;
    ret = TPC_DeleteRec(TPC_GLOBAL_CDB, dbId, usRelId, &stCond, &delRecNum);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    *recNum = delRecNum;
}
/* =========================== 2049字节 ===================== */
#pragma pack(1)
typedef struct TagTbl2047BDefT {
    uint32_t f0;
    int8_t f1;
    double f2;
    int16_t f3;
    char f4[6];
    uint8_t f5[2];
    uint8_t f6[4];
    uint32_t f7;
    uint8_t f8[6];
    uint8_t f9[5];
    uint8_t f10[3];
    uint8_t f11[1980];
    uint32_t f12;
    uint32_t f13;
} Tbl2047BDefT;
#pragma pack()

// 一条数据长度 = 所有数据长度 + 16字节（固定长度）
void SetTbl2047BValue(Tbl2047BDefT *tblField, uint32_t value)
{
    // DBT_UINT32
    tblField->f0 = value;
    // DBT_SINT8
    tblField->f1 = value & 0x7F;
    // DBT_DOUBLE
    tblField->f2 = (double)value;
    // DBT_BCD  定义长度x=3字节, 存储(x+1)/2=2字节
    tblField->f3 = value & 0x7FFF;
    // DBT_STRING  定义长度x=5字节, 存储x+1=6字节
    (void)snprintf((char *)tblField->f4, sizeof(tblField->f4), "a%04d", value);
    // DBT_BLOCK 定义长度x=2字节, 存储x=2字节
    (void)snprintf((char *)tblField->f5, sizeof(tblField->f5), "aa");
    // DBT_DATE 定义长度4字节, 存储4字节
    (void)snprintf((char *)tblField->f6, sizeof(tblField->f6), "bbbb");
    // DBT_IP_ADDRESS 定义长度4字节, 存储4字节
    tblField->f7 = value;
    // DBT_MAC_ADDRESS 定义长度6字节, 存储6字节
    (void)snprintf((char *)tblField->f8, sizeof(tblField->f8), "a%05d", value);
    // DBT_IPV4PREFIX 定义长度5字节, 存储5字节
    (void)snprintf((char *)tblField->f9, sizeof(tblField->f9), "a%04d", value);
    // DBT_TIMEZONE 定义长度3字节, 存储3字节
    (void)snprintf((char *)tblField->f10, sizeof(tblField->f10), "ccc");
    // DBT_VBYTES 定义长度x=1976字节, 存储x+2=1980字节
    *(uint16_t *)tblField->f11 = 1978;
    (void)snprintf((char *)(tblField->f11 + 2), 4, "a%03d", value);
    // DBT_BIT 定义长度x=10位, 存储4字节
    tblField->f12 = value & 0x3FF;
    // 自定义数据类型 定义长度x=4字节
    tblField->f13 = value;
}

uint32_t InsertTbl2047B(uint32_t dbId, VOS_UINT16 relId, uint32_t value, bool isCdb = false, int32_t externCdbId = -1)
{
    uint32_t tblRecLen = 0;
    int ret = TestGetTblRecLen(dbId, relId, &tblRecLen);
    RETURN_IFERR(ret);
    if (tblRecLen != 2033) {
        V1_AW_MACRO_EXPECT_EQ_INT(2033, tblRecLen);
        RETURN_IFERR(T_FAILED);
    }
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);
    Tbl2047BDefT tblField = {0};
    SetTbl2047BValue(&tblField, value);
    if (tblRecLen != sizeof(tblField)) {
        V1_AW_MACRO_EXPECT_EQ_INT(tblRecLen, sizeof(tblField));
        TEST_V1_FREE(recBuf);
        RETURN_IFERR(T_FAILED);
    }
    (void)memcpy_s(recBuf, tblRecLen, &tblField, tblRecLen);
    uint32_t cdbId = TPC_GLOBAL_CDB;
    if (externCdbId != -1) {
        cdbId = (uint32_t)externCdbId;
    }
    if (isCdb) {
        ret = TPC_BeginCDB(dbId, &cdbId);
        RETURN_IFERR(ret);
    }
    DB_DSBUF_STRU dsBuf = {.usRecLen = (uint16_t)tblRecLen,
        .usRecNum = 1,
        .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
    ret = TPC_InsertRec(cdbId, dbId, relId, &dsBuf);
    TEST_V1_FREE(recBuf);
    RETURN_IFERR(ret);
    if (isCdb) {
        ret = TPC_CommitCDB(cdbId);
        RETURN_IFERR(ret);
    }
    return DB_SUCCESS_V1;
}

uint32_t SelectTbl2047B(uint32_t dbId, VOS_UINT16 relId, uint32_t value, bool isCdb = false, int32_t externCdbId = -1)
{
    uint32_t tblRecLen = 0;
    int ret = TestGetTblRecLen(dbId, relId, &tblRecLen);
    RETURN_IFERR(ret);
    uint32_t cdbId = TPC_GLOBAL_CDB;
    if (externCdbId != -1) {
        cdbId = (uint32_t)externCdbId;
    }
    if (isCdb) {
        ret = TPC_BeginCDB(dbId, &cdbId);
        RETURN_IFERR(ret);
    }
    DB_COND_STRU cond;
    cond.usCondNum = 1;
    cond.aCond[0].enOp = DB_OP_EQUAL;
    cond.aCond[0].ucFieldId = 0;
    *(uint32_t *)cond.aCond[0].aucValue = value;
    DB_FIELDFILTER_STRU fldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_BUF_STRU buff = {0};
    buff.usRecLen = tblRecLen;
    buff.ulBufLen = tblRecLen;
    buff.ulRecNum = DB_SELECT_ALL;
    buff.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(buff.ulBufLen);
    if (buff.pBuf == NULL) {
        AW_FUN_Log(LOG_INFO, "Malloc unsucc.");
    }
    (void)memset_s(buff.pBuf, buff.ulBufLen, 0x00, buff.ulBufLen);
    ret = TPC_SelectAllRecEx(cdbId, dbId, relId, &cond, &fldFilter, &buff);
    RETURN_IFERR(ret);

    // 校验查询结果
    Tbl2047BDefT tblField = {0};
    SetTbl2047BValue(&tblField, value);
    ret = memcmp(buff.pBuf, &tblField, tblRecLen);
    TEST_V1_FREE(buff.pBuf);
    RETURN_IFERR(ret);
    if (isCdb) {
        ret = TPC_CommitCDB(cdbId);
        RETURN_IFERR(ret);
    }
    return DB_SUCCESS_V1;
}

/* =========================== 4097字节 ===================== */
#pragma pack(1)
typedef struct TagTbl4097BDefT {
    uint32_t f0;
    int8_t f1;
    double f2;
    int16_t f3;
    char f4[6];
    uint8_t f5[2];
    uint8_t f6[4];
    uint32_t f7;
    uint8_t f8[6];
    uint8_t f9[5];
    uint8_t f10[3];
    uint8_t f11[4028];
    uint32_t f12;
    uint32_t f13;
} Tbl4097BDefT;
#pragma pack()

void SetTbl4097BValue(Tbl4097BDefT *tblField, uint32_t value)
{
    // DBT_UINT32
    tblField->f0 = value;
    // DBT_SINT8
    tblField->f1 = value & 0x7F;
    // DBT_DOUBLE
    tblField->f2 = (double)value;
    // DBT_BCD  定义长度x=3字节, 存储(x+1)/2=2字节
    tblField->f3 = value & 0x7FFF;
    // DBT_STRING  定义长度x=5字节, 存储x+1=6字节
    (void)snprintf((char *)tblField->f4, sizeof(tblField->f4), "a%04d", value);
    // DBT_BLOCK 定义长度x=2字节, 存储x=2字节
    (void)snprintf((char *)tblField->f5, sizeof(tblField->f5), "aa");
    // DBT_DATE 定义长度4字节, 存储4字节
    (void)snprintf((char *)tblField->f6, sizeof(tblField->f6), "bbbb");
    // DBT_IP_ADDRESS 定义长度4字节, 存储4字节
    tblField->f7 = value;
    // DBT_MAC_ADDRESS 定义长度6字节, 存储6字节
    (void)snprintf((char *)tblField->f8, sizeof(tblField->f8), "a%05d", value);
    // DBT_IPV4PREFIX 定义长度5字节, 存储5字节
    (void)snprintf((char *)tblField->f9, sizeof(tblField->f9), "a%04d", value);
    // DBT_TIMEZONE 定义长度3字节, 存储3字节
    (void)snprintf((char *)tblField->f10, sizeof(tblField->f10), "ccc");
    // DBT_VBYTES 定义长度x=4024字节, 存储x+2=4028字节
    *(uint16_t *)tblField->f11 = 4026;
    (void)snprintf((char *)(tblField->f11 + 2), 4, "a%03d", value);
    // DBT_BIT 定义长度x=10位, 存储4字节
    tblField->f12 = value & 0x3FF;
    // 自定义数据类型 定义长度x=4字节
    tblField->f13 = value;
}

uint32_t InsertTbl4097B(uint32_t dbId, VOS_UINT16 relId, uint32_t value, bool isCdb = false, int32_t externCdbId = -1)
{
    uint32_t tblRecLen = 0;
    int ret = TestGetTblRecLen(dbId, relId, &tblRecLen);
    RETURN_IFERR(ret);
    if (tblRecLen != 4081) {
        V1_AW_MACRO_EXPECT_EQ_INT(4081, tblRecLen);
        RETURN_IFERR(T_FAILED);
    }
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);
    Tbl4097BDefT tblField = {0};
    SetTbl4097BValue(&tblField, value);
    if (tblRecLen != sizeof(tblField)) {
        V1_AW_MACRO_EXPECT_EQ_INT(tblRecLen, sizeof(tblField));
        TEST_V1_FREE(recBuf);
        RETURN_IFERR(T_FAILED);
    }
    (void)memcpy_s(recBuf, tblRecLen, &tblField, tblRecLen);
    uint32_t cdbId = TPC_GLOBAL_CDB;
    if (externCdbId != -1) {
        cdbId = (uint32_t)externCdbId;
    }
    if (isCdb) {
        ret = TPC_BeginCDB(dbId, &cdbId);
        RETURN_IFERR(ret);
    }
    DB_DSBUF_STRU dsBuf = {.usRecLen = (uint16_t)tblRecLen,
        .usRecNum = 1,
        .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
    ret = TPC_InsertRec(cdbId, dbId, relId, &dsBuf);
    TEST_V1_FREE(recBuf);
    RETURN_IFERR(ret);
    if (isCdb) {
        ret = TPC_CommitCDB(cdbId);
        RETURN_IFERR(ret);
    }
    return DB_SUCCESS_V1;
}

uint32_t SelectTbl4097B(uint32_t dbId, VOS_UINT16 relId, uint32_t value, bool isCdb = false, int32_t externCdbId = -1)
{
    uint32_t tblRecLen = 0;
    int ret = TestGetTblRecLen(dbId, relId, &tblRecLen);
    RETURN_IFERR(ret);
    uint32_t cdbId = TPC_GLOBAL_CDB;
    if (externCdbId != -1) {
        cdbId = (uint32_t)externCdbId;
    }
    if (isCdb) {
        ret = TPC_BeginCDB(dbId, &cdbId);
        RETURN_IFERR(ret);
    }
    DB_COND_STRU cond;
    cond.usCondNum = 1;
    cond.aCond[0].enOp = DB_OP_EQUAL;
    cond.aCond[0].ucFieldId = 0;
    *(uint32_t *)cond.aCond[0].aucValue = value;
    DB_FIELDFILTER_STRU fldFilter = {.ucFieldNum = DB_FIELD_ALL};
    DB_BUF_STRU buff = {0};
    buff.usRecLen = tblRecLen;
    buff.ulBufLen = tblRecLen;
    buff.ulRecNum = DB_SELECT_ALL;
    buff.pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(buff.ulBufLen);
    if (buff.pBuf == NULL) {
        AW_FUN_Log(LOG_INFO, "Malloc unsucc.");
    }
    (void)memset_s(buff.pBuf, buff.ulBufLen, 0x00, buff.ulBufLen);
    ret = TPC_SelectAllRecEx(cdbId, dbId, relId, &cond, &fldFilter, &buff);
    RETURN_IFERR(ret);

    // 校验查询结果
    Tbl4097BDefT tblField = {0};
    SetTbl4097BValue(&tblField, value);
    ret = memcmp(buff.pBuf, &tblField, tblRecLen);
    TEST_V1_FREE(buff.pBuf);
    RETURN_IFERR(ret);
    if (isCdb) {
        ret = TPC_CommitCDB(cdbId);
        RETURN_IFERR(ret);
    }
    return DB_SUCCESS_V1;
}

/* =========================== BIT ===================== */
#pragma pack(1)
typedef struct TagTblBitDefT {
    uint32_t f0;
    uint32_t f1;
    uint32_t f2;
    uint32_t f3;
} TblBitDefT;
#pragma pack()

void SetTblBitValue(TblBitDefT *tblField, uint32_t value)
{
    // DBT_BIT 定义长度x=10位, 存储4字节
    tblField->f0 = value & 0x3FF;
    tblField->f1 = value & 0x3FF;
    tblField->f2 = value & 0x3FF;
    tblField->f3 = value & 0x3FF;
}

uint32_t InsertTblBit(uint32_t dbId, VOS_UINT16 relId, uint32_t value, bool isCdb = false, int32_t externCdbId = -1)
{
    uint32_t tblRecLen = 0;
    int ret = TestGetTblRecLen(dbId, relId, &tblRecLen);
    RETURN_IFERR(ret);
    if (tblRecLen != 16) {
        V1_AW_MACRO_EXPECT_EQ_INT(16, tblRecLen);
        RETURN_IFERR(T_FAILED);
    }
    uint8_t *recBuf = (uint8_t *)TEST_V1_MALLOC(tblRecLen);
    (void)memset_s(recBuf, tblRecLen, 0x00, tblRecLen);
    TblBitDefT tblField = {0};
    SetTblBitValue(&tblField, value);
    if (tblRecLen != sizeof(tblField)) {
        V1_AW_MACRO_EXPECT_EQ_INT(tblRecLen, sizeof(tblField));
        TEST_V1_FREE(recBuf);
        RETURN_IFERR(T_FAILED);
    }
    (void)memcpy_s(recBuf, tblRecLen, &tblField, tblRecLen);
    uint32_t cdbId = TPC_GLOBAL_CDB;
    if (externCdbId != -1) {
        cdbId = (uint32_t)externCdbId;
    }
    if (isCdb) {
        ret = TPC_BeginCDB(dbId, &cdbId);
        RETURN_IFERR(ret);
    }
    DB_DSBUF_STRU dsBuf = {.usRecLen = (uint16_t)tblRecLen,
        .usRecNum = 1,
        .StdBuf = {.ulBufLen = tblRecLen, .ulActLen = tblRecLen, .pucData = recBuf}};
    ret = TPC_InsertRec(cdbId, dbId, relId, &dsBuf);
    TEST_V1_FREE(recBuf);
    RETURN_IFERR(ret);
    if (isCdb) {
        ret = TPC_CommitCDB(cdbId);
        RETURN_IFERR(ret);
    }
    return DB_SUCCESS_V1;
}

void TestSetCondVal(
    DB_CONDITEM_STRU *aCond, VOS_UINT8 ucFieldId, DB_OPTYPE_ENUM enOp, VOS_UINT8 *aucValue, VOS_UINT32 valLen)
{
    aCond->ucFieldId = ucFieldId;
    aCond->enOp = enOp;
    memset_s(aCond->aucValue, DB_ELELEN_MAX, 0x00, DB_ELELEN_MAX);
    memcpy_s(aCond->aucValue, DB_ELELEN_MAX, aucValue, valLen);
}
DB_ERR_CODE TestMallocDsBuf(DB_DSBUF_STRU *pstDsBuf, VOS_UINT16 usRecLen, VOS_UINT16 usRecNum, VOS_UINT32 ulBufLen)
{
    pstDsBuf->usRecLen = usRecLen;
    pstDsBuf->usRecNum = usRecNum;
    pstDsBuf->StdBuf.ulBufLen = ulBufLen;
    pstDsBuf->StdBuf.ulActLen = pstDsBuf->StdBuf.ulBufLen;
    pstDsBuf->StdBuf.pucData = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBuf->StdBuf.ulBufLen);
    if (pstDsBuf->StdBuf.pucData == NULL) {
        return -1;
    }
    memset_s(pstDsBuf->StdBuf.pucData, pstDsBuf->StdBuf.ulBufLen, 0x00, pstDsBuf->StdBuf.ulBufLen);

    return DB_SUCCESS_V1;
}

DB_ERR_CODE TestMallocBuf(DB_BUF_STRU *pstBufData, VOS_UINT16 usRecLen, VOS_UINT32 ulRecNum, VOS_UINT32 ulBufLen)
{
    pstBufData->usRecLen = usRecLen;
    pstBufData->ulRecNum = ulRecNum;
    pstBufData->ulBufLen = ulBufLen;
    pstBufData->pBuf = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufData->ulBufLen);
    if (pstBufData->pBuf == NULL) {
        return -1;
    }
    memset_s(pstBufData->pBuf, pstBufData->ulBufLen, 0x00, pstBufData->ulBufLen);

    return DB_SUCCESS_V1;
}
void TestFreeBuf(DB_BUF_STRU *pstBufData)
{
    TEST_V1_FREE(pstBufData->pBuf);
}
void TestFreeDsBuf(DB_DSBUF_STRU *pstDsBuf)
{
    TEST_V1_FREE(pstDsBuf->StdBuf.pucData);
}
#define TEST_ASSERT_PTR_NULL(ptr)             \
    do {                                      \
        if ((ptr) == NULL) {                  \
            V1_AW_MACRO_ASSERT_EQ_INT(-1, 0); \
        }                                     \
    } while (0)
typedef struct tagPvArgT {
    bool isAlreadyMalloc;
    uint32_t callCnt;
    DB_BUF_STRU *bufAddr;
} PvArgT;
DB_BUF_STRU *PfGetBufCallBack(VOS_VOID *pvArg)
{
    PvArgT *pvArgTmp = (PvArgT *)pvArg;

    DB_BUF_STRU *bufAddrTmp = pvArgTmp->bufAddr;
    pvArgTmp->callCnt++;

    // 由外部控制，直接使用外部已设置好的buf
    if (pvArgTmp->isAlreadyMalloc) {
        return bufAddrTmp;
    }
    // 如果长度是0，直接返回NULL
    if (bufAddrTmp->ulBufLen == 0) {
        return NULL;
    }
    // 回调内部进行内存申请
    bufAddrTmp->pBuf = TEST_V1_MALLOC(bufAddrTmp->ulBufLen);
    if (bufAddrTmp->pBuf == NULL) {
        return NULL;
    }

    return bufAddrTmp;
}

uint32_t TestCreateEdge(uint32_t dbId, uint16_t relId1, uint16_t relId2)
{
    uint16_t fieldNum = 1;
    uint8_t fieldId[DB_IDX_FLD_MAX] = {11};
    DB_EDGE_DEF_STRU edgeDef = {0};
    edgeDef.isPersistent = true;
    edgeDef.relInfos[0].relId = relId1;
    edgeDef.relInfos[0].FieldNum = fieldNum;
    for (uint16_t i = 0; i < fieldNum; i++) {
        edgeDef.relInfos[0].aucField[i] = fieldId[i];
    }
    edgeDef.relInfos[1].relId = relId2;
    edgeDef.relInfos[1].FieldNum = fieldNum;
    for (uint16_t i = 0; i < fieldNum; i++) {
        edgeDef.relInfos[1].aucField[i] = fieldId[i];
    }
    const char *edgeName = "edge";
    int ret = TPC_CreateEdge(dbId, (uint8_t *)edgeName, &edgeDef);
    RETURN_IFERR(ret);
    return DB_SUCCESS_V1;
}

uint32_t SelectEdge(uint32_t dbId, uint16_t relId1, uint16_t relId2)
{
    DB_COND_STRU cond = {0};
    cond.usCondNum = 0;
    DB_FIELDFILTER_STRU fldFilter = {0};
    fldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_PATH_STRU path = {0};
    path.edgeNum = 1;

    uint16_t fieldNum = 1;
    uint8_t fieldId[DB_IDX_FLD_MAX] = {11};
    path.edge[0].edgeInfo[0].relId = relId1;
    path.edge[0].edgeInfo[0].FieldNum = fieldNum;
    for (uint16_t i = 0; i < fieldNum; i++) {
        path.edge[0].edgeInfo[0].aucField[i] = fieldId[i];
    }
    path.edge[0].edgeInfo[1].relId = relId2;
    path.edge[0].edgeInfo[1].FieldNum = fieldNum;
    for (uint16_t i = 0; i < fieldNum; i++) {
        path.edge[0].edgeInfo[1].aucField[i] = fieldId[i];
    }
    path.edge[0].pstFldFilter = &fldFilter;
    DB_MUTIL_BUF_STRU *buff = NULL;
    int ret = TPC_SelectAllRecByPath(TPC_GLOBAL_CDB, dbId, relId1, &cond, &fldFilter, &path, &buff);
    RETURN_IFERR(ret);
    V1_AW_MACRO_EXPECT_EQ_INT(2, buff->realRelNum);
    return DB_SUCCESS_V1;
}

static void SetPathInfoToSingle(DB_EDGE_CON_STRU *nextedge, uint16_t labelId1, uint16_t labelId2,
    DB_FIELDFILTER_STRU *pstFldFilter, uint32_t fieldNum, uint8_t *field1, uint8_t *field2)
{
    nextedge->edgeInfo[0].relId = labelId1;
    nextedge->edgeInfo[0].FieldNum = fieldNum;
    for (uint32_t j = 0; j < fieldNum; j++) {
        nextedge->edgeInfo[0].aucField[j] = field1[j];
    }
    nextedge->edgeInfo[1].relId = labelId2;
    nextedge->edgeInfo[1].FieldNum = fieldNum;
    for (uint32_t j = 0; j < fieldNum; j++) {
        nextedge->edgeInfo[1].aucField[j] = field2[j];
    }
    nextedge->pstFldFilter = pstFldFilter;
}

typedef struct TagThrParaT {
    int threadId;
    uint32_t dbId;
    uint16_t relId;
} ThrParaT;

void *ThreadInsert(void *arg)
{
    AW_FUN_Log(LOG_STEP, "ThreadInsert start.");
    ThrParaT *para = (ThrParaT *)arg;
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE - (REC_NUM_PER_PAGE / pageNum);
    for (int i = 0; i < 10; i++) {
        for (int k = 0; k < insertNum; k++) {
            ret = InsertTbl64B(para->dbId, para->relId, k, false);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }
    AW_FUN_Log(LOG_STEP, "ThreadInsert stop.");
    return NULL;
}
void *ThreadDelete(void *arg)
{
    AW_FUN_Log(LOG_STEP, "ThreadDelete start.");
    ThrParaT *para = (ThrParaT *)arg;
    int ret;
    uint32_t cnt = 0;
    for (int i = 0; i < 10000; i++) {
        DeleteAllData(para->dbId, para->relId, 0, &cnt);
        V1_AW_MACRO_EXPECT_GE_INT(cnt, 0);
    }
    AW_FUN_Log(LOG_STEP, "ThreadDelete stop.");
    return NULL;
}

void *ThreadDmlRound(void *arg)
{
    AW_FUN_Log(LOG_STEP, "ThreadDmlRound start.");
    ThrParaT *para = (ThrParaT *)arg;
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;                    // 32条数据，十六分之一个页的数据,2K
    int addNum = REC_NUM_PER_PAGE - (REC_NUM_PER_PAGE / pageNum);  // 480条数据，约十六分之十五个页的数据,30K
    for (int j = 0; j < 200; j++) {
        // 更新
        for (int i = 0; i < insertNum; i++) {
            ret = UpdateTbl64B(para->dbId, para->relId, i, false);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        // 查询
        ret = TestTPC_GetRelActRec(para->dbId, para->relId, insertNum);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        // 删除
        for (int i = 0; i < insertNum; i++) {
            ret = DeleteTbl64B(para->dbId, para->relId, i + 1000, false);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        // 写入
        for (int i = 0; i < insertNum; i++) {
            ret = InsertTbl64B(para->dbId, para->relId, i, false);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }
    AW_FUN_Log(LOG_STEP, "ThreadDmlRound stop.");
    return NULL;
}
// 表数据写入大于7/8个页
void *ThreadInsertThreshold(void *arg)
{
    AW_FUN_Log(LOG_STEP, "ThreadInsertThreshold start.");
    ThrParaT *para = (ThrParaT *)arg;
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;                    // 32条数据，1/16个页的数据,2K
    int addNum = REC_NUM_PER_PAGE - (REC_NUM_PER_PAGE / pageNum);  // 480条数据，15/16个页的数据,30K
    for (int j = 0; j < 200; j++) {
        // 对表1进行写入操作，写入数据大于7/8个页
        for (int i = insertNum; i < addNum; i++) {
            ret = InsertTbl64B(para->dbId, para->relId, i, false);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        if (j % 100 == 0) {
            // TPC_Sysview查询和表明检验，预期表在独立空间
            if (j == 0) {
                ret = TestSysviewAndCheckTbl(g_dbId, para->relId, "common area details:table nums: 3",
                    "index:2, table id: 2, table name: tableC, record num: 480, record len: 48, state: 1");
                V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            } else {
                ret = TestSysviewAndCheckTbl(
                    g_dbId, para->relId, "common area details:table nums: 2, total data size: 4736");
                V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            }
        }
        ret = TestTPC_GetRelActRec(para->dbId, para->relId, addNum);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = insertNum; i < addNum; i++) {
            ret = SelectTbl64B(para->dbId, para->relId, i, false);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }

        for (int i = insertNum; i < addNum; i++) {
            ret = UpdateTbl64B(para->dbId, para->relId, i, false);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }

        // 删除数据至低于1/8个页
        for (int i = insertNum; i < addNum; i++) {
            ret = DeleteTbl64B(para->dbId, para->relId, i + 1000, false);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }
    AW_FUN_Log(LOG_STEP, "ThreadInsertThreshold stop.");
    return NULL;
}

// 表数据删除小于1/8个页
void *ThreadDeleteThreshold(void *arg)
{
    AW_FUN_Log(LOG_STEP, "ThreadDeleteThreshold start.");
    ThrParaT *para = (ThrParaT *)arg;
    int ret;
    int pageNum = 16;
    int insertNum = REC_NUM_PER_PAGE / pageNum;                    // 32条数据，1/16个页的数据,2K
    int addNum = REC_NUM_PER_PAGE - (REC_NUM_PER_PAGE / pageNum);  // 480条数据，15/16个页的数据,30K
    for (int j = 0; j < 100; j++) {
        // 删除数据至低于1/8个页
        if (j == 0) {
            for (int i = insertNum; i < addNum; i++) {
                ret = DeleteTbl64B(para->dbId, para->relId, i, false);
                V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            }
        } else {
            for (int i = insertNum; i < addNum; i++) {
                ret = DeleteTbl64B(para->dbId, para->relId, i + 1000, false);
                V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
            }
        }
        ret = TestTPC_GetRelActRec(para->dbId, para->relId, insertNum);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = 0; i < insertNum; i++) {
            ret = SelectTbl64B(para->dbId, para->relId, i, false);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }

        // 对表1进行写入操作，写入数据大于7/8个页
        for (int i = insertNum; i < addNum; i++) {
            ret = InsertTbl64B(para->dbId, para->relId, i, false);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
        ret = TestTPC_GetRelActRec(para->dbId, para->relId, addNum);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        for (int i = insertNum; i < addNum; i++) {
            ret = SelectTbl64B(para->dbId, para->relId, i, false);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }

        for (int i = insertNum; i < addNum; i++) {
            ret = UpdateTbl64B(para->dbId, para->relId, i, false);
            V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        }
    }
    AW_FUN_Log(LOG_STEP, "ThreadDeleteThreshold stop.");
    return NULL;
}
#endif
