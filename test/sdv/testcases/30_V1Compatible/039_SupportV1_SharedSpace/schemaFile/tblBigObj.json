{"tblName": "tblBigObj", "config": {"tblType": "DB_TABLE_NORMAL", "tblIntialSize": 0, "tblMaxRecCnt": 10000000, "tblFieldNum": 13, "tblKeyNum": 2}, "fields": [{"name": "F0", "type": "DBT_UINT32", "size": 4}, {"name": "F1", "type": "DBT_SINT8", "size": 1}, {"name": "F2", "type": "DBT_DOUBLE", "size": 8}, {"name": "F3", "type": "DBT_BCD", "size": 3}, {"name": "F4", "type": "DBT_STRING", "size": 5}, {"name": "F5", "type": "DBT_BLOCK", "size": 2}, {"name": "F6", "type": "DBT_DATE", "size": 4}, {"name": "F7", "type": "DBT_IP_ADDRESS", "size": 4}, {"name": "F8", "type": "DBT_MAC_ADDRESS", "size": 6}, {"name": "F9", "type": "DBT_IPV4PREFIX", "size": 5}, {"name": "F10", "type": "DBT_TIMEZONE", "size": 3}, {"name": "F11", "type": "DBT_VBYTES", "size": 32766}, {"name": "F12", "type": "DBT_BIT", "size": 4}], "keys": [{"name": "index0", "type": "DBDDL_INDEXTYPE_TTREE", "unique": true, "fields": ["F0"]}, {"name": "index1", "type": "DBDDL_INDEXTYPE_TTREE", "unique": false, "fields": ["F11"]}]}