/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 Description  : TTree索引优化重构
 Notes        : 功能测试
 Author       : nonglibin nWX860399
 Modification :
 create       : 2025/07/01
**************************************************************************** */
#include "TTreeIndexOptimization.h"

class TTreeIndexOptimization : public testing::Test {
protected:
    static void SetUpTestCase(){};
    static void TearDownTestCase(){};
public:
    virtual void SetUp();
    virtual void TearDown();
};
void TTreeIndexOptimization::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    // 用于测试用例在设备失败的时候可以设置环境变量来打印一些具体的信息
    g_testcaseDebugModeVal = getenv("TESTCASE_DEBUG_MODE");
    if (g_testcaseDebugModeVal == NULL) {
        AW_FUN_Log(LOG_INFO, "if you want to printf testcase info, you can 'export TESTCASE_DEBUG_MODE=1'.");
    }
}
void TTreeIndexOptimization::TearDown()
{
    AW_CHECK_LOG_END();
}

// 001、修改配置文件maxSeMem超过4G，TPC_Init失败
TEST_F(TTreeIndexOptimization, V1Com_040_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

#if (!CPU_BIT_32)
    // 修改配置文件
    ret = system("sh $TEST_HOME/tools/modifyCfg.sh maxSeMem=4097");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = system("sh $TEST_HOME/tools/modifyCfg.sh maxSysShmSize=12");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = system("sh $TEST_HOME/tools/modifyCfg.sh maxTotalShmSize=5120");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 初始化失败
    ret = TestTPC_Init();
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_MEMALLOCFAILURE, ret);

    // 恢复配置
    ret = system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
#endif

#if (CPU_BIT_32)
    // 修改配置文件
    ret = system("sh $TEST_HOME/tools/modifyCfg.sh maxSeMem=3048");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = system("sh $TEST_HOME/tools/modifyCfg.sh maxSysShmSize=12");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = system("sh $TEST_HOME/tools/modifyCfg.sh maxTotalShmSize=3072");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
#else
    // 修改配置文件
    ret = system("sh $TEST_HOME/tools/modifyCfg.sh maxSeMem=4096");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = system("sh $TEST_HOME/tools/modifyCfg.sh maxSysShmSize=12");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = system("sh $TEST_HOME/tools/modifyCfg.sh maxTotalShmSize=5120");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
#endif

    // 初始化成功
    ret = TestTPC_Init();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 反初始化
    ret = TestTPC_UnInit();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 恢复配置
    ret = system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

#if (CPU_BIT_32)
    // 修改配置文件
    ret = system("sh $TEST_HOME/tools/modifyCfg.sh maxSeMem=3047");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = system("sh $TEST_HOME/tools/modifyCfg.sh maxSysShmSize=12");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = system("sh $TEST_HOME/tools/modifyCfg.sh maxTotalShmSize=3072");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
#else
    // 修改配置文件
    ret = system("sh $TEST_HOME/tools/modifyCfg.sh maxSeMem=4095");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = system("sh $TEST_HOME/tools/modifyCfg.sh maxSysShmSize=12");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = system("sh $TEST_HOME/tools/modifyCfg.sh maxTotalShmSize=5120");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
#endif

    // 初始化成功
    ret = TestTPC_Init();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 反初始化
    ret = TestTPC_UnInit();
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // 恢复配置
    ret = system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002、4G配置导出
TEST_F(TTreeIndexOptimization, V1Com_040_4G_export)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 初始化
    ret = TestTPC_Init();
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据
    ret = TestCreateCustomDataType(50);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCreateCustomDataType(51);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCreateCustomDataType(52);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建DB
    uint32_t dbId = 0;
    const char dbName[DB_NAME_LEN] = "TTreeIdexDb";
    ret = TPC_CreateDB((VOS_UINT8 *)dbName, NULL, &g_dbCfg);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &dbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表
    VOS_UINT16 pusRelId1;
    DB_REL_DEF_STRU pstRelDef1;
    char tblName1[] = "TTreeIdex1";
    ret = TestTPC_CreateTbl(dbId, "schema_file/default.json", &pusRelId1, &pstRelDef1, tblName1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取表的记录长度
    uint32_t recLen1 = 0;
    ret = TestGetTblRecLen(dbId, pusRelId1, &recLen1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入数据
    ret = TestTPC_InsertRec(TPC_GLOBAL_CDB, dbId, pusRelId1, recLen1, &pstRelDef1, 1, 10000);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 用于查询数据
    DB_DSBUF_STRU pstDsBuf;
    ret = TestMallocDsBuf(&pstDsBuf, recLen1, 10000, recLen1 * 10000);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验数据
    for (uint32_t i = 1; i < 10000; i++) {
        ret = TestTPC_SelectRec(TPC_GLOBAL_CDB, dbId, pusRelId1, recLen1, i, &pstDsBuf);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        V1_AW_MACRO_ASSERT_EQ_INT(1, pstDsBuf.usRecNum);
        ret = TestCheckDsBuffVal(pstRelDef1.pstFldLst, pstRelDef1.ulNCols, recLen1, &pstDsBuf, i, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 建表
    VOS_UINT16 pusRelId2;
    DB_REL_DEF_STRU pstRelDef2;
    char tblName2[] = "TTreeIdex2";
    ret = TestTPC_CreateTbl(dbId, "schema_file/defaultNotIndex.json", &pusRelId2, &pstRelDef2, tblName2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取表的记录长度
    uint32_t recLen2 = 0;
    ret = TestGetTblRecLen(dbId, pusRelId2, &recLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入数据
    ret = TestTPC_InsertRec(TPC_GLOBAL_CDB, dbId, pusRelId2, recLen2, &pstRelDef2, 1, 10000);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 校验数据
    for (uint32_t i = 1; i < 10000; i++) {
        ret = TestTPC_SelectRec(TPC_GLOBAL_CDB, dbId, pusRelId2, recLen2, i, &pstDsBuf);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        V1_AW_MACRO_ASSERT_EQ_INT(1, pstDsBuf.usRecNum);
        ret = TestCheckDsBuffVal(pstRelDef2.pstFldLst, pstRelDef2.ulNCols, recLen2, &pstDsBuf, i, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 导出数据
    char filePath[] = "TTreeIndexDbExp.txt";
    ret = TPC_BkpPhy(dbId, (uint8_t *)filePath);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 释放内存
    TestFreeDsBuf(&pstDsBuf);

    // 删表
    TestFreeTblStructDef(&pstRelDef1);
    ret = TPC_DropTbl(dbId, pusRelId1, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeTblStructDef(&pstRelDef2);
    ret = TPC_DropTbl(dbId, pusRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删DB
    ret = TPC_CloseDB(dbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 反初始化
    ret = TestTPC_UnInit();
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002、2G配置导入并操作
TEST_F(TTreeIndexOptimization, V1Com_040_2G_imp_and_dml)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 初始化
    ret = TestTPC_Init();
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 创建自定义数据
    ret = TestCreateCustomDataType(50);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCreateCustomDataType(51);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TestCreateCustomDataType(52);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 导入
    DB_RESTORE_CONFIG_STRU stDbConfig = { .bPersistent = false };
    char filePath[] = "TTreeIndexDbExp.txt";
    const char dbName[DB_NAME_LEN] = "TTreeIdexDb";
    ret = TPC_Restore((VOS_UINT8 *)filePath, (VOS_UINT8 *)dbName, NULL, DB_RESTORETYPE_DISCARD, &stDbConfig, TPC_WAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 打开数据库
    uint32_t dbId = 0;
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)dbName, &dbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 建表,，为了获取pstRelDef1
    VOS_UINT16 pusRelId1;
    DB_REL_DEF_STRU pstRelDef1;
    char tblName10[] = "TTreeIdex10";
    ret = TestTPC_CreateTbl(dbId, "schema_file/default.json", &pusRelId1, &pstRelDef1, tblName10);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取表id
    char tblName1[] = "TTreeIdex1";
    VOS_UINT16 tblId1;
    ret = TPC_GetTblId(dbId, (VOS_UINT8 *)tblName1, &tblId1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取表的记录长度
    VOS_UINT32 tblRecLen1 = 0;
    ret = TestGetTblRecLen(dbId, tblId1, &tblRecLen1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启事务
    uint32_t cdbId = 0;
    ret = TPC_BeginCDB(dbId, &cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入
    ret = TestTPC_InsertRec(cdbId, dbId, tblId1, tblRecLen1, &pstRelDef1, 10001, 20000);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 更新
    ret = TestTPC_UpdateRec(cdbId, dbId, tblId1, tblRecLen1, &pstRelDef1, 1, 20000, 30000);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删除
    ret = TestTPC_DeleteRec(cdbId, dbId, tblId1, 30001, 35000);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 用于查询数据
    DB_DSBUF_STRU pstDsBuf;
    ret = TestMallocDsBuf(&pstDsBuf, tblRecLen1, 20000, tblRecLen1 * 20000);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // cdb查询
    for (uint32_t i = 35001; i < 50000; i++) {
        ret = TestTPC_SelectRec(cdbId, dbId, tblId1, tblRecLen1, i, &pstDsBuf);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        V1_AW_MACRO_ASSERT_EQ_INT(1, pstDsBuf.usRecNum);
        ret = TestCheckDsBuffVal(pstRelDef1.pstFldLst, pstRelDef1.ulNCols, tblRecLen1, &pstDsBuf, i, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 提交事务
    ret =  TPC_CommitCDB(cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // RDB查询
    for (uint32_t i = 35001; i < 50000; i++) {
        ret = TestTPC_SelectRec(TPC_GLOBAL_CDB, dbId, tblId1, tblRecLen1, i, &pstDsBuf);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        V1_AW_MACRO_ASSERT_EQ_INT(1, pstDsBuf.usRecNum);
        ret = TestCheckDsBuffVal(pstRelDef1.pstFldLst, pstRelDef1.ulNCols, tblRecLen1, &pstDsBuf, i, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 建表,，为了获取pstRelDef1
    VOS_UINT16 pusRelId2;
    DB_REL_DEF_STRU pstRelDef2;
    char tblName20[] = "TTreeIdex20";
    ret = TestTPC_CreateTbl(dbId, "schema_file/default.json", &pusRelId2, &pstRelDef2, tblName20);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取表id
    char tblName2[] = "TTreeIdex2";
    VOS_UINT16 tblId2;
    ret = TPC_GetTblId(dbId, (VOS_UINT8 *)tblName2, &tblId2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 获取表的记录长度
    VOS_UINT32 tblRecLen2 = 0;
    ret = TestGetTblRecLen(dbId, tblId2, &tblRecLen2);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 开启事务
    ret = TPC_BeginCDB(dbId, &cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 插入
    ret = TestTPC_InsertRec(cdbId, dbId, tblId2, tblRecLen2, &pstRelDef2, 10001, 20000);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 更新
    ret = TestTPC_UpdateRec(cdbId, dbId, tblId2, tblRecLen2, &pstRelDef2, 1, 20000, 30000);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删除
    ret = TestTPC_DeleteRec(cdbId, dbId, tblId2, 30001, 35000);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // cdb查询
    for (uint32_t i = 35001; i < 50000; i++) {
        ret = TestTPC_SelectRec(cdbId, dbId, tblId2, tblRecLen2, i, &pstDsBuf);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        V1_AW_MACRO_ASSERT_EQ_INT(1, pstDsBuf.usRecNum);
        ret = TestCheckDsBuffVal(pstRelDef2.pstFldLst, pstRelDef2.ulNCols, tblRecLen2, &pstDsBuf, i, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 提交事务
    ret =  TPC_CommitCDB(cdbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // RDB查询
    for (uint32_t i = 35001; i < 50000; i++) {
        ret = TestTPC_SelectRec(TPC_GLOBAL_CDB, dbId, tblId2, tblRecLen2, i, &pstDsBuf);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
        V1_AW_MACRO_ASSERT_EQ_INT(1, pstDsBuf.usRecNum);
        ret = TestCheckDsBuffVal(pstRelDef2.pstFldLst, pstRelDef2.ulNCols, tblRecLen2, &pstDsBuf, i, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    // 释放内存
    TestFreeDsBuf(&pstDsBuf);

    // 删表
    ret = TPC_DropTbl(dbId, tblId1, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropTbl(dbId, tblId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删表
    TestFreeTblStructDef(&pstRelDef1);
    ret = TPC_DropTbl(dbId, pusRelId1, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    TestFreeTblStructDef(&pstRelDef2);
    ret = TPC_DropTbl(dbId, pusRelId2, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 删DB
    ret = TPC_CloseDB(dbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = TPC_DropDB((VOS_UINT8 *)dbName, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    // 反初始化
    ret = TestTPC_UnInit();
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002、修改配置文件maxSeMem等于4G，TPC_Init，写数据后导出得到数据文件，再修改配置文件为2G，重新TPC_Init，进行导入
TEST_F(TTreeIndexOptimization, V1Com_040_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

#if (CPU_BIT_32)
    // 修改配置文件
    ret = system("sh $TEST_HOME/tools/modifyCfg.sh maxSeMem=3048");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = system("sh $TEST_HOME/tools/modifyCfg.sh maxSysShmSize=12");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = system("sh $TEST_HOME/tools/modifyCfg.sh maxTotalShmSize=3072");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
#else
    // 修改配置文件
    ret = system("sh $TEST_HOME/tools/modifyCfg.sh maxSeMem=4096");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = system("sh $TEST_HOME/tools/modifyCfg.sh maxSysShmSize=12");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = system("sh $TEST_HOME/tools/modifyCfg.sh maxTotalShmSize=5120");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
#endif

    // 执行用例
    ret = system("./TTreeIndexOptimization --gtest_filter=*V1Com_040_4G_export > V1Com_040_4G_export.log");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 检查用例的结果
    ret = CheckTestcaseStatus("V1Com_040_4G_export.log");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 恢复配置
    ret = system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

#if (CPU_BIT_32)
    // 修改配置文件
    ret = system("sh $TEST_HOME/tools/modifyCfg.sh maxSeMem=2048");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = system("sh $TEST_HOME/tools/modifyCfg.sh maxSysShmSize=12");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = system("sh $TEST_HOME/tools/modifyCfg.sh maxTotalShmSize=3072");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
#else
    // 修改配置文件
    ret = system("sh $TEST_HOME/tools/modifyCfg.sh maxSeMem=2048");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = system("sh $TEST_HOME/tools/modifyCfg.sh maxSysShmSize=12");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = system("sh $TEST_HOME/tools/modifyCfg.sh maxTotalShmSize=5120");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
#endif

    // 执行用例
    ret = system("./TTreeIndexOptimization --gtest_filter=*V1Com_040_2G_imp_and_dml > V1Com_040_2G_imp_and_dml.log");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 检查用例的结果
    ret = CheckTestcaseStatus("V1Com_040_2G_imp_and_dml.log");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);
    // 恢复配置
    ret = system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
