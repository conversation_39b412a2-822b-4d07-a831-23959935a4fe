#include "V1_DDLCfg.h"

int ret = 0;
class DDL_Constraints : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        #if defined ENV_RTOSV2 && defined CPU_BIT_32
        system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=3072\"");
        #else
        system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=4610\"");
        #endif
        system("sh $TEST_HOME/tools/modifyCfg.sh \"maxNormalTableNum=10000\"");
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestDB_Init());
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    };
    static void TearDownTestCase()
    {
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestDB_UnInit());
    };

public:
    virtual void SetUp();
    virtual void TearDown();
};

void DDL_Constraints::SetUp()
{
    printf("\n====================TEST:BEGIN====================\n");
    AW_CHECK_LOG_BEGIN();
    ret = DB_CreateDB((VOS_UINT8 *)g_testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_OpenDB(NULL, (VOS_UINT8 *)g_testDbName, &g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
void DDL_Constraints::TearDown()
{
    ret = DB_CloseDB(g_testDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)g_testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_CHECK_LOG_END();
    printf("\n=====================TEST:END=====================\n");
}
// 020. CreateDb的DB名字长度大于16字节，预期创建DB失败
TEST_F(DDL_Constraints, V1Com_023_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t testDbId = 0;
    const char testDbName[32] = "testDdl1_testDdl2";
    ret = DB_CreateDB((VOS_UINT8 *)testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DBNAME, ret);
}
// 021.CreateDb的DB名字长度小于16字节，预期创建DB成功
TEST_F(DDL_Constraints, V1Com_023_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t testDbId = 0;
    const char testDbName[16] = "testDdl1_testDd";
    ret = DB_CreateDB((VOS_UINT8 *)testDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 022.CreateDb的Dir大于256字节，预期创建DB失败
TEST_F(DDL_Constraints, V1Com_023_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir -p "
           "cfgPath/Directory0000000001/Directory0000000002/Directory0000000003/Directory0000000004/"
           "Directory0000000005/Directory0000000006/Directory0000000007/Directory0000000008/Directory0000000009/"
           "Directory0000000010/Directory0000000011/Directory0000000012");
    system("touch "
           "cfgPath/Directory0000000001/Directory0000000002/Directory0000000003/Directory0000000004/"
           "Directory0000000005/Directory0000000006/Directory0000000007/Directory0000000008/Directory0000000009/"
           "Directory0000000010/Directory0000000011/Directory0000000012/demo.txt");

    // CreateDB
    char dbName[20] = "dbNameA";  // DB名字
    char dbDir[] =
        "cfgPath/Directory0000000001/Directory0000000002/Directory0000000003/Directory0000000004/Directory0000000005/"
        "Directory0000000006/Directory0000000007/Directory0000000008/Directory0000000009/Directory0000000010/"
        "Directory0000000011/Directory0000000012/demo.txt";
    DB_INST_CONFIG_STRU dbCfg = {0};  // DB的相关数据等配置
    dbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = DB_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    dbCfg.enPersistent = DB_CKP_NONE;
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_DBPATH, ret);
    system("rm -rf ./cfgPath");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 023.CreateDb的Dir小于256字节，预期创建DB成功
TEST_F(DDL_Constraints, V1Com_023_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir -p "
           "cfgPath/Directory0000000001/Directory0000000002/Directory0000000003/Directory0000000004/"
           "Directory0000000005/Directory0000000006/Directory0000000007/Directory0000000008/Directory0000000009/"
           "Directory0000000010/Directory0000000011/Directory0000000012");
    system("touch "
           "cfgPath/Directory0000000001/Directory0000000002/Directory0000000003/Directory0000000004/"
           "Directory0000000005/Directory0000000006/Directory0000000007/Directory0000000008/Directory0000000009/"
           "Directory0000000010/Directory0000000011/Directory0000000012/de.txt");
    // CreateDB
    char dbName[20] = "dbNameA";  // DB名字
    char dbDir[] =
        "cfgPath/Directory0000000001/Directory0000000002/Directory0000000003/Directory0000000004/Directory0000000005/"
        "Directory0000000006/Directory0000000007/Directory0000000008/Directory0000000009/Directory0000000010/"
        "Directory0000000011/Directory0000000012/de.txt";
    DB_INST_CONFIG_STRU dbCfg = {0};  // DB的相关数据等配置
    dbCfg.enPersistent = DB_CKP_COMPLETE;
    // CreateDB
    ret = DB_CreateDB((VOS_UINT8 *)dbName, (VOS_UINT8 *)dbDir, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = DB_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./cfgPath");
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 024.CreateDB的ulMaxDBDesInfoSize取值为0，预期创建DB成功
TEST_F(DDL_Constraints, V1Com_023_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // CreateDB
    char dbName[20] = "dbNameA";      // DB名字
    DB_INST_CONFIG_STRU dbCfg = {0};  // DB的相关数据等配置
    dbCfg.ulMaxDBDescInfoSize = 0;
    ret = DB_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = DB_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 025.CreateDB的ulMaxDBDesInfoSize取值为0xFFFFFFFD，预期创建DB成功
TEST_F(DDL_Constraints, V1Com_023_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // CreateDB
    char dbName[20] = "dbNameA";             // DB名字
    DB_INST_CONFIG_STRU dbCfg = {0};         // DB的相关数据等配置
#if defined ENV_RTOSV2 && defined CPU_BIT_32
    dbCfg.ulMaxDBDescInfoSize = 0x7D000000;  // 最大范围
#else
    dbCfg.ulMaxDBDescInfoSize = 0xFFEFFFFD;  // DB能申请的最大内存
#endif
    ret = DB_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = DB_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 026.CreateDB的ulMaxDBDesInfoSize取值为0xFFFFFFFE，预期创建DB失败
TEST_F(DDL_Constraints, V1Com_023_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // CreateDB
    char dbName[20] = "dbNameA";             // DB名字
    DB_INST_CONFIG_STRU dbCfg = {0};         // DB的相关数据等配置
    dbCfg.ulMaxDBDescInfoSize = 0xFFFFFFFE;  // 全部申请，预期失败
    ret = DB_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDINPUT, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 027.infoSize大于动态内存的APP，预期CreateDB失败
TEST_F(DDL_Constraints, V1Com_023_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // CreateDB
    char dbName[20] = "dbNameA";             // DB名字
    DB_INST_CONFIG_STRU dbCfg = {0};         // DB的相关数据等配置
    dbCfg.ulMaxDBDescInfoSize = 0xFFFFFFFF;  // ulMaxDBDescInfoSize > APP_Size
    ret = DB_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDINPUT, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 028.CreateDBEx的ulMaxDBDesInfoSize取值为0，预期创建DB成功
TEST_F(DDL_Constraints, V1Com_023_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // CreateDB
    char dbName[20] = "dbNameA";      // DB名字
    DB_INST_CONFIG_STRU dbCfg = {0};  // DB的相关数据等配置
    dbCfg.ulMaxDBDescInfoSize = 0;
    ret = DB_CreateDBEx((VOS_UINT8 *)dbName, NULL, &dbCfg, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = DB_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 029.CreateDBEx的ulMaxDBDesInfoSize取值为0xFFFFFFFD，预期创建DB成功
TEST_F(DDL_Constraints, V1Com_023_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // CreateDB
    char dbName[20] = "dbNameA";             // DB名字
    DB_INST_CONFIG_STRU dbCfg = {0};         // DB的相关数据等配置
#if defined ENV_RTOSV2 && defined CPU_BIT_32
    dbCfg.ulMaxDBDescInfoSize = 0x7D000000;  // 最大范围
#else
    dbCfg.ulMaxDBDescInfoSize = 0xFFEFFFFD;  // 最大范围
#endif
    ret = DB_CreateDBEx((VOS_UINT8 *)dbName, NULL, &dbCfg, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DropDB
    ret = DB_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 030.CreateDBEx的ulMaxDBDesInfoSize取值为0xFFFFFFFE，预期创建DB失败
TEST_F(DDL_Constraints, V1Com_023_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // CreateDB
    char dbName[20] = "dbNameA";             // DB名字
    DB_INST_CONFIG_STRU dbCfg = {0};         // DB的相关数据等配置
    dbCfg.ulMaxDBDescInfoSize = 0xFFFFFFFE;  // 超出边界值
    ret = DB_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDINPUT, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 031.CreateDB的enPersistent取值为DB_CKP_NONE，预期创建DB成功
TEST_F(DDL_Constraints, V1Com_023_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // CreateDB
    char dbName[20] = "dbName";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_NONE;
    ret = DB_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 032.CreateDB的enPersistent取值为DB_CKP_COMPLETE，预期创建DB成功
TEST_F(DDL_Constraints, V1Com_023_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("mkdir filePath");
    system("touch filePath/de.txt");
    char dbDir[256] = "./filePath/de.txt";
    uint32_t testDbId = 0;
    const char testDbName[DB_NAME_LEN] = "testDdl1";
    g_testDbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = DB_CreateDB((VOS_UINT8 *)testDbName, (VOS_UINT8 *)dbDir, &g_testDbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    g_testDbCfg.enPersistent = DB_CKP_NONE;
    ret = DB_DropDB((VOS_UINT8 *)testDbName, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    system("rm -rf ./filePath");
}
// 033.CreateDB的enPersistent取值为DB_CKP_INCR，预期创建DB失败
TEST_F(DDL_Constraints, V1Com_023_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // CreateDB
    char dbName[20] = "dbName";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_INCR;
    ret = DB_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 034.CreateDB的enPersistent取值为DB_CKP_BUTT，预期创建DB失败
TEST_F(DDL_Constraints, V1Com_023_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // CreateDB
    char dbName[20] = "dbName";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_BUTT;
    ret = DB_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDINPUT, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 035.CreateDBEx的enPersistent取值为DB_CKP_NONE，预期创建DB成功
TEST_F(DDL_Constraints, V1Com_023_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // CreateDB
    char dbName[20] = "dbName";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_NONE;
    ret = DB_CreateDBEx((VOS_UINT8 *)dbName, NULL, &dbCfg, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 036.CreateDBEx的enPersistent取值为DB_CKP_COMPLETE，预期创建DB失败
TEST_F(DDL_Constraints, V1Com_023_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // CreateDB
    char dbName[20] = "dbName";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_COMPLETE;
    ret = DB_CreateDBEx((VOS_UINT8 *)dbName, NULL, &dbCfg, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 037.CreateDBEx的enPersistent取值为DB_CKP_INCR，预期创建DB失败
TEST_F(DDL_Constraints, V1Com_023_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // CreateDB
    char dbName[20] = "dbName";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_INCR;
    ret = DB_CreateDBEx((VOS_UINT8 *)dbName, NULL, &dbCfg, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_NULLPTR, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 038.CreateDBEx的enPersistent取值为DB_CKP_BUTT，预期创建DB失败
TEST_F(DDL_Constraints, V1Com_023_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // CreateDB
    char dbName[20] = "dbName";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.enPersistent = DB_CKP_BUTT;
    ret = DB_CreateDBEx((VOS_UINT8 *)dbName, NULL, &dbCfg, DB_DATA_STORAGE_RAM);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDINPUT, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 039.ulInitialSize、ulTemSize、ulExtendSize、ulRedoBufSize分别设置1024M，预期创建DB成功
TEST_F(DDL_Constraints, V1Com_023_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // CreateDB
    char dbName[20] = "dbName";
    DB_INST_CONFIG_STRU dbCfg = {0};
    dbCfg.ulInitialSize = 1024;
    dbCfg.ulTempSize = 1024;
    dbCfg.ulExtendSize = 1024;
    dbCfg.ulRedoBufSize = 1024;
    ret = DB_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    ret = DB_DropDB((VOS_UINT8 *)dbName, 1);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 040.创建255个DB，预期第256个失败
TEST_F(DDL_Constraints, V1Com_023_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // CreateDB
    char dbName[20] = {0};            // DB名字
    DB_INST_CONFIG_STRU dbCfg = {0};  // DB的相关数据等配置
    for (uint32_t i = 0; i < 254; i++) {
        (void)sprintf((char *)dbName, "dbName%d", i);
        ret = DB_CreateDB((VOS_UINT8 *)dbName, NULL, &dbCfg);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }

    ret = DB_CreateDB((VOS_UINT8 *)"dbName256", NULL, &dbCfg);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_FAILURE, ret);

    // DropDB
    for (uint32_t i = 0; i < 254; i++) {
        (void)sprintf((char *)dbName, "dbName%d", i);
        ret = DB_DropDB((VOS_UINT8 *)dbName, 1);
        if (ret != DB_SUCCESS_V1) {
            AW_FUN_Log(LOG_ERROR, "共删除%d个DB, ret = %#x", i, ret);
            break;
        }
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 041.建表，建10000张表
TEST_F(DDL_Constraints, V1Com_023_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, NULL);
    const uint32_t tblNum = 10000;
    uint32_t tblIdx[tblNum];
    for (uint32_t i = 0; i < tblNum; i++) {
        tblIdx[i] = i;
    }

    // 建表
    for (uint32_t i = 0; i < tblNum; i++) {
        (void)memset_s(testRelDef.aucRelName, DB_NAME_LEN, 0x00, DB_NAME_LEN);
        (void)sprintf_s((char *)testRelDef.aucRelName, DB_NAME_LEN, "testTable%d", tblIdx[i]);
        ret = DB_CreateTbl(g_testDbId, &testRelDef, &testRelId);
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
        if (ret != DB_SUCCESS_V1) {
            AW_FUN_Log(LOG_ERROR, "第%d个建表失败\n", i);
            break;
        }
    }
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 042.建表，建10001张表
TEST_F(DDL_Constraints, V1Com_023_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDef(&testRelDef, NULL);
    const uint32_t tblNum = 10001;
    uint32_t tblIdx[tblNum];
    for (uint32_t i = 0; i < tblNum; i++) {
        tblIdx[i] = i;
    }

    // 建表
    for (uint32_t i = 0; i < tblNum; i++) {
        (void)memset_s(testRelDef.aucRelName, DB_NAME_LEN, 0x00, DB_NAME_LEN);
        (void)sprintf_s((char *)testRelDef.aucRelName, DB_NAME_LEN, "testTable%d", tblIdx[i]);
        ret = DB_CreateTbl(g_testDbId, &testRelDef, &testRelId);
        if (ret == DB_SUCCESS_V1) {
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, ret);

        } else {
            V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RELDESC_ALLOCFAILURE, ret);
        }
    }
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 043.建表，表定义字段数设置为255
TEST_F(DDL_Constraints, V1Com_023_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU *testRelDef = NULL;
    ret = TestDB_CreateTblById(g_testDbId, "schema_file/vertexlabel2.json", testRelId, testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 044.建表，表定义字段数设置为256
TEST_F(DDL_Constraints, V1Com_023_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU *testRelDef = NULL;
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel3.json", &testRelId, testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_FIELDNUM, ret);
}
// 045.建表，表定义索引数量设置为255
TEST_F(DDL_Constraints, V1Com_023_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU *testRelDef = NULL;
    ret = TestDB_CreateTblById(g_testDbId, "schema_file/vertexlabel4.json", testRelId, testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 046.建表，表定义索引数量设置为256
TEST_F(DDL_Constraints, V1Com_023_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU *testRelDef = NULL;
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel5.json", &testRelId, testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_TOOMANYINDICES, ret);
}
// 047.建表，索引定义中单个索引包含的字段数为0
TEST_F(DDL_Constraints, V1Com_023_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    uint8_t ucIdxFldNum = 0;
    TestCreateTblInitRelDefWithIdxFldNum(&testRelDef, ucIdxFldNum);

    // 建表
    ret = DB_CreateTbl(g_testDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 048.建表，索引定义中单个索引包含的字段数为20
TEST_F(DDL_Constraints, V1Com_023_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    uint8_t ucIdxFldNum = 20;
    TestCreateTblInitRelDefWithIdxFldNum(&testRelDef, ucIdxFldNum);

    // 建表
    ret = DB_CreateTblByID(g_testDbId, testRelId, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 049.建表，索引定义中单个索引包含的字段数为21
TEST_F(DDL_Constraints, V1Com_023_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    uint8_t ucIdxFldNum = 21;
    TestCreateTblInitRelDefWithIdxFldNum(&testRelDef, ucIdxFldNum);

    // 建表
    ret = DB_CreateTblByID(g_testDbId, testRelId, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDIDXFLDNUM, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 050.建表，索引定义中单个索引包含的字段size为60k
TEST_F(DDL_Constraints, V1Com_023_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU *testRelDef = NULL;
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel6.json", &testRelId, testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 051.建表，表名长度为16（包含结束符）
TEST_F(DDL_Constraints, V1Com_023_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    const char newTblName[] = "newTableHaveSix";
    TestCreateTblInitRelDef(&testRelDef, newTblName);

    // 建表
    ret = DB_CreateTblByID(g_testDbId, testRelId, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 052.建表，表名长度为17（包含结束符）
TEST_F(DDL_Constraints, V1Com_023_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    const char newTblName[] = "newTableHaveSeve";
    TestCreateTblInitRelDef(&testRelDef, newTblName);

    // 建表
    ret = DB_CreateTblByID(g_testDbId, testRelId, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_RELNAME, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 053.建表，字段名长度为16（包含结束符）
TEST_F(DDL_Constraints, V1Com_023_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    const char *fldName = "F016abcdefghijk";
    const char *fldName1 = "F116abcdefghijk";
    TestCreateTblInitRelDefWithFld(&testRelDef, fldName, fldName1);

    // 建表
    ret = DB_CreateTblByID(g_testDbId, testRelId, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 054.建表，字段名长度为17（包含结束符）
TEST_F(DDL_Constraints, V1Com_023_054)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    const char *fldName = "F017abcdefghijkl";
    const char *fldName1 = "F117abcdefghijkl";
    TestCreateTblInitRelDefWithFld(&testRelDef, fldName, fldName1);

    // 建表
    ret = DB_CreateTblByID(g_testDbId, testRelId, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDFIELDNAME, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 055.建表，表定义ulIntialSize设置为0
TEST_F(DDL_Constraints, V1Com_023_055)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    uint32_t ulIntialSize = 0;
    uint32_t ulMaxSize = 1000;
    TestCreateTblInitRelDefWithNumberOfRecords(&testRelDef, ulIntialSize, ulMaxSize);

    // 建表
    ret = DB_CreateTblByID(g_testDbId, testRelId, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 056.建表，表定义ulIntialSize大于ulMaxSize
TEST_F(DDL_Constraints, V1Com_023_056)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    uint32_t ulIntialSize = 1000;
    uint32_t ulMaxSize = 100;
    TestCreateTblInitRelDefWithNumberOfRecords(&testRelDef, ulIntialSize, ulMaxSize);

    // 建表
    ret = DB_CreateTblByID(g_testDbId, testRelId, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_ERROR_FOR_INC_MAXREC, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 057.建表，表定义ulIntialSize设置为0xffffffff
TEST_F(DDL_Constraints, V1Com_023_057)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    uint32_t ulIntialSize = 0xffffffff;
    uint32_t ulMaxSize = 1000;
    TestCreateTblInitRelDefWithNumberOfRecords(&testRelDef, ulIntialSize, ulMaxSize);

    // 建表
    ret = DB_CreateTblByIDEx(g_testDbId, testRelId, &testRelDef, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_MAX_REC, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 058.建表，表定义ulMaxSize设置为0
TEST_F(DDL_Constraints, V1Com_023_058)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    uint32_t ulIntialSize = 100;
    uint32_t ulMaxSize = 0;
    TestCreateTblInitRelDefWithNumberOfRecords(&testRelDef, ulIntialSize, ulMaxSize);

    // 建表
    ret = DB_CreateTblByIDEx(g_testDbId, testRelId, &testRelDef, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_ERROR_FOR_INC_MAXREC, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 059.建表，表定义ulMaxSize设置为0xffffffff
TEST_F(DDL_Constraints, V1Com_023_059)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    uint32_t ulIntialSize = 100;
    uint32_t ulMaxSize = 0xffffffff;
    TestCreateTblInitRelDefWithNumberOfRecords(&testRelDef, ulIntialSize, ulMaxSize);

    // 建表
    ret = DB_CreateTblByIDEx(g_testDbId, testRelId, &testRelDef, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_ERROR_FOR_INC_MAXREC, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 060.建表，字段（变长）类型的长度合法（左边界）
TEST_F(DDL_Constraints, V1Com_023_060)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 0;
    const uint32_t fldNum = 7;
    DB_REL_DEF_STRU testRelDef = {0};
    uint32_t fldSizes[fldNum] = {1, 1, 1, 1, 1, 1, 1};
    DB_DATATYPE_ENUM_V1 dataTypes[fldNum] = {
        DBT_BCD, DBT_BIT, DBT_BYTES, DBT_STRING, DBT_BLOCK, DBT_MIBSTR, DBT_VBYTES};
    TestCreateTblInitRelDefWithFldOfLength(&testRelDef, dataTypes, fldSizes, fldNum);

    // 建表
    ret = DB_CreateTblByIDEx(g_testDbId, testRelId, &testRelDef, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 061.建表，字段（变长）类型的长度合法（右边界）
TEST_F(DDL_Constraints, V1Com_023_061)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 0;
    const uint32_t fldNum = 1;
    DB_REL_DEF_STRU testRelDef = {0};

    // DBT_BCD
    uint32_t fldSizes[fldNum] = {65535};
    DB_DATATYPE_ENUM_V1 dataTypes[fldNum] = {DBT_BCD};
    TestCreateTblInitRelDefWithFldOfLength(&testRelDef, dataTypes, fldSizes, fldNum);
    ret = DB_CreateTblByIDEx(g_testDbId, testRelId, &testRelDef, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
    ret = DB_DropTbl(g_testDbId, testRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DBT_BIT
    uint32_t fldSizes1[fldNum] = {32};
    DB_DATATYPE_ENUM_V1 dataTypes1[fldNum] = {DBT_BIT};
    TestCreateTblInitRelDefWithFldOfLength(&testRelDef, dataTypes1, fldSizes1, fldNum);
    ret = DB_CreateTblByIDEx(g_testDbId, testRelId, &testRelDef, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
    ret = DB_DropTbl(g_testDbId, testRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DBT_BYTES
    uint32_t fldSizes2[fldNum] = {65533};
    DB_DATATYPE_ENUM_V1 dataTypes2[fldNum] = {DBT_BYTES};
    TestCreateTblInitRelDefWithFldOfLength(&testRelDef, dataTypes2, fldSizes2, fldNum);
    ret = DB_CreateTblByIDEx(g_testDbId, testRelId, &testRelDef, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
    ret = DB_DropTbl(g_testDbId, testRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DBT_STRING
    uint32_t fldSizes3[fldNum] = {65534};
    DB_DATATYPE_ENUM_V1 dataTypes3[fldNum] = {DBT_STRING};
    TestCreateTblInitRelDefWithFldOfLength(&testRelDef, dataTypes3, fldSizes3, fldNum);
    ret = DB_CreateTblByIDEx(g_testDbId, testRelId, &testRelDef, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
    ret = DB_DropTbl(g_testDbId, testRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DBT_BLOCK
    uint32_t fldSizes4[fldNum] = {65535};
    DB_DATATYPE_ENUM_V1 dataTypes4[fldNum] = {DBT_BLOCK};
    TestCreateTblInitRelDefWithFldOfLength(&testRelDef, dataTypes4, fldSizes4, fldNum);
    ret = DB_CreateTblByIDEx(g_testDbId, testRelId, &testRelDef, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
    ret = DB_DropTbl(g_testDbId, testRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DBT_MIBSTR
    uint32_t fldSizes5[fldNum] = {65534};
    DB_DATATYPE_ENUM_V1 dataTypes5[fldNum] = {DBT_MIBSTR};
    TestCreateTblInitRelDefWithFldOfLength(&testRelDef, dataTypes5, fldSizes5, fldNum);
    ret = DB_CreateTblByIDEx(g_testDbId, testRelId, &testRelDef, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
    ret = DB_DropTbl(g_testDbId, testRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DBT_VBYTES
    uint32_t fldSizes6[fldNum] = {65533};
    DB_DATATYPE_ENUM_V1 dataTypes6[fldNum] = {DBT_VBYTES};
    TestCreateTblInitRelDefWithFldOfLength(&testRelDef, dataTypes6, fldSizes6, fldNum);
    ret = DB_CreateTblByIDEx(g_testDbId, testRelId, &testRelDef, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
    ret = DB_DropTbl(g_testDbId, testRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 062.建表，字段（变长）类型的长度非法（左边界）
TEST_F(DDL_Constraints, V1Com_023_062)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 0;
    const uint32_t fldNum = 7;
    DB_REL_DEF_STRU testRelDef = {0};
    uint32_t fldSizes[fldNum] = {0, 0, 0, 0, 0, 0, 0};
    DB_DATATYPE_ENUM_V1 dataTypes[fldNum] = {
        DBT_BCD, DBT_BIT, DBT_BYTES, DBT_STRING, DBT_BLOCK, DBT_MIBSTR, DBT_VBYTES};
    TestCreateTblInitRelDefWithFldOfLength(&testRelDef, dataTypes, fldSizes, fldNum);

    // 建表
    ret = DB_CreateTblByIDEx(g_testDbId, testRelId, &testRelDef, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDFLDLEN, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 063.建表，字段（变长）类型的长度非法（右边界）
TEST_F(DDL_Constraints, V1Com_023_063)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 0;
    const uint32_t fldNum = 1;
    DB_REL_DEF_STRU testRelDef = {0};

    // DBT_BCD
    uint32_t fldSizes[fldNum] = {65536};
    DB_DATATYPE_ENUM_V1 dataTypes[fldNum] = {DBT_BCD};
    TestCreateTblInitRelDefWithFldOfLength(&testRelDef, dataTypes, fldSizes, fldNum);
    ret = DB_CreateTblByIDEx(g_testDbId, testRelId, &testRelDef, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDFLDLEN, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);

    // DBT_BIT
    uint32_t fldSizes1[fldNum] = {33};
    DB_DATATYPE_ENUM_V1 dataTypes1[fldNum] = {DBT_BIT};
    TestCreateTblInitRelDefWithFldOfLength(&testRelDef, dataTypes1, fldSizes1, fldNum);
    ret = DB_CreateTblByIDEx(g_testDbId, testRelId, &testRelDef, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDFLDLEN, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);

    // DBT_BYTES
    uint32_t fldSizes2[fldNum] = {65534};
    DB_DATATYPE_ENUM_V1 dataTypes2[fldNum] = {DBT_BYTES};
    TestCreateTblInitRelDefWithFldOfLength(&testRelDef, dataTypes2, fldSizes2, fldNum);
    ret = DB_CreateTblByIDEx(g_testDbId, testRelId, &testRelDef, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDFLDLEN, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);

    // DBT_STRING
    uint32_t fldSizes3[fldNum] = {65535};
    DB_DATATYPE_ENUM_V1 dataTypes3[fldNum] = {DBT_STRING};
    TestCreateTblInitRelDefWithFldOfLength(&testRelDef, dataTypes3, fldSizes3, fldNum);
    ret = DB_CreateTblByIDEx(g_testDbId, testRelId, &testRelDef, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDFLDLEN, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);

    // DBT_BLOCK
    uint32_t fldSizes4[fldNum] = {65536};
    DB_DATATYPE_ENUM_V1 dataTypes4[fldNum] = {DBT_BLOCK};
    TestCreateTblInitRelDefWithFldOfLength(&testRelDef, dataTypes4, fldSizes4, fldNum);
    ret = DB_CreateTblByIDEx(g_testDbId, testRelId, &testRelDef, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDFLDLEN, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);

    // DBT_MIBSTR
    uint32_t fldSizes5[fldNum] = {65535};
    DB_DATATYPE_ENUM_V1 dataTypes5[fldNum] = {DBT_MIBSTR};
    TestCreateTblInitRelDefWithFldOfLength(&testRelDef, dataTypes5, fldSizes5, fldNum);
    ret = DB_CreateTblByIDEx(g_testDbId, testRelId, &testRelDef, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDFLDLEN, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);

    // DBT_VBYTES
    uint32_t fldSizes6[fldNum] = {65534};
    DB_DATATYPE_ENUM_V1 dataTypes6[fldNum] = {DBT_VBYTES};
    TestCreateTblInitRelDefWithFldOfLength(&testRelDef, dataTypes6, fldSizes6, fldNum);
    ret = DB_CreateTblByIDEx(g_testDbId, testRelId, &testRelDef, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDFLDLEN, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 064.建表，字段类型非法（包含所有非法字段类型）
TEST_F(DDL_Constraints, V1Com_023_064)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDefWithInvalidFld(&testRelDef);

    // 建表
    ret = DB_CreateTblByIDEx(g_testDbId, testRelId, &testRelDef, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDDATATYPE, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 065.建表，字段类型的长度合法（包含所有支持的字段类型）
TEST_F(DDL_Constraints, V1Com_023_065)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    TestCreateTblInitRelDefWithValidFld(&testRelDef);

    // 建表
    ret = DB_CreateTblByIDEx(g_testDbId, testRelId, &testRelDef, NULL);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 066.建表，表定义表类型有效（DB_TABLE_TEMP）
TEST_F(DDL_Constraints, V1Com_023_066)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    DB_TABLE_TYPE_ENUM enTableType = DB_TABLE_TEMP;
    TestCreateTblInitRelDefWithTableType(&testRelDef, enTableType);

    // 建表
    ret = DB_CreateTbl(g_testDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 067.建表，表定义表类型无效（DB_TABLE_BUTT）
TEST_F(DDL_Constraints, V1Com_023_067)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    DB_TABLE_TYPE_ENUM enTableType = DB_TABLE_BUTT;
    TestCreateTblInitRelDefWithTableType(&testRelDef, enTableType);

    // 建表
    ret = DB_CreateTbl(g_testDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_TBLTYPE, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 068.建表，索引名为null
TEST_F(DDL_Constraints, V1Com_023_068)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    const char idxName[] = "idexNameNotNull";
    TestCreateTblInitRelDefWithIdxName(&testRelDef, idxName, NULL);

    // 建表
    ret = DB_CreateTbl(g_testDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 069.建表，索引名长度为16（包含结束符）
TEST_F(DDL_Constraints, V1Com_023_069)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    const char idxName[] = "idexNameHaveSix";
    const char idxName1[] = "idxNameHaveSix2";
    TestCreateTblInitRelDefWithIdxName(&testRelDef, idxName, idxName1);

    // 建表
    ret = DB_CreateTbl(g_testDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 070.建表，索引名长度为17（包含结束符）
TEST_F(DDL_Constraints, V1Com_023_070)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    const char idxName[] = "idexNameHaveSeve";
    const char idxName1[] = "idxNameHaveSeve2";
    TestCreateTblInitRelDefWithIdxName(&testRelDef, idxName, idxName1);

    // 建表
    ret = DB_CreateTbl(g_testDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALID_INDEXNAME, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 071.建表，索引定义中的字段类型非法（DBT_BIT）
TEST_F(DDL_Constraints, V1Com_023_071)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    DBDDL_INDEXTYPE_ENUM enIndexType = DBDDL_INDEXTYPE_BUTT;
    TestCreateTblInitRelDefWithIdxType(&testRelDef, enIndexType);

    // 建表
    ret = DB_CreateTbl(g_testDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDINDEXTYPE, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 072.建表，索引类型合法（DBDDL_INDEXTYPE_HASH = 0，DBDDL_INDEXTYPE_SORT = 1，DBDDL_INDEXTYPE_TTREE = 2）
TEST_F(DDL_Constraints, V1Com_023_072)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    DBDDL_INDEXTYPE_ENUM enIndexType = DBDDL_INDEXTYPE_HASH;
    TestCreateTblInitRelDefWithIdxType(&testRelDef, enIndexType);

    // DBDDL_INDEXTYPE_HASH
    ret = DB_CreateTbl(g_testDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
    ret = DB_DropTbl(g_testDbId, testRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DBDDL_INDEXTYPE_SORT
    enIndexType = DBDDL_INDEXTYPE_SORT;
    TestCreateTblInitRelDefWithIdxType(&testRelDef, enIndexType);
    ret = DB_CreateTbl(g_testDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
    ret = DB_DropTbl(g_testDbId, testRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    // DBDDL_INDEXTYPE_TTREE
    enIndexType = DBDDL_INDEXTYPE_TTREE;
    TestCreateTblInitRelDefWithIdxType(&testRelDef, enIndexType);
    ret = DB_CreateTbl(g_testDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
    ret = DB_DropTbl(g_testDbId, testRelId, 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
}
// 073.建表，索引类型非法（DBDDL_INDEXTYPE_BUTT）
TEST_F(DDL_Constraints, V1Com_023_073)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    DBDDL_INDEXTYPE_ENUM enIndexType = DBDDL_INDEXTYPE_BUTT;
    TestCreateTblInitRelDefWithIdxType(&testRelDef, enIndexType);

    // 建表
    ret = DB_CreateTbl(g_testDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDINDEXTYPE, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 074.建表，设置数据类型为DBT_VSTRING
TEST_F(DDL_Constraints, V1Com_023_074)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint16_t testRelId = 0;
    const uint32_t fldNum = 7;
    DB_REL_DEF_STRU testRelDef = {0};
    uint32_t fldSizes[fldNum] = {1, 1, 1, 1, 1, 1, 1};
    DB_DATATYPE_ENUM_V1 dataTypes[fldNum] = {
        DBT_BCD, DBT_BIT, DBT_BYTES, DBT_STRING, DBT_BLOCK, DBT_MIBSTR, DBT_VSTRING};
    TestCreateTblInitRelDefWithFldOfLength(&testRelDef, dataTypes, fldSizes, fldNum);

    // 建表
    ret = DB_CreateTbl(g_testDbId, &testRelDef, &testRelId);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_INVALIDDATATYPE, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 075.建表，设置表的总size为65535
TEST_F(DDL_Constraints, V1Com_023_075)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel6.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&testRelDef);
}
// 076.建表，设置表的总size为65536
TEST_F(DDL_Constraints, V1Com_023_076)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建表
    uint16_t testRelId1 = 0;
    DB_REL_DEF_STRU testRelDef = {0};
    ret = TestDB_CreateTbl(g_testDbId, "schema_file/vertexlabel7.json", &testRelId1, &testRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(VOS_ERRNO_DB_MAXRECLEN_EXCEEDED, ret);
}
