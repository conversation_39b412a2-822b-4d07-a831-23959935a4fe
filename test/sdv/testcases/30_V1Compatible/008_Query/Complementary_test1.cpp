/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 Description  : V1支持数据查询
 Notes        : 功能测试
 Author       : herui h60035902
 Modification :
 create       : 2024/09/02
**************************************************************************** */
#include "QueryTest1.h"

class Fun_SelectAllRecEx : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_Init());
    };
    static void TearDownTestCase()
    {
        V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestTPC_UnInit());
    };
    

public:
    virtual void SetUp();
    virtual void TearDown();
};

void Fun_SelectAllRecEx::SetUp()
{
    DB_ERR_CODE ret;
    ret = TPC_CreateDB((VOS_UINT8 *)g_queryTestDbName, NULL, &g_testDbCfg);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    ret = TPC_OpenDB(NULL, (VOS_UINT8 *)g_queryTestDbName, &g_queryTestDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
}
void Fun_SelectAllRecEx::TearDown()
{
    DB_ERR_CODE ret;
    ret = TPC_CloseDB(g_queryTestDbId);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    ret = TPC_DropDB((VOS_UINT8 *)g_queryTestDbName, 0);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_CHECK_LOG_END();
}

// TPC_SelectAllRecEx接口测试，设置0个过滤条件
TEST_F(Fun_SelectAllRecEx, V1Com_008_113)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    DB_FIELD_DEF_STRU *pstFldLst;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    VOS_UINT8 ucIdxFldNum[2] = { 1, 2 };
    VOS_UINT8 ucUniqueFlagLst[2] = { 1, 1 };
    VOS_UINT8 aucFieldIDLst[2][DB_IDX_FLD_MAX] = { {0}, {0, 1} };
    CreateV1Label(&pstFldLst, ulNCols, ucIdxFldNum, aucFieldIDLst, ucUniqueFlagLst, &pusRelId);

    // 写数据
    VOS_UINT32 recordCnt = 10;
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 0;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = recordCnt;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum + 1);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt, pstBufDataGet.ulRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstBufDataGet.ulRecNum; i++) {
        TblFieldDataDefT tblFieldDataDefGet = {0};
        testGetAllField(pBufGet + (pstBufDataGet.usRecLen * i), &tblFieldDataDefGet, pstBufDataGet.usRecLen);
        ret = testCheckAllField(&tblFieldDataDefGet, i, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pBufGet);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecEx接口测试，设置条件数量超过18
TEST_F(Fun_SelectAllRecEx, V1Com_008_114)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    DB_FIELD_DEF_STRU *pstFldLst;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    VOS_UINT8 ucIdxFldNum[2] = { 1, 2 };
    VOS_UINT8 ucUniqueFlagLst[2] = { 1, 1 };
    VOS_UINT8 aucFieldIDLst[2][DB_IDX_FLD_MAX] = { {0}, {0, 1} };
    CreateV1Label(&pstFldLst, ulNCols, ucIdxFldNum, aucFieldIDLst, ucUniqueFlagLst, &pusRelId);

    // 写数据
    VOS_UINT32 recordCnt = 10;
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 19;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = recordCnt;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum + 1);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_INVALIDCONDITION, V1ErrCode(ret, VOS_ERRNO_DB_INVALIDCONDITION));
    TEST_V1_FREE(pBufGet);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecEx接口测试，查询条件设置非法的字段id
TEST_F(Fun_SelectAllRecEx, V1Com_008_115)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    DB_FIELD_DEF_STRU *pstFldLst;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    VOS_UINT8 ucIdxFldNum[2] = { 1, 2 };
    VOS_UINT8 ucUniqueFlagLst[2] = { 1, 1 };
    VOS_UINT8 aucFieldIDLst[2][DB_IDX_FLD_MAX] = { {0}, {0, 1} };
    CreateV1Label(&pstFldLst, ulNCols, ucIdxFldNum, aucFieldIDLst, ucUniqueFlagLst, &pusRelId);

    // 写数据
    VOS_UINT32 recordCnt = 10;
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_UINT32 *)pstCond.aCond[0].aucValue = 1;

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = ulNCols;
    // 非法的字段id
    pstFldFilter.aucField[0] = ulNCols + 1;
    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = recordCnt;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum + 1);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_INVALIDFIELD, V1ErrCode(ret, VOS_ERRNO_DB_INVALIDFIELD));
    TEST_V1_FREE(pBufGet);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecEx接口测试，查询结果设置非法的字段id
TEST_F(Fun_SelectAllRecEx, V1Com_008_116)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    DB_FIELD_DEF_STRU *pstFldLst;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    VOS_UINT8 ucIdxFldNum[2] = { 1, 2 };
    VOS_UINT8 ucUniqueFlagLst[2] = { 1, 1 };
    VOS_UINT8 aucFieldIDLst[2][DB_IDX_FLD_MAX] = { {0}, {0, 1} };
    CreateV1Label(&pstFldLst, ulNCols, ucIdxFldNum, aucFieldIDLst, ucUniqueFlagLst, &pusRelId);

    // 写数据
    VOS_UINT32 recordCnt = 10;
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 0;
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_UINT32 *)pstCond.aCond[0].aucValue = 1;

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = 1;
    pstFldFilter.aucField[0] = ulNCols + 1;
    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = recordCnt;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum + 1);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_INVALIDFIELD, V1ErrCode(ret, VOS_ERRNO_DB_INVALIDFIELD));
    TEST_V1_FREE(pBufGet);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecEx接口测试，设置接收条数为0，过滤不到数据
TEST_F(Fun_SelectAllRecEx, V1Com_008_117)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 建表
    DB_REL_DEF_STRU pstRelDef;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    ret = TestTPC_CreateTbl(g_queryTestDbId, "schema_file/test_033.json", &pusRelId, &pstRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 写数据
    VOS_UINT32 recordCnt = 10;
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstRelDef.pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstRelDef.pstFldLst, ulNCols, recordCnt);

    // 查询数据
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 0;
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_UINT32 *)pstCond.aCond[0].aucValue = ulNCols + 1;

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstRelDef.pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = 0;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum + 1);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, V1ErrCode(ret, VOS_ERRNO_DB_RECNOTEXIST));
    TEST_V1_FREE(pBufGet);

    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&pstRelDef);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecEx接口测试，设置接收条数为0，过滤到数据
TEST_F(Fun_SelectAllRecEx, V1Com_008_118)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    DB_FIELD_DEF_STRU *pstFldLst;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    VOS_UINT8 ucIdxFldNum[2] = { 1, 2 };
    VOS_UINT8 ucUniqueFlagLst[2] = { 1, 1 };
    VOS_UINT8 aucFieldIDLst[2][DB_IDX_FLD_MAX] = { {0}, {0, 1} };
    CreateV1Label(&pstFldLst, ulNCols, ucIdxFldNum, aucFieldIDLst, ucUniqueFlagLst, &pusRelId);

    // 写数据
    VOS_UINT32 recordCnt = 10;
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 0;
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_UINT32 *)pstCond.aCond[0].aucValue = 1;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = 0;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum + 1);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    for (VOS_UINT32 i = 0; i < pstBufDataGet.ulBufLen; i++) {
        V1_AW_MACRO_ASSERT_EQ_INT(0, pBufGet[i]);
    }
    pstBufDataGet.pBuf = pBufGet;

    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(0, pstBufDataGet.ulRecNum);
    for (VOS_UINT32 i = 0; i < pstBufDataGet.ulBufLen; i++) {
        V1_AW_MACRO_ASSERT_EQ_INT(0, pBufGet[i]);
    }
    TEST_V1_FREE(pBufGet);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecEx接口测试，设置接收条数为1，过滤不到数据
TEST_F(Fun_SelectAllRecEx, V1Com_008_119)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 建表
    DB_REL_DEF_STRU pstRelDef;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    ret = TestTPC_CreateTbl(g_queryTestDbId, "schema_file/test_033.json", &pusRelId, &pstRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 写数据
    VOS_UINT32 recordCnt = 10;
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstRelDef.pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstRelDef.pstFldLst, ulNCols, recordCnt);

    // 查询数据
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 0;
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_UINT32 *)pstCond.aCond[0].aucValue = ulNCols + 1;

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstRelDef.pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = 1;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, V1ErrCode(ret, VOS_ERRNO_DB_RECNOTEXIST));
    TEST_V1_FREE(pBufGet);

    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&pstRelDef);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecEx接口测试，设置接收条数为1，设置buffLen不足
TEST_F(Fun_SelectAllRecEx, V1Com_008_120)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    DB_FIELD_DEF_STRU *pstFldLst;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    VOS_UINT8 ucIdxFldNum[2] = { 1, 2 };
    VOS_UINT8 ucUniqueFlagLst[2] = { 1, 1 };
    VOS_UINT8 aucFieldIDLst[2][DB_IDX_FLD_MAX] = { {0}, {0, 1} };
    CreateV1Label(&pstFldLst, ulNCols, ucIdxFldNum, aucFieldIDLst, ucUniqueFlagLst, &pusRelId);

    // 写数据
    VOS_UINT32 recordCnt = 10;
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 0;
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_UINT32 *)pstCond.aCond[0].aucValue = 1;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = 1;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum) - 10;
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_BUFNOTENOUGH, V1ErrCode(ret, VOS_ERRNO_DB_BUFNOTENOUGH));
    TEST_V1_FREE(pBufGet);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecEx接口测试，有索引字段，过滤数据多条，但是不要全部数据
TEST_F(Fun_SelectAllRecEx, V1Com_008_121)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    DB_FIELD_DEF_STRU *pstFldLst;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    VOS_UINT8 ucIdxFldNum[2] = { 1, 2 };
    VOS_UINT8 ucUniqueFlagLst[2] = { 0, 0 };
    VOS_UINT8 aucFieldIDLst[2][DB_IDX_FLD_MAX] = { {0}, {0, 1} };
    CreateV1Label(&pstFldLst, ulNCols, ucIdxFldNum, aucFieldIDLst, ucUniqueFlagLst, &pusRelId);

    // 写非唯一数据
    VOS_UINT32 recordCnt = 10;
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    VOS_UINT32 uniqueVal = 1;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllFieldNotUnique(&TblFieldDataDefSet, uniqueVal, uniqueVal);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 0;
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_UINT32 *)pstCond.aCond[0].aucValue = uniqueVal;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = 5;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(5, pstBufDataGet.ulRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstBufDataGet.ulRecNum; i++) {
        TblFieldDataDefT tblFieldDataDefGet = {0};
        testGetAllField(pBufGet + (pstBufDataGet.usRecLen * i), &tblFieldDataDefGet, pstBufDataGet.usRecLen);
        ret = testCheckAllField(&tblFieldDataDefGet, uniqueVal, uniqueVal);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pBufGet);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecEx接口测试，没有索引字段，过滤数据多条，但是不要全部数据
TEST_F(Fun_SelectAllRecEx, V1Com_008_122)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    DB_FIELD_DEF_STRU *pstFldLst;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    VOS_UINT8 ucIdxFldNum[2] = { 1, 2 };
    VOS_UINT8 ucUniqueFlagLst[2] = { 1, 1 };
    VOS_UINT8 aucFieldIDLst[2][DB_IDX_FLD_MAX] = { {0}, {0, 1} };
    CreateV1Label(&pstFldLst, ulNCols, ucIdxFldNum, aucFieldIDLst, ucUniqueFlagLst, &pusRelId);

    // 写数据
    VOS_UINT32 recordCnt = 10;
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    VOS_UINT32 notIndexFieldVal = recordCnt + 1;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i, notIndexFieldVal);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 1;
    pstCond.aCond[0].ucFieldId = 5;
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(int32_t *)pstCond.aCond[0].aucValue = notIndexFieldVal;
    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = 5;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(5, pstBufDataGet.ulRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstBufDataGet.ulRecNum; i++) {
        TblFieldDataDefT tblFieldDataDefGet = {0};
        testGetAllField(pBufGet + (pstBufDataGet.usRecLen * i), &tblFieldDataDefGet, pstBufDataGet.usRecLen);
        ret = testCheckAllField(&tblFieldDataDefGet, i, notIndexFieldVal);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pBufGet);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);
    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// TPC_SelectAllRecEx接口测试，DB_OP_EQUAL, 多个过滤条件，索引字段全部匹配，不按顺序
TEST_F(Fun_SelectAllRecEx, V1Com_008_123)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    DB_FIELD_DEF_STRU *pstFldLst;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    VOS_UINT8 ucIdxFldNum[2] = { 1, 3 };
    VOS_UINT8 ucUniqueFlagLst[2] = { 1, 1 };
    VOS_UINT8 aucFieldIDLst[2][DB_IDX_FLD_MAX] = { {0}, {0, 1, 2} };
    CreateV1Label(&pstFldLst, ulNCols, ucIdxFldNum, aucFieldIDLst, ucUniqueFlagLst, &pusRelId);

    // 写数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_UINT32 recordCnt = rand() % 901 + 100;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u",recordCnt );
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_INT32 opVal = rand() % (recordCnt - 70) + 40;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 4;
    pstCond.aCond[0].ucFieldId = 2;
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_INT32 *)pstCond.aCond[0].aucValue = opVal;
    pstCond.aCond[1].ucFieldId = 0;
    pstCond.aCond[1].enOp = DB_OP_EQUAL;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_UINT32 *)pstCond.aCond[1].aucValue = opVal;
    pstCond.aCond[2].ucFieldId = 1;
    pstCond.aCond[2].enOp = DB_OP_EQUAL;
    memset_s((char *)pstCond.aCond[2].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_UINT32 *)pstCond.aCond[2].aucValue = opVal;
    pstCond.aCond[3].ucFieldId = 5;
    pstCond.aCond[3].enOp = DB_OP_EQUAL;
    memset_s((char *)pstCond.aCond[3].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(uint16_t *)pstCond.aCond[3].aucValue = UINT32_TO_UINT16(opVal);
    testPrintCond(&pstCond, pstFldLst);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = recordCnt;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum + 1);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstBufDataGet.ulRecNum);

    // 获取所有记录的数据
    TblFieldDataDefT tblFieldDataDefGet = {0};
    testGetAllField(pBufGet, &tblFieldDataDefGet, pstBufDataGet.usRecLen);
    ret = testCheckAllField(&tblFieldDataDefGet, opVal, opVal);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    TEST_V1_FREE(pBufGet);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecEx接口测试，DB_OP_EQUAL, 多个过滤条件，索引字段部分匹配，不按顺序
TEST_F(Fun_SelectAllRecEx, V1Com_008_124)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    DB_FIELD_DEF_STRU *pstFldLst;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    VOS_UINT8 ucIdxFldNum[2] = { 1, 3 };
    VOS_UINT8 ucUniqueFlagLst[2] = { 1, 1 };
    VOS_UINT8 aucFieldIDLst[2][DB_IDX_FLD_MAX] = { {0}, {0, 1, 2} };
    CreateV1Label(&pstFldLst, ulNCols, ucIdxFldNum, aucFieldIDLst, ucUniqueFlagLst, &pusRelId);

    // 写数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_UINT32 recordCnt = rand() % 901 + 100;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u",recordCnt );
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_INT32 opVal = rand() % (recordCnt - 70) + 40;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 4;
    pstCond.aCond[0].ucFieldId = 2;
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_INT32 *)pstCond.aCond[0].aucValue = opVal;
    pstCond.aCond[1].ucFieldId = 0;
    pstCond.aCond[1].enOp = DB_OP_EQUAL;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_UINT32 *)pstCond.aCond[1].aucValue = opVal;
    pstCond.aCond[2].ucFieldId = 6;
    pstCond.aCond[2].enOp = DB_OP_EQUAL;
    memset_s((char *)pstCond.aCond[2].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(int16_t *)pstCond.aCond[2].aucValue = UINT32_TO_INT16(opVal);
    pstCond.aCond[3].ucFieldId = 5;
    pstCond.aCond[3].enOp = DB_OP_EQUAL;
    memset_s((char *)pstCond.aCond[3].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(uint16_t *)pstCond.aCond[3].aucValue = UINT32_TO_UINT16(opVal);
    testPrintCond(&pstCond, pstFldLst);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = recordCnt;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum + 1);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;

    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstBufDataGet.ulRecNum);

    // 获取所有记录的数据
    TblFieldDataDefT tblFieldDataDefGet = {0};
    testGetAllField(pBufGet, &tblFieldDataDefGet, pstBufDataGet.usRecLen);
    ret = testCheckAllField(&tblFieldDataDefGet, opVal, opVal);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    TEST_V1_FREE(pBufGet);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecEx接口测试，DB_OP_EQUAL, 多个过滤条件，没有索引字段，只拿部分不按顺序定义的字段
TEST_F(Fun_SelectAllRecEx, V1Com_008_125)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    DB_FIELD_DEF_STRU *pstFldLst;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    VOS_UINT8 ucIdxFldNum[2] = { 1, 3 };
    VOS_UINT8 ucUniqueFlagLst[2] = { 1, 1 };
    VOS_UINT8 aucFieldIDLst[2][DB_IDX_FLD_MAX] = { {0}, {0, 1, 2} };
    CreateV1Label(&pstFldLst, ulNCols, ucIdxFldNum, aucFieldIDLst, ucUniqueFlagLst, &pusRelId);

    // 写数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_UINT32 recordCnt = rand() % 901 + 100;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u",recordCnt );
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_INT32 opVal = rand() % (recordCnt - 70) + 40;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 4;
    pstCond.aCond[0].ucFieldId = 3;
    pstCond.aCond[0].enOp = DB_OP_EQUAL;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(float *)pstCond.aCond[0].aucValue = UINT32_TO_FLOAT(opVal);
    pstCond.aCond[1].ucFieldId = 4;
    pstCond.aCond[1].enOp = DB_OP_EQUAL;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(double *)pstCond.aCond[1].aucValue = UINT32_TO_DOUBLE(opVal);
    pstCond.aCond[2].ucFieldId = 5;
    pstCond.aCond[2].enOp = DB_OP_EQUAL;
    memset_s((char *)pstCond.aCond[2].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(uint16_t *)pstCond.aCond[2].aucValue = UINT32_TO_UINT16(opVal);
    pstCond.aCond[3].ucFieldId = 6;
    pstCond.aCond[3].enOp = DB_OP_EQUAL;
    memset_s((char *)pstCond.aCond[3].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(int16_t *)pstCond.aCond[3].aucValue = UINT32_TO_INT16(opVal);
    testPrintCond(&pstCond, pstFldLst);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = 7;
    pstFldFilter.aucField[0] = 9;
    pstFldFilter.aucField[1] = 0;
    pstFldFilter.aucField[2] = 3;
    pstFldFilter.aucField[3] = 1;
    pstFldFilter.aucField[4] = 4;
    pstFldFilter.aucField[5] = 8;
    pstFldFilter.aucField[6] = 2;

    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetPartFieldRecLen(pstFldLst, pstFldFilter.ucFieldNum, pstFldFilter.aucField);
    pstBufDataGet.ulRecNum = recordCnt;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum + 1);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;

    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstBufDataGet.ulRecNum);

    // 获取所有记录的数据
    TblPartFieldDataDefT TblPartFieldDataDefGet = {0};
    testGetPartField(pBufGet, &TblPartFieldDataDefGet, pstBufDataGet.usRecLen);
    ret = testCheckPartField(&TblPartFieldDataDefGet, opVal, opVal);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    TEST_V1_FREE(pBufGet);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecEx接口测试，DB_OP_LESS, 多个过滤条件，索引字段全部匹配，不按顺序
TEST_F(Fun_SelectAllRecEx, V1Com_008_126)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    DB_FIELD_DEF_STRU *pstFldLst;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    VOS_UINT8 ucIdxFldNum[2] = { 1, 3 };
    VOS_UINT8 ucUniqueFlagLst[2] = { 1, 1 };
    VOS_UINT8 aucFieldIDLst[2][DB_IDX_FLD_MAX] = { {0}, {0, 1, 2} };
    CreateV1Label(&pstFldLst, ulNCols, ucIdxFldNum, aucFieldIDLst, ucUniqueFlagLst, &pusRelId);

    // 写数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_UINT32 recordCnt = rand() % 901 + 100;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u",recordCnt );
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_INT32 opVal = rand() % (recordCnt - 70) + 40;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 4;
    pstCond.aCond[0].ucFieldId = 2;
    pstCond.aCond[0].enOp = DB_OP_LESS;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_INT32 *)pstCond.aCond[0].aucValue = opVal;
    pstCond.aCond[1].ucFieldId = 0;
    pstCond.aCond[1].enOp = DB_OP_LESS;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_UINT32 *)pstCond.aCond[1].aucValue = opVal + 10;
    pstCond.aCond[2].ucFieldId = 1;
    pstCond.aCond[2].enOp = DB_OP_LESS;
    memset_s((char *)pstCond.aCond[2].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_UINT32 *)pstCond.aCond[2].aucValue = opVal + 20;
    pstCond.aCond[3].ucFieldId = 5;
    pstCond.aCond[3].enOp = DB_OP_LESS;
    memset_s((char *)pstCond.aCond[3].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(uint16_t *)pstCond.aCond[3].aucValue = UINT32_TO_UINT16(opVal + 30);
    testPrintCond(&pstCond, pstFldLst);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = recordCnt;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;

    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(opVal, pstBufDataGet.ulRecNum);

    // 获取所有记录的数据
    bool isMatch[pstBufDataGet.ulRecNum] = { 0 };
    for (VOS_UINT32 i = 0; i < pstBufDataGet.ulRecNum; i++) {
        isMatch[i] = false;
    }
    for (VOS_UINT32 i = 0; i < pstBufDataGet.ulRecNum; i++) {
        TblFieldDataDefT tblFieldDataDefGet = {0};
        testGetAllField(pBufGet + (pstBufDataGet.usRecLen * i), &tblFieldDataDefGet, pstBufDataGet.usRecLen);
        for (VOS_UINT32 j = 0; j < pstBufDataGet.ulRecNum; j++) {
            ret = testCheckAllField(&tblFieldDataDefGet, j, j);
            if (ret != DB_SUCCESS_V1) {
                continue;
            }
            isMatch[j] = true;
            break;
        }
    }
    for (VOS_UINT32 i = 0; i < pstBufDataGet.ulRecNum; i++) {
        AW_MACRO_ASSERT_EQ_BOOL(true, isMatch[i]);
    }
    TEST_V1_FREE(pBufGet);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecEx接口测试，DB_OP_LESS, 多个过滤条件，索引字段部分匹配，不按顺序
TEST_F(Fun_SelectAllRecEx, V1Com_008_127)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    DB_FIELD_DEF_STRU *pstFldLst;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    VOS_UINT8 ucIdxFldNum[2] = { 1, 3 };
    VOS_UINT8 ucUniqueFlagLst[2] = { 1, 1 };
    VOS_UINT8 aucFieldIDLst[2][DB_IDX_FLD_MAX] = { {0}, {0, 1, 2} };
    CreateV1Label(&pstFldLst, ulNCols, ucIdxFldNum, aucFieldIDLst, ucUniqueFlagLst, &pusRelId);

    // 写数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_UINT32 recordCnt = rand() % 901 + 100;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u",recordCnt );
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_INT32 opVal = rand() % (recordCnt - 70) + 40;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 4;
    pstCond.aCond[0].ucFieldId = 2;
    pstCond.aCond[0].enOp = DB_OP_LESS;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_INT32 *)pstCond.aCond[0].aucValue = opVal;
    pstCond.aCond[1].ucFieldId = 0;
    pstCond.aCond[1].enOp = DB_OP_LESS;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_UINT32 *)pstCond.aCond[1].aucValue = opVal + 10;
    pstCond.aCond[2].ucFieldId = 6;
    pstCond.aCond[2].enOp = DB_OP_LESS;
    memset_s((char *)pstCond.aCond[2].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(int16_t *)pstCond.aCond[2].aucValue = UINT32_TO_INT16(opVal + 20);
    pstCond.aCond[3].ucFieldId = 5;
    pstCond.aCond[3].enOp = DB_OP_LESS;
    memset_s((char *)pstCond.aCond[3].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(uint16_t *)pstCond.aCond[3].aucValue = UINT32_TO_UINT16(opVal + 30);
    testPrintCond(&pstCond, pstFldLst);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = recordCnt;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum + 1);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(opVal, pstBufDataGet.ulRecNum);

    // 获取所有记录的数据
    bool isMatch[pstBufDataGet.ulRecNum] = { 0 };
    for (VOS_UINT32 i = 0; i < pstBufDataGet.ulRecNum; i++) {
        isMatch[i] = false;
    }
    for (VOS_UINT32 i = 0; i < pstBufDataGet.ulRecNum; i++) {
        TblFieldDataDefT tblFieldDataDefGet = {0};
        testGetAllField(pBufGet + (pstBufDataGet.usRecLen * i), &tblFieldDataDefGet, pstBufDataGet.usRecLen);
        for (VOS_UINT32 j = 0; j < pstBufDataGet.ulRecNum; j++) {
            ret = testCheckAllField(&tblFieldDataDefGet, j, j);
            if (ret != DB_SUCCESS_V1) {
                continue;
            }
            isMatch[j] = true;
            break;
        }
    }
    for (VOS_UINT32 i = 0; i < pstBufDataGet.ulRecNum; i++) {
        AW_MACRO_ASSERT_EQ_BOOL(true, isMatch[i]);
    }
    TEST_V1_FREE(pBufGet);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecEx接口测试，DB_OP_LESS, 多个过滤条件，没有索引字段
TEST_F(Fun_SelectAllRecEx, V1Com_008_128)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    DB_FIELD_DEF_STRU *pstFldLst;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    VOS_UINT8 ucIdxFldNum[2] = { 1, 3 };
    VOS_UINT8 ucUniqueFlagLst[2] = { 1, 1 };
    VOS_UINT8 aucFieldIDLst[2][DB_IDX_FLD_MAX] = { {0}, {0, 1, 2} };
    CreateV1Label(&pstFldLst, ulNCols, ucIdxFldNum, aucFieldIDLst, ucUniqueFlagLst, &pusRelId);

    // 写数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_UINT32 recordCnt = rand() % 901 + 100;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u",recordCnt );
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_INT32 opVal = rand() % (recordCnt - 70) + 40;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 4;
    pstCond.aCond[0].ucFieldId = 3;
    pstCond.aCond[0].enOp = DB_OP_LESS;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(float *)pstCond.aCond[0].aucValue = UINT32_TO_FLOAT(opVal);
    pstCond.aCond[1].ucFieldId = 4;
    pstCond.aCond[1].enOp = DB_OP_LESS;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(double *)pstCond.aCond[1].aucValue = UINT32_TO_DOUBLE(opVal + 10);
    pstCond.aCond[2].ucFieldId = 5;
    pstCond.aCond[2].enOp = DB_OP_LESS;
    memset_s((char *)pstCond.aCond[2].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(uint16_t *)pstCond.aCond[2].aucValue = UINT32_TO_UINT16(opVal + 20);
    pstCond.aCond[3].ucFieldId = 6;
    pstCond.aCond[3].enOp = DB_OP_LESS;
    memset_s((char *)pstCond.aCond[3].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(int16_t *)pstCond.aCond[3].aucValue = UINT32_TO_INT16(opVal + 30);
    testPrintCond(&pstCond, pstFldLst);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = recordCnt;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum + 1);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(opVal, pstBufDataGet.ulRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstBufDataGet.ulRecNum; i++) {
        TblFieldDataDefT tblFieldDataDefGet = {0};
        testGetAllField(pBufGet + (pstBufDataGet.usRecLen * i), &tblFieldDataDefGet, pstBufDataGet.usRecLen);
        ret = testCheckAllField(&tblFieldDataDefGet, i, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pBufGet);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecEx接口测试，DB_OP_LESSEQUAL, 多个过滤条件，索引字段全部匹配，不按顺序
TEST_F(Fun_SelectAllRecEx, V1Com_008_129)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    DB_FIELD_DEF_STRU *pstFldLst;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    VOS_UINT8 ucIdxFldNum[2] = { 1, 3 };
    VOS_UINT8 ucUniqueFlagLst[2] = { 1, 1 };
    VOS_UINT8 aucFieldIDLst[2][DB_IDX_FLD_MAX] = { {0}, {0, 1, 2} };
    CreateV1Label(&pstFldLst, ulNCols, ucIdxFldNum, aucFieldIDLst, ucUniqueFlagLst, &pusRelId);

    // 写数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_UINT32 recordCnt = rand() % 901 + 100;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u",recordCnt );
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_INT32 opVal = rand() % (recordCnt - 70) + 40;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 4;
    pstCond.aCond[0].ucFieldId = 2;
    pstCond.aCond[0].enOp = DB_OP_LESSEQUAL;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_INT32 *)pstCond.aCond[0].aucValue = opVal;
    pstCond.aCond[1].ucFieldId = 0;
    pstCond.aCond[1].enOp = DB_OP_LESSEQUAL;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_UINT32 *)pstCond.aCond[1].aucValue = opVal + 10;
    pstCond.aCond[2].ucFieldId = 1;
    pstCond.aCond[2].enOp = DB_OP_LESSEQUAL;
    memset_s((char *)pstCond.aCond[2].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_UINT32 *)pstCond.aCond[2].aucValue = opVal + 20;
    pstCond.aCond[3].ucFieldId = 5;
    pstCond.aCond[3].enOp = DB_OP_LESSEQUAL;
    memset_s((char *)pstCond.aCond[3].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(uint16_t *)pstCond.aCond[3].aucValue = UINT32_TO_UINT16(opVal + 30);
    testPrintCond(&pstCond, pstFldLst);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = recordCnt;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum + 1);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(opVal + 1, pstBufDataGet.ulRecNum);

    // 获取所有记录的数据
    bool isMatch[pstBufDataGet.ulRecNum] = { 0 };
    for (VOS_UINT32 i = 0; i < pstBufDataGet.ulRecNum; i++) {
        isMatch[i] = false;
    }
    for (VOS_UINT32 i = 0; i < pstBufDataGet.ulRecNum; i++) {
        TblFieldDataDefT tblFieldDataDefGet = {0};
        testGetAllField(pBufGet + (pstBufDataGet.usRecLen * i), &tblFieldDataDefGet, pstBufDataGet.usRecLen);
        for (VOS_UINT32 j = 0; j < pstBufDataGet.ulRecNum; j++) {
            ret = testCheckAllField(&tblFieldDataDefGet, j, j);
            if (ret != DB_SUCCESS_V1) {
                continue;
            }
            isMatch[j] = true;
            break;
        }
    }
    for (VOS_UINT32 i = 0; i < pstBufDataGet.ulRecNum; i++) {
        AW_MACRO_ASSERT_EQ_BOOL(true, isMatch[i]);
    }
    TEST_V1_FREE(pBufGet);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecEx接口测试，DB_OP_LESSEQUAL, 多个过滤条件，索引字段部分匹配，不按顺序
TEST_F(Fun_SelectAllRecEx, V1Com_008_130)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    DB_FIELD_DEF_STRU *pstFldLst;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    VOS_UINT8 ucIdxFldNum[2] = { 1, 3 };
    VOS_UINT8 ucUniqueFlagLst[2] = { 1, 1 };
    VOS_UINT8 aucFieldIDLst[2][DB_IDX_FLD_MAX] = { {0}, {0, 1, 2} };
    CreateV1Label(&pstFldLst, ulNCols, ucIdxFldNum, aucFieldIDLst, ucUniqueFlagLst, &pusRelId);

    // 写数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_UINT32 recordCnt = rand() % 901 + 100;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u",recordCnt );
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_INT32 opVal = rand() % (recordCnt - 70) + 40;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 4;
    pstCond.aCond[0].ucFieldId = 2;
    pstCond.aCond[0].enOp = DB_OP_LESSEQUAL;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_INT32 *)pstCond.aCond[0].aucValue = opVal;
    pstCond.aCond[1].ucFieldId = 0;
    pstCond.aCond[1].enOp = DB_OP_LESSEQUAL;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_UINT32 *)pstCond.aCond[1].aucValue = opVal + 10;
    pstCond.aCond[2].ucFieldId = 6;
    pstCond.aCond[2].enOp = DB_OP_LESSEQUAL;
    memset_s((char *)pstCond.aCond[2].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(int16_t *)pstCond.aCond[2].aucValue = UINT32_TO_INT16(opVal + 20);
    pstCond.aCond[3].ucFieldId = 5;
    pstCond.aCond[3].enOp = DB_OP_LESSEQUAL;
    memset_s((char *)pstCond.aCond[3].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(uint16_t *)pstCond.aCond[3].aucValue = UINT32_TO_UINT16(opVal + 30);
    testPrintCond(&pstCond, pstFldLst);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = recordCnt;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum + 1);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(opVal + 1, pstBufDataGet.ulRecNum);

    // 获取所有记录的数据
    bool isMatch[pstBufDataGet.ulRecNum] = { 0 };
    for (VOS_UINT32 i = 0; i < pstBufDataGet.ulRecNum; i++) {
        isMatch[i] = false;
    }
    for (VOS_UINT32 i = 0; i < pstBufDataGet.ulRecNum; i++) {
        TblFieldDataDefT tblFieldDataDefGet = {0};
        testGetAllField(pBufGet + (pstBufDataGet.usRecLen * i), &tblFieldDataDefGet, pstBufDataGet.usRecLen);
        for (VOS_UINT32 j = 0; j < pstBufDataGet.ulRecNum; j++) {
            ret = testCheckAllField(&tblFieldDataDefGet, j, j);
            if (ret != DB_SUCCESS_V1) {
                continue;
            }
            isMatch[j] = true;
            break;
        }
    }
    for (VOS_UINT32 i = 0; i < pstBufDataGet.ulRecNum; i++) {
        AW_MACRO_ASSERT_EQ_BOOL(true, isMatch[i]);
    }
    TEST_V1_FREE(pBufGet);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecEx接口测试，DB_OP_LESSEQUAL, 多个过滤条件，没有索引字段
TEST_F(Fun_SelectAllRecEx, V1Com_008_131)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    DB_FIELD_DEF_STRU *pstFldLst;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    VOS_UINT8 ucIdxFldNum[2] = { 1, 3 };
    VOS_UINT8 ucUniqueFlagLst[2] = { 1, 1 };
    VOS_UINT8 aucFieldIDLst[2][DB_IDX_FLD_MAX] = { {0}, {0, 1, 2} };
    CreateV1Label(&pstFldLst, ulNCols, ucIdxFldNum, aucFieldIDLst, ucUniqueFlagLst, &pusRelId);

    // 写数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_UINT32 recordCnt = rand() % 901 + 100;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u",recordCnt );
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_INT32 opVal = rand() % (recordCnt - 70) + 40;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 4;
    pstCond.aCond[0].ucFieldId = 3;
    pstCond.aCond[0].enOp = DB_OP_LESSEQUAL;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(float *)pstCond.aCond[0].aucValue = UINT32_TO_FLOAT(opVal);
    pstCond.aCond[1].ucFieldId = 4;
    pstCond.aCond[1].enOp = DB_OP_LESSEQUAL;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(double *)pstCond.aCond[1].aucValue = UINT32_TO_DOUBLE(opVal + 10);
    pstCond.aCond[2].ucFieldId = 5;
    pstCond.aCond[2].enOp = DB_OP_LESSEQUAL;
    memset_s((char *)pstCond.aCond[2].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(uint16_t *)pstCond.aCond[2].aucValue = UINT32_TO_UINT16(opVal + 20);
    pstCond.aCond[3].ucFieldId = 6;
    pstCond.aCond[3].enOp = DB_OP_LESSEQUAL;
    memset_s((char *)pstCond.aCond[3].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(int16_t *)pstCond.aCond[3].aucValue = UINT32_TO_INT16(opVal + 30);
    testPrintCond(&pstCond, pstFldLst);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = recordCnt;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum + 1);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(opVal + 1, pstBufDataGet.ulRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstBufDataGet.ulRecNum; i++) {
        TblFieldDataDefT tblFieldDataDefGet = {0};
        testGetAllField(pBufGet + (pstBufDataGet.usRecLen * i), &tblFieldDataDefGet, pstBufDataGet.usRecLen);
        ret = testCheckAllField(&tblFieldDataDefGet, i, i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pBufGet);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// TPC_SelectAllRecEx接口测试，DB_OP_LARGER, 多个过滤条件，索引字段全部匹配，不按顺序
TEST_F(Fun_SelectAllRecEx, V1Com_008_132)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    DB_FIELD_DEF_STRU *pstFldLst;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    VOS_UINT8 ucIdxFldNum[2] = { 1, 3 };
    VOS_UINT8 ucUniqueFlagLst[2] = { 1, 1 };
    VOS_UINT8 aucFieldIDLst[2][DB_IDX_FLD_MAX] = { {0}, {0, 1, 2} };
    CreateV1Label(&pstFldLst, ulNCols, ucIdxFldNum, aucFieldIDLst, ucUniqueFlagLst, &pusRelId);

    // 写数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_UINT32 recordCnt = rand() % 901 + 100;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u",recordCnt );
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_INT32 opVal = rand() % (recordCnt - 50) + 49;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 4;
    pstCond.aCond[0].ucFieldId = 2;
    pstCond.aCond[0].enOp = DB_OP_LARGER;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_INT32 *)pstCond.aCond[0].aucValue = opVal;
    pstCond.aCond[1].ucFieldId = 0;
    pstCond.aCond[1].enOp = DB_OP_LARGER;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_UINT32 *)pstCond.aCond[1].aucValue = opVal - 10;
    pstCond.aCond[2].ucFieldId = 1;
    pstCond.aCond[2].enOp = DB_OP_LARGER;
    memset_s((char *)pstCond.aCond[2].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_UINT32 *)pstCond.aCond[2].aucValue = opVal - 20;
    pstCond.aCond[3].ucFieldId = 5;
    pstCond.aCond[3].enOp = DB_OP_LARGER;
    memset_s((char *)pstCond.aCond[3].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(uint16_t *)pstCond.aCond[3].aucValue = UINT32_TO_UINT16(opVal - 30);
    testPrintCond(&pstCond, pstFldLst);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = recordCnt;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt - opVal - 1, pstBufDataGet.ulRecNum);

    // 获取所有记录的数据
    bool isMatch[pstBufDataGet.ulRecNum] = { 0 };
    for (VOS_UINT32 i = 0; i < pstBufDataGet.ulRecNum; i++) {
        isMatch[i] = false;
    }
    for (VOS_UINT32 i = 0; i < pstBufDataGet.ulRecNum; i++) {
        TblFieldDataDefT tblFieldDataDefGet = {0};
        testGetAllField(pBufGet + (pstBufDataGet.usRecLen * i), &tblFieldDataDefGet, pstBufDataGet.usRecLen);
        for (VOS_UINT32 j = opVal + 1; j < recordCnt; j++) {
            ret = testCheckAllField(&tblFieldDataDefGet, j, j);
            if (ret != DB_SUCCESS_V1) {
                continue;
            }
            isMatch[j - opVal] = true;
            break;
        }
    }
    for (VOS_UINT32 i = opVal + 1; i < pstBufDataGet.ulRecNum; i++) {
        AW_MACRO_ASSERT_EQ_BOOL(true, isMatch[i - opVal]);
    }
    TEST_V1_FREE(pBufGet);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecEx接口测试，DB_OP_LARGER, 多个过滤条件，索引字段部分匹配，不按顺序
TEST_F(Fun_SelectAllRecEx, V1Com_008_133)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    DB_FIELD_DEF_STRU *pstFldLst;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    VOS_UINT8 ucIdxFldNum[2] = { 1, 3 };
    VOS_UINT8 ucUniqueFlagLst[2] = { 1, 1 };
    VOS_UINT8 aucFieldIDLst[2][DB_IDX_FLD_MAX] = { {0}, {0, 1, 2} };
    CreateV1Label(&pstFldLst, ulNCols, ucIdxFldNum, aucFieldIDLst, ucUniqueFlagLst, &pusRelId);

    // 写数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_UINT32 recordCnt = rand() % 901 + 100;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u",recordCnt );
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_INT32 opVal = rand() % (recordCnt - 50) + 49;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 4;
    pstCond.aCond[0].ucFieldId = 2;
    pstCond.aCond[0].enOp = DB_OP_LARGER;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_INT32 *)pstCond.aCond[0].aucValue = opVal;
    pstCond.aCond[1].ucFieldId = 0;
    pstCond.aCond[1].enOp = DB_OP_LARGER;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_UINT32 *)pstCond.aCond[1].aucValue = opVal - 10;
    pstCond.aCond[2].ucFieldId = 6;
    pstCond.aCond[2].enOp = DB_OP_LARGER;
    memset_s((char *)pstCond.aCond[2].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(int16_t *)pstCond.aCond[2].aucValue = UINT32_TO_INT16(opVal - 20);
    pstCond.aCond[3].ucFieldId = 5;
    pstCond.aCond[3].enOp = DB_OP_LARGER;
    memset_s((char *)pstCond.aCond[3].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(uint16_t *)pstCond.aCond[3].aucValue = UINT32_TO_UINT16(opVal - 30);
    testPrintCond(&pstCond, pstFldLst);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = recordCnt;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum + 1);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;

    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt - opVal - 1, pstBufDataGet.ulRecNum);

    // 获取所有记录的数据
    bool isMatch[pstBufDataGet.ulRecNum] = { 0 };
    for (VOS_UINT32 i = 0; i < pstBufDataGet.ulRecNum; i++) {
        isMatch[i] = false;
    }
    for (VOS_UINT32 i = 0; i < pstBufDataGet.ulRecNum; i++) {
        TblFieldDataDefT tblFieldDataDefGet = {0};
        testGetAllField(pBufGet + (pstBufDataGet.usRecLen * i), &tblFieldDataDefGet, pstBufDataGet.usRecLen);
        for (VOS_UINT32 j = opVal + 1; j < recordCnt; j++) {
            ret = testCheckAllField(&tblFieldDataDefGet, j, j);
            if (ret != DB_SUCCESS_V1) {
                continue;
            }
            isMatch[j - opVal] = true;
            break;
        }
    }
    for (VOS_UINT32 i = opVal + 1; i < pstBufDataGet.ulRecNum; i++) {
        AW_MACRO_ASSERT_EQ_BOOL(true, isMatch[i - opVal]);
    }
    TEST_V1_FREE(pBufGet);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecEx接口测试，DB_OP_LARGER, 多个过滤条件，没有索引字段
TEST_F(Fun_SelectAllRecEx, V1Com_008_134)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    DB_FIELD_DEF_STRU *pstFldLst;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    VOS_UINT8 ucIdxFldNum[2] = { 1, 3 };
    VOS_UINT8 ucUniqueFlagLst[2] = { 1, 1 };
    VOS_UINT8 aucFieldIDLst[2][DB_IDX_FLD_MAX] = { {0}, {0, 1, 2} };
    CreateV1Label(&pstFldLst, ulNCols, ucIdxFldNum, aucFieldIDLst, ucUniqueFlagLst, &pusRelId);

    // 写数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_UINT32 recordCnt = rand() % 901 + 100;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u",recordCnt );
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_INT32 opVal = rand() % (recordCnt - 50) + 49;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 4;
    pstCond.aCond[0].ucFieldId = 3;
    pstCond.aCond[0].enOp = DB_OP_LARGER;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(float *)pstCond.aCond[0].aucValue = UINT32_TO_FLOAT(opVal);
    pstCond.aCond[1].ucFieldId = 4;
    pstCond.aCond[1].enOp = DB_OP_LARGER;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(double *)pstCond.aCond[1].aucValue = UINT32_TO_DOUBLE(opVal - 10);
    pstCond.aCond[2].ucFieldId = 5;
    pstCond.aCond[2].enOp = DB_OP_LARGER;
    memset_s((char *)pstCond.aCond[2].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(uint16_t *)pstCond.aCond[2].aucValue = UINT32_TO_UINT16(opVal - 20);
    pstCond.aCond[3].ucFieldId = 6;
    pstCond.aCond[3].enOp = DB_OP_LARGER;
    memset_s((char *)pstCond.aCond[3].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(int16_t *)pstCond.aCond[3].aucValue = UINT32_TO_INT16(opVal - 30);
    testPrintCond(&pstCond, pstFldLst);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = recordCnt;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum + 1);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt - opVal - 1, pstBufDataGet.ulRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstBufDataGet.ulRecNum; i++) {
        TblFieldDataDefT tblFieldDataDefGet = {0};
        testGetAllField(pBufGet + (pstBufDataGet.usRecLen * i), &tblFieldDataDefGet, pstBufDataGet.usRecLen);
        ret = testCheckAllField(&tblFieldDataDefGet, opVal + i + 1, opVal + i + 1);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pBufGet);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecEx接口测试，DB_OP_LARGEREQUAL, 多个过滤条件，索引字段全部匹配，不按顺序
TEST_F(Fun_SelectAllRecEx, V1Com_008_135)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    DB_FIELD_DEF_STRU *pstFldLst;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    VOS_UINT8 ucIdxFldNum[2] = { 1, 3 };
    VOS_UINT8 ucUniqueFlagLst[2] = { 1, 1 };
    VOS_UINT8 aucFieldIDLst[2][DB_IDX_FLD_MAX] = { {0}, {0, 1, 2} };
    CreateV1Label(&pstFldLst, ulNCols, ucIdxFldNum, aucFieldIDLst, ucUniqueFlagLst, &pusRelId);

    // 写数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_UINT32 recordCnt = rand() % 901 + 100;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u",recordCnt );
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_INT32 opVal = rand() % (recordCnt - 50) + 50;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 4;
    pstCond.aCond[0].ucFieldId = 2;
    pstCond.aCond[0].enOp = DB_OP_LARGEREQUAL;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_INT32 *)pstCond.aCond[0].aucValue = opVal;
    pstCond.aCond[1].ucFieldId = 0;
    pstCond.aCond[1].enOp = DB_OP_LARGEREQUAL;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_UINT32 *)pstCond.aCond[1].aucValue = opVal - 10;
    pstCond.aCond[2].ucFieldId = 1;
    pstCond.aCond[2].enOp = DB_OP_LARGEREQUAL;
    memset_s((char *)pstCond.aCond[2].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_UINT32 *)pstCond.aCond[2].aucValue = opVal- 20;
    pstCond.aCond[3].ucFieldId = 5;
    pstCond.aCond[3].enOp = DB_OP_LARGEREQUAL;
    memset_s((char *)pstCond.aCond[3].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(uint16_t *)pstCond.aCond[3].aucValue = UINT32_TO_UINT16(opVal - 30);
    testPrintCond(&pstCond, pstFldLst);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = recordCnt;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum + 1);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt - opVal, pstBufDataGet.ulRecNum);

    // 获取所有记录的数据
    bool isMatch[pstBufDataGet.ulRecNum] = { 0 };
    for (VOS_UINT32 i = 0; i < pstBufDataGet.ulRecNum; i++) {
        isMatch[i] = false;
    }
    for (VOS_UINT32 i = 0; i < pstBufDataGet.ulRecNum; i++) {
        TblFieldDataDefT tblFieldDataDefGet = {0};
        testGetAllField(pBufGet + (pstBufDataGet.usRecLen * i), &tblFieldDataDefGet, pstBufDataGet.usRecLen);
        for (VOS_UINT32 j = opVal; j < recordCnt; j++) {
            ret = testCheckAllField(&tblFieldDataDefGet, j, j);
            if (ret != DB_SUCCESS_V1) {
                continue;
            }
            isMatch[j - opVal] = true;
            break;
        }
    }
    for (VOS_UINT32 i = opVal; i < pstBufDataGet.ulRecNum; i++) {
        AW_MACRO_ASSERT_EQ_BOOL(true, isMatch[i - opVal]);
    }
    TEST_V1_FREE(pBufGet);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecEx接口测试，DB_OP_LARGEREQUAL, 多个过滤条件，索引字段部分匹配，不按顺序
TEST_F(Fun_SelectAllRecEx, V1Com_008_136)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    DB_FIELD_DEF_STRU *pstFldLst;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    VOS_UINT8 ucIdxFldNum[2] = { 1, 3 };
    VOS_UINT8 ucUniqueFlagLst[2] = { 1, 1 };
    VOS_UINT8 aucFieldIDLst[2][DB_IDX_FLD_MAX] = { {0}, {0, 1, 2} };
    CreateV1Label(&pstFldLst, ulNCols, ucIdxFldNum, aucFieldIDLst, ucUniqueFlagLst, &pusRelId);

    // 写数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_UINT32 recordCnt = rand() % 901 + 100;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u",recordCnt );
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_INT32 opVal = rand() % (recordCnt - 50) + 50;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 4;
    pstCond.aCond[0].ucFieldId = 2;
    pstCond.aCond[0].enOp = DB_OP_LARGEREQUAL;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_INT32 *)pstCond.aCond[0].aucValue = opVal;
    pstCond.aCond[1].ucFieldId = 0;
    pstCond.aCond[1].enOp = DB_OP_LARGEREQUAL;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_UINT32 *)pstCond.aCond[1].aucValue = opVal - 10;
    pstCond.aCond[2].ucFieldId = 6;
    pstCond.aCond[2].enOp = DB_OP_LARGEREQUAL;
    memset_s((char *)pstCond.aCond[2].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(int16_t *)pstCond.aCond[2].aucValue = UINT32_TO_INT16(opVal - 20);
    pstCond.aCond[3].ucFieldId = 5;
    pstCond.aCond[3].enOp = DB_OP_LARGEREQUAL;
    memset_s((char *)pstCond.aCond[3].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(uint16_t *)pstCond.aCond[3].aucValue = UINT32_TO_UINT16(opVal - 30);
    testPrintCond(&pstCond, pstFldLst);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = recordCnt;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum + 1);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt - opVal, pstBufDataGet.ulRecNum);

    // 获取所有记录的数据
    bool isMatch[pstBufDataGet.ulRecNum] = { 0 };
    for (VOS_UINT32 i = 0; i < pstBufDataGet.ulRecNum; i++) {
        isMatch[i] = false;
    }
    for (VOS_UINT32 i = 0; i < pstBufDataGet.ulRecNum; i++) {
        TblFieldDataDefT tblFieldDataDefGet = {0};
        testGetAllField(pBufGet + (pstBufDataGet.usRecLen * i), &tblFieldDataDefGet, pstBufDataGet.usRecLen);
        for (VOS_UINT32 j = opVal; j < recordCnt; j++) {
            ret = testCheckAllField(&tblFieldDataDefGet, j, j);
            if (ret != DB_SUCCESS_V1) {
                continue;
            }
            isMatch[j - opVal] = true;
            break;
        }
    }
    for (VOS_UINT32 i = opVal; i < pstBufDataGet.ulRecNum; i++) {
        AW_MACRO_ASSERT_EQ_BOOL(true, isMatch[i - opVal]);
    }
    TEST_V1_FREE(pBufGet);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecEx接口测试，DB_OP_LARGEREQUAL, 多个过滤条件，没有索引字段
TEST_F(Fun_SelectAllRecEx, V1Com_008_137)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    DB_FIELD_DEF_STRU *pstFldLst;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    VOS_UINT8 ucIdxFldNum[2] = { 1, 3 };
    VOS_UINT8 ucUniqueFlagLst[2] = { 1, 1 };
    VOS_UINT8 aucFieldIDLst[2][DB_IDX_FLD_MAX] = { {0}, {0, 1, 2} };
    CreateV1Label(&pstFldLst, ulNCols, ucIdxFldNum, aucFieldIDLst, ucUniqueFlagLst, &pusRelId);

    // 写数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_UINT32 recordCnt = rand() % 901 + 100;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u",recordCnt );
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_INT32 opVal = rand() % (recordCnt - 50) + 50;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 4;
    pstCond.aCond[0].ucFieldId = 3;
    pstCond.aCond[0].enOp = DB_OP_LARGEREQUAL;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(float *)pstCond.aCond[0].aucValue = UINT32_TO_FLOAT(opVal);
    pstCond.aCond[1].ucFieldId = 4;
    pstCond.aCond[1].enOp = DB_OP_LARGEREQUAL;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(double *)pstCond.aCond[1].aucValue = UINT32_TO_DOUBLE(opVal - 10);
    pstCond.aCond[2].ucFieldId = 5;
    pstCond.aCond[2].enOp = DB_OP_LARGEREQUAL;
    memset_s((char *)pstCond.aCond[2].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(uint16_t *)pstCond.aCond[2].aucValue = UINT32_TO_UINT16(opVal - 20);
    pstCond.aCond[3].ucFieldId = 6;
    pstCond.aCond[3].enOp = DB_OP_LARGEREQUAL;
    memset_s((char *)pstCond.aCond[3].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(int16_t *)pstCond.aCond[3].aucValue = UINT32_TO_INT16(opVal - 30);
    testPrintCond(&pstCond, pstFldLst);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = recordCnt;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum + 1);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt - opVal, pstBufDataGet.ulRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstBufDataGet.ulRecNum; i++) {
        TblFieldDataDefT tblFieldDataDefGet = {0};
        testGetAllField(pBufGet + (pstBufDataGet.usRecLen * i), &tblFieldDataDefGet, pstBufDataGet.usRecLen);
        ret = testCheckAllField(&tblFieldDataDefGet, opVal + i, opVal + i);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pBufGet);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecEx接口测试，DB_OP_MAX_LESS, 多个过滤条件，索引字段全部匹配，不按顺序
TEST_F(Fun_SelectAllRecEx, V1Com_008_138)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    DB_FIELD_DEF_STRU *pstFldLst;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    VOS_UINT8 ucIdxFldNum[2] = { 1, 3 };
    VOS_UINT8 ucUniqueFlagLst[2] = { 1, 1 };
    VOS_UINT8 aucFieldIDLst[2][DB_IDX_FLD_MAX] = { {0}, {0, 1, 2} };
    CreateV1Label(&pstFldLst, ulNCols, ucIdxFldNum, aucFieldIDLst, ucUniqueFlagLst, &pusRelId);

    // 写数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_UINT32 recordCnt = rand() % 901 + 100;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u",recordCnt );
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_INT32 opVal = rand() % (recordCnt - 70) + 40;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 4;
    pstCond.aCond[0].ucFieldId = 2;
    pstCond.aCond[0].enOp = DB_OP_MAX_LESS;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_INT32 *)pstCond.aCond[0].aucValue = opVal;
    pstCond.aCond[1].ucFieldId = 0;
    pstCond.aCond[1].enOp = DB_OP_MAX_LESS;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_UINT32 *)pstCond.aCond[1].aucValue = opVal + 10;
    pstCond.aCond[2].ucFieldId = 1;
    pstCond.aCond[2].enOp = DB_OP_MAX_LESS;
    memset_s((char *)pstCond.aCond[2].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_UINT32 *)pstCond.aCond[2].aucValue = opVal + 20;
    pstCond.aCond[3].ucFieldId = 5;
    pstCond.aCond[3].enOp = DB_OP_MAX_LESS;
    memset_s((char *)pstCond.aCond[3].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(uint16_t *)pstCond.aCond[3].aucValue = UINT32_TO_UINT16(opVal + 30);
    testPrintCond(&pstCond, pstFldLst);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = recordCnt;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstBufDataGet.ulRecNum);

    // 获取所有记录的数据
    TblFieldDataDefT tblFieldDataDefGet = {0};
    testGetAllField(pBufGet, &tblFieldDataDefGet, pstBufDataGet.usRecLen);
    ret = testCheckAllField(&tblFieldDataDefGet, opVal - 1, opVal - 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    TEST_V1_FREE(pBufGet);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecEx接口测试，DB_OP_MAX_LESS, 多个过滤条件，索引字段部分匹配，不按顺序
TEST_F(Fun_SelectAllRecEx, V1Com_008_139)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    DB_FIELD_DEF_STRU *pstFldLst;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    VOS_UINT8 ucIdxFldNum[2] = { 1, 3 };
    VOS_UINT8 ucUniqueFlagLst[2] = { 1, 1 };
    VOS_UINT8 aucFieldIDLst[2][DB_IDX_FLD_MAX] = { {0}, {0, 1, 2} };
    CreateV1Label(&pstFldLst, ulNCols, ucIdxFldNum, aucFieldIDLst, ucUniqueFlagLst, &pusRelId);

    // 写数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_UINT32 recordCnt = rand() % 901 + 100;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u",recordCnt );
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_INT32 opVal = rand() % (recordCnt - 70) + 40;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 4;
    pstCond.aCond[0].ucFieldId = 2;
    pstCond.aCond[0].enOp = DB_OP_MAX_LESS;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_INT32 *)pstCond.aCond[0].aucValue = opVal;
    pstCond.aCond[1].ucFieldId = 0;
    pstCond.aCond[1].enOp = DB_OP_MAX_LESS;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_UINT32 *)pstCond.aCond[1].aucValue = opVal + 10;
    pstCond.aCond[2].ucFieldId = 6;
    pstCond.aCond[2].enOp = DB_OP_MAX_LESS;
    memset_s((char *)pstCond.aCond[2].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(int16_t *)pstCond.aCond[2].aucValue = UINT32_TO_INT16(opVal + 20);
    pstCond.aCond[3].ucFieldId = 5;
    pstCond.aCond[3].enOp = DB_OP_MAX_LESS;
    memset_s((char *)pstCond.aCond[3].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(uint16_t *)pstCond.aCond[3].aucValue = UINT32_TO_UINT16(opVal + 30);
    testPrintCond(&pstCond, pstFldLst);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = recordCnt;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum + 1);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstBufDataGet.ulRecNum);

    // 获取所有记录的数据
    TblFieldDataDefT tblFieldDataDefGet = {0};
    testGetAllField(pBufGet, &tblFieldDataDefGet, pstBufDataGet.usRecLen);
    ret = testCheckAllField(&tblFieldDataDefGet, opVal - 1, opVal - 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    TEST_V1_FREE(pBufGet);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecEx接口测试，DB_OP_MAX_LESS, 多个过滤条件，没有索引字段
TEST_F(Fun_SelectAllRecEx, V1Com_008_140)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    DB_FIELD_DEF_STRU *pstFldLst;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    VOS_UINT8 ucIdxFldNum[2] = { 1, 3 };
    VOS_UINT8 ucUniqueFlagLst[2] = { 0, 0 };
    VOS_UINT8 aucFieldIDLst[2][DB_IDX_FLD_MAX] = { {0}, {0, 1, 2} };
    CreateV1Label(&pstFldLst, ulNCols, ucIdxFldNum, aucFieldIDLst, ucUniqueFlagLst, &pusRelId);

    // 写数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_UINT32 recordCnt = rand() % 901 + 100;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u",recordCnt );
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    VOS_UINT32 val1 = 10;
    VOS_UINT32 val2 = 20;
    VOS_UINT32 setVal;
    VOS_UINT32 expctQueryCnt = 0;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        setVal = ((i % 2) == 0) ? val1 : val2;
        if (setVal == val1) {
            expctQueryCnt++;
        }
        testSetAllFieldNotUnique(&TblFieldDataDefSet, setVal, setVal);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_INT32 opVal = 15;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 4;
    pstCond.aCond[0].ucFieldId = 3;
    pstCond.aCond[0].enOp = DB_OP_MAX_LESS;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(float *)pstCond.aCond[0].aucValue = UINT32_TO_FLOAT(opVal);
    pstCond.aCond[1].ucFieldId = 4;
    pstCond.aCond[1].enOp = DB_OP_MAX_LESS;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(double *)pstCond.aCond[1].aucValue = UINT32_TO_DOUBLE(opVal + 1);
    pstCond.aCond[2].ucFieldId = 5;
    pstCond.aCond[2].enOp = DB_OP_MAX_LESS;
    memset_s((char *)pstCond.aCond[2].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(uint16_t *)pstCond.aCond[2].aucValue = UINT32_TO_UINT16(opVal + 2);
    pstCond.aCond[3].ucFieldId = 6;
    pstCond.aCond[3].enOp = DB_OP_MAX_LESS;
    memset_s((char *)pstCond.aCond[3].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(int16_t *)pstCond.aCond[3].aucValue = UINT32_TO_INT16(opVal + 3);
    testPrintCond(&pstCond, pstFldLst);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = recordCnt;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum + 1);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(expctQueryCnt, pstBufDataGet.ulRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstBufDataGet.ulRecNum; i++) {
        TblFieldDataDefT tblFieldDataDefGet = {0};
        testGetAllField(pBufGet + (pstBufDataGet.usRecLen * i), &tblFieldDataDefGet, pstBufDataGet.usRecLen);
        ret = testCheckAllField(&tblFieldDataDefGet, val1, val1);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pBufGet);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// TPC_SelectAllRecEx接口测试，DB_OP_MAX_LESS_EQUAL, 多个过滤条件，索引字段全部匹配，不按顺序
TEST_F(Fun_SelectAllRecEx, V1Com_008_141)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    DB_FIELD_DEF_STRU *pstFldLst;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    VOS_UINT8 ucIdxFldNum[2] = { 1, 3 };
    VOS_UINT8 ucUniqueFlagLst[2] = { 1, 1 };
    VOS_UINT8 aucFieldIDLst[2][DB_IDX_FLD_MAX] = { {0}, {0, 1, 2} };
    CreateV1Label(&pstFldLst, ulNCols, ucIdxFldNum, aucFieldIDLst, ucUniqueFlagLst, &pusRelId);

    // 写数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_UINT32 recordCnt = rand() % 901 + 100;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u",recordCnt );
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_INT32 opVal = rand() % (recordCnt - 70) + 40;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 4;
    pstCond.aCond[0].ucFieldId = 2;
    pstCond.aCond[0].enOp = DB_OP_MAX_LESS_EQUAL;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_INT32 *)pstCond.aCond[0].aucValue = opVal;
    pstCond.aCond[1].ucFieldId = 0;
    pstCond.aCond[1].enOp = DB_OP_MAX_LESS_EQUAL;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_UINT32 *)pstCond.aCond[1].aucValue = opVal + 10;
    pstCond.aCond[2].ucFieldId = 1;
    pstCond.aCond[2].enOp = DB_OP_MAX_LESS_EQUAL;
    memset_s((char *)pstCond.aCond[2].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_UINT32 *)pstCond.aCond[2].aucValue = opVal + 20;
    pstCond.aCond[3].ucFieldId = 5;
    pstCond.aCond[3].enOp = DB_OP_MAX_LESS_EQUAL;
    memset_s((char *)pstCond.aCond[3].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(uint16_t *)pstCond.aCond[3].aucValue = UINT32_TO_UINT16(opVal + 30);
    testPrintCond(&pstCond, pstFldLst);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = recordCnt;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstBufDataGet.ulRecNum);

    // 获取所有记录的数据
    TblFieldDataDefT tblFieldDataDefGet = {0};
    testGetAllField(pBufGet, &tblFieldDataDefGet, pstBufDataGet.usRecLen);
    ret = testCheckAllField(&tblFieldDataDefGet, opVal, opVal);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    TEST_V1_FREE(pBufGet);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecEx接口测试，DB_OP_MAX_LESS_EQUAL, 多个过滤条件，索引字段部分匹配，不按顺序
TEST_F(Fun_SelectAllRecEx, V1Com_008_142)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    DB_FIELD_DEF_STRU *pstFldLst;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    VOS_UINT8 ucIdxFldNum[2] = { 1, 3 };
    VOS_UINT8 ucUniqueFlagLst[2] = { 1, 1 };
    VOS_UINT8 aucFieldIDLst[2][DB_IDX_FLD_MAX] = { {0}, {0, 1, 2} };
    CreateV1Label(&pstFldLst, ulNCols, ucIdxFldNum, aucFieldIDLst, ucUniqueFlagLst, &pusRelId);

    // 写数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_UINT32 recordCnt = rand() % 901 + 100;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u",recordCnt );
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_INT32 opVal = rand() % (recordCnt - 70) + 40;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 4;
    pstCond.aCond[0].ucFieldId = 2;
    pstCond.aCond[0].enOp = DB_OP_MAX_LESS_EQUAL;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_INT32 *)pstCond.aCond[0].aucValue = opVal;
    pstCond.aCond[1].ucFieldId = 0;
    pstCond.aCond[1].enOp = DB_OP_MAX_LESS_EQUAL;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_UINT32 *)pstCond.aCond[1].aucValue = opVal + 10;
    pstCond.aCond[2].ucFieldId = 6;
    pstCond.aCond[2].enOp = DB_OP_MAX_LESS_EQUAL;
    memset_s((char *)pstCond.aCond[2].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(int16_t *)pstCond.aCond[2].aucValue = UINT32_TO_INT16(opVal + 20);
    pstCond.aCond[3].ucFieldId = 5;
    pstCond.aCond[3].enOp = DB_OP_MAX_LESS_EQUAL;
    memset_s((char *)pstCond.aCond[3].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(uint16_t *)pstCond.aCond[3].aucValue = UINT32_TO_UINT16(opVal + 30);
    testPrintCond(&pstCond, pstFldLst);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = recordCnt;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum + 1);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstBufDataGet.ulRecNum);

    // 获取所有记录的数据
    TblFieldDataDefT tblFieldDataDefGet = {0};
    testGetAllField(pBufGet, &tblFieldDataDefGet, pstBufDataGet.usRecLen);
    ret = testCheckAllField(&tblFieldDataDefGet, opVal, opVal);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    TEST_V1_FREE(pBufGet);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecEx接口测试，DB_OP_MAX_LESS_EQUAL, 多个过滤条件，没有索引字段
TEST_F(Fun_SelectAllRecEx, V1Com_008_143)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    DB_FIELD_DEF_STRU *pstFldLst;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    VOS_UINT8 ucIdxFldNum[2] = { 1, 3 };
    VOS_UINT8 ucUniqueFlagLst[2] = { 0, 0 };
    VOS_UINT8 aucFieldIDLst[2][DB_IDX_FLD_MAX] = { {0}, {0, 1, 2} };
    CreateV1Label(&pstFldLst, ulNCols, ucIdxFldNum, aucFieldIDLst, ucUniqueFlagLst, &pusRelId);

    // 写数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_UINT32 recordCnt = rand() % 901 + 100;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u",recordCnt );
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    VOS_UINT32 val1 = 15;
    VOS_UINT32 val2 = 20;
    VOS_UINT32 setVal;
    VOS_UINT32 expctQueryCnt = 0;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        setVal = ((i % 2) == 0) ? val1 : val2;
        if (setVal == val1) {
            expctQueryCnt++;
        }
        testSetAllFieldNotUnique(&TblFieldDataDefSet, setVal, setVal);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_INT32 opVal = 15;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 4;
    pstCond.aCond[0].ucFieldId = 3;
    pstCond.aCond[0].enOp = DB_OP_MAX_LESS_EQUAL;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(float *)pstCond.aCond[0].aucValue = UINT32_TO_FLOAT(opVal);
    pstCond.aCond[1].ucFieldId = 4;
    pstCond.aCond[1].enOp = DB_OP_MAX_LESS_EQUAL;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(double *)pstCond.aCond[1].aucValue = UINT32_TO_DOUBLE(opVal + 1);
    pstCond.aCond[2].ucFieldId = 5;
    pstCond.aCond[2].enOp = DB_OP_MAX_LESS_EQUAL;
    memset_s((char *)pstCond.aCond[2].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(uint16_t *)pstCond.aCond[2].aucValue = UINT32_TO_UINT16(opVal + 2);
    pstCond.aCond[3].ucFieldId = 6;
    pstCond.aCond[3].enOp = DB_OP_MAX_LESS_EQUAL;
    memset_s((char *)pstCond.aCond[3].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(int16_t *)pstCond.aCond[3].aucValue = UINT32_TO_INT16(opVal + 3);
    testPrintCond(&pstCond, pstFldLst);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = recordCnt;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum + 1);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(expctQueryCnt, pstBufDataGet.ulRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstBufDataGet.ulRecNum; i++) {
        TblFieldDataDefT tblFieldDataDefGet = {0};
        testGetAllField(pBufGet + (pstBufDataGet.usRecLen * i), &tblFieldDataDefGet, pstBufDataGet.usRecLen);
        ret = testCheckAllField(&tblFieldDataDefGet, val1, val1);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pBufGet);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecEx接口测试，DB_OP_MIN_LARGER, 多个过滤条件，索引字段全部匹配，不按顺序
TEST_F(Fun_SelectAllRecEx, V1Com_008_144)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    DB_FIELD_DEF_STRU *pstFldLst;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    VOS_UINT8 ucIdxFldNum[2] = { 1, 3 };
    VOS_UINT8 ucUniqueFlagLst[2] = { 1, 1 };
    VOS_UINT8 aucFieldIDLst[2][DB_IDX_FLD_MAX] = { {0}, {0, 1, 2} };
    CreateV1Label(&pstFldLst, ulNCols, ucIdxFldNum, aucFieldIDLst, ucUniqueFlagLst, &pusRelId);

    // 写数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_UINT32 recordCnt = rand() % 901 + 100;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u",recordCnt );
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_INT32 opVal = rand() % (recordCnt - 50) + 49;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 4;
    pstCond.aCond[0].ucFieldId = 2;
    pstCond.aCond[0].enOp = DB_OP_MIN_LARGER;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_INT32 *)pstCond.aCond[0].aucValue = opVal;
    pstCond.aCond[1].ucFieldId = 0;
    pstCond.aCond[1].enOp = DB_OP_MIN_LARGER;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_UINT32 *)pstCond.aCond[1].aucValue = opVal - 10;
    pstCond.aCond[2].ucFieldId = 1;
    pstCond.aCond[2].enOp = DB_OP_MIN_LARGER;
    memset_s((char *)pstCond.aCond[2].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_UINT32 *)pstCond.aCond[2].aucValue = opVal - 20;
    pstCond.aCond[3].ucFieldId = 5;
    pstCond.aCond[3].enOp = DB_OP_MIN_LARGER;
    memset_s((char *)pstCond.aCond[3].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(uint16_t *)pstCond.aCond[3].aucValue = UINT32_TO_UINT16(opVal - 30);
    testPrintCond(&pstCond, pstFldLst);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = recordCnt;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstBufDataGet.ulRecNum);

    // 获取所有记录的数据
    TblFieldDataDefT tblFieldDataDefGet = {0};
    testGetAllField(pBufGet, &tblFieldDataDefGet, pstBufDataGet.usRecLen);
    ret = testCheckAllField(&tblFieldDataDefGet, opVal + 1, opVal + 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    TEST_V1_FREE(pBufGet);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecEx接口测试，DB_OP_MIN_LARGER, 多个过滤条件，索引字段部分匹配，不按顺序
TEST_F(Fun_SelectAllRecEx, V1Com_008_145)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    DB_FIELD_DEF_STRU *pstFldLst;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    VOS_UINT8 ucIdxFldNum[2] = { 1, 3 };
    VOS_UINT8 ucUniqueFlagLst[2] = { 1, 1 };
    VOS_UINT8 aucFieldIDLst[2][DB_IDX_FLD_MAX] = { {0}, {0, 1, 2} };
    CreateV1Label(&pstFldLst, ulNCols, ucIdxFldNum, aucFieldIDLst, ucUniqueFlagLst, &pusRelId);

    // 写数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_UINT32 recordCnt = rand() % 901 + 100;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u",recordCnt );
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_INT32 opVal = rand() % (recordCnt - 50) + 49;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 4;
    pstCond.aCond[0].ucFieldId = 2;
    pstCond.aCond[0].enOp = DB_OP_MIN_LARGER;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_INT32 *)pstCond.aCond[0].aucValue = opVal;
    pstCond.aCond[1].ucFieldId = 0;
    pstCond.aCond[1].enOp = DB_OP_MIN_LARGER;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_UINT32 *)pstCond.aCond[1].aucValue = opVal - 10;
    pstCond.aCond[2].ucFieldId = 6;
    pstCond.aCond[2].enOp = DB_OP_MIN_LARGER;
    memset_s((char *)pstCond.aCond[2].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(int16_t *)pstCond.aCond[2].aucValue = UINT32_TO_INT16(opVal - 20);
    pstCond.aCond[3].ucFieldId = 5;
    pstCond.aCond[3].enOp = DB_OP_MIN_LARGER;
    memset_s((char *)pstCond.aCond[3].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(uint16_t *)pstCond.aCond[3].aucValue = UINT32_TO_UINT16(opVal - 30);
    testPrintCond(&pstCond, pstFldLst);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = recordCnt;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum + 1);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstBufDataGet.ulRecNum);
    
    // 获取所有记录的数据
    TblFieldDataDefT tblFieldDataDefGet = {0};
    testGetAllField(pBufGet, &tblFieldDataDefGet, pstBufDataGet.usRecLen);
    ret = testCheckAllField(&tblFieldDataDefGet, opVal + 1, opVal + 1);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    TEST_V1_FREE(pBufGet);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecEx接口测试，DB_OP_MIN_LARGER, 多个过滤条件，没有索引字段
TEST_F(Fun_SelectAllRecEx, V1Com_008_146)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    DB_FIELD_DEF_STRU *pstFldLst;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    VOS_UINT8 ucIdxFldNum[2] = { 1, 3 };
    VOS_UINT8 ucUniqueFlagLst[2] = { 0, 0 };
    VOS_UINT8 aucFieldIDLst[2][DB_IDX_FLD_MAX] = { {0}, {0, 1, 2} };
    CreateV1Label(&pstFldLst, ulNCols, ucIdxFldNum, aucFieldIDLst, ucUniqueFlagLst, &pusRelId);

    // 写数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_UINT32 recordCnt = rand() % 901 + 100;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u",recordCnt );
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    VOS_UINT32 val1 = 10;
    VOS_UINT32 val2 = 20;
    VOS_UINT32 setVal;
    VOS_UINT32 expctQueryCnt = 0;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        setVal = ((i % 2) == 0) ? val1 : val2;
        if (setVal == val2) {
            expctQueryCnt++;
        }
        testSetAllFieldNotUnique(&TblFieldDataDefSet, setVal, setVal);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_INT32 opVal = 15;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 4;
    pstCond.aCond[0].ucFieldId = 3;
    pstCond.aCond[0].enOp = DB_OP_MIN_LARGER;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(float *)pstCond.aCond[0].aucValue = UINT32_TO_FLOAT(opVal);
    pstCond.aCond[1].ucFieldId = 4;
    pstCond.aCond[1].enOp = DB_OP_MIN_LARGER;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(double *)pstCond.aCond[1].aucValue = UINT32_TO_DOUBLE(opVal - 1);
    pstCond.aCond[2].ucFieldId = 5;
    pstCond.aCond[2].enOp = DB_OP_MIN_LARGER;
    memset_s((char *)pstCond.aCond[2].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(uint16_t *)pstCond.aCond[2].aucValue = UINT32_TO_UINT16(opVal - 2);
    pstCond.aCond[3].ucFieldId = 6;
    pstCond.aCond[3].enOp = DB_OP_MIN_LARGER;
    memset_s((char *)pstCond.aCond[3].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(int16_t *)pstCond.aCond[3].aucValue = UINT32_TO_INT16(opVal - 3);
    testPrintCond(&pstCond, pstFldLst);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = recordCnt;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum + 1);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(expctQueryCnt, pstBufDataGet.ulRecNum);
    
    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstBufDataGet.ulRecNum; i++) {
        TblFieldDataDefT tblFieldDataDefGet = {0};
        testGetAllField(pBufGet + (pstBufDataGet.usRecLen * i), &tblFieldDataDefGet, pstBufDataGet.usRecLen);
        ret = testCheckAllField(&tblFieldDataDefGet, val2, val2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pBufGet);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecEx接口测试，DB_OP_MIN_LARGER_EQUAL, 多个过滤条件，索引字段全部匹配，不按顺序
TEST_F(Fun_SelectAllRecEx, V1Com_008_147)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    DB_FIELD_DEF_STRU *pstFldLst;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    VOS_UINT8 ucIdxFldNum[2] = { 1, 3 };
    VOS_UINT8 ucUniqueFlagLst[2] = { 1, 1 };
    VOS_UINT8 aucFieldIDLst[2][DB_IDX_FLD_MAX] = { {0}, {0, 1, 2} };
    CreateV1Label(&pstFldLst, ulNCols, ucIdxFldNum, aucFieldIDLst, ucUniqueFlagLst, &pusRelId);

    // 写数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_UINT32 recordCnt = rand() % 901 + 100;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u",recordCnt );
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_INT32 opVal = rand() % (recordCnt - 50) + 50;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 4;
    pstCond.aCond[0].ucFieldId = 2;
    pstCond.aCond[0].enOp = DB_OP_MIN_LARGER_EQUAL;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_INT32 *)pstCond.aCond[0].aucValue = opVal;
    pstCond.aCond[1].ucFieldId = 0;
    pstCond.aCond[1].enOp = DB_OP_MIN_LARGER_EQUAL;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_UINT32 *)pstCond.aCond[1].aucValue = opVal - 10;
    pstCond.aCond[2].ucFieldId = 1;
    pstCond.aCond[2].enOp = DB_OP_MIN_LARGER_EQUAL;
    memset_s((char *)pstCond.aCond[2].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_UINT32 *)pstCond.aCond[2].aucValue = opVal - 20;
    pstCond.aCond[3].ucFieldId = 5;
    pstCond.aCond[3].enOp = DB_OP_MIN_LARGER_EQUAL;
    memset_s((char *)pstCond.aCond[3].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(uint16_t *)pstCond.aCond[3].aucValue = UINT32_TO_UINT16(opVal - 30);
    testPrintCond(&pstCond, pstFldLst);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = recordCnt;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstBufDataGet.ulRecNum);
    
    // 获取所有记录的数据
    TblFieldDataDefT tblFieldDataDefGet = {0};
    testGetAllField(pBufGet, &tblFieldDataDefGet, pstBufDataGet.usRecLen);
    ret = testCheckAllField(&tblFieldDataDefGet, opVal, opVal);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    TEST_V1_FREE(pBufGet);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecEx接口测试，DB_OP_MIN_LARGER_EQUAL, 多个过滤条件，索引字段部分匹配，不按顺序
TEST_F(Fun_SelectAllRecEx, V1Com_008_148)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    DB_FIELD_DEF_STRU *pstFldLst;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    VOS_UINT8 ucIdxFldNum[2] = { 1, 3 };
    VOS_UINT8 ucUniqueFlagLst[2] = { 1, 1 };
    VOS_UINT8 aucFieldIDLst[2][DB_IDX_FLD_MAX] = { {0}, {0, 1, 2} };
    CreateV1Label(&pstFldLst, ulNCols, ucIdxFldNum, aucFieldIDLst, ucUniqueFlagLst, &pusRelId);

    // 写数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_UINT32 recordCnt = rand() % 901 + 100;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u",recordCnt );
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_INT32 opVal = rand() % (recordCnt - 50) + 50;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 4;
    pstCond.aCond[0].ucFieldId = 2;
    pstCond.aCond[0].enOp = DB_OP_MIN_LARGER_EQUAL;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_INT32 *)pstCond.aCond[0].aucValue = opVal;
    pstCond.aCond[1].ucFieldId = 0;
    pstCond.aCond[1].enOp = DB_OP_MIN_LARGER_EQUAL;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_UINT32 *)pstCond.aCond[1].aucValue = opVal - 10;
    pstCond.aCond[2].ucFieldId = 6;
    pstCond.aCond[2].enOp = DB_OP_MIN_LARGER_EQUAL;
    memset_s((char *)pstCond.aCond[2].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(int16_t *)pstCond.aCond[2].aucValue = UINT32_TO_INT16(opVal - 20);
    pstCond.aCond[3].ucFieldId = 5;
    pstCond.aCond[3].enOp = DB_OP_MIN_LARGER_EQUAL;
    memset_s((char *)pstCond.aCond[3].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(uint16_t *)pstCond.aCond[3].aucValue = UINT32_TO_UINT16(opVal - 30);
    testPrintCond(&pstCond, pstFldLst);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;

    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = recordCnt;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum + 1);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstBufDataGet.ulRecNum);
    
    // 获取所有记录的数据
    TblFieldDataDefT tblFieldDataDefGet = {0};
    testGetAllField(pBufGet, &tblFieldDataDefGet, pstBufDataGet.usRecLen);
    ret = testCheckAllField(&tblFieldDataDefGet, opVal, opVal);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    TEST_V1_FREE(pBufGet);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecEx接口测试，DB_OP_MIN_LARGER_EQUAL, 多个过滤条件，没有索引字段
TEST_F(Fun_SelectAllRecEx, V1Com_008_149)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    DB_FIELD_DEF_STRU *pstFldLst;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    VOS_UINT8 ucIdxFldNum[2] = { 1, 3 };
    VOS_UINT8 ucUniqueFlagLst[2] = { 0, 0 };
    VOS_UINT8 aucFieldIDLst[2][DB_IDX_FLD_MAX] = { {0}, {0, 1, 2} };
    CreateV1Label(&pstFldLst, ulNCols, ucIdxFldNum, aucFieldIDLst, ucUniqueFlagLst, &pusRelId);

    // 写数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_UINT32 recordCnt = rand() % 901 + 100;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u",recordCnt );
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    VOS_UINT32 val1 = 10;
    VOS_UINT32 val2 = 20;
    VOS_UINT32 setVal;
    VOS_UINT32 expctQueryCnt = 0;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        setVal = ((i % 2) == 0) ? val1 : val2;
        if (setVal == val2) {
            expctQueryCnt++;
        }
        testSetAllFieldNotUnique(&TblFieldDataDefSet, setVal, setVal);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_INT32 opVal = 15;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 4;
    pstCond.aCond[0].ucFieldId = 3;
    pstCond.aCond[0].enOp = DB_OP_MIN_LARGER_EQUAL;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(float *)pstCond.aCond[0].aucValue = UINT32_TO_FLOAT(opVal);
    pstCond.aCond[1].ucFieldId = 4;
    pstCond.aCond[1].enOp = DB_OP_MIN_LARGER_EQUAL;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(double *)pstCond.aCond[1].aucValue = UINT32_TO_DOUBLE(opVal + 1);
    pstCond.aCond[2].ucFieldId = 5;
    pstCond.aCond[2].enOp = DB_OP_MIN_LARGER_EQUAL;
    memset_s((char *)pstCond.aCond[2].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(uint16_t *)pstCond.aCond[2].aucValue = UINT32_TO_UINT16(opVal + 3);
    pstCond.aCond[3].ucFieldId = 6;
    pstCond.aCond[3].enOp = DB_OP_MIN_LARGER_EQUAL;
    memset_s((char *)pstCond.aCond[3].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(int16_t *)pstCond.aCond[3].aucValue = UINT32_TO_INT16(opVal + 5);
    testPrintCond(&pstCond, pstFldLst);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = recordCnt;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum + 1);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(expctQueryCnt, pstBufDataGet.ulRecNum);
    
    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstBufDataGet.ulRecNum; i++) {
        TblFieldDataDefT tblFieldDataDefGet = {0};
        testGetAllField(pBufGet + (pstBufDataGet.usRecLen * i), &tblFieldDataDefGet, pstBufDataGet.usRecLen);
        ret = testCheckAllField(&tblFieldDataDefGet, val2, val2);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pBufGet);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// TPC_SelectAllRecEx接口测试，DB_OP_HAVEPREFIX
TEST_F(Fun_SelectAllRecEx, V1Com_008_150)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    DB_FIELD_DEF_STRU *pstFldLst;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    VOS_UINT8 ucIdxFldNum[2] = { 1, 2 };
    VOS_UINT8 ucUniqueFlagLst[2] = { 1, 1 };
    VOS_UINT8 aucFieldIDLst[2][DB_IDX_FLD_MAX] = { {8}, {8, 9} };
    CreateV1Label(&pstFldLst, ulNCols, ucIdxFldNum, aucFieldIDLst, ucUniqueFlagLst, &pusRelId);

    // 写数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_UINT32 recordCnt = rand() % 901 + 100;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u",recordCnt );
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    char setStrVal1[101] = { 0 };
    char setStrVal2[101] = { 0 };
    const char *strVal1 = "abcde";
    const char *strVal2 = "Abcd";
    const char *strVal3 = "abc";
    const char *strVal4 = "ab";
    const char *setSteValPtr1 = NULL;
    const char *setSteValPtr2 = NULL;
    // 这个查询的数据量需要看下面查询数据设置的值
    VOS_UINT32 expectQueryCnt = 0;
    VOS_UINT32 *expectQueryVal = (VOS_UINT32 *)TEST_V1_MALLOC(sizeof(VOS_UINT32) * recordCnt);
    TEST_ASSERT_PTR_NULL(expectQueryVal);
    memset_s(expectQueryVal, sizeof(VOS_UINT32) * recordCnt, 0x00, sizeof(VOS_UINT32) * recordCnt);
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        if (i % 4 == 0) {
            setSteValPtr1 = strVal1;
            if (i < recordCnt / 2) {
                setSteValPtr2 = strVal2;
                expectQueryVal[expectQueryCnt] = i;
                expectQueryCnt++;
            } else {
                setSteValPtr2 = strVal3;
            }
        } else if (i % 4 == 1) {
            setSteValPtr1 = strVal2;
            setSteValPtr2 = strVal1;
        } else if (i % 4 == 2) {
            setSteValPtr1 = strVal3;
            setSteValPtr2 = strVal2;
        } else if (i % 4 == 3) {
            setSteValPtr1 = strVal4;
            setSteValPtr2 = strVal3;
        } else {
            setSteValPtr1 = strVal1;
            if (i < recordCnt / 2) {
                setSteValPtr2 = strVal2;
                expectQueryVal[expectQueryCnt] = i;
                expectQueryCnt++;
            } else {
                setSteValPtr2 = strVal3;
            }
        }
        sprintf_s(setStrVal1, sizeof(setStrVal1), "%s_%05u", setSteValPtr1, i);
        sprintf_s(setStrVal2, sizeof(setStrVal2), "%s_%05u", setSteValPtr2, i);
        testSetAllFieldByIndexString(&TblFieldDataDefSet, setStrVal1, setStrVal2, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    char opStrVal1[DB_ELELEN_MAX] = { 0 };
    sprintf_s(opStrVal1, sizeof(opStrVal1), "abcd");
    char opStrVal2[DB_ELELEN_MAX] = { 0 };
    sprintf_s(opStrVal2, sizeof(opStrVal2), "Abcd");
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 2;
    pstCond.aCond[0].ucFieldId = 8;
    pstCond.aCond[0].enOp = DB_OP_HAVEPREFIX;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    strncpy((char *)pstCond.aCond[0].aucValue, opStrVal1, DB_ELELEN_MAX);
    pstCond.aCond[1].ucFieldId = 9;
    pstCond.aCond[1].enOp = DB_OP_HAVEPREFIX;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    strncpy((char *)pstCond.aCond[1].aucValue, opStrVal2, DB_ELELEN_MAX);
    testPrintCond(&pstCond, pstFldLst);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = recordCnt;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum + 1);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(expectQueryCnt, pstBufDataGet.ulRecNum);

    // 获取所有记录的数据
    char expectStrVal1[101] = { 0 };
    char expectStrVal2[101] = { 0 };
    for (VOS_UINT32 i = 0; i < pstBufDataGet.ulRecNum; i++) {
        TblFieldDataDefT tblFieldDataDefGet = {0};
        testGetAllField(pBufGet + (pstBufDataGet.usRecLen * i), &tblFieldDataDefGet, pstBufDataGet.usRecLen);
        sprintf_s(expectStrVal1, sizeof(expectStrVal1), "%s_%05u", strVal1, expectQueryVal[i]);
        sprintf_s(expectStrVal2, sizeof(expectStrVal2), "%s_%05u", strVal2, expectQueryVal[i]);
        ret = testCheckAllFieldyIndexString(&tblFieldDataDefGet, expectStrVal1, expectStrVal2, expectQueryVal[i]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pBufGet);
    TEST_V1_FREE(expectQueryVal);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecEx接口测试，DB_OP_NOPREFIX
TEST_F(Fun_SelectAllRecEx, V1Com_008_151)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    DB_FIELD_DEF_STRU *pstFldLst;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    VOS_UINT8 ucIdxFldNum[2] = { 1, 2 };
    VOS_UINT8 ucUniqueFlagLst[2] = { 1, 1 };
    VOS_UINT8 aucFieldIDLst[2][DB_IDX_FLD_MAX] = { {8}, {8, 9} };
    CreateV1Label(&pstFldLst, ulNCols, ucIdxFldNum, aucFieldIDLst, ucUniqueFlagLst, &pusRelId);

    // 写数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_UINT32 recordCnt = rand() % 901 + 100;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u",recordCnt );
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    char setStrVal1[101] = { 0 };
    char setStrVal2[101] = { 0 };
    const char *strVal1 = "abcde";
    const char *strVal2 = "Abcd";
    const char *strVal3 = "abc";
    const char *strVal4 = "ab";
    const char *setSteValPtr1 = NULL;
    const char *setSteValPtr2 = NULL;
    // 这个查询的数据量需要看下面查询数据设置的值
    VOS_UINT32 expectQueryCnt = 0;
    VOS_UINT32 *expectQueryVal = (VOS_UINT32 *)TEST_V1_MALLOC(sizeof(VOS_UINT32) * recordCnt);
    TEST_ASSERT_PTR_NULL(expectQueryVal);
    memset_s(expectQueryVal, sizeof(VOS_UINT32) * recordCnt, 0x00, sizeof(VOS_UINT32) * recordCnt);
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        if (i % 4 == 0) {
            setSteValPtr1 = strVal1;
            if (i < recordCnt / 2) {
                setSteValPtr2 = strVal2;
            } else {
                setSteValPtr2 = strVal3;
            }
        } else if (i % 4 == 1) {
            setSteValPtr1 = strVal2;
            setSteValPtr2 = strVal1;
            expectQueryVal[expectQueryCnt] = i;
            expectQueryCnt++;
        } else if (i % 4 == 2) {
            setSteValPtr1 = strVal3;
            setSteValPtr2 = strVal2;
        } else if (i % 4 == 3) {
            setSteValPtr1 = strVal4;
            setSteValPtr2 = strVal3;
            expectQueryVal[expectQueryCnt] = i;
            expectQueryCnt++;
        } else {
            setSteValPtr1 = strVal1;
            if (i < recordCnt / 2) {
                setSteValPtr2 = strVal2;
            } else {
                setSteValPtr2 = strVal3;
            }
        }
        sprintf_s(setStrVal1, sizeof(setStrVal1), "%s_%05u", setSteValPtr1, i);
        sprintf_s(setStrVal2, sizeof(setStrVal2), "%s_%05u", setSteValPtr2, i);
        testSetAllFieldByIndexString(&TblFieldDataDefSet, setStrVal1, setStrVal2, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    char opStrVal1[DB_ELELEN_MAX] = { 0 };
    sprintf_s(opStrVal1, sizeof(opStrVal1), "abcd");
    char opStrVal2[DB_ELELEN_MAX] = { 0 };
    sprintf_s(opStrVal2, sizeof(opStrVal2), "Abcd");
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 2;
    pstCond.aCond[0].ucFieldId = 8;
    pstCond.aCond[0].enOp = DB_OP_NOPREFIX;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    strncpy((char *)pstCond.aCond[0].aucValue, opStrVal1, DB_ELELEN_MAX);
    pstCond.aCond[1].ucFieldId = 9;
    pstCond.aCond[1].enOp = DB_OP_NOPREFIX;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    strncpy((char *)pstCond.aCond[1].aucValue, opStrVal2, DB_ELELEN_MAX);
    testPrintCond(&pstCond, pstFldLst);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = recordCnt;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum + 1);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(expectQueryCnt, pstBufDataGet.ulRecNum);

    // 获取所有记录的数据
    char expectStrVal1[101] = { 0 };
    char expectStrVal2[101] = { 0 };
    for (VOS_UINT32 i = 0; i < pstBufDataGet.ulRecNum; i++) {
        TblFieldDataDefT tblFieldDataDefGet = {0};
        testGetAllField(pBufGet + (pstBufDataGet.usRecLen * i), &tblFieldDataDefGet, pstBufDataGet.usRecLen);
        bool isCheckSucc = false;
        for (VOS_UINT32 j = 0; j < pstBufDataGet.ulRecNum; j++) {
            if (expectQueryVal[j] % 4 == 0) {
                sprintf_s(expectStrVal1, sizeof(expectStrVal1), "%s_%05u", strVal1, expectQueryVal[j]);
                sprintf_s(expectStrVal2, sizeof(expectStrVal2), "%s_%05u", strVal3, expectQueryVal[j]);
            } else if (expectQueryVal[j] % 4 == 1) {
                sprintf_s(expectStrVal1, sizeof(expectStrVal1), "%s_%05u", strVal2, expectQueryVal[j]);
                sprintf_s(expectStrVal2, sizeof(expectStrVal2), "%s_%05u", strVal1, expectQueryVal[j]);
            } else if (expectQueryVal[j] % 4 == 2) {
                sprintf_s(expectStrVal1, sizeof(expectStrVal1), "%s_%05u", strVal3, expectQueryVal[j]);
                sprintf_s(expectStrVal2, sizeof(expectStrVal2), "%s_%05u", strVal2, expectQueryVal[j]);
            } else if (expectQueryVal[j] % 4 == 3) {
                sprintf_s(expectStrVal1, sizeof(expectStrVal1), "%s_%05u", strVal4, expectQueryVal[j]);
                sprintf_s(expectStrVal2, sizeof(expectStrVal2), "%s_%05u", strVal3, expectQueryVal[j]);
            }
            ret = testCheckAllFieldyIndexString(&tblFieldDataDefGet, expectStrVal1, expectStrVal2, expectQueryVal[j]);
            if (ret == DB_SUCCESS_V1) {
                isCheckSucc = true;
                break;
            }
        }
        V1_AW_MACRO_ASSERT_EQ_INT(1, isCheckSucc);
    }
    TEST_V1_FREE(pBufGet);
    TEST_V1_FREE(expectQueryVal);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecEx接口测试，DB_OP_PREFIX，DB_OP_PREFIX12或者DB_OP_PREFIX21满足其一即可）
TEST_F(Fun_SelectAllRecEx, V1Com_008_152)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    DB_FIELD_DEF_STRU *pstFldLst;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    VOS_UINT8 ucIdxFldNum[2] = { 1, 2 };
    VOS_UINT8 ucUniqueFlagLst[2] = { 0, 0 };
    VOS_UINT8 aucFieldIDLst[2][DB_IDX_FLD_MAX] = { {8}, {8, 9} };
    CreateV1Label(&pstFldLst, ulNCols, ucIdxFldNum, aucFieldIDLst, ucUniqueFlagLst, &pusRelId);

    // 写数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_UINT32 recordCnt = rand() % 901 + 100;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u",recordCnt );
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    char setStrVal1[101] = { 0 };
    char setStrVal2[101] = { 0 };
    const char *strVal1 = "abcde";
    const char *strVal2 = "Abcd";
    const char *strVal3 = "abc";
    const char *strVal4 = "ab";
    const char *setSteValPtr1 = NULL;
    const char *setSteValPtr2 = NULL;
    // 这个查询的数据量需要看下面查询数据设置的值
    VOS_UINT32 notUniqueVal = recordCnt + 1;
    VOS_UINT32 expectQueryCnt = 0;
    VOS_UINT32 *expectQueryVal = (VOS_UINT32 *)TEST_V1_MALLOC(sizeof(VOS_UINT32) * recordCnt);
    TEST_ASSERT_PTR_NULL(expectQueryVal);
    memset_s(expectQueryVal, sizeof(VOS_UINT32) * recordCnt, 0x00, sizeof(VOS_UINT32) * recordCnt);
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        if (i % 4 == 0) {
            setSteValPtr1 = strVal1;
            if (i < recordCnt / 2) {
                setSteValPtr2 = strVal2;
                expectQueryVal[expectQueryCnt] = notUniqueVal;
                expectQueryCnt++;
            } else {
                setSteValPtr2 = strVal3;
            }
        } else if (i % 4 == 1) {
            setSteValPtr1 = strVal2;
            setSteValPtr2 = strVal1;
        } else if (i % 4 == 2) {
            setSteValPtr1 = strVal3;
            setSteValPtr2 = strVal2;
        } else if (i % 4 == 3) {
            setSteValPtr1 = strVal4;
            setSteValPtr2 = strVal3;
        } else {
            setSteValPtr1 = strVal1;
            if (i < recordCnt / 2) {
                setSteValPtr2 = strVal2;
                expectQueryVal[expectQueryCnt] = notUniqueVal;
                expectQueryCnt++;
            } else {
                setSteValPtr2 = strVal3;
            }
        }
        sprintf_s(setStrVal1, sizeof(setStrVal1), "%s_%05u", setSteValPtr1, notUniqueVal);
        sprintf_s(setStrVal2, sizeof(setStrVal2), "%s_%05u", setSteValPtr2, notUniqueVal);
        testSetAllFieldByIndexString(&TblFieldDataDefSet, setStrVal1, setStrVal2, notUniqueVal);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    char opStrVal1[DB_ELELEN_MAX] = { 0 };
    sprintf_s(opStrVal1, sizeof(opStrVal1), "abcde_%05u_test", notUniqueVal);
    char opStrVal2[DB_ELELEN_MAX] = { 0 };
    sprintf_s(opStrVal2, sizeof(opStrVal2), "Abc");
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 2;
    pstCond.aCond[0].ucFieldId = 8;
    pstCond.aCond[0].enOp = DB_OP_PREFIX;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    strncpy((char *)pstCond.aCond[0].aucValue, opStrVal1, DB_ELELEN_MAX);
    pstCond.aCond[1].ucFieldId = 9;
    pstCond.aCond[1].enOp = DB_OP_PREFIX;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    strncpy((char *)pstCond.aCond[1].aucValue, opStrVal2, DB_ELELEN_MAX);
    testPrintCond(&pstCond, pstFldLst);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = recordCnt;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum + 1);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(expectQueryCnt, pstBufDataGet.ulRecNum);

    // 获取所有记录的数据
    char expectStrVal1[101] = { 0 };
    char expectStrVal2[101] = { 0 };
    for (VOS_UINT32 i = 0; i < pstBufDataGet.ulRecNum; i++) {
        TblFieldDataDefT tblFieldDataDefGet = {0};
        testGetAllField(pBufGet + (pstBufDataGet.usRecLen * i), &tblFieldDataDefGet, pstBufDataGet.usRecLen);
        sprintf_s(expectStrVal1, sizeof(expectStrVal1), "%s_%05u", strVal1, expectQueryVal[i]);
        sprintf_s(expectStrVal2, sizeof(expectStrVal2), "%s_%05u", strVal2, expectQueryVal[i]);
        ret = testCheckAllFieldyIndexString(&tblFieldDataDefGet, expectStrVal1, expectStrVal2, expectQueryVal[i]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pBufGet);
    TEST_V1_FREE(expectQueryVal);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecEx接口测试，DB_OP_PREFIX12，（假设1是数据，2是条件，数据包含条件）
TEST_F(Fun_SelectAllRecEx, V1Com_008_153)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    DB_FIELD_DEF_STRU *pstFldLst;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    VOS_UINT8 ucIdxFldNum[2] = { 1, 2 };
    VOS_UINT8 ucUniqueFlagLst[2] = { 1, 1 };
    VOS_UINT8 aucFieldIDLst[2][DB_IDX_FLD_MAX] = { {8}, {8, 9} };
    CreateV1Label(&pstFldLst, ulNCols, ucIdxFldNum, aucFieldIDLst, ucUniqueFlagLst, &pusRelId);

    // 写数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_UINT32 recordCnt = rand() % 901 + 100;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u",recordCnt );
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    char setStrVal1[101] = { 0 };
    char setStrVal2[101] = { 0 };
    const char *strVal1 = "abcde";
    const char *strVal2 = "Abcd";
    const char *strVal3 = "abc";
    const char *strVal4 = "ab";
    const char *setSteValPtr1 = NULL;
    const char *setSteValPtr2 = NULL;
    // 这个查询的数据量需要看下面查询数据设置的值
    VOS_UINT32 expectQueryCnt = 0;
    VOS_UINT32 *expectQueryVal = (VOS_UINT32 *)TEST_V1_MALLOC(sizeof(VOS_UINT32) * recordCnt);
    TEST_ASSERT_PTR_NULL(expectQueryVal);
    memset_s(expectQueryVal, sizeof(VOS_UINT32) * recordCnt, 0x00, sizeof(VOS_UINT32) * recordCnt);
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        if (i % 4 == 0) {
            setSteValPtr1 = strVal1;
            if (i < recordCnt / 2) {
                setSteValPtr2 = strVal2;
                expectQueryVal[expectQueryCnt] = i;
                expectQueryCnt++;
            } else {
                setSteValPtr2 = strVal3;
            }
        } else if (i % 4 == 1) {
            setSteValPtr1 = strVal2;
            setSteValPtr2 = strVal1;
        } else if (i % 4 == 2) {
            setSteValPtr1 = strVal3;
            setSteValPtr2 = strVal2;
        } else if (i % 4 == 3) {
            setSteValPtr1 = strVal4;
            setSteValPtr2 = strVal3;
        } else {
            setSteValPtr1 = strVal1;
            if (i < recordCnt / 2) {
                setSteValPtr2 = strVal2;
                expectQueryVal[expectQueryCnt] = i;
                expectQueryCnt++;
            } else {
                setSteValPtr2 = strVal3;
            }
        }
        sprintf_s(setStrVal1, sizeof(setStrVal1), "%s_%05u", setSteValPtr1, i);
        sprintf_s(setStrVal2, sizeof(setStrVal2), "%s_%05u", setSteValPtr2, i);
        testSetAllFieldByIndexString(&TblFieldDataDefSet, setStrVal1, setStrVal2, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    char opStrVal1[DB_ELELEN_MAX] = { 0 };
    sprintf_s(opStrVal1, sizeof(opStrVal1), "abcd");
    char opStrVal2[DB_ELELEN_MAX] = { 0 };
    sprintf_s(opStrVal2, sizeof(opStrVal2), "Abc");
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 2;
    pstCond.aCond[0].ucFieldId = 8;
    pstCond.aCond[0].enOp = DB_OP_PREFIX12;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    strncpy((char *)pstCond.aCond[0].aucValue, opStrVal1, DB_ELELEN_MAX);
    pstCond.aCond[1].ucFieldId = 9;
    pstCond.aCond[1].enOp = DB_OP_PREFIX12;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    strncpy((char *)pstCond.aCond[1].aucValue, opStrVal2, DB_ELELEN_MAX);
    testPrintCond(&pstCond, pstFldLst);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = recordCnt;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum + 1);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(expectQueryCnt, pstBufDataGet.ulRecNum);

    // 获取所有记录的数据
    char expectStrVal1[101] = { 0 };
    char expectStrVal2[101] = { 0 };
    for (VOS_UINT32 i = 0; i < pstBufDataGet.ulRecNum; i++) {
        TblFieldDataDefT tblFieldDataDefGet = {0};
        testGetAllField(pBufGet + (pstBufDataGet.usRecLen * i), &tblFieldDataDefGet, pstBufDataGet.usRecLen);
        sprintf_s(expectStrVal1, sizeof(expectStrVal1), "%s_%05u", strVal1, expectQueryVal[i]);
        sprintf_s(expectStrVal2, sizeof(expectStrVal2), "%s_%05u", strVal2, expectQueryVal[i]);
        ret = testCheckAllFieldyIndexString(&tblFieldDataDefGet, expectStrVal1, expectStrVal2, expectQueryVal[i]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pBufGet);
    TEST_V1_FREE(expectQueryVal);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecEx接口测试，DDB_OP_PREFIX21，（假设1是数据，2是条件，条件包含数据）
TEST_F(Fun_SelectAllRecEx, V1Com_008_154)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    DB_FIELD_DEF_STRU *pstFldLst;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    VOS_UINT8 ucIdxFldNum[2] = { 1, 2 };
    VOS_UINT8 ucUniqueFlagLst[2] = { 0, 0 };
    VOS_UINT8 aucFieldIDLst[2][DB_IDX_FLD_MAX] = { {8}, {8, 9} };
    CreateV1Label(&pstFldLst, ulNCols, ucIdxFldNum, aucFieldIDLst, ucUniqueFlagLst, &pusRelId);

    // 写数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_UINT32 recordCnt = rand() % 901 + 100;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u",recordCnt );
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    char setStrVal1[101] = { 0 };
    char setStrVal2[101] = { 0 };
    const char *strVal1 = "abcde";
    const char *strVal2 = "Abcd";
    const char *strVal3 = "abc";
    const char *strVal4 = "ab";
    const char *setSteValPtr1 = NULL;
    const char *setSteValPtr2 = NULL;
    // 这个查询的数据量需要看下面查询数据设置的值
    VOS_UINT32 notUniqueVal = recordCnt + 1;
    VOS_UINT32 expectQueryCnt = 0;
    VOS_UINT32 *expectQueryVal = (VOS_UINT32 *)TEST_V1_MALLOC(sizeof(VOS_UINT32) * recordCnt);
    TEST_ASSERT_PTR_NULL(expectQueryVal);
    memset_s(expectQueryVal, sizeof(VOS_UINT32) * recordCnt, 0x00, sizeof(VOS_UINT32) * recordCnt);
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        if (i % 4 == 0) {
            setSteValPtr1 = strVal1;
            if (i < recordCnt / 2) {
                setSteValPtr2 = strVal2;
                expectQueryVal[expectQueryCnt] = notUniqueVal;
                expectQueryCnt++;
            } else {
                setSteValPtr2 = strVal3;
            }
        } else if (i % 4 == 1) {
            setSteValPtr1 = strVal2;
            setSteValPtr2 = strVal1;
        } else if (i % 4 == 2) {
            setSteValPtr1 = strVal3;
            setSteValPtr2 = strVal2;
        } else if (i % 4 == 3) {
            setSteValPtr1 = strVal4;
            setSteValPtr2 = strVal3;
        } else {
            setSteValPtr1 = strVal1;
            if (i < recordCnt / 2) {
                setSteValPtr2 = strVal2;
                expectQueryVal[expectQueryCnt] = notUniqueVal;
                expectQueryCnt++;
            } else {
                setSteValPtr2 = strVal3;
            }
        }
        sprintf_s(setStrVal1, sizeof(setStrVal1), "%s_%05u", setSteValPtr1, notUniqueVal);
        sprintf_s(setStrVal2, sizeof(setStrVal2), "%s_%05u", setSteValPtr2, notUniqueVal);
        testSetAllFieldByIndexString(&TblFieldDataDefSet, setStrVal1, setStrVal2, notUniqueVal);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    char opStrVal1[DB_ELELEN_MAX] = { 0 };
    sprintf_s(opStrVal1, sizeof(opStrVal1), "abcde_%05u_test", notUniqueVal);
    char opStrVal2[DB_ELELEN_MAX] = { 0 };
    sprintf_s(opStrVal2, sizeof(opStrVal2), "Abcd_%05u_test", notUniqueVal);
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 2;
    pstCond.aCond[0].ucFieldId = 8;
    pstCond.aCond[0].enOp = DB_OP_PREFIX21;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    strncpy((char *)pstCond.aCond[0].aucValue, opStrVal1, DB_ELELEN_MAX);
    pstCond.aCond[1].ucFieldId = 9;
    pstCond.aCond[1].enOp = DB_OP_PREFIX21;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    strncpy((char *)pstCond.aCond[1].aucValue, opStrVal2, DB_ELELEN_MAX);
    testPrintCond(&pstCond, pstFldLst);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = recordCnt;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum + 1);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(expectQueryCnt, pstBufDataGet.ulRecNum);

    // 获取所有记录的数据
    char expectStrVal1[101] = { 0 };
    char expectStrVal2[101] = { 0 };
    for (VOS_UINT32 i = 0; i < pstBufDataGet.ulRecNum; i++) {
        TblFieldDataDefT tblFieldDataDefGet = {0};
        testGetAllField(pBufGet + (pstBufDataGet.usRecLen * i), &tblFieldDataDefGet, pstBufDataGet.usRecLen);
        sprintf_s(expectStrVal1, sizeof(expectStrVal1), "%s_%05u", strVal1, expectQueryVal[i]);
        sprintf_s(expectStrVal2, sizeof(expectStrVal2), "%s_%05u", strVal2, expectQueryVal[i]);
        ret = testCheckAllFieldyIndexString(&tblFieldDataDefGet, expectStrVal1, expectStrVal2, expectQueryVal[i]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pBufGet);
    TEST_V1_FREE(expectQueryVal);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecEx接口测试，DB_OP_MAX_PREFIX12（按顺序一个一个字符比较）
TEST_F(Fun_SelectAllRecEx, V1Com_008_155)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    DB_FIELD_DEF_STRU *pstFldLst;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    VOS_UINT8 ucIdxFldNum[2] = { 1, 2 };
    VOS_UINT8 ucUniqueFlagLst[2] = { 1, 1 };
    VOS_UINT8 aucFieldIDLst[2][DB_IDX_FLD_MAX] = { {8}, {8, 9} };
    CreateV1Label(&pstFldLst, ulNCols, ucIdxFldNum, aucFieldIDLst, ucUniqueFlagLst, &pusRelId);

    // 写数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_UINT32 recordCnt = rand() % 901 + 100;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u",recordCnt );
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    char setStrVal1[101] = { 0 };
    char setStrVal2[101] = { 0 };
    const char *strVal1 = "abcde";
    const char *strVal2 = "Abcd";
    const char *strVal3 = "abc";
    const char *strVal4 = "ab";
    const char *setSteValPtr1 = NULL;
    const char *setSteValPtr2 = NULL;
    // 这个查询的数据量需要看下面查询数据设置的值
    VOS_UINT32 expectQueryCnt = 0;
    VOS_UINT32 *expectQueryVal = (VOS_UINT32 *)TEST_V1_MALLOC(sizeof(VOS_UINT32) * recordCnt);
    TEST_ASSERT_PTR_NULL(expectQueryVal);
    memset_s(expectQueryVal, sizeof(VOS_UINT32) * recordCnt, 0x00, sizeof(VOS_UINT32) * recordCnt);
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        if (i % 4 == 0) {
            setSteValPtr1 = strVal1;
            if (i < recordCnt / 2) {
                setSteValPtr2 = strVal2;
                expectQueryVal[expectQueryCnt] = i;
                expectQueryCnt++;
            } else {
                setSteValPtr2 = strVal3;
            }
        } else if (i % 4 == 1) {
            setSteValPtr1 = strVal2;
            setSteValPtr2 = strVal1;
        } else if (i % 4 == 2) {
            setSteValPtr1 = strVal3;
            setSteValPtr2 = strVal2;
        } else if (i % 4 == 3) {
            setSteValPtr1 = strVal4;
            setSteValPtr2 = strVal3;
        } else {
            setSteValPtr1 = strVal1;
            if (i < recordCnt / 2) {
                setSteValPtr2 = strVal2;
                expectQueryVal[expectQueryCnt] = i;
                expectQueryCnt++;
            } else {
                setSteValPtr2 = strVal3;
            }
        }
        sprintf_s(setStrVal1, sizeof(setStrVal1), "%s_%05u", setSteValPtr1, i);
        sprintf_s(setStrVal2, sizeof(setStrVal2), "%s_%05u", setSteValPtr2, i);
        testSetAllFieldByIndexString(&TblFieldDataDefSet, setStrVal1, setStrVal2, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    char opStrVal1[DB_ELELEN_MAX] = { 0 };
    sprintf_s(opStrVal1, sizeof(opStrVal1), "abcd");
    char opStrVal2[DB_ELELEN_MAX] = { 0 };
    sprintf_s(opStrVal2, sizeof(opStrVal2), "Abc");
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 2;
    pstCond.aCond[0].ucFieldId = 8;
    pstCond.aCond[0].enOp = DB_OP_MAX_PREFIX12;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    strncpy((char *)pstCond.aCond[0].aucValue, opStrVal1, DB_ELELEN_MAX);
    pstCond.aCond[1].ucFieldId = 9;
    pstCond.aCond[1].enOp = DB_OP_MAX_PREFIX12;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    strncpy((char *)pstCond.aCond[1].aucValue, opStrVal2, DB_ELELEN_MAX);
    testPrintCond(&pstCond, pstFldLst);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = recordCnt;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum + 1);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstBufDataGet.ulRecNum);

    // 获取所有记录的数据
    char expectStrVal1[101] = { 0 };
    char expectStrVal2[101] = { 0 };
    for (VOS_UINT32 i = 0; i < pstBufDataGet.ulRecNum; i++) {
        TblFieldDataDefT tblFieldDataDefGet = {0};
        testGetAllField(pBufGet + (pstBufDataGet.usRecLen * i), &tblFieldDataDefGet, pstBufDataGet.usRecLen);
        sprintf_s(expectStrVal1, sizeof(expectStrVal1), "%s_%05u", strVal1, expectQueryVal[expectQueryCnt - 1]);
        sprintf_s(expectStrVal2, sizeof(expectStrVal2), "%s_%05u", strVal2, expectQueryVal[expectQueryCnt - 1]);
        ret = testCheckAllFieldyIndexString(&tblFieldDataDefGet, expectStrVal1, expectStrVal2,
            expectQueryVal[expectQueryCnt - 1]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pBufGet);
    TEST_V1_FREE(expectQueryVal);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecEx接口测试，DB_OP_MAX_PREFIX21（按顺序一个一个字符比较）
TEST_F(Fun_SelectAllRecEx, V1Com_008_156)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    DB_FIELD_DEF_STRU *pstFldLst;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    VOS_UINT8 ucIdxFldNum[2] = { 1, 2 };
    VOS_UINT8 ucUniqueFlagLst[2] = { 0, 0 };
    VOS_UINT8 aucFieldIDLst[2][DB_IDX_FLD_MAX] = { {8}, {8, 9} };
    CreateV1Label(&pstFldLst, ulNCols, ucIdxFldNum, aucFieldIDLst, ucUniqueFlagLst, &pusRelId);

    // 写数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_UINT32 recordCnt = rand() % 901 + 100;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u",recordCnt );
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    char setStrVal1[101] = { 0 };
    char setStrVal2[101] = { 0 };
    const char *strVal1 = "abcde";
    const char *strVal2 = "Abcd";
    const char *strVal3 = "abc";
    const char *strVal4 = "ab";
    const char *setSteValPtr1 = NULL;
    const char *setSteValPtr2 = NULL;
    // 这个查询的数据量需要看下面查询数据设置的值
    VOS_UINT32 notUniqueVal = recordCnt + 1;
    VOS_UINT32 expectQueryCnt = 0;
    VOS_UINT32 *expectQueryVal = (VOS_UINT32 *)TEST_V1_MALLOC(sizeof(VOS_UINT32) * recordCnt);
    TEST_ASSERT_PTR_NULL(expectQueryVal);
    memset_s(expectQueryVal, sizeof(VOS_UINT32) * recordCnt, 0x00, sizeof(VOS_UINT32) * recordCnt);
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        if (i % 4 == 0) {
            setSteValPtr1 = strVal1;
            if (i < recordCnt / 2) {
                setSteValPtr2 = strVal2;
                expectQueryVal[expectQueryCnt] = notUniqueVal;
                expectQueryCnt++;
            } else {
                setSteValPtr2 = strVal3;
            }
        } else if (i % 4 == 1) {
            setSteValPtr1 = strVal2;
            setSteValPtr2 = strVal1;
        } else if (i % 4 == 2) {
            setSteValPtr1 = strVal3;
            setSteValPtr2 = strVal2;
        } else if (i % 4 == 3) {
            setSteValPtr1 = strVal4;
            setSteValPtr2 = strVal3;
        } else {
            setSteValPtr1 = strVal1;
            if (i < recordCnt / 2) {
                setSteValPtr2 = strVal2;
                expectQueryVal[expectQueryCnt] = notUniqueVal;
                expectQueryCnt++;
            } else {
                setSteValPtr2 = strVal3;
            }
        }
        sprintf_s(setStrVal1, sizeof(setStrVal1), "%s_%05u", setSteValPtr1, notUniqueVal);
        sprintf_s(setStrVal2, sizeof(setStrVal2), "%s_%05u", setSteValPtr2, notUniqueVal);
        testSetAllFieldByIndexString(&TblFieldDataDefSet, setStrVal1, setStrVal2, notUniqueVal);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    char opStrVal1[DB_ELELEN_MAX] = { 0 };
    sprintf_s(opStrVal1, sizeof(opStrVal1), "abcde_%05u_test", notUniqueVal);
    char opStrVal2[DB_ELELEN_MAX] = { 0 };
    sprintf_s(opStrVal2, sizeof(opStrVal2), "Abcd_%05u_test", notUniqueVal);
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 2;
    pstCond.aCond[0].ucFieldId = 8;
    pstCond.aCond[0].enOp = DB_OP_MAX_PREFIX21;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    strncpy((char *)pstCond.aCond[0].aucValue, opStrVal1, DB_ELELEN_MAX);
    pstCond.aCond[1].ucFieldId = 9;
    pstCond.aCond[1].enOp = DB_OP_MAX_PREFIX21;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    strncpy((char *)pstCond.aCond[1].aucValue, opStrVal2, DB_ELELEN_MAX);
    testPrintCond(&pstCond, pstFldLst);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = recordCnt;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum + 1);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(expectQueryCnt, pstBufDataGet.ulRecNum);

    // 获取所有记录的数据
    char expectStrVal1[101] = { 0 };
    char expectStrVal2[101] = { 0 };
    for (VOS_UINT32 i = 0; i < pstBufDataGet.ulRecNum; i++) {
        TblFieldDataDefT tblFieldDataDefGet = {0};
        testGetAllField(pBufGet + (pstBufDataGet.usRecLen * i), &tblFieldDataDefGet, pstBufDataGet.usRecLen);
        sprintf_s(expectStrVal1, sizeof(expectStrVal1), "%s_%05u", strVal1, expectQueryVal[i]);
        sprintf_s(expectStrVal2, sizeof(expectStrVal2), "%s_%05u", strVal2, expectQueryVal[i]);
        ret = testCheckAllFieldyIndexString(&tblFieldDataDefGet, expectStrVal1, expectStrVal2, expectQueryVal[i]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pBufGet);
    TEST_V1_FREE(expectQueryVal);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecEx接口测试，DB_OP_MIN_PREFIX12（按顺序一个一个字符比较）
TEST_F(Fun_SelectAllRecEx, V1Com_008_157)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    DB_FIELD_DEF_STRU *pstFldLst;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    VOS_UINT8 ucIdxFldNum[2] = { 1, 2 };
    VOS_UINT8 ucUniqueFlagLst[2] = { 1, 1 };
    VOS_UINT8 aucFieldIDLst[2][DB_IDX_FLD_MAX] = { {8}, {8, 9} };
    CreateV1Label(&pstFldLst, ulNCols, ucIdxFldNum, aucFieldIDLst, ucUniqueFlagLst, &pusRelId);

    // 写数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_UINT32 recordCnt = rand() % 901 + 100;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u",recordCnt );
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    char setStrVal1[101] = { 0 };
    char setStrVal2[101] = { 0 };
    const char *strVal1 = "abcde";
    const char *strVal2 = "Abcd";
    const char *strVal3 = "abc";
    const char *strVal4 = "ab";
    const char *setSteValPtr1 = NULL;
    const char *setSteValPtr2 = NULL;
    // 这个查询的数据量需要看下面查询数据设置的值
    VOS_UINT32 expectQueryCnt = 0;
    VOS_UINT32 *expectQueryVal = (VOS_UINT32 *)TEST_V1_MALLOC(sizeof(VOS_UINT32) * recordCnt);
    TEST_ASSERT_PTR_NULL(expectQueryVal);
    memset_s(expectQueryVal, sizeof(VOS_UINT32) * recordCnt, 0x00, sizeof(VOS_UINT32) * recordCnt);
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        if (i % 4 == 0) {
            setSteValPtr1 = strVal1;
            if (i < recordCnt / 2) {
                setSteValPtr2 = strVal2;
                expectQueryVal[expectQueryCnt] = i;
                expectQueryCnt++;
            } else {
                setSteValPtr2 = strVal3;
            }
        } else if (i % 4 == 1) {
            setSteValPtr1 = strVal2;
            setSteValPtr2 = strVal1;
        } else if (i % 4 == 2) {
            setSteValPtr1 = strVal3;
            setSteValPtr2 = strVal2;
        } else if (i % 4 == 3) {
            setSteValPtr1 = strVal4;
            setSteValPtr2 = strVal3;
        } else {
            setSteValPtr1 = strVal1;
            if (i < recordCnt / 2) {
                setSteValPtr2 = strVal2;
                expectQueryVal[expectQueryCnt] = i;
                expectQueryCnt++;
            } else {
                setSteValPtr2 = strVal3;
            }
        }
        sprintf_s(setStrVal1, sizeof(setStrVal1), "%s_%05u", setSteValPtr1, i);
        sprintf_s(setStrVal2, sizeof(setStrVal2), "%s_%05u", setSteValPtr2, i);
        testSetAllFieldByIndexString(&TblFieldDataDefSet, setStrVal1, setStrVal2, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    char opStrVal1[DB_ELELEN_MAX] = { 0 };
    sprintf_s(opStrVal1, sizeof(opStrVal1), "abcd");
    char opStrVal2[DB_ELELEN_MAX] = { 0 };
    sprintf_s(opStrVal2, sizeof(opStrVal2), "Abc");
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 2;
    pstCond.aCond[0].ucFieldId = 8;
    pstCond.aCond[0].enOp = DB_OP_MIN_PREFIX12;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    strncpy((char *)pstCond.aCond[0].aucValue, opStrVal1, DB_ELELEN_MAX);
    pstCond.aCond[1].ucFieldId = 9;
    pstCond.aCond[1].enOp = DB_OP_MIN_PREFIX12;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    strncpy((char *)pstCond.aCond[1].aucValue, opStrVal2, DB_ELELEN_MAX);
    testPrintCond(&pstCond, pstFldLst);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = recordCnt;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum + 1);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstBufDataGet.ulRecNum);

    // 获取所有记录的数据
    char expectStrVal1[101] = { 0 };
    char expectStrVal2[101] = { 0 };
    for (VOS_UINT32 i = 0; i < pstBufDataGet.ulRecNum; i++) {
        TblFieldDataDefT tblFieldDataDefGet = {0};
        testGetAllField(pBufGet + (pstBufDataGet.usRecLen * i), &tblFieldDataDefGet, pstBufDataGet.usRecLen);
        sprintf_s(expectStrVal1, sizeof(expectStrVal1), "%s_%05u", strVal1, expectQueryVal[0]);
        sprintf_s(expectStrVal2, sizeof(expectStrVal2), "%s_%05u", strVal2, expectQueryVal[0]);
        ret = testCheckAllFieldyIndexString(&tblFieldDataDefGet, expectStrVal1, expectStrVal2,
            expectQueryVal[0]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pBufGet);
    TEST_V1_FREE(expectQueryVal);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecEx接口测试，DB_OP_MIN_PREFIX21（按顺序一个一个字符比较）
TEST_F(Fun_SelectAllRecEx, V1Com_008_158)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    DB_FIELD_DEF_STRU *pstFldLst;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    VOS_UINT8 ucIdxFldNum[2] = { 1, 2 };
    VOS_UINT8 ucUniqueFlagLst[2] = { 0, 0 };
    VOS_UINT8 aucFieldIDLst[2][DB_IDX_FLD_MAX] = { {8}, {8, 9} };
    CreateV1Label(&pstFldLst, ulNCols, ucIdxFldNum, aucFieldIDLst, ucUniqueFlagLst, &pusRelId);

    // 写数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_UINT32 recordCnt = rand() % 901 + 100;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u",recordCnt );
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    char setStrVal1[101] = { 0 };
    char setStrVal2[101] = { 0 };
    const char *strVal1 = "abcde";
    const char *strVal2 = "Abcd";
    const char *strVal3 = "abc";
    const char *strVal4 = "ab";
    const char *setSteValPtr1 = NULL;
    const char *setSteValPtr2 = NULL;
    // 这个查询的数据量需要看下面查询数据设置的值
    VOS_UINT32 notUniqueVal = recordCnt + 1;
    VOS_UINT32 expectQueryCnt = 0;
    VOS_UINT32 *expectQueryVal = (VOS_UINT32 *)TEST_V1_MALLOC(sizeof(VOS_UINT32) * recordCnt);
    TEST_ASSERT_PTR_NULL(expectQueryVal);
    memset_s(expectQueryVal, sizeof(VOS_UINT32) * recordCnt, 0x00, sizeof(VOS_UINT32) * recordCnt);
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        if (i % 4 == 0) {
            setSteValPtr1 = strVal1;
            if (i < recordCnt / 2) {
                setSteValPtr2 = strVal2;
                expectQueryVal[expectQueryCnt] = notUniqueVal;
                expectQueryCnt++;
            } else {
                setSteValPtr2 = strVal3;
            }
        } else if (i % 4 == 1) {
            setSteValPtr1 = strVal2;
            setSteValPtr2 = strVal1;
        } else if (i % 4 == 2) {
            setSteValPtr1 = strVal3;
            setSteValPtr2 = strVal2;
        } else if (i % 4 == 3) {
            setSteValPtr1 = strVal4;
            setSteValPtr2 = strVal3;
        } else {
            setSteValPtr1 = strVal1;
            if (i < recordCnt / 2) {
                setSteValPtr2 = strVal2;
                expectQueryVal[expectQueryCnt] = notUniqueVal;
                expectQueryCnt++;
            } else {
                setSteValPtr2 = strVal3;
            }
        }
        sprintf_s(setStrVal1, sizeof(setStrVal1), "%s_%05u", setSteValPtr1, notUniqueVal);
        sprintf_s(setStrVal2, sizeof(setStrVal2), "%s_%05u", setSteValPtr2, notUniqueVal);
        testSetAllFieldByIndexString(&TblFieldDataDefSet, setStrVal1, setStrVal2, notUniqueVal);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    char opStrVal1[DB_ELELEN_MAX] = { 0 };
    sprintf_s(opStrVal1, sizeof(opStrVal1), "abcde_%05u_test", notUniqueVal);
    char opStrVal2[DB_ELELEN_MAX] = { 0 };
    sprintf_s(opStrVal2, sizeof(opStrVal2), "Abcd_%05u_test", notUniqueVal);
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 2;
    pstCond.aCond[0].ucFieldId = 8;
    pstCond.aCond[0].enOp = DB_OP_MIN_PREFIX21;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    strncpy((char *)pstCond.aCond[0].aucValue, opStrVal1, DB_ELELEN_MAX);
    pstCond.aCond[1].ucFieldId = 9;
    pstCond.aCond[1].enOp = DB_OP_MIN_PREFIX21;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    strncpy((char *)pstCond.aCond[1].aucValue, opStrVal2, DB_ELELEN_MAX);
    testPrintCond(&pstCond, pstFldLst);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = recordCnt;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum + 1);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(expectQueryCnt, pstBufDataGet.ulRecNum);

    // 获取所有记录的数据
    char expectStrVal1[101] = { 0 };
    char expectStrVal2[101] = { 0 };
    for (VOS_UINT32 i = 0; i < pstBufDataGet.ulRecNum; i++) {
        TblFieldDataDefT tblFieldDataDefGet = {0};
        testGetAllField(pBufGet + (pstBufDataGet.usRecLen * i), &tblFieldDataDefGet, pstBufDataGet.usRecLen);
        sprintf_s(expectStrVal1, sizeof(expectStrVal1), "%s_%05u", strVal1, expectQueryVal[i]);
        sprintf_s(expectStrVal2, sizeof(expectStrVal2), "%s_%05u", strVal2, expectQueryVal[i]);
        ret = testCheckAllFieldyIndexString(&tblFieldDataDefGet, expectStrVal1, expectStrVal2, expectQueryVal[i]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pBufGet);
    TEST_V1_FREE(expectQueryVal);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// TPC_SelectAllRecEx接口测试，DB_OP_POSTFIX21
TEST_F(Fun_SelectAllRecEx, V1Com_008_159)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    DB_FIELD_DEF_STRU *pstFldLst;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    VOS_UINT8 ucIdxFldNum[2] = { 1, 2 };
    VOS_UINT8 ucUniqueFlagLst[2] = { 0, 0 };
    VOS_UINT8 aucFieldIDLst[2][DB_IDX_FLD_MAX] = { {8}, {8, 9} };
    CreateV1Label(&pstFldLst, ulNCols, ucIdxFldNum, aucFieldIDLst, ucUniqueFlagLst, &pusRelId);

    // 写数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_UINT32 recordCnt = rand() % 901 + 100;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u",recordCnt );
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    char setStrVal1[101] = { 0 };
    char setStrVal2[101] = { 0 };
    const char *strVal1 = "abcde";
    const char *strVal2 = "Abcd";
    const char *strVal3 = "abc";
    const char *strVal4 = "ab";
    const char *setSteValPtr1 = NULL;
    const char *setSteValPtr2 = NULL;
    // 这个查询的数据量需要看下面查询数据设置的值
    VOS_UINT32 notUniqueVal = recordCnt + 1;
    VOS_UINT32 expectQueryCnt = 0;
    VOS_UINT32 *expectQueryVal = (VOS_UINT32 *)TEST_V1_MALLOC(sizeof(VOS_UINT32) * recordCnt);
    TEST_ASSERT_PTR_NULL(expectQueryVal);
    memset_s(expectQueryVal, sizeof(VOS_UINT32) * recordCnt, 0x00, sizeof(VOS_UINT32) * recordCnt);
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        if (i % 4 == 0) {
            setSteValPtr1 = strVal1;
            if (i < recordCnt / 2) {
                setSteValPtr2 = strVal2;
                expectQueryVal[expectQueryCnt] = notUniqueVal;
                expectQueryCnt++;
            } else {
                setSteValPtr2 = strVal3;
            }
        } else if (i % 4 == 1) {
            setSteValPtr1 = strVal2;
            setSteValPtr2 = strVal1;
        } else if (i % 4 == 2) {
            setSteValPtr1 = strVal3;
            setSteValPtr2 = strVal2;
        } else if (i % 4 == 3) {
            setSteValPtr1 = strVal4;
            setSteValPtr2 = strVal3;
        } else {
            setSteValPtr1 = strVal1;
            if (i < recordCnt / 2) {
                setSteValPtr2 = strVal2;
                expectQueryVal[expectQueryCnt] = notUniqueVal;
                expectQueryCnt++;
            } else {
                setSteValPtr2 = strVal3;
            }
        }
        sprintf_s(setStrVal1, sizeof(setStrVal1), "%s_%05u", setSteValPtr1, notUniqueVal);
        sprintf_s(setStrVal2, sizeof(setStrVal2), "%s_%05u", setSteValPtr2, notUniqueVal);
        testSetAllFieldByIndexString(&TblFieldDataDefSet, setStrVal1, setStrVal2, notUniqueVal);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    char opStrVal1[DB_ELELEN_MAX] = { 0 };
    sprintf_s(opStrVal1, sizeof(opStrVal1), "hijkabcde_%05u", notUniqueVal);
    char opStrVal2[DB_ELELEN_MAX] = { 0 };
    sprintf_s(opStrVal2, sizeof(opStrVal2), "hijkAbcd_%05u", notUniqueVal);
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 2;
    pstCond.aCond[0].ucFieldId = 8;
    pstCond.aCond[0].enOp = DB_OP_POSTFIX21;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    strncpy((char *)pstCond.aCond[0].aucValue, opStrVal1, DB_ELELEN_MAX);
    pstCond.aCond[1].ucFieldId = 9;
    pstCond.aCond[1].enOp = DB_OP_POSTFIX21;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    strncpy((char *)pstCond.aCond[1].aucValue, opStrVal2, DB_ELELEN_MAX);
    testPrintCond(&pstCond, pstFldLst);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = recordCnt;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum + 1);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(expectQueryCnt, pstBufDataGet.ulRecNum);

    // 获取所有记录的数据
    char expectStrVal1[101] = { 0 };
    char expectStrVal2[101] = { 0 };
    for (VOS_UINT32 i = 0; i < pstBufDataGet.ulRecNum; i++) {
        TblFieldDataDefT tblFieldDataDefGet = {0};
        testGetAllField(pBufGet + (pstBufDataGet.usRecLen * i), &tblFieldDataDefGet, pstBufDataGet.usRecLen);
        sprintf_s(expectStrVal1, sizeof(expectStrVal1), "%s_%05u", strVal1, expectQueryVal[i]);
        sprintf_s(expectStrVal2, sizeof(expectStrVal2), "%s_%05u", strVal2, expectQueryVal[i]);
        ret = testCheckAllFieldyIndexString(&tblFieldDataDefGet, expectStrVal1, expectStrVal2, expectQueryVal[i]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pBufGet);
    TEST_V1_FREE(expectQueryVal);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecEx接口测试，DB_OP_MAX_POSTFIX21
TEST_F(Fun_SelectAllRecEx, V1Com_008_160)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    DB_FIELD_DEF_STRU *pstFldLst;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    VOS_UINT8 ucIdxFldNum[2] = { 1, 2 };
    VOS_UINT8 ucUniqueFlagLst[2] = { 0, 0 };
    VOS_UINT8 aucFieldIDLst[2][DB_IDX_FLD_MAX] = { {8}, {8, 9} };
    CreateV1Label(&pstFldLst, ulNCols, ucIdxFldNum, aucFieldIDLst, ucUniqueFlagLst, &pusRelId);

    // 写数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_UINT32 recordCnt = rand() % 901 + 100;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u",recordCnt );
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    char setStrVal1[101] = { 0 };
    char setStrVal2[101] = { 0 };
    const char *strVal1 = "abcde";
    const char *strVal2 = "Abcd";
    const char *strVal3 = "cde";
    const char *strVal4 = "ab";
    const char *setSteValPtr1 = NULL;
    const char *setSteValPtr2 = NULL;
    // 这个查询的数据量需要看下面查询数据设置的值
    VOS_UINT32 notUniqueVal = recordCnt + 1;
    VOS_UINT32 expectQueryCnt = 0;
    VOS_UINT32 *expectQueryVal = (VOS_UINT32 *)TEST_V1_MALLOC(sizeof(VOS_UINT32) * recordCnt);
    TEST_ASSERT_PTR_NULL(expectQueryVal);
    memset_s(expectQueryVal, sizeof(VOS_UINT32) * recordCnt, 0x00, sizeof(VOS_UINT32) * recordCnt);
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        if (i % 4 == 0) {
            setSteValPtr1 = strVal1;
            if (i < recordCnt / 2) {
                setSteValPtr2 = strVal2;
            } else {
                setSteValPtr2 = strVal3;
            }
        } else if (i % 4 == 1) {
            setSteValPtr1 = strVal2;
            setSteValPtr2 = strVal1;
        } else if (i % 4 == 2) {
            setSteValPtr1 = strVal3;
            setSteValPtr2 = strVal2;
            expectQueryVal[expectQueryCnt] = i;
            expectQueryCnt++;
        } else if (i % 4 == 3) {
            setSteValPtr1 = strVal4;
            setSteValPtr2 = strVal3;
        } else {
            setSteValPtr1 = strVal1;
            if (i < recordCnt / 2) {
                setSteValPtr2 = strVal2;
            } else {
                setSteValPtr2 = strVal3;
            }
        }
        sprintf_s(setStrVal1, sizeof(setStrVal1), "%s", setSteValPtr1);
        sprintf_s(setStrVal2, sizeof(setStrVal2), "%s", setSteValPtr2);
        testSetAllFieldByIndexString(&TblFieldDataDefSet, setStrVal1, setStrVal2, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    char opStrVal1[DB_ELELEN_MAX] = { 0 };
    sprintf_s(opStrVal1, sizeof(opStrVal1), "hijkabcde");
    char opStrVal2[DB_ELELEN_MAX] = { 0 };
    sprintf_s(opStrVal2, sizeof(opStrVal2), "hijkAbcd");
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 2;
    pstCond.aCond[0].ucFieldId = 8;
    pstCond.aCond[0].enOp = DB_OP_MAX_POSTFIX21;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    strncpy((char *)pstCond.aCond[0].aucValue, opStrVal1, DB_ELELEN_MAX);
    pstCond.aCond[1].ucFieldId = 9;
    pstCond.aCond[1].enOp = DB_OP_MAX_POSTFIX21;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    strncpy((char *)pstCond.aCond[1].aucValue, opStrVal2, DB_ELELEN_MAX);
    testPrintCond(&pstCond, pstFldLst);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = recordCnt;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum + 1);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(expectQueryCnt, pstBufDataGet.ulRecNum);

    // 获取所有记录的数据
    char expectStrVal1[101] = { 0 };
    char expectStrVal2[101] = { 0 };
    for (VOS_UINT32 i = 0; i < pstBufDataGet.ulRecNum; i++) {
        TblFieldDataDefT tblFieldDataDefGet = {0};
        testGetAllField(pBufGet + (pstBufDataGet.usRecLen * i), &tblFieldDataDefGet, pstBufDataGet.usRecLen);
        sprintf_s(expectStrVal1, sizeof(expectStrVal1), "%s", strVal3);
        sprintf_s(expectStrVal2, sizeof(expectStrVal2), "%s", strVal2);
        ret = testCheckAllFieldyIndexString(&tblFieldDataDefGet, expectStrVal1, expectStrVal2, expectQueryVal[i]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pBufGet);
    TEST_V1_FREE(expectQueryVal);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecEx接口测试，DB_OP_LIKE
TEST_F(Fun_SelectAllRecEx, V1Com_008_161)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    DB_FIELD_DEF_STRU *pstFldLst;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    VOS_UINT8 ucIdxFldNum[2] = { 1, 2 };
    VOS_UINT8 ucUniqueFlagLst[2] = { 0, 0 };
    VOS_UINT8 aucFieldIDLst[2][DB_IDX_FLD_MAX] = { {8}, {8, 9} };
    CreateV1Label(&pstFldLst, ulNCols, ucIdxFldNum, aucFieldIDLst, ucUniqueFlagLst, &pusRelId);

    // 写数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_UINT32 recordCnt = rand() % 901 + 100;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u",recordCnt );
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    char setStrVal1[101] = { 0 };
    char setStrVal2[101] = { 0 };
    const char *strVal1 = "abcd%ef%";
    const char *strVal2 = "b_cd_";
    const char *strVal3 = "abcfe";
    const char *strVal4 = "abefg";
    const char *setSteValPtr1 = NULL;
    const char *setSteValPtr2 = NULL;
    // 这个查询的数据量需要看下面查询数据设置的值
    VOS_UINT32 notUniqueVal = recordCnt + 1;
    VOS_UINT32 expectQueryCnt = 0;
    VOS_UINT32 *expectQueryVal = (VOS_UINT32 *)TEST_V1_MALLOC(sizeof(VOS_UINT32) * recordCnt);
    TEST_ASSERT_PTR_NULL(expectQueryVal);
    memset_s(expectQueryVal, sizeof(VOS_UINT32) * recordCnt, 0x00, sizeof(VOS_UINT32) * recordCnt);
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        if (i % 4 == 0) {
            setSteValPtr1 = strVal1;
            if (i < recordCnt / 2) {
                setSteValPtr2 = strVal2;
                expectQueryVal[expectQueryCnt] = notUniqueVal;
                expectQueryCnt++;
            } else {
                setSteValPtr2 = strVal3;
            }
        } else if (i % 4 == 1) {
            setSteValPtr1 = strVal2;
            setSteValPtr2 = strVal1;
        } else if (i % 4 == 2) {
            setSteValPtr1 = strVal3;
            setSteValPtr2 = strVal2;
        } else if (i % 4 == 3) {
            setSteValPtr1 = strVal4;
            setSteValPtr2 = strVal3;
        } else {
            setSteValPtr1 = strVal1;
            if (i < recordCnt / 2) {
                setSteValPtr2 = strVal2;
                expectQueryVal[expectQueryCnt] = notUniqueVal;
                expectQueryCnt++;
            } else {
                setSteValPtr2 = strVal3;
            }
        }
        sprintf_s(setStrVal1, sizeof(setStrVal1), "%05u_%s", notUniqueVal, setSteValPtr1);
        sprintf_s(setStrVal2, sizeof(setStrVal2), "%s", setSteValPtr2);
        testSetAllFieldByIndexString(&TblFieldDataDefSet, setStrVal1, setStrVal2, notUniqueVal);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    char opStrVal1[DB_ELELEN_MAX] = { 0 };
    sprintf_s(opStrVal1, sizeof(opStrVal1), "%%ef%%");
    char opStrVal2[DB_ELELEN_MAX] = { 0 };
    sprintf_s(opStrVal2, sizeof(opStrVal2), "__cd_");
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 2;
    pstCond.aCond[0].ucFieldId = 8;
    pstCond.aCond[0].enOp = DB_OP_LIKE;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    strncpy((char *)pstCond.aCond[0].aucValue, opStrVal1, DB_ELELEN_MAX);
    pstCond.aCond[1].ucFieldId = 9;
    pstCond.aCond[1].enOp = DB_OP_LIKE;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    strncpy((char *)pstCond.aCond[1].aucValue, opStrVal2, DB_ELELEN_MAX);
    testPrintCond(&pstCond, pstFldLst);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = recordCnt;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum + 1);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(expectQueryCnt, pstBufDataGet.ulRecNum);

    // 获取所有记录的数据
    char expectStrVal1[101] = { 0 };
    char expectStrVal2[101] = { 0 };
    for (VOS_UINT32 i = 0; i < pstBufDataGet.ulRecNum; i++) {
        TblFieldDataDefT tblFieldDataDefGet = {0};
        testGetAllField(pBufGet + (pstBufDataGet.usRecLen * i), &tblFieldDataDefGet, pstBufDataGet.usRecLen);
        sprintf_s(expectStrVal1, sizeof(expectStrVal1), "%05u_%s", expectQueryVal[i], strVal1);
        sprintf_s(expectStrVal2, sizeof(expectStrVal2), "%s", strVal2);
        ret = testCheckAllFieldyIndexString(&tblFieldDataDefGet, expectStrVal1, expectStrVal2, expectQueryVal[i]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pBufGet);
    TEST_V1_FREE(expectQueryVal);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecEx接口测试，DB_OP_LIKE
TEST_F(Fun_SelectAllRecEx, V1Com_008_162)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    DB_FIELD_DEF_STRU *pstFldLst;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    VOS_UINT8 ucIdxFldNum[2] = { 1, 2 };
    VOS_UINT8 ucUniqueFlagLst[2] = { 0, 0 };
    VOS_UINT8 aucFieldIDLst[2][DB_IDX_FLD_MAX] = { {8}, {8, 9} };
    CreateV1Label(&pstFldLst, ulNCols, ucIdxFldNum, aucFieldIDLst, ucUniqueFlagLst, &pusRelId);

    // 写数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_UINT32 recordCnt = rand() % 901 + 100;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u",recordCnt );
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    char setStrVal1[101] = { 0 };
    char setStrVal2[101] = { 0 };
    const char *strVal1 = "abcdef";
    const char *strVal2 = "bcd";
    const char *strVal3 = "abcde";
    const char *strVal4 = "abefg";
    const char *setSteValPtr1 = NULL;
    const char *setSteValPtr2 = NULL;
    // 这个查询的数据量需要看下面查询数据设置的值
    VOS_UINT32 notUniqueVal = recordCnt + 1;
    VOS_UINT32 expectQueryCnt = 0;
    VOS_UINT32 *expectQueryVal = (VOS_UINT32 *)TEST_V1_MALLOC(sizeof(VOS_UINT32) * recordCnt);
    TEST_ASSERT_PTR_NULL(expectQueryVal);
    memset_s(expectQueryVal, sizeof(VOS_UINT32) * recordCnt, 0x00, sizeof(VOS_UINT32) * recordCnt);
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        if (i % 4 == 0) {
            setSteValPtr1 = strVal1;
            if (i < recordCnt / 2) {
                setSteValPtr2 = strVal2;
                expectQueryVal[expectQueryCnt] = notUniqueVal;
                expectQueryCnt++;
            } else {
                setSteValPtr2 = strVal3;
            }
        } else if (i % 4 == 1) {
            setSteValPtr1 = strVal2;
            setSteValPtr2 = strVal1;
        } else if (i % 4 == 2) {
            setSteValPtr1 = strVal3;
            setSteValPtr2 = strVal2;
        } else if (i % 4 == 3) {
            setSteValPtr1 = strVal4;
            setSteValPtr2 = strVal3;
        } else {
            setSteValPtr1 = strVal1;
            if (i < recordCnt / 2) {
                setSteValPtr2 = strVal2;
                expectQueryVal[expectQueryCnt] = notUniqueVal;
                expectQueryCnt++;
            } else {
                setSteValPtr2 = strVal3;
            }
        }
        sprintf_s(setStrVal1, sizeof(setStrVal1), "%05u_%s", notUniqueVal, setSteValPtr1);
        sprintf_s(setStrVal2, sizeof(setStrVal2), "%s", setSteValPtr2);
        testSetAllFieldByIndexString(&TblFieldDataDefSet, setStrVal1, setStrVal2, notUniqueVal);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    char opStrVal1[DB_ELELEN_MAX] = { 0 };
    sprintf_s(opStrVal1, sizeof(opStrVal1), "%%ef");
    char opStrVal2[DB_ELELEN_MAX] = { 0 };
    sprintf_s(opStrVal2, sizeof(opStrVal2), "_cd");
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 2;
    pstCond.aCond[0].ucFieldId = 8;
    pstCond.aCond[0].enOp = DB_OP_LIKE;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    strncpy((char *)pstCond.aCond[0].aucValue, opStrVal1, DB_ELELEN_MAX);
    pstCond.aCond[1].ucFieldId = 9;
    pstCond.aCond[1].enOp = DB_OP_LIKE;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    strncpy((char *)pstCond.aCond[1].aucValue, opStrVal2, DB_ELELEN_MAX);
    testPrintCond(&pstCond, pstFldLst);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = recordCnt;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum + 1);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(expectQueryCnt, pstBufDataGet.ulRecNum);

    // 获取所有记录的数据
    char expectStrVal1[101] = { 0 };
    char expectStrVal2[101] = { 0 };
    for (VOS_UINT32 i = 0; i < pstBufDataGet.ulRecNum; i++) {
        TblFieldDataDefT tblFieldDataDefGet = {0};
        testGetAllField(pBufGet + (pstBufDataGet.usRecLen * i), &tblFieldDataDefGet, pstBufDataGet.usRecLen);
        sprintf_s(expectStrVal1, sizeof(expectStrVal1), "%05u_%s", expectQueryVal[i], strVal1);
        sprintf_s(expectStrVal2, sizeof(expectStrVal2), "%s", strVal2);
        ret = testCheckAllFieldyIndexString(&tblFieldDataDefGet, expectStrVal1, expectStrVal2, expectQueryVal[i]);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pBufGet);
    TEST_V1_FREE(expectQueryVal);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecEx接口测试，DB_OP_NOLIKE
TEST_F(Fun_SelectAllRecEx, V1Com_008_163)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    DB_FIELD_DEF_STRU *pstFldLst;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    VOS_UINT8 ucIdxFldNum[2] = { 1, 2 };
    VOS_UINT8 ucUniqueFlagLst[2] = { 0, 0 };
    VOS_UINT8 aucFieldIDLst[2][DB_IDX_FLD_MAX] = { {8}, {8, 9} };
    CreateV1Label(&pstFldLst, ulNCols, ucIdxFldNum, aucFieldIDLst, ucUniqueFlagLst, &pusRelId);

    // 写数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_UINT32 recordCnt = rand() % 901 + 100;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u",recordCnt );
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    char setStrVal1[101] = { 0 };
    char setStrVal2[101] = { 0 };
    const char *strVal1 = "abcdef";
    const char *strVal2 = "bcd";
    const char *strVal3 = "abcde";
    const char *strVal4 = "abefg";
    const char *setSteValPtr1 = NULL;
    const char *setSteValPtr2 = NULL;
    // 这个查询的数据量需要看下面查询数据设置的值
    VOS_UINT32 notUniqueVal = recordCnt + 1;
    VOS_UINT32 expectQueryCnt = 0;
    VOS_UINT32 *expectQueryVal = (VOS_UINT32 *)TEST_V1_MALLOC(sizeof(VOS_UINT32) * recordCnt);
    TEST_ASSERT_PTR_NULL(expectQueryVal);
    memset_s(expectQueryVal, sizeof(VOS_UINT32) * recordCnt, 0x00, sizeof(VOS_UINT32) * recordCnt);
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        if (i % 4 == 0) {
            setSteValPtr1 = strVal1;
            if (i < recordCnt / 2) {
                setSteValPtr2 = strVal2;
            } else {
                setSteValPtr2 = strVal3;
            }
        } else if (i % 4 == 1) {
            setSteValPtr1 = strVal2;
            setSteValPtr2 = strVal1;
            expectQueryVal[expectQueryCnt] = i;
            expectQueryCnt++;
        } else if (i % 4 == 2) {
            setSteValPtr1 = strVal3;
            setSteValPtr2 = strVal2;
        } else if (i % 4 == 3) {
            setSteValPtr1 = strVal4;
            setSteValPtr2 = strVal3;
            expectQueryVal[expectQueryCnt] = i;
            expectQueryCnt++;
        } else {
            setSteValPtr1 = strVal1;
            if (i < recordCnt / 2) {
                setSteValPtr2 = strVal2;
            } else {
                setSteValPtr2 = strVal3;
            }
        }
        sprintf_s(setStrVal1, sizeof(setStrVal1), "%05u_%s", notUniqueVal, setSteValPtr1);
        sprintf_s(setStrVal2, sizeof(setStrVal2), "%s", setSteValPtr2);
        testSetAllFieldByIndexString(&TblFieldDataDefSet, setStrVal1, setStrVal2, notUniqueVal);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    char opStrVal1[DB_ELELEN_MAX] = { 0 };
    sprintf_s(opStrVal1, sizeof(opStrVal1), "%%ef");
    char opStrVal2[DB_ELELEN_MAX] = { 0 };
    sprintf_s(opStrVal2, sizeof(opStrVal2), "_cd");
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 2;
    pstCond.aCond[0].ucFieldId = 8;
    pstCond.aCond[0].enOp = DB_OP_NOTLIKE;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    strncpy((char *)pstCond.aCond[0].aucValue, opStrVal1, DB_ELELEN_MAX);
    pstCond.aCond[1].ucFieldId = 9;
    pstCond.aCond[1].enOp = DB_OP_NOTLIKE;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    strncpy((char *)pstCond.aCond[1].aucValue, opStrVal2, DB_ELELEN_MAX);
    testPrintCond(&pstCond, pstFldLst);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = recordCnt;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum + 1);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(expectQueryCnt, pstBufDataGet.ulRecNum);

    // 获取所有记录的数据
    char expectStrVal1[101] = { 0 };
    char expectStrVal2[101] = { 0 };
    for (VOS_UINT32 i = 0; i < pstBufDataGet.ulRecNum; i++) {
        TblFieldDataDefT tblFieldDataDefGet = {0};
        testGetAllField(pBufGet + (pstBufDataGet.usRecLen * i), &tblFieldDataDefGet, pstBufDataGet.usRecLen);
        bool isCheckSucc = false;
        for (VOS_UINT32 j = 0; j < pstBufDataGet.ulRecNum; j++) {
            if (expectQueryVal[j] % 4 == 1) {
                sprintf_s(expectStrVal1, sizeof(expectStrVal1), "%05u_%s", notUniqueVal, strVal2);
                sprintf_s(expectStrVal2, sizeof(expectStrVal2), "%s", strVal1);
            } else {
                sprintf_s(expectStrVal1, sizeof(expectStrVal1), "%05u_%s", notUniqueVal, strVal4);
                sprintf_s(expectStrVal2, sizeof(expectStrVal2), "%s", strVal3);
            }
            ret = testCheckAllFieldyIndexString(&tblFieldDataDefGet, expectStrVal1, expectStrVal2, notUniqueVal);
            if (ret == DB_SUCCESS_V1) {
                isCheckSucc = true;
                break;
            }
        }
        V1_AW_MACRO_ASSERT_EQ_INT(1, isCheckSucc);
    }
    TEST_V1_FREE(pBufGet);
    TEST_V1_FREE(expectQueryVal);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecEx接口测试，简单和复杂运算的交互
TEST_F(Fun_SelectAllRecEx, V1Com_008_164)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    DB_FIELD_DEF_STRU *pstFldLst;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    VOS_UINT8 ucIdxFldNum[2] = { 1, 3 };
    VOS_UINT8 ucUniqueFlagLst[2] = { 1, 1 };
    VOS_UINT8 aucFieldIDLst[2][DB_IDX_FLD_MAX] = { {0}, {0, 1, 2} };
    CreateV1Label(&pstFldLst, ulNCols, ucIdxFldNum, aucFieldIDLst, ucUniqueFlagLst, &pusRelId);

    // 写数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_UINT32 recordCnt = rand() % 901 + 100;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u",recordCnt );
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        if (i % 2 == 0) {
            testSetAllField(&TblFieldDataDefSet, i, i, "str");
        } else {
            testSetAllField(&TblFieldDataDefSet, i, i, "mystr");
        }
        testSetAllField(&TblFieldDataDefSet, i, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_INT32 opVal = rand() % (recordCnt - 70) + 40;
    char opStrVal[DB_ELELEN_MAX] = { 0 };
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 6;
    pstCond.aCond[0].ucFieldId = 2;
    pstCond.aCond[0].enOp = DB_OP_MAX_LESS_EQUAL;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_INT32 *)pstCond.aCond[0].aucValue = opVal;
    pstCond.aCond[1].ucFieldId = 0;
    pstCond.aCond[1].enOp = DB_OP_LARGEREQUAL;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_UINT32 *)pstCond.aCond[1].aucValue = opVal - 10;
    pstCond.aCond[2].ucFieldId = 1;
    pstCond.aCond[2].enOp = DB_OP_LESS;
    memset_s((char *)pstCond.aCond[2].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_UINT32 *)pstCond.aCond[2].aucValue = opVal + 20;
    pstCond.aCond[3].ucFieldId = 8;
    pstCond.aCond[3].enOp = DB_OP_PREFIX12;
    memset_s((char *)pstCond.aCond[3].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    sprintf_s(opStrVal, sizeof(opStrVal), "str");
    strncpy((char *)pstCond.aCond[3].aucValue, opStrVal, DB_ELELEN_MAX);
    pstCond.aCond[4].ucFieldId = 9;
    pstCond.aCond[4].enOp = DB_OP_MIN_PREFIX12;
    memset_s((char *)pstCond.aCond[4].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    sprintf_s(opStrVal, sizeof(opStrVal), "str");
    strncpy((char *)pstCond.aCond[4].aucValue, opStrVal, DB_ELELEN_MAX);
    pstCond.aCond[5].ucFieldId = 9;
    pstCond.aCond[5].enOp = DB_OP_LIKE;
    memset_s((char *)pstCond.aCond[5].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    sprintf_s(opStrVal, sizeof(opStrVal), "str%%_");
    strncpy((char *)pstCond.aCond[5].aucValue, opStrVal, DB_ELELEN_MAX);
    testPrintCond(&pstCond, pstFldLst);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = recordCnt;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum + 1);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(1, pstBufDataGet.ulRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstBufDataGet.ulRecNum; i++) {
        TblFieldDataDefT tblFieldDataDefGet = {0};
        testGetAllField(pBufGet + (pstBufDataGet.usRecLen * i), &tblFieldDataDefGet, pstBufDataGet.usRecLen);
        ret = testCheckAllField(&tblFieldDataDefGet, opVal, opVal);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pBufGet);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecEx接口测试，相互矛盾的条件（过滤不到）
TEST_F(Fun_SelectAllRecEx, V1Com_008_165)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 建表
    DB_REL_DEF_STRU pstRelDef;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    ret = TestTPC_CreateTbl(g_queryTestDbId, "schema_file/test_043.json", &pusRelId, &pstRelDef);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));

    // 写数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_UINT32 recordCnt = rand() % 901 + 100;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u",recordCnt );
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstRelDef.pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        if (i % 2 == 0) {
            testSetAllField(&TblFieldDataDefSet, i, i, "str");
        } else {
            testSetAllField(&TblFieldDataDefSet, i, i, "mystr");
        }
        testSetAllField(&TblFieldDataDefSet, i, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstRelDef.pstFldLst, ulNCols, recordCnt);

    // 查询数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_INT32 opVal = rand() % (recordCnt - 70) + 40;
    char opStrVal[DB_ELELEN_MAX] = { 0 };
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 4;
    pstCond.aCond[0].ucFieldId = 2;
    pstCond.aCond[0].enOp = DB_OP_MAX_LESS_EQUAL;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_INT32 *)pstCond.aCond[0].aucValue = opVal;
    pstCond.aCond[1].ucFieldId = 0;
    pstCond.aCond[1].enOp = DB_OP_LARGEREQUAL;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_UINT32 *)pstCond.aCond[1].aucValue = opVal - 10;
    pstCond.aCond[2].ucFieldId = 1;
    pstCond.aCond[2].enOp = DB_OP_LESS;
    memset_s((char *)pstCond.aCond[2].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_UINT32 *)pstCond.aCond[2].aucValue = opVal - 10;
    pstCond.aCond[3].ucFieldId = 8;
    pstCond.aCond[3].enOp = DB_OP_PREFIX12;
    memset_s((char *)pstCond.aCond[3].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    sprintf_s(opStrVal, sizeof(opStrVal), "str");
    strncpy((char *)pstCond.aCond[3].aucValue, opStrVal, DB_ELELEN_MAX);
    testPrintCond(&pstCond, pstRelDef.pstFldLst);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstRelDef.pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = recordCnt;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum + 1);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(VOS_ERRNO_DB_RECNOTEXIST, V1ErrCode(ret, VOS_ERRNO_DB_RECNOTEXIST));
    TEST_V1_FREE(pBufGet);

    // 释放字段定义和索引定义的内存
    TestFreeTblStructDef(&pstRelDef);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// TPC_SelectAllRecEx接口测试，DB_OP_NOTEQUAL(不涉及索引), 多个过滤条件，索引字段全部匹配，不按顺序
TEST_F(Fun_SelectAllRecEx, V1Com_008_166)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    DB_FIELD_DEF_STRU *pstFldLst;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    VOS_UINT8 ucIdxFldNum[2] = { 1, 3 };
    VOS_UINT8 ucUniqueFlagLst[2] = { 1, 1 };
    VOS_UINT8 aucFieldIDLst[2][DB_IDX_FLD_MAX] = { {0}, {0, 1, 2} };
    CreateV1Label(&pstFldLst, ulNCols, ucIdxFldNum, aucFieldIDLst, ucUniqueFlagLst, &pusRelId);

    // 写数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_UINT32 recordCnt = rand() % 901 + 100;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u",recordCnt );
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_INT32 opVal = rand() % (recordCnt - 70) + 40;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 4;
    pstCond.aCond[0].ucFieldId = 2;
    pstCond.aCond[0].enOp = DB_OP_NOTEQUAL;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_INT32 *)pstCond.aCond[0].aucValue = opVal;
    pstCond.aCond[1].ucFieldId = 0;
    pstCond.aCond[1].enOp = DB_OP_NOTEQUAL;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_UINT32 *)pstCond.aCond[1].aucValue = opVal;
    pstCond.aCond[2].ucFieldId = 1;
    pstCond.aCond[2].enOp = DB_OP_NOTEQUAL;
    memset_s((char *)pstCond.aCond[2].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_UINT32 *)pstCond.aCond[2].aucValue = opVal;
    pstCond.aCond[3].ucFieldId = 5;
    pstCond.aCond[3].enOp = DB_OP_NOTEQUAL;
    memset_s((char *)pstCond.aCond[3].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(uint16_t *)pstCond.aCond[3].aucValue = UINT32_TO_UINT16(opVal);
    testPrintCond(&pstCond, pstFldLst);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = recordCnt;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum + 1);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt - 1, pstBufDataGet.ulRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstBufDataGet.ulRecNum; i++) {
        TblFieldDataDefT tblFieldDataDefGet = {0};
        testGetAllField(pBufGet + (pstBufDataGet.usRecLen * i), &tblFieldDataDefGet, pstBufDataGet.usRecLen);
        if (i < opVal) {
            ret = testCheckAllField(&tblFieldDataDefGet, i, i);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
        } else {
            ret = testCheckAllField(&tblFieldDataDefGet, i + 1, i + 1);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
        }

    }
    TEST_V1_FREE(pBufGet);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecEx接口测试，DB_OP_NOTEQUAL(不涉及索引), 多个过滤条件，索引字段部分匹配，不按顺序
TEST_F(Fun_SelectAllRecEx, V1Com_008_167)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    DB_FIELD_DEF_STRU *pstFldLst;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    VOS_UINT8 ucIdxFldNum[2] = { 1, 3 };
    VOS_UINT8 ucUniqueFlagLst[2] = { 1, 1 };
    VOS_UINT8 aucFieldIDLst[2][DB_IDX_FLD_MAX] = { {0}, {0, 1, 2} };
    CreateV1Label(&pstFldLst, ulNCols, ucIdxFldNum, aucFieldIDLst, ucUniqueFlagLst, &pusRelId);

    // 写数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_UINT32 recordCnt = rand() % 901 + 100;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u",recordCnt );
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_INT32 opVal = rand() % recordCnt;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 4;
    pstCond.aCond[0].ucFieldId = 2;
    pstCond.aCond[0].enOp = DB_OP_NOTEQUAL;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_INT32 *)pstCond.aCond[0].aucValue = opVal;
    pstCond.aCond[1].ucFieldId = 0;
    pstCond.aCond[1].enOp = DB_OP_NOTEQUAL;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(VOS_UINT32 *)pstCond.aCond[1].aucValue = opVal;
    pstCond.aCond[2].ucFieldId = 6;
    pstCond.aCond[2].enOp = DB_OP_NOTEQUAL;
    memset_s((char *)pstCond.aCond[2].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(int16_t *)pstCond.aCond[2].aucValue = UINT32_TO_INT16(opVal);
    pstCond.aCond[3].ucFieldId = 5;
    pstCond.aCond[3].enOp = DB_OP_NOTEQUAL;
    memset_s((char *)pstCond.aCond[3].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(uint16_t *)pstCond.aCond[3].aucValue = UINT32_TO_UINT16(opVal);
    testPrintCond(&pstCond, pstFldLst);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = recordCnt;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum + 1);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt - 1, pstBufDataGet.ulRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstBufDataGet.ulRecNum; i++) {
        TblFieldDataDefT tblFieldDataDefGet = {0};
        testGetAllField(pBufGet + (pstBufDataGet.usRecLen * i), &tblFieldDataDefGet, pstBufDataGet.usRecLen);
        if (i < opVal) {
            ret = testCheckAllField(&tblFieldDataDefGet, i, i);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
        } else {
            ret = testCheckAllField(&tblFieldDataDefGet, i + 1, i + 1);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
        }

    }
    TEST_V1_FREE(pBufGet);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TPC_SelectAllRecEx接口测试，DB_OP_NOTEQUAL(不涉及索引), 多个过滤条件，没有索引字段
TEST_F(Fun_SelectAllRecEx, V1Com_008_168)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DB_ERR_CODE ret;

    // 表定义
    DB_FIELD_DEF_STRU *pstFldLst;
    VOS_UINT32 ulNCols = 10;
    VOS_UINT16 pusRelId;
    VOS_UINT8 ucIdxFldNum[2] = { 1, 3 };
    VOS_UINT8 ucUniqueFlagLst[2] = { 1, 1 };
    VOS_UINT8 aucFieldIDLst[2][DB_IDX_FLD_MAX] = { {0}, {0, 1, 2} };
    CreateV1Label(&pstFldLst, ulNCols, ucIdxFldNum, aucFieldIDLst, ucUniqueFlagLst, &pusRelId);

    // 写数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_UINT32 recordCnt = rand() % 901 + 100;
    AW_FUN_Log(LOG_INFO, "recordCnt: %u",recordCnt );
    DB_DSBUF_STRU pstDsBufSet;
    pstDsBufSet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstDsBufSet.usRecNum = 1;
    pstDsBufSet.StdBuf.ulBufLen = pstDsBufSet.usRecLen;
    pstDsBufSet.StdBuf.ulActLen = pstDsBufSet.usRecLen;
    VOS_UINT8 *pucDataSet = (VOS_UINT8 *)TEST_V1_MALLOC(pstDsBufSet.StdBuf.ulBufLen);
    (void)memset_s(pucDataSet, pstDsBufSet.StdBuf.ulBufLen, 0x00, pstDsBufSet.StdBuf.ulBufLen);
    TEST_ASSERT_PTR_NULL(pucDataSet);
    TblFieldDataDefT TblFieldDataDefSet;
    for (VOS_UINT32 i = 0; i < recordCnt; i++) {
        testSetAllField(&TblFieldDataDefSet, i, i);
        memcpy_s(pucDataSet, pstDsBufSet.usRecLen, &TblFieldDataDefSet, pstDsBufSet.usRecLen);
        pstDsBufSet.StdBuf.pucData = pucDataSet;
        ret = TPC_InsertRec(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstDsBufSet);
        V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    }
    TEST_V1_FREE(pucDataSet);
    testScanAllData(g_queryTestDbId, pusRelId, pstFldLst, ulNCols, recordCnt);

    // 查询数据
    (void)srand((VOS_UINT32)time(NULL));
    VOS_INT32 opVal = rand() % recordCnt;
    DB_COND_STRU pstCond;
    pstCond.usCondNum = 4;
    pstCond.aCond[0].ucFieldId = 3;
    pstCond.aCond[0].enOp = DB_OP_NOTEQUAL;
    memset_s((char *)pstCond.aCond[0].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(float *)pstCond.aCond[0].aucValue = UINT32_TO_FLOAT(opVal);
    pstCond.aCond[1].ucFieldId = 4;
    pstCond.aCond[1].enOp = DB_OP_NOTEQUAL;
    memset_s((char *)pstCond.aCond[1].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(double *)pstCond.aCond[1].aucValue = UINT32_TO_DOUBLE(opVal);
    pstCond.aCond[2].ucFieldId = 5;
    pstCond.aCond[2].enOp = DB_OP_NOTEQUAL;
    memset_s((char *)pstCond.aCond[2].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(uint16_t *)pstCond.aCond[2].aucValue = UINT32_TO_UINT16(opVal);
    pstCond.aCond[3].ucFieldId = 6;
    pstCond.aCond[3].enOp = DB_OP_NOTEQUAL;
    memset_s((char *)pstCond.aCond[3].aucValue, DB_ELELEN_MAX, '\0', DB_ELELEN_MAX);
    *(int16_t *)pstCond.aCond[3].aucValue = UINT32_TO_INT16(opVal);
    testPrintCond(&pstCond, pstFldLst);

    DB_FIELDFILTER_STRU pstFldFilter;
    pstFldFilter.ucFieldNum = DB_FIELD_ALL;
    DB_BUF_STRU pstBufDataGet;
    pstBufDataGet.usRecLen = testGetRecLen(pstFldLst, ulNCols);
    pstBufDataGet.ulRecNum = recordCnt;
    pstBufDataGet.ulBufLen = pstBufDataGet.usRecLen * (pstBufDataGet.ulRecNum + 1);
    VOS_UINT8 *pBufGet = (VOS_UINT8 *)TEST_V1_MALLOC(pstBufDataGet.ulBufLen);
    TEST_ASSERT_PTR_NULL(pBufGet);
    (void)memset_s(pBufGet, pstBufDataGet.ulBufLen, 0x00, pstBufDataGet.ulBufLen);
    pstBufDataGet.pBuf = pBufGet;
    ret = TPC_SelectAllRecEx(TPC_GLOBAL_CDB, g_queryTestDbId, pusRelId, &pstCond, &pstFldFilter, &pstBufDataGet);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    V1_AW_MACRO_ASSERT_EQ_INT(recordCnt - 1, pstBufDataGet.ulRecNum);

    // 获取所有记录的数据
    for (VOS_UINT32 i = 0; i < pstBufDataGet.ulRecNum; i++) {
        TblFieldDataDefT tblFieldDataDefGet = {0};
        testGetAllField(pBufGet + (pstBufDataGet.usRecLen * i), &tblFieldDataDefGet, pstBufDataGet.usRecLen);
        if (i < opVal) {
            ret = testCheckAllField(&tblFieldDataDefGet, i, i);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
        } else {
            ret = testCheckAllField(&tblFieldDataDefGet, i + 1, i + 1);
            V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
        }

    }
    TEST_V1_FREE(pBufGet);

    // 释放字段定义和索引定义的内存
    testFreeFldInfo(pstFldLst);

    // 删表
    ret = TPC_DropTbl(g_queryTestDbId, pusRelId, TPC_NOWAIT);
    V1_AW_MACRO_ASSERT_EQ_INT(DB_SUCCESS_V1, V1ErrCode(ret));
    AW_FUN_Log(LOG_STEP, "test end.");
}
