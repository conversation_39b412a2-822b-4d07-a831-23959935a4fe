[{"type": "record", "name": "T80", "fields": [{"name": "F0", "type": "uint64", "nullable": false}, {"name": "F1", "type": "uint64", "nullable": false}, {"name": "F2", "type": "uint64", "nullable": false}, {"name": "F3", "type": "uint64", "nullable": false}, {"name": "F4", "type": "uint64", "nullable": false}], "keys": [{"node": "T80", "name": "T80_PK", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "T80", "name": "T80_PK_1", "fields": ["F1", "F2"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": true}}, {"node": "T80", "name": "T80_PK_2", "fields": ["F3", "F4"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": false}}]}]