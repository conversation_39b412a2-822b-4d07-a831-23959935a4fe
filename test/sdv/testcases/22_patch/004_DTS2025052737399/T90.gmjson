[{"type": "container", "name": "T90", "presence": false, "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint64", "nullable": true}, {"name": "F2", "type": "uint64", "nullable": true}, {"name": "F3", "type": "uint64", "nullable": true}, {"name": "F4", "type": "uint64", "nullable": true}], "keys": [{"node": "T90", "name": "T90_PK", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "T90", "name": "T90_PK_1", "fields": ["F1", "F2"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": true}}, {"node": "T90", "name": "T90_PK_2", "fields": ["F3", "F4"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": false}}]}, {"type": "list", "name": "list_1", "clause": [{"type": "when", "formula": "/T90/F1 = 100"}], "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID1", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "L0", "type": "uint32", "nullable": true}, {"name": "L1", "type": "uint32", "nullable": true}, {"name": "L2", "type": "uint32", "nullable": true}, {"name": "L3", "type": "uint32", "nullable": true}, {"name": "L4", "type": "uint32", "nullable": true}], "keys": [{"fields": ["PID", "PID1"], "node": "list_1", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}, {"fields": ["L0", "L1"], "node": "list_1", "name": "list_key1", "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": true}}, {"fields": ["L2", "L3"], "node": "list_1", "name": "list_key2", "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": false}}]}]