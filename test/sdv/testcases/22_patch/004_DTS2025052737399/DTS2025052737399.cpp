/*****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2022. All rights reserved.
 Description  : DTS2025052737399问题单补丁补充用例场景
 Notes        : 
 History      :
 Author       : 
 Modification : 2025/7/03
*****************************************************************************/
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <string>
#include <fstream>
#include <sstream>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"

GmcConnT *g_conn_sync = NULL;
GmcStmtT *g_stmt_sync = NULL;
GmcStmtT *g_stmt_async = NULL;
GmcConnT *g_conn_async = NULL;
GmcStmtT *g_stmt_root = NULL;
GmcStmtT *g_stmt_list = NULL;
GmcNodeT *g_rootNode = NULL;
GmcNodeT *g_childNode = {0};
GmcBatchOptionT batchOption;
GmcBatchT *batch;
GmcBatchRetT batchRet;
int32_t ret;

#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];
char const *view_name = "V\\$CATA_VERTEX_LABEL_INFO";
const char *g_vertexConfJson = R"({"max_record_count":1000, "isFastReadUncommitted": 0})";

using namespace std;
class DTS2025052737399 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {
        
    }
    static void TearDownTestCase()
    {
        
    }
};

void DTS2025052737399::SetUp()
{
        system("sh $TEST_HOME/tools/start.sh");
        ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        AW_CHECK_LOG_BEGIN();
        int ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcAllocStmt(g_conn_async, &g_stmt_root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 创建namespace
        // 设置namespace级别为：可重复读 + 乐观事务
        AsyncUserDataT userData = {0};
        const char *nameSpace1 = "Namespace001_2_A";
        const char *namespaceUserName = "abc";
        GmcNspCfgT nspCfg;
        nspCfg.tablespaceName = NULL;
        nspCfg.namespaceName = nameSpace1;
        nspCfg.userName = namespaceUserName;
        nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观

        ret = GmcDropNamespace(g_stmt_sync, nameSpace1);
        AddWhiteList(GMERR_UNDEFINED_OBJECT);
        ret = GmcCreateNamespaceWithCfg(g_stmt_sync, &nspCfg);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcUseNamespace(g_stmt_sync, nameSpace1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void DTS2025052737399::TearDown()
{
    AW_CHECK_LOG_END();
        AsyncUserDataT userData = {0};
        const char *nameSpace1 = "Namespace001_2_A";
        const char *namespaceUserName = "abc";

        GmcBatchDestroy(batch);

        ret = GmcClearNamespace(g_stmt_sync, nameSpace1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 删除namespace
        ret = GmcDropNamespace(g_stmt_sync, nameSpace1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcDisconnect(g_conn_async, g_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
}
static Status GetMemberValueOfSysViewCmd(char *cmd, char *memberName, int64_t *memberValue)
{
    char finalCmd[512] = {0};
    (void)sprintf_s(finalCmd, sizeof(finalCmd), "%s | grep \'%s:\' | awk -F\':\' \'{print $2}\'", cmd, memberName);
    FILE *pipe = popen(finalCmd, "r");
    char buffer[128];
    if (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
        *memberValue = atoi(buffer);
        pclose(pipe);
        return GMERR_OK;
    }
    pclose(pipe);
    return -1;
}
static const char *g_indexName = "T80_PK";
static Status InserOrUpdateDataForTestOptimistic3(
    GmcStmtT *stmt, uint32_t keyValue, char *tableName, GmcOperationTypeE type, uint32_t startValue)
{
    Status ret = GmcPrepareStmtByLabelName(stmt, tableName, type);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (type == GMC_OPERATION_INSERT) {
        uint64_t valueF0 = keyValue;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT64, &valueF0, sizeof(valueF0));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = GmcSetIndexKeyName(stmt, g_indexName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint64_t value = keyValue;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &value, sizeof(value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    uint64_t valueF1 = startValue + 1;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT64, &valueF1, sizeof(valueF1));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t valueF2 = startValue + 2;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT64, &valueF2, sizeof(valueF2));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t valueF3 = startValue + 3;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT64, &valueF3, sizeof(valueF3));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t valueF4 = startValue + 4;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_UINT64, &valueF4, sizeof(valueF4));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}
int TestTransStartAsync(GmcConnT *conn, GmcTxConfigT Config)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = GmcTransStartAsync(conn, &Config, trans_start_callback, &data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = testWaitAsyncRecv(&data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        return ret;
    }
}
AsyncUserDataT data1;
int TestTransCommitAsync(GmcConnT *conn)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = GmcTransCommitAsync(conn, trans_commit_callback, &data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = testWaitAsyncRecv(&data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (data.status == GMERR_TRANSACTION_ROLLBACK) {
            AW_FUN_Log(LOG_INFO, "data.status = GMERR_TRANSACTION_ROLLBACK, the transaction will rollback.");
            memset(&data1, 0, sizeof(AsyncUserDataT));
            int ret1 = GmcTransRollBackAsync(conn, trans_rollback_callback, &data1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        }
        return ret;
    }
}
static Status SetDataForTestOptimistic3(
    GmcStmtT *stmt, uint32_t keyValue, char *tableName, GmcOperationTypeE type, uint32_t startValue)
{
    Status ret = GmcPrepareStmtByLabelName(stmt, tableName, type);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t value = keyValue;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT64, &value, sizeof(value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t valueF1 = startValue + 1;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT64, &valueF1, sizeof(valueF1));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t valueF2 = startValue + 2;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT64, &valueF2, sizeof(valueF2));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t valueF3 = startValue + 3;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT64, &valueF3, sizeof(valueF3));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t valueF4 = startValue + 4;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_UINT64, &valueF4, sizeof(valueF4));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}
static Status MergeDataForTestOptimistic3(
    GmcStmtT *stmt, uint32_t keyValue, char *tableName, GmcOperationTypeE type, uint32_t startValue)
{
    Status ret = GmcPrepareStmtByLabelName(stmt, tableName, type);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, g_indexName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t value = keyValue;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &value, sizeof(value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t valueF1 = startValue + 1;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT64, &valueF1, sizeof(valueF1));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t valueF2 = startValue + 2;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT64, &valueF2, sizeof(valueF2));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t valueF3 = startValue + 3;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT64, &valueF3, sizeof(valueF3));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t valueF4 = startValue + 4;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_UINT64, &valueF4, sizeof(valueF4));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}
int TestYangSetField(GmcNodeT *node, GmcDataTypeE type, void *value, uint32_t size,
    const char *fieldName, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    int ret1 = 0;

    GmcPropValueT propValue;
    memcpy(propValue.propertyName, fieldName, (strlen(fieldName) + 1));
    propValue.type = type;
    propValue.value = value;
    propValue.size = size;
    ret = GmcYangSetNodeProperty(node, &propValue, opType);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_DEBUG, "SetProperty failed fieldName(%s)\n", fieldName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret1 = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);
        return ret;
    }

    return ret;
}
int TestBatchPrepare(GmcConnT *conn, GmcBatchT **batch, GmcBatchTypeE batchType = GMC_BATCH_YANG,
    GmcYangDiffTypeE diffType = GMC_YANG_DIFF_DELAY_READ_ON)
{
    int ret = 0;
    GmcBatchOptionT batchOption;

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBatchType(&batchOption, batchType);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcYangBatchOptionSetDiffType(&batchOption, diffType);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }

    ret = GmcBatchPrepare(conn, &batchOption, batch);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }

    return ret;
}
static Status InserOrUpdateDataForYang(
     uint32_t keyValue, char *tableName, GmcOperationTypeE type1, GmcYangPropOpTypeE type, uint32_t startValue)
{
    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "T90", type1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t valueF1 = startValue + 1;
    ret = TestYangSetField(g_rootNode, GMC_DATATYPE_UINT64, &valueF1, sizeof(valueF1), "F1", type);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t valueF2 = startValue + 2;
    ret = TestYangSetField(g_rootNode, GMC_DATATYPE_UINT64, &valueF2, sizeof(valueF2), "F2", type);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t valueF3 = startValue + 3;
    ret = TestYangSetField(g_rootNode, GMC_DATATYPE_UINT64, &valueF3, sizeof(valueF3), "F3", type);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t valueF4 = startValue + 4;
    ret = TestYangSetField(g_rootNode, GMC_DATATYPE_UINT64, &valueF4, sizeof(valueF4), "F4", type);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    return ret;
}
static Status InserOrUpdateDataForNode(
     uint32_t keyValue, char *tableName, GmcOperationTypeE type1, GmcYangPropOpTypeE type, uint32_t startValue)
{
    // 设置批处理batch参数

    ret = testGmcPrepareStmtByLabelName(g_stmt_list, "list_1", type1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_childNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF0 = startValue;
    ret = TestYangSetField(g_childNode, GMC_DATATYPE_UINT32, &valueF0, sizeof(valueF0), "L0", type);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = startValue + 1;
    ret = TestYangSetField(g_childNode, GMC_DATATYPE_UINT32, &valueF1, sizeof(valueF1), "L1", type);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF2 = startValue + 2;
    ret = TestYangSetField(g_childNode, GMC_DATATYPE_UINT32, &valueF2, sizeof(valueF2), "L2", type);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF3 = startValue + 3;
    ret = TestYangSetField(g_childNode, GMC_DATATYPE_UINT32, &valueF3, sizeof(valueF3), "L3", type);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = startValue + 4;
    ret = TestYangSetField(g_childNode, GMC_DATATYPE_UINT32, &valueF4, sizeof(valueF4), "L4", type);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    return ret;
}
static Status DeleteDataForNode(
     uint32_t keyValue, char *tableName, GmcOperationTypeE type1, uint32_t startValue)
{
    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_list, "list_1", type1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_childNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt_list, 1, GMC_DATATYPE_UINT32, &keyValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    return ret;
}

static const char g_labelConfig[] =
    R"({"max_record_count":500001, "defragmentation":false, "isFastReadUncommitted":false, "auto_increment":1, "yang_model":1})";

// 001.乐观事务，RR隔离级别下同一个事务对表进行重复update
TEST_F(DTS2025052737399, Yang_122_DTS001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char tableName[] = "T80";
    const char *nameSpace1 = "Namespace001_2_A";
    char *v_schema = NULL;
    char tablePath[] = "./T80.gmjson";

    // 拉起gmserver
    int32_t ret;

    readJanssonFile(tablePath, &v_schema);
    ASSERT_NE((void *)NULL, v_schema);
    ret = GmcCreateVertexLabel(g_stmt_sync, v_schema, g_labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK,ret);

    ret = InserOrUpdateDataForTestOptimistic3(g_stmt_sync, 0, tableName, GMC_OPERATION_INSERT, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(g_stmt_sync, nameSpace1);
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_REPEATABLE;
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX;

    ret = GmcTransStart(g_conn_sync, &config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int64_t key1Cnt = 0;
    int64_t key2Cnt = 0;
    int64_t undoCnt = -1;
    char entryUsed[32] = "ENTRY_USED";
    char nodeCnt[32] = "NODE_COUNT";
    char undoNum[32] = "UNDORECORD_NUM";
    char cmd1[256] = "gmsysview -q V\\$STORAGE_HASH_INDEX_STAT -f UNIQUEINDEX_NAME=T80_PK_1";
    char cmd2[256] = "gmsysview -q V\\$STORAGE_HASH_LINKLIST_INDEX_STAT -f NONUNIQUEINDEX_NAME=T80_PK_2";
    char cmd3[256] = "gmsysview -q V\\$STORAGE_UNDO_STAT";

    ret = GetMemberValueOfSysViewCmd(cmd1, entryUsed, &key1Cnt);
    AW_MACRO_ASSERT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd2, nodeCnt, &key2Cnt);
    AW_MACRO_ASSERT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd3, undoNum, &undoCnt);
    AW_MACRO_ASSERT_EQ_INT(ret, 0);

    AW_MACRO_EXPECT_EQ_INT(undoCnt, 0);
    AW_MACRO_EXPECT_EQ_INT(key1Cnt, 1);
    AW_MACRO_EXPECT_EQ_INT(key2Cnt, 1);

    // 先更新一条
    ret = InserOrUpdateDataForTestOptimistic3(g_stmt_sync, 0, tableName, GMC_OPERATION_UPDATE, 4);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GetMemberValueOfSysViewCmd(cmd1, entryUsed, &key1Cnt);
    AW_MACRO_ASSERT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd2, nodeCnt, &key2Cnt);
    AW_MACRO_ASSERT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd3, undoNum, &undoCnt);
    AW_MACRO_ASSERT_EQ_INT(ret, 0);

    AW_MACRO_EXPECT_EQ_INT(undoCnt, 1);
    AW_MACRO_EXPECT_EQ_INT(key1Cnt, 2);
    AW_MACRO_EXPECT_EQ_INT(key2Cnt, 2);

    // 重复更新，触发undo合并
    for (int32_t i = 2; i < 100; ++i) {
        ret = InserOrUpdateDataForTestOptimistic3(g_stmt_sync, 0, tableName, GMC_OPERATION_UPDATE, i * 4);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    ret = GetMemberValueOfSysViewCmd(cmd1, entryUsed, &key1Cnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd2, nodeCnt, &key2Cnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd3, undoNum, &undoCnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);

    AW_MACRO_EXPECT_EQ_INT(undoCnt, 1);
    AW_MACRO_EXPECT_EQ_INT(key1Cnt, 2);
    AW_MACRO_EXPECT_EQ_INT(key2Cnt, 2);

    ret = GmcTransCommit(g_conn_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GetMemberValueOfSysViewCmd(cmd3, undoNum, &undoCnt);
    AW_MACRO_ASSERT_EQ_INT(ret, 0);
    AW_MACRO_EXPECT_EQ_INT(undoCnt, 0);

    int cnt = 100;
    while (cnt--) {
        ret = GetMemberValueOfSysViewCmd(cmd1, entryUsed, &key1Cnt);
        AW_MACRO_ASSERT_EQ_INT(ret, 0);
        ret = GetMemberValueOfSysViewCmd(cmd2, nodeCnt, &key2Cnt);
        AW_MACRO_ASSERT_EQ_INT(ret, 0);
        if (key1Cnt != 1 || key2Cnt != 1) {
            usleep(100 * 1000);
            continue;
        } else {
            break;
        }
    }

    AW_MACRO_EXPECT_EQ_INT(key1Cnt, 1);
    AW_MACRO_EXPECT_EQ_INT(key2Cnt, 1);

    ret = GmcDropVertexLabel(g_stmt_sync, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 002.乐观事务，RR隔离级别下同一个事务对表进行重复replace
TEST_F(DTS2025052737399, Yang_122_DTS002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char tableName[] = "T80";
    const char *nameSpace1 = "Namespace001_2_A";
    char *v_schema = NULL;
    char tablePath[] = "./T80.gmjson";

    // 拉起gmserver
    int32_t ret;

    readJanssonFile(tablePath, &v_schema);
    ASSERT_NE((void *)NULL, v_schema);
    ret = GmcCreateVertexLabel(g_stmt_sync, v_schema, g_labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK,ret);

    ret = InserOrUpdateDataForTestOptimistic3(g_stmt_sync, 0, tableName, GMC_OPERATION_INSERT, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(g_stmt_sync, nameSpace1);
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_REPEATABLE;
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX;

    ret = GmcTransStart(g_conn_sync, &config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int64_t key1Cnt = 0;
    int64_t key2Cnt = 0;
    int64_t undoCnt = -1;
    char entryUsed[32] = "ENTRY_USED";
    char nodeCnt[32] = "NODE_COUNT";
    char undoNum[32] = "UNDORECORD_NUM";
    char cmd1[256] = "gmsysview -q V\\$STORAGE_HASH_INDEX_STAT -f UNIQUEINDEX_NAME=T80_PK_1";
    char cmd2[256] = "gmsysview -q V\\$STORAGE_HASH_LINKLIST_INDEX_STAT -f NONUNIQUEINDEX_NAME=T80_PK_2";
    char cmd3[256] = "gmsysview -q V\\$STORAGE_UNDO_STAT";

    ret = GetMemberValueOfSysViewCmd(cmd1, entryUsed, &key1Cnt);
    AW_MACRO_ASSERT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd2, nodeCnt, &key2Cnt);
    AW_MACRO_ASSERT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd3, undoNum, &undoCnt);
    AW_MACRO_ASSERT_EQ_INT(ret, 0);

    AW_MACRO_EXPECT_EQ_INT(undoCnt, 0);
    AW_MACRO_EXPECT_EQ_INT(key1Cnt, 1);
    AW_MACRO_EXPECT_EQ_INT(key2Cnt, 1);

    // 先更新一条
    ret = SetDataForTestOptimistic3(g_stmt_sync, 0, tableName, GMC_OPERATION_REPLACE, 4);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GetMemberValueOfSysViewCmd(cmd1, entryUsed, &key1Cnt);
    AW_MACRO_ASSERT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd2, nodeCnt, &key2Cnt);
    AW_MACRO_ASSERT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd3, undoNum, &undoCnt);
    AW_MACRO_ASSERT_EQ_INT(ret, 0);

    AW_MACRO_EXPECT_EQ_INT(undoCnt, 1);
    AW_MACRO_EXPECT_EQ_INT(key1Cnt, 2);
    AW_MACRO_EXPECT_EQ_INT(key2Cnt, 2);

    // 重复更新，触发undo合并
    for (int32_t i = 2; i < 100; ++i) {
        ret = SetDataForTestOptimistic3(g_stmt_sync, 0, tableName, GMC_OPERATION_REPLACE, i * 4);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    ret = GetMemberValueOfSysViewCmd(cmd1, entryUsed, &key1Cnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd2, nodeCnt, &key2Cnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd3, undoNum, &undoCnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);

    AW_MACRO_EXPECT_EQ_INT(undoCnt, 1);
    AW_MACRO_EXPECT_EQ_INT(key1Cnt, 2);
    AW_MACRO_EXPECT_EQ_INT(key2Cnt, 2);

    ret = GmcTransCommit(g_conn_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GetMemberValueOfSysViewCmd(cmd3, undoNum, &undoCnt);
    AW_MACRO_ASSERT_EQ_INT(ret, 0);
    AW_MACRO_EXPECT_EQ_INT(undoCnt, 0);

    int cnt = 100;
    while (cnt--) {
        ret = GetMemberValueOfSysViewCmd(cmd1, entryUsed, &key1Cnt);
        AW_MACRO_ASSERT_EQ_INT(ret, 0);
        ret = GetMemberValueOfSysViewCmd(cmd2, nodeCnt, &key2Cnt);
        AW_MACRO_ASSERT_EQ_INT(ret, 0);
        if (key1Cnt != 1 || key2Cnt != 1) {
            usleep(100 * 1000);
            continue;
        } else {
            break;
        }
    }

    AW_MACRO_EXPECT_EQ_INT(key1Cnt, 1);
    AW_MACRO_EXPECT_EQ_INT(key2Cnt, 1);

    ret = GmcDropVertexLabel(g_stmt_sync, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 003.乐观事务，RR隔离级别下同一个事务对表进行重复merge
TEST_F(DTS2025052737399, Yang_122_DTS003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char tableName[] = "T80";
    const char *nameSpace1 = "Namespace001_2_A";
    char *v_schema = NULL;
    char tablePath[] = "./T80.gmjson";

    // 拉起gmserver
    int32_t ret;

    readJanssonFile(tablePath, &v_schema);
    ASSERT_NE((void *)NULL, v_schema);
    ret = GmcCreateVertexLabel(g_stmt_sync, v_schema, g_labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK,ret);

    ret = InserOrUpdateDataForTestOptimistic3(g_stmt_sync, 0, tableName, GMC_OPERATION_INSERT, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(g_stmt_sync, nameSpace1);
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_REPEATABLE;
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX;

    ret = GmcTransStart(g_conn_sync, &config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int64_t key1Cnt = 0;
    int64_t key2Cnt = 0;
    int64_t undoCnt = -1;
    char entryUsed[32] = "ENTRY_USED";
    char nodeCnt[32] = "NODE_COUNT";
    char undoNum[32] = "UNDORECORD_NUM";
    char cmd1[256] = "gmsysview -q V\\$STORAGE_HASH_INDEX_STAT -f UNIQUEINDEX_NAME=T80_PK_1";
    char cmd2[256] = "gmsysview -q V\\$STORAGE_HASH_LINKLIST_INDEX_STAT -f NONUNIQUEINDEX_NAME=T80_PK_2";
    char cmd3[256] = "gmsysview -q V\\$STORAGE_UNDO_STAT";

    ret = GetMemberValueOfSysViewCmd(cmd1, entryUsed, &key1Cnt);
    AW_MACRO_ASSERT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd2, nodeCnt, &key2Cnt);
    AW_MACRO_ASSERT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd3, undoNum, &undoCnt);
    AW_MACRO_ASSERT_EQ_INT(ret, 0);

    AW_MACRO_EXPECT_EQ_INT(undoCnt, 0);
    AW_MACRO_EXPECT_EQ_INT(key1Cnt, 1);
    AW_MACRO_EXPECT_EQ_INT(key2Cnt, 1);

    // 先更新一条
    ret = MergeDataForTestOptimistic3(g_stmt_sync, 0, tableName, GMC_OPERATION_MERGE, 4);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GetMemberValueOfSysViewCmd(cmd1, entryUsed, &key1Cnt);
    AW_MACRO_ASSERT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd2, nodeCnt, &key2Cnt);
    AW_MACRO_ASSERT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd3, undoNum, &undoCnt);
    AW_MACRO_ASSERT_EQ_INT(ret, 0);

    AW_MACRO_EXPECT_EQ_INT(undoCnt, 1);
    AW_MACRO_EXPECT_EQ_INT(key1Cnt, 2);
    AW_MACRO_EXPECT_EQ_INT(key2Cnt, 2);

    // 重复更新，触发undo合并
    for (int32_t i = 2; i < 100; ++i) {
        ret = MergeDataForTestOptimistic3(g_stmt_sync, 0, tableName, GMC_OPERATION_MERGE, i * 4);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    ret = GetMemberValueOfSysViewCmd(cmd1, entryUsed, &key1Cnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd2, nodeCnt, &key2Cnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd3, undoNum, &undoCnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);

    AW_MACRO_EXPECT_EQ_INT(undoCnt, 1);
    AW_MACRO_EXPECT_EQ_INT(key1Cnt, 2);
    AW_MACRO_EXPECT_EQ_INT(key2Cnt, 2);

    ret = GmcTransCommit(g_conn_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GetMemberValueOfSysViewCmd(cmd3, undoNum, &undoCnt);
    AW_MACRO_ASSERT_EQ_INT(ret, 0);
    AW_MACRO_EXPECT_EQ_INT(undoCnt, 0);

    int cnt = 100;
    while (cnt--) {
        ret = GetMemberValueOfSysViewCmd(cmd1, entryUsed, &key1Cnt);
        AW_MACRO_ASSERT_EQ_INT(ret, 0);
        ret = GetMemberValueOfSysViewCmd(cmd2, nodeCnt, &key2Cnt);
        AW_MACRO_ASSERT_EQ_INT(ret, 0);
        if (key1Cnt != 1 || key2Cnt != 1) {
            usleep(100 * 1000);
            continue;
        } else {
            break;
        }
    }

    AW_MACRO_EXPECT_EQ_INT(key1Cnt, 1);
    AW_MACRO_EXPECT_EQ_INT(key2Cnt, 1);

    ret = GmcDropVertexLabel(g_stmt_sync, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 004.乐观事务，RR隔离级别下不同事务对表进行重复update
TEST_F(DTS2025052737399, Yang_122_DTS004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char tableName[] = "T80";
    const char *nameSpace1 = "Namespace001_2_A";
    char *v_schema = NULL;
    char tablePath[] = "./T80.gmjson";

    // 拉起gmserver
    int32_t ret;

    readJanssonFile(tablePath, &v_schema);
    ASSERT_NE((void *)NULL, v_schema);
    ret = GmcCreateVertexLabel(g_stmt_sync, v_schema, g_labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK,ret);

    ret = InserOrUpdateDataForTestOptimistic3(g_stmt_sync, 0, tableName, GMC_OPERATION_INSERT, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(g_stmt_sync, nameSpace1);
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_REPEATABLE;
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX;

    int64_t key1Cnt = 0;
    int64_t key2Cnt = 0;
    int64_t undoCnt = -1;
    char entryUsed[32] = "ENTRY_USED";
    char nodeCnt[32] = "NODE_COUNT";
    char undoNum[32] = "UNDORECORD_NUM";
    char cmd1[256] = "gmsysview -q V\\$STORAGE_HASH_INDEX_STAT -f UNIQUEINDEX_NAME=T80_PK_1";
    char cmd2[256] = "gmsysview -q V\\$STORAGE_HASH_LINKLIST_INDEX_STAT -f NONUNIQUEINDEX_NAME=T80_PK_2";
    char cmd3[256] = "gmsysview -q V\\$STORAGE_UNDO_STAT";

    ret = GetMemberValueOfSysViewCmd(cmd1, entryUsed, &key1Cnt);
    AW_MACRO_ASSERT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd2, nodeCnt, &key2Cnt);
    AW_MACRO_ASSERT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd3, undoNum, &undoCnt);
    AW_MACRO_ASSERT_EQ_INT(ret, 0);

    AW_MACRO_EXPECT_EQ_INT(undoCnt, 0);
    AW_MACRO_EXPECT_EQ_INT(key1Cnt, 1);
    AW_MACRO_EXPECT_EQ_INT(key2Cnt, 1);

    // 先更新一条
    ret = InserOrUpdateDataForTestOptimistic3(g_stmt_sync, 0, tableName, GMC_OPERATION_UPDATE, 4);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    sleep(5);

    ret = GetMemberValueOfSysViewCmd(cmd1, entryUsed, &key1Cnt);
    AW_MACRO_ASSERT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd2, nodeCnt, &key2Cnt);
    AW_MACRO_ASSERT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd3, undoNum, &undoCnt);
    AW_MACRO_ASSERT_EQ_INT(ret, 0);

    AW_MACRO_EXPECT_EQ_INT(undoCnt, 0);
    AW_MACRO_EXPECT_EQ_INT(key1Cnt, 1);
    AW_MACRO_EXPECT_EQ_INT(key2Cnt, 1);

    // 重复更新，触发undo合并
    for (int32_t i = 2; i < 100; ++i) {
        ret = InserOrUpdateDataForTestOptimistic3(g_stmt_sync, 0, tableName, GMC_OPERATION_UPDATE, i * 4);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    ret = GetMemberValueOfSysViewCmd(cmd1, entryUsed, &key1Cnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd2, nodeCnt, &key2Cnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd3, undoNum, &undoCnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);

    ret = GetMemberValueOfSysViewCmd(cmd3, undoNum, &undoCnt);
    AW_MACRO_ASSERT_EQ_INT(ret, 0);
    AW_MACRO_EXPECT_EQ_INT(undoCnt, 0);

    int cnt = 100;
    while (cnt--) {
        ret = GetMemberValueOfSysViewCmd(cmd1, entryUsed, &key1Cnt);
        AW_MACRO_ASSERT_EQ_INT(ret, 0);
        ret = GetMemberValueOfSysViewCmd(cmd2, nodeCnt, &key2Cnt);
        AW_MACRO_ASSERT_EQ_INT(ret, 0);
        if (key1Cnt != 1 || key2Cnt != 1) {
            usleep(100 * 1000);
            continue;
        } else {
            break;
        }
    }

    AW_MACRO_EXPECT_EQ_INT(key1Cnt, 1);
    AW_MACRO_EXPECT_EQ_INT(key2Cnt, 1);

    ret = GmcDropVertexLabel(g_stmt_sync, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 005.乐观事务，RR隔离级别下不同事务对表进行重复replace
TEST_F(DTS2025052737399, Yang_122_DTS005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char tableName[] = "T80";
    const char *nameSpace1 = "Namespace001_2_A";
    char *v_schema = NULL;
    char tablePath[] = "./T80.gmjson";

    // 拉起gmserver
    int32_t ret;

    readJanssonFile(tablePath, &v_schema);
    ASSERT_NE((void *)NULL, v_schema);
    ret = GmcCreateVertexLabel(g_stmt_sync, v_schema, g_labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK,ret);

    ret = InserOrUpdateDataForTestOptimistic3(g_stmt_sync, 0, tableName, GMC_OPERATION_INSERT, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(g_stmt_sync, nameSpace1);
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_REPEATABLE;
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX;

    int64_t key1Cnt = 0;
    int64_t key2Cnt = 0;
    int64_t undoCnt = -1;
    char entryUsed[32] = "ENTRY_USED";
    char nodeCnt[32] = "NODE_COUNT";
    char undoNum[32] = "UNDORECORD_NUM";
    char cmd1[256] = "gmsysview -q V\\$STORAGE_HASH_INDEX_STAT -f UNIQUEINDEX_NAME=T80_PK_1";
    char cmd2[256] = "gmsysview -q V\\$STORAGE_HASH_LINKLIST_INDEX_STAT -f NONUNIQUEINDEX_NAME=T80_PK_2";
    char cmd3[256] = "gmsysview -q V\\$STORAGE_UNDO_STAT";

    ret = GetMemberValueOfSysViewCmd(cmd1, entryUsed, &key1Cnt);
    AW_MACRO_ASSERT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd2, nodeCnt, &key2Cnt);
    AW_MACRO_ASSERT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd3, undoNum, &undoCnt);
    AW_MACRO_ASSERT_EQ_INT(ret, 0);

    AW_MACRO_EXPECT_EQ_INT(undoCnt, 0);
    AW_MACRO_EXPECT_EQ_INT(key1Cnt, 1);
    AW_MACRO_EXPECT_EQ_INT(key2Cnt, 1);

    // 先更新一条
    ret = SetDataForTestOptimistic3(g_stmt_sync, 0, tableName, GMC_OPERATION_REPLACE, 4);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    sleep(5);

    ret = GetMemberValueOfSysViewCmd(cmd1, entryUsed, &key1Cnt);
    AW_MACRO_ASSERT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd2, nodeCnt, &key2Cnt);
    AW_MACRO_ASSERT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd3, undoNum, &undoCnt);
    AW_MACRO_ASSERT_EQ_INT(ret, 0);

    AW_MACRO_EXPECT_EQ_INT(undoCnt, 0);
    AW_MACRO_EXPECT_EQ_INT(key1Cnt, 1);
    AW_MACRO_EXPECT_EQ_INT(key2Cnt, 1);

    // 重复更新，触发undo合并
    for (int32_t i = 2; i < 100; ++i) {
        ret = SetDataForTestOptimistic3(g_stmt_sync, 0, tableName, GMC_OPERATION_REPLACE, i * 4);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    ret = GetMemberValueOfSysViewCmd(cmd3, undoNum, &undoCnt);
    AW_MACRO_ASSERT_EQ_INT(ret, 0);
    AW_MACRO_EXPECT_EQ_INT(undoCnt, 0);

    int cnt = 100;
    while (cnt--) {
        ret = GetMemberValueOfSysViewCmd(cmd1, entryUsed, &key1Cnt);
        AW_MACRO_ASSERT_EQ_INT(ret, 0);
        ret = GetMemberValueOfSysViewCmd(cmd2, nodeCnt, &key2Cnt);
        AW_MACRO_ASSERT_EQ_INT(ret, 0);
        if (key1Cnt != 1 || key2Cnt != 1) {
            usleep(100 * 1000);
            continue;
        } else {
            break;
        }
    }

    AW_MACRO_EXPECT_EQ_INT(key1Cnt, 1);
    AW_MACRO_EXPECT_EQ_INT(key2Cnt, 1);

    ret = GmcDropVertexLabel(g_stmt_sync, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 006.乐观事务，RR隔离级别下不同事务对表进行重复merge
TEST_F(DTS2025052737399, Yang_122_DTS006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char tableName[] = "T80";
    const char *nameSpace1 = "Namespace001_2_A";
    char *v_schema = NULL;
    char tablePath[] = "./T80.gmjson";

    // 拉起gmserver
    int32_t ret;

    readJanssonFile(tablePath, &v_schema);
    ASSERT_NE((void *)NULL, v_schema);
    ret = GmcCreateVertexLabel(g_stmt_sync, v_schema, g_labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK,ret);

    ret = InserOrUpdateDataForTestOptimistic3(g_stmt_sync, 0, tableName, GMC_OPERATION_INSERT, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(g_stmt_sync, nameSpace1);
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_REPEATABLE;
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX;

    int64_t key1Cnt = 0;
    int64_t key2Cnt = 0;
    int64_t undoCnt = -1;
    char entryUsed[32] = "ENTRY_USED";
    char nodeCnt[32] = "NODE_COUNT";
    char undoNum[32] = "UNDORECORD_NUM";
    char cmd1[256] = "gmsysview -q V\\$STORAGE_HASH_INDEX_STAT -f UNIQUEINDEX_NAME=T80_PK_1";
    char cmd2[256] = "gmsysview -q V\\$STORAGE_HASH_LINKLIST_INDEX_STAT -f NONUNIQUEINDEX_NAME=T80_PK_2";
    char cmd3[256] = "gmsysview -q V\\$STORAGE_UNDO_STAT";

    ret = GetMemberValueOfSysViewCmd(cmd1, entryUsed, &key1Cnt);
    AW_MACRO_ASSERT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd2, nodeCnt, &key2Cnt);
    AW_MACRO_ASSERT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd3, undoNum, &undoCnt);
    AW_MACRO_ASSERT_EQ_INT(ret, 0);

    AW_MACRO_EXPECT_EQ_INT(undoCnt, 0);
    AW_MACRO_EXPECT_EQ_INT(key1Cnt, 1);
    AW_MACRO_EXPECT_EQ_INT(key2Cnt, 1);

    // 先更新一条
    ret = MergeDataForTestOptimistic3(g_stmt_sync, 0, tableName, GMC_OPERATION_MERGE, 4);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    sleep(5);

    ret = GetMemberValueOfSysViewCmd(cmd1, entryUsed, &key1Cnt);
    AW_MACRO_ASSERT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd2, nodeCnt, &key2Cnt);
    AW_MACRO_ASSERT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd3, undoNum, &undoCnt);
    AW_MACRO_ASSERT_EQ_INT(ret, 0);

    AW_MACRO_EXPECT_EQ_INT(undoCnt, 0);
    AW_MACRO_EXPECT_EQ_INT(key1Cnt, 1);
    AW_MACRO_EXPECT_EQ_INT(key2Cnt, 1);

    // 重复更新，触发undo合并
    for (int32_t i = 2; i < 100; ++i) {
        ret = MergeDataForTestOptimistic3(g_stmt_sync, 0, tableName, GMC_OPERATION_MERGE, i * 4);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    ret = GetMemberValueOfSysViewCmd(cmd3, undoNum, &undoCnt);
    AW_MACRO_ASSERT_EQ_INT(ret, 0);
    AW_MACRO_EXPECT_EQ_INT(undoCnt, 0);

    int cnt = 100;
    while (cnt--) {
        ret = GetMemberValueOfSysViewCmd(cmd1, entryUsed, &key1Cnt);
        AW_MACRO_ASSERT_EQ_INT(ret, 0);
        ret = GetMemberValueOfSysViewCmd(cmd2, nodeCnt, &key2Cnt);
        AW_MACRO_ASSERT_EQ_INT(ret, 0);
        if (key1Cnt != 1 || key2Cnt != 1) {
            usleep(100 * 1000);
            continue;
        } else {
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(key1Cnt, 1);
    AW_MACRO_EXPECT_EQ_INT(key2Cnt, 1);

    ret = GmcDropVertexLabel(g_stmt_sync, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007.乐观事务，RR隔离级别下使用GMC_OPERATION_MERGE对表重复操作
TEST_F(DTS2025052737399, Yang_122_DTS007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char tableName[] = "T90";
    const char *nameSpace1 = "Namespace001_2_A";
    char *v_schema = NULL;
    char tablePath[] = "./T90.gmjson";
    AsyncUserDataT data = {0};

    // 拉起gmserver
    int32_t ret;

    ret = GmcUseNamespaceAsync(g_stmt_async, nameSpace1,  use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    readJanssonFile(tablePath, &v_schema);
    ASSERT_NE((void *)NULL, v_schema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, v_schema, g_labelConfig,  create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK,ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_REPEATABLE;
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX;
    ret = TestTransStartAsync(g_conn_async, config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = InserOrUpdateDataForYang(0, tableName, GMC_OPERATION_INSERT, GMC_YANG_PROPERTY_OPERATION_CREATE, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    memset(&data, 0, sizeof(AsyncUserDataT)); 

    int64_t key1Cnt = 0;
    int64_t key2Cnt = 0;
    int64_t undoCnt = -1;
    char entryUsed[32] = "ENTRY_USED";
    char nodeCnt[32] = "NODE_COUNT";
    char undoNum[32] = "UNDORECORD_NUM";
    char cmd1[256] = "gmsysview -q V\\$STORAGE_HASH_INDEX_STAT -f UNIQUEINDEX_NAME=T90_PK_1";
    char cmd2[256] = "gmsysview -q V\\$STORAGE_HASH_LINKLIST_INDEX_STAT -f NONUNIQUEINDEX_NAME=T90_PK_2";
    char cmd3[256] = "gmsysview -q V\\$STORAGE_UNDO_STAT";

    ret = GetMemberValueOfSysViewCmd(cmd1, entryUsed, &key1Cnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd2, nodeCnt, &key2Cnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd3, undoNum, &undoCnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);

    AW_MACRO_EXPECT_EQ_INT(undoCnt, 1);
    AW_MACRO_EXPECT_EQ_INT(key1Cnt, 1);
    AW_MACRO_EXPECT_EQ_INT(key2Cnt, 1);

    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 先更新一条
    ret = TestTransStartAsync(g_conn_async, config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InserOrUpdateDataForYang(0, tableName, GMC_OPERATION_MERGE, GMC_YANG_PROPERTY_OPERATION_MERGE, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    memset(&data, 0, sizeof(AsyncUserDataT)); 

    ret = GetMemberValueOfSysViewCmd(cmd1, entryUsed, &key1Cnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd2, nodeCnt, &key2Cnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd3, undoNum, &undoCnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);
    AW_MACRO_EXPECT_EQ_INT(undoCnt, 1);
    AW_MACRO_EXPECT_EQ_INT(key1Cnt, 2);
    AW_MACRO_EXPECT_EQ_INT(key2Cnt, 2);

    // 重复更新，触发undo合并
    for (int32_t i = 2; i < 100; ++i) {
        ret = InserOrUpdateDataForYang(0, tableName, GMC_OPERATION_MERGE, GMC_YANG_PROPERTY_OPERATION_MERGE, i * 4);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    memset(&data, 0, sizeof(AsyncUserDataT)); 

    ret = GetMemberValueOfSysViewCmd(cmd1, entryUsed, &key1Cnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd2, nodeCnt, &key2Cnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd3, undoNum, &undoCnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);

    AW_MACRO_EXPECT_EQ_INT(undoCnt, 1);
    AW_MACRO_EXPECT_EQ_INT(key1Cnt, 2);
    AW_MACRO_EXPECT_EQ_INT(key2Cnt, 2);

    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GetMemberValueOfSysViewCmd(cmd3, undoNum, &undoCnt);
    AW_MACRO_ASSERT_EQ_INT(ret, 0);
    AW_MACRO_EXPECT_EQ_INT(undoCnt, 0);

    int cnt = 100;
    while (cnt--) {
        ret = GetMemberValueOfSysViewCmd(cmd1, entryUsed, &key1Cnt);
        AW_MACRO_ASSERT_EQ_INT(ret, 0);
        ret = GetMemberValueOfSysViewCmd(cmd2, nodeCnt, &key2Cnt);
        AW_MACRO_ASSERT_EQ_INT(ret, 0);
        if (key1Cnt != 1 || key2Cnt != 1) {
            usleep(100 * 1000);
            continue;
        } else {
            break;
        }
    }

    AW_MACRO_EXPECT_EQ_INT(key1Cnt, 1);
    AW_MACRO_EXPECT_EQ_INT(key2Cnt, 1);

    ret = GmcDropVertexLabel(g_stmt_sync, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 008.乐观事务，RR隔离级别下使用GMC_OPERATION_REPLACE_GRAPH对表重复操作
TEST_F(DTS2025052737399, Yang_122_DTS008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char tableName[] = "T90";
    const char *nameSpace1 = "Namespace001_2_A";
    char *v_schema = NULL;
    char tablePath[] = "./T90.gmjson";
    AsyncUserDataT data = {0};

    // 拉起gmserver
    int32_t ret;

    ret = GmcUseNamespaceAsync(g_stmt_async, nameSpace1,  use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    readJanssonFile(tablePath, &v_schema);
    ASSERT_NE((void *)NULL, v_schema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, v_schema, g_labelConfig,  create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK,ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_REPEATABLE;
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX;
    ret = TestTransStartAsync(g_conn_async, config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = InserOrUpdateDataForYang(0, tableName, GMC_OPERATION_INSERT, GMC_YANG_PROPERTY_OPERATION_CREATE, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    memset(&data, 0, sizeof(AsyncUserDataT)); 

    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int64_t key1Cnt = 0;
    int64_t key2Cnt = 0;
    int64_t undoCnt = -1;
    char entryUsed[32] = "ENTRY_USED";
    char nodeCnt[32] = "NODE_COUNT";
    char undoNum[32] = "UNDORECORD_NUM";
    char cmd1[256] = "gmsysview -q V\\$STORAGE_HASH_INDEX_STAT -f UNIQUEINDEX_NAME=T90_PK_1";
    char cmd2[256] = "gmsysview -q V\\$STORAGE_HASH_LINKLIST_INDEX_STAT -f NONUNIQUEINDEX_NAME=T90_PK_2";
    char cmd3[256] = "gmsysview -q V\\$STORAGE_UNDO_STAT";

    ret = GetMemberValueOfSysViewCmd(cmd1, entryUsed, &key1Cnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd2, nodeCnt, &key2Cnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd3, undoNum, &undoCnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);

    AW_MACRO_EXPECT_EQ_INT(undoCnt, 0);
    AW_MACRO_EXPECT_EQ_INT(key1Cnt, 1);
    AW_MACRO_EXPECT_EQ_INT(key2Cnt, 1);

    // 先更新一条
    ret = TestTransStartAsync(g_conn_async, config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InserOrUpdateDataForYang(0, tableName, GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    memset(&data, 0, sizeof(AsyncUserDataT)); 

    ret = GetMemberValueOfSysViewCmd(cmd1, entryUsed, &key1Cnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd2, nodeCnt, &key2Cnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd3, undoNum, &undoCnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);

    AW_MACRO_EXPECT_EQ_INT(undoCnt, 1);
    AW_MACRO_EXPECT_EQ_INT(key1Cnt, 2);
    AW_MACRO_EXPECT_EQ_INT(key2Cnt, 2);

    // 重复更新，触发undo合并
    for (int32_t i = 2; i < 100; ++i) {
        ret = InserOrUpdateDataForYang(0, tableName, GMC_OPERATION_REPLACE_GRAPH, GMC_YANG_PROPERTY_OPERATION_REPLACE, i * 4);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GetMemberValueOfSysViewCmd(cmd1, entryUsed, &key1Cnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd2, nodeCnt, &key2Cnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd3, undoNum, &undoCnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);

    AW_MACRO_EXPECT_EQ_INT(undoCnt, 1);
    AW_MACRO_EXPECT_EQ_INT(key1Cnt, 2);
    AW_MACRO_EXPECT_EQ_INT(key2Cnt, 2);

    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GetMemberValueOfSysViewCmd(cmd3, undoNum, &undoCnt);
    AW_MACRO_ASSERT_EQ_INT(ret, 0);
    AW_MACRO_EXPECT_EQ_INT(undoCnt, 0);

    int cnt = 100;
    while (cnt--) {
        ret = GetMemberValueOfSysViewCmd(cmd1, entryUsed, &key1Cnt);
        AW_MACRO_ASSERT_EQ_INT(ret, 0);
        ret = GetMemberValueOfSysViewCmd(cmd2, nodeCnt, &key2Cnt);
        AW_MACRO_ASSERT_EQ_INT(ret, 0);
        if (key1Cnt != 1 || key2Cnt != 1) {
            usleep(100 * 1000);
            continue;
        } else {
            break;
        }
    }

    AW_MACRO_EXPECT_EQ_INT(key1Cnt, 1);
    AW_MACRO_EXPECT_EQ_INT(key2Cnt, 1);

    ret = GmcDropVertexLabel(g_stmt_sync, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void ModelCheck(GmcStmtT *stmt)
{
    int ret = 0;
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(stmt, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(true, checkData.validateRes);
    if (checkData.validateRes == false) {
        AW_FUN_Log(LOG_INFO, "GmcYangValidateModelAsync result is false, failcount is %d.", checkData.failCount);
    }
    memset(&checkData, 0, sizeof(YangValidateUserDataT));
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 009.乐观事务，RR隔离级别下使用GMC_OPERATION_INSERT和GMC_OPERATION_DELETE_GRAPH对表重复操作
TEST_F(DTS2025052737399, Yang_122_DTS009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char tableName[] = "T90";
    char tableName_node[] = "list_1";

    const char *nameSpace1 = "Namespace001_2_A";
    char *v_schema = NULL;
    char *e_schema = NULL;
    char tablePath[] = "./T90.gmjson";
    char EdgePath[] = "./EdgeLabel.gmjson";
    AsyncUserDataT data = {0};

    // 拉起gmserver
    int32_t ret;

    ret = GmcUseNamespaceAsync(g_stmt_async, nameSpace1,  use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    readJanssonFile(tablePath, &v_schema);
    ASSERT_NE((void *)NULL, v_schema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, v_schema, g_labelConfig,  create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK,ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    readJanssonFile(EdgePath, &e_schema);
    ASSERT_NE((void *)NULL, e_schema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, e_schema, g_labelConfig,  create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK,ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ModelCheck(g_stmt_async);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_REPEATABLE;
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX;

    int64_t key1Cnt = 0;
    int64_t key2Cnt = 0;
    int64_t undoCnt = -1;
    char entryUsed[32] = "ENTRY_USED";
    char nodeCnt[32] = "NODE_COUNT";
    char undoNum[32] = "UNDORECORD_NUM";
    char cmd1[256] = "gmsysview -q V\\$STORAGE_HASH_INDEX_STAT -f UNIQUEINDEX_NAME=list_key1";
    char cmd2[256] = "gmsysview -q V\\$STORAGE_HASH_LINKLIST_INDEX_STAT -f NONUNIQUEINDEX_NAME=list_key2";
    char cmd3[256] = "gmsysview -q V\\$STORAGE_UNDO_STAT";

    ret = GmcAllocStmt(g_conn_async, &g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestTransStartAsync(g_conn_async, config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InserOrUpdateDataForYang(0, tableName, GMC_OPERATION_INSERT, GMC_YANG_PROPERTY_OPERATION_CREATE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    memset(&data, 0, sizeof(AsyncUserDataT)); 

    ret = GetMemberValueOfSysViewCmd(cmd1, entryUsed, &key1Cnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd2, nodeCnt, &key2Cnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd3, undoNum, &undoCnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);

    AW_MACRO_EXPECT_EQ_INT(undoCnt, 1);
    AW_MACRO_EXPECT_EQ_INT(key1Cnt, 0);
    AW_MACRO_EXPECT_EQ_INT(key2Cnt, 0);

    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    ret = TestTransStartAsync(g_conn_async, config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "T90", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 重复更新，触发undo合并
    for (int32_t i = 2; i < 100; ++i) {    
        ret = InserOrUpdateDataForNode(i, tableName_node, GMC_OPERATION_INSERT, GMC_YANG_PROPERTY_OPERATION_CREATE, i);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = DeleteDataForNode(i, tableName_node, GMC_OPERATION_DELETE_GRAPH, i);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    memset(&data, 0, sizeof(AsyncUserDataT)); 

    ret = GetMemberValueOfSysViewCmd(cmd1, entryUsed, &key1Cnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd2, nodeCnt, &key2Cnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd3, undoNum, &undoCnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);

    AW_MACRO_EXPECT_EQ_INT(undoCnt, 0);
    AW_MACRO_EXPECT_EQ_INT(key1Cnt, 0);
    AW_MACRO_EXPECT_EQ_INT(key2Cnt, 0);

    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GetMemberValueOfSysViewCmd(cmd3, undoNum, &undoCnt);
    AW_MACRO_ASSERT_EQ_INT(ret, 0);
    AW_MACRO_EXPECT_EQ_INT(undoCnt, 0);

    int cnt = 100;
    while (cnt--) {
        ret = GetMemberValueOfSysViewCmd(cmd1, entryUsed, &key1Cnt);
        AW_MACRO_ASSERT_EQ_INT(ret, 0);
        ret = GetMemberValueOfSysViewCmd(cmd2, nodeCnt, &key2Cnt);
        AW_MACRO_ASSERT_EQ_INT(ret, 0);
        if (key1Cnt != 0 || key2Cnt != 0) {
            usleep(100 * 1000);
            continue;
        } else {
            break;
        }
    }

    AW_MACRO_EXPECT_EQ_INT(key1Cnt, 0);
    AW_MACRO_EXPECT_EQ_INT(key2Cnt, 0);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 010.乐观事务，RR隔离级别下使用GMC_OPERATION_REMOVE_GRAPH对表重复操作
TEST_F(DTS2025052737399, Yang_122_DTS010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char tableName[] = "T90";
    char tableName_node[] = "list_1";
    const char *nameSpace1 = "Namespace001_2_A";
    char *v_schema = NULL;
    char *e_schema = NULL;
    char tablePath[] = "./T90.gmjson";
    char EdgePath[] = "./EdgeLabel.gmjson";
    AsyncUserDataT data = {0};

    // 拉起gmserver
    int32_t ret;

    ret = GmcUseNamespaceAsync(g_stmt_async, nameSpace1,  use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    readJanssonFile(tablePath, &v_schema);
    ASSERT_NE((void *)NULL, v_schema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, v_schema, g_labelConfig,  create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK,ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    readJanssonFile(EdgePath, &e_schema);
    ASSERT_NE((void *)NULL, e_schema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, e_schema, g_labelConfig,  create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK,ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ModelCheck(g_stmt_async);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_REPEATABLE;
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX;

    int64_t key1Cnt = 0;
    int64_t key2Cnt = 0;
    int64_t undoCnt = -1;
    char entryUsed[32] = "ENTRY_USED";
    char nodeCnt[32] = "NODE_COUNT";
    char undoNum[32] = "UNDORECORD_NUM";
    char cmd1[256] = "gmsysview -q V\\$STORAGE_HASH_INDEX_STAT -f UNIQUEINDEX_NAME=list_key1";
    char cmd2[256] = "gmsysview -q V\\$STORAGE_HASH_LINKLIST_INDEX_STAT -f NONUNIQUEINDEX_NAME=list_key2";
    char cmd3[256] = "gmsysview -q V\\$STORAGE_UNDO_STAT";

    ret = GmcAllocStmt(g_conn_async, &g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestTransStartAsync(g_conn_async, config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InserOrUpdateDataForYang(0, tableName, GMC_OPERATION_INSERT, GMC_YANG_PROPERTY_OPERATION_CREATE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    memset(&data, 0, sizeof(AsyncUserDataT)); 

    ret = GetMemberValueOfSysViewCmd(cmd1, entryUsed, &key1Cnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd2, nodeCnt, &key2Cnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd3, undoNum, &undoCnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);

    AW_MACRO_EXPECT_EQ_INT(undoCnt, 1);
    AW_MACRO_EXPECT_EQ_INT(key1Cnt, 0);
    AW_MACRO_EXPECT_EQ_INT(key2Cnt, 0);
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    ret = TestTransStartAsync(g_conn_async, config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "T90", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 重复更新，触发undo合并
    for (int32_t i = 2; i < 100; ++i) {
        ret = InserOrUpdateDataForNode(i, tableName_node, GMC_OPERATION_INSERT, GMC_YANG_PROPERTY_OPERATION_CREATE, i);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = DeleteDataForNode(i, tableName_node, GMC_OPERATION_REMOVE_GRAPH, i);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    memset(&data, 0, sizeof(AsyncUserDataT)); 

    ret = GetMemberValueOfSysViewCmd(cmd1, entryUsed, &key1Cnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd2, nodeCnt, &key2Cnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd3, undoNum, &undoCnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);

    AW_MACRO_EXPECT_EQ_INT(undoCnt, 0);
    AW_MACRO_EXPECT_EQ_INT(key1Cnt, 0);
    AW_MACRO_EXPECT_EQ_INT(key2Cnt, 0);

    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GetMemberValueOfSysViewCmd(cmd3, undoNum, &undoCnt);
    AW_MACRO_ASSERT_EQ_INT(ret, 0);
    AW_MACRO_EXPECT_EQ_INT(undoCnt, 0);

    int cnt = 100;
    while (cnt--) {
        ret = GetMemberValueOfSysViewCmd(cmd1, entryUsed, &key1Cnt);
        AW_MACRO_ASSERT_EQ_INT(ret, 0);
        ret = GetMemberValueOfSysViewCmd(cmd2, nodeCnt, &key2Cnt);
        AW_MACRO_ASSERT_EQ_INT(ret, 0);
        if (key1Cnt != 1 || key2Cnt != 1) {
            usleep(100 * 1000);
            continue;
        } else {
            break;
        }
    }

    AW_MACRO_EXPECT_EQ_INT(key1Cnt, 0);
    AW_MACRO_EXPECT_EQ_INT(key2Cnt, 0);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 011.对同一条数据反复做merge操作，之后再删除数据，再重新插入同样的数据，验证无内存泄漏，重新插入可以插入成功
TEST_F(DTS2025052737399, Yang_122_DTS011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char tableName[] = "T90";
    const char *nameSpace1 = "Namespace001_2_A";
    char *v_schema = NULL;
    char tablePath[] = "./T90.gmjson";
    AsyncUserDataT data = {0};

    // 拉起gmserver
    int32_t ret;

    ret = GmcUseNamespaceAsync(g_stmt_async, nameSpace1,  use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    readJanssonFile(tablePath, &v_schema);
    ASSERT_NE((void *)NULL, v_schema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, v_schema, g_labelConfig,  create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK,ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_REPEATABLE;
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX;
    ret = TestTransStartAsync(g_conn_async, config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = InserOrUpdateDataForYang(0, tableName, GMC_OPERATION_INSERT, GMC_YANG_PROPERTY_OPERATION_CREATE, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    memset(&data, 0, sizeof(AsyncUserDataT)); 

    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int64_t key1Cnt = 0;
    int64_t key2Cnt = 0;
    int64_t undoCnt = -1;
    char entryUsed[32] = "ENTRY_USED";
    char nodeCnt[32] = "NODE_COUNT";
    char undoNum[32] = "UNDORECORD_NUM";
    char cmd1[256] = "gmsysview -q V\\$STORAGE_HASH_INDEX_STAT -f UNIQUEINDEX_NAME=T90_PK_1";
    char cmd2[256] = "gmsysview -q V\\$STORAGE_HASH_LINKLIST_INDEX_STAT -f NONUNIQUEINDEX_NAME=T90_PK_2";
    char cmd3[256] = "gmsysview -q V\\$STORAGE_UNDO_STAT";

    ret = GetMemberValueOfSysViewCmd(cmd1, entryUsed, &key1Cnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd2, nodeCnt, &key2Cnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd3, undoNum, &undoCnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);

    AW_MACRO_EXPECT_EQ_INT(undoCnt, 0);
    AW_MACRO_EXPECT_EQ_INT(key1Cnt, 1);
    AW_MACRO_EXPECT_EQ_INT(key2Cnt, 1);

    // 先更新一条
    ret = TestTransStartAsync(g_conn_async, config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InserOrUpdateDataForYang(0, tableName, GMC_OPERATION_MERGE, GMC_YANG_PROPERTY_OPERATION_MERGE, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    memset(&data, 0, sizeof(AsyncUserDataT)); 

    ret = GetMemberValueOfSysViewCmd(cmd1, entryUsed, &key1Cnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd2, nodeCnt, &key2Cnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd3, undoNum, &undoCnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);
    AW_MACRO_EXPECT_EQ_INT(undoCnt, 1);
    AW_MACRO_EXPECT_EQ_INT(key1Cnt, 2);
    AW_MACRO_EXPECT_EQ_INT(key2Cnt, 2);

    // 重复更新，触发undo合并
    for (int32_t i = 2; i < 100; ++i) {
        ret = InserOrUpdateDataForYang(0, tableName, GMC_OPERATION_MERGE, GMC_YANG_PROPERTY_OPERATION_MERGE, i * 4);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    memset(&data, 0, sizeof(AsyncUserDataT)); 

    ret = GetMemberValueOfSysViewCmd(cmd1, entryUsed, &key1Cnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd2, nodeCnt, &key2Cnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd3, undoNum, &undoCnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);

    AW_MACRO_EXPECT_EQ_INT(undoCnt, 1);
    AW_MACRO_EXPECT_EQ_INT(key1Cnt, 2);
    AW_MACRO_EXPECT_EQ_INT(key2Cnt, 2);

    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GetMemberValueOfSysViewCmd(cmd3, undoNum, &undoCnt);
    AW_MACRO_ASSERT_EQ_INT(ret, 0);
    AW_MACRO_EXPECT_EQ_INT(undoCnt, 0);

    int cnt = 100;
    while (cnt--) {
        ret = GetMemberValueOfSysViewCmd(cmd1, entryUsed, &key1Cnt);
        AW_MACRO_ASSERT_EQ_INT(ret, 0);
        ret = GetMemberValueOfSysViewCmd(cmd2, nodeCnt, &key2Cnt);
        AW_MACRO_ASSERT_EQ_INT(ret, 0);
        if (key1Cnt != 1 || key2Cnt != 1) {
            usleep(100 * 1000);
            continue;
        } else {
            break;
        }
    }

    AW_MACRO_EXPECT_EQ_INT(key1Cnt, 1);
    AW_MACRO_EXPECT_EQ_INT(key2Cnt, 1);

    ret = TestTransStartAsync(g_conn_async, config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "T90", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    memset(&data, 0, sizeof(AsyncUserDataT)); 

    ret = GetMemberValueOfSysViewCmd(cmd1, entryUsed, &key1Cnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd2, nodeCnt, &key2Cnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd3, undoNum, &undoCnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);

    AW_MACRO_EXPECT_EQ_INT(undoCnt, 1);
    AW_MACRO_EXPECT_EQ_INT(key1Cnt, 1);
    AW_MACRO_EXPECT_EQ_INT(key2Cnt, 1);
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestTransStartAsync(g_conn_async, config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InserOrUpdateDataForYang(0, tableName, GMC_OPERATION_INSERT, GMC_YANG_PROPERTY_OPERATION_CREATE, 400);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    memset(&data, 0, sizeof(AsyncUserDataT)); 
    sleep(5);
    ret = GetMemberValueOfSysViewCmd(cmd1, entryUsed, &key1Cnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd2, nodeCnt, &key2Cnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd3, undoNum, &undoCnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);

    AW_MACRO_EXPECT_EQ_INT(undoCnt, 1);
    AW_MACRO_EXPECT_EQ_INT(key1Cnt, 1);
    AW_MACRO_EXPECT_EQ_INT(key2Cnt, 1);
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(5);
    ret = GetMemberValueOfSysViewCmd(cmd1, entryUsed, &key1Cnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd2, nodeCnt, &key2Cnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);
    ret = GetMemberValueOfSysViewCmd(cmd3, undoNum, &undoCnt);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);

    AW_MACRO_EXPECT_EQ_INT(undoCnt, 0);
    AW_MACRO_EXPECT_EQ_INT(key1Cnt, 1);
    AW_MACRO_EXPECT_EQ_INT(key2Cnt, 1);

    ret = GmcDropVertexLabel(g_stmt_sync, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
