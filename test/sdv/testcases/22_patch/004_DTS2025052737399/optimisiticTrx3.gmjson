[{"type": "record", "name": "TestOptimistic3", "fields": [{"name": "F0", "type": "uint64", "nullable": false}, {"name": "F1", "type": "uint64", "nullable": false}, {"name": "F2", "type": "uint64", "nullable": false}, {"name": "F3", "type": "uint64", "nullable": true}, {"name": "F4", "type": "uint64", "nullable": true}], "keys": [{"node": "TestOptimistic3", "name": "T35_K0", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "TestOptimistic3", "name": "TestOptimistic3_key1", "fields": ["F1", "F2"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": true}}, {"node": "TestOptimistic3", "name": "TestOptimistic3_key2", "fields": ["F3", "F4"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": false}}]}]