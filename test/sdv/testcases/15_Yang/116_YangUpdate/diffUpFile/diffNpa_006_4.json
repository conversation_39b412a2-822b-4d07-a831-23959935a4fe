alias_ContainerOne:update[(pri<PERSON><PERSON>(ID:1)),(pri<PERSON><PERSON>(ID:1))]
alias_ContainerOne.F1:update(100,101)
alias_ContainerOne.F5:create(555)
alias_ContainerOne.F6:remove(666)
alias_ContainerOne.ID1:create(NIL:26)
alias_ContainerOne.ContainerTwo:create
ContainerTwo.F0:create(100)
alias_ContainerOne.ContainerThree:remove
ContainerThree.F1:remove(100)
alias_ContainerOne.Choice:create
Choice.CaseOne:create
CaseOne.F0:create(100)
CaseOne.F7:create(100)
alias_ContainerOne.alias_ListOne:update[(priKey(PID:1,F0:str000)),(priKey(PID:1,F0:str000))]
alias_ListOne.F3:create(100)
alias_ListOne.F4:remove(100)
alias_ListOne.F7:create(NIL:8)
alias_ContainerOne.alias_ListOne:update[(pri<PERSON>ey(PID:1,F0:str001), preKey(PID:1,F0:str000)),(priKey(PID:1,F0:str001), preKey(PID:1,F0:str000))]
alias_ListOne.F3:create(100)
alias_ListOne.F4:remove(100)
alias_ListOne.F7:create(NIL:8)
alias_ContainerOne.alias_ListOne:update[(priKey(PID:1,F0:str002), preKey(PID:1,F0:str001)),(priKey(PID:1,F0:str002), preKey(PID:1,F0:str001))]
alias_ListOne.F3:create(100)
alias_ListOne.F4:remove(100)
alias_ListOne.F7:create(NIL:8)
alias_ContainerOne.alias_LeafListTwo:create[(priKey(PID:1,F0:2)),(NULL)]
alias_ContainerOne.alias_LeafListTwo:create[(priKey(PID:1,F0:3), preKey(PID:1,F0:2)),(NULL)]
