alias_ContainerOne:update[(pri<PERSON><PERSON>(ID:1)),(pri<PERSON><PERSON>(ID:1))]
alias_ContainerOne.F1:update(101,100)
alias_ContainerOne.F5:remove(555)
alias_ContainerOne.F6:create(666)
alias_ContainerOne.ID1:remove(NIL:26)
alias_ContainerOne.F25_addNew:remove(555)
alias_ContainerOne.ContainerTwo:remove
ContainerTwo.F0:remove(100)
alias_ContainerOne.ContainerThree:create
ContainerThree.F1:create(100)
alias_ContainerOne.Choice:remove
Choice.CaseOne:remove
CaseOne.F0:remove(100)
alias_ContainerOne.ContainerAddNew:remove
ContainerAddNew.F0:remove(100)
ContainerAddNew.F1:remove(100)
alias_ContainerOne.alias_ListOne:update[(priKey(PID:1,F0:str000)),(priKey(PID:1,F0:str000))]
alias_ListOne.F3:remove(100)
alias_ListOne.F4:create(100)
alias_ListOne.F8_AddNew:remove(100)
alias_ListOne.ContainerAddNew:remove
ContainerAddNew.F0:remove(100)
ContainerAddNew.F1:remove(100)
alias_ContainerOne.alias_ListOne:update[(priKey(PID:1,F0:str001), preKey(PID:1,F0:str000)),(priKey(PID:1,F0:str001), preKey(PID:1,F0:str000))]
alias_ListOne.F3:remove(100)
alias_ListOne.F4:create(100)
alias_ListOne.F8_AddNew:remove(100)
alias_ListOne.ContainerAddNew:remove
ContainerAddNew.F0:remove(100)
ContainerAddNew.F1:remove(100)
alias_ContainerOne.alias_ListOne:update[(priKey(PID:1,F0:str002), preKey(PID:1,F0:str001)),(priKey(PID:1,F0:str002), preKey(PID:1,F0:str001))]
alias_ListOne.F3:remove(100)
alias_ListOne.F4:create(100)
alias_ListOne.F8_AddNew:remove(100)
alias_ListOne.ContainerAddNew:remove
ContainerAddNew.F0:remove(100)
ContainerAddNew.F1:remove(100)
alias_ContainerOne.alias_LeafList:remove[(NULL),(priKey(PID:1,F0:2))]
alias_ContainerOne.alias_LeafList:remove[(NULL),(priKey(PID:1,F0:3), preKey(PID:1,F0:2))]
