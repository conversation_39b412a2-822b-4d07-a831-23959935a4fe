[{"type": "container", "name": "ContainerOne", "alias": "alias_Container<PERSON>ne", "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true, "default": 555, "clause": [{"type": "when", "formula": "/alias_ContainerOne/F1 = 100"}]}, {"name": "F6", "type": "uint32", "nullable": true, "default": 666, "clause": [{"type": "when", "formula": "/alias_ContainerOne/F1 != 100"}]}, {"name": "F7", "type": "uint32", "nullable": true, "default": 777}, {"name": "F8", "type": "uint32", "nullable": true, "default": 888}, {"name": "F9", "type": "uint32", "nullable": true, "default": 999}, {"name": "F10", "type": "string", "size": 10, "nullable": true}, {"name": "F12", "type": "uint16", "nullable": true}, {"name": "F13", "type": "uint32", "nullable": true}, {"name": "F14", "type": "int64", "nullable": true}, {"name": "F15", "type": "uint64", "nullable": true}, {"name": "F16", "type": "time", "nullable": true}, {"name": "F17", "type": "uint8: 4", "nullable": true}, {"name": "F18", "type": "uint16: 15", "nullable": true}, {"name": "F19", "type": "uint32: 31", "nullable": true}, {"name": "F20", "type": "bytes", "size": 7, "nullable": true}, {"name": "F21", "type": "fixed", "size": 7, "nullable": true}, {"name": "F22", "type": "bitmap", "size": 128, "nullable": true}, {"name": "F23", "type": "uint64: 59", "nullable": true}, {"name": "F24", "type": "int32", "nullable": true}, {"name": "ID1", "type": "identity", "nullable": true, "default": "level-1", "enumerate_identity": "ID1", "enumerate": [{"name": "level1", "value": 1, "derived-paths": [{"derived-path": "level1"}]}, {"name": "level2", "value": 2, "derived-paths": [{"derived-path": "level1/level2"}]}, {"name": "level3", "value": 3, "derived-paths": [{"derived-path": "level1/level2/level3"}]}, {"name": "level0", "value": 0, "derived-paths": [{"derived-path": "level0"}]}, {"name": "level-1", "value": -1, "derived-paths": [{"derived-path": "level-1"}]}, {"name": "level-2", "value": -2, "derived-paths": [{"derived-path": "level-1/level-2"}]}, {"name": "level-3", "value": -3, "derived-paths": [{"derived-path": "level-1/level-2/level-3"}]}]}, {"name": "ID2", "type": "enum", "nullable": true, "default": "level-1", "enumerate_identity": "ID2", "enumerate": [{"name": "level1", "value": 1}, {"name": "level2", "value": 2}, {"name": "level3", "value": 3}, {"name": "level0", "value": 0}, {"name": "level-1", "value": -1}, {"name": "level-2", "value": -2}, {"name": "level-3", "value": -3}]}, {"type": "container", "name": "ContainerTwo", "presence": false, "clause": [{"type": "when", "formula": "/alias_ContainerOne/F1 = 100"}], "fields": [{"name": "F0", "type": "uint32", "default": 100, "clause": [{"type": "when", "formula": "/alias_ContainerOne/F1 = 100"}]}, {"name": "F1", "type": "int32", "default": 100, "clause": [{"type": "when", "formula": "/alias_ContainerOne/F1 != 100"}]}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}, {"type": "container", "name": "Container<PERSON>hree", "presence": false, "clause": [{"type": "when", "formula": "/alias_ContainerOne/F1 != 100"}], "fields": [{"name": "F0", "type": "uint32", "default": 100, "clause": [{"type": "when", "formula": "/alias_ContainerOne/F1 = 100"}]}, {"name": "F1", "type": "int32", "default": 100, "clause": [{"type": "when", "formula": "/alias_ContainerOne/F1 != 100"}]}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}, {"type": "container", "name": "ContainerFour", "presence": true, "fields": [{"name": "F0", "type": "uint32"}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}, {"type": "container", "name": "ContainerFive", "presence": false, "fields": [{"name": "F0", "type": "uint32", "default": 100, "clause": [{"type": "when", "formula": "/alias_ContainerOne/F1 = 100"}]}, {"name": "F1", "type": "int32", "default": 100, "clause": [{"type": "when", "formula": "/alias_ContainerOne/F1 != 100"}]}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}]}]}, {"type": "choice", "name": "Choice", "clause": [{"type": "when", "formula": "/alias_ContainerOne/F1 = 100"}], "fields": [{"type": "case", "name": "CaseOne", "default": true, "fields": [{"name": "F0", "type": "uint32", "default": 100, "clause": [{"type": "when", "formula": "/alias_ContainerOne/F1 = 100"}]}, {"name": "F1", "type": "int32", "default": 100, "clause": [{"type": "when", "formula": "/alias_ContainerOne/F1 != 100"}]}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}, {"type": "container", "name": "CaseContainerOne", "presence": true, "fields": [{"name": "F0", "type": "uint32"}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}, {"type": "container", "name": "CaseContainerTwo", "presence": false, "fields": [{"name": "F0", "type": "uint32"}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}]}, {"type": "case", "name": "CaseTwo", "fields": [{"name": "F0", "type": "uint32", "default": 100, "clause": [{"type": "when", "formula": "/alias_ContainerOne/F1 = 100"}]}, {"name": "F1", "type": "int32", "default": 100, "clause": [{"type": "when", "formula": "/alias_ContainerOne/F1 != 100"}]}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}, {"type": "container", "name": "CaseContainerOne", "presence": true, "fields": [{"name": "F0", "type": "uint32"}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}, {"type": "container", "name": "CaseContainerTwo", "presence": false, "fields": [{"name": "F0", "type": "uint32"}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}]}]}, {"type": "choice", "name": "ChoiceTwo", "fields": [{"type": "case", "name": "CaseOne", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}, {"type": "container", "name": "CaseContainerOne", "presence": true, "fields": [{"name": "F0", "type": "uint32"}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}, {"type": "container", "name": "CaseContainerTwo", "presence": false, "fields": [{"name": "F0", "type": "uint32"}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}]}]}], "keys": [{"node": "ContainerOne", "name": "PK", "fields": ["ID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "ListOne", "alias": "alias_<PERSON><PERSON>ne", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "string", "nullable": false}, {"name": "F1", "type": "uint32"}, {"name": "F2", "type": "uint32"}, {"name": "F3", "type": "uint32", "default": 100, "clause": [{"type": "when", "formula": "/alias_ContainerOne/F1 = 100"}]}, {"name": "F4", "type": "uint32", "default": 100, "clause": [{"type": "when", "formula": "/alias_ContainerOne/F1 != 100"}]}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string", "clause": [{"type": "when", "formula": "/alias_ContainerOne/alias_ListOne[F0=current()]/F1 = 300"}]}, {"name": "F7", "type": "boolean"}, {"type": "container", "name": "ContainerTwo", "presence": false, "clause": [{"type": "when", "formula": "/alias_ContainerOne/F1 = 100"}], "fields": [{"name": "F0", "type": "uint32", "default": 100, "clause": [{"type": "when", "formula": "/alias_ContainerOne/F1 = 100"}]}, {"name": "F1", "type": "int32", "default": 100, "clause": [{"type": "when", "formula": "/alias_ContainerOne/F1 != 100"}]}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}, {"type": "container", "name": "Container<PERSON>hree", "presence": false, "clause": [{"type": "when", "formula": "/alias_ContainerOne/F1 != 100"}], "fields": [{"name": "F0", "type": "uint32", "default": 100, "clause": [{"type": "when", "formula": "/alias_ContainerOne/F1 = 100"}]}, {"name": "F1", "type": "int32", "default": 100, "clause": [{"type": "when", "formula": "/alias_ContainerOne/F1 != 100"}]}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}, {"type": "container", "name": "ContainerFour", "presence": true, "fields": [{"name": "F0", "type": "uint32"}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}, {"type": "container", "name": "ContainerFive", "presence": false, "fields": [{"name": "F0", "type": "uint32", "default": 100, "clause": [{"type": "when", "formula": "/alias_ContainerOne/F1 = 100"}]}, {"name": "F1", "type": "int32", "default": 100, "clause": [{"type": "when", "formula": "/alias_ContainerOne/F1 != 100"}]}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}]}]}, {"type": "choice", "name": "Choice", "clause": [{"type": "when", "formula": "/alias_ContainerOne/F1 = 100"}], "fields": [{"type": "case", "name": "CaseOne", "default": true, "fields": [{"name": "F0", "type": "uint32", "default": 100, "clause": [{"type": "when", "formula": "/alias_ContainerOne/F1 = 100"}]}, {"name": "F1", "type": "int32", "default": 100, "clause": [{"type": "when", "formula": "/alias_ContainerOne/F1 != 100"}]}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}, {"type": "container", "name": "CaseContainerOne", "presence": true, "fields": [{"name": "F0", "type": "uint32"}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}, {"type": "container", "name": "CaseContainerTwo", "presence": false, "fields": [{"name": "F0", "type": "uint32"}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}]}, {"type": "case", "name": "CaseTwo", "fields": [{"name": "F0", "type": "uint32", "default": 100, "clause": [{"type": "when", "formula": "/alias_ContainerOne/F1 = 100"}]}, {"name": "F1", "type": "int32", "default": 100, "clause": [{"type": "when", "formula": "/alias_ContainerOne/F1 != 100"}]}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}, {"type": "container", "name": "CaseContainerOne", "presence": true, "fields": [{"name": "F0", "type": "uint32"}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}, {"type": "container", "name": "CaseContainerTwo", "presence": false, "fields": [{"name": "F0", "type": "uint32"}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}]}]}, {"type": "choice", "name": "ChoiceTwo", "is_config": false, "fields": [{"type": "case", "name": "CaseOne", "is_config": false, "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}, {"type": "container", "name": "CaseContainerOne", "is_config": false, "presence": true, "fields": [{"name": "F0", "type": "uint32"}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}, {"type": "container", "name": "CaseContainerTwo", "is_config": false, "presence": false, "fields": [{"name": "F0", "type": "uint32"}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}]}]}], "keys": [{"fields": ["PID", "F0"], "node": "ListOne", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "ListOne", "name": "<PERSON><PERSON><PERSON>", "fields": ["PID", "F1"], "index": {"type": "list_localhash"}, "constraints": {"unique": true, "null_check": true}}]}, {"type": "leaf-list", "name": "LeafListTwo", "alias": "alias_LeafListTwo", "min-elements": 0, "max-elements": 10000, "is_config": true, "clause": [{"type": "when", "formula": "/alias_ContainerOne/F2 = 100"}, {"type": "must", "formula": "/alias_ContainerOne/F2 = 100"}], "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false, "default": [2, 3, 4]}], "keys": [{"fields": ["PID", "F0"], "node": "LeafListTwo", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "leaf-list", "name": "LeafList", "alias": "alias_LeafList", "is_config": false, "min-elements": 0, "max-elements": 10000, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false, "default": [2, 3, 4]}], "keys": [{"fields": ["PID", "F0"], "node": "LeafList", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "LeafList", "name": "LeafList_listlocalhash", "fields": ["PID", "F0"], "index": {"type": "list_localhash"}, "constraints": {"unique": true, "null_check": true}}, {"node": "LeafList", "name": "LeafList_localhash", "fields": ["PID", "F0"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": true}}, {"node": "LeafList", "name": "LeafList_local", "fields": ["PID", "F0"], "index": {"type": "local"}, "constraints": {"unique": true}}]}, {"type": "leaf-list", "name": "LeafList<PERSON><PERSON><PERSON>", "alias": "alias_<PERSON><PERSON>ist<PERSON><PERSON>ee", "min-elements": 0, "max-elements": 10000, "clause": [{"type": "when", "formula": "/alias_ContainerOne/F1 != 100"}], "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false, "default": [2, 3, 4]}], "keys": [{"fields": ["PID", "F0"], "node": "LeafList<PERSON><PERSON><PERSON>", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "ListTwo", "alias": "alias_ListTwo", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32"}, {"name": "F2", "type": "uint32"}, {"name": "F3", "type": "uint32", "default": 100, "clause": [{"type": "when", "formula": "/alias_ContainerOne/F1 = 100"}]}, {"name": "F4", "type": "uint32", "default": 100, "clause": [{"type": "when", "formula": "/alias_ContainerOne/F1 != 100"}]}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "boolean"}, {"type": "container", "name": "ContainerTwo", "presence": false, "clause": [{"type": "when", "formula": "/alias_ContainerOne/F1 = 100"}], "fields": [{"name": "F0", "type": "uint32", "default": 100, "clause": [{"type": "when", "formula": "/alias_ContainerOne/F1 = 100"}]}, {"name": "F1", "type": "int32", "default": 100, "clause": [{"type": "when", "formula": "/alias_ContainerOne/F1 != 100"}]}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}, {"type": "container", "name": "Container<PERSON>hree", "presence": false, "clause": [{"type": "when", "formula": "/alias_ContainerOne/F1 != 100"}], "fields": [{"name": "F0", "type": "uint32", "default": 100, "clause": [{"type": "when", "formula": "/alias_ContainerOne/F1 = 100"}]}, {"name": "F1", "type": "int32", "default": 100, "clause": [{"type": "when", "formula": "/alias_ContainerOne/F1 != 100"}]}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}, {"type": "container", "name": "ContainerFour", "presence": true, "fields": [{"name": "F0", "type": "uint32"}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}, {"type": "container", "name": "ContainerFive", "presence": false, "fields": [{"name": "F0", "type": "uint32", "default": 100, "clause": [{"type": "when", "formula": "/alias_ContainerOne/F1 = 100"}]}, {"name": "F1", "type": "int32", "default": 100, "clause": [{"type": "when", "formula": "/alias_ContainerOne/F1 != 100"}]}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}]}]}, {"type": "choice", "name": "Choice", "clause": [{"type": "when", "formula": "/alias_ContainerOne/F1 = 100"}], "fields": [{"type": "case", "name": "CaseOne", "default": true, "fields": [{"name": "F0", "type": "uint32", "default": 100, "clause": [{"type": "when", "formula": "/alias_ContainerOne/F1 = 100"}]}, {"name": "F1", "type": "int32", "default": 100, "clause": [{"type": "when", "formula": "/alias_ContainerOne/F1 != 100"}]}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}, {"type": "container", "name": "CaseContainerOne", "presence": true, "fields": [{"name": "F0", "type": "uint32"}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}, {"type": "container", "name": "CaseContainerTwo", "presence": false, "fields": [{"name": "F0", "type": "uint32"}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}]}, {"type": "case", "name": "CaseTwo", "fields": [{"name": "F0", "type": "uint32", "default": 100, "clause": [{"type": "when", "formula": "/alias_ContainerOne/F1 = 100"}]}, {"name": "F1", "type": "int32", "default": 100, "clause": [{"type": "when", "formula": "/alias_ContainerOne/F1 != 100"}]}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}, {"type": "container", "name": "CaseContainerOne", "presence": true, "fields": [{"name": "F0", "type": "uint32"}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}, {"type": "container", "name": "CaseContainerTwo", "presence": false, "fields": [{"name": "F0", "type": "uint32"}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}]}]}, {"type": "choice", "name": "ChoiceTwo", "fields": [{"type": "case", "name": "CaseOne", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}, {"type": "container", "name": "CaseContainerOne", "presence": true, "fields": [{"name": "F0", "type": "uint32"}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}, {"type": "container", "name": "CaseContainerTwo", "presence": false, "fields": [{"name": "F0", "type": "uint32"}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}]}]}], "keys": [{"fields": ["PID", "F0"], "node": "ListTwo", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}]