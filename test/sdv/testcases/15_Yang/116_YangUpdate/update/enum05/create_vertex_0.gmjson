[{"type": "leaf-list", "name": "LeafList<PERSON><PERSON><PERSON>", "alias": "alias_<PERSON><PERSON>ist<PERSON><PERSON>ee", "min-elements": 0, "max-elements": 5, "config": {"check_validity": true, "yang_model": 1, "auto_increment": 1, "isFastReadUncommitted": 0}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false}], "keys": [{"fields": ["PID", "F0"], "node": "LeafList<PERSON><PERSON><PERSON>", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}]