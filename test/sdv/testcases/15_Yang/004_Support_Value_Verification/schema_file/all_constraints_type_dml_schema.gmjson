[{"type": "container", "name": "T20_all_type", "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "range": "1|2|3|4|5|6|7|8|9"}, {"name": "F1", "type": "int8", "range": "1|2|3|4|5|6|7|8|9"}, {"name": "F2", "type": "uint8", "range": "1|2|3|4|5|6|7|8|9"}, {"name": "F3", "type": "int16", "range": "1|2|3|4|5|6|7|8|9"}, {"name": "F4", "type": "uint16", "range": "1|2|3|4|5|6|7|8|9"}, {"name": "F5", "type": "int32", "range": "1|2|3|4|5|6|7|8|9"}, {"name": "F6", "type": "int64", "range": "1..9|10..30"}, {"name": "F7", "type": "uint64", "range": "1|2|3|4|5|6|7|8|9"}, {"name": "F8", "type": "float", "range": "1|2|3|4|5|6|7|8|9"}, {"name": "F9", "type": "double", "range": "1..9|20.01|29.98"}, {"name": "F10", "type": "string", "length": "1..9|11.2..13.5"}, {"name": "F11", "type": "string", "nullable": true, "pattern": ["^GMDB$", "^GMDB$", "^G(.*?)B"]}, {"name": "F12", "type": "string", "nullable": true, "invert_pattern": ["^GMDB$", "^ABC$", "^def$"]}], "keys": [{"node": "T20_all_type", "name": "T20_PK", "fields": ["ID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]