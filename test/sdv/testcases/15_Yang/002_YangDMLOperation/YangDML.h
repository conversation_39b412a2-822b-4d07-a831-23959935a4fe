/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: 002_YangDMLOperation
 * Author: hanyang
 * Create: 2022-7-21
 */
#ifndef YANG_DML_OPERATION_TEST_H
#define YANG_DML_OPERATION_TEST_H
#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/prctl.h>
#include <sys/socket.h>
#include <sys/epoll.h>
#include <assert.h>
#include <errno.h>
#include <stdint.h>
#include <typeinfo>
#include <limits.h>
#include "gtest/gtest.h"
#include "jansson.h"
#include "t_datacom_lite.h"

// MS config
const char *g_msConfig = "{\"max_record_count\" : 1000000}";
const char *g_msConfigCCEH = "{\"max_record_count\" : 1000000, \"hash_type\":\"cceh\"}";
const char *g_msConfigTrans = "{\"max_record_count\" : 10000, \"isFastReadUncommitted\":0, \"auto_increment\":1}";
const char *g_msConfigTrans100 = "{\"max_record_count\" : 100, \"isFastReadUncommitted\":0, \"auto_increment\":1}";

// container--container类型的Vertex Name
const char *g_nameConConRoot = "Con_Con_root";
const char *g_nameConConChild01 = "Con_Con_Child_01";
const char *g_nameConConChild02 = "Con_Con_Child_02";
const char *g_nameConConChild03 = "Con_Con_Child_03";
const char *g_nameConConChild04 = "Con_Con_Child_04";
const char *g_nameConConChild05 = "Con_Con_Child_05";

// container--list类型的Vertex Name
const char *g_nameConListRoot = "Con_List_root";
const char *g_nameConListChild01 = "Con_List_Child_01";
const char *g_nameConListChild02 = "Con_List_Child_02";
const char *g_nameConListChild03 = "Con_List_Child_03";
const char *g_nameConListChild04 = "Con_List_Child_04";
const char *g_nameConListChild05 = "Con_List_Child_05";

// container--choice--case类型的Vertex Name
const char *g_nameConChoiceCaseRoot = "Con_Choice_Case_root";
const char *g_nameConChoiceChild01 = "Con_Choice_Child_01";
const char *g_nameConChoiceChild02 = "Con_Choice_Child_02";
const char *g_nameConChoiceChild03 = "Con_Choice_Child_03";
const char *g_nameConChoiceChild04 = "Con_Choice_Child_04";
const char *g_nameConChoiceChild05 = "Con_Choice_Child_05";
const char *g_nameConCaseChild01 = "Con_Case_Child_01";
const char *g_nameConCaseChild02 = "Con_Case_Child_02";
const char *g_nameConCaseChild03 = "Con_Case_Child_03";
const char *g_nameConCaseChild04 = "Con_Case_Child_04";
const char *g_nameConCaseChild05 = "Con_Case_Child_05";
const char *g_nameConCaseChild06 = "Con_Case_Child_06";

const char *g_keyName = "PK";

GmcTxConfigT g_mSTrxConfig;
const char *g_namespace = "NamespaceABC002";
const char *g_namespaceUserName = "abc";

// resource
const char *g_resPoolTestName = "resource_pool_test";
const char *g_resPoolTest =
    R"({
        "name" : "resource_pool_test",
        "pool_id" : 0,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";

void TestCreateLabel(GmcStmtT *stmt)
{
    int ret = 0;
    char *vLabelSchema = NULL;
    AsyncUserDataT data = {0};

    readJanssonFile("schema_file/Con_Con.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateVertexLabelAsync(stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    readJanssonFile("schema_file/Con_Con_Edge.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateEdgeLabelAsync(stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    readJanssonFile("schema_file/Con_List.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateVertexLabelAsync(stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    readJanssonFile("schema_file/Con_List_Edge.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateEdgeLabelAsync(stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    readJanssonFile("schema_file/Con_Choice_Case.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateVertexLabelAsync(stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    readJanssonFile("schema_file/Con_Choice_Case_Edge.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateEdgeLabelAsync(stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;
}

void TestDropLabelAll(GmcStmtT *stmt)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    sleep(1);
    // 删除container--container类型的vertex表和edge表
    ret = GmcClearNamespaceAsync(stmt, g_namespace, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
}

void TestCreateMixLabel(GmcStmtT *stmt)
{
    int ret = 0;
    char *vLabelSchema = NULL;
    AsyncUserDataT data = {0};

    readJanssonFile("schema_file/Yang_Mix.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateVertexLabelAsync(stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    readJanssonFile("schema_file/Yang_Mix_Edge.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateEdgeLabelAsync(stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;
}

void TestDropMixLabel(GmcStmtT *stmt)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    sleep(1);

    ret = GmcClearNamespaceAsync(stmt, g_namespace, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
}

void TestCreateLabelCLCL(GmcStmtT *stmt)
{
    int ret = 0;
    char *vLabelSchema = NULL;
    AsyncUserDataT data = {0};

    readJanssonFile("schema_file/Con_List_Con_List.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateVertexLabelAsync(stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    readJanssonFile("schema_file/Con_List_Con_List_Edge.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateEdgeLabelAsync(stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;
}

void TestDropLabelCLCL(GmcStmtT *stmt)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    sleep(1);

    ret = GmcClearNamespaceAsync(stmt, g_namespace, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
}

void TestCreateLabelCLC(GmcStmtT *stmt)
{
    int ret = 0;
    char *vLabelSchema = NULL;
    AsyncUserDataT data = {0};

    readJanssonFile("schema_file/Con_List_Con.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateVertexLabelAsync(stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    readJanssonFile("schema_file/Con_List_Con_Edge.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateEdgeLabelAsync(stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;
}

void TestDropLabelCLC(GmcStmtT *stmt)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    sleep(1);

    ret = GmcClearNamespaceAsync(stmt, g_namespace, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
}

void TestCreateLabelCLCC(GmcStmtT *stmt)
{
    int ret = 0;
    char *vLabelSchema = NULL;
    AsyncUserDataT data = {0};

    readJanssonFile("schema_file/Con_List_Choice_Case.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateVertexLabelAsync(stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    readJanssonFile("schema_file/Con_List_Choice_Case_Edge.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateEdgeLabelAsync(stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;
}

void TestDropLabelCLCC(GmcStmtT *stmt)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    sleep(1);

    ret = GmcClearNamespaceAsync(stmt, g_namespace, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
}

void TestCreateLabelCLL(GmcStmtT *stmt)
{
    int ret = 0;
    char *vLabelSchema = NULL;
    AsyncUserDataT data = {0};

    readJanssonFile("schema_file/Con_List_List.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateVertexLabelAsync(stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    readJanssonFile("schema_file/Con_List_List_Edge.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateEdgeLabelAsync(stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;
}

void TestDropLabelCLL(GmcStmtT *stmt)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    sleep(1);

    ret = GmcClearNamespaceAsync(stmt, g_namespace, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
}

void TestCreateLabelYang32(GmcStmtT *stmt)
{
    int ret = 0;
    char *vLabelSchema = NULL;
    AsyncUserDataT data = {0};

    readJanssonFile("schema_file/Yang_32.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateVertexLabelAsync(stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    readJanssonFile("schema_file/Yang_32_Edge.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateEdgeLabelAsync(stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;
}

void TestDropLabelYang32(GmcStmtT *stmt)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    sleep(1);

    ret = GmcClearNamespaceAsync(stmt, g_namespace, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char edgeName[1024] = {0};
    for (int i = 1; i <= 31; i++) {
        ret = snprintf(edgeName, 11, "T%02d_to_T%02d", i, (i + 1));
        AW_MACRO_EXPECT_NE_INT(0, ret);
    }

    char labelName[1024] = {0};
    for (int i = 1; i <= 32; i++) {
        ret = snprintf(labelName, 4, "T%02d", i);
        AW_MACRO_EXPECT_NE_INT(0, ret);
    }
}

/******************************DML*******************************************/

int TestBatchPrepare(GmcConnT *conn, GmcBatchT **batch, GmcBatchTypeE batchType = GMC_BATCH_YANG)
{
    int ret = 0;
    GmcBatchOptionT batchOption;

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBatchType(&batchOption, batchType);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }

    ret = GmcBatchPrepare(conn, &batchOption, batch);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }

    return ret;
}

int TestTransStartAsync(GmcConnT *conn, GmcTxConfigT Config)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = GmcTransStartAsync(conn, &Config, trans_start_callback, &data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = testWaitAsyncRecv(&data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        return ret;
    }
}

AsyncUserDataT data1;
int TestTransCommitAsync(GmcConnT *conn)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = GmcTransCommitAsync(conn, trans_commit_callback, &data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = testWaitAsyncRecv(&data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (data.status == GMERR_TRANSACTION_ROLLBACK) {
            AW_FUN_Log(LOG_INFO, "data.status = GMERR_TRANSACTION_ROLLBACK, the transaction will rollback.");
            memset(&data1, 0, sizeof(AsyncUserDataT));
            int ret1 = GmcTransRollBackAsync(conn, trans_rollback_callback, &data1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        }
        return ret;
    }
}

int TestTransRollBackAsync(GmcConnT *conn)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = GmcTransRollBackAsync(conn, trans_commit_callback, &data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = testWaitAsyncRecv(&data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

        return ret;
    }
}

int TestYangSetField(GmcStmtT *stmt, GmcDataTypeE type, void *value, uint32_t size,
    const char *fieldName, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    int ret1 = 0;

    GmcPropValueT propValue;
    memcpy(propValue.propertyName, fieldName, (strlen(fieldName) + 1));
    propValue.type = type;
    propValue.value = value;
    propValue.size = size;
    ret = GmcYangSetVertexProperty(stmt, &propValue, opType);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_DEBUG, "SetProperty failed fieldName(%s)\n", fieldName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret1 = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);
        return ret;
    }

    return ret;
}

// yang模型现在修改为只能ID或PID为主键，只有list可以有PID+其他属性为主键
void TestYangSetVertexProperty_PK(GmcStmtT *stmt, uint32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    uint32_t pkValue = i;
    ret = TestYangSetField(stmt, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "F0", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestYangSetVertexProperty(GmcStmtT *stmt, uint32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    uint32_t value = i;

    uint32_t valueF0 = value;
    ret = TestYangSetField(stmt, GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = value;
    ret = TestYangSetField(stmt, GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = TestYangSetField(stmt, GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestYangSetVertexPropertyWithoutF0(GmcStmtT *stmt, uint32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    uint32_t value = i;

    uint32_t valueF1 = value;
    ret = TestYangSetField(stmt, GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = TestYangSetField(stmt, GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestSetKeyNameAndValue(GmcStmtT *stmt, uint32_t keyvalue, uint32_t numPID = 0, bool isList = false)
{
    int ret;

    // 设置KeyValue，PID为上层节点的ID，自增从1开始，PID=0，代表是根节点
    // yang模型现在修改为只能ID或PID为主键，只有list可以有PID+其他属性为主键
    if (numPID != 0) {
        // 只有list的主键允许设置为PID+属性
        if (isList) {
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &keyvalue, sizeof(uint32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }

    ret = GmcSetIndexKeyName(stmt, g_keyName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// Con--Con预先插入数据
void TestInsertConConRoot(GmcConnT *conn, uint32_t keyvalue)
{
    int ret;
    GmcBatchT *batch = NULL;
    GmcStmtT *stmt_root = NULL;

    ret = GmcAllocStmt(conn, &stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt_root, g_nameConConRoot, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    TestYangSetVertexProperty(stmt_root, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(stmt_root);
}
void TestInsertConConChild(GmcConnT *conn, uint32_t keyvalue, const char * childName)
{
    int ret;
    GmcBatchT *batch = NULL;
    GmcStmtT *stmt_root = NULL;
    GmcStmtT *stmt_child = NULL;

    ret = GmcAllocStmt(conn, &stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_child);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt_root, g_nameConConRoot, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(stmt_child, childName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmt_root, stmt_child);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    TestYangSetVertexProperty(stmt_child, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt_child);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(stmt_root);
    GmcFreeStmt(stmt_child);
}

// Con--List预先插入数据
void TestInsertConListRoot(GmcConnT *conn, uint32_t keyvalue)
{
    int ret;
    GmcBatchT *batch = NULL;
    GmcStmtT *stmt_root = NULL;

    ret = GmcAllocStmt(conn, &stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt_root, g_nameConListRoot, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    TestYangSetVertexProperty(stmt_root, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(stmt_root);
}
void TestInsertConListChild(GmcConnT *conn, uint32_t keyvalue, const char * childName)
{
    int ret;
    GmcBatchT *batch = NULL;
    GmcStmtT *stmt_root = NULL;
    GmcStmtT *stmt_child = NULL;

    ret = GmcAllocStmt(conn, &stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_child);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt_root, g_nameConListRoot, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    uint32_t setKeyValue = 0;
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_child, childName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt_root, stmt_child);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        setKeyValue = i;
        TestYangSetVertexProperty_PK(stmt_child, setKeyValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetVertexPropertyWithoutF0(stmt_child, setKeyValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_child);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(stmt_root);
    GmcFreeStmt(stmt_child);
}

// Con--Choice--case预先插入数据
void TestInsertConChoiceCaseRoot(GmcConnT *conn, uint32_t keyvalue)
{
    int ret;
    GmcBatchT *batch = NULL;
    GmcStmtT *stmt_root = NULL;

    ret = GmcAllocStmt(conn, &stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt_root, g_nameConChoiceCaseRoot, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    TestYangSetVertexProperty(stmt_root, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(stmt_root);
}
void TestInsertConChoiceCaseChild(GmcConnT *conn, uint32_t keyvalue, const char * choiceName, const char * caseName)
{
    int ret;
    uint32_t numPID = 0;
    GmcBatchT *batch = NULL;
    GmcStmtT *stmt_root = NULL;
    GmcStmtT *stmt_child_choice = NULL;
    GmcStmtT *stmt_child_case = NULL;

    ret = GmcAllocStmt(conn, &stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_child_choice);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_child_case);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt_root, g_nameConChoiceCaseRoot, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child choice节点
    ret = testGmcPrepareStmtByLabelName(stmt_child_choice, choiceName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmt_root, stmt_child_choice);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt_child_choice);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child case节点
    ret = testGmcPrepareStmtByLabelName(stmt_child_case, caseName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmt_child_choice, stmt_child_case);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    TestYangSetVertexProperty(stmt_child_case, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt_child_case);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(stmt_root);
    GmcFreeStmt(stmt_child_choice);
    GmcFreeStmt(stmt_child_case);
}

// Yang_Mix预先插入数据
void TestInsertMix(GmcConnT *conn, uint32_t keyvalue)
{
    int ret;
    GmcBatchT *batch = NULL;
    GmcStmtT *stmt[11] = {0};

    for (uint32_t i = 0; i < 11; i++) {
        ret = GmcAllocStmt(conn, &stmt[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 启动事务
    ret = TestTransStartAsync(conn, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt[0], "T0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    TestYangSetVertexProperty(stmt[0], keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点 T1
    ret = testGmcPrepareStmtByLabelName(stmt[1], "T1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmt[0], stmt[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    TestYangSetVertexProperty_PK(stmt[1], keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetVertexPropertyWithoutF0(stmt[1], keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点 T11
    ret = testGmcPrepareStmtByLabelName(stmt[2], "T11", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmt[1], stmt[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    TestYangSetVertexProperty(stmt[2], keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点 T2
    ret = testGmcPrepareStmtByLabelName(stmt[3], "T2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmt[0], stmt[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    TestYangSetVertexProperty(stmt[3], keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点 T21
    ret = testGmcPrepareStmtByLabelName(stmt[4], "T21", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmt[3], stmt[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点 T211
    ret = testGmcPrepareStmtByLabelName(stmt[5], "T211", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmt[4], stmt[5]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    TestYangSetVertexProperty(stmt[5], keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt[5]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点 T2111
    ret = testGmcPrepareStmtByLabelName(stmt[6], "T2111", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmt[5], stmt[6]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    TestYangSetVertexProperty_PK(stmt[6], keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetVertexPropertyWithoutF0(stmt[6], keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt[6]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点 T22
    ret = testGmcPrepareStmtByLabelName(stmt[9], "T22", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmt[3], stmt[9]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    TestYangSetVertexProperty(stmt[9], keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt[9]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点 T221
    ret = testGmcPrepareStmtByLabelName(stmt[10], "T221", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmt[9], stmt[10]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    TestYangSetVertexProperty(stmt[10], keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt[10]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(9, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(9, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 11; i++) {
        GmcFreeStmt(stmt[i]);
    }
}

// remove root
void TestRemoveRoot(GmcConnT *conn, const char * vertexName)
{
    int ret;
    GmcBatchT *batch = NULL;
    GmcStmtT *stmt_root = NULL;

    ret = GmcAllocStmt(conn, &stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt_root, vertexName, GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(stmt_root);
}

// Con--List--Con预先插入数据
void TestInsertCLCRoot(GmcConnT *conn, uint32_t keyvalue)
{
    int ret;
    GmcBatchT *batch = NULL;
    GmcStmtT *stmt_root = NULL;

    ret = GmcAllocStmt(conn, &stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt_root, "T0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    TestYangSetVertexProperty(stmt_root, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(stmt_root);
}
void TestInsertCLCChild(GmcConnT *conn)
{
    int ret;
    GmcBatchT *batch = NULL;
    GmcStmtT *stmt_root = NULL;
    GmcStmtT *stmt_child1 = NULL;
    GmcStmtT *stmt_child2 = NULL;

    ret = GmcAllocStmt(conn, &stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_child1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_child2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt_root, "T0", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点T1
    uint32_t setKeyValue = 0;
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_child1, "T1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt_root, stmt_child1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        setKeyValue = i;
        TestYangSetVertexProperty_PK(stmt_child1, setKeyValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetVertexPropertyWithoutF0(stmt_child1, setKeyValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_child1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置child节点 T2
        ret = testGmcPrepareStmtByLabelName(stmt_child2, "T2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt_child1, stmt_child2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        TestYangSetVertexProperty(stmt_child2, setKeyValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_child2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(21, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(21, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(stmt_root);
    GmcFreeStmt(stmt_child1);
    GmcFreeStmt(stmt_child2);
}

// Con--List--Choice--Case预先插入数据
void TestInsertCLCCRoot(GmcConnT *conn, uint32_t keyvalue)
{
    int ret;
    GmcBatchT *batch = NULL;
    GmcStmtT *stmt_root = NULL;

    ret = GmcAllocStmt(conn, &stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt_root, "T0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    TestYangSetVertexProperty(stmt_root, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(stmt_root);
}
void TestInsertCLCCChild(GmcConnT *conn)
{
    int ret;
    GmcBatchT *batch = NULL;
    GmcStmtT *stmt_root = NULL;
    GmcStmtT *stmt_child1 = NULL;
    GmcStmtT *stmt_child2 = NULL;
    GmcStmtT *stmt_child3 = NULL;
    AsyncUserDataT data = {0};

    ret = GmcAllocStmt(conn, &stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_child1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_child2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_child3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt_root, "T0", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点T1
    uint32_t setKeyValue = 0;
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_child1, "T1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt_root, stmt_child1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        setKeyValue = i;
        TestYangSetVertexProperty_PK(stmt_child1, setKeyValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetVertexPropertyWithoutF0(stmt_child1, setKeyValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_child1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置child节点 T2
        ret = testGmcPrepareStmtByLabelName(stmt_child2, "T2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt_child1, stmt_child2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_child2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置child节点 T3
        ret = testGmcPrepareStmtByLabelName(stmt_child3, "T3", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt_child2, stmt_child3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        TestYangSetVertexProperty(stmt_child3, setKeyValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_child3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(31, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(31, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(stmt_root);
    GmcFreeStmt(stmt_child1);
    GmcFreeStmt(stmt_child2);
    GmcFreeStmt(stmt_child3);
}

// Con--List--List预先插入数据
void TestInsertCLLRoot(GmcConnT *conn, uint32_t keyvalue)
{
    int ret;
    GmcBatchT *batch = NULL;
    GmcStmtT *stmt_root = NULL;

    ret = GmcAllocStmt(conn, &stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt_root, "T0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    TestYangSetVertexProperty(stmt_root, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(stmt_root);
}
void TestInsertCLLChild(GmcConnT *conn)
{
    int ret;
    GmcBatchT *batch = NULL;
    GmcStmtT *stmt_root = NULL;
    GmcStmtT *stmt_child1 = NULL;
    GmcStmtT *stmt_child2 = NULL;

    ret = GmcAllocStmt(conn, &stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_child1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_child2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt_root, "T0", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点T1
    uint32_t setKeyValue = 0;
    for (uint32_t i = 0; i < 5; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_child1, "T1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt_root, stmt_child1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        setKeyValue = i;
        TestYangSetVertexProperty_PK(stmt_child1, setKeyValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetVertexPropertyWithoutF0(stmt_child1, setKeyValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_child1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        for (uint32_t j = 0; j < 5; j++) {
            // 设置child节点 T2
            ret = testGmcPrepareStmtByLabelName(stmt_child2, "T2", GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, stmt_child1, stmt_child2);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // 设置属性值
            setKeyValue = j;
            TestYangSetVertexProperty_PK(stmt_child2, setKeyValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
            TestYangSetVertexPropertyWithoutF0(stmt_child2, setKeyValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

            // 添加DML操作
            ret = GmcBatchAddDML(batch, stmt_child2);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(31, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(31, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(stmt_root);
    GmcFreeStmt(stmt_child1);
    GmcFreeStmt(stmt_child2);
}

/******************************Subtree*******************************************/
bool testYangJsonIsEqualInner(const json_t *jsonA, const json_t *jsonB);
bool testYangJsonIsEqualReal(const json_t *valueA, const json_t *valueB)
{
    double doubleA = json_real_value(valueA);
    double doubleB = json_real_value(valueB);
    if (fabs(doubleA - doubleB) < 1e-6) {
        return true;
    }
    return false;
}
bool testYangJsonIsEqualField(const json_t *valueA, const json_t *valueB)
{
    if (json_typeof(valueA) == JSON_STRING) {
        return strcmp(json_string_value(valueA), json_string_value(valueB)) == 0;
    }
    if (json_typeof(valueA) == JSON_INTEGER) {
        return json_integer_value(valueA) == json_integer_value(valueB);
    }
    if (json_typeof(valueA) == JSON_REAL) {
        return testYangJsonIsEqualReal(valueA, valueB);
    }
    return true;
}
bool testYangJsonIsEqualArray(const json_t *valueA, const json_t *valueB)
{
    bool isEqual = true;
    uint32_t sizeA = (uint32_t)json_array_size(valueA);
    uint32_t sizeB = (uint32_t)json_array_size(valueB);
    if (sizeA != sizeB) {
        return false;
    }
    for (uint32_t i = 0; i < sizeA; ++i) {
        json_t *itemA = json_array_get(valueA, i);
        json_t *itemB = json_array_get(valueB, i);
        if (json_typeof(itemA) == JSON_OBJECT) {
            isEqual = testYangJsonIsEqualInner(itemA, itemB);
        } else {
            isEqual = testYangJsonIsEqualField(itemA, itemB);
        }
        if (!isEqual) {
            return false;
        }
    }
    return true;
}
bool testYangJsonIsEqualInner(const json_t *jsonA, const json_t *jsonB)
{
    bool isEqual = true;
    void *itA = json_object_iter((json_t *)jsonA);
    void *itB = json_object_iter((json_t *)jsonB);

    if (itA) {
        const char *keyA = json_object_iter_key(itA);

        while ((strcmp(keyA, "ID") == 0) || (strcmp(keyA, "PID") == 0)) {
            itA = json_object_iter_next((json_t *)jsonA, itA);
            if (itA == NULL) {
                return true;
            }
            keyA = json_object_iter_key(itA);  // 规避方案，subtree查出来的ID和PID不对用户体现，不参与结果比较
        }
    }
    if (itB) {
        const char *keyB = json_object_iter_key(itB);

        while ((strcmp(keyB, "ID") == 0) || (strcmp(keyB, "PID") == 0)) {
            itB = json_object_iter_next((json_t *)jsonA, itB);
            if (itB == NULL) {
                return true;
            }
            keyB = json_object_iter_key(itB);  // 规避方案，subtree查出来的ID和PID不对用户体现，不参与结果比较
        }
    }

    while (itA && itB) {
        const char *keyA = json_object_iter_key(itA);
        json_t *valueA = json_object_iter_value(itA);
        const char *keyB = json_object_iter_key(itB);
        json_t *valueB = json_object_iter_value(itB);

        if ((json_typeof(valueA) != json_typeof(valueB)) || (strcmp(keyA, keyB) != 0)) {
            return false;
        }
        if (json_typeof(valueA) == JSON_OBJECT) {
            isEqual = testYangJsonIsEqualInner(valueA, valueB);
        } else if (json_typeof(valueA) == JSON_ARRAY) {
            isEqual = testYangJsonIsEqualArray(valueA, valueB);
        } else {
            isEqual = testYangJsonIsEqualField(valueA, valueB);
        }
        if (!isEqual) {
            return false;
        }
        itA = json_object_iter_next((json_t *)jsonA, itA);
        itB = json_object_iter_next((json_t *)jsonB, itB);
    }

    return itA == itB;
}

bool testYangJsonIsEqual(const char *json1, const char *json2)
{
    json_error_t jsonError;
    json_t *jsonA = json_loads(json1, JSON_REJECT_DUPLICATES, &jsonError);
    json_t *jsonB = json_loads(json2, JSON_REJECT_DUPLICATES, &jsonError);
    bool isEqual = testYangJsonIsEqualInner(jsonA, jsonB);
    json_decref(jsonA);
    json_decref(jsonB);
    return isEqual;
}

// userData结构
struct subtreeFilterCbParam {
    int step;
    int32_t expectStatus;          // 预期的操作状态
    const char *expectReplyJson;  // 预期返回的subtree查询结果, json字符串
};
// userData ：用户数据 replyJson ：服务端返回的子树 json status ：服务器端操作处理结果  errMsg ：错误信息
void AsyncSubtreeFilterCb(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    int ret;
    subtreeFilterCbParam *param = (subtreeFilterCbParam *)(userData);
    param->expectStatus = status;
    if (GMERR_OK != status) {
        AW_FUN_Log(LOG_ERROR, "[err] status is %d  errMsg  is %s   \n ", status, errMsg);
        return;
    }
    bool isEnd = false;
    bool isEqual = false;
    uint32_t count = 0;
    const char **jsonReply = NULL;
    ret = GmcYangFetchJsonRetDeparse(fetchRet, &isEnd, &jsonReply, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(true, isEnd);
    AW_MACRO_EXPECT_EQ_INT(1, count);
    AW_MACRO_EXPECT_NOTNULL(jsonReply);
    printf("%s\n", jsonReply[0]);

    if (param->expectReplyJson != NULL) {
        isEqual = testYangJsonIsEqual((const char*)jsonReply[0], param->expectReplyJson);
        AW_MACRO_EXPECT_EQ_INT(true, isEqual);
    } else {
        AW_FUN_Log(LOG_ERROR, "[err] no replyjson   \n ");
    }
    param->step++;
}

int testWaitAsyncSubtreeRecv(void *userData, int expRecvNum = 1, int timeout = -1, bool isAutoReset = true)
{
    int waitCnt = 0;
    struct timeval start;
    struct timeval end;
    unsigned long duration;

    gettimeofday(&start, NULL);
    subtreeFilterCbParam *userdata1 = (subtreeFilterCbParam *)userData;
    while (userdata1->step != expRecvNum) {
        usleep(10);
        waitCnt++;
        if (timeout > 0 && waitCnt >= timeout) {
            gettimeofday(&end, NULL);
            duration = 1000000 * (end.tv_sec - start.tv_sec) + end.tv_usec - start.tv_usec;
            printf("[INFO] Recv Timeout %lf ", (double)duration / 1000000);
            return -1;  // 接收超时
        }
    }
    return 0;
}

void TestSubtreeFilter(GmcStmtT *stmt, const char * rootName, const char * jsonName)
{
    int ret;

    // subtree 查询
    char replyPath[1024] = {0};
    ret = snprintf(replyPath, 1024, "SubtreeReplyJson/%s_Reply.json", jsonName);
    AW_MACRO_EXPECT_NE_INT(0, ret);
    char *replyJson = NULL;
    readJanssonFile(replyPath, &replyJson);
    ASSERT_NE((void *)NULL, replyJson);

    subtreeFilterCbParam data = {0};
    data.expectReplyJson = replyJson;
    data.expectStatus = GMERR_OK;
    data.step = 0;

    char filterPath[1024] = {0};
    ret = snprintf(filterPath, 1024, "SubTreeFilterJson/Yang_002_Func.json");
    AW_MACRO_EXPECT_NE_INT(0, ret);
    char *filterJson = NULL;
    readJanssonFile(filterPath, &filterJson);
    ASSERT_NE((void *)NULL, filterJson);

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = rootName;
    filter.subtree.json = filterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(stmt, &filters, NULL, AsyncSubtreeFilterCb, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.expectStatus);

    free(filterJson);
    free(replyJson);
    filterJson = NULL;
    replyJson = NULL;
}

#endif /* YANG_DML_OPERATION_TEST_H */
