alias_ContainerOne:update[(pri<PERSON><PERSON>(ID:1)),(pri<PERSON><PERSON>(ID:1))]
alias_ContainerOne.F0:create(100)
alias_ContainerOne.F22:create(NIL:23)
alias_ContainerOne.alias_ListOne:create[(pri<PERSON>ey(PID:1,F0:100)),(NULL)]
alias_ListOne.ID:create(1)
alias_ListOne.F1:create(100)
alias_ListOne.F4:create(NIL:8)
alias_ListOne.F6:create(aadadd)
alias_ListOne.ContainerTwo:create
ContainerTwo.F0:create(100)
ContainerTwo.F4:create(100)
ContainerTwo.F7:create(777)
ContainerTwo.F8:create(888)
ContainerTwo.F9:create(999)
ContainerTwo.F10:create(string)
ContainerTwo.F11:create(string)
ContainerTwo.F12:create(default12)
ContainerTwo.F13:create(default13)
alias_ListOne.ContainerThree:create
ContainerThree.F1:create(100)
ContainerThree.F3:create(100)
ContainerThree.F5:create(100)
ContainerThree.F8:create(888)
ContainerThree.F9:create(999)
ContainerThree.F10:create(string)
ContainerThree.F11:create(string)
ContainerThree.F12:create(default12)
ContainerThree.F13:create(default13)
alias_ListOne.alias_ListTwo:create[(priKey(PID:1,F0:100)),(NULL)]
alias_ListTwo.ID:create(1)
