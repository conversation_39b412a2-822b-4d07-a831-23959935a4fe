alias_ContainerOne:update[(pri<PERSON><PERSON>(ID:1)),(pri<PERSON><PERSON>(ID:1))]
alias_ContainerOne.alias_ListOne:update[(pri<PERSON><PERSON>(PID:1,F0:str000)),(pri<PERSON>ey(PID:1,F0:str000))]
alias_ListOne.F1:update(100,300)
alias_ListOne.F2:update(NIL:8,NIL:8)
alias_ListOne.ContainerTwo:update
ContainerTwo.F5:remove(100)
ContainerTwo.F9:remove(999)
ContainerTwo.F10:remove(string)
ContainerTwo.F11:remove(string)
alias_ListOne.ContainerThree:update
ContainerThree.F1:remove(100)
ContainerThree.F5:remove(100)
ContainerThree.F9:remove(999)
ContainerThree.F10:remove(string)
