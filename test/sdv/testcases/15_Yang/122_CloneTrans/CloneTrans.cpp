/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: 122_CloneTrans
 * Author: hanyang
 * Create: 2025-07-02
 */
#include "CloneTrans.h"

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
GmcConnT *g_conn_async1[11] = {0};
GmcStmtT *g_stmt_async1[11] = {0};
GmcStmtT *g_stmt_root1[11] = {0};
GmcStmtT *g_stmt_list1[11] = {0};
GmcNodeT *g_rootNode1[11] = {0};
GmcNodeT *g_childNode1[11] = {0};

GmcConnT *g_conn_async2[11] = {0};
GmcStmtT *g_stmt_async2[11] = {0};
GmcStmtT *g_stmt_root2[11] = {0};
GmcStmtT *g_stmt_list2[11] = {0};
GmcNodeT *g_rootNode2[11] = {0};
GmcNodeT *g_childNode2[11] = {0};

GmcConnT *g_conn_async;
GmcStmtT *g_stmt_async;

class CloneTrans_test : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void CloneTrans_test::SetUpTestCase()
{
    int ret;
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"enableTrxClone=1\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"scheduleMode=2\"");
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void CloneTrans_test::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
}

void CloneTrans_test::SetUp()
{
    int ret;
    AsyncUserDataT data = {0};
    uint32_t i = 0;

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    YangConnOptionT connOptions = {0};
    connOptions.isLobConn = true;
    for (i = 0; i < 11; i++) {
        if (i == 0) {
            // 0号连接用于操作大数据
            ret = TestYangGmcConnect(&g_conn_async1[i], &g_stmt_async1[i], GMC_CONN_TYPE_ASYNC, &connOptions);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            // 1~10号连接用于普通克隆
            ret = TestYangGmcConnect(&g_conn_async1[i], &g_stmt_async1[i], GMC_CONN_TYPE_ASYNC);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcAllocStmt(g_conn_async1[i], &g_stmt_root1[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcAllocStmt(g_conn_async1[i], &g_stmt_list1[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    for (i = 0; i < 11; i++) {
        if (i == 0) {
            // 0号连接用于操作大数据
            ret = TestYangGmcConnect(&g_conn_async2[i], &g_stmt_async2[i], GMC_CONN_TYPE_ASYNC, &connOptions);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            // 1~10号连接用于普通克隆
            ret = TestYangGmcConnect(&g_conn_async2[i], &g_stmt_async2[i], GMC_CONN_TYPE_ASYNC);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcAllocStmt(g_conn_async2[i], &g_stmt_root2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcAllocStmt(g_conn_async2[i], &g_stmt_list2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = TestYangGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步创建namespace
    GmcDropNamespace(g_stmt, NAMESPACE122);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = NAMESPACE122;
    nspCfg.tablespaceName = NULL;
    nspCfg.userName = NAMESPACE_USER_NAME;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观

    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async1[1], &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // Yang事务配置为乐观+可重复读，需要和namespace保持一致
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    for (i = 0; i < 11; i++) {
        ret = GmcUseNamespaceAsync(g_stmt_async1[i], NAMESPACE122, use_namespace_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        memset(&data, 0, sizeof(AsyncUserDataT));

        ret = GmcUseNamespaceAsync(g_stmt_async2[i], NAMESPACE122, use_namespace_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        memset(&data, 0, sizeof(AsyncUserDataT));
    }

    // 创建表
    TestCreateLabel(g_stmt_async1[1]);
    g_printFlag = true;

    AW_CHECK_LOG_BEGIN();
}

void CloneTrans_test::TearDown()
{
    int ret;
    uint32_t i = 0;
    AsyncUserDataT data = {0};
    AW_CHECK_LOG_END();

    // 删除表
    TestDropLabel(g_stmt_async1[1]);

    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_async1[1], NAMESPACE122, drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    for (i = 0; i < 11; i++) {
        GmcFreeStmt(g_stmt_root1[i]);
        GmcFreeStmt(g_stmt_list1[i]);
        ret = testGmcDisconnect(g_conn_async1[i], g_stmt_async1[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_conn_async1[i] = NULL;
        g_stmt_async1[i] = NULL;
        g_stmt_root1[i] = NULL;
        g_stmt_list1[i] = NULL;
        g_rootNode1[i] = NULL;
        g_childNode1[i] = NULL;

        GmcFreeStmt(g_stmt_root2[i]);
        GmcFreeStmt(g_stmt_list2[i]);
        ret = testGmcDisconnect(g_conn_async2[i], g_stmt_async2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_conn_async2[i] = NULL;
        g_stmt_async2[i] = NULL;
        g_stmt_root2[i] = NULL;
        g_stmt_list2[i] = NULL;
        g_rootNode2[i] = NULL;
        g_childNode2[i] = NULL;
    }

    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 002.在线修改enableTrxClone配置项，报错
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 在线修改配置项
    char cmd[MAX_CMD_SIZE];
    (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmadmin -cfgName enableTrxClone -cfgVal 8", g_toolPath);
    AW_FUN_Log(LOG_INFO, "%s\n", cmd);

    // 校验结果
    ret = executeCommand(cmd, "config change mode: not allowed", "[ERROR] [ADMIN] set config");
    if (ret != GMERR_OK) {
        // 报错并重新执行命令
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        system(cmd);
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

/*****************************************************************************
 Description  : 003.GmcTransCreateStartExtOption接口空指针
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    GmcBatchT *batch = NULL;

    GmcStartExtOptionT *option = NULL;
    ret = GmcTransCreateStartExtOption(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    AddWhiteList(GMERR_NULL_VALUE_NOT_ALLOWED);
}

/*****************************************************************************
 Description  : 004.GmcTransStartExtOptionSetTrxCfg接口空指针
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    GmcBatchT *batch = NULL;

    GmcStartExtOptionT *option = NULL;
    ret = GmcTransCreateStartExtOption(&option);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStartExtOptionSetTrxCfg(NULL, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    GmcTransStartExtOptionDestroy(option);
    AddWhiteList(GMERR_NULL_VALUE_NOT_ALLOWED);
}

/*****************************************************************************
 Description  : 005.GmcTransStartExtOptionSetCloneId接口空指针
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    GmcBatchT *batch = NULL;
    uint32_t cloneId = 0;

    GmcStartExtOptionT *option = NULL;
    ret = GmcTransCreateStartExtOption(&option);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStartExtOptionSetCloneId(NULL, cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    GmcTransStartExtOptionDestroy(option);
    AddWhiteList(GMERR_NULL_VALUE_NOT_ALLOWED);
}

/*****************************************************************************
 Description  : 006.GmcTransStartExtAsync接口空指针
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    AsyncUserDataT data = {0};

    GmcStartExtOptionT *option = NULL;
    ret = GmcTransCreateStartExtOption(&option);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStartExtOptionSetTrxCfg(option, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStartExtAsync(NULL, option, trans_start_ext_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    ret = GmcTransStartExtAsync(g_conn_async1[1], NULL, trans_start_ext_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    GmcTransStartExtOptionDestroy(option);
    AddWhiteList(GMERR_NULL_VALUE_NOT_ALLOWED);
}

/*****************************************************************************
 Description  : 007.GmcTransStartExtResGetCloneId接口空指针
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    AsyncUserDataT data = {0};
    uint32_t *cloneId = 0;

    GmcStartExtOptionT *option = NULL;
    ret = GmcTransCreateStartExtOption(&option);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStartExtOptionSetTrxCfg(option, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 回调函数中获取cloneId，并检测空指针
    ret = GmcTransStartExtAsync(g_conn_async1[1], option, trans_start_ext_callback_fail, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 提交被克隆事务
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcTransStartExtOptionDestroy(option);
    AddWhiteList(GMERR_NULL_VALUE_NOT_ALLOWED);
}

/*****************************************************************************
 Description  : 008.GmcTransStartExtOptionDestroy接口空指针
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    AsyncUserDataT data = {0};
    uint32_t *cloneId = 0;

    GmcStartExtOptionT *option = NULL;
    ret = GmcTransCreateStartExtOption(&option);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 无返回值，空指针不会core即可
    GmcTransStartExtOptionDestroy(NULL);
    GmcTransStartExtOptionDestroy(option);
}

/*****************************************************************************
 Description  : 009.连接1开启被克隆事务，DML操作，连接2开启克隆事务，DML操作，
                连接2提交事务，连接1提交事务，subtree查询，结果为连接1和连接2操作提交的数据合并
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId = 0;

    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[1], "root_1", fieldValue);

    // 启动被克隆事务
    ret =TestTransStartExtAsync(g_conn_async1[1], g_mSTrxConfig, &cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneId);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async1[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list1[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root1[1], g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list1[1], &g_childNode1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_009_01");
    TestSubtreeFilterObjAll(g_stmt_async1[1], "root_1", "Yang_122_Func_009_01");

    // 开启克隆事务
    ret = TestTransStartExtCloneAsync(g_conn_async2[1], cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async2[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root2[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list2[1], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root2[1], g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list2[1], &g_childNode2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_009_02");
    TestSubtreeFilterObjAll(g_stmt_async2[1], "root_1", "Yang_122_Func_009_02");

    // 提交克隆事务
    ret = TestTransCommitAsync(g_conn_async2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交被克隆事务
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    ret = TestTransStartAsync(g_conn_async1[1], g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_009_02");
    TestSubtreeFilterObjAll(g_stmt_async1[1], "root_1", "Yang_122_Func_009_02");
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 010.连接1开启被克隆事务，DML操作，连接2开启克隆事务，DML操作，
                连接2提交事务，连接1回滚事务，subtree查询，结果为连接1修改前的数据
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId = 0;

    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[1], "root_1", fieldValue);

    // 启动被克隆事务
    ret =TestTransStartExtAsync(g_conn_async1[1], g_mSTrxConfig, &cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneId);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async1[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list1[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root1[1], g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list1[1], &g_childNode1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_010_01");
    TestSubtreeFilterObjAll(g_stmt_async1[1], "root_1", "Yang_122_Func_010_01");

    // 开启克隆事务
    ret = TestTransStartExtCloneAsync(g_conn_async2[1], cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async2[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root2[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list2[1], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root2[1], g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list2[1], &g_childNode2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_010_02");
    TestSubtreeFilterObjAll(g_stmt_async2[1], "root_1", "Yang_122_Func_010_02");

    // 提交克隆事务
    ret = TestTransCommitAsync(g_conn_async2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 回滚被克隆事务
    ret = TestTransRollBackAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    ret = TestTransStartAsync(g_conn_async1[1], g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_010_03");
    TestSubtreeFilterObjAll(g_stmt_async1[1], "root_1", "Yang_122_Func_010_03");
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 011.连接1开启被克隆事务，DML操作，连接2开启克隆事务，DML操作，
                连接2回滚事务，连接1提交事务，subtree查询，结果只有连接1操作的数据
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId = 0;

    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[1], "root_1", fieldValue);

    // 启动被克隆事务
    ret =TestTransStartExtAsync(g_conn_async1[1], g_mSTrxConfig, &cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneId);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async1[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list1[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root1[1], g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list1[1], &g_childNode1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_011_01");
    TestSubtreeFilterObjAll(g_stmt_async1[1], "root_1", "Yang_122_Func_011_01");

    // 开启克隆事务
    ret = TestTransStartExtCloneAsync(g_conn_async2[1], cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async2[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root2[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list2[1], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root2[1], g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list2[1], &g_childNode2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_011_02");
    TestSubtreeFilterObjAll(g_stmt_async2[1], "root_1", "Yang_122_Func_011_02");
    // 回滚克隆事务
    ret = TestTransRollBackAsync(g_conn_async2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_011_01");
    TestSubtreeFilterObjAll(g_stmt_async1[1], "root_1", "Yang_122_Func_011_01");

    // 连接2普通事务subtree查询
    ret = TestTransStartAsync(g_conn_async2[1], g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_011_03");
    TestSubtreeFilterObjAll(g_stmt_async2[1], "root_1", "Yang_122_Func_011_03");
    ret = TestTransCommitAsync(g_conn_async2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接2克隆事务subtree查询
    ret = TestTransStartExtCloneAsync(g_conn_async2[1], cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_011_01");
    TestSubtreeFilterObjAll(g_stmt_async2[1], "root_1", "Yang_122_Func_011_01");
    ret = TestTransCommitAsync(g_conn_async2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交被克隆事务
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    ret = TestTransStartAsync(g_conn_async1[1], g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_011_01");
    TestSubtreeFilterObjAll(g_stmt_async1[1], "root_1", "Yang_122_Func_011_01");
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 012.连接1开启被克隆事务，DML操作，连接2开启克隆事务，DML操作，
                连接2回滚事务，连接1回滚事务，subtree查询，结果为连接1修改前的数据
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId = 0;

    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[1], "root_1", fieldValue);

    // 启动被克隆事务
    ret =TestTransStartExtAsync(g_conn_async1[1], g_mSTrxConfig, &cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneId);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async1[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list1[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root1[1], g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list1[1], &g_childNode1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_012_01");
    TestSubtreeFilterObjAll(g_stmt_async1[1], "root_1", "Yang_122_Func_012_01");

    // 开启克隆事务
    ret = TestTransStartExtCloneAsync(g_conn_async2[1], cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async2[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root2[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list2[1], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root2[1], g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list2[1], &g_childNode2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_012_02");
    TestSubtreeFilterObjAll(g_stmt_async2[1], "root_1", "Yang_122_Func_012_02");

    // 回滚克隆事务
    ret = TestTransRollBackAsync(g_conn_async2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_012_01");
    TestSubtreeFilterObjAll(g_stmt_async1[1], "root_1", "Yang_122_Func_012_01");

    // 连接2普通事务subtree查询
    ret = TestTransStartAsync(g_conn_async2[1], g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_012_03");
    TestSubtreeFilterObjAll(g_stmt_async2[1], "root_1", "Yang_122_Func_012_03");
    ret = TestTransCommitAsync(g_conn_async2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接2克隆事务subtree查询
    ret = TestTransStartExtCloneAsync(g_conn_async2[1], cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_012_01");
    TestSubtreeFilterObjAll(g_stmt_async2[1], "root_1", "Yang_122_Func_012_01");
    ret = TestTransCommitAsync(g_conn_async2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 回滚被克隆事务
    ret = TestTransRollBackAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    ret = TestTransStartAsync(g_conn_async1[1], g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_012_03");
    TestSubtreeFilterObjAll(g_stmt_async1[1], "root_1", "Yang_122_Func_012_03");
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 013.连接1开启被克隆事务，Insert数据，连接2开启克隆事务，Insert数据，和连接1插入的数据主键冲突，
                连接2回滚事务，连接1提交事务，subtree查询，结果为连接1操作的数据
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId = 0;

    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[1], "root_1", fieldValue);

    // 启动被克隆事务
    ret =TestTransStartExtAsync(g_conn_async1[1], g_mSTrxConfig, &cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneId);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async1[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list1[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root1[1], g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list1[1], &g_childNode1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 开启克隆事务
    ret = TestTransStartExtCloneAsync(g_conn_async2[1], cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async2[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root2[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list2[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root2[1], g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list2[1], &g_childNode2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, data.status);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    AddWhiteList(GMERR_PRIMARY_KEY_VIOLATION);
    AddWhiteList(GMERR_UNIQUE_VIOLATION);

    // 回滚克隆事务
    ret = TestTransRollBackAsync(g_conn_async2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交被克隆事务
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    ret = TestTransStartAsync(g_conn_async1[1], g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_013_01");
    TestSubtreeFilterObjAll(g_stmt_async1[1], "root_1", "Yang_122_Func_013_01");
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 014.连接1开启被克隆事务，Insert数据，连接2开启克隆事务，Merge连接1Insert的数据，
                连接2提交事务，连接1提交事务，subtree查询，结果为连接1和连接2操作提交的数据合并
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t keyValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId = 0;

    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[1], "root_1", fieldValue);

    // 启动被克隆事务
    ret =TestTransStartExtAsync(g_conn_async1[1], g_mSTrxConfig, &cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneId);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async1[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list1[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root1[1], g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list1[1], &g_childNode1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 开启克隆事务
    ret = TestTransStartExtCloneAsync(g_conn_async2[1], cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async2[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root2[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list2[1], "list_1", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root2[1], g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list2[1], &g_childNode2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    keyValue = 100;
    TestSetKeyNameAndValue(g_stmt_list2[1], fieldValue);
    fieldValue = 200;
    TestYangSetNodeProperty(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交克隆事务
    ret = TestTransCommitAsync(g_conn_async2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交被克隆事务
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    ret = TestTransStartAsync(g_conn_async1[1], g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_014_01");
    TestSubtreeFilterObjAll(g_stmt_async1[1], "root_1", "Yang_122_Func_014_01");
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 015.连接1开启被克隆事务，Insert数据，连接2开启克隆事务，Replace连接1Insert的数据，
                连接2提交事务，连接1提交事务，subtree查询，结果为连接1和连接2操作提交的数据合并
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId = 0;

    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[1], "root_1", fieldValue);

    // 启动被克隆事务
    ret =TestTransStartExtAsync(g_conn_async1[1], g_mSTrxConfig, &cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneId);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async1[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list1[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root1[1], g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list1[1], &g_childNode1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 开启克隆事务
    ret = TestTransStartExtCloneAsync(g_conn_async2[1], cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async2[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root2[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list2[1], "list_1", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root2[1], g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list2[1], &g_childNode2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    fieldValue = 200;
    TestYangSetNodeProperty(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交克隆事务
    ret = TestTransCommitAsync(g_conn_async2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交被克隆事务
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    ret = TestTransStartAsync(g_conn_async1[1], g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_015_01");
    TestSubtreeFilterObjAll(g_stmt_async1[1], "root_1", "Yang_122_Func_015_01");
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 016.连接1开启被克隆事务，Insert数据，连接2开启克隆事务，Delete连接1Insert的数据，
                连接2提交事务，连接1提交事务，subtree查询，结果为连接1和连接2操作提交的数据合并
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t keyValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId = 0;

    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[1], "root_1", fieldValue);

    // 启动被克隆事务
    ret =TestTransStartExtAsync(g_conn_async1[1], g_mSTrxConfig, &cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneId);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async1[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list1[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root1[1], g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list1[1], &g_childNode1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 开启克隆事务
    ret = TestTransStartExtCloneAsync(g_conn_async2[1], cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async2[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root2[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list2[1], "list_1", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root2[1], g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list2[1], &g_childNode2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    keyValue = 100;
    TestSetKeyNameAndValue(g_stmt_list2[1], fieldValue);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交克隆事务
    ret = TestTransCommitAsync(g_conn_async2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交被克隆事务
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    ret = TestTransStartAsync(g_conn_async1[1], g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_016_01");
    TestSubtreeFilterObjAll(g_stmt_async1[1], "root_1", "Yang_122_Func_016_01");
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 017.连接1开启被克隆事务，Insert数据，连接2开启克隆事务，Remove连接1Insert的数据，
                连接2提交事务，连接1提交事务，subtree查询，结果为连接1和连接2操作提交的数据合并
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t keyValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId = 0;

    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[1], "root_1", fieldValue);

    // 启动被克隆事务
    ret =TestTransStartExtAsync(g_conn_async1[1], g_mSTrxConfig, &cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneId);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async1[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list1[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root1[1], g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list1[1], &g_childNode1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 开启克隆事务
    ret = TestTransStartExtCloneAsync(g_conn_async2[1], cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async2[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root2[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list2[1], "list_1", GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root2[1], g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list2[1], &g_childNode2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    keyValue = 100;
    TestSetKeyNameAndValue(g_stmt_list2[1], fieldValue);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交克隆事务
    ret = TestTransCommitAsync(g_conn_async2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交被克隆事务
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    ret = TestTransStartAsync(g_conn_async1[1], g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_017_01");
    TestSubtreeFilterObjAll(g_stmt_async1[1], "root_1", "Yang_122_Func_017_01");
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 018.连接1开启被克隆事务，DML操作，连接1和2 subtree查询，
                连接2开启克隆事务，连接1和2 subtree查询，DML操作，连接1和2 subtree查询，
                连接2提交事务，连接1和2 subtree查询，连接1提交事务，连接1和2 subtree查询
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId = 0;

    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[1], "root_1", fieldValue);

    // 启动被克隆事务
    ret =TestTransStartExtAsync(g_conn_async1[1], g_mSTrxConfig, &cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneId);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async1[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list1[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root1[1], g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list1[1], &g_childNode1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_018_01");
    TestSubtreeFilterObjAll(g_stmt_async1[1], "root_1", "Yang_122_Func_018_01");

    // 启动事务
    ret = TestTransStartAsync(g_conn_async2[1], g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree查询
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_018_02");
    TestSubtreeFilterObjAll(g_stmt_async2[1], "root_1", "Yang_122_Func_018_02");
    // 提交事务
    ret = TestTransCommitAsync(g_conn_async2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启克隆事务
    ret = TestTransStartExtCloneAsync(g_conn_async2[1], cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_018_01");
    TestSubtreeFilterObjAll(g_stmt_async1[1], "root_1", "Yang_122_Func_018_01");
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_018_01");
    TestSubtreeFilterObjAll(g_stmt_async2[1], "root_1", "Yang_122_Func_018_01");

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async2[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root2[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list2[1], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root2[1], g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list2[1], &g_childNode2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_018_01");
    TestSubtreeFilterObjAll(g_stmt_async1[1], "root_1", "Yang_122_Func_018_01");
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_018_03");
    TestSubtreeFilterObjAll(g_stmt_async2[1], "root_1", "Yang_122_Func_018_03");

    // 提交克隆事务
    ret = TestTransCommitAsync(g_conn_async2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_018_03");
    TestSubtreeFilterObjAll(g_stmt_async1[1], "root_1", "Yang_122_Func_018_03");

    // 连接2普通事务subtree查询
    ret = TestTransStartAsync(g_conn_async2[1], g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_018_02");
    TestSubtreeFilterObjAll(g_stmt_async2[1], "root_1", "Yang_122_Func_018_02");
    ret = TestTransCommitAsync(g_conn_async2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接2克隆事务subtree查询
    ret = TestTransStartExtCloneAsync(g_conn_async2[1], cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_018_03");
    TestSubtreeFilterObjAll(g_stmt_async2[1], "root_1", "Yang_122_Func_018_03");
    ret = TestTransCommitAsync(g_conn_async2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交被克隆事务
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    ret = TestTransStartAsync(g_conn_async1[1], g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_018_03");
    TestSubtreeFilterObjAll(g_stmt_async1[1], "root_1", "Yang_122_Func_018_03");
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    ret = TestTransStartAsync(g_conn_async2[1], g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_018_03");
    TestSubtreeFilterObjAll(g_stmt_async2[1], "root_1", "Yang_122_Func_018_03");
    ret = TestTransCommitAsync(g_conn_async2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 019.连接1开启被克隆事务，Insert大对象操作，连接2开启克隆事务，Insert大对象操作，
                连接2提交事务，连接1提交事务，subtree查询，结果为连接1和连接2操作提交的数据合并
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId = 0;

    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[0], "root_1", fieldValue);
    TestInsertListBig1(g_conn_async1[0]);

    // 启动被克隆事务
    ret =TestTransStartExtAsync(g_conn_async1[0], g_mSTrxConfig, &cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneId);

    // 设置批处理batch参数
    ret = TestBatchPrepareBig(g_conn_async1[0], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[0], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_3
    ret = testGmcPrepareStmtByLabelName(g_stmt_list1[0], "list_3", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root1[0], g_stmt_list1[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list1[0], &g_childNode1[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode1[0], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode1[0], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodePropertyNum(g_childNode1[0], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, 150);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list1[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 开启克隆事务
    ret = TestTransStartExtCloneAsync(g_conn_async2[0], cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepareBig(g_conn_async2[0], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root2[0], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root2[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root2[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_4
    ret = testGmcPrepareStmtByLabelName(g_stmt_list2[0], "list_4", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root2[0], g_stmt_list2[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list2[0], &g_childNode2[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode2[0], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode2[0], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodePropertyNum(g_childNode2[0], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, 150);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list2[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交克隆事务
    ret = TestTransCommitAsync(g_conn_async2[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交被克隆事务
    ret = TestTransCommitAsync(g_conn_async1[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    ret = TestTransStartAsync(g_conn_async1[0], g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_019");
    TestSubtreeFilterObjAllMulti(g_stmt_async1[0], "root_1", "Yang_122_Func_019", 2);
    ret = TestTransCommitAsync(g_conn_async1[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 020.连接1开启被克隆事务，Merge大对象操作，连接2开启克隆事务，Merge大对象操作，
                连接2提交事务，连接1提交事务，subtree查询，结果为连接1和连接2操作提交的数据合并
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t keyValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId = 0;

    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[0], "root_1", fieldValue);
    TestInsertListBig(g_conn_async1[0]);

    // 启动被克隆事务
    ret =TestTransStartExtAsync(g_conn_async1[0], g_mSTrxConfig, &cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneId);

    // 设置批处理batch参数
    ret = TestBatchPrepareBig(g_conn_async1[0], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[0], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_3
    ret = testGmcPrepareStmtByLabelName(g_stmt_list1[0], "list_3", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root1[0], g_stmt_list1[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list1[0], &g_childNode1[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    keyValue = 100;
    TestSetKeyNameAndValue(g_stmt_list1[0], keyValue);
    TestYangSetNodePropertyNum(g_childNode1[0], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, 150);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list1[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 开启克隆事务
    ret = TestTransStartExtCloneAsync(g_conn_async2[0], cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepareBig(g_conn_async2[0], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root2[0], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root2[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root2[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_4
    ret = testGmcPrepareStmtByLabelName(g_stmt_list2[0], "list_4", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root2[0], g_stmt_list2[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list2[0], &g_childNode2[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    keyValue = 100;
    TestSetKeyNameAndValue(g_stmt_list2[0], keyValue);
    TestYangSetNodePropertyNum(g_childNode2[0], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, 150);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list2[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交克隆事务
    ret = TestTransCommitAsync(g_conn_async2[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交被克隆事务
    ret = TestTransCommitAsync(g_conn_async1[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    ret = TestTransStartAsync(g_conn_async1[0], g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_020");
    TestSubtreeFilterObjAllMulti(g_stmt_async1[0], "root_1", "Yang_122_Func_020", 1);
    ret = TestTransCommitAsync(g_conn_async1[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 021.连接1开启被克隆事务，Delete大对象操作，连接2开启克隆事务，Delete大对象操作，
                连接2提交事务，连接1提交事务，subtree查询，结果为连接1和连接2操作提交的数据合并
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t keyValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId = 0;

    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[0], "root_1", fieldValue);
    TestInsertListBig(g_conn_async1[0]);

    // 启动被克隆事务
    ret =TestTransStartExtAsync(g_conn_async1[0], g_mSTrxConfig, &cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneId);

    // 设置批处理batch参数
    ret = TestBatchPrepareBig(g_conn_async1[0], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[0], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_3
    ret = testGmcPrepareStmtByLabelName(g_stmt_list1[0], "list_3", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root1[0], g_stmt_list1[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list1[0], &g_childNode1[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    keyValue = 100;
    TestSetKeyNameAndValue(g_stmt_list1[0], keyValue);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list1[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 开启克隆事务
    ret = TestTransStartExtCloneAsync(g_conn_async2[0], cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepareBig(g_conn_async2[0], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root2[0], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root2[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root2[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_4
    ret = testGmcPrepareStmtByLabelName(g_stmt_list2[0], "list_4", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root2[0], g_stmt_list2[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list2[0], &g_childNode2[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    keyValue = 100;
    TestSetKeyNameAndValue(g_stmt_list2[0], keyValue);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list2[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交克隆事务
    ret = TestTransCommitAsync(g_conn_async2[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交被克隆事务
    ret = TestTransCommitAsync(g_conn_async1[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    ret = TestTransStartAsync(g_conn_async1[0], g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_021_01");
    TestSubtreeFilterObjAll(g_stmt_async1[0], "root_1", "Yang_122_Func_021_01");
    ret = TestTransCommitAsync(g_conn_async1[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 022.连接1开启被克隆事务，Insert大对象操作，连接2开启克隆事务，Insert大对象操作，
                连接2提交事务，连接1回滚事务，subtree查询，结果为连接1修改前数据
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId = 0;

    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[0], "root_1", fieldValue);

    // 启动被克隆事务
    ret =TestTransStartExtAsync(g_conn_async1[0], g_mSTrxConfig, &cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneId);

    // 设置批处理batch参数
    ret = TestBatchPrepareBig(g_conn_async1[0], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[0], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_3
    ret = testGmcPrepareStmtByLabelName(g_stmt_list1[0], "list_3", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root1[0], g_stmt_list1[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list1[0], &g_childNode1[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode1[0], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode1[0], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodePropertyNum(g_childNode1[0], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, 150);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list1[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 开启克隆事务
    ret = TestTransStartExtCloneAsync(g_conn_async2[0], cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepareBig(g_conn_async2[0], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root2[0], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root2[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root2[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_4
    ret = testGmcPrepareStmtByLabelName(g_stmt_list2[0], "list_4", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root2[0], g_stmt_list2[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list2[0], &g_childNode2[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode2[0], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode2[0], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodePropertyNum(g_childNode2[0], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, 150);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list2[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交克隆事务
    ret = TestTransCommitAsync(g_conn_async2[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 回滚被克隆事务
    ret = TestTransRollBackAsync(g_conn_async1[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    ret = TestTransStartAsync(g_conn_async1[0], g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_022_01");
    TestSubtreeFilterObjAll(g_stmt_async1[0], "root_1", "Yang_122_Func_022_01");
    ret = TestTransCommitAsync(g_conn_async1[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 023.连接1开启被克隆事务，Merge大对象操作，连接2开启克隆事务，Merge大对象操作，
                连接2提交事务，连接1回滚事务，subtree查询，结果为连接1修改前数据
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t keyValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId = 0;

    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[0], "root_1", fieldValue);
    TestInsertListBig(g_conn_async1[0]);

    // 启动被克隆事务
    ret =TestTransStartExtAsync(g_conn_async1[0], g_mSTrxConfig, &cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneId);

    // 设置批处理batch参数
    ret = TestBatchPrepareBig(g_conn_async1[0], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[0], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_3
    ret = testGmcPrepareStmtByLabelName(g_stmt_list1[0], "list_3", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root1[0], g_stmt_list1[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list1[0], &g_childNode1[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    keyValue = 100;
    TestSetKeyNameAndValue(g_stmt_list1[0], keyValue);
    TestYangSetNodePropertyNum(g_childNode1[0], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, 150);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list1[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 开启克隆事务
    ret = TestTransStartExtCloneAsync(g_conn_async2[0], cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepareBig(g_conn_async2[0], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root2[0], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root2[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root2[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_4
    ret = testGmcPrepareStmtByLabelName(g_stmt_list2[0], "list_4", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root2[0], g_stmt_list2[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list2[0], &g_childNode2[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    keyValue = 100;
    TestSetKeyNameAndValue(g_stmt_list2[0], keyValue);
    TestYangSetNodePropertyNum(g_childNode2[0], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, 150);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list2[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交克隆事务
    ret = TestTransCommitAsync(g_conn_async2[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 回滚被克隆事务
    ret = TestTransRollBackAsync(g_conn_async1[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    ret = TestTransStartAsync(g_conn_async1[0], g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_023");
    TestSubtreeFilterObjAllMulti(g_stmt_async1[0], "root_1", "Yang_122_Func_023", 1);
    ret = TestTransCommitAsync(g_conn_async1[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 024.连接1开启被克隆事务，Delete大对象操作，连接2开启克隆事务，Delete大对象操作，
                连接2提交事务，连接1回滚事务，subtree查询，结果为连接1修改前数据
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t keyValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId = 0;

    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[0], "root_1", fieldValue);
    TestInsertListBig(g_conn_async1[0]);

    // 启动被克隆事务
    ret =TestTransStartExtAsync(g_conn_async1[0], g_mSTrxConfig, &cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneId);

    // 设置批处理batch参数
    ret = TestBatchPrepareBig(g_conn_async1[0], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[0], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_3
    ret = testGmcPrepareStmtByLabelName(g_stmt_list1[0], "list_3", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root1[0], g_stmt_list1[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list1[0], &g_childNode1[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    keyValue = 100;
    TestSetKeyNameAndValue(g_stmt_list1[0], keyValue);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list1[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 开启克隆事务
    ret = TestTransStartExtCloneAsync(g_conn_async2[0], cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepareBig(g_conn_async2[0], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root2[0], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root2[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root2[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_4
    ret = testGmcPrepareStmtByLabelName(g_stmt_list2[0], "list_4", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root2[0], g_stmt_list2[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list2[0], &g_childNode2[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    keyValue = 100;
    TestSetKeyNameAndValue(g_stmt_list2[0], keyValue);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list2[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交克隆事务
    ret = TestTransCommitAsync(g_conn_async2[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 回滚被克隆事务
    ret = TestTransRollBackAsync(g_conn_async1[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    ret = TestTransStartAsync(g_conn_async1[0], g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_024");
    TestSubtreeFilterObjAllMulti(g_stmt_async1[0], "root_1", "Yang_122_Func_024", 1);
    ret = TestTransCommitAsync(g_conn_async1[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 025.连接1开启被克隆事务，DML操作，连接2循环开启克隆事务，DML操作，提交事务，循环10次，
                连接1提交事务，subtree查询，结果为连接1和连接2操作提交的数据合并
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId = 0;

    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[1], "root_1", fieldValue);

    // 启动被克隆事务
    ret =TestTransStartExtAsync(g_conn_async1[1], g_mSTrxConfig, &cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneId);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async1[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list1[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root1[1], g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list1[1], &g_childNode1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    for (uint32_t i = 0; i < 10; i++) {
        // 开启克隆事务
        ret = TestTransStartExtCloneAsync(g_conn_async2[1], cloneId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置批处理batch参数
        ret = TestBatchPrepare(g_conn_async2[1], &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置根节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_root2[1], "root_1", GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_root2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_root2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // list_2
        ret = testGmcPrepareStmtByLabelName(g_stmt_list2[1], "list_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root2[1], g_stmt_list2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list2[1], &g_childNode2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetNodeProperty(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 批处理提交
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 提交克隆事务
        ret = TestTransCommitAsync(g_conn_async2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 提交被克隆事务
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    ret = TestTransStartAsync(g_conn_async1[1], g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_025_01");
    TestSubtreeFilterObjAll(g_stmt_async1[1], "root_1", "Yang_122_Func_025_01");
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 026.连接1开启被克隆事务，DML操作，连接2循环开启克隆事务，DML操作，提交事务，循环10次，
                连接1回滚事务，subtree查询，结果为连接1修改前数据
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId = 0;

    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[1], "root_1", fieldValue);

    // 启动被克隆事务
    ret =TestTransStartExtAsync(g_conn_async1[1], g_mSTrxConfig, &cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneId);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async1[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list1[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root1[1], g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list1[1], &g_childNode1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    for (uint32_t i = 0; i < 10; i++) {
        // 开启克隆事务
        ret = TestTransStartExtCloneAsync(g_conn_async2[1], cloneId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置批处理batch参数
        ret = TestBatchPrepare(g_conn_async2[1], &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置根节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_root2[1], "root_1", GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_root2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_root2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // list_2
        ret = testGmcPrepareStmtByLabelName(g_stmt_list2[1], "list_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root2[1], g_stmt_list2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list2[1], &g_childNode2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetNodeProperty(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 批处理提交
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 提交克隆事务
        ret = TestTransCommitAsync(g_conn_async2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // subtree查询
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_026_01");
    TestSubtreeFilterObjAll(g_stmt_async1[1], "root_1", "Yang_122_Func_026_01");

    // 回滚被克隆事务
    ret = TestTransRollBackAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    ret = TestTransStartAsync(g_conn_async1[1], g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_026_02");
    TestSubtreeFilterObjAll(g_stmt_async1[1], "root_1", "Yang_122_Func_026_02");
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 027.连接1开启被克隆事务，DML操作，连接2循环开启克隆事务，DML操作，
                部分提交事务，部分回滚事务，循环10次，
                连接1提交事务，subtree查询，结果为连接1和连接2操作提交的数据合并
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId = 0;

    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[1], "root_1", fieldValue);

    // 启动被克隆事务
    ret =TestTransStartExtAsync(g_conn_async1[1], g_mSTrxConfig, &cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneId);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async1[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list1[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root1[1], g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list1[1], &g_childNode1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    for (uint32_t i = 0; i < 10; i++) {
        // 开启克隆事务
        ret = TestTransStartExtCloneAsync(g_conn_async2[1], cloneId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置批处理batch参数
        ret = TestBatchPrepare(g_conn_async2[1], &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置根节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_root2[1], "root_1", GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_root2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_root2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // list_2
        ret = testGmcPrepareStmtByLabelName(g_stmt_list2[1], "list_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root2[1], g_stmt_list2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list2[1], &g_childNode2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetNodeProperty(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 批处理提交
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));

        if ((i % 2) == 0) {
            // 提交克隆事务
            ret = TestTransCommitAsync(g_conn_async2[1]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            // 回滚克隆事务
            ret = TestTransRollBackAsync(g_conn_async2[1]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }

    // subtree查询
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_027_01");
    TestSubtreeFilterObjAll(g_stmt_async1[1], "root_1", "Yang_122_Func_027_01");

    // 提交被克隆事务
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    ret = TestTransStartAsync(g_conn_async1[1], g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_027_01");
    TestSubtreeFilterObjAll(g_stmt_async1[1], "root_1", "Yang_122_Func_027_01");
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 028.连接1开启被克隆事务，DML操作，10个连接顺序开启克隆事务，DML操作，提交事务，
                连接1提交事务，subtree查询，结果为所有连接操作提交的数据合并
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId = 0;

    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[1], "root_1", fieldValue);

    // 启动被克隆事务
    ret =TestTransStartExtAsync(g_conn_async1[1], g_mSTrxConfig, &cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneId);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async1[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list1[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root1[1], g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list1[1], &g_childNode1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    for (uint32_t i = 1; i <= 10; i++) {
        // 开启克隆事务
        ret = TestTransStartExtCloneAsync(g_conn_async2[i], cloneId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置批处理batch参数
        ret = TestBatchPrepare(g_conn_async2[i], &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置根节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_root2[i], "root_1", GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_root2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_root2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // list_2
        ret = testGmcPrepareStmtByLabelName(g_stmt_list2[i], "list_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root2[i], g_stmt_list2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list2[i], &g_childNode2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode2[i], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetNodeProperty(g_childNode2[i], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 批处理提交
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 提交克隆事务
        ret = TestTransCommitAsync(g_conn_async2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 提交被克隆事务
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    ret = TestTransStartAsync(g_conn_async1[1], g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_028_01");
    TestSubtreeFilterObjAll(g_stmt_async1[1], "root_1", "Yang_122_Func_028_01");
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 029.连接1开启被克隆事务，DML操作，10个连接顺序开启克隆事务，DML操作，提交事务，
                连接1回滚事务，subtree查询，结果为连接1修改前数据
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId = 0;

    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[1], "root_1", fieldValue);

    // 启动被克隆事务
    ret =TestTransStartExtAsync(g_conn_async1[1], g_mSTrxConfig, &cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneId);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async1[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list1[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root1[1], g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list1[1], &g_childNode1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    for (uint32_t i = 1; i <= 10; i++) {
        // 开启克隆事务
        ret = TestTransStartExtCloneAsync(g_conn_async2[i], cloneId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置批处理batch参数
        ret = TestBatchPrepare(g_conn_async2[i], &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置根节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_root2[i], "root_1", GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_root2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_root2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // list_2
        ret = testGmcPrepareStmtByLabelName(g_stmt_list2[i], "list_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root2[i], g_stmt_list2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list2[i], &g_childNode2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode2[i], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetNodeProperty(g_childNode2[i], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 批处理提交
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 提交克隆事务
        ret = TestTransCommitAsync(g_conn_async2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // subtree查询
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_029_01");
    TestSubtreeFilterObjAll(g_stmt_async1[1], "root_1", "Yang_122_Func_029_01");

    // 回滚被克隆事务
    ret = TestTransRollBackAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    ret = TestTransStartAsync(g_conn_async1[1], g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_029_02");
    TestSubtreeFilterObjAll(g_stmt_async1[1], "root_1", "Yang_122_Func_029_02");
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 030.连接1开启被克隆事务，DML操作，10个连接顺序开启克隆事务，DML操作，
                部分提交事务，部分回滚事务，连接1提交事务，
                subtree查询，结果为所有连接操作提交的数据合并
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId = 0;

    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[1], "root_1", fieldValue);

    // 启动被克隆事务
    ret =TestTransStartExtAsync(g_conn_async1[1], g_mSTrxConfig, &cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneId);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async1[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list1[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root1[1], g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list1[1], &g_childNode1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    for (uint32_t i = 1; i <= 10; i++) {
        // 开启克隆事务
        ret = TestTransStartExtCloneAsync(g_conn_async2[i], cloneId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置批处理batch参数
        ret = TestBatchPrepare(g_conn_async2[i], &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置根节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_root2[i], "root_1", GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_root2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_root2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // list_2
        ret = testGmcPrepareStmtByLabelName(g_stmt_list2[i], "list_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root2[i], g_stmt_list2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list2[i], &g_childNode2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode2[i], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetNodeProperty(g_childNode2[i], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 批处理提交
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));

        if ((i % 2) == 0) {
            // 提交克隆事务
            ret = TestTransCommitAsync(g_conn_async2[i]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            // 回滚克隆事务
            ret = TestTransRollBackAsync(g_conn_async2[i]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }

    // subtree查询
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_030_01");
    TestSubtreeFilterObjAll(g_stmt_async1[1], "root_1", "Yang_122_Func_030_01");

    // 提交被克隆事务
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    ret = TestTransStartAsync(g_conn_async1[1], g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_030_01");
    TestSubtreeFilterObjAll(g_stmt_async1[1], "root_1", "Yang_122_Func_030_01");
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 031.连接1开启被克隆事务，DML操作，连接2开启克隆事务，DML操作，提交事务，
                连接1提交事务，循环整个流程10次，每次都subtree查询，结果为连接1和连接2操作提交的数据合并
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId = 0;

    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[1], "root_1", fieldValue);

    for (uint32_t i = 1; i <= 10; i++) {
        // 启动被克隆事务
        ret =TestTransStartExtAsync(g_conn_async1[i], g_mSTrxConfig, &cloneId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneId);

        // 设置批处理batch参数
        ret = TestBatchPrepare(g_conn_async1[i], &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置根节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_root1[i], "root_1", GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_root1[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_root1[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // list_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list1[i], "list_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root1[i], g_stmt_list1[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list1[i], &g_childNode1[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode1[i], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetNodeProperty(g_childNode1[i], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list1[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 批处理提交
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 开启克隆事务
        ret = TestTransStartExtCloneAsync(g_conn_async2[i], cloneId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置批处理batch参数
        ret = TestBatchPrepare(g_conn_async2[i], &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置根节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_root2[i], "root_1", GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_root2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_root2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // list_2
        ret = testGmcPrepareStmtByLabelName(g_stmt_list2[i], "list_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root2[i], g_stmt_list2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list2[i], &g_childNode2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode2[i], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetNodeProperty(g_childNode2[i], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 批处理提交
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 提交克隆事务
        ret = TestTransCommitAsync(g_conn_async2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 提交被克隆事务
        ret = TestTransCommitAsync(g_conn_async1[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // subtree查询
        ret = TestTransStartAsync(g_conn_async1[i], g_mSTrxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        char fileName[128] = {0};
        (void)snprintf(fileName, 128, "Yang_122_Func_031_%02d", i);
        AW_FUN_Log(LOG_INFO, "SubtreeFilter: %s", fileName);
        TestSubtreeFilterObjAll(g_stmt_async1[i], "root_1", fileName);
        ret = TestTransCommitAsync(g_conn_async1[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

/*****************************************************************************
 Description  : 032.连接1开启被克隆事务，DML操作，连接2开启克隆事务，DML操作，提交事务，
                连接1回滚事务，循环整个流程10次，每次都subtree查询，结果为连接1修改前数据
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId = 0;

    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[1], "root_1", fieldValue);

    for (uint32_t i = 1; i <= 10; i++) {
        // 启动被克隆事务
        ret =TestTransStartExtAsync(g_conn_async1[i], g_mSTrxConfig, &cloneId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneId);

        // 设置批处理batch参数
        ret = TestBatchPrepare(g_conn_async1[i], &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置根节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_root1[i], "root_1", GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_root1[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_root1[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // list_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list1[i], "list_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root1[i], g_stmt_list1[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list1[i], &g_childNode1[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode1[i], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetNodeProperty(g_childNode1[i], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list1[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 批处理提交
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 开启克隆事务
        ret = TestTransStartExtCloneAsync(g_conn_async2[i], cloneId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置批处理batch参数
        ret = TestBatchPrepare(g_conn_async2[i], &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置根节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_root2[i], "root_1", GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_root2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_root2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // list_2
        ret = testGmcPrepareStmtByLabelName(g_stmt_list2[i], "list_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root2[i], g_stmt_list2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list2[i], &g_childNode2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode2[i], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetNodeProperty(g_childNode2[i], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 批处理提交
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 提交克隆事务
        ret = TestTransCommitAsync(g_conn_async2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // subtree查询
        char fileName[128] = {0};
        (void)snprintf(fileName, 128, "Yang_122_Func_032_%02d", i);
        AW_FUN_Log(LOG_INFO, "SubtreeFilter: %s", fileName);
        TestSubtreeFilterObjAll(g_stmt_async1[i], "root_1", fileName);

        // 回滚被克隆事务
        ret = TestTransRollBackAsync(g_conn_async1[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // subtree查询
        ret = TestTransStartAsync(g_conn_async1[i], g_mSTrxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_032_11");
        TestSubtreeFilterObjAll(g_stmt_async1[i], "root_1", "Yang_122_Func_032_11");
        ret = TestTransCommitAsync(g_conn_async1[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

/*****************************************************************************
 Description  : 033.连接1开启被克隆事务，DML操作，连接2开启克隆事务，DML操作，
                部分提交事务，部分回滚事务，连接1提交事务，循环整个流程10次，
                每次都subtree查询，结果为连接1和连接2操作提交的数据合并
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId = 0;

    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[1], "root_1", fieldValue);

    for (uint32_t i = 1; i <= 10; i++) {
        // 启动被克隆事务
        ret =TestTransStartExtAsync(g_conn_async1[i], g_mSTrxConfig, &cloneId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneId);

        // 设置批处理batch参数
        ret = TestBatchPrepare(g_conn_async1[i], &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置根节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_root1[i], "root_1", GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_root1[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_root1[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // list_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list1[i], "list_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root1[i], g_stmt_list1[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list1[i], &g_childNode1[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode1[i], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetNodeProperty(g_childNode1[i], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list1[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 批处理提交
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 开启克隆事务
        ret = TestTransStartExtCloneAsync(g_conn_async2[i], cloneId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置批处理batch参数
        ret = TestBatchPrepare(g_conn_async2[i], &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置根节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_root2[i], "root_1", GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_root2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_root2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // list_2
        ret = testGmcPrepareStmtByLabelName(g_stmt_list2[i], "list_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root2[i], g_stmt_list2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list2[i], &g_childNode2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode2[i], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetNodeProperty(g_childNode2[i], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 批处理提交
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));

        if ((i % 2) == 0) {
            // 提交克隆事务
            ret = TestTransCommitAsync(g_conn_async2[i]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            // 回滚克隆事务
            ret = TestTransRollBackAsync(g_conn_async2[i]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // subtree查询
        char fileName[128] = {0};
        (void)snprintf(fileName, 128, "Yang_122_Func_033_%02d", i);
        AW_FUN_Log(LOG_INFO, "SubtreeFilter %s_Reply.json.", fileName);
        AW_FUN_Log(LOG_INFO, "SubtreeFilter: %s", fileName);
        TestSubtreeFilterObjAll(g_stmt_async1[i], "root_1", fileName);

        // 提交被克隆事务
        ret = TestTransCommitAsync(g_conn_async1[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // subtree查询
    ret = TestTransStartAsync(g_conn_async1[1], g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_033_11");
    TestSubtreeFilterObjAll(g_stmt_async1[1], "root_1", "Yang_122_Func_033_11");
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 034.连接1开启被克隆事务，DML操作，连接2开启克隆事务，DML操作，
                连接2提交事务，连接1提交事务，过程中查询V$STORAGE_TRX_DETAIL视图
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId = 0;

    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[1], "root_1", fieldValue);

    // 启动被克隆事务
    ret =TestTransStartExtAsync(g_conn_async1[1], g_mSTrxConfig, &cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneId);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async1[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list1[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root1[1], g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list1[1], &g_childNode1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 开启克隆事务
    ret = TestTransStartExtCloneAsync(g_conn_async2[1], cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async2[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root2[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list2[1], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root2[1], g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list2[1], &g_childNode2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 视图查询表模型
    char cmd[MAX_CMD_SIZE];
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$STORAGE_TRX_DETAIL");
    AW_FUN_Log(LOG_INFO, "%s\n", cmd);
    system(cmd);

    // 校验结果
    ret = executeCommand(cmd, "TRX_CLONE_TYPE: 3", "TRX_CLONE_TYPE: 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(cmd, "TRX_CLONE_TYPE: 2", "TRX_CLONE_TYPE: 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交克隆事务
    ret = TestTransCommitAsync(g_conn_async2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交被克隆事务
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 035.10个连接都开启被克隆事务，DML操作，连接2顺序设置10个cloneId，
                开启克隆事务，DML操作，连接2提交事务，10个被克隆连接提交事务，数据不冲突，
                每次都subtree查询，结果为连接1和连接2操作提交的数据合并
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId[11] = {0};

    // 建表，teardown中会统一clear ns
    TestCreateLabel3(g_stmt_async1[1]);

    // 预先插入10个root
    fieldValue = 100;
    TestInsertRoot10(g_conn_async1[1], fieldValue);

    for (uint32_t i = 1; i <= 10; i++) {
        // 启动被克隆事务
        ret =TestTransStartExtAsync(g_conn_async1[i], g_mSTrxConfig, &cloneId[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_INFO, "GetCloneId finished. conn [%d] cloneId is %lu", i, cloneId[i]);

        // 设置批处理batch参数
        ret = TestBatchPrepare(g_conn_async1[i], &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置根节点
        char rootName[128] = {0};
        (void)snprintf(rootName, 128, "root_multi_%02d", i);
        ret = testGmcPrepareStmtByLabelName(g_stmt_root1[i], rootName, GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_root1[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_root1[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // list_1
        char list1Name[128] = {0};
        (void)snprintf(list1Name, 128, "list_multi_%02d_01", i);
        ret = testGmcPrepareStmtByLabelName(g_stmt_list1[i], list1Name, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root1[i], g_stmt_list1[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list1[i], &g_childNode1[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode1[i], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetNodeProperty(g_childNode1[i], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list1[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 批处理提交
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));
    } // 10个连接的被克隆事务已开启

    for (uint32_t i = 1; i <= 10; i++) {
        // 开启克隆事务，使用不同的cloneId
        ret = TestTransStartExtCloneAsync(g_conn_async2[i], cloneId[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置批处理batch参数
        ret = TestBatchPrepare(g_conn_async2[i], &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置根节点
        char rootName[128] = {0};
        (void)snprintf(rootName, 128, "root_multi_%02d", i);
        ret = testGmcPrepareStmtByLabelName(g_stmt_root2[i], rootName, GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_root2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_root2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // list_2
        char list2Name[128] = {0};
        (void)snprintf(list2Name, 128, "list_multi_%02d_02", i);
        ret = testGmcPrepareStmtByLabelName(g_stmt_list2[i], list2Name, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root2[i], g_stmt_list2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list2[i], &g_childNode2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode2[i], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetNodeProperty(g_childNode2[i], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 批处理提交
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 提交克隆事务
        ret = TestTransCommitAsync(g_conn_async2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 1; i <= 10; i++) {
        // 提交被克隆事务
        ret = TestTransCommitAsync(g_conn_async1[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // subtree查询
        ret = TestTransStartAsync(g_conn_async1[i], g_mSTrxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char rootName[128] = {0};
        (void)snprintf(rootName, 128, "root_multi_%02d", i);
        char fileName[128] = {0};
        (void)snprintf(fileName, 128, "Yang_122_Func_035_%02d", i);
        AW_FUN_Log(LOG_INFO, "SubtreeFilter: %s", fileName);
        TestSubtreeFilterObjAll(g_stmt_async1[i], rootName, fileName);
        ret = TestTransCommitAsync(g_conn_async1[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

/*****************************************************************************
 Description  : 036.10个连接都开启被克隆事务，DML操作，连接2顺序设置10个cloneId，
                开启克隆事务，DML操作，连接2提交事务，10个被克隆连接回滚事务，
                每次都subtree查询，结果为连接1修改前数据
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId[11] = {0};

    // 建表，teardown中会统一clear ns
    TestCreateLabel3(g_stmt_async1[1]);

    // 预先插入10个root
    fieldValue = 100;
    TestInsertRoot10(g_conn_async1[1], fieldValue);

    for (uint32_t i = 1; i <= 10; i++) {
        // 启动被克隆事务
        ret =TestTransStartExtAsync(g_conn_async1[i], g_mSTrxConfig, &cloneId[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_INFO, "GetCloneId finished. conn [%d] cloneId is %lu", i, cloneId[i]);

        // 设置批处理batch参数
        ret = TestBatchPrepare(g_conn_async1[i], &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置根节点
        char rootName[128] = {0};
        (void)snprintf(rootName, 128, "root_multi_%02d", i);
        ret = testGmcPrepareStmtByLabelName(g_stmt_root1[i], rootName, GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_root1[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_root1[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // list_1
        char list1Name[128] = {0};
        (void)snprintf(list1Name, 128, "list_multi_%02d_01", i);
        ret = testGmcPrepareStmtByLabelName(g_stmt_list1[i], list1Name, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root1[i], g_stmt_list1[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list1[i], &g_childNode1[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode1[i], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetNodeProperty(g_childNode1[i], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list1[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 批处理提交
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));
    } // 10个连接的被克隆事务已开启

    for (uint32_t i = 1; i <= 10; i++) {
        // 开启克隆事务，使用不同的cloneId
        ret = TestTransStartExtCloneAsync(g_conn_async2[i], cloneId[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置批处理batch参数
        ret = TestBatchPrepare(g_conn_async2[i], &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置根节点
        char rootName[128] = {0};
        (void)snprintf(rootName, 128, "root_multi_%02d", i);
        ret = testGmcPrepareStmtByLabelName(g_stmt_root2[i], rootName, GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_root2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_root2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // list_2
        char list2Name[128] = {0};
        (void)snprintf(list2Name, 128, "list_multi_%02d_02", i);
        ret = testGmcPrepareStmtByLabelName(g_stmt_list2[i], list2Name, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root2[i], g_stmt_list2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list2[i], &g_childNode2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode2[i], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetNodeProperty(g_childNode2[i], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 批处理提交
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // subtree查询
        char fileName[128] = {0};
        (void)snprintf(fileName, 128, "Yang_122_Func_035_%02d", i);
        AW_FUN_Log(LOG_INFO, "SubtreeFilter: %s", fileName);
        TestSubtreeFilterObjAll(g_stmt_async2[i], rootName, fileName);

        // 提交克隆事务
        ret = TestTransCommitAsync(g_conn_async2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 1; i <= 10; i++) {
        // 回滚被克隆事务
        ret = TestTransRollBackAsync(g_conn_async1[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // subtree查询
    ret = TestTransStartAsync(g_conn_async1[1], g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_036_01");
    TestSubtreeFilterObjAll(g_stmt_async1[1], "root_multi_01", "Yang_122_Func_036_01");
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_036_02");
    TestSubtreeFilterObjAll(g_stmt_async1[1], "root_multi_10", "Yang_122_Func_036_02");
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 037.10个连接都开启被克隆事务，DML操作，连接2顺序设置10个cloneId，
                开启克隆事务，DML操作，连接2提交事务，10个被克隆连接部分提交，部分回滚事务，数据不冲突，
                每次都subtree查询，结果为连接1和连接2操作提交的数据合并
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId[11] = {0};

    // 建表，teardown中会统一clear ns
    TestCreateLabel3(g_stmt_async1[1]);

    // 预先插入10个root
    fieldValue = 100;
    TestInsertRoot10(g_conn_async1[1], fieldValue);

    for (uint32_t i = 1; i <= 10; i++) {
        // 启动被克隆事务
        ret =TestTransStartExtAsync(g_conn_async1[i], g_mSTrxConfig, &cloneId[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_INFO, "GetCloneId finished. conn [%d] cloneId is %lu", i, cloneId[i]);

        // 设置批处理batch参数
        ret = TestBatchPrepare(g_conn_async1[i], &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置根节点
        char rootName[128] = {0};
        (void)snprintf(rootName, 128, "root_multi_%02d", i);
        ret = testGmcPrepareStmtByLabelName(g_stmt_root1[i], rootName, GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_root1[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_root1[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // list_1
        char list1Name[128] = {0};
        (void)snprintf(list1Name, 128, "list_multi_%02d_01", i);
        ret = testGmcPrepareStmtByLabelName(g_stmt_list1[i], list1Name, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root1[i], g_stmt_list1[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list1[i], &g_childNode1[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode1[i], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetNodeProperty(g_childNode1[i], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list1[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 批处理提交
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));
    } // 10个连接的被克隆事务已开启

    for (uint32_t i = 1; i <= 10; i++) {
        // 开启克隆事务，使用不同的cloneId
        ret = TestTransStartExtCloneAsync(g_conn_async2[i], cloneId[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置批处理batch参数
        ret = TestBatchPrepare(g_conn_async2[i], &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置根节点
        char rootName[128] = {0};
        (void)snprintf(rootName, 128, "root_multi_%02d", i);
        ret = testGmcPrepareStmtByLabelName(g_stmt_root2[i], rootName, GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_root2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_root2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // list_2
        char list2Name[128] = {0};
        (void)snprintf(list2Name, 128, "list_multi_%02d_02", i);
        ret = testGmcPrepareStmtByLabelName(g_stmt_list2[i], list2Name, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root2[i], g_stmt_list2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list2[i], &g_childNode2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode2[i], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetNodeProperty(g_childNode2[i], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 批处理提交
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 提交克隆事务
        ret = TestTransCommitAsync(g_conn_async2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 1; i <= 10; i++) {
        if ((i % 2) == 0) {
            // 提交被克隆事务
            ret = TestTransCommitAsync(g_conn_async1[i]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            // 回滚被克隆事务
            ret = TestTransRollBackAsync(g_conn_async1[i]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // subtree查询
        ret = TestTransStartAsync(g_conn_async1[i], g_mSTrxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char rootName[128] = {0};
        (void)snprintf(rootName, 128, "root_multi_%02d", i);
        char fileName[128] = {0};
        (void)snprintf(fileName, 128, "Yang_122_Func_037_%02d", i);
        AW_FUN_Log(LOG_INFO, "SubtreeFilter: %s", fileName);
        TestSubtreeFilterObjAll(g_stmt_async1[i], rootName, fileName);
        ret = TestTransCommitAsync(g_conn_async1[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

/*****************************************************************************
 Description  : 038.连接1开启被克隆事务A，两个线程并发克隆，循环克隆和合并提交，同一时间只能有1个克隆事务
 Author       : hanyang
*****************************************************************************/
uint32_t cloneIdForThread038 = 0;
void *Thread_038_01(void *args)
{
    AW_FUN_Log(LOG_STEP, "==============[1] Trans clone start==================\n\n");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    for (uint32_t i = 0; i < 100; i++) {
        // 开启克隆事务
        GmcStartExtOptionT *option = NULL;
        ret = GmcTransCreateStartExtOption(&option);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcTransStartExtOptionSetCloneId(option, cloneIdForThread038);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcTransStartExtAsync(g_conn_async2[1], option, trans_start_ext_callback_NotCheck, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (data.status != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "==============[1] GmcTransStartExtAsync fail, times is %d, status is %d.",
                i, data.status);
            continue;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
            memset(&data, 0, sizeof(AsyncUserDataT));
        }
        GmcTransStartExtOptionDestroy(option);

        // 设置批处理batch参数
        ret = TestBatchPrepare(g_conn_async2[1], &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置根节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_root2[1], "root_1", GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_root2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_root2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // list_2
        ret = testGmcPrepareStmtByLabelName(g_stmt_list2[1], "list_2", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root2[1], g_stmt_list2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list2[1], &g_childNode2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = 1;
        TestSetKeyNameAndValue(g_stmt_list2[1], fieldValue);
        TestYangSetNodeProperty(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 批处理提交
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 提交克隆事务
        ret = GmcTransCommitAsync(g_conn_async2[1], trans_commit_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (data.status != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "==============[1] GmcTransCommitAsync fail, times is %d, status is %d.",
                i, data.status);
            ret = TestTransRollBackAsync(g_conn_async2[1]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        }
    }

    AW_FUN_Log(LOG_STEP, "==============[1] Trans clone end==================\n\n");
}

void *Thread_038_02(void *args)
{
    AW_FUN_Log(LOG_STEP, "==============[2] Trans clone start==================\n\n");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    for (uint32_t i = 0; i < 100; i++) {
        // 开启克隆事务
        GmcStartExtOptionT *option = NULL;
        ret = GmcTransCreateStartExtOption(&option);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcTransStartExtOptionSetCloneId(option, cloneIdForThread038);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcTransStartExtAsync(g_conn_async2[2], option, trans_start_ext_callback_NotCheck, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (data.status != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "==============[2] GmcTransStartExtAsync fail, times is %d, status is %d.",
                i, data.status);
            continue;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
            memset(&data, 0, sizeof(AsyncUserDataT));
        }
        GmcTransStartExtOptionDestroy(option);

        // 设置批处理batch参数
        ret = TestBatchPrepare(g_conn_async2[2], &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置根节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_root2[2], "root_1", GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_root2[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_root2[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // list_2
        ret = testGmcPrepareStmtByLabelName(g_stmt_list2[2], "list_2", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root2[2], g_stmt_list2[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list2[2], &g_childNode2[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = 2;
        TestSetKeyNameAndValue(g_stmt_list2[2], fieldValue);
        TestYangSetNodeProperty(g_childNode2[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list2[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 批处理提交
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 提交克隆事务
        ret = GmcTransCommitAsync(g_conn_async2[2], trans_commit_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (data.status != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "==============[2] GmcTransCommitAsync fail, times is %d, status is %d.",
                i, data.status);
            ret = TestTransRollBackAsync(g_conn_async2[2]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        }
    }

    AW_FUN_Log(LOG_STEP, "==============[2] Trans clone end==================\n\n");
}

// main
TEST_F(CloneTrans_test, Yang_122_Func_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert root***********************************/
    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[1], "root_1", fieldValue);

    // 启动被克隆事务
    ret =TestTransStartExtAsync(g_conn_async1[1], g_mSTrxConfig, &cloneIdForThread038);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneIdForThread038);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async1[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list1[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root1[1], g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list1[1], &g_childNode1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 2线程并发
    pthread_t Thread[2] = {0};

    pthread_create(&Thread[0], NULL, Thread_038_01, NULL);
    pthread_create(&Thread[1], NULL, Thread_038_02, NULL);

    pthread_join(Thread[0], NULL);
    pthread_join(Thread[1], NULL);

    // 提交被克隆事务
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AddWhiteList(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);
    AddWhiteList(GMERR_NO_ACTIVE_TRANSACTION);
}

/*****************************************************************************
 Description  : 039.线程1开启被克隆事务，批量插入16k的数据，线程2创建克隆事务，两个线程都循环操作
 Author       : hanyang
*****************************************************************************/
uint32_t cloneIdForThread039 = 0;
void *Thread_039_01(void *args)
{
    AW_FUN_Log(LOG_STEP, "==============[1] Trans clone DML start==================\n\n");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 循环写数据
    for (uint32_t i = 0; i < 100; i++) {
        // 设置批处理batch参数
        ret = TestBatchPrepare(g_conn_async1[1], &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置根节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_root1[1], "root_1", GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_root1[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_root1[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // list_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list1[1], "list_1", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root1[1], g_stmt_list1[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list1[1], &g_childNode1[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = 100;
        TestSetKeyNameAndValue(g_stmt_list1[1], fieldValue);
        TestYangSetNodeProperty(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list1[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 批处理提交
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));
    }

    AW_FUN_Log(LOG_STEP, "==============[1] Trans clone DML end==================\n\n");
}

void *Thread_039_02(void *args)
{
    AW_FUN_Log(LOG_STEP, "==============[2] Trans clone start==================\n\n");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    for (uint32_t i = 0; i < 100; i++) {
        // 开启克隆事务
        GmcStartExtOptionT *option = NULL;
        ret = GmcTransCreateStartExtOption(&option);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcTransStartExtOptionSetCloneId(option, cloneIdForThread039);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcTransStartExtAsync(g_conn_async2[1], option, trans_start_ext_callback_NotCheck, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (data.status != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "==============[1] GmcTransStartExtAsync fail, times is %d, status is %d.",
                i, data.status);
            continue;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
            memset(&data, 0, sizeof(AsyncUserDataT));
        }
        GmcTransStartExtOptionDestroy(option);

        // 设置批处理batch参数
        ret = TestBatchPrepare(g_conn_async2[1], &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置根节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_root2[1], "root_1", GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_root2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_root2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // list_2
        ret = testGmcPrepareStmtByLabelName(g_stmt_list2[1], "list_2", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root2[1], g_stmt_list2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list2[1], &g_childNode2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = 1;
        TestSetKeyNameAndValue(g_stmt_list2[1], fieldValue);
        TestYangSetNodeProperty(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 批处理提交
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 提交克隆事务
        ret = GmcTransCommitAsync(g_conn_async2[1], trans_commit_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (data.status != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "==============[1] GmcTransCommitAsync fail, times is %d, status is %d.",
                i, data.status);
            ret = TestTransRollBackAsync(g_conn_async2[1]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        }
    }

    AW_FUN_Log(LOG_STEP, "==============[2] Trans clone end==================\n\n");
}

// main
TEST_F(CloneTrans_test, Yang_122_Func_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert root***********************************/
    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[1], "root_1", fieldValue);

    // 启动被克隆事务
    ret =TestTransStartExtAsync(g_conn_async1[1], g_mSTrxConfig, &cloneIdForThread039);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneIdForThread039);

    // 2线程并发
    pthread_t Thread[2] = {0};

    pthread_create(&Thread[0], NULL, Thread_039_01, NULL);
    pthread_create(&Thread[1], NULL, Thread_039_02, NULL);

    pthread_join(Thread[0], NULL);
    pthread_join(Thread[1], NULL);

    // 提交被克隆事务
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AddWhiteList(GMERR_DATA_EXCEPTION);
    AddWhiteList(GMERR_RESTRICT_VIOLATION);
    AddWhiteList(GMERR_SYNTAX_ERROR);
}

/*****************************************************************************
 Description  : 040.GmcTransStartExtOptionSetTrxCfg接口设置的事务类型不是乐观RR事务，报错
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    AsyncUserDataT data = {0};
    uint32_t *cloneId = 0;

    GmcStartExtOptionT *option = NULL;
    ret = GmcTransCreateStartExtOption(&option);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcTxConfigT trxConfig;
    trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    trxConfig.readOnly = false;

    // 乐观事务
    // GMC_TX_ISOLATION_COMMITTED  expStatus
    trxConfig.trxType = GMC_OPTIMISTIC_TRX;
    trxConfig.type = GMC_TX_ISOLATION_COMMITTED;
    ret = GmcTransStartExtOptionSetTrxCfg(option, &trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    data.expStatus = GMERR_INVALID_PARAMETER_VALUE;
    ret = GmcTransStartExtAsync(g_conn_async1[1], option, trans_start_ext_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // GMC_TX_ISOLATION_COMMITTED
    trxConfig.type = GMC_TX_ISOLATION_UNCOMMITTED;
    ret = GmcTransStartExtOptionSetTrxCfg(option, &trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    data.expStatus = GMERR_INVALID_PARAMETER_VALUE;
    ret = GmcTransStartExtAsync(g_conn_async1[1], option, trans_start_ext_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // GMC_TX_ISOLATION_COMMITTED
    trxConfig.type = GMC_TX_ISOLATION_SERIALIZABLE;
    ret = GmcTransStartExtOptionSetTrxCfg(option, &trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    data.expStatus = GMERR_INVALID_PARAMETER_VALUE;
    ret = GmcTransStartExtAsync(g_conn_async1[1], option, trans_start_ext_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 悲观事务
    // GMC_TX_ISOLATION_COMMITTED
    trxConfig.trxType = GMC_PESSIMISITIC_TRX;
    trxConfig.type = GMC_TX_ISOLATION_COMMITTED;
    ret = GmcTransStartExtOptionSetTrxCfg(option, &trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    data.expStatus = GMERR_INVALID_PARAMETER_VALUE;
    ret = GmcTransStartExtAsync(g_conn_async1[1], option, trans_start_ext_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // GMC_TX_ISOLATION_COMMITTED
    trxConfig.type = GMC_TX_ISOLATION_UNCOMMITTED;
    ret = GmcTransStartExtOptionSetTrxCfg(option, &trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    data.expStatus = GMERR_INVALID_PARAMETER_VALUE;
    ret = GmcTransStartExtAsync(g_conn_async1[1], option, trans_start_ext_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // GMC_TX_ISOLATION_COMMITTED
    trxConfig.type = GMC_TX_ISOLATION_SERIALIZABLE;
    ret = GmcTransStartExtOptionSetTrxCfg(option, &trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    data.expStatus = GMERR_INVALID_PARAMETER_VALUE;
    ret = GmcTransStartExtAsync(g_conn_async1[1], option, trans_start_ext_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // GMC_TX_ISOLATION_COMMITTED
    trxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    ret = GmcTransStartExtOptionSetTrxCfg(option, &trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    data.expStatus = GMERR_INVALID_PARAMETER_VALUE;
    ret = GmcTransStartExtAsync(g_conn_async1[1], option, trans_start_ext_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    GmcTransStartExtOptionDestroy(option);
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
}

/*****************************************************************************
 Description  : 041.连接1事务已提交，连接2开启克隆事务，报错
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId = 0;

    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[1], "root_1", fieldValue);

    // 启动被克隆事务
    ret =TestTransStartExtAsync(g_conn_async1[1], g_mSTrxConfig, &cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneId);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async1[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list1[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root1[1], g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list1[1], &g_childNode1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交被克隆事务
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启克隆事务
    ret = TestTransStartExtCloneAsync(g_conn_async2[1], cloneId, GMERR_NO_ACTIVE_TRANSACTION);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_NO_ACTIVE_TRANSACTION);
}

/*****************************************************************************
 Description  : 042.连接1事务已回滚，连接2开启克隆事务，报错
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId = 0;

    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[1], "root_1", fieldValue);

    // 启动被克隆事务
    ret =TestTransStartExtAsync(g_conn_async1[1], g_mSTrxConfig, &cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneId);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async1[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list1[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root1[1], g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list1[1], &g_childNode1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚被克隆事务
    ret = TestTransRollBackAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启克隆事务
    ret = TestTransStartExtCloneAsync(g_conn_async2[1], cloneId, GMERR_NO_ACTIVE_TRANSACTION);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_NO_ACTIVE_TRANSACTION);
}

/*****************************************************************************
 Description  : 043.连接2已开启一个普通事务，再开启一个克隆事务，报错
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId = 0;

    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[1], "root_1", fieldValue);

    // 启动被克隆事务
    ret =TestTransStartExtAsync(g_conn_async1[1], g_mSTrxConfig, &cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneId);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async1[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list1[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root1[1], g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list1[1], &g_childNode1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 开启普通事务
    ret =TestTransStartAsync(g_conn_async2[1], g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启克隆事务
    ret = TestTransStartExtCloneAsync(g_conn_async2[1], cloneId, GMERR_INVALID_PARAMETER_VALUE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    AddWhiteList(GMERR_TRANSACTION_ROLLBACK);

    // 提交普通事务
    ret = TestTransCommitAsync(g_conn_async2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交被克隆事务
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 044.连接2已开启一个克隆事务，再开启一个克隆事务，报错
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId = 0;

    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[1], "root_1", fieldValue);

    // 启动被克隆事务
    ret =TestTransStartExtAsync(g_conn_async1[1], g_mSTrxConfig, &cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneId);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async1[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list1[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root1[1], g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list1[1], &g_childNode1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 开启克隆事务
    ret = TestTransStartExtCloneAsync(g_conn_async2[1], cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 再开启克隆事务
    ret = TestTransStartExtCloneAsync(g_conn_async2[1], cloneId, GMERR_INVALID_PARAMETER_VALUE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async2[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root2[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list2[1], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root2[1], g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list2[1], &g_childNode2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    // 该连接的事务状态已被置为abore，需要回滚
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, data.status);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚克隆事务
    ret = TestTransRollBackAsync(g_conn_async2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交被克隆事务
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    ret = TestTransStartAsync(g_conn_async1[1], g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_044_01");
    TestSubtreeFilterObjAll(g_stmt_async1[1], "root_1", "Yang_122_Func_044_01");
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    AddWhiteList(GMERR_TRANSACTION_ROLLBACK);
}

/*****************************************************************************
 Description  : 045.连接2已开启一个克隆事务，再开启一个普通事务，报错
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId = 0;

    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[1], "root_1", fieldValue);

    // 启动被克隆事务
    ret =TestTransStartExtAsync(g_conn_async1[1], g_mSTrxConfig, &cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneId);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async1[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list1[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root1[1], g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list1[1], &g_childNode1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 开启克隆事务
    ret = TestTransStartExtCloneAsync(g_conn_async2[1], cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启普通事务
    ret = GmcTransStartAsync(g_conn_async2[1], &g_mSTrxConfig, trans_start_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, data.status);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async2[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root2[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list2[1], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root2[1], g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list2[1], &g_childNode2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    // 该连接的事务状态已被置为abore，需要回滚
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, data.status);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚克隆事务
    ret = TestTransRollBackAsync(g_conn_async2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交被克隆事务
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    ret = TestTransStartAsync(g_conn_async1[1], g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_045_01");
    TestSubtreeFilterObjAll(g_stmt_async1[1], "root_1", "Yang_122_Func_045_01");
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    AddWhiteList(GMERR_TRANSACTION_ROLLBACK);
}

/*****************************************************************************
 Description  : 046.连接1开启一个事务，连接2开启一个非克隆事务，提交事务，再开启一个克隆事务，成功
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId = 0;

    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[1], "root_1", fieldValue);
    TestInsertRoot(g_conn_async1[1], "root_2", fieldValue);

    // 启动被克隆事务
    ret =TestTransStartExtAsync(g_conn_async1[1], g_mSTrxConfig, &cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneId);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async1[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list1[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root1[1], g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list1[1], &g_childNode1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 开启普通事务
    ret =TestTransStartAsync(g_conn_async2[1], g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async2[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root2[1], "root_2", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_22
    ret = testGmcPrepareStmtByLabelName(g_stmt_list2[1], "list_22", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root2[1], g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list2[1], &g_childNode2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交普通事务
    ret = TestTransCommitAsync(g_conn_async2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    ret = TestTransStartAsync(g_conn_async2[1], g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_046_01");
    TestSubtreeFilterObjAll(g_stmt_async2[1], "root_2", "Yang_122_Func_046_01");
    ret = TestTransCommitAsync(g_conn_async2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启克隆事务
    ret = TestTransStartExtCloneAsync(g_conn_async2[1], cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async2[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root2[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list2[1], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root2[1], g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list2[1], &g_childNode2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    TestYangSetNodeProperty_PK(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_046_02");
    TestSubtreeFilterObjAll(g_stmt_async2[1], "root_1", "Yang_122_Func_046_02");

    // 提交克隆事务
    ret = TestTransCommitAsync(g_conn_async2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交被克隆事务
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    ret = TestTransStartAsync(g_conn_async1[1], g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_046_03");
    TestSubtreeFilterObjAll(g_stmt_async1[1], "root_1", "Yang_122_Func_046_03");
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_046_04");
    TestSubtreeFilterObjAll(g_stmt_async1[1], "root_2", "Yang_122_Func_046_04");
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 047.连接1开启一个事务，连接2开启一个非克隆事务，回滚事务，再开启一个克隆事务，成功
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId = 0;

    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[1], "root_1", fieldValue);
    TestInsertRoot(g_conn_async1[1], "root_2", fieldValue);

    // 启动被克隆事务
    ret =TestTransStartExtAsync(g_conn_async1[1], g_mSTrxConfig, &cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneId);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async1[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list1[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root1[1], g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list1[1], &g_childNode1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 开启普通事务
    ret =TestTransStartAsync(g_conn_async2[1], g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async2[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root2[1], "root_2", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_22
    ret = testGmcPrepareStmtByLabelName(g_stmt_list2[1], "list_22", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root2[1], g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list2[1], &g_childNode2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚普通事务
    ret = TestTransRollBackAsync(g_conn_async2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    ret = TestTransStartAsync(g_conn_async2[1], g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_047_01");
    TestSubtreeFilterObjAll(g_stmt_async2[1], "root_2", "Yang_122_Func_047_01");
    ret = TestTransCommitAsync(g_conn_async2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启克隆事务
    ret = TestTransStartExtCloneAsync(g_conn_async2[1], cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async2[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root2[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list2[1], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root2[1], g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list2[1], &g_childNode2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    TestYangSetNodeProperty_PK(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_047_02");
    TestSubtreeFilterObjAll(g_stmt_async2[1], "root_1", "Yang_122_Func_047_02");
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_047_03");
    TestSubtreeFilterObjAll(g_stmt_async2[1], "root_2", "Yang_122_Func_047_03");

    // 提交克隆事务
    ret = TestTransCommitAsync(g_conn_async2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交被克隆事务
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    ret = TestTransStartAsync(g_conn_async1[1], g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_047_04");
    TestSubtreeFilterObjAll(g_stmt_async1[1], "root_1", "Yang_122_Func_047_04");
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_047_05");
    TestSubtreeFilterObjAll(g_stmt_async1[1], "root_2", "Yang_122_Func_047_05");
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 048.连接1开启一个可克隆事务A，提交事务，连接1再开启一个可克隆事务B，
                连接2克隆事务A，报错，克隆事务B，成功
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId1 = 0;
    uint32_t cloneId2 = 0;

    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[1], "root_1", fieldValue);

    // 启动被克隆事务
    ret =TestTransStartExtAsync(g_conn_async1[1], g_mSTrxConfig, &cloneId1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneId1);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async1[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list1[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root1[1], g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list1[1], &g_childNode1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交被克隆事务
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动被克隆事务
    ret =TestTransStartExtAsync(g_conn_async1[1], g_mSTrxConfig, &cloneId2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneId2);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async1[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list1[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root1[1], g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list1[1], &g_childNode1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    TestYangSetNodeProperty_PK(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 开启克隆事务A，报错
    ret = TestTransStartExtCloneAsync(g_conn_async2[1], cloneId1, GMERR_NO_ACTIVE_TRANSACTION);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_NO_ACTIVE_TRANSACTION);

    // 开启克隆事务B
    ret = TestTransStartExtCloneAsync(g_conn_async2[1], cloneId2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async2[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root2[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list2[1], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root2[1], g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list2[1], &g_childNode2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交克隆事务
    ret = TestTransCommitAsync(g_conn_async2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交被克隆事务
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    ret = TestTransStartAsync(g_conn_async1[1], g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_048_01");
    TestSubtreeFilterObjAll(g_stmt_async1[1], "root_1", "Yang_122_Func_048_01");
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 049.连接1开启一个可克隆事务A，回滚事务，连接1再开启一个可克隆事务B，
                连接2克隆事务A，报错，克隆事务B，成功
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId1 = 0;
    uint32_t cloneId2 = 0;

    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[1], "root_1", fieldValue);

    // 启动被克隆事务
    ret =TestTransStartExtAsync(g_conn_async1[1], g_mSTrxConfig, &cloneId1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneId1);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async1[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list1[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root1[1], g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list1[1], &g_childNode1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚被克隆事务
    ret = TestTransRollBackAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动被克隆事务
    ret =TestTransStartExtAsync(g_conn_async1[1], g_mSTrxConfig, &cloneId2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneId2);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async1[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list1[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root1[1], g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list1[1], &g_childNode1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    TestYangSetNodeProperty_PK(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 开启克隆事务A，报错
    ret = TestTransStartExtCloneAsync(g_conn_async2[1], cloneId1, GMERR_NO_ACTIVE_TRANSACTION);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_NO_ACTIVE_TRANSACTION);

    // 开启克隆事务B
    ret = TestTransStartExtCloneAsync(g_conn_async2[1], cloneId2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async2[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root2[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list2[1], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root2[1], g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list2[1], &g_childNode2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交克隆事务
    ret = TestTransCommitAsync(g_conn_async2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交被克隆事务
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    ret = TestTransStartAsync(g_conn_async1[1], g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_049_01");
    TestSubtreeFilterObjAll(g_stmt_async1[1], "root_1", "Yang_122_Func_049_01");
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 050.连接2克隆事务时，传入不存在的cloneId
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId = 0;

    // 开启克隆事务
    cloneId = 100;
    ret = TestTransStartExtCloneAsync(g_conn_async2[1], cloneId, GMERR_NO_ACTIVE_TRANSACTION);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_NO_ACTIVE_TRANSACTION);
}

/*****************************************************************************
 Description  : 051.连接1开启可克隆事务，连接2克隆该事务，连接1事务提交，连接2事务提交时报错
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId = 0;

    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[1], "root_1", fieldValue);

    // 启动被克隆事务
    ret =TestTransStartExtAsync(g_conn_async1[1], g_mSTrxConfig, &cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneId);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async1[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list1[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root1[1], g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list1[1], &g_childNode1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 开启克隆事务
    ret = TestTransStartExtCloneAsync(g_conn_async2[1], cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async2[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root2[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list2[1], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root2[1], g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list2[1], &g_childNode2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交被克隆事务
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交克隆事务，报错
    ret = GmcTransCommitAsync(g_conn_async2[1], trans_commit_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NO_ACTIVE_TRANSACTION, data.status);
    AddWhiteList(GMERR_NO_ACTIVE_TRANSACTION);

    // 回滚克隆事务
    ret = TestTransRollBackAsync(g_conn_async2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 052.连接1开启可克隆事务，连接2克隆该事务，连接1事务回滚，连接2事务提交时报错
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId = 0;

    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[1], "root_1", fieldValue);

    // 启动被克隆事务
    ret =TestTransStartExtAsync(g_conn_async1[1], g_mSTrxConfig, &cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneId);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async1[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list1[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root1[1], g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list1[1], &g_childNode1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 开启克隆事务
    ret = TestTransStartExtCloneAsync(g_conn_async2[1], cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async2[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root2[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list2[1], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root2[1], g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list2[1], &g_childNode2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚被克隆事务
    ret = TestTransRollBackAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交克隆事务，报错
    ret = GmcTransCommitAsync(g_conn_async2[1], trans_commit_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NO_ACTIVE_TRANSACTION, data.status);
    AddWhiteList(GMERR_NO_ACTIVE_TRANSACTION);

    // 回滚克隆事务
    ret = TestTransRollBackAsync(g_conn_async2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 053.连接1开启可克隆事务，连接2克隆该事务，DML操作后，连接2提交事务，
                连接2再次克隆事务，DML操作，回滚事务，再次克隆事务，DML操作，提交事务，
                连接1提交事务，操作成功，数据为连接1+连接2的2次提交的修改合集
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId = 0;

    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[1], "root_1", fieldValue);

    // 启动被克隆事务
    ret =TestTransStartExtAsync(g_conn_async1[1], g_mSTrxConfig, &cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneId);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async1[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list1[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root1[1], g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list1[1], &g_childNode1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 开启克隆事务--第1次
    ret = TestTransStartExtCloneAsync(g_conn_async2[1], cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async2[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root2[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list2[1], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root2[1], g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list2[1], &g_childNode2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_053_01");
    TestSubtreeFilterObjAll(g_stmt_async2[1], "root_1", "Yang_122_Func_053_01");

    // 提交克隆事务
    ret = TestTransCommitAsync(g_conn_async2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启克隆事务--第2次
    ret = TestTransStartExtCloneAsync(g_conn_async2[1], cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async2[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root2[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list2[1], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root2[1], g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list2[1], &g_childNode2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    TestYangSetNodeProperty_PK(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_053_02");
    TestSubtreeFilterObjAll(g_stmt_async2[1], "root_1", "Yang_122_Func_053_02");

    // 回滚克隆事务
    ret = TestTransRollBackAsync(g_conn_async2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启克隆事务--第3次
    ret = TestTransStartExtCloneAsync(g_conn_async2[1], cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async2[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root2[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list2[1], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root2[1], g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list2[1], &g_childNode2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 300;
    TestYangSetNodeProperty_PK(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_053_03");
    TestSubtreeFilterObjAll(g_stmt_async2[1], "root_1", "Yang_122_Func_053_03");

    // 提交克隆事务
    ret = TestTransCommitAsync(g_conn_async2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交被克隆事务
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    ret = TestTransStartAsync(g_conn_async1[1], g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_053_04");
    TestSubtreeFilterObjAll(g_stmt_async1[1], "root_1", "Yang_122_Func_053_04");
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 054.连接1开启可克隆事务，连接2克隆该事务，连接3也克隆该事务，报错
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_054)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId = 0;

    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[1], "root_1", fieldValue);

    // 启动被克隆事务
    ret =TestTransStartExtAsync(g_conn_async1[1], g_mSTrxConfig, &cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneId);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async1[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list1[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root1[1], g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list1[1], &g_childNode1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 开启克隆事务
    ret = TestTransStartExtCloneAsync(g_conn_async2[1], cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接2开启克隆事务，报错
    ret = TestTransStartExtCloneAsync(g_conn_async2[2], cloneId, GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async2[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root2[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list2[1], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root2[1], g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list2[1], &g_childNode2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交克隆事务
    ret = TestTransCommitAsync(g_conn_async2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交被克隆事务
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    ret = TestTransStartAsync(g_conn_async1[1], g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_054_01");
    TestSubtreeFilterObjAll(g_stmt_async1[1], "root_1", "Yang_122_Func_054_01");
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 055.连接1开启可克隆事务，连接2克隆该事务，连接2事务提交，连接3也克隆该事务，操作成功
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_055)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId = 0;

    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[1], "root_1", fieldValue);

    // 启动被克隆事务
    ret =TestTransStartExtAsync(g_conn_async1[1], g_mSTrxConfig, &cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneId);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async1[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list1[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root1[1], g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list1[1], &g_childNode1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 开启克隆事务
    ret = TestTransStartExtCloneAsync(g_conn_async2[1], cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async2[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root2[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list2[1], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root2[1], g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list2[1], &g_childNode2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交克隆事务
    ret = TestTransCommitAsync(g_conn_async2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接3开启克隆事务
    ret = TestTransStartExtCloneAsync(g_conn_async2[2], cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async2[2], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root2[2], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root2[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root2[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list2[2], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root2[2], g_stmt_list2[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list2[2], &g_childNode2[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    TestYangSetNodeProperty_PK(g_childNode2[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode2[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list2[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交克隆事务
    ret = TestTransCommitAsync(g_conn_async2[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交被克隆事务
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    ret = TestTransStartAsync(g_conn_async1[1], g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_055_01");
    TestSubtreeFilterObjAll(g_stmt_async1[1], "root_1", "Yang_122_Func_055_01");
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 056.连接1开启可克隆事务，连接2克隆该事务，连接2事务回滚，连接3也克隆该事务，操作成功
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_056)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId = 0;

    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[1], "root_1", fieldValue);

    // 启动被克隆事务
    ret =TestTransStartExtAsync(g_conn_async1[1], g_mSTrxConfig, &cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneId);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async1[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list1[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root1[1], g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list1[1], &g_childNode1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 开启克隆事务
    ret = TestTransStartExtCloneAsync(g_conn_async2[1], cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async2[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root2[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list2[1], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root2[1], g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list2[1], &g_childNode2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚克隆事务
    ret = TestTransRollBackAsync(g_conn_async2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接3开启克隆事务
    ret = TestTransStartExtCloneAsync(g_conn_async2[2], cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async2[2], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root2[2], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root2[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root2[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list2[2], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root2[2], g_stmt_list2[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list2[2], &g_childNode2[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    TestYangSetNodeProperty_PK(g_childNode2[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode2[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list2[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交克隆事务
    ret = TestTransCommitAsync(g_conn_async2[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交被克隆事务
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    ret = TestTransStartAsync(g_conn_async1[1], g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_056_01");
    TestSubtreeFilterObjAll(g_stmt_async1[1], "root_1", "Yang_122_Func_056_01");
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 057.连接1开启可克隆事务，连接2克隆该事务，连接2DML操作，还未提交事务，连接1DML操作报错
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_057)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId = 0;

    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[1], "root_1", fieldValue);

    // 启动被克隆事务
    ret =TestTransStartExtAsync(g_conn_async1[1], g_mSTrxConfig, &cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneId);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async1[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list1[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root1[1], g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list1[1], &g_childNode1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 开启克隆事务
    ret = TestTransStartExtCloneAsync(g_conn_async2[1], cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async2[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root2[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list2[1], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root2[1], g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list2[1], &g_childNode2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    /**********************连接1DML操作，暂不支持报错*************************/
    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async1[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list1[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root1[1], g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list1[1], &g_childNode1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    TestYangSetNodeProperty_PK(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交克隆事务
    ret = TestTransCommitAsync(g_conn_async2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 回滚被克隆事务
    ret = TestTransRollBackAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 058.连接1开启可克隆事务，连接2克隆该事务，连接2DML操作，提交事务，连接1DML操作成功
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_058)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId = 0;

    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[1], "root_1", fieldValue);

    // 启动被克隆事务
    ret =TestTransStartExtAsync(g_conn_async1[1], g_mSTrxConfig, &cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneId);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async1[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list1[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root1[1], g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list1[1], &g_childNode1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 开启克隆事务
    ret = TestTransStartExtCloneAsync(g_conn_async2[1], cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async2[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root2[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list2[1], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root2[1], g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list2[1], &g_childNode2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交克隆事务
    ret = TestTransCommitAsync(g_conn_async2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /**********************连接1DML操作*************************/
    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async1[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list1[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root1[1], g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list1[1], &g_childNode1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    TestYangSetNodeProperty_PK(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交被克隆事务
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    ret = TestTransStartAsync(g_conn_async1[1], g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_058_01");
    TestSubtreeFilterObjAll(g_stmt_async1[1], "root_1", "Yang_122_Func_058_01");
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 059.连接1开启可克隆事务，连接2克隆该事务，连接2DML操作，回滚事务，连接1DML操作成功
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_059)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId = 0;

    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[1], "root_1", fieldValue);

    // 启动被克隆事务
    ret =TestTransStartExtAsync(g_conn_async1[1], g_mSTrxConfig, &cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneId);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async1[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list1[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root1[1], g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list1[1], &g_childNode1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 开启克隆事务
    ret = TestTransStartExtCloneAsync(g_conn_async2[1], cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async2[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root2[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list2[1], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root2[1], g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list2[1], &g_childNode2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚克隆事务
    ret = TestTransRollBackAsync(g_conn_async2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /**********************连接1DML操作*************************/
    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async1[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list1[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root1[1], g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list1[1], &g_childNode1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    TestYangSetNodeProperty_PK(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交被克隆事务
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    ret = TestTransStartAsync(g_conn_async1[1], g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_059_01");
    TestSubtreeFilterObjAll(g_stmt_async1[1], "root_1", "Yang_122_Func_059_01");
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 062.连接1设置cloneId=0开启可克隆事务，连接2克隆该事务，连接2DML操作，提交事务，连接1提交事务
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_062)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId = 0;

    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[1], "root_1", fieldValue);

    // 启动被克隆事务
    GmcStartExtOptionT *option = NULL;
    ret = GmcTransCreateStartExtOption(&option);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStartExtOptionSetTrxCfg(option, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    cloneId = 0;
    ret = GmcTransStartExtOptionSetCloneId(option, cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 回调函数中获取cloneId
    ret = GmcTransStartExtAsync(g_conn_async1[1], option, trans_start_ext_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 获取cloneId
    cloneId = data.cloneId;
    GmcTransStartExtOptionDestroy(option);
    AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneId);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async1[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list1[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root1[1], g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list1[1], &g_childNode1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 开启克隆事务
    ret = TestTransStartExtCloneAsync(g_conn_async2[1], cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async2[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root2[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list2[1], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root2[1], g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list2[1], &g_childNode2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交克隆事务
    ret = TestTransCommitAsync(g_conn_async2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交被克隆事务
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    ret = TestTransStartAsync(g_conn_async1[1], g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_062_01");
    TestSubtreeFilterObjAll(g_stmt_async1[1], "root_1", "Yang_122_Func_062_01");
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 063.连接1开启可克隆事务，连接2设置config，并克隆该事务，不报错，设置值无效果
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_063)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId = 0;

    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[1], "root_1", fieldValue);

    // 启动被克隆事务
    ret =TestTransStartExtAsync(g_conn_async1[1], g_mSTrxConfig, &cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneId);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async1[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list1[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root1[1], g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list1[1], &g_childNode1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 开启克隆事务
    GmcStartExtOptionT *option = NULL;
    ret = GmcTransCreateStartExtOption(&option);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStartExtOptionSetTrxCfg(option, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStartExtOptionSetCloneId(option, cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStartExtAsync(g_conn_async2[1], option, trans_start_ext_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    GmcTransStartExtOptionDestroy(option);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async2[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root2[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list2[1], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root2[1], g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list2[1], &g_childNode2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交克隆事务
    ret = TestTransCommitAsync(g_conn_async2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交被克隆事务
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    ret = TestTransStartAsync(g_conn_async1[1], g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_063_01");
    TestSubtreeFilterObjAll(g_stmt_async1[1], "root_1", "Yang_122_Func_063_01");
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 064.连接1开启可克隆事务，但未设置config，报错
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_064)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId = 0;

    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[1], "root_1", fieldValue);

    // 启动被克隆事务
    GmcStartExtOptionT *option = NULL;
    ret = GmcTransCreateStartExtOption(&option);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 未设置config
    ret = GmcTransStartExtAsync(g_conn_async1[1], option, trans_start_ext_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    GmcTransStartExtOptionDestroy(option);
}

/*****************************************************************************
 Description  : 067.连接1开启可克隆事务，连接1DML操作，连接2克隆该事务，连接2DML操作，
                事务未提交，连接1异常中断，查询视图，查看资源是否回收
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_067)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId = 0;

    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[1], "root_1", fieldValue);

    // 启动被克隆事务
    ret =TestTransStartExtAsync(g_conn_async1[1], g_mSTrxConfig, &cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneId);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async1[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list1[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root1[1], g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list1[1], &g_childNode1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 开启克隆事务
    ret = TestTransStartExtCloneAsync(g_conn_async2[1], cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async2[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root2[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list2[1], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root2[1], g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list2[1], &g_childNode2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 视图查询表模型
    char cmd[MAX_CMD_SIZE];
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$STORAGE_TRX_DETAIL");
    AW_FUN_Log(LOG_INFO, "%s\n", cmd);
    system(cmd);

    // 校验结果
    ret = executeCommand(cmd, "TRX_CLONE_TYPE: 3", "TRX_CLONE_TYPE: 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(cmd, "TRX_CLONE_TYPE: 2", "TRX_CLONE_TYPE: 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接1异常中断
    for (uint32_t i = 0; i < 10; i++) {
        GmcFreeStmt(g_stmt_root1[i]);
        GmcFreeStmt(g_stmt_list1[i]);
        ret = testGmcDisconnect(g_conn_async1[i], g_stmt_async1[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_conn_async1[i] = NULL;
        g_stmt_async1[i] = NULL;
        g_stmt_root1[i] = NULL;
        g_stmt_list1[i] = NULL;
        g_rootNode1[i] = NULL;
        g_childNode1[i] = NULL;
    }

    AW_FUN_Log(LOG_INFO, "============After Disconnect=============");
    system(cmd);
    // 校验结果
    ret = executeCommand(cmd, "TRX_CLONE_TYPE: 3", "TRX_CLONE_TYPE: 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(cmd, "TRX_CLONE_TYPE: 2", "TRX_CLONE_TYPE: 2");
    AW_MACRO_EXPECT_EQ_INT(-1, ret);

    // 提交克隆事务，报错
    ret = GmcTransCommitAsync(g_conn_async2[1], trans_commit_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NO_ACTIVE_TRANSACTION, data.status);
    AddWhiteList(GMERR_NO_ACTIVE_TRANSACTION);

    // 回滚克隆事务
    ret = TestTransRollBackAsync(g_conn_async2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 再次创建连接1
    YangConnOptionT connOptions = {0};
    connOptions.isLobConn = true;
    for (uint32_t i = 0; i < 11; i++) {
        if (i == 0) {
            // 0号连接用于操作大数据
            ret = TestYangGmcConnect(&g_conn_async1[i], &g_stmt_async1[i], GMC_CONN_TYPE_ASYNC, &connOptions);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            // 1~10号连接用于普通克隆
            ret = TestYangGmcConnect(&g_conn_async1[i], &g_stmt_async1[i], GMC_CONN_TYPE_ASYNC);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcAllocStmt(g_conn_async1[i], &g_stmt_root1[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcAllocStmt(g_conn_async1[i], &g_stmt_list1[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcUseNamespaceAsync(g_stmt_async1[i], NAMESPACE122, use_namespace_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        memset(&data, 0, sizeof(AsyncUserDataT));
    }
}

/*****************************************************************************
 Description  : 068.连接1开启可克隆事务，连接1DML操作，连接2克隆该事务，连接2DML操作，
                事务未提交，连接2异常中断，查询视图，查看资源是否回收
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_068)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId = 0;

    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[1], "root_1", fieldValue);

    // 启动被克隆事务
    ret =TestTransStartExtAsync(g_conn_async1[1], g_mSTrxConfig, &cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneId);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async1[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list1[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root1[1], g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list1[1], &g_childNode1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 开启克隆事务
    ret = TestTransStartExtCloneAsync(g_conn_async2[1], cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async2[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root2[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list2[1], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root2[1], g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list2[1], &g_childNode2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 视图查询表模型
    char cmd[MAX_CMD_SIZE];
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$STORAGE_TRX_DETAIL");
    AW_FUN_Log(LOG_INFO, "%s\n", cmd);
    system(cmd);

    // 校验结果
    ret = executeCommand(cmd, "TRX_CLONE_TYPE: 3", "TRX_CLONE_TYPE: 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(cmd, "TRX_CLONE_TYPE: 2", "TRX_CLONE_TYPE: 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接2异常中断
    for (uint32_t i = 0; i < 10; i++) {
        GmcFreeStmt(g_stmt_root2[i]);
        GmcFreeStmt(g_stmt_list2[i]);
        ret = testGmcDisconnect(g_conn_async2[i], g_stmt_async2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_conn_async2[i] = NULL;
        g_stmt_async2[i] = NULL;
        g_stmt_root2[i] = NULL;
        g_stmt_list2[i] = NULL;
        g_rootNode2[i] = NULL;
        g_childNode2[i] = NULL;
    }

    AW_FUN_Log(LOG_INFO, "============After Disconnect=============");
    system(cmd);
    // 校验结果
    ret = executeCommand(cmd, "TRX_CLONE_TYPE: 3", "TRX_CLONE_TYPE: 3");
    AW_MACRO_EXPECT_EQ_INT(-1, ret);
    ret = executeCommand(cmd, "TRX_CLONE_TYPE: 2", "TRX_CLONE_TYPE: 2");
    AW_MACRO_EXPECT_EQ_INT(-1, ret);
    // 被克隆之后，状态1--->状态4，克隆的事务异常断链后，状态2--->状态1，
    ret = executeCommand(cmd, "TRX_CLONE_TYPE: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(cmd, "TRX_CLONE_TYPE: 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交被克隆事务
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    ret = TestTransStartAsync(g_conn_async1[1], g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "SubtreeFilter: Yang_122_Func_068_01");
    TestSubtreeFilterObjAll(g_stmt_async1[1], "root_1", "Yang_122_Func_068_01");
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 再次创建连接2
    YangConnOptionT connOptions = {0};
    connOptions.isLobConn = true;
    for (uint32_t i = 0; i < 11; i++) {
        if (i == 0) {
            // 0号连接用于操作大数据
            ret = TestYangGmcConnect(&g_conn_async2[i], &g_stmt_async2[i], GMC_CONN_TYPE_ASYNC, &connOptions);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            // 1~10号连接用于普通克隆
            ret = TestYangGmcConnect(&g_conn_async2[i], &g_stmt_async2[i], GMC_CONN_TYPE_ASYNC);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcAllocStmt(g_conn_async2[i], &g_stmt_root2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcAllocStmt(g_conn_async2[i], &g_stmt_list2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcUseNamespaceAsync(g_stmt_async2[i], NAMESPACE122, use_namespace_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        memset(&data, 0, sizeof(AsyncUserDataT));
    }
}

/*****************************************************************************
 Description  : 069.连接1开启可克隆事务，连接1DML操作，连接2克隆该事务，连接2DML操作，
                事务未提交，连接1和连接2都异常中断，查询视图，查看资源是否回收
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_069)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId = 0;

    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[1], "root_1", fieldValue);

    // 启动被克隆事务
    ret =TestTransStartExtAsync(g_conn_async1[1], g_mSTrxConfig, &cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneId);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async1[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list1[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root1[1], g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list1[1], &g_childNode1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 开启克隆事务
    ret = TestTransStartExtCloneAsync(g_conn_async2[1], cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async2[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root2[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list2[1], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root2[1], g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list2[1], &g_childNode2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 视图查询表模型
    char cmd[MAX_CMD_SIZE];
    (void)snprintf(cmd, MAX_CMD_SIZE, "gmsysview -q V\\$STORAGE_TRX_DETAIL");
    AW_FUN_Log(LOG_INFO, "%s\n", cmd);
    system(cmd);

    // 校验结果
    ret = executeCommand(cmd, "TRX_CLONE_TYPE: 3", "TRX_CLONE_TYPE: 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(cmd, "TRX_CLONE_TYPE: 2", "TRX_CLONE_TYPE: 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接1和2异常中断
    for (uint32_t i = 0; i < 10; i++) {
        GmcFreeStmt(g_stmt_root1[i]);
        GmcFreeStmt(g_stmt_list1[i]);
        ret = testGmcDisconnect(g_conn_async1[i], g_stmt_async1[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_conn_async1[i] = NULL;
        g_stmt_async1[i] = NULL;
        g_stmt_root1[i] = NULL;
        g_stmt_list1[i] = NULL;
        g_rootNode1[i] = NULL;
        g_childNode1[i] = NULL;

        GmcFreeStmt(g_stmt_root2[i]);
        GmcFreeStmt(g_stmt_list2[i]);
        ret = testGmcDisconnect(g_conn_async2[i], g_stmt_async2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_conn_async2[i] = NULL;
        g_stmt_async2[i] = NULL;
        g_stmt_root2[i] = NULL;
        g_stmt_list2[i] = NULL;
        g_rootNode2[i] = NULL;
        g_childNode2[i] = NULL;
    }

    AW_FUN_Log(LOG_INFO, "============After Disconnect=============");
    system(cmd);
    // 校验结果
    ret = executeCommand(cmd, "TRX_CLONE_TYPE: 3", "TRX_CLONE_TYPE: 3");
    AW_MACRO_EXPECT_EQ_INT(-1, ret);
    ret = executeCommand(cmd, "TRX_CLONE_TYPE: 2", "TRX_CLONE_TYPE: 2");
    AW_MACRO_EXPECT_EQ_INT(-1, ret);

    // 再次创建连接1和2
    YangConnOptionT connOptions = {0};
    connOptions.isLobConn = true;
    for (uint32_t i = 0; i < 11; i++) {
        if (i == 0) {
            // 0号连接用于操作大数据
            ret = TestYangGmcConnect(&g_conn_async1[i], &g_stmt_async1[i], GMC_CONN_TYPE_ASYNC, &connOptions);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            // 1~10号连接用于普通克隆
            ret = TestYangGmcConnect(&g_conn_async1[i], &g_stmt_async1[i], GMC_CONN_TYPE_ASYNC);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcAllocStmt(g_conn_async1[i], &g_stmt_root1[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcAllocStmt(g_conn_async1[i], &g_stmt_list1[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcUseNamespaceAsync(g_stmt_async1[i], NAMESPACE122, use_namespace_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        memset(&data, 0, sizeof(AsyncUserDataT));

        if (i == 0) {
            // 0号连接用于操作大数据
            ret = TestYangGmcConnect(&g_conn_async2[i], &g_stmt_async2[i], GMC_CONN_TYPE_ASYNC, &connOptions);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            // 1~10号连接用于普通克隆
            ret = TestYangGmcConnect(&g_conn_async2[i], &g_stmt_async2[i], GMC_CONN_TYPE_ASYNC);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcAllocStmt(g_conn_async2[i], &g_stmt_root2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcAllocStmt(g_conn_async2[i], &g_stmt_list2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcUseNamespaceAsync(g_stmt_async2[i], NAMESPACE122, use_namespace_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        memset(&data, 0, sizeof(AsyncUserDataT));
    }
}

/*****************************************************************************
 Description  : 070.连接1和连接2操作不同的表，连接1开启可克隆事务，连接1DML操作，
                连接2克隆该事务，连接2DML操作，连接2提交事务后，连接1和连接2操作的表不能删除，
                连接1提交事务后，连接1和连接2操作的表可以删除
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTrans_test, Yang_122_Func_070)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId = 0;

    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[1], "root_1", fieldValue);
    TestInsertRoot(g_conn_async1[1], "root_2", fieldValue);

    // 启动被克隆事务
    ret =TestTransStartExtAsync(g_conn_async1[1], g_mSTrxConfig, &cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneId);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async1[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[1], "root_1", GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 开启克隆事务
    ret = TestTransStartExtCloneAsync(g_conn_async2[1], cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async2[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root2[1], "root_2", GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交克隆事务
    ret = TestTransCommitAsync(g_conn_async2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除root2
    ret = GmcDropEdgeLabelAsync(g_stmt_async2[1], "root_2_to_list_21", drop_edge_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_LOCK_NOT_AVAILABLE, data.status);

    ret = GmcDropEdgeLabelAsync(g_stmt_async2[1], "root_2_to_list_22", drop_edge_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_LOCK_NOT_AVAILABLE, data.status);

    ret = GmcDropVertexLabelAsync(g_stmt_async2[1], "root_2", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_LOCK_NOT_AVAILABLE, data.status);

    // 提交被克隆事务
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除root_2
    ret = GmcDropEdgeLabelAsync(g_stmt_async2[1], "root_2_to_list_21", drop_edge_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcDropEdgeLabelAsync(g_stmt_async2[1], "root_2_to_list_22", drop_edge_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcDropVertexLabelAsync(g_stmt_async2[1], "root_2", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcDropVertexLabelAsync(g_stmt_async2[1], "list_21", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcDropVertexLabelAsync(g_stmt_async2[1], "list_22", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);
}

class CloneTransModify_test : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void CloneTransModify_test::SetUpTestCase()
{
    g_needCheckWhenSucc = false;
}

void CloneTransModify_test::TearDownTestCase()
{}

void CloneTransModify_test::SetUp()
{}

void CloneTransModify_test::TearDown()
{}

void TestResInit()
{
    int ret;

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    uint32_t i = 0;

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    YangConnOptionT connOptions = {0};
    connOptions.isLobConn = true;
    for (i = 0; i < 11; i++) {
        if (i == 0) {
            // 0号连接用于操作大数据
            ret = TestYangGmcConnect(&g_conn_async1[i], &g_stmt_async1[i], GMC_CONN_TYPE_ASYNC, &connOptions);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            // 1~10号连接用于普通克隆
            ret = TestYangGmcConnect(&g_conn_async1[i], &g_stmt_async1[i], GMC_CONN_TYPE_ASYNC);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcAllocStmt(g_conn_async1[i], &g_stmt_root1[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcAllocStmt(g_conn_async1[i], &g_stmt_list1[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    for (i = 0; i < 11; i++) {
        if (i == 0) {
            // 0号连接用于操作大数据
            ret = TestYangGmcConnect(&g_conn_async2[i], &g_stmt_async2[i], GMC_CONN_TYPE_ASYNC, &connOptions);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            // 1~10号连接用于普通克隆
            ret = TestYangGmcConnect(&g_conn_async2[i], &g_stmt_async2[i], GMC_CONN_TYPE_ASYNC);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcAllocStmt(g_conn_async2[i], &g_stmt_root2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcAllocStmt(g_conn_async2[i], &g_stmt_list2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 异步创建namespace
    GmcDropNamespace(g_stmt, NAMESPACE122);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = NAMESPACE122;
    nspCfg.tablespaceName = NULL;
    nspCfg.userName = NAMESPACE_USER_NAME;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观

    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async1[1], &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // Yang事务配置为乐观+可重复读，需要和namespace保持一致
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    for (i = 0; i < 11; i++) {
        ret = GmcUseNamespaceAsync(g_stmt_async1[i], NAMESPACE122, use_namespace_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        memset(&data, 0, sizeof(AsyncUserDataT));

        ret = GmcUseNamespaceAsync(g_stmt_async2[i], NAMESPACE122, use_namespace_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        memset(&data, 0, sizeof(AsyncUserDataT));
    }

    // 创建表
    TestCreateLabel(g_stmt_async1[1]);
    g_printFlag = true;

    AW_CHECK_LOG_BEGIN();
}

void TestResClean()
{
    int ret;
    uint32_t i = 0;
    AsyncUserDataT data = {0};
    AW_CHECK_LOG_END();

    // 删除表
    TestDropLabel(g_stmt_async1[1]);

    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_async1[1], NAMESPACE122, drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    for (i = 0; i < 10; i++) {
        GmcFreeStmt(g_stmt_root1[i]);
        GmcFreeStmt(g_stmt_list1[i]);
        ret = testGmcDisconnect(g_conn_async1[i], g_stmt_async1[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_conn_async1[i] = NULL;
        g_stmt_async1[i] = NULL;
        g_stmt_root1[i] = NULL;
        g_stmt_list1[i] = NULL;
        g_rootNode1[i] = NULL;
        g_childNode1[i] = NULL;

        GmcFreeStmt(g_stmt_root2[i]);
        GmcFreeStmt(g_stmt_list2[i]);
        ret = testGmcDisconnect(g_conn_async2[i], g_stmt_async2[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_conn_async2[i] = NULL;
        g_stmt_async2[i] = NULL;
        g_stmt_root2[i] = NULL;
        g_stmt_list2[i] = NULL;
        g_rootNode2[i] = NULL;
        g_childNode2[i] = NULL;
    }

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
}

/*****************************************************************************
 Description  : 001.enableTrxClone配置为范围外值，服务启动失败
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTransModify_test, Yang_122_Func_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"enableTrxClone=100\"");
    system("sh $TEST_HOME/tools/start.sh");

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
    AddWhiteList(GMERR_CONNECTION_FAILURE);
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
}

/*****************************************************************************
 Description  : 060.不开启enableTrxClone，开启被克隆事务报错
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTransModify_test, Yang_122_Func_060)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"enableTrxClone=0\"");
    system("sh $TEST_HOME/tools/start.sh");

    // 建立连接，建表
    TestResInit();

    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId = 0;

    GmcStartExtOptionT *option = NULL;

    ret = GmcTransCreateStartExtOption(&option);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStartExtOptionSetTrxCfg(option, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 回调函数中获取cloneId
    data.expStatus = GMERR_INVALID_PARAMETER_VALUE;
    ret = GmcTransStartExtAsync(g_conn_async1[1], option, trans_start_ext_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, data.status);

    GmcTransStartExtOptionDestroy(option);

    // 释放连接，删表
    TestResClean();
}

/*****************************************************************************
 Description  : 061.打开增量持久化开关，创建被克隆事务报错
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTransModify_test, Yang_122_Func_061)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"enableTrxClone=1\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"scheduleMode=2\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"persistentMode=1\"");
    system("sh $TEST_HOME/tools/start.sh");

    // 同时打开增量持久化和事务克隆开关，服务启动失败，persist increment not support trx clone
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
    AddWhiteList(GMERR_CONNECTION_FAILURE);
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
}

/*****************************************************************************
 Description  : 065.开启长事务监控，连接1开启可克隆事务，连接2克隆该事务，连接2DML操作，
                增加sleep时间，达到长事务超时时间，连接2提交事务，提交失败，回滚事务，
                连接1提交事务，超过门限时间，提交失败，回滚事务
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTransModify_test, Yang_122_Func_065)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"enableTrxClone=1\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"scheduleMode=2\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"trxMonitorEnable=1\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"trxMonitorThreshold=9,10\"");
    system("sh $TEST_HOME/tools/start.sh");

    // 建立连接，建表
    TestResInit();

    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId = 0;

    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[1], "root_1", fieldValue);

    // 启动被克隆事务
    ret =TestTransStartExtAsync(g_conn_async1[1], g_mSTrxConfig, &cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneId);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async1[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list1[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root1[1], g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list1[1], &g_childNode1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 开启克隆事务
    ret = TestTransStartExtCloneAsync(g_conn_async2[1], cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async2[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root2[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list2[1], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root2[1], g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list2[1], &g_childNode2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 长事务检测超时
    sleep(10);

    // 提交克隆事务，报错
    ret = GmcTransCommitAsync(g_conn_async2[1], trans_commit_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NO_ACTIVE_TRANSACTION, data.status);
    AddWhiteList(GMERR_NO_ACTIVE_TRANSACTION);

    // 回滚克隆事务
    ret = TestTransRollBackAsync(g_conn_async2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交被克隆事务，报错
    ret = GmcTransCommitAsync(g_conn_async1[1], trans_commit_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, data.status);

    // 回滚被克隆事务
    ret = TestTransRollBackAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_TRANSACTION_ROLLBACK);

    // 释放连接，删表
    TestResClean();
}

/*****************************************************************************
 Description  : 066.开启长事务监控，连接1开启可克隆事务，连接2克隆该事务，连接2DML操作，
                增加sleep时间，未达到长事务超时时间，连接2提交事务，提交成功，
                连接1提交事务，超过门限时间，提交失败，回滚事务
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTransModify_test, Yang_122_Func_066)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"enableTrxClone=1\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"scheduleMode=2\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"trxMonitorEnable=1\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"trxMonitorThreshold=9,10\"");
    system("sh $TEST_HOME/tools/start.sh");

    // 建立连接，建表
    TestResInit();

    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId = 0;

    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[1], "root_1", fieldValue);

    // 启动被克隆事务
    ret =TestTransStartExtAsync(g_conn_async1[1], g_mSTrxConfig, &cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneId);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async1[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list1[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root1[1], g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list1[1], &g_childNode1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 开启克隆事务
    ret = TestTransStartExtCloneAsync(g_conn_async2[1], cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async2[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root2[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list2[1], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root2[1], g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list2[1], &g_childNode2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 长事务检测未超时
    sleep(2);

    // 提交克隆事务
    ret = TestTransCommitAsync(g_conn_async2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 长事务检测超时
    sleep(10);

    // 提交被克隆事务，报错
    ret = GmcTransCommitAsync(g_conn_async1[1], trans_commit_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, data.status);

    // 回滚被克隆事务
    ret = TestTransRollBackAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_TRANSACTION_ROLLBACK);

    // 释放连接，删表
    TestResClean();
}

/*****************************************************************************
 Description  : 071.改小maxConnNum的值，连接1开启被克隆事务，DML操作，
                连接2循环开启克隆事务，提交事务，直到达到事务槽最大值报错
 Author       : hanyang
*****************************************************************************/
TEST_F(CloneTransModify_test, Yang_122_Func_071)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"enableTrxClone=1\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"scheduleMode=2\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxConnNum=30\"");
    system("sh $TEST_HOME/tools/start.sh");

    // 建立连接，建表
    TestResInit();

    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId = 0;

    // 预先插入root
    fieldValue = 100;
    TestInsertRoot(g_conn_async1[1], "root_1", fieldValue);

    // 启动被克隆事务
    ret =TestTransStartExtAsync(g_conn_async1[1], g_mSTrxConfig, &cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "GetCloneId finished. cloneId is %lu", cloneId);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async1[1], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1[1], "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list1[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root1[1], g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list1[1], &g_childNode1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    for (uint32_t i = 0; i < 100; i++) {
        // 开启克隆事务
        GmcStartExtOptionT *option = NULL;
        ret = GmcTransCreateStartExtOption(&option);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcTransStartExtOptionSetCloneId(option, cloneId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcTransStartExtAsync(g_conn_async2[1], option, trans_start_ext_callback_NotCheck, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (data.status != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "GmcTransStartExtAsync fail, times is %d, status is %d.", i, data.status);
            AW_MACRO_EXPECT_EQ_INT(GMERR_INSUFFICIENT_RESOURCES, data.status);
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
            memset(&data, 0, sizeof(AsyncUserDataT));
        }
        GmcTransStartExtOptionDestroy(option);

        // 设置批处理batch参数
        ret = TestBatchPrepare(g_conn_async2[1], &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置根节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_root2[1], "root_1", GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_root2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_root2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // list_2
        ret = testGmcPrepareStmtByLabelName(g_stmt_list2[1], "list_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root2[1], g_stmt_list2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list2[1], &g_childNode2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetNodeProperty(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 批处理提交
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 提交克隆事务，可能在克隆事务提交时检测到事务槽满
        ret = GmcTransCommitAsync(g_conn_async2[1], trans_commit_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (data.status == GMERR_INSUFFICIENT_RESOURCES) {
            AW_FUN_Log(LOG_INFO, "GmcTransCommitAsync fail, times is %d, status is %d.", i, data.status);
            AW_MACRO_EXPECT_EQ_INT(GMERR_INSUFFICIENT_RESOURCES, data.status);

            // 回滚克隆事务
            ret = TestTransRollBackAsync(g_conn_async2[1]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        }
    }

    // 提交被克隆事务
    ret = TestTransCommitAsync(g_conn_async1[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_INSUFFICIENT_RESOURCES);
    // 释放连接，删表
    TestResClean();
}

/*****************************************************************************
 Description  : 072.DTS2025070705904, 乐观事务重试，重试时达到最大记录数，报错
 Author       : hanyang
*****************************************************************************/
void *Thread_072_01(void *args)
{
    AW_FUN_Log(LOG_STEP, "==============[1] Trans start==================\n\n");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    GmcTxConfigT mSTrxConfig = {0};
    mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    mSTrxConfig.readOnly = false;
    mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    mSTrxConfig.forceCommitMode = GMC_FORCE_COMMIT_MODE_0;

    for (uint32_t i = 0; i < 100; i++) {
        // 开启事务，merge新的数据
        ret = TestTransStartAsync(g_conn_async1[1], mSTrxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置批处理batch参数
        ret = TestBatchPrepare(g_conn_async1[1], &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置根节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_root1[1], "root_4", GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_root1[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_root1[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // list_41
        ret = testGmcPrepareStmtByLabelName(g_stmt_list1[1], "list_41", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root1[1], g_stmt_list1[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list1[1], &g_childNode1[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = 200;
        TestSetKeyNameAndValue(g_stmt_list1[1], fieldValue);
        TestYangSetNodeProperty(g_childNode1[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list1[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 批处理提交
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 提交事务
        ret = GmcTransCommitAsync(g_conn_async1[1], trans_commit_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (data.status != GMERR_OK) {
            ret = TestTransRollBackAsync(g_conn_async1[1]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        }

        /*******************************************************************/

        // 开启事务，remove新的数据
        ret = TestTransStartAsync(g_conn_async1[1], mSTrxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置批处理batch参数
        ret = TestBatchPrepare(g_conn_async1[1], &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置根节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_root1[1], "root_4", GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_root1[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_root1[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // list_41
        ret = testGmcPrepareStmtByLabelName(g_stmt_list1[1], "list_41", GMC_OPERATION_REMOVE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root1[1], g_stmt_list1[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list1[1], &g_childNode1[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = 200;
        TestSetKeyNameAndValue(g_stmt_list1[1], fieldValue);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list1[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 批处理提交
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 提交事务
        ret = GmcTransCommitAsync(g_conn_async1[1], trans_commit_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (data.status != GMERR_OK) {
            ret = TestTransRollBackAsync(g_conn_async1[1]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        }
    }

    AW_FUN_Log(LOG_STEP, "==============[1] Trans end==================\n\n");
}

void *Thread_072_02(void *args)
{
    AW_FUN_Log(LOG_STEP, "==============[2] Trans start==================\n\n");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    GmcTxConfigT mSTrxConfig = {0};
    mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    mSTrxConfig.readOnly = false;
    mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    mSTrxConfig.forceCommitMode = GMC_FORCE_COMMIT_MODE_0;

    for (uint32_t i = 0; i < 100; i++) {
        // 开启事务，merge新的数据
        ret = TestTransStartAsync(g_conn_async2[1], mSTrxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置批处理batch参数
        ret = TestBatchPrepare(g_conn_async2[1], &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置根节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_root2[1], "root_4", GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_root2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_root2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // list_41
        ret = testGmcPrepareStmtByLabelName(g_stmt_list2[1], "list_41", GMC_OPERATION_REMOVE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root2[1], g_stmt_list2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list2[1], &g_childNode2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = 300;
        TestSetKeyNameAndValue(g_stmt_list2[1], fieldValue);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 批处理提交
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 提交事务
        ret = GmcTransCommitAsync(g_conn_async2[1], trans_commit_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (data.status != GMERR_OK) {
            ret = TestTransRollBackAsync(g_conn_async2[1]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        }

        /*******************************************************************/

        // 开启事务，remove新的数据
        ret = TestTransStartAsync(g_conn_async2[1], mSTrxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置批处理batch参数
        ret = TestBatchPrepare(g_conn_async2[1], &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置根节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_root2[1], "root_4", GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_root2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_root2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // list_41
        ret = testGmcPrepareStmtByLabelName(g_stmt_list2[1], "list_41", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root2[1], g_stmt_list2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list2[1], &g_childNode2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = 300;
        TestSetKeyNameAndValue(g_stmt_list2[1], fieldValue);
        TestYangSetNodeProperty(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 批处理提交
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 提交事务
        ret = GmcTransCommitAsync(g_conn_async2[1], trans_commit_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (data.status != GMERR_OK) {
            ret = TestTransRollBackAsync(g_conn_async2[1]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        }
    }

    AW_FUN_Log(LOG_STEP, "==============[2] Trans end==================\n\n");
}

void *Thread_072_03(void *args)
{
    AW_FUN_Log(LOG_STEP, "==============[3] Trans start==================\n\n");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    uint32_t times = 0;
    uint32_t initValue = 0;
    uint32_t keyValue = 0;
    uint32_t newValue = 0;

    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(stmt, NAMESPACE122);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    times = 1000;
    initValue = 0;
    for (uint32_t i = 0; i < 1000; i++) {
        // 更新vertex表数据
        ret = testGmcPrepareStmtByLabelName(stmt, "vertex_04", GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        keyValue = initValue + i;

        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &keyValue, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        newValue = 100;
        TestGmcSetVertexProperty(stmt, newValue);

        // update
        ret = GmcSetIndexKeyName(stmt, "vertex_04_key");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret == GMERR_RESTRICT_VIOLATION) {
            continue;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "==============[3] Trans end==================\n\n");
}

void *Thread_072_04(void *args)
{
    AW_FUN_Log(LOG_STEP, "==============[4] Trans start==================\n\n");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    uint32_t times = 0;
    uint32_t initValue = 0;
    uint32_t keyValue = 0;
    uint32_t newValue = 0;

    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(stmt, NAMESPACE122);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 100; i++) {
        // 更新vertex表数据
        ret = testGmcPrepareStmtByLabelName(stmt, "vertex_04", GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        keyValue = initValue + i;

        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &keyValue, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        newValue = 200;
        TestGmcSetVertexProperty(stmt, newValue);

        // update
        ret = GmcSetIndexKeyName(stmt, "vertex_04_key");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret == GMERR_RESTRICT_VIOLATION) {
            continue;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "==============[4] Trans end==================\n\n");
}

// main
TEST_F(CloneTransModify_test, Yang_122_Func_072)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh");
    system("gmadmin -cfgName forceCommitEnable -cfgVal 1");

    // 建立连接，建表
    TestResInit();

    // 建表
    TestCreateLabel4(g_stmt_async1[1]);

    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t cloneId = 0;
    uint32_t times = 0;
    uint32_t initValue = 0;

    /***************************insert data***********************************/
    // 预先插入数据，yang表
    fieldValue = 100;
    TestInsertData4(g_conn_async1[1]);

    // 预先插入数据，vertex表
    ret = GmcUseNamespace(g_stmt, NAMESPACE122);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    initValue = 0;
    times = 1000;
    TestInsertVertex4(g_stmt, times, initValue, "vertex_04");

    // 线程并发
    pthread_t Thread[4] = {0};

    pthread_create(&Thread[0], NULL, Thread_072_01, NULL);
    pthread_create(&Thread[1], NULL, Thread_072_02, NULL);
    pthread_create(&Thread[2], NULL, Thread_072_03, NULL);
    pthread_create(&Thread[3], NULL, Thread_072_04, NULL);

    pthread_join(Thread[0], NULL);
    pthread_join(Thread[1], NULL);
    pthread_join(Thread[2], NULL);
    pthread_join(Thread[3], NULL);

    system("gmadmin -cfgName forceCommitEnable -cfgVal 0");
    AddWhiteList(GMERR_RECORD_COUNT_LIMIT_EXCEEDED);
    AddWhiteList(GMERR_RESTRICT_VIOLATION);
    AddWhiteList(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);
    // 释放连接，删表
    TestResClean();
}
