/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: 122_CloneTrans
 * Author: hanyang
 * Create: 2025-07-02
 */
#ifndef CLONE_TRANS_H
#define CLONE_TRANS_H
#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/prctl.h>
#include <sys/socket.h>
#include <sys/epoll.h>
#include <assert.h>
#include <errno.h>
#include <stdint.h>
#include <typeinfo>
#include <limits.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "jansson.h"

// MS config
const char *MS_CONFIG = "{\"max_record_count\" : 1000000}";
const char *MS_CONFIG_TRANS = R"(
{
    "max_record_count":1000000,
    "isFastReadUncommitted":0,
    "auto_increment":1,
    "yang_model":1
})";

const char *MS_CONFIG_TRANS_ONE = R"(
{
    "max_record_count":1,
    "isFastReadUncommitted":0,
    "auto_increment":1,
    "yang_model":1
})";

const char *g_keyName = "PK";

GmcTxConfigT g_mSTrxConfig;
const char *NAMESPACE122 = "NamespaceABC122";
const char *NAMESPACE_USER_NAME = "abc";
bool g_printFlag;
#define MAX_CMD_SIZE 1024

void TestCreateLabel(GmcStmtT *stmt)
{
    int ret = 0;
    char *vLabelSchema = NULL;
    AsyncUserDataT data = {0};

    readJanssonFile("./schema_file/yang_01.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateVertexLabelAsync(stmt, vLabelSchema, MS_CONFIG_TRANS, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    readJanssonFile("./schema_file/edge_01.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateEdgeLabelAsync(stmt, vLabelSchema, MS_CONFIG_TRANS, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    readJanssonFile("./schema_file/yang_02.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateVertexLabelAsync(stmt, vLabelSchema, MS_CONFIG_TRANS, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    readJanssonFile("./schema_file/edge_02.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateEdgeLabelAsync(stmt, vLabelSchema, MS_CONFIG_TRANS, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;
}

void TestCreateLabel3(GmcStmtT *stmt)
{
    int ret = 0;
    char *vLabelSchema = NULL;
    AsyncUserDataT data = {0};

    readJanssonFile("./schema_file/yang_03.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateVertexLabelAsync(stmt, vLabelSchema, MS_CONFIG_TRANS, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    readJanssonFile("./schema_file/edge_03.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateEdgeLabelAsync(stmt, vLabelSchema, MS_CONFIG_TRANS, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;
}

void TestCreateLabel4(GmcStmtT *stmt)
{
    int ret = 0;
    char *vLabelSchema = NULL;
    AsyncUserDataT data = {0};

    readJanssonFile("./schema_file/vertex_04.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateVertexLabelAsync(stmt, vLabelSchema, MS_CONFIG_TRANS, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    // 最大记录数1
    readJanssonFile("./schema_file/yang_04.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateVertexLabelAsync(stmt, vLabelSchema, MS_CONFIG_TRANS_ONE, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    readJanssonFile("./schema_file/edge_04.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateEdgeLabelAsync(stmt, vLabelSchema, MS_CONFIG_TRANS, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;
}

void TestDropLabel(GmcStmtT *stmt)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = GmcClearNamespaceAsync(stmt, NAMESPACE122, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
}

int TestCheckNull(GmcStmtT *stmt)
{
    if (stmt == NULL) {
        return -1;
    } else {
        return GMERR_OK;
    }
}

/******************************DML*******************************************/
// 事务克隆暂不支持diff，默认关闭
int TestBatchPrepare(GmcConnT *conn, GmcBatchT **batch, GmcBatchTypeE batchType = GMC_BATCH_YANG,
    GmcYangDiffTypeE diffType = GMC_YANG_DIFF_OFF)
{
    int ret = 0;
    GmcBatchOptionT batchOption;

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBatchType(&batchOption, batchType);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcYangBatchOptionSetDiffType(&batchOption, diffType);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }

    ret = GmcBatchPrepare(conn, &batchOption, batch);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }

    return ret;
}

int TestBatchPrepareBig(GmcConnT *conn, GmcBatchT **batch, GmcBatchTypeE batchType = GMC_BATCH_YANG,
    GmcYangDiffTypeE diffType = GMC_YANG_DIFF_OFF)
{
    int ret = 0;
    GmcBatchOptionT batchOption;

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 30000);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBatchType(&batchOption, batchType);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcYangBatchOptionSetDiffType(&batchOption, diffType);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }

    ret = GmcBatchPrepare(conn, &batchOption, batch);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }

    return ret;
}

int TestTransStartAsync(GmcConnT *conn, GmcTxConfigT Config)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = GmcTransStartAsync(conn, &Config, trans_start_callback, &data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = testWaitAsyncRecv(&data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        return ret;
    }
}

// 开启被克隆事务，返回cloneId
int TestTransStartExtAsync(GmcConnT *conn, GmcTxConfigT Config, uint32_t *cloneId)
{
    int ret = 0;
    AsyncUserDataT data = {0};
    GmcStartExtOptionT *option = NULL;

    ret = GmcTransCreateStartExtOption(&option);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStartExtOptionSetTrxCfg(option, &Config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 回调函数中获取cloneId
    ret = GmcTransStartExtAsync(conn, option, trans_start_ext_callback, &data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcTransStartExtOptionDestroy(option);
        return ret;
    }
    ret = testWaitAsyncRecv(&data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcTransStartExtOptionDestroy(option);
        return ret;
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

        // 获取cloneId
        *cloneId = data.cloneId;
        GmcTransStartExtOptionDestroy(option);
        return ret;
    }
}

// 开启克隆事务
int TestTransStartExtCloneAsync(GmcConnT *conn, uint32_t cloneId, int32_t exceptStatus = GMERR_OK)
{
    int ret = 0;
    AsyncUserDataT data = {0};
    GmcStartExtOptionT *option = NULL;

    ret = GmcTransCreateStartExtOption(&option);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStartExtOptionSetCloneId(option, cloneId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    data.expStatus = exceptStatus;
    ret = GmcTransStartExtAsync(conn, option, trans_start_ext_callback, &data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcTransStartExtOptionDestroy(option);
        return ret;
    }
    ret = testWaitAsyncRecv(&data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcTransStartExtOptionDestroy(option);
        return ret;
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(exceptStatus, data.status);
        GmcTransStartExtOptionDestroy(option);
        return ret;
    }
}

AsyncUserDataT data1;
int TestTransCommitAsync(GmcConnT *conn)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = GmcTransCommitAsync(conn, trans_commit_callback, &data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = testWaitAsyncRecv(&data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (data.status == GMERR_TRANSACTION_ROLLBACK) {
            AW_FUN_Log(LOG_INFO, "data.status = GMERR_TRANSACTION_ROLLBACK, the transaction will rollback.");
            memset(&data1, 0, sizeof(AsyncUserDataT));
            int ret1 = GmcTransRollBackAsync(conn, trans_rollback_callback, &data1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        }
        return ret;
    }
}

int TestTransRollBackAsync(GmcConnT *conn)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = GmcTransRollBackAsync(conn, trans_commit_callback, &data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = testWaitAsyncRecv(&data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

        return ret;
    }
}

void trans_start_ext_callback_fail(void *userData, GmcStartExtResT *startExtRes, int32_t status, const char *errMsg)
{
    int ret;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, status);
    if (userData) {
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        userData1->historyRecvNum++;

        // 获取cloneId句柄
        if (startExtRes != NULL) {
            ret = GmcTransStartExtResGetCloneId(startExtRes, &userData1->cloneId);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // 空指针检测
            ret = GmcTransStartExtResGetCloneId(NULL, &userData1->cloneId);
            AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
            ret = GmcTransStartExtResGetCloneId(startExtRes, NULL);
            AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        }

        if (userData1->lastError != NULL) {
            if (errMsg) {
                ret = strcmp(userData1->lastError, errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                printf("expect lastError: %s,  actual: null\n", userData1->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        userData1->recvNum++;
    }
}

void trans_start_ext_callback_NotCheck(void *userData, GmcStartExtResT *startExtRes, int32_t status, const char *errMsg)
{
    int ret;

    if (userData) {
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        userData1->status = status;
        userData1->historyRecvNum++;

        // 获取cloneId句柄
        if (startExtRes != NULL) {
            ret = GmcTransStartExtResGetCloneId(startExtRes, &userData1->cloneId);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (userData1->lastError != NULL) {
            if (errMsg) {
                ret = strcmp(userData1->lastError, errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                printf("expect lastError: %s,  actual: null\n", userData1->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        userData1->recvNum++;
    }
}

int TestYangSetField(GmcNodeT *node, GmcDataTypeE type, void *value, uint32_t size,
    const char *fieldName, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    int ret1 = 0;

    GmcPropValueT propValue;
    memcpy(propValue.propertyName, fieldName, (strlen(fieldName) + 1));
    propValue.type = type;
    propValue.value = value;
    propValue.size = size;
    ret = GmcYangSetNodeProperty(node, &propValue, opType);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_DEBUG, "SetProperty failed fieldName(%s)\n", fieldName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret1 = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);
        return ret;
    }

    return ret;
}

// 只有list类型需要设置主键
void TestYangSetNodeProperty_PK(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    uint32_t pkValue = i;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestYangSetNodeProperty(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    uint32_t value = i;

    uint32_t valueF0 = value;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = value;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF2 = value;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &valueF2, sizeof(uint32_t), "F2", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF3 = value;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &valueF3, sizeof(uint32_t), "F3", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = value;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestYangSetNodePropertyHalf(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    uint32_t value = i;

    uint32_t valueF0 = value;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = value;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F5写入和默认值不同的值
    uint32_t valueF5 = value;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &valueF5, sizeof(uint32_t), "F5", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F6写入和默认值相同的值
    uint32_t valueF6 = 666;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &valueF6, sizeof(uint32_t), "F6", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F7，F8，F9不写入值
}

void TestYangSetNodePropertyNum(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE opType, uint32_t cycleNum = 161)
{
    int ret = 0;
    uint32_t value = i;

    for (uint32_t j = 1; j <= cycleNum; j++) {
        char fieldName[10] = {0};
        (void)snprintf(fieldName, 10, "F%03d", j);

        // 写string数据
        uint32_t superSize = 65536;
        char *superValue = (char *)malloc(superSize);
        if (!superValue) {
            printf("malloc superValue failed.\n");
        }
        ASSERT_NE((void *)NULL, superValue);
        if (opType == GMC_YANG_PROPERTY_OPERATION_MERGE) {
            memset(superValue, 'B', (superSize - 1));
        } else {
            memset(superValue, 'A', (superSize - 1));
        }
        superValue[superSize - 1] = '\0';

        ret = TestYangSetField(node, GMC_DATATYPE_STRING, superValue, (superSize - 1), fieldName, opType);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        free(superValue);
    }
}

void TestYangSetNodePropertyNumDiff(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE opType, uint32_t cycleNum)
{
    int ret = 0;
    uint32_t value = i;

    for (uint32_t j = 1; j <= cycleNum; j++) {
        char fieldName[10] = {0};
        ret = snprintf(fieldName, 10, "F%04d", j);

        // 前一半数据写入值为i, 后一半数据写入值为2 * i
        if (j == (cycleNum / 2 + 1)) {
            value = 2 * i;
        }
        ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t), fieldName, opType);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

void TestSetKeyNameAndValue(GmcStmtT *stmt, uint32_t keyvalue)
{
    int ret;

    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &keyvalue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, g_keyName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// insert root
void TestInsertRoot(GmcConnT *conn, const char * vertexName, uint32_t keyvalue)
{
    int ret;
    GmcBatchT *batch = NULL;
    GmcStmtT *stmt_root = NULL;
    GmcNodeT *rootNode = NULL;

    ret = GmcAllocStmt(conn, &stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt_root, vertexName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_root, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    TestYangSetNodeProperty(rootNode, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(stmt_root);
}

void TestInsertRoot10(GmcConnT *conn, uint32_t keyvalue)
{
    int ret;
    GmcBatchT *batch = NULL;
    GmcStmtT *stmt_root = NULL;
    GmcNodeT *rootNode = NULL;

    for (uint32_t i = 1; i <= 10; i++) {
        char rootName[128] = {0};
        (void)snprintf(rootName, 128, "root_multi_%02d", i);

        ret = GmcAllocStmt(conn, &stmt_root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 启动事务
        ret = TestTransStartAsync(conn, g_mSTrxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置批处理batch参数
        ret = TestBatchPrepare(conn, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置根节点
        ret = testGmcPrepareStmtByLabelName(stmt_root, rootName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, stmt_root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_root, &rootNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        TestYangSetNodeProperty(rootNode, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 批处理提交
        AsyncUserDataT data = {0};
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 提交事务
        ret = TestTransCommitAsync(conn);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    GmcFreeStmt(stmt_root);
}

void TestInsertListBig(GmcConnT *conn)
{
    int ret;
    GmcBatchT *batch = NULL;
    GmcStmtT *stmt_root = NULL;
    GmcStmtT *stmt_child1 = NULL;
    GmcNodeT *rootNode = NULL;
    GmcNodeT *childNode[100] = {0};

    ret = GmcAllocStmt(conn, &stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_child1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepareBig(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt_root, "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_root, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    uint32_t fieldValue;
    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(stmt_child1, "list_3", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmt_root, stmt_child1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_child1, &childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodeProperty(childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodePropertyNum(childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, 150);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt_child1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(stmt_child1, "list_4", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmt_root, stmt_child1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_child1, &childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodeProperty(childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodePropertyNum(childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, 150);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt_child1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(stmt_root);
    GmcFreeStmt(stmt_child1);
}

void TestInsertListBig1(GmcConnT *conn)
{
    int ret;
    GmcBatchT *batch = NULL;
    GmcStmtT *stmt_root = NULL;
    GmcStmtT *stmt_child1 = NULL;
    GmcNodeT *rootNode = NULL;
    GmcNodeT *childNode[100] = {0};

    ret = GmcAllocStmt(conn, &stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_child1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepareBig(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt_root, "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_root, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    uint32_t fieldValue;
    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(stmt_child1, "list_3", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmt_root, stmt_child1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_child1, &childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    TestYangSetNodeProperty_PK(childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 1;
    TestYangSetNodeProperty(childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodePropertyNum(childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, 150);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt_child1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(stmt_child1, "list_4", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmt_root, stmt_child1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_child1, &childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    TestYangSetNodeProperty_PK(childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 1;
    TestYangSetNodeProperty(childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodePropertyNum(childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, 150);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt_child1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(stmt_root);
    GmcFreeStmt(stmt_child1);
}

void TestInsertData4(GmcConnT *conn)
{
    int ret;
    GmcBatchT *batch = NULL;
    GmcStmtT *stmt_root = NULL;
    GmcStmtT *stmt_child1 = NULL;
    GmcNodeT *rootNode = NULL;
    GmcNodeT *childNode[100] = {0};

    ret = GmcAllocStmt(conn, &stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_child1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt_root, "root_4", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_root, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    uint32_t fieldValue;
    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(stmt_child1, "list_41", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmt_root, stmt_child1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_child1, &childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodeProperty(childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt_child1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(stmt_root);
    GmcFreeStmt(stmt_child1);
}

void TestGmcSetVertexProperty_PK(GmcStmtT *stmt, uint32_t i)
{
    int ret = 0;
    uint32_t PK_value = i;
    ret = GmcSetVertexProperty(stmt, "PK", GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestGmcSetVertexProperty(GmcStmtT *stmt, uint32_t i)
{
    int ret = 0;
    uint32_t value = i;

    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestInsertVertex4(GmcStmtT *stmt, uint32_t times, uint32_t initValue, const char *labelname)
{
    int ret = 0;
    uint32_t i;
    uint32_t value;

    ret = testGmcPrepareStmtByLabelName(stmt, labelname, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert vertex
    for (i = 0; i < times; i++) {
        // 写数据
        value = initValue + i;

        TestGmcSetVertexProperty_PK(stmt, value);
        TestGmcSetVertexProperty(stmt, value);

        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

// remove root
void TestRemoveRoot(GmcConnT *conn, const char * vertexName)
{
    int ret;
    GmcBatchT *batch = NULL;
    GmcStmtT *stmt_root = NULL;

    ret = GmcAllocStmt(conn, &stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt_root, vertexName, GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(stmt_root);
}

void CreateSavepoint(GmcConnT *conn, const char *savepointname)
{
    int ret = 0;
    AsyncUserDataT userData = {0};

    ret = GmcTransCreateSavepointAsync(conn, savepointname, TransSavePointCb, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
}

void ReleaseSavepoint(GmcConnT *conn, const char *savepointname)
{
    int ret = 0;
    AsyncUserDataT userData = {0};

    ret = GmcTransReleaseSavepointAsync(conn, savepointname, TransSavePointCb, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
}

void RollbackSavepoint(GmcConnT *conn, const char *savepointname)
{
    int ret = 0;
    AsyncUserDataT userData = {0};

    ret = GmcTransRollBackSavepointAsync(conn, savepointname, TransSavePointCb, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
}

/******************************Subtree*******************************************/
// userData结构
struct subtreeFilterCbParam {
    int step;
    int32_t expectStatus;          // 预期的操作状态
    AsyncUserDataT *data;
    const char *expectReplyJson;  // 预期返回的subtree查询结果, json字符串
};
// userData ：用户数据 replyJson ：服务端返回的子树 json status ：服务器端操作处理结果  errMsg ：错误信息
void AsyncSubtreeFilterCb(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    int ret;
    subtreeFilterCbParam *param = (subtreeFilterCbParam *)(userData);
    param->expectStatus = status;
    if (GMERR_OK != status) {
        AW_FUN_Log(LOG_ERROR, "[err] status is %d  errMsg  is %s   \n ", status, errMsg);
        return;
    }
    bool isEnd = false;
    bool isEqual = false;
    uint32_t count = 0;
    const char **jsonReply = NULL;
    ret = GmcYangFetchJsonRetDeparse(fetchRet, &isEnd, &jsonReply, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(true, isEnd);
    AW_MACRO_EXPECT_EQ_INT(1, count);
    AW_MACRO_EXPECT_NOTNULL(jsonReply);
    printf("replyJson:\n%s\n", jsonReply[0]);

    if (param->expectReplyJson != NULL) {
        isEqual = testYangJsonIsEqual((const char*)jsonReply[0], param->expectReplyJson);
        if (isEqual == true) {
            AW_MACRO_EXPECT_EQ_INT(true, isEqual);
        } else {
            printf("expectJson:\n%s\n", param->expectReplyJson);
            AW_MACRO_EXPECT_EQ_INT(true, isEqual);
        }
    } else {
        AW_FUN_Log(LOG_ERROR, "[err] no replyjson   \n ");
    }
    param->step++;
    ((AsyncUserDataT *)(param->data))->recvNum = ((AsyncUserDataT *)(param->data))->recvNum + 1;
}

void TestSubtreeFilter(GmcStmtT *stmt, const char * rootName, const char * jsonName,
    GmcSubtreeWithDefaultModeE defaultMode = GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED)
{
    int ret;

    // subtree 查询
    char replyPath[1024] = {0};
    ret = snprintf(replyPath, 1024, "SubtreeReplyJson/%s_Reply.json", jsonName);
    AW_MACRO_EXPECT_NE_INT(0, ret);
    char *replyJson = NULL;
    readJanssonFile(replyPath, &replyJson);
    ASSERT_NE((void *)NULL, replyJson);

    subtreeFilterCbParam data = {0};
    data.expectReplyJson = replyJson;
    data.expectStatus = GMERR_OK;
    data.step = 0;

    char filterPath[1024] = {0};
    ret = snprintf(filterPath, 1024, "SubTreeFilterJson/Yang_071_Func.json");
    AW_MACRO_EXPECT_NE_INT(0, ret);
    char *filterJson = NULL;
    readJanssonFile(filterPath, &filterJson);
    ASSERT_NE((void *)NULL, filterJson);

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = rootName;
    filter.subtree.json = filterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = defaultMode;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    AsyncUserDataT asyncData = { 0 };
    data.data = &asyncData;
    ret = GmcYangSubtreeFilterExecuteAsync(stmt, &filters, NULL, AsyncSubtreeFilterCb, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(data.data, 1, 100000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.expectStatus);

    free(filterJson);
    free(replyJson);
    filterJson = NULL;
    replyJson = NULL;
}

/******************************Subtree obj模式*******************************************/
void TestSubtreeFilterObjAll(GmcStmtT *stmt, const char * rootName, const char * jsonName,
    GmcSubtreeWithDefaultModeE defaultMode = GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED)
{
    int ret;

    GmcNodeT *rootNode = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, rootName, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = NULL;
    filter.subtree.obj = rootNode;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = defaultMode;

    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_OBJ;
    filters.filter = &filter;

    char replyPath[1024] = {0};
    ret = snprintf(replyPath, 1024, "SubtreeReplyJson/%s_Reply.json", jsonName);
    AW_MACRO_EXPECT_NE_INT(0, ret);
    char *replyJson = NULL;
    readJanssonFile(replyPath, &replyJson);
    ASSERT_NE((void *)NULL, replyJson);
    std::vector<std::string> reply(1);
    reply[0] = replyJson;

    FetchRetCbParam param = {
        .step = 0,
        .stmt = stmt,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };

    char *noDataJson = NULL;
    readJanssonFile("SubtreeReplyJson/NoData.json", &noDataJson);
    ASSERT_NE((void *)NULL, noDataJson);
    bool equal = testYangJsonIsEqual(replyJson, noDataJson);
    if (!equal) {
        ret = GmcYangSubtreeFilterExecuteAsync(stmt, &filters, NULL, AsyncFetchRetCb, &param);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = GmcYangSubtreeFilterExecuteAsync(stmt, &filters, NULL, AsyncFetchRetCbNoData, &param);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testWaitAsyncSubtreeRecv_API(&param, 1, 100000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, param.expectStatus);
    free(replyJson);
    free(noDataJson);
    replyJson = NULL;
    noDataJson = NULL;
}

void TestSubtreeFilterObjAllMulti(GmcStmtT *stmt, const char * rootName, const char * jsonName, uint32_t jsonNum,
    GmcSubtreeWithDefaultModeE defaultMode = GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED)
{
    int ret;

    GmcNodeT *rootNode = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, rootName, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = NULL;
    filter.subtree.obj = rootNode;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = defaultMode;

    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_OBJ;
    filters.filter = &filter;

    char *replyJson[jsonNum] = {0};
    std::vector<std::string> reply(jsonNum);
    for (uint32_t i = 0; i < jsonNum; i++) {
        char replyPath[1024] = {0};
        ret = snprintf(replyPath, 1024, "SubtreeReplyJson/%s_%02d_Reply.json", jsonName, i);
        AW_MACRO_EXPECT_NE_INT(0, ret);

        readJanssonFile(replyPath, &replyJson[i]);
        ASSERT_NE((void *)NULL, replyJson[i]);

        // 依次填写预期json
        reply[i] = replyJson[i];
    }

    FetchRetCbParam param = {
        .step = 0,
        .stmt = stmt,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };

    ret = GmcYangSubtreeFilterExecuteAsync(stmt, &filters, NULL, AsyncFetchRetCbMulti, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param, 1, 100000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, param.expectStatus);

    for (uint32_t i = 0; i < jsonNum; i++) {
        free(replyJson[i]);
        replyJson[i] = NULL;
    }
}

void TestSubtreeFilterEncodeAll(GmcStmtT *stmt, const char * rootName, const char * jsonName,
    const char * nameSpace = "gmdb",
    GmcSubtreeWithDefaultModeE defaultMode = GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED,
    uint32_t depth = 0,
    GmcSubtreeWithConfigModeE configMode = GMC_SUBTREE_FILTER_DEFAULT)
{
    int ret;

    GmcNodeT *rootNode = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, rootName, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = NULL;
    filter.subtree.obj = rootNode;
    filter.jsonFlag =  GMC_JSON_INDENT(0);
    filter.defaultMode = defaultMode;
    filter.maxDepth = depth;
    filter.configFlag = configMode;

    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON_RFC7951;
    filters.filter = &filter;

    GmcSubtreeOptionT *option = NULL;
    ret = GmcYangCreateSubtreeOption(&option);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (nameSpace != NULL) {
        // nameSpace可以不设置
        ret = GmcYangSubtreeOptionSetJsonDefaultPrefix(option, nameSpace);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcYangSubtreeOptionSetFilter(option, &filters);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char replyPath[1024] = {0};
    ret = snprintf(replyPath, 1024, "SubtreeReplyJson/%s_Reply.json", jsonName);
    AW_MACRO_EXPECT_NE_INT(0, ret);
    char *replyJson = NULL;
    readJanssonFile(replyPath, &replyJson);
    ASSERT_NE((void *)NULL, replyJson);
    std::vector<std::string> reply(1);
    reply[0] = replyJson;

    FetchRetCbParam param = {
        .step = 0,
        .stmt = stmt,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };

    char *noDataJson = NULL;
    readJanssonFile("SubtreeReplyJson/NoData.json", &noDataJson);
    ASSERT_NE((void *)NULL, noDataJson);
    bool equal = testYangJsonIsEqual(replyJson, noDataJson);
    if (!equal) {
        ret = GmcYangSubtreeFilterExtExecuteAsync(stmt, option, NULL, AsyncFetchRetCbEncode, &param);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = GmcYangSubtreeFilterExtExecuteAsync(stmt, option, NULL, AsyncFetchRetCbNoData, &param);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testWaitAsyncSubtreeRecv_API(&param, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, param.expectStatus);

    GmcYangDestroySubtreeOption(option);
    free(replyJson);
    free(noDataJson);
    replyJson = NULL;
    noDataJson = NULL;
}

/******************************校验*******************************************/
struct ValidateParam {
    std::atomic_uint32_t *step;
    int32_t exceptStatus;  // 预期的操作状态
    GmcValidateResT validateRes;     // 预期返回的mandatory校验结果
    bool isValidateErrorPath;
    GmcErrorPathCodeE expectedErrCode;
    uint32_t expectedErrClauseIndex;
    const char *expectedErrMsg;
    const char *expectedErrPath;
    uint64_t startTime;
    bool printTime;
    bool printSize;
};

void AsyncValidateCb(void *userData, GmcValidateResT validateRes, int32_t status, const char *errMsg)
{
    ValidateParam *param = (ValidateParam *)userData;
    EXPECT_EQ(param->exceptStatus, status) << errMsg;
    // 只有返回无异常时才去校验mandatory
    if (GMERR_OK == status) {
        AW_MACRO_EXPECT_EQ_INT(validateRes.validateRes, param->validateRes.validateRes);
        AW_MACRO_EXPECT_EQ_INT(validateRes.failCount, param->validateRes.failCount);
    }

    if (param->isValidateErrorPath) {
        GmcErrorPathInfoT msg;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangGetErrorPathInfo(&msg));
        // 结果检查
        AW_MACRO_EXPECT_EQ_INT(param->expectedErrCode, msg.errorCode);
        if (msg.errorCode != GMC_VIOLATES_BUTT) {
            AW_MACRO_EXPECT_EQ_INT(param->expectedErrClauseIndex, msg.errorClauseIndex);
            AW_MACRO_EXPECT_EQ_STR(param->expectedErrMsg, msg.errorMsg);
            AW_MACRO_EXPECT_EQ_STR(param->expectedErrPath, msg.errorPath);
            ASSERT_NO_FATAL_FAILURE(GmcYangFreeErrorPathInfo());
        }
    }
    (*(param->step))++;
}

int testWaitValidateAsyncRecv(void *userData, int expRecvNum = 1, int timeout = -1, bool isAutoReset = true,
    int32_t epollFd = g_epollDataOneThread.userEpollFd)
{
    int waitCnt = 0;
    struct timeval start;
    struct timeval end;
    unsigned long duration;

    gettimeofday(&start, NULL);
    struct epoll_event events[MAX_EPOLL_EVENT_COUNT];
    ValidateParam *userDatas = (ValidateParam *)userData;
    int num = *(userDatas->step);
    if (num != 0) {
        printf("%d\n", num);
    }
    while (*(userDatas->step) != expRecvNum) {
        int fdCount = epoll_wait(epollFd, events, MAX_EPOLL_EVENT_COUNT, EPOLL_TIME_OUT_MS);
        if (fdCount < 0) {
            continue;
        }
        while (fdCount > 0) {
            --fdCount;
            if (g_runMode == 1) {
                GmcHandleEvent(events[fdCount].data.fd);
            } else {
                GmcHandleRWEvent(events[fdCount].data.fd, events[fdCount].events);
            }
        }
        if (timeout > 0 && waitCnt >= timeout) {
            gettimeofday(&end, NULL);
            duration = 1000000 * (end.tv_sec - start.tv_sec) + end.tv_usec - start.tv_usec;
            printf("[INFO] Recv Timeout %lf s, all OpNum : %d,\actually recived num : %d\n", (double)duration / 1000000,
                expRecvNum, userDatas->step);
            return -1;  // 接收超时
        }
    }
    if (isAutoReset) {
        userDatas->step = 0;
    }
    return 0;
}

// exceptRes 预期mandatory校验结果，exceptStatus 预期返回状态，apiSupport 校验接口返回状态
void WhenDataCheck(GmcStmtT *stmt, bool exceptRes, int32_t exceptStatus = GMERR_OK,
    uint32_t apiSupport = GMERR_OK, uint32_t checkType = GMC_YANG_VALIDATION_WHEN_FORCE)
{
    int ret = 0;
    bool isDataService = true;

    std::atomic_uint32_t step{0};
    GmcValidateResT validateRes {.validateRes = exceptRes};
    ValidateParam param = {.step = &step, .exceptStatus = exceptStatus, .validateRes = validateRes};
    GmcValidateConfigT cfg = {.type = checkType, .cfgJson = NULL};
    ret = GmcYangValidateAsync(stmt, &cfg, AsyncValidateCb, &param);
    AW_MACRO_EXPECT_EQ_INT(apiSupport, ret);

    ret = testWaitValidateAsyncRecv(&param);
    AW_MACRO_EXPECT_EQ_INT(exceptStatus, param.exceptStatus);
}

void ModelCheck(GmcStmtT *stmt)
{
    int ret = 0;

    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(stmt, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(true, checkData.validateRes);
    if (checkData.validateRes == false) {
        AW_FUN_Log(LOG_INFO, "GmcYangValidateModelAsync result is false, failcount is %d.", checkData.failCount);
    }
    memset(&checkData, 0, sizeof(YangValidateUserDataT));
}

void ModelCheckAndDataCheck(GmcConnT *conn, GmcStmtT *stmt)
{
    int ret;

    // 模型校验
    ModelCheck(stmt);

    // 启动事务
    ret = TestTransStartAsync(conn, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 数据校验
    WhenDataCheck(stmt, true, GMERR_OK, GMERR_OK, GMC_YANG_VALIDATION_ALL_FORCE);

    // 提交事务
    ret = TestTransCommitAsync(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/******************************Diff*******************************************/
static vector<string> expectDiff003 = {
    "root_1:update[(priKey(ID:1)),(priKey(ID:1))]\n"
    "root_1.F0:update(200,100)\n"
    "root_1.F1:update(200,100)\n"
    "root_1.F2:update(200,100)\n"
    "root_1.F3:update(200,100)\n"
    "root_1.F4:update(200,100)\n"
    "root_1.F5:update(200,100)\n"
    "root_1.con_1_1:remove\n"
    "con_1_1.F5:remove(555)\n"
    "con_1_1.F6:remove(666)\n"
    "con_1_1.F7:remove(777)\n"
    "con_1_1.F8:remove(888)\n"
    "con_1_1.F9:remove(999)\n"
    "con_1_1.F11:remove(default11)\n"
    "con_1_1.F12:remove(default12)\n"
    "con_1_1.F13:remove(default13)\n"
};

string GetValueString(GmcYangNodeValueT *value)
{
    switch (value->type) {
        case GMC_DATATYPE_STRING:
            return string("") + (const char *)value->value;
        case GMC_DATATYPE_CHAR:
        case GMC_DATATYPE_UCHAR:
            return "" + to_string(*(const char *)value->value);
            break;
        case GMC_DATATYPE_INT8:
        case GMC_DATATYPE_UINT8:
            return "" + to_string(*(const uint8_t *)value->value);
        case GMC_DATATYPE_INT16:
        case GMC_DATATYPE_UINT16:
            return "" + to_string(*(const uint16_t *)value->value);
        case GMC_DATATYPE_INT32:
        case GMC_DATATYPE_UINT32:
            return "" + to_string(*(const uint32_t *)value->value);
        case GMC_DATATYPE_UINT64:
        case GMC_DATATYPE_INT64:
        case GMC_DATATYPE_TIME:
            return "" + to_string(*(const uint64_t *)value->value);
        case GMC_DATATYPE_FLOAT:
            return "" + to_string(*(const float *)value->value);
        case GMC_DATATYPE_DOUBLE:
            return "" + to_string(*(const double *)value->value);
        case GMC_DATATYPE_NULL:
            return string("NULL");
        default:
            return string("NIL:") + to_string(value->type);
    }
}

string GetVertexString(GmcStmtT *stmt, GmcYangNodeT *info, bool isNewData)
{
    GmcYangNodeValueT *propValue = NULL;
    string res = "";
    int32_t ret;
    GmcDiffOpTypeE opType;
    EXPECT_EQ(GMERR_OK, GmcYangNodeGetDiffOpType(info, &opType));
    uint32_t propNum = 0;
    if ((isNewData && opType == GMC_DIFF_OP_CREATE) || (!isNewData && opType == GMC_DIFF_OP_REMOVE) ||
        opType == GMC_DIFF_OP_UPDATE) {
        ret = GmcYangNodeGetKeyPropNum(info, &propNum);
        if (ret == GMERR_FEATURE_NOT_SUPPORTED) {
            return "";
        }
        EXPECT_EQ(GMERR_OK, ret);
        res += "priKey(";
        for (unsigned int i = 0; i < propNum; i++) {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetKeyPropValue(info, i, &propValue));
            string propNameString = propValue->name;
            res += propNameString + ":" + GetValueString(propValue) + ",";
        }
        if (res[res.size() - 1] == ',') {
            res[res.size() - 1] = ')';
        } else {
            res += ")";
        }
    }
    bool isHasNewPrevNode = false;
    bool isHasOldPrevNode = false;
    ret = GmcYangNodeHasNewPrev(info, &isHasNewPrevNode);
    if (ret == GMERR_FEATURE_NOT_SUPPORTED) {
        return "";
    }
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangNodeHasOldPrev(info, &isHasOldPrevNode);
    if (ret == GMERR_FEATURE_NOT_SUPPORTED) {
        return "";
    }
    EXPECT_EQ(GMERR_OK, ret);
    if ((isNewData && isHasNewPrevNode) || (!isNewData && isHasOldPrevNode) || opType == GMC_DIFF_OP_UPDATE) {
        bool isExist = (isNewData ? isHasNewPrevNode : isHasOldPrevNode);
        if (!isExist) {
            if (res == "") {
                res = "NULL";
            }
            return res;
        }
        res += ", preKey(";
        ret = GmcYangNodeGetKeyPropNum(info, &propNum);
        if (ret != GMERR_OK) {
            if (res == "") {
                res = "NULL";
            }
            return res;
        }
        for (unsigned int i = 0; i < propNum; i++) {
            ret = isNewData ? GmcYangNodeGetNewPrevKey(info, i, &propValue) :
                              GmcYangNodeGetOldPrevKey(info, i, &propValue);
            if (ret == GMERR_INVALID_NAME) {
                continue;
            } else if (ret != GMERR_OK) {
                cout << "error:" << ret << ", " << propValue->name;
            }
            string propNameString = propValue->name;
            res += propNameString + ":" + GetValueString(propValue) + ",";
        }
        if (res[res.size() - 1] == ',') {
            res[res.size() - 1] = ')';
        } else {
            res += ")";
        }
    }
    if (res == "") {
        res = "NULL";
    }
    return res;
}

string GetOpTypeString(GmcDiffOpTypeE op)
{
    switch (op) {
        case GMC_DIFF_OP_CREATE:
            return "create";
        case GMC_DIFF_OP_REMOVE:
            return "remove";
        case GMC_DIFF_OP_UPDATE:
            return "update";
        default:
            return "invalid";
    }
}

void GetYangInfoString(GmcStmtT *stmt, GmcYangNodeT *info, string parentFix, string &res)
{
    res = parentFix + ":";
    GmcDiffOpTypeE opType;
    EXPECT_EQ(GMERR_OK, GmcYangNodeGetDiffOpType(info, &opType));
    res += GetOpTypeString((GmcDiffOpTypeE)opType);

    GmcYangNodeTypeE nodeType;
    EXPECT_EQ(GMERR_OK, GmcYangNodeGetType(info, &nodeType));
    if (nodeType == GMC_YANG_FIELD) {
        GmcYangNodeValueT *newValue = NULL;
        GmcYangNodeValueT *oldValue = NULL;
        if (opType == GMC_DIFF_OP_CREATE) {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetNewValue(info, &newValue));
            res += "(" + GetValueString(newValue) + ")";
        } else if (opType == GMC_DIFF_OP_REMOVE) {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetOldValue(info, &oldValue));
            res += "(" + GetValueString(oldValue) + ")";
        } else {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetNewValue(info, &newValue));
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetOldValue(info, &oldValue));
            res += "(" + GetValueString(newValue) + "," + GetValueString(oldValue) + ")";
        }
    } else {
        if (strcmp(GetVertexString(stmt, info, true).c_str(), "") != 0) {
            res += "[(" + GetVertexString(stmt, info, true) + "),";
        }
        if (strcmp(GetVertexString(stmt, info, false).c_str(), "") != 0) {
            res += "(" + GetVertexString(stmt, info, false) + ")]";
        }
    }
}

// 深度遍历生成diff信息字符串
void DFSYangNode(GmcStmtT *stmt, GmcYangNodeT *parent, string prefix, string &resStr)
{
    GmcYangNodeT *child = NULL;
    GmcYangNodeT *prevChild = NULL;
    string res = "";
    string diffStr;
    do {
        ASSERT_EQ(GMERR_OK, GmcYangNodeGetNext(parent, prevChild, &child));
        prevChild = child;
        if (child != NULL) {
            // 打印diff信息
            const char *nodeName;
            ASSERT_EQ(GMERR_OK, GmcYangNodeGetName(child, &nodeName));
            string childName = prefix + nodeName;
            ASSERT_NO_FATAL_FAILURE(GetYangInfoString(stmt, child, childName, diffStr));
            res += diffStr + "\n";
            string childStr;
            DFSYangNode(stmt, child, nodeName + string("."), childStr);
            res += childStr;
        }
    } while (child != NULL);
    resStr += res;
}

// 比较s1 s2两个字符串，如果字符串相同，返回空串，不同返回从不同位置开始的子串
string StrCmp(string &s1, string &s2)
{
    int i = 0;
    for (; s1[i] != 0 && s2[i] != 0; i++) {
        if (s1[i] != s2[i]) {
            return s1.substr(i);
        }
    }

    if (s1[i] == 0 && s2[i] == 0) {
        return string("");
    } else if (s2[i] == 0) {
        return s1.substr(i);
    } else {
        return s2.substr(i);
    }
}

void TestCheckYangTree(GmcStmtT *stmt, const GmcYangTreeT **yangTrees, uint32_t count, vector<string> &expectReply)
{
    ASSERT_EQ(expectReply.size(), count);
    GmcYangNodeT *rootInfo = NULL;
    for (uint32_t i = 0; i < count; i++) {
        ASSERT_EQ(GMERR_OK, GmcYangGetRootNode(yangTrees[i], &rootInfo));
        string res;
        const char *rootName;
        ASSERT_EQ(GMERR_OK, GmcYangNodeGetName(rootInfo, &rootName));
        ASSERT_NO_FATAL_FAILURE(GetYangInfoString(stmt, rootInfo, rootName + string(""), res));
        res += "\n";
        ASSERT_NO_FATAL_FAILURE(DFSYangNode(stmt, rootInfo, rootName + string("."), res));
        char fileName[128] = {0};
        int ret = sprintf_s(fileName, sizeof(fileName), "perf_%dDiffTreeInfo", i);
        if (ret == -1) {
            AW_FUN_Log(LOG_INFO, "sprintf_s error\n");
            return;
        }
        FILE *fp = fopen(fileName, "w");
        if (fp == NULL) {
            AW_FUN_Log(LOG_INFO, "fopen error\n");
            return;
        }
        ret = fputs(res.c_str(), fp);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "fputs error\n");
            return;
        }
        ret = fclose(fp);
        if (ret == -1) {
            AW_FUN_Log(LOG_INFO, "fclose error\n");
            return;
        }
        if (g_printFlag) {
            cout << "actual diff: \n" << res;
        }
        ASSERT_STREQ(StrCmp(expectReply[i], res).c_str(), "") << i;
        ASSERT_EQ(GMERR_OK, GmcYangFreeTree(yangTrees[i]));
    }
}

void FetchDiff_callback(
    void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    if (userData) {
        bool isEnd = false;
        uint32_t count = 0;
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        userData1->historyRecvNum++;
        userData1->status = status;
        if (userData1->lastError != NULL) {
            int ret = strcmp(userData1->lastError, errMsg);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (status == GMERR_OK) {
            const GmcYangTreeT **yangTree = NULL;
            ASSERT_EQ(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));
            ASSERT_EQ((uint32_t)(*userData1->expectDiff).size(), count);
            ASSERT_TRUE(isEnd);
            TestCheckYangTree(userData1->stmt, yangTree, count, *userData1->expectDiff);
            GmcYangFreeFetchRet(fetchRet);
        }
        userData1->recvNum++;
    }
}
void TestFetchAndDeparseDiff(GmcStmtT *stmt, GmcBatchT *batch, vector<string> &expectDiff, AsyncUserDataT data,
    int rets = GMERR_OK)
{
    data.stmt = stmt;
    data.expectDiff = &expectDiff;
    int ret = GmcYangFetchDiffExecuteAsync(stmt, NULL, FetchDiff_callback, &data);
    if (ret != GMERR_OK) {
        testGmcGetLastError(NULL);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(rets, data.status);
}

void TestCheckYangTreeHard(GmcStmtT *stmt, const GmcYangTreeT **yangTrees, uint32_t count, vector<string> &expectReply)
{
    ASSERT_EQ(expectReply.size(), count);
    GmcYangNodeT *rootInfo = NULL;
    for (uint32_t i = 0; i < count; i++) {
        ASSERT_EQ(GMERR_OK, GmcYangGetRootNode(yangTrees[i], &rootInfo));
        string res;
        const char *rootName;
        ASSERT_EQ(GMERR_OK, GmcYangNodeGetName(rootInfo, &rootName));
        ASSERT_NO_FATAL_FAILURE(GetYangInfoString(stmt, rootInfo, rootName + string(""), res));
        res += "\n";
        ASSERT_NO_FATAL_FAILURE(DFSYangNode(stmt, rootInfo, rootName + string("."), res));
        char fileName[128] = {0};
        int ret = sprintf_s(fileName, sizeof(fileName), "perf_%dDiffTreeInfo", i);
        if (ret == -1) {
            AW_FUN_Log(LOG_INFO, "sprintf_s error\n");
            return;
        }
        FILE *fp = fopen(fileName, "w");
        if (fp == NULL) {
            AW_FUN_Log(LOG_INFO, "fopen error\n");
            return;
        }
        ret = fputs(res.c_str(), fp);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "fputs error\n");
            return;
        }
        ret = fclose(fp);
        if (ret == -1) {
            AW_FUN_Log(LOG_INFO, "fclose error\n");
            return;
        }
        if (g_printFlag) {
            cout << ">>>>>>>>> diff[" << i << "]\n";
            cout << "actual diff: \n" << res;
        }
        ASSERT_EQ(GMERR_OK, GmcYangFreeTree(yangTrees[i]));
    }
}

void FetchDiffHard_callback(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    if (userData) {
        bool isEnd = false;
        uint32_t count = 0;
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        userData1->historyRecvNum++;
        userData1->status = status;
        if (userData1->lastError != NULL) {
            int ret = strcmp(userData1->lastError, errMsg);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (status == GMERR_OK) {
            const GmcYangTreeT **yangTree = NULL;
            ASSERT_EQ(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));
            ASSERT_EQ((uint32_t)(*userData1->expectDiff).size(), count);
            ASSERT_TRUE(isEnd);
            TestCheckYangTreeHard(userData1->stmt, yangTree, count, *userData1->expectDiff);
            GmcYangFreeFetchRet(fetchRet);
        }
        userData1->recvNum++;
    }
}

void TestFetchAndDeparseDiffHard(GmcStmtT *stmt, vector<string> &expectDiff, AsyncUserDataT data,
    int rets = GMERR_OK)
{
    int ret;
    data.stmt = stmt;
    data.expectDiff = &expectDiff;
    ret = GmcYangFetchDiffExecuteAsync(stmt, NULL, FetchDiffHard_callback, &data);
    if (ret != GMERR_OK) {
        testGmcGetLastError(NULL);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(rets, data.status);
}

void TestFetchAndDeparseDiffHardExt(GmcStmtT *stmt, vector<string> &expectDiff, AsyncUserDataT data,
    int rets = GMERR_OK, GmcFetchDiffModeE diffMode = GMC_FETCH_DIFF_EXPLICIT)
{
    int ret;
    data.stmt = stmt;
    data.expectDiff = &expectDiff;

    // 设置diff查询模式
    GmcFetchDiffOptionT *option = NULL;
    ret = GmcYangDiffFetchOptionCreate(&option);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangDiffFetchOptionSetMode(option, diffMode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangFetchDiffExtExecuteAsync(stmt, NULL, option, FetchDiffHard_callback, &data);
    if (ret != GMERR_OK) {
        testGmcGetLastError(NULL);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(rets, data.status);
}

void TestCheckYangTreeSn(GmcStmtT *stmt, const GmcYangTreeT **yangTrees, uint32_t count, vector<string> &expectReply)
{
    ASSERT_EQ(expectReply.size(), count);
    GmcYangNodeT *rootInfo = NULL;
    for (uint32_t i = 0; i < count; i++) {
        ASSERT_EQ(GMERR_OK, GmcYangGetRootNode(yangTrees[i], &rootInfo));
        string res;
        const char *rootName;
        ASSERT_EQ(GMERR_OK, GmcYangNodeGetName(rootInfo, &rootName));
        ASSERT_NO_FATAL_FAILURE(GetYangInfoString(stmt, rootInfo, rootName + string(""), res));
        res += "\n";
        ASSERT_NO_FATAL_FAILURE(DFSYangNode(stmt, rootInfo, rootName + string("."), res));
        char fileName[128] = {0};
        int ret = sprintf_s(fileName, sizeof(fileName), "sn_%dDiffTreeInfo", i);
        if (ret == -1) {
            AW_FUN_Log(LOG_INFO, "sprintf_s error\n");
            return;
        }
        FILE *fp = fopen(fileName, "w");
        if (fp == NULL) {
            AW_FUN_Log(LOG_INFO, "fopen error\n");
            return;
        }
        ret = fputs(res.c_str(), fp);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "fputs error\n");
            return;
        }
        ret = fclose(fp);
        if (ret == -1) {
            AW_FUN_Log(LOG_INFO, "fclose error\n");
            return;
        }
        if (g_printFlag) {
            cout << ">>>" << i << "\n";
            cout << "actual_sn diff: \n" << res;
        }
        ASSERT_STREQ(StrCmp(expectReply[i], res).c_str(), "") << i;
        ASSERT_EQ(GMERR_OK, GmcYangFreeTree(yangTrees[i]));
    }
}

void YangSnCallBack(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    printf(">>> eventType:%d, msgType:%" PRIu16 " labelCount:%" PRIu16 " serialNumber:%" PRIu64
        " subscriptionName:%s connectionName:%s. \n", info->eventType, info->msgType, info->labelCount,
        info->serialNumber, info->subscriptionName, info->connectionName);

    if (userData) {
        SnUserDataT *data = (SnUserDataT *)userData;
        ASSERT_NE((void *)NULL, data);

        ASSERT_STREQ(data->subscriptionName, info->subscriptionName);
        ASSERT_STREQ(data->connectionName, info->connectionName);
        ASSERT_EQ(info->eventType, GMC_SUB_EVENT_DIFF);
        ASSERT_EQ(info->msgType, 0);
        ASSERT_EQ(info->labelCount, 1);

        GmcFetchRetT *fetchRet = NULL;
        ASSERT_EQ(GMERR_OK, GmcFetchSubsDiffTree(subStmt, &fetchRet));
        if (data->isFetchNull) {
            ASSERT_TRUE(fetchRet == NULL);
            data->diffNum++;
            return;
        }

        ASSERT_TRUE(fetchRet != NULL);

        // 开始解析本次返回的diff数据
        bool isEnd = false;
        uint32_t count = 0;
        const GmcYangTreeT **yangTree = NULL;
        ASSERT_EQ(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));
        ASSERT_EQ(count, 1);
        ASSERT_EQ(isEnd, data->isEnd);
        ASSERT_TRUE(yangTree != NULL);
        TestCheckYangTreeSn(subStmt, yangTree, count, *data->expectDiff);
        GmcYangFreeFetchRet(fetchRet);
        data->diffNum++;
    }
}

#endif /* CLONE_TRANS_H */
