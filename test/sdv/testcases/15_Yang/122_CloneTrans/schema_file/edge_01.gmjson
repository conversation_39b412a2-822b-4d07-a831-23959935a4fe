[{"name": "root_1_to_list_1", "source_vertex_label": "root_1", "dest_vertex_label": "list_1", "source_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_1_to_list_2", "source_vertex_label": "root_1", "dest_vertex_label": "list_2", "source_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_1_to_list_3", "source_vertex_label": "root_1", "dest_vertex_label": "list_3", "source_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_1_to_list_4", "source_vertex_label": "root_1", "dest_vertex_label": "list_4", "source_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}]