[{"name": "root_multi_01_to_list_1", "source_vertex_label": "root_multi_01", "dest_vertex_label": "list_multi_01_01", "source_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_multi_01_to_list_2", "source_vertex_label": "root_multi_01", "dest_vertex_label": "list_multi_01_02", "source_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_multi_02_to_list_1", "source_vertex_label": "root_multi_02", "dest_vertex_label": "list_multi_02_01", "source_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_multi_02_to_list_2", "source_vertex_label": "root_multi_02", "dest_vertex_label": "list_multi_02_02", "source_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_multi_03_to_list_1", "source_vertex_label": "root_multi_03", "dest_vertex_label": "list_multi_03_01", "source_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_multi_03_to_list_2", "source_vertex_label": "root_multi_03", "dest_vertex_label": "list_multi_03_02", "source_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_multi_04_to_list_1", "source_vertex_label": "root_multi_04", "dest_vertex_label": "list_multi_04_01", "source_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_multi_04_to_list_2", "source_vertex_label": "root_multi_04", "dest_vertex_label": "list_multi_04_02", "source_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_multi_05_to_list_1", "source_vertex_label": "root_multi_05", "dest_vertex_label": "list_multi_05_01", "source_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_multi_05_to_list_2", "source_vertex_label": "root_multi_05", "dest_vertex_label": "list_multi_05_02", "source_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_multi_06_to_list_1", "source_vertex_label": "root_multi_06", "dest_vertex_label": "list_multi_06_01", "source_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_multi_06_to_list_2", "source_vertex_label": "root_multi_06", "dest_vertex_label": "list_multi_06_02", "source_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_multi_07_to_list_1", "source_vertex_label": "root_multi_07", "dest_vertex_label": "list_multi_07_01", "source_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_multi_07_to_list_2", "source_vertex_label": "root_multi_07", "dest_vertex_label": "list_multi_07_02", "source_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_multi_08_to_list_1", "source_vertex_label": "root_multi_08", "dest_vertex_label": "list_multi_08_01", "source_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_multi_08_to_list_2", "source_vertex_label": "root_multi_08", "dest_vertex_label": "list_multi_08_02", "source_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_multi_09_to_list_1", "source_vertex_label": "root_multi_09", "dest_vertex_label": "list_multi_09_01", "source_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_multi_09_to_list_2", "source_vertex_label": "root_multi_09", "dest_vertex_label": "list_multi_09_02", "source_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_multi_10_to_list_1", "source_vertex_label": "root_multi_10", "dest_vertex_label": "list_multi_10_01", "source_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_multi_10_to_list_2", "source_vertex_label": "root_multi_10", "dest_vertex_label": "list_multi_10_02", "source_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}]