[{"name": "ContainerList", "source_vertex_label": "ContainerOne", "dest_vertex_label": "ListOne", "source_node_path": "/ContainerTwo", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ContainerLeafList1", "source_vertex_label": "ContainerOne", "dest_vertex_label": "LeafList1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ContainerLeafList2", "source_vertex_label": "ContainerOne", "dest_vertex_label": "LeafList2", "source_node_path": "/ContainerTwo", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ListListTwo", "source_vertex_label": "ListOne", "dest_vertex_label": "ListTwo", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}]