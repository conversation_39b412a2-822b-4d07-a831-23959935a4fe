/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */
#include "RCA_COMMON.h"
#include "yangSubFEx.h"
#include "aliasTool.h"
#include "yangSubTool.h"

class yang_sub_all_type_field : public testing::Test {
public:
    SnUserDataT *newSubData;
    SnUserDataT *oldSubData;
    virtual void SetUp();
    virtual void TearDown();

    SnUserDataT *user_data;
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
    void clearSubUserData();
};

void yang_sub_all_type_field::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("mkdir -p /data/gmdb/; rm -rf /data/gmdb/*");
#ifndef FEATURE_CLT_SERVER_SAME_PROCESS
    system("sh $TEST_HOME/tools/start.sh -f");
#endif
    int ret = testEnvInit();
    ASSERT_EQ(GMERR_OK, ret);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void yang_sub_all_type_field::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}

void yang_sub_all_type_field::SetUp()
{
    int ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    user_data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(user_data, 0, sizeof(SnUserDataT));

    user_data->subscriptionName = (char *)malloc(sizeof(char) * 128);
    memset(user_data->subscriptionName, 0, sizeof(char) * 128);

    user_data->connectionName = (char *)malloc(sizeof(char) * 128);
    memset(user_data->connectionName, 0, sizeof(char) * 128);

    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务
    const char *namespaceUserName = "abc";

    TryDropNsp(g_spaceName);
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = g_spaceName;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观
    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    ret = GmcUseNamespaceAsync(g_stmt_async, g_spaceName, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcAllocStmt(g_conn_async, &g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建同步连接
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(g_stmtSync, g_spaceName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建订阅连接
    int chanRingLen = 256;
    const char *newSubConnName = "yangSubConn";
    ret = testSubConnect(&g_connSub, &g_stmtSub, 1, g_epoll_reg_info, newSubConnName, &chanRingLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(user_data->connectionName, 128, "%s", newSubConnName);

    // 创建同步连接
    ret = testGmcConnect(&g_connSync2, &g_stmtSync2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN();
}

void yang_sub_all_type_field::clearSubUserData()
{
    user_data->diffNum = 0;
    user_data->isFetchNull = false;
}

void yang_sub_all_type_field::TearDown()
{
    TryDropNsp(g_spaceName);

    // 释放all stmt
    TestYangFreeAllstmt();

    int ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connSync2, g_stmtSync2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(g_connSub, g_stmtSub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    if (user_data->subscriptionName) {
        free(user_data->subscriptionName);
        user_data->subscriptionName = NULL;
    }
    if (user_data->connectionName) {
        free(user_data->connectionName);
        user_data->connectionName = NULL;
    }
    if (user_data) {
        free(user_data);
        user_data = NULL;
    }
}

// 001.uint32类型触发yang订阅推送
TEST_F(yang_sub_all_type_field, Yang_099_yang_sub_all_type_field_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexLabelAsync(g_stmt_async, "schemaFunc/AllFieldTypeVertex.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmt_async, "schemaFunc/AllFieldTypeEdge.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ModelCheck();

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"all_field_type_vertex_label",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "list_1[F1 = 1]/F1",
                "states": [{"type": "create"}]
            }
        ],
        "events":[
            {"type": "diff"}
        ]
    })";

    GmcSubConfigT newSubInfo;
    newSubInfo.subsName = "yangSub";
    newSubInfo.configJson = newSubJson;
    ret = GmcSubscribe(g_stmtSync, &newSubInfo, g_connSub, YangSnCallBack, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(user_data->subscriptionName, 128, "%s", newSubInfo.subsName);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtRoot, "all_field_type_vertex_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtRoot, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置rootF0
    uint32_t rootF0 = 1;
    ret = SetNodeProperty(g_rootNode, GMC_DATATYPE_UINT32, &rootF0, sizeof(uint32_t),
                            "root_F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < 5; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtList, "list_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList, &g_listNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t listF1 = i;
        ret = SetNodeProperty(g_listNode, GMC_DATATYPE_UINT32, &listF1, sizeof(uint32_t),
                            "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(6, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(6, data.succNum);

    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    SetDiffFile("diffFEx/diffSn001.json", NULL, user_data);
    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 获取diff
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ReleaseDiffFile();

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    AW_FUN_Log(LOG_STEP, "END");
}

// 002.char类型触发yang订阅推送
TEST_F(yang_sub_all_type_field, Yang_099_yang_sub_all_type_field_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexLabelAsync(g_stmt_async, "schemaFunc/AllFieldTypeVertex.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmt_async, "schemaFunc/AllFieldTypeEdge.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ModelCheck();

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"all_field_type_vertex_label",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "list_1[F2='b']/F2",
                "states": [{"type": "create"}]
            }
        ],
        "events":[
            {"type": "diff"}
        ]
    })";

    GmcSubConfigT newSubInfo;
    newSubInfo.subsName = "yangSub";
    newSubInfo.configJson = newSubJson;
    ret = GmcSubscribe(g_stmtSync, &newSubInfo, g_connSub, YangSnCallBack, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(user_data->subscriptionName, 128, "%s", newSubInfo.subsName);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtRoot, "all_field_type_vertex_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtRoot, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置rootF0
    uint32_t rootF0 = 1;
    ret = SetNodeProperty(g_rootNode, GMC_DATATYPE_UINT32, &rootF0, sizeof(uint32_t),
                            "root_F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char snF[5] = {'a', 'b', 'c', 'd', 'e'};
    for (int i = 0; i < 5; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtList, "list_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList, &g_listNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置主键字段的值
        uint32_t listF1 = i;
        ret = SetNodeProperty(g_listNode, GMC_DATATYPE_UINT32, &listF1, sizeof(uint32_t),
                              "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        ret = SetNodeProperty(g_listNode, GMC_DATATYPE_CHAR, &snF[i], sizeof(char),
                              "F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(6, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(6, data.succNum);

    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    SetDiffFile("diffFEx/diffSn002.json", NULL, user_data);
    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 获取diff
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ReleaseDiffFile();

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    AW_FUN_Log(LOG_STEP, "END");
}

// 003.uchar类型触发yang订阅推送
TEST_F(yang_sub_all_type_field, Yang_099_yang_sub_all_type_field_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexLabelAsync(g_stmt_async, "schemaFunc/AllFieldTypeVertex.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmt_async, "schemaFunc/AllFieldTypeEdge.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ModelCheck();

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"all_field_type_vertex_label",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "list_1[F3='b']/F3",
                "states": [{"type": "create"}]
            }
        ],
        "events":[
            {"type": "diff"}
        ]
    })";

    GmcSubConfigT newSubInfo;
    newSubInfo.subsName = "yangSub";
    newSubInfo.configJson = newSubJson;
    ret = GmcSubscribe(g_stmtSync, &newSubInfo, g_connSub, YangSnCallBack, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(user_data->subscriptionName, 128, "%s", newSubInfo.subsName);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtRoot, "all_field_type_vertex_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtRoot, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置rootF0
    uint32_t rootF0 = 1;
    ret = SetNodeProperty(g_rootNode, GMC_DATATYPE_UINT32, &rootF0, sizeof(uint32_t),
                            "root_F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    unsigned char snF[5] = {'a', 'b', 'c', 'd', 'e'};
    for (int i = 0; i < 5; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtList, "list_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList, &g_listNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置主键字段的值
        uint32_t listF1 = i;
        ret = SetNodeProperty(g_listNode, GMC_DATATYPE_UINT32, &listF1, sizeof(uint32_t),
                              "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        ret = SetNodeProperty(g_listNode, GMC_DATATYPE_UCHAR, &snF[i], sizeof(unsigned char),
                              "F3", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(6, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(6, data.succNum);

    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    SetDiffFile("diffFEx/diffSn003.json", NULL, user_data);
    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 获取diff
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ReleaseDiffFile();

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    AW_FUN_Log(LOG_STEP, "END");
}

// 004.int8类型触发yang订阅推送
TEST_F(yang_sub_all_type_field, Yang_099_yang_sub_all_type_field_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexLabelAsync(g_stmt_async, "schemaFunc/AllFieldTypeVertex.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmt_async, "schemaFunc/AllFieldTypeEdge.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ModelCheck();

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"all_field_type_vertex_label",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "list_1[F4=1]/F4",
                "states": [{"type": "create"}]
            }
        ],
        "events":[
            {"type": "diff"}
        ]
    })";

    GmcSubConfigT newSubInfo;
    newSubInfo.subsName = "yangSub";
    newSubInfo.configJson = newSubJson;
    ret = GmcSubscribe(g_stmtSync, &newSubInfo, g_connSub, YangSnCallBack, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(user_data->subscriptionName, 128, "%s", newSubInfo.subsName);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtRoot, "all_field_type_vertex_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtRoot, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置rootF0
    uint32_t rootF0 = 1;
    ret = SetNodeProperty(g_rootNode, GMC_DATATYPE_UINT32, &rootF0, sizeof(uint32_t),
                            "root_F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < 5; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtList, "list_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList, &g_listNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置主键字段的值
        uint32_t listF1 = i;
        ret = SetNodeProperty(g_listNode, GMC_DATATYPE_UINT32, &listF1, sizeof(uint32_t),
                              "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        int8_t snF = i;
        ret = SetNodeProperty(g_listNode, GMC_DATATYPE_INT8, &snF, sizeof(int8_t),
                              "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(6, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(6, data.succNum);

    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    SetDiffFile("diffFEx/diffSn004.json", NULL, user_data);
    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 获取diff
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ReleaseDiffFile();

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    AW_FUN_Log(LOG_STEP, "END");
}

// 005.uint8类型触发yang订阅推送
TEST_F(yang_sub_all_type_field, Yang_099_yang_sub_all_type_field_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexLabelAsync(g_stmt_async, "schemaFunc/AllFieldTypeVertex.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmt_async, "schemaFunc/AllFieldTypeEdge.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ModelCheck();

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"all_field_type_vertex_label",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "list_1[F5=1]/F5",
                "states": [{"type": "create"}]
            }
        ],
        "events":[
            {"type": "diff"}
        ]
    })";

    GmcSubConfigT newSubInfo;
    newSubInfo.subsName = "yangSub";
    newSubInfo.configJson = newSubJson;
    ret = GmcSubscribe(g_stmtSync, &newSubInfo, g_connSub, YangSnCallBack, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(user_data->subscriptionName, 128, "%s", newSubInfo.subsName);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtRoot, "all_field_type_vertex_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtRoot, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置rootF0
    uint32_t rootF0 = 1;
    ret = SetNodeProperty(g_rootNode, GMC_DATATYPE_UINT32, &rootF0, sizeof(uint32_t),
                            "root_F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < 5; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtList, "list_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList, &g_listNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置主键字段的值
        uint32_t listF1 = i;
        ret = SetNodeProperty(g_listNode, GMC_DATATYPE_UINT32, &listF1, sizeof(uint32_t),
                              "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint8_t snF = i;
        ret = SetNodeProperty(g_listNode, GMC_DATATYPE_UINT8, &snF, sizeof(uint8_t),
                              "F5", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(6, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(6, data.succNum);

    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    SetDiffFile("diffFEx/diffSn005.json", NULL, user_data);
    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 获取diff
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ReleaseDiffFile();

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    AW_FUN_Log(LOG_STEP, "END");
}

// 006.int16类型触发yang订阅推送
TEST_F(yang_sub_all_type_field, Yang_099_yang_sub_all_type_field_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexLabelAsync(g_stmt_async, "schemaFunc/AllFieldTypeVertex.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmt_async, "schemaFunc/AllFieldTypeEdge.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ModelCheck();

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"all_field_type_vertex_label",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "list_1[F6=1]/F6",
                "states": [{"type": "create"}]
            }
        ],
        "events":[
            {"type": "diff"}
        ]
    })";

    GmcSubConfigT newSubInfo;
    newSubInfo.subsName = "yangSub";
    newSubInfo.configJson = newSubJson;
    ret = GmcSubscribe(g_stmtSync, &newSubInfo, g_connSub, YangSnCallBack, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(user_data->subscriptionName, 128, "%s", newSubInfo.subsName);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtRoot, "all_field_type_vertex_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtRoot, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置rootF0
    uint32_t rootF0 = 1;
    ret = SetNodeProperty(g_rootNode, GMC_DATATYPE_UINT32, &rootF0, sizeof(uint32_t),
                            "root_F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < 5; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtList, "list_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList, &g_listNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置主键字段的值
        uint32_t listF1 = i;
        ret = SetNodeProperty(g_listNode, GMC_DATATYPE_UINT32, &listF1, sizeof(uint32_t),
                              "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        int16_t snF = i;
        ret = SetNodeProperty(g_listNode, GMC_DATATYPE_INT16, &snF, sizeof(int16_t),
                              "F6", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(6, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(6, data.succNum);

    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    SetDiffFile("diffFEx/diffSn006.json", NULL, user_data);
    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 获取diff
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ReleaseDiffFile();

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    AW_FUN_Log(LOG_STEP, "END");
}

// 007.uint16类型触发yang订阅推送
TEST_F(yang_sub_all_type_field, Yang_099_yang_sub_all_type_field_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexLabelAsync(g_stmt_async, "schemaFunc/AllFieldTypeVertex.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmt_async, "schemaFunc/AllFieldTypeEdge.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ModelCheck();

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"all_field_type_vertex_label",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "list_1[F7=1]/F7",
                "states": [{"type": "create"}]
            }
        ],
        "events":[
            {"type": "diff"}
        ]
    })";

    GmcSubConfigT newSubInfo;
    newSubInfo.subsName = "yangSub";
    newSubInfo.configJson = newSubJson;
    ret = GmcSubscribe(g_stmtSync, &newSubInfo, g_connSub, YangSnCallBack, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(user_data->subscriptionName, 128, "%s", newSubInfo.subsName);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtRoot, "all_field_type_vertex_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtRoot, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置rootF0
    uint32_t rootF0 = 1;
    ret = SetNodeProperty(g_rootNode, GMC_DATATYPE_UINT32, &rootF0, sizeof(uint32_t),
                            "root_F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < 5; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtList, "list_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList, &g_listNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置主键字段的值
        uint32_t listF1 = i;
        ret = SetNodeProperty(g_listNode, GMC_DATATYPE_UINT32, &listF1, sizeof(uint32_t),
                              "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint16_t snF = i;
        ret = SetNodeProperty(g_listNode, GMC_DATATYPE_UINT16, &snF, sizeof(uint16_t),
                              "F7", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(6, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(6, data.succNum);

    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    SetDiffFile("diffFEx/diffSn007.json", NULL, user_data);
    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 获取diff
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ReleaseDiffFile();

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    AW_FUN_Log(LOG_STEP, "END");
}

// 008.int32类型触发yang订阅推送
TEST_F(yang_sub_all_type_field, Yang_099_yang_sub_all_type_field_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexLabelAsync(g_stmt_async, "schemaFunc/AllFieldTypeVertex.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmt_async, "schemaFunc/AllFieldTypeEdge.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ModelCheck();

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"all_field_type_vertex_label",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "list_1[F8=1]/F8",
                "states": [{"type": "create"}]
            }
        ],
        "events":[
            {"type": "diff"}
        ]
    })";

    GmcSubConfigT newSubInfo;
    newSubInfo.subsName = "yangSub";
    newSubInfo.configJson = newSubJson;
    ret = GmcSubscribe(g_stmtSync, &newSubInfo, g_connSub, YangSnCallBack, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(user_data->subscriptionName, 128, "%s", newSubInfo.subsName);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtRoot, "all_field_type_vertex_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtRoot, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置rootF0
    uint32_t rootF0 = 1;
    ret = SetNodeProperty(g_rootNode, GMC_DATATYPE_UINT32, &rootF0, sizeof(uint32_t),
                            "root_F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < 5; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtList, "list_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList, &g_listNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置主键字段的值
        uint32_t listF1 = i;
        ret = SetNodeProperty(g_listNode, GMC_DATATYPE_UINT32, &listF1, sizeof(uint32_t),
                              "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        int32_t snF = i;
        ret = SetNodeProperty(g_listNode, GMC_DATATYPE_INT32, &snF, sizeof(int32_t),
                              "F8", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(6, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(6, data.succNum);

    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    SetDiffFile("diffFEx/diffSn008.json", NULL, user_data);
    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 获取diff
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ReleaseDiffFile();

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    AW_FUN_Log(LOG_STEP, "END");
}

// 009.int64类型触发yang订阅推送
TEST_F(yang_sub_all_type_field, Yang_099_yang_sub_all_type_field_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexLabelAsync(g_stmt_async, "schemaFunc/AllFieldTypeVertex.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmt_async, "schemaFunc/AllFieldTypeEdge.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ModelCheck();

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"all_field_type_vertex_label",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "list_1[F9=1]/F9",
                "states": [{"type": "create"}]
            }
        ],
        "events":[
            {"type": "diff"}
        ]
    })";

    GmcSubConfigT newSubInfo;
    newSubInfo.subsName = "yangSub";
    newSubInfo.configJson = newSubJson;
    ret = GmcSubscribe(g_stmtSync, &newSubInfo, g_connSub, YangSnCallBack, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(user_data->subscriptionName, 128, "%s", newSubInfo.subsName);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtRoot, "all_field_type_vertex_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtRoot, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置rootF0
    uint32_t rootF0 = 1;
    ret = SetNodeProperty(g_rootNode, GMC_DATATYPE_UINT32, &rootF0, sizeof(uint32_t),
                            "root_F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < 5; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtList, "list_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList, &g_listNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置主键字段的值
        uint32_t listF1 = i;
        ret = SetNodeProperty(g_listNode, GMC_DATATYPE_UINT32, &listF1, sizeof(uint32_t),
                              "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        int64_t snF = i;
        ret = SetNodeProperty(g_listNode, GMC_DATATYPE_INT64, &snF, sizeof(int64_t),
                              "F9", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(6, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(6, data.succNum);

    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    SetDiffFile("diffFEx/diffSn009.json", NULL, user_data);
    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 获取diff
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ReleaseDiffFile();

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    AW_FUN_Log(LOG_STEP, "END");
}

// 010.uint64类型触发yang订阅推送
TEST_F(yang_sub_all_type_field, Yang_099_yang_sub_all_type_field_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexLabelAsync(g_stmt_async, "schemaFunc/AllFieldTypeVertex.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmt_async, "schemaFunc/AllFieldTypeEdge.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ModelCheck();

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"all_field_type_vertex_label",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "list_1[F10=1]/F10",
                "states": [{"type": "create"}]
            }
        ],
        "events":[
            {"type": "diff"}
        ]
    })";

    GmcSubConfigT newSubInfo;
    newSubInfo.subsName = "yangSub";
    newSubInfo.configJson = newSubJson;
    ret = GmcSubscribe(g_stmtSync, &newSubInfo, g_connSub, YangSnCallBack, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(user_data->subscriptionName, 128, "%s", newSubInfo.subsName);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtRoot, "all_field_type_vertex_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtRoot, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置rootF0
    uint32_t rootF0 = 1;
    ret = SetNodeProperty(g_rootNode, GMC_DATATYPE_UINT32, &rootF0, sizeof(uint32_t),
                            "root_F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < 5; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtList, "list_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList, &g_listNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置主键字段的值
        uint32_t listF1 = i;
        ret = SetNodeProperty(g_listNode, GMC_DATATYPE_UINT32, &listF1, sizeof(uint32_t),
                              "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint64_t snF = i;
        ret = SetNodeProperty(g_listNode, GMC_DATATYPE_UINT64, &snF, sizeof(uint64_t),
                              "F10", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(6, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(6, data.succNum);

    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    SetDiffFile("diffFEx/diffSn010.json", NULL, user_data);
    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 获取diff
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ReleaseDiffFile();

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    AW_FUN_Log(LOG_STEP, "END");
}

// 011.bool类型触发yang订阅推送
TEST_F(yang_sub_all_type_field, Yang_099_yang_sub_all_type_field_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexLabelAsync(g_stmt_async, "schemaFunc/AllFieldTypeVertex.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmt_async, "schemaFunc/AllFieldTypeEdge.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ModelCheck();

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"all_field_type_vertex_label",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "list_1[F11=false]/F11",
                "states": [{"type": "create"}]
            }
        ],
        "events":[
            {"type": "diff"}
        ]
    })";

    GmcSubConfigT newSubInfo;
    newSubInfo.subsName = "yangSub";
    newSubInfo.configJson = newSubJson;
    ret = GmcSubscribe(g_stmtSync, &newSubInfo, g_connSub, YangSnCallBack, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(user_data->subscriptionName, 128, "%s", newSubInfo.subsName);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtRoot, "all_field_type_vertex_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtRoot, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置rootF0
    uint32_t rootF0 = 1;
    ret = SetNodeProperty(g_rootNode, GMC_DATATYPE_UINT32, &rootF0, sizeof(uint32_t),
                            "root_F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool snF[2] = {true, false};
    for (int i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtList, "list_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList, &g_listNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置主键字段的值
        uint32_t listF1 = i;
        ret = SetNodeProperty(g_listNode, GMC_DATATYPE_UINT32, &listF1, sizeof(uint32_t),
                              "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        ret = SetNodeProperty(g_listNode, GMC_DATATYPE_BOOL, &snF[i], sizeof(bool),
                              "F11", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);

    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    SetDiffFile("diffFEx/diffSn011.json", NULL, user_data);
    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 获取diff
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ReleaseDiffFile();

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    AW_FUN_Log(LOG_STEP, "END");
}

// 012.float类型触发yang订阅推送
TEST_F(yang_sub_all_type_field, Yang_099_yang_sub_all_type_field_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexLabelAsync(g_stmt_async, "schemaFunc/AllFieldTypeVertex.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmt_async, "schemaFunc/AllFieldTypeEdge.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ModelCheck();

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"all_field_type_vertex_label",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "list_1[F12=1.0]/F12",
                "states": [{"type": "create"}]
            }
        ],
        "events":[
            {"type": "diff"}
        ]
    })";

    GmcSubConfigT newSubInfo;
    newSubInfo.subsName = "yangSub";
    newSubInfo.configJson = newSubJson;
    ret = GmcSubscribe(g_stmtSync, &newSubInfo, g_connSub, YangSnCallBack, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(user_data->subscriptionName, 128, "%s", newSubInfo.subsName);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtRoot, "all_field_type_vertex_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtRoot, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置rootF0
    uint32_t rootF0 = 1;
    ret = SetNodeProperty(g_rootNode, GMC_DATATYPE_UINT32, &rootF0, sizeof(uint32_t),
                            "root_F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < 5; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtList, "list_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList, &g_listNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置主键字段的值
        uint32_t listF1 = i;
        ret = SetNodeProperty(g_listNode, GMC_DATATYPE_UINT32, &listF1, sizeof(uint32_t),
                              "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        float snF = i;
        ret = SetNodeProperty(g_listNode, GMC_DATATYPE_FLOAT, &snF, sizeof(float),
                              "F12", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(6, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(6, data.succNum);

    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    SetDiffFile("diffFEx/diffSn012.json", NULL, user_data);
    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 获取diff
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ReleaseDiffFile();

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    AW_FUN_Log(LOG_STEP, "END");
}

// 013.double类型触发yang订阅推送
TEST_F(yang_sub_all_type_field, Yang_099_yang_sub_all_type_field_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexLabelAsync(g_stmt_async, "schemaFunc/AllFieldTypeVertex.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmt_async, "schemaFunc/AllFieldTypeEdge.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ModelCheck();

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"all_field_type_vertex_label",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "list_1[F13=1.0]/F13",
                "states": [{"type": "create"}]
            }
        ],
        "events":[
            {"type": "diff"}
        ]
    })";

    GmcSubConfigT newSubInfo;
    newSubInfo.subsName = "yangSub";
    newSubInfo.configJson = newSubJson;
    ret = GmcSubscribe(g_stmtSync, &newSubInfo, g_connSub, YangSnCallBack, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(user_data->subscriptionName, 128, "%s", newSubInfo.subsName);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtRoot, "all_field_type_vertex_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtRoot, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置rootF0
    uint32_t rootF0 = 1;
    ret = SetNodeProperty(g_rootNode, GMC_DATATYPE_UINT32, &rootF0, sizeof(uint32_t),
                            "root_F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < 5; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtList, "list_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList, &g_listNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置主键字段的值
        uint32_t listF1 = i;
        ret = SetNodeProperty(g_listNode, GMC_DATATYPE_UINT32, &listF1, sizeof(uint32_t),
                              "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        double snF = i;
        ret = SetNodeProperty(g_listNode, GMC_DATATYPE_DOUBLE, &snF, sizeof(double),
                              "F13", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(6, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(6, data.succNum);

    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    SetDiffFile("diffFEx/diffSn013.json", NULL, user_data);
    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 获取diff
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ReleaseDiffFile();

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    AW_FUN_Log(LOG_STEP, "END");
}

// 014.string类型触发yang订阅推送
TEST_F(yang_sub_all_type_field, Yang_099_yang_sub_all_type_field_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexLabelAsync(g_stmt_async, "schemaFunc/AllFieldTypeVertex.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmt_async, "schemaFunc/AllFieldTypeEdge.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ModelCheck();

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"all_field_type_vertex_label",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "list_1[F14='def']/F14",
                "states": [{"type": "create"}]
            }
        ],
        "events":[
            {"type": "diff"}
        ]
    })";

    GmcSubConfigT newSubInfo;
    newSubInfo.subsName = "yangSub";
    newSubInfo.configJson = newSubJson;
    ret = GmcSubscribe(g_stmtSync, &newSubInfo, g_connSub, YangSnCallBack, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(user_data->subscriptionName, 128, "%s", newSubInfo.subsName);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtRoot, "all_field_type_vertex_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtRoot, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置rootF0
    uint32_t rootF0 = 1;
    ret = SetNodeProperty(g_rootNode, GMC_DATATYPE_UINT32, &rootF0, sizeof(uint32_t),
                            "root_F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *snF[5] = {(char *)"abc", (char *)"def", (char *)"hig", (char *)"klm", (char *)"nop"};
    for (int i = 0; i < 5; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtList, "list_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList, &g_listNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置主键字段的值
        uint32_t listF1 = i;
        ret = SetNodeProperty(g_listNode, GMC_DATATYPE_UINT32, &listF1, sizeof(uint32_t),
                              "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        ret = SetNodeProperty(g_listNode, GMC_DATATYPE_STRING, snF[i], strlen(snF[i]),
                              "F14", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(6, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(6, data.succNum);

    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    SetDiffFile("diffFEx/diffSn014.json", NULL, user_data);
    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 获取diff
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ReleaseDiffFile();

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    AW_FUN_Log(LOG_STEP, "END");
}

// 015.identity类型触发yang订阅推送
TEST_F(yang_sub_all_type_field, Yang_099_yang_sub_all_type_field_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexLabelAsync(g_stmt_async, "schemaFunc/AllFieldTypeVertex.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmt_async, "schemaFunc/AllFieldTypeEdge.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ModelCheck();

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"all_field_type_vertex_label",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "list_1[F15=1]/F15",
                "states": [{"type": "create"}]
            }
        ],
        "events":[
            {"type": "diff"}
        ]
    })";

    GmcSubConfigT newSubInfo;
    newSubInfo.subsName = "yangSub";
    newSubInfo.configJson = newSubJson;
    ret = GmcSubscribe(g_stmtSync, &newSubInfo, g_connSub, YangSnCallBack, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(user_data->subscriptionName, 128, "%s", newSubInfo.subsName);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtRoot, "all_field_type_vertex_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtRoot, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置rootF0
    uint32_t rootF0 = 1;
    ret = SetNodeProperty(g_rootNode, GMC_DATATYPE_UINT32, &rootF0, sizeof(uint32_t),
                            "root_F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtList, "list_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList, &g_listNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置主键字段的值
        uint32_t listF1 = i;
        ret = SetNodeProperty(g_listNode, GMC_DATATYPE_UINT32, &listF1, sizeof(uint32_t),
                              "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        int32_t snF = i + 1;
        TestYangSetFieldID(g_listNode, GMC_DATATYPE_IDENTITY, &snF, sizeof(int32_t), "F15",
                           GMC_YANG_PROPERTY_OPERATION_CREATE, GMC_ATTRIBUTE_VALUE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(4, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(4, data.succNum);

    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    SetDiffFile("diffFEx/diffSn015.json", NULL, user_data);
    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 获取diff
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ReleaseDiffFile();

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    AW_FUN_Log(LOG_STEP, "END");
}

// 016.enum类型触发yang订阅推送
TEST_F(yang_sub_all_type_field, Yang_099_yang_sub_all_type_field_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexLabelAsync(g_stmt_async, "schemaFunc/AllFieldTypeVertex.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmt_async, "schemaFunc/AllFieldTypeEdge.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ModelCheck();

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"all_field_type_vertex_label",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "list_1[F16=1]/F16",
                "states": [{"type": "create"}]
            }
        ],
        "events":[
            {"type": "diff"}
        ]
    })";

    GmcSubConfigT newSubInfo;
    newSubInfo.subsName = "yangSub";
    newSubInfo.configJson = newSubJson;
    ret = GmcSubscribe(g_stmtSync, &newSubInfo, g_connSub, YangSnCallBack, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(user_data->subscriptionName, 128, "%s", newSubInfo.subsName);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtRoot, "all_field_type_vertex_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtRoot, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置rootF0
    uint32_t rootF0 = 1;
    ret = SetNodeProperty(g_rootNode, GMC_DATATYPE_UINT32, &rootF0, sizeof(uint32_t),
                            "root_F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtList, "list_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList, &g_listNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置主键字段的值
        uint32_t listF1 = i;
        ret = SetNodeProperty(g_listNode, GMC_DATATYPE_UINT32, &listF1, sizeof(uint32_t),
                              "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        int32_t snF = i + 1;
        TestYangSetFieldID(g_listNode, GMC_DATATYPE_ENUM, &snF, sizeof(int32_t), "F16",
                           GMC_YANG_PROPERTY_OPERATION_CREATE, GMC_ATTRIBUTE_VALUE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(4, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(4, data.succNum);

    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    SetDiffFile("diffFEx/diffSn016.json", NULL, user_data);
    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 获取diff
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ReleaseDiffFile();

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    AW_FUN_Log(LOG_STEP, "END");
}

// 017.time类型触发yang订阅推送
TEST_F(yang_sub_all_type_field, Yang_099_yang_sub_all_type_field_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexLabelAsync(g_stmt_async, "schemaFunc/AllFieldTypeVertex.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmt_async, "schemaFunc/AllFieldTypeEdge.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ModelCheck();

    // yang 订阅文件
#if defined CPU_BIT_32
    const char *newSubJson = R"({
        "label_name":"all_field_type_vertex_label",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "list_1[F_time=2023-03-14 10:59:41]/F_time",
                "states": [{"type": "create"}]
            }
        ],
        "events":[
            {"type": "diff"}
        ]
    })";
#else
    const char *newSubJson = R"({
        "label_name":"all_field_type_vertex_label",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "list_1[F_time=2023-03-14 18:59:41]/F_time",
                "states": [{"type": "create"}]
            }
        ],
        "events":[
            {"type": "diff"}
        ]
    })";
#endif

    GmcSubConfigT newSubInfo;
    newSubInfo.subsName = "yangSub";
    newSubInfo.configJson = newSubJson;
    ret = GmcSubscribe(g_stmtSync, &newSubInfo, g_connSub, YangSnCallBack, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(user_data->subscriptionName, 128, "%s", newSubInfo.subsName);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtRoot, "all_field_type_vertex_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtRoot, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置rootF0
    uint32_t rootF0 = 1;
    ret = SetNodeProperty(g_rootNode, GMC_DATATYPE_UINT32, &rootF0, sizeof(uint32_t),
                            "root_F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < 5; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtList, "list_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList, &g_listNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置主键字段的值
        uint32_t listF1 = i;
        ret = SetNodeProperty(g_listNode, GMC_DATATYPE_UINT32, &listF1, sizeof(uint32_t),
                              "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint64_t snF = i + 1678791580;
        ret = SetNodeProperty(g_listNode, GMC_DATATYPE_TIME, &snF, sizeof(uint64_t),
                              "F_time", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(6, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(6, data.succNum);

    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    SetDiffFile("diffFEx/diffSn017.json", NULL, user_data);
    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 获取推送diff
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ReleaseDiffFile();

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    AW_FUN_Log(LOG_STEP, "END");
}

// 018.bytes类型触发yang订阅推送
TEST_F(yang_sub_all_type_field, Yang_099_yang_sub_all_type_field_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexLabelAsync(g_stmt_async, "schemaFunc/AllFieldTypeVertex.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmt_async, "schemaFunc/AllFieldTypeEdge.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ModelCheck();

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"all_field_type_vertex_label",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "list_1[F_bytes=0x01]/F_bytes",
                "states": [{"type": "create"}]
            }
        ],
        "events":[
            {"type": "diff"}
        ]
    })";

    GmcSubConfigT newSubInfo;
    newSubInfo.subsName = "yangSub";
    newSubInfo.configJson = newSubJson;
    ret = GmcSubscribe(g_stmtSync, &newSubInfo, g_connSub, YangSnCallBack, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(user_data->subscriptionName, 128, "%s", newSubInfo.subsName);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtRoot, "all_field_type_vertex_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtRoot, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置rootF0
    uint32_t rootF0 = 1;
    ret = SetNodeProperty(g_rootNode, GMC_DATATYPE_UINT32, &rootF0, sizeof(uint32_t),
                            "root_F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtList, "list_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList, &g_listNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置主键字段的值
        uint32_t listF1 = i;
        ret = SetNodeProperty(g_listNode, GMC_DATATYPE_UINT32, &listF1, sizeof(uint32_t),
                              "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint8_t snF = i;
        ret = SetNodeProperty(g_listNode, GMC_DATATYPE_BYTES, &snF, sizeof(uint8_t),
                              "F_bytes", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(4, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(4, data.succNum);

    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    SetDiffFile("diffFEx/diffSn018.json", NULL, user_data);
    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 获取推送diff
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ReleaseDiffFile();

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    AW_FUN_Log(LOG_STEP, "END");
}

// 019.fixed类型触发yang订阅推送
TEST_F(yang_sub_all_type_field, Yang_099_yang_sub_all_type_field_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexLabelAsync(g_stmt_async, "schemaFunc/AllFieldTypeVertex.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmt_async, "schemaFunc/AllFieldTypeEdge.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ModelCheck();

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"all_field_type_vertex_label",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "list_1[F_fixed='bcdefgh']/F_fixed",
                "states": [{"type": "create"}]
            }
        ],
        "events":[
            {"type": "diff"}
        ]
    })";

    GmcSubConfigT newSubInfo;
    newSubInfo.subsName = "yangSub";
    newSubInfo.configJson = newSubJson;
    ret = GmcSubscribe(g_stmtSync, &newSubInfo, g_connSub, YangSnCallBack, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(user_data->subscriptionName, 128, "%s", newSubInfo.subsName);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtRoot, "all_field_type_vertex_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtRoot, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置rootF0
    uint32_t rootF0 = 1;
    ret = SetNodeProperty(g_rootNode, GMC_DATATYPE_UINT32, &rootF0, sizeof(uint32_t),
                            "root_F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *snF[5] = {(char *)"abcdefg", (char *)"bcdefgh", (char *)"cdefghi", (char *)"defghij", (char *)"efghijk"};
    for (int i = 0; i < 5; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtList, "list_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList, &g_listNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置主键字段的值
        uint32_t listF1 = i;
        ret = SetNodeProperty(g_listNode, GMC_DATATYPE_UINT32, &listF1, sizeof(uint32_t),
                              "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        ret = SetNodeProperty(g_listNode, GMC_DATATYPE_FIXED, snF[i], 7,
                              "F_fixed", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(6, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(6, data.succNum);

    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    SetDiffFile("diffFEx/diffSn019.json", NULL, user_data);
    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 获取推送diff
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ReleaseDiffFile();

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    AW_FUN_Log(LOG_STEP, "END");
}

// 020.bitmap类型触发yang订阅推送
TEST_F(yang_sub_all_type_field, Yang_099_yang_sub_all_type_field_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexLabelAsync(g_stmt_async, "schemaFunc/AllFieldTypeVertex.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmt_async, "schemaFunc/AllFieldTypeEdge.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ModelCheck();

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"all_field_type_vertex_label",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "list_1[F_bitmap=0x01]/F_bitmap",
                "states": [{"type": "create"}]
            }
        ],
        "events":[
            {"type": "diff"}
        ]
    })";

    GmcSubConfigT newSubInfo;
    newSubInfo.subsName = "yangSub";
    newSubInfo.configJson = newSubJson;
    ret = GmcSubscribe(g_stmtSync, &newSubInfo, g_connSub, YangSnCallBack, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(user_data->subscriptionName, 128, "%s", newSubInfo.subsName);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtRoot, "all_field_type_vertex_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtRoot, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置rootF0
    uint32_t rootF0 = 1;
    ret = SetNodeProperty(g_rootNode, GMC_DATATYPE_UINT32, &rootF0, sizeof(uint32_t),
                            "root_F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint8_t snFBitsVal[3] = {0x00, 0x01, 0x02};
    for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtList, "list_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtRoot, g_stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtList, &g_listNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置主键字段的值
        uint32_t listF1 = i;
        ret = SetNodeProperty(g_listNode, GMC_DATATYPE_UINT32, &listF1, sizeof(uint32_t),
                              "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint8_t snFBits[1] = {snFBitsVal[i]};
        GmcBitMapT snF = {0};
        snF.beginPos = 0;
        snF.endPos = 8 - 1;
        snF.bits = snFBits;
        ret = SetNodeProperty(g_listNode, GMC_DATATYPE_BITMAP, &snF, sizeof(GmcBitMapT),
                              "F_bitmap", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(4, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(4, data.succNum);

    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    SetDiffFile("diffFEx/diffSn020.json", NULL, user_data);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 获取推送diff
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ReleaseDiffFile();

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    AW_FUN_Log(LOG_STEP, "END");
}
