[{"type": "container", "name": "vertex", "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "int32", "nullable": true}, {"type": "container", "name": "con", "clause": [{"type": "when", "formula": "/vertex/con/F0=2"}], "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "int32", "nullable": true}, {"name": "F1", "type": "int32", "nullable": true}]}], "keys": [{"node": "vertex", "name": "pk", "fields": ["ID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]