alias_ContainerOne:update[(pri<PERSON><PERSON>(ID:1)),(pri<PERSON><PERSON>(ID:1))]
alias_ContainerOne.alias_ListOne:update[(pri<PERSON>ey(PID:1,F0:99)),(priKey(PID:1,F0:99))]
alias_ListOne.ContainerTwo:create
ContainerTwo.F0:create(301)
ContainerTwo.F1:create(301)
ContainerTwo.F5:create(555)
ContainerTwo.F6:create(666)
ContainerTwo.F7:create(777)
ContainerTwo.F8:create(888)
ContainerTwo.F9:create(999)
ContainerTwo.F11:create(default11)
ContainerTwo.F12:create(default12)
ContainerTwo.F13:create(default13)
alias_ContainerOne.alias_ListOne:update[(priKey(PID:1,F0:100), preKey(PID:1,F0:99)),(priKey(PID:1,F0:100), preKey(PID:1,F0:99))]
alias_ListOne.ContainerTwo:update
ContainerTwo.F0:update(301,300)
ContainerTwo.F1:update(301,300)
alias_ContainerOne.alias_ListOne:update[(priKey(PID:1,F0:101), preKey(PID:1,F0:100)),(priKey(PID:1,F0:101), preKey(PID:1,F0:100))]
alias_ListOne.ContainerTwo:remove
ContainerTwo.F0:remove(300)
ContainerTwo.F1:remove(300)
ContainerTwo.F5:remove(555)
ContainerTwo.F6:remove(666)
ContainerTwo.F7:remove(777)
ContainerTwo.F8:remove(888)
ContainerTwo.F9:remove(999)
ContainerTwo.F11:remove(default11)
ContainerTwo.F12:remove(default12)
ContainerTwo.F13:remove(default13)
