{
    "type":"container",
    "name":"hpe",
    "fields":[
        {"name":"ID", "type":"uint32", "nullable":false},
        {
            "type":"container",
            "name":"ssh",
            "presence": true,
            "fields": [
                {"name":"ifMtu", "type":"uint32"}
            ]
        },
        {
            "type":"choice",
            "name":"how",
            "fields": [
                {
                    "type":"case",
                    "name":"interval",
                    "default": true,
                    "fields": [
                        {"name":"interval", "type":"uint16", "default": 8}
                    ]
                },
                {
                    "type":"case",
                    "name":"daily",
                    "fields": [
                        {"name":"time-of-day", "type":"string"}
                    ]
                }
            ]
        },
        {
            "type":"choice",
            "name":"food",
            "nullable":false,
            "fields": [
                {
                    "type":"case",
                    "name":"apple",
                    "fields": [
                        {"name":"color", "type":"string"}
                    ]
                },
                {
                    "type":"case",
                    "name":"banana",
                    "fields": [
                        {"name":"color", "type":"string"}
                    ]
                }
            ]
        },
        {
            "type":"list",
            "name":"hpe::HppMbufInfo",
            "is_config":false,
            "fields":[
                {"name":"ID", "type":"uint32", "nullable":false, "auto_increment":true},
                {"name":"PID", "type":"int32", "nullable":false},
                {"name":"SlotId", "type":"string", "length":"0..49"},
                {"name":"UnitId", "type":"int32"},
                {"name":"FeDiagData", "type":"string", "length":"0..61311"},
                {"name":"Pid", "type":"int32"},
                {
                    "type":"container",
                    "name":"interface",
                    "fields": [
                        {"name":"ifMTU", "type":"int32"}
                    ]
                }
            ],
        }
    ],
    "keys":[
        {
            "name":"hpe.PK",
            "fields":["ID"],
            "index":{"type":"primary"},
            "constraints":{"unique":true}
        }
    ]
}

