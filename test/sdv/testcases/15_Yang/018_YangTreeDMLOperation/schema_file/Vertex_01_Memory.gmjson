[{"version": "2.0", "type": "record", "name": "vertex_01_Memory", "fields": [{"name": "PK", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "P0", "type": "string", "size": 1024, "nullable": true}, {"name": "P1", "type": "string", "size": 1024, "nullable": true}, {"name": "P2", "type": "string", "size": 1024, "nullable": true}, {"name": "P3", "type": "string", "size": 1024, "nullable": true}, {"name": "P4", "type": "string", "size": 1024, "nullable": true}, {"name": "P5", "type": "string", "size": 1024, "nullable": true}, {"name": "P6", "type": "string", "size": 1024, "nullable": true}], "keys": [{"name": "vertex_01_Memory_key", "index": {"type": "primary"}, "fields": ["PK"], "constraints": {"unique": true}, "comment": "主键索引"}]}]