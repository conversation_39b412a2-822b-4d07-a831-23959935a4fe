[{"type": "list", "name": "when_023", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "F0"}, {"type": "when", "formula": "F0"}, {"type": "when", "formula": "F0"}, {"type": "when", "formula": "F0"}, {"type": "when", "formula": "F0"}, {"type": "when", "formula": "F0"}, {"type": "when", "formula": "F0"}, {"type": "when", "formula": "F0"}, {"type": "when", "formula": "F0"}]}], "keys": [{"node": "when_023", "name": "pk", "fields": ["PID", "F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]