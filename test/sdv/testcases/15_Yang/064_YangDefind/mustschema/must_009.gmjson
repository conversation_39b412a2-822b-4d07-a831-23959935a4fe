[{"type": "container", "name": "must_009", "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"type": "choice", "name": "choice", "nullable": false, "clause": [{"type": "must", "formula": "F0"}], "fields": [{"type": "case", "name": "case", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}]}]}], "keys": [{"node": "must_009", "name": "pk", "fields": ["ID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]