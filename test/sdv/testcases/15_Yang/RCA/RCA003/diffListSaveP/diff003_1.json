alias_ContainerOne:update[(pri<PERSON><PERSON>(ID:1)),(pri<PERSON><PERSON>(ID:1))]
alias_ContainerOne.F0:create(100)
alias_ContainerOne.F6:create(str00100)
alias_ContainerOne.F22:create(NIL:23)
alias_ContainerOne.alias_ListOne:create[(priKey(PID:1,F0:10)),(NULL)]
alias_ListOne.ID:create(1)
alias_ListOne.F1:create(10)
alias_ListOne.F2:create(10)
alias_ListOne.F3:create(10)
alias_ListOne.ListContainerOne:create
ListContainerOne.F0:create(100)
alias_ContainerOne.alias_ListOne:create[(pri<PERSON>ey(PID:1,F0:11), preKey(PID:1,F0:10)),(NULL)]
alias_ListOne.ID:create(2)
alias_ListOne.F1:create(11)
alias_ListOne.F2:create(11)
alias_ListOne.F3:create(11)
alias_ListOne.ListContainerOne:create
List<PERSON><PERSON>r<PERSON><PERSON>.F0:create(100)
