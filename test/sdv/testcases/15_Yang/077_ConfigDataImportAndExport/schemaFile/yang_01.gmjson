[{"type": "container", "name": "root_1", "presence": false, "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true, "default": 222}, {"name": "F3", "type": "uint32", "nullable": true, "default": 333}, {"name": "F4", "type": "uint32", "nullable": true, "default": 444}, {"name": "F5", "type": "string", "size": 10, "nullable": true}, {"name": "F6", "type": "string", "size": 10, "nullable": true, "default": "default6"}, {"name": "F7", "type": "string", "size": 10, "nullable": true, "default": "default7"}, {"name": "F8", "type": "string", "size": 10, "nullable": true, "default": "default8"}, {"name": "ID1", "type": "enum", "nullable": true, "enumerate_identity": "ID1", "enumerate": [{"name": "level1", "value": 1}, {"name": "level2", "value": 2}, {"name": "level3", "value": 3}, {"name": "level0", "value": 0}, {"name": "level-1", "value": -1}, {"name": "level-2", "value": -2}, {"name": "level-3", "value": -3}]}, {"name": "ID2", "type": "enum", "nullable": true, "default": "level-1", "enumerate_identity": "ID2", "enumerate": [{"name": "level1", "value": 1}, {"name": "level2", "value": 2}, {"name": "level3", "value": 3}, {"name": "level0", "value": 0}, {"name": "level-1", "value": -1}, {"name": "level-2", "value": -2}, {"name": "level-3", "value": -3}]}, {"name": "ID3", "type": "enum", "nullable": true, "default": 3, "enumerate_identity": "ID3", "enumerate": [{"name": "level1", "value": 1}, {"name": "level2", "value": 2}, {"name": "level3", "value": 3}, {"name": "level0", "value": 0}, {"name": "level-1", "value": -1}, {"name": "level-2", "value": -2}, {"name": "level-3", "value": -3}]}, {"type": "container", "name": "con_1", "presence": false, "fields": [{"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true, "default": 222}, {"name": "F3", "type": "uint32", "nullable": true, "default": 333}, {"name": "F4", "type": "uint32", "nullable": true, "default": 444}, {"name": "F5", "type": "string", "size": 10, "nullable": true}, {"name": "F6", "type": "string", "size": 10, "nullable": true, "default": "default6"}, {"name": "F7", "type": "string", "size": 10, "nullable": true, "default": "default7"}, {"name": "F8", "type": "string", "size": 10, "nullable": true, "default": "default8"}, {"name": "ID1", "type": "enum", "nullable": true, "enumerate_identity": "ID1", "enumerate": [{"name": "level1", "value": 1}, {"name": "level2", "value": 2}, {"name": "level3", "value": 3}, {"name": "level0", "value": 0}, {"name": "level-1", "value": -1}, {"name": "level-2", "value": -2}, {"name": "level-3", "value": -3}]}, {"name": "ID2", "type": "enum", "nullable": true, "default": "level-1", "enumerate_identity": "ID2", "enumerate": [{"name": "level1", "value": 1}, {"name": "level2", "value": 2}, {"name": "level3", "value": 3}, {"name": "level0", "value": 0}, {"name": "level-1", "value": -1}, {"name": "level-2", "value": -2}, {"name": "level-3", "value": -3}]}, {"name": "ID3", "type": "enum", "nullable": true, "default": 3, "enumerate_identity": "ID3", "enumerate": [{"name": "level1", "value": 1}, {"name": "level2", "value": 2}, {"name": "level3", "value": 3}, {"name": "level0", "value": 0}, {"name": "level-1", "value": -1}, {"name": "level-2", "value": -2}, {"name": "level-3", "value": -3}]}]}, {"type": "choice", "name": "choice_1", "fields": [{"type": "case", "name": "case_1", "fields": [{"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true, "default": 222}, {"name": "F3", "type": "uint32", "nullable": true, "default": 333}, {"name": "F4", "type": "uint32", "nullable": true, "default": 444}, {"name": "F5", "type": "string", "size": 10, "nullable": true}, {"name": "F6", "type": "string", "size": 10, "nullable": true, "default": "default6"}, {"name": "F7", "type": "string", "size": 10, "nullable": true, "default": "default7"}, {"name": "F8", "type": "string", "size": 10, "nullable": true, "default": "default8"}, {"name": "ID1", "type": "enum", "nullable": true, "enumerate_identity": "ID1", "enumerate": [{"name": "level1", "value": 1}, {"name": "level2", "value": 2}, {"name": "level3", "value": 3}, {"name": "level0", "value": 0}, {"name": "level-1", "value": -1}, {"name": "level-2", "value": -2}, {"name": "level-3", "value": -3}]}]}]}], "keys": [{"node": "root_1", "name": "PK", "fields": ["ID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]