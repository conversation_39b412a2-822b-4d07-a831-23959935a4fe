[{"type": "record", "name": "reslabel", "fields": [{"name": "F0", "type": "int64", "nullable": false}, {"name": "F14", "type": "string", "size": 20, "nullable": false}, {"name": "F15", "type": "bytes", "size": 7, "nullable": false}, {"name": "F1", "type": "uint64", "nullable": true}, {"name": "F2", "type": "int32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": false}, {"name": "F4", "type": "int16", "nullable": true}, {"name": "F5", "type": "uint16", "nullable": true}, {"name": "F6", "type": "int8", "nullable": true}, {"name": "F7", "type": "uint8", "nullable": true}, {"name": "F8", "type": "boolean", "nullable": true}, {"name": "F9", "type": "float", "nullable": true}, {"name": "F10", "type": "double", "nullable": true}, {"name": "F11", "type": "time", "nullable": true}, {"name": "F12", "type": "char", "nullable": true}, {"name": "F13", "type": "uchar", "nullable": true}, {"name": "F16", "type": "fixed", "size": 7, "nullable": true}, {"name": "F17", "type": "uint32", "nullable": false}, {"name": "F18", "type": "uint32", "nullable": false}, {"name": "F19", "type": "uint8", "nullable": false}, {"name": "F20", "type": "string", "size": 20, "nullable": true}, {"name": "F21", "type": "bytes", "size": 7, "nullable": true}, {"name": "F22", "type": "fixed", "size": 7, "nullable": true}, {"name": "F23", "type": "time", "nullable": true}, {"name": "F24", "type": "int32", "nullable": true}, {"name": "F25", "type": "uint32", "nullable": true}, {"name": "F26", "type": "int16", "nullable": true}, {"name": "F27", "type": "uint16", "nullable": true}, {"name": "F28", "type": "int64", "nullable": true}, {"name": "F29", "type": "uint64", "nullable": true}, {"name": "F30", "type": "char", "nullable": true}, {"name": "F31", "type": "int32", "nullable": true}, {"name": "F32", "type": "uint32", "nullable": true}, {"name": "res", "type": "resource", "nullable": false}], "keys": [{"node": "reslabel", "name": "PK", "fields": ["F0", "F14", "F15"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"name": "localhash_key", "index": {"type": "<PERSON><PERSON><PERSON>"}, "fields": ["F0", "F2", "F4", "F5", "F6", "F14"], "constraints": {"unique": false}}, {"name": "lpm4_key", "index": {"type": "lpm4_tree_bitmap"}, "fields": ["F3", "F17", "F18", "F19"], "constraints": {"unique": true}}, {"name": "local_key", "fields": ["F1", "F7", "F11", "F12", "F13", "F16", "F24", "F25"], "index": {"type": "local"}, "constraints": {"unique": true}}, {"name": "hashcluster_key", "fields": ["F22", "F26", "F27", "F28", "F29", "F30", "F31", "F32"], "index": {"type": "hashcluster"}, "constraints": {"unique": true}}]}]