{"type": "list", "name": "list_1", "clause": [{"type": "must", "formula": "re-match('str1', 'str2')"}, {"type": "must", "formula": "last() > 1"}, {"type": "must", "formula": "position() > 1"}, {"type": "must", "formula": "name(/container_1) != 'str'"}, {"type": "must", "formula": "concat('str1', 'str2', 'str3', 'str4', 'str5', 'str6', 'str7', 'str8', 'str9', 'str10', 'str11', 'str12', 'str13', 'str14', 'str15', 'str16', 'str17', 'str18', 'str19', 'str20', 'str21', 'str22', 'str23', 'str24', 'str25', 'str26', 'str27', 'str28', 'str29', 'str30', 'str31', 'str32') = 'str'"}, {"type": "must", "formula": "contains('str1', 'str2')"}, {"type": "must", "formula": "substring-before('str1', 'str2') = 'str'"}, {"type": "must", "formula": "substring-after('str1', 'str2') != 'str'"}, {"type": "when", "formula": "substring('str1', 1, 2) = 'str'"}], "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "list_1_F1", "type": "int32", "nullable": false, "clause": [{"type": "leafref", "formula": "deref(/container_1)"}, {"type": "must", "formula": "string-length('str') > 1"}, {"type": "must", "formula": "normalize-space('str') = 'str'"}, {"type": "must", "formula": "boolean(1)"}, {"type": "must", "formula": "true()"}, {"type": "must", "formula": "false()"}, {"type": "must", "formula": "lang('str')"}, {"type": "must", "formula": "sum(/container_1) > 1"}, {"type": "must", "formula": "floor(1) > 1"}]}, {"name": "list_1_F2", "type": "int32", "nullable": false, "clause": [{"type": "must", "formula": "derived-from(/container_1, 'str1')"}, {"type": "must", "formula": "derived-from-or-self(/container_1, 'str1')"}, {"type": "must", "formula": "enum-value(/container_1) = 1"}, {"type": "must", "formula": "bit-is-set(/container_1, 'str1')"}, {"type": "must", "formula": "re-match('str1', 'str2')"}, {"type": "must", "formula": "local-name(/container_1) = 'str1'"}, {"type": "must", "formula": "ceiling(1) > 1"}, {"type": "must", "formula": "round(1) < 1"}, {"type": "when", "formula": "re-match('str1', 'str2')"}]}], "keys": [{"node": "list_1", "name": "pk", "fields": ["PID", "list_1_F1"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}