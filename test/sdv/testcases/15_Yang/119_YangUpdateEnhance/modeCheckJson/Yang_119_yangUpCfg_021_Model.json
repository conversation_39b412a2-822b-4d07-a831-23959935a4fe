{"type": "container", "name": "ContainerOne", "alias": "alias_Container<PERSON>ne", "rfc7951_invisible": true, "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32"}, {"name": "F2", "type": "uint32"}, {"name": "F3", "type": "uint32"}, {"name": "F4", "type": "uint32"}, {"name": "F5", "type": "uint32", "default": ["555"]}, {"name": "F6", "type": "uint32", "default": ["666"]}, {"name": "F7", "type": "uint32", "default": ["777"]}, {"name": "F8", "type": "uint32", "default": ["888"]}, {"name": "F9", "type": "uint32", "default": ["999"]}, {"name": "F10", "type": "string"}, {"name": "F12", "type": "uint16"}, {"name": "F13", "type": "uint32"}, {"name": "F14", "type": "int64"}, {"name": "F15", "type": "uint64"}, {"name": "F16", "type": "time"}, {"name": "F17", "type": "bitfield8"}, {"name": "F18", "type": "bitfield16"}, {"name": "F19", "type": "bitfield32"}, {"name": "F20", "type": "bytes"}, {"name": "F21", "type": "fixed"}, {"name": "F22", "type": "bitmap", "default": ["0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000"]}, {"name": "F23", "type": "bitfield64"}, {"name": "F24", "type": "int32"}, {"type": "container", "name": "ContainerTwo", "presence": true, "fields": [{"name": "F0", "type": "uint32"}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "bool"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "bool"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}, {"type": "container", "name": "Container<PERSON>hree", "fields": [{"name": "F0", "type": "uint32"}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "bool"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "bool"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}, {"type": "container", "name": "ContainerFour", "fields": [{"name": "F0", "type": "uint32"}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "bool"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "bool"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}]}, {"type": "choice", "name": "Choice", "fields": [{"type": "case", "name": "CaseOne", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "bool"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "bool"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}, {"type": "container", "name": "CaseContainerOne", "presence": true, "fields": [{"name": "F0", "type": "uint32"}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "bool"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "bool"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}, {"type": "container", "name": "CaseContainerTwo", "fields": [{"name": "F0", "type": "uint32"}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "bool"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "bool"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}]}]}, {"type": "choice", "name": "ChoiceTwo", "fields": [{"type": "case", "name": "CaseOne", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "bool"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "bool"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}, {"type": "container", "name": "CaseContainerOne", "presence": true, "fields": [{"name": "F0", "type": "uint32"}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "bool"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "bool"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}, {"type": "container", "name": "CaseContainerTwo", "fields": [{"name": "F0", "type": "uint32"}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "bool"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "bool"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}]}]}, {"type": "list", "name": "ListOne", "alias": "alias_<PERSON><PERSON>ne", "rfc7951_invisible": true, "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "string", "nullable": false}, {"name": "F1", "type": "uint32"}, {"name": "F2", "type": "uint32"}, {"name": "F3", "type": "uint32"}, {"name": "F4", "type": "uint32"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string", "clause": [{"type": "when", "formula": "/alias_ContainerOne/alias_ListOne[F0=current()]/F1 = 300", "status": "valid"}]}, {"name": "F7", "type": "bool"}, {"type": "container", "name": "ListContainerOne", "presence": true, "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "bool"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "bool"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}, {"type": "container", "name": "ListContainerthree", "fields": [{"name": "F0", "type": "uint32"}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "bool"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "bool"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}]}, {"type": "choice", "name": "Listchoice", "fields": [{"type": "case", "name": "ListchoiceCase", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "bool"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "bool"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}]}, {"type": "container", "name": "ListContainerTwo", "fields": [{"name": "F0", "type": "uint32"}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "bool"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "bool"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}, {"type": "list", "name": "ListTwo", "alias": "alias_ListTwo", "rfc7951_invisible": true, "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "bool"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "bool"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}], "keys": [{"node": "ListTwo", "name": "PK", "fields": ["PID", "F0"], "index": {"type": "primary"}, "constrains": {"unique": true}}]}], "keys": [{"node": "ListOne", "name": "PK", "fields": ["PID", "F0"], "index": {"type": "primary"}, "constrains": {"unique": true}}, {"node": "ListOne", "name": "<PERSON><PERSON><PERSON>", "fields": ["PID", "F1"], "index": {"type": "list_localhash"}, "constraints": {"unique": true}}, {"node": "ListOne", "name": "AutoIndex_1048729_1_8", "fields": ["F6"], "index": {"type": "list_localhash"}, "constraints": {"unique": false}}]}, {"type": "leaflist", "name": "LeafList", "alias": "alias_LeafList", "max-elements": 10000, "rfc7951_invisible": true, "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false, "default": ["2", "3"]}], "keys": [{"node": "LeafList", "name": "PK", "fields": ["PID", "F0"], "index": {"type": "primary"}, "constrains": {"unique": true}}]}], "keys": [{"node": "ContainerOne", "name": "PK", "fields": ["ID"], "index": {"type": "primary"}, "constrains": {"unique": true}}]}