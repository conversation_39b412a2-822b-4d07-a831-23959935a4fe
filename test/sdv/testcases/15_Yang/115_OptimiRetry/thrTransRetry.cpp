#include "tools.h"

GmcTransDoneT userCb = (GmcTransDoneT)AsyncRetryCommitCb;

class thr_trans_retry : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void thr_trans_retry::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"forceCommitEnable=1\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"defaultIsolationLevel=2\""); // 事务隔离级别：可重复读
    system("sh $TEST_HOME/tools/modifyCfg.sh \"defaultTransactionType=1\""); // 默认namespace设为乐观
    system("sh $TEST_HOME/tools/start.sh");
    system("gmadmin -cfgName forceCommitEnable -cfgVal 1");
}

void thr_trans_retry::TearDownTestCase()
{
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
    system("gmadmin -cfgName forceCommitEnable -cfgVal 0");
}

void thr_trans_retry::SetUp()
{
    int ret;
    AsyncUserDataT data = {0};
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步建连
    ret = testGmcConnect(&g_connAsync, &g_stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步创建namespace
    TryDropNameSpace(g_stmtAsync, G_NAMESPACE);
    TryCreateNameSpace(g_stmtAsync, G_NAMESPACE);

    ret = GmcUseNamespaceAsync(g_stmtAsync, G_NAMESPACE, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 重试前事务涉及冲突与null，在此屏蔽
    AddWhiteList(GMERR_NULL_VALUE_NOT_ALLOWED);
    AddWhiteList(GMERR_RESTRICT_VIOLATION);

    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    AddWhiteList(GMERR_NO_DATA);
}

void thr_trans_retry::TearDown()
{
    int ret;
    AsyncUserDataT data = {0};

    // 异步删除namespace
    TryDropNameSpace(g_stmtAsync, G_NAMESPACE);

    // 断连
    ret = testGmcDisconnect(g_connAsync, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_END();
    GmcDetachAllShmSeg();
    testEnvClean();
}

void *thrDoUpdateDelete(void *args)
{
    int ret = 0;
    uint32_t keyVal = *((uint32_t *)args);
    AsyncUserDataT data = {0};
    GmcBatchT *batch = NULL;

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = useNameSpaceAsync(stmt, G_NAMESPACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcStmtT *stmtRoot = NULL;
    GmcNodeT *rootNode = NULL;
    ret = GmcAllocStmt(conn, &stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcStmtT *stmtList = NULL;
    GmcNodeT *listNode = NULL;
    ret = GmcAllocStmt(conn, &stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 10000; i++) {
        if (i % 200 == 0) {
            cout << endl << "keyVal: " << keyVal << ", " << "i: " << i << endl;
            if (i % 1000 == 0) {
                system("gmsysview -q V\\$COM_DYN_CTX -f CTX_ID=0");
            }
        }
        // 开启事务
        ret = TestTransStartAsync(conn, GMC_FORCE_COMMIT_MODE_0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // merge事务操作
        ret = TestBatchPrepare(conn, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int dmlTimes = 0;

        ret = testGmcPrepareStmtByLabelName(stmtRoot, "main_label", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, stmtRoot);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmtRoot, &rootNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t f1Val = 1;
        ret = SetNodeProperty(rootNode, GMC_DATATYPE_UINT32, &f1Val, sizeof(uint32_t), "F1",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加批操作
        ret = GmcBatchAddDML(batch, stmtRoot);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        dmlTimes++;

        ret = testGmcPrepareStmtByLabelName(stmtList, "list_label", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmtRoot, stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmtList, &listNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmtList, 1, GMC_DATATYPE_UINT32, &keyVal, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t listF1Val = 1;
        ret = SetNodeProperty(listNode, GMC_DATATYPE_UINT32, &listF1Val, sizeof(uint32_t),
                            "list_F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        dmlTimes++;

        // 批处理提交
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.succNum);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));
        // 提交事务
        ret = TestTransCommitAsync(conn);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 开启事务
        ret = TestTransStartAsync(conn, GMC_FORCE_COMMIT_MODE_0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // delete事务操作
        batch = NULL;
        ret = TestBatchPrepare(conn, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        dmlTimes = 0;

        ret = testGmcPrepareStmtByLabelName(stmtRoot, "main_label", GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, stmtRoot);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmtRoot, &rootNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, stmtRoot);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        dmlTimes++;

        ret = testGmcPrepareStmtByLabelName(stmtList, "list_label", GMC_OPERATION_DELETE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmtRoot, stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmtList, &listNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmtList, 1, GMC_DATATYPE_UINT32, &keyVal, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        dmlTimes++;

        // 批处理提交
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.succNum);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 提交事务
        ret = TestTransCommitAsync(conn);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    GmcFreeStmt(stmtRoot);
    GmcFreeStmt(stmtList);

    // 断连
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 001.并发两个线程，一个循环merge、delete10000次，另一个循环merge、delete10000次不同数据
TEST_F(thr_trans_retry, Yang_115_thrTransRetry_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/diffFConflict_vertex_11.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/diffFConflict_edge.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ModelCheckAndDataCheck(g_connAsync, g_stmtAsync);

    pthread_t thrDML1;
    pthread_t thrDML2;
    uint32_t keyVal1 = 1;
    uint32_t keyVal2 = 2;
    ret = pthread_create(&thrDML1, NULL, thrDoUpdateDelete, (void *)&keyVal1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thrDML2, NULL, thrDoUpdateDelete, (void *)&keyVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_join(thrDML1, NULL);
    pthread_join(thrDML2, NULL);

    AW_FUN_Log(LOG_STEP, "test end.");
}

void *thrDoDML(void *args)
{
    int ret = 0;
    uint32_t keyVal = *((uint32_t *)args);
    AsyncUserDataT data = {0};
    GmcBatchT *batch = NULL;

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = useNameSpaceAsync(stmt, G_NAMESPACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcStmtT *stmtRoot = NULL;
    GmcNodeT *rootNode = NULL;
    ret = GmcAllocStmt(conn, &stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcStmtT *stmtList = NULL;
    GmcNodeT *listNode = NULL;
    ret = GmcAllocStmt(conn, &stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 10000; i++) {
        if (i % 200 == 0) {
            cout << endl << "keyVal: " << keyVal << ", " << "i: " << i << endl;
            if (i % 1000 == 0) {
                system("gmsysview -q V\\$COM_DYN_CTX -f CTX_ID=0");
            }
        }
        // 开启事务
        ret = TestTransStartAsync(conn, GMC_FORCE_COMMIT_MODE_0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // merge事务操作
        ret = TestBatchPrepare(conn, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int dmlTimes = 0;

        ret = testGmcPrepareStmtByLabelName(stmtRoot, "main_label", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, stmtRoot);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmtRoot, &rootNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t f1Val = 1;
        ret = SetNodeProperty(rootNode, GMC_DATATYPE_UINT32, &f1Val, sizeof(uint32_t), "F1",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加批操作
        ret = GmcBatchAddDML(batch, stmtRoot);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        dmlTimes++;

        ret = testGmcPrepareStmtByLabelName(stmtList, "list_label", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmtRoot, stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmtList, &listNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmtList, 1, GMC_DATATYPE_UINT32, &keyVal, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t listF1Val = 1;
        ret = SetNodeProperty(listNode, GMC_DATATYPE_UINT32, &listF1Val, sizeof(uint32_t),
                            "list_F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        dmlTimes++;

        // 批处理提交
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.succNum);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));
        // 提交事务
        ret = TestTransCommitAsync(conn);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 开启事务
        ret = TestTransStartAsync(conn, GMC_FORCE_COMMIT_MODE_0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // merge事务操作
        ret = TestBatchPrepare(conn, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        dmlTimes = 0;

        ret = testGmcPrepareStmtByLabelName(stmtRoot, "main_label", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, stmtRoot);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmtRoot, &rootNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        f1Val = 1;
        ret = SetNodeProperty(rootNode, GMC_DATATYPE_UINT32, &f1Val, sizeof(uint32_t), "F1",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加批操作
        ret = GmcBatchAddDML(batch, stmtRoot);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        dmlTimes++;

        ret = testGmcPrepareStmtByLabelName(stmtList, "list_label", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmtRoot, stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmtList, &listNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmtList, 1, GMC_DATATYPE_UINT32, &keyVal, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        listF1Val = 2;
        ret = SetNodeProperty(listNode, GMC_DATATYPE_UINT32, &listF1Val, sizeof(uint32_t),
                            "list_F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        dmlTimes++;

        // 批处理提交
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.succNum);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));
        // 提交事务
        ret = TestTransCommitAsync(conn);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 开启事务
        ret = TestTransStartAsync(conn, GMC_FORCE_COMMIT_MODE_0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // delete事务操作
        batch = NULL;
        ret = TestBatchPrepare(conn, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        dmlTimes = 0;

        ret = testGmcPrepareStmtByLabelName(stmtRoot, "main_label", GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, stmtRoot);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmtRoot, &rootNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, stmtRoot);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        dmlTimes++;

        ret = testGmcPrepareStmtByLabelName(stmtList, "list_label", GMC_OPERATION_DELETE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmtRoot, stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmtList, &listNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmtList, 1, GMC_DATATYPE_UINT32, &keyVal, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        dmlTimes++;

        // 批处理提交
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.succNum);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 提交事务
        ret = TestTransCommitAsync(conn);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    GmcFreeStmt(stmtRoot);
    GmcFreeStmt(stmtList);

    // 断连
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 002.并发两个线程，一个循环merge、delete10000次，另一个循环merge、delete10000次不同数据
TEST_F(thr_trans_retry, Yang_115_thrTransRetry_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/diffFConflict_vertex_11.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/diffFConflict_edge.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ModelCheckAndDataCheck(g_connAsync, g_stmtAsync);

    pthread_t thrDML1;
    pthread_t thrDML2;
    uint32_t keyVal1 = 1;
    uint32_t keyVal2 = 2;
    ret = pthread_create(&thrDML1, NULL, thrDoDML, (void *)&keyVal1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thrDML2, NULL, thrDoDML, (void *)&keyVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_join(thrDML1, NULL);
    pthread_join(thrDML2, NULL);

    AW_FUN_Log(LOG_STEP, "test end.");
}

void *thrDoDMLSameRecord1(void *args)
{
    int ret = 0;
    AsyncUserDataT data = {0};
    GmcBatchT *batch = NULL;

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = useNameSpaceAsync(stmt, G_NAMESPACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcStmtT *stmtRoot = NULL;
    GmcNodeT *rootNode = NULL;
    ret = GmcAllocStmt(conn, &stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcStmtT *stmtList = NULL;
    GmcNodeT *listNode = NULL;
    ret = GmcAllocStmt(conn, &stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 10000; i++) {
        if (i % 200 == 0) {
            if (i % 1000 == 0) {
                system("gmsysview -q V\\$COM_DYN_CTX -f CTX_ID=0");
            }
        }
        // 开启事务
        ret = TestTransStartAsync(conn, GMC_FORCE_COMMIT_MODE_0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // merge事务操作
        ret = TestBatchPrepare(conn, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int dmlTimes = 0;

        ret = testGmcPrepareStmtByLabelName(stmtRoot, "main_label", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, stmtRoot);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmtRoot, &rootNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t f1Val = 1;
        ret = SetNodeProperty(rootNode, GMC_DATATYPE_UINT32, &f1Val, sizeof(uint32_t), "F1",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加批操作
        ret = GmcBatchAddDML(batch, stmtRoot);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        dmlTimes++;

        ret = testGmcPrepareStmtByLabelName(stmtList, "list_label", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmtRoot, stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmtList, &listNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t keyVal = 1;
        ret = GmcSetIndexKeyValue(stmtList, 1, GMC_DATATYPE_UINT32, &keyVal, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t listF1Val = 1;
        ret = SetNodeProperty(listNode, GMC_DATATYPE_UINT32, &listF1Val, sizeof(uint32_t),
                            "list_F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        dmlTimes++;

        // 批处理提交
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.succNum);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));
        // 提交事务
        ret = TestTransCommitAsync(conn);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 开启事务
        ret = TestTransStartAsync(conn, GMC_FORCE_COMMIT_MODE_0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // merge事务操作
        ret = TestBatchPrepare(conn, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        dmlTimes = 0;

        ret = testGmcPrepareStmtByLabelName(stmtRoot, "main_label", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, stmtRoot);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmtRoot, &rootNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        f1Val = 1;
        ret = SetNodeProperty(rootNode, GMC_DATATYPE_UINT32, &f1Val, sizeof(uint32_t), "F1",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加批操作
        ret = GmcBatchAddDML(batch, stmtRoot);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        dmlTimes++;

        ret = testGmcPrepareStmtByLabelName(stmtList, "list_label", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmtRoot, stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmtList, &listNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmtList, 1, GMC_DATATYPE_UINT32, &keyVal, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        listF1Val = 2;
        ret = SetNodeProperty(listNode, GMC_DATATYPE_UINT32, &listF1Val, sizeof(uint32_t),
                            "list_F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        dmlTimes++;

        // 批处理提交
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.succNum);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));
        // 提交事务
        ret = TestTransCommitAsync(conn);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 开启事务
        ret = TestTransStartAsync(conn, GMC_FORCE_COMMIT_MODE_0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // delete事务操作
        batch = NULL;
        ret = TestBatchPrepare(conn, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        dmlTimes = 0;

        ret = testGmcPrepareStmtByLabelName(stmtRoot, "main_label", GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, stmtRoot);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmtRoot, &rootNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, stmtRoot);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        dmlTimes++;

        ret = testGmcPrepareStmtByLabelName(stmtList, "list_label", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmtRoot, stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmtList, &listNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmtList, 1, GMC_DATATYPE_UINT32, &keyVal, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = SetNodeProperty(listNode, GMC_DATATYPE_UINT32, &listF1Val, sizeof(uint32_t),
                            "list_F1", GMC_YANG_PROPERTY_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        dmlTimes++;

        // 批处理提交
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.succNum);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 提交事务
        ret = TestTransCommitAsync(conn);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    GmcFreeStmt(stmtRoot);
    GmcFreeStmt(stmtList);

    // 断连
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void *thrDoDMLSameRecord2(void *args)
{
    int ret = 0;
    AsyncUserDataT data = {0};
    GmcBatchT *batch = NULL;

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = useNameSpaceAsync(stmt, G_NAMESPACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcStmtT *stmtRoot = NULL;
    GmcNodeT *rootNode = NULL;
    ret = GmcAllocStmt(conn, &stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcStmtT *stmtList = NULL;
    GmcNodeT *listNode = NULL;
    ret = GmcAllocStmt(conn, &stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 10000; i++) {
        if (i % 200 == 0) {
            if (i % 1000 == 0) {
                system("gmsysview -q V\\$COM_DYN_CTX -f CTX_ID=0");
            }
        }
        // 开启事务
        ret = TestTransStartAsync(conn, GMC_FORCE_COMMIT_MODE_0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // merge事务操作
        ret = TestBatchPrepare(conn, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int dmlTimes = 0;

        ret = testGmcPrepareStmtByLabelName(stmtRoot, "main_label", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, stmtRoot);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmtRoot, &rootNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t f1Val = 1;
        ret = SetNodeProperty(rootNode, GMC_DATATYPE_UINT32, &f1Val, sizeof(uint32_t), "F1",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加批操作
        ret = GmcBatchAddDML(batch, stmtRoot);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        dmlTimes++;

        ret = testGmcPrepareStmtByLabelName(stmtList, "list_label", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmtRoot, stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmtList, &listNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t keyVal = 1;
        ret = GmcSetIndexKeyValue(stmtList, 1, GMC_DATATYPE_UINT32, &keyVal, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        uint32_t listF2Val = 2;
        ret = SetNodeProperty(listNode, GMC_DATATYPE_UINT32, &listF2Val, sizeof(uint32_t),
                            "list_F2", GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        dmlTimes++;

        // 批处理提交
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.succNum);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));
        // 提交事务
        ret = TestTransCommitAsync(conn);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 开启事务
        ret = TestTransStartAsync(conn, GMC_FORCE_COMMIT_MODE_0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // merge事务操作
        ret = TestBatchPrepare(conn, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        dmlTimes = 0;

        ret = testGmcPrepareStmtByLabelName(stmtRoot, "main_label", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, stmtRoot);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmtRoot, &rootNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        f1Val = 1;
        ret = SetNodeProperty(rootNode, GMC_DATATYPE_UINT32, &f1Val, sizeof(uint32_t), "F1",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加批操作
        ret = GmcBatchAddDML(batch, stmtRoot);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        dmlTimes++;

        ret = testGmcPrepareStmtByLabelName(stmtList, "list_label", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmtRoot, stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmtList, &listNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmtList, 1, GMC_DATATYPE_UINT32, &keyVal, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置属性值
        listF2Val = 3;
        ret = SetNodeProperty(listNode, GMC_DATATYPE_UINT32, &listF2Val, sizeof(uint32_t),
                            "list_F2", GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        dmlTimes++;

        // 批处理提交
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.succNum);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));
        // 提交事务
        ret = TestTransCommitAsync(conn);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 开启事务
        ret = TestTransStartAsync(conn, GMC_FORCE_COMMIT_MODE_0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // delete事务操作
        batch = NULL;
        ret = TestBatchPrepare(conn, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        dmlTimes = 0;

        ret = testGmcPrepareStmtByLabelName(stmtRoot, "main_label", GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, stmtRoot);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmtRoot, &rootNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, stmtRoot);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        dmlTimes++;

        ret = testGmcPrepareStmtByLabelName(stmtList, "list_label", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmtRoot, stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmtList, &listNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmtList, 1, GMC_DATATYPE_UINT32, &keyVal, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = SetNodeProperty(listNode, GMC_DATATYPE_UINT32, &listF2Val, sizeof(uint32_t),
                            "list_F2", GMC_YANG_PROPERTY_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加批操作
        ret = GmcBatchAddDML(batch, stmtList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        dmlTimes++;

        // 批处理提交
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.succNum);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 提交事务
        ret = TestTransCommitAsync(conn);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    GmcFreeStmt(stmtRoot);
    GmcFreeStmt(stmtList);

    // 断连
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 003.并发两个线程，一个循环merge、merge旧数据、delete10000次，另一个循环merge、merge旧数据、delete10000次相同数据的不同字段
TEST_F(thr_trans_retry, Yang_115_thrTransRetry_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/diffFConflict_vertex_11.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/diffFConflict_edge.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ModelCheckAndDataCheck(g_connAsync, g_stmtAsync);

    pthread_t thrDML1;
    pthread_t thrDML2;
    ret = pthread_create(&thrDML1, NULL, thrDoDMLSameRecord1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thrDML2, NULL, thrDoDMLSameRecord2, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_join(thrDML1, NULL);
    pthread_join(thrDML2, NULL);

    AW_FUN_Log(LOG_STEP, "test end.");
}
