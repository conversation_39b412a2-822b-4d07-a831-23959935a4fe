#include "tools.h"

GmcTransDoneT userCb = (GmcTransDoneT)AsyncRetryCommitCb;

class diff_data_enhance : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void diff_data_enhance::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"forceCommitEnable=1\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"defaultIsolationLevel=2\""); // 事务隔离级别：可重复读
    system("sh $TEST_HOME/tools/modifyCfg.sh \"defaultTransactionType=1\""); // 默认namespace设为乐观
    system("sh $TEST_HOME/tools/start.sh");
    system("gmadmin -cfgName forceCommitEnable -cfgVal 1");
}

void diff_data_enhance::TearDownTestCase()
{
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
    system("gmadmin -cfgName forceCommitEnable -cfgVal 0");
}

void diff_data_enhance::SetUp()
{
    int ret;
    AsyncUserDataT data = {0};
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步建连
    ret = testGmcConnect(&g_connAsync, &g_stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步创建namespace
    TryDropNameSpace(g_stmtAsync, G_NAMESPACE);
    TryCreateNameSpace(g_stmtAsync, G_NAMESPACE);

    ret = GmcUseNamespaceAsync(g_stmtAsync, G_NAMESPACE, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 重试前事务涉及冲突与null，在此屏蔽
    AddWhiteList(GMERR_NULL_VALUE_NOT_ALLOWED);
    AddWhiteList(GMERR_RESTRICT_VIOLATION);

    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    AddWhiteList(GMERR_NO_DATA);
}

void diff_data_enhance::TearDown()
{
    int ret;
    AsyncUserDataT data = {0};

    // 异步删除namespace
    TryDropNameSpace(g_stmtAsync, G_NAMESPACE);

    // 断连
    ret = testGmcDisconnect(g_connAsync, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_END();
    GmcDetachAllShmSeg();
    testEnvClean();
}

// 001.list同表不同记录被create
TEST_F(diff_data_enhance, Yang_115_diff_data_enhance_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/diff_data_enhance_vertex_1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/diff_data_enhance_edge_1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ModelCheckAndDataCheck(g_connAsync, g_stmtAsync);

    AsyncUserDataT data = {0};
    GmcBatchT *batch = NULL;

    GmcConnT *conn1 = NULL;
    GmcConnT *conn2 = NULL;
    GmcStmtT *stmt1 = NULL;
    GmcStmtT *stmt2 = NULL;
    ret = testGmcConnect(&conn1, &stmt1, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = useNameSpaceAsync(stmt1, G_NAMESPACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn2, &stmt2, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = useNameSpaceAsync(stmt2, G_NAMESPACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcStmtT *stmtRoot = NULL;
    GmcNodeT *nodeRoot = NULL;
    GmcStmtT *stmtList = NULL;
    GmcNodeT *nodeList = NULL;

    // 开启事务
    ret = TestTransStartAsync(conn1, GMC_FORCE_COMMIT_DISABLED);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestTransStartAsync(conn2, GMC_FORCE_COMMIT_MODE_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1操作
    // container
    ret = TestBatchPrepare(conn1, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int dmlTimes = 0;
    ret = GmcAllocStmt(conn1, &stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtRoot, "main_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtRoot, &nodeRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t f1Val = 1;
    ret = SetNodeProperty(nodeRoot, GMC_DATATYPE_UINT32, &f1Val, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;
    // list
    ret = GmcAllocStmt(conn1, &stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtList, "list_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmtRoot, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtList, &nodeList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    uint32_t listF0Val = 0;
    ret = SetNodeProperty(nodeList, GMC_DATATYPE_UINT32, &listF0Val, sizeof(uint32_t),
                        "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t listF1Val = 1;
    ret = SetNodeProperty(nodeList, GMC_DATATYPE_UINT32, &listF1Val, sizeof(uint32_t),
                        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    GmcFreeStmt(stmtRoot);
    GmcFreeStmt(stmtList);
    // 事务2操作
    // container
    ret = TestBatchPrepare(conn2, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes = 0;
    ret = GmcAllocStmt(conn2, &stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtRoot, "main_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtRoot, &nodeRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    f1Val = 1;
    ret = SetNodeProperty(nodeRoot, GMC_DATATYPE_UINT32, &f1Val, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;
    // list
    ret = GmcAllocStmt(conn2, &stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtList, "list_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmtRoot, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtList, &nodeList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    listF0Val = 1;
    ret = SetNodeProperty(nodeList, GMC_DATATYPE_UINT32, &listF0Val, sizeof(uint32_t),
                        "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    listF1Val = 2;
    ret = SetNodeProperty(nodeList, GMC_DATATYPE_UINT32, &listF1Val, sizeof(uint32_t),
                        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestTransCommitAsync(conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree查询
    TestSubtreeFilter(g_connAsync, g_stmtAsync, "main_label", "subtreeReplyJson/diff_data_enhance_reply_1.json");

    // 断连
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn2, stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002.list同表不同记录被create、update
TEST_F(diff_data_enhance, Yang_115_diff_data_enhance_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/diff_data_enhance_vertex_1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/diff_data_enhance_edge_1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ModelCheckAndDataCheck(g_connAsync, g_stmtAsync);

    AsyncUserDataT data = {0};
    GmcBatchT *batch = NULL;

    GmcConnT *conn1 = NULL;
    GmcConnT *conn2 = NULL;
    GmcStmtT *stmt1 = NULL;
    GmcStmtT *stmt2 = NULL;
    ret = testGmcConnect(&conn1, &stmt1, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = useNameSpaceAsync(stmt1, G_NAMESPACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn2, &stmt2, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = useNameSpaceAsync(stmt2, G_NAMESPACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcStmtT *stmtRoot = NULL;
    GmcNodeT *nodeRoot = NULL;
    GmcStmtT *stmtList = NULL;
    GmcNodeT *nodeList = NULL;

    // 预置数据
    // 开启事务
    ret = TestTransStartAsync(g_connAsync, GMC_FORCE_COMMIT_DISABLED);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // container
    ret = TestBatchPrepare(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int dmlTimes = 0;
    ret = GmcAllocStmt(g_connAsync, &stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtRoot, "main_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtRoot, &nodeRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t f1Val = 1;
    ret = SetNodeProperty(nodeRoot, GMC_DATATYPE_UINT32, &f1Val, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;
    // list
    ret = GmcAllocStmt(g_connAsync, &stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtList, "list_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmtRoot, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtList, &nodeList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    uint32_t listF0Val1 = 1;
    ret = SetNodeProperty(nodeList, GMC_DATATYPE_UINT32, &listF0Val1, sizeof(uint32_t),
                        "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t listF1Val1 = 2;
    ret = SetNodeProperty(nodeList, GMC_DATATYPE_UINT32, &listF1Val1, sizeof(uint32_t),
                        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启事务
    ret = TestTransStartAsync(conn1, GMC_FORCE_COMMIT_DISABLED);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestTransStartAsync(conn2, GMC_FORCE_COMMIT_MODE_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1操作
    // container
    ret = TestBatchPrepare(conn1, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes = 0;
    ret = GmcAllocStmt(conn1, &stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtRoot, "main_label", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtRoot, &nodeRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;
    // list
    ret = GmcAllocStmt(conn1, &stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtList, "list_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmtRoot, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtList, &nodeList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    uint32_t listF0Val2 = 0;
    ret = SetNodeProperty(nodeList, GMC_DATATYPE_UINT32, &listF0Val2, sizeof(uint32_t),
                        "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t listF1Val2 = 1;
    ret = SetNodeProperty(nodeList, GMC_DATATYPE_UINT32, &listF1Val2, sizeof(uint32_t),
                        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    GmcFreeStmt(stmtRoot);
    GmcFreeStmt(stmtList);
    // 事务2操作
    // container
    ret = TestBatchPrepare(conn2, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes = 0;
    ret = GmcAllocStmt(conn2, &stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtRoot, "main_label", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtRoot, &nodeRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;
    // list
    ret = GmcAllocStmt(conn2, &stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtList, "list_label", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmtRoot, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtList, &nodeList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmtList, 1, GMC_DATATYPE_UINT32, &listF0Val1, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    listF1Val1 = 3;
    ret = SetNodeProperty(nodeList, GMC_DATATYPE_UINT32, &listF1Val1, sizeof(uint32_t),
                        "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestTransCommitAsync(conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree查询
    TestSubtreeFilter(g_connAsync, g_stmtAsync, "main_label", "subtreeReplyJson/diff_data_enhance_reply_2.json");

    // 断连
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn2, stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003.list同表不同记录被create、delete
TEST_F(diff_data_enhance, Yang_115_diff_data_enhance_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/diff_data_enhance_vertex_1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/diff_data_enhance_edge_1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ModelCheckAndDataCheck(g_connAsync, g_stmtAsync);

    AsyncUserDataT data = {0};
    GmcBatchT *batch = NULL;

    GmcConnT *conn1 = NULL;
    GmcConnT *conn2 = NULL;
    GmcStmtT *stmt1 = NULL;
    GmcStmtT *stmt2 = NULL;
    ret = testGmcConnect(&conn1, &stmt1, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = useNameSpaceAsync(stmt1, G_NAMESPACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn2, &stmt2, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = useNameSpaceAsync(stmt2, G_NAMESPACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcStmtT *stmtRoot = NULL;
    GmcNodeT *nodeRoot = NULL;
    GmcStmtT *stmtList = NULL;
    GmcNodeT *nodeList = NULL;

    // 预置数据
    // 开启事务
    ret = TestTransStartAsync(g_connAsync, GMC_FORCE_COMMIT_DISABLED);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // container
    ret = TestBatchPrepare(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int dmlTimes = 0;
    ret = GmcAllocStmt(g_connAsync, &stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtRoot, "main_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtRoot, &nodeRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t f1Val = 1;
    ret = SetNodeProperty(nodeRoot, GMC_DATATYPE_UINT32, &f1Val, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;
    // list
    ret = GmcAllocStmt(g_connAsync, &stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtList, "list_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmtRoot, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtList, &nodeList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    uint32_t listF0Val1 = 1;
    ret = SetNodeProperty(nodeList, GMC_DATATYPE_UINT32, &listF0Val1, sizeof(uint32_t),
                        "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t listF1Val1 = 2;
    ret = SetNodeProperty(nodeList, GMC_DATATYPE_UINT32, &listF1Val1, sizeof(uint32_t),
                        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启事务
    ret = TestTransStartAsync(conn1, GMC_FORCE_COMMIT_DISABLED);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestTransStartAsync(conn2, GMC_FORCE_COMMIT_MODE_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1操作
    // container
    ret = TestBatchPrepare(conn1, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes = 0;
    ret = GmcAllocStmt(conn1, &stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtRoot, "main_label", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtRoot, &nodeRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;
    // list
    ret = GmcAllocStmt(conn1, &stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtList, "list_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmtRoot, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtList, &nodeList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    uint32_t listF0Val2 = 0;
    ret = SetNodeProperty(nodeList, GMC_DATATYPE_UINT32, &listF0Val2, sizeof(uint32_t),
                        "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t listF1Val2 = 1;
    ret = SetNodeProperty(nodeList, GMC_DATATYPE_UINT32, &listF1Val2, sizeof(uint32_t),
                        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    GmcFreeStmt(stmtRoot);
    GmcFreeStmt(stmtList);
    // 事务2操作
    // container
    ret = TestBatchPrepare(conn2, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes = 0;
    ret = GmcAllocStmt(conn2, &stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtRoot, "main_label", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtRoot, &nodeRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;
    // list
    ret = GmcAllocStmt(conn2, &stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtList, "list_label", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmtRoot, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtList, &nodeList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmtList, 1, GMC_DATATYPE_UINT32, &listF0Val1, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestTransCommitAsync(conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree查询
    TestSubtreeFilter(g_connAsync, g_stmtAsync, "main_label", "subtreeReplyJson/diff_data_enhance_reply_3.json");

    // 断连
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn2, stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004.list同表不同记录被update、create
TEST_F(diff_data_enhance, Yang_115_diff_data_enhance_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/diff_data_enhance_vertex_1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/diff_data_enhance_edge_1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ModelCheckAndDataCheck(g_connAsync, g_stmtAsync);

    AsyncUserDataT data = {0};
    GmcBatchT *batch = NULL;

    GmcConnT *conn1 = NULL;
    GmcConnT *conn2 = NULL;
    GmcStmtT *stmt1 = NULL;
    GmcStmtT *stmt2 = NULL;
    ret = testGmcConnect(&conn1, &stmt1, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = useNameSpaceAsync(stmt1, G_NAMESPACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn2, &stmt2, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = useNameSpaceAsync(stmt2, G_NAMESPACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcStmtT *stmtRoot = NULL;
    GmcNodeT *nodeRoot = NULL;
    GmcStmtT *stmtList = NULL;
    GmcNodeT *nodeList = NULL;

    // 预置数据
    // 开启事务
    ret = TestTransStartAsync(g_connAsync, GMC_FORCE_COMMIT_DISABLED);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // container
    ret = TestBatchPrepare(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int dmlTimes = 0;
    ret = GmcAllocStmt(g_connAsync, &stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtRoot, "main_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtRoot, &nodeRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t f1Val = 1;
    ret = SetNodeProperty(nodeRoot, GMC_DATATYPE_UINT32, &f1Val, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;
    // list
    ret = GmcAllocStmt(g_connAsync, &stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtList, "list_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmtRoot, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtList, &nodeList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    uint32_t listF0Val1 = 0;
    ret = SetNodeProperty(nodeList, GMC_DATATYPE_UINT32, &listF0Val1, sizeof(uint32_t),
                        "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t listF1Val1 = 1;
    ret = SetNodeProperty(nodeList, GMC_DATATYPE_UINT32, &listF1Val1, sizeof(uint32_t),
                        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启事务
    ret = TestTransStartAsync(conn1, GMC_FORCE_COMMIT_DISABLED);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestTransStartAsync(conn2, GMC_FORCE_COMMIT_MODE_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1操作
    // container
    ret = TestBatchPrepare(conn1, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes = 0;
    ret = GmcAllocStmt(conn1, &stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtRoot, "main_label", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtRoot, &nodeRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;
    // list
    ret = GmcAllocStmt(conn1, &stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtList, "list_label", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmtRoot, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtList, &nodeList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmtList, 1, GMC_DATATYPE_UINT32, &listF0Val1, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    listF1Val1 = 2;
    ret = SetNodeProperty(nodeList, GMC_DATATYPE_UINT32, &listF1Val1, sizeof(uint32_t),
                        "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    GmcFreeStmt(stmtRoot);
    GmcFreeStmt(stmtList);
    // 事务2操作
    // container
    ret = TestBatchPrepare(conn2, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes = 0;
    ret = GmcAllocStmt(conn2, &stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtRoot, "main_label", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtRoot, &nodeRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;
    // list
    ret = GmcAllocStmt(conn2, &stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtList, "list_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmtRoot, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtList, &nodeList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    uint32_t listF0Val2 = 1;
    ret = SetNodeProperty(nodeList, GMC_DATATYPE_UINT32, &listF0Val2, sizeof(uint32_t),
                        "F0", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t listF1Val2 = 2;
    ret = SetNodeProperty(nodeList, GMC_DATATYPE_UINT32, &listF1Val2, sizeof(uint32_t),
                        "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestTransCommitAsync(conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree查询
    TestSubtreeFilter(g_connAsync, g_stmtAsync, "main_label", "subtreeReplyJson/diff_data_enhance_reply_4.json");

    // 断连
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn2, stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005.list同表不同记录被update
TEST_F(diff_data_enhance, Yang_115_diff_data_enhance_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/diff_data_enhance_vertex_1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/diff_data_enhance_edge_1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ModelCheckAndDataCheck(g_connAsync, g_stmtAsync);

    AsyncUserDataT data = {0};
    GmcBatchT *batch = NULL;

    GmcConnT *conn1 = NULL;
    GmcConnT *conn2 = NULL;
    GmcStmtT *stmt1 = NULL;
    GmcStmtT *stmt2 = NULL;
    ret = testGmcConnect(&conn1, &stmt1, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = useNameSpaceAsync(stmt1, G_NAMESPACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn2, &stmt2, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = useNameSpaceAsync(stmt2, G_NAMESPACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcStmtT *stmtRoot = NULL;
    GmcNodeT *nodeRoot = NULL;
    GmcStmtT *stmtList = NULL;
    GmcNodeT *nodeList = NULL;

    // 预置数据
    // 开启事务
    ret = TestTransStartAsync(g_connAsync, GMC_FORCE_COMMIT_DISABLED);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // container
    ret = TestBatchPrepare(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int dmlTimes = 0;
    ret = GmcAllocStmt(g_connAsync, &stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtRoot, "main_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtRoot, &nodeRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t f1Val = 1;
    ret = SetNodeProperty(nodeRoot, GMC_DATATYPE_UINT32, &f1Val, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;
    // list
    // data1
    ret = GmcAllocStmt(g_connAsync, &stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtList, "list_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmtRoot, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtList, &nodeList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    uint32_t listF0Val1 = 0;
    ret = SetNodeProperty(nodeList, GMC_DATATYPE_UINT32, &listF0Val1, sizeof(uint32_t),
                        "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t listF1Val1 = 1;
    ret = SetNodeProperty(nodeList, GMC_DATATYPE_UINT32, &listF1Val1, sizeof(uint32_t),
                        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;
    // data2
    ret = GmcAllocStmt(g_connAsync, &stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtList, "list_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmtRoot, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtList, &nodeList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    uint32_t listF0Val2 = 1;
    ret = SetNodeProperty(nodeList, GMC_DATATYPE_UINT32, &listF0Val2, sizeof(uint32_t),
                        "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t listF1Val2 = 2;
    ret = SetNodeProperty(nodeList, GMC_DATATYPE_UINT32, &listF1Val2, sizeof(uint32_t),
                        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启事务
    ret = TestTransStartAsync(conn1, GMC_FORCE_COMMIT_DISABLED);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestTransStartAsync(conn2, GMC_FORCE_COMMIT_MODE_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1操作
    // container
    ret = TestBatchPrepare(conn1, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes = 0;
    ret = GmcAllocStmt(conn1, &stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtRoot, "main_label", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtRoot, &nodeRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;
    // list
    ret = GmcAllocStmt(conn1, &stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtList, "list_label", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmtRoot, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtList, &nodeList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmtList, 1, GMC_DATATYPE_UINT32, &listF0Val1, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    listF1Val1 = 2;
    ret = SetNodeProperty(nodeList, GMC_DATATYPE_UINT32, &listF1Val1, sizeof(uint32_t),
                        "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    GmcFreeStmt(stmtRoot);
    GmcFreeStmt(stmtList);
    // 事务2操作
    // container
    ret = TestBatchPrepare(conn2, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes = 0;
    ret = GmcAllocStmt(conn2, &stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtRoot, "main_label", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtRoot, &nodeRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;
    // list
    ret = GmcAllocStmt(conn2, &stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtList, "list_label", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmtRoot, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtList, &nodeList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmtList, 1, GMC_DATATYPE_UINT32, &listF0Val2, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    listF1Val2 = 3;
    ret = SetNodeProperty(nodeList, GMC_DATATYPE_UINT32, &listF1Val2, sizeof(uint32_t),
                        "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestTransCommitAsync(conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree查询
    TestSubtreeFilter(g_connAsync, g_stmtAsync, "main_label", "subtreeReplyJson/diff_data_enhance_reply_5.json");

    // 断连
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn2, stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006.list同表不同记录被update、delete
TEST_F(diff_data_enhance, Yang_115_diff_data_enhance_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/diff_data_enhance_vertex_1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/diff_data_enhance_edge_1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ModelCheckAndDataCheck(g_connAsync, g_stmtAsync);

    AsyncUserDataT data = {0};
    GmcBatchT *batch = NULL;

    GmcConnT *conn1 = NULL;
    GmcConnT *conn2 = NULL;
    GmcStmtT *stmt1 = NULL;
    GmcStmtT *stmt2 = NULL;
    ret = testGmcConnect(&conn1, &stmt1, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = useNameSpaceAsync(stmt1, G_NAMESPACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn2, &stmt2, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = useNameSpaceAsync(stmt2, G_NAMESPACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcStmtT *stmtRoot = NULL;
    GmcNodeT *nodeRoot = NULL;
    GmcStmtT *stmtList = NULL;
    GmcNodeT *nodeList = NULL;

    // 预置数据
    // 开启事务
    ret = TestTransStartAsync(g_connAsync, GMC_FORCE_COMMIT_DISABLED);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // container
    ret = TestBatchPrepare(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int dmlTimes = 0;
    ret = GmcAllocStmt(g_connAsync, &stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtRoot, "main_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtRoot, &nodeRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t f1Val = 1;
    ret = SetNodeProperty(nodeRoot, GMC_DATATYPE_UINT32, &f1Val, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;
    // list
    // data1
    ret = GmcAllocStmt(g_connAsync, &stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtList, "list_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmtRoot, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtList, &nodeList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    uint32_t listF0Val1 = 0;
    ret = SetNodeProperty(nodeList, GMC_DATATYPE_UINT32, &listF0Val1, sizeof(uint32_t),
                        "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t listF1Val1 = 1;
    ret = SetNodeProperty(nodeList, GMC_DATATYPE_UINT32, &listF1Val1, sizeof(uint32_t),
                        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;
    // data2
    ret = GmcAllocStmt(g_connAsync, &stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtList, "list_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmtRoot, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtList, &nodeList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    uint32_t listF0Val2 = 1;
    ret = SetNodeProperty(nodeList, GMC_DATATYPE_UINT32, &listF0Val2, sizeof(uint32_t),
                        "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t listF1Val2 = 2;
    ret = SetNodeProperty(nodeList, GMC_DATATYPE_UINT32, &listF1Val2, sizeof(uint32_t),
                        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启事务
    ret = TestTransStartAsync(conn1, GMC_FORCE_COMMIT_DISABLED);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestTransStartAsync(conn2, GMC_FORCE_COMMIT_MODE_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1操作
    // container
    ret = TestBatchPrepare(conn1, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes = 0;
    ret = GmcAllocStmt(conn1, &stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtRoot, "main_label", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtRoot, &nodeRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;
    // list
    ret = GmcAllocStmt(conn1, &stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtList, "list_label", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmtRoot, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtList, &nodeList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmtList, 1, GMC_DATATYPE_UINT32, &listF0Val1, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    listF1Val1 = 2;
    ret = SetNodeProperty(nodeList, GMC_DATATYPE_UINT32, &listF1Val1, sizeof(uint32_t),
                        "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    GmcFreeStmt(stmtRoot);
    GmcFreeStmt(stmtList);
    // 事务2操作
    // container
    ret = TestBatchPrepare(conn2, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes = 0;
    ret = GmcAllocStmt(conn2, &stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtRoot, "main_label", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtRoot, &nodeRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;
    // list
    ret = GmcAllocStmt(conn2, &stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtList, "list_label", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmtRoot, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtList, &nodeList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmtList, 1, GMC_DATATYPE_UINT32, &listF0Val2, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestTransCommitAsync(conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree查询
    TestSubtreeFilter(g_connAsync, g_stmtAsync, "main_label", "subtreeReplyJson/diff_data_enhance_reply_6.json");

    // 断连
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn2, stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007.list同表不同记录被delete、create
TEST_F(diff_data_enhance, Yang_115_diff_data_enhance_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/diff_data_enhance_vertex_1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/diff_data_enhance_edge_1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ModelCheckAndDataCheck(g_connAsync, g_stmtAsync);

    AsyncUserDataT data = {0};
    GmcBatchT *batch = NULL;

    GmcConnT *conn1 = NULL;
    GmcConnT *conn2 = NULL;
    GmcStmtT *stmt1 = NULL;
    GmcStmtT *stmt2 = NULL;
    ret = testGmcConnect(&conn1, &stmt1, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = useNameSpaceAsync(stmt1, G_NAMESPACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn2, &stmt2, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = useNameSpaceAsync(stmt2, G_NAMESPACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcStmtT *stmtRoot = NULL;
    GmcNodeT *nodeRoot = NULL;
    GmcStmtT *stmtList = NULL;
    GmcNodeT *nodeList = NULL;

    // 预置数据
    // 开启事务
    ret = TestTransStartAsync(g_connAsync, GMC_FORCE_COMMIT_DISABLED);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // container
    ret = TestBatchPrepare(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int dmlTimes = 0;
    ret = GmcAllocStmt(g_connAsync, &stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtRoot, "main_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtRoot, &nodeRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t f1Val = 1;
    ret = SetNodeProperty(nodeRoot, GMC_DATATYPE_UINT32, &f1Val, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;
    // list
    ret = GmcAllocStmt(g_connAsync, &stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtList, "list_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmtRoot, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtList, &nodeList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    uint32_t listF0Val1 = 0;
    ret = SetNodeProperty(nodeList, GMC_DATATYPE_UINT32, &listF0Val1, sizeof(uint32_t),
                        "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t listF1Val1 = 1;
    ret = SetNodeProperty(nodeList, GMC_DATATYPE_UINT32, &listF1Val1, sizeof(uint32_t),
                        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启事务
    ret = TestTransStartAsync(conn1, GMC_FORCE_COMMIT_DISABLED);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestTransStartAsync(conn2, GMC_FORCE_COMMIT_MODE_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1操作
    // container
    ret = TestBatchPrepare(conn1, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes = 0;
    ret = GmcAllocStmt(conn1, &stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtRoot, "main_label", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtRoot, &nodeRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;
    // list
    ret = GmcAllocStmt(conn1, &stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtList, "list_label", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmtRoot, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtList, &nodeList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmtList, 1, GMC_DATATYPE_UINT32, &listF0Val1, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    GmcFreeStmt(stmtRoot);
    GmcFreeStmt(stmtList);
    // 事务2操作
    // container
    ret = TestBatchPrepare(conn2, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes = 0;
    ret = GmcAllocStmt(conn2, &stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtRoot, "main_label", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtRoot, &nodeRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;
    // list
    ret = GmcAllocStmt(conn2, &stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtList, "list_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmtRoot, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtList, &nodeList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    uint32_t listF0Val2 = 1;
    ret = SetNodeProperty(nodeList, GMC_DATATYPE_UINT32, &listF0Val2, sizeof(uint32_t),
                        "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t listF1Val2 = 2;
    ret = SetNodeProperty(nodeList, GMC_DATATYPE_UINT32, &listF1Val2, sizeof(uint32_t),
                        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestTransCommitAsync(conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree查询
    TestSubtreeFilter(g_connAsync, g_stmtAsync, "main_label", "subtreeReplyJson/diff_data_enhance_reply_7.json");

    // 断连
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn2, stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008.list同表不同记录被delete、update
TEST_F(diff_data_enhance, Yang_115_diff_data_enhance_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/diff_data_enhance_vertex_1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/diff_data_enhance_edge_1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ModelCheckAndDataCheck(g_connAsync, g_stmtAsync);

    AsyncUserDataT data = {0};
    GmcBatchT *batch = NULL;

    GmcConnT *conn1 = NULL;
    GmcConnT *conn2 = NULL;
    GmcStmtT *stmt1 = NULL;
    GmcStmtT *stmt2 = NULL;
    ret = testGmcConnect(&conn1, &stmt1, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = useNameSpaceAsync(stmt1, G_NAMESPACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn2, &stmt2, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = useNameSpaceAsync(stmt2, G_NAMESPACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcStmtT *stmtRoot = NULL;
    GmcNodeT *nodeRoot = NULL;
    GmcStmtT *stmtList = NULL;
    GmcNodeT *nodeList = NULL;

    // 预置数据
    // 开启事务
    ret = TestTransStartAsync(g_connAsync, GMC_FORCE_COMMIT_DISABLED);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // container
    ret = TestBatchPrepare(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int dmlTimes = 0;
    ret = GmcAllocStmt(g_connAsync, &stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtRoot, "main_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtRoot, &nodeRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t f1Val = 1;
    ret = SetNodeProperty(nodeRoot, GMC_DATATYPE_UINT32, &f1Val, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;
    // list
    // data1
    ret = GmcAllocStmt(g_connAsync, &stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtList, "list_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmtRoot, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtList, &nodeList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    uint32_t listF0Val1 = 0;
    ret = SetNodeProperty(nodeList, GMC_DATATYPE_UINT32, &listF0Val1, sizeof(uint32_t),
                        "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t listF1Val1 = 1;
    ret = SetNodeProperty(nodeList, GMC_DATATYPE_UINT32, &listF1Val1, sizeof(uint32_t),
                        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;

    // data2
    ret = GmcAllocStmt(g_connAsync, &stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtList, "list_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmtRoot, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtList, &nodeList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    uint32_t listF0Val2 = 1;
    ret = SetNodeProperty(nodeList, GMC_DATATYPE_UINT32, &listF0Val2, sizeof(uint32_t),
                        "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t listF1Val2 = 2;
    ret = SetNodeProperty(nodeList, GMC_DATATYPE_UINT32, &listF1Val2, sizeof(uint32_t),
                        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启事务
    ret = TestTransStartAsync(conn1, GMC_FORCE_COMMIT_DISABLED);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestTransStartAsync(conn2, GMC_FORCE_COMMIT_MODE_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1操作
    // container
    ret = TestBatchPrepare(conn1, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes = 0;
    ret = GmcAllocStmt(conn1, &stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtRoot, "main_label", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtRoot, &nodeRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;
    // list
    ret = GmcAllocStmt(conn1, &stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtList, "list_label", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmtRoot, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtList, &nodeList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmtList, 1, GMC_DATATYPE_UINT32, &listF0Val1, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    GmcFreeStmt(stmtRoot);
    GmcFreeStmt(stmtList);
    // 事务2操作
    // container
    ret = TestBatchPrepare(conn2, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes = 0;
    ret = GmcAllocStmt(conn2, &stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtRoot, "main_label", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtRoot, &nodeRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;
    // list
    ret = GmcAllocStmt(conn2, &stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtList, "list_label", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmtRoot, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtList, &nodeList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmtList, 1, GMC_DATATYPE_UINT32, &listF0Val2, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    listF1Val2 = 3;
    ret = SetNodeProperty(nodeList, GMC_DATATYPE_UINT32, &listF1Val2, sizeof(uint32_t),
                        "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestTransCommitAsync(conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree查询
    TestSubtreeFilter(g_connAsync, g_stmtAsync, "main_label", "subtreeReplyJson/diff_data_enhance_reply_8.json");

    // 断连
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn2, stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 009.list同表不同记录被delete
TEST_F(diff_data_enhance, Yang_115_diff_data_enhance_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/diff_data_enhance_vertex_1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/diff_data_enhance_edge_1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ModelCheckAndDataCheck(g_connAsync, g_stmtAsync);

    AsyncUserDataT data = {0};
    GmcBatchT *batch = NULL;

    GmcConnT *conn1 = NULL;
    GmcConnT *conn2 = NULL;
    GmcStmtT *stmt1 = NULL;
    GmcStmtT *stmt2 = NULL;
    ret = testGmcConnect(&conn1, &stmt1, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = useNameSpaceAsync(stmt1, G_NAMESPACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn2, &stmt2, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = useNameSpaceAsync(stmt2, G_NAMESPACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcStmtT *stmtRoot = NULL;
    GmcNodeT *nodeRoot = NULL;
    GmcStmtT *stmtList = NULL;
    GmcNodeT *nodeList = NULL;

    // 预置数据
    // 开启事务
    ret = TestTransStartAsync(g_connAsync, GMC_FORCE_COMMIT_DISABLED);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // container
    ret = TestBatchPrepare(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int dmlTimes = 0;
    ret = GmcAllocStmt(g_connAsync, &stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtRoot, "main_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtRoot, &nodeRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t f1Val = 1;
    ret = SetNodeProperty(nodeRoot, GMC_DATATYPE_UINT32, &f1Val, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;
    // list
    // data1
    ret = GmcAllocStmt(g_connAsync, &stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtList, "list_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmtRoot, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtList, &nodeList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    uint32_t listF0Val1 = 0;
    ret = SetNodeProperty(nodeList, GMC_DATATYPE_UINT32, &listF0Val1, sizeof(uint32_t),
                        "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t listF1Val1 = 1;
    ret = SetNodeProperty(nodeList, GMC_DATATYPE_UINT32, &listF1Val1, sizeof(uint32_t),
                        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;

    // data2
    ret = GmcAllocStmt(g_connAsync, &stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtList, "list_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmtRoot, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtList, &nodeList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    uint32_t listF0Val2 = 1;
    ret = SetNodeProperty(nodeList, GMC_DATATYPE_UINT32, &listF0Val2, sizeof(uint32_t),
                        "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t listF1Val2 = 2;
    ret = SetNodeProperty(nodeList, GMC_DATATYPE_UINT32, &listF1Val2, sizeof(uint32_t),
                        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启事务
    ret = TestTransStartAsync(conn1, GMC_FORCE_COMMIT_DISABLED);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestTransStartAsync(conn2, GMC_FORCE_COMMIT_MODE_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1操作
    // container
    ret = TestBatchPrepare(conn1, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes = 0;
    ret = GmcAllocStmt(conn1, &stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtRoot, "main_label", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtRoot, &nodeRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;
    // list
    ret = GmcAllocStmt(conn1, &stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtList, "list_label", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmtRoot, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtList, &nodeList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmtList, 1, GMC_DATATYPE_UINT32, &listF0Val1, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    GmcFreeStmt(stmtRoot);
    GmcFreeStmt(stmtList);
    // 事务2操作
    // container
    ret = TestBatchPrepare(conn2, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes = 0;
    ret = GmcAllocStmt(conn2, &stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtRoot, "main_label", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtRoot, &nodeRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;
    // list
    ret = GmcAllocStmt(conn2, &stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtList, "list_label", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmtRoot, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtList, &nodeList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmtList, 1, GMC_DATATYPE_UINT32, &listF0Val2, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestTransCommitAsync(conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree查询
    TestSubtreeFilter(g_connAsync, g_stmtAsync, "main_label", "subtreeReplyJson/diff_data_enhance_reply_9.json");

    // 断连
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn2, stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010.leaf-list同表不同记录被create
TEST_F(diff_data_enhance, Yang_115_diff_data_enhance_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/diff_data_enhance_vertex_10.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/diff_data_enhance_edge_10.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ModelCheckAndDataCheck(g_connAsync, g_stmtAsync);

    AsyncUserDataT data = {0};
    GmcBatchT *batch = NULL;

    GmcConnT *conn1 = NULL;
    GmcConnT *conn2 = NULL;
    GmcStmtT *stmt1 = NULL;
    GmcStmtT *stmt2 = NULL;
    ret = testGmcConnect(&conn1, &stmt1, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = useNameSpaceAsync(stmt1, G_NAMESPACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn2, &stmt2, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = useNameSpaceAsync(stmt2, G_NAMESPACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcStmtT *stmtRoot = NULL;
    GmcNodeT *nodeRoot = NULL;
    GmcStmtT *stmtLeafList = NULL;
    GmcNodeT *nodeLeafList = NULL;

    // 开启事务
    ret = TestTransStartAsync(conn1, GMC_FORCE_COMMIT_DISABLED);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestTransStartAsync(conn2, GMC_FORCE_COMMIT_MODE_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1操作
    // container
    ret = TestBatchPrepare(conn1, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int dmlTimes = 0;
    ret = GmcAllocStmt(conn1, &stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtRoot, "main_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtRoot, &nodeRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t f1Val = 1;
    ret = SetNodeProperty(nodeRoot, GMC_DATATYPE_UINT32, &f1Val, sizeof(uint32_t),
                        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;
    // list
    ret = GmcAllocStmt(conn1, &stmtLeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtLeafList, "leaf-list_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmtRoot, stmtLeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtLeafList, &nodeLeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    uint32_t listF0Val1 = 1;
    ret = SetNodeProperty(nodeLeafList, GMC_DATATYPE_UINT32, &listF0Val1, sizeof(uint32_t),
                        "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmtLeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    GmcFreeStmt(stmtRoot);
    GmcFreeStmt(stmtLeafList);
    // 事务2操作
    // container
    ret = TestBatchPrepare(conn2, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes = 0;
    ret = GmcAllocStmt(conn2, &stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtRoot, "main_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtRoot, &nodeRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = SetNodeProperty(nodeRoot, GMC_DATATYPE_UINT32, &f1Val, sizeof(uint32_t),
                        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;
    // list
    ret = GmcAllocStmt(conn2, &stmtLeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtLeafList, "leaf-list_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmtRoot, stmtLeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtLeafList, &nodeLeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    uint32_t listF0Val2 = 2;
    ret = SetNodeProperty(nodeLeafList, GMC_DATATYPE_UINT32, &listF0Val2, sizeof(uint32_t),
                        "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmtLeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestTransCommitAsync(conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree查询
    TestSubtreeFilter(g_connAsync, g_stmtAsync, "main_label", "subtreeReplyJson/diff_data_enhance_reply_10.json");

    // 断连
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn2, stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 011.leaf-list同表不同记录被create、delete
TEST_F(diff_data_enhance, Yang_115_diff_data_enhance_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/diff_data_enhance_vertex_10.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/diff_data_enhance_edge_10.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ModelCheckAndDataCheck(g_connAsync, g_stmtAsync);

    AsyncUserDataT data = {0};
    GmcBatchT *batch = NULL;

    GmcConnT *conn1 = NULL;
    GmcConnT *conn2 = NULL;
    GmcStmtT *stmt1 = NULL;
    GmcStmtT *stmt2 = NULL;
    ret = testGmcConnect(&conn1, &stmt1, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = useNameSpaceAsync(stmt1, G_NAMESPACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn2, &stmt2, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = useNameSpaceAsync(stmt2, G_NAMESPACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcStmtT *stmtRoot = NULL;
    GmcNodeT *nodeRoot = NULL;
    GmcStmtT *stmtLeafList = NULL;
    GmcNodeT *nodeLeafList = NULL;

    // 预置数据
    // 开启事务
    ret = TestTransStartAsync(g_connAsync, GMC_FORCE_COMMIT_DISABLED);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // container
    ret = TestBatchPrepare(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int dmlTimes = 0;
    ret = GmcAllocStmt(g_connAsync, &stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtRoot, "main_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtRoot, &nodeRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t f1Val = 1;
    ret = SetNodeProperty(nodeRoot, GMC_DATATYPE_UINT32, &f1Val, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;
    // list
    ret = GmcAllocStmt(g_connAsync, &stmtLeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtLeafList, "leaf-list_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmtRoot, stmtLeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtLeafList, &nodeLeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    uint32_t listF0Val2 = 2;
    ret = SetNodeProperty(nodeLeafList, GMC_DATATYPE_UINT32, &listF0Val2, sizeof(uint32_t),
                        "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtLeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启事务
    ret = TestTransStartAsync(conn1, GMC_FORCE_COMMIT_DISABLED);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestTransStartAsync(conn2, GMC_FORCE_COMMIT_MODE_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1操作
    // container
    ret = TestBatchPrepare(conn1, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes = 0;
    ret = GmcAllocStmt(conn1, &stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtRoot, "main_label", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtRoot, &nodeRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;
    // list
    ret = GmcAllocStmt(conn1, &stmtLeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtLeafList, "leaf-list_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmtRoot, stmtLeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtLeafList, &nodeLeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    uint32_t listF0Val1 = 1;
    ret = SetNodeProperty(nodeLeafList, GMC_DATATYPE_UINT32, &listF0Val1, sizeof(uint32_t),
                        "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmtLeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    GmcFreeStmt(stmtRoot);
    GmcFreeStmt(stmtLeafList);
    // 事务2操作
    // container
    ret = TestBatchPrepare(conn2, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes = 0;
    ret = GmcAllocStmt(conn2, &stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtRoot, "main_label", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtRoot, &nodeRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;
    // list
    ret = GmcAllocStmt(conn2, &stmtLeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtLeafList, "leaf-list_label", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmtRoot, stmtLeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtLeafList, &nodeLeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmtLeafList, 1, GMC_DATATYPE_UINT32, &listF0Val2, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmtLeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestTransCommitAsync(conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree查询
    TestSubtreeFilter(g_connAsync, g_stmtAsync, "main_label", "subtreeReplyJson/diff_data_enhance_reply_11.json");

    // 断连
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn2, stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 012.leaf-list同表不同记录被delete、create
TEST_F(diff_data_enhance, Yang_115_diff_data_enhance_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/diff_data_enhance_vertex_10.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/diff_data_enhance_edge_10.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ModelCheckAndDataCheck(g_connAsync, g_stmtAsync);

    AsyncUserDataT data = {0};
    GmcBatchT *batch = NULL;

    GmcConnT *conn1 = NULL;
    GmcConnT *conn2 = NULL;
    GmcStmtT *stmt1 = NULL;
    GmcStmtT *stmt2 = NULL;
    ret = testGmcConnect(&conn1, &stmt1, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = useNameSpaceAsync(stmt1, G_NAMESPACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn2, &stmt2, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = useNameSpaceAsync(stmt2, G_NAMESPACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcStmtT *stmtRoot = NULL;
    GmcNodeT *nodeRoot = NULL;
    GmcStmtT *stmtLeafList = NULL;
    GmcNodeT *nodeLeafList = NULL;

    // 预置数据
    // 开启事务
    ret = TestTransStartAsync(g_connAsync, GMC_FORCE_COMMIT_DISABLED);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // container
    ret = TestBatchPrepare(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int dmlTimes = 0;
    ret = GmcAllocStmt(g_connAsync, &stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtRoot, "main_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtRoot, &nodeRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t f1Val = 1;
    ret = SetNodeProperty(nodeRoot, GMC_DATATYPE_UINT32, &f1Val, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;
    // list
    ret = GmcAllocStmt(g_connAsync, &stmtLeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtLeafList, "leaf-list_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmtRoot, stmtLeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtLeafList, &nodeLeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    uint32_t listF0Val1 = 1;
    ret = SetNodeProperty(nodeLeafList, GMC_DATATYPE_UINT32, &listF0Val1, sizeof(uint32_t),
                        "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtLeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启事务
    ret = TestTransStartAsync(conn1, GMC_FORCE_COMMIT_DISABLED);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestTransStartAsync(conn2, GMC_FORCE_COMMIT_MODE_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1操作
    // container
    ret = TestBatchPrepare(conn1, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes = 0;
    ret = GmcAllocStmt(conn1, &stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtRoot, "main_label", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtRoot, &nodeRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;
    // list
    ret = GmcAllocStmt(conn1, &stmtLeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtLeafList, "leaf-list_label", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmtRoot, stmtLeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtLeafList, &nodeLeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmtLeafList, 1, GMC_DATATYPE_UINT32, &listF0Val1, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmtLeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    GmcFreeStmt(stmtRoot);
    GmcFreeStmt(stmtLeafList);
    // 事务2操作
    // container
    ret = TestBatchPrepare(conn2, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes = 0;
    ret = GmcAllocStmt(conn2, &stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtRoot, "main_label", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtRoot, &nodeRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;
    // list
    ret = GmcAllocStmt(conn2, &stmtLeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtLeafList, "leaf-list_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmtRoot, stmtLeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtLeafList, &nodeLeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    uint32_t listF0Val2 = 2;
    ret = SetNodeProperty(nodeLeafList, GMC_DATATYPE_UINT32, &listF0Val2, sizeof(uint32_t),
                        "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmtLeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestTransCommitAsync(conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree查询
    TestSubtreeFilter(g_connAsync, g_stmtAsync, "main_label", "subtreeReplyJson/diff_data_enhance_reply_12.json");

    // 断连
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn2, stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 013.leaf-list同表不同记录被delete
TEST_F(diff_data_enhance, Yang_115_diff_data_enhance_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/diff_data_enhance_vertex_10.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/diff_data_enhance_edge_10.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ModelCheckAndDataCheck(g_connAsync, g_stmtAsync);

    AsyncUserDataT data = {0};
    GmcBatchT *batch = NULL;

    GmcConnT *conn1 = NULL;
    GmcConnT *conn2 = NULL;
    GmcStmtT *stmt1 = NULL;
    GmcStmtT *stmt2 = NULL;
    ret = testGmcConnect(&conn1, &stmt1, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = useNameSpaceAsync(stmt1, G_NAMESPACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn2, &stmt2, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = useNameSpaceAsync(stmt2, G_NAMESPACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcStmtT *stmtRoot = NULL;
    GmcNodeT *nodeRoot = NULL;
    GmcStmtT *stmtLeafList = NULL;
    GmcNodeT *nodeLeafList = NULL;

    // 预置数据
    // 开启事务
    ret = TestTransStartAsync(g_connAsync, GMC_FORCE_COMMIT_DISABLED);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // container
    ret = TestBatchPrepare(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int dmlTimes = 0;
    ret = GmcAllocStmt(g_connAsync, &stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtRoot, "main_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtRoot, &nodeRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t f1Val = 1;
    ret = SetNodeProperty(nodeRoot, GMC_DATATYPE_UINT32, &f1Val, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;
    // list
    // data1
    ret = GmcAllocStmt(g_connAsync, &stmtLeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtLeafList, "leaf-list_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmtRoot, stmtLeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtLeafList, &nodeLeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    uint32_t listF0Val1 = 1;
    ret = SetNodeProperty(nodeLeafList, GMC_DATATYPE_UINT32, &listF0Val1, sizeof(uint32_t),
                        "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtLeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;

    // data2
    ret = GmcAllocStmt(g_connAsync, &stmtLeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtLeafList, "leaf-list_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmtRoot, stmtLeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtLeafList, &nodeLeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    uint32_t listF0Val2 = 2;
    ret = SetNodeProperty(nodeLeafList, GMC_DATATYPE_UINT32, &listF0Val2, sizeof(uint32_t),
                        "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtLeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启事务
    ret = TestTransStartAsync(conn1, GMC_FORCE_COMMIT_DISABLED);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestTransStartAsync(conn2, GMC_FORCE_COMMIT_MODE_0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务1操作
    // container
    ret = TestBatchPrepare(conn1, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes = 0;
    ret = GmcAllocStmt(conn1, &stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtRoot, "main_label", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtRoot, &nodeRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;
    // list
    ret = GmcAllocStmt(conn1, &stmtLeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtLeafList, "leaf-list_label", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmtRoot, stmtLeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtLeafList, &nodeLeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmtLeafList, 1, GMC_DATATYPE_UINT32, &listF0Val1, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmtLeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    GmcFreeStmt(stmtRoot);
    GmcFreeStmt(stmtLeafList);
    // 事务2操作
    // container
    ret = TestBatchPrepare(conn2, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes = 0;
    ret = GmcAllocStmt(conn2, &stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtRoot, "main_label", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtRoot, &nodeRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;
    // list
    ret = GmcAllocStmt(conn2, &stmtLeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtLeafList, "leaf-list_label", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmtRoot, stmtLeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtLeafList, &nodeLeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmtLeafList, 1, GMC_DATATYPE_UINT32, &listF0Val2, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmtLeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dmlTimes++;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(dmlTimes, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestTransCommitAsync(conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree查询
    TestSubtreeFilter(g_connAsync, g_stmtAsync, "main_label", "subtreeReplyJson/diff_data_enhance_reply_13.json");

    // 断连
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn2, stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
