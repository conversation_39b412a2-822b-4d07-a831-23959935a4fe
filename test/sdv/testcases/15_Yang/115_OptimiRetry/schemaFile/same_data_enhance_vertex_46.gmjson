[{"type": "container", "name": "main_label", "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}], "keys": [{"name": "pk", "fields": ["ID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "list_label", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"type": "container", "name": "container_1", "fields": [{"name": "con_1_F1", "type": "uint32", "nullable": true}, {"name": "con_1_F2", "type": "uint32", "nullable": true}]}], "keys": [{"name": "pk", "fields": ["PID", "F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]