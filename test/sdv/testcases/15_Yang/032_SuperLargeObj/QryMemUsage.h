/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */
#ifndef QUERY_MEMORY_USAGE_TEST_H
#define QUERY_MEMORY_USAGE_TEST_H
#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/prctl.h>
#include <sys/socket.h>
#include <sys/epoll.h>
#include <assert.h>
#include <errno.h>
#include <stdint.h>
#include <typeinfo>
#include <limits.h>
#include "gtest/gtest.h"
#include "jansson.h"
#include "t_datacom_lite.h"


const char *g_msConfigTrans = "{\"max_record_count\" : 1000000, \"isFastReadUncommitted\":0, \"auto_increment\":1,"
    "\"yang_model\":1}";

const char *g_nameConRoot = "Con_root";
const char *g_nameConConChild = "Con_Con_Child";
const char *g_nameConListChild = "Con_List_Child";
const char *g_nameConChoiceChild = "Con_Choice_Child";
const char *g_nameConCaseChild = "Con_Case_Child";

const char *g_containRootName = "T0";
const char *g_containChildName01 = "T1";
const char *g_labelName1 = "S0";

const char *g_keyName = "PK";

GmcTxConfigT g_mSTrxConfig;
GmcTxConfigT trxConfig;

char g_valueSupLargeObj[20481] = {0};

static vector<string> expectDiffCreateBase = {
    "T0:create[(priKey(ID:1)),(NULL)]\n"
    "T0.F0:create(100)\n"
    "T0.F1:create(100)\n"
    "T0.F2:create(string)\n"
    "T0.T1:create[(priKey(PID:1)),(NULL)]\n"
    "T1.ID:create(1)\n"
    "T1.F0:create(100)\n"
    "T1.F1:create(100)\n"
    "T1.F2:create(string)\n"
};

void testDropLabelConConAsync(GmcStmtT *stmt)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    sleep(1);
    // 删除边
    ret = GmcDropEdgeLabelAsync(stmt, "Con_Edge_01", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 删除点
    ret = GmcDropVertexLabelAsync(stmt, g_nameConRoot, drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropVertexLabelAsync(stmt, g_nameConConChild, drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
}

void testDropLabelConListAsync(GmcStmtT *stmt)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    sleep(1);
    // 删除边
    ret = GmcDropEdgeLabelAsync(stmt, "Con_Edge_01", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 删除点
    ret = GmcDropVertexLabelAsync(stmt, g_nameConRoot, drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropVertexLabelAsync(stmt, g_nameConListChild, drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
}

void testCreateLabel(GmcStmtT *stmt)
{
    int ret = 0;
    char *vLabelSchema = NULL;

    readJanssonFile("schema_file/Con_Con_List_Choice-case.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateVertexLabel(stmt, vLabelSchema, g_msConfigTrans);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(vLabelSchema);
    vLabelSchema = NULL;

    readJanssonFile("schema_file/Con_Con_List_Choice-case_Edge.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateEdgeLabel(stmt, vLabelSchema, g_msConfigTrans);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(vLabelSchema);
    vLabelSchema = NULL;
}

void testCreateLabel(GmcStmtT *stmt, string nodeJsonPath, string edgeJsonPath)
{
    int ret = 0;
    char *vLabelSchema = NULL;

    readJanssonFile(nodeJsonPath.c_str(), &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateVertexLabel(stmt, vLabelSchema, g_msConfigTrans);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(vLabelSchema);
    vLabelSchema = NULL;

    readJanssonFile(edgeJsonPath.c_str(), &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateEdgeLabel(stmt, vLabelSchema, g_msConfigTrans);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(vLabelSchema);
    vLabelSchema = NULL;
}

void testCreateLabelAsync(GmcStmtT *stmt, string nodeJsonPath, string edgeJsonPath)
{
    int ret = 0;
    char *vLabelSchema = NULL;
    AsyncUserDataT data = {0};

    readJanssonFile(nodeJsonPath.c_str(), &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateVertexLabelAsync(stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    readJanssonFile(edgeJsonPath.c_str(), &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateEdgeLabelAsync(stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;
}

void CreateYang_container_container(GmcStmtT *stmt, AsyncUserDataT data, string nodeJsonPath)
{
    char *vertexSchema = NULL;
    int ret;

    readJanssonFile(nodeJsonPath.c_str(), &vertexSchema);
    AW_MACRO_ASSERT_NOTNULL(vertexSchema);
    ret = GmcCreateVertexLabelAsync(stmt, vertexSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vertexSchema);
}

int testBatchPrepare(GmcConnT *conn, GmcBatchT **batch, GmcBatchTypeE batchType = GMC_BATCH_YANG)
{
    int ret = 0;
    GmcBatchOptionT batchOption;

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBatchType(&batchOption, batchType);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }

    ret = GmcBatchPrepare(conn, &batchOption, batch);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }

    return ret;
}

int testYangSetField(GmcStmtT *stmt, GmcDataTypeE type, void *value, uint32_t size,
    const char *fieldName, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    int ret1 = 0;

    GmcPropValueT propValue;
    memcpy(propValue.propertyName, fieldName, (strlen(fieldName) + 1));
    propValue.type = type;
    propValue.value = value;
    propValue.size = size;
    ret = GmcYangSetVertexProperty(stmt, &propValue, opType);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_DEBUG, "SetProperty failed fieldName(%s)\n", fieldName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret1 = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);
        return ret;
    }

    return ret;
}

void testYangSetVertexProperty(GmcStmtT *stmt, uint32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;

    uint32_t valueF1 = i;
    ret = testYangSetField(stmt, GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = testYangSetField(stmt, GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void testYangSetNodeProperty(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE optype)
{
    int ret = 0;
    uint32_t value = i;
    uint32_t f1Value = value;
    ret = testYangSetNodePropertyNew(node, GMC_DATATYPE_UINT32, &f1Value, sizeof(uint32_t), "F1", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char f2Value[8] = "string";
    ret = testYangSetNodePropertyNew(node, GMC_DATATYPE_STRING, f2Value, (strlen(f2Value)), "F2", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void testYangSetVertexProperty_F3(GmcStmtT *stmt, uint32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    char value0[11] = "abcdefghij";

    memset(g_valueSupLargeObj, 0, sizeof(g_valueSupLargeObj));
    uint32_t address = 0;
    uint32_t t = 0;
    for (t = 0; t < 2048; ++t) {
        strcpy(&g_valueSupLargeObj[address], value0);
        address += 10;
    }
    ret = testYangSetField(stmt, GMC_DATATYPE_STRING, g_valueSupLargeObj, (strlen(g_valueSupLargeObj)), "F3", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(stmt, GMC_DATATYPE_STRING, g_valueSupLargeObj, (strlen(g_valueSupLargeObj)), "F4", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void testYangSetVertexProperty_PK(GmcStmtT *stmt, uint32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    uint32_t pkValue = i;
    ret = testYangSetField(stmt, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "F0", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void testYangSetVertexPropertyWithoutF0(GmcStmtT *stmt, uint32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    uint32_t value = i;

    uint32_t valueF1 = value;
    ret = testYangSetField(stmt, GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = testYangSetField(stmt, GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void testSetKeyNameAndValue(GmcStmtT *stmt, uint32_t keyvalue, uint32_t numPID = 0, bool isList = false)
{
    int ret;

    // 设置KeyValue，PID为上层节点的ID，自增从1开始，PID=0，代表是根节点
    // 六原语操作时 不 需要设置PID
    if (numPID == 0) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &keyvalue, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        // 只有list的主键允许设置为PID+属性
        if (isList) {
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &keyvalue, sizeof(uint32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }

    ret = GmcSetIndexKeyName(stmt, g_keyName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

int testTransStartAsync(GmcConnT *conn, GmcTxConfigT Config)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = GmcTransStartAsync(conn, &Config, trans_start_callback, &data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = testWaitAsyncRecv(&data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        return ret;
    }
}

int testTransCommitAsync(GmcConnT *conn)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = GmcTransCommitAsync(conn, trans_commit_callback, &data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = testWaitAsyncRecv(&data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (data.status == GMERR_TRANSACTION_ROLLBACK) {
            AW_FUN_Log(LOG_INFO, "data.status = GMERR_TRANSACTION_ROLLBACK, the transaction will rollback.");
            AsyncUserDataT data1 = {0};
            int ret1 = GmcTransRollBackAsync(conn, trans_rollback_callback, &data1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        }
        return ret;
    }
}

int testBatchPrepareAndSetDiff(GmcConnT *conn, GmcBatchT **batch, GmcBatchTypeE batchType = GMC_BATCH_YANG,
    GmcYangDiffTypeE diffType = GMC_YANG_DIFF_DELAY_READ_ON)
{
    int ret = 0;
    GmcBatchOptionT batchOption;

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBatchType(&batchOption, batchType);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcYangBatchOptionSetDiffType(&batchOption, diffType);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchPrepare(conn, &batchOption, batch);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }

    return ret;
}

void testBatchExecuteAndWait(GmcBatchT *batch, AsyncUserDataT data, int totalNum, int succNum)
{
    int ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(totalNum, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(succNum, data.succNum);
}

void FetchAndDeparseDiff_callback_noVerification(
    void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, status);
    if (userData) {
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        userData1->historyRecvNum++;
        if (userData1->lastError != NULL) {
            int ret = strcmp(userData1->lastError, errMsg);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        userData1->recvNum++;
    }
}
void testFetchAndDeparseDiff(GmcStmtT *stmt, GmcBatchT *batch, vector<string> &expectDiff, AsyncUserDataT data)
{
    data.stmt = stmt;
    data.expectDiff = &expectDiff;
    int ret = GmcYangFetchDiffExecuteAsync(stmt, NULL, FetchAndDeparseDiff_callback_noVerification, &data);
    if (ret != GMERR_OK) {
        testGmcGetLastError(NULL);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void DropYang_container_container(GmcStmtT *stmt, AsyncUserDataT data)
{
    int ret;

    ret = GmcDropEdgeLabelAsync(stmt, "T0_to_T1", drop_edge_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropEdgeLabelAsync(stmt, "T0_to_T2", drop_edge_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropEdgeLabelAsync(stmt, "T0_to_T3", drop_edge_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropEdgeLabelAsync(stmt, "T0_to_T4", drop_edge_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropEdgeLabelAsync(stmt, "T0_to_T5", drop_edge_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcDropVertexLabelAsync(stmt, "T0", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropVertexLabelAsync(stmt, "T1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropVertexLabelAsync(stmt, "T2", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropVertexLabelAsync(stmt, "T3", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropVertexLabelAsync(stmt, "T4", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropVertexLabelAsync(stmt, "T5", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
}

void *ScanVertexCallback(void *args)
{
    void *vertexLabel = NULL;
    GmcConnT *conn_t = NULL;
    GmcStmtT *stmt_t = NULL;
    int insertNum = 1000;
    int ret = testGmcConnect(&conn_t, &stmt_t);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt_t, g_labelName1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt_t, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt_t);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish;
    int cnt = 0;
    for (uint32_t num = 0; num < insertNum + 1; num++) {
        ret = GmcFetch(stmt_t, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        char *spget = (char *)malloc(16);
        if (spget == NULL) {
            break;
        }
        ret = GmcGetSuperfieldById(stmt_t, 0, spget, 16);
        EXPECT_EQ(GMERR_OK, ret);
        free(spget);
        cnt++;
    }
    GmcResetStmt(stmt_t);
    EXPECT_EQ(insertNum, cnt);
    GmcFreeStmt(stmt_t);
    ret = testGmcDisconnect(conn_t);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

// userData结构
struct SubtreeFilterCbParam {
    int step;
    int32_t expectStatus;          // 预期的操作状态
    const char *expectReplyJson;  // 预期返回的subtree查询结果, json字符串
};
// userData ：用户数据 replyJson ：服务端返回的子树 json status ：服务器端操作处理结果  errMsg ：错误信息
void AsyncSubtreeFilterCb(void *userData,  GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    SubtreeFilterCbParam *param = (SubtreeFilterCbParam *)(userData);
    param->expectStatus = status;
    if (GMERR_OK != status) {
        AW_FUN_Log(LOG_ERROR, "[err] status is %d  errMsg  is %s   \n ", status, errMsg);
        return;
    }
    bool isEnd = false;
    uint32_t count = 0;
    const char **jsonReply = NULL;
    ASSERT_EQ(GMERR_OK, GmcYangFetchJsonRetDeparse(fetchRet, &isEnd, &jsonReply, &count));
    ASSERT_TRUE(isEnd);
    ASSERT_EQ(1, count);
    ASSERT_TRUE(jsonReply != NULL);
    param->step++;
}

// syncMode:   0|sync  1|async 2|sub
int testGmcConnectBigObj(GmcConnT **connOut, GmcStmtT **stmt = NULL, int syncMode = 0, bool needEpoll = 1,
    EpollRegFunctionT epollReg = g_epoll_reg_info, const char *connName = NULL, const void *chanRingLen = NULL,
    ConnOptionT *connOptions = NULL, const int32_t *packShrinkThresholdSize = NULL, int runMode = -1, int csMode = 0,
    int *epollFd = &g_epollData.userEpollFd)
{
    int ret = testEnvInit(runMode);
    if (ret != GMERR_OK) {
        printf("[testGmcConnect] testEnvInit failed, ret = %d.\n", ret);
        return -1;
    }
    *connOut = NULL;
    GmcConnT *conn;

    GmcConnOptionsT *connOptionsInner;
    ret = GmcConnOptionsCreate(&connOptionsInner);
    if (ret != GMERR_OK) {
        printf("[testGmcConnect] GmcConnOptionsCreate failed, ret = %d.\n", ret);
        return ret;
    }

    ret = GmcConnOptionsSetBigObjThreshold(connOptionsInner, 512);
    if (ret != GMERR_OK) {
        printf("[testGmcConnect] GmcConnOptionsCreate failed, ret = %d.\n", ret);
        return ret;
    }

    const char *p1 = g_connServer;
    if (connOptions && connOptions->serverLocator) {
        p1 = connOptions->serverLocator;
    }
    const char *p3 = g_passwd;
    if (connOptions && connOptions->passwd) {
        p3 = connOptions->passwd;
    }

    ret = GmcConnOptionsSetServerLocator(connOptionsInner, p1);
    if (ret != GMERR_OK) {
        printf("[testGmcConnect] GmcConnOptionsSetServerLocator failed, serverLocator = %s, ret = %d.\n", p1, ret);
        GmcConnOptionsDestroy(connOptionsInner);
        return ret;
    }

    if (connName) {
        ret = GmcConnOptionsSetConnName(connOptionsInner, connName);
        if (ret != GMERR_OK) {
            printf("[testGmcConnect] GmcConnOptionsSetConnName failed, connName = %s, ret = %d.\n", connName, ret);
            GmcConnOptionsDestroy(connOptionsInner);
            return ret;
        }
    }

    if (csMode) {
        ret = GmcConnOptionsSetCSRead(connOptionsInner);
        if (ret != GMERR_OK) {
            printf("[testGmcConnect] GmcConnOptionsSetConnName failed, connName = %s, ret = %d.\n", connName, ret);
            GmcConnOptionsDestroy(connOptionsInner);
            return ret;
        }
    }

    if (needEpoll) {
        ret = GmcConnOptionsSetEpollRegFuncWithUserData(connOptionsInner, epollReg, epollFd);
        if (ret != GMERR_OK) {
            printf("[testGmcConnect] GmcConnOptionsSetEpollRegFuncWithUserData failed, ret = %d.\n", ret);
            return ret;
        }
    }

    if (packShrinkThresholdSize) {
        ret = GmcConnOptionsSetPackShrinkThreshold(connOptionsInner, *packShrinkThresholdSize);
        if (ret != GMERR_OK) {
            printf("[testGmcConnect] GmcConnOptionsSetPackShrinkThreshold failed, packShrinkThresholdSize = %d, ret = "
                   "%d.\n",
                *packShrinkThresholdSize, ret);
            GmcConnOptionsDestroy(connOptionsInner);
            return ret;
        }
    }

    if (connOptions && connOptions->requestTimeout) {
        ret = GmcConnOptionsSetRequestTimeout(connOptionsInner, connOptions->requestTimeout);
        if (ret != GMERR_OK) {
            printf("[testGmcConnect] GmcConnOptionsSetRequestTimeout failed, requestTimeout = %d, ret = %d.\n",
                connOptions->requestTimeout, ret);
            GmcConnOptionsDestroy(connOptionsInner);
            return ret;
        }
    }

    if (connOptions && connOptions->msgReadTimeout) {
        ret = GmcConnOptionsSetMsgReadTimeout(connOptionsInner, connOptions->msgReadTimeout);
        if (ret != GMERR_OK) {
            printf("[testGmcConnect] GmcConnOptionsSetMsgReadTimeout failed, msgReadTimeout = %d, ret = %d.\n",
                connOptions->msgReadTimeout, ret);
            GmcConnOptionsDestroy(connOptionsInner);
            return ret;
        }
    }

    if (connOptions && connOptions->msgWriteTimeout) {
        ret = GmcConnOptionsSetMsgWriteTimeout(connOptionsInner, connOptions->msgWriteTimeout);
        if (ret != GMERR_OK) {
            printf("[testGmcConnect] GmcConnOptionsSetMsgWriteTimeout failed, msgWriteTimeout = %d, ret = %d.\n",
                connOptions->msgWriteTimeout, ret);
            GmcConnOptionsDestroy(connOptionsInner);
            return ret;
        }
    }

    if (connOptions && connOptions->subFailedCb) {
        ret =
            GmcConnOptionsSetSubFailedCallback(connOptionsInner, connOptions->subFailedCb, connOptions->subFailedData);
        if (ret != GMERR_OK) {
            printf("[testGmcConnect] GmcConnOptionsSetSubFailedCallback failed, ret = %d.\n", ret);
            GmcConnOptionsDestroy(connOptionsInner);
            return ret;
        }
    }

    ret = GmcConnOptionsSetFlowCtrlCallback(connOptionsInner, ConnCtionFlowCtrlNotice, NULL);
    if (ret != GMERR_OK) {
        printf("[testGmcConnect] GmcConnOptionsSetFlowCtrlCallback failed, ret = %d.\n", ret);
        GmcConnOptionsDestroy(connOptionsInner);
        return ret;
    }

    ret = testSetAsyncQueueSize(connOptionsInner, (uint32_t *)chanRingLen);
    if (ret != GMERR_OK) {
        printf("[testGmcConnect] testSetAsyncQueueSize failed, ret = %d.\n", ret);
        GmcConnOptionsDestroy(connOptionsInner);
        return ret;
    }

    if (connOptions && connOptions->useReservedConn) {
        ret = GmcConnOptionsSetReservedFlag(connOptionsInner, connOptions->useReservedConn);
        if (ret != GMERR_OK) {
            printf("[testGmcConnect] GmcConnOptionsSetReservedFlag failed, ret = %d.\n", ret);
            GmcConnOptionsDestroy(connOptionsInner);
            return ret;
        }
    }
    if (connOptions && connOptions->requestConnWeight) {
        ret = GmcConnOptionsSetRequestWeight(connOptionsInner, connOptions->requestConnWeight);
        if (ret != GMERR_OK) {
            printf("[testGmcConnect] GmcConnOptionsSetRequestWeight failed, ret = %d.\n", ret);
            GmcConnOptionsDestroy(connOptionsInner);
            return ret;
        }
    }

    int n = 500;
    if (g_envType == 0) {
        pthread_mutex_lock(&g_connConcurrent);
        ++g_connRequest;
        while (g_connRequest > g_connOnline && g_connRequest - g_connOnline > 128 && n > 0) {
            usleep(10000);
            --n;
        }
        pthread_mutex_unlock(&g_connConcurrent);
    }

    GmcStmtT *st;
    do {
        ret = GmcConnect((GmcConnTypeE)syncMode, connOptionsInner, &conn);
        if (ret != GMERR_OK) {
            printf("[testGmcConnect] GmcConnect failed, serverLocator = %s, passwd = %s, ret = %d.\n", p1, p3, ret);
            break;
        }
        *connOut = conn;
        if (stmt == NULL) {
            break;
        }
        ret = GmcAllocStmt(conn, &st);
        if (ret != GMERR_OK) {
            printf("[testGmcConnect] GmcAllocStmt failed, ret = %d.\n", ret);
            break;
        }
        *stmt = st;
    } while (0);
    GmcConnOptionsDestroy(connOptionsInner);
    if (ret == 0) {
        pthread_mutex_lock(&g_connLock);
        ++g_connOnline;
        pthread_mutex_unlock(&g_connLock);
        ret = testSetNameSpace(conn, syncMode);
    } else {
        pthread_mutex_lock(&g_connConcurrent);
        --g_connRequest;
        pthread_mutex_unlock(&g_connConcurrent);
    }
    return ret;
}

#endif /* QUERY_MEMORY_USAGE_TEST_H */
