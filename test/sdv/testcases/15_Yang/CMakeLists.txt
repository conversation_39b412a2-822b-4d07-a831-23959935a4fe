set(compile_list "")

if(FEATURE_YANG)
list(APPEND compile_list 001_SupportYang)
list(APPEND compile_list 002_YangDMLOperation)
list(APPEND compile_list 003_YangSupportListDmlByOrder)
list(APPEND compile_list 004_Support_Value_Verification)
list(APPEND compile_list 005_YangSupportDiff)
list(APPEND compile_list 006_ListUniqueVerification)
list(APPEND compile_list 007_subtree)
list(APPEND compile_list 008_ListLocation)
list(APPEND compile_list 009_is_config_filter)
list(APPEND compile_list 011_SnapshotRead)
list(APPEND compile_list 012_OptimiTrans)
list(APPEND compile_list 013_Subtree_Filter_Default)
if(FEATURE_YANG_VALIDATION)
    list(APPEND compile_list 014_ErrorPath)
    list(APPEND compile_list 015_SupportMandatoryCheck)
endif()
list(APPEND compile_list 016_SupportAsync)
list(APPEND compile_list 017_YangTree)
list(APPEND compile_list 018_YangTreeDMLOperation)
list(APPEND compile_list 019_TreeSupportValueVerification)
if(FEATURE_YANG_VALIDATION)
    list(APPEND compile_list 020_YangTreeSupportMandatoryCheck)
    list(APPEND compile_list 021_TreeErrorPath)
endif()
list(APPEND compile_list 023_YangDqlLog)
list(APPEND compile_list 024_SuperLongTransaction)
list(APPEND compile_list 025_Tree_SupportYang)
list(APPEND compile_list 026_TreeSubTree)
list(APPEND compile_list 027_YangTree_SubTreeMaxDepth)
list(APPEND compile_list 028_tree_model_is_config_filter)
list(APPEND compile_list 029_SubtreeDefaultTree)
list(APPEND compile_list 030_TransSavepoint)
list(APPEND compile_list 031_Keepheartbeat)
list(APPEND compile_list 032_SuperLargeObj)
list(APPEND compile_list 033_LargeObjTime)
list(APPEND compile_list 034_Yang_Gmsysview)
list(APPEND compile_list 035_HPESupportBigPacket)
list(APPEND compile_list 036_tree_model_support_big_object)
list(APPEND compile_list 022_TreeSupportDiff)
list(APPEND compile_list 037_subtreebach)
list(APPEND compile_list 038_namespaceFullQuery)
list(APPEND compile_list 039_TreeListUnique)
list(APPEND compile_list 040_LeafList)
list(APPEND compile_list 041_DiffSupportDefaultVal)
list(APPEND compile_list 043_OptimiTransEnhance)
list(APPEND compile_list 045_subtreeEnhance)
list(APPEND compile_list 046_SupportNamespaceFullDataDeletion)
list(APPEND compile_list 049_LeafListEnhance)
list(APPEND compile_list 050_freenode)
list(APPEND compile_list 051_NameSpaceOptimized)
list(APPEND compile_list 054_YangDFXEhance)
list(APPEND compile_list RCA)
list(APPEND compile_list V3_Trans)
list(APPEND compile_list 056_GetChoiceCasePath)
if(FEATURE_YANG_VALIDATION)
    list(APPEND compile_list 057_YangSemanticValid)
    list(APPEND compile_list 058_XpathValidityCheck)
    list(APPEND compile_list 059_Tree_DDL_Xpath_Analyse)
    list(APPEND compile_list 063_XpathAnalyzePlus)
endif()
list(APPEND compile_list 060_AsyncCallbackNest)
list(APPEND compile_list 061_OMUFAST)
list(APPEND compile_list 062_YangDFX)
if(FEATURE_YANG_VALIDATION)
    list(APPEND compile_list 064_YangDefind)
    list(APPEND compile_list 065_YangXPath)
    list(APPEND compile_list 066_WhenDataValidityCheck)
    list(APPEND compile_list 067_WhenDataValidityCheckSubtree)
    list(APPEND compile_list 068_Leafref)
    list(APPEND compile_list 069_MustDataValidityCheck)
    list(APPEND compile_list 070_MinMaxElements)
    list(APPEND compile_list 071_WhenValidityWithDiff)
    list(APPEND compile_list 072_Mandatory)
    list(APPEND compile_list 073_XpathAlias)
    list(APPEND compile_list 074_YangSecurity)
    list(APPEND compile_list 075_IncrementalValidate)
    list(APPEND compile_list 076_YangSupportEnum)
    list(APPEND compile_list 077_ConfigDataImportAndExport)
    list(APPEND compile_list 078_YangIdentity)
    list(APPEND compile_list 079_CheckWhenNPA)
    list(APPEND compile_list 087_YangDiffAttach)
endif()
list(APPEND compile_list 080_SubtreeRestructure)
if(FEATURE_YANG_VALIDATION)
    list(APPEND compile_list 081_ImExportPlan)
    list(APPEND compile_list 082_XpathVariable)
    list(APPEND compile_list 083_YangCheckDFX)
endif()
list(APPEND compile_list 084_YangSubtreePerf)
if(FEATURE_PERSISTENCE)
    list(APPEND compile_list 085_YangVertexScan)
    list(APPEND compile_list 091_MinMaxWhenPst)
    list(APPEND compile_list 095_YangCompatible)
    list(APPEND compile_list 110_PstSupportEmptyUnion)
    list(APPEND compile_list 116_YangUpdate)
    list(APPEND compile_list 117_AutoIncIDRecycle)
    list(APPEND compile_list 119_YangUpdateEnhance)
endif()

if(PRODUCT_GuangQi)
    list(APPEND compile_list 122_CloneTrans)
endif()

list(APPEND compile_list 086_ValidatePlus)
list(APPEND compile_list 088_YangAddStmt)
list(APPEND compile_list 090_MinMaxWhen)
list(APPEND compile_list 092_ValueVerificationSupportW3C)
list(APPEND compile_list 093_YangPerfOptimize)
list(APPEND compile_list 094_SubtreeContainmentNode)
list(APPEND compile_list 096_GetLeafrefPath)
list(APPEND compile_list 097_YangAcrossTreeValidate)
list(APPEND compile_list 098_XpathFuncExtension)
list(APPEND compile_list 099_YangSub)
list(APPEND compile_list 100_RemoveNPADepend)
list(APPEND compile_list 101_YangDFX)
list(APPEND compile_list 102_ExplicitDiff)
list(APPEND compile_list 103_predicateNest)
list(APPEND compile_list 104_SubtreeEmptyAndUnion)
list(APPEND compile_list 105_DiffEmptyAndUnion)
list(APPEND compile_list 106_DDLSupportEmptyUnion)
list(APPEND compile_list 107_ValidateSupportEmptyUnion)
list(APPEND compile_list 108_PrimitiveEmptyAndUnion)
list(APPEND compile_list 109_SubtreeEncode)
list(APPEND compile_list 111_YangSubSuportEmptyUnion)
list(APPEND compile_list 112_YangSubExplicit)
list(APPEND compile_list 113_ExAndImportSupportEmptyUnion)
list(APPEND compile_list 114_SubtreeANDCondition)
list(APPEND compile_list 115_OptimiRetry)
list(APPEND compile_list 120_UnionDefault)
list(APPEND compile_list 121_YangOperService)
endif()

verify_and_add_directory(${compile_list})
