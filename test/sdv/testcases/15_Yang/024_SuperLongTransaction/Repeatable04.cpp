/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2022. All rights reserved.
 Description  : 超长事务处理
 Notes        :乐观可重复读

 History      :
 Author       : youwanyong ywx1157510
 Modification : 2022/9/13
**************************************************************************** */
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <atomic>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "SuperLongTransaction.h"
#include "PessimisticSerializable.h"

using namespace std;
GmcConnT *g_conn = NULL, *g_conn_async1 = NULL, *g_conn_async2 = NULL;
GmcStmtT *g_stmt = NULL, *g_stmt_async1 = NULL, *g_stmt_async2 = NULL, *g_stmt_root = NULL, *g_stmt_con = NULL;
char *g_schema = NULL, *g_edge_schema = NULL;
const char *g_ConConRootName = "Con_Con_root", *g_ConConChildName = "Con_Con_Child";
const char *g_config = "{\"max_record_count\" : 100000, \"isFastReadUncommitted\":0, \"auto_increment\":1,"
   "\"yang_model\":1}";

class SuperLongTransaction : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void SuperLongTransaction::SetUpTestCase()
{}

void SuperLongTransaction::TearDownTestCase()
{}

void SuperLongTransaction::SetUp()
{
    g_stmt = NULL;
    g_stmt_async = NULL;
    system("rm -rf \"../../../log/run/rgmserver/rgmserver.log\"");
    AW_CHECK_LOG_BEGIN(0);
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_ACTIVE_TRANSACTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}

void SuperLongTransaction::TearDown()
{
    AW_CHECK_LOG_END();
    system("sh $TEST_HOME/tools/stop.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}
/* ****************************************************************************
 Description  :  015.(yang）乐观可重复读，开启长事务管理，进行批操作，事务超时超过日志记录阈值，查询视图，
 Author       : youwanyong
**************************************************************************** */
TEST_F(SuperLongTransaction, Yang_024_015)
{
    AW_FUN_Log(LOG_STEP, "START");

    AsyncUserDataT userData = {0};
    GmcBatchT *batch = NULL;
    uint32_t fieldValue = 0, PID = 0, newvalue = 0;
    uint32_t ret;

    // 开启长事务管理功能,同时将日志阈值及回滚阈值降为（10.15）
    system("sh $TEST_HOME/tools/modifyCfg.sh \"trxMonitorThreshold=10,15\"");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh");

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建连，同时创建新的namespace
    AW_FUN_Log(LOG_STEP, "START");
    ExpextViewResultNormal();
    ret = system("cat ../../../log/run/rgmserver/rgmserver.log | grep -o \"Long Transaction\"");
    AW_MACRO_EXPECT_EQ_INT(256, ret);

    // 创建同步连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建3个异步连接
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async1, &g_stmt_async1, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async2, &g_stmt_async2, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcAllocStmt(g_conn_async, &g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_con);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = g_namespace;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};

    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcUseNamespaceAsync(g_stmt_async, g_namespace, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 创建yang表
    readJanssonFile("schemafile/Yang/Con_Con.gmjson", &g_schema);
    AW_MACRO_ASSERT_NOTNULL(g_schema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_schema, g_config, create_vertex_label_callback, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_schema);

    // 开启乐观可重复读事务
    // 事务结构体
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_REPEATABLE;  // 可重复读
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX;  // 乐观事务

    /* ************** 事务一对container、child container执行create   操作  ****************** */
    // 开启乐观事务一
    ret = GmcTransStartAsync(g_conn_async, &config, trans_start_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_ConConRootName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *rootNode = NULL, *childNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &rootNode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcYangEditChildNode(rootNode, g_ConConChildName, GMC_OPERATION_INSERT, &childNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child的属性值
    fieldValue = 100;
    testYangSetNodeProperty(childNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    AW_MACRO_EXPECT_EQ_INT(1, userData.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, userData.succNum);
    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // sleep超过日志记录阈值
    sleep(10);
    ExpextViewResultLog();
    ret = system("cat ../../../log/run/rgmserver/rgmserver.log | grep -o \"Long Transaction\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交事务一
    ret = GmcTransCommitAsync(g_conn_async, trans_commit_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ExpextViewResultLog();
    ret = system("cat ../../../log/run/rgmserver/rgmserver.log | grep -o \"Long Transaction\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /* ************** 事务二对child container执行merge操作  ****************** */
    // 开启乐观事务二
    ret = GmcUseNamespaceAsync(g_stmt_async1, g_namespace, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcTransStartAsync(g_conn_async1, &config, trans_start_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 设置批处理
    ret = testBatchPrepare(g_conn_async1, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    rootNode = NULL;
    childNode = NULL;
    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_ConConRootName, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &rootNode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(rootNode, g_ConConChildName, GMC_OPERATION_MERGE, &childNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child的属性值
    newvalue = 200;
    testYangSetNodeProperty(childNode, newvalue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    AW_MACRO_EXPECT_EQ_INT(1, userData.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, userData.succNum);
    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    /* ************** 事务三对child container执行replace操作  ****************** */
    // 开启乐观事务三
    ret = GmcUseNamespaceAsync(g_stmt_async2, g_namespace, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcTransStartAsync(g_conn_async2, &config, trans_start_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 设置批处理
    ret = testBatchPrepare(g_conn_async2, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    rootNode = NULL;
    childNode = NULL;
    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_ConConRootName, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &rootNode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(rootNode, g_ConConChildName, GMC_OPERATION_REPLACE_GRAPH, &childNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child的属性值
    newvalue = 200;
    testYangSetNodeProperty(childNode, newvalue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    AW_MACRO_EXPECT_EQ_INT(1, userData.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, userData.succNum);
    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 提交事务二
    ret = GmcTransCommitAsync(g_conn_async1, trans_commit_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_RESTRICT_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    // 提交事务三
    ret = GmcTransCommitAsync(g_conn_async2, trans_commit_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcTransRollBackAsync(g_conn_async2, trans_rollback_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    ret = testGmcGetLastError();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ExpextViewResultLog();
    ret = system("cat ../../../log/run/rgmserver/rgmserver.log | grep -o \"Long Transaction\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 断连
    // drop edge表
    ret = GmcClearNamespaceAsync(g_stmt_async, g_namespace, ClearNSCallbak, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_async, g_namespace, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
     ret = testGmcGetLastError();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    GmcFreeStmt(g_stmt_root);
    GmcFreeStmt(g_stmt_con);

    // 释放同步连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放3个异步连接
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async1, g_stmt_async1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async2, g_stmt_async2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "end");
}

/* ****************************************************************************
 Description  :  016.(yang）乐观可重复读，开启长事务管理，进行批操作，事务超时超过回滚阈值，查询视图，
 Author       : youwanyong
**************************************************************************** */
TEST_F(SuperLongTransaction, Yang_024_016)
{
    AW_FUN_Log(LOG_STEP, "START");

    AsyncUserDataT userData = {0};
    GmcBatchT *batch = NULL;
    uint32_t fieldValue = 0, PID = 0, newvalue = 0;
    uint32_t ret;

    // 开启长事务管理功能,同时将日志阈值及回滚阈值降为（10.15）
    system("sh $TEST_HOME/tools/modifyCfg.sh \"trxMonitorThreshold=10,15\"");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = createEpollOneThread();
    EXPECT_EQ(GMERR_OK, ret);
    system("gmadmin -cfgName enableLogFold -cfgVal 0");

    // 建连，同时创建新的namespace
    AW_FUN_Log(LOG_STEP, "START");
    ExpextViewResultNormal();
    ret = system("cat ../../../log/run/rgmserver/rgmserver.log | grep -o \"Long Transaction\"");
    AW_MACRO_EXPECT_EQ_INT(256, ret);

    // 创建3个异步连接
    YangConnOptionT connOptions = {0};
    connOptions.isOneThreadEpoll = true;
    connOptions.epollRegType = TEST_EPOLLREG_WITH_USD;
    connOptions.epollRegWithUsDFunc = g_epollRegInfoOneThread;
    connOptions.epollFd = &g_epollDataOneThread.userEpollFd;
    ret = TestYangGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangGmcConnect(&g_conn_async1, &g_stmt_async1, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangGmcConnect(&g_conn_async2, &g_stmt_async2, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcAllocStmt(g_conn_async, &g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_con);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = g_namespace;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};

    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcUseNamespaceAsync(g_stmt_async, g_namespace, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testWaitAsyncRecvOneThread(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 创建yang表
    readJanssonFile("schemafile/Yang/Con_Con.gmjson", &g_schema);
    AW_MACRO_ASSERT_NOTNULL(g_schema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_schema, g_config, create_vertex_label_callback, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
   ret = testWaitAsyncRecvOneThread(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    free(g_schema);

    // 开启乐观可重复读事务
    // 事务结构体
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_REPEATABLE;  // 可重复读
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX;  // 乐观事务

    /* ************** 事务一对container、child container执行create   操作  ****************** */
    // 开启乐观事务一
    ret = GmcTransStartAsync(g_conn_async, &config, trans_start_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_ConConRootName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *rootNode = NULL, *childNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &rootNode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcYangEditChildNode(rootNode, g_ConConChildName, GMC_OPERATION_INSERT, &childNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child的属性值
    fieldValue = 100;
    testYangSetNodeProperty(childNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    AW_MACRO_EXPECT_EQ_INT(1, userData.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, userData.succNum);
    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // sleep超过日志记录阈值
    sleep(10);
    ExpextViewResultLog();
    ret = system("cat ../../../log/run/rgmserver/rgmserver.log | grep -o \"Long Transaction\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // sleep超过回滚阈值
    sleep(5);
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_TRANSACTION_ROLLBACK);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    // 提交事务一
    ret = GmcTransCommitAsync(g_conn_async, trans_commit_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, userData.status);

    // 事务一回滚
    ret = GmcTransRollBackAsync(g_conn_async, trans_rollback_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);

    ExpextViewResultBack();
    ret = system("cat ../../../log/run/rgmserver/rgmserver.log | grep -o \"Long Transaction\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "事务二merge操作");
    /* ************** 事务二对child container执行merge操作  ****************** */
    // 开启乐观事务二
    ret = GmcUseNamespaceAsync(g_stmt_async1, g_namespace, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);

    ret = GmcTransStartAsync(g_conn_async1, &config, trans_start_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async1, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    rootNode = NULL;
    childNode = NULL;
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_ConConRootName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &rootNode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcYangEditChildNode(rootNode, g_ConConChildName, GMC_OPERATION_MERGE, &childNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child的属性值
    newvalue = 200;
    testYangSetNodeProperty(childNode, newvalue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    AW_MACRO_EXPECT_EQ_INT(1, userData.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, userData.succNum);
    GmcBatchDestroy(batch);

     AW_FUN_Log(LOG_STEP, "事务三repalce操作");
    /* ************** 事务三对child container执行replace操作  ****************** */
    // 开启乐观事务三
    ret = GmcUseNamespaceAsync(g_stmt_async2, g_namespace, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);

    ret = GmcTransStartAsync(g_conn_async2, &config, trans_start_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async2, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    rootNode = NULL;
    childNode = NULL;
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_ConConRootName, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = GmcGetRootNode(g_stmt_root, &rootNode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(rootNode, g_ConConChildName, GMC_OPERATION_REPLACE_GRAPH, &childNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置child的属性值
    newvalue = 200;
    testYangSetNodeProperty(childNode, newvalue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg2);

    // 提交批处理 // none操作无效·
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, userData.status);
    AW_MACRO_EXPECT_EQ_INT(1, userData.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, userData.succNum);
    GmcBatchDestroy(batch);

    // 提交事务二
    ret = GmcTransCommitAsync(g_conn_async1, trans_commit_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);

    // 提交事务三
    ret = GmcTransCommitAsync(g_conn_async2, trans_commit_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = testGmcGetLastError();

    ret = GmcTransRollBackAsync(g_conn_async2, trans_rollback_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);

    ExpextViewResultBack();
    ret = system("cat ../../../log/run/rgmserver/rgmserver.log | grep -o \"Long Transaction\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 断连
    // drop edge表
    ret = GmcClearNamespaceAsync(g_stmt_async, g_namespace, ClearNSCallbak, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_async, g_namespace, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
     ret = testGmcGetLastError();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    GmcFreeStmt(g_stmt_root);
    GmcFreeStmt(g_stmt_con);
    system("gmadmin -cfgName enableLogFold -cfgVal 1");

    // 释放3个异步连接
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async1, g_stmt_async1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async2, g_stmt_async2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    closeEpollOneThread();
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "end");
}

/* ****************************************************************************
 Description  :  017.(yang）乐观可重复读，开启长事务管理，进行批操作，事务未超时，查询视图
 Author       : youwanyong
**************************************************************************** */
TEST_F(SuperLongTransaction, Yang_024_017)
{
    AW_FUN_Log(LOG_STEP, "START");
    uint32_t ret;
    AsyncUserDataT userData = {0};
    GmcBatchT *batch = NULL;
    uint32_t fieldValue = 0, PID = 0, newvalue = 0;

    // 开启长事务管理功能,同时将日志阈值及回滚阈值降为（10.15）
    system("sh $TEST_HOME/tools/modifyCfg.sh \"trxMonitorThreshold=10,15\"");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建连，同时创建新的namespace
    AW_FUN_Log(LOG_STEP, "START");
    ExpextViewResultNormal();
    ret = system("cat ../../../log/run/rgmserver/rgmserver.log | grep -o \"Long Transaction\"");
    AW_MACRO_EXPECT_EQ_INT(256, ret);

    // 创建同步连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建3个异步连接
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async1, &g_stmt_async1, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async2, &g_stmt_async2, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcAllocStmt(g_conn_async, &g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_con);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = g_namespace;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};

    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcUseNamespaceAsync(g_stmt_async, g_namespace, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 创建yang表
    readJanssonFile("schemafile/Yang/Con_Con.gmjson", &g_schema);
    AW_MACRO_ASSERT_NOTNULL(g_schema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_schema, g_config, create_vertex_label_callback, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_schema);

    // 开启乐观可重复读事务
    // 事务结构体
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_REPEATABLE;  // 可重复读
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX;  // 乐观事务

    /* ************** 事务一对container、child container执行create   操作  ****************** */
    // 开启乐观事务一
    ret = GmcTransStartAsync(g_conn_async, &config, trans_start_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_ConConRootName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *rootNode = NULL, *childNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &rootNode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcYangEditChildNode(rootNode, g_ConConChildName, GMC_OPERATION_INSERT, &childNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child的属性值
    fieldValue = 100;
    testYangSetNodeProperty(childNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    AW_MACRO_EXPECT_EQ_INT(1, userData.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, userData.succNum);
    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 提交事务一
    ret = GmcTransCommitAsync(g_conn_async, trans_commit_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    /* ************** 事务二对child container执行merge操作  ****************** */
    // 开启乐观事务二
    ret = GmcUseNamespaceAsync(g_stmt_async1, g_namespace, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcTransStartAsync(g_conn_async1, &config, trans_start_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 设置批处理
    ret = testBatchPrepare(g_conn_async1, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_ConConRootName, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    rootNode = NULL;
    childNode = NULL;
    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_ConConRootName, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &rootNode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(rootNode, g_ConConChildName, GMC_OPERATION_MERGE, &childNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child的属性值
    newvalue = 200;
    testYangSetNodeProperty(childNode, newvalue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    AW_MACRO_EXPECT_EQ_INT(2, userData.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, userData.succNum);
    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    /* ************** 事务三对child container执行replace操作  ****************** */
    // 开启乐观事务三
    ret = GmcUseNamespaceAsync(g_stmt_async2, g_namespace, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcTransStartAsync(g_conn_async2, &config, trans_start_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 设置批处理
    ret = testBatchPrepare(g_conn_async2, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    rootNode = NULL;
    childNode = NULL;
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_ConConRootName, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_ConConRootName, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &rootNode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(rootNode, g_ConConChildName, GMC_OPERATION_REPLACE_GRAPH, &childNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child的属性值
    newvalue = 200;
    testYangSetNodeProperty(childNode, newvalue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    AW_MACRO_EXPECT_EQ_INT(2, userData.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, userData.succNum);
    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 提交事务二
    ret = GmcTransCommitAsync(g_conn_async1, trans_commit_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_RESTRICT_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    // 提交事务三
    ret = GmcTransCommitAsync(g_conn_async2, trans_commit_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcTransRollBackAsync(g_conn_async2, trans_rollback_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    ret = testGmcGetLastError();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ExpextViewResultNormal();
    ret = system("cat ../../../log/run/rgmserver/rgmserver.log | grep -o \"Long Transaction\"");
    AW_MACRO_EXPECT_EQ_INT(256, ret);

    // 断连
    // drop edge表
    ret = GmcClearNamespaceAsync(g_stmt_async, g_namespace, ClearNSCallbak, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_async, g_namespace, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    GmcFreeStmt(g_stmt_root);
    GmcFreeStmt(g_stmt_con);

    // 释放同步连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放3个异步连接
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async1, g_stmt_async1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async2, g_stmt_async2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "end");
}

/* ****************************************************************************
 Description  :  024.(yang）乐观可重复读，关闭长事务管理，进行批操作，事务超时超过日志记录阈值，查询视图，
 Author       : youwanyong
**************************************************************************** */
TEST_F(SuperLongTransaction, Yang_024_024)
{
    AW_FUN_Log(LOG_STEP, "START");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_RESTRICT_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    AsyncUserDataT userData = {0};
    GmcBatchT *batch = NULL;
    uint32_t fieldValue = 0, PID = 0, newvalue = 0;
    uint32_t ret;

    // 开启长事务管理功能,同时将日志阈值及回滚阈值降为（10.15）
    system("sh $TEST_HOME/tools/modifyCfg.sh \"trxMonitorThreshold=10,15\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"trxMonitorEnable=0\"");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建连，同时创建新的namespace
    AW_FUN_Log(LOG_STEP, "START");
    ExpextViewResultNormal();
    ret = system("cat ../../../log/run/rgmserver/rgmserver.log | grep -o \"Long Transaction\"");
    AW_MACRO_EXPECT_EQ_INT(256, ret);

    // 创建同步连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建3个异步连接
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async1, &g_stmt_async1, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async2, &g_stmt_async2, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcAllocStmt(g_conn_async, &g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_con);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = g_namespace;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};

    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcUseNamespaceAsync(g_stmt_async, g_namespace, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 创建yang表
    readJanssonFile("schemafile/Yang/Con_Con.gmjson", &g_schema);
    AW_MACRO_ASSERT_NOTNULL(g_schema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_schema, g_config, create_vertex_label_callback, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_schema);

    // 开启乐观可重复读事务
    // 事务结构体
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_REPEATABLE;  // 可重复读
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX;  // 乐观事务

    /* ************** 事务一对container、child container执行create   操作  ****************** */
    // 开启乐观事务一
    ret = GmcTransStartAsync(g_conn_async, &config, trans_start_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_ConConRootName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *rootNode = NULL, *childNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &rootNode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcYangEditChildNode(rootNode, g_ConConChildName, GMC_OPERATION_INSERT, &childNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child的属性值
    fieldValue = 100;
    testYangSetNodeProperty(childNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    AW_MACRO_EXPECT_EQ_INT(1, userData.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, userData.succNum);
    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // sleep超时预期无日志打印
    sleep(10);
    ExpextViewResultNormal();
    ret = system("cat ../../../log/run/rgmserver/rgmserver.log | grep -o \"Long Transaction\"");
    AW_MACRO_EXPECT_EQ_INT(256, ret);

    // 提交事务一
    ret = GmcTransCommitAsync(g_conn_async, trans_commit_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    /* ************** 事务二对child container执行merge操作  ****************** */
    // 开启乐观事务二
    ret = GmcUseNamespaceAsync(g_stmt_async1, g_namespace, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcTransStartAsync(g_conn_async1, &config, trans_start_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 设置批处理
    ret = testBatchPrepare(g_conn_async1, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    rootNode = NULL;
    childNode = NULL;
    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_ConConRootName, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &rootNode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(rootNode, g_ConConChildName, GMC_OPERATION_MERGE, &childNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child的属性值
    newvalue = 200;
    testYangSetNodeProperty(childNode, newvalue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    AW_MACRO_EXPECT_EQ_INT(1, userData.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, userData.succNum);
    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    /* ************** 事务三对child container执行replace操作  ****************** */
    // 开启乐观事务三
    ret = GmcUseNamespaceAsync(g_stmt_async2, g_namespace, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcTransStartAsync(g_conn_async2, &config, trans_start_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 设置批处理
    ret = testBatchPrepare(g_conn_async2, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    rootNode = NULL;
    childNode = NULL;
    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_ConConRootName, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &rootNode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(rootNode, g_ConConChildName, GMC_OPERATION_REPLACE_GRAPH, &childNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child的属性值
    newvalue = 200;
    testYangSetNodeProperty(childNode, newvalue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    AW_MACRO_EXPECT_EQ_INT(1, userData.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, userData.succNum);
    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 提交事务二
    ret = GmcTransCommitAsync(g_conn_async1, trans_commit_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 提交事务三
    ret = GmcTransCommitAsync(g_conn_async2, trans_commit_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcTransRollBackAsync(g_conn_async2, trans_rollback_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    ret = testGmcGetLastError();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ExpextViewResultNormal();
    ret = system("cat ../../../log/run/rgmserver/rgmserver.log | grep -o \"Long Transaction\"");
    AW_MACRO_EXPECT_EQ_INT(256, ret);

    // 删表
    // drop edge表
    ret = GmcClearNamespaceAsync(g_stmt_async, g_namespace, ClearNSCallbak, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_async, g_namespace, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    GmcFreeStmt(g_stmt_root);
    GmcFreeStmt(g_stmt_con);

    // 释放同步连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放3个异步连接
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async1, g_stmt_async1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async2, g_stmt_async2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "end");
}

/* ****************************************************************************
 Description  :  025.(yang）乐观可重复读，关闭长事务管理，进行批操作，事务超时超过回滚阈值，查询视图，
 Author       : youwanyong
**************************************************************************** */
TEST_F(SuperLongTransaction, Yang_024_025)
{
    AW_FUN_Log(LOG_STEP, "START");

    AsyncUserDataT userData = {0};
    GmcBatchT *batch = NULL;
    uint32_t fieldValue = 0, PID = 0, newvalue = 0;
    uint32_t ret;
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_RESTRICT_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    // 关闭长事务管理功能,同时将日志阈值及回滚阈值降为（10.15）
    system("sh $TEST_HOME/tools/modifyCfg.sh \"trxMonitorThreshold=10,15\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"trxMonitorEnable=0\"");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建连，同时创建新的namespace
    AW_FUN_Log(LOG_STEP, "START");
    ExpextViewResultNormal();
    ret = system("cat ../../../log/run/rgmserver/rgmserver.log | grep -o \"Long Transaction\"");
    AW_MACRO_EXPECT_EQ_INT(256, ret);

    // 创建同步连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建3个异步连接
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async1, &g_stmt_async1, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async2, &g_stmt_async2, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcAllocStmt(g_conn_async, &g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_con);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = g_namespace;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};

    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcUseNamespaceAsync(g_stmt_async, g_namespace, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 创建yang表
    readJanssonFile("schemafile/Yang/Con_Con.gmjson", &g_schema);
    AW_MACRO_ASSERT_NOTNULL(g_schema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_schema, g_config, create_vertex_label_callback, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_schema);

    // 开启乐观可重复读事务
    // 事务结构体
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_REPEATABLE;  // 可重复读
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX;  // 乐观事务

    /* ************** 事务一对container、child container执行create   操作  ****************** */
    // 开启乐观事务一
    ret = GmcTransStartAsync(g_conn_async, &config, trans_start_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_ConConRootName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *rootNode = NULL, *childNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &rootNode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcYangEditChildNode(rootNode, g_ConConChildName, GMC_OPERATION_INSERT, &childNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child的属性值
    fieldValue = 100;
    testYangSetNodeProperty(childNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    AW_MACRO_EXPECT_EQ_INT(1, userData.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, userData.succNum);
    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    // 超过事务回滚阈值，预期事务不回滚
    ExpextViewResultNormal();
    ret = system("cat ../../../log/run/rgmserver/rgmserver.log | grep -o \"Long Transaction\"");
    AW_MACRO_EXPECT_EQ_INT(256, ret);

    // 提交事务一
    ret = GmcTransCommitAsync(g_conn_async, trans_commit_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    /* ************** 事务二对child container执行merge操作  ****************** */
    // 开启乐观事务二
    ret = GmcUseNamespaceAsync(g_stmt_async1, g_namespace, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcTransStartAsync(g_conn_async1, &config, trans_start_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 设置批处理
    ret = testBatchPrepare(g_conn_async1, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    rootNode = NULL;
    childNode = NULL;
    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_ConConRootName, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &rootNode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(rootNode, g_ConConChildName, GMC_OPERATION_MERGE, &childNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child的属性值
    newvalue = 200;
    testYangSetNodeProperty(childNode, newvalue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    AW_MACRO_EXPECT_EQ_INT(1, userData.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, userData.succNum);
    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    /* ************** 事务三对child container执行replace操作  ****************** */
    // 开启乐观事务三
    ret = GmcUseNamespaceAsync(g_stmt_async2, g_namespace, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcTransStartAsync(g_conn_async2, &config, trans_start_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 设置批处理
    ret = testBatchPrepare(g_conn_async2, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    rootNode = NULL;
    childNode = NULL;
    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_ConConRootName, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &rootNode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(rootNode, g_ConConChildName, GMC_OPERATION_REPLACE_GRAPH, &childNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child的属性值
    newvalue = 200;
    testYangSetNodeProperty(childNode, newvalue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    AW_MACRO_EXPECT_EQ_INT(1, userData.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, userData.succNum);
    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 提交事务二
    ret = GmcTransCommitAsync(g_conn_async1, trans_commit_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 提交事务三
    ret = GmcTransCommitAsync(g_conn_async2, trans_commit_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcTransRollBackAsync(g_conn_async2, trans_rollback_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    ret = testGmcGetLastError();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ExpextViewResultNormal();
    ret = system("cat ../../../log/run/rgmserver/rgmserver.log | grep -o \"Long Transaction\"");
    AW_MACRO_EXPECT_EQ_INT(256, ret);

    // 断连
    // drop edge表
    ret = GmcClearNamespaceAsync(g_stmt_async, g_namespace, ClearNSCallbak, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_async, g_namespace, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    GmcFreeStmt(g_stmt_root);
    GmcFreeStmt(g_stmt_con);

    // 释放同步连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放3个异步连接
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async1, g_stmt_async1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async2, g_stmt_async2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "end");
}

/* ****************************************************************************
 Description  :  026.(yang）乐观可重复读，关闭长事务管理，进行批操作，事务未超时，查询视图
 Author       : youwanyong
**************************************************************************** */
TEST_F(SuperLongTransaction, Yang_024_026)
{
    AW_FUN_Log(LOG_STEP, "START");
    uint32_t ret;
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_RESTRICT_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    AsyncUserDataT userData = {0};
    GmcBatchT *batch = NULL;
    uint32_t fieldValue = 0, PID = 0, newvalue = 0;

    // 关闭长事务管理功能,同时将日志阈值及回滚阈值降为（10.15）
    system("sh $TEST_HOME/tools/modifyCfg.sh \"trxMonitorEnable=0\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"trxMonitorThreshold=10,15\"");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建连，同时创建新的namespace
    AW_FUN_Log(LOG_STEP, "START");
    ExpextViewResultNormal();
    ret = system("cat ../../../log/run/rgmserver/rgmserver.log | grep -o \"Long Transaction\"");
    AW_MACRO_EXPECT_EQ_INT(256, ret);

    // 创建同步连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建3个异步连接
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async1, &g_stmt_async1, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async2, &g_stmt_async2, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcAllocStmt(g_conn_async, &g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_con);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = g_namespace;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};

    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcUseNamespaceAsync(g_stmt_async, g_namespace, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 创建yang表
    readJanssonFile("schemafile/Yang/Con_Con.gmjson", &g_schema);
    AW_MACRO_ASSERT_NOTNULL(g_schema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_schema, g_config, create_vertex_label_callback, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_schema);

    // 开启乐观可重复读事务
    // 事务结构体
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_REPEATABLE;  // 可重复读
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX;  // 乐观事务

    /* ************** 事务一对container、child container执行create   操作  ****************** */
    // 开启乐观事务一
    ret = GmcTransStartAsync(g_conn_async, &config, trans_start_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_ConConRootName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *rootNode = NULL, *childNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &rootNode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcYangEditChildNode(rootNode, g_ConConChildName, GMC_OPERATION_INSERT, &childNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child的属性值
    fieldValue = 100;
    testYangSetNodeProperty(childNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    AW_MACRO_EXPECT_EQ_INT(1, userData.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, userData.succNum);
    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 提交事务一
    ret = GmcTransCommitAsync(g_conn_async, trans_commit_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    /* ************** 事务二对child container执行merge操作  ****************** */
    // 开启乐观事务二
    ret = GmcUseNamespaceAsync(g_stmt_async1, g_namespace, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcTransStartAsync(g_conn_async1, &config, trans_start_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 设置批处理
    ret = testBatchPrepare(g_conn_async1, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    rootNode = NULL;
    childNode = NULL;
    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_ConConRootName, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &rootNode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(rootNode, g_ConConChildName, GMC_OPERATION_MERGE, &childNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child的属性值
    newvalue = 200;
    testYangSetNodeProperty(childNode, newvalue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    AW_MACRO_EXPECT_EQ_INT(1, userData.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, userData.succNum);
    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    /* ************** 事务三对child container执行replace操作  ****************** */
    // 开启乐观事务三
    ret = GmcUseNamespaceAsync(g_stmt_async2, g_namespace, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcTransStartAsync(g_conn_async2, &config, trans_start_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 设置批处理
    ret = testBatchPrepare(g_conn_async2, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    rootNode = NULL;
    childNode = NULL;
    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_ConConRootName, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &rootNode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(rootNode, g_ConConChildName, GMC_OPERATION_REPLACE_GRAPH, &childNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child的属性值
    newvalue = 200;
    testYangSetNodeProperty(childNode, newvalue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    AW_MACRO_EXPECT_EQ_INT(1, userData.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, userData.succNum);
    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 提交事务二
    ret = GmcTransCommitAsync(g_conn_async1, trans_commit_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 提交事务三
    ret = GmcTransCommitAsync(g_conn_async2, trans_commit_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcTransRollBackAsync(g_conn_async2, trans_rollback_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    ret = testGmcGetLastError();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ExpextViewResultNormal();
    ret = system("cat ../../../log/run/rgmserver/rgmserver.log | grep -o \"Long Transaction\"");
    AW_MACRO_EXPECT_EQ_INT(256, ret);

    // 断连
    // drop edge表
    ret = GmcClearNamespaceAsync(g_stmt_async, g_namespace, ClearNSCallbak, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_async, g_namespace, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    GmcFreeStmt(g_stmt_root);
    GmcFreeStmt(g_stmt_con);

    // 释放同步连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放3个异步连接
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async1, g_stmt_async1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async2, g_stmt_async2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "end");
}

/* ****************************************************************************
 Description  : 030.同步场景，乐观事务可重复读，进行长事务操作时，事务，日志，及数据校验
 Author       : youwanyong
**************************************************************************** */
TEST_F(SuperLongTransaction, Yang_024_030)
{
    // 创建namespace、vertex表，插入数据，16个乐观事务，执行merge操作,16个乐观事务，执行replace操作
    AW_FUN_Log(LOG_STEP, "START");
    int start_num = 0, end_num = 100, affectRows = 0;
    const char *labelname = "vertex_00_Memory";
    AsyncUserDataT userData = {0};
    GmcBatchT *batch = NULL;
    uint32_t fieldValue = 0, PID = 0, newvalue = 0;
    char pk_name[] = "pk";
    uint32_t ret;
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_RESTRICT_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    // 将日志阈值及回滚阈值降为（10.30）
    system("sh $TEST_HOME/tools/modifyCfg.sh \"trxMonitorThreshold=10,30\"");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建连，同时创建新的namespace
    AW_FUN_Log(LOG_STEP, "START");
    ExpextViewResultNormal();
    ret = system("cat ../../../log/run/rgmserver/rgmserver.log | grep -o \"Long Transaction\"");
    AW_MACRO_EXPECT_EQ_INT(256, ret);

    system("gmadmin -cfgName enableLogFold -cfgVal 0");

    // 创建同步连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = 0;
    GmcConnT *conn[1024] = {NULL};
    GmcStmtT *stmt[1024] = {NULL};

    // create namespace
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = g_namespace;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观

    GmcDropNamespace(g_stmt, g_namespace);
    char errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg2);
    ret = GmcCreateNamespaceWithCfg(g_stmt, &nspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建vertexlabel
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    readJanssonFile("schemafile/Yang/NormalVertexLabel_001.gmjson", &g_schema);
    AW_MACRO_ASSERT_NE_INT((void *)NULL, g_schema);

    GmcDropVertexLabel(g_stmt, "vertex_00_Memory");
    ret = GmcCreateVertexLabel(g_stmt, g_schema, g_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_schema);

    // insert && query
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, "vertex_00_Memory", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty_PK1(g_stmt, i);
        set_VertexProperty1(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否插入成功
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, affectRows);

        // 读
        QueryVertexProperty(g_stmt, i, "vertex_00_Memory", pk_name);
    }

    // 事务结构体
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_REPEATABLE;  // 可重复读
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX;  // 乐观事务

    // 16个乐观事务对同一批数据执行merge操作
    for (uint32_t i = 0; i < 16; i++) {
        // 建连
        ret = testGmcConnect(&conn[i], &stmt[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcUseNamespace(stmt[i], g_namespace);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 开启乐观事务
        ret = GmcTransStart(conn[i], &config);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 确保事务1回滚时,其余事务不回滚
        if (i == 0) {
            AW_FUN_Log(LOG_STEP, "sleep确保事务1回滚时,其余事务不回滚");
            sleep(10);
        }

        // merge
        ret = testGmcPrepareStmtByLabelName(stmt[i], labelname, GMC_OPERATION_MERGE);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint32_t g_pk_value = 0;
        ret = GmcSetIndexKeyName(stmt[i], pk_name);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt[i], 0, GMC_DATATYPE_UINT32, &g_pk_value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty1(stmt[i], 0 + end_num);
        ret = GmcExecute(stmt[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否merge成功
        ret = GmcGetStmtAttr(stmt[i], GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(2, affectRows);

        // 读
        QueryVertexProperty(stmt[i], 0 + end_num, labelname, pk_name);
    }

    // 16个乐观事务对同一批数据执行replace操作
    for (uint32_t i = 16; i < 32; i++) {
        // 建连
        ret = testGmcConnect(&conn[i], &stmt[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcUseNamespace(stmt[i], g_namespace);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 开启乐观事务
        ret = GmcTransStart(conn[i], &config);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // replace
        ret = testGmcPrepareStmtByLabelName(stmt[i], labelname, GMC_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        set_VertexProperty_PK1(stmt[i], 1);
        set_VertexProperty1(stmt[i], 1 + end_num);
        ret = GmcExecute(stmt[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否replace成功
        ret = GmcGetStmtAttr(stmt[i], GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(2, affectRows);

        // 读
        QueryVertexProperty(stmt[i], 1 + end_num, labelname, pk_name);
    }
    // sleep 超過事务管理日志记录阈值
    sleep(10);
    // 查询视图及日志预期，都存在日志打印无回滚
    AW_FUN_Log(LOG_STEP, "超過事务管理日志记录阈值,预期32个事务需要记录日志");
    ExpextViewResultLog32();
    ret = system("cat ../../../log/run/rgmserver/rgmserver.log | grep -o \"Long Transaction\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char errorMsg3[errCodeLen] = {0};
    (void)snprintf(errorMsg3, errCodeLen, "GMERR-%d", GMERR_TRANSACTION_ROLLBACK);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg3);

    // sleep(超过回滚阈值需要回滚)
    sleep(10);
    AW_FUN_Log(LOG_STEP, "事务1超过回滚阈值需要回滚");

    ret = GmcTransStart(conn[0], &config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, ret);

    // 提交事务一
    ret = GmcTransCommit(conn[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, ret);
    ExpextViewResultLog32Back1();

    AW_FUN_Log(LOG_STEP, "事务1回滚");
    ret = GmcTransRollBack(conn[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcGetLastError();
    // 查询视图及日志预期，都存在日志打印无回滚
    AW_FUN_Log(LOG_STEP, "事务一超过回滚阈值需要回滚");
    ExpextViewResultLog32Back1();
    ret = system("cat ../../../log/run/rgmserver/rgmserver.log | grep -o \"Long Transaction\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交剩余31个事务
    for (uint32_t i = 1; i < 32; i++) {
        // 事务2提交
        if (i == 1) {
            ret = GmcTransCommit(conn[i]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcTransCommit(conn[i]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, ret);
            ret = GmcTransRollBack(conn[i]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }

    // drop vertex
    ret = GmcDropVertexLabel(g_stmt, labelname);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // drop namespace
    ret = GmcDropNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放连接
    for (uint32_t i = 0; i < 16; i++) {
        ret = testGmcDisconnect(conn[i], stmt[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmadmin -cfgName enableLogFold -cfgVal 1");
    AW_FUN_Log(LOG_STEP, "END");
}

/* ****************************************************************************
 Description
:039.(yang）乐观可重复读，开启长事务管理开关，事务开启，批操作失败，未回滚，达到日志记录阈值，查询长事务事务视图 Author:
youwanyong
**************************************************************************** */
TEST_F(SuperLongTransaction, Yang_024_039)
{
    // 创建namespace、container-container类型，事务1对表预置数据，开启乐观事务2、3，分别对child
    // container执行delete、remove操作
    AW_FUN_Log(LOG_STEP, "START");

    uint32_t ret = 0;
    uint32_t fieldValue = 0, PID = 0, newvalue = 0;
    AsyncUserDataT userData = {0};
    GmcBatchT *batch = NULL;
    AW_FUN_Log(LOG_STEP, "START");

    // 开启长事务管理功能,同时将日志阈值及回滚阈值降为（10.15）
    system("sh $TEST_HOME/tools/modifyCfg.sh \"trxMonitorThreshold=10,15\"");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建连，同时创建新的namespace
    ExpextViewResultNormal();
    ret = system("cat ../../../log/run/rgmserver/rgmserver.log | grep -o \"Long Transaction\"");
    AW_MACRO_EXPECT_EQ_INT(256, ret);

    // 创建同步连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建3个异步连接
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async1, &g_stmt_async1, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async2, &g_stmt_async2, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcAllocStmt(g_conn_async, &g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_con);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = g_namespace;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};

    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcUseNamespaceAsync(g_stmt_async, g_namespace, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 创建vertex表(container--container类型)
    readJanssonFile("schemafile/Yang/Con_Con.gmjson", &g_schema);
    AW_MACRO_ASSERT_NOTNULL(g_schema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_schema, g_config, create_vertex_label_callback, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_schema);

    // 事务结构体
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_REPEATABLE;  // 可重复读
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX;  // 乐观事务

    /* ************** 事务一对container、child container执行create   操作  ****************** */
    // 开启乐观事务一
    ret = GmcTransStartAsync(g_conn_async, &config, trans_start_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_ConConRootName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *rootNode = NULL, *childNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &rootNode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcYangEditChildNode(rootNode, g_ConConChildName, GMC_OPERATION_INSERT, &childNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child的属性值
    fieldValue = 100;
    testYangSetNodeProperty(childNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    AW_MACRO_EXPECT_EQ_INT(1, userData.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, userData.succNum);
    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 提交事务一
    ret = GmcTransCommitAsync(g_conn_async, trans_commit_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    /* ************** 事务二对child container执行delete操作  ****************** */
    // 开启乐观事务二
    ret = GmcUseNamespaceAsync(g_stmt_async1, g_namespace, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcTransStartAsync(g_conn_async1, &config, trans_start_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 设置批处理
    ret = testBatchPrepare(g_conn_async1, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    rootNode = NULL;
    childNode = NULL;
    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_ConConRootName, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &rootNode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(rootNode, g_ConConChildName, GMC_OPERATION_DELETE_GRAPH, &childNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    AW_MACRO_EXPECT_EQ_INT(1, userData.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, userData.succNum);
    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // sleep等待綫程二超时
    sleep(10);
    ExpextViewResultLog();
    ret = system("cat ../../../log/run/rgmserver/rgmserver.log | grep -o \"Long Transaction\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /* ************** 事务三对child container执行remove操作  ****************** */
    // 开启乐观事务三
    ret = GmcUseNamespaceAsync(g_stmt_async2, g_namespace, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcTransStartAsync(g_conn_async2, &config, trans_start_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 设置批处理
    ret = testBatchPrepare(g_conn_async2, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    rootNode = NULL;
    childNode = NULL;
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_ConConRootName, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &rootNode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(rootNode, g_ConConChildName, GMC_OPERATION_REMOVE_GRAPH, &childNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    AW_MACRO_EXPECT_EQ_INT(1, userData.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, userData.succNum);
    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // sleep 等待线程2超过回滚阈值
    sleep(5);
    AW_FUN_Log(LOG_STEP, "回滚事务2");
    // 提交事务二
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_TRANSACTION_ROLLBACK);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    ret = GmcTransCommitAsync(g_conn_async1, trans_commit_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 视图结果不确定，需要在等服务端已完全处理完回滚事务后再去查验视图结果
    ExpextViewResultBack();
    ret = system("cat ../../../log/run/rgmserver/rgmserver.log | grep -o \"Long Transaction\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预期事务2回滚成功
    ret = GmcTransRollBackAsync(g_conn_async1, trans_commit_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 提交事务三
    ret = GmcTransCommitAsync(g_conn_async2, trans_commit_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = testGmcGetLastError();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // drop edge表
    ret = GmcClearNamespaceAsync(g_stmt_async, g_namespace, ClearNSCallbak, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 断连
    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_async, g_namespace, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    GmcFreeStmt(g_stmt_root);
    GmcFreeStmt(g_stmt_con);

    // 释放同步连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放3个异步连接
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async1, g_stmt_async1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async2, g_stmt_async2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "end");
    // 事务查询校验
    ExpextViewResultBack();
    ret = system("cat ../../../log/run/rgmserver/rgmserver.log | grep -o \"Long Transaction\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description
:032.创建两个nameSpace，namespace1开启两个事务，对同一张表进行操作，线程一提交，线程二回滚，namespaxe2.对同一张表操作，线程一提交线程为提交
 Author: youwanyong
**************************************************************************** */
TEST_F(SuperLongTransaction, Yang_024_032)
{
    // 创建namespace、container-container类型，事务1对表预置数据，开启乐观事务2对child
    // container执行delete、remove操作
    AW_FUN_Log(LOG_STEP, "START");

    uint32_t ret = 0;
    uint32_t fieldValue = 0, PID = 0, newvalue = 0;
    AsyncUserDataT userData = {0};
    GmcBatchT *batch = NULL;
    AW_FUN_Log(LOG_STEP, "START");

    // 开启长事务管理功能,同时将日志阈值及回滚阈值降为（10.15）
    system("sh $TEST_HOME/tools/modifyCfg.sh \"trxMonitorThreshold=10,15\"");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建连，同时创建新的namespace
    ExpextViewResultNormal();
    ret = system("cat ../../../log/run/rgmserver/rgmserver.log | grep -o \"Long Transaction\"");
    AW_MACRO_EXPECT_EQ_INT(256, ret);

    // 创建同步连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建3个异步连接
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async1, &g_stmt_async1, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async2, &g_stmt_async2, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcAllocStmt(g_conn_async, &g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_con);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = g_namespace;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};

    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcUseNamespaceAsync(g_stmt_async, g_namespace, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 创建vertex表(container--container类型)
    readJanssonFile("schemafile/Yang/Con_Con.gmjson", &g_schema);
    AW_MACRO_ASSERT_NOTNULL(g_schema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_schema, g_config, create_vertex_label_callback, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_schema);

    // 事务结构体
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_REPEATABLE;  // 可重复读
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX;  // 乐观事务

    /* ************** 对container、child container执行create   操作  ****************** */
    // 开启乐观事务一
    ret = GmcTransStartAsync(g_conn_async, &config, trans_start_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_ConConRootName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *rootNode = NULL, *childNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &rootNode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcYangEditChildNode(rootNode, g_ConConChildName, GMC_OPERATION_INSERT, &childNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child的属性值
    fieldValue = 100;
    testYangSetNodeProperty(childNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    AW_MACRO_EXPECT_EQ_INT(1, userData.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, userData.succNum);
    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 提交事务一
    ret = GmcTransCommitAsync(g_conn_async, trans_commit_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    /* ************** 事务二对child container执行remove操作  ****************** */
    // 开启乐观事务二
    ret = GmcUseNamespaceAsync(g_stmt_async2, g_namespace, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcTransStartAsync(g_conn_async2, &config, trans_start_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 设置批处理
    ret = testBatchPrepare(g_conn_async2, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    rootNode = NULL;
    childNode = NULL;
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_ConConRootName, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &rootNode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(rootNode, g_ConConChildName, GMC_OPERATION_REMOVE_GRAPH, &childNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // sleep等待綫程二超时
    sleep(10);
    ExpextViewResultLog();
    ret = system("cat ../../../log/run/rgmserver/rgmserver.log | grep -o \"Long Transaction\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    AW_MACRO_EXPECT_EQ_INT(1, userData.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, userData.succNum);
    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // sleep 等待线程2超过回滚阈值
    sleep(5);
    AW_FUN_Log(LOG_STEP, "回滚事务2");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_TRANSACTION_ROLLBACK);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    // 提交事务二
    ret = GmcTransCommitAsync(g_conn_async2, trans_commit_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = testGmcGetLastError();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 视图结果不确定，需要在等服务端已完全处理完回滚事务后再去查验视图结果
    ExpextViewResultBack();
    ret = system("cat ../../../log/run/rgmserver/rgmserver.log | grep -o \"Long Transaction\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // drop edge表
    ret = GmcClearNamespaceAsync(g_stmt_async, g_namespace, ClearNSCallbak, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 断连
    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_async, g_namespace, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    GmcFreeStmt(g_stmt_root);
    GmcFreeStmt(g_stmt_con);

    // 释放同步连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放3个异步连接
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async1, g_stmt_async1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async2, g_stmt_async2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "end");
    // 事务查询校验
    ExpextViewResultBack();
    ret = system("cat ../../../log/run/rgmserver/rgmserver.log | grep -o \"Long Transaction\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  :  034.开启长事务管理，显示事务超时，不支持事务提交
 Author       : youwanyong
**************************************************************************** */
TEST_F(SuperLongTransaction, Yang_024_034)
{
    AW_FUN_Log(LOG_STEP, "START");

    AsyncUserDataT userData = {0};
    GmcBatchT *batch = NULL;
    uint32_t fieldValue = 0, PID = 0, newvalue = 0;
    uint32_t ret;

    // 开启长事务管理功能,同时将日志阈值及回滚阈值降为（10.15）
    system("sh $TEST_HOME/tools/modifyCfg.sh \"trxMonitorThreshold=10,15\"");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh");

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_TRANSACTION_ROLLBACK);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建连，同时创建新的namespace
    AW_FUN_Log(LOG_STEP, "START");
    ExpextViewResultNormal();
    ret = system("cat ../../../log/run/rgmserver/rgmserver.log | grep -o \"Long Transaction\"");
    AW_MACRO_EXPECT_EQ_INT(256, ret);

    // 创建同步连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建异步连接
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcAllocStmt(g_conn_async, &g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_con);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = g_namespace;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};

    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcUseNamespaceAsync(g_stmt_async, g_namespace, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 创建yang表
    readJanssonFile("schemafile/Yang/Con_Con.gmjson", &g_schema);
    AW_MACRO_ASSERT_NOTNULL(g_schema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_schema, g_config, create_vertex_label_callback, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_schema);

    // 开启乐观可重复读事务
    // 事务结构体
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_REPEATABLE;  // 可重复读
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX;  // 乐观事务

    /* ************** 事务一对container、child container执行create   操作  ****************** */
    // 开启乐观事务一
    ret = GmcTransStartAsync(g_conn_async, &config, trans_start_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_ConConRootName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *rootNode = NULL, *childNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &rootNode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcYangEditChildNode(rootNode, g_ConConChildName, GMC_OPERATION_INSERT, &childNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child的属性值
    fieldValue = 100;
    testYangSetNodeProperty(childNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    AW_MACRO_EXPECT_EQ_INT(1, userData.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, userData.succNum);
    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // sleep超过日志记录阈值
    sleep(10);
    ExpextViewResultLog();
    ret = system("cat ../../../log/run/rgmserver/rgmserver.log | grep -o \"Long Transaction\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // sleep超过回滚阈值
    sleep(5);

    // 提交事务一,提交失败，回滚成功
    ret = GmcTransCommitAsync(g_conn_async, trans_commit_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcTransRollBackAsync(g_conn_async, trans_commit_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    AW_FUN_Log(LOG_STEP, "提交失败回滚成功");

    ExpextViewResultBack();
    ret = system("cat ../../../log/run/rgmserver/rgmserver.log | grep -o \"Long Transaction\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 断连
    // 删表
    // drop edge表
    ret = GmcClearNamespaceAsync(g_stmt_async, g_namespace, ClearNSCallbak, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_async, g_namespace, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    GmcFreeStmt(g_stmt_root);
    GmcFreeStmt(g_stmt_con);

    // 释放同步连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放3个异步连接
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async1, g_stmt_async1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async2, g_stmt_async2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "end");
}

void *TransReplaceBackthread(void *args)
{
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    int startNum = 0, endNum = 10, affectRows = 0, ret = 0;
    char pkName[] = "pk";
    const char *labelName = "vertex_00_Memory";
    const char *nameSpace = (const char *)"userA";

    // 创建同步连接
    ret = testGmcConnect(&conn1, &stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(stmt1, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务结构体
    GmcTxConfigT config1;
    config1.transMode = GMC_TRANS_USED_IN_CS;
    config1.type = GMC_TX_ISOLATION_REPEATABLE;  // 可重复读
    config1.readOnly = false;
    config1.trxType = GMC_OPTIMISTIC_TRX;  // 乐观事务

    // 开启乐观事务一
    ret = GmcTransStart(conn1, &config1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务一执行 replace && query
    for (int i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        set_VertexProperty_PK1(stmt1, i);
        set_VertexProperty1(stmt1, i + endNum);
        ret = GmcExecute(stmt1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否replace成功
        ret = GmcGetStmtAttr(stmt1, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(2, affectRows);

        // 读
        QueryVertexProperty(stmt1, i + endNum, labelName, pkName);
    }
    sleep(6);
    // 事务一回滚，
    ret = GmcTransCommit(conn1);
    EXPECT_TRUE(GMERR_TRANSACTION_ROLLBACK);
    ret = GmcTransRollBack(conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "repalce回滚线程回滚");

    // 断连
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void *TransReplaceLogthread(void *args)
{
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    int startNum = 0, endNum = 10, affectRows = 0, ret = 0;
    char pkName[] = "pk";
    const char *labelName = "vertex_00_Memory";
    const char *nameSpace = (const char *)"userA";

    // 创建同步连接
    ret = testGmcConnect(&conn1, &stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(stmt1, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务结构体
    GmcTxConfigT config1;
    config1.transMode = GMC_TRANS_USED_IN_CS;
    config1.type = GMC_TX_ISOLATION_REPEATABLE;  // 可重复读
    config1.readOnly = false;
    config1.trxType = GMC_OPTIMISTIC_TRX;  // 乐观事务

    // 开启乐观事务一
    ret = GmcTransStart(conn1, &config1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务一执行 replace && query
    for (int i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        set_VertexProperty_PK1(stmt1, i);
        set_VertexProperty1(stmt1, i + endNum);
        ret = GmcExecute(stmt1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否replace成功
        ret = GmcGetStmtAttr(stmt1, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(2, affectRows);

        // 读
        QueryVertexProperty(stmt1, i + endNum, labelName, pkName);
    }
    sleep(2);
    // 事务一回滚
    ret = GmcTransCommit(conn1);
    // 綫程竞争导致结果变化
    EXPECT_TRUE(GMERR_RESTRICT_VIOLATION == ret || GMERR_OK == ret || GMERR_TRANSACTION_ROLLBACK == ret);
    AW_FUN_Log(LOG_STEP, "3149 ret = %d.", ret);
    ret = GmcTransRollBack(conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 断连
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void *TransDeletethread(void *args)
{
    GmcConnT *conn2 = NULL;
    GmcStmtT *stmt2 = NULL;
    int startNum = 0, endNum = 10, affectRows = 0, ret = 0;
    char pkName[] = "pk";
    const char *labelName = "vertex_00_Memory";
    const char *nameSpace = (const char *)"userA";

    // 创建同步连接 2
    ret = testGmcConnect(&conn2, &stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(stmt2, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务结构体
    GmcTxConfigT config1;
    config1.transMode = GMC_TRANS_USED_IN_CS;
    config1.type = GMC_TX_ISOLATION_REPEATABLE;  // 可重复读
    config1.readOnly = false;
    config1.trxType = GMC_OPTIMISTIC_TRX;  // 乐观事务

    // 开启乐观事务二
    ret = GmcTransStart(conn2, &config1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务二执行 delete && query
    for (int i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt2, labelName, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 主键删除
        ret = GmcSetIndexKeyValue(stmt2, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt2, pkName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否delete成功
        ret = GmcGetStmtAttr(stmt2, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, affectRows);

        // 检查所删除
        ret = testGmcPrepareStmtByLabelName(stmt2, labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt2, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt2, pkName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt2, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(isFinish, true);
    }

    // 事务二回滚
    ret = GmcTransRollBack(conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "删除线程回滚");

    // 断连 2
    ret = testGmcDisconnect(conn2, stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void QueryView(const char *expect)
{
    uint32_t ret;
    char *result = NULL;
    ret = TestViewData(g_command, &result);
    EXPECT_EQ(GMERR_OK, ret);
    printf("----------------------\n");
    printf(result);
    EXPECT_STRNE(NULL, strstr(result, expect));

    (void)free(result);
}
void *QueryLongTransactiionThread(void *args)
{
    // 保证和回滚函数后面结束线程
    QueryView(((LongTransactionViewStruct *)args)->expectView);
    QueryLog(((LongTransactionViewStruct *)args)->expectValue);
}

/* ****************************************************************************
 Description  :045.多线程，开启16个线程，前15个线程，每个线程创建一个事务，开启长事务管理，最后一个线程查询视图
 Author: youwanyong
**************************************************************************** */
TEST_F(SuperLongTransaction, Yang_024_045)
{
    AW_FUN_Log(LOG_STEP, "START");
    uint32_t ret = 0, err = 0;
    pthread_t Dml[16];
    uint32_t start_num = 0;
    uint32_t end_num = 100;
    LongTransactionViewStruct data{0};

    // 关闭长事务管理功能,同时将日志阈值及回滚阈值降为（10.15）
    system("sh $TEST_HOME/tools/modifyCfg.sh \"trxMonitorThreshold=1,5\"");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_TRANSACTION_ROLLBACK);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建连，同时创建新的namespace
    AW_FUN_Log(LOG_STEP, "START");
    ExpextViewResultNormal();
    ret = system("cat ../../../log/run/rgmserver/rgmserver.log | grep -o \"Long Transaction\"");
    AW_MACRO_EXPECT_EQ_INT(256, ret);

    // 创建同步连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = 0;
    GmcConnT *conn[1024] = {NULL};
    GmcStmtT *stmt[1024] = {NULL};

    // create namespace
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = g_namespace;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观

    char errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg2);
    GmcDropNamespace(g_stmt, g_namespace);
    ret = GmcCreateNamespaceWithCfg(g_stmt, &nspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建vertexlabel
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    readJanssonFile("schemafile/Yang/NormalVertexLabel_001.gmjson", &g_schema);
    AW_MACRO_ASSERT_NE_INT((void *)NULL, g_schema);

    GmcDropVertexLabel(g_stmt, "vertex_00_Memory");
    ret = GmcCreateVertexLabel(g_stmt, g_schema, g_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_schema);

    // insert && query
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, "vertex_00_Memory", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty_PK1(g_stmt, i);
        set_VertexProperty1(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否插入成功
        int affectRows = 0;
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, affectRows);

        // 读
        QueryVertexProperty(g_stmt, i, "vertex_00_Memory", "pk");
    }

    // // 多线程
    for (uint32_t i = 0; i < 15; i++) {
        if (i % 2 == 0) {
            ret = pthread_create(&Dml[i], NULL, TransReplaceBackthread, NULL);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }

    sleep(5);
    for (uint32_t i = 0; i < 15; i++) {
        if (i % 2 != 0) {
            ret = pthread_create(&Dml[i], NULL, TransDeletethread, NULL);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
     for (int i = 0; i < 15; i++) {
        pthread_join(Dml[i], NULL);
    }
    // 查询线程函数
    const char *expect = R"(
  LONG_TRANS_LOG_COUNT: 8
  LONG_TRANS_ROLLBACK_COUNT: 8)";
    data.expectView = expect;
    char expectVal[10] = "8";
    data.expectValue = expectVal;
    ret = pthread_create(&Dml[15], NULL, QueryLongTransactiionThread, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_join(Dml[15], NULL);
    const char *expectEnd = R"(
  LONG_TRANS_LOG_COUNT: 8
  LONG_TRANS_ROLLBACK_COUNT: 8)";
    QueryView(expectEnd);
    QueryLog(expectVal);
    GmcDropVertexLabel(g_stmt, "vertex_00_Memory");
    AW_FUN_Log(LOG_STEP, "END");
}
/* ****************************************************************************
 Description  :046.多线程，开启16个线程，前15个线程，每个线程创建一个事务，开启长事务管理，
 最后一个线程查询视图，预期5个事务未超时，5个事务达到记录日志阈值，5个事务达到回滚阈值
 Author: youwanyong
**************************************************************************** */
TEST_F(SuperLongTransaction, Yang_024_046)
{
    AW_FUN_Log(LOG_STEP, "START");
    uint32_t ret = 0, err = 0;
    pthread_t Dml[16];
    uint32_t start_num = 0;
    uint32_t end_num = 100;
    LongTransactionViewStruct data{0};

    // 关闭长事务管理功能,同时将日志阈值及回滚阈值降为（10.15）
    system("sh $TEST_HOME/tools/modifyCfg.sh \"trxMonitorThreshold=2,4\"");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmadmin -cfgName enableLogFold -cfgVal 0");

    // 建连，同时创建新的namespace
    AW_FUN_Log(LOG_STEP, "START");
    ExpextViewResultNormal();
    ret = system("cat ../../../log/run/rgmserver/rgmserver.log | grep -o \"Long Transaction\"");
    AW_MACRO_EXPECT_EQ_INT(256, ret);

    // 创建同步连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = 0;
    GmcConnT *conn[1024] = {NULL};
    GmcStmtT *stmt[1024] = {NULL};

    // create namespace
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = g_namespace;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观

    GmcDropNamespace(g_stmt, g_namespace);
    ret = GmcCreateNamespaceWithCfg(g_stmt, &nspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_TRANSACTION_ROLLBACK);
    char errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_RESTRICT_VIOLATION);
    char errorMsg3[errCodeLen] = {0};
    (void)snprintf(errorMsg3, errCodeLen, "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    AW_ADD_ERR_WHITE_LIST(3, errorMsg1, errorMsg2, errorMsg3);
    // 创建vertexlabel
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    readJanssonFile("schemafile/Yang/NormalVertexLabel_001.gmjson", &g_schema);
    AW_MACRO_ASSERT_NE_INT((void *)NULL, g_schema);

    GmcDropVertexLabel(g_stmt, "vertex_00_Memory");
    ret = GmcCreateVertexLabel(g_stmt, g_schema, g_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_schema);

    // insert && query
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, "vertex_00_Memory", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty_PK1(g_stmt, i);
        set_VertexProperty1(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否插入成功
        int affectRows = 0;
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, affectRows);

        // 读
        QueryVertexProperty(g_stmt, i, "vertex_00_Memory", "pk");
    }

    // 多线程
    for (uint32_t i = 0; i < 5; i++) {
        ret = pthread_create(&Dml[i], NULL, TransReplaceBackthread, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    sleep(3); // 保证前3s不会并发到后面的线程
    for (uint32_t i = 10; i < 15; i++) {
        ret = pthread_create(&Dml[i], NULL, TransReplaceLogthread, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    sleep(2);
    for (uint32_t i = 5; i < 10; i++) {
        ret = pthread_create(&Dml[i], NULL, TransDeletethread, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 查询线程函数
    char expectVal[10] = "10";
    const char *expect = R"(
  LONG_TRANS_LOG_COUNT: 10
  LONG_TRANS_ROLLBACK_COUNT: 5)";
    data.expectView = expect;
    data.expectValue = expectVal;
    ret = pthread_create(&Dml[15], NULL, QueryLongTransactiionThread, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 5; i++) {
        pthread_join(Dml[i], NULL);
    }
    for (int i = 5; i < 16; i++) {
        pthread_join(Dml[i], NULL);
    }
    const char *expectEnd = R"(
  LONG_TRANS_LOG_COUNT: 10
  LONG_TRANS_ROLLBACK_COUNT: 5)";
    QueryView(expectEnd);
    QueryLog(expectVal);
    GmcDropVertexLabel(g_stmt, "vertex_00_Memory");
    system("gmadmin -cfgName enableLogFold -cfgVal 1");
    AW_FUN_Log(LOG_STEP, "END");
}

/* ****************************************************************************
 Description  :048.并发25个线程循环400次，开启事务管理，开启事务，查询视图，写数据，校验，
 超日志记录，视图，插数据，插回滚，插数据，查视图。
 Author: youwanyong
**************************************************************************** */
TEST_F(SuperLongTransaction, Yang_024_048)
{
    AW_FUN_Log(LOG_STEP, "START");
    uint32_t ret = 0, err = 0;
    pthread_t Dml[25];
    uint32_t start_num = 0;
    uint32_t end_num = 100;
    LongTransactionViewStruct data{0};

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_TRANSACTION_ROLLBACK);
    char errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_RESTRICT_VIOLATION);
    char errorMsg3[errCodeLen] = {0};
    (void)snprintf(errorMsg3, errCodeLen, "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    AW_ADD_ERR_WHITE_LIST(3, errorMsg1, errorMsg2, errorMsg3);

    // 关闭长事务管理功能,同时将日志阈值及回滚阈值降为（10.15）
    system("sh $TEST_HOME/tools/modifyCfg.sh \"trxMonitorThreshold=2,5\"");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建连，同时创建新的namespace
    AW_FUN_Log(LOG_STEP, "START");
    ExpextViewResultNormal();
    ret = system("cat ../../../log/run/rgmserver/rgmserver.log | grep -o \"Long Transaction\"");
    AW_MACRO_EXPECT_EQ_INT(256, ret);

    // 创建同步连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = 0;
    GmcConnT *conn[1024] = {NULL};
    GmcStmtT *stmt[1024] = {NULL};

    // create namespace
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = g_namespace;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观

    GmcDropNamespace(g_stmt, g_namespace);
    ret = GmcCreateNamespaceWithCfg(g_stmt, &nspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建vertexlabel
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    readJanssonFile("schemafile/Yang/NormalVertexLabel_001.gmjson", &g_schema);
    AW_MACRO_ASSERT_NE_INT((void *)NULL, g_schema);

    GmcDropVertexLabel(g_stmt, "vertex_00_Memory");
    ret = GmcCreateVertexLabel(g_stmt, g_schema, g_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_schema);

    // insert && query
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, "vertex_00_Memory", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty_PK1(g_stmt, i);
        set_VertexProperty1(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否插入成功
        int affectRows = 0;
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, affectRows);

        // 读
        QueryVertexProperty(g_stmt, i, "vertex_00_Memory", "pk");
    }

    // // 多线程
    for (uint32_t i = 0; i < 8; i++) {
        ret = pthread_create(&Dml[i], NULL, TransReplaceBackthread, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 8; i < 16; i++) {
        ret = pthread_create(&Dml[i], NULL, TransDeletethread, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 16; i < 24; i++) {
        ret = pthread_create(&Dml[i], NULL, TransReplaceLogthread, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 查询线程函数
    for (int i = 0; i < 8; i++) {
        pthread_join(Dml[i], NULL);
    }
    for (int i = 8; i < 24; i++) {
        pthread_join(Dml[i], NULL);
    }
    const char *expectEnd = R"(
  LONG_TRANS_LOG_COUNT: 16
  LONG_TRANS_ROLLBACK_COUNT: 8)";
  char expectVal[10] = "10";
    QueryView(expectEnd);
    QueryLog(expectVal);
    GmcDropVertexLabel(g_stmt, "vertex_00_Memory");
    AW_FUN_Log(LOG_STEP, "END");
}
