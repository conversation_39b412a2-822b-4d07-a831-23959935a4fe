/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2012-2018. All rights reserved.
 Description  :表结构升降级工具升级功能测试
 Author       : wuxiaochun wx753022
 Modification :
001.批量升级，一次升级多张表
002.升级一张简单表，含所有支持的定长字段
003.升级一张一般复杂表，含所有支持的字段和节点
004.升级一张特殊复杂表，含所有支持的字段和节点
005.升级一张表，指定表名，与schema文件一致
006.兼容性升级一张表，指定表名，与schema文件不一致，与存在的表名一致
007.非兼容性升级一张表，指定表名，与schema文件不一致，与存在的表名一致
008.多个线程并发升级同一张表的同一个版本
009.多个线程并发批量升级同一个配置文件
010.多个线程并发升级不同表
011.写满catalog系统内存后再进行表升级操作
012.升级时同步进行truncate操作
013.写满存储区内存后进行表升级
 Date         : 2023/03/13
**************************************************************************** */
#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include <sys/time.h>
#include <time.h>

#include "gtest/gtest.h"
#include "../002_VertexLabelDowngrade/VertexLabelDowngradeStruct.h"

pthread_barrier_t g_barrierT;

class GmddlFunUpg : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void GmddlFunUpg::SetUpTestCase()
{
    system("sh ${TEST_HOME}/tools/start.sh");
    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
void GmddlFunUpg::TearDownTestCase()
{
    int ret = 0;
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    testEnvClean();
}

void GmddlFunUpg::SetUp()
{
    int ret = 0;
    g_conn = NULL;
    g_stmt = NULL;
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}
void GmddlFunUpg::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = 0;
    uint32_t i = 0;
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
}

class GmddlFunUpg2 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void GmddlFunUpg2::SetUpTestCase()
{
    int ret = 0;
    if (g_envType == 2) {
        system("sh $TEST_HOME/tools/start.sh");
    } else {
        system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysShmSize=32\"");
        system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysDynSize=64\"");
        system("sh $TEST_HOME/tools/modifyCfg.sh \"maxNormalTableNum=10000\"");
        system("sh $TEST_HOME/tools/start.sh -f");
        sleep(1);
        system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
    }
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建epoll
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void GmddlFunUpg2::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}

void GmddlFunUpg2::SetUp()
{
    // 建连
    int ret = 0;
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void GmddlFunUpg2::TearDown()
{
    int ret = 0;
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
}

class GmddlFunUpg3 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void GmddlFunUpg3::SetUpTestCase()
{
}

void GmddlFunUpg3::TearDownTestCase()
{
}

void GmddlFunUpg3::SetUp()
{
}

void GmddlFunUpg3::TearDown()
{
}

// 001.批量升级，一次升级多张表
TEST_F(GmddlFunUpg, Upgrade_005_003_001_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 10;
    int32_t schemaVersion = 2;
    bool isDefaultValue = false;
    char *expectValue = (char *)"upgrade successfully";
    char labelName[128] = {0};
    char schemaParth[256] = {0};
    char *batchUpFile = (char *)"./schemaFile/batchUpgradeFun_001.gmjson";
    char *testSchema = NULL;
    int tabelNum = 512;
    system("sh create_multi_label.sh 512");
    for (int i = 1; i <= tabelNum; i++) {
        memset(schemaParth, 0, 256);
        memset(labelName, 0, 128);
        (void)snprintf(schemaParth, 256, "./multi_vertexlabel/simpleLabel%d.gmjson", i);
        (void)snprintf(labelName, 128, "simpleLabel%d", i);
        // 创表
        ret = TestCreateLabel(g_stmt, schemaParth, labelName);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "labelName: %s create failed!!\n", labelName);
        }
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = TestUpdateVertexLabel(batchUpFile, expectValue, NULL, (char *)"batch");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 1; i <= tabelNum; i++) {
        memset(labelName, 0, 128);
        (void)snprintf(labelName, 128, "simpleLabel%d", i);
        ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, labelName, schemaVersion, GMC_OPERATION_SCAN);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        // 删表
        ret = GmcDropVertexLabel(g_stmt, labelName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

// 002.升级一张简单表，含所有支持的定长字段
TEST_F(GmddlFunUpg, Upgrade_005_003_001_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 10;
    int32_t schemaVersion = 3;
    bool isDefaultValue = false;
    char *expectValue = (char *)"upgrade successfully";
    char labelName[128] = {0};
    char *schemaParth = (char *)"./schemaFile/simpleLabel1.gmjson";
    char *schemaUpFile = (char *)"./schemaFile/SimpleLabelUpgradeFullFields2.gmjson";

    // 创表
    ret = TestCreateLabel(g_stmt, schemaParth, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestUpdateVertexLabel(schemaUpFile, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName, schemaVersion, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // Insert插入数据
    GtSimplelabelCfgT vertexCfg1 = {startValue, endValue, 0, 1, GMC_OPERATION_INSERT};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg1, schemaVersion, isDefaultValue);
    // 删表
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 003.升级一张一般复杂表，含所有支持的字段和节点
TEST_F(GmddlFunUpg, Upgrade_005_003_001_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 10;
    int32_t schemaVersion = 2;
    bool isDefaultValue = false;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    uint8_t ipValue = 0;
    char *expectValue = (char *)"upgrade successfully";
    char *schemaParth = (char *)"./schemaFile/generalLabel1.gmjson";
    char *schemaUpFile = (char *)"./schemaFile/generalLabelNode.gmjson";

    // 创表
    ret = TestCreateLabel(g_stmt, schemaParth, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestUpdateVertexLabel(schemaUpFile, expectValue, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName3, schemaVersion, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtGeneralLabelCfg vertexCfg = {startValue, endValue, 1, 3, 3, 2, GMC_OPERATION_REPLACE, {false, true}};
    // replace插入新数据
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfg, ipValue, bytesValue, stringValue, isDefaultValue);
    // 删表
    ret = GmcDropVertexLabel(g_stmt, g_labelName3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 004.升级一张特殊复杂表，含所有支持的字段和节点
TEST_F(GmddlFunUpg, Upgrade_005_003_001_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 10;
    char *expectValue = (char *)"upgrade successfully";
    bool isDefaultValue = false;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    char *schemaUpFile = (char *)"./schemaFile/SpecialTableSchemaNode.gmjson";
    char *schemaParth = (char *)"./schemaFile/SpecialTableSchema.gmjson";
    int32_t schemaVersion = 2;

    // 创表
    ret = TestCreateLabel(g_stmt, schemaParth, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestUpdateVertexLabel(schemaUpFile, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName2, schemaVersion, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写数据
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT, {false, true, true}};
    GtSpeciallabelCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue, 0, 1, 0, 3, 3, 2, GMC_OPERATION_INSERT,
                                     {false, false, false}};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfg, bytesValue, stringValue, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel4StructWrite(g_stmt, vertexCfg3, bytesValue, stringValue, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删表
    ret = GmcDropVertexLabel(g_stmt, g_labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 005.升级一张表，不指定表名
TEST_F(GmddlFunUpg, Upgrade_005_003_001_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 10;
    int32_t schemaVersion = 3;
    bool isDefaultValue = false;
    char *expectValue = (char *)"upgrade successfully";
    char labelName[128] = {0};
    char *schemaParth = (char *)"./schemaFile/simpleLabel1.gmjson";
    char *schemaUpFile = (char *)"./schemaFile/simpleLabel.gmjson";

    // 创表
    ret = TestCreateLabel(g_stmt, schemaParth, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestUpdateVertexLabel(schemaUpFile, expectValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName, schemaVersion, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // Insert插入数据
    GtSimplelabelCfgT vertexCfg1 = {startValue, endValue, 0, 1, GMC_OPERATION_INSERT};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg1, schemaVersion, isDefaultValue);
    // 删表
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 006.兼容性升级一张表，指定表名，与schema文件不一致，与存在的表名一致
TEST_F(GmddlFunUpg, Upgrade_005_003_001_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 10;
    int32_t schemaVersion = 3;
    bool isDefaultValue = false;
    char *expectValue = (char *)"upgrade successfully";
    char labelName[128] = {0};
    char *schemaParth = (char *)"./schemaFile/simpleLabel1.gmjson";
    char *schemaUpFile = (char *)"./schemaFile/SimpleLabelUpgradeFullFields3.gmjson";

    // 创表
    ret = TestCreateLabel(g_stmt, schemaParth, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestUpdateVertexLabel(schemaUpFile, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName, schemaVersion, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // Insert插入数据
    GtSimplelabelCfgT vertexCfg1 = {startValue, endValue, 0, 1, GMC_OPERATION_INSERT};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg1, schemaVersion, isDefaultValue);
    // 删表
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 007.兼容性升级一张表，指定表名，与schema文件不一致，与存在的表名一致
TEST_F(GmddlFunUpg, Upgrade_005_003_001_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 10;
    int32_t schemaVersion = 3;
    bool isDefaultValue = false;
    char *expectValue = (char *)"upgrade successfully";
    char labelName[128] = "upgradeTest";
    char *schemaParth = (char *)"./schemaFile/simpleLabel1.gmjson";
    char *schemaUpFile = (char *)"./schemaFile/SimpleLabelUpgradeFullFields3.gmjson";
    char *testSchema = NULL;

    readJanssonFile(schemaParth, &testSchema);
    EXPECT_NE((void *)NULL, testSchema);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    // 创表
    ret = GmcCreateVertexLabelWithName(g_stmt, testSchema, g_labelConfig, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(testSchema);
    ret = TestUpdateVertexLabel(schemaUpFile, expectValue, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, labelName, schemaVersion, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删表
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

int g_successNum = 0;
int g_failNum = 0;
pthread_mutex_t g_upgradeNumLock;
void *OnlineUpgradeThread(void *args)
{
    char *schemaUpFile = (char *)"./schemaFile/SpecialTableSchemaNode.gmjson";
    char *expectValue = (char *)"upgrade successfully";
    int ret = TestUpdateVertexLabel(schemaUpFile, expectValue, g_labelName2);
    pthread_mutex_lock(&g_upgradeNumLock);
    if (ret == 0) {
        g_successNum++;
    } else {
        g_failNum++;
    }
    pthread_mutex_unlock(&g_upgradeNumLock);
    return (void *)0;
}

// 008.多个线程并发升级同一张表的同一个版本
TEST_F(GmddlFunUpg, Upgrade_005_003_001_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 10;
    char *expectValue = (char *)"upgrade successfully";
    bool isDefaultValue = false;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    char *schemaParth = (char *)"./schemaFile/SpecialTableSchema.gmjson";
    int32_t schemaVersion = 2;
    g_successNum = 0;
    g_failNum = 0;
    int err = 0;
    pthread_t wrth[CONCURRENT_CONN_SIZE];
    pthread_attr_t pThreadAttrs;
    ret = pthread_attr_init(&pThreadAttrs);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_attr_setstacksize(&pThreadAttrs, 1024000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int threadNum = CONCURRENT_CONN_SIZE;
    #if defined ENV_RTOSV2X
    threadNum = CONCURRENT_CONN_SIZE - 4;
    #endif
    err = pthread_mutex_init(&g_upgradeNumLock, NULL);
    // 创表
    ret = TestCreateLabel(g_stmt, schemaParth, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < threadNum; i++) {
        err = pthread_create(&wrth[i], &pThreadAttrs, OnlineUpgradeThread, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, err);
    }
    for (int i = 0; i < threadNum; i++) {
        pthread_join(wrth[i], NULL);
    }
    if (g_successNum != 1) {
        AW_FUN_Log(LOG_ERROR, "Multithread UpgradeTest failed! successNum: %d, failNum: %d\n", g_successNum, g_failNum);
        AW_MACRO_EXPECT_EQ_INT(0, 1);
    }
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName2, schemaVersion, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写数据
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT, {false, true, true}};
    GtSpeciallabelCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue, 0, 1, 0, 3, 3, 2, GMC_OPERATION_INSERT,
                                     {false, false, false}};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfg, bytesValue, stringValue, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel4StructWrite(g_stmt, vertexCfg3, bytesValue, stringValue, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删表
    ret = GmcDropVertexLabel(g_stmt, g_labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_mutex_destroy(&g_upgradeNumLock);
    AddWhiteList(GMERR_DUPLICATE_TABLE);
}

void *BatchUpgradeThread(void *args)
{
    char *batchUpFile = (char *)"./schemaFile/batchUpgradeFun009.gmjson";
    char *expectValue = (char *)"upgrade successfully";
    int ret = TestUpdateVertexLabel(batchUpFile, expectValue, NULL, (char *)"batch");
    pthread_mutex_lock(&g_upgradeNumLock);
    if (ret == 0) {
        g_successNum++;
    }
    pthread_mutex_unlock(&g_upgradeNumLock);
    return (void *)0;
}

// 009.多个线程并发批量升级同一个配置文件
TEST_F(GmddlFunUpg, Upgrade_005_003_001_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 10;
    int32_t schemaVersion = 2;
    bool isDefaultValue = false;
    char labelName[128] = {0};
    char schemaParth[256] = {0};
    char *testSchema = NULL;
    g_successNum = 0;
    g_failNum = 0;
    int err = 0;
    pthread_t wrth[CONCURRENT_CONN_SIZE];
    pthread_attr_t pThreadAttrs;
    ret = pthread_attr_init(&pThreadAttrs);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_attr_setstacksize(&pThreadAttrs, 1024000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int threadNum = CONCURRENT_CONN_SIZE;
    #if defined ENV_RTOSV2X
    threadNum = CONCURRENT_CONN_SIZE - 4;
    #endif
    int tabelNum = 50;
    err = pthread_mutex_init(&g_upgradeNumLock, NULL);
    system("sh create_multi_label.sh 50");
    for (int i = 1; i <= tabelNum; i++) {
        memset(schemaParth, 0, 256);
        memset(labelName, 0, 128);
        (void)snprintf(schemaParth, 256, "./multi_vertexlabel/simpleLabel%d.gmjson", i);
        (void)snprintf(labelName, 128, "simpleLabel%d", i);
        AW_FUN_Log(LOG_INFO, "labelName: %s\n", labelName);
        // 创表
        ret = TestCreateLabel(g_stmt, schemaParth, labelName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    for (int i = 0; i < threadNum; i++) {
        err = pthread_create(&wrth[i], &pThreadAttrs, BatchUpgradeThread, NULL);
        ASSERT_EQ(GMERR_OK, err);
    }
    for (int i = 0; i < threadNum; i++) {
        pthread_join(wrth[i], NULL);
    }
    if (g_successNum != 1) {
        AW_FUN_Log(LOG_ERROR, "Multithread UpgradeTest failed! successNum: %d\n", g_successNum);
        AW_MACRO_EXPECT_EQ_INT(0, 1);
    }
    for (int i = 1; i <= tabelNum; i++) {
        memset(labelName, 0, 128);
        (void)snprintf(labelName, 128, "simpleLabel%d", i);
        ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, labelName, schemaVersion, GMC_OPERATION_SCAN);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        // 删表
        ret = GmcDropVertexLabel(g_stmt, labelName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    pthread_mutex_destroy(&g_upgradeNumLock);
    AddWhiteList(GMERR_DUPLICATE_TABLE);
}

void *OnlineUpgradeThreadMuliLabel(void *args)
{
    int i = *(int *)args;
    char labelName[128] ={0};
    char schemaUpFile[256] = {0};
    (void)snprintf(labelName, 128, "simpleLabel%d", i);
    (void)snprintf(schemaUpFile, 256, "./multi_vertexlabel/upgradeLabel%d.gmjson", i);
    char *expectValue = (char *)"upgrade successfully";
    int ret = TestUpdateVertexLabel(schemaUpFile, expectValue, labelName);
    pthread_mutex_lock(&g_upgradeNumLock);
    if (ret == 0) {
        g_successNum++;
    } else {
        g_failNum++;
    }
    pthread_mutex_unlock(&g_upgradeNumLock);
    return (void *)0;
}

// 010.多个线程并发升级不同表
TEST_F(GmddlFunUpg, Upgrade_005_003_001_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 10;
    int32_t schemaVersion = 2;
    bool isDefaultValue = false;
    char labelName[128] = {0};
    char schemaParth[256] = {0};
    char *testSchema = NULL;
    g_successNum = 0;
    g_failNum = 0;
    int err = 0;
    int threadNum = 10;
    pthread_t wrth[threadNum + 1];
    pthread_attr_t pThreadAttrs;
    ret = pthread_attr_init(&pThreadAttrs);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_attr_setstacksize(&pThreadAttrs, 1024000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int aNum[threadNum + 1];
    err = pthread_mutex_init(&g_upgradeNumLock, NULL);
    system("sh create_multi_label.sh 10");
    for (int i = 1; i <= 10; i++) {
        memset(schemaParth, 0, 256);
        memset(labelName, 0, 128);
        (void)snprintf(schemaParth, 256, "./multi_vertexlabel/simpleLabel%d.gmjson", i);
        (void)snprintf(labelName, 128, "simpleLabel%d", i);
        AW_FUN_Log(LOG_INFO, "labelName: %s\n", labelName);
        // 创表
        ret = TestCreateLabel(g_stmt, schemaParth, labelName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    for (int i = 1; i <= threadNum; i++) {
        aNum[i] = i;
        err = pthread_create(&wrth[i], &pThreadAttrs, OnlineUpgradeThreadMuliLabel, &aNum[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, err);
    }
    for (int i = 1; i <= threadNum; i++) {
        pthread_join(wrth[i], NULL);
    }
    if (g_successNum != threadNum) {
        AW_FUN_Log(LOG_ERROR, "Multithread UpgradeTest failed! successNum: %d, failNum: %d\n", g_successNum, g_failNum);
        AW_MACRO_EXPECT_EQ_INT(0, 1);
    }
    for (int i = 1; i <= 10; i++) {
        memset(labelName, 0, 128);
        (void)snprintf(labelName, 128, "simpleLabel%d", i);
        ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, labelName, schemaVersion, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 删表
        ret = GmcDropVertexLabel(g_stmt, labelName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    pthread_mutex_destroy(&g_upgradeNumLock);
}

char g_vertexLabelConfig[] = "{\"max_record_count\":1000, \"isFastReadUncommitted\":0}";
char g_edgeLabelConfig[] = "{\"max_record_count\":1000, \"isFastReadUncommitted\":0}";
int32_t CreateManyVertexLabel(GmcStmtT *stmt, uint32_t labelNum, uint32_t *succNum)
{
    int ret = 0;
    uint32_t createLabelNum = labelNum;
    char *testSchema = NULL;
    char labelName[128] = {0};
    char schemaPath[256] = {0};
    char createLabelSchema[256] = {0};
    int id = 0;
    uint32_t succCount = 0;
    (void)sprintf(schemaPath, "./schemaFile/treeLabel.gmjson");
    readJanssonFile(schemaPath, &testSchema);
    EXPECT_NE((void *)NULL, testSchema);
    for (id = 1; id <= createLabelNum; id++) {
        (void)sprintf(labelName, "tree1%d", id);
        ret = GmcDropVertexLabel(stmt, labelName);
        ret = GmcCreateVertexLabelWithName(stmt, testSchema, NULL, labelName);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "CreateManyVertexLabel: ret=%d\n", ret);
            break;
        }
        succCount++;
    }
    free(testSchema);
    (*succNum) = succCount;
    return ret;
}

// 删表
void DropManyVertexLabel(GmcStmtT *stmt, uint32_t labelNum)
{
    int ret = 0;
    char labelName[128] = {0};
    uint32_t createLabelNum = labelNum;
    // 删表
    for (int id = 1; id <= createLabelNum; id++) {
        (void)sprintf(labelName, "tree1%d", id);
        ret = GmcDropVertexLabel(stmt, labelName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

int32_t CreateManyEdgeLabel(GmcStmtT *stmt, uint32_t EdgeLabelNum, uint32_t *succNum)
{
    int32_t ret = 0;
    char edgeLabelJson[1024] = {0};
    char edgeLabelName[64] = {0};
    uint32_t edgeNum = EdgeLabelNum;
    int32_t id = 0;
    uint32_t succCount = 0;
    for (id = 1; id <= edgeNum; id++) {
        (void)sprintf(edgeLabelJson,
            "[{\"name\":\"edgeT%u\",\"source_vertex_label\":\"T80\",\"comment\": \"the edge "
            "xxx\",\"dest_vertex_label\":\"T90\","
            "\"constraint\":{\"operator_type\":\"and\",\"conditions\":[{\"source_property\": \"F2\",\"dest_property\": "
            "\"F2\"},"
            "{\"source_property\": \"F3\",\"dest_property\": \"F3\"}]}}]",
            id);
        (void)sprintf(edgeLabelName, "edgeT%u", id);
        ret = GmcCreateEdgeLabel(stmt, edgeLabelJson, g_edgeLabelConfig);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "CreateManyEdgeLabel: ret=%d\n", ret);
            break;
        }
        succCount++;
    }
    (*succNum) = succCount;
    return ret;
}

int32_t CreateManyEdgeLabel2(GmcStmtT *stmt, uint32_t EdgeLabelNum, uint32_t *succNum)
{
    int32_t ret = 0;
    char edgeLabelJson[1024] = {0};
    char edgeLabelName[64] = {0};
    uint32_t edgeNum = EdgeLabelNum;
    int32_t id = 0;
    uint32_t succCount = 0;
    for (id = 1; id <= edgeNum; id++) {
        (void)sprintf(edgeLabelJson,
            "[{\"name\":\"edge2T%u\",\"source_vertex_label\":\"T20\",\"comment\": \"the edge "
            "xxx\",\"dest_vertex_label\":\"T10\","
            "\"constraint\":{\"operator_type\":\"and\",\"conditions\":[{\"source_property\": \"F2\",\"dest_property\": "
            "\"F2\"},"
            "{\"source_property\": \"F3\",\"dest_property\": \"F3\"}]}}]",
            id);
        (void)sprintf(edgeLabelName, "edgeT%u", id);
        ret = GmcCreateEdgeLabel(stmt, edgeLabelJson, g_edgeLabelConfig);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "CreateManyEdgeLabel: ret=%d\n", ret);
            break;
        }
        succCount++;
    }
    (*succNum) = succCount;
    return ret;
}

int32_t CreateManyEdgeLabel3(GmcStmtT *stmt, uint32_t EdgeLabelNum, uint32_t *succNum)
{
    int32_t ret = 0;
    char edgeLabelJson[1024] = {0};
    char edgeLabelName[64] = {0};
    uint32_t edgeNum = EdgeLabelNum;
    int32_t id = 0;
    uint32_t succCount = 0;
    for (id = 1; id <= edgeNum; id++) {
        (void)sprintf(edgeLabelJson,
            "[{\"name\":\"edge3T%u\",\"source_vertex_label\":\"T30\",\"comment\": \"the edge "
            "xxx\",\"dest_vertex_label\":\"T40\","
            "\"constraint\":{\"operator_type\":\"and\",\"conditions\":[{\"source_property\": \"F2\",\"dest_property\": "
            "\"F2\"},"
            "{\"source_property\": \"F3\",\"dest_property\": \"F3\"}]}}]",
            id);
        (void)sprintf(edgeLabelName, "edgeT%u", id);
        ret = GmcCreateEdgeLabel(stmt, edgeLabelJson, g_edgeLabelConfig);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "CreateManyEdgeLabel: ret=%d\n", ret);
            break;
        }
        succCount++;
    }
    (*succNum) = succCount;
    return ret;
}

void DropManyEdgeLabel(GmcStmtT *stmt, uint32_t EdgeLabelNum)
{
    int32_t ret = 0;
    char srcVertexLabelName[] = "T80";
    char dstVertexLabelName[] = "T90";
    uint32_t edgeNum = EdgeLabelNum;
    char edgeLabelName[64] = {0};
    // 删edgeLabel
    for (int32_t id = 1; id <= edgeNum; id++) {
        (void)sprintf(edgeLabelName, "edgeT%u", id);
        ret = GmcDropEdgeLabel(stmt, edgeLabelName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // 删vertexLabel
    ret = GmcDropVertexLabel(stmt, srcVertexLabelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, dstVertexLabelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void DropManyEdgeLabel2(GmcStmtT *stmt, uint32_t EdgeLabelNum)
{
    int32_t ret = 0;
    char srcVertexLabelName[] = "T20";
    char dstVertexLabelName[] = "T10";
    uint32_t edgeNum = EdgeLabelNum;
    char edgeLabelName[64] = {0};
    // 删edgeLabel
    for (int32_t id = 1; id <= edgeNum; id++) {
        (void)sprintf(edgeLabelName, "edge2T%u", id);
        ret = GmcDropEdgeLabel(stmt, edgeLabelName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // 删vertexLabel
    ret = GmcDropVertexLabel(stmt, srcVertexLabelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, dstVertexLabelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void DropManyEdgeLabel3(GmcStmtT *stmt, uint32_t EdgeLabelNum)
{
    int32_t ret = 0;
    char srcVertexLabelName[] = "T30";
    char dstVertexLabelName[] = "T40";
    uint32_t edgeNum = EdgeLabelNum;
    char edgeLabelName[64] = {0};
    // 删edgeLabel
    for (int32_t id = 1; id <= edgeNum; id++) {
        (void)sprintf(edgeLabelName, "edge3T%u", id);
        ret = GmcDropEdgeLabel(stmt, edgeLabelName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // 删vertexLabel
    ret = GmcDropVertexLabel(stmt, srcVertexLabelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, dstVertexLabelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void *TruncateLabelThread(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_barrier_wait(&g_barrierT);
    ret = GmcTruncateVertexLabel(stmt, g_labelName3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return (void *)0;
}

void *UpgradeLabelThread(void *args)
{
    char *expectValue = (char *)"upgrade successfully";
    char *upFile = (char *)"./schemaFile/generalLabel1_Upgrade_1M_BigObject.gmjson";
    // 批量升级表
    pthread_barrier_wait(&g_barrierT);
    int ret = TestUpdateVertexLabel(upFile, expectValue, g_labelName3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return (void *)0;
}
// 012.升级时同步进行truncate操作
TEST_F(GmddlFunUpg, Upgrade_005_003_001_012)
{
    char errorMsg1[128] = {}, errorMsg2[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_INVALID_NAME);
    (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 20;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    int32_t schemaVersion = 0;
    bool isDefaultValue = false;
    uint8_t ipValue = 0;
    char *expectValue = (char *)"upgrade successfully";
    char *schemaParth2 = (char *)"./schemaFile/generalLabel1.gmjson";
    int err = 0;
    pthread_t thread[2];
    pthread_barrier_init(&g_barrierT, NULL, 2);

    // 创表
    ret = TestCreateLabel(g_stmt, schemaParth2, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtGeneralLabelCfg vertexCfg = {startValue, endValue, 1, 3, 3, 0, GMC_OPERATION_INSERT, {false, true, true}};
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfg, ipValue, bytesValue, stringValue, isDefaultValue);

    err = pthread_create(&thread[0], NULL, UpgradeLabelThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, err);
    err = pthread_create(&thread[1], NULL, TruncateLabelThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, err);
    for (int i = 0; i < 2; i++) {
        pthread_join(thread[i], NULL);
    }
    pthread_barrier_destroy(&g_barrierT);
    // 校验是否升级
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName3, 3, GMC_OPERATION_MERGE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName3, 0, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 011.写满catalog系统内存后再进行表升级操作
TEST_F(GmddlFunUpg2, Upgrade_005_003_001_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;
    uint32_t existConnNum = 0;
    char srcVertexLabelName[] = "T80";
    char dstVertexLabelName[] = "T90";
    char *srcSchema = NULL;
    char *dstSchema = NULL;
    int64_t startValue = 0;
    int64_t endValue = 50;
    int fetchNum = 0;
    char *expectValue = (char *)"unsuccessful";
    uint32_t schemaVersion = 3;
    char *schemaUpParth = (char *)"./schemaFile/generalLabel1_Upgrade_1M_BigObject.gmjson";
    char *schemaParth = (char *)"./schemaFile/generalLabel1.gmjson";
    char *schemaUpParth2 = (char *)"./schemaFile/generalLabel1_Upgrade2.gmjson";
    char *schemaUpParth3 = (char *)"./schemaFile/generalLabel1_Upgrade3.gmjson";
    // 创表
    ret = TestCreateLabel(g_stmt, schemaParth, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // InsertOrReplace插入数据
    TestGeneralT1Write(
        g_stmt, g_labelName3, startValue, endValue, 0, (char *)"string", 0, GMC_OPERATION_INSERT, true);

    char *schemaParth1 = (char *)"schemaFile/vertexSrcLabel_test_schema.gmjson";
    char *schemaParth2 = (char *)"schemaFile/vertexDstLabel_test_schema.gmjson";
    // 创建src_vertex_label
    ret = TestCreateLabel(g_stmt, schemaParth1, srcVertexLabelName, g_vertexLabelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建dst_vertex_label
    ret = TestCreateLabel(g_stmt, schemaParth2, dstVertexLabelName, g_vertexLabelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *schemaParth3 = (char *)"schemaFile/vertexSrcLabel2.gmjson";
    char *schemaParth4 = (char *)"schemaFile/vertexDstLabel2.gmjson";
    char srcVertexName[] = "T20";
    char dstVertexName[] = "T10";
    // 创建src_vertex_label
    ret = TestCreateLabel(g_stmt, schemaParth3, srcVertexName, g_vertexLabelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建dst_vertex_label
    ret = TestCreateLabel(g_stmt, schemaParth4, dstVertexName, g_vertexLabelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *schemaParth5 = (char *)"schemaFile/vertexSrcLabel3.gmjson";
    char *schemaParth6 = (char *)"schemaFile/vertexDstLabel3.gmjson";
    char srcName[] = "T30";
    char dstName[] = "T40";
    // 创建src_vertex_label
    ret = TestCreateLabel(g_stmt, schemaParth5, srcName, g_vertexLabelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建dst_vertex_label
    ret = TestCreateLabel(g_stmt, schemaParth6, dstName, g_vertexLabelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建vertexLabel
    uint32_t vertexSuccNum = 0;
    CreateManyVertexLabel(g_stmt, 1000 * 10, &vertexSuccNum);
    AW_MACRO_EXPECT_NE_INT(0, vertexSuccNum);
    AW_FUN_Log(LOG_INFO, "vertexSuccNum: %d\n", vertexSuccNum);
    // 建edgelabel
    uint32_t edgeLabelSuccNum = 0;
    ret = CreateManyEdgeLabel(g_stmt, 0xffffffff, &edgeLabelSuccNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
    AW_FUN_Log(LOG_INFO, "edgeLabelSuccNum: %d\n", edgeLabelSuccNum);
    uint32_t edgeLabelSuccNum2 = 0;
    ret = CreateManyEdgeLabel2(g_stmt, 0xffffffff, &edgeLabelSuccNum2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
    AW_FUN_Log(LOG_INFO, "edgeLabelSuccNum2: %d\n", edgeLabelSuccNum2);
    uint32_t edgeLabelSuccNum3 = 0;
    ret = CreateManyEdgeLabel3(g_stmt, 0xffffffff, &edgeLabelSuccNum3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
    AW_FUN_Log(LOG_INFO, "edgeLabelSuccNum3: %d\n", edgeLabelSuccNum3);

    ret = TestUpdateVertexLabel(schemaUpParth, expectValue, g_labelName3);
    if (ret != GMERR_OK) {
        ret = TestUpdateVertexLabel(schemaUpParth2, expectValue, g_labelName3);
        if (ret != GMERR_OK) {
            ret = TestUpdateVertexLabel(schemaUpParth3, expectValue, g_labelName3);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }

    ret = GmcDropVertexLabel(g_stmt, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    DropManyVertexLabel(g_stmt, vertexSuccNum);
    DropManyEdgeLabel(g_stmt, edgeLabelSuccNum);
    DropManyEdgeLabel2(g_stmt, edgeLabelSuccNum2);
    DropManyEdgeLabel3(g_stmt, edgeLabelSuccNum3);
}

// 写满存储区后再升级表
TEST_F(GmddlFunUpg3, Upgrade_005_003_001_013)
{
   AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 1000000;
    char *expectValue = (char *)"upgrade successfully";
    bool isDefaultValue = false;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    char *schemaParth = (char *)"./schemaFile/SpecialTableSchema.gmjson";
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaUpgrade.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SpecialTableSchemaBigObj.gmjson";
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int count = 0;
    if (g_envType == 2) {
        system("sh $TEST_HOME/tools/start.sh");
    } else {
        system("sh $TEST_HOME/tools/stop.sh");                        // 修改配置，先停服务
        system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSeMem=300\"");  // 内存大小改小，减少单个用例执行时间
        system("sh $TEST_HOME/tools/start.sh -f");
        system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\""); // 恢复配置
    }
    sleep(1);
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创表
    ret = TestCreateLabel(stmt, schemaParth, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT};

    GtSpeciallabelVertexT *vertex = (GtSpeciallabelVertexT *)malloc(sizeof(GtSpeciallabelVertexT));
    if (vertex == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex is NULL\n");
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, 1);
    }
    (void)memset(vertex, 0, sizeof(GtSpeciallabelVertexT));
    GtSpeciallabelT1VVertexT *t1V = (GtSpeciallabelT1VVertexT *)malloc(sizeof(GtSpeciallabelT1VVertexT) * vertexCfg.t1VCount);
    if (t1V == NULL) {
        AW_FUN_Log(LOG_ERROR, "t1V is NULL\n");
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, 1);
    }
    (void)memset(t1V, 0, sizeof(GtSpeciallabelT1VVertexT) * vertexCfg.t1VCount);
    vertex->t1V = t1V;
    GtSpeciallabelT2VVertexT *t2V =
    (GtSpeciallabelT2VVertexT *)malloc(sizeof(GtSpeciallabelT2VVertexT) * vertexCfg.t1VCount * vertexCfg.t2VCount);
    if (t2V == NULL) {
        AW_FUN_Log(LOG_ERROR, "t2V is NULL\n");
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, 1);
    }
    (void)memset(t2V, 0, sizeof(GtSpeciallabelT2VVertexT) * vertexCfg.t1VCount * vertexCfg.t2VCount);
    for (int32_t i = 0; i < vertexCfg.t1VCount; i++) {
        t1V[i].t2V = &t2V[vertexCfg.t2VCount * ((0) + i)];
    }
    TestLabelInfoT labelInfo = {g_labelName2, 0, g_testNameSpace};
    for (count = startValue; count < startValue + endValue; count++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName2, 0, GMC_OPERATION_INSERT);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GtSpeciallabelStructSetPk(vertex, count);
        GtSpeciallabelStructSetHashcluster(vertex, count, 0);
        GtSpeciallabelStructSetLocalhash(vertex, count, 0);
        GtSpeciallabelStructSetLocal(vertex, count);
        GtSpeciallabelStructSetLpm4(vertex, count);
        GtSpeciallabelStructSetOldProperty(vertex, count, vertexCfg.t1VCount, vertexCfg.t2VCount, bytesValue, stringValue,
                                           isDefaultValue);
        ret = testStructSetVertexWithBuf(stmt, vertex, &labelInfo);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret != 0) {
            AW_FUN_Log(LOG_INFO, "GmcExecute is ret = %d count = %d\n", ret, count);
            break;
        }
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (count % 50000 == 0) {
            AW_FUN_Log(LOG_INFO, "----ret = %d count = %d----\n", ret, count);
        }
        ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    GtSpeciallabelStructFree(vertex);
    free(t2V);
    free(t1V);
    free(vertex);

    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end.");
}
