%table A(vr_id:int4, vrf_index:int4, dest_ip_addr:byte16, mask_len:int1, e:int8) {
    index(0(vr_id)),
    timeout(field(e)),
    index(2(vr_id, vrf_index, dest_ip_addr, mask_len), type(lpm6_tree_bitmap))
}

%table B(vr_id:int4, vrf_index:int4, dest_ip_addr:byte16, mask_len:int1, e:int8) {
    index(0(vr_id))
}

B(vr_id, vrf_index, dest_ip_addr, mask_len, e) :- A(vr_id, vrf_index, dest_ip_addr, mask_len, e).
