dtl_ext_func_init.
dtl_tbm_tbl_out1, op = 0, a = 1, b = 1, c = 1, dtlReservedCount = 1.
dtl_tbm_tbl_out1, op = 0, a = 1, b = 2, c = 3, dtlReservedCount = 1.
dtl_tbm_tbl_out1, op = 0, a = 2, b = 2, c = 4, dtlReservedCount = 1.
dtl_tbm_tbl_out1, op = 0, a = 3, b = 3, c = 3, dtlReservedCount = 1.
dtl_tbm_tbl_out1, op = 0, a = 4, b = 4, c = 8, dtlReservedCount = 1.
dtl_tbm_tbl_out1, op = 2, a = 1, b = 1, c = 10, dtlReservedCount = 1.
dtl_tbm_tbl_out1, op = 2, a = 1, b = 2, c = 10, dtlReservedCount = 1.
dtl_tbm_tbl_out1, op = 2, a = 2, b = 2, c = 10, dtlReservedCount = 1.
dtl_tbm_tbl_out1, op = 2, a = 3, b = 3, c = 10, dtlReservedCount = 1.
dtl_tbm_tbl_out1, op = 2, a = 4, b = 4, c = 10, dtlReservedCount = 1.
dtl_tbm_tbl_out1, op = 2, a = 1, b = 1, c = 1, dtlReservedCount = 1.
dtl_tbm_tbl_out1, op = 2, a = 1, b = 2, c = 3, dtlReservedCount = 1.
dtl_tbm_tbl_out1, op = 2, a = 2, b = 2, c = 4, dtlReservedCount = 1.
dtl_tbm_tbl_out1, op = 2, a = 3, b = 3, c = 3, dtlReservedCount = 1.
dtl_tbm_tbl_out1, op = 2, a = 4, b = 4, c = 8, dtlReservedCount = 1.
dtl_ext_func_uninit.
