/*  版权所有 (c) 华为技术有限公司 2022-2023 */
#include "gm_udf.h"
#include "stdio.h"
#include "unistd.h"

#pragma pack(1)

typedef struct B {
    int32_t dtlReservedCount;
    int64_t a;
    int64_t b;
    int64_t c;
} B;

typedef struct Inp1 {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int64_t a;
    int64_t b;
    int64_t c;
} Inp1;

#pragma pack(0)

// 可更新表
int32_t dtl_compare_tuple_tb1(const void *tuple1, const void *tuple2, GmUdfCtxT *ctx)
{
    Inp1 *inp1 = (Inp1 *)tuple1;
    Inp1 *inp2 = (Inp1 *)tuple2;
    if (inp1->c < inp2->c) {
        return -1;
    } else if (inp1->c > inp2->c) {
        return 1;
    } else {
        if (inp1->b < inp2->b) {
            return -1;
        } else {
            return 1;
        }
        return 0;
    }
}

int32_t dtl_ext_func_func1(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;

    return GMERR_OK;
}
