/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description:
 * Author: yang<PERSON><PERSON> ywx1060383
 * Create: 2024-03-04
 */

#include <string.h>
#include "gm_udf.h"
#include "stdio.h"
#include "unistd.h"

#pragma pack(1)
typedef struct A {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int32_t a;
    int32_t b;
    int32_t c;
} A;
#pragma pack(0)

const char *g_logName = "/root/097_Func.txt";

int32_t dtl_ext_func_func1(void *tuple, GmUdfCtxT *ctx)
{
    int ret = 0;
    FILE *fp = fopen(g_logName, "a+");
    if (fp == NULL) {
        return 0;
    }

    GmUdfReaderT *readerOrginp1 = NULL;
    ret = GmUdfCreateCurrentReader(ctx, 0, &readerOrginp1);
    if (ret != GMERR_OK) {
        return ret;
    }

    A *inpA;
    while (ret = GmUdfGetNext(readerOrginp1, (void **)&inpA), ret == GMERR_OK) {
        (void)fprintf(fp, "a = %d, b = %d, c = %d, dtlReservedCount = %d, upgradeVersion = %d\n", ((A *)inpA)->a,
            ((A *)inpA)->b, ((A *)inpA)->c, ((A *)inpA)->dtlReservedCount, ((A *)inpA)->upgradeVersion);
    }

    GmUdfDestroyReader(ctx, readerOrginp1);
    (void)fclose(fp);

    return GMERR_OK;
}
