%table A20(a:int4, b:int4)
%table B20(a:int4, b:int4, c:int4, d:int4)


%function A20func001(a: int4, b: int4->c: int4, d: int4) {}
%function A20func002(a: int4, b: int4->c: int4, d: int4) {}
%function A20func003(a: int4, b: int4->c: int4, d: int4) {}
%function A20func004(a: int4, b: int4->c: int4, d: int4) {}
%function A20func005(a: int4, b: int4->c: int4, d: int4) {}
%function A20func006(a: int4, b: int4->c: int4, d: int4) {}
%function A20func007(a: int4, b: int4->c: int4, d: int4) {}
%function A20func008(a: int4, b: int4->c: int4, d: int4) {}
%function A20func009(a: int4, b: int4->c: int4, d: int4) {}
%function A20func010(a: int4, b: int4->c: int4, d: int4) {}
%function A20func011(a: int4, b: int4->c: int4, d: int4) {}
%function A20func012(a: int4, b: int4->c: int4, d: int4) {}
%function A20func013(a: int4, b: int4->c: int4, d: int4) {}
%function A20func014(a: int4, b: int4->c: int4, d: int4) {}
%function A20func015(a: int4, b: int4->c: int4, d: int4) {}
%function A20func016(a: int4, b: int4->c: int4, d: int4) {}



B20(a, b, c, d) :- A20(a, b), A20func001(a, b, c, d).
B20(a, b, c, d) :- A20(a, b), A20func002(a, b, c, d).
B20(a, b, c, d) :- A20(a, b), A20func003(a, b, c, d).
B20(a, b, c, d) :- A20(a, b), A20func004(a, b, c, d).
B20(a, b, c, d) :- A20(a, b), A20func005(a, b, c, d).
B20(a, b, c, d) :- A20(a, b), A20func006(a, b, c, d).
B20(a, b, c, d) :- A20(a, b), A20func007(a, b, c, d).
B20(a, b, c, d) :- A20(a, b), A20func008(a, b, c, d).
B20(a, b, c, d) :- A20(a, b), A20func009(a, b, c, d).
B20(a, b, c, d) :- A20(a, b), A20func010(a, b, c, d).
B20(a, b, c, d) :- A20(a, b), A20func011(a, b, c, d).
B20(a, b, c, d) :- A20(a, b), A20func012(a, b, c, d).
B20(a, b, c, d) :- A20(a, b), A20func013(a, b, c, d).
B20(a, b, c, d) :- A20(a, b), A20func014(a, b, c, d).
B20(a, b, c, d) :- A20(a, b), A20func015(a, b, c, d).
B20(a, b, c, d) :- A20(a, b), A20func016(a, b, c, d).

