/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

#include "gm_udf.h"

#pragma pack(1)
typedef struct A {
    int32_t a;
    int32_t b;
} A;

typedef struct B {
    int32_t a;
    int32_t b;
    int32_t c;
    int32_t dtlReservedCount;
} B;

#pragma pack(0)

int32_t dtl_ext_func_func1(void *tuple, GmUdfCtxT *ctx)
{
    // 增加一列
    B *b = (B *)tuple;
    b->c = b->a + b->b;

    // 读B_org表,表中没有数据，下面校验没起作用
    GmUdfReaderT *readerOrg = NULL;
    int ret = GmUdfCreateCurrentReader(ctx, 0, &readerOrg);
    if (ret != GMERR_OK) {
        return ret;
    }

    B *b1;
    int i = 0;
    while (ret = GmUdfGetNext(readerOrg, (void **)&b1), ret == GMERR_OK) {
        i++;
    }
    GmUdfDestroyReader(ctx, readerOrg);

    // 读B_delta表
    GmUdfReaderT *readerDelta = NULL;
    ret = GmUdfCreateDeltaReader(ctx, 0, &readerDelta);
    if (ret != GMERR_OK) {
        return ret;
    }

    B *b2;
    i = 0;
    while (ret = GmUdfGetNext(readerDelta, (void **)&b2), ret == GMERR_OK) {
        i++;
    }
    GmUdfDestroyReader(ctx, readerDelta);
    return GMERR_OK;
}
