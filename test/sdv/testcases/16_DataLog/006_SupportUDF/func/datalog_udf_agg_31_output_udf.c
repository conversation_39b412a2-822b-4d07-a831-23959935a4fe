/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: datalog.h
 * Description: datalog compilation
 * Author: jiangshan/j00811785
 * Create: 2022-09-13
 */

#include "gm_udf.h"

#pragma pack(1)
// 输入字段--聚合字段
typedef struct A {
    int32_t dtlReservedCount;
    int32_t a;
} A;
// 输出字段---min和max和其他29个字段
typedef struct B {
    int32_t a;
    int32_t b;
    int32_t a1;
    int32_t a2;
    int32_t a3;
    int32_t a4;
    int32_t a5;
    int32_t a6;
    int32_t a7;
    int32_t a8;
    int32_t a9;
    int32_t a10;
    int32_t a11;
    int32_t a12;
    int32_t a13;
    int32_t a14;
    int32_t a15;
    int32_t a16;
    int32_t a17;
    int32_t a18;
    int32_t a19;
    int32_t a20;
    int32_t a21;
    int32_t a22;
    int32_t a23;
    int32_t a24;
    int32_t a25;
    int32_t a26;
    int32_t a27;
    int32_t a28;
} B;

#pragma pack(0)

int32_t dtl_agg_compare_agg(void *tuple1, void *tuple2, GmUdfCtxT *ctx)
{
    A *inp1 = (A *)tuple1;
    A *inp2 = (A *)tuple2;
    if (inp1->a < inp2->a) {
        return -1;
    } else if (inp1->a > inp2->a) {
        return 1;
    } else {
        return 0;
    }
}
int32_t dtl_agg_func_agg(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    GmUdfReaderT *inputBak = input;
    A *inpStruct;
    B *outStruct = GmUdfMemAlloc(ctx, sizeof(B));
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    int32_t ret = GmUdfGetNext(inputBak, (void **)&inpStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    int32_t min = inpStruct->a;
    int32_t max = inpStruct->a;
    while (ret = GmUdfGetNext(inputBak, (void **)&inpStruct), ret == GMERR_OK) {
        max = inpStruct->a;
    }
    outStruct->a = min;
    outStruct->b = max;
    outStruct->a1 = max;
    outStruct->a2 = max;
    outStruct->a3 = max;
    outStruct->a4 = max;
    outStruct->a5 = max;
    outStruct->a6 = max;
    outStruct->a7 = max;
    outStruct->a8 = max;
    outStruct->a9 = max;
    outStruct->a10 = max;
    outStruct->a11 = max;
    outStruct->a12 = max;
    outStruct->a13 = max;
    outStruct->a14 = max;
    outStruct->a15 = max;
    outStruct->a16 = max;
    outStruct->a17 = max;
    outStruct->a18 = max;
    outStruct->a19 = max;
    outStruct->a20 = max;
    outStruct->a21 = max;
    outStruct->a22 = max;
    outStruct->a23 = max;
    outStruct->a24 = max;
    outStruct->a25 = max;
    outStruct->a26 = max;
    outStruct->a27 = max;
    outStruct->a28 = max;
    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;

    return GMERR_OK;
}
