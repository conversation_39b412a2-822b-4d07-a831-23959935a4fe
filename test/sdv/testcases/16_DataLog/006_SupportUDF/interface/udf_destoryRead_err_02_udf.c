/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

#include "gm_udf.h"

#pragma pack(1)

typedef struct Para {
    int32_t dtlReservedCount;
    int32_t a;
    int32_t b;
    int32_t c;
} Para;

// 索引结构体和表结构一样,不考虑结构体不一致的情况
typedef struct Index {
    int32_t a;
    int32_t b;
    int32_t count;
} Index;

#pragma pack(0)

int32_t dtl_ext_func_func(void *tuple, GmUdfCtxT *ctx)
{
    // 增加一列
    Para *para = (Para *)tuple;
    para->c = para->a + para->b;

    // 索引读C_org表
    GmUdfReaderT *reader = NULL;
    Index key = {.b = 2};
    int ret = GmUdfCreateCurrentReaderByIndex(ctx, sizeof(Index), &key, 0, 1, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }
    Index *c;
    ret = GmUdfGetNext(reader, (void **)&c);
    if (ret != GMERR_OK) {
        return ret;
    }
    GmUdfDestroyReader(ctx, NULL);
    if (reader != NULL) {
        return GMERR_DATA_EXCEPTION;
    }
    return GMERR_OK;
}
