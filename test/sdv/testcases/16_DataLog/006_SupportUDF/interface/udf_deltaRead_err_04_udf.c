/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

#include "gm_udf.h"
#include "stdio.h"
#pragma pack(1)
typedef struct A {
    int32_t a;
    int32_t b;
} A;

typedef struct B {
    int32_t dtlReservedCount;
    int32_t a;
    int32_t b;
    int32_t c;
} B;

typedef struct C {
    int32_t a;
    int32_t b;
    int32_t dtlReservedCount;
} C;

#pragma pack(0)

int32_t dtl_ext_func_func(void *tuple, GmUdfCtxT *ctx)
{
    // 增加一列
    B *b = (B *)tuple;
    b->c = b->a + b->b;

    // 写C_delta表
    GmUdfWriterT *writer = NULL;
    int32_t ret = GmUdfGetDeltaWriter(ctx, 0, &writer);
    if (ret != GMERR_OK) {
        return ret;
    }
    C c = {};
    c.a = b->c;
    c.b = b->c * 2;
    c.dtlReservedCount = 1;
    ret = GmUdfAppend(writer, sizeof(C), &c);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 全表读C_delta表
    GmUdfReaderT *readerDelta = NULL;
    ret = GmUdfCreateDeltaReader(ctx, 10, &readerDelta);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}
