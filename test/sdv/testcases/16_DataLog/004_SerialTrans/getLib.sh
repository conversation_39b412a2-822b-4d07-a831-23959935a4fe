# 生成用于datalog加载的so文件：
# $1 输入 .d文件[x.d] udf.c文件[x.d:x.c]
# $2 输出 .so文件，为空默认与输入名相同
# ex. getLib.sh xxx/xxx.d           getLib.sh xxx/xxx.d clean
#     getLib.sh xxx/xxx.d xxx.so    getLib.sh xxx/xxx.d xxx.so clean
#     getLib.sh "xxx/xxx.d : xxx/xxxudf.c"
#     getLib.sh "xxx/xxx.d : xxx/xxxudf.c" xxx.so

ARCH=$1
if [ "x" = "x${ARCH}" ] || [ "xh" = "x${ARCH}" ] || [ "X${ARCH}" = "X-h" ] || [ $# -lt 1 ]; then
    echo ">> support arm64 "
    echo ">>useage:arg1 [input .d udf.c file]"
    echo ">>       arg2 [output .so name]"
    exit 1
fi

log_dir=`cat sysconfig.txt |grep logDir |awk -F '[:]' '{print $2}' |sed 's/ //g'`
mkdir -p $log_dir
lib_dir=`cat sysconfig.txt |grep libDir |awk -F '[:]' '{print $2}' |sed 's/ //g'`
mkdir -p $lib_dir
include_dir=`cat sysconfig.txt |grep udfDir |awk -F '[:]' '{print $2}' |sed 's/ //g'`

fileDName=`echo $1 |awk -F '[:]' '{print $1}' |sed 's/ //g'`
fileUdfName=`echo $1 |awk -F '[:]' '{print $2}' |sed 's/ //g'`

if [ ! -f ${fileDName} ]; then
    echo ">> file ${fileDName} is no exist."
    exit 1
fi

if [ x"${fileUdfName}" != x"" ] && [ ! -f ${fileUdfName} ]; then
    echo ">> file ${fileUdfName} is no exist."
    exit 1
fi

compileOption="-fPIC --shared"
if [ x"${fileUdfName}" != x"" ]; then
    compileOption="${compileOption} -Wl,-Bsymbolic -I ${include_dir} -g"
fi

fileName=`echo ${fileDName} |awk -F '[.]' '{print $1}'`
LibfileName=${lib_dir}/$2
if [ x"$2" = x"" ] || [ x"$2" = x"clean" ]; then
    LibfileName=${fileName}.so
fi

if [ x"$3" = x"clean" ] || [ x"$2" = x"clean" ]; then
    rm -rf ${fileName}.c
    rm -rf ${LibfileName}
    echo ">> delete c_file : ${fileName}.c ."
    echo ">> delete so_file : ${LibfileName} ."
    exit 1
fi

rm -rf ${fileName}.c
gmprecompiler -f ${fileName}.d ${fileName}.c > ${log_dir}/compilerRun.txt
runResult=`cat ${log_dir}/compilerRun.txt |grep -i "error" |wc -l`
if [ $runResult -ge 1 ] || [ ! -f ${fileName}.c ]; then
    echo ">> [error] gmprecompiler fail, check compile result, log file: ${log_dir}/compilerRun.txt ."
    exit 1
fi

rm -rf ${LibfileName}
gcc ${fileName}.c ${fileUdfName} ${compileOption} -o ${LibfileName}
if [ ! -f ${LibfileName} ]; then
    echo ">> [error] gcc fail, check udf file : ${fileUdfName} ."
    exit 1
fi

echo ">> complie success, file : ${LibfileName} ."
