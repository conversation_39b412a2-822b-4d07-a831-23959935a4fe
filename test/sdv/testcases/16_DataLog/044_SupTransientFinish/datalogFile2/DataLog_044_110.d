%version v0.0.0

%table inp1(a:int4, b:int4, c:int4) {index(0(a)), update}
%table inp2(a:int4, b:int4, c:int4) {index(0(a))}
%table mid1(a:int4, b:int4, c:int4) {index(0(a)), transient(finish)}
%table mid2(a:int4, b:int4, c:int4) {index(0(a)), transient(finish)}
%table out1(a:int4, b:int4, c:int4) {index(0(a))}
%table out2(a:int4, b:int4, c:int4) {index(0(a)), tbm}

%function init()
%function uninit()

%function func(a:int4, b:int4, c:int4) {
    access_delta(inp1)
}

mid1(a, b, c) :- inp1(a, b, c), inp2(a, b, c).

out1(a, b, c) :- mid1(a, b, c), func(a, b, c).

mid2(a, b, c) :- inp2(a, b, c).

out2(a, b, c) :- mid2(a, b, c).
