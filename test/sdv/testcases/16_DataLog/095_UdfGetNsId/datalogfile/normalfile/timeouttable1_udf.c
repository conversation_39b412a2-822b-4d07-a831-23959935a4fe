/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: tbm_udf
 * Author: qibingsen 00880292
 * Create: 2024-12-23
 */

#include "gm_udf.h"
#include "stdio.h"
#include "unistd.h"

#pragma pack(1)

typedef struct Func {
    int32_t dtlReservedCount;
    int64_t a;
    int64_t b;
    int64_t c;
    int32_t d;
} Func;
typedef struct INP3 {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int64_t a;
    int64_t b;
    int64_t c;
} INP3;

#pragma pack(0)

const char *g_logName = "/root/_datalog_/TbmRunLog.txt";

int32_t dtl_ext_func_funcinp3(void *tuple, GmUdfCtxT *ctx)
{
    Func *f = (Func *)tuple;
    f->d = f->a + f->b;
    FILE *fp = fopen(g_logName, "a");
    if (fp == NULL) {
        return 28;
    }
    uint32_t namespaceid = 1;
    int ret = GmUdfGetNamespaceId(ctx, &namespaceid);
    if (ret != GMERR_OK) {
        return ret;
    }
    (void)fprintf(fp, "dtl_ext_func_funcinp3. the namespaceid is %d\n", namespaceid);
    (void)fclose(fp);
    return GMERR_OK;
}

int32_t dtl_timeout_callback_dnsp1_inp3(const void *timeoutTuple, uint32_t *extraTupleLen, void **extraTuple,
                                        GmUdfCtxT *ctx)
{
    int ret;
    // 返回记录
    FILE *fp = fopen(g_logName, "a");
    if (fp == NULL) {
        return 28;
    }
    uint32_t namespaceid = 1;
    ret = GmUdfGetNamespaceId(ctx, &namespaceid);
    if (ret != GMERR_OK) {
        return ret;
    }
    (void)fprintf(fp, "dtl_timeout_callback_dnsp1_inp3. the namespaceid is %d\n", namespaceid);
    (void)fclose(fp);
    INP3 *input = (INP3 *)timeoutTuple;
    INP3 *output = GmUdfMemAlloc(ctx, sizeof(INP3));
    if (output == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    output->a = input->a;
    output->b = input->b;
    output->c = input->c;
    output->dtlReservedCount = -input->dtlReservedCount;
    *extraTuple = (void *)output;
    *extraTupleLen = sizeof(INP3);

    return GMERR_OK;
}
