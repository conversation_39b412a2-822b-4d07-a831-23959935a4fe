/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

#include <stdio.h>
#include <string.h>
#include "gm_udf.h"
#include "assert.h"

#pragma pack(1)
typedef struct Tuple {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int32_t a;
    int32_t b;
    int32_t c;
} TupleT;

typedef struct Inp1 {
    int32_t dtlReservedCount;
    int64_t a;
    int64_t b;
    int64_t c;
    int64_t d;
} Inp1;

#pragma pack(0)

int32_t dtl_ext_func_funcinp1(void *tuple, GmUdfCtxT *ctx)
{
    Inp1 *inp1 = (Inp1 *)tuple;
    inp1->d = inp1->a + inp1->b + inp1->c;
    if (inp1->d > 10) {
        return GMERR_NO_DATA;
    }
    return GMERR_OK;
}


const char *g_logName = "/root/_datalog_/bytestrtbm.txt";
static char *g_filename = "/root/_datalog_/errlog.txt";

int32_t dtl_ext_func_init(GmUdfCtxT *ctx)
{
    return GMERR_OK;
}

int32_t dtl_ext_func_uninit(GmUdfCtxT *ctx)
{
    return GMERR_OK;
}

int32_t dtl_tbm_tbl_tbmtable(uint32_t op, void *tuple)
{
    FILE *fp = fopen(g_logName, "a+");
    if (fp == NULL) {
        return 50;
    }
    (void)fprintf(
        fp, "dtl_tbm_tbl_tbmtable, op = %d, a = %d, b = %d, c = %d, dtlReservedCount = %d. \n",
        op, ((TupleT *)tuple)->a, ((TupleT *)tuple)->b,  ((TupleT *)tuple)->c,  ((TupleT *)tuple)->dtlReservedCount);
    (void)fclose(fp);
    return GMERR_OK;
}
