%table inp1(a42:int8, b42:uint8, c12:byte1, d9:str, d10:byte){index(0(a42, b42)), update, status_merge_sub(false)}

%function funcinp1(a42:int8, b42:uint8, c12:byte1 -> ao42:int8, bo42:uint8, co12:byte1)

%table notifytable(a42:int8, b42:uint8, c12:byte1, d9:str, d10:byte){index(0(a42, b42)), notify, update}

notifytable(ao42, bo42, co12, d9, d10) :- inp1(a42, b42, c12, d9, d10), funcinp1(a42, b42, c12, ao42, bo42, co12).
