#!/bin/bash
# for multi-label

CUR_DIR=`pwd`


if [ $# -lt 1 ];then
    echo "usage:$0 sh [create label nums]"
    exit  1
fi

## 数据清除及准备  $CUR_DIR/multi_vertexlabel文件夹
cd $CUR_DIR
rm -rf multi_vertexlabel > /dev/null 2>&1
mkdir multi_vertexlabel > /dev/null 2>&1
cp $TEST_HOME/testcases/01_connect/009_AsyncConnOptimize/schemaFile/labeltest.gmjson ./multi_vertexlabel/labeltest.gmjson
sleep 1

# 构造多个 label
cd $CUR_DIR/multi_vertexlabel
#echo $1

for i in $(seq 1 $1) 
do	
	cp labeltest.gmjson labeltest$i.gmjson
	sed -i "s/\"label\"/\"label"$i"\"/g" ./labeltest$i.gmjson
done
