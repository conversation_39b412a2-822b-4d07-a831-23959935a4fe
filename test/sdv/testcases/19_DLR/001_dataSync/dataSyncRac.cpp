/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: dataSyncInteract.cpp
 * Description: 数据同步获取-交互场景测试
 * Author: qinqianbao 995465
 * Create: 2022-09-23
 */
#include "./dataSync.h"

using namespace std;

class dataSyncInteract : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);

        // 创建epoll监听线程
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret;
        ret = close_epoll_thread();
        EXPECT_EQ(0, ret);
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void dataSyncInteract::SetUp()
{
    int ret;
    // 创建连接
    g_conn = NULL;
    g_stmt = NULL;
    g_conn_async = NULL;
    g_stmt_async = NULL;
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建订阅连接
    g_conn_sub = NULL;
    g_stmt_sub = NULL;
    int chanRingLen = 256;
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}
void dataSyncInteract::TearDown()
{
    AW_CHECK_LOG_END();
    int ret;
    // 释放订阅连接
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    // 断开同步连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 001. gmimport导入同步表, 写入数据, 全表扫
TEST_F(dataSyncInteract, DLR_001_dataSyncInteract_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // create vertex
    int ret = 0;
    int32_t startNum = 0;
    int32_t endNum = 10;
    char usrName[] = "usr";
    const char *testNamespace = "test_namespace";
    ret = GmcCreateNamespace(g_stmt, testNamespace, usrName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, testNamespace);
    EXPECT_EQ(GMERR_OK, ret);

    char *labelSchema = NULL;
    char labelName[] = "simpleLabel";

    // gmimport导入同步表
    char schema_path[128] = "./schemaFile/gmimportLabel";
    char cmd[1024] = {0};
    (void)snprintf(cmd, 1024, "%s/gmimport -c cache -f %s -ns %s  -s %s",
             g_toolPath, schema_path, testNamespace,  g_connServer);
    sleep(1);
    system(cmd);

    // insert data
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    bool isDefaultValue = true;
    for (int32_t i = startNum; i < endNum; i++) {
        TestSimpleT1SetPk(g_stmt, i);
        TestSimpleT1SetProperty(g_stmt, i, isDefaultValue);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    getScanFunTest(g_stmt, labelName);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, testNamespace);
    EXPECT_EQ(GMERR_OK, ret);
    
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002. 同步表存在数据, insert新数据, 主键查询
TEST_F(dataSyncInteract, DLR_001_dataSyncInteract_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // create vertex
    int ret = 0;
    int32_t startNum = 0;
    int32_t endNum = 1;
    char usrName[] = "usr";
    const char *testNamespace = "test_namespace";
    ret = GmcCreateNamespace(g_stmt, testNamespace, usrName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, testNamespace);
    EXPECT_EQ(GMERR_OK, ret);

    char configJson[128] = "{\"max_record_count\" : 100000, \"data_sync_label\":true}";
    char *labelSchema = NULL;
    char labelName[] = "simpleLabel";
    readJanssonFile("schemaFile/simpleLabel1.gmjson", &labelSchema);
    ASSERT_NE((void *)NULL, labelSchema);
    // 创建简单表
    ret = GmcCreateVertexLabel(g_stmt, labelSchema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelSchema);

    // insert data
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    bool isDefaultValue = true;
    for (int32_t i = startNum; i < endNum; i++) {
        TestSimpleT1SetPk(g_stmt, i);
        TestSimpleT1SetProperty(g_stmt, i, isDefaultValue);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 主键查询
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = startNum; i < endNum; i++) {
        TestSimpleT1PkIndexSet(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint32_t bufSize = 1024;
        char buf[1024] = {0};
        GmcDlrDataBufT dataBufs;
        ret = GmcDlrDataBufInit(&dataBufs, buf, bufSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool getEof = false;
        while (!getEof) {
            ret = GmcGetScanDlrDataBuf(g_stmt, &dataBufs, &getEof);
            EXPECT_EQ(GMERR_OK, ret);
            if (ret != 0) {
                ret = testGmcGetLastError(NULL);
                EXPECT_EQ(GMERR_OK, ret);
                break;
            }
        }
    }

    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, testNamespace);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003. 同步表存在数据, insert新数据, 查询表的record信息
TEST_F(dataSyncInteract, DLR_001_dataSyncInteract_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // create vertex
    int ret = 0;
    int32_t startNum = 0;
    int32_t endNum = 10000;
    char usrName[] = "usr";
    const char *testNamespace = "test_namespace";
    ret = GmcCreateNamespace(g_stmt, testNamespace, usrName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, testNamespace);
    EXPECT_EQ(GMERR_OK, ret);

    char configJson[128] = "{\"max_record_count\" : 100000, \"data_sync_label\":true}";
    char *labelSchema = NULL;
    char labelName[] = "simpleLabel";
    readJanssonFile("schemaFile/simpleLabel1.gmjson", &labelSchema);
    ASSERT_NE((void *)NULL, labelSchema);
    // 创建简单表
    ret = GmcCreateVertexLabel(g_stmt, labelSchema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelSchema);

    // insert data
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    bool isDefaultValue = true;
    for (int32_t i = startNum; i < endNum; i++) {
        TestSimpleT1SetPk(g_stmt, i);
        TestSimpleT1SetProperty(g_stmt, i, isDefaultValue);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    char expOut[128] = {0};
    sprintf(expOut, "index = %d", endNum - 1);
    char command[MAX_CMD_SIZE] = {0};
    snprintf(command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, labelName, g_connServer,
        testNamespace);
    // record视图
    ret = executeCommand(command, expOut);
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t bufSize = 1024;
    char buf[1024] = {0};
    memset(buf, sizeof(char) * bufSize, 0);
    GmcDlrDataBufT dataBufs;
    ret = GmcDlrDataBufInit(&dataBufs, buf, bufSize);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool getEof = false;
    while (!getEof) {
        ret = GmcGetScanDlrDataBuf(g_stmt, &dataBufs, &getEof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != 0) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            break;
        }
    }

    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, testNamespace);
    EXPECT_EQ(GMERR_OK, ret);
    
    AW_FUN_Log(LOG_STEP, "test end.");
}
