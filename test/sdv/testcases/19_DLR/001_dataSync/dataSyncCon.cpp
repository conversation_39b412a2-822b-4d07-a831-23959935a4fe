/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: dataSyncCon.cpp
 * Description: 数据同步获取-规格约束
 * Author: qinqianbao 995465
 * Create: 2022-09-20
 */
#include "./dataSync.h"

using namespace std;

static const char *resPoolTestName = "resource_pool_test";
static const char *resPoolTest =
    R"({
        "name" : "resource_pool_test",
        "pool_id" : 65535,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";

class dataSyncCon : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);

        // 创建epoll监听线程
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret;
        ret = close_epoll_thread();
        EXPECT_EQ(0, ret);
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void dataSyncCon::SetUp()
{
    int ret;
    char errorMsg1[128] = {};
    char errorMsg2[128] = {};
    char errorMsg3[128] = {};
    char errorMsg4[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    (void)snprintf(errorMsg2, sizeof(errorMsg1), "GMERR-%d", GMERR_TRANSACTION_ROLLBACK);
    (void)snprintf(errorMsg3, sizeof(errorMsg1), "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    (void)snprintf(errorMsg4, sizeof(errorMsg1), "GMERR-%d", GMERR_RESOURCE_POOL_ERROR);
    AW_ADD_ERR_WHITE_LIST(4, errorMsg1, errorMsg2, errorMsg3, errorMsg4);
    // 创建连接
    g_conn = NULL;
    g_stmt = NULL;
    g_conn_async = NULL;
    g_stmt_async = NULL;
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}
void dataSyncCon::TearDown()
{
    AW_CHECK_LOG_END();
    int ret;
    // 断开同步连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
}

// 001. 创建kv表时config携带isDataSync
TEST_F(dataSyncCon, DLR_001_dataSyncConstraint_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // create kvTable
    char kvTable[] = "kv_DLR_001";
    char configJson[128] = "{\"max_record_count\" : 10000, \"data_sync_label\":true}";
    int ret = GmcKvCreateTable(g_stmt, kvTable, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002. 创建同步表, 开启事务写入数据、同步数据数据获取, 提交事务
TEST_F(dataSyncCon, DLR_001_dataSyncConstraint_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // create vertex
    int ret = 0;
    char configJson[128] = "{\"max_record_count\" : 10000, \"isFastReadUncommitted\":0, \"data_sync_label\":true}";
    uint32_t num = 0;
    char labelName[20] = "";
    char label_schema[1024] = "";
    (void)snprintf(label_schema, 1024,
        "[{\"type\":\"record\", \"name\":\"T%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
        "{\"name\":\"F1\", \"type\":\"int32\"}],"
        "\"keys\":[{\"name\":\"vertex_pk\", \"fields\":[\"F0\"], "
        "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
        num);
    (void)snprintf(labelName, 20, "T%d", num);
    ret = GmcCreateVertexLabel(g_stmt, label_schema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // 开启事务
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    ret = GmcTransStart(g_conn, &config);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 1; i++) {
        int32_t F0Value = i;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F0Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret); // 可观察服务端日志
        testGmcGetLastError(NULL);
    }
    ret = GmcTransCommit(g_conn);
    EXPECT_EQ(GMERR_TRANSACTION_ROLLBACK, ret);
    ret = GmcTransRollBack(g_conn);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003. 创建yang表时config携带isDataSync
TEST_F(dataSyncCon, DLR_001_dataSyncConstraint_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // create namespace
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = "yang";
    nspCfg.userName = "abc";
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观

    GmcDropNamespace(g_stmt, "yang");
    int ret = GmcCreateNamespaceWithCfg(g_stmt, &nspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // create vertexlabel
    ret = GmcUseNamespace(g_stmt, "yang");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // create yangLabel
    const char *configJson = R"(
{
    "max_record_count":10000,
    "isFastReadUncommitted":0,
    "auto_increment":1,
    "yang_model":1,
    "data_sync_label":true
})";

    uint32_t num = 0;
    char labelName[20] = "";
    char label_schema[1024] = "";
    (void)snprintf(label_schema, 1024,
        "[{\"type\":\"container\", \"name\":\"T%d\", \"fields\":["
        "{\"name\":\"ID\", \"type\":\"uint32\", \"nullable\":false},"
        "{\"name\":\"F0\", \"type\":\"uint32\"}],"
        "\"keys\":[{\"name\":\"vertex_pk\", \"fields\":[\"ID\"], "
        "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
        num);
    (void)snprintf(labelName, 20, "T%d", num);

    // 当前除了对kv表不能创同步表，其他表没有限制(但都会在显示开启事务时dml操作失败)
    ret = GmcCreateVertexLabel(g_stmt, label_schema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    // drop namespace
    ret = GmcDropNamespace(g_stmt, "yang");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004. 创建yang表时config携带isDataSync: 且创建边
TEST_F(dataSyncCon, DLR_001_dataSyncConstraint_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // create yangLabel
    int ret = 0;

    char *labelSchema = NULL;
    AsyncUserDataT data = {0};

    readJanssonFile("schemaFile/Con_Con.gmjson", &labelSchema);
    ASSERT_NE((void *)NULL, labelSchema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, labelSchema, g_configJsonYang, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, data.status);
    free(labelSchema);

    AW_FUN_Log(LOG_STEP, "test end.");
}
 

// 007. 创建vertex表时config携带isDataSync: schema中包含资源字段
TEST_F(dataSyncCon, DLR_001_dataSyncConstraint_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // create yangLabel
    int ret = 0;
    char Label_config[] = "{\"max_record_count\":100000, \"data_sync_label\":true}";
    char *resSchema = NULL;
    GmcDestroyResPool(g_stmt, resPoolTestName);
    ret = GmcCreateResPool(g_stmt, resPoolTest);
    EXPECT_EQ(GMERR_OK, ret);
    readJanssonFile("schemaFile/resource_complex_op62.gmjson", &resSchema);
    ASSERT_NE((void *)NULL, resSchema);
    ret = GmcCreateVertexLabel(g_stmt, resSchema, Label_config);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    free(resSchema);
    ret = GmcDestroyResPool(g_stmt, resPoolTestName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008. 创建特殊复杂表时config携带isDataSync
TEST_F(dataSyncCon, DLR_001_dataSyncConstraint_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // create special Label
    int ret = 0;
    char Label_config[] = "{\"max_record_count\":100000, \"data_sync_label\":true}";
    char *speSchema = NULL;

    readJanssonFile("schemaFile/SpecialTableSchema.gmjson", &speSchema);
    ASSERT_NE((void *)NULL, speSchema);

    ret = GmcCreateVertexLabel(g_stmt, speSchema, Label_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(speSchema);

    ret = GmcDropVertexLabel(g_stmt, "specialLabel");
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
