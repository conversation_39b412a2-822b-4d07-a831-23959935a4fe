/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: dataReplayAbormal.cpp
 * Description: 同步数据重演-异常功能测试
 * Author: qinqianbao 995465
 * Create: 2022-09-26
 */
#include "./dataReplay.h"

using namespace std;

class dataReplayAbormal : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);

        // 创建epoll监听线程
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret;
        ret = close_epoll_thread();
        EXPECT_EQ(0, ret);
        testEnvClean();
    }

public:
    DlrTestGetSubBufsT subData; // 获取增量数据的结构体
    virtual void SetUp();
    virtual void TearDown();
};

void dataReplayAbormal::SetUp()
{
    int ret;
    char errorMsg1[128] = {};
    char errorMsg2[128] = {};
    char errorMsg3[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    (void)snprintf(errorMsg2, sizeof(errorMsg1), "GMERR-%d", GMERR_FIELD_OVERFLOW);
    (void)snprintf(errorMsg3, sizeof(errorMsg1), "GMERR-%d", GMERR_RECORD_COUNT_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(3, errorMsg1, errorMsg2, errorMsg3);
    // 创建连接
    g_conn = NULL;
    g_stmt = NULL;
    g_symbolScanDlr = 0;
    g_symbolSubDlr = 0;

    // subData malloc 申请内存存放增量数据
    subData.dlrGetSubBuf = (GmcDlrDataBufT *)malloc(sizeof(GmcDlrDataBufT) * 1000);
    if (subData.dlrGetSubBuf == NULL) {
        return;
    }
    memset(subData.dlrGetSubBuf, sizeof(GmcDlrDataBufT) * 1000, 0);

    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建订阅连接
    g_conn_sub = NULL;
    g_stmt_sub = NULL;
    int chanRingLen = 256;
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN();
}
void dataReplayAbormal::TearDown()
{
    AW_CHECK_LOG_END();
    int ret;
    // 释放订阅连接
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    // 断开同步连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 释放存储增量数据的内存空间
    free(subData.dlrGetSubBuf);
}

// 001. 使用普通vertex表作为全量数据的dts表
TEST_F(dataReplayAbormal, DLR_002_dataReplayAbormal_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // create vertex
    int ret = 0;
    char usrName[] = "usr";
    const char *testNamespaceSrc = "test_namespace_src";
    const char *testNamespaceDst = "test_namespace_dst";
    int32_t startNum = 0;
    int32_t endNum = 1;
    uint64_t srcCount = 0;
    uint64_t dstCount = 0;
    char configJson[128] = "{\"max_record_count\" : 10000, \"data_sync_label\":true}";
    char configJsonTest[128] = "{\"max_record_count\" : 10000}";
    char *labelSchema = NULL;
    char labelName[] = "simpleLabel";
    readJanssonFile("schemaFile/simpleLabel1.gmjson", &labelSchema); // 简单表
    ASSERT_NE((void *)NULL, labelSchema);

    // 保存全表扫的数据
    DlrTestGetScanBufsT scanBuf;
    scanBuf.dlrGetScanBuf = (GmcDlrDataBufT *)malloc(sizeof(GmcDlrDataBufT) * 100);
    if (scanBuf.dlrGetScanBuf == NULL) {
        return;
    }
    memset(scanBuf.dlrGetScanBuf, sizeof(GmcDlrDataBufT) * 100, 0);

    ret = GmcCreateNamespace(g_stmt, testNamespaceSrc, usrName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateNamespace(g_stmt, testNamespaceDst, usrName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, testNamespaceSrc);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(g_stmt, labelSchema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert data
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    bool isDefaultValue = true;
    for (int32_t i = startNum; i < endNum; i++) {
        TestSimpleT1SetPk(g_stmt, i);
        TestSimpleT1SetProperty(g_stmt, i, isDefaultValue);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 全表扫
    getScanFunTest(g_stmt, labelName, &scanBuf); // 发送全量数据

    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    // 获取DLR-SRC记录数
    ret = GmcGetVertexRecordCount(g_stmt, &srcCount);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "[INFO]label_srcCount: %d", srcCount);

    // replay dts
    ret = GmcUseNamespace(g_stmt, testNamespaceDst);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, labelSchema, configJsonTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // replay 全量数据
    dataReplayAbormal001(g_stmt, labelName, scanBuf);

    // 获取DLR-DTS记录数
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexRecordCount(g_stmt, &dstCount);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "[INFO]label_dstCount: %d", dstCount);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, "test_namespace_dst");
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUseNamespace(g_stmt, testNamespaceSrc);
    EXPECT_EQ(GMERR_OK, ret);
    free(scanBuf.dlrGetScanBuf);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, "test_namespace_src");
    EXPECT_EQ(GMERR_OK, ret);
    
    ret = GmcUseNamespace(g_stmt, "public");
    EXPECT_EQ(GMERR_OK, ret);
    free(labelSchema);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002. 使用普通vertex表作为增量数据的dts表
TEST_F(dataReplayAbormal, DLR_002_dataReplayAbormal_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // create vertex
    int ret = 0;
    char usrName[] = "usr";
    const char *testNamespaceSrc = "test_namespace_src";
    const char *testNamespaceDst = "test_namespace_dst";
    int32_t startNum = 0;
    int32_t endNum = 1;
    uint64_t srcCount = 0;
    uint64_t dstCount = 0;
    char configJson[128] = "{\"max_record_count\" : 10000, \"data_sync_label\":true}";
    char configJsonTest[128] = "{\"max_record_count\" : 10000}";
    char *labelSchema = NULL;
    char labelName[] = "simpleLabel";
    readJanssonFile("schemaFile/simpleLabel1.gmjson", &labelSchema); // 简单表
    ASSERT_NE((void *)NULL, labelSchema);
    char *subInfo = NULL;
    readJanssonFile("schemaFile/schemaSubinfoNormal.gmjson", &subInfo); // 订阅关系
    ASSERT_NE((void *)NULL, subInfo);

    // 保存全表扫的数据
    DlrTestGetScanBufsT scanBuf;
    scanBuf.dlrGetScanBuf = (GmcDlrDataBufT *)malloc(sizeof(GmcDlrDataBufT) * 100);
    if (scanBuf.dlrGetScanBuf == NULL) {
        return;
    }
    memset(scanBuf.dlrGetScanBuf, sizeof(GmcDlrDataBufT) * 100, 0);

    ret = GmcCreateNamespace(g_stmt, testNamespaceSrc, usrName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateNamespace(g_stmt, testNamespaceDst, usrName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, testNamespaceSrc);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(g_stmt, labelSchema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = subInfo;
    ret = GmcSubscribe(g_stmt, &tmp_g_sub_info, g_conn_sub, sn_callback, &subData);
    EXPECT_EQ(GMERR_OK, ret);

    // insert new data
    bool isDefaultValue = true;
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int32_t i = startNum + 1; i < endNum + 1; i++) {
        TestSimpleT1SetPk(g_stmt, i);
        TestSimpleT1SetProperty(g_stmt, i, isDefaultValue);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    // 获取DLR-SRC记录数
    ret = GmcGetVertexRecordCount(g_stmt, &srcCount);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "[INFO]label_srcCount: %d", srcCount);

    sleep(1); // 等待增量数据发送完
    ret = GmcUnSubscribe(g_stmt, g_subName);
    EXPECT_EQ(GMERR_OK, ret);

    // replay dts
    ret = GmcUseNamespace(g_stmt, testNamespaceDst);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, labelSchema, configJsonTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // replay 增量数据
    dataReplayAbnormal002(g_stmt, labelName, subData);

    // 获取DLR-DTS记录数
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexRecordCount(g_stmt, &dstCount);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "[INFO]label_dstCount: %d", dstCount);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, "test_namespace_dst");
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUseNamespace(g_stmt, testNamespaceSrc);
    EXPECT_EQ(GMERR_OK, ret);
    free(scanBuf.dlrGetScanBuf);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, "test_namespace_src");
    EXPECT_EQ(GMERR_OK, ret);
    
    ret = GmcUseNamespace(g_stmt, "public");
    EXPECT_EQ(GMERR_OK, ret);
    free(labelSchema);
    free(subInfo);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003. 源端和目的端的labelCount不一致, 事务回滚
TEST_F(dataReplayAbormal, DLR_002_dataReplayAbormal_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // create vertex
    int ret = 0;
    char usrName[] = "usr";
    const char *testNamespaceSrc = "test_namespace_src";
    const char *testNamespaceDst = "test_namespace_dst";
    int32_t startNum = 0;
    int32_t endNum = 100;
    uint64_t srcCount = 0;
    uint64_t dstCount = 0;
    char configJson[128] = "{\"max_record_count\" : 100, \"data_sync_label\":true}";
    char configJsonTest[128] = "{\"max_record_count\" : 10, \"data_sync_label\":true}";
    char *labelSchema = NULL;
    char labelName[] = "simpleLabel";
    readJanssonFile("schemaFile/simpleLabel1.gmjson", &labelSchema); // 简单表
    ASSERT_NE((void *)NULL, labelSchema);

    // 保存全表扫的数据
    DlrTestGetScanBufsT scanBuf;
    scanBuf.dlrGetScanBuf = (GmcDlrDataBufT *)malloc(sizeof(GmcDlrDataBufT) * 100);
    if (scanBuf.dlrGetScanBuf == NULL) {
        return;
    }
    memset(scanBuf.dlrGetScanBuf, sizeof(GmcDlrDataBufT) * 100, 0);

    ret = GmcCreateNamespace(g_stmt, testNamespaceSrc, usrName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateNamespace(g_stmt, testNamespaceDst, usrName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, testNamespaceSrc);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(g_stmt, labelSchema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert data
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    bool isDefaultValue = true;
    for (int32_t i = startNum; i < endNum; i++) {
        TestSimpleT1SetPk(g_stmt, i);
        TestSimpleT1SetProperty(g_stmt, i, isDefaultValue);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 全表扫
    getScanFunTest(g_stmt, labelName, &scanBuf); // 发送全量数据

    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    // 获取DLR-SRC记录数
    ret = GmcGetVertexRecordCount(g_stmt, &srcCount);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "[INFO]label_srcCount: %d", srcCount);

    // replay dts
    ret = GmcUseNamespace(g_stmt, testNamespaceDst);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, labelSchema, configJsonTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // replay 全量数据
    dataReplayAbormal003(g_stmt, labelName, scanBuf);

    // 获取DLR-DTS记录数
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexRecordCount(g_stmt, &dstCount);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "[INFO]label_dstCount: %d", dstCount);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, "test_namespace_dst");
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUseNamespace(g_stmt, testNamespaceSrc);
    EXPECT_EQ(GMERR_OK, ret);
    free(scanBuf.dlrGetScanBuf);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, "test_namespace_src");
    EXPECT_EQ(GMERR_OK, ret);
    
    ret = GmcUseNamespace(g_stmt, "public");
    EXPECT_EQ(GMERR_OK, ret);
    free(labelSchema);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004. 订阅事件类型配置异常
TEST_F(dataReplayAbormal, DLR_002_dataReplayAbormal_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // create vertex
    int ret = 0;
    int32_t startNum = 0;
    int32_t endNum = 1;
    char configJson[128] = "{\"max_record_count\" : 10000, \"data_sync_label\":true}";
    char *labelSchema = NULL;
    char labelName[] = "simpleLabel";
    readJanssonFile("schemaFile/simpleLabel1.gmjson", &labelSchema); // 简单表
    ASSERT_NE((void *)NULL, labelSchema);
    char *subInfo = NULL;
    readJanssonFile("schemaFile/schemaSubinfoAbnormal.gmjson", &subInfo); // 订阅关系
    ASSERT_NE((void *)NULL, subInfo);


    ret = GmcCreateVertexLabel(g_stmt, labelSchema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert data
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    bool isDefaultValue = true;
    for (int32_t i = startNum; i < endNum; i++) {
        TestSimpleT1SetPk(g_stmt, i);
        TestSimpleT1SetProperty(g_stmt, i, isDefaultValue);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = subInfo;
    ret = GmcSubscribe(g_stmt, &tmp_g_sub_info, g_conn_sub, TestSnCallBack, &subData);
    EXPECT_EQ(GMERR_OK, ret);

    int updateValue = 1025;
    // update data
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int32_t i = startNum; i < endNum; i++) { // msgType设定为new object
        TestSimpleT1HashclusterIndexSet(g_stmt, i);
        TestSimpleT1UpdateSetProperty(g_stmt, i + updateValue, isDefaultValue);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcUnSubscribe(g_stmt, g_subName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    free(labelSchema);
    free(subInfo);

    AW_FUN_Log(LOG_STEP, "test end.");
}
