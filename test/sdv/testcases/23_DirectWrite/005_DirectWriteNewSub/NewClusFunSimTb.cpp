/*****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2022. All rights reserved.
 Description  : 直连写适配新订阅推送 -- 基本功能（定长表）(聚蔟表)
 Notes        : 001. 下发新订阅，执行insert、update、replace、delete操作
                002. 下发新订阅（条件订阅and），执行insert、update、replace、delete操作
                003. 下发新订阅（条件订阅or），执行insert、update、replace、delete操作
                004. 下发新订阅，开启对账
                005. 下发新订阅，结束对账后执行replace操作
                006. 执行insert操作，下发全量+增量订阅，执行update、replace、delete操作
                007. 表升级，分别对低、高版本下发新订阅
                008.下发新订阅，直连写+merge操作
                009. 对同一张表下发多个订阅关系（条件订阅、增量订阅）
                010. 创建多张表，每张表不同订阅关系
                011. 下发新订阅，执行insert、delete、replace操作
                012. 下发新订阅，执行insert、delete、update操作
                013. 下发新订阅，执行replace、delete、insert操作
                014. 下发新订阅，执行replace、delete、delete操作
                015. 下发新订阅，执行insert、delete、insert、update、replace、delete操作
                016. 下发新订阅，创建特殊复杂表（定长字段+节点定长数量），执行直连写

 History      :
 Author       : liaoxiang lwx1036939
 Modification : 2023/07/14
*****************************************************************************/

#include "DirectWriteNewSub.h"
#define FULLTABLE 0xff
SnUserDataT *user_data1;
SnUserDataT *user_data2;

class NewClusFunSimTb : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {
        if (g_envType == 0) {
            system("sh $TEST_HOME/tools/modifyCfg.sh \"enableClusterHash=1\"");  // 打开聚蔟容器
            system("sh $TEST_HOME/tools/start.sh");
        } else {
            system("sh $TEST_HOME/tools/start.sh");
        }
        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();

        if (g_envType == 0) {
            system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
        }
    }
};

void NewClusFunSimTb::SetUp()
{
    int ret = 0;

    ret = testSnMallocUserData(&user_data1, g_endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSnMallocUserData(&user_data2, g_endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建同步连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char errorMsg6[128] = {};
    (void)snprintf(errorMsg6, sizeof(errorMsg6), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg6);
    char errorMsg9[128] = {};
    (void)snprintf(errorMsg9, sizeof(errorMsg9), "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg9);

    AW_CHECK_LOG_BEGIN();

    // create vertex table
    ret = TestCreateLabel(g_stmt, g_simpileVertexFilePath1, g_vertexLabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建订阅连接
    ret = testSubConnect(&g_connSub, &g_stmtSub, 1, g_epoll_reg_info, g_vertexSubConnName, &g_chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubConnect(&g_connSub1, &g_stmtSub1, 1, g_epoll_reg_info, g_vertexSubConnName1, &g_chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_testcaseId = 0;
}

void NewClusFunSimTb::TearDown()
{
    AW_CHECK_LOG_END();
    testSnFreeUserData(user_data1);
    testSnFreeUserData(user_data2);

    // drop vertex table
    int ret = GmcDropVertexLabel(g_stmt, g_vertexLabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放同步连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放订阅连接
    ret = testSubDisConnect(g_connSub, g_stmtSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(g_connSub1, g_stmtSub1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_testcaseId = 0;
}

// 001. 下发新订阅，执行insert、update、replace、delete操作
TEST_F(NewClusFunSimTb, DW_005_NewClusFunSimTb_001)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0;

    // 下发订阅
    g_testcaseId = 1;
    TestGmcSubscribeCheckPush(
        g_stmt, g_connSub, g_vertexIncSubPath, g_vertexSubName, user_data1, NewSnSimpleCallBackWithCheck);

    // 执行 insert 操作
    TestWriteVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_INSERT, true);

    // 执行 update 操作
    TestUpdateVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_UPDATE, true);

    // 执行 replace update 操作
    TestUpdateVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_REPLACE, true);

    // 执行 delete 操作
    TestInitPushList(g_endNum - g_startNum, -1);
    TestDeleteVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_DELETE);

    // 校验推送次数
    ret = testWaitStMgSnRecv(user_data1, GMC_SUB_EVENT_MODIFY, 0, g_endNum * 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitStMgSnRecv(user_data1, GMC_SUB_EVENT_DELETE, g_endNum, g_endNum * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCheckPushList(g_endNum - g_startNum, 0);

    // 删除订阅关系
    ret = GmcUnSubscribe(g_stmt, g_vertexSubName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "END");
}

// 002. 下发新订阅（条件订阅and），执行insert、update、replace、delete操作
TEST_F(NewClusFunSimTb, DW_005_NewClusFunSimTb_002)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0;

    // 下发条件订阅
    g_testcaseId = 1;
    TestGmcSubscribeCheckPush(
        g_stmt, g_connSub, g_vertexConAndSubPath, g_vertexSubName, user_data1, NewSnSimpleCallBackWithCheck);

    // 执行 insert 操作
    TestWriteVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_INSERT, true);

    // 执行 update 操作
    TestUpdateVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_UPDATE, true);

    // 执行 replace update 操作
    TestUpdateVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_REPLACE, true);

    // 执行 delete 操作
    TestInitPushList(g_endNum - g_startNum, -1);
    TestDeleteVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_DELETE);

    // 校验推送次数
    ret = testWaitStMgSnRecv(user_data1, GMC_SUB_EVENT_MODIFY, 0, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // eventType是Delete和Age的时候不对条件进行过滤
    ret = testWaitStMgSnRecv(user_data1, GMC_SUB_EVENT_DELETE, g_endNum, g_endNum * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCheckPushList(g_endNum - g_startNum, 0);

    // 删除订阅关系
    ret = GmcUnSubscribe(g_stmt, g_vertexSubName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "END");
}

// 003. 下发新订阅（条件订阅or），执行insert、update、replace、delete操作
TEST_F(NewClusFunSimTb, DW_005_NewClusFunSimTb_003)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0;

    // 下发条件订阅
    g_testcaseId = 1;
    TestGmcSubscribeCheckPush(
        g_stmt, g_connSub, g_vertexConAndSubPath, g_vertexSubName, user_data1, NewSnSimpleCallBackWithCheck);

    // 执行 insert 操作
    TestWriteVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_INSERT, true);

    // 执行 update 操作
    TestUpdateVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_UPDATE, true);

    // 执行 replace update 操作
    TestUpdateVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_REPLACE, true);

    // 执行 delete 操作
    TestInitPushList(g_endNum - g_startNum, -1);
    TestDeleteVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_DELETE);

    // 校验推送次数
    ret = testWaitStMgSnRecv(user_data1, GMC_SUB_EVENT_MODIFY, 0, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // eventType是Delete和Age的时候不对条件进行过滤
    ret = testWaitStMgSnRecv(user_data1, GMC_SUB_EVENT_DELETE, g_endNum, g_endNum * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCheckPushList(g_endNum - g_startNum, 0);

    // 删除订阅关系
    ret = GmcUnSubscribe(g_stmt, g_vertexSubName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "END");
}

// 004. 下发新订阅，开启对账
TEST_F(NewClusFunSimTb, DW_005_NewClusFunSimTb_004)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0;
    bool isAbnormal = false;

    // 下发订阅
    g_testcaseId = 2;
    TestGmcSubscribeCheckPush(
        g_stmt, g_connSub, g_vertexIncSubPath, g_vertexSubName, user_data1, NewSnSimpleCallBackWithCheck);

    // 执行 insert 操作
    TestWriteVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_INSERT, true);

    // 开启对账
    ret = GmcBeginCheck(g_stmt, g_vertexLabelName, FULLTABLE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 执行 update 操作
    TestUpdateVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum / 2, GMC_OPERATION_UPDATE, true);

    // 执行 replace update 操作
    TestUpdateVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum / 2, GMC_OPERATION_REPLACE, true);

    // 执行 delete 操作
    TestInitPushList((g_endNum / 2) - g_startNum, -1, 0);
    TestDeleteVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum / 2, GMC_OPERATION_DELETE);

    // 结束对账
    TestInitPushList(g_endNum - (g_endNum / 2), -1, (g_endNum / 2));
    ret = GmcEndCheck(g_stmt, g_vertexLabelName, FULLTABLE, isAbnormal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验推送次数
    ret = testWaitStMgSnRecv(user_data1, GMC_SUB_EVENT_MODIFY, 0, g_endNum * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitStMgSnRecv(user_data1, GMC_SUB_EVENT_DELETE, g_endNum / 2, g_endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCheckPushList(g_endNum - (g_endNum / 2), 0, 0);
    ret = testWaitStMgSnRecv(user_data1, GMC_SUB_EVENT_AGED, g_endNum / 2, g_endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCheckPushList(g_endNum - (g_endNum / 2), 0, (g_endNum / 2));

    // 删除订阅关系
    ret = GmcUnSubscribe(g_stmt, g_vertexSubName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "END");
}

// 005. 下发新订阅，结束对账后执行replace操作
TEST_F(NewClusFunSimTb, DW_005_NewClusFunSimTb_005)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0;
    bool isAbnormal = false;

    // 下发订阅
    TestGmcSubscribeCheckPush(
        g_stmt, g_connSub, g_vertexIncSubPath005, g_vertexSubName, user_data1, NewSnSimpleCallBackWithCheck);

    // 执行 insert 操作
    TestWriteVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_INSERT, true);

    // // 开启对账
    ret = GmcBeginCheck(g_stmt, g_vertexLabelName, FULLTABLE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 执行 update 操作
    TestUpdateVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum / 2, GMC_OPERATION_UPDATE, true);

    // 执行 delete 操作
    TestDeleteVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum / 2, GMC_OPERATION_DELETE);
    ret = testWaitStMgSnRecv(user_data1, GMC_SUB_EVENT_MODIFY, g_endNum / 2, g_endNum * 1.5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitStMgSnRecv(user_data1, GMC_SUB_EVENT_DELETE, g_endNum / 2, g_endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结束对账
    TestInitPushList(g_endNum - (g_endNum / 2), -1, (g_endNum / 2));
    g_testcaseId = 3;
    ret = GmcEndCheck(g_stmt, g_vertexLabelName, FULLTABLE, isAbnormal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 执行 replace insert 操作
    TestInitPushList((g_endNum / 2) - g_startNum, -1, 0);
    TestWriteVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum / 2, GMC_OPERATION_REPLACE, true, false);

    // 校验推送次数
    ret = testWaitStMgSnRecv(user_data1, GMC_SUB_EVENT_MODIFY, g_endNum / 2, g_endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCheckPushList(g_endNum - (g_endNum / 2), 0, 0);
    // age 推送的delete
    ret = testWaitStMgSnRecv(user_data1, GMC_SUB_EVENT_DELETE, g_endNum / 2, g_endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCheckPushList(g_endNum - (g_endNum / 2), 0, (g_endNum / 2));

    // 删除订阅关系
    ret = GmcUnSubscribe(g_stmt, g_vertexSubName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "END");
}

// 006. 执行insert操作，下发全量+增量订阅，执行update、replace、delete操作
TEST_F(NewClusFunSimTb, DW_005_NewClusFunSimTb_006)
{
    AW_FUN_Log(LOG_STEP, "START");
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    int ret = 0;

    // 执行 insert 操作
    TestInitPushList(g_endNum, -1);
    TestWriteVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_INSERT, true);

    // 下发全量订阅
    g_testcaseId = 4;
    TestGmcSubscribeCheckPush(
        g_stmt, g_connSub1, g_vertexFullSubPath, g_vertexSubName1, user_data2, NewSnSimpleCallBackWithCheck);

    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_INITIAL_LOAD, g_endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCheckPushList(g_endNum, 0);

    // 下发订阅
    g_testcaseId = 1;
    TestInitPushList(g_endNum, -1);
    TestGmcSubscribeCheckPush(
        g_stmt, g_connSub, g_vertexIncSubPath, g_vertexSubName, user_data1, NewSnSimpleCallBackWithCheck);

    // 执行 update 操作
    TestUpdateVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_UPDATE, true);

    // 执行 replace update 操作
    TestUpdateVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_REPLACE, true);

    // 执行 delete 操作
    TestDeleteVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_DELETE);

    // 校验推送次数
    ret = testWaitStMgSnRecv(user_data1, GMC_SUB_EVENT_MODIFY, 0, g_endNum * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitStMgSnRecv(user_data1, GMC_SUB_EVENT_DELETE, g_endNum, g_endNum * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCheckPushList(g_endNum, 0);

    // 删除订阅关系
    ret = GmcUnSubscribe(g_stmt, g_vertexSubName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, g_vertexSubName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "END");
}

// 007. 表升级，对低、高版本下发新订阅
TEST_F(NewClusFunSimTb, DW_005_NewClusFunSimTb_007)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0;

    // 表升级
    char *expectValue = (char *)"upgrade successfully";
    ret = TestUpdateVertexLabel(g_simpileVertexFilePath2, expectValue, g_vertexLabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对低版本下发订阅
    g_testcaseId = 1;
    TestGmcSubscribeCheckPush(
        g_stmt, g_connSub, g_vertexIncSubPath, g_vertexSubName, user_data1, NewSnOldVersionSimpleCallBackWithCheck);

    // 对高版本下发订阅
    TestGmcSubscribeCheckPush(
        g_stmt, g_connSub1, g_vertexIncSubPath2, g_vertexSubName1, user_data2, NewSnSimpleCallBackWithCheck);

    // 执行 insert 操作
    TestWriteVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_INSERT, true);

    // 执行 update 操作
    TestUpdateVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_UPDATE, true);

    // 执行 replace update 操作
    TestUpdateVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_REPLACE, true);

    // 执行 delete 操作
    TestInitPushList(g_endNum - g_startNum, -1, g_startNum, true);
    TestDeleteVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_DELETE);

    // 校验推送次数
    ret = testWaitStMgSnRecv(user_data1, GMC_SUB_EVENT_MODIFY, 0, g_endNum * 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitStMgSnRecv(user_data1, GMC_SUB_EVENT_DELETE, g_endNum, g_endNum * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitStMgSnRecv(user_data2, GMC_SUB_EVENT_MODIFY, 0, g_endNum * 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitStMgSnRecv(user_data2, GMC_SUB_EVENT_DELETE, g_endNum, g_endNum * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCheckPushList(g_endNum - g_startNum, 0, 0, true);

    // 删除订阅关系
    ret = GmcUnSubscribe(g_stmt, g_vertexSubName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, g_vertexSubName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "END");
}

// 008.下发新订阅，直连写+merge操作
TEST_F(NewClusFunSimTb, DW_005_NewClusFunSimTb_008)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0;

    // 下发订阅
    TestGmcSubscribeCheckPush(
        g_stmt, g_connSub, g_vertexIncSubPath, g_vertexSubName, user_data1, NewSnSimpleCallBackWithCheck);

    // 执行 insert 操作
    TestWriteVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_INSERT, true);

    // 执行 update 操作
    TestUpdateVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_UPDATE, true);

    // 执行 replace update 操作
    TestUpdateVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_REPLACE, true);

    // 执行 delete 操作
    TestDeleteVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_DELETE);
    ret = testWaitStMgSnRecv(user_data1, GMC_SUB_EVENT_MODIFY, 0, g_endNum * 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitStMgSnRecv(user_data1, GMC_SUB_EVENT_DELETE, g_endNum, g_endNum * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 执行 merge insert 操作
    g_testcaseId = 5;
    TestInitPushList(g_endNum - g_startNum, -1);
    TestWriteVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_MERGE, true, false, 0);

    // 校验推送次数
    ret = testWaitSnRecv(user_data1, GMC_SUB_EVENT_MODIFY, g_endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCheckPushList(g_endNum - g_startNum, 0);

    // 删除订阅关系
    ret = GmcUnSubscribe(g_stmt, g_vertexSubName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "END");
}

// 009. 对同一张表下发多个订阅关系（条件订阅、增量订阅）
TEST_F(NewClusFunSimTb, DW_005_NewClusFunSimTb_009)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0;

    // 下发增量订阅
    g_testcaseId = 1;
    TestGmcSubscribeCheckPush(
        g_stmt, g_connSub, g_vertexIncSubPath, g_vertexSubName, user_data1, NewSnOldVersionSimpleCallBackWithCheck);
    // 下发条件订阅
    TestGmcSubscribeCheckPush(
        g_stmt, g_connSub1, g_vertexConAndSubPath2, g_vertexSubName1, user_data2, NewSnSimpleCallBackWithCheck);

    // 执行 insert 操作
    TestWriteVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_INSERT, true);

    // 执行 update 操作
    TestUpdateVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_UPDATE, true);

    // 执行 replace update 操作
    TestUpdateVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_REPLACE, true);

    // 执行 delete 操作
    TestInitPushList(g_endNum - g_startNum, -1, g_startNum, true);
    TestDeleteVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_DELETE);

    // 校验推送次数
    ret = testWaitStMgSnRecv(user_data1, GMC_SUB_EVENT_MODIFY, 0, g_endNum * 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitStMgSnRecv(user_data1, GMC_SUB_EVENT_DELETE, g_endNum, g_endNum * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitStMgSnRecv(user_data2, GMC_SUB_EVENT_MODIFY, 0, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitStMgSnRecv(user_data2, GMC_SUB_EVENT_DELETE, g_endNum, g_endNum * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCheckPushList(g_endNum - g_startNum, 0, 0, true);

    // 删除订阅关系
    ret = GmcUnSubscribe(g_stmt, g_vertexSubName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, g_vertexSubName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "END");
}

// 010. 创建多张表，每张表不同订阅关系
TEST_F(NewClusFunSimTb, DW_005_NewClusFunSimTb_010)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0;

    // create vertex table2
    ret = TestCreateLabel(g_stmt, g_simpileVertexFilePath3, g_vertexLabelName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对表1下发增量订阅
    g_testcaseId = 1;
    TestGmcSubscribeCheckPush(
        g_stmt, g_connSub, g_vertexIncSubPath, g_vertexSubName, user_data1, NewSnOldVersionSimpleCallBackWithCheck);
    // 对表2下发条件订阅
    TestGmcSubscribeCheckPush(
        g_stmt, g_connSub1, g_vertexConOrSubPath2, g_vertexSubName1, user_data2, NewSnSimpleCallBackWithCheck);

    // 表1执行 insert 操作
    TestWriteVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_INSERT, true);

    // 表1执行 update 操作
    TestUpdateVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_UPDATE, true);

    // 表1执行 replace update 操作
    TestUpdateVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_REPLACE, true);

    // 表1执行 delete 操作
    TestInitPushList(g_endNum - g_startNum, -1, g_startNum, true);
    TestDeleteVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_DELETE);

    // 表2执行 insert 操作
    TestWriteVertex(g_stmt, g_vertexLabelName1, g_startNum, g_endNum, GMC_OPERATION_INSERT, true);

    // 表2执行 update 操作
    TestUpdateVertex(g_stmt, g_vertexLabelName1, g_startNum, g_endNum, GMC_OPERATION_UPDATE, true);

    // 表2执行 replace update 操作
    TestUpdateVertex(g_stmt, g_vertexLabelName1, g_startNum, g_endNum, GMC_OPERATION_REPLACE, true);

    // 表2执行 delete 操作
    TestDeleteVertex(g_stmt, g_vertexLabelName1, g_startNum, g_endNum, GMC_OPERATION_DELETE);

    // 校验推送次数
    ret = testWaitStMgSnRecv(user_data1, GMC_SUB_EVENT_MODIFY, 0, g_endNum * 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitStMgSnRecv(user_data1, GMC_SUB_EVENT_DELETE, g_endNum, g_endNum * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitStMgSnRecv(user_data2, GMC_SUB_EVENT_MODIFY, 0, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitStMgSnRecv(user_data2, GMC_SUB_EVENT_DELETE, g_endNum, g_endNum * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCheckPushList(g_endNum - g_startNum, 0, 0, true);

    // 删除订阅关系
    ret = GmcUnSubscribe(g_stmt, g_vertexSubName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, g_vertexSubName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // drop vertex table2
    ret = GmcDropVertexLabel(g_stmt, g_vertexLabelName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "END");
}

// 011. 下发新订阅，执行insert、delete、replace操作
TEST_F(NewClusFunSimTb, DW_005_NewClusFunSimTb_011)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0;

    // 下发订阅
    TestGmcSubscribeCheckPush(
        g_stmt, g_connSub, g_vertexIncSubPath, g_vertexSubName, user_data1, NewSnSimpleCallBackWithCheck);

    // 执行 insert 操作
    TestWriteVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_INSERT, true);

    // 执行 delete 操作
    TestDeleteVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_DELETE);

    // 执行 replace insert 操作（replace标记删除数据，成功，收到modify订阅消息）
    g_testcaseId = 5;
    TestInitPushList(g_endNum - g_startNum, -1);
    TestWriteVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_REPLACE, true, false);

    // 校验推送次数
    // delete 是标记删除，可能出现一条推送多次的情况，最极端情况是每一条都推送2次，故上限是20次
    // delele 是中间状态，会被后面的modify状态合并，所以下限是0
    ret = testWaitStMgSnRecv(user_data1, GMC_SUB_EVENT_DELETE, 0, g_endNum * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitStMgSnRecv(user_data1, GMC_SUB_EVENT_MODIFY, g_endNum, g_endNum * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCheckPushList(g_endNum - g_startNum, 0);

    // 删除订阅关系
    ret = GmcUnSubscribe(g_stmt, g_vertexSubName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "END");
}

// 012. 下发新订阅，执行insert、delete、update操作
TEST_F(NewClusFunSimTb, DW_005_NewClusFunSimTb_012)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0;

    // 下发订阅
    g_testcaseId = 6;
    TestGmcSubscribeCheckPush(
        g_stmt, g_connSub, g_vertexIncSubPath, g_vertexSubName, user_data1, NewSnSimpleCallBackWithCheck);

    // 执行 insert 操作
    TestWriteVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_INSERT, true);

    // 执行 delete 操作
    TestInitPushList(g_endNum - g_startNum, -1);
    TestDeleteVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_DELETE);

    // 执行 update 操作（update标记删除的数据，返回ok，收不到订阅消息）
    TestUpdateVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_UPDATE, true, 0);

    // 校验推送次数
    ret = testWaitStMgSnRecv(user_data1, GMC_SUB_EVENT_MODIFY, 0, g_endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitStMgSnRecv(user_data1, GMC_SUB_EVENT_DELETE, g_endNum, g_endNum * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCheckPushList(g_endNum - g_startNum, 0);

    // 删除订阅关系
    ret = GmcUnSubscribe(g_stmt, g_vertexSubName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "END");
}

// 013. 下发新订阅，执行replace、delete、insert操作
TEST_F(NewClusFunSimTb, DW_005_NewClusFunSimTb_013)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0;

    // 下发订阅
    TestGmcSubscribeCheckPush(
        g_stmt, g_connSub, g_vertexIncSubPath, g_vertexSubName, user_data1, NewSnSimpleCallBackWithCheck);

    // 执行 replace insert 操作
    TestWriteVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_REPLACE, true);

    // 执行 delete 操作
    TestDeleteVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_DELETE);

    // 执行 insert 操作（insert 标记删除数据，成功，收到modify订阅消息）
    g_testcaseId = 5;
    TestInitPushList(g_endNum - g_startNum, -1);
    TestWriteVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_INSERT, true);

    // 校验推送次数
    ret = testWaitStMgSnRecv(user_data1, GMC_SUB_EVENT_MODIFY, g_endNum, g_endNum * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCheckPushList(g_endNum - g_startNum, 0);
    ret = testWaitStMgSnRecv(user_data1, GMC_SUB_EVENT_DELETE, 0, g_endNum * 2);  // delete是中间状态，推送的次数不稳定
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除订阅关系
    ret = GmcUnSubscribe(g_stmt, g_vertexSubName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "END");
}

// 014. 下发新订阅，执行replace、delete、delete操作
TEST_F(NewClusFunSimTb, DW_005_NewClusFunSimTb_014)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0;

    // 下发订阅
    g_testcaseId = 6;
    TestGmcSubscribeCheckPush(
        g_stmt, g_connSub, g_vertexIncSubPath, g_vertexSubName, user_data1, NewSnSimpleCallBackWithCheck);

    // 执行 replace insert 操作
    TestWriteVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_REPLACE, true);

    // 执行 delete 操作
    TestInitPushList(g_endNum - g_startNum, -1);
    TestDeleteVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_DELETE);

    // 执行 delete 操作（delete标记删除的数据，返回ok，收不到订阅消息）
    TestDeleteVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_DELETE, 0);

    // 校验推送次数
    ret = testWaitStMgSnRecv(user_data1, GMC_SUB_EVENT_MODIFY, 0, g_endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitStMgSnRecv(user_data1, GMC_SUB_EVENT_DELETE, g_endNum, g_endNum * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCheckPushList(g_endNum - g_startNum, 0);

    // 删除订阅关系
    ret = GmcUnSubscribe(g_stmt, g_vertexSubName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "END");
}

// 015. 下发新订阅，执行insert、delete、insert、update、replace、delete操作
TEST_F(NewClusFunSimTb, DW_005_NewClusFunSimTb_015)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0;

    // 下发订阅
    TestGmcSubscribeCheckPush(
        g_stmt, g_connSub, g_vertexIncSubPath, g_vertexSubName, user_data1, NewSnSimpleCallBackWithCheck);

    // 执行 insert 操作
    TestWriteVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_INSERT, true);

    // 执行 delete 操作
    TestDeleteVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_DELETE);

    // 执行 insert 操作
    TestWriteVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_INSERT, true);

    // 执行 update 操作
    TestUpdateVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_UPDATE, true);

    // 执行 replace update 操作
    TestUpdateVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_REPLACE, true);

    // 执行 delete 操作
    g_testcaseId = 1;
    TestInitPushList(g_endNum - g_startNum, -1);
    TestDeleteVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, GMC_OPERATION_DELETE);

    // 校验推送次数
    ret = testWaitStMgSnRecv(user_data1, GMC_SUB_EVENT_MODIFY, 0, g_endNum * 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitStMgSnRecv(user_data1, GMC_SUB_EVENT_DELETE, g_endNum, g_endNum * 2 + 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCheckPushList(g_endNum - g_startNum, 0);

    // 删除订阅关系
    ret = GmcUnSubscribe(g_stmt, g_vertexSubName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "END");
}

// 016. 下发新订阅，创建特殊复杂表（定长字段+节点定长数量），执行直连写
TEST_F(NewClusFunSimTb, DW_005_NewClusFunSimTb_016)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0, userDataIdx1 = 0;
    char string[] = "1234567";

    // create tree table
    ret = TestCreateLabel(g_stmt, g_treeFilePath2, g_treeTableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对低版本下发订阅
    TestTreeGmcSubscribe(g_stmt, g_connSub, g_treeIncSubPath2, g_vertexSubName1, user_data1);

    // 执行insert操作
    for (int i = g_startNum; i < g_endNum; i++) {
        ((int *)(user_data1->new_value))[userDataIdx1] = i;
        userDataIdx1++;
        TestGmcInsertTree(g_stmt, i, 0, string, g_arrayNum, g_vectorNum, g_treeTableName2, GMC_OPERATION_INSERT, true);
    }

    // 校验推送次数
    ret = testWaitSnRecv(user_data1, GMC_SUB_EVENT_MODIFY, g_endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除订阅关系
    ret = GmcUnSubscribe(g_stmt, g_vertexSubName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // drop tree table
    ret = GmcDropVertexLabel(g_stmt, g_treeTableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "END");
}
