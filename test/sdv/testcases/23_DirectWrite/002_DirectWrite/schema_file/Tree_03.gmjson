[{"version": "2.0", "type": "record", "name": "Tree_03", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": false}, {"name": "F2", "type": "uint32", "nullable": false}, {"name": "F3", "type": "uint32", "nullable": false}, {"name": "F4", "type": "uint32", "nullable": false}, {"name": "F5", "type": "uint32", "nullable": false}, {"name": "F6", "type": "uint32", "nullable": false}, {"name": "F7", "type": "uint32", "nullable": false}, {"name": "F8", "type": "uint32", "nullable": false}, {"name": "F9", "type": "uint32", "nullable": false}, {"name": "F10", "type": "uint32", "nullable": false}, {"name": "F11", "type": "uint32", "nullable": false}, {"name": "F12", "type": "uint32", "nullable": false}, {"name": "F13", "type": "uint32", "nullable": false}, {"name": "F14", "type": "uint32", "nullable": false}, {"name": "F15", "type": "uint32", "nullable": false}, {"name": "F16", "type": "string", "size": 20, "nullable": false}, {"name": "F17", "type": "string", "size": 20, "nullable": false}, {"name": "F18", "type": "string", "size": 20, "nullable": false}, {"name": "F19", "type": "string", "size": 20, "nullable": false}, {"name": "F20", "type": "string", "size": 20, "nullable": false}, {"name": "F21", "type": "string", "size": 20, "nullable": false}, {"name": "F22", "type": "string", "size": 20, "nullable": false}, {"name": "F23", "type": "string", "size": 20, "nullable": false}, {"name": "F24", "type": "string", "size": 20, "nullable": false}, {"name": "F25", "type": "string", "size": 20, "nullable": false}, {"name": "F26", "type": "string", "size": 20, "nullable": false}, {"name": "F27", "type": "string", "size": 20, "nullable": false}, {"name": "F28", "type": "string", "size": 20, "nullable": false}, {"name": "F29", "type": "string", "size": 20, "nullable": false}, {"name": "F30", "type": "string", "size": 20, "nullable": false}, {"name": "F31", "type": "string", "size": 20, "nullable": false}, {"name": "F32", "type": "uint32", "nullable": true}, {"name": "F33", "type": "string", "size": 20, "nullable": true}, {"name": "T1", "type": "record", "fields": [{"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "string", "size": 20, "nullable": true}]}, {"name": "A1", "type": "record", "fixed_array": true, "size": 100, "fields": [{"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "string", "size": 20, "nullable": true}]}, {"name": "V1", "type": "record", "vector": true, "size": 100, "fields": [{"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "string", "size": 20, "nullable": true}]}], "keys": [{"name": "Tree_pk", "index": {"type": "primary"}, "node": "Tree_03", "fields": ["F0", "F1", "F2", "F3", "F4", "F5", "F6", "F7", "F8", "F9", "F10", "F11", "F12", "F13", "F14", "F15", "F16", "F17", "F18", "F19", "F20", "F21", "F22", "F23", "F24", "F25", "F26", "F27", "F28", "F29", "F30", "F31"], "constraints": {"unique": true}}]}]