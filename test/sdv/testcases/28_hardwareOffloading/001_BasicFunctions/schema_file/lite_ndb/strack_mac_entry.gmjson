{"comment": "Mac Entry软表", "version": "2.0", "type": "record", "name": "strack_mac_entry", "config": {"check_validity": false}, "max_record_count": 64, "fields": [{"name": "mac", "type": "fixed", "size": 6, "comment": "Mac 地址"}, {"name": "protocol", "type": "uint32", "comment": "协议类型"}, {"name": "timeout", "type": "uint32", "comment": "超时周期"}, {"name": "acl", "type": "record", "array": true, "size": 3, "comment": "mac acl", "fields": [{"name": "entries", "type": "record", "array": true, "size": 8, "comment": "auiEntry", "fields": [{"name": "entryID", "type": "uint32", "comment": "entry ID"}]}]}, {"name": "reserve", "type": "uint32", "comment": "预留字段"}], "keys": [{"name": "strack_mac_entry_pk", "index": {"type": "primary"}, "node": "strack_mac_entry", "fields": ["mac", "protocol"], "constraints": {"unique": true}, "comment": "根据主键索引"}, {"name": "strack_mac_entry_sk", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "strack_mac_entry", "fields": ["protocol"], "constraints": {"unique": false}, "comment": "根据协议类型索引"}]}