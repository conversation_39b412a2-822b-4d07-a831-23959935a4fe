{"comment": "端口镜像观察口", "version": "2.0", "type": "record", "name": "port_observe", "config": {"check_validity": false}, "max_record_count": 256, "fields": [{"name": "vrid", "type": "uint16", "comment": "虚拟路由"}, {"name": "observIndex", "type": "uint32", "comment": "镜像索引"}, {"name": "ifIndex", "type": "uint32", "comment": "接口索引"}, {"name": "dstIpAddr", "type": "uint32", "comment": "目的IP"}, {"name": "srcIpAddr", "type": "uint32", "comment": "源IP"}, {"name": "observType", "type": "uint8", "comment": "观察口类型"}, {"name": "dscpValue", "type": "uint16", "comment": "dscp值"}, {"name": "vlanValue", "type": "uint16", "comment": "vlan索引"}, {"name": "ageTime", "type": "uint16", "comment": "时间"}, {"name": "erspanId", "type": "uint16", "comment": "erspn索引"}, {"name": "enhaced<PERSON><PERSON>", "type": "uint8", "comment": "增强模式"}, {"name": "packetNum", "type": "uint8", "comment": "包数"}, {"name": "tcpAbnormal", "type": "uint8", "comment": "tcp模式"}, {"name": "ipType", "type": "uint32", "comment": "ip类型"}, {"name": "dstIpv6Addr", "type": "fixed", "size": 16, "comment": "目的IPv6"}, {"name": "srcIpv6Addr", "type": "fixed", "size": 16, "comment": "源IPv6"}, {"name": "rspanVlanId", "type": "uint32", "comment": "rspan vlan"}, {"name": "srcPid", "type": "uint32", "comment": "保留字段"}, {"name": "cirValue", "type": "uint32", "comment": "保留字段"}, {"name": "cirUnit", "type": "uint32", "comment": "cir的大小"}, {"name": "serialId", "type": "uint32", "comment": "保留字段"}, {"name": "objId", "type": "uint64", "comment": "保留字段"}, {"name": "verNo", "type": "uint32", "comment": "保留字段"}], "keys": [{"name": "port_observe", "index": {"type": "primary"}, "node": "port_observe", "fields": ["vrid", "observIndex"], "constraints": {"unique": true}, "comment": "索引"}]}