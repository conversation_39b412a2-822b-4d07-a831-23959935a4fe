{"version": "2.0", "type": "record", "name": "ioam_res_sequence_id", "config": {"check_validity": false}, "max_record_count": 2048, "fields": [{"name": "vrid", "type": "uint32"}, {"name": "curr_index", "type": "uint32"}, {"name": "used_count", "type": "uint32"}, {"name": "resv", "type": "uint32"}], "keys": [{"name": "pk_vrid", "index": {"type": "primary"}, "node": "ioam_res_sequence_id", "fields": ["vrid"], "constraints": {"unique": true}}]}