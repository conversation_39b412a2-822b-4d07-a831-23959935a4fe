{"comment": "接口流量统计", "version": "2.0", "type": "record", "name": "l3if_statistic", "config": {"check_validity": false}, "max_record_count": 16384, "fields": [{"name": "ifindex", "type": "uint32", "comment": "端口索引"}, {"name": "vrid", "type": "uint32"}, {"name": "vsid", "type": "uint32"}, {"name": "if_type", "type": "uint32"}, {"name": "trunk_flag", "type": "uint32"}, {"name": "trunk_id", "type": "uint32"}, {"name": "tb", "type": "uint32"}, {"name": "tp", "type": "uint32"}, {"name": "vlan_id", "type": "uint32"}, {"name": "tunnelid", "type": "uint32"}, {"name": "opType", "type": "uint32"}, {"name": "memifindex", "type": "uint32"}, {"name": "ids", "type": "record", "array": true, "size": 8, "comment": "隧道id", "fields": [{"name": "ingressid", "type": "uint32", "default": 0, "comment": "隧道ingressid"}, {"name": "egressid", "type": "uint32", "default": 0, "comment": "隧道egressid"}]}], "keys": [{"name": "l3if_statistic_pk", "index": {"type": "primary"}, "node": "l3if_statistic", "fields": ["ifindex"], "constraints": {"unique": true}, "comment": "主键"}]}