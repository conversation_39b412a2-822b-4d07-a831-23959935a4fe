[{"ifindex": 1, "family": 2, "prefix_length": 8, "scope": 254, "local_ip4": "127.0.0.1", "peer_ip4": "", "broadcast_ip4": "", "multicast_ip4": "", "anycast_ip4": "", "label": "lo", "valid_lft": 4294967295, "preferred_lft": 4294967295, "create_time": 2083, "last_update_time": 2083, "flags": 128}, {"ifindex": 1, "family": 10, "prefix_length": 128, "scope": 254, "local_ip4": "::1", "peer_ip4": "", "broadcast_ip4": "", "multicast_ip4": "", "anycast_ip4": "", "label": "", "valid_lft": 4294967295, "preferred_lft": 4294967295, "create_time": 2083, "last_update_time": 2083, "flags": 128}, {"ifindex": 1000, "family": 2, "prefix_length": 24, "scope": 0, "local_ip4": "************", "peer_ip4": "", "broadcast_ip4": "*************", "multicast_ip4": "", "anycast_ip4": "", "label": "GE0", "valid_lft": 4294967295, "preferred_lft": 4294967295, "create_time": 4731, "last_update_time": 4731, "flags": 128}, {"ifindex": 3, "family": 2, "prefix_length": 24, "scope": 0, "local_ip4": "*************", "peer_ip4": "", "broadcast_ip4": "***************", "multicast_ip4": "", "anycast_ip4": "", "label": "br0", "valid_lft": 4294967295, "preferred_lft": 4294967295, "create_time": 4796, "last_update_time": 4796, "flags": 128}, {"ifindex": 12, "family": 10, "prefix_length": 64, "scope": 253, "local_ip4": "fe80::3407:6ff:fe47:2bab", "peer_ip4": "", "broadcast_ip4": "", "multicast_ip4": "", "anycast_ip4": "", "label": "", "valid_lft": 4294967295, "preferred_lft": 4294967295, "create_time": 4728, "last_update_time": 4728, "flags": 128}, {"ifindex": 3, "family": 10, "prefix_length": 64, "scope": 253, "local_ip4": "fe80::94c8:c4ff:fef9:934", "peer_ip4": "", "broadcast_ip4": "", "multicast_ip4": "", "anycast_ip4": "", "label": "", "valid_lft": 4294967295, "preferred_lft": 4294967295, "create_time": 4728, "last_update_time": 4728, "flags": 128}, {"ifindex": 12, "family": 2, "prefix_length": 24, "scope": 0, "local_ip4": "**********", "peer_ip4": "", "broadcast_ip4": "************", "multicast_ip4": "", "anycast_ip4": "", "label": "br_docker0", "valid_lft": 4294967295, "preferred_lft": 4294967295, "create_time": 6120, "last_update_time": 6120, "flags": 128}, {"ifindex": 3, "family": 10, "prefix_length": 64, "scope": 0, "local_ip4": "fc01::94c8:c4ff:fef9:934", "peer_ip4": "", "broadcast_ip4": "", "multicast_ip4": "", "anycast_ip4": "", "label": "", "valid_lft": 86400, "preferred_lft": 14400, "create_time": 5786, "last_update_time": 204537160, "flags": 256}, {"ifindex": 12, "family": 10, "prefix_length": 64, "scope": 253, "local_ip4": "fe80::1", "peer_ip4": "", "broadcast_ip4": "", "multicast_ip4": "", "anycast_ip4": "", "label": "", "valid_lft": 4294967295, "preferred_lft": 4294967295, "create_time": 6120, "last_update_time": 6120, "flags": 128}, {"ifindex": 12, "family": 10, "prefix_length": 64, "scope": 0, "local_ip4": "fc02::1", "peer_ip4": "", "broadcast_ip4": "", "multicast_ip4": "", "anycast_ip4": "", "label": "", "valid_lft": 4294967295, "preferred_lft": 4294967295, "create_time": 6121, "last_update_time": 6121, "flags": 128}]