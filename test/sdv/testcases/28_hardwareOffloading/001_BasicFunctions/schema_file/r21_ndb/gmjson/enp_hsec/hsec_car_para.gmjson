{"comment": "car profile", "version": "2.0", "type": "record", "name": "hsec_car_para", "config": {"check_validity": false}, "max_record_count": 1024, "fields": [{"name": "unit", "type": "uint32", "comment": "芯片号"}, {"name": "car_profile_id", "type": "uint32", "comment": "profile 索引"}, {"name": "cir", "type": "uint32", "comment": "承诺信息速率"}, {"name": "pir", "type": "uint32", "comment": "峰值信息速率"}, {"name": "cbs", "type": "uint32", "comment": "承诺突发尺寸"}, {"name": "pbs", "type": "uint32", "comment": "峰值突发尺寸"}, {"name": "car_type", "type": "uint32", "comment": "类型"}, {"name": "car_mode", "type": "uint32", "comment": "模式"}, {"name": "reserve", "type": "uint32", "comment": "预留字段"}], "keys": [{"name": "hsec_car_para_key", "index": {"type": "primary"}, "node": "hsec_car_para", "fields": ["unit", "car_profile_id"], "constraints": {"unique": true}, "comment": "主键索引"}, {"name": "car_para_scan", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "hsec_car_para", "fields": ["unit", "cir", "pir", "cbs", "pbs", "car_type", "car_mode"], "constraints": {"unique": true}, "comment": "主键索引"}]}