{"comment": "table of ldap_server_template", "version": "2.0", "type": "record", "name": "ldap_server_template", "config": {"check_validity": false}, "max_record_count": 8192, "fields": [{"name": "Index", "type": "uint32", "comment": "Index"}, {"name": "ulVSysID", "type": "uint32", "comment": "VsysID"}, {"name": "szSTName", "type": "bytes", "size": 256, "comment": "STName"}, {"name": "ulPrimAuthServerIPAddress", "type": "uint32", "comment": "PrimAuthServerIPAddress"}, {"name": "ulSecAuthServerIPAddress", "type": "uint32", "comment": "SecAuthServerIPAddress"}, {"name": "ulThirdAuthServerIPAddress", "type": "uint32", "comment": "ThirdAuthServerIPAddress"}, {"name": "usPrimAuthServerPort", "type": "uint16", "comment": "PrimAuthServerPort"}, {"name": "usSecAuthServerPort", "type": "uint16", "comment": "SecAuthServerPort"}, {"name": "usThirdAuthServerPort", "type": "uint16", "comment": "ThirdAuthServerPort"}, {"name": "acPrimAuthServerURL", "type": "bytes", "nullable": true, "size": 256, "comment": "PrimAuthServerURL"}, {"name": "acSecAuthServerURL", "type": "bytes", "nullable": true, "size": 256, "comment": "SecAuthServerURL"}, {"name": "acThirdAuthServerURL", "type": "bytes", "nullable": true, "size": 256, "comment": "acThirdAuthServerURL"}, {"name": "ulAppendBaseDNFlag", "type": "uint32", "comment": "AppendBaseDNFlag"}, {"name": "ulAnonymousAdminFlag", "type": "uint32", "comment": "AnonymousAdminFlag"}, {"name": "BaseDN", "type": "record", "nullable": true, "array": true, "size": 16, "comment": "BaseDNInfo", "fields": [{"name": "acBaseDN", "type": "bytes", "nullable": true, "size": 256, "comment": "BaseDN"}]}, {"name": "acUserAttrs", "type": "bytes", "nullable": true, "size": 256, "comment": "UserAttrs"}, {"name": "acGroupAttrs", "type": "bytes", "nullable": true, "size": 256, "comment": "GroupAttrs"}, {"name": "ac<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "bytes", "nullable": true, "size": 512, "comment": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "acMgrDN", "type": "bytes", "nullable": true, "size": 512, "comment": "MgrDN"}, {"name": "acMgrEncryptPW", "type": "bytes", "nullable": true, "size": 128, "comment": "MgrEncryptPW"}, {"name": "acVIPAttrs", "type": "bytes", "nullable": true, "size": 256, "comment": "VIPAttrs"}, {"name": "acMaskAttrs", "type": "bytes", "nullable": true, "size": 256, "comment": "MaskAttrs"}, {"name": "acPhoneAttrs", "type": "bytes", "nullable": true, "size": 256, "comment": "PhoneAttrs"}, {"name": "a<PERSON><PERSON><PERSON>Created", "type": "bytes", "nullable": true, "size": 256, "comment": "WhenCreated"}, {"name": "ulAuthorBindUser", "type": "uint32", "comment": "AuthorBindUser"}, {"name": "stServerStatus", "type": "record", "array": true, "size": 16, "fields": [{"name": "dulLastUpdateTime", "type": "uint64", "comment": "LastUpdateTime"}, {"name": "dulConnectStartTime", "type": "uint64", "comment": "ConnectStartTime"}, {"name": "ulServerStatus", "type": "uint32", "comment": "ServerStatus"}, {"name": "lFd", "type": "int32", "comment": "Fd"}]}, {"name": "ucDeadtime", "type": "uint8", "comment": "Deadtime"}, {"name": "ucSvrType", "type": "uint8", "comment": "SvrType"}, {"name": "ucIfSSL", "type": "bytes", "nullable": true, "size": 16, "comment": "IfSSL"}, {"name": "usUmCount", "type": "uint16", "comment": "UmCount"}, {"name": "acMgrPW", "type": "bytes", "nullable": true, "size": 256, "comment": "MgrPW"}, {"name": "acMgrCipherPW", "type": "bytes", "nullable": true, "size": 256, "comment": "MgrCipherPW"}, {"name": "ucIspUseNum", "type": "uint8", "comment": "IspUseNum"}, {"name": "ucDomainNameInclude", "type": "uint8", "comment": "DomainNameInclude"}, {"name": "uiSTNo", "type": "uint32", "comment": "STNo"}, {"name": "ulLoopBack", "type": "uint32", "comment": "LoopBack"}, {"name": "ulSourceVlan", "type": "uint32", "comment": "SourceVlan"}, {"name": "ulSrcIp", "type": "uint32", "comment": "SrcIp"}, {"name": "szPKIRealm", "type": "bytes", "nullable": true, "size": 256, "comment": "PKIRealm"}, {"name": "patch_reserved", "type": "uint32", "comment": "reserved for patch"}], "keys": [{"name": "ldap_server_template_key", "index": {"type": "primary"}, "node": "ldap_server_template", "fields": ["Index"], "constraints": {"unique": true}, "comment": "key of ldap_server_template"}]}