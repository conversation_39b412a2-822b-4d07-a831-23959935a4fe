{"comment": "开放性ip地址表", "version": "2.0", "type": "record", "name": "oas_ipaddr", "config": {"check_validity": true}, "max_record_count": 1000, "fields": [{"name": "ipType", "type": "uint32", "comment": "ip类型标识"}, {"name": "vrId", "type": "uint32", "comment": "Vs索引"}, {"name": "vrfId", "type": "uint32", "comment": "VpnInstace索引"}, {"name": "index", "type": "uint16", "comment": "用于以后可能的扩展"}, {"name": "family", "type": "uint8", "comment": "v4v6标识"}, {"name": "ip_addr", "type": "fixed", "size": 16, "comment": "ip地址"}, {"name": "<PERSON><PERSON>", "type": "uint8", "comment": "IP的掩码长度"}, {"name": "status_high_prio", "type": "uint8", "default": "0", "comment": "SERVICE高优先级下发状态,1对应成功,如下发错误,错误状态见errcode_high_prio"}, {"name": "status_normal_prio", "type": "uint8", "default": "0", "comment": "SERVICE普通优先级下发状态,1对应成功,如下发错误,错误状态见errcode_normal_prio"}, {"name": "errcode_high_prio", "type": "uint8", "default": "0", "comment": "SERVICE高优先级下发状态错误码"}, {"name": "errcode_normal_prio", "type": "uint8", "default": "0", "comment": "SERVICE普通优先级下发状态错误码"}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "oas_ipaddr", "fields": ["ipType", "vrId", "vrfId", "index", "family", "ip_addr"], "constraints": {"unique": true}, "comment": "根据主键索引"}, {"name": "iptype_hashcluster_key", "index": {"type": "hashcluster"}, "node": "oas_ipaddr", "fields": ["ipType"], "constraints": {"unique": true}, "comment": "根据ip类型索引"}]}