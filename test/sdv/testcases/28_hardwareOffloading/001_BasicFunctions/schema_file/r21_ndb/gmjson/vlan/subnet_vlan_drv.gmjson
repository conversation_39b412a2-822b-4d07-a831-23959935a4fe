{"comment": "subnet vlan acl entry info", "version": "2.0", "type": "record", "name": "subnet_vlan_drv", "config": {"check_validity": false}, "max_record_count": 10240, "fields": [{"name": "vrid", "type": "uint32", "comment": "vrid"}, {"name": "ip", "type": "uint32", "comment": "ip address"}, {"name": "ip_mask", "type": "uint32", "comment": "ip mask value"}, {"name": "ifindex", "type": "uint32", "comment": "ifindex"}, {"name": "entry_id", "type": "uint32", "comment": "acl entry  id"}], "keys": [{"name": "subnet_vlan_drv_pk", "index": {"type": "primary"}, "node": "subnet_vlan_drv", "fields": ["vrid", "ip", "ip_mask", "ifindex"], "constraints": {"unique": true}}]}