{"comment": "ipv4 urpf mode 信息", "version": "2.0", "type": "record", "name": "urpf_mode", "config": {"check_validity": false}, "max_record_count": 64000, "fields": [{"name": "ifIndex", "type": "uint32", "comment": "接口索引"}, {"name": "vrId", "type": "uint32"}, {"name": "proto", "type": "uint8", "comment": "协议版本ipv4或者ipv6"}, {"name": "reserve1", "type": "uint8", "comment": "reserve1"}, {"name": "reserve2", "type": "uint16", "comment": "reserve2"}, {"name": "urpfMode", "type": "uint8", "comment": "urpf模式"}, {"name": "allowDefault", "type": "uint8", "comment": "是否默认路由"}, {"name": "aclNum", "type": "uint16", "comment": "aclNum"}, {"name": "aclGroupId", "type": "uint32", "comment": "aclGroupId"}, {"name": "flags", "type": "uint32", "comment": "flags"}, {"name": "tableSmoothId", "type": "uint32"}, {"name": "time_stamp_create", "type": "time"}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "urpf_mode", "fields": ["ifIndex", "vrId", "proto"], "constraints": {"unique": true}, "comment": "urpfMode主key"}, {"name": "localhash_key", "index": {"type": "hashcluster"}, "node": "urpf_mode", "fields": ["aclGroupId"], "constraints": {"unique": false}, "comment": "localhash索引"}]}