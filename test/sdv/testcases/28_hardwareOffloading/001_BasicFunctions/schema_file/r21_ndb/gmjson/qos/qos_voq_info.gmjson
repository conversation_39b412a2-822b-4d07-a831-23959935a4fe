{"comment": "QOS voq资源分配信息", "version": "2.0", "type": "record", "name": "qos_voq_info", "config": {"check_validity": false}, "max_record_count": 2048, "fields": [{"name": "ifIndex", "type": "uint32", "comment": "映射索引"}, {"name": "internal", "type": "uint32", "comment": "内部端口标志"}, {"name": "slot", "type": "uint32", "comment": "槽位号"}, {"name": "tb", "type": "uint16", "comment": "全局tb"}, {"name": "tp", "type": "uint16", "comment": "全局tb对应tp"}, {"name": "unit", "type": "uint32", "comment": "unit"}, {"name": "port", "type": "uint32", "comment": "port"}, {"name": "voqBase", "type": "uint32", "comment": "VOQ分配基址"}, {"name": "chassisID", "type": "uint32", "comment": "框号"}], "keys": [{"name": "qos_voq_info_pk", "index": {"type": "primary"}, "node": "qos_voq_info", "fields": ["ifIndex", "internal"], "constraints": {"unique": true}, "comment": "KEY1"}, {"name": "tb_key", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "qos_voq_info", "fields": ["tb", "chassisID"], "constraints": {"unique": false}, "comment": "KEY2"}, {"name": "voq_key", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "qos_voq_info", "fields": ["voqBase"], "constraints": {"unique": true}, "comment": "KEY3"}]}