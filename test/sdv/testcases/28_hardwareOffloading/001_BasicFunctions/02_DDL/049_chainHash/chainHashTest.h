/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2012-2018. All rights reserved.
 Description  :chainHash能力补充测试头文件
 Author       : wuxiaochun wx753022
 Modification :
 Date         : 2023/08/08
**************************************************************************** */
#ifndef CHAIN_HASH_TEST_H
#define CHAIN_HASH_TEST_H

#include "t_datacom_lite.h"
#include "jansson.h"

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
GmcConnT *g_connAsync = NULL;
GmcStmtT *g_stmtAsync = NULL;
GmcConnT *g_connAsync1 = NULL;
GmcStmtT *g_stmtAsync1 = NULL;
GmcStmtT *g_stmtAsync2[30] = {0};
char *g_labelConfig = (char *)R"({"isFastReadUncommitted":true, "hash_type": "chained"})";
char *g_labelConfig2 = (char *)R"({"isFastReadUncommitted":false, "hash_type": "chained"})";
char *g_labelName = (char *)"labelTest";
int32_t g_concurrenceNum = 50;

typedef struct TagVertexlabelCfg {
    int32_t startVal;       // 主键或其他非成员索引的起始值
    uint32_t count;         // 主键或其他非成员索引的数量
    int32_t coefficient;    // 字段值生成系数, 通过startVal和coefficient组合生成不同的整形和浮点型字段值
    int32_t expAffectRows;  // 预期的affectRows
    GmcOperationTypeE optType;      // vertex操作类型
} GtVertexLabelCfgT;

int TestGetAffactRows(GmcStmtT *stmt, int32_t expectValue)
{
    int32_t affectRows = 0;
    int ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectValue, affectRows);
    return expectValue == affectRows ? GMERR_OK : 1;
}

int TestCreateLabel(GmcStmtT *stmt, char *schemaPath, char *labelName, char const *configJson = g_labelConfig)
{
    int ret = 0;
    char *testSchema = NULL;

    if (schemaPath) {
        readJanssonFile(schemaPath, &testSchema);
        EXPECT_NE((void *)NULL, testSchema);
    }
    ret = GmcDropVertexLabel(stmt, labelName);
    ret = GmcCreateVertexLabel(stmt, testSchema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret == GMERR_OK) {
        AW_FUN_Log(LOG_INFO, "[INFO]Test create label %s success \n", labelName);
    } else {
        testGmcGetLastError(NULL);
    }
    if (testSchema) {
        free(testSchema);
        testSchema = NULL;
    }
    return ret;
}

#define LABEL_FIXED_SIZE   9
#define STRING_LEN   16
#define BYTES_LEN    256
#define MAX_MASK_LEN_16 1000
#define MAX_MASK_LEN_24 2501000

uint32_t g_valuePk[4] = {0}; // chainHash时不支持的字段值--不支持的字段个数为4
uint32_t g_valueLocalhash[4] = {0};
uint32_t g_pkAvgCollisionRate = 0; // 主键最大冲突链长度
uint32_t g_localhashAvgCollisionRate = 0; // localhash最大冲突链长度
void TestGmsysviewLabelStorageHashIndex(char *labelName, char *pkName, char *localhashName)
{
    char command[512] = {0};
    uint64_t length;
    char tmpBuff[512];
    char str[2][512];
    for (int id = 0; id < 4; id++) {
        g_valuePk[id] = 0;
        g_valueLocalhash[id] = 0;
    }
    int32_t ret = 0;
    int i;
    char const *viewName = "V\\$STORAGE_HASH_INDEX_STAT";
    (void)snprintf(command, 512, "%s/gmsysview -q %s -f LABEL_NAME=%s", g_toolPath, viewName, labelName);
    FILE *pf = popen(command, "r");
    if (pf == NULL) {
        AW_FUN_Log(LOG_ERROR, "popen(%s) error.\n", command);
        AW_MACRO_ASSERT_NE_INT((void *)NULL, pf);
    }
    while (fgets(tmpBuff, sizeof(tmpBuff), pf) != NULL) {
        length = strlen(tmpBuff);
        while (length > 0 && (tmpBuff[length - 1] == '\n' || tmpBuff[length - 1] == '\r')) {
            tmpBuff[length - 1] = '\0';
            --length;
        }
        (void)sscanf(tmpBuff, "%s %s", str[0], str[1]);
        if (str[0][0] == ' ' || str[1][0] == '\0') {
            continue;
        }
        if (strcmp(str[0], "UNIQUEINDEX_NAME:") == 0) {
            if (strcmp(str[1], pkName) == 0) {
                while (fgets(tmpBuff, sizeof(tmpBuff), pf) != NULL) {
                    length = strlen(tmpBuff);
                    while (length > 0 && (tmpBuff[length - 1] == '\n' || tmpBuff[length - 1] == '\r')) {
                        tmpBuff[length - 1] = '\0';
                        --length;
                    }
                    (void)sscanf(tmpBuff, "%s %s", str[0], str[1]);
                    if (str[0][0] == ' ' || str[1][0] == '\0') {
                        continue;
                    }
                    if (strcmp(str[0], "SEGMENT_NUM:") == 0) {
                        if (str[1]) {
                            g_valuePk[0] = atoi(str[1]);
                        } else {
                            AW_FUN_Log(LOG_ERROR, "SEGMENT_NUM is NULL %s %s\n", str[0], str[1]);
                            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, 1);
                        }
                    } else if (strcmp(str[0], "PAGE_COUNT:") == 0) {
                        if (str[1]) {
                            g_valuePk[1] = atoi(str[1]);
                        } else {
                            AW_FUN_Log(LOG_ERROR, "PAGE_COUNT is NULL %s %s\n", str[0], str[1]);
                            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, 1);
                        }
                    } else if (strcmp(str[0], "PER_PAGE_SIZE:") == 0) {
                        if (str[2]) {
                            g_valuePk[2] = atoi(str[1]);
                        } else {
                            AW_FUN_Log(LOG_ERROR, "PER_PAGE_SIZE is NULL %s %s\n", str[0], str[1]);
                            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, 1);
                        }
                    } else if (strcmp(str[0], "SCALE_IN_COUNT:") == 0) {
                        if (str[1]) {
                            g_valuePk[3] = atoi(str[1]);
                        } else {
                            AW_FUN_Log(LOG_ERROR, "SCALE_IN_COUNT is NULL %s %s\n", str[0], str[1]);
                            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, 1);
                        }
                        break;
                    }
                }
            } else if (strcmp(str[1], localhashName) == 0) {
                while (fgets(tmpBuff, sizeof(tmpBuff), pf) != NULL) {
                    length = strlen(tmpBuff);
                    while (length > 0 && (tmpBuff[length - 1] == '\n' || tmpBuff[length - 1] == '\r')) {
                        tmpBuff[length - 1] = '\0';
                        --length;
                    }
                    (void)sscanf(tmpBuff, "%s %s", str[0], str[1]);
                    if (str[0][0] == ' ' || str[1][0] == '\0') {
                        continue;
                    }
                    if (strcmp(str[0], "SEGMENT_NUM:") == 0) {
                        if (str[1]) {
                            g_valueLocalhash[0] = atoi(str[1]);
                        } else {
                            AW_FUN_Log(LOG_ERROR, "SEGMENT_NUM is NULL %s %s\n", str[0], str[1]);
                            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, 1);
                        }
                    } else if (strcmp(str[0], "PAGE_COUNT:") == 0) {
                        if (str[1]) {
                            g_valueLocalhash[1] = atoi(str[1]);
                        } else {
                            AW_FUN_Log(LOG_ERROR, "PAGE_COUNT is NULL %s %s\n", str[0], str[1]);
                            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, 1);
                        }
                    } else if (strcmp(str[0], "PER_PAGE_SIZE:") == 0) {
                        if (str[2]) {
                            g_valueLocalhash[2] = atoi(str[1]);
                        } else {
                            AW_FUN_Log(LOG_ERROR, "PER_PAGE_SIZE is NULL %s %s\n", str[0], str[1]);
                            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, 1);
                        }
                    } else if (strcmp(str[0], "SCALE_IN_COUNT:") == 0) {
                        if (str[1]) {
                            g_valueLocalhash[3] = atoi(str[1]);
                        } else {
                            AW_FUN_Log(LOG_ERROR, "SCALE_IN_COUNT is NULL %s %s\n", str[0], str[1]);
                            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, 1);
                        }
                        break;
                    }
                }
            }
        }
    }
    for (int j = 0; j < 4; j++) {
        printf("g_valuePk[%d] = %d\n", j, g_valuePk[j]);
        printf("g_valueLocalhash[%d] = %d\n", j, g_valueLocalhash[j]);
    }
    ret = pclose(pf);
    if (ret != 0) {
        AW_FUN_Log(LOG_ERROR, "pclose(%d) error.\n", ret);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void TestGmsysviewLabelStorageIndexGlobal()
{
    char command[512] = {0};
    uint64_t length;
    char tmpBuff[512];
    char str[2][512];
    g_pkAvgCollisionRate = 0;
    g_localhashAvgCollisionRate = 0;
    int32_t ret = 0;
    int i;
    char const *viewName = "V\\$STORAGE_INDEX_GLOBAL_STAT";
    (void)snprintf(command, 512, "%s/gmsysview -q %s", g_toolPath, viewName);
    FILE *pf = popen(command, "r");
    if (pf == NULL) {
        AW_FUN_Log(LOG_ERROR, "popen(%s) error.\n", command);
        AW_MACRO_ASSERT_NE_INT((void *)NULL, pf);
    }
    while (fgets(tmpBuff, sizeof(tmpBuff), pf) != NULL) {
        length = strlen(tmpBuff);
        while (length > 0 && (tmpBuff[length - 1] == '\n' || tmpBuff[length - 1] == '\r')) {
            tmpBuff[length - 1] = '\0';
            --length;
        }
        (void)sscanf(tmpBuff, "%s %s", str[0], str[1]);
        if (str[0][0] == ' ' || str[1][0] == '\0') {
            continue;
        }
        if (strcmp(str[0], "INDEX_PK_AVG_COLLISION_RATE:") == 0) {
            if (str[1]) {
                g_pkAvgCollisionRate = atoi(str[1]);
            } else {
                AW_FUN_Log(LOG_ERROR, "SEGMENT_NUM is NULL %s %s\n", str[0], str[1]);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, 1);
            }
        } else if (strcmp(str[0], "INDEX_LOCALHASH_AVG_COLLISION_RATE:") == 0) {
            if (str[1]) {
                g_localhashAvgCollisionRate = atoi(str[1]);
            } else {
                AW_FUN_Log(LOG_ERROR, "PAGE_COUNT is NULL %s %s\n", str[0], str[1]);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, 1);
            }
        }
    }
    printf("g_pkAvgCollisionRate = %d\n", g_pkAvgCollisionRate);
    printf("g_localhashAvgCollisionRate = %d\n", g_localhashAvgCollisionRate);
    ret = pclose(pf);
    if (ret != 0) {
        AW_FUN_Log(LOG_ERROR, "pclose(%d) error.\n", ret);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void ReadMemory(char *fileName, uint32_t *memoryArr)
{
    FILE *fp = NULL;
    char buff[512] = {0};
    char buf[512] = {0};
    const char delims[32] = ":";
    const char delims2[32] = "] MB";
    const char delims3[32] = "] KB";
    const char delims4[32] = "] Byte";
    char *pSave = NULL;
    char *temp = NULL;

    uint32_t memoryPhy[3] = {0};
    uint32_t memoryAlloc[3] = {0};
    if (fileName) {
        fp = fopen(fileName, "r");
        if (fp) {
            while (fgets(buff, 512, fp)) {
                temp = strtok_r(buff, delims, &pSave);
                if (strcmp(temp, "  STORAGE_MEM_USED") == 0) {
                    for (int i = 0; i < 3; i++) {
                        if (temp != NULL) {
                            if (i == 0) {
                                temp = strtok_r(NULL, "[", &pSave);
                                temp = strtok_r(NULL, delims2, &pSave);
                                memoryPhy[i] = atoi(temp);
                            } else if (i == 1) {
                                temp = strtok_r(NULL, "[", &pSave);
                                temp = strtok_r(NULL, delims3, &pSave);
                                memoryPhy[i] = atoi(temp);
                            } else if (i == 2) {
                                temp = strtok_r(NULL, "[", &pSave);
                                temp = strtok_r(NULL, delims4, &pSave);
                                memoryPhy[i] = atoi(temp);
                            }
                        }
                    }
                    break;
                }
            }
            fclose(fp);
        }
    }
    (memoryArr[0]) = memoryPhy[0];
    (memoryArr[1]) = memoryPhy[1];
    (memoryArr[2]) = memoryPhy[2];
    AW_FUN_Log(LOG_INFO, "STORAGE_MEM_USED:%d MB %d KB %d Bytes\n", memoryPhy[0], memoryPhy[1], memoryPhy[2]);
}

char const *g_viewname = "V\\$COM_MEM_SUMMARY";
// 使用命令读取共享内存的占用内存 和释放内存
void SysviewReadMemorySummary(char *fileName, uint32_t *memoryArr)
{
    char command[512];
    (void)snprintf(command, 512, "%s/gmsysview -q %s >%s", g_toolPath, g_viewname, fileName);
    int ret = system(command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ReadMemory(fileName, memoryArr);
}

void TestVertexT1SetPk(GmcStmtT *stmt, int64_t i)
{
    int ret = 0;
    int64_t f0Value = i;
    ret = GmcSetVertexProperty(stmt, (char *)"F0", GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestVertexT1SetProperty(GmcStmtT *stmt, int64_t i, char *stringValue, bool isDefaultValue = true,
    bool isSimple = false)
{
    int ret = 0;
    int64_t f0Value = i;
    uint64_t f1Value = i;
    int32_t f2Value = i;
    uint32_t f3Value = i;
    int16_t f4Value = i % 32768;
    uint16_t f5Value = i % 65536;
    uint64_t f7Value = i;
    uint32_t vrid = 0;
    uint32_t vrfIndex = 0;
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    uint8_t f13Value = i & 0xf;
    ret = GmcSetVertexProperty(stmt, (char *)"F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F4", GMC_DATATYPE_INT16, &f4Value, sizeof(int16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F5", GMC_DATATYPE_UINT16, &f5Value, sizeof(uint16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F7", GMC_DATATYPE_TIME, &f7Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    if (!isDefaultValue) {
        uint8_t fixedValue[LABEL_FIXED_SIZE] = {0};
        for (int j = 0; j < LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = j;
        }
        uint8_t f9Value = i % 31;
        uint16_t f10Value = i % 1023;
        ret = GmcSetVertexProperty(stmt, (char *)"F8", GMC_DATATYPE_FIXED, fixedValue, LABEL_FIXED_SIZE);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, (char *)"F9", GMC_DATATYPE_BITFIELD8, &f9Value, sizeof(uint8_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, (char *)"F10", GMC_DATATYPE_BITFIELD16, &f10Value, sizeof(uint16_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcSetVertexProperty(stmt, (char *)"F3", GMC_DATATYPE_UINT32, &vrid, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F11", GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    if (i <= MAX_MASK_LEN_16) {
        destIpAddr = ((i + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if (i > MAX_MASK_LEN_16 && i <= MAX_MASK_LEN_24) {
        destIpAddr = ((i + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((i + 2));
        maskLen = ((32) & 0xff);
    }
    ret = GmcSetVertexProperty(stmt, (char *)"F12", GMC_DATATYPE_UINT32, &destIpAddr, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, (char *)"F6", GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, (char *)"F13", GMC_DATATYPE_BITFIELD8, &f13Value, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    if (!isSimple) {
        ret = GmcSetVertexProperty(stmt, (char *)"F14", GMC_DATATYPE_STRING, stringValue, strlen(stringValue));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void TestVertexT1UpdateSetProperty(GmcStmtT *stmt, int64_t i, char *stringValue, bool isDefaultValue = true,
    bool isSimple = false)
{
    int ret = 0;
    int64_t f0Value = i;
    uint64_t f1Value = i;
    int32_t f2Value = i;
    uint32_t f3Value = i;
    int16_t f4Value = i % 32768;
    uint16_t f5Value = i% 65536;
    uint64_t f7Value = i;
    uint8_t f13Value = i & 0xf;
    ret = GmcSetVertexProperty(stmt, (char *)"F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F4", GMC_DATATYPE_INT16, &f4Value, sizeof(int16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F5", GMC_DATATYPE_UINT16, &f5Value, sizeof(uint16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F7", GMC_DATATYPE_TIME, &f7Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint8_t fixedValue[LABEL_FIXED_SIZE] = {0};
    uint8_t f9Value;
    uint16_t f10Value;
    if (!isDefaultValue) {
        for (int j = 0; j < LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = j;
        }
        f9Value = i % 31;
        f10Value = i % 1023;
    } else {
        for (int j = 0; j < LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = 0xff;
        }
        f9Value = 0x1f;
        f10Value = 0x3ff;
    }
    ret = GmcSetVertexProperty(stmt, (char *)"F8", GMC_DATATYPE_FIXED, fixedValue, LABEL_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F9", GMC_DATATYPE_BITFIELD8, &f9Value, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F10", GMC_DATATYPE_BITFIELD16, &f10Value, sizeof(uint16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, (char *)"F13", GMC_DATATYPE_BITFIELD8, &f13Value, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    if (!isSimple) {
        ret = GmcSetVertexProperty(stmt, (char *)"F14", GMC_DATATYPE_STRING, stringValue, strlen(stringValue));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void TestVertexT1LpmIndexSet(GmcStmtT *stmt, int64_t value)
{
    int ret = 0;
    uint32_t vrid = 0;
    uint32_t vrfIndex = 0;
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    int64_t uiVrIndex = value;
    ret = GmcSetIndexKeyName(stmt, "lpm4_key");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vrid, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    if (uiVrIndex <= MAX_MASK_LEN_16) {
        destIpAddr = ((uiVrIndex + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if (uiVrIndex > MAX_MASK_LEN_16 && uiVrIndex <= MAX_MASK_LEN_24) {
        destIpAddr = ((uiVrIndex + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((uiVrIndex + 2));
        maskLen = ((32) & 0xff);
    }
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &destIpAddr, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestVertexT1SetLpmProperty(GmcStmtT *stmt, int64_t i)
{
    int32_t ret = 0;
    uint32_t vrid = 0;
    uint32_t vrfIndex = 0;
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    ret = GmcSetVertexProperty(stmt, (char *)"F3", GMC_DATATYPE_UINT32, &vrid, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F11", GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    if (i <= MAX_MASK_LEN_16) {
        destIpAddr = ((i + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if (i > MAX_MASK_LEN_16 && i <= MAX_MASK_LEN_24) {
        destIpAddr = ((i + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((i + 2));
        maskLen = ((32) & 0xff);
    }
    ret = GmcSetVertexProperty(stmt, (char *)"F12", GMC_DATATYPE_UINT32, &destIpAddr, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, (char *)"F6", GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestVertexT1PkIndexSet(GmcStmtT *stmt, int64_t i)
{
    int ret = 0;
    int64_t f0Value = i;
    ret = GmcSetIndexKeyName(stmt, "primary_key");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestVertexT1LocalhashIndexSet(GmcStmtT *stmt, int64_t i)
{
    int ret = 0;
    int16_t f4Value = i;
    uint16_t f5Value = i;
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT16, &f4Value, sizeof(int16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT16, &f5Value, sizeof(uint16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestVertexT1HashclusterIndexSet(GmcStmtT *stmt, int64_t i)
{
    int ret = 0;
    uint64_t f1Value = i;
    int32_t f2Value = i;
    ret = GmcSetIndexKeyName(stmt, "hashcluster_unique_key");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &f1Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestVertexT1LocalIndexSet(GmcStmtT *stmt, int64_t i)
{
    int ret = 0;
    uint32_t f3Value = 0;
    ret = GmcSetIndexKeyName(stmt, "local_key");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f3Value, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestVertexT1LocalIndexRangeSet(GmcStmtT *stmt, int64_t startValue, int64_t endValue)
{
    int ret = 0;
    unsigned int arrLen = 1;
    uint32_t lValue = startValue;
    uint32_t rValue = endValue;

    GmcPropValueT *leftKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    if (leftKeyProps == NULL) {
        AW_FUN_Log(LOG_ERROR, "leftKeyProps is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    leftKeyProps[0].type = GMC_DATATYPE_UINT32;
    leftKeyProps[0].value = &lValue;
    leftKeyProps[0].size = sizeof(uint32_t);

    GmcPropValueT *rightKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    if (rightKeyProps == NULL) {
        AW_FUN_Log(LOG_ERROR, "rightKeyProps is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    rightKeyProps[0].type = GMC_DATATYPE_UINT32;
    rightKeyProps[0].value = &rValue;
    rightKeyProps[0].size = sizeof(uint32_t);

    GmcRangeItemT items[arrLen];
    items[0].lValue = &leftKeyProps[0];
    items[0].rValue = &rightKeyProps[0];
    items[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].order = GMC_ORDER_ASC;
    ret = GmcSetKeyRange(stmt, items, arrLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "local_key");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(leftKeyProps);
    free(rightKeyProps);
}

void TestVertexT1UpdateGetPropertyByName(GmcStmtT *stmt, int64_t i, char *stringValue, bool isDefaultValue = true,
    bool isSimple = false)
{
    int ret = 0;
    bool isNull = 0;
    uint64_t f1Value = i;
    int32_t f2Value = i;
    int16_t f4Value = i % 32768;
    uint16_t f5Value = i % 65536;
    uint64_t f7Value = i;

    uint64_t f1Value2 = i;
    int32_t f2Value2 = i;
    int16_t f4Value2 = i % 32768;
    uint16_t f5Value2 = i % 65536;
    uint64_t f7Value2 = i;

    uint8_t fixedValue[LABEL_FIXED_SIZE] = {0};
    uint8_t f9Value = 0;
    uint16_t f10Value = 0;
    uint8_t fixedValueR[LABEL_FIXED_SIZE] = {0};
    uint8_t f9ValueR = 0;
    uint16_t f10ValueR = 0;
    uint8_t f13Value = i & 0xf;
    uint8_t f13ValueR = 0;
    char f14Str[130] = {0};
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F1", &f1Value2, sizeof(uint64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f1Value, f1Value2);

    ret = GmcGetVertexPropertyByName(stmt, (char *)"F2", &f2Value2, sizeof(int32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f2Value, f2Value2);

    ret = GmcGetVertexPropertyByName(stmt, (char *)"F4", &f4Value2, sizeof(int16_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f4Value, f4Value2);

    ret = GmcGetVertexPropertyByName(stmt, (char *)"F5", &f5Value2, sizeof(uint16_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f5Value, f5Value2);

    if (!isDefaultValue) {
        for (int j = 0; j < LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = j;
        }
        f9Value = i % 31;
        f10Value = i % 1023;
    } else {
        for (int j = 0; j < LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = 0xff;
        }
        f9Value = 0x1f;
        f10Value = 0x3ff;
    }
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F8", fixedValueR, LABEL_FIXED_SIZE, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = memcmp(fixedValue, fixedValueR, LABEL_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetVertexPropertyByName(stmt, (char *)"F9", &f9ValueR, sizeof(uint8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(f9Value, f9ValueR);
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F10", &f10ValueR, sizeof(uint16_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(f10Value, f10ValueR);
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F13", &f13ValueR, sizeof(uint8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(f13Value, f13ValueR);
    if (!isSimple) {
        ret = GmcGetVertexPropertyByName(stmt, (char *)"F14", f14Str, sizeof(f14Str), &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = strcmp(stringValue, (char *)f14Str);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void TestVertexT1GetLpmProperty(GmcStmtT *stmt, int64_t i)
{
    int ret = 0;
    bool isNull = false;
    uint32_t vrid = 0;
    uint32_t vrfIndex = 0;
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    uint32_t vrid2 = 0;
    uint32_t vrfIndex2 = 0;
    uint32_t destIpAddr2 = 0;
    uint8_t maskLen2 = 0;
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F3", &vrid2, sizeof(uint32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(vrid, vrid2);
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F11", &vrfIndex2, sizeof(uint32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(vrfIndex, vrfIndex2);

    if (i <= MAX_MASK_LEN_16) {
        destIpAddr = ((i + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if (i > MAX_MASK_LEN_16 && i <= MAX_MASK_LEN_24) {
        destIpAddr = ((i + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((i + 2));
        maskLen = ((32) & 0xff);
    }
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F12", &destIpAddr2, sizeof(uint32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(destIpAddr, destIpAddr2);
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F6", &maskLen2, sizeof(uint8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(maskLen, maskLen2);
}

void TestVertexTableWrite(GmcStmtT *stmt, GtVertexLabelCfgT vertexCfg, char *stringValue, bool isDefaultValue = true)
{
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t endValue = vertexCfg.count;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t coefficient = vertexCfg.coefficient;
    GmcOperationTypeE optType = vertexCfg.optType;
    int ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, optType);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = startPkVal; i < endValue; i++) {
        if (optType == GMC_OPERATION_MERGE) {
            TestVertexT1PkIndexSet(stmt, i);
        } else {
            TestVertexT1SetPk(stmt, i);
        }
        TestVertexT1SetProperty(stmt, i, stringValue, isDefaultValue);
        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestGetAffactRows(stmt, expAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

int TestVertexTUpdate(GmcStmtT *stmt, GtVertexLabelCfgT vertexCfg, uint32_t keyId, char *stringValue,
                      bool isDefaultValue = true, int32_t updateValue = 0)
{
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t endValue = vertexCfg.count;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t coefficient = vertexCfg.coefficient;
    GmcOperationTypeE optType = vertexCfg.optType;
    int ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, optType);
    AW_FUN_Log(LOG_INFO, "update: labelName: %s optType: %d\n", g_labelName, optType);
    RETURN_IFERR(ret);
    for (int i = startPkVal; i < endValue; i++) {
        if (optType == GMC_OPERATION_MERGE) {
            TestVertexT1PkIndexSet(stmt, i);
        } else {
            if (keyId == 0) {
            TestVertexT1PkIndexSet(stmt, i);
            } else if (keyId == 1) {
                TestVertexT1HashclusterIndexSet(stmt, i + coefficient);
            } else if (keyId == 2) {
                TestVertexT1LocalhashIndexSet(stmt, i + coefficient);
            } else if (keyId == 3) {
                TestVertexT1LocalIndexSet(stmt, i);
            } else {
                AW_FUN_Log(LOG_ERROR, "keyId error!! keyId = %d", keyId);
            }
        }
        TestVertexT1UpdateSetProperty(stmt, i + updateValue, stringValue, isDefaultValue);
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        int ret1 = TestGetAffactRows(stmt, expAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);
    }
}

int TestVertexTableRead(GmcStmtT *stmt, GtVertexLabelCfgT vertexCfg, uint32_t keyId, char *stringValue,
                        bool isDefaultValue = true, int32_t updateValue = 0, bool isSimple = false)
{
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t endValue = vertexCfg.count;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t localFlag = 0;
    uint32_t fetchNum = 0;
    bool isFinish = false;
    int ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    AW_FUN_Log(LOG_INFO, "g_labelName = %s keyId = %d line: %d", g_labelName, keyId, __LINE__);
    RETURN_IFERR(ret);
    for (int i = startPkVal; i < endValue; i++) {
        if (keyId == 0) {
        TestVertexT1PkIndexSet(stmt, i);
        } else if (keyId == 1) {
            TestVertexT1HashclusterIndexSet(stmt, i + updateValue);
        } else if (keyId == 2) {
            TestVertexT1LocalhashIndexSet(stmt, i + updateValue);
        } else if (keyId == 3) {
            TestVertexT1LocalIndexSet(stmt, i);
        } else if (keyId == 4) {
            TestVertexT1LpmIndexSet(stmt, i);
        } else {
            AW_FUN_Log(LOG_ERROR, "keyId error!! keyId = %d", keyId);
        }
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        ret = GmcFetch(stmt, &isFinish);
        RETURN_IFERR(ret);
        if (keyId == 3 && localFlag == 0) {
            localFlag = 1;
            int64_t f0Value = 0;
            bool isNull = false;
            bool newFieldIsNull[2] = {true};
            while (!isFinish) {
                fetchNum++;
                ret = GmcGetVertexPropertyByName(stmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                RETURN_IFERR(ret);
                if (f0Value >= startPkVal && f0Value < endValue) {
                    TestVertexT1UpdateGetPropertyByName(stmt, f0Value + updateValue, stringValue,
                                                        isDefaultValue, isSimple);
                    TestVertexT1GetLpmProperty(stmt, f0Value);
                }
                ret = GmcFetch(stmt, &isFinish);
                RETURN_IFERR(ret);
            }
            AW_MACRO_EXPECT_EQ_INT(expAffectRows, fetchNum);
            fetchNum = 0;
            return 0;
        } else if (keyId != 3) {
            TestVertexT1UpdateGetPropertyByName(stmt, i + updateValue, stringValue, isDefaultValue, isSimple);
            TestVertexT1GetLpmProperty(stmt, i);
            ret = GmcFetch(stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(true, isFinish);
            return ret;
        }
    }
}

int TestVertexTableReadNoData(GmcStmtT *stmt, GtVertexLabelCfgT vertexCfg, uint32_t keyId, int32_t updateValue = 0)
{
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t endValue = vertexCfg.count;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t localFlag = 0;
    uint32_t fetchNum = 0;
    bool isFinish = false;
    int ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    AW_FUN_Log(LOG_INFO, "g_labelName = %s keyId = %d line: %d", g_labelName, keyId, __LINE__);
    RETURN_IFERR(ret);
    for (int i = startPkVal; i < endValue; i++) {
        if (keyId == 0) {
        TestVertexT1PkIndexSet(stmt, i);
        } else if (keyId == 1) {
            TestVertexT1HashclusterIndexSet(stmt, i + updateValue);
        } else if (keyId == 2) {
            TestVertexT1LocalhashIndexSet(stmt, i + updateValue);
        } else if (keyId == 3) {
            TestVertexT1LocalIndexSet(stmt, i);
        } else if (keyId == 4) {
            TestVertexT1LpmIndexSet(stmt, i);
        } else {
            AW_FUN_Log(LOG_ERROR, "keyId error!! keyId = %d", keyId);
        }
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        ret = GmcFetch(stmt, &isFinish);
        RETURN_IFERR(ret);
        if (keyId == 3 && localFlag == 0) {
            localFlag = 1;
            AW_MACRO_EXPECT_EQ_INT(true, isFinish);
            return ret;
        } else if (keyId != 3) {
            AW_MACRO_EXPECT_EQ_INT(true, isFinish);
            return ret;
        }
    }
}

void TestVertexTDelete(GmcStmtT *stmt, char *labelName, uint32_t keyId, int32_t startValue, int32_t endValue,
                       int64_t updateValue = 0)
{
    bool isFinish = true;
    int ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = startValue; i < endValue; i++) {
        if (keyId == 0) {
            TestVertexT1PkIndexSet(stmt, i);
        } else if (keyId == 1) {
            TestVertexT1HashclusterIndexSet(stmt, i + updateValue);
        } else if (keyId == 2) {
            TestVertexT1LocalhashIndexSet(stmt, i + updateValue);
        } else if (keyId == 3) {
            TestVertexT1LocalIndexRangeSet(stmt, i, endValue);
            ret = GmcExecute(stmt);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            break;
        } else {
            AW_FUN_Log(LOG_DEBUG, "keyId = %d is error!!\n", keyId);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, 1);
        }
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_DEBUG, "i=%d\n", i);
        }
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

char *g_labelName2 = (char *)"specialLabel";

typedef struct TagSpeciallabelCfg {
    int32_t startVal;       // 主键或其他非成员索引的起始值
    uint32_t count;         // 主键或其他非成员索引的数量
    int32_t coefficient;    // 字段值生成系数, 通过startVal和coefficient组合生成不同的整形和浮点型字段值
    int32_t expAffectRows;    // 预期的affectRows
    int32_t threadId;    // 线程Id
    uint16_t t1VCount;
    uint16_t t2VCount;
    GmcOperationTypeE optType;
} GtSpeciallabelCfgT;

#pragma pack(1)
typedef struct TagSpeciallabelT2VVertex {
    uint32_t v1;
    uint32_t v2;
    uint8_t v3[2];
    uint16_t v4Len;
    uint8_t *v4;
} GtSpeciallabelT2VVertexT;
#pragma pack()

#pragma pack(1)
typedef struct TagSpeciallabelT1VVertex {
    uint32_t v1;
    uint32_t v2;
    uint8_t v3[2];
    uint16_t v4Len;
    uint8_t *v4;
    uint16_t t2VCount;
    GtSpeciallabelT2VVertexT *t2V;
} GtSpeciallabelT1VVertexT;
#pragma pack()

#pragma pack(1)
typedef struct TagSpeciallabelVertex {
    int64_t f0;
    uint64_t f1;
    int32_t f2;
    uint32_t f3;
    int16_t f4;
    uint16_t f5;
    uint8_t f6;
    uint64_t f7;
    uint8_t f8[9];
    uint8_t f9 : 5;
    uint8_t res1 : 3;
    uint16_t f10 : 10;
    uint16_t res2 : 6;
    uint32_t f11;
    uint32_t f12;
    uint8_t f13 : 4;
    uint8_t res3 : 4;
    uint16_t f14Len;
    uint8_t *f14;
    uint16_t t1VCount;
    GtSpeciallabelT1VVertexT *t1V;
} GtSpeciallabelVertexT;
#pragma pack()

void GtSpeciallabelStructSetPk(GtSpeciallabelVertexT *vertex, int64_t value)
{
    vertex->f0 = value;
}

void GtSpeciallabelStructSetHashcluster(GtSpeciallabelVertexT *vertex, int64_t value, int64_t updateValue = 0)
{
    uint64_t f1Value = value + updateValue;
    int32_t f2Value = value  + updateValue;
    vertex->f1 = f1Value;
    vertex->f2 = f2Value;
}

void GtSpeciallabelStructSetLocalhash(GtSpeciallabelVertexT *vertex, int64_t value, int64_t updateValue = 0)
{
    int16_t f4Value = (value + updateValue) % 32768;
    uint16_t f5Value = (value + updateValue) % 65536;
    vertex->f4 = f4Value;
    vertex->f5 = f5Value;
}

void GtSpeciallabelStructSetLpm4(GtSpeciallabelVertexT *vertex, int64_t value)
{
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    vertex->f3 = 0;
    vertex->f11 = 0;
    if ((value) <= MAX_MASK_LEN_16) {
        destIpAddr = ((value + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if ((value) > MAX_MASK_LEN_16 && (value) <= MAX_MASK_LEN_24) {
        destIpAddr = ((value + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((value + 2));
        maskLen = ((32) & 0xff);
    }
    vertex->f12 = destIpAddr;
    vertex->f6 = maskLen;
}

void GtSpeciallabelStructSetLocal(GtSpeciallabelVertexT *vertex, int64_t value)
{
    uint32_t f3Value = 0;
    vertex->f3 = f3Value;
}

void GtSpeciallabelStructFreeT2V(GtSpeciallabelT2VVertexT *vertex)
{
    if (vertex->v4) {
        free(vertex->v4);
    }
}

void GtSpeciallabelStructFreeT1V(GtSpeciallabelT1VVertexT *vertex)
{
    if (vertex->v4) {
        free(vertex->v4);
    }
    for (int32_t i = 0; i < vertex->t2VCount; i++) {
        GtSpeciallabelStructFreeT2V(&(vertex->t2V[i]));
    }
}

void GtSpeciallabelStructFree(GtSpeciallabelVertexT *vertex)
{
    if (vertex->f14) {
        free(vertex->f14);
    }
    for (int32_t i = 0; i < vertex->t1VCount; i++) {
        GtSpeciallabelStructFreeT1V(&(vertex->t1V[i]));
    }
}

void GtSpeciallabelStructSetT2VProperty(GtSpeciallabelT2VVertexT *vertex, int32_t value, char *stringValue)
{
    vertex->v1 = value;
    vertex->v2 = value;
    uint8_t bitmap[2] = {0x55, 0x55};
    (void)memcpy(vertex->v3, bitmap, sizeof(vertex->v3));

    vertex->v4Len = strlen(stringValue) + 1;
    if (!vertex->v4) {
        vertex->v4 = (uint8_t *)malloc(vertex->v4Len);
    }
    if (vertex->v4 == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex->v4 is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)vertex->v4, vertex->v4Len, "%s", stringValue);
}

void GtSpeciallabelStructSetT1VProperty(GtSpeciallabelT1VVertexT *vertex, int32_t value, char *stringValue,
                                        uint16_t t2VCount)
{
    vertex->v1 = value;
    vertex->v2 = value;
    uint8_t bitmap[2] = {0x55, 0x55};
    memcpy(vertex->v3, bitmap, sizeof(vertex->v3));

    vertex->v4Len = strlen(stringValue) + 1;
    if (!vertex->v4) {
        vertex->v4 = (uint8_t *)malloc(vertex->v4Len);
    }
    if (vertex->v4 == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex->v4 is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)vertex->v4, vertex->v4Len, "%s", stringValue);
    vertex->t2VCount = t2VCount;
    for (int32_t i = 0; i < vertex->t2VCount; i++) {
        GtSpeciallabelStructSetT2VProperty(&vertex->t2V[i], value, stringValue);
    }
}

void GtSpeciallabelStructSetProperty(GtSpeciallabelVertexT *vertex, int64_t value, uint16_t t1VCount,
                                     uint16_t t2VCount, char *bytesValue, char *stringValue, bool isDefaultValue = true,
                                     int32_t updateValue = 0)
{
    vertex->f1 = value + updateValue;
    vertex->f2 = value + updateValue;

    vertex->f4 = (value + updateValue) % 32768;
    vertex->f5 = (value + updateValue) % 65536;
    vertex->f7 = value + updateValue;
    if (!isDefaultValue) {
        for (int j = 0; j < LABEL_FIXED_SIZE; j++) {
            vertex->f8[j] = j;
        }
        vertex->f9 = (value + updateValue) % 31;
        vertex->f10 = (value + updateValue) % 1023;
    } else {
        for (int j = 0; j < LABEL_FIXED_SIZE; j++) {
            vertex->f8[j] = 0xff;
        }
        vertex->f9 = 0x1f;
        vertex->f10 = 0x3ff;
    }
    vertex->f13 = (value + updateValue) & 0xf;
    vertex->f14Len = strlen(bytesValue);
    if (!vertex->f14) {
        vertex->f14 = (uint8_t *)malloc(vertex->f14Len + 1);
    }
    if (vertex->f14 == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex->f14 is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)vertex->f14, vertex->f14Len + 1, "%s", bytesValue);
    vertex->t1VCount = t1VCount;
    for (int32_t i = 0; i < vertex->t1VCount; i++) {
        GtSpeciallabelStructSetT1VProperty(&vertex->t1V[i], value, stringValue, t2VCount);
    }
}

void GtSpeciallabelGetNode(GmcStmtT *stmt, GmcNodeT **root, GmcNodeT **t1V)
{
    GmcNodeT *Root, *t1;
    int32_t ret = GmcGetRootNode(stmt, &Root);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeGetChild(Root, "T1V", &t1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    *root = Root;
    *t1V = t1;
}

void GtSpeciallabelSetLpmProperty(GmcNodeT *node, int64_t i)
{
    int32_t ret = 0;
    uint32_t vrid = 0;
    uint32_t vrfIndex = 0;
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    ret = GmcNodeSetPropertyByName(node, (char *)"F3", GMC_DATATYPE_UINT32, &vrid, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F11", GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    if (i <= MAX_MASK_LEN_16) {
        destIpAddr = ((i + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if (i > MAX_MASK_LEN_16 && i <= MAX_MASK_LEN_24) {
        destIpAddr = ((i + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((i + 2));
        maskLen = ((32) & 0xff);
    }
    ret = GmcNodeSetPropertyByName(node, (char *)"F12", GMC_DATATYPE_UINT32, &destIpAddr, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F6", GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestSpecialTUpdateSetProperty(GmcNodeT *node, int64_t i, char *bytesValue, bool isDefaultValue = true)
{
    int ret = 0;
    int64_t f0Value = i;
    uint64_t f1Value = i;
    int32_t f2Value = i;
    uint32_t f3Value = i;
    int16_t f4Value = i % 32768;
    uint16_t f5Value = i% 65536;
    uint64_t f7Value = i;
    uint8_t f13Value = i & 0xf;
    ret = GmcNodeSetPropertyByName(node, (char *)"F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F4", GMC_DATATYPE_INT16, &f4Value, sizeof(int16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F5", GMC_DATATYPE_UINT16, &f5Value, sizeof(uint16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F7", GMC_DATATYPE_TIME, &f7Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint8_t fixedValue[LABEL_FIXED_SIZE] = {0};
    uint8_t f9Value;
    uint16_t f10Value;
    if (!isDefaultValue) {
        for (int j = 0; j < LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = j;
        }
        f9Value = i % 31;
        f10Value = i % 1023;
    } else {
        for (int j = 0; j < LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = 0xff;
        }
        f9Value = 0x1f;
        f10Value = 0x3ff;
    }
    ret = GmcNodeSetPropertyByName(node, (char *)"F8", GMC_DATATYPE_FIXED, fixedValue, LABEL_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F9", GMC_DATATYPE_BITFIELD8, &f9Value, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F10", GMC_DATATYPE_BITFIELD16, &f10Value, sizeof(uint16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F13", GMC_DATATYPE_BITFIELD8, &f13Value, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint8_t f14[BYTES_LEN] = {0};
    (void)snprintf((char *)f14, BYTES_LEN, "%s", bytesValue);
    ret = GmcNodeSetPropertyByName(node, "F14", GMC_DATATYPE_BYTES, f14, strlen(bytesValue));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

int GtSpeciallabelSetT1VProperty(GmcNodeT *node, uint32_t value, char *stringValue)
{
    int ret = GMERR_OK;

    uint32_t v1 = value;
    ret = GmcNodeSetPropertyByName(node, "V1", GMC_DATATYPE_UINT32, &v1, sizeof(v1));
    RETURN_IFERR(ret);

    uint32_t v2 = value;
    ret = GmcNodeSetPropertyByName(node, "V2", GMC_DATATYPE_UINT32, &v2, sizeof(v2));
    RETURN_IFERR(ret);

    uint8_t v3Bits[2] = {0x55, 0x55};
    GmcBitMapT v3 = {0};
    v3.beginPos = 0;
    v3.endPos = 16 - 1;
    v3.bits = v3Bits;
    ret = GmcNodeSetPropertyByName(node, "V3", GMC_DATATYPE_BITMAP, &v3, sizeof(v3));
    RETURN_IFERR(ret);

    uint8_t v4[STRING_LEN] = {0};
    (void)snprintf((char *)v4, STRING_LEN, "%s", stringValue);
    ret = GmcNodeSetPropertyByName(node, "V4", GMC_DATATYPE_STRING, v4, strlen(stringValue));
    RETURN_IFERR(ret);
    return GMERR_OK;
}

void GtSpeciallabelNodeSet(GmcNodeT *node, uint32_t value, char *stringValue, uint16_t t1Count, uint16_t t2Count)
{
    GmcNodeT *t2Node = NULL;
    // 插入vector节点
    for (uint16_t j = 0; j < t1Count; j++) {
        int32_t ret = GmcNodeAppendElement(node, &node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GtSpeciallabelSetT1VProperty(node, value, stringValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 获取T2V节点
        ret = GmcNodeGetChild(node, "T2V", &t2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (uint16_t k = 0; k < t2Count; k++) {
            ret = GmcNodeAppendElement(t2Node, &t2Node);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GtSpeciallabelSetT1VProperty(t2Node, value, stringValue);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
}

int GtSpeciallabelStructWrite(GmcStmtT *stmt, GtSpeciallabelCfgT vertexCfg, char *bytesValue, char *stringValue,
                              bool isDefaultValue = true)
{
    int ret = GMERR_OK;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;
    uint16_t t1VCount = vertexCfg.t1VCount;
    uint16_t t2VCount = vertexCfg.t2VCount;
    GmcOperationTypeE optType = vertexCfg.optType;
    GmcNodeT *root = NULL, *t1Node = NULL;

    GtSpeciallabelVertexT *vertex = (GtSpeciallabelVertexT *)malloc(sizeof(GtSpeciallabelVertexT));
    if (vertex == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex is NULL\n");
        return 1;
    }
    (void)memset(vertex, 0, sizeof(GtSpeciallabelVertexT));
    GtSpeciallabelT1VVertexT *t1V = (GtSpeciallabelT1VVertexT *)malloc(sizeof(GtSpeciallabelT1VVertexT) * t1VCount);
    if (t1V == NULL) {
        AW_FUN_Log(LOG_ERROR, "t1V is NULL\n");
        return 1;
    }
    (void)memset(t1V, 0, sizeof(GtSpeciallabelT1VVertexT) * t1VCount);
    vertex->t1V = t1V;
    GtSpeciallabelT2VVertexT *t2V =
    (GtSpeciallabelT2VVertexT *)malloc(sizeof(GtSpeciallabelT2VVertexT) * t1VCount * t2VCount);
    if (t2V == NULL) {
        AW_FUN_Log(LOG_ERROR, "t2V is NULL\n");
        return 1;
    }
    (void)memset(t2V, 0, sizeof(GtSpeciallabelT2VVertexT) * t1VCount * t2VCount);
    for (int32_t i = 0; i < t1VCount; i++) {
        t1V[i].t2V = &t2V[t2VCount * ((0) + i)];
    }
    TestLabelInfoT labelInfo = {g_labelName2, 0, g_testNameSpace};
    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_labelName2, optType);
        RETURN_IFERR(ret);
        if (optType == GMC_OPERATION_MERGE) {
            GtSpeciallabelStructSetPk(vertex, i);
            ret = testStructSetIndexKeyWithBuf(stmt, vertex, 0, NULL, &labelInfo);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            GtSpeciallabelGetNode(stmt, &root, &t1Node);
            if (expAffectRows == 1) {
                GtSpeciallabelSetLpmProperty(root, i);
                int64_t f0Value = i;
                ret = GmcNodeSetPropertyByName(root, (char *)"F0", GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            }
            TestSpecialTUpdateSetProperty(root, i + coefficient, bytesValue, isDefaultValue);
            GtSpeciallabelNodeSet(t1Node, i, stringValue, t1VCount, t2VCount);
        } else {
            GtSpeciallabelStructSetPk(vertex, i);
            GtSpeciallabelStructSetHashcluster(vertex, i, coefficient);
            GtSpeciallabelStructSetLocalhash(vertex, i, coefficient);
            GtSpeciallabelStructSetLocal(vertex, i);
            GtSpeciallabelStructSetLpm4(vertex, i);
            GtSpeciallabelStructSetProperty(vertex, i, t1VCount, t2VCount, bytesValue, stringValue, isDefaultValue,
                                            coefficient);
            ret = testStructSetVertexWithBuf(stmt, vertex, &labelInfo);
            RETURN_IFERR(ret);
        }
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expAffectRows);
        RETURN_IFERR(ret);
    }
    GtSpeciallabelStructFree(vertex);
    free(t2V);
    free(t1V);
    free(vertex);
    return GMERR_OK;
}

// 以结构化的方式 merge or update表的数据
int GtSpeciallabelStructUpdate(GmcStmtT *stmt, GtSpeciallabelCfgT vertexCfg, char *bytesValue, char *stringValue,
                               bool isDefaultValue = true)
{
    int ret = GMERR_OK;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;
    uint16_t t1VCount = vertexCfg.t1VCount;
    uint16_t t2VCount = vertexCfg.t2VCount;
    GmcOperationTypeE optType = vertexCfg.optType;
    GmcNodeT *root, *t1V;
    GtSpeciallabelVertexT *vertex = (GtSpeciallabelVertexT *)malloc(sizeof(GtSpeciallabelVertexT));
    if (vertex == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex is NULL\n");
        return 1;
    }
    (void)memset(vertex, 0, sizeof(GtSpeciallabelVertexT));
    TestLabelInfoT labelInfo = {g_labelName2, 0, g_testNameSpace};

    for (int64_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_labelName2, optType);
        RETURN_IFERR(ret);
        GtSpeciallabelStructSetPk(vertex, i);
        ret = testStructSetIndexKeyWithBuf(stmt, vertex, 0, NULL, &labelInfo);
        RETURN_IFERR(ret);
        GtSpeciallabelGetNode(stmt, &root, &t1V);
        if (optType == GMC_OPERATION_MERGE && expAffectRows == 1) {
            GtSpeciallabelSetLpmProperty(root, i);
        }
        TestSpecialTUpdateSetProperty(root, i + coefficient, bytesValue, isDefaultValue);
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        ret = TestGetAffactRows(stmt, expAffectRows);
        RETURN_IFERR(ret);
    }
    free(vertex);
    return GMERR_OK;
}

int GtSpeciallabelStructDelete(GmcStmtT *stmt, GtSpeciallabelCfgT vertexCfg, uint32_t keyId)
{
    int ret = GMERR_OK;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    GmcOperationTypeE optType = vertexCfg.optType;

    GtSpeciallabelVertexT *vertex = (GtSpeciallabelVertexT *)malloc(sizeof(GtSpeciallabelVertexT));
    if (vertex == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex is NULL\n");
        return 1;
    }
    (void)memset(vertex, 0, sizeof(GtSpeciallabelVertexT));
    TestLabelInfoT labelInfo = {g_labelName2, 0, g_testNameSpace};

    AW_FUN_Log(LOG_INFO, "startPkVal = %d vertexCount = %d keyId = %d", startPkVal, vertexCount, keyId);
    for (int64_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_labelName2, GMC_OPERATION_DELETE);
        RETURN_IFERR(ret);
        if (keyId == 0) {
            GtSpeciallabelStructSetPk(vertex, i);
        } else if (keyId == 1) {
            GtSpeciallabelStructSetHashcluster(vertex, i, coefficient);
        } else if (keyId == 2) {
            GtSpeciallabelStructSetLocalhash(vertex, i, coefficient);
        } else if (keyId == 3) {
            GtSpeciallabelStructSetLocal(vertex, i);
            ret = testStructSetIndexKeyWithBuf(stmt, vertex, keyId, NULL, &labelInfo);
            RETURN_IFERR(ret);
            ret = GmcExecute(stmt);
            RETURN_IFERR(ret);
            ret = TestGetAffactRows(stmt, expAffectRows);
            RETURN_IFERR(ret);
            free(vertex);
            return GMERR_OK;
        }
        ret = testStructSetIndexKeyWithBuf(stmt, vertex, keyId, NULL, &labelInfo);
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        ret = TestGetAffactRows(stmt, expAffectRows);
        RETURN_IFERR(ret);
    }
    free(vertex);
    return GMERR_OK;
}

void TestSpecialTUpdateGetPropertyByName(GmcNodeT *node, int64_t i, char *bytesValue, bool isDefaultValue = true)
{
    int ret = 0;
    bool isNull = 0;
    uint64_t f1Value = i;
    int32_t f2Value = i;
    int16_t f4Value = i % 32768;
    uint16_t f5Value = i % 65536;
    uint64_t f7Value = i;

    uint64_t f1Value2 = i;
    int32_t f2Value2 = i;
    int16_t f4Value2 = i % 32768;
    uint16_t f5Value2 = i % 65536;
    uint64_t f7Value2 = i;

    uint8_t fixedValue[LABEL_FIXED_SIZE] = {0};
    uint8_t f9Value = 0;
    uint16_t f10Value = 0;
    uint8_t fixedValueR[LABEL_FIXED_SIZE] = {0};
    uint8_t f9ValueR = 0;
    uint16_t f10ValueR = 0;
    uint8_t f13Value = i & 0xf;
    uint8_t f13ValueR = 0;
    ret = GmcNodeGetPropertyByName(node, (char *)"F1", &f1Value2, sizeof(uint64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f1Value, f1Value2);

    ret = GmcNodeGetPropertyByName(node, (char *)"F2", &f2Value2, sizeof(int32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f2Value, f2Value2);

    ret = GmcNodeGetPropertyByName(node, (char *)"F4", &f4Value2, sizeof(int16_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f4Value, f4Value2);

    ret = GmcNodeGetPropertyByName(node, (char *)"F5", &f5Value2, sizeof(uint16_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f5Value, f5Value2);

    if (!isDefaultValue) {
        for (int j = 0; j < LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = j;
        }
        f9Value = i % 31;
        f10Value = i % 1023;
    } else {
        for (int j = 0; j < LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = 0xff;
        }
        f9Value = 0x1f;
        f10Value = 0x3ff;
    }
    ret = GmcNodeGetPropertyByName(node, (char *)"F8", fixedValueR, LABEL_FIXED_SIZE, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = memcmp(fixedValue, fixedValueR, LABEL_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeGetPropertyByName(node, (char *)"F9", &f9ValueR, sizeof(uint8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(f9Value, f9ValueR);
    ret = GmcNodeGetPropertyByName(node, (char *)"F10", &f10ValueR, sizeof(uint16_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(f10Value, f10ValueR);
    ret = GmcNodeGetPropertyByName(node, (char *)"F13", &f13ValueR, sizeof(uint8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(f13Value, f13ValueR);
    ret = queryNodePropertyAndCompare(node, (char *)"F14", GMC_DATATYPE_BYTES, bytesValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestSpecialTGetLpmProperty(GmcNodeT *node, int64_t i)
{
    int ret = 0;
    bool isNull = false;
    uint32_t vrid = 0;
    uint32_t vrfIndex = 0;
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    uint32_t vrid2 = 0;
    uint32_t vrfIndex2 = 0;
    uint32_t destIpAddr2 = 0;
    uint8_t maskLen2 = 0;
    ret = GmcNodeGetPropertyByName(node, (char *)"F3", &vrid2, sizeof(uint32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(vrid, vrid2);
    ret = GmcNodeGetPropertyByName(node, (char *)"F11", &vrfIndex2, sizeof(uint32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(vrfIndex, vrfIndex2);

    if (i <= MAX_MASK_LEN_16) {
        destIpAddr = ((i + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if (i > MAX_MASK_LEN_16 && i <= MAX_MASK_LEN_24) {
        destIpAddr = ((i + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((i + 2));
        maskLen = ((32) & 0xff);
    }
    ret = GmcNodeGetPropertyByName(node, (char *)"F12", &destIpAddr2, sizeof(uint32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(destIpAddr, destIpAddr2);
    ret = GmcNodeGetPropertyByName(node, (char *)"F6", &maskLen2, sizeof(uint8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(maskLen, maskLen2);
}

void GtSpeciallabelGeneralComparePropertyVector(GmcNodeT *node, int64_t value, char *stringValue)
{
    uint32_t v1Value = value;
    int32_t ret = queryNodePropertyAndCompare(node, (char *)"V1", GMC_DATATYPE_UINT32, &v1Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, (char *)"V2", GMC_DATATYPE_UINT32, &v1Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, (char *)"V4", GMC_DATATYPE_STRING, stringValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint8_t v3Bits[2] = {0x55, 0x55};
    GmcBitMapT v3 = {0};
    v3.beginPos = 0;
    v3.endPos = 8 - 1;
    v3.bits = v3Bits;
    ret = queryNodePropertyAndCompare(node, (char *)"V3", GMC_DATATYPE_BITMAP, &v3Bits);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void GtSpeciallabelGeneralGetVector(
    GmcNodeT *node, int64_t index, char *stringValue, uint16_t t1Count, uint16_t t2Count)
{
    int ret = 0, i = 0;
    GmcNodeT *t2V = NULL;
    for (i = 0; i < t1Count; ++i) {
        ret = GmcNodeGetElementByIndex(node, i, &node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GtSpeciallabelGeneralComparePropertyVector(node, index, stringValue);
        ret = GmcNodeGetChild(node, "T2V", &t2V);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (uint32_t k = 0; k < t2Count; k++) {
            ret = GmcNodeGetElementByIndex(t2V, k, &t2V);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            GtSpeciallabelGeneralComparePropertyVector(t2V, index, stringValue);
        }
    }
}

int TestSpecialTRead(GmcStmtT *stmt, GtSpeciallabelCfgT vertexCfg, uint32_t keyId, char *bytesValue, char *stringValue,
                     bool isDefaultValue = true)
{
    int ret = 0;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;
    uint16_t t1VCount = vertexCfg.t1VCount;
    uint16_t t2VCount = vertexCfg.t2VCount;
    GmcOperationTypeE optType = vertexCfg.optType;
    int32_t localFlag = 0;
    uint32_t fetchNum = 0;
    GmcNodeT *root = NULL, *t1Node = NULL, *r1;
    bool isFinish = false;
    AW_FUN_Log(LOG_INFO, "labelName = %s keyId = %d", g_labelName2, keyId);
    for (int i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_labelName2, GMC_OPERATION_SCAN);
        RETURN_IFERR(ret);
        if (keyId == 0) {
        TestVertexT1PkIndexSet(stmt, i);
        } else if (keyId == 1) {
            TestVertexT1HashclusterIndexSet(stmt, i + coefficient);
        } else if (keyId == 2) {
            TestVertexT1LocalhashIndexSet(stmt, i + coefficient);
        } else if (keyId == 3) {
            TestVertexT1LocalIndexSet(stmt, i);
        } else if (keyId == 4) {
            TestVertexT1LpmIndexSet(stmt, i);
        } else {
            AW_FUN_Log(LOG_ERROR, "keyId error!! keyId = %d", keyId);
        }
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        ret = GmcFetch(stmt, &isFinish);
        RETURN_IFERR(ret);
        if (keyId == 3 && localFlag == 0) {
            localFlag = 1;
            int64_t f0Value = 0;
            bool isNull = false;
            while (!isFinish) {
                fetchNum++;
                GtSpeciallabelGetNode(stmt, &root, &t1Node);
                ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                RETURN_IFERR(ret);
                if (f0Value >= startPkVal && f0Value < startPkVal + vertexCount) {
                    TestSpecialTUpdateGetPropertyByName(root, f0Value + coefficient, bytesValue, isDefaultValue);
                    TestSpecialTGetLpmProperty(root, f0Value);
                    GtSpeciallabelGeneralGetVector(t1Node, f0Value, stringValue, t1VCount, t2VCount);
                }
                ret = GmcFetch(stmt, &isFinish);
                RETURN_IFERR(ret);
            }
            AW_MACRO_EXPECT_EQ_INT(expAffectRows, fetchNum);
            fetchNum = 0;
            return 0;
        } else if (keyId != 3) {
            GtSpeciallabelGetNode(stmt, &root, &t1Node);
            TestSpecialTUpdateGetPropertyByName(root, i + coefficient, bytesValue, isDefaultValue);
            TestSpecialTGetLpmProperty(root, i);
            GtSpeciallabelGeneralGetVector(t1Node, i, stringValue, t1VCount, t2VCount);
            ret = GmcFetch(stmt, &isFinish);
            RETURN_IFERR(ret);
            AW_MACRO_EXPECT_EQ_INT(true, isFinish);
        }
    }
    return 0;
}

int TestSpecialTReadNoData(GmcStmtT *stmt, GtSpeciallabelCfgT vertexCfg, uint32_t keyId)
{
    int ret = 0;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t localFlag = 0;
    bool isFinish = false;
    AW_FUN_Log(LOG_INFO, "labelName = %s keyId = %d", g_labelName2, keyId);
    for (int i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_labelName2, GMC_OPERATION_SCAN);
        RETURN_IFERR(ret);
        if (keyId == 0) {
        TestVertexT1PkIndexSet(stmt, i);
        } else if (keyId == 1) {
            TestVertexT1HashclusterIndexSet(stmt, i + coefficient);
        } else if (keyId == 2) {
            TestVertexT1LocalhashIndexSet(stmt, i + coefficient);
        } else if (keyId == 3) {
            TestVertexT1LocalIndexSet(stmt, i);
        } else if (keyId == 4) {
            TestVertexT1LpmIndexSet(stmt, i);
        } else {
            AW_FUN_Log(LOG_ERROR, "keyId error!! keyId = %d", keyId);
        }
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        ret = GmcFetch(stmt, &isFinish);
        RETURN_IFERR(ret);
        if (keyId == 3 && localFlag == 0) {
            localFlag = 1;
            AW_MACRO_EXPECT_EQ_INT(true, isFinish);
            return 0;
        } else if (keyId != 3) {
            AW_MACRO_EXPECT_EQ_INT(true, isFinish);
        }
    }
    return 0;
}

/***************************yang******************************************/
const char *g_yangConfig = "{\"max_record_count\" : 2000000, \"isFastReadUncommitted\":0, \"auto_increment\":1,"
    "\"hash_type\":\"chained\", \"yang_model\":1}";
GmcTxConfigT g_trxConfig;
GmcTxConfigT g_trxConfig1;
char *g_namesapceName = (char *)"yangChainHashTest";
const char *g_containRootName = "T0";
// container--container类型的Vertex Name
const char *g_containChildName01 = "T1";
const char *g_containChildName02 = "T2";
const char *g_containChildName03 = "T3";
const char *g_containChildName04 = "T4";
const char *g_containChildName05 = "T5";

// container--list类型的Vertex Name
const char *g_listChildName01 = "T1";
const char *g_listChildName02 = "T2";
const char *g_listChildName03 = "T3";
const char *g_listChildName04 = "T4";
const char *g_listChildName05 = "T5";

// container--choice--case类型的Vertex Name
const char *g_choiceChildName01 = "T1";
const char *g_choiceChildName02 = "T2";
const char *g_choiceChildName03 = "T3";
const char *g_choiceChildName04 = "T4";
const char *g_choiceChildName05 = "T5";
const char *g_caseChildName01 = "T11";
const char *g_caseChildName021 = "T21";
const char *g_caseChildName03 = "T31";
const char *g_caseChildName04 = "T41";
const char *g_caseChildName05 = "T51";
const char *g_caseChildName022 = "T22";

const char *g_keyName = "PK";
// userData结构
struct SubtreeFilterCbParam {
    int step;
    int32_t expectStatus;          // 预期的操作状态
    const char *expectReplyJson;  // 预期返回的subtree查询结果, json字符串
};

static vector<string> expectDiffMix62 = {
    "T0:create[(priKey(ID:1)),(NULL)]\n"
    "T0.F0:create(100)\n"
    "T0.F1:create(100)\n"
    "T0.F2:create(string)\n"
    "T0.T1:create\n"
    "T1.F0:create(100)\n"
    "T1.F1:create(100)\n"
    "T1.F2:create(string)\n"
    "T0.T2:create\n"
    "T2.T21:create\n"
    "T21.F0:create(100)\n"
    "T21.F1:create(100)\n"
    "T21.F2:create(string)\n"
    "T21.T211:create\n"
    "T211.F0:create(100)\n"
    "T211.F1:create(100)\n"
    "T211.F2:create(string)\n"
    "T21.T213:create\n"
    "T213.T2131:create\n"
    "T2131.F0:create(100)\n"
    "T2131.F1:create(100)\n"
    "T2131.F2:create(string)\n"
    "T21.T212:create[(priKey(PID:1,F0:100)),(NULL)]\n"
    "T212.ID:create(1)\n"
    "T212.F1:create(100)\n"
    "T212.F2:create(string)\n"
    "T21.T212:create[(priKey(PID:1,F0:101), preKey(PID:1,F0:100)),(NULL)]\n"
    "T212.ID:create(2)\n"
    "T212.F1:create(101)\n"
    "T212.F2:create(string)\n"
    "T21.T212:create[(priKey(PID:1,F0:102), preKey(PID:1,F0:101)),(NULL)]\n"
    "T212.ID:create(3)\n"
    "T212.F1:create(102)\n"
    "T212.F2:create(string)\n"
    "T21.T212:create[(priKey(PID:1,F0:103), preKey(PID:1,F0:102)),(NULL)]\n"
    "T212.ID:create(4)\n"
    "T212.F1:create(103)\n"
    "T212.F2:create(string)\n"
    "T21.T212:create[(priKey(PID:1,F0:104), preKey(PID:1,F0:103)),(NULL)]\n"
    "T212.ID:create(5)\n"
    "T212.F1:create(104)\n"
    "T212.F2:create(string)\n"
    "T0.T3:create[(priKey(PID:1,F0:100)),(NULL)]\n"
    "T3.ID:create(1)\n"
    "T3.F1:create(100)\n"
    "T3.F2:create(string)\n"
    "T0.T3:create[(priKey(PID:1,F0:101), preKey(PID:1,F0:100)),(NULL)]\n"
    "T3.ID:create(2)\n"
    "T3.F1:create(101)\n"
    "T3.F2:create(string)\n"
    "T0.T3:create[(priKey(PID:1,F0:102), preKey(PID:1,F0:101)),(NULL)]\n"
    "T3.ID:create(3)\n"
    "T3.F1:create(102)\n"
    "T3.F2:create(string)\n"
    "T0.T3:create[(priKey(PID:1,F0:103), preKey(PID:1,F0:102)),(NULL)]\n"
    "T3.ID:create(4)\n"
    "T3.F1:create(103)\n"
    "T3.F2:create(string)\n"
    "T0.T3:create[(priKey(PID:1,F0:104), preKey(PID:1,F0:103)),(NULL)]\n"
    "T3.ID:create(5)\n"
    "T3.F1:create(104)\n"
    "T3.F2:create(string)\n"
    "T3.T31:create\n"
    "T31.F0:create(100)\n"
    "T31.F1:create(100)\n"
    "T31.F2:create(string)\n"
    "T3.T33:create\n"
    "T33.T331:create\n"
    "T331.F0:create(100)\n"
    "T331.F1:create(100)\n"
    "T331.F2:create(string)\n"
    "T3.T32:create[(priKey(PID:5,F0:100)),(NULL)]\n"
    "T32.ID:create(1)\n"
    "T32.F1:create(100)\n"
    "T32.F2:create(string)\n"
    "T3.T32:create[(priKey(PID:5,F0:101), preKey(PID:5,F0:100)),(NULL)]\n"
    "T32.ID:create(2)\n"
    "T32.F1:create(101)\n"
    "T32.F2:create(string)\n"
    "T3.T32:create[(priKey(PID:5,F0:102), preKey(PID:5,F0:101)),(NULL)]\n"
    "T32.ID:create(3)\n"
    "T32.F1:create(102)\n"
    "T32.F2:create(string)\n"
    "T3.T32:create[(priKey(PID:5,F0:103), preKey(PID:5,F0:102)),(NULL)]\n"
    "T32.ID:create(4)\n"
    "T32.F1:create(103)\n"
    "T32.F2:create(string)\n"
    "T3.T32:create[(priKey(PID:5,F0:104), preKey(PID:5,F0:103)),(NULL)]\n"
    "T32.ID:create(5)\n"
    "T32.F1:create(104)\n"
    "T32.F2:create(string)\n"
};

static vector<string> expectDiffMix63 = {
    "T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
    "T0.T2:update\n"
    "T2.T21:update\n"
    "T21.F1:update(20,100)\n"
};

static vector<string> expectDiffMix004 = {
    "T0:remove[(NULL),(priKey(ID:1))]\n"
    "T0.F0:remove(100)\n"
    "T0.F1:remove(100)\n"
    "T0.F2:remove(string)\n"
    "T0.T1:remove\n"
    "T1.F0:remove(100)\n"
    "T1.F1:remove(100)\n"
    "T1.F2:remove(string)\n"
    "T0.T2:remove\n"
    "T2.T21:remove\n"
    "T21.F0:remove(100)\n"
    "T21.F1:remove(100)\n"
    "T21.F2:remove(string)\n"
    "T21.T211:remove\n"
    "T211.F0:remove(100)\n"
    "T211.F1:remove(100)\n"
    "T211.F2:remove(string)\n"
    "T21.T213:remove\n"
    "T213.T2131:remove\n"
    "T2131.F0:remove(100)\n"
    "T2131.F1:remove(100)\n"
    "T2131.F2:remove(string)\n"
    "T21.T212:remove[(NULL),(priKey(PID:1,F0:100))]\n"
    "T212.ID:remove(1)\n"
    "T212.F1:remove(100)\n"
    "T212.F2:remove(string)\n"
    "T21.T212:remove[(NULL),(priKey(PID:1,F0:101), preKey(PID:1,F0:100))]\n"
    "T212.ID:remove(2)\n"
    "T212.F1:remove(101)\n"
    "T212.F2:remove(string)\n"
    "T21.T212:remove[(NULL),(priKey(PID:1,F0:102), preKey(PID:1,F0:101))]\n"
    "T212.ID:remove(3)\n"
    "T212.F1:remove(102)\n"
    "T212.F2:remove(string)\n"
    "T21.T212:remove[(NULL),(priKey(PID:1,F0:103), preKey(PID:1,F0:102))]\n"
    "T212.ID:remove(4)\n"
    "T212.F1:remove(103)\n"
    "T212.F2:remove(string)\n"
    "T21.T212:remove[(NULL),(priKey(PID:1,F0:104), preKey(PID:1,F0:103))]\n"
    "T212.ID:remove(5)\n"
    "T212.F1:remove(104)\n"
    "T212.F2:remove(string)\n"
    "T0.T3:remove[(NULL),(priKey(PID:1,F0:100))]\n"
    "T3.ID:remove(1)\n"
    "T3.F1:remove(100)\n"
    "T3.F2:remove(string)\n"
    "T0.T3:remove[(NULL),(priKey(PID:1,F0:101), preKey(PID:1,F0:100))]\n"
    "T3.ID:remove(2)\n"
    "T3.F1:remove(101)\n"
    "T3.F2:remove(string)\n"
    "T0.T3:remove[(NULL),(priKey(PID:1,F0:102), preKey(PID:1,F0:101))]\n"
    "T3.ID:remove(3)\n"
    "T3.F1:remove(102)\n"
    "T3.F2:remove(string)\n"
    "T0.T3:remove[(NULL),(priKey(PID:1,F0:103), preKey(PID:1,F0:102))]\n"
    "T3.ID:remove(4)\n"
    "T3.F1:remove(103)\n"
    "T3.F2:remove(string)\n"
    "T0.T3:remove[(NULL),(priKey(PID:1,F0:104), preKey(PID:1,F0:103))]\n"
    "T3.ID:remove(5)\n"
    "T3.F1:remove(104)\n"
    "T3.F2:remove(string)\n"
    "T3.T31:remove\n"
    "T31.F0:remove(100)\n"
    "T31.F1:remove(100)\n"
    "T31.F2:remove(string)\n"
    "T3.T33:remove\n"
    "T33.T331:remove\n"
    "T331.F0:remove(100)\n"
    "T331.F1:remove(100)\n"
    "T331.F2:remove(string)\n"
    "T3.T32:remove[(NULL),(priKey(PID:5,F0:100))]\n"
    "T32.ID:remove(1)\n"
    "T32.F1:remove(100)\n"
    "T32.F2:remove(string)\n"
    "T3.T32:remove[(NULL),(priKey(PID:5,F0:101), preKey(PID:5,F0:100))]\n"
    "T32.ID:remove(2)\n"
    "T32.F1:remove(101)\n"
    "T32.F2:remove(string)\n"
    "T3.T32:remove[(NULL),(priKey(PID:5,F0:102), preKey(PID:5,F0:101))]\n"
    "T32.ID:remove(3)\n"
    "T32.F1:remove(102)\n"
    "T32.F2:remove(string)\n"
    "T3.T32:remove[(NULL),(priKey(PID:5,F0:103), preKey(PID:5,F0:102))]\n"
    "T32.ID:remove(4)\n"
    "T32.F1:remove(103)\n"
    "T32.F2:remove(string)\n"
    "T3.T32:remove[(NULL),(priKey(PID:5,F0:104), preKey(PID:5,F0:103))]\n"
    "T32.ID:remove(5)\n"
    "T32.F1:remove(104)\n"
    "T32.F2:remove(string)\n"
};

static vector<string> expectDiffMix005 = {
    "T0:update[(priKey(ID:1)),(priKey(ID:1))]\n"
    "T0.T1:remove\n"
    "T1.F0:remove(100)\n"
    "T1.F1:remove(100)\n"
    "T1.F2:remove(string)\n"
};

static vector<string> expectDiffCreateList = {
    "T0:create[(priKey(ID:1)),(NULL)]\n"
    "T0.F0:create(100)\n"
    "T0.F1:create(100)\n"
    "T0.F2:create(string)\n"
    "T0.T1:create[(priKey(PID:1,F0:1)),(NULL)]\n"
    "T1.ID:create(1)\n"
    "T1.F1:create(1)\n"
    "T1.F2:create(string)\n"
    "T0.T1:create[(priKey(PID:1,F0:2), preKey(PID:1,F0:1)),(NULL)]\n"
    "T1.ID:create(2)\n"
    "T1.F1:create(2)\n"
    "T1.F2:create(string)\n"
    "T0.T1:create[(priKey(PID:1,F0:3), preKey(PID:1,F0:2)),(NULL)]\n"
    "T1.ID:create(3)\n"
    "T1.F1:create(3)\n"
    "T1.F2:create(string)\n"
    "T0.T1:create[(priKey(PID:1,F0:4), preKey(PID:1,F0:3)),(NULL)]\n"
    "T1.ID:create(4)\n"
    "T1.F1:create(4)\n"
    "T1.F2:create(string)\n"
    "T0.T1:create[(priKey(PID:1,F0:5), preKey(PID:1,F0:4)),(NULL)]\n"
    "T1.ID:create(5)\n"
    "T1.F1:create(5)\n"
    "T1.F2:create(string)\n"
};

void CreateYangComplex(GmcStmtT *stmt, AsyncUserDataT data)
{
    char *vertexSchema = NULL;
    char *edgeSchema = NULL;
    int ret;

    readJanssonFile("schemaFile/container_mix.gmjson", &vertexSchema);
    AW_MACRO_ASSERT_NOTNULL(vertexSchema);
    readJanssonFile("schemaFile/container_mix_edge.gmjson", &edgeSchema);
    AW_MACRO_ASSERT_NOTNULL(edgeSchema);

    ret = GmcCreateVertexLabelAsync(stmt, vertexSchema, g_yangConfig, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcCreateEdgeLabelAsync(stmt, edgeSchema, g_yangConfig, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    free(edgeSchema);
    free(vertexSchema);
}

void DropYangComplex(GmcStmtT *stmt, AsyncUserDataT data)
{
    int ret;
    ret = GmcDropEdgeLabelAsync(stmt, "T0_to_T1", drop_edge_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropEdgeLabelAsync(stmt, "T0_to_T2", drop_edge_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropEdgeLabelAsync(stmt, "T0_to_T3", drop_edge_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropEdgeLabelAsync(stmt, "T2_to_T23", drop_edge_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropEdgeLabelAsync(stmt, "T2_to_T21", drop_edge_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropEdgeLabelAsync(stmt, "T2_to_T22", drop_edge_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropEdgeLabelAsync(stmt, "T21_to_T211", drop_edge_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropEdgeLabelAsync(stmt, "T21_to_T212", drop_edge_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropEdgeLabelAsync(stmt, "T21_to_T213", drop_edge_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropEdgeLabelAsync(stmt, "T213_to_T2131", drop_edge_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropEdgeLabelAsync(stmt, "T3_to_T31", drop_edge_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropEdgeLabelAsync(stmt, "T3_to_T32", drop_edge_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropEdgeLabelAsync(stmt, "T3_to_T33", drop_edge_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropEdgeLabelAsync(stmt, "T33_to_T331", drop_edge_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcDropVertexLabelAsync(stmt, "T0", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropVertexLabelAsync(stmt, "T1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropVertexLabelAsync(stmt, "T3", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropVertexLabelAsync(stmt, "T2", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropVertexLabelAsync(stmt, "T21", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropVertexLabelAsync(stmt, "T22", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropVertexLabelAsync(stmt, "T23", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropVertexLabelAsync(stmt, "T211", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropVertexLabelAsync(stmt, "T212", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropVertexLabelAsync(stmt, "T213", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropVertexLabelAsync(stmt, "T2131", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropVertexLabelAsync(stmt, "T31", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropVertexLabelAsync(stmt, "T33", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropVertexLabelAsync(stmt, "T32", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropVertexLabelAsync(stmt, "T331", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
}

int testTransStartAsync(GmcConnT *conn, GmcTxConfigT config)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = GmcTransStartAsync(conn, &config, trans_start_callback, &data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = testWaitAsyncRecv(&data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        return ret;
    }
}

int testTransCommitAsync(GmcConnT *conn)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = GmcTransCommitAsync(conn, trans_commit_callback, &data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = testWaitAsyncRecv(&data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        return ret;
    }
}

int testTransRollBackAsync(GmcConnT *conn)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = GmcTransRollBackAsync(conn, trans_rollback_callback, &data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = testWaitAsyncRecv(&data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        return ret;
    }
}


// 适配硬件卸载隔离YANG
#if defined FEATURE_YANG
int testBatchPrepareAndSetDiff(GmcConnT *conn, GmcBatchT **batch, GmcBatchTypeE batchType = GMC_BATCH_YANG,
    GmcYangDiffTypeE diffType = GMC_YANG_DIFF_DELAY_READ_ON)
{
    int ret = 0;
    GmcBatchOptionT batchOption;

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBatchType(&batchOption, batchType);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcYangBatchOptionSetDiffType(&batchOption, diffType);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchPrepare(conn, &batchOption, batch);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    return ret;
}
#endif


int testBatchExecuteAndWait(GmcBatchT *batch, AsyncUserDataT data, int totalNum, int succNum)
{
    int ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(totalNum, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(succNum, data.succNum);
    return data.status;
}


// 适配硬件卸载隔离YANG
#if defined FEATURE_YANG
int testYangSetField1(GmcStmtT *stmt, GmcDataTypeE type, void *value, uint32_t size,
    const char *fieldName, GmcYangPropOpTypeE opType)
{
    int ret = 0;

    GmcPropValueT propValue;
    memcpy(propValue.propertyName, fieldName, (strlen(fieldName) + 1));
    propValue.type = type;
    propValue.value = value;
    propValue.size = size;
    ret = GmcYangSetVertexProperty(stmt, &propValue, opType);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetLastError();
        return ret;
    }

    return ret;
}

void testYangSetVertexProperty_PK1(GmcStmtT *stmt, uint32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    uint32_t valueF0 = i;
    ret = testYangSetField1(stmt, GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void testYangSetVertexProperty1(GmcStmtT *stmt, uint32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    uint32_t value = i;

    uint32_t valueF1 = value;
    ret = testYangSetField1(stmt, GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = testYangSetField1(stmt, GMC_DATATYPE_STRING, &valueF2, (strlen(valueF2)), "F2", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void testSetChildProperty2(GmcStmtT *stmt, GmcStmtT *stmt1, GmcBatchT *batch, uint32_t count, uint32_t keyValue,
    const char *labelName, bool isChoice, GmcNodeT **node = NULL)
{
    // 设置child节点
    for (uint32_t i = 0; i < count; i++) {
        int ret = GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *conRootNode = NULL;
        ret = GmcGetRootNode(stmt1, &conRootNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        *node = conRootNode;
        if (!isChoice) {
            testYangSetVertexProperty_PK1(stmt1, keyValue + i, GMC_YANG_PROPERTY_OPERATION_CREATE);
            testYangSetVertexProperty1(stmt1, keyValue + i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        }
        // 添加DML操作
        if (i < count - 1) {
            ret = GmcBatchAddDML(batch, stmt1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
}
void testSetChildProperty1(GmcStmtT *stmt, GmcStmtT *stmt1, GmcBatchT *batch, uint32_t count, uint32_t keyValue,
    const char *labelName, bool isChoice, GmcNodeT **node = NULL)
{
    // 设置child节点
    for (uint32_t i = 0; i < count; i++) {
        int ret = GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (!isChoice) {
            testYangSetVertexProperty_PK1(stmt1, keyValue + i, GMC_YANG_PROPERTY_OPERATION_CREATE);
            testYangSetVertexProperty1(stmt1, keyValue + i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        }
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}
int testYangSetField(GmcNodeT *node, GmcDataTypeE type, void *value, uint32_t size,
    const char *fieldName, GmcYangPropOpTypeE opType)
{
    int ret = 0;

    GmcPropValueT propValue;
    memcpy(propValue.propertyName, fieldName, (strlen(fieldName) + 1));
    propValue.type = type;
    propValue.value = value;
    propValue.size = size;
    ret = GmcYangSetNodeProperty(node, &propValue, opType);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetLastError();
        return ret;
    }

    return ret;
}

void testYangSetVertexProperty_PK(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    uint32_t valueF0 = i;
    ret = testYangSetField(node, GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void testYangSetVertexProperty(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    uint32_t value = i;

    uint32_t valueF1 = value;
    ret = testYangSetField(node, GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = testYangSetField(node, GMC_DATATYPE_STRING, &valueF2, (strlen(valueF2)), "F2", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void testSetRootProperty(GmcStmtT *stmt, GmcBatchT *batch, uint32_t keyValue, GmcNodeT **node)
{
    // 设置根节点
    int ret = testGmcPrepareStmtByLabelName(stmt, g_containRootName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *conRootNode = NULL;
    ret = GmcGetRootNode(stmt, &conRootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    *node = conRootNode;

    // 设置属性值
    testYangSetVertexProperty_PK(conRootNode, keyValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty(conRootNode, keyValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
}

void testSetChildProperty(GmcNodeT *Node, GmcStmtT *stmt1, GmcBatchT *batch, uint32_t count, uint32_t keyValue,
    const char* labelName, bool isChoice, GmcNodeT **node)
{
    // 设置child节点
    for (uint32_t i = 0; i < count; i++) {
        int ret = GmcYangEditChildNode(Node, labelName, GMC_OPERATION_INSERT, node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        if (!isChoice) {
            testYangSetVertexProperty_PK(*node, keyValue + i, GMC_YANG_PROPERTY_OPERATION_CREATE);
            testYangSetVertexProperty(*node, keyValue + i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        }
    }
}

void testSetRootPropertyReplace(GmcStmtT *stmt, GmcBatchT *batch, uint32_t keyValue, GmcNodeT **node)
{
    // 设置根节点
    int ret = testGmcPrepareStmtByLabelName(stmt, g_containRootName, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *conRootNode = NULL;
    ret = GmcGetRootNode(stmt, &conRootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    *node = conRootNode;

    // 设置属性值
    testYangSetVertexProperty_PK(conRootNode, keyValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty(conRootNode, keyValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
}
void testSetChildPropertyReplace(GmcNodeT *Node, GmcStmtT *stmt1, GmcBatchT *batch, uint32_t count, uint32_t keyValue,
    const char* labelName, bool isChoice, GmcNodeT **node)
{
    // 设置child节点
    AW_FUN_Log(LOG_INFO, "labelName: %s\n", labelName);
    for (uint32_t i = 0; i < count; i++) {
        int ret = GmcYangEditChildNode(Node, labelName, GMC_OPERATION_REPLACE_GRAPH, node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        if (!isChoice) {
            testYangSetVertexProperty_PK(*node, keyValue + i, GMC_YANG_PROPERTY_OPERATION_REPLACE);
            testYangSetVertexProperty(*node, keyValue + i, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        }
    }
}
void testSetChildPropertyReplace1(GmcStmtT *stmt, GmcStmtT *stmt1, GmcBatchT *batch, uint32_t count, uint32_t keyValue,
    const char *labelName, bool isChoice, GmcNodeT **node = NULL)
{
    // 设置child节点
    AW_FUN_Log(LOG_INFO, "labelName: %s\n", labelName);
    for (uint32_t i = 0; i < count; i++) {
        int ret = GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (!isChoice) {
            testYangSetVertexProperty_PK1(stmt1, keyValue + i, GMC_YANG_PROPERTY_OPERATION_REPLACE);
            testYangSetVertexProperty1(stmt1, keyValue + i, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        }
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}
void testSetChildPropertyReplace2(GmcStmtT *stmt, GmcStmtT *stmt1, GmcBatchT *batch, uint32_t count, uint32_t keyValue,
    const char *labelName, bool isChoice, GmcNodeT **node = NULL)
{
    // 设置child节点
    AW_FUN_Log(LOG_INFO, "labelName: %s\n", labelName);
    for (uint32_t i = 0; i < count; i++) {
        int ret = GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *conRootNode = NULL;
        ret = GmcGetRootNode(stmt1, &conRootNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        *node = conRootNode;
        if (!isChoice) {
            testYangSetVertexProperty_PK1(stmt1, keyValue + i, GMC_YANG_PROPERTY_OPERATION_REPLACE);
            testYangSetVertexProperty1(stmt1, keyValue + i, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        }
        // 添加DML操作
        if (i < count - 1) {
            ret = GmcBatchAddDML(batch, stmt1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
}
#endif


string GetOpTypeString(GmcDiffOpTypeE op)
{
    switch (op) {
        case GMC_DIFF_OP_CREATE:
            return "create";
        case GMC_DIFF_OP_REMOVE:
            return "remove";
        case GMC_DIFF_OP_UPDATE:
            return "update";
        default:
            return "invalid";
    }
}

string GetValueString(GmcYangNodeValueT *value)
{
    switch (value->type) {
        case GMC_DATATYPE_STRING:
            return string("") + (const char *)value->value;
        case GMC_DATATYPE_CHAR:
        case GMC_DATATYPE_UCHAR:
            return "" + to_string(*(const char *)value->value);
            break;
        case GMC_DATATYPE_INT8:
        case GMC_DATATYPE_UINT8:
            return "" + to_string(*(const uint8_t *)value->value);
        case GMC_DATATYPE_INT16:
        case GMC_DATATYPE_UINT16:
            return "" + to_string(*(const uint16_t *)value->value);
        case GMC_DATATYPE_INT32:
        case GMC_DATATYPE_UINT32:
            return "" + to_string(*(const uint32_t *)value->value);
        case GMC_DATATYPE_UINT64:
        case GMC_DATATYPE_INT64:
        case GMC_DATATYPE_TIME:
            return "" + to_string(*(const uint64_t *)value->value);
        case GMC_DATATYPE_FLOAT:
            return "" + to_string(*(const float *)value->value);
        case GMC_DATATYPE_DOUBLE:
            return "" + to_string(*(const double *)value->value);
        case GMC_DATATYPE_NULL:
            return string("NULL");
        default:
            return string("NIL:") + to_string(value->type);
    }
}


// 适配硬件卸载隔离YANG
#if defined FEATURE_YANG
string GetVertexString(GmcStmtT *stmt, GmcYangNodeT *info, bool isNewData)
{
    GmcYangNodeValueT *propValue = NULL;
    string res = "";
    int32_t ret;
    GmcDiffOpTypeE opType;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangNodeGetDiffOpType(info, &opType));
    uint32_t propNum = 0;
    if ((isNewData && opType == GMC_DIFF_OP_CREATE) || (!isNewData && opType == GMC_DIFF_OP_REMOVE) ||
        opType == GMC_DIFF_OP_UPDATE) {
        ret = GmcYangNodeGetKeyPropNum(info, &propNum);
        if (ret == GMERR_FEATURE_NOT_SUPPORTED) {
            return "";
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        res += "priKey(";
        for (unsigned int i = 0; i < propNum; i++) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangNodeGetKeyPropValue(info, i, &propValue));
            string propNameString = propValue->name;
            res += propNameString + ":" + GetValueString(propValue) + ",";
        }
        if (res[res.size() - 1] == ',') {
            res[res.size() - 1] = ')';
        } else {
            res += ")";
        }
    }
    bool isHasNewPrevNode = false;
    bool isHasOldPrevNode = false;
    ret = GmcYangNodeHasNewPrev(info, &isHasNewPrevNode);
    if (ret == GMERR_FEATURE_NOT_SUPPORTED) {
        return "";
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangNodeHasOldPrev(info, &isHasOldPrevNode);
    if (ret == GMERR_FEATURE_NOT_SUPPORTED) {
        return "";
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if ((isNewData && isHasNewPrevNode) || (!isNewData && isHasOldPrevNode) || opType == GMC_DIFF_OP_UPDATE) {
        bool isExist = (isNewData ? isHasNewPrevNode : isHasOldPrevNode);
        if (!isExist) {
            if (res == "") {
                res = "NULL";
            }
            return res;
        }
        res += ", preKey(";
        ret = GmcYangNodeGetKeyPropNum(info, &propNum);
        if (ret != GMERR_OK) {
            if (res == "") {
                res = "NULL";
            }
            return res;
        }
        for (unsigned int i = 0; i < propNum; i++) {
            ret = isNewData ? GmcYangNodeGetNewPrevKey(info, i, &propValue) :
                              GmcYangNodeGetOldPrevKey(info, i, &propValue);
            if (ret == GMERR_INVALID_NAME) {
                continue;
            } else if (ret != GMERR_OK) {
                cout << "error:" << ret << ", " << propValue->name;
            }
            string propNameString = propValue->name;
            res += propNameString + ":" + GetValueString(propValue) + ",";
        }
        if (res[res.size() - 1] == ',') {
            res[res.size() - 1] = ')';
        } else {
            res += ")";
        }
    }
    if (res == "") {
        res = "NULL";
    }
    return res;
}

void GetYangInfoString(GmcStmtT *stmt, GmcYangNodeT *info, string parentFix, string &res)
{
    res = parentFix + ":";
    GmcDiffOpTypeE opType;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangNodeGetDiffOpType(info, &opType));
    res += GetOpTypeString((GmcDiffOpTypeE)opType);

    GmcYangNodeTypeE nodeType;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangNodeGetType(info, &nodeType));
    if (nodeType == GMC_YANG_FIELD) {
        GmcYangNodeValueT *newValue = NULL;
        GmcYangNodeValueT *oldValue = NULL;
        if (opType == GMC_DIFF_OP_CREATE) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangNodeGetNewValue(info, &newValue));
            res += "(" + GetValueString(newValue) + ")";
        } else if (opType == GMC_DIFF_OP_REMOVE) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangNodeGetOldValue(info, &oldValue));
            res += "(" + GetValueString(oldValue) + ")";
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangNodeGetNewValue(info, &newValue));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangNodeGetOldValue(info, &oldValue));
            res += "(" + GetValueString(newValue) + "," + GetValueString(oldValue) + ")";
        }
    } else {
        if (strcmp(GetVertexString(stmt, info, true).c_str(), "") != 0) {
            res += "[(" + GetVertexString(stmt, info, true) + "),";
        }
        if (strcmp(GetVertexString(stmt, info, false).c_str(), "") != 0) {
            res += "(" + GetVertexString(stmt, info, false) + ")]";
        }
    }
}

// 深度遍历生成diff信息字符串
void DFSYangNode(GmcStmtT *stmt, GmcYangNodeT *parent, string prefix, string &resStr)
{
    GmcYangNodeT *child = NULL;
    GmcYangNodeT *prevChild = NULL;
    string res = "";
    string diffStr;
    do {
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcYangNodeGetNext(parent, prevChild, &child));
        prevChild = child;
        if (child != NULL) {
            // 打印diff信息
            const char *nodeName;
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcYangNodeGetName(child, &nodeName));
            string childName = prefix + nodeName;
            ASSERT_NO_FATAL_FAILURE(GetYangInfoString(stmt, child, childName, diffStr));
            res += diffStr + "\n";
            string childStr;
            DFSYangNode(stmt, child, nodeName + string("."), childStr);
            res += childStr;
        }
    } while (child != NULL);
    resStr += res;
}
#endif


// 比较s1 s2两个字符串，如果字符串相同，返回空串，不同返回从不同位置开始的子串
string StrCmp(string &s1, string &s2)
{
    int i = 0;
    for (; s1[i] != 0 && s2[i] != 0; i++) {
        if (s1[i] != s2[i]) {
            return s1.substr(i);
        }
    }

    if (s1[i] == 0 && s2[i] == 0) {
        return string("");
    } else if (s2[i] == 0) {
        return s1.substr(i);
    } else {
        return s2.substr(i);
    }
}


// 适配硬件卸载隔离YANG
#if defined FEATURE_YANG
void TestCheckYangTree(GmcStmtT *stmt, const GmcYangTreeT **yangTrees, uint32_t count, vector<string> &expectReply)
{
    AW_MACRO_ASSERT_EQ_INT(expectReply.size(), count);
    GmcYangNodeT *rootInfo = NULL;
    for (uint32_t i = 0; i < count; i++) {
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcYangGetRootNode(yangTrees[i], &rootInfo));
        string res;
        const char *rootName;
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcYangNodeGetName(rootInfo, &rootName));
        ASSERT_NO_FATAL_FAILURE(GetYangInfoString(stmt, rootInfo, rootName + string(""), res));
        res += "\n";
        ASSERT_NO_FATAL_FAILURE(DFSYangNode(stmt, rootInfo, rootName + string("."), res));
        char fileName[128] = {0};
        int ret = sprintf_s(fileName, sizeof(fileName), "perf_%dDiffTreeInfo", count);
        if (ret == -1) {
            AW_FUN_Log(LOG_INFO, "sprintf_s error\n");
            return;
        }
        FILE *fp = fopen(fileName, "w");
        if (fp == NULL) {
            AW_FUN_Log(LOG_INFO, "fopen error\n");
            return;
        }
        ret = fputs(res.c_str(), fp);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "fputs error\n");
            return;
        }
        ret = fclose(fp);
        if (ret == -1) {
            AW_FUN_Log(LOG_INFO, "fclose error\n");
            return;
        }
        cout << "actual diff：\n" << res;
        ASSERT_STREQ(StrCmp(expectReply[i], res).c_str(), "") << i;
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcYangFreeTree(yangTrees[i]));
    }
}

void FetchDiff_callback(
    void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    if (userData) {
        bool isEnd = false;
        uint32_t count = 0;
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        userData1->historyRecvNum++;
        userData1->status = status;
        if (userData1->lastError != NULL) {
            int ret = strcmp(userData1->lastError, errMsg);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (status == GMERR_OK) {
            const GmcYangTreeT **yangTree = NULL;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));
            AW_MACRO_EXPECT_EQ_INT((uint32_t)(*userData1->expectDiff).size(), count);
            EXPECT_TRUE(isEnd);
            TestCheckYangTree(userData1->stmt, yangTree, count, *userData1->expectDiff);
            GmcYangFreeFetchRet(fetchRet);
        }
        userData1->recvNum++;
    }
}

void testFetchAndDeparseDiff(GmcStmtT *stmt, GmcBatchT *batch, vector<string> &expectDiff, AsyncUserDataT data,
    int rets = GMERR_OK)
{
    data.stmt = stmt;
    data.expectDiff = &expectDiff;
    int ret = GmcYangFetchDiffExecuteAsync(stmt, NULL, FetchDiff_callback, &data);
    if (ret != GMERR_OK) {
        testGmcGetLastError(NULL);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(rets, data.status);
}

void testYangInsertComplex(GmcConnT *conn, GmcStmtT *stmt, GmcBatchT *batch)
{
    int ret;
    GmcStmtT *stmtAsync[3];
    for (int i = 0; i < 3; i++) {
        ret = GmcAllocStmt(conn, &stmtAsync[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    GmcNodeT *nodeAsync[11];
    // 启动事务
    uint32_t keyValue = 100;
    ret = testTransStartAsync(conn, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testSetRootProperty(stmt, batch, 100, &nodeAsync[0]);
    testSetChildProperty(nodeAsync[0], stmt, batch, 1, 100, "T1", false, &nodeAsync[1]);
    testSetChildProperty(nodeAsync[0], stmt, batch, 1, 100, "T2", true, &nodeAsync[2]);
    testSetChildProperty(nodeAsync[2], stmt, batch, 1, 100, "T21", false, &nodeAsync[4]);
    testSetChildProperty(nodeAsync[4], stmt, batch, 1, 100, "T211", false, &nodeAsync[5]);
    testSetChildProperty(nodeAsync[4], stmt, batch, 1, 100, "T213", true, &nodeAsync[6]);
    testSetChildProperty(nodeAsync[6], stmt, batch, 1, 100, "T2131", false, &nodeAsync[7]);
    ret = GmcBatchAddDML(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSetChildProperty1(stmt, stmtAsync[1], batch, 5, 100, "T212", false);
    testSetChildProperty2(stmt, stmtAsync[0], batch, 5, 100, "T3", false, &nodeAsync[3]);
    testSetChildProperty(nodeAsync[3], stmt, batch, 1, 100, "T31", false, &nodeAsync[8]);
    testSetChildProperty(nodeAsync[3], stmt, batch, 1, 100, "T33", true, &nodeAsync[9]);
    testSetChildProperty(nodeAsync[9], stmt, batch, 1, 100, "T331", false, &nodeAsync[10]);
    ret = GmcBatchAddDML(batch, stmtAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSetChildProperty1(stmtAsync[0], stmtAsync[2], batch, 5, 100, "T32", false);
    // 批处理提交
    AsyncUserDataT data = {0};
    testBatchExecuteAndWait(batch, data, 16, 16);
    testFetchAndDeparseDiff(stmt, batch, expectDiffMix62, data);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = testTransCommitAsync(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeNode(nodeAsync[0]);
    GmcFreeNode(nodeAsync[3]);
    for (int i = 0; i < 3; i++) {
        GmcFreeStmt(stmtAsync[i]);
    }
}
#endif


bool testYangJsonIsEqualInner(const json_t *jsonA, const json_t *jsonB);
bool testYangJsonIsEqualReal(const json_t *valueA, const json_t *valueB)
{
    double doubleA = json_real_value(valueA);
    double doubleB = json_real_value(valueB);
    if (fabs(doubleA - doubleB) < 1e-6) {
        return true;
    }
    return false;
}
bool testYangJsonIsEqualField(const json_t *valueA, const json_t *valueB)
{
    if (json_typeof(valueA) == JSON_STRING) {
        return strcmp(json_string_value(valueA), json_string_value(valueB)) == 0;
    }
    if (json_typeof(valueA) == JSON_INTEGER) {
        return json_integer_value(valueA) == json_integer_value(valueB);
    }
    if (json_typeof(valueA) == JSON_REAL) {
        return testYangJsonIsEqualReal(valueA, valueB);
    }
    return true;
}
bool testYangJsonIsEqualArray(const json_t *valueA, const json_t *valueB)
{
    bool isEqual = true;
    uint32_t sizeA = (uint32_t)json_array_size(valueA);
    uint32_t sizeB = (uint32_t)json_array_size(valueB);
    if (sizeA != sizeB) {
        return false;
    }
    for (uint32_t i = 0; i < sizeA; ++i) {
        json_t *itemA = json_array_get(valueA, i);
        json_t *itemB = json_array_get(valueB, i);
        if (json_typeof(itemA) == JSON_OBJECT) {
            isEqual = testYangJsonIsEqualInner(itemA, itemB);
        } else {
            isEqual = testYangJsonIsEqualField(itemA, itemB);
        }
        if (!isEqual) {
            return false;
        }
    }
    return true;
}
bool testYangJsonIsEqualInner(const json_t *jsonA, const json_t *jsonB)
{
    bool isEqual = true;
    void *itA = json_object_iter((json_t *)jsonA);
    void *itB = json_object_iter((json_t *)jsonB);

    if (itA) {
        const char *keyA = json_object_iter_key(itA);

        while ((strcmp(keyA, "ID") == 0) || (strcmp(keyA, "PID") == 0)) {
            itA = json_object_iter_next((json_t *)jsonA, itA);
            if (itA == NULL) {
                return true;
            }
            keyA = json_object_iter_key(itA);  // 规避方案，subtree查出来的ID和PID不对用户体现，不参与结果比较
        }
    }
    if (itB) {
        const char *keyB = json_object_iter_key(itB);

        while ((strcmp(keyB, "ID") == 0) || (strcmp(keyB, "PID") == 0)) {
            itB = json_object_iter_next((json_t *)jsonA, itB);
            if (itB == NULL) {
                return true;
            }
            keyB = json_object_iter_key(itB);  // 规避方案，subtree查出来的ID和PID不对用户体现，不参与结果比较
        }
    }

    while (itA && itB) {
        const char *keyA = json_object_iter_key(itA);
        json_t *valueA = json_object_iter_value(itA);
        const char *keyB = json_object_iter_key(itB);
        json_t *valueB = json_object_iter_value(itB);

        if ((json_typeof(valueA) != json_typeof(valueB)) || (strcmp(keyA, keyB) != 0)) {
            return false;
        }
        if (json_typeof(valueA) == JSON_OBJECT) {
            isEqual = testYangJsonIsEqualInner(valueA, valueB);
        } else if (json_typeof(valueA) == JSON_ARRAY) {
            isEqual = testYangJsonIsEqualArray(valueA, valueB);
        } else {
            isEqual = testYangJsonIsEqualField(valueA, valueB);
        }
        if (!isEqual) {
            return false;
        }
        itA = json_object_iter_next((json_t *)jsonA, itA);
        itB = json_object_iter_next((json_t *)jsonB, itB);
    }

    return itA == itB;
}
bool testYangJsonIsEqual(const char *json1, const char *json2)
{
    json_error_t jsonError;
    json_t *jsonA = json_loads(json1, JSON_REJECT_DUPLICATES, &jsonError);
    json_t *jsonB = json_loads(json2, JSON_REJECT_DUPLICATES, &jsonError);
    bool isEqual = testYangJsonIsEqualInner(jsonA, jsonB);
    json_decref(jsonA);
    json_decref(jsonB);
    return isEqual;
}


// 适配硬件卸载隔离YANG
#if defined FEATURE_YANG
void AsyncSubtreeFilterCb(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    SubtreeFilterCbParam *param = (SubtreeFilterCbParam *)(userData);
    param->expectStatus = status;
    if (GMERR_OK != status) {
        AW_FUN_Log(LOG_ERROR, "[err] status is %d  errMsg  is %s   \n ", status, errMsg);
        return;
    }
    bool isEnd = false;
    uint32_t count = 0;
    const char **jsonReply = NULL;
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcYangFetchJsonRetDeparse(fetchRet, &isEnd, &jsonReply, &count));
    ASSERT_TRUE(isEnd);
    AW_MACRO_ASSERT_EQ_INT(1, count);
    ASSERT_TRUE(jsonReply != NULL);
    if (param->expectReplyJson != NULL) {
        printf("\nsubTree jsonReply start ------------------------------\n");
        printf("%s\n", jsonReply[0]);
        printf("subTree jsonReply end --------------------------------\n");
        EXPECT_TRUE(testYangJsonIsEqual((const char*)jsonReply[0], param->expectReplyJson));
    } else {
        AW_FUN_Log(LOG_ERROR, "[err] no replyjson   \n ");
    }
    param->step++;
}
#endif


int testWaitAsyncSubtreeRecv(void *userData, int expRecvNum = 1, int timeout = -1, bool isAutoReset = true)
{
    int waitCnt = 0;
    struct timeval start;
    struct timeval end;
    unsigned long duration;

    gettimeofday(&start, NULL);
    SubtreeFilterCbParam *userdata1 = (SubtreeFilterCbParam *)userData;
    while (userdata1->step != expRecvNum) {
        usleep(10);
        waitCnt++;
        if (timeout > 0 && waitCnt >= timeout) {
            gettimeofday(&end, NULL);
            duration = 1000000 * (end.tv_sec - start.tv_sec) + end.tv_usec - start.tv_usec;
            printf("[INFO] Recv Timeout %lf ", (double)duration / 1000000);
            return -1;  // 接收超时
        }
    }
    return 0;
}

// 树模型
const char *g_yangConfig2 = "{\"max_record_count\" : 2000000, \"isFastReadUncommitted\":false, \"auto_increment\":1,"
    "\"yang_model\":1, \"hash_type\":\"chained\"}";
const char *g_containRootName2 = "root";
void CreateTreeChoiceCaseList(GmcStmtT *stmt, AsyncUserDataT data)
{
    char *vertexSchema = NULL;
    char *edgeSchema = NULL;
    int ret = 0;

    readJanssonFile("schemaFile/choice_case_list.gmjson", &vertexSchema);
    AW_MACRO_ASSERT_NOTNULL(vertexSchema);
    readJanssonFile("schemaFile/choice_case_list_edge.gmjson", &edgeSchema);
    AW_MACRO_ASSERT_NOTNULL(edgeSchema);

    ret = GmcCreateVertexLabelAsync(stmt, vertexSchema, g_yangConfig2, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcCreateEdgeLabelAsync(stmt, edgeSchema, g_yangConfig2, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    free(edgeSchema);
    free(vertexSchema);
}

void DropTreeChoiceCaseList(GmcStmtT *stmt, AsyncUserDataT data)
{
    int ret = 0;

    ret = GmcClearNamespaceAsync(stmt, g_namesapceName, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
}

void testInitPropValue(GmcPropValueT *propValue, const char *name, GmcDataTypeE type, void *value, uint32_t size)
{
    if (propValue == NULL) {
        return;
    }
    strcpy_s(propValue->propertyName, strlen(name) + 1, name);
    propValue->type = type;
    propValue->size = size;
    propValue->value = (void *)value;
}


// 适配硬件卸载隔离YANG
#if defined FEATURE_YANG
int testYangSetNodeProperty(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE opType, bool isList = false)
{
    int ret = 0;
    GmcPropValueT propValue = {0};
    uint32_t valueF0 = i;
    uint32_t valueF1 = i;
    char valueF2[8] = "string";
    if (!isList) {
        testInitPropValue(&propValue, "F0", GMC_DATATYPE_UINT32, &valueF0, sizeof(valueF0));
        ret = GmcYangSetNodeProperty(node, &propValue, opType);
        if (ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            testGmcGetLastError();
            return ret;
        }
    }
    testInitPropValue(&propValue, "F1", GMC_DATATYPE_UINT32, &valueF1, sizeof(valueF1));
    ret = GmcYangSetNodeProperty(node, &propValue, opType);
    if (ret) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    testInitPropValue(&propValue, "F2", GMC_DATATYPE_STRING, valueF2, strlen(valueF2));
    ret = GmcYangSetNodeProperty(node, &propValue, opType);
    if (ret) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    return ret;
}
#endif


static vector<string> expectDiffBaseChoiceCaseList = {
    "root:create[(priKey(ID:1)),(NULL)]\n"
    "root.F0:create(100)\n"
    "root.F1:create(100)\n"
    "root.F2:create(string)\n"
    "root.T0:create\n"
    "T0.P1:create\n"
    "P1.F0:create(100)\n"
    "P1.F1:create(100)\n"
    "P1.F2:create(string)\n"
    "P1.T1:create[(priKey(PID:1,F0:1)),(NULL)]\n"
    "T1.ID:create(1)\n"
    "T1.F1:create(1)\n"
    "T1.F2:create(string)\n"
    "T1.A1:create\n"
    "A1.F0:create(1)\n"
    "A1.F1:create(1)\n"
    "A1.F2:create(string)\n"
    "P1.T1:create[(priKey(PID:1,F0:2), preKey(PID:1,F0:1)),(NULL)]\n"
    "T1.ID:create(2)\n"
    "T1.F1:create(2)\n"
    "T1.F2:create(string)\n"
    "T1.A1:create\n"
    "A1.F0:create(2)\n"
    "A1.F1:create(2)\n"
    "A1.F2:create(string)\n"
    "P1.T1:create[(priKey(PID:1,F0:3), preKey(PID:1,F0:2)),(NULL)]\n"
    "T1.ID:create(3)\n"
    "T1.F1:create(3)\n"
    "T1.F2:create(string)\n"
    "T1.A1:create\n"
    "A1.F0:create(3)\n"
    "A1.F1:create(3)\n"
    "A1.F2:create(string)\n"
    "P1.T1:create[(priKey(PID:1,F0:4), preKey(PID:1,F0:3)),(NULL)]\n"
    "T1.ID:create(4)\n"
    "T1.F1:create(4)\n"
    "T1.F2:create(string)\n"
    "T1.A1:create\n"
    "A1.F0:create(4)\n"
    "A1.F1:create(4)\n"
    "A1.F2:create(string)\n"
    "P1.T1:create[(priKey(PID:1,F0:5), preKey(PID:1,F0:4)),(NULL)]\n"
    "T1.ID:create(5)\n"
    "T1.F1:create(5)\n"
    "T1.F2:create(string)\n"
    "T1.A1:create\n"
    "A1.F0:create(5)\n"
    "A1.F1:create(5)\n"
    "A1.F2:create(string)\n"
    "P1.T2:create[(priKey(PID:1,F0:1)),(NULL)]\n"
    "T2.ID:create(1)\n"
    "T2.F1:create(1)\n"
    "T2.F2:create(string)\n"
    "T2.A2:create\n"
    "A2.F0:create(1)\n"
    "A2.F1:create(1)\n"
    "A2.F2:create(string)\n"
    "P1.T2:create[(priKey(PID:1,F0:2), preKey(PID:1,F0:1)),(NULL)]\n"
    "T2.ID:create(2)\n"
    "T2.F1:create(2)\n"
    "T2.F2:create(string)\n"
    "T2.A2:create\n"
    "A2.F0:create(2)\n"
    "A2.F1:create(2)\n"
    "A2.F2:create(string)\n"
    "P1.T2:create[(priKey(PID:1,F0:3), preKey(PID:1,F0:2)),(NULL)]\n"
    "T2.ID:create(3)\n"
    "T2.F1:create(3)\n"
    "T2.F2:create(string)\n"
    "T2.A2:create\n"
    "A2.F0:create(3)\n"
    "A2.F1:create(3)\n"
    "A2.F2:create(string)\n"
    "P1.T2:create[(priKey(PID:1,F0:4), preKey(PID:1,F0:3)),(NULL)]\n"
    "T2.ID:create(4)\n"
    "T2.F1:create(4)\n"
    "T2.F2:create(string)\n"
    "T2.A2:create\n"
    "A2.F0:create(4)\n"
    "A2.F1:create(4)\n"
    "A2.F2:create(string)\n"
    "P1.T2:create[(priKey(PID:1,F0:5), preKey(PID:1,F0:4)),(NULL)]\n"
    "T2.ID:create(5)\n"
    "T2.F1:create(5)\n"
    "T2.F2:create(string)\n"
    "T2.A2:create\n"
    "A2.F0:create(5)\n"
    "A2.F1:create(5)\n"
    "A2.F2:create(string)\n"
    "P1.T3:create[(priKey(PID:1,F0:1)),(NULL)]\n"
    "T3.ID:create(1)\n"
    "T3.F1:create(1)\n"
    "T3.F2:create(string)\n"
    "T3.A3:create\n"
    "A3.F0:create(1)\n"
    "A3.F1:create(1)\n"
    "A3.F2:create(string)\n"
    "P1.T3:create[(priKey(PID:1,F0:2), preKey(PID:1,F0:1)),(NULL)]\n"
    "T3.ID:create(2)\n"
    "T3.F1:create(2)\n"
    "T3.F2:create(string)\n"
    "T3.A3:create\n"
    "A3.F0:create(2)\n"
    "A3.F1:create(2)\n"
    "A3.F2:create(string)\n"
    "P1.T3:create[(priKey(PID:1,F0:3), preKey(PID:1,F0:2)),(NULL)]\n"
    "T3.ID:create(3)\n"
    "T3.F1:create(3)\n"
    "T3.F2:create(string)\n"
    "T3.A3:create\n"
    "A3.F0:create(3)\n"
    "A3.F1:create(3)\n"
    "A3.F2:create(string)\n"
    "P1.T3:create[(priKey(PID:1,F0:4), preKey(PID:1,F0:3)),(NULL)]\n"
    "T3.ID:create(4)\n"
    "T3.F1:create(4)\n"
    "T3.F2:create(string)\n"
    "T3.A3:create\n"
    "A3.F0:create(4)\n"
    "A3.F1:create(4)\n"
    "A3.F2:create(string)\n"
    "P1.T3:create[(priKey(PID:1,F0:5), preKey(PID:1,F0:4)),(NULL)]\n"
    "T3.ID:create(5)\n"
    "T3.F1:create(5)\n"
    "T3.F2:create(string)\n"
    "T3.A3:create\n"
    "A3.F0:create(5)\n"
    "A3.F1:create(5)\n"
    "A3.F2:create(string)\n"
    "P1.T4:create[(priKey(PID:1,F0:1)),(NULL)]\n"
    "T4.ID:create(1)\n"
    "T4.F1:create(1)\n"
    "T4.F2:create(string)\n"
    "T4.A4:create\n"
    "A4.F0:create(1)\n"
    "A4.F1:create(1)\n"
    "A4.F2:create(string)\n"
    "P1.T4:create[(priKey(PID:1,F0:2), preKey(PID:1,F0:1)),(NULL)]\n"
    "T4.ID:create(2)\n"
    "T4.F1:create(2)\n"
    "T4.F2:create(string)\n"
    "T4.A4:create\n"
    "A4.F0:create(2)\n"
    "A4.F1:create(2)\n"
    "A4.F2:create(string)\n"
    "P1.T4:create[(priKey(PID:1,F0:3), preKey(PID:1,F0:2)),(NULL)]\n"
    "T4.ID:create(3)\n"
    "T4.F1:create(3)\n"
    "T4.F2:create(string)\n"
    "T4.A4:create\n"
    "A4.F0:create(3)\n"
    "A4.F1:create(3)\n"
    "A4.F2:create(string)\n"
    "P1.T4:create[(priKey(PID:1,F0:4), preKey(PID:1,F0:3)),(NULL)]\n"
    "T4.ID:create(4)\n"
    "T4.F1:create(4)\n"
    "T4.F2:create(string)\n"
    "T4.A4:create\n"
    "A4.F0:create(4)\n"
    "A4.F1:create(4)\n"
    "A4.F2:create(string)\n"
    "P1.T4:create[(priKey(PID:1,F0:5), preKey(PID:1,F0:4)),(NULL)]\n"
    "T4.ID:create(5)\n"
    "T4.F1:create(5)\n"
    "T4.F2:create(string)\n"
    "T4.A4:create\n"
    "A4.F0:create(5)\n"
    "A4.F1:create(5)\n"
    "A4.F2:create(string)\n"
    "P1.T5:create[(priKey(PID:1,F0:1)),(NULL)]\n"
    "T5.ID:create(1)\n"
    "T5.F1:create(1)\n"
    "T5.F2:create(string)\n"
    "T5.A5:create\n"
    "A5.F0:create(1)\n"
    "A5.F1:create(1)\n"
    "A5.F2:create(string)\n"
    "P1.T5:create[(priKey(PID:1,F0:2), preKey(PID:1,F0:1)),(NULL)]\n"
    "T5.ID:create(2)\n"
    "T5.F1:create(2)\n"
    "T5.F2:create(string)\n"
    "T5.A5:create\n"
    "A5.F0:create(2)\n"
    "A5.F1:create(2)\n"
    "A5.F2:create(string)\n"
    "P1.T5:create[(priKey(PID:1,F0:3), preKey(PID:1,F0:2)),(NULL)]\n"
    "T5.ID:create(3)\n"
    "T5.F1:create(3)\n"
    "T5.F2:create(string)\n"
    "T5.A5:create\n"
    "A5.F0:create(3)\n"
    "A5.F1:create(3)\n"
    "A5.F2:create(string)\n"
    "P1.T5:create[(priKey(PID:1,F0:4), preKey(PID:1,F0:3)),(NULL)]\n"
    "T5.ID:create(4)\n"
    "T5.F1:create(4)\n"
    "T5.F2:create(string)\n"
    "T5.A5:create\n"
    "A5.F0:create(4)\n"
    "A5.F1:create(4)\n"
    "A5.F2:create(string)\n"
    "P1.T5:create[(priKey(PID:1,F0:5), preKey(PID:1,F0:4)),(NULL)]\n"
    "T5.ID:create(5)\n"
    "T5.F1:create(5)\n"
    "T5.F2:create(string)\n"
    "T5.A5:create\n"
    "A5.F0:create(5)\n"
    "A5.F1:create(5)\n"
    "A5.F2:create(string)\n"
};

static vector<string> expectDiffBase008 = {
    "root:update[(priKey(ID:1)),(priKey(ID:1))]\n"
    "root.T0:remove\n"
    "T0.P1:remove\n"
    "P1.F0:remove(100)\n"
    "P1.F1:remove(100)\n"
    "P1.F2:remove(string)\n"
    "P1.T1:remove[(NULL),(priKey(PID:1,F0:1))]\n"
    "T1.ID:remove(1)\n"
    "T1.F1:remove(1)\n"
    "T1.F2:remove(string)\n"
    "T1.A1:remove\n"
    "A1.F0:remove(1)\n"
    "A1.F1:remove(1)\n"
    "A1.F2:remove(string)\n"
    "P1.T1:remove[(NULL),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"
    "T1.ID:remove(2)\n"
    "T1.F1:remove(2)\n"
    "T1.F2:remove(string)\n"
    "T1.A1:remove\n"
    "A1.F0:remove(2)\n"
    "A1.F1:remove(2)\n"
    "A1.F2:remove(string)\n"
    "P1.T1:remove[(NULL),(priKey(PID:1,F0:3), preKey(PID:1,F0:2))]\n"
    "T1.ID:remove(3)\n"
    "T1.F1:remove(3)\n"
    "T1.F2:remove(string)\n"
    "T1.A1:remove\n"
    "A1.F0:remove(3)\n"
    "A1.F1:remove(3)\n"
    "A1.F2:remove(string)\n"
    "P1.T1:remove[(NULL),(priKey(PID:1,F0:4), preKey(PID:1,F0:3))]\n"
    "T1.ID:remove(4)\n"
    "T1.F1:remove(4)\n"
    "T1.F2:remove(string)\n"
    "T1.A1:remove\n"
    "A1.F0:remove(4)\n"
    "A1.F1:remove(4)\n"
    "A1.F2:remove(string)\n"
    "P1.T1:remove[(NULL),(priKey(PID:1,F0:5), preKey(PID:1,F0:4))]\n"
    "T1.ID:remove(5)\n"
    "T1.F1:remove(5)\n"
    "T1.F2:remove(string)\n"
    "T1.A1:remove\n"
    "A1.F0:remove(5)\n"
    "A1.F1:remove(5)\n"
    "A1.F2:remove(string)\n"
    "P1.T2:remove[(NULL),(priKey(PID:1,F0:1))]\n"
    "T2.ID:remove(1)\n"
    "T2.F1:remove(1)\n"
    "T2.F2:remove(string)\n"
    "T2.A2:remove\n"
    "A2.F0:remove(1)\n"
    "A2.F1:remove(1)\n"
    "A2.F2:remove(string)\n"
    "P1.T2:remove[(NULL),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"
    "T2.ID:remove(2)\n"
    "T2.F1:remove(2)\n"
    "T2.F2:remove(string)\n"
    "T2.A2:remove\n"
    "A2.F0:remove(2)\n"
    "A2.F1:remove(2)\n"
    "A2.F2:remove(string)\n"
    "P1.T2:remove[(NULL),(priKey(PID:1,F0:3), preKey(PID:1,F0:2))]\n"
    "T2.ID:remove(3)\n"
    "T2.F1:remove(3)\n"
    "T2.F2:remove(string)\n"
    "T2.A2:remove\n"
    "A2.F0:remove(3)\n"
    "A2.F1:remove(3)\n"
    "A2.F2:remove(string)\n"
    "P1.T2:remove[(NULL),(priKey(PID:1,F0:4), preKey(PID:1,F0:3))]\n"
    "T2.ID:remove(4)\n"
    "T2.F1:remove(4)\n"
    "T2.F2:remove(string)\n"
    "T2.A2:remove\n"
    "A2.F0:remove(4)\n"
    "A2.F1:remove(4)\n"
    "A2.F2:remove(string)\n"
    "P1.T2:remove[(NULL),(priKey(PID:1,F0:5), preKey(PID:1,F0:4))]\n"
    "T2.ID:remove(5)\n"
    "T2.F1:remove(5)\n"
    "T2.F2:remove(string)\n"
    "T2.A2:remove\n"
    "A2.F0:remove(5)\n"
    "A2.F1:remove(5)\n"
    "A2.F2:remove(string)\n"
    "P1.T3:remove[(NULL),(priKey(PID:1,F0:1))]\n"
    "T3.ID:remove(1)\n"
    "T3.F1:remove(1)\n"
    "T3.F2:remove(string)\n"
    "T3.A3:remove\n"
    "A3.F0:remove(1)\n"
    "A3.F1:remove(1)\n"
    "A3.F2:remove(string)\n"
    "P1.T3:remove[(NULL),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"
    "T3.ID:remove(2)\n"
    "T3.F1:remove(2)\n"
    "T3.F2:remove(string)\n"
    "T3.A3:remove\n"
    "A3.F0:remove(2)\n"
    "A3.F1:remove(2)\n"
    "A3.F2:remove(string)\n"
    "P1.T3:remove[(NULL),(priKey(PID:1,F0:3), preKey(PID:1,F0:2))]\n"
    "T3.ID:remove(3)\n"
    "T3.F1:remove(3)\n"
    "T3.F2:remove(string)\n"
    "T3.A3:remove\n"
    "A3.F0:remove(3)\n"
    "A3.F1:remove(3)\n"
    "A3.F2:remove(string)\n"
    "P1.T3:remove[(NULL),(priKey(PID:1,F0:4), preKey(PID:1,F0:3))]\n"
    "T3.ID:remove(4)\n"
    "T3.F1:remove(4)\n"
    "T3.F2:remove(string)\n"
    "T3.A3:remove\n"
    "A3.F0:remove(4)\n"
    "A3.F1:remove(4)\n"
    "A3.F2:remove(string)\n"
    "P1.T3:remove[(NULL),(priKey(PID:1,F0:5), preKey(PID:1,F0:4))]\n"
    "T3.ID:remove(5)\n"
    "T3.F1:remove(5)\n"
    "T3.F2:remove(string)\n"
    "T3.A3:remove\n"
    "A3.F0:remove(5)\n"
    "A3.F1:remove(5)\n"
    "A3.F2:remove(string)\n"
    "P1.T4:remove[(NULL),(priKey(PID:1,F0:1))]\n"
    "T4.ID:remove(1)\n"
    "T4.F1:remove(1)\n"
    "T4.F2:remove(string)\n"
    "T4.A4:remove\n"
    "A4.F0:remove(1)\n"
    "A4.F1:remove(1)\n"
    "A4.F2:remove(string)\n"
    "P1.T4:remove[(NULL),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"
    "T4.ID:remove(2)\n"
    "T4.F1:remove(2)\n"
    "T4.F2:remove(string)\n"
    "T4.A4:remove\n"
    "A4.F0:remove(2)\n"
    "A4.F1:remove(2)\n"
    "A4.F2:remove(string)\n"
    "P1.T4:remove[(NULL),(priKey(PID:1,F0:3), preKey(PID:1,F0:2))]\n"
    "T4.ID:remove(3)\n"
    "T4.F1:remove(3)\n"
    "T4.F2:remove(string)\n"
    "T4.A4:remove\n"
    "A4.F0:remove(3)\n"
    "A4.F1:remove(3)\n"
    "A4.F2:remove(string)\n"
    "P1.T4:remove[(NULL),(priKey(PID:1,F0:4), preKey(PID:1,F0:3))]\n"
    "T4.ID:remove(4)\n"
    "T4.F1:remove(4)\n"
    "T4.F2:remove(string)\n"
    "T4.A4:remove\n"
    "A4.F0:remove(4)\n"
    "A4.F1:remove(4)\n"
    "A4.F2:remove(string)\n"
    "P1.T4:remove[(NULL),(priKey(PID:1,F0:5), preKey(PID:1,F0:4))]\n"
    "T4.ID:remove(5)\n"
    "T4.F1:remove(5)\n"
    "T4.F2:remove(string)\n"
    "T4.A4:remove\n"
    "A4.F0:remove(5)\n"
    "A4.F1:remove(5)\n"
    "A4.F2:remove(string)\n"
    "P1.T5:remove[(NULL),(priKey(PID:1,F0:1))]\n"
    "T5.ID:remove(1)\n"
    "T5.F1:remove(1)\n"
    "T5.F2:remove(string)\n"
    "T5.A5:remove\n"
    "A5.F0:remove(1)\n"
    "A5.F1:remove(1)\n"
    "A5.F2:remove(string)\n"
    "P1.T5:remove[(NULL),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"
    "T5.ID:remove(2)\n"
    "T5.F1:remove(2)\n"
    "T5.F2:remove(string)\n"
    "T5.A5:remove\n"
    "A5.F0:remove(2)\n"
    "A5.F1:remove(2)\n"
    "A5.F2:remove(string)\n"
    "P1.T5:remove[(NULL),(priKey(PID:1,F0:3), preKey(PID:1,F0:2))]\n"
    "T5.ID:remove(3)\n"
    "T5.F1:remove(3)\n"
    "T5.F2:remove(string)\n"
    "T5.A5:remove\n"
    "A5.F0:remove(3)\n"
    "A5.F1:remove(3)\n"
    "A5.F2:remove(string)\n"
    "P1.T5:remove[(NULL),(priKey(PID:1,F0:4), preKey(PID:1,F0:3))]\n"
    "T5.ID:remove(4)\n"
    "T5.F1:remove(4)\n"
    "T5.F2:remove(string)\n"
    "T5.A5:remove\n"
    "A5.F0:remove(4)\n"
    "A5.F1:remove(4)\n"
    "A5.F2:remove(string)\n"
    "P1.T5:remove[(NULL),(priKey(PID:1,F0:5), preKey(PID:1,F0:4))]\n"
    "T5.ID:remove(5)\n"
    "T5.F1:remove(5)\n"
    "T5.F2:remove(string)\n"
    "T5.A5:remove\n"
    "A5.F0:remove(5)\n"
    "A5.F1:remove(5)\n"
    "A5.F2:remove(string)\n"
};

static vector<string> expectDiffBase009 = {
    "root:update[(priKey(ID:1)),(priKey(ID:1))]\n"
    "root.T0:update\n"
    "T0.P1:update\n"
    "P1.F0:update(200,100)\n"
    "P1.F1:update(200,100)\n"
};

static vector<string> expectDiffBase010 = {
    "root:update[(priKey(ID:1)),(priKey(ID:1))]\n"
    "root.T0:update\n"
    "T0.P1:update\n"
    "P1.T1:remove[(NULL),(priKey(PID:1,F0:1))]\n"
    "T1.ID:remove(1)\n"
    "T1.F1:remove(1)\n"
    "T1.F2:remove(string)\n"
    "T1.A1:remove\n"
    "A1.F0:remove(1)\n"
    "A1.F1:remove(1)\n"
    "A1.F2:remove(string)\n"
    "P1.T1:remove[(NULL),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"
    "T1.ID:remove(2)\n"
    "T1.F1:remove(2)\n"
    "T1.F2:remove(string)\n"
    "T1.A1:remove\n"
    "A1.F0:remove(2)\n"
    "A1.F1:remove(2)\n"
    "A1.F2:remove(string)\n"
    "P1.T1:remove[(NULL),(priKey(PID:1,F0:3), preKey(PID:1,F0:2))]\n"
    "T1.ID:remove(3)\n"
    "T1.F1:remove(3)\n"
    "T1.F2:remove(string)\n"
    "T1.A1:remove\n"
    "A1.F0:remove(3)\n"
    "A1.F1:remove(3)\n"
    "A1.F2:remove(string)\n"
    "P1.T1:remove[(NULL),(priKey(PID:1,F0:4), preKey(PID:1,F0:3))]\n"
    "T1.ID:remove(4)\n"
    "T1.F1:remove(4)\n"
    "T1.F2:remove(string)\n"
    "T1.A1:remove\n"
    "A1.F0:remove(4)\n"
    "A1.F1:remove(4)\n"
    "A1.F2:remove(string)\n"
    "P1.T1:remove[(NULL),(priKey(PID:1,F0:5), preKey(PID:1,F0:4))]\n"
    "T1.ID:remove(5)\n"
    "T1.F1:remove(5)\n"
    "T1.F2:remove(string)\n"
    "T1.A1:remove\n"
    "A1.F0:remove(5)\n"
    "A1.F1:remove(5)\n"
    "A1.F2:remove(string)\n"
    "P1.T2:remove[(NULL),(priKey(PID:1,F0:1))]\n"
    "T2.ID:remove(1)\n"
    "T2.F1:remove(1)\n"
    "T2.F2:remove(string)\n"
    "T2.A2:remove\n"
    "A2.F0:remove(1)\n"
    "A2.F1:remove(1)\n"
    "A2.F2:remove(string)\n"
    "P1.T2:remove[(NULL),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"
    "T2.ID:remove(2)\n"
    "T2.F1:remove(2)\n"
    "T2.F2:remove(string)\n"
    "T2.A2:remove\n"
    "A2.F0:remove(2)\n"
    "A2.F1:remove(2)\n"
    "A2.F2:remove(string)\n"
    "P1.T2:remove[(NULL),(priKey(PID:1,F0:3), preKey(PID:1,F0:2))]\n"
    "T2.ID:remove(3)\n"
    "T2.F1:remove(3)\n"
    "T2.F2:remove(string)\n"
    "T2.A2:remove\n"
    "A2.F0:remove(3)\n"
    "A2.F1:remove(3)\n"
    "A2.F2:remove(string)\n"
    "P1.T2:remove[(NULL),(priKey(PID:1,F0:4), preKey(PID:1,F0:3))]\n"
    "T2.ID:remove(4)\n"
    "T2.F1:remove(4)\n"
    "T2.F2:remove(string)\n"
    "T2.A2:remove\n"
    "A2.F0:remove(4)\n"
    "A2.F1:remove(4)\n"
    "A2.F2:remove(string)\n"
    "P1.T2:remove[(NULL),(priKey(PID:1,F0:5), preKey(PID:1,F0:4))]\n"
    "T2.ID:remove(5)\n"
    "T2.F1:remove(5)\n"
    "T2.F2:remove(string)\n"
    "T2.A2:remove\n"
    "A2.F0:remove(5)\n"
    "A2.F1:remove(5)\n"
    "A2.F2:remove(string)\n"
};

static vector<string> expectDiffBase014 = {
    "root:remove[(NULL),(priKey(ID:1))]\n"
    "root.F0:remove(100)\n"
    "root.F1:remove(100)\n"
    "root.F2:remove(string)\n"
    "root.T0:remove\n"
    "T0.P1:remove\n"
    "P1.F0:remove(100)\n"
    "P1.F1:remove(100)\n"
    "P1.F2:remove(string)\n"
    "P1.T1:remove[(NULL),(priKey(PID:1,F0:1))]\n"
    "T1.ID:remove(1)\n"
    "T1.F1:remove(1)\n"
    "T1.F2:remove(string)\n"
    "T1.A1:remove\n"
    "A1.F0:remove(1)\n"
    "A1.F1:remove(1)\n"
    "A1.F2:remove(string)\n"
    "P1.T1:remove[(NULL),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"
    "T1.ID:remove(2)\n"
    "T1.F1:remove(2)\n"
    "T1.F2:remove(string)\n"
    "T1.A1:remove\n"
    "A1.F0:remove(2)\n"
    "A1.F1:remove(2)\n"
    "A1.F2:remove(string)\n"
    "P1.T1:remove[(NULL),(priKey(PID:1,F0:3), preKey(PID:1,F0:2))]\n"
    "T1.ID:remove(3)\n"
    "T1.F1:remove(3)\n"
    "T1.F2:remove(string)\n"
    "T1.A1:remove\n"
    "A1.F0:remove(3)\n"
    "A1.F1:remove(3)\n"
    "A1.F2:remove(string)\n"
    "P1.T1:remove[(NULL),(priKey(PID:1,F0:4), preKey(PID:1,F0:3))]\n"
    "T1.ID:remove(4)\n"
    "T1.F1:remove(4)\n"
    "T1.F2:remove(string)\n"
    "T1.A1:remove\n"
    "A1.F0:remove(4)\n"
    "A1.F1:remove(4)\n"
    "A1.F2:remove(string)\n"
    "P1.T1:remove[(NULL),(priKey(PID:1,F0:5), preKey(PID:1,F0:4))]\n"
    "T1.ID:remove(5)\n"
    "T1.F1:remove(5)\n"
    "T1.F2:remove(string)\n"
    "T1.A1:remove\n"
    "A1.F0:remove(5)\n"
    "A1.F1:remove(5)\n"
    "A1.F2:remove(string)\n"
    "P1.T2:remove[(NULL),(priKey(PID:1,F0:1))]\n"
    "T2.ID:remove(1)\n"
    "T2.F1:remove(1)\n"
    "T2.F2:remove(string)\n"
    "T2.A2:remove\n"
    "A2.F0:remove(1)\n"
    "A2.F1:remove(1)\n"
    "A2.F2:remove(string)\n"
    "P1.T2:remove[(NULL),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"
    "T2.ID:remove(2)\n"
    "T2.F1:remove(2)\n"
    "T2.F2:remove(string)\n"
    "T2.A2:remove\n"
    "A2.F0:remove(2)\n"
    "A2.F1:remove(2)\n"
    "A2.F2:remove(string)\n"
    "P1.T2:remove[(NULL),(priKey(PID:1,F0:3), preKey(PID:1,F0:2))]\n"
    "T2.ID:remove(3)\n"
    "T2.F1:remove(3)\n"
    "T2.F2:remove(string)\n"
    "T2.A2:remove\n"
    "A2.F0:remove(3)\n"
    "A2.F1:remove(3)\n"
    "A2.F2:remove(string)\n"
    "P1.T2:remove[(NULL),(priKey(PID:1,F0:4), preKey(PID:1,F0:3))]\n"
    "T2.ID:remove(4)\n"
    "T2.F1:remove(4)\n"
    "T2.F2:remove(string)\n"
    "T2.A2:remove\n"
    "A2.F0:remove(4)\n"
    "A2.F1:remove(4)\n"
    "A2.F2:remove(string)\n"
    "P1.T2:remove[(NULL),(priKey(PID:1,F0:5), preKey(PID:1,F0:4))]\n"
    "T2.ID:remove(5)\n"
    "T2.F1:remove(5)\n"
    "T2.F2:remove(string)\n"
    "T2.A2:remove\n"
    "A2.F0:remove(5)\n"
    "A2.F1:remove(5)\n"
    "A2.F2:remove(string)\n"
    "P1.T3:remove[(NULL),(priKey(PID:1,F0:1))]\n"
    "T3.ID:remove(1)\n"
    "T3.F1:remove(1)\n"
    "T3.F2:remove(string)\n"
    "T3.A3:remove\n"
    "A3.F0:remove(1)\n"
    "A3.F1:remove(1)\n"
    "A3.F2:remove(string)\n"
    "P1.T3:remove[(NULL),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"
    "T3.ID:remove(2)\n"
    "T3.F1:remove(2)\n"
    "T3.F2:remove(string)\n"
    "T3.A3:remove\n"
    "A3.F0:remove(2)\n"
    "A3.F1:remove(2)\n"
    "A3.F2:remove(string)\n"
    "P1.T3:remove[(NULL),(priKey(PID:1,F0:3), preKey(PID:1,F0:2))]\n"
    "T3.ID:remove(3)\n"
    "T3.F1:remove(3)\n"
    "T3.F2:remove(string)\n"
    "T3.A3:remove\n"
    "A3.F0:remove(3)\n"
    "A3.F1:remove(3)\n"
    "A3.F2:remove(string)\n"
    "P1.T3:remove[(NULL),(priKey(PID:1,F0:4), preKey(PID:1,F0:3))]\n"
    "T3.ID:remove(4)\n"
    "T3.F1:remove(4)\n"
    "T3.F2:remove(string)\n"
    "T3.A3:remove\n"
    "A3.F0:remove(4)\n"
    "A3.F1:remove(4)\n"
    "A3.F2:remove(string)\n"
    "P1.T3:remove[(NULL),(priKey(PID:1,F0:5), preKey(PID:1,F0:4))]\n"
    "T3.ID:remove(5)\n"
    "T3.F1:remove(5)\n"
    "T3.F2:remove(string)\n"
    "T3.A3:remove\n"
    "A3.F0:remove(5)\n"
    "A3.F1:remove(5)\n"
    "A3.F2:remove(string)\n"
    "P1.T4:remove[(NULL),(priKey(PID:1,F0:1))]\n"
    "T4.ID:remove(1)\n"
    "T4.F1:remove(1)\n"
    "T4.F2:remove(string)\n"
    "T4.A4:remove\n"
    "A4.F0:remove(1)\n"
    "A4.F1:remove(1)\n"
    "A4.F2:remove(string)\n"
    "P1.T4:remove[(NULL),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"
    "T4.ID:remove(2)\n"
    "T4.F1:remove(2)\n"
    "T4.F2:remove(string)\n"
    "T4.A4:remove\n"
    "A4.F0:remove(2)\n"
    "A4.F1:remove(2)\n"
    "A4.F2:remove(string)\n"
    "P1.T4:remove[(NULL),(priKey(PID:1,F0:3), preKey(PID:1,F0:2))]\n"
    "T4.ID:remove(3)\n"
    "T4.F1:remove(3)\n"
    "T4.F2:remove(string)\n"
    "T4.A4:remove\n"
    "A4.F0:remove(3)\n"
    "A4.F1:remove(3)\n"
    "A4.F2:remove(string)\n"
    "P1.T4:remove[(NULL),(priKey(PID:1,F0:4), preKey(PID:1,F0:3))]\n"
    "T4.ID:remove(4)\n"
    "T4.F1:remove(4)\n"
    "T4.F2:remove(string)\n"
    "T4.A4:remove\n"
    "A4.F0:remove(4)\n"
    "A4.F1:remove(4)\n"
    "A4.F2:remove(string)\n"
    "P1.T4:remove[(NULL),(priKey(PID:1,F0:5), preKey(PID:1,F0:4))]\n"
    "T4.ID:remove(5)\n"
    "T4.F1:remove(5)\n"
    "T4.F2:remove(string)\n"
    "T4.A4:remove\n"
    "A4.F0:remove(5)\n"
    "A4.F1:remove(5)\n"
    "A4.F2:remove(string)\n"
    "P1.T5:remove[(NULL),(priKey(PID:1,F0:1))]\n"
    "T5.ID:remove(1)\n"
    "T5.F1:remove(1)\n"
    "T5.F2:remove(string)\n"
    "T5.A5:remove\n"
    "A5.F0:remove(1)\n"
    "A5.F1:remove(1)\n"
    "A5.F2:remove(string)\n"
    "P1.T5:remove[(NULL),(priKey(PID:1,F0:2), preKey(PID:1,F0:1))]\n"
    "T5.ID:remove(2)\n"
    "T5.F1:remove(2)\n"
    "T5.F2:remove(string)\n"
    "T5.A5:remove\n"
    "A5.F0:remove(2)\n"
    "A5.F1:remove(2)\n"
    "A5.F2:remove(string)\n"
    "P1.T5:remove[(NULL),(priKey(PID:1,F0:3), preKey(PID:1,F0:2))]\n"
    "T5.ID:remove(3)\n"
    "T5.F1:remove(3)\n"
    "T5.F2:remove(string)\n"
    "T5.A5:remove\n"
    "A5.F0:remove(3)\n"
    "A5.F1:remove(3)\n"
    "A5.F2:remove(string)\n"
    "P1.T5:remove[(NULL),(priKey(PID:1,F0:4), preKey(PID:1,F0:3))]\n"
    "T5.ID:remove(4)\n"
    "T5.F1:remove(4)\n"
    "T5.F2:remove(string)\n"
    "T5.A5:remove\n"
    "A5.F0:remove(4)\n"
    "A5.F1:remove(4)\n"
    "A5.F2:remove(string)\n"
    "P1.T5:remove[(NULL),(priKey(PID:1,F0:5), preKey(PID:1,F0:4))]\n"
    "T5.ID:remove(5)\n"
    "T5.F1:remove(5)\n"
    "T5.F2:remove(string)\n"
    "T5.A5:remove\n"
    "A5.F0:remove(5)\n"
    "A5.F1:remove(5)\n"
    "A5.F2:remove(string)\n"
};

static vector<string> expectDiffMix016 = {
    "root:create[(priKey(ID:1)),(NULL)]\n"
    "root.name:create(string)\n"
    "root.root::L0:create[(priKey(PID:1,name:string)),(NULL)]\n"
    "root::L0.ID:create(1)\n"
    "root::L0.F1:create(2)\n"
    "root::L0.F2:create(string)\n"
    "root::L0.F3:create(string)\n"
    "root::L0.F4:create(2)\n"
    "root::L0.c0:create\n"
    "c0.F1:create(100)\n"
    "c0.F2:create(string)\n"
    "c0.F3:create(string)\n"
    "c0.F4:create(200)\n"
    "c0.c0_0:create\n"
    "c0_0.F1:create(3)\n"
    "c0_0.F2:create(string)\n"
    "c0_0.F3:create(string)\n"
    "c0_0.F4:create(3)\n"
    "c0_0.c0_0_0:create\n"
    "c0_0_0.F1:create(4)\n"
    "c0_0_0.F2:create(string)\n"
    "c0_0_0.F3:create(string)\n"
    "c0_0_0.F4:create(4)\n"
    "c0.c0_1:create\n"
    "c0_1.F1:create(5)\n"
    "c0_1.F2:create(string)\n"
    "c0_1.F3:create(string)\n"
    "c0_1.F4:create(5)\n"
    "c0_1.c0_1_0:create\n"
    "c0_1_0.F1:create(50)\n"
    "c0_1_0.F2:create(string)\n"
    "c0_1_0.F3:create(string)\n"
    "c0_1_0.F4:create(50)\n"
};


// 适配硬件卸载隔离YANG
#if defined FEATURE_YANG
void testYangPresetDataList(GmcConnT *conn, GmcStmtT *stmt, GmcBatchT *batch, AsyncUserDataT data,
    bool isChoice = false)
{
    // 启动事务
    GmcStmtT *stmt_1 = NULL;
    GmcStmtT *stmt_2 = NULL;
    GmcStmtT *stmt_3 = NULL;
    GmcStmtT *stmt_4 = NULL;
    GmcStmtT *stmt_5 = NULL;

    int ret = testTransStartAsync(conn, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = GmcPrepareStmtByLabelName(stmt, g_containRootName2, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *rootNode = NULL;
    GmcNodeT *rootNode1 = NULL;
    ret = GmcGetRootNode(stmt, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    ret = testYangSetNodeProperty(rootNode, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *childNode = NULL;
    GmcNodeT *childNode1 = NULL;
    // 设置node节点T0
    ret = GmcYangEditChildNode(rootNode, "T0", GMC_OPERATION_INSERT, &childNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (isChoice) {
        ret = GmcYangEditChildNode(childNode, "P1", GMC_OPERATION_INSERT, &childNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(childNode1, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = testYangSetNodeProperty(childNode, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchAddDML(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int i = 1; i < 6; i++) {
        ret = GmcPrepareStmtByLabelName(stmt_1, "T1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(rootNode1, "A1", GMC_OPERATION_INSERT, &childNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(childNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 设置child节点
    for (int i = 1; i < 6; i++) {
        ret = GmcPrepareStmtByLabelName(stmt_2, "T2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_2, &rootNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(rootNode1, "A2", GMC_OPERATION_INSERT, &childNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(childNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 设置child节点
    for (int i = 1; i < 6; i++) {
        ret = GmcPrepareStmtByLabelName(stmt_3, "T3", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_3, &rootNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(rootNode1, "A3", GMC_OPERATION_INSERT, &childNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(childNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    for (int i = 1; i < 6; i++) {
        ret = GmcPrepareStmtByLabelName(stmt_4, "T4", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_4);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_4, &rootNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(rootNode1, "A4", GMC_OPERATION_INSERT, &childNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(childNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_4);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    for (int i = 1; i < 6; i++) {
        ret = GmcPrepareStmtByLabelName(stmt_5, "T5", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_5);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_5, &rootNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(rootNode1, "A5", GMC_OPERATION_INSERT, &childNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(childNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_5);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 批处理提交
    ret = testBatchExecuteAndWait(batch, data, 26, 26);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 获取diffTree并校验
    testFetchAndDeparseDiff(stmt_1, batch, expectDiffBaseChoiceCaseList, data);

    ret = GmcBatchDestroy(batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = testTransCommitAsync(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(stmt_1);
    GmcFreeStmt(stmt_2);
    GmcFreeStmt(stmt_3);
    GmcFreeStmt(stmt_4);
    GmcFreeStmt(stmt_5);
}
#endif


#endif
