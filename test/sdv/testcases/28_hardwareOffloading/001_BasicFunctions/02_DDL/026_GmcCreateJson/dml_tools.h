/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */
#ifndef DML_TOOLS_H
#define DML_TOOLS_H

// Created by w00495442 on 2020/9/23.
extern "C" {
}

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"

int g_dataNnum = 100;
GmcConnT *g_conn_sync = NULL, *g_conn = NULL, *g_conn_2 = NULL, *g_conn_async = NULL;
GmcStmtT *g_stmt_sync = NULL, *g_stmt = NULL, *g_stmt_2 = NULL, *g_stmt_async = NULL;
void *g_label = NULL, *g_label2 = NULL;
char *g_schema = NULL, *g_schema2 = NULL;
char g_labelConfig[] = "{\"max_record_count\":10000}";

using namespace std;

void test_setVertexProperty(GmcStmtT *stmt, int pk = 0)
{
    int ret;
    char teststr0 = 'a';
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_CHAR, &teststr0, sizeof(char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    unsigned char teststr1 = 'b';
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UCHAR, &teststr1, sizeof(unsigned char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int8_t value2 = 1 + pk;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &value2, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint8_t value3 = 10 + pk;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &value3, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int16_t value4 = 100 + pk;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &value4, sizeof(int16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint16_t value5 = 1000 + pk;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &value5, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t value6 = pk;  // 联合索引时F6是PK
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, &value6, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t value7 = pk;  // F7是PK
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT32, &value7, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool value8 = false;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &value8, sizeof(bool));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t value9 = 1000 + pk;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &value9, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t value10 = 1000 + pk;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_UINT64, &value10, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    float value11 = (float)1.2 + (float)pk;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_FLOAT, &value11, sizeof(float));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    double value12 = 10.86 + pk;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_DOUBLE, &value12, sizeof(double));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t value13 = 1000 + pk;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_TIME, &value13, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char teststr14[] = "string";
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, teststr14, (strlen(teststr14)));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char teststr15[10] = "bytes";
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_BYTES, teststr15, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char teststr16[6] = "fixed";
    AW_MACRO_EXPECT_EQ_INT(6, strlen(teststr16) + 1);
    ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_FIXED, teststr16, (strlen(teststr16)));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t value17 = 1000 + pk;
    ret = GmcSetVertexProperty(stmt, "F17", GMC_DATATYPE_UINT32, &value17, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void test_prepare_create_label_async(GmcStmtT *stmt, char *labelName, char *testSchema)
{
    AsyncUserDataT data = {0};
    int ret = GmcCreateVertexLabelAsync(stmt, testSchema, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    printf("------------create vertexlabel------------\r\n");
}

void test_prepare_get_label_and_open_async(GmcStmtT *stmt, char *labelName, void **vertexLabel)
{
    AsyncUserDataT data = {0};
    int ret;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    printf("------------get and open vertexlabel------------\r\n");
}

void TestPrepareConnect()
{
    int ret;
    g_schema = NULL;
    g_label = NULL;
    g_conn_sync = NULL;
    g_stmt_sync = NULL;
    // 创建异步连接
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建同步连接
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    printf("TestPrepareConnect OK.\n");
}

void TestTearDownDisconnect()
{
    int ret;
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_schema);
    printf("TestTearDownDisconnect End.\n");
    g_schema = NULL;
    g_label = NULL;
    g_conn_sync = NULL;
    g_stmt_sync = NULL;
    g_conn_async = NULL;
    g_stmt_async = NULL;
}

void test_close_and_drop_label(GmcStmtT *stmt, void *label, char *labelName)
{
    int ret;
    if (label) {
        label = 0;
    }
    ret = GmcDropVertexLabel(stmt, labelName);
    if (!(ret == GMERR_OK || ret == GMERR_UNDEFINED_TABLE)) {
        printf("GmcDropVertexLabel ret :%d\r\n", ret);
    }
    AW_MACRO_EXPECT_EQ_INT(true, ret == GMERR_OK || ret == GMERR_UNDEFINED_TABLE);
}

void test_close_and_drop_label_async(GmcStmtT *stmt, void *label, char *labelName)
{
    int ret;
    AsyncUserDataT data = {0};
    if (label) {
        label = 0;
    }
    ret = GmcDropVertexLabelAsync(stmt, labelName, drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(true, data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE);
}

void test_setVertexProperty_updt(GmcStmtT *stmt, int id = 0, bool is_union_pk = 0)
{
    int ret;
    char teststr0 = 'a';
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_CHAR, &teststr0, sizeof(char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    unsigned char teststr1 = 'b';
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UCHAR, &teststr1, sizeof(unsigned char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int8_t value2 = 1 + id;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &value2, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint8_t value3 = 10 + id;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &value3, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int16_t value4 = 100 + id;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &value4, sizeof(int16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint16_t value5 = 1000 + id;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &value5, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!is_union_pk) {
        int32_t value6 = id;
        ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, &value6, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    bool value8 = false;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &value8, sizeof(bool));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t value9 = 1000 + id;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &value9, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t value10 = 1000 + id;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_UINT64, &value10, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    float value11 = (float)1.2 + (float)id;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_FLOAT, &value11, sizeof(float));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    double value12 = 10.86 + id;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_DOUBLE, &value12, sizeof(double));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t value13 = 1000 + id;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_TIME, &value13, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char teststr14[] = "string";
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, teststr14, (strlen(teststr14)));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char teststr15[10] = "bytes";
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_BYTES, teststr15, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char teststr16[6] = "fixed";
    AW_MACRO_EXPECT_EQ_INT(6, strlen(teststr16) + 1);
    ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_FIXED, teststr16, (strlen(teststr16)));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t value17 = 1000 + id;
    ret = GmcSetVertexProperty(stmt, "F17", GMC_DATATYPE_UINT32, &value17, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void test_checkVertexProperty(GmcStmtT *stmt, void *label, char *labelPk, int pk = 0, bool is_union_pk = 0)
{
    int ret;
    char teststr0 = 'a';
    unsigned char teststr1 = 'b';
    int8_t value2 = 1 + pk;
    uint8_t value3 = 10 + pk;
    int16_t value4 = 100 + pk;
    uint16_t value5 = 1000 + pk;
    int32_t value6 = pk;   // 联合索引时F6是pk
    uint32_t value7 = pk;  // F7是pk
    uint32_t value8 = false;
    int64_t value9 = 1000 + pk;
    uint64_t value10 = 1000 + pk;
    float value11 = (float)1.2 + (float)pk;
    double value12 = 10.86 + pk;
    uint64_t value13 = 1000 + pk;
    char teststr14[] = "string";
    char teststr15[10] = "bytes";
    char teststr16[6] = "fixed";
    AW_MACRO_EXPECT_EQ_INT(6, strlen(teststr16) + 1);
    uint32_t value17 = 1000 + pk;

    if (is_union_pk) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value6, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &value7, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &value7, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcSetIndexKeyName(stmt, labelPk);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_CHAR, &teststr0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_UCHAR, &teststr1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_INT8, &value2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_UINT8, &value3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F4", GMC_DATATYPE_INT16, &value4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F5", GMC_DATATYPE_UINT16, &value5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F6", GMC_DATATYPE_INT32, &value6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F7", GMC_DATATYPE_UINT32, &value7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F8", GMC_DATATYPE_BOOL, &value8);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F9", GMC_DATATYPE_INT64, &value9);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F10", GMC_DATATYPE_UINT64, &value10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F11", GMC_DATATYPE_FLOAT, &value11);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F12", GMC_DATATYPE_DOUBLE, &value12);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F13", GMC_DATATYPE_TIME, &value13);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F14", GMC_DATATYPE_STRING, teststr14);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F15", GMC_DATATYPE_BYTES, teststr15);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F16", GMC_DATATYPE_FIXED, teststr16);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F17", GMC_DATATYPE_UINT32, &value17);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeIndexKey(stmt);
}

void test_checkVertexProperty_updt(GmcStmtT *stmt, void *label, char *labelPk, uint32_t key, int pk = 0,
                                   bool is_union_pk = 0)
{
    int ret;
    char teststr0 = 'a';
    unsigned char teststr1 = 'b';
    int8_t value2 = 1 + pk;
    uint8_t value3 = 10 + pk;
    int16_t value4 = 100 + pk;
    uint16_t value5 = 1000 + pk;
    int32_t value6;         // 联合索引时F6是pk
    uint32_t value7 = key;  // F7是pk
    uint32_t value8 = false;
    int64_t value9 = 1000 + pk;
    uint64_t value10 = 1000 + pk;
    float value11 = (float)1.2 + (float)pk;
    double value12 = 10.86 + pk;
    uint64_t value13 = 1000 + pk;
    char teststr14[] = "string";
    char teststr15[10] = "bytes";
    char teststr16[6] = "fixed";
    AW_MACRO_EXPECT_EQ_INT(6, strlen(teststr16) + 1);
    uint32_t value17 = 1000 + pk;

    if (is_union_pk) {
        value6 = key;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value6, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &value7, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        value6 = pk;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &value7, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcSetIndexKeyName(stmt, labelPk);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_CHAR, &teststr0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_UCHAR, &teststr1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_INT8, &value2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_UINT8, &value3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F4", GMC_DATATYPE_INT16, &value4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F5", GMC_DATATYPE_UINT16, &value5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F6", GMC_DATATYPE_INT32, &value6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F7", GMC_DATATYPE_UINT32, &value7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F8", GMC_DATATYPE_BOOL, &value8);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F9", GMC_DATATYPE_INT64, &value9);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F10", GMC_DATATYPE_UINT64, &value10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F11", GMC_DATATYPE_FLOAT, &value11);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F12", GMC_DATATYPE_DOUBLE, &value12);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F13", GMC_DATATYPE_TIME, &value13);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F14", GMC_DATATYPE_STRING, teststr14);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F15", GMC_DATATYPE_BYTES, teststr15);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F16", GMC_DATATYPE_FIXED, teststr16);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F17", GMC_DATATYPE_UINT32, &value17);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeIndexKey(stmt);
}
#endif

