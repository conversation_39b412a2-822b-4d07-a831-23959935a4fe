/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Create: Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * History: bandinglei
 */

#include "KBdb_long_stability_common.h"
#include "KBdb_thread.h"

int main()
{
    EnvClean();
    GmeConnT *conn = NULL;

    int ret = GmeOpen(CONFIG_PATH, GME_OPEN_CREATE, &conn);
    if (GMERR_OK != ret) {
        return 1;
    }
    // 建表
    ret = CreateInitTable(conn);
    if (GMERR_OK != ret) {
        return 1;
    }

    InsertInitTable(conn);

    CreateInitIndex(conn);

    ret = GmeClose(conn);
    if (GMERR_OK != ret) {
        return 1;
    }
}
