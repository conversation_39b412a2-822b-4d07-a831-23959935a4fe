# Name : localLocatorListened  DB_CFG_LOCAL_LOCATOR
# Description : The local locator listened in the unix socket mode.
# ChangeMode : Not allowed to modify.
# Type : string
# Default : usocket:/run/verona/unix_emserver;channel:ctl_channel
localLocatorListened = usocket:/run/verona/unix_emserver;channel:ctl_channel

# Name : userPolicyMode  DB_CFG_USER_POLICY_MODE
# Description : set the authentication mode : 0-disabled  1-permissive  2-enforcing
# ChangeMode : Not allowed to modify.
# Type : int32_t
# Range : [0, 1, 2]
# Default : 2
userPolicyMode = 0

# Name : featureNames DB_CFG_FEATURE_NAMES
# Description : The feature names of dynamic load features.
# ChangeMode : allow to modify.
# Type : string
# Default :
featureNames = DURABLEMEMDATA,BUFFERPOOL,SQL,TRM,PERSISTENCE

# Name : dataFileDirPath DB_CFG_DATA_FILE_DIR_PATH
# Description : db data file directory path.
# ChangeMode : Not allowed to modify
# Type : string
# Default : NULL
# Notice : must be specified if the storageMode is 1 or 2
dataFileDirPath = ./data/gmdb/

# Name : dbFileSize DB_CFG_DB_FILE_SIZE
# Description : db space file max size, unit:KB
# ChangeMode : Not limited to modify.
# Type : uint32_t
# Range :  [4096,33554432]
# Default : 1048576
# 配大点，否则扩展文件时会超过 MAX_FILE_CNT_PER_SPACE
dbFileSize = 16777216

# Name : persistentMode DB_CFG_PERSISTENT_MODE
# Description : 0 is on demand, 1 is increment
# ChangeMode : Not allowed to modify.
# Type : int32_t
# Range : [0, 1]
# Default : 0
persistentMode = 1

# Name : redoBufParts DB_CFG_REDO_BUF_PARTS
# Description : redo public buffer part count.
# ChangeMode : Not allowed to modify.
# Type : uint32_t
# Range : [1, 16]
# Default : 4
redoBufParts = 4

# Name : redoFlushByTrx DB_CFG_REDO_FLUSH_BY_TRX
# Description : redo log will be flushing while transaction committed.
# ChangeMode : Not limited to modify.
# Type : bool
# Range : [0, 1]
# Default : 1
redoFlushByTrx = 1

# Name : redoFlushBySize DB_CFG_REDO_FLUSH_BY_SIZE
# Description : redo log will be flushing while buffer size greater than threadhold, unit is KB."0" meats disable.
# ChangeMode : Not limited to modify.
# Type : uint32_t
# Range : [0, 4096]
# Default : 1000 if enable
redoFlushBySize = 1000

dbFilesMaxCnt = 100

redoPubBufSize = 8192
crcCheckEnable = 1
preFetchPagesEnable = 1
maxPreFetchThreNum = 4
enableConcurrentFetchPage = 1
deviceSize = 4
bufferPoolPolicy = 3
maxSysDynSize = 9728
maxTotalDynSize = 10240
bufferPoolChunkSize = 1048576
bufferPoolSize = 1048576
