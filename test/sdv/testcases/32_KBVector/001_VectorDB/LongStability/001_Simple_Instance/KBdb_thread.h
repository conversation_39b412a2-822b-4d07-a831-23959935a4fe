/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: 数存长稳线程文件
 * Author: bandinglei
 */
#ifndef KBDB_THREAD
#define KBDB_THREAD

#include "./KBdb_long_stability_common.h"

/* 线程1: 依据id删除一张表，并重建该表 */
void *ThreadDropAndRebuiltTale(void *args)
{
    GmeConnT *conn = NULL;
    int ret = GmeOpen(CONFIG_PATH, GME_OPEN_CREATE, &conn);
    if (ret != GMERR_OK) {
        LONG_LOG_WRITE(LONG_LOG_ERROR, "gmopen in thread indexscan init with " + to_string(ret));
    }
    while (true) {
        int tableId = RandomInt(1, INIT_TABLE_NUM - 1);
        DropInitTable(conn, tableId);
        CreateInitTable(conn, tableId);
        InsertDataTableByNum(conn, tableId, INIT_DATA_NUM);
        CreateInitIndexByTableid(conn, tableId);
        sleep(TABLE_DDL_EXECUTE_INTERVAL);
    }

    GmeClose(conn);
}

/* 线程2: 额外表创建与表删除， 待设计 */
void CreateExtrasTable(GmeConnT *conn)
{
    return;
}

/* 线程3 对随机load表进行索引检索，PQ检索待完善 */
void *ThreadIndexScanInitTable(void *args)
{
    GmeConnT *conn = NULL;
    int ret = GmeOpen(CONFIG_PATH, GME_OPEN_CREATE, &conn);
    if (ret != GMERR_OK) {
        LONG_LOG_WRITE(LONG_LOG_ERROR, "gmopen in thread indexscan init with " + to_string(ret));
    }
    while (true) {
        int table_id = RandomInt(1, INIT_TABLE_NUM - 1);
        VectorIndexScanInInitTable(conn, table_id);
    }
    GmeClose(conn);
}

/* 线程4 对load的表进行简单查询 */
void *ThreadSimpleScanInitTable(void *args)
{
    GmeConnT *conn = NULL;
    int ret = GmeOpen(CONFIG_PATH, GME_OPEN_CREATE, &conn);
    if (ret != GMERR_OK) {
        LONG_LOG_WRITE(LONG_LOG_ERROR, "gmopen in thread indexscan init with " + to_string(ret));
    }
    while (true) {
        int table_id = RandomInt(0, INIT_TABLE_NUM - 1);
        VectorSimpleScanInitTableInitTable(conn, table_id);
    }
    GmeClose(conn);
}

/* 线程5 随机表进行DML操作 */
void *ThreadDMLInitTable(void *args)
{
    GmeConnT *conn = NULL;
    int ret = GmeOpen(CONFIG_PATH, GME_OPEN_CREATE, &conn);
    if (ret != GMERR_OK) {
        LONG_LOG_WRITE(LONG_LOG_ERROR, "gmopen in thread dml init with " + to_string(ret));
    }
    while (true) {
        int table_id = RandomInt(1, INIT_TABLE_NUM - 1);
        DMLToInitTable(conn, table_id);
    }
    GmeClose(conn);
}

/* 线程6 循环查询支持的视图 */
void *ThreadQueryAllView(void *args)
{
    GmeConnT *conn = NULL;
    int ret = GmeOpen(CONFIG_PATH, GME_OPEN_CREATE, &conn);
    if (ret != GMERR_OK) {
        LONG_LOG_WRITE(LONG_LOG_ERROR, "gmopen in thread dml init with " + to_string(ret));
    }
    while (true) {
        QueryAllSystemView(conn);
        sleep(SYSTEM_VIEW_EXECUTE_INTERVAL);
    }
    GmeClose(conn);
}

/* 线程7 对load的表进行聚合查询 */
void *ThreadAggregateFuncScanInitTable(void *args)
{
    GmeConnT *conn = NULL;
    int ret = GmeOpen(CONFIG_PATH, GME_OPEN_CREATE, &conn);
    if (ret != GMERR_OK) {
        LONG_LOG_WRITE(LONG_LOG_ERROR, "gmopen in thread indexscan init with " + to_string(ret));
    }
    while (true) {
        int table_id = RandomInt(0, INIT_TABLE_NUM - 1);
        AggregateFuncScanInitTable(conn, table_id);
    }
    GmeClose(conn);
}

/* 线程8 周期性刷盘，多文件并发加载重启实例或者切换绑核 待设计 */
void *ThreadFlushDataAndRestart(void *args)
{
    return NULL;
}

/* 线程9 周期性随机抽一张向量表进行索引重建 */
void *ThreadRebultIndexInitTable(void *args)
{
    GmeConnT *conn = NULL;
    int ret = GmeOpen(CONFIG_PATH, GME_OPEN_CREATE, &conn);
    if (ret != GMERR_OK) {
        LONG_LOG_WRITE(LONG_LOG_ERROR, "gmopen in thread indexscan init with " + to_string(ret));
    }
    while (true) {
        int tableId = RandomInt(1, INIT_TABLE_NUM - 1);
        DropInitIndexByTableid(conn, tableId);
        CreateInitIndexByTableid(conn, tableId);
        sleep(TABLE_DDL_EXECUTE_INTERVAL);
    }
    GmeClose(conn);
    return NULL;
}

/* 线程10 非常用接口覆盖 待设计 */
void *ThreadExtraInterface(void *args)
{
    return NULL;
}

#endif
