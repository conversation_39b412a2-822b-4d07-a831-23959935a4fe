/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Create: Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * History: bandinglei create 20250712
 */

#include "KBdb_long_stability_common.h"
#include "KBdb_thread.h"

void RunStabilityDefault()
{
    pthread_t threads[8];

    for (uint32_t i = 0; i < 8; i++) {
        if (i % 2 == 0) {
            AW_MACRO_EXPECT_EQ_INT(T_OK, pthread_create(&threads[i], NULL, ThreadIndexScanInitTable, NULL));
        } else {
            AW_MACRO_EXPECT_EQ_INT(T_OK, pthread_create(&threads[i], NULL, ThreadDMLInitTable, NULL));
        }
    }
    // 等待线程结束
    for (uint32_t i = 0; i < 8; i++) {
        AW_MACRO_EXPECT_EQ_INT(T_OK, pthread_join(threads[i], NULL));
    }
}

int main()
{
    GmeConnT *conn = NULL;
    system("ipcrm -a");

    int ret = GmeOpen(CONFIG_PATH, GME_OPEN_CREATE, &conn);
    if (GMERR_OK != ret) {
        return 1;
    }

    RunStabilityDefault();

    ret = GmeClose(conn);
    if (GMERR_OK != ret) {
        return 1;
    }
}
