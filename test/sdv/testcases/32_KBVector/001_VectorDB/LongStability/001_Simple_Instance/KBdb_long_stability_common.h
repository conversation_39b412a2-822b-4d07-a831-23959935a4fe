/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: 数存长稳common文件
 * Author: bandinglei
 */

#ifndef KBDB_LONG_STABILITY_H
#define KBDB_LONG_STABILITY_H

#include <random>
#include <sys/time.h>
#include "gtest/gtest.h"
#include "common_sql.h"
#include "common_vec.h"

using namespace std;
using namespace testing::ext;

#define INIT_TABLE_NUM 7
#define INIT_DATA_NUM 10000
#define DML_DATA_NUM 10

// 长稳日志级别
#define LONG_LOG_ERROR 0
#define LONG_LOG_INFO 1
#define LONG_LOG_WARNING 2

// 创建向量索引类型
#define INDEX_TYPE_SQ_L2 1
#define INDEX_TYPE_SQ_COS 2
#define INDEX_TYPE_PQ_L2 3
#define INDEX_TYPE_PQ_COS 4

// DML操作类型
#define DML_TYPE_INSERT 1
#define DML_TYPE_DELETE 2
#define DML_TYPE_UPDATE 3
#define DML_TYPE_SELECT 4

#define SYSTEM_VIEW_EXECUTE_INTERVAL 300
#define TABLE_DDL_EXECUTE_INTERVAL 900

const char *CONFIG_PATH = "./sql_persistence.ini";

typedef struct {
    GmeConnT *conn;
    int tableId;
    int dataNum;
} ThreadInertArgsT;

typedef struct {
    GmeConnT *conn;
    int tableId;
} ThreadCreateIndexArgsT;

string tableNames[] = {"table_long_stability_001",
    "table_long_stability_002",
    "table_long_stability_003",
    "table_long_stability_004",
    "table_long_stability_005",
    "table_long_stability_006",
    "table_long_stability_007"};

string ViewListDoubleMode[] = {
    "V$DB_SERVER_KEY_RESOURCE",
    "V$STORAGE_MEMDATA_STAT",
    "V$SERVER_MEMORY_OVERHEAD",
    "V$CATA_TABLESPACE_INFO",
    "V$STORAGE_SHMEM_INFO",
    "V$CATA_GENERAL_INFO",
    "V$CATA_NAMESPACE_INFO",
    "V$CATA_VERTEX_LABEL_CHECK_INFO",
    "V$CATA_VERTEX_LABEL_INFO",
    "V$COM_DYN_CTX",
    "V$COM_SHMEM_CTX",
    "V$COM_SHMEM_USAGE_STAT",
    "V$COM_TABLE_MEM_SUMMARY",
    "V$CONFIG_PARAMETERS",
    "V$CST_SHMEM_INFO",
    "V$DB_PROBE_DATA",
    "V$DB_SERVER",
    "V$DRT_COM_STAT",
    "V$DRT_WORKER_POOL_STAT",
    "V$DRT_WORKER_STAT",
    "V$PRIVILEGE_ROLE_STAT",
    "V$PRIVILEGE_USER_STAT",
    "V$QRY_DML_OPER_STATIS",
    "V$QRY_DYNMEM",
    "V$QRY_SESSION",
    "V$QRY_TRX_MONITOR_STAT",
    "V$STORAGE_BTREE_INDEX_STAT",
    "V$STORAGE_FSM_STAT",
    "V$STORAGE_HEAP_STAT",
    "V$STORAGE_HEAP_VERTEX_LABEL_STAT",
    "V$STORAGE_INDEX_GLOBAL_STAT",
    "V$STORAGE_LOCK_OVERVIEW",
    "V$STORAGE_PERSISTENT_STAT",
    "V$STORAGE_SPACE_INFO",
    "V$STORAGE_TRX_DETAIL",
    "V$STORAGE_TRX_STAT",
    "V$STORAGE_UNDO_STAT",
    "V$STORAGE_VERTEX_COUNT",
    "V$SYS_MODULE_MEM_INFO",
    "V$STORAGE_DURABLE_MEMDATA_STAT",
    "V$QRY_DML_INFO",
    "V$COM_MEM_SUMMARY",
    "V$STORAGE_BUFFERPOOL_STAT",
    "V$STORAGE_HASH_LINKLIST_INDEX_STAT",
    "V$STORAGE_HASH_INDEX_STAT",
    "V$STORAGE_HASH_CLUSTER_INDEX_STAT",
};

int g_dimList[] = {0, 1, 16, 1536, 1536, 1024, 768}; // 记录load表的维度信息

int CreateInitTable(GmeConnT *conn, int tableId = 999)
{
    GmeSqlTestCtx createSql[INIT_TABLE_NUM] = {
        {"CREATE TABLE " + tableNames[0] + "(id INT UNIQUE, age INT, weight DOUBLE, name TEXT, remark BLOB);",
            GMERR_OK,
            {}},
        {"CREATE TABLE " + tableNames[1] +
                "(id INT UNIQUE, repr1 FLOATVECTOR(1), repr2 FLOAT16VECTOR(1), repr3 FLOATVECTOR(1), repr4 "
                "FLOAT16VECTOR(1), INDEX LPASMEMPQ(2));",
            GMERR_OK,
            {}},
        {"CREATE TABLE " + tableNames[2] +
                "(id INT UNIQUE, repr1 FLOATVECTOR(16), repr2 FLOAT16VECTOR(16), repr3 FLOATVECTOR(16), repr4 "
                "FLOAT16VECTOR(16), INDEX LPASMEMPQ(2));",
            GMERR_OK,
            {}},
        {"CREATE TABLE " + tableNames[3] + "(id INT UNIQUE, repr FLOATVECTOR(1536));", GMERR_OK, {}},
        {"CREATE TABLE " + tableNames[4] + "(id INT UNIQUE, repr FLOAT16VECTOR(1536), INDEX LPASMEMPQ(1));",
            GMERR_OK,
            {}},
        {"CREATE TABLE " + tableNames[5] + "(id INT UNIQUE, repr FLOAT16VECTOR(1024));", GMERR_OK, {}},
        {"CREATE TABLE " + tableNames[6] + "(id INT UNIQUE, repr FLOATVECTOR(768), INDEX LPASMEMPQ(1));", GMERR_OK, {}},
    };

    int ret = 0;
    // 指定合法id则单表创建
    if (tableId < INIT_TABLE_NUM) {
        ret = GmeSqlStepAndCheck(conn, &createSql[tableId], 1);
        return ret;
    }

    ret = GmeSqlStepAndCheck(conn, createSql, INIT_TABLE_NUM);
    return ret;
}

int DropInitTable(GmeConnT *conn, int tableId = 999)
{
    GmeSqlTestCtx dropSql[INIT_TABLE_NUM] = {
        {"DROP TABLE IF EXISTS " + tableNames[0] + ";", GMERR_OK, {}},
        {"DROP TABLE IF EXISTS " + tableNames[1] + ";", GMERR_OK, {}},
        {"DROP TABLE IF EXISTS " + tableNames[2] + ";", GMERR_OK, {}},
        {"DROP TABLE IF EXISTS " + tableNames[3] + ";", GMERR_OK, {}},
        {"DROP TABLE IF EXISTS " + tableNames[4] + ";", GMERR_OK, {}},
        {"DROP TABLE IF EXISTS " + tableNames[5] + ";", GMERR_OK, {}},
        {"DROP TABLE IF EXISTS " + tableNames[6] + ";", GMERR_OK, {}},
    };

    int ret = 0;
    // 指定合法id则单表删除
    if (tableId < INIT_TABLE_NUM) {
        ret = GmeSqlStepAndCheck(conn, &dropSql[tableId], 1);
        return ret;
    }

    ret = GmeSqlStepAndCheck(conn, dropSql, INIT_TABLE_NUM);
    return ret;
}

void LONG_LOG_WRITE(int type, string str)  // 长稳日志输出，待完善，需要重定向日志，时间，报错语句入参
{
    printf("%s\n", str.c_str());
    return;
}

void CheckSqlReturn(int ret, string sqlStr)
{
    if (ret != GMERR_OK) {
        LONG_LOG_WRITE(LONG_LOG_ERROR, "EXECUTE SQL return " + to_string(ret) + ", which SQL is :" + sqlStr);
    }
}

void EnvClean()
{
    system("rm -rf ./data/gmdb");
    system("mkdir -p ./data/gmdb");
    system("ipcrm -a");
}

void InsertDataTableByNum(GmeConnT *conn, int tableId, int dataNum)
{
    GmeSqlTestCtx insertSql = {"", GMERR_OK, {}};
    int ret = 0;
    for (int i = 0; i < dataNum; i++) {
        if (tableId == 0) {
            return;  // 普通表，先跳过
        } else if (tableId == 1 || tableId == 2) {
            insertSql.sql = "INSERT INTO " + tableNames[tableId] + " VALUES(" + to_string(i) + ", '" +
                            RandomFloat(1, 10, g_dimList[tableId]) + "', '" + RandomFloat(1, 10, g_dimList[tableId]) +
                            "', '" + RandomFloat(1, 10, g_dimList[tableId]) + "', '" +
                            RandomFloat(1, 10, g_dimList[tableId]) + "');";
        } else {
            insertSql.sql = "INSERT INTO " + tableNames[tableId] + " VALUES(" + to_string(i) + ", '" +
                            RandomFloat(1, 10, g_dimList[tableId]) + "');";
        }
        ret = GmeSqlStepAndCheck(conn, &insertSql, 1);
        CheckSqlReturn(ret, insertSql.sql);
    }
}

void InsertTableById(GmeConnT *conn, int tableId, int dataId)
{
    GmeSqlTestCtx insertSql = {"", GMERR_OK, {}};

    if (tableId == 1 || tableId == 2) {
        insertSql.sql = "INSERT INTO " + tableNames[tableId] + " VALUES(" + to_string(dataId) + ", '" +
            RandomFloat(1, 10, g_dimList[tableId]) + "', '" + RandomFloat(1, 10, g_dimList[tableId]) + "', '" +
            RandomFloat(1, 10, g_dimList[tableId]) + "', '" + RandomFloat(1, 10, g_dimList[tableId]) + "');";
    } else {
        insertSql.sql = "INSERT INTO " + tableNames[tableId] + " VALUES(" + to_string(dataId) + ", '" +
                        RandomFloat(1, 10, g_dimList[tableId]) + "');";
    }
    int ret = GmeSqlStepAndCheck(conn, &insertSql, 1);
    CheckSqlReturn(ret, insertSql.sql);
}

void DeleteTableById(GmeConnT *conn, int tableId, int dataId)
{
    GmeSqlTestCtx deleteSql = {"", GMERR_OK, {}};

    deleteSql.sql = "DELETE FROM " + tableNames[tableId] + " WHERE id = " + to_string(dataId) + ";";

    int ret = GmeSqlStepAndCheck(conn, &deleteSql, 1);
    CheckSqlReturn(ret, deleteSql.sql);
}

void UpdateTableById(GmeConnT *conn, int tableId, int dataId)
{
    GmeSqlTestCtx updateSql = {"", GMERR_OK, {}};
    string filedName = "repr";

    // 4向量字段表，只更新随机一个字段
    if (tableId == 1 || tableId == 2) {
        int filedNum = RandomInt(1, 4);
        filedName += to_string(filedNum);
    }

    updateSql.sql = "UPDATE " + tableNames[tableId] + " SET " + filedName + " = '" +
                    RandomFloat(1, 10, g_dimList[tableId]) + "' WHERE ID = " + to_string(dataId) + ";";
    int ret = GmeSqlStepAndCheck(conn, &updateSql, 1);
    CheckSqlReturn(ret, updateSql.sql);
}

void *ThreadInsertInitTable(void *args)
{
    ThreadInertArgsT *targs = (ThreadInertArgsT *)args;

    InsertDataTableByNum(targs->conn, targs->tableId, targs->dataNum);
    return NULL;
}

int InsertInitTable(GmeConnT *conn)
{
    pthread_t threads[INIT_TABLE_NUM];
    ThreadInertArgsT args[INIT_TABLE_NUM];

    for (uint32_t i = 0; i < INIT_TABLE_NUM; i++) {
        args[i].conn = conn;
        args[i].tableId = i;
        args[i].dataNum = INIT_DATA_NUM;
        AW_MACRO_EXPECT_EQ_INT(T_OK, pthread_create(&threads[i], NULL, ThreadInsertInitTable, &args[i]));
    }

    // 等待线程结束
    for (uint32_t i = 0; i < INIT_TABLE_NUM; i++) {
        AW_MACRO_EXPECT_EQ_INT(T_OK, pthread_join(threads[i], NULL));
    }

    return GMERR_OK;
}

void CreateInitIndexByTableid(GmeConnT *conn, int tableId)
{
    GmeSqlTestCtx createIdexSql = {"", GMERR_OK, {}};
    int ret = 0;
    if (tableId == 1) {
        createIdexSql.sql =
            "CREATE INDEX index_sq_l2_on_repr1 ON " + tableNames[tableId] + " USING GSLPASMEM(repr1 L2);";
        ret = GmeSqlStepAndCheck(conn, &createIdexSql, 1);
        CheckSqlReturn(ret, createIdexSql.sql);
        createIdexSql.sql =
            "CREATE INDEX index_sq_cos_on_repr2 ON " + tableNames[tableId] + " USING GSLPASMEM(repr2 COSINE);";
        ret = GmeSqlStepAndCheck(conn, &createIdexSql, 1);
        CheckSqlReturn(ret, createIdexSql.sql);
        createIdexSql.sql = "CREATE INDEX index_pq_l2_on_repr3 ON " + tableNames[tableId] +
                            " USING GSLPASMEM(repr3 L2) WITH (ENABLE_LPAS_PQ=true);";
        ret = GmeSqlStepAndCheck(conn, &createIdexSql, 1);
        CheckSqlReturn(ret, createIdexSql.sql);
        createIdexSql.sql = "CREATE INDEX index_pq_cos_on_repr4 ON " + tableNames[tableId] +
                            " USING GSLPASMEM(repr4 COSINE) WITH (ENABLE_LPAS_PQ=true);";
        ret = GmeSqlStepAndCheck(conn, &createIdexSql, 1);
        CheckSqlReturn(ret, createIdexSql.sql);
    } else if (tableId == 2) {
        createIdexSql.sql =
            "CREATE INDEX index_sq_l2_on_repr1 ON " + tableNames[tableId] + " USING GSLPASMEM(repr1 L2);";
        ret = GmeSqlStepAndCheck(conn, &createIdexSql, 1);
        CheckSqlReturn(ret, createIdexSql.sql);
        createIdexSql.sql =
            "CREATE INDEX index_sq_cos_on_repr2 ON " + tableNames[tableId] + " USING GSLPASMEM(repr2 COSINE);";
        ret = GmeSqlStepAndCheck(conn, &createIdexSql, 1);
        CheckSqlReturn(ret, createIdexSql.sql);
        createIdexSql.sql = "CREATE INDEX index_pq_l2_on_repr3 ON " + tableNames[tableId] +
                            " USING GSLPASMEM(repr3 L2) WITH (ENABLE_LPAS_PQ=true);";
        ret = GmeSqlStepAndCheck(conn, &createIdexSql, 1);
        CheckSqlReturn(ret, createIdexSql.sql);
        createIdexSql.sql = "CREATE INDEX index_pq_cos_on_repr4 ON " + tableNames[tableId] +
                            " USING GSLPASMEM(repr4 COSINE) WITH (ENABLE_LPAS_PQ=true);";
        ret = GmeSqlStepAndCheck(conn, &createIdexSql, 1);
        CheckSqlReturn(ret, createIdexSql.sql);
    } else if (tableId == 3) {
        createIdexSql.sql = "CREATE INDEX index_sq_cos_on_repr ON " + tableNames[tableId] +
	                    " USING GSLPASMEM(repr L2);";
        ret = GmeSqlStepAndCheck(conn, &createIdexSql, 1);
        CheckSqlReturn(ret, createIdexSql.sql);
    } else if (tableId == 4) {
        createIdexSql.sql = "CREATE INDEX index_pq_cos_on_repr ON " + tableNames[tableId] +
                            " USING GSLPASMEM(repr L2) WITH (ENABLE_LPAS_PQ=true);";
        ret = GmeSqlStepAndCheck(conn, &createIdexSql, 1);
        CheckSqlReturn(ret, createIdexSql.sql);
    } else if (tableId == 5) {
        createIdexSql.sql =
            "CREATE INDEX index_sq_cos_on_repr ON " + tableNames[tableId] + " USING GSLPASMEM(repr COSINE);";
        ret = GmeSqlStepAndCheck(conn, &createIdexSql, 1);
        CheckSqlReturn(ret, createIdexSql.sql);
    } else if (tableId == 6) {
        createIdexSql.sql = "CREATE INDEX index_pq_cos_on_repr ON " + tableNames[tableId] +
                            " USING GSLPASMEM(repr COSINE) WITH (ENABLE_LPAS_PQ=true);";
        ret = GmeSqlStepAndCheck(conn, &createIdexSql, 1);
        CheckSqlReturn(ret, createIdexSql.sql);
    }
}

void DropInitIndexByTableid(GmeConnT *conn, int tableId)
{
    GmeSqlTestCtx dropIdexSql = {"", GMERR_OK, {}};
    int ret = 0;
    if (tableId == 1) {
        dropIdexSql.sql = "DROP INDEX IF EXISTST " + tableNames[tableId] + ".index_sq_l2_on_repr1;";
        ret = GmeSqlStepAndCheck(conn, &dropIdexSql, 1);
        CheckSqlReturn(ret, dropIdexSql.sql);
        dropIdexSql.sql = "DROP INDEX IF EXISTST " + tableNames[tableId] + ".index_sq_cos_on_repr2;";
        ret = GmeSqlStepAndCheck(conn, &dropIdexSql, 1);
        CheckSqlReturn(ret, dropIdexSql.sql);
        dropIdexSql.sql = "DROP INDEX IF EXISTST " + tableNames[tableId] + ".index_pq_l2_on_repr3;";
        ret = GmeSqlStepAndCheck(conn, &dropIdexSql, 1);
        CheckSqlReturn(ret, dropIdexSql.sql);
        dropIdexSql.sql = "DROP INDEX IF EXISTST " + tableNames[tableId] + ".index_pq_cos_on_repr4;";
        ret = GmeSqlStepAndCheck(conn, &dropIdexSql, 1);
        CheckSqlReturn(ret, dropIdexSql.sql);
    } else if (tableId == 2) {
        dropIdexSql.sql = "DROP INDEX IF EXISTST " + tableNames[tableId] + ".index_sq_l2_on_repr1;";
        ret = GmeSqlStepAndCheck(conn, &dropIdexSql, 1);
        CheckSqlReturn(ret, dropIdexSql.sql);
        dropIdexSql.sql = "DROP INDEX IF EXISTST " + tableNames[tableId] + ".index_sq_cos_on_repr2;";
        ret = GmeSqlStepAndCheck(conn, &dropIdexSql, 1);
        CheckSqlReturn(ret, dropIdexSql.sql);
        dropIdexSql.sql = "DROP INDEX IF EXISTST " + tableNames[tableId] + ".index_pq_l2_on_repr3;";
        ret = GmeSqlStepAndCheck(conn, &dropIdexSql, 1);
        CheckSqlReturn(ret, dropIdexSql.sql);
        dropIdexSql.sql = "DROP INDEX IF EXISTST " + tableNames[tableId] + ".index_pq_cos_on_repr4;";
        ret = GmeSqlStepAndCheck(conn, &dropIdexSql, 1);
        CheckSqlReturn(ret, dropIdexSql.sql);
    } else if (tableId == 3) {
        dropIdexSql.sql = "DROP INDEX IF EXISTST " + tableNames[tableId] + ".index_sq_cos_on_repr;";
        ret = GmeSqlStepAndCheck(conn, &dropIdexSql, 1);
        CheckSqlReturn(ret, dropIdexSql.sql);
    } else if (tableId == 4) {
        dropIdexSql.sql = "DROP INDEX IF EXISTST " + tableNames[tableId] + ".index_pq_cos_on_repr;";
        ret = GmeSqlStepAndCheck(conn, &dropIdexSql, 1);
        CheckSqlReturn(ret, dropIdexSql.sql);
    } else if (tableId == 5) {
        dropIdexSql.sql = "DROP INDEX IF EXISTST " + tableNames[tableId] + ".index_sq_cos_on_repr;";
        ret = GmeSqlStepAndCheck(conn, &dropIdexSql, 1);
        CheckSqlReturn(ret, dropIdexSql.sql);
    } else if (tableId == 6) {
        dropIdexSql.sql = "DROP INDEX IF EXISTST " + tableNames[tableId] + ".index_pq_cos_on_repr;";
        ret = GmeSqlStepAndCheck(conn, &dropIdexSql, 1);
        CheckSqlReturn(ret, dropIdexSql.sql);
    }
}

void *ThreadCreateInitIndex(void *args)
{
    ThreadCreateIndexArgsT *targs = (ThreadCreateIndexArgsT *)args;
    CreateInitIndexByTableid(targs->conn, targs->tableId);
    return NULL;
}

int CreateInitIndex(GmeConnT *conn)
{
    pthread_t threads[INIT_TABLE_NUM];
    ThreadCreateIndexArgsT args[INIT_TABLE_NUM];

    for (uint32_t i = 1; i < INIT_TABLE_NUM; i++) {
        args[i].conn = conn;
        args[i].tableId = i;
        AW_MACRO_EXPECT_EQ_INT(T_OK, pthread_create(&threads[i], NULL, ThreadCreateInitIndex, &args[i]));
    }
    // 等待线程结束
    for (uint32_t i = 1; i < INIT_TABLE_NUM; i++) {
        AW_MACRO_EXPECT_EQ_INT(T_OK, pthread_join(threads[i], NULL));
    }

    return GMERR_OK;
}

/* 依据表id进行向量索引检索 */
void VectorIndexScanInInitTable(GmeConnT *conn, int tableId)
{
    string VecStr = RandomFloat(1, 10, g_dimList[tableId]);
    GmeSqlTestCtx selectSql = {"", GMERR_OK, {}};
    int ret = 0;

    if (tableId == 1) {
        selectSql.sql = "SELECT * FROM " + tableNames[tableId] + " ORDER BY repr1 <-> '" + VecStr + "' LIMIT 10;";
        ret = GmeSqlStepAndCheck(conn, &selectSql, 1);
        CheckSqlReturn(ret, selectSql.sql);
        selectSql.sql = "SELECT * FROM " + tableNames[tableId] + " ORDER BY repr2 <=> '" + VecStr + "' LIMIT 10;";
        ret = GmeSqlStepAndCheck(conn, &selectSql, 1);
        CheckSqlReturn(ret, selectSql.sql);
        selectSql.sql =
            "SELECT * FROM " + tableNames[tableId] + " ORDER BY repr3 <-> '" + VecStr + "' LIMIT 10;";  // PQ
        ret = GmeSqlStepAndCheck(conn, &selectSql, 1);
        CheckSqlReturn(ret, selectSql.sql);
        selectSql.sql =
            "SELECT * FROM " + tableNames[tableId] + " ORDER BY repr4 <=> '" + VecStr + "' LIMIT 10;";  // PQ
        ret = GmeSqlStepAndCheck(conn, &selectSql, 1);
        CheckSqlReturn(ret, selectSql.sql);
    } else if (tableId == 2) {
        selectSql.sql = "SELECT * FROM " + tableNames[tableId] + " ORDER BY repr1 <-> '" + VecStr + "' LIMIT 10;";
        ret = GmeSqlStepAndCheck(conn, &selectSql, 1);
        CheckSqlReturn(ret, selectSql.sql);
        selectSql.sql = "SELECT * FROM " + tableNames[tableId] + " ORDER BY repr2 <=> '" + VecStr + "' LIMIT 10;";
        ret = GmeSqlStepAndCheck(conn, &selectSql, 1);
        CheckSqlReturn(ret, selectSql.sql);
        selectSql.sql =
            "SELECT * FROM " + tableNames[tableId] + " ORDER BY repr3 <-> '" + VecStr + "' LIMIT 10;";  // PQ
        ret = GmeSqlStepAndCheck(conn, &selectSql, 1);
        CheckSqlReturn(ret, selectSql.sql);
        selectSql.sql =
            "SELECT * FROM " + tableNames[tableId] + " ORDER BY repr4 <=> '" + VecStr + "' LIMIT 10;";  // PQ
        ret = GmeSqlStepAndCheck(conn, &selectSql, 1);
        CheckSqlReturn(ret, selectSql.sql);
    } else if (tableId == 3) {
        selectSql.sql = "SELECT * FROM " + tableNames[tableId] + " ORDER BY repr <-> '" + VecStr + "' LIMIT 10;";
        ret = GmeSqlStepAndCheck(conn, &selectSql, 1);
        CheckSqlReturn(ret, selectSql.sql);
    } else if (tableId == 4) {
        selectSql.sql = "SELECT * FROM " + tableNames[tableId] + " ORDER BY repr <-> '" + VecStr + "' LIMIT 10;";  // PQ
        ret = GmeSqlStepAndCheck(conn, &selectSql, 1);
        CheckSqlReturn(ret, selectSql.sql);
    } else if (tableId == 5) {
        selectSql.sql = "SELECT * FROM " + tableNames[tableId] + " ORDER BY repr <=> '" + VecStr + "' LIMIT 10;";
        ret = GmeSqlStepAndCheck(conn, &selectSql, 1);
        CheckSqlReturn(ret, selectSql.sql);
    } else if (tableId == 6) {
        selectSql.sql = "SELECT * FROM " + tableNames[tableId] + " ORDER BY repr <=> '" + VecStr + "' LIMIT 10;";  // PQ
        ret = GmeSqlStepAndCheck(conn, &selectSql, 1);
        CheckSqlReturn(ret, selectSql.sql);
    }
}

/* 依据表id 进行随机的DML */
void DMLToInitTable(GmeConnT *conn, int tableId)
{
    int randomIndexList[DML_DATA_NUM];

    // 随机获取10个id，对这些id进行删除，然后重新插入同id数据
    for (int i = 0; i < DML_DATA_NUM; i++) {
        randomIndexList[i] = RandomInt(0, INIT_DATA_NUM);
    }

    for (int i = 0; i < DML_DATA_NUM; i++) {
        DeleteTableById(conn, tableId, randomIndexList[i]);
    }

    for (int i = 0; i < DML_DATA_NUM; i++) {
        InsertTableById(conn, tableId, randomIndexList[i]);
    }

    // 重新获取10个id，进行随机一个向量字段修改
    for (int i = 0; i < DML_DATA_NUM; i++) {
        randomIndexList[i] = RandomInt(0, INIT_DATA_NUM);
    }

    for (int i = 0; i < DML_DATA_NUM; i++) {
        UpdateTableById(conn, tableId, randomIndexList[i]);
    }
}

/* 随机10条数据简单查询 */
void VectorSimpleScanInitTableInitTable(GmeConnT *conn, int tableId)
{
    GmeSqlTestCtx selectSql = {"", GMERR_OK, {}};
    int ret = 0;
    int randomIndexList[DML_DATA_NUM];

    for (int i = 0; i < DML_DATA_NUM; i++) {
        randomIndexList[i] = RandomInt(0, INIT_DATA_NUM);
    }

    for (int i = 0; i < DML_DATA_NUM; i++) {
        selectSql.sql = "SELECT * FROM " + tableNames[tableId] + " WHERE ID = " + to_string(randomIndexList[i]) + ";";
        ret = GmeSqlStepAndCheck(conn, &selectSql, 1);
    }
}

void QueryAllSystemView(GmeConnT *conn)
{
    GmeSqlTestCtx selectSql = {"", GMERR_OK, {}};

    int ret = 0;
    for (const auto &item : ViewListDoubleMode) {
        selectSql.sql = "SELECT * FROM '" + item + "';";
        ret = GmeSqlStepAndCheck(conn, &selectSql, 1);
        CheckSqlReturn(ret, selectSql.sql);
    }
}

/* 随机10条数据进行聚合函数查询 */
void AggregateFuncScanInitTable(GmeConnT *conn, int tableId)
{
    GmeSqlTestCtx selectSql = {"", GMERR_OK, {}};
    int ret = 0;
    int randomIndexList[DML_DATA_NUM];

    for (int i = 0; i < DML_DATA_NUM; i++) {
        randomIndexList[i] = RandomInt(0, INIT_DATA_NUM);
    }

    for (int i = 0; i < DML_DATA_NUM; i++) {
        selectSql.sql = "SELECT COUNT(*) FROM " + tableNames[tableId] + ";";  // 是否需要规避星号检索
        ret = GmeSqlStepAndCheck(conn, &selectSql, 1);
        selectSql.sql = "SELECT AVG(id) FROM " + tableNames[tableId] + ";";
        ret = GmeSqlStepAndCheck(conn, &selectSql, 1);
        selectSql.sql = "SELECT MAX(id) FROM " + tableNames[tableId] + ";";
        ret = GmeSqlStepAndCheck(conn, &selectSql, 1);
        selectSql.sql = "SELECT MIN(id) FROM " + tableNames[tableId] + ";";
        ret = GmeSqlStepAndCheck(conn, &selectSql, 1);
        selectSql.sql = "SELECT SUM(id) FROM " + tableNames[tableId] + ";";
        ret = GmeSqlStepAndCheck(conn, &selectSql, 1);
    }
}

#endif /* KBDB_LONG_STABILITY_H */
