/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: 支持视图加固测试
 * Author: zhangjianyu
 * Create: 2025-07-07
 */
#include "gtest/gtest.h"
#include "common_base.h"
#include "common_sql.h"
#include "common_vec.h"
#include "db_json_common.h"
#include <string>
#include <vector>
#include <random>
#include <ctime>
#include <future>
#include <stdexcept>

using namespace std;
using namespace testing::ext;

static constexpr uint16_t JSON_REJECT_DUPLICATES = 0x1;

GmeSqlTestCtx sqlSetup[] = {
    // 创建表t1,预置数据
    {"CREATE TABLE t1(id int primary key, type int, city text, repr floatvector(3));", GMERR_OK, {}},
    {"CREATE TABLE t2(id int primary key, type int, city text, repr floatvector(4));", GMERR_OK, {}},
    {"CREATE TABLE t3(id int primary key, type int, city text, repr floatvector(512));", GMERR_OK, {}},
    {"CREATE TABLE t4(id int primary key, type int, city text, repr floatvector(1024));", GMERR_OK, {}},
    {"INSERT INTO t1 VALUES(1, 101, 'beijing', '[2.4,3.3,4.1]');", GMERR_OK, {}},
    {"INSERT INTO t1 VALUES(2, 102, 'shanghai', '[4.4,3.3,7.1]');", GMERR_OK, {}},
    {"INSERT INTO t1 VALUES(3, 201, 'shenzhen', '[3.7,46.7,24.1]');", GMERR_OK, {}},
    {"INSERT INTO t1 VALUES(4, 202, 'guangzhou', '[5.2,3.1,5.2]');", GMERR_OK, {}},
    {"CREATE INDEX lpasmem_cos_idx ON t1 USING GSLPASMEM(repr COSINE);", GMERR_OK, {}},
    {"CREATE INDEX lpasmem_l2_idx ON t2 USING GSLPASMEM(repr L2);", GMERR_OK, {}},
    {"CREATE INDEX lpasmem_cos_idx1 ON t3 USING GSLPASMEM(repr COSINE);", GMERR_OK, {}},
};

GmeSqlTestCtx sqlRelease[] = {
    // 删除表t1
    {"DROP TABLE t1;", GMERR_OK, {}},
    {"DROP TABLE t2;", GMERR_OK, {}},
    {"DROP TABLE t3;", GMERR_OK, {}},
    {"DROP TABLE t4;", GMERR_OK, {}},
};

class TestDfxViewAdd : public testing::Test {
public:
    GmeConnT *conn = NULL;
    char *configPath = EmbSqlGetConfigPath();
    virtual void SetUp()
    {
        system("rm -rf ./data/gmdb");
        system("mkdir -p ./data/gmdb");
        system("ipcrm -a");
        
    }

    virtual void TearDown()
    {
        int ret = GmeSqlStepAndCheck(conn, sqlRelease, 4);
        AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
        ret = GmeClose(conn);
        AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    }
};

struct ViewResultSet {
    std::vector<std::string> colNames;
    std::vector<std::vector<std::string>> colValues;
    ViewResultSet() : colNames(), colValues()
    {}
};

static Status EmbSqlViewResultSet(void *data, uint16_t colCnt, char **colValues, char **colNames)
{
    ViewResultSet *resultSet = static_cast<ViewResultSet *>(data);
    if (resultSet->colNames.empty()) {
        for (int i = 0; i < colCnt; ++i) {
            resultSet->colNames.emplace_back(colNames[i]);
        }
    }
    std::vector<std::string> currentValues;
    for (int i = 0; i < colCnt; ++i) {
        currentValues.emplace_back(colValues[i]);
    }
    resultSet->colValues.push_back(currentValues);
    return GMERR_OK;
}

//获取视图索引
size_t findColumnIndex(const ViewResultSet& resultSet, const std::string& fieldName) {
    for (size_t i = 0; i < resultSet.colNames.size(); ++i) {
        if (resultSet.colNames[i] == fieldName) {
            return i;
        }
    }
    throw std::invalid_argument("Field not found: " + fieldName);
}


//获取字段值
std::string getValue(const ViewResultSet& resultSet, const std::string& fieldName, size_t rowIndex) {
    size_t columnIndex = findColumnIndex(resultSet, fieldName);
    if (rowIndex >= resultSet.colValues.size()) {
        throw std::out_of_range("Row index out of range");
    }
    const std::vector<std::string>& row = resultSet.colValues[rowIndex];
    if (columnIndex >= row.size()) {
        throw std::out_of_range("Column index out of range");
    }
    return row[columnIndex];
}

//将字符串值转换为整数
int convertToInt(const std::string& value) {
    try {
        return std::stoi(value);
    } catch (const std::invalid_argument& e) {
        throw std::invalid_argument("Invalid integer value: " + value);
    } catch (const std::out_of_range& e) {
        throw std::out_of_range("Integer value out of range: " + value);
    }
}

//将字符串值转换为浮点数
double convertToDouble(const std::string& value) {
    try {
        return std::stod(value);
    } catch (const std::invalid_argument& e) {
        throw std::invalid_argument("Invalid double value: " + value);
    } catch (const std::out_of_range& e) {
        throw std::out_of_range("Double value out of range: " + value);
    }
}

//根据字段名和行索引，获取字段值，并返回
std::string getStringField(const ViewResultSet& resultSet, const std::string& fieldName, size_t rowIndex) {
    std::string value = getValue(resultSet, fieldName, rowIndex);
    return value;
}

//根据字段名和行索引，获取字段值，并返回整型
int getIntField(const ViewResultSet& resultSet, const std::string& fieldName, size_t rowIndex) {
    std::string value = getValue(resultSet, fieldName, rowIndex);
    return convertToInt(value);
}

//根据字段名和行索引，获取字段值，并返回双精度浮点型
double getDoubleField(const ViewResultSet& resultSet, const std::string& fieldName, size_t rowIndex){
    std::string value = getValue(resultSet, fieldName, rowIndex);
    return convertToDouble(value);
}

//两数相除返回双精度浮点型
double divideAndReturnDouble(int numerator, int denominator) {
    if (denominator == 0) {
        return -1;
    }
    return static_cast<double>(numerator) / denominator;
}


//查询视图COM_SHMEM_CTX，查询视图内字段，配置enableVerticalIsolation=0，预期成功
HWTEST_F(TestDfxViewAdd, KBVector_012_031, TestSize.Level1)
{
    ComWriteLog(LOG_STEP, "test start.");
    int ret = ModifyCfgFile("featureNames=BUFFERPOOL,SQL,TRM,PERSISTENCE", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ret = GmeSqlStepAndCheck(conn, sqlSetup, 11);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ViewResultSet resultSet;
    string SqlSeView = "select * from 'V$COM_SHMEM_CTX';";
    ret = GmeSqlExecute(conn, SqlSeView.c_str(), EmbSqlViewResultSet, &resultSet, nullptr);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    std::string value = getStringField(resultSet, "GROUP_ID", 0);
    EXPECT_EQ(value, "null");
    value = getStringField(resultSet, "METHOD_TYPE", 0);
    EXPECT_EQ(value, "ALGO_BLOCK");
}

//查询视图COM_SHMEM_USAGE_STAT，查询视图内字段，预期成功
HWTEST_F(TestDfxViewAdd, KBVector_012_032, TestSize.Level1)
{
    ComWriteLog(LOG_STEP, "test start.");
    int ret = ModifyCfgFile("featureNames=BUFFERPOOL,SQL,TRM,PERSISTENCE", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ret = GmeSqlStepAndCheck(conn, sqlSetup, 11);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ViewResultSet resultSet;
    ShowViewInfo(conn, "COM_SHMEM_USAGE_STAT");
    string SqlSeView = "select * from 'V$COM_SHMEM_USAGE_STAT';";
    ret = GmeSqlExecute(conn, SqlSeView.c_str(), EmbSqlViewResultSet, &resultSet, nullptr);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    size_t i = findColumnIndex(resultSet, "TOTAL_ALLOC_SIZE");
    EXPECT_FALSE(resultSet.colValues[0][i].empty());
    size_t j = findColumnIndex(resultSet, "TOTAL_PHY_SIZE");
    EXPECT_FALSE(resultSet.colValues[0][j].empty());
    size_t k = findColumnIndex(resultSet, "MEMORY_USAGE");
    EXPECT_FALSE(resultSet.colValues[0][k].empty());
}

//查询视图COM_TABLE_MEM_SUMMARY，查询视图内字段，预期成功
HWTEST_F(TestDfxViewAdd, KBVector_012_033, TestSize.Level1)
{
    ComWriteLog(LOG_STEP, "test start.");
    int ret = ModifyCfgFile("featureNames=BUFFERPOOL,SQL,TRM,PERSISTENCE", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ret = GmeSqlStepAndCheck(conn, sqlSetup, 11);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ViewResultSet resultSet;
    string SqlSeView = "select * from 'V$COM_TABLE_MEM_SUMMARY';";
    ret = GmeSqlExecute(conn, SqlSeView.c_str(), EmbSqlViewResultSet, &resultSet, nullptr);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    size_t i = findColumnIndex(resultSet, "DATA_PAGE_TOTAL_SIZE");
    EXPECT_FALSE(resultSet.colValues[0][i].empty());
    size_t j = findColumnIndex(resultSet, "PAGE_SIZE");
    EXPECT_FALSE(resultSet.colValues[0][j].empty());
    size_t k = findColumnIndex(resultSet, "TOTAL_SIZE");
    EXPECT_FALSE(resultSet.colValues[0][k].empty());
}

//查询视图CST_SHMEM_INFO，查询视图内字段，预期成功
HWTEST_F(TestDfxViewAdd, KBVector_012_034, TestSize.Level1)
{
    ComWriteLog(LOG_STEP, "test start.");
    int ret = ModifyCfgFile("featureNames=BUFFERPOOL,SQL,TRM,PERSISTENCE", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ret = GmeSqlStepAndCheck(conn, sqlSetup, 11);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ViewResultSet resultSet;
    string SqlSeView = "select * from 'V$CST_SHMEM_INFO';";
    ret = GmeSqlExecute(conn, SqlSeView.c_str(), EmbSqlViewResultSet, &resultSet, nullptr);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    size_t i = findColumnIndex(resultSet, "TOTAL_ALLOC_SIZE");
    EXPECT_FALSE(resultSet.colValues[0][i].empty());
    size_t j = findColumnIndex(resultSet, "TOTAL_PHY_SIZE");
    EXPECT_FALSE(resultSet.colValues[0][j].empty());
    size_t k = findColumnIndex(resultSet, "MEMORY_USAGE");
    EXPECT_FALSE(resultSet.colValues[0][k].empty());
}

//005.查询视图COM_SHMEM_GROUP，查询视图内字段，预期成功
HWTEST_F(TestDfxViewAdd, KBVector_012_035, TestSize.Level1)
{
    ComWriteLog(LOG_STEP, "test start.");
    int ret = ModifyCfgFile("featureNames=BUFFERPOOL,SQL,TRM,PERSISTENCE", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ret = GmeSqlStepAndCheck(conn, sqlSetup, 11);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ViewResultSet resultSet;
    ShowViewInfo(conn, "COM_SHMEM_GROUP");
    string SqlSeView = "select * from 'V$COM_SHMEM_GROUP';";
    ret = GmeSqlExecute(conn, SqlSeView.c_str(), EmbSqlViewResultSet, &resultSet, nullptr);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

//006.查询视图MEM_COMPACT_TASKS_STAT，查询视图内字段，预期成功
HWTEST_F(TestDfxViewAdd, KBVector_012_036, TestSize.Level1)
{
    ComWriteLog(LOG_STEP, "test start.");
    int ret = ModifyCfgFile("featureNames=BUFFERPOOL,SQL,TRM,PERSISTENCE", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ret = GmeSqlStepAndCheck(conn, sqlSetup, 11);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ViewResultSet resultSet;
    ShowViewInfo(conn, "MEM_COMPACT_TASKS_STAT");
    string SqlSeView = "select * from 'V$MEM_COMPACT_TASKS_STAT';";
    ret = GmeSqlExecute(conn, SqlSeView.c_str(), EmbSqlViewResultSet, &resultSet, nullptr);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

//007.查询视图STORAGE_ART_INDEX_STAT，数存场景支持查询
HWTEST_F(TestDfxViewAdd, KBVector_012_037, TestSize.Level1)
{
    ComWriteLog(LOG_STEP, "test start.");
    int ret = ModifyCfgFile("featureNames=BUFFERPOOL,SQL,TRM,PERSISTENCE", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ret = GmeSqlStepAndCheck(conn, sqlSetup, 11);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ViewResultSet resultSet;
    ShowViewInfo(conn, "STORAGE_ART_INDEX_STAT");
    string SqlSeView = "select * from 'V$STORAGE_ART_INDEX_STAT';";
    ret = GmeSqlExecute(conn, SqlSeView.c_str(), EmbSqlViewResultSet, &resultSet, nullptr);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

//008.查询视图STORAGE_HASH_CLUSTER_INDEX_STAT，数存场景支持查询
HWTEST_F(TestDfxViewAdd, KBVector_012_038, TestSize.Level1)
{
    ComWriteLog(LOG_STEP, "test start.");
    int ret = ModifyCfgFile("featureNames=BUFFERPOOL,SQL,TRM,PERSISTENCE", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ret = GmeSqlStepAndCheck(conn, sqlSetup, 11);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ViewResultSet resultSet;
    ShowViewInfo(conn, "STORAGE_HASH_CLUSTER_INDEX_STAT");
    string SqlSeView = "select * from 'V$STORAGE_HASH_CLUSTER_INDEX_STAT';";
    ret = GmeSqlExecute(conn, SqlSeView.c_str(), EmbSqlViewResultSet, &resultSet, nullptr);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

//009.查询视图STORAGE_HASH_INDEX_STAT，数存场景支持查询
HWTEST_F(TestDfxViewAdd, KBVector_012_039, TestSize.Level1)
{
    ComWriteLog(LOG_STEP, "test start.");
    int ret = ModifyCfgFile("featureNames=BUFFERPOOL,SQL,TRM,PERSISTENCE", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ret = GmeSqlStepAndCheck(conn, sqlSetup, 11);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ViewResultSet resultSet;
    ShowViewInfo(conn, "STORAGE_HASH_CLUSTER_INDEX_STAT");
    string SqlSeView = "select * from 'V$STORAGE_HASH_INDEX_STAT';";
    ret = GmeSqlExecute(conn, SqlSeView.c_str(), EmbSqlViewResultSet, &resultSet, nullptr);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

//010.查询视图STORAGE_HASH_LINKLIST_INDEX_STAT，数存场景支持查询
HWTEST_F(TestDfxViewAdd, KBVector_012_040, TestSize.Level1)
{
    ComWriteLog(LOG_STEP, "test start.");
    int ret = ModifyCfgFile("featureNames=BUFFERPOOL,SQL,TRM,PERSISTENCE", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ret = GmeSqlStepAndCheck(conn, sqlSetup, 11);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ViewResultSet resultSet;
    ShowViewInfo(conn, "STORAGE_HASH_CLUSTER_INDEX_STAT");
    string SqlSeView = "select * from 'V$STORAGE_HASH_CLUSTER_INDEX_STAT';";
    ret = GmeSqlExecute(conn, SqlSeView.c_str(), EmbSqlViewResultSet, &resultSet, nullptr);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

//011.查询视图STORAGE_TABLE_SHM_INFO，预期查询失败
HWTEST_F(TestDfxViewAdd, KBVector_012_041, TestSize.Level1)
{
    ComWriteLog(LOG_STEP, "test start.");
    int ret = ModifyCfgFile("featureNames=BUFFERPOOL,SQL,TRM,PERSISTENCE", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ret = GmeSqlStepAndCheck(conn, sqlSetup, 11);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ViewResultSet resultSet;
    ShowViewInfo(conn, "STORAGE_TABLE_SHM_INFO");
    string SqlSeView = "select * from 'V$STORAGE_TABLE_SHM_INFO';";
    ret = GmeSqlExecute(conn, SqlSeView.c_str(), EmbSqlViewResultSet, &resultSet, nullptr);
    AW_MACRO_ASSERT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
}

//012.查询视图COM_MEM_SUMMARY，预期查询失败
HWTEST_F(TestDfxViewAdd, KBVector_012_042, TestSize.Level1)
{
    ComWriteLog(LOG_STEP, "test start.");
    int ret = ModifyCfgFile("featureNames=BUFFERPOOL,SQL,TRM,PERSISTENCE", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ret = GmeSqlStepAndCheck(conn, sqlSetup, 11);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ViewResultSet resultSet;
    ShowViewInfo(conn, "COM_MEM_SUMMARY");
    string SqlSeView = "select * from 'V$COM_MEM_SUMMARY';";
    ret = GmeSqlExecute(conn, SqlSeView.c_str(), EmbSqlViewResultSet, &resultSet, nullptr);
    AW_MACRO_ASSERT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
}

//013.查询视图SERVER_MEMORY_OVERHEAD，预期查询失败
HWTEST_F(TestDfxViewAdd, KBVector_012_043, TestSize.Level1)
{
    ComWriteLog(LOG_STEP, "test start.");
    int ret = ModifyCfgFile("featureNames=BUFFERPOOL,SQL,TRM,PERSISTENCE", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ret = GmeSqlStepAndCheck(conn, sqlSetup, 11);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ViewResultSet resultSet;
    ShowViewInfo(conn, "SERVER_MEMORY_OVERHEAD");
    string SqlSeView = "select * from 'V$SERVER_MEMORY_OVERHEAD';";
    ret = GmeSqlExecute(conn, SqlSeView.c_str(), EmbSqlViewResultSet, &resultSet, nullptr);
    AW_MACRO_ASSERT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
}

//014.查询视图STORAGE_SHMEM_INFO，预期查询失败
HWTEST_F(TestDfxViewAdd, KBVector_012_044, TestSize.Level1)
{
    ComWriteLog(LOG_STEP, "test start.");
    int ret = ModifyCfgFile("featureNames=BUFFERPOOL,SQL,TRM,PERSISTENCE", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ret = GmeSqlStepAndCheck(conn, sqlSetup, 11);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ViewResultSet resultSet;
    ShowViewInfo(conn, "STORAGE_SHMEM_INFO");
    string SqlSeView = "select * from 'V$STORAGE_SHMEM_INFO';";
    ret = GmeSqlExecute(conn, SqlSeView.c_str(), EmbSqlViewResultSet, &resultSet, nullptr);
    AW_MACRO_ASSERT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
}

//015.查询视图DYN_MEM_TRACE_INFO，预期查询失败
HWTEST_F(TestDfxViewAdd, KBVector_012_045, TestSize.Level1)
{
    ComWriteLog(LOG_STEP, "test start.");
    int ret = ModifyCfgFile("featureNames=BUFFERPOOL,SQL,TRM,PERSISTENCE", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ret = GmeSqlStepAndCheck(conn, sqlSetup, 11);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ViewResultSet resultSet;
    ShowViewInfo(conn, "DYN_MEM_TRACE_INFO");
    string SqlSeView = "select * from 'V$DYN_MEM_TRACE_INFO';";
    ret = GmeSqlExecute(conn, SqlSeView.c_str(), EmbSqlViewResultSet, &resultSet, nullptr);
    AW_MACRO_ASSERT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
}

//016.查询视图SHMEM_TRACE_INFO，预期查询失败
HWTEST_F(TestDfxViewAdd, KBVector_012_046, TestSize.Level1)
{
    ComWriteLog(LOG_STEP, "test start.");
    int ret = ModifyCfgFile("featureNames=BUFFERPOOL,SQL,TRM,PERSISTENCE", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ret = GmeSqlStepAndCheck(conn, sqlSetup, 11);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ViewResultSet resultSet;
    ShowViewInfo(conn, "SHMEM_TRACE_INFO");
    string SqlSeView = "select * from 'V$SHMEM_TRACE_INFO';";
    ret = GmeSqlExecute(conn, SqlSeView.c_str(), EmbSqlViewResultSet, &resultSet, nullptr);
    AW_MACRO_ASSERT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
}

//017.查询视图QRY_DYNMEM，查询视图内字段，预期成功
HWTEST_F(TestDfxViewAdd, KBVector_012_047, TestSize.Level1)
{
    ComWriteLog(LOG_STEP, "test start.");
    int ret = ModifyCfgFile("featureNames=BUFFERPOOL,SQL,TRM,PERSISTENCE", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ret = GmeSqlStepAndCheck(conn, sqlSetup, 11);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ViewResultSet resultSet;
    string SqlSeView = "select * from 'V$QRY_DYNMEM';";
    ret = GmeSqlExecute(conn, SqlSeView.c_str(), EmbSqlViewResultSet, &resultSet, nullptr);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    size_t i = findColumnIndex(resultSet, "BIG_OBJ_AVRAGE_DYNMEM");
    EXPECT_FALSE(resultSet.colValues[0][i].empty());
    size_t j = findColumnIndex(resultSet, "BIG_OBJ_MAX_DYNMEM");
    EXPECT_FALSE(resultSet.colValues[0][j].empty());
}

//018.查询视图STORAGE_HEAP_VERTEX_LABEL_STAT，查询视图内字段，预期成功
HWTEST_F(TestDfxViewAdd, KBVector_012_048, TestSize.Level1)
{
    ComWriteLog(LOG_STEP, "test start.");
    int ret = ModifyCfgFile("featureNames=BUFFERPOOL,SQL,TRM,PERSISTENCE", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ret = GmeSqlStepAndCheck(conn, sqlSetup, 11);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ViewResultSet resultSet;
    string SqlSeView = "select * from 'V$STORAGE_HEAP_VERTEX_LABEL_STAT';";
    ret = GmeSqlExecute(conn, SqlSeView.c_str(), EmbSqlViewResultSet, &resultSet, nullptr);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    size_t i = findColumnIndex(resultSet, "DATA_PAGE_UNUSED_SIZE");
    EXPECT_FALSE(resultSet.colValues[0][i].empty());
    size_t j = findColumnIndex(resultSet, "DATA_PAGE_UTILIZATION_RATIO");
    EXPECT_FALSE(resultSet.colValues[0][j].empty());
}

//019.查询视图STORAGE_UNDO_STAT，查询视图内字段，预期成功
HWTEST_F(TestDfxViewAdd, KBVector_012_049, TestSize.Level1)
{
    ComWriteLog(LOG_STEP, "test start.");
    int ret = ModifyCfgFile("featureNames=BUFFERPOOL,SQL,TRM,PERSISTENCE", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ret = GmeSqlStepAndCheck(conn, sqlSetup, 11);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ViewResultSet resultSet;
    string SqlSeView = "select * from 'V$STORAGE_UNDO_STAT';";
    ret = GmeSqlExecute(conn, SqlSeView.c_str(), EmbSqlViewResultSet, &resultSet, nullptr);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    size_t i = findColumnIndex(resultSet, "TOTAL_UNDOLOG_SIZE");
    EXPECT_FALSE(resultSet.colValues[0][i].empty());
    size_t j = findColumnIndex(resultSet, "MEMORY_UNUSED_SIZE");
    EXPECT_FALSE(resultSet.colValues[0][j].empty());
}

//020.查询视图SYS_MODULE_MEM_INFO，查询视图内字段，预期成功
HWTEST_F(TestDfxViewAdd, KBVector_012_050, TestSize.Level1)
{
    ComWriteLog(LOG_STEP, "test start.");
    int ret = ModifyCfgFile("featureNames=BUFFERPOOL,SQL,TRM,PERSISTENCE", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ret = GmeSqlStepAndCheck(conn, sqlSetup, 11);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ViewResultSet resultSet;
    string SqlSeView = "select * from 'V$SYS_MODULE_MEM_INFO';";
    ret = GmeSqlExecute(conn, SqlSeView.c_str(), EmbSqlViewResultSet, &resultSet, nullptr);
    size_t i = findColumnIndex(resultSet, "MODULE_NAME");
    EXPECT_FALSE(resultSet.colValues[0][i].empty());
    size_t j = findColumnIndex(resultSet, "TOTAL_SIZE");
    EXPECT_FALSE(resultSet.colValues[0][j].empty());
}

//021.查询视图CATA_VERTEX_LABEL_INFO，查询视图内字段，预期成功
HWTEST_F(TestDfxViewAdd, KBVector_012_051, TestSize.Level1)
{
    ComWriteLog(LOG_STEP, "test start.");
    int ret = ModifyCfgFile("featureNames=BUFFERPOOL,SQL,TRM,PERSISTENCE", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ret = GmeSqlStepAndCheck(conn, sqlSetup, 11);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ViewResultSet resultSet;
    ShowViewInfo(conn, "CATA_VERTEX_LABEL_INFO");
    string SqlSeView = "select * from 'V$CATA_VERTEX_LABEL_INFO';";
    ret = GmeSqlExecute(conn, SqlSeView.c_str(), EmbSqlViewResultSet, &resultSet, nullptr);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    std::string value = getStringField(resultSet, "VERTEX_LABEL_TYPE", 0);
    EXPECT_EQ(value, "VERTEX_TYPE_SYSTABLE");
}

//022.查询视图QRY_DML_OPER_STATIS，查询视图内字段，预期成功
HWTEST_F(TestDfxViewAdd, KBVector_012_052, TestSize.Level1)
{
    ComWriteLog(LOG_STEP, "test start.");
    int ret = ModifyCfgFile("featureNames=BUFFERPOOL,SQL,TRM,PERSISTENCE", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ret = GmeSqlStepAndCheck(conn, sqlSetup, 11);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ViewResultSet resultSet;
    ShowViewInfo(conn, "QRY_DML_OPER_STATIS");
    string SqlSeView = "select * from 'V$QRY_DML_OPER_STATIS';";
    ret = GmeSqlExecute(conn, SqlSeView.c_str(), EmbSqlViewResultSet, &resultSet, nullptr);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    size_t i = findColumnIndex(resultSet, "DW_STATIS_INFO");
    DbJsonT *json = DbJsonLoads(resultSet.colValues[0][i].c_str(), JSON_REJECT_DUPLICATES);
    DbJsonT *item = DbJsonObjectGet(DbJsonArrayGet(json, 0), "DML_TYPE");
    EXPECT_TRUE(DbJsonIsString(item));
    EXPECT_FALSE(string(DbJsonStringValue(item)).empty());
    DbJsonDelete(json);
}

//023.查询视图QRY_DML_INFO，配置enableDmlPerfStat，enableDmlOperStat后查询成功，关库后开库不配置，查询失败
HWTEST_F(TestDfxViewAdd, KBVector_012_053, TestSize.Level1)
{
    ComWriteLog(LOG_STEP, "test start.");
    int ret = ModifyCfgFile("featureNames=BUFFERPOOL,SQL,TRM,PERSISTENCE enableDmlPerfStat=1 enableDmlOperStat=1", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ret = GmeSqlStepAndCheck(conn, sqlSetup, 11);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ViewResultSet resultSet;
    ShowViewInfo(conn, "QRY_DML_INFO");
    string SqlSeView = "select * from 'V$QRY_DML_INFO';";
    ret = GmeSqlExecute(conn, SqlSeView.c_str(), EmbSqlViewResultSet, &resultSet, nullptr);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmeClose(conn);
    ret = ModifyCfgFile("featureNames=BUFFERPOOL,SQL,TRM,PERSISTENCE enableDmlPerfStat=0 enableDmlOperStat=0", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ShowViewInfo(conn, "QRY_DML_INFO");
    SqlSeView = "select * from 'V$QRY_DML_INFO';";
    ret = GmeSqlExecute(conn, SqlSeView.c_str(), EmbSqlViewResultSet, &resultSet, nullptr);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, ret);
}

//024.查询视图CATA_VERTEX_LABEL_CHECK_INFO，查询视图内字段，预期成功
HWTEST_F(TestDfxViewAdd, KBVector_012_054, TestSize.Level1)
{
    ComWriteLog(LOG_STEP, "test start.");
    int ret = ModifyCfgFile("featureNames=BUFFERPOOL,SQL,TRM,PERSISTENCE", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ret = GmeSqlStepAndCheck(conn, sqlSetup, 11);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ViewResultSet resultSet;
    ShowViewInfo(conn, "CATA_VERTEX_LABEL_CHECK_INFO");
    string SqlSeView = "select * from 'V$CATA_VERTEX_LABEL_CHECK_INFO';";
    ret = GmeSqlExecute(conn, SqlSeView.c_str(), EmbSqlViewResultSet, &resultSet, nullptr);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    size_t i = findColumnIndex(resultSet, "CHECK_INFO");
    DbJsonT *json = DbJsonLoads(resultSet.colValues[0][i].c_str(), JSON_REJECT_DUPLICATES);
    DbJsonT *item = DbJsonObjectGet(DbJsonArrayGet(json, 0), "SHOULD_AGED_CNT");
    EXPECT_TRUE(DbJsonIsString(item));
    EXPECT_FALSE(string(DbJsonStringValue(item)).empty());
    DbJsonDelete(json);
}

//025.查询视图CATA_TABLESPACE_INFO，预期查询失败
HWTEST_F(TestDfxViewAdd, KBVector_012_055, TestSize.Level1)
{
    ComWriteLog(LOG_STEP, "test start.");
    int ret = ModifyCfgFile("featureNames=BUFFERPOOL,SQL,TRM,PERSISTENCE", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ret = GmeSqlStepAndCheck(conn, sqlSetup, 11);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ViewResultSet resultSet;
    ShowViewInfo(conn, "CATA_TABLESPACE_INFO");
    string SqlSeView = "select * from 'V$CATA_TABLESPACE_INFO';";
    ret = GmeSqlExecute(conn, SqlSeView.c_str(), EmbSqlViewResultSet, &resultSet, nullptr);
    AW_MACRO_ASSERT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
}

//026.查询视图STORAGE_DURABLE_MEMDATA_STAT，bufferpool模式，查询视图内字段，预期成功
HWTEST_F(TestDfxViewAdd, KBVector_012_056, TestSize.Level1)
{
    ComWriteLog(LOG_STEP, "test start.");
    int ret = ModifyCfgFile("featureNames=BUFFERPOOL,SQL,TRM,PERSISTENCE", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ret = GmeSqlStepAndCheck(conn, sqlSetup, 11);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ViewResultSet resultSet;
    ShowViewInfo(conn, "STORAGE_DURABLE_MEMDATA_STAT");
    string SqlSeView = "select * from 'V$STORAGE_DURABLE_MEMDATA_STAT';";
    ret = GmeSqlExecute(conn, SqlSeView.c_str(), EmbSqlViewResultSet, &resultSet, nullptr);
    AW_MACRO_ASSERT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
}

//027.查询视图STORAGE_DURABLE_MEMDATA_STAT，durable模式，查询视图内字段，预期成功
HWTEST_F(TestDfxViewAdd, KBVector_012_057, TestSize.Level1)
{
    ComWriteLog(LOG_STEP, "test start.");
    int ret = ModifyCfgFile("featureNames=durablememdata,BUFFERPOOL,SQL,TRM,PERSISTENCE", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ret = GmeSqlStepAndCheck(conn, sqlSetup, 11);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ViewResultSet resultSet;
    ShowViewInfo(conn, "STORAGE_DURABLE_MEMDATA_STAT");
    string SqlSeView = "select * from 'V$STORAGE_DURABLE_MEMDATA_STAT';";
    ret = GmeSqlExecute(conn, SqlSeView.c_str(), EmbSqlViewResultSet, &resultSet, nullptr);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int value_used_page = getIntField(resultSet, "USED_PAGE_CNT", 0);
    int value_allocated_page = getIntField(resultSet, "ALLOCATED_PAGE_CNT", 0);
    double value_used_page_ratio = getDoubleField(resultSet, "USED_PAGE_RATIO", 0);
    double used_page_ratio_compare = divideAndReturnDouble(value_used_page, value_allocated_page);
    EXPECT_EQ(value_used_page_ratio, used_page_ratio_compare);
    int value_device_size = getIntField(resultSet, "DEVICE_SIZE", 0);
    int value_page_size = getIntField(resultSet, "PAGE_SIZE", 0);
    double value_page_per_device = getDoubleField(resultSet, "PAGE_CNT_PER_DEVICE", 0);
    EXPECT_EQ(value_page_per_device, value_device_size/value_page_size);
}


//027.查询视图STORAGE_BUFFERPOOL_STAT，重复开关库，配置bufferpoolsize和pagesize的值，预期查询字段成功
HWTEST_F(TestDfxViewAdd, KBVector_012_058, TestSize.Level1)
{
    ComWriteLog(LOG_STEP, "test start.");
    int ret = ModifyCfgFile("featureNames=durablememdata,BUFFERPOOL,SQL,TRM,PERSISTENCE BUFFERPOOL_SIZE=2048 PAGE_SIZE=16", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ret = GmeSqlStepAndCheck(conn, sqlSetup, 11);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ViewResultSet resultSet;
    ShowViewInfo(conn, "STORAGE_BUFFERPOOL_STAT");
    string SqlSeView = "select * from 'V$STORAGE_BUFFERPOOL_STAT';";
    ret = GmeSqlExecute(conn, SqlSeView.c_str(), EmbSqlViewResultSet, &resultSet, nullptr);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int value_bufferpoolsize = getIntField(resultSet, "BUFFERPOOL_SIZE", 0);
    int value_pagesize = getIntField(resultSet, "PAGE_SIZE", 0);
    int value_capacity = getIntField(resultSet, "CAPACITY", 0);
    EXPECT_EQ(value_capacity, value_bufferpoolsize/value_pagesize);
    ret = GmeClose(conn);
    ret = ModifyCfgFile("featureNames=durablememdata,BUFFERPOOL,SQL,TRM,PERSISTENCE BUFFERPOOL_SIZE=8192 PAGE_SIZE=64", configPath);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeOpen(configPath, GME_OPEN_CREATE, &conn);
    AW_MACRO_EXPECT_EQ_INT(T_OK, ret);
    ShowViewInfo(conn, "STORAGE_BUFFERPOOL_STAT");
    SqlSeView = "select * from 'V$STORAGE_BUFFERPOOL_STAT';";
    ret = GmeSqlExecute(conn, SqlSeView.c_str(), EmbSqlViewResultSet, &resultSet, nullptr);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    value_bufferpoolsize = getIntField(resultSet, "BUFFERPOOL_SIZE", 0);
    value_pagesize = getIntField(resultSet, "PAGE_SIZE", 0);
    value_capacity = getIntField(resultSet, "CAPACITY", 0);
    EXPECT_EQ(value_capacity, value_bufferpoolsize/value_pagesize);
}
