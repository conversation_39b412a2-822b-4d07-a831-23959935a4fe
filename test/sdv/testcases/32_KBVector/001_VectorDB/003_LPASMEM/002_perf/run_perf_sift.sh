#!/bin/bash

curDir=`dirname $0`
rm -rf ${curDir}/run_log.txt
rm -rf ${curDir}/lpas_poc_perf_result.txt

# Sift亿级规格数据导入、LPAS建图、多并发查询
echo "running Sift_100M1024D_Load"
./lpas_sift_outPut --gtest_filter=*Sift_100M1024D_Load
echo "running Sift_100M1024D_BuildIndex"
./lpas_sift_outPut --gtest_filter=*Sift_100M1024D_BuildIndex
echo "running Sift_100M1024D_DualMode_VectorQuery"
numactl --interleave=all  ./lpas_sift_outPut --gtest_filter=*Sift_100M1024D_DualMode_VectorQuery
