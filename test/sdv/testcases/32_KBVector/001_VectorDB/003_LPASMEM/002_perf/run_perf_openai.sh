#!/bin/bash

curDir=`dirname $0`
rm -rf ${curDir}/run_log.txt
rm -rf ${curDir}/lpas_poc_perf_result.txt

# LPAS 500KOpenAI 性能建图、多并发查询
echo "running Lpasmem_Perf_500K1536D_Lpas_Build"
${curDir}/lpas_perf_poc --gtest_filter=*Lpasmem_Perf_500K1536D_Lpas_Build >> ${curDir}/run_log.txt
echo "running Lpasmem_Perf_500K1536D_vectorquery"
numactl --interleave=all ${curDir}/lpas_perf_poc --gtest_filter=*Lpasmem_Perf_500K1536D_vectorquery >> ${curDir}/run_log.txt
echo "running Lpasmem_Perf_500K1536D_1PercentFilter"
numactl --interleave=all ${curDir}/lpas_perf_poc --gtest_filter=*Lpasmem_Perf_500K1536D_1PercentFilter >> ${curDir}/run_log.txt
echo "running Lpasmem_Perf_500K1536D_99PercentFilter"
numactl --interleave=all ${curDir}/lpas_perf_poc --gtest_filter=*Lpasmem_Perf_500K1536D_99PercentFilter >> ${curDir}/run_log.txt

cat ${curDir}/run_log.txt | grep "perf_result" >> ${curDir}/lpas_poc_perf_result.txt
sed -i "s/\[perf_result\] //g" ${curDir}/lpas_poc_perf_result.txt
