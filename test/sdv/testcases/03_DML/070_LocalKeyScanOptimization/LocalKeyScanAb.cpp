/*****************************************************************************
 Description  : 异常场景
 Notes        : 注释如下
 History      :
 Author       : jiangdingshan
 Modification :
 Date         : 2021/9/2
*****************************************************************************/
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <stdint.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "tools.h"


using namespace std;

char g_labelName[LABELNAME_MAX_LENGTH] = "ip4forward";
char g_configJson[128] = "{\"max_record_count\" : 999999}";
GmcStmtT *stmt_sn_sync;
const char *g_subConnName = "subConnName";
const char *g_subName = "subVertexLabel";
GmcConnT *g_conn_async = NULL;
GmcStmtT *g_stmt_async = NULL;

class LocalKeyScanAbnormal : public testing::Test {
public:
    static void SetUpTestCase()
    {
        int ret;
        // 重启server
        system("sh $TEST_HOME/tools/start.sh");
        res = testEnvInit();
        EXPECT_EQ(GMERR_OK, res);
        res = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, res);
    }

    static void TearDownTestCase()
    {

        int ret;
        testEnvClean();
        res = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, res);
    };

    virtual void SetUp();
    virtual void TearDown();
};

void LocalKeyScanAbnormal::SetUp()
{
    AW_ADD_ERR_WHITE_LIST(3, "GMERR-1009002", "GMERR-1009007", "GMERR-1012000");
    printf("\n======================TEST:BEGIN======================\n");
    conn = NULL;
    stmt = NULL;
    g_conn_async = NULL;
    g_stmt_async = NULL;
    int ret;
    //封装的创建异步连接
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建同步连接
    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    GmcDropVertexLabel(stmt, g_labelName);

    AW_CHECK_LOG_BEGIN();
}

void LocalKeyScanAbnormal::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    printf("\n======================TEST:END========================\n");
}

//同步连接，建带local key的表，不预制数据，传入不存在local key 等值查询
TEST_F(LocalKeyScanAbnormal, LocalKeyScanAbnormal_019)
{
    int ret;
    // schema json 定义 local key
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_localkey.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);

    // create vertex label
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据
    int isPrint = 0;
    int oper_nums = 1024;
    ret = test_insert_vertex_ip4forward(stmt, 0, oper_nums);

    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "local_key_2");
    EXPECT_EQ(GMERR_OK, ret);

    //唯一local索引等值查找
    uint32_t p_scanValue = 1025;
    uint32_t p_primary_label = p_scanValue;
    uint32_t p_attribute_id = p_scanValue;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &p_primary_label, sizeof(p_primary_label));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &p_attribute_id, sizeof(p_attribute_id));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // fetch 等值字段值
    ret = test_localkey_scan_ip4forward_value(stmt, p_scanValue);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

//异步连接，建带local key的表，不预制数据，传入不存在local key 等值更新
TEST_F(LocalKeyScanAbnormal, LocalKeyScanAbnormal_020)
{
    int ret;
    // schema json 定义 local key
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_localkey.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);

    // create vertex label
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    //异步写入数据
    int oper_nums = 1024;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 1; i <= oper_nums; i++) {
        ret = test_batch_async_insert_vertex_ip4forward(g_stmt_async, i);
        ASSERT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &asyncData;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        ret = testWaitAsyncRecv(&asyncData);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, asyncData.status);
        EXPECT_EQ(1, asyncData.affectRows);
    }

    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "local_key_2");
    EXPECT_EQ(GMERR_OK, ret);

    //唯一local索引等值查找
    uint32_t p_scanValue = 1025;
    uint32_t p_primary_label = p_scanValue;
    uint32_t p_attribute_id = p_scanValue;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &p_primary_label, sizeof(p_primary_label));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &p_attribute_id, sizeof(p_attribute_id));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // fetch 等值字段值
    ret = test_localkey_scan_ip4forward_value(stmt, p_scanValue);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

//异步连接，建带local key的表，预制数据，传入local key 非法，等值查询
TEST_F(LocalKeyScanAbnormal, LocalKeyScanAbnormal_021)
{
    int ret;
    // schema json 定义 local key
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_localkey.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);

    // create vertex label
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    //异步写入数据
    int oper_nums = 1024;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 1; i <= oper_nums; i++) {
        ret = test_batch_async_insert_vertex_ip4forward(g_stmt_async, i);
        ASSERT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &asyncData;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        ret = testWaitAsyncRecv(&asyncData);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, asyncData.status);
        EXPECT_EQ(1, asyncData.affectRows);
    }

    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "local_key_2");
    EXPECT_EQ(GMERR_OK, ret);

    //唯一hash索引查找
    uint16_t p_scanValue = 1024;
    uint16_t p_primary_label = p_scanValue;
    uint16_t p_attribute_id = p_scanValue;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT16, &p_primary_label, sizeof(p_primary_label));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT16, &p_attribute_id, sizeof(p_attribute_id));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_DATATYPE_MISMATCH, ret);

    // fetch 等值字段值
    ret = test_localkey_scan_ip4forward_value(stmt, p_scanValue);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

//同步连接，建带local key的表，预制数据，传入local key非法 等值查询
TEST_F(LocalKeyScanAbnormal, LocalKeyScanAbnormal_022)
{
    int ret;
    // schema json 定义 local key
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_localkey.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);

    // create vertex label
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据
    int isPrint = 0;
    int oper_nums = 1024;
    ret = test_insert_vertex_ip4forward(stmt, 0, oper_nums);

    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "local_key_2");
    EXPECT_EQ(GMERR_OK, ret);

    //唯一local索引等值查找
    uint16_t p_scanValue = 1024;
    uint16_t p_primary_label = p_scanValue;
    uint16_t p_attribute_id = p_scanValue;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT16, &p_primary_label, sizeof(p_primary_label));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT16, &p_attribute_id, sizeof(p_attribute_id));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_DATATYPE_MISMATCH, ret);

    // fetch 等值字段值
    ret = test_localkey_scan_ip4forward_value(stmt, p_scanValue);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

//异步连接，建带local key的表，预制数据，传入local key 非法，等值更新
TEST_F(LocalKeyScanAbnormal, LocalKeyScanAbnormal_023)
{
    int ret;
    // schema json 定义 local key
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_localkey.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);

    // create vertex label
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    //异步写入数据
    int oper_nums = 1024;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 1; i <= oper_nums; i++) {
        ret = test_batch_async_insert_vertex_ip4forward(g_stmt_async, i);
        ASSERT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &asyncData;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        ret = testWaitAsyncRecv(&asyncData);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, asyncData.status);
        EXPECT_EQ(1, asyncData.affectRows);
    }

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);

    //唯一hash索引异步等值更新
    uint16_t p_scanValue = 1024;
    uint16_t p_primary_label = p_scanValue;
    uint16_t p_attribute_id = p_scanValue;
    ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT16, &p_primary_label, sizeof(p_primary_label));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_async, 1, GMC_DATATYPE_UINT16, &p_attribute_id, sizeof(p_attribute_id));
    EXPECT_EQ(GMERR_OK, ret);

    unsigned int coefficient = 3;
    uint8_t set_uint8 = wr_uint8 * coefficient;
    uint16_t set_uint16 = wr_uint16 * coefficient;
    uint32_t set_uint32 = uint32_tmp * coefficient;
    uint64_t set_uint64 = wr_uint64 * coefficient;
    ret = GmcSetVertexProperty(g_stmt_async, "mask_len", GMC_DATATYPE_UINT8, &set_uint8, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt_async, "app_obj_id", GMC_DATATYPE_UINT64, &set_uint64, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt_async, "app_version", GMC_DATATYPE_UINT32, &set_uint32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt_async, "reserved", GMC_DATATYPE_UINT16, &set_uint16, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_async, "localhash_key_2");
    EXPECT_EQ(GMERR_OK, ret);

    GmcAsyncRequestDoneContextT updateRequestCtx;
    updateRequestCtx.updateCb = update_vertex_callback;
    updateRequestCtx.userData = &asyncData;
    ret = GmcExecuteAsync(g_stmt_async, &updateRequestCtx);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);

    /*
    ret = testWaitAsyncRecv(&asyncData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asyncData.status);
    EXPECT_EQ(1, asyncData.affectRows);
    */
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "local_key_2");
    EXPECT_EQ(GMERR_OK, ret);

    //唯一local索引等值查找
    uint32_t p_scan = 1024;
    uint32_t p_primary = p_scanValue;
    uint32_t p_attribute = p_scanValue;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &p_primary, sizeof(p_primary));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &p_attribute, sizeof(p_attribute));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // fetch 等值字段值
    ret = test_localkey_scan_ip4forward_value(stmt, p_scan);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

//同步连接，建带local key的表，预制数据，传入local key非法 等值更新
TEST_F(LocalKeyScanAbnormal, LocalKeyScanAbnormal_024)
{
    int ret;
    // schema json 定义 local key
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_localkey.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);

    // create vertex label
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    //批量写数据
    int oper_nums = 1024;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = 1; i <= oper_nums; i++) {
        ret = test_batch_async_insert_vertex_ip4forward(stmt, i);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(oper_nums, totalNum);
    ASSERT_EQ(oper_nums, successNum);
    GmcBatchReset(batch);

    //唯一hash索引批量等值更新
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    uint16_t p_scanValue = 1024;
    uint16_t p_primary_label = p_scanValue;
    uint16_t p_attribute_id = p_scanValue;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT16, &p_primary_label, sizeof(p_primary_label));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT16, &p_attribute_id, sizeof(p_attribute_id));
    EXPECT_EQ(GMERR_OK, ret);

    unsigned int coefficient = 3;
    uint8_t set_uint8 = wr_uint8 * coefficient;
    uint16_t set_uint16 = wr_uint16 * coefficient;
    uint32_t set_uint32 = uint32_tmp * coefficient;
    uint64_t set_uint64 = wr_uint64 * coefficient;
    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &set_uint8, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &set_uint64, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &set_uint32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "reserved", GMC_DATATYPE_UINT16, &set_uint16, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key_2");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    ASSERT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_INVALID_PROPERTY, ret);
    ASSERT_EQ(0, totalNum);
    ASSERT_EQ(0, successNum);
    GmcBatchDestroy(batch);

    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "local_key_2");
    EXPECT_EQ(GMERR_OK, ret);

    //唯一local索引等值查找
    uint32_t p_scan = 1024;
    uint32_t p_primary = p_scanValue;
    uint32_t p_attribute = p_scanValue;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &p_primary, sizeof(p_primary));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &p_attribute, sizeof(p_attribute));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // fetch 等值字段值
    ret = test_localkey_scan_ip4forward_value(stmt, p_scan);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

//异步连接，建带local key的表，预制数据，传入local key 非法，等值刪除
TEST_F(LocalKeyScanAbnormal, LocalKeyScanAbnormal_025)
{
    int ret;
    // schema json 定义 local key
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_localkey.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);

    // create vertex label
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    //异步写入数据
    int oper_nums = 1024;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 1; i <= oper_nums; i++) {
        ret = test_batch_async_insert_vertex_ip4forward(g_stmt_async, i);
        ASSERT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &asyncData;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        ret = testWaitAsyncRecv(&asyncData);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, asyncData.status);
        EXPECT_EQ(1, asyncData.affectRows);
    }

    //唯一索引等值异步删除
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);

    //唯一hash索引异步等值更新
    uint16_t p_scanValue = 1024;
    uint16_t p_primary_label = p_scanValue;
    uint16_t p_attribute_id = p_scanValue;
    ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT16, &p_primary_label, sizeof(p_primary_label));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_async, 1, GMC_DATATYPE_UINT16, &p_attribute_id, sizeof(p_attribute_id));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(g_stmt_async, "localhash_key_2");
    EXPECT_EQ(GMERR_OK, ret);
    GmcAsyncRequestDoneContextT deleteRequestCtx;
    deleteRequestCtx.deleteCb = delete_vertex_callback;
    deleteRequestCtx.userData = &asyncData;
    ret = GmcExecuteAsync(g_stmt_async, &deleteRequestCtx);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    /*
    ret = testWaitAsyncRecv(&asyncData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asyncData.status);
    EXPECT_EQ(1, asyncData.affectRows);
    */
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "local_key_2");
    EXPECT_EQ(GMERR_OK, ret);
    //唯一索引等值查找
    uint32_t p_scan = 1024;
    uint32_t p_primary = p_scanValue;
    uint32_t p_attribute = p_scanValue;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &p_primary, sizeof(p_primary));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &p_attribute, sizeof(p_attribute));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // fetch 等值字段值
    ret = test_localkey_scan_ip4forward_value(stmt, p_scan);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

//同步连接，建带local key的表，预制数据，传入local key非法 等值刪除
TEST_F(LocalKeyScanAbnormal, LocalKeyScanAbnormal_026)
{
    int ret;
    // schema json 定义 local key
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_localkey.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);

    // create vertex label
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    //批量写数据
    int oper_nums = 1024;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = 1; i <= oper_nums; i++) {
        ret = test_batch_async_insert_vertex_ip4forward(stmt, i);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(oper_nums, totalNum);
    ASSERT_EQ(oper_nums, successNum);
    GmcBatchReset(batch);

    //唯一hash索引批量等值删除
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    uint16_t p_scanValue = 1024;
    uint16_t p_primary_label = p_scanValue;
    uint16_t p_attribute_id = p_scanValue;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT16, &p_primary_label, sizeof(p_primary_label));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT16, &p_attribute_id, sizeof(p_attribute_id));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key_2");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    ASSERT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    ret = testGmcGetLastError("Datatype mismatch. batch dml operation worthless");
    EXPECT_EQ(GMERR_OK, ret);
    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_INVALID_PROPERTY, ret);
    ASSERT_EQ(0, totalNum);
    ASSERT_EQ(0, successNum);
    GmcBatchDestroy(batch);

    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "local_key_2");
    EXPECT_EQ(GMERR_OK, ret);

    //唯一索引等值查找
    uint32_t p_scan = 1024;
    uint32_t p_primary = p_scanValue;
    uint32_t p_attribute = p_scanValue;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &p_primary, sizeof(p_primary));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &p_attribute, sizeof(p_attribute));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // fetch 等值字段值
    ret = test_localkey_scan_ip4forward_value(stmt, p_scan);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

//同步连接，建带local key的表，预制数据，范围查询，查询成功，设置local key值，在左值开区间 等值查询
TEST_F(LocalKeyScanAbnormal, LocalKeyScanAbnormal_027)
{
    int ret;
    // schema json 定义 local key
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_localkey_multi_fields.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);

    // create vertex label
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据
    int isPrint = 1;
    int oper_nums = 100;
    ret = test_insert_vertex_ip4forward(stmt, 0, oper_nums);
    // 主键读
    printf("\n========== insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_ip4forward(conn, "primary_key", 0, oper_nums, GMC_DATATYPE_UINT32, conn_id, "pk read", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    unsigned int l_val = 20;
    unsigned int r_val = 25;
    char l_fixed[35] = "writewritewritewritewritewritewrit";
    char r_fixed[35] = "writewritewritewritewritewritewrit";
    unsigned int arrLen = 2;

    GmcPropValueT *leftKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    leftKeyProps[0].type = GMC_DATATYPE_UINT32;
    leftKeyProps[0].value = &l_val;
    leftKeyProps[0].size = sizeof(l_val);
    leftKeyProps[1].type = GMC_DATATYPE_FIXED;
    leftKeyProps[1].value = l_fixed;
    leftKeyProps[1].size = strlen(l_fixed);

    GmcPropValueT *rightKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    rightKeyProps[0].type = GMC_DATATYPE_UINT32;
    rightKeyProps[0].value = &r_val;
    rightKeyProps[0].size = sizeof(r_val);
    rightKeyProps[1].type = GMC_DATATYPE_FIXED;
    rightKeyProps[1].value = r_fixed;
    rightKeyProps[1].size = strlen(r_fixed);

    GmcRangeItemT items[arrLen];
    items[0].lValue = &leftKeyProps[0];
    items[0].rValue = &rightKeyProps[0];
    items[0].lFlag = GMC_COMPARE_RANGE_OPEN;
    items[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].order = GMC_ORDER_ASC;
    items[1].lValue = &leftKeyProps[1];
    items[1].rValue = &rightKeyProps[1];
    items[1].lFlag = GMC_COMPARE_RANGE_OPEN;
    items[1].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items[1].order = GMC_ORDER_ASC;

    const char *keyName = "local_key";
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetKeyRange(stmt, items, arrLen);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = test_hashInx_scan_ip4forward(stmt, keyName, oper_nums, 0, "Range Scan", 1);
    EXPECT_EQ(GMERR_OK, ret);
    // EXPECT_EQ(r_val - l_val + 1, scan_end - 1);
    free(leftKeyProps);
    free(rightKeyProps);

    //唯一索引等值查找
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "local_key_2");
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t p_scan = 21;
    uint32_t p_primary = p_scan;
    uint32_t p_attribute = p_scan;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &p_primary, sizeof(p_primary));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &p_attribute, sizeof(p_attribute));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // fetch 等值字段值
    ret = test_localkey_scan_ip4forward_value(stmt, p_scan);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

//同步连接，建带local key的表，预制数据，范围查询，查询成功，设置local key值，在右值开区间 等值查询
TEST_F(LocalKeyScanAbnormal, LocalKeyScanAbnormal_028)
{
    int ret;
    // schema json 定义 local key
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_localkey_multi_fields.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);

    // create vertex label
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据
    int isPrint = 1;
    int oper_nums = 100;
    ret = test_insert_vertex_ip4forward(stmt, 0, oper_nums);
    // 主键读
    printf("\n========== insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_ip4forward(conn, "primary_key", 0, oper_nums, GMC_DATATYPE_UINT32, conn_id, "pk read", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    unsigned int l_val = 20;
    unsigned int r_val = 25;
    char l_fixed[35] = "writewritewritewritewritewritewrit";
    char r_fixed[35] = "writewritewritewritewritewritewrit";
    unsigned int arrLen = 2;

    GmcPropValueT *leftKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    leftKeyProps[0].type = GMC_DATATYPE_UINT32;
    leftKeyProps[0].value = &l_val;
    leftKeyProps[0].size = sizeof(l_val);
    leftKeyProps[1].type = GMC_DATATYPE_FIXED;
    leftKeyProps[1].value = l_fixed;
    leftKeyProps[1].size = strlen(l_fixed);

    GmcPropValueT *rightKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    rightKeyProps[0].type = GMC_DATATYPE_UINT32;
    rightKeyProps[0].value = &r_val;
    rightKeyProps[0].size = sizeof(r_val);
    rightKeyProps[1].type = GMC_DATATYPE_FIXED;
    rightKeyProps[1].value = r_fixed;
    rightKeyProps[1].size = strlen(r_fixed);

    GmcRangeItemT items[arrLen];
    items[0].lValue = &leftKeyProps[0];
    items[0].rValue = &rightKeyProps[0];
    items[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].rFlag = GMC_COMPARE_RANGE_OPEN;
    items[0].order = GMC_ORDER_ASC;
    items[1].lValue = &leftKeyProps[1];
    items[1].rValue = &rightKeyProps[1];
    items[1].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items[1].rFlag = GMC_COMPARE_RANGE_OPEN;
    items[1].order = GMC_ORDER_ASC;

    const char *keyName = "local_key";
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetKeyRange(stmt, items, arrLen);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = test_hashInx_scan_ip4forward(stmt, keyName, oper_nums, 0, "Range Scan", 1);
    EXPECT_EQ(GMERR_OK, ret);
    // EXPECT_EQ(r_val - l_val + 1, scan_end - 1);
    free(leftKeyProps);
    free(rightKeyProps);

    //唯一索引等值查找
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "local_key_2");
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t p_scan = 25;
    uint32_t p_primary = p_scan;
    uint32_t p_attribute = p_scan;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &p_primary, sizeof(p_primary));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &p_attribute, sizeof(p_attribute));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // fetch 等值字段值
    ret = test_localkey_scan_ip4forward_value(stmt, p_scan);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

//异步连接，建带local key的表，预制数据，范围查询，查询成功，设置local key值，在左值闭区间 等值查询
TEST_F(LocalKeyScanAbnormal, LocalKeyScanAbnormal_029)
{
    int ret;
    // schema json 定义 local key
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_localkey_multi_fields.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);

    // create vertex label
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    //异步写入数据
    int isPrint = 1;
    int oper_nums = 100;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 1; i <= oper_nums; i++) {
        ret = test_batch_async_insert_vertex_ip4forward(g_stmt_async, i);
        ASSERT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &asyncData;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        ret = testWaitAsyncRecv(&asyncData);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, asyncData.status);
        EXPECT_EQ(1, asyncData.affectRows);
    }
    // 主键读
    printf("\n========== insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_ip4forward(conn, "primary_key", 0, oper_nums, GMC_DATATYPE_UINT32, conn_id, "pk read", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    unsigned int l_val = 20;
    unsigned int r_val = 25;
    char l_fixed[35] = "writewritewritewritewritewritewrit";
    char r_fixed[35] = "writewritewritewritewritewritewrit";
    unsigned int arrLen = 2;

    GmcPropValueT *leftKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    leftKeyProps[0].type = GMC_DATATYPE_UINT32;
    leftKeyProps[0].value = &l_val;
    leftKeyProps[0].size = sizeof(l_val);
    leftKeyProps[1].type = GMC_DATATYPE_FIXED;
    leftKeyProps[1].value = l_fixed;
    leftKeyProps[1].size = strlen(l_fixed);

    GmcPropValueT *rightKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    rightKeyProps[0].type = GMC_DATATYPE_UINT32;
    rightKeyProps[0].value = &r_val;
    rightKeyProps[0].size = sizeof(r_val);
    rightKeyProps[1].type = GMC_DATATYPE_FIXED;
    rightKeyProps[1].value = r_fixed;
    rightKeyProps[1].size = strlen(r_fixed);

    GmcRangeItemT items[arrLen];
    items[0].lValue = &leftKeyProps[0];
    items[0].rValue = &rightKeyProps[0];
    items[0].lFlag = GMC_COMPARE_RANGE_OPEN;
    items[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].order = GMC_ORDER_ASC;
    items[1].lValue = &leftKeyProps[1];
    items[1].rValue = &rightKeyProps[1];
    items[1].lFlag = GMC_COMPARE_RANGE_OPEN;
    items[1].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items[1].order = GMC_ORDER_ASC;

    const char *keyName = "local_key";
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetKeyRange(stmt, items, arrLen);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = test_hashInx_scan_ip4forward(stmt, keyName, oper_nums, 0, "Range Scan", 1);
    EXPECT_EQ(GMERR_OK, ret);
    // EXPECT_EQ(r_val - l_val + 1, scan_end - 1);
    free(leftKeyProps);
    free(rightKeyProps);

    //唯一索引等值查找
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "local_key_2");
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t p_scan = 20;
    uint32_t p_primary = p_scan;
    uint32_t p_attribute = p_scan;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &p_primary, sizeof(p_primary));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &p_attribute, sizeof(p_attribute));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // fetch 等值字段值
    ret = test_localkey_scan_ip4forward_value(stmt, p_scan);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

//异步连接，建带local key的表，预制数据，范围查询，查询成功，设置local key值，在右值闭区间 等值查询
TEST_F(LocalKeyScanAbnormal, LocalKeyScanAbnormal_030)
{
    int ret;
    // schema json 定义 local key
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_localkey_multi_fields.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);

    // create vertex label
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    //异步写入数据
    int isPrint = 1;
    int oper_nums = 100;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 1; i <= oper_nums; i++) {
        ret = test_batch_async_insert_vertex_ip4forward(g_stmt_async, i);
        ASSERT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &asyncData;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        ret = testWaitAsyncRecv(&asyncData);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, asyncData.status);
        EXPECT_EQ(1, asyncData.affectRows);
    }
    // 主键读
    printf("\n========== insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_ip4forward(conn, "primary_key", 0, oper_nums, GMC_DATATYPE_UINT32, conn_id, "pk read", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    unsigned int l_val = 20;
    unsigned int r_val = 25;
    char l_fixed[35] = "writewritewritewritewritewritewrit";
    char r_fixed[35] = "writewritewritewritewritewritewrit";
    unsigned int arrLen = 2;

    GmcPropValueT *leftKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    leftKeyProps[0].type = GMC_DATATYPE_UINT32;
    leftKeyProps[0].value = &l_val;
    leftKeyProps[0].size = sizeof(l_val);
    leftKeyProps[1].type = GMC_DATATYPE_FIXED;
    leftKeyProps[1].value = l_fixed;
    leftKeyProps[1].size = strlen(l_fixed);

    GmcPropValueT *rightKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    rightKeyProps[0].type = GMC_DATATYPE_UINT32;
    rightKeyProps[0].value = &r_val;
    rightKeyProps[0].size = sizeof(r_val);
    rightKeyProps[1].type = GMC_DATATYPE_FIXED;
    rightKeyProps[1].value = r_fixed;
    rightKeyProps[1].size = strlen(r_fixed);

    GmcRangeItemT items[arrLen];
    items[0].lValue = &leftKeyProps[0];
    items[0].rValue = &rightKeyProps[0];
    items[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].rFlag = GMC_COMPARE_RANGE_OPEN;
    items[0].order = GMC_ORDER_ASC;
    items[1].lValue = &leftKeyProps[1];
    items[1].rValue = &rightKeyProps[1];
    items[1].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items[1].rFlag = GMC_COMPARE_RANGE_OPEN;
    items[1].order = GMC_ORDER_ASC;

    const char *keyName = "local_key";
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetKeyRange(stmt, items, arrLen);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = test_hashInx_scan_ip4forward(stmt, keyName, oper_nums, 0, "Range Scan", 1);
    EXPECT_EQ(GMERR_OK, ret);
    // EXPECT_EQ(r_val - l_val + 1, scan_end - 1);
    free(leftKeyProps);
    free(rightKeyProps);

    //唯一索引等值查找
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "local_key_2");
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t p_scan = 25;
    uint32_t p_primary = p_scan;
    uint32_t p_attribute = p_scan;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &p_primary, sizeof(p_primary));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &p_attribute, sizeof(p_attribute));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // fetch 等值字段值
    ret = test_localkey_scan_ip4forward_value(stmt, p_scan);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}
