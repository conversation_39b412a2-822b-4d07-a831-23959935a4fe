/*****************************************************************************
 Description  : 基础场景
 Notes        : 注释如下
 History      :
 Author       : jiangdingshan
 Modification :
 Date         : 2021/9/1
*****************************************************************************/
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <stdint.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "tools.h"


using namespace std;

char g_labelName[LABELNAME_MAX_LENGTH] = "ip4forward";
char g_configJson[128] = "{\"max_record_count\" : 999999}";
GmcStmtT *stmt_sn_sync;
const char *g_subConnName = "subConnName";
const char *g_subName = "subVertexLabel";
GmcConnT *g_conn_async = NULL;
GmcStmtT *g_stmt_async = NULL;

class LocalKeyScanBasic : public testing::Test {
public:
    static void SetUpTestCase()
    {
        int ret;
        // 重启server
        system("sh $TEST_HOME/tools/start.sh");
        res = testEnvInit();
        EXPECT_EQ(GMERR_OK, res);
        res = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, res);
    }

    static void TearDownTestCase()
    {

        int ret;
        testEnvClean();
        res = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, res);
    };

    virtual void SetUp();
    virtual void TearDown();
};

void LocalKeyScanBasic::SetUp()
{
    printf("\n======================TEST:BEGIN======================\n");
    conn = NULL;
    stmt = NULL;
    g_conn_async = NULL;
    g_stmt_async = NULL;
    int ret;
    //封装的创建异步连接
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建同步连接
    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    GmcDropVertexLabel(stmt, g_labelName);

    AW_CHECK_LOG_BEGIN();
}

void LocalKeyScanBasic::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    printf("\n======================TEST:END========================\n");
}

//同步连接，建带local key的表，普通DML操作，等值查询（唯一索引）
TEST_F(LocalKeyScanBasic, LocalKeyScanBasic_001)
{
    int ret;
    // schema json 定义 local key
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_localkey.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);

    // create vertex label
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据
    int oper_nums = 1024;
    ret = test_insert_vertex_ip4forward(stmt, 0, oper_nums);

    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "local_key");
    EXPECT_EQ(GMERR_OK, ret);

    //唯一local索引等值查找
    uint32_t p_scanValue = 1024;
    uint32_t p_primary_label = p_scanValue;
    uint32_t p_attribute_id = p_scanValue;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &p_primary_label, sizeof(p_primary_label));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &p_attribute_id, sizeof(p_attribute_id));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // fetch 等值字段值
    ret = test_localkey_scan_ip4forward_value(stmt, p_scanValue);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

//同步连接，建带local key的表，普通DML操作，等值更新，查询（唯一索引）
TEST_F(LocalKeyScanBasic, LocalKeyScanBasic_002)
{
    int ret;
    // schema json 定义 local key
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_localkey.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);

    // create vertex label
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据
    int isPrint = 0;
    int oper_nums = 1024;
    ret = test_insert_vertex_ip4forward(stmt, 0, oper_nums);

    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "local_key");
    EXPECT_EQ(GMERR_OK, ret);

    //唯一hash索引等值更新
    uint32_t p_scanValue = 1024;
    uint32_t p_primary_label = p_scanValue;
    uint32_t p_attribute_id = p_scanValue;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &p_primary_label, sizeof(p_primary_label));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &p_attribute_id, sizeof(p_attribute_id));
    EXPECT_EQ(GMERR_OK, ret);

    unsigned int coefficient = 3;
    uint8_t set_uint8 = wr_uint8 * coefficient;
    uint16_t set_uint16 = wr_uint16 * coefficient;
    uint32_t set_uint32 = uint32_tmp * coefficient;
    uint64_t set_uint64 = wr_uint64 * coefficient;
    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &set_uint8, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &set_uint64, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &set_uint32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "reserved", GMC_DATATYPE_UINT16, &set_uint16, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "local_key");
    EXPECT_EQ(GMERR_OK, ret);

    //唯一local索引等值查找
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &p_primary_label, sizeof(p_primary_label));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &p_attribute_id, sizeof(p_attribute_id));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // fetch 等值字段值
    ret = test_localkey_scan_ip4forward_value(stmt, p_scanValue, coefficient);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    EXPECT_EQ(GMERR_OK, ret);
    // 非唯一hash索引扫描
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    printf("\n========== non-uniq hash index scan: qos_profile_id, nhp_group_id ===============\n");
    ret = test_hashInx_scan_ip4forward(stmt, "localhash_key", oper_nums, 0, "nonUni Hash Scan", 0);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

//同步连接，建带local key的表，普通DML操作，等值删除，查询（唯一索引）
TEST_F(LocalKeyScanBasic, LocalKeyScanBasic_003)
{
    int ret;
    // schema json 定义 local key
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_localkey.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);

    // create vertex label
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据
    int isPrint = 0;
    int oper_nums = 1024;
    ret = test_insert_vertex_ip4forward(stmt, 0, oper_nums);

    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "local_key");
    EXPECT_EQ(GMERR_OK, ret);

    //唯一hash索引等值删除
    uint32_t p_scanValue = 1024;
    uint32_t p_primary_label = p_scanValue;
    uint32_t p_attribute_id = p_scanValue;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &p_primary_label, sizeof(p_primary_label));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &p_attribute_id, sizeof(p_attribute_id));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "local_key");
    EXPECT_EQ(GMERR_OK, ret);

    //唯一local索引等值查找
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &p_primary_label, sizeof(p_primary_label));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &p_attribute_id, sizeof(p_attribute_id));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // fetch 等值字段值
    ret = test_localkey_scan_ip4forward_value(stmt, p_scanValue);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    EXPECT_EQ(GMERR_OK, ret);
    // 非唯一hash索引扫描
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    printf("\n========== non-uniq hash index scan: qos_profile_id, nhp_group_id ===============\n");
    ret = test_hashInx_scan_ip4forward(stmt, "localhash_key", oper_nums, 0, "nonUni Hash Scan", 0);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

//同步连接，建带local key的表，批量DML操作，等值查询（唯一索引）
TEST_F(LocalKeyScanBasic, LocalKeyScanBasic_004)
{
    int ret;
    // schema json 定义 local key
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_localkey.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);

    // create vertex label
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    //批量写数据
    int oper_nums = 1024;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = 1; i <= oper_nums; i++) {
        ret = test_batch_async_insert_vertex_ip4forward(stmt, i);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(oper_nums, totalNum);
    ASSERT_EQ(oper_nums, successNum);
    GmcBatchDestroy(batch);

    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "local_key");
    EXPECT_EQ(GMERR_OK, ret);

    //唯一local索引等值查找
    uint32_t p_scanValue = 1024;
    uint32_t p_primary_label = p_scanValue;
    uint32_t p_attribute_id = p_scanValue;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &p_primary_label, sizeof(p_primary_label));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &p_attribute_id, sizeof(p_attribute_id));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // fetch 等值字段值
    ret = test_localkey_scan_ip4forward_value(stmt, p_scanValue);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    EXPECT_EQ(GMERR_OK, ret);
    // 非唯一hash索引更新
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    printf("\n========== non-uniq hash index scan: qos_profile_id, nhp_group_id ===============\n");
    ret = test_hashInx_scan_ip4forward(stmt, "localhash_key", oper_nums, 0, "nonUni Hash Scan", 0);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

//同步连接，建带local key的表，批量DML操作，等值更新，查询（唯一索引）
TEST_F(LocalKeyScanBasic, LocalKeyScanBasic_005)
{
    int ret;
    // schema json 定义 local key
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_localkey.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);

    // create vertex label
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    //批量写数据
    int oper_nums = 1024;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = 1; i <= oper_nums; i++) {
        ret = test_batch_async_insert_vertex_ip4forward(stmt, i);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(oper_nums, totalNum);
    ASSERT_EQ(oper_nums, successNum);
    GmcBatchReset(batch);

    //唯一hash索引批量等值更新
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t p_scanValue = 1024;
    uint32_t p_primary_label = p_scanValue;
    uint32_t p_attribute_id = p_scanValue;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &p_primary_label, sizeof(p_primary_label));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &p_attribute_id, sizeof(p_attribute_id));
    EXPECT_EQ(GMERR_OK, ret);

    unsigned int coefficient = 3;
    uint8_t set_uint8 = wr_uint8 * coefficient;
    uint16_t set_uint16 = wr_uint16 * coefficient;
    uint32_t set_uint32 = uint32_tmp * coefficient;
    uint64_t set_uint64 = wr_uint64 * coefficient;
    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &set_uint8, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &set_uint64, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &set_uint32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "reserved", GMC_DATATYPE_UINT16, &set_uint16, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "local_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(1, totalNum);
    ASSERT_EQ(1, successNum);
    GmcBatchDestroy(batch);

    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "local_key");
    EXPECT_EQ(GMERR_OK, ret);

    //唯一local索引等值查找
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &p_primary_label, sizeof(p_primary_label));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &p_attribute_id, sizeof(p_attribute_id));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // fetch 等值字段值
    ret = test_localkey_scan_ip4forward_value(stmt, p_scanValue, coefficient);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    EXPECT_EQ(GMERR_OK, ret);
    // 非唯一hash索引扫描
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    printf("\n========== non-uniq hash index scan: qos_profile_id, nhp_group_id ===============\n");
    ret = test_hashInx_scan_ip4forward(stmt, "localhash_key", oper_nums, 0, "nonUni Hash Scan", 0);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

//同步连接，建带local key的表，批量DML操作，等值删除，查询（唯一索引）
TEST_F(LocalKeyScanBasic, LocalKeyScanBasic_006)
{
    int ret;
    // schema json 定义 local key
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_localkey.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);

    // create vertex label
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    //批量写数据
    int oper_nums = 1024;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = 1; i <= oper_nums; i++) {
        ret = test_batch_async_insert_vertex_ip4forward(stmt, i);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(oper_nums, totalNum);
    ASSERT_EQ(oper_nums, successNum);
    GmcBatchReset(batch);

    //唯一hash索引批量等值删除
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t p_scanValue = 1024;
    uint32_t p_primary_label = p_scanValue;
    uint32_t p_attribute_id = p_scanValue;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &p_primary_label, sizeof(p_primary_label));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &p_attribute_id, sizeof(p_attribute_id));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "local_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(1, totalNum);
    ASSERT_EQ(1, successNum);

    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "local_key");
    EXPECT_EQ(GMERR_OK, ret);

    //唯一local索引等值查找
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &p_primary_label, sizeof(p_primary_label));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &p_attribute_id, sizeof(p_attribute_id));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // fetch 等值字段值
    ret = test_localkey_scan_ip4forward_value(stmt, p_scanValue);
    // EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    EXPECT_EQ(GMERR_OK, ret);
    // 非唯一hash索引扫描
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    printf("\n========== non-uniq hash index scan: qos_profile_id, nhp_group_id ===============\n");
    ret = test_hashInx_scan_ip4forward(stmt, "localhash_key", oper_nums, 0, "nonUni Hash Scan", 0);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

//异步连接，建带local key的表，普通DML操作，等值查询（唯一索引）
TEST_F(LocalKeyScanBasic, LocalKeyScanBasic_007)
{
    int ret;
    // schema json 定义 local key
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_localkey.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);

    // create vertex label
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    //异步写入数据
    int oper_nums = 1024;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 1; i <= oper_nums; i++) {
        ret = test_batch_async_insert_vertex_ip4forward(g_stmt_async, i);
        ASSERT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &asyncData;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        ret = testWaitAsyncRecv(&asyncData);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, asyncData.status);
        EXPECT_EQ(1, asyncData.affectRows);
    }

    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "local_key");
    EXPECT_EQ(GMERR_OK, ret);

    //唯一local索引等值查找
    uint32_t p_scanValue = 1024;
    uint32_t p_primary_label = p_scanValue;
    uint32_t p_attribute_id = p_scanValue;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &p_primary_label, sizeof(p_primary_label));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &p_attribute_id, sizeof(p_attribute_id));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // fetch 等值字段值
    ret = test_localkey_scan_ip4forward_value(stmt, p_scanValue);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

//异步连接，建带local key的表，普通DML操作，等值更新，查询（唯一索引）
TEST_F(LocalKeyScanBasic, LocalKeyScanBasic_008)
{
    int ret;
    // schema json 定义 local key
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_localkey.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);

    // create vertex label
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    //异步写入数据
    int oper_nums = 1024;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 1; i <= oper_nums; i++) {
        ret = test_batch_async_insert_vertex_ip4forward(g_stmt_async, i);
        ASSERT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &asyncData;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        ret = testWaitAsyncRecv(&asyncData);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, asyncData.status);
        EXPECT_EQ(1, asyncData.affectRows);
    }

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);

    //唯一hash索引异步等值更新
    uint32_t p_scanValue = 1024;
    uint32_t p_primary_label = p_scanValue;
    uint32_t p_attribute_id = p_scanValue;
    ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &p_primary_label, sizeof(p_primary_label));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_async, 1, GMC_DATATYPE_UINT32, &p_attribute_id, sizeof(p_attribute_id));
    EXPECT_EQ(GMERR_OK, ret);

    unsigned int coefficient = 3;
    uint8_t set_uint8 = wr_uint8 * coefficient;
    uint16_t set_uint16 = wr_uint16 * coefficient;
    uint32_t set_uint32 = uint32_tmp * coefficient;
    uint64_t set_uint64 = wr_uint64 * coefficient;
    ret = GmcSetVertexProperty(g_stmt_async, "mask_len", GMC_DATATYPE_UINT8, &set_uint8, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt_async, "app_obj_id", GMC_DATATYPE_UINT64, &set_uint64, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt_async, "app_version", GMC_DATATYPE_UINT32, &set_uint32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt_async, "reserved", GMC_DATATYPE_UINT16, &set_uint16, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_async, "local_key");
    EXPECT_EQ(GMERR_OK, ret);

    GmcAsyncRequestDoneContextT updateRequestCtx;
    updateRequestCtx.updateCb = update_vertex_callback;
    updateRequestCtx.userData = &asyncData;
    ret = GmcExecuteAsync(g_stmt_async, &updateRequestCtx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asyncData.status);
    EXPECT_EQ(1, asyncData.affectRows);

    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "local_key");
    EXPECT_EQ(GMERR_OK, ret);

    //唯一local索引等值查找
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &p_primary_label, sizeof(p_primary_label));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &p_attribute_id, sizeof(p_attribute_id));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // fetch 等值字段值
    ret = test_localkey_scan_ip4forward_value(stmt, p_scanValue, coefficient);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

//异步连接，建带local key的表，普通DML操作，等值删除，查询（唯一索引）
TEST_F(LocalKeyScanBasic, LocalKeyScanBasic_009)
{
    int ret;
    // schema json 定义 local key
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_localkey.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);

    // create vertex label
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    //异步写入数据
    int oper_nums = 1024;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 1; i <= oper_nums; i++) {
        ret = test_batch_async_insert_vertex_ip4forward(g_stmt_async, i);
        ASSERT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &asyncData;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        ret = testWaitAsyncRecv(&asyncData);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, asyncData.status);
        EXPECT_EQ(1, asyncData.affectRows);
    }

    //唯一索引等值异步删除
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t p_scanValue = 1024;
    uint32_t p_primary_label = p_scanValue;
    uint32_t p_attribute_id = p_scanValue;
    ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &p_primary_label, sizeof(p_primary_label));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_async, 1, GMC_DATATYPE_UINT32, &p_attribute_id, sizeof(p_attribute_id));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(g_stmt_async, "local_key");
    EXPECT_EQ(GMERR_OK, ret);
    GmcAsyncRequestDoneContextT deleteRequestCtx;
    deleteRequestCtx.deleteCb = delete_vertex_callback;
    deleteRequestCtx.userData = &asyncData;
    ret = GmcExecuteAsync(g_stmt_async, &deleteRequestCtx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asyncData.status);
    EXPECT_EQ(1, asyncData.affectRows);

    //唯一索引等值查找
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "local_key");
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &p_primary_label, sizeof(p_primary_label));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &p_attribute_id, sizeof(p_attribute_id));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // fetch 等值字段值
    ret = test_localkey_scan_ip4forward_value(stmt, p_scanValue);
    // EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

//异步连接，建带local key的表，批量DML操作，等值查询（唯一索引）
TEST_F(LocalKeyScanBasic, LocalKeyScanBasic_010)
{
    int ret;
    // schema json 定义 local key
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_localkey.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);

    // create vertex label
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 异步批量插入顶点
    int oper_nums = 1024;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = 1; i <= oper_nums; i++) {
        ret = test_batch_async_insert_vertex_ip4forward(g_stmt_async, i);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_async);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // 执行异步批量操作
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &asyncData);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    ASSERT_EQ(GMERR_OK, asyncData.status);
    ASSERT_EQ(oper_nums, asyncData.totalNum);
    ASSERT_EQ(oper_nums, asyncData.succNum);
    GmcBatchReset(batch);

    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "local_key");
    EXPECT_EQ(GMERR_OK, ret);

    //唯一local索引等值查找
    uint32_t p_scanValue = 1024;
    uint32_t p_primary_label = p_scanValue;
    uint32_t p_attribute_id = p_scanValue;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &p_primary_label, sizeof(p_primary_label));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &p_attribute_id, sizeof(p_attribute_id));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // fetch 等值字段值
    ret = test_localkey_scan_ip4forward_value(stmt, p_scanValue);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

//异步连接，建带local key的表，批量DML操作，等值更新，查询（唯一索引）
TEST_F(LocalKeyScanBasic, LocalKeyScanBasic_011)
{
    int ret;
    // schema json 定义 local key
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_localkey.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);

    // create vertex label
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 异步批量插入顶点
    int oper_nums = 1024;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = 1; i <= oper_nums; i++) {
        ret = test_batch_async_insert_vertex_ip4forward(g_stmt_async, i);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_async);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // 执行异步批量操作
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &asyncData);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    ASSERT_EQ(GMERR_OK, asyncData.status);
    ASSERT_EQ(oper_nums, asyncData.totalNum);
    ASSERT_EQ(oper_nums, asyncData.succNum);
    GmcBatchReset(batch);

    ////唯一hash索引异步批量等值更新
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t p_scanValue = 1024;
    uint32_t p_primary_label = p_scanValue;
    uint32_t p_attribute_id = p_scanValue;
    ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &p_primary_label, sizeof(p_primary_label));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_async, 1, GMC_DATATYPE_UINT32, &p_attribute_id, sizeof(p_attribute_id));
    EXPECT_EQ(GMERR_OK, ret);

    unsigned int coefficient = 3;
    uint8_t set_uint8 = wr_uint8 * coefficient;
    uint16_t set_uint16 = wr_uint16 * coefficient;
    uint32_t set_uint32 = uint32_tmp * coefficient;
    uint64_t set_uint64 = wr_uint64 * coefficient;
    ret = GmcSetVertexProperty(g_stmt_async, "mask_len", GMC_DATATYPE_UINT8, &set_uint8, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt_async, "app_obj_id", GMC_DATATYPE_UINT64, &set_uint64, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt_async, "app_version", GMC_DATATYPE_UINT32, &set_uint32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt_async, "reserved", GMC_DATATYPE_UINT16, &set_uint16, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_async, "local_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_async);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &asyncData);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    ASSERT_EQ(GMERR_OK, asyncData.status);
    ASSERT_EQ(1, asyncData.totalNum);
    ASSERT_EQ(1, asyncData.succNum);
    GmcBatchDestroy(batch);

    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "local_key");
    EXPECT_EQ(GMERR_OK, ret);

    //唯一local索引等值查找
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &p_primary_label, sizeof(p_primary_label));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &p_attribute_id, sizeof(p_attribute_id));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // fetch 等值字段值
    ret = test_localkey_scan_ip4forward_value(stmt, p_scanValue, coefficient);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

//异步连接，建带local key的表，批量DML操作，等值删除，查询（唯一索引）
TEST_F(LocalKeyScanBasic, LocalKeyScanBasic_012)
{
    int ret;
    // schema json 定义 local key
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_localkey.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);

    // create vertex label
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 异步批量插入顶点
    int oper_nums = 1024;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = 1; i <= oper_nums; i++) {
        ret = test_batch_async_insert_vertex_ip4forward(g_stmt_async, i);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_async);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // 执行异步批量操作
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &asyncData);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    ASSERT_EQ(GMERR_OK, asyncData.status);
    ASSERT_EQ(oper_nums, asyncData.totalNum);
    ASSERT_EQ(oper_nums, asyncData.succNum);
    GmcBatchReset(batch);

    // 删除顶点
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t p_scanValue = 1024;
    uint32_t p_primary_label = p_scanValue;
    uint32_t p_attribute_id = p_scanValue;
    ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &p_primary_label, sizeof(p_primary_label));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_async, 1, GMC_DATATYPE_UINT32, &p_attribute_id, sizeof(p_attribute_id));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_async, "local_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_async);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &asyncData);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    ASSERT_EQ(GMERR_OK, asyncData.status);
    ASSERT_EQ(1, asyncData.totalNum);
    ASSERT_EQ(1, asyncData.succNum);
    GmcBatchDestroy(batch);

    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "local_key");
    EXPECT_EQ(GMERR_OK, ret);

    //唯一local索引等值查找
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &p_primary_label, sizeof(p_primary_label));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &p_attribute_id, sizeof(p_attribute_id));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // fetch 等值字段值
    ret = test_localkey_scan_ip4forward_value(stmt, p_scanValue);
    // EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

//同步连接，建带local key的表，批量DML操作，等值查询（非唯一索引）
TEST_F(LocalKeyScanBasic, LocalKeyScanBasic_013)
{
    int ret;
    // schema json 定义 local key
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_localkey.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);

    // create vertex label
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    //批量写数据
    int oper_nums = 1024;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = 1; i <= oper_nums; i++) {
        ret = test_batch_async_insert_vertex_ip4forward(stmt, i);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(oper_nums, totalNum);
    ASSERT_EQ(oper_nums, successNum);
    GmcBatchDestroy(batch);

    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    EXPECT_EQ(GMERR_OK, ret);
    // 非唯一hash索引更新
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    printf("\n========== non-uniq hash index scan: qos_profile_id, nhp_group_id ===============\n");
    ret = test_hashInx_scan_ip4forward(stmt, "localhash_key", oper_nums, 0, "nonUni Hash Scan", 0);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

//同步连接，建带local key的表，批量DML操作，等值更新，查询（非唯一索引）
TEST_F(LocalKeyScanBasic, LocalKeyScanBasic_014)
{
    int ret;
    // schema json 定义 local key
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_localkey.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);

    // create vertex label
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    //批量写数据
    int oper_nums = 1024;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = 1; i <= oper_nums; i++) {
        ret = test_batch_async_insert_vertex_ip4forward(stmt, i);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(oper_nums, totalNum);
    ASSERT_EQ(oper_nums, successNum);
    GmcBatchReset(batch);

    //唯一hash索引批量等值更新
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t p_scanValue = 1024;
    uint32_t p_primary_label = p_scanValue;
    uint32_t p_attribute_id = p_scanValue;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &p_primary_label, sizeof(p_primary_label));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &p_attribute_id, sizeof(p_attribute_id));
    EXPECT_EQ(GMERR_OK, ret);

    unsigned int coefficient = 3;
    uint8_t set_uint8 = wr_uint8 * coefficient;
    uint16_t set_uint16 = wr_uint16 * coefficient;
    uint32_t set_uint32 = uint32_tmp * coefficient;
    uint64_t set_uint64 = wr_uint64 * coefficient;
    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &set_uint8, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &set_uint64, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &set_uint32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "reserved", GMC_DATATYPE_UINT16, &set_uint16, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "local_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(1, totalNum);
    ASSERT_EQ(1, successNum);
    GmcBatchDestroy(batch);

    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    EXPECT_EQ(GMERR_OK, ret);
    // 非唯一hash索引扫描
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    printf("\n========== non-uniq hash index scan: qos_profile_id, nhp_group_id ===============\n");
    ret = test_hashInx_scan_ip4forward(stmt, "localhash_key", oper_nums, 0, "nonUni Hash Scan", 0);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

//同步连接，建带local key的表，批量DML操作，等值删除，查询（非唯一索引）
TEST_F(LocalKeyScanBasic, LocalKeyScanBasic_015)
{
    int ret;
    // schema json 定义 local key
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_localkey.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);

    // create vertex label
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    //批量写数据
    int oper_nums = 1024;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = 1; i <= oper_nums; i++) {
        ret = test_batch_async_insert_vertex_ip4forward(stmt, i);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(oper_nums, totalNum);
    ASSERT_EQ(oper_nums, successNum);
    GmcBatchReset(batch);

    //唯一hash索引批量等值删除
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t p_scanValue = 1024;
    uint32_t p_primary_label = p_scanValue;
    uint32_t p_attribute_id = p_scanValue;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &p_primary_label, sizeof(p_primary_label));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &p_attribute_id, sizeof(p_attribute_id));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "local_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(1, totalNum);
    ASSERT_EQ(1, successNum);
    GmcBatchDestroy(batch);

    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    EXPECT_EQ(GMERR_OK, ret);
    // 非唯一hash索引扫描
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    printf("\n========== non-uniq hash index scan: qos_profile_id, nhp_group_id ===============\n");
    ret = test_hashInx_scan_ip4forward(stmt, "localhash_key", oper_nums, 0, "nonUni Hash Scan", 0);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

//异步连接，建带local key的表，批量DML操作，等值查询（非唯一索引）
TEST_F(LocalKeyScanBasic, LocalKeyScanBasic_016)
{
    int ret;
    // schema json 定义 local key
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_localkey.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);

    // create vertex label
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 异步批量插入顶点
    int oper_nums = 1024;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = 1; i <= oper_nums; i++) {
        ret = test_batch_async_insert_vertex_ip4forward(g_stmt_async, i);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_async);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // 执行异步批量操作
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &asyncData);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    ASSERT_EQ(GMERR_OK, asyncData.status);
    ASSERT_EQ(oper_nums, asyncData.totalNum);
    ASSERT_EQ(oper_nums, asyncData.succNum);
    GmcBatchDestroy(batch);

    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    EXPECT_EQ(GMERR_OK, ret);
    // 非唯一hash索引扫描
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    printf("\n========== non-uniq hash index scan: qos_profile_id, nhp_group_id ===============\n");
    ret = test_hashInx_scan_ip4forward(stmt, "localhash_key", oper_nums, 0, "nonUni Hash Scan", 0);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

//异步连接，建带local key的表，普通DML操作，等值更新，查询（唯一索引）
TEST_F(LocalKeyScanBasic, LocalKeyScanBasic_017)
{
    int ret;
    // schema json 定义 local key
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_localkey.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);

    // create vertex label
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 异步批量插入顶点
    int oper_nums = 1024;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = 1; i <= oper_nums; i++) {
        ret = test_batch_async_insert_vertex_ip4forward(g_stmt_async, i);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_async);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // 执行异步批量操作
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &asyncData);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    ASSERT_EQ(GMERR_OK, asyncData.status);
    ASSERT_EQ(oper_nums, asyncData.totalNum);
    ASSERT_EQ(oper_nums, asyncData.succNum);
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchReset(batch);

    ////唯一hash索引异步批量等值更新
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t p_scanValue = 1024;
    uint32_t p_primary_label = p_scanValue;
    uint32_t p_attribute_id = p_scanValue;
    ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &p_primary_label, sizeof(p_primary_label));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_async, 1, GMC_DATATYPE_UINT32, &p_attribute_id, sizeof(p_attribute_id));
    EXPECT_EQ(GMERR_OK, ret);

    unsigned int coefficient = 3;
    uint8_t set_uint8 = wr_uint8 * coefficient;
    uint16_t set_uint16 = wr_uint16 * coefficient;
    uint32_t set_uint32 = uint32_tmp * coefficient;
    uint64_t set_uint64 = wr_uint64 * coefficient;
    ret = GmcSetVertexProperty(g_stmt_async, "mask_len", GMC_DATATYPE_UINT8, &set_uint8, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt_async, "app_obj_id", GMC_DATATYPE_UINT64, &set_uint64, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt_async, "app_version", GMC_DATATYPE_UINT32, &set_uint32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt_async, "reserved", GMC_DATATYPE_UINT16, &set_uint16, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_async, "local_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_async);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &asyncData);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    ASSERT_EQ(GMERR_OK, asyncData.status);
    ASSERT_EQ(1, asyncData.totalNum);
    ASSERT_EQ(1, asyncData.succNum);
    GmcBatchDestroy(batch);

    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    EXPECT_EQ(GMERR_OK, ret);
    // 非唯一hash索引扫描
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    printf("\n========== non-uniq hash index scan: qos_profile_id, nhp_group_id ===============\n");
    ret = test_hashInx_scan_ip4forward(stmt, "localhash_key", oper_nums, 0, "nonUni Hash Scan", 0);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

//异步连接，建带local key的表，批量DML操作，等值删除，查询（非唯一索引）
TEST_F(LocalKeyScanBasic, LocalKeyScanBasic_018)
{
    int ret;
    // schema json 定义 local key
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_localkey.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);

    // create vertex label
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 异步批量插入顶点
    int oper_nums = 1024;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = 1; i <= oper_nums; i++) {
        ret = test_batch_async_insert_vertex_ip4forward(g_stmt_async, i);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_async);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // 执行异步批量操作
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &asyncData);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    ASSERT_EQ(GMERR_OK, asyncData.status);
    ASSERT_EQ(oper_nums, asyncData.totalNum);
    ASSERT_EQ(oper_nums, asyncData.succNum);
    GmcBatchReset(batch);

    // 删除顶点
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t p_scanValue = 1024;
    uint32_t p_primary_label = p_scanValue;
    uint32_t p_attribute_id = p_scanValue;
    ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &p_primary_label, sizeof(p_primary_label));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_async, 1, GMC_DATATYPE_UINT32, &p_attribute_id, sizeof(p_attribute_id));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_async, "local_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_async);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &asyncData);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncData);
    ASSERT_EQ(GMERR_OK, asyncData.status);
    ASSERT_EQ(1, asyncData.totalNum);
    ASSERT_EQ(1, asyncData.succNum);
    GmcBatchDestroy(batch);

    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    EXPECT_EQ(GMERR_OK, ret);
    // 非唯一hash索引扫描
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    printf("\n========== non-uniq hash index scan: qos_profile_id, nhp_group_id ===============\n");
    ret = test_hashInx_scan_ip4forward(stmt, "localhash_key", oper_nums, 0, "nonUni Hash Scan", 0);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 同步连接, 建带local key的表, 普通DML操作, 等值查询+范围查询
TEST_F(LocalKeyScanBasic, LocalKeyScanBasic_200)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // schema json 定义 local key
    char *schemaJson = NULL;
    readJanssonFile("./schema_file/ip4forward_localkey.gmjson", &schemaJson);
    ASSERT_NE((void *)NULL, schemaJson);

    // create vertex label
    ret = GmcCreateVertexLabel(stmt, schemaJson, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(schemaJson);

    // 写入数据
    int operNums = 9;
    ret = test_insert_vertex_ip4forward(stmt, 0, operNums);
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "local_key");
    EXPECT_EQ(GMERR_OK, ret);

    // 唯一local索引等值查找
    uint32_t scanValue = 9;
    uint32_t primaryLabel = scanValue;
    uint32_t attributeId = scanValue;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &primaryLabel, sizeof(primaryLabel));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &attributeId, sizeof(attributeId));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        cnt++;
    }
    AW_FUN_Log(LOG_DEBUG, "[INFO]========value of cnt=%d\n", cnt);
    EXPECT_EQ(1, cnt);
    char cmd[1024] = {0};
    (void)snprintf(cmd, 1024, "%s/gmsysview record %s -ns %s", g_toolPath, g_labelName, g_testNameSpace);
    ret = executeCommand(cmd, "primary_label", "9", "attribute_id", "9");
    EXPECT_EQ(GMERR_OK, ret);

    // local索引区间扫描
    const char *keyName = "local_key";
    uint32_t arrLen = 1;
    uint32_t leftVal = 8;
    uint32_t rightVal = 9;
    GmcPropValueT *leftKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    leftKeyProps[0].type = GMC_DATATYPE_UINT32;
    leftKeyProps[0].value = &leftVal;
    leftKeyProps[0].size = sizeof(leftVal);
    GmcPropValueT *rightKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    rightKeyProps[0].type = GMC_DATATYPE_UINT32;
    rightKeyProps[0].value = &rightVal;
    rightKeyProps[0].size = sizeof(rightVal);

    GmcRangeItemT items[arrLen];
    items[0].lValue = &leftKeyProps[0];
    items[0].rValue = &rightKeyProps[0];
    items[0].lFlag = GMC_COMPARE_RANGE_OPEN;
    items[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].order = GMC_ORDER_ASC;

    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetKeyRange(stmt, items, arrLen);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    isFinish = false;
    cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        cnt++;
    }
    AW_FUN_Log(LOG_DEBUG, "[INFO]========value of cnt=%d\n", cnt);
    EXPECT_EQ(1, cnt);
    free(leftKeyProps);
    free(rightKeyProps);
    ret = GmcDropVertexLabel(stmt, g_labelName);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
