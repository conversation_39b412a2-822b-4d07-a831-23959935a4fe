[{"version": "2.0", "type": "record", "name": "Vertex_01_partition", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": false}, {"name": "F2", "type": "uint32", "nullable": false}, {"name": "F3", "type": "uint32", "nullable": false}, {"name": "F4", "type": "uint32", "nullable": false}, {"name": "F5", "type": "uint32", "nullable": false}, {"name": "F6", "type": "uint32", "nullable": false}, {"name": "F7", "type": "string", "size": 7, "nullable": true}, {"name": "F8", "type": "partition", "nullable": false}], "keys": [{"name": "Vertex_pk", "index": {"type": "primary"}, "fields": ["F0"], "constraints": {"unique": true}}, {"name": "Vertex_local_u", "index": {"type": "local"}, "fields": ["F1"], "constraints": {"unique": true}}, {"name": "Vertex_local", "index": {"type": "local"}, "fields": ["F2"], "constraints": {"unique": false}}, {"name": "Vertex_localhash_u", "index": {"type": "<PERSON><PERSON><PERSON>"}, "fields": ["F3"], "constraints": {"unique": true}}, {"name": "Vertex_localhash", "index": {"type": "<PERSON><PERSON><PERSON>"}, "fields": ["F4"], "constraints": {"unique": false}}, {"name": "Vertex_hashcluster_u", "index": {"type": "hashcluster"}, "fields": ["F5"], "constraints": {"unique": true}}, {"name": "Vertex_hashcluster", "index": {"type": "hashcluster"}, "fields": ["F6"], "constraints": {"unique": false}}]}]