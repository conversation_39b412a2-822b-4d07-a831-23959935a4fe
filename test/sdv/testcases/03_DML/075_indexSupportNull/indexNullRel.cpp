/* ****************************************************************************
 Description  :索引支持NULL值可靠性测试
001.多线程并发插入带空值的唯一localhash索引属性
002.多线程并发插入带空值的唯一local索引属性
003.多线程并发插入带空值的唯一hashcluster索引属性
004.多线程并发更新同一条记录的同一个节点唯一memberkey带空值的索引属性

 Author       : wuxiaochun wx753022
 Modification :
 Date         : 2021/11/8
**************************************************************************** */

#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include <sys/time.h>
#include <time.h>

#include "gtest/gtest.h"
#include "indexKeySupportNull.h"

pthread_mutex_t g_indexSupportNull_lock;
int g_dml_success = 0;
int g_dml_fail = 0;

class indexKeySupportNullReliability : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void indexKeySupportNullReliability::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/start.sh");
    int ret = 0;
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    ASSERT_EQ(GMERR_OK, ret);
}

void indexKeySupportNullReliability::TearDownTestCase()
{
    int ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    testEnvClean();
}

void indexKeySupportNullReliability::SetUp()
{
    int ret = 0;
    int64_t valueInt64 = 1;
    char *strValue = (char *)"indexKeyNullTest";
    char *bytesValue = (char *)"1111111";
    char *fixedValue = (char *)"fixeds";
    ret = indexKeySupportNullCreateLabel();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    indexKeyNullLabel1InsertVertex(g_stmt, 0, 10, true, strValue, fixedValue, bytesValue);
    indexKeyNullLabel2InsertVertex(g_stmt, 0, 10, true, strValue, fixedValue, bytesValue);
    AW_CHECK_LOG_BEGIN();
    char errorMsg1[128] = {0};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
}

void indexKeySupportNullReliability::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = GmcDropVertexLabel(g_stmt, g_indexKeyNullLabel1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_indexKeyNullLabel2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
}

void *localhashUniqueThread(void *args)
{
    int ret = 0;
    int32_t pkindex = *(int32_t *)args;
    GmcNodeT *root = NULL, *T1 = NULL;
    int32_t indexValue = 100;
    int32_t indexEnd = 105;
    int32_t indexEnd2 = 110;
    bool isFinish = false;
    char strValue[21] = "test666";
    char *bytesValue = (char *)"1111111";
    uint8_t fixedValue[INDEX_FIXED_FIELD_LENGTH] = {0};
    int32_t vector_num = 3;
    bool isNull = false;
    uint8_t f18_value[LPM6_FIXED_FIELD_LENGTH] = {
        0xcd, 0xcd, 0x91, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00, 0x20, 0x21};
    GmcStmtT *stmt_t = NULL;
    GmcConnT *conn_t = NULL;
    ret = testGmcConnect(&conn_t, &stmt_t);
    EXPECT_EQ(GMERR_OK, ret);
    //设置localhash属性值为空值插入
    int64_t valueInt64 = pkindex;
    int32_t i = pkindex;
    ret = testGmcPrepareStmtByLabelName(stmt_t, g_indexKeyNullLabel2, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    indexKeySupportNullGetNode(stmt_t, &root, &T1);
    indexKeyNullSetHashclusterProperty(root, i, (uint8_t *)fixedValue);
    indexKeyNullSetLocalKeyProperty(root, i, (uint8_t *)fixedValue);
    indexKeyNullTest1SetPropertyExtra(root, i, strValue, bytesValue);
    indexKeyNullSetPk(root, &valueInt64, strValue, bytesValue);
    f18_value[15] = i;
    indexLpm6Set(root, i, f18_value);
    indexKeyNullLabel2SetLocalhashPropertyNull(root);
    // 插入array节点
    for (uint32_t j = 0; j < vector_num; j++) {
        ret = GmcNodeAppendElement(T1, &T1);
        EXPECT_EQ(GMERR_OK, ret);
        indexKeyNullSetPropertyNodeT1(T1, j, true, strValue, fixedValue, bytesValue);
    }
    ret = GmcExecute(stmt_t);
    pthread_mutex_lock(&g_indexSupportNull_lock);
    if (ret == GMERR_OK) {
        g_dml_success++;
    } else if (ret == GMERR_UNIQUE_VIOLATION) {
        g_dml_fail++;
    } else {
        EXPECT_EQ(GMERR_UNIQUE_VIOLATION, ret);
    }
    pthread_mutex_unlock(&g_indexSupportNull_lock);
    ret = testGmcDisconnect(conn_t, stmt_t);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

// 001.多线程并发插入带空值的唯一localhash索引属性
TEST_F(indexKeySupportNullReliability, DML_075_004_001)
{
    int32_t ret = 0;
    int err = 0;
    int thm = 10;
    int32_t pkindex[thm];
    memset(pkindex, 0, thm * sizeof(int32_t));
    ret = pthread_mutex_init(&g_indexSupportNull_lock, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    pthread_t localhashT[thm];
    for (int i = 0; i < thm; i++) {
        pkindex[i] = i + 100;
        err = pthread_create(&localhashT[i], NULL, localhashUniqueThread, &pkindex[i]);
        EXPECT_EQ(GMERR_OK, err);
    }
    for (int i = 0; i < thm; i++) {
        pthread_join(localhashT[i], NULL);
    }
    if ((g_dml_success == 1) && (g_dml_fail == (thm - 1))) {
        EXPECT_EQ(GMERR_OK, 0);
    } else {
        printf("g_dml_success: %d g_dml_fail: %d\n", g_dml_success, g_dml_fail);
        EXPECT_EQ(GMERR_OK, 1);
    }
    g_dml_success = 0;
    g_dml_fail = 0;
    pthread_mutex_destroy(&g_indexSupportNull_lock);
}

void *localUniqueThread(void *args)
{
    int ret = 0;
    int32_t pkindex = *(int32_t *)args;
    GmcNodeT *root = NULL, *T1 = NULL;
    int32_t indexValue = 100;
    int32_t indexEnd = 105;
    int32_t indexEnd2 = 110;
    bool isFinish = false;
    char *strValue = (char *)"indexKeyNullTest";
    char *bytesValue = (char *)"1111111";
    char *fixedValue = (char *)"fixeds";
    int32_t vector_num = 3;
    bool isNull = false;
    int64_t valueInt64 = pkindex;
    int32_t i = pkindex;
    GmcStmtT *stmt_t = NULL;
    GmcConnT *conn_t = NULL;

    ret = testGmcConnect(&conn_t, &stmt_t);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt_t, g_indexKeyNullLabel1, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    indexKeySupportNullGetNode(stmt_t, &root, &T1);
    indexKeyNullSetHashclusterProperty(root, i, (uint8_t *)fixedValue);
    indexKeyNullSetLocalKeyProperty(root, i, (uint8_t *)fixedValue, true);
    indexKeyNullTest1SetPropertyExtra(root, i, strValue, bytesValue);
    indexKeyNullSetPk(root, &valueInt64, strValue, bytesValue);
    indexKeyNullLabel1SetLocalhashProperty(root, i, i, false, strValue);
    indexLpm4Set(root, i);
    // 插入array节点
    for (uint32_t j = 0; j < vector_num; j++) {
        ret = GmcNodeAppendElement(T1, &T1);
        EXPECT_EQ(GMERR_OK, ret);
        indexKeyNullSetPropertyNodeT1(T1, j, true, strValue, (uint8_t *)fixedValue, bytesValue);
    }
    ret = GmcExecute(stmt_t);
    pthread_mutex_lock(&g_indexSupportNull_lock);
    if (ret == GMERR_OK) {
        g_dml_success++;
    } else if (ret == GMERR_UNIQUE_VIOLATION) {
        g_dml_fail++;
    } else {
        EXPECT_EQ(GMERR_UNIQUE_VIOLATION, ret);
    }
    pthread_mutex_unlock(&g_indexSupportNull_lock);
    ret = testGmcDisconnect(conn_t, stmt_t);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

// 002.多线程并发插入带空值的唯一local索引属性
TEST_F(indexKeySupportNullReliability, DML_075_004_002)
{
    int32_t ret = 0;
    int err = 0;
    int thm = 10;
    int32_t pkindex[thm];
    memset(pkindex, 0, thm * sizeof(int32_t));
    ret = pthread_mutex_init(&g_indexSupportNull_lock, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    pthread_t localT[thm];
    for (int i = 0; i < thm; i++) {
        pkindex[i] = i + 100;
        err = pthread_create(&localT[i], NULL, localUniqueThread, &pkindex[i]);
        EXPECT_EQ(GMERR_OK, err);
    }
    for (int i = 0; i < thm; i++) {
        pthread_join(localT[i], NULL);
    }
    if ((g_dml_success == thm) && (g_dml_fail == 0))  // 2022.01.17 compatibleV3=1时local/hashcluster的唯一性不存在适配
    {
        EXPECT_EQ(GMERR_OK, 0);
    } else {
        printf("g_dml_success: %d g_dml_fail: %d\n", g_dml_success, g_dml_fail);
        EXPECT_EQ(GMERR_OK, 1);
    }
    g_dml_success = 0;
    g_dml_fail = 0;
    pthread_mutex_destroy(&g_indexSupportNull_lock);
}

void *hashClulterUniqueThread(void *args)
{
    int ret = 0;
    int32_t pkindex = *(int32_t *)args;
    GmcNodeT *root = NULL, *T1 = NULL;
    int32_t indexValue = 100;
    int32_t indexEnd = 105;
    int32_t indexEnd2 = 110;
    bool isFinish = false;
    char *strValue = (char *)"indexKeyNullTest";
    char *bytesValue = (char *)"1111111";
    char *fixedValue = (char *)"fixeds";
    int32_t vector_num = 3;
    bool isNull = false;
    int64_t valueInt64 = pkindex;
    int32_t i = pkindex;
    GmcStmtT *stmt_t = NULL;
    GmcConnT *conn_t = NULL;

    ret = testGmcConnect(&conn_t, &stmt_t);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt_t, g_indexKeyNullLabel1, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    indexKeySupportNullGetNode(stmt_t, &root, &T1);
    indexKeyNullSetHashclusterProperty(root, i, (uint8_t *)fixedValue, true);
    indexKeyNullSetLocalKeyProperty(root, i, (uint8_t *)fixedValue);
    indexKeyNullTest1SetPropertyExtra(root, i, strValue, bytesValue);
    indexKeyNullSetPk(root, &valueInt64, strValue, bytesValue);
    indexKeyNullLabel1SetLocalhashProperty(root, i, i, false, strValue);
    indexLpm4Set(root, i);
    // 插入array节点
    for (uint32_t j = 0; j < vector_num; j++) {
        ret = GmcNodeAppendElement(T1, &T1);
        EXPECT_EQ(GMERR_OK, ret);
        indexKeyNullSetPropertyNodeT1(T1, j, true, strValue, (uint8_t *)fixedValue, bytesValue);
    }
    ret = GmcExecute(stmt_t);
    pthread_mutex_lock(&g_indexSupportNull_lock);
    if (ret == GMERR_OK) {
        g_dml_success++;
    } else if (ret == GMERR_UNIQUE_VIOLATION) {
        g_dml_fail++;
    } else {
        EXPECT_EQ(GMERR_UNIQUE_VIOLATION, ret);
    }
    pthread_mutex_unlock(&g_indexSupportNull_lock);
    ret = testGmcDisconnect(conn_t, stmt_t);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}
// 003.多线程并发插入带空值的唯一hashcluster索引属性
TEST_F(indexKeySupportNullReliability, DML_075_004_003)
{
    int32_t ret = 0;
    int err = 0;
    int thm = 10;
    int32_t pkindex[thm];
    memset(pkindex, 0, thm * sizeof(int32_t));
    ret = pthread_mutex_init(&g_indexSupportNull_lock, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    pthread_t hashclusterT[thm];
    for (int i = 0; i < thm; i++) {
        pkindex[i] = i + 100;
        err = pthread_create(&hashclusterT[i], NULL, hashClulterUniqueThread, &pkindex[i]);
        EXPECT_EQ(GMERR_OK, err);
    }
    for (int i = 0; i < thm; i++) {
        pthread_join(hashclusterT[i], NULL);
    }
    if ((g_dml_success == thm) && (g_dml_fail == 0))  // 2022.01.17 compatibleV3=1时local/hashcluster的唯一性不存在适配
    {
        EXPECT_EQ(GMERR_OK, 0);
    } else {
        printf("g_dml_success: %d g_dml_fail: %d\n", g_dml_success, g_dml_fail);
        EXPECT_EQ(GMERR_OK, 1);
    }
    g_dml_success = 0;
    g_dml_fail = 0;
    pthread_mutex_destroy(&g_indexSupportNull_lock);
}

void *memberKeyUniqueThread(void *args)
{
    int ret = 0;
    GmcNodeT *root = NULL, *T1 = NULL;
    GmcNodeT *keyNode = NULL;
    bool isFinish = false;
    char *strValue = (char *)"indexKeyNullTest";
    char *bytesValue = (char *)"1111111";
    uint8_t bytesPk[INDEX_FIXED_FIELD_LENGTH] = {0};
    char *fixedValue = (char *)"fixeds";
    int32_t vector_num = 3;
    bool isNull = false;
    uint8_t f18_value[LPM6_FIXED_FIELD_LENGTH] = {
        0xcd, 0xcd, 0x91, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00, 0x20, 0x21};
    uint8_t fixedT[8] = {0};
    memset(fixedT, 0, INDEX_FIXED_FIELD_LENGTH);
    char strtest[21] = "";
    GmcStmtT *stmt_t = NULL;
    GmcConnT *conn_t = NULL;

    ret = testGmcConnect(&conn_t, &stmt_t);
    EXPECT_EQ(GMERR_OK, ret);
    //设置memberkey空值插入
    int64_t valueInt64 = 1;
    int32_t i = 1;
    ret = testGmcPrepareStmtByLabelName(stmt_t, g_indexKeyNullLabel2, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    indexKeyNullLabel1SetPkIndex(stmt_t, g_PkName2, i, strValue, bytesValue);
    indexKeySupportNullGetNode(stmt_t, &root, &T1);
    // 插入array节点
    for (uint32_t j = 0; j < 1; j++) {
        ret = GmcNodeAppendElement(T1, &T1);
        EXPECT_EQ(GMERR_OK, ret);
        indexKeyNullSetPropertyMemberKey2(T1, j);
        indexKeyNullSetPropertyNodeT1Extra(T1, j, true, bytesValue);
        // indexKeyNullSetPropertyMemberKey(T1,j,strValue,fixedValue,true);
    }
    ret = GmcExecute(stmt_t);
    pthread_mutex_lock(&g_indexSupportNull_lock);
    if (ret == GMERR_OK) {
        g_dml_success++;
    } else if (ret == GMERR_MEMBER_KEY_VIOLATION) {
        g_dml_fail++;
    } else {
        EXPECT_EQ(GMERR_MEMBER_KEY_VIOLATION, ret);
    }
    pthread_mutex_unlock(&g_indexSupportNull_lock);
    ret = testGmcDisconnect(conn_t, stmt_t);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

// 004.多线程并发更新同一条记录的同一个节点唯一memberkey带空值的索引属性
TEST_F(indexKeySupportNullReliability, DML_075_004_004)
{
    int32_t ret = 0;
    int err = 0;
    int thm = 10;
    int32_t pkindex[thm];
    memset(pkindex, 0, thm * sizeof(int32_t));
    char errorMsg1[128] = {0};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_MEMBER_KEY_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    ret = pthread_mutex_init(&g_indexSupportNull_lock, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    pthread_t memberKeyT[thm];
    for (int i = 0; i < thm; i++) {
        pkindex[i] = i + 100;
        err = pthread_create(&memberKeyT[i], NULL, memberKeyUniqueThread, &pkindex[i]);
        EXPECT_EQ(GMERR_OK, err);
    }
    for (int i = 0; i < thm; i++) {
        pthread_join(memberKeyT[i], NULL);
    }
    if ((g_dml_success == 1) && (g_dml_fail == (thm - 1))) {
        EXPECT_EQ(GMERR_OK, 0);
    } else {
        printf("g_dml_success: %d g_dml_fail: %d\n", g_dml_success, g_dml_fail);
        EXPECT_EQ(GMERR_OK, 1);
    }
    g_dml_success = 0;
    g_dml_fail = 0;
    pthread_mutex_destroy(&g_indexSupportNull_lock);
}
