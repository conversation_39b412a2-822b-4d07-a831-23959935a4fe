[{"type": "record", "name": "vertexTable", "fields": [{"name": "F0", "type": "int64", "nullable": false}, {"name": "F1", "type": "uint64", "nullable": true}, {"name": "F2", "type": "int32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "int16", "nullable": true}, {"name": "F5", "type": "uint16", "nullable": true}, {"name": "F6", "type": "int8", "nullable": true}, {"name": "F7", "type": "uint8", "nullable": true}, {"name": "F8", "type": "boolean", "nullable": true}, {"name": "F9", "type": "float", "nullable": true}, {"name": "F10", "type": "double", "nullable": true}, {"name": "F11", "type": "time", "nullable": true}, {"name": "F12", "type": "char", "nullable": true}, {"name": "F13", "type": "uchar", "nullable": true}, {"name": "F14", "type": "fixed", "size": 16, "nullable": true}, {"name": "F16", "type": "string", "size": 100, "nullable": true}, {"name": "F17", "type": "bytes", "size": 100, "nullable": true}], "keys": [{"node": "TEST_T3", "name": "TEST_PK", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]