/*****************************************************************************
 Description  : GmcKvPrepareStmtByLabelName 接口测试
    DML_066_001. 创建KV表,调用接口,接口参数GmcStmtT *stmt填写正确,const char *kvTableName填写正确,打开Kv表成功
    DML_066_002. 创建KV表,调用接口,接口参数GmcStmtT *stmt填写正确,const char *kvTableName填写为空,全局打开Kv表成功
    DML_066_003. 创建KV表,调用接口,接口参数GmcStmtT *stmt填写正确,const char *kvTableName填写错误,打开Kv表失败
    DML_066_004. 创建KV表,调用接口,接口参数GmcStmtT *stmt填写为空,const char *kvTableName填写正确,打开Kv表失败
    DML_066_005. 创建KV表,调用接口,接口参数GmcStmtT *stmt填写为空,const char *kvTableName填写错误,打开Kv表失败
    DML_066_006. 创建KV表,调用接口,接口参数GmcStmtT *stmt填写为空,const char *kvTableName填写为空,打开Kv表失败
    DML_066_007. 创建KV表,调用接口,接口参数GmcStmtT *stmt填写错误,const char *kvTableName填写正确,打开Kv表失败
    DML_066_008. 创建KV表,调用接口,接口参数GmcStmtT *stmt填写错误,const char *kvTableName填写错误,打开Kv表失败
    DML_066_009. 创建KV表,调用接口,接口参数GmcStmtT *stmt填写错误,const char *kvTableName填写为空,打开Kv表失败
    DML_066_010. 创建KV表,连接1调用接口,插入数据,之后连接2调用接口,清空表数据错误,释放一个连接句柄,再次清空表数据成功
    DML_066_011. 创建KV表,调用接口100次,清空表数据成功
    DML_066_012. 创建KV表,开启事务,调用接口,打开Kv表，提交事务成功
    DML_066_013. 创建KV表,创建建1个namespace,打开Kv表成功
    DML_066_014. 异步连接,创建KV表,调用接口,接口参数GmcStmtT *stmt填写正确,const char
*kvTableName为正确,普通打开KV表成功 DML_066_015. 异步连接,创建KV表,调用接口,接口参数GmcStmtT *stmt填写正确,const char
*kvTableName为空,全局打开KV表成功 DML_066_016. 不创建表,调用接口,打开KV表成功 DML_066_017.
创建KV表,连接1调用接口,插入数据,之后连接2调用接口,清空表数据错误,重置连接2,清空表数据成功 DML_066_018.
多个stmt打开同一张kv表时，验证truncate操作结果 Notes        : History      : Author       : wensiqi wwx1060458 Create
: 2021.08.02 Modification :
*****************************************************************************/

#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <pthread.h>
#include <semaphore.h>
#include <errno.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
extern "C" {
}
#define KVNAME_MAX_LENGTH 128
GmcConnT *conn = NULL;
GmcStmtT *stmt = NULL;
void *kvtable = NULL;
GmcKvTupleT kvInfo = {0};
int32_t value = 100;
char key[] = "zhangsan";
int ret;
char g_configJson[128] = "{\"max_record_num\" : 10000}";
char g_tableName[KVNAME_MAX_LENGTH] = "KV0";
char g_label_schema[1024] =
    "[{\"type\":\"record\", \"name\":\"KV0\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
    "{\"name\":\"F1\", \"type\":\"int32\", \"nullable\" : false , \"default\" : 1},{\"name\":\"F2\", "
    "\"type\":\"int\"},{\"name\":\"F3\", \"type\":\"int32\"}],"
    "\"keys\":[{\"node\":\"KV0\", \"name\":\"KV0_K0\", \"fields\":[\"F0\"], "
    "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]";
class GmcKvPrepareStmtByLabelName_test : public testing::Test {
public:
    static void SetUpTestCase()
    {
        // 重启server
        system("sh $TEST_HOME/tools/start.sh ");
        int res = testEnvInit();
        ASSERT_EQ(GMERR_OK, res);
        res = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, res);
    };

    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
    };
    virtual void SetUp();
    virtual void TearDown();
};

void GmcKvPrepareStmtByLabelName_test::SetUp()
{
    printf("\n======================TEST:BEGIN======================\n");

    // 创建同步客户端连接
    int ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    GmcKvDropTable(stmt, g_tableName);
    AW_CHECK_LOG_BEGIN();
}

void GmcKvPrepareStmtByLabelName_test::TearDown()
{
    printf("\n======================TEST:END========================\n");
    AW_CHECK_LOG_END();
    GmcKvDropTable(stmt, g_tableName);
    // 关闭 client connection
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 001.创建KV表,调用接口,接口参数GmcStmtT *stmt填写正确,const char *kvTableName填写正确,打开Kv表成功
TEST_F(GmcKvPrepareStmtByLabelName_test, DML_066_001_GmcKvPrepareStmtByLabelName_test_001)
{
    // 创建kv表
    ret = GmcKvCreateTable(stmt, g_tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvSet(stmt, &key, sizeof(char), &value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcKvRemove(stmt, key, strlen(key));
    EXPECT_EQ(GMERR_OK, ret);
    // 删除kv表
    ret = GmcKvDropTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 002. 创建KV表,调用接口,接口参数GmcStmtT *stmt填写正确,const char *kvTableName填写为空,打开全部Kv表成功
TEST_F(GmcKvPrepareStmtByLabelName_test, DML_066_002_GmcKvPrepareStmtByLabelName_test_002)
{
    // 创建kv表
    ret = GmcKvCreateTable(stmt, g_tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    const char *kvTableName = NULL;
    ret = GmcKvPrepareStmtByLabelName(stmt, kvTableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvSet(stmt, &key, sizeof(char), &value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvRemove(stmt, key, strlen(key));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 003. 创建KV表,调用接口,接口参数GmcStmtT *stmt填写正确,const char *kvTableName填写错误,打开Kv表失败
TEST_F(GmcKvPrepareStmtByLabelName_test, DML_066_003_GmcKvPrepareStmtByLabelName_test_003)
{
    // 创建kv表
    ret = GmcKvCreateTable(stmt, g_tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    const char *kvTableName = "kvTableName";
    ret = GmcKvPrepareStmtByLabelName(stmt, kvTableName);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = GmcKvDropTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 004. 创建KV表,调用接口,接口参数GmcStmtT *stmt填写为空,const char *kvTableName填写正确,打开Kv表失败
TEST_F(GmcKvPrepareStmtByLabelName_test, DML_066_004_GmcKvPrepareStmtByLabelName_test_004)
{
    char g_errorCode[1024] = {0};
    (void)snprintf(g_errorCode, 1024, "GMERR-%d", GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode);
    // 创建kv表
    ret = GmcKvCreateTable(stmt, g_tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    GmcStmtT *g_stmt = NULL;
    ret = GmcKvPrepareStmtByLabelName(g_stmt, g_tableName);
    ASSERT_NE(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_stmt);
}

// 005. 创建KV表,调用接口,接口参数GmcStmtT *stmt填写为空,const char *kvTableName填写错误,打开Kv表失败
TEST_F(GmcKvPrepareStmtByLabelName_test, DML_066_005_GmcKvPrepareStmtByLabelName_test_005)
{
    char g_errorCode[1024] = {0};
    (void)snprintf(g_errorCode, 1024, "GMERR-%d", GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode);
    // 创建kv表
    ret = GmcKvCreateTable(stmt, g_tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    GmcStmtT *g_stmt = NULL;
    const char *kvTableName = kvTableName;
    ret = GmcKvPrepareStmtByLabelName(g_stmt, kvTableName);
    ASSERT_NE(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_stmt);
}

// 006. 创建KV表,调用接口,接口参数GmcStmtT *stmt填写为空,const char *kvTableName填写为空,打开Kv表失败
TEST_F(GmcKvPrepareStmtByLabelName_test, DML_066_006_GmcKvPrepareStmtByLabelName_test_006)
{
    char g_errorCode[1024] = {0};
    (void)snprintf(g_errorCode, 1024, "GMERR-%d", GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode);
    // 创建kv表
    ret = GmcKvCreateTable(stmt, g_tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    GmcStmtT *g_stmt = NULL;
    const char *kvTableName = NULL;
    ret = GmcKvPrepareStmtByLabelName(g_stmt, kvTableName);
    ASSERT_NE(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_stmt);
}

// 007. 创建KV表,调用接口,接口参数GmcStmtT *stmt填写错误,const char *kvTableName填写正确,打开Kv表失败
TEST_F(GmcKvPrepareStmtByLabelName_test, DML_066_007_GmcKvPrepareStmtByLabelName_test_007)
{
    char g_errorCode[1024] = {0};
    (void)snprintf(g_errorCode, 1024, "GMERR-%d", GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode);
    // 创建kv表
    ret = GmcKvCreateTable(stmt, g_tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    GmcStmtT *g_stmt = 0;
    ret = GmcKvPrepareStmtByLabelName(g_stmt, g_tableName);
    ASSERT_NE(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_stmt);
}

// 008. 创建KV表,调用接口,接口参数GmcStmtT *stmt填写错误,const char *kvTableName填写错误,打开Kv表失败
TEST_F(GmcKvPrepareStmtByLabelName_test, DML_066_007_GmcKvPrepareStmtByLabelName_test_008)
{
    char g_errorCode[1024] = {0};
    (void)snprintf(g_errorCode, 1024, "GMERR-%d", GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode);
    // 创建kv表
    ret = GmcKvCreateTable(stmt, g_tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    GmcStmtT *g_stmt = 0;
    const char *kvTableName = kvTableName;
    ret = GmcKvPrepareStmtByLabelName(g_stmt, kvTableName);
    ASSERT_NE(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_stmt);
}

// 009. 创建KV表,调用接口,接口参数GmcStmtT *stmt填写错误,const char *kvTableName填写为空,打开Kv表失败
TEST_F(GmcKvPrepareStmtByLabelName_test, DML_066_009_GmcKvPrepareStmtByLabelName_test_009)
{
    char g_errorCode[1024] = {0};
    (void)snprintf(g_errorCode, 1024, "GMERR-%d", GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode);
    // 创建kv表
    ret = GmcKvCreateTable(stmt, g_tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    GmcStmtT *g_stmt = 0;
    const char *kvTableName = NULL;
    ret = GmcKvPrepareStmtByLabelName(g_stmt, kvTableName);
    ASSERT_NE(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_stmt);
}

// 010. 创建KV表,连接1调用接口,插入数据,之后连接2调用接口,清空表数据错误,释放一个连接句柄,清空表数据成功
TEST_F(GmcKvPrepareStmtByLabelName_test, DML_066_010_GmcKvPrepareStmtByLabelName_test_010)
{
    GmcStmtT *g_stmt = NULL;
    ret = GmcAllocStmt(conn, &g_stmt);
    // 创建kv表
    ret = GmcKvCreateTable(stmt, g_tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(g_stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvTruncateTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    // EXPECT_EQ(OBJECT_IN_USE,ret);//OBJECT_IN_USE删除
    GmcFreeStmt(g_stmt);
    ret = GmcKvTruncateTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 011. 创建KV表,调用接口100次,清空表数据成功
TEST_F(GmcKvPrepareStmtByLabelName_test, DML_066_011_GmcKvPrepareStmtByLabelName_test_011)
{
    // 创建kv表
    ret = GmcKvCreateTable(stmt, g_tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 100; i++) {
        ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcKvTruncateTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 012. 创建KV表,开启事务,调用接口,打开Kv表，提交事务成功
TEST_F(GmcKvPrepareStmtByLabelName_test, DML_066_012_GmcKvPrepareStmtByLabelName_test_012)
{
    char Label_config[] = "{\"max_record_num\":1000000, \"isFastReadUncommitted\":0}";

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    ret = GmcKvCreateTable(stmt, g_tableName, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcTransStart(conn, &config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    // 提交事务
    ret = GmcTransCommit(conn);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvSet(stmt, &key, sizeof(char), &value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    // 删除kv表
    ret = GmcKvDropTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 013. 创建KV表,创建建1个namespace,打开Kv表成功
TEST_F(GmcKvPrepareStmtByLabelName_test, DML_066_013_GmcKvPrepareStmtByLabelName_test_013)
{
    void *kvtable = NULL;
    const char *nameSpace = (const char *)"use1a";
    const char *g_nameSpace_userName = (const char *)"apple";
    ret = GmcKvCreateTable(stmt, g_tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateNamespace(stmt, nameSpace, g_nameSpace_userName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvSet(stmt, &key, sizeof(char), &value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    // 删除kv表
    ret = GmcKvDropTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropNamespace(stmt, nameSpace);
    EXPECT_EQ(GMERR_OK, ret);
}

// 014. 异步连接,创建KV表,调用接口,接口参数GmcStmtT *stmt填写正确,const char *kvTableName为正确,普通打开KV表成功
TEST_F(GmcKvPrepareStmtByLabelName_test, DML_066_014_GmcKvPrepareStmtByLabelName_test_014)
{
    AsyncUserDataT data = {0};
    void *kvtable = NULL;
    GmcStmtT *g_stmt_async = NULL;
    GmcConnT *g_conn_async = NULL;
    GmcKvTupleT kvInfo = {0};
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key);
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(int32_t);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    // 异步创建kvtable	正常场景
    ret = GmcKvCreateTableAsync(g_stmt_async, g_tableName, g_configJson, create_kv_table_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    //异步打开kv表
    ret = GmcKvPrepareStmtByLabelName(g_stmt_async, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvSetAsync(g_stmt_async, &kvInfo, set_kv_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    //删除kv table
    ret = GmcKvDropTableAsync(g_stmt_async, g_tableName, drop_kv_table_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
}

// 015. 异步连接,创建KV表,调用接口,接口参数GmcStmtT *stmt填写正确,const char *kvTableName为空,全局打开KV表成功
TEST_F(GmcKvPrepareStmtByLabelName_test, DML_066_015_GmcKvPrepareStmtByLabelName_test_015)
{
    AsyncUserDataT data = {0};
    void *kvtable = NULL;
    GmcStmtT *g_stmt_async = NULL;
    GmcConnT *g_conn_async = NULL;
    GmcKvTupleT kvInfo = {0};
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key);
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(int32_t);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTableAsync(g_stmt_async, g_tableName, g_configJson, create_kv_table_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    //异步打开kv表
    ret = GmcKvPrepareStmtByLabelName(g_stmt_async, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvSetAsync(g_stmt_async, &kvInfo, set_kv_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    // 删除kv表
    ret = GmcKvDropTableAsync(g_stmt_async, g_tableName, drop_kv_table_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
}

// 016. 不创建KV表,调用接口,打开KV表成功
TEST_F(GmcKvPrepareStmtByLabelName_test, DML_066_016_GmcKvPrepareStmtByLabelName_test_016)
{
    ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
}

// 017. 创建KV表,连接1调用接口,插入数据,之后连接2调用接口,清空表数据错误,重置连接2,清空表数据成功
TEST_F(GmcKvPrepareStmtByLabelName_test, DML_066_017_GmcKvPrepareStmtByLabelName_test_017)
{
    GmcStmtT *g_stmt = NULL;
    int rew = testGmcConnect(&conn, &g_stmt);
    // 创建kv表
    ret = GmcKvCreateTable(stmt, g_tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(g_stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvTruncateTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    // EXPECT_EQ(OBJECT_IN_USE,ret);//OBJECT_IN_USE删除
    GmcResetStmt(g_stmt);
    ret = GmcKvTruncateTable(g_stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 018. 多个stmt打开同一张kv表时，验证truncate操作结果
TEST_F(GmcKvPrepareStmtByLabelName_test, DML_066_018_GmcKvPrepareStmtByLabelName_test_018)
{
    GmcConnT *conn01 = NULL;
    GmcStmtT *stmt01 = NULL;
    // 创建客户端连接
    ret = testGmcConnect(&conn01, &stmt01);
    EXPECT_EQ(GMERR_OK, ret);
    GmcConnT *conn02 = NULL;
    GmcStmtT *stmt02 = NULL;
    // 创建客户端连接
    ret = testGmcConnect(&conn02, &stmt02);
    EXPECT_EQ(GMERR_OK, ret);

    char kvtableName01[128] = "K-K-k";
    char kvtableName02[128] = "K-K-V";
    const char *configJson = R"({"max_record_num":10000,"max_record_num_check":true})";
    //创建kv表
    ret = GmcKvCreateTable(stmt01, kvtableName01, configJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTable(stmt02, kvtableName02, configJson);
    EXPECT_EQ(GMERR_OK, ret);
    // stmt01 open kvtableName01
    ret = GmcKvPrepareStmtByLabelName(stmt01, kvtableName01);
    EXPECT_EQ(GMERR_OK, ret);
    // stmt02 open kvtableName01
    ret = GmcKvPrepareStmtByLabelName(stmt02, kvtableName01);
    EXPECT_EQ(GMERR_OK, ret);
    //两个stmt同时打开同一张kv表，truncate kv 表会报错
    ret = GmcKvTruncateTable(stmt01, kvtableName01);
    EXPECT_EQ(GMERR_OK, ret);
    // EXPECT_EQ(OBJECT_IN_USE,ret);//OBJECT_IN_USE删除
    // stmt02 open kv kvtableName02
    ret = GmcKvPrepareStmtByLabelName(stmt02, kvtableName02);
    EXPECT_EQ(GMERR_OK, ret);
    //通过再次打开第2张kv表
    // truncate kv 表成功
    ret = GmcKvTruncateTable(stmt01, kvtableName01);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcKvDropTable(stmt01, kvtableName01);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt02, kvtableName02);
    EXPECT_EQ(GMERR_OK, ret);
    // disconnect
    ret = testGmcDisconnect(conn01, stmt01);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn02, stmt02);
    EXPECT_EQ(GMERR_OK, ret);
}
