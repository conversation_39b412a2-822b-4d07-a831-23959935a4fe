[{"version": "2.0", "type": "record", "name": "Tree_3layer", "comment": "Tree第一二三层含有变长字段", "fields": [{"name": "P1", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "Default_StrSize0", "type": "string", "nullable": true}, {"name": "Default_BytSize0", "type": "bytes", "nullable": true}, {"name": "Eq_Str64K0", "type": "string", "size": 65536, "nullable": true}, {"name": "Eq_Byt64K0", "type": "bytes", "size": 65536, "nullable": true}, {"name": "F2", "type": "record", "fields": [{"name": "A1", "type": "uint32", "nullable": true}, {"name": "A2", "type": "uint32", "nullable": true}, {"name": "P2", "type": "uint32", "nullable": true}, {"name": "Default_StrSize1", "type": "string", "nullable": true}, {"name": "Default_BytSize1", "type": "bytes", "nullable": true}, {"name": "Eq_Str64K1", "type": "string", "size": 65536, "nullable": true}, {"name": "Eq_Byt64K1", "type": "bytes", "size": 65536, "nullable": true}, {"name": "A3", "type": "record", "array": true, "size": 3, "fields": [{"name": "Default_StrSize2", "type": "string", "nullable": true}, {"name": "Default_BytSize2", "type": "bytes", "nullable": true}, {"name": "P3", "type": "uint32", "nullable": true}, {"name": "Eq_Str64K2", "type": "string", "size": 65536, "nullable": true}, {"name": "Eq_Byt64K2", "type": "bytes", "size": 65536, "nullable": true}, {"name": "B3", "type": "record", "vector": true, "size": 3, "fields": [{"name": "Default_StrSize3", "type": "string", "nullable": true}, {"name": "Default_BytSize3", "type": "bytes", "nullable": true}, {"name": "P4", "type": "uint32", "nullable": true}, {"name": "Eq_Str64K3", "type": "string", "size": 65536, "nullable": true}, {"name": "Eq_Byt64K3", "type": "bytes", "size": 65536, "nullable": true}, {"name": "Eq_Str64K4", "type": "string", "size": 65536, "nullable": true}, {"name": "Eq_Byt64K4", "type": "bytes", "size": 65536, "nullable": true}, {"name": "Eq_Str64K5", "type": "string", "size": 65536, "nullable": true}, {"name": "Eq_Byt64K5", "type": "bytes", "size": 65536, "nullable": true}, {"name": "Eq_Str64K6", "type": "string", "size": 65536, "nullable": true}, {"name": "Eq_Byt64K6", "type": "bytes", "size": 65536, "nullable": true}, {"name": "Eq_Str64K7", "type": "string", "size": 65536, "nullable": true}, {"name": "Eq_Byt64K7", "type": "bytes", "size": 65536, "nullable": true}, {"name": "Eq_Str64K8", "type": "string", "size": 65536, "nullable": true}, {"name": "Eq_Byt64K8", "type": "bytes", "size": 65536, "nullable": true}, {"name": "Eq_Str64K9", "type": "string", "size": 65536, "nullable": true}, {"name": "Eq_Byt64K9", "type": "bytes", "size": 65536, "nullable": true}, {"name": "Eq_Str64K10", "type": "string", "size": 65536, "nullable": true}, {"name": "Eq_Byt64K10", "type": "bytes", "size": 65536, "nullable": true}, {"name": "Eq_Str64K11", "type": "string", "size": 65536, "nullable": true}, {"name": "Eq_Byt64K11", "type": "bytes", "size": 65536, "nullable": true}, {"name": "Eq_Str64K12", "type": "string", "size": 65536, "nullable": true}, {"name": "Eq_Byt64K12", "type": "bytes", "size": 65536, "nullable": true}, {"name": "Eq_Str64K13", "type": "string", "size": 65536, "nullable": true}, {"name": "Eq_Byt64K13", "type": "bytes", "size": 65536, "nullable": true}, {"name": "Eq_Str64K14", "type": "string", "size": 65536, "nullable": true}, {"name": "Eq_Byt64K14", "type": "bytes", "size": 65536, "nullable": true}, {"name": "Eq_Str64K15", "type": "string", "size": 65536, "nullable": true}, {"name": "Eq_Byt64K15", "type": "bytes", "size": 65536, "nullable": true}, {"name": "Eq_Str64K16", "type": "string", "size": 65536, "nullable": true}, {"name": "Eq_Byt64K16", "type": "bytes", "size": 65536, "nullable": true}, {"name": "Eq_Str64K17", "type": "string", "size": 65536, "nullable": true}, {"name": "Eq_Byt64K17", "type": "bytes", "size": 65536, "nullable": true}]}]}]}], "keys": [{"name": "Tree_key1", "index": {"type": "primary"}, "node": "Tree_3layer", "fields": ["P1"], "constraints": {"unique": true}, "comment": "主键索引"}]}]