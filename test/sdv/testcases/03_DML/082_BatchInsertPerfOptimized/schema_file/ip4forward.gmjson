[{"version": "2.0", "type": "record", "name": "batch_set_ds_cfg", "fields": [{"name": "vr_id", "type": "uint32", "comment": "Vs索引", "nullable": false}, {"name": "vrf_index", "type": "uint32", "comment": "VpnInstace索引", "nullable": true}, {"name": "dest_ip_addr", "type": "uint32", "comment": "目的地址", "nullable": true}, {"name": "mask_len", "type": "uint8", "comment": "掩码长度", "nullable": true}, {"name": "nhp_group_flag", "type": "uint8", "comment": "标识Nhp或NhpG", "nullable": true}, {"name": "qos_profile_id", "type": "uint16", "comment": "QosID", "nullable": true}, {"name": "primary_label", "type": "uint32", "comment": "标签", "nullable": true}, {"name": "attribute_id", "type": "uint32", "comment": "属性ID", "nullable": true}, {"name": "nhp_group_id", "type": "uint32", "comment": "下一跳索引还是下一跳组索引，根据nhp_group_flag决定", "nullable": true}, {"name": "path_flags", "type": "uint32", "comment": "path标记", "nullable": true}, {"name": "flags", "type": "uint32", "comment": "标志(path完备性)", "nullable": true}, {"name": "status_high_prio", "type": "uint8", "nullable": true}, {"name": "status_normal_prio", "type": "uint8", "nullable": true}, {"name": "errcode_high_prio", "type": "uint8", "nullable": true}, {"name": "errcode_normal_prio", "type": "uint8", "nullable": true}, {"name": "svc_ctx_high_prio", "type": "fixed", "size": 16, "nullable": true}, {"name": "T2", "type": "int32"}, {"name": "C1", "type": "int32", "nullable": true}, {"name": "C2", "type": "int8", "nullable": true}, {"name": "C3", "type": "uint8", "nullable": true}, {"name": "C4", "type": "char", "nullable": true}, {"name": "C5", "type": "uchar", "nullable": true}, {"name": "C6", "type": "string", "size": 100, "nullable": true}, {"name": "T3", "type": "int32", "nullable": true}, {"name": "D1", "type": "int32", "nullable": true}, {"name": "D2", "type": "int8", "nullable": true}, {"name": "D3", "type": "uint8", "nullable": true}, {"name": "D4", "type": "uint32", "nullable": true}, {"name": "D5", "type": "char", "nullable": true}, {"name": "D6", "type": "uchar", "nullable": true}, {"name": "D7", "type": "string", "size": 100, "nullable": true}, {"name": "D8", "type": "int64", "nullable": true}, {"name": "D9", "type": "uint64", "nullable": true}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "batch_set_ds_cfg", "fields": ["vr_id", "vrf_index"], "constraints": {"unique": true}, "comment": "根据主键索引"}]}]