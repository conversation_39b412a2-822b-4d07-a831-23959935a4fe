/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */
#include "./op.h"

GmcConnT *g_conn_sub;
GmcStmtT *g_stmt_sub;
const char *g_subName = "subVertexLabel";
const char *g_subConnName = "subConnName";
class localSub : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/modifyCfg.sh \"compatibleV3=0\"");
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    }

public:
    SnUserDataT *user_data;
    virtual void SetUp();
    virtual void TearDown();
};

void localSub::SetUp()
{
    g_conn = NULL;
    g_stmt = NULL;
    g_conn_sub = NULL;
    g_stmt_sub = NULL;
    char errorMsg1[128] = {};
    char errorMsg2[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    (void)snprintf(errorMsg2, sizeof(errorMsg1), "GMERR-%d", GMERR_PRIMARY_KEY_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);
    int ret = testSnMallocUserData(&user_data, 100 * 10000);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建订阅连接
    int chanRingLen = 256;
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}
void localSub::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    testSnFreeUserData(user_data);
}

void sn_callback(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret;
    int i;
    SnUserDataT *user_data_test = (SnUserDataT *)userData;
    char labelName[512] = {0};
    unsigned int labelNameLen = 512;
    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }
        for (i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = 512;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(strlen(labelName), labelNameLen);
            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_AGED: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    // 调试时可打开 AW_FUN_Log(LOG_DEBUG, "[INFO]GMC_SUB_EVENT_AGED");
                    break;
                }
                default: {
                    printf("default: invalid eventType\r\n");
                    break;
                }
            }
        }
        user_data_test->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data_test->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data_test->agedNum++;
                break;
            }
        }
    }
}

// 001.订阅推送：验证推送老化后重新写入的数据(age+insert)
TEST_F(localSub, DML_096_localSub_001)
{
    AW_FUN_Log(LOG_STEP, "localSub start.");
    int ret = 0;
    bool isAbnormal = false;
    int startVal = 0;
    int endVal = 100;
    char *schemaSimple = NULL;
    const char *labelName = "localLabel";
    readJanssonFile("schemaFile/localLabel.gmjson", &schemaSimple);
    ASSERT_NE((void *)NULL, schemaSimple);
    ret = GmcCreateVertexLabel(g_stmt, schemaSimple, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schemaSimple);

    char *subInfo = NULL;
    readJanssonFile("schemaFile/subInfoRel.gmjson", &subInfo);
    EXPECT_NE((void *)NULL, subInfo);

    ret = TestLabelInsert(g_stmt, labelName, startVal, endVal); // 同步单写
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = subInfo;
    ret = GmcSubscribe(g_stmt, &tmp_g_sub_info, g_conn_sub, sn_callback, user_data); // 下发订阅
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(subInfo);

    ret = GmcBeginCheck(g_stmt, labelName, FULLTABLE); // 开启对账
    EXPECT_EQ(GMERR_OK, ret);

    TestInsertLocalCon(g_stmt, labelName, startVal, endVal); // 同步单写写入冲突数据

    ret = GmcEndCheck(g_stmt, labelName, FULLTABLE, isAbnormal); // 结束对账
    EXPECT_EQ(GMERR_OK, ret);

    ret = TestLabelInsert(g_stmt, labelName, startVal, endVal * 2); // 同步单写
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_AGED, endVal);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, endVal * 2);
    EXPECT_EQ(GMERR_OK, ret);
    
    ret = GmcUnSubscribe(g_stmt, g_subName); //取消订阅
    EXPECT_EQ(GMERR_OK, ret);

    TestLocalRangeScan(g_stmt, labelName, startVal, endVal * 2); // local索引区间扫描

    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "localSub end.");
}
