[{"type": "record", "name": "TreeNonEmptyAttributeCheck_root_PARTITION", "fields": [{"name": "F0", "type": "int64", "nullable": false}, {"name": "F1", "type": "partition", "nullable": false}, {"name": "T1", "type": "record", "fields": [{"name": "P0", "type": "int64", "nullable": true}]}], "keys": [{"node": "TreeNonEmptyAttributeCheck_root_PARTITION", "name": "TreeNonEmptyAttributeCheck_root_PARTITION_PK", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]