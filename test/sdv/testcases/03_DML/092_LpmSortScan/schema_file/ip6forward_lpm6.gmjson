[{"type": "record", "name": "ip6forward", "fields": [{"name": "primary_label", "type": "uint32", "comment": "标签", "nullable": false}, {"name": "attribute_id", "type": "uint32", "comment": "属性ID", "nullable": false}, {"name": "vr_id", "type": "uint32", "comment": "Vs索引", "nullable": false}, {"name": "vrf_index", "type": "uint32", "comment": "VpnInstace索引", "nullable": false}, {"name": "dest_ip_addr", "type": "fixed", "size": 16, "comment": "目的地址", "nullable": false}, {"name": "mask_len", "type": "uint8", "comment": "掩码长度", "nullable": false}, {"name": "nhp_group_flag", "type": "uint8", "comment": "标识Nhp或NhpG", "nullable": true}, {"name": "qos_profile_id", "type": "uint16", "comment": "QosID", "nullable": true}, {"name": "nhp_group_id", "type": "uint8", "comment": "下一跳索引还是下一跳组索引，根据nhp_group_flag决定", "nullable": true}, {"name": "path_flags", "type": "uint32", "comment": "path标记", "nullable": true}, {"name": "flags", "type": "uint32", "comment": "标志(path完备性)", "nullable": true}, {"name": "status_high_prio", "type": "uint8", "nullable": true}, {"name": "status_normal_prio", "type": "uint8", "nullable": true}, {"name": "errcode_high_prio", "type": "uint8", "nullable": true}, {"name": "errcode_normal_prio", "type": "uint8", "default": 1, "nullable": true}, {"name": "svc_ctx_high_prio", "type": "fixed", "size": 34, "default": "ffffffffffffffffffffffffffffffffff", "comment": "高优先级FWM_SERVICE返回的svcCtx", "nullable": true}, {"name": "svc_ctx_normal_prio", "type": "fixed", "size": 34, "default": "ffffffffffffffffffffffffffffffffff", "comment": "普通优先级FWM_SERVICE返回的svcCtx", "nullable": true}, {"name": "app_source_id", "type": "uint32", "nullable": true}, {"name": "table_smooth_id", "type": "uint32", "nullable": true}, {"name": "app_obj_id", "type": "uint64", "nullable": true}, {"name": "app_version", "type": "uint32", "nullable": true}, {"name": "trace", "type": "uint64", "nullable": true}, {"name": "route_flags", "type": "uint16", "comment": "路由标记", "nullable": true}, {"name": "reserved", "type": "uint16", "comment": "预留", "nullable": true}, {"name": "test_str", "type": "string", "size": 100, "nullable": true}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "ip6forward", "fields": ["primary_label", "attribute_id"], "constraints": {"unique": true}, "comment": "根据主键索引"}, {"name": "local_key", "node": "ip6forward", "index": {"type": "local"}, "fields": ["vr_id", "vrf_index", "dest_ip_addr", "mask_len"], "comment": "local索引"}, {"name": "lpm6_key", "node": "ip6forward", "index": {"type": "lpm6_tree_bitmap"}, "fields": ["vr_id", "vrf_index", "dest_ip_addr", "mask_len"], "constraints": {"unique": true}, "comment": "根据lpm6_tree_bitmap索引"}]}]