#include "gm_v5_get_error_tools.h"
#include "t_datacom_lite.h"

/* ****************************************************************************
 Description  : 获取操作动态错误信息测试
 Author       : pwx623912
 Create       : 2021.04.25 (测试时只适配了KV建表)
 Info         : 提供获取错误信息的框架, 2021/6/26日已整改错误码, lasterr信息未完善
**************************************************************************** */

class GetErrorInfoCreateKvTable : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        int32_t ret = 0;
        system("sh $TEST_HOME/tools/start.sh");
        ret = testEnvInit();
        ASSERT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        ASSERT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void GetErrorInfoCreateKvTable::SetUp()
{
    int32_t ret = 0;
    memset(g_conn_thread, 0, sizeof(void *) * THRAD_NUM);
    memset(g_stmt_rd, 0, sizeof(void *) * THRAD_NUM);
    memset(g_vertexLabel_rd, 0, sizeof(void *) * THRAD_NUM);

    ret = testSnMallocUserData(&user_data, RECORD_COUNT * 100);
    ASSERT_EQ(GMERR_OK, ret);

    (void)pthread_mutex_init(&LockSubChannel, NULL);
    printf("GetErrorInfoCreateKvTable Start.\n");
    // 创建异步连接
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    ASSERT_EQ(GMERR_OK, ret);
    // 创建同步连接
    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // 默认建顶三个顶点
    ret = func_create_vertex_label_sync((char *)"schema_file/complex_typical.gmjson", stmt, g_configJson, label_name01);
    ASSERT_EQ(GMERR_OK, ret);
    ret = func_create_vertex_label_sync((char *)"schema_file/ip4forward.gmjson", stmt, g_configJson, label_name02);
    ASSERT_EQ(GMERR_OK, ret);
    ret = func_create_vertex_label_sync((char *)"schema_file/T39_all_type.gmjson", stmt, g_configJson, label_name03);
    ASSERT_EQ(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN();
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    char errorMsg3[errCodeLen] = {0};
    char errorMsg4[errCodeLen] = {0};
    char errorMsg5[errCodeLen] = {0};
    char errorMsg6[errCodeLen] = {0};
    char errorMsg7[errCodeLen] = {0};
    char errorMsg8[errCodeLen] = {0};
    char errorMsg9[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_INVALID_NAME);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_DUPLICATE_TABLE);
    (void)snprintf(errorMsg3, errCodeLen, "GMERR-%d", GMERR_NULL_VALUE_NOT_ALLOWED);
    (void)snprintf(errorMsg4, errCodeLen, "GMERR-%d", GMERR_INVALID_VALUE);
    (void)snprintf(errorMsg5, errCodeLen, "GMERR-%d", GMERR_INVALID_JSON_CONTENT);
    (void)snprintf(errorMsg6, errCodeLen, "GMERR-%d", GMERR_INVALID_PROPERTY);
    (void)snprintf(errorMsg7, errCodeLen, "GMERR-%d", GMERR_TOO_MANY_CONNECTIONS);
    (void)snprintf(errorMsg8, errCodeLen, "GMERR-%d", GMERR_BATCH_BUFFER_FULL);
    (void)snprintf(errorMsg9, errCodeLen, "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    AW_ADD_ERR_WHITE_LIST(
        9, errorMsg1, errorMsg2, errorMsg3, errorMsg4, errorMsg5, errorMsg6, errorMsg7, errorMsg8, errorMsg9);
}
void GetErrorInfoCreateKvTable::TearDown()
{
    AW_CHECK_LOG_END();
    int32_t ret = 0;
    // 边信息删除后才能删除相应的顶点
    GmcKvDropTable(stmt, label_name01);
    GmcKvDropTable(stmt, kv_table_name_sync);
    GmcKvDropTable(stmt, kv_table_name_async);
    GmcDropVertexLabel(stmt, label_name01);
    GmcDropVertexLabel(stmt, label_name02);
    GmcDropVertexLabel(stmt, label_name03);
    // 断连同步/异步连接
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 断开非 epoll 异步连接
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    testSnFreeUserData(user_data);
    printf("GetErrorInfoCreateKvTable End.\n");
}

/* ****************************************************************************
 Description  : 同步/异步 正常创建kv表无错误信息时, GmcGetLastError接口 conn 传NULL
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_001)
{
    int32_t ret = 0;
    AsyncUserDataT data = {0};
    const char *lastErrorStr = NULL;  // 同步异步创建kv表, 异步必须通过接口 GmcRecvRespMsgAsync
    ret = GmcKvDropTable(stmt, kv_table_name_sync);  // 来获取一下结果,不然上面一次的结果会积累到下一次
    ASSERT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = GmcKvDropTableAsync(g_stmt_async, kv_table_name_async, drop_kv_table_callback,
        &data);  // 同步/异步删kv表,接口是同一个接口
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_UNDEFINED_TABLE, data.status);
    // ASSERT_EQ(GMERR_UNDEFINED_TABLE, ret);
    // 同步创建kv表 和 异步创建kv表
    ret = GmcKvCreateTable(stmt, kv_table_name_sync, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTableAsync(g_stmt_async, kv_table_name_async, g_configJson, create_kv_table_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_OK, data.status);

    ret = GmcKvPrepareStmtByLabelName(stmt, kv_table_name_sync);
    ASSERT_EQ(GMERR_OK, ret);

    char errorInfo[512] = {0};
    lastErrorStr = GmcGetLastError();  // 不管是同步还是异步, 连接传空

    ret = GmcKvDropTable(stmt, kv_table_name_sync);  // 删除kv表
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, kv_table_name_async);
    ASSERT_EQ(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  : 同步/异步 正常创建kv表无错误信息时, GmcGetLastError接口 errorInfo 传 NULL
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_002)
{
    int32_t ret = 0;
    AsyncUserDataT data = {0};
    // 同步创建kv表 和 异步创建kv表
    ret = GmcKvCreateTable(stmt, kv_table_name_sync, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTableAsync(g_stmt_async, kv_table_name_async, g_configJson, create_kv_table_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_OK, data.status);

    // 异步打开kv表
    ret = GmcKvPrepareStmtByLabelName(stmt, kv_table_name_sync);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(g_stmt_async, kv_table_name_sync);
    ASSERT_EQ(GMERR_OK, ret);

    // 删除kv表
    ret = GmcKvDropTable(stmt, kv_table_name_sync);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, kv_table_name_async);
    ASSERT_EQ(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  : 同步/异步 正常创建kv表无错误信息时, GmcGetLastError接口
                conn 传 非法void *入参, conn参数,传入错误的参数stmt
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_003)
{
    int32_t ret = 0;
    AsyncUserDataT data = {0};
    const char *lastErrorStr = NULL;
    // 同步创建kv表 和 异步创建kv表
    ret = GmcKvCreateTable(stmt, kv_table_name_sync, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTableAsync(g_stmt_async, kv_table_name_async, g_configJson, create_kv_table_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_OK, data.status);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // 异步打开kv表
    ret = GmcKvPrepareStmtByLabelName(g_stmt_async, kv_table_name_sync);
    ASSERT_EQ(GMERR_OK, ret);

    GmcKvTupleT kvInfo = {0};
    char key[] = "apple";
    uint32_t value = 30;
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key);
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(uint32_t);
    ret = GmcKvSetAsync(g_stmt_async, &kvInfo, set_kv_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_OK, data.status);
}
/* ****************************************************************************
 Description  : 同步/异步 异常创建 kv 表有错误信息时, GmcGetLastError接口 conn 传NULL
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_004)
{
    int32_t ret = 0;
    char labelName_err01[MAX_NAME_LENGTH] = "match";
    const char *lastErrorStr = NULL;
    AsyncUserDataT data = {0};
    // 构造稳定的lasterror信息，防止被环境干扰
    GmcKvDropTable(stmt, labelName_err01);
    GmcKvDropTable(stmt, labelName_err01);
    // 同步创建kv表 和 异步创建kv表

    ret = GmcKvCreateTable(stmt, labelName_err01, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, labelName_err01);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcKvCreateTableAsync(g_stmt_async, labelName_err01, g_configJson, create_kv_table_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_OK, data.status);
    ret = GmcKvDropTable(stmt, labelName_err01);
    ASSERT_EQ(GMERR_OK, ret);

    // 获取namespace id
    int32_t nameSpaceId = 0;
    char cmd[256] = "00";
    (void)sprintf(cmd, "CATA_NAMESPACE_INFO -f NAMESPACE_NAME=%s", g_testNameSpace);
    nameSpaceId = GetViewValueByField(cmd, "NAMESPACE_ID");
    AW_FUN_Log(LOG_STEP, "<namespace: %s id is %d>", g_testNameSpace, nameSpaceId);

    lastErrorStr = GmcGetLastError();
    char expectLastError[128] = "";
    (void)sprintf(expectLastError, "Undefined table. Namespace id:%d, label name: match, cataCache name: kvTableCache.",
        nameSpaceId);
    EXPECT_STREQ(lastErrorStr, expectLastError);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 同步/异步 异常创建 kv 表有错误信息时, GmcGetLastError接口 errorInfo 传 NULL
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_005)
{
    int32_t ret = 0;
    const char *lastErrorStr = NULL;
    AsyncUserDataT data = {0};
    // 同步创建kv表 和 异步创建kv表
    char labelName_err01[MAX_NAME_LENGTH] = "match";
    char labelName_err02[MAX_NAME_LENGTH] = "return";
    ret = GmcKvCreateTable(stmt, labelName_err01, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcKvCreateTableAsync(g_stmt_async, labelName_err02, g_configJson, create_kv_table_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_OK, data.status);

    lastErrorStr = GmcGetLastError();
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, labelName_err01);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTableAsync(g_stmt_async, labelName_err02, drop_kv_table_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
}
/* ****************************************************************************
 Description  : 同步/异步 异常创建 kv 表有错误信息时, GmcGetLastError接口
                conn 传 非法void *入参, conn参数,传入错误的参数stmt
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_006)
{
    int32_t ret = 0;
    const char *lastErrorStr = NULL;
    AsyncUserDataT data = {0};
    // 同步创建kv表 和 异步创建kv表
    char labelName_err01[MAX_NAME_LENGTH] = "@";
    char labelName_err02[MAX_NAME_LENGTH] = "return";
    ret = GmcKvCreateTable(stmt, labelName_err01, g_configJson);
    ASSERT_EQ(GMERR_INVALID_NAME, ret);

    ret = GmcKvCreateTableAsync(g_stmt_async, labelName_err02, g_configJson, create_kv_table_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_OK, data.status);

    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    lastErrorStr = GmcGetLastError();
    EXPECT_STREQ(lastErrorStr, "Not normal name. inv object name:@, the first character is:@.");
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, labelName_err02);
    ASSERT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 同步/异步 异常创建 kv 表有错误信息时, GmcGetLastError接口调用1万次
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_007)
{
    int32_t ret = 0;
    const char *lastErrorStr = NULL;
    AsyncUserDataT data = {0};
    data.lastError = (char *)"Not normal name. inv object name:@, the first character is:@.";
    // 同步创建kv表 和 异步创建kv表
    char labelName_err01[MAX_NAME_LENGTH] = "$";
    char labelName_err02[MAX_NAME_LENGTH] = "@";
    ret = GmcKvCreateTable(stmt, labelName_err01, g_configJson);
    ASSERT_EQ(GMERR_INVALID_NAME, ret);

    ret = GmcKvCreateTableAsync(g_stmt_async, labelName_err02, g_configJson, create_kv_table_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_INVALID_NAME, data.status);

    int32_t i = 0;
    while (++i < 10000) {
        lastErrorStr = GmcGetLastError();
        EXPECT_STREQ(
            lastErrorStr, "Not normal name. inv object name:$, the first character is:$.");
    }
}
/* ****************************************************************************
 Description  : 多线程多连接正常退出, 有错误线程输出错误信息
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_008)
{
    int32_t ret = 0;
    GmcKvDropTable(stmt, kv_table_name_sync);
    ret = GmcKvCreateTable(stmt, kv_table_name_sync, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    pthread_t kvTabel[CONN_THREAD_100];
    int index[CONN_THREAD_100];
    for (int i = 0; i < CONN_THREAD_100; i++) {
        index[i] = i;
        ret = pthread_create(&kvTabel[i], NULL, thread_create_label, &index[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < CONN_THREAD_100; i++) {
        pthread_join(kvTabel[i], NULL);
    }
}
/* ****************************************************************************
 Description  : 同步/异步 异常创建 kv 表有错误信息时, 调用获取错误信息的接口正常
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_009)
{
    int32_t ret = 0;
    const char *lastErrorStr = NULL;
    AsyncUserDataT data = {0};
    // 同步创建kv表 和 异步创建kv表
    char labelName_err01[MAX_NAME_LENGTH] = "peiyan";
    ret = GmcKvCreateTable(NULL, labelName_err01, g_configJson);
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = GmcKvCreateTableAsync(NULL, labelName_err01, g_configJson, create_kv_table_callback, &data);
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  : 同步/异步 异常创建 kv 表有错误信息时, 调用获取错误信息的接口正常 表名长度不合法
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_010)
{
    int32_t ret = 0;
    const char *lastErrorStr = NULL;
    AsyncUserDataT data = {0};
    // 同步创建kv表 和 异步创建kv表
    char labelName_err01[MAX_NAME_LENGTH] = "";
    char labelName_err02[MAX_NAME_LENGTH] = "";
    ret = GmcKvCreateTable(stmt, labelName_err01, g_configJson);
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = GmcKvCreateTableAsync(g_stmt_async, labelName_err02, g_configJson, create_kv_table_callback, &data);
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    lastErrorStr = GmcGetLastError();
    lastErrorStr = GmcGetLastError();
    EXPECT_STREQ(lastErrorStr, "Null value is not allowed. kvTableName");
}
/* ****************************************************************************
 Description  : 同步/异步 异常创建 kv 表有错误信息时, 调用获取错误信息的接口正常 表名长度小于等于128B
                拓展用例 DML_047_082 用例, 1M表名
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_011)
{
    int32_t ret = 0;
    const char *lastErrorStr = NULL;
    AsyncUserDataT data = {0};
    // 同步创建kv表 和 异步创建kv表
    char *long_kv_table = (char *)malloc(sizeof(char) * (KV_TABLE_NAME_MAX_LENTH + 1));
    (void)memset(long_kv_table, 0, sizeof(char) * (KV_TABLE_NAME_MAX_LENTH + 1));
    (void)memset(long_kv_table, 'G', sizeof(char) * (KV_TABLE_NAME_MAX_LENTH));
    ret = GmcKvCreateTable(stmt, long_kv_table, g_configJson);
    ASSERT_EQ(GMERR_INVALID_VALUE, ret);

    ret = GmcKvCreateTableAsync(g_stmt_async, long_kv_table, g_configJson, create_kv_table_callback, &data);
    ASSERT_EQ(GMERR_INVALID_VALUE, ret);
    ret = testGmcGetLastError("Not normal value. kvTableName length exceeds the limit 512.");
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetLastError("Not normal value. kvTableName length exceeds the limit 512.");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, long_kv_table);
    ASSERT_EQ(GMERR_INVALID_VALUE, ret);
    ret = testGmcGetLastError("Not normal value. kvTableName length exceeds the limit 512.");
    EXPECT_EQ(GMERR_OK, ret);
    free(long_kv_table);
    long_kv_table = NULL;
}
/* ****************************************************************************
 Description  : 同步/异步 异常创建 kv 表有错误信息时, 调用获取错误信息的接口正常 表名长度大于 600B,
测试错误信息的最大长度512B 此用例测试buff size 最大值是512B长, 由于表名没有限制, 建表等会成功返回,
错误码和日志断言调整(未删除以前断言)
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_012)
{
    int32_t ret = 0;
    const char *lastErrorStr = NULL;
    AsyncUserDataT data = {0};
    // 同步创建kv表 和 异步创建kv表
    char *long_kv_table = (char *)malloc(sizeof(char) * 600);
    (void)memset(long_kv_table, 0, sizeof(char) * 600);
    (void)memset(long_kv_table, 'G', sizeof(char) * 599);
    ret = GmcKvCreateTable(stmt, long_kv_table, g_configJson);
    ASSERT_EQ(GMERR_INVALID_VALUE, ret);
    ret = GmcKvCreateTableAsync(g_stmt_async, long_kv_table, g_configJson, create_kv_table_callback, &data);
    ASSERT_EQ(GMERR_INVALID_VALUE, ret);
    lastErrorStr = GmcGetLastError();
    ASSERT_EQ(59, strlen(lastErrorStr));
    EXPECT_STREQ(lastErrorStr, "Not normal value. kvTableName length exceeds the limit 512.");
    lastErrorStr = GmcGetLastError();
    ASSERT_EQ(59, strlen(lastErrorStr));
    EXPECT_STREQ(lastErrorStr, "Not normal value. kvTableName length exceeds the limit 512.");
    ret = GmcKvDropTable(stmt, long_kv_table);
    ASSERT_EQ(GMERR_INVALID_VALUE, ret);
    ret = testGmcGetLastError("Not normal value. kvTableName length exceeds the limit 512.");
    EXPECT_EQ(GMERR_OK, ret);

    free(long_kv_table);
    long_kv_table = NULL;
}
/* ****************************************************************************
 Description  : 同步/异步 异常创建 kv 表有错误信息时, 调用获取错误信息的接口正常 入参configJson 错误入参
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_013)
{
    int32_t ret = 0;
    const char *lastErrorStr = NULL;
    AsyncUserDataT data = {0};
    // 同步创建kv表 和 异步创建kv表
    char *long_kv_table = (char *)malloc(sizeof(char) * 600);
    (void)memset(long_kv_table, 0, sizeof(char) * 600);
    (void)memset(long_kv_table, 'G', sizeof(char) * 127);
    char *long_kv_table02 = (char *)malloc(sizeof(char) * 600);
    (void)memset(long_kv_table02, 0, sizeof(char) * 600);
    (void)memset(long_kv_table02, 'P', sizeof(char) * 127);
    char configJson_err[128] = "{\"max_record_count_abc\":1000}";
    char l_configJson[128] = "{\"max_record_count\" : 0}";
    ret = GmcKvCreateTable(stmt, long_kv_table, configJson_err);
    ASSERT_EQ(GMERR_OK, ret);
    char configJson_err1[128] = "{\"max_record_count\":1000abc}";
    ret = GmcKvCreateTableAsync(g_stmt_async, long_kv_table02, configJson_err1, create_kv_table_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_INVALID_JSON_CONTENT, data.status);

    ret = GmcKvCreateTable(stmt, "peiyan", l_configJson);  // 错误信息输出
    ASSERT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcKvCreateTable(stmt, "peiyan02", NULL);  // 正确的场景
    ASSERT_EQ(GMERR_OK, ret);
    lastErrorStr = GmcGetLastError();  // 再去获取时, 还是错误的信息展现
    EXPECT_STREQ(lastErrorStr, "Not normal property. max_record_count val is zero.");

    ret = GmcKvDropTable(stmt, long_kv_table);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt, long_kv_table02);
    ASSERT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = GmcKvDropTable(stmt, "peiyan02");
    ASSERT_EQ(GMERR_OK, ret);
    free(long_kv_table);
    long_kv_table = NULL;
    free(long_kv_table02);
    long_kv_table02 = NULL;
}

/* ****************************************************************************
 Description  : 同步/异步 异常创建 kv 表有错误信息时, 调用获取错误信息的接口正常 表为同名表
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_014)
{
    int32_t ret = 0;
    const char *lastErrorStr = NULL;
    AsyncUserDataT data = {0};
    // 同步创建kv表 和 异步创建kv表
    char labelName_err01[MAX_NAME_LENGTH];
    uint32_t tableNum = 0;
#ifndef RUN_INDEPENDENT
    ret = TestGetKvTableNum(&tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif

    for (uint32_t i = 0; i < KV_MAX_CREARE_TABLE_COUNT - tableNum; i++) {
        sprintf(labelName_err01, "KvName_%d", i);
        ret = GmcKvCreateTable(stmt, labelName_err01, g_configJson);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcKvCreateTable(stmt, "KvName_1", g_configJson);
    ASSERT_EQ(GMERR_DUPLICATE_TABLE, ret);
    ret = GmcKvCreateTableAsync(g_stmt_async, "KvName_1", g_configJson, create_kv_table_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_DUPLICATE_TABLE, data.status);

    lastErrorStr = GmcGetLastError();
    EXPECT_STREQ(lastErrorStr, "Duplicate table. Label is KvName_1.");
    lastErrorStr = GmcGetLastError();
    EXPECT_STREQ(lastErrorStr, "Duplicate table. Label is KvName_1.");
    for (uint32_t i = 0; i < KV_MAX_CREARE_TABLE_COUNT - tableNum; i++) {
        sprintf(labelName_err01, "KvName_%d", i);
        ret = GmcKvDropTable(stmt, labelName_err01);
        ASSERT_EQ(GMERR_OK, ret);
    }
}
/* ****************************************************************************
 Description  : 同步/异步 异常创建 kv 表有错误信息时, 调用获取错误信息的接口正常 表为 全局KV表
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_015)
{
    int32_t ret = 0;
    const char *lastErrorStr = NULL;
    AsyncUserDataT data = {0};
    // 同步创建kv表 和 异步创建kv表
    char labelName_err01[MAX_NAME_LENGTH];
    uint32_t tableNum = 0;
#ifndef RUN_INDEPENDENT
    ret = TestGetKvTableNum(&tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif

    for (uint32_t i = 0; i < KV_MAX_CREARE_TABLE_COUNT - tableNum; i++) {
        sprintf(labelName_err01, "KvName_%d", i);
        ret = GmcKvCreateTable(stmt, labelName_err01, g_configJson);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcKvCreateTable(stmt, "T_GMDB", g_configJson);
    ASSERT_EQ(GMERR_DUPLICATE_TABLE, ret);
    ret = GmcKvCreateTableAsync(g_stmt_async, "T_GMDB", g_configJson, create_kv_table_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_DUPLICATE_TABLE, data.status);

    lastErrorStr = GmcGetLastError();
    EXPECT_STREQ(lastErrorStr, "Duplicate table. Label is T_GMDB.");
    lastErrorStr = GmcGetLastError();
    EXPECT_STREQ(lastErrorStr, "Duplicate table. Label is T_GMDB.");
    for (uint32_t i = 0; i < KV_MAX_CREARE_TABLE_COUNT - tableNum; i++) {
        sprintf(labelName_err01, "KvName_%d", i);
        ret = GmcKvDropTable(stmt, labelName_err01);
        ASSERT_EQ(GMERR_OK, ret);
    }
}
/* ****************************************************************************
 Description  : 同步/异步 异常创建 kv 表有错误信息时, 表名含有特殊字符
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_016)
{
    int32_t ret = 0;
    const char *lastErrorStr = NULL;
    AsyncUserDataT data = {0};
    // 同步创建kv表 和 异步创建kv表
    char labelName_err01[MAX_NAME_LENGTH] = "V$+peiyan";
    char labelName_err02[MAX_NAME_LENGTH] = "haha@&#peiyan";
    ret = GmcKvCreateTable(stmt, labelName_err01, g_configJson);
    ASSERT_EQ(GMERR_INVALID_NAME, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcKvCreateTableAsync(g_stmt_async, labelName_err02, g_configJson, create_kv_table_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_INVALID_NAME, data.status);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    lastErrorStr = GmcGetLastError();
    EXPECT_STREQ(lastErrorStr, "Not normal name. The object name V$+peiyan contains special character $.");
}
/* ****************************************************************************
 Description  : 同步/异步 异常创建 kv 表有错误信息时, 调用获取错误信息的接口正常 表个数超过上限
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_017)
{
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_PROGRAM_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int32_t ret = 0;
    const char *lastErrorStr = NULL;
    AsyncUserDataT data = {0};
    // 同步创建kv表 和 异步创建kv表
    char labelName_err01[MAX_NAME_LENGTH];
    uint32_t tableNum = 0;
#ifndef RUN_INDEPENDENT
    ret = TestGetKvTableNum(&tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif

    for (uint32_t i = 0; i < KV_MAX_CREARE_TABLE_COUNT - tableNum; i++) {
        sprintf(labelName_err01, "KvName_%d", i);
        ret = GmcKvCreateTable(stmt, labelName_err01, g_configJson);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcKvCreateTable(stmt, "peiyan", g_configJson);
    ASSERT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
    ret = GmcKvCreateTableAsync(g_stmt_async, "peiyan02", g_configJson, create_kv_table_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, data.status);

    lastErrorStr = GmcGetLastError();
    EXPECT_STREQ(lastErrorStr, "Program limit exceeded. save KV label, name:peiyan");
    lastErrorStr = GmcGetLastError();
    EXPECT_STREQ(lastErrorStr, "Program limit exceeded. save KV label, name:peiyan");
    for (uint32_t i = 0; i < KV_MAX_CREARE_TABLE_COUNT - tableNum; i++) {
        sprintf(labelName_err01, "KvName_%d", i);
        ret = GmcKvDropTable(stmt, labelName_err01);
        ASSERT_EQ(GMERR_OK, ret);
    }
}
/* ****************************************************************************
 Description  : 同步/异步 异常创建 kv 表有错误信息时, 调用获取错误信息的接口正常 创建同名的 KV 表的 vertexlable
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_018)
{
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_UNDEFINED_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int32_t ret = 0;
    const char *lastErrorStr = NULL;
    AsyncUserDataT data = {0};
    // 同步创建kv表 和 异步创建kv表
    ret = GmcKvCreateTable(stmt, label_name01, g_configJson);
    ASSERT_EQ(GMERR_DUPLICATE_TABLE, ret);
    ret = GmcKvCreateTableAsync(g_stmt_async, label_name02, g_configJson, create_kv_table_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_DUPLICATE_TABLE, data.status);

    lastErrorStr = GmcGetLastError();
    EXPECT_STREQ(lastErrorStr, "Duplicate table. Label is OP_T0.");
    lastErrorStr = GmcGetLastError();
    EXPECT_STREQ(lastErrorStr, "Duplicate table. Label is OP_T0.");
    ret = GmcKvDropTable(stmt, label_name01);
    ASSERT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = GmcKvDropTable(stmt, label_name02);
    ASSERT_EQ(GMERR_UNDEFINED_TABLE, ret);
}
/* ****************************************************************************
 Description  : 同步/异步 异常创建 kv 表有错误信息时, 调用获取错误信息的接口正常 创建同名的 KV 表的 namespace
迭代三增加用例
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_019)
{
    int32_t ret = 0;
    const char *lastErrorStr = NULL;
    AsyncUserDataT data = {0};
    // 后续迭代合入后增加用例
}
/* ****************************************************************************
 Description  : 同步/异步 异常创建 kv 表有错误信息时, 调用获取错误信息的接口正常 批量ddl创建KV表/删除KV表
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_020)
{
    int32_t ret = 0;
    const char *lastErrorStr = NULL;
    AsyncUserDataT data = {0};
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    // 同步批量创建 kv 表数据
    ret = GmcBatchPrepare(conn, NULL, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    // 批量添加DDL命令
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_KV_TABLE, "edu1", NULL, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_KV_TABLE, "edu2", NULL, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_KV_TABLE, "edu3", NULL, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(totalNum, (uint32_t)3);
    ASSERT_EQ(successNum, (uint32_t)3);

    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn, NULL, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_KV_TABLE, "edu1", NULL, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_KV_TABLE, "edu2", NULL, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_KV_TABLE, "edu3", NULL, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_DUPLICATE_TABLE, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(totalNum, (uint32_t)3);
    ASSERT_EQ(successNum, (uint32_t)0);
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);

    lastErrorStr = GmcGetLastError();
    EXPECT_STREQ(lastErrorStr, "Duplicate table. Label is edu1.");
    char configJson_err1[128] = "{\"max_record_count\":1000abc}";
    ret = GmcBatchPrepare(conn, NULL, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_KV_TABLE, "edu4", NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_KV_TABLE, "edu5", NULL,
        configJson_err1);  // 假如批量中间出错, 之前成功的就成功了
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_KV_TABLE, "edu6", NULL, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_INVALID_JSON_CONTENT, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(totalNum, (uint32_t)3);
    ASSERT_EQ(successNum, (uint32_t)1);
    lastErrorStr = GmcGetLastError();
    EXPECT_STREQ(lastErrorStr, "Incorrect json content. Incorrect kv config json.");
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn, NULL, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_DROP_KV_TABLE, "edu1", NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_DROP_KV_TABLE, "edu2", NULL, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_DROP_KV_TABLE, "edu3", NULL, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_DROP_KV_TABLE, "edu4", NULL, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    lastErrorStr = GmcGetLastError();
    EXPECT_STREQ(lastErrorStr, "Incorrect json content. Incorrect kv config json.");
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 同步/异步 异常创建 kv 表有错误信息时, 调用获取错误信息的接口正常 conn级别, 多stmt操作
                不论时哪个stmt, 最终都是最后一次报错的信息, 跟stmt没关系
                批量接口修改后批量挂在conn级别, 因此用例调整为多个batch, 跟stmt没关系
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_021)
{
    int32_t ret = 0;
    const char *lastErrorStr = NULL;
    AsyncUserDataT data = {0};
    GmcBatchRetT batchRet;
    // 同步批量创建 kv 表数据
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batchRule = NULL;
    ret = GmcBatchPrepare(conn, &batchOption, &batchRule);
    EXPECT_EQ(GMERR_OK, ret);
    // 批量添加DDL命令
    ret = GmcBatchAddDDL(batchRule, GMC_OPERATION_CREATE_KV_TABLE, "edu1", NULL, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDDL(batchRule, GMC_OPERATION_CREATE_KV_TABLE, "edu2", NULL, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDDL(batchRule, GMC_OPERATION_CREATE_KV_TABLE, "edu3", NULL, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    ret = GmcBatchExecute(batchRule, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(totalNum, (uint32_t)3);
    ASSERT_EQ(successNum, (uint32_t)3);

    ret = GmcBatchPrepare(conn, &batchOption, &batchRule);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDDL(batchRule, GMC_OPERATION_CREATE_KV_TABLE, "edu1", NULL, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDDL(batchRule, GMC_OPERATION_CREATE_KV_TABLE, "edu2", NULL, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDDL(batchRule, GMC_OPERATION_CREATE_KV_TABLE, "edu3", NULL, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchExecute(batchRule, &batchRet);
    ASSERT_EQ(GMERR_DUPLICATE_TABLE, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(totalNum, (uint32_t)3);
    ASSERT_EQ(successNum, (uint32_t)0);
    lastErrorStr = GmcGetLastError();
    EXPECT_STREQ(lastErrorStr, "Duplicate table. Label is edu1.");
    char configJson_err1[128] = "{\"max_record_count\":1000abc}";
    GmcBatchT *batchSIp = NULL;

    ret = GmcBatchPrepare(conn, &batchOption, &batchSIp);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBatchAddDDL(batchSIp, GMC_OPERATION_CREATE_KV_TABLE, "edu4", NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDDL(batchSIp, GMC_OPERATION_CREATE_KV_TABLE, "edu5", NULL,
        configJson_err1);  // 假如批量中间出错, 之前成功的就成功了
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDDL(batchSIp, GMC_OPERATION_CREATE_KV_TABLE, "edu6", NULL, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchExecute(batchSIp, &batchRet);
    ASSERT_EQ(GMERR_INVALID_JSON_CONTENT, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);

    ret = testGmcGetLastError("Incorrect json content. Incorrect kv config json.");
    EXPECT_EQ(GMERR_OK, ret);
    ASSERT_EQ(totalNum, (uint32_t)3);
    ASSERT_EQ(successNum, (uint32_t)1);

    GmcBatchT *batchSIpv4 = NULL;
    ret = GmcBatchPrepare(conn, &batchOption, &batchSIpv4);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDDL(batchSIpv4, GMC_OPERATION_DROP_KV_TABLE, "edu1", NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDDL(batchSIpv4, GMC_OPERATION_DROP_KV_TABLE, "edu2", NULL, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDDL(batchSIpv4, GMC_OPERATION_DROP_KV_TABLE, "edu3", NULL, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDDL(batchSIpv4, GMC_OPERATION_DROP_KV_TABLE, "edu4", NULL, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchExecute(batchSIpv4, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    lastErrorStr = GmcGetLastError();
    EXPECT_STREQ(lastErrorStr, "Incorrect json content. Incorrect kv config json.");
    ret = GmcBatchDestroy(batchRule);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDestroy(batchSIp);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDestroy(batchSIpv4);
    EXPECT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 同步/异步 异常创建 kv 表有错误信息时, 调用获取错误信息的接口正常 异步时序问题
                在没去 testWaitAsyncRecv 时, 之前获取错误的信息都是空,得到反馈后, 信息变更
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_023)
{
    int32_t ret = 0;
    const char *lastErrorStr = NULL;
    AsyncUserDataT data = {0};
    data.lastError = (char *)"Duplicate table. Label is peiyan_2.";
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    // 同步批量创建 kv 表数据
    ret = GmcBatchPrepare(conn, NULL, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    // 批量添加DDL命令
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_KV_TABLE, kv_table_name_01, NULL, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_KV_TABLE, kv_table_name_02, NULL, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_KV_TABLE, kv_table_name_03, NULL, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(totalNum, (uint32_t)3);
    ASSERT_EQ(successNum, (uint32_t)3);

    ret =
        GmcKvCreateTableAsync(g_stmt_async, kv_table_name_02, test_delta_config_json, create_kv_table_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_DUPLICATE_TABLE, data.status);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBatchReset(batch);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcBatchAddDDL(batch, GMC_OPERATION_DROP_KV_TABLE, kv_table_name_01, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_DROP_KV_TABLE, kv_table_name_02, NULL, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_DROP_KV_TABLE, kv_table_name_03, NULL, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 同步/异步 异常创建 kv 表有错误信息时, 调用获取错误信息的接口正常 异步时序问题
                获取的是最后一条错误信息
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_024)
{
    int32_t ret = 0;
    const char *lastErrorStr = NULL;
    AsyncUserDataT data = {0};

    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    // 同步批量创建 kv 表数据
    ret = GmcBatchPrepare(conn, NULL, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    // 批量添加DDL命令
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_KV_TABLE, kv_table_name_01, NULL, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_KV_TABLE, kv_table_name_03, NULL, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(totalNum, (uint32_t)2);
    ASSERT_EQ(successNum, (uint32_t)2);

    char l_configJson[128] = "{\"max_record_count\" : 0}";
    ret =
        GmcKvCreateTableAsync(g_stmt_async, kv_table_name_02, test_delta_config_json, create_kv_table_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(0, data.status);
    data.lastError = (char *)"Not normal property. max_record_count val is zero.";
    ret = GmcKvCreateTableAsync(g_stmt_async, kv_table_name_04, l_configJson, create_kv_table_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_INVALID_PROPERTY, data.status);

    ret = GmcBatchReset(batch);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_DROP_KV_TABLE, kv_table_name_01, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_DROP_KV_TABLE, kv_table_name_02, NULL, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_DROP_KV_TABLE, kv_table_name_03, NULL, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 同步/异步 异常创建 kv 表有错误信息时, 调用获取错误信息的接口正常
                获取的是最后一条错误信息
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_025)
{
    int32_t ret = 0;
    const char *lastErrorStr = NULL;
    AsyncUserDataT data = {0};
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    // 同步批量创建 kv 表数据
    ret = GmcBatchPrepare(conn, NULL, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    // 批量添加DDL命令
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_KV_TABLE, kv_table_name_01, NULL, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_KV_TABLE, kv_table_name_02, NULL, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_KV_TABLE, kv_table_name_03, NULL, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(totalNum, (uint32_t)3);
    ASSERT_EQ(successNum, (uint32_t)3);

    char l_configJson[128] = "{\"max_record_count\" : 0}";
    ret =
        GmcKvCreateTableAsync(g_stmt_async, kv_table_name_02, test_delta_config_json, create_kv_table_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_DUPLICATE_TABLE, data.status);

    ret = GmcKvCreateTable(stmt, kv_table_name_03, g_configJson);
    ASSERT_EQ(GMERR_DUPLICATE_TABLE, ret);
    ret = GmcKvCreateTable(stmt, "", g_configJson);
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    lastErrorStr = GmcGetLastError();
    EXPECT_STREQ(lastErrorStr, "Null value is not allowed. kvTableName");

    data.lastError = (char *)"Not normal property. max_record_count val is zero.";
    ret = GmcKvCreateTableAsync(g_stmt_async, kv_table_name_04, l_configJson, create_kv_table_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_INVALID_PROPERTY, data.status);

    ret = GmcBatchReset(batch);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcBatchAddDDL(batch, GMC_OPERATION_DROP_KV_TABLE, kv_table_name_01, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_DROP_KV_TABLE, kv_table_name_02, NULL, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_DROP_KV_TABLE, kv_table_name_03, NULL, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    lastErrorStr = GmcGetLastError();
    EXPECT_STREQ(lastErrorStr, "Null value is not allowed. kvTableName");
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);
}

void *kv_operater_set_update_rewrite_thread(void *arg)
{
    TEST_INFO("Thread opera thread is start. \n");
    int ret = 0;
    int i = *(int *)arg;
    void *kvtable = NULL;
    char output[128] = {0};
    uint32_t outputLen = sizeof(output);
    const char *lastErrorStr = NULL;
    // 获取kv
    ret = GmcKvPrepareStmtByLabelName(stmt, "peiyan_5");
    EXPECT_EQ(GMERR_OK, ret);
    GmcKvTupleT kvInfo = {0};
    int32_t value = 100;
    char key[] = "zhangsan";
    // 设置k-v值
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key);
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(int32_t);
    ret = GmcKvSet(stmt, key, strlen(key), &value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    // 设置kv值
    GmcKvTupleT kvInfo1 = {0};
    int8_t value1 = 10;
    char key1[] = "wangwu";
    // 设置k-v值
    kvInfo1.key = key1;
    kvInfo1.keyLen = strlen(key1);
    kvInfo1.value = &value1;
    kvInfo1.valueLen = sizeof(int8_t);
    ret = GmcKvSet(stmt, key1, strlen(key1), &value1, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("Set kv is success. \n");

    int32_t value2 = 66;
    kvInfo.value = &value2;
    ret = GmcKvSet(stmt, key1, strlen(key1), &value2, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("Update kv is success. \n");
    // 查询插入的结果
    uint32_t count = 0;
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(2, count);
    ret = GmcKvGet(stmt, key1, strlen(key1), output, &outputLen);
    EXPECT_EQ(GMERR_OK, ret);
    // zhangsan对应的value值变为66 不是100了
    EXPECT_EQ(66, *(uint32_t *)output);
    EXPECT_EQ(4, outputLen);
    // 再次插入相同的数据
    ret = GmcKvSet(stmt, key1, strlen(key1), &value2, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(2, count);
    TEST_INFO("Rewrite kv is success. \n");

    bool key_bool = 0;
    // 设置k-v值 key为 bool 类型
    kvInfo1.key = &key_bool;
    kvInfo1.keyLen = sizeof(bool);
    kvInfo1.value = &value1;
    kvInfo1.valueLen = sizeof(int8_t);
    ret = GmcKvSet(stmt, &key_bool, sizeof(bool), &value1, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("Set kv is success. \n");
    if (ret != GMERR_OK) {
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }

    TEST_INFO("Thread opera thread is end. \n");
    return NULL;
}

/* ****************************************************************************
 Description  : 同步/异步 异常创建 kv 表有错误信息, 交互kv线程DML操作, 以下用例基本都是交互用例了
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_026)
{
    int32_t ret = 0;

    // 同步创建kv表 和 异步创建kv表
    char labelNameKv[MAX_NAME_LENGTH];
    uint32_t tableNum = 1;
#ifndef RUN_INDEPENDENT
    ret = TestGetKvTableNum(&tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
    for (uint32_t i = 0; i < KV_MAX_CREARE_TABLE_COUNT - tableNum; i++) {
        sprintf(labelNameKv, "peiyan_%d", i);
        ret = GmcKvCreateTable(stmt, labelNameKv, g_configJson);
        ASSERT_EQ(GMERR_OK, ret);
    }
    pthread_t kvTabelOper;
    int index = 0;
    void *thr_ret = 0;
    ret = pthread_create(&kvTabelOper, NULL, kv_operater_set_update_rewrite_thread, &index);
    ASSERT_EQ(GMERR_OK, ret);

    pthread_join(kvTabelOper, &thr_ret);

    for (uint32_t i = 0; i < KV_MAX_CREARE_TABLE_COUNT - tableNum; i++) {
        sprintf(labelNameKv, "peiyan_%d", i);
        ret = GmcKvDropTable(stmt, labelNameKv);
        ASSERT_EQ(GMERR_OK, ret);
    }
}

void *kv_operater_get_update_rewrite_thread(void *arg)
{
    GmcConnT *g_conn_sync = NULL;
    TEST_INFO("Thread opera thread is start. \n");
    int ret = 0;
    int i = *(int *)arg;
    void *kvtable = NULL;
    char output[128] = {0};
    uint32_t outputLen = sizeof(output);
    const char *lastErrorStr = NULL;
    // 获取kv
    ret = GmcKvPrepareStmtByLabelName(stmt, "peiyan_5");
    EXPECT_EQ(GMERR_OK, ret);
    GmcKvTupleT kvInfo1 = {0};
    char value[] = "value_kvInfo1";

    bool key_bool = false;
    // 设置k-v值 key为 bool 类型
    kvInfo1.key = &key_bool;
    kvInfo1.keyLen = sizeof(bool);
    kvInfo1.value = value;
    kvInfo1.valueLen = sizeof(value);
    ret = GmcKvSet(stmt, &key_bool, sizeof(bool), value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    key_bool = true;
    ret = GmcKvSet(stmt, &key_bool, sizeof(bool), value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("Set kv is success. \n");

    // 查询插入的结果
    uint32_t count = 0;
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(2, count);
    ret = GmcKvGet(stmt, &key_bool, sizeof(key_bool), output, &outputLen);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_STREQ(value, output);
    EXPECT_EQ(sizeof(value), outputLen);

    if (ret != GMERR_OK) {
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }

    TEST_INFO("Thread opera thread is end. \n");
    return NULL;
}

/* ****************************************************************************
 Description  : 同步/异步 异常创建 kv 表有错误信息, 交互kv线程DML操作, bool值做主键
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_027)
{
    int32_t ret = 0;
    // 同步创建kv表 和 异步创建kv表
    char labelNameKv[MAX_NAME_LENGTH];
    uint32_t tableNum = 0;
#ifndef RUN_INDEPENDENT
    ret = TestGetKvTableNum(&tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
    for (uint32_t i = 0; i < KV_MAX_CREARE_TABLE_COUNT - tableNum; i++) {
        sprintf(labelNameKv, "peiyan_%d", i);
        ret = GmcKvCreateTable(stmt, labelNameKv, g_configJson);
        ASSERT_EQ(GMERR_OK, ret);
    }
    pthread_t kvTabelOper;
    int index = 0;
    void *thr_ret = 0;
    ret = pthread_create(&kvTabelOper, NULL, kv_operater_get_update_rewrite_thread, &index);
    ASSERT_EQ(GMERR_OK, ret);

    pthread_join(kvTabelOper, &thr_ret);

    for (uint32_t i = 0; i < KV_MAX_CREARE_TABLE_COUNT - tableNum; i++) {
        sprintf(labelNameKv, "peiyan_%d", i);
        ret = GmcKvDropTable(stmt, labelNameKv);
        ASSERT_EQ(GMERR_OK, ret);
    }
}

void *kv_operater_max_len_key_thread(void *arg)
{
    TEST_INFO("Thread opera thread is start. \n");
    int ret = 0;
    int i = *(int *)arg;
    void *kvtable = NULL;
    uint32_t outputLen = sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH_1M);
    const char *lastErrorStr = NULL;
    // 获取kv
    ret = GmcKvPrepareStmtByLabelName(stmt, "peiyan_5");
    EXPECT_EQ(GMERR_OK, ret);
    GmcKvTupleT kvInfo1 = {0};
    char *long_kv_key = (char *)malloc(sizeof(char) * (KV_TABLE_KEY_MAX_LENTH));
    (void)memset(long_kv_key, 0, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH));
    (void)memset(long_kv_key, 'G', sizeof(char) * (KV_TABLE_KEY_MAX_LENTH - 1));

    char *long_kv_value = (char *)malloc(sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH));
    (void)memset(long_kv_value, 0, sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH));
    (void)memset(long_kv_value, 'a', sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH - 1));

    char *output = (char *)malloc(sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH_1M));
    (void)memset(output, 0, sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH_1M));

    char *long_kv_key_err = (char *)malloc(sizeof(char) * (KV_TABLE_KEY_MAX_LENTH + 1));
    (void)memset(long_kv_key_err, 0, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH + 1));
    (void)memset(long_kv_key_err, 'H', sizeof(char) * (KV_TABLE_KEY_MAX_LENTH));

    char *long_kv_value_err = (char *)malloc(sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH_1M + 1));
    (void)memset(long_kv_value_err, 0, sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH_1M + 1));
    (void)memset(long_kv_value_err, 'a', sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH_1M));

    // 设置k-v最大值
    kvInfo1.key = long_kv_key;
    kvInfo1.keyLen = strlen(long_kv_key);
    kvInfo1.value = long_kv_value;
    kvInfo1.valueLen = strlen(long_kv_value);
    ret = GmcKvSet(stmt, long_kv_key, strlen(long_kv_key) + 1, long_kv_value, strlen(long_kv_value) + 1);
    EXPECT_EQ(GMERR_OK, ret);

    kvInfo1.value = long_kv_value_err;
    kvInfo1.valueLen = strlen(long_kv_value_err);
    ret = GmcKvSet(stmt, long_kv_key, strlen(long_kv_key) + 1, long_kv_value_err, strlen(long_kv_value_err) + 1);
    EXPECT_EQ(GMERR_INVALID_VALUE, ret);
    ret = testGmcGetLastError("Not normal value. The val len should be larger than 0 and smaller than 1048577");
    EXPECT_EQ(GMERR_OK, ret);

    kvInfo1.key = long_kv_key_err;
    kvInfo1.keyLen = strlen(long_kv_key_err);
    kvInfo1.value = long_kv_value;
    kvInfo1.valueLen = strlen(long_kv_value);
    ret = GmcKvSet(stmt, long_kv_key_err, strlen(long_kv_key_err) + 1, long_kv_value, strlen(long_kv_value) + 1);
    EXPECT_EQ(GMERR_INVALID_VALUE, ret);
    ret = testGmcGetLastError("Not normal value. The key len should be larger than 0 and small or equal to 512");
    EXPECT_EQ(GMERR_OK, ret);

    TEST_INFO("Set kv is success. \n");

    // 查询插入的结果
    uint32_t count = 0;
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, count);
    ret = GmcKvGet(stmt, long_kv_key, strlen(long_kv_key) + 1, output, &outputLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = strncmp(long_kv_value, output, outputLen);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(strlen(long_kv_value) + 1, outputLen);

    ret = testGmcGetLastError("Not normal value. The key len should be larger than 0 and small or equal to 512");
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t valueSize = 0;
    ret = GmcKvGetValueSize(stmt, long_kv_key, strlen(long_kv_key) + 1, &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(strlen(long_kv_value) + 1, valueSize);
    free(long_kv_key);
    free(long_kv_value);
    free(output);
    free(long_kv_key_err);
    free(long_kv_value_err);
    TEST_INFO("Thread opera thread is end. \n");
    return NULL;
}

/* ****************************************************************************
 Description  : 同步/异步 异常创建 kv 表有错误信息, 交互kv线程DML操作, key value 交互最大长度
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_028)
{
    int32_t ret = 0;
    // 同步创建kv表 和 异步创建kv表
    char labelNameKv[MAX_NAME_LENGTH];
    uint32_t tableNum = 0;
#ifndef RUN_INDEPENDENT
    ret = TestGetKvTableNum(&tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
    for (uint32_t i = 0; i < KV_MAX_CREARE_TABLE_COUNT - tableNum; i++) {
        sprintf(labelNameKv, "peiyan_%d", i);
        ret = GmcKvCreateTable(stmt, labelNameKv, g_configJson);
        ASSERT_EQ(GMERR_OK, ret);
    }
    pthread_t kvTabelOper;
    int index = 0;
    void *thr_ret = 0;
    ret = pthread_create(&kvTabelOper, NULL, kv_operater_max_len_key_thread, &index);
    ASSERT_EQ(GMERR_OK, ret);

    pthread_join(kvTabelOper, &thr_ret);

    for (uint32_t i = 0; i < KV_MAX_CREARE_TABLE_COUNT - tableNum; i++) {
        sprintf(labelNameKv, "peiyan_%d", i);
        ret = GmcKvDropTable(stmt, labelNameKv);
        ASSERT_EQ(GMERR_OK, ret);
    }
}

void *kv_operater_drop_exist_key_thread(void *arg)
{
    TEST_INFO("Thread opera thread is start. \n");
    int ret = 0;
    int i = *(int *)arg;
    void *kvtable = NULL;
    uint32_t outputLen = sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH);
    const char *lastErrorStr = NULL;
    const int key_count = 26;
    // 获取kv
    ret = GmcKvPrepareStmtByLabelName(stmt, "peiyan_5");
    EXPECT_EQ(GMERR_OK, ret);
    GmcKvTupleT kvInfo1 = {0};

    char *output = (char *)malloc(sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH));
    (void)memset(output, 0, sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH));

    char *long_kv_value = (char *)malloc(sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH));
    (void)memset(long_kv_value, 0, sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH));
    (void)memset(long_kv_value, 'a', sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH - 1));

    char **key_name = (char **)malloc(sizeof(char *) * key_count);
    for (int count = 0; count < key_count; count++) {
        key_name[count] = (char *)malloc(sizeof(char) * KV_TABLE_KEY_MAX_LENTH);
        (void)memset(key_name[count], 0, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH));
        (void)memset(key_name[count], 'A' + count, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH - 1));
        // 设置k-v值 key为 最大长度
        kvInfo1.key = key_name[count];
        kvInfo1.keyLen = strlen(key_name[count]);
        kvInfo1.value = long_kv_value;
        kvInfo1.valueLen = strlen(long_kv_value);
        ret = GmcKvSet(stmt, key_name[count], strlen(key_name[count]), long_kv_value, strlen(long_kv_value));
        EXPECT_EQ(GMERR_OK, ret);
        kvInfo1.key = key_name[count];
        kvInfo1.keyLen = strlen(key_name[count]);
        ret = GmcKvSet(stmt, key_name[count], strlen(key_name[count]), long_kv_value, strlen(long_kv_value));
        EXPECT_EQ(GMERR_OK, ret);
        TEST_INFO("Set kv is success. \n");
    }

    // 查询插入的结果
    uint32_t count = 0;
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(key_count, count);

    ret = GmcKvGet(stmt, key_name[0], strlen(key_name[0]), output, &outputLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = strncmp(long_kv_value, output, outputLen);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(strlen(long_kv_value), outputLen);
    TEST_INFO("Get kv value success: %s \n", output);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // 删除 kv 全部数据
    for (int count = 0; count < key_count; count++) {
        (void)memset(key_name[count], 'A' + count, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH - 1));

        ret = GmcKvRemove(stmt, key_name[count], strlen(key_name[count]));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcKvRemove(stmt, key_name[count], strlen(key_name[count]));
        EXPECT_EQ(GMERR_OK, ret);

        // 检查删除的信息是否存在
        bool isnull;
        ret = GmcKvIsExist(stmt, key_name[count], strlen(key_name[count]), &isnull);
        EXPECT_FALSE(isnull);
        EXPECT_EQ(GMERR_OK, ret);
        TEST_INFO("Del kv one record success. \n");
    }
    for (int count = 0; count < key_count; count++) {
        (void)memset(key_name[count], 'A' + count, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH - 1));
        // 设置k-v值 key为 最大长度
        kvInfo1.key = key_name[count];
        kvInfo1.keyLen = strlen(key_name[count]);
        kvInfo1.value = long_kv_value;
        kvInfo1.valueLen = strlen(long_kv_value);
        ret = GmcKvSet(stmt, key_name[count], strlen(key_name[count]), long_kv_value, strlen(long_kv_value));
        EXPECT_EQ(GMERR_OK, ret);
        kvInfo1.key = key_name[count];
        kvInfo1.keyLen = strlen(key_name[count]);
        ret = GmcKvSet(stmt, key_name[count], strlen(key_name[count]), long_kv_value, strlen(long_kv_value));
        EXPECT_EQ(GMERR_OK, ret);
        TEST_INFO("Set kv is success. \n");
    }
    for (int count = 0; count < key_count; count++) {
        (void)memset(key_name[count], 'a' + count, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH - 1));
        // 设置k-v值 key为 最大长度
        kvInfo1.key = key_name[count];
        kvInfo1.keyLen = strlen(key_name[count]);
        kvInfo1.value = long_kv_value;
        kvInfo1.valueLen = strlen(long_kv_value);
        ret = GmcKvSet(stmt, key_name[count], strlen(key_name[count]), long_kv_value, strlen(long_kv_value));
        EXPECT_EQ(GMERR_OK, ret);
        kvInfo1.key = key_name[count];
        kvInfo1.keyLen = strlen(key_name[count]);
        ret = GmcKvSet(stmt, key_name[count], strlen(key_name[count]), long_kv_value, strlen(long_kv_value));
        EXPECT_EQ(GMERR_OK, ret);
        TEST_INFO("Set kv is success. \n");
    }
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(2 * key_count, count);

    for (int count = 0; count < key_count; count++) {
        free(key_name[count]);
    }
    free(key_name);
    free(long_kv_value);
    free(output);
    TEST_INFO("Thread opera thread is end. \n");
    return NULL;
}
/* ****************************************************************************
 Description  : 同步/异步 异常创建 kv 表有错误信息, 交互kv线程DML操作, key value 交互最大长度
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_029)
{
    int32_t ret = 0;
    // 同步创建kv表 和 异步创建kv表
    char labelNameKv[MAX_NAME_LENGTH];
    uint32_t tableNum = 0;
    ret = TestGetKvTableNum(&tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < KV_MAX_CREARE_TABLE_COUNT - tableNum; i++) {
        sprintf(labelNameKv, "peiyan_%d", i);
        ret = GmcKvCreateTable(stmt, labelNameKv, g_configJson);
        ASSERT_EQ(GMERR_OK, ret);
    }
    pthread_t kvTabelOper;
    int index = 0;
    void *thr_ret = 0;
    ret = pthread_create(&kvTabelOper, NULL, kv_operater_drop_exist_key_thread, &index);
    ASSERT_EQ(GMERR_OK, ret);

    pthread_join(kvTabelOper, &thr_ret);

    for (uint32_t i = 0; i < KV_MAX_CREARE_TABLE_COUNT - tableNum; i++) {
        sprintf(labelNameKv, "peiyan_%d", i);
        ret = GmcKvDropTable(stmt, labelNameKv);
        ASSERT_EQ(GMERR_OK, ret);
    }
}

void *kv_operater_scan_exist_key_thread(void *arg)
{
    TEST_INFO("Thread opera thread is start. \n");
    int ret = 0;
    int i = *(int *)arg;
    void *kvtable = NULL;
    uint32_t outputLen = 0;
    uint32_t fetchKeyLen = 0;
    const char *lastErrorStr = NULL;
    const int key_count = 28;
    bool isFinish = false;
    uint32_t scan_count = 0;

    // 获取kv
    ret = GmcKvPrepareStmtByLabelName(stmt, "peiyan_5");
    EXPECT_EQ(GMERR_OK, ret);

    char *output = NULL;
    char *output_key = NULL;

    char *long_kv_value = (char *)malloc(sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH));
    (void)memset(long_kv_value, 0, sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH));
    (void)memset(long_kv_value, 'a', sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH - 1));
    // 写入数据A-A+28 a-a+28 一共56条数据
    char **key_name = (char **)malloc(sizeof(char *) * key_count);
    for (int count = 0; count < key_count; count++) {
        key_name[count] = (char *)malloc(sizeof(char) * KV_TABLE_KEY_MAX_LENTH);
        (void)memset(key_name[count], 0, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH));
        (void)memset(key_name[count], 'A' + count, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH - 1));
        // 设置k-v值 key为 最大长度
        ret = GmcKvSet(stmt, key_name[count], strlen(key_name[count]), long_kv_value, strlen(long_kv_value));
        EXPECT_EQ(GMERR_OK, ret);
        (void)memset(key_name[count], 'a' + count, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH - 1));
        ret = GmcKvSet(stmt, key_name[count], strlen(key_name[count]), long_kv_value, strlen(long_kv_value));
        EXPECT_EQ(GMERR_OK, ret);
        TEST_INFO("Set kv is success. \n");
    }
    ret = GmcKvScan(stmt, key_count * 2);
    EXPECT_EQ(GMERR_OK, ret);
    while (true) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        ret = GmcKvGetFromStmt(stmt, (void **)&output_key, &fetchKeyLen, (void **)&output, &outputLen);
        EXPECT_EQ(GMERR_OK, ret);
        TEST_INFO("Fetch kv key is success: %s \n", output_key);
        EXPECT_EQ(KV_TABLE_KEY_MAX_LENTH - 1, fetchKeyLen);
        ret = strncmp(long_kv_value, output, outputLen);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(strlen(long_kv_value), outputLen);
        scan_count++;
    }
    EXPECT_EQ(scan_count, key_count * 2);
    // 删除数据A-A+28 28条数据
    for (int count = 0; count < key_count; count++) {
        (void)memset(key_name[count], 'A' + count, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH - 1));

        ret = GmcKvRemove(stmt, key_name[count], strlen(key_name[count]));
        EXPECT_EQ(GMERR_OK, ret);

        // 检查删除的信息是否存在
        bool isnull;
        ret = GmcKvIsExist(stmt, key_name[count], strlen(key_name[count]), &isnull);
        EXPECT_FALSE(isnull);
        EXPECT_EQ(GMERR_OK, ret);
        TEST_INFO("Del kv one record success. \n");
    }
    // 再次扫描为 28 条剩余数据
    scan_count = 0;
    ret = GmcKvScan(stmt, key_count * 2);
    EXPECT_EQ(GMERR_OK, ret);
    while (true) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        ret = GmcKvGetFromStmt(stmt, (void **)&output_key, &fetchKeyLen, (void **)&output, &outputLen);
        EXPECT_EQ(GMERR_OK, ret);
        TEST_INFO("Fetch kv key is success: %s \n", output_key);
        EXPECT_EQ(KV_TABLE_KEY_MAX_LENTH - 1, fetchKeyLen);
        ret = strncmp(long_kv_value, output, KV_TABLE_VALUE_MAX_LENTH - 1);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(strlen(long_kv_value), outputLen);
        scan_count++;
    }
    EXPECT_EQ(scan_count, key_count);

    for (int count = 0; count < key_count; count++) {
        free(key_name[count]);
    }
    free(key_name);
    free(long_kv_value);
    TEST_INFO("Thread opera thread is end. \n");
    return NULL;
}
/* ****************************************************************************
 Description  : 同步/异步 异常创建 kv 表有错误信息, 交互kv线程DML操作, key value 交互最大长度扫描, key的取值没限制
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_030)
{
    int32_t ret = 0;
    // 同步创建kv表 和 异步创建kv表
    char labelNameKv[MAX_NAME_LENGTH];
    uint32_t tableNum = 0;
#ifndef RUN_INDEPENDENT
    ret = TestGetKvTableNum(&tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
    for (uint32_t i = 0; i < KV_MAX_CREARE_TABLE_COUNT - tableNum; i++) {
        sprintf(labelNameKv, "peiyan_%d", i);
        ret = GmcKvCreateTable(stmt, labelNameKv, g_configJson);
        ASSERT_EQ(GMERR_OK, ret);
    }
    pthread_t kvTabelOper;
    int index = 0;
    void *thr_ret = 0;
    ret = pthread_create(&kvTabelOper, NULL, kv_operater_scan_exist_key_thread, &index);
    ASSERT_EQ(GMERR_OK, ret);

    pthread_join(kvTabelOper, &thr_ret);

    for (uint32_t i = 0; i < KV_MAX_CREARE_TABLE_COUNT - tableNum; i++) {
        sprintf(labelNameKv, "peiyan_%d", i);
        ret = GmcKvDropTable(stmt, labelNameKv);
        ASSERT_EQ(GMERR_OK, ret);
    }
}

void *kv_operater_update_key_thread(void *arg)
{
    TEST_INFO("Thread update opera thread is start. \n");
    int ret = 0;
    int i = *(int *)arg;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *kvtable = NULL;
    const int key_count = 27;
    const char *lastErrorStr = NULL;

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 获取kv
    ret = GmcKvPrepareStmtByLabelName(stmt, "peiyan_5");
    EXPECT_EQ(GMERR_OK, ret);

    GmcKvTupleT kvInfo1 = {0};

    char *long_kv_value = (char *)malloc(sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH));
    (void)memset(long_kv_value, 0, sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH));
    (void)memset(long_kv_value, 'a', sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH - 1));

    char **key_name = (char **)malloc(sizeof(char *) * key_count);
    for (int count = 0; count < key_count; count++) {
        key_name[count] = (char *)malloc(sizeof(char) * KV_TABLE_KEY_MAX_LENTH);
        (void)memset(key_name[count], 0, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH));
        (void)memset(key_name[count], 'A' + count, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH - 1));
        // 设置k-v值 key为 最大长度
        kvInfo1.key = key_name[count];
        kvInfo1.keyLen = strlen(key_name[count]);
        kvInfo1.value = long_kv_value;
        kvInfo1.valueLen = strlen(long_kv_value);
        ret = GmcKvSet(stmt, key_name[count], strlen(key_name[count]), long_kv_value, strlen(long_kv_value));
        EXPECT_EQ(GMERR_OK, ret);
        TEST_INFO("Set kv is success. \n");
    }
    // 查询插入的结果
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    for (int count = 0; count < key_count; count++) {
        free(key_name[count]);
    }
    free(key_name);
    free(long_kv_value);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("Thread update opera thread is end. \n");
    return NULL;
}
void *kv_operater_read_key_thread(void *arg)
{
    TEST_INFO("Thread read opera thread is start. \n");
    int ret = 0;
    int i = *(int *)arg;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *kvtable = NULL;
    const int key_count = 26;
    uint32_t outputlen = sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH);
    uint32_t fetchKeyLen = 0;
    bool isFinish = false;

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 获取kv
    ret = GmcKvPrepareStmtByLabelName(stmt, "peiyan_5");
    EXPECT_EQ(GMERR_OK, ret);

    char *long_kv_value = (char *)malloc(sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH));
    (void)memset(long_kv_value, 0, sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH));

    char **key_name = (char **)malloc(sizeof(char *) * key_count);
    for (int count = 0; count < key_count; count++) {
        key_name[count] = (char *)malloc(sizeof(char) * KV_TABLE_KEY_MAX_LENTH);
        (void)memset(key_name[count], 0, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH));
        (void)memset(key_name[count], 'A' + count, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH - 1));
        outputlen = sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH);
        ret = GmcKvGet(stmt, key_name[count], strlen(key_name[count]), long_kv_value, &outputlen);
        EXPECT_EQ(true, ret == GMERR_OK || ret == GMERR_NO_DATA);
        TEST_INFO("Get kv is success. \n");
    };
    char *output = NULL;
    char *output_key = NULL;

    ret = GmcKvScan(stmt, key_count * 2);
    EXPECT_EQ(GMERR_OK, ret);
    while (true) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        outputlen = sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH);
        ret = GmcKvGetFromStmt(stmt, (void **)&output_key, &fetchKeyLen, (void **)&output, &outputlen);
        EXPECT_EQ(GMERR_OK, ret);
        TEST_INFO("Fetch kv key is success: %s \n", output);
        EXPECT_EQ(KV_TABLE_KEY_MAX_LENTH - 1, fetchKeyLen);
    }

    for (int count = 0; count < key_count; count++) {
        free(key_name[count]);
    }
    free(key_name);
    free(long_kv_value);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("Thread read opera thread is end. \n");
    return NULL;
}
void *kv_operater_del_key_thread(void *arg)
{
    TEST_INFO("Thread del opera thread is start. \n");
    int ret = 0;
    int i = *(int *)arg;
    void *kvtable = NULL;
    const int key_count = 26;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    AsyncUserDataT data = {0};

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 获取kv
    ret = GmcKvPrepareStmtByLabelName(stmt, "peiyan_5");
    EXPECT_EQ(GMERR_OK, ret);

    char **key_name = (char **)malloc(sizeof(char *) * key_count);
    for (int count = 0; count < key_count; count++) {
        key_name[count] = (char *)malloc(sizeof(char) * KV_TABLE_KEY_MAX_LENTH);
        (void)memset(key_name[count], 0, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH));
        (void)memset(key_name[count], 'A' + count, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH - 1));
        // 设置k-v值 key为 最大长度
        ret = GmcKvRemove(stmt, key_name[count], strlen(key_name[count]));
        EXPECT_EQ(GMERR_OK, ret);
        TEST_INFO("Del kv one record success. \n");
    }

    for (int count = 0; count < key_count; count++) {
        free(key_name[count]);
    }
    free(key_name);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("Thread del opera thread is end. \n");
    return NULL;
}

/* ****************************************************************************
 Description  : 同步/异步 异常创建 kv 表有错误信息, 交互kv线程DML操作, key value 单表交互并发用例
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_031)
{
    int32_t ret = 0;
    // 同步创建kv表 和 异步创建kv表
    char labelNameKv[MAX_NAME_LENGTH];
    const int key_count = 26;
    uint32_t tableNum = 0;
#ifndef RUN_INDEPENDENT
    ret = TestGetKvTableNum(&tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    tableNum += 500;
#endif
    for (uint32_t i = 0; i < KV_MAX_CREARE_TABLE_COUNT - tableNum; i++) {
        sprintf(labelNameKv, "peiyan_%d", i);
        ret = GmcKvCreateTable(stmt, labelNameKv, g_configJson);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcKvPrepareStmtByLabelName(stmt, "peiyan_5");
    ASSERT_EQ(GMERR_OK, ret);
    GmcKvTupleT kvInfo1 = {0};
    char long_kv_value[] = "zhangsan";

    char **key_name = (char **)malloc(sizeof(char *) * key_count);
    for (int count = 0; count < key_count; count++) {
        key_name[count] = (char *)malloc(sizeof(char) * KV_TABLE_KEY_MAX_LENTH);
        (void)memset(key_name[count], 0, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH));
        (void)memset(key_name[count], 'A' + count, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH - 1));
        // 设置k-v值 key为 最大长度
        kvInfo1.key = key_name[count];
        kvInfo1.keyLen = strlen(key_name[count]);
        kvInfo1.value = long_kv_value;
        kvInfo1.valueLen = strlen(long_kv_value);
        ret = GmcKvSet(stmt, key_name[count], strlen(key_name[count]), long_kv_value, strlen(long_kv_value));
        ASSERT_EQ(GMERR_OK, ret);
        TEST_INFO("Set kv is success. \n");
    }

    pthread_t kvTabelOper[CONN_THREAD_100];
    int index[CONN_THREAD_100];
    void *thr_ret[CONN_THREAD_100];

    for (int i = 0; i < (CONN_THREAD_100 - 1) / 3; i++) {
        index[i] = i;
        ret = pthread_create(&kvTabelOper[i], NULL, kv_operater_update_key_thread, &index[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = (CONN_THREAD_100 - 1) / 3; i < (CONN_THREAD_100 - 1) / 3 * 2; i++) {
        index[i] = i;
        ret = pthread_create(&kvTabelOper[i], NULL, kv_operater_read_key_thread, &index[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = (CONN_THREAD_100 - 1) / 3 * 2; i < CONN_THREAD_100; i++) {
        index[i] = i;
        ret = pthread_create(&kvTabelOper[i], NULL, kv_operater_del_key_thread, &index[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }

    for (int i = 0; i < CONN_THREAD_100; i++) {
        pthread_join(kvTabelOper[i], &thr_ret[i]);
    }
    for (uint32_t i = 0; i < KV_MAX_CREARE_TABLE_COUNT - tableNum; i++) {
        sprintf(labelNameKv, "peiyan_%d", i);
        ret = GmcKvDropTable(stmt, labelNameKv);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int count = 0; count < key_count; count++) {
        free(key_name[count]);
    }
    free(key_name);
}

void *kv_operater_read_limitcount_test_thread(void *arg)
{
    TEST_INFO("Thread read opera thread is start. \n");
    int ret = 0;
    int i = *(int *)arg;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *kvtable = NULL;
    const int key_count = 26;
    uint32_t outputlen = sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH);
    uint32_t fetchKeyLen = 0;
    bool isFinish = false;

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 获取kv
    ret = GmcKvPrepareStmtByLabelName(stmt, "peiyan_5");
    EXPECT_EQ(GMERR_OK, ret);

    char *long_kv_value = (char *)malloc(sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH));
    (void)memset(long_kv_value, 0, sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH));

    char **key_name = (char **)malloc(sizeof(char *) * key_count);
    for (int count = 0; count < key_count; count++) {
        key_name[count] = (char *)malloc(sizeof(char) * KV_TABLE_KEY_MAX_LENTH);
        (void)memset(key_name[count], 0, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH));
        (void)memset(key_name[count], 'A' + count, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH - 1));
        ret = GmcKvGet(stmt, key_name[count], strlen(key_name[count]), long_kv_value, &outputlen);
        EXPECT_EQ(true, ret == GMERR_OK || ret == GMERR_DATA_EXCEPTION);
        TEST_INFO("Get kv is success. \n");
    };
    char *output = NULL;
    char *output_key = NULL;

    int scan_count = 0;
    ret = GmcKvScan(stmt, key_count / 2);  // 只获取其中一半的数据
    EXPECT_EQ(GMERR_OK, ret);
    while (true) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        ret = GmcKvGetFromStmt(stmt, (void **)&output_key, &fetchKeyLen, (void **)&output, &outputlen);
        EXPECT_EQ(GMERR_OK, ret);
        TEST_INFO("Fetch kv key is success: %s \n", output);
        EXPECT_EQ(KV_TABLE_KEY_MAX_LENTH - 1, fetchKeyLen);
        scan_count++;
    }
    EXPECT_EQ(scan_count, key_count / 2);

    for (int count = 0; count < key_count; count++) {
        free(key_name[count]);
    }
    free(key_name);
    free(long_kv_value);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("Thread read opera thread is end. \n");
    return NULL;
}
/* ****************************************************************************
 Description  : 同步/异步 异常创建 kv 表有错误信息, 交互kv线程DML操作, key 单表扫描 limitCount 测试
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_032)
{
    int32_t ret = 0;
    // 同步创建kv表 和 异步创建kv表
    char labelNameKv[MAX_NAME_LENGTH];
    const int key_count = 26;
    uint32_t tableNum = 0;
#ifndef RUN_INDEPENDENT
    ret = TestGetKvTableNum(&tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
    for (uint32_t i = 0; i < KV_MAX_CREARE_TABLE_COUNT - tableNum; i++) {
        sprintf(labelNameKv, "peiyan_%d", i);
        ret = GmcKvCreateTable(stmt, labelNameKv, g_configJson);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcKvPrepareStmtByLabelName(stmt, "peiyan_5");
    ASSERT_EQ(GMERR_OK, ret);
    GmcKvTupleT kvInfo1 = {0};
    char long_kv_value[] = "zhangsan";

    char **key_name = (char **)malloc(sizeof(char *) * key_count);
    for (int count = 0; count < key_count; count++) {
        key_name[count] = (char *)malloc(sizeof(char) * KV_TABLE_KEY_MAX_LENTH);
        (void)memset(key_name[count], 0, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH));
        (void)memset(key_name[count], 'A' + count, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH - 1));
        // 设置k-v值 key为 最大长度
        kvInfo1.key = key_name[count];
        kvInfo1.keyLen = strlen(key_name[count]);
        kvInfo1.value = long_kv_value;
        kvInfo1.valueLen = strlen(long_kv_value);
        ret = GmcKvSet(stmt, key_name[count], strlen(key_name[count]), long_kv_value, strlen(long_kv_value));
        ASSERT_EQ(GMERR_OK, ret);
        TEST_INFO("Set kv is success. \n");
    }

    pthread_t kvTabelOper[CONN_THREAD_100 / 2];
    int index[CONN_THREAD_100 / 2];
    void *thr_ret[CONN_THREAD_100 / 2];

    for (int i = 0; i < CONN_THREAD_100 / 2; i++) {
        index[i] = i;
        ret = pthread_create(&kvTabelOper[i], NULL, kv_operater_read_limitcount_test_thread, &index[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < CONN_THREAD_100 / 2; i++) {
        pthread_join(kvTabelOper[i], &thr_ret[i]);
    }

    for (uint32_t i = 0; i < KV_MAX_CREARE_TABLE_COUNT - tableNum; i++) {
        sprintf(labelNameKv, "peiyan_%d", i);
        ret = GmcKvDropTable(stmt, labelNameKv);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int count = 0; count < key_count; count++) {
        free(key_name[count]);
    }
    free(key_name);
}

/* ****************************************************************************
 Description  : 同步/异步 异常创建 kv 表有错误信息, 交互kv线程DML操作, key 单表扫描
并发操作后,查询表记录数,雷同31用例,加了get接口
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_033)
{
    int32_t ret = 0;
    // 同步创建kv表 和 异步创建kv表
    char labelNameKv[MAX_NAME_LENGTH];
    const int key_count = 26;
    uint32_t tableNum = 0;
#ifndef RUN_INDEPENDENT
    ret = TestGetKvTableNum(&tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
    for (uint32_t i = 0; i < KV_MAX_CREARE_TABLE_COUNT - tableNum; i++) {
        sprintf(labelNameKv, "peiyan_%d", i);
        ret = GmcKvCreateTable(stmt, labelNameKv, g_configJson);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcKvPrepareStmtByLabelName(stmt, "peiyan_5");
    ASSERT_EQ(GMERR_OK, ret);
    GmcKvTupleT kvInfo1 = {0};
    char long_kv_value[] = "zhangsan";

    char **key_name = (char **)malloc(sizeof(char *) * key_count);
    for (int count = 0; count < key_count; count++) {
        key_name[count] = (char *)malloc(sizeof(char) * KV_TABLE_KEY_MAX_LENTH);
        (void)memset(key_name[count], 0, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH));
        (void)memset(key_name[count], 'A' + count, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH - 1));
        // 设置k-v值 key为 最大长度
        kvInfo1.key = key_name[count];
        kvInfo1.keyLen = strlen(key_name[count]);
        kvInfo1.value = long_kv_value;
        kvInfo1.valueLen = strlen(long_kv_value);
        ret = GmcKvSet(stmt, key_name[count], strlen(key_name[count]), long_kv_value, strlen(long_kv_value));
        ASSERT_EQ(GMERR_OK, ret);
        TEST_INFO("Set kv is success. \n");
        uint32_t valueSize = 0;
        ret = GmcKvGetValueSize(stmt, key_name[count], strlen(key_name[count]), &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(strlen(long_kv_value), valueSize);
    }

    pthread_t kvTabelOper[CONN_THREAD_100];
    int index[CONN_THREAD_100];
    void *thr_ret[CONN_THREAD_100];

    for (int i = 0; i < (CONN_THREAD_100 - 1) / 3; i++) {
        index[i] = i;
        ret = pthread_create(&kvTabelOper[i], NULL, kv_operater_update_key_thread, &index[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = (CONN_THREAD_100 - 1) / 3; i < (CONN_THREAD_100 - 1) / 3 * 2; i++) {
        index[i] = i;
        ret = pthread_create(&kvTabelOper[i], NULL, kv_operater_read_key_thread, &index[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = (CONN_THREAD_100 - 1) / 3 * 2; i < CONN_THREAD_100; i++) {
        index[i] = i;
        ret = pthread_create(&kvTabelOper[i], NULL, kv_operater_del_key_thread, &index[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < CONN_THREAD_100; i++) {
        pthread_join(kvTabelOper[i], &thr_ret[i]);
    }
    uint32_t count2 = 0;
    ret = GmcKvTableRecordCount(stmt, &count2);
    ASSERT_EQ(GMERR_OK, ret);
    TEST_INFO("Get kv count is : %d \n", count2);
    ASSERT_GE(count2, 0);

    for (uint32_t i = 0; i < KV_MAX_CREARE_TABLE_COUNT - tableNum; i++) {
        sprintf(labelNameKv, "peiyan_%d", i);
        ret = GmcKvDropTable(stmt, labelNameKv);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int count = 0; count < key_count; count++) {
        free(key_name[count]);
    }
    free(key_name);
}

void *kv_operater_async_update_key_thread(void *arg)
{
    TEST_INFO("Thread async update opera thread is start. \n");
    int ret = 0;
    int i = *(int *)arg;
    GmcStmtT *g_stmt_async = NULL;
    GmcConnT *g_conn_async = NULL;
    void *kvtable = NULL;
    const int key_count = 27;
    const char *lastErrorStr = NULL;
    AsyncUserDataT data = {0};

    // 创建异步连接
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    // 获取kv
    ret = GmcKvPrepareStmtByLabelName(g_stmt_async, "peiyan_5");
    EXPECT_EQ(GMERR_OK, ret);

    GmcKvTupleT kvInfo1 = {0};
    char *long_kv_value = (char *)malloc(sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH));
    (void)memset(long_kv_value, 0, sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH));
    (void)memset(long_kv_value, 'a', sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH - 1));

    char **key_name = (char **)malloc(sizeof(char *) * key_count);
    for (int count = 0; count < key_count; count++) {
        key_name[count] = (char *)malloc(sizeof(char) * KV_TABLE_KEY_MAX_LENTH);
        (void)memset(key_name[count], 0, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH));
        (void)memset(key_name[count], 'A' + count, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH - 1));
        // 设置k-v值 key为 最大长度
        kvInfo1.key = key_name[count];
        kvInfo1.keyLen = strlen(key_name[count]);
        kvInfo1.value = long_kv_value;
        kvInfo1.valueLen = strlen(long_kv_value);
        do {
            ret = GmcKvSetAsync(g_stmt_async, &kvInfo1, set_kv_callback, &data);
            EXPECT_EQ(GMERR_OK, ret);
            ret = testWaitAsyncRecv(&data);
            EXPECT_EQ(GMERR_OK, ret);
        } while (data.status == GMERR_LOCK_NOT_AVAILABLE);
        EXPECT_EQ(GMERR_OK, data.status);
        TEST_INFO("Set kv is success. \n");
    }

    for (int count = 0; count < key_count; count++) {
        free(key_name[count]);
    }
    free(key_name);
    free(long_kv_value);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("Thread async update opera thread is end. \n");
    return NULL;
}

void *kv_operater_async_query_key_thread(void *arg)
{
    TEST_INFO("Thread async read opera thread is start. \n");
    int ret = 0;
    int i = *(int *)arg;
    GmcStmtT *g_stmt_async = NULL;
    GmcConnT *g_conn_async = NULL;
    void *kvtable = NULL;
    const int key_count = 26;
    uint32_t outputlen = 0;
    uint32_t fetchKeyLen = 0;
    AsyncUserDataT data = {0};

    // 创建异步连接, 异步读kv接口已删除
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    // 获取kv
    ret = GmcKvPrepareStmtByLabelName(g_stmt_async, "peiyan_5");
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("Thread async read opera thread is end. \n");
    return NULL;
}
void *kv_operater_async_del_key_thread(void *arg)
{
    TEST_INFO("Thread async del opera thread is start. \n");
    int ret = 0;
    AsyncUserDataT data = {0};
    int i = *(int *)arg;
    void *kvtable = NULL;
    const int key_count = 26;
    GmcStmtT *g_stmt_async = NULL;
    GmcConnT *g_conn_async = NULL;

    // 创建异步连接
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    // 获取kv
    ret = GmcKvPrepareStmtByLabelName(g_stmt_async, "peiyan_5");
    EXPECT_EQ(GMERR_OK, ret);
    char *long_kv_value = (char *)malloc(sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH));
    (void)memset(long_kv_value, 0, sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH));

    char **key_name = (char **)malloc(sizeof(char *) * key_count);
    for (int count = 0; count < key_count; count++) {
        key_name[count] = (char *)malloc(sizeof(char) * KV_TABLE_KEY_MAX_LENTH);
        (void)memset(key_name[count], 0, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH));
        (void)memset(key_name[count], 'A' + count, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH - 1));
        do {
            ret = GmcKvRemoveAsync(g_stmt_async, key_name[count], strlen(key_name[count]), delete_kv_callback, &data);
            EXPECT_EQ(GMERR_OK, ret);
            ret = testWaitAsyncRecv(&data);
            EXPECT_EQ(GMERR_OK, ret);
        } while (data.status == GMERR_LOCK_NOT_AVAILABLE);
        EXPECT_EQ(GMERR_OK, data.status);
        TEST_INFO(" Async Del kv one record success. \n");
    }

    for (int count = 0; count < key_count; count++) {
        free(key_name[count]);
    }
    free(key_name);
    free(long_kv_value);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("Thread async del opera thread is end. \n");
    return NULL;
}
/* ****************************************************************************
 Description  : 同步/异步 异常创建 kv 表有错误信息, 交互kv线程DML操作, key 交互异步kv接口
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_034)
{
    int32_t ret = 0;
    // 同步创建kv表 和 异步创建kv表
    char labelNameKv[MAX_NAME_LENGTH];
    const int key_count = 26;
    AsyncUserDataT data = {0};
    uint32_t tableNum = 0;
#ifndef RUN_INDEPENDENT
    ret = TestGetKvTableNum(&tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
    for (uint32_t i = 0; i < KV_MAX_CREARE_TABLE_COUNT - tableNum; i++) {
        sprintf(labelNameKv, "peiyan_%d", i);
        ret = GmcKvCreateTableAsync(g_stmt_async, labelNameKv, g_configJson, create_kv_table_callback, &data);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }
    ret = GmcKvPrepareStmtByLabelName(g_stmt_async, "peiyan_5");
    EXPECT_EQ(GMERR_OK, ret);

    GmcKvTupleT kvInfo1 = {0};
    char long_kv_value[] = "zhangsan";

    char **key_name = (char **)malloc(sizeof(char *) * key_count);
    for (int count = 0; count < key_count; count++) {
        key_name[count] = (char *)malloc(sizeof(char) * KV_TABLE_KEY_MAX_LENTH);
        (void)memset(key_name[count], 0, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH));
        (void)memset(key_name[count], 'A' + count, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH - 1));
        // 设置k-v值 key为 最大长度
        kvInfo1.key = key_name[count];
        kvInfo1.keyLen = strlen(key_name[count]);
        kvInfo1.value = long_kv_value;
        kvInfo1.valueLen = strlen(long_kv_value);
        ret = GmcKvSetAsync(g_stmt_async, &kvInfo1, set_kv_callback, &data);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        TEST_INFO("Set  %d kv is success. \n", count);
    }

    pthread_t kvTabelOper[CONN_THREAD_100];
    int index[CONN_THREAD_100];
    void *thr_ret[CONN_THREAD_100];

    for (int i = 0; i < (CONN_THREAD_100 - 1) / 3; i++) {
        index[i] = i;
        ret = pthread_create(&kvTabelOper[i], NULL, kv_operater_async_update_key_thread, &index[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = (CONN_THREAD_100 - 1) / 3; i < (CONN_THREAD_100 - 1) / 3 * 2; i++) {
        index[i] = i;
        ret = pthread_create(&kvTabelOper[i], NULL, kv_operater_async_query_key_thread, &index[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = (CONN_THREAD_100 - 1) / 3 * 2; i < CONN_THREAD_100; i++) {
        index[i] = i;
        ret = pthread_create(&kvTabelOper[i], NULL, kv_operater_async_del_key_thread, &index[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < CONN_THREAD_100; i++) {
        pthread_join(kvTabelOper[i], &thr_ret[i]);
    }

    for (uint32_t i = 0; i < KV_MAX_CREARE_TABLE_COUNT - tableNum; i++) {
        sprintf(labelNameKv, "peiyan_%d", i);
        ret = GmcKvDropTableAsync(g_stmt_async, labelNameKv, drop_kv_table_callback, &data);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }
    for (int count = 0; count < key_count; count++) {
        free(key_name[count]);
    }
    free(key_name);
}

/* ****************************************************************************
 Description  : 同步/异步 异常创建 kv 表有错误信息, 交互kv线程DML操作, key 交互异步kv接口,满表状态,多线程写删
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_035)
{
    int32_t ret = 0;
    // 同步创建kv表 和 异步创建kv表
    char labelNameKv[MAX_NAME_LENGTH];
    const int key_count = 26;
    AsyncUserDataT data = {0};
    uint32_t tableNum = 0;
#ifndef RUN_INDEPENDENT
    ret = TestGetKvTableNum(&tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
    for (uint32_t i = 0; i < KV_MAX_CREARE_TABLE_COUNT - tableNum; i++) {
        sprintf(labelNameKv, "peiyan_%d", i);
        ret = GmcKvCreateTableAsync(g_stmt_async, labelNameKv, g_configJson, create_kv_table_callback, &data);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }
    ret = GmcKvPrepareStmtByLabelName(g_stmt_async, "peiyan_5");
    EXPECT_EQ(GMERR_OK, ret);

    GmcKvTupleT kvInfo1 = {0};
    char long_kv_value[] = "zhangsan";

    char **key_name = (char **)malloc(sizeof(char *) * key_count);
    for (int count = 0; count < key_count; count++) {
        key_name[count] = (char *)malloc(sizeof(char) * KV_TABLE_KEY_MAX_LENTH);
        (void)memset(key_name[count], 0, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH));
        (void)memset(key_name[count], 'A' + count, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH - 1));
        // 设置k-v值 key为 最大长度
        kvInfo1.key = key_name[count];
        kvInfo1.keyLen = strlen(key_name[count]);
        kvInfo1.value = long_kv_value;
        kvInfo1.valueLen = strlen(long_kv_value);
        ret = GmcKvSetAsync(g_stmt_async, &kvInfo1, set_kv_callback, &data);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        TEST_INFO("Set  %d kv is success. \n", count);
    }

    pthread_t kvTabelOper[CONN_THREAD_100];
    int index[CONN_THREAD_100];
    void *thr_ret[CONN_THREAD_100];

    for (int i = 0; i < CONN_THREAD_100 / 2; i++) {
        index[i] = i;
        ret = pthread_create(&kvTabelOper[i], NULL, kv_operater_async_update_key_thread, &index[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = CONN_THREAD_100 / 2; i < CONN_THREAD_100; i++) {
        index[i] = i;
        ret = pthread_create(&kvTabelOper[i], NULL, kv_operater_async_del_key_thread, &index[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < CONN_THREAD_100; i++) {
        pthread_join(kvTabelOper[i], &thr_ret[i]);
    }

    for (uint32_t i = 0; i < KV_MAX_CREARE_TABLE_COUNT - tableNum; i++) {
        sprintf(labelNameKv, "peiyan_%d", i);
        ret = GmcKvDropTableAsync(g_stmt_async, labelNameKv, drop_kv_table_callback, &data);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }
    for (int count = 0; count < key_count; count++) {
        free(key_name[count]);
    }
    free(key_name);
}

void *kv_operater_batch_update_key_thread(void *arg)
{
    TEST_INFO("Thread batch update opera thread is start. \n");
    int ret = 0;
    int i = *(int *)arg;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *kvtable = NULL;
    const int key_count = 26;
    const char *lastErrorStr = NULL;
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 获取kv
    ret = GmcKvPrepareStmtByLabelName(stmt, "peiyan_5");
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    char *long_kv_value = (char *)malloc(sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH_30K));
    (void)memset(long_kv_value, 0, sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH_30K));
    (void)memset(long_kv_value, 'a', sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH_30K - 1));

    char **key_name = (char **)malloc(sizeof(char *) * key_count);
    for (int count = 0; count < key_count; count++) {
        key_name[count] = (char *)malloc(sizeof(char) * KV_TABLE_KEY_MAX_LENTH);
        (void)memset(key_name[count], 0, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH));
        (void)memset(key_name[count], 'A' + count, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH - 1));
        // 设置k-v值 key为 最大长度
        ret = GmcKvInputToStmt(
            stmt, key_name[count], strlen(key_name[count]) + 1, long_kv_value, strlen(long_kv_value) + 1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddKvDML(batch, stmt, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        TEST_INFO("Set kv is success. \n");
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    if (ret == GMERR_LOCK_NOT_AVAILABLE)
        goto threadend;
    EXPECT_EQ(GMERR_OK, ret);

    // 关闭kv表
threadend:
    for (int count = 0; count < key_count; count++) {
        free(key_name[count]);
    }
    free(key_name);
    free(long_kv_value);
    GmcBatchDestroy(batch);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("Thread batch update opera thread is end. \n");
    return NULL;
}
void *kv_operater_batch_read_key_thread(void *arg)
{
    TEST_INFO("Thread read opera thread is start. \n");
    int ret = 0;
    int i = *(int *)arg;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *kvtable = NULL;
    const int key_count = 26;
    uint32_t fetchKeyLen = 0;
    bool isFinish = false;
    uint32_t outputlen = 0;

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 获取kv
    ret = GmcKvPrepareStmtByLabelName(stmt, "peiyan_5");
    EXPECT_EQ(GMERR_OK, ret);

    char *long_kv_value = (char *)malloc(sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH));
    (void)memset(long_kv_value, 0, sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH));

    char **key_name = (char **)malloc(sizeof(char *) * key_count);
    for (int count = 0; count < key_count; count++) {
        key_name[count] = (char *)malloc(sizeof(char) * KV_TABLE_KEY_MAX_LENTH);
        (void)memset(key_name[count], 0, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH));
        (void)memset(key_name[count], 'A' + count, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH - 1));
        outputlen = sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH);
        ret = GmcKvGet(stmt, key_name[count], strlen(key_name[count]) + 1, long_kv_value, &outputlen);
        EXPECT_EQ(true, ret == GMERR_OK || ret == GMERR_NO_DATA);
        if (ret != GMERR_OK && ret != GMERR_DATA_EXCEPTION && ret == GMERR_NO_DATA) {
            printf("the ret is %d\n", ret);
        }
        TEST_INFO("Get kv is success. \n");
    };
    char *output = NULL;
    char *output_key = NULL;
    outputlen = sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH_1M);
    ret = GmcKvScan(stmt, key_count * 2);
    EXPECT_EQ(GMERR_OK, ret);
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        ret = GmcKvGetFromStmt(stmt, (void **)&output_key, &fetchKeyLen, (void **)&output, &outputlen);
        EXPECT_EQ(GMERR_OK, ret);
        TEST_INFO("Fetch kv key is success: %s \n", output);
        EXPECT_EQ(KV_TABLE_KEY_MAX_LENTH, fetchKeyLen);
    }

    for (int count = 0; count < key_count; count++) {
        free(key_name[count]);
    }
    free(key_name);
    free(long_kv_value);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("Thread read opera thread is end. \n");
    return NULL;
}
void *kv_operater_batch_del_key_thread(void *arg)
{
    TEST_INFO("Thread del opera thread is start. \n");
    int ret = 0;
    int i = *(int *)arg;
    void *kvtable = NULL;
    const int key_count = 26;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 获取kv
    ret = GmcKvPrepareStmtByLabelName(stmt, "peiyan_5");
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    char **key_name = (char **)malloc(sizeof(char *) * key_count);
    for (int count = 0; count < key_count; count++) {
        key_name[count] = (char *)malloc(sizeof(char) * KV_TABLE_KEY_MAX_LENTH);
        (void)memset(key_name[count], 0, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH));
        (void)memset(key_name[count], 'A' + count, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH - 1));
        ret = GmcKvInputToStmt(stmt, key_name[count], strlen(key_name[count]) + 1, NULL, 0);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddKvDML(batch, stmt, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        TEST_INFO("Del kv batch record success. \n");
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    if (ret == GMERR_LOCK_NOT_AVAILABLE)
        goto threadend;
    EXPECT_EQ(GMERR_OK, ret);

    // 关闭kv表
threadend:
    for (int count = 0; count < key_count; count++) {
        free(key_name[count]);
    }
    free(key_name);
    GmcBatchDestroy(batch);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("Thread del opera thread is end. \n");
    return NULL;
}
/* ****************************************************************************
 Description  : 同步/异步 异常创建 kv 表有错误信息, 交互kv线程DML操作, key 并发交互批量接口
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_036)
{
    int32_t ret = 0;
    // 异步创建kv表
    char labelNameKv[MAX_NAME_LENGTH];
    const int key_count = 26;
    AsyncUserDataT data = {0};
    uint32_t tableNum = 0;
#ifndef RUN_INDEPENDENT
    ret = TestGetKvTableNum(&tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
    for (uint32_t i = 0; i < KV_MAX_CREARE_TABLE_COUNT - tableNum; i++) {
        sprintf(labelNameKv, "peiyan_%d", i);
        ret = GmcKvCreateTableAsync(g_stmt_async, labelNameKv, g_configJson, create_kv_table_callback, &data);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }
    ret = GmcKvPrepareStmtByLabelName(stmt, "peiyan_5");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(g_stmt_async, "peiyan_5");
    EXPECT_EQ(GMERR_OK, ret);

    GmcKvTupleT kvInfo1 = {0};
    char long_kv_value[] = "zhangsan";

    char **key_name = (char **)malloc(sizeof(char *) * key_count);
    for (int count = 0; count < key_count; count++) {
        key_name[count] = (char *)malloc(sizeof(char) * KV_TABLE_KEY_MAX_LENTH);
        (void)memset(key_name[count], 0, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH));
        (void)memset(key_name[count], 'A' + count, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH - 1));
        // 设置k-v值 key为 最大长度
        kvInfo1.key = key_name[count];
        kvInfo1.keyLen = strlen(key_name[count]) + 1;
        kvInfo1.value = long_kv_value;
        kvInfo1.valueLen = strlen(long_kv_value) + 1;
        ret = GmcKvSetAsync(g_stmt_async, &kvInfo1, set_kv_callback, &data);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        TEST_INFO("Set  %d kv is success. \n", count);
    }

    pthread_t kvTabelOper[CONN_THREAD_100];
    int index[CONN_THREAD_100];
    void *thr_ret[CONN_THREAD_100];

    for (int i = 0; i < (CONN_THREAD_100 - 1) / 3; i++) {
        index[i] = i;
        ret = pthread_create(&kvTabelOper[i], NULL, kv_operater_batch_update_key_thread, &index[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = (CONN_THREAD_100 - 1) / 3; i < (CONN_THREAD_100 - 1) / 3 * 2; i++) {
        index[i] = i;
        ret = pthread_create(&kvTabelOper[i], NULL, kv_operater_batch_read_key_thread, &index[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = (CONN_THREAD_100 - 1) / 3 * 2; i < CONN_THREAD_100; i++) {
        index[i] = i;
        ret = pthread_create(&kvTabelOper[i], NULL, kv_operater_batch_del_key_thread, &index[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < CONN_THREAD_100; i++) {
        pthread_join(kvTabelOper[i], &thr_ret[i]);
    }
    for (uint32_t i = 0; i < KV_MAX_CREARE_TABLE_COUNT - tableNum; i++) {
        sprintf(labelNameKv, "peiyan_%d", i);
        ret = GmcKvDropTableAsync(g_stmt_async, labelNameKv, drop_kv_table_callback, &data);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }
    for (int count = 0; count < key_count; count++) {
        free(key_name[count]);
    }
    free(key_name);
}

void *kv_operater_batch_create_kv_table_thread(void *arg)
{
    TEST_INFO("Thread batch create/del table opera thread is start. \n");
    int ret = 0;
    int i = *(int *)arg;
    void *kvtable = NULL;
    const int key_count = 26;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    char labelNameKv[MAX_NAME_LENGTH];
    const char *lastErrorStr = NULL;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 获取kv
    ret = GmcBatchPrepare(conn, NULL, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 50; i < KV_MAX_CREARE_TABLE_COUNT; i++) {
        sprintf(labelNameKv, "KvTabel_%d", i);
        ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_KV_TABLE, labelNameKv, NULL, NULL);
        if (ret != GMERR_OK) {
            EXPECT_EQ(GMERR_BATCH_BUFFER_FULL, ret);  // 第1025次添加操作时预期返回报错 11002
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            printf("[INFO] Add cmd labelName: %s, status is %d \n", labelNameKv, ret);
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    // 报文优化2023.4.20
    // buffer大小波动变化(166~167)，设置门槛值
    EXPECT_GE(totalNum, (uint32_t)165);

    ret = GmcBatchReset(batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 50; i < successNum + 51; i++) {
        sprintf(labelNameKv, "KvTabel_%d", i);
        ret = GmcBatchAddDDL(batch, GMC_OPERATION_DROP_KV_TABLE, labelNameKv, NULL, NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(true, ret == GMERR_UNDEFINED_TABLE || ret == GMERR_OK);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    lastErrorStr = GmcGetLastError();
    TEST_INFO("This lastError is %s.\n", lastErrorStr);
    EXPECT_EQ(totalNum, successNum + 1);  // 批量中有删除一个不存在的表结构
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("Thread batch create/del table opera thread is end. \n");
    return NULL;
}
/* ****************************************************************************
 Description  : 同步/异步 异常创建 kv 表有错误信息, 交互kv线程DML操作, key 并发交互批量建表/删表
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_037)
{
    int32_t ret = 0;

    // 同步创建kv表 和 异步创建kv表
    char labelNameKv[MAX_NAME_LENGTH];
    const int key_count = 26;
    AsyncUserDataT data = {0};
    for (uint32_t i = 0; i < 50; i++) {
        sprintf(labelNameKv, "peiyan_%d", i);
        ret = GmcKvCreateTableAsync(g_stmt_async, labelNameKv, g_configJson, create_kv_table_callback, &data);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }
    ret = GmcKvPrepareStmtByLabelName(stmt, "peiyan_5");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(g_stmt_async, "peiyan_5");
    EXPECT_EQ(GMERR_OK, ret);

    GmcKvTupleT kvInfo1 = {0};
    char long_kv_value[] = "zhangsan";

    char **key_name = (char **)malloc(sizeof(char *) * key_count);
    for (int count = 0; count < key_count; count++) {
        key_name[count] = (char *)malloc(sizeof(char) * KV_TABLE_KEY_MAX_LENTH);
        (void)memset(key_name[count], 0, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH));
        (void)memset(key_name[count], 'A' + count, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH - 1));
        // 设置k-v值 key为 最大长度
        kvInfo1.key = key_name[count];
        kvInfo1.keyLen = strlen(key_name[count]) + 1;
        kvInfo1.value = long_kv_value;
        kvInfo1.valueLen = strlen(long_kv_value) + 1;
        ret = GmcKvSetAsync(g_stmt_async, &kvInfo1, set_kv_callback, &data);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        TEST_INFO("Set  %d kv is success. \n", count);
    }

    pthread_t kvTabelOper[CONN_THREAD_100];
    int index[CONN_THREAD_100];
    void *thr_ret[CONN_THREAD_100];

    for (int i = 0; i < (CONN_THREAD_100 - 1) / 3; i++) {
        index[i] = i;
        ret = pthread_create(&kvTabelOper[i], NULL, kv_operater_batch_update_key_thread, &index[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = (CONN_THREAD_100 - 1) / 3; i < (CONN_THREAD_100 - 1) / 3 * 2; i++) {
        index[i] = i;
        ret = pthread_create(&kvTabelOper[i], NULL, kv_operater_batch_read_key_thread, &index[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = (CONN_THREAD_100 - 1) / 3 * 2; i < CONN_THREAD_100 - 1; i++) {
        index[i] = i;
        ret = pthread_create(&kvTabelOper[i], NULL, kv_operater_batch_del_key_thread, &index[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = CONN_THREAD_100 - 1; i < CONN_THREAD_100; i++) {
        index[i] = i;
        ret = pthread_create(&kvTabelOper[i], NULL, kv_operater_batch_create_kv_table_thread, &index[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < CONN_THREAD_100; i++) {
        pthread_join(kvTabelOper[i], &thr_ret[i]);
    }

    for (uint32_t i = 0; i < 50; i++) {
        sprintf(labelNameKv, "peiyan_%d", i);
        ret = GmcKvDropTableAsync(g_stmt_async, labelNameKv, drop_kv_table_callback, &data);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }
    for (int count = 0; count < key_count; count++) {
        free(key_name[count]);
    }
    free(key_name);
}

void *kv_operater_async_batch_update_key_thread(void *arg)
{
    TEST_INFO("Thread asynv batch update opera thread is start. \n");
    int ret = 0;
    int i = *(int *)arg;
    GmcStmtT *g_stmt_async = NULL;
    GmcConnT *g_conn_async = NULL;
    const int key_count = 26;
    const char *lastErrorStr = NULL;
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    char kvTableName[] = "peiyan_5";
    AsyncUserDataT data = {0};

    GmcBatchT *batch = NULL;
    GmcBatchRetT *batchRet;

    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    // open
    ret = GmcKvPrepareStmtByLabelName(g_stmt_async, kvTableName);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    char *long_kv_value = (char *)malloc(sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH_30K));
    (void)memset(long_kv_value, 0, sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH_30K));
    (void)memset(long_kv_value, 'a', sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH_30K - 1));

    char **key_name = (char **)malloc(sizeof(char *) * key_count);
    for (int count = 0; count < key_count; count++) {
        ret = GmcBatchReset(batch);
        EXPECT_EQ(GMERR_OK, ret);
        key_name[count] = (char *)malloc(sizeof(char) * KV_TABLE_KEY_MAX_LENTH);
        (void)memset(key_name[count], 0, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH));
        (void)memset(key_name[count], 'A' + count, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH - 1));
        // 设置k-v值 key为 最大长度
        ret = GmcKvInputToStmt(
            g_stmt_async, key_name[count], strlen(key_name[count]) + 1, long_kv_value, strlen(long_kv_value) + 1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddKvDML(batch, g_stmt_async, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        TEST_INFO("Set kv is success. \n");
    }

    // 查询插入的结果
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    for (int count = 0; count < key_count; count++) {
        free(key_name[count]);
    }
    free(key_name);
    free(long_kv_value);
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("Thread asynv batch update opera thread is end. \n");
    return NULL;
}

void *kv_operater_async_batch_del_key_thread(void *arg)
{
    TEST_INFO("Thread asynv batch del opera thread is start. \n");
    int ret = 0;
    int i = *(int *)arg;
    GmcStmtT *g_stmt_async = NULL;
    GmcConnT *g_conn_async = NULL;
    void *kvtable = NULL;
    const int key_count = 26;
    const char *lastErrorStr = NULL;
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    char kvTableName[] = "peiyan_5";
    AsyncUserDataT data = {0};

    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    // open
    ret = GmcKvPrepareStmtByLabelName(g_stmt_async, kvTableName);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchT *batch;
    GmcBatchRetT *batchRet;

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    char **key_name = (char **)malloc(sizeof(char *) * key_count);
    for (int count = 0; count < key_count; count++) {
        key_name[count] = (char *)malloc(sizeof(char) * KV_TABLE_KEY_MAX_LENTH);
        (void)memset(key_name[count], 0, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH));
        (void)memset(key_name[count], 'A' + count, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH - 1));
        // 设置k-v值 key为 最大长度
        ret = GmcKvInputToStmt(g_stmt_async, key_name[count], strlen(key_name[count]), NULL, 0);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddKvDML(batch, g_stmt_async, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        TEST_INFO("Set kv is success. \n");
    }
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    printf("the del totalNum is %d.\n", data.totalNum);
    printf("the del successNum is %d.\n", data.succNum);
    // 查询插入的结果
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    for (int count = 0; count < key_count; count++) {
        free(key_name[count]);
    }
    free(key_name);
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("Thread asynv batch del opera thread is end. \n");
    return NULL;
}
/* ****************************************************************************
 Description  : 同步/异步 异常创建 kv 表有错误信息, 交互kv线程DML操作, key 并发交互异步批量接口
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_038)
{
    int32_t ret = 0;
    // 同步创建kv表 和 异步创建kv表
    char labelNameKv[MAX_NAME_LENGTH];
    const int key_count = 26;
    AsyncUserDataT data = {0};
    for (uint32_t i = 0; i < 50; i++) {
        sprintf(labelNameKv, "peiyan_%d", i);
        ret = GmcKvCreateTableAsync(g_stmt_async, labelNameKv, g_configJson, create_kv_table_callback, &data);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(GMERR_OK, data.status);
    }
    ret = GmcKvPrepareStmtByLabelName(g_stmt_async, "peiyan_5");
    EXPECT_EQ(GMERR_OK, ret);

    GmcKvTupleT kvInfo1 = {0};
    char long_kv_value[] = "zhangsan";

    char **key_name = (char **)malloc(sizeof(char *) * key_count);
    for (int count = 0; count < key_count; count++) {
        key_name[count] = (char *)malloc(sizeof(char) * KV_TABLE_KEY_MAX_LENTH);
        (void)memset(key_name[count], 0, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH));
        (void)memset(key_name[count], 'A' + count, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH - 1));
        // 设置k-v值 key为 最大长度
        kvInfo1.key = key_name[count];
        kvInfo1.keyLen = strlen(key_name[count]) + 1;
        kvInfo1.value = long_kv_value;
        kvInfo1.valueLen = strlen(long_kv_value) + 1;
        ret = GmcKvSetAsync(g_stmt_async, &kvInfo1, set_kv_callback, &data);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        TEST_INFO("Set  %d kv is success. \n", count);
    }

    pthread_t kvTabelOper[7];
    int index[7];
    void *thr_ret[7];

    for (int i = 0; i < 2; i++) {
        index[i] = i;
        ret = pthread_create(&kvTabelOper[i], NULL, kv_operater_async_batch_update_key_thread, &index[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 2; i < 4; i++) {
        index[i] = i;
        ret = pthread_create(&kvTabelOper[i], NULL, kv_operater_batch_read_key_thread, &index[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 4; i < 6; i++) {
        index[i] = i;
        ret = pthread_create(&kvTabelOper[i], NULL, kv_operater_async_batch_del_key_thread, &index[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 6; i < 7; i++) {
        index[i] = i;
        ret = pthread_create(&kvTabelOper[i], NULL, kv_operater_batch_create_kv_table_thread, &index[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < 7; i++) {
        pthread_join(kvTabelOper[i], &thr_ret[i]);
    }
    for (uint32_t i = 0; i < 50; i++) {
        sprintf(labelNameKv, "peiyan_%d", i);
        ret = GmcKvDropTableAsync(g_stmt_async, labelNameKv, drop_kv_table_callback, &data);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }
    for (int count = 0; count < key_count; count++) {
        free(key_name[count]);
    }
    free(key_name);
}

void *kv_operater_async_batch_create_kv_table_thread(void *arg)
{
    TEST_INFO("Thread asynv batch create kv table opera thread is end. \n");
    int ret = 0;
    int i = *(int *)arg;
    const int key_count = 26;
    GmcStmtT *g_stmt_async = NULL;
    GmcConnT *g_conn_async = NULL;
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    char kv_table_name[MAX_NAME_LENGTH];
    const char *lastErrorStr = NULL;
    AsyncUserDataT data = {0};
    GmcBatchT *batch;
    GmcBatchRetT *batchRet;

    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBatchPrepare(g_conn_async, NULL, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    int tablecount = 0;
    uint32_t tableNum = 0;
#ifndef RUN_INDEPENDENT
    ret = TestGetKvTableNum(&tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    tableNum = tableNum - 50;
#endif
    for (int i = 50; i < KV_MAX_CREARE_TABLE_COUNT - tableNum; i++) {
        sprintf(kv_table_name, "KvTabel_%d", i);
        ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_KV_TABLE, kv_table_name, NULL, NULL);
        if (ret != GMERR_OK) {
            EXPECT_EQ(GMERR_BATCH_BUFFER_FULL, ret);  // 第1025次添加操作时预期返回报错 11002
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            printf("[INFO] Add cmd labelName: %s, status is %d \n", kv_table_name, ret);
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
        tablecount++;
    }
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(tablecount, data.totalNum);
    EXPECT_EQ(tablecount, data.succNum);
    ret = GmcBatchReset(batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 50; i < tablecount + 50; i++) {
        sprintf(kv_table_name, "KvTabel_%d", i);
        ret = GmcBatchAddDDL(batch, GMC_OPERATION_DROP_KV_TABLE, kv_table_name, NULL, NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    lastErrorStr = GmcGetLastError();
    TEST_INFO("This lastError is %s.\n", lastErrorStr);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(tablecount, data.totalNum);
    EXPECT_EQ(tablecount, data.succNum);
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("Thread asynv batch create kv table opera thread is end. \n");
    return NULL;
}
/* ****************************************************************************
 Description  : 同步/异步 异常创建 kv 表有错误信息, 交互kv线程DML操作, key 并发交互异步批量接口和异步建表批量接口
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_039)
{
    int32_t ret = 0;
    // 同步创建kv表 和 异步创建kv表
    char labelNameKv[MAX_NAME_LENGTH];
    const int key_count = 26;
    AsyncUserDataT data = {0};
    for (uint32_t i = 0; i < 50; i++) {
        sprintf(labelNameKv, "peiyan_%d", i);
        ret = GmcKvCreateTableAsync(g_stmt_async, labelNameKv, g_configJson, create_kv_table_callback, &data);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }
    // 异步打开kv表
    ret = GmcKvPrepareStmtByLabelName(g_stmt_async, "peiyan_5");
    EXPECT_EQ(GMERR_OK, ret);
    GmcKvTupleT kvInfo1 = {0};
    char long_kv_value[] = "zhangsan";
    char **key_name = (char **)malloc(sizeof(char *) * key_count);
    for (int count = 0; count < key_count; count++) {
        key_name[count] = (char *)malloc(sizeof(char) * KV_TABLE_KEY_MAX_LENTH);
        (void)memset(key_name[count], 0, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH));
        (void)memset(key_name[count], 'A' + count, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH - 1));
        // 设置k-v值 key为 最大长度
        kvInfo1.key = key_name[count];
        kvInfo1.keyLen = strlen(key_name[count]) + 1;
        kvInfo1.value = long_kv_value;
        kvInfo1.valueLen = strlen(long_kv_value) + 1;
        ret = GmcKvSetAsync(g_stmt_async, &kvInfo1, set_kv_callback, &data);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        TEST_INFO("Set  %d kv is success. \n", count);
    }

    pthread_t kvTabelOper[7];
    int index[7];
    void *thr_ret[7];

    for (int i = 0; i < 2; i++) {
        index[i] = i;
        ret = pthread_create(&kvTabelOper[i], NULL, kv_operater_async_batch_update_key_thread, &index[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 2; i < 4; i++) {
        index[i] = i;
        ret = pthread_create(&kvTabelOper[i], NULL, kv_operater_batch_read_key_thread, &index[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 4; i < 6; i++) {
        index[i] = i;
        ret = pthread_create(&kvTabelOper[i], NULL, kv_operater_async_batch_del_key_thread, &index[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 6; i < 7; i++) {
        index[i] = i;
        ret = pthread_create(&kvTabelOper[i], NULL, kv_operater_async_batch_create_kv_table_thread, &index[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < 7; i++) {
        pthread_join(kvTabelOper[i], &thr_ret[i]);
    }
    for (uint32_t i = 0; i < 50; i++) {
        sprintf(labelNameKv, "peiyan_%d", i);
        ret = GmcKvDropTableAsync(g_stmt_async, labelNameKv, drop_kv_table_callback, &data);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }
    for (int count = 0; count < key_count; count++) {
        free(key_name[count]);
    }
    free(key_name);
}

/* ****************************************************************************
 Description  : 同步/异步 异常创建 kv 表有错误信息, 交互kv线程DML操作, 并发多线程交互异步批量接口和异步建表批量接口
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_040)
{
    int32_t ret = 0;
    // 同步创建kv表 和 异步创建kv表
    char labelNameKv[MAX_NAME_LENGTH];
    const int key_count = 26;
    AsyncUserDataT data = {0};
    for (uint32_t i = 0; i < 50; i++) {
        sprintf(labelNameKv, "peiyan_%d", i);
        ret = GmcKvCreateTableAsync(g_stmt_async, labelNameKv, g_configJson, create_kv_table_callback, &data);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }
    ret = GmcKvPrepareStmtByLabelName(g_stmt_async, "peiyan_5");
    EXPECT_EQ(GMERR_OK, ret);

    GmcKvTupleT kvInfo1 = {0};
    char long_kv_value[] = "zhangsan";

    char **key_name = (char **)malloc(sizeof(char *) * key_count);
    for (int count = 0; count < key_count; count++) {
        key_name[count] = (char *)malloc(sizeof(char) * KV_TABLE_KEY_MAX_LENTH);
        (void)memset(key_name[count], 0, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH));
        (void)memset(key_name[count], 'A' + count, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH - 1));
        // 设置k-v值 key为 最大长度
        kvInfo1.key = key_name[count];
        kvInfo1.keyLen = strlen(key_name[count]) + 1;
        kvInfo1.value = long_kv_value;
        kvInfo1.valueLen = strlen(long_kv_value) + 1;
        ret = GmcKvSetAsync(g_stmt_async, &kvInfo1, set_kv_callback, &data);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        TEST_INFO("Set  %d kv is success. \n", count);
    }

    pthread_t kvTabelOper[CONN_THREAD_100];
    int index[CONN_THREAD_100];
    void *thr_ret[CONN_THREAD_100];

    for (int i = 0; i < (CONN_THREAD_100 - 1) / 3; i++) {
        index[i] = i;
        ret = pthread_create(&kvTabelOper[i], NULL, kv_operater_async_batch_update_key_thread, &index[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = (CONN_THREAD_100 - 1) / 3; i < (CONN_THREAD_100 - 1) / 3 * 2; i++) {
        index[i] = i;
        ret = pthread_create(&kvTabelOper[i], NULL, kv_operater_batch_read_key_thread, &index[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = (CONN_THREAD_100 - 1) / 3 * 2; i < (CONN_THREAD_100 - 1) / 3 * 2 + 1; i++) {
        index[i] = i;
        ret = pthread_create(&kvTabelOper[i], NULL, kv_operater_async_batch_create_kv_table_thread, &index[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < (CONN_THREAD_100 - 1) / 3 * 2 + 1; i++) {
        pthread_join(kvTabelOper[i], &thr_ret[i]);
    }
    for (uint32_t i = 0; i < 50; i++) {
        sprintf(labelNameKv, "peiyan_%d", i);
        ret = GmcKvDropTableAsync(g_stmt_async, labelNameKv, drop_kv_table_callback, &data);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }
    for (int count = 0; count < key_count; count++) {
        free(key_name[count]);
    }
    free(key_name);
}

void *kv_operater_transaction_commit_thread(void *arg)
{
    TEST_INFO("Thread transaction opera thread is start. \n");
    int ret = 0;
    int i = *(int *)arg;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    const int key_count = 26;
    const char *lastErrorStr = NULL;
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    GmcTxConfigT MSTrxConfig;

    // 定义DS和MS的事务config类型
    MSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    MSTrxConfig.type = GMC_TX_ISOLATION_COMMITTED;
    MSTrxConfig.readOnly = false;

    ret = testGmcConnect(&conn, &stmt, 0, 0);  // 转需求SR.483046da  屏蔽重复失败用例 ,由036 037跟踪
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    // 获取kv
    // 启动MS事务
    int32_t cycle = 10;
    char **key_name = (char **)malloc(sizeof(char *) * key_count);
    ret == 1;
    while (true) {
        ret = GmcTransStart(conn, &MSTrxConfig);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcKvPrepareStmtByLabelName(stmt, "peiyan_5");
        EXPECT_EQ(GMERR_OK, ret);

        char *long_kv_value = (char *)malloc(sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH_30K));
        (void)memset(long_kv_value, 0, sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH_30K));
        (void)memset(long_kv_value, 'a', sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH_30K - 1));

        for (int count = 0; count < key_count; count++) {
            key_name[count] = (char *)malloc(sizeof(char) * KV_TABLE_KEY_MAX_LENTH);
            (void)memset(key_name[count], 0, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH));
            (void)memset(key_name[count], 'A' + count, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH - 1));
            // 设置k-v值 key为 最大长度
            ret = GmcKvInputToStmt(
                stmt, key_name[count], strlen(key_name[count]) + 1, long_kv_value, strlen(long_kv_value) + 1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcBatchAddKvDML(batch, stmt, GMC_OPERATION_INSERT);
            EXPECT_EQ(GMERR_OK, ret);
            TEST_INFO("Set kv is success. \n");
        }
        ret = GmcBatchExecute(batch, &batchRet);
        free(long_kv_value);
        if (!ret || cycle == 1) {
            break;
        }

        ret = GmcTransRollBack(conn);
        EXPECT_EQ(GMERR_OK, ret);
        cycle--;

        testGmcGetLastError();
    }
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(totalNum, (uint32_t)26);
    EXPECT_EQ(successNum, (uint32_t)26);
    // MS事务commit
    ret = GmcTransCommit(conn);
    EXPECT_EQ(GMERR_OK, ret);

    for (int count = 0; count < key_count; count++) {
        free(key_name[count]);
    }
    free(key_name);
    GmcBatchDestroy(batch);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("Thread transaction opera thread is end. \n");
    return NULL;
}
/* ****************************************************************************
 Description  : 同步/异步 交互kv线程DML操作, 满表交互kv 事务场景同表不同连接交互事务
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_041)
{
    int32_t ret = 0;
    // 同步创建kv表 和 异步创建kv表
    char labelNameKv[MAX_NAME_LENGTH];
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);
    const int key_count = 26;
    AsyncUserDataT data = {0};
    for (uint32_t i = 0; i < KV_MAX_CREARE_TABLE_COUNT; i++) {
        sprintf(labelNameKv, "peiyan_%d", i);
        ret = GmcKvCreateTableAsync(
            g_stmt_async, labelNameKv, gConFigisFastReadUncommitted, create_kv_table_callback, &data);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }
    ret = GmcKvPrepareStmtByLabelName(g_stmt_async, "peiyan_5");
    ASSERT_EQ(GMERR_OK, ret);

    GmcKvTupleT kvInfo1 = {0};
    char long_kv_value[] = "zhangsan";

    char **key_name = (char **)malloc(sizeof(char *) * key_count);
    for (int count = 0; count < key_count; count++) {
        key_name[count] = (char *)malloc(sizeof(char) * KV_TABLE_KEY_MAX_LENTH);
        (void)memset(key_name[count], 0, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH));
        (void)memset(key_name[count], 'A' + count, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH - 1));
        // 设置k-v值 key为 最大长度
        kvInfo1.key = key_name[count];
        kvInfo1.keyLen = strlen(key_name[count]) + 1;
        kvInfo1.value = long_kv_value;
        kvInfo1.valueLen = strlen(long_kv_value) + 1;
        ret = GmcKvSetAsync(g_stmt_async, &kvInfo1, set_kv_callback, &data);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        TEST_INFO("Set  %d kv is success. \n", count);
    }

    pthread_t kvTabelOper[CONN_THREAD_100];
    int index[CONN_THREAD_100];
    void *thr_ret[CONN_THREAD_100];
    for (int i = 0; i < CONN_THREAD_100 / 2; i++) {
        index[i] = i;
        ret = pthread_create(&kvTabelOper[i], NULL, kv_operater_transaction_commit_thread, &index[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = CONN_THREAD_100 / 2; i < CONN_THREAD_100; i++) {
        index[i] = i;
        ret = pthread_create(&kvTabelOper[i], NULL, kv_operater_batch_read_key_thread, &index[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < CONN_THREAD_100; i++) {
        pthread_join(kvTabelOper[i], &thr_ret[i]);
    }
    for (uint32_t i = 0; i < KV_MAX_CREARE_TABLE_COUNT; i++) {
        sprintf(labelNameKv, "peiyan_%d", i);
        ret = GmcKvDropTableAsync(g_stmt_async, labelNameKv, drop_kv_table_callback, &data);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }
    for (int count = 0; count < key_count; count++) {
        free(key_name[count]);
    }
    free(key_name);
}

void *kv_operater_transaction_rollback_thread(void *arg)
{
    TEST_INFO("Thread transaction opera thread is start. \n");
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *kvtable = NULL;
    const int key_count = 26;
    const char *lastErrorStr = NULL;
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    GmcTxConfigT MSTrxConfig;

    // 定义DS和MS的事务config类型
    MSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    MSTrxConfig.type = GMC_TX_ISOLATION_COMMITTED;
    MSTrxConfig.readOnly = false;

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 获取kv
    // 启动MS事务
    ret = GmcTransStart(conn, &MSTrxConfig);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(stmt, "peiyan_5");
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    char *long_kv_value = (char *)malloc(sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH_30K));
    (void)memset(long_kv_value, 0, sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH_30K));
    (void)memset(long_kv_value, 'a', sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH_30K - 1));

    char **key_name = (char **)malloc(sizeof(char *) * key_count);
    for (int count = 0; count < key_count; count++) {
        key_name[count] = (char *)malloc(sizeof(char) * KV_TABLE_KEY_MAX_LENTH);
        (void)memset(key_name[count], 0, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH));
        (void)memset(key_name[count], 'A' + count, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH - 1));
        // 设置k-v值 key为 最大长度
        ret = GmcKvInputToStmt(
            stmt, key_name[count], strlen(key_name[count]) + 1, long_kv_value, strlen(long_kv_value) + 1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddKvDML(batch, stmt, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        TEST_INFO("Set kv is success. \n");
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(totalNum, (uint32_t)26);
    EXPECT_EQ(successNum, (uint32_t)26);
    // 查询插入的结果
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // MS事务commit
    ret = GmcTransRollBack(conn);
    EXPECT_EQ(GMERR_OK, ret);

    for (int count = 0; count < key_count; count++) {
        free(key_name[count]);
    }
    free(key_name);
    free(long_kv_value);
    GmcBatchDestroy(batch);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("Thread transaction opera thread is end. \n");
    return NULL;
}
/* ****************************************************************************
 Description  : 同步/异步 异常创建 kv 表有错误信息, 交互kv线程DML操作, 满表交互kv 事务场景同表不同事务回滚
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_042)
{
    int32_t ret = 0;
    // 同步创建kv表 和 异步创建kv表
    char labelNameKv[MAX_NAME_LENGTH];
    const int key_count = 26;
    AsyncUserDataT data = {0};
    for (uint32_t i = 0; i < KV_MAX_CREARE_TABLE_COUNT; i++) {
        sprintf(labelNameKv, "peiyan_%d", i);
        ret = GmcKvCreateTableAsync(
            g_stmt_async, labelNameKv, gConFigisFastReadUncommitted, create_kv_table_callback, &data);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }
    ret = GmcKvPrepareStmtByLabelName(g_stmt_async, "peiyan_5");
    EXPECT_EQ(GMERR_OK, ret);

    GmcKvTupleT kvInfo1 = {0};
    char long_kv_value[] = "zhangsan";

    char **key_name = (char **)malloc(sizeof(char *) * key_count);
    for (int count = 0; count < key_count; count++) {
        key_name[count] = (char *)malloc(sizeof(char) * KV_TABLE_KEY_MAX_LENTH);
        (void)memset(key_name[count], 0, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH));
        (void)memset(key_name[count], 'A' + count, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH - 1));
        // 设置k-v值 key为 最大长度
        kvInfo1.key = key_name[count];
        kvInfo1.keyLen = strlen(key_name[count]) + 1;
        kvInfo1.value = long_kv_value;
        kvInfo1.valueLen = strlen(long_kv_value) + 1;
        ret = GmcKvSetAsync(g_stmt_async, &kvInfo1, set_kv_callback, &data);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        TEST_INFO("Set  %d kv is success. \n", count);
    }

    pthread_t kvTabelOper[CONN_THREAD_4];
    int index[CONN_THREAD_4];
    void *thr_ret[CONN_THREAD_4];
    for (int i = 0; i < CONN_THREAD_4 / 2; i++) {
        index[i] = i;
        ret = pthread_create(&kvTabelOper[i], NULL, kv_operater_transaction_rollback_thread, &index[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = CONN_THREAD_4 / 2; i < CONN_THREAD_4; i++) {
        index[i] = i;
        ret = pthread_create(&kvTabelOper[i], NULL, kv_operater_batch_read_key_thread, &index[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < CONN_THREAD_4; i++) {
        pthread_join(kvTabelOper[i], &thr_ret[i]);
    }
    for (uint32_t i = 0; i < KV_MAX_CREARE_TABLE_COUNT; i++) {
        sprintf(labelNameKv, "peiyan_%d", i);
        ret = GmcKvDropTableAsync(g_stmt_async, labelNameKv, drop_kv_table_callback, &data);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }
    for (int count = 0; count < key_count; count++) {
        free(key_name[count]);
    }
    free(key_name);
}

/* ****************************************************************************
 Description  : 同步/异步 异常创建 kv 表有错误信息, 交互kv线程DML操作, 满表交互kv 事务场景同一个连接不同的stmt操作事务
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_043)
{
    int32_t ret = 0;
    GmcTxConfigT MSTrxConfig;
    // 定义DS和MS的事务config类型
    MSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    MSTrxConfig.type = GMC_TX_ISOLATION_COMMITTED;
    MSTrxConfig.readOnly = false;
    // 悲观事务配置项
    MSTrxConfig.trxType = GMC_PESSIMISITIC_TRX;
    // 同步创建kv表 和 异步创建kv表
    char labelNameKv[MAX_NAME_LENGTH];
    const int key_count = 26;
    AsyncUserDataT data = {0};
    for (uint32_t i = 0; i < KV_MAX_CREARE_TABLE_COUNT; i++) {
        sprintf(labelNameKv, "peiyan_%d", i);
        ret = GmcKvCreateTableAsync(
            g_stmt_async, labelNameKv, gConFigisFastReadUncommitted, create_kv_table_callback, &data);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }
    GmcStmtT *stmt1;
    GmcStmtT *stmt2;
    GmcStmtT *stmt3;
    void *kvtable01 = NULL;
    void *kvtable02 = NULL;
    void *kvtable03 = NULL;

    ret = GmcAllocStmt(conn, &stmt1);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt2);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt3);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcKvPrepareStmtByLabelName(stmt1, kv_table_name_01);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(stmt2, kv_table_name_02);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(stmt3, kv_table_name_03);
    ASSERT_EQ(GMERR_OK, ret);
    // 启动MS事务
    ret = GmcTransStart(conn, &MSTrxConfig);
    ASSERT_EQ(GMERR_OK, ret);

    // insert
    GmcKvTupleT kvInfo = {0};
    char key[] = "KV_PK";
    uint32_t value = 1;
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key);
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(uint32_t);
    ret = GmcKvSet(stmt1, key, strlen(key), &value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcKvSet(stmt2, key, strlen(key), &value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcKvSet(stmt3, key, strlen(key), &value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    // MS事务commit
    ret = GmcTransCommit(conn);
    ASSERT_EQ(GMERR_OK, ret);

    // select
    uint32_t getvalue = 0;
    uint32_t outputLen = sizeof(uint32_t);
    ret = GmcKvGet(stmt1, key, strlen(key), &getvalue, &outputLen);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(1, getvalue);
    ASSERT_EQ(sizeof(uint32_t), outputLen);
    GmcFreeStmt(stmt1);
    GmcFreeStmt(stmt2);
    GmcFreeStmt(stmt3);
    // dorp KV table
    for (uint32_t i = 0; i < KV_MAX_CREARE_TABLE_COUNT; i++) {
        sprintf(labelNameKv, "peiyan_%d", i);
        ret = GmcKvDropTableAsync(g_stmt_async, labelNameKv, drop_kv_table_callback, &data);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }
}

void *T39_all_type_operater_transaction_thread(void *arg)
{
    TEST_INFO("Thread T39_all_type transaction opera thread is start. \n");
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    const char *lastErrorStr = NULL;
    GmcTxConfigT MSTrxConfig;

    // 定义DS和MS的事务config类型
    MSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    MSTrxConfig.type = GMC_TX_ISOLATION_COMMITTED;
    MSTrxConfig.readOnly = false;
    MSTrxConfig.trxType = GMC_PESSIMISITIC_TRX;

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 获取kv
    // 启动MS事务
    ret = GmcTransStart(conn, &MSTrxConfig);
    EXPECT_EQ(GMERR_OK, ret);
    // insert Vertex
    ret = TestInsertVertexByJson(stmt, "schema_file/data/T39_all_type.vertexdata");
    EXPECT_EQ(GMERR_OK, ret);

    // 查询插入的结果
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // MS事务commit
    ret = GmcTransCommit(conn);
    EXPECT_EQ(GMERR_OK, ret);
    // T39 hash扫描
    ret = func_T39_all_type_superfield_read(stmt, 1, 1);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("Thread T39_all_type transaction opera thread is end. \n");
    return NULL;
}
/* ****************************************************************************
 Description  : 同步/异步 异常创建 kv 表有错误信息, 交互kv线程DML操作, 满表交互kv 事务场景交互tree表事务
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_044)
{
    int32_t ret = 0;
    // 同步创建kv表 和 异步创建kv表
    char labelNameKv[MAX_NAME_LENGTH];
    const int key_count = 26;
    AsyncUserDataT data = {0};
    for (uint32_t i = 0; i < KV_MAX_CREARE_TABLE_COUNT; i++) {
        sprintf(labelNameKv, "peiyan_%d", i);
        ret = GmcKvCreateTableAsync(
            g_stmt_async, labelNameKv, gConFigisFastReadUncommitted, create_kv_table_callback, &data);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }
    ret = GmcKvPrepareStmtByLabelName(stmt, "peiyan_5");
    ASSERT_EQ(GMERR_OK, ret);
    ret = func_create_vertex_label_sync(
        (char *)"schema_file/T39_all_type.gmjson", stmt, gConFigisFastReadUncommitted, label_name03);
    ASSERT_EQ(GMERR_OK, ret);

    pthread_t kvTabelOper[3];
    int index[3];
    void *thr_ret[3];
    for (int i = 0; i < 1; i++) {
        index[i] = i;
        ret = pthread_create(&kvTabelOper[i], NULL, kv_operater_transaction_commit_thread, &index[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 1; i < 2; i++) {
        index[i] = i;
        ret = pthread_create(&kvTabelOper[i], NULL, kv_operater_batch_read_key_thread, &index[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 2; i < 3; i++) {
        index[i] = i;
        ret = pthread_create(&kvTabelOper[i], NULL, T39_all_type_operater_transaction_thread, &index[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < 3; i++) {
        pthread_join(kvTabelOper[i], &thr_ret[i]);
    }

    for (uint32_t i = 0; i < KV_MAX_CREARE_TABLE_COUNT; i++) {
        sprintf(labelNameKv, "peiyan_%d", i);
        ret = GmcKvDropTableAsync(g_stmt_async, labelNameKv, drop_kv_table_callback, &data);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }
}

void *kv_operater_transaction_random_commit_rollback_thread(void *arg)
{
    TEST_INFO("Thread transaction opera thread is start. \n");
    int ret = 0;
    int i = *(int *)arg;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    void *kvtable = NULL;
    const int key_count = 26;
    const char *lastErrorStr = NULL;
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    GmcTxConfigT MSTrxConfig;

    // 定义DS和MS的事务config类型
    MSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    MSTrxConfig.type = GMC_TX_ISOLATION_COMMITTED;
    MSTrxConfig.readOnly = false;

    ret = testGmcConnect(&conn, &stmt, 0, 0);  // 转需求SR.483046da  屏蔽重复失败用例 ,由036 037跟踪
    EXPECT_EQ(GMERR_OK, ret);
    // 获取kv
    // 启动MS事务
    ret = GmcTransStart(conn, &MSTrxConfig);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcKvPrepareStmtByLabelName(stmt, "peiyan_5");
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    char *long_kv_value = (char *)malloc(sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH_30K));
    (void)memset(long_kv_value, 0, sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH_30K));
    (void)memset(long_kv_value, 'a', sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH_30K - 1));

    char **key_name = (char **)malloc(sizeof(char *) * key_count);
    for (int count = 0; count < key_count; count++) {
        key_name[count] = (char *)malloc(sizeof(char) * KV_TABLE_KEY_MAX_LENTH);
        (void)memset(key_name[count], 0, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH));
        (void)memset(key_name[count], 'A' + count, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH - 1));
        // 设置k-v值 key为 最大长度
        ret = GmcKvInputToStmt(
            stmt, key_name[count], strlen(key_name[count]) + 1, long_kv_value, strlen(long_kv_value) + 1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddKvDML(batch, stmt, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        TEST_INFO("Set kv is success. \n");
    }
    ret = GmcBatchExecute(batch, &batchRet);
    if (ret == GMERR_LOCK_NOT_AVAILABLE)
        goto threadend;
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(totalNum, (uint32_t)26);
    EXPECT_EQ(successNum, (uint32_t)26);
    // MS事务commit或者rollback
    if (i % 2) {
        ret = GmcTransCommit(conn);
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        ret = GmcTransRollBack(conn);
        EXPECT_EQ(GMERR_OK, ret);
    }

threadend:
    for (int count = 0; count < key_count; count++) {
        free(key_name[count]);
    }
    free(key_name);
    free(long_kv_value);
    GmcBatchDestroy(batch);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("Thread transaction opera thread is end. \n");
    return NULL;
}
/* ****************************************************************************
 Description  : 同步/异步 异常创建 kv 表有错误信息, 交互kv线程DML操作, 满表交互kv 事务场景并发 同表不同事务
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_045)  // 此处用例copy 041用例,只是加了事务的回滚, 30705错误
{
    int32_t ret = 0;
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);
    // 同步创建kv表 和 异步创建kv表
    char labelNameKv[MAX_NAME_LENGTH];
    const int key_count = 26;
    AsyncUserDataT data = {0};
    for (uint32_t i = 0; i < KV_MAX_CREARE_TABLE_COUNT; i++) {
        sprintf(labelNameKv, "peiyan_%d", i);
        ret = GmcKvCreateTableAsync(
            g_stmt_async, labelNameKv, gConFigisFastReadUncommitted, create_kv_table_callback, &data);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }
    ret = GmcKvPrepareStmtByLabelName(g_stmt_async, "peiyan_5");
    EXPECT_EQ(GMERR_OK, ret);

    GmcKvTupleT kvInfo1 = {0};
    char long_kv_value[] = "zhangsan";

    char **key_name = (char **)malloc(sizeof(char *) * key_count);
    for (int count = 0; count < key_count; count++) {
        key_name[count] = (char *)malloc(sizeof(char) * KV_TABLE_KEY_MAX_LENTH);
        (void)memset(key_name[count], 0, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH));
        (void)memset(key_name[count], 'A' + count, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH - 1));
        // 设置k-v值 key为 最大长度
        kvInfo1.key = key_name[count];
        kvInfo1.keyLen = strlen(key_name[count]) + 1;
        kvInfo1.value = long_kv_value;
        kvInfo1.valueLen = strlen(long_kv_value) + 1;
        ret = GmcKvSetAsync(g_stmt_async, &kvInfo1, set_kv_callback, &data);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        TEST_INFO("Set  %d kv is success. \n", count);
    }

    pthread_t kvTabelOper[RECORD_COUNT_100];
    int index[RECORD_COUNT_100];
    void *thr_ret[RECORD_COUNT_100];
    for (int i = 0; i < RECORD_COUNT_100 / 2; i++) {
        index[i] = i;
        ret = pthread_create(&kvTabelOper[i], NULL, kv_operater_transaction_random_commit_rollback_thread, &index[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = RECORD_COUNT_100 / 2; i < RECORD_COUNT_100; i++) {
        index[i] = i;
        ret = pthread_create(&kvTabelOper[i], NULL, kv_operater_batch_read_key_thread, &index[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < RECORD_COUNT_100; i++) {
        pthread_join(kvTabelOper[i], &thr_ret[i]);
    }
    for (uint32_t i = 0; i < KV_MAX_CREARE_TABLE_COUNT; i++) {
        sprintf(labelNameKv, "peiyan_%d", i);
        ret = GmcKvDropTableAsync(g_stmt_async, labelNameKv, drop_kv_table_callback, &data);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }
    for (int count = 0; count < key_count; count++) {
        free(key_name[count]);
    }
    free(key_name);
}

void *mulit_kv_operater_transaction_random_commit_rollback_thread(void *arg)
{
    TEST_INFO("Thread transaction opera thread is start. \n");
    int ret = 0;
    int i = *(int *)arg;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    char labelNameKv[15];
    void *kvtable = NULL;
    const int key_count = 26;
    const char *lastErrorStr = NULL;
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    GmcTxConfigT MSTrxConfig;

    sprintf(labelNameKv, "peiyan_%d", i);
    // 定义DS和MS的事务config类型
    MSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    MSTrxConfig.type = GMC_TX_ISOLATION_COMMITTED;
    MSTrxConfig.readOnly = false;

    ret = testGmcConnect(&conn, &stmt, 0, 0);  // 转需求SR.483046da  屏蔽重复失败用例 ,由036 037跟踪
    EXPECT_EQ(GMERR_OK, ret);
    // 获取kv
    // 启动MS事务
    ret = GmcTransStart(conn, &MSTrxConfig);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(stmt, labelNameKv);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    char *long_kv_value = (char *)malloc(sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH_30K));
    (void)memset(long_kv_value, 0, sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH_30K));
    (void)memset(long_kv_value, 'a', sizeof(char) * (KV_TABLE_VALUE_MAX_LENTH_30K - 1));

    char **key_name = (char **)malloc(sizeof(char *) * key_count);
    for (int count = 0; count < key_count; count++) {
        key_name[count] = (char *)malloc(sizeof(char) * KV_TABLE_KEY_MAX_LENTH);
        (void)memset(key_name[count], 0, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH));
        (void)memset(key_name[count], 'A' + count, sizeof(char) * (KV_TABLE_KEY_MAX_LENTH - 1));
        // 设置k-v值 key为 最大长度
        ret = GmcKvInputToStmt(
            stmt, key_name[count], strlen(key_name[count]) + 1, long_kv_value, strlen(long_kv_value) + 1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddKvDML(batch, stmt, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        TEST_INFO("Set kv is success. \n");
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(totalNum, (uint32_t)26);
    EXPECT_EQ(successNum, (uint32_t)26);
    // MS事务commit或者rollback
    if (i % 2) {
        ret = GmcTransCommit(conn);
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        ret = GmcTransRollBack(conn);
        EXPECT_EQ(GMERR_OK, ret);
    }

    for (int count = 0; count < key_count; count++) {
        free(key_name[count]);
    }
    free(key_name);
    free(long_kv_value);
    GmcBatchDestroy(batch);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("Thread transaction opera thread is end. \n");
    return NULL;
}
/* ****************************************************************************
 Description  : 同步/异步 异常创建 kv 表有错误信息, 交互kv线程DML操作, 满表交互kv 事务场景并发回滚, 不同表不同事务
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_046)
{
    int32_t ret = 0;
    // 同步创建kv表 和 异步创建kv表
    char labelNameKv[MAX_NAME_LENGTH];
    const int key_count = 26;
    AsyncUserDataT data = {0};
    for (uint32_t i = 0; i < KV_MAX_CREARE_TABLE_COUNT; i++) {
        sprintf(labelNameKv, "peiyan_%d", i);
        ret = GmcKvCreateTableAsync(
            g_stmt_async, labelNameKv, gConFigisFastReadUncommitted, create_kv_table_callback, &data);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }

    pthread_t kvTabelOper[RECORD_COUNT_100];
    int index[RECORD_COUNT_100];
    void *thr_ret[RECORD_COUNT_100];
    for (int i = 0; i < RECORD_COUNT_100; i++) {
        index[i] = i;
        ret = pthread_create(
            &kvTabelOper[i], NULL, mulit_kv_operater_transaction_random_commit_rollback_thread, &index[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < RECORD_COUNT_100; i++) {
        pthread_join(kvTabelOper[i], &thr_ret[i]);
    }

    for (uint32_t i = 0; i < KV_MAX_CREARE_TABLE_COUNT; i++) {
        sprintf(labelNameKv, "peiyan_%d", i);
        ret = GmcKvDropTableAsync(g_stmt_async, labelNameKv, drop_kv_table_callback, &data);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }
}

/* ****************************************************************************
 Description  : 同步/异步 异常创建 kv 表有错误信息, 交互kv线程DML操作, 满表交互kv 订阅场景并发kv
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_047)
{
    int32_t ret = 0;
}

/* ****************************************************************************
 Description  : 同步/异步 异常创建 kv 表有错误信息, 交互kv线程DML操作, 满表交互kv 订阅不同事件并发推送
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_048)
{}

/* ****************************************************************************
 Description  : 新增hashcluster索引, 创建Label(同步/异步),对索引符合要求的Label创建
                再创建边,全部为定长字段,不含有bitmap类型
 Modification :
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_049)
{
    int32_t ret = 0;
    GmcDropVertexLabel(stmt, label_name01);
    // 异步创建符合条件的label信息
    ret = func_create_vertex_label_async(
        (char *)"schema_file/complex_typical.gmjson", g_stmt_async, gConFigisFastReadUncommitted);
    ASSERT_EQ(GMERR_OK, ret);
    ret = func_create_vertex_label_async(
        (char *)"schema_file/ip4forward_fixed_field.gmjson", g_stmt_async, gConFigisFastReadUncommitted);
    ASSERT_EQ(GMERR_OK, ret);
    // 再同步创建Label
    ret = func_create_vertex_label_sync((char *)"schema_file/ip4forward_fixed_field.gmjson", stmt,
        gConFigisFastReadUncommitted, (char *)"ip4forward_fixed_field");
    ASSERT_EQ(GMERR_OK, ret);
    readJanssonFile((char *)"./schema_file/edge_insert_schema.gmjson", &edge_schema01);
    ASSERT_NE((void *)NULL, edge_schema01);
    ret = GmcCreateEdgeLabel(stmt, edge_schema01, gConFigisFastReadUncommitted);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropEdgeLabel(stmt, "OP_T0_ip4forward");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, "ip4forward_fixed_field");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(edge_schema01);
}

/* ****************************************************************************
 Description  : 新增hashcluster索引, 创建Label(异步),对索引符合要求的Label创建, 再创建边
 Modification :
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_050)
{
    int32_t ret = 0;
    const char *labelName = "ip4forward_hashcluster_string";
    // 同步/异步创建hashcluster 索引含string 的label信息
    ret = func_create_vertex_label_sync((char *)"schema_file/ip4forward_hashcluster_string.gmjson", stmt, g_configJson,
        (char *)"ip4forward_hashcluster_string");
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = func_create_vertex_label_async(
        (char *)"schema_file/ip4forward_hashcluster_string.gmjson", g_stmt_async, g_configJson, GMERR_OK);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ASSERT_EQ(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  : 新增hashcluster索引, schema定义hashCluster个数大于16个(同步/异步操作)
 Modification :
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_051)
{
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_PROGRAM_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int32_t ret = 0;
    ret = func_create_vertex_label_sync((char *)"schema_file/ip4forward_hashcluster_17.gmjson", stmt, g_configJson,
        (char *)"ip4forward_hashcluster_17");
    ASSERT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = func_create_vertex_label_async((char *)"schema_file/ip4forward_hashcluster_17.gmjson", g_stmt_async,
        g_configJson, (int32_t)GMERR_PROGRAM_LIMIT_EXCEEDED);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ASSERT_EQ(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  : 新增hashcluster索引, schema定义 hashCluster unique属性为false(同步/异步操作)
 Modification :
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_052)
{
    int32_t ret = 0;
    ret = func_create_vertex_label_sync(
        (char *)"schema_file/ip4forward.gmjson", stmt, g_configJson, (char *)"ip4forward");
    ASSERT_EQ(GMERR_OK, ret);
    GmcDropVertexLabel(stmt, label_name02);
    ret = func_create_vertex_label_async((char *)"schema_file/ip4forward.gmjson", g_stmt_async, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  : 新增hashcluster索引, schema定义 hashCluster unique属性为 true (同步/异步操作)
 Modification : 目前支持unique 写true 21/7/23
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_053)
{
    int32_t ret = 0;
    ret = func_create_vertex_label_sync(
        (char *)"schema_file/ip4forward_unique_true.gmjson", stmt, g_configJson, (char *)"ip4forward_unique_true");
    ASSERT_EQ(GMERR_OK, ret);
    GmcDropVertexLabel(stmt, (char *)"ip4forward_unique_true");
    ret =
        func_create_vertex_label_async((char *)"schema_file/ip4forward_unique_true.gmjson", g_stmt_async, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    GmcDropVertexLabel(stmt, (char *)"ip4forward_unique_true");
}

/* ****************************************************************************
 Description  : 新增hashcluster索引, schema定义hashCluster在数组节点上(同步/异步操作)和索引248长度看护
 Modification :
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_054)
{
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_PROGRAM_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int32_t ret = 0;
    ret = func_create_vertex_label_sync((char *)"schema_file/TreeModel_hashcluster_arrayNode.gmjson", stmt,
        g_configJson, (char *)"TreeModel_hashcluster_arrayNode");
    ASSERT_EQ(GMERR_INVALID_PROPERTY, ret);
    // 异步会返回成功, 后面结果会返回71001错误码
    ret = func_create_vertex_label_async((char *)"schema_file/TreeModel_hashcluster_arrayNode.gmjson", g_stmt_async,
        g_configJson, (int32_t)GMERR_INVALID_PROPERTY);
    ASSERT_EQ(GMERR_OK, ret);
    const char *lastErrorStr = NULL;
    lastErrorStr = GmcGetLastError();
    if (!strstr(lastErrorStr, "Not normal property. property name is A0.\n"
                              " parse index hashcluster_arrayNode in vertex label TreeModel_hashcluster_arrayNode, "
                              "which has incorrect property A0 of the root node.")) {
        printf("the lastErrorStr is %s\n", lastErrorStr);
        ASSERT_EQ(GMERR_OK, -1);
    }
    lastErrorStr = GmcGetLastError();
    if (!strstr(lastErrorStr, "Not normal property. property name is A0.\n"
                              " parse index hashcluster_arrayNode in vertex label TreeModel_hashcluster_arrayNode, "
                              "which has incorrect property A0 of the root node.")) {
        printf("the lastErrorStr is %s\n", lastErrorStr);
        ASSERT_EQ(GMERR_OK, -1);
    }
    ret = func_create_vertex_label_async((char *)"schema_file/ip4forward_eight_field_gt_4096.gmjson", g_stmt_async,
        g_configJson, GMERR_PROGRAM_LIMIT_EXCEEDED);
    ASSERT_EQ(GMERR_OK, ret);
    lastErrorStr = GmcGetLastError();
    if (!strstr(lastErrorStr, "Not normal property. property name is A0.\n"
                              " parse index hashcluster_arrayNode in vertex label TreeModel_hashcluster_arrayNode, "
                              "which has incorrect property A0 of the root node.")) {
        printf("the lastErrorStr is %s\n", lastErrorStr);
        ASSERT_EQ(GMERR_OK, -1);
    }
}
/* ****************************************************************************
 Description  : 新增hashcluster索引, schema定义hashCluster在数组节点上(同步/异步操作)
 Modification :
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_055)
{
    int32_t ret = 0;
    ret = func_create_vertex_label_sync((char *)"schema_file/TreeModel_hashcluster_notRootNode.gmjson", stmt,
        g_configJson, (char *)"TreeModel_hashcluster_notRootNode");
    ASSERT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // 异步会返回成功, 后面结果会返回71001错误码
    ret = func_create_vertex_label_async((char *)"schema_file/TreeModel_hashcluster_notRootNode.gmjson", g_stmt_async,
        g_configJson, (int32_t)GMERR_INVALID_PROPERTY);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  : 新增hashcluster索引, 非唯一hashCluster更新，冲突量大于4k场景
 Modification :
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_056)
{
    int32_t ret = 0;
    ret = test_insert_vertex_ip4forward(stmt, 0, RECORD_COUNT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = test_read_ip4forward_by_pk(stmt, "primary_key", 0, RECORD_COUNT, GMC_DATATYPE_UINT32);
    ASSERT_EQ(GMERR_OK, ret);
    ret = func_hashcluster_ip4forward_update(conn, RECORD_COUNT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = test_read_ip4forward_by_pk(stmt, "primary_key", 0, RECORD_COUNT, GMC_DATATYPE_UINT32, 2222, '2');
    ASSERT_EQ(GMERR_OK, ret);
    ret = func_loacalhash_scan_ip4forward(conn, RECORD_COUNT);
    ASSERT_EQ(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  : 新增hashcluster索引, 非唯一hashCluster删除，冲突量大于4k场景, 主键读
 Modification :
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_057)
{
    int32_t ret = 0;
    ret = test_insert_vertex_ip4forward(stmt, 0, RECORD_COUNT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = test_read_ip4forward_by_pk(stmt, "primary_key", 0, RECORD_COUNT, GMC_DATATYPE_UINT32);
    ASSERT_EQ(GMERR_OK, ret);
    ret = func_hashcluster_ip4forward_del(conn, RECORD_COUNT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = test_read_ip4forward_by_pk(stmt, "primary_key", 0, RECORD_COUNT, GMC_DATATYPE_UINT32, 2222, '2');
    ASSERT_EQ(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  : 新增hashcluster索引, 非唯一hashCluster 批量更新场景
 Author       : pwx623912
 Modification :
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_058)
{
    int32_t ret = 0;
    ret = test_insert_vertex_ip4forward(stmt, 0, RECORD_COUNT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = test_read_ip4forward_by_pk(stmt, "primary_key", 0, RECORD_COUNT, GMC_DATATYPE_UINT32);
    ASSERT_EQ(GMERR_OK, ret);
    ret = func_hashcluster_ip4forward_update(conn, 0, 1);  // 批量操作, 影响的条数是0
    ASSERT_EQ(GMERR_OK, ret);
    ret = test_read_ip4forward_by_pk(stmt, "primary_key", 0, RECORD_COUNT, GMC_DATATYPE_UINT32, 2222, '2');
    ASSERT_EQ(GMERR_OK, ret);
    ret = func_loacalhash_scan_ip4forward(conn, RECORD_COUNT);
    ASSERT_EQ(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  : 新增hashcluster索引, 非唯一hashCluster 批量删除场景
 Modification :
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_059)
{
    int32_t ret = 0;
    ret = test_insert_vertex_ip4forward(stmt, 0, RECORD_COUNT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = test_read_ip4forward_by_pk(stmt, "primary_key", 0, RECORD_COUNT, GMC_DATATYPE_UINT32);
    ASSERT_EQ(GMERR_OK, ret);
    ret = func_hashcluster_ip4forward_del(conn, 0, 1);  // 批量操作, 影响的条数是0
    ASSERT_EQ(GMERR_OK, ret);
    ret = test_read_ip4forward_by_pk(stmt, "primary_key", 0, RECORD_COUNT, GMC_DATATYPE_UINT32, 2222, '2');
    ASSERT_EQ(GMERR_OK, ret);
}

void sn_callback_simple(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret;
    int index, i;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;
    void *label = 0;
    void *keyValue = 0;
    GmcConnT *conn_sync = 0;
    GmcStmtT *stmt_sync = 0;
    char keyName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }

        for (i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(strlen(labelName), labelNameLen);

            // 默认推送new object和old object
            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[g_subIndex];
                    printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_INSERT new_value is %d\r\n", index);

                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_NO_DATA, ret);
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_NO_DATA, ret);

                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->old_value)[g_subIndex];
                    printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_DELETE old_value is %d\r\n", index);
                    break;
                }
                case GMC_SUB_EVENT_UPDATE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[g_subIndex];
                    printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_UPDATE new_value is %d\r\n", index);

                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->old_value)[g_subIndex];
                    printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_UPDATE old_value is %d\r\n", index);
                    break;
                }
                case GMC_SUB_EVENT_REPLACE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[g_subIndex];
                    printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_REPLACE new_value is %d\r\n", index);

                    // 读old
                    if (((bool *)user_data->isReplace_insert)[g_subIndex]) {
                        printf("[NEW OBJECT] GMC_SUB_EVENT_REPLACE insert\r\n");
                        ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                        EXPECT_EQ(GMERR_OK, ret);
                        char *pValue = (char *)malloc(sizeof(int32_t));
                        bool isNull = 0;
                        ret = GmcGetVertexPropertyByName(subStmt, "F6", pValue, sizeof(int32_t), &isNull);
                        EXPECT_EQ(GMERR_OK, ret);
                        EXPECT_EQ(1, isNull);
                        free(pValue);
                    } else {
                        printf("[NEW OBJECT] GMC_SUB_EVENT_REPLACE update\r\n");
                        ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                        EXPECT_EQ(GMERR_OK, ret);
                        index = ((int *)user_data->old_value)[g_subIndex];
                    }
                    break;
                }
                default: {
                    printf("default: Incorrect eventType\r\n");
                    break;
                }
            }
            break;
        }
        g_subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
        }
    }
}
/* ****************************************************************************
 Description  : 新增hashcluster索引, 非唯一hashCluster 更新触发条件订阅推送
 Modification :
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_060)
{
    int32_t ret = 0;
    const char *g_subName = "subVertexLabel";
    int chanRingLen = 256;

    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subName, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = func_create_sub_relation(stmt, g_conn_sub,
        (char *)"./schema_file/ip4forward_subinfo_insert_update_delete_or_field.gmjson", sn_callback_simple, user_data,
        g_subName);
    ASSERT_EQ(GMERR_OK, ret);

    for (int i = 0; i < RECORD_COUNT / 100; i++) {
        ((int *)(user_data->new_value))[i] = i;
        ret = test_insert_vertex_ip4forward(stmt, i, i + 1);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 1);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = func_hashcluster_ip4forward_update(conn, RECORD_COUNT / 100);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_UPDATE, RECORD_COUNT / 100);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, g_subName);
    ASSERT_EQ(GMERR_OK, ret);
    // 释放订阅连接
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 新增hashcluster索引, 非唯一hashCluster 删除触发条件订阅推送
 Modification :
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_061)
{
    int32_t ret = 0;
    const char *g_subName = "subVertexLabel";
    int chanRingLen = 256;
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subName, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);

    ret = func_create_sub_relation(stmt, g_conn_sub,
        (char *)"./schema_file/ip4forward_subinfo_insert_update_delete_or_field.gmjson", sn_callback_simple, user_data,
        g_subName);
    ASSERT_EQ(GMERR_OK, ret);

    ret = test_insert_vertex_ip4forward(stmt, 0, RECORD_COUNT / 100);
    ASSERT_EQ(GMERR_OK, ret);

    ret = func_hashcluster_ip4forward_update(conn, RECORD_COUNT / 100);
    ASSERT_EQ(GMERR_OK, ret);
    ret = func_hashcluster_ip4forward_del(conn, RECORD_COUNT / 100, 0, 2222);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, RECORD_COUNT / 100);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_UPDATE, RECORD_COUNT / 100);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, RECORD_COUNT / 100);
    EXPECT_EQ(GMERR_OK, ret);

    ret = test_read_ip4forward_by_pk(stmt, "primary_key", 0, RECORD_COUNT, GMC_DATATYPE_UINT32, 2222, '2');
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, g_subName);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 新增hashcluster索引, 非唯一hashCluster 更新触发全表订阅推送
 Modification :
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_062)
{
    int32_t ret = 0;
    const char *g_subName_all = "subVertexLabel_union";
    int chanRingLen = 256;
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subName_all, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = func_create_sub_relation(stmt, g_conn_sub, (char *)"./schema_file/all_type_ip4forward_subinfo.gmjson",
        sn_callback_simple, user_data, g_subName_all);
    ASSERT_EQ(GMERR_OK, ret);

    ret = test_insert_vertex_ip4forward(stmt, 0, RECORD_COUNT / 100);
    ASSERT_EQ(GMERR_OK, ret);
    ret = func_hashcluster_ip4forward_update(conn, RECORD_COUNT / 100);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, RECORD_COUNT / 100);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_UPDATE, RECORD_COUNT / 100);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, g_subName_all);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 新增hashcluster索引, 非唯一hashCluster 删除触发全表订阅推送
 Modification :
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_063)
{
    int32_t ret = 0;
    const char *g_subName = "subVertexLabel_union";
    int chanRingLen = 256;
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subName, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);

    ret = func_create_sub_relation(stmt, g_conn_sub, (char *)"./schema_file/all_type_ip4forward_subinfo.gmjson",
        sn_callback_simple, user_data, g_subName);
    ASSERT_EQ(GMERR_OK, ret);
    ret = test_insert_vertex_ip4forward(stmt, 0, RECORD_COUNT / 100);
    ASSERT_EQ(GMERR_OK, ret);
    ret = func_hashcluster_ip4forward_del(conn, RECORD_COUNT / 100);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, RECORD_COUNT / 100);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, RECORD_COUNT / 100);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, g_subName);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 新增hashcluster索引, 非唯一hashCluster 与local localhash交互
                hashCluster非唯一索引更新后，local key 区间扫描、部分区间删除，localhash全表扫描
 Modification :
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_064)
{
    int32_t ret = 0;
    const char *g_subName = "subVertexLabel_union";
    int chanRingLen = 256;
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subName, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = func_create_sub_relation(stmt, g_conn_sub, (char *)"./schema_file/all_type_ip4forward_subinfo.gmjson",
        sn_callback_simple, user_data, g_subName);
    ASSERT_EQ(GMERR_OK, ret);
    ret = test_insert_vertex_ip4forward(stmt, 0, SUB_RECORD_COUNT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = func_hashcluster_ip4forward_update(conn, SUB_RECORD_COUNT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = func_localkey_ip4forward_range_del(conn, 0, SUB_RECORD_COUNT / 2, SUB_RECORD_COUNT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = func_localkey_ip4forward_range_scan(conn, 0, SUB_RECORD_COUNT * 2, SUB_RECORD_COUNT / 2 - 1);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, SUB_RECORD_COUNT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_UPDATE, SUB_RECORD_COUNT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, SUB_RECORD_COUNT / 2 + 1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, g_subName);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 新增hashcluster索引, 非唯一hashCluster 异步更新
 Modification :
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_065)
{
    int32_t ret = 0;
    const char *g_subName_all = "subVertexLabel_union";
    int chanRingLen = 256;
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subName_all, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = func_create_sub_relation(stmt, g_conn_sub, (char *)"./schema_file/all_type_ip4forward_subinfo.gmjson",
        sn_callback_simple, user_data, g_subName_all);
    ASSERT_EQ(GMERR_OK, ret);

    ret = test_insert_vertex_ip4forward(stmt, 0, SUB_RECORD_COUNT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, SUB_RECORD_COUNT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = func_hashcluster_ip4forward_update_async(g_stmt_async, SUB_RECORD_COUNT);
    ASSERT_EQ(GMERR_OK, ret);
    for (int start_id = 0; start_id < SUB_RECORD_COUNT; start_id++) {
        ((int *)(user_data->new_value))[SUB_RECORD_COUNT + start_id] = start_id;
        ((int *)(user_data->old_value))[SUB_RECORD_COUNT + start_id] = start_id;
    }
    ret = func_ip4forward_hashcluster_scan(conn, SUB_RECORD_COUNT, 2222);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_UPDATE, SUB_RECORD_COUNT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, g_subName_all);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 新增hashcluster索引, 非唯一hashCluster 异步删除
 Modification :
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_066)
{
    int32_t ret = 0;
    const char *g_subName = "subVertexLabel_union";
    int chanRingLen = 256;
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subName, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = func_create_sub_relation(stmt, g_conn_sub, (char *)"./schema_file/all_type_ip4forward_subinfo.gmjson",
        sn_callback_simple, user_data, g_subName);
    ASSERT_EQ(GMERR_OK, ret);

    ret = test_insert_vertex_ip4forward(stmt, 0, SUB_RECORD_COUNT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = func_hashcluster_ip4forward_del_async(g_stmt_async, SUB_RECORD_COUNT);
    ASSERT_EQ(GMERR_OK, ret);
    for (int start_id = 0; start_id < SUB_RECORD_COUNT; start_id++) {
        ((int *)(user_data->old_value))[SUB_RECORD_COUNT + start_id] = start_id;
    }
    ret = func_ip4forward_hashcluster_scan(conn, 0, 1111);
    ASSERT_EQ(GMERR_OK, ret);
    ret = test_read_ip4forward_by_pk(stmt, "primary_key", 0, RECORD_COUNT, GMC_DATATYPE_UINT32);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, SUB_RECORD_COUNT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, SUB_RECORD_COUNT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, g_subName);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 新增hashcluster索引, 并发场景:多线程并发hashcluster索引更新
 Modification :
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_067)
{
    int32_t ret = 0;
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    const char *g_subName_all = "subVertexLabel_union";
    int chanRingLen = 256;
    uint32_t CurentconnNum = 0;
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subName_all, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGetConnNum(&CurentconnNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = func_create_sub_relation(stmt, g_conn_sub, (char *)"./schema_file/all_type_ip4forward_subinfo.gmjson",
        sn_callback_simple, user_data, g_subName_all);
    ASSERT_EQ(GMERR_OK, ret);
    pthread_t thread_arr[THRAD_NUM];
    void *thread_ret[THRAD_NUM] = {0};
    int index[THRAD_NUM] = {0};
    int32_t threadNums = (MAX_CONN_SIZE - CurentconnNum) > THRAD_NUM ? THRAD_NUM : (MAX_CONN_SIZE - CurentconnNum);
    for (int i = 0; i < threadNums; i++) {
        index[i] = i;
        pthread_create(&thread_arr[i], NULL, thread_nonUniHashUpdate_test, (void *)&index[i]);
    }
    for (int i = 0; i < threadNums; i++) {
        pthread_join(thread_arr[i], &thread_ret[i]);
    }
    ret = GmcUnSubscribe(stmt, g_subName_all);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    pthread_mutex_destroy(&LockSubChannel);
}

/* ****************************************************************************
 Description  : V3 随机挑取一般复杂表typical_schema 用例整理为 V5测试用例
 Modification :
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_068)
{
    int32_t ret = 0;
    ret = func_create_vertex_label_sync((char *)"schema_file/typical_schema.gmjson", stmt, g_configJson, label_name06);
    ASSERT_EQ(GMERR_OK, ret);
    ret = test_insert_vertex_typical_schema(stmt, 1, 0, (char *)"fixed", 0, 2, 5, 5, label_name06);
    ASSERT_EQ(GMERR_OK, ret);
    ret = test_read_typical_schema_by_pk(conn, "primary_key", 0, 2, 0);
    ASSERT_EQ(GMERR_OK, ret);
    GmcDropVertexLabel(stmt, label_name06);
}
/* ****************************************************************************
 Description  : 实现json对象的写入 写入数据, 导出数据经过处理,由于不支持覆盖写, 删除再导入
 Modification :
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_069)
{
    int32_t ret = 0;
    ret = test_insert_vertex_ip4forward(stmt, 0, 1);
    ASSERT_EQ(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmexport -s %s -c vdata -t %s -ns %s", g_toolPath, g_connServer, label_name02,
        g_testNameSpace);
    printf("%s\n", g_command);

    ret = executeCommand(g_command, "[DONE]", "export_vdata", "successfully");
    ASSERT_EQ(GMERR_OK, ret);

    system("sed -i 's/[][]//' ip4forward.gmdata");
    ret = func_hashcluster_ip4forward_del_async(g_stmt_async, 1);
    ASSERT_EQ(GMERR_OK, ret);
    // insert Vertex by json
    json_t *data_json;
    json_error_t data_json_error;
    data_json = json_load_file("./ip4forward.gmdata", 0, &data_json_error);
    char *jStr = json_dumps(data_json, JSON_INDENT(0));
    ret = GmcSetVertexByJson(stmt, GMC_JSON_REJECT_DUPLICATES, jStr);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    free(jStr);

    ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);
    json_decref(data_json);
    ret = test_read_ip4forward_by_pk(stmt, "primary_key", 0, 1, GMC_DATATYPE_UINT32);
    ASSERT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 实现json对象的写入 测试用例补充 不支持直接覆盖写
 Modification :
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_070)
{
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_PRIMARY_KEY_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int32_t ret = 0;
    ret = test_insert_vertex_ip4forward(stmt, 0, 1);
    ASSERT_EQ(GMERR_OK, ret);
    ret = test_insert_vertex_ip4forward(stmt, 0, 1);
    ASSERT_EQ(GMERR_PRIMARY_KEY_VIOLATION, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 实现json对象的写数据, 分别进行主键扫/全表扫/索引扫
 Modification :
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_071)
{
    int32_t ret = 0;
    // insert Vertex
    ret = TestInsertVertexByJson(stmt, "schema_file/data/T39_all_type.vertexdata");
    ASSERT_EQ(GMERR_OK, ret);
    // T39 主键扫描
    ret = func_T39_all_type_pk_scan(stmt, 1, 1);
    ASSERT_EQ(GMERR_OK, ret);
    // T39 全表扫描
    ret = func_T39_all_type_all_scan(stmt, 1, 1);
    ASSERT_EQ(GMERR_OK, ret);
    // T39 hash扫描
    ret = func_T39_all_type_localhash_scan(stmt, 1, 1);
    ASSERT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 实现json对象的写数据, 开启事务, 进行操作扫描读
 Modification :
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_072)
{
    int32_t ret = 0;
    ret = func_create_vertex_label_sync(
        (char *)"schema_file/T39_all_type.gmjson", stmt, gConFigisFastReadUncommitted, label_name03);
    ASSERT_EQ(GMERR_OK, ret);
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;   // 客户端发送消息给S, c端直连内存两种
    config.type = GMC_TX_ISOLATION_COMMITTED;  // 事务的隔离级别,读已提交
    config.readOnly = false;
    config.trxType = GMC_PESSIMISITIC_TRX;  // 只读事务
    ret = GmcTransStart(conn, &config);     // 开启事务
    ASSERT_EQ(GMERR_OK, ret);
    // insert Vertex
    ret = TestInsertVertexByJson(stmt, "schema_file/data/T39_all_type.vertexdata");
    ASSERT_EQ(GMERR_OK, ret);
    // // T39 hash扫描
    ret = func_T39_all_type_localhash_scan(stmt, 1, 1);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcTransCommit(conn);  // 提交事务
    ASSERT_EQ(GMERR_OK, ret);
    // T39 hash扫描
    ret = func_T39_all_type_localhash_scan(stmt, 1, 1);
    ASSERT_EQ(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  : 实现json对象的写数据, 开启事务, 进行 superfield 读
 Modification :
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_073)
{
    int32_t ret = 0;
    ret = func_create_vertex_label_sync(
        (char *)"schema_file/T39_all_type.gmjson", stmt, gConFigisFastReadUncommitted, label_name03);
    ASSERT_EQ(GMERR_OK, ret);
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;   // 客户端发送消息给S, c端直连内存两种
    config.type = GMC_TX_ISOLATION_COMMITTED;  // 事务的隔离级别,读已提交
    config.readOnly = false;                   // 只读事务
    config.trxType = GMC_PESSIMISITIC_TRX;
    ret = GmcTransStart(conn, &config);  // 开启事务
    ASSERT_EQ(GMERR_OK, ret);
    // insert Vertex
    ret = TestInsertVertexByJson(stmt, "schema_file/data/T39_all_type.vertexdata");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcTransCommit(conn);  // 提交事务
    ASSERT_EQ(GMERR_OK, ret);
    // T39 hash扫描
    ret = func_T39_all_type_superfield_read(stmt, 1, 1);
    ASSERT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 实现json对象的写数据, 开启事务, 进行 merge 顶点(存在则修改,不存在则创建)
 Modification :
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_074)
{
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_INVALID_OBJECT);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int32_t ret = 0;
    bool isFinish = true;
    // insert Vertex
    ret = TestInsertVertexByJson(stmt, "schema_file/data/T39_all_type.vertexdata");
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, label_name03, GMC_OPERATION_MERGE);
    ASSERT_EQ(GMERR_OK, ret);
    // merge vertex
    uint32_t F7Value = 1;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &F7Value, sizeof(F7Value));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "T39_K0");
    ASSERT_EQ(GMERR_OK, ret);
    // 测试再次对merge的主键赋值是无效的赋值, 接口返回成功, 实际的数据还是1, 未发生变化
    // 2023.3.22 当前merge不再支持设置主键值
    F7Value = 99;
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT32, &F7Value, sizeof(F7Value));
    ASSERT_EQ(GMERR_INVALID_OBJECT, ret);
    uint8_t F3Value = 20;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &F3Value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 2);
    EXPECT_EQ(GMERR_OK, ret);

    // merge 第二条数据新主键
    ret = testGmcPrepareStmtByLabelName(stmt, label_name03, GMC_OPERATION_MERGE);
    ASSERT_EQ(GMERR_OK, ret);
    F7Value = 20;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &F7Value, sizeof(F7Value));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "T39_K0");
    ASSERT_EQ(GMERR_OK, ret);
    F3Value = 20;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &F3Value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
    int64_t F9Value = 21;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &F9Value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);

    // merge 第三条数据 覆盖写 第一条
    ret = testGmcPrepareStmtByLabelName(stmt, label_name03, GMC_OPERATION_MERGE);
    ASSERT_EQ(GMERR_OK, ret);
    F7Value = 1;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &F7Value, sizeof(F7Value));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "T39_K0");
    ASSERT_EQ(GMERR_OK, ret);
    F3Value = 20;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &F3Value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 2);
    EXPECT_EQ(GMERR_OK, ret);

    // 主键读 F3 字段 为 20
    ret = testGmcPrepareStmtByLabelName(stmt, label_name03, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &F7Value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "T39_K0");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_UINT8, &F3Value);
    ASSERT_EQ(GMERR_OK, ret);
    // free
    GmcFreeIndexKey(stmt);
}

/* ****************************************************************************
 Description  : 实现json对象的写数据, 进行 replace 顶点(存在删除后创建,不存在则创建)
 Modification :
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_075)
{
    int32_t ret = 0;
    bool isFinish = true;
    // insert Vertex
    ret = TestInsertVertexByJson(stmt, "schema_file/data/T39_all_type.vertexdata");
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, label_name03, GMC_OPERATION_REPLACE);
    ASSERT_EQ(GMERR_OK, ret);
    // replace vertex
    uint32_t F7Value = 1;
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT32, &F7Value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint8_t F3Value = 20;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &F3Value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 2);
    EXPECT_EQ(GMERR_OK, ret);
    // replace 第二条数据新主键
    ret = testGmcPrepareStmtByLabelName(stmt, label_name03, GMC_OPERATION_REPLACE);
    ASSERT_EQ(GMERR_OK, ret);
    F7Value = 20;
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT32, &F7Value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    F3Value = 20;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &F3Value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
    int64_t F9Value = 21;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &F9Value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcResetVertex(stmt, false);
    ASSERT_EQ(GMERR_OK, ret);
    // replace 第三条数据 覆盖写 第一条
    ret = testGmcPrepareStmtByLabelName(stmt, label_name03, GMC_OPERATION_REPLACE);
    ASSERT_EQ(GMERR_OK, ret);
    F7Value = 1;
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT32, &F7Value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    F3Value = 20;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &F3Value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
    // F9Value = 22;
    // ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &F9Value, sizeof(int64_t));
    // ASSERT_EQ(GMERR_OK,ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);  // indexKey支持null值 SR.IREQ02589772.016
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 0);
    // EXPECT_EQ(GMERR_OK, ret); 理解不应该继承F9的值, 如果放开812行对F9赋值,没问题
    // ASSERT_EQ(GMERR_OK,ret);
    // 主键读 F3 字段 为 20
    ret = testGmcPrepareStmtByLabelName(stmt, label_name03, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &F7Value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "T39_K0");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_UINT8, &F3Value);
    ASSERT_EQ(GMERR_OK, ret);
    // free
    GmcFreeIndexKey(stmt);
}

/* ****************************************************************************
 Description  : 实现json对象的写数据, json格式文件转换 GmcDumpVertexToJson
 Modification :
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_076)
{
    int32_t ret = 0;
    // insert Vertex
    ret = TestInsertVertexByJson(stmt, "schema_file/data/T39_all_type.vertexdata");
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, label_name03, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t pk = 1;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "T39_K0");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);  // 必须是读接口, 不然json数据读不出来

    char *outJson = NULL;
    ret = GmcDumpVertexToJson(stmt, GMC_JSON_EXPORT_NULL_INFO | GMC_JSON_INDENT(0), &outJson);
    ASSERT_EQ(GMERR_OK, ret);
    printf("%s\n", outJson);
    GmcFreeJsonStr(stmt, outJson);
    GmcFreeIndexKey(stmt);
}

/* ****************************************************************************
 Description  : 实现json对象的写数据, 过滤查询
 Modification :
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_077)
{
    int32_t ret = 0;
    // insert Vertex
    ret = TestInsertVertexByJson(stmt, "schema_file/data/T39_all_type.vertexdata");
    ASSERT_EQ(GMERR_OK, ret);
    const char *filter_content = "F2<5";
    ret = T39_all_type_filter_query(stmt, filter_content, 1);
    ASSERT_EQ(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  : 实现json对象的写数据, 订阅交互导入数据
 Modification :
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_078)
{
    int32_t ret = 0;
    int chanRingLen = 256;
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &chanRingLen);
    ASSERT_EQ(GMERR_OK, ret);
    ret = func_create_sub_relation(
        stmt, g_conn_sub, (char *)"./schema_file/NormalSubinfo.gmjson", sn_callback_simple, user_data, g_subConnName);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name03, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    json_t *data_json;
    json_error_t data_json_error;
    data_json = json_load_file("schema_file/data/T39_all_type_10.vertexdata", 0, &data_json_error);
    size_t array_size = json_array_size(data_json);

    for (int i = 0; i < g_data_num; i++) {
        json_t *data_json_item = json_array_get(data_json, i);
        char *jStr = json_dumps(data_json_item, JSON_INDENT(0));
        ret = GmcSetVertexByJson(stmt, GMC_JSON_REJECT_DUPLICATES, jStr);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        free(jStr);
        ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);
    }
    json_decref(data_json);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 10);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, g_subConnName);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    ASSERT_EQ(GMERR_OK, ret);
}


/* ****************************************************************************
 Description  : 同步/异步 异常创建 kv 表有错误信息时, 调用获取错误信息的接口正常 表名长度较大时
 Modification :
**************************************************************************** */
TEST_F(GetErrorInfoCreateKvTable, DML_047_082)
{
    int32_t ret = 0;
    const char *lastErrorStr = NULL;
    AsyncUserDataT data = {0};
    // 同步创建kv表 和 异步创建kv表
    char *long_kv_table = (char *)malloc(sizeof(char) * (1024 * 1024 + 2));
    (void)memset(long_kv_table, 0, sizeof(char) * (1024 * 1024 + 2));
    (void)memset(long_kv_table, 'G', sizeof(char) * (1024 * 1024 + 1));
    ret = GmcKvCreateTable(stmt, long_kv_table, g_configJson);
    ASSERT_EQ(GMERR_INVALID_VALUE, ret);
    ret = testGmcGetLastError("Not normal value. kvTableName length exceeds the limit 512.");
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcKvDropTable(stmt, long_kv_table);
    ASSERT_EQ(GMERR_INVALID_VALUE, ret);
    ret = testGmcGetLastError("Not normal value. kvTableName length exceeds the limit 512.");
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcKvCreateTableAsync(g_stmt_async, long_kv_table, g_configJson, create_kv_table_callback, &data);
    ASSERT_EQ(GMERR_INVALID_VALUE, ret);
    ret = testGmcGetLastError("Not normal value. kvTableName length exceeds the limit 512.");
    EXPECT_EQ(GMERR_OK, ret);
    free(long_kv_table);
    long_kv_table = NULL;
}
