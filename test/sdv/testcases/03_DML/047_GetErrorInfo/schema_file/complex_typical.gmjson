[{"type": "record", "name": "OP_T0", "fields": [{"name": "F0", "type": "int64", "nullable": false}, {"name": "F1", "type": "uint64", "nullable": true}, {"name": "F2", "type": "int32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "int16", "nullable": true}, {"name": "F5", "type": "uint16", "nullable": true}, {"name": "F6", "type": "int8", "nullable": true}, {"name": "F7", "type": "partition", "nullable": false}, {"name": "F8", "type": "boolean", "nullable": true}, {"name": "F9", "type": "float", "nullable": true}, {"name": "F10", "type": "double", "nullable": true}, {"name": "F11", "type": "time", "nullable": true}, {"name": "F12", "type": "char", "nullable": true}, {"name": "F13", "type": "uchar", "nullable": true}, {"name": "F14", "type": "string", "size": 100, "nullable": true}, {"name": "F15", "type": "bytes", "size": 7, "nullable": true}, {"name": "F16", "type": "fixed", "size": 7, "nullable": true}, {"name": "T1", "type": "record", "fields": [{"name": "P0", "type": "int64", "nullable": true}, {"name": "P1", "type": "uint64", "nullable": true}, {"name": "P2", "type": "int32", "nullable": true}, {"name": "P3", "type": "uint32", "nullable": true}, {"name": "P4", "type": "int16", "nullable": true}, {"name": "P5", "type": "uint16", "nullable": true}, {"name": "P6", "type": "int8", "nullable": true}, {"name": "P7", "type": "uint8", "nullable": true}, {"name": "P8", "type": "boolean", "nullable": true}, {"name": "P9", "type": "float", "nullable": true}, {"name": "P10", "type": "double", "nullable": true}, {"name": "P11", "type": "time", "nullable": true}, {"name": "P12", "type": "char", "nullable": true}, {"name": "P13", "type": "uchar", "nullable": true}, {"name": "P14", "type": "string", "size": 100, "nullable": true}, {"name": "P15", "type": "bytes", "size": 7, "nullable": true}, {"name": "P16", "type": "fixed", "size": 7, "nullable": true}, {"name": "T2", "type": "record", "array": true, "size": 3, "fields": [{"name": "A0", "type": "int64", "nullable": true}, {"name": "A1", "type": "uint64", "nullable": true}, {"name": "A2", "type": "int32", "nullable": true}, {"name": "A3", "type": "uint32", "nullable": true}, {"name": "A4", "type": "int16", "nullable": true}, {"name": "A5", "type": "uint16", "nullable": true}, {"name": "A6", "type": "int8", "nullable": true}, {"name": "A7", "type": "uint8", "nullable": true}, {"name": "A8", "type": "boolean", "nullable": true}, {"name": "A9", "type": "float", "nullable": true}, {"name": "A10", "type": "double", "nullable": true}, {"name": "A11", "type": "time", "nullable": true}, {"name": "A12", "type": "char", "nullable": true}, {"name": "A13", "type": "uchar", "nullable": true}, {"name": "A14", "type": "string", "size": 100, "nullable": true}, {"name": "A15", "type": "bytes", "size": 7, "nullable": true}, {"name": "A16", "type": "fixed", "size": 7, "nullable": true}]}]}, {"name": "T3", "type": "record", "vector": true, "size": 3, "fields": [{"name": "V0", "type": "int64", "nullable": true}, {"name": "V1", "type": "uint64", "nullable": true}, {"name": "V2", "type": "int32", "nullable": true}, {"name": "V3", "type": "uint32", "nullable": true}, {"name": "V4", "type": "int16", "nullable": true}, {"name": "V5", "type": "uint16", "nullable": true}, {"name": "V6", "type": "int8", "nullable": true}, {"name": "V7", "type": "uint8", "nullable": true}, {"name": "V8", "type": "boolean", "nullable": true}, {"name": "V9", "type": "float", "nullable": true}, {"name": "V10", "type": "double", "nullable": true}, {"name": "V11", "type": "time", "nullable": true}, {"name": "V12", "type": "char", "nullable": true}, {"name": "V13", "type": "uchar", "nullable": true}, {"name": "V14", "type": "string", "size": 100, "nullable": true}, {"name": "V15", "type": "bytes", "size": 7, "nullable": true}, {"name": "V16", "type": "fixed", "size": 7, "nullable": true}]}], "keys": [{"node": "OP_T0", "name": "OP_PK", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "OP_T0", "name": "localhash_key", "index": {"type": "<PERSON><PERSON><PERSON>"}, "fields": ["F0"], "constraints": {"unique": true}}, {"node": "OP_T0", "name": "hashcluster_key", "index": {"type": "hashcluster"}, "fields": ["F3", "F5"], "constraints": {"unique": false}}]}]