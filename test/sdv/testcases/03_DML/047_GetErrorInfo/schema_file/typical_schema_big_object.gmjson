[{"version": "2.0", "type": "record", "name": "T0", "fields": [{"name": "F1", "type": "fixed", "size": 5}, {"name": "F2", "type": "int16"}, {"name": "F3", "type": "float"}, {"name": "F4", "type": "double"}, {"name": "F5", "type": "boolean"}, {"name": "F6", "type": "time"}, {"name": "F7", "type": "uint16"}, {"name": "F8", "type": "int32"}, {"name": "F9", "type": "uint32"}, {"name": "F10", "type": "int64"}, {"name": "F11", "type": "uint64"}, {"name": "F12", "type": "int32"}, {"name": "F13", "type": "int64"}, {"name": "F14", "type": "fixed", "size": 5}, {"name": "F15", "type": "bytes", "size": 6}, {"name": "F16", "type": "string", "size": 7, "nullable": true}, {"name": "F17", "type": "partition", "nullable": false}, {"name": "F18", "type": "string"}, {"name": "F19", "type": "string"}, {"name": "F20", "type": "string"}, {"name": "F21", "type": "string"}, {"name": "T1", "type": "record", "fixed_array": true, "size": 5, "fields": [{"name": "P1", "type": "int32"}, {"name": "T2", "type": "record", "fixed_array": true, "size": 5, "fields": [{"name": "A1", "type": "int8"}, {"name": "A2", "type": "uint8"}, {"name": "A3", "type": "int16"}, {"name": "A4", "type": "uint16"}, {"name": "A5", "type": "int32"}, {"name": "A6", "type": "uint32"}, {"name": "A7", "type": "int64"}, {"name": "A8", "type": "uint64"}, {"name": "A9", "type": "int32"}, {"name": "A10", "type": "int64"}, {"name": "A11", "type": "float"}, {"name": "A12", "type": "double"}, {"name": "A13", "type": "boolean"}, {"name": "A14", "type": "time"}, {"name": "A15", "type": "string", "size": 7, "nullable": true}, {"name": "A16", "type": "bytes", "size": 6}, {"name": "A17", "type": "fixed", "size": 5}]}]}, {"name": "T3", "type": "record", "vector": true, "size": 5, "fields": [{"name": "V1", "type": "int8"}, {"name": "V2", "type": "uint8"}, {"name": "V3", "type": "int16"}, {"name": "V4", "type": "uint16"}, {"name": "V5", "type": "int32"}, {"name": "V6", "type": "uint32"}, {"name": "V7", "type": "int64"}, {"name": "V8", "type": "uint64"}, {"name": "V9", "type": "int32"}, {"name": "V10", "type": "int64"}, {"name": "V11", "type": "float"}, {"name": "V12", "type": "double"}, {"name": "V13", "type": "boolean"}, {"name": "V14", "type": "time"}, {"name": "V15", "type": "fixed", "size": 5}, {"name": "V16", "type": "string", "size": 7, "nullable": true}, {"name": "V17", "type": "string", "size": 7, "nullable": true}, {"name": "V18", "type": "string", "size": 7, "nullable": true}, {"name": "V19", "type": "string", "size": 7, "nullable": true}]}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "T0", "fields": ["F1", "F2"], "constraints": {"unique": true}}, {"name": "T1_member_key", "node": "T1", "fields": ["P1"], "constraints": {"unique": false}}, {"name": "vector_member_key", "node": "T3", "fields": ["V6"], "constraints": {"unique": false}}, {"name": "localhash_key", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "T0", "fields": ["F14"], "constraints": {"unique": false}}, {"name": "local_key", "index": {"type": "local"}, "node": "T0", "fields": ["F9"], "constraints": {"unique": false}}]}]