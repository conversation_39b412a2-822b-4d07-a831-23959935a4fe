extern "C" {
}

#include "gtest/gtest.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <pthread.h>
#include <semaphore.h>
#include <errno.h>
#include "t_datacom_lite.h"

/*****************************************************************************
 Description  : GmcCloseKvTable接口测试
 Notes        :
 History      :
 Author       : madongdong m00502160
 Created	  : 2021.03.22
 Modification :
*****************************************************************************/

GmcConnT *conn;
GmcStmtT *stmt;
int ret;
char g_tableName[128] = "KV8";
char g_configJson[128] = "{\"max_record_count\":10}";

class GmcCloseKvTable_interface_test : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();
    static void SetUpTestCase()
    {

        // 重启server
        system("sh $TEST_HOME/tools/start.sh");

        int res = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        ASSERT_EQ(GMERR_OK, res);
    };

    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
    };
};

void GmcCloseKvTable_interface_test::SetUp()
{
    printf("\n======================TEST:BEGIN======================\n");

    // 创建客户端连接
    int ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // 创建KvTable
    ret = GmcKvCreateTable(stmt, g_tableName, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN();

    char errorMsg2[128] = {};
    (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_INVALID_PARAMETER_VALUE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg2);
}

void GmcCloseKvTable_interface_test::TearDown()
{
    AW_CHECK_LOG_END();
    printf("\n======================TEST:END========================\n");
    // 删除KvTable
    ret = GmcKvDropTable(stmt, g_tableName);
    ASSERT_EQ(GMERR_OK, ret);

    // 关闭 client connection
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

#if 0
// 001.正常场景关闭kvTable
TEST_F(GmcCloseKvTable_interface_test, DML_033_008_GmcCloseKvTable_interface_test_001)
{

	// 打开kv表 
	void* kvtable = NULL;
	ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
	EXPECT_EQ(GMERR_OK, ret);

	// 关闭 kvtable 
	//ret = GmcCloseKvTable(stmt);
	//EXPECT_EQ(GMERR_OK, ret);
	
}

// 002.KvCloseKvTable接口入参stmt指针为null；
TEST_F(GmcCloseKvTable_interface_test, DML_033_008_GmcCloseKvTable_interface_test_002)
{
	
	// 查询 &kvtable(反序列化后的地址) 
	void* kvtable = NULL;
	ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
	EXPECT_EQ(GMERR_OK, ret);

	// 关闭 KvCloseKvTable接口入参stmt指针为null,失败 
	ret = GmcCloseKvTable(NULL);
	EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
	ret = testGmcGetLastError(NULL);
	EXPECT_EQ(GMERR_OK, ret);
	// 关闭 kvtable 
	//ret = GmcCloseKvTable(stmt);
	//EXPECT_EQ(GMERR_OK, ret);	
}
#if 0
//2021.07.19 kv接口整改 相应的参数已删除、无需验证、用例下架
// 003.KvCloseKvTable接口入参KvTable句柄为NULL
TEST_F(GmcCloseKvTable_interface_test, DML_033_008_GmcCloseKvTable_interface_test_003)
{
	// 查询 &kvtable(反序列化后的地址) 
	void* kvtable = NULL;
	ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
	EXPECT_EQ(GMERR_OK, ret);
	//KvCloseKvTable接口入参KvTable句柄为NULL
	ret = GmcCloseKvTable(stmt);
	EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
	ret = testGmcGetLastError(NULL);
	EXPECT_EQ(GMERR_OK, ret);
	// 关闭 kvtable 
	//ret = GmcCloseKvTable(stmt);
	//EXPECT_EQ(GMERR_OK, ret);	
}	

// 004.未使用GmcOpenKvTable打开，直接CloseKvTable
TEST_F(GmcCloseKvTable_interface_test, DML_033_008_GmcCloseKvTable_interface_test_004)
{
	void* kvtable = NULL;
	// 直接关闭 kvtable 
	ret = GmcCloseKvTable(stmt);
	EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
	ret = testGmcGetLastError(NULL);
	EXPECT_EQ(GMERR_OK, ret);
}
#endif
// 005.重复关闭同一个KvTable
TEST_F(GmcCloseKvTable_interface_test, DML_033_008_GmcCloseKvTable_interface_test_005)
{
	// 打开kv表 
	void* kvtable = NULL;
	ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
	EXPECT_EQ(GMERR_OK, ret);

	// 关闭 kvtable 
	//ret = GmcCloseKvTable(stmt);
	//EXPECT_EQ(GMERR_OK, ret);
	// 重复关闭 kvtable 
	//ret = GmcCloseKvTable(stmt);
	//EXPECT_EQ(GMERR_OK, ret);	
}
#endif
