/*****************************************************************************
 Description  : GmcKvSet接口测试
 Notes        :
 History      :
 Author       : madongdong m00502160
 Modification :
 Date         : 2021/03/27
*****************************************************************************/
extern "C" {
}

#include "gtest/gtest.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <pthread.h>
#include <semaphore.h>
#include <errno.h>
#include "t_datacom_lite.h"

int ret = 0;
GmcConnT *conn;
GmcStmtT *stmt;
char g_tableName[128] = "KV3";
char g_configJson[128] = "{\"max_record_count\":100}";
class GmcKvSetInt : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void GmcKvSetInt::SetUpTestCase()
{
    // 重启server
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    ASSERT_EQ(GMERR_OK, ret);
}

void GmcKvSetInt::TearDownTestCase()
{
    int ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    testEnvClean();
}

void GmcKvSetInt::SetUp()
{

    // 创建客户端连接
    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    //创建kv表
    ret = GmcKvCreateTable(stmt, g_tableName, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    void *kvtable = NULL;
    AW_CHECK_LOG_BEGIN();

    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    char errorMsg2[128] = {};
    (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_INVALID_VALUE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg2);

    char errorMsg3[128] = {};
    (void)snprintf(errorMsg3, sizeof(errorMsg3), "GMERR-%d", GMERR_WRONG_STMT_OBJECT);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg3);

    char errorMsg4[128] = {};
    (void)snprintf(errorMsg4, sizeof(errorMsg4), "GMERR-%d", GMERR_INVALID_PARAMETER_VALUE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg4);
}
void GmcKvSetInt::TearDown()
{
    AW_CHECK_LOG_END();
    // drop kv
    ret = GmcKvDropTable(stmt, g_tableName);
    ASSERT_EQ(GMERR_OK, ret);
    // 关闭 client connection
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 001.调用接口GmcKvSet正常插入一个key-value对
TEST_F(GmcKvSetInt, DML_033_003_GmcKvSetInt_001)
{
    void *kvtable = NULL;
    //获取kv
    ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
    ASSERT_EQ(GMERR_OK, ret);
    GmcKvTupleT kvInfo = {0};
    int32_t value = 100;
    char key[32] = "zhangsan";
    //设置k-v值
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key);
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(int32_t);
    ret = GmcKvSet(stmt, key, strlen(key), &value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    //设置kv值
    GmcKvTupleT kvInfo1 = {0};
    int8_t value1 = 10;
    char key1[64] = "wangwu";
    //设置k-v值
    kvInfo1.key = key1;
    kvInfo1.keyLen = strlen(key1);
    kvInfo1.value = &value1;
    kvInfo1.valueLen = sizeof(int8_t);
    ret = GmcKvSet(stmt, key1, strlen(key1), &value1, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);
    //查询插入的结果
    uint32_t count = 0;
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(2, count);
    // GmcKvGetValueSize接口校验
    uint32_t valueSize = 0;
    ret = GmcKvGetValueSize(stmt, key1, strlen(key1), &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(sizeof(int8_t), valueSize);
    // 异常参数
    ret = GmcKvGetValueSize(NULL, key1, strlen(key1), &valueSize);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = GmcKvGetValueSize(stmt, NULL, strlen(key1), &valueSize);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = GmcKvGetValueSize(stmt, key1, (uint32_t)0, &valueSize);
    EXPECT_EQ(GMERR_INVALID_VALUE, ret);
    ret = GmcKvGetValueSize(stmt, key1, (uint32_t)NULL, &valueSize);
    EXPECT_EQ(GMERR_INVALID_VALUE, ret);
}

// 002.GmcKvSet接口入参stmt为NULL
TEST_F(GmcKvSetInt, DML_033_003_GmcKvSetInt_002)
{
    void *kvtable = NULL;
    //获取kv
    ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
    ASSERT_EQ(GMERR_OK, ret);
    GmcKvTupleT kvInfo = {0};
    int32_t value = 100;
    char key[32] = "zhangsan";
    //设置k-v值
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key);
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(int32_t);
    //设置GmcKvSet接口入参stmt为NULL
    ret = GmcKvSet(NULL, key, strlen(key), &value, sizeof(int32_t));
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    //关闭kv表table
    // ret = GmcCloseKvTable(stmt);
    // ASSERT_EQ(GMERR_OK,ret);
}

// 003.GmcKvSet接口入参kvTuple指针为NULL
TEST_F(GmcKvSetInt, DML_033_003_GmcKvSetInt_003)
{
    void *kvtable = NULL;
    //获取kv
    ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
    ASSERT_EQ(GMERR_OK, ret);
    GmcKvTupleT kvInfo = {0};
    int32_t value = 100;
    char key[32] = "zhangsan";
    //设置k-v值
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key);
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(int32_t);
    //设置GmcKvSet接口入参kvTuple指针为NULL
    ret = GmcKvSet(stmt, NULL, strlen(key), NULL, sizeof(int32_t));
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    //关闭kv表
    // ret = GmcCloseKvTable(stmt);
    // ASSERT_EQ(GMERR_OK,ret);
}

// 004.GmcKvSet接口入参key为NULL
TEST_F(GmcKvSetInt, DML_033_003_GmcKvSetInt_004)
{
    void *kvtable = NULL;
    //获取kv
    ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
    ASSERT_EQ(GMERR_OK, ret);
    GmcKvTupleT kvInfo2 = {0};
    int32_t value2 = 100;
    char key2[] = "zhangsan";
    //设置k-v值
    kvInfo2.key = NULL;
    kvInfo2.keyLen = strlen(key2);
    kvInfo2.value = &value2;
    kvInfo2.valueLen = sizeof(int32_t);
    ret = GmcKvSet(stmt, NULL, strlen(key2), &value2, sizeof(int32_t));
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    //关闭kv表
    // ret = GmcCloseKvTable(stmt);
    // ASSERT_EQ(GMERR_OK,ret);
}
// 005.GmcKvSet接口入参keylen为NULL
TEST_F(GmcKvSetInt, DML_033_003_GmcKvSetInt_005)
{
    void *kvtable = NULL;
    //获取kv
    ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
    ASSERT_EQ(GMERR_OK, ret);
    GmcKvTupleT kvInfo = {0};
    int32_t value = 100;
    char key[] = "zhangsan";
    //设置k-v值
    kvInfo.key = key;
    kvInfo.keyLen = (unsigned int)NULL;
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(int32_t);
    ret = GmcKvSet(stmt, key, (unsigned int)NULL, &value, sizeof(int32_t));
    ASSERT_EQ(GMERR_INVALID_VALUE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    //关闭kv表
    // ret = GmcCloseKvTable(stmt);
    // ASSERT_EQ(GMERR_OK,ret);
}
// 006.GmcKvSet接口入参value的指针为NULL
TEST_F(GmcKvSetInt, DML_033_003_GmcKvSetInt_006)
{
    void *kvtable = NULL;
    //获取kv
    ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
    ASSERT_EQ(GMERR_OK, ret);
    GmcKvTupleT kvInfo = {0};
    int32_t value = 100;
    char key[32] = "zhangsan";
    //设置k-v值
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key);
    kvInfo.value = NULL;
    kvInfo.valueLen = sizeof(int32_t);
    ret = GmcKvSet(stmt, key, strlen(key), NULL, sizeof(int32_t));
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    //关闭kv表
    // ret = GmcCloseKvTable(stmt);
    // ASSERT_EQ(GMERR_OK,ret);
}

// 007.GmcKvSet接口入参valuelen为NULL
TEST_F(GmcKvSetInt, DML_033_003_GmcKvSetInt_007)
{
    void *kvtable = NULL;
    //获取kv
    ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
    ASSERT_EQ(GMERR_OK, ret);
    GmcKvTupleT kvInfo = {0};
    int32_t value = 100;
    char key[32] = "zhangsan";
    //设置k-v值
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key);
    kvInfo.value = &value;
    kvInfo.valueLen = (unsigned int)NULL;
    ret = GmcKvSet(stmt, key, strlen(key), &value, (unsigned int)NULL);
    ASSERT_EQ(GMERR_INVALID_VALUE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    //关闭kv表
    // ret = GmcCloseKvTable(stmt);
    // ASSERT_EQ(GMERR_OK,ret);
}

// 008.GmcKvSet重复设置相同的k值
TEST_F(GmcKvSetInt, DML_033_003_GmcKvSetInt_008)
{
    void *kvtable = NULL;
    //获取kv
    ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
    ASSERT_EQ(GMERR_OK, ret);
    GmcKvTupleT kvInfo = {0};
    int32_t value = 100;
    char key[] = "zhangsan";
    //设置k-v值
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key);
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(int32_t);
    ret = GmcKvSet(stmt, key, strlen(key), &value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    //设置kv值
    GmcKvTupleT kvInfo1 = {0};
    int8_t value1 = 10;
    char key1[] = "wangwu";
    //设置k-v值
    kvInfo1.key = key1;
    kvInfo1.keyLen = strlen(key1);
    kvInfo1.value = &value1;
    kvInfo1.valueLen = sizeof(int8_t);
    ret = GmcKvSet(stmt, key1, strlen(key1), &value1, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);
    //查询插入的结果
    uint32_t count = 0;
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(2, count);
    //查询一组kv值
    char output[128] = {0};
    uint32_t outputLen = sizeof(output);
    ret = GmcKvGet(stmt, key, strlen(key), output, &outputLen);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(100, *(uint32_t *)output);
    EXPECT_EQ(4, outputLen);
    //将zhangsan的信息value值设置为66
    int32_t value2 = 66;
    //设置k-v值
    kvInfo.value = &value2;
    ret = GmcKvSet(stmt, key, strlen(key), &value2, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t valueSize = 0;
    ret = GmcKvGetValueSize(stmt, key, strlen(key), &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(sizeof(int32_t), valueSize);
    //查询存在的记录数，结果还是2条
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(2, count);
    //通过kvget获取此时zhangsan对应的value值
    ret = GmcKvGet(stmt, key, strlen(key), output, &outputLen);
    EXPECT_EQ(GMERR_OK, ret);
    // zhangsan对应的value值变为66 不是100了
    EXPECT_EQ(66, *(uint32_t *)output);
    EXPECT_EQ(4, outputLen);
    //关闭kv表
    // ret = GmcCloseKvTable(stmt);
    // ASSERT_EQ(GMERR_OK,ret);
}

// 009.未创建kvTable，直接对表插入数据
TEST_F(GmcKvSetInt, DML_033_003_GmcKvSetInt_009)
{
    //删表
    ret = GmcKvDropTable(stmt, g_tableName);
    ASSERT_EQ(GMERR_OK, ret);
    void *kvtable1 = NULL;
    GmcKvTupleT kvInfo = {0};
    int32_t value = 100;
    char key[] = "zhangsan";
    //设置k-v值
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key);
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(int32_t);
    ret = GmcKvSet(stmt, key, strlen(key), &value, sizeof(int32_t));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t valueSize = 0;
    ret = GmcKvGetValueSize(stmt, key, strlen(key), &valueSize);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    //重新建表
    ret = GmcKvCreateTable(stmt, g_tableName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
}

// 010.未使用GmcOpenKvTableByName打开kv表;对表插入kv数据
TEST_F(GmcKvSetInt, DML_033_003_GmcKvSetInt_010)
{
    void *kvtable = NULL;
    GmcKvTupleT kvInfo = {0};
    int32_t value = 100;
    char key[] = "zhangsan";
    //设置k-v值
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key);
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(int32_t);
    ret = GmcKvSet(stmt, key, strlen(key), &value, sizeof(int32_t));
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}
// 011.插入1000条重复数据
TEST_F(GmcKvSetInt, DML_033_003_GmcKvSetInt_011)
{
    void *kvtable = NULL;
    //获取kv
    ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
    ASSERT_EQ(GMERR_OK, ret);
    GmcKvTupleT kvInfo = {0};
    for (uint32_t i = 0; i <= 1000; i++) {
        int32_t value = 100;
        char key[32] = "zhangsan";
        //设置k-v值
        kvInfo.key = key;
        kvInfo.keyLen = strlen(key);
        kvInfo.value = &value;
        kvInfo.valueLen = sizeof(int32_t);
        ret = GmcKvSet(stmt, key, strlen(key), &value, sizeof(int32_t));
        ASSERT_EQ(GMERR_OK, ret);
    }
    //查询插入的结果
    uint32_t count = 0;
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, count);
    // close kv
    // ret = GmcCloseKvTable(stmt);
    // ASSERT_EQ(GMERR_OK,ret);
}
// 012.插入不同类型的多种数据
TEST_F(GmcKvSetInt, DML_033_003_GmcKvSetInt_012)
{
    void *kvtable = NULL;
    //获取kv
    ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
    ASSERT_EQ(GMERR_OK, ret);
    GmcKvTupleT kvInfo = {0};
    int32_t value = 100;
    char key = 'a';
    //设置k-v值
    kvInfo.key = &key;
    kvInfo.keyLen = sizeof(char);
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(int32_t);
    ret = GmcKvSet(stmt, &key, sizeof(char), &value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    //设置kv值
    GmcKvTupleT kvInfo1 = {0};
    bool value1 = true;
    char key1 = 'a';
    //设置k-v值
    kvInfo1.key = &key1;
    kvInfo1.keyLen = sizeof(char);
    kvInfo1.value = &value1;
    kvInfo1.valueLen = sizeof(bool);
    ret = GmcKvSet(stmt, &key1, sizeof(char), &value1, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);
    //查询插入的结果
    uint32_t count = 0;
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, count);
    //设置kv值
    GmcKvTupleT kvInfo2 = {0};
    float value2 = 1.25;
    char key2 = 'b';
    //设置k-v值
    kvInfo2.key = &key2;
    kvInfo2.keyLen = sizeof(char);
    kvInfo2.value = &value2;
    kvInfo2.valueLen = sizeof(float);
    ret = GmcKvSet(stmt, &key2, sizeof(char), &value2, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);

    //设置kv值
    GmcKvTupleT kvInfo3 = {0};
    double value3 = 13.14;
    char key3 = 'c';
    //设置k-v值
    kvInfo3.key = &key3;
    kvInfo3.keyLen = sizeof(char);
    kvInfo3.value = &value3;
    kvInfo3.valueLen = sizeof(double);
    ret = GmcKvSet(stmt, &key3, sizeof(char), &value3, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);
    //查询插入的结果
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(3, count);
    //查询某一对key-value值
    char output[128] = {0};
    uint32_t outputLen = sizeof(output);
    ret = GmcKvGet(stmt, &key3, sizeof(char), output, &outputLen);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(13.14, *(double *)output);
    EXPECT_EQ(8, outputLen);
    // delete其中一条数据
    ret = GmcKvRemove(stmt, &key3, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);
    //查询插入的结果
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(2, count);
    // close kv
    // ret = GmcCloseKvTable(stmt);
    // ASSERT_EQ(GMERR_OK,ret);
}
#if 0
//013.GmcKvSet接口入参kvtable为NULL
TEST_F(GmcKvSetInt, DML_033_003_GmcKvSetInt_013)
{
    void *kvtable = NULL;
    //获取kv
    ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
    EXPECT_EQ(GMERR_OK,ret);
    GmcKvTupleT kvInfo = { 0 };
    int32_t value = 100;
    char key[32] = "zhangsan";
    //设置k-v值
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key);
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(int32_t);
    //设置GmcKvSet接口入参kvtable为NULL
    ret = GmcKvSet(stmt, key, strlen(key), &value, sizeof(int32_t));
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE,ret);
    ret=testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    //关闭kv表table
    // ret = GmcCloseKvTable(stmt);
    // EXPECT_EQ(GMERR_OK,ret);
}
#endif
