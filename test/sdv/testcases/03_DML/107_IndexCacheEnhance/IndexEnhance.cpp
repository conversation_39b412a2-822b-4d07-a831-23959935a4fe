/*****************************************************************************
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 Description  : IndexCacheEnhance
 Notes        :
                001.simi模式，主键为cceh ，使用不同句柄切换操作预期缓存正常（insert&批量删除）
                002.semi模式，主键为cceh ，使用不同句柄切换操作预期缓存正常（merge&批量删除）
 History      :
 Author       :  ywx1157510
 Modification :
 Date         : 2025/07/01
*****************************************************************************/

// 当前merge和replace未操作已写入数据

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <stdint.h>
#include "gtest/gtest.h"
#include "tools.h"

using namespace std;
class IndexEnhance : public testing::Test {
public:
    static void SetUpTestCase()
    {}

    static void TearDownTestCase(){};

    virtual void SetUp();
    virtual void TearDown();
};

void IndexEnhance::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    InitRandomSeed();
}

void IndexEnhance::TearDown()
{
    AW_CHECK_LOG_END();
}
// 001.simi模式，主键为cceh ，使用不同句柄切换操作预期缓存正常（insert&批量删除）
TEST_F(IndexEnhance, DML_107_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "DML_107_normal";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&dmlConn2, &dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/DML_107_normal.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 连接1建表获取扩缩容数据量后删表
    AW_FUN_Log(LOG_STEP, "2.建表获取扩缩容数据量后删表.");
    ret = GetLabeMemChangeValue(&labelInfo, SetPkProperty, SetPkValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 使用DML连接写数据
    AW_FUN_Log(LOG_STEP, "3.使用DML连接写数据");
    // 连接3句柄1写数据到扩容
    labelInfo.expansionValue = 1;
    labelInfo.shrinkValue = 0;
    ret = WriteDataUntilExpand(&labelInfo, dmlConn1, dmlStmt1, SetPkProperty);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接3句柄2删除数据直到触发缩容
    // 删除操作句柄可为随机相同连接不同句柄或不同连接
    GmcStmtT *delStmtArray[2] = {dmlStmt2, dmlStmt3};
    GmcConnT *delConnArray[2] = {dmlConn1, dmlConn2};
    int32_t index = GetRandomMode(2);
    if (index == 0) {
        AW_FUN_Log(LOG_STEP, "当前模式:使用相同连接不同句柄");
    } else {
        AW_FUN_Log(LOG_STEP, "当前模式:使用不同连接");
    }

    ret = DeleteDataUntilExpand(&labelInfo, delConnArray[index], delStmtArray[index], SetPkValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "DeleteDataUntilExpand labelInfo.shrinkValueDataNum is %d", labelInfo.shrinkValueDataNum);

    // 连接3句柄1再次写数据直到扩容
    AW_FUN_Log(LOG_STEP, "4.再次使用DML连接写数据");
    ret = WriteDataUntilExpand(&labelInfo, dmlConn1, dmlStmt1, SetPkProperty);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接3句柄2读数据
    ret = ScanNormalByPk(delStmtArray[index], labelInfo.labelName, labelInfo.expanSionDataNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(labelInfo.expanSionDataNum, ret);

    // 连接3句柄2删除数据直到触发缩容
    sleep(65);
    ret = DeleteDataUntilExpand(&labelInfo, delConnArray[index], delStmtArray[index], SetPkValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接3句柄1读数据
    ret = ScanNormalByPk(
        dmlStmt1, labelInfo.labelName, labelInfo.expanSionDataNum, 0, 1, 0, 24, 0, labelInfo.shrinkValueDataNum);
    AW_MACRO_EXPECT_EQ_INT(labelInfo.expanSionDataNum - labelInfo.shrinkValueDataNum, ret);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelInfo.labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002.semi模式，主键为cceh ，使用不同句柄切换操作预期缓存正常（merge&批量删除）
TEST_F(IndexEnhance, DML_107_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "DML_107_normal";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&dmlConn2, &dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/DML_107_normal.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 连接1建表获取扩缩容数据量后删表
    AW_FUN_Log(LOG_STEP, "2.建表获取扩缩容数据量后删表.");
    ret = GetLabeMemChangeValue(&labelInfo, SetPkProperty, SetPkValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 使用DML连接写数据
    AW_FUN_Log(LOG_STEP, "3.使用DML连接写数据");
    // 连接3句柄1写数据到扩容
    labelInfo.expansionValue = 1;
    labelInfo.shrinkValue = 0;
    ret = WriteDataUntilExpand(&labelInfo, dmlConn1, dmlStmt1, SetPkValue, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接3句柄2删除数据直到触发缩容
    // 删除操作句柄可为随机相同连接不同句柄或不同连接
    GmcStmtT *delStmtArray[2] = {dmlStmt2, dmlStmt3};
    GmcConnT *delConnArray[2] = {dmlConn1, dmlConn2};
    int32_t index = GetRandomMode(2);
    if (index == 0) {
        AW_FUN_Log(LOG_STEP, "当前模式:使用相同连接不同句柄");
    } else {
        AW_FUN_Log(LOG_STEP, "当前模式:使用不同连接");
    }

    ret = DeleteDataUntilExpand(&labelInfo, delConnArray[index], delStmtArray[index], SetPkValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "DeleteDataUntilExpand labelInfo.shrinkValueDataNum is %d", labelInfo.shrinkValueDataNum);

    // 连接3句柄1再次写数据直到扩容
    AW_FUN_Log(LOG_STEP, "4.再次使用DML连接写数据");
    ret = WriteDataUntilExpand(&labelInfo, dmlConn1, dmlStmt1, SetPkValue, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接3句柄2读数据
    ret = ScanNormalByPk(delStmtArray[index], labelInfo.labelName, labelInfo.expanSionDataNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(labelInfo.expanSionDataNum, ret);

    // 连接3句柄2删除数据直到触发缩容
    sleep(65);
    ret = DeleteDataUntilExpand(&labelInfo, delConnArray[index], delStmtArray[index], SetPkValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接3句柄1读数据
    ret = ScanNormalByPk(
        dmlStmt1, labelInfo.labelName, labelInfo.expanSionDataNum, 0, 1, 0, 24, 0, labelInfo.shrinkValueDataNum);
    AW_MACRO_EXPECT_EQ_INT(labelInfo.expanSionDataNum - labelInfo.shrinkValueDataNum, ret);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelInfo.labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003.semi模式，主键为cceh ，使用不同句柄切换操作预期缓存正常（replace&批量删除）
TEST_F(IndexEnhance, DML_107_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "DML_107_normal";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&dmlConn2, &dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/DML_107_normal.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 连接1建表获取扩缩容数据量后删表
    AW_FUN_Log(LOG_STEP, "2.建表获取扩缩容数据量后删表.");
    ret = GetLabeMemChangeValue(&labelInfo, SetPkProperty, SetPkValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 使用DML连接写数据
    AW_FUN_Log(LOG_STEP, "3.使用DML连接写数据");
    // 连接3句柄1写数据到扩容
    labelInfo.expansionValue = 1;
    labelInfo.shrinkValue = 0;
    ret = WriteDataUntilExpand(&labelInfo, dmlConn1, dmlStmt1, SetPkProperty, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接3句柄2删除数据直到触发缩容
    // 删除操作句柄可为随机相同连接不同句柄或不同连接
    GmcStmtT *delStmtArray[2] = {dmlStmt2, dmlStmt3};
    GmcConnT *delConnArray[2] = {dmlConn1, dmlConn2};
    int32_t index = GetRandomMode(2);
    if (index == 0) {
        AW_FUN_Log(LOG_STEP, "当前模式:使用相同连接不同句柄");
    } else {
        AW_FUN_Log(LOG_STEP, "当前模式:使用不同连接");
    }

    ret = DeleteDataUntilExpand(&labelInfo, delConnArray[index], delStmtArray[index], SetPkValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "DeleteDataUntilExpand labelInfo.shrinkValueDataNum is %d", labelInfo.shrinkValueDataNum);

    // 连接3句柄1再次写数据直到扩容
    AW_FUN_Log(LOG_STEP, "4.再次使用DML连接写数据");
    ret = WriteDataUntilExpand(&labelInfo, dmlConn1, dmlStmt1, SetPkProperty, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接3句柄2读数据
    ret = ScanNormalByPk(delStmtArray[index], labelInfo.labelName, labelInfo.expanSionDataNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(labelInfo.expanSionDataNum, ret);

    // 连接3句柄2删除数据直到触发缩容
    sleep(65);
    ret = DeleteDataUntilExpand(&labelInfo, delConnArray[index], delStmtArray[index], SetPkValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接3句柄1读数据
    ret = ScanNormalByPk(
        dmlStmt1, labelInfo.labelName, labelInfo.expanSionDataNum, 0, 1, 0, 24, 0, labelInfo.shrinkValueDataNum);
    AW_MACRO_EXPECT_EQ_INT(labelInfo.expanSionDataNum - labelInfo.shrinkValueDataNum, ret);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelInfo.labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004.semi模式，主键为cceh ，使用不同句柄切换操作预期缓存正常（insert&（前台truncate 同步||异步））
TEST_F(IndexEnhance, DML_107_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "DML_107_normal";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 使用异步连接
    ret = testGmcConnect(&dmlConn2, &dmlStmt3, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/DML_107_normal.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 连接1建表获取扩缩容数据量后删表
    AW_FUN_Log(LOG_STEP, "2.建表获取扩缩容数据量后删表.");
    ret = GetLabeMemChangeValue(&labelInfo, SetPkProperty, SetPkValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 使用DML连接写数据
    AW_FUN_Log(LOG_STEP, "3.使用DML连接写数据");
    // 连接3句柄1写数据到扩容
    labelInfo.expansionValue = 1;
    labelInfo.shrinkValue = 0;
    ret = WriteDataUntilExpand(&labelInfo, dmlConn1, dmlStmt1, SetPkProperty);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接3句柄2删除数据直到触发缩容
    // 删除操作句柄可为随机相同连接不同句柄或不同连接
    GmcStmtT *delStmtArray[2] = {dmlStmt2, dmlStmt3};
    GmcConnT *delConnArray[2] = {dmlConn1, dmlConn2};
    int32_t index = GetRandomMode(2);
    if (index == 0) {
        AW_FUN_Log(LOG_STEP, "当前模式:使用相同连接不同句柄");
    } else {
        AW_FUN_Log(LOG_STEP, "当前模式:使用不同连接");
    }
    ret = FrontTruncateDeleteData(
        &labelInfo, delConnArray[index], delStmtArray[index], index, labelInfo.expanSionDataNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "DeleteDataUntilExpand labelInfo.shrinkValueDataNum is %d", labelInfo.shrinkValueDataNum);

    // 连接3句柄1再次写数据直到扩容
    AW_FUN_Log(LOG_STEP, "4.再次使用DML连接写数据");
    labelInfo.shrinkValueDataNum = 0;
    labelInfo.shrinkValue = 1;
    ret = WriteDataUntilExpand(&labelInfo, dmlConn1, dmlStmt1, SetPkProperty);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接3句柄2读数据
    ret = ScanNormalByPk(delStmtArray[0], labelInfo.labelName, labelInfo.expanSionDataNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(labelInfo.expanSionDataNum, ret);

    // 连接3句柄2删除数据直到触发缩容
    ret = FrontTruncateDeleteData(
        &labelInfo, delConnArray[index], delStmtArray[index], index, labelInfo.expanSionDataNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接3句柄1读数据
    ret = ScanNormalByPk(
        dmlStmt1, labelInfo.labelName, labelInfo.expanSionDataNum, 0, 1, 0, 24, 0, labelInfo.shrinkValueDataNum);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelInfo.labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005.semi模式，主键为cceh ，使用不同句柄切换操作预期缓存正常（merge&（前台truncate 同步||异步））
TEST_F(IndexEnhance, DML_107_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "DML_107_normal";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 使用异步连接
    ret = testGmcConnect(&dmlConn2, &dmlStmt3, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/DML_107_normal.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 连接1建表获取扩缩容数据量后删表
    AW_FUN_Log(LOG_STEP, "2.建表获取扩缩容数据量后删表.");
    ret = GetLabeMemChangeValue(&labelInfo, SetPkProperty, SetPkValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 使用DML连接写数据
    AW_FUN_Log(LOG_STEP, "3.使用DML连接写数据");
    // 连接3句柄1写数据到扩容
    labelInfo.expansionValue = 1;
    labelInfo.shrinkValue = 0;
    ret = WriteDataUntilExpand(&labelInfo, dmlConn1, dmlStmt1, SetPkValue, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接3句柄2删除数据直到触发缩容
    // 删除操作句柄可为随机相同连接不同句柄或不同连接
    GmcStmtT *delStmtArray[2] = {dmlStmt2, dmlStmt3};
    GmcConnT *delConnArray[2] = {dmlConn1, dmlConn2};
    int32_t index = GetRandomMode(2);
    if (index == 0) {
        AW_FUN_Log(LOG_STEP, "当前模式:使用相同连接不同句柄");
    } else {
        AW_FUN_Log(LOG_STEP, "当前模式:使用不同连接");
    }
    ret = FrontTruncateDeleteData(
        &labelInfo, delConnArray[index], delStmtArray[index], index, labelInfo.expanSionDataNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "DeleteDataUntilExpand labelInfo.shrinkValueDataNum is %d", labelInfo.shrinkValueDataNum);

    // 连接3句柄1再次写数据直到扩容
    AW_FUN_Log(LOG_STEP, "4.再次使用DML连接写数据");
    labelInfo.shrinkValueDataNum = 0;
    labelInfo.shrinkValue = 1;
    ret = WriteDataUntilExpand(&labelInfo, dmlConn1, dmlStmt1, SetPkValue, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接3句柄2读数据
    ret = ScanNormalByPk(delStmtArray[0], labelInfo.labelName, labelInfo.expanSionDataNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(labelInfo.expanSionDataNum, ret);

    // 连接3句柄2删除数据直到触发缩容
    ret = FrontTruncateDeleteData(
        &labelInfo, delConnArray[index], delStmtArray[index], index, labelInfo.expanSionDataNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接3句柄1读数据
    ret = ScanNormalByPk(
        dmlStmt1, labelInfo.labelName, labelInfo.expanSionDataNum, 0, 1, 0, 24, 0, labelInfo.shrinkValueDataNum);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelInfo.labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006.semi模式，主键为cceh ，使用不同句柄切换操作预期缓存正常（replace&（前台truncate 同步||异步））
TEST_F(IndexEnhance, DML_107_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "DML_107_normal";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 使用异步连接
    ret = testGmcConnect(&dmlConn2, &dmlStmt3, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/DML_107_normal.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 连接1建表获取扩缩容数据量后删表
    AW_FUN_Log(LOG_STEP, "2.建表获取扩缩容数据量后删表.");
    ret = GetLabeMemChangeValue(&labelInfo, SetPkProperty, SetPkValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 使用DML连接写数据
    AW_FUN_Log(LOG_STEP, "3.使用DML连接写数据");
    // 连接3句柄1写数据到扩容
    labelInfo.expansionValue = 1;
    labelInfo.shrinkValue = 0;
    ret = WriteDataUntilExpand(&labelInfo, dmlConn1, dmlStmt1, SetPkProperty, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接3句柄2删除数据直到触发缩容
    // 删除操作句柄可为随机相同连接不同句柄或不同连接
    GmcStmtT *delStmtArray[2] = {dmlStmt2, dmlStmt3};
    GmcConnT *delConnArray[2] = {dmlConn1, dmlConn2};
    int32_t index = GetRandomMode(2);
    if (index == 0) {
        AW_FUN_Log(LOG_STEP, "当前模式:使用相同连接不同句柄");
    } else {
        AW_FUN_Log(LOG_STEP, "当前模式:使用不同连接");
    }
    ret = FrontTruncateDeleteData(
        &labelInfo, delConnArray[index], delStmtArray[index], index, labelInfo.expanSionDataNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "DeleteDataUntilExpand labelInfo.shrinkValueDataNum is %d", labelInfo.shrinkValueDataNum);

    // 连接3句柄1再次写数据直到扩容
    AW_FUN_Log(LOG_STEP, "4.再次使用DML连接写数据");
    labelInfo.shrinkValueDataNum = 0;
    labelInfo.shrinkValue = 1;
    ret = WriteDataUntilExpand(&labelInfo, dmlConn1, dmlStmt1, SetPkProperty, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接3句柄2读数据
    ret = ScanNormalByPk(delStmtArray[0], labelInfo.labelName, labelInfo.expanSionDataNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(labelInfo.expanSionDataNum, ret);

    // 连接3句柄2删除数据直到触发缩容
    ret = FrontTruncateDeleteData(
        &labelInfo, delConnArray[index], delStmtArray[index], index, labelInfo.expanSionDataNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接3句柄1读数据
    ret = ScanNormalByPk(
        dmlStmt1, labelInfo.labelName, labelInfo.expanSionDataNum, 0, 1, 0, 24, 0, labelInfo.shrinkValueDataNum);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelInfo.labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007.simi模式，主键为cceh,开启聚簇容器 ，使用不同句柄切换操作预期缓存正常（insert&批量删除）
TEST_F(IndexEnhance, DML_107_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "DML_107_normal";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer(true);

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&dmlConn2, &dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);
    labelInfo.isOpenCluster = true;

    char *labelJson = NULL;
    readJanssonFile("./schema_file/DML_107_normal.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 连接1建表获取扩缩容数据量后删表
    AW_FUN_Log(LOG_STEP, "2.建表获取扩缩容数据量后删表.");
    ret = GetLabeMemChangeValue(&labelInfo, SetPkProperty, SetPkValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 使用DML连接写数据
    AW_FUN_Log(LOG_STEP, "3.使用DML连接写数据");
    // 连接3句柄1写数据到扩容
    labelInfo.expansionValue = 1;
    labelInfo.shrinkValue = 0;
    ret = WriteDataUntilExpand(&labelInfo, dmlConn1, dmlStmt1, SetPkProperty);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接3句柄2删除数据直到触发缩容
    // 删除操作句柄可为随机相同连接不同句柄或不同连接
    GmcStmtT *delStmtArray[2] = {dmlStmt2, dmlStmt3};
    GmcConnT *delConnArray[2] = {dmlConn1, dmlConn2};
    int32_t index = GetRandomMode(2);
    if (index == 0) {
        AW_FUN_Log(LOG_STEP, "当前模式:使用相同连接不同句柄");
    } else {
        AW_FUN_Log(LOG_STEP, "当前模式:使用不同连接");
    }

    ret = DeleteDataUntilExpand(&labelInfo, delConnArray[index], delStmtArray[index], SetPkValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "DeleteDataUntilExpand labelInfo.shrinkValueDataNum is %d", labelInfo.shrinkValueDataNum);

    // 连接3句柄1再次写数据直到扩容
    AW_FUN_Log(LOG_STEP, "4.再次使用DML连接写数据");
    ret = WriteDataUntilExpand(&labelInfo, dmlConn1, dmlStmt1, SetPkProperty);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接3句柄2读数据
    ret = ScanNormalByPk(delStmtArray[index], labelInfo.labelName, labelInfo.expanSionDataNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(labelInfo.expanSionDataNum, ret);

    // 连接3句柄2删除数据直到触发缩容
    sleep(65);
    ret = DeleteDataUntilExpand(&labelInfo, delConnArray[index], delStmtArray[index], SetPkValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接3句柄1读数据
    ret = ScanNormalByPk(
        dmlStmt1, labelInfo.labelName, labelInfo.expanSionDataNum, 0, 1, 0, 24, 0, labelInfo.shrinkValueDataNum);
    AW_MACRO_EXPECT_EQ_INT(labelInfo.expanSionDataNum - labelInfo.shrinkValueDataNum, ret);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelInfo.labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008.semi模式，主键为cceh,开启聚簇容器 ，使用不同句柄切换操作预期缓存正常（merge&批量删除）
TEST_F(IndexEnhance, DML_107_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "DML_107_normal";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer(true);

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&dmlConn2, &dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);
    labelInfo.isOpenCluster = true;

    char *labelJson = NULL;
    readJanssonFile("./schema_file/DML_107_normal.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 连接1建表获取扩缩容数据量后删表
    AW_FUN_Log(LOG_STEP, "2.建表获取扩缩容数据量后删表.");
    ret = GetLabeMemChangeValue(&labelInfo, SetPkProperty, SetPkValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 使用DML连接写数据
    AW_FUN_Log(LOG_STEP, "3.使用DML连接写数据");
    // 连接3句柄1写数据到扩容
    labelInfo.expansionValue = 1;
    labelInfo.shrinkValue = 0;
    ret = WriteDataUntilExpand(&labelInfo, dmlConn1, dmlStmt1, SetPkValue, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接3句柄2删除数据直到触发缩容
    // 删除操作句柄可为随机相同连接不同句柄或不同连接
    GmcStmtT *delStmtArray[2] = {dmlStmt2, dmlStmt3};
    GmcConnT *delConnArray[2] = {dmlConn1, dmlConn2};
    int32_t index = GetRandomMode(2);
    if (index == 0) {
        AW_FUN_Log(LOG_STEP, "当前模式:使用相同连接不同句柄");
    } else {
        AW_FUN_Log(LOG_STEP, "当前模式:使用不同连接");
    }

    ret = DeleteDataUntilExpand(&labelInfo, delConnArray[index], delStmtArray[index], SetPkValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "DeleteDataUntilExpand labelInfo.shrinkValueDataNum is %d", labelInfo.shrinkValueDataNum);

    // 连接3句柄1再次写数据直到扩容
    AW_FUN_Log(LOG_STEP, "4.再次使用DML连接写数据");
    ret = WriteDataUntilExpand(&labelInfo, dmlConn1, dmlStmt1, SetPkValue, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接3句柄2读数据
    ret = ScanNormalByPk(delStmtArray[index], labelInfo.labelName, labelInfo.expanSionDataNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(labelInfo.expanSionDataNum, ret);

    // 连接3句柄2删除数据直到触发缩容
    sleep(65);
    ret = DeleteDataUntilExpand(&labelInfo, delConnArray[index], delStmtArray[index], SetPkValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接3句柄1读数据
    ret = ScanNormalByPk(
        dmlStmt1, labelInfo.labelName, labelInfo.expanSionDataNum, 0, 1, 0, 24, 0, labelInfo.shrinkValueDataNum);
    AW_MACRO_EXPECT_EQ_INT(labelInfo.expanSionDataNum - labelInfo.shrinkValueDataNum, ret);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelInfo.labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 009.semi模式，主键为cceh,开启聚簇容器 ，使用不同句柄切换操作预期缓存正常（replace&批量删除）
TEST_F(IndexEnhance, DML_107_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "DML_107_normal";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer(true);

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&dmlConn2, &dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);
    labelInfo.isOpenCluster = true;

    char *labelJson = NULL;
    readJanssonFile("./schema_file/DML_107_normal.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 连接1建表获取扩缩容数据量后删表
    AW_FUN_Log(LOG_STEP, "2.建表获取扩缩容数据量后删表.");
    ret = GetLabeMemChangeValue(&labelInfo, SetPkProperty, SetPkValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 使用DML连接写数据
    AW_FUN_Log(LOG_STEP, "3.使用DML连接写数据");
    // 连接3句柄1写数据到扩容
    labelInfo.expansionValue = 1;
    labelInfo.shrinkValue = 0;
    ret = WriteDataUntilExpand(&labelInfo, dmlConn1, dmlStmt1, SetPkProperty, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接3句柄2删除数据直到触发缩容
    // 删除操作句柄可为随机相同连接不同句柄或不同连接
    GmcStmtT *delStmtArray[2] = {dmlStmt2, dmlStmt3};
    GmcConnT *delConnArray[2] = {dmlConn1, dmlConn2};
    int32_t index = GetRandomMode(2);
    if (index == 0) {
        AW_FUN_Log(LOG_STEP, "当前模式:使用相同连接不同句柄");
    } else {
        AW_FUN_Log(LOG_STEP, "当前模式:使用不同连接");
    }

    ret = DeleteDataUntilExpand(&labelInfo, delConnArray[index], delStmtArray[index], SetPkValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "DeleteDataUntilExpand labelInfo.shrinkValueDataNum is %d", labelInfo.shrinkValueDataNum);

    // 连接3句柄1再次写数据直到扩容
    AW_FUN_Log(LOG_STEP, "4.再次使用DML连接写数据");
    ret = WriteDataUntilExpand(&labelInfo, dmlConn1, dmlStmt1, SetPkProperty, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接3句柄2读数据
    ret = ScanNormalByPk(delStmtArray[index], labelInfo.labelName, labelInfo.expanSionDataNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(labelInfo.expanSionDataNum, ret);

    // 连接3句柄2删除数据直到触发缩容
    sleep(65);
    ret = DeleteDataUntilExpand(&labelInfo, delConnArray[index], delStmtArray[index], SetPkValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接3句柄1读数据
    ret = ScanNormalByPk(
        dmlStmt1, labelInfo.labelName, labelInfo.expanSionDataNum, 0, 1, 0, 24, 0, labelInfo.shrinkValueDataNum);
    AW_MACRO_EXPECT_EQ_INT(labelInfo.expanSionDataNum - labelInfo.shrinkValueDataNum, ret);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelInfo.labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010.semi模式，主键为cceh,开启聚簇容器 ，使用不同句柄切换操作预期缓存正常（insert&（前台truncate 同步||异步））
TEST_F(IndexEnhance, DML_107_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "DML_107_normal";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer(true);

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 使用异步连接
    ret = testGmcConnect(&dmlConn2, &dmlStmt3, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);
    labelInfo.isOpenCluster = true;

    char *labelJson = NULL;
    readJanssonFile("./schema_file/DML_107_normal.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 连接1建表获取扩缩容数据量后删表
    AW_FUN_Log(LOG_STEP, "2.建表获取扩缩容数据量后删表.");
    ret = GetLabeMemChangeValue(&labelInfo, SetPkProperty, SetPkValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 使用DML连接写数据
    AW_FUN_Log(LOG_STEP, "3.使用DML连接写数据");
    // 连接3句柄1写数据到扩容
    labelInfo.expansionValue = 1;
    labelInfo.shrinkValue = 0;
    ret = WriteDataUntilExpand(&labelInfo, dmlConn1, dmlStmt1, SetPkProperty);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接3句柄2删除数据直到触发缩容
    // 删除操作句柄可为随机相同连接不同句柄或不同连接
    GmcStmtT *delStmtArray[2] = {dmlStmt2, dmlStmt3};
    GmcConnT *delConnArray[2] = {dmlConn1, dmlConn2};
    int32_t index = GetRandomMode(2);
    if (index == 0) {
        AW_FUN_Log(LOG_STEP, "当前模式:使用相同连接不同句柄");
    } else {
        AW_FUN_Log(LOG_STEP, "当前模式:使用不同连接");
    }
    ret = FrontTruncateDeleteData(
        &labelInfo, delConnArray[index], delStmtArray[index], index, labelInfo.expanSionDataNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "DeleteDataUntilExpand labelInfo.shrinkValueDataNum is %d", labelInfo.shrinkValueDataNum);

    // 连接3句柄1再次写数据直到扩容
    AW_FUN_Log(LOG_STEP, "4.再次使用DML连接写数据");
    labelInfo.shrinkValueDataNum = 0;
    labelInfo.shrinkValue = 1;
    ret = WriteDataUntilExpand(&labelInfo, dmlConn1, dmlStmt1, SetPkProperty);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接3句柄2读数据
    ret = ScanNormalByPk(delStmtArray[0], labelInfo.labelName, labelInfo.expanSionDataNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(labelInfo.expanSionDataNum, ret);

    // 连接3句柄2删除数据直到触发缩容
    ret = FrontTruncateDeleteData(
        &labelInfo, delConnArray[index], delStmtArray[index], index, labelInfo.expanSionDataNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接3句柄1读数据
    ret = ScanNormalByPk(
        dmlStmt1, labelInfo.labelName, labelInfo.expanSionDataNum, 0, 1, 0, 24, 0, labelInfo.shrinkValueDataNum);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelInfo.labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 011.semi模式，主键为cceh,开启聚簇容器使用不同句柄切换操作预期缓存正常（merge&（前台truncate 同步||异步））
TEST_F(IndexEnhance, DML_107_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "DML_107_normal";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer(true);

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 使用异步连接
    ret = testGmcConnect(&dmlConn2, &dmlStmt3, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);
    labelInfo.isOpenCluster = true;

    char *labelJson = NULL;
    readJanssonFile("./schema_file/DML_107_normal.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 连接1建表获取扩缩容数据量后删表
    AW_FUN_Log(LOG_STEP, "2.建表获取扩缩容数据量后删表.");
    ret = GetLabeMemChangeValue(&labelInfo, SetPkProperty, SetPkValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 使用DML连接写数据
    AW_FUN_Log(LOG_STEP, "3.使用DML连接写数据");
    // 连接3句柄1写数据到扩容
    labelInfo.expansionValue = 1;
    labelInfo.shrinkValue = 0;
    ret = WriteDataUntilExpand(&labelInfo, dmlConn1, dmlStmt1, SetPkValue, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接3句柄2删除数据直到触发缩容
    // 删除操作句柄可为随机相同连接不同句柄或不同连接
    GmcStmtT *delStmtArray[2] = {dmlStmt2, dmlStmt3};
    GmcConnT *delConnArray[2] = {dmlConn1, dmlConn2};
    int32_t index = GetRandomMode(2);
    if (index == 0) {
        AW_FUN_Log(LOG_STEP, "当前模式:使用相同连接不同句柄");
    } else {
        AW_FUN_Log(LOG_STEP, "当前模式:使用不同连接");
    }
    ret = FrontTruncateDeleteData(
        &labelInfo, delConnArray[index], delStmtArray[index], index, labelInfo.expanSionDataNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "DeleteDataUntilExpand labelInfo.shrinkValueDataNum is %d", labelInfo.shrinkValueDataNum);

    // 连接3句柄1再次写数据直到扩容
    AW_FUN_Log(LOG_STEP, "4.再次使用DML连接写数据");
    labelInfo.shrinkValueDataNum = 0;
    labelInfo.shrinkValue = 1;
    ret = WriteDataUntilExpand(&labelInfo, dmlConn1, dmlStmt1, SetPkValue, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接3句柄2读数据
    ret = ScanNormalByPk(delStmtArray[0], labelInfo.labelName, labelInfo.expanSionDataNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(labelInfo.expanSionDataNum, ret);

    // 连接3句柄2删除数据直到触发缩容
    ret = FrontTruncateDeleteData(
        &labelInfo, delConnArray[index], delStmtArray[index], index, labelInfo.expanSionDataNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接3句柄1读数据
    ret = ScanNormalByPk(
        dmlStmt1, labelInfo.labelName, labelInfo.expanSionDataNum, 0, 1, 0, 24, 0, labelInfo.shrinkValueDataNum);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelInfo.labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 012.semi模式，主键为cceh,开启聚簇容器，使用不同句柄切换操作预期缓存正常（replace&（前台truncate 同步||异步））
TEST_F(IndexEnhance, DML_107_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "DML_107_normal";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer(true);

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 使用异步连接
    ret = testGmcConnect(&dmlConn2, &dmlStmt3, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);
    labelInfo.isOpenCluster = true;

    char *labelJson = NULL;
    readJanssonFile("./schema_file/DML_107_normal.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 连接1建表获取扩缩容数据量后删表
    AW_FUN_Log(LOG_STEP, "2.建表获取扩缩容数据量后删表.");
    ret = GetLabeMemChangeValue(&labelInfo, SetPkProperty, SetPkValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 使用DML连接写数据
    AW_FUN_Log(LOG_STEP, "3.使用DML连接写数据");
    // 连接3句柄1写数据到扩容
    labelInfo.expansionValue = 1;
    labelInfo.shrinkValue = 0;
    ret = WriteDataUntilExpand(&labelInfo, dmlConn1, dmlStmt1, SetPkProperty, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接3句柄2删除数据直到触发缩容
    // 删除操作句柄可为随机相同连接不同句柄或不同连接
    GmcStmtT *delStmtArray[2] = {dmlStmt2, dmlStmt3};
    GmcConnT *delConnArray[2] = {dmlConn1, dmlConn2};
    int32_t index = GetRandomMode(2);
    if (index == 0) {
        AW_FUN_Log(LOG_STEP, "当前模式:使用相同连接不同句柄");
    } else {
        AW_FUN_Log(LOG_STEP, "当前模式:使用不同连接");
    }
    ret = FrontTruncateDeleteData(
        &labelInfo, delConnArray[index], delStmtArray[index], index, labelInfo.expanSionDataNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "DeleteDataUntilExpand labelInfo.shrinkValueDataNum is %d", labelInfo.shrinkValueDataNum);

    // 连接3句柄1再次写数据直到扩容
    AW_FUN_Log(LOG_STEP, "4.再次使用DML连接写数据");
    labelInfo.shrinkValueDataNum = 0;
    labelInfo.shrinkValue = 1;
    ret = WriteDataUntilExpand(&labelInfo, dmlConn1, dmlStmt1, SetPkProperty, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接3句柄2读数据
    ret = ScanNormalByPk(delStmtArray[0], labelInfo.labelName, labelInfo.expanSionDataNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(labelInfo.expanSionDataNum, ret);

    // 连接3句柄2删除数据直到触发缩容
    ret = FrontTruncateDeleteData(
        &labelInfo, delConnArray[index], delStmtArray[index], index, labelInfo.expanSionDataNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接3句柄1读数据
    ret = ScanNormalByPk(
        dmlStmt1, labelInfo.labelName, labelInfo.expanSionDataNum, 0, 1, 0, 24, 0, labelInfo.shrinkValueDataNum);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelInfo.labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 013.（local索引）,semi模式，进行art索引树形结构的四层扩缩容
// node（4->5（升级到16）使用不同句柄切换操作预期缓存正常（insert&批量删除）
TEST_F(IndexEnhance, DML_107_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "DML_107_normal_local";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheNormalServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&dmlConn2, &dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/DML_107_normal_local.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "生成随机数据.");
    GetArtNodeDataIndfo(&g_artNodeInfo, 5, true, true, true, true);

    // 批量写入数据
    AW_FUN_Log(LOG_STEP, "1.批量写入数据触发节点扩容.");
    int32_t recordNum = g_artNodeInfo.nodeCount4;
    ret = BatchInsertLocalTable(dmlConn1, dmlStmt1, labelName, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanNormalByPk(dmlStmt3, labelName, recordNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点增加.");
    g_artNodeLabelInfo.labelName = labelName;
    g_artNodeLabelInfo.indexType = (char *)"local";
    g_artNodeLabelInfo.nodeCount.node4Count = 0;
    g_artNodeLabelInfo.nodeCount.node16Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;

    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期node16节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.nodeCount.node16Count);
    if (g_artNodeLabelInfo.nodeCount.node16Count == 0) {
        SystemSnprintf("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -f LABEL_NAME=%s", labelName);
    }

    // 删除数据
    AW_FUN_Log(LOG_STEP, "删除数据.");
    ret = DeleteDataUntilExpandLocal(labelName, recordNum, dmlConn2, dmlStmt3, SetPkValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanNormalByPk(dmlStmt1, labelName, recordNum, 0, 1, 0, 24, 0);
    recordNum = 0;
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点释放.");
    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期free node16节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode16Count);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode4Count);
    SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放申请的内存
    free(g_artNodeInfo.upNode);

    free(g_artNodeInfo.normalNode1);

    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 014.（local索引）,semi模式，进行art索引树形结构的四层扩缩容
// node（4->5（升级到16）使用不同句柄切换操作预期缓存正常（（merge||replace）&批量删除）
TEST_F(IndexEnhance, DML_107_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "DML_107_normal_local";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&dmlConn2, &dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/DML_107_normal_local.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "生成随机数据.");
    GetArtNodeDataIndfo(&g_artNodeInfo, 5, true, true, true, true);

    // 批量写入数据
    AW_FUN_Log(LOG_STEP, "1.批量写入数据触发节点扩容.");
    int32_t recordNum = g_artNodeInfo.nodeCount4;
    ret = BatchInsertLocalTable(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = BatchInsertLocalTable(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanNormalByPk(dmlStmt3, labelName, recordNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点增加.");
    g_artNodeLabelInfo.labelName = labelName;
    g_artNodeLabelInfo.indexType = (char *)"local";
    g_artNodeLabelInfo.nodeCount.node4Count = 0;
    g_artNodeLabelInfo.nodeCount.node16Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;

    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期node16节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.nodeCount.node16Count);
    if (g_artNodeLabelInfo.nodeCount.node16Count == 0) {
        SystemSnprintf("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -f LABEL_NAME=%s", labelName);
    }

    // 删除数据
    AW_FUN_Log(LOG_STEP, "删除数据.");
    ret = DeleteDataUntilExpandLocal(labelName, recordNum, dmlConn2, dmlStmt3, SetPkValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanNormalByPk(dmlStmt1, labelName, recordNum, 0, 1, 0, 24, 0);
    recordNum = 0;
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点释放.");
    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期free node16节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode16Count);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode4Count);
    SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放申请的内存
    free(g_artNodeInfo.upNode);

    free(g_artNodeInfo.normalNode1);

    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 015.（local索引）,semi模式，进行art索引树形结构的四层扩缩容 一次性扩容
// node（16->17（升级到48）使用不同句柄切换操作预期缓存正常（insert&批量删除）
TEST_F(IndexEnhance, DML_107_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "DML_107_normal_local";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&dmlConn2, &dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/DML_107_normal_local.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "生成随机数据.");
    GetArtNodeDataIndfo(&g_artNodeInfo, 17, true, true, true, true);

    // 批量写入数据
    AW_FUN_Log(LOG_STEP, "1.批量写入数据触发节点扩容.");
    int32_t recordNum = g_artNodeInfo.nodeCount4;
    ret = BatchInsertLocalTable(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_INSERT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanNormalByPk(dmlStmt3, labelName, recordNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点增加.");
    g_artNodeLabelInfo.labelName = labelName;
    g_artNodeLabelInfo.indexType = (char *)"local";
    g_artNodeLabelInfo.nodeCount.node4Count = 0;
    g_artNodeLabelInfo.nodeCount.node16Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;

    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期node16节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.nodeCount.node48Count);
    if (g_artNodeLabelInfo.nodeCount.node48Count == 0) {
        SystemSnprintf("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -f LABEL_NAME=%s", labelName);
    }

    // 删除数据
    AW_FUN_Log(LOG_STEP, "删除数据.");
    ret = DeleteDataUntilExpandLocal(labelName, recordNum, dmlConn2, dmlStmt3, SetPkValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanNormalByPk(dmlStmt1, labelName, recordNum, 0, 1, 0, 24, 0);
    recordNum = 0;
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点释放.");
    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期free node48节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode48Count);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode4Count);
    SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放申请的内存
    free(g_artNodeInfo.upNode);

    free(g_artNodeInfo.normalNode1);

    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 016.（local索引）,semi模式，进行art索引树形结构的四层扩缩容 一次性扩容
// node（16->17（升级到48）使用不同句柄切换操作预期缓存正常（（merge||replace）&批量删除）
TEST_F(IndexEnhance, DML_107_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "DML_107_normal_local";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&dmlConn2, &dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/DML_107_normal_local.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "生成随机数据.");
    GetArtNodeDataIndfo(&g_artNodeInfo, 17, true, true, true, true);

    // 批量写入数据
    AW_FUN_Log(LOG_STEP, "1.批量写入数据触发节点扩容.");
    int32_t recordNum = g_artNodeInfo.nodeCount4;
    ret = BatchInsertLocalTable(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_MERGE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertLocalTable(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_REPLACE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanNormalByPk(dmlStmt3, labelName, recordNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点增加.");
    g_artNodeLabelInfo.labelName = labelName;
    g_artNodeLabelInfo.indexType = (char *)"local";
    g_artNodeLabelInfo.nodeCount.node4Count = 0;
    g_artNodeLabelInfo.nodeCount.node16Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;

    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期node16节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.nodeCount.node48Count);
    if (g_artNodeLabelInfo.nodeCount.node48Count == 0) {
        SystemSnprintf("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -f LABEL_NAME=%s", labelName);
    }

    // 删除数据
    AW_FUN_Log(LOG_STEP, "删除数据.");
    ret = DeleteDataUntilExpandLocal(labelName, recordNum, dmlConn2, dmlStmt3, SetPkValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanNormalByPk(dmlStmt1, labelName, recordNum, 0, 1, 0, 24, 0);
    recordNum = 0;
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点释放.");
    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期free node48节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode48Count);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode4Count);
    SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放申请的内存
    free(g_artNodeInfo.upNode);

    free(g_artNodeInfo.normalNode1);

    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 017.（local索引）,semi模式，进行art索引树形结构的四层扩缩容 两次扩容
// node（4->17（升级到48）使用不同句柄切换操作预期缓存正常（insert&批量删除）
TEST_F(IndexEnhance, DML_107_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "DML_107_normal_local";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&dmlConn2, &dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/DML_107_normal_local.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "生成随机数据.");
    GetArtNodeDataIndfo(&g_artNodeInfo, 17, true, true, true, true);

    // 批量写入数据
    AW_FUN_Log(LOG_STEP, "1.批量写入数据触发节点扩容.");
    int32_t recordNum = g_artNodeInfo.nodeCount4;
    ret = BatchInsertLocalTable(dmlConn1, dmlStmt1, labelName, 10, GMC_OPERATION_INSERT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = BatchInsertLocalTable(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_INSERT, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanNormalByPk(dmlStmt3, labelName, recordNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点增加.");
    g_artNodeLabelInfo.labelName = labelName;
    g_artNodeLabelInfo.indexType = (char *)"local";
    g_artNodeLabelInfo.nodeCount.node4Count = 0;
    g_artNodeLabelInfo.nodeCount.node16Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;

    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期node48节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.nodeCount.node48Count);
    if (g_artNodeLabelInfo.nodeCount.node48Count == 0) {
        SystemSnprintf("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -f LABEL_NAME=%s", labelName);
    }

    // 删除数据
    AW_FUN_Log(LOG_STEP, "删除数据.");
    ret = DeleteDataUntilExpandLocal(labelName, recordNum, dmlConn2, dmlStmt3, SetPkValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanNormalByPk(dmlStmt1, labelName, recordNum, 0, 1, 0, 24, 0);
    recordNum = 0;
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点释放.");
    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期free node48节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode48Count);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode4Count);
    SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放申请的内存
    free(g_artNodeInfo.upNode);

    free(g_artNodeInfo.normalNode1);

    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 018.（local索引）,semi模式，进行art索引树形结构的四层扩缩容 两次扩容
// node（4->17（升级到48）使用不同句柄切换操作预期缓存正常（（merge||replace）&批量删除）
TEST_F(IndexEnhance, DML_107_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "DML_107_normal_local";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&dmlConn2, &dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/DML_107_normal_local.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "生成随机数据.");
    GetArtNodeDataIndfo(&g_artNodeInfo, 17, true, true, true, true);

    // 批量写入数据
    AW_FUN_Log(LOG_STEP, "1.批量写入数据触发节点扩容.");
    int32_t recordNum = g_artNodeInfo.nodeCount4;
    ret = BatchInsertLocalTable(dmlConn1, dmlStmt1, labelName, 10, GMC_OPERATION_INSERT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = BatchInsertLocalTable(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_REPLACE, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanNormalByPk(dmlStmt3, labelName, recordNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点增加.");
    g_artNodeLabelInfo.labelName = labelName;
    g_artNodeLabelInfo.indexType = (char *)"local";
    g_artNodeLabelInfo.nodeCount.node4Count = 0;
    g_artNodeLabelInfo.nodeCount.node16Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;

    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期node48节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.nodeCount.node48Count);
    if (g_artNodeLabelInfo.nodeCount.node48Count == 0) {
        SystemSnprintf("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -f LABEL_NAME=%s", labelName);
    }

    // 删除数据
    AW_FUN_Log(LOG_STEP, "删除数据.");
    ret = DeleteDataUntilExpandLocal(labelName, recordNum, dmlConn2, dmlStmt3, SetPkValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanNormalByPk(dmlStmt1, labelName, recordNum, 0, 1, 0, 24, 0);
    recordNum = 0;
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点释放.");
    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期free node48节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode48Count);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode4Count);
    SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放申请的内存
    free(g_artNodeInfo.upNode);

    free(g_artNodeInfo.normalNode1);

    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 019.（local索引）,semi模式，进行art索引树形结构的四层扩缩容 一次性扩容
// node（48->49（升级到255）使用不同句柄切换操作预期缓存正常（insert&批量删除）
TEST_F(IndexEnhance, DML_107_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "DML_107_normal_local";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&dmlConn2, &dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/DML_107_normal_local.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "生成随机数据.");
    GetArtNodeDataIndfo(&g_artNodeInfo, 49, true, true, true, false);

    // 批量写入数据
    AW_FUN_Log(LOG_STEP, "1.批量写入数据触发节点扩容.");
    int32_t recordNum = g_artNodeInfo.nodeCount4;
    ret = BatchInsertLocalTable(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_INSERT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanNormalByPk(dmlStmt3, labelName, recordNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点增加.");
    g_artNodeLabelInfo.labelName = labelName;
    g_artNodeLabelInfo.indexType = (char *)"local";
    g_artNodeLabelInfo.nodeCount.node4Count = 0;
    g_artNodeLabelInfo.nodeCount.node256Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode256Count = 0;

    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期node节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.nodeCount.node256Count);
    if (g_artNodeLabelInfo.nodeCount.node256Count == 0) {
        SystemSnprintf("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -f LABEL_NAME=%s", labelName);
    }

    // 删除数据
    AW_FUN_Log(LOG_STEP, "删除数据.");
    ret = DeleteDataUntilExpandLocal(labelName, recordNum, dmlConn2, dmlStmt3, SetPkValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanNormalByPk(dmlStmt1, labelName, recordNum, 0, 1, 0, 24, 0);
    recordNum = 0;
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点释放.");
    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期free node48节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode256Count);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode4Count);
    SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放申请的内存
    free(g_artNodeInfo.upNode);
    free(g_artNodeInfo.normalNode1);

    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 020.（local索引）,semi模式，进行art索引树形结构的四层扩缩容
// 一次性扩容node（48->49（升级到255）使用不同句柄切换操作预期缓存正常（（merge||replace）&批量删除）
TEST_F(IndexEnhance, DML_107_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "DML_107_normal_local";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&dmlConn2, &dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/DML_107_normal_local.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "生成随机数据.");
    GetArtNodeDataIndfo(&g_artNodeInfo, 49, true, true, true, false);

    // 批量写入数据
    AW_FUN_Log(LOG_STEP, "1.批量写入数据触发节点扩容.");
    int32_t recordNum = g_artNodeInfo.nodeCount4;
    ret = BatchInsertLocalTable(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_MERGE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertLocalTable(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_REPLACE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanNormalByPk(dmlStmt3, labelName, recordNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点增加.");
    g_artNodeLabelInfo.labelName = labelName;
    g_artNodeLabelInfo.indexType = (char *)"local";
    g_artNodeLabelInfo.nodeCount.node4Count = 0;
    g_artNodeLabelInfo.nodeCount.node256Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode256Count = 0;

    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期node节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.nodeCount.node256Count);
    if (g_artNodeLabelInfo.nodeCount.node256Count == 0) {
        SystemSnprintf("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -f LABEL_NAME=%s", labelName);
    }

    // 删除数据
    AW_FUN_Log(LOG_STEP, "删除数据.");
    ret = DeleteDataUntilExpandLocal(labelName, recordNum, dmlConn2, dmlStmt3, SetPkValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanNormalByPk(dmlStmt1, labelName, recordNum, 0, 1, 0, 24, 0);
    recordNum = 0;
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点释放.");
    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期free node48节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode256Count);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode4Count);
    SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放申请的内存
    free(g_artNodeInfo.upNode);
    free(g_artNodeInfo.normalNode1);

    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 021.（local索引）,semi模式，进行art索引树形结构的四层扩缩容
// 三次扩容node（4->49（升级到255）使用不同句柄切换操作预期缓存正常（insert&批量删除）
TEST_F(IndexEnhance, DML_107_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "DML_107_normal_local";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&dmlConn2, &dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/DML_107_normal_local.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "生成随机数据.");
    GetArtNodeDataIndfo(&g_artNodeInfo, 49, true, true, true, false);

    // 批量写入数据
    AW_FUN_Log(LOG_STEP, "1.批量写入数据触发节点扩容.");
    int32_t recordNum = g_artNodeInfo.nodeCount4;
    ret = BatchInsertLocalTable(dmlConn1, dmlStmt1, labelName, 10, GMC_OPERATION_INSERT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertLocalTable(dmlConn1, dmlStmt1, labelName, 40, GMC_OPERATION_INSERT, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertLocalTable(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_INSERT, 40);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanNormalByPk(dmlStmt3, labelName, recordNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点增加.");
    g_artNodeLabelInfo.labelName = labelName;
    g_artNodeLabelInfo.indexType = (char *)"local";
    g_artNodeLabelInfo.nodeCount.node4Count = 0;
    g_artNodeLabelInfo.nodeCount.node256Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode256Count = 0;

    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期node节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.nodeCount.node256Count);
    if (g_artNodeLabelInfo.nodeCount.node256Count == 0) {
        SystemSnprintf("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -f LABEL_NAME=%s", labelName);
    }

    // 删除数据
    AW_FUN_Log(LOG_STEP, "删除数据.");
    ret = DeleteDataUntilExpandLocal(labelName, recordNum, dmlConn2, dmlStmt3, SetPkValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanNormalByPk(dmlStmt1, labelName, recordNum, 0, 1, 0, 24, 0);
    recordNum = 0;
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点释放.");
    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期free node48节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode256Count);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode4Count);
    SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放申请的内存
    free(g_artNodeInfo.upNode);
    free(g_artNodeInfo.normalNode1);

    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 022.（local索引）,semi模式，进行art索引树形结构的四层扩缩容 三次扩容
// node（4->49（升级到255）使用不同句柄切换操作预期缓存正常（（merge||replace）&批量删除）
TEST_F(IndexEnhance, DML_107_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "DML_107_normal_local";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&dmlConn2, &dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/DML_107_normal_local.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "生成随机数据.");
    GetArtNodeDataIndfo(&g_artNodeInfo, 49, true, true, true, false);

    // 批量写入数据
    AW_FUN_Log(LOG_STEP, "1.批量写入数据触发节点扩容.");
    int32_t recordNum = g_artNodeInfo.nodeCount4;
    ret = BatchInsertLocalTable(dmlConn1, dmlStmt1, labelName, 10, GMC_OPERATION_MERGE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertLocalTable(dmlConn1, dmlStmt1, labelName, 40, GMC_OPERATION_REPLACE, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertLocalTable(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_MERGE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanNormalByPk(dmlStmt3, labelName, recordNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点增加.");
    g_artNodeLabelInfo.labelName = labelName;
    g_artNodeLabelInfo.indexType = (char *)"local";
    g_artNodeLabelInfo.nodeCount.node4Count = 0;
    g_artNodeLabelInfo.nodeCount.node256Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode256Count = 0;

    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期node节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.nodeCount.node256Count);
    if (g_artNodeLabelInfo.nodeCount.node256Count == 0) {
        SystemSnprintf("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -f LABEL_NAME=%s", labelName);
    }

    // 删除数据
    AW_FUN_Log(LOG_STEP, "删除数据.");
    ret = DeleteDataUntilExpandLocal(labelName, recordNum, dmlConn2, dmlStmt3, SetPkValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanNormalByPk(dmlStmt1, labelName, recordNum, 0, 1, 0, 24, 0);
    recordNum = 0;
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点释放.");
    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期free node48节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode256Count);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode4Count);
    SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放申请的内存
    free(g_artNodeInfo.upNode);
    free(g_artNodeInfo.normalNode1);

    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 023.（local索引）,semi模式，进行art索引树形结构的四层扩缩容 三次扩容
// node（4->49（升级到255）使用不同句柄切换操作预期缓存正常（insert&truncate（同步||异步）删除）
TEST_F(IndexEnhance, DML_107_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "DML_107_normal_local";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 使用异步连接
    ret = testGmcConnect(&dmlConn2, &dmlStmt3, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/DML_107_normal_local.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "生成随机数据.");
    GetArtNodeDataIndfo(&g_artNodeInfo, 49, true, true, true, false);

    // 批量写入数据
    AW_FUN_Log(LOG_STEP, "1.批量写入数据触发节点扩容.");
    int32_t recordNum = g_artNodeInfo.nodeCount4;
    ret = BatchInsertLocalTable(dmlConn1, dmlStmt1, labelName, 10, GMC_OPERATION_INSERT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertLocalTable(dmlConn1, dmlStmt1, labelName, 40, GMC_OPERATION_INSERT, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertLocalTable(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_INSERT, 40);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanNormalByPk(dmlStmt2, labelName, recordNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点增加.");
    g_artNodeLabelInfo.labelName = labelName;
    g_artNodeLabelInfo.indexType = (char *)"local";
    g_artNodeLabelInfo.nodeCount.node4Count = 0;
    g_artNodeLabelInfo.nodeCount.node256Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode256Count = 0;

    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期node节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.nodeCount.node256Count);
    if (g_artNodeLabelInfo.nodeCount.node256Count == 0) {
        SystemSnprintf("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -f LABEL_NAME=%s", labelName);
    }

    // 删除数据
    GmcStmtT *delStmtArray[2] = {dmlStmt2, dmlStmt3};
    GmcConnT *delConnArray[2] = {dmlConn1, dmlConn2};
    int32_t index = GetRandomMode(2);
    if (index == 0) {
        AW_FUN_Log(LOG_STEP, "当前模式:使用相同连接不同句柄");
    } else {
        AW_FUN_Log(LOG_STEP, "当前模式:使用不同连接");
    }
    AW_FUN_Log(LOG_STEP, "删除数据.");
    ret = FrontTruncateDeleteData(
        &labelInfo, delConnArray[index], delStmtArray[index], index, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanNormalByPk(dmlStmt1, labelName, recordNum, 0, 1, 0, 24, 0);
    recordNum = 0;
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点释放.");
    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期free node256节点归0
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode256Count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode4Count);
    SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放申请的内存
    free(g_artNodeInfo.upNode);
    free(g_artNodeInfo.normalNode1);

    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 024.（local索引）,semi模式，进行art索引树形结构的四层扩缩容 三次扩容
// node（4->49（升级到255）使用不同句柄切换操作预期缓存正常（（merge||replace）&truncate（同步||异步）删除）
TEST_F(IndexEnhance, DML_107_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "DML_107_normal_local";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 使用异步连接
    ret = testGmcConnect(&dmlConn2, &dmlStmt3, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/DML_107_normal_local.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "生成随机数据.");
    GetArtNodeDataIndfo(&g_artNodeInfo, 49, true, true, true, false);

    // 批量写入数据
    AW_FUN_Log(LOG_STEP, "1.批量写入数据触发节点扩容.");
    int32_t recordNum = g_artNodeInfo.nodeCount4;
    ret = BatchInsertLocalTable(dmlConn1, dmlStmt1, labelName, 10, GMC_OPERATION_INSERT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertLocalTable(dmlConn1, dmlStmt1, labelName, 40, GMC_OPERATION_REPLACE, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertLocalTable(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_MERGE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanNormalByPk(dmlStmt2, labelName, recordNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点增加.");
    g_artNodeLabelInfo.labelName = labelName;
    g_artNodeLabelInfo.indexType = (char *)"local";
    g_artNodeLabelInfo.nodeCount.node4Count = 0;
    g_artNodeLabelInfo.nodeCount.node256Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode256Count = 0;

    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期node节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.nodeCount.node256Count);
    if (g_artNodeLabelInfo.nodeCount.node256Count == 0) {
        SystemSnprintf("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -f LABEL_NAME=%s", labelName);
    }

    // 删除数据
    GmcStmtT *delStmtArray[2] = {dmlStmt2, dmlStmt3};
    GmcConnT *delConnArray[2] = {dmlConn1, dmlConn2};
    int32_t index = GetRandomMode(2);
    if (index == 0) {
        AW_FUN_Log(LOG_STEP, "当前模式:使用相同连接不同句柄");
    } else {
        AW_FUN_Log(LOG_STEP, "当前模式:使用不同连接");
    }
    AW_FUN_Log(LOG_STEP, "删除数据.");
    ret = FrontTruncateDeleteData(
        &labelInfo, delConnArray[index], delStmtArray[index], index, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanNormalByPk(dmlStmt1, labelName, recordNum, 0, 1, 0, 24, 0);
    recordNum = 0;
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点释放.");
    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期free node256节点归0
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode256Count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode4Count);
    SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放申请的内存
    free(g_artNodeInfo.upNode);
    free(g_artNodeInfo.normalNode1);

    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 025.(hashcluster索引），semi模式，进行art索引树形结构的四层扩缩容
// node（4->5（升级到16）使用不同句柄切换操作预期缓存正常（insert&批量删除）
TEST_F(IndexEnhance, DML_107_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "DML_107_normal_hashcluster";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&dmlConn2, &dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/DML_107_normal_hashcluster.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true,\"isFastReadUncommitted\": false}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "生成随机数据.");
    GetArtNodeDataIndfo(&g_artNodeInfo, 5, true, true, true, true);

    // 批量写入数据
    AW_FUN_Log(LOG_STEP, "1.批量写入数据触发节点扩容.");
    int32_t recordNum = g_artNodeInfo.nodeCount4;
    ret = BatchInsertHashclusterTable(dmlConn1, dmlStmt1, labelName, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanNormalByPk(dmlStmt3, labelName, recordNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点增加.");
    g_artNodeLabelInfo.labelName = labelName;
    g_artNodeLabelInfo.indexType = (char *)"hashcluster";
    g_artNodeLabelInfo.nodeCount.node4Count = 0;
    g_artNodeLabelInfo.nodeCount.node16Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;

    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期node16节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.nodeCount.node16Count);
    if (g_artNodeLabelInfo.nodeCount.node16Count == 0) {
        SystemSnprintf("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -f LABEL_NAME=%s", labelName);
    }

    // 删除数据
    AW_FUN_Log(LOG_STEP, "删除数据.");
    ret = DeleteDataUntilExpandLocal(labelName, recordNum, dmlConn2, dmlStmt3, SetPkValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanNormalByPk(dmlStmt1, labelName, recordNum, 0, 1, 0, 24, 0);
    recordNum = 0;
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点释放.");
    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期free node16节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode16Count);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode4Count);
    SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放申请的内存
    free(g_artNodeInfo.upNode);

    free(g_artNodeInfo.normalNode1);

    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 026.（hashcluster索引）,semi模式，进行art索引树形结构的四层扩缩容
// node（4->5（升级到16）使用不同句柄切换操作预期缓存正常（（merge||replace）&批量删除）
TEST_F(IndexEnhance, DML_107_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "DML_107_normal_hashcluster";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&dmlConn2, &dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/DML_107_normal_hashcluster.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true,\"isFastReadUncommitted\": false}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "生成随机数据.");
    GetArtNodeDataIndfo(&g_artNodeInfo, 5, true, true, true, true);

    // 批量写入数据
    AW_FUN_Log(LOG_STEP, "1.批量写入数据触发节点扩容.");
    int32_t recordNum = g_artNodeInfo.nodeCount4;
    ret = BatchInsertHashclusterTable(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = BatchInsertHashclusterTable(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanNormalByPk(dmlStmt3, labelName, recordNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点增加.");
    g_artNodeLabelInfo.labelName = labelName;
    g_artNodeLabelInfo.indexType = (char *)"hashcluster";
    g_artNodeLabelInfo.nodeCount.node4Count = 0;
    g_artNodeLabelInfo.nodeCount.node16Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;

    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期node16节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.nodeCount.node16Count);
    if (g_artNodeLabelInfo.nodeCount.node16Count == 0) {
        SystemSnprintf("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -f LABEL_NAME=%s", labelName);
    }

    // 删除数据
    AW_FUN_Log(LOG_STEP, "删除数据.");
    ret = DeleteDataUntilExpandLocal(labelName, recordNum, dmlConn2, dmlStmt3, SetPkValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanNormalByPk(dmlStmt1, labelName, recordNum, 0, 1, 0, 24, 0);
    recordNum = 0;
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点释放.");
    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期free node16节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode16Count);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode4Count);
    SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放申请的内存
    free(g_artNodeInfo.upNode);

    free(g_artNodeInfo.normalNode1);

    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 027.（hashcluster索引）,semi模式，进行art索引树形结构的四层扩缩容 一次性扩容
// node（16->17（升级到48）使用不同句柄切换操作预期缓存正常（insert&批量删除）
TEST_F(IndexEnhance, DML_107_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "DML_107_normal_hashcluster";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&dmlConn2, &dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/DML_107_normal_hashcluster.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true,\"isFastReadUncommitted\": false}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "生成随机数据.");
    GetArtNodeDataIndfo(&g_artNodeInfo, 17, true, true, true, true);

    // 批量写入数据
    AW_FUN_Log(LOG_STEP, "1.批量写入数据触发节点扩容.");
    int32_t recordNum = g_artNodeInfo.nodeCount4;
    ret = BatchInsertHashclusterTable(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_INSERT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanNormalByPk(dmlStmt3, labelName, recordNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点增加.");
    g_artNodeLabelInfo.labelName = labelName;
    g_artNodeLabelInfo.indexType = (char *)"hashcluster";
    g_artNodeLabelInfo.nodeCount.node4Count = 0;
    g_artNodeLabelInfo.nodeCount.node16Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;

    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期node16节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.nodeCount.node48Count);
    if (g_artNodeLabelInfo.nodeCount.node48Count == 0) {
        SystemSnprintf("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -f LABEL_NAME=%s", labelName);
    }

    // 删除数据
    AW_FUN_Log(LOG_STEP, "删除数据.");
    ret = DeleteDataUntilExpandLocal(labelName, recordNum, dmlConn2, dmlStmt3, SetPkValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanNormalByPk(dmlStmt1, labelName, recordNum, 0, 1, 0, 24, 0);
    recordNum = 0;
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点释放.");
    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期free node48节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode48Count);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode4Count);
    SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放申请的内存
    free(g_artNodeInfo.upNode);

    free(g_artNodeInfo.normalNode1);

    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 028.（hashcluster索引）,semi模式，进行art索引树形结构的四层扩缩容 一次性扩容
// node（16->17（升级到48）使用不同句柄切换操作预期缓存正常（（merge||replace）&批量删除）
TEST_F(IndexEnhance, DML_107_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "DML_107_normal_hashcluster";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&dmlConn2, &dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/DML_107_normal_hashcluster.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true,\"isFastReadUncommitted\": false}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "生成随机数据.");
    GetArtNodeDataIndfo(&g_artNodeInfo, 17, true, true, true, true);

    // 批量写入数据
    AW_FUN_Log(LOG_STEP, "1.批量写入数据触发节点扩容.");
    int32_t recordNum = g_artNodeInfo.nodeCount4;
    ret = BatchInsertHashclusterTable(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_MERGE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertHashclusterTable(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_REPLACE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanNormalByPk(dmlStmt3, labelName, recordNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点增加.");
    g_artNodeLabelInfo.labelName = labelName;
    g_artNodeLabelInfo.indexType = (char *)"hashcluster";
    g_artNodeLabelInfo.nodeCount.node4Count = 0;
    g_artNodeLabelInfo.nodeCount.node16Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;

    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期node16节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.nodeCount.node48Count);
    if (g_artNodeLabelInfo.nodeCount.node48Count == 0) {
        SystemSnprintf("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -f LABEL_NAME=%s", labelName);
    }

    // 删除数据
    AW_FUN_Log(LOG_STEP, "删除数据.");
    ret = DeleteDataUntilExpandLocal(labelName, recordNum, dmlConn2, dmlStmt3, SetPkValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanNormalByPk(dmlStmt1, labelName, recordNum, 0, 1, 0, 24, 0);
    recordNum = 0;
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点释放.");
    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期free node48节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode48Count);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode4Count);
    SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放申请的内存
    free(g_artNodeInfo.upNode);

    free(g_artNodeInfo.normalNode1);

    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 029.（hashcluster索引）,semi模式，进行art索引树形结构的四层扩缩容 两次扩容
// node（4->17（升级到48）使用不同句柄切换操作预期缓存正常（insert&批量删除）
TEST_F(IndexEnhance, DML_107_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "DML_107_normal_hashcluster";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&dmlConn2, &dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/DML_107_normal_hashcluster.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true,\"isFastReadUncommitted\": false}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "生成随机数据.");
    GetArtNodeDataIndfo(&g_artNodeInfo, 17, true, true, true, true);

    // 批量写入数据
    AW_FUN_Log(LOG_STEP, "1.批量写入数据触发节点扩容.");
    int32_t recordNum = g_artNodeInfo.nodeCount4;
    ret = BatchInsertHashclusterTable(dmlConn1, dmlStmt1, labelName, 10, GMC_OPERATION_INSERT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = BatchInsertHashclusterTable(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_INSERT, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanNormalByPk(dmlStmt3, labelName, recordNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点增加.");
    g_artNodeLabelInfo.labelName = labelName;
    g_artNodeLabelInfo.indexType = (char *)"hashcluster";
    g_artNodeLabelInfo.nodeCount.node4Count = 0;
    g_artNodeLabelInfo.nodeCount.node16Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;

    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期node48节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.nodeCount.node48Count);
    if (g_artNodeLabelInfo.nodeCount.node48Count == 0) {
        SystemSnprintf("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -f LABEL_NAME=%s", labelName);
    }

    // 删除数据
    AW_FUN_Log(LOG_STEP, "删除数据.");
    ret = DeleteDataUntilExpandLocal(labelName, recordNum, dmlConn2, dmlStmt3, SetPkValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanNormalByPk(dmlStmt1, labelName, recordNum, 0, 1, 0, 24, 0);
    recordNum = 0;
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点释放.");
    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期free node48节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode48Count);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode4Count);
    SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放申请的内存
    free(g_artNodeInfo.upNode);

    free(g_artNodeInfo.normalNode1);

    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 030.（hashcluster索引）,semi模式，进行art索引树形结构的四层扩缩容 两次扩容
// node（4->17（升级到48）使用不同句柄切换操作预期缓存正常（（merge||replace）&批量删除）
TEST_F(IndexEnhance, DML_107_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "DML_107_normal_hashcluster";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&dmlConn2, &dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/DML_107_normal_hashcluster.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true,\"isFastReadUncommitted\": false}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "生成随机数据.");
    GetArtNodeDataIndfo(&g_artNodeInfo, 17, true, true, true, true);

    // 批量写入数据
    AW_FUN_Log(LOG_STEP, "1.批量写入数据触发节点扩容.");
    int32_t recordNum = g_artNodeInfo.nodeCount4;
    ret = BatchInsertHashclusterTable(dmlConn1, dmlStmt1, labelName, 10, GMC_OPERATION_INSERT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = BatchInsertHashclusterTable(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_REPLACE, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanNormalByPk(dmlStmt3, labelName, recordNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点增加.");
    g_artNodeLabelInfo.labelName = labelName;
    g_artNodeLabelInfo.indexType = (char *)"hashcluster";
    g_artNodeLabelInfo.nodeCount.node4Count = 0;
    g_artNodeLabelInfo.nodeCount.node16Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;

    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期node48节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.nodeCount.node48Count);
    if (g_artNodeLabelInfo.nodeCount.node48Count == 0) {
        SystemSnprintf("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -f LABEL_NAME=%s", labelName);
    }

    // 删除数据
    AW_FUN_Log(LOG_STEP, "删除数据.");
    ret = DeleteDataUntilExpandLocal(labelName, recordNum, dmlConn2, dmlStmt3, SetPkValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanNormalByPk(dmlStmt1, labelName, recordNum, 0, 1, 0, 24, 0);
    recordNum = 0;
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点释放.");
    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期free node48节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode48Count);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode4Count);
    SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放申请的内存
    free(g_artNodeInfo.upNode);

    free(g_artNodeInfo.normalNode1);

    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 031.（hashcluster索引）,semi模式，进行art索引树形结构的四层扩缩容 一次性扩容
// node（48->49（升级到255）使用不同句柄切换操作预期缓存正常（insert&批量删除）
TEST_F(IndexEnhance, DML_107_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "DML_107_normal_hashcluster";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&dmlConn2, &dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/DML_107_normal_hashcluster.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true,\"isFastReadUncommitted\": false}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "生成随机数据.");
    GetArtNodeDataIndfo(&g_artNodeInfo, 49, true, true, true, false);

    // 批量写入数据
    AW_FUN_Log(LOG_STEP, "1.批量写入数据触发节点扩容.");
    int32_t recordNum = g_artNodeInfo.nodeCount4;
    ret = BatchInsertHashclusterTable(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_INSERT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanNormalByPk(dmlStmt3, labelName, recordNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点增加.");
    g_artNodeLabelInfo.labelName = labelName;
    g_artNodeLabelInfo.indexType = (char *)"hashcluster";
    g_artNodeLabelInfo.nodeCount.node4Count = 0;
    g_artNodeLabelInfo.nodeCount.node256Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode256Count = 0;

    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期node节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.nodeCount.node256Count);
    if (g_artNodeLabelInfo.nodeCount.node256Count == 0) {
        SystemSnprintf("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -f LABEL_NAME=%s", labelName);
    }

    // 删除数据
    AW_FUN_Log(LOG_STEP, "删除数据.");
    ret = DeleteDataUntilExpandLocal(labelName, recordNum, dmlConn2, dmlStmt3, SetPkValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanNormalByPk(dmlStmt1, labelName, recordNum, 0, 1, 0, 24, 0);
    recordNum = 0;
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点释放.");
    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期free node48节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode256Count);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode4Count);
    SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放申请的内存
    free(g_artNodeInfo.upNode);
    free(g_artNodeInfo.normalNode1);

    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 032.（hashcluster索引）,semi模式，进行art索引树形结构的四层扩缩容
// 一次性扩容node（48->49（升级到255）使用不同句柄切换操作预期缓存正常（（merge||replace）&批量删除）
TEST_F(IndexEnhance, DML_107_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "DML_107_normal_hashcluster";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&dmlConn2, &dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/DML_107_normal_hashcluster.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true,\"isFastReadUncommitted\": false}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "生成随机数据.");
    GetArtNodeDataIndfo(&g_artNodeInfo, 49, true, true, true, false);

    // 批量写入数据
    AW_FUN_Log(LOG_STEP, "1.批量写入数据触发节点扩容.");
    int32_t recordNum = g_artNodeInfo.nodeCount4;
    ret = BatchInsertHashclusterTable(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_MERGE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertHashclusterTable(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_REPLACE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanNormalByPk(dmlStmt3, labelName, recordNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点增加.");
    g_artNodeLabelInfo.labelName = labelName;
    g_artNodeLabelInfo.indexType = (char *)"hashcluster";
    g_artNodeLabelInfo.nodeCount.node4Count = 0;
    g_artNodeLabelInfo.nodeCount.node256Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode256Count = 0;

    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期node节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.nodeCount.node256Count);
    if (g_artNodeLabelInfo.nodeCount.node256Count == 0) {
        SystemSnprintf("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -f LABEL_NAME=%s", labelName);
    }

    // 删除数据
    AW_FUN_Log(LOG_STEP, "删除数据.");
    ret = DeleteDataUntilExpandLocal(labelName, recordNum, dmlConn2, dmlStmt3, SetPkValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanNormalByPk(dmlStmt1, labelName, recordNum, 0, 1, 0, 24, 0);
    recordNum = 0;
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点释放.");
    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期free node48节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode256Count);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode4Count);
    SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放申请的内存
    free(g_artNodeInfo.upNode);
    free(g_artNodeInfo.normalNode1);

    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 033.（hashcluster索引）,semi模式，进行art索引树形结构的四层扩缩容
// 三次扩容node（4->49（升级到255）使用不同句柄切换操作预期缓存正常（insert&批量删除）
TEST_F(IndexEnhance, DML_107_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "DML_107_normal_hashcluster";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&dmlConn2, &dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/DML_107_normal_hashcluster.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true,\"isFastReadUncommitted\": false}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "生成随机数据.");
    GetArtNodeDataIndfo(&g_artNodeInfo, 49, true, true, true, false);

    // 批量写入数据
    AW_FUN_Log(LOG_STEP, "1.批量写入数据触发节点扩容.");
    int32_t recordNum = g_artNodeInfo.nodeCount4;
    ret = BatchInsertHashclusterTable(dmlConn1, dmlStmt1, labelName, 10, GMC_OPERATION_INSERT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertHashclusterTable(dmlConn1, dmlStmt1, labelName, 40, GMC_OPERATION_INSERT, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertHashclusterTable(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_INSERT, 40);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanNormalByPk(dmlStmt3, labelName, recordNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点增加.");
    g_artNodeLabelInfo.labelName = labelName;
    g_artNodeLabelInfo.indexType = (char *)"hashcluster";
    g_artNodeLabelInfo.nodeCount.node4Count = 0;
    g_artNodeLabelInfo.nodeCount.node256Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode256Count = 0;

    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期node节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.nodeCount.node256Count);
    if (g_artNodeLabelInfo.nodeCount.node256Count == 0) {
        SystemSnprintf("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -f LABEL_NAME=%s", labelName);
    }

    // 删除数据
    AW_FUN_Log(LOG_STEP, "删除数据.");
    ret = DeleteDataUntilExpandLocal(labelName, recordNum, dmlConn2, dmlStmt3, SetPkValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanNormalByPk(dmlStmt1, labelName, recordNum, 0, 1, 0, 24, 0);
    recordNum = 0;
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点释放.");
    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期free node48节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode256Count);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode4Count);
    SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放申请的内存
    free(g_artNodeInfo.upNode);
    free(g_artNodeInfo.normalNode1);

    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 034.（hashcluster索引）,semi模式，进行art索引树形结构的四层扩缩容 三次扩容
// node（4->49（升级到255）使用不同句柄切换操作预期缓存正常（（merge||replace）&批量删除）
TEST_F(IndexEnhance, DML_107_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "DML_107_normal_hashcluster";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&dmlConn2, &dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/DML_107_normal_hashcluster.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true,\"isFastReadUncommitted\": false}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "生成随机数据.");
    GetArtNodeDataIndfo(&g_artNodeInfo, 49, true, true, true, false);

    // 批量写入数据
    AW_FUN_Log(LOG_STEP, "1.批量写入数据触发节点扩容.");
    int32_t recordNum = g_artNodeInfo.nodeCount4;
    ret = BatchInsertHashclusterTable(dmlConn1, dmlStmt1, labelName, 10, GMC_OPERATION_MERGE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertHashclusterTable(dmlConn1, dmlStmt1, labelName, 40, GMC_OPERATION_REPLACE, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertHashclusterTable(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_MERGE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanNormalByPk(dmlStmt3, labelName, recordNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点增加.");
    g_artNodeLabelInfo.labelName = labelName;
    g_artNodeLabelInfo.indexType = (char *)"hashcluster";
    g_artNodeLabelInfo.nodeCount.node4Count = 0;
    g_artNodeLabelInfo.nodeCount.node256Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode256Count = 0;

    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期node节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.nodeCount.node256Count);
    if (g_artNodeLabelInfo.nodeCount.node256Count == 0) {
        SystemSnprintf("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -f LABEL_NAME=%s", labelName);
    }

    // 删除数据
    AW_FUN_Log(LOG_STEP, "删除数据.");
    ret = DeleteDataUntilExpandLocal(labelName, recordNum, dmlConn2, dmlStmt3, SetPkValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanNormalByPk(dmlStmt1, labelName, recordNum, 0, 1, 0, 24, 0);
    recordNum = 0;
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点释放.");
    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期free node48节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode256Count);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode4Count);
    SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放申请的内存
    free(g_artNodeInfo.upNode);
    free(g_artNodeInfo.normalNode1);

    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 035.（hashcluster索引）,semi模式，进行art索引树形结构的四层扩缩容 三次扩容
// node（4->49（升级到255）使用不同句柄切换操作预期缓存正常（insert&truncate（同步||异步）删除）
TEST_F(IndexEnhance, DML_107_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "DML_107_normal_hashcluster";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 使用异步连接
    ret = testGmcConnect(&dmlConn2, &dmlStmt3, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/DML_107_normal_hashcluster.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true,\"isFastReadUncommitted\": false}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "生成随机数据.");
    GetArtNodeDataIndfo(&g_artNodeInfo, 49, true, true, true, false);

    // 批量写入数据
    AW_FUN_Log(LOG_STEP, "1.批量写入数据触发节点扩容.");
    int32_t recordNum = g_artNodeInfo.nodeCount4;
    ret = BatchInsertHashclusterTable(dmlConn1, dmlStmt1, labelName, 10, GMC_OPERATION_INSERT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertHashclusterTable(dmlConn1, dmlStmt1, labelName, 40, GMC_OPERATION_INSERT, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertHashclusterTable(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_INSERT, 40);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanNormalByPk(dmlStmt2, labelName, recordNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点增加.");
    g_artNodeLabelInfo.labelName = labelName;
    g_artNodeLabelInfo.indexType = (char *)"hashcluster";
    g_artNodeLabelInfo.nodeCount.node4Count = 0;
    g_artNodeLabelInfo.nodeCount.node256Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode256Count = 0;

    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期node节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.nodeCount.node256Count);
    if (g_artNodeLabelInfo.nodeCount.node256Count == 0) {
        SystemSnprintf("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -f LABEL_NAME=%s", labelName);
    }

    // 删除数据
    GmcStmtT *delStmtArray[2] = {dmlStmt2, dmlStmt3};
    GmcConnT *delConnArray[2] = {dmlConn1, dmlConn2};
    int32_t index = GetRandomMode(2);
    if (index == 0) {
        AW_FUN_Log(LOG_STEP, "当前模式:使用相同连接不同句柄");
    } else {
        AW_FUN_Log(LOG_STEP, "当前模式:使用不同连接");
    }
    AW_FUN_Log(LOG_STEP, "删除数据.");
    ret = FrontTruncateDeleteData(
        &labelInfo, delConnArray[index], delStmtArray[index], index, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanNormalByPk(dmlStmt1, labelName, recordNum, 0, 1, 0, 24, 0);
    recordNum = 0;
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点释放.");
    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期free node256节点归0
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode256Count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode4Count);
    SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放申请的内存
    free(g_artNodeInfo.upNode);
    free(g_artNodeInfo.normalNode1);

    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 036.（hashcluster索引）,semi模式，进行art索引树形结构的四层扩缩容 三次扩容
// node（4->49（升级到255）使用不同句柄切换操作预期缓存正常（（merge||replace）&truncate（同步||异步）删除）
TEST_F(IndexEnhance, DML_107_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "DML_107_normal_hashcluster";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 使用异步连接
    ret = testGmcConnect(&dmlConn2, &dmlStmt3, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/DML_107_normal_hashcluster.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true,\"isFastReadUncommitted\": false}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "生成随机数据.");
    GetArtNodeDataIndfo(&g_artNodeInfo, 49, true, true, true, false);

    // 批量写入数据
    AW_FUN_Log(LOG_STEP, "1.批量写入数据触发节点扩容.");
    int32_t recordNum = g_artNodeInfo.nodeCount4;
    ret = BatchInsertHashclusterTable(dmlConn1, dmlStmt1, labelName, 10, GMC_OPERATION_INSERT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertHashclusterTable(dmlConn1, dmlStmt1, labelName, 40, GMC_OPERATION_REPLACE, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertHashclusterTable(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_MERGE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanNormalByPk(dmlStmt2, labelName, recordNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点增加.");
    g_artNodeLabelInfo.labelName = labelName;
    g_artNodeLabelInfo.indexType = (char *)"hashcluster";
    g_artNodeLabelInfo.nodeCount.node4Count = 0;
    g_artNodeLabelInfo.nodeCount.node256Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode256Count = 0;

    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期node节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.nodeCount.node256Count);
    if (g_artNodeLabelInfo.nodeCount.node256Count == 0) {
        SystemSnprintf("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -f LABEL_NAME=%s", labelName);
    }

    // 删除数据
    GmcStmtT *delStmtArray[2] = {dmlStmt2, dmlStmt3};
    GmcConnT *delConnArray[2] = {dmlConn1, dmlConn2};
    int32_t index = GetRandomMode(2);
    if (index == 0) {
        AW_FUN_Log(LOG_STEP, "当前模式:使用相同连接不同句柄");
    } else {
        AW_FUN_Log(LOG_STEP, "当前模式:使用不同连接");
    }
    AW_FUN_Log(LOG_STEP, "删除数据.");
    ret = FrontTruncateDeleteData(
        &labelInfo, delConnArray[index], delStmtArray[index], index, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanNormalByPk(dmlStmt1, labelName, recordNum, 0, 1, 0, 24, 0);
    recordNum = 0;
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点释放.");
    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期free node256节点归0
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode256Count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode4Count);
    SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放申请的内存
    free(g_artNodeInfo.upNode);
    free(g_artNodeInfo.normalNode1);

    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 037.lpmv4索引，semi模式，进行art索引树形结构的四层扩缩容
// node（4->5（升级到16）使用不同句柄切换操作预期缓存正常（insert&批量删除）
TEST_F(IndexEnhance, DML_107_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "ip4forward_lpm4";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&dmlConn2, &dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/ip4forward_lpm4.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true,\"isFastReadUncommitted\": false}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "生成随机数据.");
    GetArtNodeDataIndfo(&g_artNodeInfo, 5, true, true, true, true);

    // 批量写入数据
    AW_FUN_Log(LOG_STEP, "1.批量写入数据触发节点扩容.");
    int32_t recordNum = g_artNodeInfo.nodeCount4;
    ret = BatchInsertLpmV4(dmlConn1, dmlStmt1, labelName, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanLpmv4ByPk(dmlStmt3, labelName, recordNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点增加.");
    g_artNodeLabelInfo.labelName = labelName;
    g_artNodeLabelInfo.indexType = (char *)"lpm4_tree_bitmap";
    g_artNodeLabelInfo.nodeCount.node4Count = 0;
    g_artNodeLabelInfo.nodeCount.node16Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;

    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期node16节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.nodeCount.node16Count);
    if (g_artNodeLabelInfo.nodeCount.node16Count == 0) {
        SystemSnprintf("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -f LABEL_NAME=%s", labelName);
    }

    // 删除数据
    AW_FUN_Log(LOG_STEP, "删除数据.");
    ret = DeleteDataLpm(labelName, recordNum, dmlConn2, dmlStmt3, SetLpmV4PkIndexValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanLpmv4ByPk(dmlStmt1, labelName, recordNum, 0, 1, 0, 24, 0);
    recordNum = 0;
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点释放.");
    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期free node16节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode16Count);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode4Count);
    SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放申请的内存
    free(g_artNodeInfo.upNode);

    free(g_artNodeInfo.normalNode1);

    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 038.（lpmv4索引）,semi模式，进行art索引树形结构的四层扩缩容
// node（4->5（升级到16）使用不同句柄切换操作预期缓存正常（（merge||replace）&批量删除）
TEST_F(IndexEnhance, DML_107_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "ip4forward_lpm4";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&dmlConn2, &dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/ip4forward_lpm4.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "生成随机数据.");
    GetArtNodeDataIndfo(&g_artNodeInfo, 5, true, true, true, true);

    // 批量写入数据
    AW_FUN_Log(LOG_STEP, "1.批量写入数据触发节点扩容.");
    int32_t recordNum = g_artNodeInfo.nodeCount4;
    ret = BatchInsertLpmV4(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = BatchInsertLpmV4(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanLpmv4ByPk(dmlStmt3, labelName, recordNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点增加.");
    g_artNodeLabelInfo.labelName = labelName;
    g_artNodeLabelInfo.indexType = (char *)"lpm4_tree_bitmap";
    g_artNodeLabelInfo.nodeCount.node4Count = 0;
    g_artNodeLabelInfo.nodeCount.node16Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;

    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期node16节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.nodeCount.node16Count);
    if (g_artNodeLabelInfo.nodeCount.node16Count == 0) {
        SystemSnprintf("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -f LABEL_NAME=%s", labelName);
    }

    // 删除数据
    AW_FUN_Log(LOG_STEP, "删除数据.");
    ret = DeleteDataLpm(labelName, recordNum, dmlConn2, dmlStmt3, SetLpmV4PkIndexValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanLpmv4ByPk(dmlStmt1, labelName, recordNum, 0, 1, 0, 24, 0);
    recordNum = 0;
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点释放.");
    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期free node16节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode16Count);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode4Count);
    SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放申请的内存
    free(g_artNodeInfo.upNode);

    free(g_artNodeInfo.normalNode1);

    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 039.（lpmv4索引）,semi模式，进行art索引树形结构的四层扩缩容 一次性扩容
// node（16->17（升级到48）使用不同句柄切换操作预期缓存正常（insert&批量删除）
TEST_F(IndexEnhance, DML_107_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "ip4forward_lpm4";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&dmlConn2, &dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/ip4forward_lpm4.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "生成随机数据.");
    GetArtNodeDataIndfo(&g_artNodeInfo, 17, true, true, true, true);

    // 批量写入数据
    AW_FUN_Log(LOG_STEP, "1.批量写入数据触发节点扩容.");
    int32_t recordNum = g_artNodeInfo.nodeCount4;
    ret = BatchInsertLpmV4(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_INSERT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanLpmv4ByPk(dmlStmt3, labelName, recordNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点增加.");
    g_artNodeLabelInfo.labelName = labelName;
    g_artNodeLabelInfo.indexType = (char *)"lpm4_tree_bitmap";
    g_artNodeLabelInfo.nodeCount.node4Count = 0;
    g_artNodeLabelInfo.nodeCount.node16Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;

    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期node16节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.nodeCount.node48Count);
    if (g_artNodeLabelInfo.nodeCount.node48Count == 0) {
        SystemSnprintf("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -f LABEL_NAME=%s", labelName);
    }

    // 删除数据
    AW_FUN_Log(LOG_STEP, "删除数据.");
    ret = DeleteDataLpm(labelName, recordNum, dmlConn2, dmlStmt3, SetLpmV4PkIndexValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanLpmv4ByPk(dmlStmt1, labelName, recordNum, 0, 1, 0, 24, 0);
    recordNum = 0;
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点释放.");
    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期free node48节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode48Count);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode4Count);
    SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放申请的内存
    free(g_artNodeInfo.upNode);

    free(g_artNodeInfo.normalNode1);

    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 040.（lpmv4索引）,semi模式，进行art索引树形结构的四层扩缩容 一次性扩容
// node（16->17（升级到48）使用不同句柄切换操作预期缓存正常（（merge||replace）&批量删除）
TEST_F(IndexEnhance, DML_107_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "ip4forward_lpm4";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&dmlConn2, &dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/ip4forward_lpm4.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "生成随机数据.");
    GetArtNodeDataIndfo(&g_artNodeInfo, 17, true, true, true, true);

    // 批量写入数据
    AW_FUN_Log(LOG_STEP, "1.批量写入数据触发节点扩容.");
    int32_t recordNum = g_artNodeInfo.nodeCount4;
    ret = BatchInsertLpmV4(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_MERGE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertLpmV4(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_REPLACE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanLpmv4ByPk(dmlStmt3, labelName, recordNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点增加.");
    g_artNodeLabelInfo.labelName = labelName;
    g_artNodeLabelInfo.indexType = (char *)"lpm4_tree_bitmap";
    g_artNodeLabelInfo.nodeCount.node4Count = 0;
    g_artNodeLabelInfo.nodeCount.node16Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;

    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期node16节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.nodeCount.node48Count);
    if (g_artNodeLabelInfo.nodeCount.node48Count == 0) {
        SystemSnprintf("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -f LABEL_NAME=%s", labelName);
    }

    // 删除数据
    AW_FUN_Log(LOG_STEP, "删除数据.");
    ret = DeleteDataLpm(labelName, recordNum, dmlConn2, dmlStmt3, SetLpmV4PkIndexValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanLpmv4ByPk(dmlStmt1, labelName, recordNum, 0, 1, 0, 24, 0);
    recordNum = 0;
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点释放.");
    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期free node48节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode48Count);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode4Count);
    SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放申请的内存
    free(g_artNodeInfo.upNode);

    free(g_artNodeInfo.normalNode1);

    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 041.（lpmv4索引）,semi模式，进行art索引树形结构的四层扩缩容 两次扩容
// node（4->17（升级到48）使用不同句柄切换操作预期缓存正常（insert&批量删除）
TEST_F(IndexEnhance, DML_107_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "ip4forward_lpm4";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&dmlConn2, &dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/ip4forward_lpm4.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "生成随机数据.");
    GetArtNodeDataIndfo(&g_artNodeInfo, 17, true, true, true, true);

    // 批量写入数据
    AW_FUN_Log(LOG_STEP, "1.批量写入数据触发节点扩容.");
    int32_t recordNum = g_artNodeInfo.nodeCount4;
    ret = BatchInsertLpmV4(dmlConn1, dmlStmt1, labelName, 10, GMC_OPERATION_INSERT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = BatchInsertLpmV4(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_INSERT, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanLpmv4ByPk(dmlStmt3, labelName, recordNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点增加.");
    g_artNodeLabelInfo.labelName = labelName;
    g_artNodeLabelInfo.indexType = (char *)"lpm4_tree_bitmap";
    g_artNodeLabelInfo.nodeCount.node4Count = 0;
    g_artNodeLabelInfo.nodeCount.node16Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;

    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期node48节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.nodeCount.node48Count);
    if (g_artNodeLabelInfo.nodeCount.node48Count == 0) {
        SystemSnprintf("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -f LABEL_NAME=%s", labelName);
    }

    // 删除数据
    AW_FUN_Log(LOG_STEP, "删除数据.");
    ret = DeleteDataLpm(labelName, recordNum, dmlConn2, dmlStmt3, SetLpmV4PkIndexValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanLpmv4ByPk(dmlStmt1, labelName, recordNum, 0, 1, 0, 24, 0);
    recordNum = 0;
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点释放.");
    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期free node48节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode48Count);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode4Count);
    SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放申请的内存
    free(g_artNodeInfo.upNode);

    free(g_artNodeInfo.normalNode1);

    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 042.（lpmv4索引）,semi模式，进行art索引树形结构的四层扩缩容 两次扩容
// node（4->17（升级到48）使用不同句柄切换操作预期缓存正常（（merge||replace）&批量删除）
TEST_F(IndexEnhance, DML_107_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "ip4forward_lpm4";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&dmlConn2, &dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/ip4forward_lpm4.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "生成随机数据.");
    GetArtNodeDataIndfo(&g_artNodeInfo, 17, true, true, true, true);

    // 批量写入数据
    AW_FUN_Log(LOG_STEP, "1.批量写入数据触发节点扩容.");
    int32_t recordNum = g_artNodeInfo.nodeCount4;
    ret = BatchInsertLpmV4(dmlConn1, dmlStmt1, labelName, 10, GMC_OPERATION_INSERT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = BatchInsertLpmV4(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_REPLACE, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanLpmv4ByPk(dmlStmt3, labelName, recordNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点增加.");
    g_artNodeLabelInfo.labelName = labelName;
    g_artNodeLabelInfo.indexType = (char *)"lpm4_tree_bitmap";
    g_artNodeLabelInfo.nodeCount.node4Count = 0;
    g_artNodeLabelInfo.nodeCount.node16Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;

    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期node48节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.nodeCount.node48Count);
    if (g_artNodeLabelInfo.nodeCount.node48Count == 0) {
        SystemSnprintf("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -f LABEL_NAME=%s", labelName);
    }

    // 删除数据
    AW_FUN_Log(LOG_STEP, "删除数据.");
    ret = DeleteDataLpm(labelName, recordNum, dmlConn2, dmlStmt3, SetLpmV4PkIndexValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanLpmv4ByPk(dmlStmt1, labelName, recordNum, 0, 1, 0, 24, 0);
    recordNum = 0;
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点释放.");
    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期free node48节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode48Count);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode4Count);
    SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放申请的内存
    free(g_artNodeInfo.upNode);

    free(g_artNodeInfo.normalNode1);

    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 043.（lpmv4索引）,semi模式，进行art索引树形结构的四层扩缩容 一次性扩容
// node（48->49（升级到255）使用不同句柄切换操作预期缓存正常（insert&批量删除）
TEST_F(IndexEnhance, DML_107_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "ip4forward_lpm4";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&dmlConn2, &dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/ip4forward_lpm4.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "生成随机数据.");
    GetArtNodeDataIndfo(&g_artNodeInfo, 49, true, true, true, false);

    // 批量写入数据
    AW_FUN_Log(LOG_STEP, "1.批量写入数据触发节点扩容.");
    int32_t recordNum = g_artNodeInfo.nodeCount4;
    ret = BatchInsertLpmV4(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_INSERT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanLpmv4ByPk(dmlStmt3, labelName, recordNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点增加.");
    g_artNodeLabelInfo.labelName = labelName;
    g_artNodeLabelInfo.indexType = (char *)"lpm4_tree_bitmap";
    g_artNodeLabelInfo.nodeCount.node4Count = 0;
    g_artNodeLabelInfo.nodeCount.node256Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode256Count = 0;

    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期node节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.nodeCount.node256Count);
    if (g_artNodeLabelInfo.nodeCount.node256Count == 0) {
        SystemSnprintf("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -f LABEL_NAME=%s", labelName);
    }

    // 删除数据
    AW_FUN_Log(LOG_STEP, "删除数据.");
    ret = DeleteDataLpm(labelName, recordNum, dmlConn2, dmlStmt3, SetLpmV4PkIndexValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanLpmv4ByPk(dmlStmt1, labelName, recordNum, 0, 1, 0, 24, 0);
    recordNum = 0;
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点释放.");
    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期free node48节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode256Count);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode4Count);
    SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放申请的内存
    free(g_artNodeInfo.upNode);
    free(g_artNodeInfo.normalNode1);

    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 044.（lpmv4索引）,semi模式，进行art索引树形结构的四层扩缩容
// 一次性扩容node（48->49（升级到255）使用不同句柄切换操作预期缓存正常（（merge||replace）&批量删除）
TEST_F(IndexEnhance, DML_107_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "ip4forward_lpm4";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&dmlConn2, &dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/ip4forward_lpm4.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "生成随机数据.");
    GetArtNodeDataIndfo(&g_artNodeInfo, 49, true, true, true, false);

    // 批量写入数据
    AW_FUN_Log(LOG_STEP, "1.批量写入数据触发节点扩容.");
    int32_t recordNum = g_artNodeInfo.nodeCount4;
    ret = BatchInsertLpmV4(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_MERGE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertLpmV4(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_REPLACE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanLpmv4ByPk(dmlStmt3, labelName, recordNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点增加.");
    g_artNodeLabelInfo.labelName = labelName;
    g_artNodeLabelInfo.indexType = (char *)"lpm4_tree_bitmap";
    g_artNodeLabelInfo.nodeCount.node4Count = 0;
    g_artNodeLabelInfo.nodeCount.node256Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode256Count = 0;

    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期node节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.nodeCount.node256Count);
    if (g_artNodeLabelInfo.nodeCount.node256Count == 0) {
        SystemSnprintf("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -f LABEL_NAME=%s", labelName);
    }

    // 删除数据
    AW_FUN_Log(LOG_STEP, "删除数据.");
    ret = DeleteDataLpm(labelName, recordNum, dmlConn2, dmlStmt3, SetLpmV4PkIndexValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanLpmv4ByPk(dmlStmt1, labelName, recordNum, 0, 1, 0, 24, 0);
    recordNum = 0;
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点释放.");
    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期free node48节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode256Count);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode4Count);
    SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放申请的内存
    free(g_artNodeInfo.upNode);
    free(g_artNodeInfo.normalNode1);

    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 045.（lpmv4索引）,semi模式，进行art索引树形结构的四层扩缩容
// 三次扩容node（4->49（升级到255）使用不同句柄切换操作预期缓存正常（insert&批量删除）
TEST_F(IndexEnhance, DML_107_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "ip4forward_lpm4";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&dmlConn2, &dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/ip4forward_lpm4.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "生成随机数据.");
    GetArtNodeDataIndfo(&g_artNodeInfo, 49, true, true, true, false);

    // 批量写入数据
    AW_FUN_Log(LOG_STEP, "1.批量写入数据触发节点扩容.");
    int32_t recordNum = g_artNodeInfo.nodeCount4;
    AW_FUN_Log(LOG_STEP, "1.第一次批写.");
    ret = BatchInsertLpmV4(dmlConn1, dmlStmt1, labelName, 10, GMC_OPERATION_INSERT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.第二次批写.");
    ret = BatchInsertLpmV4(dmlConn1, dmlStmt1, labelName, 40, GMC_OPERATION_INSERT, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.第三次批写.");
    ret = BatchInsertLpmV4(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_INSERT, 40);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanLpmv4ByPk(dmlStmt3, labelName, recordNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点增加.");
    g_artNodeLabelInfo.labelName = labelName;
    g_artNodeLabelInfo.indexType = (char *)"lpm4_tree_bitmap";
    g_artNodeLabelInfo.nodeCount.node4Count = 0;
    g_artNodeLabelInfo.nodeCount.node256Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode256Count = 0;

    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期node节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.nodeCount.node256Count);
    if (g_artNodeLabelInfo.nodeCount.node256Count == 0) {
        SystemSnprintf("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -f LABEL_NAME=%s", labelName);
    }

    // 删除数据
    AW_FUN_Log(LOG_STEP, "删除数据.");
    ret = DeleteDataLpm(labelName, recordNum, dmlConn2, dmlStmt3, SetLpmV4PkIndexValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanLpmv4ByPk(dmlStmt1, labelName, recordNum, 0, 1, 0, 24, 0);
    recordNum = 0;
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点释放.");
    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期free node48节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode256Count);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode4Count);
    SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放申请的内存
    free(g_artNodeInfo.upNode);
    free(g_artNodeInfo.normalNode1);

    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 046.（lpmv4索引）,semi模式，进行art索引树形结构的四层扩缩容 三次扩容
// node（4->49（升级到255）使用不同句柄切换操作预期缓存正常（（merge||replace）&批量删除）
TEST_F(IndexEnhance, DML_107_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "ip4forward_lpm4";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&dmlConn2, &dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/ip4forward_lpm4.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "生成随机数据.");
    GetArtNodeDataIndfo(&g_artNodeInfo, 49, true, true, true, false);

    // 批量写入数据
    AW_FUN_Log(LOG_STEP, "1.批量写入数据触发节点扩容.");
    int32_t recordNum = g_artNodeInfo.nodeCount4;
    ret = BatchInsertLpmV4(dmlConn1, dmlStmt1, labelName, 10, GMC_OPERATION_MERGE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertLpmV4(dmlConn1, dmlStmt1, labelName, 40, GMC_OPERATION_REPLACE, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertLpmV4(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_MERGE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanLpmv4ByPk(dmlStmt3, labelName, recordNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点增加.");
    g_artNodeLabelInfo.labelName = labelName;
    g_artNodeLabelInfo.indexType = (char *)"lpm4_tree_bitmap";
    g_artNodeLabelInfo.nodeCount.node4Count = 0;
    g_artNodeLabelInfo.nodeCount.node256Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode256Count = 0;

    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期node节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.nodeCount.node256Count);
    if (g_artNodeLabelInfo.nodeCount.node256Count == 0) {
        SystemSnprintf("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -f LABEL_NAME=%s", labelName);
    }

    // 删除数据
    AW_FUN_Log(LOG_STEP, "删除数据.");
    ret = DeleteDataLpm(labelName, recordNum, dmlConn2, dmlStmt3, SetLpmV4PkIndexValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanLpmv4ByPk(dmlStmt1, labelName, recordNum, 0, 1, 0, 24, 0);
    recordNum = 0;
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点释放.");
    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期free node48节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode256Count);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode4Count);
    SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放申请的内存
    free(g_artNodeInfo.upNode);
    free(g_artNodeInfo.normalNode1);

    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 047.（lpmv4索引）,semi模式，进行art索引树形结构的四层扩缩容 三次扩容
// node（4->49（升级到255）使用不同句柄切换操作预期缓存正常（insert&truncate（同步||异步）删除）
TEST_F(IndexEnhance, DML_107_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "ip4forward_lpm4";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 使用异步连接
    ret = testGmcConnect(&dmlConn2, &dmlStmt3, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/ip4forward_lpm4.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "生成随机数据.");
    GetArtNodeDataIndfo(&g_artNodeInfo, 49, true, true, true, false);

    // 批量写入数据
    AW_FUN_Log(LOG_STEP, "1.批量写入数据触发节点扩容.");
    int32_t recordNum = g_artNodeInfo.nodeCount4;
    ret = BatchInsertLpmV4(dmlConn1, dmlStmt1, labelName, 10, GMC_OPERATION_INSERT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertLpmV4(dmlConn1, dmlStmt1, labelName, 40, GMC_OPERATION_INSERT, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertLpmV4(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_INSERT, 40);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanLpmv4ByPk(dmlStmt2, labelName, recordNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点增加.");
    g_artNodeLabelInfo.labelName = labelName;
    g_artNodeLabelInfo.indexType = (char *)"lpm4_tree_bitmap";
    g_artNodeLabelInfo.nodeCount.node4Count = 0;
    g_artNodeLabelInfo.nodeCount.node256Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode256Count = 0;

    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期node节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.nodeCount.node256Count);
    if (g_artNodeLabelInfo.nodeCount.node256Count == 0) {
        SystemSnprintf("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -f LABEL_NAME=%s", labelName);
    }

    // 删除数据
    GmcStmtT *delStmtArray[2] = {dmlStmt2, dmlStmt3};
    GmcConnT *delConnArray[2] = {dmlConn1, dmlConn2};
    int32_t index = GetRandomMode(2);
    if (index == 0) {
        AW_FUN_Log(LOG_STEP, "当前模式:使用相同连接不同句柄");
    } else {
        AW_FUN_Log(LOG_STEP, "当前模式:使用不同连接");
    }
    AW_FUN_Log(LOG_STEP, "删除数据.");
    ret = FrontTruncateDeleteData(
        &labelInfo, delConnArray[index], delStmtArray[index], index, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanLpmv4ByPk(dmlStmt1, labelName, recordNum, 0, 1, 0, 24, 0);
    recordNum = 0;
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点释放.");
    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期free node256节点归0
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode256Count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode4Count);
    SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放申请的内存
    free(g_artNodeInfo.upNode);
    free(g_artNodeInfo.normalNode1);

    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 048.（lpmv4索引）,semi模式，进行art索引树形结构的四层扩缩容 三次扩容
// node（4->49（升级到255）使用不同句柄切换操作预期缓存正常（（merge||replace）&truncate（同步||异步）删除）
TEST_F(IndexEnhance, DML_107_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "ip4forward_lpm4";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 使用异步连接
    ret = testGmcConnect(&dmlConn2, &dmlStmt3, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/ip4forward_lpm4.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "生成随机数据.");
    GetArtNodeDataIndfo(&g_artNodeInfo, 49, true, true, true, false);

    // 批量写入数据
    AW_FUN_Log(LOG_STEP, "1.批量写入数据触发节点扩容.");
    int32_t recordNum = g_artNodeInfo.nodeCount4;
    ret = BatchInsertLpmV4(dmlConn1, dmlStmt1, labelName, 10, GMC_OPERATION_INSERT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertLpmV4(dmlConn1, dmlStmt1, labelName, 40, GMC_OPERATION_REPLACE, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertLpmV4(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_MERGE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanLpmv4ByPk(dmlStmt2, labelName, recordNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点增加.");
    g_artNodeLabelInfo.labelName = labelName;
    g_artNodeLabelInfo.indexType = (char *)"lpm4_tree_bitmap";
    g_artNodeLabelInfo.nodeCount.node4Count = 0;
    g_artNodeLabelInfo.nodeCount.node256Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode256Count = 0;

    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期node节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.nodeCount.node256Count);
    if (g_artNodeLabelInfo.nodeCount.node256Count == 0) {
        SystemSnprintf("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -f LABEL_NAME=%s", labelName);
    }

    // 删除数据
    GmcStmtT *delStmtArray[2] = {dmlStmt2, dmlStmt3};
    GmcConnT *delConnArray[2] = {dmlConn1, dmlConn2};
    int32_t index = GetRandomMode(2);
    if (index == 0) {
        AW_FUN_Log(LOG_STEP, "当前模式:使用相同连接不同句柄");
    } else {
        AW_FUN_Log(LOG_STEP, "当前模式:使用不同连接");
    }
    AW_FUN_Log(LOG_STEP, "删除数据.");
    ret = FrontTruncateDeleteData(
        &labelInfo, delConnArray[index], delStmtArray[index], index, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanLpmv4ByPk(dmlStmt1, labelName, recordNum, 0, 1, 0, 24, 0);
    recordNum = 0;
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点释放.");
    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期free node256节点归0
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode256Count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode4Count);
    SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放申请的内存
    free(g_artNodeInfo.upNode);
    free(g_artNodeInfo.normalNode1);

    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 049.lpmv6索引，semi模式，进行art索引树形结构的四层扩缩容
// node（4->5（升级到16）使用不同句柄切换操作预期缓存正常（insert&批量删除）
TEST_F(IndexEnhance, DML_107_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "ip4forward_lpm6";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&dmlConn2, &dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/ip4forward_lpm6.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true,\"isFastReadUncommitted\": false}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "生成随机数据.");
    GetArtNodeDataIndfo(&g_artNodeInfo, 5, true, true, true, true);

    // 批量写入数据
    AW_FUN_Log(LOG_STEP, "1.批量写入数据触发节点扩容.");
    int32_t recordNum = g_artNodeInfo.nodeCount4;
    ret = BatchInsertLpmV6(dmlConn1, dmlStmt1, labelName, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanLpmv6ByPk(dmlStmt3, labelName, recordNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点增加.");
    g_artNodeLabelInfo.labelName = labelName;
    g_artNodeLabelInfo.indexType = (char *)"lpm6_tree_bitmap";
    g_artNodeLabelInfo.nodeCount.node4Count = 0;
    g_artNodeLabelInfo.nodeCount.node16Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;

    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期node16节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.nodeCount.node16Count);
    if (g_artNodeLabelInfo.nodeCount.node16Count == 0) {
        SystemSnprintf("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -f LABEL_NAME=%s", labelName);
    }
    SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);
    // 删除数据
    AW_FUN_Log(LOG_STEP, "删除数据.");
    ret = DeleteDataLpmv6(labelName, recordNum, dmlConn2, dmlStmt3, SetLpmV6PkIndexValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanLpmv6ByPk(dmlStmt1, labelName, recordNum, 0, 1, 0, 24, 0);
    recordNum = 0;
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点释放.");
    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期free node16节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode16Count);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode4Count);
    SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放申请的内存
    free(g_artNodeInfo.upNode);

    free(g_artNodeInfo.normalNode1);

    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 050.（lpmv6索引）,semi模式，进行art索引树形结构的四层扩缩容
// node（4->5（升级到16）使用不同句柄切换操作预期缓存正常（（merge||replace）&批量删除）
TEST_F(IndexEnhance, DML_107_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "ip4forward_lpm6";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&dmlConn2, &dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/ip4forward_lpm6.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "生成随机数据.");
    GetArtNodeDataIndfo(&g_artNodeInfo, 5, true, true, true, true);

    // 批量写入数据
    AW_FUN_Log(LOG_STEP, "1.批量写入数据触发节点扩容.");
    int32_t recordNum = g_artNodeInfo.nodeCount4;
    ret = BatchInsertLpmV6(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = BatchInsertLpmV6(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanLpmv6ByPk(dmlStmt3, labelName, recordNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点增加.");
    g_artNodeLabelInfo.labelName = labelName;
    g_artNodeLabelInfo.indexType = (char *)"lpm6_tree_bitmap";
    g_artNodeLabelInfo.nodeCount.node4Count = 0;
    g_artNodeLabelInfo.nodeCount.node16Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;

    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期node16节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.nodeCount.node16Count);
    if (g_artNodeLabelInfo.nodeCount.node16Count == 0) {
        SystemSnprintf("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -f LABEL_NAME=%s", labelName);
    }

    // 删除数据
    AW_FUN_Log(LOG_STEP, "删除数据.");
    ret = DeleteDataLpmv6(labelName, recordNum, dmlConn2, dmlStmt3, SetLpmV6PkIndexValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanLpmv6ByPk(dmlStmt1, labelName, recordNum, 0, 1, 0, 24, 0);
    recordNum = 0;
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点释放.");
    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期free node16节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode16Count);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode4Count);
    SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放申请的内存
    free(g_artNodeInfo.upNode);

    free(g_artNodeInfo.normalNode1);

    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 051.（lpmv6索引）,semi模式，进行art索引树形结构的四层扩缩容 一次性扩容
// node（16->17（升级到48）使用不同句柄切换操作预期缓存正常（insert&批量删除）
TEST_F(IndexEnhance, DML_107_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "ip4forward_lpm6";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&dmlConn2, &dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/ip4forward_lpm6.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "生成随机数据.");
    GetArtNodeDataIndfo(&g_artNodeInfo, 17, true, true, true, true);

    // 批量写入数据
    AW_FUN_Log(LOG_STEP, "1.批量写入数据触发节点扩容.");
    int32_t recordNum = g_artNodeInfo.nodeCount4;
    ret = BatchInsertLpmV6(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_INSERT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanLpmv6ByPk(dmlStmt3, labelName, recordNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点增加.");
    g_artNodeLabelInfo.labelName = labelName;
    g_artNodeLabelInfo.indexType = (char *)"lpm6_tree_bitmap";
    g_artNodeLabelInfo.nodeCount.node4Count = 0;
    g_artNodeLabelInfo.nodeCount.node16Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;

    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期node16节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.nodeCount.node48Count);
    if (g_artNodeLabelInfo.nodeCount.node48Count == 0) {
        SystemSnprintf("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -f LABEL_NAME=%s", labelName);
    }

    // 删除数据
    AW_FUN_Log(LOG_STEP, "删除数据.");
    ret = DeleteDataLpmv6(labelName, recordNum, dmlConn2, dmlStmt3, SetLpmV6PkIndexValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanLpmv6ByPk(dmlStmt1, labelName, recordNum, 0, 1, 0, 24, 0);
    recordNum = 0;
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点释放.");
    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期free node48节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode48Count);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode4Count);
    SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放申请的内存
    free(g_artNodeInfo.upNode);

    free(g_artNodeInfo.normalNode1);

    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 052.（lpmv6索引）,semi模式，进行art索引树形结构的四层扩缩容 一次性扩容
// node（16->17（升级到48）使用不同句柄切换操作预期缓存正常（（merge||replace）&批量删除）
TEST_F(IndexEnhance, DML_107_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "ip4forward_lpm6";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&dmlConn2, &dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/ip4forward_lpm6.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "生成随机数据.");
    GetArtNodeDataIndfo(&g_artNodeInfo, 17, true, true, true, true);

    // 批量写入数据
    AW_FUN_Log(LOG_STEP, "1.批量写入数据触发节点扩容.");
    int32_t recordNum = g_artNodeInfo.nodeCount4;
    ret = BatchInsertLpmV6(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_MERGE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertLpmV6(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_REPLACE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanLpmv6ByPk(dmlStmt3, labelName, recordNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点增加.");
    g_artNodeLabelInfo.labelName = labelName;
    g_artNodeLabelInfo.indexType = (char *)"lpm6_tree_bitmap";
    g_artNodeLabelInfo.nodeCount.node4Count = 0;
    g_artNodeLabelInfo.nodeCount.node16Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;

    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期node16节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.nodeCount.node48Count);
    if (g_artNodeLabelInfo.nodeCount.node48Count == 0) {
        SystemSnprintf("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -f LABEL_NAME=%s", labelName);
    }

    // 删除数据
    AW_FUN_Log(LOG_STEP, "删除数据.");
    ret = DeleteDataLpmv6(labelName, recordNum, dmlConn2, dmlStmt3, SetLpmV6PkIndexValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanLpmv6ByPk(dmlStmt1, labelName, recordNum, 0, 1, 0, 24, 0);
    recordNum = 0;
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点释放.");
    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期free node48节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode48Count);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode4Count);
    SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放申请的内存
    free(g_artNodeInfo.upNode);

    free(g_artNodeInfo.normalNode1);

    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 053.（lpmv6索引）,semi模式，进行art索引树形结构的四层扩缩容 两次扩容
// node（4->17（升级到48）使用不同句柄切换操作预期缓存正常（insert&批量删除）
TEST_F(IndexEnhance, DML_107_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "ip4forward_lpm6";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&dmlConn2, &dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/ip4forward_lpm6.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "生成随机数据.");
    GetArtNodeDataIndfo(&g_artNodeInfo, 17, true, true, true, true);

    // 批量写入数据
    AW_FUN_Log(LOG_STEP, "1.批量写入数据触发节点扩容.");
    int32_t recordNum = g_artNodeInfo.nodeCount4;
    ret = BatchInsertLpmV6(dmlConn1, dmlStmt1, labelName, 10, GMC_OPERATION_INSERT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = BatchInsertLpmV6(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_INSERT, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanLpmv6ByPk(dmlStmt3, labelName, recordNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点增加.");
    g_artNodeLabelInfo.labelName = labelName;
    g_artNodeLabelInfo.indexType = (char *)"lpm6_tree_bitmap";
    g_artNodeLabelInfo.nodeCount.node4Count = 0;
    g_artNodeLabelInfo.nodeCount.node16Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;

    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期node48节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.nodeCount.node48Count);
    if (g_artNodeLabelInfo.nodeCount.node48Count == 0) {
        SystemSnprintf("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -f LABEL_NAME=%s", labelName);
    }

    // 删除数据
    AW_FUN_Log(LOG_STEP, "删除数据.");
    ret = DeleteDataLpmv6(labelName, recordNum, dmlConn2, dmlStmt3, SetLpmV6PkIndexValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanLpmv6ByPk(dmlStmt1, labelName, recordNum, 0, 1, 0, 24, 0);
    recordNum = 0;
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点释放.");
    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期free node48节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode48Count);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode4Count);
    SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放申请的内存
    free(g_artNodeInfo.upNode);

    free(g_artNodeInfo.normalNode1);

    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 054.（lpmv6索引）,semi模式，进行art索引树形结构的四层扩缩容 两次扩容
// node（4->17（升级到48）使用不同句柄切换操作预期缓存正常（（merge||replace）&批量删除）
TEST_F(IndexEnhance, DML_107_054)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "ip4forward_lpm6";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&dmlConn2, &dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/ip4forward_lpm6.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "生成随机数据.");
    GetArtNodeDataIndfo(&g_artNodeInfo, 17, true, true, true, true);

    // 批量写入数据
    AW_FUN_Log(LOG_STEP, "1.批量写入数据触发节点扩容.");
    int32_t recordNum = g_artNodeInfo.nodeCount4;
    ret = BatchInsertLpmV6(dmlConn1, dmlStmt1, labelName, 10, GMC_OPERATION_MERGE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = BatchInsertLpmV6(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_REPLACE, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanLpmv6ByPk(dmlStmt3, labelName, recordNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点增加.");
    g_artNodeLabelInfo.labelName = labelName;
    g_artNodeLabelInfo.indexType = (char *)"lpm6_tree_bitmap";
    g_artNodeLabelInfo.nodeCount.node4Count = 0;
    g_artNodeLabelInfo.nodeCount.node16Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;

    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期node48节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.nodeCount.node48Count);
    if (g_artNodeLabelInfo.nodeCount.node48Count == 0) {
        SystemSnprintf("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -f LABEL_NAME=%s", labelName);
    }

    // 删除数据
    AW_FUN_Log(LOG_STEP, "删除数据.");
    ret = DeleteDataLpmv6(labelName, recordNum, dmlConn2, dmlStmt3, SetLpmV6PkIndexValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanLpmv6ByPk(dmlStmt1, labelName, recordNum, 0, 1, 0, 24, 0);
    recordNum = 0;
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点释放.");
    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期free node48节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode48Count);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode4Count);
    SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放申请的内存
    free(g_artNodeInfo.upNode);

    free(g_artNodeInfo.normalNode1);

    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 055.（lpmv6索引）,semi模式，进行art索引树形结构的四层扩缩容 一次性扩容
// node（48->49（升级到255）使用不同句柄切换操作预期缓存正常（insert&批量删除）
TEST_F(IndexEnhance, DML_107_055)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "ip4forward_lpm6";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&dmlConn2, &dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/ip4forward_lpm6.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "生成随机数据.");
    GetArtNodeDataIndfo(&g_artNodeInfo, 49, true, true, true, false);

    // 批量写入数据
    AW_FUN_Log(LOG_STEP, "1.批量写入数据触发节点扩容.");
    int32_t recordNum = g_artNodeInfo.nodeCount4;
    ret = BatchInsertLpmV6(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_INSERT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanLpmv6ByPk(dmlStmt3, labelName, recordNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点增加.");
    g_artNodeLabelInfo.labelName = labelName;
    g_artNodeLabelInfo.indexType = (char *)"lpm6_tree_bitmap";
    g_artNodeLabelInfo.nodeCount.node4Count = 0;
    g_artNodeLabelInfo.nodeCount.node256Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode256Count = 0;

    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期node节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.nodeCount.node256Count);
    if (g_artNodeLabelInfo.nodeCount.node256Count == 0) {
        SystemSnprintf("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -f LABEL_NAME=%s", labelName);
    }

    // 删除数据
    AW_FUN_Log(LOG_STEP, "删除数据.");
    ret = DeleteDataLpmv6(labelName, recordNum, dmlConn2, dmlStmt3, SetLpmV6PkIndexValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanLpmv6ByPk(dmlStmt1, labelName, recordNum, 0, 1, 0, 24, 0);
    recordNum = 0;
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点释放.");
    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期free node48节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode256Count);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode4Count);
    SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放申请的内存
    free(g_artNodeInfo.upNode);
    free(g_artNodeInfo.normalNode1);

    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 056.（lpmv6索引）,semi模式，进行art索引树形结构的四层扩缩容
// 一次性扩容node（48->49（升级到255）使用不同句柄切换操作预期缓存正常（（merge||replace）&批量删除）
TEST_F(IndexEnhance, DML_107_056)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "ip4forward_lpm6";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&dmlConn2, &dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/ip4forward_lpm6.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "生成随机数据.");
    GetArtNodeDataIndfo(&g_artNodeInfo, 49, true, true, true, false);

    // 批量写入数据
    AW_FUN_Log(LOG_STEP, "1.批量写入数据触发节点扩容.");
    int32_t recordNum = g_artNodeInfo.nodeCount4;
    ret = BatchInsertLpmV6(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_MERGE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertLpmV6(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_REPLACE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanLpmv6ByPk(dmlStmt3, labelName, recordNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点增加.");
    g_artNodeLabelInfo.labelName = labelName;
    g_artNodeLabelInfo.indexType = (char *)"lpm6_tree_bitmap";
    g_artNodeLabelInfo.nodeCount.node4Count = 0;
    g_artNodeLabelInfo.nodeCount.node256Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode256Count = 0;

    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期node节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.nodeCount.node256Count);
    if (g_artNodeLabelInfo.nodeCount.node256Count == 0) {
        SystemSnprintf("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -f LABEL_NAME=%s", labelName);
    }

    // 删除数据
    AW_FUN_Log(LOG_STEP, "删除数据.");
    ret = DeleteDataLpmv6(labelName, recordNum, dmlConn2, dmlStmt3, SetLpmV6PkIndexValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanLpmv6ByPk(dmlStmt1, labelName, recordNum, 0, 1, 0, 24, 0);
    recordNum = 0;
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点释放.");
    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期free node48节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode256Count);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode4Count);
    SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放申请的内存
    free(g_artNodeInfo.upNode);
    free(g_artNodeInfo.normalNode1);

    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 057.（lpmv6索引）,semi模式，进行art索引树形结构的四层扩缩容
// 三次扩容node（4->49（升级到255）使用不同句柄切换操作预期缓存正常（insert&批量删除）
TEST_F(IndexEnhance, DML_107_057)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "ip4forward_lpm6";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&dmlConn2, &dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/ip4forward_lpm6.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "生成随机数据.");
    GetArtNodeDataIndfo(&g_artNodeInfo, 49, true, true, true, false);

    // 批量写入数据
    AW_FUN_Log(LOG_STEP, "1.批量写入数据触发节点扩容.");
    int32_t recordNum = g_artNodeInfo.nodeCount4;
    ret = BatchInsertLpmV6(dmlConn1, dmlStmt1, labelName, 10, GMC_OPERATION_INSERT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertLpmV6(dmlConn1, dmlStmt1, labelName, 40, GMC_OPERATION_INSERT, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertLpmV6(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_INSERT, 40);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanLpmv6ByPk(dmlStmt3, labelName, recordNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点增加.");
    g_artNodeLabelInfo.labelName = labelName;
    g_artNodeLabelInfo.indexType = (char *)"lpm6_tree_bitmap";
    g_artNodeLabelInfo.nodeCount.node4Count = 0;
    g_artNodeLabelInfo.nodeCount.node256Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode256Count = 0;

    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期node节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.nodeCount.node256Count);
    if (g_artNodeLabelInfo.nodeCount.node256Count == 0) {
        SystemSnprintf("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -f LABEL_NAME=%s", labelName);
    }

    // 删除数据
    AW_FUN_Log(LOG_STEP, "删除数据.");
    ret = DeleteDataLpmv6(labelName, recordNum, dmlConn2, dmlStmt3, SetLpmV6PkIndexValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanLpmv6ByPk(dmlStmt1, labelName, recordNum, 0, 1, 0, 24, 0);
    recordNum = 0;
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点释放.");
    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期free node48节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode256Count);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode4Count);
    SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放申请的内存
    free(g_artNodeInfo.upNode);
    free(g_artNodeInfo.normalNode1);

    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 058.（lpmv6索引）,semi模式，进行art索引树形结构的四层扩缩容 三次扩容
// node（4->49（升级到255）使用不同句柄切换操作预期缓存正常（（merge||replace）&批量删除）
TEST_F(IndexEnhance, DML_107_058)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "ip4forward_lpm6";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&dmlConn2, &dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/ip4forward_lpm6.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "生成随机数据.");
    GetArtNodeDataIndfo(&g_artNodeInfo, 49, true, true, true, false);

    // 批量写入数据
    AW_FUN_Log(LOG_STEP, "1.批量写入数据触发节点扩容.");
    int32_t recordNum = g_artNodeInfo.nodeCount4;
    ret = BatchInsertLpmV6(dmlConn1, dmlStmt1, labelName, 10, GMC_OPERATION_MERGE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertLpmV6(dmlConn1, dmlStmt1, labelName, 40, GMC_OPERATION_REPLACE, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertLpmV6(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_MERGE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanLpmv6ByPk(dmlStmt3, labelName, recordNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点增加.");
    g_artNodeLabelInfo.labelName = labelName;
    g_artNodeLabelInfo.indexType = (char *)"lpm6_tree_bitmap";
    g_artNodeLabelInfo.nodeCount.node4Count = 0;
    g_artNodeLabelInfo.nodeCount.node256Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode256Count = 0;

    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期node节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.nodeCount.node256Count);
    if (g_artNodeLabelInfo.nodeCount.node256Count == 0) {
        SystemSnprintf("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -f LABEL_NAME=%s", labelName);
    }

    // 删除数据
    AW_FUN_Log(LOG_STEP, "删除数据.");
    ret = DeleteDataLpmv6(labelName, recordNum, dmlConn2, dmlStmt3, SetLpmV6PkIndexValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanLpmv6ByPk(dmlStmt1, labelName, recordNum, 0, 1, 0, 24, 0);
    recordNum = 0;
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点释放.");
    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期free node48节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode256Count);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode4Count);
    SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放申请的内存
    free(g_artNodeInfo.upNode);
    free(g_artNodeInfo.normalNode1);

    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 059.（lpmv6索引）,semi模式，进行art索引树形结构的四层扩缩容 三次扩容
// node（4->49（升级到255）使用不同句柄切换操作预期缓存正常（insert&truncate（同步||异步）删除）
TEST_F(IndexEnhance, DML_107_059)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "ip4forward_lpm6";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 使用异步连接
    ret = testGmcConnect(&dmlConn2, &dmlStmt3, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/ip4forward_lpm6.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "生成随机数据.");
    GetArtNodeDataIndfo(&g_artNodeInfo, 49, true, true, true, false);

    // 批量写入数据
    AW_FUN_Log(LOG_STEP, "1.批量写入数据触发节点扩容.");
    int32_t recordNum = g_artNodeInfo.nodeCount4;
    ret = BatchInsertLpmV6(dmlConn1, dmlStmt1, labelName, 10, GMC_OPERATION_INSERT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertLpmV6(dmlConn1, dmlStmt1, labelName, 40, GMC_OPERATION_INSERT, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertLpmV6(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_INSERT, 40);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanLpmv6ByPk(dmlStmt2, labelName, recordNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点增加.");
    g_artNodeLabelInfo.labelName = labelName;
    g_artNodeLabelInfo.indexType = (char *)"lpm6_tree_bitmap";
    g_artNodeLabelInfo.nodeCount.node4Count = 0;
    g_artNodeLabelInfo.nodeCount.node256Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode256Count = 0;

    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期node节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.nodeCount.node256Count);
    if (g_artNodeLabelInfo.nodeCount.node256Count == 0) {
        SystemSnprintf("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -f LABEL_NAME=%s", labelName);
    }

    // 删除数据
    GmcStmtT *delStmtArray[2] = {dmlStmt2, dmlStmt3};
    GmcConnT *delConnArray[2] = {dmlConn1, dmlConn2};
    int32_t index = GetRandomMode(2);
    if (index == 0) {
        AW_FUN_Log(LOG_STEP, "当前模式:使用相同连接不同句柄");
    } else {
        AW_FUN_Log(LOG_STEP, "当前模式:使用不同连接");
    }
    AW_FUN_Log(LOG_STEP, "删除数据.");
    ret = FrontTruncateDeleteData(
        &labelInfo, delConnArray[index], delStmtArray[index], index, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanLpmv6ByPk(dmlStmt1, labelName, recordNum, 0, 1, 0, 24, 0);
    recordNum = 0;
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点释放.");
    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期free node256节点归0
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode256Count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode4Count);
    SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放申请的内存
    free(g_artNodeInfo.upNode);
    free(g_artNodeInfo.normalNode1);

    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 060.（lpmv6索引）,semi模式，进行art索引树形结构的四层扩缩容 三次扩容
// node（4->49（升级到255）使用不同句柄切换操作预期缓存正常（（merge||replace）&truncate（同步||异步）删除）
TEST_F(IndexEnhance, DML_107_060)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "ip4forward_lpm6";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 使用异步连接
    ret = testGmcConnect(&dmlConn2, &dmlStmt3, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/ip4forward_lpm6.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "生成随机数据.");
    GetArtNodeDataIndfo(&g_artNodeInfo, 49, true, true, true, false);

    // 批量写入数据
    AW_FUN_Log(LOG_STEP, "1.批量写入数据触发节点扩容.");
    int32_t recordNum = g_artNodeInfo.nodeCount4;
    ret = BatchInsertLpmV6(dmlConn1, dmlStmt1, labelName, 10, GMC_OPERATION_INSERT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertLpmV6(dmlConn1, dmlStmt1, labelName, 40, GMC_OPERATION_REPLACE, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertLpmV6(dmlConn1, dmlStmt1, labelName, recordNum, GMC_OPERATION_MERGE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanLpmv6ByPk(dmlStmt2, labelName, recordNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点增加.");
    g_artNodeLabelInfo.labelName = labelName;
    g_artNodeLabelInfo.indexType = (char *)"lpm6_tree_bitmap";
    g_artNodeLabelInfo.nodeCount.node4Count = 0;
    g_artNodeLabelInfo.nodeCount.node256Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode4Count = 0;
    g_artNodeLabelInfo.freeNodeCount.freeNode256Count = 0;

    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期node节点增加
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, g_artNodeLabelInfo.nodeCount.node256Count);
    if (g_artNodeLabelInfo.nodeCount.node256Count == 0) {
        SystemSnprintf("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -f LABEL_NAME=%s", labelName);
    }

    // 删除数据
    GmcStmtT *delStmtArray[2] = {dmlStmt2, dmlStmt3};
    GmcConnT *delConnArray[2] = {dmlConn1, dmlConn2};
    int32_t index = GetRandomMode(2);
    if (index == 0) {
        AW_FUN_Log(LOG_STEP, "当前模式:使用相同连接不同句柄");
    } else {
        AW_FUN_Log(LOG_STEP, "当前模式:使用不同连接");
    }
    AW_FUN_Log(LOG_STEP, "删除数据.");
    ret = FrontTruncateDeleteData(
        &labelInfo, delConnArray[index], delStmtArray[index], index, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanLpmv6ByPk(dmlStmt1, labelName, recordNum, 0, 1, 0, 24, 0);
    recordNum = 0;
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    // 视图校验
    AW_FUN_Log(LOG_STEP, "art视图查询校验预期node4节点释放.");
    GetArtTriggerExpansionOrshrinkValue(&g_artNodeLabelInfo);
    // 预期free node256节点归0
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode256Count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, g_artNodeLabelInfo.freeNodeCount.freeNode4Count);
    SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放申请的内存
    free(g_artNodeInfo.upNode);
    free(g_artNodeInfo.normalNode1);

    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 061.insert写数据扩容后，老化部分数据触发缩容，再次写入insert数据扩容，再使用其它连接删除缩容，不同连接查询数据
TEST_F(IndexEnhance, DML_107_061)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "DML_107_normal";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&dmlConn2, &dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/DML_107_normal.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 连接1建表获取扩缩容数据量后删表
    AW_FUN_Log(LOG_STEP, "2.建表获取扩缩容数据量后删表.");
    ret = GetLabeMemChangeValue(&labelInfo, SetPkProperty, SetPkValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 使用DML连接写数据
    AW_FUN_Log(LOG_STEP, "3.使用DML连接写数据");
    // 连接3句柄1写数据到扩容
    labelInfo.expansionValue = 1;
    labelInfo.shrinkValue = 0;
    ret = WriteDataUntilExpand(&labelInfo, dmlConn1, dmlStmt1, SetPkProperty);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接3句柄2删除数据直到触发缩容
    // 删除操作句柄可为随机相同连接不同句柄或不同连接
    GmcStmtT *delStmtArray[2] = {dmlStmt2, dmlStmt3};
    GmcConnT *delConnArray[2] = {dmlConn1, dmlConn2};
    int32_t index = GetRandomMode(2);
    if (index == 0) {
        AW_FUN_Log(LOG_STEP, "当前模式:使用相同连接不同句柄");
    } else {
        AW_FUN_Log(LOG_STEP, "当前模式:使用不同连接");
    }

    // 老化部分数据
    labelInfo.shrinkValueDataNum = 1900;
    labelInfo.shrinkValue = 1000;
    bool isAbnormal = false;
    ret = GmcBeginCheck(dmlStmt1, labelName, 0xff);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = WriteData(&labelInfo,
        labelInfo.shrinkValueDataNum,
        labelInfo.expanSionDataNum,
        dmlConn1,
        dmlStmt1,
        SetPkProperty,
        GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcEndCheck(dmlStmt1, labelName, 0xff, isAbnormal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(60);
    GetTriggerExpansionOrshrinkValue(&labelInfo);
    AW_MACRO_EXPECT_EQ_INT(1, labelInfo.shrinkValue);

    AW_FUN_Log(LOG_STEP, "DeleteDataUntilExpand labelInfo.shrinkValueDataNum is %d", labelInfo.shrinkValueDataNum);

    // 连接3句柄1再次写数据直到扩容
    AW_FUN_Log(LOG_STEP, "4.再次使用DML连接写数据");
    ret = WriteDataUntilExpand(&labelInfo, dmlConn1, dmlStmt1, SetPkProperty, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接3句柄2读数据
    ret = ScanNormalByPk(delStmtArray[index], labelInfo.labelName, labelInfo.expanSionDataNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(labelInfo.expanSionDataNum, ret);

    // 连接3句柄2删除数据直到触发缩容
    sleep(65);
    ret = DeleteDataUntilExpand(&labelInfo, delConnArray[index], delStmtArray[index], SetPkValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接3句柄1读数据
    ret = ScanNormalByPk(
        dmlStmt1, labelInfo.labelName, labelInfo.expanSionDataNum, 0, 1, 0, 24, 0, labelInfo.shrinkValueDataNum);
    AW_MACRO_EXPECT_EQ_INT(labelInfo.expanSionDataNum - labelInfo.shrinkValueDataNum, ret);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelInfo.labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 062.（merge|replace）写数据后扩容后，老化部分数据缩容，再次（merge|replace）写入数据扩容，再使用其它连接删除缩容，不同连接查询数据
TEST_F(IndexEnhance, DML_107_062)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "DML_107_normal";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&dmlConn2, &dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/DML_107_normal.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // 连接1建表获取扩缩容数据量后删表
    AW_FUN_Log(LOG_STEP, "2.建表获取扩缩容数据量后删表.");
    ret = GetLabeMemChangeValue(&labelInfo, SetPkProperty, SetPkValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 使用DML连接写数据
    AW_FUN_Log(LOG_STEP, "3.使用DML连接写数据");
    // 连接3句柄1写数据到扩容
    labelInfo.expansionValue = 1;
    labelInfo.shrinkValue = 0;
    ret = WriteDataUntilExpand(&labelInfo, dmlConn1, dmlStmt1, SetPkValue, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接3句柄2删除数据直到触发缩容
    // 删除操作句柄可为随机相同连接不同句柄或不同连接
    GmcStmtT *delStmtArray[2] = {dmlStmt2, dmlStmt3};
    GmcConnT *delConnArray[2] = {dmlConn1, dmlConn2};
    int32_t index = GetRandomMode(2);
    if (index == 0) {
        AW_FUN_Log(LOG_STEP, "当前模式:使用相同连接不同句柄");
    } else {
        AW_FUN_Log(LOG_STEP, "当前模式:使用不同连接");
    }

    // 老化部分数据
    labelInfo.shrinkValueDataNum = 1900;
    labelInfo.shrinkValue = 1000;
    bool isAbnormal = false;
    ret = GmcBeginCheck(dmlStmt1, labelName, 0xff);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = WriteData(&labelInfo,
        labelInfo.shrinkValueDataNum,
        labelInfo.expanSionDataNum,
        dmlConn1,
        dmlStmt1,
        SetPkProperty,
        GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcEndCheck(dmlStmt1, labelName, 0xff, isAbnormal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(60);
    GetTriggerExpansionOrshrinkValue(&labelInfo);
    AW_MACRO_EXPECT_EQ_INT(1, labelInfo.shrinkValue);

    AW_FUN_Log(LOG_STEP, "DeleteDataUntilExpand labelInfo.shrinkValueDataNum is %d", labelInfo.shrinkValueDataNum);

    // 连接3句柄1再次写数据直到扩容
    AW_FUN_Log(LOG_STEP, "4.再次使用DML连接写数据");
    ret = WriteDataUntilExpand(&labelInfo, dmlConn1, dmlStmt1, SetPkProperty, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接3句柄2读数据
    ret = ScanNormalByPk(delStmtArray[index], labelInfo.labelName, labelInfo.expanSionDataNum, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(labelInfo.expanSionDataNum, ret);

    // 连接3句柄2删除数据直到触发缩容
    sleep(65);
    ret = DeleteDataUntilExpand(&labelInfo, delConnArray[index], delStmtArray[index], SetPkValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接3句柄1读数据
    ret = ScanNormalByPk(
        dmlStmt1, labelInfo.labelName, labelInfo.expanSionDataNum, 0, 1, 0, 24, 0, labelInfo.shrinkValueDataNum);
    AW_MACRO_EXPECT_EQ_INT(labelInfo.expanSionDataNum - labelInfo.shrinkValueDataNum, ret);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcDropVertexLabel(ddlStmt, labelInfo.labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 063.对不同namespace下相同表写入相同数据，对namespace1写入数据后，查询namespace2数据，使用连接2，删除namespace2数据后，查询namespace1数据。两次删除数据不同，
TEST_F(IndexEnhance, DML_107_063)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "DML_107_normal";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建新的namespace
    char nameSpaceName[] = "IndexEnhanceNameSpace";
    ret = GmcCreateNamespace(ddlStmt, nameSpaceName, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&dmlConn2, &dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/DML_107_normal.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // namespace1写入数据后，查询namespace2数据
    AW_FUN_Log(LOG_STEP, "2.namespace1写入数据后，查询namespace2数据.");

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(ddlStmt, nameSpaceName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = WriteData(&labelInfo, 3000, 3500, dmlConn1, dmlStmt1, SetPkProperty, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(dmlStmt1, nameSpaceName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = WriteData(&labelInfo, 3000, 3500, dmlConn1, dmlStmt1, SetPkProperty, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 使用DML连接写数据
    AW_FUN_Log(LOG_STEP, "3.使用DML连接写数据");
    // 连接3句柄1写数据到扩容
    labelInfo.expansionValue = 1;
    labelInfo.shrinkValue = 0;
    ret = GmcUseNamespace(dmlStmt2, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = WriteData(&labelInfo, 0, 3000, dmlConn1, dmlStmt2, SetPkProperty);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t expandNum = labelInfo.expanSionDataNum;

    // 读其它namespace下同名表
    ret = GmcUseNamespace(dmlStmt3, nameSpaceName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ScanNormalByPk(dmlStmt3, labelInfo.labelName, 3500, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(500, ret);

    // 连接3句柄2删除数据直到触发缩容
    // 删除操作句柄可为随机相同连接不同句柄或不同连接
    GmcStmtT *delStmtArray[2] = {dmlStmt2, dmlStmt3};
    GmcConnT *delConnArray[2] = {dmlConn1, dmlConn2};
    int32_t index = GetRandomMode(2);
    if (index == 0) {
        AW_FUN_Log(LOG_STEP, "当前模式:使用相同连接不同句柄");
    } else {
        AW_FUN_Log(LOG_STEP, "当前模式:使用不同连接");
    }

    // 删新表
    ret = GmcUseNamespace(delStmtArray[1 - index], nameSpaceName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = WriteData(
        &labelInfo, 0, 3500, delConnArray[1 - index], delStmtArray[1 - index], SetPkValue, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接3句柄2读数据
    ret = GmcUseNamespace(delStmtArray[1 - index], g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ScanNormalByPk(delStmtArray[1 - index], labelInfo.labelName, 3500, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(3500, ret);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcUseNamespace(ddlStmt, nameSpaceName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(ddlStmt, labelInfo.labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(ddlStmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(ddlStmt, labelInfo.labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(ddlStmt, nameSpaceName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 064.对不同namespace下相同表写入相同数据，对namespace1（merge|replace）数据后，查询namespace2数据，使用连接2，删除namespace2数据后，查询namespace1数据。两次删除数据不同
TEST_F(IndexEnhance, DML_107_064)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    char labelName[128] = "DML_107_normal";

    // 启动服务
    AW_FUN_Log(LOG_STEP, "1.启动服务.");
    StartIndexCacheServer();

    // 建表连接
    GmcConnT *ddlConn = NULL;
    GmcStmtT *ddlStmt = NULL;
    ret = testGmcConnect(&ddlConn, &ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建新的namespace
    char nameSpaceName[] = "IndexEnhanceNameSpace";
    ret = GmcCreateNamespace(ddlStmt, nameSpaceName, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML连接
    GmcConnT *dmlConn1 = NULL;
    GmcConnT *dmlConn2 = NULL;
    GmcStmtT *dmlStmt1 = NULL;
    GmcStmtT *dmlStmt2 = NULL;
    GmcStmtT *dmlStmt3 = NULL;
    ret = testGmcConnect(&dmlConn1, &dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(dmlConn1, &dmlStmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&dmlConn2, &dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    IndexCacheLabelInfoT labelInfo;
    InitLabelInfo(&labelInfo, labelName);

    char *labelJson = NULL;
    readJanssonFile("./schema_file/DML_107_normal.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    char configJson[] = "{\"max_record_count\" : 1000000, \"defragmentation\" : true}";
    labelInfo.labelJson = labelJson;
    labelInfo.configJson = configJson;

    // namespace1写入数据后，查询namespace2数据
    AW_FUN_Log(LOG_STEP, "2.namespace1写入数据后，查询namespace2数据.");

    // 使用建表连接建表
    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(ddlStmt, nameSpaceName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)GmcDropVertexLabel(ddlStmt, labelName);
    ret = GmcCreateVertexLabel(ddlStmt, labelInfo.labelJson, labelInfo.configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = WriteData(&labelInfo, 3000, 3500, dmlConn1, dmlStmt1, SetPkProperty, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(dmlStmt1, nameSpaceName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = WriteData(&labelInfo, 3000, 3500, dmlConn1, dmlStmt1, SetPkProperty, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 使用DML连接写数据
    AW_FUN_Log(LOG_STEP, "3.使用DML连接写数据");
    // 连接3句柄1写数据到扩容
    labelInfo.expansionValue = 1;
    labelInfo.shrinkValue = 0;
    ret = GmcUseNamespace(dmlStmt2, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = WriteData(&labelInfo, 0, 3500, dmlConn1, dmlStmt2, SetPkProperty, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t expandNum = labelInfo.expanSionDataNum;

    // 读其它namespace下同名表
    ret = GmcUseNamespace(dmlStmt3, nameSpaceName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ScanNormalByPk(dmlStmt3, labelInfo.labelName, 3500, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(500, ret);

    // 连接3句柄2删除数据直到触发缩容
    // 删除操作句柄可为随机相同连接不同句柄或不同连接
    GmcStmtT *delStmtArray[2] = {dmlStmt2, dmlStmt3};
    GmcConnT *delConnArray[2] = {dmlConn1, dmlConn2};
    int32_t index = GetRandomMode(2);
    if (index == 0) {
        AW_FUN_Log(LOG_STEP, "当前模式:使用相同连接不同句柄");
    } else {
        AW_FUN_Log(LOG_STEP, "当前模式:使用不同连接");
    }

    // 删新表
    ret = GmcUseNamespace(delStmtArray[1 - index], nameSpaceName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = WriteData(
        &labelInfo, 0, 3500, delConnArray[1 - index], delStmtArray[1 - index], SetPkValue, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 连接3句柄2读数据
    ret = GmcUseNamespace(delStmtArray[1 - index], g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ScanNormalByPk(delStmtArray[1 - index], labelInfo.labelName, 3500, 0, 1, 0, 24, 0);
    AW_MACRO_EXPECT_EQ_INT(3500, ret);

    // 删表
    AW_FUN_Log(LOG_STEP, "删表.");
    ret = GmcUseNamespace(ddlStmt, nameSpaceName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(ddlStmt, labelInfo.labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(ddlStmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(ddlStmt, labelInfo.labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(ddlStmt, nameSpaceName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(ddlConn, ddlStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(dmlStmt2);
    ret = testGmcDisconnect(dmlConn1, dmlStmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(dmlConn2, dmlStmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 停止服务
    AW_FUN_Log(LOG_STEP, "停止服务.");
    StopIndexCacheServer();
    AW_FUN_Log(LOG_STEP, "test end.");
}
