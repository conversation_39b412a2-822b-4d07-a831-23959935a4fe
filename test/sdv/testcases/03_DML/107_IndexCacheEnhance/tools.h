/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * @Author: ywx1157510
 * @Date: 2025-07-12
 */

#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"

using namespace std;
#define MAX_CMD_SIZE 1024
#define CONN_NUM 4  // 创建三个长连接  1个用于建表，一个用于获取扩容和缩容数据量，其余两个用于写数据
GmcConnT *g_conn[CONN_NUM];
GmcStmtT *g_stmt[CONN_NUM];

typedef void (*FuncWritePkProperty)(
    GmcStmtT *stmt, int32_t loop, uint32_t vrId, uint32_t vrfIndex, uint32_t destIpAddr, uint8_t maskLen);
typedef void (*FuncWritePkValue)(
    GmcStmtT *stmt, int32_t loop, uint32_t vrId, uint32_t vrfIndex, uint32_t destIpAddr, uint8_t maskLen);

// cceh扩缩容结构体
typedef struct {
    char *labelName;
    char *labelJson;
    char *configJson;
    uint32_t expansionValue;      // 扩容值
    uint32_t shrinkValue;         // 缩容值
    uint32_t expanSionDataNum;    // 触发扩容数据量
    uint32_t shrinkValueDataNum;  // 触发缩容数据量
    bool isOpenCluster;           // 是否开启聚簇容器
} IndexCacheLabelInfoT;

// art索引相关结构
// art node节点数量 结构体
typedef struct {
    uint32_t node4Count;      // 4节点个数
    uint32_t node16Count;     // 16节点个数
    uint32_t node48Count;     // 48节点个数
    uint32_t node256Count;    // 256节点个数
    uint32_t nodeTotalCount;  // 总的节点个数
} NodeCountT;
typedef struct {
    uint32_t freeNode4Count;      // 4节点个数
    uint32_t freeNode16Count;     // 16节点个数
    uint32_t freeNode48Count;     // 48节点个数
    uint32_t freeNode256Count;    // 256节点个数
    uint32_t freeNodeTotalCount;  // 总的节点个数
} FreeNodeCountT;

// art扩缩容结构体
typedef struct {
    char *labelName;
    char *labelJson;
    char *configJson;
    char *indexType;               // 索引类型
    uint32_t indexRecordCount;     // 索引记录数
    NodeCountT nodeCount;          // 申请的node节点信息
    FreeNodeCountT freeNodeCount;  // 申请后已释放的节点信息
} ArtIndexLabelInfoT;

// node扩容节点构成
typedef struct {
    int32_t nodeCount;     // 节点升级模式
    int32_t nodeCount1;    // 第一层节点数据个数
    int32_t nodeCount2;    // 第二层节点数据个数
    int32_t nodeCount3;    // 第三层节点数据个数
    int32_t nodeCount4;    // 第四层节点数据个数
    int32_t arrayIndex1;   // 第二层升级的具体节点下标
    int32_t arrayIndex2;   // 第二层升级的具体节点下标
    int32_t arrayIndex3;   // 第二层升级的具体节点下标
    int32_t *upNode;       // 第一层节点数据
    int32_t *upNode1;      // 第二层节点升级节点数据
    int32_t *upNode2;      // 第三层节点升级节点数据
    int32_t *upNode3;      // 第四层节点升级节点数据
    int32_t *normalNode1;  // 第二层节点普通数据
    int32_t *normalNode2;  // 第三层节点普通数据
    int32_t *normalNode3;  // 第四层节点普通数据
} ArtIndexNodeInfoT;

ArtIndexNodeInfoT g_artNodeInfo;        // 用于全局存放art节点升级信息
ArtIndexLabelInfoT g_artNodeLabelInfo;  // 用于全局存放art节点升级相关信息

void SystemSnprintf(const char *format, ...)
{
    char command[MAX_CMD_SIZE] = {0};
    (void)memset(command, 0, sizeof(command));
    va_list p;
    va_start(p, format);
    (void)vsnprintf(command, MAX_CMD_SIZE, format, p);
    va_end(p);
    int32_t ret = system(command);
    if (ret) {
        AW_FUN_Log(LOG_INFO, "system cmd is excuted no ok !!!");
        AW_FUN_Log(LOG_INFO, "cmd is %s", command);
    }
}

// 启动服务
void StartIndexCacheServer(bool isOpenCluster = false)
{
    // 开启单表缩容功能(memCompactEnable:1,defragmentation:true,minFragmentationRateThreshold:%50)
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"memCompactEnable=1\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"enableClusterHash=0\"");
    if (isOpenCluster) {
        system("sh $TEST_HOME/tools/modifyCfg.sh \"enableClusterHash=1\"");
    } else {
        system("sh $TEST_HOME/tools/modifyCfg.sh \"minFragmentationMemThreshold=0\"");
    }
    system("sh $TEST_HOME/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 停止服务恢复环境
void StopIndexCacheServer()
{
    int32_t ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("sh $TEST_HOME/tools/stop.sh -f");
}

// 启动正常服务
void StartIndexCacheNormalServer(bool isOpenCluster = false)
{
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("sh $TEST_HOME/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/******* ipv4地址 转换为 uint32 *******/
unsigned int TransIp(const char *ipStr)
{
    // 我当前的IP是: *************** (增加一个实例来判断)
    char *ipstr = NULL;
    char strIpIndex[4] = {'\0'};
    unsigned int ipInt, ipAdd = 0, ipIntIndex[4], ipTempNumbr = 24;
    int j = 0, a = 3;
    for (unsigned int i = 0; i <= strlen(ipStr); i++) {
        if (ipStr[i] == '\0' || ipStr[i] == '.') {
            ipInt = atoi(strIpIndex);
            if (ipInt < 0 || ipInt > 255) {
                printf("IP地址有误\n");
                system("pause");
                return 0;
            }
            ipAdd += (ipInt * ((unsigned int)pow(256.0, a)));
            a--;
            memset(strIpIndex, 0, sizeof(strIpIndex));
            j = 0;
            continue;
        }
        strIpIndex[j] = ipStr[i];
        j++;
    }
    return ipAdd;
}

// 获取不同层级扩容数据
// 定长的数据组合（uchar、char、uint8、int8、uint16、int16、uint32、int32、uint64、int64、fixed、time）
// 定长加变长的数据组合
// lpm定长字段的组合
// vr_id uint32
// vrf_index uint32
// dest_ip_addr uint32   dest_ip_addr fixed  16
// mask_len type uint8
// 交换位置
// 随机打乱位置
// 获取随机模式
// 在程序入口处初始化随机种子
void InitRandomSeed()
{
    time_t now = time(0);
    if (now == (time_t)-1) {
        // 处理错误
        fprintf(stderr, "Failed to get time\n");
    } else {
        srand((uint32_t)now);
    }
}
int32_t GetRandomMode(int32_t limitValue)
{
    if (limitValue <= 0) {
        return 0;  // 处理非法参数
    }
    return rand() % limitValue;
}
void ShuffleArray(int array[], int n)
{
    for (int i = n - 1; i > 0; i--) {
        int j = GetRandomMode(i + 1);
        int32_t temp = array[i];
        array[i] = array[j];
        array[j] = temp;
    }
}
// 获取随机的不相同的数字
int32_t GetRandomNoSameNumber(int32_t randomArray[], int32_t randomNum)
{
    // 当前在50以内随机
    int32_t limitValue = 50;
    int32_t arrayValue[limitValue];
    for (int32_t i = 0; i < limitValue; i++) {
        arrayValue[i] = i + 1;
    }
    // 随机打乱数组
    ShuffleArray(arrayValue, limitValue);
    int32_t value = GetRandomMode(limitValue) + 1;
    ;
    for (int32_t i = 0; i < randomNum; i++) {
        randomArray[i] = arrayValue[(i + value) % limitValue];
    }
    for (int32_t i = 0; i < randomNum; i++) {
        printf("%d ", randomArray[i]);
        if (i == randomNum - 1) {
            printf("\n");
        }
    }
    return 0;
}

// 生成节点数据
// 节点信息保存，升级的模式，第一层是否升级，第二层是否升级，第三层是否升级，第四层节点是否升级
void GetArtNodeDataIndfo(
    ArtIndexNodeInfoT *artNodeInfo, int32_t nodeCount, bool isUpOne, bool isUpTwo, bool isUpThree, bool isUpFour)
{
    int32_t nodeCount1 = 2;
    int32_t normalCount = 49;
    if (isUpOne) {
        nodeCount1 = nodeCount;
    }
    int32_t nodeCount2 = nodeCount1 * 2;
    if (isUpTwo) {
        nodeCount2 = nodeCount1 * 2 + nodeCount - 2;
    }
    int32_t nodeCount3 = nodeCount2 * 2;
    if (isUpThree) {
        nodeCount3 = nodeCount2 * 2 + nodeCount - 2;
    }
    int32_t nodeCount4 = nodeCount3 * 2;
    if (isUpFour) {
        nodeCount4 = nodeCount3 * 2 + nodeCount - 2;
    }

    // 扩容节点需要插入这些数据
    int32_t *upNode = (int32_t *)malloc(nodeCount1 * sizeof(int32_t));
    // 普通节点数组
    int32_t *normalNode1 = (int32_t *)malloc(normalCount * sizeof(int32_t));
    // 随机生成目标值
    if (isUpOne || isUpTwo || isUpThree) {
        (void)GetRandomNoSameNumber(upNode, nodeCount1);
    }
    (void)GetRandomNoSameNumber(normalNode1, normalCount);
    // 下一层能够出现扩容的数组下标
    int32_t arrayIndex1 = GetRandomMode(nodeCount1);
    int32_t arrayIndex2 = GetRandomMode(nodeCount2);
    int32_t arrayIndex3 = GetRandomMode(nodeCount3);
    AW_FUN_Log(LOG_INFO, "arrayIndex1 is %d", arrayIndex1);
    AW_FUN_Log(LOG_INFO, "arrayIndex2 is %d", arrayIndex2);
    AW_FUN_Log(LOG_INFO, "arrayIndex3 is %d", arrayIndex3);
    artNodeInfo->nodeCount = nodeCount;
    artNodeInfo->nodeCount1 = nodeCount1;
    artNodeInfo->nodeCount2 = nodeCount2;
    artNodeInfo->nodeCount3 = nodeCount3;
    artNodeInfo->nodeCount4 = nodeCount4;
    artNodeInfo->arrayIndex1 = 1000;
    artNodeInfo->arrayIndex2 = 1000;
    artNodeInfo->arrayIndex3 = 1000;
    if (isUpTwo) {
        artNodeInfo->arrayIndex1 = arrayIndex1;
    }
    if (isUpThree) {
        artNodeInfo->arrayIndex2 = arrayIndex2;
    }
    if (isUpFour) {
        artNodeInfo->arrayIndex3 = arrayIndex3;
    }
    artNodeInfo->upNode = upNode;
    artNodeInfo->upNode1 = upNode;
    artNodeInfo->upNode2 = upNode;
    artNodeInfo->upNode3 = upNode;
    artNodeInfo->normalNode1 = normalNode1;
    artNodeInfo->normalNode2 = normalNode1;
    artNodeInfo->normalNode3 = normalNode1;
}

// 获取单层数据的值
// 第几条数据，随机生成的相关信息，该层节点是否升级,升级时的数组，普通节点的数据，获取的节点层数
int32_t GetNodeValue(
    int32_t i, ArtIndexNodeInfoT artNodeInfo, bool isUp, int32_t upNode[], int32_t normalNode[], int32_t valueMode)
{
    // 获取第四层数据
    int32_t mormalCount = 2;
    int32_t value4Index = i;
    int32_t value4;
    if (valueMode == 4) {
        value4 = normalNode[value4Index % mormalCount];
        if (isUp < 1000) {
            if (value4Index >= artNodeInfo.arrayIndex3 * 2 &&
                value4Index < artNodeInfo.arrayIndex3 * 2 + artNodeInfo.nodeCount) {
                value4 = upNode[(value4Index - artNodeInfo.arrayIndex3 * 2) % (artNodeInfo.nodeCount)];
            }
        }
        return value4;
    }
    // 获取第三层数据
    int32_t value3Index = i;
    // 第四层升级的
    if (artNodeInfo.arrayIndex3 != 1000 && i >= artNodeInfo.arrayIndex3 * 2) {
        if (i > artNodeInfo.arrayIndex3 * 2 + artNodeInfo.nodeCount - 2) {
            value3Index -= artNodeInfo.nodeCount - 2;
        } else {
            value3Index = artNodeInfo.arrayIndex3 * 2;
        }
    }
    value3Index /= 2;

    int32_t value3;
    if (valueMode == 3) {
        value3 = normalNode[value3Index % mormalCount];
        if (isUp < 1000) {
            if (value3Index >= artNodeInfo.arrayIndex2 * 2 &&
                value3Index < artNodeInfo.arrayIndex2 * 2 + artNodeInfo.nodeCount) {
                value3 = upNode[(value3Index - artNodeInfo.arrayIndex2 * 2) % (artNodeInfo.nodeCount)];
            }
        }
        return value3;
    }
    // 获取第二层数据
    int32_t value2Index = value3Index;
    // 第三层升级的
    if (artNodeInfo.arrayIndex2 != 1000 && value3Index >= artNodeInfo.arrayIndex2 * 2) {
        if (value3Index > artNodeInfo.arrayIndex2 * 2 + artNodeInfo.nodeCount - 2) {
            value2Index -= artNodeInfo.nodeCount - 2;
        } else {
            value2Index = artNodeInfo.arrayIndex2 * 2;
        }
    }
    value2Index /= 2;
    int32_t value2;
    if (valueMode == 2) {
        value2 = normalNode[value2Index % mormalCount];
        if (isUp < 1000) {
            if (value2Index >= artNodeInfo.arrayIndex1 * 2 &&
                value2Index < artNodeInfo.arrayIndex1 * 2 + artNodeInfo.nodeCount) {
                value2 = upNode[value2Index - artNodeInfo.arrayIndex1 * 2];
            }
        }
        return value2;
    }
    // 获取第一层数据
    int32_t value1Index = value2Index;
    // 第二层升级的
    if (artNodeInfo.arrayIndex1 != 1000 && value2Index >= artNodeInfo.arrayIndex1 * 2) {
        if (value2Index > artNodeInfo.arrayIndex1 * 2 + artNodeInfo.nodeCount - 2) {
            value1Index -= artNodeInfo.nodeCount - 2;
        } else {
            value1Index = artNodeInfo.arrayIndex1 * 2;
        }
    }
    value1Index /= 2;
    int32_t value1;
    if (valueMode == 1) {
        value1 = upNode[value1Index];
        return value1;
    }
    return -1;
}

// 设置lpmv4索引&&主键字段
void SetLpmPkProperty(
    GmcStmtT *stmt, int32_t loop, uint32_t vrId, uint32_t vrfIndex, uint32_t destIpAddr, uint8_t maskLen)
{
    int32_t ret = 1;
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &vrId, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char ipAddrTmp[16];
    // ************  3232239108  192.地址，切割 --> 二进制（去掉.）--> 10进制
    (void)sprintf(ipAddrTmp, "192.%d.%d.0", loop % 168, loop % 128);
    uint32_t transVal = TransIp(ipAddrTmp);
    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_UINT32, &transVal, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 设置主键字段
void SetPkProperty(GmcStmtT *stmt, int32_t loop, uint32_t vrId, uint32_t vrfIndex, uint32_t destIpAddr, uint8_t maskLen)
{
    int32_t ret = 1;
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &vrId, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char ipAddrTmp[16];
    // ************  3232239108  192.地址，切割 --> 二进制（去掉.）--> 10进制
    (void)sprintf(ipAddrTmp, "192.%d.%d.0", loop % 168, loop % 128);
    uint32_t transVal = TransIp(ipAddrTmp);
    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_UINT32, &transVal, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 设置lpmv6索引&&主键字段
void SetLpmV6PkProperty(
    GmcStmtT *stmt, int32_t loop, uint32_t vrId, uint32_t vrfIndex, char *ipAddrTmp, uint8_t maskLen)
{
    int32_t ret = 1;
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &vrId, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t vrfIndexTmp = vrfIndex + loop;
    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &vrfIndexTmp, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_FIXED, ipAddrTmp, 16);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 设置普通字段
void SetLpmProperty(
    GmcStmtT *stmt, int32_t loop, uint32_t vrId, uint32_t vrfIndex, uint32_t destIpAddr, uint8_t maskLen)
{
    uint8_t wrUint8 = loop % 127;
    uint16_t wrUint16 = loop % 30000;
    uint32_t uint32Tmp = loop;
    uint64_t wrUint64 = loop;
    int32_t ret = 1;
    ret = GmcSetVertexProperty(stmt, "nhp_group_flag", GMC_DATATYPE_UINT8, &wrUint8, sizeof(wrUint8));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // hash index: unique = false
    ret = GmcSetVertexProperty(stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &wrUint16, sizeof(wrUint16));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // hash index: unique = true
    ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT32, &vrId, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // hash index: unique = true
    ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT32, &vrId, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // hash index: unique = false
    ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT32, &uint32Tmp, sizeof(uint32Tmp));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "path_flags", GMC_DATATYPE_UINT32, &uint32Tmp, sizeof(uint32Tmp));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "flags", GMC_DATATYPE_UINT32, &uint32Tmp, sizeof(uint32Tmp));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "status_high_prio", GMC_DATATYPE_UINT8, &wrUint8, sizeof(wrUint8));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "status_normal_prio", GMC_DATATYPE_UINT8, &wrUint8, sizeof(wrUint8));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "errcode_high_prio", GMC_DATATYPE_UINT8, &wrUint8, sizeof(wrUint8));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "errcode_normal_prio", GMC_DATATYPE_UINT8, &wrUint8, sizeof(wrUint8));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // fixed 34
    char wrFixed[34] = "write";
    ret = GmcSetVertexProperty(stmt, "svc_ctx_high_prio", GMC_DATATYPE_FIXED, wrFixed, 34);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // fixed 34
    ret = GmcSetVertexProperty(stmt, "svc_ctx_normal_prio", GMC_DATATYPE_FIXED, wrFixed, 34);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &uint32Tmp, sizeof(uint32Tmp));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "table_smooth_id", GMC_DATATYPE_UINT32, &uint32Tmp, sizeof(uint32Tmp));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &wrUint64, sizeof(wrUint64));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &uint32Tmp, sizeof(uint32Tmp));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "trace", GMC_DATATYPE_UINT64, &wrUint64, sizeof(wrUint64));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "route_flags", GMC_DATATYPE_UINT16, &wrUint16, sizeof(wrUint16));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "reserved", GMC_DATATYPE_UINT16, &wrUint16, sizeof(wrUint16));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 设置普通字段
void SetLocalProperty(
    GmcStmtT *stmt, int32_t loop, uint32_t vrId, uint32_t vrfIndex, uint32_t destIpAddr, uint8_t maskLen)
{
    uint8_t wrUint8 = loop % 127;
    uint16_t wrUint16 = loop % 30000;
    uint32_t uint32Tmp = loop;
    uint64_t wrUint64 = loop;
    int32_t ret = 1;
    // 获取节点数据
    uint32_t value1 = GetNodeValue(loop, g_artNodeInfo, true, g_artNodeInfo.upNode, NULL, 1);
    uint32_t value2 = GetNodeValue(
        loop, g_artNodeInfo, g_artNodeInfo.arrayIndex1, g_artNodeInfo.upNode1, g_artNodeInfo.normalNode1, 2);
    uint32_t value3 = GetNodeValue(
        loop, g_artNodeInfo, g_artNodeInfo.arrayIndex2, g_artNodeInfo.upNode2, g_artNodeInfo.normalNode2, 3);
    uint32_t value4 = GetNodeValue(
        loop, g_artNodeInfo, g_artNodeInfo.arrayIndex3, g_artNodeInfo.upNode3, g_artNodeInfo.normalNode3, 4);

    char ipAddrTmp1[34];
    (void)sprintf(ipAddrTmp1, "%c%c%c%c", (char)value1, (char)value2, (char)value3, (char)value4);
    ret = GmcSetVertexProperty(stmt, "localKey", GMC_DATATYPE_FIXED, &ipAddrTmp1, 34);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "nhp_group_flag", GMC_DATATYPE_UINT8, &wrUint8, sizeof(wrUint8));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // hash index: unique = false
    ret = GmcSetVertexProperty(stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &wrUint16, sizeof(wrUint16));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // hash index: unique = true
    ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT32, &vrId, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // hash index: unique = true
    ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT32, &vrId, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // hash index: unique = false
    ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT32, &uint32Tmp, sizeof(uint32Tmp));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "path_flags", GMC_DATATYPE_UINT32, &uint32Tmp, sizeof(uint32Tmp));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "flags", GMC_DATATYPE_UINT32, &uint32Tmp, sizeof(uint32Tmp));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "status_high_prio", GMC_DATATYPE_UINT8, &wrUint8, sizeof(wrUint8));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "status_normal_prio", GMC_DATATYPE_UINT8, &wrUint8, sizeof(wrUint8));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "errcode_high_prio", GMC_DATATYPE_UINT8, &wrUint8, sizeof(wrUint8));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "errcode_normal_prio", GMC_DATATYPE_UINT8, &wrUint8, sizeof(wrUint8));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // fixed 34
    char wrFixed[34] = "write";
    ret = GmcSetVertexProperty(stmt, "svc_ctx_high_prio", GMC_DATATYPE_FIXED, wrFixed, 34);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // fixed 34
    ret = GmcSetVertexProperty(stmt, "svc_ctx_normal_prio", GMC_DATATYPE_FIXED, wrFixed, 34);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &uint32Tmp, sizeof(uint32Tmp));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "table_smooth_id", GMC_DATATYPE_UINT32, &uint32Tmp, sizeof(uint32Tmp));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &wrUint64, sizeof(wrUint64));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &uint32Tmp, sizeof(uint32Tmp));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "trace", GMC_DATATYPE_UINT64, &wrUint64, sizeof(wrUint64));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "route_flags", GMC_DATATYPE_UINT16, &wrUint16, sizeof(wrUint16));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "reserved", GMC_DATATYPE_UINT16, &wrUint16, sizeof(wrUint16));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 设置普通字段
void SetHashclusterProperty(
    GmcStmtT *stmt, int32_t loop, uint32_t vrId, uint32_t vrfIndex, uint32_t destIpAddr, uint8_t maskLen)
{
    uint8_t wrUint8 = loop % 127;
    uint16_t wrUint16 = loop % 30000;
    uint32_t uint32Tmp = loop;
    uint64_t wrUint64 = loop;
    int32_t ret = 1;
    // 获取节点数据
    uint32_t value1 = GetNodeValue(loop, g_artNodeInfo, true, g_artNodeInfo.upNode, NULL, 1);
    uint32_t value2 = GetNodeValue(
        loop, g_artNodeInfo, g_artNodeInfo.arrayIndex1, g_artNodeInfo.upNode1, g_artNodeInfo.normalNode1, 2);
    uint32_t value3 = GetNodeValue(
        loop, g_artNodeInfo, g_artNodeInfo.arrayIndex2, g_artNodeInfo.upNode2, g_artNodeInfo.normalNode2, 3);
    uint32_t value4 = GetNodeValue(
        loop, g_artNodeInfo, g_artNodeInfo.arrayIndex3, g_artNodeInfo.upNode3, g_artNodeInfo.normalNode3, 4);

    char ipAddrTmp1[34];
    (void)sprintf(ipAddrTmp1, "%c%c%c%c", (char)value1, (char)value2, (char)value3, (char)value4);
    ret = GmcSetVertexProperty(stmt, "hashclusterKey", GMC_DATATYPE_FIXED, &ipAddrTmp1, 34);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "nhp_group_flag", GMC_DATATYPE_UINT8, &wrUint8, sizeof(wrUint8));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // hash index: unique = false
    ret = GmcSetVertexProperty(stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &wrUint16, sizeof(wrUint16));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // hash index: unique = true
    ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT32, &vrId, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // hash index: unique = true
    ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT32, &vrId, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // hash index: unique = false
    ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT32, &uint32Tmp, sizeof(uint32Tmp));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "path_flags", GMC_DATATYPE_UINT32, &uint32Tmp, sizeof(uint32Tmp));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "flags", GMC_DATATYPE_UINT32, &uint32Tmp, sizeof(uint32Tmp));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "status_high_prio", GMC_DATATYPE_UINT8, &wrUint8, sizeof(wrUint8));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "status_normal_prio", GMC_DATATYPE_UINT8, &wrUint8, sizeof(wrUint8));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "errcode_high_prio", GMC_DATATYPE_UINT8, &wrUint8, sizeof(wrUint8));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "errcode_normal_prio", GMC_DATATYPE_UINT8, &wrUint8, sizeof(wrUint8));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // fixed 34
    char wrFixed[34] = "write";
    ret = GmcSetVertexProperty(stmt, "svc_ctx_high_prio", GMC_DATATYPE_FIXED, wrFixed, 34);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // fixed 34
    ret = GmcSetVertexProperty(stmt, "svc_ctx_normal_prio", GMC_DATATYPE_FIXED, wrFixed, 34);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &uint32Tmp, sizeof(uint32Tmp));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "table_smooth_id", GMC_DATATYPE_UINT32, &uint32Tmp, sizeof(uint32Tmp));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &wrUint64, sizeof(wrUint64));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &uint32Tmp, sizeof(uint32Tmp));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "trace", GMC_DATATYPE_UINT64, &wrUint64, sizeof(wrUint64));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "route_flags", GMC_DATATYPE_UINT16, &wrUint16, sizeof(wrUint16));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "reserved", GMC_DATATYPE_UINT16, &wrUint16, sizeof(wrUint16));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 设置lpmv4索引&&主键字段
void SetLpmV4PkProperty(
    GmcStmtT *stmt, int32_t loop, uint32_t vrId, uint32_t vrfIndex, uint32_t destIpAddr, uint8_t maskLen)
{
    int32_t ret = 1;
    int32_t i = vrfIndex;
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &vrId, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    vrfIndex = vrId + 1;
    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t value4 = GetNodeValue(
        loop, g_artNodeInfo, g_artNodeInfo.arrayIndex3, g_artNodeInfo.upNode3, g_artNodeInfo.normalNode3, 4);
    int32_t value3 = GetNodeValue(
        loop, g_artNodeInfo, g_artNodeInfo.arrayIndex2, g_artNodeInfo.upNode2, g_artNodeInfo.normalNode2, 3);
    int32_t value2 = GetNodeValue(
        loop, g_artNodeInfo, g_artNodeInfo.arrayIndex1, g_artNodeInfo.upNode1, g_artNodeInfo.normalNode1, 2);

    int32_t value1 = GetNodeValue(loop, g_artNodeInfo, true, g_artNodeInfo.upNode, NULL, 1);

    char ipAddrTmp[16];
    (void)sprintf(ipAddrTmp, "%d.%d.%d.%d", value1, value2, value3, value4);
    uint32_t transVal = TransIp(ipAddrTmp);
    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_UINT32, &transVal, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 设置lpmv6索引&&主键字段
uint8_t g_ipAddrTmp[16] = {
    0xff, 0xcd, 0x91, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00, 0x20, 0x20};
void SetLpmV6PkProperty(
    GmcStmtT *stmt, int32_t loop, uint32_t vrId, uint32_t vrfIndex, uint32_t destIpAddr, uint8_t maskLen)
{
    int32_t ret = 1;
    int32_t i = vrfIndex;
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &vrId, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    vrfIndex = vrId + 1;
    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t value1 = GetNodeValue(loop, g_artNodeInfo, true, g_artNodeInfo.upNode, NULL, 1);
    int32_t value2 = GetNodeValue(
        loop, g_artNodeInfo, g_artNodeInfo.arrayIndex1, g_artNodeInfo.upNode1, g_artNodeInfo.normalNode1, 2);
    int32_t value3 = GetNodeValue(
        loop, g_artNodeInfo, g_artNodeInfo.arrayIndex2, g_artNodeInfo.upNode2, g_artNodeInfo.normalNode2, 3);
    int32_t value4 = GetNodeValue(
        loop, g_artNodeInfo, g_artNodeInfo.arrayIndex3, g_artNodeInfo.upNode3, g_artNodeInfo.normalNode3, 4);
    g_ipAddrTmp[0] = (char)value1;
    g_ipAddrTmp[1] = (char)value2;
    g_ipAddrTmp[2] = (char)value3;
    g_ipAddrTmp[3] = (char)value4;

    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_FIXED, g_ipAddrTmp, 16);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 设置lpmv4索引&&主键索引值
void SetLpmV4PkIndexValue(
    GmcStmtT *stmt, int32_t loop, uint32_t vrId, uint32_t vrfIndex, uint32_t destIpAddr, uint8_t maskLen)
{
    char ipAddrTmp[16];
    int32_t ret = 1;

    uint32_t value1 = GetNodeValue(loop, g_artNodeInfo, true, g_artNodeInfo.upNode, NULL, 1);
    uint32_t value2 = GetNodeValue(
        loop, g_artNodeInfo, g_artNodeInfo.arrayIndex1, g_artNodeInfo.upNode1, g_artNodeInfo.normalNode1, 2);
    uint32_t value3 = GetNodeValue(
        loop, g_artNodeInfo, g_artNodeInfo.arrayIndex2, g_artNodeInfo.upNode2, g_artNodeInfo.normalNode2, 3);
    uint32_t value4 = GetNodeValue(
        loop, g_artNodeInfo, g_artNodeInfo.arrayIndex3, g_artNodeInfo.upNode3, g_artNodeInfo.normalNode3, 4);
    (void)sprintf(ipAddrTmp, "%d.%d.%d.%d", value1, value2, value3, value4);
    uint32_t transVal = TransIp(ipAddrTmp);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vrId, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    vrfIndex = vrId + 1;
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &transVal, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, "primary_key");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void SetPkIndexValue(
    GmcStmtT *stmt, int32_t loop, uint32_t vrId, uint32_t vrfIndex, uint32_t destIpAddr, uint8_t maskLen)
{
    char ipAddrTmp[16];
    (void)sprintf(ipAddrTmp, "192.%d.%d.0", loop % 168, loop % 128);
    uint32_t transVal = TransIp(ipAddrTmp);
    int32_t ret = 1;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vrId, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &transVal, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_key");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 设置lpmv6索引&&主键索引值
void SetLpmV6PkIndexValue(
    GmcStmtT *stmt, int32_t loop, uint32_t vrId, uint32_t vrfIndex, uint32_t destIpAddr, uint8_t maskLen)
{
    uint32_t value1 = GetNodeValue(loop, g_artNodeInfo, true, g_artNodeInfo.upNode, NULL, 1);
    uint32_t value2 = GetNodeValue(
        loop, g_artNodeInfo, g_artNodeInfo.arrayIndex1, g_artNodeInfo.upNode1, g_artNodeInfo.normalNode1, 2);
    uint32_t value3 = GetNodeValue(
        loop, g_artNodeInfo, g_artNodeInfo.arrayIndex2, g_artNodeInfo.upNode2, g_artNodeInfo.normalNode2, 3);
    uint32_t value4 = GetNodeValue(
        loop, g_artNodeInfo, g_artNodeInfo.arrayIndex3, g_artNodeInfo.upNode3, g_artNodeInfo.normalNode3, 4);
    g_ipAddrTmp[0] = (char)value1;
    g_ipAddrTmp[1] = (char)value2;
    g_ipAddrTmp[2] = (char)value3;
    g_ipAddrTmp[3] = (char)value4;
    int32_t ret = 1;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vrId, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    vrfIndex = vrId + 1;
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, g_ipAddrTmp, 16);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_key");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void SetPkValue(GmcStmtT *stmt, int32_t loop, uint32_t vrId, uint32_t vrfIndex, uint32_t destIpAddr, uint8_t maskLen)
{
    char ipAddrTmp[16];
    (void)sprintf(ipAddrTmp, "192.%d.%d.0", loop % 168, loop % 128);
    uint32_t transVal = TransIp(ipAddrTmp);
    int32_t ret = 1;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vrId, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &transVal, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_key");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 主键读数据
int32_t ScanNormalByPk(GmcStmtT *stmt, const char *vtxLabelName, uint32_t recordNum, uint32_t vrId, uint32_t vrfIndex,
    uint32_t destIpAddr, uint8_t maskLen, uint32_t offsetValue = 0, uint32_t offset = 0, int32_t expectRet = GMERR_OK)
{
    uint8_t wrUint8 = 0;
    uint16_t wrUint16 = 0;
    uint32_t uint32Tmp = 0;
    uint64_t wrUint64 = 0;
    char wrFixed[34] = "write";
    int32_t cnt = 0;
    int32_t ret = testGmcPrepareStmtByLabelName(stmt, vtxLabelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = offset; i < recordNum; i++) {
        SetPkIndexValue(stmt, i, 0, (i + 1) % 1024, i, 24);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (ret) {
            AW_FUN_Log(LOG_INFO, "scan data no ok ret is %d", ret);
            break;
        }

        wrUint8 = (i + offsetValue) % 127;
        wrUint16 = (i + offsetValue) % 30000;
        uint32Tmp = i + offsetValue;
        wrUint64 = i + offsetValue;
        // GET F0 and compare
        while (true) {
            bool isFinish;
            ret = GmcFetch(stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (isFinish || ret != GMERR_OK) {
                break;
            }
            if (ret != expectRet) {
                return -1;
            }
            // Get nhp_group_flag
            ret = queryPropertyAndCompare(stmt, "nhp_group_flag", GMC_DATATYPE_UINT8, &wrUint8);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // Get qos_profile_id
            ret = queryPropertyAndCompare(stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &wrUint16);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // Get primary_label
            ret = queryPropertyAndCompare(stmt, "primary_label", GMC_DATATYPE_UINT32, &vrId);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // Get attribute_id
            ret = queryPropertyAndCompare(stmt, "attribute_id", GMC_DATATYPE_UINT32, &vrId);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // Get nhp_group_id
            ret = queryPropertyAndCompare(stmt, "nhp_group_id", GMC_DATATYPE_UINT32, &uint32Tmp);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // Get path_flags
            ret = queryPropertyAndCompare(stmt, "path_flags", GMC_DATATYPE_UINT32, &uint32Tmp);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // Get flags
            ret = queryPropertyAndCompare(stmt, "flags", GMC_DATATYPE_UINT32, &uint32Tmp);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // Get status_high_prio
            ret = queryPropertyAndCompare(stmt, "status_high_prio", GMC_DATATYPE_UINT8, &wrUint8);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // Get status_normal_prio
            ret = queryPropertyAndCompare(stmt, "status_normal_prio", GMC_DATATYPE_UINT8, &wrUint8);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // Get errcode_high_prio
            ret = queryPropertyAndCompare(stmt, "errcode_high_prio", GMC_DATATYPE_UINT8, &wrUint8);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // Get errcode_normal_prio
            ret = queryPropertyAndCompare(stmt, "errcode_normal_prio", GMC_DATATYPE_UINT8, &wrUint8);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // Get svc_ctx_high_prio
            ret = queryPropertyAndCompare(stmt, "svc_ctx_high_prio", GMC_DATATYPE_FIXED, wrFixed);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // Get svc_ctx_normal_prio
            ret = queryPropertyAndCompare(stmt, "svc_ctx_normal_prio", GMC_DATATYPE_FIXED, wrFixed);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // Get app_source_id
            ret = queryPropertyAndCompare(stmt, "app_source_id", GMC_DATATYPE_UINT32, &uint32Tmp);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // Get table_smooth_id
            ret = queryPropertyAndCompare(stmt, "table_smooth_id", GMC_DATATYPE_UINT32, &uint32Tmp);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // Get app_obj_id
            ret = queryPropertyAndCompare(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &wrUint64);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // Get app_version
            ret = queryPropertyAndCompare(stmt, "app_version", GMC_DATATYPE_UINT32, &uint32Tmp);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // Get trace
            ret = queryPropertyAndCompare(stmt, "trace", GMC_DATATYPE_UINT64, &wrUint64);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // Get route_flags
            ret = queryPropertyAndCompare(stmt, "route_flags", GMC_DATATYPE_UINT16, &wrUint16);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // Get reserved
            ret = queryPropertyAndCompare(stmt, "reserved", GMC_DATATYPE_UINT16, &wrUint16);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            cnt++;
        }
    }
    return cnt;
}

// 主键读数据
int32_t ScanLpmv4ByPk(GmcStmtT *stmt, const char *vtxLabelName, uint32_t recordNum, uint32_t vrId, uint32_t vrfIndex,
    uint32_t destIpAddr, uint8_t maskLen, uint32_t offsetValue = 0, uint32_t offset = 0, int32_t expectRet = GMERR_OK)
{
    uint8_t wrUint8 = 0;
    uint16_t wrUint16 = 0;
    uint32_t uint32Tmp = 0;
    uint64_t wrUint64 = 0;
    char wrFixed[34] = "write";
    int32_t cnt = 0;
    int32_t ret = testGmcPrepareStmtByLabelName(stmt, vtxLabelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = offset; i < recordNum; i++) {
        SetLpmV4PkIndexValue(stmt, i, 0, (i + 1) % 1024, i, 32);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (ret) {
            AW_FUN_Log(LOG_INFO, "scan data no ok ret is %d", ret);
            break;
        }

        wrUint8 = (i + offsetValue) % 127;
        wrUint16 = (i + offsetValue) % 30000;
        uint32Tmp = i + offsetValue;
        wrUint64 = i + offsetValue;
        // GET F0 and compare
        while (true) {
            bool isFinish;
            ret = GmcFetch(stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (isFinish || ret != GMERR_OK) {
                break;
            }
            if (ret != expectRet) {
                return -1;
            }
            // Get nhp_group_flag
            ret = queryPropertyAndCompare(stmt, "nhp_group_flag", GMC_DATATYPE_UINT8, &wrUint8);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // Get qos_profile_id
            ret = queryPropertyAndCompare(stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &wrUint16);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // Get primary_label
            ret = queryPropertyAndCompare(stmt, "primary_label", GMC_DATATYPE_UINT32, &vrId);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // Get attribute_id
            ret = queryPropertyAndCompare(stmt, "attribute_id", GMC_DATATYPE_UINT32, &vrId);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // Get nhp_group_id
            ret = queryPropertyAndCompare(stmt, "nhp_group_id", GMC_DATATYPE_UINT32, &uint32Tmp);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // Get path_flags
            ret = queryPropertyAndCompare(stmt, "path_flags", GMC_DATATYPE_UINT32, &uint32Tmp);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // Get flags
            ret = queryPropertyAndCompare(stmt, "flags", GMC_DATATYPE_UINT32, &uint32Tmp);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // Get status_high_prio
            ret = queryPropertyAndCompare(stmt, "status_high_prio", GMC_DATATYPE_UINT8, &wrUint8);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // Get status_normal_prio
            ret = queryPropertyAndCompare(stmt, "status_normal_prio", GMC_DATATYPE_UINT8, &wrUint8);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // Get errcode_high_prio
            ret = queryPropertyAndCompare(stmt, "errcode_high_prio", GMC_DATATYPE_UINT8, &wrUint8);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // Get errcode_normal_prio
            ret = queryPropertyAndCompare(stmt, "errcode_normal_prio", GMC_DATATYPE_UINT8, &wrUint8);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // Get svc_ctx_high_prio
            ret = queryPropertyAndCompare(stmt, "svc_ctx_high_prio", GMC_DATATYPE_FIXED, wrFixed);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // Get svc_ctx_normal_prio
            ret = queryPropertyAndCompare(stmt, "svc_ctx_normal_prio", GMC_DATATYPE_FIXED, wrFixed);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // Get app_source_id
            ret = queryPropertyAndCompare(stmt, "app_source_id", GMC_DATATYPE_UINT32, &uint32Tmp);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // Get table_smooth_id
            ret = queryPropertyAndCompare(stmt, "table_smooth_id", GMC_DATATYPE_UINT32, &uint32Tmp);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // Get app_obj_id
            ret = queryPropertyAndCompare(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &wrUint64);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // Get app_version
            ret = queryPropertyAndCompare(stmt, "app_version", GMC_DATATYPE_UINT32, &uint32Tmp);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // Get trace
            ret = queryPropertyAndCompare(stmt, "trace", GMC_DATATYPE_UINT64, &wrUint64);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // Get route_flags
            ret = queryPropertyAndCompare(stmt, "route_flags", GMC_DATATYPE_UINT16, &wrUint16);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // Get reserved
            ret = queryPropertyAndCompare(stmt, "reserved", GMC_DATATYPE_UINT16, &wrUint16);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            cnt++;
        }
    }
    return cnt;
}
// 主键读数据
int32_t ScanLpmv6ByPk(GmcStmtT *stmt, const char *vtxLabelName, uint32_t recordNum, uint32_t vrId, uint32_t vrfIndex,
    uint32_t destIpAddr, uint8_t maskLen, uint32_t offsetValue = 0, uint32_t offset = 0, int32_t expectRet = GMERR_OK)
{
    uint8_t wrUint8 = 0;
    uint16_t wrUint16 = 0;
    uint32_t uint32Tmp = 0;
    uint64_t wrUint64 = 0;
    char wrFixed[34] = "write";
    int32_t cnt = 0;
    int32_t ret = testGmcPrepareStmtByLabelName(stmt, vtxLabelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = offset; i < recordNum; i++) {
        SetLpmV6PkIndexValue(stmt, i, 0, (i + 1) % 1024, i, 128);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (ret) {
            AW_FUN_Log(LOG_INFO, "scan data no ok ret is %d", ret);
            break;
        }

        wrUint8 = (i + offsetValue) % 127;
        wrUint16 = (i + offsetValue) % 30000;
        uint32Tmp = i + offsetValue;
        wrUint64 = i + offsetValue;
        // GET F0 and compare
        while (true) {
            bool isFinish;
            ret = GmcFetch(stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (isFinish || ret != GMERR_OK) {
                break;
            }
            if (ret != expectRet) {
                return -1;
            }
            // Get nhp_group_flag
            ret = queryPropertyAndCompare(stmt, "nhp_group_flag", GMC_DATATYPE_UINT8, &wrUint8);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // Get qos_profile_id
            ret = queryPropertyAndCompare(stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &wrUint16);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // Get primary_label
            ret = queryPropertyAndCompare(stmt, "primary_label", GMC_DATATYPE_UINT32, &vrId);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // Get attribute_id
            ret = queryPropertyAndCompare(stmt, "attribute_id", GMC_DATATYPE_UINT32, &vrId);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // Get nhp_group_id
            ret = queryPropertyAndCompare(stmt, "nhp_group_id", GMC_DATATYPE_UINT32, &uint32Tmp);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // Get path_flags
            ret = queryPropertyAndCompare(stmt, "path_flags", GMC_DATATYPE_UINT32, &uint32Tmp);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // Get flags
            ret = queryPropertyAndCompare(stmt, "flags", GMC_DATATYPE_UINT32, &uint32Tmp);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // Get status_high_prio
            ret = queryPropertyAndCompare(stmt, "status_high_prio", GMC_DATATYPE_UINT8, &wrUint8);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // Get status_normal_prio
            ret = queryPropertyAndCompare(stmt, "status_normal_prio", GMC_DATATYPE_UINT8, &wrUint8);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // Get errcode_high_prio
            ret = queryPropertyAndCompare(stmt, "errcode_high_prio", GMC_DATATYPE_UINT8, &wrUint8);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // Get errcode_normal_prio
            ret = queryPropertyAndCompare(stmt, "errcode_normal_prio", GMC_DATATYPE_UINT8, &wrUint8);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // Get svc_ctx_high_prio
            ret = queryPropertyAndCompare(stmt, "svc_ctx_high_prio", GMC_DATATYPE_FIXED, wrFixed);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // Get svc_ctx_normal_prio
            ret = queryPropertyAndCompare(stmt, "svc_ctx_normal_prio", GMC_DATATYPE_FIXED, wrFixed);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // Get app_source_id
            ret = queryPropertyAndCompare(stmt, "app_source_id", GMC_DATATYPE_UINT32, &uint32Tmp);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // Get table_smooth_id
            ret = queryPropertyAndCompare(stmt, "table_smooth_id", GMC_DATATYPE_UINT32, &uint32Tmp);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // Get app_obj_id
            ret = queryPropertyAndCompare(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &wrUint64);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // Get app_version
            ret = queryPropertyAndCompare(stmt, "app_version", GMC_DATATYPE_UINT32, &uint32Tmp);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // Get trace
            ret = queryPropertyAndCompare(stmt, "trace", GMC_DATATYPE_UINT64, &wrUint64);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // Get route_flags
            ret = queryPropertyAndCompare(stmt, "route_flags", GMC_DATATYPE_UINT16, &wrUint16);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // Get reserved
            ret = queryPropertyAndCompare(stmt, "reserved", GMC_DATATYPE_UINT16, &wrUint16);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            cnt++;
            if (ret) {
                return ret;
            }
        }
    }
    return cnt;
}

// 初始化结构体
void InitLabelInfo(IndexCacheLabelInfoT *labelInfo, char *labelName)
{
    labelInfo->labelName = labelName;
    labelInfo->labelJson = NULL;
    labelInfo->configJson = NULL;
    labelInfo->expansionValue = 0;
    labelInfo->shrinkValue = 0;
    labelInfo->expanSionDataNum = 0;
    labelInfo->shrinkValueDataNum = 0;
    labelInfo->isOpenCluster = false;
}

// 查询索引相关页变更视图
int32_t GetTriggerExpansionOrshrinkValue(IndexCacheLabelInfoT *labelInfo)
{
    char cmd[1024] = {};
    // 开聚簇容器
    if (labelInfo->isOpenCluster) {
        // STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT
        (void)sprintf(cmd, "STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT -f LABEL_NAME=%s", labelInfo->labelName);
        labelInfo->expansionValue = GetViewValueByField(cmd, "SEGMENT_PAGE_COUNT");
        labelInfo->shrinkValue = GetViewValueByField(cmd, "SCALE_IN_ACTUAL_BEGIN_COUNT");
    } else {
        // STORAGE_HASH_INDEX_STAT
        (void)sprintf(cmd, "STORAGE_HASH_INDEX_STAT -f LABEL_NAME=%s", labelInfo->labelName);
        labelInfo->expansionValue = GetViewValueByField(cmd, "SEGMENT_NUM");
        labelInfo->shrinkValue = GetViewValueByField(cmd, "SCALE_IN_COUNT");
    }
    return 0;
}

// 查询art索引升降级变更视图
int32_t GetArtTriggerExpansionOrshrinkValue(ArtIndexLabelInfoT *labelInfo)
{
    char cmd[1024] = {};
    // STORAGE_ART_INDEX_STAT
    (void)sprintf(cmd, "STORAGE_ART_INDEX_STAT -f LABEL_NAME=%s", labelInfo->labelName);
    labelInfo->indexRecordCount = GetViewValueByField(cmd, "RECORD_COUNT");
    labelInfo->nodeCount.nodeTotalCount = GetViewValueByField(cmd, "TOTAL_NODE_COUNT");
    labelInfo->nodeCount.node4Count = GetViewValueByField(cmd, "ART_NODE4_COUNT");
    labelInfo->nodeCount.node16Count = GetViewValueByField(cmd, "ART_NODE16_COUNT");
    labelInfo->nodeCount.node48Count = GetViewValueByField(cmd, "ART_NODE48_COUNT");
    labelInfo->nodeCount.node256Count = GetViewValueByField(cmd, "ART_NODE256_COUNT");
    labelInfo->freeNodeCount.freeNodeTotalCount = GetViewValueByField(cmd, "TOTAL_FREE_NODE_COUNT");
    labelInfo->freeNodeCount.freeNode4Count = GetViewValueByField(cmd, "ART_FREE_NODE4_COUNT");
    labelInfo->freeNodeCount.freeNode16Count = GetViewValueByField(cmd, "ART_FREE_NODE16_COUNT");
    labelInfo->freeNodeCount.freeNode48Count = GetViewValueByField(cmd, "ART_FREE_NODE48_COUNT");
    labelInfo->freeNodeCount.freeNode256Count = GetViewValueByField(cmd, "ART_FREE_NODE256_COUNT");
    SystemSnprintf("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -f LABEL_NAME=%s", labelInfo->labelName);
    return 0;
}

// 获取扩容和缩容的数据量
int32_t GetLabeMemChangeValue(
    IndexCacheLabelInfoT *labelInfo, FuncWritePkProperty setPkProperty, FuncWritePkValue setPkValue)
{
    int32_t ret = 1;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {0};
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建表
    GmcDropVertexLabel(stmt, labelInfo->labelName);
    ret = GmcCreateVertexLabel(stmt, labelInfo->labelJson, labelInfo->configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置初始扩缩容值
    uint32_t initExpansionValue = 1;
    uint32_t initShrinkValue = 0;

    // 写数据获取扩容值
    ret = testGmcPrepareStmtByLabelName(stmt, labelInfo->labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t i = 0;
    // 当前预估首次扩容在160条数据左右,从该数据量开始找触发扩容数据量
    int32_t insertNum = 160;
    if (!labelInfo->isOpenCluster) {
        insertNum = 1024;
    }
    for (i = 0; i < insertNum; i++) {
        setPkProperty(stmt, i, 0, (i + 1) % 1024, i, 24);
        SetLpmProperty(stmt, i, 0, 1, i, 24);
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    if (ret) {
        GmcBatchDestroy(batch);
        batch = NULL;
        AW_FUN_Log(LOG_INFO, "【GetLabeMemChangeValue】获取扩容数据量失败 ret is %d", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    while (ret == GMERR_OK && ((initExpansionValue + 1) != labelInfo->expansionValue)) {
        setPkProperty(stmt, i, 0, (i + 1) % 1024, i, 24);
        SetLpmProperty(stmt, i, 0, 1, i, 24);
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchExecute(batch, &batchRet);
        if (ret) {
            GmcBatchDestroy(batch);
            batch = NULL;
            AW_FUN_Log(LOG_INFO, "【GetLabeMemChangeValue】获取扩容数据量失败 ret is %d", ret);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            return ret;
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (i < 200 || i > 2000) {
            // 视图查询特别耗时
            GetTriggerExpansionOrshrinkValue(labelInfo);
        }
        i++;
    }
    // 查询视图获取页是否增加
    AW_FUN_Log(LOG_INFO, "【GetLabeMemChangeValue】获取扩容数据量成功 触发扩容数据量是 %d", i);
    GetTriggerExpansionOrshrinkValue(labelInfo);
    AW_MACRO_EXPECT_EQ_INT(initExpansionValue + 1, labelInfo->expansionValue);
    AW_MACRO_EXPECT_EQ_INT(initShrinkValue, labelInfo->shrinkValue);
    labelInfo->expanSionDataNum = i;
    SystemSnprintf("gmsysview count %s -ns %s", labelInfo->labelName, g_testNameSpace);

    // 删除数据获取缩容值(删除的数据量是触发数据量的3/4)(minFragmentationRateThreshold单表内存占用率低于该值就会触发缩容)
    int32_t delDataNum = i;
    // 第一次触发缩容是立即触发不需要等60s
    ret = testGmcPrepareStmtByLabelName(stmt, labelInfo->labelName, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = 0; i < delDataNum; i++) {
        setPkValue(stmt, i, 0, (i + 1) % 1024, i, 24);
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchExecute(batch, &batchRet);
        if (ret) {
            GmcBatchDestroy(batch);
            batch = NULL;
            AW_FUN_Log(LOG_INFO, "【GetLabeMemChangeValue】获取缩容容数据量失败 ret is %d", ret);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            return ret;
        }
        if (i < 10 || i > 1500) {
            // 视图查询特别耗时
            GetTriggerExpansionOrshrinkValue(labelInfo);
        }
        if (labelInfo->shrinkValue == (initShrinkValue + 1)) {
            i++;
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询视图获取缩容页是否增加
    GetTriggerExpansionOrshrinkValue(labelInfo);
    AW_FUN_Log(LOG_INFO, "【GetLabeMemChangeValue】获取缩容数据量成功 is %d", i);
    labelInfo->shrinkValueDataNum = i;
    SystemSnprintf("gmsysview count %s -ns %s", labelInfo->labelName, g_testNameSpace);
    SystemSnprintf("gmsysview -q V\\$STORAGE_HASH_INDEX_STAT -f LABEL_NAME=%s", labelInfo->labelName);
    AW_MACRO_EXPECT_EQ_INT(initShrinkValue + 1, labelInfo->shrinkValue);

    AW_FUN_Log(LOG_INFO, "【GetLabeMemChangeValue】获取扩容数据量成功 is %d", labelInfo->expanSionDataNum);
    AW_FUN_Log(LOG_INFO, "【GetLabeMemChangeValue】获取缩容数据量成功 is %d", labelInfo->shrinkValueDataNum);

    GmcBatchDestroy(batch);
    batch = NULL;
    // 删表
    ret = GmcDropVertexLabel(stmt, labelInfo->labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

// 写数据直到扩容
int32_t WriteDataUntilExpand(IndexCacheLabelInfoT *labelInfo, GmcConnT *conn, GmcStmtT *stmt,
    FuncWritePkProperty setPkProperty, GmcOperationTypeE operationType = GMC_OPERATION_INSERT)
{
    int32_t ret = 1;
    int32_t isBatch = 0;
    bool isOnceWrite = false;
    // 获取随机模式
    isBatch = GetRandomMode(3);  // 单写，批量单写，批量多写
    isOnceWrite = GetRandomMode(2);
    // 设置初始扩缩容值
    uint32_t initExpansionValue = labelInfo->expansionValue;
    uint32_t initShrinkValue = labelInfo->shrinkValue;

    // 判断是否是第二次触发扩容
    bool isSecondTriggerExpansion = false;
    if (initShrinkValue > 0) {
        isOnceWrite = 1;
        isBatch = 1;
        isSecondTriggerExpansion = true;
    }
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {0};

    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据获取扩容值
    ret = testGmcPrepareStmtByLabelName(stmt, labelInfo->labelName, operationType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t i = 0;
    int32_t insertNum = labelInfo->expanSionDataNum;
    if (isSecondTriggerExpansion) {
        insertNum = labelInfo->expanSionDataNum * 3;
    }
    if (!isOnceWrite) {
        SystemSnprintf("gmsysview count %s -ns %s", labelInfo->labelName, g_testNameSpace);
        AW_FUN_Log(LOG_INFO, "【WriteDataUntilExpand】分两次写数据第二次触发扩容");
        insertNum -= 1;
        if (isBatch == 1) {
            AW_FUN_Log(LOG_INFO, "【WriteDataUntilExpand】批量单写");
        }
    } else {
        AW_FUN_Log(LOG_INFO, "【WriteDataUntilExpand】一次性写数据直到扩容");
    }
    if (isBatch) {
        AW_FUN_Log(LOG_INFO, "【WriteDataUntilExpand】第一次为批写");
    } else {
        AW_FUN_Log(LOG_INFO, "【WriteDataUntilExpand】第一次为单写");
    }
    for (i = 0; i < insertNum; i++) {
        if (isSecondTriggerExpansion && labelInfo->shrinkValueDataNum != 0 && labelInfo->shrinkValueDataNum <= i &&
            i < labelInfo->expanSionDataNum) {
            continue;
        }
        setPkProperty(stmt, i, 0, (i + 1) % 1024, i, 24);
        SetLpmProperty(stmt, i, 0, 1, i, 24);
        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            if (ret) {
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                AW_FUN_Log(LOG_INFO, "【WriteDataUntilExpand】GmcBatchAddDML no ok i is %d", i);
            }
            if (i % 1023 == 0 && i > 0 && (i + 1 != insertNum) && isBatch == 2) {
                ret = GmcBatchExecute(batch, &batchRet);
                if (ret) {
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    AW_FUN_Log(LOG_INFO, "【WriteDataUntilExpand】GmcBatchExecute no ok i is %d", i);
                    break;
                }
            }
        } else {
            ret = GmcExecute(stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (isBatch == 1) {
            ret = GmcBatchExecute(batch, &batchRet);
            if (ret) {
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                break;
            }
            if (isSecondTriggerExpansion) {
                if (i < 400 || i > 1500) {
                    GetTriggerExpansionOrshrinkValue(labelInfo);
                    if (initExpansionValue + 1 == labelInfo->expansionValue) {
                        i++;
                        break;
                    }
                }
            }
        }
    }
    if (isBatch == 2) {
        usleep(1000);
        ret = GmcBatchExecute(batch, &batchRet);
    }
    if (ret) {
        if (isBatch) {
            GmcBatchDestroy(batch);
            batch = NULL;
        }
        AW_FUN_Log(LOG_INFO, "【WriteDataUntilExpand】写数据直到扩容失败 ret is %d", ret);
        return ret;
    }
    if (!isOnceWrite) {
        SystemSnprintf("gmsysview count %s -ns %s", labelInfo->labelName, g_testNameSpace);
        isBatch = GetRandomMode(2);
        if (isBatch) {
            AW_FUN_Log(LOG_INFO, "【WriteDataUntilExpand】第二次写数据为批量单写");
        } else {
            AW_FUN_Log(LOG_INFO, "【WriteDataUntilExpand】第二次写数据为单写");
        }

        setPkProperty(stmt, i, 0, (i + 1) % 1024, i, 24);
        SetLpmProperty(stmt, i, 0, 1, i, 24);
        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcBatchExecute(batch, &batchRet);
        } else {
            ret = GmcExecute(stmt);
        }
        if (ret) {
            if (isBatch) {
                GmcBatchDestroy(batch);
                batch = NULL;
            }
            AW_FUN_Log(LOG_INFO, "【WriteDataUntilExpand】获取扩容数据量失败 ret is %d", ret);
            return ret;
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        i++;  // for循环外面不会++影响预期
    }
    // 查询视图获取页是否增加
    AW_FUN_Log(LOG_INFO, "【WriteDataUntilExpand】获取扩容数据量成功 触发扩容数据量是 %d", i);
    GetTriggerExpansionOrshrinkValue(labelInfo);
    AW_MACRO_EXPECT_EQ_INT(initExpansionValue + 1, labelInfo->expansionValue);
    labelInfo->expanSionDataNum = i;
    SystemSnprintf("gmsysview count %s -ns %s", labelInfo->labelName, g_testNameSpace);
    SystemSnprintf("gmsysview -q V\\$STORAGE_HASH_INDEX_STAT -f LABEL_NAME=%s", labelInfo->labelName);
    if (batch) {
        GmcBatchDestroy(batch);
        batch = NULL;
    }
    return ret;
}

// 写数据
int32_t WriteData(IndexCacheLabelInfoT *labelInfo, int32_t begin, int32_t end, GmcConnT *conn, GmcStmtT *stmt,
    FuncWritePkProperty setPkProperty, GmcOperationTypeE operationType = GMC_OPERATION_INSERT)
{
    int32_t ret = 1;
    int32_t isBatch = 0;
    bool isOnceWrite = false;
    // 获取随机模式
    isBatch = GetRandomMode(3);  // 单写，批量单写，批量多写
    isOnceWrite = GetRandomMode(2);
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {0};

    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据获取扩容值
    ret = testGmcPrepareStmtByLabelName(stmt, labelInfo->labelName, operationType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t i = 0;
    int32_t insertNum = end;
    if (!isOnceWrite) {
        SystemSnprintf("gmsysview count %s -ns %s", labelInfo->labelName, g_testNameSpace);
        AW_FUN_Log(LOG_INFO, "【WriteData】分两次写数据第二次触发扩容");
        insertNum -= 1;
        if (isBatch == 1) {
            AW_FUN_Log(LOG_INFO, "【WriteData】批量单写");
        }
    } else {
        AW_FUN_Log(LOG_INFO, "【WriteData】一次性写数据直到扩容");
    }
    if (isBatch) {
        AW_FUN_Log(LOG_INFO, "【WriteData】第一次为批写");
    } else {
        AW_FUN_Log(LOG_INFO, "【WriteData】第一次为单写");
    }
    for (i = begin; i < insertNum; i++) {
        setPkProperty(stmt, i, 0, (i + 1) % 1024, i, 24);
        SetLpmProperty(stmt, i, 0, 1, i, 24);
        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            if (ret) {
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                AW_FUN_Log(LOG_INFO, "【WriteData】GmcBatchAddDML no ok i is %d", i);
            }
            if (i % 1023 == 0 && i > 0 && (i + 1 != insertNum) && isBatch == 2) {
                ret = GmcBatchExecute(batch, &batchRet);
                if (ret) {
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    AW_FUN_Log(LOG_INFO, "【WriteData】GmcBatchExecute no ok i is %d", i);
                    break;
                }
            }
        } else {
            ret = GmcExecute(stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (isBatch == 1) {
            ret = GmcBatchExecute(batch, &batchRet);
            if (ret) {
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                break;
            }
        }
    }
    if (isBatch == 2) {
        usleep(1000);
        ret = GmcBatchExecute(batch, &batchRet);
    }
    if (ret) {
        if (isBatch) {
            GmcBatchDestroy(batch);
            batch = NULL;
        }
        AW_FUN_Log(LOG_INFO, "【WriteData】写数据直到扩容失败 ret is %d", ret);
        return ret;
    }
    if (!isOnceWrite) {
        SystemSnprintf("gmsysview count %s -ns %s", labelInfo->labelName, g_testNameSpace);
        isBatch = GetRandomMode(2);
        if (isBatch) {
            AW_FUN_Log(LOG_INFO, "【WriteData】第二次写数据为批量单写");
        } else {
            AW_FUN_Log(LOG_INFO, "【WriteData】第二次写数据为单写");
        }

        setPkProperty(stmt, i, 0, (i + 1) % 1024, i, 24);
        SetLpmProperty(stmt, i, 0, 1, i, 24);
        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcBatchExecute(batch, &batchRet);
        } else {
            ret = GmcExecute(stmt);
        }
        if (ret) {
            if (isBatch) {
                GmcBatchDestroy(batch);
                batch = NULL;
            }
            AW_FUN_Log(LOG_INFO, "【WriteData】获取扩容数据量失败 ret is %d", ret);
            return ret;
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        i++;  // for循环外面不会++影响预期
    }
    if (batch) {
        GmcBatchDestroy(batch);
        batch = NULL;
    }
    return ret;
}

// 删除数据直到缩容
int32_t DeleteDataUntilExpand(
    IndexCacheLabelInfoT *labelInfo, GmcConnT *conn, GmcStmtT *stmt, FuncWritePkValue setPkValue)
{
    int32_t ret = 1;
    int32_t isBatch = 0;
    bool isOnceWrite = false;
    // 获取随机模式
    isBatch = GetRandomMode(3);  // 单删，批量单删，批量多删
    isOnceWrite = GetRandomMode(2);

    // 判断是否是第二次触发缩容,二次缩容走固定批量多删
    bool isSecondTriggerExpansion = false;
    // 设置初始扩缩容值
    uint32_t initExpansionValue = labelInfo->expansionValue;
    uint32_t initShrinkValue = labelInfo->shrinkValue;
    if (initShrinkValue > 0) {
        isSecondTriggerExpansion = true;
        isOnceWrite = true;
        isBatch = 2;
    }

    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {0};
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除数据直到触发缩容
    ret = testGmcPrepareStmtByLabelName(stmt, labelInfo->labelName, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t i = 0;
    int32_t insertNum = labelInfo->shrinkValueDataNum;
    if (isSecondTriggerExpansion) {
        insertNum = labelInfo->expanSionDataNum - 2;
    }
    if (insertNum == 1) {
        isOnceWrite = 1;
    }
    if (!isOnceWrite) {
        SystemSnprintf("gmsysview count %s -ns %s", labelInfo->labelName, g_testNameSpace);
        AW_FUN_Log(LOG_INFO, "【DeleteDataUntilExpand】分两次删数据第二次触发缩容");
        insertNum -= 1;
        if (isBatch == 1) {
            AW_FUN_Log(LOG_INFO, "【DeleteDataUntilExpand】批量单删");
        }
    } else {
        AW_FUN_Log(LOG_INFO, "【DeleteDataUntilExpand】一次性删除数据直到缩容");
    }
    if (isBatch) {
        AW_FUN_Log(LOG_INFO, "【DeleteDataUntilExpand】第一次为批删");
    } else {
        AW_FUN_Log(LOG_INFO, "【DeleteDataUntilExpand】第一次为单删");
    }
    for (i = 0; i < insertNum; i++) {
        setPkValue(stmt, i, 0, (i + 1) % 1024, i, 24);
        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            if (ret != GMERR_OK) {
                AW_FUN_Log(LOG_INFO, "【DeleteDataUntilExpand】GmcBatchAddDML no ok is %d", i);
                break;
            }
            if (i % 1023 == 0 && i > 0 && (i + 1 != insertNum) && isBatch == 2) {
                ret = GmcBatchExecute(batch, &batchRet);
                if (ret) {
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    break;
                }
            }
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcExecute(stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (isBatch == 1) {
            ret = GmcBatchExecute(batch, &batchRet);
        }
    }
    if (isBatch == 2) {
        ret = GmcBatchExecute(batch, &batchRet);
    }
    if (ret) {
        if (isBatch) {
            GmcBatchDestroy(batch);
            batch = NULL;
        }
        AW_FUN_Log(LOG_INFO, "【DeleteDataUntilExpand】删除数据直到缩容失败 ret is %d", ret);
        return ret;
    }
    if (!isOnceWrite) {
        SystemSnprintf("gmsysview count %s -ns %s", labelInfo->labelName, g_testNameSpace);
        isBatch = GetRandomMode(2);
        if (isBatch) {
            AW_FUN_Log(LOG_INFO, "【DeleteDataUntilExpand】第二次删除数据为批量单删");
        } else {
            AW_FUN_Log(LOG_INFO, "【DeleteDataUntilExpand】第二次删除数据为单删");
        }
        setPkValue(stmt, i, 0, (i + 1) % 1024, i, 24);
        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcBatchExecute(batch, &batchRet);
        } else {
            ret = GmcExecute(stmt);
        }
        if (ret) {
            if (isBatch) {
                GmcBatchDestroy(batch);
                batch = NULL;
            }
            AW_FUN_Log(LOG_INFO, "【DeleteDataUntilExpand】触发缩容失败 ret is %d", ret);
            return ret;
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        i++;  // for循环外面不会++影响预期
    }
    // 查询视图获取页是否增加
    AW_FUN_Log(LOG_INFO, "【DeleteDataUntilExpand】校验删除后缩容信息i is %d", i);
    GetTriggerExpansionOrshrinkValue(labelInfo);
    EXPECT_LE(initShrinkValue + 1, labelInfo->shrinkValue);
    labelInfo->shrinkValueDataNum = i;
    SystemSnprintf("gmsysview count %s -ns %s", labelInfo->labelName, g_testNameSpace);
    SystemSnprintf("gmsysview -q V\\$STORAGE_HASH_INDEX_STAT -f LABEL_NAME=%s", labelInfo->labelName);
    if (batch) {
        GmcBatchDestroy(batch);
        batch = NULL;
    }
    return ret;
}

// 删除数据直到缩容
int32_t DeleteDataUntilExpandLocal(
    char *labelName, int32_t recordNum, GmcConnT *conn, GmcStmtT *stmt, FuncWritePkValue setPkValue)
{
    int32_t ret = 1;
    int32_t isBatch = 0;
    bool isOnceWrite = false;
    // 获取随机模式
    isBatch = GetRandomMode(3);  // 单删，批量单删，批量多删
    isOnceWrite = GetRandomMode(2);

    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {0};
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t i = 0;
    int32_t insertNum = recordNum;
    if (insertNum == 1) {
        isOnceWrite = 1;
    }
    if (!isOnceWrite) {
        SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);
        AW_FUN_Log(LOG_INFO, "【DeleteDataUntilExpandLocal】分两次删数据第二次触发缩容");
        insertNum -= 1;
        if (isBatch == 1) {
            AW_FUN_Log(LOG_INFO, "【DeleteDataUntilExpandLocal】批量单删");
        }
    } else {
        AW_FUN_Log(LOG_INFO, "【DeleteDataUntilExpandLocal】一次性删除数据直到缩容");
    }
    if (isBatch) {
        AW_FUN_Log(LOG_INFO, "【DeleteDataUntilExpandLocal】第一次为批删");
    } else {
        AW_FUN_Log(LOG_INFO, "【DeleteDataUntilExpandLocal】第一次为单删");
    }
    for (i = 0; i < insertNum; i++) {
        setPkValue(stmt, i, 0, (i + 1) % 1024, i, 24);
        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            if (ret != GMERR_OK) {
                AW_FUN_Log(LOG_INFO, "【DeleteDataUntilExpandLocal】GmcBatchAddDML no ok is %d", i);
                break;
            }
            if (i % 1023 == 0 && i > 0 && (i + 1 != insertNum) && isBatch == 2) {
                ret = GmcBatchExecute(batch, &batchRet);
                if (ret) {
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    break;
                }
            }
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcExecute(stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (isBatch == 1) {
            ret = GmcBatchExecute(batch, &batchRet);
        }
    }
    if (isBatch == 2) {
        ret = GmcBatchExecute(batch, &batchRet);
    }
    if (ret) {
        if (isBatch) {
            GmcBatchDestroy(batch);
            batch = NULL;
        }
        AW_FUN_Log(LOG_INFO, "【DeleteDataUntilExpand】删除数据直到缩容失败 ret is %d", ret);
        return ret;
    }
    if (!isOnceWrite) {
        SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);
        isBatch = GetRandomMode(2);
        if (isBatch) {
            AW_FUN_Log(LOG_INFO, "【DeleteDataUntilExpand】第二次删除数据为批量单删");
        } else {
            AW_FUN_Log(LOG_INFO, "【DeleteDataUntilExpand】第二次删除数据为单删");
        }
        setPkValue(stmt, i, 0, (i + 1) % 1024, i, 24);
        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcBatchExecute(batch, &batchRet);
        } else {
            ret = GmcExecute(stmt);
        }
        if (ret) {
            if (isBatch) {
                GmcBatchDestroy(batch);
                batch = NULL;
            }
            AW_FUN_Log(LOG_INFO, "【DeleteDataUntilExpand】触发缩容失败 ret is %d", ret);
            return ret;
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        i++;  // for循环外面不会++影响预期
    }
    // 查询视图获取页是否增加
    AW_FUN_Log(LOG_INFO, "【DeleteDataUntilExpand】校验删除后缩容信息i is %d", i);
    SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);
    SystemSnprintf("gmsysview -q V\\$STORAGE_HASH_INDEX_STAT -f LABEL_NAME=%s", labelName);
    if (batch) {
        GmcBatchDestroy(batch);
        batch = NULL;
    }
    return ret;
}

// 删除数据直到缩容
int32_t DeleteDataLpm(char *labelName, int32_t recordNum, GmcConnT *conn, GmcStmtT *stmt, FuncWritePkValue setPkValue)
{
    int32_t ret = 1;
    int32_t isBatch = 0;
    bool isOnceWrite = false;
    // 获取随机模式
    isBatch = GetRandomMode(3);  // 单删，批量单删，批量多删
    isOnceWrite = GetRandomMode(2);

    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {0};
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t i = 0;
    int32_t insertNum = recordNum;
    if (insertNum == 1) {
        isOnceWrite = 1;
    }
    if (!isOnceWrite) {
        SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);
        AW_FUN_Log(LOG_INFO, "【DeleteDataLpm】分两次删数据");
        insertNum -= 1;
        if (isBatch == 1) {
            AW_FUN_Log(LOG_INFO, "【DeleteDataLpm】批量单删");
        }
    } else {
        AW_FUN_Log(LOG_INFO, "【DeleteDataLpm】一次性删除数据");
    }
    if (isBatch) {
        AW_FUN_Log(LOG_INFO, "【DeleteDataLpm】第一次为批删");
    } else {
        AW_FUN_Log(LOG_INFO, "【DeleteDataLpm】第一次为单删");
    }
    for (i = 0; i < insertNum; i++) {
        setPkValue(stmt, i, 0, (i + 1) % 1024, i, 32);
        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            if (ret != GMERR_OK) {
                AW_FUN_Log(LOG_INFO, "【DeleteDataLpm】GmcBatchAddDML no ok is %d", i);
                break;
            }
            if (i % 1023 == 0 && i > 0 && (i + 1 != insertNum) && isBatch == 2) {
                ret = GmcBatchExecute(batch, &batchRet);
                if (ret) {
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    break;
                }
            }
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcExecute(stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (isBatch == 1) {
            ret = GmcBatchExecute(batch, &batchRet);
        }
    }
    if (isBatch == 2) {
        ret = GmcBatchExecute(batch, &batchRet);
    }
    if (ret) {
        if (isBatch) {
            GmcBatchDestroy(batch);
            batch = NULL;
        }
        AW_FUN_Log(LOG_INFO, "【DeleteDataLpm】删除数据直到缩容失败 ret is %d", ret);
        return ret;
    }
    if (!isOnceWrite) {
        SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);
        isBatch = GetRandomMode(2);
        if (isBatch) {
            AW_FUN_Log(LOG_INFO, "【DeleteDataLpm】第二次删除数据为批量单删");
        } else {
            AW_FUN_Log(LOG_INFO, "【DeleteDataLpm】第二次删除数据为单删");
        }
        setPkValue(stmt, i, 0, (i + 1) % 1024, i, 32);
        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcBatchExecute(batch, &batchRet);
        } else {
            ret = GmcExecute(stmt);
        }
        if (ret) {
            if (isBatch) {
                GmcBatchDestroy(batch);
                batch = NULL;
            }
            AW_FUN_Log(LOG_INFO, "【DeleteDataLpm】触发缩容失败 ret is %d", ret);
            return ret;
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        i++;  // for循环外面不会++影响预期
    }
    // 查询视图获取页是否增加
    AW_FUN_Log(LOG_INFO, "【DeleteDataLpm】校验删除后缩容信息i is %d", i);
    SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);
    SystemSnprintf("gmsysview -q V\\$STORAGE_HASH_INDEX_STAT -f LABEL_NAME=%s", labelName);
    if (batch) {
        GmcBatchDestroy(batch);
        batch = NULL;
    }
    return ret;
}

int32_t DeleteDataLpmv6(char *labelName, int32_t recordNum, GmcConnT *conn, GmcStmtT *stmt, FuncWritePkValue setPkValue)
{
    int32_t ret = 1;
    int32_t isBatch = 0;
    bool isOnceWrite = false;
    // 获取随机模式
    isBatch = GetRandomMode(3);  // 单删，批量单删，批量多删
    isOnceWrite = GetRandomMode(2);

    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {0};

    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t i = 0;
    int32_t insertNum = recordNum;
    if (insertNum == 1) {
        isOnceWrite = 1;
    }
    if (!isOnceWrite) {
        SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);
        AW_FUN_Log(LOG_INFO, "【DeleteDataLpmv6】分两次删数据");
        insertNum -= 1;
        if (isBatch == 1) {
            AW_FUN_Log(LOG_INFO, "【DeleteDataLpmv6】批量单删");
        }
    } else {
        AW_FUN_Log(LOG_INFO, "【DeleteDataLpmv6】一次性删除数据");
    }
    if (isBatch) {
        AW_FUN_Log(LOG_INFO, "【DeleteDataLpmv6】第一次为批删");
    } else {
        AW_FUN_Log(LOG_INFO, "【DeleteDataLpmv6】第一次为单删");
    }
    for (i = 0; i < insertNum; i++) {
        setPkValue(stmt, i, 0, (i + 1) % 1024, i, 128);
        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            if (ret != GMERR_OK) {
                AW_FUN_Log(LOG_INFO, "【DeleteDataLpmv6】GmcBatchAddDML no ok is %d", i);
                break;
            }
            if (i % 1023 == 0 && i > 0 && (i + 1 != insertNum) && isBatch == 2) {
                ret = GmcBatchExecute(batch, &batchRet);
                if (ret) {
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    break;
                }
            }
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcExecute(stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (isBatch == 1) {
            ret = GmcBatchExecute(batch, &batchRet);
        }
    }
    if (isBatch == 2) {
        ret = GmcBatchExecute(batch, &batchRet);
    }
    if (ret) {
        if (isBatch) {
            GmcBatchDestroy(batch);
            batch = NULL;
        }
        AW_FUN_Log(LOG_INFO, "【DeleteDataLpmv6】删除数据失败 ret is %d", ret);
        return ret;
    }
    if (!isOnceWrite) {
        SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);
        isBatch = GetRandomMode(2);
        if (isBatch) {
            AW_FUN_Log(LOG_INFO, "【DeleteDataLpmv6】第二次删除数据为批量单删");
        } else {
            AW_FUN_Log(LOG_INFO, "【DeleteDataLpmv6】第二次删除数据为单删");
        }
        setPkValue(stmt, i, 0, (i + 1) % 1024, i, 128);
        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcBatchExecute(batch, &batchRet);
        } else {
            ret = GmcExecute(stmt);
        }
        if (ret) {
            if (isBatch) {
                GmcBatchDestroy(batch);
                batch = NULL;
            }
            AW_FUN_Log(LOG_INFO, "【DeleteDataLpmv6】触发缩容失败 ret is %d", ret);
            return ret;
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        i++;  // for循环外面不会++影响预期
    }
    // 查询视图获取页是否增加
    AW_FUN_Log(LOG_INFO, "【DeleteDataLpmv6】校验删除后缩容信息i is %d", i);
    SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);
    SystemSnprintf("gmsysview -q V\\$STORAGE_HASH_INDEX_STAT -f LABEL_NAME=%s", labelName);
    if (batch) {
        GmcBatchDestroy(batch);
        batch = NULL;
    }
    return ret;
}

// 删除数据直到缩容
int32_t DeleteDataUntilExpandLpm(
    char *labelName, int32_t recordNum, GmcConnT *conn, GmcStmtT *stmt, FuncWritePkValue setPkValue)
{
    int32_t ret = 1;
    int32_t isBatch = 0;
    bool isOnceWrite = false;
    // 获取随机模式
    isBatch = GetRandomMode(3);  // 单删，批量单删，批量多删
    isOnceWrite = GetRandomMode(2);

    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {0};
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t i = 0;
    int32_t insertNum = recordNum;
    if (insertNum == 1) {
        isOnceWrite = 1;
    }
    if (!isOnceWrite) {
        SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);
        AW_FUN_Log(LOG_INFO, "【DeleteDataUntilExpandLocal】分两次删数据第二次触发缩容");
        insertNum -= 1;
        if (isBatch == 1) {
            AW_FUN_Log(LOG_INFO, "【DeleteDataUntilExpandLocal】批量单删");
        }
    } else {
        AW_FUN_Log(LOG_INFO, "【DeleteDataUntilExpandLocal】一次性删除数据直到缩容");
    }
    if (isBatch) {
        AW_FUN_Log(LOG_INFO, "【DeleteDataUntilExpandLocal】第一次为批删");
    } else {
        AW_FUN_Log(LOG_INFO, "【DeleteDataUntilExpandLocal】第一次为单删");
    }
    for (i = 0; i < insertNum; i++) {
        SetLpmV4PkIndexValue(stmt, i, 0, (i + 1) % 1024, i, 24);
        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            if (ret != GMERR_OK) {
                AW_FUN_Log(LOG_INFO, "【DeleteDataUntilExpandLocal】GmcBatchAddDML no ok is %d", i);
                break;
            }
            if (i % 1023 == 0 && i > 0 && (i + 1 != insertNum) && isBatch == 2) {
                ret = GmcBatchExecute(batch, &batchRet);
                if (ret) {
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    break;
                }
            }
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcExecute(stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (isBatch == 1) {
            ret = GmcBatchExecute(batch, &batchRet);
        }
    }
    if (isBatch == 2) {
        ret = GmcBatchExecute(batch, &batchRet);
    }
    if (ret) {
        if (isBatch) {
            GmcBatchDestroy(batch);
            batch = NULL;
        }
        AW_FUN_Log(LOG_INFO, "【DeleteDataUntilExpand】删除数据直到缩容失败 ret is %d", ret);
        return ret;
    }
    if (!isOnceWrite) {
        SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);
        isBatch = GetRandomMode(2);
        if (isBatch) {
            AW_FUN_Log(LOG_INFO, "【DeleteDataUntilExpand】第二次删除数据为批量单删");
        } else {
            AW_FUN_Log(LOG_INFO, "【DeleteDataUntilExpand】第二次删除数据为单删");
        }
        SetLpmV4PkIndexValue(stmt, i, 0, (i + 1) % 1024, i, 24);
        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcBatchExecute(batch, &batchRet);
        } else {
            ret = GmcExecute(stmt);
        }
        if (ret) {
            if (isBatch) {
                GmcBatchDestroy(batch);
                batch = NULL;
            }
            AW_FUN_Log(LOG_INFO, "【DeleteDataUntilExpand】触发缩容失败 ret is %d", ret);
            return ret;
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        i++;  // for循环外面不会++影响预期
    }
    // 查询视图获取页是否增加
    AW_FUN_Log(LOG_INFO, "【DeleteDataUntilExpand】校验删除后缩容信息i is %d", i);
    SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);
    SystemSnprintf("gmsysview -q V\\$STORAGE_HASH_INDEX_STAT -f LABEL_NAME=%s", labelName);
    if (batch) {
        GmcBatchDestroy(batch);
        batch = NULL;
    }
    return ret;
}

// 删除数据直到缩容
int32_t DeleteDataUntilExpandLpm6(
    char *labelName, int32_t recordNum, GmcConnT *conn, GmcStmtT *stmt, FuncWritePkValue setPkValue)
{
    int32_t ret = 1;
    int32_t isBatch = 0;
    bool isOnceWrite = false;
    // 获取随机模式
    isBatch = GetRandomMode(3);  // 单删，批量单删，批量多删
    isOnceWrite = GetRandomMode(2);

    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {0};

    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t i = 0;
    int32_t insertNum = recordNum;
    if (insertNum == 1) {
        isOnceWrite = 1;
    }
    if (!isOnceWrite) {
        SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);
        AW_FUN_Log(LOG_INFO, "【DeleteDataUntilExpandLocal】分两次删数据第二次触发缩容");
        insertNum -= 1;
        if (isBatch == 1) {
            AW_FUN_Log(LOG_INFO, "【DeleteDataUntilExpandLocal】批量单删");
        }
    } else {
        AW_FUN_Log(LOG_INFO, "【DeleteDataUntilExpandLocal】一次性删除数据直到缩容");
    }
    if (isBatch) {
        AW_FUN_Log(LOG_INFO, "【DeleteDataUntilExpandLocal】第一次为批删");
    } else {
        AW_FUN_Log(LOG_INFO, "【DeleteDataUntilExpandLocal】第一次为单删");
    }
    for (i = 0; i < insertNum; i++) {
        SetLpmV6PkIndexValue(stmt, i, 0, (i + 1) % 1024, i, 24);
        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            if (ret != GMERR_OK) {
                AW_FUN_Log(LOG_INFO, "【DeleteDataUntilExpandLocal】GmcBatchAddDML no ok is %d", i);
                break;
            }
            if (i % 1023 == 0 && i > 0 && (i + 1 != insertNum) && isBatch == 2) {
                ret = GmcBatchExecute(batch, &batchRet);
                if (ret) {
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    break;
                }
            }
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcExecute(stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (isBatch == 1) {
            ret = GmcBatchExecute(batch, &batchRet);
        }
    }
    if (isBatch == 2) {
        ret = GmcBatchExecute(batch, &batchRet);
    }
    if (ret) {
        if (isBatch) {
            GmcBatchDestroy(batch);
            batch = NULL;
        }
        AW_FUN_Log(LOG_INFO, "【DeleteDataUntilExpand】删除数据直到缩容失败 ret is %d", ret);
        return ret;
    }
    if (!isOnceWrite) {
        SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);
        isBatch = GetRandomMode(2);
        if (isBatch) {
            AW_FUN_Log(LOG_INFO, "【DeleteDataUntilExpand】第二次删除数据为批量单删");
        } else {
            AW_FUN_Log(LOG_INFO, "【DeleteDataUntilExpand】第二次删除数据为单删");
        }
        SetLpmV6PkIndexValue(stmt, i, 0, (i + 1) % 1024, i, 24);
        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcBatchExecute(batch, &batchRet);
        } else {
            ret = GmcExecute(stmt);
        }
        if (ret) {
            if (isBatch) {
                GmcBatchDestroy(batch);
                batch = NULL;
            }
            AW_FUN_Log(LOG_INFO, "【DeleteDataUntilExpand】触发缩容失败 ret is %d", ret);
            return ret;
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        i++;  // for循环外面不会++影响预期
    }
    // 查询视图获取页是否增加
    AW_FUN_Log(LOG_INFO, "【DeleteDataUntilExpand】校验删除后缩容信息i is %d", i);
    SystemSnprintf("gmsysview count %s -ns %s", labelName, g_testNameSpace);
    SystemSnprintf("gmsysview -q V\\$STORAGE_HASH_INDEX_STAT -f LABEL_NAME=%s", labelName);
    if (batch) {
        GmcBatchDestroy(batch);
        batch = NULL;
    }
    return ret;
}
// 使用前台truncate删除数据
int32_t FrontTruncateDeleteData(
    IndexCacheLabelInfoT *labelInfo, GmcConnT *conn, GmcStmtT *stmt, bool isUseAsync, bool isPkCceh = true)
{
    int32_t ret = GMERR_OK;
    sleep(60);  // 等待数据回收
    AsyncUserDataT data = {0};
    // 设置初始扩缩容值
    uint32_t initExpansionValue = labelInfo->expansionValue;
    uint32_t initShrinkValue = labelInfo->shrinkValue;
    // 使用异步truncate
    if (isUseAsync) {
        ret = GmcTruncateVertexLabelAsync(stmt, labelInfo->labelName, truncate_vertex_label_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    } else {
        // 使用同步truncate
        ret = GmcTruncateVertexLabel(stmt, labelInfo->labelName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    if (isPkCceh) {
        GetTriggerExpansionOrshrinkValue(labelInfo);
        AW_MACRO_EXPECT_EQ_INT(initShrinkValue, labelInfo->shrinkValue);
        SystemSnprintf("gmsysview -q V\\$STORAGE_HASH_INDEX_STAT -f LABEL_NAME=%s", labelInfo->labelName);
    }
    SystemSnprintf("gmsysview count %s -ns %s", labelInfo->labelName, g_testNameSpace);
    return ret;
}
// 设置local key字段
void SetLocalKeyProperty(
    GmcStmtT *stmt, int32_t loop, uint32_t vrId, uint32_t vrfIndex, uint32_t destIpAddr, uint8_t maskLen)
{
    int32_t ret = 1;
    int32_t i = vrfIndex;
    char ipAddrTmp[16];
    (void)sprintf(ipAddrTmp, "192.%d.%d.0", loop % 168, loop % 128);
    uint32_t transVal = TransIp(ipAddrTmp);
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &vrId, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_UINT32, &transVal, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 设置hashcluster key字段
void SetHashclusterKeyProperty(
    GmcStmtT *stmt, int32_t loop, uint32_t vrId, uint32_t vrfIndex, uint32_t destIpAddr, uint8_t maskLen)
{
    int32_t ret = 1;
    int32_t i = vrfIndex;
    char ipAddrTmp[16];
    (void)sprintf(ipAddrTmp, "192.%d.%d.0", loop % 168, loop % 128);
    uint32_t transVal = TransIp(ipAddrTmp);
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &vrId, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_UINT32, &transVal, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 批量插入
int32_t BatchInsertLocalTable(GmcConnT *conn, GmcStmtT *stmt, const char *vtxLabelName, uint32_t batchNum,
    GmcOperationTypeE operationType = GMC_OPERATION_INSERT, int32_t offset = 0,
    FuncWritePkProperty setPkProperty = SetLocalKeyProperty, uint32_t batchOrder = GMC_BATCH_ORDER_SEMI)
{
    int32_t ret = 1;
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {0};
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, batchOrder);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (operationType == GMC_OPERATION_MERGE) {
        setPkProperty = SetPkIndexValue;
    }

    ret = testGmcPrepareStmtByLabelName(stmt, vtxLabelName, operationType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = offset; i < batchNum; i++) {
        setPkProperty(stmt, i, 0, (i + 1) % 1024, i, 24);
        SetLocalProperty(stmt, i, 0, 1, i, 24);
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (ret != GMERR_OK) {
            GmcBatchDestroy(batch);
            batch = NULL;
            return ret;
        }
    }
    ret = GmcBatchExecute(batch, &batchRet);
    if (ret != GMERR_OK) {
        GmcBatchDestroy(batch);
        batch = NULL;
        return ret;
    }

    GmcBatchDestroy(batch);
    batch = NULL;
    return ret;
}

// 批量插入
int32_t BatchInsertHashclusterTable(GmcConnT *conn, GmcStmtT *stmt, const char *vtxLabelName, uint32_t batchNum,
    GmcOperationTypeE operationType = GMC_OPERATION_INSERT, int32_t offset = 0,
    FuncWritePkProperty setPkProperty = SetHashclusterKeyProperty, uint32_t batchOrder = GMC_BATCH_ORDER_SEMI)
{
    int32_t ret = 1;
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {0};
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, batchOrder);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (operationType == GMC_OPERATION_MERGE) {
        setPkProperty = SetPkIndexValue;
    }

    ret = testGmcPrepareStmtByLabelName(stmt, vtxLabelName, operationType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = offset; i < batchNum; i++) {
        setPkProperty(stmt, i, 0, (i + 1) % 1024, i, 24);
        SetHashclusterProperty(stmt, i, 0, 1, i, 24);
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (ret != GMERR_OK) {
            GmcBatchDestroy(batch);
            batch = NULL;
            return ret;
        }
    }
    ret = GmcBatchExecute(batch, &batchRet);
    if (ret != GMERR_OK) {
        GmcBatchDestroy(batch);
        batch = NULL;
        return ret;
    }

    GmcBatchDestroy(batch);
    batch = NULL;
    return ret;
}

// 批量插入
int32_t BatchInsertLpmV4(GmcConnT *conn, GmcStmtT *stmt, const char *vtxLabelName, uint32_t batchNum,
    GmcOperationTypeE operationType = GMC_OPERATION_INSERT, int32_t offset = 0,
    FuncWritePkProperty setPkProperty = SetLpmV4PkProperty, uint32_t batchOrder = GMC_BATCH_ORDER_SEMI)
{
    int32_t ret = 1;
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {0};
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, batchOrder);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (operationType == GMC_OPERATION_MERGE) {
        setPkProperty = SetLpmV4PkIndexValue;
    }

    ret = testGmcPrepareStmtByLabelName(stmt, vtxLabelName, operationType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = offset; i < batchNum; i++) {
        setPkProperty(stmt, i, 0, (i + 1) % 1024, i, 32);
        SetLpmProperty(stmt, i, 0, 1, i, 24);
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (ret != GMERR_OK) {
            GmcBatchDestroy(batch);
            batch = NULL;
            return ret;
        }
    }
    ret = GmcBatchExecute(batch, &batchRet);
    if (ret != GMERR_OK) {
        GmcBatchDestroy(batch);
        batch = NULL;
        return ret;
    }

    GmcBatchDestroy(batch);
    batch = NULL;
    return ret;
}

// 批量插入
int32_t BatchInsertLpmV6(GmcConnT *conn, GmcStmtT *stmt, const char *vtxLabelName, uint32_t batchNum,
    GmcOperationTypeE operationType = GMC_OPERATION_INSERT, int32_t offset = 0,
    FuncWritePkProperty setPkProperty = SetLpmV6PkProperty, uint32_t batchOrder = GMC_BATCH_ORDER_SEMI)
{
    int32_t ret = 1;
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {0};
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, batchOrder);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (operationType == GMC_OPERATION_MERGE) {
        setPkProperty = SetLpmV6PkIndexValue;
    }

    ret = testGmcPrepareStmtByLabelName(stmt, vtxLabelName, operationType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = offset; i < batchNum; i++) {
        setPkProperty(stmt, i, 0, (i + 1) % 1024, i, 128);
        SetLpmProperty(stmt, i, 0, 1, i, 24);
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (ret != GMERR_OK) {
            GmcBatchDestroy(batch);
            batch = NULL;
            return ret;
        }
    }
    ret = GmcBatchExecute(batch, &batchRet);
    if (ret != GMERR_OK) {
        GmcBatchDestroy(batch);
        batch = NULL;
        return ret;
    }

    GmcBatchDestroy(batch);
    batch = NULL;
    return ret;
}
