[{"type": "record", "name": "Person_N", "fields": [{"name": "ID", "type": "uint32"}, {"name": "P_Name", "type": "string", "size": 10}, {"name": "Sex", "type": "uint32", "size": 2}, {"name": "Age", "type": "uint32", "size": 3}, {"name": "Country", "type": "string", "size": 15}], "keys": [{"node": "Person_N", "name": "Person_N_PK", "fields": ["ID"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"name": "Person_N_ART", "node": "Person_N", "fields": ["Age"], "index": {"type": "hashcluster"}, "constraints": {"unique": false}}]}]