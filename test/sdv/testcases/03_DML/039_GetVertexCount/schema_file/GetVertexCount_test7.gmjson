[{"type": "record", "name": "Person_T7", "fields": [{"name": "ID", "type": "int32"}, {"name": "P_Name", "type": "string", "size": 10}, {"name": "Sex", "type": "int16", "size": 2}, {"name": "Age", "type": "int16", "size": 3}, {"name": "Country", "type": "string", "size": 15}], "keys": [{"node": "Person_T7", "name": "Person_T7_PK", "fields": ["ID"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "Person_T7", "name": "Person_T7_SI", "fields": ["Age"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": false}}, {"node": "Person_T7", "name": "Person_T7_SI2", "fields": ["Sex"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": false}}]}]