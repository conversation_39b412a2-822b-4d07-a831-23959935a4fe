#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <stdint.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"

/*****************************************************************************
 Description  : 全局Kv表并发测试(异步连接)
 Notes        :
 History      :
 Author       : qinqianbao qwx995465/ madongdong m00502160
 Modification :
*****************************************************************************/
GmcStmtT *g_stmt_async = NULL;
GmcConnT *g_conn_async = NULL;
int ret = 0;
char kvTableName[] = "T_GMDB";
const char *cfgJson = R"({"max_record_count":100})";

class KvMulThrAsync : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        // 重启server
        system("sh $TEST_HOME/tools/start.sh ");
        // 创建epoll thread
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void KvMulThrAsync::SetUp()
{
    printf("[INFO] KvMulThrAsync Start.\n");
    // 创建异步连接
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void KvMulThrAsync::TearDown()
{
    AW_CHECK_LOG_END();
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    printf("[INFO] KvMulThrAsync End.\n");
}

char kvTableName1[] = "T_GMDB";
void *thread_create_kv(void *arg)
{
    char errorMsg5[128] = {};
    (void)snprintf(errorMsg5, sizeof(errorMsg5), "GMERR-%d", GMERR_DUPLICATE_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg5);

    AsyncUserDataT tdata = {0};
    GmcStmtT *stmt_async = NULL;
    GmcConnT *conn_async = NULL;
    int ret = testGmcConnect(&conn_async, &stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvCreateTableAsync(stmt_async, kvTableName1, cfgJson, create_kv_table_callback, &tdata);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&tdata);
    EXPECT_EQ(GMERR_OK, ret);
    if (tdata.status != 0) {
        EXPECT_EQ(GMERR_DUPLICATE_TABLE, tdata.status);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        printf(" student1 false\n");
    } else {
        EXPECT_EQ(GMERR_OK, tdata.status);
        printf("student1 true\n");
        //删除kvtable
        ret = GmcKvDropTableAsync(stmt_async, kvTableName1, drop_kv_table_callback, &tdata);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&tdata);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, tdata.status);
    }
    ret = testGmcDisconnect(conn_async, stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

// 01. 调用接口并发创建同一张kv表：并发创建两个同名表
TEST_F(KvMulThrAsync, DML_040_003_KvMulThrAsync_001)
{
    // multi threads creat drop label
    int tdNum = 2;
    int err = 0;
    pthread_t kvTabel[tdNum];
    for (int i = 0; i < tdNum; i++) {
        err = pthread_create(&kvTabel[i], NULL, thread_create_kv, NULL);
        EXPECT_EQ(GMERR_OK, err);
    }
    for (int i = 0; i < tdNum; i++) {
        pthread_join(kvTabel[i], NULL);
    }
}

void *thread_set_data(void *arg)
{
    AsyncUserDataT tdata = {0};
    GmcStmtT *stmt_async = NULL;
    GmcConnT *conn_async = NULL;
    int ret = testGmcConnect(&conn_async, &stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    void *kvtable = NULL;
    ret = GmcKvPrepareStmtByLabelName(stmt_async, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    GmcKvTupleT kvInfo = {0};
    // data 1 set
    char key1[] = "zhangsan1";
    int32_t value = 10;
    kvInfo.key = key1;
    kvInfo.keyLen = strlen(key1);
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(int32_t);
    ret = GmcKvSetAsync(stmt_async, &kvInfo, set_kv_callback, &tdata);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&tdata);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, tdata.status);
    // data 2 set
    char key2[] = "zhangsan2";
    int32_t value2 = 20;
    kvInfo.key = key2;
    kvInfo.keyLen = strlen(key2);
    kvInfo.value = &value2;
    kvInfo.valueLen = sizeof(int32_t);
    ret = GmcKvSetAsync(stmt_async, &kvInfo, set_kv_callback, &tdata);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&tdata);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, tdata.status);
    // data 3 set
    char key3[] = "zhangsan3";
    int32_t value3 = 30;
    kvInfo.key = key3;
    kvInfo.keyLen = strlen(key3);
    kvInfo.value = &value3;
    kvInfo.valueLen = sizeof(int32_t);
    // set data
    ret = GmcKvSetAsync(stmt_async, &kvInfo, set_kv_callback, &tdata);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&tdata);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, tdata.status);
    // ret = GmcCloseKvTable(stmt_async);
    // EXPECT_EQ(ret, GMERR_OK);
    ret = testGmcDisconnect(conn_async, stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

// 02. 异步并发set数据  挂单：DTS2021040707M7ERP0F00
TEST_F(KvMulThrAsync, DML_040_003_KvMulThrAsync_002)
{
    AsyncUserDataT data = {0};
    int tdNum = 2;
    int err = 0;
    void *kvtable = NULL;
    pthread_t kvTabel[tdNum];
    for (int i = 0; i < tdNum; i++) {
        err = pthread_create(&kvTabel[i], NULL, thread_set_data, (void *)g_stmt_async);
        EXPECT_EQ(GMERR_OK, err);
    }
    for (int i = 0; i < tdNum; i++) {
        pthread_join(kvTabel[i], NULL);
    }
    // truncate kv 表
    ret = GmcKvTruncateTableAsync(g_stmt_async, kvTableName, truncate_kv_table_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
}
#if 0
//
void *thread_get_data(void *arg)
{
    AsyncUserDataT tdata = {0};
    GmcStmtT *stmt_async = NULL;
    GmcConnT *conn_async = NULL;
    int ret = testGmcConnect(&conn_async, &stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret); 
    void *kvtable = NULL;
    char *key = (char *)arg;
    ret = GmcKvPrepareStmtByLabelName(stmt_async, kvTableName);
    EXPECT_EQ(GMERR_OK, ret);    
    if(strncmp(key, "zhangsan", strlen(key)) == 0) { // key1 data get 
        uint32_t tmpValue = 0;
        tdata.kvValue = &tmpValue;
        tdata.kvValueSize = sizeof(uint32_t);
        ret = GmcKvGetAsync(stmt_async, kvtable, key, strlen(key), get_kv_callback, &tdata);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&tdata);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, tdata.status);
        EXPECT_EQ(30, *(int *)tdata.kvValue);
        EXPECT_EQ(4, tdata.kvValueSize);
    }
    //ret = GmcCloseKvTable(stmt_async);
    //EXPECT_EQ(ret, GMERR_OK);
    printf("get data\n");
    ret = testGmcDisconnect(conn_async, stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
}
//GmcIsKvExistAsync GmcKvTableRecordCountAsync GmcKvGetAsync 接口用例下架
// 03. 异步并发get数据  
TEST_F(KvMulThrAsync, DML_040_003_KvMulThrAsync_003)
{
    AsyncUserDataT data = {0};
    int tdNum = 2;
    int err = 0;
    void *kvtable = NULL;

    ret = GmcKvPrepareStmtByLabelName(g_stmt_async, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    GmcKvTupleT kvInfo = { 0 };
    char key[] = "zhangsan";
    int32_t value = 30;
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key);
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(int32_t);
    ret = GmcKvSetAsync(g_stmt_async, &kvInfo, set_kv_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    //ret = GmcCloseKvTable(g_stmt_async);
    //EXPECT_EQ(ret, GMERR_OK);

    pthread_t kvTabel[tdNum];
    for(int i = 0; i < tdNum; i++) {
        err = pthread_create(&kvTabel[i], NULL, thread_get_data, (void *)key);
        EXPECT_EQ(GMERR_OK, err);
    }  
    for(int i = 0; i < tdNum; i++) {
        pthread_join(kvTabel[i], NULL);
    }

    //truncate kv 表
    ret = GmcKvTruncateTableAsync(g_stmt_async, kvTableName, truncate_kv_table_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
}
#endif
void *thread_delete_data(void *arg)
{
    AsyncUserDataT tdata = {0};
    GmcStmtT *stmt_async = NULL;
    GmcConnT *conn_async = NULL;
    int ret = testGmcConnect(&conn_async, &stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    void *kvtable = NULL;
    char *key = (char *)arg;
    ret = GmcKvPrepareStmtByLabelName(stmt_async, kvTableName);
    EXPECT_EQ(GMERR_OK, ret);
    if (strncmp(key, "zhangsan1", strlen(key)) == 0) {  // key1 data delete
        ret = GmcKvRemoveAsync(stmt_async, key, strlen(key), delete_kv_callback, &tdata);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&tdata);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, tdata.status);
    }
    // ret = GmcCloseKvTable(stmt_async);
    // EXPECT_EQ(ret, GMERR_OK);
    printf("delete data\n");
    ret = testGmcDisconnect(conn_async, stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

// 04. 异步并发delete数据
TEST_F(KvMulThrAsync, DML_040_003_KvMulThrAsync_004)
{
    AsyncUserDataT data = {0};
    int tdNum = 2;
    int err = 0;
    void *kvtable = NULL;
    ret = GmcKvPrepareStmtByLabelName(g_stmt_async, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    GmcKvTupleT kvInfo = {0};
    char key[] = "zhangsan";
    int32_t value = 30;
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key);
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(int32_t);
    ret = GmcKvSetAsync(g_stmt_async, &kvInfo, set_kv_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    // close kv table
    // ret = GmcCloseKvTable(g_stmt_async);
    // EXPECT_EQ(ret, GMERR_OK);

    pthread_t kvTabel[tdNum];
    for (int i = 0; i < tdNum; i++) {
        err = pthread_create(&kvTabel[i], NULL, thread_delete_data, (void *)key);
        EXPECT_EQ(GMERR_OK, err);
    }
    for (int i = 0; i < tdNum; i++) {
        pthread_join(kvTabel[i], NULL);
    }
    // truncate kv 表
    ret = GmcKvTruncateTableAsync(g_stmt_async, kvTableName, truncate_kv_table_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
}
#if 0
void *thread_isExist_data(void *arg)
{
    AsyncUserDataT tdata = {0};
    GmcStmtT *stmt_async = NULL;
    GmcConnT *conn_async = NULL;
    int ret = testGmcConnect(&conn_async, &stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret); 
    void *kvtable = NULL;
    char *key = (char *)arg;
    ret = GmcKvPrepareStmtByLabelName(stmt_async, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    if(strncmp(key, "zhangsan1", strlen(key)) == 0) { // key1 data isExist
        ret = GmcKvIsExistAsync(stmt_async, kvtable, key, strlen(key), is_kv_exist_callback, &tdata);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&tdata);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, tdata.status);
        EXPECT_EQ(1, tdata.kvIsExist);
    }
    //ret = GmcCloseKvTable(stmt_async);
    //EXPECT_EQ(ret, GMERR_OK);
    printf("isExist data\n");
    ret = testGmcDisconnect(conn_async, stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
}

void *thread_recordCount_data(void *arg)
{
    AsyncUserDataT tdata = {0};
    GmcStmtT *stmt_async = NULL;
    GmcConnT *conn_async = NULL;
    int ret = testGmcConnect(&conn_async, &stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret); 
    void *kvtable = NULL;
    char *key = (char *)arg;
    ret = GmcKvPrepareStmtByLabelName(stmt_async, kvTableName);
    EXPECT_EQ(GMERR_OK, ret);
    if(strncmp(key, "zhangsan1", strlen(key)) == 0) { // key1 data record
        ret = GmcKvTableRecordCountAsync(stmt_async, kvtable, get_kv_record_count_callback, &tdata);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&tdata);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, tdata.status);
        EXPECT_EQ(1, tdata.kvCount);
    }
    //ret = GmcCloseKvTable(stmt_async);
    //EXPECT_EQ(ret, GMERR_OK);
    printf("record data\n");
    ret = testGmcDisconnect(conn_async, stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
}

// 05. 异步并发get、isExist、recordCount数据
TEST_F(KvMulThrAsync, DML_040_003_KvMulThrAsync_005)
{
    AsyncUserDataT data = {0};
    int tdNum = 2;
    int err = 0;
    void *kvtable = NULL;
    ret = GmcKvPrepareStmtByLabelName(g_stmt_async, kvTableName);
    EXPECT_EQ(GMERR_OK, ret);
    GmcKvTupleT kvInfo = { 0 };
    char key[] = "zhangsan";
    int32_t value = 30;
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key);
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(int32_t);
    ret = GmcKvSetAsync(g_stmt_async, &kvInfo,set_kv_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);

    //ret = GmcCloseKvTable(g_stmt_async);
    //EXPECT_EQ(ret, GMERR_OK);

    pthread_t kvTabel[6];
    err = pthread_create(&kvTabel[0], NULL, thread_get_data, (void *)key);
    EXPECT_EQ(GMERR_OK, err);
    err = pthread_create(&kvTabel[1], NULL, thread_get_data, (void *)key);
    EXPECT_EQ(GMERR_OK, err);
    err = pthread_create(&kvTabel[2], NULL, thread_isExist_data, (void *)key);
    EXPECT_EQ(GMERR_OK, err);
    err = pthread_create(&kvTabel[3], NULL, thread_isExist_data, (void *)key);
    EXPECT_EQ(GMERR_OK, err);
    err = pthread_create(&kvTabel[4], NULL, thread_recordCount_data, (void *)key);
    EXPECT_EQ(GMERR_OK, err);
    err = pthread_create(&kvTabel[5], NULL, thread_recordCount_data, (void *)key);
    EXPECT_EQ(GMERR_OK, err);

    pthread_join(kvTabel[0], NULL);
    pthread_join(kvTabel[1], NULL);
    pthread_join(kvTabel[2], NULL);
    pthread_join(kvTabel[3], NULL);
    pthread_join(kvTabel[4], NULL);
    pthread_join(kvTabel[5], NULL);

    //truncate kv 表
    ret = GmcKvTruncateTableAsync(g_stmt_async, kvTableName, truncate_kv_table_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
}
#endif
// 06. 异步接口混合测试
TEST_F(KvMulThrAsync, DML_040_003_KvMulThrAsync_006)
{
    AsyncUserDataT data = {0};
    void *kvtable = NULL;
    EXPECT_EQ(GMERR_OK, ret);
    //异步打开kv表
    ret = GmcKvPrepareStmtByLabelName(g_stmt_async, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    GmcKvTupleT kvInfo = {0};
    char key[] = "zhangsan";
    int32_t value = 30;
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key);
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(int32_t);
    ret = GmcKvSetAsync(g_stmt_async, &kvInfo, set_kv_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);

    // uint32_t tmpValue = 0;
    // data.kvValue = &tmpValue;
    // data.kvValueSize = sizeof(uint32_t);
    // ret = GmcKvGetAsync(g_stmt_async, kvtable, key, strlen(key), get_kv_callback, &data);
    // EXPECT_EQ(GMERR_OK, ret);
    // ret = testWaitAsyncRecv(&data);
    // EXPECT_EQ(GMERR_OK, ret);
    // EXPECT_EQ(GMERR_OK, data.status);
    // EXPECT_EQ(30, *(int *)data.kvValue);
    // EXPECT_EQ(4, data.kvValueSize);

    //异步检查kv表中数据是否存在
    // ret = GmcKvIsExistAsync(g_stmt_async, kvtable, key, strlen(key), is_kv_exist_callback, &data);
    // EXPECT_EQ(GMERR_OK, ret);
    // ret = testWaitAsyncRecv(&data);
    // EXPECT_EQ(GMERR_OK, ret);
    // EXPECT_EQ(GMERR_OK, data.status);
    // EXPECT_EQ(1, data.kvIsExist);

    //异步查询kv表记录数
    // ret = GmcKvTableRecordCountAsync(g_stmt_async, kvtable, get_kv_record_count_callback, &data);
    // EXPECT_EQ(GMERR_OK, ret);
    // ret = testWaitAsyncRecv(&data);
    // EXPECT_EQ(GMERR_OK, ret);
    // EXPECT_EQ(GMERR_OK, data.status);
    // EXPECT_EQ(1, data.kvCount);

    // 删除kv表中一条数据
    ret = GmcKvRemoveAsync(g_stmt_async, key, strlen(key), delete_kv_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);

    // ret = GmcCloseKvTable(g_stmt_async);
    // EXPECT_EQ(ret, GMERR_OK);

    // truncate kv 表
    ret = GmcKvTruncateTableAsync(g_stmt_async, kvTableName, truncate_kv_table_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
}
