
#include "VertexLabelTypical.h"
GmcConnT *gConn = NULL;
GmcStmtT *gStmt = NULL;
const char *gLabelLpm4JsonPath = "./schema/VertexLabelLpm4.gmjson";
const char *gLabelLpm4Name = "Lpm4VertexLabel";
const char *gKvTableName = "T_GMDB";
// const char *gLabelTypicalJsonPath2 = "./schema/VertexLabelTypical2.gmjson";
// const char *gLabelTypicalName2 = "TypicalVertexLabel2";
int32_t gStartVal = 10;
uint32_t gCount = 1000;
GtTypicalVertexCfgT gVertexCfg = {gStartVal, gCount, 10, 60, 0};
// const char *gKvTableName = NULL;
class V3_trans_big_object : public testing::Test {
public:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/modifyCfg.sh  \"compatibleV3=0\"");
        GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh");
        int ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        ASSERT_EQ(GMERR_OK, ret);
        ret = testGmcConnect(&gConn, &gStmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    static void TearDownTestCase()
    {
        int ret = testGmcDisconnect(gConn, gStmt);
        EXPECT_EQ(GMERR_OK, ret);
        gConn = NULL;
        gStmt = NULL;
        ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        ret = testEnvClean();
        EXPECT_EQ(GMERR_OK, ret);
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    }

    virtual void SetUp()
    {
        GtResetTypicalFieldLenth();
        int ret = GtCreateVertexLabel(gStmt, gLabelTypicalJsonPath, gConfigJsonPath);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GtCreateVertexLabel(gStmt, gLabelTypicalJsonPath2, gConfigJsonPath);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GtCreateVertexLabel(gStmt, gLabelTypicalJsonPath3, gConfigJsonPath);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GtCreateVertexLabel(gStmt, gLabelTypicalJsonPath4, gConfigJsonPath);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GtCreateVertexLabel(gStmt, gLabelTypicalJsonPath5, gConfigJsonPath);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GtCreateVertexLabel(gStmt, gLabelTypicalJsonPath6, gConfigJsonPath);
        ASSERT_EQ(GMERR_OK, ret);
        // ret = GmcKvCreateTable(gStmt, gKvTableName, NULL);
        // ASSERT_EQ(GMERR_OK, ret);
        // ret = GtCreateVertexLabel(gStmt, gLabelLpm4JsonPath, gConfigJsonPath);
        // ASSERT_EQ(GMERR_OK, ret);
        GtSetTypicalScanVerifyRule(true);
        // GtSetLpm4ScanVerifyRule(true);
        AW_CHECK_LOG_BEGIN();

        char errorMsg1[128] = {};
        (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_INVALID_VALUE);
        AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

        char errorMsg2[128] = {};
        (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_MEMBER_KEY_VIOLATION);
        AW_ADD_ERR_WHITE_LIST(1, errorMsg2);

        char errorMsg3[128] = {};
        (void)snprintf(errorMsg3, sizeof(errorMsg3), "GMERR-%d", GMERR_INVALID_JSON_CONTENT);
        AW_ADD_ERR_WHITE_LIST(1, errorMsg3);
    }

    virtual void TearDown()
    {
        AW_CHECK_LOG_END();
        GmcKvDropTable(gStmt, gKvTableName);
        GmcDropVertexLabel(gStmt, gLabelTypicalName);
        GmcDropVertexLabel(gStmt, gLabelTypicalName2);
        GmcDropVertexLabel(gStmt, gLabelTypicalName3);
        GmcDropVertexLabel(gStmt, gLabelTypicalName4);
        GmcDropVertexLabel(gStmt, gLabelTypicalName5);
        GmcDropVertexLabel(gStmt, gLabelTypicalName6);
    }
};

char g_command[1024];
// 写入小于32K的默认kv表对象
TEST_F(V3_trans_big_object, DML_077_V3_001_001)
{
    uint32_t valLen = 32 * 1024 - 1;
    int ret = GtKvSetBytesArray_Global(gStmt, gKvTableName, 0, 'a', valLen);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GtKvGetBytesArray(gStmt, gKvTableName, 0, 'a', valLen);
    ASSERT_EQ(GMERR_OK, ret);
    char const *view_name = "V\\$CATA_KV_TABLE_INFO";
    snprintf(g_command, 1024, "%s/gmsysview -u %s -s %s -p %s  -q %s", g_toolPath, g_userName, g_connServer, g_passwd,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    memset(g_command, 0, sizeof(g_command));
    view_name = "V\\$STORAGE_KV_STAT";
    snprintf(g_command, 1024, "%s/gmsysview -u %s -s %s -p %s  -q %s", g_toolPath, g_userName, g_connServer, g_passwd,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    memset(g_command, 0, sizeof(g_command));
}

// 写入大于32K的默认kv表对象
TEST_F(V3_trans_big_object, DML_077_V3_001_002)
{
    uint32_t valLen = 32 * 1024 + 1;
    int ret = GtKvSetBytesArray_Global(gStmt, gKvTableName, 0, 'a', valLen);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GtKvGetBytesArray(gStmt, gKvTableName, 0, 'a', valLen);
    ASSERT_EQ(GMERR_OK, ret);
    char const *view_name = "V\\$CATA_KV_TABLE_INFO";
    snprintf(g_command, 1024, "%s/gmsysview -u %s -s %s -p %s  -q %s", g_toolPath, g_userName, g_connServer, g_passwd,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    memset(g_command, 0, sizeof(g_command));
    view_name = "V\\$STORAGE_KV_STAT";
    snprintf(g_command, 1024, "%s/gmsysview -u %s -s %s -p %s  -q %s", g_toolPath, g_userName, g_connServer, g_passwd,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
}

// 写入小于等于1M的默认kv表对象
TEST_F(V3_trans_big_object, DML_077_V3_001_003)
{
    uint32_t valLen = 1024 * 1023;
    int ret = GtKvSetBytesArray_Global(gStmt, gKvTableName, 0, 'a', valLen);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GtKvGetBytesArray(gStmt, gKvTableName, 0, 'a', valLen);
    ASSERT_EQ(GMERR_OK, ret);
    char const *view_name = "V\\$CATA_KV_TABLE_INFO";
    snprintf(g_command, 1024, "%s/gmsysview -u %s -s %s -p %s  -q %s", g_toolPath, g_userName, g_connServer, g_passwd,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    memset(g_command, 0, sizeof(g_command));
    view_name = "V\\$STORAGE_KV_STAT";
    snprintf(g_command, 1024, "%s/gmsysview -u %s -s %s -p %s  -q %s", g_toolPath, g_userName, g_connServer, g_passwd,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
}

// 写入大于1M的默认kv表对象
TEST_F(V3_trans_big_object, DML_077_V3_001_004)
{
    uint32_t valLen = 1024 * 1025;
    int ret = GtKvSetBytesArray_Global(gStmt, gKvTableName, 0, 'a', valLen);
    EXPECT_EQ(GMERR_INVALID_VALUE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GtKvGetBytesArray(gStmt, gKvTableName, 0, 'a', valLen);
    ASSERT_NE(0, ret);
    char const *view_name = "V\\$CATA_KV_TABLE_INFO";
    snprintf(g_command, 1024, "%s/gmsysview -u %s -s %s -p %s  -q %s", g_toolPath, g_userName, g_connServer, g_passwd,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    memset(g_command, 0, sizeof(g_command));
    view_name = "V\\$STORAGE_KV_STAT";
    snprintf(g_command, 1024, "%s/gmsysview -u %s -s %s -p %s  -q %s", g_toolPath, g_userName, g_connServer, g_passwd,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
}

// 写入小于等于1M的默认kv对象, 覆盖写后删除
TEST_F(V3_trans_big_object, DML_077_V3_001_005)
{
    uint32_t valLen = 1024 * 1023;
    int ret = GmcKvTruncateTable(gStmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(gStmt, NULL);
    uint8_t *val = (uint8_t *)malloc(valLen * sizeof(uint8_t));
    memset(val, 'a', valLen);
    GmcKvTupleT kvTuple;
    int64_t key = 1;
    kvTuple.key = &key;
    kvTuple.keyLen = sizeof(key);
    kvTuple.value = val;
    kvTuple.valueLen = valLen;
    ret = GmcKvSet(gStmt, &key, sizeof(key), val, valLen);
    free(val);

    uint8_t *val2 = (uint8_t *)malloc(valLen * sizeof(uint8_t));
    memset(val2, 'b', valLen);
    kvTuple.key = &key;
    kvTuple.keyLen = sizeof(key);
    kvTuple.value = val2;
    kvTuple.valueLen = valLen;
    ret = GmcKvSet(gStmt, &key, sizeof(key), val2, valLen);
    free(val2);

    char const *view_name = "V\\$CATA_KV_TABLE_INFO";
    snprintf(g_command, 1024, "%s/gmsysview -u %s -s %s -p %s  -q %s", g_toolPath, g_userName, g_connServer, g_passwd,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    memset(g_command, 0, sizeof(g_command));
    view_name = "V\\$STORAGE_KV_STAT";
    snprintf(g_command, 1024, "%s/gmsysview -u %s -s %s -p %s  -q %s", g_toolPath, g_userName, g_connServer, g_passwd,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    ret = GmcKvRemove(gStmt, &key, sizeof(key));
    EXPECT_EQ(GMERR_OK, ret);
    bool isExist = true;
    uint32_t count = 0;
    ret = GmcKvTableRecordCount(gStmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, count);
    ret = GmcKvIsExist(gStmt, &key, sizeof(key), &isExist);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, isExist);
    printf("____________________!!!!!!!!!!!!!!!!!!!!!_______________________");
    view_name = "V\\$CATA_KV_TABLE_INFO";
    snprintf(g_command, 1024, "%s/gmsysview -u %s -s %s -p %s  -q %s", g_toolPath, g_userName, g_connServer, g_passwd,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
    memset(g_command, 0, sizeof(g_command));
    view_name = "V\\$STORAGE_KV_STAT";
    snprintf(g_command, 1024, "%s/gmsysview -u %s -s %s -p %s  -q %s", g_toolPath, g_userName, g_connServer, g_passwd,
        view_name);
    printf("%s\n", g_command);
    system(g_command);
}

// local索引多字段扫描数据
TEST_F(V3_trans_big_object, DML_077_V3_001_006)
{
    GtTypicalVertexCfgT vertexCfg = {10, 1000, 10, 60, 0};
    int ret = GtReplaceTypicalVertex2(gStmt, vertexCfg);
    ASSERT_EQ(GMERR_OK, ret);
    int32_t left = gStartVal;
    int32_t right = gStartVal + gCount;
    ret = GtScanTypicalVertexByLocalIndex(gStmt, gVertexCfg, "LocalKey", left, right, GMC_ORDER_DESC);
    ASSERT_EQ(GMERR_OK, ret);
}
// local 单字段扫描删
TEST_F(V3_trans_big_object, DML_077_V3_001_007)
{
    GtTypicalVertexCfgT vertexCfg = {10, 1000, 10, 60, 0};
    int ret = GtReplaceTypicalVertex(gStmt, vertexCfg);
    ASSERT_EQ(GMERR_OK, ret);
    int32_t left = gStartVal;
    int32_t right = gStartVal + gCount;
    ret = GtScanTypicalVertexByLocalIndex(gStmt, gVertexCfg, "LocalKey", left, right, GMC_ORDER_DESC);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GtDeleteTypicalVertexByIndex(gStmt, vertexCfg, "LocalKey");
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t count;
    ret = GmcGetVertexCount(gStmt, gLabelTypicalName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(0, count);
}
// local 多字段扫描删
TEST_F(V3_trans_big_object, DML_077_V3_001_008)
{
    GtTypicalVertexCfgT vertexCfg = {10, 1000, 10, 60, 0};
    int ret = GtReplaceTypicalVertex2(gStmt, vertexCfg);
    ASSERT_EQ(GMERR_OK, ret);
    int32_t left = gStartVal;
    int32_t right = gStartVal + gCount;
    ret = GtScanTypicalVertexByLocalIndex(gStmt, gVertexCfg, "LocalKey", left, right, GMC_ORDER_DESC);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GtDeleteTypicalVertexByIndex(gStmt, vertexCfg, "LocalKey");
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t count;
    ret = GmcGetVertexCount(gStmt, gLabelTypicalName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(0, count);
}
// local key 2个字段 左右相等扫描数据,只设置一个字段预期不进入循环
TEST_F(V3_trans_big_object, DML_077_V3_001_009)
{
    GtTypicalVertexCfgT vertexCfg = {10, 1000, 10, 60, 0};
    int ret = GtReplaceTypicalVertex2(gStmt, vertexCfg);
    ASSERT_EQ(GMERR_OK, ret);
    int32_t left = gStartVal;
    int32_t right = gStartVal + 1;
    ret = GtScanTypicalVertexByLocalIndex2(gStmt, gVertexCfg, "LocalKey", left, right, GMC_ORDER_DESC);
    ASSERT_EQ(GMERR_OK, ret);
}
// local key 1个字段 左右相等扫描数据（默认local为4K一条数据，按实际情况可以扩展）
TEST_F(V3_trans_big_object, DML_077_V3_001_010)
{
    // GtSetTypicalFieldLenth(4096, 4096);
    GtTypicalVertexCfgT vertexCfg = {10, 1000, 10, 60, 0};
    int ret = GtReplaceTypicalVertex(gStmt, vertexCfg);
    ASSERT_EQ(GMERR_OK, ret);
    int32_t left = gStartVal;
    int32_t right = gStartVal + 1;
    ret = GtScanTypicalVertexByLocalIndex2(gStmt, gVertexCfg, "LocalKey", left, right, GMC_ORDER_DESC);
    ASSERT_EQ(GMERR_OK, ret);
}
//唯一localhash索引扫描删除
TEST_F(V3_trans_big_object, DML_077_V3_001_011)
{

    GtTypicalVertexCfgT vertexCfg = {10, 100, 10, 60, 0};
    int ret = GtReplaceTypicalVertex3(gStmt, vertexCfg);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GtScanTypicalVertexByHashIndex(gStmt, gVertexCfg, "LocalHashKey");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GtDeleteTypicalVertexByIndex(gStmt, vertexCfg, "LocalHashKey");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GtScanTypicalVertexByHashIndex(gStmt, gVertexCfg, "LocalHashKey");
    ASSERT_EQ(GMERR_OK, ret);
}
//唯一localhash索引扫描删除,再次核查数据
TEST_F(V3_trans_big_object, DML_077_V3_001_012)
{
    GtTypicalVertexCfgT vertexCfg = {10, 100, 10, 60, 0};
    int ret = GtReplaceTypicalVertex3(gStmt, vertexCfg);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GtScanTypicalVertexByHashIndex(gStmt, gVertexCfg, "LocalHashKey");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GtDeleteTypicalVertexByIndex(gStmt, vertexCfg, "LocalHashKey");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GtScanTypicalVertexByHashIndex(gStmt, gVertexCfg, "LocalHashKey");
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t count;
    ret = GmcGetVertexCount(gStmt, gLabelTypicalName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(0, count);
}
// member key 写二级索引冲突(schema中为唯一)为什么memberkey不能设置B4————》B4大小512K可以
TEST_F(V3_trans_big_object, DML_077_V3_001_013)
{
    GtTypicalVertexCfgT vertexCfg = {10, 1, 10, 2, 0};
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t startMkVal = vertexCfg.startMkVal;
    uint32_t childCount = vertexCfg.childCount;
    int32_t coefficient = vertexCfg.coefficient;

    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        int ret = testGmcPrepareStmtByLabelName(gStmt, gLabelTypicalName4, GMC_OPERATION_REPLACE);
        ASSERT_EQ(GMERR_OK, ret);

        GmcNodeT *rootNode = NULL;
        ret = GmcGetRootNode(gStmt, &rootNode);
        ASSERT_EQ(GMERR_OK, ret);

        // 设置所有根节点属性
        int32_t a0 = i;
        ret = GmcSetVertexProperty(gStmt, "A0", GMC_DATATYPE_INT32, &a0, sizeof(a0));
        ASSERT_EQ(GMERR_OK, ret);

        // 设置非主键属性
        int64_t eleNum = i + coefficient;
        uint8_t eleBit = abs(coefficient) % 255;
        uint8_t eleStr = abs(coefficient) % 26 + 'a';
        uint8_t eleFixed = abs(coefficient) % 26 + 'A';
        uint8_t eleBytes = abs(coefficient) % 26 + 'A';

        int64_t a1 = eleNum;
        ret = GmcSetVertexProperty(gStmt, "A1", GMC_DATATYPE_INT64, &a1, sizeof(a1));
        ASSERT_EQ(GMERR_OK, ret);
        uint32_t a2 = eleNum;
        ret = GmcSetVertexProperty(gStmt, "A2", GMC_DATATYPE_UINT32, &a2, sizeof(a2));
        ASSERT_EQ(GMERR_OK, ret);
        uint64_t a3 = eleNum;
        ret = GmcSetVertexProperty(gStmt, "A3", GMC_DATATYPE_UINT64, &a3, sizeof(a3));
        ASSERT_EQ(GMERR_OK, ret);
        float a4 = eleNum;
        ret = GmcSetVertexProperty(gStmt, "A4", GMC_DATATYPE_FLOAT, &a4, sizeof(a4));
        ASSERT_EQ(GMERR_OK, ret);
        double a5 = eleNum;
        ret = GmcSetVertexProperty(gStmt, "A5", GMC_DATATYPE_DOUBLE, &a5, sizeof(a5));
        ASSERT_EQ(GMERR_OK, ret);

        uint8_t bits[TYPICAL_FIELD_BITMAP_LEN / 8] = {0};
        GtFillBytes(bits, sizeof(bits), eleBit, false);
        GmcBitMapT a6 = {0};
        a6.beginPos = 0;
        a6.endPos = TYPICAL_FIELD_BITMAP_LEN - 1;
        a6.bits = bits;
        ret = GmcSetVertexProperty(gStmt, "A6", GMC_DATATYPE_BITMAP, &a6, sizeof(a6));
        ASSERT_EQ(GMERR_OK, ret);

        uint8_t a7[TYPICAL_FIELD_FIXED_LEN] = {0};
        GtFillBytes(a7, sizeof(a7), eleFixed, false);
        ret = GmcSetVertexProperty(gStmt, "A7", GMC_DATATYPE_FIXED, &a7, sizeof(a7));
        ASSERT_EQ(GMERR_OK, ret);

        uint8_t a8[4096] = {0};
        GtFillBytes(a8, sizeof(a8), eleFixed, false);
        ret = GmcSetVertexProperty(gStmt, "A8", GMC_DATATYPE_BYTES, a8, sizeof(a8));
        ASSERT_EQ(GMERR_OK, ret);

        uint8_t a9[4096] = {0};
        GtFillBytes(a9, sizeof(a9), eleStr, true);
        ret = GmcSetVertexProperty(gStmt, "A9", GMC_DATATYPE_STRING, a9, strlen((char *)a9));
        ASSERT_EQ(GMERR_OK, ret);

        // 设置子节点属性
        GmcNodeT *childNode = NULL;
        ret = GmcGetChildNode(gStmt, "M0", &childNode);
        ASSERT_EQ(GMERR_OK, ret);
        // uint8_t eleStr = abs(coefficient) % 26 + 'a';
        // uint8_t eleBytes = abs(coefficient) % 26 + 'A';
        for (int i = startMkVal; i < startMkVal + childCount; i++) {
            int64_t eleNum = i + coefficient;
            GmcNodeT *elementNode = NULL;
            int ret = GmcNodeAppendElement(childNode, &elementNode);
            ASSERT_EQ(GMERR_OK, ret);

            int32_t b0 = i;
            ret = GmcNodeSetPropertyByName(elementNode, "B0", GMC_DATATYPE_INT32, &b0, sizeof(b0));
            ASSERT_EQ(GMERR_OK, ret);
            uint32_t b1 = eleNum;
            ret = GmcNodeSetPropertyByName(elementNode, "B1", GMC_DATATYPE_UINT32, &b1, sizeof(b1));
            ASSERT_EQ(GMERR_OK, ret);

            uint8_t b2[11312] = {0};
            GtFillBytes(b2, sizeof(b2), eleBytes, false);
            ret = GmcNodeSetPropertyByName(elementNode, "B2", GMC_DATATYPE_BYTES, b2, sizeof(b2));
            ASSERT_EQ(GMERR_OK, ret);
            uint8_t b3[11312] = {0};
            GtFillBytes(b3, sizeof(b3), eleBytes, false);
            ret = GmcNodeSetPropertyByName(elementNode, "B3", GMC_DATATYPE_BYTES, b3, sizeof(b3));
            ASSERT_EQ(GMERR_OK, ret);

            uint8_t b4[512] = {0};
            GtFillBytes(b4, sizeof(b4), eleStr, true);
            ret = GmcNodeSetPropertyByName(elementNode, "B4", GMC_DATATYPE_STRING, b4, strlen((char *)b4));
            ASSERT_EQ(GMERR_OK, ret);
            uint8_t b5[11312] = {0};
            GtFillBytes(b5, sizeof(b5), eleStr, true);
            ret = GmcNodeSetPropertyByName(elementNode, "B5", GMC_DATATYPE_STRING, b5, strlen((char *)b5));
            ASSERT_EQ(GMERR_OK, ret);

            // 如果还有子节点, 通过该接口获取其句柄: GmcNodeGetChild()
        }
        // ret = GtAddTypicalArrayElement(childNode, startMkVal, childCount, coefficient);
        ret = GmcExecute(gStmt);
        EXPECT_EQ(GMERR_MEMBER_KEY_VIOLATION, ret);
    }
}
// member key 写二级索引冲突(schema中为非唯一)
TEST_F(V3_trans_big_object, DML_077_V3_001_014)
{
    GtTypicalVertexCfgT vertexCfg = {10, 1, 10, 4, 0};
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t startMkVal = vertexCfg.startMkVal;
    uint32_t childCount = vertexCfg.childCount;
    int32_t coefficient = vertexCfg.coefficient;

    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        int ret = testGmcPrepareStmtByLabelName(gStmt, gLabelTypicalName5, GMC_OPERATION_REPLACE);
        ASSERT_EQ(GMERR_OK, ret);

        GmcNodeT *rootNode = NULL;
        ret = GmcGetRootNode(gStmt, &rootNode);
        ASSERT_EQ(GMERR_OK, ret);

        // 设置所有根节点属性
        int32_t a0 = i;
        ret = GmcSetVertexProperty(gStmt, "A0", GMC_DATATYPE_INT32, &a0, sizeof(a0));
        ASSERT_EQ(GMERR_OK, ret);

        // 设置非主键属性
        int64_t eleNum = i + coefficient;
        uint8_t eleBit = abs(coefficient) % 255;
        uint8_t eleStr = abs(coefficient) % 26 + 'a';
        uint8_t eleFixed = abs(coefficient) % 26 + 'A';
        uint8_t eleBytes = abs(coefficient) % 26 + 'A';

        int64_t a1 = eleNum;
        ret = GmcSetVertexProperty(gStmt, "A1", GMC_DATATYPE_INT64, &a1, sizeof(a1));
        ASSERT_EQ(GMERR_OK, ret);
        uint32_t a2 = eleNum;
        ret = GmcSetVertexProperty(gStmt, "A2", GMC_DATATYPE_UINT32, &a2, sizeof(a2));
        ASSERT_EQ(GMERR_OK, ret);
        uint64_t a3 = eleNum;
        ret = GmcSetVertexProperty(gStmt, "A3", GMC_DATATYPE_UINT64, &a3, sizeof(a3));
        ASSERT_EQ(GMERR_OK, ret);
        float a4 = eleNum;
        ret = GmcSetVertexProperty(gStmt, "A4", GMC_DATATYPE_FLOAT, &a4, sizeof(a4));
        ASSERT_EQ(GMERR_OK, ret);
        double a5 = eleNum;
        ret = GmcSetVertexProperty(gStmt, "A5", GMC_DATATYPE_DOUBLE, &a5, sizeof(a5));
        ASSERT_EQ(GMERR_OK, ret);

        uint8_t bits[TYPICAL_FIELD_BITMAP_LEN / 8] = {0};
        GtFillBytes(bits, sizeof(bits), eleBit, false);
        GmcBitMapT a6 = {0};
        a6.beginPos = 0;
        a6.endPos = TYPICAL_FIELD_BITMAP_LEN - 1;
        a6.bits = bits;
        ret = GmcSetVertexProperty(gStmt, "A6", GMC_DATATYPE_BITMAP, &a6, sizeof(a6));
        ASSERT_EQ(GMERR_OK, ret);

        uint8_t a7[TYPICAL_FIELD_FIXED_LEN] = {0};
        GtFillBytes(a7, sizeof(a7), eleFixed, false);
        ret = GmcSetVertexProperty(gStmt, "A7", GMC_DATATYPE_FIXED, &a7, sizeof(a7));
        ASSERT_EQ(GMERR_OK, ret);

        uint8_t a8[4096] = {0};
        GtFillBytes(a8, sizeof(a8), eleFixed, false);
        ret = GmcSetVertexProperty(gStmt, "A8", GMC_DATATYPE_BYTES, a8, sizeof(a8));
        ASSERT_EQ(GMERR_OK, ret);

        uint8_t a9[4096] = {0};
        GtFillBytes(a9, sizeof(a9), eleStr, true);
        ret = GmcSetVertexProperty(gStmt, "A9", GMC_DATATYPE_STRING, a9, strlen((char *)a9));
        ASSERT_EQ(GMERR_OK, ret);

        // 设置子节点属性
        GmcNodeT *childNode = NULL;
        ret = GmcGetChildNode(gStmt, "M0", &childNode);
        ASSERT_EQ(GMERR_OK, ret);
        // uint8_t eleStr = abs(coefficient) % 26 + 'a';
        // uint8_t eleBytes = abs(coefficient) % 26 + 'A';
        for (int i = startMkVal; i < startMkVal + childCount; i++) {
            int64_t eleNum = i + coefficient;
            GmcNodeT *elementNode = NULL;
            int ret = GmcNodeAppendElement(childNode, &elementNode);
            ASSERT_EQ(GMERR_OK, ret);

            int32_t b0 = i;
            ret = GmcNodeSetPropertyByName(elementNode, "B0", GMC_DATATYPE_INT32, &b0, sizeof(b0));
            ASSERT_EQ(GMERR_OK, ret);
            uint32_t b1 = eleNum;
            ret = GmcNodeSetPropertyByName(elementNode, "B1", GMC_DATATYPE_UINT32, &b1, sizeof(b1));
            ASSERT_EQ(GMERR_OK, ret);

            uint8_t b2[11312] = {0};
            GtFillBytes(b2, sizeof(b2), eleBytes, false);
            ret = GmcNodeSetPropertyByName(elementNode, "B2", GMC_DATATYPE_BYTES, b2, sizeof(b2));
            ASSERT_EQ(GMERR_OK, ret);
            uint8_t b3[11312] = {0};
            GtFillBytes(b3, sizeof(b3), eleBytes, false);
            ret = GmcNodeSetPropertyByName(elementNode, "B3", GMC_DATATYPE_BYTES, b3, sizeof(b3));
            ASSERT_EQ(GMERR_OK, ret);

            uint8_t b4[512] = {0};
            GtFillBytes(b4, sizeof(b4), eleStr, true);
            ret = GmcNodeSetPropertyByName(elementNode, "B4", GMC_DATATYPE_STRING, b4, strlen((char *)b4));
            ASSERT_EQ(GMERR_OK, ret);
            uint8_t b5[11312] = {0};
            GtFillBytes(b5, sizeof(b5), eleStr, true);
            ret = GmcNodeSetPropertyByName(elementNode, "B5", GMC_DATATYPE_STRING, b5, strlen((char *)b5));
            ASSERT_EQ(GMERR_OK, ret);

            // 如果还有子节点, 通过该接口获取其句柄: GmcNodeGetChild()
        }
        // ret = GtAddTypicalArrayElement(childNode, startMkVal, childCount, coefficient);
        ret = GmcExecute(gStmt);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != 0) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
}

// member key 数组节点写入冲突值再次写入非冲突值
TEST_F(V3_trans_big_object, DML_077_V3_001_015)
{
    GtTypicalVertexCfgT vertexCfg = {10, 1, 10, 2, 0};
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t startMkVal = vertexCfg.startMkVal;
    uint32_t childCount = vertexCfg.childCount;
    int32_t coefficient = vertexCfg.coefficient;

    int ret = testGmcPrepareStmtByLabelName(gStmt, gLabelTypicalName4, GMC_OPERATION_REPLACE);
    ASSERT_EQ(GMERR_OK, ret);
    int32_t i;
    // 设置非主键属性
    int64_t eleNum = i + coefficient;
    uint8_t eleBit = abs(coefficient) % 255;
    uint8_t eleStr = abs(coefficient) % 26 + 'a';
    uint8_t eleStr2 = abs(coefficient) % 26 + 'b';
    uint8_t eleFixed = abs(coefficient) % 26 + 'A';
    uint8_t eleBytes = abs(coefficient) % 26 + 'A';
    uint8_t eleBytes2 = abs(coefficient) % 26 + 'B';
    GmcNodeT *rootNode = NULL;
    ret = GmcGetRootNode(gStmt, &rootNode);
    ASSERT_EQ(GMERR_OK, ret);

    // 设置所有根节点属性
    int32_t a0 = 10;
    ret = GmcSetVertexProperty(gStmt, "A0", GMC_DATATYPE_INT32, &a0, sizeof(a0));
    ASSERT_EQ(GMERR_OK, ret);

    int64_t a1 = eleNum;
    ret = GmcSetVertexProperty(gStmt, "A1", GMC_DATATYPE_INT64, &a1, sizeof(a1));
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t a2 = eleNum;
    ret = GmcSetVertexProperty(gStmt, "A2", GMC_DATATYPE_UINT32, &a2, sizeof(a2));
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t a3 = eleNum;
    ret = GmcSetVertexProperty(gStmt, "A3", GMC_DATATYPE_UINT64, &a3, sizeof(a3));
    ASSERT_EQ(GMERR_OK, ret);
    float a4 = eleNum;
    ret = GmcSetVertexProperty(gStmt, "A4", GMC_DATATYPE_FLOAT, &a4, sizeof(a4));
    ASSERT_EQ(GMERR_OK, ret);
    double a5 = eleNum;
    ret = GmcSetVertexProperty(gStmt, "A5", GMC_DATATYPE_DOUBLE, &a5, sizeof(a5));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t bits[TYPICAL_FIELD_BITMAP_LEN / 8] = {0};
    GtFillBytes(bits, sizeof(bits), eleBit, false);
    GmcBitMapT a6 = {0};
    a6.beginPos = 0;
    a6.endPos = TYPICAL_FIELD_BITMAP_LEN - 1;
    a6.bits = bits;
    ret = GmcSetVertexProperty(gStmt, "A6", GMC_DATATYPE_BITMAP, &a6, sizeof(a6));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t a7[TYPICAL_FIELD_FIXED_LEN] = {0};
    GtFillBytes(a7, sizeof(a7), eleFixed, false);
    ret = GmcSetVertexProperty(gStmt, "A7", GMC_DATATYPE_FIXED, &a7, sizeof(a7));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t a8[4096] = {0};
    GtFillBytes(a8, sizeof(a8), eleFixed, false);
    ret = GmcSetVertexProperty(gStmt, "A8", GMC_DATATYPE_BYTES, a8, sizeof(a8));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t a9[4096] = {0};
    GtFillBytes(a9, sizeof(a9), eleStr, true);
    ret = GmcSetVertexProperty(gStmt, "A9", GMC_DATATYPE_STRING, a9, strlen((char *)a9));
    ASSERT_EQ(GMERR_OK, ret);

    // 设置子节点属性
    GmcNodeT *childNode = NULL;
    ret = GmcGetChildNode(gStmt, "M0", &childNode);
    ASSERT_EQ(GMERR_OK, ret);
    // uint8_t eleStr = abs(coefficient) % 26 + 'a';
    // uint8_t eleBytes = abs(coefficient) % 26 + 'A';
    for (int i = startMkVal; i < startMkVal + childCount + 1; i++) {
        if (i < startMkVal + 2) {
            int64_t eleNum = i + coefficient;
            GmcNodeT *elementNode = NULL;
            int ret = GmcNodeAppendElement(childNode, &elementNode);
            ASSERT_EQ(GMERR_OK, ret);
            int32_t b0 = i;
            ret = GmcNodeSetPropertyByName(elementNode, "B0", GMC_DATATYPE_INT32, &b0, sizeof(b0));
            ASSERT_EQ(GMERR_OK, ret);
            uint32_t b1 = eleNum;
            ret = GmcNodeSetPropertyByName(elementNode, "B1", GMC_DATATYPE_UINT32, &b1, sizeof(b1));
            ASSERT_EQ(GMERR_OK, ret);

            uint8_t b2[11312] = {0};
            GtFillBytes(b2, sizeof(b2), eleBytes, false);
            ret = GmcNodeSetPropertyByName(elementNode, "B2", GMC_DATATYPE_BYTES, b2, sizeof(b2));
            ASSERT_EQ(GMERR_OK, ret);
            uint8_t b3[11312] = {0};
            GtFillBytes(b3, sizeof(b3), eleBytes, false);
            ret = GmcNodeSetPropertyByName(elementNode, "B3", GMC_DATATYPE_BYTES, b3, sizeof(b3));
            ASSERT_EQ(GMERR_OK, ret);

            uint8_t b4[512] = {0};
            GtFillBytes(b4, sizeof(b4), eleStr, true);
            ret = GmcNodeSetPropertyByName(elementNode, "B4", GMC_DATATYPE_STRING, b4, strlen((char *)b4));
            ASSERT_EQ(GMERR_OK, ret);
            uint8_t b5[11312] = {0};
            GtFillBytes(b5, sizeof(b5), eleStr, true);
            ret = GmcNodeSetPropertyByName(elementNode, "B5", GMC_DATATYPE_STRING, b5, strlen((char *)b5));
            ASSERT_EQ(GMERR_OK, ret);
        } else if (i == startMkVal + 2) {
            int64_t eleNum = i + coefficient;
            GmcNodeT *elementNode = NULL;
            int ret = GmcNodeAppendElement(childNode, &elementNode);
            ASSERT_EQ(GMERR_OK, ret);
            int32_t b0 = i;
            ret = GmcNodeSetPropertyByName(elementNode, "B0", GMC_DATATYPE_INT32, &b0, sizeof(b0));
            ASSERT_EQ(GMERR_OK, ret);
            uint32_t b1 = eleNum;
            ret = GmcNodeSetPropertyByName(elementNode, "B1", GMC_DATATYPE_UINT32, &b1, sizeof(b1));
            ASSERT_EQ(GMERR_OK, ret);

            uint8_t b2[11312] = {0};
            GtFillBytes(b2, sizeof(b2), eleBytes2, false);
            ret = GmcNodeSetPropertyByName(elementNode, "B2", GMC_DATATYPE_BYTES, b2, sizeof(b2));
            ASSERT_EQ(GMERR_OK, ret);
            uint8_t b3[11312] = {0};
            GtFillBytes(b3, sizeof(b3), eleBytes2, false);
            ret = GmcNodeSetPropertyByName(elementNode, "B3", GMC_DATATYPE_BYTES, b3, sizeof(b3));
            ASSERT_EQ(GMERR_OK, ret);

            uint8_t b4[512] = {0};
            GtFillBytes(b4, sizeof(b4), eleStr2, true);
            ret = GmcNodeSetPropertyByName(elementNode, "B4", GMC_DATATYPE_STRING, b4, strlen((char *)b4));
            ASSERT_EQ(GMERR_OK, ret);
            uint8_t b5[11312] = {0};
            GtFillBytes(b5, sizeof(b5), eleStr2, true);
            ret = GmcNodeSetPropertyByName(elementNode, "B5", GMC_DATATYPE_STRING, b5, strlen((char *)b5));
            ASSERT_EQ(GMERR_OK, ret);
        }
    }
    ret = GmcExecute(gStmt);
    EXPECT_EQ(GMERR_MEMBER_KEY_VIOLATION, ret);
}

// member key 整体写入冲突值再次写入非冲突值[待确认]
TEST_F(V3_trans_big_object, DML_077_V3_001_016)
{
    GtTypicalVertexCfgT vertexCfg = {10, 1, 10, 2, 0};
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t startMkVal = vertexCfg.startMkVal;
    uint32_t childCount = vertexCfg.childCount;
    int32_t coefficient = vertexCfg.coefficient;

    int ret = testGmcPrepareStmtByLabelName(gStmt, gLabelTypicalName4, GMC_OPERATION_REPLACE);
    ASSERT_EQ(GMERR_OK, ret);
    int32_t i;
    // 设置非主键属性
    int64_t eleNum = i + coefficient;
    int64_t eleNum2 = i + 99;
    uint8_t eleBit = abs(coefficient) % 255;
    uint8_t eleBit2 = abs(7) % 255;
    uint8_t eleStr = abs(coefficient) % 26 + 'a';
    uint8_t eleStr2 = abs(coefficient) % 26 + 'b';
    uint8_t eleFixed = abs(coefficient) % 26 + 'A';
    uint8_t eleFixed2 = abs(coefficient) % 26 + 'C';
    uint8_t eleBytes = abs(coefficient) % 26 + 'A';
    uint8_t eleBytes2 = abs(coefficient) % 26 + 'B';
    GmcNodeT *rootNode = NULL;
    ret = GmcGetRootNode(gStmt, &rootNode);
    ASSERT_EQ(GMERR_OK, ret);

    // 设置所有根节点属性
    int32_t a0 = 10;
    ret = GmcSetVertexProperty(gStmt, "A0", GMC_DATATYPE_INT32, &a0, sizeof(a0));
    ASSERT_EQ(GMERR_OK, ret);
    int64_t a1 = eleNum;
    ret = GmcSetVertexProperty(gStmt, "A1", GMC_DATATYPE_INT64, &a1, sizeof(a1));
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t a2 = eleNum;
    ret = GmcSetVertexProperty(gStmt, "A2", GMC_DATATYPE_UINT32, &a2, sizeof(a2));
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t a3 = eleNum;
    ret = GmcSetVertexProperty(gStmt, "A3", GMC_DATATYPE_UINT64, &a3, sizeof(a3));
    ASSERT_EQ(GMERR_OK, ret);
    float a4 = eleNum;
    ret = GmcSetVertexProperty(gStmt, "A4", GMC_DATATYPE_FLOAT, &a4, sizeof(a4));
    ASSERT_EQ(GMERR_OK, ret);
    double a5 = eleNum;
    ret = GmcSetVertexProperty(gStmt, "A5", GMC_DATATYPE_DOUBLE, &a5, sizeof(a5));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t bits[TYPICAL_FIELD_BITMAP_LEN / 8] = {0};
    GtFillBytes(bits, sizeof(bits), eleBit, false);
    GmcBitMapT a6 = {0};
    a6.beginPos = 0;
    a6.endPos = TYPICAL_FIELD_BITMAP_LEN - 1;
    a6.bits = bits;
    ret = GmcSetVertexProperty(gStmt, "A6", GMC_DATATYPE_BITMAP, &a6, sizeof(a6));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t a7[TYPICAL_FIELD_FIXED_LEN] = {0};
    GtFillBytes(a7, sizeof(a7), eleFixed, false);
    ret = GmcSetVertexProperty(gStmt, "A7", GMC_DATATYPE_FIXED, &a7, sizeof(a7));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t a8[4096] = {0};
    GtFillBytes(a8, sizeof(a8), eleFixed, false);
    ret = GmcSetVertexProperty(gStmt, "A8", GMC_DATATYPE_BYTES, a8, sizeof(a8));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t a9[4096] = {0};
    GtFillBytes(a9, sizeof(a9), eleStr, true);
    ret = GmcSetVertexProperty(gStmt, "A9", GMC_DATATYPE_STRING, a9, strlen((char *)a9));
    ASSERT_EQ(GMERR_OK, ret);

    // 设置子节点属性
    GmcNodeT *childNode = NULL;
    ret = GmcGetChildNode(gStmt, "M0", &childNode);
    ASSERT_EQ(GMERR_OK, ret);
    // uint8_t eleStr = abs(coefficient) % 26 + 'a';
    // uint8_t eleBytes = abs(coefficient) % 26 + 'A';
    for (int i = startMkVal; i < startMkVal + 2; i++) {
        int64_t eleNum = i + coefficient;
        GmcNodeT *elementNode = NULL;
        int ret = GmcNodeAppendElement(childNode, &elementNode);
        ASSERT_EQ(GMERR_OK, ret);
        int32_t b0 = i;
        ret = GmcNodeSetPropertyByName(elementNode, "B0", GMC_DATATYPE_INT32, &b0, sizeof(b0));
        ASSERT_EQ(GMERR_OK, ret);
        uint32_t b1 = eleNum;
        ret = GmcNodeSetPropertyByName(elementNode, "B1", GMC_DATATYPE_UINT32, &b1, sizeof(b1));
        ASSERT_EQ(GMERR_OK, ret);

        uint8_t b2[11312] = {0};
        GtFillBytes(b2, sizeof(b2), eleBytes, false);
        ret = GmcNodeSetPropertyByName(elementNode, "B2", GMC_DATATYPE_BYTES, b2, sizeof(b2));
        ASSERT_EQ(GMERR_OK, ret);
        uint8_t b3[11312] = {0};
        GtFillBytes(b3, sizeof(b3), eleBytes, false);
        ret = GmcNodeSetPropertyByName(elementNode, "B3", GMC_DATATYPE_BYTES, b3, sizeof(b3));
        ASSERT_EQ(GMERR_OK, ret);

        uint8_t b4[512] = {0};
        GtFillBytes(b4, sizeof(b4), eleStr, true);
        ret = GmcNodeSetPropertyByName(elementNode, "B4", GMC_DATATYPE_STRING, b4, strlen((char *)b4));
        ASSERT_EQ(GMERR_OK, ret);
        uint8_t b5[11312] = {0};
        GtFillBytes(b5, sizeof(b5), eleStr, true);
        ret = GmcNodeSetPropertyByName(elementNode, "B5", GMC_DATATYPE_STRING, b5, strlen((char *)b5));
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcExecute(gStmt);
    EXPECT_EQ(GMERR_MEMBER_KEY_VIOLATION, ret);

    a0 = 11;
    ret = GmcSetVertexProperty(gStmt, "A0", GMC_DATATYPE_INT32, &a0, sizeof(a0));
    ASSERT_EQ(GMERR_OK, ret);
    a1 = eleNum2;
    ret = GmcSetVertexProperty(gStmt, "A1", GMC_DATATYPE_INT64, &a1, sizeof(a1));
    ASSERT_EQ(GMERR_OK, ret);
    a2 = eleNum2;
    ret = GmcSetVertexProperty(gStmt, "A2", GMC_DATATYPE_UINT32, &a2, sizeof(a2));
    ASSERT_EQ(GMERR_OK, ret);
    a3 = eleNum2;
    ret = GmcSetVertexProperty(gStmt, "A3", GMC_DATATYPE_UINT64, &a3, sizeof(a3));
    ASSERT_EQ(GMERR_OK, ret);
    a4 = eleNum2;
    ret = GmcSetVertexProperty(gStmt, "A4", GMC_DATATYPE_FLOAT, &a4, sizeof(a4));
    ASSERT_EQ(GMERR_OK, ret);
    a5 = eleNum2;
    ret = GmcSetVertexProperty(gStmt, "A5", GMC_DATATYPE_DOUBLE, &a5, sizeof(a5));
    ASSERT_EQ(GMERR_OK, ret);

    bits[(TYPICAL_FIELD_BITMAP_LEN / 8) - 1] = {0};
    GtFillBytes(bits, sizeof(bits), eleBit2, false);
    a6 = {0};
    a6.beginPos = 0;
    a6.endPos = TYPICAL_FIELD_BITMAP_LEN - 1;
    a6.bits = bits;
    ret = GmcSetVertexProperty(gStmt, "A6", GMC_DATATYPE_BITMAP, &a6, sizeof(a6));
    ASSERT_EQ(GMERR_OK, ret);

    a7[TYPICAL_FIELD_FIXED_LEN - 1] = {0};
    GtFillBytes(a7, sizeof(a7), eleFixed2, false);
    ret = GmcSetVertexProperty(gStmt, "A7", GMC_DATATYPE_FIXED, &a7, sizeof(a7));
    ASSERT_EQ(GMERR_OK, ret);

    a8[4095] = {0};
    GtFillBytes(a8, sizeof(a8), eleFixed2, false);
    ret = GmcSetVertexProperty(gStmt, "A8", GMC_DATATYPE_BYTES, a8, sizeof(a8));
    ASSERT_EQ(GMERR_OK, ret);

    a9[4095] = {0};
    GtFillBytes(a9, sizeof(a9), eleStr2, true);
    ret = GmcSetVertexProperty(gStmt, "A9", GMC_DATATYPE_STRING, a9, strlen((char *)a9));
    ASSERT_EQ(GMERR_OK, ret);

    // 设置子节点属性
    GmcNodeT *childNode2 = NULL;
    ret = GmcGetChildNode(gStmt, "M0", &childNode2);
    ASSERT_EQ(GMERR_OK, ret);

    GmcNodeT *elementNode = NULL;
    ret = GmcNodeAppendElement(childNode2, &elementNode);
    ASSERT_EQ(GMERR_OK, ret);
    int32_t b0 = 888;
    ret = GmcNodeSetPropertyByName(elementNode, "B0", GMC_DATATYPE_INT32, &b0, sizeof(b0));
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t b1 = eleNum2;
    ret = GmcNodeSetPropertyByName(elementNode, "B1", GMC_DATATYPE_UINT32, &b1, sizeof(b1));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t b2[11312] = {0};
    GtFillBytes(b2, sizeof(b2), eleBytes2, false);
    ret = GmcNodeSetPropertyByName(elementNode, "B2", GMC_DATATYPE_BYTES, b2, sizeof(b2));
    ASSERT_EQ(GMERR_OK, ret);
    uint8_t b3[11312] = {0};
    GtFillBytes(b3, sizeof(b3), eleBytes2, false);
    ret = GmcNodeSetPropertyByName(elementNode, "B3", GMC_DATATYPE_BYTES, b3, sizeof(b3));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t b4[512] = {0};
    GtFillBytes(b4, sizeof(b4), eleStr2, true);
    ret = GmcNodeSetPropertyByName(elementNode, "B4", GMC_DATATYPE_STRING, b4, strlen((char *)b4));
    ASSERT_EQ(GMERR_OK, ret);
    uint8_t b5[11312] = {0};
    GtFillBytes(b5, sizeof(b5), eleStr2, true);
    ret = GmcNodeSetPropertyByName(elementNode, "B5", GMC_DATATYPE_STRING, b5, strlen((char *)b5));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(gStmt);
    EXPECT_EQ(GMERR_MEMBER_KEY_VIOLATION, ret);
    if (ret != 0) {
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
}
//主键写不同数据，member key 插入不相同数据成功
TEST_F(V3_trans_big_object, DML_077_V3_001_017)
{
    GtTypicalVertexCfgT vertexCfg = {10, 2, 10, 1, 0};
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t startMkVal = vertexCfg.startMkVal;
    uint32_t childCount = vertexCfg.childCount;
    int32_t coefficient = vertexCfg.coefficient;

    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        int ret = testGmcPrepareStmtByLabelName(gStmt, gLabelTypicalName4, GMC_OPERATION_REPLACE);
        ASSERT_EQ(GMERR_OK, ret);

        GmcNodeT *rootNode = NULL;
        ret = GmcGetRootNode(gStmt, &rootNode);
        ASSERT_EQ(GMERR_OK, ret);

        // 设置所有根节点属性
        int32_t a0 = i;
        ret = GmcSetVertexProperty(gStmt, "A0", GMC_DATATYPE_INT32, &a0, sizeof(a0));
        ASSERT_EQ(GMERR_OK, ret);

        // 设置非主键属性
        int64_t eleNum = i + coefficient;
        uint8_t eleBit = abs(coefficient) % 255;
        uint8_t eleStr = abs(coefficient) % 26 + 'a';
        uint8_t eleStr2 = abs(coefficient) % 26 + 'b';
        uint8_t eleStr3 = abs(coefficient) % 26 + 'c';
        uint8_t eleStr4 = abs(coefficient) % 26 + 'd';
        uint8_t elestr_num[4] = {eleStr, eleStr2, eleStr3, eleStr4};
        uint8_t eleFixed = abs(coefficient) % 26 + 'A';
        uint8_t eleBytes = abs(coefficient) % 26 + 'A';

        int64_t a1 = eleNum;
        ret = GmcSetVertexProperty(gStmt, "A1", GMC_DATATYPE_INT64, &a1, sizeof(a1));
        ASSERT_EQ(GMERR_OK, ret);
        uint32_t a2 = eleNum;
        ret = GmcSetVertexProperty(gStmt, "A2", GMC_DATATYPE_UINT32, &a2, sizeof(a2));
        ASSERT_EQ(GMERR_OK, ret);
        uint64_t a3 = eleNum;
        ret = GmcSetVertexProperty(gStmt, "A3", GMC_DATATYPE_UINT64, &a3, sizeof(a3));
        ASSERT_EQ(GMERR_OK, ret);
        float a4 = eleNum;
        ret = GmcSetVertexProperty(gStmt, "A4", GMC_DATATYPE_FLOAT, &a4, sizeof(a4));
        ASSERT_EQ(GMERR_OK, ret);
        double a5 = eleNum;
        ret = GmcSetVertexProperty(gStmt, "A5", GMC_DATATYPE_DOUBLE, &a5, sizeof(a5));
        ASSERT_EQ(GMERR_OK, ret);

        uint8_t bits[TYPICAL_FIELD_BITMAP_LEN / 8] = {0};
        GtFillBytes(bits, sizeof(bits), eleBit, false);
        GmcBitMapT a6 = {0};
        a6.beginPos = 0;
        a6.endPos = TYPICAL_FIELD_BITMAP_LEN - 1;
        a6.bits = bits;
        ret = GmcSetVertexProperty(gStmt, "A6", GMC_DATATYPE_BITMAP, &a6, sizeof(a6));
        ASSERT_EQ(GMERR_OK, ret);

        uint8_t a7[TYPICAL_FIELD_FIXED_LEN] = {0};
        GtFillBytes(a7, sizeof(a7), eleFixed, false);
        ret = GmcSetVertexProperty(gStmt, "A7", GMC_DATATYPE_FIXED, &a7, sizeof(a7));
        ASSERT_EQ(GMERR_OK, ret);

        uint8_t a8[4096] = {0};
        GtFillBytes(a8, sizeof(a8), eleFixed, false);
        ret = GmcSetVertexProperty(gStmt, "A8", GMC_DATATYPE_BYTES, a8, sizeof(a8));
        ASSERT_EQ(GMERR_OK, ret);

        uint8_t a9[4096] = {0};
        GtFillBytes(a9, sizeof(a9), eleStr, true);
        ret = GmcSetVertexProperty(gStmt, "A9", GMC_DATATYPE_STRING, a9, strlen((char *)a9));
        ASSERT_EQ(GMERR_OK, ret);

        // 设置子节点属性
        GmcNodeT *childNode = NULL;
        ret = GmcGetChildNode(gStmt, "M0", &childNode);
        ASSERT_EQ(GMERR_OK, ret);
        // uint8_t eleStr = abs(coefficient) % 26 + 'a';
        // uint8_t eleBytes = abs(coefficient) % 26 + 'A';
        GmcNodeT *elementNode = NULL;
        ret = GmcNodeAppendElement(childNode, &elementNode);
        ASSERT_EQ(GMERR_OK, ret);

        int32_t b0 = i;
        ret = GmcNodeSetPropertyByName(elementNode, "B0", GMC_DATATYPE_INT32, &b0, sizeof(b0));
        ASSERT_EQ(GMERR_OK, ret);
        uint32_t b1 = eleNum;
        ret = GmcNodeSetPropertyByName(elementNode, "B1", GMC_DATATYPE_UINT32, &b1, sizeof(b1));
        ASSERT_EQ(GMERR_OK, ret);

        uint8_t b2[11312] = {0};
        GtFillBytes(b2, sizeof(b2), eleBytes, false);
        ret = GmcNodeSetPropertyByName(elementNode, "B2", GMC_DATATYPE_BYTES, b2, sizeof(b2));
        ASSERT_EQ(GMERR_OK, ret);
        uint8_t b3[11312] = {0};
        GtFillBytes(b3, sizeof(b3), eleBytes, false);
        ret = GmcNodeSetPropertyByName(elementNode, "B3", GMC_DATATYPE_BYTES, b3, sizeof(b3));
        ASSERT_EQ(GMERR_OK, ret);

        uint8_t b4[512] = {0};
        GtFillBytes(b4, sizeof(b4), elestr_num[0], true);
        ret = GmcNodeSetPropertyByName(elementNode, "B4", GMC_DATATYPE_STRING, b4, strlen((char *)b4));
        ASSERT_EQ(GMERR_OK, ret);
        uint8_t b5[11312] = {0};
        GtFillBytes(b5, sizeof(b5), eleStr, true);
        ret = GmcNodeSetPropertyByName(elementNode, "B5", GMC_DATATYPE_STRING, b5, strlen((char *)b5));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcExecute(gStmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
}
//主键写不同数据，member key插入相同数据失败
TEST_F(V3_trans_big_object, DML_077_V3_001_018)
{
    GtTypicalVertexCfgT vertexCfg = {10, 2, 10, 1, 0};
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t startMkVal = vertexCfg.startMkVal;
    uint32_t childCount = vertexCfg.childCount;
    int32_t coefficient = vertexCfg.coefficient;

    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        int ret = testGmcPrepareStmtByLabelName(gStmt, gLabelTypicalName4, GMC_OPERATION_REPLACE);
        ASSERT_EQ(GMERR_OK, ret);

        GmcNodeT *rootNode = NULL;
        ret = GmcGetRootNode(gStmt, &rootNode);
        ASSERT_EQ(GMERR_OK, ret);

        // 设置所有根节点属性
        int32_t a0 = i;
        ret = GmcSetVertexProperty(gStmt, "A0", GMC_DATATYPE_INT32, &a0, sizeof(a0));
        ASSERT_EQ(GMERR_OK, ret);

        // 设置非主键属性
        int64_t eleNum = i + coefficient;
        uint8_t eleBit = abs(coefficient) % 255;
        uint8_t eleStr = abs(coefficient) % 26 + 'a';
        uint8_t eleStr2 = abs(coefficient) % 26 + 'b';
        uint8_t eleStr3 = abs(coefficient) % 26 + 'c';
        uint8_t eleStr4 = abs(coefficient) % 26 + 'd';
        uint8_t elestr_num[4] = {eleStr, eleStr2, eleStr3, eleStr4};
        uint8_t eleFixed = abs(coefficient) % 26 + 'A';
        uint8_t eleBytes = abs(coefficient) % 26 + 'A';

        int64_t a1 = eleNum;
        ret = GmcSetVertexProperty(gStmt, "A1", GMC_DATATYPE_INT64, &a1, sizeof(a1));
        ASSERT_EQ(GMERR_OK, ret);
        uint32_t a2 = eleNum;
        ret = GmcSetVertexProperty(gStmt, "A2", GMC_DATATYPE_UINT32, &a2, sizeof(a2));
        ASSERT_EQ(GMERR_OK, ret);
        uint64_t a3 = eleNum;
        ret = GmcSetVertexProperty(gStmt, "A3", GMC_DATATYPE_UINT64, &a3, sizeof(a3));
        ASSERT_EQ(GMERR_OK, ret);
        float a4 = eleNum;
        ret = GmcSetVertexProperty(gStmt, "A4", GMC_DATATYPE_FLOAT, &a4, sizeof(a4));
        ASSERT_EQ(GMERR_OK, ret);
        double a5 = eleNum;
        ret = GmcSetVertexProperty(gStmt, "A5", GMC_DATATYPE_DOUBLE, &a5, sizeof(a5));
        ASSERT_EQ(GMERR_OK, ret);

        uint8_t bits[TYPICAL_FIELD_BITMAP_LEN / 8] = {0};
        GtFillBytes(bits, sizeof(bits), eleBit, false);
        GmcBitMapT a6 = {0};
        a6.beginPos = 0;
        a6.endPos = TYPICAL_FIELD_BITMAP_LEN - 1;
        a6.bits = bits;
        ret = GmcSetVertexProperty(gStmt, "A6", GMC_DATATYPE_BITMAP, &a6, sizeof(a6));
        ASSERT_EQ(GMERR_OK, ret);

        uint8_t a7[TYPICAL_FIELD_FIXED_LEN] = {0};
        GtFillBytes(a7, sizeof(a7), eleFixed, false);
        ret = GmcSetVertexProperty(gStmt, "A7", GMC_DATATYPE_FIXED, &a7, sizeof(a7));
        ASSERT_EQ(GMERR_OK, ret);

        uint8_t a8[4096] = {0};
        GtFillBytes(a8, sizeof(a8), eleFixed, false);
        ret = GmcSetVertexProperty(gStmt, "A8", GMC_DATATYPE_BYTES, a8, sizeof(a8));
        ASSERT_EQ(GMERR_OK, ret);

        uint8_t a9[4096] = {0};
        GtFillBytes(a9, sizeof(a9), eleStr, true);
        ret = GmcSetVertexProperty(gStmt, "A9", GMC_DATATYPE_STRING, a9, strlen((char *)a9));
        ASSERT_EQ(GMERR_OK, ret);

        // 设置子节点属性
        GmcNodeT *childNode = NULL;
        ret = GmcGetChildNode(gStmt, "M0", &childNode);
        ASSERT_EQ(GMERR_OK, ret);
        for (int i = 0; i < 2; i++) {
            GmcNodeT *elementNode = NULL;
            ret = GmcNodeAppendElement(childNode, &elementNode);
            ASSERT_EQ(GMERR_OK, ret);

            int32_t b0 = i;
            ret = GmcNodeSetPropertyByName(elementNode, "B0", GMC_DATATYPE_INT32, &b0, sizeof(b0));
            ASSERT_EQ(GMERR_OK, ret);
            uint32_t b1 = eleNum;
            ret = GmcNodeSetPropertyByName(elementNode, "B1", GMC_DATATYPE_UINT32, &b1, sizeof(b1));
            ASSERT_EQ(GMERR_OK, ret);

            uint8_t b2[11312] = {0};
            GtFillBytes(b2, sizeof(b2), eleBytes, false);
            ret = GmcNodeSetPropertyByName(elementNode, "B2", GMC_DATATYPE_BYTES, b2, sizeof(b2));
            ASSERT_EQ(GMERR_OK, ret);
            uint8_t b3[11312] = {0};
            GtFillBytes(b3, sizeof(b3), eleBytes, false);
            ret = GmcNodeSetPropertyByName(elementNode, "B3", GMC_DATATYPE_BYTES, b3, sizeof(b3));
            ASSERT_EQ(GMERR_OK, ret);

            uint8_t b4[512] = {0};
            GtFillBytes(b4, sizeof(b4), elestr_num[0], true);
            ret = GmcNodeSetPropertyByName(elementNode, "B4", GMC_DATATYPE_STRING, b4, strlen((char *)b4));
            ASSERT_EQ(GMERR_OK, ret);
            uint8_t b5[11312] = {0};
            GtFillBytes(b5, sizeof(b5), eleStr, true);
            ret = GmcNodeSetPropertyByName(elementNode, "B5", GMC_DATATYPE_STRING, b5, strlen((char *)b5));
            ASSERT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(gStmt);
        EXPECT_EQ(GMERR_MEMBER_KEY_VIOLATION, ret);
    }
}

// tree表写32k对象写满db内存后删除数据再次写满，比较2次记录数
TEST_F(V3_trans_big_object, DML_077_V3_001_019)
{
    char g_errorCode[1024] = {0};
    (void)snprintf(g_errorCode, 1024, "GMERR-%d", GMERR_OUT_OF_MEMORY);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode);
    GtTypicalVertexCfgT gVertexCfg = {RECORD_NUM_001/10000, RECORD_NUM_001, RECORD_NUM_001/10000,
        RECORD_NUM_001/10000*6, 0};
    GtSetTypicalFieldLenth(128, 128);
    uint64_t count;

    int ret = GtInsertTypicalVertex(gStmt, gVertexCfg);
    ASSERT_EQ(GMERR_OUT_OF_MEMORY, ret);
    ret = GmcGetVertexCount(gStmt, gLabelTypicalName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GtDeleteTypicalVertexByIndex(gStmt, gVertexCfg, "PrimaryKey");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GtInsertTypicalVertex(gStmt, gVertexCfg);
    ASSERT_EQ(GMERR_OUT_OF_MEMORY, ret);
    uint64_t count2;
    ret = GmcGetVertexCount(gStmt, gLabelTypicalName, NULL, &count2);
    ASSERT_EQ(GMERR_OK, ret);
    printf("%d,%d\n", count, count2);
    //    ASSERT_EQ(count2, count);
    int value = abs((int)count2 - (int)count);
    ASSERT_LT(value, 100);
}
// tree表多个字段写64k对象写满db内存后删除数据再次写满，比较2次记录数[错误码保持注意]
TEST_F(V3_trans_big_object, DML_077_V3_001_020)
{
    char g_errorCode[1024] = {0};
    (void)snprintf(g_errorCode, 1024, "GMERR-%d", GMERR_OUT_OF_MEMORY);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode);
    GtTypicalVertexCfgT gVertexCfg = {RECORD_NUM_001/10000, RECORD_NUM_001, RECORD_NUM_001/10000,
        RECORD_NUM_001/10000*6, 0};
    GtSetTypicalFieldLenth(256, 256);
    uint64_t count;

    int ret = GtInsertTypicalVertex(gStmt, gVertexCfg);
    ASSERT_EQ(GMERR_OUT_OF_MEMORY, ret);
    ret = GmcGetVertexCount(gStmt, gLabelTypicalName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GtDeleteTypicalVertexByIndex(gStmt, gVertexCfg, "PrimaryKey");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GtInsertTypicalVertex(gStmt, gVertexCfg);
    ASSERT_EQ(GMERR_OUT_OF_MEMORY, ret);
    uint64_t count2;
    ret = GmcGetVertexCount(gStmt, gLabelTypicalName, NULL, &count2);
    ASSERT_EQ(GMERR_OK, ret);
    printf("%d,%d\n", count, count2);
    // ASSERT_EQ(count2, count);
    int value = abs((int)count2 - (int)count);
    ASSERT_LT(value, 100);
}

// tree表写128k对象写满db内存后删除数据再次写满，比较2次记录数
TEST_F(V3_trans_big_object, DML_077_V3_001_021)
{
    char g_errorCode[1024] = {0};
    (void)snprintf(g_errorCode, 1024, "GMERR-%d", GMERR_OUT_OF_MEMORY);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode);
    GtTypicalVertexCfgT gVertexCfg = {RECORD_NUM_001/10000, RECORD_NUM_001, RECORD_NUM_001/10000,
        RECORD_NUM_001/10000*6, 0};
    GtSetTypicalFieldLenth(512, 256);
    uint64_t count;

    int ret = GtInsertTypicalVertex(gStmt, gVertexCfg);
    ASSERT_EQ(GMERR_OUT_OF_MEMORY, ret);
    ret = GmcGetVertexCount(gStmt, gLabelTypicalName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GtDeleteTypicalVertexByIndex(gStmt, gVertexCfg, "PrimaryKey");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GtInsertTypicalVertex(gStmt, gVertexCfg);
    ASSERT_EQ(GMERR_OUT_OF_MEMORY, ret);
    uint64_t count2;
    ret = GmcGetVertexCount(gStmt, gLabelTypicalName, NULL, &count2);
    ASSERT_EQ(GMERR_OK, ret);
    printf("%d,%d\n", count, count2);
    // ASSERT_EQ(count2, count);
    int value = abs((int)count2 - (int)count);
    ASSERT_LT(value, 100);
}
// tree表写256k对象写满db内存后删除数据再次写满，比较2次记录数
TEST_F(V3_trans_big_object, DML_077_V3_001_022)
{
    char g_errorCode[1024] = {0};
    (void)snprintf(g_errorCode, 1024, "GMERR-%d", GMERR_OUT_OF_MEMORY);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode);
    GtTypicalVertexCfgT gVertexCfg = {RECORD_NUM_001/10000, RECORD_NUM_001, RECORD_NUM_001/10000,
        RECORD_NUM_001/10000*6, 0};
    GtSetTypicalFieldLenth(1024, 1024);
    uint64_t count;

    int ret = GtInsertTypicalVertex(gStmt, gVertexCfg);
    ASSERT_EQ(GMERR_OUT_OF_MEMORY, ret);
    ret = GmcGetVertexCount(gStmt, gLabelTypicalName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GtDeleteTypicalVertexByIndex(gStmt, gVertexCfg, "PrimaryKey");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GtInsertTypicalVertex(gStmt, gVertexCfg);
    ASSERT_EQ(GMERR_OUT_OF_MEMORY, ret);
    uint64_t count2;
    ret = GmcGetVertexCount(gStmt, gLabelTypicalName, NULL, &count2);
    ASSERT_EQ(GMERR_OK, ret);
    printf("%d,%d\n", count, count2);
    ASSERT_EQ(count2, count);
}
// tree表写512k对象写满db内存后删除数据再次写满，比较2次记录数
TEST_F(V3_trans_big_object, DML_077_V3_001_023)
{
    char g_errorCode[1024] = {0};
    (void)snprintf(g_errorCode, 1024, "GMERR-%d", GMERR_OUT_OF_MEMORY);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode);
    GtTypicalVertexCfgT gVertexCfg = {10, 100000, 10, 60, 0};
    GtSetTypicalFieldLenth(2 * 1024, 2 * 1024);
    uint64_t count;

    int ret = GtInsertTypicalVertex(gStmt, gVertexCfg);
    ASSERT_EQ(GMERR_OUT_OF_MEMORY, ret);
    ret = GmcGetVertexCount(gStmt, gLabelTypicalName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GtDeleteTypicalVertexByIndex(gStmt, gVertexCfg, "PrimaryKey");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GtInsertTypicalVertex(gStmt, gVertexCfg);
    ASSERT_EQ(GMERR_OUT_OF_MEMORY, ret);
    uint64_t count2;
    ret = GmcGetVertexCount(gStmt, gLabelTypicalName, NULL, &count2);
    ASSERT_EQ(GMERR_OK, ret);
    printf("%d,%d\n", count, count2);
    ASSERT_EQ(count2, count);
}
// tree表写1024k对象写满db内存后删除数据再次写满，比较2次记录数
TEST_F(V3_trans_big_object, DML_077_V3_001_024)
{
    char g_errorCode[1024] = {0};
    (void)snprintf(g_errorCode, 1024, "GMERR-%d", GMERR_OUT_OF_MEMORY);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode);
    GtTypicalVertexCfgT gVertexCfg = {10, 100000, 10, 60, 0};
    GtSetTypicalFieldLenth(4 * 1024, 4 * 1024);
    uint64_t count;

    int ret = GtInsertTypicalVertex(gStmt, gVertexCfg);
    ASSERT_EQ(GMERR_OUT_OF_MEMORY, ret);
    ret = GmcGetVertexCount(gStmt, gLabelTypicalName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GtDeleteTypicalVertexByIndex(gStmt, gVertexCfg, "PrimaryKey");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GtInsertTypicalVertex(gStmt, gVertexCfg);
    ASSERT_EQ(GMERR_OUT_OF_MEMORY, ret);
    uint64_t count2;
    ret = GmcGetVertexCount(gStmt, gLabelTypicalName, NULL, &count2);
    ASSERT_EQ(GMERR_OK, ret);
    printf("%d,%d\n", count, count2);
    ASSERT_EQ(count2, count);
}
// bitmapsize同步建表失败，长度不合法
TEST_F(V3_trans_big_object, DML_077_V3_001_025)
{
    int ret = 0;
    ret = GtCreateVertexLabel(gStmt, gLabelTypicalJsonPath7, gConfigJsonPath);
    EXPECT_EQ(GMERR_INVALID_JSON_CONTENT, ret);
    if (ret != 0) {
        testGmcGetLastError(NULL);
    }
    GmcDropVertexLabel(gStmt, gLabelTypicalName7);
}
