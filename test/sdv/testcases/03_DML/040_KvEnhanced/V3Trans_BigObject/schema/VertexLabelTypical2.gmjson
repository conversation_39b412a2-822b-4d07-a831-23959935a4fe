[{"comment": "典型的vertex label2, 覆盖常见字段和索引, 适用于通用测试场景", "version": "3.0", "type": "record", "name": "TypicalVertexLabel2", "fields": [{"name": "A0", "type": "int32", "nullable": false}, {"name": "A1", "type": "int64", "nullable": false}, {"name": "A2", "type": "uint32", "nullable": false}, {"name": "A3", "type": "uint64", "nullable": false}, {"name": "A4", "type": "float", "nullable": true}, {"name": "A5", "type": "double", "nullable": true}, {"name": "A6", "type": "bitmap", "size": 16, "nullable": true}, {"name": "A7", "type": "fixed", "size": 16, "nullable": true}, {"name": "A8", "type": "bytes", "nullable": true}, {"name": "A9", "type": "string", "nullable": true}, {"name": "M0", "type": "record", "array": true, "size": 1024, "fields": [{"name": "B0", "type": "int32", "nullable": true}, {"name": "B1", "type": "uint32", "nullable": true}, {"name": "B2", "type": "bytes", "nullable": true}, {"name": "B3", "type": "bytes", "nullable": true}, {"name": "B4", "type": "string", "nullable": true}, {"name": "B5", "type": "string", "nullable": true}]}], "super_fields": [{"name": "SuperFiled", "id": 0, "fields": {"begin": "A1", "end": "A7"}}], "keys": [{"node": "TypicalVertexLabel2", "name": "<PERSON><PERSON><PERSON>", "index": {"type": "primary"}, "fields": ["A0"], "constraints": {"unique": true}, "comment": "主键索引"}, {"node": "TypicalVertexLabel2", "name": "LocalKey", "index": {"type": "local"}, "fields": ["A2", "A3"], "constraints": {"unique": false}, "comment": "local索引"}, {"node": "TypicalVertexLabel2", "name": "LocalHashKey", "index": {"type": "<PERSON><PERSON><PERSON>"}, "fields": ["A3"], "constraints": {"unique": false}, "comment": "localhash索引"}, {"node": "TypicalVertexLabel2", "name": "HashClusterKey", "index": {"type": "hashcluster"}, "fields": ["A3"], "constraints": {"unique": false}, "comment": "hashcluster索引"}, {"node": "M0", "name": "M0MemberKey", "fields": ["B0"], "constraints": {"unique": true}, "comment": "M0节点成员索引"}]}]