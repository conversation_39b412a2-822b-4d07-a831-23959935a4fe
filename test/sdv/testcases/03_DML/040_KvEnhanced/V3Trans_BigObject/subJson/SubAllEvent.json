{"comment": "VertexLabel 订阅所有事件", "label_name": "TypicalVertexLabel", "events": [{"type": "insert", "msgTypes": ["new object", "old object"]}, {"type": "update", "msgTypes": ["new object", "old object"]}, {"type": "delete", "msgTypes": ["new object", "old object"]}, {"type": "replace", "msgTypes": ["new object", "old object"]}, {"type": "age", "msgTypes": ["new object", "old object"]}], "is_path": false, "is_reliable": true, "retry": false, "persist": true, "constraint": {"operator_type": "or", "conditions": [{"property": "A1"}, {"property": "A2"}]}}