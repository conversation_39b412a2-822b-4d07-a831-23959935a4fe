#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <time.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"

//全局变量
GmcConnT *conn, *g_conn_sub = NULL;
;                                    //键连句柄
GmcStmtT *stmt, *g_stmt_sub = NULL;  // stmt操作句柄

#define MAX_NAME_LENGTH 128
int g_end_num = 100;

void *label = NULL;
int start_num = 1;
int end_num = 10;
int array_num = 3;
int vector_num = 3;

int ret;

char g_label_config[] = "{\"max_record_count\":10000}";
class DML_060_001 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh");
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
    }

public:
    SnUserDataT *user_data;
    int *newValue;
    int *oldValue;
    virtual void SetUp();
    virtual void TearDown();
};
void DML_060_001::SetUp()
{
    //创建同步连接
    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}
void DML_060_001::TearDown()
{
    AW_CHECK_LOG_END();
    //客户端断链
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

void sn_callback(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret, index, i, array_num = 3, vector_num = 3;
    bool bool_value;
    char f14_value[256] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }

        for (i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(strlen(labelName), labelNameLen);

            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    //读new
                    bool_value = 0;
                    strcpy(f14_value, "string1");
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    printf("[INFO] GMC_SUB_EVENT_INSERT new_value is %d\r\n", index);
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    //读old
                    bool_value = 1;
                    strcpy(f14_value, "1234567");
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->old_value)[user_data->subIndex];
                    printf("[INFO] GMC_SUB_EVENT_DELETE old_value is %d\r\n", index);
                    break;
                }
                case GMC_SUB_EVENT_UPDATE: {
                    //读new
                    bool_value = 1;
                    strcpy(f14_value, "1234567");
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    printf("[INFO] GMC_SUB_EVENT_UPDATE new_value is %d\r\n", index);
                    break;
                }
                case GMC_SUB_EVENT_REPLACE: {
                    //读new
                    if (((bool *)user_data->isReplace_insert)[user_data->subIndex]) {
                        bool_value = 0;
                        strcpy(f14_value, "string1");
                    } else {
                        bool_value = 1;
                        strcpy(f14_value, "1234567");
                    }
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    printf("[INFO] GMC_SUB_EVENT_REPLACE new_value is %d\r\n", index);
                    break;
                }
                default: {
                    printf("default: invalid eventType\r\n");
                    break;
                }
            }
        }
        user_data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
        }
    }
}
void TestGmcNodeSetPropertyByName_PK(GmcNodeT *root, int i)
{
    int ret = 0;
    int64_t f0_value = i;
    ret = GmcNodeSetPropertyByName(root, "F0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
}
void TestGmcNodeSetPropertyByName_R(GmcNodeT *root, int i, bool bool_value, char *f21_value)
{
    int ret = 0;
    int8_t value_8 = i % 128;
    uint8_t value_u8 = i % 256;
    int16_t value_16 = i % 32768;
    uint16_t value_u16 = i % 65566;
    char f1_value = 'a';
    ret = GmcNodeSetPropertyByName(root, "F1", GMC_DATATYPE_CHAR, &f1_value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);

    unsigned char f2_value = 'b';
    ret = GmcNodeSetPropertyByName(root, "F2", GMC_DATATYPE_UCHAR, &f2_value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);

    int8_t f3_value = value_8;
    ret = GmcNodeSetPropertyByName(root, "F3", GMC_DATATYPE_INT8, &f3_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t f4_value = value_u8;
    ret = GmcNodeSetPropertyByName(root, "F4", GMC_DATATYPE_UINT8, &f4_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    int16_t f5_value = value_16;
    ret = GmcNodeSetPropertyByName(root, "F5", GMC_DATATYPE_INT16, &f5_value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint16_t f6_value = value_u16;
    ret = GmcNodeSetPropertyByName(root, "F6", GMC_DATATYPE_UINT16, &f6_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    int32_t f7_value = 7 * i;
    ret = GmcNodeSetPropertyByName(root, "F7", GMC_DATATYPE_INT32, &f7_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t f8_value = 8 * i;
    ret = GmcNodeSetPropertyByName(root, "F8", GMC_DATATYPE_UINT32, &f8_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    int64_t f9_value = 9 * i;
    ret = GmcNodeSetPropertyByName(root, "F9", GMC_DATATYPE_INT64, &f9_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f10_value = 10 * i;
    ret = GmcNodeSetPropertyByName(root, "F10", GMC_DATATYPE_UINT64, &f10_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    bool f11_value = bool_value;
    ret = GmcNodeSetPropertyByName(root, "F11", GMC_DATATYPE_BOOL, &f11_value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);

    float f12_value = 12 * i;
    ret = GmcNodeSetPropertyByName(root, "F12", GMC_DATATYPE_FLOAT, &f12_value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);

    double f13_value = 13 * i;
    ret = GmcNodeSetPropertyByName(root, "F13", GMC_DATATYPE_DOUBLE, &f13_value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);

    // f14  类型为resource

    uint64_t f15_value = 1000;
    ret = GmcNodeSetPropertyByName(root, "F15", GMC_DATATYPE_TIME, &f15_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t f16_value = 1;
    ret = GmcNodeSetPropertyByName(root, "F16", GMC_DATATYPE_BITFIELD8, &f16_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint16_t f17_value = 2;
    ret = GmcNodeSetPropertyByName(root, "F17", GMC_DATATYPE_BITFIELD16, &f17_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t f18_value = 2;
    ret = GmcNodeSetPropertyByName(root, "F18", GMC_DATATYPE_BITFIELD32, &f18_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    int64_t f19_value = 2;
    ret = GmcNodeSetPropertyByName(root, "F19", GMC_DATATYPE_BITFIELD64, &f19_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);

    // f20 类型为partion

    ret = GmcNodeSetPropertyByName(root, "F21", GMC_DATATYPE_STRING, f21_value, (strlen(f21_value)));
    ASSERT_EQ(GMERR_OK, ret);

    char f22_value[] = "222222";
    ret = GmcNodeSetPropertyByName(root, "F22", GMC_DATATYPE_BYTES, f22_value, 7);
    EXPECT_EQ(GMERR_OK, ret);
    char f23_value[] = "232222";
    ret = GmcNodeSetPropertyByName(root, "F23", GMC_DATATYPE_FIXED, f23_value, 7);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBitMapT bitMap = {0, 127, NULL};
    uint8_t bits[128 / 8];
    memset(bits, 0xffff, 128 / 8);
    bits[128 / 8 - 1] = '\0';
    bitMap.bits = bits;
    ret = GmcNodeSetPropertyByName(root, "F24", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
}
void TestGmcNodeSetPropertyByName_P(GmcNodeT *T1, int i, bool bool_value, char *f21_value)
{
    int ret = 0;
    int8_t value_8 = i % 128;
    uint8_t value_u8 = i % 256;
    int16_t value_16 = i % 32768;
    uint16_t value_u16 = i % 65566;

    char f1_value = 'a';
    ret = GmcNodeSetPropertyByName(T1, "P1", GMC_DATATYPE_CHAR, &f1_value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);

    unsigned char f2_value = 'b';
    ret = GmcNodeSetPropertyByName(T1, "P2", GMC_DATATYPE_UCHAR, &f2_value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);

    int8_t f3_value = value_8;
    ret = GmcNodeSetPropertyByName(T1, "P3", GMC_DATATYPE_INT8, &f3_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t f4_value = value_u8;
    ret = GmcNodeSetPropertyByName(T1, "P4", GMC_DATATYPE_UINT8, &f4_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    int16_t f5_value = value_16;
    ret = GmcNodeSetPropertyByName(T1, "P5", GMC_DATATYPE_INT16, &f5_value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint16_t f6_value = value_u16;
    ret = GmcNodeSetPropertyByName(T1, "P6", GMC_DATATYPE_UINT16, &f6_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    int32_t f7_value = 7 * i;
    ret = GmcNodeSetPropertyByName(T1, "P7", GMC_DATATYPE_INT32, &f7_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t f8_value = 8 * i;
    ret = GmcNodeSetPropertyByName(T1, "P8", GMC_DATATYPE_UINT32, &f8_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    int64_t f9_value = 9 * i;
    ret = GmcNodeSetPropertyByName(T1, "P9", GMC_DATATYPE_INT64, &f9_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f10_value = 10 * i;
    ret = GmcNodeSetPropertyByName(T1, "P10", GMC_DATATYPE_UINT64, &f10_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    bool f11_value = bool_value;
    ret = GmcNodeSetPropertyByName(T1, "P11", GMC_DATATYPE_BOOL, &f11_value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);

    float f12_value = 12 * i;
    ret = GmcNodeSetPropertyByName(T1, "P12", GMC_DATATYPE_FLOAT, &f12_value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);

    double f13_value = 13 * i;
    ret = GmcNodeSetPropertyByName(T1, "P13", GMC_DATATYPE_DOUBLE, &f13_value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);

    // f14  类型为resource

    uint64_t f15_value = 1000;
    ret = GmcNodeSetPropertyByName(T1, "P15", GMC_DATATYPE_TIME, &f15_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t f16_value = 1;
    ret = GmcNodeSetPropertyByName(T1, "P16", GMC_DATATYPE_BITFIELD8, &f16_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint16_t f17_value = 2;
    ret = GmcNodeSetPropertyByName(T1, "P17", GMC_DATATYPE_BITFIELD16, &f17_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t f18_value = 2;
    ret = GmcNodeSetPropertyByName(T1, "P18", GMC_DATATYPE_BITFIELD32, &f18_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    int64_t f19_value = 2;
    ret = GmcNodeSetPropertyByName(T1, "P19", GMC_DATATYPE_BITFIELD64, &f19_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);

    // f20 类型为partion

    ret = GmcNodeSetPropertyByName(T1, "P21", GMC_DATATYPE_STRING, f21_value, (strlen(f21_value)));
    ASSERT_EQ(GMERR_OK, ret);

    char f22_value[] = "222222";
    ret = GmcNodeSetPropertyByName(T1, "P22", GMC_DATATYPE_BYTES, f22_value, 7);
    EXPECT_EQ(GMERR_OK, ret);
    char f23_value[] = "232222";
    ret = GmcNodeSetPropertyByName(T1, "P23", GMC_DATATYPE_FIXED, f23_value, 7);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBitMapT bitMap = {0, 127, NULL};
    uint8_t bits[128 / 8];
    memset(bits, 0xffff, 128 / 8);
    bits[128 / 8 - 1] = '\0';
    bitMap.bits = bits;
    ret = GmcNodeSetPropertyByName(T1, "P24", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
    EXPECT_EQ(GMERR_OK, ret);
}
void TestGmcNodeSetPropertyByName_PA(GmcNodeT *T2, int i)
{
    int ret = 0;
    int64_t A0_value = i;
    ret = GmcNodeSetPropertyByName(T2, "A0", GMC_DATATYPE_INT64, &A0_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
}
void TestGmcNodeSetPropertyByName_A(GmcNodeT *T2, int i, bool bool_value, char *f21_value)
{
    int ret = 0;
    int8_t value_8 = i % 128;
    uint8_t value_u8 = i % 256;
    int16_t value_16 = i % 32768;
    uint16_t value_u16 = i % 65566;

    char f1_value = 'a';
    ret = GmcNodeSetPropertyByName(T2, "A1", GMC_DATATYPE_CHAR, &f1_value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);

    unsigned char f2_value = 'b';
    ret = GmcNodeSetPropertyByName(T2, "A2", GMC_DATATYPE_UCHAR, &f2_value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);

    int8_t f3_value = value_8;
    ret = GmcNodeSetPropertyByName(T2, "A3", GMC_DATATYPE_INT8, &f3_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t f4_value = value_u8;
    ret = GmcNodeSetPropertyByName(T2, "A4", GMC_DATATYPE_UINT8, &f4_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    int16_t f5_value = value_16;
    ret = GmcNodeSetPropertyByName(T2, "A5", GMC_DATATYPE_INT16, &f5_value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint16_t f6_value = value_u16;
    ret = GmcNodeSetPropertyByName(T2, "A6", GMC_DATATYPE_UINT16, &f6_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    int32_t f7_value = 7 * i;
    ret = GmcNodeSetPropertyByName(T2, "A7", GMC_DATATYPE_INT32, &f7_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t f8_value = 8 * i;
    ret = GmcNodeSetPropertyByName(T2, "A8", GMC_DATATYPE_UINT32, &f8_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    int64_t f9_value = 9 * i;
    ret = GmcNodeSetPropertyByName(T2, "A9", GMC_DATATYPE_INT64, &f9_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f10_value = 10 * i;
    ret = GmcNodeSetPropertyByName(T2, "A10", GMC_DATATYPE_UINT64, &f10_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    bool f11_value = bool_value;
    ret = GmcNodeSetPropertyByName(T2, "A11", GMC_DATATYPE_BOOL, &f11_value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);

    float f12_value = 12 * i;
    ret = GmcNodeSetPropertyByName(T2, "A12", GMC_DATATYPE_FLOAT, &f12_value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);

    double f13_value = 13 * i;
    ret = GmcNodeSetPropertyByName(T2, "A13", GMC_DATATYPE_DOUBLE, &f13_value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);

    // f14  类型为resource

    uint64_t f15_value = 1000;
    ret = GmcNodeSetPropertyByName(T2, "A15", GMC_DATATYPE_TIME, &f15_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t f16_value = 1;
    ret = GmcNodeSetPropertyByName(T2, "A16", GMC_DATATYPE_BITFIELD8, &f16_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint16_t f17_value = 2;
    ret = GmcNodeSetPropertyByName(T2, "A17", GMC_DATATYPE_BITFIELD16, &f17_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t f18_value = 2;
    ret = GmcNodeSetPropertyByName(T2, "A18", GMC_DATATYPE_BITFIELD32, &f18_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    int64_t f19_value = 2;
    ret = GmcNodeSetPropertyByName(T2, "A19", GMC_DATATYPE_BITFIELD64, &f19_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);

    // f20 类型为partion

    ret = GmcNodeSetPropertyByName(T2, "A21", GMC_DATATYPE_STRING, f21_value, (strlen(f21_value)));
    ASSERT_EQ(GMERR_OK, ret);

    char f22_value[] = "222222";
    ret = GmcNodeSetPropertyByName(T2, "A22", GMC_DATATYPE_BYTES, f22_value, 7);
    EXPECT_EQ(GMERR_OK, ret);
    char f23_value[] = "232222";
    ret = GmcNodeSetPropertyByName(T2, "A23", GMC_DATATYPE_FIXED, f23_value, 7);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBitMapT bitMap = {0, 127, NULL};
    uint8_t bits[128 / 8];
    memset(bits, 0xffff, 128 / 8);
    bits[128 / 8 - 1] = '\0';
    bitMap.bits = bits;
    ret = GmcNodeSetPropertyByName(T2, "A24", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
}
void TestGmcNodeSetPropertyByName_V(GmcNodeT *T3, int i, bool bool_value, char *f21_value)
{
    int ret = 0;
    int8_t value_8 = i % 128;
    uint8_t value_u8 = i % 256;
    int16_t value_16 = i % 32768;
    uint16_t value_u16 = i % 65566;
    char f1_value = 'a';
    ret = GmcNodeSetPropertyByName(T3, "V1", GMC_DATATYPE_CHAR, &f1_value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);

    unsigned char f2_value = 'b';
    ret = GmcNodeSetPropertyByName(T3, "V2", GMC_DATATYPE_UCHAR, &f2_value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);

    int8_t f3_value = value_8;
    ret = GmcNodeSetPropertyByName(T3, "V3", GMC_DATATYPE_INT8, &f3_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t f4_value = value_u8;
    ret = GmcNodeSetPropertyByName(T3, "V4", GMC_DATATYPE_UINT8, &f4_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    int16_t f5_value = value_16;
    ret = GmcNodeSetPropertyByName(T3, "V5", GMC_DATATYPE_INT16, &f5_value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);

    // uint16_t f6_value = value_u16;
    uint16_t f6_value = 10;
    ret = GmcNodeSetPropertyByName(T3, "V6", GMC_DATATYPE_UINT16, &f6_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    int32_t f7_value = 7 * i;
    ret = GmcNodeSetPropertyByName(T3, "V7", GMC_DATATYPE_INT32, &f7_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t f8_value = 8 * i;
    ret = GmcNodeSetPropertyByName(T3, "V8", GMC_DATATYPE_UINT32, &f8_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    int64_t f9_value = 9 * i;
    ret = GmcNodeSetPropertyByName(T3, "V9", GMC_DATATYPE_INT64, &f9_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f10_value = 10 * i;
    ret = GmcNodeSetPropertyByName(T3, "V10", GMC_DATATYPE_UINT64, &f10_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    bool f11_value = bool_value;
    ret = GmcNodeSetPropertyByName(T3, "V11", GMC_DATATYPE_BOOL, &f11_value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);

    float f12_value = 12 * i;
    ret = GmcNodeSetPropertyByName(T3, "V12", GMC_DATATYPE_FLOAT, &f12_value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);

    double f13_value = 13 * i;
    ret = GmcNodeSetPropertyByName(T3, "V13", GMC_DATATYPE_DOUBLE, &f13_value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);

    // f14  类型为resource

    uint64_t f15_value = 1000;
    ret = GmcNodeSetPropertyByName(T3, "V15", GMC_DATATYPE_TIME, &f15_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t f16_value = 1;
    ret = GmcNodeSetPropertyByName(T3, "V16", GMC_DATATYPE_BITFIELD8, &f16_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint16_t f17_value = 2;
    ret = GmcNodeSetPropertyByName(T3, "V17", GMC_DATATYPE_BITFIELD16, &f17_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t f18_value = 2;
    ret = GmcNodeSetPropertyByName(T3, "V18", GMC_DATATYPE_BITFIELD32, &f18_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    int64_t f19_value = 2;
    ret = GmcNodeSetPropertyByName(T3, "V19", GMC_DATATYPE_BITFIELD64, &f19_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);

    // f20 类型为partion

    ret = GmcNodeSetPropertyByName(T3, "V21", GMC_DATATYPE_STRING, f21_value, (strlen(f21_value)));
    ASSERT_EQ(GMERR_OK, ret);

    char f22_value[] = "121222";
    ret = GmcNodeSetPropertyByName(T3, "V22", GMC_DATATYPE_BYTES, f22_value, 7);
    EXPECT_EQ(GMERR_OK, ret);
    char f23_value[] = "132222";
    ret = GmcNodeSetPropertyByName(T3, "V23", GMC_DATATYPE_FIXED, f23_value, 7);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBitMapT bitMap = {0, 127, NULL};
    uint8_t bits[128 / 8];
    memset(bits, 0xffff, 128 / 8);
    bits[128 / 8 - 1] = '\0';
    bitMap.bits = bits;
    ret = GmcNodeSetPropertyByName(T3, "V24", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
}
//插入成功
void TestGMCInsertVertex(GmcStmtT *stmt, int index, bool bool_value, char *f21_value, int start_num, int end_num,
    int array_num, int vector_num, const char *labelName)
{
    int32_t ret = 0;
    void *label = NULL;
    // 插入顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);

        GmcNodeT *root, *T1, *T2, *T3;  // T1 对应普通子节点  T2对应fixed_array子节点（是T1的子节点） T3对应vector子节点
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);

        TestGmcNodeSetPropertyByName_PK(root, i * index);
        TestGmcNodeSetPropertyByName_R(root, i * index, bool_value, f21_value);
        TestGmcNodeSetPropertyByName_P(T1, i * index, bool_value, f21_value);

        ret = GmcNodeGetChild(
            T1, "T2", &T2);  // 插入array节点    // ret = GmcSetNodeRecordIndex(stmt, (char *)"T1.T2", 0);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcNodeSetPropertyByName_PA(T2, (i + 1) * index * (j + 1));  //保证mebKey值唯一
            TestGmcNodeSetPropertyByName_A(T2, i * index, bool_value, f21_value);
            ret = GmcNodeGetNextElement(T2, &T2);  // ret = GmcGetNodeNextRecord(stmt, (char *)"T1.T2");
            if (j < array_num - 1) {
                ASSERT_EQ(GMERR_OK, ret);
            } else {
                ASSERT_EQ(GMERR_NO_DATA, ret);  // 适配最新错误码
                ret = testGmcGetLastError(NULL);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        // 插入vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(
                T3, &T3);  // Vector 使用的接口   // ret = GmcAddNodeVectorRecord(stmt, (char *)"T3");
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_V(T3, i * index, bool_value, f21_value);
        }
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        // printf("ret is %d \n",ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
}
//设置条件更新的 属性值
void TestGmcNodeSetPropertyByName_T1(GmcStmtT *stmt, int64_t P9_value)
{
    GmcNodeT *root, *T1;
    int ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T1, (char *)"P9", GMC_DATATYPE_INT64, &P9_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : NodePathChange  Node节点描述符非法 普通子节点
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :  member 不允许设在普通子节点上  只能在vector 和array上
 Author       : wangkun wwx1038088
 Modification :
**************************************************************************** */
TEST_F(DML_060_001, DML_060_001_001)
{
    printf("**********************DML_060_001_001********************\n");
    char *schema_json = NULL;                                   // schema_json
    char g_configJson[128] = "{\"max_record_count\" : 10000}";  // config_json
    //读json文件  和config文件
    readJanssonFile("./schema_file/NodePathChange.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    // 创建VertexLabel
    int32_t ret = 0;
    char labelName[128] = "NodePathChange";  //表名
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);  //建表失败返回错误码
                               // malloc json释放
    free(schema_json);
    // 删除表
    ret = GmcDropVertexLabel(stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  : NodePathChange  Node节点描述符非法 array子节点
 Input        : None
 Output       : None
 Return Value :
 Notes        : 建表失败返回错误码
 History      :
 Author       : wangkun wwx1038088
 Modification :
**************************************************************************** */
TEST_F(DML_060_001, DML_060_001_002)
{
    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    printf("**********************DML_060_001_002********************\n");
    char *schema_json = NULL;                                   // schema_json
    char g_configJson[128] = "{\"max_record_count\" : 10000}";  // config_json
    //读json文件  和config文件
    readJanssonFile("./schema_file/NodePathChangeArray.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    // 创建VertexLabel
    int32_t ret = 0;
    char labelName[128] = "NodePathChangeArray";  //表名
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_INVALID_NAME, ret);  //建表失败返回错误码
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // malloc json释放
    free(schema_json);
}
/* ****************************************************************************
 Description  : NodePathChange  Node节点描述符非法 vector子节点
 Input        : None
 Output       : None
 Return Value : 建表失败 返回错误码
 Notes        :
 History      :
 Author       : wangkun wwx1038088
 Modification :
**************************************************************************** */
TEST_F(DML_060_001, DML_060_001_003)
{
    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    printf("**********************DML_060_001_003********************\n");
    char *schema_json = NULL;                                   // schema_json
    char g_configJson[128] = "{\"max_record_count\" : 10000}";  // config_json
    //读json文件  和config文件
    readJanssonFile("./schema_file/NodePathChangeVector.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    // 创建VertexLabel
    int32_t ret = 0;
    char labelName[128] = "NodePathChangeVector";  //表名
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_INVALID_NAME, ret);  //建表失败 返回错误码
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // malloc json释放
    free(schema_json);
}
/* ****************************************************************************
 Description  : NodePathChange  Node节点描述符非法 32层tree表
 Input        : None
 Output       : None
 Return Value : 建表失败返回错误码
 Notes        :
 History      :
 Author       : wangkun wwx1038088
 Modification :
**************************************************************************** */
TEST_F(DML_060_001, DML_060_001_004)
{
    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    printf("**********************DML_060_001_004********************\n");
    char *schema_json = NULL;                                   // schema_json
    char g_configJson[128] = "{\"max_record_count\" : 10000}";  // config_json
    //读json文件  和config文件
    readJanssonFile("./schema_file/NodePathChange32Deep.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    // 创建VertexLabel
    int32_t ret = 0;
    char labelName[128] = "NodePathChange32Deep";  //表名
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_INVALID_NAME, ret);  //建表失败返回错误码
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // malloc json释放
    free(schema_json);
}
/* ****************************************************************************
 Description  : NodePathChange  Node节点描述符非法 “//”
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : wangkun wwx1038088
 Modification :
**************************************************************************** */
TEST_F(DML_060_001, DML_060_001_005)
{
    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    printf("**********************DML_060_001_005********************\n");
    char *schema_json = NULL;                                   // schema_json
    char g_configJson[128] = "{\"max_record_count\" : 10000}";  // config_json
    //读json文件  和config文件
    readJanssonFile("./schema_file/NodePathChangeTwoFaile.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    // 创建VertexLabel
    int32_t ret = 0;
    char labelName[128] = "NodePathChangeTwoFaile";  //表名
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_DATA_EXCEPTION, ret);  //建表失败返回错误码  这里的错误码返回4000  与开发沟通是QE这层有last error覆盖掉了DM的
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // malloc json释放
    free(schema_json);
}
/* ****************************************************************************
 Description  : NodePathChange  子树订阅 node描述符非法
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : wangkun wwx1038088
 Modification :
**************************************************************************** */
TEST_F(DML_060_001, DML_060_001_006)
{
    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_INVALID_PROPERTY);
    char g_errorCode02[1024] = {0};
    (void)snprintf(g_errorCode02, 1024, "GMERR-%d", GMERR_INVALID_OBJECT_DEFINITION);
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode01, g_errorCode02);
    printf("**********************DML_060_001_006********************\n");
    user_data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(user_data, 0, sizeof(SnUserDataT));

    user_data->new_value = (int *)malloc(sizeof(int) * g_end_num * 10);
    memset(user_data->new_value, 0, sizeof(int) * g_end_num * 10);

    user_data->old_value = (int *)malloc(sizeof(int) * g_end_num * 10);
    memset(user_data->old_value, 0, sizeof(int) * g_end_num * 10);

    user_data->isReplace_insert = (bool *)malloc(sizeof(bool) * g_end_num * 10);
    memset(user_data->isReplace_insert, 0, sizeof(bool) * g_end_num * 10);
    //创建订阅连接
    int chanRingLen = 256;
    const char *g_subConnName = "subConnName";
    const char *g_subName = "subVertexLabel";
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &chanRingLen);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // EXPECT_EQ(GMERR_OK, ret);
    // 创建VertexLabel
    char *schema_json = NULL;                                   // schema_json
    char g_configJson[128] = "{\"max_record_count\" : 10000}";  // config_json
    readJanssonFile("./schema_file/NodePathChangeFilterCondition.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    int32_t ret = 0;
    char labelName[128] = "NodePathChangeFilterCondition";  //表名
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    // malloc json释放
    free(schema_json);
    // 创建订阅关系
    char *g_sub_info = NULL;
    readJanssonFile("schema_file/NodePathChange_Subinfo.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);
    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(stmt, &tmp_g_sub_info, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);  // 下发订阅关系的时候 返回错误码
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // 删除表
    ret = GmcDropVertexLabel(stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    //释放内存
    free(g_sub_info);
    free(user_data->isReplace_insert);
    free(user_data->old_value);
    free(user_data->new_value);
    free(user_data);
}
/* ****************************************************************************
 Description  : NodePathChange  过滤条件更新顶点  参数传入的node节点描述符非法。
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : wangkun wwx1038088
 Modification :
**************************************************************************** */
TEST_F(DML_060_001, DML_060_001_007)
{
    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_INVALID_PROPERTY);
    char g_errorCode02[1024] = {0};
    (void)snprintf(g_errorCode02, 1024, "GMERR-%d", GMERR_NO_DATA);
    char g_errorCode03[1024] = {0};
    (void)snprintf(g_errorCode03, 1024, "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    AW_ADD_ERR_WHITE_LIST(3, g_errorCode01, g_errorCode02, g_errorCode03);
    printf("**********************DML_060_001_007********************\n");
    char *schema_json = NULL;                                   // schema_json
    char g_configJson[128] = "{\"max_record_count\" : 10000}";  // config_json
    //读json文件  和config文件
    readJanssonFile("./schema_file/NodePathChangeFilterCondition.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    // 创建VertexLabel
    int32_t ret = 0;
    char labelName[128] = "NodePathChangeFilterCondition";  //表名
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    // malloc json释放
    free(schema_json);
    // 插入数据
    TestGMCInsertVertex(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, labelName);
    // 条件更新顶点 失败
    int64_t P9_value = 10000;
    const char *cond = (const char *)"NodePathChangeFilterCondition.T1.P9>0";
    // praper 参数为更新顶点
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);  //更新顶点 预备操作
    ASSERT_EQ(GMERR_OK, ret);
    // 设置属性
    TestGmcNodeSetPropertyByName_T1(stmt, P9_value);
    ret = GmcSetFilter(stmt, cond);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);  //报错在这里
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // 删除表
    ret = GmcDropVertexLabel(stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  : NodePathChange  过滤条件删除顶点  参数传入的node节点描述符非法。
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : wangkun wwx1038088
 Modification :
**************************************************************************** */
TEST_F(DML_060_001, DML_060_001_008)
{
    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_INVALID_PROPERTY);
    char g_errorCode02[1024] = {0};
    (void)snprintf(g_errorCode02, 1024, "GMERR-%d", GMERR_NO_DATA);
    char g_errorCode03[1024] = {0};
    (void)snprintf(g_errorCode03, 1024, "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    AW_ADD_ERR_WHITE_LIST(3, g_errorCode01, g_errorCode02, g_errorCode03);
    printf("**********************DML_060_001_008********************\n");
    char *schema_json = NULL;                                   // schema_json
    char g_configJson[128] = "{\"max_record_count\" : 10000}";  // config_json
    //读json文件  和config文件
    readJanssonFile("./schema_file/NodePathChangeFilterCondition.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    // 创建VertexLabel
    int32_t ret = 0;
    char labelName[128] = "NodePathChangeFilterCondition";  //表名
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    // malloc json释放
    free(schema_json);
    // 插入数据
    TestGMCInsertVertex(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, labelName);
    // 条件删除顶点 失败
    int64_t P9_value = 10000;
    const char *cond = (const char *)"NodePathChangeFilterCondition.T1.P9>0";
    // praper 参数为删除
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);  //删除顶点 预备操作
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, cond);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);  //报错在这里
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // 删除表
    ret = GmcDropVertexLabel(stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  : NodePathChange  过滤条件扫描顶点 参数传入的node节点描述符非法。
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : wangkun wwx1038088
 Modification :
**************************************************************************** */
TEST_F(DML_060_001, DML_060_001_009)
{
    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_INVALID_PROPERTY);
    char g_errorCode02[1024] = {0};
    (void)snprintf(g_errorCode02, 1024, "GMERR-%d", GMERR_NO_DATA);
    char g_errorCode03[1024] = {0};
    (void)snprintf(g_errorCode03, 1024, "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    AW_ADD_ERR_WHITE_LIST(3, g_errorCode01, g_errorCode02, g_errorCode03);
    printf("**********************DML_060_001_009********************\n");
    char *schema_json = NULL;                                   // schema_json
    char g_configJson[128] = "{\"max_record_count\" : 10000}";  // config_json
    //读json文件  和config文件
    readJanssonFile("./schema_file/NodePathChangeFilterCondition.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    // 创建VertexLabel
    int32_t ret = 0;
    char labelName[128] = "NodePathChangeFilterCondition";  //表名
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    // malloc json释放
    free(schema_json);
    // 插入数据
    TestGMCInsertVertex(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, labelName);
    // 条件扫描顶点 失败
    int64_t P9_value = 10000;
    const char *cond = (const char *)"NodePathChangeFilterCondition.T1.P9>0";
    // praper 参数为更新顶点
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);  //查询顶点 预备操作
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, cond);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetOutputFormat(stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);  //报错在这里
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // 删除表
    ret = GmcDropVertexLabel(stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  : NodePathChange  过滤条件删除顶点，传入参数时，(char *)"labelTree/节点/属性
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : wangkun wwx1038088
 Modification :
**************************************************************************** */
TEST_F(DML_060_001, DML_060_001_010)
{
    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_INVALID_PROPERTY);
    char g_errorCode02[1024] = {0};
    (void)snprintf(g_errorCode02, 1024, "GMERR-%d", GMERR_NO_DATA);
    char g_errorCode03[1024] = {0};
    (void)snprintf(g_errorCode03, 1024, "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    char g_errorCode04[1024] = {0};
    (void)snprintf(g_errorCode04, 1024, "GMERR-%d", GMERR_DATATYPE_MISMATCH);
    AW_ADD_ERR_WHITE_LIST(4, g_errorCode01, g_errorCode02, g_errorCode03, g_errorCode04);
    printf("**********************DML_060_001_010********************\n");
    char *schema_json = NULL;                                   // schema_json
    char g_configJson[128] = "{\"max_record_count\" : 10000}";  // config_json
    //读json文件  和config文件
    readJanssonFile("./schema_file/NodePathChangeFilterCondition.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    // 创建VertexLabel
    int32_t ret = 0;
    char labelName[128] = "NodePathChangeFilterCondition";  //表名
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    // malloc json释放
    free(schema_json);
    // 插入数据
    TestGMCInsertVertex(stmt, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, labelName);
    // 条件删除顶点 失败
    int64_t P9_value = 10000;
    const char *cond = (const char *)"NodePathChangeFilterCondition/T1/P9>0";
    // praper 参数为删除
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);  //删除顶点 预备操作
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, cond);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);  //报错在这里
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // 删除表
    ret = GmcDropVertexLabel(stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  : NodePathChange  创建tree表，表名包含“ / ”
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : wangkun wwx1038088
 Modification :
**************************************************************************** */
TEST_F(DML_060_001, DML_060_001_011)
{
    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    printf("**********************DML_060_001_011********************\n");
    char *schema_json = NULL;                                   // schema_json
    char g_configJson[128] = "{\"max_record_count\" : 10000}";  // config_json
    //读json文件  和config文件
    readJanssonFile("./schema_file/NodePathChangeVertexLabelNameFaile.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    // 创建VertexLabel
    int32_t ret = 0;
    char labelName[128] = "NodePathChange/VertexLabelNameFaile";  //表名
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_INVALID_NAME, ret);  //建表失败返回错误码
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // malloc json释放
    free(schema_json);
    // 删除表
    ret = GmcDropVertexLabel(stmt, labelName);
}
/* ****************************************************************************
 Description  : NodePathChange  建表，表字段属性名包含“/”
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : wangkun wwx1038088
 Modification :
**************************************************************************** */
TEST_F(DML_060_001, DML_060_001_012)
{
    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_INVALID_JSON_CONTENT);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    printf("**********************DML_060_001_012********************\n");
    char *schema_json = NULL;                                   // schema_json
    char g_configJson[128] = "{\"max_record_count\" : 10000}";  // config_json
    //读json文件  和config文件
    readJanssonFile("./schema_file/NodePathChangeProperty.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    // 创建VertexLabel
    int32_t ret = 0;
    char labelName[128] = "NodePathChangeProperty";  //表名
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_INVALID_JSON_CONTENT, ret);  //建表失败返回错误码
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // malloc json释放
    free(schema_json);
    // 删除表
    ret = GmcDropVertexLabel(stmt, labelName);
}
