/*****************************************************************************
 Description  : 轻量化事务处理
 Notes        :
 History      :
 Author       : 覃乾宝 qwx995465
 Modification :
 Date         : 2021/12/25
*****************************************************************************/
extern "C" {
}
#include "gtest/gtest.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <pthread.h>
#include <errno.h>
#include "t_datacom_lite.h"
#include "t_datacom_lite.h"

GmcConnT *g_conn;
GmcStmtT *g_stmt;
int affectRows;
unsigned int len;
char *normal_vertexlabel_schema = NULL;
char *normal_graph_vertex_label_schema = NULL;
char *normal_graph_edge_label_schema = NULL;
const char *normal_config_json = R"(
    {
        "max_record_count":10000
    }
)";
const char *g_normal_vertexlabel_name = "T39_all_type";
const char *g_normal_pk_name = "T39_K0";
const char *g_normal_sk_name = "T39_hash";

void set_VertexProperty_PK(GmcStmtT *stmt, int i);
void set_VertexProperty_SK(GmcStmtT *stmt, int i);
void set_VertexProperty(GmcStmtT *stmt, int i);
void query_VertexProperty(GmcStmtT *stmt, int i);

class LightTransClose : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/modifyCfg.sh  \"isFastReadUncommitted=0\"");
        system("sh $TEST_HOME/tools/modifyCfg.sh  \"workerHungThreshold=10,20,60\"");
        system("sh $TEST_HOME/tools/start.sh");
        int ret = 0;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        readJanssonFile("schemaFile/NormalVertexLabel.gmjson", &normal_vertexlabel_schema);
        ASSERT_NE((void *)NULL, normal_vertexlabel_schema);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        free(normal_vertexlabel_schema);
        int ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void LightTransClose::SetUp()
{
    printf("[INFO] LightTransClose Start.\n");
	char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    int ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void LightTransClose::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    printf("[INFO] LightTransClose End.\n");
}

void set_VertexProperty_PK(GmcStmtT *stmt, int i)
{
    int ret = 0;
    uint32_t F7Value = i;
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT32, &F7Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
}

void set_VertexProperty_SK(GmcStmtT *stmt, int i)
{
    int ret = 0;
    int64_t F9Value = i;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &F9Value, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
}

void set_VertexProperty(GmcStmtT *stmt, int i)
{
    int ret = 0;
    char F0Value = i;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_CHAR, &F0Value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);
    unsigned char F1Value = i;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UCHAR, &F1Value, sizeof(unsigned char));
    EXPECT_EQ(GMERR_OK, ret);
    int8_t F2Value = i;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &F2Value, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t F3Value = i;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &F3Value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    int16_t F4Value = i;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &F4Value, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint16_t F5Value = i;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &F5Value, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    int32_t F6Value = i;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, &F6Value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    bool F8Value = false;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &F8Value, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t F10Value = i;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_UINT64, &F10Value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    float F11Value = i;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_FLOAT, &F11Value, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);
    double F12Value = i;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_DOUBLE, &F12Value, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t F13Value = i;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_TIME, &F13Value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    char F14Value[] = "testver";
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, F14Value, (strlen(F14Value)));
    EXPECT_EQ(GMERR_OK, ret);
    char F15Value[12] = "12";
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_BYTES, F15Value, 12);
    EXPECT_EQ(GMERR_OK, ret);
    char F16Value[12] = "13";
    ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_FIXED, F16Value, 12);
    EXPECT_EQ(GMERR_OK, ret);
}

void query_VertexProperty(GmcStmtT *stmt, int i)
{
    int ret = 0;
    // Get F0
    char F0Value = i;
    ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_CHAR, &F0Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F1
    unsigned char F1Value = i;
    ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_UCHAR, &F1Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F2
    int8_t F2Value = i;
    ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_INT8, &F2Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F3
    uint8_t F3Value = i;
    ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_UINT8, &F3Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F4
    int16_t F4Value = i;
    ret = queryPropertyAndCompare(stmt, "F4", GMC_DATATYPE_INT16, &F4Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F5
    uint16_t F5Value = i;
    ret = queryPropertyAndCompare(stmt, "F5", GMC_DATATYPE_UINT16, &F5Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F6
    int32_t F6Value = i;
    ret = queryPropertyAndCompare(stmt, "F6", GMC_DATATYPE_INT32, &F6Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F8
    bool F8Value = false;
    ret = queryPropertyAndCompare(stmt, "F8", GMC_DATATYPE_BOOL, &F8Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F10
    uint64_t F10Value = i;
    ret = queryPropertyAndCompare(stmt, "F10", GMC_DATATYPE_UINT64, &F10Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F11
    float F11Value = i;
    ret = queryPropertyAndCompare(stmt, "F11", GMC_DATATYPE_FLOAT, &F11Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F12
    double F12Value = i;
    ret = queryPropertyAndCompare(stmt, "F12", GMC_DATATYPE_DOUBLE, &F12Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F13
    uint64_t F13Value = i;
    ret = queryPropertyAndCompare(stmt, "F13", GMC_DATATYPE_TIME, &F13Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F14
    char F14Value[] = "testver";
    ret = queryPropertyAndCompare(stmt, "F14", GMC_DATATYPE_STRING, F14Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F15
    char F15Value[12] = "12";
    ret = queryPropertyAndCompare(stmt, "F15", GMC_DATATYPE_BYTES, F15Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F16
    char F16Value[12] = "13";
    ret = queryPropertyAndCompare(stmt, "F16", GMC_DATATYPE_FIXED, F16Value);
    EXPECT_EQ(GMERR_OK, ret);
}

// 001.系统级开关关闭, 表级开关打开, 针对轻量化表, 预期支持
TEST_F(LightTransClose, DML_087_LightTransClose_test_001)
{
    int ret = 0;
    char vertexLabel_config[] = "{\"max_record_count\":100000, \"isFastReadUncommitted\":1}";

    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, vertexLabel_config);
    EXPECT_EQ(GMERR_OK, ret);

    int count = 10000;
    // insert Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < count; i++) {
        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty_SK(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);
    }

    printf("insert vertex 10000 data done\n");
    // merge Vertex
    for (uint32_t i = 0; i < count; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t newVal = i + 10000;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "T39_K0");
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty_SK(g_stmt, newVal);
        set_VertexProperty(g_stmt, newVal);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);

        // read
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        query_VertexProperty(g_stmt, newVal);
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &newVal);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("merge vertex 10000 data done\n");
    // replace Vertex
    for (uint32_t i = 0; i < count; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t newVal = i + 20000;
        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty_SK(g_stmt, newVal);
        set_VertexProperty(g_stmt, newVal);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 2);
        EXPECT_EQ(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);

        bool isFinish = true;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        query_VertexProperty(g_stmt, newVal);
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &newVal);
        EXPECT_EQ(GMERR_OK, ret);
    }

    printf("replace vertex 10000 data done\n");
    // delete Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);
    }

    printf("delete vertex 10000 data done\n");
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 002.系统级开关关闭, 表级开关打开, 针对非轻量化表, 预期不支持
TEST_F(LightTransClose, DML_087_LightTransClose_test_002)
{
    char errorMsg1[128] = {}, errorMsg2[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);
    int ret = 0;
    char vertexLabel_config[] = "{\"max_record_count\":100000, \"isFastReadUncommitted\":1}";

    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, vertexLabel_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 事务开启
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    ret = GmcTransStart(g_conn, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    int count = 10000;
    // insert Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < count; i++) {
        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty_SK(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    }

    printf("insert vertex 10000 data done\n");
    // merge Vertex
    for (uint32_t i = 0; i < count; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t newVal = i + 10000;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "T39_K0");
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty_SK(g_stmt, newVal);
        set_VertexProperty(g_stmt, newVal);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);

        // read
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, ret);
    }
    printf("merge vertex 10000 data done\n");
    // replace Vertex
    for (uint32_t i = 0; i < count; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t newVal = i + 20000;
        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty_SK(g_stmt, newVal);
        set_VertexProperty(g_stmt, newVal);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);

        bool isFinish = true;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, ret);
    }

    printf("replace vertex 10000 data done\n");
    // delete Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    }

    printf("delete vertex 10000 data done\n");

    // 事务提交
    ret = GmcTransCommit(g_conn);
    ASSERT_EQ(GMERR_OK, ret);
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 003.系统级开关关闭, 表级开关关闭, 针对轻量化表, 预期支持
TEST_F(LightTransClose, DML_087_LightTransClose_test_003)
{
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int ret = 0;
    char vertexLabel_config[] = "{\"max_record_count\":100000, \"isFastReadUncommitted\":0}";

    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, vertexLabel_config);
    EXPECT_EQ(GMERR_OK, ret);

    int count = 10000;
    // insert Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < count; i++) {
        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty_SK(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);
    }

    printf("insert vertex 10000 data done\n");
    // merge Vertex
    for (uint32_t i = 0; i < count; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t newVal = i + 10000;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "T39_K0");
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty_SK(g_stmt, newVal);
        set_VertexProperty(g_stmt, newVal);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);

        // read
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        query_VertexProperty(g_stmt, newVal);
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &newVal);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("merge vertex 10000 data done\n");
    // replace Vertex
    for (uint32_t i = 0; i < count; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t newVal = i + 20000;
        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty_SK(g_stmt, newVal);
        set_VertexProperty(g_stmt, newVal);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 2);
        EXPECT_EQ(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);

        bool isFinish = true;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        query_VertexProperty(g_stmt, newVal);
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &newVal);
        EXPECT_EQ(GMERR_OK, ret);
    }

    printf("replace vertex 10000 data done\n");
    // delete Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);
    }

    printf("delete vertex 10000 data done\n");
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 004.系统级开关关闭, 表级开关关闭, 针对非轻量化表, 预期支持
TEST_F(LightTransClose, DML_087_LightTransClose_test_004)
{
    int ret = 0;
    char vertexLabel_config[] = "{\"max_record_count\":100000, \"isFastReadUncommitted\":0}";

    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, vertexLabel_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 事务开启
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    ret = GmcTransStart(g_conn, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    int count = 10000;
    // insert Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < count; i++) {
        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty_SK(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);
    }

    printf("insert vertex 10000 data done\n");
    // merge Vertex
    for (uint32_t i = 0; i < count; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t newVal = i + 10000;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "T39_K0");
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty_SK(g_stmt, newVal);
        set_VertexProperty(g_stmt, newVal);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);

        // read
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        query_VertexProperty(g_stmt, newVal);
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &newVal);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("merge vertex 10000 data done\n");
    // replace Vertex
    for (uint32_t i = 0; i < count; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t newVal = i + 20000;
        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty_SK(g_stmt, newVal);
        set_VertexProperty(g_stmt, newVal);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 2);
        EXPECT_EQ(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);

        bool isFinish = true;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        query_VertexProperty(g_stmt, newVal);
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &newVal);
        EXPECT_EQ(GMERR_OK, ret);
    }

    printf("replace vertex 10000 data done\n");
    // delete Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);
    }

    printf("delete vertex 10000 data done\n");

    // 事务提交
    ret = GmcTransCommit(g_conn);
    ASSERT_EQ(GMERR_OK, ret);
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 005.系统级开关关闭, 表级开关不设置, 针对轻量化表, 预期支持
TEST_F(LightTransClose, DML_087_LightTransClose_test_005)
{
    int ret = 0;

    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    int count = 10000;
    // insert Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < count; i++) {
        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty_SK(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);
    }

    printf("insert vertex 10000 data done\n");
    // merge Vertex
    for (uint32_t i = 0; i < count; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t newVal = i + 10000;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "T39_K0");
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty_SK(g_stmt, newVal);
        set_VertexProperty(g_stmt, newVal);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);

        // read
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        query_VertexProperty(g_stmt, newVal);
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &newVal);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("merge vertex 10000 data done\n");
    // replace Vertex
    for (uint32_t i = 0; i < count; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t newVal = i + 20000;
        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty_SK(g_stmt, newVal);
        set_VertexProperty(g_stmt, newVal);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 2);
        EXPECT_EQ(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);

        bool isFinish = true;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        query_VertexProperty(g_stmt, newVal);
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &newVal);
        EXPECT_EQ(GMERR_OK, ret);
    }

    printf("replace vertex 10000 data done\n");
    // delete Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);
    }

    printf("delete vertex 10000 data done\n");
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 006.系统级开关关闭, 表级开关不设置, 针对非轻量化表, 预期支持
TEST_F(LightTransClose, DML_087_LightTransClose_test_006)
{
    int ret = 0;
    // char vertexLabel_config[] = "{\"max_record_count\":100000, \"isFastReadUncommitted\":0}";

    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // 事务开启
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    ret = GmcTransStart(g_conn, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    int count = 10000;
    // insert Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < count; i++) {
        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty_SK(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);
    }

    printf("insert vertex 10000 data done\n");
    // merge Vertex
    for (uint32_t i = 0; i < count; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t newVal = i + 10000;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "T39_K0");
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty_SK(g_stmt, newVal);
        set_VertexProperty(g_stmt, newVal);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);

        // read
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        query_VertexProperty(g_stmt, newVal);
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &newVal);
        EXPECT_EQ(GMERR_OK, ret);
    }
    printf("merge vertex 10000 data done\n");
    // replace Vertex
    for (uint32_t i = 0; i < count; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t newVal = i + 20000;
        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty_SK(g_stmt, newVal);
        set_VertexProperty(g_stmt, newVal);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 2);
        EXPECT_EQ(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);

        bool isFinish = true;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        query_VertexProperty(g_stmt, newVal);
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &newVal);
        EXPECT_EQ(GMERR_OK, ret);
    }

    printf("replace vertex 10000 data done\n");
    // delete Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);
    }

    printf("delete vertex 10000 data done\n");

    // 事务提交
    ret = GmcTransCommit(g_conn);
    ASSERT_EQ(GMERR_OK, ret);

    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

void *thread_ddl_007(void *args)
{
    int ret;
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    ret = testGmcConnect(&conn1, &stmt1);
    EXPECT_EQ(GMERR_OK, ret);

    int count = *(int *)args;

    int vetexlabel_count = 1023;
    for (uint32_t num = 0; num < vetexlabel_count; num++) {
        char labelName[20] = "";
        char label_schema[1024] = "";
        snprintf(label_schema, 1024,
            "[{\"type\":\"record\", \"name\":\"T%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
            "{\"name\":\"F1\", \"type\":\"int32\"}],"
            "\"keys\":[{\"node\":\"T%d\", \"name\":\"vertex_pk\", \"fields\":[\"F0\"], "
            "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
            num, num);
        snprintf(labelName, 20, "T%d", num);
        ret = GmcCreateVertexLabel(stmt1, label_schema, NULL);
        EXPECT_EQ((ret == GMERR_OK) ? GMERR_OK : GMERR_DUPLICATE_TABLE, ret);  // 仅校验建重复表
    }

    ret = testGmcDisconnect(conn1, stmt1);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

#ifdef ENV_RTOSV2X
#define TREAD_NUM 64
#else
#define TREAD_NUM 128
#endif
// 007.多线程并发创建同名vertexLabel:1023 1024则会校验表的规格限制
// 系统级开关关闭, 表级开关不设置, 针对非轻量化表, 预期支持
TEST_F(LightTransClose, DML_087_LightTransClose_test_007)
{
    int ret = 0;
	char errorCode1[128] = {0};
    char errorCode2[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_DUPLICATE_TABLE);
    (void)snprintf(errorCode2, 128, "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(2, errorCode1, errorCode2);
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int index[TREAD_NUM] = {0};
    pthread_t thr_arr_01[TREAD_NUM];
    for (int i = 0; i < TREAD_NUM; i++) {
        index[i] = i;
        ret = pthread_create(&thr_arr_01[i], NULL, thread_ddl_007, (void *)&index[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }

    for (int i = 0; i < TREAD_NUM; i++) {
        pthread_join(thr_arr_01[i], NULL);
    }

    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int vetexlabel_count = 1023;
    for (uint32_t num = 0; num < vetexlabel_count; num++) {
        char labelName[20] = "";
        snprintf(labelName, 20, "T%d", num);
        ret = GmcDropVertexLabel(g_stmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

void *thread_ddl_008(void *args)
{
    int ret;
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    ret = testGmcConnect(&conn1, &stmt1);
    EXPECT_EQ(GMERR_OK, ret);

    int count = *(int *)args;  // thread id
    int vetexlabel_count = 8;
    for (uint32_t num = vetexlabel_count * count; num < vetexlabel_count * count + vetexlabel_count;
         num++) {  // 128*8=1024
        char labelName[20] = "";
        char label_schema[1024] = "";
        snprintf(label_schema, 1024,
            "[{\"type\":\"record\", \"name\":\"T%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
            "{\"name\":\"F1\", \"type\":\"int32\"}],"
            "\"keys\":[{\"node\":\"T%d\", \"name\":\"vertex_pk\", \"fields\":[\"F0\"], "
            "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
            num, num);
        snprintf(labelName, 20, "T%d", num);
        ret = GmcCreateVertexLabel(stmt1, label_schema, NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = testGmcDisconnect(conn1, stmt1);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

// 008.多线程并发创建不同名vertexLabel:1024 128*8
// 系统级开关关闭, 表级开关不设置, 针对非轻量化表, 预期支持
TEST_F(LightTransClose, DML_087_LightTransClose_test_008)
{
    int ret = 0;
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int index[TREAD_NUM] = {0};
    pthread_t thr_arr_01[TREAD_NUM];
    for (int i = 0; i < TREAD_NUM; i++) {
        index[i] = i;
        ret = pthread_create(&thr_arr_01[i], NULL, thread_ddl_008, (void *)&index[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }

    for (int i = 0; i < TREAD_NUM; i++) {
        pthread_join(thr_arr_01[i], NULL);
    }

    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int vetexlabel_count = 1024;
    for (uint32_t num = 0; num < vetexlabel_count; num++) {
        char labelName[20] = "";
        snprintf(labelName, 20, "T%d", num);
        ret = GmcDropVertexLabel(g_stmt, labelName);
        ASSERT_EQ(GMERR_OK, ret);
    }
}

GmcConnT *conn_test[1025] = {0};
GmcStmtT *stmt_test[1025] = {0};
void *thread_ddl_009(void *args)
{
    int ret;
    int count = *(int *)args;
    int vetexlabel_count = 8;
    for (uint32_t i = count * vetexlabel_count; i < count * vetexlabel_count + vetexlabel_count; i++) {
        ret = testGmcConnect(&conn_test[i], &stmt_test[i]);  // 线程全部退出后再释放连接
        EXPECT_EQ(GMERR_OK, ret);
        char labelName[20] = "";
        char label_schema[1024] = "";
        snprintf(label_schema, 1024,
            "[{\"type\":\"record\", \"name\":\"T%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
            "{\"name\":\"F1\", \"type\":\"int32\"}],"
            "\"keys\":[{\"node\":\"T%d\", \"name\":\"vertex_pk\", \"fields\":[\"F0\"], "
            "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
            i, i);
        snprintf(labelName, 20, "T%d", i);
        ret = GmcCreateVertexLabel(stmt_test[i], label_schema, NULL);
        EXPECT_EQ(GMERR_OK, ret);
        // ret = testGmcDisconnect(conn_test[i], stmt_test[i]);
        // EXPECT_EQ(GMERR_OK, ret); // 构造1024连接&1024张表
    }
    return NULL;
}

// 009.多线程并发创建1024个连接和vertexLabel:1024
// 系统级开关关闭, 表级开关不设置, 针对非轻量化表, 预期支持
TEST_F(LightTransClose, DML_087_LightTransClose_test_009)
{
    int ret = 0;
	char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);  // 断连
    int thrNum = TREAD_NUM;
    char cmd[1024] = {0};
    const char *viewName = "V\\$STORAGE_PERSISTENT_STAT";
    (void)snprintf(cmd, 1024, "%s/gmsysview -q %s", g_toolPath, viewName);
    ret = executeCommand(cmd, "DIRTY_PAGE_THRESHOLD"); // 判断当前是否光启编译
    if (ret == GMERR_OK) {
        thrNum = TREAD_NUM - 1;
    }
    int index[thrNum];
    memset(index, 0x00, sizeof(int) * thrNum);
    pthread_t thr_arr_01[thrNum];
    for (int i = 0; i < thrNum; i++) {
        index[i] = i;
        ret = pthread_create(&thr_arr_01[i], NULL, thread_ddl_009, (void *)&index[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }

    for (int i = 0; i < thrNum; i++) {
        pthread_join(thr_arr_01[i], NULL);
    }

    for (int i = 0; i < thrNum * 8; i++) {
        ret = testGmcDisconnect(conn_test[i], stmt_test[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = testGmcConnect(&g_conn, &g_stmt);  // 建连
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t num = 0; num < thrNum * 8; num++) {
        char labelName[20] = "";
        snprintf(labelName, 20, "T%d", num);
        ret = GmcDropVertexLabel(g_stmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

void *thread_ddl_010(void *args)
{
    int ret;
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    ret = testGmcConnect(&conn1, &stmt1);
    EXPECT_EQ(GMERR_OK, ret);

    int count = *(int *)args;
    int vetexlabel_count = 8;

    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn1, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    for (uint32_t num = vetexlabel_count * count; num < vetexlabel_count * count + vetexlabel_count; num++) {
        char labelName[20] = "";
        char label_schema[1024] = "";
        snprintf(label_schema, 1024,
            "[{\"type\":\"record\", \"name\":\"T%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
            "{\"name\":\"F1\", \"type\":\"int32\"}],"
            "\"keys\":[{\"node\":\"T%d\", \"name\":\"vertex_pk\", \"fields\":[\"F0\"], "
            "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
            num, num);
        snprintf(labelName, 20, "T%d", num);
        ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, labelName, label_schema, normal_config_json);
        EXPECT_EQ(GMERR_OK, ret);
    }

    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(8, totalNum);
    EXPECT_EQ(8, successNum);  // 每个线程创建8张
    GmcBatchDestroy(batch);

    ret = testGmcDisconnect(conn1, stmt1);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

// 010.多线程并发批量创建不同名vertexLabel:1024
// 系统级开关关闭, 表级开关不设置, 针对非轻量化表, 预期支持
TEST_F(LightTransClose, DML_087_LightTransClose_test_010)
{
    int ret = 0;
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int index[TREAD_NUM] = {0};
    pthread_t thr_arr_01[TREAD_NUM];
    for (int i = 0; i < TREAD_NUM; i++) {
        index[i] = i;
        ret = pthread_create(&thr_arr_01[i], NULL, thread_ddl_010, (void *)&index[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }

    for (int i = 0; i < TREAD_NUM; i++) {
        pthread_join(thr_arr_01[i], NULL);
    }
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int vetexlabel_count = 1024;
    for (uint32_t num = 0; num < vetexlabel_count; num++) {
        char labelName[20] = "";
        snprintf(labelName, 20, "T%d", num);
        ret = GmcDropVertexLabel(g_stmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

void *thread_ddl_011(void *args)
{
    int ret;
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    ret = testGmcConnect(&conn1, &stmt1);
    EXPECT_EQ(GMERR_OK, ret);

    int count = *(int *)args;

    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn1, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, g_normal_vertexlabel_name, normal_vertexlabel_schema,
        normal_config_json);
    EXPECT_EQ((ret == GMERR_OK) ? GMERR_OK : GMERR_DUPLICATE_TABLE, ret);

    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ((ret == GMERR_OK) ? GMERR_OK : GMERR_DUPLICATE_TABLE, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(totalNum == 0 || totalNum == 1, true);
    EXPECT_EQ(successNum == 0 || successNum == 1, true);
    GmcBatchDestroy(batch);

    ret = testGmcDisconnect(conn1, stmt1);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

// 011.多线程并发批量创建同名vertexLabel
// 系统级开关关闭, 表级开关不设置, 针对非轻量化表, 预期支持
TEST_F(LightTransClose, DML_087_LightTransClose_test_011)
{
    int ret = 0;
	char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_DUPLICATE_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int index[TREAD_NUM] = {0};
    pthread_t thr_arr_01[TREAD_NUM];
    for (int i = 0; i < TREAD_NUM; i++) {
        index[i] = i;
        ret = pthread_create(&thr_arr_01[i], NULL, thread_ddl_011, (void *)&index[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }

    for (int i = 0; i < TREAD_NUM; i++) {
        pthread_join(thr_arr_01[i], NULL);
    }
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

void *thread_ddl_012(void *args)
{
    int ret;
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    ret = testGmcConnect(&conn1, &stmt1);
    EXPECT_EQ(GMERR_OK, ret);

    int count = *(int *)args;  // thread id
    int vetexlabel_count = 8;
    for (uint32_t num = vetexlabel_count * count; num < vetexlabel_count * count + vetexlabel_count;
         num++) {  // 128*8=1024
        char labelName[20] = "";
        char label_schema[1024] = "";
        snprintf(label_schema, 1024,
            "[{\"type\":\"record\", \"name\":\"T%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
            "{\"name\":\"F1\", \"type\":\"int32\"}],"
            "\"keys\":[{\"node\":\"T%d\", \"name\":\"vertex_pk\", \"fields\":[\"F0\"], "
            "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
            num, num);
        snprintf(labelName, 20, "T%d", num);
        ret = GmcCreateVertexLabel(stmt1, label_schema, NULL);
        EXPECT_EQ(GMERR_OK, ret);

        char KvTable_Name[20] = "";
        snprintf(KvTable_Name, 20, "kv_%d", num);
        ret = GmcKvCreateTable(stmt1, KvTable_Name, NULL);
        EXPECT_EQ(GMERR_OK, ret);

        // drop vertex
        ret = GmcDropVertexLabel(stmt1, labelName);
        EXPECT_EQ(GMERR_OK, ret);

        // dorp KV table
        ret = GmcKvDropTable(stmt1, (char *)KvTable_Name);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = testGmcDisconnect(conn1, stmt1);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

// 012.多线程并发创建删除不同名kv&vertex
// 系统级开关关闭, 表级开关不设置, 针对非轻量化表, 预期支持
TEST_F(LightTransClose, DML_087_LightTransClose_test_012)
{
    int ret = 0;
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int index[TREAD_NUM] = {0};
    pthread_t thr_arr_01[TREAD_NUM];
    for (int i = 0; i < TREAD_NUM; i++) {
        index[i] = i;
        ret = pthread_create(&thr_arr_01[i], NULL, thread_ddl_012, (void *)&index[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }

    for (int i = 0; i < TREAD_NUM; i++) {
        pthread_join(thr_arr_01[i], NULL);
    }

    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

void *thread_ddl_013(void *args)
{
    int ret;
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    ret = testGmcConnect(&conn1, &stmt1);
    EXPECT_EQ(GMERR_OK, ret);

    int count = *(int *)args;

    int vetexlabel_count = 1023;
    for (uint32_t num = 0; num < vetexlabel_count; num++) {
        char labelName[20] = "";
        char label_schema[1024] = "";
        snprintf(label_schema, 1024,
            "[{\"type\":\"record\", \"name\":\"T%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
            "{\"name\":\"F1\", \"type\":\"int32\"}],"
            "\"keys\":[{\"node\":\"T%d\", \"name\":\"vertex_pk\", \"fields\":[\"F0\"], "
            "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
            num, num);
        snprintf(labelName, 20, "T%d", num);
        ret = GmcCreateVertexLabel(stmt1, label_schema, NULL);
        EXPECT_EQ((ret == GMERR_OK) ? GMERR_OK : GMERR_DUPLICATE_TABLE, ret);  // 仅校验建重复表

        char KvTable_Name[20] = "";
        snprintf(KvTable_Name, 20, "kv_%d", num);
        ret = GmcKvCreateTable(stmt1, KvTable_Name, NULL);
        EXPECT_EQ((ret == GMERR_OK) ? GMERR_OK : GMERR_DUPLICATE_TABLE, ret);
    }

    ret = testGmcDisconnect(conn1, stmt1);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}
// 013.多线程并发创建同名kv&vertex
// 系统级开关关闭, 表级开关不设置, 针对非轻量化表, 预期支持
TEST_F(LightTransClose, DML_087_LightTransClose_test_013)
{
    int ret = 0;
	char errorCode1[128] = {0};
    char errorCode2[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_DUPLICATE_TABLE);
    (void)snprintf(errorCode2, 128, "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(2, errorCode1, errorCode2);
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int index[TREAD_NUM] = {0};
    pthread_t thr_arr_01[TREAD_NUM];
    for (int i = 0; i < TREAD_NUM; i++) {
        index[i] = i;
        ret = pthread_create(&thr_arr_01[i], NULL, thread_ddl_013, (void *)&index[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }

    for (int i = 0; i < TREAD_NUM; i++) {
        pthread_join(thr_arr_01[i], NULL);
    }

    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int vetexlabel_count = 1023;
    for (uint32_t num = 0; num < vetexlabel_count; num++) {
        char labelName[20] = "";
        snprintf(labelName, 20, "T%d", num);
        ret = GmcDropVertexLabel(g_stmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);

        // dorp KV table
        char KvTable_Name[20] = "";
        snprintf(KvTable_Name, 20, "kv_%d", num);
        ret = GmcKvDropTable(g_stmt, (char *)KvTable_Name);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

void *thread_ddl_014(void *args)
{
    int ret;
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    ret = testGmcConnect(&conn1, &stmt1);
    EXPECT_EQ(GMERR_OK, ret);

    int count = *(int *)args;
    int vetexlabel_count = 8;

    GmcBatchRetT batchRet;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn1, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    // 批量添加DDL命令
    for (int i = vetexlabel_count * count; i < vetexlabel_count * count + vetexlabel_count; i++) {
        char labelName[128] = "";
        sprintf(labelName, "KvTabel_%d", i);
        ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_KV_TABLE, labelName, NULL, NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(totalNum, 8);
    EXPECT_EQ(successNum, 8);
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(conn1, stmt1);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

// 014.多线程并发批量创建不同名kv：1024 128*8
// 系统级开关关闭, 表级开关不设置, 针对非轻量化表, 预期支持
TEST_F(LightTransClose, DML_087_LightTransClose_test_014)
{
    int ret = 0;
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    int index[TREAD_NUM] = {0};
    pthread_t thr_arr_01[TREAD_NUM];
    for (int i = 0; i < TREAD_NUM; i++) {
        index[i] = i;
        ret = pthread_create(&thr_arr_01[i], NULL, thread_ddl_014, (void *)&index[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }

    for (int i = 0; i < TREAD_NUM; i++) {
        pthread_join(thr_arr_01[i], NULL);
    }

    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int vetexlabel_count = 1024;
    for (uint32_t num = 0; num < vetexlabel_count; num++) {
        // dorp KV table
        char KvTable_Name[20] = "";
        snprintf(KvTable_Name, 20, "KvTabel_%d", num);
        ret = GmcKvDropTable(g_stmt, (char *)KvTable_Name);
        ASSERT_EQ(GMERR_OK, ret);
    }
}

void *thread_ddl_015(void *args)
{
    int ret;
    int start_num = *(int *)args;
    int vetexlabel_count = 8;
    AsyncUserDataT data = {0};
    GmcStmtT *stmt_async = NULL;
    GmcConnT *conn_async = NULL;
    ret = testGmcConnect(&conn_async, &stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    // 异步批量创建
    GmcBatchRetT batchRet;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    // int tabel_num = 1024;
    for (int i = start_num * vetexlabel_count; i < start_num * vetexlabel_count + vetexlabel_count; i++) {
        char labelName[128] = "";
        sprintf(labelName, "KvTabel_%d", i);
        ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_KV_TABLE, labelName, NULL, NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(8, data.totalNum);
    EXPECT_EQ(8, data.succNum);
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(conn_async, stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

// 015.多线程异步并发批量创建不同名kv：1024 128*8
// 系统级开关关闭, 表级开关不设置, 针对非轻量化表, 预期支持
TEST_F(LightTransClose, DML_087_LightTransClose_test_015)
{
    int ret = 0;
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    int index[TREAD_NUM] = {0};
    pthread_t thr_arr_01[TREAD_NUM];
    for (int i = 0; i < TREAD_NUM; i++) {
        index[i] = i;
        ret = pthread_create(&thr_arr_01[i], NULL, thread_ddl_015, (void *)&index[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }

    for (int i = 0; i < TREAD_NUM; i++) {
        pthread_join(thr_arr_01[i], NULL);
    }

    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int vetexlabel_count = 1024;
    for (uint32_t num = 0; num < vetexlabel_count; num++) {
        // dorp KV table
        char KvTable_Name[20] = "";
        snprintf(KvTable_Name, 20, "KvTabel_%d", num);
        ret = GmcKvDropTable(g_stmt, (char *)KvTable_Name);
        ASSERT_EQ(GMERR_OK, ret);
    }
}

void *thread_ddl_016(void *args)
{
    int ret;
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    ret = testGmcConnect(&conn1, &stmt1);
    EXPECT_EQ(GMERR_OK, ret);

    int count = *(int *)args;  // thread id
    int vetexlabel_count = 8;
    for (uint32_t num = vetexlabel_count * count; num < vetexlabel_count * count + vetexlabel_count;
         num++) {  // 64*8=512 (src+dst=1024)
        // src
        char labelName_src[20] = "";
        char label_schema_src[1024] = "";
        snprintf(label_schema_src, 1024,
            "[{\"type\":\"record\", \"name\":\"Vertex01_%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
            "{\"name\":\"F1\", \"type\":\"int32\"},"
            "{\"name\":\"F2\", \"type\":\"int32\"},"
            "{\"name\":\"F3\", \"type\":\"int32\"},"
            "{\"name\":\"F4\", \"type\":\"int32\"},"
            "{\"name\":\"F5\", \"type\":\"int32\"},"
            "{\"name\":\"F6\", \"type\":\"int32\"},"
            "{\"name\":\"F7\", \"type\":\"int32\"}],"
            "\"keys\":[{\"node\":\"Vertex01_%d\", \"name\":\"vertex_pk\", \"fields\":[\"F0\"], "
            "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
            num, num);
        ret = GmcCreateVertexLabel(stmt1, label_schema_src, NULL);
        EXPECT_EQ(GMERR_OK, ret);

        // dst
        char labelName_dst[20] = "";
        char label_schema_dst[1024] = "";
        snprintf(label_schema_dst, 1024,
            "[{\"type\":\"record\", \"name\":\"Vertex02_%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
            "{\"name\":\"F1\", \"type\":\"int32\"},"
            "{\"name\":\"F2\", \"type\":\"int32\"},"
            "{\"name\":\"F3\", \"type\":\"int32\"},"
            "{\"name\":\"F4\", \"type\":\"int32\"},"
            "{\"name\":\"F5\", \"type\":\"int32\"},"
            "{\"name\":\"F6\", \"type\":\"int32\"},"
            "{\"name\":\"F7\", \"type\":\"int32\"}],"
            "\"keys\":[{\"node\":\"Vertex02_%d\", \"name\":\"vertex_pk\", \"fields\":[\"F0\"], "
            "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
            num, num);
        ret = GmcCreateVertexLabel(stmt1, label_schema_dst, NULL);
        EXPECT_EQ(GMERR_OK, ret);

        // edge
        char labelName_edge[20] = "";
        char label_schema_edge[1024] = "";
        sprintf(label_schema_edge,
            "[{\"name\":\"edge_%d\",\"source_vertex_label\":\"Vertex01_%d\",\"comment\": \"the edge "
            "xxx\",\"dest_vertex_label\":\"Vertex02_%d\","
            "\"constraint\":{\"operator_type\":\"and\",\"conditions\":[{\"source_property\": \"F1\",\"dest_property\": "
            "\"F1\"},"
            "{\"source_property\": \"F2\",\"dest_property\": \"F2\"},"
            "{\"source_property\": \"F3\",\"dest_property\": \"F3\"},"
            "{\"source_property\": \"F4\",\"dest_property\": \"F4\"},"
            "{\"source_property\": \"F5\",\"dest_property\": \"F5\"},"
            "{\"source_property\": \"F6\",\"dest_property\": \"F6\"},"
            "{\"source_property\": \"F7\",\"dest_property\": \"F7\"}]}}]",
            num, num, num);
        ret = GmcCreateEdgeLabel(stmt1, label_schema_edge, NULL);
        EXPECT_EQ(GMERR_OK, ret);

        // drop edge and drop vertex
        sprintf(labelName_edge, "edge_%d", num);
        ret = GmcDropEdgeLabel(stmt1, labelName_edge);
        EXPECT_EQ(GMERR_OK, ret);

        snprintf(labelName_src, 20, "Vertex01_%d", num);
        ret = GmcDropVertexLabel(stmt1, labelName_src);
        EXPECT_EQ(GMERR_OK, ret);

        snprintf(labelName_dst, 20, "Vertex02_%d", num);
        ret = GmcDropVertexLabel(stmt1, labelName_dst);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(conn1, stmt1);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

// 016.多线程并发创建删除不同名且带边的vertex：src+dst=1024
// 系统级开关关闭, 表级开关不设置, 针对非轻量化表, 预期支持
TEST_F(LightTransClose, DML_087_LightTransClose_test_016)
{
    int ret = 0;
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    int index[TREAD_NUM / 2] = {0};
    pthread_t thr_arr_01[TREAD_NUM / 2];
    for (int i = 0; i < TREAD_NUM / 2; i++) {
        index[i] = i;
        ret = pthread_create(&thr_arr_01[i], NULL, thread_ddl_016, (void *)&index[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }

    for (int i = 0; i < TREAD_NUM / 2; i++) {
        pthread_join(thr_arr_01[i], NULL);
    }

    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

