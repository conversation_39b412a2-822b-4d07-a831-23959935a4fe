/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */
#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/prctl.h>
#include <sys/socket.h>
#include <sys/epoll.h>
#include <assert.h>
#include <errno.h>
#include <stdint.h>
#include <typeinfo>
#include <limits.h>
#include "gtest/gtest.h"
#include "jansson.h"
#include "t_datacom_lite.h"

#define MAX_CMD_SIZE 1024
GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
char *g_schemaNormal = NULL;
class indexShmOut : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/modifyCfg.sh \"maxNormalTableNum=10000\"");
        system("sh $TEST_HOME/tools/modifyCfg.sh \"hashClusterBucketsCount=10000000\"");
        system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSeMem=8\"");
        system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalShmSize=32\""); // maxSysShmSize
        system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysShmSize=12\"");
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        int ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void indexShmOut::SetUp()
{
    g_conn = NULL;
    g_stmt = NULL;
    char errorMsg1[128] = {};
    char errorMsg2[128] = {};
    char errorMsg3[128] = {};
    char errorMsg4[128] = {};
    char errorMsg5[128] = {};
    char errorMsg6[128] = {};
    char errorMsg7[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_OUT_OF_MEMORY);
    (void)snprintf(errorMsg2, sizeof(errorMsg1), "GMERR-%d", GMERR_MEMORY_OPERATE_FAILED);
    (void)snprintf(errorMsg3, sizeof(errorMsg1), "GMERR-%d", GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD);
    (void)snprintf(errorMsg4, sizeof(errorMsg1), "GMERR-%d", GMERR_INSUFFICIENT_RESOURCES);
    (void)snprintf(errorMsg5, sizeof(errorMsg1), "GMERR-%d", GMERR_NULL_VALUE_NOT_ALLOWED);
    (void)snprintf(errorMsg6, sizeof(errorMsg1), "GMERR-%d", GMERR_PROGRAM_LIMIT_EXCEEDED);
    (void)snprintf(errorMsg7, sizeof(errorMsg1), "GMERR-%d", GMERR_INTERNAL_ERROR);
    AW_ADD_ERR_WHITE_LIST(7, errorMsg1, errorMsg2, errorMsg3, errorMsg4, errorMsg5, errorMsg6, errorMsg7);
    int ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}
void indexShmOut::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 001.共享内存调到最小, 唯一localhas索引写入
TEST_F(indexShmOut, DML_087_indexShmOut_test_001)
{
    AW_FUN_Log(LOG_STEP, "indexShmOut start.");
    int ret = 0;
    int num = 0;
    char labelName[20] = "";
    char labelSchema[1024] = "";
    snprintf(labelSchema, 1024,
        "[{\"type\":\"record\", \"name\":\"Tx%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
        "{\"name\":\"F1\", \"type\":\"int32\"},"
        "{\"name\":\"F2\", \"type\":\"int32\"}],"
        "\"keys\":[{\"node\":\"Tx%d\", \"name\":\"vertex_pk\", \"fields\":[\"F0\"], "
        "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}},"
        "{\"node\":\"Tx%d\", \"name\":\"localhash_key\", \"fields\":[\"F1\"], "
        "\"index\":{\"type\":\"localhash\"},\"constraints\":{ \"unique\":true}}]}]",
        num, num, num);
    snprintf(labelName, 20, "Tx%d", num);
    ret = GmcCreateVertexLabel(g_stmt, labelSchema, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int i = 1;
    while (i) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F0Value = i;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F2Value = i;
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        if (ret != 0) {
            EXPECT_EQ(GMERR_OUT_OF_MEMORY, ret); // 83518
            AW_FUN_Log(LOG_DEBUG, "[INFO]------------------LABEL_CNT: %d", i);
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
        i++;
    }

    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "indexShmOut end.");
}

// 002.共享内存调到最小, 非唯一localhas索引写入
TEST_F(indexShmOut, DML_087_indexShmOut_test_002)
{
    AW_FUN_Log(LOG_STEP, "indexShmOut start.");
    int ret = 0;
    int num = 0;
    char labelName[20] = "";
    char labelSchema[1024] = "";
    snprintf(labelSchema, 1024,
        "[{\"type\":\"record\", \"name\":\"Tx%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
        "{\"name\":\"F1\", \"type\":\"int32\"},"
        "{\"name\":\"F2\", \"type\":\"int32\"}],"
        "\"keys\":[{\"node\":\"Tx%d\", \"name\":\"vertex_pk\", \"fields\":[\"F0\"], "
        "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}},"
        "{\"node\":\"Tx%d\", \"name\":\"localhash_key\", \"fields\":[\"F1\"], "
        "\"index\":{\"type\":\"localhash\"},\"constraints\":{ \"unique\":false}}]}]",
        num, num, num);
    snprintf(labelName, 20, "Tx%d", num);
    ret = GmcCreateVertexLabel(g_stmt, labelSchema, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int i = 1;
    while (i) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F0Value = i;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F2Value = i;
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        if (ret != 0) {
            EXPECT_EQ(GMERR_OUT_OF_MEMORY, ret); // 83518
            AW_FUN_Log(LOG_DEBUG, "[INFO]------------------LABEL_CNT: %d", i);
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
        i++;
    }

    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "indexShmOut end.");
}

// 003.共享内存调到最小, local索引写入
TEST_F(indexShmOut, DML_087_indexShmOut_test_003)
{
    AW_FUN_Log(LOG_STEP, "indexShmOut start.");
    int ret = 0;
    int num = 0;
    char labelName[20] = "";
    char labelSchema[1024] = "";
    snprintf(labelSchema, 1024,
        "[{\"type\":\"record\", \"name\":\"Tx%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
        "{\"name\":\"F1\", \"type\":\"int32\"},"
        "{\"name\":\"F2\", \"type\":\"int32\"}],"
        "\"keys\":[{\"node\":\"Tx%d\", \"name\":\"vertex_pk\", \"fields\":[\"F0\"], "
        "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}},"
        "{\"node\":\"Tx%d\", \"name\":\"local_key\", \"fields\":[\"F1\"], "
        "\"index\":{\"type\":\"local\"},\"constraints\":{ \"unique\":false}}]}]",
        num, num, num);
    snprintf(labelName, 20, "Tx%d", num);
    ret = GmcCreateVertexLabel(g_stmt, labelSchema, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int i = 1;
    while (i) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F0Value = i;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F2Value = i;
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        if (ret != 0) {
            EXPECT_EQ(GMERR_OUT_OF_MEMORY, ret); // 83518
            AW_FUN_Log(LOG_DEBUG, "[INFO]------------------LABEL_CNT: %d", i);
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
        i++;
    }

    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "indexShmOut end.");
}

// 004.local索引写入, 结束对账后重新写入, 查询
TEST_F(indexShmOut, DML_087_indexShmOut_test_004)
{
    AW_FUN_Log(LOG_STEP, "indexShmOut start.");
    int ret = 0;
    int num = 0;
    char labelName[20] = "";
    char labelSchema[1024] = "";
    snprintf(labelSchema, 1024,
        "[{\"type\":\"record\", \"name\":\"Tx%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
        "{\"name\":\"F1\", \"type\":\"int32\"},"
        "{\"name\":\"F2\", \"type\":\"int32\"}],"
        "\"keys\":[{\"node\":\"Tx%d\", \"name\":\"vertex_pk\", \"fields\":[\"F0\"], "
        "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}},"
        "{\"node\":\"Tx%d\", \"name\":\"local_key\", \"fields\":[\"F1\"], "
        "\"index\":{\"type\":\"local\"},\"constraints\":{ \"unique\":false}}]}]",
        num, num, num);
    snprintf(labelName, 20, "Tx%d", num);
    ret = GmcCreateVertexLabel(g_stmt, labelSchema, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    int count = 10;
    for (int i = 0; i < count; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F0Value = i;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F2Value = i;
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcBeginCheck(g_stmt, labelName, 0xff);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(g_stmt, labelName, 0xff, false);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < count; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F0Value = i;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F2Value = i;
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // scan Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        cnt++;
    }
    EXPECT_EQ(count, cnt);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "indexShmOut end.");
}

// 005.lpm索引写入, 结束对账后重新写入, 查询
TEST_F(indexShmOut, DML_087_indexShmOut_test_005)
{
    AW_FUN_Log(LOG_STEP, "indexShmOut start.");
    int ret = 0;
    int num = 0;
    char labelName[20] = "";
    char labelSchema[1024] = "";
    snprintf(labelSchema, 1024,
        "[{\"type\":\"record\", \"name\":\"Tx%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
        "{\"name\":\"F1\", \"type\":\"int32\"},"
        "{\"name\":\"vr_id\", \"type\":\"uint32\"},"
        "{\"name\":\"vrf_index\", \"type\":\"uint32\"},"
        "{\"name\":\"dest_ip_addr\", \"type\":\"uint32\"},"
        "{\"name\":\"mask_len\", \"type\":\"uint8\"},"
        "{\"name\":\"F2\", \"type\":\"int32\"}],"
        "\"keys\":[{\"node\":\"Tx%d\", \"name\":\"vertex_pk\", \"fields\":[\"F0\"], "
        "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}},"
        "{\"node\":\"Tx%d\", \"name\":\"local_key\", \"fields\":[\"F1\"], "
        "\"index\":{\"type\":\"local\"},\"constraints\":{ \"unique\":false}}]}]",
        num, num, num);
    snprintf(labelName, 20, "Tx%d", num);
    ret = GmcCreateVertexLabel(g_stmt, labelSchema, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    int count = 10;
    for (int i = 0; i < count; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F0Value = i;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t vrId = 1;
        ret = GmcSetVertexProperty(g_stmt, "vr_id", GMC_DATATYPE_UINT32, &vrId, sizeof(vrId));
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t vrfIndex = 1;
        ret = GmcSetVertexProperty(g_stmt, "vrf_index", GMC_DATATYPE_UINT32, &vrfIndex, sizeof(vrfIndex));
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t destIpAddr = i;
        ret = GmcSetVertexProperty(g_stmt, "dest_ip_addr", GMC_DATATYPE_UINT32, &destIpAddr, sizeof(destIpAddr));
        EXPECT_EQ(GMERR_OK, ret);
        uint8_t maskLen = 32;
        ret = GmcSetVertexProperty(g_stmt, "mask_len", GMC_DATATYPE_UINT8, &maskLen, sizeof(maskLen));
        EXPECT_EQ(GMERR_OK, ret);

        int32_t F2Value = i;
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcBeginCheck(g_stmt, labelName, 0xff);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(g_stmt, labelName, 0xff, false);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < count; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F0Value = i;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t vrId = 1;
        ret = GmcSetVertexProperty(g_stmt, "vr_id", GMC_DATATYPE_UINT32, &vrId, sizeof(vrId));
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t vrfIndex = 1;
        ret = GmcSetVertexProperty(g_stmt, "vrf_index", GMC_DATATYPE_UINT32, &vrfIndex, sizeof(vrfIndex));
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t destIpAddr = i;
        ret = GmcSetVertexProperty(g_stmt, "dest_ip_addr", GMC_DATATYPE_UINT32, &destIpAddr, sizeof(destIpAddr));
        EXPECT_EQ(GMERR_OK, ret);
        uint8_t maskLen = 32;
        ret = GmcSetVertexProperty(g_stmt, "mask_len", GMC_DATATYPE_UINT8, &maskLen, sizeof(maskLen));
        EXPECT_EQ(GMERR_OK, ret);

        int32_t F2Value = i;
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // scan Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        cnt++;
    }
    EXPECT_EQ(count, cnt);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "indexShmOut end.");
}

// 006.共享内存调到最小, lpm索引写入
TEST_F(indexShmOut, DML_087_indexShmOut_test_006)
{
    AW_FUN_Log(LOG_STEP, "indexShmOut start.");
    int ret = 0;
    int num = 0;
    char labelName[20] = "";
    char labelSchema[1024] = "";
    snprintf(labelSchema, 1024,
        "[{\"type\":\"record\", \"name\":\"Tx%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
        "{\"name\":\"F1\", \"type\":\"int32\"},"
        "{\"name\":\"vr_id\", \"type\":\"uint32\"},"
        "{\"name\":\"vrf_index\", \"type\":\"uint32\"},"
        "{\"name\":\"dest_ip_addr\", \"type\":\"uint32\"},"
        "{\"name\":\"mask_len\", \"type\":\"uint8\"},"
        "{\"name\":\"F2\", \"type\":\"int32\"}],"
        "\"keys\":[{\"node\":\"Tx%d\", \"name\":\"vertex_pk\", \"fields\":[\"F0\"], "
        "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}},"
        "{\"node\":\"Tx%d\", \"name\":\"local_key\", \"fields\":[\"F1\"], "
        "\"index\":{\"type\":\"local\"},\"constraints\":{ \"unique\":false}}]}]",
        num, num, num);
    snprintf(labelName, 20, "Tx%d", num);
    ret = GmcCreateVertexLabel(g_stmt, labelSchema, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    int i = 1;
    while (i) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F0Value = i;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t vrId = 1;
        ret = GmcSetVertexProperty(g_stmt, "vr_id", GMC_DATATYPE_UINT32, &vrId, sizeof(vrId));
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t vrfIndex = 1;
        ret = GmcSetVertexProperty(g_stmt, "vrf_index", GMC_DATATYPE_UINT32, &vrfIndex, sizeof(vrfIndex));
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t destIpAddr = i;
        ret = GmcSetVertexProperty(g_stmt, "dest_ip_addr", GMC_DATATYPE_UINT32, &destIpAddr, sizeof(destIpAddr));
        EXPECT_EQ(GMERR_OK, ret);
        uint8_t maskLen = 32;
        ret = GmcSetVertexProperty(g_stmt, "mask_len", GMC_DATATYPE_UINT8, &maskLen, sizeof(maskLen));
        EXPECT_EQ(GMERR_OK, ret);

        int32_t F2Value = i;
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        if (ret != 0) {
            EXPECT_EQ(GMERR_OUT_OF_MEMORY, ret);
            AW_FUN_Log(LOG_DEBUG, "[INFO]------------------LABEL_CNT: %d", i);
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
        i++;
    }

    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "indexShmOut end.");
}

// 007.共享内存阈值调到最小, hashcluster索引建表
TEST_F(indexShmOut, DML_087_indexShmOut_test_007)
{
    AW_FUN_Log(LOG_STEP, "indexShmOut start.");
    int ret = 0;
    int flag = 0;
    int tableNum = 5000;
    for (int num = 0; num < tableNum; num++) {
        char labelSchema[1024] = "";
        snprintf(labelSchema, 1024,
            "[{\"type\":\"record\", \"name\":\"Tx%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
            "{\"name\":\"F1\", \"type\":\"int32\"},"
            "{\"name\":\"F2\", \"type\":\"int32\"}],"
            "\"keys\":[{\"node\":\"Tx%d\", \"name\":\"vertex_pk\", \"fields\":[\"F0\"], "
            "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}},"
            "{\"node\":\"Tx%d\", \"name\":\"hashcluster_key\", \"fields\":[\"F1\"], "
            "\"index\":{\"type\":\"hashcluster\"},\"constraints\":{ \"unique\":false}}]}]",
            num, num, num);
        ret = GmcCreateVertexLabel(g_stmt, labelSchema, NULL);
        if (ret != 0) {
            flag = num;
            EXPECT_EQ(true, ret == GMERR_OUT_OF_MEMORY || ret == GMERR_INSUFFICIENT_RESOURCES);
            AW_FUN_Log(LOG_DEBUG, "[INFO]------------------LABEL_CNT: %d", num);
            testGmcGetLastError(NULL);
            break;
        } else {
            flag++;
        }
        EXPECT_EQ(GMERR_OK, ret);
    }

    for (int num = 0; num < flag; num++) {
        char labelName[20] = "";
        snprintf(labelName, 20, "Tx%d", num);
        ret = GmcDropVertexLabel(g_stmt, labelName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "indexShmOut end.");
}

// 008.两个点表不断建边, 触发了共享内存达到上限后查询视图
TEST_F(indexShmOut, DML_087_indexShmOut_test_008)
{
    AW_FUN_Log(LOG_STEP, "indexShmOut start.");
    int ret = 0;
    char labelConfig[] = "{\"isFastReadUncommitted\":0}";
    char *vertexLabelJson1 = NULL;
    char *vertexLabelJson2 = NULL;
    const char *vertexLabelName1 = "M1";
    const char *vertexLabelName2 = "M2";
    readJanssonFile("schemaFile/VertexLabel1.gmjson", &vertexLabelJson1);
    ASSERT_NE((void *)NULL, vertexLabelJson1);
    ret = GmcCreateVertexLabel(g_stmt, vertexLabelJson1, labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    readJanssonFile("schemaFile/VertexLabel2.gmjson", &vertexLabelJson2);
    ASSERT_NE((void *)NULL, vertexLabelJson2);
    ret = GmcCreateVertexLabel(g_stmt, vertexLabelJson2, labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(vertexLabelJson1);
    free(vertexLabelJson2);

    char edgeLabelName[32] = "";
    char edgeLabelJson[1024] = "";
    int successCount = 0;
    int edgeLabelCount = 1024;
    for (int i = 0; i < edgeLabelCount; i++) {
        (void)sprintf(edgeLabelJson,
            "[{\"name\":\"testT%d\",\"source_vertex_label\":\"M1\",\"comment\": \"the edge "
            "xxx\",\"dest_vertex_label\":\"M2\","
            "\"constraint\":{\"operator_type\":\"and\",\"conditions\":[{\"source_property\": \"F7\",\"dest_property\": "
            "\"F7\"},"
            "{\"source_property\": \"F9\",\"dest_property\": \"F9\"}]}}]",
            i);
        (void)sprintf(edgeLabelName, "testT%d", i);
        ret = GmcCreateEdgeLabel(g_stmt, edgeLabelJson, labelConfig);
        if (ret == GMERR_OUT_OF_MEMORY || ret == GMERR_MEMORY_OPERATE_FAILED) {
            successCount = i;
            testGmcGetLastError(NULL);
            AW_FUN_Log(LOG_DEBUG, "[INFO]------------------EDGE_LABEL_CNT: %d", i);
            system("gmsysview -q V\\$COM_SHMEM_CTX"); // 预期查询失败, 且服务没问题
            break;
        } else {
            successCount = i;
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }

    for (int i = 0; i < successCount; i++) {
        sprintf(edgeLabelName, "testT%d", i);
        ret = GmcDropEdgeLabel(g_stmt, edgeLabelName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(g_stmt, vertexLabelName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, vertexLabelName2);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "indexShmOut end.");
}
