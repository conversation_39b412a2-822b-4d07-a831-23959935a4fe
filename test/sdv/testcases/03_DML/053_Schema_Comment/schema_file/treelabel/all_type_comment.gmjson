[{"comment": "TREELABEL", "type": "record", "name": "all_type", "fields": [{"name": "F0", "type": "int8", "nullable": false, "comment": "666"}, {"name": "F1", "type": "uint8", "nullable": true, "comment": "666"}, {"name": "F2", "type": "int16", "nullable": true, "comment": "666"}, {"name": "F3", "type": "uint16", "nullable": true, "comment": "666"}, {"name": "F4", "type": "int32", "nullable": true, "comment": "666"}, {"name": "F5", "type": "uint32", "nullable": true, "comment": "666"}, {"name": "F6", "type": "int64", "nullable": true, "comment": "666"}, {"name": "F7", "type": "uint64", "nullable": true, "comment": "666"}, {"name": "F8", "type": "boolean", "nullable": true, "comment": "666"}, {"name": "F9", "type": "float", "nullable": true, "comment": "666"}, {"name": "F10", "type": "double", "nullable": true, "comment": "666"}, {"name": "F11", "type": "time", "nullable": true, "comment": "666"}, {"name": "F12", "type": "char", "nullable": true, "comment": "666"}, {"name": "F13", "type": "uchar", "nullable": true, "comment": "666"}, {"name": "F14", "type": "string", "size": 100, "nullable": true, "comment": "666"}, {"name": "F15", "type": "bytes", "size": 7, "nullable": true, "comment": "666"}, {"name": "F16", "type": "fixed", "size": 7, "nullable": true, "comment": "666"}, {"name": "T1", "type": "record", "fields": [{"name": "P0", "type": "int8", "nullable": true, "comment": "666"}, {"name": "P1", "type": "uint8", "nullable": true, "comment": "666"}, {"name": "P2", "type": "int16", "nullable": true, "comment": "666"}, {"name": "P3", "type": "uint16", "nullable": true, "comment": "666"}, {"name": "P4", "type": "int32", "nullable": true, "comment": "666"}, {"name": "P5", "type": "uint32", "nullable": true, "comment": "666"}, {"name": "P6", "type": "int64", "nullable": true, "comment": "666"}, {"name": "P7", "type": "uint64", "nullable": true, "comment": "666"}, {"name": "P8", "type": "boolean", "nullable": true, "comment": "666"}, {"name": "P9", "type": "float", "nullable": true, "comment": "666"}, {"name": "P10", "type": "double", "nullable": true, "comment": "666"}, {"name": "P11", "type": "time", "nullable": true, "comment": "666"}, {"name": "P12", "type": "char", "nullable": true, "comment": "666"}, {"name": "P13", "type": "uchar", "nullable": true, "comment": "666"}, {"name": "P14", "type": "string", "size": 100, "nullable": true, "comment": "666"}, {"name": "P15", "type": "bytes", "size": 7, "nullable": true, "comment": "666"}, {"name": "P16", "type": "fixed", "size": 7, "nullable": true, "comment": "666"}, {"name": "T2", "type": "record", "comment": "666", "fixed_array": true, "size": 3, "fields": [{"name": "A0", "type": "int8", "nullable": true, "comment": "666"}, {"name": "A1", "type": "uint8", "nullable": true, "comment": "666"}, {"name": "A2", "type": "int16", "nullable": true, "comment": "666"}, {"name": "A3", "type": "uint16", "nullable": true, "comment": "666"}, {"name": "A4", "type": "int32", "nullable": true, "comment": "666"}, {"name": "A5", "type": "uint32", "nullable": true, "comment": "666"}, {"name": "A6", "type": "int64", "nullable": true, "comment": "666"}, {"name": "A7", "type": "uint64", "nullable": true, "comment": "666"}, {"name": "A8", "type": "boolean", "nullable": true, "comment": "666"}, {"name": "A9", "type": "float", "nullable": true, "comment": "666"}, {"name": "A10", "type": "double", "nullable": true, "comment": "666"}, {"name": "A11", "type": "time", "nullable": true, "comment": "666"}, {"name": "A12", "type": "char", "nullable": true, "comment": "666"}, {"name": "A13", "type": "uchar", "nullable": true, "comment": "666"}, {"name": "A14", "type": "string", "size": 100, "nullable": true, "comment": "666"}, {"name": "A15", "type": "bytes", "size": 7, "nullable": true, "comment": "666"}, {"name": "A16", "type": "fixed", "size": 7, "nullable": true, "comment": "666"}]}]}, {"name": "T3", "type": "record", "comment": "666", "vector": true, "size": 3, "fields": [{"name": "V0", "type": "int8", "nullable": true, "comment": "666"}, {"name": "V1", "type": "uint8", "nullable": true, "comment": "666"}, {"name": "V2", "type": "int16", "nullable": true, "comment": "666"}, {"name": "V3", "type": "uint16", "nullable": true, "comment": "666"}, {"name": "V4", "type": "int32", "nullable": true, "comment": "666"}, {"name": "V5", "type": "uint32", "nullable": true, "comment": "666"}, {"name": "V6", "type": "int64", "nullable": true, "comment": "666"}, {"name": "V7", "type": "uint64", "nullable": true, "comment": "666"}, {"name": "V8", "type": "boolean", "nullable": true, "comment": "666"}, {"name": "V9", "type": "float", "nullable": true, "comment": "666"}, {"name": "V10", "type": "double", "nullable": true, "comment": "666"}, {"name": "V11", "type": "time", "nullable": true, "comment": "666"}, {"name": "V12", "type": "char", "nullable": true, "comment": "666"}, {"name": "V13", "type": "uchar", "nullable": true, "comment": "666"}, {"name": "V14", "type": "string", "size": 100, "nullable": true, "comment": "666"}, {"name": "V15", "type": "bytes", "size": 7, "nullable": true, "comment": "666"}, {"name": "V16", "type": "fixed", "size": 7, "nullable": true, "comment": "666"}]}], "keys": [{"node": "all_type", "name": "OP_PK", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "all_type", "name": "localhash_key", "index": {"type": "<PERSON><PERSON><PERSON>"}, "fields": ["F0"], "constraints": {"unique": true}}]}]