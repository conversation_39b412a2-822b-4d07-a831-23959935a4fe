/*****************************************************************************
 Description  : hashcluster 非唯一索引
 History      :
 Author       : houjia hwx390087
 Modification :
 Date         : 2021/3/23
*****************************************************************************/
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <stdint.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "tools.h"


using namespace std;

char g_labelName[LABELNAME_MAX_LENGTH] = "ip4forward";
char g_configJson[128] = "{\"max_record_count\" : 999999}";
GmcStmtT *stmt_sn_sync;
const char *g_subConnName = "subConnName";
const char *g_subName = "subVertexLabel";
int res = 0;
int ret = 0;
int g_data_num = 100;
GmcConnT *g_conn_async = NULL;
GmcStmtT *g_stmt_async = NULL;

class HashCluster_V3_test : public testing::Test {
public:
    SnUserDataT *user_data;
    int *newValue;
    int *oldValue;

    static void SetUpTestCase()
    {
        // 重启server
        system("sh $TEST_HOME/tools/start.sh -f");

        res = testEnvInit();
        EXPECT_EQ(GMERR_OK, res);
        res = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, res);
    }

    static void TearDownTestCase()
    {

        res = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, res);
        testEnvClean();
    };

    virtual void SetUp();
    virtual void TearDown();
};

void HashCluster_V3_test::SetUp()
{
    printf("\n======================TEST:BEGIN======================\n");
    conn = NULL;
    stmt = NULL;
    vertexLabel = NULL;

    user_data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(user_data, 0, sizeof(SnUserDataT));

    user_data->new_value = (int *)malloc(sizeof(int) * g_data_num * 10);
    memset(user_data->new_value, 0, sizeof(int) * g_data_num * 10);

    user_data->old_value = (int *)malloc(sizeof(int) * g_data_num * 10);
    memset(user_data->old_value, 0, sizeof(int) * g_data_num * 10);

    user_data->isReplace_insert = (bool *)malloc(sizeof(bool) * g_data_num * 10);
    memset(user_data->isReplace_insert, 0, sizeof(bool) * g_data_num * 10);

    // 创建同步连接
    int ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN();
    AW_ADD_ERR_WHITE_LIST(3, "GMERR-1003001", "GMERR-1015002", "GMERR-1012000");
}

void HashCluster_V3_test::TearDown()
{
    printf("\n======================TEST:END========================\n");
    AW_CHECK_LOG_END();

    // 断开客户端连接
    GmcDropVertexLabel(stmt, g_labelName);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);

    free(user_data->new_value);
    free(user_data->old_value);
    free(user_data->isReplace_insert);
    free(user_data);
}

// 01. gmimport工具验证 V5 hashcluster索引 unique=true，建表报错; V3 unique=true，可导入成功，默认unique=false
// 2021.7.23 迭代五二级索引增强特性：hashcluster索引和local索引支持唯一索引；
TEST_F(HashCluster_V3_test, DML_030_HashCluster_V3_test_001)
{
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_hashcluster_true.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);

    // V5 hashcluster unique = true，建表接口报错
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);  // STATUS_QUERY_INVALID_CONSTRAINT
                               // ret = testGmcGetLastError(NULL);
                               // EXPECT_EQ(GMERR_OK, ret);
    GmcDropVertexLabel(stmt, g_labelName);
    free(schema_json);

    // V5 local unique = true，建表接口报错
    char *schema_json1 = NULL;
    readJanssonFile("./schema_file/ip4forward_local_true.gmjson", &schema_json1);
    ASSERT_NE((void *)NULL, schema_json1);
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json1, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);  // STATUS_QUERY_INVALID_CONSTRAINT
                               // ret = testGmcGetLastError(NULL);
                               // EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, g_labelName);
    free(schema_json1);

    // 导入 V3 schema: hashcluster unique = true
    ret = GmcDropVertexLabel(stmt, g_labelName);
    char schema_file[128] = "schema_file/ip4forward_hashcluster_true_V3.gmjson";
    char cmd[512];
    snprintf(cmd, 512, "%s/gmimport -c vschema -f %s -s %s", g_toolPath, schema_file, g_connServer);
    printf("\n[INFO] hashcluster true V3: %s\n\n", cmd);
    sleep(1);
    system(cmd);
    GmcDropVertexLabel(stmt, "ip4forward_hashcluster_true_V3");
}

// 02.gmimport工具验证 V5 hashcluster索引 unique=true，建表报错; V3 unique=true，可导入成功，默认unique=false
TEST_F(HashCluster_V3_test, DML_030_HashCluster_V3_test_002)
{

    // 导入 V3 schema: local unique = true
    ret = GmcDropVertexLabel(stmt, g_labelName);
    char cmd[512];
    char schema_file[128] = "schema_file/ip4forward_local_true_V3.gmjson";
    snprintf(cmd, 512, "%s/gmimport -c vschema -f %s -s %s", g_toolPath, schema_file, g_connServer);
    printf("\n[INFO] local true V3: %s\n\n", cmd);
    sleep(1);
    system(cmd);
    GmcDropVertexLabel(stmt, "ip4forward_local_true_V3");
}

// 03.Localhash删除，删除hashcluster字段后，hashcluster查询hashcluster不存在
TEST_F(HashCluster_V3_test, DML_030_HashCluster_V3_test_003)
{
    // 创建vertexLabel
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_hashcluster.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据
    int isPrint = 0;
    int oper_nums = 1000;
    ret = test_insert_vertex_ip4forward(stmt, 0, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);

    printf("\n========= insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_ip4forward(
        conn, "primary_key", 0, oper_nums, GMC_DATATYPE_UINT32, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // 非唯一localhash索引删除
    printf("\n========= non-unique hash index delete: qos_profile_id, nhp_group_id ==============\n");
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT16, &wr_uint16, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);  // non-uniq
    EXPECT_EQ(GMERR_OK, ret);
    // get affect row
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(oper_nums, affectRows);
    printf("\n[INFO] [ non-uniq hash remove ] affect row: %d \n\n", affectRows);

    // 根据hashcluster扫描
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "hashcluster_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT16, &wr_uint16, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    printf("\n========== non-uniq hashcluster index scan: qos_profile_id, nhp_group_id ===============\n");
    ret = test_hashInx_scan_ip4forward(stmt, "hashcluster_key", oper_nums, 0, "nonUni Hashcluster Scan", isPrint);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, scan_end - 1);  // 删除后预期为0

    GmcDropVertexLabel(stmt, g_labelName);
    GmcFreeIndexKey(stmt);
}

// 04.localhash更新，更新hashcluster字段后，hashcluster查询hashcluster存在
TEST_F(HashCluster_V3_test, DML_030_HashCluster_V3_test_004)
{
    // 创建vertexLabel
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_hashcluster.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据
    int isPrint = 0;
    int oper_nums = 1000;
    ret = test_insert_vertex_ip4forward(stmt, 0, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);

    printf("\n========= insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_ip4forward(
        conn, "primary_key", 0, oper_nums, GMC_DATATYPE_UINT32, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // 非唯一localhash索引更新hashcluster索引所在字段值
    printf("\n========= non-unique localhash index update hashcluster index fields value ==============\n");
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    // 更新非唯一hashcluster索引所在字段值
    unsigned short up_qos_profile_id = 3333;
    ret = GmcSetVertexProperty(
        stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &up_qos_profile_id, sizeof(up_qos_profile_id));
    EXPECT_EQ(GMERR_OK, ret);
    unsigned int up_nhp_group_id = 98765;
    ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT32, &up_nhp_group_id, sizeof(up_nhp_group_id));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // get affect row
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(oper_nums, affectRows);
    printf("\n[INFO] [ non-uniq hash update ] affect row: %d \n\n", affectRows);

    // 根据hashcluster扫描
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "hashcluster_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT16, &up_qos_profile_id, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &up_nhp_group_id, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    printf("\n========== non-uniq hashcluster index scan: qos_profile_id, nhp_group_id ===============\n");
    ret = test_hashInx_scan_ip4forward(stmt, "hashcluster_key", oper_nums, 0, "nonUni Hashcluster Scan", isPrint);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(oper_nums, scan_end - 1);  // 删除后预期为0

    GmcDropVertexLabel(stmt, g_labelName);
    GmcFreeIndexKey(stmt);
}

// 05.Truncate表，删除hashcluster字段后，hashcluster查询hashcluster不存在，继续写数据然后hashcluster查询
TEST_F(HashCluster_V3_test, DML_030_HashCluster_V3_test_005)
{
    // 创建vertexLabel
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_hashcluster.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据
    int isPrint = 0;
    int oper_nums = 1000;
    ret = test_insert_vertex_ip4forward(stmt, 0, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);

    printf("\n========= insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_ip4forward(
        conn, "primary_key", 0, oper_nums, GMC_DATATYPE_UINT32, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // Truncate表中数据
    ret = GmcTruncateVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);

    // hashcluster索引扫描，数据不存在
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "hashcluster_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT16, &wr_uint16, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    printf("\n========== non-uniq hashcluster index scan: qos_profile_id, nhp_group_id ===============\n");
    ret = test_hashInx_scan_ip4forward(stmt, "hashcluster_key", oper_nums, 0, "nonUni Hashcluster Scan", isPrint);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, scan_end - 1);  // 表中数据已清空

    // 重新写入相同数据，再次根据hashcluster索引扫描
    ret = test_insert_vertex_ip4forward(stmt, 0, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);

    // hashcluster scan
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "hashcluster_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT16, &wr_uint16, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    printf("\n========== non-uniq hashcluster index scan: qos_profile_id, nhp_group_id ===============\n");
    ret = test_hashInx_scan_ip4forward(stmt, "hashcluster_key", oper_nums, 0, "nonUni Hashcluster Scan", isPrint);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(oper_nums, scan_end - 1);

    GmcDropVertexLabel(stmt, g_labelName);
    GmcFreeIndexKey(stmt);
}

// 06.批量写数据，hashcluster查询hashcluster存在
TEST_F(HashCluster_V3_test, DML_030_HashCluster_V3_test_006)
{
    int isPrint = 0;
    int oper_nums = 500;

    // 创建vertexLabel
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_hashcluster.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 批量写
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (int loop = 0; loop < oper_nums; loop++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        // set_vtxLabel_pk_field_ip4forward(stmt, loop);
        set_vtxLabel_pk_field_ip4forward(stmt, loop, loop, loop, 24);
        set_vtxLabel_field_ip4forward(stmt, loop);
        // ret = GmcBatchAddVertexDML(stmt, GMC_CMD_INSERT_VERTEX);
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(oper_nums, totalNum);
    EXPECT_EQ(oper_nums, successNum);
    printf("\n[INFO][#7 %13s][ batch sync write] %d \n", g_labelName, successNum);

    // hashcluster索引扫描
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "hashcluster_key");
    EXPECT_EQ(GMERR_OK, ret);
    // "qos_profile_id", "nhp_group_id"
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT16, &wr_uint16, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    printf("\n========== non-uniq hashcluster index scan: qos_profile_id, nhp_group_id ===============\n");
    ret = test_hashInx_scan_ip4forward(stmt, "hashcluster_key", oper_nums + 2, 0, "nonUni Hashcluster Scan", isPrint);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(oper_nums, scan_end - 1);

    GmcDropVertexLabel(stmt, g_labelName);
    GmcFreeIndexKey(stmt);
}

// 07.批量删除，批量删除hashcluster字段后，hashcluster查询hashcluster不存在
TEST_F(HashCluster_V3_test, DML_030_HashCluster_V3_test_007)
{
    int isPrint = 0;
    int oper_nums = 500;

    // (1) 创建vertexLabel
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_hashcluster.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // (2) 批量写
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (int loop = 0; loop < oper_nums; loop++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        // set_vtxLabel_pk_field_ip4forward(stmt, loop);
        set_vtxLabel_pk_field_ip4forward(stmt, loop, loop, loop, 24);
        set_vtxLabel_field_ip4forward(stmt, loop);
        // ret = GmcBatchAddVertexDML(stmt, GMC_CMD_INSERT_VERTEX);
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(oper_nums, totalNum);
    EXPECT_EQ(oper_nums, successNum);
    printf("\n[INFO][#7 %13s][ batch sync write] %d \n", g_labelName, successNum);

    // (3) hashcluster索引扫描
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "hashcluster_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT16, &wr_uint16, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    printf("\n========== non-uniq hashcluster index scan: qos_profile_id, nhp_group_id ===============\n");
    ret = test_hashInx_scan_ip4forward(stmt, "hashcluster_key", oper_nums, 0, "nonUni Hashcluster Scan", isPrint);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(oper_nums, scan_end - 1);

    // (4) 批量删除
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (int loop = 0; loop < oper_nums; loop++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &loop, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &loop, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &loop, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcBatchAddDML(stmt, GMC_CMD_DELETE_VERTEX, "primary_key", NULL);
        // ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "primary_key");
        EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcBatchAddVertexDML(stmt, GMC_CMD_DELETE_VERTEX);
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(oper_nums, totalNum);
    EXPECT_EQ(oper_nums, successNum);
    printf("\n[INFO][#7 %13s][ batch sync write] %d \n", g_labelName, successNum);

    // (5) 删除后 hashcluster索引扫描
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "hashcluster_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT16, &wr_uint16, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    printf("\n========== non-uniq hashcluster index scan: qos_profile_id, nhp_group_id ===============\n");
    ret = test_hashInx_scan_ip4forward(stmt, "hashcluster_key", oper_nums, 0, "nonUni Hashcluster Scan", isPrint);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, scan_end - 1);

    GmcDropVertexLabel(stmt, g_labelName);
    GmcFreeIndexKey(stmt);
}

// 08.批量更新，更新hashcluster字段后，hashcluster查询hashcluster存在
TEST_F(HashCluster_V3_test, DML_030_HashCluster_V3_test_008)
{
    int isPrint = 0;
    int oper_nums = 500;

    // (1) 创建vertexLabel
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_hashcluster.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // (2) 批量写
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (int loop = 0; loop < oper_nums; loop++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        set_vtxLabel_pk_field_ip4forward(stmt, loop, loop, loop, 24);
        set_vtxLabel_field_ip4forward(stmt, loop);
        // ret = GmcBatchAddVertexDML(stmt, GMC_CMD_INSERT_VERTEX);
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(oper_nums, totalNum);
    EXPECT_EQ(oper_nums, successNum);
    printf("\n[INFO][#7 %13s][ batch sync write] %d \n", g_labelName, successNum);

    // (3) 批量更新hashcluster索引字段值
    unsigned short up_qos_profile_id = 3333;
    unsigned int up_nhp_group_id = 98765;
    // ret = GmcBatchPrepare(stmt);
    // ASSERT_EQ(GMERR_OK, ret);
    for (int loop = 0; loop < oper_nums; loop++) {

        ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        // set index key value
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &loop, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &loop, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &loop, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        // set update fields value
        ret = GmcSetVertexProperty(stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &up_qos_profile_id, sizeof(uint16_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT32, &up_nhp_group_id, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(stmt, "primary_key");
        EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcBatchAddVertexDML(stmt, GMC_CMD_UPDATE_VERTEX);
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(oper_nums, totalNum);
    EXPECT_EQ(oper_nums, successNum);
    printf("\n[INFO][#7 %13s][ batch sync update] %d \n", g_labelName, successNum);

    // (5) 更新后 hashcluster索引扫描
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "hashcluster_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT16, &up_qos_profile_id, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &up_nhp_group_id, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    printf("\n========== non-uniq hashcluster index scan: qos_profile_id, nhp_group_id ===============\n");
    ret = test_hashInx_scan_ip4forward(stmt, "hashcluster_key", oper_nums, 0, "nonUni Hashcluster Scan", isPrint);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(oper_nums, scan_end - 1);

    GmcDropVertexLabel(stmt, g_labelName);
    GmcFreeIndexKey(stmt);
}

// 09.非唯一hashcluster扫描记录存在
TEST_F(HashCluster_V3_test, DML_030_HashCluster_V3_test_009)
{
    // 创建vertexLabel
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_hashcluster.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据
    int isPrint = 0;
    int oper_nums = 1000;
    ret = test_insert_vertex_ip4forward(stmt, 0, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);

    printf("\n========= insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_ip4forward(
        conn, "primary_key", 0, oper_nums, GMC_DATATYPE_UINT32, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // 根据hashcluster扫描
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "hashcluster_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT16, &wr_uint16, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    printf("\n========== non-uniq hashcluster index scan: qos_profile_id, nhp_group_id ===============\n");
    ret = test_hashInx_scan_ip4forward(stmt, "hashcluster_key", oper_nums, 0, "nonUni Hashcluster Scan", isPrint);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(oper_nums, scan_end - 1);

    GmcDropVertexLabel(stmt, g_labelName);
    GmcFreeIndexKey(stmt);
}

// 10.非唯一hashcluster扫描记录不存在
TEST_F(HashCluster_V3_test, DML_030_HashCluster_V3_test_010)
{
    // 创建vertexLabel
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_hashcluster.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据
    int isPrint = 0;
    int oper_nums = 1000;
    ret = test_insert_vertex_ip4forward(stmt, 0, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);

    printf("\n========= insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_ip4forward(
        conn, "primary_key", 0, oper_nums, GMC_DATATYPE_UINT32, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // 主键删除
    for (int loop = 0; loop < oper_nums; loop++) {

        ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "primary_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &loop, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &loop, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &loop, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcDeleteVertexByIndexKey(stmt, vertexLabel, "primary_key");
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        // get affect rows
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    // 非唯一hashcluster扫描数据已删除，扫描不报错，scan num = 0
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "hashcluster_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT16, &wr_uint16, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    printf("\n========== non-uniq hashcluster index scan: qos_profile_id, nhp_group_id ===============\n");
    ret = test_hashInx_scan_ip4forward(stmt, "hashcluster_key", oper_nums, 0, "nonUni Hashcluster Scan", isPrint);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, scan_end - 1);

    GmcDropVertexLabel(stmt, g_labelName);
    GmcFreeIndexKey(stmt);
}

// 11.非唯一hashcluster更新记录存在
TEST_F(HashCluster_V3_test, DML_030_HashCluster_V3_test_011)
{
    // 创建vertexLabel
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_hashcluster.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据，主键读
    int isPrint = 0;
    int oper_nums = 1000;
    ret = test_insert_vertex_ip4forward(stmt, 0, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);
    printf("\n========= insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_ip4forward(
        conn, "primary_key", 0, oper_nums, GMC_DATATYPE_UINT32, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // 非唯一hashcluster索引更新
    printf("\n========= non-unique localhash index update hashcluster index fields value ==============\n");
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "hashcluster_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    unsigned short up_qos_profile_id = 3333;
    ret = GmcSetVertexProperty(
        stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &up_qos_profile_id, sizeof(up_qos_profile_id));
    EXPECT_EQ(GMERR_OK, ret);
    unsigned int up_nhp_group_id = 98765;
    ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT32, &up_nhp_group_id, sizeof(up_nhp_group_id));
    EXPECT_EQ(GMERR_OK, ret);
    // ret = GmcUpdateVertexByIndexKey(stmt, "hashcluster_key");
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // get affect row
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(oper_nums, affectRows);
    printf("\n[INFO] [ non-uniq hash update ] affect row: %d \n\n", affectRows);

    // 根据hashcluster扫描
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "hashcluster_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT16, &up_qos_profile_id, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &up_nhp_group_id, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    printf("\n========== non-uniq hashcluster index scan: qos_profile_id, nhp_group_id ===============\n");
    ret = test_hashInx_scan_ip4forward(stmt, "hashcluster_key", oper_nums, 0, "nonUni Hashcluster Scan", isPrint);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(oper_nums, scan_end - 1);  // 删除后预期为0

    GmcDropVertexLabel(stmt, g_labelName);
    GmcFreeIndexKey(stmt);
}

// 12.非唯一hashcluster更新记录不存在，不会新增记录
TEST_F(HashCluster_V3_test, DML_030_HashCluster_V3_test_012)
{
    // 创建vertexLabel
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_hashcluster.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据，主键读
    int isPrint = 0;
    int oper_nums = 1000;
    ret = test_insert_vertex_ip4forward(stmt, 0, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);
    printf("\n========= insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_ip4forward(
        conn, "primary_key", 0, oper_nums, GMC_DATATYPE_UINT32, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // 非唯一hashcluster索引更新，set index key value为不存在数据，更新不报错，affect rows = 0
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "hashcluster_key");
    EXPECT_EQ(GMERR_OK, ret);
    unsigned short up_qos_profile_id = 3333;
    unsigned int up_nhp_group_id = 98765;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT16, &up_qos_profile_id, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &up_nhp_group_id, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &up_qos_profile_id, sizeof(up_qos_profile_id));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT32, &up_nhp_group_id, sizeof(up_nhp_group_id));
    EXPECT_EQ(GMERR_OK, ret);
    // ret = GmcUpdateVertexByIndexKey(stmt, "hashcluster_key");
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // get affect row
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, affectRows);
    printf("\n[INFO] [ non-uniq hash update ] affect row: %d \n\n", affectRows);

    // 全表扫描
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = test_hashInx_scan_ip4forward(stmt, NULL, oper_nums, 0, "nonUni Hashcluster Scan", isPrint);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(oper_nums, scan_end - 1);

    GmcDropVertexLabel(stmt, g_labelName);
    GmcFreeIndexKey(stmt);
}

// 13.非唯一hashcluster删除记录不存在
TEST_F(HashCluster_V3_test, DML_030_HashCluster_V3_test_013)
{
    // 创建vertexLabel
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_hashcluster.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据，主键读
    int isPrint = 0;
    int oper_nums = 1;
    ret = test_insert_vertex_ip4forward(stmt, 0, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);
    printf("\n========= insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_ip4forward(
        conn, "primary_key", 0, oper_nums, GMC_DATATYPE_UINT32, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // 非唯一hashcluster索引删除不存在的数据，set index key value为不存在数据，删除不报错，affect rows = 0
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "hashcluster_key");
    EXPECT_EQ(GMERR_OK, ret);
    unsigned short up_qos_profile_id = 3333;
    unsigned int up_nhp_group_id = 98765;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT16, &up_qos_profile_id, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &up_nhp_group_id, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    // ret = GmcDeleteVertexByIndexKey(stmt, vertexLabel, "hashcluster_key");
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // get affect row
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, affectRows);
    printf("\n[INFO] [ non-uniq hashcluster delete ] affect row: %d \n\n", affectRows);

    // 全表扫描
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = test_hashInx_scan_ip4forward(stmt, NULL, oper_nums, 0, "nonUni Hashcluster Scan", isPrint);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(oper_nums, scan_end - 1);

    GmcDropVertexLabel(stmt, g_labelName);
    GmcFreeIndexKey(stmt);
}

// 14.主键删除，删除hashcluster字段后，查询hashcluster不存在
TEST_F(HashCluster_V3_test, DML_030_HashCluster_V3_test_014)
{
    // 创建vertexLabel
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_hashcluster.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据，主键读
    int isPrint = 0;
    int oper_nums = 1000;
    ret = test_insert_vertex_ip4forward(stmt, 0, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);
    printf("\n========= insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_ip4forward(conn, "primary_key", 0, oper_nums, GMC_DATATYPE_UINT32, conn_id, "PK read", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // 主键删除
    for (int loop = 0; loop < oper_nums; loop++) {

        ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "primary_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &loop, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &loop, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &loop, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        // get affect rows
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    // hashcluster索引扫描，数据不存在
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "hashcluster_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT16, &wr_uint16, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    printf("\n========== non-uniq hashcluster index scan: qos_profile_id, nhp_group_id ===============\n");
    ret = test_hashInx_scan_ip4forward(stmt, "hashcluster_key", oper_nums, 0, "nonUni Hashcluster Scan", isPrint);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, scan_end - 1);  // 表中数据已清空

    GmcDropVertexLabel(stmt, g_labelName);
    GmcFreeIndexKey(stmt);
}

// 15.唯一hashcluster扫描记录存在
TEST_F(HashCluster_V3_test, DML_030_HashCluster_V3_test_015)
{
    int isPrint = 0;
    int oper_nums = 1000;

    // gmimport工具适配导入 V3 schema: hashcluster unique = true
    ret = GmcDropVertexLabel(stmt, g_labelName);
    char schema_file[128] = "schema_file/ip4forward_hashcluster_true_V3.gmjson";
    char cmd[512];
    snprintf(cmd, 512, "%s/gmimport -c vschema -f %s -t ip4forward -s %s", g_toolPath, schema_file, g_connServer);
    system(cmd);
    printf("\n[INFO] hashcluster true V3: %s\n\n", cmd);

    // 写入数据，主键读
    ret = test_insert_vertex_ip4forward(stmt, 0, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);
    printf("\n========= insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_ip4forward(
        conn, "primary_key", 0, oper_nums, GMC_DATATYPE_UINT32, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // 唯一hashcluster扫描

    for (int loop = 0; loop < oper_nums; loop++) {

        ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "hashcluster_key");
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &loop, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &loop, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = test_hashInx_scan_ip4forward(stmt, "hashcluster_key", 2, 0, "Uniq Hashcluster Scan", 0);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, scan_end - 1);
    }

    GmcDropVertexLabel(stmt, g_labelName);
    GmcFreeIndexKey(stmt);
}

// 16.唯一hashcluster扫描记录不存在
TEST_F(HashCluster_V3_test, DML_030_HashCluster_V3_test_016)
{
    int isPrint = 0;
    int oper_nums = 1000;

    // gmimport工具适配导入 V3 schema: hashcluster unique = true
    ret = GmcDropVertexLabel(stmt, g_labelName);
    char schema_file[128] = "schema_file/ip4forward_hashcluster_true_V3.gmjson";
    char cmd[512];
    snprintf(cmd, 512, "%s/gmimport -c vschema -f %s -t ip4forward  -s %s", g_toolPath, schema_file, g_connServer);
    system(cmd);
    printf("\n[INFO] hashcluster true V3: %s\n\n", cmd);

    // 写入数据，主键读
    ret = test_insert_vertex_ip4forward(stmt, 0, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);
    printf("\n========= insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_ip4forward(
        conn, "primary_key", 0, oper_nums, GMC_DATATYPE_UINT32, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // 非唯一localhash索引删除
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");  // non-uniq
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT16, &wr_uint16, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // get affect row
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(oper_nums, affectRows);
    printf("\n[INFO] [ non-uniq localhash delete ] affect row: %d \n\n", affectRows);

    // 唯一hashcluster扫描数据不存在
    for (int loop = 0; loop < oper_nums; loop++) {

        ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "hashcluster_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &loop, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &loop, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = test_hashInx_scan_ip4forward(stmt, "hashcluster_key", 2, 0, "Uniq Hashcluster Scan", 0);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, scan_end - 1);  // 数据已删除，扫描不到数据
    }
    GmcDropVertexLabel(stmt, g_labelName);
    GmcFreeIndexKey(stmt);
}

// 17.唯一hashcluster更新记录存在
TEST_F(HashCluster_V3_test, DML_030_HashCluster_V3_test_017)
{
    int isPrint = 0;
    int oper_nums = 1000;

    // gmimport工具适配导入 V3 schema: hashcluster unique = true
    ret = GmcDropVertexLabel(stmt, g_labelName);
    char schema_file[128] = "schema_file/ip4forward_hashcluster_true_V3.gmjson";
    char cmd[512];
    snprintf(cmd, 512, "%s/gmimport -c vschema -f %s -t ip4forward -s %s", g_toolPath, schema_file, g_connServer);
    system(cmd);
    printf("\n[INFO] hashcluster true V3: %s\n\n", cmd);

    // 写入数据，主键读
    ret = test_insert_vertex_ip4forward(stmt, 0, oper_nums * 2);
    EXPECT_EQ(GMERR_OK, ret);
    printf("\n========= insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_ip4forward(
        conn, "primary_key", 0, oper_nums, GMC_DATATYPE_UINT32, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // 唯一hashcluster索引更新数据已存在
    for (int loop = 0; loop < oper_nums; loop++) {

        ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "hashcluster_key");
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &loop, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &loop, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        // 更新主键字段值为数据库中已存在的数据
        unsigned int up_val = oper_nums + 1;
        ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &up_val, sizeof(uint32_t));
        EXPECT_EQ(GMERR_INVALID_OBJECT, ret);
        ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &up_val, sizeof(uint32_t));
        EXPECT_EQ(GMERR_INVALID_OBJECT, ret);
        ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_UINT32, &up_val, sizeof(uint32_t));
        EXPECT_EQ(GMERR_INVALID_OBJECT, ret);

        // ret = GmcUpdateVertexByIndexKey(stmt, "hashcluster_key");
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);

        // get affect row
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);

    GmcDropVertexLabel(stmt, g_labelName);
    GmcFreeIndexKey(stmt);
}

// 18.唯一hashcluster更新记录不存在，不会新增记录
TEST_F(HashCluster_V3_test, DML_030_HashCluster_V3_test_018)
{
    int isPrint = 0;
    int oper_nums = 1000;

    // gmimport工具适配导入 V3 schema: hashcluster unique = true
    ret = GmcDropVertexLabel(stmt, g_labelName);
    char schema_file[128] = "schema_file/ip4forward_hashcluster_true_V3.gmjson";
    char cmd[512];
    snprintf(cmd, 512, "%s/gmimport -c vschema -f %s -t ip4forward -s %s", g_toolPath, schema_file, g_connServer);
    system(cmd);
    printf("\n[INFO] hashcluster true V3: %s\n\n", cmd);

    // 写入数据，主键读
    ret = test_insert_vertex_ip4forward(stmt, 0, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);
    printf("\n========= insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_ip4forward(
        conn, "primary_key", 0, oper_nums, GMC_DATATYPE_UINT32, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // 唯一hashcluster索引更新记录不存在
    for (int loop = 0; loop < oper_nums; loop++) {

        // 更新记录不存在
        ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "hashcluster_key");
        EXPECT_EQ(GMERR_OK, ret);
        unsigned int upt_non_exit = oper_nums + loop;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &upt_non_exit, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &upt_non_exit, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        // 更新主键字段值为数据库中已存在的数据
        unsigned int up_val = oper_nums + 1;
        ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &up_val, sizeof(uint32_t));
        EXPECT_EQ(GMERR_INVALID_OBJECT, ret);
        ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &up_val, sizeof(uint32_t));
        EXPECT_EQ(GMERR_INVALID_OBJECT, ret);
        ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_UINT32, &up_val, sizeof(uint32_t));
        EXPECT_EQ(GMERR_INVALID_OBJECT, ret);

        // ret = GmcUpdateVertexByIndexKey(stmt, "hashcluster_key");
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);

        // get affect row
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, affectRows);
    }
    GmcDropVertexLabel(stmt, g_labelName);
    GmcFreeIndexKey(stmt);
}

// 19.唯一hashcluster删除记录存在
TEST_F(HashCluster_V3_test, DML_030_HashCluster_V3_test_019)
{
    int isPrint = 0;
    int oper_nums = 1000;

    // gmimport工具适配导入 V3 schema: hashcluster unique = true
    ret = GmcDropVertexLabel(stmt, g_labelName);
    char schema_file[128] = "schema_file/ip4forward_hashcluster_true_V3.gmjson";
    char cmd[512];
    snprintf(cmd, 512, "%s/gmimport -c vschema -f %s -t ip4forward -s %s", g_toolPath, schema_file, g_connServer);
    system(cmd);
    printf("\n[INFO] hashcluster true V3: %s\n\n", cmd);

    // 写入数据，主键读
    ret = test_insert_vertex_ip4forward(stmt, 0, oper_nums * 2);
    EXPECT_EQ(GMERR_OK, ret);
    printf("\n========= insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_ip4forward(
        conn, "primary_key", 0, oper_nums, GMC_DATATYPE_UINT32, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // 唯一hashcluster索引删除已存在的数据，删除成功
    for (int loop = 0; loop < oper_nums; loop++) {

        ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "hashcluster_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &loop, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &loop, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcDeleteVertexByIndexKey(stmt, vertexLabel, "hashcluster_key");
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        // get affect row
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    GmcDropVertexLabel(stmt, g_labelName);
    GmcFreeIndexKey(stmt);
}

// 20.唯一hashcluster删除记录不存在
TEST_F(HashCluster_V3_test, DML_030_HashCluster_V3_test_020)
{
    int isPrint = 0;
    int oper_nums = 1000;

    // gmimport工具适配导入 V3 schema: hashcluster unique = true
    ret = GmcDropVertexLabel(stmt, g_labelName);
    char schema_file[128] = "schema_file/ip4forward_hashcluster_true_V3.gmjson";
    char cmd[512];
    snprintf(cmd, 512, "%s/gmimport -c vschema -f %s -t ip4forward -s %s", g_toolPath, schema_file, g_connServer);
    system(cmd);
    printf("\n[INFO] hashcluster true V3: %s\n\n", cmd);

    // 写入数据，主键读
    ret = test_insert_vertex_ip4forward(stmt, 0, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);
    printf("\n========= insert vertex, pk read, expect: %d ===============\n", oper_nums);
    ret = test_PK_read_ip4forward(
        conn, "primary_key", 0, oper_nums, GMC_DATATYPE_UINT32, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    // 唯一hashcluster索引删除不存在的数据
    for (int loop = 0; loop < oper_nums; loop++) {

        // 删除不存在的数据
        ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "hashcluster_key");
        EXPECT_EQ(GMERR_OK, ret);
        unsigned int upt_non_exit = oper_nums + loop;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &upt_non_exit, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &upt_non_exit, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcDeleteVertexByIndexKey(stmt, vertexLabel, "hashcluster_key");
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);  // 删除不存在的数据，delete接口不报错，affect rows = 0

        // get affect row
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(0, affectRows);
    }

    // 删除数据不存在，不会删除数据，主键读验证未删除数据
    ret = test_PK_read_ip4forward(
        conn, "primary_key", 0, oper_nums, GMC_DATATYPE_UINT32, conn_id, "insert vertex", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    GmcDropVertexLabel(stmt, g_labelName);
    GmcFreeIndexKey(stmt);
}
