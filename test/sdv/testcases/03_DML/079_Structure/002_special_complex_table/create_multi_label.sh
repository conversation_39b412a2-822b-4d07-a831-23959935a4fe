#!/bin/bash
# for multi-label

CUR_DIR=`pwd`


if [ $# -lt 1 ];then
    echo "usage:$0 sh [create label nums]"
    exit  1
fi

## 数据清除及准备  $CUR_DIR/multi_vertexlabel文件夹
cd $CUR_DIR
rm -r multi_vertexlabel > /dev/null 2>&1
mkdir multi_vertexlabel > /dev/null 2>&1
cp $TEST_HOME/testcases/03_DML/079_Structure/002_special_complex_table/schema_file/special_complex_table_001.gmjson ./multi_vertexlabel/special_complex_table_001.gmjson
sleep 1

# 构造多个 subinfo
cd $CUR_DIR/multi_vertexlabel
echo create_multi_label $1

for i in $(seq 0 $1)
do	
	cp special_complex_table_001.gmjson special_complex_table_001_$i.gmjson
	sed -i "s/\"name\":\"TEST_SC_T1\"/\"name\": \"TEST_SC_T1_"$i"\"/g" ./special_complex_table_001_$i.gmjson
done
