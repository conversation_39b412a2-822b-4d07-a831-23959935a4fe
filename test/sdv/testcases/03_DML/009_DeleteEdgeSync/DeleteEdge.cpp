extern "C" {
}

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <stdint.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"

// GmcStmtT *stmt;
GmcConnT *conn;
char src_VertexLabel_name[] = "T100";
char src_VertexLabel_name_PK[] = "T100_PK";
char dst_VertexLabel_name[] = "T200";
char dst_VertexLabel_name2[] = "T10";
char dst_VertexLabel_name_PK[] = "T200_PK";
char g_EdgeLabelName[] = "from_T100_to_T200";
char g_EdgeLabelNameOr[] = "from_T100_or_T200";
char g_VertexLabel_config[] = "{\"max_record_count\":1000, \"isFastReadUncommitted\":0}";
char g_EdgeLabel_config[] = "{\"max_record_count\":1000, \"isFastReadUncommitted\":0}";
char g_EdgeLabelNameAnd[] = "from_T100_to_T10";
char edgeLabel_self_to_self[] = "from_T100_to_T100";
char edgeLabel_T100_and_T200[] = "from_T100_and_T200";
class test_GmcDeleteEdgeByPrimFilter : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

// normal insert records
int insert_srcVertex_records()
{
    void *label = NULL;
    uint32_t F0_value = 1;
    uint32_t F2_value = 1;
    int16_t F3_value = 1;
    uint16_t F4_value = 0;
    GmcStmtT *stmt = NULL;
    char F1_str1[] = "testver";
    char F1_str2[] = "test";
    int32_t ret = 0;
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, src_VertexLabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int16_t i = 0; i < 100; i++) {
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0_value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        if ((i % 2) == 0) {
            ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_STRING, F1_str1, (strlen(F1_str1)));
            EXPECT_EQ(GMERR_OK, ret);
        } else {
            ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_STRING, F1_str2, (strlen(F1_str2)));
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &F2_value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT16, &F3_value, sizeof(int16_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_UINT16, &F4_value, sizeof(uint16_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ++F0_value;
        ++F2_value;
        ++F3_value;
        ++F4_value;
    }
    F0_value = 15;
    F2_value = 800;
    F3_value = 15;
    F4_value = 15;
    for (int i = 0; i < 5; i++) {

        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0_value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        if ((i % 2) == 0) {
            ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_STRING, F1_str1, (strlen(F1_str1)));
            EXPECT_EQ(GMERR_OK, ret);
        } else {
            ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_STRING, F1_str2, (strlen(F1_str2)));
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &F2_value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT16, &F3_value, sizeof(int16_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_UINT16, &F4_value, sizeof(uint16_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        F2_value++;
    }
    //单独插入一条记录与dst只有一组值相等
    F0_value = 3000;
    F2_value = 3000;
    F3_value = 3000;
    F4_value = 3000;
    char F1_str3[] = "test123";
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_STRING, F1_str3, (strlen(F1_str3)));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &F2_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT16, &F3_value, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_UINT16, &F4_value, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    GmcFreeStmt(stmt);
    return ret;
}

int insert_dstVertex_records()
{
    void *label = NULL;
    GmcStmtT *stmt = NULL;
    uint32_t F0_value = 1;
    uint32_t F2_value = 1;
    int16_t F3_value = 1;
    char F1_str1[] = "testver";
    char F1_str2[] = "test";
    int32_t ret = 0;
    uint32_t pkvalue = 500;
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, dst_VertexLabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int16_t i = 0; i < 50; i++) {
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0_value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        if ((i % 2) == 0) {
            ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_STRING, F1_str1, (strlen(F1_str1)));
            EXPECT_EQ(GMERR_OK, ret);
        } else {
            ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_STRING, F1_str2, (strlen(F1_str2)));
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &F2_value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT16, &F3_value, sizeof(int16_t));
        EXPECT_EQ(GMERR_OK, ret);
        // printf("i: %d\n",i);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ++F0_value;
        ++F2_value;
        ++F3_value;
    }
    //单独插入主键值不同,其余值相同
    for (int i = 0; i < 5; i++) {
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &pkvalue, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_STRING, F1_str2, (strlen(F1_str2)));
        EXPECT_EQ(GMERR_OK, ret);
        F2_value = 1;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &F2_value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT16, &F2_value, sizeof(int16_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        pkvalue++;
    }
    //单独插入一条记录与src只有一组值相等
    pkvalue = 4000;
    F2_value = 4000;
    F3_value = 4000;
    char F1_str3[] = "test123";
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &pkvalue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_STRING, F1_str3, (strlen(F1_str3)));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &F2_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT16, &F2_value, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);

    GmcFreeStmt(stmt);
    return ret;
}

// normal insert records
int insert_dst2Vertex_records()
{
    void *label = NULL;
    uint32_t F0_value = 50;
    uint32_t F2_value = 50;
    int16_t F3_value = 50;
    GmcStmtT *stmt = NULL;
    int32_t ret = 0;
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, dst_VertexLabel_name2, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int16_t i = 0; i < 20; i++) {
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0_value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &F2_value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT16, &F3_value, sizeof(int16_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ++F0_value;
        ++F2_value;
        ++F3_value;
    }

    GmcFreeStmt(stmt);
    return ret;
}

void test_GmcDeleteEdgeByPrimFilter::SetUpTestCase()
{
    system("$TEST_HOME/tools/start.sh");
    int32_t ret = 0;
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    ASSERT_EQ(GMERR_OK, ret);
    void *edgeLabelAnd = NULL;
    void *edgeLabelOr = NULL;
    GmcStmtT *stmt = NULL;
    char *src_vertexLabel_schema = NULL;
    char *dst_vertexLabel_schema = NULL;
    char *dst_vertexLabel_schema2 = NULL;
    char *edge_label_schema_and = NULL;
    char *edge_label_schema_or = NULL;
    char *edge_label_schema_and2 = NULL;
    char *edge_label_schema4 = NULL;
    char *edge_label_schema5 = NULL;
    ret = testGmcConnect(&conn);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);

    GmcDropGraphLabel(stmt, src_VertexLabel_name);
    GmcDropGraphLabel(stmt, dst_VertexLabel_name);
    GmcDropGraphLabel(stmt, dst_VertexLabel_name2);

    readJanssonFile("schemaFile/vertexSrcLabel_test_schema.gmjson", &src_vertexLabel_schema);
    ASSERT_NE((void *)NULL, src_vertexLabel_schema);
    readJanssonFile("schemaFile/vertexDstLabel_test_schema.gmjson", &dst_vertexLabel_schema);
    ASSERT_NE((void *)NULL, dst_vertexLabel_schema);
    readJanssonFile("schemaFile/vertexDstLabel2_test_schema.gmjson", &dst_vertexLabel_schema2);
    ASSERT_NE((void *)NULL, dst_vertexLabel_schema2);

    //创建src_vertex_label
    ret = GmcCreateVertexLabel(stmt, src_vertexLabel_schema, g_VertexLabel_config);
    ASSERT_EQ(GMERR_OK, ret);
    //创建dst_vertex_label
    ret = GmcCreateVertexLabel(stmt, dst_vertexLabel_schema, g_VertexLabel_config);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, dst_vertexLabel_schema2, g_VertexLabel_config);
    ASSERT_EQ(GMERR_OK, ret);

    free(src_vertexLabel_schema);
    free(dst_vertexLabel_schema);
    free(dst_vertexLabel_schema2);
    //创建edgelabel
    readJanssonFile("schemaFile/edge_insert_schema.gmjson", &edge_label_schema_and);
    ASSERT_NE((void *)NULL, edge_label_schema_and);
    readJanssonFile("schemaFile/edge_insert_or_schema.gmjson", &edge_label_schema_or);
    ASSERT_NE((void *)NULL, edge_label_schema_or);
    readJanssonFile("schemaFile/edge_insert_and_schema.gmjson", &edge_label_schema_and2);
    ASSERT_NE((void *)NULL, edge_label_schema_and2);
    readJanssonFile("schemaFile/edge_insert_src_dst_vertex_same_schema.gmjson", &edge_label_schema4);
    ASSERT_NE((void *)NULL, edge_label_schema4);
    readJanssonFile("schemaFile/edge_insert_and2_schema.gmjson", &edge_label_schema5);
    ASSERT_NE((void *)NULL, edge_label_schema5);

    ret = GmcCreateEdgeLabel(stmt, edge_label_schema_and, g_EdgeLabel_config);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcCreateEdgeLabel(stmt, edge_label_schema_or, g_EdgeLabel_config);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcCreateEdgeLabel(stmt, edge_label_schema_and2, g_EdgeLabel_config);
    ASSERT_EQ(GMERR_OK, ret);
    // ret=GmcCreateEdgeLabel(stmt,edge_label_schema4,g_EdgeLabel_config);
    // ASSERT_EQ(GMERR_OK,ret);
    ret = GmcCreateEdgeLabel(stmt, edge_label_schema5, g_EdgeLabel_config);
    ASSERT_EQ(GMERR_OK, ret);
    free(edge_label_schema_and);
    free(edge_label_schema_or);
    free(edge_label_schema_and2);
    free(edge_label_schema4);
    free(edge_label_schema5);
    GmcFreeStmt(stmt);
    // vertexlabel插入数据
    ret = insert_srcVertex_records();
    ASSERT_EQ(GMERR_OK, ret);
    ret = insert_dstVertex_records();
    ASSERT_EQ(GMERR_OK, ret);
    insert_dst2Vertex_records();
    ASSERT_EQ(GMERR_OK, ret);
}

void test_GmcDeleteEdgeByPrimFilter::TearDownTestCase()
{
    int32_t ret = 0;
    GmcStmtT *stmt = NULL;
    ret = GmcAllocStmt(conn, &stmt);
    GmcDropGraphLabel(stmt, src_VertexLabel_name);
    GmcDropGraphLabel(stmt, dst_VertexLabel_name);
    ret = GmcDropGraphLabel(stmt, dst_VertexLabel_name2);
    GmcFreeStmt(stmt);
    // ret = testGmcDisconnect(conn);
    // EXPECT_EQ(GMERR_OK, ret);
    // 用例本身为预期断连失败场景, 此处临时规避断连core, 后续统一整改(添加返回值)
    GmcDisconnect(conn);
    pthread_mutex_lock(&g_connLock);
    --g_connOnline;
    pthread_mutex_unlock(&g_connLock);
    pthread_mutex_lock(&g_connConcurrent);
    --g_connRequest;
    pthread_mutex_unlock(&g_connConcurrent);
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    testEnvClean();
}

void test_GmcDeleteEdgeByPrimFilter::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    char errorMsg1[128] = {0};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
}

void test_GmcDeleteEdgeByPrimFilter::TearDown()
{
    AW_CHECK_LOG_END();
}

// normal create EdgeLabel
TEST_F(test_GmcDeleteEdgeByPrimFilter, DML_009_001)
{
    GmcStmtT *stmt = NULL;
    void *edgelabe = NULL;
    int32_t ret = 0;
    uint32_t pkValue = 1;
    uint32_t pkValue2 = 3000;
    uint32_t dstpkVal = 4000;
    // and
    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // src和dst点
    ret = GmcOpenEdgeLabelByName(stmt, g_EdgeLabelName, &edgelabe);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexName(stmt, src_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexName(stmt, dst_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    //插入edge
    // ret=GmcInsertEdge(stmt,edgelabe);
    // ASSERT_EQ(GMERR_OK,ret);
    ret = GmcCloseEdgeLabel(stmt, edgelabe);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);

    // 拓扑邻点的查询流程
    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, src_VertexLabel_name, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDirectFetchNeighborBegin(stmt, g_EdgeLabelName);
    ASSERT_EQ(GMERR_OK, ret);

    bool isEof = false;
    ret = GmcFetch(stmt, &isEof);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(isEof, false);
    ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_UINT32, &pkValue);
    ASSERT_EQ(GMERR_OK, ret);
    char F1_str1[] = "testver";
    ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_STRING, F1_str1);
    ASSERT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_UINT32, &pkValue);
    ASSERT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_INT16, &pkValue);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcFetch(stmt, &isEof);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(isEof, true);
    ret = GmcDirectFetchNeighborEnd(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);

    // or
    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // src和dst点
    ret = GmcOpenEdgeLabelByName(stmt, g_EdgeLabelNameOr, &edgelabe);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexName(stmt, src_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue2, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexName(stmt, dst_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &dstpkVal, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    //插入edge
    // ret=GmcInsertEdge(stmt,edgelabe);
    // ASSERT_EQ(GMERR_OK,ret);
    ret = GmcCloseEdgeLabel(stmt, edgelabe);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);

    // 拓扑邻点的查询流程
    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, src_VertexLabel_name, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue2, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDirectFetchNeighborBegin(stmt, g_EdgeLabelNameOr);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcFetch(stmt, &isEof);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(isEof, false);
    ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_UINT32, &dstpkVal);
    ASSERT_EQ(GMERR_OK, ret);
    char F1_str2[] = "test123";
    ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_STRING, F1_str2);
    ASSERT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_UINT32, &dstpkVal);
    ASSERT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_INT16, &dstpkVal);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcFetch(stmt, &isEof);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(isEof, true);
    ret = GmcDirectFetchNeighborEnd(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);

    //删除edge
    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // src和dst点
    ret = GmcOpenEdgeLabelByName(stmt, g_EdgeLabelNameOr, &edgelabe);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexName(stmt, src_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue2, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexName(stmt, dst_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &dstpkVal, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    //删除edge
    ret = GmcDeleteEdgeByIndexKey(stmt, edgelabe);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcCloseEdgeLabel(stmt, edgelabe);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);
    //查询不到
    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, src_VertexLabel_name, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue2, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDirectFetchNeighborBegin(stmt, g_EdgeLabelNameOr);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcFetch(stmt, &isEof);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(isEof, true);
    ret = GmcDirectFetchNeighborEnd(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);

    //拓扑邻点的删除
    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // src和dst点
    ret = GmcOpenEdgeLabelByName(stmt, g_EdgeLabelName, &edgelabe);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexName(stmt, src_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexName(stmt, dst_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    //删除edge
    ret = GmcDeleteEdgeByIndexKey(stmt, edgelabe);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcCloseEdgeLabel(stmt, edgelabe);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);
    //查询不存在
    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, src_VertexLabel_name, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue2, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDirectFetchNeighborBegin(stmt, g_EdgeLabelName);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcFetch(stmt, &isEof);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(isEof, true);
    ret = GmcDirectFetchNeighborEnd(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);
}

//空指针检测
TEST_F(test_GmcDeleteEdgeByPrimFilter, DML_009_002)
{
    GmcStmtT *stmt = NULL;
    void *edgelabe = NULL;
    int32_t ret = 0;
    uint32_t pkValue = 5;
    char errorMsg1[128] = {0}, errorMsg2[128] = {0};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_NULL_VALUE_NOT_ALLOWED);
    (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_INVALID_PARAMETER_VALUE);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);
    // and
    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // src和dst点
    ret = GmcOpenEdgeLabelByName(stmt, g_EdgeLabelName, &edgelabe);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexName(stmt, src_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexName(stmt, dst_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    //插入edge
    ret = GmcInsertEdge(stmt, edgelabe);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcCloseEdgeLabel(stmt, edgelabe);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);

    //拓扑邻点的删除
    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // src和dst点
    ret = GmcOpenEdgeLabelByName(stmt, g_EdgeLabelName, &edgelabe);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexName(stmt, src_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexName(stmt, dst_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    //删除edge
    // stmt NULL
    ret = GmcDeleteEdgeByIndexKey(NULL, edgelabe);
    ASSERT_EQ(
        GMERR_INVALID_PARAMETER_VALUE, ret);  //实际错误码为GMERR_INVALID_PARAMETER_VALUE 非 GMERR_UNEXPECTED_NULL_VALUE
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // edgelabe=NULL
    ret = GmcDeleteEdgeByIndexKey(stmt, NULL);
    ASSERT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCloseEdgeLabel(stmt, edgelabe);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);
    //查询还存在
    bool isEof = true;
    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, src_VertexLabel_name, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDirectFetchNeighborBegin(stmt, g_EdgeLabelName);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcFetch(stmt, &isEof);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(isEof, false);
    ret = GmcDirectFetchNeighborEnd(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);
}

//指定两个点的主键，删除一条边，边名字错误，删除返回成功，但是affect rows 0
//目前客户端这边affect rows 0代码还未合入----DTS2020092507VC9SP1H00
// edgeLabel与stmt不匹配
// edgelabel2中无边的场景
TEST_F(test_GmcDeleteEdgeByPrimFilter, DML_009_003)
{
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmt2 = NULL;
    char *edge_label_schema = NULL;
    char *edge_label_schema2 = NULL;
    void *edgelabe = NULL;
    void *edgelabel2 = NULL;
    int32_t ret = 0;
    uint32_t pkValue = 3000;
    uint32_t pkValuedst = 4000;

    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    //构造src和dst点
    ret = GmcOpenEdgeLabelByName(stmt, g_EdgeLabelNameOr, &edgelabe);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexName(stmt, src_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexName(stmt, dst_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValuedst, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    //插入edge
    // ret=GmcInsertEdge(stmt,edgelabe);
    // ASSERT_EQ(GMERR_OK,ret);
    ret = GmcCloseEdgeLabel(stmt, edgelabe);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);
    //删除edge
    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcOpenEdgeLabelByName(stmt, g_EdgeLabelName, &edgelabel2);
    ASSERT_EQ(GMERR_OK, ret);
    // src和dst点
    ret = GmcSetEdgeSrcVertexIndexName(stmt, src_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexName(stmt, dst_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValuedst, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDeleteEdgeByIndexKey(stmt, edgelabel2);
    ASSERT_EQ(GMERR_OK, ret);
    // affectRows=0
    int affectRows = 1;
    unsigned int len = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, affectRows);

    ret = GmcCloseEdgeLabel(stmt, edgelabel2);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);
}

//指定两个点的主键，删除一条边，边名字错误，删除返回成功，但是affect rows 0
// edgeLabel与stmt不匹配
// edgelabel2中有边的场景
TEST_F(test_GmcDeleteEdgeByPrimFilter, DML_009_003_2)
{
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmt2 = NULL;
    char *edge_label_schema = NULL;
    char *edge_label_schema2 = NULL;
    void *edgelabe = NULL;
    void *edgelabel2 = NULL;
    int32_t ret = 0;
    uint32_t pkValue = 3000;
    uint32_t pkValuedst = 4000;

    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    //构造src和dst点
    ret = GmcOpenEdgeLabelByName(stmt, g_EdgeLabelNameOr, &edgelabe);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexName(stmt, src_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexName(stmt, dst_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValuedst, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    //插入edgelabel1
    ret = GmcInsertEdge(stmt, edgelabe);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcCloseEdgeLabel(stmt, edgelabe);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);

    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t pkValue2 = 18;
    uint32_t pkValuedst2 = 18;
    ret = GmcOpenEdgeLabelByName(stmt, g_EdgeLabelName, &edgelabel2);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexName(stmt, src_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue2, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexName(stmt, dst_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValuedst2, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    //插入edgelabel2
    ret = GmcInsertEdge(stmt, edgelabel2);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcCloseEdgeLabel(stmt, edgelabel2);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);

    // edgelabel2中删除edgelabel 1中的边
    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcOpenEdgeLabelByName(stmt, g_EdgeLabelName, &edgelabel2);
    ASSERT_EQ(GMERR_OK, ret);
    // src和dst点
    ret = GmcSetEdgeSrcVertexIndexName(stmt, src_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexName(stmt, dst_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValuedst, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDeleteEdgeByIndexKey(stmt, edgelabel2);
    ASSERT_EQ(GMERR_OK, ret);
    // affectRows=0
    int affectRows = 1;
    unsigned int len = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, affectRows);

    ret = GmcCloseEdgeLabel(stmt, edgelabel2);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);
}

//同一个连接同一个EdgeLabel一次删除多条不同边
TEST_F(test_GmcDeleteEdgeByPrimFilter, DML_009_004)
{
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmt2 = NULL;
    char *edge_label_schema = NULL;
    char *edge_label_schema2 = NULL;
    void *edgelabe = NULL;
    void *edgelabel2 = NULL;
    int32_t ret = 0;
    uint32_t pkValue = 4;
    uint32_t pkValuedst = 4;

    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // src和dst点
    ret = GmcOpenEdgeLabelByName(stmt, g_EdgeLabelName, &edgelabe);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 3; i++) {
        ret = GmcSetEdgeSrcVertexIndexName(stmt, src_VertexLabel_name_PK);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetEdgeSrcVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetEdgeDstVertexIndexName(stmt, dst_VertexLabel_name_PK);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetEdgeDstVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValuedst, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        //插入edge
        ret = GmcInsertEdge(stmt, edgelabe);
        ASSERT_EQ(GMERR_OK, ret);
        pkValue++;
        pkValuedst++;
    }
    ret = GmcCloseEdgeLabel(stmt, edgelabe);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);
    //删除edge
    uint32_t srcVal = 6;
    pkValuedst = 6;
    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcOpenEdgeLabelByName(stmt, g_EdgeLabelName, &edgelabel2);
    ASSERT_EQ(GMERR_OK, ret);
    // src和dst点
    for (int i = 0; i < 3; i++) {
        ret = GmcSetEdgeSrcVertexIndexName(stmt, src_VertexLabel_name_PK);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetEdgeSrcVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &srcVal, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetEdgeDstVertexIndexName(stmt, dst_VertexLabel_name_PK);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetEdgeDstVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValuedst, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcDeleteEdgeByIndexKey(stmt, edgelabel2);
        ASSERT_EQ(GMERR_OK, ret);
        srcVal++;
        pkValuedst++;
    }
    ret = GmcCloseEdgeLabel(stmt, edgelabel2);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);
    //查询是否存在
    srcVal = 6;
    bool isEof = true;
    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, src_VertexLabel_name, GMC_OPERATION_SCAN);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &srcVal, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcDirectFetchNeighborBegin(stmt, g_EdgeLabelName);
        ASSERT_EQ(GMERR_OK, ret);
        //提单DTS2020092705ZX6SP1J00
        ret = GmcFetch(stmt, &isEof);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(isEof, true);
        ret = GmcDirectFetchNeighborEnd(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        srcVal++;
    }
    GmcFreeStmt(stmt);
}

//同一个连接对不同EdgeLabel进行边删除
TEST_F(test_GmcDeleteEdgeByPrimFilter, DML_009_005)
{
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmt2 = NULL;
    char *edge_label_schema = NULL;
    char *edge_label_schema2 = NULL;
    void *edgelabe = NULL;
    void *edgelabel2 = NULL;
    int32_t ret = 0;
    uint32_t pkValue = 3000;
    uint32_t pkValuedst = 4000;
    uint32_t pkValue2 = 14;
    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // src和dst点
    ret = GmcOpenEdgeLabelByName(stmt, g_EdgeLabelNameOr, &edgelabe);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcOpenEdgeLabelByName(stmt, g_EdgeLabelName, &edgelabel2);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 3; i++) {
        ret = GmcSetEdgeSrcVertexIndexName(stmt, src_VertexLabel_name_PK);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetEdgeSrcVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetEdgeDstVertexIndexName(stmt, dst_VertexLabel_name_PK);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetEdgeDstVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValuedst, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        //插入edge
        // ret=GmcInsertEdge(stmt,edgelabe);
        // ASSERT_EQ(GMERR_OK,ret);
        // pkValue++;
        // pkValuedst++;
    }
    for (int i = 0; i < 3; i++) {
        ret = GmcSetEdgeSrcVertexIndexName(stmt, src_VertexLabel_name_PK);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetEdgeSrcVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue2, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetEdgeDstVertexIndexName(stmt, dst_VertexLabel_name_PK);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetEdgeDstVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue2, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        //插入edge
        // ret=GmcInsertEdge(stmt,edgelabel2);
        // ASSERT_EQ(GMERR_OK,ret);
        // pkValue2++;
    }
    ret = GmcCloseEdgeLabel(stmt, edgelabe);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcCloseEdgeLabel(stmt, edgelabel2);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);

    //删除edge
    uint32_t srcVal = 11;
    // pkValuedst=11;
    pkValue2 = 14;
    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcOpenEdgeLabelByName(stmt, g_EdgeLabelNameOr, &edgelabel2);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcOpenEdgeLabelByName(stmt, g_EdgeLabelName, &edgelabe);
    ASSERT_EQ(GMERR_OK, ret);
    // src和dst点
    for (int i = 0; i < 1; i++) {
        ret = GmcSetEdgeSrcVertexIndexName(stmt, src_VertexLabel_name_PK);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetEdgeSrcVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetEdgeDstVertexIndexName(stmt, dst_VertexLabel_name_PK);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetEdgeDstVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValuedst, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcDeleteEdgeByIndexKey(stmt, edgelabel2);
        ASSERT_EQ(GMERR_OK, ret);
        // srcVal++;
        // pkValuedst++;
    }
    // src和dst点
    for (int i = 0; i < 3; i++) {
        ret = GmcSetEdgeSrcVertexIndexName(stmt, src_VertexLabel_name_PK);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetEdgeSrcVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue2, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetEdgeDstVertexIndexName(stmt, dst_VertexLabel_name_PK);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetEdgeDstVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue2, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcDeleteEdgeByIndexKey(stmt, edgelabe);
        ASSERT_EQ(GMERR_OK, ret);
        pkValue2++;
    }
    ret = GmcCloseEdgeLabel(stmt, edgelabel2);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcCloseEdgeLabel(stmt, edgelabe);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);
    //查询是否存在
    srcVal = 11;
    bool isEof = true;
    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, src_VertexLabel_name, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 1; i++) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcDirectFetchNeighborBegin(stmt, g_EdgeLabelNameOr);
        ASSERT_EQ(GMERR_OK, ret);
        //提单DTS2020092705ZX6SP1J00
        ret = GmcFetch(stmt, &isEof);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(isEof, true);
        ret = GmcDirectFetchNeighborEnd(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        srcVal++;
    }
    pkValue2 = 14;
    for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, src_VertexLabel_name, GMC_OPERATION_SCAN);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue2, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcDirectFetchNeighborBegin(stmt, g_EdgeLabelName);
        ASSERT_EQ(GMERR_OK, ret);
        //提单DTS2020092705ZX6SP1J00
        ret = GmcFetch(stmt, &isEof);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(isEof, true);
        ret = GmcDirectFetchNeighborEnd(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        pkValue2++;
    }
    GmcFreeStmt(stmt);
}

// src点的keyName不存在
TEST_F(test_GmcDeleteEdgeByPrimFilter, DML_009_006)
{
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmt2 = NULL;
    char *edge_label_schema = NULL;
    char *edge_label_schema2 = NULL;
    void *edgelabe = NULL;
    void *edgelabel2 = NULL;
    int32_t ret = 0;
    uint32_t pkValue = 20;
    uint32_t pkValuedst = 20;
    uint32_t pkValue2 = 25;
    char errorMsg1[128] = {0};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // src和dst点
    ret = GmcOpenEdgeLabelByName(stmt, g_EdgeLabelNameOr, &edgelabe);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 3; i++) {
        ret = GmcSetEdgeSrcVertexIndexName(stmt, src_VertexLabel_name_PK);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetEdgeSrcVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetEdgeDstVertexIndexName(stmt, dst_VertexLabel_name_PK);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetEdgeDstVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValuedst, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        //插入edge
        ret = GmcInsertEdge(stmt, edgelabe);
        ASSERT_EQ(GMERR_OK, ret);
        pkValue++;
        pkValuedst++;
    }
    ret = GmcCloseEdgeLabel(stmt, edgelabe);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);

    //删除edge
    uint32_t srcVal = 20;
    pkValuedst = 20;
    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcOpenEdgeLabelByName(stmt, g_EdgeLabelNameOr, &edgelabel2);
    ASSERT_EQ(GMERR_OK, ret);
    ;
    // src和dst点
    for (int i = 0; i < 3; i++) {
        ret = GmcSetEdgeSrcVertexIndexName(stmt, "PK");
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetEdgeSrcVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &srcVal, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetEdgeDstVertexIndexName(stmt, dst_VertexLabel_name_PK);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetEdgeDstVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValuedst, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcDeleteEdgeByIndexKey(stmt, edgelabel2);
        ASSERT_NE(GMERR_OK, ret);
        srcVal++;
        pkValuedst++;
    }
    ret = GmcCloseEdgeLabel(stmt, edgelabel2);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);
    //查询是否存在
    srcVal = 20;
    bool isEof = true;
    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, src_VertexLabel_name, GMC_OPERATION_SCAN);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &srcVal, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcDirectFetchNeighborBegin(stmt, g_EdgeLabelNameOr);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isEof);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(isEof, false);
        ret = GmcDirectFetchNeighborEnd(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        srcVal++;
    }
    GmcFreeStmt(stmt);
}

// dst点的keyName不存在
TEST_F(test_GmcDeleteEdgeByPrimFilter, DML_009_007)
{
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmt2 = NULL;
    char *edge_label_schema = NULL;
    char *edge_label_schema2 = NULL;
    void *edgelabe = NULL;
    void *edgelabel2 = NULL;
    int32_t ret = 0;
    uint32_t pkValue = 25;
    uint32_t pkValuedst = 25;
    char errorMsg1[128] = {0};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // src和dst点
    ret = GmcOpenEdgeLabelByName(stmt, g_EdgeLabelNameOr, &edgelabe);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 3; i++) {
        ret = GmcSetEdgeSrcVertexIndexName(stmt, src_VertexLabel_name_PK);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetEdgeSrcVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetEdgeDstVertexIndexName(stmt, dst_VertexLabel_name_PK);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetEdgeDstVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValuedst, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        //插入edge
        ret = GmcInsertEdge(stmt, edgelabe);
        ASSERT_EQ(GMERR_OK, ret);
        pkValue++;
        pkValuedst++;
    }
    ret = GmcCloseEdgeLabel(stmt, edgelabe);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);

    //删除edge
    uint32_t srcVal = 25;
    pkValuedst = 25;
    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcOpenEdgeLabelByName(stmt, g_EdgeLabelNameOr, &edgelabel2);
    ASSERT_EQ(GMERR_OK, ret);
    // src和dst点
    for (int i = 0; i < 3; i++) {
        ret = GmcSetEdgeSrcVertexIndexName(stmt, src_VertexLabel_name_PK);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetEdgeSrcVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &srcVal, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetEdgeDstVertexIndexName(stmt, "PK");
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetEdgeDstVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValuedst, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcDeleteEdgeByIndexKey(stmt, edgelabel2);
        ASSERT_NE(GMERR_OK, ret);
        srcVal++;
        pkValuedst++;
    }
    ret = GmcCloseEdgeLabel(stmt, edgelabel2);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);
    //查询是否存在
    srcVal = 25;
    bool isEof = true;
    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, src_VertexLabel_name, GMC_OPERATION_SCAN);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &srcVal, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcDirectFetchNeighborBegin(stmt, g_EdgeLabelNameOr);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isEof);
        ASSERT_EQ(GMERR_OK, ret);
        EXPECT_EQ(isEof, false);
        ret = GmcDirectFetchNeighborEnd(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        srcVal++;
    }
    GmcFreeStmt(stmt);
}

// src点未设置
TEST_F(test_GmcDeleteEdgeByPrimFilter, DML_009_008)
{
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmt2 = NULL;
    char *edge_label_schema = NULL;
    char *edge_label_schema2 = NULL;
    void *edgelabe = NULL;
    void *edgelabel2 = NULL;
    int32_t ret = 0;
    uint32_t pkValue = 28;
    uint32_t pkValuedst = 28;
    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // src和dst点
    ret = GmcOpenEdgeLabelByName(stmt, g_EdgeLabelNameOr, &edgelabe);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcSetEdgeSrcVertexIndexName(stmt, src_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexName(stmt, dst_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValuedst, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    //插入edge
    ret = GmcInsertEdge(stmt, edgelabe);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcCloseEdgeLabel(stmt, edgelabe);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);

    //删除edge
    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcOpenEdgeLabelByName(stmt, g_EdgeLabelNameOr, &edgelabel2);
    ASSERT_EQ(GMERR_OK, ret);
    ;
    // src点未设置
    ret = GmcSetEdgeDstVertexIndexName(stmt, dst_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValuedst, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDeleteEdgeByIndexKey(stmt, edgelabel2);
    ASSERT_NE(GMERR_OK, ret);

    ret = GmcCloseEdgeLabel(stmt, edgelabel2);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);
    //查询是否存在
    bool isEof = true;
    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, src_VertexLabel_name, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDirectFetchNeighborBegin(stmt, g_EdgeLabelNameOr);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcFetch(stmt, &isEof);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(isEof, false);
    ret = GmcDirectFetchNeighborEnd(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);
}

// dst点未设置
TEST_F(test_GmcDeleteEdgeByPrimFilter, DML_009_009)
{
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmt2 = NULL;
    char *edge_label_schema = NULL;
    char *edge_label_schema2 = NULL;
    void *edgelabe = NULL;
    void *edgelabel2 = NULL;
    int32_t ret = 0;
    uint32_t pkValue = 29;
    uint32_t pkValuedst = 29;
    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // src和dst点
    ret = GmcOpenEdgeLabelByName(stmt, g_EdgeLabelNameOr, &edgelabe);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcSetEdgeSrcVertexIndexName(stmt, src_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexName(stmt, dst_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValuedst, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    //插入edge
    ret = GmcInsertEdge(stmt, edgelabe);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcCloseEdgeLabel(stmt, edgelabe);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);

    //删除edge
    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcOpenEdgeLabelByName(stmt, g_EdgeLabelNameOr, &edgelabel2);
    ASSERT_EQ(GMERR_OK, ret);
    ;
    // dst点未设置
    ret = GmcSetEdgeSrcVertexIndexName(stmt, src_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDeleteEdgeByIndexKey(stmt, edgelabel2);
    ASSERT_NE(GMERR_OK, ret);

    ret = GmcCloseEdgeLabel(stmt, edgelabel2);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);

    //查询是否存在
    bool isEof = true;
    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, src_VertexLabel_name, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDirectFetchNeighborBegin(stmt, g_EdgeLabelNameOr);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcFetch(stmt, &isEof);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(isEof, false);
    ret = GmcDirectFetchNeighborEnd(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);
}

// src点设置的属性的类型为非主键属性类型
TEST_F(test_GmcDeleteEdgeByPrimFilter, DML_009_010)
{
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmt2 = NULL;
    char *edge_label_schema = NULL;
    char *edge_label_schema2 = NULL;
    void *edgelabe = NULL;
    void *edgelabel2 = NULL;
    int32_t ret = 0;
    uint32_t pkValue = 30;
    uint32_t pkValuedst = 30;
    char errorMsg1[128] = {0};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_DATATYPE_MISMATCH);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // src和dst点
    ret = GmcOpenEdgeLabelByName(stmt, g_EdgeLabelNameOr, &edgelabe);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcSetEdgeSrcVertexIndexName(stmt, src_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexName(stmt, dst_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValuedst, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    //插入edge
    ret = GmcInsertEdge(stmt, edgelabe);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcCloseEdgeLabel(stmt, edgelabe);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);

    //删除edge
    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcOpenEdgeLabelByName(stmt, g_EdgeLabelNameOr, &edgelabel2);
    ASSERT_EQ(GMERR_OK, ret);
    ;
    // src点设置的属性类型不对
    ret = GmcSetEdgeSrcVertexIndexName(stmt, src_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &pkValue, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexName(stmt, dst_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValuedst, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDeleteEdgeByIndexKey(stmt, edgelabel2);
    ASSERT_NE(GMERR_OK, ret);

    ret = GmcCloseEdgeLabel(stmt, edgelabel2);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);

    //查询是否存在
    bool isEof = true;
    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, src_VertexLabel_name, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDirectFetchNeighborBegin(stmt, g_EdgeLabelNameOr);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcFetch(stmt, &isEof);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(isEof, false);
    ret = GmcDirectFetchNeighborEnd(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);
}

// dst点设置的属性的类型为非主键属性类型
TEST_F(test_GmcDeleteEdgeByPrimFilter, DML_009_011)
{
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmt2 = NULL;
    char *edge_label_schema = NULL;
    char *edge_label_schema2 = NULL;
    void *edgelabe = NULL;
    void *edgelabel2 = NULL;
    int32_t ret = 0;
    uint32_t pkValue = 31;
    uint32_t pkValuedst = 31;
    char errorMsg1[128] = {0};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_DATATYPE_MISMATCH);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // src和dst点
    ret = GmcOpenEdgeLabelByName(stmt, g_EdgeLabelNameOr, &edgelabe);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcSetEdgeSrcVertexIndexName(stmt, src_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexName(stmt, dst_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValuedst, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    //插入edge
    ret = GmcInsertEdge(stmt, edgelabe);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcCloseEdgeLabel(stmt, edgelabe);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);

    //删除edge
    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcOpenEdgeLabelByName(stmt, g_EdgeLabelNameOr, &edgelabel2);
    ASSERT_EQ(GMERR_OK, ret);

    // dst点设置的属性类型不对
    ret = GmcSetEdgeSrcVertexIndexName(stmt, src_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexName(stmt, dst_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_INT16, &pkValuedst, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDeleteEdgeByIndexKey(stmt, edgelabel2);
    ASSERT_NE(GMERR_OK, ret);

    ret = GmcCloseEdgeLabel(stmt, edgelabel2);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);
}

//指定两个点的主键，其中一个点不存在，删除指定的边，返回成功，affect rows 0
//目前客户端这边affect rows 0代码还未合入：----DTS2020092507VC9SP1H00
// src点不存在
TEST_F(test_GmcDeleteEdgeByPrimFilter, DML_009_012)
{
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmt2 = NULL;
    char *edge_label_schema = NULL;
    char *edge_label_schema2 = NULL;
    void *edgelabe = NULL;
    void *edgelabel2 = NULL;
    int32_t ret = 0;
    uint32_t pkValue = 1;
    uint32_t pkValuedst = 500;
    uint32_t srcVal = 10001;
    uint32_t dstValNoExist = 10001;
    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // src和dst点
    ret = GmcOpenEdgeLabelByName(stmt, g_EdgeLabelNameOr, &edgelabe);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexName(stmt, src_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexName(stmt, dst_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValuedst, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    //插入edge
    ret = GmcInsertEdge(stmt, edgelabe);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcCloseEdgeLabel(stmt, edgelabe);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);

    bool isEof = true;
    // 拓扑邻点的查询流程
    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, src_VertexLabel_name, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDirectFetchNeighborBegin(stmt, g_EdgeLabelNameOr);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcFetch(stmt, &isEof);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(isEof, false);
    ret = GmcDirectFetchNeighborEnd(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);

    //删除edge,src点不存在
    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcOpenEdgeLabelByName(stmt, g_EdgeLabelNameOr, &edgelabel2);
    ASSERT_EQ(GMERR_OK, ret);
    // src和dst点
    ret = GmcSetEdgeSrcVertexIndexName(stmt, src_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &srcVal, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexName(stmt, dst_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValuedst, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDeleteEdgeByIndexKey(stmt, edgelabel2);
    ASSERT_EQ(GMERR_OK, ret);
    // affectRows=0测试
    int affectRows = 1;
    unsigned int len = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, affectRows);

    ret = GmcCloseEdgeLabel(stmt, edgelabel2);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);
}

//指定两个点的主键，其中一个点不存在，删除指定的边，返回成功，affect rows 0
//目前客户端这边affect rows 0代码还未合入：----DTS2020092507VC9SP1H00
// dst点不存在
TEST_F(test_GmcDeleteEdgeByPrimFilter, DML_009_013)
{
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmt2 = NULL;
    char *edge_label_schema = NULL;
    char *edge_label_schema2 = NULL;
    void *edgelabe = NULL;
    void *edgelabel2 = NULL;
    int32_t ret = 0;
    uint32_t pkValue = 1;
    uint32_t pkValuedst = 500;
    uint32_t srcVal = 10001;
    uint32_t dstValNoExist = 10001;
    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // src和dst点
    ret = GmcOpenEdgeLabelByName(stmt, g_EdgeLabelNameOr, &edgelabe);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexName(stmt, src_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexName(stmt, dst_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValuedst, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    //插入edge
    ret = GmcInsertEdge(stmt, edgelabe);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcCloseEdgeLabel(stmt, edgelabe);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);

    bool isEof = true;
    // 拓扑邻点的查询流程
    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, src_VertexLabel_name, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDirectFetchNeighborBegin(stmt, g_EdgeLabelNameOr);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcFetch(stmt, &isEof);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(isEof, false);
    ret = GmcDirectFetchNeighborEnd(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);

    //删除edge：dst点不存在
    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcOpenEdgeLabelByName(stmt, g_EdgeLabelNameOr, &edgelabel2);
    ASSERT_EQ(GMERR_OK, ret);
    // src和dst点
    ret = GmcSetEdgeSrcVertexIndexName(stmt, src_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexName(stmt, dst_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &dstValNoExist, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    //当前返回OK
    ret = GmcDeleteEdgeByIndexKey(stmt, edgelabel2);
    ASSERT_EQ(GMERR_OK, ret);
    // affectRows=0测试
    int affectRows = 1;
    unsigned int len = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, affectRows);

    ret = GmcCloseEdgeLabel(stmt, edgelabel2);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);
}

//重复删除一条边
TEST_F(test_GmcDeleteEdgeByPrimFilter, DML_009_014)
{
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmt2 = NULL;
    char *edge_label_schema = NULL;
    char *edge_label_schema2 = NULL;
    void *edgelabe = NULL;
    void *edgelabel2 = NULL;
    int32_t ret = 0;
    uint32_t pkValue = 32;
    uint32_t pkValuedst = 32;
    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // src和dst点
    ret = GmcOpenEdgeLabelByName(stmt, g_EdgeLabelNameOr, &edgelabe);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcSetEdgeSrcVertexIndexName(stmt, src_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexName(stmt, dst_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValuedst, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    //插入edge
    ret = GmcInsertEdge(stmt, edgelabe);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcCloseEdgeLabel(stmt, edgelabe);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);

    //删除edge
    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcOpenEdgeLabelByName(stmt, g_EdgeLabelNameOr, &edgelabel2);
    ASSERT_EQ(GMERR_OK, ret);
    ;

    ret = GmcSetEdgeSrcVertexIndexName(stmt, src_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexName(stmt, dst_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValuedst, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDeleteEdgeByIndexKey(stmt, edgelabel2);
    ASSERT_EQ(GMERR_OK, ret);
    //重复删除
    ret = GmcSetEdgeSrcVertexIndexName(stmt, src_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexName(stmt, dst_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValuedst, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    //返回OK，affect rows=0   DTS2020092507VC9SP1H00
    ret = GmcDeleteEdgeByIndexKey(stmt, edgelabel2);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcCloseEdgeLabel(stmt, edgelabel2);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);
}

//删除一条不存在的边：src点与dst点都不存在
TEST_F(test_GmcDeleteEdgeByPrimFilter, DML_009_016)
{
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmt2 = NULL;
    char *edge_label_schema = NULL;
    char *edge_label_schema2 = NULL;
    void *edgelabe = NULL;
    void *edgelabel2 = NULL;
    int32_t ret = 0;
    uint32_t pkValue = 34;
    uint32_t pkValuedst = 34;
    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // src和dst点
    ret = GmcOpenEdgeLabelByName(stmt, g_EdgeLabelNameOr, &edgelabe);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcSetEdgeSrcVertexIndexName(stmt, src_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexName(stmt, dst_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValuedst, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    //插入edge
    ret = GmcInsertEdge(stmt, edgelabe);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcCloseEdgeLabel(stmt, edgelabe);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);

    //删除edge:不存在的边
    pkValue = 20000;
    pkValuedst = 20007;
    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcOpenEdgeLabelByName(stmt, g_EdgeLabelNameOr, &edgelabel2);
    ASSERT_EQ(GMERR_OK, ret);
    ;
    // src与dst点设置
    ret = GmcSetEdgeSrcVertexIndexName(stmt, src_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexName(stmt, dst_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValuedst, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    //返回OK，affect rows=0   DTS2020092507VC9SP1H00
    ret = GmcDeleteEdgeByIndexKey(stmt, edgelabel2);
    ASSERT_EQ(GMERR_OK, ret);
    int affectRows;
    unsigned int len;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, affectRows);
    ret = GmcCloseEdgeLabel(stmt, edgelabel2);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);
}

// src点设置成dst点，dst点设置成src点
TEST_F(test_GmcDeleteEdgeByPrimFilter, DML_009_017)
{
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmt2 = NULL;
    char *edge_label_schema = NULL;
    char *edge_label_schema2 = NULL;
    void *edgelabe = NULL;
    void *edgelabel2 = NULL;
    int32_t ret = 0;
    uint32_t pkValue = 35;
    uint32_t pkValuedst = 35;
    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // src和dst点
    ret = GmcOpenEdgeLabelByName(stmt, g_EdgeLabelNameOr, &edgelabe);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcSetEdgeSrcVertexIndexName(stmt, src_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexName(stmt, dst_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValuedst, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    //插入edge
    ret = GmcInsertEdge(stmt, edgelabe);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcCloseEdgeLabel(stmt, edgelabe);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);

    //删除edge:
    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcOpenEdgeLabelByName(stmt, g_EdgeLabelNameOr, &edgelabel2);
    ASSERT_EQ(GMERR_OK, ret);
    ;
    // src与dst点设置
    ret = GmcSetEdgeSrcVertexIndexName(stmt, dst_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexName(stmt, src_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValuedst, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    //返回OK，affect rows=0   DTS2020092507VC9SP1H00
    ret = GmcDeleteEdgeByIndexKey(stmt, edgelabel2);
    ASSERT_EQ(GMERR_INVALID_NAME, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCloseEdgeLabel(stmt, edgelabel2);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);
}

//两个存在的点的主键，且两点间存在多条边，指定删除全部边，则两点间的边全部被删除
TEST_F(test_GmcDeleteEdgeByPrimFilter, DML_009_018)
{
    GmcStmtT *stmt = NULL;
    char *edge_label_schema = NULL;
    void *edgelabel = NULL;
    int32_t ret = 0;
    uint32_t srcpkValue = 8;
    uint32_t dstpkValue = 8;
    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // src和dst点
    ret = GmcOpenEdgeLabelByName(stmt, g_EdgeLabelName, &edgelabel);
    ASSERT_EQ(GMERR_OK, ret);
    //重复插入边
    for (int i = 0; i < 5; i++) {
        ret = GmcSetEdgeSrcVertexIndexName(stmt, src_VertexLabel_name_PK);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetEdgeSrcVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &srcpkValue, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetEdgeDstVertexIndexName(stmt, dst_VertexLabel_name_PK);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetEdgeDstVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &dstpkValue, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        //插入edge
        ret = GmcInsertEdge(stmt, edgelabel);
        ASSERT_EQ(GMERR_OK, ret);
    }

    //删除边
    ret = GmcSetEdgeSrcVertexIndexName(stmt, src_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &srcpkValue, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexName(stmt, dst_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &dstpkValue, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    //删除edge
    ret = GmcDeleteEdgeByIndexKey(stmt, edgelabel);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcCloseEdgeLabel(stmt, edgelabel);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);

    bool isEof = true;
    // 拓扑邻点的查询流程
    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, src_VertexLabel_name, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &srcpkValue, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDirectFetchNeighborBegin(stmt, g_EdgeLabelName);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcFetch(stmt, &isEof);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(isEof, true);
    ret = GmcDirectFetchNeighborEnd(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);
}

//删除一条边前数据库服务故障
TEST_F(test_GmcDeleteEdgeByPrimFilter, DML_009_015)
{
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmt2 = NULL;
    char *edge_label_schema = NULL;
    char *edge_label_schema2 = NULL;
    void *edgelabe = NULL;
    void *edgelabel2 = NULL;
    int32_t ret = 0;
    uint32_t pkValue = 33;
    uint32_t pkValuedst = 33;
    char errorMsg1[128] = {0};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_CONNECTION_RESET_BY_PEER);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // src和dst点
    ret = GmcOpenEdgeLabelByName(stmt, g_EdgeLabelNameOr, &edgelabe);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcSetEdgeSrcVertexIndexName(stmt, src_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexName(stmt, dst_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValuedst, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    //插入edge
    ret = GmcInsertEdge(stmt, edgelabe);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcCloseEdgeLabel(stmt, edgelabe);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);

    //删除edge
    ret = GmcAllocStmt(conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcOpenEdgeLabelByName(stmt, g_EdgeLabelNameOr, &edgelabel2);
    ASSERT_EQ(GMERR_OK, ret);
    ;
    // src与dst点设置
    ret = GmcSetEdgeSrcVertexIndexName(stmt, src_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexName(stmt, dst_VertexLabel_name_PK);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValuedst, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    //删除前数据库服务故障
    system("$TEST_HOME/tools/stop.sh -f");
    system("$TEST_HOME/tools/start.sh -f");  //数据库服务故障测试用例，需重启服务
    ret = GmcDeleteEdgeByIndexKey(stmt, edgelabel2);
    ASSERT_EQ(GMERR_CONNECTION_RESET_BY_PEER, ret); // 适配executeLevel  = 1
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCloseEdgeLabel(stmt, edgelabel2);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);
}

