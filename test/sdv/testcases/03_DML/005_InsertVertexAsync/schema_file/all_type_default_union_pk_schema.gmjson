[{"type": "record", "name": "T100_all_type_default", "fields": [{"name": "F6", "type": "int32", "default": 1, "nullable": false}, {"name": "F7", "type": "uint32", "default": 1, "nullable": false}, {"name": "F0", "type": "char", "default": "a", "nullable": false}, {"name": "F1", "type": "uchar", "default": "b", "nullable": false}, {"name": "F2", "type": "int8", "default": 2, "nullable": false}, {"name": "F3", "type": "uint8", "default": 11, "nullable": false}, {"name": "F4", "type": "int16", "default": 101, "nullable": false}, {"name": "F5", "type": "uint16", "default": 1001, "nullable": false}, {"name": "F8", "type": "boolean", "default": false, "nullable": false}, {"name": "F9", "type": "int64", "default": 1001, "nullable": false}, {"name": "F10", "type": "uint64", "default": 1001, "nullable": false}, {"name": "F11", "type": "float", "default": 2.2, "nullable": false}, {"name": "F12", "type": "double", "default": 11.86, "nullable": false}, {"name": "F13", "type": "time", "default": 1001, "nullable": false}, {"name": "F14", "type": "string", "default": "string", "nullable": false, "size": 100}, {"name": "F15", "type": "bytes", "default": "bytes", "nullable": false, "size": 10}, {"name": "F16", "type": "fixed", "default": "fixed", "nullable": false, "size": 5}, {"name": "F17", "type": "uint32", "default": 1001, "nullable": false}], "keys": [{"node": "T100_all_type_default", "name": "T100_PK", "fields": ["F6", "F7"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]