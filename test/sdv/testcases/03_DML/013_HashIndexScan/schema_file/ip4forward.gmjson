[{"version": "2.0", "type": "record", "name": "ip4forward", "fields": [{"name": "vr_id", "type": "uint32", "comment": "Vs索引"}, {"name": "vrf_index", "type": "uint32", "comment": "VpnInstace索引"}, {"name": "dest_ip_addr", "type": "uint32", "comment": "目的地址"}, {"name": "mask_len", "type": "uint8", "comment": "掩码长度"}, {"name": "nhp_group_flag", "type": "uint8", "comment": "标识Nhp或NhpG"}, {"name": "qos_profile_id", "type": "uint16", "comment": "QosID"}, {"name": "primary_label", "type": "uint32", "comment": "标签"}, {"name": "attribute_id", "type": "uint32", "comment": "属性ID"}, {"name": "nhp_group_id", "type": "uint32", "comment": "下一跳索引还是下一跳组索引，根据nhp_group_flag决定"}, {"name": "path_flags", "type": "uint32", "comment": "path标记"}, {"name": "flags", "type": "uint32", "comment": "标志(path完备性)"}, {"name": "status_high_prio", "type": "uint8"}, {"name": "status_normal_prio", "type": "uint8"}, {"name": "errcode_high_prio", "type": "uint8"}, {"name": "errcode_normal_prio", "type": "uint8"}, {"name": "svc_ctx_high_prio", "type": "fixed", "size": 16}, {"name": "T2", "type": "int32"}, {"name": "C1", "type": "int32"}, {"name": "C2", "type": "int8"}, {"name": "C3", "type": "uint8"}, {"name": "C4", "type": "char"}, {"name": "C5", "type": "uchar"}, {"name": "C6", "type": "string", "size": 100}, {"name": "T3", "type": "int32"}, {"name": "D1", "type": "int32"}, {"name": "D2", "type": "int8"}, {"name": "D3", "type": "uint8"}, {"name": "D4", "type": "uint32"}, {"name": "D5", "type": "char"}, {"name": "D6", "type": "uchar"}, {"name": "D7", "type": "string", "size": 100}, {"name": "D8", "type": "int64"}, {"name": "D9", "type": "uint64"}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "ip4forward", "fields": ["vr_id", "vrf_index"], "constraints": {"unique": true}, "comment": "根据主键索引"}]}]