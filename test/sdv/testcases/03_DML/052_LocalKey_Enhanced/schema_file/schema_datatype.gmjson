[{"version": "2.0", "config": {"check_validity": false}, "type": "record", "name": "schema_datatype", "fields": [{"name": "F0", "type": "int32"}, {"name": "F1", "type": "int8"}, {"name": "F2", "type": "uint8"}, {"name": "F3", "type": "int16"}, {"name": "F4", "type": "uint16"}, {"name": "F5", "type": "int32"}, {"name": "F6", "type": "uint32"}, {"name": "F7", "type": "int64"}, {"name": "F8", "type": "uint64"}, {"name": "F9", "type": "int"}, {"name": "F10", "type": "long"}, {"name": "F11", "type": "float"}, {"name": "F12", "type": "double"}, {"name": "F13", "type": "boolean", "nullable": true}, {"name": "F14", "type": "bytes", "size": 6, "nullable": true}, {"name": "F15", "type": "string", "size": 100, "nullable": true}, {"name": "F16", "type": "fixed", "size": 6, "nullable": true}, {"name": "F17", "type": "time", "nullable": true}, {"name": "F18", "type": "char"}, {"name": "F19", "type": "uchar"}], "keys": [{"name": "pk", "node": "schema_datatype", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"name": "hash", "node": "schema_datatype", "fields": ["F1", "F2"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": false}}]}]