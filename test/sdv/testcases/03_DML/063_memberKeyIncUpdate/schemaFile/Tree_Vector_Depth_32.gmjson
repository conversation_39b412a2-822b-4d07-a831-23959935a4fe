[{"type": "record", "name": "MS_Tree_Vector_32", "fields": [{"name": "F0", "type": "int64", "nullable": false}, {"name": "V01", "type": "record", "vector": true, "size": 3, "fields": [{"name": "F0", "type": "uint8", "nullable": true}, {"name": "F1", "type": "uint8", "nullable": true}, {"name": "V02", "type": "record", "fields": [{"name": "F0", "type": "uint8", "nullable": true}, {"name": "F1", "type": "uint8", "nullable": true}, {"name": "V03", "type": "record", "fields": [{"name": "F0", "type": "uint8", "nullable": true}, {"name": "F1", "type": "uint8", "nullable": true}, {"name": "V04", "type": "record", "fields": [{"name": "F0", "type": "uint8", "nullable": true}, {"name": "F1", "type": "uint8", "nullable": true}, {"name": "V05", "type": "record", "fields": [{"name": "F0", "type": "uint8", "nullable": true}, {"name": "F1", "type": "uint8", "nullable": true}, {"name": "V06", "type": "record", "fields": [{"name": "F0", "type": "uint8", "nullable": true}, {"name": "F1", "type": "uint8", "nullable": true}, {"name": "V07", "type": "record", "fields": [{"name": "F0", "type": "uint8", "nullable": true}, {"name": "F1", "type": "uint8", "nullable": true}, {"name": "V08", "type": "record", "fields": [{"name": "F0", "type": "uint8", "nullable": true}, {"name": "F1", "type": "uint8", "nullable": true}, {"name": "V09", "type": "record", "fields": [{"name": "F0", "type": "uint8", "nullable": true}, {"name": "F1", "type": "uint8", "nullable": true}, {"name": "V10", "type": "record", "fields": [{"name": "F0", "type": "uint8", "nullable": true}, {"name": "F1", "type": "uint8", "nullable": true}, {"name": "V11", "type": "record", "fields": [{"name": "F0", "type": "uint8", "nullable": true}, {"name": "F1", "type": "uint8", "nullable": true}, {"name": "V12", "type": "record", "fields": [{"name": "F0", "type": "uint8", "nullable": true}, {"name": "F1", "type": "uint8", "nullable": true}, {"name": "V13", "type": "record", "fields": [{"name": "F0", "type": "uint8", "nullable": true}, {"name": "F1", "type": "uint8", "nullable": true}, {"name": "V14", "type": "record", "fields": [{"name": "F0", "type": "uint8", "nullable": true}, {"name": "F1", "type": "uint8", "nullable": true}, {"name": "V15", "type": "record", "fields": [{"name": "F0", "type": "uint8", "nullable": true}, {"name": "F1", "type": "uint8", "nullable": true}, {"name": "V16", "type": "record", "fields": [{"name": "F0", "type": "uint8", "nullable": true}, {"name": "F1", "type": "uint8", "nullable": true}, {"name": "V17", "type": "record", "fields": [{"name": "F0", "type": "uint8", "nullable": true}, {"name": "F1", "type": "uint8", "nullable": true}, {"name": "V18", "type": "record", "fields": [{"name": "F0", "type": "uint8", "nullable": true}, {"name": "F1", "type": "uint8", "nullable": true}, {"name": "V19", "type": "record", "fields": [{"name": "F0", "type": "uint8", "nullable": true}, {"name": "F1", "type": "uint8", "nullable": true}, {"name": "V20", "type": "record", "fields": [{"name": "F0", "type": "uint8", "nullable": true}, {"name": "F1", "type": "uint8", "nullable": true}, {"name": "V21", "type": "record", "fields": [{"name": "F0", "type": "uint8", "nullable": true}, {"name": "F1", "type": "uint8", "nullable": true}, {"name": "V22", "type": "record", "fields": [{"name": "F0", "type": "uint8", "nullable": true}, {"name": "F1", "type": "uint8", "nullable": true}, {"name": "V23", "type": "record", "fields": [{"name": "F0", "type": "uint8", "nullable": true}, {"name": "F1", "type": "uint8", "nullable": true}, {"name": "V24", "type": "record", "fields": [{"name": "F0", "type": "uint8", "nullable": true}, {"name": "F1", "type": "uint8", "nullable": true}, {"name": "V25", "type": "record", "fields": [{"name": "F0", "type": "uint8", "nullable": true}, {"name": "F1", "type": "uint8", "nullable": true}, {"name": "V26", "type": "record", "fields": [{"name": "F0", "type": "uint8", "nullable": true}, {"name": "F1", "type": "uint8", "nullable": true}, {"name": "V27", "type": "record", "fields": [{"name": "F0", "type": "uint8", "nullable": true}, {"name": "F1", "type": "uint8", "nullable": true}, {"name": "V28", "type": "record", "fields": [{"name": "F0", "type": "uint8", "nullable": true}, {"name": "F1", "type": "uint8", "nullable": true}, {"name": "V29", "type": "record", "fields": [{"name": "F0", "type": "uint8", "nullable": true}, {"name": "F1", "type": "uint8", "nullable": true}, {"name": "V30", "type": "record", "fields": [{"name": "F0", "type": "uint8", "nullable": true}, {"name": "F1", "type": "uint8", "nullable": true}, {"name": "V31", "type": "record", "vector": true, "size": 10, "fields": [{"name": "F0", "type": "int64", "nullable": true}, {"name": "F1", "type": "uint64", "nullable": true}, {"name": "F2", "type": "int32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "int16", "nullable": true}, {"name": "F5", "type": "uint16", "nullable": true}, {"name": "F6", "type": "int8", "nullable": true}, {"name": "F7", "type": "uint8", "nullable": true}, {"name": "F11", "type": "time", "nullable": true}, {"name": "F14", "type": "string", "size": 20, "nullable": true}, {"name": "F16", "type": "fixed", "size": 7, "nullable": true}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}], "keys": [{"node": "MS_Tree_Vector_32", "name": "MS_Tree_Vector_32_PK", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "V01/V02/V03/V04/V05/V06/V07/V08/V09/V10/V11/V12/V13/V14/V15/V16/V17/V18/V19/V20/V21/V22/V23/V24/V25/V26/V27/V28/V29/V30/V31", "name": "member_key_V31", "index": {"type": "none"}, "fields": ["F0"], "constraints": {"unique": false}}]}]