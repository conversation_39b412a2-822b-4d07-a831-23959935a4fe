/* ****************************************************************************
 Description  : HashCluster支持唯一属性验证
 Node      :
    001 hashcluster为唯一索引，正常插入数据
    002 hashcluster为唯一索引，插入冲突数据
    003 hashcluster为唯一索引，merge冲突数据
    004 hashcluster为唯一索引，replace冲突数据
    005 hashcluster为唯一索引，update冲突数据
    006 hashcluster为唯一索引，通过hashcluster索引更新数据
    007 hashcluster为唯一索引，通过hashcluster索引删除数据
    008 hashcluster为唯一索引，通过hashcluster索引fetch数据
    009 导入hashcluster为唯一的表，导入hashcluster不冲突的数据
    010 导入hashcluster为唯一的表，导入hashcluster冲突的数据
 Author       : 黄楚灿 hwx1007418
 Modification :
 Date         : 2021/07/
 node :
**************************************************************************** */

extern "C" {
}

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "SecondIndex.h"

#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;

const char *g_labelName = "T25";
const char *g_PKName = "T25_PK";
const char *g_localName = "local_key1";
const char *g_LocalhashName = "localhash_key1";
const char *g_HashclusterName = "hashcluster_key1";
TestAlarmDataT g_before = {0};
TestAlarmDataT g_after = {0};

class HashClusterUnique_test : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/modifyCfg.sh  \"compatibleV3=0\"");
        system("sh $TEST_HOME/tools/start.sh -f");
        int ret;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
        system("sh $TEST_HOME/tools/modifyCfg.sh  \"compatibleV3=1\"");
        system("sh $TEST_HOME/tools/start.sh -f");
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void HashClusterUnique_test::SetUp()
{
    int ret = 0;
    char *schema = NULL;
    //建立连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schema_file/UniqueIndex_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    GmcDropVertexLabel(g_stmt, g_labelName);
    ret = GmcCreateVertexLabel(g_stmt, schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    ret = TestGmcGetAlarmData(&g_before);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void HashClusterUnique_test::TearDown()
{
    int ret = 0;
    AW_CHECK_LOG_END();
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    EXPECT_EQ(GMERR_OK, ret);
    // 断开连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = TestGmcGetAlarmData(&g_after);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(0, g_after.subConnRingFailTimes - g_before.subConnRingFailTimes);
}

// 001 hashcluster为唯一索引，正常插入数据
TEST_F(HashClusterUnique_test, DML_059_025)
{
    int ret = 0;
    uint64_t f7_value = 1;
    uint64_t locahash_value = 2, hashcluster_value = 3, local = 4, value = 4;

    // insert
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 30; i++) {
        WriteRecord_All(g_stmt, 0, 0, NULL, 0, &f7_value, &value, &locahash_value, &hashcluster_value, &local);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        f7_value++;
        locahash_value++;
        hashcluster_value++;
        local++;
        value++;
    }

    // fetch
    f7_value = 1, locahash_value = 2, hashcluster_value = 3, local = 4, value = 4;
    for (int i = 0; i < 30; i++) {
        int cnt = ReadAndCheck_Vertexdata(
            g_stmt, g_labelName, g_PKName, 0, true, &f7_value, &value, &locahash_value, &hashcluster_value, &local);
        EXPECT_EQ(1, cnt);
        f7_value++;
        locahash_value++;
        hashcluster_value++;
        local++;
        value++;
    }
}
// 002 hashcluster为唯一索引，插入冲突数据
TEST_F(HashClusterUnique_test, DML_059_026)
{
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int ret = 0;
    uint64_t f7_value = 1;
    uint64_t locahash_value = 2, hashcluster_value = 3, local = 4, value = 4;

    // insert
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    WriteRecord_All(g_stmt, 0, 0, NULL, 0, &f7_value, &value, &locahash_value, &hashcluster_value, &local);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    f7_value++;
    locahash_value++;
    // hashcluster_value++;
    local++;

    // insert collision record
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    WriteRecord_All(g_stmt, 0, 0, NULL, 0, &f7_value, &value, &locahash_value, &hashcluster_value, &local);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_UNIQUE_VIOLATION, ret);

    // fetch
    f7_value = 1, locahash_value = 2, hashcluster_value = 3, local = 4, value = 4;
    int cnt = ReadAndCheck_Vertexdata(
        g_stmt, g_labelName, g_PKName, 0, true, &f7_value, &value, &locahash_value, &hashcluster_value, &local);
    EXPECT_EQ(1, cnt);
    f7_value++;
    locahash_value++;
    // hashcluster_value++;
    local++;

    // fetch
    ret = ReadAndCheck_Vertexdata(
        g_stmt, g_labelName, g_PKName, 0, false, &f7_value, &value, &locahash_value, &hashcluster_value, &local);
    EXPECT_EQ(0, ret);
}
// 003 hashcluster为唯一索引，merge冲突数据
TEST_F(HashClusterUnique_test, DML_059_027)
{
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int ret = 0;
    uint64_t f7_value = 1;
    uint64_t locahash_value = 2, hashcluster_value = 3, local = 4, value = 4;

    // insert
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    WriteRecord_All(g_stmt, 0, 0, NULL, 0, &f7_value, &value, &locahash_value, &hashcluster_value, &local);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    f7_value++;
    locahash_value++;
    // hashcluster_value++;
    local++;
    value++;

    // insert collision record
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    set_IndexKeyValue_F7(g_stmt, f7_value);
    ret = GmcSetIndexKeyName(g_stmt, g_PKName);
    EXPECT_EQ(GMERR_OK, ret);
    WriteRecord_All(g_stmt, 0, 0, NULL, 0, NULL, &value, &locahash_value, &hashcluster_value, &local);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_UNIQUE_VIOLATION, ret);

    // fetch
    f7_value = 1, locahash_value = 2, hashcluster_value = 3, local = 4, value = 4;
    int cnt = ReadAndCheck_Vertexdata(
        g_stmt, g_labelName, g_PKName, 0, true, &f7_value, &value, &locahash_value, &hashcluster_value, &local);
    EXPECT_EQ(1, cnt);
    f7_value++;
    locahash_value++;
    // hashcluster_value++;
    local++;

    // fetch
    ret = ReadAndCheck_Vertexdata(
        g_stmt, g_labelName, g_PKName, 0, false, &f7_value, &value, &locahash_value, &hashcluster_value, &local);
    EXPECT_EQ(0, ret);
}
// 004 hashcluster为唯一索引，replace冲突数据
TEST_F(HashClusterUnique_test, DML_059_028)
{
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int ret = 0;
    uint64_t f7_value = 1;
    uint64_t locahash_value = 2, hashcluster_value = 3, local = 4, value = 4;

    // insert
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    WriteRecord_All(g_stmt, 0, 0, NULL, 0, &f7_value, &value, &locahash_value, &hashcluster_value, &local);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    f7_value++;
    locahash_value++;
    // hashcluster_value++;
    local++;
    value++;

    // insert collision record
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    set_IndexKeyValue_F7(g_stmt, f7_value);
    ret = GmcSetIndexKeyName(g_stmt, g_PKName);
    EXPECT_EQ(GMERR_OK, ret);
    WriteRecord_All(g_stmt, 0, 0, NULL, 0, NULL, &value, &locahash_value, &hashcluster_value, &local);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_UNIQUE_VIOLATION, ret);

    // fetch
    f7_value = 1, locahash_value = 2, hashcluster_value = 3, local = 4, value = 4;
    int cnt = ReadAndCheck_Vertexdata(
        g_stmt, g_labelName, g_PKName, 0, true, &f7_value, &value, &locahash_value, &hashcluster_value, &local);
    EXPECT_EQ(1, cnt);
    f7_value++;
    locahash_value++;
    // hashcluster_value++;
    local++;

    // fetch
    ret = ReadAndCheck_Vertexdata(
        g_stmt, g_labelName, g_PKName, 0, false, &f7_value, &value, &locahash_value, &hashcluster_value, &local);
    EXPECT_EQ(0, ret);
}
// 005 hashcluster为唯一索引，update冲突数据
TEST_F(HashClusterUnique_test, DML_059_029)
{
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int ret = 0;
    uint64_t f7_value = 1;
    uint64_t locahash_value = 2, hashcluster_value = 3, local = 4, value = 4;

    // insert
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 30; i++) {
        WriteRecord_All(g_stmt, 0, 0, NULL, 0, &f7_value, &value, &locahash_value, &hashcluster_value, &local);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        f7_value++;
        locahash_value++;
        hashcluster_value++;
        local++;
        value++;
    }

    // fetch
    f7_value = 1, locahash_value = 2, hashcluster_value = 3, local = 4, value = 4;
    for (int i = 0; i < 30; i++) {
        int cnt = ReadAndCheck_Vertexdata(
            g_stmt, g_labelName, g_PKName, 0, true, &f7_value, &value, &locahash_value, &hashcluster_value, &local);
        EXPECT_EQ(1, cnt);
        f7_value++;
        locahash_value++;
        hashcluster_value++;
        local++;
        value++;
    }

    // primary update
    f7_value = 1, locahash_value = 100, hashcluster_value = 10, local = 100;
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    set_IndexKeyValue_F7(g_stmt, f7_value);
    set_VertexProperty_hashcluster(g_stmt, hashcluster_value, &local);
    ret = GmcSetIndexKeyName(g_stmt, g_PKName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_UNIQUE_VIOLATION, ret);
    // locahash update
    f7_value = 1, locahash_value = 2, hashcluster_value = 10, local = 100;
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    int8_t f2_value = locahash_value;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT8, &f2_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);
    set_VertexProperty_hashcluster(g_stmt, hashcluster_value, &local);
    ret = GmcSetIndexKeyName(g_stmt, g_LocalhashName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_UNIQUE_VIOLATION, ret);
    // local update
    f7_value = 1, locahash_value = 100, hashcluster_value = 10, local = 4;
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    uint16_t f5_value = local;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint8_t f3_value = hashcluster_value;
    ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_UINT8, &f3_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, g_localName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_UNIQUE_VIOLATION, ret);

    // fetch
    f7_value = 1, locahash_value = 2, hashcluster_value = 3, local = 4, value = 4;
    for (int i = 0; i < 30; i++) {
        int cnt = ReadAndCheck_Vertexdata(
            g_stmt, g_labelName, g_PKName, 0, true, &f7_value, &value, &locahash_value, &hashcluster_value, &local);
        EXPECT_EQ(1, cnt);
        f7_value++;
        locahash_value++;
        hashcluster_value++;
        local++;
        value++;
    }
}
// 006 hashcluster为唯一索引，通过hashcluster索引更新数据
TEST_F(HashClusterUnique_test, DML_059_030)
{
    int ret = 0;
    uint64_t f7_value = 1;
    uint64_t locahash_value = 2, hashcluster_value = 3, local = 4, value = 4;

    // insert
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 30; i++) {
        WriteRecord_All(g_stmt, 0, 0, NULL, 0, &f7_value, &value, &locahash_value, &hashcluster_value, &local);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        f7_value++;
        locahash_value++;
        hashcluster_value++;
        local++;
        value++;
    }

    // fetch
    f7_value = 1, locahash_value = 2, hashcluster_value = 3, local = 4, value = 4;
    for (int i = 0; i < 30; i++) {
        int cnt = ReadAndCheck_Vertexdata(
            g_stmt, g_labelName, g_PKName, 0, true, &f7_value, &value, &locahash_value, &hashcluster_value, &local);
        EXPECT_EQ(1, cnt);
        f7_value++;
        locahash_value++;
        hashcluster_value++;
        local++;
        value++;
    }

    hashcluster_value = 3;
    locahash_value = 102, local = 104, value = 104;
    // hashcluster update
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        uint8_t f3_value = hashcluster_value;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT8, &f3_value, sizeof(uint8_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_HashclusterName);
        EXPECT_EQ(GMERR_OK, ret);
        WriteRecord_All(g_stmt, 0, 0, NULL, 0, NULL, &value, &locahash_value, NULL, &local, false);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        locahash_value++;
        hashcluster_value++;
        local++;
        value++;
    }

    // fetch
    hashcluster_value = 3;
    f7_value = 1, locahash_value = 102, local = 104, value = 104;
    for (int i = 0; i < 10; i++) {
        int cnt = ReadAndCheck_Vertexdata(
            g_stmt, g_labelName, g_PKName, 0, true, &f7_value, &value, &locahash_value, &hashcluster_value, &local);
        EXPECT_EQ(1, cnt);
        f7_value++;
        locahash_value++;
        hashcluster_value++;
        local++;
        value++;
    }
}
// 007 hashcluster为唯一索引，通过hashcluster索引删除数据
TEST_F(HashClusterUnique_test, DML_059_031)
{
    int ret = 0;
    uint64_t f7_value = 1;
    uint64_t locahash_value = 2, hashcluster_value = 3, local = 4, value = 4;

    // insert
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 30; i++) {
        WriteRecord_All(g_stmt, 0, 0, NULL, 0, &f7_value, &value, &locahash_value, &hashcluster_value, &local);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        f7_value++;
        locahash_value++;
        hashcluster_value++;
        local++;
        value++;
    }

    hashcluster_value = 3;
    // hashcluster delete
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        uint8_t f3_value = hashcluster_value;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT8, &f3_value, sizeof(uint8_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_HashclusterName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        hashcluster_value++;
    }

    // fetch
    hashcluster_value = 3;
    f7_value = 101, locahash_value = 102, local = 104, value = 104;
    for (int i = 0; i < 10; i++) {
        ret = ReadAndCheck_Vertexdata(
            g_stmt, g_labelName, g_PKName, 0, false, &f7_value, &value, &locahash_value, &hashcluster_value, &local);
        EXPECT_EQ(0, ret);
        f7_value++;
    }
}
// 008 hashcluster为唯一索引，通过hashcluster索引fetch数据
TEST_F(HashClusterUnique_test, DML_059_032)
{
    int ret = 0;
    uint64_t f7_value = 1;
    uint64_t locahash_value = 2, hashcluster_value = 3, local = 4, value = 4;

    // insert
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 30; i++) {
        WriteRecord_All(g_stmt, 0, 0, NULL, 0, &f7_value, &value, &locahash_value, &hashcluster_value, &local);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        f7_value++;
        locahash_value++;
        hashcluster_value++;
        local++;
        value++;
    }

    // fetch
    hashcluster_value = 3;
    f7_value = 1, locahash_value = 2, local = 4, value = 4;
    for (int i = 0; i < 30; i++) {
        int cnt = ReadAndCheck_Vertexdata(g_stmt, g_labelName, g_HashclusterName, 1, false, &f7_value, &value,
            &locahash_value, &hashcluster_value, &local);
        EXPECT_EQ(1, cnt);
        f7_value++;
        locahash_value++;
        hashcluster_value++;
        local++;
        value++;
    }
}
// 009 导入hashcluster为唯一的表，导入hashcluster不冲突的数据
TEST_F(HashClusterUnique_test, DML_059_047)
{
    int ret = 0;
    uint64_t f7_value = 1;
    uint64_t locahash_value = 2, hashcluster_value = 3, local = 4, value = 4;

    // insert
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 30; i++) {
        WriteRecord_All(g_stmt, 0, 0, NULL, 0, &f7_value, &value, &locahash_value, &hashcluster_value, &local);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        f7_value++;
        locahash_value++;
        hashcluster_value++;
        local++;
        value++;
    }

    // fetch
    hashcluster_value = 3;
    f7_value = 1, locahash_value = 2, local = 4, value = 4;
    for (int i = 0; i < 30; i++) {
        int cnt = ReadAndCheck_Vertexdata(g_stmt, g_labelName, g_HashclusterName, 1, false, &f7_value, &value,
            &locahash_value, &hashcluster_value, &local);
        EXPECT_EQ(1, cnt);
        f7_value++;
        locahash_value++;
        hashcluster_value++;
        local++;
        value++;
    }

    char const *g_filePath = "./vertexdata/";
    char const *g_filePath1 = "./vertexjson/";
    char const *g_vertexPath = "./vertexjson/T25.gmjson";
    char const *g_dataPath = "./vertexdata/T25.gmdata";
    // export 导出schema
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmexport -c vschema -t %s -f %s  -s %s -ns %s",
        g_toolPath, g_labelName,
        g_filePath1, g_connServer, g_testNameSpace);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Command type: export_vschema, export file successfully.");
    ASSERT_EQ(GMERR_OK, ret);

    // export 导出data数据
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmexport -c vdata -t %s -f %s  -s %s -ns %s",
        g_toolPath, g_labelName,
        g_filePath, g_connServer, g_testNameSpace);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Command type: export_vdata, export file successfully.");
    ASSERT_EQ(GMERR_OK, ret);

    //删除vertexlabel
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    EXPECT_EQ(GMERR_OK, ret);

    //导入schema
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vschema -f %s  -s %s -ns %s",
        g_toolPath, g_vertexPath,
        g_connServer, g_testNameSpace);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "Command type: import_vschema",
        "GMDBV5/test/sdv/testcases/03_DML/059_SecondIndexEnhance/vertexjson/T25.gmjson\" successfully");
    ASSERT_EQ(GMERR_OK, ret);

    //导入data
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vdata -f %s  -s %s -ns %s",
        g_toolPath, g_dataPath,
        g_connServer, g_testNameSpace);
    printf("%s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "Insert data succeed. totalNum: 30, successNum: 30", "Command type: import_vdata",
        "GMDBV5/test/sdv/testcases/03_DML/059_SecondIndexEnhance/vertexdata/T25.gmdata\" successfully");
    ASSERT_EQ(GMERR_OK, ret);

    // fetch
    hashcluster_value = 3;
    f7_value = 1, locahash_value = 2, local = 4, value = 4;
    for (int i = 0; i < 30; i++) {
        int cnt = ReadAndCheck_Vertexdata(g_stmt, g_labelName, g_HashclusterName, 1, false, &f7_value, &value,
            &locahash_value, &hashcluster_value, &local);
        EXPECT_EQ(1, cnt);
        f7_value++;
        locahash_value++;
        hashcluster_value++;
        local++;
        value++;
    }
}
// 010 导入hashcluster为唯一的表，导入hashcluster冲突的数据
TEST_F(HashClusterUnique_test, DML_059_048)
{
    int ret = 0;
    uint64_t f7_value = 1;
    uint64_t locahash_value = 2, hashcluster_value = 3, local = 4, value = 4;

    char const *g_vertexPath = "./vertexjson/CheckHashc.vertexjson";
    char const *g_dataPath = "./vertexdata/CheckHashc.vertexdata";

    //添加错误白名单
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    //删除vertexlabel
    GmcDropVertexLabel(g_stmt, "CheckHashc");

    //导入schema
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vschema -f %s  -s %s -ns %s",
        g_toolPath, g_vertexPath,
        g_connServer, g_testNameSpace);
    printf("%s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "Command type: import_vschema",
        "GMDBV5/test/sdv/testcases/03_DML/059_SecondIndexEnhance/vertexjson/CheckHashc.vertexjson\" successfully");
    ASSERT_EQ(GMERR_OK, ret);

    //导入冲突data，冲突记录导入失败，其他导入成功
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vdata -f %s  -s %s -ns %s", g_toolPath, g_dataPath,
        g_connServer, g_testNameSpace);
    printf("%s\n", g_command);
    // system(g_command);
    ret = executeCommand(g_command, "insert partial data unsucc. totalNum: 30, successNum: 29, duplicateNum: 1",
        "Command type: import_vdata, Import file from",
        "GMDBV5/test/sdv/testcases/03_DML/059_SecondIndexEnhance/vertexdata/CheckHashc.vertexdata\" successfully");
    ASSERT_EQ(GMERR_OK, ret);

    // fetch
    hashcluster_value = 3;
    f7_value = 1, locahash_value = 2, local = 4, value = 4;
    for (int i = 0; i < 30; i++) {
        if (i == 29) {
            int cnt = ReadAndCheck_Vertexdata(g_stmt, "CheckHashc", g_HashclusterName, 1, false, &f7_value, &value,
                &locahash_value, &hashcluster_value, &local);
            EXPECT_EQ(0, cnt);
            continue;
        }
        int cnt = ReadAndCheck_Vertexdata(g_stmt, "CheckHashc", g_HashclusterName, 1, true, &f7_value, &value,
            &locahash_value, &hashcluster_value, &local);
        EXPECT_EQ(1, cnt);
        f7_value++;
        locahash_value++;
        hashcluster_value++;
        local++;
        value++;
    }
    ret = GmcDropVertexLabel(g_stmt, "CheckHashc");
    ASSERT_EQ(GMERR_OK, ret);
}
