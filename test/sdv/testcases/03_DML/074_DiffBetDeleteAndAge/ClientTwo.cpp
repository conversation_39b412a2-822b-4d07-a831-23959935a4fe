#include "t_datacom_lite.h"

static const char *g_subLabelJson =
    R"({
        "name":"subInfo1",
        "label_name":"DML_074_021",
        "type":"before_commit",
        "events":
            [
                {"type":"insert", "msgTypes": ["new object", "key"]},
                {"type":"update", "msgTypes": ["new object", "old object"]},
                {"type":"delete", "msgTypes": ["old object", "key"]},
                {"type":"replace", "msgTypes":["new object", "old object"]}
            ],
        "is_path":false,
        "retry":false,
        "persist":false
    })";

void OldSnCallBack(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret = 0;
    char labelName[128] = {0};
    unsigned int labelNameLen = 0;
    bool eof = false;

    while (!eof) {
        if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
            break;
        }

        ret = GmcFetch(subStmt, &eof);  // fetch 1 次
        if (ret != GMERR_OK || eof == true) {
            break;
        }

        for (int i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            ret = GmcSubGetLabelName(subStmt, i, labelName, &(labelNameLen = sizeof(labelName)));
            TEST_EXPECT_INT32(GMERR_OK, ret);
            TEST_EXPECT_INT32(strlen(labelName), labelNameLen);

            // 默认推送new object和old object
            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    TEST_EXPECT_INT32(GMERR_OK, ret);

                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    TEST_EXPECT_INT32(GMERR_NO_DATA, ret);
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    TEST_EXPECT_INT32(GMERR_NO_DATA, ret);

                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_UPDATE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    TEST_EXPECT_INT32(GMERR_OK, ret);

                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_REPLACE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    TEST_EXPECT_INT32(GMERR_OK, ret);

                    break;
                }
                case GMC_SUB_EVENT_AGED: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    TEST_EXPECT_INT32(GMERR_NO_DATA, ret);
                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_INITIAL_LOAD: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    TEST_EXPECT_INT32(GMERR_NO_DATA, ret);
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_INFO, "default: [%s] [%d] invalid eventType %d.\n", __FUNCTION__, __LINE__,
                        info->eventType);
                    break;
                }
            }
            break;
        }
    }
}

int main()
{
    int ret;

    // 创建老订阅
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    int chanRingLen = 256;
    GmcConnT *connSub = NULL, *conn = NULL;
    GmcStmtT *stmtSub = NULL, *stmt = NULL;
    const char *subConnName = "subConn";
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);
    const char *subName = "subInfo1";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subLabelJson;
    ret = GmcSubscribe(stmt, &subInfo, connSub, OldSnCallBack, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    const char *subName2 = "subInfo2";
    GmcSubConfigT subInfo2;
    subInfo2.subsName = subName2;
    subInfo2.configJson = g_subLabelJson;
    ret = GmcSubscribe(stmt, &subInfo2, connSub, OldSnCallBack, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    sleep(5);

        // 取消订阅
    ret = GmcUnSubscribe(stmt, subName);
    EXPECT_EQ(GMERR_OK, ret);
}
