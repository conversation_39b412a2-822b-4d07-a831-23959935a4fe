[{"name": "bigobject", "type": "record", "version": "2.0", "fields": [{"name": "F0", "type": "int32"}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "int32"}, {"name": "F3", "type": "int32"}, {"name": "F4", "type": "int32"}, {"name": "F5", "type": "int32"}, {"name": "F6", "type": "string", "nullable": true}, {"name": "F7", "type": "string", "nullable": true}, {"name": "F8", "type": "string", "nullable": true}, {"name": "F9", "type": "string", "nullable": true}, {"name": "F10", "type": "string", "nullable": true}, {"name": "F11", "type": "string", "nullable": true}, {"name": "F12", "type": "string", "nullable": true}, {"name": "F13", "type": "string", "nullable": true}, {"name": "F14", "type": "string", "nullable": true}], "keys": [{"fields": ["F0"], "index": {"type": "primary"}, "name": "pk", "node": "bigobject"}]}]