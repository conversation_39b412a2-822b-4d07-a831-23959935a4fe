[{"comment": "前缀表，对应7#表", "version": "2.0", "type": "record", "name": "ip4forward", "fields": [{"name": "vr_id", "type": "uint32", "comment": "Vs索引"}, {"name": "vrf_index", "type": "uint32", "comment": "VpnInstace索引"}, {"name": "dest_ip_addr", "type": "uint32", "comment": "目的地址"}, {"name": "mask_len", "type": "uint8", "comment": "掩码长度"}, {"name": "nhp_group_flag", "type": "uint8", "comment": "标识Nhp或NhpG"}, {"name": "qos_profile_id", "type": "uint16", "comment": "QosID"}, {"name": "primary_label", "type": "uint32", "comment": "标签"}, {"name": "attribute_id", "type": "uint32", "comment": "属性ID"}, {"name": "nhp_group_id", "type": "uint32", "comment": "下一跳索引还是下一跳组索引，根据nhp_group_flag决定"}, {"name": "path_flags", "type": "uint32", "comment": "path标记"}, {"name": "flags", "type": "uint32", "comment": "标志(path完备性)"}, {"name": "status_high_prio", "type": "uint8"}, {"name": "status_normal_prio", "type": "uint8"}, {"name": "errcode_high_prio", "type": "uint8"}, {"name": "errcode_normal_prio", "type": "uint8", "default": 1}, {"name": "svc_ctx_high_prio", "type": "fixed", "size": 34, "default": "ffffffffffffffffffffffffffffffffff", "comment": "高优先级FWM_SERVICE返回的svcCtx"}, {"name": "svc_ctx_normal_prio", "type": "fixed", "size": 34, "default": "ffffffffffffffffffffffffffffffffff", "comment": "普通优先级FWM_SERVICE返回的svcCtx"}, {"name": "app_source_id", "type": "uint32"}, {"name": "table_smooth_id", "type": "uint32"}, {"name": "app_obj_id", "type": "uint64"}, {"name": "app_version", "type": "uint32"}, {"name": "trace", "type": "uint64"}, {"name": "route_flags", "type": "uint16", "comment": "路由标记"}, {"name": "reserved", "type": "uint16", "comment": "预留"}, {"name": "test_int8", "type": "int8"}, {"name": "test_int16", "type": "int16"}, {"name": "test_int32", "type": "int32"}, {"name": "test_int64", "type": "int64"}, {"name": "test_float", "type": "float"}, {"name": "test_double", "type": "double"}, {"name": "test_str", "type": "string", "size": 100, "nullable": true}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "ip4forward", "fields": ["vr_id", "vrf_index", "dest_ip_addr"], "constraints": {"unique": true}, "comment": "根据主键索引"}, {"name": "localhash_1", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "ip4forward", "fields": ["vr_id"], "constraints": {"unique": false}}, {"name": "localhash_2", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "ip4forward", "fields": ["vrf_index"], "constraints": {"unique": false}}, {"name": "localhash_3", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "ip4forward", "fields": ["dest_ip_addr"], "constraints": {"unique": false}}, {"name": "localhash_4", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "ip4forward", "fields": ["mask_len"], "constraints": {"unique": false}}, {"name": "localhash_5", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "ip4forward", "fields": ["nhp_group_flag"], "constraints": {"unique": false}}, {"name": "localhash_6", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "ip4forward", "fields": ["qos_profile_id"], "constraints": {"unique": false}}, {"name": "localhash_7", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "ip4forward", "fields": ["primary_label"], "constraints": {"unique": false}}, {"name": "localhash_8", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "ip4forward", "fields": ["attribute_id"], "constraints": {"unique": false}}, {"name": "localhash_9", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "ip4forward", "fields": ["nhp_group_id"], "constraints": {"unique": false}}, {"name": "localhash_10", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "ip4forward", "fields": ["path_flags"], "constraints": {"unique": false}}, {"name": "localhash_11", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "ip4forward", "fields": ["flags"], "constraints": {"unique": false}}, {"name": "localhash_12", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "ip4forward", "fields": ["status_high_prio"], "constraints": {"unique": false}}, {"name": "localhash_13", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "ip4forward", "fields": ["status_normal_prio"], "constraints": {"unique": false}}, {"name": "localhash_14", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "ip4forward", "fields": ["errcode_high_prio"], "constraints": {"unique": false}}, {"name": "localhash_15", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "ip4forward", "fields": ["errcode_normal_prio"], "constraints": {"unique": false}}, {"name": "localhash_16", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "ip4forward", "fields": ["svc_ctx_high_prio"], "constraints": {"unique": false}}]}]