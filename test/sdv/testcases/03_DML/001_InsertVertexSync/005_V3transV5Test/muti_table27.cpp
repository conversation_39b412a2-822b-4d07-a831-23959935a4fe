/* ****************************************************************************
 Description  :027.多线程并发建多张表并写数据，删除操作
线程1：20线程并发建20张表写100条记录
线程2：20线程并发删：等线程1写完才开始删
 Author       : wuxiaochun wx753022
 Modification :
 Date         : 2021/08/20
**************************************************************************** */

#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include <sys/time.h>
#include <time.h>

#include "gtest/gtest.h"

#include "V3transV5Test.h"

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
char *g_pkname = (char *)"T20_PK";
char g_labelName1[16] = "T20_all_type";
#define INSERT_NUM 100
pthread_barrier_t barrier1;
pthread_barrier_t barrier2;
#define MULTI_THREAD_NUM 20
static int remove_flag[MULTI_THREAD_NUM + 1] = {0};
uint32_t g_thrNum = 0;

class V3transV5MultiThread_test : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void V3transV5MultiThread_test::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/start.sh");
    int32_t ret = 0;
#if defined ENV_RTOSV2X
    uint32_t currUseConn = 0;
    uint32_t currFreeConn = 0;
    ret = testGetConnNum(&currUseConn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    currFreeConn = MAX_RTOSV2X_CONN_NUM - currUseConn;
    // 空2个链接保证稳定性，write和update并发，线程各占1个，故除以2
    g_thrNum = ((currFreeConn - 2) / 2) > MULTI_THREAD_NUM ? MULTI_THREAD_NUM : ((currFreeConn - 2) / 2);
    AW_FUN_Log(
        LOG_INFO, "rtosv2x currUseConn = %u, currFreeConn = %u , g_thrNum = %u.", currUseConn, currFreeConn, g_thrNum);
#else
    g_thrNum = MULTI_THREAD_NUM;
#endif
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    ASSERT_EQ(GMERR_OK, ret);
}

void V3transV5MultiThread_test::TearDownTestCase()
{
    int ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    testEnvClean();
}

void V3transV5MultiThread_test::SetUp()
{
    AW_CHECK_LOG_BEGIN();
}

void V3transV5MultiThread_test::TearDown()
{
    AW_CHECK_LOG_END();
}

void *create_write_thread_func(void *args)
{
    int32_t ret = 0;
    int32_t id = *(int32_t *)args;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    char *test_schema = NULL;
    uint32_t affectRows = 0;
    char labelName[128] = {0};
    char schema_path[1024] = {0};
    sprintf(schema_path, "./multi_vertexlabel/all_type_schema_%d.gmjson", id);
    readJanssonFile(schema_path, &test_schema);
    EXPECT_NE((void *)NULL, test_schema);
    sprintf(labelName, "T20_all_type_%d", id);
    printf("[INFO] create label %s\n", labelName);
    pthread_barrier_wait(&barrier1);
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    ret = GmcCreateVertexLabel(stmt, test_schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(test_schema);
    //写数据
    for (int32_t i = 0; i < INSERT_NUM; i++) {
        uint32_t priK = i;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        test_setPropertyPk(stmt, priK);
        test_setVertexProperty(stmt, priK);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    remove_flag[id] = 1;
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

void *remove_thread_func(void *args)
{
    int32_t ret = 0;
    int32_t id = *(int32_t *)args;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    uint32_t affectRows = 0;
    char labelName[128] = {0};
    sprintf(labelName, "T20_all_type_%d", id);
    printf("[INFO] remove label %s\n", labelName);
    while (!remove_flag[id]) {
        usleep(1000);
    }
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    //删数据
    for (int32_t i = 0; i < INSERT_NUM; i++) {
        uint32_t priK = i;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &priK, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, g_pkname);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    remove_flag[id] = 0;
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

void drop_thread_func()
{
    int32_t ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    char labelName[128] = {0};

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (int32_t i = 1; i <= g_thrNum; i++) {
        sprintf(labelName, "T20_all_type_%d", i);
        ret = GmcDropVertexLabel(stmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return;
}

TEST_F(V3transV5MultiThread_test, DML_001_V3_003_021)
{
    int ret = 0;
    int index[MULTI_THREAD_NUM + 1] = {0};
    int remove_index[MULTI_THREAD_NUM + 1] = {0};
    pthread_t create_thread[MULTI_THREAD_NUM + 1], remove_thread[MULTI_THREAD_NUM + 1];
    pthread_barrier_init(&barrier1, NULL, g_thrNum);

    system("sh create_multi_label.sh 20");

    for (int32_t i = 1; i <= g_thrNum; i++) {
        remove_flag[i] = 0;
        index[i] = i;
        remove_index[i] = i;
        ret = pthread_create(&create_thread[i], NULL, create_write_thread_func, (void *)&index[i]);
        EXPECT_EQ(GMERR_OK, ret);
        ret = pthread_create(&remove_thread[i], NULL, remove_thread_func, (void *)&remove_index[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }

    for (int32_t i = 1; i <= g_thrNum; i++) {
        ret = pthread_join(create_thread[i], NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = pthread_join(remove_thread[i], NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    pthread_barrier_destroy(&barrier1);

    drop_thread_func();
}
