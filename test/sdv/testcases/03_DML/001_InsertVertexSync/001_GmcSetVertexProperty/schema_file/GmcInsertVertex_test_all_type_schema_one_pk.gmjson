[{"type": "record", "name": "T39_all_type_one_pk", "fields": [{"name": "F0", "type": "int8", "nullable": true}, {"name": "F1", "type": "uint8", "nullable": true}, {"name": "F2", "type": "int16", "nullable": false}, {"name": "F3", "type": "uint16", "nullable": false}, {"name": "F4", "type": "int32", "nullable": false}, {"name": "F5", "type": "uint32", "nullable": false}, {"name": "F6", "type": "boolean", "nullable": false}, {"name": "F7", "type": "int64", "nullable": false}, {"name": "F8", "type": "uint64", "nullable": false}, {"name": "F9", "type": "float", "nullable": false}, {"name": "F10", "type": "double", "nullable": false}, {"name": "F11", "type": "time", "nullable": false}, {"name": "F12", "type": "string", "nullable": false, "size": 100}], "keys": [{"node": "T39_all_type_one_pk", "name": "PK", "fields": ["F4"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]