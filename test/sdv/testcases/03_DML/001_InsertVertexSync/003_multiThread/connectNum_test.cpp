extern "C" {
}

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"

int ret = 0;

class escape_route_conn : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh");
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void escape_route_conn::SetUp()
{
    AW_CHECK_LOG_BEGIN();
}
void escape_route_conn::TearDown()
{
    AW_CHECK_LOG_END();
}

//获取连接数的个数
int executeCommand_connect(char *cmd, const char *v1, int *connectNum)
{
    (*connectNum) = 0;
    char buffer[1024] = {0};
    FILE *pf = popen(cmd, "r");
    if (pf == NULL) {
        printf("popen(%s) error.\n", cmd);
        return -1;
    }
    while (NULL != fgets(buffer, 500, pf)) {
        if (strstr(buffer, "index")) {
            (*connectNum)++;
        }
    }
    pclose(pf);
    pf = NULL;
    return 0;
}

/*****************************************************************************
 * Description  : 001
 * 超时断连时间默认为1min:建立1022个连接，管理员用户占满2个连接后，等待1min，查看连接数，再使用管理员用户申请2个连接，预期成功；
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : wuxiaochun
 * Modification : Create function
 * ****************************************************************************/
TEST_F(escape_route_conn, CONNECT_003_001)
{
    char errorMsg1[128] = {}, errorMsg2[128] = {}, errorMsg3[128] = {}, errorMsg4[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_INSUFFICIENT_RESOURCES);
    (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_MEMORY_OPERATE_FAILED);
    (void)snprintf(errorMsg3, sizeof(errorMsg3), "GMERR-%d", GMERR_UNEXPECTED_NULL_VALUE);
    (void)snprintf(errorMsg4, sizeof(errorMsg4), "GMERR-%d", GMERR_TOO_MANY_CONNECTIONS);
    AW_ADD_ERR_WHITE_LIST(4, errorMsg1, errorMsg2, errorMsg3, errorMsg4);
    GmcConnT *conn_t[MAX_CONN_SIZE] = {0};
    GmcConnT *conn = NULL;
    int i;

    //先建1022个连接
    for (i = 0; i < (MAX_CONN_SIZE - 2); i++) {
        ret = testGmcConnect(&conn_t[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    //管理员建2个连接
    for (i = (MAX_CONN_SIZE - 2); i < MAX_CONN_SIZE; i++) {
        ret = testGmcConnect(&conn_t[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }

    printf("wait 60s for Escape route disconnect\n");
    sleep(60);
    //查看连接数
    char *command = (char *)malloc(1024);
    int connect_num = 0;
    char const *view_name = "V\\$DRT_CONN_STAT";
    snprintf(command, 1024, "%s/gmsysview -u %s -s %s -p %s  -q %s", g_toolPath, g_userName, g_connServer, g_passwd,
        view_name);
    ret = executeCommand_connect(command, (const char *)"index", &connect_num);
    EXPECT_EQ(GMERR_OK, ret);
    printf("connect_num: %d\n", connect_num);
    EXPECT_EQ(1023, connect_num);

    free(command);
    //管理员再次申请2个连接
    for (i = (MAX_CONN_SIZE - 2); i < MAX_CONN_SIZE; i++) {
        ret = testGmcConnect(&conn_t[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    for (i = 0; i < MAX_CONN_SIZE; i++) {
        ret = testGmcDisconnect(conn_t[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
}
