[{"comment": "下一跳组结点表，对应9#表", "version": "2.0", "type": "record", "name": "nhp_group_node", "fields": [{"name": "nhp_group_id", "type": "uint32"}, {"name": "attribute_id", "type": "uint32"}, {"name": "primary_nhp_id", "type": "uint32"}, {"name": "primary_label", "type": "uint32"}, {"name": "backup_nhp_id", "type": "uint32"}, {"name": "backup_label", "type": "uint32"}, {"name": "vr_id", "type": "uint32"}, {"name": "vrf_index", "type": "uint32", "nullable": true}, {"name": "flags", "type": "uint32", "nullable": true}, {"name": "app_source_id", "type": "uint32", "nullable": true}, {"name": "group_smooth_id", "type": "uint32", "nullable": true}, {"name": "app_obj_id", "type": "uint64", "nullable": true}, {"name": "app_version", "type": "uint32", "nullable": true}, {"name": "time_stamp_create", "type": "time", "nullable": true}, {"name": "time_stamp_smooth", "type": "time", "nullable": true}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "nhp_group_node", "fields": ["nhp_group_id", "attribute_id", "primary_nhp_id", "primary_label", "backup_nhp_id", "backup_label", "vr_id"], "constraints": {"unique": true}, "comment": "根据主键索引"}, {"node": "nhp_group_node", "name": "nhp_group_node_hash", "fields": ["vr_id", "nhp_group_id"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": false}}]}]