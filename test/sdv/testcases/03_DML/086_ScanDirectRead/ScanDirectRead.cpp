/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: 086_ScanDirectRead
 * Author: hanyang
 * Create: 2022-1-29
 */
#include "ScanDirectRead_test.h"

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;

class ScanDirectRead_test : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void ScanDirectRead_test::SetUpTestCase()
{
    int ret;
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    ASSERT_EQ(GMERR_OK, ret);
}

void ScanDirectRead_test::TearDownTestCase()
{
    int ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    testEnvClean();
}

void ScanDirectRead_test::SetUp()
{
    int ret;
    ret = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // 创建Vertex和KV表
    testCreateLabel(g_stmt);
    testCreateKVTable(g_stmt);

    AW_CHECK_LOG_BEGIN();
}

void ScanDirectRead_test::TearDown()
{
    int ret;
    AW_CHECK_LOG_END();

    // 删除Vertex和KV表
    testDropLabel(g_stmt, Vertex_Name);
    testDropKVTable(g_stmt);

    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
}

// DML
void *Thread_001_01(void *args)
{
    int ret;
    uint32_t initValue = 100;
    uint32_t newValue;
    uint32_t times = 100;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    printf("==============[1] DML Thread Begin==================\n\n");
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入数据[100-199]
    testInsertVertexLabel(stmt, times, initValue, Vertex_Name);

    // 更新数据[100-199]
    initValue = 100;
    newValue = 200;
    testUpdateVertexLabel(stmt, Vertex_KeyName, times, initValue, newValue, Vertex_Name);

    // 删除数据[100-199]
    initValue = 100;
    testDeleteVertexLabel(stmt, Vertex_KeyName, times, initValue, Vertex_Name);

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    printf("==============[1] DML Thread End==================\n\n");
    return NULL;
}

// 主键读
void *Thread_001_02(void *args)
{
    int ret;
    uint32_t i;
    uint32_t value;
    uint32_t initValue = 0;
    uint32_t times = 100;
    uint32_t succNum = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // select vertex[0-99]
    for (i = 0; i < times; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, Vertex_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        value = initValue + i;
        // 设置Filter
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        // select
        ret = GmcSetIndexKeyName(stmt, Vertex_KeyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret == GMERR_OK) {
            succNum++;
        }

        // 校验值
        value = (initValue + i) % 10;
        TestGmcGetVertexPropertyByName(stmt, value);
    }

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    printf("==============[2] Select Key Thread Succ Num(%d)==================\n\n", succNum);
    return NULL;
}

// Full Scan
void *Thread_001_03(void *args)
{
    int ret;
    uint32_t succNum = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // scan vertex
    ret = testGmcPrepareStmtByLabelName(stmt, Vertex_Name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;

    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (GMERR_OK != ret) {
            EXPECT_EQ(GMERR_OK, ret);
            ret = testGmcGetLastError();
            EXPECT_EQ(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        succNum++;
    }

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    printf("==============[3] Full Scan Thread Succ Num(%d)==================\n\n", succNum);
    return NULL;
}

// LocalHash Scan
void *Thread_001_04(void *args)
{
    int ret;
    uint32_t i;
    uint32_t value;
    uint32_t succNum = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // scan vertex
    ret = testGmcPrepareStmtByLabelName(stmt, Vertex_Name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    value = 1;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, Vertex_LocalHashName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;

    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (GMERR_OK != ret) {
            EXPECT_EQ(GMERR_OK, ret);
            ret = testGmcGetLastError();
            EXPECT_EQ(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        succNum++;
    }

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    printf("==============[4] LocalHash Scan Thread Succ Num(%d)==================\n\n", succNum);
    return NULL;
}

// HashCluster
void *Thread_001_05(void *args)
{
    int ret;
    uint32_t i;
    uint32_t value;
    uint32_t succNum = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // scan vertex
    ret = testGmcPrepareStmtByLabelName(stmt, Vertex_Name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    value = 1;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, Vertex_HashClusterName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;

    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (GMERR_OK != ret) {
            EXPECT_EQ(GMERR_OK, ret);
            ret = testGmcGetLastError();
            EXPECT_EQ(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        succNum++;
    }

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    printf("==============[5] HashCluster Scan Thread Succ Num(%d)==================\n\n", succNum);
    return NULL;
}

// Local Scan
void *Thread_001_06(void *args)
{
    int ret;
    uint32_t i;
    uint32_t value;
    uint32_t succNum = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // scan vertex
    ret = testGmcPrepareStmtByLabelName(stmt, Vertex_Name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    value = 1;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, Vertex_LocalName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;

    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (GMERR_OK != ret) {
            EXPECT_EQ(GMERR_OK, ret);
            ret = testGmcGetLastError();
            EXPECT_EQ(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        succNum++;
    }

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    printf("==============[6] Local Scan Thread Succ Num(%d)==================\n\n", succNum);
    return NULL;
}

// LPM4 Scan
void *Thread_001_07(void *args)
{
    int ret;
    uint32_t i;
    uint32_t succNum = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // scan vertex
    ret = testGmcPrepareStmtByLabelName(stmt, Vertex_Name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t vr_id = 5;
    uint32_t vrf_index = 5;
    uint32_t dest_ip_addr = trans_ip("***********");
    uint8_t mask_len = 24;

    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vr_id, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrf_index, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &dest_ip_addr, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &mask_len, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, Vertex_lPM4Name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;

    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (GMERR_OK != ret) {
            EXPECT_EQ(GMERR_OK, ret);
            ret = testGmcGetLastError();
            EXPECT_EQ(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        succNum++;
    }

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    printf("==============[7] LPM4 Scan Thread Succ Num(%d)==================\n\n", succNum);
    return NULL;
}

/*****************************************************************************
 Description  : 001.Vertex表DML和主键读、全表扫描、二级索引扫描（localhash索引，hashcluster索引，local索引，lpm）并发
 Author       : hanyang
*****************************************************************************/
TEST_F(ScanDirectRead_test, DML_086_001)
{
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 100;

    // 插入数据
    testInsertVertexLabel(g_stmt, times, initValue, Vertex_Name);

    // 7线程并发
    pthread_t Thread[7] = {0};

    pthread_create(&Thread[0], NULL, Thread_001_01, NULL);
    pthread_create(&Thread[1], NULL, Thread_001_02, NULL);
    pthread_create(&Thread[2], NULL, Thread_001_03, NULL);
    pthread_create(&Thread[3], NULL, Thread_001_04, NULL);
    pthread_create(&Thread[4], NULL, Thread_001_05, NULL);
    pthread_create(&Thread[5], NULL, Thread_001_06, NULL);
    pthread_create(&Thread[6], NULL, Thread_001_07, NULL);

    pthread_join(Thread[0], NULL);
    pthread_join(Thread[1], NULL);
    pthread_join(Thread[2], NULL);
    pthread_join(Thread[3], NULL);
    pthread_join(Thread[4], NULL);
    pthread_join(Thread[5], NULL);
    pthread_join(Thread[6], NULL);
}

// DML
void *Thread_002_01(void *args)
{
    int ret;
    uint32_t initValue = 100;
    uint32_t newValue;
    uint32_t times = 100;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    printf("==============[1] DML Thread Begin==================\n\n");
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入数据[100-199]
    testInsertKVTable(stmt, times, initValue, KVTable_Name);

    // 删除数据[50-149]
    initValue = 50;
    testDeleteKVTable(stmt, times, initValue, KVTable_Name);

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    printf("==============[1] DML Thread End==================\n\n");
    return NULL;
}

// KV Get
void *Thread_002_02(void *args)
{
    int ret;
    uint32_t initValue = 0;
    uint32_t succNum = 0;
    uint32_t times = 100;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // KV Get
    uint32_t i;
    uint32_t value;

    for (i = 0; i < times; i++) {
        ret = GmcKvPrepareStmtByLabelName(stmt, KVTable_Name);
        EXPECT_EQ(GMERR_OK, ret);
        value = initValue + i;
        char key[32];
        sprintf_s(key, 30, "zhangsan_%d", value);
        // 设置k-v值
        uint32_t output = 0;
        uint32_t outputLen = sizeof(uint32_t);
        ret = GmcKvGet(stmt, key, strlen(key), &output, &outputLen);
        // 数据可能已经被删除
        if (ret == GMERR_NO_DATA) {
            continue;
        } else {
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(value, output);
            EXPECT_EQ(sizeof(uint32_t), outputLen);
        }

        if (ret == GMERR_OK) {
            succNum++;
        }
    }

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    printf("==============[2] KV Get Thread Succ Num(%d)==================\n\n", succNum);
    return NULL;
}

// KV Scan
void *Thread_002_03(void *args)
{
    int ret;
    uint32_t succNum = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcKvPrepareStmtByLabelName(stmt, KVTable_Name);
    EXPECT_EQ(GMERR_OK, ret);

    int64_t limitCount = 0;
    ret = GmcKvScan(stmt, limitCount);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (GMERR_OK != ret) {
            EXPECT_EQ(GMERR_OK, ret);
            ret = testGmcGetLastError();
            EXPECT_EQ(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }

        succNum++;
    }

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    printf("==============[3] KV Scan Thread Succ Num(%d)==================\n\n", succNum);
    return NULL;
}

/*****************************************************************************
 Description  : 002.KV表DML和key直接读取、KV全表扫描并发。
 Author       : hanyang
*****************************************************************************/
TEST_F(ScanDirectRead_test, DML_086_002)
{
    int ret;

    uint32_t initValue = 0;
    uint32_t times = 100;

    // 插入数据
    testInsertKVTable(g_stmt, times, initValue, KVTable_Name);

    // 3线程并发
    pthread_t Thread[3] = {0};

    pthread_create(&Thread[0], NULL, Thread_002_01, NULL);
    pthread_create(&Thread[1], NULL, Thread_002_02, NULL);
    pthread_create(&Thread[2], NULL, Thread_002_03, NULL);

    pthread_join(Thread[0], NULL);
    pthread_join(Thread[1], NULL);
    pthread_join(Thread[2], NULL);
}

/*****************************************************************************
 Description  : 003.全表扫描，同一stmt连续执行2次GmcExecute，可以正常获取扫描到的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(ScanDirectRead_test, DML_086_003)
{
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 100;
    uint32_t succNum = 0;

    // 插入数据
    testInsertVertexLabel(g_stmt, times, initValue, Vertex_Name);

    // scan vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // 执行2次GmcExecute
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            EXPECT_EQ(GMERR_OK, ret);
            ret = testGmcGetLastError();
            EXPECT_EQ(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        succNum++;
    }

    EXPECT_EQ(succNum, times);
}

/*****************************************************************************
 Description  : 004.全表扫描，GmcExecute后不GmcFetch获取数据，再次执行prepare（SCAN）和GmcExecute，fetch获取数据正常。
 Author       : hanyang
*****************************************************************************/
TEST_F(ScanDirectRead_test, DML_086_004)
{
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 100;
    uint32_t succNum = 0;

    // 插入数据
    testInsertVertexLabel(g_stmt, times, initValue, Vertex_Name);

    // scan vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // GmcExecute后不GmcFetch获取数据，再次执行prepare
    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            EXPECT_EQ(GMERR_OK, ret);
            ret = testGmcGetLastError();
            EXPECT_EQ(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        succNum++;
    }

    EXPECT_EQ(succNum, times);
}

/*****************************************************************************
 Description  : 005.全表扫描，不GmcExecute，直接GmcFetch，报错。
 Author       : hanyang
*****************************************************************************/
TEST_F(ScanDirectRead_test, DML_086_005)
{
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 100;
    uint32_t succNum = 0;

    // 插入数据
    testInsertVertexLabel(g_stmt, times, initValue, Vertex_Name);

    // scan vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // 不GmcExecute，直接GmcFetch，报错
    bool isFinish = false;
    ret = GmcFetch(g_stmt, &isFinish);
    EXPECT_EQ(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, ret);
    ret = testGmcGetLastError();
    EXPECT_EQ(GMERR_OK, ret);
    AddWhiteList(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);
}

/*****************************************************************************
 Description  : 006.全表扫描过程中，GmcExecute后连接中断，重新建立连接后，DML操作和全表扫描操作都正常。
 Author       : hanyang
*****************************************************************************/
TEST_F(ScanDirectRead_test, DML_086_006)
{
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 100;
    uint32_t succNum = 0;

    // 插入数据
    testInsertVertexLabel(g_stmt, times, initValue, Vertex_Name);

    // scan vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 中断连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    // 重建连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // scan vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            EXPECT_EQ(GMERR_OK, ret);
            ret = testGmcGetLastError();
            EXPECT_EQ(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        succNum++;
    }

    EXPECT_EQ(succNum, times);

    // 插入数据
    initValue = 100;
    testInsertVertexLabel(g_stmt, times, initValue, Vertex_Name);
}

/*****************************************************************************
 Description  : 007.全表扫描过程中，GmcFetch后连接中断，重新建立连接后，DML操作和全表扫描操作都正常。
 Author       : hanyang
*****************************************************************************/
TEST_F(ScanDirectRead_test, DML_086_007)
{
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 100;
    uint32_t succNum = 0;

    // 插入数据
    testInsertVertexLabel(g_stmt, times, initValue, Vertex_Name);

    // scan vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    bool isFinish = false;
    ret = GmcFetch(g_stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);

    // 中断连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    // 重建连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // scan vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(g_stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    isFinish = false;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            EXPECT_EQ(GMERR_OK, ret);
            ret = testGmcGetLastError();
            EXPECT_EQ(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        succNum++;
    }

    EXPECT_EQ(succNum, times);

    // 插入数据
    initValue = 100;
    testInsertVertexLabel(g_stmt, times, initValue, Vertex_Name);
}

/*****************************************************************************
 Description  : 008.localhash扫描过程中，GmcExecute后连接中断，重新建立连接后，DML操作和localhash扫描操作都正常。
 Author       : hanyang
*****************************************************************************/
TEST_F(ScanDirectRead_test, DML_086_008)
{
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 100;
    uint32_t value;
    uint32_t succNum = 0;

    // 插入数据
    testInsertVertexLabel(g_stmt, times, initValue, Vertex_Name);

    // scan vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    value = 1;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(g_stmt, Vertex_LocalHashName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 中断连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    // 重建连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // scan vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    value = 1;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(g_stmt, Vertex_LocalHashName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            EXPECT_EQ(GMERR_OK, ret);
            ret = testGmcGetLastError();
            EXPECT_EQ(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        succNum++;
    }

    EXPECT_EQ(succNum, (times / 10));

    // 插入数据
    initValue = 100;
    testInsertVertexLabel(g_stmt, times, initValue, Vertex_Name);
}

/*****************************************************************************
 Description  : 009.localhash扫描过程中，GmcFetch后连接中断，重新建立连接后，DML操作和localhash扫描操作都正常。
 Author       : hanyang
*****************************************************************************/
TEST_F(ScanDirectRead_test, DML_086_009)
{
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 100;
    uint32_t value;
    uint32_t succNum = 0;

    // 插入数据
    testInsertVertexLabel(g_stmt, times, initValue, Vertex_Name);

    // scan vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    value = 1;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(g_stmt, Vertex_LocalHashName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    bool isFinish = false;
    ret = GmcFetch(g_stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);

    // 中断连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    // 重建连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // scan vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    value = 1;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(g_stmt, Vertex_LocalHashName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    isFinish = false;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            EXPECT_EQ(GMERR_OK, ret);
            ret = testGmcGetLastError();
            EXPECT_EQ(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        succNum++;
    }

    EXPECT_EQ(succNum, (times / 10));

    // 插入数据
    initValue = 100;
    testInsertVertexLabel(g_stmt, times, initValue, Vertex_Name);
}

/*****************************************************************************
 Description  : 010.hashcluster扫描过程中，GmcExecute后连接中断，重新建立连接后，DML操作和hashcluster扫描操作都正常。
 Author       : hanyang
*****************************************************************************/
TEST_F(ScanDirectRead_test, DML_086_010)
{
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 100;
    uint32_t value;
    uint32_t succNum = 0;

    // 插入数据
    testInsertVertexLabel(g_stmt, times, initValue, Vertex_Name);

    // scan vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    value = 1;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(g_stmt, Vertex_HashClusterName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 中断连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    // 重建连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // scan vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    value = 1;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(g_stmt, Vertex_HashClusterName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            EXPECT_EQ(GMERR_OK, ret);
            ret = testGmcGetLastError();
            EXPECT_EQ(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        succNum++;
    }

    EXPECT_EQ(succNum, (times / 10));

    // 插入数据
    initValue = 100;
    testInsertVertexLabel(g_stmt, times, initValue, Vertex_Name);
}

/*****************************************************************************
 Description  : 011.hashcluster扫描过程中，GmcFetch后连接中断，重新建立连接后，DML操作和hashcluster扫描操作都正常。
 Author       : hanyang
*****************************************************************************/
TEST_F(ScanDirectRead_test, DML_086_011)
{
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 100;
    uint32_t value;
    uint32_t succNum = 0;

    // 插入数据
    testInsertVertexLabel(g_stmt, times, initValue, Vertex_Name);

    // scan vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    value = 1;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(g_stmt, Vertex_HashClusterName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    bool isFinish = false;
    ret = GmcFetch(g_stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);

    // 中断连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    // 重建连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // scan vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    value = 1;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(g_stmt, Vertex_HashClusterName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    isFinish = false;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            EXPECT_EQ(GMERR_OK, ret);
            ret = testGmcGetLastError();
            EXPECT_EQ(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        succNum++;
    }

    EXPECT_EQ(succNum, (times / 10));

    // 插入数据
    initValue = 100;
    testInsertVertexLabel(g_stmt, times, initValue, Vertex_Name);
}

/*****************************************************************************
 Description  : 012.KV key get读取过程中，连接中断，重新建立连接后，DML操作和KV key get读取操作都正常。
 Author       : hanyang
*****************************************************************************/
TEST_F(ScanDirectRead_test, DML_086_012)
{
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 100;
    uint32_t succNum = 0;

    // 插入数据
    testInsertKVTable(g_stmt, times, initValue, KVTable_Name);

    // KV Get
    uint32_t i;
    uint32_t value;

    for (i = 0; i < times; i++) {
        if (i == (times / 2)) {
            // 中断连接
            ret = testGmcDisconnect(g_conn, g_stmt);
            EXPECT_EQ(GMERR_OK, ret);
            g_conn = NULL;
            g_stmt = NULL;
            break;
        }
        ret = GmcKvPrepareStmtByLabelName(g_stmt, KVTable_Name);
        EXPECT_EQ(GMERR_OK, ret);
        value = initValue + i;
        char key[32];
        sprintf_s(key, 30, "zhangsan_%d", value);
        // 设置k-v值
        uint32_t output = 0;
        uint32_t outputLen = sizeof(uint32_t);
        ret = GmcKvGet(g_stmt, key, strlen(key), &output, &outputLen);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(value, output);
        EXPECT_EQ(sizeof(uint32_t), outputLen);

        if (ret == GMERR_OK) {
            succNum++;
        }
    }

    // 重建连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    succNum = 0;
    for (i = 0; i < times; i++) {
        ret = GmcKvPrepareStmtByLabelName(g_stmt, KVTable_Name);
        EXPECT_EQ(GMERR_OK, ret);
        value = initValue + i;
        char key[32];
        sprintf_s(key, 30, "zhangsan_%d", value);
        // 设置k-v值
        uint32_t output = 0;
        uint32_t outputLen = sizeof(uint32_t);
        ret = GmcKvGet(g_stmt, key, strlen(key), &output, &outputLen);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(value, output);
        EXPECT_EQ(sizeof(uint32_t), outputLen);

        if (ret == GMERR_OK) {
            succNum++;
        }
    }

    EXPECT_EQ(succNum, times);

    // 插入数据
    initValue = 100;
    testInsertKVTable(g_stmt, times, initValue, KVTable_Name);
}

/*****************************************************************************
 Description  : 013.KV全表扫描，同一stmt连续执行2次GmcKvScan，可以正常获取扫描到的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(ScanDirectRead_test, DML_086_013)
{
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 100;
    uint32_t succNum = 0;

    // 插入数据
    testInsertKVTable(g_stmt, times, initValue, KVTable_Name);

    ret = GmcKvPrepareStmtByLabelName(g_stmt, KVTable_Name);
    EXPECT_EQ(GMERR_OK, ret);

    int64_t limitCount = 0;
    // 连续执行2次GmcKvScan
    ret = GmcKvScan(g_stmt, limitCount);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvScan(g_stmt, limitCount);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (GMERR_OK != ret) {
            EXPECT_EQ(GMERR_OK, ret);
            ret = testGmcGetLastError();
            EXPECT_EQ(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }

        succNum++;
    }

    EXPECT_EQ(succNum, times);
}

/*****************************************************************************
 Description  : 014.V全表扫描，GmcKvScan后不GmcFetch获取数据，再次执行prepare（SCAN）和GmcKvScan，fetch获取数据正常。
 Author       : hanyang
*****************************************************************************/
TEST_F(ScanDirectRead_test, DML_086_014)
{
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 100;
    uint32_t succNum = 0;

    // 插入数据
    testInsertKVTable(g_stmt, times, initValue, KVTable_Name);

    ret = GmcKvPrepareStmtByLabelName(g_stmt, KVTable_Name);
    EXPECT_EQ(GMERR_OK, ret);

    int64_t limitCount = 0;
    ret = GmcKvScan(g_stmt, limitCount);
    EXPECT_EQ(GMERR_OK, ret);

    // 再次执行prepare（SCAN）和GmcKvScan
    ret = GmcKvPrepareStmtByLabelName(g_stmt, KVTable_Name);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcKvScan(g_stmt, limitCount);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            EXPECT_EQ(GMERR_OK, ret);
            ret = testGmcGetLastError();
            EXPECT_EQ(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }

        succNum++;
        char *fetchKey = NULL;
        uint32_t fetchKeyLen = 0;
        char *fetchValue = NULL;
        uint32_t fetchValueLen = 0;
        ret = GmcKvGetFromStmt(g_stmt, (void **)&fetchKey, &fetchKeyLen, (void **)&fetchValue, &fetchValueLen);
        EXPECT_EQ(GMERR_OK, ret);
    }

    EXPECT_EQ(succNum, times);
}

/*****************************************************************************
 Description  : 015.KV全表扫描，GmcKvScan后不GmcFetch，get数据报错。
 Author       : hanyang
*****************************************************************************/
TEST_F(ScanDirectRead_test, DML_086_015)
{
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 100;
    uint32_t succNum = 0;

    // 插入数据
    testInsertKVTable(g_stmt, times, initValue, KVTable_Name);

    ret = GmcKvPrepareStmtByLabelName(g_stmt, KVTable_Name);
    EXPECT_EQ(GMERR_OK, ret);

    int64_t limitCount = 0;
    ret = GmcKvScan(g_stmt, limitCount);
    EXPECT_EQ(GMERR_OK, ret);

    char *fetchKey = NULL;
    uint32_t fetchKeyLen = 0;
    char *fetchValue = NULL;
    uint32_t fetchValueLen = 0;
    ret = GmcKvGetFromStmt(g_stmt, (void **)&fetchKey, &fetchKeyLen, (void **)&fetchValue, &fetchValueLen);
    EXPECT_EQ(GMERR_NO_DATA, ret);
    ret = testGmcGetLastError();
    EXPECT_EQ(GMERR_OK, ret);
    AddWhiteList(GMERR_NO_DATA);
}

/*****************************************************************************
 Description  : 016.KV全表扫描过程中，GmcKvScan后连接中断，重新建立连接后，DML操作和KV全表扫描操作都正常。
 Author       : hanyang
*****************************************************************************/
TEST_F(ScanDirectRead_test, DML_086_016)
{
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 100;
    uint32_t succNum = 0;

    // 插入数据
    testInsertKVTable(g_stmt, times, initValue, KVTable_Name);

    ret = GmcKvPrepareStmtByLabelName(g_stmt, KVTable_Name);
    EXPECT_EQ(GMERR_OK, ret);

    int64_t limitCount = 0;
    ret = GmcKvScan(g_stmt, limitCount);
    EXPECT_EQ(GMERR_OK, ret);

    // 中断连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    // 重建连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcKvPrepareStmtByLabelName(g_stmt, KVTable_Name);
    EXPECT_EQ(GMERR_OK, ret);

    limitCount = 0;
    ret = GmcKvScan(g_stmt, limitCount);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (GMERR_OK != ret) {
            EXPECT_EQ(GMERR_OK, ret);
            ret = testGmcGetLastError();
            EXPECT_EQ(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }

        succNum++;
    }

    EXPECT_EQ(succNum, times);

    // 插入数据
    initValue = 100;
    testInsertKVTable(g_stmt, times, initValue, KVTable_Name);
}

/*****************************************************************************
 Description  : 017.KV全表扫描过程中，GmcFetch后连接中断，重新建立连接后，DML操作和KV全表扫描操作都正常。
 Author       : hanyang
*****************************************************************************/
TEST_F(ScanDirectRead_test, DML_086_017)
{
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 100;
    uint32_t succNum = 0;

    // 插入数据
    testInsertKVTable(g_stmt, times, initValue, KVTable_Name);

    ret = GmcKvPrepareStmtByLabelName(g_stmt, KVTable_Name);
    EXPECT_EQ(GMERR_OK, ret);

    int64_t limitCount = 0;
    ret = GmcKvScan(g_stmt, limitCount);
    EXPECT_EQ(GMERR_OK, ret);

    bool isFinish = false;
    ret = GmcFetch(g_stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);

    // 中断连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    // 重建连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcKvPrepareStmtByLabelName(g_stmt, KVTable_Name);
    EXPECT_EQ(GMERR_OK, ret);

    limitCount = 0;
    ret = GmcKvScan(g_stmt, limitCount);
    EXPECT_EQ(GMERR_OK, ret);
    isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (GMERR_OK != ret) {
            EXPECT_EQ(GMERR_OK, ret);
            ret = testGmcGetLastError();
            EXPECT_EQ(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }

        succNum++;
    }

    EXPECT_EQ(succNum, times);

    // 插入数据
    initValue = 100;
    testInsertKVTable(g_stmt, times, initValue, KVTable_Name);
}

/*****************************************************************************
 Description  : 023.两个连接，1个连接启动事务，写入数据，未提交事务，另一个连接进行全表扫描，不能读到未提交事务的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(ScanDirectRead_test, DML_086_023)
{
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 100;

    // 删除表
    testDropLabel(g_stmt, Vertex_Name);

    // 创建显示事务专用表
    char *VLabel_schema = NULL;
    readJanssonFile("schema_file/Vertex_01.gmjson", &VLabel_schema);
    ASSERT_NE((void *)NULL, VLabel_schema);
    ret = GmcCreateVertexLabel(g_stmt, VLabel_schema, MS_config_trans);
    EXPECT_EQ(GMERR_OK, ret);
    free(VLabel_schema);

    // 连接1，插入数据
    // 插入数据0-99
    testInsertVertexLabel(g_stmt, times, initValue, Vertex_Name);

    // 定义MS的事务config类型
    GmcTxConfigT MSTrxConfig;
    MSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    MSTrxConfig.type = GMC_TX_ISOLATION_COMMITTED;
    MSTrxConfig.readOnly = false;
    MSTrxConfig.trxType = GMC_PESSIMISITIC_TRX;

    // 启动MS事务
    ret = GmcTransStart(g_conn, &MSTrxConfig);
    EXPECT_EQ(GMERR_OK, ret);

    // 连接1，插入数据
    // 插入数据，100-199
    initValue = 100;
    times = 100;
    testInsertVertexLabel(g_stmt, times, initValue, Vertex_Name);

    // 连接2
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // 连接2，全表扫描，只能扫描到0-99
    uint32_t succNum = 0;
    uint32_t expectNum = 100;

    // scan vertex
    ret = testGmcPrepareStmtByLabelName(stmt, Vertex_Name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;

    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (GMERR_OK != ret) {
            EXPECT_EQ(GMERR_OK, ret);
            ret = testGmcGetLastError();
            EXPECT_EQ(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        succNum++;
    }

    EXPECT_EQ(expectNum, succNum);
    printf("=================Before Trans Commit, Scan number(%d)================\n\n", succNum);

    // MS事务commit
    ret = GmcTransCommit(g_conn);
    EXPECT_EQ(GMERR_OK, ret);

    // 事务提交后，全表扫描，可以扫到0-199
    succNum = 0;
    expectNum = 200;

    // scan vertex
    ret = testGmcPrepareStmtByLabelName(stmt, Vertex_Name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    isFinish = false;

    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (GMERR_OK != ret) {
            EXPECT_EQ(GMERR_OK, ret);
            ret = testGmcGetLastError();
            EXPECT_EQ(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        succNum++;
    }

    EXPECT_EQ(expectNum, succNum);
    printf("=================After Trans Commit, Scan number(%d)================\n\n", succNum);

    // 释放连接2
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    conn = NULL;
    stmt = NULL;
}

/*****************************************************************************
 Description  : 024.两个连接，1个连接启动事务，删除数据，未提交事务，另一个连接进行全表扫描，可以读到未提交事务的数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(ScanDirectRead_test, DML_086_024)
{
    int ret;
    uint32_t initValue = 0;
    uint32_t times = 200;

    // 删除表
    testDropLabel(g_stmt, Vertex_Name);

    // 创建显示事务专用表
    char *VLabel_schema = NULL;
    readJanssonFile("schema_file/Vertex_01.gmjson", &VLabel_schema);
    ASSERT_NE((void *)NULL, VLabel_schema);
    ret = GmcCreateVertexLabel(g_stmt, VLabel_schema, MS_config_trans);
    EXPECT_EQ(GMERR_OK, ret);
    free(VLabel_schema);

    // 连接1，插入数据
    // 插入数据0-199
    testInsertVertexLabel(g_stmt, times, initValue, Vertex_Name);

    // 定义MS的事务config类型
    GmcTxConfigT MSTrxConfig;
    MSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    MSTrxConfig.type = GMC_TX_ISOLATION_COMMITTED;
    MSTrxConfig.readOnly = false;
    MSTrxConfig.trxType = GMC_PESSIMISITIC_TRX;

    // 启动MS事务
    ret = GmcTransStart(g_conn, &MSTrxConfig);
    EXPECT_EQ(GMERR_OK, ret);

    // 连接1，删除数据
    // 删除数据[100-199]
    initValue = 100;
    times = 100;
    testDeleteVertexLabel(g_stmt, Vertex_KeyName, times, initValue, Vertex_Name);

    // 连接2
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // 连接2，全表扫描，能扫描到0-199
    uint32_t succNum = 0;
    uint32_t expectNum = 200;

    // scan vertex
    ret = testGmcPrepareStmtByLabelName(stmt, Vertex_Name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;

    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (GMERR_OK != ret) {
            EXPECT_EQ(GMERR_OK, ret);
            ret = testGmcGetLastError();
            EXPECT_EQ(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        succNum++;
    }

    EXPECT_EQ(expectNum, succNum);
    printf("=================Before Trans Commit, Scan number(%d)================\n\n", succNum);

    // MS事务commit
    ret = GmcTransCommit(g_conn);
    EXPECT_EQ(GMERR_OK, ret);

    // 事务提交后，全表扫描，可以扫到0-99
    succNum = 0;
    expectNum = 100;

    // scan vertex
    ret = testGmcPrepareStmtByLabelName(stmt, Vertex_Name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    isFinish = false;

    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (GMERR_OK != ret) {
            EXPECT_EQ(GMERR_OK, ret);
            ret = testGmcGetLastError();
            EXPECT_EQ(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        succNum++;
    }

    EXPECT_EQ(expectNum, succNum);
    printf("=================After Trans Commit, Scan number(%d)================\n\n", succNum);

    // 释放连接2
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    conn = NULL;
    stmt = NULL;
}
