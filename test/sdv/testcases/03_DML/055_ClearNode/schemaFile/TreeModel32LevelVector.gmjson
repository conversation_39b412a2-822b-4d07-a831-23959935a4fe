[{"type": "record", "name": "OP_T0", "fields": [{"name": "F0", "type": "char", "nullable": false}, {"name": "T1", "type": "record", "fields": [{"name": "F1", "type": "int64", "nullable": true}, {"name": "T2", "type": "record", "fields": [{"name": "F2", "type": "int64", "nullable": true}, {"name": "T3", "type": "record", "fields": [{"name": "F3", "type": "int64", "nullable": true}, {"name": "T4", "type": "record", "fields": [{"name": "F4", "type": "int64", "nullable": true}, {"name": "T5", "type": "record", "fields": [{"name": "F5", "type": "int64", "nullable": true}, {"name": "T6", "type": "record", "fields": [{"name": "F7", "type": "int64", "nullable": true}, {"name": "T7", "type": "record", "fields": [{"name": "T8", "type": "record", "fields": [{"name": "T9", "type": "record", "fields": [{"name": "T10", "type": "record", "fields": [{"name": "T11", "type": "record", "fields": [{"name": "F5", "type": "int64", "nullable": true}, {"name": "T12", "type": "record", "fields": [{"name": "T13", "type": "record", "fields": [{"name": "T14", "type": "record", "fields": [{"name": "T15", "type": "record", "fields": [{"name": "T16", "type": "record", "fields": [{"name": "T17", "type": "record", "fields": [{"name": "T18", "type": "record", "fields": [{"name": "T19", "type": "record", "fields": [{"name": "T20", "type": "record", "fields": [{"name": "T21", "type": "record", "fields": [{"name": "T22", "type": "record", "fields": [{"name": "T23", "type": "record", "fields": [{"name": "T24", "type": "record", "fields": [{"name": "T25", "type": "record", "fields": [{"name": "T26", "type": "record", "fields": [{"name": "T27", "type": "record", "fields": [{"name": "T28", "type": "record", "fields": [{"name": "T29", "type": "record", "fields": [{"name": "T30", "type": "record", "fields": [{"name": "T31", "type": "record", "vector": true, "size": 1, "fields": [{"name": "P0", "type": "char", "nullable": false}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}], "keys": [{"node": "OP_T0", "name": "OP_PK", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]