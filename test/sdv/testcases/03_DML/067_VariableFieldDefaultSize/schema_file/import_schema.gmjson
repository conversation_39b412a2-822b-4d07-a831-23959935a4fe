[{"version": "2.0", "type": "record", "name": "import_pri", "comment": "主要验证primarykey和localhashkey和变长交互", "fields": [{"name": "Default_StrSize", "type": "string"}, {"name": "Default_StrSize2", "type": "string"}, {"name": "Default_BytSize", "type": "bytes"}, {"name": "Default_BytSize2", "type": "bytes"}, {"name": "Eq_Str8K", "type": "string", "size": 8192}, {"name": "Eq_Byt8K", "type": "bytes", "size": 8192}, {"name": "Eq_Str8K2", "type": "string", "size": 8192}, {"name": "Eq_Byt8K2", "type": "bytes", "size": 8192}, {"name": "Eq_Str256B", "type": "string", "size": 256}, {"name": "Eq_Byt256B", "type": "bytes", "size": 256}, {"name": "Eq_Str256B2", "type": "string", "size": 256}, {"name": "Eq_Byt256B2", "type": "bytes", "size": 256}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "import_pri", "fields": ["Default_StrSize"], "constraints": {"unique": true}, "comment": "主键索引"}, {"name": "localhash_keys", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "import_pri", "fields": ["Default_StrSize2"], "constraints": {"unique": false}, "comment": "localhash索引"}, {"name": "localhash_keyb", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "import_pri", "fields": ["Default_BytSize2"], "constraints": {"unique": false}, "comment": "localhash索引"}, {"name": "localhash_keys8k", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "import_pri", "fields": ["Eq_Str8K"], "constraints": {"unique": false}, "comment": "localhash索引"}, {"name": "localhash_keyb8k", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "import_pri", "fields": ["Eq_Byt8K"], "constraints": {"unique": false}, "comment": "localhash索引"}]}]