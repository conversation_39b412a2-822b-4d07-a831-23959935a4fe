[{"version": "2.0", "type": "record", "name": "Vertex_lpm6", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": false}, {"name": "F2", "type": "uint32", "nullable": false}, {"name": "F3", "type": "uint32", "nullable": false}, {"name": "F4", "type": "uint32", "nullable": false}, {"name": "F5", "type": "uint32", "nullable": false}, {"name": "F6", "type": "uint32", "nullable": false}, {"name": "F7", "type": "string", "size": 7, "nullable": true}, {"name": "vr_id", "type": "uint32", "comment": "Vs索引", "nullable": false}, {"name": "vrf_index", "type": "uint32", "comment": "VpnInstace索引", "nullable": false}, {"name": "dest_ip_addr", "type": "fixed", "size": 16, "comment": "目的地址", "nullable": false}, {"name": "mask_len", "type": "uint8", "comment": "掩码长度", "nullable": false}], "keys": [{"name": "Vertex_pk", "index": {"type": "primary"}, "fields": ["F0"], "constraints": {"unique": true}}, {"name": "Vertex_local_u", "index": {"type": "local"}, "fields": ["F1"], "constraints": {"unique": true}}, {"name": "Vertex_local", "index": {"type": "local"}, "fields": ["F2"], "constraints": {"unique": false}}, {"name": "Vertex_localhash_u", "index": {"type": "<PERSON><PERSON><PERSON>"}, "fields": ["F3"], "constraints": {"unique": true}}, {"name": "Vertex_localhash", "index": {"type": "<PERSON><PERSON><PERSON>"}, "fields": ["F4"], "constraints": {"unique": false}}, {"name": "Vertex_hashcluster_u", "index": {"type": "hashcluster"}, "fields": ["F5"], "constraints": {"unique": true}}, {"name": "Vertex_hashcluster", "index": {"type": "hashcluster"}, "fields": ["F6"], "constraints": {"unique": false}}, {"name": "Vertex_lpm6_key", "index": {"type": "lpm6_tree_bitmap"}, "fields": ["vr_id", "vrf_index", "dest_ip_addr", "mask_len"], "constraints": {"unique": true}}]}]