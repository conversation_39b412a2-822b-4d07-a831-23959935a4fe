[{"name": "T80", "version": "2.0", "type": "record", "fields": [{"name": "F2", "type": "int16", "nullable": false}, {"name": "F3", "type": "uint16", "nullable": false}, {"name": "F4", "type": "int32", "nullable": false}, {"name": "F5", "type": "uint32", "nullable": false}, {"name": "F6", "type": "int64", "nullable": false}, {"name": "F7", "type": "uint64", "nullable": false}, {"name": "F10", "type": "string", "size": 100, "nullable": false}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint64", "nullable": true}, {"name": "F0", "type": "int8", "nullable": true}, {"name": "F1", "type": "uint8", "nullable": true}, {"name": "F11", "type": "bytes", "size": 12, "nullable": true}], "keys": [{"node": "T80", "name": "T80_PK", "fields": ["F2", "F3", "F4", "F5", "F6", "F7", "F10"], "index": {"type": "primary"}}]}]