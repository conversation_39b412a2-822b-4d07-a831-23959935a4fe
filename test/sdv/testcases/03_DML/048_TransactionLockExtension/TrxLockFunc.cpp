/*****************************************************************************
 Description  : 事务锁动态扩展
 Notes        :
DML_048_001 1个事务内，1个表，写数据不触发锁升级，查看视图，提交事务后，再次查看视图
DML_048_002 1个事务内，1个表，写数据触发锁升级的边界场景，查看视图，提交事务后，再次查看视图
DML_048_003 1个事务内，2个表，表1写少量数据，表2先触发锁升级，继续写表1，触发表1锁升级
DML_048_004
创建50个表，1个事务内，第一个表写入5k条记录，第二个表少写一条记录，第三个表再少写一条记录，以此类推，每个表都升级
DML_048_005 创建225个表，随机顺序往每个表写入1条记录，循环1000次，锁资源预期足够
DML_048_006 创建5109个表，1个事务内，每个表写入10条数据，中间必然触发锁资源不足，回滚
DML_048_007 同一个表，2事务同时编辑不重叠的数据，其中一个写入较多数据，触发表锁升级，升级成功场景
DML_048_008 同一个表，2事务同时编辑不重叠的数据，其中一个写入较多数据，触发表锁升级，升级超时场景
DML_048_009 同一个表，2事务同时编辑不重叠的数据，其中一个写入较多数据，分别写入较多数据，分别触发表锁升级，升级冲突场景
DML_048_010 同一个表，事务1开启扫描，事务2写入5k条数据，触发升级，由于事务1一直不提交，事务2升级超时，事务失败回滚
DML_048_011 同一个表，事务1开启扫描，事务2写入5k条数据，触发升级，事务1等待3s后再提交，事务2升级成功
DML_048_012 2个事务内，各112个表，各写50条数据，预期都能锁升级
DML_048_013
1024个事务内，共1个表，各写100条数据，不提交，预期后面从某个事务开始，一个会升级超时失败，其余均由于升级冲突失败
DML_048_014 1024个事务内，各1个表，各写100条数据，预期后面从某个事务开始，均由于锁资源不足失败
DML_048_015 1:1建边场景：1个事务内，2个表，各写100条数据，自动建边，查看视图，提交事务后，再次查看视图
DML_048_016 2个表，各写1条数据，开启事务，手动建10000条边，读边，查看视图，提交事务后，再次查看视图
DML_048_017 1个事务内，1个表，批量写入大数据量
DML_048_018 gmimport导入数据，使用隐式事务
DML_048_019 连接并发，每个线程开启事务，进行DML操作，操作不同表，部分线程同表，同数据
DML_048_020 事务1插入数据，升级，不提交；事务2插入多条数据，不升级，不提交，直接断链接，验证异常退出资源释放情况
DML_048_021 循环查看视图
DML_048_022 视图参数异常测试
 History      :
 Author       : 吴雪琦 00495442
 Modification :
 Date         : 2021/5/1
*****************************************************************************/

extern "C" {
}
#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "tools.h"

TestAlarmDataT g_before = {0};
TestAlarmDataT g_after = {0};

class TrxLockExtension_func : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    SnUserDataT *user_data;
    int *newValue;
    int *oldValue;

    virtual void SetUp();
    virtual void TearDown();
};

void TrxLockExtension_func::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/start.sh");
    int ret = 0;
    ret = testEnvInit();
    ASSERT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
}

void TrxLockExtension_func::TearDownTestCase()
{
    int ret;
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    testEnvClean();
}

void TrxLockExtension_func::SetUp()
{
    int ret = 0;

    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_sync, &g_stmt_sync2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_sync, &g_stmt_sync3);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_sync, &g_stmt_sync4);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_sync, &g_stmt_sync5);
    EXPECT_EQ(GMERR_OK, ret);
    GmcDropGraphLabel(g_stmt_sync, g_labelName1);
    GmcDropGraphLabel(g_stmt_sync, g_labelName2);
    GmcDropGraphLabel(g_stmt_sync, g_labelName3);
    GmcDropGraphLabel(g_stmt_sync, g_labelName5);
    readJanssonFile("schema_file/TreeModel_006.gmjson", &g_schema1);
    EXPECT_NE((void *)NULL, g_schema1);

    ret = GmcCreateVertexLabel(g_stmt_sync, g_schema1, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schema_file/TreeModel_002.gmjson", &g_schema2);
    EXPECT_NE((void *)NULL, g_schema2);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schema2, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schema_file/TreeModel_003.gmjson", &g_schema3);
    EXPECT_NE((void *)NULL, g_schema3);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schema3, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schema_file/one_field_schema.gmjson", &g_schema5);
    EXPECT_NE((void *)NULL, g_schema5);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schema5, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    GmcUnbindResPoolFromLabel(g_stmt_sync4, g_labelName4);
    (void)GmcDestroyResPool(g_stmt_sync4, resPoolTestName);
    ret = GmcCreateResPool(g_stmt_sync4, gResPoolTest);
    ASSERT_EQ(GMERR_OK, ret);
    readJanssonFile("schema_file/TreeModel_004.gmjson", &g_schema4);
    EXPECT_NE((void *)NULL, g_schema4);
    ret = GmcCreateVertexLabel(g_stmt_sync4, g_schema4, g_label_config);
    if (ret != GMERR_OK && ret != GMERR_DUPLICATE_TABLE) {
        printf("GmcCreateVertexLabel faied, ret : %d\n", ret);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(true, ret == GMERR_OK || ret == GMERR_DUPLICATE_TABLE);
    ret = GmcBindResPoolToLabel(g_stmt_sync4, resPoolTestName, g_labelName4);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建edge_1
    readJanssonFile("schema_file/edge_1.gmjson", &g_edgeLabelJson1);
    EXPECT_NE((void *)NULL, g_edgeLabelJson1);
    ret = GmcCreateEdgeLabel(g_stmt_sync, g_edgeLabelJson1, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName1, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync2, g_labelName2, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync3, g_labelName3, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync4, g_labelName4, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync5, g_labelName5, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    ret = TestGmcGetAlarmData(&g_before);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN();
}

void TrxLockExtension_func::TearDown()
{
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0}, errorMsg2[errCodeLen] = {0}, errorMsg3[errCodeLen] = {0},
         errorMsg4[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_NO_DATA);
    (void)snprintf(errorMsg3, errCodeLen, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    (void)snprintf(errorMsg4, errCodeLen, "GMERR-%d", GMERR_TRANSACTION_ROLLBACK);
    AW_ADD_ERR_WHITE_LIST(4, errorMsg1, errorMsg2, errorMsg3, errorMsg4);
    AW_CHECK_LOG_END();

    int32_t ret = 0;
    ret = GmcDropGraphLabel(g_stmt_sync, g_labelName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropGraphLabel(g_stmt_sync, g_labelName3);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropGraphLabel(g_stmt_sync, g_labelName5);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnbindResPoolFromLabel(g_stmt_sync4, g_labelName4);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDestroyResPool(g_stmt_sync4, resPoolTestName);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync4, g_labelName4);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    GmcFreeStmt(g_stmt_sync2);
    GmcFreeStmt(g_stmt_sync3);
    GmcFreeStmt(g_stmt_sync4);
    GmcFreeStmt(g_stmt_sync5);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);
    ret = TestGmcGetAlarmData(&g_after);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(0, g_after.subConnRingFailTimes - g_before.subConnRingFailTimes);
    free(g_schema1);
    free(g_schema2);
    free(g_schema3);
    free(g_schema4);
    free(g_schema5);
    free(g_sub_info);
    free(g_edgeLabelJson1);
}

bool isMatchPushCond(int value)
{
    return (value == 0) || (value == 100);
}

// 1个事务内，1个表，写数据不触发锁升级，查看视图，提交事务后，再次查看视图
TEST_F(TrxLockExtension_func, DML_048_001)
{
    int32_t ret = 0, i, userDataIdx = 0;
    // char string1[] = "qwertyuiopasdfghjklzxcvbnm123456789!@#$%^&*()_+-=`~{}[]|:\';,<    .>/? ";
    char string1[] = "string1";
    char string2[] = "1234567";
    tbl_cnt = 1;
    g_end_num = ROW_NOT_UPGRADE_MAX_CNT;
    char lock_cnt[50] = "", used_lock_cnt[50] = "", free_lock_cnt[50] = "", bucket_lock_cnt[50] = "",
         pool_lock_cnt[50] = "", reserve_lock_cnt[50] = "";
    snprintf(lock_cnt, sizeof(lock_cnt), "LOCK_CNT: %d", LOCK_CNT);
    snprintf(used_lock_cnt, sizeof(used_lock_cnt), "USED_LOCK_CNT: %d", ROW_NOT_UPGRADE_MAX_CNT + 3);
    snprintf(free_lock_cnt, sizeof(free_lock_cnt), "FREE_LOCK_CNT: %d", LOCK_CNT - (ROW_NOT_UPGRADE_MAX_CNT)-3);
    snprintf(bucket_lock_cnt, sizeof(bucket_lock_cnt), "BUCKET_LOCK_CNT: %d", BUCKET_LOCK_CNT);
    snprintf(pool_lock_cnt, sizeof(pool_lock_cnt), "POOL_LOCK_CNT: %d", POOL_LOCK_CNT);
    snprintf(reserve_lock_cnt, sizeof(reserve_lock_cnt), "RESERVE_LOCK_CNT: %d", POOL_LOCK_RESERVED_LOCK_CNT);

    char const *view_name = "V\\$STORAGE_LOCK_OVERVIEW";
    memset(g_command, 0, sizeof(g_command));
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer,

        view_name);
    ret = executeCommand(g_command, lock_cnt, "USED_LOCK_CNT: 0");
    EXPECT_EQ(GMERR_OK, ret);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    ret = GmcTransStart(g_conn_sync, &config);
    EXPECT_EQ(GMERR_OK, ret);

    // 同步插入数据
    for (i = g_start_num; i < g_end_num; i++) {
        // printf("[INSERT] i = %d\n", i);
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName1, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcInsertVertex(g_stmt_sync, i, 0, string1, g_array_num, g_vector_num);
    }

    // char const *view_name = "V\\$STORAGE_LOCK_OVERVIEW";
    memset(g_command, 0, sizeof(g_command));
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer,

        view_name);
    system(g_command);
    ret = executeCommand(g_command, lock_cnt, used_lock_cnt, free_lock_cnt, bucket_lock_cnt, reserve_lock_cnt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcTransCommit(g_conn_sync);
    EXPECT_EQ(GMERR_OK, ret);

    // 查看视图
    snprintf(lock_cnt, sizeof(lock_cnt), "LOCK_CNT: %d", LOCK_CNT);
    snprintf(used_lock_cnt, sizeof(used_lock_cnt), "USED_LOCK_CNT: %d", 0);

    memset(g_command, 0, sizeof(g_command));
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer,

        view_name);
    system(g_command);
    ret = executeCommand(g_command, lock_cnt, used_lock_cnt);
    EXPECT_EQ(GMERR_OK, ret);

    // 读取数据
    TestGmcDirectFetchVertex(g_stmt_sync, 0, 0, string1, g_start_num, g_end_num, g_array_num, g_vector_num,
        g_labelName1, g_lableName_PK1, true);

    printf("tbl_cnt : %d, ROW_NOT_UPGRADE_MAX_CNT : %d, ROW_MUST_UPGRADE_CNT : %d\n", tbl_cnt, ROW_NOT_UPGRADE_MAX_CNT,
        ROW_MUST_UPGRADE_CNT);
}

// 1个事务内，1个表，写数据触发锁升级的边界场景，查看视图，提交事务后，再次查看视图
TEST_F(TrxLockExtension_func, DML_048_002)
{
    int32_t ret = 0, i;
    char string1[] = "string1";
    char string2[] = "1234567";
    tbl_cnt = 1;
    g_end_num = ROW_MUST_UPGRADE_CNT;
    char lock_cnt[50] = "", used_lock_cnt[50] = "", free_lock_cnt[50] = "", bucket_lock_cnt[50] = "",
         pool_lock_cnt[50] = "", reserve_lock_cnt[50] = "";

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    ret = GmcTransStart(g_conn_sync, &config);
    EXPECT_EQ(GMERR_OK, ret);

    // 同步插入数据
    for (i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName1, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcInsertVertex(g_stmt_sync, i, 0, string1, g_array_num, g_vector_num);
        if (i % 100 == 0) {
            // printf("[INSERT] i = %d\n", i);
            char const *view_name = "V\\$STORAGE_LOCK_OVERVIEW";
            memset(g_command, 0, sizeof(g_command));
            snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer,

                view_name);
            snprintf(used_lock_cnt, sizeof(used_lock_cnt), "USED_LOCK_CNT: %d", i + 4);
            printf("i : %d, used_lock_cnt : %s\n", i, used_lock_cnt);
            ret = executeCommand(g_command, used_lock_cnt);
            if (ret != GMERR_OK) {
                // system(g_command);
                ret = executeCommand(g_command, "USED_LOCK_CNT: 3");
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
    }

    ret = GmcTransCommit(g_conn_sync);
    EXPECT_EQ(GMERR_OK, ret);

    // 查看视图
    char const *view_name = "V\\$STORAGE_LOCK_OVERVIEW";
    snprintf(lock_cnt, sizeof(lock_cnt), "LOCK_CNT: %d", LOCK_CNT);
    snprintf(used_lock_cnt, sizeof(used_lock_cnt), "USED_LOCK_CNT: %d", 0);
    memset(g_command, 0, sizeof(g_command));
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer,

        view_name);
    system(g_command);
    ret = executeCommand(g_command, lock_cnt, used_lock_cnt);
    EXPECT_EQ(GMERR_OK, ret);

    // 读取数据
    TestGmcDirectFetchVertex(g_stmt_sync, 0, 0, string1, g_start_num, g_end_num, g_array_num, g_vector_num,
        g_labelName1, g_lableName_PK1, true);
}

// 1个事务内，2个表，表1写少量数据，表2先触发锁升级，继续写表1，触发表1锁升级
TEST_F(TrxLockExtension_func, DML_048_003)
{
    int32_t ret = 0, i, userDataIdx = 0;
    char string1[] = "string1";
    char string2[] = "1234567";
    tbl_cnt = 1;

    char lock_cnt[50] = "", used_lock_cnt[50] = "", free_lock_cnt[50] = "", bucket_lock_cnt[50] = "",
         pool_lock_cnt[50] = "", reserve_lock_cnt[50] = "";

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    ret = GmcTransStart(g_conn_sync, &config);
    EXPECT_EQ(GMERR_OK, ret);

    // 表1同步插入数据
    g_end_num = ROW_NOT_UPGRADE_MAX_CNT;
    for (i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName1, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        // printf("[INSERT] i = %d\n", i);
        TestGmcInsertVertex(g_stmt_sync, i, 0, string1, g_array_num, g_vector_num);
    }

    // 查看视图
    char const *view_name = "V\\$STORAGE_LOCK_OVERVIEW";
    snprintf(lock_cnt, sizeof(lock_cnt), "LOCK_CNT: %d", LOCK_CNT);
    snprintf(used_lock_cnt, sizeof(used_lock_cnt), "USED_LOCK_CNT: %d", ROW_NOT_UPGRADE_MAX_CNT + 3);
    memset(g_command, 0, sizeof(g_command));
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer,

        view_name);
    system(g_command);
    ret = executeCommand(g_command, lock_cnt, used_lock_cnt);
    EXPECT_EQ(GMERR_OK, ret);

    // 表2同步插入数据触发锁升级
    g_end_num = ROW_MUST_UPGRADE_CNT;
    for (i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync2, g_labelName2, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        // printf("[INSERT] i = %d\n", i);
        TestGmcInsertVertex(g_stmt_sync2, i, 0, string1, g_array_num, g_vector_num);
    }

    // 查看视图
    snprintf(lock_cnt, sizeof(lock_cnt), "LOCK_CNT: %d", LOCK_CNT);
    snprintf(used_lock_cnt, sizeof(used_lock_cnt), "USED_LOCK_CNT: %d", ROW_NOT_UPGRADE_MAX_CNT + 3);
    memset(g_command, 0, sizeof(g_command));
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer,

        view_name);
    system(g_command);
    ret = executeCommand(g_command, lock_cnt, used_lock_cnt);
    EXPECT_EQ(GMERR_OK, ret);

    // 表1继续同步插入数据触发锁升级
    g_end_num = ROW_NOT_UPGRADE_MAX_CNT;
    for (i = ROW_NOT_UPGRADE_MAX_CNT; i < ROW_MUST_UPGRADE_CNT; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName1, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        // printf("[INSERT] i = %d\n", i);
        TestGmcInsertVertex(g_stmt_sync, i, 0, string1, g_array_num, g_vector_num);
    }

    // 查看视图
    snprintf(lock_cnt, sizeof(lock_cnt), "LOCK_CNT: %d", LOCK_CNT);
    snprintf(used_lock_cnt, sizeof(used_lock_cnt), "USED_LOCK_CNT: %d", 3);
    memset(g_command, 0, sizeof(g_command));
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer,

        view_name);
    system(g_command);
    ret = executeCommand(g_command, lock_cnt, used_lock_cnt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcTransCommit(g_conn_sync);
    EXPECT_EQ(GMERR_OK, ret);

    // 查看视图
    snprintf(lock_cnt, sizeof(lock_cnt), "LOCK_CNT: %d", LOCK_CNT);
    snprintf(used_lock_cnt, sizeof(used_lock_cnt), "USED_LOCK_CNT: %d", 0);
    memset(g_command, 0, sizeof(g_command));
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer,

        view_name);
    system(g_command);
    ret = executeCommand(g_command, lock_cnt, used_lock_cnt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 使用锁池保留的表锁资源场景1
// 创建LOCK_CNT个表，1个事务内，
// 第一个表写入ROW_MUST_UPGRADE_CNT条记录，第二个表少写一条记录，第三个表再少写一条记录，以此类推，每个表都升级
// label_index=1975的表开始写入数据报30313,内存不足
// 原设计写1500个表，由于执行时间过长。减少为50个表
TEST_F(TrxLockExtension_func, DML_048_004)
{
    int ret, i, label_index;
    void *label = 0;
    GmcConnT *conn = 0;
    GmcStmtT *stmt = 0;
    char command_buf[50] = "", command[50] = "";
    char *schema_info = NULL;
    char schema_path[512] = {0};
    char labelName[128] = "T10";
    tbl_cnt = 1;
    int num;
#ifdef ENV_RTOSV2X
    num = 2;
#else
    num = 50;
#endif

    snprintf(command_buf, sizeof(command_buf), "%d", /*LOCK_CNT*/ num);
    snprintf(command, sizeof(command), "sh create_multi_label.sh %s", command_buf);
    ret = system(command);
    EXPECT_EQ(GMERR_OK, WEXITSTATUS(ret));

    for (i = 0; i < /*LOCK_CNT*/ num; i++) {
        sprintf(schema_path, "./multi_vertexlabel/one_field_schema_%d.gmjson", i);
        readJanssonFile(schema_path, &schema_info);
        EXPECT_NE((void *)NULL, schema_info);
        sprintf(labelName, "T10_%d", i);

        ret = GmcCreateVertexLabel(g_stmt_sync, schema_info, g_label_config);
        EXPECT_EQ(GMERR_OK, ret);
        free(schema_info);
    }

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    ret = GmcTransStart(conn, &config);
    EXPECT_EQ(GMERR_OK, ret);

    // 同步插入数据
    g_end_num = ROW_MUST_UPGRADE_CNT;
    for (label_index = 0; label_index < /*LOCK_CNT*/ num; label_index++) {
        sprintf(labelName, "T10_%d", label_index);
        printf("22222222222labelName : %s\n", labelName);

        for (i = g_start_num; i < g_end_num; i++) {
            ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
            EXPECT_EQ(GMERR_OK, ret);
            // printf("[INSERT] i = %d\n", i);
            uint32_t value = i;
            ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            ASSERT_EQ(GMERR_OK, ret);
            ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
            ASSERT_EQ(GMERR_OK, ret);
            ASSERT_EQ(1, affectRows);
        }
    }

    ret = GmcTransCommit(conn);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 查看视图

    for (i = 0; i < /*LOCK_CNT*/ num; i++) {
        sprintf(labelName, "T10_%d", i);
        ret = GmcDropVertexLabel(g_stmt_sync, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

// 使用锁池保留的表锁资源场景2
// 创建POOL_LOCK_RESERVED_LOCK_CNT + 1个表，随机顺序往每个表写入1条记录，循环1000次，锁资源预期足够
TEST_F(TrxLockExtension_func, DML_048_005)
{
    int ret, i, label_index;
    void *label = 0;
    GmcConnT *conn = 0;
    GmcStmtT *stmt = 0;
    char command_buf[50] = "", command[50] = "";
    char *schema_info = NULL;
    char schema_path[512] = {0};
    char labelName[128] = "T10";
    tbl_cnt = 1;
    char lock_cnt[50] = "", used_lock_cnt[50] = "", free_lock_cnt[50] = "", bucket_lock_cnt[50] = "",
         pool_lock_cnt[50] = "", reserve_lock_cnt[50] = "";

    snprintf(command_buf, sizeof(command_buf), "%d", POOL_LOCK_RESERVED_LOCK_CNT + 1);
    snprintf(command, sizeof(command), "sh create_multi_label.sh %s", command_buf);
    ret = system(command);
    EXPECT_EQ(GMERR_OK, WEXITSTATUS(ret));

    for (i = 0; i < POOL_LOCK_RESERVED_LOCK_CNT + 1; i++) {
        sprintf(schema_path, "./multi_vertexlabel/one_field_schema_%d.gmjson", i);
        readJanssonFile(schema_path, &schema_info);
        EXPECT_NE((void *)NULL, schema_info);
        sprintf(labelName, "T10_%d", i);
        ret = GmcCreateVertexLabel(g_stmt_sync, schema_info, g_label_config);
        EXPECT_EQ(GMERR_OK, ret);
        free(schema_info);
    }

    // 洗牌算法生成随机顺序
    int poker[POOL_LOCK_RESERVED_LOCK_CNT + 1];
    for (i = 0; i < POOL_LOCK_RESERVED_LOCK_CNT + 1; i++) {
        poker[i] = i;
    }
    for (i = 0; i < POOL_LOCK_RESERVED_LOCK_CNT + 1; i++) {
        int index = rand() % (POOL_LOCK_RESERVED_LOCK_CNT + 1 - i) + i;  // 获取从i~POKER_NUM的一个索引
        std::swap(poker[i], poker[index]);                               // 交换
    }

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    ret = GmcTransStart(conn, &config);
    EXPECT_EQ(GMERR_OK, ret);

    // 同步插入数据
#ifdef ENV_RTOSV2X
    g_end_num = 10;
#else
    g_end_num = 1000;
#endif
    for (i = g_start_num; i < g_end_num; i++) {
        printf("record : %d\n", i);
        for (label_index = 0; label_index < POOL_LOCK_RESERVED_LOCK_CNT + 1; label_index++) {
            sprintf(labelName, "T10_%d", poker[label_index]);
            // printf("22222222222labelName : %s\n", labelName);
            ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
            EXPECT_EQ(GMERR_OK, ret);

            uint32_t value = i;
            ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            ASSERT_EQ(GMERR_OK, ret);
            ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
            ASSERT_EQ(GMERR_OK, ret);
            ASSERT_EQ(1, affectRows);
        }
    }

    // 查看视图
    snprintf(lock_cnt, sizeof(lock_cnt), "LOCK_CNT: %d", LOCK_CNT);
    snprintf(used_lock_cnt, sizeof(used_lock_cnt), "USED_LOCK_CNT: %d", 0);
    char const *view_name = "V\\$STORAGE_LOCK_OVERVIEW";
    memset(g_command, 0, sizeof(g_command));
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer,

        view_name);
    system(g_command);
    // ret = executeCommand(g_command, lock_cnt, used_lock_cnt);
    // EXPECT_EQ(GMERR_OK, ret);

    ret = GmcTransCommit(conn);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 查看视图
    snprintf(lock_cnt, sizeof(lock_cnt), "LOCK_CNT: %d", LOCK_CNT);
    snprintf(used_lock_cnt, sizeof(used_lock_cnt), "USED_LOCK_CNT: %d", 0);
    memset(g_command, 0, sizeof(g_command));
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer,

        view_name);
    system(g_command);
    ret = executeCommand(g_command, lock_cnt, used_lock_cnt);
    EXPECT_EQ(GMERR_OK, ret);

    for (i = 0; i < POOL_LOCK_RESERVED_LOCK_CNT + 1; i++) {
        sprintf(labelName, "T10_%d", i);
        ret = GmcDropVertexLabel(g_stmt_sync, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

// 锁资源不足场景
// 创建LOCK_CNT+1个表，1个事务内，每个表写入10条数据，中间必然触发锁资源不足，回滚
TEST_F(TrxLockExtension_func, DML_048_006)
{
    int ret, i, label_index;
    void *label = 0;
    GmcConnT *conn = 0;
    GmcStmtT *stmt = 0;
    char command_buf[50] = "", command[50] = "";
    char *schema_info = NULL;
    char schema_path[512] = {0};
    char labelName[128] = "T10";
    tbl_cnt = 1;
    char lock_cnt[50] = "", used_lock_cnt[50] = "", free_lock_cnt[50] = "", bucket_lock_cnt[50] = "",
         pool_lock_cnt[50] = "", reserve_lock_cnt[50] = "";
    int label_num;
#ifdef ENV_RTOSV2X
    label_num = 100;
#else
    label_num = 1018;  // 20211009 简单表增加个数上限1024
#endif

    snprintf(command_buf, sizeof(command_buf), "%d", label_num + 1);
    snprintf(command, sizeof(command), "sh create_multi_label.sh %s", command_buf);
    ret = system(command);
    EXPECT_EQ(GMERR_OK, WEXITSTATUS(ret));
    printf("create_multi_label done\n");

    for (i = 0; i < label_num + 1; i++) {
        sprintf(schema_path, "./multi_vertexlabel/one_field_schema_%d.gmjson", i);
        readJanssonFile(schema_path, &schema_info);
        EXPECT_NE((void *)NULL, schema_info);
        sprintf(labelName, "T10_%d", i);

        ret = GmcCreateVertexLabel(g_stmt_sync, schema_info, g_label_config);
        EXPECT_EQ(GMERR_OK, ret);
        free(schema_info);
    }

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    ret = GmcTransStart(conn, &config);
    EXPECT_EQ(GMERR_OK, ret);

    // 同步插入数据
    g_end_num = 10;
    char const *viewName = "STORAGE_LOCK_OVERVIEW";
    int memCompactEnable = 0;
    ret = TestGetConfigValueInt("memCompactEnable", &memCompactEnable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int no_lock_res_label_index = 0, no_lock_res_i = 0, flag = 0;
    for (label_index = 0; label_index < label_num + 1; label_index++) {
        sprintf(labelName, "T10_%d", label_index);
        // printf("2222222222labelName : %s\n", labelName);

        for (i = g_start_num; i < g_end_num; i++) {
            ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
            EXPECT_EQ(GMERR_OK, ret);
            // printf("[INSERT] i = %d\n", i);
            uint32_t value = i;
            ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            EXPECT_EQ(true, ret == GMERR_OK || ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_TRANSACTION_ROLLBACK);
            if (ret == GMERR_OK) {
                ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
                ASSERT_EQ(GMERR_OK, ret);
                ASSERT_EQ(1, affectRows);
                // printf("ret : %d\n", ret);
            } else if (ret == GMERR_LOCK_NOT_AVAILABLE) {  // 报错需要手动回滚
                ret = testGmcGetLastError(NULL);
                EXPECT_EQ(GMERR_OK, ret);

                if (flag == 0) {
                    flag = 1;
                    no_lock_res_label_index = label_index;
                    no_lock_res_i = i;
                }
            } else if (ret == GMERR_TRANSACTION_ROLLBACK) {
                printf("label_index : %d, i: %d, ret : %d\n", label_index, i, ret);
                ret = testGmcGetLastError(NULL);
                EXPECT_EQ(GMERR_OK, ret);

                ret = GmcTransRollBack(conn);
                EXPECT_EQ(GMERR_OK, ret);

                // 查看视图
                int32_t lockCnt = GetViewValueByField(viewName, "LOCK_CNT");
                AW_MACRO_EXPECT_EQ_INT(LOCK_CNT, lockCnt);
                int32_t usedLockCnt = GetViewValueByField(viewName, "USED_LOCK_CNT");
                if (memCompactEnable == 1) {
                    usedLockCnt = (usedLockCnt == 1 ? 0 : usedLockCnt);
                    AW_MACRO_EXPECT_EQ_INT(0, usedLockCnt);
                } else {
                    AW_MACRO_EXPECT_EQ_INT(0, usedLockCnt);
                }
                printf("label_index : %d, i : %d, ret : %d, flag : %d\n", label_index, i, ret, flag);
            } else {
                printf("label_index : %d, i: %d, ret : %d\n", label_index, i, ret);
            }
        }
    }
    printf("no_lock_res_label_index : %d, no_lock_res_i : %d\n", no_lock_res_label_index, no_lock_res_i);

    ret = GmcTransCommit(conn);
    EXPECT_EQ(GMERR_OK, ret);

    for (label_index = 0; label_index < label_num + 1; label_index++) {
        sprintf(labelName, "T10_%d", label_index);

        for (i = g_start_num; i < g_end_num; i++) {
            ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
            EXPECT_EQ(GMERR_OK, ret);
            // printf("[QUERY] label_index : %d, i : %d\n", label_index, i);
            uint32_t f0Value = i, isNull = 0;
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f0Value, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyName(stmt, g_lableName_PK5);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            if (label_index < no_lock_res_label_index) {
                bool isFinish = true;
                ret = GmcFetch(stmt, &isFinish);
                ASSERT_EQ(GMERR_OK, ret);
                ASSERT_EQ(isFinish, true);
            } else if (label_index == no_lock_res_label_index && (i == no_lock_res_i || i == no_lock_res_i + 1)) {
                bool isFinish = true;
                ret = GmcFetch(stmt, &isFinish);
                ASSERT_EQ(GMERR_OK, ret);
                ASSERT_EQ(isFinish, true);
            } else {
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
    }

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 查看视图
    int32_t lockCnt = GetViewValueByField(viewName, "LOCK_CNT");
    AW_MACRO_EXPECT_EQ_INT(LOCK_CNT, lockCnt);
    int32_t usedLockCnt = GetViewValueByField(viewName, "USED_LOCK_CNT");
    if (memCompactEnable == 1) {
        usedLockCnt = (usedLockCnt == 1 ? 0 : usedLockCnt);
        AW_MACRO_EXPECT_EQ_INT(0, usedLockCnt);
    } else {
        AW_MACRO_EXPECT_EQ_INT(0, usedLockCnt);
    }

    for (i = 0; i < label_num + 1; i++) {
        sprintf(labelName, "T10_%d", i);
        ret = GmcDropVertexLabel(g_stmt_sync, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

void *thread_trx_lock1(void *args)
{
    int ret, i;
    // int id = *((int *)args);
    void *label = 0;
    GmcConnT *conn = 0;
    GmcStmtT *stmt = 0;
    int affectRows;
    unsigned int len;
    char string1[] = "string1";
    char string2[] = "1234567";
    int start_num = 0, end_num = 0;

    start_num = 0;
    end_num = 10;

    // 创建同步连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    ret = GmcTransStart(conn, &config);
    EXPECT_EQ(GMERR_OK, ret);

    // 同步插入数据
    for (i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_labelName1, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        // printf("[INSERT] i : %d\n", i);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);

        TestGmcNodeSetPropertyByName_PK(root, i);
        TestGmcNodeSetPropertyByName_R(root, i, 0, string1);

        uint8_t f21_value = i & 0xF;
        ret = GmcNodeSetPropertyByName(root, (char *)"F21", GMC_DATATYPE_PARTITION, &f21_value, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);

        TestGmcNodeSetPropertyByName_P(t1, i, 0, string1);
        // 插入array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < g_array_num; j++) {
            TestGmcNodeSetPropertyByName_A(t2, i, 0, string1);
            GmcNodeGetNextElement(t2, &t2);
        }
        // 插入vector节点
        for (uint32_t j = 0; j < g_vector_num; j++) {
            ret = GmcNodeAppendElement(t3, &t3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_V(t3, i, 0, string1);
        }
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    pthread_mutex_lock(&g_threadLock);
    g_thread_wait++;
    pthread_mutex_unlock(&g_threadLock);

    struct timeval start;
    struct timeval end;
    unsigned long duration;
    int thread_lock = g_thread_lock;

    while (g_thread_lock <= ROW_MUST_UPGRADE_CNT + ROW_MUST_UPGRADE_CNT) {
        // printf("thread_lock : %d, g_thread_lock : %d.\r\n", thread_lock, g_thread_lock);
        gettimeofday(&start, NULL);
        while (thread_lock == g_thread_lock) {
            gettimeofday(&end, NULL);
            duration = 1000000 * (end.tv_sec - start.tv_sec) + end.tv_usec - start.tv_usec;
            if (duration / 1000000 >= 3) {  // g_thread_lock超过3秒没有变化，说明事务2想要锁升级，正在等锁
                printf("1 thread_lock : %d, g_thread_lock : %d, wait lock spent %lf seconds.\r\n", thread_lock,
                    g_thread_lock, (double)duration / 1000000);
                break;
            }
        }
        thread_lock++;
        if (duration / 1000000 >= 3) {  // g_thread_lock超过3秒没有变化，说明事务2想要锁升级，正在等锁
            printf("2 thread_lock : %d, g_thread_lock : %d, wait lock spent %lf seconds.\r\n", thread_lock,
                g_thread_lock, (double)duration / 1000000);
            break;
        }
    }

    // sleep(20);
    // 等待3秒后直接提交，让事务2等待后可以完成锁升级
    ret = GmcTransCommit(conn);
    EXPECT_EQ(GMERR_OK, ret);

    pthread_mutex_lock(&g_threadLock);
    g_thread_wait++;
    pthread_mutex_unlock(&g_threadLock);

    for (i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_labelName1, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        // printf("[QUERY] i : %d\n", i);
        int64_t f0Value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, g_lableName_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

void *thread_trx_lock2(void *args)
{
    int ret, i;
    // int id = *((int *)args);
    void *label = 0;
    GmcConnT *conn = 0;
    GmcStmtT *stmt = 0;
    int affectRows;
    unsigned int len;
    char string1[] = "string1";
    char string2[] = "1234567";
    int start_num = 0, end_num = 0;

    start_num = ROW_MUST_UPGRADE_CNT;
    end_num = ROW_MUST_UPGRADE_CNT + ROW_MUST_UPGRADE_CNT;

    // 创建同步连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    while (g_thread_wait < 1) {
        usleep(1000000);
    }

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    ret = GmcTransStart(conn, &config);
    EXPECT_EQ(GMERR_OK, ret);

    // 同步插入数据
    int upgrade_i = 0;
    for (i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_labelName1, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        // printf("[INSERT] i : %d\n", i);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_PK(root, i);
        TestGmcNodeSetPropertyByName_R(root, i, 0, string1);

        uint8_t f21_value = i & 0xF;
        ret = GmcNodeSetPropertyByName(root, (char *)"F21", GMC_DATATYPE_PARTITION, &f21_value, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);

        TestGmcNodeSetPropertyByName_P(t1, i, 0, string1);
        // 插入array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < g_array_num; j++) {
            TestGmcNodeSetPropertyByName_A(t2, i, 0, string1);
            GmcNodeGetNextElement(t2, &t2);
        }
        // 插入vector节点
        for (uint32_t j = 0; j < g_vector_num; j++) {
            ret = GmcNodeAppendElement(t3, &t3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_V(t3, i, 0, string1);
        }
        ret = GmcExecute(stmt);
        g_thread_lock++;
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            upgrade_i = i;
            printf("GMERR_LOCK_NOT_AVAILABLE, upgrade_i = %d\n", upgrade_i);

            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);

            pthread_mutex_lock(&g_threadLock);
            g_thread_wait++;
            pthread_mutex_unlock(&g_threadLock);
        } else if (ret == GMERR_TRANSACTION_ROLLBACK) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);

            printf("GMERR_TRANSACTION_ROLLBACK, i = %d\n", i);

            ret = GmcTransRollBack(conn);
            EXPECT_EQ(GMERR_OK, ret);
        } else {
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(1, affectRows);
        }
    }
    printf("upgrade_i = %d, GmcTransCommit\n", upgrade_i);

    ret = GmcTransCommit(conn);
    EXPECT_EQ(GMERR_OK, ret);

    for (i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_labelName1, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        // printf("[QUERY] i : %d\n", i);
        int64_t f0Value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, g_lableName_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (i <= upgrade_i + 1) {
            bool isFinish = true;
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(isFinish, true);

        } else {
            EXPECT_EQ(GMERR_OK, ret);
        }
    }

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

// 同一个表，2事务同时编辑不重叠的数据，其中一个写入较多数据，触发表锁升级，升级成功场景。
TEST_F(TrxLockExtension_func, DML_048_007)
{
    int32_t ret = 0, i;
    char string1[] = "string1";
    char string2[] = "1234567";
    pthread_t thr_arr[1000];
    void *thr_ret[1000];
    int index[1000] = {0};
    g_thread_wait = 0;
    g_thread_lock = 0;

    pthread_create(&thr_arr[0], NULL, thread_trx_lock1, NULL);
    pthread_create(&thr_arr[1], NULL, thread_trx_lock2, NULL);

    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);

    // 查看视图
    CheckTrxLock();
}

void *thread_trx_lock3(void *args)
{
    int ret, i;
    // int id = *((int *)args);
    void *label = 0;
    GmcConnT *conn = 0;
    GmcStmtT *stmt = 0;
    int affectRows;
    unsigned int len;
    char string1[] = "string1";
    char string2[] = "1234567";
    int start_num = 0, end_num = 0;

    start_num = 0;
    end_num = 10;

    // 创建同步连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    ret = GmcTransStart(conn, &config);
    EXPECT_EQ(GMERR_OK, ret);

    // 同步插入数据
    for (i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_labelName1, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        // printf("[INSERT] i : %d\n", i);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_PK(root, i);
        TestGmcNodeSetPropertyByName_R(root, i, 0, string1);

        uint8_t f21_value = i & 0xF;
        ret = GmcNodeSetPropertyByName(root, (char *)"F21", GMC_DATATYPE_PARTITION, &f21_value, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);

        TestGmcNodeSetPropertyByName_P(t1, i, 0, string1);
        // 插入array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < g_array_num; j++) {
            TestGmcNodeSetPropertyByName_A(t2, i, 0, string1);
            GmcNodeGetNextElement(t2, &t2);
        }
        // 插入vector节点
        for (uint32_t j = 0; j < g_vector_num; j++) {
            ret = GmcNodeAppendElement(t3, &t3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_V(t3, i, 0, string1);
        }
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    pthread_mutex_lock(&g_threadLock);
    g_thread_wait++;
    pthread_mutex_unlock(&g_threadLock);

    struct timeval start;
    struct timeval end;
    unsigned long duration;
    int thread_lock = g_thread_lock;

    while (g_thread_lock <= ROW_MUST_UPGRADE_CNT + ROW_MUST_UPGRADE_CNT) {
        // printf("thread_lock : %d, g_thread_lock : %d.\r\n", thread_lock, g_thread_lock);
        gettimeofday(&start, NULL);
        while (thread_lock == g_thread_lock) {
            gettimeofday(&end, NULL);
            duration = 1000000 * (end.tv_sec - start.tv_sec) + end.tv_usec - start.tv_usec;
            if (duration / 1000000 >= 3) {  // g_thread_lock超过3秒没有变化，说明事务2想要锁升级，正在等锁
                printf("1 thread_lock : %d, g_thread_lock : %d, wait lock spent %lf seconds.\r\n", thread_lock,
                    g_thread_lock, (double)duration / 1000000);
                break;
            }
        }
        thread_lock++;
        if (duration / 1000000 >= 3) {  // g_thread_lock超过3秒没有变化，说明事务2想要锁升级，正在等锁
            printf("2 thread_lock : %d, g_thread_lock : %d, wait lock spent %lf seconds.\r\n", thread_lock,
                g_thread_lock, (double)duration / 1000000);
            break;
        }
    }

    sleep(20);  // sleep使事务2锁升级等待超时，返回GMERR_LOCK_NOT_AVAILABLE

    ret = GmcTransCommit(conn);
    EXPECT_EQ(GMERR_OK, ret);

    pthread_mutex_lock(&g_threadLock);
    g_thread_wait++;
    pthread_mutex_unlock(&g_threadLock);

    for (i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_labelName1, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        // printf("[QUERY] i : %d\n", i);
        int64_t f0Value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, g_lableName_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

// 同一个表，2事务同时编辑不重叠的数据，其中一个写入较多数据，触发表锁升级，升级超时场景。
TEST_F(TrxLockExtension_func, DML_048_008)
{
    int32_t ret = 0, i;
    char string1[] = "string1";
    char string2[] = "1234567";
    pthread_t thr_arr[1000];
    void *thr_ret[1000];
    int index[1000] = {0};
    g_thread_wait = 0;
    g_thread_lock = 0;

    pthread_create(&thr_arr[0], NULL, thread_trx_lock3, NULL);
    pthread_create(&thr_arr[1], NULL, thread_trx_lock2, NULL);

    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);

    // 查看视图
    CheckTrxLock();
}

void *thread_trx_lock4(void *args)
{
    int ret, i;
    // int id = *((int *)args);
    void *label = 0;
    GmcConnT *conn = 0;
    GmcStmtT *stmt = 0;
    int affectRows;
    unsigned int len;
    char string1[] = "string1";
    char string2[] = "1234567";
    int start_num = 0, end_num = 0;

    start_num = 0;
    end_num = 10;

    // 创建同步连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    ret = GmcTransStart(conn, &config);
    EXPECT_EQ(GMERR_OK, ret);

    // 同步插入数据
    for (i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_labelName1, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        // printf("[INSERT] i : %d\n", i);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_PK(root, i);
        TestGmcNodeSetPropertyByName_R(root, i, 0, string1);

        uint8_t f21_value = i & 0xF;
        ret = GmcNodeSetPropertyByName(root, (char *)"F21", GMC_DATATYPE_PARTITION, &f21_value, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);

        TestGmcNodeSetPropertyByName_P(t1, i, 0, string1);
        // 插入array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < g_array_num; j++) {
            TestGmcNodeSetPropertyByName_A(t2, i, 0, string1);
            GmcNodeGetNextElement(t2, &t2);
        }
        // 插入vector节点
        for (uint32_t j = 0; j < g_vector_num; j++) {
            ret = GmcNodeAppendElement(t3, &t3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_V(t3, i, 0, string1);
        }
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    pthread_mutex_lock(&g_threadLock);
    g_thread_wait++;
    pthread_mutex_unlock(&g_threadLock);

    struct timeval start;
    struct timeval end;
    unsigned long duration;
    int thread_lock = g_thread_lock;

    while (g_thread_lock <= ROW_MUST_UPGRADE_CNT + ROW_MUST_UPGRADE_CNT) {
        // printf("thread_lock : %d, g_thread_lock : %d.\r\n", thread_lock, g_thread_lock);
        gettimeofday(&start, NULL);
        while (thread_lock == g_thread_lock) {
            gettimeofday(&end, NULL);
            duration = 1000000 * (end.tv_sec - start.tv_sec) + end.tv_usec - start.tv_usec;
            if (duration / 1000000 >= 3) {  // g_thread_lock超过3秒没有变化，说明事务2想要锁升级，正在等锁
                printf("1 thread_lock : %d, g_thread_lock : %d, wait lock spent %lf seconds.\r\n", thread_lock,
                    g_thread_lock, (double)duration / 1000000);
                break;
            }
        }
        thread_lock++;
        if (duration / 1000000 >= 3) {  // g_thread_lock超过3秒没有变化，说明事务2想要锁升级，正在等锁
            printf("2 thread_lock : %d, g_thread_lock : %d, wait lock spent %lf seconds.\r\n", thread_lock,
                g_thread_lock, (double)duration / 1000000);
            break;
        }
    }

    start_num = 10;
    end_num = ROW_MUST_UPGRADE_CNT;
    for (i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_labelName1, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        // printf("[INSERT] i : %d\n", i);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_PK(root, i);
        TestGmcNodeSetPropertyByName_R(root, i, 0, string1);

        uint8_t f21_value = i & 0xF;
        ret = GmcNodeSetPropertyByName(root, (char *)"F21", GMC_DATATYPE_PARTITION, &f21_value, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);

        TestGmcNodeSetPropertyByName_P(t1, i, 0, string1);
        // 插入array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < g_array_num; j++) {
            TestGmcNodeSetPropertyByName_A(t2, i, 0, string1);
            GmcNodeGetNextElement(t2, &t2);
        }
        // 插入vector节点
        for (uint32_t j = 0; j < g_vector_num; j++) {
            ret = GmcNodeAppendElement(t3, &t3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_V(t3, i, 0, string1);
        }
        ret = GmcExecute(stmt);
        EXPECT_EQ(true, ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_OK || ret == GMERR_TRANSACTION_ROLLBACK);

        if (ret != GMERR_OK) {  // 两个事务同时升级，会有锁冲突
            printf("i = %d, ret = %d\n", i, ret);
            if (ret == GMERR_TRANSACTION_ROLLBACK) {
                ret = testGmcGetLastError(NULL);
                EXPECT_EQ(GMERR_OK, ret);

                ret = GmcTransRollBack(conn);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
    }

    pthread_mutex_lock(&g_threadLock);
    g_thread_wait++;
    pthread_mutex_unlock(&g_threadLock);

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

// 同一个表，2事务同时编辑不重叠的数据，其中一个写入较多数据，分别写入较多数据，分别触发表锁升级，升级冲突场景
TEST_F(TrxLockExtension_func, DML_048_009)
{
    int32_t ret = 0, i;
    char string1[] = "string1";
    char string2[] = "1234567";
    pthread_t thr_arr[1000];
    void *thr_ret[1000];
    int index[1000] = {0};
    g_thread_wait = 0;
    g_thread_lock = 0;

    pthread_create(&thr_arr[0], NULL, thread_trx_lock4, NULL);
    pthread_create(&thr_arr[1], NULL, thread_trx_lock2, NULL);

    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);

    CheckTrxLock();
}

void *thread_trx_lock5(void *args)
{
    int ret, i;
    // int id = *((int *)args);
    void *label = 0;
    GmcConnT *conn = 0;
    GmcStmtT *stmt = 0;
    int affectRows;
    unsigned int len;
    char string1[] = "string1";
    char string2[] = "1234567";
    int start_num = 0, end_num = 0;

    start_num = ROW_MUST_UPGRADE_CNT;
    end_num = ROW_MUST_UPGRADE_CNT + ROW_MUST_UPGRADE_CNT;

    // 创建同步连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    ret = GmcTransStart(conn, &config);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);  // 扫描加IS锁
    EXPECT_EQ(GMERR_OK, ret);

    pthread_mutex_lock(&g_threadLock);
    g_thread_wait++;
    pthread_mutex_unlock(&g_threadLock);

    bool isFinish;
    int cnt = 0, id;

    for (id = start_num; id < end_num + 1; id++) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true) {
            // printf("id : %d\n", id);
            break;
        }
        cnt++;
    }
    GmcResetStmt(stmt);
    // EXPECT_EQ(end_num - start_num, cnt);
    // printf("[INFO] scan cnt : %d\r\n", cnt);

    struct timeval start;
    struct timeval end;
    unsigned long duration;
    int thread_lock = g_thread_lock;

    while (g_thread_lock <= ROW_MUST_UPGRADE_CNT + ROW_MUST_UPGRADE_CNT) {
        // printf("thread_lock : %d, g_thread_lock : %d.\r\n", thread_lock, g_thread_lock);
        gettimeofday(&start, NULL);
        while (thread_lock == g_thread_lock) {
            gettimeofday(&end, NULL);
            duration = 1000000 * (end.tv_sec - start.tv_sec) + end.tv_usec - start.tv_usec;
            if (duration / 1000000 >= 3) {  // g_thread_lock超过3秒没有变化，说明事务2想要锁升级，正在等锁
                printf("1 thread_lock : %d, g_thread_lock : %d, wait lock spent %lf seconds.\r\n", thread_lock,
                    g_thread_lock, (double)duration / 1000000);
                break;
            }
        }
        thread_lock++;
        if (duration / 1000000 >= 3) {  // g_thread_lock超过3秒没有变化，说明事务2想要锁升级，正在等锁
            printf("2 thread_lock : %d, g_thread_lock : %d, wait lock spent %lf seconds.\r\n", thread_lock,
                g_thread_lock, (double)duration / 1000000);
            break;
        }
    }

    sleep(20);  // 事务2锁升级等待超时，返回GMERR_LOCK_NOT_AVAILABLE

    ret = GmcTransCommit(conn);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

void *thread_trx_lock6(void *args)
{
    int ret, i;
    // int id = *((int *)args);
    void *label = 0;
    GmcConnT *conn = 0;
    GmcStmtT *stmt = 0;
    int affectRows;
    unsigned int len;
    char string1[] = "string1";
    char string2[] = "1234567";
    int start_num = 0, end_num = 0;

    start_num = ROW_MUST_UPGRADE_CNT;
    end_num = ROW_MUST_UPGRADE_CNT + ROW_MUST_UPGRADE_CNT;

    // 创建同步连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    while (g_thread_wait < 1) {
        usleep(1000000);
    }

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    ret = GmcTransStart(conn, &config);
    EXPECT_EQ(GMERR_OK, ret);

    // 同步插入数据
    int upgrade_i = 0;
    for (i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_labelName1, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        // printf("[INSERT] i : %d\n", i);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_PK(root, i);
        TestGmcNodeSetPropertyByName_R(root, i, 0, string1);

        uint8_t f21_value = i & 0xF;
        ret = GmcNodeSetPropertyByName(root, (char *)"F21", GMC_DATATYPE_PARTITION, &f21_value, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);

        TestGmcNodeSetPropertyByName_P(t1, i, 0, string1);
        // 插入array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < g_array_num; j++) {
            TestGmcNodeSetPropertyByName_A(t2, i, 0, string1);
            GmcNodeGetNextElement(t2, &t2);
        }
        // 插入vector节点
        for (uint32_t j = 0; j < g_vector_num; j++) {
            ret = GmcNodeAppendElement(t3, &t3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_V(t3, i, 0, string1);
        }
        ret = GmcExecute(stmt);
        g_thread_lock++;
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);

            upgrade_i = i;
            printf("GMERR_LOCK_NOT_AVAILABLE, upgrade_i = %d\n", upgrade_i);

            pthread_mutex_lock(&g_threadLock);
            g_thread_wait++;
            pthread_mutex_unlock(&g_threadLock);
        } else if (ret == GMERR_TRANSACTION_ROLLBACK) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);

            printf("GMERR_TRANSACTION_ROLLBACK, i = %d\n", i);

            ret = GmcTransRollBack(conn);
            EXPECT_EQ(GMERR_OK, ret);
        } else {
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(1, affectRows);
        }
    }
    printf("upgrade_i = %d\n", upgrade_i);
    ret = GmcTransCommit(conn);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

// 同一个表，事务1开启扫描，事务2写入ROW_MUST_UPGRADE_CNT条数据，触发升级，由于事务1一直不提交，事务2升级超时，事务失败回滚
TEST_F(TrxLockExtension_func, DML_048_010)
{
    int32_t ret = 0, i;
    char string1[] = "string1";
    char string2[] = "1234567";
    pthread_t thr_arr[1000];
    void *thr_ret[1000];
    int index[1000] = {0};
    g_thread_wait = 0;
    g_thread_lock = 0;

    pthread_create(&thr_arr[0], NULL, thread_trx_lock5, NULL);
    pthread_create(&thr_arr[1], NULL, thread_trx_lock6, NULL);

    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);

    CheckTrxLock();
}

void *thread_trx_lock7(void *args)
{
    int ret, i;
    // int id = *((int *)args);
    void *label = 0;
    GmcConnT *conn = 0;
    GmcStmtT *stmt = 0;
    int affectRows;
    unsigned int len;
    char string1[] = "string1";
    char string2[] = "1234567";
    int start_num = 0, end_num = 0;

    start_num = ROW_MUST_UPGRADE_CNT;
    end_num = ROW_MUST_UPGRADE_CNT + ROW_MUST_UPGRADE_CNT;

    // 创建同步连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    ret = GmcTransStart(conn, &config);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);  // 扫描加IS锁
    EXPECT_EQ(GMERR_OK, ret);

    pthread_mutex_lock(&g_threadLock);
    g_thread_wait++;
    pthread_mutex_unlock(&g_threadLock);

    bool isFinish;
    int cnt = 0, id;

    for (id = start_num; id < end_num + 1; id++) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true) {
            // printf("id : %d\n", id);
            break;
        }
        cnt++;
    }
    GmcResetStmt(stmt);
    // EXPECT_EQ(end_num - start_num, cnt);
    // printf("[INFO] scan cnt : %d\r\n", cnt);

    struct timeval start;
    struct timeval end;
    unsigned long duration;
    int thread_lock = g_thread_lock;

    while (g_thread_lock <= ROW_MUST_UPGRADE_CNT + ROW_MUST_UPGRADE_CNT) {
        // printf("thread_lock : %d, g_thread_lock : %d.\r\n", thread_lock, g_thread_lock);
        gettimeofday(&start, NULL);
        while (thread_lock == g_thread_lock) {
            gettimeofday(&end, NULL);
            duration = 1000000 * (end.tv_sec - start.tv_sec) + end.tv_usec - start.tv_usec;
            if (duration / 1000000 >= 3) {  // g_thread_lock超过3秒没有变化，说明事务2想要锁升级，正在等锁
                printf("1 thread_lock : %d, g_thread_lock : %d, wait lock spent %lf seconds.\r\n", thread_lock,
                    g_thread_lock, (double)duration / 1000000);
                break;
            }
        }
        thread_lock++;
        if (duration / 1000000 >= 3) {  // g_thread_lock超过3秒没有变化，说明事务2想要锁升级，正在等锁
            printf("2 thread_lock : %d, g_thread_lock : %d, wait lock spent %lf seconds.\r\n", thread_lock,
                g_thread_lock, (double)duration / 1000000);
            break;
        }
    }

    // sleep(20);

    // 事务2锁升级等待时提交，锁升级成功
    ret = GmcTransCommit(conn);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

// 同一个表，事务1开启扫描，事务2写入ROW_MUST_UPGRADE_CNT条数据，触发升级，事务1等待3s后再提交，事务2升级成功
TEST_F(TrxLockExtension_func, DML_048_011)
{
    int32_t ret = 0, i;
    pthread_t thr_arr[1000];
    void *thr_ret[1000];
    int index[1000] = {0};
    g_thread_wait = 0;

    pthread_create(&thr_arr[0], NULL, thread_trx_lock7, NULL);
    pthread_create(&thr_arr[1], NULL, thread_trx_lock6, NULL);

    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);

    CheckTrxLock();
}

void *thread_trx_lock8(void *args)
{
    int ret, i;
    // int id = *((int *)args);
    void *label = 0;
    GmcConnT *conn = 0;
    GmcStmtT *stmt = 0;
    int affectRows;
    unsigned int len;
    int start_num = 0, end_num = 0, label_index;
    char labelName[128] = "T10";

    start_num = 0;
    end_num = ROW_MUST_UPGRADE_CNT;

    int label_num = 50;
#ifdef ENV_RTOSV2X
    label_num = 5;
#else
    label_num = 50;
#endif

    // 创建同步连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    ret = GmcTransStart(conn, &config);
    EXPECT_EQ(GMERR_OK, ret);

    // 同步插入数据
    for (label_index = 0; label_index < (label_num /*POOL_LOCK_RESERVED_LOCK_CNT + 1*/) / 2; label_index++) {
        sprintf(labelName, "T10_%d", label_index);
        // printf("1111111111labelName : %s\n", labelName);

        for (i = start_num; i < end_num; i++) {
            ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
            EXPECT_EQ(GMERR_OK, ret);
            uint32_t value = i;
            // printf("[INSERT] i : %d\n", i);
            ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(1, affectRows);
        }
    }

    ret = GmcTransCommit(conn);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

void *thread_trx_lock9(void *args)
{
    int ret, i;
    // int id = *((int *)args);
    void *label = 0;
    GmcConnT *conn = 0;
    GmcStmtT *stmt = 0;
    int affectRows;
    unsigned int len;
    int start_num = 0, end_num = 0, label_index;
    char labelName[128] = "T10";

    start_num = 0;
    end_num = ROW_MUST_UPGRADE_CNT;

    int label_num = 50;
#ifdef ENV_RTOSV2X
    label_num = 5;
#else
    label_num = 50;
#endif

    // 创建同步连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    ret = GmcTransStart(conn, &config);
    EXPECT_EQ(GMERR_OK, ret);

    // 同步插入数据
    for (label_index = (label_num /*POOL_LOCK_RESERVED_LOCK_CNT + 1*/) / 2;
         label_index < label_num /*POOL_LOCK_RESERVED_LOCK_CNT + 1*/; label_index++) {
        sprintf(labelName, "T10_%d", label_index);
        // printf("1111111111labelName : %s\n", labelName);

        for (i = start_num; i < end_num; i++) {
            ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
            EXPECT_EQ(GMERR_OK, ret);
            uint32_t value = i;
            // printf("[INSERT] i : %d\n", i);
            ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(1, affectRows);
        }
    }

    ret = GmcTransCommit(conn);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

// 2个事务内，各（POOL_LOCK_RESERVED_LOCK_CNT + 1）/2个表，各写ROW_MUST_UPGRADE_CNT条数据，预期都能锁升级
// 构建时间过长，需要30min，减少表数
TEST_F(TrxLockExtension_func, DML_048_012)
{
    int ret, i, circle;
    void *label = {0};
    char command_buf[50] = "", command[50] = "";
    char *schema_info = NULL;
    char schema_path[512] = {0};
    char labelName[128] = "T10";
    tbl_cnt = 1;
    g_thread_wait = 0;
    int label_num = 50;
#if defined TEST_STATIC_ASAN
    label_num = 5;
#elif defined ENV_RTOSV2X
    label_num = 5;
#else
    label_num = 50;
#endif

    snprintf(command_buf, sizeof(command_buf), "%d", label_num /*POOL_LOCK_RESERVED_LOCK_CNT + 1*/);
    snprintf(command, sizeof(command), "sh create_multi_label.sh %s", command_buf);
    ret = system(command);
    EXPECT_EQ(GMERR_OK, WEXITSTATUS(ret));

    for (circle = 0; circle < 2; circle++) {  // CI执行时间过长，减小循环次数
        printf("circle : %d\n", circle);
        for (i = 0; i < label_num /*POOL_LOCK_RESERVED_LOCK_CNT + 1*/; i++) {
            sprintf(schema_path, "./multi_vertexlabel/one_field_schema_%d.gmjson", i);
            readJanssonFile(schema_path, &schema_info);
            EXPECT_NE((void *)NULL, schema_info);
            sprintf(labelName, "T10_%d", i);
            // printf("1111111111labelName : %s\n", labelName);

            ret = GmcCreateVertexLabel(g_stmt_sync, schema_info, g_label_config);
            EXPECT_EQ(GMERR_OK, ret);
            free(schema_info);
        }

        pthread_t thr_arr[1000];
        void *thr_ret[1000];
        int index[1000] = {0};

        pthread_create(&thr_arr[0], NULL, thread_trx_lock8, NULL);
        pthread_create(&thr_arr[1], NULL, thread_trx_lock9, NULL);

        pthread_join(thr_arr[0], NULL);
        pthread_join(thr_arr[1], NULL);

        // 查看视图

        for (i = 0; i < label_num /*POOL_LOCK_RESERVED_LOCK_CNT + 1*/; i++) {
            sprintf(labelName, "T10_%d", i);
            // printf("33333333333labelName : %s\n", labelName);
            ret = GmcDropVertexLabel(g_stmt_sync, labelName);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }

    char lock_cnt[50] = "", used_lock_cnt[50] = "", free_lock_cnt[50] = "", bucket_lock_cnt[50] = "",
         pool_lock_cnt[50] = "", reserve_lock_cnt[50] = "";
    snprintf(lock_cnt, sizeof(lock_cnt), "LOCK_CNT: %d", LOCK_CNT);
    snprintf(used_lock_cnt, sizeof(used_lock_cnt), "USED_LOCK_CNT: %d", 0);

    char const *view_name = "V\\$STORAGE_LOCK_OVERVIEW";
    memset(g_command, 0, sizeof(g_command));
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer,

        view_name);
    system(g_command);
    ret = executeCommand(g_command, lock_cnt, used_lock_cnt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 1024个事务内，共1个表，各写100条数据，不提交。
// 预期后面从某个事务开始，一个会升级超时失败，其余均由于升级冲突失败
TEST_F(TrxLockExtension_func, DML_048_013)
{
    int32_t ret = 0, i, conn_index;
    char string1[] = "string1";
    char string2[] = "1234567";
    GmcConnT *conn[MAX_CONN] = {0};
    GmcStmtT *stmt[MAX_CONN] = {0};
    void *label[MAX_CONN] = {0};
    tbl_cnt = 1;
    g_end_num = 100;

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    ret = GmcTransStart(g_conn_sync, &config);
    EXPECT_EQ(GMERR_OK, ret);

    // 同步插入数据
    for (i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync5, g_labelName5, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t value = i;
        // printf("[INSERT] i : %d\n", i);
        ret = GmcSetVertexProperty(g_stmt_sync5, "F0", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync5);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync5, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    int cnt = 0;
    for (conn_index = 1; conn_index < MAX_CONN; conn_index++) {
        printf("conn_index = %d\n", conn_index);
        ret = testGmcConnect(&conn[conn_index], &stmt[conn_index]);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(stmt[conn_index], g_labelName5, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);

        GmcTxConfigT config;
        config.transMode = GMC_TRANS_USED_IN_CS;
        config.type = GMC_TX_ISOLATION_COMMITTED;
        config.readOnly = false;
        config.trxType = GMC_DEFAULT_TRX;
        ret = GmcTransStart(conn[conn_index], &config);
        EXPECT_EQ(GMERR_OK, ret);

        for (i = g_end_num * conn_index; i < g_end_num * conn_index + g_end_num; i++) {
            uint32_t value = i;
            // printf("[INSERT] i : %d\n", i);
            ret = GmcSetVertexProperty(stmt[conn_index], "F0", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt[conn_index]);
            EXPECT_EQ(true, ret == GMERR_OK || ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_LOCK_NOT_AVAILABLE ||
                                ret == GMERR_TRANSACTION_ROLLBACK);

            if (ret == GMERR_OK) {
                ret = GmcGetStmtAttr(stmt[conn_index], GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
                EXPECT_EQ(GMERR_OK, ret);
                ASSERT_EQ(1, affectRows);
            } else if (ret == GMERR_TRANSACTION_ROLLBACK) {
                printf("ret : %d\n", ret);
                ret = testGmcGetLastError(NULL);
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcTransRollBack(conn[conn_index]);
                EXPECT_EQ(GMERR_OK, ret);
            } else {
                cnt++;
                printf("ret : %d, cnt : %d\n", ret, cnt);
                if (cnt > 1)
                    goto LOOP;
            }
        }
    }
LOOP:
    ret = GmcTransCommit(g_conn_sync);
    // EXPECT_EQ(GMERR_OK, ret);

    for (conn_index = 1; conn_index < MAX_CONN; conn_index++) {
        ret = GmcTransCommit(conn[conn_index]);
        // EXPECT_EQ(GMERR_OK, ret);

        ret = testGmcDisconnect(conn[conn_index], stmt[conn_index]);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

class TrxLockExtension_monitor_disable : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void TrxLockExtension_monitor_disable::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"trxMonitorEnable=0\"");
    system("sh $TEST_HOME/tools/start.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TrxLockExtension_monitor_disable::TearDownTestCase()
{
    int ret;
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/stop.sh -f");
}

void TrxLockExtension_monitor_disable::SetUp()
{
    int ret = 0;

    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcDropVertexLabel(g_stmt_sync, g_labelName1);
    readJanssonFile("schema_file/TreeModel_006.gmjson", &g_schema1);
    EXPECT_NE((void *)NULL, g_schema1);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schema1, g_label_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(g_schema1);

    AW_CHECK_LOG_BEGIN();
}

void TrxLockExtension_monitor_disable::TearDown()
{
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0}, errorMsg2[errCodeLen] = {0}, errorMsg3[errCodeLen] = {0},
         errorMsg4[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_NO_DATA);
    (void)snprintf(errorMsg3, errCodeLen, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    (void)snprintf(errorMsg4, errCodeLen, "GMERR-%d", GMERR_TRANSACTION_ROLLBACK);
    AW_ADD_ERR_WHITE_LIST(4, errorMsg1, errorMsg2, errorMsg3, errorMsg4);
    AW_CHECK_LOG_END();

    int32_t ret = 0;
    ret = GmcDropVertexLabel(g_stmt_sync, g_labelName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 1024个事务内，各1个表，各写100条数据，预期后面从某个事务开始，均由于锁资源不足失败。
TEST_F(TrxLockExtension_monitor_disable, DML_048_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret = 0, i, conn_index;
    char string1[] = "string1";
    char string2[] = "1234567";
    GmcConnT *conn[MAX_CONN] = {0};
    GmcStmtT *stmt[MAX_CONN] = {0};
    void *label[MAX_CONN] = {0};
    tbl_cnt = 1;
    g_end_num = 100;
    char command_buf[50] = "", command[50] = "";
    char *schema_info = NULL;
    char schema_path[512] = {0};
    char labelName[128] = "T10";

    snprintf(command_buf, sizeof(command_buf), "%d", MAX_CONN);
    snprintf(command, sizeof(command), "sh create_multi_label.sh %s", command_buf);
    ret = system(command);
    EXPECT_EQ(GMERR_OK, WEXITSTATUS(ret));

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    ret = GmcTransStart(g_conn_sync, &config);
    EXPECT_EQ(GMERR_OK, ret);

    // 同步插入数据
    for (i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName1, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        // printf("[INSERT] i = %d\n", i);
        TestGmcInsertVertex(g_stmt_sync, i, 0, string1, g_array_num, g_vector_num);
    }

    for (conn_index = 1; conn_index < MAX_CONN - 5; conn_index++) {  // 20211009 简单表增加个数上限1024
        ret = testGmcConnect(&conn[conn_index], &stmt[conn_index]);
        EXPECT_EQ(GMERR_OK, ret);

        sprintf(schema_path, "./multi_vertexlabel/one_field_schema_%d.gmjson", conn_index);
        readJanssonFile(schema_path, &schema_info);
        EXPECT_NE((void *)NULL, schema_info);
        sprintf(labelName, "T10_%d", conn_index);
        printf("labelName : %s\n", labelName);

        ret = GmcCreateVertexLabel(stmt[conn_index], schema_info, g_label_config);
        EXPECT_EQ(GMERR_OK, ret);
        free(schema_info);

        config.transMode = GMC_TRANS_USED_IN_CS;
        config.type = GMC_TX_ISOLATION_COMMITTED;
        config.readOnly = false;
        config.trxType = GMC_DEFAULT_TRX;
        ret = GmcTransStart(conn[conn_index], &config);
        EXPECT_EQ(GMERR_OK, ret);

        for (i = g_start_num; i < g_end_num; i++) {
            ret = testGmcPrepareStmtByLabelName(stmt[conn_index], labelName, GMC_OPERATION_INSERT);
            EXPECT_EQ(GMERR_OK, ret);
            uint32_t value = i;
            // printf("[INSERT] i : %d\n", i);
            ret = GmcSetVertexProperty(stmt[conn_index], "F0", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt[conn_index]);
            EXPECT_EQ(true, ret == GMERR_OK || ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_TRANSACTION_ROLLBACK);

            if (ret == GMERR_OK) {
                ret = GmcGetStmtAttr(stmt[conn_index], GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
                EXPECT_EQ(GMERR_OK, ret);
                EXPECT_EQ(1, affectRows);
            } else if (ret == GMERR_TRANSACTION_ROLLBACK) {
                ret = testGmcGetLastError(NULL);
                EXPECT_EQ(GMERR_OK, ret);

                ret = GmcTransRollBack(conn[conn_index]);
                EXPECT_EQ(GMERR_OK, ret);
            } else {
                printf("conn_index : %d, i : %d, ret : %d\n", conn_index, i, ret);
            }
        }
    }

    ret = GmcTransCommit(g_conn_sync);
    EXPECT_EQ(GMERR_OK, ret);

    for (conn_index = 1; conn_index < MAX_CONN - 5; conn_index++) {
        ret = GmcTransCommit(conn[conn_index]);
        EXPECT_EQ(GMERR_OK, ret);

        sprintf(labelName, "T10_%d", conn_index);
        // printf("33333333333labelName : %s\n", labelName);
        ret = GmcDropVertexLabel(stmt[conn_index], labelName);
        EXPECT_EQ(GMERR_OK, ret);

        ret = testGmcDisconnect(conn[conn_index], stmt[conn_index]);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

// 1:1建边场景：1个事务内，2个表，各写100条数据，自动建边，读边，查看视图，提交事务后，再次查看视图
TEST_F(TrxLockExtension_func, DML_048_015)
{
    int32_t ret = 0, i, userDataIdx = 0;
    char string1[] = "string1";
    char string2[] = "1234567";
    g_end_num = 100;
    char lock_cnt[50] = "", used_lock_cnt[50] = "", free_lock_cnt[50] = "", bucket_lock_cnt[50] = "",
         pool_lock_cnt[50] = "", reserve_lock_cnt[50] = "";

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    ret = GmcTransStart(g_conn_sync, &config);
    EXPECT_EQ(GMERR_OK, ret);

    // 表1同步插入数据
    for (i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName1, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcInsertVertex(g_stmt_sync, i, 0, string1, g_array_num, g_vector_num);
    }
    // 表2同步插入数据
    for (i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync2, g_labelName2, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcInsertVertex(g_stmt_sync2, i, 0, string1, g_array_num, g_vector_num);
    }

    // 查看视图
    char const *view_name = "V\\$STORAGE_LOCK_OVERVIEW";
    snprintf(lock_cnt, sizeof(lock_cnt), "LOCK_CNT: %d", LOCK_CNT);
    snprintf(used_lock_cnt, sizeof(used_lock_cnt), "USED_LOCK_CNT: %d", 303);
    memset(g_command, 0, sizeof(g_command));
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer,

        view_name);
    system(g_command);
    ret = executeCommand(g_command, lock_cnt, used_lock_cnt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcTransCommit(g_conn_sync);
    EXPECT_EQ(GMERR_OK, ret);

    // 查看视图
    snprintf(lock_cnt, sizeof(lock_cnt), "LOCK_CNT: %d", LOCK_CNT);
    snprintf(used_lock_cnt, sizeof(used_lock_cnt), "USED_LOCK_CNT: %d", 0);
    memset(g_command, 0, sizeof(g_command));
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer,

        view_name);
    system(g_command);
    ret = executeCommand(g_command, lock_cnt, used_lock_cnt);
    EXPECT_EQ(GMERR_OK, ret);

    // 读取数据
    TestGmcDirectFetchVertex(g_stmt_sync, 0, 0, string1, g_start_num, g_end_num, g_array_num, g_vector_num,
        g_labelName1, g_lableName_PK1, true);
    TestGmcDirectFetchVertex(g_stmt_sync2, 0, 0, string1, g_start_num, g_end_num, g_array_num, g_vector_num,
        g_labelName2, g_lableName_PK1, true);
}

// 2个表，各写1条数据，开启事务，手动建10000条边，读边，查看视图，提交事务后，再次查看视图
TEST_F(TrxLockExtension_func, DML_048_016)
{
    int32_t ret = 0, i, userDataIdx = 0;
    char string1[] = "string1";
    char string2[] = "1234567";
    void *edgelabel = 0;
    g_end_num = 1;
    char lock_cnt[50] = "", used_lock_cnt[50] = "", free_lock_cnt[50] = "", bucket_lock_cnt[50] = "",
         pool_lock_cnt[50] = "";
    // 表1同步插入数据
    for (i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName1, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcInsertVertex(g_stmt_sync, i, 0, string1, g_array_num, g_vector_num);
    }
    // 表2同步插入数据
    for (i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync2, g_labelName2, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcInsertVertex(g_stmt_sync2, i, 0, string1, g_array_num, g_vector_num);
    }

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    ret = GmcTransStart(g_conn_sync, &config);
    EXPECT_EQ(GMERR_OK, ret);

    int64_t pk = 0;
    for (i = 0; i < 1000; i++) {
        ret = GmcOpenEdgeLabelByName(g_stmt_sync, g_edgeLabelName, &edgelabel);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetEdgeSrcVertexIndexName(g_stmt_sync, g_lableName_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetEdgeSrcVertexIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetEdgeDstVertexIndexName(g_stmt_sync, g_lableName_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetEdgeDstVertexIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &pk, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcInsertEdge(g_stmt_sync, edgelabel);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcCloseEdgeLabel(g_stmt_sync, edgelabel);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 查看视图
    char const *view_name = "V\\$STORAGE_LOCK_OVERVIEW";
    snprintf(lock_cnt, sizeof(lock_cnt), "LOCK_CNT: %d", LOCK_CNT);
    snprintf(used_lock_cnt, sizeof(used_lock_cnt), "USED_LOCK_CNT: %d", 1006);
    memset(g_command, 0, sizeof(g_command));
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer,

        view_name);
    system(g_command);
    ret = executeCommand(g_command, lock_cnt, used_lock_cnt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcTransCommit(g_conn_sync);
    EXPECT_EQ(GMERR_OK, ret);

    // 查看视图
    snprintf(lock_cnt, sizeof(lock_cnt), "LOCK_CNT: %d", LOCK_CNT);
    snprintf(used_lock_cnt, sizeof(used_lock_cnt), "USED_LOCK_CNT: %d", 0);
    memset(g_command, 0, sizeof(g_command));
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer,

        view_name);
    system(g_command);
    ret = executeCommand(g_command, lock_cnt, used_lock_cnt);
    EXPECT_EQ(GMERR_OK, ret);

    // 读取数据
    TestGmcDirectFetchVertex(g_stmt_sync, 0, 0, string1, g_start_num, g_end_num, g_array_num, g_vector_num,
        g_labelName1, g_lableName_PK1, true);
    TestGmcDirectFetchVertex(g_stmt_sync2, 0, 0, string1, g_start_num, g_end_num, g_array_num, g_vector_num,
        g_labelName2, g_lableName_PK1, true);
}

// 1个事务内，1个表，批量写入大数据量
TEST_F(TrxLockExtension_func, DML_048_017)
{
    int32_t ret = 0, i, index;
    char string1[] = "string1";
    char string2[] = "1234567";
    tbl_cnt = 1;
    g_end_num = 1024;

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    ret = GmcTransStart(g_conn_sync, &config);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync5, g_labelName5, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (index = 0; index < 10; index++) {
        // 同步插入数据
        GmcBatchOptionT batchOption;
        ret = GmcBatchOptionInit(&batchOption);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
        EXPECT_EQ(GMERR_OK, ret);
        GmcBatchT *batch;
        GmcBatchRetT batchRet;
        ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
        ASSERT_EQ(GMERR_OK, ret);
        for (i = g_end_num * index; i < g_end_num * index + g_end_num; i++) {
            printf("[INSERT] i = %d\n", i);
            uint32_t value = i;
            ret = GmcSetVertexProperty(g_stmt_sync5, "F0", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcBatchAddDML(batch, g_stmt_sync5);
            EXPECT_EQ(GMERR_OK, ret);
        }
        unsigned int totalNum = 0;
        unsigned int successNum = 0;
        ret = GmcBatchExecute(batch, &batchRet);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(g_end_num, totalNum);
        ASSERT_EQ(g_end_num, successNum);
        GmcBatchDestroy(batch);
    }

    ret = GmcTransCommit(g_conn_sync);
    EXPECT_EQ(GMERR_OK, ret);

    char lock_cnt[50] = "", used_lock_cnt[50] = "", free_lock_cnt[50] = "", bucket_lock_cnt[50] = "",
         pool_lock_cnt[50] = "", reserve_lock_cnt[50] = "";
    snprintf(lock_cnt, sizeof(lock_cnt), "LOCK_CNT: %d", LOCK_CNT);
    snprintf(used_lock_cnt, sizeof(used_lock_cnt), "USED_LOCK_CNT: %d", 0);

    char const *view_name = "V\\$STORAGE_LOCK_OVERVIEW";
    memset(g_command, 0, sizeof(g_command));
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer,

        view_name);
    system(g_command);
    ret = executeCommand(g_command, lock_cnt, used_lock_cnt);
    EXPECT_EQ(GMERR_OK, ret);

    // char const *g_filePath="./vertexdata";
    // snprintf(g_command, MAX_CMD_SIZE, "%s/gmexport -c vdata -t %s -f %s  -s %s", g_toolPath,
    // g_labelName5, g_filePath,      g_connServer); system(g_command);
}

// gmimport导入数据，使用隐式事务
TEST_F(TrxLockExtension_func, DML_048_018)
{
    int32_t ret = 0, i, index;
    char string1[] = "string1";
    char string2[] = "1234567";

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    ret = GmcTransStart(g_conn_sync, &config);
    EXPECT_EQ(GMERR_OK, ret);

    char const *g_filePath = "./vertexdata/T10.vertexdata";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vdata -f %s  -s %s", g_toolPath, g_filePath,

        g_connServer);
    system(g_command);

    ret = GmcTransCommit(g_conn_sync);
    EXPECT_EQ(GMERR_OK, ret);
}

void *thread_trx_lock10(void *args)
{
    int ret, i;
    int id = *((int *)args);
    void *label = 0;
    GmcConnT *conn = 0;
    GmcStmtT *stmt = 0;
    int affectRows;
    unsigned int len;
    char string1[] = "string1";
    char string2[] = "1234567";
    int start_num = 0, end_num = 0;

    start_num = 0;
    end_num = ROW_MUST_UPGRADE_CNT;

    // 创建同步连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    ret = GmcTransStart(conn, &config);
    EXPECT_EQ(GMERR_OK, ret);

    // 同步插入数据
    for (i = end_num * id; i < end_num * id + end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_labelName1, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        // printf("[TEST_T0 INSERT] id = %d, i : %d\n", id, i);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_PK(root, i);
        TestGmcNodeSetPropertyByName_R(root, i, 0, string1);

        uint8_t f21_value = i & 0xF;
        ret = GmcNodeSetPropertyByName(root, (char *)"F21", GMC_DATATYPE_PARTITION, &f21_value, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);

        TestGmcNodeSetPropertyByName_P(t1, i, 0, string1);
        // 插入array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < g_array_num; j++) {
            TestGmcNodeSetPropertyByName_A(t2, i, 0, string1);
            GmcNodeGetNextElement(t2, &t2);
        }
        // 插入vector节点
        for (uint32_t j = 0; j < g_vector_num; j++) {
            ret = GmcNodeAppendElement(t3, &t3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_V(t3, i, 0, string1);
        }
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_LOCK_NOT_AVAILABLE) {
            printf("ret : %d\n", ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        } else if (ret == GMERR_TRANSACTION_ROLLBACK) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcTransRollBack(conn);
            EXPECT_EQ(GMERR_OK, ret);
        } else if (ret == GMERR_OK) {
            ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(1, affectRows);
        } else {
            EXPECT_EQ(GMERR_OK, ret);
        }
    }

    // 同步更新数据
    for (i = end_num * id; i < end_num * id + end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_labelName1, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        // printf("[TEST_T0 UPDATE] id = %d, i = %d\n", id, i);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);
        // TestGmcNodeSetPropertyByName_PK(root, i + g_end_num * i);
        TestGmcNodeSetPropertyByName_R(root, i, 1, string2);
        TestGmcNodeSetPropertyByName_P(t1, i, 1, string2);
        // 插入array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < g_array_num; j++) {
            TestGmcNodeSetPropertyByName_A(t2, i, 1, string2);
            GmcNodeGetNextElement(t2, &t2);
        }
        // 插入vector节点
        for (uint32_t j = 0; j < g_vector_num; j++) {
            ret = GmcNodeGetElementByIndex(t3, j, &t3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_V(t3, i, 1, string2);
        }
        ret = GmcSetIndexKeyName(stmt, g_lableName_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_LOCK_NOT_AVAILABLE) {
            printf("ret : %d\n", ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        } else if (ret == GMERR_OK) {
            ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
            EXPECT_EQ(GMERR_OK, ret);
            // EXPECT_EQ(1, affectRows);
        } else {
            if (ret != GMERR_OK) {
                ret = testGmcGetLastError();
            }
            EXPECT_EQ(GMERR_OK, ret);
        }
    }

    // 同步删除数据
    for (i = end_num * id; i < end_num * id + end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_labelName1, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        // printf("[TEST_T0 DELETE] id = %d, i = %d\n", id, i);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, g_lableName_PK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(1, affectRows);
    }
    ret = GmcTransCommit(conn);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

void *thread_trx_lock11(void *args)
{
    int ret, i;
    int id = *((int *)args);
    void *label = 0;
    GmcConnT *conn = 0;
    GmcStmtT *stmt = 0;
    int affectRows;
    unsigned int len;
    int start_num = 0, end_num = 0, label_index;
    char labelName[128] = "T10";

    start_num = 0;
    end_num = ROW_MUST_UPGRADE_CNT;

    // 创建同步连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    ret = GmcTransStart(conn, &config);
    EXPECT_EQ(GMERR_OK, ret);

    // 同步插入数据
    sprintf(labelName, "T10_%d", id);

    for (i = end_num * id; i < end_num * id + end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t value = i;
        // printf("[T10 INSERT] id = %d, i : %d\n", id, i);
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_LOCK_NOT_AVAILABLE) {
            printf("ret : %d\n", ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        } else if (ret == GMERR_OK) {
            ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(1, affectRows);
        } else {
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    for (i = end_num * id; i < end_num * id + end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        // printf("[T10 UPDATE] id = %d, i : %d\n", id, i);
        uint32_t value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        // 20211124 不支持更新主键
        // ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        // EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, g_lableName_PK5);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_LOCK_NOT_AVAILABLE) {
            printf("ret : %d\n", ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        } else if (ret == GMERR_OK) {
            ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
            EXPECT_EQ(GMERR_OK, ret);
            // EXPECT_EQ(1, affectRows);
        } else {
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    for (i = end_num * id; i < end_num * id + end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        // printf("[T10 DELETE] id = %d, i : %d\n", id, i);
        uint32_t value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, g_lableName_PK5);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_LOCK_NOT_AVAILABLE) {
            printf("ret : %d\n", ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        } else if (ret == GMERR_OK) {
            ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
            EXPECT_EQ(GMERR_OK, ret);
            // EXPECT_EQ(1, affectRows);
        } else {
            EXPECT_EQ(GMERR_OK, ret);
        }
    }

    ret = GmcTransCommit(conn);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

// 并发随机测试，结束后，验证资源边界
// 连接并发，每个线程开启事务，进行DML操作，操作不同表、部分线程同表，同数据。随机commit或者rollback。如此每个线程执行1000个事务循环。
// 结束后，再验证资源没有泄露。
TEST_F(TrxLockExtension_func, DML_048_019)
{
    int32_t ret = 0, i;
    pthread_t thr_arr[1000];
    void *thr_ret[1000];
    int index[1000] = {0};
    int thr_num = 3;

    char command_buf[50] = "", command[50] = "";
    char *schema_info = NULL;
    char schema_path[512] = {0};
    char labelName[128] = "T10";
    tbl_cnt = 1;
    g_thread_wait = 0;

    snprintf(command_buf, sizeof(command_buf), "%d", thr_num);
    snprintf(command, sizeof(command), "sh create_multi_label.sh %s", command_buf);
    ret = system(command);
    EXPECT_EQ(GMERR_OK, WEXITSTATUS(ret));

    for (i = 0; i < thr_num; i++) {
        sprintf(schema_path, "./multi_vertexlabel/one_field_schema_%d.gmjson", i);
        readJanssonFile(schema_path, &schema_info);
        EXPECT_NE((void *)NULL, schema_info);
        sprintf(labelName, "T10_%d", i);
        // printf("1111111111labelName : %s\n", labelName);

        ret = GmcCreateVertexLabel(g_stmt_sync, schema_info, g_label_config);
        EXPECT_EQ(GMERR_OK, ret);
        free(schema_info);
    }

    for (i = 0; i < thr_num; i++) {
        index[i] = i;
        pthread_create(&thr_arr[i], NULL, thread_trx_lock10, &index[i]);
        pthread_create(&thr_arr[i + thr_num], NULL, thread_trx_lock11, &index[i]);
    }

    for (i = 0; i < thr_num; i++) {
        pthread_join(thr_arr[i], NULL);
        pthread_join(thr_arr[i + thr_num], NULL);
    }

    // 查看视图
    char lock_cnt[50] = "", used_lock_cnt[50] = "", free_lock_cnt[50] = "", bucket_lock_cnt[50] = "",
         pool_lock_cnt[50] = "", reserve_lock_cnt[50] = "";
    snprintf(lock_cnt, sizeof(lock_cnt), "LOCK_CNT: %d", LOCK_CNT);
    snprintf(used_lock_cnt, sizeof(used_lock_cnt), "USED_LOCK_CNT: %d", 0);

    char const *view_name = "V\\$STORAGE_LOCK_OVERVIEW";
    memset(g_command, 0, sizeof(g_command));
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer,

        view_name);
    system(g_command);
    ret = executeCommand(g_command, lock_cnt, used_lock_cnt);
    EXPECT_EQ(GMERR_OK, ret);

    for (i = 0; i < thr_num; i++) {
        sprintf(labelName, "T10_%d", i);
        // printf("33333333333labelName : %s\n", labelName);
        ret = GmcDropVertexLabel(g_stmt_sync, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

void *thread_trx_lock12(void *args)
{
    int ret, i;
    int id = *((int *)args);
    void *label = 0;
    GmcConnT *conn = 0;
    GmcStmtT *stmt = 0;
    int affectRows;
    unsigned int len;
    char string1[] = "string1";
    char string2[] = "1234567";
    int start_num = 0, end_num = 0;

    start_num = 0;
    end_num = ROW_MUST_UPGRADE_CNT;

    // 创建同步连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    while (g_thread_wait < 1) {
        usleep(1000000);
    }

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    ret = GmcTransStart(conn, &config);
    EXPECT_EQ(GMERR_OK, ret);

    // 同步插入数据
    for (i = end_num * (id + 1); i < end_num * (id + 1) + end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_labelName1, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        printf("[trx2 INSERT] i : %d\n", i);
        TestGmcInsertVertex(stmt, i, 0, string1, g_array_num, g_vector_num);
    }

    pthread_mutex_lock(&g_threadLock);
    g_thread_wait++;
    pthread_mutex_unlock(&g_threadLock);

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

void *thread_trx_lock13(void *args)
{
    int ret, i;
    int id = *((int *)args);
    void *label = 0;
    GmcConnT *conn = 0;
    GmcStmtT *stmt = 0;
    int affectRows;
    unsigned int len;
    char string1[] = "string1";
    char string2[] = "1234567";
    int start_num = 0, end_num = 0;

    start_num = 0;
    end_num = 300;

    // 创建同步连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    ret = GmcTransStart(conn, &config);
    EXPECT_EQ(GMERR_OK, ret);

    // 同步插入数据
    for (i = end_num * id; i < end_num * id + end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_labelName1, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        printf("[trx1 INSERT] i : %d\n", i);
        TestGmcInsertVertex(stmt, i, 0, string1, g_array_num, g_vector_num);
    }

    pthread_mutex_lock(&g_threadLock);
    g_thread_wait++;
    pthread_mutex_unlock(&g_threadLock);

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    printf("trx1 disconnect\n");
    return NULL;
}

// 事务1插入数据，升级，不提交。
// 事务2插入多条数据，不升级，不提交，直接断链接。
// 结束后，再验证资源没有泄露。
// 事务一异常退出后，事务2仍会30707
TEST_F(TrxLockExtension_func, DML_048_020)
{
    int32_t ret = 0, i;
    pthread_t thr_arr[1000];
    void *thr_ret[1000];
    int index[1000] = {0};
    int thr_num = 1;

    for (i = 0; i < thr_num; i++) {
        index[i] = i;
        pthread_create(&thr_arr[i], NULL, thread_trx_lock12, &index[i]);
        pthread_create(&thr_arr[i + thr_num], NULL, thread_trx_lock13, &index[i]);
    }

    for (i = 0; i < thr_num; i++) {
        pthread_join(thr_arr[i], NULL);
        pthread_join(thr_arr[i + thr_num], NULL);
    }

    char lock_cnt[50] = "", used_lock_cnt[50] = "", free_lock_cnt[50] = "", bucket_lock_cnt[50] = "",
         pool_lock_cnt[50] = "", reserve_lock_cnt[50] = "";
    snprintf(lock_cnt, sizeof(lock_cnt), "LOCK_CNT: %d", LOCK_CNT);
    snprintf(used_lock_cnt, sizeof(used_lock_cnt), "USED_LOCK_CNT: %d", 0);

    char const *view_name = "V\\$STORAGE_LOCK_OVERVIEW";
    memset(g_command, 0, sizeof(g_command));
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer,

        view_name);
    system(g_command);
    ret = executeCommand(g_command, lock_cnt, used_lock_cnt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 循环查看视图
TEST_F(TrxLockExtension_func, DML_048_021)
{
    int32_t ret = 0, i, userDataIdx = 0;
    // char string1[] = "qwertyuiopasdfghjklzxcvbnm123456789!@#$%^&*()_+-=`~{}[]|:\';,<    .>/? ";
    char string1[] = "string1";
    char string2[] = "1234567";
    tbl_cnt = 1;
    g_end_num = ROW_NOT_UPGRADE_MAX_CNT;
    char lock_cnt[50] = "", used_lock_cnt[50] = "", free_lock_cnt[50] = "", bucket_lock_cnt[50] = "",
         pool_lock_cnt[50] = "", reserve_lock_cnt[50] = "";

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    ret = GmcTransStart(g_conn_sync, &config);
    EXPECT_EQ(GMERR_OK, ret);

    // 同步插入数据
    for (i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName1, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        // printf("[INSERT] i = %d\n", i);
        TestGmcInsertVertex(g_stmt_sync, i, 0, string1, g_array_num, g_vector_num);
    }

    for (i = 0; i < 100; i++) {
        char const *view_name = "V\\$STORAGE_LOCK_OVERVIEW";
        snprintf(lock_cnt, sizeof(lock_cnt), "LOCK_CNT: %d", LOCK_CNT);
        snprintf(used_lock_cnt, sizeof(used_lock_cnt), "USED_LOCK_CNT: %d", 2850);
        memset(g_command, 0, sizeof(g_command));
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer,

            view_name);
        // system(g_command);
        printf("%s\n", g_command);
        ret = executeCommand(g_command, lock_cnt, used_lock_cnt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcTransCommit(g_conn_sync);
    EXPECT_EQ(GMERR_OK, ret);
}

// 视图参数异常
TEST_F(TrxLockExtension_func, DML_048_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg[errCodeLen] = {0};
    (void)snprintf(errorMsg, errCodeLen, "GMERR-%d", GMERR_DATATYPE_MISMATCH);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    int32_t ret = 0, i;
    char const *view_name = "V\\$STORAGE_LOCK_OVERVIEW";
    memset(g_command, 0, sizeof(g_command));
    char *userName = (char *)"";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, userName, g_connServer,

        view_name);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "must input 1 parameter(s).");
    EXPECT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    char *connServer = (char *)"";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, connServer,

        view_name);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "must input 1 parameter(s).");
    EXPECT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    char *passwd = (char *)"";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer, passwd,

        view_name);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "must input 1 parameter(s).");
    EXPECT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    char *t_view_name = (char *)"";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer,

        t_view_name);
    printf("%s\n", g_command);
    ret = executeCommand(g_command, "must input 1 parameter(s).");
    EXPECT_EQ(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s  -q %s -f %s", g_toolPath, g_connServer,

        view_name, "LOCK_CNT");
    printf("%s\n", g_command);
    system(g_command);
    ret = executeCommand(g_command, "[WARN] sysview exec scan view unsucc");
    EXPECT_EQ(GMERR_OK, ret);
}
