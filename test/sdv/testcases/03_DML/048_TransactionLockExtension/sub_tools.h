//
// Created by w00495442 on 2020/12/11.
//
extern "C" {
}

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"

#define MAX_CONN MAX_CONN_SIZE
#define MAX_NAME_LENGTH 128

typedef enum EnumOpTypeNum {
    OpTypeNum_1 = 1,  //只有1种dml类型，申请内存时只需申请1个g_data_num的大小
    OpTypeNum_2,      //有2种dml类型
    OpTypeNum_3,
} OpTypeNumE;

GmcConnT *g_conn_sync = NULL, *g_conn_sub = NULL;
GmcStmtT *g_stmt_sync = NULL, *g_stmt_sub = NULL;
char *g_sub_info_insert = NULL, *g_sub_info_delete = NULL;
int affectRows;
unsigned int len;
int g_data_num = 100;  // 10000
pthread_mutex_t g_threadLock;

using namespace std;

void test_close_and_drop_label(GmcStmtT *stmt, void *label, char *labelName)
{
    int ret;
    if (label) {
        label = 0;
    }
    ret = GmcDropVertexLabel(stmt, labelName);
    if (!(ret == GMERR_OK || ret == GMERR_UNDEFINED_TABLE)) {
        printf("GmcDropVertexLabel ret :%d\r\n", ret);
    }
    EXPECT_EQ(true, ret == GMERR_OK || ret == GMERR_UNDEFINED_TABLE);
}

void test_scan_7(GmcStmtT *stmt, void *label, int start_num, int end_num)
{
    int ret;
    ret = GmcSetIndexKeyName(stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish;
    int cnt = 0, id, i;

    for (id = start_num; id < end_num + 1; id++) {
        uint8_t value_u8 = id;
        uint16_t value_u16 = id;
        uint32_t value_u32 = id;
        uint64_t value_u64 = id;
        uint8_t fixed[17] = {0};
        for (i = 0; i < 16; i++) {
            fixed[i] = '0' + id;
        }

        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        ret = queryPropertyAndCompare(stmt, "nhp_group_flag", GMC_DATATYPE_UINT8, &value_u8);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &value_u16);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "primary_label", GMC_DATATYPE_UINT32, &value_u32);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "attribute_id", GMC_DATATYPE_UINT32, &value_u32);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "nhp_group_id", GMC_DATATYPE_UINT32, &value_u32);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "path_flags", GMC_DATATYPE_UINT32, &value_u32);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "flags", GMC_DATATYPE_UINT32, &value_u32);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "status_high_prio", GMC_DATATYPE_UINT8, &value_u8);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "status_normal_prio", GMC_DATATYPE_UINT8, &value_u8);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "errcode_high_prio", GMC_DATATYPE_UINT8, &value_u8);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "errcode_normal_prio", GMC_DATATYPE_UINT8, &value_u8);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "svc_ctx_high_prio", GMC_DATATYPE_FIXED, fixed);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "svc_ctx_normal_prio", GMC_DATATYPE_FIXED, fixed);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "app_source_id", GMC_DATATYPE_UINT32, &value_u32);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "table_smooth_id", GMC_DATATYPE_UINT32, &value_u32);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &value_u64);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "app_version", GMC_DATATYPE_UINT32, &value_u32);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "trace", GMC_DATATYPE_UINT64, &value_u64);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "route_flags", GMC_DATATYPE_UINT16, &value_u16);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "reserved", GMC_DATATYPE_UINT16, &value_u16);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "time_stamp_create", GMC_DATATYPE_TIME, &value_u64);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "time_stamp_smooth", GMC_DATATYPE_TIME, &value_u64);
        EXPECT_EQ(GMERR_OK, ret);
        cnt++;
    }
    GmcResetStmt(stmt);
    EXPECT_EQ(end_num - start_num, cnt);
    printf("[INFO] #7 scan cnt : %d\r\n", cnt);
}

void test_setVertexProperty_8(GmcStmtT *stmt, int id = 0)
{
    int ret, i;
    uint8_t value_u8 = id;
    uint16_t value_u16 = id;
    uint32_t value_u32 = id;
    uint64_t value_u64 = id;
    uint8_t fixed[16] = {0};
    for (i = 0; i < 16; i++) {
        fixed[i] = id;
    }

    //主键字段
    ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "nhp_number", GMC_DATATYPE_UINT16, &value_u16, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "flags", GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "ref_count", GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "new_vrf", GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "is_next_table", GMC_DATATYPE_UINT8, &value_u8, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "nhp_type_high_prio", GMC_DATATYPE_UINT8, &value_u8, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "nhp_type_normal_prio", GMC_DATATYPE_UINT8, &value_u8, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "status_high_prio", GMC_DATATYPE_UINT8, &value_u8, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "status_normal_prio", GMC_DATATYPE_UINT8, &value_u8, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "errcode_high_prio", GMC_DATATYPE_UINT8, &value_u8, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "errcode_normal_prio", GMC_DATATYPE_UINT8, &value_u8, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "table_smooth_id", GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &value_u64, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "is_meth_local", GMC_DATATYPE_UINT8, &value_u8, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
}

void test_scan_8(GmcStmtT *stmt, void *label, int start_num, int end_num)
{
    int ret;
    ret = GmcSetIndexKeyName(stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish;
    int cnt = 0, id, i;

    for (id = start_num; id < end_num + 1; id++) {
        uint8_t value_u8 = id;
        uint16_t value_u16 = id;
        uint32_t value_u32 = id;
        uint64_t value_u64 = id;
        uint8_t fixed[16] = {0};
        for (i = 0; i < 16; i++) {
            fixed[i] = id;
        }

        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        ret = queryPropertyAndCompare(stmt, "vrf_index", GMC_DATATYPE_UINT32, &value_u32);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "nhp_number", GMC_DATATYPE_UINT16, &value_u16);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "flags", GMC_DATATYPE_UINT32, &value_u32);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "ref_count", GMC_DATATYPE_UINT32, &value_u32);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "new_vrf", GMC_DATATYPE_UINT32, &value_u32);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "is_next_table", GMC_DATATYPE_UINT8, &value_u8);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "nhp_type_high_prio", GMC_DATATYPE_UINT8, &value_u8);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "nhp_type_normal_prio", GMC_DATATYPE_UINT8, &value_u8);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "status_high_prio", GMC_DATATYPE_UINT8, &value_u8);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "status_normal_prio", GMC_DATATYPE_UINT8, &value_u8);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "errcode_high_prio", GMC_DATATYPE_UINT8, &value_u8);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "errcode_normal_prio", GMC_DATATYPE_UINT8, &value_u8);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "app_source_id", GMC_DATATYPE_UINT32, &value_u32);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "table_smooth_id", GMC_DATATYPE_UINT32, &value_u32);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &value_u64);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "app_version", GMC_DATATYPE_UINT32, &value_u32);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "is_meth_local", GMC_DATATYPE_UINT8, &value_u8);
        EXPECT_EQ(GMERR_OK, ret);
        cnt++;
    }
    GmcResetStmt(stmt);
    EXPECT_EQ(end_num - start_num, cnt);
    printf("[INFO] #8 scan cnt : %d\r\n", cnt);
}

void test_setVertexProperty_9(GmcStmtT *stmt, int id = 0)
{
    int ret, i;
    uint8_t value_u8 = id;
    uint16_t value_u16 = id;
    uint32_t value_u32 = id;
    uint64_t value_u64 = id;
    uint8_t fixed[16] = {0};
    for (i = 0; i < 16; i++) {
        fixed[i] = id;
    }

    //主键字段
    ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    //主键字段
    ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    //主键字段
    ret = GmcSetVertexProperty(stmt, "primary_nhp_id", GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    //主键字段
    ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    //主键字段
    ret = GmcSetVertexProperty(stmt, "backup_nhp_id", GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    //主键字段
    ret = GmcSetVertexProperty(stmt, "backup_label", GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    //主键字段
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "flags", GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "group_smooth_id", GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
}

void test_scan_9(GmcStmtT *stmt, void *label, int start_num, int end_num)
{
    int ret;
    ret = GmcSetIndexKeyName(stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish;
    int cnt = 0, id, i;

    for (id = start_num; id < end_num + 1; id++) {
        uint8_t value_u8 = id;
        uint16_t value_u16 = id;
        uint32_t value_u32 = id;
        uint64_t value_u64 = id;
        uint8_t fixed[16] = {0};
        for (i = 0; i < 16; i++) {
            fixed[i] = id;
        }

        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        ret = queryPropertyAndCompare(stmt, "vrf_index", GMC_DATATYPE_UINT32, &value_u32);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "flags", GMC_DATATYPE_UINT32, &value_u32);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "app_source_id", GMC_DATATYPE_UINT32, &value_u32);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "group_smooth_id", GMC_DATATYPE_UINT32, &value_u32);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "app_version", GMC_DATATYPE_UINT32, &value_u32);
        EXPECT_EQ(GMERR_OK, ret);
        cnt++;
    }
    GmcResetStmt(stmt);
    EXPECT_EQ(end_num - start_num, cnt);
    printf("[INFO] #9 scan cnt : %d\r\n", cnt);
}

void test_setVertexProperty_10(GmcStmtT *stmt, int id = 0)
{
    int ret, i;
    uint8_t value_u8 = id;
    uint16_t value_u16 = id;
    uint32_t value_u32 = id;
    uint64_t value_u64 = id;
    uint8_t fixed[16] = {0};
    for (i = 0; i < 16; i++) {
        fixed[i] = id;
    }

    //主键字段
    ret = GmcSetVertexProperty(stmt, "nhp_index", GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "origin_nhp", GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "nhp_flag", GMC_DATATYPE_UINT8, &value_u8, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "nhp_num", GMC_DATATYPE_UINT8, &value_u8, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "ref_cnt", GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "flags", GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "iid_flags", GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "table_smooth_id", GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &value_u64, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
}

void test_scan_10(GmcStmtT *stmt, void *label, int start_num, int end_num)
{
    int ret;
    ret = GmcSetIndexKeyName(stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish;
    int cnt = 0, id, i;

    for (id = start_num; id < end_num + 1; id++) {
        uint8_t value_u8 = id;
        uint16_t value_u16 = id;
        uint32_t value_u32 = id;
        uint64_t value_u64 = id;
        uint8_t fixed[16] = {0};
        for (i = 0; i < 16; i++) {
            fixed[i] = id;
        }

        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        ret = queryPropertyAndCompare(stmt, "vr_id", GMC_DATATYPE_UINT32, &value_u32);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "vrf_index", GMC_DATATYPE_UINT32, &value_u32);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "origin_nhp", GMC_DATATYPE_UINT32, &value_u32);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "nhp_flag", GMC_DATATYPE_UINT8, &value_u8);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "nhp_num", GMC_DATATYPE_UINT8, &value_u8);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "ref_cnt", GMC_DATATYPE_UINT32, &value_u32);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "flags", GMC_DATATYPE_UINT32, &value_u32);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "iid_flags", GMC_DATATYPE_UINT32, &value_u32);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "app_source_id", GMC_DATATYPE_UINT32, &value_u32);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "table_smooth_id", GMC_DATATYPE_UINT32, &value_u32);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &value_u64);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "app_version", GMC_DATATYPE_UINT32, &value_u32);
        EXPECT_EQ(GMERR_OK, ret);
        cnt++;
    }
    GmcResetStmt(stmt);
    EXPECT_EQ(end_num - start_num, cnt);
    printf("[INFO] #10 scan cnt : %d\r\n", cnt);
}

void test_setVertexProperty_42(GmcStmtT *stmt, int id = 0)
{
    int ret, i;
    uint8_t value_u8 = id;
    uint16_t value_u16 = id;
    uint32_t value_u32 = id;
    uint64_t value_u64 = id;
    uint8_t fixed[16] = {0};
    for (i = 0; i < 16; i++) {
        fixed[i] = id;
    }

    //主键字段
    ret = GmcSetVertexProperty(stmt, "nhp_index", GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    //主键字段
    ret = GmcSetVertexProperty(stmt, "next_hop", GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    //主键字段
    ret = GmcSetVertexProperty(stmt, "out_if_index", GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "flags", GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "if_type", GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "iid_flags", GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "work_if_index", GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "group_smooth_id", GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &value_u64, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "attr_flag", GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "fwd_if_type", GMC_DATATYPE_UINT16, &value_u16, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "reserved", GMC_DATATYPE_UINT16, &value_u16, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
}

void test_scan_42(GmcStmtT *stmt, void *label, int start_num, int end_num)
{
    int ret;
    ret = GmcSetIndexKeyName(stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish;
    int cnt = 0, id, i;

    for (id = start_num; id < end_num + 1; id++) {
        uint8_t value_u8 = id;
        uint16_t value_u16 = id;
        uint32_t value_u32 = id;
        uint64_t value_u64 = id;
        uint8_t fixed[16] = {0};
        for (i = 0; i < 16; i++) {
            fixed[i] = id;
        }

        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        ret = queryPropertyAndCompare(stmt, "vr_id", GMC_DATATYPE_UINT32, &value_u32);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "vrf_index", GMC_DATATYPE_UINT32, &value_u32);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "flags", GMC_DATATYPE_UINT32, &value_u32);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "if_type", GMC_DATATYPE_UINT32, &value_u32);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "iid_flags", GMC_DATATYPE_UINT32, &value_u32);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "work_if_index", GMC_DATATYPE_UINT32, &value_u32);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "app_source_id", GMC_DATATYPE_UINT32, &value_u32);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "group_smooth_id", GMC_DATATYPE_UINT32, &value_u32);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &value_u64);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "app_version", GMC_DATATYPE_UINT32, &value_u32);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "attr_flag", GMC_DATATYPE_UINT32, &value_u32);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "fwd_if_type", GMC_DATATYPE_UINT16, &value_u16);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "reserved", GMC_DATATYPE_UINT16, &value_u16);
        EXPECT_EQ(GMERR_OK, ret);
        cnt++;
    }
    GmcResetStmt(stmt);
    EXPECT_EQ(end_num - start_num, cnt);
    printf("[INFO] #42 scan cnt : %d\r\n", cnt);
}

void sn_callback_not_cmp(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret;
    int pk, i;
    SnUserDataT *user_data = (SnUserDataT *)userData;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
        }
    }
}
