[{"version": "2.0", "type": "record", "name": "Localhash_fixed", "fields": [{"name": "F0", "type": "uint8"}, {"name": "F1", "type": "int8"}, {"name": "F2", "type": "int16"}, {"name": "F3", "type": "uint16"}, {"name": "F4", "type": "int32"}, {"name": "F5", "type": "uint32"}, {"name": "F6", "type": "int64"}, {"name": "F7", "type": "uint64"}, {"name": "F8", "type": "uchar"}, {"name": "F9", "type": "char"}, {"name": "F10", "type": "fixed", "size": 12}, {"name": "F11", "type": "time", "nullable": true}, {"name": "F12", "type": "uint8"}, {"name": "F13", "type": "fixed", "size": 9}], "keys": [{"name": "primary_pk", "node": "Localhash_fixed", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"name": "localhash_key", "node": "Localhash_fixed", "fields": ["F13"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": false}, "filter": {"operator_type": "and", "conditions": [{"property": "F13", "compare_type": "unequal", "value": "0x112233445566778899"}, {"property": "F13", "compare_type": "unequal", "value": "fffffffff"}]}}]}]