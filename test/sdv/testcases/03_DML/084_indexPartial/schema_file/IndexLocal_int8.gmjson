[{"version": "2.0", "type": "record", "name": "IndexLocal", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uchar", "nullable": true}, {"name": "F2", "type": "char", "nullable": true}, {"name": "F3", "type": "uint8", "nullable": true}, {"name": "F4", "type": "int8", "nullable": true}, {"name": "F5", "type": "uint16", "nullable": true}, {"name": "F6", "type": "int16", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "int32", "nullable": true}, {"name": "F9", "type": "uint64", "nullable": true}, {"name": "F10", "type": "int64", "nullable": true}, {"name": "F11", "type": "fixed", "size": 7, "nullable": true}, {"name": "F12", "type": "time", "nullable": true}], "keys": [{"name": "IndexLocal_pk", "index": {"type": "primary"}, "node": "IndexLocal", "fields": ["F0"], "constraints": {"unique": true}}, {"name": "IndexLocal_local", "index": {"type": "local"}, "node": "IndexLocal", "fields": ["F4"], "constraints": {"unique": false}, "filter": {"operator_type": "and", "conditions": [{"property": "F4", "compare_type": "equal", "value": -1}]}}]}]