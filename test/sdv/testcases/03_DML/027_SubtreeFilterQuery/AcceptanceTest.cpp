#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
extern "C" {
}
#define THREAD_COUNTS 3
GmcConnT *conn;
GmcStmtT *stmt;
bool isNull;
unsigned int posValue;
int affectvalue;
int affectRows = 0;
unsigned int len = 0;
int F0Value = 10, F1Value = 20, F2Value = 30, F3Value = 40;
unsigned int sizeF0, sizeF1, sizeF2, sizeF3, sizeF4;
char *F4Value = (char *)"GMDBV5_TEST";
char *accept_first_label_schema = NULL;
char *accept_second_label_schema = NULL;
#define CONNECT_COUNTS 100
const char *normal_config_json = R"(
    {
        "max_record_count":1000
    }
)";

class AcceptanceTestCases : public testing::Test {
protected:
    static void SetUpTestCase()
    {

        system("sh $TEST_HOME/tools/start.sh");
        readJanssonFile("schema/AcceptanceTestCases_second_schema.gmjson", &accept_second_label_schema);
        ASSERT_NE((void *)NULL, accept_second_label_schema);
        readJanssonFile("schema/AcceptanceTestCases_first_schema.gmjson", &accept_first_label_schema);
        ASSERT_NE((void *)NULL, accept_first_label_schema);
        int ret = 0;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        free(accept_second_label_schema);
        free(accept_first_label_schema);
        int ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void AcceptanceTestCases::SetUp()
{
    int ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void AcceptanceTestCases::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}
void *readThread(void *arg)
{
    GmcStmtT *g_stmt;
    GmcConnT *g_conn;
    void *vertexLabel = NULL;
    int ret1 = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret1);
    /******** 主键读取 ********/
    ret1 = testGmcPrepareStmtByLabelName(g_stmt, "T38", GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret1);
    const char *keyName = "T38_K0";
    ret1 = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &F0Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret1);
    ret1 = GmcSetIndexKeyName(g_stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret1);
    ret1 = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret1);
    bool isFinish = false;
    while (!isFinish) {
        ret1 = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret1);
        if (isFinish == true) {
            break;
        }
        // Get F0
        ret1 = GmcGetVertexPropertySizeByName(g_stmt, "F0", &sizeF0);
        EXPECT_EQ(GMERR_OK, ret1);
        int read_valueF0;
        ret1 = GmcGetVertexPropertyByName(g_stmt, "F0", &read_valueF0, sizeof(int), &isNull);
        EXPECT_EQ(GMERR_OK, ret1);
        printf("[INFO][Thread_0] pk index read F0 value is %d, size is %d \n", read_valueF0, sizeF0);
        EXPECT_EQ(F0Value, read_valueF0);
        EXPECT_EQ(4, sizeF0);

        // get F1
        ret1 = GmcGetVertexPropertySizeByName(g_stmt, "F1", &sizeF1);
        EXPECT_EQ(GMERR_OK, ret1);
        int read_valueF1;
        ret1 = GmcGetVertexPropertyByName(g_stmt, "F1", &read_valueF1, sizeof(int), &isNull);
        EXPECT_EQ(GMERR_OK, ret1);
        printf("[INFO][Thread_0] pk index read F1 value is %d, size is %d \n", read_valueF1, sizeF1);
        EXPECT_EQ(F1Value, read_valueF1);
        EXPECT_EQ(4, sizeF0);
        // Get F2
        ret1 = GmcGetVertexPropertySizeByName(g_stmt, "F2", &sizeF2);
        EXPECT_EQ(GMERR_OK, ret1);
        int read_valueF2;
        ret1 = GmcGetVertexPropertyByName(g_stmt, "F2", &read_valueF2, sizeof(int), &isNull);
        EXPECT_EQ(GMERR_OK, ret1);
        printf("[INFO][Thread_0] pk index read F2 value is %d, size is %d \n", read_valueF2, sizeF2);
        EXPECT_EQ(F2Value, read_valueF2);
        EXPECT_EQ(4, sizeF2);

        // Get F3
        ret1 = GmcGetVertexPropertySizeByName(g_stmt, "F3", &sizeF3);
        EXPECT_EQ(GMERR_OK, ret1);
        int read_valueF3;
        ret1 = GmcGetVertexPropertyByName(g_stmt, "F3", &read_valueF3, sizeof(int), &isNull);
        EXPECT_EQ(GMERR_OK, ret1);
        printf("[INFO][Thread_0] pk index read F3 value is %d, size is %d \n", read_valueF3, sizeF3);
        EXPECT_EQ(F3Value, read_valueF3);
        EXPECT_EQ(4, sizeF3);
        // get F4
        ret1 = GmcGetVertexPropertySizeByName(g_stmt, "F4", &sizeF4);
        EXPECT_EQ(GMERR_OK, ret1);
        char *read_valueF4 = (char *)malloc(sizeF4);
        ret1 = GmcGetVertexPropertyByName(g_stmt, "F4", read_valueF4, sizeF4, &isNull);
        EXPECT_EQ(GMERR_OK, ret1);
        ret1 = strcmp(F4Value, read_valueF4);
        EXPECT_EQ(GMERR_OK, ret1);
        printf("[INFO][Thread_0] pk index read F4 value is %s, size is %d \n", read_valueF4, sizeF4);
        free(read_valueF4);
    }
    int ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return ((void *)0);
}

void *scanThread(void *arg)
{

    GmcStmtT *g_stmt;
    GmcConnT *g_conn;
    void *vertexLabel = NULL;

    int ret1 = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret1);
    /******** hash索引扫描 ********/
    ret1 = testGmcPrepareStmtByLabelName(g_stmt, "T38", GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret1);
    // printf("\n=============== [Thread_%d] hash index scan ===============\n", conn_id);
    bool isFinish;
    ret1 = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &F1Value, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret1);
    ret1 = GmcSetIndexKeyName(g_stmt, (char *)arg);
    EXPECT_EQ(GMERR_OK, ret1);
    ret1 = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret1);
    while (!isFinish) {
        ret1 = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret1);
        if (isFinish == true) {
            break;
        }
        // Get F0
        ret1 = GmcGetVertexPropertySizeByName(g_stmt, "F0", &sizeF0);
        EXPECT_EQ(GMERR_OK, ret1);
        int scan_valueF0;
        ret1 = GmcGetVertexPropertyByName(g_stmt, "F0", &scan_valueF0, sizeof(int), &isNull);
        EXPECT_EQ(GMERR_OK, ret1);
        printf("[INFO][Thread_0] hash index scan F0 value is %d, size is %d \n", scan_valueF0, sizeF0);
        EXPECT_EQ(F0Value, scan_valueF0);
        EXPECT_EQ(4, sizeF0);

        // get F1
        ret1 = GmcGetVertexPropertySizeByName(g_stmt, "F1", &sizeF1);
        EXPECT_EQ(GMERR_OK, ret1);
        int scan_valueF1;
        ret1 = GmcGetVertexPropertyByName(g_stmt, "F1", &scan_valueF1, sizeof(int), &isNull);
        EXPECT_EQ(GMERR_OK, ret1);
        printf("[INFO][Thread_0] hash index scan F1 value is %d, size is %d \n", scan_valueF1, sizeF1);
        EXPECT_EQ(F1Value, scan_valueF1);
        EXPECT_EQ(4, sizeF0);
        // Get F2
        ret1 = GmcGetVertexPropertySizeByName(g_stmt, "F2", &sizeF2);
        EXPECT_EQ(GMERR_OK, ret1);
        int scan_valueF2;
        ret1 = GmcGetVertexPropertyByName(g_stmt, "F2", &scan_valueF2, sizeof(int), &isNull);
        EXPECT_EQ(GMERR_OK, ret1);
        printf("[INFO][Thread_0] hash index scan F2 value is %d, size is %d \n", scan_valueF2, sizeF2);
        EXPECT_EQ(F2Value, scan_valueF2);
        EXPECT_EQ(4, sizeF2);

        // Get F3
        ret1 = GmcGetVertexPropertySizeByName(g_stmt, "F3", &sizeF3);
        EXPECT_EQ(GMERR_OK, ret1);
        int scan_valueF3;
        ret1 = GmcGetVertexPropertyByName(g_stmt, "F3", &scan_valueF3, sizeof(int), &isNull);
        EXPECT_EQ(GMERR_OK, ret1);
        printf("[INFO][Thread_0] hash index scan F3 value is %d, size is %d \n", scan_valueF3, sizeF3);
        EXPECT_EQ(F3Value, scan_valueF3);
        EXPECT_EQ(4, sizeF3);
        // get F4
        ret1 = GmcGetVertexPropertySizeByName(g_stmt, "F4", &sizeF4);
        EXPECT_EQ(GMERR_OK, ret1);
        char *scan_valueF4 = (char *)malloc(sizeF4);
        ret1 = GmcGetVertexPropertyByName(g_stmt, "F4", scan_valueF4, sizeF4, &isNull);
        EXPECT_EQ(GMERR_OK, ret1);
        ret1 = strcmp(F4Value, scan_valueF4);
        EXPECT_EQ(GMERR_OK, ret1);
        printf("[INFO][Thread_0] hash index scan F4 value is %s, size is %d \n", scan_valueF4, sizeF4);
        free(scan_valueF4);
    }
    GmcResetStmt(g_stmt);
    GmcFreeIndexKey(g_stmt);
    // close vertexLabel

    int ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return ((void *)0);
}
void *scanThread_non(void *arg)
{

    GmcStmtT *g_stmt;
    GmcConnT *g_conn;
    void *vertexLabel = NULL;

    int ret1 = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret1);
    /******** hash索引扫描 ********/
    ret1 = testGmcPrepareStmtByLabelName(g_stmt, "T38", GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret1);
    // printf("\n=============== [Thread_%d] hash index scan ===============\n", conn_id);
    bool isFinish = false;
    ret1 = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &F2Value, sizeof(F2Value));
    EXPECT_EQ(GMERR_OK, ret1);
    ret1 = GmcSetIndexKeyName(g_stmt, (char *)arg);
    EXPECT_EQ(GMERR_OK, ret1);
    ret1 = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret1);
    while (!isFinish) {
        ret1 = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret1);
        if (isFinish == true) {
            break;
        }
        // Get F0
        ret1 = GmcGetVertexPropertySizeByName(g_stmt, "F0", &sizeF0);
        EXPECT_EQ(GMERR_OK, ret1);
        int scan_valueF0;
        ret1 = GmcGetVertexPropertyByName(g_stmt, "F0", &scan_valueF0, sizeof(int), &isNull);
        EXPECT_EQ(GMERR_OK, ret1);
        printf("[INFO][Thread_0] hash index scan F0 value is %d, size is %d \n", scan_valueF0, sizeF0);
        EXPECT_EQ(F0Value, scan_valueF0);
        EXPECT_EQ(4, sizeF0);

        // get F1
        ret1 = GmcGetVertexPropertySizeByName(g_stmt, "F1", &sizeF1);
        EXPECT_EQ(GMERR_OK, ret1);
        int scan_valueF1;
        ret1 = GmcGetVertexPropertyByName(g_stmt, "F1", &scan_valueF1, sizeof(int), &isNull);
        EXPECT_EQ(GMERR_OK, ret1);
        printf("[INFO][Thread_0] hash index scan F1 value is %d, size is %d \n", scan_valueF1, sizeF1);
        EXPECT_EQ(F1Value, scan_valueF1);
        EXPECT_EQ(4, sizeF0);
        // Get F2
        ret1 = GmcGetVertexPropertySizeByName(g_stmt, "F2", &sizeF2);
        EXPECT_EQ(GMERR_OK, ret1);
        int scan_valueF2;
        ret1 = GmcGetVertexPropertyByName(g_stmt, "F2", &scan_valueF2, sizeof(int), &isNull);
        EXPECT_EQ(GMERR_OK, ret1);
        printf("[INFO][Thread_0] hash index scan F2 value is %d, size is %d \n", scan_valueF2, sizeF2);
        EXPECT_EQ(F2Value, scan_valueF2);
        EXPECT_EQ(4, sizeF2);

        // Get F3
        ret1 = GmcGetVertexPropertySizeByName(g_stmt, "F3", &sizeF3);
        EXPECT_EQ(GMERR_OK, ret1);
        int scan_valueF3;
        ret1 = GmcGetVertexPropertyByName(g_stmt, "F3", &scan_valueF3, sizeof(int), &isNull);
        EXPECT_EQ(GMERR_OK, ret1);
        printf("[INFO][Thread_0] hash index scan F3 value is %d, size is %d \n", scan_valueF3, sizeF3);
        EXPECT_EQ(F3Value, scan_valueF3);
        EXPECT_EQ(4, sizeF3);
        // get F4
        ret1 = GmcGetVertexPropertySizeByName(g_stmt, "F4", &sizeF4);
        EXPECT_EQ(GMERR_OK, ret1);
        char *scan_valueF4 = (char *)malloc(sizeF4);
        ret1 = GmcGetVertexPropertyByName(g_stmt, "F4", scan_valueF4, sizeF4, &isNull);
        EXPECT_EQ(GMERR_OK, ret1);
        ret1 = strcmp(F4Value, scan_valueF4);
        EXPECT_EQ(GMERR_OK, ret1);
        printf("[INFO][Thread_0] hash index scan F4 value is %s, size is %d \n", scan_valueF4, sizeF4);
        free(scan_valueF4);
    }
    GmcResetStmt(g_stmt);
    GmcFreeIndexKey(g_stmt);
    int ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return ((void *)0);
}
void *writeThread(void *arg)
{

    const char *keyName = "T38_K0";
    GmcStmtT *g_stmt;
    int ret1 = GmcAllocStmt(conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret1);
    ret1 = testGmcPrepareStmtByLabelName(g_stmt, "T38", GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret1);
    //主键/二级索引 同步更新
    ret1 = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &F0Value, sizeof(F0Value));
    EXPECT_EQ(GMERR_OK, ret1);
    ret1 = GmcSetIndexKeyName(g_stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret1);
    //更新F3
    unsigned int updateValue = 100;
    ret1 = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_UINT32, &updateValue, sizeof(updateValue));
    EXPECT_EQ(GMERR_OK, ret1);
    ret1 = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret1);
    // get affect row
    ret1 = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret1);
    EXPECT_EQ(1, affectRows);
    // close vertexLabel

    // ret = testGmcDisconnect(g_conn_write1[countid], g_stmt_write1[countid]);
    // EXPECT_EQ(GMERR_OK, ret);
    return ((void *)0);
}

void *scanThread1(void *arg)
{

    GmcStmtT *g_stmt;
    GmcConnT *g_conn;
    void *vertexLabel = NULL;

    int ret1 = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret1);
    /******** hash索引扫描 ********/
    ret1 = testGmcPrepareStmtByLabelName(g_stmt, "T38", GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret1);
    unsigned int F1Value1 = 20, F0Value1 = 10, F2Value1 = 30, F3Value1 = 100;
    char *F4Value1 = (char *)"GMDBV5_TEST";
    // printf("\n=============== [Thread_%d] hash index scan ===============\n", conn_id);
    bool isFinish = false;
    ret1 = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &F1Value1, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret1);
    const char *keyName = "T38_H0";
    ret1 = GmcSetIndexKeyName(g_stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret1);
    ret1 = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret1);
    while (!isFinish) {
        ret1 = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret1);
        if (isFinish == true) {
            break;
        }
        // Get F0
        ret1 = GmcGetVertexPropertySizeByName(g_stmt, "F0", &sizeF0);
        EXPECT_EQ(GMERR_OK, ret1);
        int scan_valueF0;
        ret1 = GmcGetVertexPropertyByName(g_stmt, "F0", &scan_valueF0, sizeof(int), &isNull);
        EXPECT_EQ(GMERR_OK, ret1);
        printf("[INFO][Thread_0] hash index scan F0 value is %d, size is %d \n", scan_valueF0, sizeF0);
        EXPECT_EQ(F0Value1, scan_valueF0);
        EXPECT_EQ(4, sizeF0);

        // get F1
        ret1 = GmcGetVertexPropertySizeByName(g_stmt, "F1", &sizeF1);
        EXPECT_EQ(GMERR_OK, ret1);
        int scan_valueF1;
        ret1 = GmcGetVertexPropertyByName(g_stmt, "F1", &scan_valueF1, sizeof(int), &isNull);
        EXPECT_EQ(GMERR_OK, ret1);
        printf("[INFO][Thread_0] hash index scan F1 value is %d, size is %d \n", scan_valueF1, sizeF1);
        EXPECT_EQ(F1Value1, scan_valueF1);
        EXPECT_EQ(4, sizeF0);
        // Get F2
        ret1 = GmcGetVertexPropertySizeByName(g_stmt, "F2", &sizeF2);
        EXPECT_EQ(GMERR_OK, ret1);
        int scan_valueF2;
        ret1 = GmcGetVertexPropertyByName(g_stmt, "F2", &scan_valueF2, sizeof(int), &isNull);
        EXPECT_EQ(GMERR_OK, ret1);
        printf("[INFO][Thread_0] hash index scan F2 value is %d, size is %d \n", scan_valueF2, sizeF2);
        EXPECT_EQ(F2Value1, scan_valueF2);
        EXPECT_EQ(4, sizeF2);

        // Get F3
        ret1 = GmcGetVertexPropertySizeByName(g_stmt, "F3", &sizeF3);
        EXPECT_EQ(GMERR_OK, ret1);
        int scan_valueF3;
        ret1 = GmcGetVertexPropertyByName(g_stmt, "F3", &scan_valueF3, sizeof(int), &isNull);
        EXPECT_EQ(GMERR_OK, ret1);
        printf("[INFO][Thread_0] hash index scan F3 value is %d, size is %d \n", scan_valueF3, sizeF3);
        if ((scan_valueF3 == 40) || (scan_valueF3 == 100)) {
            ret1 = 0;
        } else {
            ret1 = 1;
        }
        EXPECT_EQ(GMERR_OK, ret1);
        EXPECT_EQ(4, sizeF3);
        // get F4
        ret1 = GmcGetVertexPropertySizeByName(g_stmt, "F4", &sizeF4);
        EXPECT_EQ(GMERR_OK, ret1);
        char *scan_valueF4 = (char *)malloc(sizeF4);
        ret1 = GmcGetVertexPropertyByName(g_stmt, "F4", scan_valueF4, sizeF4, &isNull);
        EXPECT_EQ(GMERR_OK, ret1);
        ret1 = strcmp(F4Value1, scan_valueF4);
        EXPECT_EQ(GMERR_OK, ret1);
        printf("[INFO][Thread_0] hash index scan F4 value is %s, size is %d \n", scan_valueF4, sizeF4);
        free(scan_valueF4);
    }
    GmcResetStmt(g_stmt);
    GmcFreeIndexKey(g_stmt);

    // close vertexLabel

    int ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return ((void *)0);
}
void *scanThread1_non(void *arg)
{

    GmcStmtT *g_stmt;
    GmcConnT *g_conn;
    void *vertexLabel = NULL;
    int ret1 = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret1);

    /******** hash索引扫描 ********/
    ret1 = testGmcPrepareStmtByLabelName(g_stmt, "T38", GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret1);
    unsigned int F1Value1 = 20, F0Value1 = 10, F2Value1 = 30, F3Value1 = 100;
    char *F4Value1 = (char *)"GMDBV5_TEST";
    // printf("\n=============== [Thread_%d] hash index scan ===============\n", conn_id);
    bool isFinish = false;
    ret1 = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &F2Value1, sizeof(F2Value));
    EXPECT_EQ(GMERR_OK, ret1);
    const char *keyName = "T38_H1";
    ret1 = GmcSetIndexKeyName(g_stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret1);
    ret1 = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret1);
    while (!isFinish) {
        ret1 = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret1);
        if (isFinish == true) {
            break;
        }
        // Get F0
        ret1 = GmcGetVertexPropertySizeByName(g_stmt, "F0", &sizeF0);
        EXPECT_EQ(GMERR_OK, ret1);
        int scan_valueF0;
        ret1 = GmcGetVertexPropertyByName(g_stmt, "F0", &scan_valueF0, sizeof(int), &isNull);
        EXPECT_EQ(GMERR_OK, ret1);
        printf("[INFO][Thread_0] hash index scan F0 value is %d, size is %d \n", scan_valueF0, sizeF0);
        EXPECT_EQ(F0Value1, scan_valueF0);
        EXPECT_EQ(4, sizeF0);

        // get F1
        ret1 = GmcGetVertexPropertySizeByName(g_stmt, "F1", &sizeF1);
        EXPECT_EQ(GMERR_OK, ret1);
        int scan_valueF1;
        ret1 = GmcGetVertexPropertyByName(g_stmt, "F1", &scan_valueF1, sizeof(int), &isNull);
        EXPECT_EQ(GMERR_OK, ret1);
        printf("[INFO][Thread_0] hash index scan F1 value is %d, size is %d \n", scan_valueF1, sizeF1);
        EXPECT_EQ(F1Value1, scan_valueF1);
        EXPECT_EQ(4, sizeF0);
        // Get F2
        ret1 = GmcGetVertexPropertySizeByName(g_stmt, "F2", &sizeF2);
        EXPECT_EQ(GMERR_OK, ret1);
        int scan_valueF2;
        ret1 = GmcGetVertexPropertyByName(g_stmt, "F2", &scan_valueF2, sizeof(int), &isNull);
        EXPECT_EQ(GMERR_OK, ret1);
        printf("[INFO][Thread_0] hash index scan F2 value is %d, size is %d \n", scan_valueF2, sizeF2);
        EXPECT_EQ(F2Value1, scan_valueF2);
        EXPECT_EQ(4, sizeF2);

        // Get F3
        ret1 = GmcGetVertexPropertySizeByName(g_stmt, "F3", &sizeF3);
        EXPECT_EQ(GMERR_OK, ret1);
        int scan_valueF3;
        ret1 = GmcGetVertexPropertyByName(g_stmt, "F3", &scan_valueF3, sizeof(int), &isNull);
        EXPECT_EQ(GMERR_OK, ret1);
        printf("[INFO][Thread_0] hash index scan F3 value is %d, size is %d \n", scan_valueF3, sizeF3);
        if ((scan_valueF3 == 40) || (scan_valueF3 == 100)) {
            ret1 = 0;
        } else {
            ret1 = 1;
        }
        EXPECT_EQ(GMERR_OK, ret1);
        EXPECT_EQ(4, sizeF3);
        // get F4
        ret1 = GmcGetVertexPropertySizeByName(g_stmt, "F4", &sizeF4);
        EXPECT_EQ(GMERR_OK, ret1);
        char *scan_valueF4 = (char *)malloc(sizeF4);
        ret1 = GmcGetVertexPropertyByName(g_stmt, "F4", scan_valueF4, sizeF4, &isNull);
        EXPECT_EQ(GMERR_OK, ret1);
        ret1 = strcmp(F4Value1, scan_valueF4);
        EXPECT_EQ(GMERR_OK, ret1);
        printf("[INFO][Thread_0] hash index scan F4 value is %s, size is %d \n", scan_valueF4, sizeF4);
        free(scan_valueF4);
    }
    GmcResetStmt(g_stmt);
    GmcFreeIndexKey(g_stmt);
    int ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return ((void *)0);
}

// 001. 同步建表，唯一hash索引和非唯一hash索引多线程读取同一条数据及扫描同张表数据（无错误发生）
TEST_F(AcceptanceTestCases, DML_027_AcceptanceTestCases_001)
{
    // Create VertexLabel
    const char *labelName = "T38";
    int ret = GmcCreateVertexLabel(stmt, accept_second_label_schema, normal_config_json);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0Value, sizeof(F0Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1Value, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &F2Value, sizeof(F2Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT32, &F3Value, sizeof(F3Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_STRING, F4Value, strlen(F4Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    const char *keyName = "T38_H0";
    const char *keyName_non = "T38_H1";
    pthread_t tid_scan;
    pthread_t tid_scan_non;
    pthread_t tid_read;
    //多线程读取
    ret = pthread_create(&tid_read, NULL, readThread, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    //多线程扫描
    ret = pthread_create(&tid_scan, NULL, scanThread, (void *)keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = pthread_create(&tid_scan_non, NULL, scanThread_non, (void *)keyName_non);
    EXPECT_EQ(GMERR_OK, ret);
    pthread_join(tid_read, NULL);
    pthread_join(tid_scan, NULL);
    pthread_join(tid_scan_non, NULL);

    ret = GmcDropVertexLabel(stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 002. 同步建表，主键修改一张表的记录，唯一hash索引和非唯一hash索引多线程扫描同张表数据（无错误发生）
TEST_F(AcceptanceTestCases, DML_027_AcceptanceTestCases_002)
{  // Create VertexLabel
    const char *labelName = "T38";
    int ret = GmcCreateVertexLabel(stmt, accept_second_label_schema, normal_config_json);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0Value, sizeof(F0Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1Value, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &F2Value, sizeof(F2Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT32, &F3Value, sizeof(F3Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_STRING, F4Value, strlen(F4Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    const char *keyName = "T38_H0";
    const char *keyName_non = "T38_H1";
    pthread_t tid_scan;
    pthread_t tid_scan_non;
    pthread_t tid_write;

    //主键修改
    ret = pthread_create(&tid_write, NULL, writeThread, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    //多线程扫描
    ret = pthread_create(&tid_scan, NULL, scanThread1, (void *)keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = pthread_create(&tid_scan_non, NULL, scanThread1_non, (void *)keyName_non);
    EXPECT_EQ(GMERR_OK, ret);

    pthread_join(tid_write, NULL);
    pthread_join(tid_scan, NULL);
    pthread_join(tid_scan_non, NULL);

    ret = GmcDropVertexLabel(stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
}
