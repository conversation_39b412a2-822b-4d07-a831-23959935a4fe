/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * Description: 支持大对象测试用例 -- 规格约束测试
 * Author: guopanpan
 * Create: 2021-10-14
 * History:
 */
#include "VertexLabelTypical.h"

GmcConnT *gConn = NULL;
GmcStmtT *gStmt = NULL;
const char *gKvTableName = "TestKvTable";

class BigObject003 : public testing::Test {
public:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/modifyCfg.sh  \"compatibleV3=0\"");
        GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh");
        int ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        ASSERT_EQ(GMERR_OK, ret);
        ret = testGmcConnect(&gConn, &gStmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    static void TearDownTestCase()
    {
        int ret = testGmcDisconnect(gConn, gStmt);
        EXPECT_EQ(GMERR_OK, ret);
        gConn = NULL;
        gStmt = NULL;
        ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        ret = testEnvClean();
        EXPECT_EQ(GMERR_OK, ret);
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    }

    virtual void SetUp()
    {
        AW_ADD_ERR_WHITE_LIST(1, "GMERR-1011000");
        GtResetTypicalFieldLenth();
        int ret = GtCreateVertexLabel(gStmt, gLabelTypicalJsonPath, gConfigJsonPath);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcKvCreateTable(gStmt, gKvTableName, NULL);
        ASSERT_EQ(GMERR_OK, ret);
        AW_CHECK_LOG_BEGIN();
    }

    virtual void TearDown()
    {
        AW_CHECK_LOG_END();
        GmcKvDropTable(gStmt, gKvTableName);
        GmcDropVertexLabel(gStmt, gLabelTypicalName);
    }
};

// 写入小于32K的vertex对象
TEST_F(BigObject003, DML_077_027)
{
    uint32_t vertexCount = 100;
    uint32_t childCount = 1;
    GtTypicalVertexCfgT vertexCfg = {10, vertexCount, 10, childCount, 0};
    int ret = GtInsertTypicalVertex(gStmt, vertexCfg);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GtScanTypicalVertexByHashIndex(gStmt, vertexCfg, "PrimaryKey");
    ASSERT_EQ(GMERR_OK, ret);
}

// 写入大于32K的vertex对象
TEST_F(BigObject003, DML_077_028)
{
    uint32_t vertexCount = 100;
    uint32_t childCount = 2;
    GtTypicalVertexCfgT vertexCfg = {10, vertexCount, 10, childCount, 0};
    int ret = GtInsertTypicalVertex(gStmt, vertexCfg);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GtScanTypicalVertexByHashIndex(gStmt, vertexCfg, "PrimaryKey");
    ASSERT_EQ(GMERR_OK, ret);
}

//  当前最大仅支持1MB，目前以1MB作为上限进行测试 (下同)
// 写入小于32M的vertex对象
TEST_F(BigObject003, DML_077_029)
{
    uint32_t vertexCount = 100;
    uint32_t childCount = 60;
    GtTypicalVertexCfgT vertexCfg = {10, vertexCount, 10, childCount, 0};
    int ret = GtInsertTypicalVertex(gStmt, vertexCfg);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GtScanTypicalVertexByHashIndex(gStmt, vertexCfg, "PrimaryKey");
    ASSERT_EQ(GMERR_OK, ret);
}

// 写入大于32M的vertex对象
TEST_F(BigObject003, DML_077_030)
{
    uint32_t vertexCount = 100;
    uint32_t childCount = 64;
    GtTypicalVertexCfgT vertexCfg = {10, vertexCount, 10, childCount, 0, GMC_OPERATION_INSERT};
    int ret = GtInsertTypicalVertex(gStmt, vertexCfg);
    ASSERT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);

    uint64_t count;
    ret = GmcGetVertexCount(gStmt, gLabelTypicalName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(0, count);
}

// 写入小于32M的vertex对象, 增量更新为大于32M的vertex对象
TEST_F(BigObject003, DML_077_031)
{
    int32_t startVal = 10;
    uint32_t vertexCount = 100;
    int32_t startMkVal = 10;
    uint32_t childCount = 60;
    GtTypicalVertexCfgT insertVertexCfg = {startVal, vertexCount, startMkVal, childCount, 0, GMC_OPERATION_INSERT};
    int ret = GtInsertTypicalVertex(gStmt, insertVertexCfg);
    ASSERT_EQ(GMERR_OK, ret);

    // 测试点
    GtTypicalVertexCfgT updateVertexCfg = {
        startVal, vertexCount, startMkVal + (int)childCount, 4, 0, GMC_OPERATION_UPDATE, GT_TYPICAL_CHILD_OPER_INSERT};
    ret = GtUpdateTypicalVertexByIndex(gStmt, updateVertexCfg, "PrimaryKey");
    ASSERT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);

    ret = GtScanTypicalVertexByHashIndex(gStmt, insertVertexCfg, "PrimaryKey");
    ASSERT_EQ(GMERR_OK, ret);
}

// replace大于32M的vertex对象
TEST_F(BigObject003, DML_077_032)
{
    uint32_t vertexCount = 100;
    uint32_t childCount = 64;
    GtTypicalVertexCfgT vertexCfg = {10, vertexCount, 10, childCount, 0, GMC_OPERATION_REPLACE};
    int ret = GtReplaceTypicalVertex(gStmt, vertexCfg);
    ASSERT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
}

// 写入小于32M的vertex对象, replace大于32M的相同主键数据
TEST_F(BigObject003, DML_077_033)
{
    uint32_t vertexCount = 100;
    uint32_t childCount = 60;
    GtTypicalVertexCfgT insertVertexCfg = {10, vertexCount, 10, childCount, 0, GMC_OPERATION_INSERT};
    int ret = GtInsertTypicalVertex(gStmt, insertVertexCfg);
    ASSERT_EQ(GMERR_OK, ret);

    // 测试点
    GtTypicalVertexCfgT replaceVertexCfg = {10, vertexCount, 10, childCount + 4, 0, GMC_OPERATION_REPLACE};
    ret = GtReplaceTypicalVertex(gStmt, replaceVertexCfg);
    ASSERT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
}

// meger大于32M的vertex对象
// XXX 单次merge子节点最大支持16
TEST_F(BigObject003, DML_077_034)
{
    uint32_t vertexCount = 100;
    uint32_t childCount = 16;
    GtSetTypicalFieldLenth(13312, 13312);
    GtTypicalVertexCfgT vertexCfg = {
        10, vertexCount, 10, childCount, 0, GMC_OPERATION_MERGE, GT_TYPICAL_CHILD_OPER_INSERT};
    int ret = GtMergeTypicalVertex(gStmt, vertexCfg);
    ASSERT_EQ(GMERR_OK, ret);
}

// 写入小于32M的vertex对象, meger大于32M的相同主键数据
TEST_F(BigObject003, DML_077_035)
{
    int32_t startVal = 10;
    uint32_t vertexCount = 100;
    int32_t startMkVal = 10;
    uint32_t childCount = 16;
    GtSetTypicalFieldLenth(13312, 13312);
    GtTypicalVertexCfgT insertVertexCfg = {
        startVal, vertexCount, startMkVal, childCount, 0, GMC_OPERATION_INSERT, GT_TYPICAL_CHILD_OPER_INSERT};
    int ret = GtInsertTypicalVertex(gStmt, insertVertexCfg);
    ASSERT_EQ(GMERR_OK, ret);

    // 测试点
    GtTypicalVertexCfgT replaceVertexCfg = {startVal, vertexCount, startMkVal + (int32_t)childCount, childCount, 0,
        GMC_OPERATION_MERGE, GT_TYPICAL_CHILD_OPER_INSERT};
    ret = GtMergeTypicalVertex(gStmt, replaceVertexCfg);
    ASSERT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
}

// 写入小于32K的kv对象
TEST_F(BigObject003, DML_077_036)
{
    uint32_t valLen = 32 * 1024 - 1;
    int ret = GtKvSetBytesArray(gStmt, gKvTableName, 0, 'a', valLen);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GtKvGetBytesArray(gStmt, gKvTableName, 0, 'a', valLen);
    ASSERT_EQ(GMERR_OK, ret);
}

// 写入大于32K的kv对象
TEST_F(BigObject003, DML_077_037)
{
    uint32_t valLen = 32 * 1024 + 1;
    int ret = GtKvSetBytesArray(gStmt, gKvTableName, 0, 'a', valLen);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GtKvGetBytesArray(gStmt, gKvTableName, 0, 'a', valLen);
    ASSERT_EQ(GMERR_OK, ret);
}

// 写入小于等于32M的kv对象
TEST_F(BigObject003, DML_077_038)
{
    uint32_t valLen = 1024 * 1023;
    int ret = GtKvSetBytesArray(gStmt, gKvTableName, 0, 'a', valLen);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GtKvGetBytesArray(gStmt, gKvTableName, 0, 'a', valLen);
    ASSERT_EQ(GMERR_OK, ret);
}

// 写入大于32M的kv对象
TEST_F(BigObject003, DML_077_039)
{
    uint32_t valLen = 1024 * 1024 + 1;
    int ret = GtKvSetBytesArray(gStmt, gKvTableName, 0, 'a', valLen);
    ASSERT_EQ(GMERR_INVALID_VALUE, ret);
    ret = GtKvGetBytesArray(gStmt, gKvTableName, 0, 'a', valLen);
    ASSERT_EQ(GMERR_NO_DATA, ret);
}

// 写入小于32M的kv对象, 更新为大于32M的kv对象
TEST_F(BigObject003, DML_077_040)
{
    uint32_t valLen = 1024 * 1024;
    int ret = GtKvSetBytesArray(gStmt, gKvTableName, 0, 'a', valLen);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GtKvSetBytesArray(gStmt, gKvTableName, 0, 'b', valLen + 1024 + 1);
    ASSERT_EQ(GMERR_INVALID_VALUE, ret);
    ret = GtKvGetBytesArray(gStmt, gKvTableName, 0, 'a', valLen);
    ASSERT_EQ(GMERR_OK, ret);
}
