[{"name": "vsys_rule", "source_vertex_label": "vsys", "comment": "haha", "dest_vertex_label": "vsys::rule", "constraint": {"operator_type": "and", "conditions": [{"source_property": "id", "dest_property": "vsys::id"}, {"source_property": "F15", "dest_property": "F15"}, {"source_property": "F16", "dest_property": "F16"}, {"source_property": "F17", "dest_property": "F17"}]}}, {"name": "ruleAndsource_ip", "source_vertex_label": "vsys::rule", "comment": "hahaha", "dest_vertex_label": "vsys::rule::source_ip", "constraint": {"operator_type": "and", "conditions": [{"source_property": "vsys::id", "dest_property": "rule::vsys::id"}, {"source_property": "id", "dest_property": "rule::id"}, {"source_property": "F18", "dest_property": "F18"}, {"source_property": "F19", "dest_property": "F19"}, {"source_property": "F20", "dest_property": "F20"}]}}]