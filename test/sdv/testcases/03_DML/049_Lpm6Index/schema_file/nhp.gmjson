[{"name": "nhp", "comment": "下一跳表，对应10#表", "fields": [{"name": "nhp_index", "type": "uint32"}, {"name": "vr_id", "type": "uint32"}, {"name": "vrf_index", "type": "uint32"}, {"name": "origin_nhp", "type": "uint32"}, {"name": "nhp_flag", "type": "uint8"}, {"name": "nhp_num", "type": "uint8"}, {"name": "ref_cnt", "type": "uint32"}, {"name": "flags", "type": "uint32"}, {"name": "iid_flags", "type": "uint32"}, {"name": "app_source_id", "type": "uint32"}, {"name": "table_smooth_id", "type": "uint32"}, {"name": "app_obj_id", "type": "uint64"}, {"name": "app_version", "type": "uint32"}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "nhp", "fields": ["nhp_index"], "constraints": {"unique": true}}]}]