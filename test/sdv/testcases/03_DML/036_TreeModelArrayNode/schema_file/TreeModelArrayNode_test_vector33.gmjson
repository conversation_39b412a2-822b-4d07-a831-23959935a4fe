[{"type": "record", "name": "OP_T0", "fields": [{"name": "F0", "type": "int64"}, {"name": "F1", "type": "uint64"}, {"name": "F2", "type": "int32"}, {"name": "F3", "type": "uint32"}, {"name": "F4", "type": "int16"}, {"name": "F5", "type": "uint16"}, {"name": "F6", "type": "int8"}, {"name": "F7", "type": "uint8"}, {"name": "F8", "type": "boolean"}, {"name": "F9", "type": "float"}, {"name": "F10", "type": "double"}, {"name": "F11", "type": "time"}, {"name": "F12", "type": "char"}, {"name": "F13", "type": "uchar"}, {"name": "F14", "type": "string", "size": 100}, {"name": "F15", "type": "bytes", "size": 7}, {"name": "F16", "type": "fixed", "size": 7}, {"name": "T1", "type": "record", "fields": [{"name": "P0", "type": "int64"}, {"name": "P1", "type": "uint64"}, {"name": "P2", "type": "int32"}, {"name": "P3", "type": "uint32"}, {"name": "P4", "type": "int16"}, {"name": "P5", "type": "uint16"}, {"name": "P6", "type": "int8"}, {"name": "P7", "type": "uint8"}, {"name": "P8", "type": "boolean"}, {"name": "P9", "type": "float"}, {"name": "P10", "type": "double"}, {"name": "P11", "type": "time"}, {"name": "P12", "type": "char"}, {"name": "P13", "type": "uchar"}, {"name": "P14", "type": "string", "size": 100}, {"name": "P15", "type": "bytes", "size": 7}, {"name": "P16", "type": "fixed", "size": 7}, {"name": "T2", "type": "record", "fixed_array": true, "size": 3, "fields": [{"name": "A0", "type": "int64"}, {"name": "A1", "type": "uint64"}, {"name": "A2", "type": "int32"}, {"name": "A3", "type": "uint32"}, {"name": "A4", "type": "int16"}, {"name": "A5", "type": "uint16"}, {"name": "A6", "type": "int8"}, {"name": "A7", "type": "uint8"}, {"name": "A8", "type": "boolean"}, {"name": "A9", "type": "float"}, {"name": "A10", "type": "double"}, {"name": "A11", "type": "time"}, {"name": "A12", "type": "char"}, {"name": "A13", "type": "uchar"}, {"name": "A14", "type": "string", "size": 100}, {"name": "A15", "type": "bytes", "size": 7}, {"name": "A16", "type": "fixed", "size": 7}], "super_fields": [{"name": "superfiled1", "comment": "test", "fields": ["A0", "A1", "A2", "A3", "A4"]}]}], "super_fields": [{"name": "superfiled0", "comment": "test", "fields": ["P0", "P1", "P2", "P3", "P4"]}]}, {"name": "T3", "type": "record", "vector": true, "size": 3, "fields": [{"name": "V0", "type": "int64"}, {"name": "V1", "type": "uint64"}, {"name": "V2", "type": "int32"}, {"name": "V3", "type": "uint32"}, {"name": "V4", "type": "int16"}, {"name": "V5", "type": "uint16"}, {"name": "V6", "type": "int8"}, {"name": "V7", "type": "uint8"}, {"name": "V8", "type": "int64"}, {"name": "V9", "type": "uint64"}, {"name": "V10", "type": "int32"}, {"name": "V11", "type": "uint32"}, {"name": "V12", "type": "int16"}, {"name": "V13", "type": "uint16"}, {"name": "V14", "type": "int8"}, {"name": "V15", "type": "uint8"}, {"name": "V16", "type": "int64"}, {"name": "V17", "type": "uint64"}, {"name": "V18", "type": "int32"}, {"name": "V19", "type": "uint32"}, {"name": "V20", "type": "int16"}, {"name": "V21", "type": "uint16"}, {"name": "V22", "type": "int8"}, {"name": "V23", "type": "uint8"}, {"name": "V24", "type": "int64"}, {"name": "V25", "type": "uint64"}, {"name": "V26", "type": "int32"}, {"name": "V27", "type": "uint32"}, {"name": "V28", "type": "int16"}, {"name": "V29", "type": "uint16"}, {"name": "V30", "type": "int8"}, {"name": "V31", "type": "uint8"}, {"name": "V32", "type": "uint8"}, {"name": "V33", "type": "boolean"}, {"name": "V34", "type": "float"}, {"name": "V35", "type": "double"}, {"name": "V36", "type": "time"}, {"name": "V37", "type": "char"}, {"name": "V38", "type": "uchar"}, {"name": "V39", "type": "string", "size": 100}, {"name": "V40", "type": "bytes", "size": 7}, {"name": "V41", "type": "fixed", "size": 7}], "super_fields": [{"name": "superfiled2", "comment": "test", "fields": ["V0", "V1", "V2", "V3", "V4"]}]}], "keys": [{"node": "OP_T0", "name": "OP_PK", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "OP_T0", "name": "localhash_key", "index": {"type": "<PERSON><PERSON><PERSON>"}, "fields": ["F0"], "constraints": {"unique": true}}, {"node": "T3", "name": "member_key1", "index": {"type": "none"}, "fields": ["V0", "V1", "V2", "V3", "V4", "V5", "V6", "V7", "V8", "V9", "V10", "V11", "V12", "V13", "V14", "V15", "V16", "V17", "V18", "V19", "V20", "V21", "V22", "V23", "V24", "V25", "V26", "V27", "V28", "V29", "V30", "V31", "V32"], "constraints": {"unique": true}}]}]