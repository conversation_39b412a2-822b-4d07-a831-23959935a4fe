[{"type": "record", "name": "T39", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "string", "size": 10, "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint16", "nullable": true}, {"name": "F5", "type": "int32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "int64", "nullable": true}, {"name": "F8", "type": "uint64", "nullable": true}, {"name": "F9", "type": "int", "nullable": true}, {"name": "F10", "type": "int", "nullable": true}, {"name": "F11", "type": "float", "nullable": true}, {"name": "F12", "type": "double", "nullable": true}], "keys": [{"node": "T39", "name": "T39_K0", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "T39", "name": "T39_hash", "fields": ["F2", "F3", "F4", "F5"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": false}}]}]