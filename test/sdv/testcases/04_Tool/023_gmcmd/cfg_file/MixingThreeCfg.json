{"int8": {"generate_rule": "automatic", "type": "random", "properties": {"max_value": 10, "min_value": -10}}, "uint8": {"generate_rule": "automatic", "type": "random", "properties": {"max_value": 10, "min_value": 1}}, "int16": {"generate_rule": "automatic", "type": "random", "properties": {"max_value": 1000, "min_value": -1000}}, "uint16": {"generate_rule": "automatic", "type": "random", "properties": {"max_value": 1000, "min_value": 1}}, "int32": {"generate_rule": "automatic", "type": "sequential", "properties": {"max_value": 10000, "min_value": -10000, "step": 2}}, "uint32": {"generate_rule": "automatic", "type": "sequential", "properties": {"max_value": 10000, "min_value": 1, "step": 2}}, "int64": {"generate_rule": "automatic", "type": "sequential", "properties": {"max_value": 100000, "min_value": -100000, "step": 2}}, "uint64": {"generate_rule": "automatic", "type": "sequential", "properties": {"max_value": 100000, "min_value": 1, "step": 2}}, "float": {"generate_rule": "automatic", "type": "fix_value", "properties": {"fix_value": 10.0}}, "string": {"generate_rule": "automatic", "type": "fix_value", "properties": {"fix_value": "hahahaha"}}, "time": {"generate_rule": "automatic", "type": "fix_value", "format": "%Y-%m-%d %H:%M:%S", "properties": {"fix_value": "2022-03-08 12:00:00"}}, "byte": {"generate_rule": "automatic", "type": "fix_value", "properties": {"fix_value": 1, "fix_value_len": 7}}, "bitmap": {"generate_rule": "automatic", "type": "fix_value", "properties": {"fix_value": "1010101010101010"}}, "boolean": {"generate_rule": "automatic", "type": "fix_value", "properties": {"fix_value": true}}}