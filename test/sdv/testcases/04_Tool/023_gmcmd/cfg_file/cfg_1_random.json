{"int8": {"generate_rule": "automatic", "type": "random", "properties": {"max_value": 10, "min_value": -10}}, "uint8": {"generate_rule": "automatic", "type": "random", "properties": {"max_value": 10, "min_value": 1}}, "int16": {"generate_rule": "automatic", "type": "random", "properties": {"max_value": 1000, "min_value": -1000}}, "uint16": {"generate_rule": "automatic", "type": "random", "properties": {"max_value": 1000, "min_value": 1}}, "int32": {"generate_rule": "automatic", "type": "random", "properties": {"max_value": 10000, "min_value": -10000}}, "uint32": {"generate_rule": "automatic", "type": "random", "properties": {"max_value": 10000, "min_value": 1}}, "int64": {"generate_rule": "automatic", "type": "random", "properties": {"max_value": 100000, "min_value": -100000}}, "uint64": {"generate_rule": "automatic", "type": "random", "properties": {"max_value": 100000, "min_value": 1}}, "float": {"generate_rule": "automatic", "type": "random", "properties": {"max_value": 19.9, "min_value": 1.0}}, "string": {"generate_rule": "automatic", "type": "random", "properties": {"lower_case": true, "upper_case": true, "number": true, "special_char": true, "custom_char": "!@#$%^&_", "max_value": 5, "min_value": 1}}, "time": {"generate_rule": "automatic", "type": "random", "format": "%Y-%m-%d %H:%M:%S", "properties": {"max_value": "2018-01-01 12:00:00", "min_value": "2000-01-01 12:00:00"}}, "byte": {"generate_rule": "automatic", "type": "random", "properties": {"max_value": 5, "min_value": 1}}, "boolean": {"generate_rule": "automatic", "type": "fix_value", "properties": {"fix_value": true}}}