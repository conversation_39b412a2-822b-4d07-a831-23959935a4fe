[{"type": "record", "name": "ResourceLabel", "fields": [{"name": "F0", "type": "int32", "nullable": false}, {"name": "F1", "type": "int32", "nullable": false}, {"name": "F2", "type": "int32", "nullable": false}, {"name": "F3", "type": "resource", "nullable": false}], "keys": [{"node": "ResourceLabel", "name": "ResourceLabel_PK", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]