[{"type": "record", "name": "CheckSimple013", "fields": [{"name": "F7", "type": "uint64", "nullable": false}, {"name": "F0", "type": "char", "nullable": false}, {"name": "F1", "type": "uchar", "nullable": false}, {"name": "F2", "type": "int8", "nullable": false}, {"name": "F3", "type": "uint8", "nullable": false}, {"name": "F4", "type": "int16", "nullable": false}, {"name": "F5", "type": "uint16", "nullable": false}, {"name": "F6", "type": "int32", "nullable": false}, {"name": "F8", "type": "boolean", "nullable": false}, {"name": "F9", "type": "int64", "nullable": false}, {"name": "F10", "type": "uint64", "nullable": false}, {"name": "F11", "type": "float", "nullable": false}, {"name": "F12", "type": "double", "nullable": false}, {"name": "F13", "type": "time", "nullable": false}, {"name": "F14", "type": "string", "nullable": false, "size": 100}, {"name": "F15", "type": "bytes", "size": 7, "nullable": true}, {"name": "F16", "type": "fixed", "size": 7, "nullable": true}, {"name": "F17", "type": "uint8: 8", "nullable": false}, {"name": "F18", "type": "uint32: 28", "nullable": false}, {"name": "F19", "type": "uint16: 12", "nullable": false}, {"name": "F20", "type": "uint64: 58", "nullable": false}, {"name": "FFF", "type": "uint32", "nullable": true, "default": 90, "comment": "备注"}, {"name": "FF", "type": "uint32", "nullable": true, "auto_increment": true}, {"name": "F21", "type": "partition", "nullable": false}, {"name": "F23", "type": "resource", "nullable": false}, {"name": "FC1", "type": "int32", "nullable": true}], "keys": [{"node": "CheckSimple013", "name": "T20_PK", "fields": ["F7"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "CheckSimple013", "name": "localhash_key1", "index": {"type": "<PERSON><PERSON><PERSON>"}, "fields": ["F10"], "constraints": {"unique": true}}, {"node": "CheckSimple013", "name": "localhash_key2", "index": {"type": "<PERSON><PERSON><PERSON>"}, "fields": ["F4"], "constraints": {"unique": false}}, {"node": "CheckSimple013", "name": "hashcluster_key1", "index": {"type": "hashcluster"}, "fields": ["F3"], "constraints": {"unique": false}}, {"node": "CheckSimple013", "name": "hashcluster_key2", "index": {"type": "hashcluster"}, "fields": ["F5"], "constraints": {"unique": false}}]}]