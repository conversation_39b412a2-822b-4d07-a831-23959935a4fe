{"comment": "IF AFT", "version": "2.0", "type": "record", "name": "if", "config": {"check_validity": false}, "max_record_count": 128000, "fields": [{"name": "ifindex", "type": "uint32", "comment": "端口索引"}, {"name": "name", "type": "fixed", "size": 64, "comment": "端口名"}, {"name": "vrid", "type": "uint32", "comment": "虚拟设备ID"}, {"name": "if_type", "type": "uint32", "comment": "端口类型"}, {"name": "shutdown", "type": "uint32", "comment": "shutdown标志"}, {"name": "linkup", "type": "uint32", "comment": "链接状态"}, {"name": "tbtp", "type": "uint32", "comment": "tbtp"}, {"name": "tb", "type": "uint32", "comment": "tb"}, {"name": "tp", "type": "uint32", "comment": "tp"}, {"name": "port_switch", "type": "uint32", "comment": "port_switch"}, {"name": "forwardType", "type": "uint32", "comment": "转发类型"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "fixed", "size": 6, "comment": "mac地址"}, {"name": "ipv4_mtu", "type": "uint16", "comment": "ipv4报文mtu"}, {"name": "ipv4_enable", "type": "uint16", "comment": "ipv4报文状态"}, {"name": "ipv6_mtu", "type": "uint16", "comment": "ipv6报文mtu"}, {"name": "ipv6_enable", "type": "uint16", "comment": "ipv6报文状态"}, {"name": "on_board", "type": "uint32", "comment": "板在线标志"}, {"name": "lagid", "type": "uint32", "comment": "id of the interface trunk"}, {"name": "hppsvcflg", "type": "uint32", "comment": "端口是否下HPP标志位"}, {"name": "error_down", "type": "uint32", "comment": "error down的新标记"}, {"name": "speed", "type": "uint64", "comment": "速率值"}, {"name": "ifm", "type": "record", "comment": "接口管理", "fields": [{"name": "simple_name", "type": "uint32", "comment": "简记"}, {"name": "description", "type": "fixed", "size": 64, "comment": "描述"}, {"name": "is_configure", "type": "uint32", "comment": "配置类型"}, {"name": "main_ifindex", "type": "uint32", "comment": "主接口"}, {"name": "sub_max_num", "type": "uint32", "comment": "子接口最大数量"}, {"name": "sub_curr_num", "type": "uint32", "comment": "子接口当前数量"}, {"name": "error_down", "type": "uint32", "comment": "error down标记"}, {"name": "statistic", "type": "uint32", "comment": "端口统计"}, {"name": "vsys_id", "type": "uint32", "comment": "虚拟系统id"}, {"name": "zone_id", "type": "uint32", "comment": "虚拟空间id"}, {"name": "last_up_time", "type": "uint32", "comment": "最近up时间"}, {"name": "last_down_time", "type": "uint32", "comment": "最近down时间"}]}, {"name": "dev", "type": "record", "comment": "设备属性", "fields": [{"name": "dev_id", "type": "uint32", "comment": "设备id"}, {"name": "chassis_id", "type": "uint32", "comment": "底板id"}, {"name": "slot_id", "type": "uint32", "comment": "槽位id"}, {"name": "card_id", "type": "uint32", "comment": "单板id"}, {"name": "unit_id", "type": "uint32", "comment": "单元id"}, {"name": "port_id", "type": "uint32", "comment": "端口id"}, {"name": "port_num", "type": "uint32", "comment": "端口序号"}]}, {"name": "l2", "type": "record", "comment": "二层属性", "fields": [{"name": "trunk_id", "type": "uint32", "comment": "phy member belong to a trunk id"}, {"name": "vlan_id", "type": "uint32", "comment": "vlan id"}, {"name": "l2_portindex", "type": "uint32", "comment": "二层口索引"}, {"name": "vsi", "type": "uint32", "comment": "vsi"}, {"name": "tunnel_id", "type": "uint32", "comment": "隧道id"}, {"name": "bd_id", "type": "uint32", "comment": "bd id"}]}, {"name": "port", "type": "record", "comment": "端口属性", "fields": [{"name": "speed", "type": "uint64", "comment": "速率"}, {"name": "duplex", "type": "uint32", "comment": "双工标志"}, {"name": "flow_control", "type": "uint32", "comment": "流控制"}, {"name": "phy_type", "type": "uint32", "comment": "物理口类型"}, {"name": "jumbo", "type": "uint32", "comment": "jumbo"}, {"name": "baud", "type": "uint32", "comment": "波特率"}, {"name": "rmon", "type": "uint32", "comment": "远程监控"}, {"name": "phy_link", "type": "uint32", "comment": "物理链路"}, {"name": "if_mib", "type": "uint32", "comment": "端口管理信息库"}, {"name": "on_board", "type": "uint32", "comment": "板在位"}]}], "keys": [{"name": "if_pk", "index": {"type": "primary"}, "node": "if", "fields": ["ifindex"], "constraints": {"unique": true}, "comment": "端口索引键"}, {"name": "ifname_pk", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "if", "fields": ["name"], "constraints": {"unique": false}, "comment": "端口名键"}]}