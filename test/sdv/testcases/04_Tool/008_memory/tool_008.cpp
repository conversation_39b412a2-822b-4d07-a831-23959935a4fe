/*****************************************************************************
 Description  : 内存管理模块视图能力增强测试
 Notes        : 01.单线程查询共享内存视图，校验SEGMENT_IN_CTX_INFO字段
                02.单线程查询共享内存视图，校验MEMORY_USAGE字段
                03.单线程查询共享内存视图，校验SEGMGR_INFO字段
                04.单线程查询动态内存视图，校验FREELIST_INFO字段
                05.单线程查询动态内存视图，校验BIGCHUNK_INFO字段
                06.单线程查询动态内存视图，校验BLOCK_INFO字段
                07.单线程查询动态内存视图，校验MEMORY_USAGE字段、
                08.mainstore,创建label,写入10万条记录;查看共享内存视图
                09.deltastore,创建label,写入10万条记录;查看动态内存视图
                10.mainstore,创建label,写入10万条记录;查看动态内存视图
                11.deltastore,创建label,写入10万条记录;查看共享内存视图
                12.tree模型中，执行多次dml操作;查看共享内存视图
                13.tree模型中，执行多次dml操作;查看动态内存视图
                14.循环查询共享内存视图1000次
                15.循环查询动态内存视图1000次
                16.多线程循环查询共享内存视图1000次
                17.多线程循环查询动态内存视图1000次
                18.开启事务，写入10万条数据；查看内存视图"
                19.写入10万条数据；查看内存视图；删除10万条数据；查看视图"
                20.写入10万条数据；查看内存视图；更新10万条数据；查看视图"
                21.创建KV表，执行10万次dml操作；查看视图
                22.创建10万条边；查看视图"
                23.创建资源池；插10万条数据；查看视图"
                24.创建订阅，插入10000条数据；查看视图"
 History      :
 Author       : dwx992801
 Modification :
 Date         : 2021/4/25
*****************************************************************************/
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "../../05_SN/002_CreateAndDeleteFullLabelSubscription//sub_tools.h"

#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];
GmcConnT *g_conn;
GmcStmtT *g_stmt;
int ret = 0;
char g_label_config3[] = "{\"max_record_num\":10000, \"isFastReadUncommitted\":0}";

#if defined ENV_RTOSV2X
#define INSERT_NUM 10000
#else
#define INSERT_NUM 100000
#endif


const char *deltaStoreJson = "{     \
    \"delta_stores\":                        \
    [{                                       \
        \"name\": \"dsDemo\" ,  \
        \"init_mem_size\": 32768,            \
        \"max_mem_size\": 10485760,          \
        \"extend_mem_size\": 16384,          \
        \"page_size\": 4096                   \
    }]                                       \
}";

const char *g_label_config1 = R"(
        {
            "max_record_count":200000,
            "writers":"abc"     
        })";

const char *g_label_config2 = R"(
        {
            "max_record_count":200000,
            "delta_store_name":"dsDemo",
            "writers":"abc"     
        })";

class Tool_008 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh ");
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void Tool_008::SetUp()
{
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void Tool_008::TearDown()
{
    GmcFreeStmt(g_stmt);
    ret = testGmcDisconnect(g_conn);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(Tool_008, Tool_008_001)
{
    char const *view_name = "V\\$COM_SHMEM_CTX";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s\n", g_toolPath, g_connServer,
        view_name);
    LOG("g_command = %s", g_command);
    ret = executeCommand(g_command, "SEGMENT_NUM");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

TEST_F(Tool_008, Tool_008_002)
{
    char const *view_name = "V\\$COM_SHMEM_CTX";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s\n", g_toolPath, g_connServer,
        view_name);
    LOG("g_command = %s", g_command);
    ret = executeCommand(g_command, "MEMORY_USAGE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

TEST_F(Tool_008, Tool_008_003)
{
    char const *view_name = "V\\$COM_SHMEM_CTX";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s\n", g_toolPath, g_connServer,
        view_name);
    LOG("g_command = %s", g_command);
    // ret = executeCommand(g_command, "SEGMGR_INFO");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

TEST_F(Tool_008, Tool_008_004)
{
    char const *view_name = "V\\$COM_DYN_CTX";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s\n", g_toolPath, g_connServer,
        view_name);
    LOG("g_command = %s", g_command);
    ret = executeCommand(g_command, "");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

TEST_F(Tool_008, Tool_008_005)
{
    char const *view_name = "V\\$COM_DYN_CTX";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s\n", g_toolPath, g_connServer,
        view_name);
    LOG("g_command = %s", g_command);
    ret = executeCommand(g_command, "BIG_CHUNK_INFO");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

TEST_F(Tool_008, Tool_008_006)
{
    char const *view_name = "V\\$COM_DYN_CTX";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s\n", g_toolPath, g_connServer,
        view_name);
    LOG("g_command = %s", g_command);
    ret = executeCommand(g_command, "BLOCK_INFO");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

TEST_F(Tool_008, Tool_008_007)
{
    char const *view_name = "V\\$COM_DYN_CTX";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s\n", g_toolPath, g_connServer,
        view_name);
    LOG("g_command = %s", g_command);
    ret = executeCommand(g_command, "MEMORY_USAGE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

int get_global_phy_size(char *cmd, int *mem_mb, int *mem_kb, int *mem_byte)
{
    FILE *pf = popen(cmd, "r");
    char s[100] = {0};
    if (pf == NULL) {
        LOG("popen(%s) error.\n", cmd);
        EXPECT_EQ(0, 1);
        return -1;
    }
    fgets(s, 100, pf);
    sscanf(s, "  GLOBAL_PHY_SIZE: [%d] MB [%d] KB [%d] Byte", mem_mb, mem_kb, mem_byte);
    pclose(pf);
    return 0;
}

TEST_F(Tool_008, Tool_008_008)  // index :TOTAL_PHY_SIZE增加
{
    int32_t value = 1;
    char *label_schema = NULL;
    const char *labelName0 = "demo1";
    void *vertexLabel = NULL;
    char const *view_name = "V\\$COM_SHMEM_CTX";
    char const *view_name1 = "V\\$COM_DYN_CTX";

    readJanssonFile("schema_file/label1.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label schema:%s", label_schema);

    GmcDropVertexLabel(g_stmt, labelName0);
    ret = GmcCreateVertexLabel(g_stmt, label_schema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(label_schema);

    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 0; i < INSERT_NUM; i++) {
        value = i;
        //设置F0属性值
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &value, sizeof(value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        //设置F1属性值
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &value, sizeof(value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        //插入顶点
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s | grep -E -A 17 'index = 0' > shmem.log\n",
        g_toolPath, g_connServer, view_name);
    LOG("g_command = %s", g_command);
    ret = executeCommand(g_command);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s | grep -E -A 17 'index = 0' > dyn.log\n",
        g_toolPath, g_connServer, view_name1);
    LOG("g_command = %s", g_command);
    ret = executeCommand(g_command);

    int mem_mb_shm = 0, mem_kb_shm = 0, mem_byte_shm = 0;
    snprintf(g_command, MAX_CMD_SIZE, "cat shmem.log | grep 'GLOBAL_PHY_SIZE'\n");
    ret = get_global_phy_size(g_command, &mem_mb_shm, &mem_kb_shm, &mem_byte_shm);
    LOG("mem_mb_shm = %d, mem_kb_shm = %d, mem_byte_shm = %d", mem_mb_shm, mem_kb_shm, mem_byte_shm);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int mem_mb_dyn = 0, mem_kb_dyn = 0, mem_byte_dyn = 0;
    snprintf(g_command, MAX_CMD_SIZE, "cat dyn.log | grep 'GLOBAL_PHY_SIZE'\n");
    ret = get_global_phy_size(g_command, &mem_mb_dyn, &mem_kb_dyn, &mem_byte_dyn);
    LOG("mem_mb_dyn = %d, mem_kb_dyn = %d, mem_byte_dyn = %d", mem_mb_dyn, mem_kb_dyn, mem_byte_dyn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // system("rm -f dyn.log shmem.log");
    //关闭顶点label
    ret = GmcDropVertexLabel(g_stmt, labelName0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

TEST_F(Tool_008, Tool_008_010)  // index :TOTAL_PHY_SIZE增加
{
    int32_t value = 1;
    char *label_schema = NULL;
    const char *labelName0 = "demo1";
    void *vertexLabel = NULL;
    char const *view_name = "V\\$COM_SHMEM_CTX";
    char const *view_name1 = "V\\$COM_DYN_CTX";

    readJanssonFile("schema_file/label1.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label schema:%s", label_schema);

    GmcDropVertexLabel(g_stmt, labelName0);
    ret = GmcCreateVertexLabel(g_stmt, label_schema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(label_schema);

    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 0; i < INSERT_NUM; i++) {
        value = i;
        //设置F0属性值
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &value, sizeof(value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        //设置F1属性值
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &value, sizeof(value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        //插入顶点
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s | grep -E -A 17 'index = 0' > shmem.log\n",
        g_toolPath, g_connServer, view_name);
    LOG("g_command = %s", g_command);
    ret = executeCommand(g_command);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s | grep -E -A 17 'index = 0' > dyn.log\n",
        g_toolPath, g_connServer, view_name1);
    LOG("g_command = %s", g_command);
    ret = executeCommand(g_command);

    int mem_mb_shm = 0, mem_kb_shm = 0, mem_byte_shm = 0;
    snprintf(g_command, MAX_CMD_SIZE, "cat shmem.log | grep 'GLOBAL_PHY_SIZE'\n");
    ret = get_global_phy_size(g_command, &mem_mb_shm, &mem_kb_shm, &mem_byte_shm);
    LOG("mem_mb_shm = %d, mem_kb_shm = %d, mem_byte_shm = %d", mem_mb_shm, mem_kb_shm, mem_byte_shm);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int mem_mb_dyn = 0, mem_kb_dyn = 0, mem_byte_dyn = 0;
    snprintf(g_command, MAX_CMD_SIZE, "cat dyn.log | grep 'GLOBAL_PHY_SIZE'\n");
    ret = get_global_phy_size(g_command, &mem_mb_dyn, &mem_kb_dyn, &mem_byte_dyn);
    LOG("mem_mb_dyn = %d, mem_kb_dyn = %d, mem_byte_dyn = %d", mem_mb_dyn, mem_kb_dyn, mem_byte_dyn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // system("rm -f dyn.log shmem.log");
    //关闭顶点label
    ret = GmcDropVertexLabel(g_stmt, labelName0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

const char *labelName = "OP_T0";
int start_num = 0;
int end_num = 1;
int array_num = 3;
int vector_num = 3;

class TreeModel : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void TreeModel::SetUpTestCase()
{
    int ret = 0;
    system("sh $TEST_HOME/tools/start.sh ");
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TreeModel::TearDownTestCase()
{
    int ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}

void TreeModel::SetUp()
{
    int ret = 0;
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TreeModel::TearDown()
{
    int32_t ret = 0;
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

void TestGmcSetNodePropertyByName_PK(GmcNodeT *root, int i)
{
    int ret = 0;
    int8_t f0_value = i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F0", GMC_DATATYPE_INT8, &f0_value, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestGmcSetNodePropertyByName_R(GmcNodeT *root, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    uint8_t f1_value = 1 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F1", GMC_DATATYPE_UINT8, &f1_value, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int16_t f2_value = 2 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F2", GMC_DATATYPE_INT16, &f2_value, sizeof(int16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint16_t f3_value = 3 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F3", GMC_DATATYPE_UINT16, &f3_value, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t f4_value = 4 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F4", GMC_DATATYPE_INT32, &f4_value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t f5_value = 5 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F5", GMC_DATATYPE_UINT32, &f5_value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int64_t f6_value = 6 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F6", GMC_DATATYPE_INT64, &f6_value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t f7_value = 7 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F7", GMC_DATATYPE_UINT64, &f7_value, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(root, (char *)"F8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    float f9_value = 9 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    double f10_value = 10 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char f12_value = 1 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    unsigned char f13_value = 13 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(root, (char *)"F14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(root, (char *)"F15", GMC_DATATYPE_BYTES, f14_value, 7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(root, (char *)"F16", GMC_DATATYPE_FIXED, f14_value, 7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void TestGmcSetNodePropertyByName_P(GmcNodeT *root, int i, bool bool_value, char *f14_value)
{
    int ret = 0;

    int8_t f0_value = i;
    ret = GmcNodeSetPropertyByName(root, (char *)"P0", GMC_DATATYPE_INT8, &f0_value, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint8_t f1_value = 1 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"P1", GMC_DATATYPE_UINT8, &f1_value, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int16_t f2_value = 2 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"P2", GMC_DATATYPE_INT16, &f2_value, sizeof(int16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint16_t f3_value = 3 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"P3", GMC_DATATYPE_UINT16, &f3_value, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t f4_value = 4 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"P4", GMC_DATATYPE_INT32, &f4_value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t f5_value = 5 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"P5", GMC_DATATYPE_UINT32, &f5_value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int64_t f6_value = 6 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"P6", GMC_DATATYPE_INT64, &f6_value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t f7_value = 7 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"P7", GMC_DATATYPE_UINT64, &f7_value, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(root, (char *)"P8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    float f9_value = 9 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"P9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    double f10_value = 10 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"P10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"P11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char f12_value = 1 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"P12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    unsigned char f13_value = 13 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"P13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(root, (char *)"P14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(root, (char *)"P15", GMC_DATATYPE_BYTES, f14_value, 7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(root, (char *)"P16", GMC_DATATYPE_FIXED, f14_value, 7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestGmcSetNodePropertyByName_A(GmcNodeT *root, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    int8_t f0_value = i;
    ret = GmcNodeSetPropertyByName(root, (char *)"A0", GMC_DATATYPE_INT8, &f0_value, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint8_t f1_value = 1 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"A1", GMC_DATATYPE_UINT8, &f1_value, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int16_t f2_value = 2 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"A2", GMC_DATATYPE_INT16, &f2_value, sizeof(int16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint16_t f3_value = 3 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"A3", GMC_DATATYPE_UINT16, &f3_value, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t f4_value = 4 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"A4", GMC_DATATYPE_INT32, &f4_value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t f5_value = 5 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"A5", GMC_DATATYPE_UINT32, &f5_value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int64_t f6_value = 6 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"A6", GMC_DATATYPE_INT64, &f6_value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t f7_value = 7 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"A7", GMC_DATATYPE_UINT64, &f7_value, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(root, (char *)"A8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    float f9_value = 9 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"A9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    double f10_value = 10 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"A10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"A11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char f12_value = 1 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"A12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    unsigned char f13_value = 13 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"A13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(root, (char *)"A14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(root, (char *)"A15", GMC_DATATYPE_BYTES, f14_value, 7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(root, (char *)"A16", GMC_DATATYPE_FIXED, f14_value, 7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestGmcSetNodePropertyByName_V(GmcNodeT *root, int i, bool bool_value, char *f14_value)
{
    int ret = 0;

    int8_t f0_value = i;
    ret = GmcNodeSetPropertyByName(root, (char *)"V0", GMC_DATATYPE_INT8, &f0_value, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint8_t f1_value = 1 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"V1", GMC_DATATYPE_UINT8, &f1_value, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int16_t f2_value = 2 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"V2", GMC_DATATYPE_INT16, &f2_value, sizeof(int16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint16_t f3_value = 3 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"V3", GMC_DATATYPE_UINT16, &f3_value, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t f4_value = 4 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"V4", GMC_DATATYPE_INT32, &f4_value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t f5_value = 5 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"V5", GMC_DATATYPE_UINT32, &f5_value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int64_t f6_value = 6 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"V6", GMC_DATATYPE_INT64, &f6_value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t f7_value = 7 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"V7", GMC_DATATYPE_UINT64, &f7_value, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(root, (char *)"V8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    float f9_value = 9 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"V9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    double f10_value = 10 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"V10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"V11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char f12_value = 1 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"V12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    unsigned char f13_value = 13 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"V13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(root, (char *)"V14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(root, (char *)"V15", GMC_DATATYPE_BYTES, f14_value, 7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(root, (char *)"V16", GMC_DATATYPE_FIXED, f14_value, 7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestGmcGetNodePropertyByName_R(GmcNodeT *root, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    bool isNull;
    uint8_t f1_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"F1", &f1_value, sizeof(uint8_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(1 * i, f1_value);

    int16_t f2_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"F2", &f2_value, sizeof(int16_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(2 * i, f2_value);

    uint16_t f3_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"F3", &f3_value, sizeof(uint16_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(3 * i, f3_value);

    int32_t f4_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"F4", &f4_value, sizeof(int32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(4 * i, f4_value);

    uint32_t f5_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"F5", &f5_value, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(5 * i, f5_value);

    int64_t f6_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"F6", &f6_value, sizeof(int64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(6 * i, f6_value);

    uint64_t f7_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"F7", &f7_value, sizeof(uint64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(7 * i, f7_value);

    bool f8_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"F8", &f8_value, sizeof(bool), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(bool_value, f8_value);

    float f9_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"F9", &f9_value, sizeof(float), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(9 * i, f9_value);

    double f10_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"F10", &f10_value, sizeof(double), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(10 * i, f10_value);

    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"F11", &f11_value, sizeof(uint64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(11 * i, f11_value);

    char f12_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"F12", &f12_value, sizeof(char), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(1 * i, f12_value);

    unsigned char f13_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"F13", &f13_value, sizeof(unsigned char), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    unsigned char k = (unsigned char)(13 * i);
    AW_MACRO_EXPECT_EQ_INT(k, f13_value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(root, (char *)"F14", &propSize);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(propSize, strlen(f14_value) + 1);

    char string_value[7];
    ret = GmcNodeGetPropertyByName(root, (char *)"F14", &string_value, propSize, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(root, (char *)"F15", &string_value, 7, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(root, (char *)"F16", &string_value, 7, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string_value, f14_value), 0);
}

void TestGmcGetNodePropertyByName_p(GmcNodeT *root, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    bool isNull;
    int8_t f0_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"P0", &f0_value, sizeof(int8_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(i, f0_value);

    uint8_t f1_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"P1", &f1_value, sizeof(uint8_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(1 * i, f1_value);

    int16_t f2_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"P2", &f2_value, sizeof(int16_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(2 * i, f2_value);

    uint16_t f3_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"P3", &f3_value, sizeof(uint16_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(3 * i, f3_value);

    int32_t f4_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"P4", &f4_value, sizeof(int32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(4 * i, f4_value);

    uint32_t f5_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"P5", &f5_value, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(5 * i, f5_value);

    int64_t f6_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"P6", &f6_value, sizeof(int64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(6 * i, f6_value);

    uint64_t f7_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"P7", &f7_value, sizeof(uint64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(7 * i, f7_value);

    bool f8_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"P8", &f8_value, sizeof(bool), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(bool_value, f8_value);

    float f9_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"P9", &f9_value, sizeof(float), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(9 * i, f9_value);

    double f10_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"P10", &f10_value, sizeof(double), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(10 * i, f10_value);

    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"P11", &f11_value, sizeof(uint64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(11 * i, f11_value);

    char f12_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"P12", &f12_value, sizeof(char), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(1 * i, f12_value);

    unsigned char f13_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"P13", &f13_value, sizeof(unsigned char), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    unsigned char k = (unsigned char)(13 * i);
    AW_MACRO_EXPECT_EQ_INT(k, f13_value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(root, (char *)"P14", &propSize);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(propSize, strlen(f14_value) + 1);

    char string_value[7];
    ret = GmcNodeGetPropertyByName(root, (char *)"P14", &string_value, propSize, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(root, (char *)"P15", &string_value, 7, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(root, (char *)"P16", &string_value, 7, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string_value, f14_value), 0);
}

void TestGmcGetNodePropertyByName_A(GmcNodeT *root, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    bool isNull;
    int8_t f0_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"A0", &f0_value, sizeof(int8_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(i, f0_value);

    uint8_t f1_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"A1", &f1_value, sizeof(uint8_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(1 * i, f1_value);

    int16_t f2_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"A2", &f2_value, sizeof(int16_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(2 * i, f2_value);

    uint16_t f3_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"A3", &f3_value, sizeof(uint16_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(3 * i, f3_value);

    int32_t f4_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"A4", &f4_value, sizeof(int32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(4 * i, f4_value);

    uint32_t f5_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"A5", &f5_value, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(5 * i, f5_value);

    int64_t f6_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"A6", &f6_value, sizeof(int64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(6 * i, f6_value);

    uint64_t f7_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"A7", &f7_value, sizeof(uint64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(7 * i, f7_value);

    bool f8_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"A8", &f8_value, sizeof(bool), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(bool_value, f8_value);

    float f9_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"A9", &f9_value, sizeof(float), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(9 * i, f9_value);

    double f10_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"A10", &f10_value, sizeof(double), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(10 * i, f10_value);

    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"A11", &f11_value, sizeof(uint64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(11 * i, f11_value);

    char f12_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"A12", &f12_value, sizeof(char), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(1 * i, f12_value);

    unsigned char f13_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"A13", &f13_value, sizeof(unsigned char), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    unsigned char k = (unsigned char)(13 * i);
    AW_MACRO_EXPECT_EQ_INT(k, f13_value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(root, (char *)"A14", &propSize);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(propSize, strlen(f14_value) + 1);

    char string_value[7];
    ret = GmcNodeGetPropertyByName(root, (char *)"A14", &string_value, propSize, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(root, (char *)"A15", &string_value, 7, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(root, (char *)"A16", &string_value, 7, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string_value, f14_value), 0);
}

void TestGmcGetNodePropertyByName_V(GmcNodeT *root, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    bool isNull;
    int8_t f0_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"V0", &f0_value, sizeof(int8_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(i, f0_value);

    uint8_t f1_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"V1", &f1_value, sizeof(uint8_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(1 * i, f1_value);

    int16_t f2_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"V2", &f2_value, sizeof(int16_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(2 * i, f2_value);

    uint16_t f3_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"V3", &f3_value, sizeof(uint16_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(3 * i, f3_value);

    int32_t f4_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"V4", &f4_value, sizeof(int32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(4 * i, f4_value);

    uint32_t f5_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"V5", &f5_value, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(5 * i, f5_value);

    int64_t f6_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"V6", &f6_value, sizeof(int64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(6 * i, f6_value);

    uint64_t f7_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"V7", &f7_value, sizeof(uint64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(7 * i, f7_value);

    bool f8_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"V8", &f8_value, sizeof(bool), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(bool_value, f8_value);

    float f9_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"V9", &f9_value, sizeof(float), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(9 * i, f9_value);

    double f10_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"V10", &f10_value, sizeof(double), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(10 * i, f10_value);

    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"V11", &f11_value, sizeof(uint64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(11 * i, f11_value);

    char f12_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"V12", &f12_value, sizeof(char), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(1 * i, f12_value);

    unsigned char f13_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"V13", &f13_value, sizeof(unsigned char), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    unsigned char k = (unsigned char)(13 * i);
    AW_MACRO_EXPECT_EQ_INT(k, f13_value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(root, (char *)"V14", &propSize);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(propSize, strlen(f14_value) + 1);

    char string_value[7];
    ret = GmcNodeGetPropertyByName(root, (char *)"V14", &string_value, propSize, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(root, (char *)"V15", &string_value, 7, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(root, (char *)"V16", &string_value, 7, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string_value, f14_value), 0);
}

void TestGmcInsertVertex(GmcStmtT *stmt, int index, bool bool_value, char *f14_value, int start_num, int end_num,
    int array_num, int vector_num, const char *labelName)
{
    int32_t ret = 0;
    void *label = NULL;
    GmcNodeT *root, *T1, *T2, *T3;

    // 插入顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //设置根节点
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_PK(root, i * index);
        TestGmcSetNodePropertyByName_R(root, i * index, bool_value, f14_value);

        //设置T1节点
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_P(T1, i * index, bool_value, f14_value);

        // 插入array节点
        ret = GmcNodeGetChild(T1, "T2", &T2);  // T2 是在 T1 下，故第一个参数应该是 T1
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcSetNodePropertyByName_A(T2, i * index, bool_value, f14_value);
            ret = GmcNodeGetNextElement(T2, &T2);
            if (j < array_num - 1) {
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_MACRO_EXPECT_EQ_INT(GMERR_NO_DATA, ret);
            }
        }

        // 插入vector节点
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(T3, &T3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_V(T3, i * index, bool_value, f14_value);
        }
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

TEST_F(TreeModel, Tool_008_012)
{
    char const *view_name = "V\\$COM_SHMEM_CTX";
    char const *view_name1 = "V\\$COM_DYN_CTX";
    char *label_schema = NULL;
    char label_name1[] = "OP_T0";
    void *vertexLabel = NULL;
    readJanssonFile("schema_file/NormalTreeModel.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);
    GmcDropVertexLabel(g_stmt, labelName);
    ret = GmcCreateVertexLabel(g_stmt, label_schema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(label_schema);

    TestGmcInsertVertex(g_stmt, 1, 0, (char *)"string", start_num, 21, array_num, vector_num, label_name1);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s | grep -E -A 17 'index = 0' > shmem.log\n",
        g_toolPath, g_connServer, view_name);
    LOG("g_command = %s", g_command);
    ret = executeCommand(g_command);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s | grep -E -A 17 'index = 0' > dyn.log\n",
        g_toolPath, g_connServer, view_name1);
    LOG("g_command = %s", g_command);
    ret = executeCommand(g_command);

    int mem_mb_shm = 0, mem_kb_shm = 0, mem_byte_shm = 0;
    snprintf(g_command, MAX_CMD_SIZE, "cat shmem.log | grep 'GLOBAL_PHY_SIZE'\n");
    ret = get_global_phy_size(g_command, &mem_mb_shm, &mem_kb_shm, &mem_byte_shm);
    LOG("mem_mb_shm = %d, mem_kb_shm = %d, mem_byte_shm = %d", mem_mb_shm, mem_kb_shm, mem_byte_shm);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int mem_mb_dyn = 0, mem_kb_dyn = 0, mem_byte_dyn = 0;
    snprintf(g_command, MAX_CMD_SIZE, "cat dyn.log | grep 'GLOBAL_PHY_SIZE'\n");
    ret = get_global_phy_size(g_command, &mem_mb_dyn, &mem_kb_dyn, &mem_byte_dyn);
    LOG("mem_mb_dyn = %d, mem_kb_dyn = %d, mem_byte_dyn = %d", mem_mb_dyn, mem_kb_dyn, mem_byte_dyn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // system("rm -f dyn.log shmem.log");
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

TEST_F(TreeModel, Tool_008_013)
{
    char const *view_name = "V\\$COM_SHMEM_CTX";
    char const *view_name1 = "V\\$COM_DYN_CTX";
    char *label_schema = NULL;
    char label_name1[] = "OP_T0";
    void *vertexLabel = NULL;
    readJanssonFile("schema_file/NormalTreeModel.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);
    GmcDropVertexLabel(g_stmt, labelName);
    ret = GmcCreateVertexLabel(g_stmt, label_schema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(label_schema);

    TestGmcInsertVertex(g_stmt, 1, 0, (char *)"string", start_num, 21, array_num, vector_num, label_name1);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s | grep -E -A 17 'index = 0' > shmem.log\n",
        g_toolPath, g_connServer, view_name);
    LOG("g_command = %s", g_command);
    ret = executeCommand(g_command);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s | grep -E -A 17 'index = 0' > dyn.log\n",
        g_toolPath, g_connServer, view_name1);
    LOG("g_command = %s", g_command);
    ret = executeCommand(g_command);

    int mem_mb_shm = 0, mem_kb_shm = 0, mem_byte_shm = 0;
    snprintf(g_command, MAX_CMD_SIZE, "cat shmem.log | grep 'GLOBAL_PHY_SIZE'\n");
    ret = get_global_phy_size(g_command, &mem_mb_shm, &mem_kb_shm, &mem_byte_shm);
    LOG("mem_mb_shm = %d, mem_kb_shm = %d, mem_byte_shm = %d", mem_mb_shm, mem_kb_shm, mem_byte_shm);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int mem_mb_dyn = 0, mem_kb_dyn = 0, mem_byte_dyn = 0;
    snprintf(g_command, MAX_CMD_SIZE, "cat dyn.log | grep 'GLOBAL_PHY_SIZE'\n");
    ret = get_global_phy_size(g_command, &mem_mb_dyn, &mem_kb_dyn, &mem_byte_dyn);
    LOG("mem_mb_dyn = %d, mem_kb_dyn = %d, mem_byte_dyn = %d", mem_mb_dyn, mem_kb_dyn, mem_byte_dyn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // system("rm -f dyn.log shmem.log");
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

TEST_F(Tool_008, Tool_008_014)
{
    char const *view_name = "V\\$COM_SHMEM_CTX";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s\n", g_toolPath, g_connServer,
        view_name);
    LOG("g_command = %s", g_command);
    for (int loop = 0; loop < 100; loop++) {
        LOG("loop = %d", loop);
        ret = executeCommand(g_command, "SEGMENT_NUM", "MEMORY_USAGE");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

TEST_F(Tool_008, Tool_008_015)
{
    char const *view_name = "V\\$COM_DYN_CTX";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s\n", g_toolPath, g_connServer,
        view_name);
    LOG("g_command = %s", g_command);
    for (int loop = 0; loop < 100; loop++) {
        ret = executeCommand(g_command, "BIG_CHUNK_INFO", "BLOCK_INFO", "MEMORY_USAGE");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

void *Thread_Tool_008_016(void *args)
{
    char const *view_name = "V\\$COM_SHMEM_CTX";
    char command[MAX_CMD_SIZE];
    snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s\n", g_toolPath, g_connServer,
        view_name);
    LOG("command = %s", command);
    for (int loop = 0; loop < 50; loop++) {
        ret = executeCommand(command, "SEGMENT_NUM", "MEMORY_USAGE");
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != 0) {
            break;
        }
    }

    return NULL;
}
TEST_F(Tool_008, Tool_008_016)
{
#if defined ENV_RTOSV2X
#define THREAD_NUM 1
#else
#define THREAD_NUM 5
#endif

    pthread_t sameNameth[THREAD_NUM];
    for (int i = 0; i < THREAD_NUM; i++) {
        ret = pthread_create(&sameNameth[i], NULL, Thread_Tool_008_016, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    for (int i = 0; i < THREAD_NUM; i++) {
        pthread_join(sameNameth[i], NULL);
    }
}

void *Thread_Tool_008_017(void *args)
{
    char const *view_name = "V\\$COM_DYN_CTX";
    char command[MAX_CMD_SIZE];
    snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s\n", g_toolPath, g_connServer,
        view_name);
    LOG("command = %s", command);
    for (int loop = 0; loop < 50; loop++) {
        for (; ;) {
            ret = executeCommand(command, "BIG_CHUNK_INFO", "BLOCK_INFO", "MEMORY_USAGE");
            if (ret != 0) {
                ret = executeCommand(command, "WARNING: The view information is being updated, please try again!");
                EXPECT_EQ(ret == GMERR_OK || ret == -1, true);
                break;
            } else {
                break;
            }
        }
    }

    return NULL;
}

TEST_F(Tool_008, Tool_008_017)
{
    int tdNum = 5;
    pthread_t sameNameth[tdNum];
    for (int i = 0; i < tdNum; i++) {
        ret = pthread_create(&sameNameth[i], NULL, Thread_Tool_008_017, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    for (int i = 0; i < tdNum; i++) {
        pthread_join(sameNameth[i], NULL);
    }
}

TEST_F(Tool_008, Tool_008_018)
{
    char const *view_name = "V\\$COM_SHMEM_CTX";
    char const *view_name1 = "V\\$COM_DYN_CTX";
    int32_t value = 1;
    char *label_schema = NULL;
    const char *labelName0 = "demo1";
    void *vertexLabel = NULL;

    readJanssonFile("schema_file/label1.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label schema:%s", label_schema);

    char vertexLabel_config[] = "{\"max_record_count\":1000, \"isFastReadUncommitted\":0}";
    GmcDropVertexLabel(g_stmt, labelName0);
    ret = GmcCreateVertexLabel(g_stmt, label_schema, vertexLabel_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(label_schema);

    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 0; i < 10000; i++) {
        value = i;
        //设置F0属性值
        GmcTxConfigT config;
        config.transMode = GMC_TRANS_USED_IN_CS;
        config.type = GMC_TX_ISOLATION_COMMITTED;
        config.readOnly = false;
        config.trxType = GMC_DEFAULT_TRX;
        ret = GmcTransStart(g_conn, &config);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &value, sizeof(value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        //设置F1属性值
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &value, sizeof(value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        //插入顶点
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcTransRollBack(g_conn);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s | grep -E -A 17 'index = 0' > shmem.log\n",
        g_toolPath, g_connServer, view_name);
    LOG("g_command = %s", g_command);
    ret = executeCommand(g_command);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s | grep -E -A 17 'index = 0' > dyn.log\n",
        g_toolPath, g_connServer, view_name1);
    LOG("g_command = %s", g_command);
    ret = executeCommand(g_command);

    int mem_mb_shm = 0, mem_kb_shm = 0, mem_byte_shm = 0;
    snprintf(g_command, MAX_CMD_SIZE, "cat shmem.log | grep 'GLOBAL_PHY_SIZE'\n");
    ret = get_global_phy_size(g_command, &mem_mb_shm, &mem_kb_shm, &mem_byte_shm);
    LOG("mem_mb_shm = %d, mem_kb_shm = %d, mem_byte_shm = %d", mem_mb_shm, mem_kb_shm, mem_byte_shm);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int mem_mb_dyn = 0, mem_kb_dyn = 0, mem_byte_dyn = 0;
    snprintf(g_command, MAX_CMD_SIZE, "cat dyn.log | grep 'GLOBAL_PHY_SIZE'\n");
    ret = get_global_phy_size(g_command, &mem_mb_dyn, &mem_kb_dyn, &mem_byte_dyn);
    LOG("mem_mb_dyn = %d, mem_kb_dyn = %d, mem_byte_dyn = %d", mem_mb_dyn, mem_kb_dyn, mem_byte_dyn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // system("rm -f dyn.log shmem.log");
    //关闭顶点label
    ret = GmcDropVertexLabel(g_stmt, labelName0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

TEST_F(Tool_008, Tool_008_019)
{
    char const *view_name = "V\\$COM_SHMEM_CTX";
    char const *view_name1 = "V\\$COM_DYN_CTX";
    int32_t value = 1;
    char *label_schema = NULL;
    const char *labelName0 = "demo1";
    void *vertexLabel = NULL;

    readJanssonFile("schema_file/label1.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label schema:%s", label_schema);

    GmcDropVertexLabel(g_stmt, labelName0);
    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(label_schema);

    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 0; i < INSERT_NUM; i++) {
        value = i;
        //设置F0属性值
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &value, sizeof(value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        //设置F1属性值
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &value, sizeof(value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        //插入顶点
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // delete
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName0, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s | grep -E -A 17 'index = 0' > shmem.log\n",
        g_toolPath, g_connServer, view_name);
    LOG("g_command = %s", g_command);
    ret = executeCommand(g_command);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s | grep -E -A 17 'index = 0' > dyn.log\n",
        g_toolPath, g_connServer, view_name1);
    LOG("g_command = %s", g_command);
    ret = executeCommand(g_command);

    int mem_mb_shm = 0, mem_kb_shm = 0, mem_byte_shm = 0;
    snprintf(g_command, MAX_CMD_SIZE, "cat shmem.log | grep 'GLOBAL_PHY_SIZE'\n");
    ret = get_global_phy_size(g_command, &mem_mb_shm, &mem_kb_shm, &mem_byte_shm);
    LOG("mem_mb_shm = %d, mem_kb_shm = %d, mem_byte_shm = %d", mem_mb_shm, mem_kb_shm, mem_byte_shm);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int mem_mb_dyn = 0, mem_kb_dyn = 0, mem_byte_dyn = 0;
    snprintf(g_command, MAX_CMD_SIZE, "cat dyn.log | grep 'GLOBAL_PHY_SIZE'\n");
    ret = get_global_phy_size(g_command, &mem_mb_dyn, &mem_kb_dyn, &mem_byte_dyn);
    LOG("mem_mb_dyn = %d, mem_kb_dyn = %d, mem_byte_dyn = %d", mem_mb_dyn, mem_kb_dyn, mem_byte_dyn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // system("rm -f dyn.log shmem.log");

    //关闭顶点label
    ret = GmcDropVertexLabel(g_stmt, labelName0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

TEST_F(Tool_008, Tool_008_020)
{
    char const *view_name = "V\\$COM_SHMEM_CTX";
    char const *view_name1 = "V\\$COM_DYN_CTX";
    int32_t value = 1;
    char *label_schema = NULL;
    const char *labelName0 = "demo1";
    void *vertexLabel = NULL;

    readJanssonFile("schema_file/label1.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label schema:%s", label_schema);

    GmcDropVertexLabel(g_stmt, labelName0);
    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(label_schema);

    //获取顶点label
    for (int32_t i = 0; i < INSERT_NUM; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName0, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        value = i;
        //设置F0属性值
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &value, sizeof(value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        //设置F1属性值
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &value, sizeof(value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        //插入顶点
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s | grep -E -A 17 'index = 0' > shmem.log\n",
        g_toolPath, g_connServer, view_name);
    LOG("g_command = %s", g_command);
    ret = executeCommand(g_command);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s | grep -E -A 17 'index = 0' > dyn.log\n",
        g_toolPath, g_connServer, view_name1);
    LOG("g_command = %s", g_command);
    ret = executeCommand(g_command);

    int mem_mb_shm = 0, mem_kb_shm = 0, mem_byte_shm = 0;
    snprintf(g_command, MAX_CMD_SIZE, "cat shmem.log | grep 'GLOBAL_PHY_SIZE'\n");
    ret = get_global_phy_size(g_command, &mem_mb_shm, &mem_kb_shm, &mem_byte_shm);
    LOG("after insert:mem_mb_shm = %d, mem_kb_shm = %d, mem_byte_shm = %d", mem_mb_shm, mem_kb_shm, mem_byte_shm);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int mem_mb_dyn = 0, mem_kb_dyn = 0, mem_byte_dyn = 0;
    snprintf(g_command, MAX_CMD_SIZE, "cat dyn.log | grep 'GLOBAL_PHY_SIZE'\n");
    ret = get_global_phy_size(g_command, &mem_mb_dyn, &mem_kb_dyn, &mem_byte_dyn);
    LOG("after insert:mem_mb_dyn = %d, mem_kb_dyn = %d, mem_byte_dyn = %d", mem_mb_dyn, mem_kb_dyn, mem_byte_dyn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // update
    value = 99999;
    for (int32_t i = 0; i < INSERT_NUM; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName0, GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcResetVertex(g_stmt, false);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        //设置F0属性值
        value += 1;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &value, sizeof(value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        //更新顶点
        ret = GmcSetIndexKeyName(g_stmt, "PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s | grep -E -A 17 'index = 0' > shmem.log\n",
        g_toolPath, g_connServer, view_name);
    LOG("g_command = %s", g_command);
    ret = executeCommand(g_command);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s | grep -E -A 17 'index = 0' > dyn.log\n",
        g_toolPath, g_connServer, view_name1);
    LOG("g_command = %s", g_command);
    ret = executeCommand(g_command);

    snprintf(g_command, MAX_CMD_SIZE, "cat shmem.log | grep 'GLOBAL_PHY_SIZE'\n");
    ret = get_global_phy_size(g_command, &mem_mb_shm, &mem_kb_shm, &mem_byte_shm);
    LOG("after update:mem_mb_shm = %d, mem_kb_shm = %d, mem_byte_shm = %d", mem_mb_shm, mem_kb_shm, mem_byte_shm);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "cat dyn.log | grep 'GLOBAL_PHY_SIZE'\n");
    ret = get_global_phy_size(g_command, &mem_mb_dyn, &mem_kb_dyn, &mem_byte_dyn);
    LOG("after update:mem_mb_dyn = %d, mem_kb_dyn = %d, mem_byte_dyn = %d", mem_mb_dyn, mem_kb_dyn, mem_byte_dyn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // system("rm -f dyn.log shmem.log");

    //关闭顶点label
    ret = GmcDropVertexLabel(g_stmt, labelName0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

char kvTableName[128] = "KV";
char kv_configJson[128] = "{\"max_record_count\":100}";
TEST_F(Tool_008, Tool_008_021)
{
    char const *view_name = "V\\$COM_SHMEM_CTX";
    char const *view_name1 = "V\\$COM_DYN_CTX";
    //创建kv表
    GmcKvDropTable(g_stmt, kvTableName);
    ret = GmcKvCreateTable(g_stmt, kvTableName, kv_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    void *kvtable = NULL;
    //获取kv
    ret = GmcKvPrepareStmtByLabelName(g_stmt, kvTableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcKvTupleT kvInfo = {0};
    int32_t value;
    char key[10];
    for (int32_t i = 0; i < INSERT_NUM; i++) {
        snprintf(key, 10, "%d", i);
        value = i;
        //设置k-v值
        kvInfo.key = key;
        kvInfo.keyLen = strlen(key);
        kvInfo.value = &value;
        kvInfo.valueLen = sizeof(int32_t);
        ret = GmcKvSet(g_stmt, key, strlen(key), &value, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcKvRemove(g_stmt, key, strlen(key));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s | grep -E -A 17 'index = 0' > shmem.log\n",
        g_toolPath, g_connServer, view_name);
    LOG("g_command = %s", g_command);
    ret = executeCommand(g_command);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s | grep -E -A 17 'index = 0' > dyn.log\n",
        g_toolPath, g_connServer, view_name1);
    LOG("g_command = %s", g_command);
    ret = executeCommand(g_command);

    int mem_mb_shm = 0, mem_kb_shm = 0, mem_byte_shm = 0;
    snprintf(g_command, MAX_CMD_SIZE, "cat shmem.log | grep 'GLOBAL_PHY_SIZE'\n");
    ret = get_global_phy_size(g_command, &mem_mb_shm, &mem_kb_shm, &mem_byte_shm);
    LOG("mem_mb_shm = %d, mem_kb_shm = %d, mem_byte_shm = %d", mem_mb_shm, mem_kb_shm, mem_byte_shm);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int mem_mb_dyn = 0, mem_kb_dyn = 0, mem_byte_dyn = 0;
    snprintf(g_command, MAX_CMD_SIZE, "cat dyn.log | grep 'GLOBAL_PHY_SIZE'\n");
    ret = get_global_phy_size(g_command, &mem_mb_dyn, &mem_kb_dyn, &mem_byte_dyn);
    LOG("mem_mb_dyn = %d, mem_kb_dyn = %d, mem_byte_dyn = %d", mem_mb_dyn, mem_kb_dyn, mem_byte_dyn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // system("rm -f dyn.log shmem.log");

    // close kv
    // ret = GmcCloseKvTable(g_stmt);
    // AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmt, kvTableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}


static const char *gResPoolTest =
    R"({
        "name" : "resource_pool_test",
        "pool_id" : 65535,
        "start_id" : 1,
        "capacity" : 200000,
        "order" : 0,
        "alloc_type" : 0
    })";

static const char *resPoolDumpName = "resource_pool_dump";

class Resource_Pool : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh -f");  //带有资源列的表不能被删除，只能重启gmserver。
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
    }
};

void Resource_Pool::SetUp()
{
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void Resource_Pool::TearDown()
{
    GmcFreeStmt(g_stmt);
    ret = testGmcDisconnect(g_conn);
    EXPECT_EQ(GMERR_OK, ret);
}

// 一个表中含有多个资源类型的字段
TEST_F(Resource_Pool, Tool_008_023)
{
    char const *view_name = "V\\$COM_SHMEM_CTX";
    char const *view_name1 = "V\\$COM_DYN_CTX";
    const char *resPoolTestName = "resource_pool_test";
    const char *label_name = "TestResource";
    const char *label_config = R"({"max_record_count":200000})";
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    ret = GmcCreateResPool(g_stmt, gResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    readJanssonFile("schema_file/resourcePool.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);
    GmcDropVertexLabel(g_stmt, label_name);
    ret = GmcCreateVertexLabel(g_stmt, label_schema, label_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(label_schema);

    ret = GmcBindResPoolToLabel(g_stmt, resPoolTestName, label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, label_name, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 0; i < INSERT_NUM; i++) {
        if (!(i % 1000)) {
            LOG("insert:i = %d", i);
        }
        int F0Value = i;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int F1Value = i;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int F2Value = i;
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint64_t respoolId = 1;
        uint64_t count = 1;
        uint64_t startIndex = 2 + i;
        uint64_t tmpResIdx = 0;
        ret = GmcSetPoolIdResource(AUTO_POOL_ID, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetCountResource(count, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetStartIdxResource(AUTO_START_IDX, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // delete
    ret = testGmcPrepareStmtByLabelName(g_stmt, label_name, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < INSERT_NUM; i++) {
        char cond[50];
        if (!(i % 1000)) {
            LOG("delete:i = %d", i);
        }

        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &i, sizeof(i));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "T35_K0");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s | grep -E -A 17 'index = 0' > shmem.log\n",
        g_toolPath, g_connServer, view_name);
    LOG("g_command = %s", g_command);
    ret = executeCommand(g_command);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s | grep -E -A 17 'index = 0' > dyn.log\n",
        g_toolPath, g_connServer, view_name1);
    LOG("g_command = %s", g_command);
    ret = executeCommand(g_command);

    int mem_mb_shm = 0, mem_kb_shm = 0, mem_byte_shm = 0;
    snprintf(g_command, MAX_CMD_SIZE, "cat shmem.log | grep 'GLOBAL_PHY_SIZE'\n");
    ret = get_global_phy_size(g_command, &mem_mb_shm, &mem_kb_shm, &mem_byte_shm);
    LOG("mem_mb_shm = %d, mem_kb_shm = %d, mem_byte_shm = %d", mem_mb_shm, mem_kb_shm, mem_byte_shm);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int mem_mb_dyn = 0, mem_kb_dyn = 0, mem_byte_dyn = 0;
    snprintf(g_command, MAX_CMD_SIZE, "cat dyn.log | grep 'GLOBAL_PHY_SIZE'\n");
    ret = get_global_phy_size(g_command, &mem_mb_dyn, &mem_kb_dyn, &mem_byte_dyn);
    LOG("mem_mb_dyn = %d, mem_kb_dyn = %d, mem_byte_dyn = %d", mem_mb_dyn, mem_kb_dyn, mem_byte_dyn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // system("rm -f dyn.log shmem.log");

    ret = GmcUnbindResPoolFromLabel(g_stmt, label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *resPoolJson;
    ret = GmcGetResPool(g_stmt, resPoolTestName, &resPoolJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(172, strlen(resPoolJson));

    ret = GmcDestroyResPool(g_stmt, resPoolTestName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);  // 2021.12.15 现支持删除 解绑后的资源表
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(Tool_008, Tool_008_025)
{
    int32_t value = 1;
    char *label_schema = NULL;
    const char *labelName0 = "demo1";
    void *vertexLabel = NULL;
    char const *view_name = "V\\$COM_SHMEM_CTX";
    char const *view_name1 = "V\\$COM_DYN_CTX";

    readJanssonFile("schema_file/label1.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label schema:%s", label_schema);

    GmcDropVertexLabel(g_stmt, labelName0);
    ret = GmcCreateVertexLabel(g_stmt, label_schema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(label_schema);

    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 0; i < 1000000; i++) {
        value = i;
        //设置F0属性值
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &value, sizeof(value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        //设置F1属性值
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &value, sizeof(value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        //插入顶点
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s | grep -E -A 17 'index = 0' > shmem.log\n",
        g_toolPath, g_connServer, view_name);
    LOG("g_command = %s", g_command);
    ret = executeCommand(g_command);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s | grep -E -A 17 'index = 0' > dyn.log\n",
        g_toolPath, g_connServer, view_name1);
    LOG("g_command = %s", g_command);
    ret = executeCommand(g_command);

    int mem_mb_shm = 0, mem_kb_shm = 0, mem_byte_shm = 0;
    snprintf(g_command, MAX_CMD_SIZE, "cat shmem.log | grep 'GLOBAL_PHY_SIZE'\n");
    ret = get_global_phy_size(g_command, &mem_mb_shm, &mem_kb_shm, &mem_byte_shm);
    LOG("mem_mb_shm = %d, mem_kb_shm = %d, mem_byte_shm = %d", mem_mb_shm, mem_kb_shm, mem_byte_shm);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int mem_mb_dyn = 0, mem_kb_dyn = 0, mem_byte_dyn = 0;
    snprintf(g_command, MAX_CMD_SIZE, "cat dyn.log | grep 'GLOBAL_PHY_SIZE'\n");
    ret = get_global_phy_size(g_command, &mem_mb_dyn, &mem_kb_dyn, &mem_byte_dyn);
    LOG("mem_mb_dyn = %d, mem_kb_dyn = %d, mem_byte_dyn = %d", mem_mb_dyn, mem_kb_dyn, mem_byte_dyn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // system("rm -f dyn.log shmem.log");
    //关闭顶点label
    ret = GmcDropVertexLabel(g_stmt, labelName0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

TEST_F(Tool_008, Tool_008_026)
{
    int32_t value = 1;
    char *label_schema = NULL;
    const char *labelName0 = "demo1";
    void *vertexLabel = NULL;
    char const *view_name = "V\\$COM_SHMEM_CTX";
    char const *view_name1 = "V\\$COM_DYN_CTX";

    readJanssonFile("schema_file/label1.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label schema:%s", label_schema);

    GmcDropVertexLabel(g_stmt, labelName0);
    ret = GmcCreateVertexLabel(g_stmt, label_schema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(label_schema);

    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 0; i < 1000000; i++) {
        value = i;
        //设置F0属性值
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &value, sizeof(value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        //设置F1属性值
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &value, sizeof(value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        //插入顶点
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s | grep -E -A 17 'index = 0' > shmem.log\n",
        g_toolPath, g_connServer, view_name);
    LOG("g_command = %s", g_command);
    ret = executeCommand(g_command);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s | grep -E -A 17 'index = 0' > dyn.log\n",
        g_toolPath, g_connServer, view_name1);
    LOG("g_command = %s", g_command);
    ret = executeCommand(g_command);

    int mem_mb_shm = 0, mem_kb_shm = 0, mem_byte_shm = 0;
    snprintf(g_command, MAX_CMD_SIZE, "cat shmem.log | grep 'GLOBAL_PHY_SIZE'\n");
    ret = get_global_phy_size(g_command, &mem_mb_shm, &mem_kb_shm, &mem_byte_shm);
    LOG("mem_mb_shm = %d, mem_kb_shm = %d, mem_byte_shm = %d", mem_mb_shm, mem_kb_shm, mem_byte_shm);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int mem_mb_dyn = 0, mem_kb_dyn = 0, mem_byte_dyn = 0;
    snprintf(g_command, MAX_CMD_SIZE, "cat dyn.log | grep 'GLOBAL_PHY_SIZE'\n");
    ret = get_global_phy_size(g_command, &mem_mb_dyn, &mem_kb_dyn, &mem_byte_dyn);
    LOG("mem_mb_dyn = %d, mem_kb_dyn = %d, mem_byte_dyn = %d", mem_mb_dyn, mem_kb_dyn, mem_byte_dyn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // system("rm -f dyn.log shmem.log");
    //关闭顶点label
    ret = GmcDropVertexLabel(g_stmt, labelName0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
