[{"comment": "IF AFT", "version": "2.0", "type": "record", "name": "ddl_general", "config": {"check_validity": true}, "max_record_count": 128000, "fields": [{"name": "ifindex", "type": "uint32"}, {"name": "name", "type": "fixed", "size": 6, "nullable": true}, {"name": "vrid", "type": "uint32", "nullable": true}, {"name": "if_type", "type": "uint32", "nullable": true}, {"name": "shutdown", "type": "uint32", "nullable": true}, {"name": "linkup", "type": "uint32", "nullable": true}, {"name": "tbtp", "type": "uint32", "nullable": true}, {"name": "tb", "type": "uint32", "nullable": true}, {"name": "tp", "type": "uint32", "nullable": true}, {"name": "port_switch", "type": "uint32", "nullable": true}, {"name": "forwardType", "type": "uint32", "nullable": true}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "fixed", "size": 6, "nullable": true}, {"name": "ipv4_mtu", "type": "uint16", "nullable": true}, {"name": "ipv6_mtu", "type": "uint16", "nullable": true}, {"name": "on_board", "type": "uint32", "nullable": true}, {"name": "lagid", "type": "uint32", "nullable": true}, {"name": "ifm", "type": "record", "fields": [{"name": "simple_name", "type": "uint32", "nullable": true}, {"name": "description", "type": "fixed", "size": 6, "nullable": true}, {"name": "is_configure", "type": "uint32", "nullable": true}, {"name": "main_ifindex", "type": "uint32", "nullable": true}, {"name": "sub_max_num", "type": "uint32", "nullable": true}, {"name": "sub_curr_num", "type": "uint32", "nullable": true}, {"name": "error_down", "type": "uint32", "nullable": true}, {"name": "statistic", "type": "uint32", "nullable": true}, {"name": "vsys_id", "type": "uint32", "nullable": true}, {"name": "zone_id", "type": "uint32", "nullable": true}, {"name": "last_up_time", "type": "uint32", "nullable": true}, {"name": "last_down_time", "type": "uint32", "nullable": true}]}, {"name": "dev", "type": "record", "fields": [{"name": "dev_id", "type": "uint32", "nullable": true}, {"name": "chassis_id", "type": "uint32", "nullable": true}, {"name": "slot_id", "type": "uint32", "nullable": true}, {"name": "card_id", "type": "uint32", "nullable": true}, {"name": "unit_id", "type": "uint32", "nullable": true}, {"name": "port_id", "type": "uint32", "nullable": true}]}, {"name": "l2", "type": "record", "comment": "二层属性", "fields": [{"name": "trunk_id", "type": "uint32", "nullable": true}, {"name": "vlan_id", "type": "uint32", "nullable": true}, {"name": "l2_portindex", "type": "uint32", "nullable": true}, {"name": "vsi", "type": "uint32", "nullable": true}, {"name": "tunnel_id", "type": "uint32", "nullable": true}]}, {"name": "port", "type": "record", "fields": [{"name": "speed", "type": "uint32", "nullable": true}, {"name": "duplex", "type": "uint32", "nullable": true}, {"name": "flow_control", "type": "uint32", "nullable": true}, {"name": "phy_type", "type": "uint32", "nullable": true}, {"name": "jumbo", "type": "uint32", "nullable": true}, {"name": "baud", "type": "uint32", "nullable": true}, {"name": "rmon", "type": "uint32", "nullable": true}, {"name": "phy_link", "type": "uint32", "nullable": true}, {"name": "if_mib", "type": "uint32", "nullable": true}, {"name": "on_board", "type": "uint32", "nullable": true}]}, {"name": "T1_V", "type": "record", "vector": true, "size": 64, "fields": [{"name": "V1", "type": "uint32", "nullable": true}, {"name": "V2", "type": "uint32", "nullable": true}]}], "keys": [{"name": "if_pk", "index": {"type": "primary"}, "node": "ddl_general", "fields": ["ifindex"], "constraints": {"unique": true}}, {"name": "ifname_pk", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "ddl_general", "fields": ["name"], "constraints": {"unique": false}}, {"name": "local_pk", "index": {"type": "local"}, "node": "ddl_general", "fields": ["vrid", "if_type"], "constraints": {"unique": false}}, {"name": "mem_keyv", "index": {"type": "none"}, "node": "T1_V", "fields": ["V1"]}]}]