{"label_name": "test_frm_inst", "comment": "VertexLabel1 subscription", "persist": false, "subs_type": "message_queue", "type": "before_commit", "is_full_sync": false, "events": [{"type": "replace", "msgTypes": ["new object"]}, {"type": "merge insert", "msgTypes": ["new object"]}, {"type": "update", "msgTypes": ["new object"]}, {"type": "merge update", "msgTypes": ["new object"]}, {"type": "delete", "msgTypes": ["key"]}, {"type": "age", "msgTypes": ["key"]}, {"type": "initial_load", "msgTypes": ["new object"]}], "is_reliable": false, "is_path": false}