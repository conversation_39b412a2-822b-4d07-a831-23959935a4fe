/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: 04.SubtreeMulti
 * Author: hanyang
 * Create: 2025-04-18
 */
#include "SubtreeMulti.h"
#include "t_rd_inject.h"

GmcConnT *g_conn_async = NULL;
GmcStmtT *g_stmt_async = NULL;
GmcStmtT *g_stmt_root1 = NULL;
GmcStmtT *g_stmt_root2 = NULL;
GmcNodeT *g_rootNode1 = NULL;
GmcNodeT *g_rootNode2 = NULL;
GmcNodeT *g_childNode[100] = {0};

class SubtreeMulti_test : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void SubtreeMulti_test::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/gmserver_replace.sh inject");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("sh $TEST_HOME/tools/start.sh -f");
    int ret = testEnvInit();
    ASSERT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void SubtreeMulti_test::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh $TEST_HOME/tools/gmserver_replace.sh recover");
}


void SubtreeMulti_test::SetUp()
{
    int ret;
    AsyncUserDataT data = {0};

    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_root1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_root2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = g_namespace;
    nspCfg.tablespaceName = NULL;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观

    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // Yang事务配置为乐观+可重复读，需要和namespace保持一致
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = GmcUseNamespaceAsync(g_stmt_async, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 创建表
    TestCreateLabel(g_stmt_async);
    g_printFlag = true;

    AW_CHECK_LOG_BEGIN();
}

void SubtreeMulti_test::TearDown()
{
    int ret;
    uint32_t i = 0;
    AsyncUserDataT data = {0};
    AW_CHECK_LOG_END();

    // 删除表
    TestDropLabel(g_stmt_async);

    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_async, g_namespace, drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    GmcFreeStmt(g_stmt_root1);
    GmcFreeStmt(g_stmt_root2);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_conn_async = NULL;
    g_stmt_async = NULL;
    g_stmt_root1 = NULL;
    g_stmt_root2 = NULL;

    g_rootNode1 = NULL;
    g_rootNode2 = NULL;
    for (i = 0; i < 100; i++) {
        g_childNode[i] = NULL;
    }
    AW_CHECK_LOG_END();
}

/*
root--con_1
    --con_2--con_2_1--con_2_1_1--con_2_1_1_1
                    --con_2_1_2
*/
/*****************************************************************************
 * Description  : 001.创建多层数节点，subtreebatch查询，第一个条件查询数据为空，第二个条件触发数据分层，查询数据不为空
 * Input        : None
 * Output       : None
 * Author       : hanyang
 * Modification : DTS2025041632296
 * *****************************************************************************/
TEST_F(SubtreeMulti_test, Yang_Inj_DTS2025041632296_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    ret = TestCollectlibPath("subtree_batch_4_time8_inject");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestInjectCommand();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "Stub successfully.");

    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root1, &g_rootNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode1, "con_2", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_childNode[1], "con_2_1", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_childNode[2], "con_2_1_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestYangSetNodeProperty(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_childNode[3], "con_2_1_1_1", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestYangSetNodeProperty(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_childNode[2], "con_2_1_2", GMC_OPERATION_INSERT, &g_childNode[5]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestYangSetNodeProperty(g_childNode[5], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    // 第一个查询条件
    ret = testGmcPrepareStmtByLabelName(g_stmt_root1, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode1 = NULL;
    ret = GmcGetRootNode(g_stmt_root1, &g_rootNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode1, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 第二个查询条件
    ret = testGmcPrepareStmtByLabelName(g_stmt_root2, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode2 = NULL;
    ret = GmcGetRootNode(g_stmt_root2, &g_rootNode2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode2, "con_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "con_2_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[2], "con_2_1_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangSetField(g_childNode[3], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSubtreeFilterObjMulti(g_stmt_root1, g_rootNode1, g_rootNode2, "root");

    ret = TestUnInjectCommand();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestRemoveInjectFile();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "END");
}
