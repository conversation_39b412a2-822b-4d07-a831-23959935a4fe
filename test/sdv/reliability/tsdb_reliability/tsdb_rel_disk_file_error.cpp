
/*****************************************************************************
 Description  : 磁盘写文件错误
 History      :
 Author       : qwx 620469
 Modification :
 Date         : 2025-4-20
*****************************************************************************/

#include "tsdb_incre_common.h"

class tsdb_rel_disk_file_error : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}
};

void tsdb_rel_disk_file_error::SetUp()
{
    printf("[INFO] Persistence cfg test Start.\n");
    int ret = ChangeTsGmserverCfg((char *)"recover", NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    (void)sprintf(g_dbFilePath, "%s/gmdb", pwdDir);
    (void)Rmdir(g_dbFilePath);
    system("rm /data/gmdb/* -rf");
    system("rm ./gmdb/* -rf");
    system("ipcrm -a");
    ret = mkdir(g_dbFilePath, S_IRUSR | S_IWUSR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeTsGmserverCfg((char *)"dataFileDirPath", g_dbFilePath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
// euler和环境环境清共享内存
#if defined RUN_INDEPENDENT
    system("sh $TEST_HOME/tools/stop.sh -ts");
#endif
    AW_CHECK_LOG_BEGIN();
}

void tsdb_rel_disk_file_error::TearDown()
{
    // 持久化目录只读
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "%s/gmdb", pwdDir);
    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception 2\n");
    int ret = DisConnAndClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeTsGmserverCfg((char *)"recover", NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_FILE_OPERATE_FAILED);
    AW_CHECK_LOG_END();
    system("chattr -R -i gmdb/");
    system("rm ./gmdb/* -rf");
// euler和环境环境清共享内存
#if defined RUN_INDEPENDENT
    system("sh $TEST_HOME/tools/stop.sh -f");
#endif
    // 重启服务，避免构建卡死
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("rm ./gmdb/* -rf");
    system("rm /data/gmdb/* -rf");
    system("sh $TEST_HOME/tools/start.sh -ts");
    printf("[INFO] Incremental Persistence cfg test End.\n");
}

// 磁盘文件错误，建连
TEST_F(tsdb_rel_disk_file_error, tsdb_rel_disk_file_error_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/stop.sh -ts");
    int ret = ChangeTsGmserverCfg((char *)"redoFileSize", (char *)"16");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/start.sh -ts");

    // 持久化目录只读
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "%s/gmdb", pwdDir);

    // 注入异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw inject %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");

    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw query");
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");
    sleep(3);

    // 建连
    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");

    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建连
    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 磁盘文件错误，建表，故障恢复后继续建表
TEST_F(tsdb_rel_disk_file_error, tsdb_rel_disk_file_error_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/stop.sh -ts");
    int ret = ChangeTsGmserverCfg((char *)"redoFileSize", (char *)"16");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/start.sh -ts");

    ret = InitAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int tableNum = 1;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);

    // 持久化目录只读
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "%s/gmdb", pwdDir);

    // 注入异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw inject %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");

    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw query");
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");
    sleep(3);

    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FILE_OPERATE_FAILED, ret);

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");

    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 磁盘文件错误，插入数据
TEST_F(tsdb_rel_disk_file_error, tsdb_rel_disk_file_error_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/stop.sh -ts");
    int ret = ChangeTsGmserverCfg((char *)"redoFileSize", (char *)"16");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/start.sh -ts");
    ret = InitAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int tableNum = 1;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 持久化目录只读
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "%s/gmdb", pwdDir);

    // 注入异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw inject %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");

    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw query");
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");
    sleep(3);
    Test_dml_acl2(g_conn_sync, g_stmt_sync, "testdb0", 20);

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");

    Test_dml_acl2(g_conn_sync, g_stmt_sync, "testdb0", 20);

    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_DATABASE_NOT_AVAILABLE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 磁盘文件错误，disk_limit
TEST_F(tsdb_rel_disk_file_error, tsdb_rel_disk_file_error_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/stop.sh -ts");

    int ret = ChangeTsGmserverCfg((char *)"redoFileSize", (char *)"16");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/start.sh -ts");
    ret = InitAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int tableNum = 1;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    Test_dml_acl(g_conn_sync, g_stmt_sync, "testdb0", 20);

    char g_command[1024];
    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "ROW_CNT: 600");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 持久化目录只读
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "%s/gmdb", pwdDir);

    // 注入异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw inject %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");

    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw query");
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");
    sleep(3);

    // 在线修改disk_limit
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "alter table %s set (disk_limit = '1 MB')", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FILE_OPERATE_FAILED, ret);

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");

    // 故障取消后视图查询后台任务生效
    g_command[1024];
    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(g_command);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "Can not exec sysview sql, ret = 1019004");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 磁盘文件错误，ttl
TEST_F(tsdb_rel_disk_file_error, tsdb_rel_disk_file_error_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"tsLcmCheckPeriod=3\" ");
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("rm ./gmdb/* -rf");
    system("rm /data/gmdb -rf");
    int ret = ChangeTsGmserverCfg((char *)"redoFileSize", (char *)"16");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/start.sh -ts");
    ret = InitAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int tableNum = 1;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    Test_dml_acl(g_conn_sync, g_stmt_sync, "testdb0", 20);

    char g_command[1024];
    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "ROW_CNT: 600");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 持久化目录只读
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "%s/gmdb", pwdDir);

    // 注入异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw inject %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");

    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw query");
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");
    sleep(3);

    sleep(5);

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");

    sleep(6);

    // 故障取消后视图查询后台任务生效
    g_command[1024];
    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(g_command);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "Can not exec sysview sql, ret = 1019004");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 磁盘文件错误，查询视图
TEST_F(tsdb_rel_disk_file_error, tsdb_rel_disk_file_error_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/stop.sh -ts");
    int ret = ChangeTsGmserverCfg((char *)"redoFileSize", (char *)"16");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/start.sh -ts");
    ret = InitAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int tableNum = 1;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    Test_dml_acl(g_conn_sync, g_stmt_sync, "testdb0", 20);

    // 持久化目录只读
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "%s/gmdb", pwdDir);

    // 注入异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw inject %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");

    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw query");
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");
    sleep(3);

    // 校验
    const char *queryCommand = "select * from testdb0;";
    ret = GmcExecDirect(g_stmt_sync, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_FILE_OPERATE_FAILED);

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");

    // 查逻辑表会失败，但不会锁库
    int data_num = 30;
    int count = 20;
    uint32_t i = 0;
    bool eof = false;
    // 校验
    queryCommand = "select * from testdb0;";
    ret = GmcExecDirect(g_stmt_sync, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    while (!eof) {
        ret = GmcFetch(g_stmt_sync, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)(data_num * count));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 磁盘文件错误，查询逻辑表
TEST_F(tsdb_rel_disk_file_error, tsdb_rel_disk_file_error_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/stop.sh -ts");
    int ret = ChangeTsGmserverCfg((char *)"redoFileSize", (char *)"16");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/start.sh -ts");
    ret = InitAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int tableNum = 1;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    Test_dml_acl(g_conn_sync, g_stmt_sync, "testdb0", 20);

    // 持久化目录只读
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "%s/gmdb", pwdDir);

    // 注入异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw inject %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");

    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw query");
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");
    sleep(3);

    // 校验
    const char *queryCommand = "select * from testdb0;";
    ret = GmcExecDirect(g_stmt_sync, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_FILE_OPERATE_FAILED);
    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");

    // 校验
    int data_num = 30;
    int count = 20;
    uint32_t i = 0;
    bool eof = false;
    // 校验
    queryCommand = "select * from testdb0;";
    ret = GmcExecDirect(g_stmt_sync, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    while (!eof) {
        ret = GmcFetch(g_stmt_sync, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)(data_num * count));

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 磁盘文件错误，查询内存表
TEST_F(tsdb_rel_disk_file_error, tsdb_rel_disk_file_error_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/stop.sh -ts");
    int ret = ChangeTsGmserverCfg((char *)"redoFileSize", (char *)"16");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/start.sh -ts");
    ret = InitAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 持久化目录只读
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "%s/gmdb", pwdDir);

    // 建表、写入数据
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet,"
        " description text, id1 integer, time1 integer, "
        "INDEX idx1(id))with (enGine = 'mEmOry', max_size =1000000, time_col = 'time', interval = '1 hour' );",
        "testdb0");
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    constexpr int64_t count = 10;
    ret = insertOrderData(g_stmt_sync, "testdb0", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 注入异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw inject %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");

    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw query");
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");
    sleep(3);

    // 校验
    const char *queryCommand = "select * from testdb0;";
    ret = GmcExecDirect(g_stmt_sync, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");

    // 查逻辑表会成功，但不会锁库
    uint32_t i = 0;
    bool eof = false;
    // 校验
    queryCommand = "select * from testdb0;";
    ret = GmcExecDirect(g_stmt_sync, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    while (!eof) {
        ret = GmcFetch(g_stmt_sync, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, 10);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 磁盘文件错误，tsdb_aging
TEST_F(tsdb_rel_disk_file_error, tsdb_rel_disk_file_error_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/stop.sh -ts");

    int ret = ChangeTsGmserverCfg((char *)"redoFileSize", (char *)"16");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/start.sh -ts");
    ret = InitAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int tableNum = 1;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    Test_dml_acl(g_conn_sync, g_stmt_sync, "testdb0", 20);

    char g_command[1024];
    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "ROW_CNT: 600");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 持久化目录只读
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "%s/gmdb", pwdDir);

    // 注入异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw inject %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");

    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw query");
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");
    sleep(3);

    // 磁盘文件错误，tsdb_aging
    char sqlCmd[512] = {0};
    uint32_t cmdLen = 0;
    (void)sprintf(sqlCmd, "SELECT tsdb_aging('%s');", g_tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FILE_OPERATE_FAILED, ret);

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");

    // 故障取消后视图查询后台任务生效
    g_command[1024];
    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(g_command);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "Can not exec sysview sql, ret = 1019004");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 磁盘文件错误，主动Truncate
TEST_F(tsdb_rel_disk_file_error, tsdb_rel_disk_file_error_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/stop.sh -ts");

    int ret = ChangeTsGmserverCfg((char *)"redoFileSize", (char *)"16");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/start.sh -ts");
    ret = InitAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int tableNum = 1;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    Test_dml_acl(g_conn_sync, g_stmt_sync, "testdb0", 20);

    char g_command[1024];
    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "ROW_CNT: 600");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 持久化目录只读
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "%s/gmdb", pwdDir);

    // 注入异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw inject %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");

    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw query");
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");
    sleep(3);

    // 主动Truncate
    char sqlCmd[512] = "truncate TABLE testdb0;";
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FILE_OPERATE_FAILED, ret);

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");

    // 故障取消后视图查询后台任务生效
    g_command[1024];
    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(g_command);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "Can not exec sysview sql, ret = 1019004");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 磁盘文件错误，在线修改表结构
TEST_F(tsdb_rel_disk_file_error, tsdb_rel_disk_file_error_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/stop.sh -ts");

    int ret = ChangeTsGmserverCfg((char *)"redoFileSize", (char *)"16");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/start.sh -ts");
    ret = InitAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int tableNum = 1;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    Test_dml_acl(g_conn_sync, g_stmt_sync, "testdb0", 20);

    // 持久化目录只读
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "%s/gmdb", pwdDir);

    // 注入异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw inject %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");

    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw query");
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");
    sleep(3);

    char sqlCmd[512];

    // 新增列
    sprintf(sqlCmd, "alter table testdb0 add ip2 inet;");
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FILE_OPERATE_FAILED, ret);

    // 新增列
    sprintf(sqlCmd, "alter table testdb0 add  ns2 text;");
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);

    // 新增列
    sprintf(sqlCmd, "alter table testdb0 add message blob;");
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);

    // 新增列
    sprintf(sqlCmd, "alter table testdb0 add message2 blob;");
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);

    // 新增列
    sprintf(sqlCmd, "alter table testdb0 add id2 integer;");
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");

    char g_command[1024];
    snprintf(g_command, 1024, "gmsysview -sql \"select * from  testdb0 limit 3\" -s %s", g_connServerTsdb);
    ret = system(g_command);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "Can not exec sysview sql, ret = 1019004");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 磁盘文件错误，备份目录
TEST_F(tsdb_rel_disk_file_error, tsdb_rel_disk_file_error_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/stop.sh -ts");

    int ret = ChangeTsGmserverCfg((char *)"redoFileSize", (char *)"16");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/start.sh -ts");

    ret = InitAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 持久化目录只读
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "%s/gmdb", pwdDir);

    // 注入异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw inject %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");

    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw query");
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");
    sleep(3);

    char sqlCmd[512];
    // 备份目录
    ret = GmcFlushDataBackup(g_stmt_sync, g_newDataDir, false, GMC_DATABASE_BACKUP_SCHEMA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FILE_OPERATE_FAILED, ret);

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");

    system("rm /data/gmdb -rf");

    // 备份目录
    ret = GmcFlushDataBackup(g_stmt_sync, g_newDataDir, false, GMC_DATABASE_BACKUP_SCHEMA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);

    // 杀掉服务
    system("sh $TEST_HOME/tools/stop.sh -ts");

    // 切换起服务地址
    (void)sprintf(sqlCmd, "sh $TEST_HOME/tools/modifyCfg.sh -ts \"dataFileDirPath=%s\"", g_newDataDir);
    system(sqlCmd);
    // 使用新目录重启服务
    system("sh $TEST_HOME/tools/start.sh -ts");

    ret = InitAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int tableNum = 1;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    Test_dml_acl(g_conn_sync, g_stmt_sync, "testdb0", 20);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 磁盘文件错误，在线目录切换
TEST_F(tsdb_rel_disk_file_error, tsdb_rel_disk_file_error_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/stop.sh -ts");

    int ret = ChangeTsGmserverCfg((char *)"redoFileSize", (char *)"16");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    InitTsCiCfgModify();

    ret = InitAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int tableNum = 1;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    Test_dml_acl(g_conn_sync, g_stmt_sync, "testdb0", 20);

    // 持久化目录只读
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "%s/gmdb", pwdDir);

    // 注入异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw inject %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");

    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw query");
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");
    sleep(3);

    // 切换目录
    ret = GmcSwapDataDirSleep(g_stmt_sync, g_newDataDir, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");

    // 切换目录
    ret = GmcSwapDataDirSleep(g_stmt_sync, g_dbFilePath, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = DropCmTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

int thr_count = 0;
void *tsdb_rel_disk_file_error_014_001(void *args)
{
    int ret = 0;
    int m = *(int *)args;
    GmcConnT *g_conn_sync1 = NULL;  //  conn 句柄
    GmcStmtT *g_stmt_sync = NULL;   //  stmt 句柄
    YangConnOptionT tsConnOptions = {0};
    tsConnOptions.isCsMode = true;

    ret = TestTsGmcConnect(&g_conn_sync1, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    Test_dml_acl2(g_conn_sync, g_stmt_sync, "testdb0", 20);

    ret = testGmcDisconnect(g_conn_sync1, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    thr_count++;
    return 0;
}

void *tsdb_rel_disk_file_error_014_002(void *args)
{
    int ret = 0;
    int m = *(int *)args;
    GmcConnT *g_conn_sync1 = NULL;  //  conn 句柄
    GmcStmtT *g_stmt_sync = NULL;   //  stmt 句柄
    YangConnOptionT tsConnOptions = {0};
    tsConnOptions.isCsMode = true;

    ret = TestTsGmcConnect(&g_conn_sync1, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验
    const char *queryCommand = "select * from testdb0;";
    ret = GmcExecDirect(g_stmt_sync, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = testGmcDisconnect(g_conn_sync1, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    thr_count++;
    return 0;
}

void *tsdb_rel_disk_file_error_014_003(void *args)
{
    int ret = 0;
    int m = *(int *)args;
    // 持久化目录只读
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "%s/gmdb", pwdDir);

    // 注入异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw inject %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");

    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw query");
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");
    sleep(3);

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");

    thr_count++;
    return 0;
}

// 并发磁盘文件错误，写入、查询数据
TEST_F(tsdb_rel_disk_file_error, tsdb_rel_disk_file_error_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/stop.sh -ts");
    int ret = ChangeTsGmserverCfg((char *)"redoFileSize", (char *)"16");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/start.sh -ts");
    ret = InitAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int tableNum = 1;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_t thr1, thr2, thr3;
    int thr_num = 1;
    int index[thr_num];
    for (int k = 0; k < thr_num; k++) {
        index[k] = k;
        ret = pthread_create(&thr1, NULL, tsdb_rel_disk_file_error_014_001, (void *)&index[k]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = pthread_create(&thr2, NULL, tsdb_rel_disk_file_error_014_002, (void *)&index[k]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = pthread_create(&thr3, NULL, tsdb_rel_disk_file_error_014_003, (void *)&index[k]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    for (int k = 0; k < thr_num; k++) {
        ret = pthread_join(thr1, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = pthread_join(thr2, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = pthread_join(thr3, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    while (thr_count < 3) {
        sleep(3);
    }

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 磁盘文件错误，插入数据 DTS2025041910722
TEST_F(tsdb_rel_disk_file_error, tsdb_rel_disk_file_error_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("ipcrm -a");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    int ret = ChangeTsGmserverCfg((char *)"redoFileSize", (char *)"16");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/start.sh -ts");
    // 持久化目录只读
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "%s/gmdb", pwdDir);

    // 注入异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw inject %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");

    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw query");
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");
    sleep(3);

    ret = InitAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查db视图CONFIG_PARAMETERS会报错，会锁库

    int tableNum = 1;
    // rfile_unwrite故障中间建了表，db就会锁库，只能重启db恢复，如果故障中间不进行操作，db就不会感知不会锁库
    //  建表、写入数据
    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");

    // 切换目录
    ret = GmcSwapDataDirSleep(g_stmt_sync, g_newDataDir, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 切换目录
    ret = GmcSwapDataDirSleep(g_stmt_sync, g_command1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver_ts");
    system("sh $TEST_HOME/tools/stop.sh -ts");
    sleep(1);
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(1);
    system("ps -ef |grep gmserver_ts");

    // 故障恢复后需要重新建连
    ret = InitAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    Test_dml_acl(g_conn_sync, g_stmt_sync, "testdb0", 20);

    int data_num = 30;
    int count = 20;
    // 校验
    const char *queryCommand = "select * from testdb0;";
    ret = GmcExecDirect(g_stmt_sync, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    uint32_t i = 0;
    bool eof = false;
    while (!eof) {
        ret = GmcFetch(g_stmt_sync, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)(data_num * count));

    ret = RestartAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    data_num = 30;
    count = 20;
    // 校验
    queryCommand = "select * from testdb0;";
    ret = GmcExecDirect(g_stmt_sync, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    i = 0;
    eof = false;
    while (!eof) {
        ret = GmcFetch(g_stmt_sync, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)(data_num * count));

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 磁盘文件错误，建表
TEST_F(tsdb_rel_disk_file_error, tsdb_rel_disk_file_error_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/stop.sh -ts");
    int ret = ChangeTsGmserverCfg((char *)"redoFileSize", (char *)"16");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/start.sh -ts");
    ret = InitAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 持久化目录只读
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "%s/gmdb", pwdDir);

    // 注入异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw inject %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");

    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw query");
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");
    sleep(3);

    // 建表、写入数据
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet,"
        " description text, id1 integer, time1 integer, "
        "INDEX idx1(id))with (enGine = 'mEmOry', max_size =1000000, time_col = 'time', interval = '1 hour' );",
        "testdb0");
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FILE_OPERATE_FAILED, ret);

    constexpr int64_t count = 10;
    ret = insertOrderData(g_stmt_sync, "testdb0", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);

    // 校验
    const char *queryCommand = "select * from testdb0;";
    ret = GmcExecDirect(g_stmt_sync, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_DATABASE_NOT_AVAILABLE);

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");

    // 查逻辑表会成功，但不会锁库
    uint32_t i = 0;
    bool eof = false;
    // 校验
    queryCommand = "select * from testdb0;";
    ret = GmcExecDirect(g_stmt_sync, queryCommand, 150);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_DATABASE_NOT_AVAILABLE);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 磁盘文件错误，写数据
TEST_F(tsdb_rel_disk_file_error, tsdb_rel_disk_file_error_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/stop.sh -ts");
    int ret = ChangeTsGmserverCfg((char *)"redoFileSize", (char *)"16");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/start.sh -ts");
    ret = InitAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 持久化目录只读
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "%s/gmdb", pwdDir);

    // 建表、写入数据
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet,"
        " description text, id1 integer, time1 integer, "
        "INDEX idx1(id))with (enGine = 'mEmOry', max_size =1000000, time_col = 'time', interval = '1 hour' );",
        "testdb0");
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 注入异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw inject %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");

    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw query");
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");
    sleep(3);

    constexpr int64_t count = 10;
    ret = insertOrderData(g_stmt_sync, "testdb0", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FILE_OPERATE_FAILED, ret);

    // 校验
    const char *queryCommand = "select * from testdb0;";
    ret = GmcExecDirect(g_stmt_sync, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_DATABASE_NOT_AVAILABLE);

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");

    // 查逻辑表会成功，但不会锁库
    uint32_t i = 0;
    bool eof = false;
    // 校验
    queryCommand = "select * from testdb0;";
    ret = GmcExecDirect(g_stmt_sync, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_DATABASE_NOT_AVAILABLE);

    AW_FUN_Log(LOG_STEP, "test end.");
}
