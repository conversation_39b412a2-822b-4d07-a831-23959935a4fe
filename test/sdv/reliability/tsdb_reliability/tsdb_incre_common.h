/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: incre_pst_common.h
 * Author: qwx620469
 * Create: 2024-12-12
 */
#ifndef INCRE_COMMON_H
#define INCRE_COMMON_H
extern "C" {}

#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/prctl.h>
#include <sys/socket.h>
#include <sys/epoll.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <sys/ipc.h>  // 共享内存需要的头文件
#include <sys/shm.h>  // 共享内存需要的头文件
#include <assert.h>
#include <errno.h>
#include <stdint.h>
#include <typeinfo>
#include <limits.h>
#include <semaphore.h>
#include <string>
#include "gtest/gtest.h"
#include "jansson.h"
#include "t_datacom_lite.h"
#include "../../common/include/component/t_rd_ts.h"
#include "gmc_persist.h"
#include "gmc_test.h"
#include "t_light.h"
#include "gmc_ts_persist.h"
#define LOG_IFERR(ret)                                                                                        \
    do {                                                                                                      \
        if ((ret) != T_OK) {                                                                                  \
            fprintf(stdout, "Error: %s:%d func:%s " #ret " = %d\n", __FILE__, __LINE__, __FUNCTION__, (ret)); \
        }                                                                                                     \
    } while (0)

GmcConnT *g_conn_sync = NULL;
GmcStmtT *g_stmt_sync = NULL;
GmcStmtT *g_stmtAsync = NULL;
GmcConnT *g_conn_syncAsync = NULL;
const char *g_simpleLabel = "vl_simple";
const char *g_complexLabel = "vl_general_complex";
char *g_simpleSchema = NULL;
char *g_complexSchema = NULL;
char g_dbFilePath[1024] = {0};
char g_newDbFilePath[1024] = {0};
char *g_newDataDir = "/data/gmdb";

uint8_t *g_aptAddr = NULL;
uint32_t g_aptMemSize = 0;
pthread_t g_svThreadId = 0;
int g_shmid;
static char g_tableName[64] = "testdb";
static char g_tableName2[64] = "testdb1";
#define MAX_CMD_SIZE 1024
char *g_dir = getenv("TEST_HOME");

char g_ip[20][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111", "33333333",
    "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
    "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
    "123456789a9887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
    "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
char g_name[20][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc", "lucy",
    "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
char g_desText[20][64] = {"test data of the text type:0", "test data of the text type:1",
    "test data of the text type:2", "test data of the text type:3", "test data of the text type:4",
    "test data of the text type:5", "test data of the text type:6", "test data of the text type:7",
    "test data of the text type:8", "test data of the text type:9", "test data of the text type:0",
    "test data of the text type:1", "test data of the text type:2", "test data of the text type:3",
    "test data of the text type:4", "test data of the text type:5", "test data of the text type:6",
    "test data of the text type:7", "test data of the text type:8", "test data of the text type:9"};

#define COUNT 20
typedef struct TagC2Int8C4Str {
    int64_t *id;
    int64_t *time;
    char (*name)[64];
    char (*ip)[33];
    char *ns[COUNT];
} C2Int8C4StrT;

int C2Int8C4StrSet(GmcStmtT *stmt, void *t)
{
    int ret = 0;
    C2Int8C4StrT *objT = (C2Int8C4StrT *)t;
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, objT->id, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, objT->time, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_FIXED, objT->name, sizeof(objT->name[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_FIXED, objT->ip, sizeof(objT->ip[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_STRING, objT->ns, sizeof(objT->ns[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}
typedef int (*FuncWrite)(GmcStmtT *stmt, void *t);
template <typename StructObjT>
int writeRecordTs(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, StructObjT *obj, int objLen, FuncWrite func)
{
    int ret = 0;

    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &objLen, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = func(stmt, obj);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

template <typename StructObjT2>
int writeRecordTs2(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, StructObjT2 *obj, int objLen, FuncWrite func)
{
    int ret = 0;

    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SQL_INSERT);
    if (ret != 0) {
        return ret;
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &objLen, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = func(stmt, obj);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    return ret;
}

int RestartGmserver()
{
    int ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/stop.sh -ts");
    RETURN_IFERR(ret);
    ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh -ts");
    RETURN_IFERR(ret);
}

int InitAndConn()
{
    int ret = testEnvInit(-1, false);
    RETURN_IFERR(ret);
    ret = create_epoll_thread();
    RETURN_IFERR(ret);
    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    RETURN_IFERR(ret);
    return T_OK;
}

int RestartAndConn()
{
    // 重启
    int ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    RETURN_IFERR(ret);
    testEnvClean();
    system("ps -ef |grep gmserver_ts");
    system("sh $TEST_HOME/tools/stop.sh -ts");
    sleep(1);
    system("gmserver_ts -p /usr/local/file/gmserver_ts.ini -b");
    sleep(1);
    system("ps -ef |grep gmserver_ts");
    ret = testEnvInit();
    RETURN_IFERR(ret);
    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    RETURN_IFERR(ret);
    return T_OK;
}

int RestartAndConn2()
{
    // 重启
    int ret;
    testEnvClean();
    system("ps -ef |grep gmserver_ts");
    system("sh $TEST_HOME/tools/stop.sh -ts");
    sleep(1);
    system("gmserver_ts -p /usr/local/file/gmserver_ts.ini -b");
    sleep(1);
    system("ps -ef |grep gmserver_ts");
    ret = testEnvInit(-1, false);
    RETURN_IFERR(ret);
    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    RETURN_IFERR(ret);
    return T_OK;
}

int DisConnAndClean()
{
    int ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    RETURN_IFERR(ret);
    ret = close_epoll_thread();
    RETURN_IFERR(ret);
    testEnvClean();
    return T_OK;
}

void Test_dml_acl(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, uint32_t count, uint32_t data_num = 30)
{
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    char ip[count][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[count][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc", "lucy",
        "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};

    char *ns[count] = {0};
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1704067200 + i;
        ns[i] = name[i];
    }

    printf("data_num111=%d\n", data_num);
    // insert 数据
    // 读输入表的数据进行校验
    C2Int8C4StrT obj1 = {.id = id, .time = time, .name = name, .ip = ip};
    for (int i = 0; i < count; i++) {
        obj1.ns[i] = ns[i];
    }

    for (int i = 0; i < data_num; i++) {
        // 插入数据
        int ret = writeRecordTs(conn, stmt, "testdb0", &obj1, count, C2Int8C4StrSet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    printf("data_num222=%d\n", data_num);
    printf("count=%d\n", count);
    const char *queryCommand = "select * from testdb0;";
    int ret = GmcExecDirect(stmt, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    uint32_t i = 0;
    bool eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if ((eof) || (ret != 0)) {
            break;
        }
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)(data_num * count));
}

void Test_dml_acl2(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, uint32_t count, uint32_t data_num = 30)
{
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    char ip[count][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[count][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc", "lucy",
        "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};

    char *ns[count] = {0};
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1704067200 + i;
        ns[i] = name[i];
    }

    printf("data_num111=%d\n", data_num);
    // insert 数据
    // 读输入表的数据进行校验
    C2Int8C4StrT obj1 = {.id = id, .time = time, .name = name, .ip = ip};
    for (int i = 0; i < count; i++) {
        obj1.ns[i] = ns[i];
    }

    for (int i = 0; i < data_num; i++) {
        // 插入数据
        int ret = writeRecordTs2(conn, stmt, "testdb0", &obj1, count, C2Int8C4StrSet);
        if (ret != 0 && ret == GMERR_LOAD_THIRD_PARTY_LIBRARY_FAILED) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_LOAD_THIRD_PARTY_LIBRARY_FAILED, ret);
        } else if (ret != 0 && ret == GMERR_FILE_OPERATE_FAILED) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_FILE_OPERATE_FAILED, ret);
        } else if (ret != 0 && ret == GMERR_DATABASE_NOT_AVAILABLE) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
}


void Test_dml_acl3(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, uint32_t count, uint32_t data_num = 30)
{
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    char ip[count][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[count][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc", "lucy",
        "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};

    char *ns[count] = {0};
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1704067200 + i;
        ns[i] = name[i];
    }

    printf("data_num111=%d\n", data_num);
    // insert 数据
    // 读输入表的数据进行校验
    C2Int8C4StrT obj1 = {.id = id, .time = time, .name = name, .ip = ip};
    for (int i = 0; i < count; i++) {
        obj1.ns[i] = ns[i];
    }

    for (int i = 0; i < data_num; i++) {
        // 插入数据
        int ret = writeRecordTs2(conn, stmt, "testdb1", &obj1, count, C2Int8C4StrSet);
        if (ret != 0 && ret == GMERR_LOAD_THIRD_PARTY_LIBRARY_FAILED) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_LOAD_THIRD_PARTY_LIBRARY_FAILED, ret);
        } else if (ret != 0 && ret == GMERR_FILE_OPERATE_FAILED) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_FILE_OPERATE_FAILED, ret);
        } else if (ret != 0 && ret == GMERR_DATABASE_NOT_AVAILABLE) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
}

// 注入顺序数据
int insertOrderData(GmcStmtT *stmt, char *tableName, int64_t dataCount)
{
    int ret = 0;
    int64_t count = dataCount > 20000 ? 20000 : dataCount;
    int64_t cycleNum = dataCount > 20000 ? dataCount / 20000 : 1;
    int64_t *id;
    int64_t *id1;
    int64_t *time;
    int64_t *timet;
    // 申请内存
    char *nameList = (char *)malloc(count * 64);
    if (nameList == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    char *ipList = (char *)malloc(count * 33);
    if (ipList == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    char **textList = (char **)malloc(count * sizeof(char *));
    if (textList == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    for (int i = 0; i < count; i++) {
        textList[i] = (char *)malloc(64 * sizeof(char));
        (void)memset(textList[i], 0, 64);
    }
    id = (int64_t *)malloc(sizeof(int64_t) * count);
    if (id == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    id1 = (int64_t *)malloc(sizeof(int64_t) * count);
    if (id == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    time = (int64_t *)malloc(sizeof(int64_t) * count);
    if (time == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    timet = (int64_t *)malloc(sizeof(int64_t) * count);
    if (timet == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    uint8_t *blobList[count];
    // 构造数据
    for (int i = 0; i < count; i++) {
        id[i] = i;
        id1[i] = i;
        // 2010年11月2日 01：00：30 开始
        time[i] = 10000 + i;   // 2010年1月1日0点 1262275200
        timet[i] = 10000 + i;  // 2010年1月1日0点 1262275200
        int j = i % 20;
        memcpy((ipList + i * 33), (char *)g_ip[j], 33);
        memcpy((nameList + i * 64), (char *)g_name[j], 64);
        (void)sprintf(*(textList + i), "%s", (char *)g_desText[j], 64);
    }
    for (int k = 0; k < cycleNum; k++) {
        ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
        RETURN_IFERR(ret);
        ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int64_t));
        RETURN_IFERR(ret);
        ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
        RETURN_IFERR(ret);
        ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, time, sizeof(time[0]), NULL);
        RETURN_IFERR(ret);
        ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_FIXED, nameList, 64, NULL);
        RETURN_IFERR(ret);
        ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_FIXED, ipList, 33, NULL);
        RETURN_IFERR(ret);
        ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_STRING, textList, sizeof(textList[0]), NULL);
        RETURN_IFERR(ret);
        ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_INT64, id1, sizeof(id1[0]), 0);
        RETURN_IFERR(ret);
        ret = GmcBindCol(stmt, 6, (GmcDataTypeE)DB_DATATYPE_INT64, timet, sizeof(timet[0]), 0);
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
    }
    // 释放内存
    free(id);
    free(id1);
    free(time);
    free(timet);
    free(nameList);
    free(ipList);
    free(textList);
    return ret;
}

int DropCmTable(GmcStmtT *g_stmt_sync, uint32_t tableNum)
{
    int ret = 0;
    char sqlCmd[256] = {0};

    for (int i = 0; i < tableNum; i++) {
        (void)sprintf(g_tableName, "testdb%d", i);
        (void)sprintf(sqlCmd, "drop table %s;", g_tableName);
        uint32_t cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
        if (ret != 0 && ret != 1009010) {
            printf("drop i=%d,ret=%d\n", i, ret);
            break;
        }
    }
    return ret;
}

// 批量创建表
int BatchCreateTable(GmcStmtT *g_stmt_sync, uint32_t tableNum)
{
    int ret = 0;
    char gSqlCmd[MAX_CMD_SIZE] = {0};

    for (int i = 0; i < tableNum; i++) {
        (void)sprintf(g_tableName, "testdb%d", i);
        (void)snprintf(gSqlCmd, MAX_CMD_SIZE,
            "create table %s(id integer, time integer, name char(64), ip inet, ns text)"
            " with (time_col = 'time', interval = '1 hour', ttl = '1 hour', cache_size = '0', disk_limit = '100 "
            "MB',"
            " compression = 'fast(rapidlz)');",
            g_tableName);

        ret = GmcExecDirect(g_stmt_sync, gSqlCmd, strlen(gSqlCmd));
        if (ret != 0) {
            printf("create i=%d,ret=%d\n", i, ret);
            break;
        }
    }
    return ret;
}

void InitTsCiCfgModify()
{
// euler和仿真环境清共享内存，清理原有DB文件
#if defined RUN_INDEPENDENT
    system("sh $TEST_HOME/tools/stop.sh -f");
#endif
    system("sh $TEST_HOME/tools/stop.sh -ts");
    TsDefulatDbFileClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(2);
}

int GmcSwapDataDirSleep(GmcStmtT *stmt, char *ctrlFilePath, char *tempFilePath)
{
    // 延时2秒触发插入数据或者查询开始
    sleep(2);
    int ret = 0;
    ret = GmcSwapDataDir(stmt, ctrlFilePath, tempFilePath);
    sleep(2);
    return ret;
}

#endif /* INCRE_COMMON_H */
