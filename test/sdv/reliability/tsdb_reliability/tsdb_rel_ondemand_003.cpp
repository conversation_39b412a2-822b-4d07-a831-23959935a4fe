
/*****************************************************************************
 Description  : 句柄不足&文件/目录权限错误&文件系统只读加固
 History      :
 Author       : qwx 620469
 Modification :
 Date         : 2025-6-25
*****************************************************************************/

#include "tsdb_incre_common.h"

int ret = 0;

class tsdb_rel_ondemand_003 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}
};

void tsdb_rel_ondemand_003::SetUp()
{
    printf("[INFO] Persistence cfg test Start.\n");
    ret = ChangeTsGmserverCfg((char *)"recover", NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    (void)sprintf(g_dbFilePath, "%s/gmdb", pwdDir);
    (void)Rmdir(g_dbFilePath);
    ret = mkdir(g_dbFilePath, S_IRUSR | S_IWUSR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeTsGmserverCfg((char *)"dataFileDirPath", g_dbFilePath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
// euler和环境环境清共享内存
#if defined RUN_INDEPENDENT
    system("sh $TEST_HOME/tools/stop.sh -ts");
#endif
    AW_CHECK_LOG_BEGIN();

    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("rm ./gmdb/* -rf");
    system("rm /data/gmdb -rf");
    system("rm ./gmdb2/* -rf");
    system("ipcrm -a");
    ret = ChangeTsGmserverCfg((char *)"redoFileSize", (char *)"16");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/start.sh -ts");

    ret = InitAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void tsdb_rel_ondemand_003::TearDown()
{

    // 持久化目录只读
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "%s/gmdb", pwdDir);

    // 取消异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unwrite clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_deplenish clean");

    ret = DisConnAndClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeTsGmserverCfg((char *)"recover", NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_FILE_OPERATE_FAILED);
    AW_CHECK_LOG_END();
}

// 文件句柄不足，建逻辑表
TEST_F(tsdb_rel_ondemand_003, tsdb_rel_ondemand_003_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int tableNum = 1;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);

    // 注入异常
    int time_out = 5;
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_deplenish inject %d", time_out);

    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FILE_OPERATE_FAILED, ret);

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_deplenish clean");
    sleep(20);

    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 文件句柄不足，建内存表
TEST_F(tsdb_rel_ondemand_003, tsdb_rel_ondemand_003_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int tableNum = 1;
    char sqlCmd[MAX_CMD_SIZE] = {0};
    uint32_t cmdLen = strlen(sqlCmd);
    (void)sprintf(sqlCmd, "drop table %s;", g_tableName2);

    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);

    // 注入异常
    int time_out = 5;
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_deplenish inject %d", time_out);

    (void)snprintf(sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, ns text)"
        " with (engine = 'memory', max_size =500000,time_col = 'time', interval = '1 hour', ttl = '1 hour', "
        "disk_limit = '100 MB', sensitive_col = 'name');",
        g_tableName2);

    ret = GmcExecDirect(g_stmt_sync, sqlCmd, MAX_CMD_SIZE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_deplenish clean");
    sleep(20);

    (void)snprintf(sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, ns text)"
        " with (engine = 'memory', max_size =500000,time_col = 'time', interval = '1 hour', ttl = '1 hour', "
        "disk_limit = '100 MB', sensitive_col = 'name');",
        g_tableName2);

    ret = GmcExecDirect(g_stmt_sync, sqlCmd, MAX_CMD_SIZE);
    AW_MACRO_EXPECT_EQ_INT(1009013, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 文件句柄不足，删表
TEST_F(tsdb_rel_ondemand_003, tsdb_rel_ondemand_003_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int tableNum = 1;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 注入异常
    int time_out = 5;
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_deplenish inject %d", time_out);

    ret = DropCmTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_deplenish clean");
    sleep(20);

    ret = DropCmTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(1009010, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 文件句柄耗尽，向逻辑表和内存表中写数据
TEST_F(tsdb_rel_ondemand_003, tsdb_rel_ondemand_003_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int tableNum = 1;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char sqlCmd[MAX_CMD_SIZE] = {0};
    uint32_t cmdLen = strlen(sqlCmd);
    (void)sprintf(sqlCmd, "drop table %s;", g_tableName2);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    (void)snprintf(sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, ns text)"
        " with (engine = 'memory', max_size =500000,time_col = 'time', interval = '1 hour', ttl = '1 hour', "
        "disk_limit = '100 MB', sensitive_col = 'name');",
        g_tableName2);

    ret = GmcExecDirect(g_stmt_sync, sqlCmd, MAX_CMD_SIZE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 注入异常
    int time_out = 5;
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_deplenish inject %d", time_out);

    Test_dml_acl2(g_conn_sync, g_stmt_sync, "testdb0", 20);
    Test_dml_acl3(g_conn_sync, g_stmt_sync, "testdb1", 20);

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_deplenish clean");
    sleep(20);

    Test_dml_acl2(g_conn_sync, g_stmt_sync, "testdb0", 20);
    Test_dml_acl3(g_conn_sync, g_stmt_sync, "testdb1", 20);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 文件句柄耗尽，向逻辑表中insert into 数据
TEST_F(tsdb_rel_ondemand_003, tsdb_rel_ondemand_003_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int tableNum = 2;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 注入异常
    int time_out = 5;
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_deplenish inject %d", time_out);

    // insert_into
    char sqlCmd2[512] = {0};
    (void)sprintf(sqlCmd2, "insert into %s select * from %s;", g_tableName2, "testdb0");
    uint32_t cmdLen = 0;
    cmdLen = strlen(sqlCmd2);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd2, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_deplenish clean");
    sleep(20);

    // insert_into
    sqlCmd2[512] = {0};
    (void)sprintf(sqlCmd2, "insert into %s select * from %s;", g_tableName2, "testdb0");
    cmdLen = 0;
    cmdLen = strlen(sqlCmd2);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd2, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 文件句柄耗尽，Truncate数据
TEST_F(tsdb_rel_ondemand_003, tsdb_rel_ondemand_003_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int tableNum = 1;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 注入异常
    int time_out = 5;
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_deplenish inject %d", time_out);

    // 主动Truncate
    char sqlCmd[512] = "truncate TABLE testdb0;";
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_deplenish clean");
    sleep(20);

    // 主动Truncate
    char sqlCmd2[512] = "truncate TABLE testdb0;";
    ret = GmcExecDirect(g_stmt_sync, sqlCmd2, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 文件句柄不足，查询逻辑表
TEST_F(tsdb_rel_ondemand_003, tsdb_rel_ondemand_003_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int tableNum = 1;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    Test_dml_acl(g_conn_sync, g_stmt_sync, "testdb0", 20);

    // 注入异常
    int time_out = 5;
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_deplenish inject %d", time_out);

    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "select * from %s", "testdb0");
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FILE_OPERATE_FAILED, ret);

    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, 0);

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_deplenish clean");
    sleep(20);

    (void)sprintf(sqlCmd, "select * from %s", "testdb0");
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    dataCount = 0;
    ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, 600);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 文件句柄不足，查询内存表
TEST_F(tsdb_rel_ondemand_003, tsdb_rel_ondemand_003_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int tableNum = 1;
    char sqlCmd[MAX_CMD_SIZE] = {0};
    uint32_t cmdLen = strlen(sqlCmd);
    (void)sprintf(sqlCmd, "drop table %s;", g_tableName2);

    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);

    (void)snprintf(sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, ns text)"
        " with (engine = 'memory', max_size =500000,time_col = 'time', interval = '1 hour', ttl = '1 hour', "
        "disk_limit = '100 MB', sensitive_col = 'name');",
        g_tableName2);

    ret = GmcExecDirect(g_stmt_sync, sqlCmd, MAX_CMD_SIZE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    Test_dml_acl3(g_conn_sync, g_stmt_sync, "testdb1", 20);

    // 注入异常
    int time_out = 5;
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_deplenish inject %d", time_out);

    sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "select * from %s", g_tableName2);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, 600);

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_deplenish clean");
    sleep(20);

    (void)sprintf(sqlCmd, "select * from %s", g_tableName2);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    dataCount = 0;
    ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, 600);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 文件句柄不足，进行copy to操作
TEST_F(tsdb_rel_ondemand_003, tsdb_rel_ondemand_003_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int tableNum = 1;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    Test_dml_acl(g_conn_sync, g_stmt_sync, "testdb0", 20);

    // 注入异常
    int time_out = 5;
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_deplenish inject %d", time_out);

    char sqlCmd[256] = {0};
    sprintf(sqlCmd, "COPY (SELECT * FROM testdb0) TO"
                    "'${TEST_HOME}/reliability/tsdb_reliability/data.csv';");
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, 256);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DIRECTORY_OPERATE_FAILED, ret);

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_deplenish clean");
    sleep(20);
    sqlCmd[256] = {0};
    sprintf(sqlCmd, "COPY (SELECT * FROM testdb0) TO"
                    "'${TEST_HOME}/reliability/tsdb_reliability/data.csv';");
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, 256);
    AW_MACRO_ASSERT_EQ_INT((GMERR_OK, ret), ret);
}

// 文件句柄不足，触发disk_limit、ttl
TEST_F(tsdb_rel_ondemand_003, tsdb_rel_ondemand_003_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("rm ./gmdb/* -rf");
    system("rm /data/gmdb -rf");

    ret = ChangeTsGmserverCfg((char *)"redoFileSize", (char *)"16");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"tsLcmCheckPeriod=3\" ");

    system("sh $TEST_HOME/tools/start.sh -ts");

    ret = InitAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int tableNum = 1;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    Test_dml_acl(g_conn_sync, g_stmt_sync, "testdb0", 20);

    // 注入异常
    int time_out = 5;
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_deplenish inject %d", time_out);

    sleep(6);

    // 在线修改disk_limit
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "alter table %s set (disk_limit = '1 MB')", "testdb0");
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_deplenish clean");
    sleep(20);

    // 实际上后台任务失败，磁盘文件存在，只不过是前台查不到数据
    //  故障取消后视图查询后台任务生效 DTS2025032526804
    char g_command[1024];
    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "ROW_CNT: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 文件句柄不足，构造服务端重启
TEST_F(tsdb_rel_ondemand_003, tsdb_rel_ondemand_003_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int tableNum = 1;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    Test_dml_acl(g_conn_sync, g_stmt_sync, "testdb0", 20);

    // 注入异常
    int time_out = 5;
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_deplenish inject %d", time_out);

    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_deplenish clean");
    sleep(20);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    ret = InitAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 权限错误，建逻辑表
TEST_F(tsdb_rel_ondemand_003, tsdb_rel_ondemand_003_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int tableNum = 1;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);

    // 持久化目录只读
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "%s/gmdb", pwdDir);

    // 注入异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw inject %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");

    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw query");
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");
    sleep(3);

    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FILE_OPERATE_FAILED, ret);

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");

    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 权限错误，建内存表
TEST_F(tsdb_rel_ondemand_003, tsdb_rel_ondemand_003_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char sqlCmd[MAX_CMD_SIZE] = {0};
    uint32_t cmdLen = strlen(sqlCmd);
    (void)sprintf(sqlCmd, "drop table %s;", g_tableName2);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);

    // 持久化目录只读
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "%s/gmdb", pwdDir);

    // 注入异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw inject %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");

    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw query");
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");
    sleep(3);

    (void)snprintf(sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, ns text)"
        " with (engine = 'memory', max_size =500000,time_col = 'time', interval = '1 hour', ttl = '1 hour', "
        "disk_limit = '100 MB', sensitive_col = 'name');",
        g_tableName2);

    ret = GmcExecDirect(g_stmt_sync, sqlCmd, MAX_CMD_SIZE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FILE_OPERATE_FAILED, ret);

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");

    (void)snprintf(sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, ns text)"
        " with (engine = 'memory', max_size =500000,time_col = 'time', interval = '1 hour', ttl = '1 hour', "
        "disk_limit = '100 MB', sensitive_col = 'name');",
        g_tableName2);

    ret = GmcExecDirect(g_stmt_sync, sqlCmd, MAX_CMD_SIZE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 权限错误，工具建表
TEST_F(tsdb_rel_ondemand_003, tsdb_rel_ondemand_003_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd, "drop table %s;", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);

    char filePath[512] = {0};
    char fileName[512] = "testdb";

    // 持久化目录只读
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "%s/gmdb", pwdDir);

    // 注入异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw inject %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");

    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw query");
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");
    sleep(3);

    char g_command[1024];
    (void)sprintf(filePath, "./%s.gmsql", fileName);
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c sql -f %s -s %s", filePath, g_connServerTsdb);
    ret = executeCommand(g_command, "gmimport create sql total success num: 0.");
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");

    (void)sprintf(filePath, "./%s.gmsql", fileName);
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c sql -f %s -s %s", filePath, g_connServerTsdb);
    ret = executeCommand(g_command, "gmimport create sql total success num: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 权限错误，删表
TEST_F(tsdb_rel_ondemand_003, tsdb_rel_ondemand_003_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int tableNum = 1;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 持久化目录只读
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "%s/gmdb", pwdDir);

    // 注入异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw inject %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");

    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw query");
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");
    sleep(3);

    char sqlCmd[256] = {0};

    (void)sprintf(sqlCmd, "drop table %s;", "testdb0");
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(1013000, ret);

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");

    (void)sprintf(sqlCmd, "drop table %s;", "testdb0");
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 默认目录不可读写，向逻辑表和内存表中写数据
TEST_F(tsdb_rel_ondemand_003, tsdb_rel_ondemand_003_016)
{

    AW_FUN_Log(LOG_STEP, "test start.");

    int tableNum = 1;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char sqlCmd[MAX_CMD_SIZE] = {0};
    uint32_t cmdLen = strlen(sqlCmd);
    (void)sprintf(sqlCmd, "drop table %s;", g_tableName2);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    (void)snprintf(sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, ns text)"
        " with (engine = 'memory', max_size =500000,time_col = 'time', interval = '1 hour', ttl = '1 hour', "
        "disk_limit = '100 MB', sensitive_col = 'name');",
        g_tableName2);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, MAX_CMD_SIZE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 持久化目录只读
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "%s/gmdb", pwdDir);

    // 注入异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw inject %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");

    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw query");
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");
    sleep(3);

    Test_dml_acl2(g_conn_sync, g_stmt_sync, "testdb0", 20);
    Test_dml_acl3(g_conn_sync, g_stmt_sync, "testdb1", 20);

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");

    Test_dml_acl2(g_conn_sync, g_stmt_sync, "testdb0", 20);
    Test_dml_acl3(g_conn_sync, g_stmt_sync, "testdb1", 20);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 默认目录不可读写，向逻辑表中insert into 数据
TEST_F(tsdb_rel_ondemand_003, tsdb_rel_ondemand_003_017)
{

    AW_FUN_Log(LOG_STEP, "test start.");

    int tableNum = 2;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 持久化目录只读
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "%s/gmdb", pwdDir);

    // 注入异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw inject %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");

    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw query");
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");
    sleep(3);

    // insert_into
    char sqlCmd2[512] = {0};
    (void)sprintf(sqlCmd2, "insert into %s select * from %s;", g_tableName2, "testdb0");
    uint32_t cmdLen = 0;
    cmdLen = strlen(sqlCmd2);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd2, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");

    // insert_into
    sqlCmd2[512] = {0};
    (void)sprintf(sqlCmd2, "insert into %s select * from %s;", g_tableName2, "testdb0");
    cmdLen = 0;
    cmdLen = strlen(sqlCmd2);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd2, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// table_path目录不可读写，向逻辑表中insert into 数据
TEST_F(tsdb_rel_ondemand_003, tsdb_rel_ondemand_003_018)
{

    AW_FUN_Log(LOG_STEP, "test start.");

    // 持久化目录只读
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "%s/gmdb/", pwdDir);

    int tableNum = 2;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    char sqlCmd[MAX_CMD_SIZE] = {0};

    for (int i = 0; i < tableNum; i++) {
        (void)sprintf(g_tableName, "testdb%d", i);
        (void)snprintf(sqlCmd, MAX_CMD_SIZE,
            "create table %s(id integer, time integer, name char(64), ip inet, ns text)"
            " with (time_col = 'time', interval = '1 hour', ttl = '1 hour', cache_size = '0',"
            " compression = 'fast(rapidlz)' ,  table_path = '%s');",
            g_tableName, g_command1);

        ret = GmcExecDirect(g_stmt_sync, sqlCmd, strlen(sqlCmd));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 注入异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw inject %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");

    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw query");
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");
    sleep(3);

    // insert_into
    char sqlCmd2[512] = {0};
    (void)sprintf(sqlCmd2, "insert into %s select * from %s;", g_tableName2, "testdb0");
    uint32_t cmdLen = 0;
    cmdLen = strlen(sqlCmd2);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd2, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");

    // insert_into
    sqlCmd2[512] = {0};
    (void)sprintf(sqlCmd2, "insert into %s select * from %s;", g_tableName2, "testdb0");
    cmdLen = 0;
    cmdLen = strlen(sqlCmd2);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd2, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 默认目录不可读写，Truncate数据
TEST_F(tsdb_rel_ondemand_003, tsdb_rel_ondemand_003_019)
{

    AW_FUN_Log(LOG_STEP, "test start.");

    int tableNum = 1;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    Test_dml_acl(g_conn_sync, g_stmt_sync, "testdb0", 20);

    // 持久化目录只读
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "%s/gmdb", pwdDir);

    // 注入异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw inject %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");

    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw query");
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");
    sleep(3);

    // 主动Truncate
    char sqlCmd[512] = "truncate TABLE testdb0;";
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FILE_OPERATE_FAILED, ret);

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");

    // 主动Truncate
    char sqlCmd2[512] = "truncate TABLE testdb0;";
    ret = GmcExecDirect(g_stmt_sync, sqlCmd2, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 默认路径没有读写权限，查询逻辑表、内存表
TEST_F(tsdb_rel_ondemand_003, tsdb_rel_ondemand_003_020)
{

    AW_FUN_Log(LOG_STEP, "test start.");

    int tableNum = 1;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char sqlCmd[MAX_CMD_SIZE] = {0};
    uint32_t cmdLen = strlen(sqlCmd);
    (void)sprintf(sqlCmd, "drop table %s;", g_tableName2);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    (void)snprintf(sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, ns text)"
        " with (engine = 'memory', max_size =500000,time_col = 'time', interval = '1 hour', ttl = '1 hour', "
        "disk_limit = '100 MB', sensitive_col = 'name');",
        g_tableName2);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, MAX_CMD_SIZE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    Test_dml_acl(g_conn_sync, g_stmt_sync, "testdb0", 20);
    Test_dml_acl3(g_conn_sync, g_stmt_sync, "testdb1", 20);

    // 持久化目录只读
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "%s/gmdb", pwdDir);

    // 注入异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw inject %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");

    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw query");
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");
    sleep(3);

    sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "select * from %s", "testdb0");
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FILE_OPERATE_FAILED, ret);

    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, 0);

    (void)sprintf(sqlCmd, "select * from %s", "testdb1");
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    dataCount = 0;
    ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, 600);

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");

    sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "select * from %s", "testdb0");
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    dataCount = 0;
    ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, 600);

    (void)sprintf(sqlCmd, "select * from %s", "testdb1");
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    dataCount = 0;
    ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, 600);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 易失性路径没有读写权限，查询逻辑表
TEST_F(tsdb_rel_ondemand_003, tsdb_rel_ondemand_003_021)
{

    AW_FUN_Log(LOG_STEP, "test start.");

    // 持久化目录只读
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "%s/gmdb2/", pwdDir);

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");

    int tableNum = 1;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    char sqlCmd[MAX_CMD_SIZE] = {0};

    for (int i = 0; i < tableNum; i++) {
        (void)sprintf(g_tableName, "testdb%d", i);
        (void)snprintf(sqlCmd, MAX_CMD_SIZE,
            "create table %s(id integer, time integer, name char(64), ip inet, ns text)"
            " with (is_volatile_label = 'true',time_col = 'time', interval = '1 hour', ttl = '1 hour', cache_size = "
            "'0',"
            " compression = 'fast(rapidlz)' ,  table_path = '%s');",
            g_tableName, g_command1);

        ret = GmcExecDirect(g_stmt_sync, sqlCmd, strlen(sqlCmd));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    Test_dml_acl(g_conn_sync, g_stmt_sync, "testdb0", 20);

    // 注入异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw inject %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");

    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw query");
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");
    sleep(3);

    sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "select * from %s", "testdb0");
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FILE_OPERATE_FAILED, ret);

    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, 0);

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");

    sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "select * from %s", "testdb0");
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    dataCount = 0;
    ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, 600);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// copy to 指定的目录没有读写权限
TEST_F(tsdb_rel_ondemand_003, tsdb_rel_ondemand_003_022)
{

    AW_FUN_Log(LOG_STEP, "test start.");

    int tableNum = 1;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    Test_dml_acl(g_conn_sync, g_stmt_sync, "testdb2", 20);

    // 持久化目录只读
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "%s/gmdb/", pwdDir);

    // 注入异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw inject %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");

    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw query");
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");
    sleep(3);

    char sqlCmd[256] = {0};
    sprintf(sqlCmd, "COPY (SELECT * FROM testdb0) TO"
                    "'${TEST_HOME}/reliability/tsdb_reliability/gmdb/data.csv';");
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, 256);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DIRECTORY_OPERATE_FAILED, ret);

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");

    sqlCmd[256] = {0};
    sprintf(sqlCmd, "COPY (SELECT * FROM testdb0) TO"
                    "'${TEST_HOME}/reliability/tsdb_reliability/gmdb/data.csv';");
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, 256);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DIRECTORY_OPERATE_FAILED, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 默认路径没有读写权限，触发disk_limit、ttl
TEST_F(tsdb_rel_ondemand_003, tsdb_rel_ondemand_003_023)
{

    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("rm ./gmdb/* -rf");
    system("rm /data/gmdb -rf");
    ret = ChangeTsGmserverCfg((char *)"redoFileSize", (char *)"16");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"tsLcmCheckPeriod=3\" ");
    system("sh $TEST_HOME/tools/start.sh -ts");

    ret = InitAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int tableNum = 1;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    Test_dml_acl(g_conn_sync, g_stmt_sync, "testdb0", 20);

    // 持久化目录只读
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "%s/gmdb", pwdDir);

    // 注入异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw inject %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");

    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw query");
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");
    sleep(3);

    sleep(6);

    // 在线修改disk_limit
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "alter table %s set (disk_limit = '1 MB')", "testdb0");
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(1019004, ret);

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");

    sleep(6);

    // 实际上后台任务失败，磁盘文件存在，只不过是前台查不到数据
    //  故障取消后视图查询后台任务生效 DTS2025032526804
    char g_command[1024];
    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(g_command);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "ret = 1019004");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 易失性路径没有读写权限，触发disk_limit、ttl
TEST_F(tsdb_rel_ondemand_003, tsdb_rel_ondemand_003_024)
{

    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("rm ./gmdb/* -rf");
    system("rm /data/gmdb -rf");
    ret = ChangeTsGmserverCfg((char *)"redoFileSize", (char *)"16");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"tsLcmCheckPeriod=3\" ");
    system("sh $TEST_HOME/tools/start.sh -ts");

    ret = InitAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 持久化目录只读
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "%s/gmdb2/", pwdDir);

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");

    int tableNum = 1;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    char sqlCmd[MAX_CMD_SIZE] = {0};

    for (int i = 0; i < tableNum; i++) {
        (void)sprintf(g_tableName, "testdb%d", i);
        (void)snprintf(sqlCmd, MAX_CMD_SIZE,
            "create table %s(id integer, time integer, name char(64), ip inet, ns text)"
            " with (is_volatile_label = 'true',time_col = 'time', interval = '1 hour', ttl = '1 hour', cache_size = "
            "'0',"
            " compression = 'fast(rapidlz)' ,  table_path = '%s');",
            g_tableName, g_command1);

        ret = GmcExecDirect(g_stmt_sync, sqlCmd, strlen(sqlCmd));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    Test_dml_acl(g_conn_sync, g_stmt_sync, "testdb0", 20);

    // 注入异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw inject %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");

    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw query");
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");
    sleep(3);

    sleep(6);

    // 在线修改disk_limit
    sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "alter table %s set (disk_limit = '1 MB')", "testdb0");
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");

    // 实际上后台任务失败，磁盘文件存在，只不过是前台查不到数据
    //  故障取消后视图查询后台任务生效 DTS2025032526804
    char g_command[1024];
    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "ROW_CNT: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 默认路径没有读写权限，构造服务端重启
TEST_F(tsdb_rel_ondemand_003, tsdb_rel_ondemand_003_025)
{

    AW_FUN_Log(LOG_STEP, "test start.");

    int tableNum = 1;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    Test_dml_acl(g_conn_sync, g_stmt_sync, "testdb0", 20);

    // 持久化目录只读
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "%s/gmdb", pwdDir);

    // 注入异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw inject %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");

    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw query");
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");
    sleep(3);

    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");

    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");

    ret = InitAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 易失性路径没有读写权限，构造服务端重启
TEST_F(tsdb_rel_ondemand_003, tsdb_rel_ondemand_003_026)
{

    AW_FUN_Log(LOG_STEP, "test start.");

    // 持久化目录只读
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "%s/gmdb2/", pwdDir);

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");

    int tableNum = 1;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    char sqlCmd[MAX_CMD_SIZE] = {0};

    for (int i = 0; i < tableNum; i++) {
        (void)sprintf(g_tableName, "testdb%d", i);
        (void)snprintf(sqlCmd, MAX_CMD_SIZE,
            "create table %s(id integer, time integer, name char(64), ip inet, ns text)"
            " with (is_volatile_label = 'true',time_col = 'time', interval = '1 hour', ttl = '1 hour', cache_size = "
            "'0',"
            " compression = 'fast(rapidlz)' ,  table_path = '%s');",
            g_tableName, g_command1);

        ret = GmcExecDirect(g_stmt_sync, sqlCmd, strlen(sqlCmd));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    Test_dml_acl(g_conn_sync, g_stmt_sync, "testdb0", 20);

    // 注入异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw inject %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");

    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw query");
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");
    sleep(3);

    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");

    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");

    ret = InitAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 文件系统只读，建表
TEST_F(tsdb_rel_ondemand_003, tsdb_rel_ondemand_003_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int tableNum = 1;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);

    // 持久化目录只读
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "%s/gmdb", pwdDir);

    // 注入异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unwrite inject %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");

    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unwrite query");
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");
    sleep(3);

    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FILE_OPERATE_FAILED, ret);

    // 取消异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unwrite clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");

    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 文件系统只读，删表
TEST_F(tsdb_rel_ondemand_003, tsdb_rel_ondemand_003_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int tableNum = 1;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 持久化目录只读
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "%s/gmdb", pwdDir);

    // 注入异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unwrite inject %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");

    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unwrite query");
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");
    sleep(3);

    char sqlCmd[256] = {0};

    (void)sprintf(sqlCmd, "drop table %s;", "testdb0");
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(1013000, ret);

    // 取消异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unwrite clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");

    (void)sprintf(sqlCmd, "drop table %s;", "testdb0");
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 默认目录只读，向逻辑表和内存表中写数据
TEST_F(tsdb_rel_ondemand_003, tsdb_rel_ondemand_003_029)
{

    AW_FUN_Log(LOG_STEP, "test start.");

    int tableNum = 1;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char sqlCmd[MAX_CMD_SIZE] = {0};
    uint32_t cmdLen = strlen(sqlCmd);
    (void)sprintf(sqlCmd, "drop table %s;", g_tableName2);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    (void)snprintf(sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, ns text)"
        " with (engine = 'memory', max_size =500000,time_col = 'time', interval = '1 hour', ttl = '1 hour', "
        "disk_limit = '100 MB', sensitive_col = 'name');",
        g_tableName2);

    ret = GmcExecDirect(g_stmt_sync, sqlCmd, MAX_CMD_SIZE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 持久化目录只读
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "%s/gmdb", pwdDir);

    // 注入异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unwrite inject %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");

    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unwrite query");
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");
    sleep(3);

    Test_dml_acl2(g_conn_sync, g_stmt_sync, "testdb0", 20);
    Test_dml_acl3(g_conn_sync, g_stmt_sync, "testdb1", 20);

    // 取消异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unwrite clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");

    Test_dml_acl2(g_conn_sync, g_stmt_sync, "testdb0", 20);
    Test_dml_acl3(g_conn_sync, g_stmt_sync, "testdb1", 20);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 默认目录只读，向逻辑表中insert into 数据
TEST_F(tsdb_rel_ondemand_003, tsdb_rel_ondemand_003_030)
{

    AW_FUN_Log(LOG_STEP, "test start.");

    int tableNum = 2;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 持久化目录只读
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "%s/gmdb", pwdDir);

    // 注入异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unwrite inject %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");

    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unwrite query");
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");
    sleep(3);

    // insert_into
    char sqlCmd2[512] = {0};
    (void)sprintf(sqlCmd2, "insert into %s select * from %s;", g_tableName2, "testdb0");
    uint32_t cmdLen = 0;
    cmdLen = strlen(sqlCmd2);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd2, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    // 取消异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unwrite clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");

    // insert_into
    sqlCmd2[512] = {0};
    (void)sprintf(sqlCmd2, "insert into %s select * from %s;", g_tableName2, "testdb0");
    cmdLen = 0;
    cmdLen = strlen(sqlCmd2);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd2, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// table_path目录只读，向逻辑表中insert into 数据
TEST_F(tsdb_rel_ondemand_003, tsdb_rel_ondemand_003_031)
{

    AW_FUN_Log(LOG_STEP, "test start.");

    // 持久化目录只读
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "%s/gmdb/", pwdDir);

    int tableNum = 2;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    char sqlCmd[MAX_CMD_SIZE] = {0};

    for (int i = 0; i < tableNum; i++) {
        (void)sprintf(g_tableName, "testdb%d", i);
        (void)snprintf(sqlCmd, MAX_CMD_SIZE,
            "create table %s(id integer, time integer, name char(64), ip inet, ns text)"
            " with (time_col = 'time', interval = '1 hour', ttl = '1 hour', cache_size = '0',"
            " compression = 'fast(rapidlz)' ,  table_path = '%s');",
            g_tableName, g_command1);

        ret = GmcExecDirect(g_stmt_sync, sqlCmd, strlen(sqlCmd));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 注入异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unwrite inject %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");

    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unwrite query");
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");
    sleep(3);

    // insert_into
    char sqlCmd2[512] = {0};
    (void)sprintf(sqlCmd2, "insert into %s select * from %s;", g_tableName2, "testdb0");
    uint32_t cmdLen = 0;
    cmdLen = strlen(sqlCmd2);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd2, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unwrite clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");

    // insert_into
    sqlCmd2[512] = {0};
    (void)sprintf(sqlCmd2, "insert into %s select * from %s;", g_tableName2, "testdb0");
    cmdLen = 0;
    cmdLen = strlen(sqlCmd2);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd2, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 默认目录只读，Truncate数据
TEST_F(tsdb_rel_ondemand_003, tsdb_rel_ondemand_003_032)
{

    AW_FUN_Log(LOG_STEP, "test start.");

    int tableNum = 1;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    Test_dml_acl(g_conn_sync, g_stmt_sync, "testdb0", 20);

    // 持久化目录只读
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "%s/gmdb", pwdDir);

    // 注入异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw inject %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");

    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw query");
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");
    sleep(3);

    // 主动Truncate
    char sqlCmd[512] = "truncate TABLE testdb0;";
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FILE_OPERATE_FAILED, ret);

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");

    // 主动Truncate
    char sqlCmd2[512] = "truncate TABLE testdb0;";
    ret = GmcExecDirect(g_stmt_sync, sqlCmd2, 512);
    AW_MACRO_EXPECT_EQ_INT(1019004, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 默认路径只读，查询逻辑表、内存表
TEST_F(tsdb_rel_ondemand_003, tsdb_rel_ondemand_003_033)
{

    AW_FUN_Log(LOG_STEP, "test start.");

    int tableNum = 1;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char sqlCmd[MAX_CMD_SIZE] = {0};
    uint32_t cmdLen = strlen(sqlCmd);
    (void)sprintf(sqlCmd, "drop table %s;", g_tableName2);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    (void)snprintf(sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, ns text)"
        " with (engine = 'memory', max_size =500000,time_col = 'time', interval = '1 hour', ttl = '1 hour', "
        "disk_limit = '100 MB', sensitive_col = 'name');",
        g_tableName2);

    ret = GmcExecDirect(g_stmt_sync, sqlCmd, strlen(sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    Test_dml_acl(g_conn_sync, g_stmt_sync, "testdb0", 20);
    Test_dml_acl3(g_conn_sync, g_stmt_sync, "testdb1", 20);

    // 持久化目录只读
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "%s/gmdb", pwdDir);

    // 注入异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unwrite inject %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");

    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unwrite query");
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");
    sleep(3);

    sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "select * from %s", "testdb0");
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FILE_OPERATE_FAILED, ret);

    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, 0);

    (void)sprintf(sqlCmd, "select * from %s", "testdb1");
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    dataCount = 0;
    ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, 600);

    // 取消异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unwrite clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");

    sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "select * from %s", "testdb0");
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    dataCount = 0;
    ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, 600);

    (void)sprintf(sqlCmd, "select * from %s", "testdb1");
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    dataCount = 0;
    ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, 600);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 易失性路径只读，查询逻辑表
TEST_F(tsdb_rel_ondemand_003, tsdb_rel_ondemand_003_034)
{

    AW_FUN_Log(LOG_STEP, "test start.");

    // 持久化目录只读
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "%s/gmdb2/", pwdDir);

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");

    int tableNum = 1;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    char sqlCmd[MAX_CMD_SIZE] = {0};

    for (int i = 0; i < tableNum; i++) {
        (void)sprintf(g_tableName, "testdb%d", i);
        (void)snprintf(sqlCmd, MAX_CMD_SIZE,
            "create table %s(id integer, time integer, name char(64), ip inet, ns text)"
            " with (is_volatile_label = 'true',time_col = 'time', interval = '1 hour', ttl = '1 hour', cache_size = "
            "'0',"
            " compression = 'fast(rapidlz)' ,  table_path = '%s');",
            g_tableName, g_command1);

        ret = GmcExecDirect(g_stmt_sync, sqlCmd, strlen(sqlCmd));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    Test_dml_acl(g_conn_sync, g_stmt_sync, "testdb0", 20);

    // 注入异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unwrite inject %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");

    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unwrite query");
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");
    sleep(3);

    sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "select * from %s", "testdb0");
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FILE_OPERATE_FAILED, ret);

    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, 0);

    // 取消异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unwrite clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");

    sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "select * from %s", "testdb0");
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    dataCount = 0;
    ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, 600);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// copy to 指定的目录只读，查询逻辑表、内存表
TEST_F(tsdb_rel_ondemand_003, tsdb_rel_ondemand_003_035)
{

    AW_FUN_Log(LOG_STEP, "test start.");

    int tableNum = 1;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    Test_dml_acl(g_conn_sync, g_stmt_sync, "testdb2", 20);

    // 持久化目录只读
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "%s/gmdb/", pwdDir);

    // 注入异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unwrite inject %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");

    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unwrite query");
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");
    sleep(3);

    char sqlCmd[256] = {0};
    sprintf(sqlCmd, "COPY (SELECT * FROM testdb0) TO"
                    "'${TEST_HOME}/reliability/tsdb_reliability/gmdb/data.csv';");
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, 256);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DIRECTORY_OPERATE_FAILED, ret);

    // 取消异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unwrite clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");

    sqlCmd[256] = {0};
    sprintf(sqlCmd, "COPY (SELECT * FROM testdb0) TO"
                    "'${TEST_HOME}/reliability/tsdb_reliability/gmdb/data.csv';");
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, 256);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DIRECTORY_OPERATE_FAILED, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 默认路径只读，触发disk_limit、ttl
TEST_F(tsdb_rel_ondemand_003, tsdb_rel_ondemand_003_036)
{

    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("rm ./gmdb/* -rf");
    system("rm /data/gmdb -rf");
    ret = ChangeTsGmserverCfg((char *)"redoFileSize", (char *)"16");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"tsLcmCheckPeriod=3\" ");
    system("sh $TEST_HOME/tools/start.sh -ts");

    ret = InitAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int tableNum = 1;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    Test_dml_acl(g_conn_sync, g_stmt_sync, "testdb0", 20);

    // 持久化目录只读
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "%s/gmdb", pwdDir);

    // 注入异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unwrite inject %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");

    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unwrite query");
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");
    sleep(3);

    sleep(6);

    // 在线修改disk_limit
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "alter table %s set (disk_limit = '1 MB')", "testdb0");
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(1019004, ret);

    // 取消异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unwrite clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");

    sleep(6);

    // 实际上后台任务失败，磁盘文件存在，只不过是前台查不到数据
    //  故障取消后视图查询后台任务生效 DTS2025032526804
    char g_command[1024];
    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(g_command);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "ret = 1019004");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 易失性路径只读，触发disk_limit、ttl
TEST_F(tsdb_rel_ondemand_003, tsdb_rel_ondemand_003_037)
{

    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("rm ./gmdb/* -rf");
    system("rm /data/gmdb -rf");
    ret = ChangeTsGmserverCfg((char *)"redoFileSize", (char *)"16");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"tsLcmCheckPeriod=3\" ");
    system("sh $TEST_HOME/tools/start.sh -ts");

    ret = InitAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 持久化目录只读
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "%s/gmdb2/", pwdDir);

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");

    int tableNum = 1;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    char sqlCmd[MAX_CMD_SIZE] = {0};

    for (int i = 0; i < tableNum; i++) {
        (void)sprintf(g_tableName, "testdb%d", i);
        (void)snprintf(sqlCmd, MAX_CMD_SIZE,
            "create table %s(id integer, time integer, name char(64), ip inet, ns text)"
            " with (is_volatile_label = 'true',time_col = 'time', interval = '1 hour', ttl = '1 hour', cache_size = "
            "'0',"
            " compression = 'fast(rapidlz)' ,  table_path = '%s');",
            g_tableName, g_command1);

        ret = GmcExecDirect(g_stmt_sync, sqlCmd, strlen(sqlCmd));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    Test_dml_acl(g_conn_sync, g_stmt_sync, "testdb0", 20);

    // 注入异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unwrite inject %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");

    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unwrite query");
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");
    sleep(3);

    sleep(6);

    // 在线修改disk_limit
    sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "alter table %s set (disk_limit = '1 MB')", "testdb0");
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unwrite clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");

    // 实际上后台任务失败，磁盘文件存在，只不过是前台查不到数据
    //  故障取消后视图查询后台任务生效 DTS2025032526804
    char g_command[1024];
    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "ROW_CNT: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 默认路径只读，构造服务端重启
TEST_F(tsdb_rel_ondemand_003, tsdb_rel_ondemand_003_038)
{

    AW_FUN_Log(LOG_STEP, "test start.");

    int tableNum = 1;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    Test_dml_acl(g_conn_sync, g_stmt_sync, "testdb0", 20);

    // 持久化目录只读
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "%s/gmdb", pwdDir);

    // 注入异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unwrite inject %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");

    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unwrite query");
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");
    sleep(3);

    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");

    // 取消异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unwrite clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");

    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");

    ret = InitAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 易失性路径只读，构造服务端重启
TEST_F(tsdb_rel_ondemand_003, tsdb_rel_ondemand_003_039)
{

    AW_FUN_Log(LOG_STEP, "test start.");

    // 持久化目录只读
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "%s/gmdb2/", pwdDir);

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");

    int tableNum = 1;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    char sqlCmd[MAX_CMD_SIZE] = {0};

    for (int i = 0; i < tableNum; i++) {
        (void)sprintf(g_tableName, "testdb%d", i);
        (void)snprintf(sqlCmd, MAX_CMD_SIZE,
            "create table %s(id integer, time integer, name char(64), ip inet, ns text)"
            " with (is_volatile_label = 'true',time_col = 'time', interval = '1 hour', ttl = '1 hour', cache_size = "
            "'0',"
            " compression = 'fast(rapidlz)' ,  table_path = '%s');",
            g_tableName, g_command1);

        ret = GmcExecDirect(g_stmt_sync, sqlCmd, strlen(sqlCmd));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    Test_dml_acl(g_conn_sync, g_stmt_sync, "testdb0", 20);

    // 注入异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unwrite inject %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");

    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unwrite query");
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");
    sleep(3);

    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");

    // 取消异常
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unwrite clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");

    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");

    ret = InitAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
