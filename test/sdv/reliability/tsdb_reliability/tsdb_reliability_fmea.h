/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: 【时序交付】TSDB支持在线存储实例切换
 */
#ifndef TSDB_RELIABILITY_FMEA_H
#define TSDB_RELIABILITY_FMEA_H

#include "gtest/gtest.h"
#include "t_rd_ts.h"
#include "gmc_sysview.h"
#include "gmc_persist.h"
#include "gmc_ts_persist.h"
#include "adpt_sleep.h"
#include "t_rd_inject.h"

char g_tableName[] = "testdb0";
char g_tableName2[] = "testdb1";
char g_tableName3[] = "testdb_TEST1";
bool g_isTrue = false;
bool g_isInsertDataSuccessed = true;
int64_t g_insertTimes = 0;
char g_intervalHour[] = "1 hour";
char g_intervalDay[] = "1 Day";
char g_intervalMonth[] = "1 Month";
char g_intervalYear[] = "1 year";
static char g_dataFilePath[150] = {0};
static char g_dataFilePath1[150] = {0};
static char g_dataFilePath2[150] = {0};
static char g_dataFilePath3[150] = {0};
static char g_tableFilePath[150] = {0};
int64_t g_dataSize1Before = 0;
int64_t g_dataSize1After = 0;
int64_t g_dataSize2Before = 0;
int64_t g_dataSize2After = 0;
int64_t g_concurrentrStatus = 0;
char *g_filePath = getenv("PWD");

#ifdef RUN_INDEPENDENT
#define TEMPFILE_PATH "/data/gmdb/temp/large_result"
#define TABLE_PATH "/data/gmdb/"
#else
#define TEMPFILE_PATH "/mnt/hdd/data/gmdb/temp/large_result"
#define TABLE_PATH "/mnt/hdd/data/gmdb/"
#endif

char g_ip[20][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111", "33333333",
    "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
    "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
    "123456789a9887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
    "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
char g_name[20][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc", "lucy",
    "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
char g_message[20][160] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537", "0071 3521",
    "0010 0001", "", "3102 0022", "0000 0001", "9021 6538", "0071 3522", "0010 0002", "0010 0000", "3102 0023",
    "0000 0002", "9021 6539", "0071 3523", "0010 0003"};
char g_desText[20][64] = {"test data of the text type:0", "test data of the text type:1",
    "test data of the text type:2", "test data of the text type:3", "test data of the text type:4",
    "test data of the text type:5", "test data of the text type:6", "test data of the text type:7",
    "test data of the text type:8", "test data of the text type:9", "test data of the text type:0",
    "test data of the text type:1", "test data of the text type:2", "test data of the text type:3",
    "test data of the text type:4", "test data of the text type:5", "test data of the text type:6",
    "test data of the text type:7", "test data of the text type:8", "test data of the text type:9"};

#ifdef __cplusplus
extern "C" {
#endif

int DropTable(GmcStmtT *stmt, char *tableName)
{
    int ret = 0;
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "drop table %s;", tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    if (ret != 0) {
        AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNDEFINED_TABLE);
    }
    return ret;
}

int GmcSwapDataDirSleep(GmcStmtT *stmt, char *ctrlFilePath, char *tempFilePath)
{
    // 延时2秒触发插入数据或者查询开始
    sleep(2);
    int ret = 0;
    ret = GmcSwapDataDir(stmt, ctrlFilePath, tempFilePath);
    sleep(2);
    return ret;
}

int CreateTable(GmcStmtT *stmt, char *tableName, char *intervalValue, char *diskLimitValue, char *ttlValue,
    char *tempTablePath)
{
    int ret = 0;
    char sqlCmd[512] = {0};
    char tempDiskLimit[20] = {0};
    if (diskLimitValue == NULL) {
        (void)sprintf(tempDiskLimit, "1024 MB");
    } else {
        (void)sprintf(tempDiskLimit, "%s", diskLimitValue);
    }
    if (ttlValue == NULL) {
        if (tempTablePath == NULL) {
            (void)sprintf(sqlCmd,
                "create table %s(id integer, time integer, name char(64), ip inet, message blob(160),"
                " description text, id1 integer, time1 integer)with (time_col = 'time', interval = '%s', "
                " disk_limit = '%s', compression = 'fast(rapidlz)');",
                tableName, intervalValue, tempDiskLimit);
        } else {
            (void)sprintf(sqlCmd,
                "create table %s(id integer, time integer, name char(64), ip inet, message blob(160),"
                " description text, id1 integer, time1 integer)with (time_col = 'time', interval = '%s', "
                " disk_limit = '%s', compression = 'fast(rapidlz)', table_path = '%s',"
                " is_volatile_label = 'true');",
                tableName, intervalValue, tempDiskLimit, tempTablePath);
        }
    } else {
        if (tempTablePath == NULL) {
            (void)sprintf(sqlCmd,
                "create table %s(id integer, time integer, name char(64), ip inet, message blob(160),"
                " description text, id1 integer, time1 integer)with (time_col = 'time', interval = '%s',  "
                " disk_limit = '%s', ttl ='%s', compression = 'fast(rapidlz)');",
                tableName, intervalValue, tempDiskLimit, ttlValue);
        } else {
            (void)sprintf(sqlCmd,
                "create table %s(id integer, time integer, name char(64), ip inet, message blob(160),"
                " description text, id1 integer, time1 integer)with (time_col = 'time', interval = '%s',  "
                " disk_limit = '%s', ttl ='%s', compression = 'fast(rapidlz)', table_path = '%s',"
                " is_volatile_label = 'true');",
                tableName, intervalValue, tempDiskLimit, ttlValue, tempTablePath);
        }
    }
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    RETURN_IFERR(ret);
    return ret;
}

// 时序接口客户端建连设置msgReadTimeout
int TestTsGmcConnectTime(GmcConnT **conn, GmcStmtT **stmt, int syncMode = 0, char *connName = NULL)
{
    int ret = 0;
    YangConnOptionT tsConnOptions = {0};
    tsConnOptions.msgReadTimeout = 180 * 1000;
    tsConnOptions.isCsMode = true;
    tsConnOptions.serverLocator = g_connServerTsdb;
    ret = TestYangGmcConnect(conn, stmt, syncMode, &tsConnOptions);
    return ret;
}

// 注入顺序数据
int rowInsertData(GmcStmtT *stmt, char *tableName, int64_t dataCount, int64_t startTime)
{
    int ret = 0;
    int64_t count = dataCount > 100000 ? 100000 : dataCount;
    int64_t *id;
    int64_t *id1;
    int64_t *time;
    int64_t *timet;
    // 申请内存
    char *nameList = (char *)malloc(count * 64);
    if (nameList == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    char *ipList = (char *)malloc(count * 33);
    if (ipList == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    char **textList = (char **)malloc(count * sizeof(char *));
    if (textList == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    for (int i = 0; i < count; i++) {
        textList[i] = (char *)malloc(64 * sizeof(char));
        (void)memset(textList[i], 0, 64);
    }
    id = (int64_t *)malloc(sizeof(int64_t) * count);
    if (id == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    id1 = (int64_t *)malloc(sizeof(int64_t) * count);
    if (id == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    time = (int64_t *)malloc(sizeof(int64_t) * count);
    if (time == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    timet = (int64_t *)malloc(sizeof(int64_t) * count);
    if (timet == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    char **blobList = (char **)malloc(count * sizeof(char *));
    if (blobList == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    for (int i = 0; i < count; i++) {
        blobList[i] = (char *)malloc(160 * sizeof(char));
        (void)memset(blobList[i], 0, 160);
    }
    // 构造数据
    for (int i = 0; i < count; i++) {
        id[i] = i;
        id1[i] = i;
        // 1288630830  2010年11月2日 01：00：30 开始
        time[i] = startTime + i * 3;   // 2010年1月1日0点 1262275200
        timet[i] = startTime + i * 3;  // 2010年1月1日0点 1262275200
        int j = i % 20;
        memcpy((ipList + i * 33), (char *)g_ip[j], 33);
        memcpy((nameList + i * 64), (char *)g_name[j], 64);
        (void)sprintf(*(textList + i), "%s", (char *)g_desText[j]);
        // 释放内存时不能直接赋值，需要复制后才能释放
        (void)strcpy(blobList[i], g_message[j]);
    }

    uint32_t rowNum = dataCount > 100000 ? 100000 : dataCount;
    uint32_t loopNum = dataCount > 100000 ? (dataCount / 100000) : 1;
    // AW_FUN_Log(LOG_STEP, "插入数据开始");
    for (int i = 0; i < loopNum; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
        if (ret != 0) {
            goto freeAndReturn;
        }
        ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(int64_t));
        ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), 0);
        ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, time, sizeof(time[0]), 0);
        ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_FIXED, nameList, 64, NULL);
        ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_FIXED, ipList, 33, NULL);
        ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_BYTES, blobList, sizeof(blobList[0]), NULL);
        ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_STRING, textList, sizeof(char *), 0);
        ret = GmcBindCol(stmt, 6, (GmcDataTypeE)DB_DATATYPE_INT64, id1, sizeof(id1[0]), 0);
        ret = GmcBindCol(stmt, 7, (GmcDataTypeE)DB_DATATYPE_INT64, timet, sizeof(timet[0]), 0);
        ret = GmcExecute(stmt);
        if (ret != 0) {
            goto freeAndReturn;
        }
    }
    // AW_FUN_Log(LOG_STEP, "插入数据完成");
freeAndReturn:
    // 释放内存
    free(id);
    free(id1);
    free(time);
    free(timet);
    free(nameList);
    free(ipList);
    for (int i = 0; i < count; i++) {
        free(blobList[i]);
        free(textList[i]);
    }
    free(blobList);
    free(textList);
    return ret;
}

// 注入数据,时间列缓慢递增
int rowInsertDataTimeLow(GmcStmtT *stmt, char *tableName, int64_t dataCount, int64_t startTime)
{
    int ret = 0;
    int64_t count = dataCount > 100000 ? 100000 : dataCount;
    int64_t *id;
    int64_t *id1;
    int64_t *time;
    int64_t *timet;
    // 申请内存
    char *nameList = (char *)malloc(count * 64);
    if (nameList == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    char *ipList = (char *)malloc(count * 33);
    if (ipList == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    char **textList = (char **)malloc(count * sizeof(char *));
    if (textList == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    for (int i = 0; i < count; i++) {
        textList[i] = (char *)malloc(64 * sizeof(char));
        (void)memset(textList[i], 0, 64);
    }
    id = (int64_t *)malloc(sizeof(int64_t) * count);
    if (id == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    id1 = (int64_t *)malloc(sizeof(int64_t) * count);
    if (id == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    time = (int64_t *)malloc(sizeof(int64_t) * count);
    if (time == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    timet = (int64_t *)malloc(sizeof(int64_t) * count);
    if (timet == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    char **blobList = (char **)malloc(count * sizeof(char *));
    if (blobList == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    for (int i = 0; i < count; i++) {
        blobList[i] = (char *)malloc(160 * sizeof(char));
        (void)memset(blobList[i], 0, 160);
    }
    // 构造数据
    for (int i = 0; i < count; i++) {
        id[i] = i;
        id1[i] = i;
        time[i] = startTime + i / 500;   // 2010年1月1日0点 1262275200
        timet[i] = startTime + i / 500;  // 2010年1月1日0点 1262275200
        int j = i % 20;
        memcpy((ipList + i * 33), (char *)g_ip[j], 33);
        memcpy((nameList + i * 64), (char *)g_name[j], 64);
        (void)sprintf(*(textList + i), "%s", (char *)g_desText[j]);
        // 释放内存时不能直接赋值，需要复制后才能释放
        (void)strcpy(blobList[i], g_message[j]);
    }

    uint32_t rowNum = dataCount > 100000 ? 100000 : dataCount;
    uint32_t loopNum = dataCount > 100000 ? (dataCount / 100000) : 1;
    // AW_FUN_Log(LOG_STEP, "插入数据开始");
    for (int i = 0; i < loopNum; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
        if (ret != 0) {
            goto freeAndReturn;
        }
        ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(int64_t));
        ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), 0);
        ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, time, sizeof(time[0]), 0);
        ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_FIXED, nameList, 64, NULL);
        ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_FIXED, ipList, 33, NULL);
        ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_BYTES, blobList, sizeof(blobList[0]), NULL);
        ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_STRING, textList, sizeof(char *), 0);
        ret = GmcBindCol(stmt, 6, (GmcDataTypeE)DB_DATATYPE_INT64, id1, sizeof(id1[0]), 0);
        ret = GmcBindCol(stmt, 7, (GmcDataTypeE)DB_DATATYPE_INT64, timet, sizeof(timet[0]), 0);
        ret = GmcExecute(stmt);
        if (ret != 0) {
            goto freeAndReturn;
        }
    }
    // AW_FUN_Log(LOG_STEP, "插入数据完成");
freeAndReturn:
    // 释放内存
    free(id);
    free(id1);
    free(time);
    free(timet);
    free(nameList);
    free(ipList);
    for (int i = 0; i < count; i++) {
        free(blobList[i]);
        free(textList[i]);
    }
    free(blobList);
    free(textList);
    return ret;
}

typedef struct {
    int64_t *ids;
    int64_t *checkTimes;
    char (*names)[64];
    char (*ips)[33];
    char (*blobs)[160];
    int64_t *ids2;
    int64_t *times2;
    int64_t coefficients;  // 系数，比较数据时使用
    int64_t dataCount;
} QueryData;

void checkQueryData(GmcStmtT *stmt, const char *queryCommand, QueryData querydata)
{
    assert(queryCommand != NULL);
    uint32_t sqlStateLen = strlen(queryCommand);
    Status ret = GmcExecDirect(stmt, queryCommand, sqlStateLen);
    EXPECT_EQ(ret, GMERR_OK);
    // 查询过滤后的数据是否正确
    bool eof = false;
    bool isNull = false;
    int64_t cId = 0;
    int64_t cTime = 0;
    char cIp[33] = {0};
    char cName[64] = {0};
    uint8_t cBlob[160] = {0};
    uint32_t sizeInt = sizeof(int64_t);
    uint32_t sizeCharIp = sizeof(cIp);
    uint32_t sizeCharNmae = sizeof(cName);
    uint32_t sizeCharBolb = sizeof(cBlob);
    int64_t fetchTimes = 0;
    int64_t count = 0;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &cId, &sizeInt, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(querydata.ids[fetchTimes], cId);
        count = (cId / querydata.coefficients) % 20;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &cTime, &sizeInt, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(querydata.checkTimes[fetchTimes], cTime);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &cIp, &sizeCharIp, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_STR((const char *)querydata.ips[count], cIp);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &cName, &sizeCharNmae, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_STR((const char *)querydata.names[count], cName);
        sizeCharBolb = 1000;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &cBlob, &sizeCharBolb, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        EXPECT_EQ(memcmp(cBlob, querydata.blobs[count], 160), 0);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 5, &cId, &sizeInt, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(querydata.ids2[fetchTimes], cId);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 6, &cTime, &sizeInt, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(querydata.times2[fetchTimes], cTime);
        fetchTimes++;
    }
    AW_MACRO_EXPECT_EQ_INT(fetchTimes, querydata.dataCount);
}

typedef struct {
    int64_t *ids;
    int64_t *checkTimes;
    char (*names)[64];
    char (*ips)[33];
    char (*texts)[64];
    int64_t *ids2;
    int64_t *times2;
    int64_t coefficients;  // 系数，比较数据时使用
    int64_t dataCount;
} QueryData1;

void checkQueryDataGroupBy(GmcStmtT *stmt, QueryData1 querydata)
{
    int ret = 0;
    // 查询过滤后的数据是否正确
    bool eof = false;
    bool isNull = false;
    int64_t cId = 0;
    int64_t cTime = 0;
    char cIp[33] = {0};
    char cName[64] = {0};
    char cText[64] = {0};
    uint32_t sizeInt = sizeof(int64_t);
    uint32_t sizeCharIp = sizeof(cIp);
    uint32_t sizeCharNmae = sizeof(cName);
    uint32_t sizeCharText = sizeof(cText);
    int64_t fetchTimes = 0;
    int64_t count = 0;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (eof) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &cId, &sizeInt, &isNull);
        AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_ASSERT_EQ_INT(querydata.ids[fetchTimes], cId);
        count = (cId / querydata.coefficients) % 20;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &cTime, &sizeInt, &isNull);
        AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_ASSERT_EQ_INT(querydata.checkTimes[fetchTimes], cTime);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &cIp, &sizeCharIp, &isNull);
        AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_ASSERT_EQ_STR((const char *)querydata.ips[count], cIp);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &cName, &sizeCharNmae, &isNull);
        AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_ASSERT_EQ_STR((const char *)querydata.names[count], cName);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &cText, &sizeCharText, &isNull);
        AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_ASSERT_EQ_STR((const char *)querydata.texts[count], cText);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 5, &cId, &sizeInt, &isNull);
        AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_ASSERT_EQ_INT(querydata.ids2[fetchTimes], cId);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 6, &cTime, &sizeInt, &isNull);
        AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_ASSERT_EQ_INT(querydata.times2[fetchTimes], cTime);
        fetchTimes++;
    }
    AW_MACRO_ASSERT_EQ_INT(fetchTimes, querydata.dataCount);
}

typedef struct {
    int64_t *ids;
    int64_t *checkTimes;
    char (*names)[64];
    char (*ips)[33];
    char (*blobs)[160];
    char (*texts)[64];
    int64_t *ids2;
    int64_t *times2;
    int64_t coefficients;  // 系数，比较数据时使用
    int64_t dataCount;
} QueryData2;

void checkQueryDataBlob(GmcStmtT *stmt, QueryData2 querydata)
{
    int ret = 0;
    // 查询过滤后的数据是否正确
    bool eof = false;
    bool isNull = false;
    int64_t cId = 0;
    int64_t cTime = 0;
    char cIp[33] = {0};
    char cName[64] = {0};
    char cText[64] = {0};
    char cBlob[160] = {0};
    uint32_t sizeInt = sizeof(int64_t);
    uint32_t sizeCharIp = sizeof(cIp);
    uint32_t sizeCharNmae = sizeof(cName);
    uint32_t sizeCharText = sizeof(cText);
    uint32_t sizeCharBlob = sizeof(cBlob);
    int64_t fetchTimes = 0;
    int64_t count = 0;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (eof) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &cId, &sizeInt, &isNull);
        AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_ASSERT_EQ_INT(querydata.ids[fetchTimes], cId);
        count = (cId / querydata.coefficients) % 20;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &cTime, &sizeInt, &isNull);
        AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_ASSERT_EQ_INT(querydata.checkTimes[fetchTimes], cTime);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &cIp, &sizeCharIp, &isNull);
        AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_ASSERT_EQ_STR((const char *)querydata.ips[count], cIp);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &cName, &sizeCharNmae, &isNull);
        AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_ASSERT_EQ_STR((const char *)querydata.names[count], cName);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &cText, &sizeCharText, &isNull);
        AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_ASSERT_EQ_STR((const char *)querydata.texts[count], cText);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 5, &cId, &sizeInt, &isNull);
        AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_ASSERT_EQ_INT(querydata.ids2[fetchTimes], cId);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 6, &cTime, &sizeInt, &isNull);
        AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_ASSERT_EQ_INT(querydata.times2[fetchTimes], cTime);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 7, &cBlob, &sizeCharBlob, &isNull);
        AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
        fetchTimes++;
    }
    AW_MACRO_ASSERT_EQ_INT(fetchTimes, querydata.dataCount);
}

// 构造迁入路径不为空目录，修改地址以及启动服务
int emptyPathStartup(GmcConnT **conn, GmcStmtT **stmt, char *filePath, char *item)
{
    int ret = 0;
    // euler和仿真环境清共享内存，清理原有DB文件
#if defined RUN_INDEPENDENT
    system("sh $TEST_HOME/tools/stop.sh -f");
#endif
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts recover");
    if (filePath != NULL) {
        ret = ChangeTsGmserverCfg((char *)"dataFileDirPath", filePath);
        RETURN_IFERR(ret);
    }
    if (item != NULL) {
        ret = system(item);
        RETURN_IFERR(ret);
    }
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(2);
    ret = TestTsGmcConnectTime(conn, stmt);
    RETURN_IFERR(ret);
    return ret;
}

// 构造迁入路径不为空目录，断连以及恢复环境
int emptyPathRecover(GmcConnT *conn, GmcStmtT *stmt)
{
    int ret = 0;
    // 优雅关闭
    ret = GmcShutdownServer(conn);
    RETURN_IFERR(ret);
    bool procExist = true;
    int64_t timeout = 20;
    for (int i = 0; i < timeout; i++) {
        int ret = GtExecSystemCmd("pidof gmserver_ts");
        if (ret != GMERR_OK) {
            procExist = false;
            break;
        }
        sleep(1);
    }
    if (procExist) {
        AW_FUN_Log(LOG_STEP, "The process does not exit after %d seconds.", timeout);
        return FAILED;
    }
    ret = testGmcDisconnect(conn, stmt);
    RETURN_IFERR(ret);
    ret = testEnvClean();
    RETURN_IFERR(ret);
    RecoverTsCiCfg();
    return ret;
}

static int32_t TestPrintfDefault(const char *format, ...)
{
    va_list ap;
    va_start(ap, format);
    int32_t ret = vprintf(format, ap);
    va_end(ap);
    (void)fflush(stdout);
    return ret;
}

bool isDataCsvExist()
{
    if (FILE *file = fopen("data.csv", "r")) {
        fclose(file);
        return true;
    }
    return false;
}

void GetCsvFileContent(char returnValue[])
{
    FILE *file = fopen("data.csv", "r");
    char row[300];
    while (fgets(row, 300, file) != NULL) {
        strcat(returnValue, row);
    }
    (void)fclose(file);
}

int GetTempFileNumber(char *tempFileDir)
{
    char command[128];
    (void)snprintf(command, 128, "ls -1 %s | wc -l", tempFileDir);

    FILE *fp = popen(command, "r");
    if (fp == NULL) {
        AW_FUN_Log(LOG_STEP, "临时文件地址错误");
        return -1;
    }
    char result[20] = {0};
    char *flag1 = fgets(result, sizeof(result), fp);
    if (flag1 == NULL) {
        AW_FUN_Log(LOG_STEP, "获取临时文件数量错误");
        return -1;
    }
    int ret = atoi(result);
    pclose(fp);
    return ret;
}

typedef struct {
    GmcStmtT *stmt;
    char *tableName;
    int64_t dataCount;
    int64_t startTime;
} InsertQueryDataType;

// 并发写数据
void *InsertDataToTable(void *arg)
{
    InsertQueryDataType constructData = *(InsertQueryDataType *)arg;
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    int ret = 0;
    char sqlCmd[512] = {0};
    ret = TestTsGmcConnect(&conn1, &stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int randomNumber = rand() % 10;
    for (int i = 0; i < 100; i++) {
        (void)memset(sqlCmd, 0, 512);
        (void)sprintf(sqlCmd, "testdb%d", (i + randomNumber) % 100);
        ret = rowInsertData(stmt1, sqlCmd, constructData.dataCount, constructData.startTime);
        if (ret != GMERR_OK && ret != GMERR_UNDEFINED_TABLE) {
            AW_FUN_Log(LOG_STEP, "注入数据失败,ret is %d", ret);
        }
    }
    AW_FUN_Log(LOG_STEP, "插入数据完成");
    g_concurrentrStatus = ret;
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return nullptr;
}

// 并发查询数据
void *QueryDataToTable(void *arg)
{
    AW_FUN_Log(LOG_STEP, "查询开始");
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    int ret = 0;
    char sqlCmd[512] = {0};
    ret = TestTsGmcConnect(&conn1, &stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t cmdLen = 0;
    g_concurrentrStatus = GMERR_OK;
    for (int i = 0; i < 100; i++) {
        (void)memset(sqlCmd, 0, 512);
        (void)sprintf(sqlCmd,
            "select sum(iD + iD1), sum(time + tiMe1), ip, name, description, last(id1), count(time)  from testdb%d"
            " where name like '%%bob' or description like 'test data of the text type%%'"
            "  group by id, time, ip, name",
            i);
        cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(stmt1, sqlCmd, cmdLen);
        if (ret != GMERR_OK && ret != GMERR_UNDEFINED_TABLE) {
            AW_FUN_Log(LOG_STEP, "查询失败,ret is %d", ret);
            g_concurrentrStatus = ret;
        }
        if (ret == GMERR_CONNECTION_RESET_BY_PEER) {
            ret = TestTsGmcConnect(&conn1, &stmt1);
        }
    }
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return nullptr;
}

// 并发copy to
void *CopyToDataToTable(void *arg)
{
    AW_FUN_Log(LOG_STEP, "copy to开始");
    InsertQueryDataType constructData = *(InsertQueryDataType *)arg;
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    int ret = TestTsGmcConnect(&conn1, &stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *dir1 = getenv("PWD");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd,
        "COPY (select sum(iD + iD1), sum(time + tiMe1), ip, name, description, last(id1), count(time)  from %s"
        " where name like '%bob' group by id, time, ip, name) TO "
        " '%s/data1.csv';",
        constructData.tableName, dir1);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt1, sqlCmd, cmdLen);
    AW_FUN_Log(LOG_STEP, "copy to完成");
    g_concurrentrStatus = ret;
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return nullptr;
}

typedef struct {
    char *sourceTableName; // 源表
    char *objectiveTableName; // 目标表
    int cycleTime;
} InsertIntoDataType;

// 并发insertinto
void *InsertIntoDataToTable(void *arg)
{
    AW_FUN_Log(LOG_STEP, "insert into开始");
    int ret = 0;
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    ret = TestTsGmcConnect(&conn1, &stmt1);
    if (ret == GMERR_CONNECTION_FAILURE) {
        int reTry = 0;
        while (reTry < 10) {
            ret = TestTsGmcConnect(&conn1, &stmt1);
            if (ret == GMERR_OK) {
                reTry = 11;
                break;
            }
            reTry++;
            usleep(200000);
        }
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    InsertIntoDataType constructData = *(InsertIntoDataType *)arg;
    char sqlCmd[512] = {0};
    (void)memset(sqlCmd, 0, 512);
    uint32_t cmdLen = 0;
    for (int i = 0; i < constructData.cycleTime; i++) {
        // 执行insert into语句
        (void)sprintf(sqlCmd,
            "insert into %s(id, time, ip, name, description, id1, time1)"
            "select iD + iD1, time + tiMe1, ip, name, description, id1, time  from %s"
            " where name like '%bob' or name like 'd%d';",
            constructData.objectiveTableName, constructData.sourceTableName);
        cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(stmt1, sqlCmd, cmdLen);
        if (ret != 0) {
            AW_FUN_Log(LOG_STEP, "insert into失败, ret is %ld", ret);
            break;
        }
    }
    AW_FUN_Log(LOG_STEP, "insert into完成");
    g_concurrentrStatus = ret;
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return nullptr;
}

// 并发建表
void *ConcurrentCreateTable(void *arg)
{
    AW_FUN_Log(LOG_STEP, "建表开始");
    InsertQueryDataType constructData = *(InsertQueryDataType *)arg;
    GmcStmtT *stmttemp = constructData.stmt;
    int ret = 0;
    char sqlCmd[512] = {0};
    uint32_t cmdLen = 0;
    for (int i = 0; i < 100; i++) {
        (void)memset(sqlCmd, 0, 512);
        (void)sprintf(sqlCmd,
            "create table testdb%ld(id integer, time integer, name char(64), ip inet, message blob(160),"
            " description text, id1 integer, time1 integer)with (time_col = 'time', interval = '%s', "
            " disk_limit = '1024 MB', compression = 'fast(rapidlz)');",
            i, g_intervalHour);
        cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(stmttemp, sqlCmd, cmdLen);
        if (ret != 0) {
            AW_FUN_Log(LOG_STEP, "失败, ret is %ld", ret);
            break;
        }
    }
    AW_FUN_Log(LOG_STEP, "建表完成");
    g_concurrentrStatus = ret;

    return nullptr;
}

// 并发删表
void *ConcurrentDeleteTable(void *arg)
{
    AW_FUN_Log(LOG_STEP, "删表开始");
    InsertQueryDataType constructData = *(InsertQueryDataType *)arg;
    GmcStmtT *stmttemp = constructData.stmt;
    int ret = 0;
    char sqlCmd[512] = {0};
    uint32_t cmdLen = 0;
    for (int i = 0; i < 100; i++) {
        (void)memset(sqlCmd, 0, 512);
        (void)sprintf(sqlCmd, "drop table testdb%ld", i);
        cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(stmttemp, sqlCmd, cmdLen);
        if (ret != 0) {
            AW_FUN_Log(LOG_STEP, "失败, ret is %ld, table is testdb%d", ret, i);
            break;
        }
    }
    AW_FUN_Log(LOG_STEP, "删表完成");
    g_concurrentrStatus = ret;
    return nullptr;
}

// 并发建表
void *ConcurrentSwapDataDir(void *arg)
{
    AW_FUN_Log(LOG_STEP, "切换开始");
    InsertQueryDataType constructData = *(InsertQueryDataType *)arg;
    GmcStmtT *stmttemp = constructData.stmt;
    int ret = 0;
    ret = GmcSwapDataDir(stmttemp, constructData.tableName, NULL);
    AW_FUN_Log(LOG_STEP, "切换完成");
    sleep(1);
    g_concurrentrStatus = ret;
    return nullptr;
}

// 并发重启
void *ConcurrentRestart(void *arg)
{
    AW_FUN_Log(LOG_STEP, "重启开始");
    system("sh $TEST_HOME/tools/stop.sh -ts");
    sleep(1);
    system("sh $TEST_HOME/tools/start.sh -ts");
    return nullptr;
}

// 并发进行alter
void *ConcurrentAlter(void *arg)
{
    AW_FUN_Log(LOG_STEP, "ALTER开始");
    InsertQueryDataType constructData = *(InsertQueryDataType *)arg;
    int ret = 0;
    char sqlCmd[512] = {0};
    uint32_t cmdLen = 0;
    // 复用InsertQueryDataType结构体
    // dataCount表示需要执行多少次
    for (int i = 0; i < constructData.dataCount; i++) {
        (void)memset(sqlCmd, 0, 512);
        (void)sprintf(sqlCmd, "ALTER TABLE %s%d SET (ttl = '1 hour');",
            constructData.tableName, constructData.dataCount - i);
        cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(constructData.stmt, sqlCmd, cmdLen);
        if (ret != 0) {
            AW_FUN_Log(LOG_STEP, "失败, ret is %d,tableid is %d", ret, constructData.dataCount - i);
            break;
        }
    }
    AW_FUN_Log(LOG_STEP, "ALTER结束");
    g_concurrentrStatus = ret;
    return nullptr;
}

void InitTsCiCfgModify()
{
// euler和仿真环境清共享内存，清理原有DB文件
#if defined RUN_INDEPENDENT
    system("sh $TEST_HOME/tools/stop.sh -f");
#endif
    system("sh $TEST_HOME/tools/stop.sh -ts");
    TsDefulatDbFileClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts recover");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    // 有单跟踪 system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"redoFlushByTrx=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
}

// 注入顺序数据
int insertOutOfOrderData(GmcStmtT *stmt, char *tableName, int64_t dataCount)
{
    int ret = 0;
    int64_t count = dataCount;
    int64_t *id;
    int64_t *id1;
    int64_t *time;
    int64_t *timet;
    // 申请内存
    char *nameList = (char *)malloc(count * 64);
    if (nameList == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    char *ipList = (char *)malloc(count * 33);
    if (ipList == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    char **textList = (char **)malloc(count * sizeof(char *));
    if (textList == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    for (int i = 0; i < count; i++) {
        textList[i] = (char *)malloc(64 * sizeof(char));
        (void)memset(textList[i], 0, 64);
    }
    id = (int64_t *)malloc(sizeof(int64_t) * count);
    if (id == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    id1 = (int64_t *)malloc(sizeof(int64_t) * count);
    if (id == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    time = (int64_t *)malloc(sizeof(int64_t) * count);
    if (time == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    timet = (int64_t *)malloc(sizeof(int64_t) * count);
    if (timet == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    // 构造数据
    for (int i = 0; i < count; i++) {
        id[i] = i;
        id1[i] = i;
        // 2010年11月2日 01：00：30 开始
        time[i] = 10000 + rand() % 1000;  // 2010年1月1日0点 1262275200
        timet[i] = 10000 + i;             // 2010年1月1日0点 1262275200
        int j = i % 20;
        memcpy((ipList + i * 33), (char *)g_ip[j], 33);
        memcpy((nameList + i * 64), (char *)g_name[j], 64);
        (void)sprintf(*(textList + i), "%s", (char *)g_desText[j], 64);
    }
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    RETURN_IFERR(ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int64_t));
    RETURN_IFERR(ret);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    RETURN_IFERR(ret);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, time, sizeof(time[0]), NULL);
    RETURN_IFERR(ret);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_FIXED, nameList, 64, NULL);
    RETURN_IFERR(ret);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_STRING, textList, sizeof(textList[0]), NULL);
    RETURN_IFERR(ret);
    ret = GmcBindCol(stmt, 6, (GmcDataTypeE)DB_DATATYPE_INT64, id1, sizeof(id1[0]), 0);
    RETURN_IFERR(ret);
    ret = GmcBindCol(stmt, 7, (GmcDataTypeE)DB_DATATYPE_INT64, timet, sizeof(timet[0]), 0);
    RETURN_IFERR(ret);
    ret = GmcExecute(stmt);
    RETURN_IFERR(ret);
    // 释放内存
    free(id);
    free(id1);
    free(time);
    free(timet);
    free(nameList);
    free(ipList);
    free(textList);
    return ret;
}

// 注入顺序数据
int returnDataCount(GmcStmtT *stmt, char *tableName)
{
    char sqlCmd[512] = {0};
    int ret = 0;
    (void)sprintf(sqlCmd, "select * from %s", tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    RETURN_IFERR(ret);
    uint32_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(uint32_t));
    RETURN_IFERR(ret);
    return dataCount;
}

int32_t GetViewFieldResultValue(const char *viewName)
{
    int maxCmdSize = 256;
    char cmdOutput[maxCmdSize];
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));

    char command[maxCmdSize];
    (void)snprintf(command, maxCmdSize, "%s", viewName);

    FILE *pf = popen(command, "r");
    if (pf == NULL) {
        printf("popen(%s) error./n", command);
    }
    EXPECT_NE((void *)NULL, pf);
    while (NULL != fgets(cmdOutput, maxCmdSize, pf)) {
    };
    pclose(pf);

    return atoi(cmdOutput);
}

struct TableInfo {
    char tableName[20];
    bool isMemoryTable = false;
    char tablePath[256];
    bool isVolatile = false;
    int diskLimit;
    int ttl;
    int cacheSize;
};

// 根据参数建表
int createTableByParam(GmcStmtT *stmt, TableInfo *tableInfo)
{
    int ret = 0;
    char sqlCmd[512] = {0};
    uint32_t cmdLen = 0;
    if (tableInfo->isMemoryTable) {
        (void)sprintf(sqlCmd,
            "create table if not exists %s(id integer, time integer, name char(64), ip inet, message blob(160), "
            "description text, id1 integer, time1 integer) with (time_col = 'time', interval = '1 hour', disk_limit = "
            "'%d MB', ttl = '%d h', enGine = 'mEmOry', max_size = 1000000);",
            tableInfo->tableName, tableInfo->diskLimit, tableInfo->ttl);
    } else if (tableInfo->isVolatile) {
        (void)sprintf(sqlCmd,
            "create table if not exists %s(id integer, time integer, name char(64), ip inet, message blob(160), "
            "description text, id1 integer, time1 integer) with (time_col = 'time', interval = '1 hour', disk_limit = "
            "'%d MB', ttl = '%d h', cache_size = %d, table_path = '%s', is_volatile_label = 'true');",
            tableInfo->tableName, tableInfo->diskLimit, tableInfo->ttl, tableInfo->cacheSize, tableInfo->tablePath);
    } else {
        (void)sprintf(sqlCmd,
            "create table if not exists %s(id integer, time integer, name char(64), ip inet, message blob(160), "
            "description text, id1 integer, time1 integer) with (time_col = 'time', interval = '1 hour', disk_limit = "
            "'%d MB', ttl = '%d h', cache_size = %d);",
            tableInfo->tableName, tableInfo->diskLimit, tableInfo->ttl, tableInfo->cacheSize);
    }
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    return ret;
}

struct CreateInfo {
    int tableCount;
    // 0代表为普通逻辑表，1代表为易失性路径逻辑表，2代表为内存表
    int tableType;
    int startId;
};

void *ConcurrentCreateTableByParam(void *arg)
{
    CreateInfo createInfo = *(CreateInfo *)arg;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int tableCount = createInfo.tableCount;
    int tableType = createInfo.tableType;
    int startId = createInfo.startId;
    TableInfo tableInfos[tableCount];
    memset(tableInfos, 0, sizeof(TableInfo) * tableCount);
    for (int i = 0; i < tableCount; i++) {
        (void)sprintf(tableInfos[i].tableName, "testdb%d", i + startId);
        tableInfos[i].diskLimit = 20;
        tableInfos[i].ttl = 50;
        tableInfos[i].cacheSize = 100;
        if (tableType == 0) {
            ret = createTableByParam(stmt, &tableInfos[i]);
        } else if (tableType == 1) {
            (void)sprintf(tableInfos[i].tablePath, "%s/gmdb/", g_filePath);
            tableInfos[i].isVolatile = true;
            ret = createTableByParam(stmt, &tableInfos[i]);
        } else {
            tableInfos[i].isMemoryTable = true;
            ret = createTableByParam(stmt, &tableInfos[i]);
        }
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_STEP, "create table failed, tableName is %s", tableInfos[i].tableName);
            break;
        }
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return nullptr;
}

// 根据参数建表,表中不含cache_size
int createTableByParam1(GmcStmtT *stmt, TableInfo *tableInfo)
{
    int ret = 0;
    char sqlCmd[512] = {0};
    uint32_t cmdLen = 0;
    if (tableInfo->isMemoryTable) {
        (void)sprintf(sqlCmd,
            "create table %s(id integer, time integer, name char(64), ip inet, message blob(160), "
            "description text, id1 integer, time1 integer) with (time_col = 'time', interval = '1 hour', disk_limit = "
            "'%d MB', ttl = '%d h', enGine = 'mEmOry', max_size = 1000000);",
            tableInfo->tableName, tableInfo->diskLimit, tableInfo->ttl);
    } else if (tableInfo->isVolatile) {
        (void)sprintf(sqlCmd,
            "create table %s(id integer, time integer, name char(64), ip inet, message blob(160), "
            "description text, id1 integer, time1 integer) with (time_col = 'time', interval = '1 hour', disk_limit = "
            "'%d MB', ttl = '%d h', table_path = '%s', is_volatile_label = 'true');",
            tableInfo->tableName, tableInfo->diskLimit, tableInfo->ttl, tableInfo->tablePath);
    } else {
        (void)sprintf(sqlCmd,
            "create table %s(id integer, time integer, name char(64), ip inet, message blob(160), "
            "description text, id1 integer, time1 integer) with (time_col = 'time', interval = '1 hour', disk_limit = "
            "'%d MB', ttl = '%d h');",
            tableInfo->tableName, tableInfo->diskLimit, tableInfo->ttl);
    }
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    return ret;
}

// 并发建表，表中不含cache_size
void *ConcurrentCreateTableByParam1(void *arg)
{
    CreateInfo createInfo = *(CreateInfo *)arg;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int tableCount = createInfo.tableCount;
    int tableType = createInfo.tableType;
    int startId = createInfo.startId;
    TableInfo tableInfos[tableCount];
    memset(tableInfos, 0, sizeof(TableInfo) * tableCount);
    for (int i = 0; i < tableCount; i++) {
        (void)sprintf(tableInfos[i].tableName, "testdb%d", i + startId);
        tableInfos[i].diskLimit = 200;
        tableInfos[i].ttl = 50;
        tableInfos[i].cacheSize = 100;
        if (tableType == 0) {
            ret = createTableByParam1(stmt, &tableInfos[i]);
        } else if (tableType == 1) {
            (void)sprintf(tableInfos[i].tablePath, "%s/gmdb/", g_filePath);
            tableInfos[i].isVolatile = true;
            ret = createTableByParam1(stmt, &tableInfos[i]);
        } else {
            tableInfos[i].isMemoryTable = true;
            ret = createTableByParam1(stmt, &tableInfos[i]);
        }
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_STEP, "create table failed, tableName is %s", tableInfos[i].tableName);
            break;
        }
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return nullptr;
}

// 构造文件损坏, 是否损坏cuMetaFile, 损坏分区数，是否超过阈值
void DamageDataFile(bool isDamageMeta, uint32_t partitionNum, bool isDamageOverLimit, char *dataFilePath)
{
    char cmd[160] = {0};
    int err1 = sprintf_s(cmd, sizeof(cmd), "ls %scstore/", dataFilePath);
    if (err1 == -1) {
        ASSERT_EQ(true, false);
    }
    ASSERT_GE(err1, 0);
    FILE *fp = popen(cmd, "r");
    if (fp == NULL) {
        ASSERT_TRUE(false);
    }
    char result[20] = {0};
    char *flag1 = fgets(result, sizeof(result), fp);
    if (flag1 == NULL) {
        ASSERT_EQ(true, false);
    }
    int logicalId = atoi(result);
    pclose(fp);

    char cmd2[160] = {};
    int err = sprintf_s(cmd2, sizeof(cmd2), "ls %scstore/%d", dataFilePath, logicalId);
    if (err == -1) {
        ASSERT_EQ(true, false);
    }
    ASSERT_GE(err, 0);

    fp = popen(cmd2, "r");
    if (fp == NULL) {
        ASSERT_TRUE(false);
    }
    char result2[20] = {0};

    int *physicalId = (int *)malloc(sizeof(int) * partitionNum);
    if (physicalId == NULL) {
        ASSERT_EQ(true, false);
    }
    ASSERT_NE(physicalId, nullptr);

    for (uint32_t i = 0; i < partitionNum; i++) {
        char *flag2 = fgets(result2, sizeof(result2), fp);
        if (flag2 == NULL) {
            ASSERT_EQ(true, false);
        }
        physicalId[i] = atoi(result2);
    }
    pclose(fp);

    for (uint32_t i = 0; i < partitionNum; i++) {
        char cmd3[256] = {};
        err = sprintf_s(cmd3, sizeof(cmd3), "ls %scstore/%d/%d", dataFilePath, logicalId, physicalId[i]);
        ASSERT_GE(err, 0);
        fp = popen(cmd3, "r");
        if (fp == NULL) {
            ASSERT_TRUE(false);
        }
        char result3[20] = {0};
        char *flag3 = fgets(result3, sizeof(result3), fp);
        if (flag3 == NULL) {
            ASSERT_EQ(true, false);
        }
        pclose(fp);

        result3[strlen(result3) - 1] = '\0';  // 替换结尾换行符

        char filePath[512] = {};
        err = sprintf_s(filePath, sizeof(filePath), "%scstore/%d/%d/%s",
            dataFilePath, logicalId, physicalId[i], result3);
        ASSERT_GE(err, 0);

        FILE *file = fopen(filePath, "r+");
        ASSERT_EQ(true, file != NULL);

        err = fseek(file, 0, SEEK_SET);
        ASSERT_EQ(err, 0);

        // 破坏文件的内容
        if (!isDamageOverLimit) {
            err = fwrite("ab", 1, 2, file);  // 少量损坏
            ASSERT_GT(err, 0);
        } else {
            struct stat fileStat;
            stat(filePath, &fileStat);
            size_t damageFileSize = fileStat.st_size / 2;  // 损坏一半数据
            char *buf = (char *)malloc(damageFileSize);
            memset_s(buf, damageFileSize, 0x38, damageFileSize);

            err = fwrite(buf, 1, damageFileSize, file);

            free(buf);
            ASSERT_GT(err, 0);
        }

        // 关闭文件
        err = fclose(file);
        ASSERT_EQ(err, 0);

        if (isDamageMeta) {
            err = sprintf_s(filePath, sizeof(filePath), "%scstore/%d/%d/cuMetaFile",
                dataFilePath, logicalId, physicalId[i]);
            ASSERT_GE(err, 0);

            file = fopen(filePath, "r+");
            ASSERT_EQ(true, file != NULL);

            err = fseek(file, 0, SEEK_SET);
            ASSERT_EQ(err, 0);

            // 破坏文件的内容
            err = fwrite("k", 1, 1, file);
            ASSERT_GT(err, 0);

            // 关闭文件
            err = fclose(file);
            ASSERT_EQ(err, 0);
        }
    }
    free(physicalId);
}

int32_t GetKeyWordValue(const char *viewName, char *keyWord, char *cutSymbol)
{
    int maxCmdSize = 512;
    char cmdOutput[maxCmdSize];
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));

    char command[maxCmdSize];
    (void)snprintf(command, maxCmdSize, "%s", viewName);
    if (viewName == NULL) {
        (void)snprintf(command, maxCmdSize,
            "gmsysview -sql \"select * from 'V\\$COM_SHMEM_CTX' where CTX_NAME = 'devMgrMemctx'\" -s"
            "  %s | grep PEAK_ALLOC_SIZE |awk -F ':' '{print $2}' |awk -F ' ' '{print $1}' |awk -F '[' '{print $2}' "
            "|awk -F ']' '{print $1}' ",
            g_connServerTsdb);
    } else {
        (void)snprintf(command, maxCmdSize, "%s -s %s", viewName, g_connServerTsdb);
    }
    if (viewName != NULL) {
        (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
        (void)snprintf(command, maxCmdSize, "%s -s %s |grep %s", g_connServerTsdb, keyWord);
    }
    if (keyWord != NULL) {
        (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
        (void)snprintf(command, maxCmdSize, "%s -s %s |grep %s", viewName, g_connServerTsdb, keyWord);
    }
    if (cutSymbol != NULL) {
        (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
        (void)snprintf(command, maxCmdSize, "%s -s %s |grep %s | %s", viewName, g_connServerTsdb, keyWord, cutSymbol);
    }
    printf("command is %s\n", command);
    system(command);
    FILE *pf = popen(command, "r");
    if (pf == NULL) {
        printf("popen(%s) error./n", command);
    }
    EXPECT_NE((void *)NULL, pf);
    while (NULL != fgets(cmdOutput, maxCmdSize, pf)) {
    };
    pclose(pf);
    return atoi(cmdOutput);
}

int GetCpuNum()
{
    char command[128];
    (void)snprintf(command, 128, "cat /proc/cpuinfo | grep processor | wc -l");
    FILE *fp = popen(command, "r");
    if (fp == NULL) {
        AW_FUN_Log(LOG_STEP, "db文件地址错误");
        return -1;
    }
    char result[20] = {0};
    char *flag1 = fgets(result, sizeof(result), fp);
    if (flag1 == NULL) {
        AW_FUN_Log(LOG_STEP, "获取db文件数量错误");
        return -1;
    }
    int ret = atoi(result);
    pclose(fp);
    return ret;
}

// 并发设备复位
void *ConcurrentDeviceReset(void *arg)
{
    AW_FUN_Log(LOG_STEP, "复位开始");
    sleep(2);
    GmcUnInit();
    int ret = 0;
    char tempSqlCmd[1024] = {0};
    (void)sprintf(g_dataFilePath3, "%s/data/gmdb3/", g_filePath);
    system("rm -rf ./data/gmdb3");
    // 进程退出，清理共享内存，删除table_path目录，在空B目录启动，切换会有数据的A目录
    system("pkill gmserver_ts");
    system("ipcrm -a");
    (void)memset(tempSqlCmd, 0, 1024);
    (void)sprintf(tempSqlCmd, "rm -rf %s", g_tableFilePath);
    system(tempSqlCmd);
    (void)memset(tempSqlCmd, 0, 1024);
    (void)sprintf(tempSqlCmd, "sh $TEST_HOME/tools/modifyCfg.sh -ts \"dataFileDirPath=%s\"", g_dataFilePath3);
    system(tempSqlCmd);
    system("sh $TEST_HOME/tools/start.sh -ts");
    GmcInit();
    sleep(1);
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    ret = TestTsGmcConnect(&conn1, &stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 切换至目录B
    ret = GmcSwapDataDir(stmt1, g_dataFilePath, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_isInsertDataSuccessed = true;
    g_concurrentrStatus = ret;
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return nullptr;
}

// 获取关键字value
void GetViewFieldResultValueString(char result[], const char *viewName, const char *key)
{
    char cmdOutput[512] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));

    char command[512] = {0};
    (void)snprintf(command, 512, "%s |grep %s |awk -F ':' '{print $2}'", viewName, key);

    FILE *pf = popen(command, "r");
    if (pf == NULL) {
        AW_FUN_Log(LOG_ERROR, "popen(%s) error./n", command);
    }
    while (NULL != fgets(cmdOutput, 512, pf)) {
        (void)memcpy(result, cmdOutput, 512);
    };
    pclose(pf);
}

// 并发写数据
void *InsertDataTimeAdd(void *arg)
{
    InsertQueryDataType constructData = *(InsertQueryDataType *)arg;
    GmcStmtT *stmttemp = constructData.stmt;
    int ret = 0;
    int tempStartTime = constructData.startTime;
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    while (g_isInsertDataSuccessed) {
        if (tempStartTime > time_T + 36000) {
            tempStartTime = constructData.startTime;
        }
        ret = rowInsertData(stmttemp, constructData.tableName, constructData.dataCount, tempStartTime);
        if (ret != GMERR_OK && ret != GMERR_OUT_OF_MEMORY) {
            AW_FUN_Log(LOG_STEP, "ret is %d", ret);
            g_concurrentrStatus = ret;
        }
        tempStartTime += 3600;
    }
    AW_FUN_Log(LOG_STEP, "插入数据完成");
    return nullptr;
}

// 并发随机进行alter操作
void *ConcurrentRandomAlter(void *arg)
{
    AW_FUN_Log(LOG_STEP, "ALTER开始");
    InsertQueryDataType constructData = *(InsertQueryDataType *)arg;
    int ret = 0;
    char sqlCmd[512] = {0};
    uint32_t cmdLen = 0;
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    int randomNumber = 0;
    ret = TestTsGmcConnect(&conn1, &stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 复用InsertQueryDataType结构体
    // dataCount表示需要执行多少次
    for (int i = 0; i < constructData.dataCount; i++) {
        randomNumber = (rand() % 10) % 5;
        if (randomNumber > 2) {
            (void)memset(sqlCmd, 0, 512);
            (void)sprintf(sqlCmd, "ALTER TABLE testdb%d SET (ttl = '1 hour');",
                i);
            cmdLen = strlen(sqlCmd);
            ret = GmcExecDirect(constructData.stmt, sqlCmd, cmdLen);
        } else if (randomNumber <= 2 && randomNumber != 0) {
            (void)memset(sqlCmd, 0, 512);
            (void)sprintf(sqlCmd, "ALTER TABLE testdb%d SET (disk_limit = '5 MB');",
                i);
            cmdLen = strlen(sqlCmd);
            ret = GmcExecDirect(constructData.stmt, sqlCmd, cmdLen);
        } else {
            // 在线修改AllowDiskClean
            uint32_t setConfigValue = rand()%1;
            ret = GmcSetCfg(stmt1, "tsAllowDiskClean", GMC_DATATYPE_INT32, &setConfigValue, sizeof(uint32_t));
        }
        if (ret != 0) {
            AW_FUN_Log(LOG_STEP, "失败, ret is %d,random_number is: %d", ret, randomNumber);
        }
    }
    AW_FUN_Log(LOG_STEP, "ALTER结束");
    g_concurrentrStatus = ret;
    return nullptr;
}

// 并发Truncate
void *ConcurrentTruncate(void *arg)
{
    CreateInfo createInfo = *(CreateInfo *)arg;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    int tableCount = createInfo.tableCount;
    int startId = createInfo.startId;
    TableInfo tableInfos[tableCount];
    memset(tableInfos, 0, sizeof(TableInfo) * tableCount);
    for (int i = 0; i < tableCount; i++) {
        (void)sprintf(tableInfos[i].tableName, "testdb%d", i + startId);
        (void)sprintf(sqlCmd, "TRUNCATE TABLE %s;", tableInfos[i].tableName);
        ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_STEP, "Truncate failed, tableName is %s ret is %d", tableInfos[i].tableName, ret);
            continue;
        }
    }
    g_concurrentrStatus = ret;
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return nullptr;
}

// 超大规格表操作流程
// isCreate 建表 isLogicTable 逻辑表/内存表 isInjectData 批量注入 isInsertInto insert into操作 isTruncat Truncate表
int LargeScaleTableOperations(bool isCreate, bool isLogicTable, bool isInjectData, bool isInsertInto,
    bool isTruncate, int64_t startTime)
{
    int ret = 0;
    uint32_t cmdLen = 0;
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    char tableName[20] = {0};
    ret = TestTsGmcConnect(&conn1, &stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (isCreate) {
        if (isLogicTable) {
            // 创建256列的逻辑表
            char sql[10000] = "CREATE TABLE testdb0 (id0 integer, time0 integer, name0 char(64), ip0 inet, "
                "message0 blob(160), description0 text";
            for (int i = 1; i < 42; i++) {
                char col[107] = {0};
                (void)sprintf(col, ", id%d integer, time%d integer, name%d char(64), ip%d inet, message%d blob(160),"
                " description%d text", i, i, i, i, i, i);
                strcat(sql, col);
            }
            strcat(sql, ", id42 integer, time42 integer, name42 char(64), ip42 inet) "
                "with (time_col = 'time0', interval= '1 hour', disk_limit = '100 MB', ttl = '20 h');");
            cmdLen = strlen(sql);
            ret = GmcExecDirect(stmt1, sql, cmdLen);
        } else {
            // 创建256列的内存表
            char sql[10000] = "CREATE TABLE testdb1 (id0 integer, time0 integer, name0 char(64), ip0 inet, "
                "message0 blob(160), description0 text";
            for (int i = 1; i < 42; i++) {
                char col[107] = {0};
                (void)sprintf(col, ", id%d integer, time%d integer, name%d char(64), ip%d inet, message%d blob(160),"
                " description%d text", i, i, i, i, i, i);
                strcat(sql, col);
            }
            strcat(sql, ", id42 integer, time42 integer, name42 char(64), ip42 inet) "
                "with (time_col = 'time0', interval= '1 hour', disk_limit = '100 MB', ttl = '20 h', "
                "enGine = 'mEmOry', max_size = 1000000);");
            cmdLen = strlen(sql);
            ret = GmcExecDirect(stmt1, sql, cmdLen);
        }
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_STEP, "create Table fail, ret is %d", ret);
            RETURN_IFERR(ret);
        }
    }
    if (isInjectData) {
        int64_t count = 30000;
        if (isLogicTable) {
            (void)sprintf(tableName, "testdb0");
        } else {
            count = 1000;
            (void)sprintf(tableName, "testdb1");
        }
        int64_t *id;
        int64_t *id1;
        int64_t *time;
        int64_t *timet;
        // 申请内存
        char *nameList = (char *)malloc(count * 64);
        if (nameList == NULL) {
            AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
        }
        char *ipList = (char *)malloc(count * 33);
        if (ipList == NULL) {
            AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
        }
        char **textList = (char **)malloc(count * sizeof(char *));
        if (textList == NULL) {
            AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
        }
        for (int i = 0; i < count; i++) {
            textList[i] = (char *)malloc(64 * sizeof(char));
            (void)memset(textList[i], 0, 64);
        }
        id = (int64_t *)malloc(sizeof(int64_t) * count);
        if (id == NULL) {
            AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
        }
        id1 = (int64_t *)malloc(sizeof(int64_t) * count);
        if (id == NULL) {
            AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
        }
        time = (int64_t *)malloc(sizeof(int64_t) * count);
        if (time == NULL) {
            AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
        }
        timet = (int64_t *)malloc(sizeof(int64_t) * count);
        if (timet == NULL) {
            AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
        }
        char **blobList = (char **)malloc(count * sizeof(char *));
        if (blobList == NULL) {
            AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
        }
        for (int i = 0; i < count; i++) {
            blobList[i] = (char *)malloc(160 * sizeof(char));
            (void)memset(blobList[i], 0, 160);
        }
        // 构造数据
        for (int i = 0; i < count; i++) {
            id[i] = i;
            id1[i] = i;
            time[i] = startTime + i * 3;
            timet[i] = startTime + i * 3;
            int j = i % 20;
            memcpy((ipList + i * 33), (char *)g_ip[j], 33);
            memcpy((nameList + i * 64), (char *)g_name[j], 64);
            (void)sprintf(*(textList + i), "%s", (char *)g_desText[j]);
            // 释放内存时不能直接赋值，需要复制后才能释放
            (void)strcpy(blobList[i], g_message[j]);
        }
        int startNum = 0;
        
        ret = GmcPrepareStmtByLabelName(stmt1, tableName, GMC_OPERATION_SQL_INSERT);
        if (ret != 0) {
            AW_FUN_Log(LOG_STEP, "批量注入数据失败, ret is %d", ret);
            goto freeAndReturn;
        }
        ret = GmcSetStmtAttr(stmt1, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int64_t));
        startNum = 0;
        while (startNum < 252) {
            ret = GmcBindCol(stmt1, startNum, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), 0);
            ret = GmcBindCol(stmt1, startNum + 1, (GmcDataTypeE)DB_DATATYPE_INT64, time, sizeof(time[0]), 0);
            ret = GmcBindCol(stmt1, startNum + 2, (GmcDataTypeE)DB_DATATYPE_FIXED, nameList, 64, NULL);
            ret = GmcBindCol(stmt1, startNum + 3, (GmcDataTypeE)DB_DATATYPE_FIXED, ipList, 33, NULL);
            ret = GmcBindCol(stmt1, startNum + 4, (GmcDataTypeE)DB_DATATYPE_BYTES, blobList, sizeof(blobList[0]), NULL);
            ret = GmcBindCol(stmt1, startNum + 5, (GmcDataTypeE)DB_DATATYPE_STRING, textList, sizeof(char *), 0);
            startNum += 6;
        }
        ret = GmcBindCol(stmt1, 252, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), 0);
        ret = GmcBindCol(stmt1, 253, (GmcDataTypeE)DB_DATATYPE_INT64, time, sizeof(time[0]), 0);
        ret = GmcBindCol(stmt1, 254, (GmcDataTypeE)DB_DATATYPE_FIXED, nameList, 64, NULL);
        ret = GmcBindCol(stmt1, 255, (GmcDataTypeE)DB_DATATYPE_FIXED, ipList, 33, NULL);
        ret = GmcExecute(stmt1);
        if (ret != 0) {
            AW_FUN_Log(LOG_STEP, "批量注入数据失败, tableName is %s ret is %d", tableName, ret);
            goto freeAndReturn;
        }
freeAndReturn:
        // 释放内存
        free(id);
        free(id1);
        free(time);
        free(timet);
        free(nameList);
        free(ipList);
        for (int i = 0; i < count; i++) {
            free(blobList[i]);
            free(textList[i]);
        }
        free(blobList);
        free(textList);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

typedef struct {
    char *sqlCmd;
    int64_t cycleTime;
} ConcurrentOperationsType;

// 并发操作
void *ConcurrentOperations(void *arg)
{
    ConcurrentOperationsType concurrentData = *(ConcurrentOperationsType *)arg;
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    int ret = 0;
    ret = TestTsGmcConnect(&conn1, &stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t cmdLen = 0;
    for (int i = 0; i < concurrentData.cycleTime; i++) {
        cmdLen = strlen(concurrentData.sqlCmd);
        ret = GmcExecDirect(stmt1, concurrentData.sqlCmd, cmdLen);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_STEP, "并发操作失败,ret is %d", ret);
        }
    }
    AW_FUN_Log(LOG_STEP, "并发操作完成");
    g_concurrentrStatus = ret;
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return nullptr;
}

void *CurrentTruncateTable(void *arg)
{
    AW_FUN_Log(LOG_STEP, "Truncate start");
    InsertQueryDataType constructData = *(InsertQueryDataType *)arg;
    GmcStmtT *stmttemp = constructData.stmt;
    int ret = 0;
    char sqlCmd[512] = {0};
    uint32_t cmdLen = 0;
    (void)sprintf(sqlCmd, "truncate table %s;", constructData.tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmttemp, sqlCmd, cmdLen);
    AW_FUN_Log(LOG_STEP, "Truncate finished");
    g_concurrentrStatus = ret;
    return nullptr;
}

typedef struct {
    int64_t coefficients;
    int64_t swapCount;
} SwapData;

#ifdef __cplusplus
}
#endif

#endif /* TSDB_STORAGE_INSTANCE_SWITCHOVER_H */
