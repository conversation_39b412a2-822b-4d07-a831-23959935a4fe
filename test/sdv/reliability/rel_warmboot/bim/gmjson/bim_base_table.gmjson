{"version": "2.0", "type": "record", "name": "bim_base_table", "config": {"check_validity": false, "defragmentation": true, "use_write_cache": true, "is_support_reserved_memory": true}, "max_record_count": 50000, "fields": [{"name": "Key", "type": "bytes"}, {"name": "Value", "type": "bytes"}], "keys": [{"name": "Index0", "index": {"type": "primary"}, "node": "bim_base_table", "fields": ["Key"], "constraints": {"unique": true}}]}