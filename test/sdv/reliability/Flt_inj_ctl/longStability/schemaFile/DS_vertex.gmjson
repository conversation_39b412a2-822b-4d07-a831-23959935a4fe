[{"version": "2.0", "type": "record", "name": "vertex_DS", "comment": "除了key和hash，其余属性大小都为256（字节），一共16个属性，大小为4k", "fields": [{"name": "PK0", "type": "uint32"}, {"name": "PK1", "type": "uint32"}, {"name": "Hash0", "type": "uint32"}, {"name": "Hash1", "type": "uint32"}, {"name": "F0", "type": "fixed", "size": 256, "nullable": true}, {"name": "F1", "type": "fixed", "size": 256, "nullable": true}, {"name": "F2", "type": "fixed", "size": 256, "nullable": true}, {"name": "F3", "type": "fixed", "size": 256, "nullable": true}, {"name": "F4", "type": "fixed", "size": 256, "nullable": true}, {"name": "F5", "type": "fixed", "size": 256, "nullable": true}, {"name": "F6", "type": "fixed", "size": 256, "nullable": true}, {"name": "F7", "type": "fixed", "size": 256, "nullable": true}, {"name": "F8", "type": "fixed", "size": 256, "nullable": true}, {"name": "F9", "type": "fixed", "size": 256, "nullable": true}, {"name": "F10", "type": "fixed", "size": 256, "nullable": true}, {"name": "F11", "type": "fixed", "size": 256, "nullable": true}, {"name": "F12", "type": "fixed", "size": 256, "nullable": true}, {"name": "F13", "type": "fixed", "size": 256, "nullable": true}, {"name": "F14", "type": "fixed", "size": 256, "nullable": true}, {"name": "F15", "type": "fixed", "size": 256, "nullable": true}], "super_fields": [{"name": "DS_superfields", "comment": "为了批量赋值", "fields": {"begin": "F0", "end": "F15"}}], "keys": [{"name": "vertex_DS_PK", "index": {"type": "primary"}, "node": "vertex_DS", "fields": ["PK0", "PK1"], "constraints": {"unique": true}, "comment": "主键索引"}, {"name": "vertex_DS_lh", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "vertex_DS", "fields": ["Hash0", "Hash1"], "constraints": {"unique": false}, "comment": "非唯一hash索引"}]}]