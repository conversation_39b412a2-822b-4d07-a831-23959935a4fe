/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * File Name: rel_workpool_datalog.cpp
 * Description: test_for_DTS2025062529466
 * Author: qibingsen 00880292
 * Create: 2025-07-21
 */

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"

#define MAX_NAME_LENGTH 512
#define MAX_CMD_SIZE 512
#define MAX_FILE_PATH 512

typedef int (*FuncWrite)(GmcStmtT *stmt, void *t);
char g_user[MAX_NAME_LENGTH] = "user";
char g_namespace[MAX_NAMESPACE_LENGTH] = "public";
char g_subConnName[MAX_NAME_LENGTH] = "subConnName";
char g_notifytable[MAX_NAME_LENGTH] = "notifytable";
char g_namespace1[MAX_NAMESPACE_LENGTH] = "nsp1";
char g_namespace2[MAX_NAMESPACE_LENGTH] = "nsp2";
char g_inp1[MAX_NAME_LENGTH] = "inp1";
uint32_t g_fetchNum = 1;
int g_wrongTimes = 0;

GmcConnT *g_connSub = NULL, *g_conn = NULL, *g_conn_replay_nsp2 = NULL;
GmcStmtT *g_stmtSub = NULL, *g_stmt = NULL, *g_stmt_replay_nsp2 = NULL;

typedef struct ComplexTableStruct {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int8_t a1;
    int8_t a2;
    int8_t a3;
    int8_t a4;
    int8_t b1;
    int8_t b2;
    int8_t b3;
    int8_t b4;
    int8_t c1;
    int8_t c2;
    int8_t c3;
    int8_t c4;
    int8_t c5;
    int8_t c6;
    int8_t c7;
    int8_t c8;
    int8_t c9;
    int8_t c10;
    int8_t a11;
    int8_t a21;
    int8_t a31;
    int8_t a41;
    int8_t b11;
    int8_t b21;
    int8_t b31;
    int8_t b41;
    int8_t c11;
    int8_t c21;
    int8_t c31;
    int8_t c41;
    int8_t c51;
    int8_t c61;
    int8_t c71;
    int8_t c81;
    int8_t c91;
    int8_t c101;
    int8_t a12;
    int16_t a22;
    int32_t a32;
    int64_t a42;
    uint8_t b12;
    uint16_t b22;
    uint32_t b32;
    uint64_t b42;
    uint8_t c12[1];
    uint8_t c22[2];
    uint8_t c32[4];
    uint8_t c42[8];
    uint8_t c52[16];
    uint8_t c62[32];
    uint8_t c72[64];
    uint8_t c82[128];
    uint8_t c92[256];
    uint8_t c102[512];
    int32_t d9len;
    char *d9;
    uint16_t d10len;
    uint8_t *d10;
} ComplexTableStructT;

typedef struct TaglabelCfg {
    GmcOperationTypeE opType;
    char *labelName;
    int32_t writeCount;  // 主键或其他非成员索引的数量
    bool isBatch;
    bool isStruct;
    char *namespaceName = g_namespace1;
} GtlabelCfgT;


typedef struct LoadDataLogCfg {
    char *localnamespace = NULL;
    int32_t epoll_num = 0;
} LoadDataLogCfgT;
int32_t judgeresp = 0;

int ret = 0;

typedef struct DlrReplayData {
    char *replaytablename;
    char *userBuf;
    uint32_t bufSize;
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    GmcConnT *subConn;
    int32_t countcallback;
    int32_t sendEof = 1;
} DlrReplayDataT;


// 生成byte变长字段
int Generate_Bytes(uint8_t **bytes, uint32_t byteLen)
{
    uint8_t *byteArray = (uint8_t *)malloc(byteLen + 1);
    if (byteArray == NULL) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    for (uint32_t i = 0; i < byteLen; i++) {
        int num = i;
        while (num >= 10) {
            num = num % 10;
        }
        byteArray[i] = (uint8_t)num;
    }
    byteArray[byteLen] = '\n';
    *bytes = byteArray;
    return GMERR_OK;
}

int createDlrSubscription(GmcStmtT *stmtSync, GmcConnT *connSub, char *subJsonPath, DlrReplayData *subData,
                          uint32_t mallocCount, const char *subsName, GmcSubCallbackT userCb)
{
    char *subInfo = NULL;
    readJanssonFile(subJsonPath, &subInfo);
    AW_MACRO_EXPECT_NOTNULL(subInfo);
    GmcSubConfigT tmpSubInfo;
    tmpSubInfo.subsName = subsName;
    tmpSubInfo.configJson = subInfo;
    int ret = GmcSubscribe(stmtSync, &tmpSubInfo, connSub, userCb, subData);
    RETURN_IFERR(ret);
    free(subInfo);
    return GMERR_OK;
}

int cancelDlrSubscription(GmcStmtT *stmtSync, const char *subsName)
{
    int ret = GmcUnSubscribe(stmtSync, subsName);
    RETURN_IFERR(ret);
    return GMERR_OK;
}

int ComplexTableSet(GmcStmtT *stmt, void *t)
{
    ComplexTableStructT *obj = (ComplexTableStructT *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a12", GMC_DATATYPE_INT8, &obj->a12, sizeof(int8_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ComplexTableSet] a12: %d, ret = %d.", obj->a12, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "a22", GMC_DATATYPE_INT16, &obj->a22, sizeof(int16_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ComplexTableSet] a22: %d, ret = %d.", obj->a22, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "a32", GMC_DATATYPE_INT32, &obj->a32, sizeof(int32_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ComplexTableSet] a32: %d, ret = %d.", obj->a32, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "a42", GMC_DATATYPE_INT64, &obj->a42, sizeof(int64_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ComplexTableSet] a42: %d, ret = %d.", obj->a42, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "b12", GMC_DATATYPE_UINT8, &obj->b12, sizeof(uint8_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ComplexTableSet] b12: %d, ret = %d.", obj->b12, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "b22", GMC_DATATYPE_UINT16, &obj->b22, sizeof(uint16_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ComplexTableSet] b22: %d, ret = %d.", obj->b22, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "b32", GMC_DATATYPE_UINT32, &obj->b32, sizeof(uint32_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ComplexTableSet] b32: %d, ret = %d.", obj->b32, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "b42", GMC_DATATYPE_UINT64, &obj->b42, sizeof(uint64_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ComplexTableSet] b42: %d, ret = %d.", obj->b42, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "c12", GMC_DATATYPE_FIXED, obj->c12, sizeof(obj->c12));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ComplexTableSet] c12, ret = %d.", ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "c22", GMC_DATATYPE_FIXED, obj->c22, sizeof(obj->c22));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ComplexTableSet] c22, ret = %d.", ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "c32", GMC_DATATYPE_FIXED, obj->c32, sizeof(obj->c32));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ComplexTableSet] c32, ret = %d.", ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "c42", GMC_DATATYPE_FIXED, obj->c42, sizeof(obj->c42));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ComplexTableSet] c42, ret = %d.", ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "c52", GMC_DATATYPE_FIXED, obj->c52, sizeof(obj->c52));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ComplexTableSet] c52, ret = %d.", ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "c62", GMC_DATATYPE_FIXED, obj->c62, sizeof(obj->c62));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ComplexTableSet] c62, ret = %d.", ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "c72", GMC_DATATYPE_FIXED, obj->c72, sizeof(obj->c72));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ComplexTableSet] c72, ret = %d.", ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "c82", GMC_DATATYPE_FIXED, obj->c82, sizeof(obj->c82));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ComplexTableSet] c82, ret = %d.", ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "c92", GMC_DATATYPE_FIXED, obj->c92, sizeof(obj->c92));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ComplexTableSet] c92, ret = %d.", ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "c102", GMC_DATATYPE_FIXED, obj->c102, sizeof(obj->c102));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ComplexTableSet] c102, ret = %d.", ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "d9", GMC_DATATYPE_STRING, obj->d9, (strlen((char *)obj->d9)));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ComplexTableSet] d9: %s, ret = %d.", obj->d9, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "d10", GMC_DATATYPE_BYTES, obj->d10, obj->d10len);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ComplexTableSet] d10: %s, ret = %d.", obj->d10, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "upgradeVersion", GMC_DATATYPE_INT32, &obj->upgradeVersion,
                               sizeof(obj->upgradeVersion));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ComplexTableSet] upgradeVersion: %d, ret = %d.", obj->upgradeVersion, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount,
                               sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ComplexTableSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return GMERR_OK;
}


// 无位域字段
void SetComplexTableValue(ComplexTableStructT *objIn, char *strT, int recordNum = 100, int startValue = 0)
{
    for (int i = startValue; i < startValue + recordNum; i++) {
        objIn[i - startValue].a12 = i;
        objIn[i - startValue].a22 = i;
        objIn[i - startValue].a32 = i;
        objIn[i - startValue].a42 = i;
        objIn[i - startValue].b12 = i;
        objIn[i - startValue].b22 = i;
        objIn[i - startValue].b32 = i;
        objIn[i - startValue].b42 = i;
        for (int j = 0; j < 1; j++) {
            objIn[i - startValue].c12[j] = (i - startValue + 1) % 50;
        }
        for (int j = 0; j < 2; j++) {
            objIn[i - startValue].c22[j] = (i - startValue + 1) % 50;
        }
        for (int j = 0; j < 4; j++) {
            objIn[i - startValue].c32[j] = (i - startValue + 1) % 50;
        }
        for (int j = 0; j < 8; j++) {
            objIn[i - startValue].c42[j] = (i - startValue + 1) % 50;
        }
        for (int j = 0; j < 16; j++) {
            objIn[i - startValue].c52[j] = (i - startValue + 1) % 50;
        }
        for (int j = 0; j < 32; j++) {
            objIn[i - startValue].c62[j] = (i - startValue + 1) % 50;
        }
        for (int j = 0; j < 64; j++) {
            objIn[i - startValue].c72[j] = (i - startValue + 1) % 50;
        }
        for (int j = 0; j < 128; j++) {
            objIn[i - startValue].c82[j] = (i - startValue + 1) % 50;
        }
        for (int j = 0; j < 256; j++) {
            objIn[i - startValue].c92[j] = (i - startValue + 1) % 50;
        }
        for (int j = 0; j < 512; j++) {
            objIn[i - startValue].c102[j] = (i - startValue + 1) % 50;
        }
        objIn[i - startValue].d10len = 10;
        objIn[i - startValue].d9 = strT;
        objIn[i - startValue].d9len = 10;
        (void)snprintf((char *)objIn[i - startValue].d9, objIn[i - startValue].d9len, "0x%07d",
                       (i - startValue + 1) % 10);
        int ret = Generate_Bytes(&objIn[i - startValue].d10, objIn[i - startValue].d10len);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        objIn[i - startValue].dtlReservedCount = 1;
        objIn[i - startValue].upgradeVersion = 0;
    }
}

void sn_callback_sleep_NULL(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *subDataTest)
{
    int ret = 0;
    AW_FUN_Log(LOG_INFO, "sn_callback_sleep_NULL, judgeresp = %d\n", judgeresp);
    DlrReplayData *replayData = (DlrReplayData *)subDataTest;
    GmcRespT *response = NULL;
    ret = GmcCreateResp(subStmt, &response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetRespMode(response, GMC_RESP_SEND_FAILED_INDEX);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint16_t failedDataNum = 0;
    uint16_t failedIndexes[GMC_SUB_BATCH_MAX] = { 0 };
    ret = GmcSetSubFailedIndex(response, failedDataNum, failedIndexes);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool getEof = false;
    bool isEof = false;
    sleep(3);
    while (judgeresp <= 2){}
    ret = GmcSendResp(subStmt, response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDestroyResp(subStmt, response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (replayData->sendEof == 0) {
        replayData->sendEof = 0;
    } else {
        replayData->sendEof--;
    }
}
int BatchPrepare(GmcConnT *conn, GmcBatchT **batch)
{
    GmcBatchOptionT batchOption;
    int ret = GmcBatchOptionInit(&batchOption);
    RETURN_IFERR(ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    RETURN_IFERR(ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    RETURN_IFERR(ret);
    ret = GmcBatchPrepare(conn, &batchOption, batch);
    RETURN_IFERR(ret);
    return ret;
}

template <typename StructObjT>
int WriteKnownData(GmcConnT *conn, GmcStmtT *stmt, GtlabelCfgT vertexCfg, FuncWrite func, StructObjT *obj,
                   bool isLargeBatch = false, char *strcutLabelName = NULL);

template <typename StructObjT>
int WriteKnownData(GmcConnT *conn, GmcStmtT *stmt, GtlabelCfgT vertexCfg, FuncWrite func, StructObjT *obj,
                   bool isLargeBatch, char *strcutLabelName)
{
    int ret = 0;

    char *labelName = vertexCfg.labelName;
    GmcOperationTypeE opType = vertexCfg.opType;
    int32_t vertexCount = vertexCfg.writeCount;
    bool isBatch = vertexCfg.isBatch;
    bool isStruct = vertexCfg.isStruct;

    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    RETURN_IFERR(ret);

    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    if (isBatch) {
        ret = BatchPrepare(conn, &batch);
        RETURN_IFERR(ret);
        if (isStruct) {
            ret = GmcBatchBindStmt(batch, stmt);
            RETURN_IFERR(ret);
        }
    }
    int32_t j = 0;
    for (int32_t i = 0; i < vertexCount; i++) {
        j++;
        if (isStruct) {
            TestLabelInfoT labelInfo = { (char *)strcutLabelName, 0, vertexCfg.namespaceName };
            ret = testStructSetVertexWithBuf(stmt, (obj + i), &labelInfo);
        } else {
            ret = func(stmt, (void *)(obj + i));
        }
        RETURN_IFERR(ret);
        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            RETURN_IFERR(ret);
            if (j % 50000 == 0) {
                AW_FUN_Log(LOG_INFO, "this is the %ds batch add DMLL.", i);
                system("ps -ef");
            }
        } else {
            ret = GmcExecute(stmt);
            RETURN_IFERR(ret);
        }
        
    }
    if (isBatch) {
        ret = GmcBatchExecute(batch, &batchRet);
        if (ret != 0) {
            AW_FUN_Log(LOG_INFO, "batchexecute unsuccessfully\n");
        }
        RETURN_IFERR(ret);
        uint32_t totalNum, successNum;
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        RETURN_IFERR(ret);
        RETURN_IFERR((int32_t)totalNum != vertexCount);
        RETURN_IFERR((int32_t)successNum != vertexCount);
        if (isStruct) {
            GmcBatchUnbindStmt(batch, stmt);
        }
        GmcBatchDestroy(batch);
    }
    return ret;
}

void *MultiThreadUnLoadDataLog(void *args)
{
    int ret = 0;
    LoadDataLogCfgT *cfg = (LoadDataLogCfgT *)args;
    char *localnamespace = cfg->localnamespace;
    int32_t epoll_num = cfg->epoll_num;
    AW_FUN_Log(LOG_INFO, "epoll_num:%d", epoll_num);
    GmcStmtT *localstmt = NULL;
    GmcConnT *localconn = NULL;
    int writeCount = 5;
    ComplexTableStructT *objIn = (ComplexTableStructT *)malloc(sizeof(ComplexTableStructT) * writeCount);
    char str[10] = {0};
    bool isBatch = true, isStruct = false;
    GtlabelCfgT vertexCfg = { GMC_OPERATION_INSERT, g_inp1, writeCount, isBatch, isStruct };
    SetComplexTableValue(objIn, str, writeCount);
    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[epoll_num].userEpollFd;
    ret = TestYangGmcConnect(&localconn, &localstmt, GMC_CONN_TYPE_SYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(localstmt, cfg->localnamespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    judgeresp++;
    AW_FUN_Log(LOG_INFO, "sn_callback_sleep_NULL, MultiThreadUnLoadDataLog = %d\n", judgeresp);
    ret = WriteKnownData(localconn, localstmt, vertexCfg, ComplexTableSet, objIn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(localconn, localstmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

class datalogworkpool : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void datalogworkpool::SetUpTestCase()
{
    system("sh ${TEST_HOME}/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSignalRegisterNotify();
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 7; i++) {
        ret = create_epoll_thread(&g_epAsync[i]);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
}
void datalogworkpool::TearDownTestCase()
{
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void datalogworkpool::SetUp()
{
    printf("\n======================TEST:BEGIN======================\n");
    system("rm -rf /root/_datalog_/");
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
#ifndef RUN_INDEPENDENT
    system("echo \"\" > /opt/vrpv8/home/<USER>/diag.log");
    sleep(3);
#endif
    system("sh ${TEST_HOME}/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"workerHungThreshold=11,200,300\"");

    system("sh $TEST_HOME/tools/modifyCfg.sh \"datalogCallBackTimeoutThreshold=15\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"trxLockTimeOut=12000\"");
    system("sh ${TEST_HOME}/tools/start.sh");
    AW_FUN_Log(LOG_STEP, "server start.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    // 创建同步客户端连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubConnect(&g_connSub, &g_stmtSub, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
#ifndef  RUN_INDEPENDENT
    AW_ADD_TRUNCATION_WHITE_LIST(1, "The resource usage of the GMDBV5 component is about to exceed the upper limit");
#endif
}

void datalogworkpool::TearDown()
{
    AW_CHECK_LOG_END();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    // 断开同步连接
    ret = testGmcDisconnect(g_connSub, g_stmtSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
    printf("\n======================TEST:END========================\n");
}

/* ****************************************************************************
用例步骤：创建3个命名空间，每个命名空间包含1个notify表，分别使用不同epoll下三个订阅连接订阅三个notify表，
并发向三个命名空间的输入表中写数据，触发notify表订阅推送，在仅使用定时创建新线程的情况下，预期回调不超时，回调推送完成。
**************************************************************************** */
TEST_F(datalogworkpool, DataLog_reliability_workpool_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sofilepath[MAX_FILE_PATH] = "datalogfile/rel_datalog.so";
    char dname[MAX_FILE_PATH] = "rel_datalog";
    ret = testGmcConnect(&g_conn_replay_nsp2, &g_stmt_replay_nsp2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char g_namespace3[MAX_NAME_LENGTH] = "nsp3";
    GmcConnT *g_connSub0 = NULL, *g_connSub1 = NULL, *g_connSub2 = NULL;
    GmcStmtT *g_stmtSub0 = NULL,*g_stmtSub1 = NULL, *g_stmtSub2 = NULL;
    char g_subConnName0[MAX_NAME_LENGTH] = "subConnName0";
    char g_subConnName1[MAX_NAME_LENGTH] = "subConnName1";
    char g_subConnName2[MAX_NAME_LENGTH] = "subConnName2";
    YangConnOptionT connOptions0 = { 0 };
    connOptions0.epollFd = &g_epAsync[1].userEpollFd;
    connOptions0.connName = g_subConnName0;
    ret = TestYangGmcConnect(&g_connSub0, &g_stmtSub0, GMC_CONN_TYPE_SUB, &connOptions0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    YangConnOptionT connOptions1 = { 0 };
    connOptions1.epollFd = &g_epAsync[2].userEpollFd;
    connOptions1.connName = g_subConnName1;
    ret = TestYangGmcConnect(&g_connSub1, &g_stmtSub1, GMC_CONN_TYPE_SUB, &connOptions1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    YangConnOptionT connOptions2 = { 0 };
    connOptions2.epollFd = &g_epAsync[3].userEpollFd;
    connOptions2.connName = g_subConnName2;
    ret = TestYangGmcConnect(&g_connSub2, &g_stmtSub2, GMC_CONN_TYPE_SUB, &connOptions2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespace(g_stmt, g_namespace1, g_user);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespace(g_stmt, g_namespace2, g_user);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespace(g_stmt, g_namespace3, g_user);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt_replay_nsp2, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestInterfaceLoadDatalog(g_stmt, sofilepath, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestInterfaceLoadDatalog(g_stmt, sofilepath, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestInterfaceLoadDatalog(g_stmt, sofilepath, g_namespace3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t bufSize = 20480;  // 1k
    char *subname = "sub1", *subname1 = "sub2", *subname2 = "sub3";
    char *userBuf = (char *)malloc(sizeof(char) * bufSize);
    if (userBuf == NULL) {    // 符合: 对返回值进行合法性检查
        AW_FUN_Log(LOG_INFO, "malloc failed");
    }
    int32_t countcallback = 0;
    g_fetchNum = 10;
    int writeCount = 5;
    ComplexTableStructT *objIn = (ComplexTableStructT *)malloc(sizeof(ComplexTableStructT) * writeCount);
    char str[10] = {0};
    bool isBatch = true, isStruct = false;
    GtlabelCfgT vertexCfg = { GMC_OPERATION_INSERT, g_inp1, writeCount, isBatch, isStruct };
    SetComplexTableValue(objIn, str, writeCount);
    DlrReplayDataT replayData = { subname,    userBuf,   bufSize,      g_conn_replay_nsp2,
                                g_stmt_replay_nsp2, g_connSub0, countcallback };
    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = createDlrSubscription(g_stmt, g_connSub0, (char *)"datalogfile/notifytable.gmjson", &replayData, 100,
                                subname, sn_callback_sleep_NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = createDlrSubscription(g_stmt, g_connSub1, (char *)"datalogfile/notifytable.gmjson", &replayData, 100,
                                subname1, sn_callback_sleep_NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = createDlrSubscription(g_stmt, g_connSub2, (char *)"datalogfile/notifytable.gmjson", &replayData, 100,
                                subname2, sn_callback_sleep_NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    LoadDataLogCfgT cfg1 = {g_namespace1, 4};
    LoadDataLogCfgT cfg2 = {g_namespace2, 5};
    LoadDataLogCfgT cfg3 = {g_namespace3, 6};
    int32_t threadNum = 3;
    pthread_t write_threads[threadNum];
    ret = pthread_create(&write_threads[0], NULL, MultiThreadUnLoadDataLog, (LoadDataLogCfgT *)&cfg1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&write_threads[1], NULL, MultiThreadUnLoadDataLog, (LoadDataLogCfgT *)&cfg2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&write_threads[2], NULL, MultiThreadUnLoadDataLog, (LoadDataLogCfgT *)&cfg3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_join(write_threads[0], NULL);
    pthread_join(write_threads[1], NULL);
    pthread_join(write_threads[2], NULL);

    ret = GmcUseNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = cancelDlrSubscription(g_stmt, subname);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestInterfaceUnLoadDatalog(g_stmt, dname, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, g_namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = cancelDlrSubscription(g_stmt, subname1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestInterfaceUnLoadDatalog(g_stmt, dname, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_namespace3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = cancelDlrSubscription(g_stmt, subname2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestInterfaceUnLoadDatalog(g_stmt, dname, g_namespace3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, g_namespace3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_replay_nsp2, g_stmt_replay_nsp2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < writeCount; i++) {
        free(objIn[i].d10);
    }
    free(objIn);
    free(userBuf);
    AW_FUN_Log(LOG_STEP, "test end.");
}

