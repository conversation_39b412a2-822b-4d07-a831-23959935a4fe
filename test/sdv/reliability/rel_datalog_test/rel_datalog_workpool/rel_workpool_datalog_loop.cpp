#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
// 30分钟的秒数
#define TIMelimit 30 * 60
#define MAX_NAME_LENGTH 512
int main() {
    time_t start_time, current_time;
    double elapsed_time;
    bool ischecked = false;
    // 获取开始时间戳
    start_time = time(NULL);
    int32_t number = 0;
    while (1) {
        // 执行目标函数
        system("./rel_workpool_datalog --gtest_filter=*001 > rel_workpool_datalog_result.txt");
        char grepcmd[MAX_NAME_LENGTH] = {0};
        (void)snprintf(grepcmd, MAX_NAME_LENGTH, "grep 1016004 rel_workpool_datalog_result.txt");
        printf("this is the %ds time\n", number);
        int localret = GtExecSystemCmd(grepcmd);
        if (localret == GMERR_OK) {
            printf("the workpool is limited and detect the hungs in testcases\n");
            system("cat rel_workpool_datalog_result.txt");
            ischecked = true;
            break;
        }
        number++;
        sleep(30);
        current_time = time(NULL);
        elapsed_time = difftime(current_time, start_time);

        if (elapsed_time >= TIMelimit) {
            printf("已运行30分钟，退出程序\n");
            break;
        }
    }
    if (!ischecked){
        printf("testcases result is unexpected\n");
    }

    return 0;
}
