/*  版权所有 (c) 华为技术有限公司 2025-2025 */
#include "gm_udf.h"
#include "stdio.h"
#include "unistd.h"

#pragma pack(1)

typedef struct Func1 {
    int32_t dtlReservedCount;
    int8_t a12;
    uint8_t b12;
    uint8_t c12[1];
    int8_t ao12;
    uint8_t bo12;
    uint8_t co12[1];
} Func1;

#pragma pack(0)

int32_t dtl_ext_func_funcinp1(void *tuple, GmUdfCtxT *ctx)
{
    Func1 *func = (Func1 *)tuple;
    func->ao12 = func->a12;
    func->bo12 = func->b12;
    func->co12[0] = func->c12[0];
    sleep(2);
    return GMERR_OK;
}

