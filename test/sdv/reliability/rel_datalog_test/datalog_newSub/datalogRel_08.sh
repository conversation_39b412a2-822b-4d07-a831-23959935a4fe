CFE="/root/CFE_Tool/cfe/cfe"
#系统cpu过载
#将CPU1使用率达到90% 等待20s后恢复
function rCPU_Overloadl_value90() 
{
    $CFE "inject rCPU_Overloadl (cpuid, usage) values(0, 90)"
    echo "rCPU_Overloadl"
    sleep 20
    $CFE "clean rCPU_Overloadl"
    echo "clean rCPU_Overloadl"
    return 0
}
#将CPU1使用率达到100% 等待20s后恢复
function rCPU_Overloadl_value() 
{
    $CFE "inject rCPU_Overloadl (cpuid, usage) values(0, 100)"
    echo "rCPU_Overloadl"
    sleep 20
    $CFE "clean rCPU_Overloadl"
    echo "clean rCPU_Overloadl"
    return 0
}
#Oracle共享内存池耗尽 
function rORA_shared_pool() 
{
    $CFE "inject rORA_shared_pool"
    echo "rORA_shared_pool"
    sleep 20
    $CFE "clean rORA_shared_pool"
    echo "clean rORA_shared_pool"
    return 0
}
#rSysClockJump_60 
function rSysClockJump_60() 
{
    $CFE "inject rSysClockJump (DIRECTION,OFFSET) values (-,60)"
    echo "inject rSysClockJump (DIRECTION,OFFSET) values (-,60)"
    sleep 20
    $CFE "clean rSysClockJump where DIRECTION=- and OFFSET=60"
    echo "clean rSysClockJump where DIRECTION=- and OFFSET=60"
    return 0
}
#rSysClockJump+60 
function rSysClockJump_plus60() 
{
    $CFE "inject rSysClockJump (DIRECTION,OFFSET) values ($direction,$offset)"
    echo "inject rSysClockJump (DIRECTION,OFFSET) values ($direction,$offset)"
    sleep 60
    $CFE "clean rSysClockJump where DIRECTION=$direction and OFFSET=$offset"
    echo "clean rSysClockJump where DIRECTION=$direction and OFFSET=$offset"
    return 0
}
# 自定义跳转时间和周期
direction=$2
offset=$3
sleep_time=$4
function SysClockJump() 
{
    
    $CFE "inject rSysClockJump (DIRECTION,OFFSET) values ($direction,$offset)"
    echo "inject rSysClockJump (DIRECTION,OFFSET) values ($direction,$offset)"
    sleep $sleep_time
    $CFE "clean rSysClockJump where DIRECTION=$direction and OFFSET=$offset"
    echo "clean rSysClockJump where DIRECTION=$direction and OFFSET=$offset"
    return 0
}
#rProc_k
function rProc_k() 
{
    echo "AAAAAAAAAAAAAAAAAAAAAAAA"
    sleep 20
    echo "BBBBBBBBBBBBBBBBBBBB"
    kill -9 `pidof gmserver`
    return 0
}
#rProc_k
function rProc_k_clent() 
{
    echo "AAAAAAAAAAAAAAAAAAAAAAAA"
    sleep 20
    echo "BBBBBBBBBBBBBBBBBBBB"
    kill -9 `pidof $(pwd)/rel_ddl_client`
    return 0
}
function rProc_kill19_18_server() 
{
    sleep 10
    echo "AAAAAAAAAAAAAAAAAAAAAAAA"
    kill -19 `pidof gmserver`
    sleep 65
    kill -18 `pidof gmserver`
    echo "bbbbbbbbbbbbbbbb"
    return 0
}
function rProc_kill19_18_clent() 
{
    echo "AAAAAAAAAAAAAAAAAAAAAAAA"
    kill -19 `pidof patch_clientStop`
    sleep 65
    kill -18 `pidof patch_clientStop`
    echo "bbbbbbbbbbbbbbbb"
    return 0
}
function rProc_SIGILL() 
{
    sleep 10
    echo "AAAAAAAAAAAAAAAAAAAAAAAA"
    kill -4 `pidof gmserver`
    return 0
}
function rProc_SIGINT() 
{
    sleep 10
    echo "AAAAAAAAAAAAAAAAAAAAAAAA"
    kill -2 `pidof gmserver`
    return 0
}
function rProc_SIGPIPE() 
{
    sleep 10
    echo "AAAAAAAAAAAAAAAAAAAAAAAA"
    kill -13 `pidof gmserver`
    sleep 10
    echo "BBBBBBBBBBBBBBBBBBBBBB"
    return 0
}
#执行rCPU_Overloadl_value90
if [ "$1" = rCPU_Overloadl_value90 ]
then
rCPU_Overloadl_value90
fi
#执行rCPU_Overloadl_value100
if [ "$1" = rCPU_Overloadl_value ]
then
rCPU_Overloadl_value
fi
#执行rORA_shared_pool
if [ "$1" = rORA_shared_pool ]
then
rORA_shared_pool
fi
#执行rSysClockJump_60
if [ "$1" = rSysClockJump_60 ]
then
rSysClockJump_60
fi
#执行rSysClockJump+60
if [ "$1" = rSysClockJump_plus60 ]
then
rSysClockJump_plus60
fi

#执行SysClockJump
if [ "$1" = SysClockJump ]
then
SysClockJump
fi
#执行rSysClockJump+60
if [ "$1" = rProc_k ]
then
rProc_k
fi
#执行rSysClockJump+60
if [ "$1" = rProc_k_clent ]
then
rProc_k_clent
fi
#执行rProc_kill19_18
if [ "$1" = rProc_kill19_18_server ]
then
rProc_kill19_18_server
fi
#执行rProc_kill19_18
if [ "$1" = rProc_kill19_18_clent ]
then
rProc_kill19_18_clent
fi
#执行rProc_SIGILL
if [ "$1" = rProc_SIGILL ]
then
rProc_SIGILL
fi
#执行rProc_SIGINT
if [ "$1" = rProc_SIGINT ]
then
rProc_SIGINT
fi
#执行rProc_SIGPIPE
if [ "$1" = rProc_SIGPIPE ]
then
rProc_SIGPIPE
fi
