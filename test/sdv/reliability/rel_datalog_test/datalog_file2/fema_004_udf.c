/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: tbm_udf
 * Author: yang<PERSON>wen ywx1060383
 * Create: 2023-11-09
 */

#include "gm_udf.h"
#include "stdio.h"

#pragma pack(1)
typedef struct Tbm1 {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int32_t a;
    int32_t b;
    int32_t c;
} Tbm1;
#pragma pack(0)

const char *g_logName = "/root/TbmRunLog.txt";

int32_t dtl_ext_func_init(GmUdfCtxT *ctx)
{
    FILE* fp = fopen(g_logName, "w");
    if (fp == NULL) {
        return 0;
    }
    (void)fprintf(fp, "[%s] dtl_ext_func_init.\n", __FILE__);
    (void)fclose(fp);
    return GMERR_OK;
}

int32_t dtl_ext_func_uninit(GmUdfCtxT *ctx)
{
    FILE* fp = fopen(g_logName, "a+");
    if (fp == NULL) {
        return 0;
    }
    (void)fprintf(fp, "[%s] dtl_ext_func_uninit.\n", __FILE__);
    (void)fclose(fp);
    return GMERR_OK;
}

int32_t dtl_tbm_tbl_tbm(uint32_t op, void *tuple)
{
    if (op == 2) {
        return GMERR_DATA_EXCEPTION;
    }

    FILE *fp = fopen(g_logName, "a+");
    if (fp == NULL) {
        return 0;
    }
    (void)fprintf(fp, "op = %d, a = %d.\n", op, ((Tbm1 *)tuple)->a);
    (void)fprintf(fp, "op = %d, b = %d.\n", op, ((Tbm1 *)tuple)->b);
    (void)fprintf(fp, "op = %d, c = %d.\n", op, ((Tbm1 *)tuple)->c);
    (void)fprintf(fp, "op = %d, dtlReservedCount = %d.\n", op, ((Tbm1 *)tuple)->dtlReservedCount);
    (void)fclose(fp);
    return GMERR_OK;
}
