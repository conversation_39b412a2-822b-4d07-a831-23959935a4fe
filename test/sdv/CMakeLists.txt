cmake_minimum_required(VERSION 3.14.1)

project(GMDBV5_TEST)

set(TEST_DIR ${CMAKE_CURRENT_SOURCE_DIR})
set(GMDB_ROOT ${CMAKE_CURRENT_SOURCE_DIR}/../..)
include(${GMDB_ROOT}/cmake/function.cmake)

find_program(CCACHE_FOUND ccache)
if(CCACHE_FOUND)
    set_property(GLOBAL PROPERTY RULE_LAUNCH_COMPILE "${CCACHE_FOUND}")
endif(CCACHE_FOUND)

if(ENABLE_VERBOSE)
    set(CMAKE_VERBOSE_MAKEFILE ON)
endif()

if(ENABLE_DEBUG_MSG)
    add_definitions(-D ENABLE_DEBUG_MSG)
endif()

if(PERF_ON)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -O2 -fPIC")
else()
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -g -O0 -fPIC")
endif()

if(DIRECT_WRITE)
    add_definitions(-DDIRECT_WRITE)
endif()

if(FEATURE_CLT_SERVER_SAME_PROCESS)
    link_directories(${CMAKE_CURRENT_SOURCE_DIR}/exec/third_party/lib)
    add_definitions(-DFEATURE_CLT_SERVER_SAME_PROCESS)
endif()

if(FEATURE_NERGC)
    add_definitions(-DFEATURE_NERGC)
endif()

if(ART_CONTAINER)
    add_definitions(-DART_CONTAINER)
endif()

if(BSC_LLVM)
    add_compile_options(-Wno-vla)
    add_compile_options(-Werror=return-type)
endif()

if(DOMAIN_DATACOM)
    add_definitions(-DDOMAIN_DATACOM)
endif()
if(DOMAIN_CONSUMER)
    add_definitions(-DDOMAIN_CONSUMER)
endif()
if(PRODUCT_XIAOYI)
    add_definitions(-DPRODUCT_XIAOYI)
endif()
if(PRODUCT_CARD)
    add_definitions(-DPRODUCT_CARD)
endif()
if(PRODUCT_IDS)
    add_definitions(-DPRODUCT_IDS)
    if(NOT EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/jansson)
        EXECUTE_PROCESS(
                COMMAND cp -r ${GMDB_ROOT}/open_source/jansson ${CMAKE_CURRENT_SOURCE_DIR}
        )
    endif()
    add_subdirectory(jansson)
    include_directories(${CMAKE_CURRENT_SOURCE_DIR}/build/jansson/include)
    link_directories(${CMAKE_CURRENT_SOURCE_DIR}/build/jansson/lib)
endif()
if(PRODUCT_USG_TS)
    add_definitions(-DPRODUCT_USG_TS)
endif()
if(PRODUCT_ADAPTV1)
    add_definitions(-DPRODUCT_ADAPTV1)
endif()

if(PRODUCT_RSMEM)
    add_definitions(-DPRODUCT_RSMEM)
endif()

if(PRODUCT_GuangQi)
    add_definitions(-DPRODUCT_GuangQi)
    add_definitions(-DEXPERIMENTAL_GUANGQI)
endif()

if(PRODUCT_NE)
    add_definitions(-DPRODUCT_NE)
endif()

if(FEATURE_SIMPLEREL)
    add_definitions(-DFEATURE_SIMPLEREL)
endif()
if(FEATURE_FASTPATH)
    add_definitions(-DFEATURE_FASTPATH)
endif()
if(FEATURE_YANG)
    add_definitions(-DFEATURE_YANG)
endif()
if(FEATURE_YANG_VALIDATION)
    add_definitions(-DFEATURE_YANG_VALIDATION)
endif()
if(FEATURE_DATALOG)
    add_definitions(-DFEATURE_DATALOG)
endif()
if(FEATURE_SQL)
    add_definitions(-DFEATURE_SQL)
    if(RD_SQL_API_GME)
        add_definitions(-DRD_SQL_API_GME)
    elseif(RD_SQL_API_GRD)
        add_definitions(-DRD_SQL_API_GRD)
    elseif(RD_SQL_API_SQLITE)
        add_definitions(-DRD_SQL_API_SQLITE)
    endif()
endif()
if(FEATURE_GQL)
    add_definitions(-DFEATURE_GQL)
endif()
if(FEATURE_PERSISTENCE)
    link_directories(${CMAKE_CURRENT_SOURCE_DIR}/exec/third_party/lib)
    add_definitions(-DFEATURE_PERSISTENCE)
endif()
if(FEATURE_TS)
    add_definitions(-DFEATURE_TS)
endif()
if(FEATURE_MULTI_TS)
    add_definitions(-DFEATURE_MULTI_TS)
endif()
if(FEATURE_VECTOR)
    add_definitions(-DFEATURE_VECTOR)
endif()
if(FEATURE_HAC)
    add_definitions(-DFEATURE_HAC)
endif()
if(FEATURE_STREAM)
    add_definitions(-DFEATURE_STREAM)
endif()
if(FEATURE_STREAM_EMB)
    add_definitions(-DFEATURE_STREAM_EMB)
endif()
if(FEATURE_REPLICATION)
    add_definitions(-DFEATURE_REPLICATION)
endif()
if(FEATURE_RSMEM)
    add_definitions(-DFEATURE_RSMEM)
endif()
add_definitions(-DFEATURE_DLR)
if(RUN_SIMULATE)
    add_definitions(-DRUN_SIMULATE)
endif()

if(libfuzzer)
    add_definitions(-Dlibfuzzer)
    add_definitions(-DARM64)
endif()

if(FTTx32)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -m32 -DX86")
endif()

add_compile_options(-fstack-protector-all)

set(GMDB_PATH $ENV{GMDB_PATH})
# for development
include_directories(${GMDB_PATH}/include)
include_directories(${GMDB_PATH}/include/adapter4v1)
include_directories(${GMDB_PATH}/grd)
include_directories(${GMDB_PATH}/third_party/include)
# for development ce
#include_directories(${GMDB_PATH}/../armv7l/include)
#include_directories(${GMDB_PATH}/../armv7l/third_party/include)
# for test
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/common/include)

include_directories(${CMAKE_CURRENT_SOURCE_DIR}/hpe_ci)
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/deps/include)
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/hpe_ci/deps/include/aarch64-linux-gnu/gmdb)
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/hpe_ci/deps/include/aarch64-linux-gnu/jansson)

# for struct test
set(GMDB_ADAPTER_INTERNAL_PATH ${GMDB_ROOT}/src/adapter/)
include_sub_directories_recursively(${GMDB_ADAPTER_INTERNAL_PATH})
set(GMDB_COMMON_INTERNAL_PATH ${GMDB_ROOT}/src/common/)
include_sub_directories_recursively(${GMDB_COMMON_INTERNAL_PATH})
set(GMDB_CLIENT_PATH ${GMDB_ROOT}/src/client/)
include_sub_directories_recursively(${GMDB_CLIENT_PATH})
set(GMDB_DATAMODEL_INTERNAL_PATH ${GMDB_ROOT}/src/datamodel/)
include_sub_directories_recursively(${GMDB_DATAMODEL_INTERNAL_PATH})
set(GMDB_EXECUTOR_INTERNAL_PATH ${GMDB_ROOT}/src/executor/)
include_sub_directories_recursively(${GMDB_EXECUTOR_INTERNAL_PATH})
set(GMDB_STORAGE_INTERNAL_PATH ${GMDB_ROOT}/src/storage/)
include_sub_directories_recursively(${GMDB_STORAGE_INTERNAL_PATH})

if(DOMAIN_DATACOM)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fPIE -rdynamic")
    if(NOT PRODUCT_ADAPTV1 AND NOT PRODUCT_USG_TS AND NOT FEATURE_STREAM AND NOT FEATURE_STREAM_EMB)
        include_directories(${GMDB_ROOT}/test/tools/testutil)
        link_libraries(gmtestutil)
    endif()
    add_link_options("LINKER:-rpath-link,${GMDB_PATH}/third_party/lib")
    if(FEATURE_REPLICATION)
        include_directories(${GMDB_ROOT}/test/tools/test_ha)
        link_libraries(orm_ha)
    endif()
endif()

link_directories(${GMDB_PATH}/lib)
link_directories(${GMDB_PATH}/third_party/lib)
#link_directories(${GMDB_PATH}/../armv7l/third_party/lib)
link_directories(${CMAKE_CURRENT_SOURCE_DIR}/hpe_ci/lib)
link_directories(${CMAKE_CURRENT_SOURCE_DIR}/hpe_ci/deps/lib/aarch64-linux-gnu/gmdb-libs-client-ext)
link_directories(${CMAKE_CURRENT_SOURCE_DIR}/hpe_ci/deps/lib/aarch64-linux-gnu/gmdb-libs)
link_directories(${CMAKE_CURRENT_SOURCE_DIR}/hpe_ci/deps/lib/aarch64-linux-gnu/jansson)

set(CMAKE_INSTALL_PREFIX ${CMAKE_CURRENT_SOURCE_DIR})

add_definitions(-DNRELEASE)
add_definitions(-DRELIA_RWLATCH)
if(NDEBUG)
    add_definitions(-DNDEBUG)
endif()
if(ENV_EULER)
    add_definitions(-DENV_EULER)
elseif(ENV_RTOSV2)
    add_definitions(-DENV_RTOSV2)
    set(libsuffix rtosv2)
elseif(ENV_RTOSV2X)
    if(NOT RUN_SIMULATE)
        set(CMAKE_CXX_FLAGS "-s -fPIC -DGTEST_USE_OWN_TR1_TUPLE=1")
    endif()
    add_definitions(-DENV_RTOSV2X)
    set(libsuffix rtosv2x)
elseif(ENV_SUSE)
    add_definitions(-DENV_SUSE)
elseif(ENV_UBUNTU)
    add_definitions(-DENV_UBUNTU)
endif()

if(RUN_INDEPENDENT)
    add_definitions(-DRUN_INDEPENDENT)
elseif(RUN_DATACOM_DAP)
    add_definitions(-DRUN_DATACOM_DAP)
    if(ENV_RTOSV2)
        add_definitions(-DRTOSV2)
    elseif(ENV_RTOSV2X)
        add_definitions(-DRTOSV2X)
    endif()
elseif(RUN_DATACOM_HPE)
    add_definitions(-DRUN_DATACOM_HPE)
    add_definitions(-DHPE)
elseif(RUN_CONSUMER_EMBED)
    add_definitions(-DRUN_CONSUMER_EMBED)
endif()

if(CPU_ARC_ARM)
    add_definitions(-DCPU_ARC_ARM)
elseif(CPU_ARC_X86)
    add_definitions(-DCPU_ARC_X86)
    add_compile_options(-mfpmath=sse -msse2)
endif()

if(CPU_BIT_32)
    add_definitions(-DCPU_BIT_32)
elseif(CPU_BIT_64)
    add_definitions(-DCPU_BIT_64)
endif()

if(RUN_DATACOM_HPE)
    include_directories(${CMAKE_CURRENT_SOURCE_DIR}/exec/gtest_hpe/include)
    link_libraries(jansson securec gmadapter gmdb dl rt pthread)
    link_directories(${CMAKE_CURRENT_SOURCE_DIR}/exec/gtest_hpe/lib)
    link_libraries(gtest_main_${libsuffix})
    link_directories(${HPE_PATH}/lib)
    set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -O2 -fPIC -std=c99 -rdynamic")
    set(DEPENDENT_LIBS c -nostdlib)
    set(CMAKE_CXX_LINK_EXECUTABLE "<CMAKE_C_COMPILER> -o <TARGET> ${CMAKE_CXX_FLAGS} <LINK_FLAGS> <OBJECTS> <LINK_LIBRARIES> -Wl,--start-group $ENV{RTOS_SDK_SYSROOT}/lib64/libstdc++.a -Wl,--end-group")
else()
    link_directories(${CMAKE_CURRENT_SOURCE_DIR}/exec/lib)
    if(TEST_EXEC_GTEST)
        include_directories(${CMAKE_CURRENT_SOURCE_DIR}/exec/gtest/include)
        link_libraries(gtest_main)
    else()
        include_directories(${CMAKE_CURRENT_SOURCE_DIR}/exec/googletest/googletest/include)
        link_libraries(gtest_main gtest)
    endif()
    if(DOMAIN_DATACOM)
        if(PRODUCT_IDS)
            link_libraries(gmtools jansson cjson)
        elseif(PRODUCT_ADAPTV1)
            link_libraries(gmdbEmbed jansson)
        elseif(PRODUCT_USG_TS AND NOT FEATURE_STREAM)
            link_libraries(gmtools_ts jansson)
        elseif(FEATURE_STREAM_EMB)
            link_libraries(jansson)
        else()
            link_libraries(gmtools jansson)
        endif()
    else()
        if(PRODUCT_XIAOYI OR FEATURE_SQL)
            link_libraries(cjson)
        else()
            link_libraries(jansson)
        endif()
        if(FEATURE_SQL)
            include_directories(${CMAKE_CURRENT_SOURCE_DIR}/testcases_rd/002_sql/common)
            if(RD_SQL_API_GRD)
                include_directories(${GMDB_PATH}/grd)
                link_directories(${GMDB_ROOT}/platform/GaussDB_RD_Naturalbase/platform/gaussdb_rd_kernel/build/lib)
                link_libraries(grd_store gmserver)
            elseif(RD_SQL_API_SQLITE)
                link_libraries(sqlite3)
            endif()
        endif()
    endif()
    if(PRODUCT_ADAPTV1)
        link_libraries(securec dl rt pthread)
    elseif(PRODUCT_NE)
        link_libraries(securec gmdbEmbed gmadapter dl rt pthread)
    else()
        link_libraries(securec gmadapter dl rt pthread)
    endif()
    if(SINGLE_SO)
        if(NOT PRODUCT_ADAPTV1)
            link_libraries(gmdb)
        endif()
    else()
        if(FEATURE_PERSISTENCE)
            link_libraries(gmcommon gmmemdata gmtrm gmfastpath gmserverbase)
            link_libraries(z)
        else()
            link_libraries(gmcommon gmmemdata gmtrm gmfastpath gmfusion gmyang gmserverbase)
            if(FEATURE_DATALOG)
                link_libraries(gmdatalog)
            endif()
        endif()
    endif()
    if(FEATURE_CLT_SERVER_SAME_PROCESS)
        link_libraries(gmadmin_embed gmerror_embed gmexport_embed gmimport_embed gmlog_embed gmrule_embed gmips_embed gmids_embed gmasst_embed gmddl_embed gmtools gmserver_embed)
    endif()
    if(FEATURE_NERGC)
        link_libraries(gmadmin_embed gmerror_embed gmexport_embed gmimport_embed gmlog_embed gmrule_embed gmips_embed gmids_embed gmasst_embed gmddl_embed gmtools gmserver_embed)
    endif()
    if(PRODUCT_CARD OR PRODUCT_XIAOYI)
        link_libraries(grd_store)
    endif()
endif()

if(COVERAGE)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fprofile-arcs -ftest-coverage")
endif()

if(ENV_RTOSV2 AND RUN_DATACOM_DAP AND (ASAN OR ASAN_STATIC OR UBSAN OR TSAN))
    link_directories(${GCC_HOME}/lib64)
endif()
if(ASAN)
    add_definitions(-D ASAN)
    link_libraries(asan)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fsanitize=address -fsanitize=leak -fsanitize-recover=address,all -fno-omit-frame-pointer -g -lm")
endif()
if(ASAN_STATIC)
    add_definitions(-D ASAN -D TEST_STATIC_ASAN)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fsanitize=address -fsanitize=leak -fsanitize-recover=address,all -fno-omit-frame-pointer -static-libasan -g -lm")
endif()
if(UBSAN)
    link_libraries(ubsan)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fsanitize=undefined -fno-sanitize=alignment -fsanitize=float-divide-by-zero -fsanitize=float-cast-overflow -fsanitize-recover=address,all -fno-omit-frame-pointer -g -lm")
endif()
if(TSAN)
    link_libraries(tsan)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fsanitize=thread -fno-omit-frame-pointer -g -lm")
endif()

set(CMAKE_SKIP_RPATH TRUE)

if(NOT FEATURE_VECTOR)
    include_directories(${CMAKE_CURRENT_SOURCE_DIR}/common/include)
    include_directories(${CMAKE_CURRENT_SOURCE_DIR}/common/include/component)
    include_directories(${CMAKE_CURRENT_SOURCE_DIR}/common/include/scene)
    if(FEATURE_STREAM)
        include_directories(${CMAKE_CURRENT_SOURCE_DIR}/common/include/component/stream/base)
        include_directories(${CMAKE_CURRENT_SOURCE_DIR}/common/include/component/stream/public)
        include_directories(${CMAKE_CURRENT_SOURCE_DIR}/common/include/component/stream/feature)
        include_directories(${CMAKE_CURRENT_SOURCE_DIR}/common/include/component/stream/long)
    endif()
    if(NOT IGNORE_COMMON)
        add_subdirectory(common)
    endif()
    link_directories(${CMAKE_CURRENT_SOURCE_DIR}/common/lib)
    link_libraries(trdaw)
else()
    include_directories(${CMAKE_CURRENT_SOURCE_DIR}/GRD_testcases/VectorDB/common/include)
    link_directories(${CMAKE_CURRENT_SOURCE_DIR}/GRD_testcases/VectorDB/common)
    link_directories(${CMAKE_CURRENT_SOURCE_DIR}/../../platform/GaussDB_RD_Naturalbase/platform/gaussdb_rd_kernel/build/lib)
    link_libraries(grd_vec gmserver)
endif()

if(FEATURE_VECTORDB)
    include_directories(${CMAKE_CURRENT_SOURCE_DIR}/common/include/component)
    link_libraries(trdaw)
    link_libraries(gmdb pthread)
endif()

if(INJECT)
    add_definitions(-DINJECT)
endif()
add_subdirectory(utils)

set(COMPILE_EXCLUDE_LIST "")
if(COMPILE_EXCLUDE)
    set(tmplist "")
    string(REPLACE "," ";" tmplist ${COMPILE_EXCLUDE})
    foreach(item ${tmplist})
        list(APPEND COMPILE_EXCLUDE_LIST "${CMAKE_CURRENT_SOURCE_DIR}/${item}")
    endforeach(item)
endif()
set(COMPILE_EXCLUDE_LIST ${COMPILE_EXCLUDE_LIST} CACHE INTERNAL "")
message(STATUS "COMPILE_EXCLUDE_LIST=${COMPILE_EXCLUDE_LIST}")

if(DOMAIN_DATACOM)
    set(compile_list testcases reliability ComponentbasedTest longStability resilience)
else()
    set(compile_list testcases_rd longStabilityRd)
endif()

set(COMPILE_PATH_LIST "")
if(COMPILE_PATH)
    string(REPLACE "," ";" COMPILE_PATH_LIST ${COMPILE_PATH})
    foreach(item ${COMPILE_PATH_LIST})
        string(SUBSTRING ${item} 0 4 tmp_prefix)
        if ("${tmp_prefix}" STREQUAL "perf")
            list(APPEND compile_list perf)
            break()
        endif()
    endforeach(item)
else()
    set(COMPILE_PATH_LIST ${compile_list})
endif()
set(list_sdv ${COMPILE_PATH_LIST})

message(STATUS "COMPILE_PATH_LIST=${COMPILE_PATH_LIST}")
message(STATUS "list_sdv=${list_sdv}")

include(${CMAKE_CURRENT_SOURCE_DIR}/cmake/function.cmake)

verify_and_add_directory(${compile_list})
