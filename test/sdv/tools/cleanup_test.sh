#!/bin/bash

dirPath=$(cd "$(dirname $0)"; pwd)
if [ "x.y" = "x${dirPath}y" ]; then
    dirPath=`pwd`
fi

function usage()
{
    echo "Usage: $0 [-f asan|fuzz]"
}

function parse_opts()
{
    ARGS=""
    while [ $# -gt 0 ]
    do
        unset OPTIND
        unset OPTARG
        while getopts "f:" options
        do
        case $options in
            f)
                feature="$OPTARG"
                if [[ X"${feature}" == X"asan" ]]; then
                    asan=1
                elif [[ X"${feature}" == X"fuzz" ]]; then
                    fuzz=1
                else
                    echo "feature(${feature}) is invalid, it must been in [asan, fuzz]"
                    exit 1
                fi
                opt_arr="${opt_arr} -f ${feature}"
                ;;
        esac
    done
    shift $((OPTIND-1))
    ARGS="${ARGS} $1"
    shift
    done
}
parse_opts $@

EnvType=`grep -w EnvType ${dirPath}/../cfg/sysconfig.ini | awk '{print $3}'`
cfgFile=`grep -w sysGmdbCfg ${dirPath}/../cfg/sysconfig.ini | awk '{print $3}'`
cfgFilePath=`echo ${cfgFile} | awk -F"gmserver.ini" '{print $1}'`
TestMode=`grep -w TestMode ${dirPath}/../cfg/sysconfig.ini | awk '{print $3}'`

DBSERVER_NAME="gmserver"
if [ ${EnvType} -eq 2 ];then
    alias PS="ps -wwww"
    alias AWK="awk '{print \$1}'"
    DBSERVER_NAME="/usr/bin/vm.elf"
elif [ ${EnvType} -eq 1 ];then
    alias PS="ps -ef"
    alias AWK="awk '{print \$2}'"
    DBSERVER_NAME="hpk"
elif [ ${EnvType} -eq 0 ];then
    alias PS="ps -ef"
    alias AWK="awk '{print \$2}'"
    DBSERVER_NAME="gmserver"
fi

function get_pid_of()
{
    key="$1"
    local pid=$(PS | grep $key | grep -v grep | grep -v autotest_env | AWK)
    echo $pid;
}

function kill_all_pid()
{
    local pid="$@"
    for p in $pid
    do 
        kill -9 $p || true
    done
}

server_pid=$(get_pid_of ${DBSERVER_NAME})
filter_pid=$(get_pid_of gtest_filter)
gmimport_pid=$(get_pid_of gmimport)
gmexport_pid=$(get_pid_of gmexport)
gmsysview_pid=$(get_pid_of gmsysview)
gmrule_pid=$(get_pid_of gmrule)

kill_all_pid $filter_pid

kill_all_pid $gmimport_pid

kill_all_pid $gmexport_pid

kill_all_pid $gmsysview_pid

kill_all_pid $gmrule_pid

#计算时间间隔，超过590秒则杀掉服务端
function kill_server_if_exceeds_time()
{
    his_file=`find ${dirPath}/.. -name start_history.sh`
    begin_time=`grep 'echo CI@' ${his_file} | tail -n 1 | awk -F[\]\[] '{print $2}'`
    killtime=590
    if [[ X"${asan}" == X"1" ]];then
        # asan超时时间设置为60分钟
        killtime=3600
    fi
    if [[ X"${begin_time}" != X ]] && [[ X"${server_pid}" != X ]];then
        end_time=`date "+%F %T"`
        exec_time=`echo $(($(date +%s -d "${end_time}") - $(date +%s -d "${begin_time}")))`
        if [ ${exec_time} -gt ${killtime} ];then
            echo "Checked time elapsed 10 mins. Test may be stuck. Now we will KILL DB server..."
            kill -9 ${server_pid} || true
        fi
    fi
}

if [[ X"${TestMode}" != X"0x01" ]] && [[ ${EnvType} -ne 2 ]];then;then
    kill_server_if_exceeds_time
fi

rm -rf  ${cfgFilePath}/gmserver.ini 
mkdir -p ${cfgFilePath}/ 
cp ${cfgFilePath}/gmserver.ini.ci ${cfgFilePath}/gmserver.ini 
