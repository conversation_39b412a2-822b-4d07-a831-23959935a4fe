#!/bin/bash

dirPath=$(cd "$(dirname $0)"; pwd)
export GMDB_HOME=${dirPath}/../../..
arch="aarch64"

function usage()
{
    echo "Usage: $0 -R <euler/rtosv2/rtosv2x/rtos/orm/suse/ubuntu> [ -m 0/1/2/3 ] -n <configFile> [-a arm32|aarch64|x86|x86_64|arm32a15le] [-f asan|fuzz] [-s \"configName=configValue\"] [-t mini]"
    exit 1
}

function check_config()
{
    if [ $# -lt 3 ];then
        exit 1
    fi
    local conf="$1"
    local keyName="$2"
    local keyVal="$3"
    local line=$(grep ^$keyName $conf)
    local linecnt=$(grep ^$keyName $conf| wc -l)
    if [ $linecnt -ne 1 ];then
        echo "error: replace config fail! Find keyName error."
        exit 1
    fi
    local value=$(echo $line | cut -d'=' -f2- | tr -d ' ')
    if [[ X"${value}" != X"${keyVal}" ]];then
        echo "error: replace config fail! Check keyValue error."
        exit 1
    fi
}

function replace_config()
{
    if [ $# -lt 3 ];then
        exit 1
    fi
    local conf="$1"
    local key="$2"
    local value="$3"
    sed -i "/^$key.*/d" ${conf} && sed -i "\$a $key = $value" ${conf}
    check_config "$conf" "$key" "$value"
}

envVal="nil"
envType="nil"
runMode="nil"
configFile="nil"
conf_flag="no"
mini_flag="nil"

configCnt=0
DB_Config_Name=""
DB_Config_Value=""
function extra_config_add()
{
    if [ $# -lt 2 ];then
        exit 1
    fi
    local keyName="$1"
    local keyVal="$2"
    if [[ X"${keyVal}" == X"" ]];then
        keyVal="nil"
    fi
    echo "${keyName},${keyVal}";
    configCnt=$((configCnt+1))
    DB_Config_Name="${DB_Config_Name} ${keyName}"
    DB_Config_Value="${DB_Config_Value} ${keyVal}"
}
# 在这里设置你想修改的配置项
#extra_config_add "userPolicyMode" "0"

function extra_config_change()
{
    if [[ "${conf_flag}" == 'yes' ]]; then
        echo "Changing gmserver.ini from parameter ..."
        for i in `seq 1 ${configCnt}`;do
            unset keyName
            unset keyVal
            local keyName=$(echo $DB_Config_Name | awk -v i=$i -F' ' '{print $i}')
            local keyVal=$(echo $DB_Config_Value | awk -v i=$i -F' ' '{print $i}')
            echo "${keyName},${keyVal}";
            local strBuf=`grep -w "^${keyName}" ${tmpconfigFile} | wc -l`
            if [ ${strBuf} -eq 0 ]; then
                echo "Warning: failed to find key:${keyName} in ${tmpconfigFile}."
            fi
            if [[ X"${keyVal}" == X"nil" ]];then
                keyVal=""
            fi
            replace_config ${tmpconfigFile} "${keyName}" "${keyVal}"
        done
    fi
}

ts=0
function parse_opts()
{
    ARGS=""
    while [ $# -gt 0 ]
    do
        unset OPTIND
        unset OPTARG
        while getopts "a:f:m:R:n:s:t:" options
        do
        case $options in
            f)
                feature="$OPTARG"
                if [[ X"${feature}" == X"asan" ]]; then
                    asan=1
                elif [[ X"${feature}" == X"fuzz" ]]; then
                    fuzz=1
                else
                    echo "error: feature(${feature}) is invalid, it must been in [asan, fuzz]"
                    exit 1
                fi
                opt_arr="${opt_arr} -f ${feature}"
                ;;
            a)
                arch="$OPTARG"
                if [ "x${arch}" != x"aarch64" -a "x${arch}" != x"arm32" -a "x${arch}" != x"x86" -a "x${arch}" != x"x86_64" -a "x${arch}" != x"arm32a15le" ];then
                    echo "error: arch:${arch} is invalid, it must been in aarch64/arm32/x86/x86_64/arm32a15le."
                    usage
                    exit 1
                fi
                ;;
            m)
                runMode="$OPTARG"
                if [ "x${runMode}" != "x0" -a "x${runMode}" != "x1" -a "x${runMode}" != "x2" -a "x${runMode}" != "x3" ];then
                    echo "error: runMode:${runMode} is invalid, it must been in [0, 1, 2, 3]."
                    usage
                    exit 1
                fi
                ;;
            R)
                envVal="$OPTARG"
                if [ "x${envVal}" = "xeuler" -o "x${envVal}" = "xorm" ]; then
                    envType=0
                elif [ "x${envVal}" = "xrtosv2" ]; then
                    envType=1
                elif [ "x${envVal}" = "xrtosv2x" ]; then
                    envType=2
                elif [ "x${envVal}" = "xsuse" ]; then
                    envType=3
                elif [ "x${envVal}" = "xubuntu" ]; then
                    envType=4
                elif [ "x${envVal}" = "xrtos" ]; then
                    envType=0
                else
                    echo "error: envVal:${envVal} is invalid, it must been in [euler, rtosv2, rtosv2x, orm, suse, ubuntu, rtos]"
                    usage
                    exit 1
                fi
                ;;
            n)
                configFile="$OPTARG"
                if [ ! -f ${configFile} ]; then
                    echo "error: configFile(${configFile}) not exist!"
                    exit 1
                fi
                result=$(echo ${configFile} | grep "ts")
                if [[ "$result" != "" ]]; then
                    ts=1
                fi
                ;;
            s)
                conf_flag="yes" ;
                keyName=`echo $OPTARG | awk -F"=" '{print $1}' | tr -d ' '`
                keyVal=`echo $OPTARG | awk -F"=" '{print $2}' | tr -d ' '`
                echo "Option -s, argument \`$OPTARG', keyName:${keyName}, keyVal:${keyVal}" ;
                if [[ "x" == "x${keyName}" ]]; then
                    echo "error: config(${keyName},${keyVal}) empty!"
                    exit 1
                fi
                extra_config_add "${keyName}" "${keyVal}"
                ;;
            t)
                arg="$OPTARG"
                if [[ "${arg}" != 'mini' ]]; then
                    echo "error: -t argument is invalid, it must been in [mini]."
                    usage
                    exit 1
                fi
                mini_flag="yes"
                ;;
        esac
    done
    shift $((OPTIND-1))
    ARGS="${ARGS} $1"
    done

    # 不允许不设置configFile
    if [ "x${configFile}" = "xnil" ]; then
        echo "error: config file not defined! This script must run with config file path!"
        usage
    fi
    # 不允许不设置env
    if [ "x${envVal}" = "xnil" ]; then
        echo "error: env not defined! This script must run with env type!"
        usage
    fi
    if [ "x${runMode}" = "xnil" ]; then
        if [ "x${envVal}" = "xeuler" -o "x${envVal}" = "xorm" -o "x${envVal}" = "xsuse" -o "x${envVal}" = "xrtos" ]; then
            runMode=0
        else
            runMode=1
        fi
    fi
    if [[ ${envType} -eq 2 ]] && [[ "${mini_flag}" == 'nil' ]]; then
        mini_flag="yes"
    fi
}

parse_opts $@

if [ "${envType}" = "4" -o "${runMode}" = "3" ]; then
    exit 0
fi

tmpconfigFile=./__tmp__.ini
rm -f ${tmpconfigFile}
cp ${configFile} ${tmpconfigFile}
chmod +w ${tmpconfigFile}
echo "Info: runMode:${runMode}, envType:${envType}."

replace_config ${tmpconfigFile} "enableClusterHash" "1"  # 打开聚簇容器
replace_config ${tmpconfigFile} "clientServerFlowControl" "0;0;0;1"
replace_config ${tmpconfigFile} "flowControlSleepTime" "200,10000,1000000"
replace_config ${tmpconfigFile} "overloadThreshold" "cpu:70,80,80,85,85,90;dynamicMemory:70,80,80,85,85,90;shareMemory:70,80,80,85,85,90;subscribeQueue:70,75,85,90,90,95"

replace_config ${tmpconfigFile} "udfEnable" "1"
replace_config ${tmpconfigFile} "userPolicyMode" "0"
replace_config ${tmpconfigFile} "auditLogEnableDML" "0"
replace_config ${tmpconfigFile} "memPeakDfxThreshold" "10"

if [ "x$envVal" = "xorm" ]; then
    # orm测试需要改成300s
    replace_config ${tmpconfigFile} "workerHungThreshold" "300,301,302"
elif [ "x$envType" == "x0" -a "${arch}" = "aarch64" ]; then
    # 改成3,4,5。IOT 除了欧拉aarch64都使用默认的
    replace_config ${tmpconfigFile} "workerHungThreshold" "3,4,5"
fi

if [ ${runMode} -eq 0 ]; then
    # runMode = 0  run independent
    replace_config ${tmpconfigFile} "maxConnNum" "1536"
    replace_config ${tmpconfigFile} "scheduleMode" "2"
    replace_config ${tmpconfigFile} "memCompactEnable" "1"
    replace_config ${tmpconfigFile} "enableReleaseDevice" "1"
    if [ ${envType} -eq 1 ]; then
        # arm32: 非卸载 + RTOSV2
        replace_config ${tmpconfigFile} "maxConnNum" "1024"
    fi
    replace_config ${tmpconfigFile} "DBA" "root:gmrule;gmips;gmids;gmadmin"
    replace_config ${tmpconfigFile} "localLocatorListened" "usocket:/run/verona/unix_emserver"
    if [ ${envType} -eq 2 ]; then
        # 自行适配
        replace_config ${tmpconfigFile} "maxSeMem" "32"
        replace_config ${tmpconfigFile} "maxTotalShmSize" "64"
        replace_config ${tmpconfigFile} "maxConnNum" "64"
        replace_config ${tmpconfigFile} "deviceSize" "4"
        replace_config ${tmpconfigFile} "pageSize" "8"
        replace_config ${tmpconfigFile} "DBA" "root:gmrule;gmips;gmids;gmadmin"
        replace_config ${tmpconfigFile} "compatibleV3" "1"
    fi
    #echo "Info: set ${tmpconfigFile} done. Arguments:$@"
else
    # runMode = 1  run in hpe
    if [ ${envType} -eq 1 ]; then
        # 大云杉设备，在CC原本的配置基础上再适配修改
        replace_config ${tmpconfigFile} "maxConnNum" "1024"
        replace_config ${tmpconfigFile} "DBA" "root:gmrule;gmips;gmids;gmadmin"
        replace_config ${tmpconfigFile} "udfEnable" "0"
        replace_config ${tmpconfigFile} "memCompactEnable" "1"
        replace_config ${tmpconfigFile} "enableReleaseDevice" "1"
        #echo "Info: set ${tmpconfigFile} done. Arguments:$@"
    fi
fi

if [[ "${mini_flag}" == 'yes' ]];then
    # 小型化设备，在CC原本的配置基础上再适配修改
    replace_config ${tmpconfigFile} "enableClusterHash" "0"  # 小型化设备配置项关闭聚簇容器
    replace_config ${tmpconfigFile} "pageSize" "4"
    replace_config ${tmpconfigFile} "defaultHashType" "chained"
    replace_config ${tmpconfigFile} "liteDynMemMod" "1"
    replace_config ${tmpconfigFile} "lockTableBucketNum" "509"
    replace_config ${tmpconfigFile} "hashClusterBucketsCount" "100"
    replace_config ${tmpconfigFile} "monitorWorkerSchedulePeriod" "500"
    replace_config ${tmpconfigFile} "scheduleMode" "2"
fi

if [[ X"${asan}" == X"1" ]];then
    replace_config ${tmpconfigFile} "trxLockTimeOut" "3000"
    replace_config ${tmpconfigFile} "workerHungThreshold" "20,299,300"
fi

if [[ X"${fuzz}" == X"1" ]];then
    replace_config ${tmpconfigFile} "workerHungThreshold" "20,299,300"
fi

# 数通设备仿真环境
isSimulate=0
if  [ ${runMode} -eq 0 ]; then
    if [ -f ${GMDB_HOME}/output/euler/aarch64/lib/libgmadapter.so ]; then
        strTmp=`strings ${GMDB_HOME}/output/euler/aarch64/lib/libgmadapter.so | grep -w adpt_pipe_simulation_hpe.c`
        if [ "x${strTmp}" != "x" ]; then
            isSimulate=1
            replace_config ${tmpconfigFile} "localLocatorListened" "channel:ctl_channel"
            replace_config ${tmpconfigFile} "workerHungThreshold" "15,20,25"
            replace_config ${tmpconfigFile} "enableShareMsgPool" "0"
            if [ "x$envVal" = "xrtosv2x" ]; then
                replace_config ${tmpconfigFile} "defaultTablespaceMaxSize" "16"
                replace_config ${tmpconfigFile} "pageSize" "4"
                replace_config ${tmpconfigFile} "maxSeMem" "64"
                replace_config ${tmpconfigFile} "maxTotalShmSize" "102"
                replace_config ${tmpconfigFile} "maxSysShmSize" "18"
                replace_config ${tmpconfigFile} "liteDynMemMod" "1"
                replace_config ${tmpconfigFile} "defaultHashType" "chained"
                replace_config ${tmpconfigFile} "maxConnNum" "120"
                replace_config ${tmpconfigFile} "subsChannelGlobalShareMemSizeMax" "20"
                replace_config ${tmpconfigFile} "hashClusterBucketsCount" "100"
                replace_config ${tmpconfigFile} "enableClusterHash" "0"
                replace_config ${tmpconfigFile} "lockTableBucketNum" "509"
                replace_config ${tmpconfigFile} "clientHeartbeatInterval" "30"
            elif [ "x$envVal" = "xrtosv2" ]; then
                replace_config ${tmpconfigFile} "pageSize" "32"
                replace_config ${tmpconfigFile} "lpm4VrfIdMax" "16384"
                replace_config ${tmpconfigFile} "lpm6VrfIdMax" "16384"
                replace_config ${tmpconfigFile} "defaultHashType" "cceh"
                replace_config ${tmpconfigFile} "hashClusterBucketsCount" "30000"
                replace_config ${tmpconfigFile} "enableClusterHash" "1"
            fi
        fi
    fi
fi

# 时序
if  [ ${ts} -eq 1 ]; then
    if [ ${runMode} -eq 0 ]; then
        replace_config ${tmpconfigFile} "workerHungThreshold" "20,299,300"
        replace_config ${tmpconfigFile} "instanceId" "2"
        replace_config ${tmpconfigFile} "enableVectorizedPushDown" "1"
        replace_config ${tmpconfigFile} "cuCompactEnable" "1"
        if  [ ${isSimulate} -eq 1 ]; then
            #时序仿真
            replace_config ${tmpconfigFile} "localLocatorListened" "channel:ctl_channel_tsdb"
        else
            replace_config ${tmpconfigFile} "localLocatorListened" "usocket:/run/verona/unix_emserver_tsdb"
        fi
    else
        if [ ${envType} -eq 1 ]; then
            # 大云杉usg设备
            replace_config ${tmpconfigFile} "diskLessBoot" "0"
            replace_config ${tmpconfigFile} "dataFileDirPath" "/mnt/hdd/data/gmdb"
            replace_config ${tmpconfigFile} "tempFileDir" "/mnt/hdd/temp"
            replace_config ${tmpconfigFile} "cStoreDir" "/mnt/hdd/data/gmdb/cstore"
        fi
    fi
fi

# 硬件卸载
if  [ ${runMode} -eq 0 ]; then
    if [ -d ${GMDB_HOME}/output/euler/aarch64 ]; then
        strTmp=`strings ${GMDB_HOME}/output/euler/aarch64/lib/*.so | grep -w se_hac_common.c`
        if [ "x${strTmp}" != "x" ]; then
            replace_config ${tmpconfigFile} "memCompactEnable" "0"
            replace_config ${tmpconfigFile} "enableReleaseDevice" "0"
            replace_config ${tmpconfigFile} "hacMode" "2"
            replace_config ${tmpconfigFile} "multiHashBucketCount" "16384"
        fi
    fi
fi

# 光启nerg
if  [ ${runMode} -eq 0 ]; then
    if [ -d ${GMDB_HOME}/output/euler/aarch64 ]; then
        strTmp=`strings ${GMDB_HOME}/output/euler/aarch64/lib/*.so | grep -w GmcModifyCliSessionId`
        if [ "x${strTmp}" != "x" ]; then
            eth0=`ifconfig |grep -o eth0 >/dev/null && echo "eth0" ||ifconfig |head -n 1 |awk '{print $1}'|awk -F: '{print $1}'`
            IP_ADDR=$(ifconfig $eth0 | awk '/inet / {print $2}' | cut -d: -f2)
            hostName="tcp:host=$IP_ADDR,port=10086"
            # 拉远通信修改通信方式服务端
            replace_config ${tmpconfigFile} "localLocatorListened" "$hostName"
            # 拉远通信不支持共享内存池
            replace_config ${tmpconfigFile} "enableShareMsgPool" "0"
            replace_config ${tmpconfigFile} "DBA" "TcpConnRoot"
        fi
    fi
fi

# 持久化配置项
hasPst=$(grep ^"persistentMode" ${tmpconfigFile} | wc -l)
if [ $hasPst -ne 0 ];then
    replace_config ${tmpconfigFile} "memCompactEnable" "0"
    replace_config ${tmpconfigFile} "enableReleaseDevice" "0"
fi

extra_config_change

/bin/cp ${tmpconfigFile} ${configFile}

exit 0
