#!/bin/bash

function usage()
{
    echo "[stop.sh] usage: stop.sh [-f] [-ts] [-m] [-s]"
    echo "-f:  stop force "
    echo "-ts: stop ts db "
    echo "-m: stop master db"
    echo "-s: stop slave db"
    exit 3
}

function needStop()
{
    if [ ${forceFlag} -eq 1 ]; then
        return 1
    fi
    if [ ${forceFlag} -eq 2 ]; then
        return 1
    fi
    if [ -f ${TEST_HOME}/.updateCfg ]; then
        rm -f ${TEST_HOME}/.updateCfg
	return 1
    fi
    if [ ${ExecuteLevel} -eq 1 ]; then
        return 0
    fi
    return 1
}

forceFlag=0
master=0
slave=0
stop_proc_name="gmserver"
forceFlag=0
if [ $# -eq 1 ]; then
    if [ "x$1" = "x-f" ]; then
        forceFlag=1
    elif [ "x$1" = "x-ts" ];then
        forceFlag=2
        stop_proc_name="gmserver_ts"
    elif [ "x$1" = "x-m" ];then
        master=1
        stop_proc_name="gmserver"
    elif [ "x$1" = "x-s" ];then
        slave=1
        stop_proc_name="gmserver_2"
    else
        usage
    fi
elif [ $# -eq 2 ]; then
    if [ "x$1" = "x-f" -o "x$2" = "x-f" ]; then
        forceFlag=1
    fi
    if [ "x$1" = "x-m" -o "x$2" = "x-m" ]; then
        master=1
        stop_proc_name="gmserver"
    fi
    if [ "x$1" = "x-s" -o "x$2" = "x-s" ]; then
        slave=1
        stop_proc_name="gmserver_2"
    fi
    if [ "x$1" = "x-ts" ]; then
        forceFlag=2
        stop_proc_name="gmserver_ts"
    fi
elif [ $# -gt 2 ]; then
    usage
fi

fileName="${TEST_HOME}/cfg/sysconfig.ini"
if [ ! -f ${fileName} ]; then
    echo "[$0] file:${fileName} is not existant."
    exit 1
fi
EnvType=`grep -w EnvType ${fileName}| awk '{print $3}'`
if [ "x${EnvType}" != "x0" -a "x${EnvType}" != "x1" -a "x${EnvType}" != "x2" -a "x${EnvType}" != "x3" -a "x${EnvType}" != "x5" ]; then
    echo "[$0] invalid EnvType:${EnvType}, it must been in [0, 1, 2, 5]."
    exit 2
fi
RunMode=`grep -w RunMode ${fileName}| awk '{print $3}'`
if [ "x${RunMode}" != "x0" -a "x${RunMode}" != "x1" -a "x${RunMode}" != "x2" ]; then
    echo "invalid RunMode:${RunMode}, it must been in [0, 1, 2]. Please Check ${TEST_HOME}/cfg/sysconfig.ini"
    exit 2
fi
if [ ${RunMode} -eq 1 -a ${forceFlag} -ne 2 ] ||  [ ${RunMode} -eq 2 ]; then
    exit 1
fi
ExecuteLevel=`grep -w executeLevel ${fileName}| awk '{print $3}'`
if [ "x${ExecuteLevel}" != "x0" -a "x${ExecuteLevel}" != "x1" -a "x${ExecuteLevel}" != "x2" -a "x${ExecuteLevel}" != "x3" ]; then
    echo "[$0] invalid ExecuteLevel:${ExecuteLevel}, it must been in [0, 1, 2, 3]."
    exit 2
fi

if [ $master -ne 1 -a $slave -ne 1 ]; then
    needStop
    if [ $? -ne 1 ]; then
        exit 4
    fi
fi

pidOfServer=""
procExist=""
function getProcExist()
{
    if [ $master -eq 1 -o $slave -eq 1 ]; then
        procExist=`ps ux|grep "${stop_proc_name} "|grep -v grep|awk '{print $2}'`
    else
        procExist=`pidof ${stop_proc_name}`
    fi
    ts_proc=`pidof gmserver_ts`
    if [ "x" != "x${ts_proc}" ]; then
        procExist="${procExist} ${ts_proc}"
    fi
}

if [ ${RunMode} -eq 0 ]; then
    if [ $master -eq 1 -o $slave -eq 1 ]; then
        pidOfServer=`ps ux|grep "${stop_proc_name} "|grep -v grep|awk '{print $2}'`
    else
        pidOfServer=$(pidof ${stop_proc_name})
    fi
    getProcExist
    if [ "x" != "x${procExist}" ]; then
        if [ "x$TCP_MODE" = "x1" ]; then
            kill -9 ${procExist}
        else
            kill ${procExist}
        fi 
    fi
    idx=0
    while [ "x" != "x${procExist}" -a ${idx} -lt 30 ]
    do
        sleep 1
        getProcExist
        idx=`expr ${idx} + 1`
    done
    if [ "x" != "x${procExist}" ]; then
        kill -9 ${procExist}
    fi
    if [ $slave -eq 1 -o $master -eq 1 ]; then
        echo "-"
    else
        shmIdList=`ipcs |grep -w ${LOGNAME} | awk '{print $2}'`
        if [ ${forceFlag} -ne 2 ]; then
            for id in ${shmIdList}
            do
                ipcrm -m ${id}
            done
            ipcrm -a
        fi
    fi
elif [ ${EnvType} -eq 1 ]; then
    if [ ${forceFlag} -ne 2 ]; then
        which fsinit 1>/dev/null 2>/dev/null
        kill -9 $(ps -ef | grep start_hpe.sh | grep -v grep | awk '{print $2}')
        kill -9 $(pidof qemu-system-aarch64)
        kill -9 $(pidof hpk)
        rm -rf /mnt/hpe/hpehuge/*
        ipcrm -a
    else
        # 停时序设备服务
        kill -9 $(pidof ${stop_proc_name})
    fi
elif [ ${EnvType} -eq 2 ]; then
    pid2=`ps | grep -w hpk | grep -v grep | awk '{print $1}'`
    if [ "x" != "x${pid2}" ]; then
        kill -9 ${pid2}
    fi
    try="0"
    while [ $(ps -w | grep 'hpk' | grep -v "defunct" | grep -v "grep" | wc -l) -ne 0 -a "${try}" -lt 30000 ]
    do
        kill -9 $(ps -w | grep 'hpk' | grep -v "grep" | awk '{print $1}' | tr -s '\n' ' ')  >/dev/null 2>&1
        sleep 0.5
        try="$(expr $try + 500)"
    done
    pid1=`ps | grep -w start_hpe.sh | grep -v grep | awk '{print $1}'`
    if [ "x" != "x${pid1}" ]; then
        kill -9 ${pid1}
    fi
    if [ "x" != "x${pid1}" -o "x" != "x${pid2}" ]; then
        sleep 1
    fi
    ipcrm -a
else
    echo "[$0] EnvType:${EnvType} is wrong!"
    exit 1
fi

echo "Info: before stop service, gmserver pid: ${pidOfServer}"
echo "Info: stopped service success"
