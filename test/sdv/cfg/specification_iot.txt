#ifndef __TEST_CONFIG_H__
#define __TEST_CONFIG_H__

#define  MAX_CONN_SIZE  118 

#define  MAX_ASYNC_CONN_SIZE_PER_PRO  118
#define  MAX_SUB_CONN_SIZE  118

#define  CONCURRENT_CONN_SIZE  30
#define  RECORD_NUM_001  10000
#define  RECORD_NUM_002  2000
#define  RECORD_NUM_003  100

int g_envType = 2;
int g_runMode = 1;
const int g_cpuType = 0;
const int g_cpuBit  = 64;

int g_tableNum001   = 10;

// for sn
int g_snThrdCount01 = 25;

// for respool
int64_t g_resMaxSize = 335544320;

// for ddrt.schedule
int g_schCycleNum = 10;
int g_schBigData  = 1000;
int g_schUserConnSize = 30;
int g_schNum001  = 50000;

#endif/*#define  __TEST_CONFIG_H__*/
