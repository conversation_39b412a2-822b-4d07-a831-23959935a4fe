{"version": "2.0", "name": "EventLog", "type": "record", "fields": [{"name": "LOG_SEQUENCE", "type": "uint64", "auto_increment": true, "comment": "log sequence id"}, {"name": "LOG_ID", "type": "uint32", "comment": "model id"}, {"name": "LOG_TIME", "type": "uint32", "comment": "log generation time"}, {"name": "SYSTEM_NAME", "type": "string", "comment": "system name"}, {"name": "LOG_MODULE", "type": "string", "comment": "log module"}, {"name": "LOG_LEVEL", "type": "uint32", "comment": "log level(1:<PERSON><PERSON><PERSON><PERSON>, 2:<PERSON><PERSON><PERSON>, 5:AlarmLog, 18:<PERSON><PERSON>og)"}, {"name": "LOG_NAME", "type": "string", "comment": "log name"}, {"name": "LOG_TYPE", "type": "uint32", "comment": "log type"}, {"name": "VSYS_ID", "type": "uint32", "comment": "vsys id"}, {"name": "APP_ID", "type": "uint32", "comment": "app id"}, {"name": "LOG_DESC", "type": "string", "comment": "log description"}], "keys": [{"name": "EventLog_key", "fields": ["LOG_SEQUENCE"], "node": "EventLog", "index": {"type": "primary"}}]}