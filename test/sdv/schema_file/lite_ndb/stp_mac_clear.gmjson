{"comment": "MSTP基于实例清除MAC，FES356#", "version": "2.0", "type": "record", "name": "stp_mac_clear", "config": {"check_validity": false}, "max_record_count": 256, "fields": [{"name": "tb", "type": "uint16"}, {"name": "tp", "type": "uint16"}, {"name": "vrid", "type": "uint32"}, {"name": "serial_no", "type": "uint32"}, {"name": "app_source_id", "type": "uint32"}, {"name": "app_serial_id", "type": "uint32"}, {"name": "app_obj_id", "type": "uint64"}, {"name": "app_version", "type": "uint32"}, {"name": "clearmactime", "type": "time"}], "keys": [{"name": "stp_mac_clear_pk", "index": {"type": "primary"}, "node": "stp_mac_clear", "fields": ["tb", "tp", "vrid"], "constraints": {"unique": true}}]}