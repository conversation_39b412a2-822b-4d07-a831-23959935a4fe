{"version": "2.0", "type": "record", "name": "mqc_acl_enable", "config": {"check_validity": false}, "max_record_count": 47104, "fields": [{"name": "unit", "type": "uint8", "comment": "芯片号"}, {"name": "vrid", "type": "uint8", "comment": "虚拟路由"}, {"name": "is_lag", "type": "uint8"}, {"name": "app_pri", "type": "uint8", "comment": "应用实例索引"}, {"name": "app_inst", "type": "uint32", "comment": "应用实例ID"}, {"name": "vlan", "type": "uint16", "comment": "根据enableType遍历时新增VLAN字段"}, {"name": "port_lag", "type": "uint16", "comment": "根据enableType遍历时新增PORT/LAG字段"}, {"name": "ce_vlan", "type": "uint16", "comment": "ce_VLAN字段"}, {"name": "direction", "type": "uint16", "comment": "应用方向"}, {"name": "is_l2_subif", "type": "uint16"}, {"name": "enable_type", "type": "uint16", "comment": "使能类型"}, {"name": "subif_index", "type": "uint32"}, {"name": "subif_eid", "type": "uint32"}, {"name": "vlan_domain", "type": "uint32"}, {"name": "vsi", "type": "uint32"}], "keys": [{"name": "pri_key", "index": {"type": "primary"}, "node": "mqc_acl_enable", "fields": ["unit", "vrid", "is_lag", "app_pri", "app_inst", "vlan", "port_lag", "ce_vlan"], "constraints": {"unique": true}, "comment": "应用实例索引关键字名称"}, {"name": "inst_scan", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "mqc_acl_enable", "fields": ["unit", "vrid", "app_inst"], "constraints": {"unique": false}, "comment": "应用实例遍历关键字名称"}, {"name": "type_scan", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "mqc_acl_enable", "fields": ["unit", "vrid", "is_lag", "vlan", "port_lag", "ce_vlan", "direction", "enable_type"], "constraints": {"unique": false}, "comment": "应用实例类型遍历关键字名称"}], "super_fields": [{"name": "acl_enable_sf", "fields": ["unit", "vrid", "is_lag", "app_pri", "app_inst", "vlan", "port_lag", "ce_vlan", "direction", "is_l2_subif", "enable_type", "subif_index", "subif_eid", "vlan_domain", "vsi"]}]}