{"version": "2.0", "type": "record", "name": "nsres_template_index", "config": {"check_validity": false}, "max_record_count": 256, "fields": [{"name": "vrid", "type": "uint32"}, {"name": "template", "type": "uint32"}, {"name": "mod", "type": "uint32"}, {"name": "index", "type": "uint32"}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "nsres_template_index", "fields": ["vrid", "template"], "constraints": {"unique": true}}, {"name": "index_key", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "nsres_template_index", "fields": ["vrid", "index"], "constraints": {"unique": true}}]}