{"version": "2.0", "type": "record", "name": "syslog_gblcfg", "config": {"check_validity": false}, "max_record_count": 8, "fields": [{"name": "vsid", "type": "uint32", "comment": "vs id"}, {"name": "charset", "type": "uint32", "comment": "character set"}], "keys": [{"name": "syslog_gblcfg_key", "index": {"type": "primary"}, "node": "syslog_gblcfg", "fields": ["vsid"], "constraints": {"unique": true}, "comment": "primay key"}]}