{"comment": "RESOURCE", "version": "2.0", "type": "record", "name": "resource", "config": {"check_validity": false}, "max_record_count": 12800, "fields": [{"name": "vsys_id", "type": "uint16"}, {"name": "ipsec_tunnel_reserve", "type": "uint32"}, {"name": "ipsec_tunnel_maximum", "type": "uint32"}], "keys": [{"name": "id_pk", "index": {"type": "primary"}, "node": "resource", "fields": ["vsys_id"], "constraints": {"unique": true}}]}