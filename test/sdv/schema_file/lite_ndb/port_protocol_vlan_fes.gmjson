{"comment": "fes 2151", "version": "2.0", "type": "record", "name": "port_protocol_vlan_fes", "config": {"check_validity": false}, "max_record_count": 65535, "fields": [{"name": "ifindex", "type": "uint32", "comment": "ifindex"}, {"name": "vrid", "type": "uint32", "comment": "vrid"}, {"name": "protocol_index", "type": "uint16", "comment": "protocol index"}, {"name": "vlan_id", "type": "uint16", "comment": "vlan id"}, {"name": "vlan_priority", "type": "uint8", "comment": "vlan priority, 0-7"}], "keys": [{"name": "port_protocol_vlan_fes_pk", "index": {"type": "primary"}, "node": "port_protocol_vlan_fes", "fields": ["ifindex", "vrid", "protocol_index", "vlan_id"], "constraints": {"unique": true}, "comment": "primary key"}, {"name": "port_protocol_vlan_fes_pk_2", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "port_protocol_vlan_fes", "fields": ["ifindex"], "constraints": {"unique": false}, "comment": "match protocol_vlan_fes table"}, {"name": "port_protocol_vlan_fes_pk_3", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "port_protocol_vlan_fes", "fields": ["vrid", "protocol_index", "vlan_id"], "constraints": {"unique": false}}]}