{"comment": "代理IP业务表", "version": "2.0", "type": "record", "name": "vxlan_nve_entry", "config": {"check_validity": true}, "max_record_count": 64, "fields": [{"name": "vrid", "type": "uint32", "comment": "vs索引"}, {"name": "ifindex", "type": "uint32", "comment": "NVE地址索引"}, {"name": "src_addr", "type": "uint32", "comment": "源地址"}, {"name": "proxy_ip", "type": "uint32", "comment": "代理IP"}, {"name": "dwn_flag", "type": "uint8", "comment": "下发标识"}, {"name": "rsv1", "type": "fixed", "size": 3, "comment": "保留"}, {"name": "ver_no", "type": "uint32", "comment": "版本号"}], "keys": [{"name": "ifindex_key", "index": {"type": "primary"}, "node": "vxlan_nve_entry", "fields": ["vrid", "ifindex"], "constraints": {"unique": true}, "comment": "根据主键索引"}, {"name": "vr_index", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "vxlan_nve_entry", "fields": ["vrid"], "constraints": {"unique": false}, "comment": "根据vs索引"}, {"name": "sip_index", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "vxlan_nve_entry", "fields": ["vrid", "src_addr"], "constraints": {"unique": false}, "comment": "根据源地址索引"}]}