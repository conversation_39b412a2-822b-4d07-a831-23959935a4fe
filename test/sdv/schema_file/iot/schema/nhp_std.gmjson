{"comment": "标准下一跳表，对应42#表", "version": "2.0", "type": "record", "name": "nhp_std", "config": {"check_validity": true}, "max_record_count": 4096000, "fields": [{"name": "nhp_index", "type": "uint32", "comment": "下一跳索引"}, {"name": "next_hop", "type": "uint32", "comment": "下一跳"}, {"name": "out_if_index", "type": "uint32", "comment": "出接口"}, {"name": "ns_id", "type": "uint32", "comment": "Vs索引"}, {"name": "vrf_index", "type": "uint32", "comment": "VpnInstace索引"}, {"name": "iid_flags", "type": "uint32", "comment": "直连路由/非直连路由切换过"}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "nhp_std", "fields": ["nhp_index", "next_hop", "out_if_index"], "constraints": {"unique": true}, "comment": "根据主键索引"}, {"name": "nhpindex_localhash_key", "index": {"type": "hashcluster"}, "node": "nhp_std", "fields": ["nhp_index"], "constraints": {"unique": false}, "comment": "根据nhp_index索引"}, {"name": "ip_ifindex_localhash_key", "index": {"type": "hashcluster"}, "node": "nhp_std", "fields": ["next_hop", "out_if_index"], "constraints": {"unique": false}, "comment": "根据出接口和下一跳查询"}, {"name": "ifindex_localhash_key", "index": {"type": "hashcluster"}, "node": "nhp_std", "fields": ["out_if_index"], "constraints": {"unique": false}, "comment": "根据出接口查询"}]}