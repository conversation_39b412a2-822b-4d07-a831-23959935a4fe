{"comment": "nctl_arp_if_cfg表", "version": "2.0", "type": "record", "name": "nctl_arp_if_cfg", "max_record_count": 1024, "fields": [{"name": "ifindex", "type": "uint32", "comment": "ARP对应的三层接口索引"}, {"name": "global_flag", "type": "uint8", "comment": "全局老化时间标记，1为全局配置，0为接口下配置"}, {"name": "fake_time", "type": "uint32", "comment": "ARP老化时间配置"}], "keys": [{"name": "nctl_arp_if_cfg_key", "index": {"type": "primary"}, "node": "nctl_arp_if_cfg", "fields": ["ifindex"], "constraints": {"unique": true}}]}