{"version": "2.0", "type": "record", "name": "smlk_rcv_cfg", "config": {"check_validity": false}, "max_record_count": 1024, "fields": [{"name": "ifindex", "type": "uint32"}, {"name": "vr_id", "type": "uint32"}, {"name": "ctrl_vlan_id", "type": "int16"}, {"name": "flush_rcv_enable", "type": "uint8"}, {"name": "port_type", "type": "uint8"}, {"name": "trunk_mem_port_num", "type": "uint32"}, {"name": "trunk_mem_port_tb", "type": "fixed", "size": 512}, {"name": "trunk_mem_port_tp", "type": "fixed", "size": 512}, {"name": "phy_port_tb", "type": "uint16"}, {"name": "phy_port_tp", "type": "uint16"}], "keys": [{"name": "main_index", "index": {"type": "primary"}, "node": "smlk_rcv_cfg", "fields": ["ifindex"], "constraints": {"unique": true}}]}