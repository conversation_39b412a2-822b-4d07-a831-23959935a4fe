{"comment": "IPV4 Entry软表", "version": "2.0", "type": "record", "name": "strack_v4_entry", "config": {"check_validity": false}, "max_record_count": 64, "fields": [{"name": "ip", "type": "uint32", "comment": "ipv4地址"}, {"name": "protocol", "type": "uint32", "comment": "协议类型"}, {"name": "timeout", "type": "uint32", "comment": "超时周期"}, {"name": "acl", "type": "record", "array": true, "size": 3, "comment": "ipv4 acl", "fields": [{"name": "entrys", "type": "record", "array": true, "size": 8, "comment": "auiEntry", "fields": [{"name": "entryID", "type": "uint32", "comment": "entry ID"}]}]}, {"name": "reserve", "type": "uint32", "comment": "预留字段"}], "keys": [{"name": "strack_v4_entry_pk", "index": {"type": "primary"}, "node": "strack_v4_entry", "fields": ["ip", "protocol"], "constraints": {"unique": true}, "comment": "根据主键索引"}, {"name": "strack_v4_entry_sk", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "strack_v4_entry", "fields": ["protocol"], "constraints": {"unique": false}, "comment": "根据协议类型索引"}]}