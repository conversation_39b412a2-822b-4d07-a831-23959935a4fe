#include "expectation.hpp"
#include "execution_configuration.hpp"
#include "logger.hpp"
#include "span.hpp"
#include "string_view.hpp"
#include <gmc.h>  // GmcGetPropertyById
#include <gmc_connection.h>
#include <gmc_graph.h>  // GmcGetVertexPropertySizeByName
#include <algorithm>
#include <sstream>
#include <system_error>

namespace gmdb::test {
namespace {

struct row_to_csv {
    explicit row_to_csv(size_t column_count) : _column_count{column_count}
    {}

private:
    size_t _column_count;
};

expectation_result expect_std_impl(
    const std::vector<std::string> &output, const std::vector<std::string> &actual_out, std::string_view name)
{
    expectation_result er{};

    if (!std::equal(output.begin(), output.end(), actual_out.begin(), actual_out.end())) {
        auto exp_sz = std::size(output);
        auto act_sz = std::size(actual_out);
        if (exp_sz != act_sz) {
            std::stringstream ss;
            ss << "expected " << exp_sz << ", got " << act_sz << " " << name;
            er.context.push_back(ss.str());
        }

        for (size_t i = 0, end_i = std::min(exp_sz, act_sz); i < end_i; ++i) {
            const auto &exp_out = output[i];
            const auto &act_out = actual_out[i];
            if (exp_out != act_out) {
                std::stringstream ss;
                ss << "mismatched line " << i << ": expected '" << exp_out << "', got '" << act_out << "'";
                er.context.push_back(ss.str());
            }
        }

        std::stringstream ss;
        ss << "got " << std::size(actual_out) << " line(s) in: " << name << ": [\n";
        join_collections(ss, actual_out, "\n");
        ss << "\n]";
        er.context.push_back(ss.str());
        er.status = check_result::failure;
        return er;
    }
    er.status = check_result::success;
    return er;
}

}  // namespace

expectation_result expect_nothing::execute()
{
    // Nothing to do: non-comment, normal comment or blank line.
    return {.status = check_result::nothing, .context = {}, .output = {}};
}

expectation_result expect_run::execute() const
{
    expectation_result er{};
    er.context.push_back("RUN: " + command);

    try {
        int exit_code = std::system(command.c_str());

        er.output = detail::output_result{
            //
            .exit_code = exit_code,
        };
    } catch (const std::system_error &e) {
        er.output = detail::output_result{};
        er.output->exit_code = -1;
        std::stringstream ss;
        ss << "Execution failed, caught system error: (" << e.code().value(); 
        ss << ") " << e.code().message();
        ss << " - " << e.what();
        er.context.push_back(ss.str());
    } catch (...) {
        er.output = detail::output_result{};
        er.output->exit_code = -1;
        er.context.emplace_back("Execution failed, caught unknown error");
    }
    return er;
}

expectation_result expect_stdout::execute(const detail::output_result &out) const
{
    return expect_std_impl(rows, out.std_out, "stdout");
}

expectation_result expect_stderr::execute(const detail::output_result &out) const
{
    return expect_std_impl(rows, out.std_err, "stderr");
}

expectation_result expect_exit_code::execute(const detail::output_result &out) const
{
    if (out.exit_code != exit_code) {
        return {.status = check_result::failure,
            .context = {"got exit code: " + std::to_string(out.exit_code)},
            .output = {}};
    }
    return {.status = check_result::success, .context = {}, .output = {}};
}

expectation_result expect_success::execute(const detail::query_result &qr)
{
    expectation_result er{};

    if (auto err = qr.get_error(); IS_FAILURE(err)) {
        {
            std::stringstream ss;
            ss << "got error: " << pretty_error{err};
            er.context.push_back(ss.str());
        }

        const auto *r = qr.get_result();
        if (r) {
            std::string_view msg = r->error_message;
            if (!std::empty(msg)) {
                std::stringstream ss;
                ss << "got message: " << msg;
                er.context.push_back(ss.str());
            }
        }
        er.status = check_result::failure;
        return er;
    }

    er.status = check_result::success;
    return er;
}

expectation_result expect_error::execute(const detail::query_result &qr) const
{
    expectation_result er{};

    auto err = qr.get_error();
    if (err != error) {
        std::stringstream ss;
        ss << "got error: " << pretty_error{err};
        er.context.push_back(ss.str());
        er.status = check_result::failure;
        return er;
    }

    er.status = check_result::success;
    return er;
}

expectation_result expect_no_message::execute(const detail::query_result &qr)
{
    expectation_result er{};
    const auto *r = qr.get_result();
    if (!r) {
        er.status = check_result::success;
        return er;
    }

    std::string_view msg = r->error_message;
    if (std::empty(msg)) {
        er.status = check_result::success;
        return er;
    }

    log::emit(log::info, "got message: ", msg);
    er.context.push_back("got message: " + std::string(msg));
    er.status = check_result::failure;
    return er;
}

expectation_result expect_message::execute(const detail::query_result &qr) const
{
    expectation_result er{};

    const auto *r = qr.get_result();
    if (!r) {
        er.context.push_back("got no result");
        er.status = check_result::failure;
        return er;
    }

    const std::string &msg = r->error_message;
    if (msg.find(message) == std::string::npos) {
        er.context.push_back("got message: " + std::string(msg));
        er.status = check_result::failure;
        return er;
    }

    er.status = check_result::success;
    return er;
}

expectation_result expect_columns::execute(const detail::query_result &qr) const
{
    expectation_result er{};

    const auto *r = qr.get_result();
    if (!r) {
        er.context.push_back("got no result");
        er.status = check_result::failure;
        return er;
    }

    GmcStmtT *stmt = r->stmt;

    uint32_t column_count = 0;
    {
        Status ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &column_count, sizeof(column_count));
        if (ret != GMERR_OK) {
            std::stringstream ss;
            ss << "cannot get result column count: " << pretty_error{ret};
            er.context.push_back(ss.str());
            er.status = check_result::failure;
            return er;
        }
    }

    auto exp_sz = std::size(columns);
    if (column_count != exp_sz) {
        std::stringstream ss;
        ss << "expected " << exp_sz << ", got " << column_count << " columns";
        er.context.push_back(ss.str());
        er.status = check_result::failure;
        return er;
    }

    for (const std::string &col : columns) {
        uint32_t property_size = 0;  // We don't care about the value.
        Status ret = GmcGetVertexPropertySizeByName(stmt, col.data(), &property_size);
        log::emit(log::debug, "check column '", col, ": size=", property_size, ", ret=", ret);
        if (ret != GMERR_OK) {
            er.context.push_back("column '" + col + "' is missing");
            er.status = check_result::failure;
            return er;
        }
    }

    er.status = check_result::success;
    return er;
}

expectation_result expect_rows::execute(const detail::query_result &qr) const
{
    expectation_result er{};

    const auto *r = qr.get_result();
    if (!r) {
        er.context.push_back("got no result");
        er.status = check_result::failure;
        return er;
    }

    assert(r->stmt);
    if (!r->stmt) {
        er.context.push_back("got no statement");
        er.status = check_result::failure;
        return er;
    }

    GmcStmtT *stmt = r->stmt;

    uint32_t column_count = 0;
    {
        Status ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &column_count, sizeof(column_count));
        if (ret != GMERR_OK) {
            std::stringstream ss;
            ss << "cannot get result column count: " << pretty_error{ret};
            er.context.push_back(ss.str());
            er.status = check_result::failure;
            return er;
        }
    }

    uint32_t row_count = 0;
    {
        Status ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &row_count, sizeof(row_count));
        if (ret != GMERR_OK) {
            std::stringstream ss;
            ss << "cannot get result row count: " << pretty_error{ret};
            er.context.push_back(ss.str());
            er.status = check_result::failure;
            return er;
        }
    }

    auto exp_sz = std::size(rows);
    if (row_count != exp_sz) {
        std::stringstream ss;
        ss << "expected " << exp_sz << ", got " << row_count << "rows";
        er.context.push_back(ss.str());
        er.status = check_result::failure;
        return er;
    }

    std::vector<std::string> actual_rows;
    actual_rows.reserve(row_count);

    size_t row_i = 0;
    bool eof = false;
    while (!eof) {
        Status ret = GmcFetch(stmt, &eof);
        if (ret != GMERR_OK) {
            std::stringstream ss;
            ss << "cannot fetch, got error: " << ret << ", eof=" << eof;
            er.context.push_back(ss.str());
            er.status = check_result::failure;
            return er;
        }
        if (eof) {
            break;
        }

        std::vector<std::string> row_values;
        row_values.reserve(column_count);
        for (size_t col_i = 0; col_i < column_count; ++col_i) {
            int64_t value = 0;
            uint32_t property_size = sizeof(value);
            bool is_null = false;
            ret = GmcGetPropertyById(const_cast<const GmcStmtT *>(stmt), col_i, &value, &property_size, &is_null);
            if (ret != GMERR_OK) {
                std::stringstream ss;
                ss << "cannot get row no. " << row_i << ", col no. " << col_i << ", got error: " << ret << ", is_null=" << is_null;
                er.context.push_back(ss.str());
                er.status = check_result::failure;
                return er;
            }

            row_values.push_back(std::to_string(value));
        }
        actual_rows.push_back(join_strings(row_values, ","));
        ++row_i;
    }

    if (row_i != row_count) {
        // Sanity check
        std::stringstream ss;
        ss << "fetched " << row_i << " rows, but row count was " << row_count;
        er.context.push_back(ss.str());
        er.status = check_result::failure;
        return er;
    }

    if (unordered) {
        std::sort(actual_rows.begin(), actual_rows.end());
    }

    if (!std::equal(rows.begin(), rows.end(), actual_rows.begin(), actual_rows.end())) {
        auto exp_sz = std::size(rows);
        auto act_sz = std::size(actual_rows);
        if (exp_sz != act_sz) {
            std::stringstream ss;
            ss << "expected " << exp_sz << ", got " << act_sz << " rows";
            er.context.push_back(ss.str());
        }

        for (size_t i = 0, end_i = std::min(exp_sz, act_sz); i < end_i; ++i) {
            const auto &exp_row = rows[i];
            const auto &act_row = actual_rows[i];
            if (exp_row != act_row) {
                std::stringstream ss;
                ss << "mismatched row " << i << ": expected '" << exp_row << "', got '" << act_row << "'";
                er.context.push_back(ss.str());
            }
        }

        std::stringstream ss;
        ss << "got " << std::size(actual_rows) << " row(s): [\n";
        join_collections(ss, actual_rows, "\n");
        ss << "\n]";
        er.context.push_back(ss.str());
        er.status = check_result::failure;
        return er;
    }

    er.status = check_result::success;
    return er;
}

expectation_result expect_scanned_point_count::execute(const detail::query_result &qr) const
{
    expectation_result er{};

    const auto *r = qr.get_result();
    if (!r) {
        er.context.push_back("got no result");
        er.status = check_result::failure;
        return er;
    }

    GmcStmtT *stmt = r->stmt;

    assert(stmt);
    if (!stmt) {
        er.context.push_back("got no statement");
        er.status = check_result::failure;
        return er;
    }

    uint32_t affected_rows_count = 0;
    {
        Status ret =
            GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affected_rows_count, sizeof(affected_rows_count));
        if (ret != GMERR_OK) {
            std::stringstream ss;
            ss << "cannot get affected rows count: " << pretty_error{ret};
            er.context.push_back(ss.str());
            er.status = check_result::failure;
            return er;
        }
    }

    if (affected_rows_count != scanned_point_count) {
        er.context.push_back("got scanned point count: " + std::to_string(affected_rows_count));
        er.status = check_result::failure;
        return er;
    }

    er.status = check_result::success;
    return er;
}

void expect_nothing::prepare(const execution_configuration & /*cfg*/, cache_map & /*cache*/)
{}

void expect_run::prepare(const execution_configuration &cfg, cache_map &cache)
{
    command = apply_replacements(cfg, cache, command);
}

void expect_stdout::prepare(const execution_configuration &cfg, cache_map &cache)
{
    for (auto &s : rows) {
        s = apply_replacements(cfg, cache, s);
    }
}

void expect_stderr::prepare(const execution_configuration &cfg, cache_map &cache)
{
    for (auto &s : rows) {
        s = apply_replacements(cfg, cache, s);
    }
}

void expect_exit_code::prepare(const execution_configuration & /*cfg*/, cache_map & /*cache*/)
{}

void expect_success::prepare(const execution_configuration & /*cfg*/, cache_map & /*cache*/)
{}

void expect_error::prepare(const execution_configuration & /*cfg*/, cache_map & /*cache*/)
{}

void expect_no_message::prepare(const execution_configuration & /*cfg*/, cache_map & /*cache*/)
{}

void expect_message::prepare(const execution_configuration &cfg, cache_map &cache)
{
    message = apply_replacements(cfg, cache, message);
}

void expect_columns::prepare(const execution_configuration &cfg, cache_map &cache)
{
    for (auto &v : columns) {
        v = apply_replacements(cfg, cache, v);
    }
}

void expect_rows::prepare(const execution_configuration &cfg, cache_map &cache)
{
    for (auto &v : rows) {
        v = apply_replacements(cfg, cache, v);
    }

    if (unordered) {
        std::sort(rows.begin(), rows.end());
    }
}

void expect_scanned_point_count::prepare(const execution_configuration & /*cfg*/, cache_map & /*cache*/)
{}

}  // namespace gmdb::test
