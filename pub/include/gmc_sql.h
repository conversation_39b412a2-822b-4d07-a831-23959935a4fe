/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: gmc_ts.h
 * Description: File provide GMC APIs related to time-series module.
 *              Function order: DDL\DML\other func.
 *              Please keep new function in its corresponding position.
 * Author:
 * Create: 2024-02-22
 */

#ifndef GMC_SQL_H
#define GMC_SQL_H

#include "gmc_types.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @defgroup gmc_ts 客户端接口
 */

/**
 * @ingroup gmc_ts
 * @par 描述：
 * 直接执行传入的sql语句 / Execute given sql command directly
 * @attention 请勿随意更改入参stmt结构，以GMDB接口构造为准，其安全性由调用者保证。
 * @param[in] stmt 客户端stmt句柄。/ stmt handle inside client
 * @param[in] statementText 用户传入的sql语句。/ sql command given by user
 * @param[in] textLength 用户传入的sql语句的长度。/ length of sql command
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcExecDirect(GmcStmtT *stmt, const char *statementText, uint32_t textLength);

/**
 * @ingroup gmc_ts
 * @par 描述：
 * 在结构化注入前对字段（列）进行数据绑定 / Bind application data buffers to columns before bulk insert
 * @attention
 * 请勿随意更改入参stmt结构，以GMDB接口构造为准，其安全性由调用者保证。
 * 绑定列编号从1开始，书签列从0开始，暂时不支持书签列绑定。
 * 绑定数据类型支持UINT8-UINT32，INT8-INT64以及FIXED定长字符串
 * @param[in] stmt 客户端stmt句柄。/ stmt handle inside client
 * @param[in] columnNumber 绑定字段（列）对应编号。/ number of the data set column to bind
 * @param[in] targetType 绑定字段（列）对应数据类型。/ data type of the data set column to bind
 * @param[in] targetValue 指向绑定到字段（列）数据源所在buffer的指针。/ pointer to the data buffer to bind to the column
 * @param[in] bufferLength 数据源所在buffer的长度。/ Length of the *targetValue buffer in bytes
 * @param[in] strLenOrIndPtr 指向要绑定到列的长度/指示器缓冲区的指针。/ pointer to the length/indicator buffer to bind
 * to the column
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcBindCol(GmcStmtT *stmt, uint32_t columnNumber, GmcDataTypeE targetType, void *targetValue,
    uint32_t bufferLength, uint32_t *strLenOrIndPtr);

/**
 * @ingroup gmc_ts
 * @par 描述：
 * 关闭服务器
 * @attention 以GMDB接口构造为准，其安全性由调用者保证。
 * @param[in] conn 连接句柄。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcShutdownServer(GmcConnT *conn);

/**
 * @ingroup gmc_ts
 * @par 描述：
 * 修改dataFileDirPath配置项值。
 * @attention 以GMDB接口构造为准，其安全性由调用者保证。
 * @param[in] dataFileDirPath dataFileDirPath配置项值
 * @param[in] instanceId 目标DB的instanceId。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcSetPersistPath(const char *dataFileDirPath, uint32_t instanceId);

/**
 * @ingroup gmc_connection
 * @par 描述：
 * 在创建stmt句柄后，可获取句柄的快照，并配合GmcSetStmtView完成状态恢复；仅支持CS模式下使用。
 * @attention @li
 * stmtView快照结构体的内存由DB分配，请勿在DB销毁内存后访问此结构体；且在此结构体使用完成后需要及时调用GmcFreeStmtView进行内存回收。
 *  @li 请勿随意更改入参stmt结构，以GMDB接口构造为准，其安全性由调用者保证。
 * @param[in] stmt stmt句柄。
 * @param[out] stmtView 返回快照。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcGetStmtView(GmcStmtT *stmt, GmcStmtViewT **stmtView);

/**
 * @ingroup gmc_connection
 * @par 描述：
 * 在创建stmt句柄后，可根据快照恢复stmt的状态；快照仅可通过GmcGetStmtView获得；仅支持CS模式下使用。
 * @attention @li
 * stmtView快照结构体的内存由DB分配，请勿在DB销毁内存后访问此结构体；且在此结构体使用完成后需要及时调用GmcFreeStmtView进行内存回收。
 *  @li 请勿随意更改入参stmt、stmtView结构，以GMDB接口构造为准，其安全性由调用者保证。
 * @param[in] stmt stmt句柄。
 * @param[in] stmtView 用于恢复的快照。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcSetStmtView(GmcStmtT *stmt, GmcStmtViewT *stmtView);

/**
 * @ingroup gmc_connection
 * @par 描述：
 * 释放stmtView的内存；快照仅可通过GmcGetStmtView获得；仅支持CS模式下使用。
 * @attention @li
 * stmtView快照结构体的内存由DB分配，请勿在调用此接口后继续访问此结构体；
 *  @li 请勿随意更改入参stmtView结构，以GMDB接口构造为准，其安全性由调用者保证。
 * @param[in] stmtView 要被销毁的快照。
 * @return 无。
 */
GMC_EXPORT void GmcFreeStmtView(GmcStmtViewT *stmtView);

/**
 * @ingroup gmc_ts
 * @par 描述：
 * 准备参数化查询的查询语句。
 * @attention 请勿随意更改stmt结构，以GMDB接口构造为准，其安全性由调用者保证。需要传入带有匿名参数的SELECT语句。
 * @param[in] stmt stmt句柄。
 * @param[in] sql 参数化的查询语句。
 * @param[in] cmdLen 语句长度。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcPrepareSql(GmcStmtT *stmt, const char *sql, uint32_t cmdLen);

/**
 * @ingroup gmc_ts
 * @par 描述：
 * 为已经准备过的参数化查询语句绑定参数的实际值。
 * @attention 请勿随意更改stmt结构，以GMDB接口构造为准，其安全性由调用者保证。此阶段不作参数与查询语句的匹配校验。
 * @param[in] stmt stmt句柄。
 * @param[in] index 对应查询语句从左至右的第几个参数，从1开始。
 * @param[in] valueType 绑定参数的数据类型。
 * @param[in] value 绑定实际值的地址。
 * @param[in] strLen 绑定实际值为字符串类型时，字符串的长度。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcBindPara(GmcStmtT *stmt, uint32_t index, GmcDataTypeE valueType, void *value, uint32_t strLen);

#ifdef __cplusplus
}
#endif

#endif
/* GMC_SQL_H */
