/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2021. All rights reserved.
 * File Name: gmc_errno.h
 * Description: header file for GMDB code
 * Author:
 * Create: 2020-7-27
 */

#ifndef GMC_ERRNO_H
#define GMC_ERRNO_H

#include <inttypes.h>

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

/* *
 * This enum provides symbolic names for the status values returned to applications.
 *
 * Zero(0) means success;
 * Anything else means that an error occurred.
 */

typedef void *HandleT;
// 为了避免和V3错误码兼容，加上一个基数
#define GMERR_BASE 1000000

enum {
    // Default return value when an operation was successful.
    GMERR_OK = 0,

    // No data
    GMERR_NO_DATA = GMERR_BASE + 1000,

    GMERR_DATABASE_DOES_NOT_EXIST = GMERR_BASE + 1001,

    GMERR_MAP_FILE_DOES_NOT_EXIST = GMERR_BASE + 1003,

    GMERR_UNRECOGNIZED_DB_VERSION = GMERR_BASE + 1004,

    GMERR_ANN_INDEX_SCAN_DONE = GMERR_BASE + 1005,

    // Connection Exception
    GMERR_CONNECTION_EXCEPTION = GMERR_BASE + 2000,

    GMERR_CONNECTION_DOES_NOT_EXIST = GMERR_BASE + 2001,

    GMERR_CONNECTION_FAILURE = GMERR_BASE + 2002,

    GMERR_CLIENT_UNABLE_TO_ESTABLISH_CONNECTION = GMERR_BASE + 2003,

    // Feature not supported
    GMERR_FEATURE_NOT_SUPPORTED = GMERR_BASE + 3000,

    GMERR_INVALID_OBJECT = GMERR_BASE + 3001,  // for V3 compatibility

    // Data Exception
    GMERR_DATA_EXCEPTION = GMERR_BASE + 4000,

    GMERR_ARRAY_SUBSCRIPT_ERROR = GMERR_BASE + 4001,

    GMERR_INVALID_DATETIME_FORMAT = GMERR_BASE + 4002,

    GMERR_FIELD_OVERFLOW = GMERR_BASE + 4003,

    GMERR_INVALID_PARAMETER_VALUE = GMERR_BASE + 4004,

    GMERR_NULL_VALUE_NOT_ALLOWED = GMERR_BASE + 4005,

    GMERR_INVALID_JSON_CONTENT = GMERR_BASE + 4006,

    GMERR_DIVISION_BY_ZERO = GMERR_BASE + 4007,

    GMERR_ERROR_IN_ASSIGNMENT = GMERR_BASE + 4008,

    GMERR_SEMANTIC_ERROR = GMERR_BASE + 4009,

    GMERR_EPOLL_REG_FUNC_NULL = GMERR_BASE + 4010,  // for V3 compatibility

    GMERR_DUPLICATE_HEARTBEAT_REGISTER = GMERR_BASE + 4011,  // for V3 compatibility

    // Integrity constraint violation
    GMERR_UNIQUE_VIOLATION = GMERR_BASE + 5000,

    GMERR_RESTRICT_VIOLATION = GMERR_BASE + 5001,

    GMERR_PRIMARY_KEY_VIOLATION = GMERR_BASE + 5002,

    GMERR_MEMBER_KEY_VIOLATION = GMERR_BASE + 5003,  // for V3 compatibility

    GMERR_FOREIGN_KEY_VIOLATION = GMERR_BASE + 5004,

    GMERR_CONSTRAINT_CHECK_VIOLATION = GMERR_BASE + 5005,

    // Invalid transaction state
    GMERR_ACTIVE_TRANSACTION = GMERR_BASE + 6000,

    GMERR_READ_ONLY_SQL_TRANSACTION = GMERR_BASE + 6001,

    GMERR_NO_ACTIVE_TRANSACTION = GMERR_BASE + 6002,

    GMERR_TRANS_MODE_MISMATCH = GMERR_BASE + 6003,

    // Dependent priv descriptors still exist
    GMERR_DEPENDENT_OBJECTS_STILL_EXIST = GMERR_BASE + 7000,

    // Transaction rollback
    GMERR_TRANSACTION_ROLLBACK = GMERR_BASE + 8000,

    // Syntax error or access rule violation
    GMERR_SYNTAX_ERROR = GMERR_BASE + 9000,

    GMERR_INVALID_NAME = GMERR_BASE + 9001,

    GMERR_INVALID_PROPERTY = GMERR_BASE + 9002,

    GMERR_INVALID_TABLE_DEFINITION = GMERR_BASE + 9003,

    GMERR_NAME_TOO_LONG = GMERR_BASE + 9004,

    GMERR_RESERVED_NAME = GMERR_BASE + 9005,

    GMERR_INVALID_OPTION = GMERR_BASE + 9006,

    GMERR_DATATYPE_MISMATCH = GMERR_BASE + 9007,

    GMERR_WRONG_STMT_OBJECT = GMERR_BASE + 9008,

    GMERR_UNDEFINED_OBJECT = GMERR_BASE + 9009,

    GMERR_UNDEFINED_TABLE = GMERR_BASE + 9010,

    GMERR_UNDEFINE_COLUMN = GMERR_BASE + 9011,

    GMERR_DUPLICATE_OBJECT = GMERR_BASE + 9012,

    GMERR_DUPLICATE_TABLE = GMERR_BASE + 9013,

    GMERR_DUPLICATE_COLUMN = GMERR_BASE + 9014,

    GMERR_INVALID_OBJECT_DEFINITION = GMERR_BASE + 9015,

    GMERR_INVALID_COLUMN_DEFINITION = GMERR_BASE + 9016,

    GMERR_RESOURCE_POOL_ERROR = GMERR_BASE + 9017,

    GMERR_RESOURCE_POOL_ALREADY_EXIST = GMERR_BASE + 9018,  // for V3 compatibility

    GMERR_RESOURCE_POOL_ALREADY_BOUND = GMERR_BASE + 9019,  // for V3 compatibility

    // 只针对DbValue使用
    GMERR_INVALID_VALUE = GMERR_BASE + 9020,  // for V3 compatibility

    GMERR_RESOURCE_POOL_NOT_ENOUGH = GMERR_BASE + 9021,  // for V3 compatibility

    // TS独有错误码
    GMERR_TABLE_EXCEED_DISK_LIMIT = GMERR_BASE + 9022,

    // YANG独有错误码，针对升级场景返回异常信息提示业务全量重试
    GMERR_UPGRADE_VIOLATION = GMERR_BASE + 9023,

    // Insufficient resources
    GMERR_INSUFFICIENT_RESOURCES = GMERR_BASE + 10000,

    GMERR_OUT_OF_MEMORY = GMERR_BASE + 10001,

    GMERR_TOO_MANY_CONNECTIONS = GMERR_BASE + 10002,

    GMERR_CONFIGURATION_LIMIT_EXCEEDED = GMERR_BASE + 10003,

    GMERR_COMMON_STREAM_OVERLOAD = GMERR_BASE + 10004,  // for V3 compatibility

    // Program limit exceeded
    GMERR_PROGRAM_LIMIT_EXCEEDED = GMERR_BASE + 11000,

    GMERR_TOO_MANY_ARGUMENTS = GMERR_BASE + 11001,

    GMERR_BATCH_BUFFER_FULL = GMERR_BASE + 11002,  // for V3 compatibility

    GMERR_RECORD_COUNT_LIMIT_EXCEEDED = GMERR_BASE + 11003,  // for V3 compatibility

    GMERR_TABLE_LIMIT_EXCEEDED = GMERR_BASE + 11004,

    GMERR_PROCESS_LIMIT_EXCEEDED = GMERR_BASE + 11005,

    // Object not in prerequisite state
    GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE = GMERR_BASE + 12000,

    GMERR_LOCK_NOT_AVAILABLE = GMERR_BASE + 12002,

    GMERR_TABLE_IN_CHECKING = GMERR_BASE + 12003,  // for V3 compatibility

    GMERR_TABLE_NOT_IN_CHECKING = GMERR_BASE + 12004,  // for V3 compatibility

    GMERR_REPLACE_CONFLICT_IN_CHECKING = GMERR_BASE + 12005,

    GMERR_BUFFERPOOL_ALREADY_RESIZING = GMERR_BASE + 12006,

    // System error
    GMERR_FILE_OPERATE_FAILED = GMERR_BASE + 13000,

    GMERR_DIRECTORY_OPERATE_FAILED = GMERR_BASE + 13001,

    GMERR_GET_PATH_FAILED = GMERR_BASE + 13002,

    GMERR_GET_CPU_INFO_FAILED = GMERR_BASE + 13003,

    GMERR_EPOLL_OPERATE_FAILED = GMERR_BASE + 13004,

    GMERR_FILE_LOCK_ERROR = GMERR_BASE + 13005,

    GMERR_FILE_LOCK_BUSY = GMERR_BASE + 13006,

    GMERR_CRC_CHECK_FAILED = GMERR_BASE + 13007,

    GMERR_CRC_CHECK_DISABLED = GMERR_BASE + 13008,

    GMERR_PROCESS_ABORT = GMERR_BASE + 13009,

    GMERR_BYTES_ORDER_MISMATCH = GMERR_BASE + 13010,  // for V1 compatibility

    // Config file error
    GMERR_CONFIG_ERROR = GMERR_BASE + 14000,

    // Internal error
    GMERR_INTERNAL_ERROR = GMERR_BASE + 15000,

    GMERR_DATA_CORRUPTED = GMERR_BASE + 15001,

    GMERR_UNEXPECTED_NULL_VALUE = GMERR_BASE + 15002,

    GMERR_INVALID_BUFFER = GMERR_BASE + 15003,

    GMERR_MEMORY_OPERATE_FAILED = GMERR_BASE + 15004,

    GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD = GMERR_BASE + 15005,

    GMERR_FILEMAP_RECONFIGURE_ERROR = GMERR_BASE + 15006,

    GMERR_FILEMAP_READ_CONFIG_ERROR = GMERR_BASE + 15007,

    // Retry error
    GMERR_CONNECTION_TIMED_OUT = GMERR_BASE + 16000,

    GMERR_CONNECTION_RESET_BY_PEER = GMERR_BASE + 16001,

    GMERR_CONNECTION_SEND_BUFFER_FULL = GMERR_BASE + 16002,

    GMERR_BATCH_CMD_SEND_BUFF_OVERFLOW = GMERR_BASE + 16003,  // for V3 compatibility

    GMERR_REQUEST_TIME_OUT = GMERR_BASE + 16004,

    GMERR_SUB_PUSH_QUEUE_FULL = GMERR_BASE + 16006,  // for V3 compatibility

    // Load third party library failed
    GMERR_LOAD_THIRD_PARTY_LIBRARY_FAILED = GMERR_BASE + 17000,

    GMERR_GET_THIRD_PARTY_FUNCTION_FAILED = GMERR_BASE + 17001,

    GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED = GMERR_BASE + 17002,

    // priv error
    GMERR_INVALID_GRANTOR = GMERR_BASE + 18000,

    GMERR_INVALID_GRANT_OPERATION = GMERR_BASE + 18001,

    GMERR_PRIVILEGE_NOT_GRANTED = GMERR_BASE + 18002,

    GMERR_PRIVILEGE_NOT_REVOKED = GMERR_BASE + 18003,

    GMERR_INSUFFICIENT_PRIVILEGE = GMERR_BASE + 18004,

    // 数通场景，持久化外部错误码会暴露
    GMERR_DISK_NO_SPACE_ERROR = GMERR_BASE + 19000,

    GMERR_DIR_OPERATE_FAILED = GMERR_BASE + 19001,

    GMERR_FLUSH_STATE_ERROR = GMERR_BASE + 19002,

    GMERR_DATA_CORRUPTION = GMERR_BASE + 19003,

    GMERR_DATABASE_NOT_AVAILABLE = GMERR_BASE + 19004,

    GMERR_PERMISSION_DENIED = GMERR_BASE + 20000,

    GMERR_MODEL_NOT_SUPPORT = GMERR_BASE + 21001,

    GMERR_SCAN_AT_END = GMERR_BASE + 24000,

    GMERR_REBUILD_DATABASE = GMERR_BASE + 25000,

    GMERR_FILE_NO_SPACE_ERROR = GMERR_BASE + 26000,

    GMERR_FATAL = GMERR_BASE + 27000,

    // 分布式场景
    GMERR_SEQUENCE_INVALID_CONTENT = GMERR_BASE + 29000,

    GMERR_SEQUENCE_CONTENT_TOO_LONG = GMERR_BASE + 29001,

    GMERR_SEQUENCE_INVALID_INDEX = GMERR_BASE + 29002,

    GMERR_SEQUENCE_INVALID_LENGTH = GMERR_BASE + 29003,

    GMERR_DUPLICATE_SHARED_OBJ = GMERR_BASE + 29004,

    GMERR_UNDEFINED_SHARED_OBJ = GMERR_BASE + 29005,

    GMERR_SEQUENCE_INVALID_EMBED = GMERR_BASE + 29006,

    GMERR_SEQUENCE_EMBED_TYPE_LENGTH_INVALID = GMERR_BASE + 29007,

    GMERR_SEQUENCE_EMBED_PATH_LENGTH_INVALID = GMERR_BASE + 29008,

    GMERR_SEQUENCE_EMBED_ATTRIBUTE_NOT_SUPPORT = GMERR_BASE + 29009,

    GMERR_SEQUENCE_DELETION_APPLY_INCOMPLETE = GMERR_BASE + 29010,

    GMERR_DB_INSTANCE_ABNORAML = GMERR_BASE + 29011,

    GMERR_EQUIP_ID_CONFLICT = GMERR_BASE + 29012,

    GMERR_INVALID_EQUIP_ID = GMERR_BASE + 29013,

    GMERR_EQUIP_ID_NOT_REGISTERED = GMERR_BASE + 29014,

    GMERR_SYNC_PREREQUISITES_ABNORMAL = GMERR_BASE + 29015,

    GMERR_SYNC_INVALID_ARGS = GMERR_BASE + 29016,

    GMERR_SYNC_EXCEED_TASK_QUEUE_LIMIT = GMERR_BASE + 29017,

    GMERR_SUBSCRIPTION_LIMIT_EXCEEDED = GMERR_BASE + 29018,

    GMERR_SUBSCRIPTION_CONFLICT = GMERR_BASE + 29019,

    GMERR_SUBSCRIPTION_NOT_EXIST = GMERR_BASE + 29020,

    GMERR_OP_HISTORY_EXCEED_LIMIT = GMERR_BASE + 29021,

    GMERR_OP_HISTORY_MANAGER_CONFLICT = GMERR_BASE + 29022,

    GMERR_OP_HISTORY_MANAGER_NOT_EXIST = GMERR_BASE + 29023,

    GMERR_OP_HISTORY_UNDO_NOT_AVAILABLE = GMERR_BASE + 29024,

    GMERR_OP_HISTORY_REDO_NOT_AVAILABLE = GMERR_BASE + 29025,

    GMERR_OP_HISTORY_UNDO_INVALID = GMERR_BASE + 29026,

    GMERR_OP_HISTORY_REDO_INVALID = GMERR_BASE + 29027,

    GMERR_FIELD_TYPE_NOT_MATCH = GMERR_BASE + 29028,

    GMERR_FRAGMENT_ITEM_NOT_SUPPORT = GMERR_BASE + 29029,

    GMERR_FRAGMENT_INVALID_INDEX = GMERR_BASE + 29030,

    GMERR_FRAGMENT_INVALID_LENGTH = GMERR_BASE + 29031,

    GMERR_FRAGMENT_EMPTY = GMERR_BASE + 29032,

    GMERR_SCHEMA_CHANGED = GMERR_BASE + 29033,

    // LPAS_Mem
    GMERR_LPASMEM_WRONG = GMERR_BASE + 30000,
    // Bottom
    GMERR_MAX_VALUE,
};

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* GMC_ERRNO_H */
