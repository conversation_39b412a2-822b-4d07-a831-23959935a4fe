/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * File Name: gmc_sysview.h
 * Description: File include sysview api
 * Create: 2021-07-22
 */

#ifndef GMC_SYSVIEW_H
#define GMC_SYSVIEW_H

#include "gmc_errno.h"
#include "gmc_types.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @defgroup gmc_sysview gmsysview接口
 */

/**
 * @ingroup gmc_sysview
 * @par 描述：
 * 提供视图查询接口，并通过printFunc函数自定义打印格式。
 * @attention @li GmcSysview接口只供V8使用\n
 *  @li GmcSysview接口不支持多线程并发调用\n
 *  @li GmcSysview接口需要手动初始化环境
 * @param[in] argc 字符串数组个数。当argc为1时，默认进入交互式界面。
 * @param[in] argv 由字符串数组组成的一串命令，需要确保该字符串以'\0'结尾，否则可能会导致客户端所在进程异常退出。\n
 *  GmcSysview内部从第二个入参开始解析，第一个入参默认为进程名，DB内部不做任何处理。\n
 *  例如：\n
 *  {\n
 *      char gmsysview[] = "gmsysview";\n
 *      char q[] = "-q";\n
 *      char view_name[] = "V$DRT_CONN_STAT";\n
 *      char s[] = "-s";\n
 *      char server[] = "channel:ctl_channel";\n
 *      int argc = 5;\n
 *      char *argv[5] = {gmsysview, q, view_name, s, server};\n
 *      int32_t ret = GmcSysview(argc, argv, TlsStPrintf);\n
 *      EXPECT_EQ(GMERR_OK, ret);\n
 *  }\n
 *  内部忽略第一个入参"gmsysview"，由第二个入参"-q"开始解析至"channel:ctl_channel"。
 * @param[in] printFunc 自定义打印函数，用于处理打印的格式。该函数是按行处理视图结果，传NULL是和工具一样打印。
 * @return @li GMERR_OK @li 其他返回值，请参见“错误码参考”章节。
 */
GMC_EXPORT int32_t GmcSysview(int32_t argc, char **argv, int32_t(printFunc)(const char *format, ...));

#ifdef __cplusplus
}
#endif

#endif
