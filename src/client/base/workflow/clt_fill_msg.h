/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * Description: functions that use proto to fill msg into buffer
 * Author: WANG Ning
 * Create: 2021-04-09
 */

#ifndef CLT_FILL_MSG_H
#define CLT_FILL_MSG_H

#include "clt_proto.h"

#ifdef __cplusplus
extern "C" {
#endif

Status RpcReserveMsgOpHeader(FixBufferT *req);

static void SetMsgModelType(MsgOpcodeRpcE opCode, uint8_t *modelType)
{
    // 性能关键路径，遇到DML操作直接走FastPath不进行DCL及switch判断后再赋值
    if (SECUREC_LIKELY(opCode >= MSG_OP_RPC_DML_BEGIN && opCode < MSG_OP_RPC_DML_END)) {
        *modelType = MODEL_FASTPATH;
        return;
    }

    if ((opCode < MSG_OP_RPC_DCL_END && opCode >= MSG_OP_RPC_DCL_BEGIN) || opCode == MSG_OP_RPC_GET_VERTEX_LABEL ||
        opCode == MSG_OP_RPC_CREATE_USER) {
        *modelType = MODEL_PUBLIC;
        return;
    }

    // 流计算的modelType设置
    if (opCode >= MSG_OP_RPC_STREAM_BEGIN && opCode < MSG_OP_RPC_STREAM_END) {
        *modelType = MODEL_STREAM;
        return;
    }

    switch (opCode) {
        case MSG_OP_RPC_IMPORT_DATALOG:
        case MSG_OP_RPC_UPGRADE_DATALOG:
        case MSG_OP_RPC_ROLLBACK_DATALOG:
        case MSG_OP_RPC_UNINSTALL_DATALOG:
        case MSG_OP_RPC_DATALOG_SUB_RESPONSE:
        case MSG_OP_RPC_DATALOG_SUB_BATCH_RESPONSE:
            // 打桩：fastPath
            *modelType = MODEL_DATALOG;
            break;
        default:
            *modelType = MODEL_FASTPATH;
    }
}

/**
 * reset buffer, reserve rpc header and fill fields
 * inlined to improve performance
 */
inline static Status CltFillMsgHeader(FixBufferT *buffer, MsgOpcodeRpcE opCode, uint16_t stmtId)
{
    Status ret = RpcReserveMsgOpHeader(buffer);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_AND_SET_LASERR(ret, "reserve rpc header");
        return ret;
    }

    MsgHeaderT *msg = RpcPeekMsgHeader(buffer);
    OpHeaderT *op = ProtocolPeekFirstOpHeader(buffer);

    msg->stmtId = stmtId;
    msg->serviceId = DRT_SERVICE_STMT;
    SetMsgModelType(opCode, &msg->modelType);
    op->opCode = (uint32_t)opCode;

    return GMERR_OK;
}

/**
 * fill datalog error code
 */
inline static Status CltFillDtlSubErrorCode(FixBufferT *buffer, int64_t dtlErrorCode)
{
    return FixBufPutInt64(buffer, dtlErrorCode);
}

/**
 * fill msg body into buf, we assume that the space of header has been reserved outside
 * @param buf: the place we fill msg body into
 * @param proto: the structure that holds data used to make up a msg
 */
Status CltFillMsgBody(FixBufferT *buf, const ProtoHeadT *proto);

/**
 * fill buffer to form a complete rpc message
 */
inline static Status CltFillMsg(FixBufferT *buffer, uint16_t stmtId, const ProtoHeadT *proto)
{
    Status ret = CltFillMsgHeader(buffer, proto->opCode, stmtId);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    return CltFillMsgBody(buffer, proto);
}

inline static Status FillSchemaInfo(FixBufferT *buf, uint32_t version, uint32_t uuid)
{
    // fill vertexLabelSchemaVersion
    Status ret = FixBufPutUint32(buf, version);
    if (ret != GMERR_OK) {
        return ret;
    }
    // fill uuid
    if ((ret = FixBufPutUint32(buf, uuid)) != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

inline static void CltSetDataServiceHeaderInfo(MsgHeaderT *header, MsgOpcodeRpcE opCode)
{
    // 当前该函数仅支持datalog的单条插入数据进行修改serviceId，后续可根据需要进行扩展
    if (opCode == MSG_OP_RPC_BATCH || opCode == MSG_OP_RPC_INSERT_VERTEX || opCode == MSG_OP_RPC_DELETE_VERTEX ||
        opCode == MSG_OP_RPC_UPDATE_VERTEX) {
        header->serviceId = DRT_SERVICE_STMT;
        header->modelType = MODEL_DATALOG;
    }
}

#ifdef FEATURE_GQL
inline static void CltSetDataServiceHeaderInfoWithModelType(
    MsgHeaderT *header, MsgOpcodeRpcE opCode, ModelTypeE modelType)
{
    // 当前该函数仅支持datalog的单条插入数据进行修改serviceId，后续可根据需要进行扩展
    if (opCode == MSG_OP_RPC_BATCH || opCode == MSG_OP_RPC_INSERT_VERTEX || opCode == MSG_OP_RPC_DELETE_VERTEX ||
        opCode == MSG_OP_RPC_UPDATE_VERTEX) {
        header->serviceId = DRT_SERVICE_STMT;
        header->modelType = MODEL_DATALOG;
    }
    if (modelType == MODEL_GQL) {
        header->modelType = modelType;
    }
}
#endif  // FEATURE_GQL

// 临时暴露给send_protocol以减少无用代码，移除send_protocol后删除
Status HelpFillIndexKey(FixBufferT *buf, const DmIndexKeyT *indexKey);
#ifdef FEATURE_GQL
Status HelpFillStructFilterList(FixBufferT *buf, const StructFilterListT *structFilterList);
Status HelpFillStructFilterListByVertex(FixBufferT *buf, const CltOperVertexT *op);
#endif  // FEATURE_GQL
Status HelpFillVertex(FixBufferT *buf, DmVertexT *vertex);
Status HelpFillPosInfo(const DmIndexKeyT *uniqFilter, DmListPosE pos, FixBufferT *fixBuffer);
Status HelpFillIndexKeyWithSubBuf(FixBufferT *buf, const DmIndexKeyT *indexKey, GmcDlrDataBufT *dataBufT);
Status HelpFillString(FixBufferT *buf, bool handleNull, const char *str);
Status HelpFillMultiString(FixBufferT *buf, bool handleNull, uint32_t strNum, ...);
Status HelpFillSimpleObject(FixBufferT *buf, const void *obj, uint32_t objSize);
Status HelpFillEdge(FixBufferT *buf, const DmEdgeT *edge);
Status HelpPutDmValue(FixBufferT *buf, const DmValueT *value);

#ifdef __cplusplus
}
#endif

#endif /* CLT_FILL_MSG_H */
