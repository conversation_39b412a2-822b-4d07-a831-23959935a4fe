/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * Description: define structures that used in client module
 * Author: WANG Ning
 * Create: 2021-04-09
 */

#ifndef CLT_TYPES_H
#define CLT_TYPES_H

#include "dm_data_tree.h"
#include "dm_data_prop.h"
#include "dm_yang_common.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    const char *name;
    uint32_t nameLen;
    GmcOrderDirectionE order;
} CltOrderByParamT;

// 游标信息
typedef struct {
    uint32_t index;  // 游标过滤树的索引，表示过滤到第几棵过滤树
} CltCursorT;

typedef struct GmcNodeT {
    uint32_t magic;
    DmVertexT *dmVertex;  // if != NULL, will use dmVertex instead of dmNode
    void *vertexLabel;
    DmNodeT *dmNode;
    DmSubtreeT *tree;  // for subtree filter
    DmIndexKeyT *key;  // if != NULL, will use key instead of index
    uint32_t index;
    enum {
        DELTA_UPDATE_NODE,  // is update or merge operation
        READ_ONLY_NODE,     // for V3 compatibility interface (LookupNodeWithoutKeyBuf)
        READ_WRITE_NODE,    // for V3 compatibility interface (LookupNodeWithoutKeyBuf)
    } type;
    GmcOperationTypeE operationType;
    GmcStmtT *stmt;
    DbMemCtxT *memCtx;  // 其父为stmt->opCtx，没有单独清理，生命周期同stmt->opCtx，在每次reset
                        // stmt的时候都会进行reset操作
    TagLinkedListT childListNode;  // 用来记录基于根节点申请的子节点
    bool isSupportFree;            // 标记是否支持Free
} GmcNodeT;

typedef struct CltNeighborInfo {
    const char *labelName;
    const char *outputFormat;
} CltNeighborInfoT;

typedef struct GmcIndexKeyT {
    DbMemCtxT *memCtx;  // 其父为node->memCtx，没有进行单独的清理，生命周期同stmt->opCtx
    DmVlIndexLabelT *indexLabel;
    DmValueT indexValue[];
} GmcIndexKeyT;

#ifdef __cplusplus
}
#endif

#endif /* CLT_TYPES_H */
