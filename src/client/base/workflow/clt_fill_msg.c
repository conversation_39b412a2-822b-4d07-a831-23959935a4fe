/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * Description: functions that use proto to fill msg into buffer
 * Author: WANG Ning
 * Create: 2021-04-09
 */

#include "clt_fill_msg.h"
#include "db_secure_msg_buffer.h"
#include "clt_yang_common.h"
#include "clt_stmt.h"
#include "dm_yang_interface.h"
#include "clt_msg.h"

/**
 * create edge label / kv table msg
 *      RpcMsgHeader | labelJsonLen | labelJson | cfgJsonLen | cfgJson
 */
static Status FillCreateLabelMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    const CreateLabelProtoT *proto = (const CreateLabelProtoT *)protoObj;
    const uint32_t strNum = 2;
    return HelpFillMultiString(buf, true, strNum, proto->labelJson, proto->configJson);
}

/**
 * create vertex label msg
 *      RpcMsgHeader | labelJsonLen | labelJson | cfgJsonLen | cfgJson | labelNameLen | LabelName
 */
static Status FillCreateVertexMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    const CreateLabelProtoT *proto = (const CreateLabelProtoT *)protoObj;
    const uint32_t strNum = 3;
    return HelpFillMultiString(buf, true, strNum, proto->labelJson, proto->configJson, proto->labelName);
}

static Status FillSetScheduleTaskCfgMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    FillPublicStmtMsgHeader(buf, protoObj->opCode);
    const ScheduleTaskProtoT *proto = (const ScheduleTaskProtoT *)protoObj;
    Status ret = FixBufPutUint32(buf, proto->workload);
    if (ret != GMERR_OK) {
        return ret;
    }
    return FixBufPutUint32(buf, proto->duration);
}

static Status FillSetUserRequestWeightMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    FillPublicStmtMsgHeader(buf, protoObj->opCode);
    const UserRequestWeightProtoT *proto = (const UserRequestWeightProtoT *)protoObj;
    const uint32_t strNum = 2;
    Status ret = FixBufPutUint32(buf, proto->weight);
    if (ret != GMERR_OK) {
        return ret;
    }
    return HelpFillMultiString(buf, true, strNum, proto->userName, proto->processName);
}

/**
 * set log level or log switch msg
 *      RpcMsgHeader | startCycle | duration | logCtrlVal | processName
 * logCtrlVal can be used as log level or log switch depend on opCode
 */
static Status FillSetLogCtrlMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    FillPublicStmtMsgHeader(buf, protoObj->opCode);
    const LogCtrlProtoT *proto = (const LogCtrlProtoT *)(const void *)protoObj;
    Status ret = SecureFixBufPutUint64(buf, proto->startCycle);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = SecureFixBufPutUint32(buf, proto->duration);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = SecureFixBufPutUint32(buf, proto->logCtrlVal);
    if (ret != GMERR_OK) {
        return ret;
    }
    return HelpFillString(buf, true, proto->processName);
}

/**
 * drop/truncate label msg
 *      RpcMsgHeader | labelNameLen | labelName (| schemaVersion | isCascade)
 * only drop vertex label contains 'isCascade'
 */
static Status FillDropTruncateLabelMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    const DropTruncateLabelProtoT *proto = (const DropTruncateLabelProtoT *)protoObj;
    // fill label name with len
    Status ret = HelpFillString(buf, true, proto->labelName);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (proto->protoHead.opCode == MSG_OP_RPC_DEGRADE_VERTEX_LABEL) {
        // fill schemaVersion
        ret = FixBufPutUint32(buf, proto->schemaVersion);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    if (proto->protoHead.opCode == MSG_OP_RPC_DROP_VERTEX_LABEL) {
        // fill isCascade
        ret = FixBufPutUint32(buf, (uint32_t)proto->isCascade);
    }
    return ret;
}

/**
 * create subscription msg
 *      RpcMsgHeader | subInfoJsonLen | subInfoJson | subConnNameLen | subConnName
 */
static Status FillCreateSubMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    const CreateSubProtoT *proto = (const CreateSubProtoT *)protoObj;
    const uint32_t strNum = 3;
    return HelpFillMultiString(buf, true, strNum, proto->subsName, proto->configJson, proto->channelName);
}

/**
 * drop subscription msg
 *      RpcMsgHeader | subNameLen | subName
 */
static Status FillDropSubMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    const DropSubProtoT *proto = (const DropSubProtoT *)protoObj;
    return HelpFillString(buf, true, proto->subName);
}

/**
 * trigger subscription full scan msg
 *      RpcMsgHeader | subNameLen | subName
 */
static Status FillTriggerSubScanMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    const TriggerSubScanProtoT *proto = (const void *)protoObj;
    return HelpFillString(buf, true, proto->subName);
}

/**
 * create/drop/use namespace msg
 *      RpcMsgHeader | namespaceNameLen | namespaceName (| userNameLen | userName) | isolationLevel |
 * trxTyp
 */
static Status FillNamespaceMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    const NamespaceProtoT *proto = (const NamespaceProtoT *)protoObj;
    const uint32_t strNum = 2;
    Status ret = HelpFillMultiString(buf, false, strNum, proto->namespaceName, proto->userName);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = HelpFillString(buf, true, proto->tablespaceName);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = FixBufPutUint32(buf, (uint32_t)proto->trxCfg.isolationLevel);
    if (ret != GMERR_OK) {
        return ret;
    }
    return FixBufPutUint32(buf, (uint32_t)proto->trxCfg.trxType);
}

/**
 * create/drop/use tablespace msg
 *      RpcMsgHeader | tablespaceNameLen | tablespaceName | initSize | stepSize | maxSize
 */
static Status FillTablespaceMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    const TablespaceProtoT *proto = (const TablespaceProtoT *)protoObj;
    Status ret = HelpFillString(buf, true, proto->tablespaceName);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = SecureFixBufPutUint16(buf, proto->initSize);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = SecureFixBufPutUint16(buf, proto->stepSize);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = SecureFixBufPutUint16(buf, proto->maxSize);
    if (ret != GMERR_OK) {
        return ret;
    }
    return SecureFixBufPutUint16(buf, proto->isUseRsm);
}

#ifdef FEATURE_RESPOOL_CLI
/**
 * create/drop/get resource pool msg
 * create resource pool:
 *      RpcMsgHeader | resPoolJsonLen | resPoolJson
 * drop/get resource pool:
 *      RpcMsgHeader | resPoolNameLen | resPoolName
 */
static Status FillResPoolMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    const ResPoolProtoT *proto = (const ResPoolProtoT *)protoObj;
    return HelpFillString(buf, true, proto->content);
}

/**
 * bind/unbind resource pool to vertexlabel/extended resource pool msg
 * bind to vertexlabel:
 *      RpcMsgHeader | resPoolNameLen | resPoolName | vertexLabelNameLen | vertexLabelName
 * unbind from vertexlabel:
 *      RpcMsgHeader | vertexLabelNameLen | vertexLabelName
 * bind to extended resource pool:
 *      RpcMsgHeader | resPoolNameLen | resPoolName | extendedPoolNameLen | extendedPoolName
 * unbind from resource pool:
 *      RpcMsgHeader | resPoolNameLen | resPoolName
 */
static Status FillBindResPoolMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    const BindResPoolProtoT *proto = (const BindResPoolProtoT *)protoObj;
    const uint32_t strNum = 3;
    return HelpFillMultiString(buf, false, strNum, proto->resPoolName, proto->bindName, proto->extraName);
}
#else
static Status FillResPoolMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    return GMERR_FEATURE_NOT_SUPPORTED;
}

static Status FillBindResPoolMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    return GMERR_FEATURE_NOT_SUPPORTED;
}
#endif

/**
 * insert/replace/merge vertex msg
 *      RpcMsgHeader | vertexLabelId | schemaVersion | vertexNum = 1 | vertexPosition | positionKey
 *      vertexLen | vertex | operateEdgeFlag | isReplay
 */
static Status FillInsertVertexMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    Status ret;
    const InsertVertexProtoT *proto = (const InsertVertexProtoT *)protoObj;
    DmVertexLabelT *label = proto->cltCataLabel->vertexLabel;

    // refresh header info for datalog tables
    if (DmVertexLabelIsDatalogLabel(label)) {
        MsgHeaderT *header = RpcPeekMsgHeader(buf);
        CltSetDataServiceHeaderInfo(header, proto->protoHead.opCode);
    }

    // fill vertexLabelId
    if ((ret = FixBufPutUint32(buf, label->metaCommon.metaId)) != GMERR_OK) {
        return ret;
    }
    // fill vertexLabelSchemaVersion
    if ((ret = FixBufPutUint32(buf, label->metaCommon.version)) != GMERR_OK) {
        return ret;
    }
    // fill uuid
    if ((ret = FixBufPutUint32(buf, label->metaVertexLabel->uuid)) != GMERR_OK) {
        return ret;
    }
    // fill vertexNum
    if ((ret = FixBufPutUint32(buf, 1)) != GMERR_OK) {
        return ret;
    }
    if (DmIsListVertexLabel(label)) {
        if ((ret = HelpFillPosInfo(proto->posKey, proto->vertex->position, buf)) != GMERR_OK) {
            DB_LOG_ERROR(ret, "fill pos key, vertexName=%s", label->metaCommon.metaName);
            DmDestroyIndexKey(proto->posKey);
            return ret;
        }
        DmDestroyIndexKey(proto->posKey);
    }
    // fill vertex with len
    if ((ret = HelpFillVertex(buf, proto->vertex)) != GMERR_OK) {
        return ret;
    }
    // fill operateEdgeFlag
    if ((ret = FixBufPutUint32(buf, proto->operateEdgeFlag)) != GMERR_OK) {
        return ret;
    }
    // fill isReplay
    VertexLabelCommonInfoT *commonInfo = label->commonInfo;
    if (SECUREC_UNLIKELY(commonInfo == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "fill insert vertex msg for null vertexlabel commonInfo.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    if (commonInfo->dlrInfo.isDataSyncLabel) {
        // fill isReplay, 常规操作，不支持DLR
        return FixBufPutUint32(buf, 0);
    }
    return GMERR_OK;
}

inline static Status FillCommonInfoisDataSyncLabel(const DmVertexLabelT *label, FixBufferT *buf)
{
    VertexLabelCommonInfoT *commonInfo = label->commonInfo;
    if (SECUREC_UNLIKELY(commonInfo == NULL)) {
        DB_SET_LASTERR(GMERR_INTERNAL_ERROR, "get null commonInfo.");
        return GMERR_INTERNAL_ERROR;
    }
    if (SECUREC_UNLIKELY(commonInfo->dlrInfo.isDataSyncLabel)) {
        // fill isReplay, 常规操作，不支持DLR
        return FixBufPutUint32(buf, 0);
    }
    return GMERR_OK;
}

/**
 * update vertex msg
 *      RpcMsgHeader | vertexLabelId | schemaVersion | indexKeyId | condStr |
 *      updateNum = 1 |  uniqueIndexKey | vertex | operateEdgeFlag
 */
static Status FillUpdateVertexMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    const UpdateVertexProtoT *proto = (const UpdateVertexProtoT *)protoObj;
    const DmVertexLabelT *label = proto->cltCataLabel->vertexLabel;
    // fill vertexLabelId vertexLabelSchemaVersion uuid indexkeyindex
    uint32_t keyIndex = proto->uniqueIndexKey == NULL ? DB_MAX_UINT32 : proto->uniqueIndexKey->indexId;
    struct UpdateMeta {
        uint32_t metaId;
        uint32_t version;
        uint32_t uuid;
        uint32_t keyIndex;
    };
    struct UpdateMeta *updateMeta = FixBufReserveData(buf, sizeof(struct UpdateMeta));
    if (SECUREC_UNLIKELY(updateMeta == NULL)) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    *updateMeta = (struct UpdateMeta){
        .metaId = label->metaCommon.metaId,
        .version = label->metaCommon.version,
        .uuid = label->metaVertexLabel->uuid,
        .keyIndex = keyIndex,
    };
    Status ret;
    // fill condStr with len
    if (SECUREC_UNLIKELY((ret = HelpFillString(buf, true, proto->condStr)) != GMERR_OK)) {
        return ret;
    }
    // fill update number
    ret = FixBufPutUint32(buf, 1);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    // fill uniqueIndexKey with len
    if (SECUREC_UNLIKELY((ret = HelpFillIndexKey(buf, proto->uniqueIndexKey)) != GMERR_OK)) {
        return ret;
    }
    // fill vertex with len
    if (SECUREC_UNLIKELY((ret = HelpFillVertex(buf, proto->vertex)) != GMERR_OK)) {
        return ret;
    }
    // fill operateEdgeFlag
    if (SECUREC_UNLIKELY((ret = FixBufPutUint32(buf, proto->operateEdgeFlag)) != GMERR_OK)) {
        return ret;
    }
    if (SECUREC_UNLIKELY((ret = FillCommonInfoisDataSyncLabel(label, buf)) != GMERR_OK)) {
        return ret;
    }
    if (SECUREC_UNLIKELY(DmVertexLabelIsDatalogLabel(label))) {
        MsgHeaderT *header = RpcPeekMsgHeader(buf);
        CltSetDataServiceHeaderInfo(header, proto->protoHead.opCode);
    }
    return GMERR_OK;
}

static inline Status FillDeleteIndexKeyIndexId(FixBufferT *buf, const DeleteVertexProtoT *proto)
{
    if (proto->leftIndexKey != NULL) {
        return FixBufPutUint32(buf, proto->leftIndexKey->indexId);
    } else if (proto->rightIndexKey != NULL) {
        return FixBufPutUint32(buf, proto->rightIndexKey->indexId);
    } else {
        return FixBufPutUint32(buf, DB_MAX_UINT32);
    }
    return GMERR_OK;
}

/**
 * delete vertex msg
 *      RpcMsgHeader | vertexLabelId | schemaVersion | indexKeyId | condStr |
 *      deleteNum = 1 | rangeScanflag | leftIndexKey | rightIndexKey | operateEdgeFlag
 */
static Status FillDeleteVertexMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    const DeleteVertexProtoT *proto = (const DeleteVertexProtoT *)protoObj;
    const DmVertexLabelT *label = proto->vertexLabel;
    // fill vertexLabelId
    Status ret = FixBufPutUint32(buf, label->metaCommon.metaId);
    if (ret != GMERR_OK) {
        return ret;
    }
    if ((ret = FillSchemaInfo(buf, label->metaCommon.version, label->metaVertexLabel->uuid)) != GMERR_OK) {
        return ret;
    }
    // fill indexkey index
    ret = FillDeleteIndexKeyIndexId(buf, proto);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    // fill condStr with len
    if ((ret = HelpFillString(buf, true, proto->condString)) != GMERR_OK) {
        return ret;
    }
    // fill delete Num 初次填报文肯定是1，这里不用记录
    if ((ret = FixBufPutUint32(buf, 1)) != GMERR_OK) {
        return ret;
    }
    // fill rangeScanFlag
    if ((ret = FixBufPutUint32(buf, proto->rangeScanFlag)) != GMERR_OK) {
        return ret;
    }
    // fill leftIndexKey with len
    if ((ret = HelpFillIndexKey(buf, proto->leftIndexKey)) != GMERR_OK) {
        return ret;
    }
    // fill rightIndexKey with len
    if ((ret = HelpFillIndexKey(buf, proto->rightIndexKey)) != GMERR_OK) {
        return ret;
    }
#ifdef FEATURE_GQL
    // fill struct filter, not support in request not batch
    if ((ret = FixBufPutUint32(buf, 0)) != GMERR_OK) {
        return ret;
    }
#endif  // FEATURE_GQL
    // fill operateEdgeFlag
    if ((ret = FixBufPutUint32(buf, proto->operateEdgeFlag)) != GMERR_OK) {
        return ret;
    }
    ret = FillCommonInfoisDataSyncLabel(label, buf);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (SECUREC_UNLIKELY(DmVertexLabelIsDatalogLabel(label))) {
        MsgHeaderT *header = RpcPeekMsgHeader(buf);
        CltSetDataServiceHeaderInfo(header, proto->protoHead.opCode);
    }
    return GMERR_OK;
}

/**
 * insert/delete edge msg
 *      RpcMsgHeader | edgeLabelNameLen | edgeLabelName | edgeLen | edge
 */
static Status FillEdgeMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    const EdgeProtoT *proto = (const EdgeProtoT *)protoObj;
    DmEdgeLabelT *label = proto->edgeLabel;
    // fill edgeLabelName with len
    Status ret = HelpFillString(buf, true, MEMBER_PTR(&label->metaCommon, metaName));
    if (ret != GMERR_OK) {
        return ret;
    }
    // fill edge with len
    return HelpFillEdge(buf, proto->edge);
}

#ifdef FEATURE_KV
/**
 * set kv msg
 *      RpcMsgHeader | kvTableId | keyLen | key | valueLen | value
 */
static Status FillSetKvMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    const KvProtoT *proto = (const KvProtoT *)protoObj;
    // fill kvTableId
    Status ret = FixBufPutUint32(buf, proto->kvTableId);
    if (ret != GMERR_OK) {
        return ret;
    }
    // fill key with len
    ret = SecureFixBufPutRawText(buf, proto->indexKey.keyLen, proto->indexKey.keyData);
    if (ret != GMERR_OK) {
        return ret;
    }
    // fill value with len
    ret = SecureFixBufPutRawText(buf, proto->valueLen, proto->value);
    return ret;
}

/**
 * delete/get kv, is kv exist msg
 *      RpcMsgHeader | kvTableId | keyLen | key
 */
static Status FillGetKvMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    const KvProtoT *proto = (const void *)protoObj;
    // fill kvTableId
    Status res = FixBufPutUint32(buf, proto->kvTableId);
    if (res != GMERR_OK) {
        return res;
    }
    // fill key with len
    res = SecureFixBufPutRawText(buf, proto->indexKey.keyLen, proto->indexKey.keyData);
    return res;
}

/**
 * get kv record count msg
 *      RpcMsgHeader | kvTableId
 */
static Status FillGetKvCountMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    // fill kvTableId
    const KvProtoT *proto = (const void *)protoObj;
    return FixBufPutUint32(buf, proto->kvTableId);
}
#else
static Status FillSetKvMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    return GMERR_FEATURE_NOT_SUPPORTED;
}

static Status FillGetKvMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    return GMERR_FEATURE_NOT_SUPPORTED;
}

static Status FillGetKvCountMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    return GMERR_FEATURE_NOT_SUPPORTED;
}
#endif

static Status FillObjectPrivsMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    FillPublicStmtMsgHeader(buf, protoObj->opCode);
    const ObjectPrivsProtoT *proto = (const ObjectPrivsProtoT *)protoObj;
    Status ret = FixBufPutUint32(buf, proto->objType);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = HelpFillString(buf, true, proto->userName);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = HelpFillString(buf, true, proto->processName);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = HelpFillString(buf, true, proto->objName);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (proto->objType != GMC_NAMESPACE) {
        ret = HelpFillString(buf, true, proto->nameSpaceName);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    ret = FixBufPutUint32(buf, proto->privs);
    if (ret != GMERR_OK) {
        return ret;
    }
    return SecureFixBufPutUint32(buf, (uint32_t)proto->isAtomic);
}

static Status FillAlterConfigMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    const AlterConfigProtoT *proto = (const void *)protoObj;
    const uint32_t strNum = 2;
    return HelpFillMultiString(buf, true, strNum, proto->vertexLabelName, proto->configJson);
}

/**
 * duplicate vertex label with label name msg
 *      RpcMsgHeader | originLabelNameLen | originLabelName | newLabelNameLen | newLabelName
 */
static Status FillDuplicateLabelMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    const DuplicateLabelProtoT *proto = (const void *)protoObj;
    const uint32_t strNum = 3;
    return HelpFillMultiString(buf, true, strNum, proto->originLabelName, proto->newLabelName, proto->configJson);
}

/**
 * alter vertex label with label name msg
 *      RpcMsgHeader | labelJson | labelName | isCompatible
 */
static Status FillAlterLabelMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    const AlterLabelProtoT *proto = (const AlterLabelProtoT *)protoObj;
    const uint32_t strNum = 2;
    Status ret = HelpFillMultiString(buf, true, strNum, proto->labelJson, proto->labelName);
    if (ret != GMERR_OK) {
        return ret;
    }
    return FixBufPutUint32(buf, (uint32_t)proto->isCompatible);
}

static Status FillUserBatchOperationMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    const BatchUserProtoT *proto = (const BatchUserProtoT *)protoObj;
    uint16_t userNum = proto->userNum;
    // msgheader+userNum+(userName+processName+reservedConnNum)[1~userNum]
    Status ret = SecureFixBufPutUint16(buf, userNum);
    if (ret != GMERR_OK) {
        return ret;
    }
    for (uint32_t i = 0; i < userNum; i++) {
        ret = HelpFillString(buf, true, proto->userNames[i]);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = HelpFillString(buf, true, proto->processNames[i]);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = SecureFixBufPutUint16(buf, proto->reservedConnNumArray[i]);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = SecureFixBufPutData(buf, proto->userMetadataMaxNumArray[i], USER_METADATA_FILED_NUM * sizeof(uint16_t));
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    ret = SecureFixBufPutUint32(buf, (uint32_t)proto->isAtomic);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

/**
 * savepoint msg
 *      RpcMsgHeader | savepointName
 */
static Status FillSavepointMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    FillPublicStmtMsgHeader(buf, protoObj->opCode);
    const SavepointProtoT *proto = (const SavepointProtoT *)protoObj;
    return HelpFillString(buf, true, proto->savepointName);
}

static Status FillSystemPrivsOperationMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    FillPublicStmtMsgHeader(buf, protoObj->opCode);
    const SystemPrivsProtoT *proto = (const SystemPrivsProtoT *)protoObj;
    Status ret = HelpFillString(buf, true, proto->userName);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = HelpFillString(buf, true, proto->processName);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = FixBufPutUint32(buf, (uint32_t)proto->opType);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = FixBufPutUint32(buf, (uint32_t)proto->objectType);
    if (ret != GMERR_OK) {
        return ret;
    }
    return FixBufPutUint32(buf, (uint32_t)proto->isAtomic);
}

/**
 * get alarm data msg
 *      RpcMsgHeader | alarmId
 */
static Status FillGetAlarmDataMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    const AlarmDataProtoT *proto = (const AlarmDataProtoT *)protoObj;
    // fill alarmId
    return FixBufPutUint32(buf, (uint32_t)proto->alarmId);
}

/**
 * modify alarm threshold msg
 *      RpcMsgHeader | alarmId | raiseRatio | clearRatio
 */
static Status FillModifyAlarmThresholdMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    const AlarmThresholdProtoT *proto = (const AlarmThresholdProtoT *)protoObj;
    // fill alarmId
    Status ret = FixBufPutUint32(buf, (uint32_t)proto->alarmId);
    if (ret != GMERR_OK) {
        return ret;
    }
    // fill raise and clear threshold
    ret = SecureFixBufPutUint16(buf, proto->raiseRatio);
    if (ret != GMERR_OK) {
        return ret;
    }
    return SecureFixBufPutUint16(buf, proto->clearRatio);
}

/**
 * get edgeLabel/kvTable msg
 *      RpcMsgHeader | labelId | labelNameLen (| labelName)
 */
static Status FillGetLabelMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    const GetLabelProtoT *proto = (const GetLabelProtoT *)protoObj;
    // fill labelId
    Status ret = FixBufPutUint32(buf, proto->labelId);
    if (ret != GMERR_OK) {
        return ret;
    }
    // fill labelName with len
    return HelpFillString(buf, true, proto->labelName);
}

/**
 * get vertexLabel msg
 *      RpcMsgHeader | labelId | labelNameLen (| labelName) | isView | isCsMode | versionId
 */
static Status FillGetVertexLabelMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    Status ret = FillGetLabelMsg(buf, protoObj);
    if (ret != GMERR_OK) {
        return ret;
    }
    // fill isView
    const GetLabelProtoT *proto = (const GetLabelProtoT *)protoObj;
    ret = SecureFixBufPutUint16(buf, proto->isView);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = FixBufPutUint16(buf, proto->isCsMode);
    if (ret != GMERR_OK) {
        return ret;
    }
    return FixBufPutUint32(buf, proto->versionId);
}

/**
 * get vertex count msg
 *      RpcMsgHeader | labelId | indexKeyLen (| indexKey)
 */
static Status FillGetCountMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    const GetCountProtoT *proto = (const GetCountProtoT *)protoObj;
    // fill labelName with len
    Status ret = FixBufPutUint32(buf, proto->labelId);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 获取表记录数不用
    uint32_t dumySchemaVersion = 0;
    ret = FixBufPutUint32(buf, dumySchemaVersion);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 获取表记录数不用
    uint32_t dumyUuid = 0;
    ret = FixBufPutUint32(buf, dumyUuid);
    if (ret != GMERR_OK) {
        return ret;
    }
    // fill indexKey with len
    return HelpFillIndexKey(buf, proto->indexKey);
}

/**
 * get change statistics count msg
 *      RpcMsgHeader | labelNum | labelId1 | labelNameLen1 | labelName1 | ...
 */
static Status FillGetStatCountMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    const GetStatCountProtoT *proto = (const GetStatCountProtoT *)protoObj;
    // fill labelNum
    Status ret = FixBufPutUint32(buf, proto->labelCount);
    for (uint32_t i = 0; ret == GMERR_OK && i < proto->labelCount; i++) {
        // fill labelId
        ret = FixBufPutUint32(buf, 0);
        if (ret != GMERR_OK) {
            break;
        }
        // fill labelName with len
        ret = HelpFillString(buf, true, proto->labelName[i]);
    }
    return ret;
}

static Status FillGetWarmRebootTables(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    FixBufferT *buffer = buf;
    DB_UNUSED(buffer);
    DB_UNUSED(protoObj);
    return GMERR_OK;
}

static Status FillFetchCypherMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    FixBufferT *buffer = buf;
    DB_UNUSED(buffer);
    DB_UNUSED(protoObj);
    return GMERR_OK;
}

/**
 * get vertexLabel Degrade Process
 *      RpcMsgHeader | labelNameLen (| labelName)
 */
static Status FillGetLabelDegradeProgress(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    const GetLabelDegradeProcessProtoT *proto = (const GetLabelDegradeProcessProtoT *)protoObj;
    // fill labelName
    return HelpFillString(buf, true, proto->labelName);
}

/**
 * scan vertex msg
 *      RpcMsgHeader | prepExecReq | labelNameLen | labelName | rangeScanFlag | leftKeyLen | leftKey |
 *      rightKeyLen | rightKey | condStrLen | condStr | outputFmtStrLen | outputFmtStr |
 *      orderByColNum | (orderByFlag1 | colName1Len | colName1 | orderByFlag2 | colName2Len | colName2 | ...) |
 *      limitCount
 */
static Status FillScanVertexMsgInner(FixBufferT *buf, const CsScanVertexProtoT *proto)
{
    // fill leftKey with len
    Status ret = HelpFillIndexKey(buf, proto->leftIndexKey);
    if (ret != GMERR_OK) {
        return ret;
    }
    // fill rightKey with len
    ret = HelpFillIndexKey(buf, proto->rightIndexKey);
    return ret;
}

static Status FillScanVertexMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    const CsScanVertexProtoT *proto = (const CsScanVertexProtoT *)protoObj;
    // fill prepExecReq
    Status ret = HelpFillSimpleObject(buf, &proto->prepExecReq, sizeof(DbPrepExecReqT));
    if (ret != GMERR_OK) {
        return ret;
    }
    // fill labelId
    ret = FixBufPutUint32(buf, proto->labelId);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = FillSchemaInfo(buf, proto->schemaVersion, proto->uuid);
    if (ret != GMERR_OK) {
        return ret;
    }

    // fill rangeScanFlag
    ret = FixBufPutUint32(buf, proto->rangeScanFlag);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = FillScanVertexMsgInner(buf, proto);
    if (ret != GMERR_OK) {
        return ret;
    }
    // fill isSysviewRecordCmd
    ret = FixBufPutUint32(buf, (uint32_t)proto->isSysviewRecordCmd);
    if (ret != GMERR_OK) {
        return ret;
    }
    // fill condStr & outputFmtStr with len
    const uint32_t strNum = 2;
    ret = HelpFillMultiString(buf, true, strNum, proto->condStr, proto->outputFmtStr);
    if (ret != GMERR_OK) {
        return ret;
    }
    // fill order by
    const CltOrderByParamT *params = proto->orderByParams;
    uint32_t count = proto->orderByParamCount;
    ret = FixBufPutUint32(buf, count);
    if (ret != GMERR_OK) {
        return ret;
    }
    for (uint32_t i = 0; i < count; i++) {
        // fill orderByFlag
        const CltOrderByParamT *param = &params[i];
        ret = SecureFixBufPutUint16(buf, (uint16_t)param->order);
        if (ret != GMERR_OK) {
            return ret;
        }
        // fill colName with len
        ret = SecureFixBufPutRawText(buf, param->nameLen, param->name);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    // fill limitCount
    return SecureFixBufPutUint64(buf, proto->limitCount);
}

/**
 * scan path msg
 *      RpcMsgHeader | prepExecReq | direction | srcLabelNameLen | srcLabelName | indexKeyLen | indexKey |
 *      condStrLen | condStr | neighborLabelNum |
 *      neighborLabel1NameLen | neighborLabel1Name | outputFmtStr1Len | outputFmtStr1 | ...
 */
static Status FillScanPathMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    const ScanPathProtoT *proto = (const ScanPathProtoT *)protoObj;
    // fill prepExecReq
    Status ret = HelpFillSimpleObject(buf, &proto->prepExecReq, sizeof(DbPrepExecReqT));
    if (ret != GMERR_OK) {
        return ret;
    }
    // fill direction
    ret = FixBufPutUint32(buf, proto->direction);
    if (ret != GMERR_OK) {
        return ret;
    }
    // fill srcLabelName with len
    ret = HelpFillString(buf, true, proto->srcLabelName);
    if (ret != GMERR_OK) {
        return ret;
    }
    // fill indexKey with len
    ret = HelpFillIndexKey(buf, proto->indexKey);
    if (ret != GMERR_OK) {
        return ret;
    }
    // fill condStr with len
    ret = HelpFillString(buf, true, proto->condStr);
    if (ret != GMERR_OK) {
        return ret;
    }
    // fill neighborLabelNum
    uint32_t count = proto->neighborCount;
    ret = FixBufPutUint32(buf, count);
    // fill neighbor labels' info
    const CltNeighborInfoT *neighborInfo = proto->neighborsInfo;
    for (uint32_t i = 0; ret == GMERR_OK && i < count; i++) {
        const CltNeighborInfoT *info = &neighborInfo[i];
        ret = HelpFillMultiString(buf, true, 0x2, info->labelName, info->outputFormat);
    }
    return ret;
}

#ifdef FEATURE_KV
/**
 * scan kv msg
 *      RpcMsgHeader | kvTableId | limitCount
 */
static Status FillScanKvMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    const KvProtoT *proto = (const KvProtoT *)protoObj;
    // fill prepExecReq
    Status ret = HelpFillSimpleObject(buf, &proto->prepExecReq, sizeof(DbPrepExecReqT));
    if (ret != GMERR_OK) {
        return ret;
    }
    // fill kvTableId
    ret = FixBufPutUint32(buf, proto->kvTableId);
    if (ret != GMERR_OK) {
        return ret;
    }
    // fill limitCount
    ret = SecureFixBufPutUint64(buf, proto->limitCount);
    return ret;
}
#else
static Status FillScanKvMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    return GMERR_FEATURE_NOT_SUPPORTED;
}
#endif

static Status FillBeginCheckMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    const BeginCheckProtoT *proto = (const BeginCheckProtoT *)protoObj;
    Status result = FixBufPutUint32(buf, proto->vertexLabelId);
    if (result != GMERR_OK) {
        return result;
    }
    result = FixBufPutUint32(buf, proto->partitionId);
    return result;
}

static Status FillEndCheckMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    const EndCheckProtoT *proto = (const EndCheckProtoT *)protoObj;
    Status result = FixBufPutUint32(buf, proto->vertexLabelId);
    if (result != GMERR_OK) {
        return result;
    }
    result = FixBufPutUint32(buf, proto->partitionId);
    if (result != GMERR_OK) {
        return result;
    }
    result = FixBufPutUint32(buf, proto->isAbnormal);
    return result;
}

#ifdef WARM_REBOOT
static Status FillEndAllPartitionCheck(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    const EndAllPartitionCheckProtoT *proto = (const EndAllPartitionCheckProtoT *)protoObj;
    Status result = FixBufPutUint32(buf, proto->vertexLabelId);
    if (result != GMERR_OK) {
        return result;
    }
    result = FixBufPutUint32(buf, proto->isAbnormal);
    return result;
}
#endif

static Status FillUpdateCheckVersionMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    const UpdateCheckVersionProtoT *proto = (const UpdateCheckVersionProtoT *)protoObj;
    Status result = FixBufPutUint32(buf, proto->vertexLabelId);
    if (result != GMERR_OK) {
        return result;
    }
    return HelpFillIndexKey(buf, proto->indexKey);
}

static Status FillCheckReplaceMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    const CheckReplaceProtoT *proto = (const CheckReplaceProtoT *)protoObj;
    Status result = FixBufPutUint32(buf, proto->vertexLabelId);
    if (result != GMERR_OK) {
        return result;
    }
    result = FixBufPutUint64(buf, proto->trxId);
    if (result != GMERR_OK) {
        return result;
    }
    result = FixBufPutUint32(buf, proto->keySize);
    if (result != GMERR_OK) {
        return result;
    }
    return HelpFillSimpleObject(buf, proto->keyBuf, proto->keySize);
}

static Status FillGetCheckInfoMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    const GetCheckInfoProtoT *proto = (const GetCheckInfoProtoT *)protoObj;
    Status result = FixBufPutUint32(buf, proto->vertexLabelId);
    if (result != GMERR_OK) {
        return result;
    }
    result = FixBufPutUint32(buf, proto->partitionId);
    return result;
}

static Status FillSetCfgMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    FillPublicStmtMsgHeader(buf, protoObj->opCode);
    const SetCfgProtoT *proto = (const SetCfgProtoT *)protoObj;
    Status res = HelpFillString(buf, true, proto->configName);
    if (res != GMERR_OK) {
        return res;
    }
    const DmValueT *value = proto->configValue;
    return HelpPutDmValue(buf, value);
}

static Status FillGetCfgMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    const GetCfgProtoT *proto = (const GetCfgProtoT *)protoObj;
    return HelpFillString(buf, true, proto->configName);
}

static Status FillGetLabelTypeMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    const GetLabelTypeProtoT *proto = (const GetLabelTypeProtoT *)protoObj;
    Status ret = FixBufPutUint32(buf, proto->labelId);
    if (ret != GMERR_OK) {
        return ret;
    }
#if defined(WARM_REBOOT) || defined(FEATURE_RSMEM)
    ret = HelpFillString(buf, true, proto->labelName);
    if (ret != GMERR_OK) {
        return ret;
    }
    return FixBufPutUint32(buf, (uint32_t)proto->queryExist);
#else
    return HelpFillString(buf, true, proto->labelName);
#endif
}

static Status FillGetMemSizeInfo(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    const GetMemSizeProtoT *proto = (const GetMemSizeProtoT *)protoObj;
    const uint32_t strNum = 2;
    return HelpFillMultiString(buf, true, strNum, proto->labelJson, proto->configJson);
}

static Status FillImportDatalog(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    const ImportDatalogProtoT *proto = (const ImportDatalogProtoT *)protoObj;
    Status ret = HelpFillString(buf, true, proto->filePath);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = HelpFillString(buf, true, proto->namespaceName);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = SecureFixBufPutUint32(buf, (uint32_t)proto->isDistribute);
    if (ret != GMERR_OK) {
        return ret;
    }
    return ret;
}

#ifdef FEATURE_YANG
static Status PutSingleSubtreeFilter2Buf(FixBufferT *buf, const GmcSubtreeFilterItemT *filter, uint32_t mode)
{
    Status ret = SecureFixBufPut5Uint32(
        buf, filter->jsonFlag, filter->maxDepth, filter->isLocationFilter, filter->defaultMode, filter->configFlag);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (DmSubtreeFilterIsFetchFullMode(mode)) {
        return GMERR_OK;
    }
    if (mode == (uint32_t)GMC_FETCH_MODEL || mode == (uint32_t)GMC_FETCH_MODEL_LIST) {
        uint32_t offset;
        ret = FixBufReserveDataOffset(buf, sizeof(uint32_t), &offset);
        if (ret != GMERR_OK) {
            return ret;
        }
        const uint32_t strNum = 1;
        uint32_t hasRootName = 0;
        if (filter->rootName != NULL) {
            hasRootName = 1;
            ret = HelpFillMultiString(buf, true, strNum, filter->rootName);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
        return FixBufPutReservedData(buf, offset, &hasRootName, sizeof(hasRootName));
    }
    if (mode == (uint32_t)GMC_FETCH_JSON) {
        const uint32_t strNum = 2;
        return HelpFillMultiString(buf, true, strNum, filter->rootName, filter->subtree.json);
    }
    return CltPutSubtree2Buf(filter->subtree.obj->tree, buf);
}

/**
 * subtree filter msg
 * 初次查询(obj模式): RpcMsgHeader | 0 | filterMode | filterCount | vertexLabel count |
    vertexLabel ID 1 | vertexLabel ID 2 | filterOptions1 | tree1 | filterOptions2 | tree2 ...
 * 初次查询(json模式): RpcMsgHeader | 0 | filterMode | filterCount | filterOptions1 | json1 | filterOptions2 | json2 ...
 * 非初次查询: RpcMsgHeader | cursor
 */
static Status FillSubtreeFilterMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    Status ret = GMERR_OK;
    const SubtreeFilterProtoT *proto = (const SubtreeFilterProtoT *)(const void *)protoObj;
    // 传入了cursor，说明不是第一次查询，而是分批查询中的下一次查询，此时，只需要发送cursor，不用再发送过滤条件，
    // 服务端已经保存有过滤条件
    if (proto->cursor != NULL) {
        ret = FixBufPutUint32(buf, DM_YANG_HAS_CURSOR);
        if (ret != GMERR_OK) {
            return ret;
        }
        return FixBufPutData(buf, (const void *)proto->cursor, sizeof(CltCursorT));
    }

    // 第一次发送查询条件，填入cursor的labelId为0，服务端收到labelId为0会认为不存在cursor，是第一次查询，会解析整个查询树
    if (proto->filter == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "fill subtree FILTER message.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    uint32_t filterMode = proto->filter->filterMode;
    bool hasDefaultPrefix = proto->defaultPrefix != NULL;
    // buf ：cursor标记+filterMode+hasDefaultPrefix
    ret = SecureFixBufPut3Uint32(buf, DM_YANG_NO_CURSOR, filterMode, (uint32_t)hasDefaultPrefix);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (hasDefaultPrefix) {
        ret = FixBufPutRawText(buf, (uint32_t)strlen(proto->defaultPrefix) + 1, (const void *)proto->defaultPrefix);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    uint32_t offset;
    ret = FixBufReserveDataOffset(buf, sizeof(uint32_t), &offset);
    if (ret != GMERR_OK) {
        return ret;
    }
    uint32_t count = 0;
    const GmcSubtreeFilterItemT *currFilter = proto->filter->filter;
    while (currFilter != NULL) {
        count++;
        ret = PutSingleSubtreeFilter2Buf(buf, currFilter, filterMode);
        if (ret != GMERR_OK) {
            return ret;
        }
        currFilter = currFilter->next;
    }
    ret = FixBufPutReservedData(buf, offset, &count, sizeof(count));
    return ret;
}
#endif
static Status FillImportDataMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    Status ret = GMERR_OK;
    const ImportDataProtoT *dataProto = (const ImportDataProtoT *)(const void *)protoObj;
    ret = FixBufPutUint32(buf, (uint32_t)dataProto->mode);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = FixBufPutUint32(buf, (uint32_t)dataProto->algorithm);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = HelpFillString(buf, true, dataProto->key);
    if (ret != GMERR_OK) {
        return ret;
    }
    return HelpFillString(buf, false, dataProto->importDir);
}

static Status FillExportDataMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    Status ret = GMERR_OK;
    const ExportDataProtoT *proto = (const ExportDataProtoT *)(const void *)protoObj;
    ret = FixBufPutUint32(buf, (uint32_t)proto->mode);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = FixBufPutUint32(buf, (uint32_t)proto->algorithm);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = HelpFillString(buf, true, proto->key);
    if (ret != GMERR_OK) {
        return ret;
    }
    return HelpFillString(buf, false, proto->exportDir);
}

static Status FillIpsCloseConnectionMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    FillPublicStmtMsgHeader(buf, protoObj->opCode);
    const IpsCloseConnectionProtoT *proto = (const IpsCloseConnectionProtoT *)(const void *)protoObj;
    return SecureFixBufPutUint16(buf, proto->connId);
}

static Status FillIpsSetUserModeMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    FillPublicStmtMsgHeader(buf, protoObj->opCode);
    const IpsSetUserModeProtoT *proto = (const IpsSetUserModeProtoT *)(const void *)protoObj;
    return FixBufPutUint32(buf, proto->userMode);
}

static Status FillIpsProcessPrivsMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    FillPublicStmtMsgHeader(buf, protoObj->opCode);
    const IpsRejectOrAccepProcessProtoT *proto = (const IpsRejectOrAccepProcessProtoT *)(const void *)protoObj;
    return SecureFixBufPut2Uint32(buf, (uint32_t)proto->ipsOperationType, proto->pid);
}

static Status FillIpsSetUserResLimitMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    FillPublicStmtMsgHeader(buf, protoObj->opCode);
    const IpsUserResourcesLimitProtoT *proto = (const IpsUserResourcesLimitProtoT *)(const void *)protoObj;
    Status ret = FixBufPutUint32(buf, (uint32_t)proto->resType);
    if (ret != GMERR_OK) {
        return ret;
    }
    const uint32_t strNum = 2;
    ret = HelpFillMultiString(buf, true, strNum, proto->userName, proto->processName);
    if (ret != GMERR_OK) {
        return ret;
    }
    return FixBufPutUint32(buf, proto->value);
}

static Status FillIpsSetConnResLimitMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    FillPublicStmtMsgHeader(buf, protoObj->opCode);
    const IpsConnResourcesLimitProtoT *proto = (const IpsConnResourcesLimitProtoT *)(const void *)protoObj;
    return SecureFixBufPut3Uint32(buf, (uint32_t)proto->resType, proto->connId, proto->value);
}

static Status FillDtlSubRespMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    const DataLogSubRespProtoT *proto = (const DataLogSubRespProtoT *)(const void *)protoObj;
    Status ret = HelpFillSimpleObject(buf, proto->resp, proto->respTypeSize);
    if (ret != GMERR_OK) {
        return ret;
    }
    return CltFillDtlSubErrorCode(buf, proto->subErrorCode);
}

static Status FillUninstallDatalog(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    Status ret = GMERR_OK;
#ifdef FEATURE_DATALOG
    const UninstallDatalogProtoT *proto = (const UninstallDatalogProtoT *)protoObj;
    ret = HelpFillString(buf, true, proto->fileName);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = HelpFillString(buf, true, proto->namespaceName);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = FixBufPutUint32(buf, (uint32_t)proto->ignorePubsub);
    if (ret != GMERR_OK) {
        return ret;
    }
#endif
    return ret;
}

static Status FillIpsSetUserProtocalMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    FillPublicStmtMsgHeader(buf, protoObj->opCode);
    const IpsSetUserProtocolProtoT *proto = (const IpsSetUserProtocolProtoT *)protoObj;
    const uint32_t paramsCount = 2;
    Status ret = HelpFillMultiString(buf, true, paramsCount, proto->userName, proto->processName);
    if (ret != GMERR_OK) {
        return ret;
    }
    return SecureFixBufPut2Uint32(buf, proto->protocolType, proto->isEnable);
}

static Status FillRsmMigrationMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    FillPublicStmtMsgHeader(buf, protoObj->opCode);
    const RsmMigrationProtoT *proto = (const RsmMigrationProtoT *)protoObj;
    return HelpFillString(buf, true, proto->rsmMigrationPath);
}

typedef Status (*FillMsgFunc)(FixBufferT *, const ProtoHeadT *);

static FillMsgFunc GetFillMsgFunc(MsgOpcodeRpcE opCode)
{
    FillMsgFunc fillMsgFuncs[] = {
        NULL,                         // MSG_OP_RPC_NONE
        NULL,                         // MSG_OP_RPC_CONNECT
        NULL,                         // MSG_OP_RPC_DISCONNECT
        NULL,                         // MSG_OP_RPC_HEARTBEAT
        NULL,                         // MSG_OP_RPC_RELEASE_STMT
        NULL,                         // MSG_OP_RPC_CLT_SHM_EXPAND
        FillCreateVertexMsg,          // MSG_OP_RPC_CREATE_VERTEX_LABEL
        FillCreateLabelMsg,           // MSG_OP_RPC_CREATE_EDGE_LABEL
        FillCreateSubMsg,             // MSG_OP_RPC_CREATE_SUBSCRIPTION
        NULL,                         // MSG_OP_RPC_CREATE_PATH
        FillNamespaceMsg,             // MSG_OP_RPC_CREATE_NAMESPACE
        FillTablespaceMsg,            // MSG_OP_RPC_CREATE_TABLESPACE
        FillCreateLabelMsg,           // MSG_OP_RPC_CREATE_KV_TABLE
        FillUserBatchOperationMsg,    // MSG_OP_RPC_CREATE_USER
        NULL,                         // MSG_OP_RPC_CREATE_GROUP
        FillAlterLabelMsg,            // MSG_OP_RPC_ALTER_VERTEX_LABEL
        FillDropTruncateLabelMsg,     // MSG_OP_RPC_DROP_VERTEX_LABEL
        FillDropTruncateLabelMsg,     // MSG_OP_RPC_DEGRADE_VERTEX_LABEL
        FillDropTruncateLabelMsg,     // MSG_OP_RPC_DROP_EDGE_LABEL
        FillDropSubMsg,               // MSG_OP_RPC_DROP_SUBSCRIPTION
        NULL,                         // MSG_OP_RPC_DROP_PATH
        FillNamespaceMsg,             // MSG_OP_RPC_DROP_NAMESPACE
        FillTablespaceMsg,            // MSG_OP_RPC_DROP_TABLESPACE
        FillDropTruncateLabelMsg,     // MSG_OP_RPC_DROP_KV_TABLE
        NULL,                         // MSG_OP_RPC_DROP_USER
        NULL,                         // MSG_OP_RPC_DROP_GROUP
        FillDropTruncateLabelMsg,     // MSG_OP_RPC_TRUNCATE_KV_TABLE
        FillDropTruncateLabelMsg,     // MSG_OP_RPC_TRUNCATE_VERTEX_LABEL
        FillDropTruncateLabelMsg,     // MSG_OP_RPC_TRUNCATE_VERTEX_LABEL_BACKGROUND
        FillResPoolMsg,               // MSG_OP_RPC_CREATE_RES_POOL
        FillResPoolMsg,               // MSG_OP_RPC_DROP_RES_POOL
        FillBindResPoolMsg,           // MSG_OP_RPC_BIND_EXTENDED_RES_POOL
        FillBindResPoolMsg,           // MSG_OP_RPC_UNBIND_EXTENDED_RES_POOL
        FillBindResPoolMsg,           // MSG_OP_RPC_BIND_RES_POOL_TO_VERTEX_LABEL
        FillBindResPoolMsg,           // MSG_OP_RPC_UNBIND_RES_POOL_TO_VERTEX_LABEL
        NULL,                         // MSG_OP_RPC_BIND_RES_POOL_TO_FIELD(deleted)
        NULL,                         // MSG_OP_RPC_UNBIND_RES_POOL_TO_FIELD(deleted)
        FillBeginCheckMsg,            // MSG_OP_RPC_BEGIN_CHECK
        FillEndCheckMsg,              // MSG_OP_RPC_END_CHECK
        FillModifyAlarmThresholdMsg,  // MSG_OP_RPC_MODIFY_ALARM_THRESHOLD
        FillImportDatalog,            // MSG_OP_RPC_IMPORT_DATALOG
        FillImportDatalog,            // MSG_OP_RPC_UPGRADE_DATALOG
        FillImportDatalog,            // MSG_OP_RPC_ROLLBACK_DATALOG
        FillUninstallDatalog,         // MSG_OP_RPC_UNINSTALL_DATALOG
        NULL,                         // MSG_OP_RPC_CLEAR_NAMESPACE
        NULL,                         // MSG_OP_RPC_TRUNCATE_NAMESPACE
        NULL,                         // MSG_OP_RPC_BIND_NSP_TO_TSP
        NULL,                         // MSG_OP_RPC_YANG_VALIDATION_MODEL
        FillAlterConfigMsg,           // MSG_OP_RPC_ALTER_VERTEX_LABEL_CONFIG
        FillDuplicateLabelMsg,        // MSG_OP_RPC_DUPLICATE_VERTEX_LABEL
#ifdef WARM_REBOOT
        FillEndAllPartitionCheck,  // MSG_OP_RPC_END_ALL_PARTITION_CHECK
#endif
        FillInsertVertexMsg,          // MSG_OP_RPC_INSERT_VERTEX
        FillUpdateVertexMsg,          // MSG_OP_RPC_UPDATE_VERTEX
        FillDeleteVertexMsg,          // MSG_OP_RPC_DELETE_VERTEX
        FillDeleteVertexMsg,          // MSG_OP_RPC_DELETE_GRAPH
        FillDeleteVertexMsg,          // MSG_OP_RPC_REMOVE_GRAPH
        FillInsertVertexMsg,          // MSG_OP_RPC_REPLACE_VERTEX
        FillInsertVertexMsg,          // MSG_OP_RPC_REPLACE_GRAPH
        FillInsertVertexMsg,          // MSG_OP_RPC_MERGE_VERTEX
        NULL,                         // MSG_OP_RPC_NONE_VERTEX
        FillSetKvMsg,                 // MSG_OP_RPC_SET_KV
        FillGetKvMsg,                 // MSG_OP_RPC_DELETE_KV
        FillEdgeMsg,                  // MSG_OP_RPC_INSERT_EDGE
        FillEdgeMsg,                  // MSG_OP_RPC_DELETE_EDGE
        FillUpdateCheckVersionMsg,    // MSG_OP_RPC_UPDATE_CHECK_VERSION
        FillCheckReplaceMsg,          // MSG_OP_RPC_CHECK_REPLACE
        NULL,                         // MSG_OP_RPC_BATCH_SET_KV
        NULL,                         // MSG_OP_RPC_BATCH_DELETE_KV
        NULL,                         // MSG_OP_RPC_TX_START
        NULL,                         // MSG_OP_RPC_TX_COMMIT
        NULL,                         // MSG_OP_RPC_TX_ROLLBACK
        NULL,                         // MSG_OP_RPC_TX_ABORT
        FillSavepointMsg,             // MSG_OP_RPC_TX_CREATE_SAVEPOINT
        FillSavepointMsg,             // MSG_OP_RPC_TX_RELEASE_SAVEPOINT
        FillSavepointMsg,             // MSG_OP_RPC_TX_ROLLBACK_SAVEPOINT
        FillNamespaceMsg,             // MSG_OP_RPC_USE_NAMESPACE
        FillSystemPrivsOperationMsg,  // MSG_OP_RPC_GRANT_USER_SYSTEM_PRIVS
        FillSystemPrivsOperationMsg,  // MSG_OP_RPC_REVOKE_USER_SYSTEM_PRIVS
        NULL,                         // MSG_OP_RPC_GRANT_ROLE_SYS_PRIVS
        NULL,                         // MSG_OP_RPC_REVOKE_ROLE_SYS_PRIVS
        FillObjectPrivsMsg,           // MSG_OP_RPC_GRANT_OBJECT_PRIVS
        FillObjectPrivsMsg,           // MSG_OP_RPC_REVOKE_OBJECT_PRIVS
        FillIpsProcessPrivsMsg,       // MSG_OP_RPC_IPS_REJECT_OR_ACCEPT_PROCESS
        FillIpsCloseConnectionMsg,    // MSG_OP_RPC_IPS_CLOSE_CONNECTION
        FillIpsSetUserResLimitMsg,    // MSG_OP_RPC_IPS_SET_USER_RES_LIMIT,
        FillIpsSetConnResLimitMsg,    // MSG_OP_RPC_IPS_SET_CONN_RES_LIMIT,
        FillIpsSetUserModeMsg,        // MSG_OP_RPC_IPS_SET_USER_MODE
        FillIpsSetUserProtocalMsg,    // MSG_OP_RPC_IPS_SET_USER_PROTOCOL
        NULL,                         // MSG_OP_RPC_IDS_CONFIG_DETECTION
        FillSetCfgMsg,                // MSG_OP_RPC_SET_CFG
        FillSetScheduleTaskCfgMsg,    // MSG_OP_RPC_SET_SCHEDULE_TASK_CFG
        FillSetUserRequestWeightMsg,  // MSG_OP_RPC_SET_USER_REQUEST_WEIGHT
        FillSetLogCtrlMsg,            // MSG_OP_RPC_SET_LOG_LEVEL,
        FillSetLogCtrlMsg,            // MSG_OP_RPC_SET_LOG_SWITCH,
        NULL,                         // MSG_OP_RPC_TX_CHECK_OPTIMISTIC_CLASH
        NULL,                         // MSG_OP_RPC_FLUSH_DATA
        NULL,                         // MSG_OP_RPC_FLUSH_REDO
        NULL,                         // MSG_OP_RPC_FLUSH_ENABLE
        FillImportDataMsg,            // MSG_OP_RPC_IMPORT_DATA
        NULL,                         // MSG_OP_RPC_VERIFY_PERSIST_DATA
        NULL,                         // MSG_OP_RPC_SWAP_DATA_DIR
        FillRsmMigrationMsg,          // MSG_OP_RPC_RSM_MIGRATION
        NULL,                         // MSG_OP_RPC_SET_EMERGENCY
#ifdef SHUTDOWN
        NULL,  // MSG_OP_RPC_SHUTDOWN_SERVER
#endif
        FillGetVertexLabelMsg,  // MSG_OP_RPC_GET_VERTEX_LABEL
        FillGetLabelTypeMsg,    // MSG_OP_RPC_GET_LABEL
        FillResPoolMsg,         // MSG_OP_RPC_GET_RES_POOL
        FillGetLabelMsg,        // MSG_OP_RPC_GET_EDGE_LABEL
        FillGetLabelMsg,        // MSG_OP_RPC_GET_KV_TABLE
        FillGetKvMsg,           // MSG_OP_RPC_GET_KV
        FillGetKvMsg,           // MSG_OP_RPC_IS_KV_EXIST
        FillGetKvCountMsg,      // MSG_OP_RPC_GET_KV_RECORD_COUNT
        FillGetCfgMsg,          // MSG_OP_RPC_GET_CFG
        FillGetMemSizeInfo,     // MSG_OP_RPC_GET_ESTIMATE_MEMORY
        FillScanVertexMsg,      // MSG_OP_RPC_SCAN_VERTEX_BEGIN
        FillScanKvMsg,          // MSG_OP_RPC_SCAN_KV_BEGIN
        FillScanPathMsg,        // MSG_OP_RPC_BEGIN_SCAN_PATH
        NULL,                   // MSG_OP_RPC_GET_PATH
        FillGetCountMsg,        // MSG_OP_RPC_GET_COUNT
        FillGetStatCountMsg,    // MSG_OP_RPC_GET_STAT_COUNT
        NULL,                   // MSG_OP_RPC_FULL_SYNC_SCAN
#ifdef FEATURE_YANG
        NULL,                  // MSG_OP_RPC_YANG_VALIDATION
        FillSubtreeFilterMsg,  // MSG_OP_RPC_SUBTREE_FILTER
        NULL,                  // MSG_OP_RPC_FETCH_DIFF
        NULL,                  // MSG_OP_RPC_YANG_GET_LEAFREF_PATH
#else
        NULL,  // MSG_OP_RPC_YANG_VALIDATION
        NULL,  // MSG_OP_RPC_SUBTREE_FILTER
        NULL,  // MSG_OP_RPC_FETCH_DIFF
        NULL,  // MSG_OP_RPC_YANG_GET_LEAFREF_PATH
#endif
        FillTriggerSubScanMsg,        // MSG_OP_RPC_TRIGGER_SCAN
        FillGetCheckInfoMsg,          // MSG_OP_RPC_GET_CHECK_INFO
        FillGetAlarmDataMsg,          // MSG_OP_RPC_GET_ALARM_DATA
        FillDtlSubRespMsg,            // MSG_OP_RPC_DATALOG_SUB_RESPONSE
        FillGetLabelDegradeProgress,  // MSG_OP_RPC_GET_LABEL_DEGRADE_PROGRESS
        NULL,                         // MSG_OP_RPC_DATALOG_SUB_BATCH_RESPONSE
        NULL,                         // MSG_OP_RPC_GET_VERTEX_LABELS
        NULL,                         // MSG_OP_RPC_GET_EDGE_LABELS
        NULL,                         // MSG_OP_RPC_SCAN_KV
        NULL,                         // MSG_OP_RPC_MOVE_KV
        NULL,                         // MSG_OP_RPC_FILTER_KV
        NULL,                         // MSG_OP_RPC_GET_COLLECTION_PROPERTY
        NULL,                         // MSG_OP_RPC_LOAD_IDX
        FillExportDataMsg,            // MSG_OP_RPC_EXPORT_DATA
        NULL,                         // MSG_OP_RPC_TABLESPACE_ALARM
        NULL,                         // MSG_OP_RPC_IS_VERTEX_EXIST
        NULL,                         // MSG_OP_RPC_GET_INVALID_LABEL_BY_LABEL_LIST
        FillGetWarmRebootTables,      // MSG_OP_RPC_GET_WARM_REBOOT_TABLES
        NULL,                         // MSG_OP_RPC_BATCH
        NULL,                         // MSG_OP_RPC_EXEC
        NULL,                         // MSG_OP_RPC_SYS_NOTIFY
        NULL,                         // MSG_OP_RPC_PREPARE_CYPHER
        NULL,                         // MSG_OP_RPC_EXEC_CYPHER
        FillFetchCypherMsg,           // MSG_OP_RPC_FETCH_CYPHER
        NULL,                         // MSG_OP_RPC_TS_EXEC,
        NULL,                         // MSG_OP_RPC_TS_BULK_INSERT
        NULL,                         // MSG_OP_RPC_TS_PREPARE_EXEC
        NULL,                         // MSG_OP_RPC_SQL_EXEC
        NULL,                         // MSG_OP_RPC_SQL_PREPARE
        NULL,                         // MSG_OP_RPC_SQL_STEP
        NULL,                         // MSG_OP_RPC_SQL_FLUSH_DATA
        NULL,                         // MSG_OP_RPC_SQL_LOAD_TABLE_TO_MEM
        NULL,                         // MSG_OP_RPC_SQL_UNLOAD_TABLE_FROM_MEM
        NULL,                         // MSG_OP_RPC_SQL_GET_LOAD_TABLE_STATUS
        NULL,                         // MSG_OP_RPC_SQL_BIND_CPU_SET
        NULL,                         // MSG_OP_RPC_SQL_RESIZE_BUFFER_POOL
        NULL,                         // MSG_OP_RPC_SQL_FINISH
        NULL,                         // MSG_OP_RPC_STREAM_EXEC
    };

    DB_ASSERT(opCode < ELEMENT_COUNT(fillMsgFuncs));
    return fillMsgFuncs[opCode];
}

Status CltFillMsgBody(FixBufferT *buf, const ProtoHeadT *proto)
{
    Status ret;
#ifdef FEATURE_VLIVF
    if (proto->opCode == MSG_OP_RPC_LOAD_INDEX) {
        return FillInsertVertexMsg(buf, proto);
    }
#endif
    uint32_t opCode = FILL_MSG_OFFSET((uint32_t)proto->opCode);
    FillMsgFunc func = GetFillMsgFunc(opCode);
    if (SECUREC_UNLIKELY(func == NULL)) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "no fill msg method for opCode %" PRIu32 ".", opCode);
        return GMERR_DATA_EXCEPTION;
    }
    ret = func(buf, proto);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "fill message body for opCode %" PRIu32 ".", opCode);
    }
    return ret;
}
