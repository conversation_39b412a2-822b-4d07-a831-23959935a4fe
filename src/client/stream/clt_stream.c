/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Author: Stream Team
 * Description: insert structuring data of client
 * Create: 2023-02-23
 */
#include <string.h>
#include "dm_meta_schema.h"
#include "gmc_connection.h"
#include "clt_msg.h"
#include "clt_fill_msg.h"
#include "clt_check.h"
#include "clt_stream.h"
#include "db_query_language_define.h"
#include "clt_graph_filter.h"

#define STREAM_GRAPH_VIS_HORIZ_CMD "StreamGraphVis_hor"
#define STREAM_GRAPH_VIS_VERTI_CMD "StreamGraphVis_ver"

Status ParseStreamRsp(FixBufferT *buf, void *out)
{
    return GMERR_OK;
}

static Status CltStreamRequestSync(GmcStmtT *stmt, Status (*buildReq)(FixBufferT *req, const void *in), const void *in,
    Status (*parseStreamRsp)(FixBufferT *rsp, void *stmt))
{
    DB_POINTER3(stmt, buildReq, parseStreamRsp);
    FixBufferT *rsp = &stmt->recvPack;
    Status ret = CltRequestSendOnly(stmt->conn, buildReq, in);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltRecvResponse(stmt->conn, rsp);
    if (ret != GMERR_OK) {
        return ret;
    }
    const MsgHeaderT *msg = RpcPeekMsgHeader(rsp);
    ret = msg->opStatus;
    if (ret != GMERR_OK) {
        return ret;
    }
    // 解析应答数据并保存到 stmt 中
    ret = parseStreamRsp(rsp, stmt);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "parse rsp message");
        return ret;
    }
    return GMERR_OK;
}

static Status FillStreamSqlMsg(FixBufferT *buf, const ProtoHeadT *protoObj)
{
    DB_POINTER2(buf, protoObj);
    const ExecStreamProtoT *proto = (const ExecStreamProtoT *)(const void *)protoObj;
    return HelpFillString(buf, true, proto->command);
}

static Status CltFillCreateStreamMsgGraphVis(FixBufferT *req, const void *input)
{
    DB_POINTER2(req, input);
    MsgHeaderT *msg = RpcPeekMsgHeader(req);
    OpHeaderT *op = ProtocolPeekFirstOpHeader(req);
    msg->modelType = MODEL_STREAM;
    msg->serviceId = DRT_SERVICE_STMT;
    op->opCode = (uint32_t)MSG_OP_RPC_STREAM_GRAPH_VISUALIZATION;
    return FillStreamSqlMsg(req, (const ProtoHeadT *)input);
}

static Status CltFillCreateStreamMsg(FixBufferT *req, const void *input)
{
    DB_POINTER2(req, input);
    MsgHeaderT *msg = RpcPeekMsgHeader(req);
    OpHeaderT *op = ProtocolPeekFirstOpHeader(req);
    msg->modelType = MODEL_STREAM;
    msg->serviceId = DRT_SERVICE_STMT;
    op->opCode = (uint32_t)MSG_OP_RPC_STREAM_EXEC;
    return FillStreamSqlMsg(req, (const ProtoHeadT *)input);
}

Status CltStreamReq(GmcStmtT *stmt, const char *stmtChar)
{
    DB_POINTER2(stmt, stmtChar);
    Status ret = CltStmtBasicCheck(stmt, GMC_CONN_TYPE_SYNC);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "clt basic check problem");
        return ret;
    }
    ExecStreamProtoT proto = {};
    proto.command = stmtChar;
    if ((strcmp(stmtChar, STREAM_GRAPH_VIS_VERTI_CMD) == 0) || (strcmp(stmtChar, STREAM_GRAPH_VIS_HORIZ_CMD) == 0)) {
        proto.protoHead.opCode = MSG_OP_RPC_STREAM_GRAPH_VISUALIZATION;
        return CltStreamRequestSync(stmt, CltFillCreateStreamMsgGraphVis, &proto, ParseStreamRsp);
    } else {
        proto.protoHead.opCode = MSG_OP_RPC_STREAM_EXEC;
        return CltStreamRequestSync(stmt, CltFillCreateStreamMsg, &proto, ParseStreamRsp);
    }
}

Status StreamExecDirect(GmcStmtT *stmt, const char *statementText)
{
    Status ret = CltStreamReq(stmt, (const char *)statementText);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "Execute StreamExecDirect with problem.");
        return ret;
    }
    stmt->stmtType = CLT_STMT_TYPE_STREAM;
    return GMERR_OK;
}

// 客户端插入数据到流表
Status CltInsertStream(GmcStmtT *stmt)
{
    Status ret = CltSendRequest(stmt->conn, &stmt->conn->sendPack);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    return CltRecvStmtResponseWithCheckOpStatus(stmt);
}
