/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: gmc_datalog.c
 * Description: Implement of GMDB client datalog operations
 * Author:
 * Create: 2024-01-05
 */

#include "gmc_datalog.h"
#include "gmc_internal.h"
#include "clt_stmt.h"
#include "clt_check.h"
#include "clt_fill_msg.h"
#include "clt_msg.h"
#include "db_secure_msg_buffer.h"

#define DTL_DEFAULT_NAMESPACE "public"
#define DTL_IMPORT_PATH_MAX 260u
#define DTL_NAMESPACE_LENGTH_MAX 128
#define DTL_RSP_MSG_MAX 128

struct GmcImportDatalogOptionsT {
    bool isDistribute;                            /**< 是否分布式场景。 */
    char filePath[DTL_IMPORT_PATH_MAX];           /**< datalog so文件的路径。 */
    char namespaceName[DTL_NAMESPACE_LENGTH_MAX]; /**< 命名空间名。 */
};

struct GmcUnimportDatalogOptionsT {
    char soName[DTL_SO_NAME_LENGTH_MAX];          /**< datalog so名称。 */
    char namespaceName[DTL_NAMESPACE_LENGTH_MAX]; /**< 命名空间名。 */
};

/************************************** 用户处理pubsub响应函数 **************************************/

static Status GmcCreateRespCheck(GmcStmtT *stmt, GmcRespT **resp)
{
    Status ret = CltBasicCheckWithStmt(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (resp == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_NULL_VALUE_NOT_ALLOWED, "Resp.");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    if (stmt->stmtType != CLT_STMT_TYPE_SUB_VERTEX) {
        DB_LOG_AND_SET_LASERR(
            GMERR_WRONG_STMT_OBJECT, "Current stmt type %" PRId32 ", need subscribe vertex.", (int32_t)stmt->stmtType);
        return GMERR_WRONG_STMT_OBJECT;
    }
    SubPushSingleLabel *sub = CltGetOperationContext(stmt);
    if (!sub->isDatalogSub || sub->isStatusMerge) {
        // not a datalog sync subscription
        DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "Curr subscription.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }

    return GMERR_OK;
}

Status GmcCreateResp(GmcStmtT *stmt, GmcRespT **resp)
{
    Status ret = GmcCreateRespCheck(stmt, resp);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 用户调用GmcDestroyResp函数时，释放该内存
    *resp = (GmcRespT *)DbDynMemCtxAlloc(stmt->memCtx, sizeof(GmcRespT));
    if (*resp == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Alloc resp.");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(*resp, sizeof(GmcRespT), 0, sizeof(GmcRespT));
    // 默认申请的
    (*resp)->bindStmt = stmt;
    return GMERR_OK;
}

Status GmcSetRespMode(GmcRespT *resp, GmcRespModeTypeE mode)
{
    if (SECUREC_LIKELY(resp == NULL)) {
        DB_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "Resp.");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    switch (mode) {
        case GMC_RESP_SEND_BATCH_INSERT:
            resp->batchInsertResp.state = RESP_BATCH_STATE_PREPARED;
            resp->batchInsertResp.sendBuf = &resp->bindStmt->conn->sendPack;
            resp->batchInsertResp.rollBackPos = 0;
            resp->batchInsertResp.actualNum = 0;
            break;
        case GMC_RESP_SEND_FAILED_INDEX:
            (void)memset_s(&resp->failIndexResp, sizeof(RespSendFailedIndexT), 0, sizeof(RespSendFailedIndexT));
            break;
        default:
            DB_SET_LASTERR(GMERR_INVALID_VALUE, "Mode.");
            return GMERR_INVALID_VALUE;
    }
    resp->mode = mode;
    return GMERR_OK;
}

static Status GmcSubAddRespDMLCheck(GmcRespT *resp, GmcStmtT *stmt)
{
    Status ret = CltPtr2CheckWithErr(resp, stmt);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (stmt->stmtType != CLT_STMT_TYPE_SUB_VERTEX) {
        DB_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED, "Stmt only support CLT_STMT_TYPE_SUB_VERTEX type.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    // 创建resp的时候已经做限制，只有datalog的订阅才可以创建resp
    if (resp->mode != GMC_RESP_SEND_BATCH_INSERT) {
        DB_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED, "Resp only support GMC_RESP_SEND_BATCH_INSERT type.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    // 用来校验vertex是否为null
    if (stmt->fetchEof) {
        return GMERR_NO_DATA;
    }
    RespSendBatchInsertT *batchInsertResp = &resp->batchInsertResp;
    // 当前128可以满足datalog全同步的规格约束
    if (SECUREC_UNLIKELY(batchInsertResp->actualNum >= DTL_RSP_MSG_MAX)) {
        //  exceeds the upper limit
        DB_SET_LASTERR(GMERR_BATCH_BUFFER_FULL, "The num of resp batch operation.");
        return GMERR_BATCH_BUFFER_FULL;
    }
    return ret;
}

/* dml头信息填充      */
inline static Status RespInsertBatchFillVertxInfo(FixBufferT *buf, const DmVertexLabelT *vertexLabel)
{
    // fill vertexLabelId
    Status ret = FixBufPutUint32(buf, vertexLabel->metaCommon.metaId);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = FixBufPutUint32(buf, vertexLabel->metaCommon.version);
    if (ret != GMERR_OK) {
        return ret;
    }
    return FixBufPutUint32(buf, vertexLabel->metaVertexLabel->uuid);
}

static Status InitRespInsertBatchHeader(RespSendBatchInsertT *resp, const DmVertexLabelT *vertexLabel)
{
    FixBufferT *buf = resp->sendBuf;
    // 考虑到后续组件化演进，以及Datalog走独立的service处理batch insert操作，此处并未复用batch的相关函数
    // 但是与CltBatchInitHeader保持统一，即保持Batch insert的报文格式一致
    Status ret = CltFillMsgHeader(buf, MSG_OP_RPC_DATALOG_SUB_BATCH_RESPONSE, 0);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    uint32_t offset;
    ret = SecureFixBufReserveDataOffset(buf, sizeof(BatchHeaderT), &offset);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Header create when init batch header.");
        return ret;
    }
    BatchHeaderT *batchHeader = SecureFixBufOffsetToAddr(buf, offset);
#ifdef SECUREFIXBUF
    if (SECUREC_UNLIKELY(batchHeader == NULL)) {
        return GMERR_CONNECTION_RESET_BY_PEER;
    }
#endif
    // 此处都是采用batch默认的类型，用户无法独立设置
    (void)memset_s(batchHeader, sizeof(BatchHeaderT), 0, sizeof(BatchHeaderT));
    batchHeader->totalNum = 1;  // 由于Datalog全同步只支持insert类型的操作，故totalNum固定值为1
    resp->rollBackPos = FixBufGetPos(buf);
    resp->actualNum = 0;
    ret = SecureFixBufPutInt32(buf, MSG_OP_RPC_INSERT_VERTEX);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    ret = RespInsertBatchFillVertxInfo(buf, vertexLabel);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    resp->objectNumPos = FixBufGetPos(buf);
    // 初始化BatchHeader的时候，填充Vertex的数量为0
    return FixBufPutUint32(buf, 0);
}

static Status RespRebuildSendBuf(RespSendBatchInsertT *batchInsertResp, DmVertexT *vertex, CltCataLabelT *cltCataLabel)
{
    FixBufferT *buffer = batchInsertResp->sendBuf;
    // 异常情况1：之前申请失败或主动释放导致buffer为空
    if (SECUREC_UNLIKELY(batchInsertResp->rollBackPos == 0)) {
        DB_ASSERT(batchInsertResp->state == RESP_BATCH_STATE_PREPARED);
        return InitRespInsertBatchHeader(batchInsertResp, cltCataLabel->vertexLabel);
    }
    // 异常情况2：之前填写报文的操作没有成功，回滚点不符合预期
    bool rollback = FixBufGetPos(buffer) != batchInsertResp->rollBackPos;
    if (SECUREC_UNLIKELY(rollback)) {
        // 执行回滚。注意rollBackPos在buffer不为空时才有意义
        FixBufInitPut(buffer, batchInsertResp->rollBackPos);
    }
    return GMERR_OK;
}

static Status RespBatchDMLVertex(FixBufferT *buf, DmVertexT *vertex, uint8_t operateEdgeFlag)
{
    // 针对insert操作需要进行校验
    Status ret = DmVertexConstraintCheck(vertex);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    DmGetBitMapPropInfo(vertex);
    ret = DmCheckBitMapConstrict(vertex);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    ret = HelpFillVertex(buf, vertex);
    if (ret != GMERR_OK) {
        return ret;
    }
    // fill operateEdgeFlag
    return FixBufPutUint32(buf, operateEdgeFlag);
}

Status GmcSubAddRespDML(GmcRespT *resp, GmcStmtT *stmt)
{
    Status ret = GmcSubAddRespDMLCheck(resp, stmt);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    RespSendBatchInsertT *batchInsertResp = &resp->batchInsertResp;
    DmVertexT *vertex = CltGetVertexInStmt(stmt);
    CltCataLabelT *cltCataLabel = CltGetCltCataLabelInStmt(stmt);
    DB_POINTER(vertex);
    ret = RespRebuildSendBuf(batchInsertResp, vertex, cltCataLabel);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    ret = RespBatchDMLVertex(batchInsertResp->sendBuf, vertex, stmt->operateEdgeFlag);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        FixBufInitPut(batchInsertResp->sendBuf, batchInsertResp->rollBackPos);
        return ret;
    }
    batchInsertResp->state = RESP_BATCH_STATE_INSERT;
    batchInsertResp->actualNum++;
    batchInsertResp->rollBackPos = FixBufGetPos(batchInsertResp->sendBuf);
    uint32_t offset = MSG_HEADER_ALIGN_SIZE + MSG_OP_HEADER_ALIGN_SIZE;
    BatchHeaderT *header = (BatchHeaderT *)SecureFixBufOffsetToAddr(batchInsertResp->sendBuf, offset);
#ifdef SECUREFIXBUF
    if (SECUREC_UNLIKELY(header == NULL)) {
        // Unable to add sub resp dml,
        DB_LOG_ERROR(GMERR_CONNECTION_RESET_BY_PEER, "batch header is null");
        return GMERR_CONNECTION_RESET_BY_PEER;
    }
#endif
    header->totalOpNum = batchInsertResp->actualNum;
    (*((uint32_t *)&(batchInsertResp->sendBuf->buf[batchInsertResp->objectNumPos])))++;
    return GMERR_OK;
}

Status GmcSetSubFailedIndex(GmcRespT *resp, uint16_t failedDataNum, uint16_t failedIndexes[GMC_SUB_BATCH_MAX])
{
    if (resp == NULL || failedIndexes == NULL) {
        DB_LOG_AND_SET_LASERR(
            GMERR_NULL_VALUE_NOT_ALLOWED, "Resp or Indexes, failedDataNum is %" PRIu32 ".", (uint32_t)failedDataNum);
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    if (resp->mode != GMC_RESP_SEND_FAILED_INDEX) {
        DB_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED, "Resp only support GMC_RESP_SEND_FAILED_INDEX type.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    if (failedDataNum > GMC_SUB_BATCH_MAX) {
        DB_LOG_AND_SET_LASERR(
            GMERR_INVALID_VALUE, "Resp or Indexes or DataNum, failedDataNum is %" PRIu32 ".", (uint32_t)failedDataNum);
        return GMERR_INVALID_VALUE;
    }
    for (int i = 0; i < failedDataNum; ++i) {
        if (failedIndexes[i] >= GMC_SUB_BATCH_MAX) {
            // Wrong suffer a defeat index range,
            DB_LOG_AND_SET_LASERR(GMERR_INVALID_VALUE, "failedIndexes|i| is %" PRIu32 ".", (uint32_t)failedIndexes[i]);
            return GMERR_INVALID_VALUE;
        }
    }
    resp->failIndexResp.failedDataNum = failedDataNum;
    errno_t err = memcpy_s(resp->failIndexResp.failedIndexes, GMC_SUB_BATCH_MAX * sizeof(uint16_t), failedIndexes,
        failedDataNum * sizeof(uint16_t));
    if (err != EOK) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "cpy index resp.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    resp->failIndexResp.isFillFailedIndex = true;
    return GMERR_OK;
}

static Status GmcSetSubRespCheck(GmcStmtT *stmt, GmcRespT *resp)
{
    Status ret = CltBasicCheckWithStmt(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (resp == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_NULL_VALUE_NOT_ALLOWED, "Resp.");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    return GMERR_OK;
}

static Status ExecuteSendfailIndexResp(GmcStmtT *stmt, RespSendFailedIndexT *failedIndexResp)
{
    if (failedIndexResp->failedDataNum > GMC_SUB_BATCH_MAX) {
        // Wrong suffer a defeat data num,
        DB_LOG_AND_SET_LASERR(
            GMERR_INVALID_VALUE, "failedDataNum of resp is %" PRIu32 ".", (uint32_t)failedIndexResp->failedDataNum);
        return GMERR_INVALID_VALUE;
    }
    for (int i = 0; i < failedIndexResp->failedDataNum; ++i) {
        if (failedIndexResp->failedIndexes[i] >= GMC_SUB_BATCH_MAX) {
            // Wrong suffer a defeat index range,
            DB_LOG_AND_SET_LASERR(
                GMERR_INVALID_VALUE, "failedIndexes|i| is %" PRIu32 ".", (uint32_t)failedIndexResp->failedIndexes[i]);
            return GMERR_INVALID_VALUE;
        }
    }
    DataLogSubRespProtoT proto = {};
    proto.protoHead.opCode = MSG_OP_RPC_DATALOG_SUB_RESPONSE;
    proto.resp = failedIndexResp;
    proto.respTypeSize = (uint32_t)sizeof(RespSendFailedIndexT);
    proto.subErrorCode = stmt->dtlErrorCode;
    return CltStmtRespMsg(stmt, &proto.protoHead);
}

Status GmcSendResp(GmcStmtT *stmt, GmcRespT *resp)
{
    Status ret = GmcSetSubRespCheck(stmt, resp);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (stmt->conn->connType != GMC_CONN_TYPE_SUB || stmt->stmtType != CLT_STMT_TYPE_SUB_VERTEX) {
        DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "Operation for given connection type.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }

    SubPushSingleLabel *sub = CltGetOperationContext(stmt);
    if (!sub->isDatalogSub) {
        DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "Curr subscription.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }

    switch (resp->mode) {
        case GMC_RESP_SEND_FAILED_INDEX:
            if (!resp->failIndexResp.isFillFailedIndex) {
                DB_LOG_AND_SET_LASERR(GMERR_INVALID_PROPERTY, "No set index response.");
                return GMERR_INVALID_PROPERTY;
            }
            if ((ret = ExecuteSendfailIndexResp(stmt, &resp->failIndexResp)) == GMERR_OK) {
                resp->failIndexResp.isFillFailedIndex = false;
            }
            break;
        case GMC_RESP_SEND_BATCH_INSERT: {
            if (resp->batchInsertResp.state != RESP_BATCH_STATE_INSERT) {
                DB_LOG_AND_SET_LASERR(GMERR_INVALID_PROPERTY, "No batch response command.");
                return GMERR_INVALID_PROPERTY;
            }
            if ((ret = CltFillDtlSubErrorCode(&stmt->conn->sendPack, stmt->dtlErrorCode)) != GMERR_OK) {
                return ret;
            }
            ProtoHeadT protoHead = {.opCode = MSG_OP_RPC_DATALOG_SUB_BATCH_RESPONSE};
            // 由于Resp Batch操作在AddRespDML进行实际的报文填充操作，故发送成功后，要重新进行AddRespDML
            // 否则在设备环境上，共享内存给到服务端，客户端重新初始化fixbuf，会导致buf为NULL，再次发送便会空指针引用
            if ((ret = CltStmtRespMsg(stmt, &protoHead)) == GMERR_OK) {
                resp->batchInsertResp.state = RESP_BATCH_STATE_PREPARED;
                resp->batchInsertResp.sendBuf = &resp->bindStmt->conn->sendPack;
                resp->batchInsertResp.rollBackPos = 0;
                resp->batchInsertResp.actualNum = 0;
            }
            break;
        }
        default:
            DB_LOG_AND_SET_LASERR((ret = GMERR_FEATURE_NOT_SUPPORTED), "Resp type %" PRIu32 ".", (uint32_t)resp->mode);
    }
    return ret;
}

Status GmcDestroyResp(GmcStmtT *stmt, GmcRespT *resp)
{
    Status ret = GmcSetSubRespCheck(stmt, resp);
    if (ret != GMERR_OK) {
        return ret;
    }
    DbDynMemCtxFree(stmt->memCtx, resp);  // 正常释放内存，用户传入参数无法置空
    return GMERR_OK;
}

Status GmcResetResp(GmcRespT *resp)
{
    if (resp == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_NULL_VALUE_NOT_ALLOWED, "Resp null.");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    (void)memset_s(resp, sizeof(GmcRespT), 0, sizeof(GmcRespT));
    return GMERR_OK;
}

Status GmcGetUpgradeVersion(GmcStmtT *stmt, int32_t *upgradeVersion)
{
    Status ret = CltBasicCheckWithStmt(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (upgradeVersion == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_NULL_VALUE_NOT_ALLOWED, "upgradeVersion null.");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    ret = CltCheckStmtType(stmt, CLT_STMT_TYPE_VERTEX);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    CltOperVertexT *op = (CltOperVertexT *)CltGetOperationContext(stmt);
    DmVertexLabelT *vertexLabel = op->cltCataLabel->vertexLabel;
    if (!DmVertexLabelIsDatalogLabel(vertexLabel)) {
        DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "get upgradeVersion in Non-datalog table.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    VertexLabelCommonInfoT *commonInfo = vertexLabel->commonInfo;
    if (SECUREC_UNLIKELY(commonInfo == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "get upgrade vertex, vertexlabel commonInfo is null.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    DmDatalogLabelInfoT *dtlInfo = MEMBER_PTR(commonInfo, datalogLabelInfo);
    if (dtlInfo == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_NULL_VALUE_NOT_ALLOWED, "DatalogLabelInfo.");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    *upgradeVersion = dtlInfo->upgradeInfo.upgradeVersion;
    return GMERR_OK;
}

Status GmcBatchDeparseRetDtlError(
    GmcBatchRetT *batchRet, int64_t *errorCode, char *errorLableName, uint32_t errorLableNameSize)
{
    Status ret = CltPtr3CheckWithErr(batchRet, errorLableName, errorCode);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (errorLableNameSize == 0) {
        DB_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "blunderLableNameSize.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    FixBufferT *buffer = batchRet->batchBuf;
    if (buffer == NULL || !(*batchRet->batchBufValid)) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PROPERTY, "Batch buf.");
        return GMERR_INVALID_PROPERTY;
    }

    const MsgHeaderT *msgHeader = RpcPeekMsgHeader(buffer);
    if (msgHeader->serviceId != (uint16_t)DRT_SERVICE_STMT || msgHeader->modelType != (uint16_t)MODEL_DATALOG) {
        DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED,
            "get blunder code and blunder label name from non-datalog batch response. serviceId=%" PRIu16
            ", modelType=%" PRIu8 ".",
            msgHeader->serviceId, msgHeader->modelType);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }

    FixBufSeek(buffer, MSG_HEADER_ALIGN_SIZE + MSG_OP_HEADER_ALIGN_SIZE + sizeof(BatchRespT));
    errorLableName[0] = '\0';
    return CltMsgGetDtlError(buffer, errorCode, errorLableName, errorLableNameSize);
}

Status CltFormatAndCheckFileName(const char *filePath, char *realFilePath)
{
    Status ret = DbGetRealPath(filePath, realFilePath, PATH_MAX);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Get realpath from %s.", filePath);
        return ret;
    }
    if (!DbFileExist(realFilePath) && !DbDirExist(realFilePath)) {
        DB_LOG_AND_SET_LASERR(GMERR_FILE_OPERATE_FAILED, "No such file or directory: %s.", realFilePath);
        return GMERR_FILE_OPERATE_FAILED;
    }
    if (DbAdptAccess(realFilePath, R_OK) != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(GMERR_FILE_OPERATE_FAILED, "access file: %s.", realFilePath);
        return GMERR_FILE_OPERATE_FAILED;
    }
    return GMERR_OK;
}

Status GmcImportDatalog(GmcStmtT *stmt, const char *filePath)
{
    if (SECUREC_UNLIKELY(filePath == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_NULL_VALUE_NOT_ALLOWED, "FilePath.");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    char realFilePath[PATH_MAX] = {0};
    Status ret = CltFormatAndCheckFileName(filePath, realFilePath);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GmcImportDatalogSo(stmt, realFilePath, MSG_OP_RPC_IMPORT_DATALOG, DTL_DEFAULT_NAMESPACE, false);
}

Status GmcImportDatalogOptionsCreate(GmcImportDatalogOptionsT **options)
{
    Status ret = CltPtrCheckWithErr(options);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 用户调用GmcImportDatalogOptionsDestroy接口释放该内存
    *options = DbDynMemCtxAlloc(g_gmdbCltInstance.memCtx, sizeof(GmcImportDatalogOptionsT));
    if (*options == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Client alloc import datalog options.");
        return GMERR_OUT_OF_MEMORY;
    }
    (**options) = (GmcImportDatalogOptionsT){0};
    return GMERR_OK;
}

void GmcImportDatalogOptionsDestroy(GmcImportDatalogOptionsT *options)
{
    // 正常释放内存，后续不会再用到。注：DbDynMemCtxFree内部会对options判空
    DbDynMemCtxFree(g_gmdbCltInstance.memCtx, options);
}

Status GmcImportDatalogOptionsSetFilePath(GmcImportDatalogOptionsT *options, const char *filePath)
{
    Status ret = CltPtrCheck2(options, filePath);
    if (ret != GMERR_OK) {
        return ret;
    }
    errno_t err = strcpy_s(options->filePath, sizeof(options->filePath), filePath);
    if (SECUREC_UNLIKELY(err != EOK)) {
        DB_LOG_AND_SET_LASERR(GMERR_NAME_TOO_LONG, "File path.");
        return GMERR_NAME_TOO_LONG;
    }
    return GMERR_OK;
}

Status GmcImportDatalogOptionsSetNamespaceName(GmcImportDatalogOptionsT *options, const char *namespaceName)
{
    Status ret = CltPtrCheck2(options, namespaceName);
    if (ret != GMERR_OK) {
        return ret;
    }
    errno_t err = strcpy_s(options->namespaceName, sizeof(options->namespaceName), namespaceName);
    if (SECUREC_UNLIKELY(err != EOK)) {
        DB_LOG_AND_SET_LASERR(GMERR_NAME_TOO_LONG, "Namespace name.");
        return GMERR_NAME_TOO_LONG;
    }
    return GMERR_OK;
}

Status GmcImportDatalogOptionsSetIsDistribute(GmcImportDatalogOptionsT *options)
{
    Status ret = CltPtrCheckWithErr(options);
    if (ret != GMERR_OK) {
        return ret;
    }
    options->isDistribute = true;
    return GMERR_OK;
}

Status GmcImportDatalogWithOption(GmcStmtT *stmt, GmcImportDatalogOptionsT *options)
{
    if (SECUREC_UNLIKELY(options == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_NULL_VALUE_NOT_ALLOWED, "options.");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }

    const char *nspName = options->namespaceName;
    if (IsStringEmpty(nspName)) {
        // 如果用户未设置namespaceName，则使用默认的public命名空间
        nspName = DTL_DEFAULT_NAMESPACE;
    }

    // 校验文件路径
    if (SECUREC_UNLIKELY(options->filePath == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_NULL_VALUE_NOT_ALLOWED, "FilePath.");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    char realFilePath[PATH_MAX] = {0};
    Status ret = CltFormatAndCheckFileName(options->filePath, realFilePath);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GmcImportDatalogSo(stmt, realFilePath, MSG_OP_RPC_IMPORT_DATALOG, nspName, options->isDistribute);
}

Status GmcUnimportDatalogOptionsCreate(GmcUnimportDatalogOptionsT **options)
{
    Status ret = CltPtrCheckWithErr(options);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 用户调用GmcUnimportDatalogOptionsDestroy接口释放该内存
    *options = DbDynMemCtxAlloc(g_gmdbCltInstance.memCtx, sizeof(GmcUnimportDatalogOptionsT));
    if (*options == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Client alloc unimport datalog options.");
        return GMERR_OUT_OF_MEMORY;
    }
    (**options) = (GmcUnimportDatalogOptionsT){0};
    return GMERR_OK;
}

void GmcUnimportDatalogOptionsDestroy(GmcUnimportDatalogOptionsT *options)
{
    // 正常释放内存，后续不会再用到。注：DbDynMemCtxFree内部会对options判空
    DbDynMemCtxFree(g_gmdbCltInstance.memCtx, options);
}

Status GmcUnimportDatalogOptionsSetSoName(GmcUnimportDatalogOptionsT *options, const char *soName)
{
    Status ret = CltPtrCheck2(options, soName);
    if (ret != GMERR_OK) {
        return ret;
    }
    errno_t err = strcpy_s(options->soName, sizeof(options->soName), soName);
    if (SECUREC_UNLIKELY(err != EOK)) {
        DB_LOG_AND_SET_LASERR(GMERR_NAME_TOO_LONG, "Datalog so name.");
        return GMERR_NAME_TOO_LONG;
    }
    return GMERR_OK;
}

Status GmcUnimportDatalogOptionsSetNamespaceName(GmcUnimportDatalogOptionsT *options, const char *namespaceName)
{
    Status ret = CltPtrCheck2(options, namespaceName);
    if (ret != GMERR_OK) {
        return ret;
    }
    errno_t err = strcpy_s(options->namespaceName, sizeof(options->namespaceName), namespaceName);
    if (SECUREC_UNLIKELY(err != EOK)) {
        DB_LOG_AND_SET_LASERR(GMERR_NAME_TOO_LONG, "Namespace name.");
        return GMERR_NAME_TOO_LONG;
    }
    return GMERR_OK;
}

Status GmcUnimportDatalogWithOption(GmcStmtT *stmt, GmcUnimportDatalogOptionsT *options)
{
    if (SECUREC_UNLIKELY(options == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_NULL_VALUE_NOT_ALLOWED, "config.");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }

    const char *nspName = options->namespaceName;
    if (IsStringEmpty(nspName)) {
        // 如果用户未设置namespaceName，则使用默认的public命名空间
        nspName = DTL_DEFAULT_NAMESPACE;
    }

    return GmcUnimportDatalogSo(stmt, options->soName, nspName);
}
