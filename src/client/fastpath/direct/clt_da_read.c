/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: direct_read.c
 * Description: functions for direct read
 * Author: shenjunhui
 * Create: 2023-4-11
 */

#include "clt_da_read.h"

#include "clt_da_handle.h"
#include "dm_meta_prop_label.h"
#include "gmc_types.h"
#include "ds_log.h"
#include "se_resource_session_pub.h"
#include "ds_concurrency_control.h"
#include "db_memcpy.h"
#include "db_secure_msg_buffer.h"
#include "clt_da_vertex_directread.h"
#include "clt_da_kv_directacs.h"
#include "clt_da_read_secidx_scan.h"

#include "se_page_mgr.h"
#include "clt_resource.h"
#include "clt_exception.h"

#ifdef __cplusplus
extern "C" {
#endif

static Status CltObjPrivCheck(
    DbSessionCtxT *sessionCtx, const CataRoleT *role, DmObjPrivT *objPriv, DmPrivilegeE objPrivType, bool *hasPriv)
{
    DB_POINTER3(role, objPriv, hasPriv);
    if (objPriv->privEntryNum > 0) {
        DbRWSpinRLockWithSession(sessionCtx, &((DmObjPrivT *)objPriv)->rwLatch,
            &((DmObjPrivT *)objPriv)->rwLatchShmAddr, LATCH_ADDR_OBJ_PRIV_RWLATCH_SHMEM);
        DmObjPrivEntryT *objPrivEntrysClt = DbShmPtrToAddr(objPriv->objPrivEntriesShm);
        if (SECUREC_UNLIKELY(objPrivEntrysClt == NULL)) {
            DbRWSpinRUnlockWithSession(sessionCtx, &((DmObjPrivT *)objPriv)->rwLatch);
            DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED,
                "Transform shm to addr in CltObjPrivCheck, segId: %" PRIu32 ", offset: %" PRIu32,
                objPriv->objPrivEntriesShm.segId, objPriv->objPrivEntriesShm.offset);
            return GMERR_MEMORY_OPERATE_FAILED;
        }
        for (uint32_t i = 0; i < objPriv->privEntryNum; i++) {
            if (objPrivEntrysClt[i].grantee != role->metaCommon.metaId) {
                continue;
            }
            if ((objPrivEntrysClt[i].privileges & (uint16_t)objPrivType) != 0) {
                DbRWSpinRUnlockWithSession(sessionCtx, &((DmObjPrivT *)objPriv)->rwLatch);
                *hasPriv = true;
                return GMERR_OK;
            }
            break;
        }
        DbRWSpinRUnlockWithSession(sessionCtx, &((DmObjPrivT *)objPriv)->rwLatch);
    }
    *hasPriv = false;
    return GMERR_OK;
}

bool IsVertexLabelPrivChanged(const CltCataLabelT *cltLabel, DirectAccessRunCtxT *daRunCtxBase)
{
    DmVertexLabelT *vertexLabel = cltLabel->vertexLabel;
    VertexLabelCommonInfoT *commonInfo = vertexLabel->commonInfo;
    if (SECUREC_UNLIKELY(commonInfo->objPrivVersion != cltLabel->objPrivVersion)) {
        return true;
    }
    if (SECUREC_UNLIKELY(daRunCtxBase->objPrivNsp == NULL)) {
        daRunCtxBase->objPrivNsp = DbShmPtrToAddr(commonInfo->nspObjPrivShmPtr);
        if (daRunCtxBase->objPrivNsp == NULL) {  // 后面check的时候会返回失败值
            DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "Shmptr to addr, segId: %" PRIu32 ", offset: %" PRIu32,
                commonInfo->nspObjPrivShmPtr.segId, commonInfo->nspObjPrivShmPtr.offset);
            return true;
        }
    }
    if (SECUREC_UNLIKELY(daRunCtxBase->objPrivNsp->objPrivVersion != daRunCtxBase->nspPrivVersion)) {
        return true;
    }
    return false;
}

Status DirectAccessPrivCheck(DirectAccessRunCtxT *runCtxBase, DmObjPrivT *objPriv, CataObjTypeE cataObjType,
    CataSysPrivTypeE sysPrivType, DmPrivilegeE objPrivType)
{
    // step 1：检查是否是不authenticate模式，不authenticate模式直接返回
    if (SECUREC_UNLIKELY(runCtxBase->privPolicyMode == (uint32_t)PRIV_NON_AUTH)) {
        return GMERR_OK;
    }
    CataRoleT *role = (CataRoleT *)(runCtxBase->role);
    // step 2：检查对象权限
    bool hasPriv = false;
    Status ret = CltObjPrivCheck(runCtxBase->sessionCtx, role, objPriv, objPrivType, &hasPriv);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "obj priv check.");
        return ret;
    }
    if (SECUREC_LIKELY(hasPriv)) {
        runCtxBase->passPrivType = CLI_OBJ_PRIV;
        return GMERR_OK;
    }
    // step 3：检查系统权限
    if (SECUREC_LIKELY((role->sysPrivileges[cataObjType].privilege & (uint32_t)sysPrivType) != 0)) {
        runCtxBase->passPrivType = CLI_SYS_PRIV;
        return GMERR_OK;
    }
    // step 4：检查namespace权限
    if (SECUREC_UNLIKELY(runCtxBase->objPrivNsp == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "namespace priv check, objPrivNsp NULL.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    ret = CltObjPrivCheck(runCtxBase->sessionCtx, role, runCtxBase->objPrivNsp, objPrivType, &hasPriv);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "namespace priv check.");
        return ret;
    }
    if (SECUREC_LIKELY(hasPriv)) {
        runCtxBase->passPrivType = CLI_NSP_PRIV;
        return GMERR_OK;
    }

    DS_ERROR_1(GMERR_INSUFFICIENT_PRIVILEGE,
        "Insufficient priv to operate. Role %" PRIu32 " has no permission to operate, priv is %" PRIu32 ". %" PRIu32,
        role->metaCommon.metaId, (uint32_t)objPrivType, runCtxBase->privPolicyMode);
    // step 5: 宽容模式下，即便权限校验不通过，也可以继续执行，但是需要打印日志。
    if (SECUREC_LIKELY(runCtxBase->privPolicyMode == (uint32_t)PRIV_TOLERATE)) {
        return GMERR_OK;
    }
    DB_SET_LAST_ERROR(GMERR_INSUFFICIENT_PRIVILEGE,
        "Insufficient priv to operate. Role %" PRIu32 " has no permission to operate, priv is %" PRIu32 ". %" PRIu32,
        role->metaCommon.metaId, (uint32_t)objPrivType, runCtxBase->privPolicyMode);
    DB_ASSERT(runCtxBase->privPolicyMode == PRIV_AUTH);
    return GMERR_INSUFFICIENT_PRIVILEGE;
}

void DirectReadCtxCloseHeapCursor(DrRunCtxT *drRunCtx);
void DirectReadCtxCloseHeapCursorOpt(DrRunStructOptCtxT *drRunCtx);

SEC_IDX_READ_FUNC Status DirectReadCtxOpen(DrRunCtxT *drRunCtx)
{
    DB_POINTER(drRunCtx);
    SeInstanceHdT seIns = ((SeRunCtxT *)drRunCtx->base.seRunCtxHandle)->seIns;
    if (SECUREC_UNLIKELY(SeGetStorageStatus(seIns) == SE_ON_DISK_EMRGNCY)) {
        return GMERR_DATABASE_NOT_AVAILABLE;
    }
    // 重入Open时，需要先重置一下runCtx
    DirectReadCtxCloseHeapCursorNoLock(drRunCtx);
    return MainStoreReadOpen(drRunCtx);
}

Status DirectReadAllocCtxMem(
    DbMemCtxT *memCtx, uint16_t instanceId, uint16_t trxSlot, uint32_t sessionId, DrRunCtxT **drRunCtx)
{
    // 正常情况下该块内存在断连的时候才会释放
    DrRunCtxT *drRunCtxTmp = (DrRunCtxT *)DbDynMemCtxAlloc(memCtx, sizeof(DrRunCtxT));
    if (drRunCtxTmp == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "Open delta store");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    SeOpenCfgT cltSeOpenCfg = {
        .isClient = true,
        .isDirectWrite = false,
        .rsmCtxPtr = DB_INVALID_SHMPTR,
        .reserveMemCtx = NULL,
        .pageMgrMemCtx = g_gmdbCltInstance.memCtx,
        .trxSlot = trxSlot,
    };
    Status ret = SeOpen(instanceId, memCtx, &cltSeOpenCfg, (SeRunCtxHdT *)&drRunCtxTmp->base.seRunCtxHandle);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "Open SE when open delta store");
        goto CLEAN1;
    }
    if ((ret = SeAttachResSessionById(drRunCtxTmp->base.seRunCtxHandle, sessionId)) != GMERR_OK) {
        goto CLEAN2;
    }
    *drRunCtx = drRunCtxTmp;
    return GMERR_OK;
CLEAN2:
    (void)SeClose(drRunCtxTmp->base.seRunCtxHandle);
CLEAN1:
    DbDynMemCtxFree(memCtx, drRunCtxTmp);  // 失败场景下内存指针没有传递到外面，并且会返回错误码，不用置空
    return ret;
}

Status DirectAccessInitBaseRunCtxInfo(DirectAccessRunCtxT *directRunCtx, DbMemCtxT *memCtx)
{
    Status ret = SeGetResSessionRole(directRunCtx->seRunCtxHandle, (uint8_t **)&directRunCtx->role);
    if (ret != GMERR_OK) {
        return ret;
    }
    directRunCtx->privPolicyMode = SeGetPolicyMode(directRunCtx->seRunCtxHandle);
    directRunCtx->clientMemCtx = memCtx;
    directRunCtx->pkIndexRunCtx = NULL;
    directRunCtx->heapCtxMem = NULL;
    directRunCtx->heapRunCtx = NULL;
    directRunCtx->chCtxMem = NULL;
    directRunCtx->chRunCtx = NULL;
    directRunCtx->chCursor = NULL;
    directRunCtx->isUseClusteredHashTable = false;
    directRunCtx->containerType = CONTAINER_INVALID;
    directRunCtx->idxCtxMem = NULL;
    directRunCtx->lastPrivPass = false;
    directRunCtx->passPrivType = CLI_INVALID_PRIV;
    directRunCtx->objPrivNsp = NULL;
    directRunCtx->pkPrepareFlag = false;
#ifdef ART_CONTAINER
    directRunCtx->isRealCluster = false;
    directRunCtx->artMemCtx = NULL;
    directRunCtx->artCtxMem = NULL;
    directRunCtx->artRunCtx = NULL;
#endif
    return GMERR_OK;
}

Status DirectReadInitCtxInfo(DrRunCtxT *drRunCtx, DbMemCtxT *memCtx)
{
    (void)memset_s(&drRunCtx->structureCtx, sizeof(StructureCtxT), 0x00, sizeof(StructureCtxT));
    TupleBufInit(&drRunCtx->tupleBuf, memCtx);
    drRunCtx->currSecIndexRunCtx = NULL;
    drRunCtx->htCursor = NULL;
    drRunCtx->heapCursor = NULL;
    drRunCtx->dsFetchHandle.indexNotOpen = false;
    drRunCtx->isHeapCursorRefInc = false;
    drRunCtx->heapCursorReportTimeOut = 60;  // 默认超时时间为60s
    return GMERR_OK;
}

Status DirectReadOpen(GmcConnT *conn, uint32_t sessionId, uint16_t trxSlot, DbMemCtxT *memCtx, DrRunCtxT **drRunCtx)
{
    DB_POINTER2(memCtx, drRunCtx);
    Status ret = DirectReadAllocCtxMem(memCtx, (uint16_t)conn->instanceId, trxSlot, sessionId, drRunCtx);
    if (ret != GMERR_OK) {
        *drRunCtx = NULL;
        return ret;
    }
    ret = DirectAccessInitBaseRunCtxInfo(&(*drRunCtx)->base, memCtx);
    if (ret != GMERR_OK) {
        // 异常分支外部DirectReadOpen统一释放资源。
        return ret;
    }
    (*drRunCtx)->cfgCompatibleV3 = CltCfgIsOpenInt32(conn->cfgShmConfig[CLT_CFG_COMPATIBLE_V3].value.int32Val);
    return DirectReadInitCtxInfo(*drRunCtx, memCtx);
}

Status DirectReadClose(DbMemCtxT *memCtx, DrRunCtxT *drRunCtx)
{
    DB_POINTER(memCtx);
    // 内存申请失败的场景下drRunCtx可能会空
    if (drRunCtx == NULL) {
        DB_LOG_WARN(GMERR_UNEXPECTED_NULL_VALUE, "drRunCtx NULL.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    SeRunCtxHdT seRunCtx = drRunCtx->base.seRunCtxHandle;
    // 调用存储引擎的close接口
    Status ret = SeClose(seRunCtx);
    if (ret != GMERR_OK) {
        return ret;
    }
    drRunCtx->base.seRunCtxHandle = NULL;
    TupleBufRelease(&drRunCtx->tupleBuf);
    DbDynMemCtxFree(memCtx, drRunCtx);  // 正常释放内存，后续不会再用到
    return ret;
}

Status MainStoreHeapLabelOpen(DrRunCtxT *drRunCtx, DmLabelTypeE labelType)
{
    DirectAccessRunCtxT *drRunCtxBase = &drRunCtx->base;
    if (SECUREC_LIKELY(drRunCtxBase->heapRunCtx != NULL)) {
        ContainerOpenForDirectRead(drRunCtxBase->heapRunCtx, drRunCtxBase->clientMemCtx);
        return GMERR_OK;
    }
    Status ret;
    void *labelInfo = NULL;
    ShmemPtrT heapShmAddr;
    bool isUseRsm = false;
    if (labelType == VERTEX_LABEL) {
        ret = VertexGetLabelInfoAndHeapShmAddr(drRunCtx, &labelInfo, &heapShmAddr, &isUseRsm);
    } else {
#ifdef FEATURE_KV
        ret = KvGetLabelInfoAndHeapShmAddr(drRunCtx, &labelInfo, &heapShmAddr, &isUseRsm);
#else
        ret = GMERR_FEATURE_NOT_SUPPORTED;
#endif
    }
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (SECUREC_UNLIKELY(drRunCtxBase->heapCtxMem == NULL)) {
        ContainerInitCfgT allocCfg = {
            .containerType = CONTAINER_HEAP,
            .seRunCtx = drRunCtxBase->seRunCtxHandle,
        };
        if ((ret = ContainerAllocRunCtx(&drRunCtxBase->heapCtxMem, &allocCfg)) != GMERR_OK) {
            DB_LOG_ERROR(ret, "Alloc heapCtx when client open VL.");
            return ret;
        }
    }
    ContainerInitCfgT initCfg = {
        .containerType = CONTAINER_HEAP,
        .labelShmAddr = heapShmAddr,
        .seRunCtx = (SeRunCtxHdT)drRunCtxBase->seRunCtxHandle,
        .dmInfo = labelInfo,
        .isUseRsm = isUseRsm,
        .isBackGround = false,
    };
    ret = ContainerTryInitRunCtx(drRunCtxBase->heapCtxMem, &initCfg);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    drRunCtxBase->heapRunCtx = drRunCtxBase->heapCtxMem;
    ContainerOpenForDirectRead(drRunCtxBase->heapRunCtx, drRunCtxBase->clientMemCtx);

    return ret;
}

static void VertexGetLabelInfoAndHeapShmAddrOpt(
    const DrRunStructOptCtxT *drRunCtx, void **labelInfo, ShmemPtrT *heapShmAddr, bool *isUseRsm)
{
    VertexScanArgsT vertexScanArgs = drRunCtx->dsFetchArgs.vertexScanArgs;
    VertexLabelCommonInfoT *commonInfo =
        (VertexLabelCommonInfoT *)(vertexScanArgs.cltCataLabel->vertexLabel->commonInfo);
    *labelInfo = (void *)(vertexScanArgs.cltCataLabel->vertexLabel);
    *heapShmAddr = commonInfo->heapInfo.heapShmAddr;
    *isUseRsm = vertexScanArgs.cltCataLabel->vertexLabel->metaCommon.isUseRsm;
}

Status MainStoreHeapLabelOpenOpt(DrRunStructOptCtxT *drRunCtx, DmLabelTypeE labelType)
{
    DirectAccessStructOptRunCtxT *drRunCtxBase = &drRunCtx->base;
    if (SECUREC_LIKELY(drRunCtxBase->heapRunCtx != NULL)) {
        ContainerOpenForDirectRead(drRunCtxBase->heapRunCtx, drRunCtxBase->clientMemCtx);
        return GMERR_OK;
    }
    Status ret;
    void *labelInfo = NULL;
    ShmemPtrT heapShmAddr;
    bool isUseRsm = false;
    VertexGetLabelInfoAndHeapShmAddrOpt(drRunCtx, &labelInfo, &heapShmAddr, &isUseRsm);
    if (SECUREC_UNLIKELY(drRunCtxBase->heapCtxMem == NULL)) {
        ContainerInitCfgT allocCfg = {
            .containerType = CONTAINER_HEAP,
            .seRunCtx = drRunCtxBase->seRunCtxHandle,
        };
        if ((ret = ContainerAllocRunCtx(&drRunCtxBase->heapCtxMem, &allocCfg)) != GMERR_OK) {
            DB_LOG_ERROR(ret, "Alloc heapCtx when client open VL.");
            return ret;
        }
    }
    ContainerInitCfgT initCfg = {
        .containerType = CONTAINER_HEAP,
        .labelShmAddr = heapShmAddr,
        .seRunCtx = (SeRunCtxHdT)drRunCtxBase->seRunCtxHandle,
        .dmInfo = labelInfo,
        .isUseRsm = isUseRsm,
        .isBackGround = false,
    };
    ret = ContainerTryInitRunCtx(drRunCtxBase->heapCtxMem, &initCfg);
    if (ret != GMERR_OK) {
        return ret;
    }
    drRunCtxBase->heapRunCtx = drRunCtxBase->heapCtxMem;
    ContainerOpenForDirectRead(drRunCtxBase->heapRunCtx, drRunCtxBase->clientMemCtx);

    return ret;
}

Status MainStoreIndexOpen(DrRunCtxT *drRunCtx, DmLabelTypeE labelType)
{
    DirectAccessRunCtxT *drRunCtxBase = &drRunCtx->base;
    if (drRunCtxBase->pkIndexRunCtx != NULL) {  // 使用上次缓存的主键索引
        return GMERR_OK;
    }
    Status ret;
    if (SECUREC_UNLIKELY(drRunCtxBase->idxCtxMem == NULL)) {
        // indexAlloc 函数要求直连读场景传入INDEX_TYPE_MAX，防止再次Open时，之前alloc的index长度不够用
        if ((ret = IdxAlloc((SeRunCtxHdT)drRunCtxBase->seRunCtxHandle, INDEX_TYPE_MAX, &(drRunCtxBase->idxCtxMem))) !=
            GMERR_OK) {
            DB_LOG_ERROR(ret, "Alloc idxCtx when client open index.");
            return ret;
        }
    }
    if (labelType == VERTEX_LABEL) {
        return MainStoreVertexIndexOpen(drRunCtx);
    } else {
#ifdef FEATURE_KV
        return MainStoreKvIndexOpen(drRunCtx);
#endif
    }
    return GMERR_OK;
}

Status MainStoreHeapCursorOpen(DrRunCtxT *drRunCtx)
{
    DirectAccessRunCtxT *drRunCtxBase = &drRunCtx->base;
    if (SECUREC_LIKELY(drRunCtxBase->containerType == CONTAINER_CLUSTERED_HASH)) {
        ChLabelScanCursorHdlT chCursor = NULL;
        ChLabelBeginScanCfgT beginScanCfg = {.isDefragmentation = false,
            .isRecovery = false,
            .maxFetchNum = drRunCtx->dsFetchArgs.preFetchRows,
            .beginAddr = {.rowId = BEFORE_FIRST_TUPLE_ADDR, .segmentPos = {0}},
            .scanMode = (drRunCtx->dsFetchArgs.isSetPreFetch || drRunCtx->dsFetchArgs.limitCount > 0) ?
                            CH_SCAN_ROWS :
                            CH_SCAN_ONE_PAGE,
            .chLabelLatchInfoForClt =
                ChLabelLatchInfoInit(drRunCtxBase->labelLatchVersionId, drRunCtxBase->labelLatchShmAddr)};
        Status ret = ChLabelBeginScanCursor(drRunCtxBase->chRunCtx, &beginScanCfg, &chCursor);
        if (ret != GMERR_OK) {
            return ret;
        }
        drRunCtxBase->chCursor = chCursor;
    } else if (drRunCtxBase->containerType == CONTAINER_HEAP) {
        HeapScanCursorHdlT heapCursor = NULL;
        uint64_t createTime = (uint64_t)DbRdtsc();
        uint64_t threadId = (uint64_t)DbThreadGetSelfId();
        HeapBeginScanCfgT beginScanCfg = {.isDefragmentation = false,
            .beginAddr = {.tupleAddr = BEFORE_FIRST_TUPLE_ADDR, .blockId = BEFORE_FIRST_TUPLE_BLOCK_ID},
            .maxFetchNum = drRunCtx->dsFetchArgs.preFetchRows,
            .heapDowngradeInfo = HeapLabelDowngradeInfoInit(),
            .heapLabelLatchInfoForClt =
                HeapLabelLatchInfoInit(drRunCtxBase->labelLatchVersionId, drRunCtxBase->labelLatchShmAddr)};
        Status ret = HeapLabelBeginScanHpTupleBuffer(drRunCtxBase->heapRunCtx, &beginScanCfg, &heapCursor);
        if (ret != GMERR_OK) {
            return ret;
        }
        drRunCtx->heapCursor = heapCursor;
        drRunCtx->timeOfCreateCursor = createTime;
        drRunCtx->threadIdOfCreateCursor = threadId;
    }
#ifdef ART_CONTAINER
    else if (drRunCtxBase->containerType == CONTAINER_ART) {
        DB_LOG_ERROR(GMERR_FEATURE_NOT_SUPPORTED, "Art container no support seq scan");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
#endif
    else {
        DB_ASSERT(false);
    }
    return GMERR_OK;
}

Status MainStoreReadOpen(DrRunCtxT *drRunCtx)
{
    DsScanModeE scanMode = drRunCtx->dsFetchArgs.scanMode;
    switch (SWITCH_LIKELY(scanMode, DS_LOOKUP_VERTEX_INDEX_PRIMARY)) {
        case DS_SCAN_VERTEX_SEQUENCE:
            return MainStoreVertexScanOpen(drRunCtx, VERTEX_LABEL);
        case DS_LOOKUP_VERTEX_INDEX_PRIMARY:
            return MainStoreVertexPkIdxOpen(drRunCtx, VERTEX_LABEL);
        case DS_SCAN_VERTEX_INDEX_SECONDARY:
            return DsOpenHeapAndSecIdxs(drRunCtx);
#ifdef ART_CONTAINER
        case DS_SCAN_VERTEX_INDEX_PRIMARY:
            return MainStoreContainerCondScanOpen(drRunCtx, VERTEX_LABEL);
#endif
#ifdef FEATURE_KV
        case DS_SCAN_KV_SEQUENCE:
            return MainStoreKvScanOpen(drRunCtx, KV_TABLE);
        case DS_SCAN_KV_INDEX:
            return MainStoreKvPkIdxOpen(drRunCtx, KV_TABLE);
#endif
        default:
            break;
    }
    DB_LOG_AND_SET_LASERR(
        GMERR_FEATURE_NOT_SUPPORTED, "Open direct read. Scan mode no support: %d.", (int32_t)scanMode);
    return GMERR_FEATURE_NOT_SUPPORTED;
}

PRI_IDX_READ_FUNC static Status MainStoreReadFetchNextPerform(DrRunCtxT *drRunCtx, uint32_t *fetchedRows, bool *isEOF)
{
    DsScanModeE scanMode = drRunCtx->dsFetchArgs.scanMode;
    switch (SWITCH_LIKELY(scanMode, DS_SCAN_VERTEX_SEQUENCE)) {
        case DS_SCAN_VERTEX_SEQUENCE:
            return MainStoreVertexScanFetchNext(drRunCtx, VERTEX_LABEL, fetchedRows, isEOF);
        case DS_LOOKUP_VERTEX_INDEX_PRIMARY:
            *isEOF = true;
            return MainStoreVertexPkIdxFetchNext(drRunCtx, fetchedRows);
        case DS_SCAN_VERTEX_INDEX_SECONDARY:
            return DsSecondaryFetchNext(drRunCtx, fetchedRows, isEOF);
#ifdef ART_CONTAINER
        case DS_SCAN_VERTEX_INDEX_PRIMARY:
            return MainStoreContainerFetchNext(drRunCtx, fetchedRows, isEOF);
#endif
#ifdef FEATURE_KV
        case DS_SCAN_KV_SEQUENCE:
            return MainStoreKvScanFetchNext(drRunCtx, KV_TABLE, fetchedRows, isEOF);
        case DS_SCAN_KV_INDEX:
            *isEOF = true;
            return MainStoreKvPkIdxFetchNext(drRunCtx, fetchedRows);
#endif
        default:
            break;
    }
    DB_LOG_AND_SET_LASERR(
        GMERR_FEATURE_NOT_SUPPORTED, "Direct read to fetch next. Scan mode no support: %d.", (int32_t)scanMode);
    return GMERR_FEATURE_NOT_SUPPORTED;
}

PRI_IDX_READ_FUNC Status MainStoreReadFetchNext(DrRunCtxT *drRunCtx, uint32_t *fetchedRows, bool *isEOF)
{
    DB_POINTER3(drRunCtx, fetchedRows, isEOF);
    DirectAccessRunCtxT *drRunCtxBase = &drRunCtx->base;
#ifdef ART_CONTAINER
    if (SECUREC_UNLIKELY(drRunCtxBase->heapRunCtx == NULL && drRunCtxBase->chRunCtx == NULL &&
                         drRunCtxBase->artRunCtx == NULL)) {  // 没有Open
#else
    if (SECUREC_UNLIKELY(drRunCtxBase->heapRunCtx == NULL && drRunCtxBase->chRunCtx == NULL)) {  // 没有Open
#endif
        DB_LOG_AND_SET_LASERR(
            GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, "Direct read to fetch next. Fetch Next Without Open.");
        return GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
    }
    Status ret = MainStoreReadFetchNextPerform(drRunCtx, fetchedRows, isEOF);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    SeInstanceHdT seIns = ((SeRunCtxT *)drRunCtx->base.seRunCtxHandle)->seIns;
    if (SECUREC_UNLIKELY(SeGetStorageStatus(seIns) == SE_ON_DISK_EMRGNCY)) {
        return GMERR_DATABASE_NOT_AVAILABLE;
    }
    return GMERR_OK;
}

static inline void DsCloseHeapCtx(DrRunCtxT *drRunCtx)
{
    DirectAccessRunCtxT *drRunCtxBase = &drRunCtx->base;
    if (drRunCtxBase->heapRunCtx != NULL) {  // directread那边的操作会使得heapRunCtx和heapOpen，因此需要同时判断
        // 表锁模式代替openCnt
        HeapLabelResetCtx(drRunCtxBase->heapRunCtx);
        drRunCtxBase->heapRunCtx = NULL;
    }
}

static inline void DsCloseHeapCtxOpt(DrRunStructOptCtxT *drRunCtx)
{
    DirectAccessStructOptRunCtxT *drRunCtxBase = &drRunCtx->base;
    if (drRunCtxBase->heapRunCtx != NULL) {  // directread那边的操作会使得heapRunCtx和heapOpen，因此需要同时判断
        // 表锁模式代替openCnt
        HeapLabelResetCtx(drRunCtxBase->heapRunCtx);
        drRunCtxBase->heapRunCtx = NULL;
    }
}

static inline void DsCloseHashClusterCtx(DrRunCtxT *drRunCtx)
{
    DirectAccessRunCtxT *drRunCtxBase = &drRunCtx->base;
    if (drRunCtxBase->chRunCtx != NULL) {
        ChLabelRunCtxReset(drRunCtxBase->chRunCtx);
        drRunCtxBase->chRunCtx = NULL;
    }
    return;
}

static inline void DsClosePkCtx(DrRunCtxT *drRunCtx)
{
    DirectAccessRunCtxT *drRunCtxBase = &drRunCtx->base;
    if (drRunCtxBase->pkIndexRunCtx != NULL) {
        TupleBufRelease(&((IndexCtxT *)drRunCtxBase->pkIndexRunCtx)->tupleBuf);
        IdxClose(drRunCtxBase->pkIndexRunCtx);
        drRunCtxBase->pkIndexRunCtx = NULL;
    }
    return;
}

static inline void DsClosePkCtxOpt(DrRunStructOptCtxT *drRunCtx)
{
    DirectAccessStructOptRunCtxT *drRunCtxBase = &drRunCtx->base;
    if (drRunCtxBase->pkIndexRunCtx != NULL) {
        TupleBufRelease(&((IndexCtxT *)drRunCtxBase->pkIndexRunCtx)->tupleBuf);
        IdxClose(drRunCtxBase->pkIndexRunCtx);
        drRunCtxBase->pkIndexRunCtx = NULL;
    }
    return;
}

static void DsCloseSecIdxCtx(DrRunCtxT *drRunCtx)
{
    if (drRunCtx->currSecIndexRunCtx != NULL) {
        TupleBufRelease(&drRunCtx->currSecIndexRunCtx->tupleBuf);
        IdxClose(drRunCtx->currSecIndexRunCtx);
        drRunCtx->currSecIndexRunCtx = NULL;
    }
    return;
}

static ALWAYS_INLINE void DsCloseCursor(DrRunCtxT *drRunCtx)
{
    DirectAccessRunCtxT *drRunCtxBase = &drRunCtx->base;
    if (SECUREC_LIKELY(drRunCtxBase->chCursor != NULL)) {
        Status ret = DsAcqLatchForDirectRead(
            drRunCtxBase->labelLatch, drRunCtxBase->labelLatchVersionId, drRunCtxBase->sessionCtx);
        if (ret != GMERR_OK) {
            ChScanCursorDestroy(drRunCtxBase->chRunCtx, drRunCtxBase->chCursor);
        } else {
            ChLabelEndScan(drRunCtxBase->chRunCtx, drRunCtxBase->chCursor);
            DsReleaseLatchForDirectRead(drRunCtxBase->labelLatch, drRunCtxBase->sessionCtx);
        }
        drRunCtxBase->chCursor = NULL;
    }
    if (SECUREC_UNLIKELY(drRunCtx->htCursor != NULL)) {
        IdxEndScan(drRunCtx->currSecIndexRunCtx, drRunCtx->htCursor);
        drRunCtx->htCursor = NULL;
    }
    Status ret;
    // heapCtx要做缓存，这里只清理heap的cursor，不清理heap的ctx
    if (SECUREC_UNLIKELY(drRunCtx->heapCursor != NULL)) {
        ret = DsAcqLatchForDirectRead(
            drRunCtxBase->labelLatch, drRunCtxBase->labelLatchVersionId, drRunCtxBase->sessionCtx);
        if (ret != GMERR_OK) {  // 加锁失败只释放资源不操作heap
            HeapLabelReleaseScan(drRunCtxBase->heapRunCtx, drRunCtx->heapCursor);
        } else {
            HeapLabelEndScan(drRunCtxBase->heapRunCtx, drRunCtx->heapCursor);
            DsReleaseLatchForDirectRead(drRunCtxBase->labelLatch, drRunCtxBase->sessionCtx);
        }
        drRunCtx->heapCursor = NULL;
    }
    // 以下逻辑适用于二级索引扫描场景
    if (drRunCtx->isHeapCursorRefInc) {
        // 加锁，如果当前表已经被标记删除，不用处理heap
        ret = DsAcqLatchForDirectRead(
            drRunCtxBase->labelLatch, drRunCtxBase->labelLatchVersionId, drRunCtxBase->sessionCtx);
        if (ret == GMERR_OK) {
            HeapLabelScanDecCursorNumAndUnrecordLongCursor(drRunCtxBase->heapRunCtx, false,
                &(ScanCursorOpenerT){
                    g_gmdbCltInstance.pid, drRunCtx->threadIdOfCreateCursor, drRunCtx->timeOfCreateCursor});
            DsReleaseLatchForDirectRead(drRunCtxBase->labelLatch, drRunCtxBase->sessionCtx);
        }
        drRunCtx->isHeapCursorRefInc = false;
        drRunCtx->timeOfCreateCursor = 0;
        drRunCtx->threadIdOfCreateCursor = 0;
    }
    return;
}

inline void DirectReadCtxCloseHeapCursor(DrRunCtxT *drRunCtx)
{
    DsCloseCursor(drRunCtx);
    DirectAccessRunCtxT *drRunCtxBase = &drRunCtx->base;
    if (SECUREC_UNLIKELY(drRunCtxBase->heapRunCtx != NULL)) {
        HeapLabelResetCtx(drRunCtxBase->heapRunCtx);
    }
}

static ALWAYS_INLINE void DsCloseCursorNoLock(DrRunCtxT *drRunCtx)
{
    DirectAccessRunCtxT *drRunCtxBase = &drRunCtx->base;
    if (SECUREC_LIKELY(drRunCtxBase->chCursor != NULL)) {
        ChLabelEndScan(drRunCtxBase->chRunCtx, drRunCtxBase->chCursor);
        drRunCtxBase->chCursor = NULL;
    }
    if (SECUREC_UNLIKELY(drRunCtx->htCursor != NULL)) {
        IdxEndScan(drRunCtx->currSecIndexRunCtx, drRunCtx->htCursor);
        drRunCtx->htCursor = NULL;
    }
    // heapCtx要做缓存，这里只清理heap的cursor，不清理heap的ctx
    if (SECUREC_UNLIKELY(drRunCtx->heapCursor != NULL)) {
        HeapLabelEndScan(drRunCtxBase->heapRunCtx, drRunCtx->heapCursor);
        drRunCtx->heapCursor = NULL;
    }
    // 以下逻辑适用于二级索引扫描场景
    if (SECUREC_UNLIKELY(drRunCtx->isHeapCursorRefInc)) {
        // 加锁，如果当前表已经被标记删除，不用处理heap
        HeapLabelScanDecCursorNumAndUnrecordLongCursor(drRunCtxBase->heapRunCtx, false,
            &(ScanCursorOpenerT){
                g_gmdbCltInstance.pid, drRunCtx->threadIdOfCreateCursor, drRunCtx->timeOfCreateCursor});
        drRunCtx->isHeapCursorRefInc = false;
        drRunCtx->timeOfCreateCursor = 0;
        drRunCtx->threadIdOfCreateCursor = 0;
    }
}

inline void DirectReadCtxCloseHeapCursorNoLock(DrRunCtxT *drRunCtx)
{
    DsCloseCursorNoLock(drRunCtx);
    DirectAccessRunCtxT *drRunCtxBase = &drRunCtx->base;
    if (SECUREC_UNLIKELY(drRunCtxBase->heapRunCtx != NULL)) {
        HeapLabelResetCtx(drRunCtxBase->heapRunCtx);
    }
}

inline void DirectReadCtxCloseHeapCursorOpt(DrRunStructOptCtxT *drRunCtx)
{
    DirectAccessStructOptRunCtxT *drRunCtxBase = &drRunCtx->base;
    if (SECUREC_UNLIKELY(drRunCtxBase->heapRunCtx != NULL)) {
        HeapLabelResetCtx(drRunCtxBase->heapRunCtx);
    }
}

void DirectReadCtxClose(DrRunCtxT *drRunCtx, DirectAccessCloseTypeE type)
{
    if (drRunCtx == NULL) {
        return;
    }
    DirectAccessRunCtxT *drRunCtxBase = &drRunCtx->base;
    switch (type) {
        case DIRECT_ACCESS_ONLY_RESET_HEAP:  // MainStoreLookUpHash适用场景
            if (drRunCtxBase->heapRunCtx != NULL) {
                HeapLabelResetCtx(drRunCtxBase->heapRunCtx);
            }
            return;
        case DIRECT_ACCESS_CLOSE_HEAP_CURSOR:  // 直连读缓存场景
            DirectReadCtxCloseHeapCursor(drRunCtx);
            return;
        case DIRECT_ACCESS_CLOSE_INDEXCTX:  // 索引改变场景
            DsCloseCursor(drRunCtx);
            DsCloseSecIdxCtx(drRunCtx);
            DsClosePkCtx(drRunCtx);
            DsCloseHashClusterCtx(drRunCtx);
            if (drRunCtxBase->heapRunCtx != NULL) {
                HeapLabelResetCtx(drRunCtxBase->heapRunCtx);
            }
            drRunCtxBase->pkPrepareFlag = false;
            return;
        case DIRECT_ACCESS_CLOSE_HEAP_CURSOR_CTX:  // 直连读缓存清理场景
            DsCloseCursor(drRunCtx);
            DsCloseSecIdxCtx(drRunCtx);
            DsClosePkCtx(drRunCtx);
            DsCloseHashClusterCtx(drRunCtx);
            DsCloseHeapCtx(drRunCtx);
            drRunCtxBase->pkPrepareFlag = false;
            drRunCtx->dsFetchArgs.vertexScanArgs.filter = NULL;
            drRunCtx->dsFetchArgs.vertexScanArgs.vertex = NULL;
            drRunCtx->dsFetchArgs.limitCount = 0;
            drRunCtx->dsFetchArgs.totalFetchedRowCnt = 0;
            drRunCtx->dsFetchHandle.indexNotOpen = false;
            return;
        default:
            return;
    }
}

void DirectReadCtxCloseOpt(DrRunStructOptCtxT *drRunCtx, DirectAccessCloseTypeE type)
{
    if (drRunCtx == NULL) {
        return;
    }
    DirectAccessStructOptRunCtxT *drRunCtxBase = &drRunCtx->base;
    switch (type) {
        case DIRECT_ACCESS_ONLY_RESET_HEAP:  // MainStoreLookUpHash适用场景
            if (drRunCtxBase->heapRunCtx != NULL) {
                HeapLabelResetCtx(drRunCtxBase->heapRunCtx);
            }
            return;
        case DIRECT_ACCESS_CLOSE_HEAP_CURSOR:  // 直连读缓存场景
            DirectReadCtxCloseHeapCursorOpt(drRunCtx);
            return;
        case DIRECT_ACCESS_CLOSE_INDEXCTX:  // 索引改变场景
            DsClosePkCtxOpt(drRunCtx);
            if (drRunCtxBase->heapRunCtx != NULL) {
                HeapLabelResetCtx(drRunCtxBase->heapRunCtx);
            }
            drRunCtxBase->pkPrepareFlag = false;
            return;
        case DIRECT_ACCESS_CLOSE_HEAP_CURSOR_CTX:  // 直连读缓存清理场景
            DsClosePkCtxOpt(drRunCtx);
            DsCloseHeapCtxOpt(drRunCtx);
            drRunCtxBase->pkPrepareFlag = false;
            drRunCtx->dsFetchArgs.vertexScanArgs.filter = NULL;
            drRunCtx->dsFetchArgs.vertexScanArgs.vertex = NULL;
            drRunCtx->dsFetchArgs.limitCount = 0;
            drRunCtx->dsFetchArgs.totalFetchedRowCnt = 0;
            drRunCtx->dsFetchHandle.indexNotOpen = false;
            return;
        default:
            return;
    }
}

Status FixBufferCopy(FixBufferT *directReadBuff, const uint8_t *buf, const uint32_t bufSize)
{
    uint32_t maxLength = FixBufGetMaxLenLimit(directReadBuff);
    if (SECUREC_UNLIKELY(sizeof(uint32_t) + SIZE_ALIGN4(bufSize) > maxLength - FixBufGetPos(directReadBuff))) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Fixbuf copy, buf len limit is %" PRIu32 ".", maxLength);
        return GMERR_OUT_OF_MEMORY;
    }
    Status ret = FixBufPutUint32(directReadBuff, bufSize);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_AND_SET_LASERR(ret, "Fixbuf copy. put uint32.");
        return ret;
    }
    ret = FixBufPutData(directReadBuff, buf, bufSize);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_AND_SET_LASERR(ret, "Fixbuf copy. put data.");
        return ret;
    }
    return GMERR_OK;
}

#ifdef __cplusplus
}
#endif
