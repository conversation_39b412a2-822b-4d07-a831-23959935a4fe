/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2023. All rights reserved.
 * Description: Storage Access Method for directwrite
 * Author:
 * Create:
 */

#include "clt_da_write_index.h"
#include "clt_da_write.h"
#include "clt_da_write_pubsub_status_merge.h"
#include "clt_da_write_respool.h"
#include "clt_stmt.h"
#include "clt_graph_filter.h"
#include "clt_stmt_extend.h"
#include "clt_resource.h"
#include "db_memcpy.h"
#include "dm_meta_prop_label.h"
#include "dm_meta_rsm_basic.h"
#include "clt_da_handle.h"
#include "ee_key_cmp.h"
#include "ee_statistic.h"
#include "se_define.h"
#include "se_heap_base.h"
#include "se_clustered_hash_label_base.h"

inline static DwLabelStatisticValueT *DwGetStatisticValue(DwRunCtxT *dwRunCtx, uint8_t partitionId)
{
    uint8_t idx = (partitionId == DB_INVALID_UINT8) ? 0 : partitionId;
    return &dwRunCtx->labelStatistics->statisticsValue[idx];
}

// 逻辑计数更新封装EE
inline static void DwUpdateStatics(DwTupleStateE old, DwTupleStateE new, DwLabelStatisticValueT *value)
{
    QryUpdateStatics((QryTupleStateE)old, (QryTupleStateE) new, (QryLabelStatisticValueT *)value);
}

inline static Status DwGetTupleState(TupleT tuple, DmVertexLabelT *vertexLabel, DwTupleStateE *state)
{
    return QryGetTupleState(tuple, vertexLabel, (QryTupleStateE *)state);
}

inline static void DwUpdateLogicCnt(DwRunCtxT *dwRunCtx, DwTupleStateE old, DwTupleStateE new, uint8_t partitionId)
{
    DmVertexLabelT *vertexLabel = DwGetVertexLabel(dwRunCtx);
    if (DmVertexLabelIsNormalLabel(vertexLabel)) {
        DwUpdateStatics(old, new, DwGetStatisticValue(dwRunCtx, partitionId));
    }
}

Status DwRecordOldTupleState(DwRunCtxT *dwRunCtx, TupleT oldTuple)
{
    DmVertexLabelT *vertexLabel = DwGetVertexLabel(dwRunCtx);
    DwTupleStateE state = DW_TUPLE_NULL;
    Status ret = DwGetTupleState(oldTuple, vertexLabel, &state);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    dwRunCtx->oldTupleState = state;
    return ret;
}

#ifdef FEATURE_RSMEM
Status RsmSetLiteTrxUndo(SeRunCtxHdT seRunCtx, DmRsmInfoT *rsmInfo, bool isRecovery)
{
    UndoLiteRecordT *undoLiteRec = NULL;
    RsmTableInfoT *rsmTableInfo = (RsmTableInfoT *)DbShmPtrToAddr(rsmInfo->rsmTableInfoPtr);
    if (rsmTableInfo == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED,
            "Get rsmTable info addr, (segId: %" PRIu32 ", offset: %" PRIu32 ").", rsmInfo->rsmTableInfoPtr.segId,
            rsmInfo->rsmTableInfoPtr.offset);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    if (DbIsShmPtrValid(rsmTableInfo->undoLiteRecPtr)) {
        undoLiteRec = DbShmPtrToAddr(rsmTableInfo->undoLiteRecPtr);
        if (undoLiteRec == NULL) {
            DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "rsm-LiteUndo addr:(%" PRIu32 ", %" PRIu32 ")",
                rsmTableInfo->undoLiteRecPtr.segId, rsmTableInfo->undoLiteRecPtr.offset);
            return GMERR_UNEXPECTED_NULL_VALUE;
        }
    } else {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "rsm-LiteUndo ptr inv, rsm-LiteUndo addr:(%" PRIu32 ", %" PRIu32 ")",
            rsmTableInfo->undoLiteRecPtr.segId, rsmTableInfo->undoLiteRecPtr.offset);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    TrxLiteSetUndoLiteRec(seRunCtx->trx, undoLiteRec, isRecovery);
    return GMERR_OK;
}

Status RsmSetRsmUndo(SeRunCtxHdT seRunCtx, ConcurrencyControlE ccType, DmRsmInfoT *rsmInfo)
{
    if (DmIsLabelLatchMode(ccType) || ccType == CONCURRENCY_CONTROL_READ_UNCOMMIT) {
        // 当前只支持轻量化事务，由表latch保证rsmUndo的并发
        RsmTableInfoT *rsmTableInfo = (RsmTableInfoT *)DbShmPtrToAddr(rsmInfo->rsmTableInfoPtr);
        if (rsmTableInfo == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED,
                "Get rsmTable info addr, (segId: %" PRIu32 ", offset: %" PRIu32 ").", rsmInfo->rsmTableInfoPtr.segId,
                rsmInfo->rsmTableInfoPtr.offset);
            return GMERR_MEMORY_OPERATE_FAILED;
        }
        SeTransSetRsmUndo(seRunCtx, &rsmTableInfo->rsmUndoRec);
        return GMERR_OK;
    }

    DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Inv ccType:%" PRIu32 "", ccType);
    return GMERR_INVALID_PARAMETER_VALUE;
}

Status SetRsmUndoForVertexLabel(SeRunCtxT *seRunCtx, DmVertexLabelT *vertexLabel)
{
    DB_POINTER2(seRunCtx, vertexLabel);
    if (!vertexLabel->metaCommon.isUseRsm) {
        return GMERR_OK;
    }
    DmRsmInfoT *rsmInfo = MEMBER_PTR(vertexLabel->commonInfo, rsmInfo);

    Status ret = RsmSetLiteTrxUndo(seRunCtx, rsmInfo, false);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = RsmSetRsmUndo(seRunCtx, vertexLabel->commonInfo->heapInfo.ccType, rsmInfo);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Set trx lite undo rec, VLName=%s.", vertexLabel->metaCommon.metaName);
        return ret;
    }
    return ret;
}
#endif

Status HeapOpenAndInitHeapRunCtx(DwRunCtxT *dwRunCtx)
{
    Status ret;
    DmVertexLabelT *vertexLabel = dwRunCtx->dwArgs.vertexArgs.cltCataLabel->vertexLabel;
    if (SECUREC_UNLIKELY(dwRunCtx->base.heapCtxMem == NULL)) {
        VertexLabelCommonInfoT *commonInfo = vertexLabel->commonInfo;
        ContainerInitCfgT initCfg = {
            .containerType = CONTAINER_HEAP,
            .labelShmAddr = commonInfo->heapInfo.heapShmAddr,
            .seRunCtx = (SeRunCtxHdT)dwRunCtx->base.seRunCtxHandle,
            .dmInfo = (void *)vertexLabel,
            .isBackGround = false,
            .isUseRsm = vertexLabel->metaCommon.isUseRsm,
        };
        ret = ContainerAllocAndInitRunCtx((ContainerHdlT *)&(dwRunCtx->base.heapCtxMem), &initCfg);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            dwRunCtx->base.heapCtxMem = NULL;
            DB_LOG_ERROR(ret, "dw alloc and init heap run ctx.");
            return ret;
        }
    }

    // 服务端的函数指针在客户端无法使用，必须特殊处理heap中挂的钩子函数
    dwRunCtx->base.heapRunCtx = dwRunCtx->base.heapCtxMem;

#ifdef FEATURE_RSMEM
    // 初始化warm reboot相关资源
    HpRunHdlT heapRunHdl = dwRunCtx->base.heapRunCtx;
    ret = SetRsmUndoForVertexLabel(heapRunHdl->seRunCtx, vertexLabel);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "clt set rsm undo for VL.");
        goto EXIT;
    }
#endif
    ContainerOpenCfgT openCfg = {
        .opType = GetDwOpTypeToHeapOpType(dwRunCtx->dwArgs.currOpType),
        .usrMemCtx = dwRunCtx->base.clientMemCtx,
    };
    ret = ContainerLabelOpen(dwRunCtx->base.heapRunCtx, &openCfg);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Dw open heap label.");
        goto EXIT;
    }
    return GMERR_OK;
EXIT:
    ContainerReleaseRunCtx(dwRunCtx->base.heapCtxMem);
    dwRunCtx->base.heapCtxMem = NULL;
    dwRunCtx->base.heapRunCtx = NULL;
    return ret;
}

inline static void DwSetClusterInitCfg(DwRunCtxT *dwRunCtx, ContainerInitCfgT *initCfg)
{
    CltCataLabelT *cltCataLabel = dwRunCtx->dwArgs.vertexArgs.cltCataLabel;
    DmVertexLabelT *vertexLabel = cltCataLabel->vertexLabel;
    ShmemPtrT containerShmAddr = vertexLabel->metaVertexLabel->containerShmAddr;
    VertexLabelCommonInfoT *commonInfo = vertexLabel->commonInfo;
    initCfg->containerType = dwRunCtx->base.containerType;
    initCfg->labelShmAddr = containerShmAddr;
    initCfg->seRunCtx = (SeRunCtxHdT)dwRunCtx->base.seRunCtxHandle;
    initCfg->dmInfo = (void *)vertexLabel;
    initCfg->vertex = dwRunCtx->dwArgs.vertexArgs.vertex;
    initCfg->tupleBuf = &dwRunCtx->oldTupleBuf;
    initCfg->userData = NULL;
    initCfg->indexKeyInfo = NULL;
    initCfg->keyCmp = QryHeapBufCompare4ClusteredHash;
    initCfg->secIndexNum = vertexLabel->metaVertexLabel->secIndexNum;
    initCfg->secIdxCtx = dwRunCtx->secIdxCtx;
    initCfg->labelLatchVersionId = commonInfo->vertexLabelLatchVersionId;
    initCfg->labelRWLatch = dwRunCtx->base.labelLatch;
    initCfg->pkIndex = MEMBER_PTR(vertexLabel->metaVertexLabel, pkIndex);
}

#ifdef ART_CONTAINER
Status ArtOpenAndInitArtRunCtx(DwRunCtxT *dwRunCtx)
{
    // 获取 art cluster hash 分配和初始化所需的 config 信息
    Status ret = GMERR_OK;

    if (SECUREC_UNLIKELY(dwRunCtx->base.artCtxMem == NULL)) {
        ContainerInitCfgT initCfg = {0};
        DwSetClusterInitCfg(dwRunCtx, &initCfg);
        ret = ContainerAllocAndInitRunCtx((ContainerHdlT *)&(dwRunCtx->base.artCtxMem), &initCfg);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            dwRunCtx->base.artRunCtx = NULL;
            DB_LOG_ERROR(ret, "dw alloc and init art container run ctx.");
            return ret;
        }
    }
    dwRunCtx->base.artRunCtx = (ArtContainerRunHdlT)dwRunCtx->base.artCtxMem;

    ContainerOpenCfgT openCfg = {
        .vertex = dwRunCtx->dwArgs.vertexArgs.vertex,
        .opType = GetDwOpTypeToHeapOpType(dwRunCtx->dwArgs.currOpType),
        .usrMemCtx = dwRunCtx->base.clientMemCtx,
    };
    ret = ContainerLabelOpen((ContainerHdlT)dwRunCtx->base.artRunCtx, &openCfg);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        ContainerReleaseRunCtx(dwRunCtx->base.artCtxMem);
        dwRunCtx->base.artCtxMem = NULL;
        dwRunCtx->base.artRunCtx = NULL;
        DB_LOG_ERROR(ret, "dw open art clustered.");
        return ret;
    }
    return ret;
}
#endif

Status ChOpenAndInitChRunCtx(DwRunCtxT *dwRunCtx)
{
    // 获取 clustered hash 分配和初始化所需的 config 信息
    Status ret = GMERR_OK;

    if (SECUREC_UNLIKELY(dwRunCtx->base.chCtxMem == NULL)) {
        ContainerInitCfgT initCfg = {0};
        DwSetClusterInitCfg(dwRunCtx, &initCfg);
        ret = ContainerAllocAndInitRunCtx((ContainerHdlT *)&(dwRunCtx->base.chCtxMem), &initCfg);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            dwRunCtx->base.chRunCtx = NULL;
            DB_LOG_ERROR(ret, "dw alloc and init clustered hash run ctx.");
            return ret;
        }
    }
    dwRunCtx->base.chRunCtx = (ChLabelRunHdlT)dwRunCtx->base.chCtxMem;

#ifdef FEATURE_RSMEM
    // 初始化warm reboot相关资源
    DmVertexLabelT *vertexLabel = dwRunCtx->dwArgs.vertexArgs.cltCataLabel->vertexLabel;
    ret = SetRsmUndoForVertexLabel(dwRunCtx->base.chRunCtx->seRunCtx, vertexLabel);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "clt set rsm undo for VL in Ch.");
        goto EXIT;
    }
#endif

    DmVertexT *vertex = dwRunCtx->dwArgs.vertexArgs.vertex;
    ContainerOpenCfgT openCfg = {
        .vertex = vertex,
        .opType = GetDwOpTypeToHeapOpType(dwRunCtx->dwArgs.currOpType),
        .usrMemCtx = dwRunCtx->base.clientMemCtx,
    };
    ret = ContainerLabelOpen((ContainerHdlT)dwRunCtx->base.chRunCtx, &openCfg);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "dw open clustered hash.");
        goto EXIT;
    }
    return GMERR_OK;
EXIT:
    ContainerReleaseRunCtx(dwRunCtx->base.chCtxMem);
    dwRunCtx->base.chCtxMem = NULL;
    dwRunCtx->base.chRunCtx = NULL;
    return ret;
}

void ReleaseHeapCtx(DwRunCtxT *dwRunCtx)
{
    DB_POINTER(dwRunCtx);
    if (dwRunCtx->base.containerType == CONTAINER_CLUSTERED_HASH && dwRunCtx->base.chCtxMem != NULL) {
        ContainerResetCtx(dwRunCtx->base.chCtxMem);
        ContainerResetLastCompareTupleBufFunc(dwRunCtx->base.chCtxMem);
        ContainerReleaseRunCtx(dwRunCtx->base.chCtxMem);
        dwRunCtx->base.chCtxMem = NULL;
        dwRunCtx->base.chRunCtx = NULL;
    } else if (dwRunCtx->base.containerType == CONTAINER_HEAP && dwRunCtx->base.heapCtxMem != NULL) {
        ContainerResetCtx(dwRunCtx->base.heapCtxMem);
        ContainerResetLastCompareTupleBufFunc(dwRunCtx->base.heapCtxMem);
        ContainerReleaseRunCtx(dwRunCtx->base.heapCtxMem);
        dwRunCtx->base.heapCtxMem = NULL;
        dwRunCtx->base.heapRunCtx = NULL;
    }
#ifdef ART_CONTAINER
    else if (dwRunCtx->base.containerType == CONTAINER_ART && dwRunCtx->base.artCtxMem != NULL) {
        ContainerResetCtx(dwRunCtx->base.artCtxMem);
        ContainerResetLastCompareTupleBufFunc(dwRunCtx->base.artCtxMem);
        ContainerReleaseRunCtx(dwRunCtx->base.artCtxMem);
        dwRunCtx->base.artCtxMem = NULL;
        dwRunCtx->base.artRunCtx = NULL;
    }
#endif
}

static void SetTupleBufCheckVersion(CltCataLabelT *cataLabel, DmCheckInfoT *checkInfo, HeapTupleBufT *heapTupleBuf)
{
    // 插入前，设置该tuple的checkVersion，确保和vertexLabel中的checkVersion保持一致
    DmVertexBufSetSysPrope(CHECK_VERSION,
        &(DmValueT){.type = DB_DATATYPE_UINT8, .value.ubyteValue = checkInfo->version.version},
        cataLabel->vertexLabel->vertexDesc, heapTupleBuf->buf);
}

static Status TupleBufInsertInner(DwRunCtxT *dwRunCtx, HeapTupleBufT *heapTupleBuf)
{
    Status ret = GMERR_OK;
    DmVertexT *vertex = dwRunCtx->dwArgs.vertexArgs.vertex;
    if (dwRunCtx->base.containerType == CONTAINER_CLUSTERED_HASH) {
        IndexKeyT hashKey;
        DmIndexKeyBufInfoT *keyInfo = NULL;
        uint32_t indexId = ChLabelGetIndexId(dwRunCtx->base.chRunCtx);
        DmVertexGetVertexKeyBuf(vertex, (uint8_t **)&hashKey.keyData);
        if (SECUREC_UNLIKELY((ret = DmVertexGetIndexKeyInfo(vertex, indexId, &keyInfo)) != GMERR_OK)) {
            DB_LOG_ERROR(ret, "Get index key, indexId = %" PRIu32 ".", indexId);
            return ret;
        }
        DmGetKeyBufFromVertexBuf(keyInfo, heapTupleBuf->buf, hashKey.keyData, &hashKey.keyLen);
        ret = ChLabelInsert(dwRunCtx->base.chRunCtx, hashKey, heapTupleBuf, &dwRunCtx->tupleAddr);
        // To distinguish between primary conflicts and secondary index conflicts
        if (ret == GMERR_UNIQUE_VIOLATION) {
            DB_LOG_AND_SET_LASERR(GMERR_PRIMARY_KEY_VIOLATION,
                "dw insert data for chLabel violates primary unique constraint restriction.");
            ret = GMERR_PRIMARY_KEY_VIOLATION;
        }
        return ret;
    } else if (dwRunCtx->base.containerType == CONTAINER_HEAP) {
        return HeapLabelInsertHpTupleBuffer(dwRunCtx->base.heapRunCtx, heapTupleBuf, &dwRunCtx->tupleAddr);
    }
#ifdef ART_CONTAINER
    else if (dwRunCtx->base.containerType == CONTAINER_ART) {
        if (!dwRunCtx->base.isRealCluster) {
            return ArtLabelAllocMem(ArtContainerGetRunCtxAllocBufMemCtx(dwRunCtx->base.artRunCtx),
                ArtContainerGetRunCtxAllocBufMemCtxIdx(dwRunCtx->base.artRunCtx), heapTupleBuf->buf,
                heapTupleBuf->bufSize, &dwRunCtx->tupleAddr);
        }
        return GMERR_OK;
    }
#endif
    else {
        DB_ASSERT(false);
    }
    return ret;
}

/***** insert *****/
static Status TupleBufInsert(DwRunCtxT *dwRunCtx, HeapTupleBufT *heapTupleBuf)
{
    Status ret = GMERR_OK;
    CltCataLabelT *cataLabel = dwRunCtx->dwArgs.vertexArgs.cltCataLabel;
    DmVertexLabelT *vertexLabel = cataLabel->vertexLabel;
    if (SECUREC_UNLIKELY(dwRunCtx->dwArgs.vertexArgs.partitionId == DM_MAX_PARTITION_ID)) {
        uint8_t partitionId = 0;
        ret = DwGetPartitionId(vertexLabel, cataLabel, heapTupleBuf, &partitionId);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
        dwRunCtx->dwArgs.vertexArgs.partitionId = partitionId;
    }
    DmCheckInfoT *checkInfo = DwGetCheckInfoById(cataLabel, dwRunCtx->dwArgs.vertexArgs.partitionId);
    SetTupleBufCheckVersion(cataLabel, checkInfo, heapTupleBuf);
    if (dwRunCtx->stmgSubData.isStMgLabel) {
        if ((ret = DwStMgInitInsertNode(&dwRunCtx->stmgSubData, cataLabel, heapTupleBuf)) != GMERR_OK) {
            return ret;
        }
    }

    return TupleBufInsertInner(dwRunCtx, heapTupleBuf);
}

Status DwTupleInsertInner(GmcStmtT *stmt, DwRunCtxT *dwRunCtx, DmVertexT *vertex)
{
    // 申请资源列
    Status ret = DwAllocRes(stmt, dwRunCtx, vertex);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "dw alloc resource.");
        return ret;
    }

    HeapTupleBufT *heapTupleBuf = &dwRunCtx->dwArgs.vertexArgs.vertexBuf;
    if (SECUREC_UNLIKELY(!dwRunCtx->useStructSeriBuf)) {
        // 序列化vertex到heapTupleBuf
        ret = DmSerializeVertex(vertex, (uint8_t **)&heapTupleBuf->buf, (uint32_t *)&heapTupleBuf->bufSize);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(ret, "Seri vertex before dw insert.");
            return ret;
        }
    }
    // 插入数据
    ret = TupleBufInsert(dwRunCtx, heapTupleBuf);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "dw insert heap tuple.");
        return ret;
    }
    if (SECUREC_UNLIKELY(dwRunCtx->stmgSubData.isStMgLabel)) {
        DwSetStMgPubNodeTuple(dwRunCtx->stmgSubData.insertNode, dwRunCtx->tupleAddr);
    }

    // 插入索引
    ret = DwAllIdxesInsert(dwRunCtx, heapTupleBuf);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "dw insert indexes.");
        return ret;
    }
    return GMERR_OK;
}

// 数据插入最外层入口
Status DwTupleInsert(GmcStmtT *stmt, DmVertexT *vertex)
{
    DwRunCtxT *dwRunCtx = stmt->dwRunCtx;
    CltCataLabelT *cataLabel = dwRunCtx->dwArgs.vertexArgs.cltCataLabel;
    DmVertexLabelT *vertexLabel = cataLabel->vertexLabel;
    char *metaName = MEMBER_PTR(&vertexLabel->metaCommon, metaName);
    Status ret = DwTupleInsertInner(stmt, dwRunCtx, vertex);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "dw insert heapTuple, labelName:%s.", metaName);
        return ret;
    }
    // 更新逻辑计数。Insert流程里会填充partitionId
    uint8_t partitionId = dwRunCtx->dwArgs.vertexArgs.partitionId;
    DwUpdateLogicCnt(dwRunCtx, DW_TUPLE_NULL, DW_TUPLE_NORMAL, partitionId);
    stmt->affectRows = 1;
    dwRunCtx->subsRowData.newTupleBuf = dwRunCtx->dwArgs.vertexArgs.vertexBuf;
    return ret;
}

/***** update *****/
// 获取一个tuple buffer，缓存在stmt上，避免每次都要申请和释放
static Status GetCachedTupBuf(GmcStmtT *stmt, uint32_t tupBufSize, uint8_t **buf)
{
    DwRunCtxT *dwRunCtx = stmt->dwRunCtx;
    if (tupBufSize > dwRunCtx->cachedTupBufSize || dwRunCtx->cachedTupBuf == NULL) {
        if (dwRunCtx->cachedTupBuf != NULL) {
            DbDynMemCtxFree(stmt->memCtx, dwRunCtx->cachedTupBuf);
            dwRunCtx->cachedTupBufSize = 0;
            dwRunCtx->cachedTupBuf = NULL;
        }

        // 在直连写执行更新操作时申请内存，在GmcFreeStmt()时释放
        dwRunCtx->cachedTupBuf = (uint8_t *)DbDynMemCtxAlloc(stmt->memCtx, tupBufSize);
        if (dwRunCtx->cachedTupBuf == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "dw alloc tupleBuf, size|%" PRIu32 "|.", tupBufSize);
            return GMERR_OUT_OF_MEMORY;
        }
        dwRunCtx->cachedTupBufSize = tupBufSize;
    }
    DB_ASSERT(dwRunCtx->cachedTupBuf != NULL);
    (void)memset_s(dwRunCtx->cachedTupBuf, tupBufSize, 0x00, tupBufSize);
    *buf = dwRunCtx->cachedTupBuf;
    return GMERR_OK;
}

static Status UpdateStMgRecordCount(DwRunCtxT *dwRunCtx, HeapTupleBufT *heapTupleBuf)
{
    uint8_t partitionId = 0;
    Status ret = DwGetPartitionId(DwGetVertexLabel(dwRunCtx), DwGetCltCataLabel(dwRunCtx), heapTupleBuf, &partitionId);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    // 更新逻辑计数
    DwUpdateLogicCnt(dwRunCtx, dwRunCtx->oldTupleState, DW_TUPLE_NORMAL, partitionId);
    return GMERR_OK;
}

/*
 * 不指定tupleInfo默认使用vertexArgs的tuple buf。
 * 直连写索引更新用的是heapTupleBuf而不是vertex，注意保证本函数的heapTupleBuf和后续索引更新用的heapTupleBuf一致。
 */
Status DwHeapUpdate(DwRunCtxT *dwRunCtx, DwTupleInfoT *tupleInfo, bool isSerialize)
{
    DB_POINTER(dwRunCtx);
    Status ret;
    HeapTupleBufT *heapTupleBuf =
        (tupleInfo == NULL) ? &dwRunCtx->dwArgs.vertexArgs.vertexBuf : &tupleInfo->heapTupleBuf;
    DmVertexT *vertex = (tupleInfo == NULL) ? dwRunCtx->dwArgs.vertexArgs.vertex : tupleInfo->vertex;
    if (isSerialize) {
        ret = DmSerializeVertex(vertex, (uint8_t **)&heapTupleBuf->buf, (uint32_t *)&heapTupleBuf->bufSize);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
    }
    bool isAged = false;
    ret = DwTupleCheckVersionUpdate(dwRunCtx, heapTupleBuf, &isAged);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    if (dwRunCtx->base.containerType == CONTAINER_CLUSTERED_HASH) {
        ret = ChLabelUpdate(dwRunCtx->base.chRunCtx, heapTupleBuf, dwRunCtx->tupleAddr);
    } else if (dwRunCtx->base.containerType == CONTAINER_HEAP) {
        HeapUpdOutPara out = {.isUndoBypass = NULL};
        ret = HeapLabelUpdateWithHpTupleBuf(dwRunCtx->base.heapRunCtx, heapTupleBuf, dwRunCtx->tupleAddr, &out);
    }
#ifdef ART_CONTAINER
    else if (dwRunCtx->base.containerType == CONTAINER_ART) {
        IndexKeyT pkIdxKey = {0};
        DmIndexKeyGetKeyBuf(dwRunCtx->idxKey, (uint8_t **)&pkIdxKey.keyData, &pkIdxKey.keyLen);
        ret = ArtContainerUpdate(dwRunCtx->base.artRunCtx, pkIdxKey, heapTupleBuf, HEAP_INVALID_ADDR, NULL);
    }
#endif
    else {
        ret = GMERR_UNEXPECTED_NULL_VALUE;
    }
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    // 更新逻辑计数。
    uint8_t partitionId;
    TupleT oldTuple = TupleBufGet(&dwRunCtx->oldTupleBuf);
    ret = DwGetPartitionId(DwGetVertexLabel(dwRunCtx), DwGetCltCataLabel(dwRunCtx), &oldTuple, &partitionId);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Get partition Id when delete tuple of clusteredHash.");
        return ret;
    }
    // 更新逻辑计数
    DwUpdateLogicCnt(dwRunCtx, dwRunCtx->oldTupleState, DW_TUPLE_NORMAL, partitionId);
    return ret;
}

Status VertexBufMergeInner(
    GmcStmtT *stmt, DmVertexT *deltaVertex, HeapTupleBufT *oldTupleBuf, HeapTupleBufT *newTupleBuf)
{
    Status ret;
    DwRunCtxT *dwRunCtx = stmt->dwRunCtx;
    CltCataLabelT *cataLabel = dwRunCtx->dwArgs.vertexArgs.cltCataLabel;
    DmVertexLabelT *vertexLabel = cataLabel->vertexLabel;
    bool isFixed = false;
    if (vertexLabel->metaVertexLabel->labelLevel == VERTEX_LEVEL_SIMPLE) {
        isFixed = true;
    } else {
        if ((ret = DmVertexUpdatePropeIsAllFixed(
                 cataLabel->vertexLabel->vertexDesc, cataLabel->vertexLabel, deltaVertex, &isFixed)) != GMERR_OK) {
            return ret;
        }
    }

    if (isFixed && !(vertexLabel->commonInfo->hasUpd)) {
        // 如果是定长的,则新buffer的size应该是由老的buffer一致，这里之后需要判断表升级情况
        newTupleBuf->bufSize = oldTupleBuf->bufSize;
    } else {
        if ((ret = DmVertexGetMergeBufLength(cataLabel->vertexLabel->vertexDesc, deltaVertex, oldTupleBuf->buf,
                 &newTupleBuf->bufSize)) != GMERR_OK) {
            return ret;
        }
    }

    // 为新的tuple buffer申请内存，后续会将老的tuple buffer的数据合并到新的buffer里面
    if ((ret = GetCachedTupBuf(stmt, newTupleBuf->bufSize, &newTupleBuf->buf)) != GMERR_OK) {
        return ret;
    }

    // 新老 tuple buffer合并
    if (isFixed && !(vertexLabel->commonInfo->hasUpd)) {
        DbFastMemcpy(newTupleBuf->buf, newTupleBuf->bufSize, oldTupleBuf->buf, oldTupleBuf->bufSize);
        ret = DmVertexMergeIntoOldBuf(deltaVertex, newTupleBuf->buf);
    } else {
        ret = DmVertexMergeIntoNewBuf(deltaVertex, oldTupleBuf->buf, newTupleBuf->bufSize, newTupleBuf->buf);
    }
    return ret;
}

// 只能merge单条记录，多条记录此函数不适用
Status VertexBufMerge(GmcStmtT *stmt)
{
    Status ret = GMERR_OK;
    DwRunCtxT *dwRunCtx = stmt->dwRunCtx;
    DmVertexT *deltaVertex = dwRunCtx->dwArgs.vertexArgs.vertex;
    CltCataLabelT *cataLabel = dwRunCtx->dwArgs.vertexArgs.cltCataLabel;
    DmVertexLabelT *vertexLabel = cataLabel->vertexLabel;
    char *metaName = MEMBER_PTR(&vertexLabel->metaCommon, metaName);

    DmVertexSetAutoIncProp2Null(deltaVertex, vertexLabel);
    // 获取老的tuple
    HeapTupleBufT oldTuple = TupleBufGet(&dwRunCtx->oldTupleBuf);
    DB_ASSERT(oldTuple.buf != NULL);
    dwRunCtx->subsRowData.oldTupleBuf = oldTuple;

    // 获取新的tuplebuffer，此时buf没有申请，由DwUseCacheTupBuf申请
    HeapTupleBufT *newTupleBuf = &dwRunCtx->dwArgs.vertexArgs.vertexBuf;
    // 在MergeVertexBuf里面会为新的tuple buffer申请内存，在后续的heap更新和索引更新中会用到
    // 因此在此函数作用域内不能释放,由DwExecUpdateInner释放
    if ((ret = VertexBufMergeInner(stmt, deltaVertex, &oldTuple, newTupleBuf)) != GMERR_OK) {
        return ret;
    }

    // 判断partition是否改变
    ret = DmCheckIsPartitionNotChange(
        vertexLabel, oldTuple.buf, oldTuple.bufSize, newTupleBuf->buf, newTupleBuf->bufSize);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Label %s partition not support change.", metaName);
        return ret;
    }
    // heap与deltavetex合并后的数据，对应newTupleBuf。本函数内申请释放
    DmVertexT *newVertex = NULL;
    ret = DmCreateEmptyVertexByDescWithMemCtx(stmt->memCtx, cataLabel->vertexLabel->vertexDesc, &newVertex);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = DmDeSerialize2ExistsVertex(newTupleBuf->buf, newTupleBuf->bufSize, newVertex, CltGetSecurityMode());
    if (ret != GMERR_OK) {
        goto CLEAR;
    }
    if (DmVertexHasMemberKey(newVertex)) {
        if ((ret = DmVertexMemberKeyCheckInUpdate(newVertex, deltaVertex)) != GMERR_OK) {
            DB_LOG_ERROR(ret, "Check vertex member key for label %s during UPDATE.", metaName);
        }
    }

    // 新的vertex出了作用域不在使用，destroy掉。
CLEAR:
    DmDestroyVertex(newVertex);
    return ret;
}

static Status UpdateWithUdiType(DwRunCtxT *dwRunCtx, HeapTupleBufT *heapTupleBuf, DmVertexLabelT *vertexLabel)
{
    Status ret = GMERR_OK;
    if (dwRunCtx->base.isUseClusteredHashTable) {
        ret = ChLabelUpdate(dwRunCtx->base.chRunCtx, heapTupleBuf, dwRunCtx->tupleAddr);
        if (ret != GMERR_OK) {
            char *labelName = MEMBER_PTR(&vertexLabel->metaCommon, metaName);
            DB_LOG_ERROR(ret, "Update clusteredHash tuple in StMg, VL:%s.", labelName);
            return ret;
        }
    } else {
        HeapUpdOutPara out = {.isUndoBypass = NULL};
        ret = HeapLabelUpdateWithHpTupleBuf(dwRunCtx->base.heapRunCtx, heapTupleBuf, dwRunCtx->tupleAddr, &out);
        if (ret != GMERR_OK) {
            char *labelName = MEMBER_PTR(&vertexLabel->metaCommon, metaName);
            DB_LOG_ERROR(ret, "Update heapTuple in StMg, VL:%s.", labelName);
            return ret;
        }
    }
    return GMERR_OK;
}

/*
 * 不指定tupleInfo默认使用vertexArgs的tuple buf。
 * 直连写索引更新用的是heapTupleBuf而不是vertex，注意保证本函数的heapTupleBuf和后续索引更新用的heapTupleBuf一致。
 */
Status DwStMgHeapUpdate(GmcStmtT *stmt, DwTupleInfoT *tupleInfo, bool needSerialized)
{
    Status ret = GMERR_OK;
    DwRunCtxT *dwRunCtx = stmt->dwRunCtx;
    HeapTupleBufT *heapTupleBuf =
        (tupleInfo == NULL) ? &dwRunCtx->dwArgs.vertexArgs.vertexBuf : &tupleInfo->heapTupleBuf;
    DmVertexT *vertex = (tupleInfo == NULL) ? dwRunCtx->dwArgs.vertexArgs.vertex : tupleInfo->vertex;
    TupleT oldTuple = TupleBufGet(&dwRunCtx->oldTupleBuf);
    DmVertexLabelT *vertexLabel = DwGetVertexLabel(dwRunCtx);
    CltCataLabelT *cataLabel = DwGetCltCataLabel(dwRunCtx);
    uint8_t deleteMark = 0;

    DwGetStMgDataDeleteMark(&oldTuple, cataLabel, &deleteMark);
    if (deleteMark != 0) {
        ret = DwAllocRes(stmt, dwRunCtx, dwRunCtx->dwArgs.vertexArgs.vertex);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(ret, "Dw stmg update alloc res.");
            return ret;
        }
    }

    if (needSerialized) {
        ret = DmSerializeVertex(vertex, (uint8_t **)&heapTupleBuf->buf, &heapTupleBuf->bufSize);
        if (ret != GMERR_OK) {
            char *labelName = MEMBER_PTR(&vertexLabel->metaCommon, metaName);
            DB_LOG_ERROR(ret, "Seri vertex when update StMg heapTuple, VL: %s.", labelName);
            return ret;
        }
    }
    bool isAged = false;
    ret = DwTupleCheckVersionUpdate(dwRunCtx, heapTupleBuf, &isAged);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = DwStMgInitUpdateNode(&dwRunCtx->stmgSubData, cataLabel, &oldTuple, heapTupleBuf);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Init update node for StMg heapTuple.");
        return ret;
    }

    ret = UpdateWithUdiType(dwRunCtx, heapTupleBuf, vertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Update StMg heapTuple.");
        return ret;
    }
    return UpdateStMgRecordCount(dwRunCtx, heapTupleBuf);
}

Status MergeAndUpdate(GmcStmtT *stmt, DwRunCtxT *dwRunCtx)
{
    Status ret;
    if (dwRunCtx->base.containerType == CONTAINER_HEAP) {
        ret = HeapLabelResetOpType(dwRunCtx->base.heapRunCtx, HEAP_OPTYPE_UPDATE, false);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
    }
    if ((ret = VertexBufMerge(stmt)) != GMERR_OK) {
        DB_LOG_ERROR(ret, "Merge vertex when dw update .");
        goto CLEAR;
    }
    // 更新heap和索引
    // 使用stmgSubData.vertexLabel来区分是否是状态合并订阅场景
    if (dwRunCtx->stmgSubData.isStMgLabel) {
        ret = DwStMgHeapUpdate(stmt, NULL, false);
    } else {
        ret = DwHeapUpdate(dwRunCtx, NULL, false);
    }

    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Update heapTuple when dw update.");
        goto CLEAR;
    }
    if ((ret = DwSecondaryIdxesUpdate(dwRunCtx)) != GMERR_OK) {
        DB_LOG_ERROR(ret, "Update secondary index when dw update.");
        goto CLEAR;
    }
    stmt->affectRows += 1;
    return GMERR_OK;
CLEAR:
    DwFreeCachedTupBufIfLarge(stmt);
    return ret;
}

Status UpdateSingleVertex(GmcStmtT *stmt, DwRunCtxT *dwRunCtx)
{
    CltCataLabelT *cataLabel = dwRunCtx->dwArgs.vertexArgs.cltCataLabel;
    const char *labelName = MEMBER_PTR(cataLabel->vertexLabel, metaCommon.metaName);
    Status ret = MergeAndUpdate(stmt, dwRunCtx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Dw update tuple, labelName:%s.", labelName);
        return ret;
    }
    if (NeedDwGenerateSubsMessage(&dwRunCtx->labelDef, DM_SUBS_EVENT_UPDATE)) {
        // Memory alloc and free by dm, when opVertex.vertex is released.
        ret = DmSerializeVertex(dwRunCtx->dwArgs.vertexArgs.vertex,
            (uint8_t **)&dwRunCtx->subsRowData.deltaVertexBuf.str, &dwRunCtx->subsRowData.deltaVertexBuf.len);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Seri update delta vertex for pubsub, labelName:%s.", labelName);
            return ret;
        }
    }
    dwRunCtx->subsRowData.newTupleBuf = dwRunCtx->dwArgs.vertexArgs.vertexBuf;
    return GMERR_OK;
}

static Status CheckReplaceStatusVerify(DwRunCtxT *dwRunCtx)
{
    Status ret = GMERR_OK;
    CltCataLabelT *cataLabel = dwRunCtx->dwArgs.vertexArgs.cltCataLabel;
    DmVertexLabelT *vertexLabel = cataLabel->vertexLabel;
    char *labelName = MEMBER_PTR(&vertexLabel->metaCommon, metaName);

    TupleT oldTuple = TupleBufGet(&dwRunCtx->oldTupleBuf);
    DmCheckInfoT *checkInfo = NULL;
    ret = DwGetCheckInfo(vertexLabel, cataLabel, &oldTuple, &checkInfo);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Get checkinfo for dw checkReplace. LabelName: %s.", labelName);
        return ret;
    }
    if (checkInfo->version.checkStatus != DM_CHECK_STATUS_CHECKING) {
        ret = GMERR_TABLE_NOT_IN_CHECKING;
        DB_LOG_AND_SET_LASERR(ret, "Checking status not triggered before dw checkReplace. LabelName: %s.", labelName);
    }
    return ret;
}

// 正常更新记录，但是不生成订阅消息。
Status UpdateSingleVertex4CheckReplace(GmcStmtT *stmt, DwRunCtxT *dwRunCtx)
{
    Status ret = GMERR_OK;
    CltCataLabelT *cataLabel = dwRunCtx->dwArgs.vertexArgs.cltCataLabel;
    const char *labelName = MEMBER_PTR(cataLabel->vertexLabel, metaCommon.metaName);

    // 校验对账状态
    if (SECUREC_UNLIKELY((ret = CheckReplaceStatusVerify(dwRunCtx)) != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Table not checking status when executing dw checkReplace. LabelName: %s.", labelName);
        return ret;
    }
    if (!dwRunCtx->base.isUseClusteredHashTable) {
        ret = HeapLabelResetOpType(dwRunCtx->base.heapRunCtx, HEAP_OPTYPE_UPDATE, false);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
    }
    // 使用前面读出来的oldTupleBuf更新即可。
    DwTupleInfoT tupleInfo = {0};
    tupleInfo.vertex = NULL;
    tupleInfo.heapTupleBuf = TupleBufGet(&dwRunCtx->oldTupleBuf);
    // 更新heap和索引
    // 使用stmgSubData.vertexLabel来区分是否是状态合并订阅场景
    if (dwRunCtx->stmgSubData.isStMgLabel) {
        ret = DwStMgHeapUpdate(stmt, &tupleInfo, false);
    } else {
        ret = DwHeapUpdate(dwRunCtx, &tupleInfo, false);
    }
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Update tuple for dw checkReplace. LabelName: %s.", labelName);
        return ret;
    }
    stmt->affectRows += 1;
    // 将订阅data置空，确保不生成订阅消息。
    dwRunCtx->ageRowData = (DwSubsRowDataT){0};
    dwRunCtx->subsRowData = (DwSubsRowDataT){0};
    return ret;
}

/***** replace *****/
static Status DwChPushAgeRecord(
    DwRunCtxT *dwRunCtx, CltCataLabelT *cataLabel, HeapTupleBufT *heapTupleBuf, uint8_t deleteMark)
{
    bool isTruncated = false;
    Status ret = DwSetAgeDataStatus(dwRunCtx, cataLabel, heapTupleBuf, &isTruncated);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Set dw age data status.");
        return ret;
    }
    if (deleteMark != 0) {
        return GMERR_OK;
    }
    DmSubsEventE eventType = isTruncated ? DM_SUBS_EVENT_DELETE : DM_SUBS_EVENT_AGED;
    DwPushAgeRecordBeforeUpdate(dwRunCtx, cataLabel->vertexLabel, heapTupleBuf, eventType);
    return GMERR_OK;
}

bool DwNeedUpdateCheckInfo(CltCataLabelT *cataLabel, uint8_t partitionId)
{
    if (SECUREC_LIKELY(!cataLabel->vertexAccCheck->hasBeenChecked)) {
        return false;
    }
    const DmCheckInfoT *checkInfo = DwGetCheckInfoById(cataLabel, partitionId);
    return checkInfo->version.hasBeenChecked;
}

static Status DwChReplaceUpdateIdx(
    DwRunCtxT *dwRunCtx, CltCataLabelT *cataLabel, ChLabelDeleteOrReplaceParaT *replacePara, uint8_t partitionId)
{
    // 统计
    Status ret = GMERR_OK;
    TupleT oldTuple = TupleBufGet(&dwRunCtx->oldTupleBuf);
    if (replacePara->isAged) {
        ret = DwChPushAgeRecord(dwRunCtx, cataLabel, &oldTuple, replacePara->deleteMark);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Dw push age record in replace update clustered hash.");
            return ret;
        }
    }
    if (SECUREC_UNLIKELY(DwNeedUpdateCheckInfo(cataLabel, partitionId))) {
        DwStatisticsChangeForCheckAccountBySubType(dwRunCtx, cataLabel, &oldTuple, partitionId);
    }
    ret = DwSecondaryIdxesUpdate(dwRunCtx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Update secondary index.");
        return ret;
    }
    dwRunCtx->subsEvent = DM_SUBS_EVENT_INSERT;
    if (!dwRunCtx->isIns2Rep) {
        dwRunCtx->subsEvent = (replacePara->deleteMark != 0 || replacePara->isAged) ? DM_SUBS_EVENT_REPLACE_INSERT :
                                                                                      DM_SUBS_EVENT_REPLACE_UPDATE;
    }
    if (dwRunCtx->subsEvent == DM_SUBS_EVENT_REPLACE_UPDATE) {
        dwRunCtx->subsRowData.oldTupleBuf = oldTuple;
        dwRunCtx->subsRowData.deltaVertexBuf.str = (char *)dwRunCtx->dwArgs.vertexArgs.vertexBuf.buf;
        dwRunCtx->subsRowData.deltaVertexBuf.len = dwRunCtx->dwArgs.vertexArgs.vertexBuf.bufSize;
    }
    dwRunCtx->subsRowData.newTupleBuf = dwRunCtx->dwArgs.vertexArgs.vertexBuf;
    return GMERR_OK;
}

static Status DwChReplaceInsertIdx(DwRunCtxT *dwRunCtx, DmVertexLabelT *vertexLabel, HeapTupleBufT *heapTupleBuf)
{
    CRASHPOINT(DB_CRASH_DIRECT_WRITE, DB_CRASH_STATUS_DW_BEFORE_IDX_UPD);
    if (vertexLabel->metaVertexLabel->secIndexNum > vertexLabel->metaVertexLabel->hcIndexNum) {
        Status ret = DwSecondaryIdxesInsert(dwRunCtx, heapTupleBuf);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(ret, "ChLabel insert sec idx in dw.");
            return ret;
        }
    }
    if (dwRunCtx->dwArgs.vertexArgs.hasSubs) {
        dwRunCtx->subsEvent = dwRunCtx->isIns2Rep ? DM_SUBS_EVENT_INSERT : DM_SUBS_EVENT_REPLACE_INSERT;
        dwRunCtx->subsRowData.newTupleBuf = dwRunCtx->dwArgs.vertexArgs.vertexBuf;
    }
    if (dwRunCtx->stmgSubData.isStMgLabel) {
        DwSetStMgPubNodeTuple(dwRunCtx->stmgSubData.insertNode, dwRunCtx->tupleAddr);
    }
    return GMERR_OK;
}

// 用于ch replace流程的工具方法
static Status GetPrimaryKeyBufInV3Mode(const DmVertexT *vertex, uint8_t *vertexBuf, uint8_t **keyBuf, uint32_t *keyLen)
{
    DB_POINTER4(vertex, vertexBuf, keyBuf, keyLen);
    DmVertexGetVertexKeyBuf(vertex, keyBuf);
    DmIndexKeyBufInfoT *indexKey = &vertex->vertexDesc->indexKeyBufInfos[0];
    return DmGetPrimaryKeyBufInV3Mode(indexKey, vertexBuf, *keyBuf, keyLen);
}

static Status ChSetCheckVersionAndGetKeyBufNormal(DwRunCtxT *dwRunCtx, IndexKeyT *pkIdxKey)
{
    Status ret = GMERR_OK;
    DmVertexT *vertex = dwRunCtx->dwArgs.vertexArgs.vertex;
    HeapTupleBufT *heapTupleBuf = &dwRunCtx->dwArgs.vertexArgs.vertexBuf;
    CltCataLabelT *cataLabel = dwRunCtx->dwArgs.vertexArgs.cltCataLabel;
    DmVertexLabelT *vertexLabel = cataLabel->vertexLabel;
    bool isUseStructSeriBuf = dwRunCtx->useStructSeriBuf;

    if (SECUREC_UNLIKELY(!isUseStructSeriBuf)) {
        ret = DmSerializeVertex(vertex, (uint8_t **)&heapTupleBuf->buf, (uint32_t *)&heapTupleBuf->bufSize);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(ret, "Seri vertex when dw chLabel replace vertex.");
            return ret;
        }
    }

    // 获取分区id
    uint8_t partitionId = 0;
    ret = DwGetPartitionId(vertexLabel, cataLabel, heapTupleBuf, &partitionId);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    dwRunCtx->dwArgs.vertexArgs.partitionId = partitionId;
    DmCheckInfoT *checkInfo = DwGetCheckInfoById(cataLabel, partitionId);
    SetTupleBufCheckVersion(cataLabel, checkInfo, heapTupleBuf);

    if (SECUREC_LIKELY(isUseStructSeriBuf)) {
        ret = GetPrimaryKeyBufInV3Mode(vertex, heapTupleBuf->buf, &pkIdxKey->keyData, &pkIdxKey->keyLen);
    } else {
        DmVlIndexLabelT *pkIndex = MEMBER_PTR(vertexLabel->metaVertexLabel, pkIndex);
        ret = DmGetKeyBufFromVertex(vertex, pkIndex->idxLabelBase.indexId, &pkIdxKey->keyData, &pkIdxKey->keyLen);
    }
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Get keyBuf from vertex when dw chLabel replace vertex.");
        return ret;
    }
    return GMERR_OK;
}

static Status ChSetCheckVersionAndGetKeyBufFast(DwRunCtxT *dwRunCtx, IndexKeyT *pkIdxKey)
{
    Status ret;
    HeapTupleBufT *heapTupleBuf = &dwRunCtx->dwArgs.vertexArgs.vertexBuf;
    CltCataLabelT *cataLabel = dwRunCtx->dwArgs.vertexArgs.cltCataLabel;
    DmCheckInfoT *checkInfo = &cataLabel->vertexAccCheck->checkInfo[0];
    dwRunCtx->dwArgs.vertexArgs.partitionId = DB_INVALID_UINT8;
    // CHECK_VERSION 系统字段已经在结构化写流程中置为 0，或者在反序列化流程中置为 0，无对账则可以略过此系统字段设值
    if (SECUREC_UNLIKELY(checkInfo->version.hasBeenChecked)) {
        SetTupleBufCheckVersion(cataLabel, checkInfo, heapTupleBuf);
    }
    ret = GetPrimaryKeyBufInV3Mode(
        dwRunCtx->dwArgs.vertexArgs.vertex, heapTupleBuf->buf, &pkIdxKey->keyData, &pkIdxKey->keyLen);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Get keyBuf from vertex when dw chLabel replace vertex.");
        return ret;
    }
    return GMERR_OK;
}

static void UpdateLogicCnt4ChReplace(DwRunCtxT *dwRunCtx, ChLabelDeleteOrReplaceParaT *replacePara)
{
    CltCataLabelT *cataLabel = dwRunCtx->dwArgs.vertexArgs.cltCataLabel;
    DmVertexLabelT *vertexLabel = cataLabel->vertexLabel;
    if (!DmVertexLabelIsNormalLabel(vertexLabel)) {
        return;
    }
    // DwChReplaceVertex入口处赋值dwRunCtx->dwArgs.vertexArgs.partitionId。
    uint8_t partitionId = dwRunCtx->dwArgs.vertexArgs.partitionId;
    if (replacePara->isFind) {
        // 更新场景，模仿QryHandleReplaceUpdateVertex
        DwTupleStateE state = DW_TUPLE_NULL;
        if (replacePara->deleteMark != 0) {
            state = DW_TUPLE_STMGGC;
        } else if (replacePara->isAged) {
            state = DW_TUPLE_AGING;
        } else {
            state = DW_TUPLE_NORMAL;
        }
        DwUpdateStatics(state, DW_TUPLE_NORMAL, DwGetStatisticValue(dwRunCtx, partitionId));
    } else {
        // 插入场景
        DwUpdateStatics(DW_TUPLE_NULL, DW_TUPLE_NORMAL, DwGetStatisticValue(dwRunCtx, partitionId));
    }
}

static void InitReplacePara4ChReplace(DwRunCtxT *dwRunCtx, ChLabelDeleteOrReplaceParaT *replacePara)
{
    CltCataLabelT *cataLabel = DwGetCltCataLabel(dwRunCtx);

    replacePara->isIns2Rep = dwRunCtx->isIns2Rep;
    replacePara->isStmgVl = dwRunCtx->stmgSubData.isStMgLabel;
    replacePara->labelDef = &dwRunCtx->labelDef;
    replacePara->checkMarkDeleteFunc = ChCheckNeedUpdateViewNum;
    replacePara->checkBeforeUpdateFunc = DwCheckBeforeUpdate;

    replacePara->dwInsertStatusMergeNode.func = DwStMgInitInsertNodeCb;
    replacePara->dwInsertStatusMergeNode.subData = &dwRunCtx->stmgSubData;
    replacePara->dwInsertStatusMergeNode.vertexLabel = cataLabel;
    replacePara->dwInsertStatusMergeNode.heapTupleBuf = &dwRunCtx->dwArgs.vertexArgs.vertexBuf;

    replacePara->dwUpdateStatusMergeNode.func = DwStMgInitUpdateNodeCb;
    replacePara->dwUpdateStatusMergeNode.subData = &dwRunCtx->stmgSubData;
    replacePara->dwUpdateStatusMergeNode.vertexLabel = cataLabel;
    replacePara->dwUpdateStatusMergeNode.newTupleBuf = &dwRunCtx->dwArgs.vertexArgs.vertexBuf;
}

Status DwChReplaceVertex(GmcStmtT *stmt)
{
    Status ret;
    DwRunCtxT *dwRunCtx = stmt->dwRunCtx;
    IndexKeyT pkIdxMem = {0};
    HeapTupleBufT *heapTupleBuf = &dwRunCtx->dwArgs.vertexArgs.vertexBuf;
    CltCataLabelT *cataLabel = dwRunCtx->dwArgs.vertexArgs.cltCataLabel;
    DmVertexLabelT *vertexLabel = cataLabel->vertexLabel;
    if (SECUREC_LIKELY(dwRunCtx->useStructSeriBuf && !cataLabel->vertexAccCheck->isPartition)) {
        ret = ChSetCheckVersionAndGetKeyBufFast(dwRunCtx, &pkIdxMem);
    } else {
        ret = ChSetCheckVersionAndGetKeyBufNormal(dwRunCtx, &pkIdxMem);
    }
    if (ret != GMERR_OK) {
        return ret;
    }

    ChLabelDeleteOrReplaceParaT replacePara = {0};
    InitReplacePara4ChReplace(dwRunCtx, &replacePara);

    ret = ChLabelReplace(dwRunCtx->base.chRunCtx, pkIdxMem, heapTupleBuf, &replacePara);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        char *metaName = MEMBER_PTR(&vertexLabel->metaCommon, metaName);
        if (ret == GMERR_UNIQUE_VIOLATION) {
            DB_LOG_AND_SET_LASERR(GMERR_PRIMARY_KEY_VIOLATION,
                "Inserted data for chLabel violates primary unique constraint restriction, labelName: %s.", metaName);
            return GMERR_PRIMARY_KEY_VIOLATION;
        }
        DB_LOG_ERROR(ret, "Replace tuple of chLabel, labelName: %s.", metaName);
        return ret;
    }
    dwRunCtx->tupleAddr = replacePara.tupleAddr;
    // 直连写不支持 DLR，所以 isDrop 一定为 false
    if (SECUREC_UNLIKELY(replacePara.isDrop)) {
        return GMERR_OK;
    }
    ret = !replacePara.isFind ?
              DwChReplaceInsertIdx(dwRunCtx, vertexLabel, heapTupleBuf) :
              DwChReplaceUpdateIdx(dwRunCtx, cataLabel, &replacePara, dwRunCtx->dwArgs.vertexArgs.partitionId);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 更新逻辑计数。
    UpdateLogicCnt4ChReplace(dwRunCtx, &replacePara);
    // dwRunCtx->isIns2Rep为true时代表用户进行的是insert操作
    bool isOperateInsert = dwRunCtx->isIns2Rep || !replacePara.isFind || (replacePara.deleteMark != 0);
    stmt->affectRows = isOperateInsert ? REPLACE_INSERT_AFFECT_ROW : REPLACE_UPDATE_AFFECT_ROW;

    return GMERR_OK;
}

Status HeapReplaceInsert(GmcStmtT *stmt, DmVertexT *vertex)
{
    DwRunCtxT *dwRunCtx = stmt->dwRunCtx;
    dwRunCtx->subsEvent = DM_SUBS_EVENT_REPLACE_INSERT;
    Status ret = HeapLabelResetOpType(dwRunCtx->base.heapRunCtx, HEAP_OPTYPE_REPLACE_INSERT, false);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    ret = DwTupleInsertInner(stmt, dwRunCtx, vertex);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Insert heapTuple in dw replace-insert.");
        return ret;
    }
    // 更新逻辑计数。Insert流程里会填充partitionId
    uint8_t partitionId = dwRunCtx->dwArgs.vertexArgs.partitionId;
    DwUpdateLogicCnt(dwRunCtx, DW_TUPLE_NULL, DW_TUPLE_NORMAL, partitionId);
    stmt->affectRows = 1;
    dwRunCtx->subsRowData.newTupleBuf = dwRunCtx->dwArgs.vertexArgs.vertexBuf;
    return ret;
}

Status HeapReplaceUpdate(GmcStmtT *stmt, DwRunCtxT *dwRunCtx, CltCataLabelT *cataLabel)
{
    // 找到，进入replace-update流程
    TupleT oldTuple = TupleBufGet(&dwRunCtx->oldTupleBuf);
    // 检查是否需要生成aged或truncated订阅消息
    Status ret = DwCheckRecordPush(dwRunCtx, &oldTuple, cataLabel);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Check record need push in replace-update.");
        return ret;
    }
    // 对于replace操作，在存储层会进一步区分为REPLACE_INSERT和REPLACE_UPDATE
    // 由于默认初始化为REPLACE_INSERT，因此这里需reset
    ret = HeapLabelResetOpType(dwRunCtx->base.heapRunCtx, HEAP_OPTYPE_REPLACE_UPDATE, false);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    bool isMarkDelete = DwIsHeapTupleMarkDelete(&oldTuple, cataLabel);
    // 走常规的replace update流程
    if (DmIsLabelSupportStatusMerge(cataLabel->vertexLabel)) {
        // 状态合并表如果oldTuple是标记删除状态，replace流程里面会重新申请资源列，因此需要重新序列化。
        ret = DwStMgHeapUpdate(stmt, NULL, isMarkDelete);
    } else {
        // 在上一层函数中已经序列化好了
        ret = DwHeapUpdate(dwRunCtx, NULL, false);
    }
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Update heapTuple in replace-update.");
        return ret;
    }
    ret = DwSecondaryIdxesUpdate(dwRunCtx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Update sec idx in replace-update.");
        return ret;
    }
    // 如果为标记删除，对用户而言为replace insert，对于存储层来说是replace update
    if (isMarkDelete) {
        dwRunCtx->subsEvent = DM_SUBS_EVENT_REPLACE_INSERT;
        stmt->affectRows = REPLACE_INSERT_AFFECT_ROW;
    } else {
        dwRunCtx->subsEvent = DM_SUBS_EVENT_REPLACE_UPDATE;
        dwRunCtx->subsRowData.oldTupleBuf = oldTuple;
        dwRunCtx->subsRowData.deltaVertexBuf.str = (char *)dwRunCtx->dwArgs.vertexArgs.vertexBuf.buf;
        dwRunCtx->subsRowData.deltaVertexBuf.len = dwRunCtx->dwArgs.vertexArgs.vertexBuf.bufSize;
        stmt->affectRows = REPLACE_UPDATE_AFFECT_ROW;
    }
    dwRunCtx->subsRowData.newTupleBuf = dwRunCtx->dwArgs.vertexArgs.vertexBuf;
    return GMERR_OK;
}

Status DwHeapReplaceVertex(GmcStmtT *stmt)
{
    DwRunCtxT *dwRunCtx = stmt->dwRunCtx;
    DmVertexT *vertex = dwRunCtx->dwArgs.vertexArgs.vertex;
    CltCataLabelT *cataLabel = dwRunCtx->dwArgs.vertexArgs.cltCataLabel;
    DmVertexLabelT *vertexLabel = cataLabel->vertexLabel;
    bool isFound = false;
    char *metaName = MEMBER_PTR(&vertexLabel->metaCommon, metaName);
    DmVlIndexLabelT *pkIndex = MEMBER_PTR(vertexLabel->metaVertexLabel, pkIndex);
    Status ret = DwIdxLookup(dwRunCtx, NULL, pkIndex->idxLabelBase.indexId, &isFound);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Lookup index in dw replace.");
        return ret;
    }
    // 未找到，进入replace-insert流程
    if (!isFound) {
        return HeapReplaceInsert(stmt, vertex);
    }
    // 找到，进入replace-update流程
    // 更新前先校验NonChangeable字段（partitionId）
    TupleT oldTuple = TupleBufGet(&dwRunCtx->oldTupleBuf);
    HeapTupleBufT *heapTupleBuf = &dwRunCtx->dwArgs.vertexArgs.vertexBuf;
    if (SECUREC_UNLIKELY(!dwRunCtx->useStructSeriBuf)) {
        ret = DmSerializeVertex(vertex, (uint8_t **)&heapTupleBuf->buf, (uint32_t *)&heapTupleBuf->bufSize);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
    }
    // replace-update流程，需要对资源字段进行校验
    ret = DwCheckResForReplace(stmt, dwRunCtx, &oldTuple);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    ret = DmCheckIsPartitionNotChange(
        vertexLabel, oldTuple.buf, oldTuple.bufSize, heapTupleBuf->buf, heapTupleBuf->bufSize);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Label %s partition not support change.", metaName);
        return ret;
    }
    return HeapReplaceUpdate(stmt, dwRunCtx, cataLabel);
}

#ifdef ART_CONTAINER

static void UpdateLogicCnt4ArtReplace(DwRunCtxT *dwRunCtx, Status ret)
{
    if (ret != GMERR_PRIMARY_KEY_VIOLATION && ret != GMERR_OK) {
        return;
    }
    CltCataLabelT *cataLabel = dwRunCtx->dwArgs.vertexArgs.cltCataLabel;
    DmVertexLabelT *vertexLabel = cataLabel->vertexLabel;
    if (!DmVertexLabelIsNormalLabel(vertexLabel)) {
        return;
    }
    // DwChReplaceVertex入口处赋值dwRunCtx->dwArgs.vertexArgs.partitionId。
    uint8_t partitionId = dwRunCtx->dwArgs.vertexArgs.partitionId;
    if (ret == GMERR_PRIMARY_KEY_VIOLATION) {
        // replace冲突
        DwUpdateStatics(DW_TUPLE_NORMAL, DW_TUPLE_NORMAL, DwGetStatisticValue(dwRunCtx, partitionId));
    } else {
        // 插入场景
        DwUpdateStatics(DW_TUPLE_NULL, DW_TUPLE_NORMAL, DwGetStatisticValue(dwRunCtx, partitionId));
    }
}

// 提供replace，业务希望如果存在则报冲突
Status DwArtReplaceVertex(GmcStmtT *stmt)
{
    DwRunCtxT *dwRunCtx = stmt->dwRunCtx;
    DmVertexT *vertex = dwRunCtx->dwArgs.vertexArgs.vertex;
    // 序列化vertex到heapTupleBuf
    HeapTupleBufT *heapTupleBuf = &dwRunCtx->dwArgs.vertexArgs.vertexBuf;
    Status ret = GMERR_OK;
    if (!dwRunCtx->useStructSeriBuf) {
        ret = DmSerializeVertex(vertex, (uint8_t **)&heapTupleBuf->buf, (uint32_t *)&heapTupleBuf->bufSize);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(ret, "Seri vertex before dw insert.");
            return ret;
        }
    }

    // 插入数据
    ret = TupleBufInsert(dwRunCtx, heapTupleBuf);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Dw insert heap tuple.");
        return ret;
    }
    if (dwRunCtx->stmgSubData.isStMgLabel) {
        DwSetStMgPubNodeTuple(dwRunCtx->stmgSubData.insertNode, dwRunCtx->tupleAddr);
    }

    // 插入Art容器
    DmVertexLabelT *vertexLabel = dwRunCtx->dwArgs.vertexArgs.cltCataLabel->vertexLabel;
    IndexKeyT indexKey = {0};
    if (SECUREC_LIKELY(dwRunCtx->useStructSeriBuf)) {
        ret = GetPrimaryKeyBufInV3Mode(vertex, heapTupleBuf->buf, &indexKey.keyData, &indexKey.keyLen);
    } else {
        DmVlIndexLabelT *pkIndex = MEMBER_PTR(vertexLabel->metaVertexLabel, pkIndex);
        ret = DmGetKeyBufFromVertex(vertex, pkIndex->idxLabelBase.indexId, &indexKey.keyData, &indexKey.keyLen);
    }
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Get keyBuf from vertex when dw replace vertex for art label.");
        return ret;
    }
    HpTupleAddr outputTuple = DB_INVALID_UINT64;
    ret = ArtContainerReplace(dwRunCtx->base.artRunCtx, indexKey, heapTupleBuf, dwRunCtx->tupleAddr, &outputTuple);
    if (SECUREC_UNLIKELY(ret != GMERR_PRIMARY_KEY_VIOLATION && ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Dw insert indexes.");
    }
    if (!dwRunCtx->base.isRealCluster && ret != GMERR_OK) {
        // replace返回冲突或者其他错误就释放该次内存
        uint8_t *buf = (uint8_t *)(dwRunCtx->tupleAddr);
        DbDynMemCtxFree(ArtContainerGetAllocBufMemCtxByIdx(dwRunCtx->base.artRunCtx, *buf), buf);
    }
    if (ret != GMERR_OK) {
        return ret;
    }
    UpdateLogicCnt4ArtReplace(dwRunCtx, ret);
    stmt->affectRows = 1;
    return ret;
}
#endif

/***** delete *****/
Status DwGenerateSubs4ChLabelDeleteCb(void *stmt, void *cursor, const HeapTupleBufT *heapTupleBuf, bool isAged)
{
    (void)stmt;
    (void)cursor;
    (void)heapTupleBuf;
    return GMERR_OK;
}

static Status StmgHeapMarkDeleteTuple(DwRunCtxT *dwRunCtx, HeapTupleBufT *heapTupleBuf, CltCataLabelT *cataLabel)
{
    Status ret;
    DmVertexLabelT *vertexLabel = cataLabel->vertexLabel;
    char *metaName = MEMBER_PTR(&vertexLabel->metaCommon, metaName);
    if (dwRunCtx->base.isUseClusteredHashTable) {
        ret = ChLabelUpdate(dwRunCtx->base.chRunCtx, heapTupleBuf, dwRunCtx->tupleAddr);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Update clusteredHash label for VL %s when mark delete.", metaName);
            return ret;
        }
    } else {
        uint32_t offset = 0;
        uint32_t len = 0;
        DmVertexGetSysPropePosition(STATUS_MERGE_DELETE_MARK, cataLabel->vertexLabel->vertexDesc, &offset, &len);
        ret =
            HeapLabelUpdateByMergeSubsDelete(dwRunCtx->base.heapRunCtx, heapTupleBuf, dwRunCtx->tupleAddr, offset, len);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Update heap label for VL %s when mark delete.", metaName);
            return ret;
        }
    }

    SeRunCtxHdT seRunCtx = (SeRunCtxHdT)dwRunCtx->base.seRunCtxHandle;
    SeTransSetSendSubFlag(seRunCtx);

    uint8_t partitionId;
    ret = DwGetPartitionId(vertexLabel, cataLabel, heapTupleBuf, &partitionId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Get partition Id for VL %s when mark delete.", metaName);
        return ret;
    }
    // 逻辑计数
    DwUpdateLogicCnt(dwRunCtx, dwRunCtx->oldTupleState, DW_TUPLE_STMGGC, partitionId);
    // 对账统计
    DwStatisticsChangeForCheckAccount(dwRunCtx, cataLabel, heapTupleBuf, partitionId);
    return GMERR_OK;
}

Status StmgHeapMarkDelete(GmcStmtT *stmt, DmVertexLabelT *vertexLabel)
{
    Status ret = GMERR_OK;
    DwRunCtxT *dwRunCtx = stmt->dwRunCtx;
    // 数据存在，将待删除的旧数据缓存至dwArgs中，供后续使用

    if (!dwRunCtx->base.isUseClusteredHashTable) {
        ret = HeapLabelResetOpType(dwRunCtx->base.heapRunCtx, HEAP_OPTYPE_DELETE, false);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
    }
    CltCataLabelT *cataLabel = dwRunCtx->dwArgs.vertexArgs.cltCataLabel;
    TupleT oldTuple = TupleBufGet(&dwRunCtx->oldTupleBuf);
    ret = DwStMgInitDeleteNode(&dwRunCtx->stmgSubData, cataLabel, &oldTuple, dwRunCtx->dataStatus);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Init delete node for StMg in dw delete.");
        return ret;
    }

    ret = StmgHeapMarkDeleteTuple(dwRunCtx, &oldTuple, cataLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Mark delete tuple in dw delete.");
        (void)DmResetVertex(dwRunCtx->dwArgs.vertexArgs.vertex);
        DwFreeCachedTupBufIfLarge(stmt);
        return ret;
    }

    // delete删除操作添加到gc task标记中
    dwRunCtx->stmgSubData.gcLabelId = vertexLabel->metaCommon.metaId;
    stmt->affectRows += 1;
    return GMERR_OK;
}

Status ChLabelDeleteProc(GmcStmtT *stmt, CltCataLabelT *cataLabel)
{
    DmVertexLabelT *vertexLabel = cataLabel->vertexLabel;
    DwRunCtxT *dwRunCtx = stmt->dwRunCtx;
    DmIndexKeyT *indexKey = dwRunCtx->idxKey;
    Status ret = GMERR_OK;
    if (indexKey->indexConstraint == PRIMARY) {
        IndexKeyT pkIdxKey = {0};
        DmIndexKeyGetKeyBuf(indexKey, (uint8_t **)&pkIdxKey.keyData, &pkIdxKey.keyLen);
        ChLabelDeleteOrReplaceParaT deletePara = {0};
        deletePara.checkMarkDeleteFunc = ChCheckNeedUpdateViewNum;
        // 聚簇容器内部并不会调到此函数，因为直连写 delete 流程中对于标记删除（包含老化）的数据在前面已经直接 return 了
        deletePara.deletePreOper.func = DwGenerateSubs4ChLabelDeleteCb;
        deletePara.labelDef = &dwRunCtx->labelDef;
        if (SECUREC_UNLIKELY((ret = ChLabelDeleteByKey(dwRunCtx->base.chRunCtx, pkIdxKey, &deletePara)) != GMERR_OK)) {
            DB_LOG_ERROR(ret, "Delete tuple of clusteredHash in dw delete.");
            return ret;
        }
        if (!deletePara.isFind || deletePara.isDrop) {
            return GMERR_OK;
        }
        dwRunCtx->tupleAddr = deletePara.tupleAddr;
    } else {
        if ((ret = ChLabelDelete(dwRunCtx->base.chRunCtx, dwRunCtx->tupleAddr)) != GMERR_OK) {
            DB_LOG_ERROR(ret, "Delete tuple of clusteredHash.");
            return ret;
        }
    }

    if ((ret = SecondaryIdxesDelete(dwRunCtx)) != GMERR_OK) {
        DB_LOG_ERROR(ret, "Delete indexes of clusteredHash in dw delete.");
        return ret;
    }

    // 更新统计计数
    uint8_t partitionId;
    TupleT oldTuple = TupleBufGet(&dwRunCtx->oldTupleBuf);
    if (SECUREC_UNLIKELY((ret = DwGetPartitionId(vertexLabel, cataLabel, &oldTuple, &partitionId)) != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Get partition Id when delete tuple of clusteredHash.");
        return ret;
    }
    DwUpdateLogicCnt(dwRunCtx, dwRunCtx->oldTupleState, DW_TUPLE_NULL, partitionId);
    DwStatisticsChangeForCheckAccount(dwRunCtx, cataLabel, &oldTuple, partitionId);
    stmt->affectRows += 1;
    return GMERR_OK;
}

static Status DwHeapDelete(DwRunCtxT *dwRunCtx)
{
    DB_POINTER(dwRunCtx);
    Status ret = GMERR_OK;
    ret = HeapLabelDeleteHpTuple(dwRunCtx->base.heapRunCtx, dwRunCtx->tupleAddr);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Dw delete heapTuple.");
        return ret;
    }
    CltCataLabelT *cataLabel = dwRunCtx->dwArgs.vertexArgs.cltCataLabel;
    DmVertexLabelT *vertexLabel = cataLabel->vertexLabel;

    // 更新逻辑计数
    TupleT oldTuple = TupleBufGet(&dwRunCtx->oldTupleBuf);
    uint8_t partitionId = 0;
    ret = DwGetPartitionId(vertexLabel, cataLabel, &oldTuple, &partitionId);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "Get partition Id when dw delete heapTuple.");
        return ret;
    }
    DwUpdateLogicCnt(dwRunCtx, dwRunCtx->oldTupleState, DW_TUPLE_NULL, partitionId);
    // 更新对账信息
    DwStatisticsChangeForCheckAccount(dwRunCtx, cataLabel, &oldTuple, partitionId);
    return ret;
}

Status HeapLabelDeleteProc(GmcStmtT *stmt, DmVertexLabelT *vertexLabel)
{
    DwRunCtxT *dwRunCtx = stmt->dwRunCtx;
    Status ret = HeapLabelResetOpType(dwRunCtx->base.heapRunCtx, HEAP_OPTYPE_DELETE, false);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    // 对于轻量化事务，先删除hash cluster
    CltCataLabelT *cataLabel = dwRunCtx->dwArgs.vertexArgs.cltCataLabel;
    VertexLabelCommonInfoT *commonInfo = cataLabel->vertexLabel->commonInfo;
    if (SECUREC_LIKELY(DmIsLabelLiteTrx(commonInfo->heapInfo.ccType))) {
        if ((ret = HcIdxesDelete(dwRunCtx)) != GMERR_OK) {
            DB_LOG_ERROR(ret, "Delete hash cluster index in dw delete.");
            return ret;
        }
    }
    // 删除tuple
    if ((ret = DwHeapDelete(dwRunCtx)) != GMERR_OK) {
        DB_LOG_ERROR(ret, "Delete heapTuple in dw delete.");
        return ret;
    }
    // 删除索引
    // 对于heap容器，分别删除主键索引和二级索引
    if ((ret = AllIdxesDelete(dwRunCtx)) != GMERR_OK) {
        DB_LOG_ERROR(ret, "Delete indexes in dw delete.");
        return ret;
    }

    stmt->affectRows += 1;
    return GMERR_OK;
}

#ifdef ART_CONTAINER
Status ArtLabelDeleteProc(GmcStmtT *stmt)
{
    DwRunCtxT *dwRunCtx = stmt->dwRunCtx;
    // 对于轻量化事务，先删除hash cluster
    Status ret = GMERR_OK;
    IndexKeyT pkIdxKey = {0};
    DmIndexKeyGetKeyBuf(dwRunCtx->idxKey, (uint8_t **)&pkIdxKey.keyData, &pkIdxKey.keyLen);
    HpTupleAddr outputAddr = HEAP_INVALID_ADDR;
    ret = ArtContainerDelete(dwRunCtx->base.artRunCtx, pkIdxKey, NULL, dwRunCtx->tupleAddr, &outputAddr);
    if (!dwRunCtx->base.isRealCluster && outputAddr != HEAP_INVALID_ADDR) {
        uint8_t *buf = (uint8_t *)(outputAddr);
        DbDynMemCtxFree(ArtContainerGetAllocBufMemCtxByIdx(dwRunCtx->base.artRunCtx, *buf), buf);
    }
    if (ret == GMERR_OK && outputAddr != HEAP_INVALID_ADDR) {
        stmt->affectRows += 1;
        // 更新统计计数
        uint8_t partitionId;
        CltCataLabelT *cataLabel = dwRunCtx->dwArgs.vertexArgs.cltCataLabel;
        TupleT oldTuple = TupleBufGet(&dwRunCtx->oldTupleBuf);
        DmVertexLabelT *vertexLabel = dwRunCtx->dwArgs.vertexArgs.cltCataLabel->vertexLabel;
        if (SECUREC_UNLIKELY((ret = DwGetPartitionId(vertexLabel, cataLabel, &oldTuple, &partitionId)) != GMERR_OK)) {
            DB_LOG_ERROR(ret, "Get partition Id when delete tuple of clusteredHash.");
            return ret;
        }
        DwUpdateLogicCnt(dwRunCtx, dwRunCtx->oldTupleState, DW_TUPLE_NULL, partitionId);
    }
    return ret;
}
#endif

Status DeleteSingleVertexDw(GmcStmtT *stmt, DwRunCtxT *dwRunCtx)
{
    CltCataLabelT *cataLabel = dwRunCtx->dwArgs.vertexArgs.cltCataLabel;
    DmVertexLabelT *vertexLabel = cataLabel->vertexLabel;
    char *labelName = MEMBER_PTR(&vertexLabel->metaCommon, metaName);
    TupleT oldTuple = TupleBufGet(&dwRunCtx->oldTupleBuf);
    Status ret = DwFreeRes(dwRunCtx, &oldTuple, stmt->memCtx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Dw free resource, labelName:%s.", labelName);
        return ret;
    };
    if (dwRunCtx->stmgSubData.isStMgLabel) {
        ret = StmgHeapMarkDelete(stmt, vertexLabel);  // 状态合并表走标记删除，实际上为update逻辑，故单独开辟分支
    } else {
        if (dwRunCtx->base.containerType == CONTAINER_CLUSTERED_HASH) {
            ret = ChLabelDeleteProc(stmt, cataLabel);
        } else if (dwRunCtx->base.containerType == CONTAINER_HEAP) {
            ret = HeapLabelDeleteProc(stmt, vertexLabel);
        }
#ifdef ART_CONTAINER
        else if (dwRunCtx->base.containerType == CONTAINER_ART) {
            ret = ArtLabelDeleteProc(stmt);
        }
#else
        else {
            DB_ASSERT(false);
        }
#endif
    }
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Dw delete tuple, labelName:%s.", labelName);
        return ret;
    }
    dwRunCtx->subsRowData.oldTupleBuf = oldTuple;
    return GMERR_OK;
}
