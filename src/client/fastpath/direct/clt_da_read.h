/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: clt_da_read.h
 * Description: functions and structures for direct read
 * Author:shenjunhui
 * Create: 2023-04-11
 */
#ifndef CLT_DA_READ_H
#define CLT_DA_READ_H

#include "clt_da_handle.h"
#include "se_trx.h"

#ifdef __cplusplus
extern "C" {
#endif

#define MAX_CLT_HOLD_HEAP_TUPLEBUF_SIZE 512

typedef enum {
    DIRECT_ACCESS_ONLY_RESET_HEAP = 0,
    DIRECT_ACCESS_CLOSE_HEAP_CURSOR,
    DIRECT_ACCESS_CLOSE_INDEXCTX,
    DIRECT_ACCESS_CLOSE_HEAP_CURSOR_CTX,
} DirectAccessCloseTypeE;

typedef enum PrivPolicyMode {
    PRIV_NON_AUTH = 0,  // 不进行authentication
    PRIV_TOLERATE = 1,  // 宽容模式，如果权限不满足，可以继续执行，但是需要打印日志
    PRIV_AUTH = 2,      // 强制authentication模式
} PrivPolicyModeE;

typedef struct TagDsHpFetchAndCmpInfoT {
    const DmIndexKeyBufInfoT *tpKeyBufInfo;
    uint8_t nullInfoBytes;
    uint8_t *tpKeyBuf;
    uint8_t *idxKeyData;
    uint32_t idxkeyLen;
    bool isMatch;
    bool isV3Mode;
} DsHpFetchAndCmpInfoT;

typedef struct TagDirectReadHpCopyExtArgs {
    DrRunCtxT *drRunCtx;
    DmVertexT *vertex;
    CltCataLabelT *cltCataLabel;
    uint64_t trxId;
    bool isAged;
    bool needCopy;
    bool checkAge;
    DsHpFetchAndCmpInfoT cmpInfo;
    bool *isMatch;
    int32_t *cmpRet;
    bool filterRet;
    bool needDsStructCheck;
    bool needCmp;
    bool reserve;
} DrHpCopyExtArgsT;

typedef struct TagDirectReadHpCopyExtOptArgs {
    DrRunStructOptCtxT *drRunCtx;
    DmVertexT *vertex;
    uint64_t trxId;
    bool isAged;
    bool needCopy;
    bool checkAge;
    DsHpFetchAndCmpInfoT cmpInfo;
} DrHpCopyExtArgsOptT;

typedef struct {
    FixBufferT *buff;
    DmVertexT *vertex;
    CltCataLabelT *cltCataLabel;
    DmLabelTypeE labelType;
    uint64_t limitCount;  // 直连扫描预期读取的条数（静态值，不随扫描过程而变化）
    uint64_t actualFetchedCnt;  // 表示每轮扫描开始前，已成功扫描到的条数。每完成一轮扫描更新一次
    uint32_t maxFetchNum;                 // 一轮扫描允许获取的最大条数
    StructFilterListT *structFilterList;  // kv-null
#ifdef FEATURE_HP_ANNSEQSCAN
    VectorSearchCtxT *vecSearchCtx;
#endif
} MsHpScanUserDataT;  // Heap扫描用户参数

typedef struct TagDirectReadHpCopyExtArgs *DrHpCopyExtHdlT;
typedef struct TagDirectReadHpCopyExtOptArgs *DrHpCopyExtOptHdlT;

Status DirectReadCtxOpen(DrRunCtxT *drRunCtx);
void DirectReadCtxClose(DrRunCtxT *drRunCtx, DirectAccessCloseTypeE type);
void DirectReadCtxCloseOpt(DrRunStructOptCtxT *drRunCtx, DirectAccessCloseTypeE type);
void DirectReadCtxCloseHeapCursor(DrRunCtxT *drRunCtx);
void DirectReadCtxCloseHeapCursorNoLock(DrRunCtxT *drRunCtx);
Status DirectReadClose(DbMemCtxT *memCtx, DrRunCtxT *drRunCtx);
Status MainStoreHeapLabelOpen(DrRunCtxT *drRunCtx, DmLabelTypeE labelType);
Status MainStoreHeapLabelOpenOpt(DrRunStructOptCtxT *drRunCtx, DmLabelTypeE labelType);
Status MainStoreReadOpen(DrRunCtxT *drRunCtx);
Status MainStoreReadFetchNext(DrRunCtxT *drRunCtx, uint32_t *fetchedRows, bool *isEOF);
Status FixBufferCopy(FixBufferT *directReadBuff, const uint8_t *buf, const uint32_t bufSize);
Status MainStorePkIdxFetchNext(DrRunCtxT *drRunCtx, DmLabelTypeE labelType, uint32_t *fetchedRows, bool *isEOF);
Status DirectReadOpen(GmcConnT *conn, uint32_t sessionId, uint16_t trxSlot, DbMemCtxT *memCtx, DrRunCtxT **drRunCtx);
Status DsModifyHashCompare(
    IndexCtxT *idxCtx, IndexKeyT hashKey, const HpTupleAddr addr, int32_t *cmpRet, bool *isMatch);
Status DsModifyHashCompareOpt(
    IndexCtxT *idxCtx, IndexKeyT hashKey, const HpTupleAddr addr, int32_t *cmpRet, bool *isMatch);
Status DirectReadGetCount(DrRunCtxT *drRunCtx, uint64_t *count);
int32_t DsCmpKeyBufFromDM(DsHpFetchAndCmpInfoT *cmpInfo, uint8_t *buf);
Status MainStoreHeapCursorOpen(DrRunCtxT *drRunCtx);
Status MainStoreIndexOpen(DrRunCtxT *drRunCtx, DmLabelTypeE labelType);
static inline Status CltChangeSeTransStateBeforeRead(bool trxOpen, SeRunCtxHdT seRunCtx)
{
    if (SECUREC_LIKELY(!trxOpen)) {
        return GMERR_OK;
    }
    return SeTransStartDirectRead(seRunCtx);
}
static inline void CltChangeSeTransStateAfterRead(bool trxOpen, SeRunCtxHdT seRunCtx)
{
    if (SECUREC_LIKELY(!trxOpen)) {
        return;
    }
    SeTransEndDirectRead(seRunCtx);
}

#ifdef __cplusplus
}
#endif
#endif  // CLT_DA_READ_H
