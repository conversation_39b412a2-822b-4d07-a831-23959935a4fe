/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2023. All rights reserved.
 * Description: The direct write entries and funcs
 * Author:
 * Create:
 */

#ifndef CLT_DA_WRITE_STORAGE_H
#define CLT_DA_WRITE_STORAGE_H

#include "clt_da_write.h"
#include "clt_stmt.h"
#include "se_define.h"

#ifdef __cplusplus
extern "C" {
#endif

Status DwHeapReplaceVertex(GmcStmtT *stmt);
Status DwChReplaceVertex(GmcStmtT *stmt);
#ifdef ART_CONTAINER
Status DwArtReplaceVertex(GmcStmtT *stmt);
#endif

Status DwTupleInsert(GmcStmtT *stmt, DmVertexT *vertex);

Status DwStMgHeapUpdate(GmcStmtT *stmt, DwTupleInfoT *tupleInfo, bool needSerialized);
Status DwHeapUpdate(DwRunCtxT *dwRunCtx, DwTupleInfoT *tupleInfo, bool isSerialize);
Status MergeAndUpdate(GmcStmtT *stmt, DwRunCtxT *dwRunCtx);
Status UpdateSingleVertex(GmcStmtT *stmt, DwRunCtxT *dwRunCtx);
Status UpdateSingleVertex4CheckReplace(GmcStmtT *stmt, DwRunCtxT *dwRunCtx);

Status StmgHeapMarkDelete(GmcStmtT *stmt, DmVertexLabelT *vertexLabel);
Status ChLabelDeleteProc(GmcStmtT *stmt, CltCataLabelT *cataLabel);
Status HeapLabelDeleteProc(GmcStmtT *stmt, DmVertexLabelT *vertexLabel);
#ifdef ART_CONTAINER
Status ArtLabelDeleteProc(GmcStmtT *stmt);
Status ArtOpenAndInitArtRunCtx(DwRunCtxT *dwRunCtx);
#endif
Status DeleteSingleVertexDw(GmcStmtT *stmt, DwRunCtxT *dwRunCtx);

Status HeapOpenAndInitHeapRunCtx(DwRunCtxT *dwRunCtx);
Status ChOpenAndInitChRunCtx(DwRunCtxT *dwRunCtx);
void ReleaseHeapCtx(DwRunCtxT *dwRunCtx);

Status SetRsmUndoForVertexLabel(SeRunCtxT *seRunCtx, DmVertexLabelT *vertexLabel);

#ifdef __cplusplus
}
#endif

#endif /* CLT_DA_WRITE_STORAGE_H */
