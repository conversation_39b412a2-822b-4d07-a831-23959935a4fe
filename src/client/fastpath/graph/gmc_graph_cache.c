/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * File Name: gmc_graph_cache.c
 * Description: realization of interface of gmdb graph cache
 * Author:
 * Create: 2025-07-22
 */

#include "gmc_graph.h"
#include "db_last_error.h"
#include "adpt_types.h"
#include "dm_meta_prop_strudefs.h"
#include "clt_check.h"
#include "clt_stmt_extend.h"
#include "gmc_graph_utils.h"

CLT_ALWAYS_INLINE static Status GmcPrepareStmtByLabelNameCheckWithCache(GmcStmtT *stmt, const char *vertexLabelName)
{
    Status ret = CltBasicCheckWithStmt(stmt);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    GmcConnTypeE connType = stmt->conn->connType;
    if (SECUREC_UNLIKELY(connType != GMC_CONN_TYPE_SYNC)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED, "Operation for given connection type.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }

    ret = CltCheckStringValid(vertexLabelName, MAX_TABLE_NAME_LEN, "vertexLabelName");
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    return GMERR_OK;
}

Status GmcPrepareStmtByLabelNameWithCache(GmcStmtT *stmt, const char *vertexLabelName)
{
    // 必要的校验
    Status ret = GmcPrepareStmtByLabelNameCheckWithCache(stmt, vertexLabelName);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    bool *isUsing = &stmt->conn->isUsing;
    ret = CheckAndInitUsing(isUsing, "prepare by name");
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    // 如果该stmt上一次操作的是同一张表，直接return
    if (stmt->cache != NULL) {
        DmVertexLabelT *vertexLabel = stmt->cache->vl;
        char *metaName = MEMBER_PTR_NO_CHECK(&(vertexLabel->metaCommon), metaName);
        GmcConnT *conn = stmt->conn;
        if (SECUREC_LIKELY(strcmp(metaName, vertexLabelName) == 0 &&
                           vertexLabel->metaCommon.namespaceId == conn->namespaceId &&
                           vertexLabel->metaCommon.dbId == conn->dbId)) {
            *isUsing = false;
            return GMERR_OK;
        }
    }
    // prepare label
    uint32_t versionId = DM_SCHEMA_MIN_VERSION;
    ret = PrepareStmtByLabelNameWithCache(stmt, vertexLabelName, versionId);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        CltResetStmt(stmt, true);
    }
    *isUsing = false;
    return ret;
}

static Status GmcSetCacheWithBufCheck(const GmcStmtT *stmt, const GmcSeriT *seri)
{
    Status ret = CltBasicCheckWithStmt(stmt);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (SECUREC_UNLIKELY(seri == NULL)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "seri");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    if (SECUREC_UNLIKELY(seri->seriFunc == NULL)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "seriFunc");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    if (SECUREC_UNLIKELY(stmt->cache == NULL)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "cache");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    return GMERR_OK;
}

static Status CltSetVertexWithBufForCache(GmcStmtT *stmt, GmcSeriT *seri)
{
    CacheItemT *cache = stmt->cache;
    GmcStructureResvT reservedSize = {0};
    Status ret = (*seri->seriFunc)(seri, cache->replaceBuf, &reservedSize);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "call serialization function.");
        return ret;
    }
    return GMERR_OK;
}

Status GmcSetCacheBuf(GmcStmtT *stmt, GmcSeriT *seri)
{
    Status ret = GmcSetCacheWithBufCheck(stmt, seri);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    ret = CltSetVertexWithBufForCache(stmt, seri);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "Clt set vertex buf for cache.");
        return ret;
    }
    return ret;
}

static Status GmcExecuteCacheCheck(const GmcStmtT *stmt)
{
    Status ret = CltBasicCheckWithStmt(stmt);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (SECUREC_UNLIKELY(stmt->cache == NULL)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "cache");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    return GMERR_OK;
}

static Status ExecuteCacheReplaceUpdate(
    CacheBucketT *bucketArray, uint32_t keyLen, uint32_t valueLen, uint8_t *replaceBuf)
{
    int8_t *keyAndValueArray = DbShmPtrToAddr(bucketArray->slot.keyAndValueArrayShmPtr);
    if (SECUREC_UNLIKELY(keyAndValueArray == NULL)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE,
            "keyAndValueArrayShmPtr, segId: %" PRIu32 ", offset: %" PRIu32,
            bucketArray->slot.keyAndValueArrayShmPtr.segId, bucketArray->slot.keyAndValueArrayShmPtr.offset);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    for (uint32_t i = 0; i < CACHE_BUCKET_KEY_NUM; i++) {
        int8_t *currentKey = keyAndValueArray + i * keyLen;
        if (memcmp(currentKey, replaceBuf, keyLen) == 0) {
            // 匹配成功 replace_update
            int8_t *currentValue = keyAndValueArray + CACHE_BUCKET_KEY_NUM * keyLen + i * valueLen;
            (void)memcpy_s(currentValue, valueLen, replaceBuf, valueLen);
            return GMERR_OK;
        }
    }
    CacheSlotT *next = (CacheSlotT *)DbShmPtrToAddr(bucketArray->slot.nextShmPtr);
    while (next != NULL) {
        int8_t *nextKeyAndValueArray = DbShmPtrToAddr(next->keyAndValueArrayShmPtr);
        if (nextKeyAndValueArray == NULL) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_MEMORY_OPERATE_FAILED,
                "keyAndValueArrayShmPtr, segId: %" PRIu32 ", offset: %" PRIu32, next->keyAndValueArrayShmPtr.segId,
                next->keyAndValueArrayShmPtr.offset);
            return GMERR_MEMORY_OPERATE_FAILED;
        }
        for (uint32_t i = 0; i < CACHE_BUCKET_KEY_NUM; i++) {
            int8_t *nextCurrentKey = nextKeyAndValueArray + i * keyLen;
            if (memcmp(nextCurrentKey, replaceBuf, keyLen) == 0) {
                // 匹配成功 replace_update
                int8_t *nextCurrentValue = nextKeyAndValueArray + CACHE_BUCKET_KEY_NUM * keyLen + i * valueLen;
                (void)memcpy_s(nextCurrentValue, valueLen, replaceBuf, valueLen);
                return GMERR_OK;
            }
        }
        next = (CacheSlotT *)DbShmPtrToAddr(next->nextShmPtr);
    }
    return GMERR_NO_DATA;
}

static Status ExecuteCacheReplaceInsert(
    CacheBucketT *bucketArray, uint32_t keyLen, uint32_t valueLen, uint8_t *replaceBuf)
{
    int8_t *keyAndValueArray = DbShmPtrToAddr(bucketArray->slot.keyAndValueArrayShmPtr);
    if (SECUREC_UNLIKELY(keyAndValueArray == NULL)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_UNEXPECTED_NULL_VALUE,
            "keyAndValueArrayShmPtr, segId: %" PRIu32 ", offset: %" PRIu32,
            bucketArray->slot.keyAndValueArrayShmPtr.segId, bucketArray->slot.keyAndValueArrayShmPtr.offset);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    for (uint32_t i = 0; i < CACHE_BUCKET_KEY_NUM; i++) {
        int8_t *currentKey = keyAndValueArray + i * keyLen;
        if (*currentKey == SLOT_EMPTY_FLAG) {
            // 找到空槽位 replace_insert
            (void)memcpy_s(currentKey, keyLen, replaceBuf, keyLen);
            int8_t *currentValue = keyAndValueArray + CACHE_BUCKET_KEY_NUM * keyLen + i * valueLen;
            (void)memcpy_s(currentValue, valueLen, replaceBuf, valueLen);
            return GMERR_OK;
        }
    }
    CacheSlotT *next = (CacheSlotT *)DbShmPtrToAddr(bucketArray->slot.nextShmPtr);
    while (next != NULL) {
        int8_t *nextKeyAndValueArray = DbShmPtrToAddr(next->keyAndValueArrayShmPtr);
        if (nextKeyAndValueArray == NULL) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_MEMORY_OPERATE_FAILED,
                "keyAndValueArrayShmPtr, segId: %" PRIu32 ", offset: %" PRIu32, next->keyAndValueArrayShmPtr.segId,
                next->keyAndValueArrayShmPtr.offset);
            return GMERR_MEMORY_OPERATE_FAILED;
        }
        for (uint32_t i = 0; i < CACHE_BUCKET_KEY_NUM; i++) {
            int8_t *nextCurrentKey = nextKeyAndValueArray + i * keyLen;
            if (*nextCurrentKey == SLOT_EMPTY_FLAG) {
                // 找到空槽位 replace_insert
                (void)memcpy_s(nextCurrentKey, keyLen, replaceBuf, keyLen);
                int8_t *nextCurrentValue = nextKeyAndValueArray + CACHE_BUCKET_KEY_NUM * keyLen + i * valueLen;
                (void)memcpy_s(nextCurrentValue, valueLen, replaceBuf, valueLen);
                return GMERR_OK;
            }
        }
        next = (CacheSlotT *)DbShmPtrToAddr(next->nextShmPtr);
    }
    return GMERR_OK;
}

uint32_t GetHashFromKey(uint32_t bucketNum, uint32_t keyLen, uint8_t *replaceBuf)
{
    uint32_t hash = DbHash32(replaceBuf, keyLen) % bucketNum;
    return hash;
}

Status GmcExecuteCacheReplace(GmcStmtT *stmt)
{
    // 校验
    Status ret = GmcExecuteCacheCheck(stmt);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    CacheItemT *cache = stmt->cache;

    // 根据replaceBuf获取主键
    uint8_t *replaceBuf = cache->replaceBuf;
    DmVertexLabelCacheT *vlCache = cache->vlCache;
    uint32_t keyLen = vlCache->keyLen;
    uint32_t valueLen = vlCache->valueLen;

    // 根据主键路由到bucket
    uint32_t hash = GetHashFromKey(vlCache->bucketNum, keyLen, replaceBuf);
    CacheBucketT bucketArray = vlCache->bucketArray[hash];

    // 遍历bucket上所有元素
    DbRWSpinWLock(&bucketArray.lock);
    ret = ExecuteCacheReplaceUpdate(&bucketArray, keyLen, valueLen, replaceBuf);
    if (ret == GMERR_OK) {
        DbRWSpinWUnlock(&bucketArray.lock);
        return ret;
    }
    if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
        DbRWSpinWUnlock(&bucketArray.lock);
        return ret;
    }

    DB_ASSERT(ret == GMERR_NO_DATA);

    // 未匹配成功 replace_insert
    ret = ExecuteCacheReplaceInsert(&bucketArray, keyLen, valueLen, replaceBuf);
    DbRWSpinWUnlock(&bucketArray.lock);
    return ret;
}
