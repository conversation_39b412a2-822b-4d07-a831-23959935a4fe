/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: gmc_graph_warm_reboot.c
 * Description: realization of interface of gmdb graph for warm reboot
 * Author:
 * Create: 2024-06-03
 */

#include "gmc_internal.h"

#include "clt_check.h"
#include "clt_stmt.h"
#include "gmc_types.h"

#if defined(WARM_REBOOT) || defined(FEATURE_RSMEM)
static Status SaveExportTablesInfo(GmcStmtT *stmt, char **dst, TextT *data)
{
    // 需传递给外层，其内存由调用者释放
    *dst = DbDynMemCtxAlloc(stmt->memCtx, data->len);
    if (*dst == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Alloc table info, size is %" PRIu32 ".", data->len);
        return GMERR_OUT_OF_MEMORY;
    }
    errno_t err = memcpy_s(*dst, data->len, data->str, data->len);
    if (err != EOK) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "copy name, name is %s.", data->str);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    return GMERR_OK;
}

static Status InitWarmRebootLabelInfo(GmcStmtT *stmt, WarmRebootLabelInfoT *labelInfo, FixBufferT *buffer)
{
    Status ret = GMERR_OK;
    TextT data = {0};
    if ((ret = FixBufGetText(buffer, &data)) != GMERR_OK) {
        return ret;
    }
    ret = SaveExportTablesInfo(stmt, &labelInfo->oriLabelName, &data);
    if (ret != GMERR_OK) {
        goto ERROR_EXIT;
    }

    if ((ret = FixBufGetText(buffer, &data)) != GMERR_OK) {
        goto ERROR_EXIT;
    }
    ret = SaveExportTablesInfo(stmt, &labelInfo->labelName, &data);
    if (ret != GMERR_OK) {
        goto ERROR_EXIT;
    }

    if ((ret = FixBufGetText(buffer, &data)) != GMERR_OK) {
        goto ERROR_EXIT;
    }
    ret = SaveExportTablesInfo(stmt, &labelInfo->namespaceName, &data);
    if (ret != GMERR_OK) {
        goto ERROR_EXIT;
    }
    return GMERR_OK;

ERROR_EXIT:
    DbDynMemCtxFree(stmt->memCtx, labelInfo->oriLabelName);
    DbDynMemCtxFree(stmt->memCtx, labelInfo->labelName);
    DbDynMemCtxFree(stmt->memCtx, labelInfo->namespaceName);
    return ret;
}

static void GetWarmRebootTablesDynFreeInner(void *memCtx, WarmRebootLabelInfoT *labelInfo)
{
    if (labelInfo == NULL) {
        return;
    }
    DbDynMemCtxFree(memCtx, labelInfo->oriLabelName);
    labelInfo->oriLabelName = NULL;
    DbDynMemCtxFree(memCtx, labelInfo->labelName);
    labelInfo->labelName = NULL;
    DbDynMemCtxFree(memCtx, labelInfo->namespaceName);
    labelInfo->namespaceName = NULL;
}

static Status RecvGetWarmRebootTables(GmcStmtT *stmt, DbListT *list, uint32_t *vlCount)
{
    FixBufferT *buffer = &stmt->recvPack;
    uint32_t kvCount = 0;
    Status ret = FixBufGetUint32(buffer, &kvCount);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "kv label count data.");
        return ret;
    }
    if ((ret = FixBufGetUint32(buffer, vlCount)) != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "vertex label count data.");
        return ret;
    }
    for (uint32_t i = 0; i < kvCount + *vlCount; i++) {
        WarmRebootLabelInfoT *labelInfo = DbDynMemCtxAlloc(stmt->memCtx, sizeof(WarmRebootLabelInfoT));
        if (labelInfo == NULL) {
            DB_LOG_AND_SET_LASERR(
                GMERR_OUT_OF_MEMORY, "Alloc labelInfo, size is %" PRIu32 ".", (uint32_t)sizeof(WarmRebootLabelInfoT));
            return GMERR_OUT_OF_MEMORY;
        }
        (void)memset_s(labelInfo, sizeof(WarmRebootLabelInfoT), 0, sizeof(WarmRebootLabelInfoT));
        labelInfo->labelType = i >= *vlCount ? GMC_LABEL_TYPE_KV : GMC_LABEL_TYPE_VERTEX;
        // 里面申请的字符串内存，在外面释放list时释放，FreeLabelInfosList
        ret = InitWarmRebootLabelInfo(stmt, labelInfo, buffer);
        if (ret != GMERR_OK) {
            DbDynMemCtxFree(stmt->memCtx, labelInfo);
            return ret;
        }
        if ((ret = DbAppendListItem(list, labelInfo)) != GMERR_OK) {
            // 表信息没添加到列表，释放内存再退出
            GetWarmRebootTablesDynFreeInner(stmt->memCtx, labelInfo);
            DbDynMemCtxFree(stmt->memCtx, labelInfo);
            return ret;
        }
        DbDynMemCtxFree(stmt->memCtx, labelInfo);
    }
    return GMERR_OK;
}

Status CltGetWarmRebootTables(GmcStmtT *stmt, DbListT *list, uint32_t *count)
{
    DB_POINTER2(stmt, list);
    Status ret = CltBasicCheckWithStmt(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    ProtoHeadT proto = {};
    proto.opCode = MSG_OP_RPC_GET_WARM_REBOOT_TABLES;
    ret = CltStmtSendAndRecvMsg(stmt, &proto);
    if (ret != GMERR_OK) {
        return ret;
    }

    return RecvGetWarmRebootTables(stmt, list, count);
}
#endif
