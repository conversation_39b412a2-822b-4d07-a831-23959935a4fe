/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * File Name: clt_graph_vertex_struct.c
 * Description: Implement of GMDB client graph vertex struct API
 * Author: zhaodu
 * Create: 2020-08-03
 */

#include "clt_graph_vertex_struct.h"
#include "gmc_internal.h"

#include "clt_batch.h"
#include "clt_graph_dml_vertex.h"
#include "clt_graph_filter.h"
#include "clt_msg.h"
#include "clt_resource.h"
#include "dm_data_prop_seri.h"

#define MAX_STACK_SIZE_ALLOWED (1 * DB_KIBI)

static Status CltSubVertexDeseri(GmcStmtT *stmt, GmcDeseriT *deseri, GmcStructureResvT *reservedSize);

Status CltGetVertexDeseri(GmcStmtT *stmt, GmcDeseriT *deseri)
{
    GmcStructureResvT reservedSize;
    // 这里屏蔽FIRST_EDGE_ADDRS对buffer的影响
    reservedSize.fixedPropResvSize = 1;
    reservedSize.propertyAddNum = 1;

    if (stmt->stmtType == CLT_STMT_TYPE_SUB_VERTEX) {
        // 临时方案，需要重构掉
        return CltSubVertexDeseri(stmt, deseri, &reservedSize);
    }
    DmVertexT *vertex = CltGetVertexInStmt(stmt);
    DB_ASSERT(vertex != NULL);  // 外部已检查stmt类型
    uint32_t edgeAddrSize = DmVertexGetEdgeAddrLen(vertex);
    // 读取fetch到的数据，完成后回归偏移位置以支持继续使用非结构化读
    TextT vertexBuf;
    FixBufferT *fixBuf = &stmt->recvPack;
    uint32_t originPos = FixBufGetSeekPos(fixBuf);
    Status ret = FixBufGetObject(fixBuf, &vertexBuf);
    FixBufSeek(fixBuf, originPos);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "get srcBufSize when CltGetVertexBuf");
        return ret;
    }

    // 反序列化系统字段区
    uint32_t sysProLen = DmGetSysPropeSeriBufLen(vertex->vertexDesc);
    void *srcBuf = vertexBuf.str + sysProLen;  // 用户操作的buf需要略过系统字段区

    uint32_t srcBufSize = ((vertexBuf.len - edgeAddrSize) - sysProLen);
    ret = (*deseri->deseriFunc)(deseri, srcBuf, srcBufSize, &reservedSize);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "call Deserialize function.");
        return ret;
    }

    return GMERR_OK;
}

static Status CltSetVertexInSecureFixBufStructure(GmcStmtT *stmt, GmcSeriT *seri, FixBufferT *fixBuffer);

static Status CltSetVertexWithToBatchBuf(GmcStmtT *stmt, GmcSeriT *seri)
{
    GmcBatchT *batch = stmt->batch;
    FixBufferT *fixBuffer = &batch->batchSendBuf;
    CltOperVertexT *operVertex = CltGetOperationContext(stmt);
    const DmVertexLabelT *vertexLabel = operVertex->cltCataLabel->vertexLabel;

    bool isSameOp = false;
    CltBatchLastOpInfoT lastOp = batch->lastOpInfo;  // 栈上结构体直接赋值
    Status ret = CltBatchAddStructureBuf(batch, stmt, vertexLabel, &isSameOp);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = CltSetVertexInSecureFixBufStructure(stmt, seri, fixBuffer);
    if (ret == GMERR_OK) {
        MsgBufUpdateBatchNum(fixBuffer, isSameOp, batch->lastOpInfo.objectNumPos, batch->actualNum);
        return GMERR_OK;
    }

    if (isSameOp) {
        // 回滚actualNum和lastOpinfo
        batch->lastOpInfo = lastOp;
    }
    batch->actualNum--;
    return ret;
}

static Status CltReserveDataForPerformance(GmcStmtT *stmt, FixBufferT *fixBuffer, GmcSeriT *seri)
{
    struct HeaderSize {
        MsgHeaderT msg;
        OpHeaderT op;
    };
    FixBufResetMem(fixBuffer);
    const int32_t count = 6;  // 这里需要覆盖正常路径的全部字段
    uint32_t size = count * (uint32_t)sizeof(uint32_t);
    // 大于报文初始化默认4k才走这个函数。否则会对较小数据的结构化写有影响
    // 针对于结构化单写的性能优化，提前做内存扩容，防止2次扩容
    // 此处需要保证提前扩容的大小，需要包含所有的填充内容
    CltOperVertexT *operVertex = CltGetOperationContext(stmt);
    DmVertexT *currentVertex = operVertex->vertex;
    // 系统字段区对应的长度
    uint32_t sysProLen = DmGetSysPropeSeriBufLen(currentVertex->vertexDesc);
    uint32_t edgePointerSize = DmVertexGetEdgeAddrLen(currentVertex);
    uint32_t offset = 0;
    return SecureFixBufReserveDataOffset(
        fixBuffer, sizeof(struct HeaderSize) + size + sysProLen + seri->bufSize + edgePointerSize, &offset);
}

static Status CltSetVertexWithToStmtBuf(GmcStmtT *stmt, GmcSeriT *seri)
{
    GmcConnT *conn = stmt->conn;
    FixBufferT *fixBuffer = &conn->sendPack;
    CltOperVertexT *operVertex = CltGetOperationContext(stmt);
    const DmVertexLabelT *vertexLabel = operVertex->cltCataLabel->vertexLabel;

    MsgOpcodeRpcE opCode = MSG_OP_RPC_INSERT_VERTEX;
    GmcOperationTypeE opType = stmt->operationType;
    if (opType != GMC_OPERATION_INSERT && opType != GMC_OPERATION_SQL_INSERT &&
        opType != GMC_OPERATION_INSERT_WITH_RESOURCE) {
        DB_ASSERT(opType == GMC_OPERATION_REPLACE || opType == GMC_OPERATION_REPLACE_WITH_RESOURCE ||
                  opType == GMC_OPERATION_CHECK_REPLACE);
        opCode = MSG_OP_RPC_REPLACE_VERTEX;
    }
#if defined(FEATURE_STREAM) || defined(STREAM_MULTI_INST)
    // 设置流计算的opCode
    if (stmt->modelType == MODEL_STREAM && opType == GMC_OPERATION_SQL_INSERT) {
        opCode = MSG_OP_RPC_STREAM_INSERT;
    }
#endif
    Status ret = GMERR_OK;
    if (seri->bufSize > CS_PACK_SIZE) {
        ret = CltReserveDataForPerformance(stmt, fixBuffer, seri);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_AND_SET_LASERR(ret, "Reserve data for performance.");
            return ret;
        }
    }

    ret = CltFillMsgHeader(fixBuffer, opCode, stmt->stmtId);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    if (stmt->isDataService) {
        MsgHeaderT *header = RpcPeekMsgHeader(fixBuffer);
        CltSetDataServiceHeaderInfo(header, opCode);
    }

    // fill label id, schema version and vertex num (default is 1)
    ret = SecureFixBufPut4Uint32(fixBuffer, vertexLabel->metaCommon.metaId, vertexLabel->metaCommon.version,
        vertexLabel->metaVertexLabel->uuid, 1);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    return CltSetVertexInSecureFixBufStructure(stmt, seri, fixBuffer);
}

Status CltSetVertexWithBufForDw(GmcStmtT *stmt, GmcSeriT *seri)
{
    CltOperVertexT *operVertex = CltGetOperationContext(stmt);
    DmVertexDescT *vertexDesc = operVertex->vertex->vertexDesc;
    // 这里需要通过 dm 获取
    GmcStructureResvT reservedSize = {.fixedPropResvSize = 1, .propertyAddNum = 1};
    // 直连写情况下，edgeLabelNum 一定为 0
    uint32_t memSize = seri->bufSize + DM_EDGE_ADDRS_BYTES(0) + DmGetSysPropeSeriBufLen(vertexDesc);
    DwRunCtxT *dwRunCtx = stmt->dwRunCtx;
    if (dwRunCtx->structWriteBufLen != memSize) {
        DbDynMemCtxFree(stmt->memCtx, dwRunCtx->structWriteBuf);
        dwRunCtx->structWriteBuf = DbDynMemCtxAlloc(stmt->memCtx, memSize);
        if (SECUREC_UNLIKELY(dwRunCtx->structWriteBuf == NULL)) {
            dwRunCtx->structWriteBufLen = 0;
            DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "alloc (%" PRIu32 ") for dwRunctx struct write buf", memSize);
            return GMERR_OUT_OF_MEMORY;
        }
        dwRunCtx->structWriteBufLen = memSize;
    }
    uint8_t *bufCursor = dwRunCtx->structWriteBuf;
    *(uint8_t *)bufCursor = (uint8_t)vertexDesc->sysPropeTotalLen;
    // 偏移记录 vertexDesc->sysPropeTotalLen 所用的长度
    bufCursor += (uint32_t)sizeof(uint8_t);
    // 系统字段长度为 1，表明只有 CHECK_VERSION 一个系统字段
    if (SECUREC_LIKELY(vertexDesc->sysPropeTotalLen == 1)) {
        *(uint8_t *)bufCursor = 0;
    } else {
        (void)memset_s(bufCursor, vertexDesc->sysPropeTotalLen, 0x00, vertexDesc->sysPropeTotalLen);
    }
    bufCursor += vertexDesc->sysPropeTotalLen;
    Status ret = (*seri->seriFunc)(seri, bufCursor, &reservedSize);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_AND_SET_LASERR(ret, "call serialization function.");
        DbDynMemCtxFree(stmt->memCtx, dwRunCtx->structWriteBuf);
        dwRunCtx->structWriteBuf = NULL;
        dwRunCtx->structWriteBufLen = 0;
        return ret;
    }
    // 放入edge adderss信息, 这里应该是vertex序列化的一部分但是外围难以感知
    uint8_t *edgeAddrBuff = bufCursor + seri->bufSize;
    // 省略了 memset_s 0
    *((uint32_t *)edgeAddrBuff) = DmVertexGetEdgeLabelNum(operVertex->vertex);
    return GMERR_OK;
}

Status CltSetVertexWithBuf(GmcStmtT *stmt, GmcSeriT *seri)
{
    Status ret;
    if (stmt->batch == NULL) {
        if (stmt->openDirectWrite) {
            ret = CltSetVertexWithBufForDw(stmt, seri);
            if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                DB_LOG_AND_SET_LASERR(ret, "Clt set vertex buf for direct write.");
                return ret;
            }
            stmt->isStructure = true;
            return GMERR_OK;
        } else {
            ret = CltSetVertexWithToStmtBuf(stmt, seri);
        }
    } else {
        ret = CltSetVertexWithToBatchBuf(stmt, seri);
    }
    if (SECUREC_LIKELY(ret == GMERR_OK)) {
        stmt->isStructure = true;
        return GMERR_OK;
    }

    DB_LOG_AND_SET_LASERR(ret, "CltSetVertexWithBuf when fill message to buf.");
    return ret;
}

static Status CltSeriVertex2InvokerBuf(
    DmVertexT *vertex, uint8_t *tempSrcbuf, uint32_t srcBufSize, GmcStructBufferT *inputBufInfo)
{
    Status ret = DmSerializeVertex2InvokerBuf(vertex, srcBufSize, tempSrcbuf);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_AND_SET_LASERR(ret, "serialize vertex when copy sub push buf worthless");
        return ret;
    }
    uint8_t vertexOffset = vertex->vertexDesc->vertexOffset;
    errno_t errNumber =
        memcpy_s(inputBufInfo->buf, inputBufInfo->bufLen, tempSrcbuf + vertexOffset, inputBufInfo->bufLen);
    if (SECUREC_UNLIKELY(errNumber != EOK)) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "copy vertex buffer to inputBufInfo when copy sub push buf");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    return GMERR_OK;
}

Status CltCopySubBuf(GmcStmtT *stmt, GmcStructBufferT *inputBufInfo)
{
    uint32_t srcBufSize = 0;
    DmVertexT *vertex = ((const CltOperVertexT *)CltGetConstOperationContext(stmt))->vertex;
    DB_ASSERT(vertex != NULL);
    // 订阅用临时方案支持全表扫，之后fetch重构完成之后之这里修改为直接读取返回报文
    Status ret = DmVertexGetSeriBufLength(vertex, &srcBufSize);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "get buf len when copy sub push buf");
        return ret;
    }
    uint8_t vertexOffset = vertex->vertexDesc->vertexOffset;
    if (srcBufSize < vertexOffset + inputBufInfo->bufLen) {
        ret = GMERR_INVALID_PARAMETER_VALUE;
        DB_LOG_AND_SET_LASERR(ret, "buf len plus offset greater than srcBufSize len when copy sub push buf");
        return ret;
    }
    if (srcBufSize < MAX_STACK_SIZE_ALLOWED) {  // 主要是为了性能场景下用栈内存速度更快
        uint8_t tempSrcbuf[MAX_STACK_SIZE_ALLOWED];
        ret = CltSeriVertex2InvokerBuf(vertex, tempSrcbuf, srcBufSize, inputBufInfo);
    } else {  // 内存过大时无法使用栈内存，改用动态内存
        // 本函数内申请释放
        uint8_t *tempSrcbuf = (uint8_t *)DbDynMemCtxAlloc(stmt->memCtx, srcBufSize);
        if (tempSrcbuf == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "alloc size %" PRIu32 " for sub src buffer.", srcBufSize);
            return GMERR_OUT_OF_MEMORY;
        }
        ret = CltSeriVertex2InvokerBuf(vertex, tempSrcbuf, srcBufSize, inputBufInfo);
        DbDynMemCtxFree(stmt->memCtx, tempSrcbuf);  // 只在函数内部使用，内存指针没有往外传递，不用置空
    }
    return ret;
}

static Status CltSeriVertexInSecureFixBufStructure(
    FixBufferT *fixBuffer, GmcStmtT *stmt, GmcSeriT *seri, uint32_t sysProLen, uint32_t edgePointerSize)
{
    // 放入序列化好的record信息
    CltOperVertexT *operVertex = CltGetOperationContext(stmt);
    DmVertexT *currentVertex = operVertex->vertex;
    uint32_t offset;
    Status ret = SecureFixBufReserveDataOffset(fixBuffer, sysProLen + seri->bufSize + edgePointerSize, &offset);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    uint8_t *destBuf = SecureFixBufOffsetToAddr(fixBuffer, offset);
#ifdef SECUREFIXBUF
    if (SECUREC_UNLIKELY(destBuf == NULL)) {
        return GMERR_CONNECTION_RESET_BY_PEER;
    }
#endif
    // 填充系统字段区
    uint8_t *destBufCursor = destBuf;
    DmSeriSysPrope(currentVertex, &destBufCursor);
    // 这里需要同过dm获取
    GmcStructureResvT reservedSize;
    reservedSize.fixedPropResvSize = 1;
    reservedSize.propertyAddNum = 1;
    // 标记vertexBuf位置用于checkreplace时结构化下能找到vertexBuf
    operVertex->seriVertexBuf.str = (char *)destBuf;
    operVertex->seriVertexBuf.len = sysProLen + seri->bufSize + edgePointerSize;

    ret = (*seri->seriFunc)(seri, destBufCursor, &reservedSize);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_AND_SET_LASERR(ret, "call serialization function.");
        return ret;
    }
    // 放入edge adderss信息, 这里应该是vertex序列化的一部分但是外围难以感知
    uint8_t *edgeAddrBuff = destBufCursor + seri->bufSize;
    uint32_t edgeNum = DmVertexGetEdgeLabelNum(currentVertex);
    if (edgeNum) {
        (void)memset_s(edgeAddrBuff, edgePointerSize, 0x00, edgePointerSize);
    }
    *((uint32_t *)edgeAddrBuff) = edgeNum;
    return ret;
}

static Status CltSetVertexInSecureFixBufStructure(GmcStmtT *stmt, GmcSeriT *seri, FixBufferT *fixBuffer)
{
    // 序列化vertex开始
    CltOperVertexT *operVertex = CltGetOperationContext(stmt);
    DmVertexT *currentVertex = operVertex->vertex;
    // 系统字段区对应的长度
    uint32_t sysProLen = DmGetSysPropeSeriBufLen(currentVertex->vertexDesc);
    uint32_t edgePointerSize = DmVertexGetEdgeAddrLen(currentVertex);
    Status ret = FixBufPutUint32(fixBuffer, sysProLen + seri->bufSize + edgePointerSize);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_AND_SET_LASERR(ret, "call serialization function.");
        return ret;
    }

    ret = CltSeriVertexInSecureFixBufStructure(fixBuffer, stmt, seri, sysProLen, edgePointerSize);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    // 序列化vertex结束放入operateEdgeFlag
    if (SECUREC_UNLIKELY((ret = FixBufPutUint32(fixBuffer, stmt->operateEdgeFlag)) != GMERR_OK)) {
        DB_LOG_AND_SET_LASERR(ret, "fill operateEdgeFlag when fill VertexMsgBody.");
        return ret;
    }
    VertexLabelCommonInfoT *commonInfo = currentVertex->vertexDesc->commonInfo;
    if (SECUREC_UNLIKELY(commonInfo->dlrInfo.isDataSyncLabel)) {
        // 放入isReplay
        if ((ret = FixBufPutUint32(fixBuffer, 0)) != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "fill isReplay when fill VertexMsgBody.");
            return ret;
        }
    }
    return ret;
}

static Status CltSubVertexDeseriInner(
    GmcDeseriT *deseri, DmVertexT *vertex, TextT *srcBuf, uint32_t edgeAddrSize, GmcStructureResvT *reservedSize)
{
    Status ret = DmSerializeVertex2InvokerBuf(vertex, srcBuf->len, (uint8_t *)srcBuf->str);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "copy buf when CltGetVertexBuf");
        return ret;
    }
    uint32_t sysProLen = DmGetSysPropeSeriBufLen(vertex->vertexDesc);
    srcBuf->str += sysProLen;
    srcBuf->len -= edgeAddrSize;
    srcBuf->len -= sysProLen;
    ret = (*deseri->deseriFunc)(deseri, (uint8_t *)srcBuf->str, srcBuf->len, reservedSize);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "call Deserialize function in sub seri.");
        return ret;
    }
    return GMERR_OK;
}

static Status CltSubVertexDeseri(GmcStmtT *stmt, GmcDeseriT *deseri, GmcStructureResvT *reservedSize)
{
    uint32_t srcBufSize = 0;
    DmVertexT *vertex = CltGetVertexInStmt(stmt);
    DB_POINTER(vertex);
    // 订阅用临时方案支持全表扫，之后fetch重构完成之后之这里修改为直接读取返回报文
    Status ret = DmVertexGetSeriBufLength(vertex, &srcBufSize);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "get buf len when get seribuf length");
        return ret;
    }
    uint32_t edgeAddrSize = DmVertexGetEdgeAddrLen(vertex);
    if (srcBufSize < MAX_STACK_SIZE_ALLOWED) {  // 主要是为了性能场景下用栈内存速度更快
        uint8_t tempSrcbuf[MAX_STACK_SIZE_ALLOWED];
        TextT srcBuf = {.str = (char *)tempSrcbuf, .len = srcBufSize};
        ret = CltSubVertexDeseriInner(deseri, vertex, &srcBuf, edgeAddrSize, reservedSize);
    } else {  // 内存过大时无法使用栈内存，改用动态内存
        // 本函数内申请释放
        uint8_t *tempSrcbuf = (uint8_t *)DbDynMemCtxAlloc(stmt->memCtx, srcBufSize);
        if (tempSrcbuf == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "alloc %" PRIu32 " for sub vertex src buffer.", srcBufSize);
            return GMERR_OUT_OF_MEMORY;
        }
        TextT srcBuf = {.str = (char *)tempSrcbuf, .len = srcBufSize};
        ret = CltSubVertexDeseriInner(deseri, vertex, &srcBuf, edgeAddrSize, reservedSize);
        DbDynMemCtxFree(stmt->memCtx, tempSrcbuf);  // 只在函数内部使用，内存指针没有往外传递，不用置空
    }
    return ret;
}
