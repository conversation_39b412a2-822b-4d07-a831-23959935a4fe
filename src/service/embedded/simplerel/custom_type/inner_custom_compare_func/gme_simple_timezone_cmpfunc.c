/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012-2024. All rights reserved.
 * File Name: gme_simple_timezone_cmpfunc.c
 * Description: Type time zone compare func source file
 * Author:
 * Create:
 */
#include "gme_simple_timezone_cmpfunc.h"

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif /* __cplusplus */
#endif /* __cplusplus */

/*******************************************************************************
 Function    : TPC_TIMEZONE_ulCond
 Description : Convert DB_TIMEZONE_STRU type to VOS_UINT32 type.
 Input       : pstCond - Pointer containing value in the condition
 Output      : ulCond - 32-bit time value
 Return      : ulCond
*******************************************************************************/
static VOS_UINT32 TPC_TIMEZONE_ulCond(const DB_TIMEZONE_STRU *pstCond)
{
    VOS_UINT32 ulCond;

    ulCond = pstCond->ucUTCSign;
    ulCond = ulCond ^ 1;
    ulCond <<= DBKNL_BYTE;
    ulCond += pstCond->ucUTCHour;
    ulCond <<= DBKNL_BYTE;
    ulCond += pstCond->ucUTCMinute;

    return ulCond;
}

/*******************************************************************************
 Function    : TPC_TIMEZONE_Equal
 Description : Checks if the input time is equal to the time in the input
               condition.Time comparion is done based on each byte.
 Input       : pFieldValue - Input value of the field to be compared
               pCondValue  - Pointer containing value in the condition
               usLen       - Length of comparison
 Output      : pbMatch - boolean pointer indicates, values are matching or not
               pucDir  - value indicates further search has to be made or not
 Return      : DB_SUCCESS_V1 on success, error codes on failure
*******************************************************************************/
DB_ERR_CODE TPC_TIMEZONE_Equal(
    const VOS_VOID *pFieldValue, const VOS_VOID *pCondValue, VOS_UINT16 usLen, VOS_BOOL *pbMatch, VOS_UINT8 *pucDir)
{
    DB_POINTER4(pFieldValue, pCondValue, pbMatch, pucDir);
    DB_UNUSED(usLen);
    const DB_TIMEZONE_STRU *pstField = (const DB_TIMEZONE_STRU *)pFieldValue;
    const DB_TIMEZONE_STRU *pstCond = (const DB_TIMEZONE_STRU *)pCondValue;

    VOS_UINT32 ulField = TPC_TIMEZONE_ulCond(pstField);
    VOS_UINT32 ulCondTimeZone = TPC_TIMEZONE_ulCond(pstCond);
    if (ulField == ulCondTimeZone) {
        *pbMatch = VOS_TRUE;
        *pucDir = DBTC_SEARCH_ALL;
    } else if (ulField < ulCondTimeZone) {
        *pbMatch = VOS_FALSE;
        *pucDir = DBTC_SEARCH_LARGE;
    } else {
        *pbMatch = VOS_FALSE;
        *pucDir = DBTC_SEARCH_SMALL;
    }

    return DB_SUCCESS_V1;
}

/*******************************************************************************
 Function    : TPC_TIMEZONE_NotEqual
 Description : Checks if the input time is not equal to the time in the
               input condition.Time comparion is done based on each byte.
 Input       : pFieldValue - Input value of the field to be compared
               pCondValue  - Pointer containing value in the condition
               usLen       - Length of comparison
 Output      : pbMatch - boolean pointer indicates, values are matching or not
               pucDir  - value indicates further search has to be made or not
 Return      : DB_SUCCESS_V1 on success, error codes on failure
*******************************************************************************/
DB_ERR_CODE TPC_TIMEZONE_NotEqual(
    const VOS_VOID *pFieldValue, const VOS_VOID *pCondValue, VOS_UINT16 usLen, VOS_BOOL *pbMatch, VOS_UINT8 *pucDir)
{
    DB_POINTER4(pFieldValue, pCondValue, pbMatch, pucDir);
    DB_UNUSED(usLen);
    const DB_TIMEZONE_STRU *pstField = (const DB_TIMEZONE_STRU *)pFieldValue;
    const DB_TIMEZONE_STRU *pstCond = (const DB_TIMEZONE_STRU *)pCondValue;

    VOS_UINT32 ulField = TPC_TIMEZONE_ulCond(pstField);
    VOS_UINT32 ulCondTimeZone = TPC_TIMEZONE_ulCond(pstCond);
    if (ulField == ulCondTimeZone) {
        *pbMatch = VOS_FALSE;
        *pucDir = DBTC_SEARCH_ALL;
    } else {
        *pbMatch = VOS_TRUE;
        *pucDir = DBTC_SEARCH_ALL;
    }

    return DB_SUCCESS_V1;
}

/*******************************************************************************
 Function    : TPC_TIMEZONE_Small
 Description : Checks if the input time is smaller than the time in the
               input condition.Time comparion is done based on each byte.
 Input       : pFieldValue - Input value of the field to be compared
               pCondValue  - Pointer containing value in the condition
               usLen       - Length of comparison
 Output      : pbMatch - boolean pointer indicates, values are matching or not
               pucDir  - value indicates further search has to be made or not
 Return      : DB_SUCCESS_V1 on success, error codes on failure
*******************************************************************************/
DB_ERR_CODE TPC_TIMEZONE_Small(
    const VOS_VOID *pFieldValue, const VOS_VOID *pCondValue, VOS_UINT16 usLen, VOS_BOOL *pbMatch, VOS_UINT8 *pucDir)
{
    DB_POINTER4(pFieldValue, pCondValue, pbMatch, pucDir);
    DB_UNUSED(usLen);
    const DB_TIMEZONE_STRU *pstField = (const DB_TIMEZONE_STRU *)pFieldValue;
    const DB_TIMEZONE_STRU *pstCond = (const DB_TIMEZONE_STRU *)pCondValue;

    VOS_UINT32 ulField = TPC_TIMEZONE_ulCond(pstField);
    VOS_UINT32 ulCondTimeZone = TPC_TIMEZONE_ulCond(pstCond);
    if (ulField < ulCondTimeZone) {
        *pbMatch = VOS_TRUE;
        *pucDir = DBTC_SEARCH_ALL;
    } else {
        *pbMatch = VOS_FALSE;
        *pucDir = DBTC_SEARCH_SMALL;
    }

    return DB_SUCCESS_V1;
}

/*******************************************************************************
 Function    : TPC_TIMEZONE_SmallEqual
 Description : Checks if the input time is smaller or equal to than time in
               input condition.Time comparison is done based on each byte.
 Input       : pFieldValue - Input value of the field to be compared
               pCondValue  - Pointer containing value in the condition
               usLen       - Length of comparison
 Output      : pbMatch - boolean pointer indicates, values are matching or not
               pucDir  - value indicates further search has to be made or not
 Return      : DB_SUCCESS_V1 on success, error codes on failure
*******************************************************************************/
DB_ERR_CODE TPC_TIMEZONE_SmallEqual(
    const VOS_VOID *pFieldValue, const VOS_VOID *pCondValue, VOS_UINT16 usLen, VOS_BOOL *pbMatch, VOS_UINT8 *pucDir)
{
    DB_POINTER4(pFieldValue, pCondValue, pbMatch, pucDir);
    DB_UNUSED(usLen);
    const DB_TIMEZONE_STRU *pstField = (const DB_TIMEZONE_STRU *)pFieldValue;
    const DB_TIMEZONE_STRU *pstCond = (const DB_TIMEZONE_STRU *)pCondValue;

    VOS_UINT32 ulField = TPC_TIMEZONE_ulCond(pstField);
    VOS_UINT32 ulCondTimeZone = TPC_TIMEZONE_ulCond(pstCond);
    if (ulField <= ulCondTimeZone) {
        *pbMatch = VOS_TRUE;
        *pucDir = DBTC_SEARCH_ALL;
    } else {
        *pbMatch = VOS_FALSE;
        *pucDir = DBTC_SEARCH_SMALL;
    }

    return DB_SUCCESS_V1;
}

/*******************************************************************************
 Function    : TPC_TIMEZONE_Large
 Description : Checks if the input time is larger than time in
               input condition.Time comparison is done based on each byte.
 Input       : pFieldValue - Input value of the field to be compared
               pCondValue  - Pointer containing value in the condition
               usLen       - Length of comparison
 Output      : pbMatch - boolean pointer indicates, values are matching or not
               pucDir  - value indicates further search has to be made or not
 Return      : DB_SUCCESS_V1 on success, error codes on failure
*******************************************************************************/
DB_ERR_CODE TPC_TIMEZONE_Large(
    const VOS_VOID *pFieldValue, const VOS_VOID *pCondValue, VOS_UINT16 usLen, VOS_BOOL *pbMatch, VOS_UINT8 *pucDir)
{
    DB_POINTER4(pFieldValue, pCondValue, pbMatch, pucDir);
    DB_UNUSED(usLen);
    const DB_TIMEZONE_STRU *pstField = (const DB_TIMEZONE_STRU *)pFieldValue;
    const DB_TIMEZONE_STRU *pstCond = (const DB_TIMEZONE_STRU *)pCondValue;

    VOS_UINT32 ulField = TPC_TIMEZONE_ulCond(pstField);
    VOS_UINT32 ulCondTimeZone = TPC_TIMEZONE_ulCond(pstCond);
    if (ulField > ulCondTimeZone) {
        *pbMatch = VOS_TRUE;
        *pucDir = DBTC_SEARCH_ALL;
    } else {
        *pbMatch = VOS_FALSE;
        *pucDir = DBTC_SEARCH_LARGE;
    }

    return DB_SUCCESS_V1;
}

/*******************************************************************************
  Function    : TPC_TIMEZONE_LargeEqual
  Description : Checks if the input time is larger or equal to than time in
                input condition.Time comparion is done based on each byte.
  Input       : pFieldValue - Input value of the field to be compared
                pCondValue  - Pointer containing value in the condition
                usLen       - Length of comparison
  Output      : pbMatch - boolean pointer indicates, values are matching or not
                pucDir  - value indicates further search has to be made or not
  Return      : DB_SUCCESS_V1 on success, error codes on failure
*******************************************************************************/
DB_ERR_CODE TPC_TIMEZONE_LargeEqual(
    const VOS_VOID *pFieldValue, const VOS_VOID *pCondValue, VOS_UINT16 usLen, VOS_BOOL *pbMatch, VOS_UINT8 *pucDir)
{
    DB_POINTER4(pFieldValue, pCondValue, pbMatch, pucDir);
    DB_UNUSED(usLen);
    const DB_TIMEZONE_STRU *pstField = (const DB_TIMEZONE_STRU *)pFieldValue;
    const DB_TIMEZONE_STRU *pstCond = (const DB_TIMEZONE_STRU *)pCondValue;

    VOS_UINT32 ulField = TPC_TIMEZONE_ulCond(pstField);
    VOS_UINT32 ulCondTimeZone = TPC_TIMEZONE_ulCond(pstCond);
    if (ulField >= ulCondTimeZone) {
        *pbMatch = VOS_TRUE;
        *pucDir = DBTC_SEARCH_ALL;
    } else {
        *pbMatch = VOS_FALSE;
        *pucDir = DBTC_SEARCH_LARGE;
    }

    return DB_SUCCESS_V1;
}

#ifdef __cplusplus
#if __cplusplus
}
#endif /* __cplusplus */
#endif /* __cplusplus */
