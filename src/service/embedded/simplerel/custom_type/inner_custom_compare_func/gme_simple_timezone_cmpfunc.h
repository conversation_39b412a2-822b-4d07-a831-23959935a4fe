/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012-2024. All rights reserved.
 * File Name: gme_simple_timezone_cmpfunc.h
 * Description: Type time zone compare func header file
 * Author:
 * Create:
 */
#ifndef GME_SIMPREL_TIMEZONE_H
#define GME_SIMPREL_TIMEZONE_H

#include "gme_simple_inner_custom_common.h"

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif /* __cplusplus */
#endif /* __cplusplus */

/* Functions for TIMEZONE comparison */
/* TimeZone Equal */
DB_ERR_CODE TPC_TIMEZONE_Equal(
    const VOS_VOID *pFieldValue, const VOS_VOID *pCondValue, VOS_UINT16 usLen, VOS_BOOL *pbMatch, VOS_UINT8 *pucDir);

/* TimeZone not Equal */
DB_ERR_CODE TPC_TIMEZONE_NotEqual(
    const VOS_VOID *pFieldValue, const VOS_VOID *pCondValue, VOS_UINT16 usLen, VOS_BOOL *pbMatch, VOS_UINT8 *pucDir);

/* TimeZone Small */
DB_ERR_CODE TPC_TIMEZONE_Small(
    const VOS_VOID *pFieldValue, const VOS_VOID *pCondValue, VOS_UINT16 usLen, VOS_BOOL *pbMatch, VOS_UINT8 *pucDir);

/* TimeZone Small Equal */
DB_ERR_CODE TPC_TIMEZONE_SmallEqual(
    const VOS_VOID *pFieldValue, const VOS_VOID *pCondValue, VOS_UINT16 usLen, VOS_BOOL *pbMatch, VOS_UINT8 *pucDir);

/* TimeZone Large */
DB_ERR_CODE TPC_TIMEZONE_Large(
    const VOS_VOID *pFieldValue, const VOS_VOID *pCondValue, VOS_UINT16 usLen, VOS_BOOL *pbMatch, VOS_UINT8 *pucDir);

/* TimeZone Large Equal */
DB_ERR_CODE TPC_TIMEZONE_LargeEqual(
    const VOS_VOID *pFieldValue, const VOS_VOID *pCondValue, VOS_UINT16 usLen, VOS_BOOL *pbMatch, VOS_UINT8 *pucDir);

#ifdef __cplusplus
#if __cplusplus
}
#endif /* __cplusplus */
#endif /* __cplusplus */

#endif /* _GME_SIMPREL_TIMEZONE_H */
