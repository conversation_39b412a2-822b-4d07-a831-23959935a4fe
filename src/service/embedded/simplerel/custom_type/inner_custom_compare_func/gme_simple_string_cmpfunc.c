/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012-2024. All rights reserved.
 * File Name: gme_simple_macaddr_cmpfunc.c
 * Description: Type mac address compare func source file
 * Author:
 * Create:
 */
#include "gme_simple_string_cmpfunc.h"

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif /* __cplusplus */
#endif /* __cplusplus */

#define DB_NUM_MINUS_ONE (-1)

/*******************************************************************************
 Function    : TPC_STRING_Equal
 Description : Compares if the string input is equal to input condition
 Input       : pFieldValue - Input value of the field to be compared
               pCondValue  - pointer containing value in the condition
               usLen       - length of comparison
 Output      : pbMatch - boolean pointer indicates, values are matching or not
               pucDir  - value indicates further search has to be made or not
 Return      : DB_SUCCESS on success, error codes on failure
*******************************************************************************/
DB_ERR_CODE TPC_STRING_Equal(
    const VOS_VOID *pFieldValue, const VOS_VOID *pCondValue, VOS_UINT16 usLen, VOS_BOOL *pbMatch, VOS_UINT8 *pucDir)
{
    VOS_INT32 cRslt = strncmp((const char *)pFieldValue, (const char *)pCondValue, usLen);
    if (cRslt == 0) {
        *pbMatch = VOS_TRUE;
        *pucDir = DBTC_SEARCH_ALL;
    } else if (cRslt > 0) {
        /* If the pucField  is greater than pucCond, cRslt is 1               */
        *pbMatch = VOS_FALSE;
        *pucDir = DBTC_SEARCH_SMALL;
    } else {
        /* If the pucField  is lesser than pucCond, cRslt is 1                */
        *pbMatch = VOS_FALSE;
        *pucDir = DBTC_SEARCH_LARGE;
    }

    return DB_SUCCESS;
}

/*******************************************************************************
 Function    : TPC_STRING_NotEqual
 Description : Compares if the string input is not equal to input condition
 Input       : pFieldValue - Input value of the field to be compared
               pCondValue  - pointer containing value in the condition
               usLen       - length of comparison
 Output      : pbMatch - boolean pointer indicates, values are matching or not
               pucDir  - value indicates further search has to be made or not
 Return      : DB_SUCCESS on success, error codes on failure
*******************************************************************************/
DB_ERR_CODE TPC_STRING_NotEqual(
    const VOS_VOID *pFieldValue, const VOS_VOID *pCondValue, VOS_UINT16 usLen, VOS_BOOL *pbMatch, VOS_UINT8 *pucDir)
{
    VOS_INT32 cRslt = strncmp((const char *)pFieldValue, (const char *)pCondValue, usLen);
    if (cRslt == 0) {
        *pbMatch = VOS_FALSE;
    } else {
        *pbMatch = VOS_TRUE;
    }

    *pucDir = DBTC_SEARCH_ALL;
    return DB_SUCCESS;
}

/*******************************************************************************
 Function    : TPC_STRING_Small
 Description : Compares if the string input is smaller than input
               condition
 Input       : pFieldValue - Input value of the field to be compared
               pCondValue  - pointer containing value in the condition
               usLen       - length of comparison
 Output      : pbMatch - boolean pointer indicates, values are matching or not
               pucDir  - value indicates further search has to be made or not
 Return      : DB_SUCCESS on success, error codes on failure
*******************************************************************************/
DB_ERR_CODE TPC_STRING_Small(
    const VOS_VOID *pFieldValue, const VOS_VOID *pCondValue, VOS_UINT16 usLen, VOS_BOOL *pbMatch, VOS_UINT8 *pucDir)
{
    VOS_INT32 cRslt = strncmp((const char *)pFieldValue, (const char *)pCondValue, usLen);
    if (cRslt < 0) {
        *pbMatch = VOS_TRUE;
        *pucDir = DBTC_SEARCH_ALL;
    } else {
        *pbMatch = VOS_FALSE;
        *pucDir = DBTC_SEARCH_SMALL;
    }

    return DB_SUCCESS;
}

/*******************************************************************************
 Function    : TPC_STRING_SmallEqual
 Description : Compares if the string input is smaller or equal to the input
               condition
 Input       : pFieldValue - Input value of the field to be compared
               pCondValue  - pointer containing value in the condition
               usLen       - length of comparison
 Output      : pbMatch - boolean pointer indicates, values are matching or not
               pucDir  - value indicates further search has to be made or not
 Return      : DB_SUCCESS on success, error codes on failure
*******************************************************************************/
DB_ERR_CODE TPC_STRING_SmallEqual(
    const VOS_VOID *pFieldValue, const VOS_VOID *pCondValue, VOS_UINT16 usLen, VOS_BOOL *pbMatch, VOS_UINT8 *pucDir)
{
    VOS_INT32 cRslt = strncmp((const char *)pFieldValue, (const char *)pCondValue, usLen);
    if (cRslt <= 0) {
        *pbMatch = VOS_TRUE;
        *pucDir = DBTC_SEARCH_ALL;
    } else {
        *pbMatch = VOS_FALSE;
        *pucDir = DBTC_SEARCH_SMALL;
    }

    return DB_SUCCESS;
}

/*******************************************************************************
 Function    : TPC_STRING_Large
 Description : Compares if the string input is lager than input condition
 Input       : pFieldValue - Input value of the field to be compared
               pCondValue  - pointer containing value in the condition
               usLen       - length of comparison
 Output      : pbMatch - boolean pointer indicates, values are matching or not
               pucDir  - value indicates further search has to be made or not
 Return      : DB_SUCCESS on success, error codes on failure
*******************************************************************************/
DB_ERR_CODE TPC_STRING_Large(
    const VOS_VOID *pFieldValue, const VOS_VOID *pCondValue, VOS_UINT16 usLen, VOS_BOOL *pbMatch, VOS_UINT8 *pucDir)
{
    VOS_INT32 cRslt = strncmp((const char *)pFieldValue, (const char *)pCondValue, usLen);
    if (cRslt > 0) {
        *pbMatch = VOS_TRUE;
        *pucDir = DBTC_SEARCH_ALL;
    } else {
        *pbMatch = VOS_FALSE;
        *pucDir = DBTC_SEARCH_LARGE;
    }

    return DB_SUCCESS;
}

/*******************************************************************************
 Function    : TPC_STRING_LargeEqual
 Description : Compares if the string input is lager or equal to input condition
 Input       : pFieldValue - Input value of the field to be compared
               pCondValue  - pointer containing value in the condition
               usLen       - length of comparison
 Output      : pbMatch - boolean pointer indicates, values are matching or not
               pucDir  - value indicates further search has to be made or not
 Return      : DB_SUCCESS on success, error codes on failure
*******************************************************************************/
DB_ERR_CODE TPC_STRING_LargeEqual(
    const VOS_VOID *pFieldValue, const VOS_VOID *pCondValue, VOS_UINT16 usLen, VOS_BOOL *pbMatch, VOS_UINT8 *pucDir)
{
    VOS_INT32 cRslt = strncmp((const char *)pFieldValue, (const char *)pCondValue, usLen);
    if (cRslt >= 0) {
        *pbMatch = VOS_TRUE;
        *pucDir = DBTC_SEARCH_ALL;
    } else {
        *pbMatch = VOS_FALSE;
        *pucDir = DBTC_SEARCH_LARGE;
    }

    return DB_SUCCESS;
}

/*******************************************************************************
 Function    : TPC_STRING_Prefix
 Description : Check if prefixes of field string and condition string are
               same till the specified length or end or field (or) condition
               string end is reached.
 Input       : pFieldValue - Input value of the field to be compared
               pCondValue  - pointer containing value in the condition
               usLen       - length of comparison
 Output      : pbMatch - boolean pointer indicates, values are matching or not
               pucDir  - value indicates further search has to be made or not
 Return      : DB_SUCCESS on success, error codes on failure
*******************************************************************************/
DB_ERR_CODE TPC_STRING_Prefix(
    const VOS_VOID *pFieldValue, const VOS_VOID *pCondValue, VOS_UINT16 usLen, VOS_BOOL *pbMatch, VOS_UINT8 *pucDir)
{
    VOS_UINT16 usIndex = 0;
    const VOS_UINT8 *pucField = VOS_NULL_PTR;
    const VOS_UINT8 *pucCond = VOS_NULL_PTR;

    pucField = (const VOS_UINT8 *)pFieldValue;
    pucCond = (const VOS_UINT8 *)pCondValue;

    *pbMatch = VOS_TRUE;
    *pucDir = DBTC_SEARCH_ALL;

    /* Compare first usLen bytes of Field string with condition string.       */
    while (((*pucField != 0) && (*pucCond != 0)) && (usIndex < usLen)) {
        /*
         * If the current byte of field string is not same as condition string
         * return the comparison result Large or Small which ever is appropriate
         */
        if (*pucField != *pucCond) {
            *pbMatch = VOS_FALSE;
            if (*pucField < *pucCond) {
                *pucDir = DBTC_SEARCH_LARGE;
            } else {
                *pucDir = DBTC_SEARCH_SMALL;
            }
            break;
        }

        pucField++;
        pucCond++;
        usIndex++;
    }

    /* Field string prefix matches the condition string.                      */
    return DB_SUCCESS;
}

/*******************************************************************************
 Function    : TPC_STRING_HavePrefix
 Description : Check if condition string is same as the field string prefix.
               Compare prefixes of field string and condition string for the
               specified length or condition string end is reached
 Input       : pFieldValue - Input value of the field to be compared
               pCondValue  - pointer containing value in the condition
               usLen       - length of comparison
 Output      : pbMatch - boolean pointer indicates, values are matching or not
               pucDir  - value indicates further search has to be made or not
 Return      : DB_SUCCESS on success, error codes on failure
*******************************************************************************/
DB_ERR_CODE TPC_STRING_HavePrefix(
    const VOS_VOID *pFieldValue, const VOS_VOID *pCondValue, VOS_UINT16 usLen, VOS_BOOL *pbMatch, VOS_UINT8 *pucDir)
{
    VOS_UINT16 usIdx = 0;
    const VOS_UINT8 *pucField = VOS_NULL_PTR;
    const VOS_UINT8 *pucCond = VOS_NULL_PTR;

    pucField = (const VOS_UINT8 *)pFieldValue;
    pucCond = (const VOS_UINT8 *)pCondValue;

    *pbMatch = VOS_TRUE;
    *pucDir = DBTC_SEARCH_ALL;

    while ((*pucCond != 0) && (usIdx < usLen)) {
        if (*pucField != *pucCond) {
            *pbMatch = VOS_FALSE;
            if (*pucField < *pucCond) {
                *pucDir = DBTC_SEARCH_LARGE;
            } else {
                *pucDir = DBTC_SEARCH_SMALL;
            }
            break;
        }

        pucField++;
        pucCond++;
        usIdx++;
    }

    return DB_SUCCESS;
}

/*******************************************************************************
 Function    : TPC_STRING_Prefix21
 Description : Check if field string is same as the condition string prefix.
               Compares field string and condition string for the specified
               length or field string end is reached
 Input       : pFieldValue - Input value of the field to be compared
               pCondValue  - pointer containing value in the condition
               usLen       - length of comparison
 Output      : pbMatch - boolean pointer indicates, values are matching or not
               pucDir  - value indicates further search has to be made or not
 Return      : DB_SUCCESS on success, error codes on failure
*******************************************************************************/
DB_ERR_CODE TPC_STRING_Prefix21(
    const VOS_VOID *pFieldValue, const VOS_VOID *pCondValue, VOS_UINT16 usLen, VOS_BOOL *pbMatch, VOS_UINT8 *pucDir)
{
    VOS_UINT16 usIndex = 0;
    const VOS_UINT8 *pucField = VOS_NULL_PTR;
    const VOS_UINT8 *pucCond = VOS_NULL_PTR;

    pucField = (const VOS_UINT8 *)pFieldValue;
    pucCond = (const VOS_UINT8 *)pCondValue;

    *pbMatch = VOS_TRUE;
    *pucDir = DBTC_SEARCH_ALL;

    while ((*pucField != 0) && (usIndex < usLen)) {
        if (*pucField != *pucCond) {
            *pbMatch = VOS_FALSE;
            if (*pucField < *pucCond) {
                *pucDir = DBTC_SEARCH_LARGE;
            } else {
                *pucDir = DBTC_SEARCH_SMALL;
            }
            break;
        }

        pucField++;
        pucCond++;
        usIndex++;
    }

    return DB_SUCCESS;
}

/*******************************************************************************
 Function    : TPC_STRING_NoPrefix
 Description : Check if Field string prefix is not same as condition string.
 Input       : pFieldValue - Input value of the field to be compared
               pCondValue  - pointer containing value in the condition
               usLen       - length of comparison
 Output      : pbMatch - boolean pointer indicates, values are matching or not
               pucDir  - value indicates further search has to be made or not
 Return      : DB_SUCCESS on success, error codes on failure
*******************************************************************************/
DB_ERR_CODE TPC_STRING_NoPrefix(
    const VOS_VOID *pFieldValue, const VOS_VOID *pCondValue, VOS_UINT16 usLen, VOS_BOOL *pbMatch, VOS_UINT8 *pucDir)
{
    VOS_UINT16 usIndex = 0;
    const VOS_UINT8 *pucField = VOS_NULL_PTR;
    const VOS_UINT8 *pucCond = VOS_NULL_PTR;

    pucField = (const VOS_UINT8 *)pFieldValue;
    pucCond = (const VOS_UINT8 *)pCondValue;

    *pbMatch = VOS_FALSE;
    *pucDir = DBTC_SEARCH_ALL;
    while ((*pucCond != 0) && (usIndex < usLen)) {
        if (*pucField != *pucCond) {
            *pbMatch = VOS_TRUE;
            break;
        }

        pucField++;
        pucCond++;
        usIndex++;
    }

    return DB_SUCCESS;
}

/*******************************************************************************
 Function    : TPC_FindSubStr
 Description : This function will find the sub string in the given string
 Input       : pucField    - Input value of the field to be compared
               usFldLen    - Field Length
               pucSubStr   - Pointer to the Sub-String
               usSubStrLen - Sub string length
               bIsWildCard - Flag to notify if wildcard (%) is encountered in
                             given condition or not
 Output      : pusPosMatch - Position of the sub string
 Return      : VOS_TRUE, VOS_FALSE
*******************************************************************************/
VOS_BOOL TPC_FindSubStr(const VOS_UINT8 *pucField, VOS_UINT16 usFldLen, const VOS_UINT8 *pucSubStr,
    VOS_UINT16 usSubStrLen, VOS_BOOL bIsWildCard, VOS_UINT16 *pusPosMatch)
{
    VOS_UINT16 usLoop;
    VOS_UINT16 usMatchPos = 0;
    const VOS_UINT8 *pucFirst = VOS_NULL_PTR;
    *pusPosMatch = VOS_NULL_WORD;

    if (usFldLen < usSubStrLen) {
        return VOS_FALSE;
    }

    while (pucField[usMatchPos] != '\0') {
        pucFirst = &pucField[usMatchPos];
        for (usLoop = 0; usLoop < usSubStrLen; usLoop++) {
            if ((pucSubStr[usLoop] != '_') && (pucSubStr[usLoop] != pucFirst[usLoop])) {
                /*
                 * If no wildcard (%) in the condition till now, then entire
                 * sub-string should match from beginning. If not, then return FALSE
                 */
                if (!bIsWildCard) {
                    return VOS_FALSE;
                }

                usMatchPos++;
                break;
            }
        }

        /* If the whole sub-string is found in the field value, then return TRUE */
        if (usLoop == usSubStrLen) {
            *pusPosMatch = usMatchPos;
            return VOS_TRUE;
        }
    }

    /* No match is found for the given sub-string */
    return VOS_FALSE;
}

/*******************************************************************************
 Function    : TPC_StrIsLike
 Description : Check if Field string follows the pattern specified by the
               condition string. '_' character allows to match a single
               character and '%' allows any number of characters in a sting.
 Input       : pucField - Input value of the field to be compared
               pucCond  - pointer containing value in the condition
               usMaxLen - Max length of pucField and pucCond
 Output      : None
 Return      : VOS_TRUE, VOS_FALSE
*******************************************************************************/
VOS_BOOL TPC_StrIsLike(const VOS_UINT8 *pucField, const VOS_UINT8 *pucCond, VOS_UINT16 usMaxLen)
{
    VOS_BOOL bIsWildCard = VOS_FALSE;
    VOS_UINT16 usSubStrLen = 0;
    VOS_UINT16 usFieldLen;
    VOS_UINT16 usCondLen;
    VOS_UINT16 usPosFld = 0;
    VOS_UINT16 usPosCond = 0;
    VOS_UINT16 usMatchPos = 0;
    const VOS_UINT8 *pucSubStr = VOS_NULL_PTR;

    usFieldLen = (VOS_UINT16)strnlen((const char *)pucField, usMaxLen);
    usCondLen = (VOS_UINT16)strnlen((const char *)pucCond, usMaxLen);

    while (pucCond[usPosCond] != '\0') {
        /* Skip all the consecutive wildcards in the confdition */
        while (pucCond[usPosCond] == '%') {
            bIsWildCard = VOS_TRUE;
            usPosCond++;

            /* If the remaining condition has only wildcards (%), then return TRUE */
            if (usCondLen == usPosCond) {
                return VOS_TRUE;
            }
        }

        usSubStrLen = 0;
        pucSubStr = &pucCond[usPosCond];

        /* Get the length of the sub-string to be searched */
        while ((pucCond[usPosCond] != '\0') && (pucCond[usPosCond] != '%')) {
            usSubStrLen++;
            usPosCond++;
        }

        /*
         * If the current sub-string is the last one and the last character is not wildcard,
         * then need to match the entire sub-string till the end of the field value
         */
        if ((pucCond[usPosCond] == '\0') && (bIsWildCard)) {
            /*
             * If the length of the remaining field string to traverse is less
             * than the cond sub-string's length, then return FALSE
             */
            if ((usFieldLen - usPosFld) < usSubStrLen) {
                return VOS_FALSE;
            }

            usPosFld = (VOS_UINT16)(usFieldLen - usSubStrLen);
        }

        /*
         * Find the condition sub-string in the field value from given position
         * till the end for any occurrence
         */
        bool temp = !TPC_FindSubStr(
            &pucField[usPosFld], (VOS_UINT16)(usFieldLen - usPosFld), pucSubStr, usSubStrLen, bIsWildCard, &usMatchPos);
        if (temp) {
            return VOS_FALSE;
        } else {
            usPosFld += (usMatchPos + usSubStrLen);
        }
    }

    /* If the field value doesn't end, then the condition doesn't matches */
    if (pucField[usPosFld] != '\0') {
        return VOS_FALSE;
    }

    return VOS_TRUE;
}

/*******************************************************************************
 Function    : TPCIF_STRING_AllocateAndUpdBuf
 Description : Allocates Field and Condition local buffer and update them.
 Input       : pFieldValue - Input value of the field to be compared
               pCondValue  - pointer containing value in the condition
               usMaxLen    - length of comparison
 Output      : ppucField - Field Value buf
               ppucCond  - Condition value buf
 Return      : DB_SUCCESS on success, error codes on failure
*******************************************************************************/
DB_ERR_CODE TPCIF_STRING_AllocateAndUpdBuf(VOS_UINT8 **ppucField, VOS_UINT8 **ppucCond, VOS_UINT16 usMaxLen,
    const VOS_VOID *pFieldValue, const VOS_VOID *pCondValue)
{
    VOS_UINT8 *pucField = VOS_NULL_PTR;
    VOS_UINT8 *pucCond = VOS_NULL_PTR;
    VOS_INT32 lRet;

    /* Allocate the memory for the field value */
    pucField = (VOS_UINT8 *)malloc((usMaxLen + 1));
    if (VOS_NULL_PTR == pucField) {
        return VOS_ERRNO_DB_MEMALLOCFAILURE;
    }

    /* Allocate the memory for the condition value */
    pucCond = (VOS_UINT8 *)malloc((usMaxLen + 1));
    if (VOS_NULL_PTR == pucCond) {
        free(pucField);
        return VOS_ERRNO_DB_MEMALLOCFAILURE;
    }

    /* 1 is added to include '\0' at the end of the string */
    lRet = memset_s(pucField, (usMaxLen + 1), DB_NULL_CHAR, (usMaxLen + 1));
    if (EOK != lRet) {
        free(pucField);
        free(pucCond);
        return VOS_ERRNO_DB_MEMSET_FAILURE;
    }

    lRet = memset_s(pucCond, (usMaxLen + 1), DB_NULL_CHAR, (usMaxLen + 1));
    if (EOK != lRet) {
        free(pucField);
        free(pucCond);
        return VOS_ERRNO_DB_MEMSET_FAILURE;
    }

    /* For the string length more than 256, it should do string copy of maximum length. */
    lRet = strncpy_s((char *)pucField, (usMaxLen + 1), (const char *)pFieldValue, usMaxLen);
    if (EOK != lRet) {
        free(pucField);
        free(pucCond);
        return VOS_ERRNO_DB_STROPER_FAILURE;
    }

    lRet = strncpy_s((char *)pucCond, (usMaxLen + 1), (const char *)pCondValue, usMaxLen);
    if (EOK != lRet) {
        free(pucField);
        free(pucCond);
        return VOS_ERRNO_DB_STROPER_FAILURE;
    }

    *ppucField = pucField;
    *ppucCond = pucCond;
    return DB_SUCCESS;
}

/*******************************************************************************
 Function    : TPC_STRING_Like
 Description : Check if Field string follows the pattern specified by the
               condition string. '_' character allows to match a single
               character and '%' allows any number of characters in a sting.
               The pattern matching is performed upto the specified
               number of characters only
 Input       : pFieldValue - Input value of the field to be compared
               pCondValue  - pointer containing value in the condition
               usLen       - length of comparison
 Output      : pbMatch - boolean pointer indicates, values are matching or not
               pucDir  - value indicates further search has to be made or not
 Return      : DB_SUCCESS on success, error codes on failure
*******************************************************************************/
DB_ERR_CODE TPC_STRING_Like(
    const VOS_VOID *pFieldValue, const VOS_VOID *pCondValue, VOS_UINT16 usLen, VOS_BOOL *pbMatch, VOS_UINT8 *pucDir)
{
    VOS_UINT8 *pucField = VOS_NULL_PTR;
    VOS_UINT8 *pucCond = VOS_NULL_PTR;
    VOS_UINT16 usMaxLen;
    DB_ERR_CODE errCode;

    *pbMatch = VOS_FALSE;

    if (usLen == 0) {
        return VOS_ERRNO_DB_INVALIDDATALEN;
    }

    if (usLen > DB_ELELEN_MAX) {
        usMaxLen = usLen;
    } else {
        usMaxLen = DB_ELELEN_MAX;
    }

    errCode = TPCIF_STRING_AllocateAndUpdBuf(&pucField, &pucCond, usMaxLen, pFieldValue, pCondValue);
    if (DB_SUCCESS != errCode) {
        return errCode;
    }

    *pbMatch = TPC_StrIsLike(pucField, pucCond, usMaxLen);
    *pucDir = DBTC_SEARCH_ALL;

    free(pucField);
    free(pucCond);

    return DB_SUCCESS;
}

/*******************************************************************************
 Function    : TPC_STRING_NotLike
 Description : Check if Field string is not follows the pattern specified by
               the condition string. '_' character allows to match a single
               character and '%' allows any number of characters in a sting.
               The pattern matching is performed upto the specified number
               of characters only
 Input       : pFieldValue - Input value of the field to be compared
               pCondValue  - pointer containing value in the condition
               usLen       - length of comparison
 Output      : pbMatch - boolean pointer indicates, values are matching or not
               pucDir  - value indicates further search has to be made or not
 Return      : DB_SUCCESS on success, error codes on failure
*******************************************************************************/
DB_ERR_CODE TPC_STRING_NotLike(
    const VOS_VOID *pFieldValue, const VOS_VOID *pCondValue, VOS_UINT16 usLen, VOS_BOOL *pbMatch, VOS_UINT8 *pucDir)
{
    VOS_UINT8 *pucField = VOS_NULL_PTR;
    VOS_UINT8 *pucCond = VOS_NULL_PTR;
    VOS_UINT16 usMaxLen;
    DB_ERR_CODE errCode;

    *pbMatch = VOS_FALSE;

    if (usLen == 0) {
        return VOS_ERRNO_DB_INVALIDDATALEN;
    }

    if (usLen > DB_ELELEN_MAX) {
        usMaxLen = usLen;
    } else {
        usMaxLen = DB_ELELEN_MAX;
    }

    errCode = TPCIF_STRING_AllocateAndUpdBuf(&pucField, &pucCond, usMaxLen, pFieldValue, pCondValue);
    if (DB_SUCCESS != errCode) {
        return errCode;
    }

    *pbMatch = TPC_StrIsLike(pucField, pucCond, usMaxLen);
    *pbMatch = !(*pbMatch);
    *pucDir = DBTC_SEARCH_ALL;

    free(pucField);
    free(pucCond);

    return DB_SUCCESS;
}

/***********************************************************************
 Function    : TPCIF_STRING_Postfix21
 Description : PostFix21 validate function for STRING
 Input       : pucField - Input value of the field to be compared
               pucCond  - pointer containing value in the condition
               lFieldlenPara - length of field
 Output      : pbMatch - boolean pointer indicates, values are matching or not
               pucDir  - value indicates further search has to be made or not
 Return      : DB_SUCCESS on success, error codes on failure
*******************************************************************************/
static VOS_VOID TPCIF_STRING_Postfix21(
    const VOS_UINT8 *pucField, const VOS_UINT8 *pucCond, VOS_UINT8 *pucDir, VOS_BOOL *pbMatch, VOS_INT32 lFieldlenPara)
{
    VOS_INT32 lFieldlen = lFieldlenPara;

    while (lFieldlen >= 0) {
        if (*pucField != *pucCond) {
            *pbMatch = VOS_FALSE;
            if (*pucField < *pucCond) {
                *pucDir = DBTC_SEARCH_LARGE;
            } else {
                *pucDir = DBTC_SEARCH_SMALL;
            }
            break;
        }

        pucField--;
        pucCond--;
        lFieldlen--;
    }
}

/***********************************************************************
 Function    : TPC_STRING_Postfix21
 Description : Check for String postfix21
 Input       : pFieldValue - Input value of the field to be compared
               pCondValue  - pointer containing value in the condition
               usLen       - length of comparison
 Output      : pbMatch - boolean pointer indicates, values are matching or not
               pucDir  - value indicates further search has to be made or not
 Return      : DB_SUCCESS on success, error codes on failure
*******************************************************************************/
DB_ERR_CODE TPC_STRING_Postfix21(
    const VOS_VOID *pFieldValue, const VOS_VOID *pCondValue, VOS_UINT16 usLen, VOS_BOOL *pbMatch, VOS_UINT8 *pucDir)
{
    const VOS_UINT8 *pucField = VOS_NULL_PTR;
    const VOS_UINT8 *pucCond = VOS_NULL_PTR;
    VOS_INT32 lFieldlen;
    VOS_INT32 lCondlen;
    pucField = (const VOS_UINT8 *)pFieldValue;
    pucCond = (const VOS_UINT8 *)pCondValue;

    *pbMatch = VOS_TRUE;
    *pucDir = DBTC_SEARCH_ALL;

    /* since input to function is defined length, so to detect error for
     * condition buffer length, need to get max length based on stored length.
     */
    lCondlen = (VOS_INT32)strnlen((const char *)pucCond, usLen + 1);
    /* If Condition length is greater than the defined length, return error */
    if ((VOS_UINT32)lCondlen > usLen) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            VOS_ERRNO_DB_INVALIDDATALEN, "Unable to check condition len when compare postfix21");
        return VOS_ERRNO_DB_INVALIDDATALEN;
    }

    /* If the input condition is null string, pbMatch should be false */
    if (lCondlen == 0) {
        *pbMatch = VOS_FALSE;
        return DB_SUCCESS;
    }

    lFieldlen = (VOS_INT32)strnlen((const char *)pucField, usLen);
    /* If field data is null string, pbMatch should be true and return success */
    if (lFieldlen == 0) {
        return DB_SUCCESS;
    }

    /* here 1 it subtracted to get the position of the last character of the
     * string pCondValue and pFieldValue.
     */
    lCondlen--;
    lFieldlen--;

    pucCond += lCondlen;
    pucField += lFieldlen;

    if (lCondlen < lFieldlen) {
        *pbMatch = VOS_FALSE;
        return DB_SUCCESS;
    }

    TPCIF_STRING_Postfix21(pucField, pucCond, pucDir, pbMatch, lFieldlen);
    return DB_SUCCESS;
}

#ifdef __cplusplus
}
#endif
