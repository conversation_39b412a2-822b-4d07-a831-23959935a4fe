/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: Embedded simplerel prepare func
 * Author:
 * Create:
 */
#include "srv_emb_sim_com.h"
#include "gme_simple_rel_dql.h"

static void LabelCursorFreeHeapTuple(SimRelLabelCursorT *labelCursor)
{
    if (labelCursor->heapTupleBuf.buf != NULL) {
        DbDynMemCtxFree(labelCursor->memCtx, labelCursor->heapTupleBuf.buf);
        labelCursor->heapTupleBuf = (HeapTupleBufT){0};
    }
}

void LabelCursorFreeIdxCtx(IndexCtxT *idxCtx)
{
    if (idxCtx->tupleBuf.buf) {
        TupleBufRelease(&idxCtx->tupleBuf);
    }
    IdxClose(idxCtx);
    IdxRelease(idxCtx);
}

static void LabelCursorFreeAllIdxCtx(SimRelLabelCursorT *labelCursor)
{
    if (labelCursor->idxCtx == NULL) {
        return;
    }

    for (uint32_t i = 0; i < labelCursor->idxNum; i++) {
        IndexCtxT *idxCtx = (IndexCtxT *)labelCursor->idxCtx[i];
        if (idxCtx != NULL && idxCtx->hasOpened == true) {
            LabelCursorFreeIdxCtx(idxCtx);
            labelCursor->idxCtx[i] = NULL;
        }
    }
    DbDynMemCtxFree(labelCursor->memCtx, labelCursor->idxCtx);
    labelCursor->idxCtx = NULL;
    labelCursor->idxNum = 0;
}

inline static void SimpleRelSetInvalidLastOp(SimRelLabelCursorT *labelCursor)
{
    labelCursor->lastOpType = DB_INVALID_UINT8;
    labelCursor->lastOpratedMetaId = DB_INVALID_UINT32;
}

void SimpRelCloseWriteCursor(SimRelLabelCursorT *labelCursor, bool isFree)
{
    LabelCursorFreeAllIdxCtx(labelCursor);
    LabelCursorFreeHeapTuple(labelCursor);

    if (labelCursor->hpHandle != NULL) {
        HeapLabelCloseAndResetCtx(labelCursor->hpHandle);
        if (isFree) {
            HeapLabelReleaseRunctx(labelCursor->hpHandle);
            labelCursor->hpHandle = NULL;
        }
    }
    SimpleRelSetInvalidLastOp(labelCursor);
    labelCursor->vertexLabel = NULL;
}

Status SimpleRelLabelCursorAlloc(EmbSimpleRunCtxT *embCtx)
{
    if (embCtx->labelCursor == NULL) {
        DbMemCtxT *memCtx = embCtx->sessionMemctx;
        SimRelLabelCursorT *labelCursor = DbDynMemCtxAlloc(memCtx, sizeof(SimRelLabelCursorT));
        if (labelCursor == NULL) {
            DB_LOG_ERROR_AND_SET_LASTERR(
                VOS_ERRNO_DB_MEMALLOCFAILURE, "unbable to alloc label cursor when prepare label");
            return VOS_ERRNO_DB_MEMALLOCFAILURE;
        }
        // 申请成功确保长度合法，不校验memset_s的返回值
        (void)memset_s(labelCursor, sizeof(SimRelLabelCursorT), 0x00, sizeof(SimRelLabelCursorT));
        SimpleRelSetInvalidLastOp(labelCursor);
        labelCursor->memCtx = memCtx;
        labelCursor->apiMemCtx = embCtx->apiMemCtx;
        embCtx->labelCursor = labelCursor;
        DbGaListInit(
            &labelCursor->queryCtxForUpdOrDel.hpTupleList, memCtx, SimpleRelationAllocMem, SimpleRelationFreeMem);
    }
    return GMERR_OK;
}

void SimpleRelLabelCursorFree(EmbSimpleRunCtxT *embCtx)
{
    DbDynMemCtxFree(embCtx->sessionMemctx, embCtx->labelCursor);
    embCtx->labelCursor = NULL;
}

void SimpRelCloseReadCursor(SimRelLabelCursorT *labelCursor, bool isFree)
{
    SimpleRelFreeQueryCtx(labelCursor->memCtx, &labelCursor->queryCtx);
    LabelCursorFreeAllIdxCtx(labelCursor);
    // 排序上下文可复用，没有放到SimpleRelFreeQueryCtx中

    if (labelCursor->hpHandle != NULL) {
        HeapLabelCloseAndResetCtx(labelCursor->hpHandle);
        if (isFree) {
            HeapLabelReleaseRunctx(labelCursor->hpHandle);
            labelCursor->hpHandle = NULL;
        }
    }
    SimpleRelSetInvalidLastOp(labelCursor);
    labelCursor->vertexLabel = NULL;
}

Status SimpleRelHeapOpen(EmbSimpleRunCtxT *embCtx, SeRunCtxHdT seRunCtx, DbMemCtxT *memCtx, DmVertexLabelT *vertexLabel,
    HpRunHdlT *hpHandle, HpOpTypeE heapOpType)
{
    Status ret = GMERR_OK;
    // hpHandle复用场景
    if (embCtx != NULL) {
        SimRelLabelCursorT *labelCursor = embCtx->labelCursor;
        bool isSameOp = (heapOpType == HEAP_OPTYPE_DIRECTREAD && labelCursor->lastOpType == SIMPLEREL_READ) ||
                        (heapOpType == HEAP_OPTYPE_NONE_OP && labelCursor->lastOpType == SIMPLEREL_WRITE);
        if (isSameOp && vertexLabel->metaCommon.metaId == labelCursor->lastOpratedMetaId) {
            if (labelCursor->hpHandle != NULL && embCtx->isZeroRDBConn && embCtx->RDBTrxState == RDB_TRX_RUNNING) {
                return GMERR_OK;
            }
            if (labelCursor->hpHandle != NULL) {
                HeapLabelCloseAndResetCtx(embCtx->labelCursor->hpHandle);
            }
        } else {
            if (labelCursor->lastOpType == SIMPLEREL_READ) {
                SimpRelCloseReadCursor(labelCursor, false);
            } else if (labelCursor->lastOpType == SIMPLEREL_WRITE) {
                SimpRelCloseWriteCursor(labelCursor, false);
            }
        }
    }

    if (*hpHandle == NULL) {
        ret = HeapLabelAllocRunctx(seRunCtx, hpHandle);
        // 去掉非空判断会导致流水线检测过不了
        if (ret != GMERR_OK || *hpHandle == NULL) {
            DB_LOG_ERROR_UNFOLD(ret, "Alloc heapCtx worthless when client open vertexLabel.");
            return ret;
        }
    }

    HeapRunCtxAllocCfgT heapRunCtxInitCfg = {.seRunCtx = seRunCtx,
        .dmInfo = vertexLabel,
        .heapShmAddr = vertexLabel->commonInfo->heapInfo.heapShmAddr,
        .isBackGround = false};
    ret = HeapLabelInitRunctx(&heapRunCtxInitCfg, *hpHandle);
    if (ret != GMERR_OK) {
        HeapLabelCloseAndResetCtx(*hpHandle);
        DB_LOG_ERROR_UNFOLD(ret, "Init heap run ctx worthless when open simprel run ctx.");
        return ret;
    }

    SimpRelSetHpHandleComAreaLabelId(*hpHandle, vertexLabel->metaCommon.metaId);
    ret = HeapLabelOpen(*hpHandle, heapOpType, memCtx);
    if (ret != GMERR_OK) {
        HeapLabelCloseAndResetCtx(*hpHandle);
        DB_LOG_ERROR_UNFOLD(ret, "Open heap label worthless when open simprel run ctx.");
        return ret;
    }
    DB_ASSERT((*hpHandle)->perfStat->writeBytes >= (*hpHandle)->perfStat->deleteBytes);
    return GMERR_OK;
}

Status SimpleRelIndicesOpen(SeRunCtxHdT seRunCtx, DmVertexLabelT *vertexLabel, SimRelLabelCursorT *labelCursor)
{
    // 不一定创建索引, 若有索引全部都是二级索引，包含额外索引
    if (vertexLabel->metaVertexLabel->secIndexNum == 0) {
        labelCursor->idxNum = 0;
        return GMERR_OK;
    }

    labelCursor->idxNum = vertexLabel->metaVertexLabel->secIndexNum;
    if (labelCursor->idxCtx == NULL) {
        uint32_t secIdxSize = (uint32_t)(vertexLabel->metaVertexLabel->secIndexNum * sizeof(IndexCtxT *));
        void *secIdxCtxBuf = DbDynMemCtxAlloc(labelCursor->memCtx, secIdxSize);
        if (secIdxCtxBuf == NULL) {
            DB_LOG_ERROR_AND_SET_LASTERR(VOS_ERRNO_DB_MEMALLOCFAILURE, "init queryctx oom");
            return VOS_ERRNO_DB_MEMALLOCFAILURE;
        }
        (void)memset_s(secIdxCtxBuf, secIdxSize, 0x00, secIdxSize);
        labelCursor->idxCtx = (IndexCtxT **)secIdxCtxBuf;
    }
    DmVlIndexLabelT *secIdx = vertexLabel->metaVertexLabel->secIndexes;
    for (uint32_t i = 0; i < labelCursor->idxNum; i++, secIdx++) {
        if (labelCursor->idxCtx[i] == NULL) {
            Status ret = SimpleRelOpenAndInitOneIdx(seRunCtx, labelCursor, secIdx, &labelCursor->idxCtx[i]);
            if (ret != GMERR_OK) {
                LabelCursorFreeIdxCtx(labelCursor->idxCtx[i]);
                labelCursor->idxCtx[i] = NULL;
                return ret;
            }
        }
    }

    return GMERR_OK;
}

Status SimpleRelPrepareDmlCursor(EmbSimpleRunCtxT *embCtx, VlabelCtrlT *vLabelCtrl)
{
    Status ret = GMERR_OK;
    SeRunCtxHdT seRunCtx = embCtx->seRunCtx;
    SimRelLabelCursorT *labelCursor = embCtx->labelCursor;
    DmVertexLabelT *vertexLabel = vLabelCtrl->vertexLabel;
    ret = SimpleRelHeapOpen(
        embCtx, seRunCtx, labelCursor->memCtx, vertexLabel, &labelCursor->hpHandle, HEAP_OPTYPE_NONE_OP);
    if (ret != GMERR_OK) {
        return ret;
    }
    labelCursor->lastOpType = SIMPLEREL_WRITE;
    labelCursor->vertexLabel = vertexLabel;
    labelCursor->lastOpratedMetaId = vertexLabel->metaCommon.metaId;
    labelCursor->isBigObj = !HeapCheckIsNormalRowByBufSize(SimpRelGetVlMaxRecLen(vertexLabel));
    labelCursor->isUseCommonArea = vLabelCtrl->isUseCommonArea;
    return SimpleRelIndicesOpen(seRunCtx, vertexLabel, labelCursor);
}

Status SimpleRelationPrepareIndexScan(SeRunCtxHdT seRunCtx, SimRelLabelCursorT *labelCursor)
{
    DmVertexLabelT *vertexLabel = labelCursor->vertexLabel;
    if (labelCursor->idxCtx == NULL) {
        uint32_t secIdxSize = (uint32_t)(vertexLabel->metaVertexLabel->secIndexNum * sizeof(IndexCtxT *));
        void *secIdxCtxBuf = DbDynMemCtxAlloc(labelCursor->memCtx, secIdxSize);
        if (secIdxCtxBuf == NULL) {
            DB_LOG_ERROR_AND_SET_LASTERR(VOS_ERRNO_DB_MEMALLOCFAILURE, "init queryctx oom");
            return VOS_ERRNO_DB_MEMALLOCFAILURE;
        }
        (void)memset_s(secIdxCtxBuf, secIdxSize, 0x00, secIdxSize);
        labelCursor->idxCtx = (IndexCtxT **)secIdxCtxBuf;
        labelCursor->idxNum = vertexLabel->metaVertexLabel->secIndexNum;
    }

    uint32_t indexId = labelCursor->bestIndexId;
    DmVlIndexLabelT *idxLabel = &labelCursor->vertexLabel->metaVertexLabel->secIndexes[indexId];
    if (labelCursor->idxCtx[indexId] != NULL && labelCursor->idxCtx[indexId]->constructedWhenOpen == true) {
        return GMERR_OK;
    }

    Status ret = SimpleRelOpenAndInitOneIdx(seRunCtx, labelCursor, idxLabel, &labelCursor->idxCtx[indexId]);
    if (ret != GMERR_OK) {
        LabelCursorFreeIdxCtx(labelCursor->idxCtx[indexId]);
        labelCursor->idxCtx[indexId] = NULL;
        return ret;
    }
    return GMERR_OK;
}

Status SimpleRelationPrepareSequenceScan(SeRunCtxHdT seRunCtx, SimRelLabelCursorT *labelCursor)
{
    Status ret = GMERR_OK;
    // heapCursor init
    HeapScanCursorHdlT heapCursor = NULL;
    HeapBeginScanCfgT beginScanCfg = {
        .isDefragmentation = false,
        .beginAddr = {.tupleAddr = BEFORE_FIRST_TUPLE_ADDR, .blockId = BEFORE_FIRST_TUPLE_BLOCK_ID},  // 默认从头开始查
        .maxFetchNum = 1,  // 设置为1，不缓存数据，直接存入resultSet
        .heapDowngradeInfo = HeapLabelDowngradeInfoInit(),
    };

    // fetch select 接口记录上一次fetch的位置
    if (SECUREC_UNLIKELY(labelCursor->queryCtx.isFetchSelect)) {
        beginScanCfg.beginAddr.tupleAddr = labelCursor->queryCtx.resumeAddr->tupleAddr;
        beginScanCfg.beginAddr.blockId = labelCursor->queryCtx.resumeAddr->blockId;
    }

    ret = HeapLabelBeginScanHpTupleBuffer(labelCursor->hpHandle, &beginScanCfg, &heapCursor);
    if (ret != GMERR_OK) {
        return ret;
    }

    labelCursor->heapCursor = heapCursor;
    return GMERR_OK;
}

void SimpleRelResetQueryCtx(
    DbMemCtxT *memCtx, EmbSimpleQueryCtxT *queryCtx, SimpRelScanCtxT *scanCtx, uint32_t extraCondCnt)
{
    // fetchSelect直接释放掉，使用挂在查询handle下面的条件
    if (scanCtx->isFetchSelect) {
        QueryCtxFreeConds(memCtx, queryCtx);
    } else {
        uint32_t curCondNum = queryCtx->filterCondNum + queryCtx->idxCondNum;
        if (scanCtx->cond != NULL && curCondNum >= (scanCtx->cond->usCondNum + extraCondCnt)) {
            queryCtx->filterCondNum = 0;
            queryCtx->idxCondNum = 0;
            queryCtx->idxConds = NULL;
        } else {
            QueryCtxFreeConds(memCtx, queryCtx);
        }
    }

    if (scanCtx->fldFilter != NULL && queryCtx->projectionNum >= scanCtx->fldFilter->ucFieldNum) {
        queryCtx->projectionNum = 0;
    } else {
        QueryCtxFreeProjectionItem(memCtx, queryCtx);
    }
    queryCtx->hpTupleList.usedCount = 0;
    queryCtx->complexOpStru.complexOpNum = 0;
    queryCtx->matchedCnt = NULL;
    queryCtx->isFetchSelect = false;
    queryCtx->needCopyResult = true;
    queryCtx->isZeroFldFilter = false;
    queryCtx->isBigObj = false;
    queryCtx->resumeDataInfo = NULL;
    queryCtx->needAddIdxScanCnt = false;
    queryCtx->lastInvalidAddr = DB_INVALID_UINT64;
    queryCtx->lastFetchAddr = DB_INVALID_UINT64;
    queryCtx->sortItem.matchedCnt = 0;
    queryCtx->sortItem.curSortFiledNum = 0;
    queryCtx->sortItem.sortType = DB_SORTTYPE_ASCEND;
    queryCtx->relIdCondCnt = extraCondCnt;
    (void)memset_s(&queryCtx->scanUserData, sizeof(SimpRelScanUserDataT), 0x00, sizeof(SimpRelScanUserDataT));
}

Status SimpleRelInitQueryCtx(DbMemCtxT *memCtx, VlabelCtrlT *vLabelCtrl, IdxMatchStruT *idxMatchStru,
    SimpRelScanCtxT *scanCtx, EmbSimpleQueryCtxT *queryCtx);

static Status SimpleRelInitScanLabelCursor(
    EmbSimpleRunCtxT *embCtx, VlabelCtrlT *vLabelCtrl, IdxMatchStruT *idxMatchStru, SimpRelScanCtxT *scanCtx)
{
    Status ret = GMERR_OK;
    DmVertexLabelT *vertexLabel = vLabelCtrl->vertexLabel;
    DbMemCtxT *memCtx = embCtx->sessionMemctx;
    SimRelLabelCursorT *labelCursor = embCtx->labelCursor;
    ret = SimpleRelHeapOpen(
        embCtx, embCtx->seRunCtx, memCtx, vertexLabel, &labelCursor->hpHandle, HEAP_OPTYPE_DIRECTREAD);
    if (ret != GMERR_OK) {
        return ret;
    }
    labelCursor->vertexLabel = vertexLabel;
    labelCursor->lastOpratedMetaId = vertexLabel->metaCommon.metaId;
    labelCursor->lastOpType = SIMPLEREL_READ;
    labelCursor->isBigObj = !HeapCheckIsNormalRowByBufSize(SimpRelGetVlMaxRecLen(vertexLabel));
    labelCursor->isUseCommonArea = vLabelCtrl->isUseCommonArea;
    // 共享空间添加表ID作为条件
    uint32_t extraCondNum = SimpRelGetExtraCondNum(idxMatchStru, vLabelCtrl);
    SimpleRelResetQueryCtx(labelCursor->memCtx, &labelCursor->queryCtx, scanCtx, extraCondNum);
    ret = SimpleRelInitQueryCtx(memCtx, vLabelCtrl, idxMatchStru, scanCtx, &labelCursor->queryCtx);
    if (ret != GMERR_OK) {
        SimpleRelFreeQueryCtx(memCtx, &labelCursor->queryCtx);
        return ret;
    }
    labelCursor->queryCtx.scanUserData.labelCursor = labelCursor;
    labelCursor->queryCtx.isBigObj = labelCursor->isBigObj;
    if (SimpRelCanScanByIdx(idxMatchStru, vLabelCtrl)) {
        labelCursor->bestIndexId = idxMatchStru->isMatch ? idxMatchStru->idxId : 0;  // 设置通过索引全表查询
        labelCursor->scanMode = SIMP_REL_INDEX_SCAN;
        ret = SimpleRelationPrepareIndexScan(embCtx->seRunCtx, labelCursor);
    } else {
        labelCursor->scanMode = SIMP_REL_SEQUENCE_SCAN;
        ret = SimpleRelationPrepareSequenceScan(embCtx->seRunCtx, labelCursor);
    }
    return ret;
}

static Status SimpleRelSelectIndexByScanCtx(
    VlabelCtrlT *vLabelCtrl, SimpRelScanCtxT *scanCtx, IdxMatchStruT *idxMatchStru, bool isTpcDb)
{
    if (SECUREC_LIKELY(scanCtx->queryPlan == NULL)) {
        SimpleRelSelectIndex(vLabelCtrl, scanCtx->cond, idxMatchStru, scanCtx->isSimpleSelect, isTpcDb);
        SimpRelSetUseInterIdx(vLabelCtrl, idxMatchStru);
        return GMERR_OK;
    }
    idxMatchStru->labelId = DB_INVALID_UINT32;
    idxMatchStru->idxCondNum = 0;
    const uint8_t idxId = scanCtx->queryPlan->iIndexId;
    if (idxId == DB_INVALID_UINT8) {
        SimpRelSetUseInterIdx(vLabelCtrl, idxMatchStru);
        (void)memset_s(idxMatchStru->idxConds, DB_COND_MAX, DB_INVALID_UINT8, DB_COND_MAX);
        return GMERR_OK;
    }

    uint32_t secIdxNum = vLabelCtrl->vertexLabel->metaVertexLabel->secIndexNum;
    if (idxId < secIdxNum) {
        if (scanCtx->isSimpleSelect &&
            vLabelCtrl->originDefInfo->pstIdxLst[scanCtx->queryPlan->iIndexId].enIndexType == DBDDL_INDEXTYPE_HASH) {
            DB_LOG_ERROR_AND_SET_LASTERR(VOS_ERRNO_DB_NOTSUPPORT, "HASH index scan is not supported by simple select.");
            return VOS_ERRNO_DB_NOTSUPPORT;
        }
    } else {
        DB_LOG_ERROR_AND_SET_LASTERR(VOS_ERRNO_DB_INVALIDINDEX,
            "Index id %" PRIu8 " is larger euqal than the index number of relation %" PRIu32 ".", idxId, secIdxNum);
        return VOS_ERRNO_DB_INVALIDINDEX;
    }

    idxMatchStru->isMatch = true;
    idxMatchStru->idxId = idxId;
    for (uint8_t i = 0; i < DB_COND_MAX; i++) {
        idxMatchStru->idxConds[i] = scanCtx->queryPlan->aucCondRef[i];
        if (idxMatchStru->idxConds[i] != DB_INVALID_UINT8) {
            idxMatchStru->idxCondNum++;
        }
    }
    return GMERR_OK;
}

Status SimpleRelPrepareScanCursor(EmbSimpleRunCtxT *embCtx, VlabelCtrlT *vLabelCtrl)
{
    Status ret = GMERR_OK;
    SimpRelScanCtxT *scanCtx = embCtx->apiCtx;
    EmbSimpleQueryCtxT *queryCtx = &embCtx->labelCursor->queryCtx;
    if (!scanCtx->isFetchSelect) {
        ret = SimpleRelCheckScanCtx(scanCtx, vLabelCtrl, true, embCtx->nspCtrl->isTpcDb);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = SimpleRelSelectIndexByScanCtx(vLabelCtrl, scanCtx, &queryCtx->idxMatchStru, embCtx->nspCtrl->isTpcDb);
        if (ret != GMERR_OK) {
            return ret;
        }
    } else {
        // fetch select 操作在TPC_BeginSelect已经做好了校验和索引匹配
        queryCtx->idxMatchStru.labelId = DB_INVALID_UINT32;
        queryCtx->idxMatchStru.idxCondNum = 0;
        queryCtx->idxMatchStru.isMatch = scanCtx->handleCond->isMatchedIdx;
        queryCtx->idxMatchStru.idxId = scanCtx->handleCond->bestIdxId;
        queryCtx->idxMatchStru.isUseInterIdx = scanCtx->handleCond->isUseInterIdx;
    }

    return SimpleRelInitScanLabelCursor(embCtx, vLabelCtrl, &queryCtx->idxMatchStru, scanCtx);
}

static Status SimpleRelationPrepareLabel(EmbSimpleRunCtxT *embCtx, SimRelPrepareFunc prepareFunc)
{
    if (embCtx->vLabelCtrl == NULL || embCtx->relNo != embCtx->vLabelCtrl->relationId) {
        // find vertexLabel by relNo
        VlabelCtrlT *vLabelCtrl = NULL;
        // 此处尝试懒加载
        Status ret =
            SimpleRelGetVlabelCtrlById(&embCtx->nspCtrl->vLabelCtrlList, embCtx->relNo, NULL, &vLabelCtrl, embCtx);
        if (ret != GMERR_OK) {
            return ret;
        }

        embCtx->vLabelCtrl = vLabelCtrl;
    }

    // 这里需要判断一下是否进行过空间迁移，需重新open label cursor
    if (embCtx->labelCursor != NULL && embCtx->labelCursor->isUseCommonArea != embCtx->vLabelCtrl->isUseCommonArea) {
        if (embCtx->labelCursor->lastOpType == SIMPLEREL_READ) {
            SimpRelCloseReadCursor(embCtx->labelCursor, true);
        } else {
            SimpRelCloseWriteCursor(embCtx->labelCursor, true);
        }
        embCtx->labelCursor = NULL;
    }

    Status ret = SimpleRelLabelCursorAlloc(embCtx);
    if (ret != GMERR_OK) {
        return ret;
    }

    return prepareFunc(embCtx, embCtx->vLabelCtrl);
}

Status SimpleRelationPrepare(EmbSimpleRunCtxT *embCtx)
{
    Status ret = GMERR_OK;

    if (EmbSimRelOpIsDML(embCtx->opCode)) {
        if (embCtx->isZeroRDBFirstDml) {
            ret = GmeSimpleChangeArea(embCtx);
            if (ret != GMERR_OK) {
                DB_LOG_ERROR_AND_SET_LASTERR(ret, "change area.");
                return ret;
            }
        }
        SeTransSetLabelModifiedActive(embCtx->seRunCtx);
        ret = SimpleRelationPrepareLabel(embCtx, SimpleRelPrepareDmlCursor);
    } else if (EmbSimRelOpIsDQL(embCtx->opCode)) {
        ret = SimpleRelationPrepareLabel(embCtx, SimpleRelPrepareScanCursor);
    }
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Prepare label worthless.");
    }
    return ret;
}

Status SimpleRelOpenNewLabelCursor(DbMemCtxT *memCtx, SeRunCtxHdT seRunCtx, VlabelCtrlT *vLabelCtrl,
    SimRelLabelCursorT *labelCursor, HpOpTypeE heapOpType)
{
    DB_POINTER2(vLabelCtrl, labelCursor);
    Status ret = GMERR_OK;
    DmVertexLabelT *vertexLabel = vLabelCtrl->vertexLabel;
    ret = SimpleRelHeapOpen(NULL, seRunCtx, memCtx, vertexLabel, &labelCursor->hpHandle, heapOpType);
    if (ret != GMERR_OK) {
        return ret;
    }
    labelCursor->vertexLabel = vertexLabel;
    labelCursor->isBigObj = !HeapCheckIsNormalRowByBufSize(SimpRelGetVlMaxRecLen(vertexLabel));
    labelCursor->memCtx = memCtx;
    ret = SimpleRelIndicesOpen(seRunCtx, vertexLabel, labelCursor);
    if (ret != GMERR_OK) {
        HeapLabelReleaseRunctx(labelCursor->hpHandle);
        labelCursor->hpHandle = NULL;
    }
    return ret;
}

/* V1 arm32 场景 安全解引用接口  begin */
#ifdef ARM32
typedef union {
    uint64_t u64;
    int64_t i64;
    double d;
    uint32_t u32;
    int32_t i32;
    float f;
    uint16_t u16;
    int16_t i16;
    uint8_t u8;
    int8_t i8;
} V1SafeDerefTypeT;

V1SafeDerefTypeT SimpRelSaveDeref(const void *buf, uint32_t derefLen)
{
    V1SafeDerefTypeT saveObj = {0};
    errno_t err = memcpy_s(&saveObj, sizeof(V1SafeDerefTypeT), buf, derefLen);
    DB_ASSERT(err == EOK);
    return saveObj;
}
#endif

uint8_t SimpRelSaveDerefUint8(void *buf)
{
    DB_POINTER(buf);
#ifndef ARM32
    return *((uint8_t *)buf);
#else
    return SimpRelSaveDeref(buf, sizeof(uint8_t)).u8;
#endif
}

uint16_t SimpRelSaveDerefUint16(void *buf)
{
    DB_POINTER(buf);
#ifndef ARM32
    return *((uint16_t *)buf);
#else
    return SimpRelSaveDeref(buf, sizeof(uint16_t)).u16;
#endif
}

uint32_t SimpRelSaveDerefUint32(void *buf)
{
    DB_POINTER(buf);
#ifndef ARM32
    return *((uint32_t *)buf);
#else
    return SimpRelSaveDeref(buf, sizeof(uint32_t)).u32;
#endif
}

uint64_t SimpRelSaveDerefUint64(void *buf)
{
    DB_POINTER(buf);
#ifndef ARM32
    return *((uint64_t *)buf);
#else
    return SimpRelSaveDeref(buf, sizeof(uint64_t)).u64;
#endif
}

int8_t SimpRelSaveDerefInt8(void *buf)
{
    DB_POINTER(buf);
#ifndef ARM32
    return *((int8_t *)buf);
#else
    return SimpRelSaveDeref(buf, sizeof(int8_t)).i8;
#endif
}

int16_t SimpRelSaveDerefInt16(void *buf)
{
    DB_POINTER(buf);
#ifndef ARM32
    return *((int16_t *)buf);
#else
    return SimpRelSaveDeref(buf, sizeof(int16_t)).i16;
#endif
}

int32_t SimpRelSaveDerefInt32(void *buf)
{
    DB_POINTER(buf);
#ifndef ARM32
    return *((int32_t *)buf);
#else
    return SimpRelSaveDeref(buf, sizeof(int32_t)).i32;
#endif
}

int64_t SimpRelSaveDerefInt64(void *buf)
{
    DB_POINTER(buf);
#ifndef ARM32
    return *((int64_t *)buf);
#else
    return SimpRelSaveDeref(buf, sizeof(int64_t)).i64;
#endif
}

float SimpRelSaveDerefFloat(void *buf)
{
    DB_POINTER(buf);
#ifndef ARM32
    return *((float *)buf);
#else
    return SimpRelSaveDeref(buf, sizeof(float)).f;
#endif
}

double SimpRelSaveDerefDouble(void *buf)
{
    DB_POINTER(buf);
#ifndef ARM32
    return *((double *)buf);
#else
    return SimpRelSaveDeref(buf, sizeof(double)).d;
#endif
}

void SimpRelSaveCopyByte(void *destBuf, uint32_t srcMaxLen, void *srcBuf, uint32_t copyLen)
{
    DB_POINTER2(destBuf, srcBuf);
#ifndef ARM32
    if (copyLen == sizeof(uint16_t)) {
        *(uint16_t *)destBuf = *(uint16_t *)srcBuf;
    } else if (copyLen == sizeof(uint32_t)) {
        *(uint32_t *)destBuf = *(uint32_t *)srcBuf;
    } else if (copyLen == sizeof(uint64_t)) {
        *(uint64_t *)destBuf = *(uint64_t *)srcBuf;
    } else {
        DB_ASSERT(false);
    }
#else
    errno_t err = memcpy_s(destBuf, srcMaxLen, srcBuf, copyLen);
    DB_ASSERT(err == EOK);
#endif
}
/* V1 arm32 场景 安全解引用接口   end  */
