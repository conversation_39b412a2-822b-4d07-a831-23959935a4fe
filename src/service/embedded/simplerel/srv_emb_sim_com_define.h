/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: srv_emb_sim_com_define.h
 * Description: the header file of common struct on embedded service.
 * Author:
 * Create:
 */

#ifndef SRV_EMB_REL_COM_DEFINE_H
#define SRV_EMB_REL_COM_DEFINE_H

#include "adpt_types.h"
#include "srv_simple_rel.h"
#include "srv_emb_sql_com.h"
#include "db_list.h"
#include "db_label_latch_mgr.h"
#include "se_ttree_index_base.h"

#ifdef __cplusplus
extern "C" {
#endif

#define DB_CHECK_REC_EXIST (1)
#define BITMAP_NUM 1024
#define IDX_PER_BITMAP 64
#define SIMPLE_REL_INIT_LOCK_CNT 10
#define SIMPLE_REL_LIST_EXTENT_SIZE 8
#define DB_RESTORE_INDEX_MAX_FIELD_NUM 20
#define RECLIST_EXTEND_LEN 8
#define DB_BYTEHEAD_LEN 2  // DBT_BYTES DBT_VBYTES类型头部长度
#define DB_BITMASK(X) (1L << (X))
#define DB_CRC_16 16
#define DBKNL_BYTE 8
#define DBKNL_SHIFT_ONEBITS 1
#define DB_CRC_POLY_16 0x1021
#define DB_WIDTHMASK(X) (((VOS_UINT32)(((VOS_UINT32)(1L) << ((X)-1)) - 1L) << 1) | 1L)
#define V1_CHECKSUM_DEFAULT_VAL 0
/* Macro to store the last error information */
#define DB_MAX_ERRINFO_LENGTH SIZE_K(1)

extern DBTC_OPERATION_FUNC g_TPC_OpFuncs[DBT_BUTT][DB_OP_BUTT];

typedef enum TagSimpleRelOpType {
    SIMPLEREL_OP_TYPE_NONE,
    // DDL
    SIMPLEREL_OP_TYPE_DDL_BEG,
    SIMPLEREL_OP_TYPE_NAMESPACE_CREATE,
    SIMPLEREL_OP_TYPE_NAMESPACE_DROP,
    SIMPLEREL_OP_TYPE_SET_NAMESPACE_DESC,
    SIMPLEREL_OP_TYPE_IMPORT_DATA,
    SIMPLEREL_OP_TYPE_NAMESPACE_OPEN,  // Note1: 下面所有请求, 都必须在 open之后 才可以执行
    SIMPLEREL_OP_TYPE_NAMESPACE_TRUNCATE,
    SIMPLEREL_OP_TYPE_NAMESPACE_CLOSE,
    SIMPLEREL_OP_TYPE_RESET_BKP_PROGRESS,
    SIMPLEREL_OP_TYPE_RELATION_CREATE,
    SIMPLEREL_OP_TYPE_RELATION_DROP,
    SIMPLEREL_OP_TYPE_EDGE_CREATE,
    SIMPLEREL_OP_TYPE_GET_EDGE,
    SIMPLEREL_OP_TYPE_EDGE_DROP,
    SIMPLEREL_OP_TYPE_MOD_RELATION_NAME_AND_ID,
    SIMPLEREL_OP_TYPE_SET_RELATION_MAX_RECNUM,
    SIMPLEREL_OP_TYPE_TOPO_CREATE,
    SIMPLEREL_OP_TYPE_TOPO_DROP,
    SIMPLEREL_OP_TYPE_COMPRESS_TABLE,
    SIMPLEREL_OP_TYPE_CLONE_TABLE,
    SIMPLEREL_OP_TYPE_DDL_END,
    // DCL
    SIMPLEREL_OP_TYPE_DCL_BEG,
    SIMPLEREL_OP_TYPE_TRX_BEGIN,
    SIMPLEREL_OP_TYPE_TRX_COMMIT,
    SIMPLEREL_OP_TYPE_TRX_ROLLBACK,
    SIMPLEREL_OP_TYPE_DCL_END,
    SIMPLEREL_OP_TYPE_EXPORT_DATA,     // 全局加读锁，nsp加读锁
    SIMPLEREL_OP_TYPE_GET_LAST_ERROR,  // 全局加读锁，nsp加读锁
    SUMPLEREL_OP_TYPE_GET_DB_MEM,      // 全局加读锁，nsp加读锁
    // DML
    SIMPLEREL_OP_TYPE_DML_BEG,
    SIMPLEREL_OP_TYPE_INSERT,
    SIMPLEREL_OP_TYPE_UPDATE,
    SIMPLEREL_OP_TYPE_DELETE,
    SIMPLEREL_OP_TYPE_REPLAY,
    SIMPLEREL_OP_TYPE_DML_END,
    // DQL
    SIMPLEREL_OP_TYPE_DQL_BEG,
    SIMPLEREL_OP_TYPE_SCAN,
    SIMPLEREL_OP_TYPE_GET_DIFF,
    SIMPLEREL_OP_TYPE_REPLAY_GET_DIFF,
    SIMPLEREL_OP_TYPE_DQL_END,
    // META
    SIMPLEREL_OP_TYPE_GET_META_WITH_DBID,  // 获取元数据需要校验Db Id的接口
    SIMPLEREL_OP_TYPE_GET_TBL_METAINFO,
    SIMPLEREL_OP_TYPE_GET_META_WITHOUT_DBID,
    SIMPLEREL_OP_TYPE_BUTT,
} SimpleRelOpE;

static inline bool EmbSimRelOpIsNspDDL(SimpleRelOpE opCode)
{
    if (opCode >= SIMPLEREL_OP_TYPE_NAMESPACE_CREATE && opCode <= SIMPLEREL_OP_TYPE_NAMESPACE_TRUNCATE) {
        return true;
    }
    return false;
}

static inline bool EmbSimRelOpIsDDL(SimpleRelOpE opCode)
{
    if (opCode > SIMPLEREL_OP_TYPE_DDL_BEG && opCode < SIMPLEREL_OP_TYPE_DDL_END) {
        return true;
    }
    return false;
}

static inline bool EmbSimRelOpIsDCL(SimpleRelOpE opCode)
{
    if (opCode > SIMPLEREL_OP_TYPE_DCL_BEG && opCode < SIMPLEREL_OP_TYPE_DCL_END) {
        return true;
    }
    return false;
}

static inline bool EmbSimRelOpIsDML(SimpleRelOpE opCode)
{
    if (opCode > SIMPLEREL_OP_TYPE_DML_BEG && opCode < SIMPLEREL_OP_TYPE_DML_END) {
        return true;
    }
    return false;
}

static inline bool EmbSimRelIsReplayOp(SimpleRelOpE opCode)
{
    return opCode == SIMPLEREL_OP_TYPE_REPLAY || opCode == SIMPLEREL_OP_TYPE_REPLAY_GET_DIFF;
}

static inline bool EmbSimRelOpIsDQL(SimpleRelOpE opCode)
{
    if (opCode > SIMPLEREL_OP_TYPE_DQL_BEG && opCode < SIMPLEREL_OP_TYPE_DQL_END) {
        return true;
    }
    return false;
}

typedef struct EmbeddedSimpleRelationIdMgr {
    uint32_t count;
    uint64_t idBitMap[BITMAP_NUM];  // 管理表Id，DB id bitmap
} TblIdMgrT, DbIdMgrT;

typedef struct EmbeddedSimpleRelationOriginDefInfo {
    VOS_UINT32 ulMaxSize; /* Max record count for the relation     */
    VOS_UINT32 ulNCols;   /* No. of Fields in the relation         */
    VOS_UINT32 ulNIdxs;   /* No. of Indices in the relation        */
    VOS_UINT16 usRecLen;  /* One record length of the relation, exclude extra length(EXTERN_DATA_BUF_LEN) */
    VOS_UINT16 allocSize;
    DB_FIELD_DEF_STRU *pstFldLst; /* Array of ulNFields field definitions  */
    DB_INDEX_DEF_STRU *pstIdxLst; /* Array of ulNIdxs index definitions    */
} OriginDefInfoT;

typedef struct EmbeddedSimpleRelationVertexLabelControlT {
    DbRWSpinLockT rwLock;      // 控制懒加载
    uint32_t directSearchCnt;  // 全表扫描的次数
    uint16_t relationId;       // tableIdMgr中获取的表ID映射值
    uint16_t ref;              // 对labelCtrl的引用计数
    uint16_t checkSum;         // 用于检查数据是否被修改
    bool isTempLabel : 1;      // 标记该表是否为临时表
    bool hasInsert : 1;        // 标记是否写入过
    bool needLazyLoad : 1;     // 标记是否需要懒加载,懒加载的表在操作前才会创建
    uint16_t reserved1 : 13;
    char relationName[DB_REL_NAME_LEN];  // 原始表名
    DmVertexLabelT *vertexLabel;         // 表缓存
    LabelRWLatchT *latch;                // 表锁
    OriginDefInfoT *originDefInfo;       // 存储部分原始建表时信息
    HeapPerfStatT *perfStat;
} VlabelCtrlT;

typedef struct SimpleVLabelCreateCtxT {
    uint32_t sessionDbId;
    uint16_t *relId;
    bool isCreateById;
    bool isTempLabel;
    DB_REL_DEF_STRU *pstRelDef;
    DmVertexLabelT *vertexLabel;
} VLabelCreateCtxT;

typedef struct EmbeddedEdgeRelationInfoT {
    uint32_t labelId;
    uint32_t indexId;
    uint32_t fieldNum;
    uint8_t fields[DB_IDX_FLD_MAX];
} EdgeRelationInfoT;

typedef struct EmbeddedSimpleRelationEdgeControlT {
    bool isPersistence;  // 标记该表是否为临时表
    uint16_t relId[DB_EDGE_REL_NUM];
    char edgeName[DB_REL_NAME_LEN];  // 原始表名
    EdgeRelationInfoT relInfo[DB_EDGE_REL_NUM];
} EdgeCtrlT;

typedef struct BackupStatistics {
    DbLatchT latch;                 // latch for backup statistics
    DB_BKPSTATE_ENUM enState;       // Operation state
    uint16_t totalTblNum;           // Total number of tables to be backed up
    uint16_t finishedTblSchemaNum;  // Number of tables schema that have been backed up
    uint16_t finishedTblDataNum;    // Number of tables data that have been backed up
} BackupStatisticsT;

typedef struct EmbeddedSimpleRelationNamespaceControlT {
    DbLatchT latch;
    volatile uint32_t openCnt;        // open db次数
    uint32_t dbId;                    // 从DbIdMgrT获取的映射DB Id
    uint32_t metaId;                  // Catalog中存储的metaId
    char *nspName;                    // 对应db name
    char *loadPath;                   // DB 持久化路径
    char *dbDesc;                     // db 描述信息
    char *lastError;                  // 错误信息
    uint32_t actDescLen;              // db 描述信息实际长度
    uint32_t maxDescLen;              // db 描述信息可用最大长度
    DbListT vLabelCtrlList;           // VlabelCtrlT，缓存当前db建的所有表
    DbListT edgeCtrlList;             // EdgeCtrlT，缓存当前db建的所有表
    TblIdMgrT tableIdMgr;             // 表id映射管理
    BackupStatisticsT bkpStat;        // 备份状态信息
    DB_CHECKSUM_CFG_STRU *pstChksum;  // 校验和配置信息
    bool isAllocId;                   // 标记是否已经被Open分配Id
    bool isTpcDb;                     // 是否为tpc db
    bool isInBkpProcess;              // 标记是否正在导出
    bool reserve;
} NspCtrlT;

typedef struct EmbeddedSimpleRelationMgrT {
    DbLatchT latch;
    DbMemCtxT *serviceMemCtx;  // v5全局使用内存
    DbMemCtxT *wrapperMemCtx;  // v1适配层使用内存
    DbListT nspCtrlList;       // NspCtrlT，缓存所有建的DB
    DbIdMgrT dbIdMgr;          // db id映射管理
} EmbSimpRelMgrT;

typedef struct EmbeddedSimpleConndItemT EmbSimpleCondItemT;

typedef enum EmbSimpOrderDirection { SIMP_ORDER_ASC = 0, SIMP_ORDER_DESC, SIMP_ORDER_BUTT } EmbSimpOrderDirectionE;

typedef struct EmbSimpSortPair {
    uint8_t *buf;   // 排序buf
    int32_t index;  // 排序下标
} EmbSimpSortPairT;

typedef struct EmbSimpleSortItem {
    DbMemCtxT *memCtx;             // 排序使用memCtx，每次排序会创建，排序完成delete
    uint32_t matchedCnt;           // 排序的数据量
    uint32_t recArrayAllocedNum;   // recArray容量
    EmbSimpSortPairT *recArray;    // 有序array
    uint32_t *fldOffset;           // 该字段在数据中的偏移
    uint32_t *propeMaxLen;         // 该字段的长度
    DB_DATATYPE_ENUM_V1 *v1Type;   // 条件V1类型
    uint32_t curSortFiledNum;      // 当前排序字段数
    uint32_t sortFiledAllocedNum;  // 已申请的排序字段数
    uint8_t sortType;              // 排序类型（只有复杂查询可能有多个排序类型）
    bool isBigObjSort;             // 是否是大对象数据排序
} EmbSimpleSortItemT;

typedef struct EmbSimpleWildCardItem {
    DbMemCtxT *memCtx;                     // 默认值查询使用memCtx，为embCtx->apiMemCtx
    uint32_t wildCardValues[DB_COND_MAX];  // 默认值查询，数组大小为
    int32_t maxWildCardCalc;
    uint8_t maxFindCnt;
    uint8_t totalWildCardCnt;
    DbListT wildCardMatchList;
} EmbSimpleWildCardItemT;

typedef struct EmbeddedSimpleQueryCtxT EmbSimpleQueryCtxT;

typedef struct EmbeddedSimpleScanUserData SimpRelScanUserDataT;
typedef Status (*IdxStoreDataFunc)(
    ScanDataCtxT *scanDataCtx, EmbSimpleQueryCtxT *pstSearchPara, SimpRelScanUserDataT *userData);

typedef struct SimpHpTupleInfo {
    TuplePointer32T addr32;
    HeapTupleBufT tupleBuf;
    void *buf;  // 数据头物理addr，索引中记录addr
    uint32_t ttreePos;
} SimpHpTupleInfoT;

typedef enum TagSimpleRelLockState {
    SIMPLEREL_LOCK_TYPE_NONE,
    SIMPLEREL_LOCK_TYPE_WRITE,
    SIMPLEREL_LOCK_TYPE_READ,
} SimRelLockStateE;
typedef struct EmbeddedSimpleConcurrentControlT {
    SimRelLockStateE instanceLockState;
    SimRelLockStateE nspLockState;
    SimRelLockStateE labelState;
    DbListT labelLock;  // 表锁指针
} EmbSimpCCT;

typedef enum SimpScanMode {
    SIMP_REL_SEQUENCE_SCAN,
    SIMP_REL_INDEX_SCAN,
    SIMP_REL_BUTT_SCAN,
} SimpScanModeE;

struct EmbeddedSimpleScanUserData {
    uint32_t matchedCnt;           // 当前匹配到的总数据量
    uint32_t limitCnt;             // 限制最大匹配数据量
    uint32_t actRecLen;            // 一条数据长度
    uint32_t limitBufLen;          // 查询传入的buf长度
    uint32_t curMatchedCnt;        // 当前结果集匹配数据条数
    uint32_t curBufLen;            // 当前结果集写入数据buf长度
    uint32_t bufOffset;            // 传入的buf偏移
    uint32_t ttreePos;             // ttree索引查询位置
    uint32_t recArrayAllocedNum;   // recArray容量(min max op使用)
    uint8_t **recArray;            // 缓存查询结果指针(min max op使用)
    uint64_t *addrArray;           // 中间结果的逻辑addr(min max op使用)
    void *labelCursor;             // SimRelLabelCursorT
    EmbSimpleQueryCtxT *queryCtx;  // dml使用临时使用query ctx
    DB_BUF_STRU *curBufStru;       // 当前结果集(Select2\SimpleSelect)
    DB_GET_BUF_FUNC pfGetBuf;      // 传入的获取buf回调函数
    void *pGetBufArgs;             // 传入的获取buf回调函数参数
    bool isBufNotEnough;           // 标识查询传入的buf长度是否不足够
    bool scanFinish;               // 标识索引查询结束
    bool isSelectAll;              // 标识是否是获取全部数据
    bool isMatch;                  // 标识当前这条是否match
    bool isUsedUserGetBuf;         // 标识是否使用用户传入的获取buf回调函数
    bool isRecArrayNeedAlloc;      // 标识复杂查询是否是大对象查询(recArray是否申请缓存)
};

typedef struct {
    uint32_t complexOpNum;
    uint32_t fldOffset[DB_COND_MAX];          // 该字段在数据中的偏移
    uint32_t propeDefLen[DB_COND_MAX];        // 该字段的长度
    DB_DATATYPE_ENUM_V1 v1Type[DB_COND_MAX];  // 条件V1类型
    uint8_t sortType[DB_COND_MAX];            // 复杂查询对应需要的排序类型
} EmbSimpRelComplexOpStruT;

typedef struct IdxMatchStru {
    uint32_t idxId;
    uint32_t idxCondNum;
    uint8_t idxConds[DB_COND_MAX];  // 索引上的条件(数组下标为条件id，值为0xff代表非索引条件)
    bool isMatch;
    uint32_t labelId;                  // 记录上次匹配表ID
    uint32_t condNum;                  // 记录上次匹配条件数
    VOS_UINT8 ucFieldId[DB_COND_MAX];  // 记录上次匹配表字段信息
    DB_OPTYPE_ENUM enOp[DB_COND_MAX];  // 记录上次匹配表条件信息
} IdxMatchStruT;

struct EmbeddedSimpleQueryCtxT {
    uint32_t affectRows;                     // udp/del 操作影响条数
    uint8_t filterCondNum;                   // 非索引条件数量
    uint8_t idxCondNum;                      // 索引条件数量
    uint8_t idxResumeCondNum;                // TTree 索引 Fetch select 额外索引条件数量
    uint8_t projectionNum;                   // 查询结果字段投影数量\更新数据设置字段投影数量
    EmbSimpleCondItemT *filterConds;         // 非索引条件，filterConds\idxCond\idxResumeConds是连续Addr
    EmbSimpleCondItemT *idxConds;            // 索引条件
    EmbSimpleCondItemT *idxResumeConds;      // fetch 分批查询额外索引条件
    EmbSimpleSortItemT sortItem;             // 排序使用的上下文
    uint32_t *projectionPropId;              // 字段投影数组
    EmbSimpRelComplexOpStruT complexOpStru;  // 复杂查询的信息（MAX_LESS, MIN_LARGER等）
    uint32_t *matchedCnt;                    // TPC_CountMatchingRecs、TPC_RecordExist接口使用
    IdxMatchStruT idxMatchStru;              // 删除、更新匹配到的索引信息
    DB_BUF_STRU *resultSet;                  // 查询结果集DB_BUF_STRU
    GaListT hpTupleList;                     // SimpHpTupleInfoT  dml操作查询中间结果
    SimpRelScanUserDataT scanUserData;       // 查询中间处理
    IdxStoreDataFunc idxStoreDataFunc;  // ttree 处理不同情况的存储中间数据方法（排序，仅获取数量，dml操作查询等）
    bool isFirstFetch : 1;                 // 是否是第一次fetch查询
    bool isFetchSelect : 1;                // TPC_FetchSelectRec标识
    bool isFetchTopo : 1;                  // 标识是否是topo fetch查询
    bool needCopyResult : 1;               // 标识当前查询接口是否需要存储结果
    bool isBigObj : 1;                     // 标识是否是大对象表
    bool needAddIdxScanCnt : 1;            // 当前操作是否要增加索引扫描次数
    bool isZeroFldFilter : 1;              // 是否是0个字段投影
    bool isCmpOpOpposed : 1;               // fetch原始条件与额外索引条件是否为相反比较符号
    SimpHpTupleInfoT *filterData;          // 唯一性校验去除当前已删除的节点
    HpTupleCombineAddrT *resumeAddr;       // 全表扫描 Fetch Select缓存上一次查询最后一条 heap row info
    HpTupleCombineAddrT oldResumeAddr;     // fetch查询用于失败回滚上一次存储的adr信息
    DataInfoT *resumeDataInfo;             // TTree 索引 Fetch select 缓存的数据信息
    EmbSimpleWildCardItemT *wildCardItem;  // 默认值查询使用结构
    TuplePointer32T lastInvalidAddr;       // 查询过程中过滤不可见的addr
    TuplePointer32T lastFetchAddr;         // 上次fetch的逻辑addr
    void *lastFetchBuf;                    // 上次fetch的缓存buf
    IndexCtxT *idxCtx;                     // 当前索引查询使用的idx上下文
};

#define SIMPLEREL_READ 0
#define SIMPLEREL_WRITE 1
typedef struct SimpleRelLabelCursorT {
    DbMemCtxT *apiMemCtx;           // 当前执行接口结束需要释放的内存资源，从此memCtx申请
    DbMemCtxT *memCtx;              // 下面需要申请资源, 均从该memCtx申请
    DmVertexLabelT *vertexLabel;    // 当前操作的表
    HpRunHdlT hpHandle;             // heap运行山下文
    uint32_t idxNum;                // 索引数量
    IndexCtxT **idxCtx;             // 插入数据的索引句柄数组
    HpTupleAddr addr;               // 数据插入addr
    HeapTupleBufT heapTupleBuf;     // 插入数据的heap buf
    SimpScanModeE scanMode;         // 查询类型（索引 || 非索引查询）
    uint32_t bestIndexId;           // 匹配到的最佳索引
    HeapScanCursorHdlT heapCursor;  // heap全表扫描使用上下文
    EmbSimpleQueryCtxT queryCtx;    // 查询上下文（条件、排序、字段投影等）
    EmbSimpleQueryCtxT queryCtxForCheckUnique;  // 唯一性校验复用上下文
    EmbSimpleQueryCtxT queryCtxForUpdOrDel;     // 唯一性校验复用上下文
    uint32_t lastOpratedMetaId;                 // 上一次操作的表ID
    uint8_t lastOpType;                         // 上一次操作的类型 读0写1
    bool isBigObj;                              // 标识是否是大对象表
} SimRelLabelCursorT;

typedef enum SimpleRelDiffState {
    DIFF_STATE_INVALID = 0,
    DIFF_STATE_CREAT,
    DIFF_STATE_CREAT_UPDATE,
    DIFF_STATE_UPDATE,
    DIFF_STATE_UPDATE_UPDATE,
    DIFF_STATE_DELETE,
    DIFF_STATE_UPDATE_DELETE,
    DIFF_STATE_BUTT
} SimpleRelDiffStateE;

typedef enum SimpleRelDiffOp {
    DIFF_OPERATION_CREAT = 0,
    DIFF_OPERATION_UPDATE,
    DIFF_OPERATION_DELETE,
    DIFF_OPERATION_ROLLBACK,
    DIFF_OPERATION_BUTT
} SimpleRelDiffOpE;

typedef struct SimpleRelDiffListItem {
    uint64_t addr;              // 记录唯一标识（存储位置）
    uint32_t savePointSeq;      // 最后一次编辑的savepoint序号
    SimpleRelDiffStateE state;  // 记录轨迹的状态
} SimpleRelDiffListItemT;

typedef struct SimpleRelDiffList {
    uint32_t labelId;  // 表ID
    GaListT diffList;  // 该表的记录轨迹链表
    DbOamapT diffMap;
} SimpleRelDiffListT;

typedef struct SimpleRelDiffCtx {
    DbMemCtxT *memCtx;  // 下面需要申请资源, 均从该memCtx申请
    uint32_t savepointSeq;  // 当前操作的savepoint序号，每次开启savepoint加1，用于diff回滚确定该次编辑释放回滚
    DbOamapT diffMap;   // diff轨迹查找级，用户快速查找记录是否已经被编辑过
    DbListT relNoList;  // diff记录过的表链表，用于强制提交获取cdb编辑过的表
    GaListT labelList;  // diff轨迹链表，记录每张表的diff轨迹
} SimpleRelDiffCtxT;

// relNoList 节点状态
typedef enum SimpRelNoListNodeState {
    SIMP_REL_NOLIST_STATE_READ = 0,
    SIMP_REL_NOLIST_STATE_WRITE = 1,
    SIMP_REL_NOLIST_STATE_BUTT
} SimpRelNoListNodeStateE;

typedef struct SimpRelRelNoListNode {
    uint16_t relNo;                     // relation Id
    SimpRelNoListNodeStateE nodeState;  // 当前节点状态
} SimpRelRelNoListNodeT;

typedef struct SimpleRelImExCtxOption {
    DB_FILE_FORMAT_TYPE endianType;       // 指定大小端类型
    DB_GetTblConvHook pfnGetTblConvHook;  // 大小端转换钩子函数
    bool isCheckSum;                      // 是否需要校验和
} SimpleRelImExCtxOptionT;                // 导入导出使用,记录导入导出配置参数

typedef struct EmbeddedSimpleRunCtxT {
    DbInstanceHdT dbInstance;         // DbInstanceT
    EmbSimpRelMgrT *simpleRelMgr;     // v1管理结构
    struct TagEmbSession *session;    // 会话连接
    SeRunCtxHdT seRunCtx;             // se 运行上下文
    void *sessionMemctx;              // 连接生命周期的memCtx
    void *apiMemCtx;                  // Gme* 接口生命周期的memCtx
    void *apiCtx;                     // 执行上下文(接口入参)
    EmbSimpCCT concurentCtrl;         // 并发控制结构
    SimpleRelOpE opCode;              // 操作码
    uint32_t nspId;                   // NspCtrlT中的dbId
    uint32_t relNo;                   // 表id
    NspCtrlT *nspCtrl;                // 当前操作db的nspCtrl
    VlabelCtrlT *vLabelCtrl;          // 当前操作表的vLabelCtrl
    GmcSeriT *seri;                   // 插入数据序列化方法
    SimRelLabelCursorT *labelCursor;  // 内部dml、dql运行上下文
    SimpleRelDiffCtxT *diffCtx;       // diff轨迹记录运行上下文
    uint8_t RDBTrxState;              // 开始操作前0号连接RDB状态
    bool isZeroRDBConn;               // RDB0号连接标记
    bool isRestoreDb;
    bool isForceCommit;
    bool isCreateSavePonit;
    bool isRestoreDelete;                // 标记nspCtrl是否在放锁期间被释放
    bool isQuickExit;                    // dml操作快速返回标记
    bool isTpcOp;                        // 是否为tpc 操作
    bool isTwoStage;                     // 是否是两阶段（操作，DB）
    SimpleRelImExCtxOptionT imExOption;  // 导入导出选项
    uint64_t writeBytes;                 // savepoint info
    uint64_t deleteBytes;                // savepoint info
} EmbSimpleRunCtxT;

typedef struct SimpRelSelectIdxStru {
    uint8_t condNum;              // 索引条件数量
    uint8_t condNo[DB_COND_MAX];  // 记录索引上的条件id，值为条件id，下标无意义
} SimpRelSelectIdxStruT;

typedef struct SimpTupleBufArray {
    uint32_t insertNum;         /* Number of insert tuples */
    HeapTupleBufT *newTuples;   /* New tuple array */
    HpBatchOutT *batchOut;      /* Tuple address and page head ptr array */
    HpTupleAddr *newTupleAddrs; /* new tuples address */
} SimpTupleBufArrayT;

// 传入buffer与len返回增量计算的checkSum值
inline static void SimpRelCalcCrc16Bit(uint8_t *buf, uint32_t len, uint16_t *checkSum)
{
    DB_POINTER2(buf, checkSum);
    uint16_t *crcTable = g_adptV1Instance.crcTbl16;
    uint16_t currReg = *checkSum;
    for (uint32_t i = 0; i < len; ++i) {
        currReg = (currReg << DBKNL_BYTE) ^ crcTable[(currReg >> DBKNL_BYTE) ^ buf[i]];
    }
    *checkSum = currReg;
}

typedef struct SimpSortIndexDetail {
    uint8_t indexId;
    DB_SORT_INDEX_INFO *pstSortIndexDetail;
} SimpSortIndexDetailT;

#ifdef __cplusplus
}
#endif

#endif /* SRV_EMB_REL_COM_DEFINE_H */
