/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * File Name: gme_simple_rel_id_mgr.c
 * Description: the source file of tbl \ db id idMgr.
 * Author:
 * Create:
 */

#include "gme_simple_rel_ddl.h"

#define BYTE_SIZE_EACH_IDX (8)

Status SimpRelInitIdMgr(DbMemCtxT *memCtx, uint32_t size, uint32_t maxId, SimpRelIdMgrT *idMgr)
{
    idMgr->bitmap = (uint8_t *)DbDynMemCtxAlloc(memCtx, size);
    if (!idMgr->bitmap) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            VOS_ERRNO_DB_MEMALLOCFAILURE, "Alloc id mgr bitmap unsucc, size %" PRIu32 ".", size);
        return VOS_ERRNO_DB_MEMALLOCFAILURE;
    }
    (void)memset_s(idMgr->bitmap, size, 0x00, size);

    idMgr->nextFree = 0;
    idMgr->count = 0;
    idMgr->maxId = maxId;  // [0, maxId)
    idMgr->size = size;
    return GMERR_OK;
}

void SimpRelDestoryIdMgr(DbMemCtxT *memCtx, SimpRelIdMgrT *idMgr)
{
    if (idMgr->bitmap) {
        DbDynMemCtxFree(memCtx, idMgr->bitmap);
        idMgr->bitmap = NULL;
    }
}

uint16_t SimpRelAllocId(SimpRelIdMgrT *idMgr)
{
    if (idMgr->count >= idMgr->maxId) {
        DB_ASSERT(false);  // 内部使用id，不会走到此分支
        return DB_INVALID_UINT16;
    }

    uint16_t id = idMgr->nextFree;
    for (uint16_t i = 0; i <= idMgr->maxId; i++) {
        uint16_t idx = (id + i) % (idMgr->maxId + 1);
        uint16_t byteIdx = idx / BYTE_SIZE_EACH_IDX;
        uint8_t bitIdx = idx % BYTE_SIZE_EACH_IDX;
        if (!(idMgr->bitmap[byteIdx] & (1 << bitIdx))) {
            idMgr->bitmap[byteIdx] |= (1 << bitIdx);
            idMgr->count++;
            idMgr->nextFree = (idx + 1) % (idMgr->maxId + 1);
            return idx;
        }
    }
    DB_ASSERT(false);  // 内部使用id，不会走到此分支
    return DB_INVALID_UINT16;
}

Status SimpRelRegisterId(SimpRelIdMgrT *idMgr, uint16_t regId)
{
    if (regId >= idMgr->maxId) {
        DB_LOG_ERROR_AND_SET_LASTERR(VOS_ERRNO_DB_RELDESC_ALLOCFAILURE, "Id %" PRIu16 " nonvalid.", regId);
        return VOS_ERRNO_DB_RELDESC_ALLOCFAILURE;
    }

    uint16_t byteIdx = regId / BYTE_SIZE_EACH_IDX;
    uint8_t bitIdx = regId % BYTE_SIZE_EACH_IDX;

    if (idMgr->bitmap[byteIdx] & (1 << bitIdx)) {
        DB_LOG_ERROR_AND_SET_LASTERR(VOS_ERRNO_DB_RELDESC_ALLOCFAILURE, "Id %" PRIu16 " has benn used.", regId);
        return VOS_ERRNO_DB_RELDESC_ALLOCFAILURE;
    }

    idMgr->bitmap[byteIdx] |= (1 << bitIdx);
    idMgr->count++;
    idMgr->nextFree = (regId + 1) % (idMgr->maxId + 1);
    return GMERR_OK;
}

void SimpRelFreeId(SimpRelIdMgrT *idMgr, uint16_t id)
{
    if (id >= idMgr->maxId) {
        return;
    }

    uint16_t byteIdx = id / BYTE_SIZE_EACH_IDX;
    uint8_t bitIdx = id % BYTE_SIZE_EACH_IDX;
    if (idMgr->bitmap[byteIdx] & (1 << bitIdx)) {
        idMgr->bitmap[byteIdx] &= ~(1 << bitIdx);
        idMgr->count--;
        idMgr->nextFree = id;
    }
}

bool SimpRelIsIdAllocated(SimpRelIdMgrT *idMgr, uint16_t id)
{
    if (id >= idMgr->maxId) {
        return false;
    }

    uint16_t byteIdx = id / BYTE_SIZE_EACH_IDX;
    uint8_t bitIdx = id % BYTE_SIZE_EACH_IDX;
    return (idMgr->bitmap[byteIdx] & (1 << bitIdx)) != 0;
}
