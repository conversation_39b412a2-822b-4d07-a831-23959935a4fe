/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: Embedded simplerel common impl
 * Author:
 * Create:
 */
#include "gme_simple_rel_persistence.h"
#include "se_ttree_index.h"
#include "se_heap_inner.h"
#include "se_daemon.h"

#define DB_COMAREA_UUID_BASE (0x5F5E100)
#define DB_CHANGE_AREA_MAX_TIME 200

static Status SimpRelInitCommonHeapInfo(DbInstanceHdT dbInstance, HeapAccessCfgT *heapCfg, bool isTpcDb)
{
    heapCfg->pageType = HEAP_VAR_LEN_ROW_PAGE;  // 普通变长页存储
    heapCfg->tupleType = HEAP_TUPLE_TYPE_VERTEX;
    heapCfg->seInstanceId = DbGetInstanceId(dbInstance);
    heapCfg->maxItemNum = DB_MAX_UINT64;
    heapCfg->ccType = CONCURRENCY_CONTROL_LABEL_LATCH;  // 大表锁模式
    heapCfg->isolation = g_adptV1Instance.isolationLevel[isTpcDb];
    heapCfg->trxType = OPTIMISTIC_TRX;
    heapCfg->isUseRsm = false;
    heapCfg->tableSpaceId = 0;
    heapCfg->isYangBigStore = false;
    heapCfg->isStatusMergeSubs = false;
    heapCfg->isPersistent = false;
    heapCfg->isLabelLockSerializable = true;
    uint32_t uuid;
    Status ret = CataGenerateUuid(dbInstance, &uuid);
    if (ret != GMERR_OK) {
        return ret;
    }
    heapCfg->labelId = uuid + DB_COMAREA_UUID_BASE;
    return CataGetTspMgrIdxById(heapCfg->tableSpaceId, &heapCfg->fsmTableSpaceIndex, dbInstance);
}

Status SimpRelCreateCommArea(SeRunCtxHdT seRunCtx, NspCtrlT *nspCtrl)
{
    DB_POINTER2(seRunCtx, nspCtrl);
    if (nspCtrl->isInitCommonArea) {
        return GMERR_OK;
    }

    HeapAccessCfgT heapCfg = {0};
    DbInstanceT *instance = DbGetInstanceByMemCtx(seRunCtx->sessionMemCtx);
    Status ret = SimpRelInitCommonHeapInfo(instance, &heapCfg, nspCtrl->isTpcDb);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "DB %s create common area init heap cfg.", nspCtrl->nspName);
        return ret;
    }

    ShmemPtrT heapShmAddr;
    ret = HeapLabelCreate(seRunCtx, &heapCfg, &heapShmAddr);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "DB %s create common area.", nspCtrl->nspName);
        return ret;
    }
    nspCtrl->comAreaPtr = heapShmAddr;
    nspCtrl->isInitCommonArea = true;

    DbMemCtxT *srvMemCtx = ((EmbSimpRelMgrT *)instance->simpleRelMgr)->serviceMemCtx;
    DbLatchT *comAreaLatch = DbDynMemCtxAlloc(srvMemCtx, sizeof(DbLatchT));
    if (comAreaLatch == NULL) {
        (void)SimpRelCreateCommArea(seRunCtx, nspCtrl);
        DB_LOG_ERROR_AND_SET_LASTERR(
            VOS_ERRNO_DB_MEMALLOCFAILURE, "Alloc common area latch size %" PRIu32 "", (uint32_t)sizeof(DbLatchT));
        return VOS_ERRNO_DB_MEMALLOCFAILURE;
    }
    (void)memset_s(comAreaLatch, sizeof(DbLatchT), 0x00, sizeof(DbLatchT));
    nspCtrl->comAreaLatch = comAreaLatch;
    return GMERR_OK;
}

Status SimpRelDropCommArea(SeRunCtxHdT seRunCtx, NspCtrlT *nspCtrl)
{
    if (!nspCtrl->isInitCommonArea) {
        return GMERR_OK;
    }

    HeapCntrAcsInfoT heapCntrAcsInfo = {.heapShmAddr = nspCtrl->comAreaPtr,
        .isPersistent = false,
        .isUseRsm = false,
        .instanceId = seRunCtx->instanceId};
    Status ret = HeapLabelDrop(seRunCtx, &heapCntrAcsInfo, NULL);
    if (ret != GMERR_OK) {
        return ret;
    }
    nspCtrl->isInitCommonArea = false;
    nspCtrl->comAreaPtr = DB_INVALID_SHMPTR;
    return GMERR_OK;
}

inline static Status SimpRelInitRelCommonArea(EmbSimpleRunCtxT *embCtx, VlabelCtrlT *vLabelCtrl)
{
    Status ret = SimpRelCreateCommArea(embCtx->seRunCtx, embCtx->nspCtrl);
    if (ret != GMERR_OK) {
        return ret;
    }

    SimpRelSetCommonAreaPtr(embCtx->nspCtrl, vLabelCtrl->vertexLabel, true);
    return GMERR_OK;
}

static Status SimpRelPrepareTupleBufArr(
    DbMemCtxT *memCtx, SimpTupleBufArrayT *tupleBufArr, uint32_t insertCnt, uint32_t comAreaRecLen)
{
    uint32_t allocSize = (uint32_t)(insertCnt * (sizeof(HeapTupleBufT) + sizeof(HpBatchOutT) + sizeof(HpTupleAddr)));
    uint8_t *struBuf = DbDynMemCtxAlloc(memCtx, allocSize);
    if (struBuf == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(VOS_ERRNO_DB_MEMALLOCFAILURE, "Unable to alloc tupleBufArr buf.");
        return VOS_ERRNO_DB_MEMALLOCFAILURE;
    }

    uint8_t *bufTmp = struBuf;
    tupleBufArr->newTuples = (HeapTupleBufT *)bufTmp;
    bufTmp += insertCnt * sizeof(HeapTupleBufT);
    tupleBufArr->batchOut = (HpBatchOutT *)bufTmp;
    bufTmp += insertCnt * sizeof(HpBatchOutT);
    tupleBufArr->newTupleAddrs = (HpTupleAddr *)bufTmp;

    uint8_t *allRecBuffer = DbDynMemCtxAlloc(memCtx, comAreaRecLen * insertCnt);
    if (allRecBuffer == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(VOS_ERRNO_DB_MEMALLOCFAILURE, "Unable to alloc allRecBuffer.");
        DbDynMemCtxFree(memCtx, struBuf);
        return VOS_ERRNO_DB_MEMALLOCFAILURE;
    }

    // comAreaRecLen : record(act RecLen) | relation id (uint16_t)
    for (uint32_t i = 0; i < insertCnt; i++) {
        tupleBufArr->newTuples[i].buf = allRecBuffer + (i * comAreaRecLen);
        tupleBufArr->newTuples[i].bufSize = comAreaRecLen;
    }
    tupleBufArr->insertNum = insertCnt;
    return GMERR_OK;
}

static void SimpleRelFreeTupleBufArr(DbMemCtxT *memCtx, SimpTupleBufArrayT *tupleBufArr)
{
    // free all rec buf
    DbDynMemCtxFree(memCtx, tupleBufArr->newTuples[0].buf);
    // free stru
    DbDynMemCtxFree(memCtx, tupleBufArr->newTuples);
}

static Status SimpRelSetAllRecBuf4CommonArea(
    ImportDataCtxT *ctx, VlabelCtrlT *vLabelCtrl, uint32_t recLen, SimpTupleBufArrayT *tupleBufArr)
{
    for (uint32_t i = 0; i < tupleBufArr->insertNum; i++) {
        uint8_t *fileRecBuf = DbImportDataFromBatch(&ctx->importBuf, recLen, ctx->isNeedTransEndian);
        if (SECUREC_UNLIKELY(fileRecBuf == NULL)) {
            DB_LOG_ERROR_AND_SET_LASTERR(
                GMERR_DATA_EXCEPTION, "Read vertex buf. Vertex idx %" PRIu32 ", Buf len %" PRIu32 ".", i, recLen);
            return GMERR_DATA_EXCEPTION;
        }

        SimpleRelImportTransDataBufEndian(ctx, vLabelCtrl, fileRecBuf);
        // v5 fileRecBuf   : record(recLen)
        // common area rec : record(recLen) | seMetaId (2)
        (void)memcpy_s(tupleBufArr->newTuples[i].buf, recLen, fileRecBuf, recLen);
        *(uint16_t *)(tupleBufArr->newTuples[i].buf + recLen) = vLabelCtrl->seMetaId;
    }
    return GMERR_OK;
}

static Status SimpRelCommonAreaInsertAllIdx(EmbSimpleRunCtxT *embCtx, SimpTupleBufArrayT *tupleBufArr)
{
    Status ret = GMERR_OK;
    IndexKeyT hashKey = {0};
    bool checkAged = true;
    for (uint32_t i = 0; i < embCtx->labelCursor->idxNum; i++) {
        IndexCtxT *secIdxCtx = embCtx->labelCursor->idxCtx[i];
        secIdxCtx->idxSeType = TTREE_VAR_ROW;  // 共享空间修改为var row
        ((TTreeHeadT *)secIdxCtx->idxRunCtx)->idxSeType = TTREE_VAR_ROW;
        secIdxCtx->destNode = NULL_NODEID;
        secIdxCtx->idxOpenCfg.userData = &checkAged;
        for (uint32_t idx = 0; idx < tupleBufArr->insertNum; idx++) {
            secIdxCtx->ttreeDataInfo.buf = tupleBufArr->batchOut[idx].realBuf;
            secIdxCtx->ttreeDataInfo.addr64 = tupleBufArr->batchOut[idx].addrOut;
            secIdxCtx->ttreeDataInfo.masterAddr64 = tupleBufArr->batchOut[idx].addrOut;
            ret = IdxInsert(secIdxCtx, hashKey, embCtx->labelCursor->addr);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
        secIdxCtx->idxOpenCfg.userData = NULL;
    }
    return GMERR_OK;
}

// SimpRelAddRealItemNum
inline static void SimpRelCommonAreaAddRealItemNum(HpRunHdlT hpHandle, uint32_t vertexCount, uint32_t recLen)
{
    hpHandle->perfStat->realItemNum += vertexCount;
    hpHandle->perfStat->writeBytes += vertexCount * (recLen + DB_V1_TBLID_LEN);
}

Status SimpRelInsertCommonAreaInner(
    EmbSimpleRunCtxT *embCtx, ImportDataCtxT *ctx, uint32_t vertexCount, uint32_t recLen, VlabelCtrlT *vLabelCtrl)
{
    Status ret = GMERR_OK;
    SimpTupleBufArrayT tupleBufArr = {0};
    ret = SimpRelPrepareTupleBufArr(embCtx->apiMemCtx, &tupleBufArr, vertexCount, recLen + DB_V1_TBLID_LEN);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = SimpRelSetAllRecBuf4CommonArea(ctx, vLabelCtrl, recLen, &tupleBufArr);
    if (ret != GMERR_OK) {
        goto EXIT;
    }

    HpRunHdlT hpHandle = embCtx->labelCursor->hpHandle;
    ret = HeapLabelInsertTupleBatch(hpHandle, tupleBufArr.insertNum, tupleBufArr.newTuples, tupleBufArr.batchOut);
    if (ret != GMERR_OK) {
        goto EXIT;
    }

    ret = SimpRelCommonAreaInsertAllIdx(embCtx, &tupleBufArr);
    if (ret != GMERR_OK) {
        goto EXIT;
    }
    SimpRelCommonAreaAddRealItemNum(hpHandle, vertexCount, recLen);

    SimpleRelFreeTupleBufArr(embCtx->apiMemCtx, &tupleBufArr);
    return SimpRelSkipIndexBuf(&ctx->importBuf, vLabelCtrl, ctx->isNeedTransEndian);

EXIT:
    SimpleRelFreeTupleBufArr(embCtx->apiMemCtx, &tupleBufArr);
    return ret;
}

static Status SimpRelInsertComAreaPrepare(EmbSimpleRunCtxT *embCtx, VlabelCtrlT *vLabelCtrl)
{
    Status ret = SimpRelInitRelCommonArea(embCtx, vLabelCtrl);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = SimpleRelPrepareDmlCursor(embCtx, vLabelCtrl);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = HeapLabelResetOpType(embCtx->labelCursor->hpHandle, HEAP_OPTYPE_INSERT, false);
    if (ret != GMERR_OK) {
        SimpRelCloseWriteCursor(embCtx->labelCursor, false);
        DB_LOG_ERROR(ret, "Unable to reset opType when simplerel batch insert.");
        return ret;
    }

    return GMERR_OK;
}

Status SimpRelInsertAllRec2CommonArea(
    EmbSimpleRunCtxT *embCtx, ImportDataCtxT *ctx, uint32_t vertexCount, uint32_t recLen, VlabelCtrlT *vLabelCtrl)
{
    Status ret = SimpRelInsertComAreaPrepare(embCtx, vLabelCtrl);
    if (ret != GMERR_OK) {
        return ret;
    }

    SeTransSetDmlHint4BatchExtendNum(embCtx->seRunCtx, vertexCount);
    ret = SimpRelInsertCommonAreaInner(embCtx, ctx, vertexCount, recLen, vLabelCtrl);
    SimpRelCloseWriteCursor(embCtx->labelCursor, false);
    if (ret != GMERR_OK) {
        return ret;
    }

    vLabelCtrl->hasInsert = true;
    vLabelCtrl->isUseCommonArea = true;
    vLabelCtrl->vertexLabel->commonInfo->isUseComArea = true;
    vLabelCtrl->vertexLabel->comAreaLatch = embCtx->nspCtrl->comAreaLatch;
    vLabelCtrl->realItemCnt = vertexCount;
    return GMERR_OK;
}

static Status SimpRelSetAllRecBuf4CommonArea4v1(ImportDataCtxT *ctx, ImportV1RecordInfoT *recordInfo,
    VlabelCtrlT *vLabelCtrl, uint32_t recLen, SimpTupleBufArrayT *tupleBufArr)
{
    for (uint32_t i = 0; i < recordInfo->recordNum; i++) {
        uint8_t *v1RecBuf = (recordInfo->recordBuffer + (i * recordInfo->recordSize));
        if (SECUREC_UNLIKELY(v1RecBuf == NULL)) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION,
                "Read v1 rec buf. Vertex idx %" PRIu32 ", Buf len %" PRIu32 ".", i, recordInfo->recordSize);
            return GMERR_DATA_EXCEPTION;
        }

        // recLen          : recordInfo->recordSize
        // v1 RecBuf       : record(recordInfo->recordSize)
        // common area rec : record(recordInfo->recordSize) | se meta id (2)
        (void)memcpy_s(tupleBufArr->newTuples[i].buf, recordInfo->recordSize, v1RecBuf, recordInfo->recordSize);
        *(uint16_t *)(tupleBufArr->newTuples[i].buf + recLen) = vLabelCtrl->seMetaId;
        // 转化大小端
        SimpleRelImportTransDataBufEndian(ctx, vLabelCtrl, tupleBufArr->newTuples[i].buf);
    }
    return GMERR_OK;
}

static Status SimpRelInsertCommonAreaInner4v1(
    EmbSimpleRunCtxT *embCtx, ImportDataCtxT *ctx, ImportV1RecordInfoT *recordInfo, VlabelCtrlT *vLabelCtrl)
{
    Status ret = GMERR_OK;
    HpRunHdlT hpHandle = embCtx->labelCursor->hpHandle;
    SimpTupleBufArrayT tupleBufArr = {0};
    uint32_t recLen = recordInfo->recordSize;
    ret = SimpRelPrepareTupleBufArr(embCtx->apiMemCtx, &tupleBufArr, recordInfo->recordNum, recLen + DB_V1_TBLID_LEN);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = SimpRelSetAllRecBuf4CommonArea4v1(ctx, recordInfo, vLabelCtrl, recLen, &tupleBufArr);
    if (ret != GMERR_OK) {
        goto EXIT;
    }

    ret = HeapLabelInsertTupleBatch(hpHandle, tupleBufArr.insertNum, tupleBufArr.newTuples, tupleBufArr.batchOut);
    if (ret != GMERR_OK) {
        goto EXIT;
    }

    ret = SimpRelCommonAreaInsertAllIdx(embCtx, &tupleBufArr);
    if (ret != GMERR_OK) {
        goto EXIT;
    }
    SimpRelCommonAreaAddRealItemNum(hpHandle, recordInfo->recordNum, recLen);

EXIT:
    SimpleRelFreeTupleBufArr(embCtx->apiMemCtx, &tupleBufArr);
    return ret;
}

Status SimpRelInsertAllRec2CommonArea4V1(
    EmbSimpleRunCtxT *embCtx, ImportDataCtxT *ctx, ImportV1RecordInfoT *recordInfo, VlabelCtrlT *vLabelCtrl)
{
    Status ret = SimpRelInsertComAreaPrepare(embCtx, vLabelCtrl);
    if (ret != GMERR_OK) {
        return ret;
    }

    SeTransSetDmlHint4BatchExtendNum(embCtx->seRunCtx, recordInfo->recordNum);
    ret = SimpRelInsertCommonAreaInner4v1(embCtx, ctx, recordInfo, vLabelCtrl);
    SimpRelCloseWriteCursor(embCtx->labelCursor, false);
    if (ret != GMERR_OK) {
        return ret;
    }

    vLabelCtrl->hasInsert = true;
    vLabelCtrl->isUseCommonArea = true;
    vLabelCtrl->vertexLabel->commonInfo->isUseComArea = true;
    vLabelCtrl->vertexLabel->comAreaLatch = embCtx->nspCtrl->comAreaLatch;
    vLabelCtrl->realItemCnt = recordInfo->recordNum;
    return GMERR_OK;
}

static Status SimpRelInitFixedHeapInfo(
    DbInstanceHdT dbInstance, HeapAccessCfgT *heapCfg, bool isTpcDb, VlabelCtrlT *vLabelCtrl)
{
    heapCfg->pageType = HEAP_FIX_LEN_ROW_PAGE;
    heapCfg->tupleType = HEAP_TUPLE_TYPE_VERTEX;
    heapCfg->seInstanceId = DbGetInstanceId(dbInstance);
    heapCfg->labelId = vLabelCtrl->vertexLabel->metaCommon.metaId;
    heapCfg->maxItemNum = vLabelCtrl->originDefInfo->ulMaxSize;
    heapCfg->ccType = CONCURRENCY_CONTROL_LABEL_LATCH;  // 表锁模式 \ 页锁模式使用哪个？
    heapCfg->isolation = g_adptV1Instance.isolationLevel[isTpcDb];
    heapCfg->trxType = OPTIMISTIC_TRX;
    heapCfg->isUseRsm = false;
    heapCfg->tableSpaceId = 0;
    heapCfg->isYangBigStore = false;
    heapCfg->isStatusMergeSubs = false;
    heapCfg->isPersistent = false;
    heapCfg->isLabelLockSerializable = true;
    heapCfg->fixRowSize = vLabelCtrl->originDefInfo->usRecLen;
    return CataGetTspMgrIdxById(heapCfg->tableSpaceId, &heapCfg->fsmTableSpaceIndex, dbInstance);
}

inline static void SimpRelAddRealItemNumAndWriteBytes(HpRunHdlT hpHandle, uint32_t vertexCount, uint32_t recLen)
{
    hpHandle->perfStat->phyItemNum += vertexCount;
    hpHandle->perfStat->realItemNum += vertexCount;
    hpHandle->perfStat->writeBytes += vertexCount * recLen;
}

typedef struct SimpRelChangeAreaInfo {
    DbMemCtxT *memCtx;
    VlabelCtrlT *vLabelCtrl;          // 待迁表的vLabelCtrl
    IndexUpdateInfoT *idxUpdateInfo;  // 迁移信息
    ShmemPtrT *idxShmAddr;            // 待迁表的索引(未迁移之前)
    ShmemPtrT *newIdxShmAddr;         // 新创建的索引
    ShmemPtrT fixedHeapPtr;           // 定长heap
    uint8_t *oldTtreeHead;            // 迁移前索引的head
    uint32_t recordLen;               // 数据长度
    uint16_t seMetaId;                // 数据标识哪个表的id
} SimpRelChangeAreaInfoT;

static Status GmeSimpleInitFixedHeap(EmbSimpleRunCtxT *embCtx, VlabelCtrlT *vLabelCtrl, ShmemPtrT *heapShmAddr)
{
    HeapAccessCfgT heapCfg = {0};
    Status ret = SimpRelInitFixedHeapInfo(
        DbGetInstanceByMemCtx(embCtx->apiMemCtx), &heapCfg, embCtx->nspCtrl->isTpcDb, vLabelCtrl);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "create init heap cfg.");
        return ret;
    }

    ret = HeapLabelCreate(embCtx->seRunCtx, &heapCfg, heapShmAddr);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "create heap shm addr.");
        return ret;
    }
    return GMERR_OK;
}

static Status GmeSimpleDropHeap(EmbSimpleRunCtxT *embCtx, ShmemPtrT heapShmAddr)
{
    HeapCntrAcsInfoT heapCntrAcsInfo = {.heapShmAddr = heapShmAddr,
        .isPersistent = false,
        .isUseRsm = false,
        .instanceId = embCtx->seRunCtx->instanceId};
    Status ret = HeapLabelDrop(embCtx->seRunCtx, &heapCntrAcsInfo, NULL);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

static Status GmeSimpleInitIndexInfo(
    EmbSimpleRunCtxT *embCtx, VlabelCtrlT *vLabelCtrl, DbMemCtxT *memCtx, SimpRelChangeAreaInfoT *changeAreaInfo)
{
    // 将索引全部更新成新索引
    uint32_t idxNum = vLabelCtrl->vertexLabel->metaVertexLabel->secIndexNum;
    if (idxNum != 0) {
        ShmemPtrT *idxShmAddr = DbDynMemCtxAlloc(memCtx, idxNum * (sizeof(ShmemPtrT)));
        if (SECUREC_UNLIKELY(idxShmAddr == NULL)) {
            DB_LOG_ERROR(VOS_ERRNO_DB_MEMALLOCFAILURE, "alloc memory for index shmAddr.");
            return VOS_ERRNO_DB_MEMALLOCFAILURE;
        }
        ShmemPtrT *newIdxShmAddr = DbDynMemCtxAlloc(memCtx, idxNum * (sizeof(ShmemPtrT)));
        if (SECUREC_UNLIKELY(newIdxShmAddr == NULL)) {
            DB_LOG_ERROR(VOS_ERRNO_DB_MEMALLOCFAILURE, "alloc memory for new index shmAddr.");
            return VOS_ERRNO_DB_MEMALLOCFAILURE;
        }
        uint8_t idxSeType = vLabelCtrl->isUseCommonArea ? TTREE_FIX_ROW : TTREE_VAR_ROW;
        // 索引先插入可能会有冲突，将备份插入的数据插入到新的索引，保留老的索引
        for (uint32_t i = 0; i < idxNum; i++) {
            DmVlIndexLabelT *index = &vLabelCtrl->vertexLabel->metaVertexLabel->secIndexes[i];
            idxShmAddr[i] = index->idxLabelBase.shmAddr;
            TTreeHeadT *ttreeIdx = (TTreeHeadT *)DbShmPtrToAddr(index->idxLabelBase.shmAddr);
            if (i == 0) {
                changeAreaInfo->oldTtreeHead = (uint8_t *)ttreeIdx;  // 设置老索引的head用于查询数据
            }
            IndexMetaCfgT indexCfg = ttreeIdx->idxBase.indexCfg;
            indexCfg.idxSeType = idxSeType;
            Status ret = IdxCreate(embCtx->seRunCtx, indexCfg, &(index->idxLabelBase.shmAddr));
            if (ret != GMERR_OK) {
                DB_LOG_ERROR(ret, "create ttree shm addr, index:%" PRIu32 ".", i);
                return ret;
            }
            newIdxShmAddr[i] = index->idxLabelBase.shmAddr;
        }
        changeAreaInfo->idxShmAddr = idxShmAddr;
        changeAreaInfo->newIdxShmAddr = newIdxShmAddr;
    }
    return GMERR_OK;
}

static Status GmeSimpleInitCommonArea2FixedInfo(
    EmbSimpleRunCtxT *embCtx, VlabelCtrlT *vLabelCtrl, SimpRelChangeAreaInfoT *changeAreaInfo)
{
    DbMemCtxArgsT args = {};
    DbMemCtxT *memCtx = DbCreateDynMemCtx(embCtx->apiMemCtx, false, "comAreaMemCtx", &args);
    if (memCtx == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_MEMORY_OPERATE_FAILED, "alloc comAreaMemCtx.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    uint32_t allocSize = vLabelCtrl->realItemCnt * (sizeof(IndexUpdateInfoT));
    IndexUpdateInfoT *idxUpdateInfo = DbDynMemCtxAlloc(memCtx, allocSize);
    if (SECUREC_UNLIKELY(idxUpdateInfo == NULL)) {
        DbDeleteDynMemCtx(memCtx);
        DB_LOG_ERROR(VOS_ERRNO_DB_MEMALLOCFAILURE, "alloc memory for tupleBufArr.");
        return VOS_ERRNO_DB_MEMALLOCFAILURE;
    }
    (void)memset_s(idxUpdateInfo, allocSize, 0x00, allocSize);

    ShmemPtrT fixedHeapShmAddr;
    Status ret = GmeSimpleInitFixedHeap(embCtx, vLabelCtrl, &fixedHeapShmAddr);
    if (ret != GMERR_OK) {
        DbDeleteDynMemCtx(memCtx);
        return ret;
    }

    ret = GmeSimpleInitIndexInfo(embCtx, vLabelCtrl, memCtx, changeAreaInfo);
    if (ret != GMERR_OK) {
        (void)GmeSimpleDropHeap(embCtx, fixedHeapShmAddr);
        DbDeleteDynMemCtx(memCtx);
        return ret;
    }
    changeAreaInfo->recordLen = vLabelCtrl->originDefInfo->usRecLen;
    changeAreaInfo->fixedHeapPtr = fixedHeapShmAddr;
    changeAreaInfo->idxUpdateInfo = idxUpdateInfo;
    changeAreaInfo->memCtx = memCtx;
    changeAreaInfo->vLabelCtrl = vLabelCtrl;
    return GMERR_OK;
}

static Status GmeSimpleInitFixed2CommonAreaInfo(
    EmbSimpleRunCtxT *embCtx, VlabelCtrlT *vLabelCtrl, SimpRelChangeAreaInfoT *changeAreaInfo)
{
    DbMemCtxArgsT args = {};
    DbMemCtxT *memCtx = DbCreateDynMemCtx(embCtx->apiMemCtx, false, "comAreaMemCtx", &args);
    if (memCtx == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_MEMORY_OPERATE_FAILED, "alloc comAreaMemCtx.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    if (vLabelCtrl->realItemCnt != 0) {
        uint32_t allocSize = vLabelCtrl->realItemCnt * (sizeof(IndexUpdateInfoT));
        IndexUpdateInfoT *idxUpdateInfo = DbDynMemCtxAlloc(memCtx, allocSize);
        if (SECUREC_UNLIKELY(idxUpdateInfo == NULL)) {
            DbDeleteDynMemCtx(memCtx);
            DB_LOG_ERROR(VOS_ERRNO_DB_MEMALLOCFAILURE, "alloc memory for tupleBufArr.");
            return VOS_ERRNO_DB_MEMALLOCFAILURE;
        }
        (void)memset_s(idxUpdateInfo, allocSize, 0x00, allocSize);
        changeAreaInfo->idxUpdateInfo = idxUpdateInfo;
    }

    Status ret = GmeSimpleInitIndexInfo(embCtx, vLabelCtrl, memCtx, changeAreaInfo);
    if (ret != GMERR_OK) {
        DbDeleteDynMemCtx(memCtx);
        return ret;
    }
    changeAreaInfo->recordLen = vLabelCtrl->originDefInfo->usRecLen;
    changeAreaInfo->seMetaId = vLabelCtrl->seMetaId;
    changeAreaInfo->fixedHeapPtr = vLabelCtrl->vertexLabel->commonInfo->heapInfo.heapShmAddr;  // 保存当前定长空间的
    changeAreaInfo->memCtx = memCtx;
    changeAreaInfo->vLabelCtrl = vLabelCtrl;
    return GMERR_OK;
}

static void GmeSimpleRelease2FixedHeapChangeAreaInfo(
    EmbSimpleRunCtxT *embCtx, Status status, SimpRelChangeAreaInfoT *changeAreaInfo)
{
    if (status != GMERR_OK) {
        // 共享空间迁移到定长失败
        // 异常分支释放新建的索引，将索引更新成老的索引
        for (uint32_t i = 0; i < changeAreaInfo->vLabelCtrl->vertexLabel->metaVertexLabel->secIndexNum; i++) {
            DmVlIndexLabelT *index = &changeAreaInfo->vLabelCtrl->vertexLabel->metaVertexLabel->secIndexes[i];
            index->idxLabelBase.shmAddr = changeAreaInfo->idxShmAddr[i];
            // 需要Dorp索引
            (void)IdxDrop(embCtx->seRunCtx, TTREE_INDEX, changeAreaInfo->newIdxShmAddr[i]);
        }
        // 删除创建的定长空间
        (void)GmeSimpleDropHeap(embCtx, changeAreaInfo->fixedHeapPtr);
        // 将共享空间更新回来
        changeAreaInfo->vLabelCtrl->vertexLabel->commonInfo->heapInfo.heapShmAddr = embCtx->nspCtrl->comAreaPtr;
    }
    if (changeAreaInfo->memCtx != NULL) {
        DbDeleteDynMemCtx(changeAreaInfo->memCtx);
    }
}

static void GmeSimpleRelease2ComAreaChangeAreaInfo(
    EmbSimpleRunCtxT *embCtx, Status status, SimpRelChangeAreaInfoT *changeAreaInfo)
{
    if (status != GMERR_OK) {
        // 定长迁移到共享空间失败
        // 异常分支释放新建的索引，将索引更新成老的索引
        for (uint32_t i = 0; i < changeAreaInfo->vLabelCtrl->vertexLabel->metaVertexLabel->secIndexNum; i++) {
            DmVlIndexLabelT *index = &changeAreaInfo->vLabelCtrl->vertexLabel->metaVertexLabel->secIndexes[i];
            index->idxLabelBase.shmAddr = changeAreaInfo->idxShmAddr[i];

            // 需要Dorp索引
            (void)IdxDrop(embCtx->seRunCtx, TTREE_INDEX, changeAreaInfo->newIdxShmAddr[i]);
        }
        // 将定长空间的恢复回表上
        changeAreaInfo->vLabelCtrl->vertexLabel->commonInfo->heapInfo.heapShmAddr = changeAreaInfo->fixedHeapPtr;
    } else {
        // 定长迁移到共享空间成功，直接删除其存储空间
        (void)GmeSimpleDropHeap(embCtx, changeAreaInfo->fixedHeapPtr);
    }
    if (changeAreaInfo->memCtx != NULL) {
        DbDeleteDynMemCtx(changeAreaInfo->memCtx);
    }
}

static Status ChangeAreaInsertIndex(SimpRelChangeAreaInfoT *changeAreaInfo, SimRelLabelCursorT *labelCursor)
{
    // 将索引全部更新成新
    uint32_t idxNum = changeAreaInfo->vLabelCtrl->vertexLabel->metaVertexLabel->secIndexNum;
    DB_ASSERT(idxNum != 0);
    // 索引先插入可能会有冲突，将备份插入的数据插入到新的索引，保留老的索引
    for (uint32_t i = 0; i < idxNum; i++) {
        IndexCtxT *idxCtx = labelCursor->idxCtx[i];
        for (uint32_t k = 0; k < changeAreaInfo->vLabelCtrl->realItemCnt; k++) {
            idxCtx->ttreeDataInfo.addr64 = changeAreaInfo->idxUpdateInfo[k].newAddr;
            idxCtx->ttreeDataInfo.masterAddr64 = changeAreaInfo->idxUpdateInfo[k].newAddr;
            idxCtx->ttreeDataInfo.buf = changeAreaInfo->idxUpdateInfo[k].newIdxKey.keyData;
            idxCtx->ttreeDataInfo.isUndoData = false;
            idxCtx->destNode = NULL_NODEID;
            Status ret =
                IdxInsert(idxCtx, changeAreaInfo->idxUpdateInfo[k].newIdxKey, changeAreaInfo->idxUpdateInfo[k].newAddr);
            if (ret != STATUS_OK_INTER) {
                return ret;
            }
        }
    }
    return GMERR_OK;
}

static Status GmeSimple2FixedHeapInsertAndDelete(
    SimpRelChangeAreaInfoT *changeAreaInfo, SimRelLabelCursorT *labelCursor)
{
    Status ret = GMERR_OK;
    // 调用BatchUpdate获取全数据
    IndexCtxT *secIdxCtx = labelCursor->idxCtx[0];
    ret = IdxFetchAddr(secIdxCtx, changeAreaInfo->oldTtreeHead, changeAreaInfo->vLabelCtrl->realItemCnt,
        changeAreaInfo->idxUpdateInfo);
    if (ret != GMERR_OK) {
        return ret;
    }
    HeapChangeAreaInfoT heapChangeInfo = {
        .isCommonArea2Fix = true,
        .newAddr = changeAreaInfo->fixedHeapPtr,
        .recordLen = changeAreaInfo->recordLen,
        .recordNum = changeAreaInfo->vLabelCtrl->realItemCnt,
    };
    // heap迁移
    ret = HeapChangeArea(
        secIdxCtx->idxOpenCfg.seRunCtx, &heapChangeInfo, (HeapUpdateInfoT *)changeAreaInfo->idxUpdateInfo);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = ChangeAreaInsertIndex(changeAreaInfo, labelCursor);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = HeapLabelResetOpType(labelCursor->hpHandle, HEAP_OPTYPE_DELETE, false);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "reset opType HEAP_OPTYPE_DELETE.");
        return ret;
    }
    // 删除共享空间的数据
    labelCursor->hpHandle->compV1Info.isDropTblCleanRec = true;
    for (uint32_t i = 0; i < changeAreaInfo->vLabelCtrl->realItemCnt; i++) {
        ret = HeapDelete(
            labelCursor->hpHandle, (*(HpItemPointerT *)(void *)&changeAreaInfo->idxUpdateInfo[i].oldAddr), false);
        if (ret != GMERR_OK) {
            labelCursor->hpHandle->compV1Info.isDropTblCleanRec = false;
            return ret;
        }
    }
    labelCursor->hpHandle->compV1Info.isDropTblCleanRec = false;
    return GMERR_OK;
}

static Status GmeSimple2FixedheapExec(
    EmbSimpleRunCtxT *embCtx, SimpRelChangeAreaInfoT *changeAreaInfo, SimRelLabelCursorT *labelCursor)
{
    Status ret = SimpleRelHeapOpen(NULL, embCtx->seRunCtx, labelCursor->memCtx, changeAreaInfo->vLabelCtrl->vertexLabel,
        &labelCursor->hpHandle, HEAP_OPTYPE_NONE_OP);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = SimpleRelIndicesOpen(embCtx->seRunCtx, changeAreaInfo->vLabelCtrl->vertexLabel, labelCursor);
    if (ret != GMERR_OK) {
        SimpRelCloseWriteCursor(labelCursor, true);
        return ret;
    }
    ret = GmeSimple2FixedHeapInsertAndDelete(changeAreaInfo, labelCursor);
    if (ret != GMERR_OK) {
        SimpRelCloseWriteCursor(labelCursor, true);
        return ret;
    }
    // 更新heap和标志位
    changeAreaInfo->vLabelCtrl->vertexLabel->commonInfo->heapInfo.heapShmAddr = changeAreaInfo->fixedHeapPtr;
    ret = SimpleRelHeapOpen(NULL, embCtx->seRunCtx, embCtx->apiMemCtx, changeAreaInfo->vLabelCtrl->vertexLabel,
        &labelCursor->hpHandle, HEAP_OPTYPE_NONE_OP);
    if (ret != GMERR_OK) {
        SimpRelCloseWriteCursor(labelCursor, true);
        return ret;
    }
    SimpRelAddRealItemNumAndWriteBytes(labelCursor->hpHandle, changeAreaInfo->vLabelCtrl->realItemCnt,
        changeAreaInfo->vLabelCtrl->originDefInfo->usRecLen);
    SimpRelCloseWriteCursor(labelCursor, true);
    return GMERR_OK;
}

static Status GmeSimpleCommonArea2FixedHeapInner(
    EmbSimpleRunCtxT *embCtx, VlabelCtrlT *vLabelCtrl, uint32_t totalRecNum)
{
    uint64_t startTime = DbClockGetTsc();
    // 新的迁移逻辑
    // 不开启事务
    // 创建新的索引addr 和 定长heap addr
    SimpRelChangeAreaInfoT changeAreaInfo = {0};
    Status ret = GmeSimpleInitCommonArea2FixedInfo(embCtx, vLabelCtrl, &changeAreaInfo);
    if (ret != GMERR_OK) {
        return ret;
    }
    SimRelLabelCursorT labelCursor = {0};
    labelCursor.vertexLabel = changeAreaInfo.vLabelCtrl->vertexLabel;
    labelCursor.memCtx = changeAreaInfo.memCtx;
    ret = GmeSimple2FixedheapExec(embCtx, &changeAreaInfo, &labelCursor);
    if (ret != GMERR_OK) {
        GmeSimpleRelease2FixedHeapChangeAreaInfo(embCtx, ret, &changeAreaInfo);
        return ret;
    }

    // 删除保存下来的索引
    for (uint32_t i = 0; i < changeAreaInfo.vLabelCtrl->vertexLabel->metaVertexLabel->secIndexNum; i++) {
        (void)IdxDrop(embCtx->seRunCtx, TTREE_INDEX, changeAreaInfo.idxShmAddr[i]);
        DmVlIndexLabelT *index = &vLabelCtrl->vertexLabel->metaVertexLabel->secIndexes[i];
        index->idxLabelBase.shmAddr = changeAreaInfo.newIdxShmAddr[i];
    }
    GmeSimpleRelease2FixedHeapChangeAreaInfo(embCtx, ret, &changeAreaInfo);
    vLabelCtrl->isUseCommonArea = false;
    vLabelCtrl->vertexLabel->commonInfo->isUseComArea = false;
    vLabelCtrl->vertexLabel->comAreaLatch = NULL;
    uint64_t costTime = DbToMseconds(DbClockGetTsc() - startTime);
    if (costTime > DB_CHANGE_AREA_MAX_TIME) {
        DB_LOG_WARN_UNFOLD(GMERR_REQUEST_TIME_OUT, "common area to fixed heap cost time:%" PRIu64 " ms.", costTime);
    }
    return GMERR_OK;
}

// 函数用于表压缩时的迁出，所以需要函数内部加锁开事务
Status GmeSimpleCommonArea2FixedHeap(EmbSimpleRunCtxT *embCtx, uint32_t totalRecNum, bool *isAlreadyChangeArea)
{
    VlabelCtrlT *vLabelCtrl = embCtx->vLabelCtrl;
    // 当前表未使用共享空间，直接返回
    if (!vLabelCtrl->isUseCommonArea) {
        return GMERR_OK;
    }
    uint32_t oneRecLen = vLabelCtrl->originDefInfo->usRecLen;
    // 当前表未达到迁出共享空间条件，直接返回
    if (!SimpleRelCheckIsUseFixedHeap(oneRecLen, vLabelCtrl->realItemCnt)) {
        return GMERR_OK;
    }
    // 加锁
    DbRWLatchW(embCtx->nspCtrl->comAreaLatch);
    // 开事务
    SessionT *session = embCtx->session->session;
    TrxCfgT cfg = {.isolationLevel = g_adptV1Instance.isolationLevel[embCtx->isTwoStage], .trxType = OPTIMISTIC_TRX};
    Status ret = SeTransBegin(session->seInstance, &cfg);
    if (ret != GMERR_OK) {
        DbRWUnlatchW(embCtx->nspCtrl->comAreaLatch);
        return ret;
    }
    ret = SeTransAssignReadView(session->seInstance);
    if (ret != GMERR_OK) {
        (void)SeTransRollback(session->seInstance, false);
        DbRWUnlatchW(embCtx->nspCtrl->comAreaLatch);
        return ret;
    }
    ret = GmeSimpleCommonArea2FixedHeapInner(embCtx, vLabelCtrl, totalRecNum);
    if (ret != GMERR_OK) {
        (void)SeTransRollback(session->seInstance, false);
        DbRWUnlatchW(embCtx->nspCtrl->comAreaLatch);
        return ret;
    }
    ret = SeTransCommit(session->seInstance);
    if (ret != GMERR_OK) {
        (void)SeTransRollback(session->seInstance, false);
        DbRWUnlatchW(embCtx->nspCtrl->comAreaLatch);
        return ret;
    }
    DbRWUnlatchW(embCtx->nspCtrl->comAreaLatch);
    *isAlreadyChangeArea = true;
    return GMERR_OK;
}

static void GmeSimpleClearCommonAreaResidue(EmbSimpleRunCtxT *embCtx, SimpRelChangeAreaInfoT *changeAreaInfo)
{
    if (changeAreaInfo->idxUpdateInfo[0].newAddr == 0) {
        // 走进该分支说明 未向共享空间写入数据
        return;
    }
    SimRelLabelCursorT labelCursor = {0};
    labelCursor.vertexLabel = changeAreaInfo->vLabelCtrl->vertexLabel;
    labelCursor.memCtx = changeAreaInfo->memCtx;
    changeAreaInfo->vLabelCtrl->vertexLabel->commonInfo->heapInfo.heapShmAddr = embCtx->nspCtrl->comAreaPtr;
    Status ret = SimpleRelHeapOpen(NULL, embCtx->seRunCtx, embCtx->apiMemCtx, changeAreaInfo->vLabelCtrl->vertexLabel,
        &labelCursor.hpHandle, HEAP_OPTYPE_DELETE);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "heap pen when clear common area residue.");
        return;
    }
    uint32_t recLen = changeAreaInfo->vLabelCtrl->originDefInfo->usRecLen + DB_V1_TBLID_LEN;
    // 先设置已插入个数为需要迁移的数据条数，计数为0的时候删除可能会导致数据反转
    labelCursor.hpHandle->perfStat->phyItemNum += changeAreaInfo->vLabelCtrl->realItemCnt;
    labelCursor.hpHandle->perfStat->realItemNum += changeAreaInfo->vLabelCtrl->realItemCnt;
    labelCursor.hpHandle->perfStat->writeBytes += changeAreaInfo->vLabelCtrl->realItemCnt * recLen;
    uint32_t recCnt = 0;
    // 删除共享空间的数据
    labelCursor.hpHandle->compV1Info.isDropTblCleanRec = true;
    for (uint32_t i = 0; i < changeAreaInfo->vLabelCtrl->realItemCnt; i++) {
        if (changeAreaInfo->idxUpdateInfo[i].newAddr != 0) {
            ret = HeapDelete(
                labelCursor.hpHandle, (*(HpItemPointerT *)(void *)&changeAreaInfo->idxUpdateInfo[i].newAddr), false);
            if (ret != GMERR_OK) {
                labelCursor.hpHandle->compV1Info.isDropTblCleanRec = false;
                SimpRelCloseWriteCursor(&labelCursor, true);
                DB_LOG_ERROR(ret, "heap delete when clear common area residue.");
                return;
            }
            recCnt++;
        }
    }
    labelCursor.hpHandle->compV1Info.isDropTblCleanRec = false;

    // 刷新HpHandle上perfStat的计数
    labelCursor.hpHandle->perfStat->phyItemNum -= (changeAreaInfo->vLabelCtrl->realItemCnt - recCnt);
    labelCursor.hpHandle->perfStat->realItemNum -= (changeAreaInfo->vLabelCtrl->realItemCnt - recCnt);
    labelCursor.hpHandle->perfStat->writeBytes -= (changeAreaInfo->vLabelCtrl->realItemCnt - recCnt) * recLen;

    SimpRelCloseWriteCursor(&labelCursor, true);
}

// 通过索引查询出逻辑，物理，插入到新的定长的heap addr 中 （查询，插入）
static Status GmeSimple2CommonAreaInsert(
    EmbSimpleRunCtxT *embCtx, SimpRelChangeAreaInfoT *changeAreaInfo, SimRelLabelCursorT *labelCursor)
{
    Status ret = GMERR_OK;
    // 调用BatchUpdate获取全数据
    IndexCtxT *secIdxCtx = labelCursor->idxCtx[0];
    ret = IdxFetchAddr(secIdxCtx, changeAreaInfo->oldTtreeHead, changeAreaInfo->vLabelCtrl->realItemCnt,
        changeAreaInfo->idxUpdateInfo);
    if (ret != GMERR_OK) {
        return ret;
    }
    HeapChangeAreaInfoT heapChangeInfo = {.isCommonArea2Fix = false,
        .newAddr = embCtx->nspCtrl->comAreaPtr,
        .recordLen = changeAreaInfo->recordLen,
        .recordNum = changeAreaInfo->vLabelCtrl->realItemCnt,
        .v1LableId = changeAreaInfo->seMetaId};
    // heap迁移
    ret = HeapChangeArea(
        secIdxCtx->idxOpenCfg.seRunCtx, &heapChangeInfo, (HeapUpdateInfoT *)changeAreaInfo->idxUpdateInfo);
    if (ret != GMERR_OK) {
        GmeSimpleClearCommonAreaResidue(embCtx, changeAreaInfo);
        return ret;
    }
    ret = ChangeAreaInsertIndex(changeAreaInfo, labelCursor);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = HeapLabelResetOpType(labelCursor->hpHandle, HEAP_OPTYPE_DELETE, false);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "reset opType HEAP_OPTYPE_DELETE.");
        return ret;
    }
    // 删除共享空间的数据
    labelCursor->hpHandle->compV1Info.isDropTblCleanRec = true;
    for (uint32_t i = 0; i < changeAreaInfo->vLabelCtrl->realItemCnt; i++) {
        ret = HeapDelete(
            labelCursor->hpHandle, (*(HpItemPointerT *)(void *)&changeAreaInfo->idxUpdateInfo[i].oldAddr), false);
        if (ret != GMERR_OK) {
            labelCursor->hpHandle->compV1Info.isDropTblCleanRec = false;
            return ret;
        }
    }
    labelCursor->hpHandle->compV1Info.isDropTblCleanRec = false;
    return GMERR_OK;
}

static Status GmeSimple2CommonAreaExec(
    EmbSimpleRunCtxT *embCtx, SimpRelChangeAreaInfoT *changeAreaInfo, SimRelLabelCursorT *labelCursor)
{
    // 这里打开老的heap空间
    Status ret = SimpleRelHeapOpen(NULL, embCtx->seRunCtx, labelCursor->memCtx, changeAreaInfo->vLabelCtrl->vertexLabel,
        &labelCursor->hpHandle, HEAP_OPTYPE_NONE_OP);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = SimpleRelIndicesOpen(embCtx->seRunCtx, changeAreaInfo->vLabelCtrl->vertexLabel, labelCursor);
    if (ret != GMERR_OK) {
        SimpRelCloseWriteCursor(labelCursor, true);
        return ret;
    }
    // 插入数据到共享空间，定长空间的数据在最后一定成功时，直接drop heap
    ret = GmeSimple2CommonAreaInsert(embCtx, changeAreaInfo, labelCursor);
    if (ret != GMERR_OK) {
        SimpRelCloseWriteCursor(labelCursor, true);
        return ret;
    }

    // 更新heap和标志位
    changeAreaInfo->vLabelCtrl->vertexLabel->commonInfo->heapInfo.heapShmAddr = embCtx->nspCtrl->comAreaPtr;
    ret = SimpleRelHeapOpen(NULL, embCtx->seRunCtx, embCtx->apiMemCtx, changeAreaInfo->vLabelCtrl->vertexLabel,
        &labelCursor->hpHandle, HEAP_OPTYPE_NONE_OP);
    if (ret != GMERR_OK) {
        SimpRelCloseWriteCursor(labelCursor, true);
        return ret;
    }
    // 刷新HpHandle上perfStat的计数
    SimpRelAddRealItemNumAndWriteBytes(labelCursor->hpHandle, changeAreaInfo->vLabelCtrl->realItemCnt,
        changeAreaInfo->vLabelCtrl->originDefInfo->usRecLen + DB_V1_TBLID_LEN);
    SimpRelCloseWriteCursor(labelCursor, true);
    return GMERR_OK;
}

static Status GmeSimpleFixedHeap2CommonAreaInner(
    EmbSimpleRunCtxT *embCtx, VlabelCtrlT *vLabelCtrl, uint32_t totalRecNum)
{
    uint64_t startTime = DbClockGetTsc();
    // 新的迁移逻辑
    // 不开启事务
    // 创建新的索引addr 和 定长heap addr
    SimpRelChangeAreaInfoT changeAreaInfo = {0};
    Status ret = GmeSimpleInitFixed2CommonAreaInfo(embCtx, vLabelCtrl, &changeAreaInfo);
    if (ret != GMERR_OK) {
        return ret;
    }
    SimRelLabelCursorT labelCursor = {0};
    labelCursor.vertexLabel = vLabelCtrl->vertexLabel;
    labelCursor.memCtx = changeAreaInfo.memCtx;

    ret = GmeSimple2CommonAreaExec(embCtx, &changeAreaInfo, &labelCursor);
    if (ret != GMERR_OK) {
        GmeSimpleRelease2ComAreaChangeAreaInfo(embCtx, ret, &changeAreaInfo);
        return ret;
    }
    // 删除保存下来的索引
    for (uint32_t i = 0; i < changeAreaInfo.vLabelCtrl->vertexLabel->metaVertexLabel->secIndexNum; i++) {
        (void)IdxDrop(embCtx->seRunCtx, TTREE_INDEX, changeAreaInfo.idxShmAddr[i]);
        DmVlIndexLabelT *index = &vLabelCtrl->vertexLabel->metaVertexLabel->secIndexes[i];
        index->idxLabelBase.shmAddr = changeAreaInfo.newIdxShmAddr[i];
    }
    // 走到这里说明已经迁移成功，索引和heap均已更新
    GmeSimpleRelease2ComAreaChangeAreaInfo(embCtx, ret, &changeAreaInfo);
    vLabelCtrl->isUseCommonArea = true;
    vLabelCtrl->vertexLabel->commonInfo->isUseComArea = true;
    vLabelCtrl->vertexLabel->comAreaLatch = embCtx->nspCtrl->comAreaLatch;
    uint64_t costTime = DbToMseconds(DbClockGetTsc() - startTime);
    if (costTime > DB_CHANGE_AREA_MAX_TIME) {
        DB_LOG_WARN_UNFOLD(GMERR_REQUEST_TIME_OUT, "fixed heap to common area cost time:%" PRIu64 " ms.", costTime);
    }
    return GMERR_OK;
}

Status GmeSimpleFixedHeap2CommonArea(EmbSimpleRunCtxT *embCtx, uint32_t totalRecNum, bool *isAlreadyChangeArea)
{
    VlabelCtrlT *vLabelCtrl = embCtx->vLabelCtrl;
    // 当前表未使用共享空间，直接返回
    if (vLabelCtrl->isUseCommonArea) {
        return GMERR_OK;
    }
    // 当前表未达到迁入共享空间条件，直接返回
    if (!SimpleRelCheckIsUseComArea(embCtx->vLabelCtrl, totalRecNum)) {
        return GMERR_OK;
    }
    // 共享空间迁入，定长Heap迁出
    DbRWLatchW(embCtx->nspCtrl->comAreaLatch);
    // 开事务
    SessionT *session = embCtx->session->session;
    TrxCfgT cfg = {.isolationLevel = g_adptV1Instance.isolationLevel[embCtx->isTwoStage], .trxType = OPTIMISTIC_TRX};
    Status ret = SeTransBegin(session->seInstance, &cfg);
    if (ret != GMERR_OK) {
        DbRWUnlatchW(embCtx->nspCtrl->comAreaLatch);
        return ret;
    }
    ret = SeTransAssignReadView(session->seInstance);
    if (ret != GMERR_OK) {
        (void)SeTransRollback(session->seInstance, false);
        DbRWUnlatchW(embCtx->nspCtrl->comAreaLatch);
        return ret;
    }
    ret = GmeSimpleFixedHeap2CommonAreaInner(embCtx, vLabelCtrl, totalRecNum);
    if (ret != GMERR_OK) {
        (void)SeTransRollback(session->seInstance, false);
        DbRWUnlatchW(embCtx->nspCtrl->comAreaLatch);
        return ret;
    }
    ret = SeTransCommit(session->seInstance);
    if (ret != GMERR_OK) {
        (void)SeTransRollback(session->seInstance, false);
        DbRWUnlatchW(embCtx->nspCtrl->comAreaLatch);
        return ret;
    }
    DbRWUnlatchW(embCtx->nspCtrl->comAreaLatch);
    *isAlreadyChangeArea = true;
    return GMERR_OK;
}

static Status GmeSimpleFixedHeap2CommonAreaZero(EmbSimpleRunCtxT *embCtx, VlabelCtrlT *vLabelCtrl)
{
    SimpRelChangeAreaInfoT changeAreaInfo = {0};
    Status ret = GmeSimpleInitFixed2CommonAreaInfo(embCtx, vLabelCtrl, &changeAreaInfo);
    if (ret != GMERR_OK) {
        return ret;
    }

    for (uint32_t i = 0; i < changeAreaInfo.vLabelCtrl->vertexLabel->metaVertexLabel->secIndexNum; i++) {
        (void)IdxDrop(embCtx->seRunCtx, TTREE_INDEX, changeAreaInfo.idxShmAddr[i]);
        DmVlIndexLabelT *index = &vLabelCtrl->vertexLabel->metaVertexLabel->secIndexes[i];
        index->idxLabelBase.shmAddr = changeAreaInfo.newIdxShmAddr[i];
    }

    GmeSimpleRelease2ComAreaChangeAreaInfo(embCtx, ret, &changeAreaInfo);
    vLabelCtrl->vertexLabel->commonInfo->heapInfo.heapShmAddr = embCtx->nspCtrl->comAreaPtr;
    vLabelCtrl->isUseCommonArea = true;
    vLabelCtrl->vertexLabel->commonInfo->isUseComArea = true;
    vLabelCtrl->vertexLabel->comAreaLatch = embCtx->nspCtrl->comAreaLatch;
    return GMERR_OK;
}

static Status GmeSimpleChangeAreaExec(EmbSimpleRunCtxT *embCtx)
{
    Status ret = GMERR_OK;
    if (embCtx->vLabelCtrl->isUseCommonArea) {
#ifndef NDEBUG
        // 从共享空间做迁出一定是 框架 加了共享空间写锁状态
        DB_ASSERT(DbRWLatchIsWriteLock(embCtx->nspCtrl->comAreaLatch));
#endif
        ret = GmeSimpleCommonArea2FixedHeapInner(embCtx, embCtx->vLabelCtrl, embCtx->vLabelCtrl->realItemCnt);
        if (ret != GMERR_OK) {
            return ret;
        }
        // 迁出成功后一定要把写锁解掉，因为标志位被修改，框架不会走解锁逻辑了
        DbRWUnlatchW(embCtx->nspCtrl->comAreaLatch);
    } else {
        // 迁入共享空间，对共享空间加写锁，解锁的逻辑放在框架内，需要注意在标志位修改之前失败需要放锁
        DbRWLatchW(embCtx->nspCtrl->comAreaLatch);
        // 考虑数据条数为0的特殊场景
        if (embCtx->vLabelCtrl->realItemCnt == 0) {
            ret = GmeSimpleFixedHeap2CommonAreaZero(embCtx, embCtx->vLabelCtrl);
            if (ret != GMERR_OK) {
                DbRWUnlatchW(embCtx->nspCtrl->comAreaLatch);
                return ret;
            }
            return GMERR_OK;
        }
        ret = GmeSimpleFixedHeap2CommonAreaInner(embCtx, embCtx->vLabelCtrl, embCtx->vLabelCtrl->realItemCnt);
        if (ret != GMERR_OK) {
            DbRWUnlatchW(embCtx->nspCtrl->comAreaLatch);
            return ret;
        }
    }
    return GMERR_OK;
}

Status GmeSimpleCommonAreaHasUndo(EmbSimpleRunCtxT *embCtx, VlabelCtrlT *targetCtrl, bool *hasUndo)
{
    *hasUndo = false;
    for (uint32_t usTab = 0; usTab < embCtx->nspCtrl->vLabelCtrlList.count; usTab++) {
        VlabelCtrlT *vLabelCtrl = DbListItem(&embCtx->nspCtrl->vLabelCtrlList, usTab);
        if (vLabelCtrl->needLazyLoad) {
            continue;
        }
        if (!vLabelCtrl->isUseCommonArea && vLabelCtrl->relationId != targetCtrl->relationId) {
            continue;
        }
        DmVlIndexLabelT *vlIdx = &vLabelCtrl->vertexLabel->metaVertexLabel->secIndexes[0];
        TTreeHeadT *ttreeHead = (TTreeHeadT *)DbShmPtrToAddr(vlIdx->idxLabelBase.shmAddr);
        for (uint32_t i = 0; i < ttreeHead->nodeCnt; i++) {
            TTreeNodeT *pstTTreeNode = GetTTreeNode(ttreeHead, i);
            for (uint32_t j = 0; j < pstTTreeNode->ucCurIdxNum; j++) {
                uint64_t rowId = TTreeUnCompressAddr64(pstTTreeNode->simpleAddr[j]);
                PageIdT pageBlockId = TTreeDeserializePageId(((HpItemPointerT *)(void *)&rowId)->pageId);
                DB_ASSERT(g_devEntryCacheHac[pageBlockId.deviceId] != NULL);
                PageHeadT *pageHead = (void *)g_devEntryCacheHac[pageBlockId.deviceId] +
                                      ((uint64_t)pageBlockId.blockId << g_AddrCompressMgr.pageSizeShiftBit);
                if (pageHead->pageType >= PERSISTENCE_PAGE_TYPE_UNDO_RSEG &&
                    pageHead->pageType <= PERSISTENCE_PAGE_TYPE_UNDO) {
                    *hasUndo = true;
                    return GMERR_OK;
                }
            }
        }
    }
    return GMERR_OK;
}

Status GmeSimpleChangeArea(EmbSimpleRunCtxT *embCtx)
{
    VlabelCtrlT *vLabelCtrl = embCtx->vLabelCtrl;
    // check
    if (vLabelCtrl->cdbRef > 0 || vLabelCtrl->handleRef > 0) {
        return GMERR_OK;
    }
    if (vLabelCtrl->originDefInfo->usNAllIdxs == 0) {
        return GMERR_OK;
    }
    uint32_t oneRecLen = embCtx->vLabelCtrl->originDefInfo->usRecLen;
    if (vLabelCtrl->isUseCommonArea) {
        // 当前表未达到迁出共享空间条件，直接返回
        if (!SimpleRelCheckIsUseFixedHeap(oneRecLen, vLabelCtrl->realItemCnt)) {
            return GMERR_OK;
        }
    } else {
        uint32_t recCnt = vLabelCtrl->realItemCnt != 0 ? vLabelCtrl->realItemCnt : 1;
        // 当前表未达到迁入共享空间条件，直接返回
        if (!SimpleRelCheckIsUseComArea4ChangeArea(embCtx->vLabelCtrl, recCnt)) {
            return GMERR_OK;
        }
    }
    // 本次进入操作无事务
    if (!(embCtx->session->session->isInteractiveTrx ||
            (embCtx->isZeroRDBConn && g_adptV1Instance.RDBTrxState == RDB_TRX_RUNNING))) {
        return GMERR_OK;
    }
    bool hasUndo = false;
    // 判断是否还有未处理完的后台任务
    Status ret = GmeSimpleCommonAreaHasUndo(embCtx, embCtx->vLabelCtrl, &hasUndo);
    if (ret != GMERR_OK || hasUndo) {
        return GMERR_OK;
    }
    return GmeSimpleChangeAreaExec(embCtx);
}
