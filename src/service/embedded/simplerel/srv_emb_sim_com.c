/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: Embedded SIMPLE REL API
 * Author:
 * Create:
 */
#include "srv_emb_sim_com.h"
#include "db_sysapp_context.h"
#include "se_define.h"
#include "ee_cmd.h"
#include "dm_data_prop_in.h"
#include "dm_meta_vertex_label.h"
#include "dm_cache_basic.h"
#include "se_trx_inner.h"
#include "se_heap_base.h"
#include "se_ttree_index.h"
#include "se_trx_mgr.h"
#include "db_instance.h"

void DbEmbeddedUnInitSimpleRelMgr(DbInstanceT *instance)
{
    EmbSimpRelMgrT *mgr = instance->simpleRelMgr;
    DbDestroyList(&mgr->nspCtrlList);
    DbDeleteDynMemCtx(mgr->wrapperMemCtx);
    DbDeleteDynMemCtx(mgr->serviceMemCtx);
    GmeSimpleRelUnInitDataTypeMgr();
}

uint32_t SimpGetLabelIdxNum(VlabelCtrlT *vLabelCtrl)
{
    if (vLabelCtrl->needLazyLoad) {
        return vLabelCtrl->originDefInfo->usNAllIdxs;
    }
    return vLabelCtrl->vertexLabel->metaVertexLabel->secIndexNum;
}

uint32_t SimpGetLabelPropNum(VlabelCtrlT *vLabelCtrl)
{
    if (vLabelCtrl->needLazyLoad) {
        return vLabelCtrl->originDefInfo->ulNCols;
    }
    return vLabelCtrl->vertexLabel->metaVertexLabel->schema->propeNum;
}

Status DbEmbeddedInitSimpleRelMgr(DbInstanceT *instance)
{
    // 初始化自定义类型数据管理结构
    Status ret = GmeSimpleRelInitDataTypeMgr();
    if (ret != GMERR_OK) {
        return ret;
    }
    DbMemCtxT *appMemCtx = DbSrvGetAppDynCtx(DbGetInstanceId(instance));
    if (appMemCtx == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(VOS_ERRNO_DB_MEMORY_OVERFLOW, "Unable to get app memctx.");
        return VOS_ERRNO_DB_MEMORY_OVERFLOW;
    }

    DbMemCtxArgsT args = {0};
    args.dynCtxNeedRecycle = true;
    args.liteModOn = true;
    // memCtx用途：V1存储DB管理结构、表管理结构的内存
    // 生命周期：长进程级别
    // 释放方式：就近释放
    // 兜底清空措施：客户端进程去初始化时（DbEmbeddedUnInitSimpleRelMgr），销毁该memCtx
    DbMemCtxT *simpleRelTopMemCtx = DbCreateDynMemCtx(appMemCtx, true, "SimpleRelTop", &args);
    if (appMemCtx == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(VOS_ERRNO_DB_MEMORY_OVERFLOW, "Unable to create simple rel top memctx.");
        return VOS_ERRNO_DB_MEMORY_OVERFLOW;
    }
    EmbSimpRelMgrT *mgr = DbDynMemCtxAlloc(simpleRelTopMemCtx, sizeof(EmbSimpRelMgrT));
    if (mgr == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(VOS_ERRNO_DB_MEMORY_OVERFLOW, "Unable to alloc simple rel mgr.");
        DbDeleteDynMemCtx(simpleRelTopMemCtx);
        return VOS_ERRNO_DB_MEMORY_OVERFLOW;
    }
    (void)memset_s(mgr, sizeof(EmbSimpRelMgrT), 0, sizeof(EmbSimpRelMgrT));

    // memCtx用途：V1适配层存储,CDB、RDB相关连接，sysview中间缓存，注册feature的内存
    // 生命周期：长进程级别
    // 释放方式：就近释放
    // 兜底清空措施：客户端进程去初始化时（DbEmbeddedUnInitSimpleRelMgr），销毁该memCtx
    mgr->wrapperMemCtx = DbCreateDynMemCtx(simpleRelTopMemCtx, true, "SimpleRelAdaptWrapper", &args);
    if (mgr->wrapperMemCtx == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(VOS_ERRNO_DB_MEMORY_OVERFLOW, "Unable to create simple rel wrapper memctx.");
        DbDeleteDynMemCtx(simpleRelTopMemCtx);
        return VOS_ERRNO_DB_MEMORY_OVERFLOW;
    }

    mgr->serviceMemCtx = simpleRelTopMemCtx;
    DbCreateList(&mgr->nspCtrlList, sizeof(NspCtrlT), simpleRelTopMemCtx);
    instance->simpleRelMgr = mgr;
    return GMERR_OK;
}

void EmbSessionUnInitSimpleRelCtx(struct TagEmbSession *embSession)
{
    EmbSimpleRunCtxT *ctx = embSession->embSimpleRunCtx;
    DbDestroyList(&ctx->concurentCtrl.labelLock);
    DbDeleteDynMemCtx((DbMemCtxT *)ctx->apiMemCtx);

    DbMemCtxT *sessionMemCtx = ctx->sessionMemctx;
    DbDynMemCtxFree(sessionMemCtx, ctx);
    embSession->embSimpleRunCtx = NULL;
    DbDeleteDynMemCtx(sessionMemCtx);
}

Status EmbSessionInitSimpleRelCtx(DbInstanceT *instance, struct TagEmbSession *embSession)
{
    Status ret = GMERR_OK;
    EmbSimpRelMgrT *mgr = instance->simpleRelMgr;
    DbMemCtxArgsT args = {0};
    args.dynCtxNeedRecycle = true;
    args.liteModOn = true;
    DbMemCtxT *simSessionCtx = DbCreateDynMemCtx(mgr->serviceMemCtx, false, "SimpleRelSession", &args);
    if (simSessionCtx == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(VOS_ERRNO_DB_MEMORY_OVERFLOW, "Unable to create simple rel session memctx.");
        return VOS_ERRNO_DB_MEMORY_OVERFLOW;
    }
    EmbSimpleRunCtxT *ctx = DbDynMemCtxAlloc(simSessionCtx, sizeof(EmbSimpleRunCtxT));
    if (ctx == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(VOS_ERRNO_DB_MEMORY_OVERFLOW, "Unable to create simple rel run context.");
        DbDeleteDynMemCtx(simSessionCtx);
        return VOS_ERRNO_DB_MEMORY_OVERFLOW;
    }
    (void)memset_s(ctx, sizeof(EmbSimpleRunCtxT), 0, sizeof(EmbSimpleRunCtxT));
    ctx->dbInstance = instance;
    ctx->simpleRelMgr = mgr;
    ctx->session = embSession;
    ctx->seRunCtx = embSession->session->seInstance;
    ctx->sessionMemctx = simSessionCtx;
    ctx->apiMemCtx = DbCreateDynMemCtx(simSessionCtx, false, "SimpleRelSessionApi", &args);
    if (ctx->apiMemCtx == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(VOS_ERRNO_DB_MEMORY_OVERFLOW, "Unable to create simple rel session api memctx.");
        DbDeleteDynMemCtx(simSessionCtx);
        return VOS_ERRNO_DB_MEMORY_OVERFLOW;
    }
    DbCreateList(&ctx->concurentCtrl.labelLock, sizeof(void *), simSessionCtx);
    ret = DbListReserve(&ctx->concurentCtrl.labelLock, SIMPLE_REL_INIT_LOCK_CNT);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(VOS_ERRNO_DB_MEMORY_OVERFLOW, "Unable to reserve list for label lock.");
        DbDeleteDynMemCtx(ctx->apiMemCtx);
        DbDeleteDynMemCtx(simSessionCtx);
        return VOS_ERRNO_DB_MEMORY_OVERFLOW;
    }
    embSession->embSimpleRunCtx = ctx;
    return GMERR_OK;
}

Status GmeSimpleRelBeginInteracTrans(GmeConnT *gmeConn)
{
    struct Session *session = gmeConn->embSession->session;
    TrxCfgT cfg = {0};
    cfg.isolationLevel = g_adptV1Instance.isolationLevel[V1TPCOP];
    cfg.trxType = OPTIMISTIC_TRX;
    cfg.isInteractive = true;
    Status ret = SeTransBegin(session->seInstance, &cfg);
    if (ret != GMERR_OK) {
        return ret;
    }
    session->isInteractiveTrx = true;
    session->needOpenHeap = true;
    return SeTransAssignReadView(session->seInstance);
}

Status SimpleRelationAllocMem(void *memCtx, size_t size, void **buf)
{
    DB_POINTER2(memCtx, buf);
    *buf = DbDynMemCtxAlloc(memCtx, size);
    if (*buf == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            VOS_ERRNO_DB_MEMALLOCFAILURE, "Unable to alloc memctx size=%" PRIu32 ".", (uint32_t)size);
        return VOS_ERRNO_DB_MEMALLOCFAILURE;
    }
    return GMERR_OK;
}

void SimpleRelationFreeMem(void *memCtx, void *ptr)
{
    DbDynMemCtxFree(memCtx, ptr);
}

Status GmeSimpleRelBeginDiff(GmeConnT *gmeConn)
{
    EmbSimpleRunCtxT *embCtx = gmeConn->embSession->embSimpleRunCtx;
    if (embCtx->diffCtx != NULL) {
        return GMERR_OK;
    }
    // 记录diff轨迹上下文
    SimpleRelDiffCtxT *diffCtx =
        (SimpleRelDiffCtxT *)DbDynMemCtxAlloc(embCtx->sessionMemctx, sizeof(SimpleRelDiffCtxT));
    if (diffCtx == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(VOS_ERRNO_DB_MEMALLOCFAILURE, "Unable to alloc diff list ctx.");
        return VOS_ERRNO_DB_MEMALLOCFAILURE;
    }
    DbMemCtxArgsT args = {0};
    args.dynCtxNeedRecycle = true;
    args.liteModOn = true;
    Status ret = GMERR_OK;
    // 统一用该memCtx管理内存，提交或者回滚事务时统一释放
    DbMemCtxT *diffMemCtx = DbCreateDynMemCtx(embCtx->sessionMemctx, false, "SimpleRelDiff", &args);
    if (diffMemCtx == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(VOS_ERRNO_DB_MEMALLOCFAILURE, "Unable to create simple rel diff memctx.");
        ret = VOS_ERRNO_DB_MEMALLOCFAILURE;
        goto Release1;
    }

    DbCreateListWithExtendSize(
        &diffCtx->labelList, sizeof(SimpleRelDiffListT), SIMPLE_REL_LIST_EXTENT_SIZE, diffMemCtx);
    DbCreateListWithExtendSize(
        &diffCtx->relNoList, sizeof(SimpRelRelNoListNodeT), SIMPLE_REL_LIST_EXTENT_SIZE, diffMemCtx);
    diffCtx->memCtx = diffMemCtx;
    diffCtx->isRedo = false;
    diffCtx->savepointSeq = 0;
    embCtx->diffCtx = diffCtx;
    return GMERR_OK;
Release1:
    DbDynMemCtxFree(embCtx->sessionMemctx, diffCtx);
    return ret;
}

void GmeSimpleRelEndDiff(GmeConnT *gmeConn)
{
    if (gmeConn == NULL) {
        return;
    }
    EmbSimpleRunCtxT *embCtx = gmeConn->embSession->embSimpleRunCtx;
    if (embCtx->diffCtx == NULL) {
        return;
    }
    DbDeleteDynMemCtx(embCtx->diffCtx->memCtx);
    DbDynMemCtxFree(embCtx->sessionMemctx, embCtx->diffCtx);
    embCtx->diffCtx = NULL;
    return;
}

void SimprelTrxLockAllTbl(GmeConnT *gmeConn, NspCtrlT *nspCtrl)
{
    DbListT *relNoList = GmeSimpleGetRedoTblList(gmeConn);
    if (relNoList == NULL || DbListGetItemCnt(relNoList) == 0) {
        return;
    }

    for (uint32_t i = 0; i < DbListGetItemCnt(relNoList); i++) {
        VlabelCtrlT *vLabelCtrl = NULL;
        uint16_t relNo = *(uint16_t *)DbListItem(relNoList, i);
        (void)SimpleRelGetVlabelCtrlById(&nspCtrl->vLabelCtrlList, relNo, NULL, &vLabelCtrl, NULL);
        if (vLabelCtrl != NULL) {
            LabelReentrantWLatchAcquire(vLabelCtrl->latch);
        }
    }
}

void SimprelTrxUnlockAllTbl(GmeConnT *gmeConn, NspCtrlT *nspCtrl)
{
    DbListT *relNoList = GmeSimpleGetRedoTblList(gmeConn);
    if (relNoList == NULL || DbListGetItemCnt(relNoList) == 0) {
        return;
    }

    for (uint32_t i = 0; i < DbListGetItemCnt(relNoList); i++) {
        VlabelCtrlT *vLabelCtrl = NULL;
        uint16_t relNo = *(uint16_t *)DbListItem(relNoList, i);
        (void)SimpleRelGetVlabelCtrlById(&nspCtrl->vLabelCtrlList, relNo, NULL, &vLabelCtrl, NULL);
        if (vLabelCtrl != NULL) {
            LabelWLatchRelease(vLabelCtrl->latch);
        }
    }
}

Status GmeSimpleRelCommitInteracTrans(GmeConnT *gmeConn)
{
    SessionT *session = gmeConn->embSession->session;
    Status ret = SeTransCommit(session->seInstance);
    if (ret != GMERR_OK) {
        return ret;
    }
    session->isInteractiveTrx = false;
    return GMERR_OK;
}

Status GmeSimpleRelRollbackInteracTrans(GmeConnT *gmeConn)
{
    SessionT *session = gmeConn->embSession->session;
    Status ret = SeTransRollback(session->seInstance, false);
    if (ret != GMERR_OK) {
        return ret;
    }
    session->isInteractiveTrx = false;
    return GMERR_OK;
}

Status GmeSimpleRelCheckOrCommit(GmeConnT *gmeConn)
{
    EmbSimpleRunCtxT *embCtx = gmeConn->embSession->embSimpleRunCtx;
    SessionT *session = embCtx->session->session;
    NspCtrlT *nspCtrl = embCtx->nspCtrl;
    if (nspCtrl == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(VOS_ERRNO_DB_INVALID_DATABASE, "Incorrect DB when check commit.");
        return VOS_ERRNO_DB_INVALID_DATABASE;
    }
    Status ret = SeOptimisticTrxConflictCheck(session->seInstance);
    if (ret == STATUS_OK_INTER) {
        ret = SeTransCommit(session->seInstance);
        if (ret != STATUS_OK_INTER) {
            DB_LOG_ERROR_UNFOLD(ret, "check success but unable to commit.");
        }
    }
    return ret;
}

void GmeSimpleRelSetNsp(GmeConnT *conn, uint32_t dbId)
{
    EmbSimpleRunCtxT *embCtx = conn->embSession->embSimpleRunCtx;
    embCtx->nspCtrl = SimpleRelGetNspCtrlById(&embCtx->simpleRelMgr->nspCtrlList, dbId, NULL);
}

Status GmeSimpleRelTransCCBegin(GmeConnT *conn, uint32_t dbId)
{
    DB_POINTER(conn);
    EmbSimpleRunCtxT *embCtx = conn->embSession->embSimpleRunCtx;
    SimpRelMgrRLatchAcquire(embCtx);
    NspCtrlT *nspCtrl = SimpleRelGetNspCtrlById(&embCtx->simpleRelMgr->nspCtrlList, dbId, NULL);
    if (nspCtrl == NULL) {
        SimpRelMgrRLatchRelease(embCtx);
        DB_LOG_ERROR_AND_SET_LASTERR(VOS_ERRNO_DB_INVALID_DATABASE, "Incorrect DB ID (%" PRIu32 ").", dbId);
        return VOS_ERRNO_DB_INVALID_DATABASE;
    }

    // GmeSimpleRelTransCCEnd 统一解锁
    DbRWLatchR(&nspCtrl->latch);
    SimprelTrxLockAllTbl(conn, nspCtrl);
    embCtx->nspCtrl = nspCtrl;
    return GMERR_OK;
}

void GmeSimpleRelTransCCEnd(GmeConnT *conn, uint32_t dbId)
{
    // 必须配合GmeSimpleRelTransCCBegin使用，否则可能死锁
    // 必须在GmeSimpleRelEndDiff前释放所有表锁
    DB_POINTER(conn);
    EmbSimpleRunCtxT *embCtx = conn->embSession->embSimpleRunCtx;
    if (embCtx->nspCtrl == NULL || embCtx->nspCtrl->dbId != dbId) {
        DB_LOG_ERROR_AND_SET_LASTERR(VOS_ERRNO_DB_INVALID_DATABASE, "DB ID (%" PRIu32 ") is not match.", dbId);
        DB_ASSERT(false);
        return;
    }
    SimprelTrxUnlockAllTbl(conn, embCtx->nspCtrl);
    DbRWUnlatchR(&embCtx->nspCtrl->latch);
    SimpRelMgrRLatchRelease(embCtx);
    return;
}

DbListT *GmeSimpleGetRedoTblList(GmeConnT *gmeConn)
{
    if (gmeConn == NULL || gmeConn->embSession == NULL) {
        return NULL;
    }

    EmbSimpleRunCtxT *embCtx = gmeConn->embSession->embSimpleRunCtx;
    if (embCtx->diffCtx == NULL) {
        return NULL;
    }
    return &embCtx->diffCtx->relNoList;
}

NspCtrlT *SimpleRelGetNspCtrlByName(DbListT *nspCtrlList, const char *targetNspName, uint32_t *nspIdx)
{
    NspCtrlT *nspCtrl = DbListItem(nspCtrlList, 0);
    for (uint32_t i = 0; i < nspCtrlList->count; i++, nspCtrl++) {
        if (strcmp(nspCtrl->nspName, targetNspName) == 0) {
            if (nspIdx != NULL) {
                *nspIdx = i;
            }
            return nspCtrl;
        }
    }
    return NULL;
}

NspCtrlT *SimpleRelGetNspCtrlById(DbListT *nspCtrlList, const uint32_t nspId, uint32_t *nspIdx)
{
    for (uint32_t i = 0; i < nspCtrlList->count; i++) {
        NspCtrlT *nspCtrl = DbListItem(nspCtrlList, i);
        if (nspCtrl->dbId == nspId) {
            if (nspIdx != NULL) {
                *nspIdx = i;
            }
            return nspCtrl;
        }
    }
    return NULL;
}

Status SimpInitRelDefByLabelCtrl(VlabelCtrlT *vLabelCtrl, DB_REL_DEF_STRU *relDef)
{
    error_t err = memcpy_s(relDef->aucRelName, DB_REL_NAME_LEN, vLabelCtrl->relationName, DB_REL_NAME_LEN);
    if (err != EOK) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_MEMORY_OPERATE_FAILED,
            "Unable to copy the aucRelName memory. copy len is %" PRIu32 ".", DB_REL_NAME_LEN);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    relDef->enTableType = vLabelCtrl->isTempLabel;  // temp 1 normal 0

    relDef->ulMaxSize = vLabelCtrl->originDefInfo->ulMaxSize;
    relDef->pstFldLst = vLabelCtrl->originDefInfo->pstFldLst;
    relDef->pstIdxLst = vLabelCtrl->originDefInfo->pstIdxLst;
    relDef->ulNCols = vLabelCtrl->originDefInfo->ulNCols;
    relDef->ulNIdxs = vLabelCtrl->originDefInfo->usNAllIdxs;  // 建表要加上内部索引数量
    return GMERR_OK;
}

static Status SimpRelLazyCreateVertexLabelInner(
    EmbSimpleRunCtxT *embCtx, VlabelCtrlT *vLabelCtrl, DmVertexLabelT *vertexLabel, DB_REL_DEF_STRU *relDef)
{
    const char *relName = (const char *)relDef->aucRelName;
    // 从catalog获取完整创建的vertexLabel
    DmVertexLabelT *newVertexLabel = NULL;
    Status ret = CataGetVertexLabelById(embCtx->dbInstance, vertexLabel->metaCommon.metaId, &newVertexLabel);
    if (ret != GMERR_OK) {
        (void)CmdDropVertexLabelFromCatalog(vertexLabel);
        CmdDropVertexLabel(embCtx->seRunCtx, vertexLabel);
        DmDestroyVertexLabel(vertexLabel);
        DB_LOG_ERROR(ret, "Unable to get verteLabel from catalog when lazy load, rel name(%s).", relName);
        return ret;
    }
    (void)CataReleaseVertexLabel(newVertexLabel);

    LabelRWLatchT *latch = GetLabelRWLatchPtrById(newVertexLabel->commonInfo->vertexLabelLatchId, embCtx->dbInstance);
    if (latch == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            VOS_ERRNO_DB_FAILURE, "Unable to get vertexlabel rw latch when lazy load, rel name(%s).", relName);
        (void)CataReleaseVertexLabel(newVertexLabel);
        (void)CmdDropVertexLabelFromCatalog(newVertexLabel);
        CmdDropVertexLabel(embCtx->seRunCtx, newVertexLabel);
        return VOS_ERRNO_DB_FAILURE;
    }
    vLabelCtrl->vertexLabel = newVertexLabel;
    vLabelCtrl->latch = latch;
    vLabelCtrl->needLazyLoad = false;
    SimpRelSetCommonAreaPtr(embCtx->nspCtrl, vLabelCtrl->vertexLabel, vLabelCtrl->isUseCommonArea);
    return GMERR_OK;
}

Status SimpRelLazyCreateVertexLabel(EmbSimpleRunCtxT *embCtx, VlabelCtrlT *vLabelCtrl)
{
    DB_REL_DEF_STRU relDef = {0};
    Status ret = SimpInitRelDefByLabelCtrl(vLabelCtrl, &relDef);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 进行懒加载时,embCtx上需要挂载nspCtrl
    NspCtrlT *nspCtrl = embCtx->nspCtrl;
    DmVertexLabelT *vertexLabel = NULL;
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(embCtx->apiMemCtx);
    ret = DmSimpFillShmVertexLabel(
        dbInstance, &vertexLabel, &relDef, nspCtrl->metaId, nspCtrl->isTpcDb, (char *)relDef.aucRelName);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 导入场景不创建容器,导入数据时判断是否使用共享容器
    // 正常建表若表记录长度 > 阈值（pageSize >> DB_COM_AREA_DIV_BITS)才创建容器
    // 若无法建内部索引，放到自有容器
    vLabelCtrl->isUseCommonArea = embCtx->isRestoreDb || SimpleRelCheckIsUseComArea(vLabelCtrl, 1);
    if (!embCtx->isRestoreDb && vLabelCtrl->isUseCommonArea) {
        vLabelCtrl->isUseCommonArea = SimpleRelCheckIsUseComAreaByInitSize(vLabelCtrl);
    }
    vertexLabel->commonInfo->isUseComArea = vLabelCtrl->isUseCommonArea;
    ret = CmdSimpCreateVertexLabel(embCtx->seRunCtx, vertexLabel, !vLabelCtrl->isUseCommonArea);
    if (ret != GMERR_OK) {
        DestroyShmVertexLabel(DmGetCataCache(dbInstance)->shmMemCtx, vertexLabel);
        DB_LOG_ERROR(ret, "Unable to lazy create vertexlabel(%s).", relDef.aucRelName);
        return ret;
    }

    return SimpRelLazyCreateVertexLabelInner(embCtx, vLabelCtrl, vertexLabel, &relDef);
}

Status SimpleRelGetVlabelCtrlByIdSilent(
    DbListT *vLabelCtrlList, const uint32_t relId, uint32_t *tblIdx, VlabelCtrlT **labelCtrl, EmbSimpleRunCtxT *embCtx)
{
    VOS_UINT32 ulMin = 0;
    VOS_UINT32 ulMid;
    VOS_UINT32 ulMax = vLabelCtrlList->count;

    VlabelCtrlT *vLabelCtrl = DbListItem(vLabelCtrlList, 0);
    while (ulMin < ulMax) {
        ulMid = (ulMin + ulMax) >> 1;
        if (vLabelCtrl[ulMid].relationId == relId) {
            if (tblIdx != NULL) {
                *tblIdx = ulMid;
            }
            DbRWSpinWLock(&vLabelCtrl[ulMid].rwLock);
            // 找到vLabelCtrl
            if (vLabelCtrl[ulMid].needLazyLoad && embCtx != NULL) {
                DB_ASSERT(vLabelCtrl[ulMid].vertexLabel == NULL);
                Status ret = SimpRelLazyCreateVertexLabel(embCtx, &vLabelCtrl[ulMid]);
                if (ret != GMERR_OK) {
                    DbRWSpinWUnlock(&vLabelCtrl[ulMid].rwLock);
                    return ret;
                }
            }
            DbRWSpinWUnlock(&vLabelCtrl[ulMid].rwLock);
            *labelCtrl = &vLabelCtrl[ulMid];
            return GMERR_OK;
        } else if (vLabelCtrl[ulMid].relationId > relId) {
            ulMax = ulMid;
        } else {
            ulMin = ulMid + 1;
        }
    }
    return VOS_ERRNO_DB_INVALIDREL;
}

// embCtx非null且vlabelCtrl needLazyLoad为true时会触发建表操作
Status SimpleRelGetVlabelCtrlById(
    DbListT *vLabelCtrlList, const uint32_t relId, uint32_t *tblIdx, VlabelCtrlT **labelCtrl, EmbSimpleRunCtxT *embCtx)
{
    Status ret = SimpleRelGetVlabelCtrlByIdSilent(vLabelCtrlList, relId, tblIdx, labelCtrl, embCtx);
    if (ret == VOS_ERRNO_DB_INVALIDREL) {
        DB_LOG_ERROR_AND_SET_LASTERR(VOS_ERRNO_DB_INVALIDREL, "Relation with id (%" PRIu16 ") does not exist.", relId);
        return VOS_ERRNO_DB_INVALIDREL;
    }
    return ret;
}

VlabelCtrlT *SimpleRelGetVlabelCtrlByName(DbListT *vLabelCtrlList, const char *relName, uint32_t *tblIdx)
{
    VlabelCtrlT *vLabelCtrl = DbListItem(vLabelCtrlList, 0);
    for (uint32_t i = 0; i < vLabelCtrlList->count; i++, vLabelCtrl++) {
        if (strcmp(vLabelCtrl->relationName, relName) == 0) {
            if (tblIdx != NULL) {
                *tblIdx = i;
            }
            return vLabelCtrl;
        }
    }
    return NULL;
}

static Status SetNeedMallocDmValue(DmValueT *dmValue, uint32_t valueType, const void *value, uint32_t size)
{
    // for DB_DATATYPE_STRING, length = strlen + 1
    if (valueType == DB_DATATYPE_STRING) {
        if (SECUREC_UNLIKELY(size != strlen(value))) {
            DB_LOG_ERROR_AND_SET_LASTERR(VOS_ERRNO_DB_INVALIDFLDLEN,
                "input size is not equal type size. input size=%" PRIu32 ", type size=%" PRIu32 ".", size,
                (uint32_t)strlen(value));
            return VOS_ERRNO_DB_INVALIDFLDLEN;
        }
        dmValue->value.constStrAddr = value;
        dmValue->value.length = size + 1;
        return GMERR_OK;
    }

    // DB_DATATYPE_BYTES and DB_DATATYPE_FIXED and DB_DATATYPE_FIXED_STRING
    dmValue->value.constStrAddr = value;
    dmValue->value.length = size;

    return GMERR_OK;
}

static Status CopyDmValue(DmValueT *dmValue, uint32_t valueType, const void *value, uint32_t size, uint32_t expected)
{
    switch (valueType) {
        case DB_DATATYPE_INT8:
        case DB_DATATYPE_UINT8:
            *((uint8_t *)(&dmValue->value)) = *(const uint8_t *)value;
            return GMERR_OK;
        case DB_DATATYPE_INT16:
        case DB_DATATYPE_UINT16:
            *((uint16_t *)(&dmValue->value)) = *(const uint16_t *)value;
            return GMERR_OK;
        case DB_DATATYPE_ENUM:
        case DB_DATATYPE_IDENTITY:
        case DB_DATATYPE_INT32:
        case DB_DATATYPE_UINT32:
            *((uint32_t *)(&dmValue->value)) = *(const uint32_t *)value;
            return GMERR_OK;
        case DB_DATATYPE_INT64:
        case DB_DATATYPE_UINT64:
            *((uint64_t *)(&dmValue->value)) = *(const uint64_t *)value;
            return GMERR_OK;
        default: {
            errno_t err = memcpy_s(&dmValue->value, expected, value, size);
            if (SECUREC_UNLIKELY(err != EOK)) {
                DB_LOG_ERROR_AND_SET_LASTERR(
                    VOS_ERRNO_DB_MEMCPY_FAILURE, "Memory copy worthless when set dm value, return value is %d", err);
                return VOS_ERRNO_DB_MEMCPY_FAILURE;
            }
            break;
        }
    }
    return GMERR_OK;
}

Status EmbSetDmValue(DmValueT *dmValue, uint32_t valueType, const void *value, uint32_t size, uint16_t typeId)
{
    if (SECUREC_UNLIKELY(valueType >= DB_DATATYPE_UNDEFINED)) {
        DB_LOG_ERROR_AND_SET_LASTERR(VOS_ERRNO_DB_INVALIDDATATYPE, "type is not valid, type=%" PRIu32 ".", valueType);
        return VOS_ERRNO_DB_INVALIDDATATYPE;
    }

    if (SECUREC_UNLIKELY(
            valueType == DB_DATATYPE_NULL || value == NULL || (valueType == DB_DATATYPE_BYTES && size == 0))) {
        dmValue->type = DB_DATATYPE_NULL;
        return GMERR_OK;
    }

    dmValue->type = valueType;
    // 自定义数据类型ID，无效为DM_INVALID_CUSTOM_ID
    dmValue->value.beginPos = typeId;

    if (SECUREC_UNLIKELY(DM_TYPE_NEED_MALLOC(valueType))) {
        return SetNeedMallocDmValue(dmValue, valueType, value, size);
    }

    // for non DM_TYPE_NEED_MALLOC types, caller should check size and set value
    uint32_t expected = DmGetBasicDataTypeLength(valueType);
    if (SECUREC_UNLIKELY(size != expected)) {
        DB_LOG_ERROR_AND_SET_LASTERR(VOS_ERRNO_DB_INVALIDFLDLEN,
            "size is not equal expected size. size=%" PRIu32 ", expected size=%" PRIu32 ".", size, expected);
        return VOS_ERRNO_DB_INVALIDFLDLEN;
    }

    Status ret = CopyDmValue(dmValue, valueType, value, size, expected);
    if (ret != GMERR_OK) {
        return ret;
    }

    // valid partition range is [0, DM_MAX_PARTITION_ID)
    bool ok = (valueType != DB_DATATYPE_PARTITION || dmValue->value.partitionValue < DM_MAX_PARTITION_ID);
    if (!ok) {
        DB_LOG_ERROR_AND_SET_LASTERR(VOS_ERRNO_DB_INVALIDDATATYPE, "data type is not valid.");
        return VOS_ERRNO_DB_INVALIDDATATYPE;
    }
    return GMERR_OK;
}

inline static bool IsIndexSupportOpTye(DB_OPTYPE_ENUM opType)
{
    return ((opType == DB_OP_LESS) || (opType == DB_OP_LESSEQUAL) || (opType == DB_OP_LARGER) ||
            (opType == DB_OP_LARGEREQUAL) || (opType == DB_OP_MAX_LESS) || (opType == DB_OP_MAX_LESS_EQUAL) ||
            (opType == DB_OP_MIN_LARGER) || (opType == DB_OP_MIN_LARGER_EQUAL));
}

inline static void SimpleRelCondFilter(DB_COND_STRU *pstCond, uint8_t *condFilter, uint32_t arrLen)
{
    for (uint8_t condId = 0; condId < pstCond->usCondNum && condId < arrLen; condId++) {
        DB_OPTYPE_ENUM opType = pstCond->aCond[condId].enOp;
        uint8_t fldId = pstCond->aCond[condId].ucFieldId;

        if (opType == DB_OP_EQUAL) {
            condFilter[fldId] = condId;
        } else if (IsIndexSupportOpTye(opType) && (condFilter[fldId] == DB_INVALID_UINT8)) {
            condFilter[fldId] = condId;
        }
    }
}

static uint32_t SimpRelSelectIndexInner(VlabelCtrlT *vLabelCtrl, DB_COND_STRU *pstCond,
    SimpRelSelectIdxStruT *bestIdxItems, bool isSimpleSelect, bool isTpcDb)
{
    SimpRelSelectIdxStruT curIdxItems;
    uint8_t condFilter[DB_FLDOFREL_MAX];
    (void)memset_s(condFilter, DB_FLDOFREL_MAX, DB_INVALID_UINT8, DB_FLDOFREL_MAX);
    /*  条件过滤规则：同一个字段有多个条件，仅保留第一个支持索引查询条件的下标或EQ条件下标(EQ优先级最高)
     *  condFilter数组下标为字段号(FieldId)，值对应条件编号(condId)，0xff代表该下标对应的字段没有设置条件  */
    SimpleRelCondFilter(pstCond, condFilter, DB_FLDOFREL_MAX);

    DmVlIndexLabelT *secIndex = vLabelCtrl->vertexLabel->metaVertexLabel->secIndexes;
    uint32_t bestIdxId = DB_INVALID_UINT8;
    for (uint32_t i = 0; i < vLabelCtrl->originDefInfo->ulNIdxs; i++, secIndex++) {
        if (SECUREC_UNLIKELY(
                isSimpleSelect && vLabelCtrl->originDefInfo->pstIdxLst[i].enIndexType == DBDDL_INDEXTYPE_HASH)) {
            continue;
        }
        uint32_t fldNumOfIdx = secIndex->propeNum;
        // 初始化当前匹配数目
        curIdxItems.condNum = 0;

        for (uint32_t j = 0; j < fldNumOfIdx; j++) {
            uint32_t fldId = secIndex->propIds[j];
            // 当前索引字段上没有设置条件，跳出匹配
            if (condFilter[fldId] == DB_INVALID_UINT8) {
                break;
            }

            // 添加当前条件到索引条件中
            curIdxItems.condNo[curIdxItems.condNum] = condFilter[fldId];
            curIdxItems.condNum++;

            // 仅记录前缀为等值的条件及第一个非等值的条件，碰到第一个非等值条件则跳出匹配
            if (pstCond->aCond[condFilter[fldId]].enOp != DB_OP_EQUAL) {
                break;
            }
        }

        // 适配TPC_DB V1 hash唯一索引添加额外字段
        if (SECUREC_UNLIKELY(vLabelCtrl->originDefInfo->pstIdxLst[i].enIndexType == DBDDL_INDEXTYPE_HASH)) {
            uint32_t idxFieldCnt =
                ((secIndex->idxLabelBase.indexConstraint == UNIQUE) && isTpcDb) ? (fldNumOfIdx + 1) : fldNumOfIdx;
            if (idxFieldCnt == curIdxItems.condNum &&
                pstCond->aCond[curIdxItems.condNo[curIdxItems.condNum - 1]].enOp == DB_OP_EQUAL) {
                *bestIdxItems = curIdxItems;
                bestIdxId = i;
                break;
            }
            continue;
        }

        // 当前匹配到的索引条件数量大于之前匹配到的最好的索引条件数量，则最佳索引为当前索引
        if (curIdxItems.condNum > bestIdxItems->condNum) {
            *bestIdxItems = curIdxItems;
            bestIdxId = i;
        }
    }

    return bestIdxId;
}

bool IsSameCond(DB_COND_STRU *pstCond, IdxMatchStruT *idxMatchStru)
{
    if (pstCond->usCondNum != idxMatchStru->condNum) {
        return false;
    }
    for (uint32_t i = 0; i < pstCond->usCondNum; i++) {
        if (pstCond->aCond[i].ucFieldId != idxMatchStru->ucFieldId[i]) {
            return false;
        }
        if (pstCond->aCond[i].enOp != idxMatchStru->enOp[i]) {
            return false;
        }
    }
    return true;
}

void SetMatchStru(DB_COND_STRU *pstCond, IdxMatchStruT *idxMatchStru)
{
    idxMatchStru->condNum = pstCond->usCondNum;
    for (uint32_t i = 0; i < pstCond->usCondNum; i++) {
        idxMatchStru->ucFieldId[i] = pstCond->aCond[i].ucFieldId;
        idxMatchStru->enOp[i] = pstCond->aCond[i].enOp;
    }
}

void SimpleRelSelectIndex(
    VlabelCtrlT *vLabelCtrl, DB_COND_STRU *pstCond, IdxMatchStruT *idxMatchStru, bool isSimpleSelect, bool isTpcDb)
{
    uint32_t indexNum = SimpGetLabelIdxNum(vLabelCtrl);
    if (indexNum == 0 || pstCond->usCondNum == 0) {
        idxMatchStru->isMatch = false;
        idxMatchStru->labelId = DB_INVALID_UINT32;
        idxMatchStru->idxCondNum = 0;
        (void)memset_s(idxMatchStru->idxConds, DB_COND_MAX, DB_INVALID_UINT8, DB_COND_MAX);
        return;
    }

    if (!isSimpleSelect && idxMatchStru->labelId == vLabelCtrl->vertexLabel->metaCommon.metaId &&
        IsSameCond(pstCond, idxMatchStru)) {
        return;
    }
    // idxConds记录索引条件，下标为条件id，值为字段id，0xff则该条件为非索引条件
    (void)memset_s(idxMatchStru->idxConds, DB_COND_MAX, DB_INVALID_UINT8, DB_COND_MAX);
    SimpRelSelectIdxStruT bestIdxItems = {0, {0}};
    uint32_t bestIdxId = SimpRelSelectIndexInner(vLabelCtrl, pstCond, &bestIdxItems, isSimpleSelect, isTpcDb);
    if (bestIdxId == DB_INVALID_UINT8) {
        idxMatchStru->isMatch = false;
        idxMatchStru->labelId = vLabelCtrl->vertexLabel->metaCommon.metaId;
        idxMatchStru->idxCondNum = 0;
        SetMatchStru(pstCond, idxMatchStru);
        return;
    }

    idxMatchStru->isMatch = true;
    // 记录索引上的条件id, （下标为条件id，值不为0xff即代表是索引条件）
    for (uint8_t condId = 0; condId < bestIdxItems.condNum; condId++) {
        idxMatchStru->idxConds[bestIdxItems.condNo[condId]] = condId;
    }
    idxMatchStru->idxCondNum = bestIdxItems.condNum;
    idxMatchStru->idxId = bestIdxId;
    idxMatchStru->labelId = vLabelCtrl->vertexLabel->metaCommon.metaId;
    idxMatchStru->isUseInterIdx = false;
    SetMatchStru(pstCond, idxMatchStru);
}

static void SimpleRelInitComplexOp(
    DB_COND_STRU *cond, EmbSimpRelComplexOpStruT *complexOpStru, DmVertexLabelT *vertexLabel)
{
    if (cond == NULL) {
        return;
    }
    DmPropertySchemaT *properties = SimpRelGetPropesFromVertexLabel(vertexLabel);
    for (uint32_t i = 0; i < cond->usCondNum; i++) {
        switch (cond->aCond[i].enOp) {
            case DB_OP_MAX_PREFIX12:   /* fall-through */
            case DB_OP_MAX_PREFIX21:   /* fall-through */
            case DB_OP_MAX_LESS_EQUAL: /* fall-through */
            case DB_OP_MAX_LESS:       /* fall-through */
            case DB_OP_MAX_POSTFIX21: {
                complexOpStru->sortType[complexOpStru->complexOpNum] = DB_SORTTYPE_DESCEND;
                complexOpStru->fldOffset[complexOpStru->complexOpNum] = properties[cond->aCond[i].ucFieldId].offset;
                complexOpStru->propeDefLen[complexOpStru->complexOpNum] =
                    (uint32_t)properties[cond->aCond[i].ucFieldId].propeDefLen;
                complexOpStru->v1Type[complexOpStru->complexOpNum] = properties[cond->aCond[i].ucFieldId].customTypeId;
                complexOpStru->complexOpNum++;
                break;
            }
            case DB_OP_MIN_PREFIX12: /* fall-through */
            case DB_OP_MIN_PREFIX21: /* fall-through */
            case DB_OP_MIN_LARGER:   /* fall-through */
            case DB_OP_MIN_LARGER_EQUAL: {
                complexOpStru->sortType[complexOpStru->complexOpNum] = DB_SORTTYPE_ASCEND;
                complexOpStru->fldOffset[complexOpStru->complexOpNum] = properties[cond->aCond[i].ucFieldId].offset;
                complexOpStru->propeDefLen[complexOpStru->complexOpNum] =
                    (uint32_t)properties[cond->aCond[i].ucFieldId].propeDefLen;
                complexOpStru->v1Type[complexOpStru->complexOpNum] = properties[cond->aCond[i].ucFieldId].customTypeId;
                complexOpStru->complexOpNum++;
                break;
            }
            default:
                break;
        }
    }
}

ALWAYS_INLINE_C static GME_CMPTYPE_ENUM SimpleRelSetCondCmpOp(DB_OPTYPE_ENUM enOp)
{
    switch (enOp) {
        case DB_OP_MAX_PREFIX12:
        case DB_OP_MIN_PREFIX12:
            return GME_CMP_PREFIX12;
        case DB_OP_MAX_PREFIX21:
        case DB_OP_MIN_PREFIX21:
            return GME_CMP_PREFIX21;
        case DB_OP_MAX_LESS:
            return GME_CMP_LESS;
        case DB_OP_MAX_LESS_EQUAL:
            return GME_CMP_LESSEQUAL;
        case DB_OP_MIN_LARGER:
            return GME_CMP_LARGER;
        case DB_OP_MIN_LARGER_EQUAL:
            return GME_CMP_LARGEREQUAL;
        case DB_OP_MAX_POSTFIX21:
            return GME_CMP_POSTFIX21;
        default:
            return (GME_CMPTYPE_ENUM)enOp;
    }
    return (GME_CMPTYPE_ENUM)enOp;
}

ALWAYS_INLINE_C void SimpleRelInitOneCond(
    EmbSimpleCondItemT *queryCond, DmVertexLabelT *vertexLabel, DB_CONDITEM_STRU *cond, uint32_t condIdx)
{
    DmPropertySchemaT *properties = SimpRelGetPropesFromVertexLabel(vertexLabel);
    queryCond[condIdx].enOp = SimpleRelSetCondCmpOp(cond->enOp);
    queryCond[condIdx].propId = cond->ucFieldId;
    queryCond[condIdx].fldOffset = properties[cond->ucFieldId].offset;
    queryCond[condIdx].propeMaxLen = (uint32_t)properties[cond->ucFieldId].propeDefLen;
    queryCond[condIdx].v1Type = properties[cond->ucFieldId].customTypeId;
    queryCond[condIdx].v5Type = properties[cond->ucFieldId].dataType;
    if (SECUREC_LIKELY(properties[cond->ucFieldId].size <= DB_ELELEN_MAX)) {
        queryCond[condIdx].value = cond->aucValue;
    } else {
        *(VOS_UINTPTR *)(&queryCond[condIdx].value) = *(VOS_UINTPTR *)cond->aucValue;
    }
    queryCond[condIdx].isCommonAreaCond = false;
}

static bool SimpRelSetCondCmpOpStatus(EmbSimpleQueryCtxT *queryCtx, SimpRelHandleCondT *handleCond)
{
    if (handleCond->isCmpOpOpposed) {
        return true;
    }

    if (queryCtx->idxResumeCondNum == 0) {
        return false;
    }

    // 此时idxConds为额外条件(比较符只会是GME_CMP_LESSEQUAL与GME_CMP_LARGEREQUAL)， idxResumeConds为初始条件
    bool isAscend = queryCtx->idxConds[0].enOp <= GME_CMP_LESSEQUAL ? false : true;
    for (uint32_t i = 0; i < queryCtx->idxResumeCondNum; i++) {
        if ((queryCtx->idxResumeConds[i].enOp == GME_CMP_EQUAL) ||
            (isAscend && queryCtx->idxResumeConds[i].enOp <= GME_CMP_LESSEQUAL) ||
            (!isAscend && queryCtx->idxResumeConds[i].enOp >= GME_CMP_LESSEQUAL)) {
            handleCond->isCmpOpOpposed = true;
            return true;
        }
    }

    return false;
}

static void SimpRelSetFetchSelectQueryConds(
    DmVertexLabelT *vertexLabel, SimpRelScanCtxT *scanCtx, IdxMatchStruT *idxMatchStru, EmbSimpleQueryCtxT *queryCtx)
{
    if (queryCtx->isFetchTopo) {  // topo复杂(min/max)查询
        SimpleRelInitComplexOp(scanCtx->cond, &queryCtx->complexOpStru, vertexLabel);
    }
    queryCtx->filterConds = scanCtx->handleCond->filterConds;
    queryCtx->filterCondNum = scanCtx->handleCond->filterCondNum;
    queryCtx->isFirstFetch = scanCtx->handleCond->isFirstFetch;
    if (scanCtx->handleCond->isMatchedIdx) {
        if (!scanCtx->handleCond->isFirstFetch) {
            EmbSimpleCondItemT *resumeIdxCond = scanCtx->handleCond->resumeConds;
            for (uint32_t i = 0; i < scanCtx->handleCond->idxResumeCondNum; i++, resumeIdxCond++) {
                // 其他参数不会改变
                resumeIdxCond->value = resumeIdxCond->valueForHandl;
            }
            // 把额外条件放在前面查询，加速获取索引本次查询起始位置
            queryCtx->idxCondNum = scanCtx->handleCond->idxResumeCondNum;
            queryCtx->idxConds = scanCtx->handleCond->resumeConds;
            queryCtx->idxResumeCondNum = scanCtx->handleCond->idxCondNum;
            queryCtx->idxResumeConds = scanCtx->handleCond->idxConds;
            queryCtx->isCmpOpOpposed = SimpRelSetCondCmpOpStatus(queryCtx, scanCtx->handleCond);
        } else {
            // 第一次fetch使用原条件
            queryCtx->idxCondNum = scanCtx->handleCond->idxCondNum;
            queryCtx->idxConds = scanCtx->handleCond->idxConds;
            queryCtx->idxResumeCondNum = 0;
            queryCtx->idxResumeConds = scanCtx->handleCond->resumeConds;
        }
        return;
    }

    // 非匹配索引fetch查询
    (void)memcpy_s(
        &queryCtx->oldResumeAddr, sizeof(HpTupleCombineAddrT), scanCtx->hpBeginAddr, sizeof(HpTupleCombineAddrT));
}

ALWAYS_INLINE_C bool isSimpleType(uint16_t customTypeId)
{
    switch (customTypeId) {
        case DBT_FLOAT:
        case DBT_DOUBLE:
        case DBT_UINT8:
        case DBT_UINT16:
        case DBT_UINT32:
        case DBT_SINT8:
        case DBT_SINT16:
        case DBT_SINT32:
        case DBT_INT64:
        case DBT_UINT64:
            return true;
        default:
            return false;
    }
}

ALWAYS_INLINE_C Status SimpleRelAllocConds(
    DbMemCtxT *memCtx, SimpRelScanCtxT *scanCtx, IdxMatchStruT *idxMatchStru, EmbSimpleQueryCtxT *queryCtx)
{
    uint32_t allCondCnt = scanCtx->cond->usCondNum + queryCtx->relIdCondCnt;
    if (allCondCnt == 0) {
        return GMERR_OK;
    }
    uint32_t filterCondNum = allCondCnt - idxMatchStru->idxCondNum;
    if (queryCtx->filterConds == NULL) {
        uint32_t condtionsSize = allCondCnt * sizeof(EmbSimpleCondItemT);
        void *conditions = DbDynMemCtxAlloc(memCtx, condtionsSize);
        if (conditions == NULL) {
            DB_LOG_ERROR_AND_SET_LASTERR(VOS_ERRNO_DB_MEMALLOCFAILURE, "Alloc memory for condtions worthless.");
            return VOS_ERRNO_DB_MEMALLOCFAILURE;
        }
        queryCtx->filterConds = (EmbSimpleCondItemT *)conditions;
        queryCtx->idxConds = (EmbSimpleCondItemT *)conditions + filterCondNum;
    } else {
        queryCtx->idxConds = queryCtx->filterConds + filterCondNum;
    }
    return GMERR_OK;
}

void SimpleRelInitCommonAreaFilterCond(
    EmbSimpleCondItemT *filterCond, VlabelCtrlT *vLabelCtrl, bool isMatchIdx, uint16_t *filterCondNum, bool isFetch)
{
    if (!vLabelCtrl->isUseCommonArea || isMatchIdx) {
        return;
    }

    // 使用共享空间的表额外添加过滤查询条件，条件为（== seMetaId)
    filterCond->enOp = GME_CMP_EQUAL;
    filterCond->propId = DB_INVALID_UINT8;
    filterCond->fldOffset = SimpRelGetVlMaxRecLen(vLabelCtrl->vertexLabel);
    filterCond->propeMaxLen = DB_V1_TBLID_LEN;
    filterCond->v1Type = DBT_UINT16;
    filterCond->v5Type = DB_DATATYPE_UINT16;
    if (isFetch) {
        (void)memcpy_s(filterCond->value, DB_V1_TBLID_LEN, &vLabelCtrl->seMetaId, DB_V1_TBLID_LEN);
    } else {
        filterCond->value = (void *)&vLabelCtrl->seMetaId;
    }
    filterCond->isCommonAreaCond = true;
    (*filterCondNum)++;
    return;
}

Status SimpleRelInitQueryConds(DbMemCtxT *memCtx, VlabelCtrlT *vLabelCtrl, SimpRelScanCtxT *scanCtx,
    IdxMatchStruT *idxMatchStru, EmbSimpleQueryCtxT *queryCtx)
{
    DmVertexLabelT *vertexLabel = vLabelCtrl->vertexLabel;
    if (scanCtx->isFetchSelect) {
        SimpRelSetFetchSelectQueryConds(vertexLabel, scanCtx, idxMatchStru, queryCtx);
        return GMERR_OK;
    }
    Status ret = SimpleRelAllocConds(memCtx, scanCtx, idxMatchStru, queryCtx);
    if (SECUREC_LIKELY(ret != GMERR_OK)) {
        return ret;
    }

    SimpleRelInitCommonAreaFilterCond(
        &queryCtx->filterConds[0], vLabelCtrl, idxMatchStru->isMatch, &queryCtx->filterCondNum, false);
    SimpleRelInitComplexOp(scanCtx->cond, &queryCtx->complexOpStru, vertexLabel);
    DB_CONDITEM_STRU *pCond = scanCtx->cond->aCond;
    DmPropertySchemaT *properties = vertexLabel->metaVertexLabel->schema->properties;
    VOS_UINT16 condAll = scanCtx->cond->usCondNum;
    for (uint32_t i = 0; i < condAll; i++, pCond++) {
        // 非索引条件
        if (idxMatchStru->idxConds[i] == DB_INVALID_UINT8) {
            // 复杂排序条件场景，过滤条件有顺序依赖
            if (queryCtx->complexOpStru.complexOpNum > 0 || isSimpleType(properties[pCond->ucFieldId].customTypeId)) {
                SimpleRelInitOneCond(queryCtx->filterConds, vertexLabel, pCond, queryCtx->filterCondNum);
                queryCtx->filterCondNum++;
            }
        } else {
            SimpleRelInitOneCond(queryCtx->idxConds, vertexLabel, pCond, idxMatchStru->idxConds[i]);
            queryCtx->idxCondNum++;
        }
    }
    pCond = scanCtx->cond->aCond;
    for (uint32_t i = 0;
         i < condAll && (queryCtx->filterCondNum + queryCtx->idxCondNum) < (condAll + queryCtx->relIdCondCnt);
         i++, pCond++) {
        // 非索引条件
        if (idxMatchStru->idxConds[i] == DB_INVALID_UINT8 && !isSimpleType(properties[pCond->ucFieldId].customTypeId)) {
            SimpleRelInitOneCond(queryCtx->filterConds, vertexLabel, pCond, queryCtx->filterCondNum);
            queryCtx->filterCondNum++;
        }
    }
    return GMERR_OK;
}

static void SimpleRelInitActRecLen(SimpRelScanCtxT *scanCtx, DmSchemaT *schema, EmbSimpleQueryCtxT *queryCtx)
{
    const DmPropertySchemaT *properties = schema->properties;
    uint32_t realSize = 0;
    if (scanCtx->fldFilter->ucFieldNum == DB_FIELD_ALL) {
        realSize = schema->maxRecLen;
    } else {
        for (uint32_t i = 0; i < scanCtx->fldFilter->ucFieldNum; i++) {
            realSize += properties[scanCtx->fldFilter->aucField[i]].size;
        }
    }
    queryCtx->scanUserData.actRecLen = realSize;
}

static void SimpleRelInitQueryResultSet(SimpRelScanCtxT *scanCtx, DmSchemaT *schema, EmbSimpleQueryCtxT *queryCtx)
{
    DB_BUF_STRU *stBufStru = (DB_BUF_STRU *)scanCtx->dataStru;
    SimpleRelInitActRecLen(scanCtx, schema, queryCtx);
    // 初始化0参数已在SimpleRelResetQueryCtx中设置
    queryCtx->scanUserData.isSelectAll = (stBufStru->ulRecNum == DB_SELECT_ALL);
    queryCtx->scanUserData.limitBufLen = stBufStru->ulBufLen;
    queryCtx->scanUserData.pfGetBuf = scanCtx->pfGetBuf;
    queryCtx->scanUserData.pGetBufArgs = scanCtx->pGetBufArgs;
    queryCtx->scanUserData.queryCtx = queryCtx;
    queryCtx->scanUserData.curBufStru = stBufStru;
    queryCtx->resultSet = stBufStru;

    if (SECUREC_LIKELY(stBufStru->ulRecNum != 0)) {
        queryCtx->scanUserData.limitCnt = stBufStru->ulRecNum;
        return;
    }

    // fetch场景如果是给定数量为0，查所有
    if (SECUREC_LIKELY(!scanCtx->isSimpleSelect) && !scanCtx->isFetchSelect) {
        // 查询时，设置的限制记录数为0且不是TPC_SimpleSelectAllRec接口则为判断该查询条件下记录是否存在
        queryCtx->scanUserData.limitCnt = DB_CHECK_REC_EXIST;  // 变为check record exist
        queryCtx->needCopyResult = false;
        queryCtx->idxStoreDataFunc = IdxScanGetMatchingCount;
    } else {
        queryCtx->scanUserData.limitCnt = DB_SELECT_ALL;
    }
}

static void SimpleRelInitTopoQueryResultSet(SimpRelScanCtxT *scanCtx, DmSchemaT *schema, EmbSimpleQueryCtxT *queryCtx)
{
    queryCtx->scanUserData.actRecLen = schema->maxRecLen;
    if (scanCtx->fetchNum != 0) {
        // 查询时，设置的限制记录数为0则为判断该查询条件下记录是否存在
        queryCtx->scanUserData.limitCnt = scanCtx->fetchNum;  // 变为check record exist
    } else {
        queryCtx->scanUserData.limitCnt = DB_SELECT_ALL;
    }
    queryCtx->scanUserData.bufOffset = 0;
    queryCtx->scanUserData.isBufNotEnough = false;
    queryCtx->scanUserData.scanFinish = false;
    queryCtx->scanUserData.limitBufLen = 0;
    queryCtx->scanUserData.matchedCnt = DbListGetItemCnt(&((SimpRelSelectTopoResultT *)scanCtx->dataStru)->recList);
    queryCtx->scanUserData.curBufStru = scanCtx->dataStru;
    queryCtx->scanUserData.queryCtx = queryCtx;
    queryCtx->resultSet = scanCtx->dataStru;
}

static void SimpleRelInitQuerySortItems(
    DbMemCtxT *memCtx, SimpRelScanCtxT *scanCtx, EmbSimpleQueryCtxT *queryCtx, DmVertexLabelT *vertexLabel)
{
    uint32_t sortNum = scanCtx->sort->ucSortNum;
    DmPropertySchemaT *properties = SimpRelGetPropesFromVertexLabel(vertexLabel);
    queryCtx->sortItem.sortType = scanCtx->sort->enSortType;
    if (queryCtx->sortItem.sortFiledAllocedNum < sortNum) {
        if (queryCtx->sortItem.sortFiledAllocedNum != 0) {
            DbDynMemCtxFree(memCtx, queryCtx->sortItem.fldOffset);
            DbDynMemCtxFree(memCtx, queryCtx->sortItem.propeMaxLen);
            DbDynMemCtxFree(memCtx, queryCtx->sortItem.v1Type);
        }
        queryCtx->sortItem.sortFiledAllocedNum = sortNum;
        queryCtx->sortItem.fldOffset = (uint32_t *)DbDynMemCtxAlloc(memCtx, sizeof(uint32_t) * sortNum);
        queryCtx->sortItem.propeMaxLen = (uint32_t *)DbDynMemCtxAlloc(memCtx, sizeof(uint32_t) * sortNum);
        queryCtx->sortItem.v1Type = (uint32_t *)DbDynMemCtxAlloc(memCtx, sizeof(DB_DATATYPE_ENUM_V1) * sortNum);
    }
    for (uint32_t i = 0; i < sortNum; i++) {
        uint32_t fldOffset = properties[scanCtx->sort->pSortFields[i]].offset;
        queryCtx->sortItem.propeMaxLen[i] = properties[scanCtx->sort->pSortFields[i]].propeDefLen;
        queryCtx->sortItem.v1Type[i] = properties[scanCtx->sort->pSortFields[i]].customTypeId;
        queryCtx->sortItem.fldOffset[i] = fldOffset;
    }

    EmbSimpleSortItemT *sortItem = &queryCtx->sortItem;
    sortItem->matchedCnt = 0;
    sortItem->memCtx = memCtx;
    sortItem->curSortFiledNum = sortNum;
    sortItem->isBigObjSort = !HeapCheckIsNormalRowByBufSize(SimpRelGetVlMaxRecLen(vertexLabel));
}

Status SimpleRelInitQueryFldFilter(DbMemCtxT *memCtx, SimpRelScanCtxT *scanCtx, EmbSimpleQueryCtxT *queryCtx)
{
    if (scanCtx->fldFilter->ucFieldNum == 0) {
        queryCtx->projectionNum = 0;
        queryCtx->needCopyResult = false;
        queryCtx->isZeroFldFilter = true;
        return GMERR_OK;
    }
    if (queryCtx->projectionPropId == NULL) {
        queryCtx->projectionPropId = DbDynMemCtxAlloc(memCtx, sizeof(uint32_t) * scanCtx->fldFilter->ucFieldNum);
        if (queryCtx->projectionPropId == NULL) {
            DB_LOG_ERROR_AND_SET_LASTERR(
                VOS_ERRNO_DB_MEMALLOCFAILURE, "Unable to alloc dynamic memctx for projection item.");
            return VOS_ERRNO_DB_MEMALLOCFAILURE;
        }
    }

    queryCtx->projectionNum = scanCtx->fldFilter->ucFieldNum;
    for (uint32_t i = 0; i < queryCtx->projectionNum; i++) {
        queryCtx->projectionPropId[i] = scanCtx->fldFilter->aucField[i];
    }
    return GMERR_OK;
}

ALWAYS_INLINE_C static void SimpRelSetMatchCntQueryCtxAndUserData(
    EmbSimpleQueryCtxT *queryCtx, SimpRelScanCtxT *scanCtx, EmbSimpRelComplexOpStruT *complexOpStru)
{
    queryCtx->matchedCnt = scanCtx->matchedCnt;
    queryCtx->needCopyResult = false;
    if (complexOpStru->complexOpNum == 0) {
        queryCtx->idxStoreDataFunc = IdxScanGetMatchingCount;
    }

    if (scanCtx->isCheckRecExist) {
        queryCtx->scanUserData.limitCnt = DB_CHECK_REC_EXIST;
        queryCtx->scanUserData.isSelectAll = false;
        queryCtx->idxStoreDataFunc = IdxScanGetMatchingCount;
    } else {
        queryCtx->scanUserData.limitCnt = DB_SELECT_ALL;
        queryCtx->scanUserData.isSelectAll = true;
    }
    queryCtx->scanUserData.queryCtx = queryCtx;
}

Status SimpleRelInitQueryCtx(DbMemCtxT *memCtx, VlabelCtrlT *vLabelCtrl, IdxMatchStruT *idxMatchStru,
    SimpRelScanCtxT *scanCtx, EmbSimpleQueryCtxT *queryCtx)
{
    Status ret = GMERR_OK;
    queryCtx->needCopyResult = true;
    queryCtx->needAddIdxScanCnt = idxMatchStru->isMatch && !idxMatchStru->isUseInterIdx;
    queryCtx->idxStoreDataFunc = IdxScanMakeResultSet;  // 默认设置
    queryCtx->isFetchTopo = scanCtx->isFetchTopo;

    if (scanCtx->isFetchSelect || scanCtx->cond->usCondNum != 0 || vLabelCtrl->isUseCommonArea) {
        if ((ret = SimpleRelInitQueryConds(memCtx, vLabelCtrl, scanCtx, idxMatchStru, queryCtx)) != GMERR_OK) {
            return ret;
        }
    }

    if (scanCtx->sort->ucSortNum > 0) {
        if (scanCtx->isFetchSelect && !scanCtx->isFetchTopo) {
            queryCtx->sortItem.sortType = scanCtx->sort->enSortType;
        } else {
            SimpleRelInitQuerySortItems(memCtx, scanCtx, queryCtx, vLabelCtrl->vertexLabel);
            queryCtx->idxStoreDataFunc = IdxScanMakeSortItem;
        }
    }

    if (scanCtx->matchedCnt != NULL || scanCtx->isCheckRecExist) {
        SimpRelSetMatchCntQueryCtxAndUserData(queryCtx, scanCtx, &queryCtx->complexOpStru);
        return GMERR_OK;
    }

    if (scanCtx->fldFilter != NULL && scanCtx->fldFilter->ucFieldNum != DB_FIELD_ALL) {
        if ((ret = SimpleRelInitQueryFldFilter(memCtx, scanCtx, queryCtx)) != GMERR_OK) {
            return ret;
        }
    }

    if (scanCtx->isFetchSelect) {
        queryCtx->isFetchSelect = scanCtx->isFetchSelect;
        queryCtx->resumeDataInfo = scanCtx->resumeDataInfo;
        queryCtx->resumeAddr = scanCtx->hpBeginAddr;
        queryCtx->idxStoreDataFunc = scanCtx->isFetchTopo && queryCtx->complexOpStru.complexOpNum == 0 ?
                                         IdxScanMakeTopoResultSet :
                                         IdxScanMakeResultSet;
    }

    if (scanCtx->dataStru != NULL) {
        scanCtx->isFetchTopo ?
            SimpleRelInitTopoQueryResultSet(scanCtx, vLabelCtrl->vertexLabel->metaVertexLabel->schema, queryCtx) :
            SimpleRelInitQueryResultSet(scanCtx, vLabelCtrl->vertexLabel->metaVertexLabel->schema, queryCtx);
    }
    return GMERR_OK;
}

void QueryCtxFreeConds(DbMemCtxT *memCtx, EmbSimpleQueryCtxT *queryCtx)
{
    if (queryCtx->filterConds == NULL) {
        return;
    }
    DbDynMemCtxFree(memCtx, queryCtx->filterConds);
    queryCtx->filterCondNum = 0;
    queryCtx->filterConds = NULL;
    queryCtx->idxCondNum = 0;
    queryCtx->idxConds = NULL;
}

void QueryCtxFreeSortItem(EmbSimpleSortItemT *sortItem)
{
    if (sortItem->recArrayAllocedNum != 0) {
        if (SECUREC_UNLIKELY(sortItem->isBigObjSort)) {
            for (uint32_t i = 0; i < sortItem->matchedCnt; i++) {
                DbDynMemCtxFree(sortItem->memCtx, sortItem->recArray[i].buf);
            }
        }
        sortItem->recArrayAllocedNum = 0;
        DbDynMemCtxFree(sortItem->memCtx, sortItem->recArray);
    }
    if (sortItem->sortFiledAllocedNum != 0) {
        sortItem->sortFiledAllocedNum = 0;
        DbDynMemCtxFree(sortItem->memCtx, sortItem->fldOffset);
        DbDynMemCtxFree(sortItem->memCtx, sortItem->propeMaxLen);
        DbDynMemCtxFree(sortItem->memCtx, sortItem->v1Type);
    }
    sortItem->recArray = NULL;
    sortItem->fldOffset = NULL;
    sortItem->propeMaxLen = NULL;
    sortItem->v1Type = NULL;
    sortItem->memCtx = NULL;
}

void QueryCtxFreeProjectionItem(DbMemCtxT *memCtx, EmbSimpleQueryCtxT *queryCtx)
{
    if (queryCtx->projectionPropId == NULL) {
        return;
    }
    DbDynMemCtxFree(memCtx, queryCtx->projectionPropId);
    queryCtx->projectionNum = 0;
    queryCtx->projectionPropId = NULL;
}

ALWAYS_INLINE_C void QueryCtxFreeComplexOpRecArray(DbMemCtxT *memCtx, EmbSimpleQueryCtxT *queryCtx)
{
    if (SECUREC_LIKELY(queryCtx->scanUserData.recArrayAllocedNum == 0)) {
        return;
    }

    if (SECUREC_UNLIKELY(queryCtx->scanUserData.isRecArrayNeedAlloc)) {
        for (uint32_t i = 0; i < queryCtx->scanUserData.recArrayAllocedNum; i++) {
            DbDynMemCtxFree(memCtx, queryCtx->scanUserData.recArray[i]);
            if (queryCtx->scanUserData.recArray[i] == NULL) {
                break;
            }
        }
    }
    queryCtx->scanUserData.recArrayAllocedNum = 0;
    DbDynMemCtxFree(memCtx, queryCtx->scanUserData.recArray);
}

void SimpleRelFreeQueryCtx(DbMemCtxT *memCtx, EmbSimpleQueryCtxT *queryCtx)
{
    if (queryCtx->hpTupleList.freeFunc != NULL) {
        uint32_t nodeCount = 0;
        while ((nodeCount = DbGaListGetCount(&queryCtx->hpTupleList)) > 0) {
            DbGaListDelete(&queryCtx->hpTupleList, nodeCount - 1);
        }
        (void)DbGaListDestroy(&queryCtx->hpTupleList);
    }
    QueryCtxFreeConds(memCtx, queryCtx);
    QueryCtxFreeSortItem(&queryCtx->sortItem);
    QueryCtxFreeProjectionItem(memCtx, queryCtx);
    QueryCtxFreeComplexOpRecArray(memCtx, queryCtx);
    queryCtx->matchedCnt = NULL;
    queryCtx->resumeAddr = NULL;
    queryCtx->resumeDataInfo = NULL;
    (void)memset_s(&queryCtx->scanUserData, sizeof(SimpRelScanUserDataT), 0x00, sizeof(SimpRelScanUserDataT));
}

ALWAYS_INLINE_C static bool CmpOpMatch(GME_CMPTYPE_ENUM cmpOp, int8_t result)
{
    switch (cmpOp) {
        case GME_CMP_EQUAL:
            return result == 0;
        case GME_CMP_NOTEQUAL:
            return result != 0;
        case GME_CMP_LESS:
        case GME_CMP_MAX_LESS:
            return result < 0;
        case GME_CMP_LESSEQUAL:
        case GME_CMP_MAX_LESS_EQUAL:
            return result <= 0;
        case GME_CMP_LARGER:
        case GME_CMP_MIN_LARGER:
            return result > 0;
        case GME_CMP_LARGEREQUAL:
        case GME_CMP_MIN_LARGER_EQUAL:
            return result >= 0;
        default:
            DB_ASSERT(false);
            return false;
    }
}

ALWAYS_INLINE_C static bool SimprelCompareUint32(const HeapTupleBufT *tupleBuf, EmbSimpleCondItemT *cond)
{
    int8_t result = 0;
    uint32_t v1 = *(uint32_t *)(void *)(tupleBuf->buf + cond->fldOffset);
    uint32_t v2 = *(uint32_t *)(void *)cond->value;
    if (v1 > v2) {
        result = 1;
    } else if (v1 < v2) {
        result = -1;
    }
    if (!CmpOpMatch(cond->enOp, result)) {
        return false;
    }
    return true;
}

ALWAYS_INLINE_C Status SimpleRelMatchFilterCond(
    const HeapTupleBufT *tupleBuf, EmbSimpleCondItemT *filterConds, uint32_t condNum, bool *isMatch)
{
    *isMatch = false;  // 默认置为不匹配
    VOS_BOOL match;
    uint8_t pucDir;
    uint32_t offset = 0;
    for (uint32_t i = 0; i < condNum; i++) {
        EmbSimpleCondItemT *cond = &filterConds[i];
        // uint32_t 快捷路径
        if (SECUREC_LIKELY(cond->v5Type == DB_DATATYPE_UINT32)) {
            if (!SimprelCompareUint32(tupleBuf, cond)) {
                return GMERR_OK;
            }
            continue;
        }
        // 共享容器数据，最后两个字节为表ID
        offset = !cond->isCommonAreaCond ? cond->fldOffset : (tupleBuf->bufSize - DB_V1_TBLID_LEN);
        Status ret = V1ValueMatch(
            tupleBuf->buf + offset, cond->value, cond->propeMaxLen, cond->enOp, cond->v1Type, &match, &pucDir);
        if (ret != GMERR_OK || match == false) {
            return ret;
        }
    }
    // 所有条件均匹配
    *isMatch = true;
    return GMERR_OK;
}

ALWAYS_INLINE_C bool SimpleRelMatchRowFilter(uint8_t *data, void *userData)
{
    SimpRelScanUserDataT *inputData = (SimpRelScanUserDataT *)userData;
    EmbSimpleQueryCtxT *queryCtx = &((SimRelLabelCursorT *)inputData->labelCursor)->queryCtx;
    uint32_t extOffset = 0;
    for (uint32_t i = 0; i < queryCtx->filterCondNum; i++) {
        EmbSimpleCondItemT *cond = &queryCtx->filterConds[i];
        VOS_BOOL match = false;
        VOS_UINT8 pucDir;
        Status ret = V1ValueMatch(data + cond->fldOffset + extOffset, cond->value, cond->propeMaxLen, cond->enOp,
            cond->v1Type, &match, &pucDir);
        if (ret != GMERR_OK) {  // 报错场景不过滤
            inputData->isMatch = false;
            return true;
        }
        if (match == false) {
            inputData->isMatch = false;
            return false;
        }
    }
    inputData->isMatch = true;
    return true;
}

ALWAYS_INLINE_C void SimpleRelResetMatchRowFilter(void *userData)
{
    SimpRelScanUserDataT *inputData = (SimpRelScanUserDataT *)userData;
    inputData->isMatch = false;
    return;
}

SimpleRelDiffListT *SimpleRelationGetDiffListById(EmbSimpleRunCtxT *embCtx, uint32_t labelId, uint32_t *pos)
{
    if (embCtx->diffCtx == NULL) {
        return NULL;
    }

    uint32_t left = 0;
    uint32_t right = DbListGetItemCnt(&embCtx->diffCtx->labelList);
    while (left < right) {
        uint32_t mid = (left + right) >> 1;
        SimpleRelDiffListT *diffListTmp = (SimpleRelDiffListT *)DbListItem(&embCtx->diffCtx->labelList, mid);
        if (diffListTmp->labelId == labelId) {
            return diffListTmp;
        } else if (diffListTmp->labelId > labelId) {
            right = mid;
        } else {
            left = mid + 1;
        }
    }
    *pos = right;
    return NULL;
}

Status SimpleRelationAllocDiffList(
    EmbSimpleRunCtxT *embCtx, uint32_t labelId, uint32_t pos, SimpleRelDiffListT **diffList)
{
    SimpleRelDiffListT list;
    DbGaListInit(&list.diffList, embCtx->diffCtx->memCtx, (GaListAllocFuncT)SimpleRelationAllocMem,
        (GaListFreeFuncT)SimpleRelationFreeMem);
    list.labelId = labelId;
    list.deleteCnt = 0;
    list.insertCnt = 0;

    Status ret = DbOamapInit(&list.diffMap, 0, DbOamapUint64Compare, embCtx->diffCtx->memCtx, true);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "Unable to create simple rel diff map.");
        return ret;
    }

    ret = DbInsertListItem(&embCtx->diffCtx->labelList, &list, pos);
    if (ret != GMERR_OK) {
        return ret;
    }
    *diffList = (SimpleRelDiffListT *)DbListItem(&embCtx->diffCtx->labelList, pos);
    return GMERR_OK;
}

Status SimpleRelationAllocDiffItem(
    SimpleRelDiffListT *diffList, uint32_t savePointSeq, uint64_t addr, SimpleRelDiffListItemT **newItem)
{
    SimpleRelDiffListItemT *item = NULL;
    Status ret = DbGaListNew(&diffList->diffList, sizeof(SimpleRelDiffListItemT), (void **)&item);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Unable to alloc diff item, size : %" PRIu32 ".", (uint32_t)sizeof(SimpleRelDiffListItemT));
        return ret;
    }
    item->addr = addr;
    item->savePointSeq = savePointSeq;
    item->state = DIFF_STATE_INVALID;
    *newItem = item;
    return GMERR_OK;
}

static ALWAYS_INLINE_C VOS_BOOL IsTblIDExist(DbListT *relNoList, uint16_t relNo, VOS_UINT32 *pusPos)
{
    VOS_UINT32 ulMin;
    VOS_UINT32 ulMid;
    VOS_UINT32 ulMax;
    ulMin = 0;
    ulMax = relNoList->count;
    ulMid = (ulMin + ulMax) >> 1;
    while (ulMin < ulMax) {
        SimpRelRelNoListNodeT *node = (SimpRelRelNoListNodeT *)DbListItem(relNoList, ulMid);
        if (node->relNo >= relNo) {
            if (node->relNo == relNo) {
                *pusPos = ulMid;
                return VOS_TRUE;
            }
            ulMax = ulMid;
        } else {
            ulMin = ulMid + 1;
        }
        ulMid = (ulMin + ulMax) >> 1;
    }

    *pusPos = ulMid;
    return VOS_FALSE;
}

Status SimpleRelationDiffInsertTblList(DbListT *relNoList, uint16_t relNo, SimpRelNoListNodeStateE nodeState)
{
    uint32_t count = DbListGetItemCnt(relNoList);
    uint32_t pos = count;
    if (IsTblIDExist(relNoList, relNo, &pos)) {
        SimpRelRelNoListNodeT *node = (SimpRelRelNoListNodeT *)DbListItem(relNoList, pos);
        if (node->nodeState == SIMP_REL_NOLIST_STATE_READ && nodeState == SIMP_REL_NOLIST_STATE_WRITE) {
            node->nodeState = SIMP_REL_NOLIST_STATE_WRITE;
        }
        return GMERR_OK;
    }
    SimpRelRelNoListNodeT node = {.relNo = relNo, .nodeState = nodeState};
    return DbInsertListItem(relNoList, (void *)&node, pos);
}

Status SimpleRelationGetDiffItemByAddr(EmbSimpleRunCtxT *embCtx, SimpleRelDiffListT **diffList, uint32_t labelId,
    uint64_t addr, SimpleRelDiffListItemT **item, uint32_t pos)
{
    Status ret = GMERR_OK;
    uint32_t hash = DbHash32((unsigned char *)&addr, sizeof(uint64_t));
    if (*diffList != NULL) {
        // 尝试从map查找已经编辑过的数据
        SimpleRelDiffListItemT *itemTmp = DbOamapLookup(&(*diffList)->diffMap, hash, &addr, NULL);
        if (itemTmp != NULL) {
            *item = itemTmp;
            return GMERR_OK;
        }
    }
    if (*diffList == NULL) {
        // 该表没有创建过轨迹，生成对应表的编辑轨迹
        ret = SimpleRelationAllocDiffList(embCtx, labelId, pos, diffList);
        if (ret != GMERR_OK) {
            return ret;
        }
        // 记录表ID，主要用于强制提交可以获取所有操作表，还用于提交回滚加表锁，因此这里需要保证顺序，不然死锁
        ret = SimpleRelationDiffInsertTblList(
            &embCtx->diffCtx->relNoList, (uint16_t)embCtx->relNo, SIMP_REL_NOLIST_STATE_WRITE);
        if (ret != GMERR_OK) {
            return ret;
        }
        if (!embCtx->diffCtx->isRedo) {
            embCtx->vLabelCtrl->cdbRef++;
        }
    }

    if (*diffList == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(VOS_ERRNO_DB_MEMORYDAMAGED, "Unexpected null pointer of diffList.");
        return VOS_ERRNO_DB_MEMORYDAMAGED;
    } else {
        // 新增轨迹
        SimpleRelDiffListItemT *newItem = NULL;
        ret = SimpleRelationAllocDiffItem(*diffList, embCtx->diffCtx->savepointSeq, addr, &newItem);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = DbOamapInsert(&(*diffList)->diffMap, hash, &newItem->addr, newItem, NULL);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to insert diff map, rel name (%s).", embCtx->vLabelCtrl->relationName);
            return ret;
        }
        *item = newItem;
    }
    return GMERR_OK;
}

// diff轨迹的状态机转换
SimpleRelDiffStateE DiffStateConvert(SimpleRelDiffStateE currentState, SimpleRelDiffOpE currentOperation)
{
    // T当前操作顺序为CREATE、UPDATE、DELETE、ROLLBACK
    const SimpleRelDiffStateE diffStateConvert[(int32_t)DIFF_STATE_BUTT][(int32_t)DIFF_OPERATION_BUTT] = {
        // 当前状态为DIFF_STATE_INVALID
        {DIFF_STATE_CREAT, DIFF_STATE_UPDATE, DIFF_STATE_DELETE, DIFF_STATE_CREAT},
        // 当前状态为DIFF_STATE_CREAT
        {DIFF_STATE_BUTT, DIFF_STATE_CREAT_UPDATE, DIFF_STATE_INVALID, DIFF_STATE_INVALID},
        // 当前状态为DIFF_STATE_CREAT_UPDATE
        {DIFF_STATE_BUTT, DIFF_STATE_CREAT_UPDATE, DIFF_STATE_INVALID, DIFF_STATE_CREAT},
        // 当前状态为DIFF_STATE_UPDATE
        {DIFF_STATE_BUTT, DIFF_STATE_UPDATE_UPDATE, DIFF_STATE_UPDATE_DELETE, DIFF_STATE_INVALID},
        // 当前状态为DIFF_STATE_UPDATE_UPDATE
        {DIFF_STATE_BUTT, DIFF_STATE_UPDATE_UPDATE, DIFF_STATE_UPDATE_DELETE, DIFF_STATE_UPDATE},
        // 当前状态为DIFF_STATE_DELETE
        {DIFF_STATE_BUTT, DIFF_STATE_BUTT, DIFF_STATE_BUTT, DIFF_STATE_INVALID},
        // 当前状态为DIFF_STATE_UPDATE_DELETE
        {DIFF_STATE_BUTT, DIFF_STATE_BUTT, DIFF_STATE_BUTT, DIFF_STATE_UPDATE}};
    return diffStateConvert[currentState][currentOperation];
}

inline static void SimpleRelationSetDiffRecCnt(SimpleRelDiffListT *diffList, SimpleRelDiffListItemT *item, bool isAscnd)
{
    DB_POINTER2(diffList, item);
    int32_t changeVal = isAscnd ? 1 : -1;
    if (SimpleRelationIsCreateOp(item->state)) {
        diffList->insertCnt += changeVal;
    } else if (SimpleRelationIsDeleteOp(item->state)) {
        diffList->deleteCnt += changeVal;
    }
}

Status SimpleRelationRecordDiff(EmbSimpleRunCtxT *embCtx, uint32_t labelId, uint64_t addr, SimpleRelDiffOpE op)
{
    if (embCtx->diffCtx == NULL) {
        return GMERR_OK;
    }

    uint32_t pos;
    SimpleRelDiffListT *diffList = SimpleRelationGetDiffListById(embCtx, labelId, &pos);
    SimpleRelDiffListItemT *item = NULL;
    Status ret = SimpleRelationGetDiffItemByAddr(embCtx, &diffList, labelId, addr, &item, pos);
    if (ret != GMERR_OK) {
        return ret;
    }
    DB_ASSERT(item != NULL);
    item->savePointSeq = embCtx->diffCtx->savepointSeq;
    SimpleRelationSetDiffRecCnt(diffList, item, false);
    item->state = DiffStateConvert(item->state, op);
    SimpleRelationSetDiffRecCnt(diffList, item, true);
    if (item->state == DIFF_STATE_BUTT) {
        DB_LOG_ERROR(VOS_ERRNO_DB_FAILURE, "Unable to insert diff, op: %" PRIu8 ".", op);
        return VOS_ERRNO_DB_FAILURE;
    }
    return GMERR_OK;
}

void SimpleRelationRollbackDiff(EmbSimpleRunCtxT *embCtx)
{
    if (embCtx->diffCtx == NULL) {
        return;
    }
    // 没开过表，直接返回, SimpleRelationPrepareLabel失败vertexLabel为null
    if (embCtx->labelCursor == NULL || embCtx->labelCursor->vertexLabel == NULL) {
        return;
    }
    uint32_t labelId = embCtx->labelCursor->vertexLabel->metaCommon.metaId;
    uint32_t pos;
    SimpleRelDiffListT *diffList = SimpleRelationGetDiffListById(embCtx, labelId, &pos);
    if (diffList == NULL) {
        // 没有操作过表，直接返回
        return;
    }
    for (int32_t i = DbGaListGetCount(&diffList->diffList) - 1; i >= 0; i--) {
        SimpleRelDiffListItemT *item = DbGaListGet(&diffList->diffList, (uint32_t)i);
        // 根据与当前savepointSeq比较判断最近发生过编辑
        if (item->savePointSeq == embCtx->diffCtx->savepointSeq) {
            SimpleRelationSetDiffRecCnt(diffList, item, false);
            item->state = DiffStateConvert(item->state, DIFF_OPERATION_ROLLBACK);
            SimpleRelationSetDiffRecCnt(diffList, item, true);
            if (item->state != DIFF_STATE_INVALID) {
                continue;
            }
            // 状态无效，表示都是新生成的轨迹，可以去掉
            uint32_t hash = DbHash32((unsigned char *)&item->addr, sizeof(uint64_t));
            (void)DbOamapRemove(&diffList->diffMap, hash, &item->addr);
            // 最新的大部分都在尾部，从尾部开始可以减少内存的移动，
            DbGaListDelete(&diffList->diffList, i);
        }
    }
    return;
}

bool SimpleRelationIsCreateOp(SimpleRelDiffStateE state)
{
    return state == DIFF_STATE_CREAT || state == DIFF_STATE_CREAT_UPDATE;
}

bool SimpleRelationIsDeleteOp(SimpleRelDiffStateE state)
{
    return state == DIFF_STATE_DELETE || state == DIFF_STATE_UPDATE_DELETE;
}

bool SimpleRelationIsUpdateOp(SimpleRelDiffStateE state)
{
    return state == DIFF_STATE_UPDATE || state == DIFF_STATE_UPDATE_UPDATE;
}

static Status SimpleRelationGetTtreeInfo(EmbSimpleRunCtxT *embCtx)
{
    char *tmpBuff = NULL;
    DbInstanceT *dbInstance = NULL;
    Status ret = DbGetInstanceById(DbGetProcGlobalId(), &dbInstance);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = TTreeIndexSysview(dbInstance, embCtx->labelCursor->idxCtx, embCtx->vLabelCtrl->originDefInfo->ulNIdxs,
        embCtx->labelCursor->vertexLabel->metaCommon.metaName, &tmpBuff);
    DbReleaseInstance(dbInstance);
    if (ret != GMERR_OK) {
        return ret;
    }
    SimpRelScanCtxT *scanCtx = embCtx->apiCtx;
    char **resultBuff = scanCtx->sysviewResultBuff;
    // 赋值
    *resultBuff = tmpBuff;
    return GMERR_OK;
}

Status GmeSimpleRelGetTtreeInfo(GmeConnT *conn, SimpRelScanCtxT *scanCtx)
{
    EmbSimpleRunCtxT *embCtx = conn->embSession->embSimpleRunCtx;
    // bind api args 2 embCtx
    embCtx->opCode = SIMPLEREL_OP_TYPE_GET_TTREE_INFO;
    embCtx->relNo = scanCtx->relId;
    embCtx->nspId = scanCtx->dbId;
    embCtx->apiCtx = scanCtx;
    return SimpleRelationExecute(embCtx, SimpleRelationGetTtreeInfo);
}

AdptV1GloabalT g_adptV1Instance = {.RDBUsedDbId = DB_INVALID_UINT32, .RDBUsedRelId = DB_INVALID_UINT16};

ALWAYS_INLINE_C bool SimpRelIsTableFullAndLastOpIsWrite(GmeConnT *gmeConn)
{
    EmbSimpleRunCtxT *embCtx = gmeConn->embSession->embSimpleRunCtx;
    SimRelLabelCursorT *labelCursor = embCtx->labelCursor;
    if (labelCursor == NULL || labelCursor->hpHandle == NULL || embCtx->vLabelCtrl == NULL ||
        labelCursor->vertexLabel == NULL ||
        !CataVertexLabelExist(embCtx->labelCursor->lastOpratedMetaId, DbGetInstanceByMemCtx(embCtx->sessionMemctx))) {
        return false;
    }
    uint32_t pos;
    SimpleRelDiffListT *diffList = SimpleRelationGetDiffListById(embCtx, embCtx->labelCursor->lastOpratedMetaId, &pos);
    uint32_t curRelRecCnt = embCtx->vLabelCtrl->realItemCnt + SimpleRelGetCdbOperateRecCntByDiffList(diffList);
    return (labelCursor->lastOpType == SIMPLEREL_WRITE) &&
           (curRelRecCnt == labelCursor->vertexLabel->commonInfo->heapInfo.maxVertexNum);
}

Status RDBTryCommit(void)
{
    AdptV1ConnPoolT *connPool = &g_adptV1Instance.connPool;
    VOS_UINT32 ulDbId = g_adptV1Instance.RDBUsedDbId;
    DbRWLatchR(&connPool->latch);
    if (connPool->connList.count == 0 || ulDbId == DB_INVALID_UINT32 || g_adptV1Instance.isNoNeedTryCommit) {
        DbRWUnlatchR(&connPool->latch);
        return GMERR_OK;
    }
    GmeConnT *conn = *(GmeConnT **)DbListItem(&connPool->connList, 0);
    DbRWUnlatchR(&connPool->latch);
    EmbSimpleRunCtxT *embCtx = conn->embSession->embSimpleRunCtx;
    SessionT *session = embCtx->session->session;
    Status ret = GMERR_OK;
    RDBConnLock(conn);
    if (g_adptV1Instance.RDBTrxState == RDB_TRX_RUNNING) {
        ret = SeTransCommit(session->seInstance);
        if (ret != GMERR_OK) {
            if (ret == GMERR_UNDEFINED_TABLE) {
                ret = GMERR_OK;  // 表在提交前被删除
            } else {
                DB_LOG_ERROR_UNFOLD(ret, "Unabel to commit zero rdb!");
                DB_ASSERT(false);
            }
            (void)SeTransRollback(session->seInstance, false);
        }
        g_adptV1Instance.RDBTrxState = RDB_TRX_WAIT;
        g_adptV1Instance.RDBWriteCnt = 0;
        g_adptV1Instance.isNoNeedTryCommit = g_adptV1Instance.isAutoCommitZeroRDB;
        g_adptV1Instance.isModDB = false;
        g_adptV1Instance.RDBUsedDbId = DB_INVALID_UINT32;
        g_adptV1Instance.RDBUsedRelId = DB_INVALID_UINT16;
    }
    RDBConnUnlock(conn);
    return ret;
}

void SetZeroRDBInfo(GmeConnT *conn, bool isZeroRDBConn)
{
    EmbSimpleRunCtxT *embCtx = conn->embSession->embSimpleRunCtx;
    if (isZeroRDBConn) {
        embCtx->RDBTrxState = g_adptV1Instance.RDBTrxState;
    }
    embCtx->isZeroRDBConn = isZeroRDBConn;
}

void RDBConnLock(GmeConnT *conn)
{
    DbRWSpinWLock(&conn->embSession->session->rwLock);
}

void RDBConnUnlock(GmeConnT *conn)
{
    DbRWSpinWUnlock(&conn->embSession->session->rwLock);
}

void GmeSimpleRelSetTtreeTraceSwitchState(bool state)
{
    TtreeSetTraceSwitchState(state);
}

bool isEdgeDefMatch(EdgeCtrlT *edgeCtrl, DB_EDGE_FIELD_STRU *relInfos)
{
    uint32_t match = 0;
    for (uint32_t i = 0; i < DB_EDGE_REL_NUM; i++) {
        for (uint32_t j = 0; j < DB_EDGE_REL_NUM; j++) {
            if (edgeCtrl->relId[i] != relInfos[j].relId) {
                continue;
            }
            if (edgeCtrl->relInfo[i].fieldNum != relInfos[j].FieldNum) {
                return false;
            }
            for (uint32_t k = 0; k < relInfos[j].FieldNum; k++) {
                if (edgeCtrl->relInfo[i].fields[k] != relInfos[j].aucField[k]) {
                    return false;
                }
            }
            match++;
            break;
        }
    }
    return match > 1;
}

void GmeSimpleSetRelItemCntAfterTrx(GmeConnT *conn, Status execRet, bool isRollBack)
{
    EmbSimpleRunCtxT *embCtx = conn->embSession->embSimpleRunCtx;
    if (embCtx->diffCtx == NULL || embCtx->nspCtrl == NULL) {
        DB_LOG_WARN(VOS_ERRNO_DB_NULLPTR, "Set rec cnt with null ptr.");
        return;
    }

    if (execRet != GMERR_OK) {
        // 提交未成功不修改数据量
        return;
    }

    uint32_t pos;
    VlabelCtrlT *vLabelCtrl = NULL;
    SimpleRelDiffCtxT *diffCtx = embCtx->diffCtx;
    for (uint32_t i = 0; i < DbListGetItemCnt(&diffCtx->relNoList); i++) {
        Status ret = SimpleRelGetVlabelCtrlByIdSilent(
            &embCtx->nspCtrl->vLabelCtrlList, *(uint16_t *)DbListItem(&diffCtx->relNoList, i), NULL, &vLabelCtrl, NULL);
        if (ret != GMERR_OK) {
            continue;
        }
        SimpleRelDiffListT *diffList =
            SimpleRelationGetDiffListById(embCtx, vLabelCtrl->vertexLabel->metaCommon.metaId, &pos);
        if (diffList == NULL) {
            continue;
        }

        if (!isRollBack) {
            int32_t cdbRecCnt = SimpleRelGetCdbOperateRecCntByDiffList(diffList);
            vLabelCtrl->realItemCnt += cdbRecCnt;
        }
        DB_ASSERT(vLabelCtrl->cdbRef > 0);
        vLabelCtrl->cdbRef--;
    }
    return;
}
