/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: api for xml text
 * Author: chentingrong
 * Create: 2024-03-18
 */

#ifndef GRD_XML_TEXT_API_H
#define GRD_XML_TEXT_API_H

#include <stdint.h>
#include "grd_type_export.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief Read one xml text object to value string
 * @param[in] db Pointer to the db
 * @param[in] opPos op position in fragment.
 * @param[out] value String read from shared object
 * @return GRD_OK if success
 */
GRD_API int32_t GRD_XmlTextReadInJsonMode(GRD_DB *db, GRD_XmlOpPositionT *opPos, char **value);

/**
 * @brief Get length of this xml text
 * @param[in] db Pointer to the db
 * @param[in] opPos op position in fragment.
 * @param[out] length Content length of all texts
 * @return GRD_OK if success
 */
GRD_API int32_t GRD_XmlTextGetLength(GRD_DB *db, GRD_XmlOpPositionT *opPos, uint32_t *length);

/**
 * @brief Insert content to one xml text with given index.
 * @param[in] db Pointer to the db.
 * @param[in] opPos op position in fragment.
 * @param[in] index Insert start position
 * @param[in] content Insert content
 * @param[in] attrStr Insert attribute
 * @return GRD_OK if success.
 */
GRD_API int32_t GRD_XmlTextInsert(
    GRD_DB *db, GRD_XmlOpPositionT *opPos, uint32_t index, const char *content, const char *attrStr);

/**
 * @brief Delete content of one xml text with given length from given index
 * @param[in] db Pointer to the db
 * @param[in] opPos op position in fragment.
 * @param[in] index Delete start position
 * @param[in] length Delete length
 * @return GRD_OK if success
 */
GRD_API int32_t GRD_XmlTextDelete(GRD_DB *db, GRD_XmlOpPositionT *opPos, uint32_t index, uint32_t length);

/**
 * @brief free gotten xml text
 * @param[in] value String read from shared object
 * @return GRD_OK if success
 */
GRD_API int32_t GRD_XmlTextFree(char *value);

/**
 * @brief Assign attributes to one xml text with given length from given index
 * @param[in] db Pointer to the db
 * @param[in] opPos op position in fragment.
 * @param[in] index Assign start position
 * @param[in] length Assign length
 * @param[in] attrStr Attributes string, for example: {"hw_font":"1.45"}
 * @return GRD_OK if success
 */
GRD_API int32_t GRD_XmlTextAssignAttributes(
    GRD_DB *db, GRD_XmlOpPositionT *opPos, uint32_t index, uint32_t length, const char *attrStr);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif  // GRD_XML_TEXT_API_H
